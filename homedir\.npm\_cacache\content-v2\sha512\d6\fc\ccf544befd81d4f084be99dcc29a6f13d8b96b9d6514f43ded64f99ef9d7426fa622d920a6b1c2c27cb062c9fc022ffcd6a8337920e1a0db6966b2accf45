{"_id": "@esbuild/netbsd-arm64", "_rev": "10-b18947345036ae165b118ed2036c49ec", "name": "@esbuild/netbsd-arm64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.0.1": {"name": "@esbuild/netbsd-arm64", "version": "0.0.1", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.0.1", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "9af66e62a31db9e9aac5028de40804c4b08b229a", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.0.1.tgz", "fileCount": 2, "integrity": "sha512-mYf9LRPtROtqU9JnffHYA4ye7Sx740Pdh2JvJeAbBWXGQWj8tg3avxqelcSMCTn8nlukFeaVYd0DPs62lJMImQ==", "signatures": [{"sig": "MEQCIB6eMoZZigmD4QlUmnp+eNPZFUsvTZPxmXC8EyQDrmYNAiAoQvgcf2DRqkD02w5d4zFBDa3VUtjwQ6tlW+8IAEXwzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 790}, "engines": {"node": ">=18"}, "gitHead": "3ae3525a3c4aa1408cf94f5f473a1d5481c176cf", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.0.1_1734652238845_0.6780467473415881", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.1": {"name": "@esbuild/netbsd-arm64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "03d24e0aa8e29b97d279a5599dc7a8c62e1b981b", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-lH+bWKi8aCvlDu0vDVcZV4ENiHjVus3SQFueeydJ/mSfKywQ3LnbSjJ8PUgj+3dllq1OTFCGgh+x/14hrULVrg==", "signatures": [{"sig": "MEYCIQD+4t28j9xOiiaqnA47gh5dv18NArumlP4p4VIPBeuEMwIhAIUaAdi3mVD1xrFvQtdils649ej/Kqjs/ZF3QyTazJl+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10146754}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.24.1_1734673256507_0.4252563568946812", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/netbsd-arm64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "65f19161432bafb3981f5f20a7ff45abb2e708e6", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-wuLK/VztRRpMt9zyHSazyCVdCXlpHkKm34WUyinD2lzK07FAHTq0KQvZZlXikNWkDGoT6x3TD51jKQ7gMVpopw==", "signatures": [{"sig": "MEUCIQDFy6bgjX+DEcY4nETdLW6trQt3TjNWebpqRDkLgIjvTQIgVtJXRyiUFaZ8WY5eWBYHUDKG84wmOz8ewpC0yRmA5QY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10146754}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.24.2_1734717374068_0.05439563585679563", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/netbsd-arm64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "935c6c74e20f7224918fbe2e6c6fe865b6c6ea5b", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-RuG4PSMPFfrkH6UwCAqBzauBWTygTvb1nxWasEJooGSJ/NwRw7b2HOwyRTQIU97Hq37l3npXoZGYMy3b3xYvPw==", "signatures": [{"sig": "MEYCIQCcvlhm9BYQxDgJOZJTVn6AGnS0xzbm/6JFUc2nuns81QIhANYvYTFM0BunkkvILQ0CXLkBauz3tL3M0EnjGozDvC09", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503682}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.25.0_1738983732094_0.8212938726631314", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/netbsd-arm64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "0d280b7dfe3973f111b02d5fe9f3063b92796d29", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-O96poM2XGhLtpTh+s4+nP7YCCAfb4tJNRVZHfIE7dgmax+yMP2WgMd2OecBuaATHKTHsLWHQeuaxMRnCsH8+5g==", "signatures": [{"sig": "MEUCIQC9+2lk9/o6AsYaAbBMLdoSnafNiwh3lI14gwxnyPCtGQIgAV5KqqBBDzY1AuqsqznNcqLfMj6F1hU/q7wO5cWeOvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503682}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.25.1_1741578321475_0.1581539291815468", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/netbsd-arm64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "744affd3b8d8236b08c5210d828b0698a62c58ac", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-talAIBoY5M8vHc6EeI2WW9d/CkiO9MQJ0IOWX8hrLhxGbro/vBXJvaQXefW2cP0z0nQVTdQ/eNyGFV1GSKrxfw==", "signatures": [{"sig": "MEQCIEqdLZGdzFzUJNDvHTfnL1d3pPcM4ZuvWw3raWdPIOgdAiAkgQGUlM3RJEdcJAWia9pXd7Hl1C/UrGdWgJbNLxfm3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503682}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.25.2_1743355971743_0.43237635592216894", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/netbsd-arm64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "4c15c68d8149614ddb6a56f9c85ae62ccca08259", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-1QaLtOWq0mzK6tzzp0jRN3eccmN3hezey7mhLnzC6oNlJoUJz4nym5ZD7mDnS/LZQgkrhEbEiTn515lPeLpgWA==", "signatures": [{"sig": "MEQCIFsL+c+TF1rT65dG1RaYu29mzQpXQY33eKzEQYRqWEftAiBuNYNUVnSwm5FP41p/iwm4W9gYG502pyVIEEuu3P6pVQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503682}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.25.3_1745380554468_0.5779524889702432", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/netbsd-arm64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/netbsd-arm64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["netbsd"], "cpu": ["arm64"], "dist": {"shasum": "02e483fbcbe3f18f0b02612a941b77be76c111a4", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-vUnkBYxZW4hL/ie91hSqaSNjulOnYXE1VSLusnvHg2u3jewJBz3YzB9+oCw8DABeVqZGg94t9tyZFoHma8gWZQ==", "signatures": [{"sig": "MEYCIQDYfeDAyFNPeF14BCTXht78W8UdGfCMiuf8CsGJHuR+bwIhAK1az36EbuOgYED0M8wf+4jysJJvDFFSAGDW6Az7GdQ9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9503682}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/netbsd-arm64_0.25.4_1746491440402_0.9840925794234894", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/netbsd-arm64", "version": "0.25.5", "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["netbsd"], "cpu": ["arm64"], "_id": "@esbuild/netbsd-arm64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==", "shasum": "53b4dfb8fe1cee93777c9e366893bd3daa6ba63d", "tarball": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9503682, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDqmV16wmswhvodp+GqhMkawS/uJSO9wB49arxgpl+xhQIgIkLmF4yDtN71vBpCDmvyOrwemx3/cYfhxQJORL5ghMw="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/netbsd-arm64_0.25.5_1748315560357_0.8249055103113456"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-19T23:50:38.720Z", "modified": "2025-05-27T03:12:41.091Z", "0.0.1": "2024-12-19T23:50:39.013Z", "0.24.1": "2024-12-20T05:40:56.762Z", "0.24.2": "2024-12-20T17:56:14.321Z", "0.25.0": "2025-02-08T03:02:12.426Z", "0.25.1": "2025-03-10T03:45:21.723Z", "0.25.2": "2025-03-30T17:32:52.002Z", "0.25.3": "2025-04-23T03:55:54.766Z", "0.25.4": "2025-05-06T00:30:40.741Z", "0.25.5": "2025-05-27T03:12:40.622Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the NetBSD ARM 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n\n⚠️ Note: NetBSD is not one of [Node's supported platforms](https://nodejs.org/api/process.html#process_process_platform), so installing esbuild may or may not work on NetBSD depending on how Node has been patched. This is not a problem with esbuild. ⚠️\n", "readmeFilename": "README.md"}