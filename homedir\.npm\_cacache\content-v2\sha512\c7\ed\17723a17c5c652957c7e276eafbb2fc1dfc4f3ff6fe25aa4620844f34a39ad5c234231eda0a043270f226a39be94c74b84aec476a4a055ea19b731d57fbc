{"_id": "@webassemblyjs/wasm-edit", "_rev": "86-8e480d941e2db66b4d630ea7bf3804f2", "name": "@webassemblyjs/wasm-edit", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.1.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "52457ace0b59e0deb2544cd40fd28519933b1f6f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-usf1HgmedlWZNMx5ld4X92CUevKUQ1Dg+aJsBhMVwqU1JFLfSYRx1tGLm1ku4dOSEEvwRY0pA6cd9D0Z1OAgHA==", "signatures": [{"sig": "MEYCIQC1DKRhTTuM91uB4mN4eoZGedc/5/G+tP8a0h498fBZrQIhAOqmlenB/Owt2mcvW4nnZVwDG2gpBqneFcV53yTvRY5n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12634}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.0", "@webassemblyjs/wasm-gen": "1.1.0", "@webassemblyjs/wasm-parser": "1.1.0", "@webassemblyjs/helper-buffer": "1.1.0", "@webassemblyjs/helper-wasm-section": "1.1.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.0_1520241686485_0.6195212440981637", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "767a6ff2a0b6647e8b21d6d0100cd0f1aa92c85e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.1.tgz", "fileCount": 6, "integrity": "sha512-KRwJYyGE2AUT8n7+1bvFHBJpFVOd/UgvKAQnwu5D9znRGKHi6oWBorqvTwVJWbBDyTtHEMFGX5oK/y5PpeXWwA==", "signatures": [{"sig": "MEQCIDNayjjxb99mTIwG4tlNWtpNasr/4N05L231kV8QApLwAiA6fAaKcSgR+dyTGMUJlvQeXEA8f7ubIodZ7n0ClDLxwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13152}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.1", "@webassemblyjs/wasm-gen": "1.1.1", "@webassemblyjs/wasm-parser": "1.1.1", "@webassemblyjs/helper-buffer": "1.1.1", "@webassemblyjs/helper-wasm-section": "1.1.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.1_1520245631149_0.6101209752873555", "host": "s3://npm-registry-packages"}}, "1.1.2-y.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9145814a40b4e1ef023a6b644dab8832d7c62531", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.0.tgz", "fileCount": 6, "integrity": "sha512-jGPD9mQN0RuvW9ohV12fgXzb6tqmd8vWC8NS++k6WV/pentFsoaSNu+Oh8u6YsrD4joj7P9NtJC96MoX7wRn2w==", "signatures": [{"sig": "MEUCIQCeqWYg02SYAckD9fOKxuvroXX5RZYIoY+omXggp2WdMwIgY1c79Lui61/87BVEgVzdizQEhOz8xCYXvIu7U/apCco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13180}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.0", "@webassemblyjs/wasm-gen": "1.1.2-y.0", "@webassemblyjs/wasm-parser": "1.1.2-y.0", "@webassemblyjs/helper-buffer": "1.1.2-y.0", "@webassemblyjs/helper-wasm-section": "1.1.2-y.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.0_1520266587710_0.8663149802432848", "host": "s3://npm-registry-packages"}}, "1.1.2-y.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "330476c48389d8c2c6d1d947c2447171e82ef2b3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.1.tgz", "fileCount": 6, "integrity": "sha512-/kB5Ps2qu3MPrRSFnXTtD/vGD8tIPrfd0aRA55GfLB49fkxWe7FEFkrcQoQ8A41a2KGoN9QiV8wXbfadA6dlrA==", "signatures": [{"sig": "MEUCIEOmyJOGY2gvgk3UX3l/BDMK9pzirq/L4JN9gbFDsRkEAiEA3aPOiOX0cwZbbuFqP2ver0hjxXASPEyoyKgUhd5kHMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13180}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.1", "@webassemblyjs/wasm-gen": "1.1.2-y.1", "@webassemblyjs/wasm-parser": "1.1.2-y.1", "@webassemblyjs/helper-buffer": "1.1.2-y.1", "@webassemblyjs/helper-wasm-section": "1.1.2-y.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.1_1520532623595_0.5724601311902779", "host": "s3://npm-registry-packages"}}, "1.1.2-y.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.2", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0762d8cbda731473bb78541f986c1e11024cf56e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.2.tgz", "fileCount": 6, "integrity": "sha512-wuhI6H91SUfn3yClBnrmuQp5xVop3Q+2OH2An9W3j07zd4WFOrdYZfb0Eh22cjXh/JKPxrGykc1kCXdJjJA3og==", "signatures": [{"sig": "MEQCIBkICceAj2EBQbJNZkMa0YZS9xdU2hRaBjhk+JAPSiVeAiBXmh9DZdD81cnQ9/Yd6RAYLXkYEzVU2VIESyN5Uk5HIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13180}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.2", "@webassemblyjs/wasm-gen": "1.1.2-y.2", "@webassemblyjs/wasm-parser": "1.1.2-y.2", "@webassemblyjs/helper-buffer": "1.1.2-y.2", "@webassemblyjs/helper-wasm-section": "1.1.2-y.2", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.2_1520582287903_0.5277833966030436", "host": "s3://npm-registry-packages"}}, "1.1.2-y.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.3", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1e349c5d24936efa2889185f1d88c82635bbb80c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.3.tgz", "fileCount": 6, "integrity": "sha512-rlMLfJolwrBwY9TNQM4yYYJLZxxJeeA5D+WSPeLPWMRQbpyNLgq8IHrktnpiaieXzYZ9t2iKmmV/VWonB7PPxg==", "signatures": [{"sig": "MEUCIQCiGqF5tIM0LqrIHxVtQSXeARUNUNhHs6uEAXvoMIHB2wIgCdD93oOuI5Sm/8CCVWsuKblWrAox3iX7opKjZIlmSUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13180}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.3", "@webassemblyjs/wasm-gen": "1.1.2-y.3", "@webassemblyjs/wasm-parser": "1.1.2-y.3", "@webassemblyjs/helper-buffer": "1.1.2-y.3", "@webassemblyjs/helper-wasm-section": "1.1.2-y.3", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.3_1520582957171_0.24769383712425053", "host": "s3://npm-registry-packages"}}, "1.1.2-y.4": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.4", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "604f074d6516fd5131a9dd427964df97d58d4c9d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.4.tgz", "fileCount": 6, "integrity": "sha512-MJJRoxdAej8WeW9HOFdLb0uTeZ/a7E9DyDbLGbjTx95QZKc1mACvCwRg2safu9VRxa57eHfDcF+A653hOXKSRQ==", "signatures": [{"sig": "MEQCIBr6ofDFE+5f6h4rWnCivZyYVn9hjyGF31rDRZfVkiD0AiBOHolhlGYs9FC1iivDqDV54DGHQjmRFoKI7QYFjhdGag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13180}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.4", "@webassemblyjs/wasm-gen": "1.1.2-y.4", "@webassemblyjs/wasm-parser": "1.1.2-y.4", "@webassemblyjs/helper-buffer": "1.1.2-y.4", "@webassemblyjs/helper-wasm-section": "1.1.2-y.4", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.4_1520583347702_0.7689156747680725", "host": "s3://npm-registry-packages"}}, "1.1.2-y.5": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.5", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "f1834af50b98cc492ab6794ced97d8554b6a2df5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.5.tgz", "fileCount": 6, "integrity": "sha512-Im2ZgyD2XNvPKAz36biSOaR3Bv1KUGtdyqumCwYQu0y5iIuloZX0pAqAKuyRf7Y3vOp2K84K9kXa2ksbYWMxAA==", "signatures": [{"sig": "MEUCID19lfFzGA6AGruLgCMezL27LA5rN8YOyy08Fz1bCCqsAiEA3OEdvXYdxG9VzSIWAzWiF4U7CvHvZ5WrLVJ7Xh3xWqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17326}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.5", "@webassemblyjs/wasm-gen": "1.1.2-y.5", "@webassemblyjs/wasm-parser": "1.1.2-y.5", "@webassemblyjs/helper-buffer": "1.1.2-y.5", "@webassemblyjs/helper-wasm-section": "1.1.2-y.5", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.5_1520590726472_0.9890015962907479", "host": "s3://npm-registry-packages"}}, "1.1.2-y.6": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.6", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8ae7646094f9aa3ae2c4cb8f5bff42ce1c2d6bba", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.6.tgz", "fileCount": 6, "integrity": "sha512-4937mwZrIImudtoVxn0Oq7iU/7aqQVK99xYCHfTnUnxiwFFhtxS+ic0oulgFuHnFr8doYoRfu1sMNhbz4I8+/w==", "signatures": [{"sig": "MEYCIQC0CQU0+vX+8W++04qAKRrufm5fzv0Ba9VCaMXOaa6mpgIhAJKwiGyTWIjnxRX093fmtLW8G+ClNWBk5zbDUewD9MWm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17280}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.6", "@webassemblyjs/wasm-gen": "1.1.2-y.6", "@webassemblyjs/wasm-parser": "1.1.2-y.6", "@webassemblyjs/helper-buffer": "1.1.2-y.6", "@webassemblyjs/helper-wasm-section": "1.1.2-y.6", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.6_1520591363531_0.6261845644800084", "host": "s3://npm-registry-packages"}}, "1.1.2-y.7": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3b857f92c7477911067fca79fc759512812041dd", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.7.tgz", "fileCount": 9, "integrity": "sha512-CpRYmkfXISlxfBk7oePcy4nI7T0EnZKGvpToCGcP3p9xcA6SDJSJSGpSeT+7jGlgTwItwMhcnpSyZWdMv/8yTA==", "signatures": [{"sig": "MEYCIQC9y1no1Me2bU8fyoLlx3bEnXOJUv0ukX1djEcVt5KhvQIhAMYwnAl53Pue4qkrxwtHpejpLOZQKvr8NhRPYWronyw6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31080}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.7", "@webassemblyjs/wasm-gen": "1.1.2-y.7", "@webassemblyjs/wasm-parser": "1.1.2-y.7", "@webassemblyjs/wast-printer": "1.1.2-y.7", "@webassemblyjs/helper-buffer": "1.1.2-y.7", "@webassemblyjs/helper-wasm-section": "1.1.2-y.7", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.7_1520615270442_0.7655854069962225", "host": "s3://npm-registry-packages"}}, "1.1.2-y.8": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "70763781d40daab7451526f2795e24136fdf16e2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.8.tgz", "fileCount": 6, "integrity": "sha512-Xw0UBSdbwHlT2J9tmq/1eIXc9/5mla2lp8DbdHHJNj7r8ZZnwIzDWD/V3f9knW0xyCCaEJhWE5BiRQNFYlyf2w==", "signatures": [{"sig": "MEUCIGYxWabDfsuUyyflpRlcAJ2WNKNWSq/5FUU0wQEjN0D2AiEAtHG4lsPhqzudM7LlCKatCLYOKVLQUorRLxk21hHK9BQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17154}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.8", "@webassemblyjs/wasm-gen": "1.1.2-y.8", "@webassemblyjs/wasm-parser": "1.1.2-y.8", "@webassemblyjs/wast-printer": "1.1.2-y.8", "@webassemblyjs/helper-buffer": "1.1.2-y.8", "@webassemblyjs/helper-wasm-section": "1.1.2-y.8", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.8_1520850186107_0.010531895893128418", "host": "s3://npm-registry-packages"}}, "1.1.2-y.9": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.9", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d202646b4e4484a1f959de0ffeaf1e83de6589a7", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.9.tgz", "fileCount": 6, "integrity": "sha512-rTBewPgX3VOafyt8w2tcSQyNHnAZ/SJRZyH7afXnuDvJRgF7RyI1QGoiLAJEu4AHfiXYuyM5vps5Z2ZbpKbiHA==", "signatures": [{"sig": "MEQCIERPM5YR0WixdxpV/Dj+jxgnQrI3YkY12pjEgTJa7RPDAiAhF0Bs+n/bjfcpPuPWrEhvw7vrrcxzYIAxQZd6EVyEVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17154}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.9", "@webassemblyjs/wasm-gen": "1.1.2-y.9", "@webassemblyjs/wasm-parser": "1.1.2-y.9", "@webassemblyjs/wast-printer": "1.1.2-y.9", "@webassemblyjs/helper-buffer": "1.1.2-y.9", "@webassemblyjs/helper-wasm-section": "1.1.2-y.9", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.9_1520852732186_0.947840317180781", "host": "s3://npm-registry-packages"}}, "1.1.2-y.10": {"name": "@webassemblyjs/wasm-edit", "version": "1.1.2-y.10", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.1.2-y.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3954a31e1c54772578b5380921d2e8cf970ba5c0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.1.2-y.10.tgz", "fileCount": 6, "integrity": "sha512-QBc6iWORoRvatmttwQxlPNMpH8c3EqgPGvm0BE7ezon/pcoxGcQSn+eL2zAh9AHbQ5Gx90CSxhVgwtgZisvrrA==", "signatures": [{"sig": "MEUCIQCrjt5RnYYiJUqnyn6+NPPPCaSl04bk0YJiqBNk4iUazAIgWARK9oxMu8NW/osn3URAkoYvINMUxRjOJNd0SNBXuh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19276}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.10", "@webassemblyjs/wasm-gen": "1.1.2-y.10", "@webassemblyjs/wasm-parser": "1.1.2-y.10", "@webassemblyjs/wast-printer": "1.1.2-y.10", "@webassemblyjs/helper-buffer": "1.1.2-y.10", "@webassemblyjs/helper-wasm-section": "1.1.2-y.10", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.1.2-y.10_1520871159196_0.8691139655596465", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3dcc87f11d2cef965fde569b05e27e51e5a20b99", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.0.tgz", "fileCount": 6, "integrity": "sha512-mhRwTb/SmFvH83TvN6JrXzMUAAS1HoZz4u3bi1kgtXWnCmjxiYZoSfEE4T727uzKlQAnN3egSg52tsWhaNqu+g==", "signatures": [{"sig": "MEQCIDek1snYd3EdPlkmvL7pld5fG1Ac+5GpPiQctjrykCoKAiBCvG5B1BNiaT1QLsP/X9QrfsBxPqf5t1vCqoZpRcKqhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19836}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.0", "@webassemblyjs/wasm-gen": "1.2.0", "@webassemblyjs/wasm-parser": "1.2.0", "@webassemblyjs/wast-printer": "1.2.0", "@webassemblyjs/helper-buffer": "1.2.0", "@webassemblyjs/helper-wasm-section": "1.2.0", "@webassemblyjs/helper-wasm-bytecode": "1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.0_1521099336761_0.8669697642330547", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "2d60aa7144eace65db304086d0014ba1a0a1cc10", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.1.tgz", "fileCount": 6, "integrity": "sha512-a8AgZRMcXkuynLaVWOh2WuJo8XXefS1XYJlIfWmoXoFzsuXSJsA/fTyEI29Ma11QzGMS9Mmvm0ZvpmJD+vNFSQ==", "signatures": [{"sig": "MEUCIAvlXoFbFKvwWKCB2l9N6WJOKhMV5a+YP0YhhFIiBR9kAiEA6LGTH9QcVrA2ZsyiEEemLAuXIxaislZL3lsixRk1+Cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.1", "@webassemblyjs/wasm-gen": "1.2.1", "@webassemblyjs/wasm-parser": "1.2.1", "@webassemblyjs/wast-printer": "1.2.1", "@webassemblyjs/helper-buffer": "1.2.1", "@webassemblyjs/helper-wasm-section": "1.2.1", "@webassemblyjs/helper-wasm-bytecode": "1.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.1_1521118213610_0.08418723879164625", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.2", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "229ffe0db16ec9c15a521b49de1d2844e773db4c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.2.tgz", "fileCount": 6, "integrity": "sha512-B3+GYfSfhbVzeuGKZ4IqeTFbfgtPWw2hiZDRN4zCKUGVq9yr3z85oFvDkCdhfUKBWI2AeM/snCHU/5dYgl/4/g==", "signatures": [{"sig": "MEYCIQC22Ku4NNdjKXPDe7pfUxPa+rZtkMc5vEzmKTWn/ofTuwIhAOBOXfs3ZvbbGGqc6bnMm90n66La+jLQuSk8/cy9xJCo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.2", "@webassemblyjs/wasm-gen": "1.2.2", "@webassemblyjs/wasm-parser": "1.2.2", "@webassemblyjs/wast-printer": "1.2.2", "@webassemblyjs/helper-buffer": "1.2.2", "@webassemblyjs/helper-wasm-section": "1.2.2", "@webassemblyjs/helper-wasm-bytecode": "1.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.2_1522324072769_0.9675610136494324", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.3", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7ecfb6dfbe5f362c8391fc308cfa9935289f2d6a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.3.tgz", "fileCount": 6, "integrity": "sha512-D0yn4mR9yiDkU8qUmyygMFM4oVYtxdNjrATewkiPpk4hy3jDKkggY5h4thV9DYJahTF9ajc6X6c1HLU2gzF2CQ==", "signatures": [{"sig": "MEUCIQCsyG5ugWihGAoCi8HRUpIHE3nuPDNxrXcG6O8nlPiA/gIgM8Oal4JNFSlJaMdmSE+gzhxeoe6pW96iojghIxOXwXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.3", "@webassemblyjs/wasm-gen": "1.2.3", "@webassemblyjs/wasm-parser": "1.2.3", "@webassemblyjs/wast-printer": "1.2.3", "@webassemblyjs/helper-buffer": "1.2.3", "@webassemblyjs/helper-wasm-section": "1.2.3", "@webassemblyjs/helper-wasm-bytecode": "1.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.3_1523096826524_0.9253015998607459", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.4", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b650375aefeb3de7498a9e71eaf02ce7ed33d728", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.4.tgz", "fileCount": 6, "integrity": "sha512-k3cxtSlZ5OyjjB5ttn9Kks6ZH4n5TernzHc8rp1wWwzdH723ROIme16I9iexUF6s/Wi7CUcquL0jXNbzK+xmKg==", "signatures": [{"sig": "MEUCIHFq2SaN8wqrXd2qIOyfK/FNvir4xtBprlZGMmG8ktzDAiEAzAmuiZ5oaTblBrpXvemYFh8GEgepjX+nQ0I7kh7DYuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.4", "@webassemblyjs/wasm-gen": "1.2.4", "@webassemblyjs/wasm-parser": "1.2.4", "@webassemblyjs/wast-printer": "1.2.4", "@webassemblyjs/helper-buffer": "1.2.4", "@webassemblyjs/helper-wasm-section": "1.2.4", "@webassemblyjs/helper-wasm-bytecode": "1.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.4_1523615508728_0.46635091223361447", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.5", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9c9185f545b071a36e4446451675240f4ede584f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.5.tgz", "fileCount": 6, "integrity": "sha512-5P+pYWGLKHt9tvim+mdY4PpsUPzNCbBacPpUjlitVR2kV/ZlNHuZTZBvIUxgMgWreP6WAtLmtutSbuJLQG6KkA==", "signatures": [{"sig": "MEUCIQC0vvRi+8JuvK7E59NFkifo9Oc7dJTRJUIrCeWx74zCywIgUx7KWDSDsslii1Plhxq31ICPtMn6bX2REPD4uTZYN2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.5", "@webassemblyjs/wasm-gen": "1.2.5", "@webassemblyjs/wasm-parser": "1.2.5", "@webassemblyjs/wast-printer": "1.2.5", "@webassemblyjs/helper-buffer": "1.2.5", "@webassemblyjs/helper-wasm-section": "1.2.5", "@webassemblyjs/helper-wasm-bytecode": "1.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.5_1523639244443_0.40714222345760764", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.6", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c8c4555b4fbcdcf7521bc7e48df31a4c7ce68b72", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.6.tgz", "fileCount": 6, "integrity": "sha512-qhHjINcZGTOXax8l0ndd5SGi1Ugx96qpKWoeBZmxgcIkq5vUqFRY4mn+IlqgkRQksyebYmjg/sDlEfPGUua+Uw==", "signatures": [{"sig": "MEYCIQDHbKvFjBuHV4woAoPBE0qxXRyXemdUieq8REkuQCaX8gIhAP1Cm7IRmty7wPweqXlit+kpvgY1puBF5YKl1fjDIPHK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1hDJCRA9TVsSAnZWagAAFvEP/2dI4nsUogf/4/gjVWRL\ncfA1ZFUIc+RQ0jNXhphiwcW179aGsRs76kUyBCw685VBuV0Nmdu5+OYaV4y/\noMEORsPyIZMu26NX4PhlKg0cVexPAEezmLMTS9a3ZuQB2HyY0fLfA7U3jl56\nzMTmZHAMNBzYBQNTOLrVlyI6Z1e75asjxmDPd+oKYtGE4qnhzzc9fsW42DdH\nBUveef0P7SpR/NYGARLQV1rLwT4DqCkO/ZTGtCd27qw9c4xftg/X8MPPyeO4\n/CjJsBnh516eiSB5IqLu9alp3WNiUbFODgXbqBNURxS9mqG5Eu5Akj+znVy6\nPAF19m4Y+ND0wSdpbOhTAr5w3HKWhca9B/PfPbmxe+uYHhblkTNnBvq7mC2x\nH5JKVsW9WwFlZAJ+i2twP8Ob6A6BhFSoBWY/CNlq3vOYGLwuoW1p+P3q3ZrT\nQpuiZBzDCgUewFduL1rgVgXqbKQAycViNpL7j4J6XZSVxsjFUXmiAWsEfuvS\nnETkGae4KyQSOg0/LHIsbqoU04ju7GFYpTBnDGdysEungzRHrd6CKxloFJNj\nP2pVn4O1VOWBeX4gZKciq+iuBa07vr3tMFOldO5cZ/UnDt98z5ni6z4KvJER\ndWwrEkEPphBZkrNvo9vJKEsi1TW8JvvFTlc3CiIZVE+WYefj8S2DJhA1zSBy\nF5tV\r\n=wL3W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.6", "@webassemblyjs/wasm-gen": "1.2.6", "@webassemblyjs/wasm-parser": "1.2.6", "@webassemblyjs/wast-printer": "1.2.6", "@webassemblyjs/helper-buffer": "1.2.6", "@webassemblyjs/helper-wasm-section": "1.2.6", "@webassemblyjs/helper-wasm-bytecode": "1.2.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.6_1523978441268_0.5343278712202664", "host": "s3://npm-registry-packages"}}, "1.2.7": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "5a253b0451dd49ee4159b9bae78ce47d23610d9c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.7.tgz", "fileCount": 6, "integrity": "sha512-fFgRRDeXiFCX9pUJh3W/XCugpl5uutfYWMud9ucRHe4RD22rIda5AEB4b1EjI/OgPSKRughMfVSjfJNpJ0Vwpg==", "signatures": [{"sig": "MEUCIQCsM4DIXMVxOwd5GX7bWetGfL43dvSxGEB5Y8WwKw8bMQIgHJweqIUXsGXsSj5o19v9VKAWPjKu3UkCHzDUD6JoJZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5yuyCRA9TVsSAnZWagAAn2sP/jX6Fa9v4DvgSGfG6pH9\n94YZH+C8AUjoqc37jDPW0V51jTcYyQIza4g2NU4cljMWlMIare1VGV/ENdpu\nu/bkkYvm8jOuBp7OEwUIzGQX6d36zM7MMNlGNiKL9gXdXD5KU0gWKS68LjNJ\nc3n675+YBc2OcUvSL1HyeT0eDlpjogHTssk1Yz9T9J2upPVrAjhGiB79bReA\ncaVUL6XNUjmaqK+469g3WEM0MeePik6da2mFZglFMQtUnjDNKK+B6x+EVzXJ\nv8OVWb6rpc0i56Qm++iY/DuyqEYycDqnU/xxnYnsRHsjUwJJq5Pb5Mrw5IBe\na+9W5B0sQ8ejxYEe1YyOW8/jbueZ68rLB0qrrdIu8ufhxp0RtMHEt/bFpe5A\nPQnxVXnLDyViwPPWWxE7dt3uLSpuO1L9JU29DW9PC2OECwHokjDKTCoYLJ9n\n9MHRNKvF692L/NKYvkqacjbP2fuMUxeIWWKS4+L098cP3fTHDu5YZGsIB3Em\n6tS+OWQN+d/JC/49nHzJZmdQoxUY0haoUD7gvyr5LacnlFicOrcWKVqpnuMG\nenGjIXteMJiFtwWtTXwSiyfBOIIZ1KrnJCHAeREbtOmNZTdBpmGWmTQtOASL\n+pbgeBFLQhLVrsvLjkwqBLN8k8NctMuG6C3vxYaHf7+BUQajzOxIbOc+5Kq0\nO3yN\r\n=oZ3h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.7", "@webassemblyjs/wasm-gen": "1.2.7", "@webassemblyjs/wasm-parser": "1.2.7", "@webassemblyjs/wast-printer": "1.2.7", "@webassemblyjs/helper-buffer": "1.2.7", "@webassemblyjs/helper-wasm-section": "1.2.7", "@webassemblyjs/helper-wasm-bytecode": "1.2.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.7_1525099441251_0.18906987356666183", "host": "s3://npm-registry-packages"}}, "1.2.8": {"name": "@webassemblyjs/wasm-edit", "version": "1.2.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.2.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "f412cc553c70d8fb1249cf6e3b72e895c37aa508", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.2.8.tgz", "fileCount": 6, "integrity": "sha512-NraxN9+E05hY/S4XGacGnfojn+1LdaPcjRhvYMBX4TptdijQQpP22Fz7DkqTArjdNnT34eH08enoCPitVLykoQ==", "signatures": [{"sig": "MEYCIQCjvcms7BDwsJs373+/lzoSHYg1Ty9Ilpa85tq/mvbRpQIhANAZb8f7BqhFRsARkaBUYBb2JneUYErjpJdtKHpjCX+3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fSLCRA9TVsSAnZWagAArOUP/3AAAl5ha1cxNlZ/q+xe\nUry7YuQa2KwadakUlDchtYAZWMyRXk48y8jOgj26uPZvSmdoODnw7cZkvDw4\ncyz30R/VrXznNwnwa5COl259tHVIJ93N1rzhgeMJ7yfZIGPu25M9Zn/i/U3Z\nIsETP6m+4nJVo9/gD/L3Vi3bCtB9Ocdj/AoFEiBzOzS+uquMjdhbtPOLoCuw\nZKBfauZzUR5orGPw/75poPbsBPF/Dl+Ok7RVSefCITwwD8eRXA/DBq+F6Nok\nTKCi2Ql5woykaqSw3kygRjzJUIQJgWkjwQS/QQuClPzRNz7dM5pPZK3Jk3Qr\nbJ/1KwmxOecHPDdLPpmpqu4S8UIzOPjt00P769Hk+79m7VnkaGkue22+goSW\n/rBK6nEKpdAWeyO6CvnpVE1H7Me1nrMPIJM2v6pdRgpAalVzOYaIDiPCObzR\nrj5pWM+f7B4ZjzI34lpjCXKYSFgVHG4IUSeG9Lso4JrYp5kbWX4F1YNb/DBS\nf0LfqAK6OVGINjIOc2AD3zCPuk5wyvHbY6wHXgfY2wa7aJWTmiVvguP5yAC3\nDwufe5+ftty2VrH5oZwRpQasfUAmLJjnSSY5Lf5swtKMV5OvoCb7YbSNpV7X\nCT39jK8LVzG3apXz2fuxyJXmUo1lm2AvS2EDgrdeASRAXw4AhUNbeSi8B6G8\n8PXo\r\n=lii2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.2.8", "@webassemblyjs/wasm-gen": "1.2.8", "@webassemblyjs/wasm-opt": "1.2.8", "@webassemblyjs/wasm-parser": "1.2.8", "@webassemblyjs/wast-printer": "1.2.8", "@webassemblyjs/helper-buffer": "1.2.8", "@webassemblyjs/helper-wasm-section": "1.2.8", "@webassemblyjs/helper-wasm-bytecode": "1.2.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.2.8_1525281931182_0.4473874031671927", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.3.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.3.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "48551c391aebb07e82634cd4ecf257456208a0d3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.3.0.tgz", "fileCount": 9, "integrity": "sha512-xfrnmh7WTxbc55FJmpJCqOvdctG1xZeetdasYLEbPfUP8AgoDovjkhpMRBJcQk6z6AOuldt93rkxbjt9fA+bjw==", "signatures": [{"sig": "MEYCIQChCk5+TcDcrivLj2KeHPeiv4+7UeyZDWZid7bG+emufwIhAKwRTW8lakTYieUfWITLmXxRcE7iHgC7QCJgqjW1t8If", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GDACRA9TVsSAnZWagAAJmMQAI4fd9Du7ks9e1opaD9F\nlg5ViLBHH3WFAQRWlCS+xaxtYS/ZuGuem+PjeCfuUWwFnBFTS0DNnuyIkSL0\nIycc4io+JLme4FixenBbIXRYpR5aD/r5iYeJQwNDdydT2q7exolpqg47PQOk\nwoWRvelFbkOTRgC9l+m4XD4c6vvubklXCEq2wAIsCKZYVnEnpuKzcMn00w+s\nv+oSMv2lev5u2THcKtbedpeI/oTvXvbCchIVIBknccrXzIyiDnAt58jEcMd3\nMyQu2dKam+bdqhtRp/09uRi4UFlfZ8FGpC4y5NUDnuuZL/2CxNDY1elXHrmb\nXPCFKlc+Cev1T4W1oVqQcX8Czg2aFZadU4MrlKrK1j3i8+lpqzR+Jm5Fd0lU\nFeqLfVjRjtwa2dFatkrArDXLAEL8vkG4IMs9hRnYBg04PHEKlunJZRnHOqgr\nxEzoAOD9pcl6LEOg9AVGZ/HA45iJFsmWI/oNEfIzfLuQ3ZnllxscyMPGzq9M\nni+H3lpWn7ww7R9yFK9bofHYdOeBRJxDWWCUlzOI+qRLmbOC/XiMueYuZdCe\nxo+7uVBcIE1mMHRfXOckEtav2H701LL2+JjdbZvis7Xk+G50mZSZ605BEGia\nZ5vWouCfkhqFH3fBqRFdKGUzvNdb+6Zxf3vIoHVqZ94tufFf1zP3gEPEF7pk\nhDBL\r\n=ORbR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.3.0", "@webassemblyjs/wasm-gen": "1.3.0", "@webassemblyjs/wasm-opt": "1.3.0", "@webassemblyjs/wasm-parser": "1.3.0", "@webassemblyjs/wast-printer": "1.3.0", "@webassemblyjs/helper-buffer": "1.3.0", "@webassemblyjs/helper-wasm-section": "1.3.0", "@webassemblyjs/helper-wasm-bytecode": "1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.3.0_1525440703473_0.06924303950985089", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.3.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-edit@1.3.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a16ca4d9a12144b1b28d4e66ad1ad66ec65e479e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.3.1.tgz", "fileCount": 6, "integrity": "sha512-0T75tHKR0dIDiO3oqBZmo+2McJy5FkgSL2AIP3rJvqrDFaJ19jCrC5KLqGbqo3ZicdY9x9Xdc9zaY2A1TCkAiw==", "signatures": [{"sig": "MEQCIHchz9hVgymZAEoQuP+0/YZHpmQSoboJk2HlTXJYcrt7AiAJDyGwcE3WGY/WW9QrxJcvRj21bgCatDYErEK7Qaxf+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JZsCRA9TVsSAnZWagAA8dYP/2mhpSXPDiW+3ZStmqdP\nNEA8axoeVV/YVGZRL2z2BXrCMC6OWebNhXNyyjGOPRfeHttBeXYfn68gMLq5\nFocm0pwH9UZsKrOcBOSxogypGU1LmlMDdb6uU2IBvn19aDkKk++zICOTSluT\nhimm+VJ1y86Q1zpTBgQNFjFQkRWsfb39mnCzmy1Besd4/sfvi0kOnP0gZmdx\nI2c6qKqZYqfIFf/Y6G0/NdL9vu+3Q/SCHEKElRNfsmSniREU8MggpOJ3iWQo\n8nRYqkQnx5V43iQGTVmccoKf0swf9y2ShAGVMAmXdPUXd3tRFCxrbzXDVV84\nRe4ktI7uvsGMzVGmGVP1ONMbSw4vFTfqAKBgraVH9cfFEtYVRhX5n/W272NA\ncmLUdwyPblSLvh0i15HhFzz0NYdV4H2GOds5xiQdttODZzjyWfXMkjapyPD1\nmlyhIZBtyDGQbfve3coqqAZzmQ2ePqxgGjdshsI3bJdWIXR4vrI2AmTDuDl3\nbRxzKBOZCRUyzindil+Da4EUua8ing5mpUmLWeUoGOA4vXhhwueOdcbV+I0p\nraPUBWbivHzFrF+OsfcCDLBn2BqaY4MMkMKdYWKOZttGLgZ32+6/J6aZDOkA\nIEyqC4jsbJ/r3UOocesVD+MfHLDJjm32wuY09k8bLlThyBV+jkKWMd4i8HHK\nm85v\r\n=z+w+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.3.1", "@webassemblyjs/wasm-gen": "1.3.1", "@webassemblyjs/wasm-opt": "1.3.1", "@webassemblyjs/wasm-parser": "1.3.1", "@webassemblyjs/wast-printer": "1.3.1", "@webassemblyjs/helper-buffer": "1.3.1", "@webassemblyjs/helper-wasm-section": "1.3.1", "@webassemblyjs/helper-wasm-bytecode": "1.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.3.1_1525716587952_0.8998808389133757", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.3.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "eaf8df33b4003f88426a34cd4981c60127db0a0d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.3.2.tgz", "fileCount": 4, "integrity": "sha512-78c0BApisoF7/+SHYnKdfhQlW+kqgVZv6y2lPljc4m+uP9G2UOWgoizffbW40iuG/9cnRa1u/NihNkW7qG7M/g==", "signatures": [{"sig": "MEQCIAIz2hO19Un6wAO6cMhV+qWw8zfmoR3cdufzBGiyVdhxAiBrHUxlpa1V/4b8Gf2CN52pEYV1G0xwNp08ztUXydhxtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fDZCRA9TVsSAnZWagAAPdkP/08G0OGfzbj7k/7SWjxN\nQbtDZOoaHY2XCi2wog5KWR4DzFyIo+uKhQbfhRreRu5QxHXYIOoBaAUY4cBX\nbJhobz43qB79AQm0pkd26422dANJTTXGEqnRXSzpTBd9zkIOu0/Vj/fHMTlo\nk19LP7rdAaVgm83qkgjC/SBLpfj4abIzHBVHYLUFo/ZjOq0DZIs9LROj6Nxi\nC2PK4+pHYICQqtHENB0p73SwljLbtX67Ji7tHyYvLrYarahf9+orpaqsXLDR\nIQ6heeaJe+9QnZOLPWmwpCfxeAmtsi/L6Tsk1Mxkwha0D+UIqEs2i+/Fh8bH\ng/+cJmmOXCjvhxGckFRRlasObBFuygBgajEiaYYpzp6iLCB2HnlLsPLxm5S2\n+465R2L9QB+4sZDdnRLtrvqtLZo2hwlDp8/atVztGS5GuPJkF5w/z03k2OGT\nYKO8FOKJJXcgQpUumQDVPRIyNaGGoLTdZCdOt1iFm//qwI9EI0EWEneTZik6\nAgfOrW76a+e3rif0qcxa4jg9U8Hh1ifJKt+QCK+/9EZTNtmRnWI/bo080NiK\nQwet/4XYAja3ytms5zBanYzG7PXSuV4yZQ88vkXu6fvyApn/ss2SQC7ZrdFt\nc0Nw9utpqBDmQKLPJoVbwEQfTN68u/XTZZ0WZVMEC9MdjnLLzqRvGXoUiIXt\n5YTp\r\n=Mvw/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.3.2", "@webassemblyjs/wasm-gen": "1.3.2", "@webassemblyjs/wasm-opt": "1.3.2", "@webassemblyjs/wasm-parser": "1.3.2", "@webassemblyjs/wast-printer": "1.3.2", "@webassemblyjs/helper-buffer": "1.3.2", "@webassemblyjs/helper-wasm-section": "1.3.2", "@webassemblyjs/helper-wasm-bytecode": "1.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.3.2_1525805272152_0.8058200930368677", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.3.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.3.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "88374e6d69ed1398b5e8e47bbd4bbc219256f8a4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.3.3.tgz", "fileCount": 4, "integrity": "sha512-F/y6wNRX8EiYesIIG1PU1opsNpqB+YEyMlhoHQYHIPQ1DHRbfHyVuFiH+3QxFRB9q1SU0VJlpu+UormCuQwt0Q==", "signatures": [{"sig": "MEUCIBoFlHvWtgtoFTysgccDeZP/BYj367bQHufPwUOvUamKAiEA+ftiigMzWd/adUBZAQTh0EyujUdASq4NZclbf4N10do=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qkWCRA9TVsSAnZWagAA64MP/iDBBkBbNGXwkKw41rEA\nRh30yDbMy6yKHdk9oEhphJJ9b5WG1q8MVZe5sDTkdBHPeOjZ0pBzAbo0YJ+y\nZY1bJoWlj+8SHscjBynF8p6fd+NFrUdvMwlZUPvCT2xtQaUkyh3WKeIv2XRr\nZGsMqNGoLgreTgVpy+wRjgmzx4/GiCFEzeZojHNFUb+RKX3sMBvJ/27u1b2e\na6FcJFphBegyys6tqrJK1bpOPFoOVnz71xkYUWJ3W3wyjvXqR3xF7C0R5+tf\nfAFXBBTGgb82N4j6/EQ7vqMdvloCfANTObMPH6lJoxUfysGLSNoLWanuliFv\nKNz9FemlfozDbAeWWoCOossmHVraSDWBS5L7o3t4D+qt9Yi0OyF226JDpYUP\nGdxOAgUc7e80rC1YEl/4gqsyLgJcsoqqFf5Aqi5s30IhH1403mhmbWRohx3P\nydYZT2TpYiLZPNVBq9Rd+tAyDR9JxvgVaEnpSEf+1lC6tClAAAzyie7jXfyo\nZ7i2mgAjNrCpIlcryokNbAw5UBJiCtFLN6d8kvwhZts+SujnAa8dfwPAPJZP\nnVfurXRZnwreNHvfTrLzYnARQFdr0ebQfL/gLNbWht2Rx+ZIhb6hVtxTZYuD\nubP+5mpo3n4QPWyY16YXJ37K3yMc8s5lfNVqHeETkalM8fDkqV1BJqZYFJzo\n0EBU\r\n=JdKL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.3.3", "@webassemblyjs/wasm-gen": "1.3.3", "@webassemblyjs/wasm-opt": "1.3.3", "@webassemblyjs/wasm-parser": "1.3.3", "@webassemblyjs/wast-printer": "1.3.3", "@webassemblyjs/helper-buffer": "1.3.3", "@webassemblyjs/helper-wasm-section": "1.3.3", "@webassemblyjs/helper-wasm-bytecode": "1.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.3.3_1525852437898_0.3719914906000712", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.4.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "96703293b470bd79fb33304fd940f4c905ece2d2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.4.0.tgz", "fileCount": 4, "integrity": "sha512-cWeP/TMmq+hz8RxBlEutGcCZ7KLJCBZXsT1wle1O+crsqF9uogvFDojBPCnH/BvBufN+ANGTGvNfqTs+x6+2pA==", "signatures": [{"sig": "MEYCIQCLf3SjKzN55ByZQcdUOlYoxLXGMDff4setxce7ftjC1AIhALDnXLOB2ndNS367L2Mr0oEFbemBMXZE2DXAb+/MX+j3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wz9CRA9TVsSAnZWagAA12gP/RgDYylHhKpIpaYASOPD\naqtDmNplBoEzJZf0IB8RYUawGoXjXi1L0bFl5lzLvOxKaz9uFDm/1FvfKC1i\nLjjiH6nQv7SBxC8Fv8CfABNDaES2mAYHLwHWdS6Dl5OyqW1uvrOJNDMsAwZM\n4AxbRcHdzPxbybYIgXTxOttfrfSEIJBaaiHcLYG6M9edIf+/jPVx5bvbBoGY\nu7oe71351/vLXqVx9WvL3frZdm73YPadfrVVgrMDIVtihtXV7uHjAaCbPvaT\ntizH8fcfW9xe1/wT+jGLaq9yAYaAhe25deRBcxSClm0fO/mlLbjrOhCNuUYn\n4UQwJNAVW1qfB+ecQWv5mj08RrzoJvIkroTE37jA54IbfACoWkjwLWIyg0V4\nvNW/gaoPeAPAKZPs/uvLXNPTjGDclshkt+yoyliKQxQmwFd5hAu6B2DvTx8u\nYdPr2RManout1IzBBs5x1ltrV3jECANsFBzOu0m89ML7yZDUheepB0DQCd1v\nc0QPLNH/0a7NQMbgaC9MPFUoxRUKE95ph68lEqrUHp8023yJZgQPgDTO9vT/\nJjl7n28SsAEpDtLH62Jq6FnEbSl/rvPioKwCtiPMuKt5jspniCcD+l36R+0U\nw5OqeYFZLZwQYp/1+p78mnq+Foik/XIrSa/gBXrYNU73oL1smVVZcDajIhxD\nCY4s\r\n=1Vx+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.4.0", "@webassemblyjs/wasm-gen": "1.4.0", "@webassemblyjs/wasm-opt": "1.4.0", "@webassemblyjs/wasm-parser": "1.4.0", "@webassemblyjs/wast-printer": "1.4.0", "@webassemblyjs/helper-buffer": "1.4.0", "@webassemblyjs/helper-wasm-section": "1.4.0", "@webassemblyjs/helper-wasm-bytecode": "1.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.4.0_1525878011540_0.7948911210974217", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.4.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "76bc68470a64c5c36309146548b566c5cb3ce2ec", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.4.1.tgz", "fileCount": 4, "integrity": "sha512-8Qxtk6J7SeTUYv0Y4QhfgGJ5l4pAk7sBIRWLr2VAJrfDoZIW4xDonBfxRiC8vtTi6fqkkBUQMVqMVDgX5nIzKg==", "signatures": [{"sig": "MEYCIQD6/ACvfDmLH9Dscw26ISm0U01RXnRqwSIvwipJ2VYkbAIhAP0fyS8IhiyjEFb8RIEt1GQ8irwCNHa0oF2SXTTse3sQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13485, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9KFXCRA9TVsSAnZWagAALiQP+wSwKB045WMpTogLD6sz\nBgZq05oDcUTk8k/+wYpd3LUQqTf9vWy5wzs6TqJJdEGrX05Hzt/suIZOdLV6\nkiDdGdYuSy/xFZ3gdCwXo4E00lTDb9blT2KMX2Rwbhb9afsIopIRV5ZCPxHf\n66s+UV/PiNd5D2pblk6cr7GEvOIk0GjimiiZErDp6aQUryDu7FHMK6kRxVBQ\nCi0eFF9waRuYxWe3UHhMSe9Tx1WFhzcfaSs0YGVQi/B8UU668iMKmOhE9MZv\ncsPAJePj6hwiGt+tAEGK4iRq9HXLXlpOF4MnVIkoXmop1LfupagC1PesNt3B\nQZDjRM0/xo2Y0uifa5cLOszxEmAuUQ7RJMpjRFL3+fKY3bfRiREw1q5liIr/\nF1i5D9tv0As7yapCx8yruv6nI6OWF/R953yX5z2CwLRxXOiIG9CCKL22XlUM\n0MBZkX4I4OnvGVLm3jrDxQ9JlAe6gMRYr1RxYaIl4Wmzw59xCvr7yZxwWw6T\n4R8pVEu0Upe9uWbcD9bUJUlsu9EBvaNkjQy+gr8zVH5lntipZMiZoZ+aD/qg\n4rR7rNV/TheoCzibpEpiPLCYj565krq2UYCeOYVUbRc53iVhXK0YgaUCocfS\nmXSQHV1YGc6m2cx4l5zoQ312vT9PFXA4tzbDzhjEjaB2I5iMeT+OorgVTsdO\nTtDs\r\n=BURw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.4.1", "@webassemblyjs/wasm-gen": "1.4.1", "@webassemblyjs/wasm-opt": "1.4.1", "@webassemblyjs/wasm-parser": "1.4.1", "@webassemblyjs/wast-printer": "1.4.1", "@webassemblyjs/helper-buffer": "1.4.1", "@webassemblyjs/helper-wasm-section": "1.4.1", "@webassemblyjs/helper-wasm-bytecode": "1.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.4.1_1525981527039_0.6864166388023683", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.4.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.4.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "bde9a581065f63f257ed511d7d9cf04f8cd04524", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.4.2.tgz", "fileCount": 4, "integrity": "sha512-/iVXRqMpFAs+R7Jcw9pJAv0dLrors3SW54fsFtWUYbOt7Uy8Nfp21zVuCgurlVfwEIXyyyf8LqdZGJm7FmL+ag==", "signatures": [{"sig": "MEYCIQC/H/vgiDFloQSd28pvIsqzSYZao+lRDma0KavXtdub1AIhAMfO4jFvHcbUtGPP/dOkr0La7sLXP0JZ5IjLzais1wnd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aUlCRA9TVsSAnZWagAAlbsP/3dzAkZh2nOOZWTO61SR\njLKzEYx22ce5g8QMiNp4M5HLWuSGO4THPzxLRnDxJEq0ZvbYRddhyJ2Cp/Vr\nTPGCGZUJP6l/6ksmD5Aa2KbPc46cQWF3N+1wCMOGLmO0crD3PlIM2qrH4TNb\nZ3kdjfweaAuxG5YkzmwFsiWDD4VnXlZ6zAuq5DdQx64vRt1hWx0ptr5CEycB\n7BCMMHA2loZI9g6PcQ1m7d1RmS5flclxhj+6xL1uZeoDcS3oscyYvhOpMSne\nXCVsVRzsIfVaa/i5MCWumNRfkY8cnQ8jInZgNfhs0GSiY5R4EnHq2j0NWEyA\nMFFCfY39P1dzuuz7YrA7kRNI62OrFGRwrg8/A09I00szP9rZHw6cpOlHiSys\neOoD5krz/FP+ddbi+1oKDLLxWW0TFXM/yJzYWqrFJGKw8Zd6pPA/WmT0Bhth\nPmVXldLM8ecoSn+rdJvwhDA8Rn5Y+pLpq8wFFvpFXXGbaIaGB1jKLJksF9nX\n++HnrEUfO6JQjb1m/4uiCXqrMo8L1ezFjMDM0sGNO0xPomrBi1WDQuVaEGy2\n0LW8NbPCYhEDmGr2B8+e8jO2A/Wt8rzBN+tWGMXVkOLnrRcLerp+Z4gay+Z4\n9682HIab0P2CojILgAGHfAYbLm5rMXR1Sbi4nv7x7IP+Y4ZfPGVWtbWfMJLV\ny2ps\r\n=df22\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.4.2", "@webassemblyjs/wasm-gen": "1.4.2", "@webassemblyjs/wasm-opt": "1.4.2", "@webassemblyjs/wasm-parser": "1.4.2", "@webassemblyjs/wast-printer": "1.4.2", "@webassemblyjs/helper-buffer": "1.4.2", "@webassemblyjs/helper-wasm-section": "1.4.2", "@webassemblyjs/helper-wasm-bytecode": "1.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.4.2_1526048037075_0.005093471482700007", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.4.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.4.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "87febd565e0ffb5ae25f6495bb3958d17aa0a779", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.4.3.tgz", "fileCount": 4, "integrity": "sha512-qzuwUn771PV6/LilqkXcS0ozJYAeY/OKbXIWU3a8gexuqb6De2p4ya/baBeH5JQ2WJdfhWhSvSbu86Vienttpw==", "signatures": [{"sig": "MEUCIFv9PZJ4CF/oMvuWuJmtVoZ2xek7SJfVrW3r7uvbzEeGAiEAkO4bJv5UiozzhNMdXC5IFKWe0LIe9EnzE2WDrcSqc3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rIsCRA9TVsSAnZWagAAUTMP/jhah4R9TEiFQ9LppcWa\nm+NsV19U8YYv17b/L/I1BJmFbXJ32GSnnKOHDFr0eRCL2OPQqETYZsujuOD+\n+wQAkPJhdcGFyKvI9IesC+j6PgqGRIb+K/J4Ndzp2MNdoCqPCbYlAw19QYFr\nSpSvvNcXcZbkxqRxixuJXdvLbA6rc3HAgVMETW0wU0Eb4tmWzgF4/4CE196V\nETBVB5C2HGmdBUgRHCjmBnKnd4N5/cM6MRP2RJV/YlV9/k5XOumoApsHaLV9\nPRGc/wuszZ+041zJxYqN7J6DxF5Rd4KxGrHbgtomVLmSnO6MtOIlXzvlfet6\njRDaE5s2tbnNs7BF5VD+WqLtOSeBOGywzZf1f4GkqPio1QLGpCp4xrS0GB7v\ngRuSp0/ldxlPsKoBiA3HU134zMBPRca0zTlEnEKESf4sp188fjJBBMSJ5wht\nwmOHkuy9xorPoZQ7gxrndPH62zBB+tPAoDZGiz3TcrobQqnzPNQrShJGLsQF\nBvdVWFolZsd6rmg5VcdC/6GkuOiwe6obsaaegYWoFGkbrc3lhcgJTZja261E\nubu5Ol/mMCAs6eE3lo5umSmdS/pawrL3keMakw4SW9Px7WhBrzHSWh5T0q8e\nqL8ucVhndcd1b67bGwmIAlpx9KwhsmQPVnASUdY2DKoDz3J9NS3/1VRp7rg+\n8jH7\r\n=27sx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.4.3", "@webassemblyjs/wasm-gen": "1.4.3", "@webassemblyjs/wasm-opt": "1.4.3", "@webassemblyjs/wasm-parser": "1.4.3", "@webassemblyjs/wast-printer": "1.4.3", "@webassemblyjs/helper-buffer": "1.4.3", "@webassemblyjs/helper-wasm-section": "1.4.3", "@webassemblyjs/helper-wasm-bytecode": "1.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.4.3_1526116907892_0.5763225505681646", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3876d692afa4810dc05566e860a2f6c081865a9a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.0.tgz", "fileCount": 4, "integrity": "sha512-DdbeJvyoC9UdxPgFwYTxok7gPjCyaEKwDmmL+KqeJ9Hb1gVV6LPyar6a2jLANFFE9wAAzYDZhxh0hWkzSJ0dHQ==", "signatures": [{"sig": "MEQCIQDgWwaSTBtgsMHsskvX1AzMcjaIqIUCz1pUhTyA3HahUQIfKXkt8OWT7BAEFDqIHlpCTnSER+nrdAmDaDM9Yv2jRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b24CRA9TVsSAnZWagAAnVcP/0Og99pVuPq0Y0E0GPyn\nTAWpk+uKGjt0hUs2jpS2vVB3o/7r1hRU35Qui0Z5XHTWEwN8ssbEdQ0wm1ak\nANuBouWQfWZDfBdCnNC2TWjvPDLxzwHchnGRPkVJMMZRfPszl9/D3kr1o8eF\nBQa621ZFJhWwsAL2HezpD0bsUhpWh5gvqqJ/9HXKLyUo85Xuoe71CIu2FEQo\ns63Wx1K+fT4ftSat96wnPKqQjuNh+/qKfPnXFTDipHx3jmYtPAW4ozoiSVsC\njNWzFyT6Iqjv94lnRLBlmk+JWs4pMtlVhkQmr4bod9gHXmFs0XLPNg127OyZ\nJa1aNhRcvJ+HZuxCjy+VtLsm4lXcWRIic92guJJk/zqfBSiaaI640eIJzmoB\nni62hsTZho72V2zL27P6H2SXBMNLsL75duW82m+A7unFG1so5ZFzMARaqAqq\nR0wHLAT/qLv6f7oGwaI2hGQ49drASeaFgt/SMUNfRJ0Q4sdnD8UEK4VPew1J\nuV2J+/rzXdl/5cnCXoeoPFuY7mHBxPmtxNIbTvvpTcJ5sAC2QI5DAba4uXQf\nmVkA4lwQxFYtY7sXgaZ4DMfzR+xIG3p+ecjoOJa+ZO0AW8W2K50nHsP8KmGk\n0AGKqiT7JRPAwR1tCSiPgYrcE4CprWyYZ7CawvwQ5IDBykLTpLW8clbTv8xG\nLZtt\r\n=VMHr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.0", "@webassemblyjs/wasm-gen": "1.5.0", "@webassemblyjs/wasm-opt": "1.5.0", "@webassemblyjs/wasm-parser": "1.5.0", "@webassemblyjs/wast-printer": "1.5.0", "@webassemblyjs/helper-buffer": "1.5.0", "@webassemblyjs/helper-wasm-section": "1.5.0", "@webassemblyjs/helper-wasm-bytecode": "1.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.0_1526316471654_0.5457141750717571", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "4b3eb413031e63de2c4fddd69eda3f0a2a43c772", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.1.tgz", "fileCount": 4, "integrity": "sha512-S4Pj02vqaiJh3HL2F13Kb21mHjCDLqD9E5Y/3Vam4K2plQOht1C4KghZLAN0HivlfgYjHb97BlzS2I4QK6TOGw==", "signatures": [{"sig": "MEUCIQCaOG1SSzEMNqL2uHpUxb50eZc0suwkMmokdmZJlBZTJwIgRx3xqq1Bb1Nn+7sDPhjossCA7OY+hs5jB4njpLQVW+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BVcCRA9TVsSAnZWagAAA2cP/RuWKJcXdXj4t9IKuTbd\nI96VcpmsXmFxqtKfjgPzWthia2mTMK1EyUWJ7EKninIY9kJZ2ArPX/T+A0Cu\npByCNGLjvZ06xCypK34LzZmgWFnx1fNle603uCs8MBZb3Oygi9T2GCPB6Vp2\nW/T269v7vWHRJKepntkvpb9BY28NIB6RI0Dp9f6kulE+FtnVGZdHL+YrC2vr\nQtNZcVN2UkvKjYcX6nv2BecJhXEj04ilvo6UBKGcf1GPiyfw06+b+24uwxhQ\nGDc7GoreNI6jB0/IiM9suVBT78Es0xhN48c1j8YhyjIFELyymOTIbPXgl7PS\nlbdluI4xwjnim3SnprSpDLFx6CPEOnlpvvMS6k71xw+GLURpGqeq7Hr3MOR2\nzzevqnb+JBxs9sx1pjS6uHaN3yVob6I59RyTP/Mt28OLOC6cfAWyNUqNijKs\npgW0bNIghfzqE7OlX7H6ytV8G6Dg0mRI2DH2glWj8CGyc/Csw+9XjvZgyrBO\nzCmeEGdPxl4ui3BSkIJkUU69i4AIEOrfPpAJwIzIgOHnh3Rn4zD0Kfas253w\niPQeP6LnEmugzXVi7Z3CBE148J6dcCqR1jdFun8WcCM9B4ox/VbPGrwYzlc5\n0yByybfE5bTvbh8IKsGFIRw7CrZYhQoMBBWUeVnFqckc/M9lkdPUklQDFpFa\nzlx1\r\n=Pme4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.1", "@webassemblyjs/wasm-gen": "1.5.1", "@webassemblyjs/wasm-opt": "1.5.1", "@webassemblyjs/wasm-parser": "1.5.1", "@webassemblyjs/wast-printer": "1.5.1", "@webassemblyjs/helper-buffer": "1.5.1", "@webassemblyjs/helper-wasm-section": "1.5.1", "@webassemblyjs/helper-wasm-bytecode": "1.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.1_1526469980008_0.5287601921160809", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "526416a3a3a7281c77a6c45ea6fc5d362e40e754", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.2.tgz", "fileCount": 4, "integrity": "sha512-daweZ1HkUeM62gOj4Kz+upgzQEoc1Ds5eeYzAUbitfzuTiOPAFnl1B6Z2PIxWEF5v/oKVCUDg2JykuPOspOcjg==", "signatures": [{"sig": "MEYCIQDtrPZyADNYL01gZI48WijPqmnr+WN6gu2Sou8qEK9eGAIhANtWEPzETaozqSy0xrOCD9VERgCHgbDALTtI5OIpKsAL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XrXCRA9TVsSAnZWagAAvH0QAIwA4/W9RCJmxBt9N64D\nsP7/Qp3Uae6DvhteGmt76sqRLWHxS0TEY6DepXbTSyjCcKofIKXbdLKQQQ7J\nOgKZuD9b7KExkMLrHZ7aijDgOjpS7uBjYWOMcHfoM+inKUuL0OHnTvqNn+7Y\nFV0P8brvllQUbkX3lVkcEMrXkgX60xJYizdZ0upz+N1oyuc6WlcLtUuIaPY6\nPZbr11XAqop052UdgztOBF8sUY6U6uDeEe2H4tCcENQ/1yyhkdpfJne77YO8\nuVBVx25TqZblhTF7LiWlHFG+dWVq+SG6W77bystp6WqzZ8b78SF3hWV969vU\nibrMn73h6GBcpyPuwR7jJ3SSW9a4+4RFSehSWvrvpiYlZO+HjE1gRgEwhWnu\n+cvhOw2feyHciNeBFTMgjgSKZQSkcCqbnoRSlGwOmPscm1lQlTVQIsF9otGx\nof++maMbBAxjXZqLNDP1d/MQ2xrsib7g22NT7Hy/+X2h1Kj/uIBh/DIVQoqD\nr/iltsS0jDGO5eNSvt+Q3kFvOPI3tcFLb9wRZO2IUKluvXQqnFhajchXOqHJ\n/mibk/QYNUj4+PdTZNsM8e4g6yBw5cqhkNAjxoz2RnH1XXD886ggMNZmqQin\nBoYthgBkb4dpP2MYi11QEiNOCspuSYxXqbkRXXH90lEdRyH5O6Z9ZfgmjaDI\nd4uv\r\n=PsET\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.2", "@webassemblyjs/wasm-gen": "1.5.2", "@webassemblyjs/wasm-opt": "1.5.2", "@webassemblyjs/wasm-parser": "1.5.2", "@webassemblyjs/wast-printer": "1.5.2", "@webassemblyjs/helper-buffer": "1.5.2", "@webassemblyjs/helper-wasm-section": "1.5.2", "@webassemblyjs/helper-wasm-bytecode": "1.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.2_1526561495479_0.458622993489743", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "05ade86d765755c7401d509dbe61b401e320589f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.3.tgz", "fileCount": 4, "integrity": "sha512-dTRPX4vLD4Mf8yZYmR9XG/WZ+eWTLv00PIizbsG0N7H5qzehcMlp6SDu1x7R40gdAnWH8z4Q9A0lLUcVYbqoyQ==", "signatures": [{"sig": "MEYCIQDZhPkSsFxEdG33SbipuequU5VIaBfny9QCi8E5B/8PpQIhALKYIDZVICVqxQyHeRw1VDuOuUKsVsx/mBWV6SgaEITH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqjXCRA9TVsSAnZWagAAUg4QAJf9cGBPdNX6HJVZezYx\n2VFp28MMkgBP2TTRIpNRI/uz/hTATBmerHlIWcpEqyeCa3FkavqNOCbsP1rC\n++MYsAmHoAB1k6wS2iB9kHIW26UBLRmgxJi7jGAirD8ZUFTBvVhPBI6KXjo6\nduFZs13oS8RuSauvyEeDqWVHXFaYAB26yob5z7v/5PfI7Ow0r/aG+4y0kqtV\nbyWlWXAsD8KXC6C3OlTua1eYFQAS7qi1ynF2mvIRdtzFJGY2ViIHlkrO3AGC\n8WPXcISTdfbDFKDtu3zLaIO0TyAOJoTaLXyUz2uSxXZBPur9T1/egFp6dpnj\nBAsg41K4PJwVqmSCQWi1XYn61G9kWt6F9gba3r3L6VIV5YsU6CjUlJQ8m+0G\nDNvXtHYoqy/yPdMNJMJAZqETteIDAegZ92SmEaBLHs1y4xijE7nbNylMizjx\nWMPXbpPUJ1Wj18dALxs4elfUw0OxhuWwQ1rluouh2BWqrA1moCtMEwfLtE6E\nl25Ri0o6zWNxKizQqlcN5xpu1GTrj7ln6X/HKWzyCt3tGZYtNxeBJlPisoj+\nYSZiVOykyGyjHvZS3gNELmZmsJpjCAw3/+JUy9/PwUhSK6jpHuzPKsE3Aggt\ntzDB8Z6hkroVbn1Kg3IKvPvc3/cuUilGODubw8fmjGZxuhn+pDtj2Ohrd0fL\nNrCX\r\n=X2Jr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.3", "@webassemblyjs/wasm-gen": "1.5.3", "@webassemblyjs/wasm-opt": "1.5.3", "@webassemblyjs/wasm-parser": "1.5.3", "@webassemblyjs/wast-printer": "1.5.3", "@webassemblyjs/helper-buffer": "1.5.3", "@webassemblyjs/helper-wasm-section": "1.5.3", "@webassemblyjs/helper-wasm-bytecode": "1.5.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.3_1526900950978_0.12481029497679086", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "40d3d76a31755e5497559dfe8733fa753e23698d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.4.tgz", "fileCount": 4, "integrity": "sha512-HeK5pdqX7FKZ70ukmEchHWfepJSBko8+C0TyvAjlcYFlW787Sh9AIT637dDb1c2OP2qJueAhxxsDPsZYw4+3/g==", "signatures": [{"sig": "MEUCIFh6aEd/g1duFNoOWVNuEg8iSlkl+nc3MG/dR02iPpQ+AiEAoTrw5KJ0nNGy8hjLQ6IAM0q92+T6e9BzjJjMIq0ZhGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsJNCRA9TVsSAnZWagAAtBQP/RE2C0xfm8gpGR+C3lOu\nA1jcF4my62lg9/gD17p6Ns9L32nzqUpvnGc6ItRqXwbmDwUX8WP30SigusBE\n01OV2U1z6vB13qEZL0B7et1aJ5LZObuDm5KJk3tFQVzdosY+Kut63CRYlyto\nTbmP2PQdhQq5G+b5z4UIOScc7lwyUOF0MK9fe6cYnOzN/pzcR92CeoEwuQla\nP5V5kgWPun0gUlE/6ZYsrRe8BLJoS1ndFE0YBN00V/iCs7kkfaWOFnNlHQKA\nFQ5Fri/VkuPJBikE8raokIvUqYyR+5KYYm1gIpUi6dBmeFzrCiD0bihA+pCi\nHqlbVR3er1FIscUbwqFyudHcpHwEozDEJvLxmd2XvPWw7WIgjBNF3Cuyum9W\nczjkQFKTiOxn3P+qn0RTeEGtxYr6HJRryhpyCgbxbeC/OgQrFbkq/DjvW0we\nqlNuwT47yYiQ6FpxsCf5whTK/RBZ6v21syp5Es0ofpv1kZ1dH8zTH4nWXLNZ\n+rnXcv/+fA1xyOuys4blRKfZwGbmw0UIhKjYIfC30RaSuAta6Z5D0rJG08Vl\nG2A7Gao4nBqFl3o9+qqhXHkIaHTdupLoqY23xE6T+FNMcw4CI+MOFUER5p5P\nmMLumDkUzSPVrzEcAGiccTHGuDUJWgXvQLs+vCZppb15SWbsE5UkCIMyB4Ja\n9RdS\r\n=YiFm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.4", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/wasm-opt": "1.5.4", "@webassemblyjs/wasm-parser": "1.5.4", "@webassemblyjs/wast-printer": "1.5.4", "@webassemblyjs/helper-buffer": "1.5.4", "@webassemblyjs/helper-wasm-section": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.4_1526907469506_0.8355638910630871", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "239a99330b7bb837eda4cfe6a0cfee7b59c7bf03", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.5.tgz", "fileCount": 4, "integrity": "sha512-Z78O1yMHG2+hu9FK3B5H0HOvZ1S104Io+hR4+fJhSU3yVc7UpW5DdGgu/6lwhSxLhs/EuciOiSkIBo/weNmWsg==", "signatures": [{"sig": "MEQCIAUKNrd5U/mLiMtBDQbjIhVjwD0wzjiZjWym1GcyR1vnAiBSTRu/qjdkK1gVgzSx0Ymk6mU9MTkB4R1Y+KsdmDdJrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnGoCRA9TVsSAnZWagAAtGQP/R94afN9h+gvEJF0tuZG\nx26bxLmg1xihgXAKj4tjLLjtABoHqfsC2PKmgnJPM+aimlTg899eV0u5XEOe\nObCbMuXjvF/s3ywVkMTNtnsY/RdmSoaD7T7T+XsrSsF4agb7Yw4mMfX5aPXn\nzxCxJb4NpFTkshqr/dghpEqPSGUO4WjQw3IpG4zt+YpbWXWFU9ICVryFKQao\nAk4YzBdC7flO3i/vWe9yMqJHXNwA1BDMJ0acrY+KzEmbvP+Cp6r7i1ho9AHY\nz0ETMh4T44mnGsV1mua5vcnW50O8Bl1FMcikiXQuDHXvBC4iDN/K09S6Vq2r\nIGk8TC/5WeIUNVWLSOHRLd1p1TEitnm5WIvL+SNOD374VQGToGlpQb9lr6k2\n1SRVNTZX6Nc8R4SOlOpOV25Gn/2dSbYQLLLF6P76rImxDIyn7Ktg3n7RZRvI\nAKiYc+k7rTxR50YHb0zqLYV95lhjjSwQmrLlsFC03vBgwc6f6+ICIur8Mtme\nkPStdG4LaIDLg501IvdtpS/s1GlNTxo1G7s/koltCAlxsffYLFeIgppIPNOd\njbPF9FAA6yR5ks17LFfcJ0TQKJumMVDHN9J1fP+frzKWmLWgOEHUUDmipb/j\nfGe1LxUcEw3v0I0RUQY0F5AFr+KjZJTHlBKhxADA5JIexeCoc4SLqf0p1fOO\nu7qc\r\n=nv47\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.5", "@webassemblyjs/wasm-gen": "1.5.5", "@webassemblyjs/wasm-opt": "1.5.5", "@webassemblyjs/wasm-parser": "1.5.5", "@webassemblyjs/wast-printer": "1.5.5", "@webassemblyjs/helper-buffer": "1.5.5", "@webassemblyjs/helper-wasm-section": "1.5.5", "@webassemblyjs/helper-wasm-bytecode": "1.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.5_1527148967937_0.9661292968593602", "host": "s3://npm-registry-packages"}}, "1.5.6": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c6ba2551dc41a1da7e2bbd3ebff5b196da2d2464", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.6.tgz", "fileCount": 4, "integrity": "sha512-MVNs1VeoN5x2VqPPVqM8IW9+LMJ1dgSJQJwv6f0I7kTSGXBHYWxOYsHbfqq++/IxCwVeg07lgAqRidtIZpRsWQ==", "signatures": [{"sig": "MEUCIQCQ9cjLUAN6lP1A3dE+mJrmVQbmW2TonqJ5T1hhMBVW8wIgeoITnGLpQGDTBhiQQLkBsW5EE5MTaBxRThscLVEwe8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsqxCRA9TVsSAnZWagAA10gP/R0L6bmg2uzWdvtDaPsA\nahB5a8BXjKnD2W21ECNXMPYwfh2mQ1qiwB0tDKTbdndknujz8h5szgH4oO8n\no+3EgtTdlDFWeU0qcT31WTLZyQheTEienoPesO28XvhpUXinOZviH5TkfuiC\neYrekWN7S+3L5xeouTDvdCMGSrJYhRuPxnp1p2BubebuFR05j48IVs0xN5Ec\n2YcicifjM1omF5D8DJn63kEstBuXEzwpr2kn0vOJYRP1LuHjY3RYhF4fDShw\n7r2sskqr1jvqanC4jLh7+rW6cPNgl5ccl4y0mC1DEp4RHjSxHhPdrR9cchDc\nJn0sOAcpVRPE9Ji3umPeXxgaCWySsrpcnM2PnhS1qy7lug5zIahVan7iemcj\ns55Q0mHc666b3V2DtxlZIeYmdwowBfc0tnv/+qXg36MH6AYLLdYWzDokMKlQ\nFAxORaZF3m5aNDjvgxMQbCNMlX8HeaKRL+oB5r/5zC8ZtXtzpManAi4potuk\nuFfGNbsJzabVIuDkzgVz9PGAQvADyVBDbgZAXMAxlEvMrJ1iDsvpF0MoB+Pg\nGZTHwJ3GZQJVAau9C0527RYQ5ZUYt0mP+3ARWwWoFsC0GH1vIge35r43njci\neYcmq5E84rpe4Gvodi+gUwqq8sluD4QA3cFQEeqo/IxMpljihWDEzTs2a6bY\ncLR1\r\n=m21M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.6", "@webassemblyjs/wasm-gen": "1.5.6", "@webassemblyjs/wasm-opt": "1.5.6", "@webassemblyjs/wasm-parser": "1.5.6", "@webassemblyjs/wast-printer": "1.5.6", "@webassemblyjs/helper-buffer": "1.5.6", "@webassemblyjs/helper-wasm-section": "1.5.6", "@webassemblyjs/helper-wasm-bytecode": "1.5.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.6"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.6_1527171760574_0.4891512836959191", "host": "s3://npm-registry-packages"}}, "1.5.7": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7d368a3a059028b8b6e756ef333730ddb140f0bc", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.7.tgz", "fileCount": 4, "integrity": "sha512-vIy9YnvhEgH2GqeYyVjn+y0RG3pUneiEk1TjpvT1dBTGcCcqGuznsM2XkScXKFBsJHJebqQ2AiGcPg9VrPa7aA==", "signatures": [{"sig": "MEYCIQDeDkrv91hWsHlyzFAAtJjwoqJL53s+fk74vObkxEpQ/QIhAMV2XNBvY5vUx9n9FPQBziaNOOk0a3r426Po5lmQU2S8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAexCRA9TVsSAnZWagAAvIYP/2oxoan7PGg2pu7j9tHh\n3JDV2VBEQ17mJZh0tq78utUcPoqSVkwsKT80Jpl8MqxGdD4uJ82iEQjkAuRu\ncqM5fWQEhnwgJDE7JV2TCvviqjOWKtnMg86YPAVRP8SxCy+qrRGPiCRI021Z\nIEXVir3qGcKVPzQl4KwVrbCXvPvC6XKB/0cbuFHQGrGZKh27qDD+GfXCtuAY\nny83aJlK1yKJdz9fhxaxLZgRjw7ERZ/LKnxoO/+BLiJNLfpibu/sJkfJ+sj+\n2RzoEEnUA6AjatZPfcdjyW+JfsCXNg9m7q3PxgnysSn0IAZ0dHpFlbChKJiH\nH2txRSlRLRoGfkrSPpOqgKxBoZ8TTLqb3txeb/Oit8BvYrEWAMFWzNUTwNRJ\n/ZJp6aCyFSUF5pGma8fx8pwS3zptrn8TWch2jH4mIoItWCpSlbr9MaVFyuR6\nPbiY+kiGghgII/d2YlJDhcty+i7whgWWRlaWdfjKZr2q4xQwMIKp7mJ20IUE\nQcDnst2/DzUGdhnWVaqnJgTR+SO+d20o14lnnPhqE0alKu7EzRk3DCyZERPm\n7aUH/kwDNCCMNJDJP5V2v2w45gYh4H5FW9wOFxBtRtfmeHbpgtmmWyNIy1sn\nPWw1miERpb4eamriDYtZgAGberI0SpbsInBSI0rS6qsCp/i4AWF8Wg95RUr8\nMibR\r\n=Tt4l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.7", "@webassemblyjs/wasm-gen": "1.5.7", "@webassemblyjs/wasm-opt": "1.5.7", "@webassemblyjs/wasm-parser": "1.5.7", "@webassemblyjs/wast-printer": "1.5.7", "@webassemblyjs/helper-buffer": "1.5.7", "@webassemblyjs/helper-wasm-section": "1.5.7", "@webassemblyjs/helper-wasm-bytecode": "1.5.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.7"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.7_1527252912607_0.9247536052091208", "host": "s3://npm-registry-packages"}}, "1.5.8": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "592d3678894eaa2ee7e7c2c6a13c2a697db1aa7e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.8.tgz", "fileCount": 4, "integrity": "sha512-YgdT1/vGAtB2daWhNB6Xg32RBvgy21OZN7wnzh/SVibdQHBBx7HcqoBNbr8M1WwGdDitcdSdxzG0Hg2rjga0Pg==", "signatures": [{"sig": "MEUCIHyp2V3BjeBtAL7YTUIfvNC+qXXG+kd0etz2EzNj3e2PAiEAz7ooUpxE7OmOadc3mAvMELng0dbS38iFZIHzHR5Dzbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/rxCRA9TVsSAnZWagAA22cQAJs3Q3aEp8NuzijHbSm6\nCS/otqtr27nDtzqN1F35P2fCUeArips1UU+khZbUJ8qnXBECyvkJaUaGRQkD\n+MaJ+7sj2GLvFU49Rda1HwQ1eDjk047Dvv+0Mi5dduBW64QASlhPyitCgTCE\nUhsGCe9owEFE0KSavTJxsM+uERgQXK/SdEiHB0ZhYaDHGq924yquYzUu07/V\nYcRX8PcNWUqUlOljo/PwJRoBUvcs5peMCTjXfVPPl9oaD/XSy/F5LfDX5s1q\nQuipH4oV/+wnN1ND80C6ZCEpScXySaD9wOXlhYDnBLbwvU1o8MksFNP7mWdZ\ncJm5E6eeUr0xBcRiAtbuWRK3lKu6Nxbe0kRi7kOivgkBcHnzvPPKE5yX3n09\nICjlMNtt4AT8BcnQg1FndtrB5xS/QFdpHniYA8wgoVSy2bfX82rH4TaHp9ey\nK3oReVA6VaoL9LvtQdJcfWCKoLUDJ3tiRthle+BXkM0fXyL3Yg/sXxY6Pbes\nRxVZKE1zNmmJgrGvj02U/g3AikIn6AeqsA3EDPDZe5Jen4Atc8QYrpN51iu0\nHaSMjIenTbXvw12l7IbZAEEk/IiClWGYkwRUhJ9Vsdh3mjt5YUGGjT5ahV/p\n5VWP6sKsAULhMEf5qKoLacpgNy7Nvwa/pBXvhCg0kyyDLP9dALdRhKQzbRdS\npRsp\r\n=Wv/7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.8", "@webassemblyjs/wasm-gen": "1.5.8", "@webassemblyjs/wasm-opt": "1.5.8", "@webassemblyjs/wasm-parser": "1.5.8", "@webassemblyjs/wast-printer": "1.5.8", "@webassemblyjs/helper-buffer": "1.5.8", "@webassemblyjs/helper-wasm-section": "1.5.8", "@webassemblyjs/helper-wasm-bytecode": "1.5.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.8"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.8_1527511792947_0.8459009534306365", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9b8e054b2d305a7e0528088571c95904bd73df48", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.9.tgz", "fileCount": 4, "integrity": "sha512-pMWe3HomnWAMZytJ5sSNBS6qTbSoULUHkvDrtcarmLBTclmupZe25INy1jxbWGKsuFxw6w0xQ+eLRPlC8HPjhg==", "signatures": [{"sig": "MEUCIQCzV+/tE/Wlj3sF9GCaYhQdQCtDyTsHDrgyGb+65h644gIgUkq0Tma8AI3HhULn00BnxBAMw74DolXJ1KV519jZwl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVIuCRA9TVsSAnZWagAAxyQP/1pGdm0bJCayyLOhgCz2\nhdSnDNomiaNj/b/hnQY1g81vpXk180ALG/KqN9o03QiJZVFnFhM9dCZKMBk4\n25jhXU00v2cIOSJPV6GLjFnkIKr6Y8gPltqoaSuS/JEuvX7jKTA5/UoIFIj+\newUtaCmvBVpmYVFmgGRas39vRF/TNVOAm0re6ukmSV61ZsAHWX8fq76nrB00\nRLvjP/ZD3zY0IiLLZ10pWL7j/bnW3QU35rAPxOZknPRbbDxnRHMPE+cEmAYM\n1H51kQLahEBvIvlkwO5bR9Il2us96Yo75gy5f0cSmmP04/jhrYnCY3ZQ8BlE\nRBq/ioCILOpIHFetiwt5aYYtcdaiDlTJGr8s+/GmtIXu4UA91HpcWqTlxqvl\nAhL9xcsYuuFZ9yoOMDPxnDLtNht5wGWhvjeHm9VtWC4fDXsDfjwHQMgIr/yT\n+KvcMNI4D930OrkQaoiwdj0A1moMp1DDYl+eUCsqomG3arHtJtPGqVfyQKwv\n6NkA+HcCtzh/jfgvzo5EDFSbjP3rUI+w/dBE24FGTfk/6Vi5dO7QEr61nIqj\nPPCbXI1gyssriMcYEvHlXQbd8g0HTnJQffrVYjDCwL3BS3fDsLeBGDgbKzCZ\n1TxZzWp3957Y/XSJ8NjJRgeGOSznJ1Gx8Ue5Gc/xdPRC/jgH6EfLUo1OaAME\nJAC1\r\n=+VVL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.9", "@webassemblyjs/wasm-gen": "1.5.9", "@webassemblyjs/wasm-opt": "1.5.9", "@webassemblyjs/wasm-parser": "1.5.9", "@webassemblyjs/wast-printer": "1.5.9", "@webassemblyjs/helper-buffer": "1.5.9", "@webassemblyjs/helper-wasm-section": "1.5.9", "@webassemblyjs/helper-wasm-bytecode": "1.5.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.9"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.9_1527599661818_0.45022041235791055", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0fe80f19e57f669eab1caa8c1faf9690b259d5b9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.10.tgz", "fileCount": 4, "integrity": "sha512-mrMZw5A0+p6A58iquzq/d0SJej481H4pNwPO65rEjzDsHs+yykT6de26VQD2GtaCTThfSNcw3JJXWJ1biqO/+g==", "signatures": [{"sig": "MEQCICFKQj8/nBaEYwJf8nci5zSVU8sPP7PsA5H7Z4SrtFbTAiAr9ahodBTMaqY2uj1oLU1+52FiIdwybgROE771xJ11bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUiBCRA9TVsSAnZWagAA3SUP/R3lTGy9jMLqbirZ4j28\n6pTF+ABLT0q7kBi/1S8EM+xSJZIlRzXMda7+QfpHMM4OXnfakq41BZT0ttET\nNkqK0tLj4nnVjbwyvhs7iyRu2YqS2Szp8ua2Ibyva3ncEynhCC75Re4iImSt\nhtufwRKFhMEuJ9/Q13mtHOc2li49jdy5FlCMQz65uHAzHL2Bp70k2uqlykne\nJ9oaE+ZCgkW0xQeNwgD4045DYTPlViRTs8BWW1dO5LAiXriWTiK+LkqI/+/H\ny3Bxx3dkmXAx1x+DogsuP8ipd+GCI1hEz4u1UTzWiYtzSRcKoK4Ti2UHCmXX\nQg0l/u20uGzAeopG/zlzVRDKMd85sEv3leZU4D1dW6rzCG0AWKrsgmJx+8IC\npiUXqjfdmBRLY7gYeaLVB5Mb42tTlEw6oEP1/TlLMqecoXFAixjYp4tC1rqB\nQExIhsaP1AC7skHBbQjzfEsXj/gYzM82b5FmF8gNTFVilwq95KObSKW6wW43\nWFAjfnMoh/7zwO1kSe3YF7c1k2hJHFb4tdGSZCQpTpDPyhnkvXTQtrXo0o64\noAgSIdXhN0vj6pns02u8LHgBGshtah/wngAVPBBHH2dFUftrMlvyyV7LBMBD\nkhePb2oZHjck51wvkrFHC2EaV7g/IlSF8CSlu/chhc5qWbX/o41vqecxpUNW\ndTSx\r\n=PgIA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.10", "@webassemblyjs/wasm-gen": "1.5.10", "@webassemblyjs/wasm-opt": "1.5.10", "@webassemblyjs/wasm-parser": "1.5.10", "@webassemblyjs/wast-printer": "1.5.10", "@webassemblyjs/helper-buffer": "1.5.10", "@webassemblyjs/helper-wasm-section": "1.5.10", "@webassemblyjs/helper-wasm-bytecode": "1.5.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.10"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.10_1527859328656_0.09459026292450545", "host": "s3://npm-registry-packages"}}, "1.5.11": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d4cc55ca51afd2f075cce11bf4bb3f748d623919", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.11.tgz", "fileCount": 4, "integrity": "sha512-iTr6unwqi3S8cvimVBkS3ifyaciT3ARJxUmLX4Sq1c+490yEVxagTxS9wndSGE6sBlOdczWUcVNl1MUce2YjAw==", "signatures": [{"sig": "MEUCIHMRGq5ENMRCAu0W+7pn7L0dK1Ak5E/J40SLdQpKsQVsAiEAuggpDTWErf89bBTIYWx5pHCgpptOmmSNXh1w/CyCJ4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6JvCRA9TVsSAnZWagAA82gQAI0C4tvj19p47aT/S16t\n8TUQBciQgh3BxO18jqiP/DujLuDzK/b80/hN3DAHn76J4TifWqtn/RYjj04K\nbnjQEXA0QCgCgbfQooQ/WLamNuZd+Y3PRGQNXc6juEPenAs6TcKFcqjgFGBP\nA3M9Xk2mxJWMVC5etxqGGzNyzVG5ppeHrqs6WuZrNnSAz8+66AAWVW0xWITH\nYhcj1BuFJeQT3Bb5uDPlSkAyKMw0crDDHIJp0xcGXj+nTdGlXKFqpKoiaY5u\nfowWBWhpS/W8VSjDnpOMA47IaVQXqS1I016sY6cG6Os7ut47NKCyiqBVWNe5\njT4C1v4uDM3hyi1DcxnPg0w7AbR6Gb3wr4krcQAHDa+Y9KnMB5o4fDDiZmjD\n3/BS26UWpDvXqhHEi5/CdswpaRQRrFyLqn5bPevWYhQtTwvlruZpl1UCui7N\nNq5SRlZ3/EDCiGdO/rgoy3rIVhAQZZRysG8Lez24LfyZj8lJfg5eTxMX2pE0\nXnW1fTL7FO6CtrGQB+I3I7phqLJB+IMTRHp4fGBbazsUaH84+EA0t3PFy/gL\nxYI2o0YqnKcAozTzPIbZntv4Y0ujiJFqpNI4JxcxiTxDl0fz050IGPbaVPjb\nLiTD4Pygpt2KWh5OgDdZ5JyqucyEsXwd/3i89GpzKt5esLUBsFFAqQ3rzfYQ\nNbyI\r\n=Zwlt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.11", "@webassemblyjs/wasm-gen": "1.5.11", "@webassemblyjs/wasm-opt": "1.5.11", "@webassemblyjs/wasm-parser": "1.5.11", "@webassemblyjs/wast-printer": "1.5.11", "@webassemblyjs/helper-buffer": "1.5.11", "@webassemblyjs/helper-wasm-section": "1.5.11", "@webassemblyjs/helper-wasm-bytecode": "1.5.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.11"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.11_1528275566957_0.030698006606556305", "host": "s3://npm-registry-packages"}}, "1.5.12": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.12", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "821c9358e644a166f2c910e5af1b46ce795a17aa", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.12.tgz", "fileCount": 4, "integrity": "sha512-r/oZAyC4EZl0ToOYJgvj+b0X6gVEKQMLT34pNNbtvWBehQOnaSXvVUA5FIYlH8ubWjFNAFqYaVGgQTjR1yuJdQ==", "signatures": [{"sig": "MEQCIFOo4lutT7zw4Fb3p4BfTqhmzlogf0P6K0VSC3quzUpzAiBgVGbszSm3O4m2z2ExD5x8jwA1OXccPa+jLFCsNvs1ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPpDCRA9TVsSAnZWagAA6CwQAI0sjf39Pskkf+Z7TqKk\nZeCfkO1CfTxkS7MYX4121HFGnMyJuXAtXLwqA7xmeET+EQ1IpM4CMgdJVatE\ni/vyznOEOC4JFAEKcrNxcoqJUnE2J9Q+gjxYkHNowD38ys4MenLf3E5r9iIM\nO6AA6Ax32ZVKm3UM55cfBPNYS6/rPUmmYkHFk4qZTGTy/EIbyyxoFAV22ndY\nN4Jgt3BhK73Y4upV8bKRvhqGR21LE04xiX5D9C2l769SJO9Q7ttknUIuRccI\ng1fL+KNpBUiOVRT8o8T4SvRQfaPLIt2tP3kpzKMLAhHjjhwqgaD1Isk4w+7l\nUJ4j/E+l7dkpi/vBo+gtf9l/IB7cNS3cXd+axoDxmq/mDEj1rzk5OGhQ1pR1\n5H/pLnQsHoAUpFbaeeIdoEfbRxUEXkGPyE9t07mvypKL1hvWC3uD1i5cbbnE\nt6/zmyE0PLZmIrn2fwmrLphrNyqm5UffAWAbmB3Zzlx3SCtCv5kIE1mUE7Ed\nfTxi5H5/mIomlheCFU6k4b09PghEtrmCNnj94ptI3vOxLu9S9Hy7xkrBLBNQ\nV4m+5xaa0BPFo/+wU2/YFyjooZ2YwihNcry3LsSESTv7AjutcMGeIIrkODli\nMf1mMXbFuBeKsqdo1Xb0KoYywPAiAhwgwEokIOFBd2zgGFCTZyAPzEA6lr1X\nx6Qw\r\n=58Gq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.12", "@webassemblyjs/wasm-gen": "1.5.12", "@webassemblyjs/wasm-opt": "1.5.12", "@webassemblyjs/wasm-parser": "1.5.12", "@webassemblyjs/wast-printer": "1.5.12", "@webassemblyjs/helper-buffer": "1.5.12", "@webassemblyjs/helper-wasm-section": "1.5.12", "@webassemblyjs/helper-wasm-bytecode": "1.5.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.12"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.12_1528363586604_0.5158935519003616", "host": "s3://npm-registry-packages"}}, "1.5.13": {"name": "@webassemblyjs/wasm-edit", "version": "1.5.13", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.5.13", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c9cef5664c245cf11b3b3a73110c9155831724a8", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.5.13.tgz", "fileCount": 4, "integrity": "sha512-X7ZNW4+Hga4f2NmqENnHke2V/mGYK/xnybJSIXImt1ulxbCOEs/A+ZK/Km2jgihjyVxp/0z0hwIcxC6PrkWtgw==", "signatures": [{"sig": "MEYCIQCHLte+0l7bcY3/b9shd9EAn7eU9cBY8Oh13XMnD3BmRQIhAOySqcKOL5nXx1eGyPzoVtyMWjzJJS2wE1LdiMFECI8y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4l/CRA9TVsSAnZWagAA9BgP/jMIkmniMFeV9CT/NmwQ\n6gmRcte6Cc1ew0x1xJn5mfllqppI4CXE9VJlwIAxesNa0S4QV4ItEBUbmC0q\nXVAnzcKiMgLr7YsGWiNeSHBmkacytQ4q5mMzfJboyKdTpWetWasRNrClcaxf\nNxZ/WM15uQ1R0UQHqJoWPaqTE94+CRVkmsMbeM0M5kBa+ypeRUEBYFg8jVLr\nbUe5CatQawFp49sWhtPev9mqYS+n+O01lQtQtNAUAylXFXEU0cv0CdCNRPK2\nWJhwvJztswKn/emLxi4bcy1ub6N/NTQb6JHgvo824gulzYPZ2m6PzddhL2Nu\nKz3WIiaLrkFyUm1xIo10B8AEiFNMRedbi+7Z9yh6iqFYzfK2VAwfCBgtnrHH\nut0UOPKEWZakP9taSZlA7iR/J/hFzMg/1LD/r+PZPwMgsqOrCHiOy9DuehVn\nQvNwghnq0gelKhR4HWPX9Jqbp3LVEaqXArUS8aabt6u5kGIGY7uD3P0X66+7\nIKBXfTA7DYXJXkpxn7FOK1WAvP2MnZWlixRpxDWQNmTXSanmPq+jhjj3PWhh\nOGXuBIskIYBwo2aeG2BpC5d84TipLIchPTmKhcsMkq5+0AqttIXHxptP5mE+\n7rXixxm9kqmBBACxkzctBdqOS8ryF1GPUXPktXVdDFqNOWuTBwNaa+F83iXw\n8IR1\r\n=q8CU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.13", "@webassemblyjs/wasm-gen": "1.5.13", "@webassemblyjs/wasm-opt": "1.5.13", "@webassemblyjs/wasm-parser": "1.5.13", "@webassemblyjs/wast-printer": "1.5.13", "@webassemblyjs/helper-buffer": "1.5.13", "@webassemblyjs/helper-wasm-section": "1.5.13", "@webassemblyjs/helper-wasm-bytecode": "1.5.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.13"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.5.13_1530366335353_0.07282610828168012", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.6.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "35f56abf662b569d98e2549223842409c18efe0e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.6.0.tgz", "fileCount": 4, "integrity": "sha512-XnpN3wKq2XlhO88rZRTK5mCNHEbPP4kMVAtt6H3PP/YjIzQiNJwA+eyMqztlOV2YLRcsSPNit/a3f/AMX+YXsg==", "signatures": [{"sig": "MEUCIA4neAYIXlRmuFvV1mGlEmtLWVhqOFm1ci1IcO/csXIfAiEAyK5dEZ/VCn3oaLdi8KhOhDIkhlJSFYNi+GOKoVeiD/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTGAOCRA9TVsSAnZWagAAmjcQAIw4gW4hfm4F/c40ENZg\nj/rdy9Zp/ULC0tjUXiroV4ekxCZVQ49A32G/D27FvV2Frkm7La5Y+yLFxyBQ\nrrT+2ZsnnRoHQiCmtzkSK6eP2AofDypPuzlspX1U6+vJheCpLee5AeY+cRbG\n9DJmAxcnjUyhbnrSSA5Ps/AC4fq90+C/PDkcYFQpACGXrE708vLO3MQFocnG\noSn5Zo3dtIcEBR+AlD64JOIFruwrPbQvtz6IkvmMNtF6v/BQCAMK+uLVHxRu\nWTTu9CS1wVKap5hq9zxspOgpbNcvXjpL6Bl6SNf7XOW0UJt0/iuXILqRVoin\nrXOs1AkuhLZ/raoYoKBpuYn570eWg1NuuuzIw/IRZn9pvuCrMmUeUlRTbc41\nM4dJKhrVRBTWqLgKg6ShiOq74Fcx1s8ZE4gkjeFUptdZNCETiZFsVoGctKxp\nQ8i6ZS4kkU0Y8K4EoXrK9RJGws0BoYxhyWNJ06DHKzZ7KMmp8H/kXcTBjYkX\nU3TTj/+j94zScx1Osqd0cu1PmGF/F4XeFtoUqzLkKKNkeMiSXVRM9Z6E/P0n\n8WkKWeOPF4xZdpFN066wYveyHZY3gj8fJT/15TTdGsq93KmDoteHC/jVigdg\nYdLYhBi1GAiWiTo3PDdDqt9OtzyHSHYRuOM1RYTCeYz2gYh0FBQ31kL0uqB5\nmxyj\r\n=9zXn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.6.0", "@webassemblyjs/wasm-gen": "1.6.0", "@webassemblyjs/wasm-opt": "1.6.0", "@webassemblyjs/wasm-parser": "1.6.0", "@webassemblyjs/wast-printer": "1.6.0", "@webassemblyjs/helper-buffer": "1.6.0", "@webassemblyjs/helper-wasm-section": "1.6.0", "@webassemblyjs/helper-wasm-bytecode": "1.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.6.0_1531731982892_0.8000221947996637", "host": "s3://npm-registry-packages"}}, "1.7.0-0": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.0-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.0-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "15f86af85f0f8e7263e9058032a5ffe721d3a02e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.0-0.tgz", "fileCount": 6, "integrity": "sha512-PsXXB5x7LWVuD9s2e4VhDTV+KWWaVSdGjPd9G4MX/EIwv7kCEqAVOEB5dgoeIaoMYZy+x9jO2MsE8YWR25JMTg==", "signatures": [{"sig": "MEQCIFBslQI7kzS/8nXQbI9wimK2ydNERvPL7uN9CbNLxxGuAiBpbQIXn/WYagOzAXDC9ZFvvP1lrDF3lDnPASHT3Da/dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzmsCRA9TVsSAnZWagAAAjcP/j9sAuFDlRFCij/IMoJz\nC2jcDhYvrlpvOsXVE/tvlR02O31Bp80x3+iQZFFDN0z9zLZo831raBPrOdcm\nZ/QdCORycXuzG145g+UHQG0/wqZ/3OrF5m2qDRiwbGhaiD4oCD+y5SohK+Ud\nrLGqWwIbQd+OYSp8XR/pFwhnsF1yIYvlJ7rGpfMSXpxqiAubhysrTARso8qT\nx4VSH05gzbeePYf569DKX4oTx5g2972ldNBM22Xebkkhtf6VieGhdHUzV9Ot\niCw1nXlpxlr/iPqDm2Lm0vha3MS2y0fbYtA0nETNNhT9Us2AV/m0/9Md9a0j\nUwrKWypnDpJrDGjgLJYwYvWUH3nmp3tvSVZ+GBalJE35uGyiEQz+4yeYbrAf\nI3Yfu/UBsm7291yRPGnxy9PzFm5ObRcZwRWSXekNjq2A3s76TUBgZmiUmTG+\nWwc4BADVjtIzolKwmDh1cZ3U4X9yXAuuIwTLJeXEk9ExVmVcMUbTI8SpJo1n\nikY5Hix3R9iW7CynZ5DD3MoOHT5CMqmh/MQfha+U1KwBeWHoCIW5JQujYS+j\njqbgOWvps/32Yii6WY8f/tOU2c4OdFTJv0aqoQgJoZVKl7O5h9KrzonJ2ipB\nm7sGDmuZEUV6SK76/qKEQz/Sfg5k9ifRfzV1uETV2WnpBYrsyZwsUwaAvkAA\n5nHc\r\n=T6dA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.7.0-0", "@webassemblyjs/wasm-gen": "1.7.0-0", "@webassemblyjs/wasm-opt": "1.7.0-0", "@webassemblyjs/wasm-parser": "1.7.0-0", "@webassemblyjs/wast-printer": "1.7.0-0", "@webassemblyjs/helper-buffer": "1.7.0-0", "@webassemblyjs/helper-wasm-section": "1.7.0-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.0-0_1531918764669_0.7242030952247691", "host": "s3://npm-registry-packages"}}, "1.7.1-0": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.1-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.1-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fa52ee8def7d94cb6dee46d99717887723deb08a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.1-0.tgz", "fileCount": 6, "integrity": "sha512-EkIVyVSa21pbGsQXiPgyL2Gn/XYE2FasJEVtAQnObMVAQGJ8D8MH3B0E2KSpOW+DmIM9tgxKWrlJg1eyMXfLBQ==", "signatures": [{"sig": "MEUCIQCwzUit40tKxyUY1XhPCUNfRhp/ItTuiJctKSNWSw7HSAIgA0XT+oD17VltL7A0r7DL/4TY806jfFbtxgCAX0ZzZ5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzxNCRA9TVsSAnZWagAA0zQQAJ8sExDlaB1dyg9XDNKf\ncY3GNGMNyRcm2S4lHOxLVSO2VbyKQiQf6ZxOl1T3WufB/2KYypcBNU8yqjJt\nqzQKli3AsXT8BbkeVVyO6i1NZKwLTK0dgacVFFhupoNb9qOe1Ai3DJP77wZ7\n9xIRYlTJySn3UcZcOLfUTT7r2Db5b/YEvGVjO94H98dHBp+BTFAfGNk1M9OX\n1vOStlLgg0NEVuLS58focgy6Vp9PR7xDg3dHtLWZMb8BkSHVmVpfvmN8Y2YR\n8MnS5Ks8nJSaWwPrF4IoLMvMKGZdI0a/I/r2qOLddA7AesPLDeie5/vyrhjo\ncCt/Tjvc1RucJjsAoUljCaq/waPiHIBdAr3IwYY+ky56GEMW0r2WS19WZ/vl\ntGXzGj/f3JY+RWTFH4O0O2kFUhC8yTeCWsyoI1zMDNeL9wr84174WTOEg110\nHMmKRnwQgXJEmrf07Wo0iDDqyiSFrEbUzpSlCvofIdRi2Wp/z1bbwpki2BPB\nw5wR9yEtwh9n7W09yp4YC/se3lNCMDX64jtd59tq65Srr6mD/7dnMQobSNYF\nQObvXUbj6NnJSAFL9VKXenehbFTQLNmdXBwQscejJNXPLjZklX73b9xZfAi9\nksHSecnNsHrXprIw4vPWoFUGuB/+oStK7g2bfRyWJRuABEQPs08GObDJ8ZpB\nLJVD\r\n=+ODa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.7.1-0", "@webassemblyjs/wasm-gen": "1.7.1-0", "@webassemblyjs/wasm-opt": "1.7.1-0", "@webassemblyjs/wasm-parser": "1.7.1-0", "@webassemblyjs/wast-printer": "1.7.1-0", "@webassemblyjs/helper-buffer": "1.7.1-0", "@webassemblyjs/helper-wasm-section": "1.7.1-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.1-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.1-0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.1-0_1531919437443_0.3949685194872783", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.6.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "69e292b0b9a0414d0ea91b0feb03de50a8f62bbd", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.6.1.tgz", "fileCount": 4, "integrity": "sha512-76ejD0ciEXZTnQSxZ1W7z/ONu8/f/brLg+h9ULF1k4M661ljFA39jk6igED3LOZllObf3IU+V8ViICsj744HKw==", "signatures": [{"sig": "MEQCIExEbMt/xsUM70k7gGxT/6kBZXzuSFXxb8yFjjfZ9uauAiArj4hd0yhhvSGqLWtepP/o3STevMqw7Bh397ngLceZmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0X2CRA9TVsSAnZWagAAszcP+QEuWboeMlDRsOnN31RA\nNZ7kp5ztjKpi+yfjo1FNc7XcXM1YkEeMRMDth4qXGy/rm8WIKlt7myA1u+AR\nEWpXMTfa+jajcEHfdAWWI2rADsPgGwrm48DVzM4bVDhe97rffBS1/pN+wSG4\nyG2HTqXjq002JtgLcTH3ppN+QoHdocYfN1o8+hoSV9Fm0giSRn9oAPbENbWu\njNA12SQRGgAhGRe87c/Xrfo2vH6Soojqa5hQvSzEoFmhFWKbzLDwrDl8o+a3\niGixJf6u/+R2X2sAT4VE8A3O2gUGa5qQOtNis/oUM1J14hi9ogguWjkq2iRj\nZbZFbjTb7QSsxxGNTl5X8afnJV2H01JjCCcw+rghf/HTLFF+Th2rizWPRLBh\npyEF6lqQUpvw33BzIDYnnSGV8P2NBr+rZb29kkrvnyyeL3GarLpd+WSGVxMu\n6EsEV3QF/Gmfm1OpTsajBQ+zCAsoMA+mHNoOHJc4h71xTXcU63yxqVwZfEsN\n+zhKaflJeI+OWUe1jv9q2hImt3KrtwSIJsjbPwirS94nzCpxd8U4QzGZ3MQo\no+AzRnrTBpDOotxz+lJE3nVEWk10HUijwySh8nQNqucV8R+aSr1xiIpQV3qM\nOiUqtcDl7Qi+jrLePualLqoBD1UKyqrnks395elijfuQsrpgrqqdVqsZMseH\nRlTq\r\n=sPYc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.6.1", "@webassemblyjs/wasm-gen": "1.6.1", "@webassemblyjs/wasm-opt": "1.6.1", "@webassemblyjs/wasm-parser": "1.6.1", "@webassemblyjs/wast-printer": "1.6.1", "@webassemblyjs/helper-buffer": "1.6.1", "@webassemblyjs/helper-wasm-section": "1.6.1", "@webassemblyjs/helper-wasm-bytecode": "1.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.6.1_1531921910788_0.06856913483473526", "host": "s3://npm-registry-packages"}}, "1.7.0-1": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.0-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.0-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fcda9697560bb1e0a078f03fb9d2515e9240ad41", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.0-1.tgz", "fileCount": 6, "integrity": "sha512-pQIqruPSEk5L84Rp7hixZGoCjPLSMtz14vZkv6vS+JtmxNqKMKpc860AqSOQXGgmMwaotuLm2iLkzc0EH8wjLw==", "signatures": [{"sig": "MEQCIGP6PKty2SZzxbQa/OTOHo5mh5SFRleH2/yFSF/s1hFYAiBvurEJ2zexdao//Ro6dwFh/+ucwoVtQrSascCDoTEMzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1ZlCRA9TVsSAnZWagAAm8AP/3R8eoW+k7a2RWWPTBDz\nT94EnOHR+F+W17ZKYp3E8bGhBz1Z0WyaFacTdV+9OY+Y6buL1Zl1wfsZoMDq\nhiXLFXVzF+N7uyUT9hb4YYADqge58hTZCtqbwcFyp2tAodWJTwRf/gXBFebf\nH3gS4JPqIw9pGK2DtSvp6lwmAh+PCk+mTbiSkSiZqa/gACIRMxTERK2yxt1l\ndGObQg9CzaLnBQ9YtY4wsgAZcdmwFiJjYsswcIgcCjIH81W1zCaIb7Ql1JAO\nnpi9IjBLmtOIe5EoOh9XVomLSYdC40F0qhHIWXnR9EaNWCn3COXgE59XOOeh\ngqC4oBKTUrl0ZAsT0EROkJNDRD9IvDLY9elAL+d+fZT0CfU4Hkq7vg8I4Jyr\nl4kPcRDeecvY/VPApZRHs2Mu8Lu2ucBv8VcvOaHpo5Kj1T1wpvQ9DAJfsp41\nFWFGYNiLDBEpOs26syY0cB4N9kP8QrYXx9hQCfGflOjgBlHnzaIGp8mtSKsY\njPURK2jGuC27VEOJ4/k25e24TxNNrZPFFOIhk2NgfPy7Wo95zkBPG7za8jgW\nPRLTkF/hJ7S2bNo47lmgE6fYioLXjeJSpgKTLccvTW9TLQz4ZMh4DW3fkvHo\nK+rNQzfmGzTVROaaye7nbJ49c/mlPQSuGFNOkSGnmiXlS8FYD4ccdEIWDBEZ\n1YhV\r\n=uHnD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-1", "@webassemblyjs/wasm-gen": "1.7.0-1", "@webassemblyjs/wasm-opt": "1.7.0-1", "@webassemblyjs/wasm-parser": "1.7.0-1", "@webassemblyjs/wast-printer": "1.7.0-1", "@webassemblyjs/helper-buffer": "1.7.0-1", "@webassemblyjs/helper-wasm-section": "1.7.0-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.0-1_1531926117428_0.004195471001632756", "host": "s3://npm-registry-packages"}}, "1.7.0-2": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.0-2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.0-2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "4c803dee2a1270c3b8f8534575481db7be0dc6ab", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.0-2.tgz", "fileCount": 6, "integrity": "sha512-/uFxUsg+Mu4kZDRbcwbgSl22MkSyRZKqPHVa7sv+dQRYF0JffvJKjjD8hdeJx4bPkxSYglCvtjsZl+qyXW90Nw==", "signatures": [{"sig": "MEQCIDY9snonULxVdTr14XPWPleCcSJNP+8q1x4t3hk8T3wEAiAjmAoKAU+7Lj7De8XdUKylF1/aFga/BnXqRaqqSNjo5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1nyCRA9TVsSAnZWagAAisEQAIdjbD7+IJ3Rktqr0XWK\nOWNt6yDnpey7aRR8WuQ7rosKhC/amDiaYeKASOzN3fYpZkw8PTTzCKGd2lkP\n5JIbYoXz06jYiehbp37yFD77nrsu7BEip4j4JX/o8xY19aoiFEiR+5+WqJcF\nT1Bn4lYp8ZZhCG2aUI4xS/Qer4JZCs+Peld2r1nkURLzJ7BhvWWH0phQ5j89\nipPWYxMtdPjF2QGWbncDML4gNn6C8wXnZ19U8PySXlLPSYzsNXN1+Q2XlsR8\nClDDrxnn3oflRpsW9q2yTEouglNszfDrRHrPp9vZ9cWOeP5SxOZYOdbxbOG3\npNMNpPAicnjD818qbhBDM5A6cuUKrKW5gVQZNvKUgxkk0GVrY/CD/XOoVXE3\n/x28VY5h46rlimLObOT18UgDimhNYjsKUJikHo7FruiMnT74gQnNdO1taX7g\nt+Eklh+ffqAlBsn2cVZAaEQh3hUhz786wOiPS8PHzTXLdawlzQixH3TxfpzX\n+LMw6lgJWBoYC5pTCEa8l5zLwNA4OykrL+qELnUtctQkJfR4u+KOQPHGxq8u\n0pBVnK95kd9smOT0nZxO3fommDGCluogSh+keInZHllveiIuPIGGoeTafAR0\nLsdac+GB5db9bT/7E3KSQZ0Ks6GfGuuJkkQrNlImAcEdMyVxYF0M3yc5V47N\nc4MX\r\n=oNHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-2", "@webassemblyjs/wasm-gen": "1.7.0-2", "@webassemblyjs/wasm-opt": "1.7.0-2", "@webassemblyjs/wasm-parser": "1.7.0-2", "@webassemblyjs/wast-printer": "1.7.0-2", "@webassemblyjs/helper-buffer": "1.7.0-2", "@webassemblyjs/helper-wasm-section": "1.7.0-2", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.0-2_1531927026419_0.17613496247477767", "host": "s3://npm-registry-packages"}}, "1.7.0-3": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.0-3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.0-3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "82f5a902337a25c010deb4dddceb7db12885402a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.0-3.tgz", "fileCount": 6, "integrity": "sha512-AmxEI4OnXvNDY4lyUb5ytbizyPE5j0njnawkgQKaLlOUQK2Q3r4M/Ap+9zT7iJbPtdpsx+c+HGB1+cXnhoxkAA==", "signatures": [{"sig": "MEUCIQCsIdikzi2sjsO5kJCAZPFg45qDi/8E7/Im2npCMBhYSAIgBgH0skiK3s95/JHQPFaYWjp426Y7fTgIe0GMAhzX5wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5GWCRA9TVsSAnZWagAAE70P/0q4RCJn2W4nDAO81zP/\nVh/Uap9EglRNjdLq/lJ4a4YPi2sDfKJQAoDPowhIK+iPqEMip6RqmrjSTcWt\nD/l2/dSg3vDJBm/BmulEW37+csDoGL0OCjdGFCK/lBwk+TOEPh45Cqg7vgwM\nd6NOc2BS2kRzgWde+LYESWursJC1ofDMQ6iIrKyPU5YO3LBJa0mH22AciyDr\nj9XjR788cma0aCa6JNCgipn6iPj8sSKlO2GmLwI4alcKSsuD/Thd/ZNxd2aG\n8S+gC1s4vPQlWXtYiq/ZK5ooNMUwRAbw1lc+sw7WGkd4JEx2ANbYO3XcS83q\nPaAEyAYaXuK7yihA7bq/sUWa0bkUkNfednxSrZi0G7qu5P2eJtcMXRhu9esY\n6uVw7c/nv/6+ds0QFyJ1bb9TRz0+xbFhknWeE5qoH5H27u48haleHnGUnuS7\n6DizvGcUq+Uklr+y8KTR5fxhPbpq61ckNU1QAg2ILIsj9YC7fu6sxFg8OHyd\niK0SYYL9691lQmOXj3HOZHFs3IxsaYdNETET5IaCzImL9HT9J+wNVWWHn5Cs\nL9LfOjPEA51MNWRhftoggyNz+h1vLvfVXrot5Tf1KfyACeXClHDuygm6n/Tp\nVkSMaFBQIxZbO21GAXyhCW97cX7v5iPtPaDveefoANYdnCm2+dTh047hbr8R\n9bkp\r\n=ZeHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-3", "@webassemblyjs/wasm-gen": "1.7.0-3", "@webassemblyjs/wasm-opt": "1.7.0-3", "@webassemblyjs/wasm-parser": "1.7.0-3", "@webassemblyjs/wast-printer": "1.7.0-3", "@webassemblyjs/helper-buffer": "1.7.0-3", "@webassemblyjs/helper-wasm-section": "1.7.0-3", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.0-3_1531941270379_0.7836884636234631", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "5b8a7913f7f5db58516116cc642aa06a3144de8c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.0.tgz", "fileCount": 6, "integrity": "sha512-Tx3QagCeDv4Xx6MQ9e5CmhCs7iZF1v7tFKewZXI5jk1gDGyD+o0RNXlmnL8fzfB3zgFawF/gNl++Ha5XQEKObA==", "signatures": [{"sig": "MEUCIQDF0/n7WDiX4Oc5qhxp7QWjK3BNbldt+D42JWUuikwMRQIgVJ0fcJBCajA3k8JrwBr/elm9xtzQR0MeBm5F5zvAm8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULYGCRA9TVsSAnZWagAA8KMP/i9KdxBywXQoXtW9WytX\n0hNDV8t3tC5rcCY9VTCirmWrvjnX/KmsdNRHVetahCRbiWN2bcoaugYWH4wb\nzicClqTWxdwAf9pA1sMqDrLF4RxwT9KzO426sm9NgyO32GI9T0nafpTUNBxS\nHmFARjrFjl2pim61SzzRZJDN3Shg0JFK33HpehDQ6/r87uP3O5nqJSnbea2W\nvcsyxFd7I5jnpD1JW2dR/wFtWAP9FbMKFi4ZDzhl26oySfo4XF+olF8BJ8Rz\n0eOTpL+PaKKqDcmxJDFOypOiejWGmpj/mKbOunvecHOWJQWeQvNS2k3ErJvH\nwISRmfH01OWdPvGIXyDxUZQnhZysjYRoKQRAEMjSixznBlq19XVE0TSscuQz\neKIRnbPjlB+iHpSvEDOi7LxfjfFXCVA2L5ZGIuUaCccs9dt5qBINEwrYr+HL\n98UY3M8f5/IJcIa2BJPO+fW12JtZ4ONrFZF5PonnKlZ4zodZkxLy4l73Y1OP\nJDR00tZv/H5+u9vSA0FGo/v6JBwX11qMrBY/YFeUiUKXMH54WNDJ5z29lrEK\nwuKcdQOwilaEPICsRhsGHH7N2DtZJw+RBZ3GZ69f9fPEgln03aei9Qm7JAY1\nyi/3PYDZFkaqbMn7S/UhXrpxsi1894D4oitwcaNv6I4DLxkHB7ueMsdry5Ig\nixnP\r\n=6/qL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0", "@webassemblyjs/wasm-gen": "1.7.0", "@webassemblyjs/wasm-opt": "1.7.0", "@webassemblyjs/wasm-parser": "1.7.0", "@webassemblyjs/wast-printer": "1.7.0", "@webassemblyjs/helper-buffer": "1.7.0", "@webassemblyjs/helper-wasm-section": "1.7.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.0_1532016134744_0.4227367867498144", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "dcc205d4feee35ed4869565ba5ac356b57c9a1b9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.1.tgz", "fileCount": 6, "integrity": "sha512-/tPkQkmSha8YeC+dsKu2LZuHwPOlt/KykcqYwYXVtjvlhg4McIfRdEbnWQtaNbzZa3aztzDBCLSlkMEf0IogEg==", "signatures": [{"sig": "MEUCIQCQ95Enzpq8nqoSNE0psE2/3ItLMSXqscYF2cSRtQ7CMQIgOZ5p1RBAmN7zau/asGjYq5pniZWYgQNMF9l6jvZB71U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUL02CRA9TVsSAnZWagAAgyIP/jWd31YvtAXdc0K5F2Yk\nujX0MxGZwp1jcqxuqqw6gCD2aTTxRV4vK3oUYCdS8Z5XR7u+P4v7fff07Cii\ngx1cHUkQBrbdw70AuLVQx/IP1jOAWKdvR0kr1b+FVncfS/Woqwj/CAafLLeb\n0tBjmXO6Ve6aNqvbqP5SxcUQFxxeIeudS1XEJyenU6jHx/UUQfjiV8uwE0aM\nPn7AaATH+UOXxIgbT8RiqUDijYcQn3z9c/GDg+p0ydvmQCdFc+WI6Gz1PpkF\nd479xT8NGCpgTi5dhTRAAjMJ0HfR+ySvvadb5ztmwXK9ORG08SCIy//Rx62+\n5dIkHZxIHPr98zFMvZ72aZh25biFhW65v7dHgpmZWtbHiCLs/7fUc9DdBg9L\ngkktV5QBcVDn1qlO2UI+JuyWM7BQNpqcUiLbuv9Wuy56D2fnaLe3JbRCluW9\nL2aIkrE/C3Hq26UsOmuPnS94TLiPc/xqguJGpFmMzaZmkpmYOWUd6BBaokAY\nCduNFnPrPXALwNLpd4oqmqcI0/ty8lId7qnIicQVRXcdZIkg0tIHB5iJSdhl\nY9gyHdkvzluc1g82CXWuHBgMxXnUZf0ftriuNujKLxTxhtjV34Ubjyb8Dd/c\nRhSx5dBgcnai+6kdk2mzDYSbzYInJrUGMatEaJoUs/i5ZRCvKRJ3mpQeimrF\nytj7\r\n=oLBt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.1", "@webassemblyjs/wasm-gen": "1.7.1", "@webassemblyjs/wasm-opt": "1.7.1", "@webassemblyjs/wasm-parser": "1.7.1", "@webassemblyjs/wast-printer": "1.7.1", "@webassemblyjs/helper-buffer": "1.7.1", "@webassemblyjs/helper-wasm-section": "1.7.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.1_1532017974475_0.652007474343766", "host": "s3://npm-registry-packages"}}, "1.7.2-0": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.2-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.2-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a9a94fa1b6b4c3344a81acb5cb5f12af60e8b88c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.2-0.tgz", "fileCount": 6, "integrity": "sha512-vKAvUEXV6LKpXtbpqJNfZXZuBIoGLZ/s4W9xtW63dkDCYvUEgf3ZUfRKVZefMl/0YNhTv1245n1B1wwQpgzr7w==", "signatures": [{"sig": "MEUCIHD7ShpVA4SRfBMX/aahJWIzlQdiHZp7vdCbgxLY2hVSAiEA0I///63vUblzMnyblWvf4O2DnKHwmWQKT+wkTrJX4TI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNQ2CRA9TVsSAnZWagAAbRUP/AiwpvWmSYFTWsJXnOGZ\nREmkTVWyMvUcPlVvQJqkpXQ3hco5+5KxPKoWhY1N6VfF/sZ5Vc/KDOy/xawR\nzK7tGL2wiTtpjac2Hcue+OfoNhuLdVuKN8txVGpqR6DM69YMmuiD49EENoXS\n8Jk1hshQeBI6kdCtiy90WWiUkBCmnPRbjGreUTjT6K9Ejp95Uw9OHcPnQd2F\nh3cAyI/geGYO9cPbHq3YVuPjnZkLxCpM/sbwDwUN+QxjlyCr9KmTkxX5gsqT\nQTC8IQupzB90X7HDujBlVx+zlQH7EP/dsrQdcZgrB4ejhVJWBau3Ws2lfi8N\nPls1MXeaA4ApMMOe+j4iegRe05JEerJ/LCQ40ebXU75GyRoQgAzmn7ewVpce\nV3vAqC7v+UZh+KPbj9YsEaQs6MMsURI7uSg0CblQF5sTO4kGi8+73s+eRV6/\nZXQ4p0ZeZAOyENsBpRLwg5Jhif2I6pP7h+7MH+2Gb2nNB1YSnB3As2pwujp9\ng6b2IhveVmvt711rAJKbOlH9vuyctxatqQLd7sFh7mMutKtyvFiZu6Z9p1c+\nKNZSfmAtWU6LweEhCf8H4THhgWpAR4nPV3hSL1zSdkYqc+nKFNDk4e8o4ycQ\nQuxYSPPsyF0h73uVnsN84+r1Oz0uPJrO/TN82M+y5KU7+h/tr+jkYb/8QXJs\nWQsm\r\n=Ff13\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-0", "@webassemblyjs/wasm-gen": "1.7.2-0", "@webassemblyjs/wasm-opt": "1.7.2-0", "@webassemblyjs/wasm-parser": "1.7.2-0", "@webassemblyjs/wast-printer": "1.7.2-0", "@webassemblyjs/helper-buffer": "1.7.2-0", "@webassemblyjs/helper-wasm-section": "1.7.2-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.2-0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.2-0_1532023862834_0.7224965987123306", "host": "s3://npm-registry-packages"}}, "1.7.2-1": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.2-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.2-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e3aebe7ebea252cd64840877fb8d91fcbb6ab86d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.2-1.tgz", "fileCount": 6, "integrity": "sha512-MLxaLYZt5HcZPEeobd0rwSlstDmfkNo0vXDHFsOwcIlLnGbZE/7OmYB9kt8aoiuTUHDF9Gp+zZM/5g5Zpj/eDg==", "signatures": [{"sig": "MEUCIQCLxfgJAYr69wkPfaBEG4rPzpGsB4j6LNgv3U6M4IW31QIgcWWuMT7biUZ33tmVDXvxo+RG4w2/7jOm5I9orqjo370=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNe2CRA9TVsSAnZWagAAJI8P/0kKKMzPImG0H2JzqQuG\nEUqNevPcT6fZ615igvzT+M1sRoG63G17hRh6KZhjg8Ann/KD3OPFmRLl4KqR\nc6X7mrDHGT8s7RTIr0JOJ7W95rFJr+yNGR3e9LwWNzYwcTtKCPGG+JqM0oV6\nnXU4/JW/z0Z+ssOwnEWJZmxWWxe7D8jU0ju4KEBaECL0hYYAn+xND6ct7/YP\nTlQD6JuIHVUPvo2sfNdK50Ho8nKUEiw96B3TmW0S9zCibho6xAuQUl8a1FrK\nAMxHF4GTXYUtoSJwZEAclyRPciA7a95W7B58FQVgRym1MVyY0Xpho72s+MdE\ncH0vuBN0AEymt1weWv0W3HFc7PUmzT+PX+kUXX1t4OzH+rBzypum3kGu1pFB\nzSVIEW28ghH7s8fyYOzXE3TAgskB/qniEldvewVyGFA7rS+pmYvdrVxkCokJ\noR7UlNkHFM6eVXNUR+ipLRWgsvTUPK5OyqGVek8+tVSnmPX5hthl2XlAVhfy\njCWHDIppF27xM/zK1JYoXSQfrQu4/0yhL8ryQvDZdh2DtGZMsZnHzqFHlilN\nrGnQ2gh9R4oxj89UruoAWvFf53JMss1oSmYZcY3LbkLw6aufvpSHe5SHHxSj\ngt44YsSag/2yFCubZz0l8BDr9wNJnYu0rNa3dcrcQ50d202CBU9cut7MKI/r\njcpW\r\n=cEY4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-1", "@webassemblyjs/wasm-gen": "1.7.2-1", "@webassemblyjs/wasm-opt": "1.7.2-1", "@webassemblyjs/wasm-parser": "1.7.2-1", "@webassemblyjs/wast-printer": "1.7.2-1", "@webassemblyjs/helper-buffer": "1.7.2-1", "@webassemblyjs/helper-wasm-section": "1.7.2-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.2-1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.2-1_1532024757987_0.2966998687403317", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "29806aa2ef5932e1855e3e2329e86662b6748362", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.2.tgz", "fileCount": 6, "integrity": "sha512-IU7tdbX6SwnC1kjOn4fn5sRnri9LnUfi1AnLXNWL8HCObHbfx83JCUhM576HHpNg5MM8uEp+eUVlP3sCMPybDw==", "signatures": [{"sig": "MEYCIQChAuqfzOq7P4FIukWNdQH/9IMvGOnhIjdsb5cd+nXdKwIhAIFFoZqdBObR+Q1+y21Qk5rGVGwHxbdR0xJm3thuXDNC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNnXCRA9TVsSAnZWagAAK8gP/0JlVDnkZop2LAqdB4dU\n5yMNBj2IK/oYUp2gF3ajWHN1inrsAS/knXP+lQBjGrik3m4MxErLfhgoN6cq\nvZWZRUJbysX6KB3LWUWgiQAP6oJjJlIRL2Aa9IYBvaDMjwWRLyPHdLcNl784\nlW7H7KAiItZBSI1VUcn84l4Xmq1F7X8/uAOxL47FvXzcVMJ1sJVmku4Od/9p\nGNiXFyoGdn7ZX6AC24cjn8/XDRmEbH7BV1HIjRgE/x4mcaU3o2hX/PuLfHM7\nsWHcjJ/h4HOnLvLu3you53BUCqdXyncjCIbvl42wzBEYRGKl579agRT0DRR9\nlomeLkFHwdC7jgnd6GzJvmrLFD53ArE+h2eWtcxbW+pgWvvf0Ogo46TCsu34\nWVx0D33rElH0jg/bwd3xH7OO7/UdiVXeHY7Owo1X0ShPY8RK4Kvk8c8Ti1eT\nXVLP+koa+8JqqszMq9oNPDsBtXautPb88Ly4N6A/myf3oHxuCEa63pjG4z+H\non9x4QqQVQEQ5uI7aKyNQzN2CN72ibeHsclNGqlhgKnsIy8TstQDUcsIOL/7\nBavAc4o+gIETcUNk6mmlOjxTwRUxZmgUNYygwk5C/lyMuTZF4LZWXBFBboaN\n32E0IgPU/OT7koNxoSo8YioefB9DXFUzv0kPfcDiaj+vBAnBj42D28i45ur8\nO0KL\r\n=qkpy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2", "@webassemblyjs/wasm-gen": "1.7.2", "@webassemblyjs/wasm-opt": "1.7.2", "@webassemblyjs/wasm-parser": "1.7.2", "@webassemblyjs/wast-printer": "1.7.2", "@webassemblyjs/helper-buffer": "1.7.2", "@webassemblyjs/helper-wasm-section": "1.7.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.2_1532025303491_0.701164957744755", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c74beead01c2001aff0d4d5204d55c5839a6f432", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.3.tgz", "fileCount": 6, "integrity": "sha512-MMK09isNEZwPmCzJ7bpMhOuZ0CNszBKkZX1T28cMrq8LUKAkDH6wy04FSHlDs4kyYmRY7zIdFYg2Oq9svukLvw==", "signatures": [{"sig": "MEQCIAh6V8XBvlup8hDMVK2Exfr9yYNk4XlH9zzl+yFOI+Q3AiAPkJAztBLMWQVIUUtfoiS3Ntm6s7FS5bDaU0/a/SBigQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX3FCRA9TVsSAnZWagAAKmYP/0D6ZhaP0LP9ulWXAPVQ\nmRiWOb7IJ5GwHmWt9BMPXLagWacdFDHIpktcCOPMJCtEb6aCYe8iO0BvViT4\nfqKHHJVpu+Yq5AUfDPG/zBgrwqK2U5z3jLdQHeDumOb5v2J7d93C0s1vdDiN\nXuGehLR4pCp6WrClgZA4VmIKExiR7Ggp/dvCwhX1sVCpnJOXJiH/0rRkr/Lq\nMZRFGpMX5BMmq+WkoXc+oD2UBk4yUON9wUJOg4NGPlKDOCT7ANBuFRCHDBVU\n1Nb7K5CJsimzNgrAM2imUt/YSTfXv8LUEAVcAtMTnYVe8yz8PoY/D2Z1G9kS\nyWz+yp3nnyughiX1wpzbDoRlY7mHEqRhQB701iZTjF93b/5suWmLmwB6Cvcf\n2KMFWVqjTHW9+rBlDK8pIr/KjiUUz+dg0t1SJScMfHFuib6RxJHVeNGhsydT\ngRPQ1zHns0hPQR3QhdAbodywFM4LQpXOQYOu3083HCdA5BhQMSl0CngKdWri\nWyz1gQyhSZ/YlhEfYlWAcdbQAEFeZ5u3g+8Uphox+sbs2xvDMPcUnJQTZMOs\n36EHb4yXxCizz7QlJO+souZDBqjYbivEWdZWF/Pn3EH3IgSnNu6/nzDzottG\nrBauejw3bd9w59GO/FXbv+oC5ZlBNEjJNfkoLZ6/C+RGmacwleHWoqY4rbAA\n6eoG\r\n=3baW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.3", "@webassemblyjs/wasm-gen": "1.7.3", "@webassemblyjs/wasm-opt": "1.7.3", "@webassemblyjs/wasm-parser": "1.7.3", "@webassemblyjs/wast-printer": "1.7.3", "@webassemblyjs/helper-buffer": "1.7.3", "@webassemblyjs/helper-wasm-section": "1.7.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.3_1532329413652_0.8426797921211093", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "09a36001dbd5471c1adfb63000f477d05536f1e7", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.4.tgz", "fileCount": 6, "integrity": "sha512-VkOhQZ/UwkYLlHyqv3K/HAmKZTr002LXw1sPKLXD0p6bYW1Fsindz59oy5DO+OpgRSdqyAYo24xMybtlMtqLfw==", "signatures": [{"sig": "MEYCIQCARQL/KyTS8m6eOgPKty5g7RcUT/A0hAdFGIb/AzhkAwIhAPizM/vQEniLmh5oxne7YsqzzyMWhjtdLQNeNn2QxUey", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs86CRA9TVsSAnZWagAAwuMP/104PaoZN2Ckf1jiYkeU\nyEO1rM7g6TFp97XELxZbcq3soW3VRmCLcG91tFCfKvc5nKz+iPOnvr/1XN7F\nqaZZazUSlFUgNAQ/9+/2rGwT6pYsmP5ZgKeTgY/gl79Dzah5tj2jVJ3IM4Xl\nw7DF72bzElcVdA0gMJJB9Pi1q7YEZgYDZACuMN3KwV4FU9pZ1Jp1Hw+jlEHc\nosJm6EupPUJwzPPyawUD3YZtuTkF7n8h7RZSBHwM25D5POxE+eZACNMOiQkn\n/N93gxyI/PbW2UbZjzDgFyQx8bBZOO8MjF4iyOZr/3TnzCKRawou+wgL9tds\nh4Ln5HY0jFGzulOePuUAE8s5L3o0lIR33Gi/J+N+qNHGTRPwx5yQt/iBRYvs\nt6kOe2QdJZYAgSXZ3OuA5ly1WO66vuwWcjRog+JcPb5L+RobIdDmu3mki2vg\nnmiCOs1i6yckTu+3qdPZrkxTn0VEgFZq+NN+dMP3nDIadB2aTIpmT9ZlUT+N\n5n6oABKSDlb3j1MKGRbOrY1ROm75YjUDtfEnE7pEW+lrvHoV0jaXOJW+L/RG\nNydTl+Hua2vp9nnrnc//AYFe97+1lrUFoH1seE0yohN7C2JtLTPZ99M6gL0N\nFTtJ1VMtrTz0G7xTzSptJ1UeIoWLwiidPdKN3QWfHU3R506EP/hrys7/qPAc\nq3YJ\r\n=ZxTS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.4", "@webassemblyjs/wasm-gen": "1.7.4", "@webassemblyjs/wasm-opt": "1.7.4", "@webassemblyjs/wasm-parser": "1.7.4", "@webassemblyjs/wast-printer": "1.7.4", "@webassemblyjs/helper-buffer": "1.7.4", "@webassemblyjs/helper-wasm-section": "1.7.4", "@webassemblyjs/helper-wasm-bytecode": "1.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.4_1532415802472_0.49361925656267847", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b2e7d47d13966d0bf3f93a568b47cdab0aa20b02", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.5.tgz", "fileCount": 6, "integrity": "sha512-3tg+yFIpw17rkfyA/Nta4VvHaRgrI/HNmDM16H5J3sib3mQJFvcijIzX5AQmrgtxnBBInBBnhu8YlwgH2GBOxw==", "signatures": [{"sig": "MEUCIBSXA/4siEOxwB2Lf+pUN9KZC0H7PaaBeH2CZIsHOgp7AiEAqgnnlK3/VbSM2vx3eDMnZ+BwhpfCZYF0DapaGjUZaoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZBTCRA9TVsSAnZWagAAlrEP+wS8x27T4/zAM0TE2P2W\ng4252ceN95EzmHkqhhl0WYs92m5X+CFj90e3MCR+FStD8ztl8DhEPuK8t8Ih\nKnAX8+z+YKgt++/UyRD4Bja6dTvKS6T6HvUzOkZgmwqjM5EV/rUBhUs4fmlw\nboSnxEVf0X31z6WKhgMDhvBNUs3/JB+0JObt3JNeQYu9t+9RdRjTid5ny0g5\nKzRTD2E9FhItJ12gNdEleuu2lQHKeP45oWb4nGrrWencZYnlAnIu54MsuKVE\nPENigmhEdqFPgGQsdv2TatWwgGWHKJ8R7BUk8ey/fgCca05rjtuhfhLgvxIj\nc7CHjEstJdQ1bMe1IUFJ6nzZoJPhWcgZ0Np+jan6Ir3kUDA5zZP7JJ9nFgOX\ngH+rIUzdicRun8bEezfT7pJj3yxBbhPNiSaPF/AdsoErhuke4ldYhiRVsQlY\nGIth+RrjUi+S02b+5akr947LRIZCJGyf65SGJaAuAYdS78btWGq4+8I79dO5\nYbwCdvCsUlYDKdAAAI/xBIvyJQh4CrN9fMTOHb1BsZSVg6VDRNRJE6hH+lOS\nPNK/ZbcQAhEfzk86b9c1rO3gmjzxEvGOuKJHesjs0RUUd1xQEIMtizGB40eX\nlCn11renP1kKmVzcXTZUsLGXrcnG5vQI74pf2dWkYRJdNnFPQjz9vffhlanN\nMGd1\r\n=xizq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.5", "@webassemblyjs/wasm-gen": "1.7.5", "@webassemblyjs/wasm-opt": "1.7.5", "@webassemblyjs/wasm-parser": "1.7.5", "@webassemblyjs/wast-printer": "1.7.5", "@webassemblyjs/helper-buffer": "1.7.5", "@webassemblyjs/helper-wasm-section": "1.7.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.5_1534431314572_0.42302937087630843", "host": "s3://npm-registry-packages"}}, "1.7.6": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fa41929160cd7d676d4c28ecef420eed5b3733c5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.6.tgz", "fileCount": 6, "integrity": "sha512-pTNjLO3o41v/Vz9VFLl+I3YLImpCSpodFW77pNoH4agn5I6GgSxXHXtvWDTvYJFty0jSeXZWLEmbaSIRUDlekg==", "signatures": [{"sig": "MEYCIQD98uv+dCyDWut3JMP7z4WDQrPJKNmyKoomgArznU8IRgIhAIfV0rhIn60ZYcrOGmA0p9RL0Dwl/rdVU6CUwdKGmr02", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbln1dCRA9TVsSAnZWagAA/+oP/i2IQWogIhZM/zl1YRlk\nNaCOvsK2TxY1dW1ItXuvLb5euuDmyJrChGU2D1MUK3qUKWHjkALrTVGYCvnI\nHjC/mr2iwSQJ4N+UpKOEaITKTunhHwwL3ipracpJbQ3lND9NkrOzwT4LvKQM\nbe1BWUE9zmO+drECP7WZLaW0NgzJG+rNTvJtBaPh+C6suoh1D3sfMY8Ctkcf\ncb66a7DRL3+DlG9pQUVNsRfNs5MQOgCoj/UiuYwgtK+wGXiG4PwbzLD4xNRu\nGzjUOwruk1zmJXrj2l9x1wiAxLtgIS4jPmm1++QDSM3LJQS6I5DXHTtqYB5s\nfjwh1rDVziKoSu9Gkh7Z1ucEdbSqB5VZxiWR+KrySuu5Hxxe1PWAJiZhWL1H\n8vzrmbvqaEj9Yg5qsQ2oApo0O2lYeXfJsmPlUSbkKWEDYzs8fr1uOQ98ywxQ\nGS9ypaXv9pXSdBsOweHFgzBGgpbTJ5lccaB2VJV+rf7M0CvnjSbDLZULJNYG\nvhU5yPrtD4WuH7jK1XFBUbud6+C/55RCxaD85yS9GojMW9ZsnP1SqOdZZ9e/\ngJ+7KBJbucD/RUMVjfJElJvZBrpHx6kwzC7aitOE0vHIBQpquyjNw5cwI7TI\noMay2QJ7uw4YysyfT7KGroZZMksdueq3P0RpTjlbSM5r5HdgpepXZBKQ5DrS\nntkM\r\n=zsvt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/wasm-gen": "1.7.6", "@webassemblyjs/wasm-opt": "1.7.6", "@webassemblyjs/wasm-parser": "1.7.6", "@webassemblyjs/wast-printer": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6", "@webassemblyjs/helper-wasm-section": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.6"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.6_1536589148607_0.43020102617100076", "host": "s3://npm-registry-packages"}}, "1.7.7": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0777da9fc26c2a97aad9eda9bbb05bacafa6a358", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.7.tgz", "fileCount": 6, "integrity": "sha512-yWxM1RqmJQzdZXMvps9J+wgO8Qw+1llGzpg2SUemL2klehnkXHPZB50m4EBh3Gv4VkiuRBdAbgI1qn+LdTLvVw==", "signatures": [{"sig": "MEQCIH+H7wRHhW7swjYmc2r6cT0DY/cVV3uYpOdB+3FKQoU0AiAxCG8v/zDhrA3N1uPq6L1BRSE70Fed2Mg6YpnuoXsVeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeE8CRA9TVsSAnZWagAAegoP/j+8v3U168DcXCYTjy2v\n47+4YG7RBsd6NwBA8E8dnj0IoBwdlWg1Ndtk/sCUGFFCwdUV/AV5w9Eo60sK\nAxwbRD253utLvYIVq7XrLATHtkktb5FA8A5DX36YcytVirpucELoCVzV3+ej\nDqLuiOACrLyGSbM1nmkgwizLFeYfc/YWpL4kIis8GeZB5OGiIXf9C7zAYmSt\nSOLLSGOf9eZpvpZJGUYamyvHmTxkZYVXF1xcha+cwp65d/PKOessp6yif2sz\nVvoWTDF+U4qOMpLAeAFgCs9l+SoEn2X6FUFHdkSAOlVjhwbShRgZQUdOULa/\nwET9uLGjdTmk59mtNhIFe8dBc8AfxrqRT3nI28kFWE9XqTZsclnYR6rRSEbC\nyJXrDE6S7yPRafcVorbwELzpn1AvFFB8zfkpLzi/lx7lKWKDwE+9MPkk5Kkl\nx6/WSx3+CreeMlhQGGNqUYGO8rAlyHvQ7JYzRJ6XpSKnrrPknPOwoOfl5fXj\nWLSbDTTQiYxwMMOR6/KUUnlqhVa21zjIPe5Segi/iJYiZ1GT1VVZaPeYwHrC\nvt+Gk41iAt8DF/exiVh3+euvHf+u3amVAY+xAYwuoDQghRBW7ixCDgnF78T4\nBR+mn1lmqhaa3CxyuRm71LKTNTBBh3I+fIjqQ/w5Ly/cSGGzDjYUzKkPvDu5\n12yO\r\n=FMzd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.7", "@webassemblyjs/wasm-gen": "1.7.7", "@webassemblyjs/wasm-opt": "1.7.7", "@webassemblyjs/wasm-parser": "1.7.7", "@webassemblyjs/wast-printer": "1.7.7", "@webassemblyjs/helper-buffer": "1.7.7", "@webassemblyjs/helper-wasm-section": "1.7.7", "@webassemblyjs/helper-wasm-bytecode": "1.7.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.7"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.7_1537335611926_0.4832683144013925", "host": "s3://npm-registry-packages"}}, "1.7.8": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f8bdbe7088718eca27b1c349bb7c06b8a457950c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.8.tgz", "fileCount": 7, "integrity": "sha512-6D3Hm2gFixrfyx9XjSON4ml1FZTugqpkIz5Awvrou8fnpyprVzcm4X8pyGRtA2Piixjl3DqmX/HB1xdWyE097A==", "signatures": [{"sig": "MEYCIQC2u5s5vzrA743dYWFDmYvAMipnh1It+rqUNei9SKgY5QIhAPRbDQBFpShDwUsrI6k+1QVPuhwVwna/JxF35UnKM5q+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/WCRA9TVsSAnZWagAAIhcP+wVHT56tfRJW6Ga8D//K\nVMR5QlC1qplUCXGZSj5Gm0ejReeiquJTqI8IhuFTUGHzK8t+hZZaWxlKCZTZ\n8KA8QrUZwZFtdIohzUHLRmB0EMS2UOgxS3BuxUodaD0LQJQBQyqtBxuI8hGI\nrf9CR6mAb03qf+j/BQxVDwvhM5/kswjtyPtsuj7exc73Ysd5DvWEmdHi1HHx\n9fGKh6NF9l2plbk92/+g+JvqA4s92rRG4uSf2Ji3HBCX8wnsjOD/Ts1zWaDZ\n8ymur5sfZ3xkVUJDtxxssHBkaMcQsP8p6uPh5G9kGIr/oiOclCSFkVwII7WZ\nGqAkc2BLocBRPOsrZ1OJ/eDPL0/MbCFThDxcsypfQx8rOIBqfkyfpohpnHkQ\ner03/vBisRsf/pRJ66L4NNvLb0BT3Y4IJgRCNaxr5PEl6MrEOhd0PE6Wob3S\nPmvEbyqaN4PQOAjpUIvvTOwTwR9coPuOcFYygWA6ndrgW4bxZeCshy/Xmwmw\nDiNWhEJd9ViYEe+X3dZLvzEBHUazsFirBuFe3wD5ed+lkQebbqBOO1l8T85f\nD1kLGZHcX1ft66tYu1SdSMpGQbuzTWP84r4lX3m0mkbe+EAlodUq5z/8rMd/\nAxEZ8PybGld3e9pY19albpSTjkXTmAE6d7IPdHfqBDlFj24/5vrj0Xpa2dZb\nQ8pR\r\n=Ybch\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "43b83b600939b19c48c3c27a1733592c493c4386", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.8", "@webassemblyjs/wasm-gen": "1.7.8", "@webassemblyjs/wasm-opt": "1.7.8", "@webassemblyjs/wasm-parser": "1.7.8", "@webassemblyjs/wast-printer": "1.7.8", "@webassemblyjs/helper-buffer": "1.7.8", "@webassemblyjs/helper-wasm-section": "1.7.8", "@webassemblyjs/helper-wasm-bytecode": "1.7.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.8"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.8_1537515477598_0.9882748002200845", "host": "s3://npm-registry-packages"}}, "1.7.9": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "82203801a11914c36772dbf7f1bb3ef46050132c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.9.tgz", "fileCount": 7, "integrity": "sha512-r502qHeO178A9fjCObCQ4JsaeRjrAFXjpUpYwhLaC9qXtx1ImQK3loTtL1IJrU46/2AEU+onz8uTr7rGIcqmbw==", "signatures": [{"sig": "MEQCIFv1F+sicRtoaTZO30n1pcgg7ybmgo3FVkgzdbmC50iCAiAOQzWHrr55MFlzrhm0eO8fz9+BX5ep6IakkGr+sXcGqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgZCRA9TVsSAnZWagAA0bkP/3d32McaWlW2CR44l5hd\npPLlJfEn7Bj5goJ3sLpM3UXG0VVwcWxr7tbWvD/Uc4CHmEfNbiSZimBRxTwK\nWXtKqJ6fasyWUxGAuz5tumOYXRfeM6gdJADZPAKPOl7WUgpzXEBdcV/bTVV9\ninAwjojKPAaRkF/vUdyylqLQms0PXz5STLE8x6GJTX2X1s2ft6pqrJJNM29K\njTDSDtIDkAlBDPbGLySeU/OMejByCwoSg5Jkpa7hLC2DtZCQZjsfdj3WzV2S\nDBth+evTcwBc3qD37b4VAlvezmjBbmPZNQbJ4aAXbX3z7vfL5WiHSTXBwccb\nw+tY1c78kZwDbYn7ZZpJmHRoMIrCpYqqKdxGdiM/9arf3To8LZxKzqEA5dWB\n3TJKf50vCHG6iF64Hv9qI3MNU1Vu9MR6L8Qnx0/qWsXFjOmNoxR0bC4QD5Pp\nFIhQJL+bJc3HhKqpQTL4tbJiTZLLWsUzeVyD5ww+guD14eabx5dOJQfLV0FZ\nF9RnuTSloZ/rStgHzsAFBE9iKYafxVKVUyJiruBfpSDzyYbpyn+nW2jwCLgM\nSn3Cda0HnwzhajPEy1keDpHjnYpoL3TmuKziakAnQ0T4q8Os5DVVSXg3/snI\n/EvTYcP4h9njmD9ntQa58NVomQZQiqMhvvl14JDd/FXwLyqTCJ8R3Gvf0L7z\n1wli\r\n=F+Kh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6c5bd6e21d734967e12bb7b7aaa38c80697b3b68", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.9", "@webassemblyjs/wasm-gen": "1.7.9", "@webassemblyjs/wasm-opt": "1.7.9", "@webassemblyjs/wasm-parser": "1.7.9", "@webassemblyjs/wast-printer": "1.7.9", "@webassemblyjs/helper-buffer": "1.7.9", "@webassemblyjs/helper-wasm-section": "1.7.9", "@webassemblyjs/helper-wasm-bytecode": "1.7.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.9"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.9_1539880984374_0.39930304146023987", "host": "s3://npm-registry-packages"}}, "1.7.10": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "83fe3140f5a58f5a30b914702be9f0e59a399092", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.10.tgz", "fileCount": 7, "integrity": "sha512-e9RZFQlb+ZuYcKRcW9yl+mqX/Ycj9+3/+ppDI8nEE/NCY6FoK8f3dKBcfubYV/HZn44b+ND4hjh+4BYBt+sDnA==", "signatures": [{"sig": "MEUCIB6G/xt4lw3QVvu8qhL3Usz8nqSAtwKf3OBiJHF2g61hAiEAuIZr+NczOIABkEc14XWOGjd9MBj8Gggwo0XMNs+pbEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuvQCRA9TVsSAnZWagAADsUP/0rx93Xql9HVUyfEseld\nOx/mIIXu4nJnfiJcdxypjV17IZBfZkE2Wwnn2mvZQPEWo5WyKElkS2EZLCfl\nXtxBi+vO7DHxtqb2Wl9VOWCdBlUPSOdWONkhDp5aWJTPnA0WloUTGidPhk7N\n2eM/nRS4juQg6ZaeFnzxPjp+N9r2ElgjX/OAAAHbSvOlsJqgavhnBy4bXFjA\nm+j5FjVZyhWQ/FeM+TLy2HeFr/q6mezqYe6XlCNOYvESd/qBwdJW/eZQeqvd\nXo75389lVW8+5T7H9YgGBAEP2nw/3SBN9mXM0ffX2LvzafejKK0a07GTAqPW\npQvTCgeCVZ/Uylwj2aAZrCZKcJAHUg0FYOE2mNrZrZCeIacld/ZVuFbmiSO9\nYp5X8HmEI4XhAAelNIdZWc55NFad4BgCkHKUMdJCz1rB247Ck3fghjOUVwBN\nN2CRjUM7bbHq/1g5FOFuuowtwYDBO8agvIxtG9GmtIwb9cpsEsBAjSZPmYi5\nk4mQa9z/6gKTo8YGxYeNc0V05R1dc5cmNzKiwJEt+FIlzh/SzYHj/dnIvj7c\nMBupRH8UpDcf2C17k492bwEDzolcst4RuyCNzscX8Cs5Y8LmVwRCbXdBF1SJ\nEdmpX/o5WyJO8PdyFx8FoBDEMaWAIq5UzrX3NGlD1h9jFMpttIDl7yMBElur\n34td\r\n=LaO7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f558c6c047187f24a2200ab04104f173de226794", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/wasm-gen": "1.7.10", "@webassemblyjs/wasm-opt": "1.7.10", "@webassemblyjs/wasm-parser": "1.7.10", "@webassemblyjs/wast-printer": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10", "@webassemblyjs/helper-wasm-section": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.10"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.10_1540287439936_0.9911741035038104", "host": "s3://npm-registry-packages"}}, "1.7.11": {"name": "@webassemblyjs/wasm-edit", "version": "1.7.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.7.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8c74ca474d4f951d01dbae9bd70814ee22a82005", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.7.11.tgz", "fileCount": 8, "integrity": "sha512-FUd97guNGsCZQgeTPKdgxJhBXkUbMTY6hFPf2Y4OedXd48H97J+sOY2Ltaq6WGVpIH8o/TGOVNiVz/SbpEMJGg==", "signatures": [{"sig": "MEUCIFXjA0nJAkBPdd4zjeUW/7Tg8zaXhimqQSIPBVTjpCJbAiEAl7KckFwk9JOj+MO/U4PjcZkA71wMDfSLCZ4/5PlOVQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxmCRA9TVsSAnZWagAAEEQP/3TipxY58G4nM0Fo4ohn\nhKsnfAKfx6Ef2ej3EWXdaMXPdnmWepJ4fUtcpOJKGJfSwfMsWwtHzFdP8U8z\nxWtNqayX+IMKTQxtUesTKQHFH9n1GTWuIjBeWP0Y8isEyTq7FojK/I70Eq6Z\nPhPo0KGabbfTvjqCLO4nGSYs9d2sn++PO9DmfIzIb4Lum1rFd5OUAMBXg7So\nxzXRgkO/WHyEPnQ6mKZazje4qlgTXl6XNgpo0jq7YOoJp8dujw535dFOm+B2\ngFpwosF0KsnOzjq8iDKdJSeX714EC06iuNBXzUwOyJHBXL7rrB2YzwIn5BOK\nbTgoi1v7D4NpPq7aJj8F7mpyZOgPf9M6QxFBE0SJXC3jE0C72YvqARut7Yty\noUqT3vgXH/c+w9eF9WMrSwbISaR9EEbl3TKF3pOlTBT47F6/XzrcdOoqFlxg\n7JbdhD3WhJo2GHQfAnBmclmHIhmsJ7+x+qR5vDUVZGKlwmxz9DRybFJk+VE0\nFbDOQ/lB/oC7/sVkNnrCXLRuOfYUW4VE58h4gVOZb3cH/DQgLNhcZaqyeKq2\nnbWo7QPGbYoaRcmO2TElgSkw2nOgIHHhcJTD8x7yAy71TGmRsmKtwk7js/w/\n6zeCjg5jUbJwvNh1G9YIRLDcAh+u+6vzEOHIDYu+5NIEyWZjJt6l7rCuBGmC\n3+7B\r\n=zZEd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wasm-opt": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11", "@webassemblyjs/wast-printer": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-wasm-section": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.7.11_1540922469316_0.9361353457929964", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.8.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f788faee33cd0080279774b3ef7da48f416e7717", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.0.tgz", "fileCount": 8, "integrity": "sha512-UYrHDQXUb+z8s8giBLXxCVnXfJafVbsAPivxkH7gu8F3RnysowNDQUgjfHDGTqdGNZRXmr286BoTDwGGxoIGuQ==", "signatures": [{"sig": "MEUCIAy4AyGTR4i3KcfOJXOgtATHvM2Ywm+uMUstVPsfL2xJAiEAhZ8GspgW4dbwE8zXAF+Dm7dynullDd/vmjoqAsucO10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2YxCRA9TVsSAnZWagAAY/cP/iZgUZvsMbGlSbf7jUT6\noQqbKgy7WG2kvvGMVqTokTWy/s89a3Em4woWUg8+BSuHIS1dkHRa+/u2J6CW\nZb8iMP5QB7q0c6Jsc1jTcYt34uAFkQVna9EVIhZ/iWckZQMj3Ac67ivrnOpT\nlkSTyiMQN2EAiLKzjyEoeMoD+scH9J2lmDzqzd4lelEBdugS+5J4EhlsUAoN\nqHIobM5rMXXp6bEi4j69hId+X32SjGIVmdx+eftBMbSiZy0va6ir/Ti9uzCd\nptiMN879iyP9piNIN5+XmDuAx8bWAx9Zoefu+kcWZy6ZmM1MWp92LXSDgJrh\nSjfnSXXxuM6V5no/koRB/jKRWrZwR7mOpVmKIifzQPcq+qRClEXV3ofqeC8J\n8AOzJwXAZ2zolHAEIC7NiEwYCoPaKHMVNPNKrqI6ZAu6sxYZJLvSMajFn8Ci\nZBl4pILcgIqdFZmdfi3AOB8kkYi3V0M8PEDG4/OBzn/zp0L8r/2P5sEmlNdN\nA9mWe/DfvO02dMkwnzBrJ+5aVTPNWnP4QrYyhZTcxRD9TWUKo+b2fCL3W+A7\nT+Tw/04NX46FqHW0ChiZN2CsekfiBf4XTZvqqw4iv0TEBG/jEYDtpWQ6Lpla\nmcT6dDbIFAPUD6FUpLg9bmPWFF6imWBP9HmdXaUm73zgXfYETcM/e/1CW25E\nbB4j\r\n=CvDK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "8b2d1afa793ea81f20ec63416134c201e39694eb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.0", "@webassemblyjs/wasm-gen": "1.8.0", "@webassemblyjs/wasm-opt": "1.8.0", "@webassemblyjs/wasm-parser": "1.8.0", "@webassemblyjs/wast-printer": "1.8.0", "@webassemblyjs/helper-buffer": "1.8.0", "@webassemblyjs/helper-wasm-section": "1.8.0", "@webassemblyjs/helper-wasm-bytecode": "1.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.8.0_1544513072767_0.21247269214310238", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.8.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.8.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c8aa4092718e5859190e4275c9f31885a8299642", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.1.tgz", "fileCount": 8, "integrity": "sha512-pooCQs7dSLjkHk2l6y7ZPNaGKBgjtfTTE+gk7bHj7UK+ky+IE0dClzkQA7B69qnEPCeb7Nyz9B/JqoYY5VxUJg==", "signatures": [{"sig": "MEYCIQD6t+Iu+XkqIZaEH3EsG05VSfa4czCWzvePmrWQqGYeqAIhAIwHan7zGxoo1FNIShzFTi547LwNkOeAdfGue8wK+pEH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgKCRA9TVsSAnZWagAAjUsQAJSNtLoPaAL7+TQwEbQ5\nVxq+mssObcRI239AwqiQwXqKy1Kg6P7/LJMM54+wBaZRRv0iZjjjiryxYl8M\no6SrIfE53jD9O4O+E6IcnGNb2xQsK+Ms70RanaDvzPr68j9tJg7AJ9YKKvJW\nKYJ/ydyIMcnvSadjzW+/fP3u/9160v8pQMmq6cdO4c+ECIGZbWn/OcGFoK96\ntWLUPaBF+qPx+5DQ3GRa+XhYCqbc5Xgbd1/gUwwttixx1H1UfWeX3OsuNKBY\n9eMRtT3pIaXbCiVRehEv1nBvN4sL63PoF5tyQihGM2aib9de7hOHMUvlKRDs\naW0HwF2QCzLSmHRMBnLUy3owViS9azMWr/EQMGMAi9d2zQPijrib/S7WrmhO\nsDRIvUbXSiXdh9QwETRk6iVRyymOaasUJhbACd2IUAXwLnUfzMOxkflHx2k7\ndVlQ0rcBgOeTA/ij4Lz7ZrEwzAIIz68LUvEAsyiC6FpdUIq1Mclao30yzWCB\n1Xt/V8m4Hf8hni8NZlcr30F3JwBsVAGb/6ctoThp0wIhPsDi30F2CvWg0k4G\nsF0Q7t9UdU00l/AkwqUIvQvOA41YIhc6JSGD4IQdXu8vWZmbBceRCf4EOI3W\nB2eww3ERPw87TS5sxX2rQjZj+AZTjTdFm39lHmY3rLXJI0qGtrJFoXOb73qR\nvPFW\r\n=846c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "a2f42245e9b597e3541e0f697253449d60fc4d79", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.1", "@webassemblyjs/wasm-gen": "1.8.1", "@webassemblyjs/wasm-opt": "1.8.1", "@webassemblyjs/wasm-parser": "1.8.1", "@webassemblyjs/wast-printer": "1.8.1", "@webassemblyjs/helper-buffer": "1.8.1", "@webassemblyjs/helper-wasm-section": "1.8.1", "@webassemblyjs/helper-wasm-bytecode": "1.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.8.1_1547540490257_0.06354361835447331", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.8.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.8.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3d3bbf10c46084e6525a1d9e2476bd072f0edd5b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.2.tgz", "fileCount": 8, "integrity": "sha512-rM1sgdLQrXQs4ZapglK86mW8QMml0FJ+jwZ5961sEmHISTkJRvheILuzA9jcKy5vwhWgkPf/nIhO2I6A9rkGww==", "signatures": [{"sig": "MEQCIFh7EcMqGrjrfhn0dG+3E7HinW8A9YoEuQc64zb6K7mXAiBSj2a/NooiEtTY5n9Sw3zYJ5G87CoXE21guDVJ4EI3VA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWjCRA9TVsSAnZWagAAE8oP/A1GNJ5hy4nglelTWqXw\nI3/R9kyP7hBcIlmElkxADwZwxOi0Xyt3fXtgzR0LDQmlRHPDhgkLJoriy1mS\nhyFJGb+JIVpg4eLdTzQIeGY/Odlr3ZToDvEyfU4IYWWdDHr5rNEIv0mrOqyM\nZz1dUB4DAw6VX/FCj9H18u5dp1WINwRJqWVDW4ObPsbNlu3LV2E2zc2i6LCF\n3cZpRdoB7huu2WAi2hW+unj2idq6ZxDmSbAY29azVb9GiXcsYxEGaHlSZCi1\ne8YrZZwb5uGsPoFRcleg0fohOIvUKiRcDCVzV9B8MP9TumNkOSTr0VZSgL7m\ni05Pq6bjoZGVGUcLlo0adsD9eP/eN3EVI7JbnDcpNvCO+G3AQdYNgLoUK1w3\ndgQ3zFzT2m0UBVBZUnhifeLzzlKE0jycm8QuiT7SzNWxDHk96y6uMo5j/W7g\n43QHY/PVJ2d5te+X87KMl5WnbowExaJIxewj+P6cQVZTZSApMv4OeZWmKS0L\n6l2j/xCAwriif7yLs8a+lpchgECD0/ALWPdIXRlHa5R+bHnDIpPsZW3xc8iJ\nFVU4DyM6QeXuzkLimRHGh9ix+Ae+4/CTK8dBfRXtQnlSH6Ubbj04joQ7BDkQ\ni9fYgiX6HYvXdb+bGN9tWr8M93ke5NsJwxI91hCuYd45fZtDwdSIUJIyjyPC\nU9lg\r\n=cVhg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "02af462b507aa7a24f5d3201178434b181bcdabb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.2", "@webassemblyjs/wasm-gen": "1.8.2", "@webassemblyjs/wasm-opt": "1.8.2", "@webassemblyjs/wasm-parser": "1.8.2", "@webassemblyjs/wast-printer": "1.8.2", "@webassemblyjs/helper-buffer": "1.8.2", "@webassemblyjs/helper-wasm-section": "1.8.2", "@webassemblyjs/helper-wasm-bytecode": "1.8.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.8.2_1550161314882_0.4453711304760868", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.8.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.8.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "23c3c6206b096f9f6aa49623a5310a102ef0fb87", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.3.tgz", "fileCount": 8, "integrity": "sha512-nB19eUx3Yhi1Vvv3yev5r+bqQixZprMtaoCs1brg9Efyl8Hto3tGaUoZ0Yb4Umn/gQCyoEGFfUxPLp1/8+Jvnw==", "signatures": [{"sig": "MEQCIHtU9JId+dKUcHxqL/dl+XiqLuX8kaR4rMAsysRIjlQaAiAn6Rd4ldPPsRJ1I5hA9UBkhBAXptTRTxtmVFUifLPu6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam7ACRA9TVsSAnZWagAATh0P/i/M9/GEJWPfV/zfBfGY\nBKKQ15nPXqlL5o11dgkrYCQvOcgPqKFtZ1gLR+aMY16stOrpzFjpw6J1F11V\nfq9Ylgcl9y0zvdAfitdxYw1+xAu+Bdj2ifKYhlcPNGbJNknImYgmyHL4C9l3\nhc48m1kNN4XrmEbzJ86EZPHl0HLb8aAGYaKYpcg15pddM0R/0Yl5Kbv3Vbaj\nXVzntZ6nyZ40cI9IBQtorsesNHnE+ZMyAhchQ77hrg74OntfBeypXQB79cbm\nY7fNdDQRntCxdwE30EqQAT0U3NFFMFB37kB/ZZEez4k4xMqzBnH+ZYdMdg+i\nFs1AzAMDK+QKqtFAB/DGhVUfycMFCjbmhXxGu6Ht5u00Fp7gZW1kESjSwd79\nKSF0GzKm3k0vcLRsViwelWl71nZPHX5Z82/6PncX9da016kECn5wsArmyrF+\nRExIsJVI0LDx9eKYnixYnOeST5eNSqJkjtIGjiBye/cqNyuVeqcmlBHxuo4g\nylxJojdbASXR4WciBC12dT5BDCw5VX2EYbCHEDmGYd0E/glRD4tvFG5SGYJH\n3i7Ucs6iaW3XSrlMOjNu+faNA1poZE7SDzXjE1S3293ViTpcVQUq9PtwdwOi\nhL9ONfsccwBBZFc2F6k8Y6EWZyO0/lB2WqgJlRwey9qTbW0obeYQQLChWQtL\nuD9z\r\n=MPY6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "e482c7ec291d61fc46e42c93d3b8ec7517b629e1", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.3", "@webassemblyjs/wasm-gen": "1.8.3", "@webassemblyjs/wasm-opt": "1.8.3", "@webassemblyjs/wasm-parser": "1.8.3", "@webassemblyjs/wast-printer": "1.8.3", "@webassemblyjs/helper-buffer": "1.8.3", "@webassemblyjs/helper-wasm-section": "1.8.3", "@webassemblyjs/helper-wasm-bytecode": "1.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.8.3_1550479039939_0.6385160718010494", "host": "s3://npm-registry-packages"}}, "1.8.4": {"name": "@webassemblyjs/wasm-edit", "version": "1.8.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.8.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "43585502e87d4a20518bc784b014e9286587420f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.4.tgz", "fileCount": 8, "integrity": "sha512-ejhFfyMVy3CpCqICW0D8B+LoSfXoySH8lSJLWn5KBU/GooZB1/Y7Wcuy29U0qm2sDAgcmeFzzIWc0B72uhOxMA==", "signatures": [{"sig": "MEQCIHgwbbYXA5Rlv8fiN5C4r406WNVw382XtUmtawbP8HL5AiBVzjxv5cJien5PTNpgscEzeXDX6NkajXd8fjJpgY3maA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyGCRA9TVsSAnZWagAAuDcP/jHD6d++SQ8ukgqkO4PF\ndnS24K87C529+NSBB5kkW48DWlBlzu9Ep18HaNud0rfA5M8X440+b36RVjHa\nmeQug2stSZXxheCQZHPqD+16MZsZHut1eLBuErkYekNw0N/D1+HLBZHbwbBG\nB+AOPHwJOPZFULdg2WGJHwD6dGiQ0aeGAfniKaX/P2nXfMyNl4p97cSRWydV\nhy4/vkxbjDIDIrlntm/yUGffFCvLmSRHY4bc3tvC+2fdn1Vit8TAEe51Z4Ki\nbSZUE01DATvRJb4d9B8ziysfsAL3QrbmQAm5voBGL6YRuuOYzBOqDm3US8By\nEoPQS3dZs30pdx59Ckll8RnoSWusRiVNYW2oYtNSd+g3TT7ye0PIjvBYx9YS\nDbtNrtbV9ypaPVP0dNw5pQ9CBkjn9Bpo/dVYPHTt7UrLEBQKaS1GJay+1HOM\n/Br+Sq3LmJTOZSgSs0VAGFO6uEjcsXxbG7EO46ivO3LecuUikorte83SwmRz\nbAjQG6PMp19h64TEi2nTu/8oPLhPzD++OO6+JNxDjkfrwkAFuEyILYgWcXWy\nDQVn4HMZvPfWUVphfS6Yv+NNPIBRSGFKm6SKysYgiPGTefXv7ia8J1Hrss3s\nca85GZJihOzDGZ57aQhaT24rm/b68AXPq4BFDUewUwDmf/vdXGjycFxPgANF\nl5Vu\r\n=taYv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0154b989cc9b41c695724a361b3aa6fa19c5b032", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.4", "@webassemblyjs/wasm-gen": "1.8.4", "@webassemblyjs/wasm-opt": "1.8.4", "@webassemblyjs/wasm-parser": "1.8.4", "@webassemblyjs/wast-printer": "1.8.4", "@webassemblyjs/helper-buffer": "1.8.4", "@webassemblyjs/helper-wasm-section": "1.8.4", "@webassemblyjs/helper-wasm-bytecode": "1.8.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.4"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.8.4_1550597254113_0.994786211566117", "host": "s3://npm-registry-packages"}}, "1.8.5": {"name": "@webassemblyjs/wasm-edit", "version": "1.8.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.8.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "962da12aa5acc1c131c81c4232991c82ce56e01a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.8.5.tgz", "fileCount": 8, "integrity": "sha512-A41EMy8MWw5yvqj7MQzkDjU29K7UJq1VrX2vWLzfpRHt3ISftOXqrtojn7nlPsZ9Ijhp5NwuODuycSvfAO/26Q==", "signatures": [{"sig": "MEQCIG1psqYwEGeLXUYyx3D47mbqNfQCHt8/VUEAtxJAo/toAiAuw5mcBqmJPhw7UjrqtvqGlMmgyZ1wVjJip3nkMxrK5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnU2CRA9TVsSAnZWagAAU34P/00YMAqTkc3ekzfl6yww\nXwcopJvvLwPg3iWh1VzfHmXyZj0QWi11wLmeYKAdAxsBMKLb2q5iPPItTwQd\nWigP7hcTpedgUG0U6Ip5rc+oDXn1QdauNOojqrvEXOA98+Q2WcELrWhIiWdU\nvNdWTHoC7ckp/SXhsXqfNTcQEhsQ4idMir0tFB68j4uWTciibIovBdkPD7P4\njWQidaq6a2ZsG3+j4OUs5Pf9R4P1ZNd2or0hb7uYKF+t0FultFzQb3Uaf3FG\nZ3kxq/5Xvcr0MI47Ed4mxgQOo6HfBfc1wWReLGz58+V9Er0E6uwlO2KAtKlh\nwKRauu3SFfuoayr3+JOUv1rPtocBBwXPJPTXMS6QwDRlcRtiQhmimR+WPWBC\n2vHXF6hDAZ1OxBL8nafY8bjISNsMofBwIRtdtqocHQfnx3foteivc0kgU9+P\nPkwLefrq3r6qCS0XYfBic6SocIDCnL09RWRjgoqhzfrHdgVtwF6/auuzNxgS\nrCG0U0iRX7eWFi35NGhbVLoTLGLEwiD/vayTz7nvoaxG0Ak2De2lqCOXdn97\nXoWCGuOSP26EnuFwQ8YL8UwlOdIXyV/6kDJx42Nrc3G+R+RgOuT4POlEgcOS\nLDjwbQYOceQiM/yNAGgkUt/hO05GziFu9YLcivCCveNpsmHDmx/wEM48dwz2\njGeq\r\n=vGH4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "> Rewrite a WASM binary", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/wasm-opt": "1.8.5", "@webassemblyjs/wasm-parser": "1.8.5", "@webassemblyjs/wast-printer": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5", "@webassemblyjs/helper-wasm-section": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.5"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.8.5_1551004982059_0.9796406039393042", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.9.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3fe6d79d3f0f922183aa86002c42dd256cfee9cf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "fileCount": 7, "integrity": "sha512-FgHzBm80uwz5M8WKnMTn6j/sVbqilPdQXTWraSjBwFXSYGirpkSWE2R9Qvz9tNiTKQvoKILpCuTjBKzOIm0nxw==", "signatures": [{"sig": "MEQCICr/5PI1Bqmxp+eH8TEKwsHzE2lWKC9u4p9iUhUSSiUZAiBQYfmixZuGL15XVBVq5/qRpYHK1i7Gnvg0Snn04W7qGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgemCRA9TVsSAnZWagAAes8P/A9uRtypVCKawcMCoY2v\nXQ/Iko8PIyQzNfO0KsGciHDAz2d2ajxdWhkqwctWLaOVdayRJSS/A/lw36/M\nwhaAD9VPxGj30NCoOhLOT4yxSvHpGqi1eS+KTDCMrE5G2ho/QYwsVrGeBHgh\n5n/hXgJNt54RzLQ5AT36kDa0kKqhvTj7K9g9oxnIrMr9G01xzm8rF1G5plEB\nYdNbzBJYybxWadHCa2CGkqMpzWM4jLrnP2YITtsGWgSzGfo2yuk+KahqKDf7\nmDV3in1I4RvOnYX2zMxhV+2VNip8Pxx9Tohfte3yxB7mLrbGFUvBZj2AdZwA\nxBaUc2QsuwIVdjTUl29s0IlpfTZTJPQOSAiMMAkgKKxyY2Nq92y2EIJzBOTe\n4dyapuSm6JwaXFkc72X3pdmGRFsrfLug3NshCdNIaZryGqFSTq1qmmDNYqNj\nbPCxfVA45OsU3fuCJSGhdkyO9x2I4kr3xu1aL766jUrMqomoFI8MTuG3sjB1\nNFn03FrK0shfIdv6/eojK8WcHf9NBEfU+pgfBrwc/DfDKtLdmLiyVo8MbAXo\neVvLDT7AtTIfmljG5q2feXekfICuMWAf/vbzNnhK9iWiupVPR7RyyAzcPwYU\n183SdFB9tV9KlWr4kHFlWvDN7dn7dhV5Uqxtlb9r1VDp34mBe7BHu0B4WoHM\nqw4H\r\n=Rxm3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.3.1+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.9.0_1580599206276_0.3791209354976195", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.9.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e27a6bdbf78e5c72fa812a2fc3cbaad7c3e37578", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.1.tgz", "fileCount": 7, "integrity": "sha512-S2IaD6+x9B2Xi8BCT0eGsrXXd8UxAh2LVJpg1ZMtHXnrDcsTtIX2bDjHi40Hio6Lc62dWHmKdvksI+MClCYbbw==", "signatures": [{"sig": "MEUCIFCmG+qnmvRaHyYx2Juc2StXxyBlzZglguPO6lmeLV/aAiEAoImuTl24DLa8TP6K8qGAy91ZuqlsOpfg7LpG3oFCcfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNonCRA9TVsSAnZWagAA7WwP/2mEohkzV7lhFjLct+jw\njWY5i7B7Kqpm4hqEVai1G+voKFsFbp660rTMo07f8CByXZimqL3eD5uZuvog\n4DEnqUcQq03HQS3AkiM/l8iHKNTSTLs7k1QMJUP98EaDDGX/Ec4xgTE+qy2t\nCzK65DejsNyz1yvz4bmMuAfysfWWUOEpjg/9VeCRgNvIY3n9bkaL5rEurjDd\nmheqs0L+EBqpvdJqUgAyDixMcVwivOkT0AMD2fwdetbJH7L9mto5YmMpMUdM\n3bfosgl/erp0sSwq6zgwJoHxlWGN29EJl/3GRfGQsYeimm3hwnIwhbsooDkn\nKEJviIkt+Cy/V8H/OA+JIS3MVGiHdejz+umw0qCxiBWJti20WaiREnPQWIV9\nqhf2XWrkadAaETSwKwH+oynKwFLCrgFUKzOsFfVahTvCSqG0Zed1cih/JJeK\nOYQY0pgUKL6ekLNljjeaaQ6DkRcWwJV4R6eHxTY8fK3SllIXhkrchtWmtmIy\nZbrk3wTc7XN/sBZldlidaeUtZZ0sltdC8gDhQd4zi7+NzePip1RXFLJqojcc\nlQWzIltPSyuY+guYlBMspklXnPCmK5vHlvWrhDA0dlhdmePE3tkHdzqa4uJ2\ncitRA8iJXImnA5009y35o5YkHMhdAfdPdiivnt9ThN0yrM0Mh/zXYxrTlHYe\n2k4q\r\n=hI9d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "697a5f63048049e9ecb3205d789c1e80eaadf478", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.9.1", "@webassemblyjs/wasm-gen": "1.9.1", "@webassemblyjs/wasm-opt": "1.9.1", "@webassemblyjs/wasm-parser": "1.9.1", "@webassemblyjs/wast-printer": "1.9.1", "@webassemblyjs/helper-buffer": "1.9.1", "@webassemblyjs/helper-wasm-section": "1.9.1", "@webassemblyjs/helper-wasm-bytecode": "1.9.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.9.1_1601755686688_0.07265784596353853", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.10.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.10.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "099ff98d9b2a23e23bb76ef76acde1e0f053315b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.10.0.tgz", "fileCount": 7, "integrity": "sha512-YvppsuH9uH0SubkcVyvtm2O+LzKOU046VjYFPcTf1hH3O2jSMbX3utGzYp3My/aD5m4Wi+c0mTv5SybTFI9M3w==", "signatures": [{"sig": "MEUCIHp54OV1d5duENYuNwOGKlyF4uvXYOHL6nKa8x2ovkbWAiEAsFOOiM8hSi7EYPm7oG2JFNiUMUyPTojQj0rzoHJORAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zRoCRA9TVsSAnZWagAAIikP/2iRASkV9AXNU2kHRAGd\nXK3XFatXwPjj+iQF4AIrX7ZL503P4Db63Kr110y6Efzo0XEbbsHLlhpws3wn\nuwIS9XrJMAYFJEVTfg4stiJtvmt8zLDDwJorM1O1tWj72U8zOENtR2uO5e+k\nsFOww9zPj96wl1T6kjHun0uzzktFbB/FUtgGH0ZZJui/3f2qZ5JeT4HXqfzF\nVhlE13gBIzEZVnpfaXjAaCbuR3WH5UmSTfieTRT+rYggWjJtS9S2VSRphPGB\nePQkdtWHDaMmqdA6KMphTihZn91TkQoe5CLF8lBAynU3wsKf+3NfL86HYV4A\nrVMkMqmxL/i8HYMI+CgUXXi25Vtk3w4MUHyjdaps8OEDC/P72aDmBxQDfpz/\nHJIYUBluG3pf4MIYr+M3CRhRfiaWnt7FWXRvJmsS3UXLauhk8brzY4+hcG9U\nu37ej3+y871b8uE39otahEgBvAoVS+a95sBB2BMHq8gJgp+qnV4XH1oYhZxq\nfi/lWcDwWWbl8WxKa7baVDLKHlqK8wIGH4cDKwbv4XDDLMck45XgpNJArx7o\nNvxLY0XQaD4Nb283JvpfTIyUJeVe623ZhY6cv5AcqN+wgCjDtxQ4WWpV49ED\naMdMwzxkNRI5jQ8yoZVjKID5z7nULqhlsGe1F5bltgXHg64f9lrui+7H6WQ1\npW0e\r\n=lS2L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "d00b899ece0242275e2475e75232ae18ddd03aeb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.0", "@webassemblyjs/wasm-gen": "1.10.0", "@webassemblyjs/wasm-opt": "1.10.0", "@webassemblyjs/wasm-parser": "1.10.0", "@webassemblyjs/wast-printer": "1.10.0", "@webassemblyjs/helper-buffer": "1.10.0", "@webassemblyjs/helper-wasm-section": "1.10.0", "@webassemblyjs/helper-wasm-bytecode": "1.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.10.0_1610036327852_0.9929477047520188", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.10.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.10.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "91c9d11d7ddf5dee060f9c9b86e84e6ff5398ffa", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.10.1.tgz", "fileCount": 7, "integrity": "sha512-MypvRwRyZ8ZAleI9wZLGCEv5b7TufkxLIabdKI4mGl8khfE9zOXHXROfJ+r8mAX/BQE2EHfftrKxkn8C+EIaJA==", "signatures": [{"sig": "MEUCIEM6H9iN8wFIDA4hCj1/vPKspFM+8k98fK4PZzf2cT7wAiEAqTVGb+zGvZUK4qj7zjYkYyjWKEdiaaxw+K+44p2IhkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zq3CRA9TVsSAnZWagAABdYP/jFJBIHXcr6e06cxFIHp\nW9YaJ++PXRmLaHXENKyqzQl6v21Zop1/efxA2ul4+BupWRd83DuZGB5/x2s9\n7qnxkNWtb4teNWHJGz01vk/XfJ9YuICah/9t6h5czjxwjbHmhLgX8opEpukd\nioz6P/m4v15N1eaX+aK3hwozMP/VuGjeOsdux39jhlAGrAneXHuYIQXPpgRD\nRjuBJ9Upsk7FBtjx/6GQ9MCfb8RmZlkUt6Ivvxf9wqbkBNK5SbDp/hi0D3cx\n/LUOYix4zyjvd5BrbFG+okYYRHVg9aRo6MCM+CA3G5zaiYYg6WDW9k1OWXKn\nuC1jqdYQ05qD0dvOa/a/QHk2KZhM26LHqKwixio4sLanQyyKGglUmO6zVYPK\nCpf5hokeFCikp4H4EbHKoMZGPj9TFYwTKE4dtbVfsKA3h0jdqsq6RiuyIZHS\nJAfkwZg3RM8czdybgPWwhQ8BoHRovvu6XrsjzlcA3n8CrhJ4A8uPwa0r0fWy\ncuxmXdcMmyZEd8SU9WFEgL82eoB1IsQw0gcBzUYxoEPjPZpnw75Nz9KrZPgT\nyaMP/JIBei5s+QkknDp+jQPvjrUuexKB/fzaT327VwI/51Gvzc/AzULn4BTa\nOcKYXsHr/rQXiO78IkCDOzxG45TiHDsBDB0BBQnX3pL1oDlaCpe2vtu3P9Bi\n3l5C\r\n=cOVJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f723f2cdd9bfccb5e199962dd8c5c09bdb0faca4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.1", "@webassemblyjs/wasm-gen": "1.10.1", "@webassemblyjs/wasm-opt": "1.10.1", "@webassemblyjs/wasm-parser": "1.10.1", "@webassemblyjs/wast-printer": "1.10.1", "@webassemblyjs/helper-buffer": "1.10.1", "@webassemblyjs/helper-wasm-section": "1.10.1", "@webassemblyjs/helper-wasm-bytecode": "1.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.10.1_1610037942561_0.6200070324876292", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "@webassemblyjs/wasm-edit", "version": "1.11.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.11.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ee4a5c9f677046a210542ae63897094c2027cb78", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.0.tgz", "fileCount": 7, "integrity": "sha512-JHQ0damXy0G6J9ucyKVXO2j08JVJ2ntkdJlq1UTiUrIgfGMmA7Ik5VdC/L8hBK46kVJgujkBIoMtT8yVr+yVOQ==", "signatures": [{"sig": "MEYCIQDnJ2LVXYumehhg9NWlQZh9xdXvFCpw5qd+mnkQlGW/rAIhAJq4kzhmcsNXodCrWpO+E7U82/dNi4wWaNWcKeGGGP1m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9070CRA9TVsSAnZWagAAIxYP/i8YEmhxOnUzimRhScZV\nYpc8eEK5EkoO+gpQbTF5EZ076NO6rr2XqYWn7Gt+05DahT4E3S131INy/iI7\n64DDMkziItUsV9hljZ2nqL7E2LRrNXAQYMGxYTqAvuqQb1iJNSu5X6UFO9YP\nUfHXR/X4L3HQlg1G5bUzqXCgeSV0fvQ7bZ6vjSGT3KkToSjh1jVnc/w2qJrf\ngChyTYiV8QQIrml4G+TzIW9fwkF81YZsYPsDhrGoAfPo++ltzi/28oCoXAVt\nALFaQImMd/MS27jonJ8RuI+QaMbTlJVpYrKt0Sql/QmzV5F9AbU2TGQIBYro\nkQXDHfDWMBztknCg5gIahNkMDB4T4gsHtmE1BigUXy6It8NpCknLspkvuK2w\nnQzTPsa8sM/XsnPjbXxgWsVPI1FRpWErnoWG3EP2ZouqOKH4QIWb1PFUNxTZ\nyPxQLZ8CuPMZ4C6vlvECgweVAQrd/ymW/IB0CtFmQF+BLdvOWlz4kMQ+vY7t\n4+hUHxUQqPy9cLQ4vQ2fDmB9ERHK55NnRYkUUKoFblbhL5n9r4jQiklX7uhX\nDD1KQ0FJepPGvY6LoPqMLWwJU9n1p1GQ1aOMbritv5R5P4tvP2q3zMd1f3Y+\n6D1WFGXjXKuSeYpdXlrPcru3baihZj4XE1UAfkV7Rtw3K/iCU4lfknb5e9cK\ntNA3\r\n=9H1h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "2646d3b7d79bba66c4a5930c52ae99a30a9767db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.0", "@webassemblyjs/wasm-gen": "1.11.0", "@webassemblyjs/wasm-opt": "1.11.0", "@webassemblyjs/wasm-parser": "1.11.0", "@webassemblyjs/wast-printer": "1.11.0", "@webassemblyjs/helper-buffer": "1.11.0", "@webassemblyjs/helper-wasm-section": "1.11.0", "@webassemblyjs/helper-wasm-bytecode": "1.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.11.0_1610043123473_0.11397643998434814", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.11.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.11.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ad206ebf4bf95a058ce9880a8c092c5dec8193d6", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.1.tgz", "fileCount": 7, "integrity": "sha512-g+RsupUC1aTHfR8CDgnsVRVZFJqdkFHpsHMfJuWQzWU3tvnLC07UqHICfP+4XyL2tnr1amvl1Sdp06TnYCmVkA==", "signatures": [{"sig": "MEYCIQC6ENcCENFMpER9o3DqwpCKNLV+3/NsmFPTaomU3+hHLgIhAN3HALSbo7S7iWN6GDXvbMN2b95pqXfC2yXw8NrA8FcR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sFECRA9TVsSAnZWagAAW8IQAII/pwTqmrd3Plc/AOPB\nvdkTTdjsiGAfF6QMRb6/1Q6lBhQLRe634xy4rHPG2vJVIrKXDrD0syhlbO9b\nBrsjlKWgH+0zip9TngPO+Cwh7PRm+qv8aHdWvUIa0s03GT2t5WrxLmnea9z1\n8k551KI8ZtImhcnk7eoBJ8zM5TuFS418w08mZx2DzqUcRf/h8wKct9RyFMlR\nfx1qsUupb7jpbNCCF5kyPnRxZlIUgZ56MpbZE9Ayb3tlqcsTv9GNveQAv3sd\nMNgH6Bj58bUpPE+xJnmAyOxBKxSzV/WTyR9p2gkRX3Bwg64PM4MT820J6k+E\nWpNaqANI1ZpzskLhF8SFGy39tmVBi0kHlp1P0eI1MAuFMC+O9FxDYnEaWH3G\nm3/twjax6S77w6mSWO8pnuYyl5zGCgRtnUP/Tih50YKI4fGO8ryxtqcBWybD\n/6hUelUgYXptxj+2iFoZREOgL59vwAVHO+xoytTCs+zhW2opJUQnBYzt3tvr\nLCm3HAcWECdvYqg76VNLAncHlSad0TFIn+FKcDo2Hq4IMthN9XBzNI7BfsA6\nzeTeRELB/3BXYyxxvERXstL3C4bm6ATRK5GIFU+Zb3/NDtUcMy8WC/hGBZyi\nvZF6aYC2IFwTY0oR6oIu8HfKcjvY2peLHrWne38Dcbj8DfDlRW4KrcHrYxwn\nHB6i\r\n=cY5j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-opt": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/wast-printer": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-section": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.11.1_1625473348588_0.06656220279092806", "host": "s3://npm-registry-packages"}}, "1.11.3": {"name": "@webassemblyjs/wasm-edit", "version": "1.11.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.11.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ab44fc7406bdbf393a19154a7e25f9e9219283c8", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.3.tgz", "fileCount": 7, "integrity": "sha512-FAsr/o9L4TYujNZZQbWfNsTru3UXQZTCysjvOKllXp3i4MZT5WhA4+oESzoHl4qYDHE1tQaIoC3vv34hfDeaZw==", "signatures": [{"sig": "MEUCIDVJQRVH1ZC/RUfgMbUJGScGaG6lG52tbHIQtCE1NN+iAiEAhjjyqU9EkABJbhlQ8z2GNTll4LyuGN6eHTLuJ/8jbzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34634, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjAg/8DZsz3S6/+2TP/FffJfiyROLqoxehs1OLzRsz9HOb+98v5OtK\r\nxQaYD2iIEQ4AdPZh/vujBV+FWUx5lq8c3rzjoQrozcFBd9pf+C4pOArAG+oB\r\nOOcLdMRzl5pQfysOsFePWDS8dG/2SO+An4M7u/pSx49U7U5legDCKdwbAeaP\r\nUzVye7WJvDslbmVNOs25zX1xA/QYr3z/jfoyq9hOSrutrn541sB9O+TNst58\r\nsbqppHGCIAO6kYRfHN0BsPtqcDO/9c3TXYUAH7SQcTkT+egMFCxMxeNvHCE7\r\nJ6Uuka9pRCJqe4hRSiQZVa4kj5N/GksmKBlK6vrt2LQrrtKRuSS7APvXbwI3\r\ncV0/2kKGPdxrGif0w9walhtJ7BnEiU4xbQG+teEBlJ4ss7ZYPS+jPf9sNZQA\r\n8FAvdcOuKmXNQQz84RFOgOtTT06HvdOMKYW76k/iEPp9j5/2mOCkExrNsk7k\r\nbWWjQ9GJzQv7990v4cFwijI2RdHGVgybO2viFNAZHnKQ2dKH7FqWk9f3dHix\r\nwBG7NE/R5T5XogMFPjd3Ao2GEOv8JZS5jn7wBDX5dHJPE4efFIvIUsD37zUl\r\nquF9f/P7fkBe4ME60XqrHP/dtvSPJ7hDRMYpzEKK3XSVkz0bRd1HRX/CZyiz\r\nby881JrFNkJrrvuE294Mg8SVdWZV4xIjAyk=\r\n=ARR/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "5fd2425602b752576bbe8089c343d5d70ebc861c", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v16.13.0+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@webassemblyjs/ast": "1.11.3", "@webassemblyjs/wasm-gen": "1.11.3", "@webassemblyjs/wasm-opt": "1.11.3", "@webassemblyjs/wasm-parser": "1.11.3", "@webassemblyjs/wast-printer": "1.11.3", "@webassemblyjs/helper-buffer": "1.11.3", "@webassemblyjs/helper-wasm-section": "1.11.3", "@webassemblyjs/helper-wasm-bytecode": "1.11.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.11.3"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.11.3_1656762779722_0.5891273408280648", "host": "s3://npm-registry-packages"}}, "1.11.5": {"name": "@webassemblyjs/wasm-edit", "version": "1.11.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.11.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "93ee10a08037657e21c70de31c47fdad6b522b2d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.5.tgz", "fileCount": 4, "integrity": "sha512-C0p9D2fAu3Twwqvygvf42iGCQ4av8MFBLiTb+08SZ4cEdwzWx9QeAHDo1E2k+9s/0w1DM40oflJOpkZ8jW4HCQ==", "signatures": [{"sig": "MEUCIHKXsTWpzmQ6+A3Yiab1wSEnw7Ph/7D8Tq5lzWuILK/eAiEA2tGwKA0lDZMiLODRKNjxd3bBqAmw3XmRRQIPmSoMgd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6BuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkWw/+Jtdej/JA1sdJ4HlrN+IZZ86YD1ubbjIJqhi3v/NOq5s/RwzW\r\n4u6Bd+o5pNuMsEsaV6IWMxFCTnzyVQVBvrRuUBkMtssYTEGIy8LOlkQiTT0L\r\nchJ2eXcjNDoFXTsr3C6xUOVbjGSovh/g76lH+A7CMSr8joyRP3E19zLzNTNk\r\nATi9TXKs1DZ/ak4t1vhfE4l6jGjw4luBgPcF+QDazXzmFP5+qioSP5w4VcLR\r\nRhMBOavWgSsLibh2T+0IohE5hnA20AXzv1hi2QsotyJjCMBYqtwx0aHM32VE\r\nshXpWGT65+BjTC4n2xpLarjK0XPLC2QwtJvEVGaUg3kPeIfNqseGdVq45j26\r\nLUYZeDwkSUbEQG+eGJFTGyqqiuwQJlJs6Bt4D9LjZf7HoXt5Tirpu4cgeGOU\r\n3pt5cpF4wZeII9ILNfvGeZMNMYnh9Vwvs80k0nHx3wmLEWu9Typic/6gVxKR\r\njGf8OnuydzkQrIelY8zTqQIuiZ0rzYGZ9Kdfs31ux3IJTrb4QlLuCQWiy4fF\r\n1tfyflVUp3wv9vHrmXOZbqsWKANB2CG0cuwhONkEJ+XYjV30dBqlN+JanoGs\r\n/B7a14xtDmCn5CylzNClyYtrghaqMg4eBSBKeRbAHjR1oP4XQRbWIv4F6hvA\r\n8E5+F9qd1cKZt+fwBzemKQRFEP0jH190c7I=\r\n=Dkj8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cc856f3cc847a69c31e92ceb2c6527e1d30a9511", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/wasm-gen": "1.11.5", "@webassemblyjs/wasm-opt": "1.11.5", "@webassemblyjs/wasm-parser": "1.11.5", "@webassemblyjs/wast-printer": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5", "@webassemblyjs/helper-wasm-section": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.11.5"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.11.5_1681629294117_0.11713239696300581", "host": "s3://npm-registry-packages"}}, "1.11.6": {"name": "@webassemblyjs/wasm-edit", "version": "1.11.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.11.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c72fa8220524c9b416249f3d94c2958dfe70ceab", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.11.6.tgz", "fileCount": 4, "integrity": "sha512-Ybn2I6fnfIGuCR+Faaz7YcvtBKxvoLV3Lebn1tM4o/IAJzmi9AWYIPWpyBfU8cC+JxAO57bk4+zdsTjJR+VTOw==", "signatures": [{"sig": "MEQCIC7NwVpJb3lLSzwwh77o9D+QYoDWpdD0shUAOKxEKkQXAiBUyTEO8x6woQNURqDJxa8Gn1Uf4bVWvl7Xbel4jq2W9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18994}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "58d40904ea7de2dd17f6f8d894ebe611b812a4db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.6", "@webassemblyjs/wasm-gen": "1.11.6", "@webassemblyjs/wasm-opt": "1.11.6", "@webassemblyjs/wasm-parser": "1.11.6", "@webassemblyjs/wast-printer": "1.11.6", "@webassemblyjs/helper-buffer": "1.11.6", "@webassemblyjs/helper-wasm-section": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.11.6"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.11.6_1683645081734_0.0731986622310643", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.12.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.12.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9f9f3ff52a14c980939be0ef9d5df9ebc678ae3b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.12.1.tgz", "fileCount": 7, "integrity": "sha512-1DuwbVvADvS5mGnXbE+c9NfA8QRcZ6iKquqjjmR10k6o+zzsRVesil54DKexiowcFCPdr/Q0qaMgB01+SQ1u6g==", "signatures": [{"sig": "MEYCIQC5NR31KF6p448qrp6Nsz1xq/eQvjzhzPGM+0ZWweoWMgIhAOtrNzQo/0UetsmyXKOx4Vxb8mK9SusxoDewuR1WCf7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34634}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/wasm-gen": "1.12.1", "@webassemblyjs/wasm-opt": "1.12.1", "@webassemblyjs/wasm-parser": "1.12.1", "@webassemblyjs/wast-printer": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/helper-wasm-section": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.12.1_1710325162892_0.09595109435425586", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.13.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.13.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "84a7c07469bf03589c82afd23c0b26b75a3443c9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.13.1.tgz", "fileCount": 7, "integrity": "sha512-YHnh/f4P4ggjPB+pcri8Pb2HHwCGK+B8qBE+NeEp/WTMQ7dAjgWTnLhXxUqb6WLOT25TK5m0VTCAKTYw8AKxcg==", "signatures": [{"sig": "MEQCIBEuj6fEP2iNmMetfvgwp4FynBmrgHZ5KlDOWJs4Y5C/AiBcfQUv0KOOAQJGoDh3GpGwArsenEqMsBzwlAaHW5/64A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34634}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cfe35c57093d414839b9350398369b78d97815b4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.1", "@webassemblyjs/wasm-gen": "1.13.1", "@webassemblyjs/wasm-opt": "1.13.1", "@webassemblyjs/wasm-parser": "1.13.1", "@webassemblyjs/wast-printer": "1.13.1", "@webassemblyjs/helper-buffer": "1.13.1", "@webassemblyjs/helper-wasm-section": "1.13.1", "@webassemblyjs/helper-wasm-bytecode": "1.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.13.1_1730912134665_0.15054418505720513", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "@webassemblyjs/wasm-edit", "version": "1.13.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-edit@1.13.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "14ab0a64f1504d1690301087368a0ebd28408861", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.13.2.tgz", "fileCount": 7, "integrity": "sha512-Qan1LIGy5Buo5+LZylJyHMfZ1zyyxnmn3eKmF/e9Z0TLY6qX1yL5SPXzOv4S0vqiLRE3VtBNpwvx9bc3BT0Q2w==", "signatures": [{"sig": "MEUCIQCof1AiCyK0yDLb/w09wsiauKOBifkpPmyeDnmS+UcPqgIgT6tmNQd9srAT0wickXFfO78mHSf3Ff5zNCzb+sNEPAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34634}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "> Rewrite a WASM binary", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.2", "@webassemblyjs/wasm-gen": "1.13.2", "@webassemblyjs/wasm-opt": "1.13.2", "@webassemblyjs/wasm-parser": "1.13.2", "@webassemblyjs/wast-printer": "1.13.2", "@webassemblyjs/helper-buffer": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.13.2"}, "_npmOperationalInternal": {"tmp": "tmp/wasm-edit_1.13.2_1730929439858_0.38396126086754734", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "@webassemblyjs/wasm-edit", "version": "1.14.1", "description": "> Rewrite a WASM binary", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.14.1"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "homepage": "https://github.com/xtuc/webassemblyjs#readme", "_id": "@webassemblyjs/wasm-edit@1.14.1", "_nodeVersion": "21.7.1", "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "dist": {"integrity": "sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==", "shasum": "ac6689f502219b59198ddec42dcd496b1004d597", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz", "fileCount": 7, "unpackedSize": 34634, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICe8RADGPIzio8npNhhDkBdtRHoN8WVazhadOK4BLVtTAiAPydJER4FOuSGuzSFT1QpmN6X2WIkNLN/hjABF9VXJVg=="}]}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wasm-edit_1.14.1_1730930022713_0.2449484169132572"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-05T09:21:26.368Z", "modified": "2024-11-06T21:53:43.119Z", "1.1.0": "2018-03-05T09:21:26.629Z", "1.1.1": "2018-03-05T10:27:11.446Z", "1.1.2-y.0": "2018-03-05T16:16:27.758Z", "1.1.2-y.1": "2018-03-08T18:10:23.661Z", "1.1.2-y.2": "2018-03-09T07:58:07.966Z", "1.1.2-y.3": "2018-03-09T08:09:17.208Z", "1.1.2-y.4": "2018-03-09T08:15:47.743Z", "1.1.2-y.5": "2018-03-09T10:18:46.517Z", "1.1.2-y.6": "2018-03-09T10:29:23.579Z", "1.1.2-y.7": "2018-03-09T17:07:50.493Z", "1.1.2-y.8": "2018-03-12T10:23:06.152Z", "1.1.2-y.9": "2018-03-12T11:05:32.265Z", "1.1.2-y.10": "2018-03-12T16:12:39.241Z", "1.2.0": "2018-03-15T07:35:36.811Z", "1.2.1": "2018-03-15T12:50:13.722Z", "1.2.2": "2018-03-29T11:47:52.856Z", "1.2.3": "2018-04-07T10:27:06.568Z", "1.2.4": "2018-04-13T10:31:49.029Z", "1.2.5": "2018-04-13T17:07:24.492Z", "1.2.6": "2018-04-17T15:20:41.313Z", "1.2.7": "2018-04-30T14:44:01.336Z", "1.2.8": "2018-05-02T17:25:31.305Z", "1.3.0": "2018-05-04T13:31:43.554Z", "1.3.1": "2018-05-07T18:09:48.030Z", "1.3.2": "2018-05-08T18:47:52.240Z", "1.3.3": "2018-05-09T07:53:57.967Z", "1.4.0": "2018-05-09T15:00:12.714Z", "1.4.1": "2018-05-10T19:45:27.119Z", "1.4.2": "2018-05-11T14:13:57.279Z", "1.4.3": "2018-05-12T09:21:48.015Z", "1.5.0": "2018-05-14T16:47:51.844Z", "1.5.1": "2018-05-16T11:26:20.101Z", "1.5.2": "2018-05-17T12:51:35.604Z", "1.5.3": "2018-05-21T11:09:11.059Z", "1.5.4": "2018-05-21T12:57:49.561Z", "1.5.5": "2018-05-24T08:02:48.005Z", "1.5.6": "2018-05-24T14:22:40.667Z", "1.5.7": "2018-05-25T12:55:12.692Z", "1.5.8": "2018-05-28T12:49:53.333Z", "1.5.9": "2018-05-29T13:14:21.872Z", "1.5.10": "2018-06-01T13:22:08.741Z", "1.5.11": "2018-06-06T08:59:27.019Z", "1.5.12": "2018-06-07T09:26:26.697Z", "1.5.13": "2018-06-30T13:45:35.462Z", "1.6.0": "2018-07-16T09:06:22.980Z", "1.7.0-0": "2018-07-18T12:59:24.765Z", "1.7.1-0": "2018-07-18T13:10:37.512Z", "1.6.1": "2018-07-18T13:51:50.878Z", "1.7.0-1": "2018-07-18T15:01:57.485Z", "1.7.0-2": "2018-07-18T15:17:06.472Z", "1.7.0-3": "2018-07-18T19:14:30.453Z", "1.7.0": "2018-07-19T16:02:14.818Z", "1.7.1": "2018-07-19T16:32:54.539Z", "1.7.2-0": "2018-07-19T18:11:02.921Z", "1.7.2-1": "2018-07-19T18:25:58.050Z", "1.7.2": "2018-07-19T18:35:03.581Z", "1.7.3": "2018-07-23T07:03:33.762Z", "1.7.4": "2018-07-24T07:03:22.527Z", "1.7.5": "2018-08-16T14:55:14.645Z", "1.7.6": "2018-09-10T14:19:08.788Z", "1.7.7": "2018-09-19T05:40:12.028Z", "1.7.8": "2018-09-21T07:37:57.771Z", "1.7.9": "2018-10-18T16:43:04.469Z", "1.7.10": "2018-10-23T09:37:20.101Z", "1.7.11": "2018-10-30T18:01:09.454Z", "1.8.0": "2018-12-11T07:24:32.903Z", "1.8.1": "2019-01-15T08:21:30.480Z", "1.8.2": "2019-02-14T16:21:55.068Z", "1.8.3": "2019-02-18T08:37:20.096Z", "1.8.4": "2019-02-19T17:27:34.208Z", "1.8.5": "2019-02-24T10:43:02.194Z", "1.9.0": "2020-02-01T23:20:06.378Z", "1.9.1": "2020-10-03T20:08:06.851Z", "1.10.0": "2021-01-07T16:18:47.979Z", "1.10.1": "2021-01-07T16:45:42.742Z", "1.11.0": "2021-01-07T18:12:03.601Z", "1.11.1": "2021-07-05T08:22:28.694Z", "1.11.3": "2022-07-02T11:52:59.877Z", "1.11.5": "2023-04-16T07:14:54.307Z", "1.11.6": "2023-05-09T15:11:21.877Z", "1.12.1": "2024-03-13T10:19:23.093Z", "1.13.1": "2024-11-06T16:55:34.800Z", "1.13.2": "2024-11-06T21:44:00.046Z", "1.14.1": "2024-11-06T21:53:42.909Z"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "description": "> Rewrite a WASM binary", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "readme": "# @webassemblyjs/wasm-edit\n\n> Rewrite a WASM binary\n\nReplace in-place an AST node in the binary.\n\n## Installation\n\n```sh\nyarn add @webassemblyjs/wasm-edit\n```\n\n## Usage\n\nUpdate:\n\n```js\nimport { edit } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst visitors = {\n  ModuleImport({ node }) {\n    node.module = \"foo\";\n    node.name = \"bar\";\n  }\n};\n\nconst newBinary = edit(binary, visitors);\n```\n\nReplace:\n\n```js\nimport { edit } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst visitors = {\n  Instr(path) {\n    const newNode = t.callInstruction(t.indexLiteral(0));\n    path.replaceWith(newNode);\n  }\n};\n\nconst newBinary = edit(binary, visitors);\n```\n\nRemove:\n\n```js\nimport { edit } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst visitors = {\n  ModuleExport({ node }) {\n    path.remove()\n  }\n};\n\nconst newBinary = edit(binary, visitors);\n```\n\nInsert:\n\n```js\nimport { add } from \"@webassemblyjs/wasm-edit\";\n\nconst binary = [/*...*/];\n\nconst newBinary = add(actualBinary, [\n  t.moduleImport(\"env\", \"mem\", t.memory(t.limit(1)))\n]);\n```\n\n## Providing the AST\n\nProviding an AST allows you to handle the decoding yourself, here is the API:\n\n```js\naddWithAST(Program, ArrayBuffer, Array<Node>): ArrayBuffer;\neditWithAST(Program, ArrayBuffer, visitors): ArrayBuffer;\n```\n\nNote that the AST will be updated in-place.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}