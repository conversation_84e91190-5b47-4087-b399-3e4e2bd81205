{"name": "rechoir", "dist-tags": {"latest": "0.8.0"}, "versions": {"0.1.0": {"name": "rechoir", "version": "0.1.0", "dependencies": {"resolve": "^0.6.1", "interpret": "^0.2.0"}, "devDependencies": {"mocha": "^1.17.1", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "dist": {"shasum": "107c8d54e0479748f9e85fa6b976d77bd80faf6b", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.1.0.tgz", "integrity": "sha512-MVIOEdGZ6b4USVyq7oBH2rFXb9bE6fqYOsGiHQlE/PuNToMaShM6T0bTG1gQ0NkYX10CssxM6rlWDQhOYRtLlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCF2esmxfIAVlGO8EVIAEtZoyRDL15w4q9emNDGrvit4AIgKh9uGzaL+1GbkLnBIVA9XPrcGhzrDTjQ049XXc6araI="}]}, "engines": {"node": ">= 0.10"}}, "0.2.0": {"name": "rechoir", "version": "0.2.0", "dependencies": {"resolve": "^0.6.1", "interpret": "^0.3.0"}, "devDependencies": {"mocha": "^1.17.1", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "dist": {"shasum": "673b56ff87558922a2d4309b70996e8ceb51ee19", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.2.0.tgz", "integrity": "sha512-vhMjCzLqyPzgWXAK9YCqT0U49KesbDw9NfGeYmUVaZvnw/sApyPn7nxx8s7a37c8QNzAbMtkv2qqNDVKsXxUzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/risat7iY6wxhROUZxPtcbIcbBYHrHqNnICRIKSfYgAiBjn2dmfiQgYAONCKu3HhFJyxt0PkC9KSkGFj89FI4u/Q=="}]}, "engines": {"node": ">= 0.10"}}, "0.2.1": {"name": "rechoir", "version": "0.2.1", "dependencies": {"resolve": "^0.6.1", "interpret": "^0.3.0"}, "devDependencies": {"mocha": "^1.17.1", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "dist": {"shasum": "7047685cc660b676f9038f3c5b0c37bb6b82d531", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.2.1.tgz", "integrity": "sha512-eFhsT22oKCB14/30mbyiGd8CCBPSu3nycNz3hYa9Ug/QCBwD17UizHUuNPQx18SmXauvqQ9+QAlpAt92vTPz2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFKDvwbjObUqpnv+UQk5Fx8mf8W+T4bqFW8LmARFrcaQIgCaGIHQ8jH6guVzqimo2fOcIvHhs/Rc5R+R25WIiVh3o="}]}, "engines": {"node": ">= 0.10"}}, "0.2.2": {"name": "rechoir", "version": "0.2.2", "dependencies": {"resolve": "^0.6.1", "interpret": "^0.3.0"}, "devDependencies": {"LiveScript": "^1.2.0", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "iced-coffee-script": "^1.7.1-b", "json5": "^0.4.0", "mocha": "^1.17.1", "node-jsx": "^0.10.0", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "toml-require": "^1.0.1"}, "dist": {"shasum": "0f9a1a09ac410381f5ae953b70b37149bfd06c4d", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.2.2.tgz", "integrity": "sha512-LXfROFzT+u0n06OCQypwtwq+CaSmczvjHJ4Te5sZdswnTEOtnY8jbTH2vJQ68TkxGPWDy9KnkU+t7mKDRwdyjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFWQaHtaLPyW9FQjzimeYF5J54CovS1cTBzKLvgm7SR5AiEA7WPNkIQlaJJKdVdR2hi52L/h76SK5l5bBXOyIP+IBQA="}]}, "engines": {"node": ">= 0.10"}}, "0.3.0": {"name": "rechoir", "version": "0.3.0", "dependencies": {"resolve": "^0.6.1", "interpret": "^0.4.0"}, "devDependencies": {"6to5": "^2.9.4", "LiveScript": "^1.2.0", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "iced-coffee-script": "^1.7.1-b", "json5": "^0.4.0", "mocha": "^1.17.1", "node-jsx": "^0.10.0", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "toml-require": "^1.0.1"}, "dist": {"shasum": "db1ee6f8f27596a3720aa9d83b9a7aa74c99d4a3", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.3.0.tgz", "integrity": "sha512-oppyLr2ChpILyPFy7rA5QpqVtDIUJkQdSnZX82yQjEBXXd14WDpp7r5EZhBjaytEyNWqDcEeK/4unirfIgvXdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuM7B4h0T5wmPchi7lTS+1KVp+2yFDOjcHLBBB5kJIVwIgXSMYpibZFDhQFmkpuIhxFUo0iYpuKBLl/NqeuPtxjwY="}]}, "engines": {"node": ">= 0.10"}}, "0.4.0": {"name": "rechoir", "version": "0.4.0", "dependencies": {"resolve": "^0.6.1", "interpret": "^0.5.0"}, "devDependencies": {"babel": "^4.1.1", "LiveScript": "^1.2.0", "chai": "^1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "iced-coffee-script": "^1.7.1-b", "json5": "^0.4.0", "mocha": "^1.17.1", "node-jsx": "^0.10.0", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "toml-require": "^1.0.1", "typescript-register": "^1.0.6"}, "dist": {"shasum": "7445ba6f5b0e3fc8af8c65884bc847afb5b023b6", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.4.0.tgz", "integrity": "sha512-kwt9+3mo/aWkB41MayNoNFMO+DGNIf8VC5EvvdfnoagfDMBMIZgICpfA3NwjruJqDCbMyuDRgtLfiz3+IrPxVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGlv7MvYu25jVvFSjK/k+LaeowqQmgT7tHrbYUplOhGGAiEA7SZEETYHf3vfL3dURuXnwXE170hebqwNN9zObYdPWtU="}]}, "engines": {"node": ">= 0.10"}}, "0.5.0": {"name": "rechoir", "version": "0.5.0", "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "dist": {"shasum": "aa7f34454ad246ec2ce6bb986b983f1d9647adfe", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.5.0.tgz", "integrity": "sha512-q3U14QZKKmZ5450v0giRBTm7UV0vjUkiEfXwTaZTz+V3OmDgIrn5JJ12De9vzpsZh8HGNOrGrADFPAfh91EelQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDbO+Fu+0kKEA0FUPoOEEEUElVIct2xNw7RW0IRbKmhAIgcK+p8ouXuXQu2js2LlfYcDCRGA2mwfJhyFaQ58fWhfY="}]}, "engines": {"node": ">= 0.10"}}, "0.6.0": {"name": "rechoir", "version": "0.6.0", "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "dist": {"shasum": "cc3f3dd4718636a7f580ef7f202b24bee3a08376", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.0.tgz", "integrity": "sha512-9UfomdwOAUYd0k6y/1hL8m16/2fjASoDFVs73Cm/q3SY/mQy6cSS2Rhnl/6Q8kb+3N8z4VL8jItQ28iuSt7fgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBihSYyJjmjbhPTOw3anfaYa8loRYWO8uJI1vHu5RhewIhAOU6apEgyN0Xw0mQpdxd9NipbsxglpST09PPufizl8Lz"}]}, "engines": {"node": ">= 0.10"}}, "0.6.1": {"name": "rechoir", "version": "0.6.1", "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "dist": {"shasum": "462beb9fe212d568a0d1282447b09de364e5f435", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.1.tgz", "integrity": "sha512-7/v0lyIPoo0zEQL5c5wKlbhPvAOlDzvs2yvxnI9eevI6x0fbbxrQu5DZnzBWuydU6vbZTc3edVzZoJTXnhBXaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAd2N08Q9P3SGWQ3+pwovzFN0LajE/OJAlROqsMoZAqNAiEAvsQ2VKlUjjD8nR4iDWYxEQbKuBZNkoChAOicLdXeRxk="}]}, "engines": {"node": ">= 0.10"}}, "0.6.2": {"name": "rechoir", "version": "0.6.2", "dependencies": {"resolve": "^1.1.6"}, "devDependencies": {"babel": "^5.4.3", "chai": "^2.3.0", "coco": "^0.9.1", "coffee-script": "^1.9.2", "earlgrey": "0.0.9", "iced-coffee-script": "^1.8.0-d", "interpret": "^0.6.1", "json5": "^0.4.0", "livescript": "^1.4.0", "mocha": "^2.2.5", "node-jsx": "^0.13.3", "require-csv": "0.0.1", "require-ini": "0.0.1", "require-uncached": "^1.0.2", "require-xml": "0.0.1", "require-yaml": "0.0.1", "rimraf": "^2.3.4", "semver": "^4.3.4", "sinon": "^1.14.1", "toml-require": "^1.0.1", "typescript-register": "^1.1.0"}, "dist": {"shasum": "85204b54dba82d5742e28c96756ef43af50e3384", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOy3x4667cKBHl2LvSZnAKE3S/GfaSn4YwYvSsVf5vmwIhAI/nUwNifyygMhhRRAkPYmrSEm8yGetjJAJIw8d4o71p"}]}, "engines": {"node": ">= 0.10"}}, "0.7.0": {"name": "rechoir", "version": "0.7.0", "dependencies": {"resolve": "^1.9.0"}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-ADsDEH2bvbjltXEP+hTIAmeFekTFK0V2BTxMkok6qILyAJEXV0AFfoWcAq4yfll5VdIMd/RVXq0lR+wQi5ZU3Q==", "shasum": "32650fd52c21ab252aa5d65b19310441c7e03aca", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.7.0.tgz", "fileCount": 7, "unpackedSize": 7911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcL8b+CRA9TVsSAnZWagAA9KkP/jXej4OXTvCT5tobuOlT\nJWIwu/ybmiXA+0e8gKF9+hrKYKBbwzb2I8k0CL4n7tmgTDmhGcgBEHNpzkMu\nEbX4E2k9DgBkrkH09IlK6knBeGV12U03tSYN4TpiOUAcPnmsdSTXdRQCZgHJ\npAFRG4432ufk9VP9WKCiov69gys5UwynKDI5Bq8Yt9cP3TCVnhm5Swj3mdEh\nJahlKULuW4zsly0WDE0ZxxEYOEaFxcwtR3J4Z+K5TlFZpL+R+PG/OzYLqV9b\nnrP2ZUm2F3gHVXoQEFmFvtVo7hIgM06U+/Buzwx45iX1C4czC11PRtzneK1Y\nMgShfTLWp+/baye5m6EkpcS2iWHx9NomiZaH84IpHfOiCwoVbZC87gBCj2Qv\n5YKtV4a62L93C9s4+MyhJADqspVzoXCvO+vjBUpM7EJu6x5ar2SZghjfff6I\ntK65FwQahk2768IFC8jvtqxThHmPaIGIh8PzO3JjUy91h78i4Fd9DCsD80Fv\nyPI7p1YSqvsdDnShjmckVt2DO5KIEuctTGJAydQ5JViJ+S1pWYM+MCtjrBJW\nff00HzHm5d35zhJj9Y1LVQSVUE3j2Gd+kmIdcI/sTu9RowGl+pTUHCn2y2Xq\n0sPzH6LWWRwYmAOAZ9SAP5kdECJPAujGtj76++dGKpzojGoTRGtwj05adFfY\nlXAz\r\n=dNAG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRYQFP0Wmz93RcC2jlE0obPDcZc7ANiAfocjdeZXJS9QIgTd0Tkuwx20L0dSryxuRDc2umCNLO7XCAzItT4TzPThc="}]}, "engines": {"node": ">= 0.10"}}, "0.7.1": {"name": "rechoir", "version": "0.7.1", "dependencies": {"resolve": "^1.9.0"}, "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "shasum": "9478a96a1ca135b5e88fc027f03ee92d6c645686", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.7.1.tgz", "fileCount": 7, "unpackedSize": 8936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9mktCRA9TVsSAnZWagAAtFgP+gLgv667+e02xlFoqOV2\nVDaA5CeUcaCufirmkZnjBwWbAJzQP6a8TkHCbb/Vww0V9kjFSCOLK2jLRvmt\n9qf/G7qMq6f6UPJucgJ7+F3KaXQQ4mo9DsmXNhyPC/+CTltjdeic+6opBCh4\nzz4Ac//lnkWebRMg18v4FhLUBbzx7XgNiUYy+19YBM+CZyQkLJlIPm56lcm1\njqU+bwEnaSqolWcl5ZOctAQ/OvwRGaoOw6O1Qv9cEmW5s9ebeCH/dqbUV1fM\nUmJ8uLiObWt2ro9WwM3Y+9VZ3QTj8idlXmEi2bfoxA2Bs3IIG4YZnMjCdT9k\n2AtkAZ7J72rIuB2e4O+6tiJPk78tH/Vv4uiWmwQGwRAVLNX2OytYSacWR9p9\nlaU7pC+uNaLectme1fTCImrB9TF3knPazRF9ECcnrt8abSVbTAeyI+aySag3\ndKhesbQXD7hF2fS55Qo8xkJheo5F9KeNEvNPWDC4Xb5UVBZ76f4BnZZJEzjk\nKWAXs885GdwGQknUN6HdywrRI0/NbspsbOT/2kxqgu8RcDR6uqiSSnLpB23j\nr2nq9JY20ryagce8KURTSzK8ZEJ4rQ96vvqKVM4lFTsBNmRHfBAWdqN7rxD/\nK4OPOO375lEnTrWlBb5cpYuKthF+ZhbIwfxOelR7/C4eXbQR5KoqmSlgF0cC\nxHQj\r\n=UZa4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnNXQJ6AZ77blkxxwtT29FPk7xKayg8Xua396mtPTDbgIgD8kdYhYrBi3OOfCZqjR4LqUBENi4dgUdrzEBGaVC7fQ="}]}, "engines": {"node": ">= 0.10"}}, "0.8.0": {"name": "rechoir", "version": "0.8.0", "dependencies": {"resolve": "^1.20.0"}, "devDependencies": {"eslint": "^7.21.0", "eslint-config-gulp": "^5.0.1", "expect": "^27.0.0", "mocha": "^8.3.0", "nyc": "^15.1.0"}, "dist": {"integrity": "sha512-/vxpCXddiX8NGfGO/mTafwjq4aFa/71pvamip0++IQk3zG8cbCj0fifNPrjjF1XMXUne91jL9OoxmdykoEtifQ==", "shasum": "49f866e0d32146142da3ad8f0eff352b3215ff22", "tarball": "https://registry.npmjs.org/rechoir/-/rechoir-0.8.0.tgz", "fileCount": 8, "unpackedSize": 8639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh25bvCRA9TVsSAnZWagAAoB4P/0XNYtZ5Kshx3wUPJjCq\nZ5LxJrLGP3j37WDRraccdcMswf9h3jxWpld/q4xWzyVU1PkFA+JAmlGxY9Dg\nbeyKLYFnEqzqh+D793iL202dkmcMyYF2sRg3iuLscq20swP+i1KT/l9Qhojm\noQ4RGC259N2qa/hdHbJ0dMvzCykpkOZB5E4lgx5COdCxnV+qcJby4j2JPZeC\naaBS307koFTPkndg29Sz/EtmFGCr855Z86BagGaLiR5vtnIftLeY+MGgMwpo\nmjYeJMLhR/5bpA9WChdcDrTsEcoCRAbQFVG2miMJGAQLK0iEKi6AoISeyF+N\nUuxGFpy71thS+GJpBOBFrEN45ZzotYfxr0rhD474hpMkvEgTCcnP+xZ7T18E\nTi/8yhjrQBEB2ghMCthlDpsfkpvTnNKwqWBsNEnCvQEnH72IpAuOHdJlcSWn\nBLkQ+ndcM76+k+Jm1k+OFFvNSyvlGO4q5GrDd4nirFfREwftijx0zbzp4qpk\naeqFj9dLkNROFztlsMP+AUfdYby61pLTJE6WVgyd5jWppa2GXVfvI341GgYu\ndGpvGveLZnjswQUcBTf7Uvox2RH0IB6boAitwrNlNT7q6A/voWmyYphgO9zB\nyA8ALvikIaJj652GvadjhhCDD0Wevrx+lbS4rCi7bW+Hr3snQM97aFRvSV9v\nHn07\r\n=p+0N\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqhH6REuG+9mcpZCn1feoOlSglb9FN1/QTRyIvEg7swAIhAOcTDWfDCrnPxyCmGmPOZwHJUOg3qreCGyc2OfOjAlSR"}]}, "engines": {"node": ">= 10.13.0"}}}, "modified": "2024-04-04T23:53:42.146Z"}