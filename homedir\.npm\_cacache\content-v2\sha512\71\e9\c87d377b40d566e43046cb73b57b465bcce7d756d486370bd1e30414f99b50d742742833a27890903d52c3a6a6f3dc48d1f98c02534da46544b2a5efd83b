{"name": "fast-uri", "dist-tags": {"latest": "3.0.6"}, "versions": {"0.0.1": {"name": "fast-uri", "version": "0.0.1", "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "dist": {"shasum": "8ae168f4f9566f98b6fdc58924da4702f2a697eb", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-0.0.1.tgz", "fileCount": 22, "integrity": "sha512-XX/gBR9Xge9sDBWPcweAMhr0CWrEm8S+GP3fVSX//SrkaKh0Wrll0pvpvKKHJ4FG6CRkuQbVb5UomG1qICGCow==", "signatures": [{"sig": "MEUCIQCIaDrfyW08CkCo2FFDJWLgqcDxVGJhIja7LEJ76ZzaZQIgCmwIZYWNuvPAKwS2AkTfuR5DUI0Da7f+eLoynh3kie8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtdH8CRA9TVsSAnZWagAAGs4QAJzeH0Kwuh0pqJ2vVpKF\nFbceAjLhse/jZMBuNh1EeN1jOS7eJUc72EWMh1W40liH4uNhw2YRVSc02Wo4\nHXcSgxAu4QYj3TS8uiVjwAE2jd7yicqPqHqpVsNs8h9p62S2O8g+i2+MBcdm\nyR5Hfg0R3lnvKlGj0IZ4COcmGjM7IprmdhqGuPnsJakYcJITImHJ3UG30AQX\nQauo4CulpbxKzbeZ1TCAzyCgJdQBIY55mVkQmtLeOnwXYiaTdxX7OtfGtxwk\nJ3SWwfReIEve2PC/CpjY9Z7h7sNB0CgDdhvixRQgqLxlwwZdVAC0zyYrICSi\nJ1zGPQCN/D94iKmkzXty/n5MuC96R60C0YVH/pfn+hl/rFxnow5uRdvM9Ykh\nijUgEtRH5FkweoDRRry417PCfifoo/Bax4lpzxcy74B32QNeqy1grTp3Axc9\nHWk85dqKAxb2Z0/FXjqzIZjpTm1+ffMPQ0PggfCPrsgrsSAQ2VRMLc+NndNm\nUfIX0qeqsP98TOeBYA3zsnuMUviHT8NNboRgZaVaEigPEZEs+FSagya+FFZH\nHZSweyUgTlB8lcw5WxXpKQve5W6O62EDXgDGtMk/EOTbTdv1nQx5+85oQm7T\nrXIf5jZBvZHowDbVXy+uqV0vBDe6tckSHTdtegDoPOL397Yf4iIq9N55E0t2\nEPiD\r\n=r1rA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "fast-uri", "version": "0.0.2", "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "dist": {"shasum": "3bf622e7f65db1ca4c068ea227e847f67556ea1a", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-0.0.2.tgz", "fileCount": 22, "integrity": "sha512-JmJ7NLpJjIVaC8P03wRHa6SY3NN5/k46iJ6ZRCVN3/crbueGrqqVIbeJ0lxQyzTNaFA+nWmazmtRwRPBuLNJ/Q==", "signatures": [{"sig": "MEQCIAm2XN8L3IY1gnjqIk12VKmCSd6PqyS4s5nlPyt7Bo9RAiBYxd6HBAlqD5zXCpXS+oqy432OirK3Fvkgx3jjoXSflA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6nX3CRA9TVsSAnZWagAABZcP/jMIY4EPahkcAUn2SXW4\n40SbmpWzHeNO4PK/2iHswST6yJywr4dK4EggWi1Ot4oia4ikjWeSLekIJsIV\niMfl3BbnoXk5yyFmD7qPxfpCyBIVWOtOZO8eHtj1GptxJhM4SynTxijZHqtm\nga4fkmjp4LguNFew44xM4EfATL/OWwy+tqjgJwCRNA2k3xygizN3dEOw/m2m\njHRYGowQmkVE73jdShDyoxSpc7dsW4/pxAyag4EcogwdM5aUVrdiGDDNhdfV\nB2rsWzdyM+EVMFNG0/V3zQbEUdge3A+dtD1KO+WEf7+RR/lc9VVd1z2zpojD\nKzgbfztFhbeQOG1CWEltuw8L9sN+ORuubP5vvPERFuNREgGI8XuLazks54iL\nA67FmyCQJDSiQZgG3X1mSyEXWAOZhs/2UzhvOK9yVSNpaBQL6lgFSb5YXDLP\nJ7nyXa74mzKdXlkou90MxF2z/R7So3ZbWXEZuxx2byu87uUlrtSOa0m4otKN\nVxalI6apMLO4y9zPhfOYlUEAqSE0lzdVR70fIGmP405XYQ1bq+/3sNlvfH45\nyPHB2XXpuys+xCpyNXJqrsg0QH+wDIUeOCNlVVpgmQvvSL7xfu6NiKd7RoEE\nZR1p+lPwsw6ZIJiJfFVgiraOaqvh1hTY09wj+NLprJ/r6P83UBOKF3rLxLQ7\n5LEE\r\n=7cmL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "fast-uri", "version": "1.0.0", "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "dist": {"shasum": "aeb77c699b79d6277e337e4b3cc3907f18ef441e", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-1.0.0.tgz", "fileCount": 22, "integrity": "sha512-uAHuqh4OjEeKj2VEZ+f4lv9nu2dae2qV0pbOI2S1ZiIaQImlfDf5GfxDLc+f0v2VgIiTwvdMfssGw5vblK6RTQ==", "signatures": [{"sig": "MEQCIEP3UXUqiKX7XiY30YtlQ67H59WosYi8MVgyoKmvvr8sAiBXNz10C1tzPsPKBKeLvBZAU73BYAF2Egimg+RxIBzYpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6upTCRA9TVsSAnZWagAApd0P/3A3MoE6txGTwR3axSWY\nek4QmPnIwieVL41yaEpDjGdPiJ6/FceStl5lmR5QBv3HI2j4MwCXTGVCVU3g\n40yzQZ677zoLeKFKNzoBjds1pbLpoK2TiT1saBqjjmd6kMbop/3YDgCmjsu9\ntCppyTtmq0Ac1ngEHr9c7injrpYppZBwZJGlUXlJo96Sb1fMrIpRsjkp1Q8w\nqrpkr7c8YOuBKkSPI1X+PQMlQTxqgHF0tEQehsR8rjPDLKzuBq3k9opPFxGZ\ny3FNM9JAOdEqGvXwKlBfbvQXxa2NQ4zE7bOp2Dv3NPEgM/GoUpjiXujQIvee\nV/GoQkVwFKHhbGrf1zvQPQEZ3kFIxd1GEVxHiconY7thePED2LryfHIPkIQK\nO4UUbLLBdWh+fGtC42CJm5EkhOeLX9tE2Jj4w30vJUSwigByeHjoggJVzwLN\nqJRXN42eQ3VUIfbU2YDDYn9y/UipqdHJUrN5jvE3X72q267jW6gjq36RtPio\nz7JR1cZnelp6BLWkQhtgLBMDm0qQnGUvre7prczJGOx8vcx30ik3iIpMNafD\nyIxfcZqUQvkhnK7qmhFBq6kqx7TaeJ2N4zdAwp8PLqxc/buxRYvBWrD4OXUq\n0nffLya1Ch3rgNlxX3ER0KTpOQwDyuj79jL2mEglFWJVp2crbGAfCAQaFwQN\n/EnQ\r\n=3ChO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "fast-uri", "version": "1.0.1", "devDependencies": {"tap": "^15.0.10", "tsd": "^0.19.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^16.0.4", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "dist": {"shasum": "dd637f093bccf17ebea58a70c178ee8a70b5aa45", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-1.0.1.tgz", "fileCount": 22, "integrity": "sha512-dbO/+ny6lX4tt7pvfPMTiHfQVR5igYKFa5BJ2a21TWuOgd2ySp5DYswsEGuMcJZLL3/eJ/MQJ5KNcXyNUvDt8w==", "signatures": [{"sig": "MEUCIQC2GmQgiwVXyPK8lXSWxe5xPT7/aK0HOW4m13DB5NYvJwIgXfKtH3VxrzoVJa0k0zr7KSGV0v2jYJTFRHx0Cw3h2t0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/m5OCRA9TVsSAnZWagAAnEsP/j+dHd1FnydKVdYexzmD\ngTFhp2NMtGmsF1UTf8R2fh+lrpICHCiui1jxSq/zzQp50b0CFgdyuICiXSfy\nSX/hQi+LQJPfoOOV/T/qLkEHFDdwQMzxHieBEiMyo3Hlcn6qukinlOnNA86m\nV1K18WFza3GRlsJ9WwzxtpWr7x4YSqpbYf4DY+9UZe4d929wEl7zmo0kuL/m\nLJVts7NpYrKk8OnNGXCNhcjRK9XkSHLEQqvP51nvWuR9wiXacFv1qEv66Bsv\nnBgI4/ek+FpkGVllXYAhif1YNTAG3DRdZ7zRXI7hKRS6pq2LWpNk5tj8RyE5\nB/sPFi49jRAoj5LsrAPbOZxmwqtu6DnYQN5Yz7jDE69AEWhxFlG4G1OJx9iw\n0WUOi1cUJxlad4QFyjfdHjQEe15Qq2UBJuQ51jAgQzdx1cUKO7Rf9dyeTPA9\nIiJrmuNLzuZqJt4uTCmddqEAtFp2y24StErrNVK92wKBpH7kFCy6Eq77odf6\nRG+iDjJKmFw+iIWJT6MzdjpK1SJyqjjsJ36jk0e+zZobBbiUBuFR3GGg2b69\nll1jPHR5G0YxPUmNOZZHaghE9l/Eig5hFYKv/aN8mdMBxyoHMdmBn1P+PBZe\n0KHs/RmQZG1mLmkwlBf31wQjis3g4yhRPdxxHAbc0ocabMv6vOaE7V+52m6S\nmvhU\r\n=0eVb\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "fast-uri", "version": "2.0.0", "devDependencies": {"tap": "^16.0.0", "tsd": "^0.20.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "dist": {"shasum": "a0c1fcbfe769cb023f9d9808798103847de66036", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.0.0.tgz", "fileCount": 22, "integrity": "sha512-Mt/lDpFwcPcLv3G0Qd3WlIjP5AeCQo/X91TZ9G8Px/3GzkTADX5smwginXYz+iX8mSa5pBN98EObj05X39/JoA==", "signatures": [{"sig": "MEUCIQCBZ+EVd5LT0BCzalgfFuqrQBHJWkccYf5voCkuJIVt/wIgNiEMAKMl5JhILxlAtTYEjzCKQ+Es0ou10RexPfA/Lik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijgllACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1jg/+OoIs7yD3ot/UFisU8lPYhrd9aGnifBtOkWDTDaOWu0JyQWZL\r\ni3Jb+fvimKGHZOk/Y+hcKPt2DPjdF0Rzhk0Wj9HGOFW3N9sa/QKlbTxF2XSI\r\ngl9+tGbwycdgVeFhx6nNFdZxAFJj57fPTCXuig9k9OcytqXD6jyX3a2n54pX\r\nyM1PmzcrT36sbmaXviqtiSVpNiIFTp1jc+mDCuxc9f80+ajfDTY3AFImXtO5\r\n2LiU0LWy5DUf7k6gH0j9yVe7rHsYe0alEmuWRWvmWKo32jm7hRnsXdZ4HTyy\r\npDMv+HbKOnCuWX8EDuHHC/TqcXg6L95cDnSG9EwNYpi6Yxr50n1X6ekDwEUk\r\nJ+zXzrBp9kxepSeqektEuwyOUod1y3Vd1E/HLjPk7YaE0hIAn7RzLwjjJ0Mg\r\nGwV9ovrV7xqwYdhLH6J2Xj6nE28YB3V8R+0SQB3SDSp8fBROD1vdIwtQSGz9\r\n6u0zWQwoQ7u7kcEIhTuHa3rcSxlTW1oqEPUaBZkUPumn7tnCCc99FOsMDKYT\r\nalzLjA44Ox+NoMRXpW0rJorD6w8bY8Ne/B7aFxxPvDTrd8jC86m0mPVTEaHz\r\n5eHvpobpL76ZaZVn5ubc4XcfBFLC40qe1nXDACQookDT+BnK505jzKgx8ufp\r\nMuRuADr7X+BsTiMGzRdIVeUDn+KU/gYyj60=\r\n=i449\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "fast-uri", "version": "2.1.0", "devDependencies": {"tap": "^16.0.0", "tsd": "^0.20.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "typescript": "^4.4.4"}, "dist": {"shasum": "9279432d6b53675c90116b947ed2bbba582d6fb5", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.1.0.tgz", "fileCount": 22, "integrity": "sha512-qKRta6N7BWEFVlyonVY/V+BMLgFqktCUV0QjT259ekAIlbVrMaFnFLxJ4s/JPl4tou56S1BzPufI60bLe29fHA==", "signatures": [{"sig": "MEYCIQCiwZ9Sw4Nfc3PnH1NwgKCypdQIsuOqmgde0JuawWZV0AIhAPKWT+Vj3wMXzv543hsDE9VwU/v9yUrfQKhGkJhUeL82", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio2qVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqznA/9Fmv/IoSnRQrmKhF7gNH8sU8F+DZc8n9L9dkgD97CX5L0d7v/\r\n5u38n7zj7WGWEEEISdycGtM5yIXzASBreHvmUYSUfNPmQVX5/Ea3bRkTYU/o\r\nN6RRdhW1kzDdvb1rkR1y3VFvjBCObG9ewjOGBV6GndR1VEDOww8xDS7ZOIXe\r\nMPycs2LtzUiJcND9SXeFlP6COaFY6kePg4vjfXTKFdt3S3Z4dDVilk5Haw0n\r\n38TLQZwwZklddr/KhcK+TIK+O9ic6B6nG3S+Q2Ju9myFaB3CO50HWx1jJNKc\r\nE7NUd71IaYN8KpO+ewH99PFrlWmM0y/X2z31JtmaMsHmm4DehpyPwSJgjs8F\r\nMX9jS4XulZkYRCK1405du89Qfs3vVImGhgqLGVCbD/Zi8908iJ5kA0KSk9a3\r\nHO9b9Mip26sR28OSFzqir8Eu/XLCS4gPc8nhfXRSbLK29N3zKR/1xWMsYFxm\r\n3Koi5DVsKoRniBmDtG8SyjqzG1mzlPlRo/O0NwTYDLlgxV3be1mLvRsG4nQY\r\n+r6xV6fywaM4pqNo2yY8Nni8F7Rbmyc4rTaKwcHW5O+GakuaJqDo31ES5Ebd\r\nEGg0RDegvld+1MbkTub2BdjvZgRZ8SIKKeHpI0yUhI9gRChKAnh2fRrwkzPY\r\nYDjRCx+eowX+l3TDH5MJNyJsayzdhhY9A6I=\r\n=kLMt\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "fast-uri", "version": "2.2.0", "devDependencies": {"tap": "^16.0.0", "tsd": "^0.24.1", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "dist": {"shasum": "519a0f849bef714aad10e9753d69d8f758f7445a", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.2.0.tgz", "fileCount": 21, "integrity": "sha512-cIusKBIt/R/oI6z/1nyfe2FvGKVTohVRfvkOhvx0nCEW+xf5NoCXjAHcWp93uOUBchzYcsvPlrapAdX1uW+YGg==", "signatures": [{"sig": "MEYCIQD7I5D4lx+4ng7XoQHji0F8OLPSJcqNfZp+7eDaSzd7ugIhAMNZWQkhKEVwlFhNoy4zC35GtWUzHFHixthk20yOTvmU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjbmDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolChAAoJpxckv8mcJiFw5NyAzfJ4gz39lgmMMtsW5IT7hcoTK+xlCl\r\ndDul68JTTa/VS4CMstkcwUgf0oG1E6TZJHWrS9PJ5Sq9TFlevkTxv3R1fPxh\r\nCA/eKeaY4VajnGDET058TkJoePYDWF/qKjGrt6WTzj0z7dreGYmoLruoA5Oq\r\nA5F/A0MpbSq6fv5J57Xph84wTIG4ZqWOrgrAsKIhgFaHHQKRA05QeBoMfflI\r\nucEQ94TLr9WozenJrahTu+spzcLNZ3zJzJcoRsX8ZYsXswZ+EVGBqO5l9jha\r\nWHzdoz2z/ELjkNve02veQ5zrXbsUJMMSZ1tP1WM1+HjhOkTMZqJYdhxedtMw\r\n7WYm3YUonRHelQWgn0ktWOBgZsRtwGf8XAbXt33OM7m9hJANDAhLI9aeRtni\r\nm+4qGt0tTqA9Yob8w+i8zPUDs+3NDCPxK4+FCY4S77uqLcrmklWCK+7DHs7A\r\nWBUVquNgYMsUIdvy4ernNWkA5aat0mdWmiYwUUmVUgb+Ux5Tgo5ilfne4dD3\r\nZdEt1W7B26rPkKAFRsF46y9gpU6nDMRyqo9WSNlc7JfjKM3t2QCB+r3bIGh/\r\nmWAGNdIvZKSDNEMTqkLtk1+PCIH5DGO1fNkQlWBDIny35MO/SDqwC1bgSveG\r\nEW19PXIAYWr7wP1Yqxj8vioaO7I/dWHxfpg=\r\n=8xTf\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "fast-uri", "version": "2.3.0", "devDependencies": {"tap": "^16.0.0", "tsd": "^0.29.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "dist": {"shasum": "bdae493942483d299e7285dcb4627767d42e2793", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.3.0.tgz", "fileCount": 22, "integrity": "sha512-eel5UKGn369gGEWOqBShmFJWfq/xSJvsgDzgLYC845GneayWvXBf0lJCBn5qTABfewy1ZDPoaR5OZCP+kssfuw==", "signatures": [{"sig": "MEUCIE0Z1NpMnzJRcDXMbg9ETRkaEpbViuXj8leQNvdV5I5FAiEA50THSXxysL4xVOvlXwNkAFOCYMAjyAoyXBTaxHTYGww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64395}}, "2.3.1": {"name": "fast-uri", "version": "2.3.1", "devDependencies": {"tap": "^16.0.0", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "dist": {"shasum": "14af6294f8d5b7b10703351ca63590686251a6bf", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.3.1.tgz", "fileCount": 23, "integrity": "sha512-iC7SLdMJx2KGdBkhJ6UulbNfpeIGTMS3/OIJpPa1JkZu9DKVQmPtBBme9Esoa4XP2eLGaHBv4vzRqlolXKo9cg==", "signatures": [{"sig": "MEQCIDw9hrCHHvLDLGcGQTf4UFaXX0no14cKYe8qONIxbjEcAiAt66+tTrk+/C/dFEFRnAuRH3yjoUrZDM5u5LfOLoRTyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64327}}, "2.4.0": {"name": "fast-uri", "version": "2.4.0", "devDependencies": {"tap": "^16.0.0", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.0.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1"}, "dist": {"shasum": "67eae6fbbe9f25339d5d3f4c4234787b65d7d55e", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-2.4.0.tgz", "fileCount": 23, "integrity": "sha512-ypuAmmMKInk5q7XcepxlnUWDLWv4GFtaJqAzWKqn62IpQ3pejtr5dTVbt3vwqVaMKmkNR55sTT+CqUKIaT21BA==", "signatures": [{"sig": "MEUCIQCfJuTv2Elb2h4e8bmTZu/z4+DxeOlJypJ0iL5QX8ignAIgUh9g0KKZ0Ia5x9ZznbYDE0AhG9xXVmfNsVuw8ZUNW18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64382}}, "3.0.0": {"name": "fast-uri", "version": "3.0.0", "devDependencies": {"tap": "^18.7.2", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "dist": {"shasum": "f5ba3f3b26a06d54ebccde911b432391c63be251", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.0.tgz", "fileCount": 25, "integrity": "sha512-zv72gAcgHJjkq7h2IHzRB1Hg+9QIy9+IHmy2wBlmZnG1T7CfdfSjLXotI0vhq4T4Fqg31zkg+Dp2Dj8iFM6ULw==", "signatures": [{"sig": "MEYCIQCdoNWWl8rPkniFa8fUsRx3tL2G0IsrVd0azcJhxrG+wAIhALyct/5LuFSTAtgb3rof5cKL7L5TnDCQpUuUxXc7vWsF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110398}}, "3.0.1": {"name": "fast-uri", "version": "3.0.1", "devDependencies": {"ajv": "^8.16.0", "tap": "^18.7.2", "tsd": "^0.31.0", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "dist": {"shasum": "cddd2eecfc83a71c1be2cc2ef2061331be8a7134", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.1.tgz", "fileCount": 26, "integrity": "sha512-MWipKbbYiYI0UC7cl8m/i/IWTqfC8YXsqjzybjddLsFjStroQzsHXkc73JutMvBiXmOvapk+axIl79ig5t55Bw==", "signatures": [{"sig": "MEQCIBOyEr5e7zsbHtnYXO4uBzgXaozwrsWICLMUYZ+rHM5BAiAHB02RDF9VbEQA6m1T/oATTkaogmkfh0tSoctbVpstfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111169}}, "3.0.2": {"name": "fast-uri", "version": "3.0.2", "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "dist": {"shasum": "d78b298cf70fd3b752fd951175a3da6a7b48f024", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.2.tgz", "fileCount": 25, "integrity": "sha512-GR6f0hD7XXyNJa25Tb9BuIdN0tdr+0BMi6/CJPH3wJO1JjNG3n/VsSw38AwRdKZABm8lGbPfakLRkYzx2V9row==", "signatures": [{"sig": "MEUCIQCBkEMlIhNt5F6TOYXk4ioiNAe2Q+SjKc+D8Z/w4yE/7AIgM/+FvZzHcQ9/xLbM793/efVF2jiEtfziWX2Z6OpbMyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107789}}, "3.0.3": {"name": "fast-uri", "version": "3.0.3", "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "snazzy": "^9.0.0", "uri-js": "^4.4.1", "standard": "^17.1.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "@fastify/pre-commit": "^2.1.0"}, "dist": {"shasum": "892a1c91802d5d7860de728f18608a0573142241", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.3.tgz", "fileCount": 25, "integrity": "sha512-aLrHthzCjH5He4Z2H9YZ+v6Ujb9ocRuW6ZzkJQOrTxleEijANq4v1TsaPaVG1PZcuurEzrLcWRyYBYXD5cEiaw==", "signatures": [{"sig": "MEUCIH8+CKmNr+yvZKf7xDUIbmfnNYtP2abWs9FqPuhb06FNAiEAr9N3VC6YaN+l8J2kPB/ftSDEAMlQ8TJKl3QVjSTE1ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107798}}, "3.0.4": {"name": "fast-uri", "version": "3.0.4", "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "eslint": "^9.17.0", "uri-js": "^4.4.1", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "neostandard": "^0.12.0", "@fastify/pre-commit": "^2.1.0"}, "dist": {"shasum": "bf2973f18465da231ef4b1e43a188c3bf580cf98", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.4.tgz", "fileCount": 26, "integrity": "sha512-G3iTQw1DizJQ5eEqj1CbFCWhq+pzum7qepkxU7rS1FGZDqjYKcrguo9XDRbV7EgPnn8CgaPigTq+NEjyioeYZQ==", "signatures": [{"sig": "MEUCIFAi6jQT2z2fe4YBrnHTLXH84uttR1e8ZxlDP9VuSANMAiEAmlmKXPljKM83Mu3QYJRhXPW2TgLgxxVBRrJgrREpdB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108451}, "funding": [{"url": "https://github.com/sponsors/fastify", "type": "github"}, {"url": "https://opencollective.com/fastify", "type": "opencollective"}]}, "3.0.5": {"name": "fast-uri", "version": "3.0.5", "devDependencies": {"ajv": "^8.16.0", "tsd": "^0.31.0", "tape": "^5.8.1", "eslint": "^9.17.0", "uri-js": "^4.4.1", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "neostandard": "^0.12.0", "@fastify/pre-commit": "^2.1.0"}, "dist": {"shasum": "19f5f9691d0dab9b85861a7bb5d98fca961da9cd", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.5.tgz", "fileCount": 26, "integrity": "sha512-5JnBCWpFlMo0a3ciDy/JckMzzv1U9coZrIhedq+HXxxUfDTAiS0LA8OKVao4G9BxmCVck/jtA5r3KAtRWEyD8Q==", "signatures": [{"sig": "MEYCIQDN7TLkILG+9B/KSXlIdsuzom7uJhjxy1e1TTcNwUO78wIhAM4j5sTCbu8MrUA91Sm4lUpxb+jxB+Pux9pRv+CAKiPo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109457}, "funding": [{"url": "https://github.com/sponsors/fastify", "type": "github"}, {"url": "https://opencollective.com/fastify", "type": "opencollective"}]}, "3.0.6": {"name": "fast-uri", "version": "3.0.6", "devDependencies": {"@fastify/pre-commit": "^2.1.0", "ajv": "^8.16.0", "benchmark": "^2.1.4", "coveralls": "^3.1.1", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tape": "^5.8.1", "tsd": "^0.31.0", "uri-js": "^4.4.1"}, "dist": {"integrity": "sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==", "shasum": "88f130b77cfaea2378d56bf970dea21257a68748", "tarball": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz", "fileCount": 26, "unpackedSize": 109402, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeqT0S5ftTVvf+XUGRkNorGlmiKgZ/4MR7JWKnPxiWrgIgNedrRYDKiH2vb4SzcgTvchhVoEQpcHNsHb8g5Gqet/0="}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}]}}, "modified": "2025-01-20T20:22:54.876Z"}