{"_id": "lightningcss-darwin-x64", "_rev": "37-2ff6808bfa2f79dd02fb5b08ede392ed", "name": "lightningcss-darwin-x64", "dist-tags": {"latest": "1.30.1"}, "versions": {"1.14.0": {"name": "lightningcss-darwin-x64", "version": "1.14.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2d4796ab81790bcce8266fb3a84f85e7c414b510", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.14.0.tgz", "fileCount": 3, "integrity": "sha512-xF9YSNxHiAOyYp5/Ogo4K+SybcQExWUi+vkIGnpO6zQsQda7KFgJt2aA3AwaEj9ouf2yUHHKcsk2TiixlZTtTg==", "signatures": [{"sig": "MEUCICluRpti6Ra42aIDEmSP/1J8tRq1q9NDDBxayozpfVj4AiEAwOHlVa9PuLb103xh1FsVcmEwu0RPTNrJATwHYkEsOnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3580254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxiA//YcgLXNc3GL8CqGvvw5STON6oi8ckj1eYw8FiES1m91E0s8+M\r\nEE4Ez8KwRZS67yQ9+kbBTSQH5jE5UY20xCCoPEIzviSql8uepDd/IF8GhPkx\r\nKQ2hO4D6FYYiV4uK6p609UP4wpeP9VVtT/Nc0Irpmn1iwI/sWeHkKcio26Kx\r\nBfBNhZC2dR3ERFdy/YqLNsr+vC4JyOjUB9+yBJDgPVBeXFyuE7oCkYvTEU2+\r\nahG708ySBWVq8DRqDBl1hyNjWKKWQ56g30PPqFqujG3nbVC8BZ2jk5XdZQjN\r\nKTBigRFXEFxEs2JQRBTn3WW8WRRFpFhIH9NG5rFcSa72LdPKgu3xHKF3KbIP\r\nA6XVtGoZHNRMnO0Vky4nZ+CsxoaLyrrSPM+o5UanBx202BA42riiaZinNCNO\r\nUEeduUbTx7HEAKfCuGTneOgaMl7dcvgPUTO2NjPEcHXgJHke7VyoGHGkE2Vp\r\nrEYdbV+Gp9hNgX6buxDZCP2ok8gpLRv/OMyN73RmSfni+8sjhO7h5xYMrN8N\r\nOv3RhQdL9CLQK+K40N3dzYqwz6jE1ATs/2YJRsvtm1vuIYWRVbRCRs+/qgG9\r\nqL+HSbcFVtxc9GfzGg+GgtSOnPgK+lZA8N6duYKZhlTzemFlq40PkPsZ0fA/\r\nwrp3QHFvygbb3APvbDK4ybIRnox9DbFkfTc=\r\n=QPMp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "2d17e0e1ed1482525985ef4d30809b4c06d76944", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.14.0_1662614344288_0.9369841920955015", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "lightningcss-darwin-x64", "version": "1.15.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "509cb0189b7db4d7ea4c3fb710ba79192c17fc9b", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.15.0.tgz", "fileCount": 3, "integrity": "sha512-mWYc0xwBL+PDw6AZsgp8ScwyYgh89rgO7JAcbhxAUbFKSGPV6ymseXJVwYEecRbdtmfKxZrL1S1fE+609mRQWA==", "signatures": [{"sig": "MEUCIC//AkQCOy8nV/Wg7IGYByb9g2turnpwKwBIWba8yimAAiEA5XWGM1RZs1A7tDr/geoTOcd6ILXiHEbb1Xn4g9qsjxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3613125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofqA//d+rNfjF6pFjLynKhINuKbfxVRYBKRgI22qxyaYDeDil8el4L\r\nhX9XP8S8VLV8LO0By2f3Q82T8zMKKgAUkUWXSrXUndfPFk8CFj73feyImVLS\r\nW+nTX+R/1T/5A0rqT+Nivf/pMmKmR4zz1K0uBAw0M0HF6YgsKu/E6wbnwuo7\r\nF4mz61M4HhMiFXGvQn2MJ4bHWID0Z8cXj6v3+uG+ZfPvTgaVyDYpUT2K072H\r\nZ8CGXk98Olb0sVojNKz7p6cK866xFE0324f2AH/uwBeuxx9PxKy8lniMh2Si\r\nMRKQiwKaKFyxKo551EC56+lrPdVLUzUD06ETQEtUl3J3mYoJZT6kcX3DPhqb\r\nUb2Zlw2dYa5fWft5p+OZfyC4TOtO9JbWmn1cibeIepMpZbv8Npx3uETLiNmW\r\nIRuNjan9OrPm2EUvMipa9hF8Daah7TI0QrvfW0y3WzPwtHtSyjhsjEZEn6Ej\r\nFE+N9za0EywTa4uQysisVsIAurlGc6x2EQc5Suf3vfZMY5ibY2dGSUk/CLXo\r\nxH3Sf/R/TIfjT3P26EjWcf3g3nkkisQaiTGQ8u158qPMp+N2wgzU6+efGm86\r\n3M2FtGLQ+mdwW0boBBtMBmDBFBv/HthUkzlBSA0ev43cWZY9EbJTOwjMPGRZ\r\n1tbtLrxAxUfHDZBZgDQMAo/V8zLXWn/uWIg=\r\n=XLtt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "1a23835a0d72afc06ab5f98cbbee5fb03ae09074", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.15.0_1663215384150_0.2064620252313638", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "lightningcss-darwin-x64", "version": "1.15.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "897cd5eb116fe50c7608c3936fbc8277b5e26498", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.15.1.tgz", "fileCount": 3, "integrity": "sha512-1W7kt2Nd0lPFkZ5VzieJfs/ePVADysM3FS33HcUUzktE52vWL2B6S4ntWibHj6Ccg/lDH5o6GiLcCYwpOPLHug==", "signatures": [{"sig": "MEYCIQDZaH17ERwL4MhOv8ZbhoVfjKf+8L0lZNLu0/XZBZ6baQIhAOygM5bU4sNGpx9TOtTqYiYVdad+Zo2tSSq8T7cBu7qW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3613125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/I6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQgw//RRebMFkO0Sv68GY+bgu7GyNch5imT1QmCi8ZlvdG42ZkQOQr\r\nzfxu/K7eFAM7TAv8DYVYPRAVpAqj4GVueeXM3Np3p6tq/9txFuz37MRvZMNt\r\n4zl3WbOTNl6jav1RCKzJMfd+ofmSWfqu14kEzldwYbVSvIT4pjAq2gnJ6Nm3\r\nwIeKB+CgNjjyDzOx3j9prXzvTAXa//chf8U3bM3vXF0G5mwkYx8td2C/hExS\r\n063lSPe72mTxNBAdTpSjvExe+k/9sYgatdSJvTCTiMQ+/jAkaAmqTUTFV79g\r\n1m5BQu/4hqrxpuglWDtbQK7HtPg5DcGXSFmZ+6tP6I2b9s5oDh/AYLqPaK/q\r\nRm7ZJqL2Q0rUFTWtT7WbOi1zEngWFXwDKPU/w0SuHo82oOj+fLezQ3tdmCds\r\nKBNTvAFXxYgo+CNHwjLcXaNSh+S/KhJrYiLiKJa6Ydrl/wruj7VMi7GGjcl2\r\nbxSOJqdYkg2C/6jx3ib19uutdG42m1kv9LiQz4IMCEDg7nfPhlGUcdKkUR/o\r\nrUDtNvQGj2O81EaR1SXphE3gwG0gXYbBYdTuDOdtUdRCLCrnwrKC54qBb9fE\r\n/ibvFrN0eiFTR3amZ8H5fZWTeQfC4GFDhyZB6vbsNRUkdfVsyCnd6XsqMSXx\r\nuwMg24uV3pVvj+NDZ5GC6w17V/0WUZm2t60=\r\n=+Yrf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb66ece2f9b47bf4ef60848736dde697ec80a319", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.15.1_1663300154478_0.5851174398933396", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "lightningcss-darwin-x64", "version": "1.16.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "46361b701b572ce9ec29730624000b439c2184bb", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.16.0.tgz", "fileCount": 3, "integrity": "sha512-kLPi+OEpDj3UGY6DC8TfjbcULJDKMP+TVKSlrEkNGn8t1YRzi2g4oy7UVTSB5AnSbT0CusUItzdVjHQ49EdoNA==", "signatures": [{"sig": "MEUCIQC6nXAT+ncUp1atl6ENmSrpxzpWeqRqmLmJkGm1I3gL0gIgCvRZkuAS+qc3hoHn43O+ABb2t1qdaxR1r5T1NY20Ol0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4598493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmov3A/+MswaX5FpZl9VlLsz+I7xTewsJvIjMnI3IWGurK7dopd+pBHf\r\ngAt/a22zoKST9dCydrLbZytEc4pb5rOpPinXcTII0QftWYrKs+zKR9P5xQPS\r\nft4BnIFEU8MnDwuvQ3I5SUg7k9ykenz2j8uZdu2Uh0ZDTXCOKshCt4oMZ54T\r\n+uOhn8qvHDnTC8O7w8OjrBv0RvOcThFlioQwrbByYwPxBmMXtB5KWRwOolyE\r\nBCSDqDgkVMAetizQoqcVLsbwQcSjlqX7mNZFQwQM25qz/wUUrEk+4oU2J864\r\nOhfS+tWk1It+S14Ta6gvDXFgIYmGg0Knzu6TwbFGPEghjjV2gkve79A98puY\r\nQ9GaO6pOLMTA2fuME6+yZ+aqHbI+Vc9nxaDWOlKfuhoxfLZnVoC3p4E8cQj4\r\nML0LHSoisJGLdXUZoaI0Lwfd0AHyIyJ/L/Al1FCpIVF5QqYGGajzbsCtYAD/\r\nu7bPMmfhd71kgnBJZGCJ0mIZGHOCKAclu3EslJ9vWppKI9Do0oqHbYSre5Il\r\nGxQ2UHe4gdOkjbhU4duW13AbPlpp8ogEHGIe2AIi9UOo6efH4gsmVhDKfzFA\r\nwLwKGpf+ZgHOsMxzzp1LtJGRuMbavE65hPUJ7JZRkLzB4T+MeB23XcDM/ZKq\r\nFPozJQLuvSwLz1l91Y9/n/Cv4ZA3BrTfJ5E=\r\n=vX72\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "200120e24aa31449703707db14ffef1bf40a7fea", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.16.0_1663644184953_0.4481386666277041", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "lightningcss-darwin-x64", "version": "1.16.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2dc89dd4e1eb3c39ca4abbeca769a276c206b038", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.16.1.tgz", "fileCount": 3, "integrity": "sha512-vyKCNPRNRqke+5i078V+N0GLfMVLEaNcqIcv28hA/vUNRGk/90EDkDB9EndGay0MoPIrC2y0qE3Y74b/OyedqQ==", "signatures": [{"sig": "MEYCIQCGee7xtcPskicsZsRYsh154nBvvSqMNmecFqDjBA2WeQIhAOubdHGyz0zXE7288T+JbAqCFxC8kAOiZlqjBzu+HyZZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/aWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmkQ//UX8SABPiuYFbRWLGFehUDy53HiG7+6LD8T/4EuelC7fEiK0A\r\nzXHIGMVpTZhcEAmFQOtM6pb+pyeBxwd750WFvKour2b/EUAMGhL+kdtJ1CZm\r\nuyzPY2dXfONiNu21jT+1Qyb8uVDWM4hqGFoAfGGvVFmWTDdOOFquplk3JLGV\r\n+US+SGqX9Q6YesvfgQ3F6h3rhkiSH2pBEc8Ypx3iMi8Ycu9/9rMhWiN5OkHc\r\nxF9qfohHlP7Dcbheq5RWTmDIG8pXYfQb4cYLMIFWnplySOxaqSmdYzBiIS73\r\nHd8jqUA8G4uxJCoSeP8bDbJeN9zS5YtkFm+GHR0UF+HkhehiiVcEVdrKcJxI\r\nbwpXReTaRXZY9CPgFvleISI5RCyClZAJd+mPkuW8Zgmez1lI6TYQQvW/ek04\r\nPC+M/VI/Vy3XOkC6nToTL59KFQe7Kyg8kNE13zIHYKccRAGQLgJvsP4k6bBa\r\njDwPnEM/yG6E8oaO/4AJFC6hgE7ZRLJfIVdcVhOW3aFq0w98f2y0WFcGEkA8\r\n+tXp9CJ1zC0JXyVSa93GDgw/kYrXyeSoS1v2VvGfGloCmY1IkJ8CZJQ4SclV\r\nsSFs+s04y2jRXYaVCLm2tiZdzif5s3KuMCOx664lMZXGcrjyU6wjV1j7H5HW\r\n5/hcvYJulZv8UF8C3WQmQO86FXOIqv+YPMg=\r\n=YSVp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "87e808fd771374ae705886351ee62c4fac3d3630", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.16.1_1667757718507_0.9555402158538309", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "lightningcss-darwin-x64", "version": "1.17.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "4ec0f17ff0234fd574a347f235af549beecefabe", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.17.0.tgz", "fileCount": 3, "integrity": "sha512-tkY6IsflCzU63/QlM5L+ygIRMnnJdx2l7ay7dphEhMw1I2Rf3vQli8N/Lj3MSxFe5srJwxv2leDM9VzVhKFm3Q==", "signatures": [{"sig": "MEYCIQCeredMZP19jMl4nim6dV5n16ymIUnESn21pyBcaa3F0wIhAKFFKjeRScoChqwwBZyUcRluGHIDMXXmzt1pN8S6qCuW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5619653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhjesACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry+RAAng86MIC1qYAusIUqEC1cVf/+tnz1HcPXKLRUrg6xFhoL5uPQ\r\nzgZtJTBcZUCoNQW1vHalh75BKry7zAdjcpbd3wIs3PAc3Tdjz4Fyx1lwjRRc\r\nnty4AA74jz13Ob9eT/q5eCrfHsyjdWK2m6pN1A0Lq1DAwtCe+7d057F38ywB\r\njMogunHiB3WmY7Y9lb3Bb2xB9FpVzbjPm5hc+IKfSIi/qG1fzQ1H8/ZAg70B\r\nVMi33k/Q6cwVlnWtJkrlEVbfQnvI9yGCcVGW6GtAUEyxOPBrMc4bplfuvOyR\r\n1A6uPn48EVJ1YGoqy4HGDIkSQvwbI37fS4T2OTkJeBZ5rTHJia3ZvfD7xLAT\r\n3k00+NhLwyPVfDgxE0+Zn2kP9DnELZW6Nh4ZPB3MfnTD1ZdVf4qxlLkKl94g\r\natF7nYPtUZh0XxVRgpuMMapXODXdO3prAiNt1c0oaAeJqc2w/89Afgg/tAwA\r\niCrlVz8IUECep5O5C7VH57vccPiff3RMcqzF0/fwyOLL9gBED6V2gH/mHxAE\r\nVOv6hEne/djH4plt1QQ6Er3HpaGy7ErBDODPsSvkOWe1+/3e/1fSDmNMo1PS\r\nKppxIO0eCgNxIxhl7w+kBtnS+C2hDQP1buWpoUEGv9pz0xxQLWuOlZCIIb33\r\nqbd0QiJDwm9TXYUPPi/ilrJS1yVs7xVcXm4=\r\n=XNiJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d4853b204cc80b051d29be324e2d16f621e76336", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.17.0_1669740460635_0.1334789740990736", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "lightningcss-darwin-x64", "version": "1.17.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7fa5853f71eb8698b511dbad43305666e0e0d871", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.17.1.tgz", "fileCount": 3, "integrity": "sha512-UhXPUS2+yTTf5sXwUV0+8QY2x0bPGLgC/uhcknWSQMqWn1zGty4fFvH04D7f7ij0ujwSuN+Q0HtU7lgmMrPz0A==", "signatures": [{"sig": "MEUCIGcF1c0fVaCPrRAhXCBobb56MoaHbZvLlg04Obr58f3aAiEAhUCUs9HSJTh1AqF1G5Q9Oj8gTcZiZC8rey71SHjf9b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5619653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5MwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq44g//TEAUVevJekFJo12fSeThjjsgETbh8zIxQotYU8OUNr73jfjE\r\nxXsNQx69sbtm3D/wV8uq7sswQcCyV19xlpAIE4YPl0LfvUfSCWL4vYBY5p03\r\nyWl9BAHv7IjLSwUZrSIgK/McyUFpN8cGVX8Tn2vk7zfhc33gWVsFD558mwM1\r\nTFx/34IqYUyIJcsQKL5g6voGvxagSDUdsov69sc/tSs/BvtXn6oyAR7vUKxR\r\nJiBVYs6Xs4oscYxQsnq86QwQU9R6hFE3v/DFvQBs7Py7GVKtkK+CGJXQn0kT\r\nsKXqOZaLfPJ60/hLnEiTgsXE8x36HOz8DNoFSvmolaK7kMk1FQpcswoYEndi\r\npKzBFsiWw/FlpiXG+d4zskeZQHuZbfhmhOHJcAPhs9ez3Li1H9+t0Auk860y\r\nlrBuXxg7CynbG7jInQIzJeJG2cIF5bNuhPQ+/lsVYDVCYq8XwU0RGUHEY9Ge\r\nc9Nik9agplLNDsjKet5k9ZWGXoO39Y+rBAcgRXHXZuoKOuKVUmLs0SD+DKyz\r\n1GVOVBANqy7il2G4UXBQmTsWJzgv3xVk04Dvp0RGxYE3V2YkXsExlLt4C8uB\r\nZvcE9Ai4P1kP3Q9mwKrl5I1abojmRUXVHoevjr9FvSMIp58nN1i3/MY6J9JR\r\nASi4pZVa5gmNEZRO+RSxZ0cdTNAhJcAlHlE=\r\n=yzqi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "27cb9c2a382fd8d9702de8def9dd18db54280c43", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.17.1_1669829424245_0.8778266618080033", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "lightningcss-darwin-x64", "version": "1.18.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "952abea2405fe2bb8dd0bb57a9d5590f8d1d6414", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.18.0.tgz", "fileCount": 3, "integrity": "sha512-mNiuPHj89/JHZmJMp+5H8EZSt6EL5DZRWJ31O6k3DrLLnRIQjXuXdDdN8kP7LoIkeWI5xvyD60CsReJm+YWYAw==", "signatures": [{"sig": "MEUCIA6uzx+TMP/KaNlPVkmlnVjJlozFnV2eBGzt2SmT7AX5AiEAuLWoRQzRuh0OCWyKjuMd0lK0r+KjPfzH5KSIsGsPJc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9746946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbCXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTDA//XuBNZeyREdQB/Y/z3d1VU80f5GwaJgn6vogdx/3XDfXRRxAh\r\nZAta45NRU6bZ9X/zieczSYLAVuRuYCEU4/ITFXP2WA4mwFf+uvIuu5Tstx+E\r\nUF6RLhhqycs+LSdAXuFghf9sfb8f75Gb1+nXwXiImOjTue+aRsCWwduqP91a\r\nuzJzjm9L4t7jSnhIGQ0Dxzq+C/FWDmJNQw0ntTAB3tFNBPDaUc/xKaOmwLTJ\r\n0RVp6jaqKDB7JTT4AYJu8OiX/7sk/wcEA9TVPLcRLZQI7lqj9g5gF83UcTv4\r\nfHkHda3ygnSNFWA/nq6ZYUhm49AYHoOcYioFZJ3cwVtomvOCN1OUlBJCYbXh\r\nOlHFBgeUEkBp9MnJmWpIwzs0wXdt6jvokBlW4rohJpj08sVnAZuCooHNU0Ky\r\nItAcLBo6/q0MfxnaKJOQe4talyUKx2/aAd9RlcOTQ9WeykvFTPsa8C2ayojc\r\n+xUEp+jRDW5HDRy8FAmjhD7K27NKlTAezkpKbe40IjCrfEsLgdnxH+BLOY6B\r\nT06W8rKjOEB6KqZv3dHWE4lE2R2TCtmYvEYjxrsSbP6x9unmqaOmp9BaP4H4\r\nyfIUR+cgANL1AjOtROckzmSks/17O1XDj9V+3NAWYdbisfTDl2oKy1XMPrBT\r\nRzmrUpXqJuDScyKzGw6u8PLzCmMXCBLMkZ0=\r\n=OtPh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "6fb77292c9ef4320fa85a1e26fec3fbdbae3cf8a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.18.0_1672851607255_0.20659057345627296", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "lightningcss-darwin-x64", "version": "1.19.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.19.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c867308b88859ba61a2c46c82b1ca52ff73a1bd0", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-Lif1wD6P4poaw9c/4Uh2z+gmrWhw/HtXFoeZ3bEsv6Ia4tt8rOJBdkfVaUJ6VXmpKHALve+iTyP2+50xY1wKPw==", "signatures": [{"sig": "MEUCIB9ieZP2lXmovVbzhWyJqeSPMqZnyKkfwvnGaKRAOD5uAiEAwkX2xL3Ed6FOjbYcDIv+7FKLHo+qMgPpfrT096+TUkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9121138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQkBAAjQPPeRysaZMvI/Mgj6uFJ624002oIthcVyqHsh1EfQfTxl9+\r\nlgn/6Cf+3/EgiqS3Hpfs6NtLN7MMggV9yWVmW/0SG0/luKRmcuO1TG7CE6iV\r\n8mBbqg+LNuB6j3qVRM+H2IqFPyAbjshO6sI3wyn5d3EAI0Fa9igCckmi86y0\r\nkiWol1ptbizW18yMxqWIkkkp8ngcbk1dzirFWWVrtqyWVmlHhkJv41al+BXd\r\n+G0FKCthguvFuEyLxnb+QeeLgMZntRp/0I5pLbJ8PcMgMedf+jAdW9h7DezW\r\nYExBU7cjXVaGH656T6qeQO85m/jOn3tZOL5zEa/zLQhwJb48La3KhxNqj3Gq\r\nfeiXNAipmJpHn3JUY8O4DyDQmlxFzoIvbulCZmKUvb3Bx8wvDVuv8ngB0WJS\r\nIonYg3kBi3p72D9Dja2aVYtEHzUJAnbRUlxxJxzyQzMsIIyrV6OGwvCdTge1\r\nN5SwobZIzB2JnyGC7Irk+wXyEGCGKVX1ngIbEJwndJBOd7SWmJWla8mt+J5r\r\ngSMkI0OcVg6mo2gVZBlIJ2CxqOI1DWQLEg3GSh/bsCCuYm0UsfmdPCREvZpE\r\nocRWZZVDDwxH3jagG0yi6QfloFRKS54zJTWlWIthb/fFF+WIoeFUfik4pamJ\r\nY3LFrezNlrWBJo4smokxT32eCZHjsTM9L0A=\r\n=ZikC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "05c23f1269321d4b072f6264a5c9cb6edd8a02bb", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "16.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.19.0_1676303940170_0.4110791560726701", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "lightningcss-darwin-x64", "version": "1.20.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.20.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "4caa2b38fe223eabb32ebc3e1268a6b09e6b8d06", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.20.0.tgz", "fileCount": 4, "integrity": "sha512-cmMgY8FFWVaGgtift7eKKkHMqlz9O09/yTdlCXEDOeDP9yeo6vHOBTRP7ojb368kjw8Ew3l0L2uT1Gtx56eNkg==", "signatures": [{"sig": "MEQCIEp8g5mY3feOhD4X3l9uNhJXP8CIQRj8ipsmWBehaqiIAiB0N+jXh86BUZWA8n6hhxT+m20WaD2dXrJ9LukhpedGiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9367514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGQ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMYw/+NFzmcTyPipse32Of43I3znD8LYi6buCehHtBEKq8QYEi+mmC\r\nkmXi5Aof4DKurMqTF+6VOF1VxawJu8IAZ0r5oRB/H1Kwd5LuRm36NpR8jRlA\r\nVFQPtuwTdQs+ItZDQb5RqHFg7CnT8ivakcnnr35h6L6iCXd4vBT7VbRfLNlQ\r\ncWBR/bKo8CkOcUapbEeSC/rMPlVU5VYbfFQwFs+lF46cUgVEqV9pK9bHnluv\r\nM+/u+JTIM2ziLfFxaj+VS/yFlEWgI/1pGJJprxRqlZun0Fkv/6cuycSAwPjY\r\nTNVF53G42M3olutS5x7jryo0o8zaDgize5OzYJYikRFYTZpVam6KYhcEGs4v\r\nYPo2hS3dwR5QPcoikmCeilko5myZBQwUxgPu/osuQXNcAXQGRpJFZSo0bqw6\r\nzv4fk0R1JuUeKOzuvrMmUWvaH/n3tUy+8mp2fAW1AfaKGeFYK8mRo3uRnQcv\r\nmFPb/sxM34RsyiRTbAwldhvobd3rEf2gmLoDGu+ATc3op/HeUowgxaY4NANd\r\nJ31WXZEW6G8ueUYmMubW2Xx63msQ9mCobIBsVMEy/tUfEcKTDLtK2//zNmy6\r\nh4HF7ZIBnAnVE0u6hN196OYRJBex0oxGT17luvikb+Z3AmG90wQ1HptdPHvU\r\nhnSnnT6TEkrMmNVOZQlMjUZ+J+hcpEp3ktg=\r\n=rSjh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4028d4d9026750b31b813ecb4ca5387dba8dd396", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.15.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.20.0_1681941562393_0.6684573823799904", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "lightningcss-darwin-x64", "version": "1.21.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "7d7ceec31af2fac955e1409fa571dd1d5170bba3", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.0.tgz", "fileCount": 4, "integrity": "sha512-xHwMHfcTIHX6fY4YQimI1V/KcbozoNVeKMncZzrp/3NAj0sp3ktxobCj1e0sGqVJMUMaHu/SWvt0mS8jAIhkYw==", "signatures": [{"sig": "MEUCIBb4n2ajR3rXgA114CLlLgxpRgkjgXW/7EIJsG9vNxniAiEAudR8jNE8MfSsk4C6VeJ+IV+JayCPz/J13px8xpn0fRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9710154}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "11f4179f81041e14a04536d3bb0618f06de4e454", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.0_1686117490071_0.9002092916777513", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "lightningcss-darwin-x64", "version": "1.21.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d8ca5997794b7d8bec8e7390da848c2cb865ac33", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.1.tgz", "fileCount": 4, "integrity": "sha512-e/dAKKOcLe2F/A5a89gh03ABxZHn4yjGapGimCFxnCpg68iIdtoPrJTFAyxPV3Jty4djLYRlitoIWNidOK35zA==", "signatures": [{"sig": "MEUCIQDXO9whQCUHvXC4yb6//4qw3zf//gpsYsHguLYpePp4kwIgZEzOm55YJMt8akVaG1EJ+9gIwysIfTszvrv1mtWwH9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9726554}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a36c105af3bb83370f64e28a8cf98b7dea52a6e0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.21.1_1687660726681_0.10846571976177422", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "lightningcss-darwin-x64", "version": "1.21.2", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c746f752799d10ee80971fdebaac24ed9c2a273d", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.2.tgz", "fileCount": 4, "integrity": "sha512-Qv+Tra9p+Lqw4o3XMrDOJBWz5HhNueFHPG+AZMatyQKyWIVKTxDtW9/J7J7J8I2PE26UeLhlQE26ych5PGAp4Q==", "signatures": [{"sig": "MEQCIF0WflW2NStJrfFpyT97jubPwU2FxIipRGUbafw3oTNIAiAUiXjCVGc8RdRwzNbMzXH2RdjdVMBDdN40Y+2BKbxeNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9726642}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "e4963a58a813e02b47a7dd0320defcf0c4182512", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.2_1688266089898_0.883750752685863", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "lightningcss-darwin-x64", "version": "1.21.3", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d3ffa578be15819e9ad4c0a4cdef4b3a85ff96c8", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.3.tgz", "fileCount": 4, "integrity": "sha512-biYATnAiuIWDwlrUE3LE5p21Eyyft72qf5o6V7AKYrGyE2/6tn/10Wb4K2GgNHm2vwNtWiPSqntptqzxsUj6lQ==", "signatures": [{"sig": "MEQCIAOI3eS67qta+WnZPCTs+lBko5iRkiHu6snvGCsWLlImAiA3heL0jLTgKVwHSWE632NFZsrfupcHYyy+sobPIES7wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9726642}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "83bab71e80781b4d2b128f5938099b2f0c8239bd", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.3_1688397342736_0.7423916166249056", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "lightningcss-darwin-x64", "version": "1.21.4", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "d934feef6b1ac7346c75341e12201b48f181e085", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.4.tgz", "fileCount": 4, "integrity": "sha512-R6TfDHzl+kuZdrCfQWKhGf5b4K6XtVDfwQkMzncSimykchFwPzLe1QGKH3Cr45glBD265+2Hwmo4V6DYCiVcPQ==", "signatures": [{"sig": "MEYCIQCQhTJhtXfZlbb7/+mWcobFrotjjHUV5ovKnMYYwCDSUgIhAIP63wijUrz+QaNT426WMGxRNasybd7eF09wXk5Adzmb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9726610}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "bd517fde245dcce75a4b2eb21a25b147d54eb64a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.4_1688441862647_0.6717737280759992", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "lightningcss-darwin-x64", "version": "1.21.5", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "b9b2306809306635afee8a9981f93e7668ecb8e8", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.5.tgz", "fileCount": 4, "integrity": "sha512-<PERSON><PERSON><PERSON>ej/U9MrdPxDk7+FWhO8+UqVoZUHG4VvKT5RQ4RJtqtANTiWiI97LvoVNMtdMnHaKs1Pkji6wHUFxjJsHQ==", "signatures": [{"sig": "MEUCIQDgnUkF/xzHYoFQ2LfQ/tyuffknPkn5CjT3rgtXrFa72wIgOfVUWy4+5Y6ovQde6JmWLpCNauVjlihW6z/MTM4ylXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9726610}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "d44a73c3c000ccebbe8355b7cf9272236e573b4d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.16.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.5_1688531409390_0.5936278508620494", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "lightningcss-darwin-x64", "version": "1.21.6", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "13afd17ffce0d84ab38b7e2b71c7af588f774882", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.6.tgz", "fileCount": 4, "integrity": "sha512-YefJilvIq4PodtRqjIxGlncxcWx/F83hzW4NWG6WkB+O/CyoAcH4ejRUt5iwcfocPfeEwPoCtFA8GrOKpVaA1A==", "signatures": [{"sig": "MEQCIGbv+VgFHvpUYTTYY070U05MsqtZxtmyyy7dwT7YtIVUAiAglj5C3TSdY5r7UOjnQdjffntpzixMBO6HUecZNNJ2TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9742986}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f485eb51d13e677a674cb10e170c71138446b71d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.6_1692511438368_0.6130137640511761", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "lightningcss-darwin-x64", "version": "1.21.7", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "9c9be83d66e34479ddfb54cc292149f96d6af37f", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.7.tgz", "fileCount": 4, "integrity": "sha512-F4gS4bf7eWekfPT+TxJNm/pF+QRgZiTrTkQH6cw4/UWfdeZISfuhD5El2dm16giFnY0K5ylIwO+ZusgYNkGSXA==", "signatures": [{"sig": "MEYCIQC8HRnWKhhl2sHYe2c5YZz3nU4IBKGWeZGTm1S2FbqJlQIhAJL3gjBzSyiiWLsYQArxOU4DQNjXPbjk4O2+fyag2du4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9742986}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "393013928888d47ec7684d52ed79f758d371bd7b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.7_1692555038251_0.6287625711053999", "host": "s3://npm-registry-packages"}}, "1.21.8": {"name": "lightningcss-darwin-x64", "version": "1.21.8", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.21.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "81f4671cf9c245bb25a6536c01ddac76973fd283", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.21.8.tgz", "fileCount": 4, "integrity": "sha512-YhF64mcVDPKKufL4aNFBnVH7uvzE0bW3YUsPXdP4yUcT/8IXChypOZ/PE1pmt2RlbmsyVuuIIeZU4zTyZe5Amw==", "signatures": [{"sig": "MEUCIDtXOePYQywdtyf173VtH6T8abzXIo6bXpOYH9Xyb0JAAiEA5EUuyfCd3/XWO/y8/l8MMQrDGPXv1JmaPSZlj4RCGTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9742994}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81cb0daee30dad924b14af7fbc739ddfe31d7f9e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.21.8_1694407619841_0.28918408565091047", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "lightningcss-darwin-x64", "version": "1.22.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.22.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "1c5fe3e3ab31c9f1741f6d5d650ab683bd942854", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.22.0.tgz", "fileCount": 4, "integrity": "sha512-9KHRFA0Y6mNxRHeoQMp0YaI0R0O2kOgUlYPRjuasU4d+pI8NRhVn9bt0yX9VPs5ibWX1RbDViSPtGJvYYrfVAQ==", "signatures": [{"sig": "MEQCIGiT2NoroBB+/3/TwWU8FxzutHGtcMkrm/qVJRnLyQXsAiBDY718/UP+ooiXBZ+arZ6+zpiixiHKWCIo/Osvp4QY+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9742706}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "7ff93ca5c69ba9df415e1e2319d275e2cec249d7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.17.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.22.0_1694990930563_0.5517255830287564", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "lightningcss-darwin-x64", "version": "1.22.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.22.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "cdd380006a176b7faea83d1d642d9c5d65620f74", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.22.1.tgz", "fileCount": 4, "integrity": "sha512-5p2rnlVTv6Gpw4PlTLq925nTVh+HFh4MpegX8dPDYJae+NFVjQ67gY7O6iHIzQjLipDiYejFF0yHrhjU3XgLBQ==", "signatures": [{"sig": "MEQCIGwdTHvos0D0xMXeWgtt3McI+CzhSKYg15g9KfdcABmtAiBTrdv42wb0+nRQsqEtdpM/OUdIMqLlsP1JNqWu33ek0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9792122}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "4994306f8f8d98a2b37433fced50efdacbbab901", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.18.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.22.1_1699395801158_0.4810359518319143", "host": "s3://npm-registry-packages"}}, "1.23.0": {"name": "lightningcss-darwin-x64", "version": "1.23.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.23.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "8394edaa04f0984b971eab42b6f68edb1258b3ed", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.23.0.tgz", "fileCount": 4, "integrity": "sha512-KeRFCNoYfDdcolcFXvokVw+PXCapd2yHS1Diko1z1BhRz/nQuD5XyZmxjWdhmhN/zj5sH8YvWsp0/lPLVzqKpg==", "signatures": [{"sig": "MEUCIQCzMGfBc5fEfYAdn6mgHG5dZBRgK6Qzvp8+Pt3eBGCiGAIgN0iP0DAFq0lPwJAcdILKTaBH6XtQJnr19gM6ZsW5PkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9824970}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b47f496a86075adcb6719aee3a8867e749a880b9", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.0", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.23.0_1705276067117_0.6334894525557426", "host": "s3://npm-registry-packages"}}, "1.24.0": {"name": "lightningcss-darwin-x64", "version": "1.24.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.24.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "98cb96ad7ea78990ee24cd7741b9983696c1adab", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.24.0.tgz", "fileCount": 4, "integrity": "sha512-4KCeF2RJjzp9xdGY8zIH68CUtptEg8uz8PfkHvsIdrP4t9t5CIgfDBhiB8AmuO75N6SofdmZexDZIKdy9vA7Ww==", "signatures": [{"sig": "MEYCIQD/HePVHR7Ynazpcg0mVrX7saHuwaKQ1FjVYf8un9wX4QIhAJJyE6/nDFcWpHZPuS1DKEUPRZuxnhOpyauN/i5j7rR3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9971642}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "30b9d79b3d277dfb90d90d78cd9182ac755ad6cf", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.24.0_1708648880279_0.3980264047067201", "host": "s3://npm-registry-packages"}}, "1.24.1": {"name": "lightningcss-darwin-x64", "version": "1.24.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.24.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5acb1338ac0aae38e405efd854ed97ba11509eea", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.24.1.tgz", "fileCount": 4, "integrity": "sha512-R4R1d7VVdq2mG4igMU+Di8GPf0b64ZLnYVkubYnGG0Qxq1KaXQtAzcLI43EkpnoWvB/kUg8JKCWH4S13NfiLcQ==", "signatures": [{"sig": "MEUCIQCRUw0EPLXR6FEGk05MUPo7uPMir6kyJpxLXfMT/dczCAIgA0OkAGXTG+J+/KrQzaB1PCzZsKayf7OPlWrm5fwN60M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9576610}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "baa1a2b7fa52eeb3827f8edcc2e14de33cd69ad0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.24.1_1710476568944_0.2790922518213914", "host": "s3://npm-registry-packages"}}, "1.25.0": {"name": "lightningcss-darwin-x64", "version": "1.25.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.25.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "58030af570c68e6966d1ed6152b8255fd0f71ccb", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.25.0.tgz", "fileCount": 4, "integrity": "sha512-h1XBxDHdED7TY4/1V30UNjiqXceGbcL8ARhUfbf8CWAEhD7wMKK/4UqMHi94RDl31ko4LTmt9fS2u1uyeWYE6g==", "signatures": [{"sig": "MEUCIQD/akP2L8EjmrTdJySDLkzBMqPJL5wcxPndwTLbhwlRAAIgH41tE3ap707Y+AH9YdvJCZ9BVU320LqpiN7Ob9XWYwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9727122}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "81d21b9f5201c6eb8365fbb4fa81cbd947c3474f", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.25.0_1715973749014_0.0639033455433844", "host": "s3://npm-registry-packages"}}, "1.25.1": {"name": "lightningcss-darwin-x64", "version": "1.25.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.25.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "dc5d2d5c4372308b1a326a8c5efcc3e489b654c6", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.25.1.tgz", "fileCount": 4, "integrity": "sha512-dYWuCzzfqRueDSmto6YU5SoGHvZTMU1Em9xvhcdROpmtOQLorurUZz8+xFxZ51lCO2LnYbfdjZ/gCqWEkwixNg==", "signatures": [{"sig": "MEQCIDM/Z2wEmRmodY+csZYPWw8h0J68/zy3sk0SRFaXp//4AiAYMYQHFaucuGCrMNk19Gxo1s2yCMS7Vs4BQ1l5/5NDvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9727130}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "fa6c015317e5cca29fa8a09b12d09ac53dcfbc77", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.2", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.25.1_1716617169983_0.6363046268900863", "host": "s3://npm-registry-packages"}}, "1.26.0": {"name": "lightningcss-darwin-x64", "version": "1.26.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.26.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "eb311b9aa661948c6e92971f499afcaf453d2235", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.26.0.tgz", "fileCount": 4, "integrity": "sha512-Rf9HuHIDi1R6/zgBkJh25SiJHF+dm9axUZW/0UoYCW1/8HV0gMI0blARhH4z+REmWiU1yYT/KyNF3h7tHyRXUg==", "signatures": [{"sig": "MEUCIQCg6SwiSpFeltCPMyuU8a8lLg3eURzoIXVfBxIbrl55lQIgHrlQrMz1c9fwlTx17w/wdQzKHQdXJKroOZAnv8PpQyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9616642}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "0bcd896e81a8a2c5d847f626a37e2cffea79e2a0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.26.0_1722958359200_0.7469103294385306", "host": "s3://npm-registry-packages"}}, "1.27.0": {"name": "lightningcss-darwin-x64", "version": "1.27.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.27.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c906a267237b1c7fe08bff6c5ac032c099bc9482", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.27.0.tgz", "fileCount": 4, "integrity": "sha512-0+mZa54IlcNAoQS9E0+niovhyjjQWEMrwW0p2sSdLRhLDc8LMQ/b67z7+B5q4VmjYCMSfnFi3djAAQFIDuj/Tg==", "signatures": [{"sig": "MEQCIA7jRchtx6Baz0Zn4b6JmbhQ4M/8fmWCLmKF3AOTMlFKAiBpmAaczo7YOWsMW9ZlMCk+BDNguClNyd4poSeSE3dDcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624842}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "eb49015cf887ae720b80a2856ccbdf61bf940ef1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.27.0_1726023633455_0.8061434068686972", "host": "s3://npm-registry-packages"}}, "1.28.0": {"name": "lightningcss-darwin-x64", "version": "1.28.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.28.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "5111bd71da237ff0d789a4220ff5177b6dccbbba", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.28.0.tgz", "fileCount": 4, "integrity": "sha512-2LvkPzzBj92Js8fOpo0xFhfoKIyjjog4dIKlO2Mu5VZg2WakrjjQnGeEULXp9APfeWt0A9S+vzszT1gMCrJfbw==", "signatures": [{"sig": "MEYCIQDY2X4Tgd4U/3AnJDX3OqO+DsxlNJITWm4mjQ1JYxQFugIhAPjrQhob5CMARuHLrhgtPYinrbb/872BF3qpWMruJric", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9633079}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "8a67583105757e4a25378d65d243b87a345b2c2d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.28.0_1730668651718_0.832986442054461", "host": "s3://npm-registry-packages"}}, "1.28.1": {"name": "lightningcss-darwin-x64", "version": "1.28.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.28.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c0f975759af364699fdbd7a4756ac66767ed9767", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.28.1.tgz", "fileCount": 4, "integrity": "sha512-O7ORdislvKfMohFl4Iq7fxKqdJOuuxArcglVI3amuFO5DJ0wfV3Gxgi1JRo49slfr7OVzJQEHLG4muTWYM5cTQ==", "signatures": [{"sig": "MEYCIQC3N7DRilV6019tKlDL1VeeBHS/CgYpxI8VHLZitYYaggIhAN/g4rwEZumuViMECjXx/o7RWEsLZDZY7/2jAe74nBPe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9633079}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "a3390fd4140ca87f5035595d22bc9357cf72177e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.4", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.28.1_1730674690766_0.20542499384734958", "host": "s3://npm-registry-packages"}}, "1.28.2": {"name": "lightningcss-darwin-x64", "version": "1.28.2", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.28.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "6c43249d4ae821416d0d78403eae56111d0c6a94", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.28.2.tgz", "fileCount": 4, "integrity": "sha512-R7sFrXlgKjvoEG8umpVt/yutjxOL0z8KWf0bfPT3cYMOW4470xu5qSHpFdIOpRWwl3FKNMUdbKtMUjYt0h2j4g==", "signatures": [{"sig": "MEUCIDrBktvoXtXrAkWy1211mPYR40X4C04zz9DLxv+TKK8IAiEAoU231ZLKbQW0K587Sv1DnnFWpcnyvhNb+qZUers/d14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9637183}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "9b2e8bbe732d7c101272ddab03ac21b88bf55c4a", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "18.20.5", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.28.2_1732512280189_0.5337448830707796", "host": "s3://npm-registry-packages"}}, "1.29.0": {"name": "lightningcss-darwin-x64", "version": "1.29.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.29.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "db8bd593de1ce4cf2e05098b04426350884907e1", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.29.0.tgz", "fileCount": 4, "integrity": "sha512-HrD1nEhQLE7GPzlw+DlbnLBGOae+42kct1kr7BRsWpFtVRMcgAMg316ruQvM6MEbZ22og01s2GJtp4uCBu7jrw==", "signatures": [{"sig": "MEYCIQDvvZcb+jgzm4rOd2CJDfIXCkBjLOw+rcwYVqNz9iEhSAIhAOOFprcQXXR90J0Eq1my1tR+/eTQVquTlLwUYzV3bPY/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9225391}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "222fa9f7ad8b4004afd7a30c2f99cab3c8621191", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.29.0_1736401652168_0.49972210927827576", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.1": {"name": "lightningcss-darwin-x64", "version": "1.29.1", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.29.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "e79c984180c57d00ee114210ceced83473d72dfc", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.29.1.tgz", "fileCount": 4, "integrity": "sha512-k33G9IzKUpHy/J/3+9MCO4e+PzaFblsgBjSGlpAaFikeBFm8B/CkO3cKU9oI4g+fjS2KlkLM/Bza9K/aw8wsNA==", "signatures": [{"sig": "MEQCIC19BsR6XqwrD1BJyyEbTQxs/0wTjxGLvkjoMZZnhC0iAiASasVYEOxjD5tCbQ8aRhoT2XB1NV/DyO0JZQlDNUtQjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9225399}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "b10f9baf8878411bf2b09dfe8d64ba09ef7a4eac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"caniuse-lite": "^1.0.30001677", "lightningcss": "link:."}, "_nodeVersion": "20.18.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.29.1_1736445747208_0.12981616009554298", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.2": {"name": "lightningcss-darwin-x64", "version": "1.29.2", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.29.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "891b6f9e57682d794223c33463ca66d3af3fb038", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.29.2.tgz", "fileCount": 4, "integrity": "sha512-j5qYxamyQw4kDXX5hnnCKMf3mLlHvG44f24Qyi2965/Ycz829MYqjrVg2H8BidybHBp9kom4D7DR5VqCKDXS0w==", "signatures": [{"sig": "MEUCIQDBDLzWe3hAPBhaKKoGdmpaEAW6mmg3tGzX7XbUzz+JqQIgbtABN43woquYEvFa4u1RFYqmdiAs3A2KI1Lgz7ZzAWU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9229450}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "f2303df29448a55c5f4f284f747eb53760769fe4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-darwin-x64_1.29.2_1741242106906_0.17149548361167466", "host": "s3://npm-registry-packages-npm-production"}}, "1.29.3": {"name": "lightningcss-darwin-x64", "version": "1.29.3", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.29.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "2823ca2274eb0a2dfa26090b5c7c367ee9b1b88f", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.29.3.tgz", "fileCount": 4, "integrity": "sha512-KF2XZ4ZdmDGGtEYmx5wpzn6u8vg7AdBHaEOvDKu8GOs7xDL/vcU2vMKtTeNe1d4dogkDdi3B9zC77jkatWBwEQ==", "signatures": [{"sig": "MEQCICqJ3PaWi0NCVNgGec+EUHefeKQRTYbNx4SYiE4WxrGEAiBk5sSIDaF/wwQzbbOjGoRx2q4SZbfxTlc2nKtS4CQoTA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9249986}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "80eb8617c5f8f5519ed85bd3eb6d8953f4d32493", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.18.3", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.29.3_1741974583650_0.5375150738079049", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.0": {"name": "lightningcss-darwin-x64", "version": "1.30.0", "license": "MPL-2.0", "_id": "lightningcss-darwin-x64@1.30.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "os": ["darwin"], "cpu": ["x64"], "dist": {"shasum": "c10aa1c1238d5f260b0ee5f17e86809dbc24fe3f", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.0.tgz", "fileCount": 4, "integrity": "sha512-+qNst+L3GGwG5LypEFTmDUOtNarQVh717Enk4AfmKfwlTrKCSe9kAiPyK7ces269a+f0jNSa8Uww8WwGFXzt8w==", "signatures": [{"sig": "MEQCID9hn9Y5vPt1n7xnydaQYAL44llokoXs3chWlvJSnlSmAiAcUoUfd9lXkYg4CBj1aJzLGlxSVtUklwCrgc4bxuORGw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9307386}, "main": "lightningcss.darwin-x64.node", "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "gitHead": "474c67c046b4a54217166057296fa4ca28764858", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/parcel-bundler/lightningcss.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "A CSS parser, transformer, and minifier written in Rust", "directories": {}, "resolutions": {"lightningcss": "link:."}, "_nodeVersion": "20.19.1", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lightningcss-dar<PERSON>-x64_1.30.0_1746945542384_0.49477172522584856", "host": "s3://npm-registry-packages-npm-production"}}, "1.30.1": {"name": "lightningcss-darwin-x64", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.darwin-x64.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "resolutions": {"lightningcss": "link:."}, "os": ["darwin"], "cpu": ["x64"], "_id": "lightningcss-darwin-x64@1.30.1", "gitHead": "f2dc67c4d3fe92f26693c02366db1e60cae0db27", "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==", "shasum": "e81105d3fd6330860c15fe860f64d39cff5fbd22", "tarball": "https://registry.npmjs.org/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz", "fileCount": 4, "unpackedSize": 9307386, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD2NK8qRQ85Ct8TQ70ihgFeFjC/XjMGXGPThwbKvpFyGgIhALhaq6TiGvGPSDovNg8CZ0Gq8JBIPrcDkZhHVY9SVaS+"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/lightningcss-darwin-x64_1.30.1_1747193911608_0.47503750084863294"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-08T05:19:04.211Z", "modified": "2025-05-14T03:38:32.103Z", "1.14.0": "2022-09-08T05:19:04.524Z", "1.15.0": "2022-09-15T04:16:24.403Z", "1.15.1": "2022-09-16T03:49:14.748Z", "1.16.0": "2022-09-20T03:23:05.159Z", "1.16.1": "2022-11-06T18:01:58.753Z", "1.17.0": "2022-11-29T16:47:40.918Z", "1.17.1": "2022-11-30T17:30:24.483Z", "1.18.0": "2023-01-04T17:00:07.521Z", "1.19.0": "2023-02-13T15:59:00.421Z", "1.20.0": "2023-04-19T21:59:22.675Z", "1.21.0": "2023-06-07T05:58:10.312Z", "1.21.1": "2023-06-25T02:38:46.951Z", "1.21.2": "2023-07-02T02:48:10.249Z", "1.21.3": "2023-07-03T15:15:43.045Z", "1.21.4": "2023-07-04T03:37:42.838Z", "1.21.5": "2023-07-05T04:30:09.640Z", "1.21.6": "2023-08-20T06:03:58.717Z", "1.21.7": "2023-08-20T18:10:38.630Z", "1.21.8": "2023-09-11T04:47:00.075Z", "1.22.0": "2023-09-17T22:48:50.873Z", "1.22.1": "2023-11-07T22:23:21.459Z", "1.23.0": "2024-01-14T23:47:47.356Z", "1.24.0": "2024-02-23T00:41:20.562Z", "1.24.1": "2024-03-15T04:22:49.213Z", "1.25.0": "2024-05-17T19:22:29.473Z", "1.25.1": "2024-05-25T06:06:10.250Z", "1.26.0": "2024-08-06T15:32:39.499Z", "1.27.0": "2024-09-11T03:00:33.747Z", "1.28.0": "2024-11-03T21:17:32.138Z", "1.28.1": "2024-11-03T22:58:11.042Z", "1.28.2": "2024-11-25T05:24:40.465Z", "1.29.0": "2025-01-09T05:47:32.422Z", "1.29.1": "2025-01-09T18:02:27.528Z", "1.29.2": "2025-03-06T06:21:47.213Z", "1.29.3": "2025-03-14T17:49:43.909Z", "1.30.0": "2025-05-11T06:39:02.674Z", "1.30.1": "2025-05-14T03:38:31.910Z"}, "bugs": {"url": "https://github.com/parcel-bundler/lightningcss/issues"}, "license": "MPL-2.0", "homepage": "https://github.com/parcel-bundler/lightningcss#readme", "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/lightningcss.git"}, "description": "A CSS parser, transformer, and minifier written in Rust", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "This is the x86_64-apple-darwin build of lightningcss. See https://github.com/parcel-bundler/lightningcss for details.", "readmeFilename": "README.md"}