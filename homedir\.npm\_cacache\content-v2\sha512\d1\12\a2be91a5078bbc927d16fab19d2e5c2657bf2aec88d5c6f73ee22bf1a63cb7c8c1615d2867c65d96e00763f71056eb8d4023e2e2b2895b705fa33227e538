{"_id": "extsprintf", "_rev": "59-9ccaf892af6c19475d192e0a77a088ea", "name": "extsprintf", "dist-tags": {"latest": "1.4.1"}, "versions": {"1.0.0": {"name": "extsprintf", "version": "1.0.0", "_id": "extsprintf@1.0.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "4d58b815ace5bebfc4ebf03cf98b0a7604a99b86", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.0.0.tgz", "integrity": "sha512-AtBFy+ysvNodhorg/XoW8l+3VtfhILEWc53shEOsAuyDQTnQoXDfI2x9c93QY+RMdCQWAbL0XqpidgJe3BOCFA==", "signatures": [{"sig": "MEUCID39Bh0k9g+mdBDNqav0DS/MolRD2M+llALCwvTrRm5FAiEAw9p2VRPwF6Jej9IZF8Yr3cBTeFQhs7QxzVTKz36xtWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "extended POSIX-style sprintf", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.1": {"name": "extsprintf", "version": "1.0.1", "_id": "extsprintf@1.0.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "590e147d5c59e2180b3366f7bc21acabaf69a185", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.0.1.tgz", "integrity": "sha512-NITHi7GagOnTtofWByW/X1N2YsFflOUlS7ItDJpo3b8lbwYWq3XUXfEbIM2Ey7jbSLsD4oEQC48NwxsOM5qisA==", "signatures": [{"sig": "MEUCIQDwOMksn2ShuuGPFv9ks0dKP/MVJJWJRiTFjZ30IsfgbgIgPGnnRoJATr5Mvm3zzGxv0cRrGeazLWD1MAkit0gINlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "extended POSIX-style sprintf", "directories": {}, "_nodeVersion": "v0.6.17", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.2": {"name": "extsprintf", "version": "1.0.2", "_id": "extsprintf@1.0.2", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "e1080e0658e300b06294990cc70e1502235fd550", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.0.2.tgz", "integrity": "sha512-g21Br4ELmVaKCVSUSSTXecKG+MiLcHFoby5RPPUmfZdhQTontXUOPf0QK/TvreRjgItRiyO928zxR4TCrnuwmA==", "signatures": [{"sig": "MEUCIQDOu3/o2rrbABL3MF8QmoSR7nflqj9+SLklNnvxvZ1wbQIgIrMvarcWqzxlL4H9lrs5pS6qZTbv2zhdtGUDPCJgB3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "extended POSIX-style sprintf", "directories": {}}, "1.0.3": {"name": "extsprintf", "version": "1.0.3", "license": "MIT", "_id": "extsprintf@1.0.3", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "3310ca8ced5205e5234766b0b2744ea5b2788d67", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.0.3.tgz", "integrity": "sha512-Py/IoM1IJWM1mEAvhH8MVTiDEMG7joiuVmXPCMpwkVRQhkJSYycbAP3Q99IXxzevjGqJoIiGXKgD8QLrdqXhqQ==", "signatures": [{"sig": "MEUCIQDOedkkgGzMJtWCgBiyHpfTXQbjBgCBHdWLDk3JcqiVigIgEI1CIE/S5DKPb/T7xh75A3qFMVqlw+Y7hM3//dSvev8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "extended POSIX-style sprintf", "directories": {}}, "1.1.0": {"name": "extsprintf", "version": "1.1.0", "license": "MIT", "_id": "extsprintf@1.1.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "f9b87b9970f66788aef90d398a77590987b22472", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.1.0.tgz", "integrity": "sha512-JT7+brYBT732E5cFDxxz+UQjwYxkEtyh2h9BpxHxVFt+It+QEoLLWmiwfJoCcTUUsqqJsJb9fcRuXLD8VsjOWA==", "signatures": [{"sig": "MEYCIQDtVQzu3XQM8mx2NAB1NSuSKp99yag4S1PKT3DYMuepFwIhALZz9bBXLxKdBZcwoqUiY+vQiUoOWGmTk62A2QYCHhqx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "_shasum": "f9b87b9970f66788aef90d398a77590987b22472", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "extended POSIX-style sprintf", "directories": {}}, "1.1.1": {"name": "extsprintf", "version": "1.1.1", "license": "MIT", "_id": "extsprintf@1.1.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "3e8b91a53ca4b1764e77435f7d9faccb8a0a3550", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.1.1.tgz", "integrity": "sha512-7KSBn+vpZ4bbeQgU4fuSxR/QN959/si1ZUm14cKLa3V99E9bSL0ZjpytziQUz4wejmPZY0os9gyp45rIGbErFg==", "signatures": [{"sig": "MEYCIQCGJM8lnqpOLsrlMvMYAGa6/vNlfWymWmb1G+nJjl76QgIhANCWG1EgHrkDEEFke+u/xYwLdbJvTMqpVwayEYlBLsCx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "_shasum": "3e8b91a53ca4b1764e77435f7d9faccb8a0a3550", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "extended POSIX-style sprintf", "directories": {}}, "1.2.0": {"name": "extsprintf", "version": "1.2.0", "license": "MIT", "_id": "extsprintf@1.2.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "5ad946c22f5b32ba7f8cd7426711c6e8a3fc2529", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.2.0.tgz", "integrity": "sha512-T3PYC6HucmF4OfunfZb5d1nRvTSvWYhsr/Og33HANcCuCtGPUtWVyt/tTs8SU9sR0SGh5Z/xQCuX/D72ph2H+A==", "signatures": [{"sig": "MEQCIF3AXhaBMSJDNTZjMGVZZHTvLa/tBtvAHjL38IvrWhKkAiB0KplqvJaio+7x9BRkSnyAE8PxTWbKPipdlPTLDfpHBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "_shasum": "5ad946c22f5b32ba7f8cd7426711c6e8a3fc2529", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "extended POSIX-style sprintf", "directories": {}}, "1.2.1": {"name": "extsprintf", "version": "1.2.1", "license": "MIT", "_id": "extsprintf@1.2.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "1e42560fc26783d14ab640dcf22cc73789b3844e", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.2.1.tgz", "integrity": "sha512-L4ZIE54Bb7uGUje6pHrT/kpw+7O7nwK+IRtI6WRaMTnjp0EoNo6wdJnCFzOlAdsfU6Y3aYx/8TwyMZgRlsfTTw==", "signatures": [{"sig": "MEYCIQChwgWV8dMQSGT8FWaYxgjkU+ly5c7y80XR/HiIfn1sCwIhAKMckDiFkMRZ9Y7bNp81OYQ2w/kjt6fmAY49hn95F9uz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "_shasum": "1e42560fc26783d14ab640dcf22cc73789b3844e", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "extended POSIX-style sprintf", "directories": {}}, "1.3.0": {"name": "extsprintf", "version": "1.3.0", "license": "MIT", "_id": "extsprintf@1.3.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "96918440e3041a7a414f8c52e3c574eb3c3e1e05", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "signatures": [{"sig": "MEUCIBxD6HEF+1yX3VW7jhhmt6JwtPUVwnIvvlNUTabzu1GXAiEAtDxdElXf7QjX6+7lFlbGJTalSqH4XCGOvWu0g3xf37Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "_shasum": "96918440e3041a7a414f8c52e3c574eb3c3e1e05", "engines": ["node >=0.6.0"], "gitHead": "accc9f2774189a416f294546ed03b626eec3f80c", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "extended POSIX-style sprintf", "directories": {}, "_nodeVersion": "0.12.0"}, "1.4.0": {"name": "extsprintf", "version": "1.4.0", "license": "MIT", "_id": "extsprintf@1.4.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf#readme", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "e2689f8f356fad62cca65a3a91c5df5f9551692f", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.4.0.tgz", "integrity": "sha512-6NW8DZ8pWBc5NbGYUiqqccj9dXnuSzilZYqprdKJBZsQodGH9IyUoFOGxIWVDcBzHMb8ET24aqx9p66tZEWZkA==", "signatures": [{"sig": "MEYCIQD4FFXlndqTZ7dREMwzBaozwd1UL7gL5S9PyLSMXLCIKwIhAM7y1LuH2XApzqto0KwMxkbhLpKwQAqvffByEKig1MJI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/extsprintf.js", "_from": ".", "_shasum": "e2689f8f356fad62cca65a3a91c5df5f9551692f", "engines": ["node >=0.6.0"], "gitHead": "26f360d1588a2d4106f3061bfd757f6ecdee37dc", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "extended POSIX-style sprintf", "directories": {}, "_nodeVersion": "0.10.45", "_npmOperationalInternal": {"tmp": "tmp/extsprintf-1.4.0.tgz_1512077204279_0.1054401712026447", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "extsprintf", "version": "1.4.1", "license": "MIT", "_id": "extsprintf@1.4.1", "maintainers": [{"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chudley", "email": "<EMAIL>"}, {"name": "tcha<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d<PERSON>ell", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-extsprintf#readme", "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "dist": {"shasum": "8d172c064867f235c0c84a596806d279bf4bcc07", "tarball": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.4.1.tgz", "fileCount": 12, "integrity": "sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==", "signatures": [{"sig": "MEYCIQCuMDAZOLE9AWDP6u8vC18sDEtpsygCd0Qta6Ip3G72/gIhAI3vAEf8lRBFtKi2DFS2h/oubFFa9c9+V72Pw0BkLWsH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2qnOCRA9TVsSAnZWagAAY/AP/RUJokQ6Ei+g8fthCsro\n/VQFn2QVlQAkmR/dZVF+bzMrr0DDUx7mjcuPJ6JCF8xsap3Tc5KcjniWRYhX\n/iz0jwV+XMgDN7+gBHswwwJ+4IRAGDVPm9+mhPCH0YkuiMUDUOtGoxUaRuRo\ntvOFhBBD3A/6ajAIwdC9yBJeGdr2uDkkg+3sTt/ZU/FtCYEpjC3Nked3tg8m\n2CRQZY3R17x7qqnIu4aqrk6ig6uSKtdxJdbNwM+ApgImu6cyBpLaJ1QFwXN4\ntq47qgaIKVj8kbsx8soWqOYjnMDr4Q+ztb0JXCPCmpj6J1+XHa973CBYWwKt\nRYdpe+mTKgtqjoTZHHnJG3mZ7Vn54aNQGJWWS0IGxu2QTki5CZt0YLbjPmpC\nojZxX9VVbtWirzpwqD7frsfJjQTkHKKlCLmmwFjWqnknUjQTdgLzjvG6rIff\n3kGTpIgIvG+1f6oH4tuCMbW1R5pritypTGss0ubbElt41WpTJaW2KEz05Vyi\nsJDuV+x2Y3PA/XK81aScH3azdzAWXkJZaHowTw92OUglFPgF27Nh3p7ZZmdu\n4/iiEi3A3r6CyRaJBjt/ggzvZHNVm+CEy9mqWsnVgiI7qo8LdxGQBMpMB7Xc\nuZ49Gk2+x7gIw53r4jclPBguIFmNcLnPsSV6fDWOqS+H2crASlOk4PgPS9Hf\nYbr6\r\n=G295\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/extsprintf.js", "engines": ["node >=0.6.0"], "gitHead": "0faac1e42c71249731f93f720d98c21e55591b4b", "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "extended POSIX-style sprintf", "directories": {}, "_nodeVersion": "12.22.7", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/extsprintf_1.4.1_1635889677853_0.23533804056555296", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-04-18T01:14:25.292Z", "modified": "2025-02-07T15:27:56.258Z", "1.0.0": "2012-04-18T01:14:26.459Z", "1.0.1": "2012-06-14T00:13:58.871Z", "1.0.2": "2013-02-06T18:25:43.668Z", "1.0.3": "2014-01-15T00:31:32.926Z", "1.1.0": "2014-06-06T21:00:44.164Z", "1.1.1": "2014-06-13T16:30:25.968Z", "1.2.0": "2014-06-16T21:20:26.849Z", "1.2.1": "2014-11-21T00:31:52.236Z", "1.3.0": "2015-03-07T00:13:00.888Z", "1.4.0": "2017-11-30T21:26:44.392Z", "1.4.1": "2021-11-02T21:47:58.004Z"}, "bugs": {"url": "https://github.com/davepacheco/node-extsprintf/issues"}, "license": "MIT", "homepage": "https://github.com/davepacheco/node-extsprintf#readme", "repository": {"url": "git+https://github.com/davepacheco/node-extsprintf.git", "type": "git"}, "description": "extended POSIX-style sprintf", "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "trentm"}, {"email": "<EMAIL>", "name": "dap"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "melloc"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "readme": "# extsprintf: extended POSIX-style sprintf\n\nStripped down version of s[n]printf(3c).  We make a best effort to throw an\nexception when given a format string we don't understand, rather than ignoring\nit, so that we won't break existing programs if/when we go implement the rest\nof this.\n\nThis implementation currently supports specifying\n\n* field alignment ('-' flag),\n* zero-pad ('0' flag)\n* always show numeric sign ('+' flag),\n* field width\n* conversions for strings, decimal integers, and floats (numbers).\n* argument size specifiers.  These are all accepted but ignored, since\n  Javascript has no notion of the physical size of an argument.\n\nEverything else is currently unsupported, most notably: precision, unsigned\nnumbers, non-decimal numbers, and characters.\n\nBesides the usual POSIX conversions, this implementation supports:\n\n* `%j`: pretty-print a JSON object (using node's \"inspect\")\n* `%r`: pretty-print an Error object\n\n# Example\n\nFirst, install it:\n\n    # npm install extsprintf\n\nNow, use it:\n\n    var mod_extsprintf = require('extsprintf');\n    console.log(mod_extsprintf.sprintf('hello %25s', 'world'));\n\noutputs:\n\n    hello                     world\n\n# Also supported\n\n**printf**: same args as sprintf, but prints the result to stdout\n\n**fprintf**: same args as sprintf, preceded by a Node stream.  Prints the result\nto the given stream.\n", "readmeFilename": "README.md", "users": {"andr": true, "mojaray2k": true}}