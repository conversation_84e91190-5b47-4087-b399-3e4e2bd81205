{"_id": "minizlib", "_rev": "58-2e438ed1765323c70b676576a736ee35", "name": "minizlib", "dist-tags": {"latest": "3.0.2"}, "versions": {"0.0.1": {"name": "minizlib", "version": "0.0.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@0.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "86c1f23d56bd35c2e1fcf045548e11c8ed6c231a", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-0.0.1.tgz", "integrity": "sha512-KROQh8o6KxUkoYOy+cAr8uEGpLEN0rvcJ9yhoUerstRZ3mirQxs3T4GJA+TeHnIGJzyn6firaLqZDFxuEGK2ig==", "signatures": [{"sig": "MEYCIQC+DLfm3Rxe4hp88vh28zeUJ/+Ay3Rth20Ye5OTzQy3UQIhAMNR7VxyJw0ytFtmsSn8othVpdPwR2iFYsYW8D9jTUOX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "86c1f23d56bd35c2e1fcf045548e11c8ed6c231a", "gitHead": "eeb244f223858fb95742540fcdff8020c4e13e03", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "A smaller, faster, zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"minipass": "^1.1.1"}, "devDependencies": {"tap": "^10.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-0.0.1.tgz_1490686822685_0.5977697225753218", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "minizlib", "version": "1.0.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.0.0", "maintainers": [{"name": "cee<PERSON><PERSON>", "email": "ceejce<PERSON>@gmail.com"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "a5b3994a671ad46c769af9905b7dc1cfe4aa1fb6", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.0.0.tgz", "integrity": "sha512-ND9e2gVqtoqk2sBSsq5vbY1axx2n4m5sHAUFXGk1SPWc5GFa4xNObFaKeHSzyV5VAQt9qLBpIjqVcb2hmA4zKQ==", "signatures": [{"sig": "MEUCICpsCeS8IGNsbWawpZvKa3JQnI0/fKujTua8UMgLlWkzAiEA3z1pngk+28P/XrFUhdB9Qps2GmtrtV65MONzML4pl04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "a5b3994a671ad46c769af9905b7dc1cfe4aa1fb6", "gitHead": "356f7243ac9a988b172b0a58074d05aac8351529", "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"minipass": "^1.1.6"}, "devDependencies": {"tap": "^10.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-1.0.0.tgz_1490775065011_0.09464420657604933", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.1": {"name": "minizlib", "version": "1.0.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.0.1", "maintainers": [{"name": "cee<PERSON><PERSON>", "email": "ceejce<PERSON>@gmail.com"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "038b4fc0dd85290fb4c94eb8a9c8254047985218", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.0.1.tgz", "integrity": "sha512-zDkCQomfcSRkBBNAF3Lsxy00b4mFXTd+kST1bxHvTf5FhsK8xlHKP5oc3VuqAo7ZtMJLbOPsdqYA+g+XPCM6XQ==", "signatures": [{"sig": "MEUCIQD3Szs/DfTkROhYObgSaqgsqP0T+AeGsTx0yKgqlWM7nQIgYN8yJ+nh/rEenOYpez1+cV4jjOI2BMrqNIkKN0Itw8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "038b4fc0dd85290fb4c94eb8a9c8254047985218", "gitHead": "89ef737a875a1176a60a6824b405c44d78eb9d5e", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"minipass": "^1.1.6"}, "devDependencies": {"tap": "^10.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-1.0.1.tgz_1490775112216_0.8146075017284602", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "minizlib", "version": "1.0.2", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.0.2", "maintainers": [{"name": "cee<PERSON><PERSON>", "email": "ceejce<PERSON>@gmail.com"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "6c4a4c624bee69905b0044b166ea1adfcd6581fe", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.0.2.tgz", "integrity": "sha512-Nn1NlBrwT/H19jZOSzAkiK5J+0kefOBBClisNA3bF9073iVgykoDQ5GNLdht92eGQzmyqlnucN3UeC8az9/Fyw==", "signatures": [{"sig": "MEYCIQCoOKGnNoMTAxKZD85Zrg4Nyj4/JPWRqa2XiC5iwUnDkQIhAKFPgBeW3kVU0NV936Nr2n6vrYUhPneZH8pVwqpURzMB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "constants.js"], "_shasum": "6c4a4c624bee69905b0044b166ea1adfcd6581fe", "gitHead": "666b33cf8158c12111046d94449eed53c9c9fab2", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"minipass": "^1.1.6"}, "devDependencies": {"tap": "^10.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-1.0.2.tgz_1490775174783_0.6375827312003821", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.3": {"name": "minizlib", "version": "1.0.3", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "d5c1abf77be154619952e253336eccab9b2a32f5", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.0.3.tgz", "integrity": "sha512-o3Z2Gf+6pyplapNa19Hsmf8vJvawkWYx5SsgmGKjp+HpOEWEAr4mSHsrr1yONVf6eW87Ug1lFZPmsh1vWjK8sw==", "signatures": [{"sig": "MEUCIQCPLhMbCLQ0DeHuqPyxT4aCqlO3yir2iGNUVBOAm8Kc8AIgS8m7mg39/42zb+P30msEHHtPdcdQDtBI54KSAcmanxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "constants.js"], "_shasum": "d5c1abf77be154619952e253336eccab9b2a32f5", "gitHead": "18adfc6e208468eae276617b34bb4dfaeec1f532", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"minipass": "^2.0.0"}, "devDependencies": {"tap": "^10.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-1.0.3.tgz_1493884866315_0.7689040366094559", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.4": {"name": "minizlib", "version": "1.0.4", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "8ebb51dd8bbe40b0126b5633dbb36b284a2f523c", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.0.4.tgz", "integrity": "sha512-sN4U9tIJtBRwKbwgFh9qJfrPIQ/GGTRr1MGqkgOeMTLy8/lM0FcWU//FqlnZ3Vb7gJ+Mxh3FOg1EklibdajbaQ==", "signatures": [{"sig": "MEUCIQCCE2yUQ/cxanYn42KxXGZUuJEhR7xmVMe6oKoQ4Z256wIgCyeHls+1u551ut3+BitPirGA2VrKvVJBmoBXnZiHR9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "constants.js"], "gitHead": "98bb59595f49abb12246e9bf91a1b96d9809768d", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"tap": "^10.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-1.0.4.tgz_1508312111191_0.25975665473379195", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "minizlib", "version": "1.1.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "11e13658ce46bc3a70a267aac58359d1e0c29ceb", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.1.0.tgz", "integrity": "sha512-4T6Ur/GctZ27nHfpt9THOdRZNgyJ9FZchYO1ceg5S8Q3DNLCKYy44nCZzgCJgcvx2UM8czmqak5BCxJMrq37lA==", "signatures": [{"sig": "MEQCIApMgp9U7yxVys2EDSq/7o5yz5FuyRVsVnffs1NwlQ5qAiAcFGzs2lZK7fTLX6Y5QXtDs9+qt44Gb5S31SwEWje62g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "constants.js"], "gitHead": "e10e489a9d7a41b52dccacb39bb22e078835ebed", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"minipass": "^2.2.1"}, "devDependencies": {"tap": "^10.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib-1.1.0.tgz_1513828974124_0.5935359639115632", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "minizlib", "version": "1.1.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "6734acc045a46e61d596a43bb9d9cd326e19cc42", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-TrfjCjk4jLhcJyGMYymBH6oTXcWjYbUAXTHDbtnWHjZC25h0cdajHuPE1zxb4DVmu8crfh+HwH/WMuyLG0nHBg==", "signatures": [{"sig": "MEUCICdH1zoFuPQfzronfe1ldh732NY/FWpoYBrLuW9Iko17AiEA5nbOZN0wKkmZSpX7lx0FUTj7QV551e+bV9ZMK8Z07oA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvjxVCRA9TVsSAnZWagAAxiwP/1Fioq4FlT1/T1EvRWLJ\nSdpN1jRYSe0VLtnUVeHKSyaXB+48PLkN+XynKdTlo5u2mwbY2LeETZCPSDGN\ns7Q/ueBEOkbPD49q9uhEm7locS4n40uz2bRJhyfS73zaT9/UH7mkXyAULmxA\nrcU6ZJ5TBTYK7DFGDxwcz146nNbT+xlClmpdb+HYRcSbK6V9iksx3U734x0n\n5AeTmZbwcrQdKaXMwzeHggrHKWdpUR+RtZS94FQFTofZEj7SdpqXpSbqaAUY\nTytNzCAAUjvC0/VZRsMjIfMIAvhl64bkYc1s2PuqAtxojeYYDGIqf02Sz4I3\n0wRcM2KPmEQTivEq4W7RiDCUOJNrcgAARCTqWUxwlVE3L6hNReXo5IIuUkPN\n6DFlswUQt6DodRWDfDJIJ2npkbdiYn5uEChkCZr0CzfEFWp+HXfbdWyNoLDI\n3VqAdcbJecTu6cR/A/w97xq0O4fiFDPNQLppg5h1ExfrTJfCUJTp+W5vz8CF\nfOov+oaBn2osNWE+zviErHmlyMNZsaBb82syHNrnihny6P1sA+DFEqLcYK9O\n8TY7MTVRWq4Q6esfRjpTxHIMvL37ImMuPQfU5YHqivDYmWy0CM4aDvWcS9KV\nq+NX98et99EgrOw91pKbAL8VP4cg89Y/nAqyrobPRUAq4TAxqIc851lpu4cn\nMfmw\r\n=PUvW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "814af72cf7ee5011814dbc6226b5961051ea5bc3", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"minipass": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^10.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.1.1_1539193940689_0.34679390777921637", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "minizlib", "version": "1.2.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "59517387478fd98d8017ed0299c6cb16cbd12da3", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-vQhkoouK/oKRVuFJynustmW3wrqZEXOrfbVVirvOVeglH4TNvIkcqiyojlIbbZYYDJZSbEKEXmDudg+tyRkm6g==", "signatures": [{"sig": "MEUCIHWalmM3ClLIgI0AHIUO8/QJFla/uFxdzojBKEt4oj5CAiEAqZUlUlC4TdmZd2ps1h5WQW/Gd4N2hn+TZJUPYsCDbEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCa/aCRA9TVsSAnZWagAAPuQP/0rwpM6+WDfOkEG5Ao+B\nljWJcDxsLSa/2VEg1Na2DtATNtold/yVY3xSXsE43/d0l6z8m6gtbZ2QZg8A\nSI0L+aE4l/mpmmLLtOoVfz9hB+/+VCXu+8w2XxW2fHPtLJEunb1k4cwHCu6A\ny6eUJc5R8iBi+Mrx2vBcN6XjZ9U6uE/0FXQgHsUPshKTork5FRQvNRM+KiHk\nTWmFUZ1WZNtFXT9JtD/1/BOyFJFi1IQSgsjEn3n0y9FCdyom14KFxKRv8pif\nLcsa6W0h6Rd3riZr/GmP5+rtWLmMiUDqhqqIsHJeECjVB4sFPCK7SywFx49h\njw7Xra+u+ufrCDNVYeqD+Tem7e1RUPiMQW5mxhpXSSVWNqarejqe7nPJdXPJ\nvQFWI7O1Y1m+pzm18BdHGga3lijz0rFonxl1vYBMO+PZUS0XNJse2eKmvubg\na0lW1pUwKCkPXeFt8toWrVZcGtRFvc3Ww4YcBIreuRJjT5G1QOD2F1J1efj6\nPY9MeMGWZ8AevRqhCNkXH9riuTzKKZTypI+aSy1FSzq17PRX4L/43EXKd5KN\nbzandAJy04lFsmNRDwcly0axiGEoA55oShUbJW4DCUUUdXgg1SNIxcIzaDx3\nxy6kmz1i6urVYPH9MmIKWGm9H+B4+kGYV6gLFHvX8DVn+g4s6+wk8byzCx33\n+EPG\r\n=M1Qu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "94087dacbcc5e49e4b55ccd3ee4901d64a0a37e1", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"minipass": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.2.0_1544138714049_0.5809128408831707", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "minizlib", "version": "1.2.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "dd27ea6136243c7c880684e8672bb3a45fd9b614", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-7+4oTUOWKg7AuL3vloEWekXY2/D20cevzsrNT2kGWm+39J9hGTCBv8VI5Pm5lXZ/o3/mdR4f8rflAPhnQb8mPA==", "signatures": [{"sig": "MEUCIQDPSLSMNcgTzuW3sZJrMEvpKpFdPdZpXMw/BI5DYM/WVAIgf/WQY4wgWzVCnVk1tI3/OX/yVd7benrc4i9/X351k+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCrVbCRA9TVsSAnZWagAA0moP/iuTcgV6U7x5Q7nGJV9/\ne4DOELm1SiwmY4N6TDK2o6nWHzaKMAjzYhnDtdtitlhecxjTUC6Ka65KQbVQ\n/ouXNpmTHfC+DhL+4xvVDkoZO/3aTCPNQ1QCf3JHgA+Lrmz0LD4eGvWSfWkq\nCxiJLJJ5W7SvDmzLasR2EHWlvScOd0ssDL1oZfa28dS6BXdPTwERm9iQLJ66\nI1fmmljV6oEbAzjgx1fw0iPFbCatktQtO1J0rDjiREIgYQOa9zA1DWxn3GWZ\naw5fAgQtxjh5MEsF+2s5FJSv5UcCmr9btUMzp5RW8lPMM26OIfZlT4Nr5TNE\njvlQ3zNcsJsu2sRZ9D0JtcEOJfVGrT63UqvEvFOiQUGVTOPLZFl/r2UogBbL\n5eNpcUCqY+gALQpXq24nl3OCXdMQ4QngFSqgaUAY+lqy1dzXIxZa+lBnt4ny\nHvm0nZnxOgZgkzWGyLavLLvkhSA1sjIjWpXolVfwlVdj/yJBsV0ZE7uvbcSJ\nKcdRe1pz75SPQyCdzDgmqPKb4FKraiGazQQWNMSrQa3P4VEl9xin1d7sN/m4\nSjATLEQr3relzwtiW9idJrpCsieSblBOf6S/ukL1/moQVdMGSqJjTGuJwYxE\niekEgVsdnzXg3nTAx/tOWfKOVjljY4B31XfU/TWNW2bCrjaIrxPerfHoESzG\n/NQh\r\n=te1d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "ad2e969851c3573eff84a5ddfe906344c814581b", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"minipass": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.2.1_1544205658697_0.07448144618627572", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "minizlib", "version": "1.2.2", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.2.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "6f0ccc82fa53e1bf2ff145f220d2da9fa6e3a166", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.2.2.tgz", "fileCount": 5, "integrity": "sha512-hR3At21uSrsjjDTWrbu0IMLTpnkpv8IIMFDFaoz43Tmu4LkmAXfH44vNNzpTnf+OAQQCHrb91y/wc2J4x5XgSQ==", "signatures": [{"sig": "MEUCIQDFv81idMmPPEMGAAo2BV/PsA9ZiR818Zn5w3cJ7PUKSwIgRRDMSdKcNU8+uc4JsFBMdmKv04L7tU8VWOBAE7trT94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdeqooCRA9TVsSAnZWagAAStcP/0K6BxXoJ79GwHfk6e69\nwXfKfuNZ0erL6SIaoGwPHHW4TEC9B5/YWTM8GeE07dmxtBob4xZP2trm1f7l\n48QPtmmHUqVIKW0WcfPR/jixyOBfMCWJyxa3YhdR97Ul87/Mya2SPccliFL0\n5z1LwDWMCYcDcFQSLY6M41npE8NMw5uI2fIIeAXSdNLmWYGc8CLdWeCDPQmb\nrxpQZdQdcg0Z2YX1Wb+IMu6/fxNaIezthD4qUJzmZ5q0reZcRvBTUbdCr8Y0\nhRReamqBNWmmvMMmc0CJkaALxsCl0NWLWyHi2BnMF9XVe1i6Lv4zO6u47ucD\n7gkIngwF5piMt3holgnFn5JyvYo2Lz0c/PQ88AL+Di7zJHZezu8wzgaTUH/7\nm4a7kNPPcgwzvCynhFlVjSfLq07aDLeBvZ6zn1iMO0W+bTDjHcqninR+FPWz\ndPBhL2IOJ985kSQ725GoVaaAba2OZ8JGalhcRU+kiyg+xVk5Jsegj84iwMTW\nC1YYo0zUniKdV3uKIOilfi0aEo6jUuBI6wF16x0HWdY7tluro56Zskq7+7qz\nqV/NO97Z7NAB2qgNpELeMW2zJx7jhDhBpScDjZCrnGvwOWeWra4wRciJ+PHr\nF1OtqE+f+AldXIskeTKeMQH1pQDGX7HQiLzsnJ4N/IyAZIhgrcKQ51kBrtPO\nuIU/\r\n=uu9P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "199410f31c7537f85712bf82d31746090d313d1c", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"minipass": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.2.2_1568320039595_0.05086099863204718", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "minizlib", "version": "1.3.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "d8735da9d41e874c21815c056ed1be4eec18ffbb", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.3.0.tgz", "fileCount": 5, "integrity": "sha512-IniAKI+ubC16nctWWvXeatNvrShLjoMCVENNeUmwE0q3C7sGzAjFgjkv4ntaZ3zgvBciUuHohuXH20YvGpylgw==", "signatures": [{"sig": "MEUCIG8oOZ5vMBixPXrR5n9FfgSTQt5sDAvj8CZowD9rOFPeAiEAkrlorqNQfCljuBuhC3aK9yWQ1XCmeucD8W4IykLuw2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjFCnCRA9TVsSAnZWagAAvYYP/Anm4WB4uj6/92CH61cc\nP0a9Kv/RzRNSg7xskUENDvSnG8Wjg6B5D9J1cNMa5J30NkzQzbcQ/BaMleMe\nrvJy7RMSrEc6MBz2RTvUj9NZECg56YXt4s5mUd9cuyJUHjRFOooKfy3PhF3r\ntv5J0ixkbKKwy7+HLFtb2JTUPSJz+TOmzPJboDt2Ux5WRF+VP9PYSwzNtEd8\nD0a66Mf1yR/bBIuuE492iixTqKDF1K97gTvXAp1Yq5cN/N7tnvzNpZ8PZ2jq\nUCVEO9hMHtVNuw9vcTIi3c+avNeWkSfuhRsuqnDuDAGDQS8HbtJ+YbSyE0l+\nx16tJCElimhSUSzSf4ajrGDza2+BpxxryIj3s7CTav1IlK9Y3y/lb59k93fu\nFXFfxROjXw8Um687Yj0FVmg/irMlQBA9oiqJkUIYXeMwT1R54Mcf9735z+R8\nkeCWdNJp7D2AmtuuZUjO90dUTZLa5jY9G1img4Te5uBqf73Z23hEoccsJ7Wv\n582iQ/AoRHjHLvKVbNAsge3CJIBPd7bJPG8L329I54zf0m1xmEAwTsegsGta\nUvB82jHJnjAHUil/z0Om57FHYi3vxvcLALQdHfSqWfN8ppVp6A3jyKg8Tf5u\nfmLIZc8OP/MfDWzp3E8fyZzxdM9g4NU/30zsLt+hIrhjjVtFXaRnwf7Pyc7o\nPG9R\r\n=hs4d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d11127c699251418b585ea9bcccc06bcf870b47a", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"minipass": "^2.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.3.0_1569476774305_0.025062827598726578", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "minizlib", "version": "1.3.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.3.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "7335640a57f584320b4fcf41069082c442bc6bf7", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.3.1.tgz", "fileCount": 5, "integrity": "sha512-8AgjrT7C8U/HQWM+02YJHLPh4BypAhc5pFddr0nCcowNy1Hj0hmKPMq9WkjBMn0rtUg3ia30MkCexdd1pTiTIA==", "signatures": [{"sig": "MEUCIQDc7Rt5d1UNI5eoy5vzE//CPwkwMGr3R6IanNur15ZncQIgNXSFs5GMtCDrcZI1bPhRAJSTsjOOVoI35f6g1LVQJJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjFT0CRA9TVsSAnZWagAAaScP/0kdHGrr2BsdNFRlR8H+\nP9mbQCs+HorIAyZZVeaX5PqgkR35YyXR+hRoiXMjlY+/b1TqWeTLPKm0XcuI\nsSaa8BZxTQCkthha5aS9vU8aStdRW26nrWBbRH3jMKmNNvyiFsdjSDp3YDf1\nHrcunbvTRmiln8oW9j2gAcdYn8/RZ/Vjxol1fANvycNpp5o1G5fWLA7oNYbp\nhF3MahXcxar0KscsvO4erVALs3Fgq362xIK5CEM/BcN6l2zLGMSnHEvta1Jw\nZu3uXYTqgyGUBXmlXxODRSkVzJYxuiDE24EwSx8dCiID5VckD3hMnHJPIC8d\npZTZl/LTiGZL7sGNb20Fov/nlAVpO9l7Gdr8B43tMvp3DZGE4PHgYKTUIuVu\nVOuuH5lc8hQpvaGxIiy9c9lqE49TTxPYPbQCatYFTqTqtyMfBHSXprXi7Vy/\ndkVWGjm6AJGlQv3mYIHChfMqieO1o4NE+/cCPDdiWUXdLOM/WYbh40BX38sL\nsw2Ob9VA1CvQNP+jbuY4atltHThqrhfLOwWer/jp3kOnHjnUg8k1e+PheGDE\n5sdlFQj9U60naEYOz/QGeL7zDOJjHsXejOX1z1OmVWtlZ97reXVAID/fxPmS\nKXIoANLWLcqeqc5TwwynN3kq9QQbZWVWJCSRytujMeqf4VqMFxhZ20CRuypq\n8Vhd\r\n=Mi/L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2dc3a10b12918a77223c8733930ff5e5ae14ce20", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"minipass": "^2.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.3.1_1569477875661_0.24884649880068666", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "minizlib", "version": "1.3.2", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.3.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "5d24764998f98112586f7e566bd4c0999769dad4", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.3.2.tgz", "fileCount": 5, "integrity": "sha512-lsNFqSHdJ21EwKzCp12HHJGxSMtHkCW1EMA9cceG3MkMNARjuWotZnMe3NKNshAvFXpm4loZqmYsCmRwhS2JMw==", "signatures": [{"sig": "MEQCIAsSQBSaSukTXQH500GENKeHEiMG+NcGk3sMGPFDgpSaAiAMrYJwbxxJ4c1HGtJ5zBJtx/N/eeuwXZlJxDZfE1o+9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjNxoCRA9TVsSAnZWagAAW9QP/3IOBhKzDM/r4Zkx1BEt\n/ASatWaYJ4KQ0jSaFcfg+4+h07rgInuARXTaKgWTFWRM+6xKnjR4/vz9iQJ2\nirgD/jweQVbtAQRnPhwdsNcTVuXdMWKYn35keGeRXVUiIBdfEPH7YI1GeMxq\n6kVwy8n8Aps6bvQSb5DPGc03qfceMTJCi9uOoJ6TQtSOj7Q/Gihum5xw6l/l\nQ2Bz1NjxYNon7vsAnmKZUM4d7W6OVjOYdn+Qi0fNThMU5ZB7sCkn8G5dv5zN\n/hFrrZgemluoHlthQXwWyqtGCDq/rDHKO2GvgxlwZyPwuCRlt+8AFOS7zml/\nWU1H9QwmZizINxxQ5xd+kpRp4p+Km9ENW0MBjnoQh06y7pnBeZz+mMe0m5ji\ncfVOSz+wrvmvKRnTY3AQMQb67+RAW4fxC5qOSSQegRvitB4Tt9/LygJyFd+G\ndH+t1mTn59w8G+ljtAYCi2VQ/at8IDoFP1xq9PVhJtcuSdCpUHWHPsJ6LKkE\nutPc8xxXdLGrWJh9TfmTNp+z09TS4e5tFwjZY2lhy7eZr17B2wjVmYfSxG6d\npA+6xFNoLqBornRjOTmKBY9ezSH5F6WAAAl8kM4qDJyvwSDJGg58wVAThY/X\n1msF/hsdikzLbe2lMGbtswgfPKFtdLH2KlV1uuCE2bNjF8CC94DFd/ISiIQs\nSZ3N\r\n=EzLV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e11b0571bb5a3fcf69f5099f908c1d6e228ee2fd", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"minipass": "^2.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.3.2_1569512552274_0.7311787344216469", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "minizlib", "version": "1.3.3", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@1.3.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "2290de96818a34c29551c8a8d301216bd65a861d", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-1.3.3.tgz", "fileCount": 5, "integrity": "sha512-6ZYMOEnmVsdCeTJVE0W9ZD+pVnE8h9Hma/iOwwRDsdQoePpoX56/8B6z3P9VNwppJuBKNRuFDRNRqRWexT9G9Q==", "signatures": [{"sig": "MEUCICHZ4QD1rDAe2BgZwSGZD2eOKQBmKv5VxprlkQkfF3IhAiEAo9VKDXaTMcPUO9JA2KUT/B7fv8mp/Ngedz+YIaxDqqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmEJCRA9TVsSAnZWagAAQWcP/1maH408C4vMFnhvD5qK\nyTaEHXOJyaxe4nwEmicnDqpeF+siJSNrwMf86Z0EVXEGzrrT0fjKLDqjY0vb\ntnFr5s23ayb9bPFT54VFZdmKBRDSjwl6ix6jDa6yfVL0QTYkhJaJ15xtbpFK\nvD62Lb+e92Fg0sL01dRpMAh818g4X/FIruTjeGe1NEs3PIx/6QC4/4rq/f2U\nmMcOGGBYIeFj90kO6zD6P2QOpCxoK2OTtqrampfwETLmXQN+ILf89TG5lyFw\ngVLc0RwF/1VHHb3WjfPGIrkkuMhM9MKOauvNcY+WnxalJ+nsdNu0ISP6iGvA\nRV8mYTQZKakUIt8sOh54KsZN9ByqOiAHed2eUaf4EWoENnO4xowfdGmwir61\nxUVrcz5COayZy4uUG976xExxQevb68JMOgH9VRlcl4b2d/tvQGeNmn0R/pYW\nLEY+oeE02XdDP57xvmO+zmwJmSeRmNh7G6pgJysNUtLLRxHxejklnGZRBNyY\nhKoL0rJKpJk5/RpoKS4HRXtfyo/GMWLcjnzlOqyGDAvYbJW4uCupn8NVSsDm\n12oKfUoWaRcHbGR2f2qlNBnDMfLn8b3FeILn1/iOO07WyPH7sZKLyJBq+Xb8\nGSbzBdpGP9oieCVJ5yLwR7+nUO23EG7ijNUModw4JWITS0nLBCQsfmyyQLjl\n0I0b\r\n=hrTW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "5e2825db6fb4ee20b63dc08ab12f4415b8ed71d4", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"minipass": "^2.9.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_1.3.3_1569874185075_0.18396379860852585", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "minizlib", "version": "2.0.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@2.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "1c26c23055feaaaf8f8a86b166819d8ad906e697", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-8f6fgftVu7S2bYKe4Ks9jS5ViU/3As9kEmCg6p9T5OCDAzxVooUmhhSqGar19ZR07xh0rknj9ZmGme0bv0d+Nw==", "signatures": [{"sig": "MEQCICyTiOe6bS6N1JgvETyJmzj2JhPUheUTxe2sTnba0FUTAiBhCW5+JTeCQ1MQp3H3HRMDDYO9K7s5WMst78RBw/IAbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmL7CRA9TVsSAnZWagAAUYkQAJxbzuhUzKnDEGQE9md6\nMtgP2MfpvCw3kRj/uRwU/zmq6iFYuE3rzMw33IHS5uczzRMi2e3eopWRl2CQ\n5KdfJ2Ub36oTReAt0v9Zd+/21vedxDIRpnP4QwO/BtNz1NxCgPoIePD0oc9B\nAa6X0UwVPa5KE/AolzjY6YRD42doetq5bLNhMZ1KsvV1AQ8hEGOrrfWZI4Op\n1wbVxLkJXV7tQwlKPYO6TqYXUwa9OhY5khKre2M4m8TEQFwDd2cnLhR2aJc/\nev9ddxgTU/ZIE42mt6rwIDdHaAJG9Lpm8tKyOlN2m6Q8ib00WbSA2bocM3QN\nmxVgsYvAIlPwlCfDrT+CcC4xpQkSsq2DO9hu4e9dWJFxE33rjJbdu4z1mf8I\nw16XOk504gSMOdi34td1nkvAlTLL1PIUtZDs0pW9AFaEQO3TW35nikUnPk12\nzcnQUVkm3zxcGbk/HuXOOzvrBBSOP4GQd4PNXikZeIlSM9oKZ2g15MovWJxv\nh+U03UCy4uXrz5QHP22tmZZG1QixqjHZUcmH2/uMFSpsFCTOtKQK7mVFuEG0\nxHFbdpAZ01ZTQyvaq2g520kZsGFPr7siedBDwzpoG1ri5NdYnHxAC1eD+EbU\nu8Ra4HskUO/C0sEp3a2DCrc9UAxS9xJ5pTcTlZVE/GgFY1AlxmnD1wSSYeSh\n+KvC\r\n=e9w0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "51e62f213076a941d5bebbecc7cab702c6b5aaf7", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"yallist": "^4.0.0", "minipass": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_2.0.0_1569874683321_0.8665600631616883", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "minizlib", "version": "2.1.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@2.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "fd52c645301ef09a63a2c209697c294c6ce02cf3", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-EzTZN/fjSvifSX0SlqUERCN39o6T40AMarPbv0MrarSFtIITCBh7bi+dU8nxGFHuqs9jdIAeoYoKuQAAASsPPA==", "signatures": [{"sig": "MEUCIQCZ6POqNz64f5Sfc31qicg3DkTUM8whrQMwNICFCAaWkgIgf+mBxcwh/DmgiTp5txy/P42f3//0Evxy9P1QKhs0CNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmDMICRA9TVsSAnZWagAABA4P/jtl/CKsJNfDGIu42CYz\nF1FuFyVWgTSSELICuqFRc75qZs5zvavwTJ/By937chUOa1N+dXzIVqGqOSow\nqZfNnQ9LjSg0/fa1c+TjpfmKVbp5MuNiTFMQEehxuezAuF1C/RR9/dXvD6bO\nodVZ/wy+gfCHz4HZIo2OzF6sadPIRvfse3nGtyIEvjBBCBwu+FtJ5vlmUIOz\nCeMB3PZGb+gjtF7f432xQv6JmIy3xBmlwdkT/oq61Szik0hzZdDFtFL3AYnv\n1GA/KtYAvYMy1jiLSjCdY4ZVSAvj/6Dz18aUvd8AthVOZluduJxY3L19ICzx\nB4jeE9wUzl1Cx02i2iswIyXHR3mYyg3wtN1sev3nSpMLtMt0rGeCIshdwk3W\nuO78Bpu5Ng5s+1SGm5zGNbuAw5mLMg1PGIM8X/Q+oLCbmdyYRRbzBKj2fN7K\nbKmvzCEFlRUyp0bG7cXiMv4PPlMiLIYf5C1IldaJ4iDVZ5fh4J68+96DIico\npJxm/PsTwwvg2RPl3V2C+aVbEHkSsAcB0LjIadKsVy7kMk853p4hHU9rJ3CT\nsQ3Y0EP7kTujB57wDH6bKHbDexTMDgVqDwu99iyQbi7LsB6ia1SKFD08/Jdv\nBHApJpE9e6IfmhsFYUrm6Uzvgd5mgf+rGhxsvM+bkiQHYxRltM2JUtn+KkaS\ncjAO\r\n=chmV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "7972a022540d40f62ba8fd3e8038c057773e981c", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"yallist": "^4.0.0", "minipass": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_2.1.0_1570255623624_0.5196261615937616", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "minizlib", "version": "2.1.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@2.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "e7c4598f8caf81f4958d759b3fda20de130ae390", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-JDuq+CU5UgBMfeExTb0iQpxCiH3EsPf4ypyLBQaBqY1EMwwp39mapJM2kwTQC0tf+dp5Cs3Mwy/N4CrrdvZzQw==", "signatures": [{"sig": "MEUCIA4FfWkaTdEnyNbegJHBvBNXySz35eypmnJAR+rQ73E3AiEAmk3ztRAHsC08ZTHaB6417tfWhJcdCkuemK1Apgjwaug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNwByCRA9TVsSAnZWagAAHFIP/2TZ6x4PjbREj1dXzdx8\nZS2T5HHD44j8CmwKsWwf6gyhiImV3aFYmSdM0WjQ6t0+jyt/RfG+Svk63KUl\n1GMJU5YOOf8GdSvUSIMKYG8bNNbAuFLcDOXwbR2h9FrGGc7khR7Xt3DdtljD\nAqhXDcpU0cdf28P4sutbaTyMU2pU6sjmwUrxZEJYfP33t50AdFNzBImFZuTm\nkQs7bjj1NSDfh8jHPNjaI/4xxWG39KLjvr1SWdypEHkahkaS04IzksLbhxIU\nrM+QYMO3uneYjUc3+99BsRMw20JE6h4MSpskyFeQOBuu7sD3cGly6bdqnhKK\nNscJ8LXsZJYtfQlJIJftXm3qn1hCyYBwlXJuvCGc3YzktNFUq4nls4qwX5Y1\n9O8sxL0KtzPVO/MbJxJIcBrmNRwQEc4PeuYGkzDcTGfxESaconhJf8DtxUQ4\nYpRRE/gJ/Lmd4JmBczUg2VRP97nMYoMt9rQLlZ9oKuiB3NF1hUKkzDhbdMA5\ny1WCp8IWtUQDXH0jaHmuJ6NCfmieAHEO7iN4BbWpf3Q2cBcK6x9DuCAOjbvl\n4yPyZ0FRPFUMGWs4JAixI9UJOch4hkb2smORvf2vcZrSZRsIioAu++3E6KNG\nnePIJMsMqdX28Q1Undy4fLL97QWDhdL7XBnZPKJMQAry/dTFw9jJNOvfVjpI\nKSLt\r\n=4UHq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "ec3483b05b67753a89f6466494076cf8609e6e17", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"yallist": "^4.0.0", "minipass": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_2.1.1_1597440111476_0.4159523791162707", "host": "s3://npm-registry-packages"}}, "2.1.1-testing-publish-with-beta-v7-npm.0": {"name": "minizlib", "version": "2.1.1-testing-publish-with-beta-v7-npm.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@2.1.1-testing-publish-with-beta-v7-npm.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "6a8253c329f8e12bda15c24fab29cc0a0142b629", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.1-testing-publish-with-beta-v7-npm.0.tgz", "fileCount": 5, "integrity": "sha512-YHdsiBOw5jDd2+Sav0y9V1x+UeKMoP23b8wXjk3yOGPpcdHfwjkR/yUK5qP+bTuaPZB2bDTw0+GtOOsJfzhH/A==", "signatures": [{"sig": "MEUCIQCziUgd8KDykqx+7jCY8uXN679ztqmgvoO7IZHMjgTqIgIgQXTTLrZM6F0d985a43U8N1R1Cnzf6ZjuQZKZWxi1QW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNwF1CRA9TVsSAnZWagAAXLEP/Rj22pIIyjXx2USKCQyx\nv6S2loEnqvVcmpyeWxluhPT1PJSE9cUobj7q21E12wvtj7UULkIHSCIQbXj5\nzMr6klQkAQvwve0Ui9+cTdZ1pGPaDtwQGpXVKfueWuhx37/FyJGR/GvLy64v\nLB+7s81Tx8OZ1Zn0+FGpiFV7mKlamz7USa0CGDZFSU1ourHhLqqYU25FSfIi\nObMD/IFmyfYvNpSD+tOs8HVQWlEToxA5GIn3a0U471lkG4qQRWcHTxFPkV/L\nVUtfNiR1+e+RzrYuNcxq6b5jkpfWoKAtVRkVrvbwpx4qaGZa+2i1CuuXRd73\nbZjyg5LoMLo0teikcW+rr5+1avIT02zuw9Zmxso9hdkPHG4+/xZbZkC5ieAF\nMFLD4JJj+Jmr4Q1lt7tbPEGlifm47FT8cDynaj7qsPMb4a991tqdSTdrZ7uH\nR2NYoNtKzq9r+nTNJtYUn/AwZfb/2gfL0hZxMOgVW2Li1BHxXACPCDDI2kw+\nmXMH4n8B7TJZBPJpARAlQpuuFdqmQr8aojRI19za99i4CqksoqIGjnbMMfJC\nGV4i4nM7WMVBYlX1DaF6PuQnUL8fwV67cCJbkqAhMdg1mFMXZ3mCPZzinVNr\n7YSS85XOyjWt1ixre6P7ORMGn+W8OexKXHekAdI1ovKSvmWW+F4D541BHuGc\nf3Jm\r\n=+1Ls\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "ec3483b05b67753a89f6466494076cf8609e6e17", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "7.0.0-beta.4", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"yallist": "^4.0.0", "minipass": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_2.1.1-testing-publish-with-beta-v7-npm.0_1597440370683_0.035692628993942765", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "minizlib", "version": "2.1.2", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@2.1.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "e90d3466ba209b932451508a11ce3d3632145931", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "fileCount": 5, "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "signatures": [{"sig": "MEQCIFFUhtHPTvBoEDsGT0dRfPAyJBQIXN7KLCr2qbTbuAERAiAOSmdDwiA7whgyuc+cg/HUG68h6/+b2JAJuw73+8DsrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNxNuCRA9TVsSAnZWagAASdIP/RRnOjk1e2hmhdyLX58h\nTn+WzhsGAp0FttFn3962ib19L57ynxj32+TpbrO37TJbmLSDAjFZ4hOWPexk\n4aSkfoCzNzeoDUh8CH8cXMGalPMFSNAcoj/V9p2e4JBQbRWgT0B+XyRFTFKR\n8B9Zza+7fZBfiAZtDbUkxBr4JCSCuc0bLCOoeoqt+8WxwRmZvC8/CjsyO3uP\nNfMP/uMi0+zv0+78+L8/oghYuoEEUv5KsEFIne8GXqCik7PZFwbswfZtlI1d\nbXArRr8pVZvaK8++OaKN8ppk/ZHCmyvbBySXtznsy2++KVdEG0mg7SR+kg1y\nqwNdZWnqalFj9XHayqDZ97sdmx4SscGTeZwpx/LBqXVDW59v8rX9SFQ3as8p\n9Uy9KxMjk1asDriPS+aaJjbM27x2VSeBv+hFcmIsXv6JjdQSyCOcM1Kpk7hD\n8w0q5LQTvuHPGiqdrLAwLu1JH2aEHPr/jrYrEms5Q6eeYHCKinKrxWcpVkHa\nwGlvB2KXo6CyW3SWnpSlSToCjzDEuCKcRpyhZffLNSpGdEmI49PbpGteaTO1\ngc2yLV4q5uQNhQES7Lovh1/FCk+kcAN6kFLtEeaYsK/G5/VDbj3Mv6ovEC+O\nVqs5vBbkA7Pjw48Uuva1PYBCxPw3O/K2C9TP0O3TWyR101VVhTK5Dhvv5fLk\nROVN\r\n=0klg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "433c0caa0a3ba92a31623025c4ac386836649b09", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "7.0.0-beta.4", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"yallist": "^4.0.0", "minipass": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_2.1.2_1597444973743_0.32588819043211026", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "minizlib", "version": "3.0.0", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@3.0.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "021909a23d6ed855d1ca192192930b412648940e", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.0.tgz", "fileCount": 21, "integrity": "sha512-LOM/D1a1wbYVnC3eNpqAKlOh++jpJFOMtV7nXx/xaG6l5V43wQglxW3f0a6RVLnSHI+FDnlFEwm+IDB6t4IP0A==", "signatures": [{"sig": "MEUCIApJfW5PrADYVxAN40DOVx34m1pvTYVdb28Nxyys8tmmAiEAjaiIEQHl8WJqWfxQUzdBjYpqAD9tu9Oyak7ynXA2ljw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107964}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">= 18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "gitHead": "9642ae1edd3c1613b1e6c96452d97507d884cbae", "scripts": {"test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"rimraf": "^5.0.5", "minipass": "^7.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.1", "tshy": "^1.12.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.12", "@types/node": "^20.11.29"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_3.0.0_1710817219131_0.2935235277815398", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "minizlib", "version": "3.0.1", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "minizlib@3.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/minizlib#readme", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "dist": {"shasum": "46d5329d1eb3c83924eff1d3b858ca0a31581012", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.1.tgz", "fileCount": 21, "integrity": "sha512-umcy022ILvb5/3Djuu8LWeqUa8D68JaBzlttKeMWen48SjabqS3iY5w/vzeMzMUNhLDifyhbOwKDSznB1vvrwg==", "signatures": [{"sig": "MEUCIH3P2K7ntuct62g5BC7WNoJ44dBppfMWuGpgPdhz/lykAiEA8DXD3Flj/Sa7A+ZWWeqOdBfFn/lofmUhPqzEVxU/jok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107996}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">= 18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "gitHead": "3412623e9470bb72827c8a61e685140b5ee7b8a0", "scripts": {"test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 75, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/minizlib.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"rimraf": "^5.0.5", "minipass": "^7.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.1", "tshy": "^1.12.0", "mkdirp": "^3.0.1", "typedoc": "^0.25.12", "@types/node": "^20.11.29"}, "_npmOperationalInternal": {"tmp": "tmp/minizlib_3.0.1_1712447085360_0.583390192639742", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "minizlib", "version": "3.0.2", "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "main": "./dist/commonjs/index.js", "dependencies": {"minipass": "^7.1.2"}, "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "devDependencies": {"@types/node": "^22.13.14", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.1"}, "engines": {"node": ">= 18"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "module": "./dist/esm/index.js", "_id": "minizlib@3.0.2", "gitHead": "c77e92c29633b2a5bd19c9912c4c72929523540b", "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "homepage": "https://github.com/isaacs/minizlib#readme", "_nodeVersion": "22.14.0", "_npmVersion": "11.2.0", "dist": {"integrity": "sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==", "shasum": "f33d638eb279f664439aa38dc5f91607468cb574", "tarball": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz", "fileCount": 21, "unpackedSize": 110996, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQC0qybrna9EFhvLvutAT5dhLmQROWo0+xZ0ePwczoHLwgIgA0lkOja3EwfW0D4bullHB9mixjtNPlUMeMDiLyYXlIY="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/minizlib_3.0.2_1743439189362_0.970927312976229"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-03-28T07:40:22.993Z", "modified": "2025-03-31T16:39:49.713Z", "0.0.1": "2017-03-28T07:40:22.993Z", "1.0.0": "2017-03-29T08:11:06.728Z", "1.0.1": "2017-03-29T08:11:52.460Z", "1.0.2": "2017-03-29T08:12:55.022Z", "1.0.3": "2017-05-04T08:01:06.591Z", "1.0.4": "2017-10-18T07:35:11.315Z", "1.1.0": "2017-12-21T04:02:54.335Z", "1.1.1": "2018-10-10T17:52:20.823Z", "1.2.0": "2018-12-06T23:25:14.139Z", "1.2.1": "2018-12-07T18:00:58.925Z", "1.2.2": "2019-09-12T20:27:19.882Z", "1.3.0": "2019-09-26T05:46:14.498Z", "1.3.1": "2019-09-26T06:04:35.808Z", "1.3.2": "2019-09-26T15:42:32.412Z", "1.3.3": "2019-09-30T20:09:45.317Z", "2.0.0": "2019-09-30T20:18:03.436Z", "2.1.0": "2019-10-05T06:07:03.772Z", "2.1.1": "2020-08-14T21:21:51.605Z", "2.1.1-testing-publish-with-beta-v7-npm.0": "2020-08-14T21:26:10.820Z", "2.1.2": "2020-08-14T22:42:53.986Z", "3.0.0": "2024-03-19T03:00:19.304Z", "3.0.1": "2024-04-06T23:44:45.539Z", "3.0.2": "2025-03-31T16:39:49.530Z"}, "bugs": {"url": "https://github.com/isaacs/minizlib/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "homepage": "https://github.com/isaacs/minizlib#readme", "keywords": ["zlib", "gzip", "gunzip", "deflate", "inflate", "compression", "zip", "unzip"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/minizlib.git"}, "description": "A small fast zlib stream built on [minipass](http://npm.im/minipass) and Node.js's zlib binding.", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# minizlib\n\nA fast zlib stream built on [minipass](http://npm.im/minipass) and\nNode.js's zlib binding.\n\nThis module was created to serve the needs of\n[node-tar](http://npm.im/tar) and\n[minipass-fetch](http://npm.im/minipass-fetch).\n\nBrotli is supported in versions of node with a Brotli binding.\n\n## How does this differ from the streams in `'node:zlib'`?\n\nFirst, there are no convenience methods to compress or decompress a\nbuffer.  If you want those, use the built-in `zlib` module.  This is\nonly streams.  That being said, Minipass streams to make it fairly easy to\nuse as one-liners: `new zlib.Deflate().end(data).read()` will return the\ndeflate compressed result.\n\nThis module compresses and decompresses the data as fast as you feed\nit in.  It is synchronous, and runs on the main process thread.  Zlib\nand Brotli operations can be high CPU, but they're very fast, and doing it\nthis way means much less bookkeeping and artificial deferral.\n\nNode's built in zlib streams are built on top of `stream.Transform`.\nThey do the maximally safe thing with respect to consistent\nasynchrony, buffering, and backpressure.\n\nSee [Minipass](http://npm.im/minipass) for more on the differences between\nNode.js core streams and Minipass streams, and the convenience methods\nprovided by that class.\n\n## Classes\n\n- Deflate\n- Inflate\n- Gzip\n- Gunzip\n- DeflateRaw\n- InflateRaw\n- Unzip\n- BrotliCompress (Node v10 and higher)\n- BrotliDecompress (Node v10 and higher)\n\n## USAGE\n\n```js\nimport { BrotliDecompress } from 'minizlib'\n// or: const BrotliDecompress = require('minizlib')\n\nconst input = sourceOfCompressedData()\nconst decode = new BrotliDecompress()\nconst output = whereToWriteTheDecodedData()\ninput.pipe(decode).pipe(output)\n```\n\n## REPRODUCIBLE BUILDS\n\nTo create reproducible gzip compressed files across different operating\nsystems, set `portable: true` in the options.  This causes minizlib to set\nthe `OS` indicator in byte 9 of the extended gzip header to `0xFF` for\n'unknown'.\n", "readmeFilename": "README.md"}