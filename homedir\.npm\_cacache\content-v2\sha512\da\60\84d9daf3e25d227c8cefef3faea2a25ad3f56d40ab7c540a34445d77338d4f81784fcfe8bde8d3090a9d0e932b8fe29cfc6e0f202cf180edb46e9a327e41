{"_id": "verror", "_rev": "139-e47af82ee8ed63eb790a1e34c77d6f2b", "name": "verror", "dist-tags": {"latest": "1.10.1"}, "versions": {"1.0.0": {"name": "verror", "version": "1.0.0", "_id": "verror@1.0.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "5cb98dd3b334144679ed2f9df5d300a2053a71ed", "tarball": "https://registry.npmjs.org/verror/-/verror-1.0.0.tgz", "integrity": "sha512-2FBnEvs5D0J+O/QuukjZ72bAmPAyqugI2DZtZ7zOsP8bkYDApsgIOrdyDgtUgsY8ERGu6p1Xkmjw81jWuKnHJA==", "signatures": [{"sig": "MEQCIG/7C3fTsJt46s0+3+slREzVT0j6bjIVUmsxal+OrmvpAiAOfaxyTgTbW4QFDrK7Te8ll6dY3Rd+zOv3sQXaQlvh7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"extsprintf": "1.0.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.1": {"name": "verror", "version": "1.0.1", "_id": "verror@1.0.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "758d8fb57cc988fdd744a4186d87ed8ab691e7c7", "tarball": "https://registry.npmjs.org/verror/-/verror-1.0.1.tgz", "integrity": "sha512-+HJ1IfgNb7BbttuuoBgl7zoTSJU8OU0gdG2JLWIVOIbV4U6SF4jwwjwUZ3Aw8riDk8eCPLOEcReZBAiyyTdBgA==", "signatures": [{"sig": "MEUCIQD7uX0nPbTlrKzjfWkgtS6mmuN+o39/phVX0NMif3H9rQIgLaOzv30eqRdY82ou8mvxkovTU7cJBWsSZ1m5xSmLN3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.0.94", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "v0.4.2", "dependencies": {"extsprintf": "1.0.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": false}, "1.1.0": {"name": "verror", "version": "1.1.0", "_id": "verror@1.1.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "2a4b4eb14a207051e75a6f94ee51315bf173a1b0", "tarball": "https://registry.npmjs.org/verror/-/verror-1.1.0.tgz", "integrity": "sha512-5X/HjC1yBdrpYrQvjUC55HB1CuRPyk1b+Fz0Rpp60fhQR9aE4hcjnRwFfPNF7SNwoCb5fMrjGHr7Gla0zUerXA==", "signatures": [{"sig": "MEQCIEKh8CPIbAIfVkdiIQtx91/YwqaPj2O+NeX3eo5mvkIqAiAG8/5zIXdGJaw+aN6pyksjbDtUVMeYifig/8MUDJrV6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "v0.6.10", "dependencies": {"extsprintf": "1.0.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.2.0": {"name": "verror", "version": "1.2.0", "_id": "verror@1.2.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "0bfad0cb643d50ddb02c517cecae9aae6965f28a", "tarball": "https://registry.npmjs.org/verror/-/verror-1.2.0.tgz", "integrity": "sha512-4l6WTICRcWDoNSHVfDgdaW4f0mGTpYHHRfcea1RQ5/WbADNx7KkQZBHLr5h7BqLRVSgkkV7thyUF+mudc0xZPQ==", "signatures": [{"sig": "MEQCIHOHxktsAzEjjDkyNboj2/Nz/3wKcXOcpEacLuidIn46AiAInPDxd9qzAwaCH7lRC2/aDpfu7tBbTlhy4rqTZCwSiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {"extsprintf": "1.0.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.3.0": {"name": "verror", "version": "1.3.0", "_id": "verror@1.3.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "e6047cae0bb5bdf862ac0b7e1fa1e1f8e3cfc6db", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.0.tgz", "integrity": "sha512-3V3tnOBZCOlQB+7NbBoQ5zrdpCHi6KjNPc3D17/euNX9bbQLtQGXNH2s4KRztjtMNtjLqnX8RWAoSV/xZRcJMg==", "signatures": [{"sig": "MEUCIQCz7DmyN0H3xuMDvjzmSb63So1DqafEMDPyJxE6gBeGewIgOAS+nwRxDZQy96NTsb+YtHtQe4yQ8Gr83yKVfPGJvs0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.0"}}, "1.3.1": {"name": "verror", "version": "1.3.1", "_id": "verror@1.3.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "0d6598b520fdbac16753c2994a9f0f6de8a1ca68", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.1.tgz", "integrity": "sha512-8Cnw/aK+DML9+Ugf8XRhT7FbBJSCCZvnT6CUZei9efcQ8nMsf0kjV6XjuLILZzqz7ljV1uHVI8J28mJBy8KTIA==", "signatures": [{"sig": "MEUCIQC2TiMf0byAEtrVi49eyoP2xg8xxOlmXsxkR/CzGyWCRwIgNVy1K+0NZ/VcmS7XeErnpfkww/4lk117oNTOGcGc/y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.0"}}, "1.3.2": {"name": "verror", "version": "1.3.2", "_id": "verror@1.3.2", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "371c3a07de6b699cd43f0bc2c7425eea5d5e1b1f", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.2.tgz", "integrity": "sha512-8frkPbTg/ltkpdHAHpMFsBxXv+KD8K+LLjlmg4RK6/rTwFyk+sgiRsn3DNxzwPTsWv1b0PMLBhpfTws6l6CSKA==", "signatures": [{"sig": "MEYCIQD+yye43MRWHmeQ30+y4HAG3gkk8FHUMNly5ITmHwdMtQIhANevdbiqs88tgXgp3qRdNz/RG0S5Yv52dLnhPFobxKtc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.0"}}, "1.3.3": {"name": "verror", "version": "1.3.3", "_id": "verror@1.3.3", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "8a6a4ac3a8c774b6f687fece49bdffd78552e2cd", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.3.tgz", "integrity": "sha512-FRyhVjZX8Hy5Mlmvo4YbbN3V8KaagA6W6Xc3kNOtIQqd3DohGqQzqie1NDVfZbZuWZjCTF0E5PNUqQZ5BVqdew==", "signatures": [{"sig": "MEUCIQD97HttS9jEBTNTtdhQYmtUpo1tLaQoTtNOvcmmZ/I4XAIgB0bl6kV/9Sf2ei5+av9rrCOtjOhlOrzxhDoWsBPhQpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.0"}}, "1.3.4": {"name": "verror", "version": "1.3.4", "_id": "verror@1.3.4", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "0cd68820c711206b5157b751d037b6e04a4244f6", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.4.tgz", "integrity": "sha512-C3ej2ff78d600MUm61dL7uqxPgJ/4ceElI1XX7WEdLc1UsignKbshPg05Vj4A4rdTS9Jjs5qRlFCZ8BcLoqOFg==", "signatures": [{"sig": "MEUCIFqFjKq7xFgB6AI1RGzlsEa6JmaQ6aCH2sz7mS2rH/h/AiEA0Du1IWrNbhjIvWSpIZ4yKydWpEd3LP6/SxZ7n9O8oM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.0"}}, "1.3.5": {"name": "verror", "version": "1.3.5", "_id": "verror@1.3.5", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "5d2d0af474628bf6979f25ee869407af9a148c32", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.5.tgz", "integrity": "sha512-TCxqtnX7rAGvG/xg/4y6+l3M2p8DxwkcHaGncJganXjsthMaRONx41u7Q9EZYMC6cE2E1fG83WP6zrw8e5+tFw==", "signatures": [{"sig": "MEUCIQDMhIBP+EgVl6aZ37O9BkNBCi33ir5dPG4Yw4cRzz2TzwIgNaSffS5vk4Ul+BiXjyPEvg/79+6KDzUjlU+EvkAjUkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.0"}}, "1.3.6": {"name": "verror", "version": "1.3.6", "_id": "verror@1.3.6", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "cff5df12946d297d2baaefaa2689e25be01c005c", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.6.tgz", "integrity": "sha512-i8GFYwImt5D5B8CPpi2jrDTy/faq4OEW+NkOTLSKcIdPfdYJvWv3VZddDKl0ByvBe6cJ2s5Mm2XDtv5c2pj/Eg==", "signatures": [{"sig": "MEUCIQDJRMVao2qENrKtJAo6+IQHvBSwRVzmvvJCsKx1/1xeIgIgFcruCl2hAD0aC6uAFXgI6f1gfK8pmSVMdGQZFHxV0PI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.2"}}, "1.3.7": {"name": "verror", "version": "1.3.7", "license": "MIT", "_id": "verror@1.3.7", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "b9dd10e98d1291ab4dde3746d70ef966162237f0", "tarball": "https://registry.npmjs.org/verror/-/verror-1.3.7.tgz", "integrity": "sha512-vYodFyraTVczbZflwh+dqzCa90q8wMkT0R0ZHsPDIBNnJQhNLX7FM52+7AvgNxORV96JJ/Q8DhD7qyu4VOGv3w==", "signatures": [{"sig": "MEUCIFubOtmCzjtF1PYrXU3MDKsezdAbclU314g9oIaAFS5FAiEAkQ73IBNKlfENAsxw3u8II7yzknS8XMqe9Ec4q8dNGtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.2"}}, "1.4.0": {"name": "verror", "version": "1.4.0", "license": "MIT", "_id": "verror@1.4.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "5d8fdf5875141c3183b7c6bc23a0aa3e3e6ca4e2", "tarball": "https://registry.npmjs.org/verror/-/verror-1.4.0.tgz", "integrity": "sha512-crgPZAcuITUgJxkNS4+0RWrlxUfMb035Np2QOmq7qXgmLItoiY5eC70rEyE1A9qHIwjzs5RlUZvlaegZ9XJ4Ug==", "signatures": [{"sig": "MEUCIQDMUjDrGnWjXs2fQIblVckdvOG/M3JFskHgVCwJwJd8QwIgQAzV+dl+Us5K21JhECd6GkrDTCULXAEg4Z1vLyjKSM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.3"}}, "1.5.0": {"name": "verror", "version": "1.5.0", "license": "MIT", "_id": "verror@1.5.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "c0f5b0965ed75f17ed891a7735539173f229e1bc", "tarball": "https://registry.npmjs.org/verror/-/verror-1.5.0.tgz", "integrity": "sha512-BE5FGi8mpkp5wX1UkPZtuogtoyJ7JojiGrR7yrBafTDFbxQz0nqhKGHv7UJjynx6XZb9/OhA1xWeLG5Wy2iv4Q==", "signatures": [{"sig": "MEQCICe1vf3KsOB/eoFSch7jGNGXwdXIzNhELjknoNo+omLJAiAFpTQPlfEIeAtAsLnHoaiBobznn7he7YI3SpVoY4mNfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "c0f5b0965ed75f17ed891a7735539173f229e1bc", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.0.3"}}, "1.5.1": {"name": "verror", "version": "1.5.1", "license": "MIT", "_id": "verror@1.5.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "6ba9003345a4f8c3c6a6ada344f3cd6fa7b98821", "tarball": "https://registry.npmjs.org/verror/-/verror-1.5.1.tgz", "integrity": "sha512-zfTiezsdLoEI/cItjwO7hBh2vndDZ0B+BU4/dB6M7PcRlbOG8z8PYe+fsfpaPn8EEHiGWnX4LhFJjogHEZ4D3Q==", "signatures": [{"sig": "MEQCIB7KqjstpmldekzESapxYbYRo8THTTeDhPNFecPELqzWAiBuM3hijAsOmHYbiGhv8+CZZ8znpcpSRO/kxjV2lWwMog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "6ba9003345a4f8c3c6a6ada344f3cd6fa7b98821", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.2.0"}}, "1.6.0": {"name": "verror", "version": "1.6.0", "license": "MIT", "_id": "verror@1.6.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "7d13b27b1facc2e2da90405eb5ea6e5bdd252ea5", "tarball": "https://registry.npmjs.org/verror/-/verror-1.6.0.tgz", "integrity": "sha512-bIOaZx4+Bf6a7sIORfmYnyKLDLk/lhVym6rjYlq+vkitYKnhFmUpmPpDTCltWFrUTlGKs6sCeoDWfMA0oOOneA==", "signatures": [{"sig": "MEUCIQClnwNid8BfSHzz8s4c+3bs5ASPx4fy76AvaAudrODg3gIgK65CBoR0tavWXorM2ay/z55/weHZGJ0gfqV4inyat+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "7d13b27b1facc2e2da90405eb5ea6e5bdd252ea5", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.2.0"}}, "1.6.1": {"name": "verror", "version": "1.6.1", "license": "MIT", "_id": "verror@1.6.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "236402060648c219d1162c2451d1c341a0e1c9ce", "tarball": "https://registry.npmjs.org/verror/-/verror-1.6.1.tgz", "integrity": "sha512-XPWRmzTdDyQm8sDw2WsJAF+nG+FI8nB/mdbPvN1WRe32YiEyiQwCPBshpb+fYfQTyTJSIr3aTEdlqGGfoUcLiA==", "signatures": [{"sig": "MEUCICiheT2h5Cf3FUYVPhCoNrHAXFK+4yo/KwsjBUwyL5UrAiEA+9N55kbrQjsgLS7ZDL50KSOKkfdaU7psQ2b6GyJZMwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "236402060648c219d1162c2451d1c341a0e1c9ce", "engines": ["node >=0.6.0"], "gitHead": "b1c7bf701c232eb0039da2321291636c6815e061", "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "1.2.0", "core-util-is": "1.0.2"}}, "1.7.0": {"name": "verror", "version": "1.7.0", "license": "MIT", "_id": "verror@1.7.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror#readme", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "e60584035c54417b981a631326cd047af3a065fb", "tarball": "https://registry.npmjs.org/verror/-/verror-1.7.0.tgz", "integrity": "sha512-kJ0TAVh5y+icKwcylh7msiz2X9xnr32s/BF8gPn+spfHz2n7lxoJyjCwZCDQTC5buMLk3w7KJUe/wHCfb1QmjQ==", "signatures": [{"sig": "MEYCIQDNijc4L4hbA3XSW7tqMACQUCfMQ3iUMoy/khQzEilEpQIhAMCFbmEzo11/hqyv4Efcr78JWFh63NWn4vOEVlNbAs3G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "e60584035c54417b981a631326cd047af3a065fb", "engines": ["node >=0.6.0"], "gitHead": "a4de6666c36a0889283cd13dfadb5645f77b9137", "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"extsprintf": "^1.2.0", "assert-plus": "^1.0.0", "core-util-is": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/verror-1.7.0.tgz_1470965402274_0.31455223984085023", "host": "packages-16-east.internal.npmjs.com"}}, "1.8.0": {"name": "verror", "version": "1.8.0", "license": "MIT", "_id": "verror@1.8.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror#readme", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "f538042e92a1a60eff17b8e2a5cb7f27e3a98a09", "tarball": "https://registry.npmjs.org/verror/-/verror-1.8.0.tgz", "integrity": "sha512-sr7ta5j4tH+bKA3NLR/arb4GXteKQAj3/KaJQLS6/hFj3S1Ry0zmfMvIfSXgn7m/lA27Ocz9Xo6M7vP5AaYmVQ==", "signatures": [{"sig": "MEUCIQDkn3gmmVk47OW7yAHF8yrfKQENF+s4OhGYh+NVoGk4cwIgMAV6LmgVXmPTvEQi7VPG6BBNEIJPK/KydsbKm8tlMEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "f538042e92a1a60eff17b8e2a5cb7f27e3a98a09", "engines": ["node >=0.6.0"], "gitHead": "8b782cb7514aa3a09503e848e4c9ac3662a37075", "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"extsprintf": "^1.2.0", "assert-plus": "^1.0.0", "core-util-is": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/verror-1.8.0.tgz_1471383208692_0.5374380375724286", "host": "packages-16-east.internal.npmjs.com"}}, "1.8.1": {"name": "verror", "version": "1.8.1", "license": "MIT", "_id": "verror@1.8.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror#readme", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "157589400a2d14570a62f2d5dd6a0f6214be3029", "tarball": "https://registry.npmjs.org/verror/-/verror-1.8.1.tgz", "integrity": "sha512-gfBb4SOzDbgOHIkFRn4u64/v068q6PhdRaUkT5QTCzwzSctR66ND4ed0FRVhrpF9TvlhziTQ3dvtdblwS9ecFw==", "signatures": [{"sig": "MEUCIBmfWQvUee0Sj5qL8TxOawp9KzIo2iC8AkU1CjKplcbqAiEA4q1w9uWfJ9nDjPdD0XkeqGWiz0LuuVvqcSmm5F0b8d4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "157589400a2d14570a62f2d5dd6a0f6214be3029", "engines": ["node >=0.6.0"], "gitHead": "53b3b757acf0a59cad30ad6d2bf688160cab0369", "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"extsprintf": "^1.2.0", "assert-plus": "^1.0.0", "core-util-is": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/verror-1.8.1.tgz_1471459232080_0.8993467639666051", "host": "packages-16-east.internal.npmjs.com"}}, "1.9.0": {"name": "verror", "version": "1.9.0", "license": "MIT", "_id": "verror@1.9.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror#readme", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "107a8a2d14c33586fc4bb830057cd2d19ae2a6ee", "tarball": "https://registry.npmjs.org/verror/-/verror-1.9.0.tgz", "integrity": "sha512-6pLuacRdt9S/6AcjpES9fxvlsud8VBnLGXvm/v0pVoVnpIR1tmq+uUPUCII8P7uQe1mT9s5vdRdKbDR+KwJYIQ==", "signatures": [{"sig": "MEQCIDiFJOkwhsp9xkaXv60rA+ndERnLdioxkW5B9bpmJBCuAiBez4ysA/brL+BubzXcaw9PbvN0O/6huQ6r7Wab5Ouqog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "107a8a2d14c33586fc4bb830057cd2d19ae2a6ee", "engines": ["node >=0.6.0"], "gitHead": "29b7ce4dc7c4e488b00355db5a41a11496633679", "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "0.10.45", "dependencies": {"extsprintf": "^1.2.0", "assert-plus": "^1.0.0", "core-util-is": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/verror-1.9.0.tgz_1479333178189_0.022463352419435978", "host": "packages-18-east.internal.npmjs.com"}}, "1.10.0": {"name": "verror", "version": "1.10.0", "license": "MIT", "_id": "verror@1.10.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-verror", "bugs": {"url": "https://github.com/davepacheco/node-verror/issues"}, "dist": {"shasum": "3a105ca17053af55d6e270c1f8288682e18da400", "tarball": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "signatures": [{"sig": "MEQCIDdvmzIftlJeM0UKP4/RuqabX/B82OZ+6YwXQORTnaw6AiAdcfkpZ6HRV3Pbs3tCBo24GPUTOmAUo25gL0XekFzvKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/verror.js", "_from": ".", "_shasum": "3a105ca17053af55d6e270c1f8288682e18da400", "engines": ["node >=0.6.0"], "scripts": {"test": "make test"}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-verror.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "richer JavaScript errors", "directories": {}, "dependencies": {"extsprintf": "^1.2.0", "assert-plus": "^1.0.0", "core-util-is": "1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/verror-1.10.0.tgz_1493743247437_0.7535550429020077", "host": "packages-12-west.internal.npmjs.com"}}, "1.10.1": {"name": "verror", "version": "1.10.1", "keywords": ["error", "errors", "err", "exception", "exceptions", "custom"], "license": "MIT", "_id": "verror@1.10.1", "maintainers": [{"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chudley", "email": "<EMAIL>"}, {"name": "tcha<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d<PERSON>ell", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-verror#readme", "bugs": {"url": "https://github.com/joyent/node-verror/issues"}, "dist": {"shasum": "4bf09eeccf4563b109ed4b3d458380c972b0cdeb", "tarball": "https://registry.npmjs.org/verror/-/verror-1.10.1.tgz", "fileCount": 6, "integrity": "sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==", "signatures": [{"sig": "MEUCIQCnyTLbvc9KMGDitKyiVR/D47S7mLaS/7DjpJ/uN55mKwIgC19nn+DBFLvnccDTcwmIJEs4qT2P5v0Ge56rVLGJsag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2/qgCRA9TVsSAnZWagAANTYQAIga+X9/PxWRky6J1Vnm\noarq5m3w7olNNaTbh7ucyZYY9Qyj/31PN25s5uCVVLXc7DYtXakwnjGfpYEe\nKw+oKIccJ8gSmTZxATLlO+nUwNOaPYflutdTYcg2A3WDE0g8VJwxtt/HXhiI\nlMYzA05hHLX4IYUdaApXXAJsO5B4tssaYF1Wqk+dgjQx8jqXpQ8NW10QqgR2\nB5nu+tiK/P+ZWFy7Gp/Vsp8IK/EcCcjFODM78/CRtp2lNprkUVkZS2vH4cew\nxcPj+DaPGEtJYJ58YlaMRh0NQrA0+Z4fYANGmM/6u1KNiC0uX6RsSJBprdeU\njOljVkzUoPZwxgblAKCt+tEPsPKEWTv/biorOiN/cyCnH/t5S3zN2CBrzvNy\nb4Dt4q6FkN0P4v/JCtrK5P8BCmRcdoNdiNU7BvdAa84Z9sDKTbKFY1EO6OjA\nl8wTkZ2REbQpD016ofUpEyH5Q2HhjxQTw1AZVgy09YKwhj+U+qGRxu7k4uQa\ng/eQjU6FBrSXD9H2Gz1DSd6LujHWBfedgfvWs+MVVPIbzl5vuNZg407RRgcL\n92Hfn7cU+94pe69glc5PSZ9R342pb0gtcXbXo7k2CZbuura7OCahmVU41AaS\n8Q70lNphbUwql6/7Jk6ubt1XKpIdg/IidK4KIoUMvhejdSTQvKIn0XWFZlo1\nS6GI\r\n=Ma65\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/verror.js", "engines": {"node": ">=0.6.0"}, "gitHead": "0fd41fd9a1dd1e2014a554d0dabb50b210229638", "scripts": {"test": "make test"}, "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-verror.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "richer JavaScript errors", "directories": {}, "_nodeVersion": "12.22.7", "dependencies": {"extsprintf": "^1.2.0", "assert-plus": "^1.0.0", "core-util-is": "1.0.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/verror_1.10.1_1635884956218_0.2278787717721098", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-04-18T01:13:51.779Z", "modified": "2025-02-07T15:28:30.108Z", "1.0.0": "2012-04-18T01:13:53.033Z", "1.0.1": "2012-04-18T16:21:48.400Z", "1.1.0": "2012-05-01T19:11:24.848Z", "1.2.0": "2012-07-11T18:56:59.684Z", "1.3.0": "2012-07-27T00:03:18.667Z", "1.3.1": "2012-07-27T17:37:13.264Z", "1.3.2": "2012-07-30T23:16:11.948Z", "1.3.3": "2012-07-30T23:57:10.716Z", "1.3.4": "2012-10-17T18:55:23.042Z", "1.3.5": "2012-12-14T17:25:51.996Z", "1.3.6": "2013-02-06T18:29:57.875Z", "1.3.7": "2014-01-15T00:33:58.024Z", "1.4.0": "2014-03-13T04:35:07.331Z", "1.5.0": "2014-11-17T18:25:19.433Z", "1.5.1": "2014-11-17T18:28:09.308Z", "1.6.0": "2014-11-20T23:24:57.881Z", "1.6.1": "2016-01-30T00:25:54.798Z", "1.7.0": "2016-08-12T01:30:04.564Z", "1.8.0": "2016-08-16T21:33:32.494Z", "1.8.1": "2016-08-17T18:40:33.822Z", "1.9.0": "2016-11-16T21:53:00.253Z", "1.10.0": "2017-05-02T16:40:47.677Z", "1.10.1": "2021-11-02T20:29:16.342Z"}, "bugs": {"url": "https://github.com/joyent/node-verror/issues"}, "license": "MIT", "homepage": "https://github.com/joyent/node-verror#readme", "keywords": ["error", "errors", "err", "exception", "exceptions", "custom"], "repository": {"url": "git+https://github.com/joyent/node-verror.git", "type": "git"}, "description": "richer JavaScript errors", "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "trentm"}, {"email": "<EMAIL>", "name": "dap"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "melloc"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "readme": "# verror: rich JavaScript errors\n\nThis module provides several classes in support of Joyent's [Best Practices for\nError Handling in Node.js](http://www.joyent.com/developers/node/design/errors).\nIf you find any of the behavior here confusing or surprising, check out that\ndocument first.\n\nThe error classes here support:\n\n* printf-style arguments for the message\n* chains of causes\n* properties to provide extra information about the error\n* creating your own subclasses that support all of these\n\nThe classes here are:\n\n* **VError**, for chaining errors while preserving each one's error message.\n  This is useful in servers and command-line utilities when you want to\n  propagate an error up a call stack, but allow various levels to add their own\n  context.  See examples below.\n* **WError**, for wrapping errors while hiding the lower-level messages from the\n  top-level error.  This is useful for API endpoints where you don't want to\n  expose internal error messages, but you still want to preserve the error chain\n  for logging and debugging.\n* **SError**, which is just like VError but interprets printf-style arguments\n  more strictly.\n* **MultiError**, which is just an Error that encapsulates one or more other\n  errors.  (This is used for parallel operations that return several errors.)\n\n\n# Quick start\n\nFirst, install the package:\n\n    npm install verror\n\nIf nothing else, you can use VError as a drop-in replacement for the built-in\nJavaScript Error class, with the addition of printf-style messages:\n\n```javascript\nvar err = new VError('missing file: \"%s\"', '/etc/passwd');\nconsole.log(err.message);\n```\n\nThis prints:\n\n    missing file: \"/etc/passwd\"\n\nYou can also pass a `cause` argument, which is any other Error object:\n\n```javascript\nvar fs = require('fs');\nvar filename = '/nonexistent';\nfs.stat(filename, function (err1) {\n\tvar err2 = new VError(err1, 'stat \"%s\"', filename);\n\tconsole.error(err2.message);\n});\n```\n\nThis prints out:\n\n    stat \"/nonexistent\": ENOENT, stat '/nonexistent'\n\nwhich resembles how Unix programs typically report errors:\n\n    $ sort /nonexistent\n    sort: open failed: /nonexistent: No such file or directory\n\nTo match the Unixy feel, when you print out the error, just prepend the\nprogram's name to the VError's `message`.  Or just call\n[node-cmdutil.fail(your_verror)](https://github.com/joyent/node-cmdutil), which\ndoes this for you.\n\nYou can get the next-level Error using `err.cause()`:\n\n```javascript\nconsole.error(err2.cause().message);\n```\n\nprints:\n\n    ENOENT, stat '/nonexistent'\n\nOf course, you can chain these as many times as you want, and it works with any\nkind of Error:\n\n```javascript\nvar err1 = new Error('No such file or directory');\nvar err2 = new VError(err1, 'failed to stat \"%s\"', '/junk');\nvar err3 = new VError(err2, 'request failed');\nconsole.error(err3.message);\n```\n\nThis prints:\n\n    request failed: failed to stat \"/junk\": No such file or directory\n\nThe idea is that each layer in the stack annotates the error with a description\nof what it was doing.  The end result is a message that explains what happened\nat each level.\n\nYou can also decorate Error objects with additional information so that callers\ncan not only handle each kind of error differently, but also construct their own\nerror messages (e.g., to localize them, format them, group them by type, and so\non).  See the example below.\n\n\n# Deeper dive\n\nThe two main goals for VError are:\n\n* **Make it easy to construct clear, complete error messages intended for\n  people.**  Clear error messages greatly improve both user experience and\n  debuggability, so we wanted to make it easy to build them.  That's why the\n  constructor takes printf-style arguments.\n* **Make it easy to construct objects with programmatically-accessible\n  metadata** (which we call _informational properties_).  Instead of just saying\n  \"connection refused while connecting to ***********:80\", you can add\n  properties like `\"ip\": \"***********\"` and `\"tcpPort\": 80`.  This can be used\n  for feeding into monitoring systems, analyzing large numbers of Errors (as\n  from a log file), or localizing error messages.\n\nTo really make this useful, it also needs to be easy to compose Errors:\nhigher-level code should be able to augment the Errors reported by lower-level\ncode to provide a more complete description of what happened.  Instead of saying\n\"connection refused\", you can say \"operation X failed: connection refused\".\nThat's why VError supports `causes`.\n\nIn order for all this to work, programmers need to know that it's generally safe\nto wrap lower-level Errors with higher-level ones.  If you have existing code\nthat handles Errors produced by a library, you should be able to wrap those\nErrors with a VError to add information without breaking the error handling\ncode.  There are two obvious ways that this could break such consumers:\n\n* The error's name might change.  People typically use `name` to determine what\n  kind of Error they've got.  To ensure compatibility, you can create VErrors\n  with custom names, but this approach isn't great because it prevents you from\n  representing complex failures.  For this reason, VError provides\n  `findCauseByName`, which essentially asks: does this Error _or any of its\n  causes_ have this specific type?  If error handling code uses\n  `findCauseByName`, then subsystems can construct very specific causal chains\n  for debuggability and still let people handle simple cases easily.  There's an\n  example below.\n* The error's properties might change.  People often hang additional properties\n  off of Error objects.  If we wrap an existing Error in a new Error, those\n  properties would be lost unless we copied them.  But there are a variety of\n  both standard and non-standard Error properties that should _not_ be copied in\n  this way: most obviously `name`, `message`, and `stack`, but also `fileName`,\n  `lineNumber`, and a few others.  Plus, it's useful for some Error subclasses\n  to have their own private properties -- and there'd be no way to know whether\n  these should be copied.  For these reasons, VError first-classes these\n  information properties.  You have to provide them in the constructor, you can\n  only fetch them with the `info()` function, and VError takes care of making\n  sure properties from causes wind up in the `info()` output.\n\nLet's put this all together with an example from the node-fast RPC library.\nnode-fast implements a simple RPC protocol for Node programs.  There's a server\nand client interface, and clients make RPC requests to servers.  Let's say the\nserver fails with an UnauthorizedError with message \"user 'bob' is not\nauthorized\".  The client wraps all server errors with a FastServerError.  The\nclient also wraps all request errors with a FastRequestError that includes the\nname of the RPC call being made.  The result of this failed RPC might look like\nthis:\n\n    name: FastRequestError\n    message: \"request failed: server error: user 'bob' is not authorized\"\n    rpcMsgid: <unique identifier for this request>\n    rpcMethod: GetObject\n    cause:\n        name: FastServerError\n        message: \"server error: user 'bob' is not authorized\"\n        cause:\n            name: UnauthorizedError\n            message: \"user 'bob' is not authorized\"\n            rpcUser: \"bob\"\n\nWhen the caller uses `VError.info()`, the information properties are collapsed\nso that it looks like this:\n\n    message: \"request failed: server error: user 'bob' is not authorized\"\n    rpcMsgid: <unique identifier for this request>\n    rpcMethod: GetObject\n    rpcUser: \"bob\"\n\nTaking this apart:\n\n* The error's message is a complete description of the problem.  The caller can\n  report this directly to its caller, which can potentially make its way back to\n  an end user (if appropriate).  It can also be logged.\n* The caller can tell that the request failed on the server, rather than as a\n  result of a client problem (e.g., failure to serialize the request), a\n  transport problem (e.g., failure to connect to the server), or something else\n  (e.g., a timeout).  They do this using `findCauseByName('FastServerError')`\n  rather than checking the `name` field directly.\n* If the caller logs this error, the logs can be analyzed to aggregate\n  errors by cause, by RPC method name, by user, or whatever.  Or the\n  error can be correlated with other events for the same rpcMsgid.\n* It wasn't very hard for any part of the code to contribute to this Error.\n  Each part of the stack has just a few lines to provide exactly what it knows,\n  with very little boilerplate.\n\nIt's not expected that you'd use these complex forms all the time.  Despite\nsupporting the complex case above, you can still just do:\n\n   new VError(\"my service isn't working\");\n\nfor the simple cases.\n\n\n# Reference: VError, WError, SError\n\nVError, WError, and SError are convenient drop-in replacements for `Error` that\nsupport printf-style arguments, first-class causes, informational properties,\nand other useful features.\n\n\n## Constructors\n\nThe VError constructor has several forms:\n\n```javascript\n/*\n * This is the most general form.  You can specify any supported options\n * (including \"cause\" and \"info\") this way.\n */\nnew VError(options, sprintf_args...)\n\n/*\n * This is a useful shorthand when the only option you need is \"cause\".\n */\nnew VError(cause, sprintf_args...)\n\n/*\n * This is a useful shorthand when you don't need any options at all.\n */\nnew VError(sprintf_args...)\n```\n\nAll of these forms construct a new VError that behaves just like the built-in\nJavaScript `Error` class, with some additional methods described below.\n\nIn the first form, `options` is a plain object with any of the following\noptional properties:\n\nOption name      | Type             | Meaning\n---------------- | ---------------- | -------\n`name`           | string           | Describes what kind of error this is.  This is intended for programmatic use to distinguish between different kinds of errors.  Note that in modern versions of Node.js, this name is ignored in the `stack` property value, but callers can still use the `name` property to get at it.\n`cause`          | any Error object | Indicates that the new error was caused by `cause`.  See `cause()` below.  If unspecified, the cause will be `null`.\n`strict`         | boolean          | If true, then `null` and `undefined` values in `sprintf_args` are passed through to `sprintf()`.  Otherwise, these are replaced with the strings `'null'`, and '`undefined`', respectively.\n`constructorOpt` | function         | If specified, then the stack trace for this error ends at function `constructorOpt`.  Functions called by `constructorOpt` will not show up in the stack.  This is useful when this class is subclassed.\n`info`           | object           | Specifies arbitrary informational properties that are available through the `VError.info(err)` static class method.  See that method for details.\n\nThe second form is equivalent to using the first form with the specified `cause`\nas the error's cause.  This form is distinguished from the first form because\nthe first argument is an Error.\n\nThe third form is equivalent to using the first form with all default option\nvalues.  This form is distinguished from the other forms because the first\nargument is not an object or an Error.\n\nThe `WError` constructor is used exactly the same way as the `VError`\nconstructor.  The `SError` constructor is also used the same way as the\n`VError` constructor except that in all cases, the `strict` property is\noverriden to `true.\n\n\n## Public properties\n\n`VError`, `WError`, and `SError` all provide the same public properties as\nJavaScript's built-in Error objects.\n\nProperty name | Type   | Meaning\n------------- | ------ | -------\n`name`        | string | Programmatically-usable name of the error.\n`message`     | string | Human-readable summary of the failure.  Programmatically-accessible details are provided through `VError.info(err)` class method.\n`stack`       | string | Human-readable stack trace where the Error was constructed.\n\nFor all of these classes, the printf-style arguments passed to the constructor\nare processed with `sprintf()` to form a message.  For `WError`, this becomes\nthe complete `message` property.  For `SError` and `VError`, this message is\nprepended to the message of the cause, if any (with a suitable separator), and\nthe result becomes the `message` property.\n\nThe `stack` property is managed entirely by the underlying JavaScript\nimplementation.  It's generally implemented using a getter function because\nconstructing the human-readable stack trace is somewhat expensive.\n\n## Class methods\n\nThe following methods are defined on the `VError` class and as exported\nfunctions on the `verror` module.  They're defined this way rather than using\nmethods on VError instances so that they can be used on Errors not created with\n`VError`.\n\n### `VError.cause(err)`\n\nThe `cause()` function returns the next Error in the cause chain for `err`, or\n`null` if there is no next error.  See the `cause` argument to the constructor.\nErrors can have arbitrarily long cause chains.  You can walk the `cause` chain\nby invoking `VError.cause(err)` on each subsequent return value.  If `err` is\nnot a `VError`, the cause is `null`.\n\n### `VError.info(err)`\n\nReturns an object with all of the extra error information that's been associated\nwith this Error and all of its causes.  These are the properties passed in using\nthe `info` option to the constructor.  Properties not specified in the\nconstructor for this Error are implicitly inherited from this error's cause.\n\nThese properties are intended to provide programmatically-accessible metadata\nabout the error.  For an error that indicates a failure to resolve a DNS name,\ninformational properties might include the DNS name to be resolved, or even the\nlist of resolvers used to resolve it.  The values of these properties should\ngenerally be plain objects (i.e., consisting only of null, undefined, numbers,\nbooleans, strings, and objects and arrays containing only other plain objects).\n\n### `VError.fullStack(err)`\n\nReturns a string containing the full stack trace, with all nested errors recursively\nreported as `'caused by:' + err.stack`.\n\n### `VError.findCauseByName(err, name)`\n\nThe `findCauseByName()` function traverses the cause chain for `err`, looking\nfor an error whose `name` property matches the passed in `name` value. If no\nmatch is found, `null` is returned.\n\nIf all you want is to know _whether_ there's a cause (and you don't care what it\nis), you can use `VError.hasCauseWithName(err, name)`.\n\nIf a vanilla error or a non-VError error is passed in, then there is no cause\nchain to traverse. In this scenario, the function will check the `name`\nproperty of only `err`.\n\n### `VError.hasCauseWithName(err, name)`\n\nReturns true if and only if `VError.findCauseByName(err, name)` would return\na non-null value.  This essentially determines whether `err` has any cause in\nits cause chain that has name `name`.\n\n### `VError.errorFromList(errors)`\n\nGiven an array of Error objects (possibly empty), return a single error\nrepresenting the whole collection of errors.  If the list has:\n\n* 0 elements, returns `null`\n* 1 element, returns the sole error\n* more than 1 element, returns a MultiError referencing the whole list\n\nThis is useful for cases where an operation may produce any number of errors,\nand you ultimately want to implement the usual `callback(err)` pattern.  You can\naccumulate the errors in an array and then invoke\n`callback(VError.errorFromList(errors))` when the operation is complete.\n\n\n### `VError.errorForEach(err, func)`\n\nConvenience function for iterating an error that may itself be a MultiError.\n\nIn all cases, `err` must be an Error.  If `err` is a MultiError, then `func` is\ninvoked as `func(errorN)` for each of the underlying errors of the MultiError.\nIf `err` is any other kind of error, `func` is invoked once as `func(err)`.  In\nall cases, `func` is invoked synchronously.\n\nThis is useful for cases where an operation may produce any number of warnings\nthat may be encapsulated with a MultiError -- but may not be.\n\nThis function does not iterate an error's cause chain.\n\n\n## Examples\n\nThe \"Demo\" section above covers several basic cases.  Here's a more advanced\ncase:\n\n```javascript\nvar err1 = new VError('something bad happened');\n/* ... */\nvar err2 = new VError({\n    'name': 'ConnectionError',\n    'cause': err1,\n    'info': {\n        'errno': 'ECONNREFUSED',\n        'remote_ip': '127.0.0.1',\n        'port': 215\n    }\n}, 'failed to connect to \"%s:%d\"', '127.0.0.1', 215);\n\nconsole.log(err2.message);\nconsole.log(err2.name);\nconsole.log(VError.info(err2));\nconsole.log(err2.stack);\n```\n\nThis outputs:\n\n    failed to connect to \"127.0.0.1:215\": something bad happened\n    ConnectionError\n    { errno: 'ECONNREFUSED', remote_ip: '127.0.0.1', port: 215 }\n    ConnectionError: failed to connect to \"127.0.0.1:215\": something bad happened\n        at Object.<anonymous> (/home/<USER>/node-verror/examples/info.js:5:12)\n        at Module._compile (module.js:456:26)\n        at Object.Module._extensions..js (module.js:474:10)\n        at Module.load (module.js:356:32)\n        at Function.Module._load (module.js:312:12)\n        at Function.Module.runMain (module.js:497:10)\n        at startup (node.js:119:16)\n        at node.js:935:3\n\nInformation properties are inherited up the cause chain, with values at the top\nof the chain overriding same-named values lower in the chain.  To continue that\nexample:\n\n```javascript\nvar err3 = new VError({\n    'name': 'RequestError',\n    'cause': err2,\n    'info': {\n        'errno': 'EBADREQUEST'\n    }\n}, 'request failed');\n\nconsole.log(err3.message);\nconsole.log(err3.name);\nconsole.log(VError.info(err3));\nconsole.log(err3.stack);\n```\n\nThis outputs:\n\n    request failed: failed to connect to \"127.0.0.1:215\": something bad happened\n    RequestError\n    { errno: 'EBADREQUEST', remote_ip: '127.0.0.1', port: 215 }\n    RequestError: request failed: failed to connect to \"127.0.0.1:215\": something bad happened\n        at Object.<anonymous> (/home/<USER>/node-verror/examples/info.js:20:12)\n        at Module._compile (module.js:456:26)\n        at Object.Module._extensions..js (module.js:474:10)\n        at Module.load (module.js:356:32)\n        at Function.Module._load (module.js:312:12)\n        at Function.Module.runMain (module.js:497:10)\n        at startup (node.js:119:16)\n        at node.js:935:3\n\nYou can also print the complete stack trace of combined `Error`s by using\n`VError.fullStack(err).`\n\n```javascript\nvar err1 = new VError('something bad happened');\n/* ... */\nvar err2 = new VError(err1, 'something really bad happened here');\n\nconsole.log(VError.fullStack(err2));\n```\n\nThis outputs:\n\n    VError: something really bad happened here: something bad happened\n        at Object.<anonymous> (/home/<USER>/node-verror/examples/fullStack.js:5:12)\n        at Module._compile (module.js:409:26)\n        at Object.Module._extensions..js (module.js:416:10)\n        at Module.load (module.js:343:32)\n        at Function.Module._load (module.js:300:12)\n        at Function.Module.runMain (module.js:441:10)\n        at startup (node.js:139:18)\n        at node.js:968:3\n    caused by: VError: something bad happened\n        at Object.<anonymous> (/home/<USER>/node-verror/examples/fullStack.js:3:12)\n        at Module._compile (module.js:409:26)\n        at Object.Module._extensions..js (module.js:416:10)\n        at Module.load (module.js:343:32)\n        at Function.Module._load (module.js:300:12)\n        at Function.Module.runMain (module.js:441:10)\n        at startup (node.js:139:18)\n        at node.js:968:3\n\n`VError.fullStack` is also safe to use on regular `Error`s, so feel free to use\nit whenever you need to extract the stack trace from an `Error`, regardless if\nit's a `VError` or not.\n\n# Reference: MultiError\n\nMultiError is an Error class that represents a group of Errors.  This is used\nwhen you logically need to provide a single Error, but you want to preserve\ninformation about multiple underlying Errors.  A common case is when you execute\nseveral operations in parallel and some of them fail.\n\nMultiErrors are constructed as:\n\n```javascript\nnew MultiError(error_list)\n```\n\n`error_list` is an array of at least one `Error` object.\n\nThe cause of the MultiError is the first error provided.  None of the other\n`VError` options are supported.  The `message` for a MultiError consists the\n`message` from the first error, prepended with a message indicating that there\nwere other errors.\n\nFor example:\n\n```javascript\nerr = new MultiError([\n    new Error('failed to resolve DNS name \"abc.example.com\"'),\n    new Error('failed to resolve DNS name \"def.example.com\"'),\n]);\n\nconsole.error(err.message);\n```\n\noutputs:\n\n    first of 2 errors: failed to resolve DNS name \"abc.example.com\"\n\nSee the convenience function `VError.errorFromList`, which is sometimes simpler\nto use than this constructor.\n\n## Public methods\n\n\n### `errors()`\n\nReturns an array of the errors used to construct this MultiError.\n\n\n# Contributing\n\nSee separate [contribution guidelines](CONTRIBUTING.md).\n", "readmeFilename": "README.md", "users": {"srl": true, "dwqs": true, "isao": true, "j.su": true, "debel": true, "garek": true, "jaxon": true, "rping": true, "arikon": true, "fuhbaz": true, "sinfex": true, "ziflex": true, "barenko": true, "dkannan": true, "lgl1993": true, "pdedkov": true, "wenbing": true, "aidenzou": true, "cmudrick": true, "davepoon": true, "forivall": true, "ghanbari": true, "jonathas": true, "leonzhao": true, "nicknaso": true, "ninjatux": true, "paraself": true, "samshull": true, "santihbc": true, "shahzaib": true, "voxpelli": true, "djensen47": true, "fgribreau": true, "goliatone": true, "gonzalofj": true, "juriwiens": true, "leonstill": true, "mojaray2k": true, "sasquatch": true, "cheapsteak": true, "quocnguyen": true, "selenasong": true, "ganeshkbhat": true, "linfeng9008": true, "poppowerlb2": true, "vparaskevas": true, "evanshortiss": true, "francisbrito": true, "vectorhacker": true, "deividasjackus": true, "joaquin.briceno": true, "programmer.severson": true}}