{"name": "merge-descriptors", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.1": {"name": "merge-descriptors", "version": "0.0.1", "dist": {"shasum": "2ff0980c924cf81d0b5d1fb601177cb8bb56c0d0", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-0.0.1.tgz", "integrity": "sha512-1VjrOxz6kouIMS/jZ+oQTAUsXufrF8hVzkfzSxqBh0Wy/KzEqZSvy3OZe/Ntrd5QeHtNCUF1bE0bIRLslzHCcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvuTLQ12a3MztrlD5cx+AMXViVdWfOievYFQzlMImtMAiBAHIzI5ETnYJxQDTxkRVWHOy/+pWlDDJ04pP7eZ7XeYg=="}]}}, "0.0.2": {"name": "merge-descriptors", "version": "0.0.2", "dist": {"shasum": "c36a52a781437513c57275f39dd9d317514ac8c7", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-0.0.2.tgz", "integrity": "sha512-dYBT4Ep+t/qnPeJcnMymmhTdd4g8/hn48ciaDqLAkfRf8abzLPS6Rb6EBdz5CZCL8tzZuI5ps9MhGQGxk+EuKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBelv09AJeKf+RJ2eyYVU9pu/Lnmnca0/kxVNgFx75k2AiATIfP1yCbe8P/mpaWrheBoMtnXmEJxnJQTx00D8XoZHA=="}]}}, "1.0.0": {"name": "merge-descriptors", "version": "1.0.0", "dist": {"shasum": "2169cf7538e1b0cc87fb88e1502d8474bbf79864", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.0.tgz", "integrity": "sha512-YJiZmTZTkrqvgefMsWdioTKsZdHnfAhHHkEdPg+4PCqMJEGHQo5iJQjEbMv3XyBZ6y3Z2Rj1mqq1WNKq9e0yNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDdR1eP68e5IHritGhbE9bu1ga8dBTkWhmxYm93TfqBEAiAoDZWgjmWxDi2AQJab1ZyS1npJXBA+bED4x0ra2mnlVw=="}]}}, "1.0.1": {"name": "merge-descriptors", "version": "1.0.1", "devDependencies": {"istanbul": "0.4.1", "mocha": "1.21.5"}, "dist": {"shasum": "b00aaa556dd8b44568150ec9d1b953f3f90cbb61", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNrN6gt6C+tUkjr9vSLHPycmhxt1e61SpK4k1v8NVKwAiEAhj5MrJHHspYdf093w3Pvji78/dtcNfokwqR0Z4sArjI="}]}}, "1.0.2": {"name": "merge-descriptors", "version": "1.0.2", "devDependencies": {"eslint": "5.9.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "dist": {"integrity": "sha512-mQy+fGU2VdxDEo3m+3+BqKyiDBgwQrmvdzaF6n1iDKp5Pk/VsaCprU6nmN6AKSe6LjuqYqF59xEnooYZVVCsXA==", "shasum": "21c9c09fc9a12211d7bc66955c5b3fc1bc00963e", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.2.tgz", "fileCount": 5, "unpackedSize": 5024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8HNRP/zH6vizzqiVIP53bRFW0sMZGaJKKjguc26+bBwIhAMaBK7wmKxjZmk/RMxArv7s27z+ouT1cPoT144qaWtyQ"}]}}, "1.0.3": {"name": "merge-descriptors", "version": "1.0.3", "devDependencies": {"eslint": "5.9.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "dist": {"integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==", "shasum": "d80319a65f3c7935351e5cfdac8f9318504dbed5", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "fileCount": 5, "unpackedSize": 5081, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC40Qs4u5va0yM+JhUuQIxLvoKZK5p4CHXJvFaMxLi/NAiEA4BHCvcU8buakxzZIKTnstviZOODgUBz++F2n1aCI/Bs="}]}, "funding": "https://github.com/sponsors/sindresorhus"}, "2.0.0": {"name": "merge-descriptors", "version": "2.0.0", "devDependencies": {"ava": "^5.3.1", "xo": "^0.56.0"}, "dist": {"integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "shasum": "ea922f660635a2249ee565e0449f951e6b603808", "tarball": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "fileCount": 5, "unpackedSize": 4373, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpTo0yi5Dj8yVNfLLVkLNKUlpcV4bSeYjZ2OyCRfu/7wIgSJJYX1nK6+pNepu5hwGH4BDTZvMbQkOLP4npxRA9CTo="}]}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2023-11-17T16:22:01.206Z"}