{"_id": "etag", "_rev": "55-e23419fbb578a97fc5eb375afddb87a3", "name": "etag", "dist-tags": {"latest": "1.8.1"}, "versions": {"1.0.0": {"name": "etag", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}, "license": "MIT", "_id": "etag@1.0.0", "maintainers": [{"name": "kesla", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/kesla/etag", "bugs": {"url": "https://github.com/kesla/etag/issues"}, "dist": {"shasum": "db9f9610cc2bf22036d713012dff4aa7e0c96162", "tarball": "https://registry.npmjs.org/etag/-/etag-1.0.0.tgz", "integrity": "sha512-IHsJ/pkALbMcdzbAC0hbN2dpoMFFQMP/TxEe8emE5cjanh9Lh053UdfeNyrlNW9vJaJWVoSk3mgo/MdOcNmtQQ==", "signatures": [{"sig": "MEQCICDue2w0/zq+eR1/FFkncdGKUCVSYC/qeQBVOjkjwkwQAiA1K9vAWi6yp/X5rc2dE4zjdfuQWTpJ2nudm7ZJPWkzyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "etag.js", "_from": ".", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "kesla", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/kesla/etag.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Small thingy to create an etag from a String or Buffer", "directories": {}}, "1.0.1": {"name": "etag", "version": "1.0.1", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.0.1", "maintainers": [{"name": "kesla", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "2aa41de474ffc45669f25c9fedacd64fea4f6ff7", "tarball": "https://registry.npmjs.org/etag/-/etag-1.0.1.tgz", "integrity": "sha512-UT24BZefQnjqe7po1hLOvkRz16uzIRkfFhtNfcj0mKc3PBakEv9eS7Ms68d5ueHygFtxftF9gzFiiay0eZJrOQ==", "signatures": [{"sig": "MEUCIQD8SBlF3xDXWITASqiasMca+r7eOkgLEd211UmvxcX+RQIgFltgbu6hGo6NwNqBqpTNiexH9iFRapaVsAzSYt3ogto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "2aa41de474ffc45669f25c9fedacd64fea4f6ff7", "engines": {"node": ">= 0.8.0"}, "gitHead": "d28ee6654ff51e83f3025a73677a30ec0fe04fc8", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0"}}, "1.1.0": {"name": "etag", "version": "1.1.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "e44af7bbabe2f998d9fc3bee00db0d34b29c13a3", "tarball": "https://registry.npmjs.org/etag/-/etag-1.1.0.tgz", "integrity": "sha512-5HRVInBL2chkZ/7Qh6Vsn8JuJM0O7N3eBHIi7Xq2yOn/vfYQBOCAGxw59pVBi9UBtk7wZZYBqDsc7Ok7u9UYQw==", "signatures": [{"sig": "MEYCIQDGROs6COwPNyUKYGzSBakNrH3gx6i+27RXfEUxqz97+wIhAN23+Z6uryCRxDB7KIkm0OhZJCeJB2tRAoTCOFn7qjH6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e44af7bbabe2f998d9fc3bee00db0d34b29c13a3", "engines": {"node": ">= 0.8.0"}, "gitHead": "415cebe1d2a87510ffbd102816733e2c9934d0c7", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "2.1.1"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0"}}, "1.2.0": {"name": "etag", "version": "1.2.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "fa537a8e1b1e29aa53480b69cfceb906a47aca8a", "tarball": "https://registry.npmjs.org/etag/-/etag-1.2.0.tgz", "integrity": "sha512-U2Y9fbTBK/CjkNdtGAI4WVb9DUMkbxc21tVMhthnDrpnu7DcjgQoa74E/wQKQZA1Ocn34OSHrf5Y+/ZXlThleQ==", "signatures": [{"sig": "MEYCIQD98eQYrnjImWnKa3wcO1lSvgsp0ePcdSlXUdsy+UT14QIhAO4aBaDmzvzYDbS9C1jR3EDkMqBf66EqB7l6aGtzWO29", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "fa537a8e1b1e29aa53480b69cfceb906a47aca8a", "engines": {"node": ">= 0.8.0"}, "gitHead": "0d38a87d4217882b31e4a34786069281631a5cb2", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "2.1.1"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0"}}, "1.2.1": {"name": "etag", "version": "1.2.1", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "74b16ea7511ce0041b944852eca2a95b9afefba3", "tarball": "https://registry.npmjs.org/etag/-/etag-1.2.1.tgz", "integrity": "sha512-Oq6Jk14sLDlp1AdgghBhHi+l/gg+1BksXtAU/bv9GVUVvnhN3sw8Sqo1DWzAGe4hGScGxp+apiQjDzeSra5sQQ==", "signatures": [{"sig": "MEUCIHbPxxprZ0wFJRN/OURre5alhd1yYwboNnAnhlgqR4WDAiEAiM5C1Fhz1/3hCkptGGVty6pZ+oupVPPZUesNr0Mgo4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "74b16ea7511ce0041b944852eca2a95b9afefba3", "engines": {"node": ">= 0.8.0"}, "gitHead": "4b9369db7434104ee7c449f288af03c6abe7bad3", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"buffer-crc32": "0.2.3"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0", "benchmark": "1.0.0", "seedrandom": "~2.3.6", "beautify-benchmark": "0.2.4"}}, "1.3.0": {"name": "etag", "version": "1.3.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "c837debfbfe0baf7eb8e2f0bbb3d1d9cc3229697", "tarball": "https://registry.npmjs.org/etag/-/etag-1.3.0.tgz", "integrity": "sha512-sBQV6zfLQpgK0+ddZ9Cf5Xgc8haZFVL3FQIYrNCPc9BYnz5f8ClGyxsScD1MPhI+cq9BmVcDQ1N4I4B9aWqAng==", "signatures": [{"sig": "MEUCIHIrfuOrFWUDEW36IWvs/vKG1Qd3PkjUeNP+HOMEGz/mAiEAhAJOGXpDIFpDH/YWw6YZxpw7vTK5x/dgad+EBh7CxNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "c837debfbfe0baf7eb8e2f0bbb3d1d9cc3229697", "engines": {"node": ">= 0.8.0"}, "gitHead": "b1531685ee679ca8fe329202e56d429d3c02eac0", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"buffer-crc32": "0.2.3"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.0", "benchmark": "1.0.0", "seedrandom": "~2.3.6", "beautify-benchmark": "0.2.4"}}, "1.3.1": {"name": "etag", "version": "1.3.1", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "e51925728688a32dc4eea1cfa9ab4f734d055567", "tarball": "https://registry.npmjs.org/etag/-/etag-1.3.1.tgz", "integrity": "sha512-ICSa6zJo0hne1RNfqlyIZWaF4gfpUzswBpiIT1YIc15FT/urweVAKas5/0yaV3ekgTUwqQ+ucAmskypDseDMsw==", "signatures": [{"sig": "MEUCIQDaqI5+9TWnMNyyE4ylkzhFpyrgywqFTbj+CNTvg0/2lAIgExitUC6hAFVeLgQDXk3fEnjbh7dTW/e054WtBjypD6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e51925728688a32dc4eea1cfa9ab4f734d055567", "engines": {"node": ">= 0.8"}, "gitHead": "88a83fdccc15065893cfad6e2c7af8a379941f16", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "3.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2", "benchmark": "1.0.0", "seedrandom": "~2.3.6", "beautify-benchmark": "0.2.4"}}, "1.4.0": {"name": "etag", "version": "1.4.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "3050991615857707c04119d075ba2088e0701225", "tarball": "https://registry.npmjs.org/etag/-/etag-1.4.0.tgz", "integrity": "sha512-IGmspHfAV6opq/T6Z4MU8mRgOMdiLQ+c1wj9vshUrY3sol2PNtPZXbj8BYt59QVxJ/NrJ0KiFuLnVJOJfqsjXQ==", "signatures": [{"sig": "MEYCIQCUW4RHdG9AoaxQL3jTKOjtpe95UGEr7nlIfFv7lkuDcwIhAN2DX2gx9SdRS7Xhep8O3QSHASn7BTpgrjMiJAO6sAod", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "3050991615857707c04119d075ba2088e0701225", "engines": {"node": ">= 0.6"}, "gitHead": "6b701773b06a102947cc063286e7e3f3bceae27b", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "3.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2", "benchmark": "1.0.0", "seedrandom": "~2.3.6", "beautify-benchmark": "0.2.4"}}, "1.5.0": {"name": "etag", "version": "1.5.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "8ca0f7a30b4b7305f034e8902fb8ec3c321491e4", "tarball": "https://registry.npmjs.org/etag/-/etag-1.5.0.tgz", "integrity": "sha512-jR8xaGzJdD8YlurYcfMvKcfcqUFh6iAkGXkTF9sty7FCRK0H9TGHIyNkPwr8nEqQZLrIBcKyOhuFwZMHKDrBzg==", "signatures": [{"sig": "MEUCIQCq3wJPeAlA9eCQ1aiqBrKOmJRT5GX78a0wLHcZnt5iyAIgV2PynyEijqx5MjwSw/v5IcnDRkbCpeVeLCyKl0z7W48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "8ca0f7a30b4b7305f034e8902fb8ec3c321491e4", "engines": {"node": ">= 0.6"}, "gitHead": "d42734af50250c05893f02cb900e1c71efffbc45", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "3.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2", "benchmark": "1.0.0", "seedrandom": "~2.3.6", "beautify-benchmark": "0.2.4"}}, "1.5.1": {"name": "etag", "version": "1.5.1", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "54c50de04ee42695562925ac566588291be7e9ea", "tarball": "https://registry.npmjs.org/etag/-/etag-1.5.1.tgz", "integrity": "sha512-Y+bhHICnjqZeY4I1kHDwvWTN0VcrI3ucWNbtofd0LLarRKEK8DkAL0uBdl3HCmf1HMjyrmgC/kqj+zXG5mYe7A==", "signatures": [{"sig": "MEUCIDEM2+PkBC0HJAn8DQ+C1k4WDi3tfVJl+vqYWHxeEOvyAiEAtbkPC8M8Olb6jAgvjXUF3mlQPVHKJ2Y37kGiq2caLvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "54c50de04ee42695562925ac566588291be7e9ea", "engines": {"node": ">= 0.6"}, "gitHead": "27335e2265388109e50a9f037452081dc8a8260f", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.21", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "3.2.1"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.2", "benchmark": "1.0.0", "seedrandom": "~2.3.6", "beautify-benchmark": "0.2.4"}}, "1.6.0": {"name": "etag", "version": "1.6.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "8bcb2c6af1254c481dfc8b997c906ef4e442c207", "tarball": "https://registry.npmjs.org/etag/-/etag-1.6.0.tgz", "integrity": "sha512-nuKHp9E7WegPlkpbHWPFLD0Yidt/wbV3mZHGr1tUn8apKrsRPbQOxdJm/wQH0uyz+CULQyfRzoqArVByI7WGIg==", "signatures": [{"sig": "MEUCIQDMXw1062tnVa8zmrY+pvgxPJO8Twa72JOqEezJuVpIPQIgdqAZFFla5hTFz+8E4nUQj6GrmuN2LgIQtYgcTREf/nA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "8bcb2c6af1254c481dfc8b997c906ef4e442c207", "engines": {"node": ">= 0.6"}, "gitHead": "76a8f5250b02e85bc1c9b1b049d83b853a87df44", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create simple ETags", "directories": {}, "dependencies": {"crc": "3.2.1"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.9", "benchmark": "1.0.0", "seedrandom": "2.3.11", "beautify-benchmark": "0.2.4"}}, "1.7.0": {"name": "etag", "version": "1.7.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "03d30b5f67dd6e632d2945d30d6652731a34d5d8", "tarball": "https://registry.npmjs.org/etag/-/etag-1.7.0.tgz", "integrity": "sha512-Mbv5pNpLNPrm1b4rzZlZlfTRpdDr31oiD43N362sIyvSWVNu5Du33EcJGzvEV4YdYLuENB1HzND907cQkFmXNw==", "signatures": [{"sig": "MEUCIGbmDq1i7EgrUHQx3sW6JcasxaAy1HPs5kX1uDZWGH/EAiEAtYEWskX+hIywoXYqbtuhoUOLQQOKVmQsX0yQ1B3mDsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "03d30b5f67dd6e632d2945d30d6652731a34d5d8", "engines": {"node": ">= 0.6"}, "gitHead": "a511f5c8c930fd9546dbd88acb080f96bc788cfc", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/etag", "type": "git"}, "_npmVersion": "1.4.28", "description": "Create simple ETags", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "0.3.14", "benchmark": "1.0.0", "seedrandom": "2.3.11", "beautify-benchmark": "0.2.4"}}, "1.8.0": {"name": "etag", "version": "1.8.0", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag#readme", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "6f631aef336d6c46362b51764044ce216be3c051", "tarball": "https://registry.npmjs.org/etag/-/etag-1.8.0.tgz", "integrity": "sha512-cNpO3IDp+dpOoM+0nNZYJc3ztKz+t6jLuX2Xs87qsFIrlgAqm83zPHtgrbxIREdky8mNTB2o2gIYqx9155CQlA==", "signatures": [{"sig": "MEUCIA2qkRhAim4JCkp/YnS+oQUS361UNtlhwVjrDQkKSuK0AiEAzbqZjdHDMEZPKG9DmhsDwGHiBz1i7J3vcekA+/4w6Y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "6f631aef336d6c46362b51764044ce216be3c051", "engines": {"node": ">= 0.6"}, "gitHead": "16979f788efa8c793c8d07543b4d6aef3d2bfff8", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/etag.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Create simple HTTP ETags", "directories": {}, "_nodeVersion": "4.7.3", "devDependencies": {"mocha": "1.21.5", "eslint": "3.15.0", "istanbul": "0.4.5", "benchmark": "2.1.3", "seedrandom": "2.4.2", "beautify-benchmark": "0.2.4", "eslint-plugin-promise": "3.4.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/etag-1.8.0.tgz_1487475735517_0.6724899658001959", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.1": {"name": "etag", "version": "1.8.1", "keywords": ["etag", "http", "res"], "license": "MIT", "_id": "etag@1.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/etag#readme", "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "dist": {"shasum": "41ae2eeb65efa62268aebfea83ac7d79299b0887", "tarball": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "signatures": [{"sig": "MEUCIQDmQvGw0RG/9apgqbEcy2K1dQaJbcUPSwjEIbvHrIi3xgIgfTVwPkm42HjC31VgqyWEtdzaQTV0Q0SWWfuRTO4bN2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "41ae2eeb65efa62268aebfea83ac7d79299b0887", "engines": {"node": ">= 0.6"}, "gitHead": "9b1e3e41df31cda4080833c187120b91a7ce8327", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/etag.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Create simple HTTP ETags", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "seedrandom": "2.4.3", "safe-buffer": "5.1.1", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/etag-1.8.1.tgz_1505270623443_0.24458415526896715", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-05-18T11:14:58.281Z", "modified": "2025-05-14T14:56:17.076Z", "1.0.0": "2014-05-18T11:14:58.281Z", "1.0.1": "2014-08-24T23:28:33.196Z", "1.1.0": "2014-08-25T02:07:34.113Z", "1.2.0": "2014-08-25T04:09:33.706Z", "1.2.1": "2014-08-30T03:58:39.314Z", "1.3.0": "2014-08-30T04:25:31.815Z", "1.3.1": "2014-09-14T16:54:11.346Z", "1.4.0": "2014-09-21T18:47:20.760Z", "1.5.0": "2014-10-14T04:48:49.796Z", "1.5.1": "2014-11-20T07:06:45.217Z", "1.6.0": "2015-05-11T02:11:35.427Z", "1.7.0": "2015-06-09T03:38:54.614Z", "1.8.0": "2017-02-19T03:42:17.486Z", "1.8.1": "2017-09-13T02:43:44.422Z"}, "bugs": {"url": "https://github.com/jshttp/etag/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/etag#readme", "keywords": ["etag", "http", "res"], "repository": {"url": "git+https://github.com/jshttp/etag.git", "type": "git"}, "description": "Create simple HTTP ETags", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "david.b<PERSON>rk<PERSON>@gmail.com"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# etag\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCreate simple HTTP ETags\n\nThis module generates HTTP ETags (as defined in RFC 7232) for use in\nHTTP responses.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install etag\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar etag = require('etag')\n```\n\n### etag(entity, [options])\n\nGenerate a strong ETag for the given entity. This should be the complete\nbody of the entity. Strings, `Buffer`s, and `fs.Stats` are accepted. By\ndefault, a strong ETag is generated except for `fs.Stats`, which will\ngenerate a weak ETag (this can be overwritten by `options.weak`).\n\n<!-- eslint-disable no-undef -->\n\n```js\nres.setHeader('ETag', etag(body))\n```\n\n#### Options\n\n`etag` accepts these properties in the options object.\n\n##### weak\n\nSpecifies if the generated ETag will include the weak validator mark (that\nis, the leading `W/`). The actual entity tag is the same. The default value\nis `false`, unless the `entity` is `fs.Stats`, in which case it is `true`.\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## Benchmark\n\n```bash\n$ npm run-script bench\n\n> etag@1.8.1 bench nodejs-etag\n> node benchmark/index.js\n\n  http_parser@2.7.0\n  node@6.11.1\n  v8@5.1.281.103\n  uv@1.11.0\n  zlib@1.2.11\n  ares@1.10.1-DEV\n  icu@58.2\n  modules@48\n  openssl@1.0.2k\n\n> node benchmark/body0-100b.js\n\n  100B body\n\n  4 tests completed.\n\n  buffer - strong x 258,647 ops/sec ±1.07% (180 runs sampled)\n  buffer - weak   x 263,812 ops/sec ±0.61% (184 runs sampled)\n  string - strong x 259,955 ops/sec ±1.19% (185 runs sampled)\n  string - weak   x 264,356 ops/sec ±1.09% (184 runs sampled)\n\n> node benchmark/body1-1kb.js\n\n  1KB body\n\n  4 tests completed.\n\n  buffer - strong x 189,018 ops/sec ±1.12% (182 runs sampled)\n  buffer - weak   x 190,586 ops/sec ±0.81% (186 runs sampled)\n  string - strong x 144,272 ops/sec ±0.96% (188 runs sampled)\n  string - weak   x 145,380 ops/sec ±1.43% (187 runs sampled)\n\n> node benchmark/body2-5kb.js\n\n  5KB body\n\n  4 tests completed.\n\n  buffer - strong x 92,435 ops/sec ±0.42% (188 runs sampled)\n  buffer - weak   x 92,373 ops/sec ±0.58% (189 runs sampled)\n  string - strong x 48,850 ops/sec ±0.56% (186 runs sampled)\n  string - weak   x 49,380 ops/sec ±0.56% (190 runs sampled)\n\n> node benchmark/body3-10kb.js\n\n  10KB body\n\n  4 tests completed.\n\n  buffer - strong x 55,989 ops/sec ±0.93% (188 runs sampled)\n  buffer - weak   x 56,148 ops/sec ±0.55% (190 runs sampled)\n  string - strong x 27,345 ops/sec ±0.43% (188 runs sampled)\n  string - weak   x 27,496 ops/sec ±0.45% (190 runs sampled)\n\n> node benchmark/body4-100kb.js\n\n  100KB body\n\n  4 tests completed.\n\n  buffer - strong x 7,083 ops/sec ±0.22% (190 runs sampled)\n  buffer - weak   x 7,115 ops/sec ±0.26% (191 runs sampled)\n  string - strong x 3,068 ops/sec ±0.34% (190 runs sampled)\n  string - weak   x 3,096 ops/sec ±0.35% (190 runs sampled)\n\n> node benchmark/stats.js\n\n  stat\n\n  4 tests completed.\n\n  real - strong x 871,642 ops/sec ±0.34% (189 runs sampled)\n  real - weak   x 867,613 ops/sec ±0.39% (190 runs sampled)\n  fake - strong x 401,051 ops/sec ±0.40% (189 runs sampled)\n  fake - weak   x 400,100 ops/sec ±0.47% (188 runs sampled)\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/etag.svg\n[npm-url]: https://npmjs.org/package/etag\n[node-version-image]: https://img.shields.io/node/v/etag.svg\n[node-version-url]: https://nodejs.org/en/download/\n[travis-image]: https://img.shields.io/travis/jshttp/etag/master.svg\n[travis-url]: https://travis-ci.org/jshttp/etag\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/etag/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/etag?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/etag.svg\n[downloads-url]: https://npmjs.org/package/etag\n", "readmeFilename": "README.md", "users": {"tdevm": true, "gfilip": true, "hualei": true, "bracken": true, "myprlab": true, "shanoor": true, "xtx1130": true, "makediff": true, "allen_lyu": true, "blitzprog": true, "fistynuts": true, "mojaray2k": true, "wxttxw125": true, "kankungyip": true, "liushoukai": true, "simplyianm": true, "yesseecity": true, "kodekracker": true, "wangnan0610": true, "program247365": true}}