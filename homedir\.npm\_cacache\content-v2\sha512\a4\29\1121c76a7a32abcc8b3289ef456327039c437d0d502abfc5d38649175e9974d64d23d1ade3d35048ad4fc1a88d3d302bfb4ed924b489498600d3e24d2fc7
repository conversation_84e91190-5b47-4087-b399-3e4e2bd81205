{"_id": "tar", "_rev": "307-cde53fbc94807299e85ff7e73ee4c595", "name": "tar", "dist-tags": {"v3-legacy": "3.2.3", "v5-legacy": "5.0.11", "v4-legacy": "4.4.19", "latest": "7.4.3"}, "versions": {"0.0.1": {"name": "tar", "version": "0.0.1", "author": {"name": "<PERSON>"}, "_id": "tar@0.0.1", "dist": {"shasum": "2f94ccd48020df33ade32241ccb21ea109b11f56", "tarball": "https://registry.npmjs.org/tar/-/tar-0.0.1.tgz", "integrity": "sha512-vWG/yzh21vuAwt/7vlnhASrOVlHbCtE8pt6zm82/gzev0HIHA+xj//VEqXbAHNHcZ7/c1xcPgdrEbCMni/BGcg==", "signatures": [{"sig": "MEQCICWm822dKocwy2YL4N0jdrGfdvn9k5KnwYYQ/VpwSARTAiANJQwBmRC41Il+BaurWPDyBRgHfBc/urphVkoScF5vjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/tar", "engines": {"node": "*"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "_npmVersion": "0.2.7-2", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.1.0": {"name": "tar", "version": "0.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "10fcd1d940ba7330f89c2a35ad5d16f0107a5d67", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.0.tgz", "integrity": "sha512-TwL/w7JZhsiGRR3tFks+QX1h5vCA72/kamD3DriA5AgFM1nxbyoBdZdRX83fqVAf7Prbi6kQljFHxC5Qw683og==", "signatures": [{"sig": "MEUCICA/7M15nBgG2txOm2G7+gBYYvHld/wRQ52rAp86Lh1cAiEA23YW/K9Ozh/NfUPU2njWbRPN6xNnf88nCAsR0AK9MVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.2-pre", "dependencies": {"fstream": "~0.1", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.2": {"name": "tar", "version": "0.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "52cebd0abc1456c9c405d37838d4201d5d186ebe", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.2.tgz", "integrity": "sha512-sRmod77koebkYDPwd8zfVrH44t4q0etejBlTISTyliknesZmakNGmnAEITwdRiHGY5JkEG+vLxe4Zhvx8qYwuQ==", "signatures": [{"sig": "MEUCIQCqxyK9kjUW3zCTalTY29kzl8KOpx8GC1GUEEgpLScKvwIgPqTef/l1Ti5W77f/WGPZKBJeb8f4X35UAGnii1uB1vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.2-pre", "dependencies": {"fstream": "~0.1", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.3": {"name": "tar", "version": "0.1.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "b40685c0727a74af56df9e03bae44621cbde206f", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.3.tgz", "integrity": "sha512-QqgKeT9Gjqyyl2K+1brKK76RvqZFe1tXYFLjpsIfR88tARrMP5IZ7DRSBEJF6LQ+S+OXCLvGZL8mpOnf/4+QtA==", "signatures": [{"sig": "MEYCIQDM4Ecb4VrCtaeBwN97c/3PJHCaAh6W8Yguu+M1tEFFwgIhAIC9utp/d+nDeZGOFI3LYMhEhS/BimH6czunXwheTSkX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-2", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.2-pre", "dependencies": {"fstream": "0.1", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.4": {"name": "tar", "version": "0.1.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "6af6319b23c830c787bad1b4b2ea2189a776074d", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.4.tgz", "integrity": "sha512-zDIL5Gx/gqsbEDWfkW9XJMjJ5XmX/feCGT5VcO7T8qvJ/bIVfP57sU3Ub2qXlHA10K1Ys/nbuKM5TFPAwfFaUg==", "signatures": [{"sig": "MEQCIHmlctyiuON0HkfJst8yuubG3P8+3l7Z1JKywGSsRLu2AiAiYAQfIkoftmfDoEiyxzpAeUCvAEMyjRsbuTsSvfcB1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-2", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.4-pre", "dependencies": {"fstream": "~0.1.3", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.5": {"name": "tar", "version": "0.1.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "190ad20031b539c26268f7d96a079acc1a189b8b", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.5.tgz", "integrity": "sha512-+dTLL3SNrfk9bue4EdGqAZvnOorsWAmfUirK9LSnZE+T8oGFOQBLlKb0f4QLxXd0Jd/qKRl8afrQF2Gcz27U+w==", "signatures": [{"sig": "MEYCIQDVOHcdBRkRAoRRoC1wiIO6jumtzBbuGwQBTkhGjDCmPgIhAOa6QuDDgi/HaXCF+9HlbbCtlyz1I0JuHtm+8zv+nTvp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-2", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.4-pre", "dependencies": {"fstream": "~0.1.5", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.6": {"name": "tar", "version": "0.1.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "ac70abbfac3ab0b1f18775e54fe74d6114b028f8", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.6.tgz", "integrity": "sha512-0kKXkpdXGPi0+FCzSoxfbMrvk6LIPUtk3oMBVqEFcm60f0d8Gv5GVsPrB7bORlAnAkWVZE6ND1B2O75+eWNpmg==", "signatures": [{"sig": "MEUCIQCQ9ySLmuR/9jjqgjjnrinJlVo3LFnQtMuJQOXOy/rvXgIgDwEWIKlyd7NN0/hLeDl7sv5mjGICph5nZHbO/Vh7tFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-2", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.4-pre", "dependencies": {"fstream": "~0.1.5", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.7": {"name": "tar", "version": "0.1.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "33d288cfa1fc62a3b95a265a475a960b0a5721fe", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.7.tgz", "integrity": "sha512-xoHS5yDx6W0oKUDw3IKfxrbGBrks/7rPGB8JVujBLMIGNAybxcmW1OttPAEeT+b72Sdo5Xmv2fYTMLspOUbNrQ==", "signatures": [{"sig": "MEYCIQCN7M+sLsHjDc2BZFhOkbLdwUV3Q0G8fn9DROUxA9PcKwIhALHJjP5/1WMVngfFRKGQAVC9YYcs1B7Lr2Gu8HYOqANM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-3", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.4-pre", "dependencies": {"fstream": "~0.1.5", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.8": {"name": "tar", "version": "0.1.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "39316070df0332271186688061a002c973f6e928", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.8.tgz", "integrity": "sha512-+XKtVSWnLh0mVOj5/j6uHqOk12jjMIIWVBtvD9yRrJ8jLN4QpGht5/quOYzx1bqPj43lPezgPC6OTdOCPK8hZw==", "signatures": [{"sig": "MEUCIDcIFbsuUTDN4CBuaZoy5hLEjnRZM96h+bIe0As4XdpBAiEA627lgD//wRGe2NX6aEx7dfwFAcRCxekbnhN/8v38Kl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {"fstream": "~0.1.6", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.9": {"name": "tar", "version": "0.1.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "e68653171937dd505c2b2e57bbcb9e0c780de1fa", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.9.tgz", "integrity": "sha512-sWlPGnyYhlDFtMBh8Av9Swx6C+wQjHLXY827z8CqcNTwfPDgU+cHuTISIEHDkXVJXc5FMRpNGV51a+gQZAzO+g==", "signatures": [{"sig": "MEYCIQCoz8mfRzIAnKOU70K56+yVlEtnUU7M5fUKJ5D2ZxcjlAIhAIyaW97HVKR6htIvsU0lvbrfidjUveQ92nHieIDNao+9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.6-pre", "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.10": {"name": "tar", "version": "0.1.10", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "4bd1ad67043152899df0836c2fc8fe69d8521a55", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.10.tgz", "integrity": "sha512-ZnnIhQR0b/M/FxakXyI6/q5ojglFEA1VwuTuJYD1yAVOagKNCuXbbEvKGpDFPy33SFXvGuz5u+s0UtB4sAyACg==", "signatures": [{"sig": "MEUCIHjjHpsjtgsBPOAlDY5fkj6FFoNUIO586FRYpXUhl0RfAiEAzKVhT070BKqECuwrHzF8za0MgQ/Pm+Zryk22qX/lXek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-beta-8", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.11": {"name": "tar", "version": "0.1.11", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "621119ee024da5c5daf4dad6255ce858763a9718", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.11.tgz", "integrity": "sha512-STR6jTv6l967CVvstdLWVOMPmOqONsbxt8HoyBwkfoDzTHkKh3U17MKa9VY32XYZjJ3yP1K3ESkWwqALR/MQlg==", "signatures": [{"sig": "MEQCIFBQ1UDSMCXKNJIXWYcBIoa4uhKwBZygmYM+lmmcFpuVAiBeNzzeP/GZnZ+pMvEGprp8TBQkfW+7k3O+ovxdh+OvZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "rm -rf test/tmp; tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true}, "0.1.12": {"name": "tar", "version": "0.1.12", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.12", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "43893a50eeb87e4e604c437ae6a650095506605c", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.12.tgz", "integrity": "sha512-QeuY+ufsv3KJ+GAaXBuqgGdkc2EpQ6KNLsphyoQuN2mNEiXnhwnRGvm0Nl8B2nSQUwL3k/7PYBl1q8ggAeJxuw==", "signatures": [{"sig": "MEUCIGHX+vXaAfLTPhs20fadJZnAFvJlietlqs9AEa5LGLr6AiEAxZE9wUkZrPz3AN6ORFI3HuRQwxTWxLKkXT4htiWwBfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.0", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.13": {"name": "tar", "version": "0.1.13", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tar@0.1.13", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "5e467fcfb4b911ca8881e36eb5c1eb4ab06d9ae5", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.13.tgz", "integrity": "sha512-hmRuydJ6R1wqVzq2zD6qGArzdQ0Cc5r8ze0ZTZThHCBwxsffYwX8lbSiykFLgslIzw9GSPF1hJdQlB9HAjxtpA==", "signatures": [{"sig": "MEUCICaSf7xPOeKP0V0hTPOporgXG/DTrmUfAtYj+gl/XsZ0AiEAtp8Z+1IqSASzXvLzlUdxN5t103txiFp+FzcDBmPwaR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "tar for node", "directories": {}, "_nodeVersion": "v0.7.6-pre", "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.14": {"name": "tar", "version": "0.1.14", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.14", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "20fc8516337e9371c82adb143309911e49d78044", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.14.tgz", "integrity": "sha512-RJRRwqf/GcwLHeKp+umQN6czuK42dqKg85qT84BU0RsaDAjQpK111WPHXQXWbltwrORjxlhJo+gs90Td5xpDOg==", "signatures": [{"sig": "MEUCIDw0F03MxmVmanPq0fVQVRBQNpfl1CfrMPGYfGSCBaoRAiEAxRGLJumHXha/acq3x6JOQ8dmSzEUsu2qG7pO3ulod00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.1.68", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "0.1.15": {"name": "tar", "version": "0.1.15", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.15", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "5c8f42e0c8a4392625274f132df6286a6e62c408", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.15.tgz", "integrity": "sha512-nb/rhOuuQrW5p5OMXFBGTUk5g6TR/uA2N/jXju3y5VO8B2wYNjNIBOKu34h6tDxnP5XmM2UIxrmHyPxpg9/3Qw==", "signatures": [{"sig": "MEUCIQCvu6mCK55Hf4iILM6/pWPs8Yn+Qdg9Zv+yUGH602rL0gIgTdv9Aylr124eJu31Ihqg/by5x5SMTK8eNHTu0pSa9W0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.2.4", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "0.1.16": {"name": "tar", "version": "0.1.16", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.16", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "4e0d9cd1fe08c9cfd1e6edb497153d1636617fee", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.16.tgz", "integrity": "sha512-HALEU6/Td0dY8jOf5yPe/zBGcbunXSCEv1sUEuZRvKNhMSkgEtUMFhmkCCpRyD+BV0TqVGZ4dbSsR5ZZpYS16g==", "signatures": [{"sig": "MEUCIFUIfvWXTbBXl9VT6KTVyNL7NcuootkYPHrRiTrlSXPkAiEAq03LzDrRSEoeVTpQbBe9/8wCBlD7KRsPfvAMy7mvd4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "engines": {"node": "~0.5.9 || 0.6 || 0.7 || 0.8"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.2.4", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "0.1.17": {"name": "tar", "version": "0.1.17", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.17", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "408c8a95deb8e78a65b59b1a51a333183a32badc", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.17.tgz", "integrity": "sha512-pmkFvH6JqkYZ+FR62iNrcq9aAM+iGkhc/gaWdlEZFeJ3UoibWdU8e6n++a28VpDJNCxpThmqfsAiHDaXbgA+Ig==", "signatures": [{"sig": "MEUCIBCImalKweqC80xou7bR+93oUwEufdZjDSRbm3hFT90sAiEAlAJ9Bg+zBSSqdo7TzE2NfY34qHtyO4CdsjDZZt+0aEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.8", "inherits": "1.x", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "0.1.18": {"name": "tar", "version": "0.1.18", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.18", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "b76c3b23c5e90f9e3e344462f537047c695ba635", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.18.tgz", "integrity": "sha512-n/G0ARVxLtm08ljk7qO4rmRIssde6+0SO0x9s5h1igiD2lWtjZQeV+MpFIWPwWVsU7DTAGHJ6oyGaN0nXn5P2w==", "signatures": [{"sig": "MEQCIBzcN+MAFtsIDoaiUon95AO8PhDXJzCTT66c0CIjW4H2AiAu0feqLcWt1N2QOm4o2bZkXAumqYArcMZdst2hOLjuOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.8", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "0.1.19": {"name": "tar", "version": "0.1.19", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.19", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "fe45941799e660ce1ea52d875d37481b4bf13eac", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.19.tgz", "integrity": "sha512-rIgWlooBwcq8vgE9AbxcCicx9dd1hQw5OOzhtpRrDBVwngKoh3yaE8ICD9RzZUPdOu/nX4Y9qunZghKAoc3GTw==", "signatures": [{"sig": "MEUCIQCVYNgyszaa87xQARShPhuQz0tzW6+eVQcaSgUc9a4qcQIgK78MLrw3ne9Ua/dtU3QYv4svvatxWlVkVMh37ZI/IW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.8", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "0.1.20": {"name": "tar", "version": "0.1.20", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@0.1.20", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "42940bae5b5f22c74483699126f9f3f27449cb13", "tarball": "https://registry.npmjs.org/tar/-/tar-0.1.20.tgz", "integrity": "sha512-RW79YhJI8vSyOcVv0OzOr7DVGkhtFhyz6cufNETPF7dmCJ/kQH+ubpCZkV8Wb7RvQlPuGplQPy0o2ihElzkEZA==", "signatures": [{"sig": "MEQCID8Kd9pd/GKDdPZe+3XOYi5xB3K4lnRBAIdPpcYANJhSAiBa0i/Qx/8+ng+C9igK1bx9DHuFor8KAZhOTgukJ+2RuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "42940bae5b5f22c74483699126f9f3f27449cb13", "gitHead": "b5931010907cd1ef5a186bc947954391050cbcce", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.4.16", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "~0.1.28", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "1.0.0": {"name": "tar", "version": "1.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "36636d76e8ae12b4bc11a940ac606b5ca8a5fe1f", "tarball": "https://registry.npmjs.org/tar/-/tar-1.0.0.tgz", "integrity": "sha512-kc/JSzTO1QyzWBwNP2SMv6H8lAZZGkiuqORztYo7qnqjDjFPji3D2rznusngJceuxzXYlMOg6C0xXNPpyxQw+Q==", "signatures": [{"sig": "MEQCIDXIt34jqOsDKUHp5mEnN35d86RtlWAOw54inA9hGvrbAiApfaPtGa/Gli6zHGvDPmWLzb1WtGgYbhEEm2fLI8pLyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "36636d76e8ae12b4bc11a940ac606b5ca8a5fe1f", "gitHead": "49979621a55c73c3f668d8e01830eba1ea9df862", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.4.22", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "^1.0.0", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x"}}, "1.0.1": {"name": "tar", "version": "1.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@1.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "6075b5a1f236defe0c7e3756d3d9b3ebdad0f19a", "tarball": "https://registry.npmjs.org/tar/-/tar-1.0.1.tgz", "integrity": "sha512-v/h4WGLjqGY23MA6/aK9VrLBTriD1qCY9ZwXzWIJdiDVnI9DNV/v++VTSKolbJxA1Y+cf00iydP/MGdgToB2gQ==", "signatures": [{"sig": "MEQCIHi43MKMlrfC4Dyjm5Shg4D1y9h0B+8PZe/3G3BSePDoAiB1dMuQjpoS1Qa3b+n508Vv+FJaiFSqF8MNoL0LqpLviw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "6075b5a1f236defe0c7e3756d3d9b3ebdad0f19a", "gitHead": "476bf6f5882b9c33d1cbf66f175d0f25e3981044", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "tar for node", "directories": {}, "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "1.0.2": {"name": "tar", "version": "1.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@1.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "8b0f6740f9946259de26a3ed9c9a22890dff023f", "tarball": "https://registry.npmjs.org/tar/-/tar-1.0.2.tgz", "integrity": "sha512-2Rmw++RNkX86qPI/sgS3XFzwecGjSiG1+INHhl46Je9Y8938snM2vIk7niI2cPX4Ic4xDwLHBYW3KJ3ZvZVORw==", "signatures": [{"sig": "MEUCIB2kfaLoAtPETjl2B21Q5Ed4UjqXFU+wx7QGUz+pshhLAiEAtKmRebeUX26fLWuSRF9ck/H7rd6CNFxKKMUJNX3PwAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "8b0f6740f9946259de26a3ed9c9a22890dff023f", "gitHead": "f31811bfa4ed1d1a89c380c65595ac92474dd84d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.1.3", "description": "tar for node", "directories": {}, "_nodeVersion": "0.10.31", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "1.0.3": {"name": "tar", "version": "1.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@1.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "15bcdab244fa4add44e4244a0176edb8aa9a2b44", "tarball": "https://registry.npmjs.org/tar/-/tar-1.0.3.tgz", "integrity": "sha512-/quI12ZS9lw9pGwkxXrSicTSwujEDJWdGLLlzRG6SR7Yih11Efy3paaKof6TUqf1emBIt3z/Ua5qFxpm9JCTbg==", "signatures": [{"sig": "MEYCIQDjO/YboKzoNtypU6X/EHy+rGhotBlsHcQmtxoaNYELvwIhAMbxMPoj8suxdIEuJU5mYQyFC1M3dZ7cEyx880VJdaFt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "15bcdab244fa4add44e4244a0176edb8aa9a2b44", "gitHead": "f4151128c585da236c6b1e278b762ecaedc20c15", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.1.10", "description": "tar for node", "directories": {}, "_nodeVersion": "0.10.33", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "2.0.0": {"name": "tar", "version": "2.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "7cf627bc632167766ce2a5a1f23e4ecb8e3f28bc", "tarball": "https://registry.npmjs.org/tar/-/tar-2.0.0.tgz", "integrity": "sha512-XOCzEegB7fnwSqdn5zsndAg7MHz0JysT1Sz8mDX0c2Y9RbbBfvjPlxOy+XmVg7ePbrQY7mxDx+8jx4VGGBmrHQ==", "signatures": [{"sig": "MEYCIQCDskq9RyeRaS3GFiT03wJAv5/Z8GpmU8upTlRvAG6sFAIhAMmo75els+L2NfVt62aQkiH7x+cXcic51jDDBgaNZZAn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "7cf627bc632167766ce2a5a1f23e4ecb8e3f28bc", "gitHead": "9bde260b9ebe6808837a85bedf9c6f7bb04e004f", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "tar for node", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "2.0.1": {"name": "tar", "version": "2.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "a1537ab0d1ce61462ce87b4eed1cd263fba5fc17", "tarball": "https://registry.npmjs.org/tar/-/tar-2.0.1.tgz", "integrity": "sha512-bB62AcFkbsdJXLXr1K0mlO5cVcqI7TaUXFwkbHpfwJ7Mzqn7R5DNQZM0b3kuDpdREtu0GGN+ml6QJY1FRV7JMg==", "signatures": [{"sig": "MEQCICOUtueI9A/SgPWDm2L+uFf+qM06IgBOx7Q5h7Wn+9qMAiBgE2iHpTgFgFAkmqi52YrqzoZvRYPozsemMRKoqVv2Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "a1537ab0d1ce61462ce87b4eed1cd263fba5fc17", "gitHead": "ce405d0b96f0fe186dd4cc68d666fabb0c59818d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "tar for node", "directories": {}, "_nodeVersion": "1.4.2", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "2.1.0": {"name": "tar", "version": "2.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "tar@2.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "d287aad12e947c766e319ac364f3c234900f65ec", "tarball": "https://registry.npmjs.org/tar/-/tar-2.1.0.tgz", "integrity": "sha512-+2s+I4nV0EtP5geFZCxNkQI3O5jodB2toStlzb9+RJoVHB7MVQ/fHLXhLHhV/pjkb1U7ES4FAZ0N0iXnxDS+5A==", "signatures": [{"sig": "MEUCIQCTDdJSLLVy4z/9DnoxJh74dOBhfvVP/8vyYfJjJV7vMgIgBY92l54qN8URVt7/U8+hvNVfp9+ciRnYvFLZehpnuGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "d287aad12e947c766e319ac364f3c234900f65ec", "gitHead": "b4c03a8e922fa522a3ddadaf2764bc1ab38d484e", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "tar for node", "directories": {}, "_nodeVersion": "1.7.1", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "2.1.1": {"name": "tar", "version": "2.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@2.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "ac0649e135fa4546e430c7698514e1da2e8a7cc4", "tarball": "https://registry.npmjs.org/tar/-/tar-2.1.1.tgz", "integrity": "sha512-E3Dh0jsaXqrqHOYiUaOEardfoT/FEsq+4i+abbtsroXwdVH5SajgQVaILfXwV5O3UwHKSEOhcvW5YcfF8n2I7Q==", "signatures": [{"sig": "MEYCIQDR+an5Nqa2nhxvDuVFgxrJzzJK0hSwn5kxdMPFRCXU9gIhAITZAUGHQYLjN7QM64oLowH76Q3yC915C8zWlyEdAcHT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "ac0649e135fa4546e430c7698514e1da2e8a7cc4", "gitHead": "2cbe6c805fc5d87ce099183ed13c43faba962224", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "tar for node", "directories": {}, "_nodeVersion": "1.8.1", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "2.2.0": {"name": "tar", "version": "2.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@2.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "527c595940b9673f386c7237759982ab2f274d08", "tarball": "https://registry.npmjs.org/tar/-/tar-2.2.0.tgz", "integrity": "sha512-RMpnyvcR174rI+jivaKUAkyj/GcNnMMdKo+IjQW8QELO5y+hgfJZvJ4HhTiqomzhkA0Dmj0RoQL95j5UHLrhIg==", "signatures": [{"sig": "MEUCIC4XY44mNdT7e1IJtP1efjRnWKSETJb82aOKOIDLLbOMAiEAgq51auxO0IdvMdEOA+EI+0Wlr7Bz0pkMcV4q0XKtnxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "527c595940b9673f386c7237759982ab2f274d08", "gitHead": "3cab63959c51451a84cc8d1f8ef02d45b8b4f836", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "soldair", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.13.4", "description": "tar for node", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^3.0.2"}}, "2.2.1": {"name": "tar", "version": "2.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@2.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "8e4d2a256c0e2185c6b18ad694aec968b83cb1d1", "tarball": "https://registry.npmjs.org/tar/-/tar-2.2.1.tgz", "integrity": "sha512-2Tw2uNtZqQTSHTIMbKHKFeAPmKcljrNKqKiIN7pu3V/CxYqRgS8DLXvMkFRrbtXlg6mTOQcuTX7DMj18Xi0dtg==", "signatures": [{"sig": "MEYCIQCbU4M2+5BFoQ78OCpNaJTPGnHCnyfnhh64S3/joWhtMgIhALjmnx0vF8p/VLyzVSOTjRmPWzzgpmoTp9rIur+ph3XY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "_from": ".", "_shasum": "8e4d2a256c0e2185c6b18ad694aec968b83cb1d1", "gitHead": "52237e39d2eb68d22a32d9a98f1d762189fe6a3d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "tar for node", "directories": {}, "_nodeVersion": "2.2.2", "dependencies": {"fstream": "^1.0.2", "inherits": "2", "block-stream": "*"}, "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^4.1.2"}}, "3.0.0": {"name": "tar", "version": "3.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "cb7b29da4edfcffd06849a1be3d4aeade23daf48", "tarball": "https://registry.npmjs.org/tar/-/tar-3.0.0.tgz", "integrity": "sha512-Dkr/uUoaelHrifTp4BkVY/jldqgD+Sxo8NOdyT6dkby9R3U9G4gH6Sw4buKgPrGYQgFqs5UK2j0vB4W6nqTpXQ==", "signatures": [{"sig": "MEUCIQCyEpnyBov8C/LlI4zA8DBDe08lTHSCMUaqGEVuxtZ2FAIgS7sOfeERf9La4i4Rnhf2Znb43skgEJPnFoPdVygFrOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "files": ["tar.js", "lib/"], "gitHead": "1098f4b91d2f51aab7301279fdfa104b12eb45e8", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.0-beta.44", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.1", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.0.0.tgz_1494379464880_0.09705448755994439", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.1": {"name": "tar", "version": "3.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "b677a7963caf00fb1cd5ac4d17c5cd22aa46b33a", "tarball": "https://registry.npmjs.org/tar/-/tar-3.0.1.tgz", "integrity": "sha512-IqHpYSK33G55aw9qWHIS/0+kO5ZJUBX5hKS5xxAdoUj8nbjO+txqKjwXCXUzJcbIjZKbSqXO9lYBBmorRRaDQA==", "signatures": [{"sig": "MEQCIGjLw3cOTbxohBKeu/ItGe/BmsyQsz2qNJidBp+++annAiBLRUNEs+SkvUnwB4W+IQECWixwupkmuEmZlyT0lvGsGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "files": ["tar.js", "lib/"], "gitHead": "c3251a725663d014ed84f3a820d5e9e3167996b7", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.0-beta.44", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.0.1.tgz_1494438955537_0.8215328669175506", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.0": {"name": "tar", "version": "3.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "9d3cc4dafcb96d6786ac802b564ec01de6eec2b0", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.0.tgz", "integrity": "sha512-idZ4x9VQS7gLoxxnS1QCjtl6Xu1GaPnHY6o1d5o9aN1Hk8A/Hk9yNfTprV12Y/8BuSEsSbhe6G4O0GQz5wyYPg==", "signatures": [{"sig": "MEYCIQChW2iREsFq8FOttpwCN1xAjw1we50doYx+q5YnCyYEVwIhAK7jnOiYBKLQK3MVMSpZUziIvlqUw6IxIgX6Pun8ar7q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "files": ["tar.js", "lib/"], "gitHead": "2ba07e56f7f712cd90c20db9a7d1a99b1fc7aeed", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.0-beta.44", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.0.tgz_1494607398991_0.9328433778136969", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.1": {"name": "tar", "version": "3.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "3e6fa6949ae903f34d3b30131f825b6f0258d2e1", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.1.tgz", "integrity": "sha512-UrbClqQLaiYVzR8mMmYDzbgyiexM+zX/KlYktBMu9V6yOvYvShxlbdb00xjJYmdi91sapqnq7EqAtXIKqI+Ktg==", "signatures": [{"sig": "MEUCIGjFm7SHGodkCUiZv/3agMxGao9QRHk8fdnieOLsWonnAiEAoUwlOYMuiBFq4/1d3naZ4+sXLIHxGLrs3won0a6MApM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "files": ["tar.js", "lib/"], "gitHead": "51f0d2ea7162c2e9f02c0e6845b82cfa94c71cbd", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.0-beta.44", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.1.tgz_1494608536218_0.847736562602222", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.2": {"name": "tar", "version": "3.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "b4f3c86dd177822eb51922f8418cd91d4a41b19e", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.2.tgz", "integrity": "sha512-hBYJ51xunos1Ar3aCsT+ApNvT9GBEG31hwwM4CryTToHcc7gl6cAt9lHW7YCbOz3FFMtVX7c49FqRQYmGrIqSg==", "signatures": [{"sig": "MEUCIQCjzbiLM+3W++MwvK20EBYoWj1tlPvLVmzlLwrrJiQWYAIgbAtNTUAsMvPaPkoLtHx37fQun7LL/EJYEAqSIVKuq0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "tar.js", "files": ["tar.js", "lib/"], "gitHead": "0525639c2d512791752ae9c4bf0f23eccd7e6833", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.0-beta.44", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.2.tgz_1494629671784_0.9335505347698927", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.3": {"name": "tar", "version": "3.1.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "b4a04aafb5375a969a9fb7cc08e48a8ac3514277", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.3.tgz", "integrity": "sha512-C7Ets2EikonCWW+JzLJsGxoy5CpYpR4N80EwTRaFw3aMXa9IKmsg2ItcjPT/dTaKTuROmdc/Ki/VBh5v7wNH9Q==", "signatures": [{"sig": "MEQCIEDEGwsyFmgr3Y6xNerGPSx4glhVuZOt4dAlnv4RFO/GAiAHmMQF3ZE73aLDPFoaTNF+1ygMPSqq6+35nRmfhyOx1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "gitHead": "a570fadc432ad91f3627ca09cc69c7e38ae39bed", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.0-beta.49", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.3.tgz_1494896164709_0.45167989330366254", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.4": {"name": "tar", "version": "3.1.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "53afbcdb7140bbe0dc2ada0a1b14e14b3ef541b6", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.4.tgz", "integrity": "sha512-Y8sSCIdq+mxBbYaEMSjHRnUPGSfTAp45Mb8+QvbJ0/6f6f5qToRW+N7Q+d1+Y/26WleexXiQbTQ8RwjprIuddA==", "signatures": [{"sig": "MEUCIQDufmlGxCBsTU8hygLfQtK1AkcjjI3OpZj1O3dY3VpLNQIgPquDTSKL8ju8ujP1i0Et8bAIVf+7bf0Yq8emV6gIjrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4"}, "gitHead": "0baac1f2274db222d7615c98bec9317947ee69e8", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.1", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.4.tgz_1496446872587_0.1321567993145436", "host": "s3://npm-registry-packages"}}, "3.1.5": {"name": "tar", "version": "3.1.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "4981e97ab7bad4cb1d5da9232047c9047a681aef", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.5.tgz", "integrity": "sha512-TKJKz1fqBOZBaIQ/MGRKU0EnTGmKMLy4ReTRgP10AgtfOWBbj9PBg4MgY80GFpqGbs2EzcIctW5gbwbP4woDYg==", "signatures": [{"sig": "MEUCIFPTnLZtXJO1DMQ1O//h4jJI3xNzBsCGJ43qDZod6q8uAiEAkgfi8hstt2P5C81QHAob9+kFXsyW99ZHjXFY8yF3JGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4"}, "gitHead": "fce07b25fae8b42d0f8e594f7d4d52e7077a2596", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "tar for node", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.5.tgz_1496449018542_0.8640544661320746", "host": "s3://npm-registry-packages"}}, "3.1.6": {"name": "tar", "version": "3.1.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.6", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "27af227680177bcff2bd8d690867dce5eb8511b8", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.6.tgz", "integrity": "sha512-yPMyN763J2vdUrW5omojOzGIEI2rg0m24Yb5OqVA1LQZ1FKe+WSPJqz8rN9vcSpr4OAk4IioFpC7cYPH786a7Q==", "signatures": [{"sig": "MEUCIH94Cc8CRllWNw+SCiPuv1DJDyUalmWmG2hHxcvqZnKiAiEAy1RnBeSJAOmWSBTA6Drq7EClS19mR4Edclx9wTKhjTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4"}, "gitHead": "edc4a0c4644c1378b9aa9dbc259d69d77ab733a4", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.6.tgz_1501023798446_0.3663195220287889", "host": "s3://npm-registry-packages"}}, "3.1.7": {"name": "tar", "version": "3.1.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.7", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "ca4a4a6259748dcdaa38f5bfa5c76fe9e4c89fd8", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.7.tgz", "integrity": "sha512-FWD5niz4Dt/fktgui9NjyDknmGJHQaoooEyLc9uRM72CcJZuFOqIB1GAB3msTrxJ4N9/0QkrN1U8Q5pSpZRJQQ==", "signatures": [{"sig": "MEYCIQDzxr0E1KEnsiXGYxAlsVj3rir6UCjyGQK4e+J1vZnNMAIhAMqwFZFwm5/0Glu/ijxEVNTnNgqJhIbsiLeh2q40S6NT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4"}, "gitHead": "18af12da7b639d6ffc3845f910f5e27a5d681ab0", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.7.tgz_1501092185757_0.33061009529046714", "host": "s3://npm-registry-packages"}}, "3.1.8": {"name": "tar", "version": "3.1.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.8", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "578861772c1dce2133dbbda6cdffc42be39addfa", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.8.tgz", "integrity": "sha512-Ib0gFf5PaRxDHeUf6w8i9w6M0qV0vK2/RFbD14PSq4e1CDYoRQyV9N2xTJfDw2BfLygJAO9ZBvYIqZTld+fiOg==", "signatures": [{"sig": "MEUCIQDhmful81dzhU2tt4qP7XNPrBoYcCrewv0RivQiMfSV3gIgRLelRVO/Y9Y08GV2A7sGZbB7GQRn0PhSggaDNVLRyLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4"}, "gitHead": "4b5fe3469c0280bacfa740a757da88c1ffabcd26", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.8.tgz_1501627989373_0.34951902367174625", "host": "s3://npm-registry-packages"}}, "3.1.9": {"name": "tar", "version": "3.1.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.9", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "2d58fe9fc5dd54652387746e1415223229c9bbeb", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.9.tgz", "integrity": "sha512-/Aiui1ynEeSz6rr8RhLk3Vp9BEbtYnlIauKHco1ILlP5U6W6SSUWnDtycqTPCovdnnoutKHzzCOchZqEfCcvVw==", "signatures": [{"sig": "MEYCIQDHlhKzIiNSAzr96liJCoOrCY8X2JXh2RTStu+/rpaO0wIhAKfHBvA2uDG6fKuYEbGwZOuQYEE95Zprpuoc/dWhf3qY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4"}, "gitHead": "747bd164449a20689cba13001922375ffde4223b", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.9.tgz_1501714110946_0.5878034713678062", "host": "s3://npm-registry-packages"}}, "3.1.10": {"name": "tar", "version": "3.1.10", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.10", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "86d006424b2f5cc9a89fb02711f7e68757147582", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.10.tgz", "integrity": "sha512-7G41HWrzHoujtnxRw5nYWSnA/vNIj6z+MnubIDMdeOrBvXSgIzYy/KcWlLALABd4gEbETJK2ds0ZEYmua33dFQ==", "signatures": [{"sig": "MEUCIFVKQq473TgmYlRlnJazULKo6UD0EqOsKJDDdgZOVW2LAiEAzb6sGXwXcdgxSYSg1jZNaRGri7yrOr9DF3x+F/Mm5uY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "9780dc159e080607f24a6bee81277db2ceb0892c", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.10.tgz_1502782962912_0.47463428881019354", "host": "s3://npm-registry-packages"}}, "3.1.11": {"name": "tar", "version": "3.1.11", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.11", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "5e2506344f17d0bed442c6ee9c2d226d3c905804", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.11.tgz", "integrity": "sha512-XkLy/D6+cqRED5zUf5KiAR9lAzAG6LBf04L/yiR9gZLgcQmUoNvqj4RcO+wSyxYEYNlAk6ivQsMdZ2B6z3Gy2w==", "signatures": [{"sig": "MEUCIQCCohqOyj74CzPTP7JTFrWcPclW9+OK8sNOfehFcQNR5QIgc1LrWIgbj/GmtJksVzvHoGIfASw60ywJmRW2qn2OfEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "474686b31782ae009ac0761b74ec07f91084aefc", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.11.tgz_1502814844953_0.422957036877051", "host": "s3://npm-registry-packages"}}, "3.1.12": {"name": "tar", "version": "3.1.12", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.12", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "7c46610330ca9088af11b961c95e64dcacc31ac8", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.12.tgz", "integrity": "sha512-mKpGqIzTQSvY9oRBO9NkfF8LjoU38U3JIeL5DD9dfB3Z1Z7bJCQWFkC5OgZGOV8ee38MBb+6KnQylIxVekTF3w==", "signatures": [{"sig": "MEUCIE3ZMoufOPeKhVuUNqUoKkRLy0IuNbZASV74+ke8OrEMAiEAkvNwFSw2nUhm6p4+DkAUQVYKKNC3pbQ9ynD7bhZeLfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "587a459ce7c72cfd50239188a1d7f6d095c44cb0", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.12.tgz_1502840731877_0.04427452408708632", "host": "s3://npm-registry-packages"}}, "3.1.13": {"name": "tar", "version": "3.1.13", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.13", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "f035a4f5b8b0b51b94530e20080f8bc696e3e320", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.13.tgz", "integrity": "sha512-jDTf6d8VGRRNdYF6DjsagbRKy7LQvjXs0VuqzoJoSJXRY/wUlVttR8Ciz+sULGzLldKxbkCQAHfi0cpVpPdDmw==", "signatures": [{"sig": "MEYCIQDyfEyPMYDtyLIsGjy72dhW0R5EH2vEFdJN3a4ouZgghwIhAIuGEFMl8zKd+kFj3dw9qteP0ydXitBcHxRFxDTc4qST", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "2176335779cc0c7fcd28c778eeaf0b1d8963c254", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.13.tgz_1502843523241_0.4201464627403766", "host": "s3://npm-registry-packages"}}, "3.1.14": {"name": "tar", "version": "3.1.14", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.14", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "fb25515074360457de49f001e2e8a49c10aa3b20", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.14.tgz", "integrity": "sha512-+WVxPQOhypYpDUymCYQ703cfQaGZKUYT8aE/ebQKBoW3ntsxDFgKMPA36bAH7b8HbB+DMZe138jyrLo2ba8U9A==", "signatures": [{"sig": "MEUCIAw/eZGPFUAswPfbKq18X3zsruIsHDsiHznFc3dfy+ndAiEAuwybw+VVlOQOLd2kypv/hlAQyMJsH59n/LIZNQoBC/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "0594dcf3c8c2d79bf71261f7cd1e462a7af65cf8", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.14.tgz_1502926217498_0.000836462015286088", "host": "s3://npm-registry-packages"}}, "3.1.15": {"name": "tar", "version": "3.1.15", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.1.15", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "cccdc35b90917d58e4c3837795d5d022d7a1f46f", "tarball": "https://registry.npmjs.org/tar/-/tar-3.1.15.tgz", "integrity": "sha512-pQNFsg+Wb6VXsrIPUnuQwrHR4wD5ASBR0jRyiT4/AALFA2Nl+CjhkDX5fTmIwCuULRtyQR3Dae2BBnP2EFHscw==", "signatures": [{"sig": "MEUCIHVQW90dCYa9+30pt7c2xdHlNB2PsDr8sTLPJFnr+Dk2AiEAtOdN+LIfOUA7oj1hoSPmYVkqlnskYJtckxpyI354HcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "ef33670d429409558879ce616d4347fe4ae0a52d", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.1.15.tgz_1502931034160_0.11256602685898542", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "tar", "version": "3.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.2.0", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "534255ba01befa11f91257cc7fa9cea773596852", "tarball": "https://registry.npmjs.org/tar/-/tar-3.2.0.tgz", "integrity": "sha512-YRHTZzumkYuLx/lXsWZVowi9WrYbOQvxbEDtuFjq+LPCtLLGleWLkL/pdCnTwZMA92+Vg3UwVRBjpVQNcg6H3A==", "signatures": [{"sig": "MEUCICS1lIF6aVaQGDTLIiKczz6lOtOxqIgC9SHhnCNhjlpYAiEAtKnq8kIfjt1dlcfwGQpFM2B4H8yhczi/L7gVjYTspOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "6a86ec4963d081e8362081a3a431f5b75a91984a", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.2.0.tgz_1502931349675_0.5638887928798795", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "tar", "version": "4.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.0.0", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "aa7d58cfb750cc919d9da1d3319f9ccabac339e0", "tarball": "https://registry.npmjs.org/tar/-/tar-4.0.0.tgz", "integrity": "sha512-oHhf8rlu/1pLR0fEaUvkjTFScTBPWxyH6rxCURo4NWHjM7T0WzPvVaIZ1cw3VfdKc5852QAWavADhR14XeoaEA==", "signatures": [{"sig": "MEQCIFljrCjeu+3RFPwDLGvQipQLDame220RPA0Ybwz4g/84AiBx2UXSla296eXM9BQXkREgwHkqU1NdO8ahUnldi1nsTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "90ac1e8464b819131f8dbe69374e879f16998253", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.0.0.tgz_1503095073992_0.6837309603579342", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "tar", "version": "4.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.0.1", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "3f5b2e5289db30c2abe4c960f43d0d9fff96aaf0", "tarball": "https://registry.npmjs.org/tar/-/tar-4.0.1.tgz", "integrity": "sha512-XBpU+/azPOMvE5m2Tn7Sl6U1ahpGfe77LkdrAlFilwrgHZsR+2iy0l8klQtfJNM+DACZO2Xrw10MTyQRB4du5A==", "signatures": [{"sig": "MEUCIQDCmF8MKKdKUVagUUF0L9D8jOiN9kZ7bUCUbgorAq+DDAIgfzBms38oi8uQg0o0yu93xOGESHniGykkSAjR6o/ro5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "89a14aa4d681ee4745d5038517c031154c2c8546", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.0.1.tgz_1503277822930_0.6976792945060879", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "tar", "version": "3.2.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.2.1", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "9aa8e41c88f09e76c166075bc71f93d5166e61b1", "tarball": "https://registry.npmjs.org/tar/-/tar-3.2.1.tgz", "integrity": "sha512-ZSzds1E0IqutvMU8HxjMaU8eB7urw2fGwTq88ukDOVuUIh0656l7/P7LiVPxhO5kS4flcRJQk8USG+cghQbTUQ==", "signatures": [{"sig": "MEYCIQDHJlfhrzpLiARKSwBWzk/z2VPNeH6rJy0rFhOvNLoaCgIhAM2cVKowfbYMehIwBYZMm0eWUYmAen4FtsKlX9MXBcxs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "1dea1df937846192453b8dfd02982c02c556c1d1", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-3.2.1.tgz_1503277952302_0.7272206638008356", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "tar", "version": "4.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.0.2", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "e8e22bf3eec330e5c616d415a698395e294e8fad", "tarball": "https://registry.npmjs.org/tar/-/tar-4.0.2.tgz", "integrity": "sha512-4lWN4uAEWzw8aHyBUx9HWXvH3vIFEhOyvN22HfBzWpE07HaTBXM8ttSeCQpswRo5On4q3nmmYmk7Tomn0uhUaw==", "signatures": [{"sig": "MEQCICF9/wI6YA6fyu75aL8i90Ucvz5TiTddYhL+BvIDuryKAiBqaAgApMzi3heyrXMw8qta/tdrPaf3PEyAoW+hkPXCjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "ec864233e717a978553be174dc352f9ca411a8e4", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.5.1-canary.2", "description": "tar for node", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.0.4"}, "devDependencies": {"tap": "^10.7.2", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.0.2.tgz_1508355005096_0.7286235468927771", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "tar", "version": "4.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.1.0", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "3344ed174eb2c6c3749cc1bbee391395962256d9", "tarball": "https://registry.npmjs.org/tar/-/tar-4.1.0.tgz", "integrity": "sha512-xQoupzP/LFDSz3llXIS86u6PHAqzc+AUVJLLcaKg898rQ8c71cmklu0G05NQpIKqh8XNF+Pv+EptKySI7rseNw==", "signatures": [{"sig": "MEUCIBiXHQZtPjybpAgdsj8Wv5vJhCsDzmggmXDRKzHFAxOGAiEA3aiFYQ3HUYjOITPtqKofkmkI9da46SOWcxevnjtrgok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "61908e63aec9eb437b15d34a81e7baa956d13a7e", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.0.4", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.1.0.tgz_1511831257451_0.5656173080205917", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "tar", "version": "4.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.1.1", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "82fab90e34ac7575f925084de66cc0d2b4a4e040", "tarball": "https://registry.npmjs.org/tar/-/tar-4.1.1.tgz", "integrity": "sha512-p2lLtRABOEhuC28GDpMYwxcDYRqAFfiz2AIZiQlI+fhrACPKtQ1mcF/bPY8T1h9aKlpDpd+WE33Y2PJ/hWhm8g==", "signatures": [{"sig": "MEUCIQCeMpSOZNiB+EjaTH857hylWR3dj9kmn+T8cnwj2NsVBQIgPDfTXPArfBa/xyLj513lLHe49I/i2SDMQvjEXxgny2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "1bfffcfd65df2b4254d85e34f4121b53d11fa4a5", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.0.4", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.1.1.tgz_1511898050474_0.540841075591743", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "tar", "version": "4.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.1.2", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "40a9da18ae9806d29e54a2633601205b83a9e99f", "tarball": "https://registry.npmjs.org/tar/-/tar-4.1.2.tgz", "integrity": "sha512-+yCXsTFPXvZjc7tMUQo/OYPZL10weuLfEod1CMPIYJMfo7ChptNZr4UdCAuVihZ2MHVCSs6IIYTvkXqivL5bDA==", "signatures": [{"sig": "MEUCIEsEalr6w43XAx94rUqPZuXKZtMYesdagHNq1LWIPioYAiEA9KzGncBTfPJXqvFk1g0T6LGhnDkYGPjpm3AeF7xV8/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "a4d8246ab22bd6c00d4d31c67363ac2e4244daa5", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.0.4", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.1.2.tgz_1513826225359_0.6972173426765949", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "tar", "version": "4.2.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.2.0", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "7e2bdadf55a4a04bf64a9d2680b4455e7c61d45e", "tarball": "https://registry.npmjs.org/tar/-/tar-4.2.0.tgz", "integrity": "sha512-8c4LjonehF+KArauze53Tbx1tfPsWiF94cS8wK8s94wSGTWHwdVMLCRxvqe0u8fzTXfnAjlpkpOAQl240K/anw==", "signatures": [{"sig": "MEUCIQC2ywIP/RrdYjjHyaiiEXs+iN4yLJLv2kSI9nsYjN47MQIgB3J1fCGaQmCtZhbSnLbYcKMzOZRZRoMqWmn4IruV8CI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "13bd921a8b4022d518d663fecab63d15c043c8a0", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.1.0", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.2.0.tgz_1513830458858_0.795999555150047", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "tar", "version": "4.3.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.3.0", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "11351be1c7944c59dd197850119c2081d8bc7fe5", "tarball": "https://registry.npmjs.org/tar/-/tar-4.3.0.tgz", "integrity": "sha512-Ta5X6BSrA8QHznB156/nbqXUFf1M6A7rXrChKY5+6CkOjoFuOOBcJAj8FvMzDYyYBn7tb1UQNg4vUr7tkSlCUA==", "signatures": [{"sig": "MEQCIBTFlbAPnAEyeTsCkmi/OOBEQg+kGgZO4c4wzwfk+kO/AiAlaZu+N96290o5WPabkdiADVsaoXGAbQeMVyZ2JlPQjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "51b6e83761dedcce661404819571779cefd35d04", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.1.0", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.3.0.tgz_1516238225635_0.41157911252230406", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "tar", "version": "4.3.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.3.1", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "82f711646291f1583934cc5a2118611d4c43ebc0", "tarball": "https://registry.npmjs.org/tar/-/tar-4.3.1.tgz", "integrity": "sha512-10Q9Xx7GHaCPH1fFu7fHbq7w70A1aA5rB77bYbOjV79mQVwaK65mFVQwnxbwgYTE4krDqgoWz6XkbUf5iv63uA==", "signatures": [{"sig": "MEYCIQDvUwAU+hbViiZoLlLj9t738N/LtT7pczwRz6RqU1qkVgIhANyDuDgrHbODUmqL1i72owMzk9V8bII0OJCNt50As/U0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "5afba25d097657916abd57cf580657cff818c8c6", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0-canary.7", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.1.0", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.3.1.tgz_1517458964716_0.8735048579983413", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "tar", "version": "4.3.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.3.2", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "ef4e813c7cfd2eb261da107753fb8ba15ab8954b", "tarball": "https://registry.npmjs.org/tar/-/tar-4.3.2.tgz", "integrity": "sha512-xq2YLcAiE6/THltmw5cmWkwildoat5hv+VoqSX7qqIs1ELzeR4J59QEetSoSI13Bd3RFSdcar4X/pzElEOy9Ow==", "signatures": [{"sig": "MEUCIHVojeJ9WPTuJ8az1nLDkqut6mJZOqr7t7xrVSWzYKcBAiEA0AVflW7etdpwqnZBgoNMFWx4uCmKuizYljL/cugUWXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "5e3dff6e238c6ba74274dda018e9413d504a9249", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0-canary.7", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.1.0", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.3.2.tgz_1517503004727_0.008722305297851562", "host": "s3://npm-registry-packages"}}, "4.3.3": {"name": "tar", "version": "4.3.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.3.3", "maintainers": [{"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "e03823dbde4e8060f606fef7d09f92ce06c1064b", "tarball": "https://registry.npmjs.org/tar/-/tar-4.3.3.tgz", "integrity": "sha512-v9wjbOXloOIeXifMQGkKhPH3H7tjd+8BubFKOTU+64JpFZ3q2zBfsGlnc7KmyRgl8UxVa1SCRiF3F9tqSOgcaQ==", "signatures": [{"sig": "MEQCICieGgoRWsYIp38QfalMKaPvhRciEcKYN3pJag1SP8L2AiBBzBbJqO+DlXxvW5iANMZ8Q9cbofJvoMqZmgsN4SpUXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "9e92533a0724585c695e775d38fc1f64baf8f6ab", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0-canary.8", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.1.0", "fs-minipass": "^1.2.3"}, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar-4.3.3.tgz_1517948326480_0.17111232038587332", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "tar", "version": "4.4.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "3aaf8c29b6b800a8215f33efb4df1c95ce2ac2f5", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.0.tgz", "fileCount": 22, "integrity": "sha512-gJlTiiErwo96K904FnoYWl+5+FBgS+FimU6GMh66XLdLa55al8+d4jeDfPoGwSNHdtWI5FJP6xurmVqhBuGJpQ==", "signatures": [{"sig": "MEUCIQDbg9ynjsHZlFrAgJ9fPKP5KAzpQH44F+WCbpgsppkOegIgLEVKjEvJ6di+iZjnaQHeqRBzy9Ti1ukntzlxK4sl82Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129127}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "c1f5de5a621bf03474e546aa5efad5ea7b2cd2ac", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.6.0-canary.11", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.1", "minizlib": "^1.1.0", "fs-minipass": "^1.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^11.0.0-rc.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.0_1519155419137_0.8068667000360024", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "tar", "version": "4.4.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "b25d5a8470c976fd7a9a8a350f42c59e9fa81749", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.1.tgz", "fileCount": 23, "integrity": "sha512-O+v1r9yN4tOsvl90p5HAP4AEqbYhx4036AGMm075fH9F8Qwi3oJ+v4u50FkT/KkvywNGtwkk0zRI+8eYm1X/xg==", "signatures": [{"sig": "MEUCIGSlA+w0iTVg+UeHFgK8pj2wzULkB7mrlALzzXcZaVOJAiEA3PMfDUa55CS/aXaDdywe3CHveiy4/lwQ17n+w2t9oHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129665}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "58a8d437abccd6dc884541b8bf6554901ec592cc", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.4", "minizlib": "^1.1.0", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^11.1.3", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.0", "mutate-fs": "^2.1.1", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.1_1521565198053_0.7013183734265185", "host": "s3://npm-registry-packages"}}, "4.4.2": {"name": "tar", "version": "4.4.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "60685211ba46b38847b1ae7ee1a24d744a2cd462", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.2.tgz", "fileCount": 23, "integrity": "sha512-BfkE9CciGGgDsATqkikUHrQrraBCO+ke/1f6SFAEMnxyyfN9lxC+nW1NFWMpqH865DhHIy9vQi682gk1X7friw==", "signatures": [{"sig": "MEUCIQDsTs4f4LySPV0FvlsDkl5d2Pygjzp4XQ/smpu/XjDQlAIgNG40wo0TN8eh/uwYDayPNbB4ATrTjdYL2UwKZorPdzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa52ctCRA9TVsSAnZWagAAk7EP/2wFqsV2I9D2V0WGki/9\ne81pKNP0BsychjGQ/V7TNzwJIKfbvbwWR9NS1lrq77oX4IFcpT3o4JcKXDez\naV7EV2LDJuGqQqNJz+I6kzGWigEoPLYd+YGUabj1fk2+lv7W0xsmjNB0vuv/\nyOQSFkCZAyjF3o0FMrpiWMQhoVS7XXYCXWlA6/e6rgnTnj38mPGyJqvVTAVF\nx6Fmt1gvh4fyQVmJsqYMITCosYfHQcrJdArNzR0A8TlC0ueqQfu4L0s0w5UJ\ndKP459K0tWNaV3rhA3xjF4Tpp/RYOkARIJKFIRSZrxuwRqS3JN2MDTN6rvBx\n7lEcajKzeiUdzJjMWtoUabqulm0IyNHfrBa1xdWtk7uM5G68AfzPdqV8R4OP\nnK/Dj/4BAFX01Ey3F9BgZ2gZupTd3HAX7ICkch0z6zB0jeVN7PTntbR5+0J/\naLeHp13Ci6JNfcnfDAyNYnWEE7W+Tb7GlzKiS3HNlKQL+lI7+ncUI/sCxKVZ\nU4KW/NDHH8mJR6LfTvoIfGRnCJzoORNx3yZokcnMqWd5vrzOOQB7Y4OoDPAg\nfJ3jaAQb4SXrRzRt7KiTBdfL9P2zlzx39IywplkScvc60Zmc5vYoRWZwCxcS\nn480hWiLdELGzwlFCQmeIU3HzM9e5Seb8+L5nBKCOjtLyQFtWDIXEb14LLZG\nCl1p\r\n=xxRA\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "a5f77792dd158603b93cb00e230ef957bc3ce55b", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "tar for node", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.2.4", "minizlib": "^1.1.0", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^11.1.4", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.2_1525114667418_0.8437672073445661", "host": "s3://npm-registry-packages"}}, "4.4.3": {"name": "tar", "version": "4.4.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "d6bd509dc7f6b5a5d2c13aa0f7d57b03269b8376", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.3.tgz", "fileCount": 24, "integrity": "sha512-LBw+tcY+/iCCTvF4i3SjqKWIgixSs/dB+Elg3BaY0MXh03D9jWclYskg3BiOkgg414NqpFI3nRgr2Qnw5jJs7Q==", "signatures": [{"sig": "MEYCIQCBeUYI0mf+3/Pgc4muhJNR8BdDP0R1mfLvH73MtH1c0gIhAOlFChlVkxNBCJBaOOXH4eiOBOz2bfj3Qu2f6wQbgfSw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBf9bCRA9TVsSAnZWagAAwg0P/2/cer3pm0gmfJ1hJPGc\npnGFP2YHgQvG9qidz4FgLKey+huYFPXLMQx5i+nthzR9JG6KCxEm+L4jwckK\nopJ36J5zTx7ao/so4wmyujuvpHUc19gKQXQhMN4/u1aHocuVp7u4T3ERWBu5\nxVqyB5SRiFLYXXuXKyb6r+AaQnXWGfg77R4/gbjBETVY1DQDl+23b3uGYAEN\nGZsX2GZize1WDjRilDv0huMAQjckVzp3yqcOmmkHMLT1DoW2dUI3B5YFlQjy\nl58cDJ87U5QaXqJ5XeMYMvccrvoiLbyIBO+IdWhkKuOF/+3pju+Fx4CeUGa/\n3C+QCq1tw/DQGq1ApAEJK4VOQ2wS+N8aemFZ/xP79IYSRaueCyp1qcT9xafp\n8aJsNwfRDbUT9Yp8PuOCEe3e8Zev4T7vH9AamSAzXvIzSgG7nKQ8G3Oj4Yer\n7pvJkDxiwtXTRixdJu4cQv6flO3/J/D2PVYtfq70rfC5psEcxtKEiQaqnv9z\nCY95/thTMWxH4z1q++S0dXfNSL1oXXrGinj3FEzQ+pEy3XSqcjpqGa5XDbdk\nchFtraTI63KVnYU182oBVl/6I+lQEO6JjIJFsXPLWCe0YASstDfErx4nRyYs\n7l3z1AnoJgvnDwMhqYGHoJn+naLyf0K+m1rvx0Ro1zsC1P3dMyfHBLg2fVzJ\nh58E\r\n=QcJM\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "2131b0f6a2f672fe17a0ca4bd2f61c831184a7b3", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.0.1", "description": "tar for node", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.3.3", "minizlib": "^1.1.0", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.3_1527119705592_0.7399063802078791", "host": "s3://npm-registry-packages"}}, "4.4.4": {"name": "tar", "version": "4.4.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "ec8409fae9f665a4355cc3b4087d0820232bb8cd", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.4.tgz", "fileCount": 24, "integrity": "sha512-mq9ixIYfNF9SK0IS/h2HKMu8Q2iaCuhDDsZhdEag/FHv8fOaYld4vN7ouMgcSSt5WKZzPs8atclTcJm36OTh4w==", "signatures": [{"sig": "MEUCIQDwYicZz/6GcjeTXmnWIYYMLXdFNFMyGPaM6tnfA/6hPwIgNWN6YykLwWX2IMfQWWvo1XYGdCs8ag3lCiJLuJLhlDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbB1/ICRA9TVsSAnZWagAAieQQAKIWLvyZqHBo+Cfz/XeR\nzhamOs/SAIw7Him/GxZEm1VcreBJtRLL5YN9Cb8u3k7BCVYDkfgum/ErGYPr\nQ/c4/9kjBrm6TiHcilN9c1I81dLctcJhn+tnxU8s4L/n5Rnktl+/eHOm7/fQ\niS8EtDuTUIgNorVbFOpWN8YBVDfVG0JziLwpaMIG0bkOO6cWevh6faNCbznQ\nRcJfukzwCV8dIrW+ZFinMD0Mf5PHmPydPudZUNgpPM+k6OT8Pmi9iqO/kIBE\nRNNgwcnMsNpQC3CmmQjzSYhmoPRJRKVM9lak0EOXkB1W+OqC8+cfO9nglG4b\n+3G8zFU/+2HGrPaQ7VtGCwvIfNQTuhHcS+gXpo8hB1MRNwDitRMtl6f7ekjQ\n98xerKA/5yPnmnL6yz2Imb+Gy9Mg5xTaEMtZrIvYvB/2XWp3UcP0AdPeuNVZ\n0uFzx46FqiEuMoPkkK7hVI6fSoc2qeJ3VCsuggsRl+hMpMVr4UZFXfcIxOnQ\nF/R49msMmby+FYhSkiWgRV+y+iUYPgk4mCuRdI00pkh8MBskG4kfpbK4fdwM\nwQZS6Ux7+OMYhBtSqqzxARlVZM2eIwUf48+IPCN6yq0Rfna7t39jo40OERFF\n9GdJMB6LM1BFM/a9IkG1q0gq9kUDie+nwxeuMPi/+guMXPW3Ge/YclGHp71/\n/0P7\r\n=qLIp\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "362bbc528c15e2d8e454699f8c3964f6b8bed560", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.0.1", "description": "tar for node", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.3.3", "minizlib": "^1.1.0", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.4_1527209926524_0.05577650681719604", "host": "s3://npm-registry-packages"}}, "4.4.5": {"name": "tar", "version": "4.4.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.5", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "bd81907a98a0f72d6b5c6586e3ee9d07f72ace09", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.5.tgz", "fileCount": 26, "integrity": "sha512-caZkmxkASHTDm/GDcc7oVe94p6ZiYiDoyivHFa3L7gwDFDscJbE9dZAkAE1cyvQF97FOc0jffpJsxAF8P+V9TQ==", "signatures": [{"sig": "MEUCIQCzu8e6OygDZLMnw+y2J+xqVjE547sksPS1TcKg70X7PgIgNvVznZorfjFSUCN/uVxdmTU4qdbcpjSHHVSYjPXUdoc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYkzRCRA9TVsSAnZWagAAVyMP/0tWVxTL6rwmGS7s9p5m\nLuyW7Jj6AL/WZFgm5xn/nV0p58W6v7s6YabE1ZfRQ6nbTF7EqgpL0KSA8591\n4wmodV9Q7UIgHkIHfUDC6CBt8m9buqpadXqVChpEoEQUU19mF1+JNxOJsKfs\nb8KasfOqVkCmJYZR70LnFsVQaBLwBssYDQnrJOCB1y34QRLusLDp8dbQMFO7\nNBoTmXx/L8P96urf1Oj057pE3wPkmW7iJkWEEcW3Nho21BydFCNBIbi5fy/+\nUP12NYl56c5N9f8XT9520JNv9oJJeEAi15O3t+dWZkoECgO0wutc4UKTebgQ\nQbznJ28xc7M5ZP7kZR2E8u8rDcKCDsiRCEOnPFHLZFUIyPOcXTux7dgYHwLA\nxoIlia/EvSu1i2dLHWYDUHHFXcBO7MrranvppUbDJk8xteeBfM8GFicuZfAD\necdauccrXmtlY/8azFW1s6v6/SB2etgct2GzpoCj+qsg/kBezlShDIgqr0hP\njXomF+PGjEkMTTGc8a0SsU68CXmRMRZ3EtdaHdTTskhCqY2UgUHksHOtZz2+\nDW7Ds6G16IlnYQ/4jg9aKRFFXpcwVz1waIZImIeo2Ltl8/1EzDflLNTGNRvR\nqk3fmSUjQCjbjWb5FMNhQeQoZpfOFPV8JKs+r2dHUleZ0dIgAZ8xgzpJVFUY\n4bV6\r\n=dsOV\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "b40376a9894bf12265e7a0f76a28dabf823bd771", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "tar for node", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.3.3", "minizlib": "^1.1.0", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.5_1533168848329_0.24223311305749817", "host": "s3://npm-registry-packages"}}, "4.4.6": {"name": "tar", "version": "4.4.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.6", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "63110f09c00b4e60ac8bcfe1bf3c8660235fbc9b", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.6.tgz", "fileCount": 24, "integrity": "sha512-tMkTnh9EdzxyfW+6GK6fCahagXsnYk6kE6S9Gr9pjVdys769+laCTbodXDhPAjzVtEBazRgP0gYqOjnk9dQzLg==", "signatures": [{"sig": "MEYCIQC2IDJdIlzFER2HWz8sDBggc5ydGf+LU2K0uXHBzzVyfAIhAJ0+W3a1WSw1zuyLpSpNEsXOv3JRSV4yrBRLYGCT70xJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYk0YCRA9TVsSAnZWagAAzzoP+QH5cG9HT93D4RH9oozd\n/jQoZgsb8xGu7p8xL0hfQXchZMn8GFdZlOypdQDVjrgaCDYApFLcDNtH+vQQ\nB0MgrKB8lrpZcAUdiCzj/8eh+23/mJxrENf/4TmYx9w+k7KxzmxgLLjJwH6K\nwh/wrt1rMzPmWLht6c6HtUQJC5kQYAWC+EpA+wcICDBou/28JvVWXW1Lumq+\noj+DG7V8McdEachZT5tH3zcSACVXkg090PA33xCfcz0RElOtX3XXP2Kf8x4I\neqMWRX0kLOuPMoRV90C28g6C/dIaYZz7houon0SMw8uUSnas9ry0aONd0v/Q\nOHahwxkyjejYlaAuOwL5Q/RSekT5oaUEMhXKR8XVA8AIjjy2DdqEO++tjh/b\nhWTIhb+Qyz6mrkJR57KoNe+7adZZ06EkfcSnihCnZnYwJsnlhXLcAZuLnm+s\nHGGyliOv/+Ajr41uw2GgylljIuPYCS/gDBqmPV1SygjdraeXzsIm8ppsSEA7\nR031BWB6XgidbmTsGtxEfYxPfFG2RO2hulV6xeA2ZqzeWQkRqtU+nOz+6YK9\nIqqnGmgzWOTBimm/QAbcDbB8YT/gNq2bs7cfnZyU9n/s+adUzyD1lStPmO7s\nMsLTILIULN0+gGQgbC94/HlA8bGoUi+HWBJ7/X7DNkCqe2xXDrILEfJE/eiC\nq5SS\r\n=y8kB\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib/"], "engines": {"node": ">=4.5"}, "gitHead": "8a34ca860e79f4c6d6b38ebd9ec44d95ed8d6df4", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "tar for node", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.3.3", "minizlib": "^1.1.0", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "chmodr": "^1.0.2", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.0", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.6_1533168919920_0.1504720663856911", "host": "s3://npm-registry-packages"}}, "4.4.7": {"name": "tar", "version": "4.4.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.7", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "14df45023ffdcd0c233befa2fc01ebb76ee39e7c", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.7.tgz", "fileCount": 24, "integrity": "sha512-mR3MzsCdN0IEWjZRuF/J9gaWHnTwOvzjqPTcvi1xXgfKTDQRp39gRETPQEfPByAdEOGmZfx1HrRsn8estaEvtA==", "signatures": [{"sig": "MEUCIQDtVz6kyV7QffUmvHQLfY1kA3nA85suTe+g478/f+BEKwIgI7romUiiayFU/Fc97nBjENICeTn5gfMAjbruNg4gOcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4LKeCRA9TVsSAnZWagAAjA4P/0Mf2u9SL2viQdhcd8IH\n3/ze69kZGyWnHBgFJ9W8c1RWRJHzFOdKzF6vZqf7iqXl4nDUSVQlLU4fnmbo\njIEO+6M14Z835xdkZ9DXe6n0NAAyn53jUwR5zHhSybR1XnIK7uvAeCKPFfHp\nWTWOrom2nZq6AkjOVpApOSllLIdXrFSiIUKoEpPZ0YDRMmZRIUzmYdj88YNB\nFgCemkvJy7LWWavfDVaIYNvK1qtTBHfLSvL/1S4iG7rwFVw6Kq7FUO/0Mzpz\nqJuD0ECTbXjBy81BmXQVd7Jzbtzay5xHCkmw2ND3TNBrU2xCn+/X+MIptMPe\nZdzBmW8ylbZ/KqZcu8z8rkymUg2BJmzFojb0qJv6ur3F5nB/Be75qjdzQN5W\n/s6RtYnG0wKiplJ8mHV1bjIfZigvvGXp4pMiPiy+qzHlPZYX86bf73QkaWix\nC3fhybLxJ0ZmYf8YwUEZ3JNxZFBnG39eOv9riPS+eT8nRjfk5AQQIq7KYuwC\nkbMpuFMZISWwyb9kR8k9CNR/fKyvkP9PQS1IYtcUkjeAuH24AuHfnTI1y/va\n8f7c+A3puxxnMPlIirH9FGrBLYiLMcuWkWPdVIrxwX17HafqWZXugrCgoi5B\nvr61Tf6MERIiYXg31g7A5sSga3a0QKdcpQ2Y0GdLNDMJME3Guzz3e2TEqd/6\noeOk\r\n=M8jT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "88d6071c248042e29258f6f72c1b71721904cf22", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "tar for node", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.3.4", "minizlib": "^1.1.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "chmodr": "^1.2.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.7_1541452445523_0.7630714409712416", "host": "s3://npm-registry-packages"}}, "4.4.8": {"name": "tar", "version": "4.4.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.8", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "b19eec3fde2a96e64666df9fdb40c5ca1bc3747d", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.8.tgz", "fileCount": 24, "integrity": "sha512-LzHF64s5chPQQS0IYBn9IN5h3i98c12bo4NCO7e0sGM2llXQ3p2FGC5sdENN4cTW48O915Sh+x+EXx7XW96xYQ==", "signatures": [{"sig": "MEYCIQCbmbyPR64+WA7KKSHyLu6KUCEAh65zHkmeW3/Gxw+vvwIhAPmH5ZOKwwdTo+yeIMifnJLqTf380zYioz/8rekVChBb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5MxyCRA9TVsSAnZWagAAHuwP/34t2MLp/xdLe4+ZQlei\ne+8Q0TlskGcKO4USCXGuzym1q1GBwZ0+2TZrZIq3XdY6g4RVHthDSFHKPseR\nTJQxU2pZzslAbayIrFipEZUAbis+Gb5hVxsbmNc8LhQY65Wy62jxXQMT4s7G\ng0G4uEjcQCcpm+i/s/geM+8mt8Ub89DhQsfsFESQ+C97Kcv9b+srosyDwlHi\nJXTa7DgCQwmACAhaKXghckd3KBsEQ8407SVkfK+mbSOL4j/yn4p5yixsj/o+\nBbB5IPK7ufosQ60eeioNP9bUj57OIW3DhXg1OsgJeCKmhh5eM9W6mExNYU9M\n7PnlimMXnL49vlKkzMVVYw+O3qMq6cIZePj6cX51dkRIFmLzewXnvmruNtA3\nsxThqXklgTB9U0tcDxBuMAd/lAuyFsYXKgsc60nJQRrf16aslztPTFBBYfO8\nkLD8QeSSjru8UtIONQRaEqepEqGYCscVDer/Jr620NDBsBOXlyRwcperxxsw\nHyahRkyGUQ9MJYn42xcdVoCQQ3HF1Es0D06C7kV2rRy6zIqnZZb/bj/zys5A\ntd6ftsqw1M/JHatcH3QyWPsCs4fRGnv/0AyKR2ymAnL2+xowsWpVctqDqon1\nrxbdKQhGlXRRFfSagkSwB7TVyjiiLkeb5r8c4Wj63/+vjp3M3FsY/edwuIOW\nozRO\r\n=fJbB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "074c89b1e639485468706af3c141a68ef8826728", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text -c", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "tar for node", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.3.4", "minizlib": "^1.1.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "chmodr": "^1.2.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.8_1541721201200_0.6164252511068276", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "tar", "version": "2.2.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@2.2.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "dist": {"shasum": "0ca8848562c7299b8b446ff6a4d60cdbb23edc40", "tarball": "https://registry.npmjs.org/tar/-/tar-2.2.2.tgz", "fileCount": 88, "integrity": "sha512-FCEhQ/4rE1zYv9rYXJw/msRqsnmlje5jHP6huWeBZ704jUTy02c5AZyWujpMR1ax6mVw9NyJMfuK2CMDWVIfgA==", "signatures": [{"sig": "MEUCIAStdRg7JoBAoTQNQGtU/FZaxzChxihgY9skOrzzg8vTAiEAgR//sgOCNmOS18ssR9p2bFrkP3LgC6JnRss+6v+4f9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1304644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc22DnCRA9TVsSAnZWagAA4lQP/2kDncCwyD8KcVYRkVN8\npbvFvEqscpjN2kUNL/8G3ik0IsyXT97CCXGKKFFeZylEsFFFJuFNT0gcf0EO\nx990mlhSQ02b53uw4rJCEKa8Cqnwps7Qb2sx4D3lr8Pw+hghLU8LySls45dS\nJRmmrnlX0aFxGr5pAuJR4V5OHyEwXIN9q3VJ3WgOgwVOMOKqrP37r5xSeiDA\nbDclGxV5HPyESyMH4iioZtBGqrsRyhFB8R/+wL1CIzDaDHkuF4SRCNt7QE/l\n8KSdIwt5OGndRwoM9X2YO+9Sxjmfq/3GxdBGWRi9fTURTZ4Sj7fnn1TiV+8B\nW1z8OT7dKyaleE9fAO4iM2u1YS9D6XNriI4bCkUEAsgCPuIVFxDCutuPpBd2\noIsD59m1v9FTgyWx7tF8Vkh3hAD17TGiwA+mCJCna3+3RcyyhOPeCo0/nqAG\nfMcFeXThMrnM16UARt7zfMXdpil2oP/9SlRb43pFe2YzTuZZafhuCaytTIRl\noOKQubCTtWBUueSmBSoJrZQKxJCC4JTMa2zmz5vFcicxUXrQWVKEKnnbexW8\nZl+vf8RItEsytT6GiNDYZOFPvD3n8qxPxTWDwF9IpjnQYCGgBKLf8qhvc/6i\nsLPHKdCDf4aB+r8PjfMCfvDswNnJbMHrvH6/MkcNpZCj0nlunEgMrVpWXmd8\nIXVU\r\n=+U+B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "tar.js", "gitHead": "523c5c7fef48b10811fccd12b42803c61b6aead8", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "tar for node", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"fstream": "^1.0.12", "inherits": "2", "block-stream": "*"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "0.x", "mkdirp": "^0.5.0", "rimraf": "1.x", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_2.2.2_1557881062489_0.30195366078850117", "host": "s3://npm-registry-packages"}}, "4.4.9": {"name": "tar", "version": "4.4.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.9", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "058fbb152f6fc45733e84585a40c39e59302e1b3", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.9.tgz", "fileCount": 24, "integrity": "sha512-xisFa7Q2i3HOgfn+nmnWLGHD6Tm23hxjkx6wwGmgxkJFr6wxwXnJOdJYcZjL453PSdF0+bemO03+flAzkIdLBQ==", "signatures": [{"sig": "MEQCIHUBKaH/t4OAMXlLSTFWwXQn3FNbuoaW01TDUMPZlfuHAiBgoC7r01KMugI/3vsLVRWeCXgG8gh/Z/eBdhEQGLP5oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8gOoCRA9TVsSAnZWagAAWJAP/06XXIyh/BjaspGTelLl\nucUP1YE+eW/Rwxs04//nwjEHCrYgUfqIZWNaqiO4p0dkKOCXqvKYcrFdsGfp\ni7S6dhjLFGHO++4U1wSnEHShzY4gS5GQOSFQMvWelKd4/GH5e6tBiXpIw+Z4\noxbNO9rgRuPtrvKk7sGw+Yrei4RWIyjYtJe4yPQlSVzSe8VFzlXHPiOztwuH\nG3UB+NKYYVuv9z1m8GibmlA6qaX0UVGJXkuOC7YTNZsPp6wLlqGi0gUsE1qn\nACfLAbriXHvMDZuJMPYTrENEzkBUJ+gXw8+MMnfaRRey/G5ui4INXJMsaugq\nE2EpltqmdUTQB+2wBVJu2gl57a01Neshs1AbuJIf7x9G6ymmu+A+tANnLhas\nClB5jeWEzAogwaX7RYMy1keNAfnb7wHxSGPkSR2wobFghtbD3lZdpyD09VEP\nxzb2H8J1m6xHvr7ZHtfeEQJwk6pnIvHRsCsQ5cXsQFQ6p4ZcvHeIw/J/JJeQ\nIcLKGgiPnK9wDBFMZSINBynDipbb+C2IcDsckMdcxMpsyLSm1O0vK7KQGED1\nbrDZGGssyf6VdIV/5rbKmrDrE3ixKxwW4DnCcDd7Sh+GXfqjPVDq2mxpODKl\nVrQVzh3RMsFokw+vUJO2RuPpIHblu6aXRkUj6/WZXkFvF1xbiPbsEi1GnAYn\nxkMm\r\n=bapo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "8f85cab6b71483397fabac1b2aaa2276e3e37a11", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "tar for node", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.3.5", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.2.0", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.9_1559364519667_0.4525932297857702", "host": "s3://npm-registry-packages"}}, "4.4.10": {"name": "tar", "version": "4.4.10", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.10", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "soldair", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "946b2810b9a5e0b26140cf78bea6b0b0d689eba1", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.10.tgz", "fileCount": 25, "integrity": "sha512-g2SVs5QIxvo6OLp0GudTqEf05maawKUxXru104iaayWA09551tFCTI8f1Asb4lPfkBr91k07iL4c11XO3/b0tA==", "signatures": [{"sig": "MEUCIEkuMl0D+mYxXDsAdNnsnMKyUmPz5XUd0NpgIvDVYbERAiEAl91kOgxpIMzujM5x2PVu2o24LM4ERU5MKVuJ/N43D18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9sylCRA9TVsSAnZWagAAIVgQAJV7ZvUYuZCAcDuD3AOz\nLrBRgjymBGTDYEtXAjKC42CH07TzAUjrKYSJb6hixXynzVH/AwJYHTqXIED/\nsO4EODSy4TxFf+S6hJ8Vu21ipWIstVKCboSdpi3g59gZmc4VGzoPYbAvyq6C\nS2EG1CMoA9/Wke9p7nKhlLnm0W3Cw9BYI2TsCsFHVR9P4J6j6kVjV9bQCiK1\nqb9Ppvg4BNMglFZhQlOJZGIxz8dvPQPsuPN8G4N54eDLYRMTK3jfYd8ApUW0\nkoFq/5q0EnQ6/ghTzNDVsk8lgh1ilW++JSQa3bcN59ytYrh01C/WQUG0uixn\njT7WKSIts1eE5KxznR8110y6Hg+MmM83CcKeYX4GZSmy4//h8Bygqpwf7aYX\nCmAAsY6fb28vpjxEdziyuaZksA6e4w4UQ1R5sASgWDCEZ3eiDycOgJ/dlDgV\nJlaBdZvR1MJunx9HRmV6Sy8ZteiCFESRVfkh86EQd4eRbT4KDc5K21oFxFOJ\nszSKL6o7w1sm5UF5K8w/9txT8trhiK2iw4g09kgNn0fJ2OQ+wt2L5OnzT1TU\nXtu729OS5uSCkUU0baDgJDsaP+8JppmCF/Zbqf3lgbv8dbX90Bu0L9v79joE\ngpSbW2CtDswIWkESqKUHb9zZZlJp/kJS668hyBxcGlJZaogsSus2mezMc/uU\nkm+J\r\n=Yn1k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "84ab44d8201e04139f3635685ce7ea2c2e20710a", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "tar for node", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.3.5", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.2.0", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.10_1559678116260_0.05459998121393972", "host": "s3://npm-registry-packages"}}, "4.4.11": {"name": "tar", "version": "4.4.11", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.11", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "7ac09801445a3cf74445ed27499136b5240ffb73", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.11.tgz", "fileCount": 24, "integrity": "sha512-iI4zh3ktLJKaDNZKZc+fUONiQrSn9HkCFzamtb7k8FFmVilHVob7QsLX/VySAW8lAviMzMbFw4QtFb4errwgYA==", "signatures": [{"sig": "MEQCICaqeK8hIS7jXb3qKUZDmBc8PdADhtogCsHNlslaQITBAiB3cnmIHBsAp1R87n+Gq/n54EWhJ8brRziti6UoabBMTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgRswCRA9TVsSAnZWagAA544P/RGMWN5vwq3w00Bf7xPI\nXx59KsHvK3K9we7HG7x5IMYWCsWy932xAK83vrQsrxjjmmnLTASsInQmZlfO\nTBPHuVzZuGSpTleyucABuuUCMDYDvurxVzCCHtfi0aND6bm02IX+ZwTqLpP1\nR9YGIGdGhCetHJlKxvPLDY9I23pGzlDiYHDN+Cr1dVSVYf8QStBUisqmvRsC\nrv+EabiFrZOGuV6wthC5qwEND5z2khToG7a4W2y3v4bFrlDQpnugSwkWO2m/\nwxDQTYhMlUZWjyMfscEgXCOORiPSJd+8l+/oRjZPctT9zwvt+H89ZGLtRiBw\nKngWc5WtbxXbkV6KhW1Y/LXvp+qrDb+umymDE1SmhBN5QvDCVEk6/0/oqFjl\nqzSKKslePPzh7ScCd09AdalN20S3ev6LeYePNOsCFiEPa50NR2RDCe+l6xzw\nFiUWRgwBJqhHYdrU+Zx3BIZ4LVai9ApFdup+aW79ux7NWUzEWT4+BYe1tMev\nQY5Icnhg7VENvAxPA8XAKm5eNoMJDlsQUFKQlygHLCaxRaIGyZC64CxE/SgE\nn3boXoP/bCDiA3a5T3pqqvLjfvNTH2KvCVJCmA6/obfkrkaafLtDty4dhcqX\nxTHDsLaVM6X8D5aTnIwEE6aZGcT15TZ3m2nFkxY5Sr0YedvBVqLznLKG9WYe\nPXaj\r\n=n3HB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "9232b3d7da934c142e3d0ab97ef35ec0ba3917fc", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.6.4", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.2.0", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.11_1568742191270_0.9961642803459372", "host": "s3://npm-registry-packages"}}, "4.4.12": {"name": "tar", "version": "4.4.12", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.12", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "6a1275a870a782f92828e24d28fa6aa253193af7", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.12.tgz", "fileCount": 24, "integrity": "sha512-4GwpJwdSjIHlUrWd/1yJrl63UqcqjJyVglgIwn4gcG+Lrp9TXpZ1ZRrGLIRBNqLTUvz6yoPJrX4B/MISxY/Ukg==", "signatures": [{"sig": "MEUCIQDBRcEAKTlKCGZ3KCvIS2Pf9oLvB9559Kv1Q5kFD8t7BgIgRtV3Hs931QsuMmjrAxPeh/eebplOPmbj7IMUbf4TJNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdikMwCRA9TVsSAnZWagAAe9gP/1yHeO5MtNHhwP9OCljj\n7+ytEe7YbH1FYG0WwNHvFp3/K+goAz3i6DOf603bGRcDD3qvTVFd+z9tUjJe\npJXdwDIYBIxaeWdmmp/rcQNv6GAVBkmOus/zXK85rbfy8uPCtFV5EMJKsPpv\nks953vzNW3S7Vib11lFzyMMFlEiAmyv7UqQ36ztmSubhiPiN8kIg5O/3kwnY\nxbwsDbM2BWmguflSM8SbNUNP9WinHVWPRFw+xHAycjMgy2M3fES700A/A0YZ\n/LC3Iq8f3WSL9MAwdGSAqySED9q05ianmiYrRTg0O1ci81FvOcRxKMNoO69T\nW+kMSVBeGU3CbeJpROvJgiN5sh+aBco3CY5biqKjUbqvD4O50q1HIWXPMCS/\n9BZhLolAEEyRGhWQvrnMZ/IDVTmxelp5Wx3u6EhR2f2m6piObphSsCAU0Vtt\npoGxrQYeJO9tFvykfpkri2ICt8UHaQ6O07m0IwIJOk3xwsic+6Etf8s9UAkv\n+MySTsmp7tkw08DAHb/ZzlqfaicWbIon/IRX0BQLREZtQx/Ubu8X+Uwozv08\nl1eQb8cobhuw1OJJFQAVOMCdFCmyzkJ1aAvr2EhbutAt8Cd4AU3sNPD/SiPM\n/tE47rWZC5ebPL0VZ+evdBFn1f/Z40YtbIwHHYtc4JbnG+8cjYWO7Vdpoalf\noZXT\r\n=kGFG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "dbd6f52ba9cdfbce2a28d8cd28a016bc3435946a", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.8.6", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.5", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.12_1569342256046_0.8140056451128697", "host": "s3://npm-registry-packages"}}, "4.4.13": {"name": "tar", "version": "4.4.13", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.13", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "43b364bc52888d555298637b10d60790254ab525", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.13.tgz", "fileCount": 24, "integrity": "sha512-w2VwSrBoHa5BsSyH+KxEqeQBAllHhccyMFVHtGtdMpF4W7IRWfZjFiQceJPChOeTsSDVUpER2T8FA93pr0L+QA==", "signatures": [{"sig": "MEYCIQDaYuCbvx701ihpled+AWsiGwhH8tKWz7T4dxfNd7Tc4QIhAIPfhXA1Pggqfk0I7IVysWyfRvcjooLUs1DwNn5LRmOV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdivODCRA9TVsSAnZWagAAKF0QAIz6zD9/wKs8V0YXQ4zM\nQWqU4k26ZZgEtnvAFW9ovgJVGnHQS9v2QhuJtHp4ap44I4fxKk2+1SeT1mhT\nUxnD5P08WBbAH0OrPorMP1r6r7zkAzQVNWmLUATdAGjK+zoWCxjd+6+7uItk\ni6sLpsQzXFFRttzl4Z9qDhrn1HxbBG2WGLZ9dhcTBnsEfFifPhBVIJhsU9uv\nsXdZZHNK14A52ieCADwQdqI+u1DIawM82G3e2AXEFN4i/2Km3UGrlRZ+Q76Z\nDp+iQN3o/amWTW1wddup0JeBEjGCjng7jD+S6BkiGWtTKWpeG3pSRv6g1MN4\n7YvAYXjOv2JKJ9MNJ5qIIdC0FeHuDMFfXTparMczr1sIAtvjue++drwp9dCF\n8XDDP7TjxkefbBvbIl1tF35MShC5+dsJwOmlyVcWSMdIInZmV5KGRdMdcFV2\nOT5hhdTAxSpcSdp4lJHYXhJUvP1ScRD/08RPfWmB/pr3hPEblv6M2qxpDfca\nhwhaV3srdvnicCqhju3KMzGBBJIikR/lAiI67QVVNaC3D68tHYOVUmFSKg0g\nGJpC7fiD31u1HTT9ndcfxKgUJ1OuRs5T35BtglvKFcpEd2FF4mANsegD4KwX\nGm/ID0I/8+K6ynTRd9+gpcUK2QGR+IH/IuiLvDJ/EZv7W/hirJ9o2r5RTthI\nQwxS\r\n=1EKm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "65edb39114ad5956c06f8d7893365e942042ede1", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.8.6", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.5", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.13_1569387394602_0.5681974056976136", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "tar", "version": "5.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "a1a5f7da2b56f1df50b261f3fc3be8d13d6c1a9d", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.0.tgz", "fileCount": 24, "integrity": "sha512-FGgZUEOBsNqREFu/bfuEa70xnOuM6iHAxn8xpEWEHjTrDX5Xgf6boLgXcCV2tXfymiXxu0YZLLSuTumSMk3VAg==", "signatures": [{"sig": "MEUCIQCNUCfxjSRBDQDW+7u91DQPSlsjlagBJHDahFJ+OFHBPQIgJeEajjyNF5hCFvbA2ZaB34ZU+GRNT6SdXNapeJd+Rqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiwJpCRA9TVsSAnZWagAADHAQAKIg+3bZTbcZKYo7P+OB\nX7TQAf3XuSXIgy5yWIYgmSz4WbyyMlX2kNKV5x1t/OhYZ/I0tA+V4BGvIfmy\ndVUVQnVF8C2eFHOhaQjV8GDOSdTWkC/fTINSsSfrFwIsDIOOWJjKMtZgTWwh\nEwrUr7S+d2THih7GPv+lOgGk4vv1iQLE/SxfjXK+dkFTHoX0R/OCx9nKzV4f\nQ1KS5n4jbqHNQmDD6XzLUV1iywsCXGDaX5g4aBF1nWrLt4z9iJdN4gl5wz0T\nrjt93BuEqdjG+pl9PQV9zXeLDpazeHJpTpCV3HBVejPnH/1WvLVYLH2Eug0+\nCf0OVF7r87yjyIM5CfFb1mv5nNJZWAeAS7LaIE1SX2EAUUTZxTLdkYXNDPaE\nvsOkalSxrm0gbeikF8DhCe3nJReoIuNuqmPzJE9+iyszN2cPEiHLRfzicuvX\nLYdZ/grlMUhIGaI1qN+k+IDBSlROfqzWjPOlE2oQ/yphHw3fLw8F/jWzFYvG\nmPpfslkdMeQt1DK9rN/aRB9AtuZupMDYNEjZ8/CBehdxGxrEgcjvJAYpjFSN\npkAfAxhe/Aixgu1ovExD0IsO4aQhnPha34xC52RLqF+iRDSn7iOFo1dgBSjj\n51xJ28fosh+jfbCbh4mTRFXB9o2rbkZ4tklTsSG/kmNLCwkOvvjfMWUrzMWk\nhX7i\r\n=EQSD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "7bc7c20521154f26529b9ef8ae74c62a7fea0433", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.9.0", "minizlib": "^1.2.2", "fs-minipass": "^1.2.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.6.5", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.0_1569391208530_0.8943554666506541", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "tar", "version": "5.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "88d8cdc9e05e157bac6d0672a34c77b127684e5a", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.1.tgz", "fileCount": 25, "integrity": "sha512-7YIaS5rgI5NEWrBx7HJmrdZnBxAoQP7ykgBDFX3YLPGVyCzQYxXQtZRtgmJfzcpLLDi5zbDxnDPZ9G0cs3/fKA==", "signatures": [{"sig": "MEUCIQC8Fh4N560kRV08MOnqpG3Xh4bBxdZw4LgWhmNdLhVE0QIgKZ+U3Bzl9bC4soCpwEn8n4cWgRjBvHu+IJAKvmK/5/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjAlsCRA9TVsSAnZWagAA9n0P/Aie/DBcDii6BfHJA+SE\nv3n1/KRYFBzIVngdjNbzMfxMmsXZIx1bdJvJ8jKyT2NL0qPx2ny+8kPE/nt8\nLEKzRoxDZ2iV6A/WCjXM0Ohe8ORO8zZVqDxBqlaRbq1qjHWGVNt/kB88WSJ+\njuh5lCInmuwDDdC0saK7sHmc2tbtCyQt7GEumFuMFHaj2bibaZR7go6EqzNa\nvXlyJhhV/hhFf9YfO/Qgx+4Mr9L17A5ua6BlqjhL07pEdkNZsPqJw/Hb/toX\nGVh3s+rqae5p/sl1XPil1PcN/1uFhZWFFIUr6Rv/AvcG35IH/yEi30+XfJm+\nECF96MimBTp9dSZrHx3YOZUcN85hhVR6tOzlGnbL0QiGav5khDivuoM+3Nfv\nOgk2xn1hd8xtE4skZINPaL6jjqlPFo4iUMzfOPmTtogeADvS8xbULRfhP/Xm\nAimO4VvJSJd/ptjio12nI1W1jrwLTupaCv/Zp5UWpmHzHVQOlXVPX6UcEYoZ\n5wvZTEqv6rLfVkXz9fksMI5h/dW+HHtg5zPQy1Qzo5kT+PgahytsKSLW4Z5d\nZJLtfAeSbTUCRSsgFgsGmaSIhhyOJyx5BXPx47FaFsIEIzx7cSbldjVNRHLx\nCkTTceHkmDz/hBBDgYmU1h1D4V/aJH4NlxwR3sAQh6+CA9kLwGc+bP/ohFB1\nwuqr\r\n=S3eU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "14dc6f48af4c5476d7613761adc7100c2f8d2a2d", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.9.0", "minizlib": "^1.2.2", "fs-minipass": "^1.2.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.6.5", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.1_1569458539557_0.3475226537774334", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "tar", "version": "5.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "d75a50cf8a37539f8c1b2077bef5c991920d45d2", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.2.tgz", "fileCount": 25, "integrity": "sha512-s6QnhHIoyEPImp1Y4Tq3UOhPMT6/4kgHwzeZc9fbgq0/+s6RAzezIT43Meepn5RdUFpxdzQkd5x2PkcFdVIEPw==", "signatures": [{"sig": "MEUCIQCCPG9M/Y+Z79Pgm+y5n6q4hhlDmk9o6a7i3H/d7tD1RAIgWHY8aFIwS4L7vOwteKTbkjOwKoV8kf2vISQ9+e1zORw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkm6ECRA9TVsSAnZWagAA3g8P/is48bzNKlC+VdCUqBRE\n0mcVGoPcFOUHJHwdM9WNzpFA4/Y9Sr2CRoFbfn+lqGRB61f2pRHKFaYJppLJ\n/avA/sXRVKFYrNwhQJH5H9cuntNKpDeFK+q5X1xkenPpH1wSTFTz3v1D56qs\n0zzP2ZimON0OyvDrR8Um9T3jRtyonKOyUXpMnwzJuhvvPWW6H9sn24L8KW70\ndY0e2mundPng896bazeXQp3avz5lpy1r3J4xGc7+FwbCSW15qzAwQxM2D/++\n7Mo36MfrJiEeUtC7Rrg/9iTAEOhkUGah5yEs11dHcKFMjozJTknQFBq9faJG\neGF4tgxzvqPdAELMKRgeQNbibTZyKMthZrcwunZBaqBaxlYFGCTjF2A44FP9\nmp5Mkleu7+tQrLNEGRdi8kcK6rk3zfs27m0O2A2it590rlc6y1H06JTtwYsA\n5LzA97wVW3OT8zXKo5j+gkEgCtL2Y1eB2pT3fjtcvYNzzJUWcSG2UqgUIOqV\nvlkAOBN4rseJBPYZDKsYK9rUiNpEwGxPoOFKu53bXgZGmd2NQ7iEo2XjV4fe\nqhYbokHwF0BPaRImIB8yE2aac+mQ9aszPSXxV3ibocEN22rQwbSxjJRWLMtD\nET2RbY5pAvaWim8xtb5Q+nSCayUBrUYJ7k/EzE/61TBoJRcWk/Uy/kgpu0VE\nptXf\r\n=H7JC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "a2f8899feb46830cf71e4876eb88ab56d288d61e", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.0.0", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.2_1569877635941_0.939547983387822", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "tar", "version": "5.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "b000641deee390eb687b06ad5df6430286c4d6d2", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.4.tgz", "fileCount": 25, "integrity": "sha512-o+97kfIToDuZV9pKZS2kLyb7nH92AWEj2DkOAvtcqmla13E4666B5eI5j6/1pLvAizbLFIhX5wsiFPKYM7tvBQ==", "signatures": [{"sig": "MEUCIQCyRCIAcsXx5pVMBaMK64slr3I8exsmfVXBc2TYXj0yEwIgT6AQp7UBwNJtbEAgoBPa9EpWEcwOXGrucT1uMCvXGJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdl/roCRA9TVsSAnZWagAAgvAQAKP6HI6Knl7bj2wzvaFB\nOG3hbvwJxumYz27EXTFCciNisrYTll2MhDKuV4PcWpTSpdvgmDrqvBQCHKUB\n2XXBFmwEk4GeMZNus9hjZ7t7VMD+J7JlCgaBGH3vkwuhdsQxKt3Put8YC2Pv\ndRw9+Y3r3bAP8/XZDdVNILYrBz36aSjOv9JeTMvHNv8p9cGxwaRPVOybODrh\n6rKgpsdn4et2l87f1AyFybzB8CvrXe+k8mYKdOwg+5YJE+9RMoUFIkwcpRJr\nXwHVBniDWzwPdyRxIcbkOQnOMdBHWI/zkplhU1gGmN+GBEM3MEgWguQyT11o\nSeWRCoP1/tGxmRqi/Y5VKSs0nbIdJAYb3yiw6MvQTuaZWZlqgCwjzifL+Hb2\njGcwHFxNiuhcF31zHbmJVUcO1Dp+HamuxsXrDGuxa+1nIC0eGvbKUnu6caut\nLSWq1lnJuLHMNAtgbQfEf8OXu0rMDrX+SiDR1eGz/1Aekcjem3HJYsA+dns8\nueDIw2Gq445N0LnBOSwn+vm95jMGi66yuwFc/7zmLUYHEfMBzkp3O3eb/MSB\nwXW2lu6FJeaXcR51ypiIcr++ADeuxOmoR6YKOva4mVgxwFq6hXczh4evlSOo\nQaXLn97Kpr5nLKpCcKXsr/gx41V86IPPISxuRn7N4SLLgFqXOzk82I/YkGVW\neZOb\r\n=dok0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "58668c6490e840e59b8d45d62299e6ba14ec2d00", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "tar for node", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.0.0", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.4_1570241255866_0.8184306181475431", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "tar", "version": "5.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.5", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "03fcdb7105bc8ea3ce6c86642b9c942495b04f93", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.5.tgz", "fileCount": 25, "integrity": "sha512-MNIgJddrV2TkuwChwcSNds/5E9VijOiw7kAc1y5hTNJoLDSuIyid2QtLYiCYNnICebpuvjhPQZsXwUL0O3l7OQ==", "signatures": [{"sig": "MEUCIQDTmMT9M71dKHQcXUPfh7FjH5uLaw/1K7Bg2/7KcT/kbgIgWFfZ+VHGCugNxLYWEo0dTM3xHO8VdYHAyZm3z0Gb4rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmD5ECRA9TVsSAnZWagAAZLgQAKPD84pf5r/v/5PHBPEb\nJnWMtLbn0mCT3C6svr+aWea+/xGNxuzTluGkU6ZDjNMbf9S2eQWKItgvlL90\ntPUd7nlLjxIjBWwyBxMyeOTkuJi1oBK47RWiFTk0bLbz8DVqu2t50kPv9Sji\nAwT78LSxAgYQyF5J+EF+qEGk3HnqkWLIkSmHJLZF92U4IRaMbFd5ccr11aWi\n0nLh1wb0NozJpktjThXG+wJxoCC7WfJ8kffwZuf5FHrouucgK7kDMav7d3kn\n5nequOYjyaHWpbzKdK24ZRxPHPKPlUqps5tvaGlDQqw+I2c3j8yLe6e4Qa1l\nzF32sHGXO558MeWSzqQZv3TAZfcYZMXAaIRyi6uAAGbya/2Ixjk3C0lQ2kfW\nXlru15ykUWqQ06yEQ6ED67tGb+xvTa6w/2T6SBkZWMQT1MiaVMVpSgr1vd5J\nS+LC2fm9u0ZGgPkSkvFE9rkZHvGDpU6CIjmBoUqp5ewgytRAzK5Yaptd9r20\n8HCOyHBkYlPoRGn7aHTo7sGx9EgQJvnR/b+Leg/Tzgl8RzZpSB+aQC+k0c0h\n02ONu5sZYwl+YOZVNFUsyU4+kbFPw5p0O2F6ftW5RtMGqBVhszYYFq5Jk283\n8X0wbShNPZLApJkPtK69jYydLS47rB8QNN1mHYZ8375yNsNzQgVN+aYD3dae\nM8fo\r\n=5lUP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "114efef625f3fe82f7afec838b50982867619130", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "tar for node", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.5_1570258499174_0.07767788159909328", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "tar", "version": "6.0.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "7bc58492f2bdaa068ea3189eae335ce60329eea3", "tarball": "https://registry.npmjs.org/tar/-/tar-6.0.0.tgz", "fileCount": 25, "integrity": "sha512-yD9H/S7GLhqkObYQneUzN+1GXaCIXbdMwTyo4Cfj0raQRbhdpXIX3WUcTcvheYf7hx/APsbpO7L4cUDGRXSOEA==", "signatures": [{"sig": "MEUCICPy0Y4oPyqsP8wQm/V0vUqsME6FHY6uKy/ShKrIx8SOAiEA8xx2ZypzLPGe3+7iLpzfM8i/iz14Njr/8RerTPERdYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL5JsCRA9TVsSAnZWagAAI1AP/1+Q5Lcurkbtw6jOBsFV\nTOcbokNhUIwnc5wVjoxWCFRWWauILq8zaH8t8KZWi1ME3XHsYWEqF61n4jCW\nPUFn6stKDNj2QIRsgTruUpSSctcSPIQZrmomU6bCwooZAfsD3+7Jow9hnVid\n+jTIml7olX6kF8g801EMUhzkaN5o0GdBbxAZwsVScCDdobr3oBt6RfwjvuI4\nDTPJGhj/hh4H9UCqM2ww+50aYuzqfc9zgtTInvRjVQbRRAAbFcc9awWN5oDq\n1QdUdPTEx7j56gmY2o3u3kKvhuu6Mp6MamyHCN9Q8hKUS9/5g/EVtQPxAR2W\ngQG9ycrhKGcs1kevPb+sncT998XzlE3++ab0NZorYz7pF/Tx3hvtUlpvVOwI\n+x7xnBtCnYm8sOAjvyu7WKx1l3rkTqWx1su18XjJisqcLQ4Wybgt4Ei2VWFx\nEdVNhjtY1wy4K+sR0Xs9yWy7oxcxJ+wFadFaLO9h0X/3BPfzUB1PtmrvZbY8\nwys/er9GIHwjtnDKy2nzFJZHNXTNOFJmml4QfcSy+UrD3RpifOQeh+xKAesm\npfr2UT0vMix+PW+UqWRbjEdUxkvINIlyPjKWMXvo7RCbX8MSJ7ALbB5JMVCY\nq8XQ1EL9oQVRssrJusQGjP4Syt40+LkGBbKMBdNG0zIyRbMaUG3ozMjcbW6p\nHACu\r\n=Qa9n\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "a069020b2af38f3fddc57e49a870c27beb363fa0", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "tar for node", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.0.0_1580175979757_0.34532456490896735", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "tar", "version": "6.0.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "7b3bd6c313cb6e0153770108f8d70ac298607efa", "tarball": "https://registry.npmjs.org/tar/-/tar-6.0.1.tgz", "fileCount": 26, "integrity": "sha512-bKhKrrz2FJJj5s7wynxy/fyxpE0CmCjmOQ1KV4KkgXFWOgoIT/NbTMnB1n+LFNrNk0SSBVGGxcK5AGsyC+pW5Q==", "signatures": [{"sig": "MEYCIQCjfF9phQCLr1YhhCIbgg01OlJZLUehxT7inVeSXE6BfgIhAOZUiGPuNUdgiBEDh5PivikOGbhkIhzZaO+8O4OYDI+h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMQwiCRA9TVsSAnZWagAA9OEP/1CCMWQkpfGuSCYIMAHJ\nQhJrvkIOQEpv22k++56DHElsABgThLqgiu3vB+F9B+aJ5ax71Caauw6d9MGS\nrnYNZ1e+cqSZ5502BUiYKbZN0MRg9W8GcnZSn57e4GCU9siuF9wE4nQM1gJ/\n/TKxM5/O1U9RcMXy+RciPWY74wzGAZMNptcm2qqrRxZLoJ72r03xaoLJuB6D\nmXGf0QFUBYS/CU/PaElKQ94Bb2HBp3sI9q+oL/bv8hmLI5WdVSkzZIULgGs9\ndzkm/Zygt6H0TpEgwly3VDFcCi+7eOF3cp9HZq27QIzYqGv+A6oax9H4rDVU\nEc+aO2v2muIWcGQAjyqEnGOibW7WoDfteFtl0lBzGQspDHXkmZ6OLXhfDKLJ\nKd+1cAMOiOVS0oBvVjepCemNqyvIdKeCRQJcWPLQ3A4pG3go+S1fiAUgZP7H\nZ//zjzHvrqBMhEuNfCD0im0UT9uODLiNGTEwUUKe57/tZKr3Bek6wEnIHJ4G\nHCmJCPADpQdz+0bLdNUhkkEwrQMd7L49py3ooaZkDCtqnoxQfRs17KPSsIgJ\nZBCNr3MWl+Zx6Svsaa3QbRORZrWYIleHPBe1WxztBO8uJbOlss3C1ZdsU7jN\n9N4xLKCDy3jNvFA/PxCceb72PL9nWGz9IPBHMyKcj1cAb5yCz9yMvaREr5t6\nrqD1\r\n=BKkG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "97a74cde1ee7c3d55ee296ed063630e071e38b57", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "tar for node", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.0.1_1580272674209_0.32882047995015373", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "tar", "version": "6.0.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "5df17813468a6264ff14f766886c622b84ae2f39", "tarball": "https://registry.npmjs.org/tar/-/tar-6.0.2.tgz", "fileCount": 26, "integrity": "sha512-Glo3jkRtPcvpDlAs/0+hozav78yoXKFr+c4wgw62NNMO3oo4AaJdCo21Uu7lcwr55h39W2XD1LMERc64wtbItg==", "signatures": [{"sig": "MEQCIGeOzwslv6pNhJP69cbhcR1nstPa8qRB3RxibqJC1GWiAiBHAG26EDh3wuUFbyE4tbVsBJVhXI7/gxH9gAISwv5HOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJep0h8CRA9TVsSAnZWagAA9RoP/30VAF0VSMbTVWe0s0FI\nTXsujJzd2z56sc35tIzTrQPafweeBisHKPASZ45izw7PhDBHHfWHOPCoU7Sv\nsiI2PfHpUGVHcPl/fUOdggY22XfYMiov/F7Ot72+yavE7y4mNg/KKX4Gapvv\nLmykJCGoPg6bCN7YQjWSBqVjK7oy5dT+lh50W42PURXKzlffzys5zpgYhywI\n8RC9OiJnLHOUoNRWKcuzquvzaGTqV0hCnKmUt66Q5V0NfvB4vYQkgebez7Lz\nW+sK64jTK8DfNCka277jr8AIjpFFOJaFGntcoBf6kGCa+eXi12Fflt9YISr9\nbadBavNmhkMq0lHtSR/GLyJ/b66C3ochYSYOUXTn/fylEhNw2zqg4GPdnp4K\nAo92mfXrSLS61+fa+rR5wKEe868oK3TKmuRjvs85jRr2mtmJCoXLUQYzjmMb\nEIJCMyQaFmXCFBQ+0pV/ic7fLJ0fp5YAY9Saw+LYvK79hb/I82obLp4WjGby\np9E/G+guIbjyECoIqMjRJKNRZJSj8BoFZCBooc1Z0i6jOjlEHkr1zSq7Qgru\n4TCjnwsDi/zh1vp10cpMPkm29sQMiruNZYt0Mx32cX0hUpCuEOZ77Zo+xsAL\ntvZZJefDsqgUYMB+Q/eEl3z874Z3iI7LTW3IACf42NyXvPgqISnFuRZPeaGs\nOmSZ\r\n=toDb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "0a55ace9898e8456799a0b4a8448fde670d79fa8", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "tar for node", "directories": {}, "_nodeVersion": "13.10.1", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.0.2_1588021372116_0.5634859440969344", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "tar", "version": "6.0.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.0.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "ae47d8b2e07b0ee8343145ea17f6457e36795a7d", "tarball": "https://registry.npmjs.org/tar/-/tar-6.0.3.tgz", "fileCount": 26, "integrity": "sha512-/BPytVizGJg1AVWmu5lEHMwJ8MygYCl8hGGdtR7ng+dUZDrmmSUZZhB12SmXY/yolTKwZh1YYhkXK7gjU8qRxg==", "signatures": [{"sig": "MEYCIQCX2I1TmhjOSr/Qn4feWyveEtIz39TqmLfBogB3y8OxggIhALIfly2kHQzff37b8LtGI9U1D+OXlCUYtKjJTvr9CIvY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNxC7CRA9TVsSAnZWagAAH18P/1BQv7iTN7FI6meM3vdJ\nguvSEGd4J5IhJHJKpHBXPm1zBxmadxVSu2hCthSbccwyf3AWk+P83M7BpBwf\neItCsPrQWLjJIgMxjXaJxP22yqQUgtvGfFzbtqnaejDwZs8AcNlJFPf0xlHG\nEmbQw8oPToD1vaRenFs/2DRyCUDPMgczY7wz8nfntoZoATnfGzLxJc9vWda9\nOr8vZK44M0J+KwacLF+LNye02JsIkA9jgVJZ6Z5skxQvG54hHyhg/F6uyoeD\n66eZdqHkKexI1jrC9F3mPWGhfCgvOqQ0g8eDR0jBt2vVWyPTaJWSzkiPqdFQ\nJ6FRxZISO2xUSsft3p0rCWSGPsOtVuuhjKFn2D/DXJSInxGTUk5t4RGfBWRK\nEMP2IUkYKglJb/fpjYtDqrj+qoT90DM8EIydxxvyociabfsuLh+t2zU4m2ua\n+ONJbMJnX9xDgxCMjLBeYRvVJX2PTYoCanpYyDLX/4c9TznF3tbNNVqs2Xoa\nKPmxo16pf/lFBrMHLWbdtWs+tWUSaBFfWgFDoI7YcUQe+yc+rvOWmW6I0G8J\nABJkqxJ/8FoMPJHfL1mJgXWopTW5QINbZjphJFpZ73u4N3Jeg1WfvZCai3nk\ncCBGbpv7YfjQDwIU9l1JpRARCsAwiRRyj4+GEaS6dxwefuKGyFxUI/lMVCor\niBCy\r\n=vhOi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "12b37e106eca3879cd2e07bd04928633b08cb370", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.0.0-beta.4", "description": "tar for node", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.0.3_1597444282961_0.4625030267175003", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "tar", "version": "6.0.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.0.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "ea653f822aa392df7458b82ae107444eb712be73", "tarball": "https://registry.npmjs.org/tar/-/tar-6.0.4.tgz", "fileCount": 26, "integrity": "sha512-pnbXgbXNWgC8qAxAYF8Gz5YkFwUiHq//ddUL8yjbiksGOpXcZvmfMw4JkVYkEvCb0lur/bBX3FK8jrvh6TOTgA==", "signatures": [{"sig": "MEUCIQD6QXwpdMAOJmTE2bIAsTC+t1ymEF+Tr3wt1Rn0vORwKAIgVfc8MVi9pCZjclwUMu/uOJ5eZAupO/spV+RPQvL1I1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNxFfCRA9TVsSAnZWagAABXMQAIAqKjRis+EuH+jIy7AP\nFHukLBsQrSjc0+mPbffcv+rf2dpZrzi+1olCGL2L+JZIyagrkfI7PwkUcgt8\naDJC2SvMwEgslWQPkcfmZp9HiEhFuQkZRR4ANWhiO5hqDjeE1faA/OEENU5d\nkJ/vbmH75RLZgJGeuXicGXgc/w6zN+S6JB3t7qgCTCB9khfeja7fzit6E8wY\nO5XdySTFs1+oaC7lLNWDO96ssSFuDzeBP8F22MdkKTLsmOzdjDlC2M8oZoFs\niqR0oJF7qN9/iFxRNQEcZOGdBAWrhjnCymhg9cRIBzhtm/xwdJs3v7KGiQxv\nvRVaYBshg6o+51NiX7l3Z6ma95me8m0mDcYN/mEF2hdRrCPhKbWAIQC9S+6b\nsc2Yhq/0ZsYWbFZcO/RyMsyOK8aYHVUlLX4gKfWZsf2roUHvdy7ofvxt0Ro6\n9jLrW7/kZjD2KSf2fJO6uPLfXj8ZzZs+FRvSMzbX6Hvn1KR4cCSX91nF3wJ1\ntHW0sTDX9IeUVSFH4GL8yfNCNT+6P2cMpFyEF+AMcr2KhSW0Pw2DjIcNq5GG\ndYAMPQMsZw4gxVgOhap/RLY+domB1QV+Isyo/uYxHvYEy+soJbmg39KXqbEY\n8gpCmJu9OBzgiAtJ3NW7WV4AG2wn/k1geZiCrqpaPSAqG3ZHwK/bp9CRzTul\neRqf\r\n=ZsUh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "e8fcd639697e07e6ff27d72ceaf542443c5f5434", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.0.0-beta.4", "description": "tar for node", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.0.4_1597444447017_0.9090883583113589", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "tar", "version": "6.0.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.0.5", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "bde815086e10b39f1dcd298e89d596e1535e200f", "tarball": "https://registry.npmjs.org/tar/-/tar-6.0.5.tgz", "fileCount": 26, "integrity": "sha512-0b4HOimQHj9nXNEAA7zWwMM91Zhhba3pspja6sQbgTpynOJf+bkjBnfybNYzbpLbnwXnbyB4LOREvlyXLkCHSg==", "signatures": [{"sig": "MEYCIQCOqsZ+djA5OYe5Q6H8Y89EAY/tVYlx9UGDKd66cXGOKAIhALmHv4ab17YiCJYQiMWtlNCo8gvZG1sncx81K5g5GyG2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNxO6CRA9TVsSAnZWagAAw2sP/iIDJeVpEEdXDaNmSz5M\naDIbBa0/d9dzAEkZ0VlQVyInJy4FXyK0M7FwV7T4St3Fgin7w3tbDgQsfeLj\np+3am5iP00CPrVfAUE1s8dS4mqgkjvVX8rhWJO0SsoTLPWbFDyCGkaNDZ74u\nXDiYRy7/gyBx7OdT5bdusRD+amUtYWhG5skK9TgUwZ2kOuvdqJL1L8qnlZuw\nc098UJBAUzVsoxaYjz3ZAb0U5AhWZ3/K3IOif2Cgl7qKC0aXBKFBj/jaHbhe\n3Okl9NGb+1KB3a2odTdk4MqksXwJW9slQfAAYb8de4FrFa75q5qS5zml8YKu\nn2i+a3jeVbqLW1QLPMeyY6fa8PifB5stryj25IZe5kurD0jJBMfOaFc7oMwa\niX/30smTEyKWoxBA3eTzmXIaDdalmIp+SDevEUXavCCSa9/hofbj3MnN7oYf\n9qTNaFtm7M1nz0n4B/FsEHUajGg6IwKuBfGB6QMZ4iGMVfytuUzkMRbHXrlD\nyRezTDGg/ZVDh7HIAo7beaoWZhAqetrICei9Ojy6UGbuwmsWjvyo4mS7VZ8r\n22edlgvcdvZ/ed8CLPvE6KCyztbv1eJt/AZG/5M+ya4cnin1zyjFQ0I8Vy0J\nDg6RBUdiVcCsqRpMwgY3CRI/7zBNoPcZmm3FTNQavztrceIT6DsdbMQPHTYU\n8o3u\r\n=6IVd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "7028aeb3f5bb843bf80af8f5af09c47c3d97503f", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.0.0-beta.4", "description": "tar for node", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.0.5_1597445050141_0.9055669008937857", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "tar", "version": "6.1.0", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.0", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "d1724e9bcc04b977b18d5c573b333a2207229a83", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.0.tgz", "fileCount": 26, "integrity": "sha512-DUCttfhsnLCjwoDoFcI+B2iJgYa93vBnDUATYEeRx6sntCTdN01VnqsIuTlALXla/LWooNg0yEGeB+Y8WdFxGA==", "signatures": [{"sig": "MEUCIAoVfAmMKqa1yjavU5KyJ7ZWOnolsD2aYjijJI4vabDUAiEA49M0oa0BWHmSdOZIL2oz8lBI5/gU6RD4VQHQogL0CIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf919qCRA9TVsSAnZWagAA2S8P/36mvo4hSGsbNsh+Lp/5\n1t12XLLoxdfAXss4W3qiRGyTN513H1v0ipSdc9gBVLil60KpZ72xiVKI+GbT\nQE6dMOrGvyAn4XF5tTubBbwG6QMJBVD42IMsReU+D31+mamCtbxBhK6BTB5L\nR8U1Poaf4ov0QeQQdWanq77NlrupPwY9zE6UOfFnvVKdjOptgY/1E1lBXalb\nM+0t/Gzh0A4iEjodb6kZmDbigljgmByo5YfZK4PdxO9Sc/On1TZC56HN4LPV\nX0BnRml4lA6OsvGem0Esh72BeHVOfJNQOSG6jccVI/Z0XQa1ECr05FAxqj6a\naDZ5l/sonjnXT0uahm+r9msOvsN3c99ktLZIhluV0DuukOxNXkPt8nwNASkM\n6nN3qTZIaapTrXEn8rv6Caa4blEihy5B8b/vCfSykrO3usfHh3cx3RXWDVzW\nNQ1/yIDKCeoX4rPbzMNd+8IZYvU0gvu5y3u2i3YDshwY0EF8vixhZpepGRc1\nzLHEeoeh/wUpztIoCqpFcfFASHBuUmNsCOTvYdPo4AyOLIdOpv5h92/RgaXO\nlZ5cFax7P++r20q40UzPbX48QAtobrbKtMd53n4TWZM+fv/8DMYO1akCOtyT\nMY/TvdebaOnZVfngE+ze9DQ3uvoAmTE0ud4TX8qyWZyG1oRgw/juD1G4W7y0\n6tOq\r\n=0LKp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "259e6494b73936a68dd2b279ad16e2286bdb9344", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "ruyadorno", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "tar for node", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.9.2", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.0_1610047337437_0.7810618101317366", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "tar", "version": "5.0.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "e65a7e72ac904b923924b4f5921cd1cf539dcb36", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.6.tgz", "fileCount": 26, "integrity": "sha512-LDIypi8Avr5l12YdvI9UIKMA+n8ND/E9ZHHD3Zdz9RbgaXLX+AVuu3ivsyd4enPnLLwZHUgrQzBCuyIRvHF1cw==", "signatures": [{"sig": "MEUCIBpTvCqbSnvAvXIP0hI6jzxTUpxC12kzNimPqrBImKhxAiEA+4wkHo3TtuJaZBoOhKUBEfOtsuA8tEa27nAv8rrWIQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+0ZYCRA9TVsSAnZWagAA83MP/0dHkA6zgwQzpRC5e9ud\n9mt3lV2C+QUyS1w1LR9IU5eq54+azcs79awaLXOGa83RpvAa4X9pA/5B4uDg\ns5Bfcs/VyL+7yQinUAY7HFIf193rrQbvCL+RKUuU+yXXnrgIVthiKUuWU0XV\nxnJ1766d/21BQmUJGmTlmDosXKnkRrukOjZ8LLyHYQdhKNSgDcpJ86jogmja\nGMndxP+Vj73JgVj8YoT/w7jr3oLU2RCstGwUrP6bfIjSkax/KD1Nf4nTBF1/\nhBPgg1yF+wWXBfdMjs92diBismYmmhqN073sQ874Am3tUr8IjUB42Sl8YVcd\n0YUmxNoYKUse8kBSwjeBKZocIddfOb/pMbiJ42B3mQswR1cKjHUa+GMWEJC/\nEs8jcfcUqWIQEzwaUxxpYz++bn0RqKHIDlDJyUpr5TyHu+mUdkNAcUIOOH+T\nZFB1+q2BdqHHIrbchQWkdUTraSi3Lv+FwwzMgD+Jky4difOKEeMsh6uHVpzu\nyYzFaH3TlOEVAyZBXgi3zxcreKR8AvU31xizgeexHIN9hd1THjy2oDHiYk73\nmrhCZkLnPHFDyfIKZLnUST0VShz5J3/5OkwwGWpXTVkYOIl/BSv3tciIzT1V\n0FAHUdlKRvFkp0IWLvfZ1nYO/NOHheeVi3h15fyVZYOImUeinJpN0EZQgffv\noerG\r\n=sCvr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "fea868401c2860f4c84e17ff070d2317a6f8d349", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "tar for node", "directories": {}, "_nodeVersion": "12.22.3", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "fs-minipass": "^2.0.0"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.6.9", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.6_1627080279937_0.9897126968196139", "host": "s3://npm-registry-packages"}}, "4.4.14": {"name": "tar", "version": "4.4.14", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.14", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "994901b59ce34402be3907ed7c0332a9e38c43c5", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.14.tgz", "fileCount": 25, "integrity": "sha512-ouN3XcSWYOAHmXZ+P4NEFJvqXL50To9OZBSQNNP30vBUFJFZZ0PLX15fnwupv6azfxMUfUDUr2fhYw4zGAEPcg==", "signatures": [{"sig": "MEYCIQCoz5owZDZcr5Dd84rbXcovQo8uDBmTMRWWrFwU/MyV3gIhAIlsviUBL1eUwK0hlyLgMWGV8jV4QQwnvvEgWDNiuHt3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134215, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+0aICRA9TVsSAnZWagAAWYoP/RDvHDa0ctCpVd9WC3gk\nXwN13L9zoKkMoYlwOmoFMrkvXM8mKplCabwPZOSwzQJL8GJhi7i6pR3iJX+M\njFTpvGxXdLEOpebQEznE5yg+ykLqSq61EFcRnkb8RiLbNgv3pcnFTWh75weF\nDA7p9hTLkTcD6xnBu40mlJ/FwnCRPwRWEwucF8HYCBtUDTZkhq9l/IyrG2nf\nlUiyiYXQntn/R/RCFF7ANdRkH5WHjP6FkFOg0vlU27jmFQqgErAfZpm77Gzs\nY7jMka1gsritQjglNixR2Cuuswe6QqoNqz8yPXCTcd+Oj8fT5YunDQFLWhHy\ng7aIk1uLoTp/KemHZGJGkmYm7GpOPAc0w0r+d7/54jx+ukMpxUe2TmULAzcZ\n5tVsFtaIN9Kba41G3Wigi+/BCwN6qFXccdNayRUYcP4ReZfLDiMczqqHlHm7\nzvs5co0sHZp6xj6W2clyx6JoHAWBlcwJOtTypvWvuZBnUrXeqTFr2YZdxxGI\neimRrQWy6l7Bu2KWB6vq0zGB54z6995hyiSa4Eyy8g3pIQWP9UZ6+JMjErub\nJoHBsfMMNqv6XnDNgsGuPvCRozN67Iqn7Eq9SrdNtE9HoOhprhcR624DpCZP\nXYkLjNLd3dWlgrI75PPUn5wH7MQha8/TgrIs/8lzmoFW62BeQlqNohwk7QbX\nCfIE\r\n=81kQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "df3aa4d10253a886be82519acb901b446ca3feeb", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "tar for node", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.8.6", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "publishConfig": {"tag": "v4-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.6.5", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.14_1627080328673_0.7268348147155164", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "tar", "version": "3.2.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "b81363630ce7ef98c6bde6473dc211a0ba24e8ad", "tarball": "https://registry.npmjs.org/tar/-/tar-3.2.2.tgz", "fileCount": 23, "integrity": "sha512-D2nGRkPUc4Nsoa8fgmzQmeouUNUutMOYkUOfajmv1POZvTboS/jsgAiUnUkeb5kBExHzCrLgUkg/GZgrgMgwzg==", "signatures": [{"sig": "MEYCIQDF5DxrXIGdH3ik7cdHSpl3BVHZWtqAdCjRMP9M1z6UhAIhAM1tisrcgSddVuFt0vPe//brKYI0Kw4rGOnv4SkgpoQC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+0bbCRA9TVsSAnZWagAAaZYP/3CIpIoJEcUsXsf7vDB2\ngCH9/PHovsMD9BYinVcxIMEw5P2CKOEHx3MPkAtetiHbsoyfSpEIIEm+GtVg\n42weWIZ6rC1E7tQWNySwbK0XGa6CBl5J+DR+QIMwTxzjTCfO9DAvnAtSvBRn\nZIA0ed+6HQ4sPJRiUJfFoyLItS94HE14mKal3NUgeBGUy32PQBRiSJabG73g\ny9Sza/zRB2YX4Rzt2g3dJzh0tOo17tbHrkovOInH5ftIawc0ncUmOLR5xc4L\n7/tl1KHQwR0nbTPHTgc3wAWm1Vr3tNXbffDqbNorCE3QbDfhnpmFKIT09aXf\nrPJLKCRvvdzLMa5QJGMxTW0ww/rM3sBoUPYQoH9Tmf624/RWtNY//4aUOrqL\nbGkWzpiM6aOA6xuvxTyq1uFscW5idfZDLye7HcipyMnloI1nzNgetJU+MRlb\n/pPK5s/MEB1Bdw6vSwX0s1tOlkfYaUH4zO/QgIHc5pOic4TwKhzHz17oMyD3\njk5MTcMu8ByTbNR3yCngeKv90c9pBSpcAiDc6rL9yEmGzoWnlGwcZdQ1jfrX\ndndFBYLfljk+TVX8wjBjqpjo1Z5FVq3qHXE9NuTIlDOz0qacnyDBY3SJcnTd\nOBcTGwmQg/MQJubOrHsBO0fSKQrFhmcDPWNSoQYbBNnfbvl7RKAakNZxl9Jw\niEH0\r\n=nUUa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "9c393bb04023e58e3445127ab770ed7b0e9e6135", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "tar for node", "directories": {}, "_nodeVersion": "8.17.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "publishConfig": {"tag": "v3-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_3.2.2_1627080411435_0.20706143498776952", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "tar", "version": "6.1.1", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "4d7da4b132b334bb8c175ed1de466fe9157ea0eb", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.1.tgz", "fileCount": 26, "integrity": "sha512-GG0R7yt/CQkvG4fueXDi52Zskqxe2AyRJ+Wm54yqarnBgcX3qRIWh10qLVAAN+mlPFGTfP5UxvD3Fbi11UOTUQ==", "signatures": [{"sig": "MEUCIQCJyWDnrx1vLkopW3sNDInllKTHXCeXgwX4SaVyI3bYIgIgMmzwdUmohE59lqAO2iIhiBHmsk+r7ipzOVQEa4tCv6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+0fGCRA9TVsSAnZWagAAeqUP/0nv39Tm3/fdR2kh2+q/\n+yKc8dSWEy0hqJ+mx4mR2BMkJHc1DuXRuonI5RF5frPcRQyX00adAmWd9Q+6\n2mRcduxfniObtSpZmN8dG3N5T3tH782NMQ1yjn8ijYfPYfwMlsJZefW1lrbj\n1YU8BvhH5eCqDfungjf7ZPPUrxricgMmBxFaUxlfknK0X1FEAPt6+moVzY1i\n57b4PVteK3Ws9jEOvIVaAGj0ZlNEcfdbPWD2nLhZJlF/A5tipLD1aJm9r2j1\nF0OgA77FHZkOi1QFJs5aPa6v5gsViTsCBOeVlJQfo1zHJzZ2E8jiU7X78sFJ\n/xy16P3V+c52jb5ShepHd5gxpmLdjtHp4Or6pC7q3KRz5VCIEvVvfCdO/rsm\nYTxWRJtBsEXXvt6vdd1kKX5sE2gYOUmN1J1mITwnFLSSWLxWz7MMHZIR+wrT\nKnAATccX5NJ+jM+zo3QQSQRYfaLgwnJ8HBKj0w/y0VtZGAu/nsdjNZNTapYS\nDg/liVAz1zLdzXax+uYVj1BdkKQsCrd+Un65zdYm+foJl9jSCm2MNyGFks72\n1roeedeMWdZxI/g9X2lWGXkk77xluxfLVMTSpvTXwXnUBCVryYdVB5sCJ66o\niG8p7eXA4VuE6UZgJFQ/VaBtKKlW18jLneixFBzEHLHZrOeIO4woJSTzkRMQ\nxuPC\r\n=vmg4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "1e33534e1e96ca6385b3a4749876aea2cda61cea", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.19.0", "description": "tar for node", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.1_1627080645953_0.5865173563945221", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "tar", "version": "6.1.2", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "1f045a90a6eb23557a603595f41a16c57d47adc6", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.2.tgz", "fileCount": 26, "integrity": "sha512-EwKEgqJ7nJoS+s8QfLYVGMDmAsj+StbI2AM/RTHeUSsOw6Z8bwNBRv5z3CY0m7laC5qUAqruLX5AhMuc5deY3Q==", "signatures": [{"sig": "MEYCIQDbeAHzuQlWDk/Ekoufv/NwXBSswzJcbGqflGjpClho6gIhANNWk+TN1Q6o6E9Qv1cqM/ojQ4xX0RWqAORBbYx+1Fmz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/0EhCRA9TVsSAnZWagAA2z4QAJAj6XkeOhUHbyJk/x5O\n3R6Z7d5EUe9OeKA9PkWrtCnxfP2odnvoU/ygqhQQ7pzjtz8BqdwP4Dm84+1d\nzbpEctv14WB28owSURnh9LOHtoYtcycUWI9bAH8f2wBHOqndNZ9TGuDf45qq\nP6Go9XxRi0tmIFq1H8yLq+Upv/9OtSBRLdot57Pfro9xvVA/7WrfX/wlTr51\ni4CuCPaY39jirs+qETG2klgUBkguHMGkwJGvXwx+00UcG+v6Q9Fe+Db7zCpU\nbVMrCH1Of670hu2BADOhgX4HXS3yhKB3JNs14Rwh2NanjDYk4GzolHyz6dNx\nOuSeIEHOfcge8NdTweB/hUVsDxoRkKt7mLngNZDNR0/8OoEkyaLP55P/vkQL\nS1+ofvGQFaOCMEktZOcdy68qDtOXa4D0lZtstKe5X4OV8mgg/EpziT4MBx90\nUm2zDxiadcSDiOlj3UgIam1iiBflEeIIRhEsuu3VWQ0Eo1ZPj4NG5hixtbZg\nAFOMhKtmHO0wa5RHySJO490aCDLgHP4DOIFJLbyi9J4Vo+HPWw4IsqalpZk8\nRlEWBuhGxyDpgb0PnPlLky+igU0BIofZbf01SqokDsfW7GU1kYOd9/0Hx+HP\nkM4MIpEcpHuPdz59so+Y6nkdx2BBgOwDSzQ/rRLSGjM1dRcj0D51RiV2gzMQ\nIPFk\r\n=1qiC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "3f7b20097e0daba10441507becbf5b87c6b83b8b", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.19.0", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.2_1627341089760_0.7159131491758044", "host": "s3://npm-registry-packages"}}, "5.0.7": {"name": "tar", "version": "5.0.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "42ff8ca3b731a52f4f2be72cc4cdd7688268f2af", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.7.tgz", "fileCount": 26, "integrity": "sha512-g0qlHHRtAZAxzkZkJvt0P5C6ODEolw2paouzsSbVqE7l5jKani1m9ogy7VxGp6hEngiKpPCwkh9pX5UH8Wp6QA==", "signatures": [{"sig": "MEYCIQCuYSjrTP5i0Em+9cJo9IbDcB/FYUgnhjmFDR5nA1i6GgIhAMrA7swGu1JGxA4i+w2fyugMHHrEou9VuqIcsKrpoBiP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/0OdCRA9TVsSAnZWagAAv2oP/ic2j72wFhu4rGGBzm1y\ne5bzHj9EAvFlOc02JrDfe4bgVUOG+9qHe/LflvbPQd9Ln65mIGBm635Jhqq6\nvZFkiW9i+HfaqNDCWVAXxBrgoWpcHSSidw5FZVnk6ZKUky7r43KtSaXAPe2R\nWaNxtdh4+PbAP0C+4/Xog5M89bTnlX7Ci5tr0PhpWwj6QBgZedG5l2WzVosS\nJHYrff9jlcoznocgkvHqJoBhK99YwB2K46xB2LkoKnWZ2ZQVCqM39USL3uwa\nJ2qMCpowbSVdLWog7ia7BLCaQukb6J8K+0jPccPDgcIT+RBMVC3kXjGDYbnH\nCaQ433C+bcMIf7rS5fxyvKuDXLht+wwBIR0GeNoV+0ZMBE8F79TPfgPiVUwX\nytTIVsCZifYS03agjfCzASKHd3OPSLPt6V1eR+3CjJwmlLgu2v/v3A2A7Vaq\npcZEBM42YdPtp47KRBfc9H1EHCAzhKBoZjq6ObZdbdCmwYzJWyRwMgqEek1W\n/PnNqhXJtwIU8agQEA+gnvH9ohbPqna2slUktnKL18CO4Y1evbv7a/wjdrNc\ngKGGM6c4pbrd34kyG5zGApbiVJWCm8uPtbwVH/R7kHSeL9ddeXhAju8c6MeS\nEHPBXSMInGO5n+v56QUFvmANZGWo9wmWauuUmTaagdbD2TRfxZzGq7P8xhvf\npAYb\r\n=HgyR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "b10856c79362289c483b6bc9c8ced6bd04c2d1b1", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "tar for node", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"chownr": "^1.1.3", "mkdirp": "^0.5.0", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.0", "fs-minipass": "^2.0.0"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.6.9", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.7_1627341724900_0.3149280160798942", "host": "s3://npm-registry-packages"}}, "4.4.15": {"name": "tar", "version": "4.4.15", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.15", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "3caced4f39ebd46ddda4d6203d48493a919697f8", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.15.tgz", "fileCount": 25, "integrity": "sha512-ItbufpujXkry7bHH9NpQyTXPbJ72iTlXgkBAYsAjDXk3Ds8t/3NfO5P4xZGy7u+sYuQUbimgzswX4uQIEeNVOA==", "signatures": [{"sig": "MEQCIGpqkqoQHwSAjgt0tKE5cV3UKhWIrgkVAIHCmhaOyetjAiAcpLgMrvjwGYGI+sut2MeY400cvw4FJ1xBIZoBYpVgfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/0O6CRA9TVsSAnZWagAA3LsQAJl7V6OOYOO7berKuWDj\n/78abe0dSAKdSKuM4Y7Ffuf9/l8cdwV5NnPtmb6Wk99+kWWdST82tRsTABBZ\nGxzKXB2EDZJcBKZuNTHAngpJsd2wm49K+7jNk+qGWdG6iDSXOdJ7ja7cCRvQ\n2b2RgRjUVcNcQsL9erOoSx1rbc1KITv/7R/kWRoinMFLCt7RNAeqWAI6rGL3\nwHqVjP8pMO1Qk5YtGrkxJL/RGPmBzf4M3QqNlcesQy04EAZnBW/og/vQHsFI\n5P1ordH2Zy/XdBaUn/tvInQ5bXaHx+2nxnf3G34TDsjfTUONmSJd9MmVAGb2\nlw54NziCL1oJdyW/4ae6vZFGEJQtkiWBfReoUHLyBUXcGFdr9hA3e7ZH3dQV\nnaZGBmqRBDMKlU8Hn5m2QXaHVkYMlIWsx49Vann10O5/a51picd0IuOYVKFH\n9G/XNs9QX7eXAoWbnWzbhWfMRY/GjHRV8adVB/i7iDq5vA0IhE/CtjvMtKCx\nbEoteIkDyOEkAplSY6uNYihUeSgWJt8I2T8f77g/4omgww1Q1nialpQmaJAr\nVNTO4HODf8Li7ThWPGYADezfq1EyTONIOMTWvqmJbIpg1iUCHcfa3DqvNady\nwMLZsrVeRptc7lB9QyChD2cIOh2Adviy7ct5tXeAswnZeirCvvlVGiFnZhk+\nTM+k\r\n=1wo6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "843c897e6844f70a34bb115df6c8a9b60112aaf5", "scripts": {"test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "tar for node", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"chownr": "^1.1.1", "mkdirp": "^0.5.0", "yallist": "^3.0.3", "minipass": "^2.8.6", "minizlib": "^1.2.1", "fs-minipass": "^1.2.5", "safe-buffer": "^5.1.2"}, "publishConfig": {"tag": "v4-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.6.5", "chmodr": "^1.2.0", "rimraf": "^2.6.3", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.1", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.15_1627341754148_0.822054593561887", "host": "s3://npm-registry-packages"}}, "3.2.3": {"name": "tar", "version": "3.2.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@3.2.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "gimli01", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "dist": {"shasum": "6fa6db1421293ab65655ca9d0eaf858c91e1ee17", "tarball": "https://registry.npmjs.org/tar/-/tar-3.2.3.tgz", "fileCount": 23, "integrity": "sha512-dceKyLOOHJCE5NQx9zAS7UjVSVQ0BPrbDc2KN0LI42fBWC8OV9+DP/dS3CMn4SnnNpYKdmEP6crYgdbVf1ZCCg==", "signatures": [{"sig": "MEQCIBJXbakfh2uva8Jgkj9jtGQBR99tkpn4+ivybz4V9k5dAiBQANtD4QfZfufN89lpiSUvGwkAd1UaMja+jHiO1F0Jcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/0PECRA9TVsSAnZWagAAefcP/1TmogcHuOVdkOGSbp5s\n0iWrBE/v3PZY+J2mxtjXDVXzXBd0EgdGuj/PT/Gysfw9S50zw3sz+AY17iRI\n/2s3mrVEZdQTcGaez+yh7+Jm0T/vZa0gZV+KVvzKELJh3drMRg8paxXjLc2B\ntZnuGdHg/QPpwK3R0osnAO+MQrNVgd0kG6TRGJO6J67fKehMPG9fdqeHqMnf\nf0XIWxkBDTUE3Mk6W3DnNBxpDWPVLPwD6l3fFpudzAKECwQP1H1JOTpLCjsc\nw7ydRAWK+FcZvvBLQx/wmm+uBtdZljTPwxMA17McMItzg7167IFUHf0BnuGU\nBE62ZY9suebCZN4sZ+UKKpNlwLxt2FCehniLjozKdILJc5z1Jnu3WYVHGBIM\nm+rel7jOi0xIRf4xkN6e22oJ4oEZNJeFw5DQ/uDSGOMuL85EB9/ZNNDhKbAa\nxNh3ybMYv2tLssAa0DV+nr5abecLuFudAECTgDnFMsO0CdDl0mNoYB9AJrYB\naeh8i0yIpu54KtV5ox0b6mFO5YoTSK3XG+b99pw1AihSirkpOgRGoyZYXJY0\nFJa1KhID0uNv+oEXRqHypft/ePCvQBktVQLcZB0zSXbzg/QV4CJ6Iw8HFvOD\n2/O+Eh4pksDXf2EgqaT1GJqs+Tu3E5iNhvQXXeQ5QdV9S7m9YoHQ/ys/hic3\nwRw0\r\n=94LZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "0532554701454050693bad82fe0470637715b400", "scripts": {"test": "tap test/*.js --100 -J --coverage-report=text", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "deprecated": "This version of tar is no longer supported, and will not receive security updates. Please upgrade asap.", "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "tar for node", "directories": {}, "_nodeVersion": "8.17.0", "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.0", "yallist": "^3.0.2", "minipass": "^2.0.2", "minizlib": "^1.0.3"}, "publishConfig": {"tag": "v3-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^10.3.3", "chmodr": "^1.0.2", "rimraf": "1.x", "tar-fs": "^1.15.2", "mutate-fs": "^1.1.0", "tar-stream": "^1.5.2", "end-of-stream": "^1.4.0", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_3.2.3_1627341763889_0.9491950608130486", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "tar", "version": "6.1.3", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.3", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "e44b97ee7d6cc7a4c574e8b01174614538291825", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.3.tgz", "fileCount": 26, "integrity": "sha512-3r<PERSON><PERSON>wucgVZXTeyJyL2jqtUau8/8r54SioM1xj3AmTX3HnWQdj2AydfJ2qYYayPyIIznSplcvU9mhBb7dR2XF3w==", "signatures": [{"sig": "MEYCIQDFwZz53fvV9pQWlU4ZcqRKKm9WPMs7r1X02LceMgq+lAIhAIqlJ9YZnbtIfePKjqeTNdqi2Hgh0Fi6SseLvUNu/5eJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCClRCRA9TVsSAnZWagAAi9gP/jkI/lQvmeNtnirpVeRP\n+uNrLgcP5p7ohIxmSJh2dWXsDnOX+MX9mwfBsgTi2FtJjwk0uW0IOW3s8AJs\nMt8u+k01ccUs1H+LmfgIqbwM1jBXIsB6wf9FREmPTFo9FrJ+mjK1uh31FoEZ\nCc1sB+X79FEBRFNEe86vlgBzFPwqUt/yPDFTey2ipZVPh2GGhkp1raS6roZ5\n5QBNjzOeL5o76SdnPscuIfVlmvQBwfFFF0w3hz+SdcAii2TT5Zobi7O5lncE\nCLUXqJ8p+kgQR2xMNo4X/XdmC+XmPqPn7MN4eAMctXUnzYj4gKDPgAh4gfh+\nM8ASdm+dwL0/d37R5usxx2hrdT7GNwtZ1PEY7DMqNo5PlLlJbOEVsTzOuhHz\newWYrnAW/X0UhjjgiaiWt/WHMGTE4DyMPkB/lpGksvwrf4OYuplUcelG8v9b\n2fNs/59ohuWJEUl6KRePrzF7rTQzET2OrvkvMzlTwAhlRFxExwhvIAPo7ah6\nGQKiEaim+c+shI7aD114k3T07mqWzJ0c23ZtXYxDhZdeggSRpRmOzT+u3YxT\n4wW356GQqTvWOFjIYpFyBw1rwR+UWHFPxnCzSDf1Ni4cKz2OKOyFyWw9Q2L1\nHxbiBLL7NkCfXxNFX4zhwceie8tc3nP8nYKvi44dkvmYMRrV32L9JsUtAb19\n91qY\r\n=wfn/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "0b78386c53b00dce422742e19de94f2a4d9389f3", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.3_1627924817188_0.6374361063660521", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "tar", "version": "6.1.4", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.4", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "9f0722b772a5e00dba7d52e1923b37a7ec3799b3", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.4.tgz", "fileCount": 27, "integrity": "sha512-kcPWrO8S5ABjuZ/v1xQHP8xCEvj1dQ1d9iAb6Qs4jLYzaAIYWwST2IQpz7Ud8VNYRI+fGhFjrnzRKmRggKWg3g==", "signatures": [{"sig": "MEUCIQDVjEBW/w2oYy+OhG+TTVGjPggKwbbl2tLbDgQXg0FbegIgXwxHcXFv8dS8somjuQiuKnWL6AVssPuDWJix5k06Z9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCbcPCRA9TVsSAnZWagAAQ+sP/2dU0y9qknWMVCqCcjy2\neD+nkIm2Yh4EUZ0vdanoL7lBDIvDsn+or9VDKOVPdvlsiVP7M8gKS7pXKzDN\nhYHfLcVsoeYgZbgQmKBCgkrz4RxhGDHuAgX757OHOY/ejkaFqopZl3hIHKw2\nNKvCXI/X8WEE51gW0LBRY3I6YCckmrhdlXhceRIwqseNmVHWGP/Ny6cmgfXA\n+KW3owiUooCkI1qx26N91LUeTvg7rfgebOzMGEVKGwKrOd5ANzjypw9YFusn\nLu7o2D6Bir0opjzbMarYhf5/mK2ZtHSF53PL+ug6hQRwtZp4niqupaDIcK4u\nZkvCnTLmtMXiXOGgQr08ss1uKtCvsKOSQo4XtkE+N+hYsPd2asy6CkCo0gG9\naanJZOqeVAZV/aHPyPy4jd6gDRAnut93EoYQlpQYNh9owOg6e8WT1eyUpnyI\n9DmZcTJWg7PWryx9oECmpgzaCoTDdJFmyhOM8oPtGsdD3sIageXyRM3nLHjX\nBagZPKHQDxYA6OLewb48tClzI3h9tfaQAJzBpggW9a4r14qd5ySRavMcZ0mz\nk4BxZhP3fm0m+A97Zn12I+ujXw9PPPbHKmjz3+9IYXvptDGJacAEswAnM228\nSHHZyxg26fs4ZMVR5IOXIZATgZf23sc6DtUPcieTLo3fpRX82d6xbAUnMFwK\nfmCX\r\n=pjNi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "bf693837b3dcfeb76878b212310302dc5dc3d3dc", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.4_1628026639011_0.9987201778192802", "host": "s3://npm-registry-packages"}}, "6.1.5": {"name": "tar", "version": "6.1.5", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.5", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "6e25bee1cfda94317aedc3f5d49290ae68361d73", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.5.tgz", "fileCount": 27, "integrity": "sha512-FiK6MQyyaqd5vHuUjbg/NpO8BuEGeSXcmlH7Pt/JkugWS8s0w8nKybWjHDJiwzCAIKZ66uof4ghm4tBADjcqRA==", "signatures": [{"sig": "MEYCIQDgXuUrHihYEeXgX+/Al9tZSKuzwNDXmgv/XMqCpE5ypwIhAMcRR+XcnuIpc52hSgJgkagczKsTiibhPocOyxCbi+wa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCegdCRA9TVsSAnZWagAArIAQAJ4dlWgeNKz0cz/bMzJV\nVWjxGK7gPZUmW4cGy7JoYE2F+wUn8/ZTR+6w7cEbwgVPQ4Btv10tvLbHLoVI\n6P9ktrlY5N+sFQ0rp06OThEAdwax9JPMVQDn0HROJzut2FedJYDo8mA+l6I0\nI6AYVc+Kc7KGefXjT7DNPU6tPyI3mrCtFPElYTbwBUm7mmmdT5W97oglrU15\npT8mRnZ/9adTMBzTyQ402n1F4CsXqbjn04MLf4Xl1bfQm49PFokGkVRWeH9X\n+W4hGiSqynsb6m440FekZyUSclk75h8W5VIkjhjHbJ/LoePr1OfwCx1U2GSz\nlLZxtCcaoRW6D1fRFAygucqWOb+YLASnvOfyr0pq24ZbDn+qaUtpKq//w06D\ntcBsaF2+FhZfgiaMldZSE4p5D2LJXeh2sAsqz9NZVHF0CvMG66wTy8I50UGm\nPJQq56PfF20kunjAZfdDhBsib27gW4eApKvxiDDOUrttgYRl+QhhCSVPDY9s\nuqswtUI7cYK/XB3TEsi6CHMEp5P1FHVAHOz8Us5cpVSJ2AdsuR1roeQGSX6L\naCbS4B6+xT2+1wEEHc9Qced/i+DJaScYBGPURuTlS5ITh1xId2T59j9XlXxu\neiKiI3YhZJT73i4jcwxd6EykoviChjz0o3+QmImP7SpP7OcHUFZOc2Jk2YuV\nEuDk\r\n=gD5Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "bd4691c90478f41b2649a97048199e34927dc046", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.5_1628039197727_0.37752617123503684", "host": "s3://npm-registry-packages"}}, "6.1.6": {"name": "tar", "version": "6.1.6", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.6", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "c23d797b0a1efe5d479b1490805c5443f3560c5d", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.6.tgz", "fileCount": 27, "integrity": "sha512-oaWyu5dQbHaYcyZCTfyPpC+VmI62/OM2RTUYavTk1MDr1cwW5Boi3baeYQKiZbY2uSQJGr+iMOzb/JFxLrft+g==", "signatures": [{"sig": "MEUCIQC9me/viMAD/Nz4wLnrAk16qt5xfjXlTfwVcwaI0gdfAQIgXt2VCb7jWyOJ44RoZBJ8XfRk88fAhS76V9Ap5ZPotYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCkCBCRA9TVsSAnZWagAA8TQP/0lXsIVWQ8w4WC3sf3oa\nb5TJauK5VVbeYvblQIQyv5bULhyDiZcvdeJRCt+NRbVv9G0IqAoko7p1tIlB\nd5wz+cnkGmcHMS4YYgILeJsTIumg1Q1yG1dV9iZa31W4qjBPSYTHsZz/WQDX\ngWjpynoGmwibCQEDvJDKNOH2eIpu+K0o10UeyqIm1izKwhYOT1gcKewUjvRS\nqgQEib0+B1e6W4TK0gP2foW/glN6avZBCMyB7ArvvzcRnpokrHS7OUgiy/b5\nVuH2yT8jnWLnKi4BD6oQAn4fjtKvrHUSbF0nkm6yIQmKuGh7TeuVetRxYE7Z\nH9ZdxqgZV2cntdh5GuWyxDx7nRk0fCeCshK4l/uUBJLUi4TNRsOqUSK0aqKG\nQDW+aOwErTB0Qz9zFlMwSjdLL+eLr49tO5feN4uG5L+lX4ofhz7FB/isez9f\nEtHXXuDCw7hZnWXIyuajUSVYcNIEt6LhPPuOXWoboqHGnpSV6Aj+5Vh1L/3l\nxxAlnSMEhOW5WuE2SiV01n/gdH0gD2zNtsWYmIUXGcs42sTmOf2SdtAuYLL1\nFL4N1xgwp5PVwOvYqz9sv4/AkueQ10pN5zvW9YAgv6ud2flKASLeliayVfAl\nXlu5iKJsnl1LLC5/4Z2NfJxRdTk3VAOHx8zLCrYE9ZsDNL8K5Bau2Wh8/i4x\nLLBh\r\n=vmmX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "9bc1729939eec1c822b528385b1cc513b9888835", "scripts": {"lint": "npm run eslint -- test lib", "test": "tap", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.6_1628061825171_0.8830153421193472", "host": "s3://npm-registry-packages"}}, "6.1.7": {"name": "tar", "version": "6.1.7", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.7", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "c566d1107d38b09e92983a68db5534fc7f6cab42", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.7.tgz", "fileCount": 28, "integrity": "sha512-PBoRkOJU0X3lejJ8GaRCsobjXTgFofRDSPdSUhRSdlwJfifRlQBwGXitDItdGFu0/h0XDMCkig0RN1iT7DBxhA==", "signatures": [{"sig": "MEQCIEx3YzFweegfSql8se7qgYp9ykzTpDaOpX35d4RRRq2oAiAczZ0FiERx/QwbYTbSdELK31LLP3sd4xCZEz8IG8Sr3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEbcJCRA9TVsSAnZWagAAOkUP/RXLL6YGa5RsICn9KUYV\ntYub8z3dVCkeLFqyLnNe3JoYOb0hDweHLk7Z7eREM6IjVnpM6kiawaWo9yqT\nD/aFKEFlwIYqlrUD3kMpp9SiZwe+9R5cdKC4Eg4IHBae1eVAeIa1pECZfgnw\npIm4CFsiLaDu4kEDvrcHFr7dvyxJsh2MTWwN4JtKeptKTNtZQqakoZNkVspN\n41ylo60G5yzST5Czyqn14sLcXNwU3WHKTHFcbqwS17YBVt/gDJ3nV2MGqVt2\nn/i8I4KUPbGoWyr7ae2yVPiXDctqvCWMvdGmZa1GVdRVmLrz4VVTDfR0GpA9\nBK0FqZc7ybqt3UYo25/odNNpgbIxSDk074i7Y89wUHL9CHcsxqIZQbjxpQkd\nTs6p7k5b2gFCdasKfVONovo/8hOvfHl133FjMfhidFPliTE025cq1uLB7Zmu\n7ub2coroevSTP7j0kvF+BmdrPpuq7oo/dXdk+EWwnGBFfLEmx+v05Pxczo9x\ndlfCdvA4ETUe/OMhCPbwLy7JstXAEyROoR2PZ6Ut2Bs2oEZOmUtY/uUeNj3a\nERNE22axWJ+tZtIV7dZWGHGnSPl/cxls2ZLfDOBXmSXx7ecRTxZkMiHjCZ+0\nluFfewl6ERThMt05r+iZOlM2Iw+iyeAdAnyeqwXZcfO2PgD3AxtLp+ih+oj7\nyAwS\r\n=N5ZY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "d61628cb40381d89f119431a16a4aab2fbecb056", "scripts": {"lint": "npm run eslint -- test lib", "test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.7_1628550921534_0.3728176440701534", "host": "s3://npm-registry-packages"}}, "5.0.8": {"name": "tar", "version": "5.0.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.8", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "8cbddead599d7335aa2be551bfb18521dea488a2", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.8.tgz", "fileCount": 28, "integrity": "sha512-v71KDHvYI+EJ8+o6W44fzuiTDcfHrlVjtvn4ITF1ClH5GbU6H9InNX1XNG07YwZSkloNQQxAJabkjEdHyqjn1g==", "signatures": [{"sig": "MEUCIH9QapVkDlb/zPEFu3CrjxrbzyLsJ71TcuXI+OFI3bs5AiEAr/9+N7HMa5jI89+vQuzHPpVaimHai7zs0HGJPD8P7eY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEbcaCRA9TVsSAnZWagAAGdEP/j5GIg6YhN7BlCoj1od6\na8lOxPiBbCjifP79FD/8nDd2tH24VCb5xb3uEkqtXE3wOiYqH972Ut31KgE4\njS3S/I1RivSoku5FZ9XOKxLdllOP8dWNTbYrRU1PJJ/nu8Prwl0ZpZA2wJZx\nIz9Kxd7/03ngmBE2VzPqnPZDQ+euCrfdtKttAaDL6R3BP6nb0+CzyesESMlR\nJz4zJd2baNMGnAh1e+xaC5RM+DfYluN7DaJI+CCcxS/N2fzV1FzJvLJDFVWV\njovkukpUrhDPe+ePE+DXBmdRFT+7vbSztjqXdKzZqVMp0ZcP/zPMmufO7GcB\nuzr/s/S1Vi4qN2DUMU406J/jPZEZ+cc6sxxGaJay8EPTmAHAdxG0xWI/ojOt\nN1CwfNPCW5Zb7PcRPU1//Sz7NYcg9Y2SarxmW51Ut/KrkcV1TLs3NZuG1F3d\ntk+4sEHmEhr3SlO8e4ZkDZZsrBSZxyr8brC2p4iLC9HwwmuoAfrScbjpJH/B\nvDxgfGK5zpt8bAfqao3mmkS5aQ8IbtZ16bE3G45O3LVGoNm51WF1v44rjMSv\n4rjsHM3jb8UqfZhXJTKHlvb1iLR86fjMwnPkrem6mrY0gmaAbtXD1x7mTfdT\nzkAtJ9XvJ/1l9+rHcXJIWkw4Ki8JQL3cLpiNA5c8kUtemtal7JSHhrZA1+9x\n3I0D\r\n=2DbV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "8d2a3168021d12c3a48dbe74b4add954269d16a5", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.4", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^4.0.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "fs-minipass": "^2.1.0"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.8_1628550938369_0.31359692616754", "host": "s3://npm-registry-packages"}}, "4.4.16": {"name": "tar", "version": "4.4.16", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.16", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "4a48b3c025e77d9d0c788f038a09b91c594d326d", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.16.tgz", "fileCount": 28, "integrity": "sha512-gOVUT/KWPkGFZQmCRDVFNUWBl7niIo/PRR7lzrIqtZpit+st54lGROuVjc6zEQM9FhH+dJfQIl+9F0k8GNXg5g==", "signatures": [{"sig": "MEUCIQDCLrUu0U0x6tPszkHn8iQB7JH5YYQC8I3vMBwLYAM/fgIgSPeelONdveYnCznWQygsABVJjwBqyuf9GvLp3URAj04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEbdvCRA9TVsSAnZWagAAfUcQAIHDfuatAhFkMndAzm/V\nNO3ytAsi0cZaAGyDaivsox/8QOZb3pTa402v0/pXLQz2zL5C5eLhG0umsCNA\nYz/itxqztRDU8FknqUvdSZtcAb0ra2ovKq2FvXu/nbmVN34uI4NEvXAu2TDG\nO87NiwpUDFdHYc6PT/xLQHooP7PvAoADsxNmrhMwkn/e3Bq7igmVNldtutd5\nyu6i4NHNI0AG/qFNd+ul1I/ZsPUR9AGmr/vWIPmUe5CuQhOLOqMYBHISy1NL\nJFd1BZya1gv6w84afsxndxjqcMxkr5i+lpDI/Yw8T3aevCLD3WmH4u9rBjDc\nI2WiXoOYE6sor/HecitdblorAArCaQIeGmP3Ua2mRyhW00kgsQhzNxlCSiE5\ntl24fwIHDLhR86EgKLwvE/zAIuPfWNsEqZln/6RFgmd05i1ghUs4zKtw9nlz\ngEGFPvQjsM5HOrk+uL/OzUqEqq9W5VLS/pwdSm9y20rmV2wLNGcWqxjzfk20\nJcLkdAeGX7OJwUERLBI4+Uea8adLY/MwtgkrD3op2kLR6cjPIvdGW+Al8QM8\nuukBUTdKXAU+3handJTcOMwr/+3seNTOVlHqlW5qLbqzAIsrymTAzVsPhoko\nEH8B+J80orQUsrtEagjOvjkoMIuvI9ftmSsUUZECrHKDtNwIlR+OH9hwr8eS\nZfM6\r\n=l3/2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "fd6accba697070560f301604b8f5f7e2995a2a8b", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.4", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^3.1.1", "minipass": "^2.9.0", "minizlib": "^1.3.3", "fs-minipass": "^1.2.7", "safe-buffer": "^5.2.1"}, "publishConfig": {"tag": "v4-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.16_1628551023337_0.40079804245386286", "host": "s3://npm-registry-packages"}}, "6.1.8": {"name": "tar", "version": "6.1.8", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.8", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "4fc50cfe56511c538ce15b71e05eebe66530cbd4", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.8.tgz", "fileCount": 28, "integrity": "sha512-sb9b0cp855NbkMJcskdSYA7b11Q8JsX4qe4pyUAfHp+Y6jBjJeek2ZVlwEfWayshEIwlIzXx0Fain3QG9JPm2A==", "signatures": [{"sig": "MEUCIQDn5gw2YKGLquKOtzHPmstHGAyg2A2iQSsaLVdkZ36G4QIgMvwT3wRiubZc1cUyNzeIXkfADjgmsDNiF4YChMSuT2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFCPGCRA9TVsSAnZWagAAvi4P/RFYZ56TeDu9+t+IcFlk\ny3Eg0rkyBjfcdcuMixY1PrUYs24XQz6M3wuJJw2qcoOxYm+4xB1uTKYgi+Aj\nfeD7drVcLLqzqCT4v0aRLmqBPR7JU06STZboew1DwKK+zVlXwX12LM9R+K9l\nbsbekOkjp2L0gbT9/vs9AkaMXvbqyBcNkRq8pUyhfE+lnxuVJ662e1IC/CI0\nKWEIfF4YR/2p8Jn/VMdlA0iMbwq7RhJOMCswg7AoCZlj9cCFvJp3DUjDlSS4\ngKOPMSC0hm6e6tMjyf0U5Uq10Hw7BIMzKx7nrGlOfD6EFnuT/l0BAedNmcqK\npLAzNkObQSZU3LDPVXlm2vRiwhw7we0WBV9NFNVur6HmGn2bB+3G/pL5Tboh\nq7cEdWqL1hGVQEhV+Ze/Ykb9pLbxovmS7tkdLFjTmfd8ZjFj2mk2PjDS4CR2\n0XXc9m28UEb6svVQsCWO6ufuHpG8m3ludGglXOPw7bS/lL3DMl6K/AmNMJwR\n0FBCQOKWnWDMGrq3i6SkngTn3RTehz8oco/NejMrBtXuZM61YsZPtaTC1Jn8\n9Ia12MUWSfJ/Ww02BCIKImzpZwv0R+9/ntNZdFGKB4+m1lQUCHcGtnHIk6oU\nATOn84h53JwHRh9Ri48ekxzWgAMUBnTYl7bX+5xERkuvMEZBxmCCuz7mj9sY\ntRlq\r\n=8OPn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "6a9c51da31a2c9b67d266d8ce7119e1e2c0d1e5d", "scripts": {"lint": "npm run eslint -- test lib", "test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.8_1628709830403_0.21673040450647862", "host": "s3://npm-registry-packages"}}, "4.4.17": {"name": "tar", "version": "4.4.17", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.17", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "44be5e3fa8353ee1d11db3b1401561223a5c3985", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.17.tgz", "fileCount": 28, "integrity": "sha512-q7OwXq6NTdcYIa+k58nEMV3j1euhDhGCs/VRw9ymx/PbH0jtIM2+VTgDE/BW3rbLkrBUXs5fzEKgic5oUciu7g==", "signatures": [{"sig": "MEUCIEZooy6gPnOI37CsmcOXSVQgcX21ul3GvSe4p74y/RHIAiEAw0oV28+ku3ydVwwtKl04tTEl7+ZtBOUWGakQpCGf+1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFCP4CRA9TVsSAnZWagAAw8oP/jqWaCO6AFF2rktqv7Gq\nORPACrjPl8+sh6rPMQuIG2B+gQ3mlz0jbbKZ5hI4MrPM3caINKSgYoe9UoLM\nBAAo2bcA6l+FB88Sll1bh83C9J1uQZxe+kqsQl+WIPm4PMiW2yhhBfnr3FmN\nkdRHioh6tDJ5fKgdH35CVQcIDlAjXVFnjJfRMvm6b3REkHEOMSde9mwG2MXv\n5clwQ5Ca8Ij8pUwKNhEBcMqlxAu5ISElfMlVDm5wKrFELqqZnH6RKV62Izly\nuGRgObmxmRuHphp51NU5lMLKNzUSEBr35HTEw2JhAZvEjuznKtRZHivjnkkk\nrOKziHBfdCy18dB1yrQpaZPoDQAIlJCmgzAILxoFrpG/M7tLZdr107LtUbRM\nH9zvMBKIKgcFhZlvvN3fo0/+/XHyuKa7JLES05ZqiBNC56ayMvuqn3Xwda8b\nGVVbJXSq4Y8XtnfbxEij5Pq7ECfzmJXWompHOnv1ZYtGNgC3fIa5U1BVPWfh\nB9TT6hlstLSYHJPl/Bs4GLZO8fUKNlczcdP05gnZE2yABhWskGoE7hYWfFGy\nB+ggUurxarkhX86DtYX7Yp6kYoQYlhdTVN0LJTRfpwoJf11h/32OaZb1XbUo\nIwq/aDfl7rmnovHVuDErdOenrIYVFYgxAsVAdu66+0GyYc3PNHOO/4TiExmQ\n0Ela\r\n=KR1K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "9bf70a8cf725c3af5fe2270f1e5d2e06d1559b93", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^3.1.1", "minipass": "^2.9.0", "minizlib": "^1.3.3", "fs-minipass": "^1.2.7", "safe-buffer": "^5.2.1"}, "publishConfig": {"tag": "v4-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.17_1628709879947_0.32096013723033967", "host": "s3://npm-registry-packages"}}, "5.0.9": {"name": "tar", "version": "5.0.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.9", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "bbdffcb5949fa61e691dbcac03f248fe08b5828a", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.9.tgz", "fileCount": 28, "integrity": "sha512-XvvsfjcXKwCmAKTmxRyG5XbyUO0eZ8tyKXkAQd+At0SxOFCN/WvuxWXNAR/UCGXCvoDi+rLi5FV5q7wVhC5X/w==", "signatures": [{"sig": "MEQCID+CQqSSIC/lsnp8tIkTJvWLWHCKJHdRqJuREEdB6go4AiAkg/MLmjLz/ggYB7PR+21oWq65Sp1Xh8XtTgneZzW+6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFCQRCRA9TVsSAnZWagAALEYP+wRo+b/JptAd7qtRr9WN\ne/jHAiUWu48+qCutqgS/LLFTOzUDL/5fiMgSDBpXHXyZan6cKdeOdEjxI6Jt\nxuz6iK+2QbjHZKesPz1mKdnImJPFT7aVooUgPQqTRtlDy3yOJq/OIxcZV54k\nYimoz39db6z/T8nlxK1pYTaLajBpVayFwZEMfGzadLNQXDW+WJCyGmB6XdDU\nY30RJcFZB9tN2Xb5RQJerhn/Rxfjn6HDOu/D3kY3L6c6XF4ZqwqawSJW07r2\nLy2XqjSYrbmP23zU0tTW3uNdTa5swNzZN3VkQh4wb8wQioVccATdOJZTKq+1\nV6MMWuBKlEonG9iqfpWoFkTZRu7h935GUJ+z1cOL2txyTN+XmhmTB3bXNc56\nHodpE5DwgvvurEEmaloVJaqlDLrweI4yPgGLmA7YFcp4nUA/EUGoZCWtONUm\ndunXCEcKDiTIXGc9F1c/b/hD8XjWOkiJl8w8506qsbV/G3islOSkihvpDBUk\nGlN+rayJ0a/yGfDhjO8C0mrC7WaDOIhjBHe70XQKIuQv9xiT+MoldygjJqqj\nbYSnvrXWGDJaSacT/fLIoxq8Dekn8Tr2u98Vlj/Sq/yE1XqPyJQDeswUOFm5\nbbrEc3FE8Ec3kFm9yeGA/UUfU9OBzSnjYT044vLX/V1gW1TO8ii06IudcgFY\nbBM/\r\n=UvPb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "173800ddb104f5c74f241da0967b5992af9d45d7", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^4.0.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "fs-minipass": "^2.1.0"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.9_1628709905098_0.30075042077406366", "host": "s3://npm-registry-packages"}}, "6.1.9": {"name": "tar", "version": "6.1.9", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.9", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "5646ef51342ac55456b2466e44da810439978db1", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.9.tgz", "fileCount": 28, "integrity": "sha512-XjLaMNl76o07zqZC/aW4lwegdY07baOH1T8w3AEfrHAdyg/oYO4ctjzEBq9Gy9fEP9oHqLIgvx6zuGDGe+bc8Q==", "signatures": [{"sig": "MEYCIQCM4tH3oeD/TnnVljh8a0d8iBUsDSIyeytpA0KSHJvkNAIhAOiLVkRsYvQJicwUinVLIaUQ4aQefD5GPwx9XWstWIMi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHb7yCRA9TVsSAnZWagAAIesQAIoF4y3F547zP3k9SIPG\nNt7vr3rCJixJmcju0yJ1ZomOhf/NHxP3Wj+eUe+H/5/kBCfqDJAkHlU58ECf\n7uzd4Dy4ZgLfb+hNkh/ka/CBF8rNE283091jS069/Rwgmb4AIFUkIJT0EaLT\nQOgDbVJl3q9dTCUGR61va219bbBFwL1yeHvk65Hia2lUeaqmpPb+mG4uSyjt\n6K6dmmeFnJ79cYvt3Cz5aMj0zCQMgmciaLxnQAOovf4nk5zv8CvdPot5ZAur\nBVAuimPieCdOELdF/Oczv9F1KExP8xbUixPBbezXA9Rg5N/z5oJySIDn+LjJ\n4l3PwrYWLV1RDhl+3gagt8TRf2rsgl/Ifpg24i7xDKbQyPqAtBWPdFhzRNkV\nRuvGM5lfkjhS2Prn2gGPQq6YjyDHFWma+z4/PLQP1CFEVvX7wUCCUWjiTOVw\nWFs9QqmAMt9u6CKgBj/IDNK1qo1koZV/gTaBhj/fBs2mCwQtVeT03MDlBP0j\nxD+L5QF0/6q871GdjiA1t5MUBeKhbFoVK2b2fhx/8crebeKc3vJLZR3RH7MS\n50JNweIWal9wXoGLGzVJsmXXflaUnWVBb1n8bPVj1jT9b4EhCuWKNVJWqJCD\nFE5l9aOkvRVIEVrWoCIgy4wVHt9LAObSS+X+3FUwWyt5JJdiQteQQaY9OxP4\nfLHp\r\n=5QoG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "4f1f4a21fca64f3089da0e83ceea775c66b55052", "scripts": {"lint": "npm run eslint -- test lib", "test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.6", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.9_1629339378004_0.3574697354359857", "host": "s3://npm-registry-packages"}}, "5.0.10": {"name": "tar", "version": "5.0.10", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.10", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "5bbbb6464472d9703ffc329f3e4de04a858b61f8", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.10.tgz", "fileCount": 28, "integrity": "sha512-DE64epEYvKcBVi7u5jZpnffbiZMzBZIxJtZpXkb96LoQWpDvCPUgRG6lqHW9P5oF866fYuz4XwmpALndo4T/Xw==", "signatures": [{"sig": "MEQCIFm+6qqaPvTe+iADrAOzx+XLV2fS6uq3guPsjRku5NvjAiBaLc43861qHHR2zqPmpkHSleg4uvAYCjbR6PMa6rpohQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHcEpCRA9TVsSAnZWagAA6bwP+waCPerud95wfyl0VHlj\nd4hEVD1TrXESxO25UtW0PPipiNBTO0x8AU4f5gE2ORRenhQfRLITSVlhij5Z\nrN7qMUDxrATQ0PbiMaPhVUqzcWX4XDWLmGZRjPrUOMPq/rmjxFzwyETCIYij\nmUokxtpIT+TwGDRc2smyIOpnAfInDHwL7rs+8E4coqaLUk8hcRT0VNYykHi8\nHDjq3p2T3aRcy1Nxw0FfQdJ9QhLLD5cWpUhRB0xn3C7i8Ul1YwmkJN+n7wYr\njvOYLyzqTGxtQ2w/Q7d2ZwahsTuzytoUEfPWJPuxAZMo2+3j+OvknAZIwbmP\n+OY752D8RxDFZ/IJGg9v3bVKH1F0xOe/jItrR0NY8SgxcdDUu+IDI9rl3GrL\n5oA5wqYjuE0cnD9oCrc4hSRD97iGRZZeG2A4cSvbnIlNEGmDqPs+jnkAseq+\nETQ8fi5+mxnFibKtic3FfHTqpNEpRvJ8/88l/3kFuL+f/CGQP6ro/WLQ7zG6\nRSIgfTNy3z33n6Iv0g3lehAxnmXFVeMpyPEaInCPMPTFidwCL0QU4dP+Xqoa\nR1Hje6wWHoAJoBydt2jHmy8IKs8B9ERhcxhteHpXH2uOITPJEUT/TCIlJhJK\niYRy7l10a/YKkITypKAZNULxMv643GiaOkMVggYqGx6UyTVCVTHFbdOt00u6\nsYpO\r\n=YFDU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "df64c4d5d80292e1e043f590de34da237a2ea12c", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^4.0.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "fs-minipass": "^2.1.0"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.10_1629339945005_0.4606425075348879", "host": "s3://npm-registry-packages"}}, "4.4.18": {"name": "tar", "version": "4.4.18", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.18", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "a565090fdcf786ee08ed14b1739179451b3cc476", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.18.tgz", "fileCount": 28, "integrity": "sha512-ZuOtqqmkV9RE1+4odd+MhBpibmCxNP6PJhH/h2OqNuotTX7/XHPZQJv2pKvWMplFH9SIZZhitehh6vBH6LO8Pg==", "signatures": [{"sig": "MEQCIBEBT2BWSybIJhij2tk0A1+1SZWKuNJIVOLMdIm4HpClAiAorPhWqptXL4k0RH9XNkipeKCnWnp5kviuBhC7NfBaww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHcGvCRA9TVsSAnZWagAAubsP/iGBqNhIga+PmNeu3WBr\nQelWv3CeiC+fOn0kEXgpoXTPKECafp1XUIavM6cNohvMvisS2DrmRMGpNluU\nlpkmPZdTUUmd0Wqgld1eBa4q+QbS4jU2j3Rz1D5Hdne9KhusfrJ6xVljs2bU\n7m/THjN2ive2KEOUQXvy1PKoZcDeg99jf7vwhvUjMVIkxTCF0roslEYNUcvc\nJoKE/vU9ZvpIDh7uFtrxvB0QBxRyyhA/ixzKqJt33nb0QvjS0CSHXVQBQVC8\nSV+gwq6V0tZ1d5eelVZ1VR3lN1quRegv6drtfI3DI9w629kR0mlRPR2HrXC2\nFEzsI/yVTG3dd+rW/DgPiY9NGsEHC+tOIAuKvWCxBhAH66IWrPN0ODXZqfxI\nXWWG8oaGj7Zqx6anVb+Ybxvkt5Qbq9ubyPnWQzpOw334RQv/u1OPYLpnDKvY\nKZ6UTNxPjKSi0hklWOZn5ukEHhAHrm3cxzG5h8SYOvIGTRN81CzsREyPsPOi\nQG9bIzatqHa7oN76qjcTs8ayrz7mohjy3nFYzYp1VHFEcADN1hai5pRlSIPK\nPrJi77IaaxSMvbB3vg0jTp6c9BIaGRTBzjx/oQSkyVYIklhJySqC64JuvICq\nni3p47wEJCtxttudh4hq2m9WVBlvSxa7dJ7VXEwH3aqAcI7d1AMxUbFTGkr7\nBa+G\r\n=pwaa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "3e35515c09da615ac268254bed85fe43ee71e2f0", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^3.1.1", "minipass": "^2.9.0", "minizlib": "^1.3.3", "fs-minipass": "^1.2.7", "safe-buffer": "^5.2.1"}, "publishConfig": {"tag": "v4-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.18_1629340078966_0.525414223109776", "host": "s3://npm-registry-packages"}}, "6.1.10": {"name": "tar", "version": "6.1.10", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.10", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "8a320a74475fba54398fa136cd9883aa8ad11175", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.10.tgz", "fileCount": 28, "integrity": "sha512-kvvfiVvjGMxeUNB6MyYv5z7vhfFRwbwCXJAeL0/lnbrttBVqcMOnpHUf0X42LrPMR8mMpgapkJMchFH4FSHzNA==", "signatures": [{"sig": "MEYCIQDxEIKvUa6OuIGRlaOQ6eLE8u6AigPYXW699LO0c+7vvwIhAI86pqEIFbyO4TE7ynldyM63z3Y/sQGzLyr+N18n1Z18", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHcOKCRA9TVsSAnZWagAAS7MP/1eFMKE8eUHjVPkAX+ff\nD+JJrq9oJp331NV+x/cPk4AiHplZbMrYmNiQ6QJW4stp+pKEdVhtauC45d/G\nyJM0arPgQVuedG/Z4rQ/J7v1Caa9xXZRuhSfrnEZ3eAPUJRtb2YCZvYMY3Vt\nYtIfSNmtAl9SmpI5AUGmuZElbxRLzUIJrOBkYiAp2zfNgekcr1JxmJpuOVdC\na2RKTJWdENjxuvQ4ulmbkkcwHJecq1R19D70noC65o5O/5aPCc0gQn0T3VF5\n2xpCx9j+IQDBVZEiFUES7TINXUupsEy/xVPjGAa/5V4qUkqoiJf5hlhq15mL\nGmr3tfgqvN21E7kNdC1snPXiXah/kNhRX6IV7/GP6EMdBW7DGZEMo1NjKUHZ\nwsLCMw5gSRYyPrJgc/m0b7+Q3RJuPemYch/feY/Wt+Ivh/UKNYPZd3aOdFJM\no++HzQ2Da4Tzh9DrD2R1FFm1rrjLZ+fXardWSg40LX54O9mYhtSh/JQVEZVY\nn3L/ccPja4EMIZlQjHhYJsdOQTmA1fE33GE8br0qujOt1Stt4n+Fd0KT09YW\n607J3+npB6JBbRRhWjmJuKf3b93edt1A1M5O2rOE64yv5HKAKzSS6xGjb1iz\nSxQzKReVOqluVnuh37k4K4yLAGwpR0PFNSyw9t93lK5gO4JBaUabgS/IQHhm\nb9pZ\r\n=bm+H\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "188baddc1d0e6ef5140c7a788f03fc2a6c3df2ea", "scripts": {"lint": "npm run eslint -- test lib", "test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.6", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.10_1629340554316_0.5902710599713845", "host": "s3://npm-registry-packages"}}, "5.0.11": {"name": "tar", "version": "5.0.11", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@5.0.11", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "f6e972e26960f71387c88e4313b202889be09292", "tarball": "https://registry.npmjs.org/tar/-/tar-5.0.11.tgz", "fileCount": 28, "integrity": "sha512-E6q48d5y4XSCD+Xmwc0yc8lXuyDK38E0FB8N4S/drQRtXOMUhfhDxbB0xr2KKDhNfO51CFmoa6Oz00nAkWsjnA==", "signatures": [{"sig": "MEUCIQDVscZoT+IzBD7tBFettmfAzLg6oMxXcGCoYQ19Q9MTuAIgScgTzTwwSdMFi5oLy5EUZvWAYi0YJamuls7Wms9hjpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHcOgCRA9TVsSAnZWagAAg3sP/1KCM2NAZfEA8b5BADT4\neVK6zPWbd/wWtGNMXOtjtKLEmYP1Z25pZMF/paq95pbaSVvC4d3jNhDzN/qe\nnLNqL0rnL0Ju1VaFVk9WBqi0s9AzqluBQphmnFzQfbTN+GnghwQIENGKEADm\nxT882ZBrK+mnfkIJ3gE0BuwfE7OaAH4m4JiGP/h3Ro/qr6etct63JbTVMyPs\nkg/HSCoT/bmL6cypc+9zFkcM71LgzBxu9GRuVATC5gIJd/ExWsHQDNOPdLVo\nFfrVaLRclfv/LmTUC4XImkHAvqRLKArNMEzuRxZ36VemEhgDZjPA/R8yhuZb\npw8RzdJ1Z9waYiT3AMSZWfqnNqeRbN6KpJaB1GVoobNcjt8vuI0WcXM1o5Vg\nhgojtYhGvmW4eBo0qMuH31uigrAJZY+wCUPM340DNprRMdA3c2V1tT0ZH621\nSdUQ8vkVuKpkmYvAeXXJb9QGewjZCjAF1n2Y/B69loB1LRP/YthMYMEE8If9\ne+SWICK63lvDP5nt/nKuxX9CtBfTrGTWeS57OmulgrBJLJDf7ZcQWlo2L0OZ\n5BUXBqlmRed3FzxLn5PIyVkbGUh+llZVtAaidm1WTQTAq0bhhFsR9fxk2bVX\nav7NFNp1CnX2ry+gDOtX1yOKmxj58JjIsOUdvjo1jyb6vwyy7V5X9Uvm28uo\n7Xvi\r\n=yAz2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}, "gitHead": "c01354990a450c5903919efeaa9e332976e074a3", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^4.0.0", "minipass": "^3.1.3", "minizlib": "^2.1.2", "fs-minipass": "^2.1.0"}, "publishConfig": {"tag": "v5-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_5.0.11_1629340576343_0.7895363638731205", "host": "s3://npm-registry-packages"}}, "4.4.19": {"name": "tar", "version": "4.4.19", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@4.4.19", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "2e4d7263df26f2b914dee10c825ab132123742f3", "tarball": "https://registry.npmjs.org/tar/-/tar-4.4.19.tgz", "fileCount": 28, "integrity": "sha512-a20gEsvHnWe0ygBY8JbxoM4w3SJdhc7ZAuxkLqh+nvNQN2IOt0B5lLgM490X5Hl8FF0dl0tOf2ewFYAlIFgzVA==", "signatures": [{"sig": "MEUCIQCvRs+WEaa1AjWE94RtjgAwtVgghE1doUwt9aMcSvGiGQIgOVaQ/xzjgUkrEGIqtanDZUgdwr0M1LpsqtngLmY91Ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHcO+CRA9TVsSAnZWagAA3mgP/0lUg7bA/vhWFdJ8fGE7\noskwsxTyR5wisGOfrq96NKTJzDN4LStd0p5PSi/WMOieSKIl96HTNU8X6UFL\nrAjaP6BNyedj3FU+3E7oapbZQjStWHMmmMnAvybnTTcsS61lZdd1jEA20hQg\naUdSegyS2Z6wbgGS+sbmAjN/if8B8o+9R625Wat/aCfacH31HTSP3NfCjoe7\nabiucLMXjRbSuFe2GlbtgOZ4DsQVG8Tn6U0Dv5nuEmtsv5UdpjGC4ZJjfmti\nqM9Efvvy0zWtoDwnmMqh572xheznBJIeVr9y+WTnZTSa/vg6VUA+Ayt0G31H\n+LnrV++mJaWHMFU+ABlxqdgsXOKVPPrPwBBU4cCajPuUalS/w5e4PlXE3T1C\nr2isboXWyzyntygqVMXtuKev2q7NgNdTMIpWofEJG+NuQifiw+6dEb8SB4If\nzYzlNI0L4DewGXJnKY55TrwDBifFdyFUhEZTmIQa7FkIji0iP5Zih/qCLpEk\nFMxMqM2ArVislcd4ATWkf8kz3Qb1RZxzClTajVmJG/yH9E5DrSx1q7N5tqO/\nGhXGEBD76SfroytAEM3FGVCPYsCnV4M/wFr6y8krG2/01+mCp5PPjX7OEZEV\nxv3ARq8HxEeOYxIathqVZuC6HhEzQzVTtPRb8x1GQC8jr9bGhkY697j1VOMv\nCpPa\r\n=G1o2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.5"}, "gitHead": "9a6faa017ca90538840f3ae2ccdb4550ac3f4dcf", "scripts": {"test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "tar for node", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"chownr": "^1.1.4", "mkdirp": "^0.5.5", "yallist": "^3.1.1", "minipass": "^2.9.0", "minizlib": "^1.3.3", "fs-minipass": "^1.2.7", "safe-buffer": "^5.2.1"}, "publishConfig": {"tag": "v4-legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.11.0", "chmodr": "^1.2.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.4", "require-inject": "^1.4.4", "events-to-array": "^1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/tar_4.4.19_1629340605951_0.7949247502919905", "host": "s3://npm-registry-packages"}}, "6.1.11": {"name": "tar", "version": "6.1.11", "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "tar@6.1.11", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "6760a38f003afa1b2ffd0ffe9e9abbd0eab3d621", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.11.tgz", "fileCount": 29, "integrity": "sha512-an/KZQzQUkZCkuoAA64hM92X0Urb6VpRhAFllDzz44U2mcD5scmT3zBc4VgVpkugF580+DQn8eAFSyoQt0tznA==", "signatures": [{"sig": "MEYCIQDTCxGgpnD7yKB8QG8XoAkBqyOGEQYaQbAL1Y3LkcIIhgIhAIei9kPMsA5njuscuDHdoxbYM65rzJOjCSN4hmaCglI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJ75NCRA9TVsSAnZWagAA4J8QAIlhw88gXI9mAI1j3RZD\n5/wkSdtwfndUlO+f9xb7aaZTrcztXM5gof2mrnq+BhN4sJUFulxoLWhVcwNp\nGNYAh5RRfRkzpOBAlnvxdYumd7puIsnIBIt/FrNNrDSok2iDYMlOu/t8BEHE\n2oARDdjieLtTMjoFQt7fnJ1Aim4yEuZRv4OPA6G68oUJl3fOUM8qLhXMXHL6\naIOz+njpfBUcZ5tp6nTLt5kD+NJKI34NSAJ0VDSQ+kX00goZB9AHiQrTRb/d\nTbue3dwwCq4IWo4KuThdAG9J4CTNcwnUr+4L4yQZG1epuPgZcWfjyvW05l6X\nn/gNPgsTgCmaK8/3SzUSZ7meXEMXiev75Jeuope4GDnM9sLsXRL9BWIVR4xJ\niuxrwNwupSDhsUFvklm8y+yjJRnlbcq/h3peMtrREUVeb/kJVLpnQ8ruWL0d\nziNhPa81TecohqlKla6ZdenSCvKenk0CopDde4CcA0xO6eze62lM+FkSI9y8\n5JgCy0h2EdryYMaZcM2QPVFjXtzzxFEE23Mw4Npmez6L4XVwarCLfSFN8ZTc\n/8XFXlaJdHTjei64v9AI9Hlsi4OGETCpklPXaieTXKimgi1stGgII3xLp/8J\n9cQ0QPjIkSPET5wxPEEng6vsPYzZx4/3iVEy4x3VTj4372qOdzWyopAbAJ/m\nam/9\r\n=fPEP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "gitHead": "e573aeea19d4d650908b7f6bf0a1ad8dce9f1736", "scripts": {"lint": "npm run eslint -- test lib", "test": "node test/fixtures/test.js", "bench": "for i in benchmarks/*/*.js; do echo $i; for j in {1..5}; do node $i || break; done; done", "eslint": "eslint", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "posttest": "npm run lint", "preversion": "npm test", "test:posix": "tap", "test:win32": "tap --lines=98 --branches=98 --statements=98 --functions=98", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "7.20.6", "description": "tar for node", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.9", "chmodr": "^1.2.0", "eslint": "^7.17.0", "rimraf": "^2.7.1", "tar-fs": "^1.16.3", "mutate-fs": "^2.1.1", "tar-stream": "^1.6.2", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.11_1629994573123_0.2986815842736079", "host": "s3://npm-registry-packages"}}, "6.1.12": {"name": "tar", "version": "6.1.12", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@6.1.12", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "3b742fb05669b55671fb769ab67a7791ea1a62e6", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.12.tgz", "fileCount": 29, "integrity": "sha512-jU4TdemS31uABHd+Lt5WEYJuzn+TJTCBLljvIAHZOz6M9Os5pJ4dD+vRFLxPa/n3T0iEFzpi+0x1UfuDZYbRMw==", "signatures": [{"sig": "MEYCIQCdimIooOADDZSMMowWnrIHjXXdEV24e0mwwZ3vBMtp4gIhAPAQz0afQ4sUV9Vau+DC0Sd7nlJ+J2cyp6NFgAVo2pmY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYUpIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEGw//cIKPUslx+XEb4iw1FsC1c1WJM7qc2sPfcPTaZ4C/OxoDmuxe\r\nQkuVXQ9if5c/xqkNSVzUodKwa4kXAxhMxZcCIpv9BC0//BDDiyb0q93ifMM0\r\nmaKO8qyNEzyOZy+v/nGjG+e7k+GwfKGgbe4G+hSFQV48Y3ekgo7JctAi+6xG\r\nvsJz9bLYg/WCS+XEaUGP29sQ5N0fl9JFwCFgjynq5c5O4R6xP+fsqghSyaRx\r\ncfJqxFp9upbeuwpqhfhZFSchfJnWFamMs0DZKXKQcVGr3tqCIrkKEsEQ09t7\r\nnfi3BXXYTtDQ+Tz4cg8M/V+DXQl8f1aWOLbK/GCSo/oInKiJF3w2CBSyDPNb\r\nUZ5vWqday0XUlM8KJkcXsG+BuwPhMUlsIgO7P6NbWT/hc4IiXFEXIEhXhFBH\r\nwRSRozYrpYU7R6ICBNJQzmMVA6J1qeiTrSchvSyQykiLjAAxoGw9tOLdMzjl\r\nBp5vz3bIecInd5M/P9yb2oXeGBPPqnmqJw3iSJ+Jo7VQI3ruBlgZNFew0w5j\r\niQXcitJl8V1eOXQx+2oF5v89cNFVMsdXmfHwba1b/QkxLB1L1Cp8j9dP4Q8o\r\nYwn9aLILJiZPdqNf61Ge20r7l2Kh9Lch+OtlFEmTR53uTL/wuWFAkjNHQqtE\r\na//Fcr+OeGtapcsjpBYGQ6p9aoHjxVEazzE=\r\n=yo/O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "bec36c741c5a29aa925acd6034a7b27101212716", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "9.0.1", "description": "tar for node", "directories": {}, "templateOSS": {"content": "scripts/template-oss", "engines": ">=10", "version": "4.8.0", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.12.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^3.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.9", "chmodr": "^1.2.0", "rimraf": "^3.0.2", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^1.1.2", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.12_1667320392062_0.6486373287121292", "host": "s3://npm-registry-packages"}}, "6.1.13": {"name": "tar", "version": "6.1.13", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@6.1.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-tar#readme", "bugs": {"url": "https://github.com/npm/node-tar/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "46e22529000f612180601a6fe0680e7da508847b", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "fileCount": 29, "integrity": "sha512-jdIBIN6LTIe2jqzay/2vtYLlBHa3JF42ot3h1dW8Q0PaAG4v8rm0cvpVePtau5C6OKXGGcgO9q2AMNSWxiLqKw==", "signatures": [{"sig": "MEUCIQCsY3hlrt7Ss13tFx/l1Z6E8DqBeKw78Ps0gBhBmy3ZEAIgOjNXw6kcinDoe0N9/gHzQ5AUa7mXPdnjT/WJGh95uW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkPhnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0Yg/9F9ntQ5GfP4trWX2QRIwPLnyeWW6JC61KEOaVTH0e6deT8170\r\nf9PpoxhimSmCNrJS8ISCntxz6zTnWOhRuQw7KAZBI3FZgUIU6P2/ykthgeqc\r\nNslXEr4j9HL0MltPGO/mXqFJoBCEMxSYwTBOuHtwxu4uN2JnTaG+2eKwsLgH\r\ntmawtLVgVoz/uVK+z4FRLg8K/7htkQ2vbdim0mnRs2e+W61qzyI7OdnStbC2\r\n1VIKwr2Z24A3WCLHKBfbLINxWShL2e9alDKEcGIt3OdM+OfK1bV6ZCWHrSNO\r\nkl99IKzT9TStPvCAQUi0sNf7+OI/kCyYLt6vPGlzeUW+UaAW+xDjHCTD5SYU\r\nJWEBWw2I83yYd8/b1kqcxqm9Og8wgCORyzsVk310g0vr4ZBzUv/0BrRdBGHX\r\nzklW23cc0nMrfWSaeJBgxg8txM+024U+aKvijrYd9pXp1DIGSD1q38kaH158\r\n/bIvAgwrtHmpy//V/qAHvz79FZbP+qSDRllrnsneMj9w40srK6rf42iCcuAP\r\nHpTAeLT9Zo/b8TPR8hJ3Onn0sV+rvvn7+8oaM6jjZ5eS6UTX1mBtn71q0FLB\r\nplYCoeL+t9v8V6ziypEZ6OjFCzUXXbHo7Zef+Y/wnP6vX3Ymcl1H5gpdPNMx\r\n1cCUqXbqd4903y0WUq8gYdpKzXhfz8Y5Acc=\r\n=H8T8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "a044a87c6c7fb3ace4ea9bf903c63f0f15965398", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-tar.git", "type": "git"}, "_npmVersion": "9.1.1", "description": "tar for node", "directories": {}, "templateOSS": {"content": "scripts/template-oss", "engines": ">=10", "version": "4.10.0", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.12.1", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^4.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.9", "chmodr": "^1.2.0", "rimraf": "^3.0.2", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "@npmcli/template-oss": "4.10.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.13_1670445159418_0.7871782761036956", "host": "s3://npm-registry-packages"}}, "6.1.14": {"name": "tar", "version": "6.1.14", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@6.1.14", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "e87926bec1cfe7c9e783a77a79f3e81c1cfa3b66", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.14.tgz", "fileCount": 29, "integrity": "sha512-piERznXu0U7/pW7cdSn7hjqySIVTYT6F76icmFk7ptU7dDYlXTm5r9A6K04R2vU3olYgoKeo1Cg3eeu5nhftAw==", "signatures": [{"sig": "MEYCIQClA/VrAjP6HiEv37th/ycIMApyaE4wCUPQt8fKjLsvLwIhAOD5wKnsE59xGICpWS0AC9+5Eu1uvBJzB1nmukESoP0P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUZKvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbERAAoYlEpd7xCq41CemT28lLpyMT+FweYCIAmZWPba+JJgvwSrp7\r\n/y+luD07DhGpKiGfwcHvVIuGZcKugWnPvH04dTi/H7GK2GqxOVQDtAQHDp3L\r\no6CMmT9F4VWxsL4H0UTsbLnuyCa0I9o5PkRjOpPNo28F+4zvlDkEC7BkFsOM\r\n2yi5E/bc1BxfukmKaUO+Az+RvxDLx0aK/nk8iU2o6TtKx6nWTedINzRsiHyp\r\nbrAJy1MNpZoBuLDdnWR3j8Hv7TpAjJOBGg20aOAXFNgVaPKFZcIQHFAzJz0k\r\nNnJyqaw/yN7qV6jFOwZsncXXNpAgaWtTuaNJf153iKhEnLTpR23YlHet7+ZN\r\nbHbyVnkzOlH6NFxKsNEKPvJS33dPGOgljnMCROq5KoxyZ7ZgN/oKLalpAlzr\r\nQo2XWmh0Z/6YYAkviL3rJ31QMcbvfqjs2SLq5o6f/UdNaJY39DGZ9QqPOejx\r\nogp23CXIAXyWplehG+mvvNaDA687WQ2KVAlVtfLrSAbQNlErV76mFzuuj4MR\r\nmZ36hF/G3sxvgT6+35Sf7NViLhnltoUhYhRyiSbaT4dzu5ytj2uPnKASFs54\r\nipt/qoeVf81VV+/CBf5eHHJgwPfI24AeMCOA2rFXn8dHsfFHeaVVZ+0AtYzG\r\nS79tgwkCwgFBRT9I9FV6zKPY894DnirjV80=\r\n=lEWu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "4aaffc862f4e991f7965ecf6527072c4423ecb49", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "tar for node", "directories": {}, "templateOSS": {"content": "scripts/template-oss", "engines": ">=10", "version": "4.11.0", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.9", "chmodr": "^1.2.0", "rimraf": "^3.0.2", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.14_1683067567126_0.8102772200579933", "host": "s3://npm-registry-packages"}}, "6.1.15": {"name": "tar", "version": "6.1.15", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@6.1.15", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "c9738b0b98845a3b344d334b8fa3041aaba53a69", "tarball": "https://registry.npmjs.org/tar/-/tar-6.1.15.tgz", "fileCount": 29, "integrity": "sha512-/zKt9UyngnxIT/EAGYuxaMYgOIJiP81ab9ZfkILq4oNLPFX50qyYmu7jRj9qeXoxmJHjGlbH0+cm2uy1WCs10A==", "signatures": [{"sig": "MEUCIQCDwemW47JqdR340tK1p+sJOkh/2yQkHj5OUeZxwy9kfQIgVJYqRwjhbRRgnEvQEXwkTLalC4xCwMmLPg4OTK7OfJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163655}, "engines": {"node": ">=10"}, "gitHead": "3302cf7330052982ad7d7e9f85e823fa1bb945a4", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "genparse": "node scripts/generate-parse-fixtures.js", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "tar for node", "directories": {}, "templateOSS": {"content": "scripts/template-oss", "engines": ">=10", "version": "4.11.0", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.9", "chmodr": "^1.2.0", "rimraf": "^3.0.2", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.1.15_1684301931697_0.9208356227037398", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "tar", "version": "6.2.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@6.2.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "b14ce49a79cb1cd23bc9b016302dea5474493f73", "tarball": "https://registry.npmjs.org/tar/-/tar-6.2.0.tgz", "fileCount": 29, "integrity": "sha512-/Wo7DcT0u5HUV486xg675HtjNd3BXZ6xDbzsCUZPt5iw8bTQ63bP0Raut3mvro9u+CUyq7YQd8Cx55fsZXxqLQ==", "signatures": [{"sig": "MEQCIHHmm6S/j2Mzqz02plQEPKVkvucfDCfwOYNZCjRSM93ZAiBV5b4KEkjVREUu8MMsQTduB6sRnxbuUEQKdRULw4bGlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165629}, "engines": {"node": ">=10"}, "gitHead": "5bc9d404e88c39870e0fbb55655a53de6fbf0a04", "scripts": {"snap": "tap", "test": "tap", "genparse": "node scripts/generate-parse-fixtures.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "tar for node", "directories": {}, "templateOSS": {"content": "scripts/template-oss", "engines": ">=10", "version": "4.11.0", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.9", "chmodr": "^1.2.0", "rimraf": "^3.0.2", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.2.0_1693892029140_0.06913049104751678", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "tar", "version": "6.2.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@6.2.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "717549c541bc3c2af15751bea94b1dd068d4b03a", "tarball": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "fileCount": 29, "integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "signatures": [{"sig": "MEQCICiN57b6g7ZWCQN3sRJ+KpQedOYvNEmp0rJiwyGiT6ilAiA92/r4ohXAxTc9ATTxiPbhsnqxFx5d2WFp4qzPVMI+cA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166613}, "engines": {"node": ">=10"}, "gitHead": "c65f76d89a69a4c0d2a1e3ab97c289ce965f6476", "scripts": {"snap": "tap", "test": "tap", "genparse": "node scripts/generate-parse-fixtures.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "tar for node", "directories": {}, "templateOSS": {"content": "scripts/template-oss", "engines": ">=10", "version": "4.11.0", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.11.0", "dependencies": {"chownr": "^2.0.0", "mkdirp": "^1.0.3", "yallist": "^4.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "fs-minipass": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "nock": "^13.2.9", "chmodr": "^1.2.0", "rimraf": "^3.0.2", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar_6.2.1_1711055584589_0.44397657073835783", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "tar", "version": "7.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.0.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "3ee87a8552666422773acecc8a99ab6ab92539a4", "tarball": "https://registry.npmjs.org/tar/-/tar-7.0.0.tgz", "fileCount": 229, "integrity": "sha512-spRiR+tDOVD01YeeWBUbNa6HoQErjztT2BXxZWmxJDgaCVgZMO1RAoeKpybiUbr8FxKsUm/svtiEyIRZeWYhAw==", "signatures": [{"sig": "MEUCIQDfKQHQwKOt6UgVXzRChr8izyWFR5a/rO1PXwmk9kxrXQIga9Bzhpom5my2rv7N+S0cLNyiiJuq3XhVUj7WuRb9YAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1191469}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "b0fbdea4631f9f65f15786fb5333695a9164a536", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "tar for node", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^5.0.0", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.0.0_1712779643755_0.7922138040170696", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "tar", "version": "7.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "coverage-map": "map.js"}, "dist": {"shasum": "8f6ccebcd91b69e9767a6fc4892799e8b0e606d5", "tarball": "https://registry.npmjs.org/tar/-/tar-7.0.1.tgz", "fileCount": 229, "integrity": "sha512-IjMhdQMZFpKsHEQT3woZVxBtCQY+0wk3CVxdRkGXEgyGa0dNS/ehPvOMr2nmfC7x5Zj2N+l6yZUpmICjLGS35w==", "signatures": [{"sig": "MEQCIARdoweD8Qlt+yskRd9B9NnWnOqYImwEv1zDc7G6WtOAAiBn8hoLaCxMzAhR8lBkYgSI36BqgpQW5xwLzIZ/DM4muw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1192165}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "d99fce38ebf5175cce4c6623c53f4b17d6d31157", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --loglevel warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "tar for node", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^5.0.0", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.0.1_1713131124986_0.48876574112094695", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "tar", "version": "7.1.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "c6d4ec5b10ccdffe8bc412b206eaeaf5181f3098", "tarball": "https://registry.npmjs.org/tar/-/tar-7.1.0.tgz", "fileCount": 229, "integrity": "sha512-ENhg4W6BmjYxl8GTaE7/h99f0aXiSWv4kikRZ9n2/JRxypZniE84ILZqimAhxxX7Zb8Px6pFdheW3EeHfhnXQQ==", "signatures": [{"sig": "MEQCIASesW9W2ouMl20pvVPdBWefdVjBs9mHRQ5mfm3Lo2ZXAiA21X5XEnQCvrbHp7S7hsbUuOE8+F3RvxZhkm01QAmRJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1213036}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "ce612d0aa818f93a133a5f76e14fe6eef2abb12f", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.0", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.1.0_1714788420470_0.24385213168751663", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "tar", "version": "7.2.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "f03ae6ecd2e2bab880f2ef33450f502e761d7548", "tarball": "https://registry.npmjs.org/tar/-/tar-7.2.0.tgz", "fileCount": 237, "integrity": "sha512-hctwP0Nb4AB60bj8WQgRYaMOuJYRAPMGiQUAotms5igN8ppfQM+IvjQ5HcKu1MaZh2Wy2KWVTe563Yj8dfc14w==", "signatures": [{"sig": "MEUCIBqNWyIOz+B2I7bOm+cyCp9jshGC/JFYPf5F7EiS0EycAiEA9SdyEF1Y95/Lpc+dSTsVHHQd0FF6o14h1WPpnuR3IfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1228514}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "ed17f588f202c598bf74d9cdcda0998e706e86a1", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.0", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.2.0_1717032544676_0.31685659408416456", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "tar", "version": "7.3.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "c6f1944f990d2f5821b26eb0ddaba7fd5ea25425", "tarball": "https://registry.npmjs.org/tar/-/tar-7.3.0.tgz", "fileCount": 237, "integrity": "sha512-ihockMgWX0hnTa1eJf/usF91BXd2R5CwOUiWgAgdcd82Pv12ByK8S9obWnS5NTUrdadg3EpPUlowNqT+4GTE6A==", "signatures": [{"sig": "MEUCIQCJd/KmP2iswaHDvUFpxL9aWbZszsTmUrSfKpU4SPeLAAIgFOy1B/ambxrpCOqvoQbOr83OBRltjfVqGLUwFyKb9g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233620}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "source": "./src/types.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "source": "./src/types.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "source": "./src/header.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "source": "./src/header.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "556a13c7c6733cab8baf29f8d382a3bef3f6fb07", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.3.0_1718764011606_0.3710217112313847", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "tar", "version": "7.4.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "tar@7.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "e513afef5ba20ce250fd99397b4599db07d06443", "tarball": "https://registry.npmjs.org/tar/-/tar-7.4.0.tgz", "fileCount": 237, "integrity": "sha512-XQs0S8fuAkQWuqhDeCdMlJXDX80D7EOVLDPVFkna9yQfzS+PHKgfxcei0jf6/+QAWcjqrnC8uM3fSAnrQl+XYg==", "signatures": [{"sig": "MEQCIFg/URgoseRlqvYMJzLZ4Ecr9ND9t1gmeaBlYAmAT0iLAiBRHebnlOXn1J/rQOXSQ8SiLmoEp25liihStrca0HPuEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1234297}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "source": "./src/types.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "source": "./src/types.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "source": "./src/header.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "source": "./src/header.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "02b26879b880df80271cfb1ac98b7153becf9fb0", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.4.0_1718764622117_0.6934545195773854", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "tar", "version": "7.4.1", "author": {"name": "<PERSON>"}, "license": "ISC", "_id": "tar@7.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "e74de34641cc0cb6a1d6a200085a3fa55de64843", "tarball": "https://registry.npmjs.org/tar/-/tar-7.4.1.tgz", "fileCount": 237, "integrity": "sha512-dDJzpQf7Nud96mCs3wtw+XUiWGpi9WHxytSusrg0lYlj/Kr11DnB5hfw5bNDQNzx52JJ2Vy+7l8AFivp6H7ETA==", "signatures": [{"sig": "MEYCIQCPUQf0j5JmuTnL9yEhg7H4enmdqEuwhT3FBKgUBdN6ugIhAPEPZt8O1ZZKpMxXgvviAT2vLN+5Kb70hb6ibLTH4Ioq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1234420}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "source": "./src/types.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "source": "./src/types.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "source": "./src/header.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "source": "./src/header.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "f1d7a4d39bba883cbe31687a1497a728876479e7", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.4.1_1721664159522_0.5712520713329583", "host": "s3://npm-registry-packages"}}, "7.4.2": {"name": "tar", "version": "7.4.2", "author": {"name": "<PERSON>"}, "license": "ISC", "_id": "tar@7.4.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-tar#readme", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "tap": {"timeout": 0, "typecheck": true, "coverage-map": "map.js"}, "dist": {"shasum": "244859617851fc6dd5413c67b195585d25a7a2f4", "tarball": "https://registry.npmjs.org/tar/-/tar-7.4.2.tgz", "fileCount": 237, "integrity": "sha512-pP4ToLATHpXOrbxOZudW6rnfyPWJZoyERNrpTpQEtW6LkvpXw+WDnpj2GBQX9tyQ6teUyPESyyiOCFgp6HnGdw==", "signatures": [{"sig": "MEUCIQCl4TEQVcMGdK3nHkLjudipEVHj4A/Jdtm1VDBhFeq0ogIgLwEsXuOcKpNnujI8iZNkBpi2/o9q5gt2qEc5NnsfzBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1237853}, "main": "./dist/commonjs/index.js", "tshy": {"exports": {".": "./src/index.ts", "./c": "./src/create.ts", "./r": "./src/create.ts", "./t": "./src/list.ts", "./u": "./src/update.ts", "./x": "./src/extract.ts", "./pax": "./src/pax.ts", "./list": "./src/list.ts", "./pack": "./src/pack.ts", "./parse": "./src/parse.ts", "./types": "./src/types.ts", "./create": "./src/create.ts", "./header": "./src/header.ts", "./unpack": "./src/unpack.ts", "./update": "./src/update.ts", "./extract": "./src/extract.ts", "./replace": "./src/create.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./package.json": "./package.json"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "source": "./src/index.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "source": "./src/index.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./t": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./u": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./x": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./pax": {"import": {"types": "./dist/esm/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/esm/pax.js"}, "require": {"types": "./dist/commonjs/pax.d.ts", "source": "./src/pax.ts", "default": "./dist/commonjs/pax.js"}}, "./list": {"import": {"types": "./dist/esm/list.d.ts", "source": "./src/list.ts", "default": "./dist/esm/list.js"}, "require": {"types": "./dist/commonjs/list.d.ts", "source": "./src/list.ts", "default": "./dist/commonjs/list.js"}}, "./pack": {"import": {"types": "./dist/esm/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/esm/pack.js"}, "require": {"types": "./dist/commonjs/pack.d.ts", "source": "./src/pack.ts", "default": "./dist/commonjs/pack.js"}}, "./parse": {"import": {"types": "./dist/esm/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/esm/parse.js"}, "require": {"types": "./dist/commonjs/parse.d.ts", "source": "./src/parse.ts", "default": "./dist/commonjs/parse.js"}}, "./types": {"import": {"types": "./dist/esm/types.d.ts", "source": "./src/types.ts", "default": "./dist/esm/types.js"}, "require": {"types": "./dist/commonjs/types.d.ts", "source": "./src/types.ts", "default": "./dist/commonjs/types.js"}}, "./create": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./header": {"import": {"types": "./dist/esm/header.d.ts", "source": "./src/header.ts", "default": "./dist/esm/header.js"}, "require": {"types": "./dist/commonjs/header.d.ts", "source": "./src/header.ts", "default": "./dist/commonjs/header.js"}}, "./unpack": {"import": {"types": "./dist/esm/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/esm/unpack.js"}, "require": {"types": "./dist/commonjs/unpack.d.ts", "source": "./src/unpack.ts", "default": "./dist/commonjs/unpack.js"}}, "./update": {"import": {"types": "./dist/esm/update.d.ts", "source": "./src/update.ts", "default": "./dist/esm/update.js"}, "require": {"types": "./dist/commonjs/update.d.ts", "source": "./src/update.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"types": "./dist/esm/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/esm/extract.js"}, "require": {"types": "./dist/commonjs/extract.d.ts", "source": "./src/extract.ts", "default": "./dist/commonjs/extract.js"}}, "./replace": {"import": {"types": "./dist/esm/create.d.ts", "source": "./src/create.ts", "default": "./dist/esm/create.js"}, "require": {"types": "./dist/commonjs/create.d.ts", "source": "./src/create.ts", "default": "./dist/commonjs/create.js"}}, "./read-entry": {"import": {"types": "./dist/esm/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/esm/read-entry.js"}, "require": {"types": "./dist/commonjs/read-entry.d.ts", "source": "./src/read-entry.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"types": "./dist/esm/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/esm/write-entry.js"}, "require": {"types": "./dist/commonjs/write-entry.d.ts", "source": "./src/write-entry.ts", "default": "./dist/commonjs/write-entry.js"}}, "./package.json": "./package.json"}, "gitHead": "68a685b30eadddad71cec56bc136bd276fa7e7f6", "scripts": {"snap": "tap", "test": "tap", "format": "prettier --write . --log-level warn", "prepare": "tshy", "presnap": "npm run prepare", "pretest": "npm run prepare", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts", "genparse": "node scripts/generate-parse-fixtures.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "prettier": {"semi": false, "useTabs": false, "tabWidth": 2, "endOfLine": "lf", "printWidth": 70, "arrowParens": "avoid", "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "experimentalTernaries": true}, "repository": {"url": "git+https://github.com/isaacs/node-tar.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "tar for node", "directories": {}, "_nodeVersion": "20.13.1", "dependencies": {"chownr": "^3.0.0", "mkdirp": "^3.0.1", "yallist": "^5.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "@isaacs/fs-minipass": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^18.7.2", "nock": "^13.5.4", "tshy": "^1.13.1", "chmodr": "^1.2.0", "rimraf": "^5.0.5", "typedoc": "^0.25.13", "prettier": "^3.2.5", "mutate-fs": "^2.1.1", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/tar_7.4.2_1721865482015_0.8905065251417372", "host": "s3://npm-registry-packages"}}, "7.4.3": {"author": {"name": "<PERSON>"}, "name": "tar", "description": "tar for node", "version": "7.4.3", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap", "pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "tshy", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --log-level warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.0.1", "mkdirp": "^3.0.1", "yallist": "^5.0.0"}, "devDependencies": {"chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.5.4", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "license": "ISC", "engines": {"node": ">=18"}, "tap": {"coverage-map": "map.js", "timeout": 0, "typecheck": true}, "prettier": {"experimentalTernaries": true, "semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./c": "./src/create.ts", "./create": "./src/create.ts", "./replace": "./src/create.ts", "./r": "./src/create.ts", "./list": "./src/list.ts", "./t": "./src/list.ts", "./update": "./src/update.ts", "./u": "./src/update.ts", "./extract": "./src/extract.ts", "./x": "./src/extract.ts", "./pack": "./src/pack.ts", "./unpack": "./src/unpack.ts", "./parse": "./src/parse.ts", "./read-entry": "./src/read-entry.ts", "./write-entry": "./src/write-entry.ts", "./header": "./src/header.ts", "./pax": "./src/pax.ts", "./types": "./src/types.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"source": "./src/index.ts", "types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"source": "./src/index.ts", "types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./c": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./create": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./replace": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./r": {"import": {"source": "./src/create.ts", "types": "./dist/esm/create.d.ts", "default": "./dist/esm/create.js"}, "require": {"source": "./src/create.ts", "types": "./dist/commonjs/create.d.ts", "default": "./dist/commonjs/create.js"}}, "./list": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./t": {"import": {"source": "./src/list.ts", "types": "./dist/esm/list.d.ts", "default": "./dist/esm/list.js"}, "require": {"source": "./src/list.ts", "types": "./dist/commonjs/list.d.ts", "default": "./dist/commonjs/list.js"}}, "./update": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./u": {"import": {"source": "./src/update.ts", "types": "./dist/esm/update.d.ts", "default": "./dist/esm/update.js"}, "require": {"source": "./src/update.ts", "types": "./dist/commonjs/update.d.ts", "default": "./dist/commonjs/update.js"}}, "./extract": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./x": {"import": {"source": "./src/extract.ts", "types": "./dist/esm/extract.d.ts", "default": "./dist/esm/extract.js"}, "require": {"source": "./src/extract.ts", "types": "./dist/commonjs/extract.d.ts", "default": "./dist/commonjs/extract.js"}}, "./pack": {"import": {"source": "./src/pack.ts", "types": "./dist/esm/pack.d.ts", "default": "./dist/esm/pack.js"}, "require": {"source": "./src/pack.ts", "types": "./dist/commonjs/pack.d.ts", "default": "./dist/commonjs/pack.js"}}, "./unpack": {"import": {"source": "./src/unpack.ts", "types": "./dist/esm/unpack.d.ts", "default": "./dist/esm/unpack.js"}, "require": {"source": "./src/unpack.ts", "types": "./dist/commonjs/unpack.d.ts", "default": "./dist/commonjs/unpack.js"}}, "./parse": {"import": {"source": "./src/parse.ts", "types": "./dist/esm/parse.d.ts", "default": "./dist/esm/parse.js"}, "require": {"source": "./src/parse.ts", "types": "./dist/commonjs/parse.d.ts", "default": "./dist/commonjs/parse.js"}}, "./read-entry": {"import": {"source": "./src/read-entry.ts", "types": "./dist/esm/read-entry.d.ts", "default": "./dist/esm/read-entry.js"}, "require": {"source": "./src/read-entry.ts", "types": "./dist/commonjs/read-entry.d.ts", "default": "./dist/commonjs/read-entry.js"}}, "./write-entry": {"import": {"source": "./src/write-entry.ts", "types": "./dist/esm/write-entry.d.ts", "default": "./dist/esm/write-entry.js"}, "require": {"source": "./src/write-entry.ts", "types": "./dist/commonjs/write-entry.d.ts", "default": "./dist/commonjs/write-entry.js"}}, "./header": {"import": {"source": "./src/header.ts", "types": "./dist/esm/header.d.ts", "default": "./dist/esm/header.js"}, "require": {"source": "./src/header.ts", "types": "./dist/commonjs/header.d.ts", "default": "./dist/commonjs/header.js"}}, "./pax": {"import": {"source": "./src/pax.ts", "types": "./dist/esm/pax.d.ts", "default": "./dist/esm/pax.js"}, "require": {"source": "./src/pax.ts", "types": "./dist/commonjs/pax.d.ts", "default": "./dist/commonjs/pax.js"}}, "./types": {"import": {"source": "./src/types.ts", "types": "./dist/esm/types.d.ts", "default": "./dist/esm/types.js"}, "require": {"source": "./src/types.ts", "types": "./dist/commonjs/types.d.ts", "default": "./dist/commonjs/types.js"}}}, "type": "module", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "_id": "tar@7.4.3", "gitHead": "206fcf91b01fae95ae859b8f3254bfd88744602a", "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "homepage": "https://github.com/isaacs/node-tar#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==", "shasum": "88bbe9286a3fcd900e94592cda7a22b192e80571", "tarball": "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz", "fileCount": 237, "unpackedSize": 1239499, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDasJ6vLVVHfYJ+aYAkE/DlmPOe1m64zpGNEJCOm87+MAIgb0C7vdQ0KmAMLD7+EOL6LQKsYaNghtIxC6wTE+nr7iQ="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/tar_7.4.3_1721968425683_0.9899170882636155"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-11-20T07:11:18.109Z", "modified": "2024-07-26T04:33:46.189Z", "0.0.1": "2011-11-20T07:11:18.109Z", "0.1.0": "2011-11-20T07:52:36.525Z", "0.1.2": "2011-11-21T22:31:19.762Z", "0.1.3": "2011-11-23T00:44:56.703Z", "0.1.4": "2011-11-29T03:02:22.216Z", "0.1.5": "2011-11-30T18:54:34.219Z", "0.1.6": "2011-11-30T20:50:10.803Z", "0.1.7": "2011-12-01T03:12:57.060Z", "0.1.8": "2011-12-03T02:29:20.608Z", "0.1.9": "2011-12-09T02:01:45.330Z", "0.1.10": "2012-01-05T00:46:43.197Z", "0.1.11": "2012-01-06T01:47:30.560Z", "0.1.12": "2012-01-13T18:05:48.499Z", "0.1.13": "2012-03-13T00:07:39.934Z", "0.1.14": "2012-12-03T04:12:29.063Z", "0.1.15": "2013-02-02T02:38:57.779Z", "0.1.16": "2013-02-02T19:18:49.559Z", "0.1.17": "2013-03-20T06:23:09.563Z", "0.1.18": "2013-07-24T03:35:17.606Z", "0.1.19": "2013-12-09T17:26:25.554Z", "0.1.20": "2014-06-24T20:51:47.700Z", "1.0.0": "2014-07-31T22:28:36.607Z", "1.0.1": "2014-08-19T19:26:19.807Z", "1.0.2": "2014-10-28T06:50:17.116Z", "1.0.3": "2014-11-28T01:50:25.407Z", "2.0.0": "2015-03-27T01:45:53.078Z", "2.0.1": "2015-04-09T00:56:16.262Z", "2.1.0": "2015-04-17T07:03:07.838Z", "2.1.1": "2015-05-08T00:38:16.661Z", "2.2.0": "2015-08-27T21:01:29.846Z", "2.2.1": "2015-09-10T01:49:38.319Z", "3.0.0": "2017-05-10T01:24:27.621Z", "3.0.1": "2017-05-10T17:55:58.326Z", "3.1.0": "2017-05-12T16:43:21.251Z", "3.1.1": "2017-05-12T17:02:16.466Z", "3.1.2": "2017-05-12T22:54:32.029Z", "3.1.3": "2017-05-16T00:56:04.961Z", "3.1.4": "2017-06-02T23:41:13.115Z", "3.1.5": "2017-06-03T00:16:58.694Z", "3.1.6": "2017-07-25T23:03:18.524Z", "3.1.7": "2017-07-26T18:03:06.054Z", "3.1.8": "2017-08-01T22:53:09.529Z", "3.1.9": "2017-08-02T22:48:31.039Z", "3.1.10": "2017-08-15T07:42:43.039Z", "3.1.11": "2017-08-15T16:34:05.049Z", "3.1.12": "2017-08-15T23:45:32.046Z", "3.1.13": "2017-08-16T00:32:03.399Z", "3.1.14": "2017-08-16T23:30:17.615Z", "3.1.15": "2017-08-17T00:50:34.313Z", "3.2.0": "2017-08-17T00:55:49.757Z", "4.0.0": "2017-08-18T22:24:34.231Z", "4.0.1": "2017-08-21T01:10:23.741Z", "3.2.1": "2017-08-21T01:12:32.637Z", "4.0.2": "2017-10-18T19:30:06.418Z", "4.1.0": "2017-11-28T01:07:37.579Z", "4.1.1": "2017-11-28T19:40:50.614Z", "4.1.2": "2017-12-21T03:17:05.518Z", "4.2.0": "2017-12-21T04:27:39.029Z", "4.3.0": "2018-01-18T01:17:05.740Z", "4.3.1": "2018-02-01T04:22:44.860Z", "4.3.2": "2018-02-01T16:36:44.943Z", "4.3.3": "2018-02-06T20:18:46.562Z", "4.4.0": "2018-02-20T19:36:59.186Z", "4.4.1": "2018-03-20T16:59:58.112Z", "4.4.2": "2018-04-30T18:57:47.535Z", "4.4.3": "2018-05-23T23:55:05.909Z", "4.4.4": "2018-05-25T00:58:46.640Z", "4.4.5": "2018-08-02T00:14:08.433Z", "4.4.6": "2018-08-02T00:15:20.037Z", "4.4.7": "2018-11-05T21:14:05.702Z", "4.4.8": "2018-11-08T23:53:21.416Z", "2.2.2": "2019-05-15T00:44:22.633Z", "4.4.9": "2019-06-01T04:48:39.791Z", "4.4.10": "2019-06-04T19:55:16.494Z", "4.4.11": "2019-09-17T17:43:11.530Z", "4.4.12": "2019-09-24T16:24:16.186Z", "4.4.13": "2019-09-25T04:56:34.833Z", "5.0.0": "2019-09-25T06:00:08.689Z", "5.0.1": "2019-09-26T00:42:19.709Z", "5.0.2": "2019-09-30T21:07:16.078Z", "5.0.3": "2019-10-05T02:05:48.604Z", "5.0.4": "2019-10-05T02:07:36.005Z", "5.0.5": "2019-10-05T06:54:59.338Z", "6.0.0": "2020-01-28T01:46:19.890Z", "6.0.1": "2020-01-29T04:37:54.305Z", "6.0.2": "2020-04-27T21:02:52.265Z", "6.0.3": "2020-08-14T22:31:23.092Z", "6.0.4": "2020-08-14T22:34:07.117Z", "6.0.5": "2020-08-14T22:44:10.304Z", "6.1.0": "2021-01-07T19:22:17.608Z", "5.0.6": "2021-07-23T22:44:40.117Z", "4.4.14": "2021-07-23T22:45:28.854Z", "3.2.2": "2021-07-23T22:46:51.627Z", "6.1.1": "2021-07-23T22:50:46.143Z", "6.1.2": "2021-07-26T23:11:29.889Z", "5.0.7": "2021-07-26T23:22:05.063Z", "4.4.15": "2021-07-26T23:22:34.327Z", "3.2.3": "2021-07-26T23:22:44.076Z", "6.1.3": "2021-08-02T17:20:17.578Z", "6.1.4": "2021-08-03T21:37:19.217Z", "6.1.5": "2021-08-04T01:06:37.871Z", "6.1.6": "2021-08-04T07:23:45.335Z", "6.1.7": "2021-08-09T23:15:21.658Z", "5.0.8": "2021-08-09T23:15:38.491Z", "4.4.16": "2021-08-09T23:17:03.494Z", "6.1.8": "2021-08-11T19:23:50.567Z", "4.4.17": "2021-08-11T19:24:40.099Z", "5.0.9": "2021-08-11T19:25:05.518Z", "6.1.9": "2021-08-19T02:16:18.186Z", "5.0.10": "2021-08-19T02:25:45.258Z", "4.4.18": "2021-08-19T02:27:59.112Z", "6.1.10": "2021-08-19T02:35:54.513Z", "5.0.11": "2021-08-19T02:36:16.529Z", "4.4.19": "2021-08-19T02:36:46.121Z", "6.1.11": "2021-08-26T16:16:13.333Z", "6.1.12": "2022-11-01T16:33:12.294Z", "6.1.13": "2022-12-07T20:32:39.620Z", "6.1.14": "2023-05-02T22:46:07.312Z", "6.1.15": "2023-05-17T05:38:51.855Z", "6.2.0": "2023-09-05T05:33:49.378Z", "6.2.1": "2024-03-21T21:13:04.779Z", "7.0.0": "2024-04-10T20:07:24.043Z", "7.0.1": "2024-04-14T21:45:25.236Z", "7.1.0": "2024-05-04T02:07:00.708Z", "7.2.0": "2024-05-30T01:29:04.900Z", "7.3.0": "2024-06-19T02:26:51.845Z", "7.4.0": "2024-06-19T02:37:02.405Z", "7.4.1": "2024-07-22T16:02:39.813Z", "7.4.2": "2024-07-24T23:58:02.238Z", "7.4.3": "2024-07-26T04:33:46.030Z"}, "bugs": {"url": "https://github.com/isaacs/node-tar/issues"}, "author": {"name": "<PERSON>"}, "license": "ISC", "homepage": "https://github.com/isaacs/node-tar#readme", "repository": {"type": "git", "url": "git+https://github.com/isaacs/node-tar.git"}, "description": "tar for node", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "readme": "# node-tar\n\nFast and full-featured Tar for Node.js\n\nThe API is designed to mimic the behavior of `tar(1)` on unix systems.\nIf you are familiar with how tar works, most of this will hopefully be\nstraightforward for you. If not, then hopefully this module can teach\nyou useful unix skills that may come in handy someday :)\n\n## Background\n\nA \"tar file\" or \"tarball\" is an archive of file system entries\n(directories, files, links, etc.) The name comes from \"tape archive\".\nIf you run `man tar` on almost any Unix command line, you'll learn\nquite a bit about what it can do, and its history.\n\nTar has 5 main top-level commands:\n\n- `c` Create an archive\n- `r` Replace entries within an archive\n- `u` Update entries within an archive (ie, replace if they're newer)\n- `t` List out the contents of an archive\n- `x` Extract an archive to disk\n\nThe other flags and options modify how this top level function works.\n\n## High-Level API\n\nThese 5 functions are the high-level API. All of them have a\nsingle-character name (for unix nerds familiar with `tar(1)`) as well\nas a long name (for everyone else).\n\nAll the high-level functions take the following arguments, all three\nof which are optional and may be omitted.\n\n1. `options` - An optional object specifying various options\n2. `paths` - An array of paths to add or extract\n3. `callback` - Called when the command is completed, if async. (If\n   sync or no file specified, providing a callback throws a\n   `TypeError`.)\n\nIf the command is sync (ie, if `options.sync=true`), then the\ncallback is not allowed, since the action will be completed immediately.\n\nIf a `file` argument is specified, and the command is async, then a\n`Promise` is returned. In this case, if async, a callback may be\nprovided which is called when the command is completed.\n\nIf a `file` option is not specified, then a stream is returned. For\n`create`, this is a readable stream of the generated archive. For\n`list` and `extract` this is a writable stream that an archive should\nbe written into. If a file is not specified, then a callback is not\nallowed, because you're already getting a stream to work with.\n\n`replace` and `update` only work on existing archives, and so require\na `file` argument.\n\nSync commands without a file argument return a stream that acts on its\ninput immediately in the same tick. For readable streams, this means\nthat all of the data is immediately available by calling\n`stream.read()`. For writable streams, it will be acted upon as soon\nas it is provided, but this can be at any time.\n\n### Warnings and Errors\n\nTar emits warnings and errors for recoverable and unrecoverable situations,\nrespectively. In many cases, a warning only affects a single entry in an\narchive, or is simply informing you that it's modifying an entry to comply\nwith the settings provided.\n\nUnrecoverable warnings will always raise an error (ie, emit `'error'` on\nstreaming actions, throw for non-streaming sync actions, reject the\nreturned Promise for non-streaming async operations, or call a provided\ncallback with an `Error` as the first argument). Recoverable errors will\nraise an error only if `strict: true` is set in the options.\n\nRespond to (recoverable) warnings by listening to the `warn` event.\nHandlers receive 3 arguments:\n\n- `code` String. One of the error codes below. This may not match\n  `data.code`, which preserves the original error code from fs and zlib.\n- `message` String. More details about the error.\n- `data` Metadata about the error. An `Error` object for errors raised by\n  fs and zlib. All fields are attached to errors raisd by tar. Typically\n  contains the following fields, as relevant:\n  - `tarCode` The tar error code.\n  - `code` Either the tar error code, or the error code set by the\n    underlying system.\n  - `file` The archive file being read or written.\n  - `cwd` Working directory for creation and extraction operations.\n  - `entry` The entry object (if it could be created) for `TAR_ENTRY_INFO`,\n    `TAR_ENTRY_INVALID`, and `TAR_ENTRY_ERROR` warnings.\n  - `header` The header object (if it could be created, and the entry could\n    not be created) for `TAR_ENTRY_INFO` and `TAR_ENTRY_INVALID` warnings.\n  - `recoverable` Boolean. If `false`, then the warning will emit an\n    `error`, even in non-strict mode.\n\n#### Error Codes\n\n- `TAR_ENTRY_INFO` An informative error indicating that an entry is being\n  modified, but otherwise processed normally. For example, removing `/` or\n  `C:\\` from absolute paths if `preservePaths` is not set.\n\n- `TAR_ENTRY_INVALID` An indication that a given entry is not a valid tar\n  archive entry, and will be skipped. This occurs when:\n\n  - a checksum fails,\n  - a `linkpath` is missing for a link type, or\n  - a `linkpath` is provided for a non-link type.\n\n  If every entry in a parsed archive raises an `TAR_ENTRY_INVALID` error,\n  then the archive is presumed to be unrecoverably broken, and\n  `TAR_BAD_ARCHIVE` will be raised.\n\n- `TAR_ENTRY_ERROR` The entry appears to be a valid tar archive entry, but\n  encountered an error which prevented it from being unpacked. This occurs\n  when:\n\n  - an unrecoverable fs error happens during unpacking,\n  - an entry is trying to extract into an excessively deep\n    location (by default, limited to 1024 subfolders),\n  - an entry has `..` in the path and `preservePaths` is not set, or\n  - an entry is extracting through a symbolic link, when `preservePaths` is\n    not set.\n\n- `TAR_ENTRY_UNSUPPORTED` An indication that a given entry is\n  a valid archive entry, but of a type that is unsupported, and so will be\n  skipped in archive creation or extracting.\n\n- `TAR_ABORT` When parsing gzipped-encoded archives, the parser will\n  abort the parse process raise a warning for any zlib errors encountered.\n  Aborts are considered unrecoverable for both parsing and unpacking.\n\n- `TAR_BAD_ARCHIVE` The archive file is totally hosed. This can happen for\n  a number of reasons, and always occurs at the end of a parse or extract:\n\n  - An entry body was truncated before seeing the full number of bytes.\n  - The archive contained only invalid entries, indicating that it is\n    likely not an archive, or at least, not an archive this library can\n    parse.\n\n  `TAR_BAD_ARCHIVE` is considered informative for parse operations, but\n  unrecoverable for extraction. Note that, if encountered at the end of an\n  extraction, tar WILL still have extracted as much it could from the\n  archive, so there may be some garbage files to clean up.\n\nErrors that occur deeper in the system (ie, either the filesystem or zlib)\nwill have their error codes left intact, and a `tarCode` matching one of\nthe above will be added to the warning metadata or the raised error object.\n\nErrors generated by tar will have one of the above codes set as the\n`error.code` field as well, but since errors originating in zlib or fs will\nhave their original codes, it's better to read `error.tarCode` if you wish\nto see how tar is handling the issue.\n\n### Examples\n\nThe API mimics the `tar(1)` command line functionality, with aliases\nfor more human-readable option and function names. The goal is that\nif you know how to use `tar(1)` in Unix, then you know how to use\n`import('tar')` in JavaScript.\n\nTo replicate `tar czf my-tarball.tgz files and folders`, you'd do:\n\n```js\nimport { create } from 'tar'\ncreate(\n  {\n    gzip: <true|gzip options>,\n    file: 'my-tarball.tgz'\n  },\n  ['some', 'files', 'and', 'folders']\n).then(_ => { .. tarball has been created .. })\n```\n\nTo replicate `tar cz files and folders > my-tarball.tgz`, you'd do:\n\n```js\n// if you're familiar with the tar(1) cli flags, this can be nice\nimport * as tar from 'tar'\ntar.c(\n  {\n    // 'z' is alias for 'gzip' option\n    z: <true|gzip options>\n  },\n  ['some', 'files', 'and', 'folders']\n).pipe(fs.createWriteStream('my-tarball.tgz'))\n```\n\nTo replicate `tar xf my-tarball.tgz` you'd do:\n\n```js\ntar.x( // or `tar.extract`\n  {\n    // or `file:`\n    f: 'my-tarball.tgz'\n  }\n).then(_=> { .. tarball has been dumped in cwd .. })\n```\n\nTo replicate `cat my-tarball.tgz | tar x -C some-dir --strip=1`:\n\n```js\nfs.createReadStream('my-tarball.tgz').pipe(\n  tar.x({\n    strip: 1,\n    C: 'some-dir', // alias for cwd:'some-dir', also ok\n  }),\n)\n```\n\nTo replicate `tar tf my-tarball.tgz`, do this:\n\n```js\ntar.t({\n  file: 'my-tarball.tgz',\n  onReadEntry: entry => { .. do whatever with it .. }\n})\n```\n\nFor example, to just get the list of filenames from an archive:\n\n```js\nconst getEntryFilenames = async tarballFilename => {\n  const filenames = []\n  await tar.t({\n    file: tarballFilename,\n    onReadEntry: entry => filenames.push(entry.path),\n  })\n  return filenames\n}\n```\n\nTo replicate `cat my-tarball.tgz | tar t` do:\n\n```js\nfs.createReadStream('my-tarball.tgz')\n  .pipe(tar.t())\n  .on('entry', entry => { .. do whatever with it .. })\n```\n\nTo do anything synchronous, add `sync: true` to the options. Note\nthat sync functions don't take a callback and don't return a promise.\nWhen the function returns, it's already done. Sync methods without a\nfile argument return a sync stream, which flushes immediately. But,\nof course, it still won't be done until you `.end()` it.\n\n```js\nconst getEntryFilenamesSync = tarballFilename => {\n  const filenames = []\n  tar.t({\n    file: tarballFilename,\n    onReadEntry: entry => filenames.push(entry.path),\n    sync: true,\n  })\n  return filenames\n}\n```\n\nTo filter entries, add `filter: <function>` to the options.\nTar-creating methods call the filter with `filter(path, stat)`.\nTar-reading methods (including extraction) call the filter with\n`filter(path, entry)`. The filter is called in the `this`-context of\nthe `Pack` or `Unpack` stream object.\n\nThe arguments list to `tar t` and `tar x` specify a list of filenames\nto extract or list, so they're equivalent to a filter that tests if\nthe file is in the list.\n\nFor those who _aren't_ fans of tar's single-character command names:\n\n```\ntar.c === tar.create\ntar.r === tar.replace (appends to archive, file is required)\ntar.u === tar.update (appends if newer, file is required)\ntar.x === tar.extract\ntar.t === tar.list\n```\n\nKeep reading for all the command descriptions and options, as well as\nthe low-level API that they are built on.\n\n### tar.c(options, fileList, callback) [alias: tar.create]\n\nCreate a tarball archive.\n\nThe `fileList` is an array of paths to add to the tarball. Adding a\ndirectory also adds its children recursively.\n\nAn entry in `fileList` that starts with an `@` symbol is a tar archive\nwhose entries will be added. To add a file that starts with `@`,\nprepend it with `./`.\n\nThe following options are supported:\n\n- `file` Write the tarball archive to the specified filename. If this\n  is specified, then the callback will be fired when the file has been\n  written, and a promise will be returned that resolves when the file\n  is written. If a filename is not specified, then a Readable Stream\n  will be returned which will emit the file data. [Alias: `f`]\n- `sync` Act synchronously. If this is set, then any provided file\n  will be fully written after the call to `tar.c`. If this is set,\n  and a file is not provided, then the resulting stream will already\n  have the data ready to `read` or `emit('data')` as soon as you\n  request it.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`. [Alias: `C`]\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()` [Alias: `z`]\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths. [Alias: `P`]\n- `mode` The mode to set on the created file archive\n- `noDirRecurse` Do not recursively archive the contents of\n  directories. [Alias: `n`]\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such. [Alias: `L`, `h`]\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n  [Alias: `m`, `no-mtime`]\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\nThe following options are mostly internal, but can be modified in some\nadvanced use cases, such as re-using caches between runs.\n\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `readdirCache` A Map object that caches calls to `readdir`.\n- `jobs` A number specifying how many concurrent jobs to run.\n  Defaults to 4.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n\n### tar.x(options, fileList, callback) [alias: tar.extract]\n\nExtract a tarball archive.\n\nThe `fileList` is an array of paths to extract from the tarball. If\nno paths are provided, then all the entries are extracted.\n\nIf the archive is gzipped, then tar will detect this and unzip it.\n\nNote that all directories that are created will be forced to be\nwritable, readable, and listable by their owner, to avoid cases where\na directory prevents extraction of child entries by virtue of its\nmode.\n\nMost extraction errors will cause a `warn` event to be emitted. If\nthe `cwd` is missing, or not a directory, then the extraction will\nfail completely.\n\nThe following options are supported:\n\n- `cwd` Extract files relative to the specified directory. Defaults\n  to `process.cwd()`. If provided, this must exist and must be a\n  directory. [Alias: `C`]\n- `file` The archive file to extract. If not specified, then a\n  Writable stream is returned where the archive data should be\n  written. [Alias: `f`]\n- `sync` Create files and directories synchronously.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being unpacked. Return `true` to unpack the entry from the\n  archive, or `false` to skip it.\n- `newer` Set to true to keep the existing file on disk if it's newer\n  than the file in the archive. [Alias: `keep-newer`,\n  `keep-newer-files`]\n- `keep` Do not overwrite existing files. In particular, if a file\n  appears more than once in an archive, later copies will not\n  overwrite earlier copies. [Alias: `k`, `keep-existing`]\n- `preservePaths` Allow absolute paths, paths containing `..`, and\n  extracting through symbolic links. By default, `/` is stripped from\n  absolute paths, `..` paths are not extracted, and any file whose\n  location would be modified by a symbolic link is not extracted.\n  [Alias: `P`]\n- `unlink` Unlink files before creating them. Without this option,\n  tar overwrites existing files, which preserves existing hardlinks.\n  With this option, existing hardlinks will be broken, as will any\n  symlink that would affect the location of an extracted file. [Alias:\n  `U`]\n- `strip` Remove the specified number of leading path elements.\n  Pathnames with fewer elements will be silently skipped. Note that\n  the pathname is edited after applying the filter, but before\n  security checks. [Alias: `strip-components`, `stripComponents`]\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `preserveOwner` If true, tar will set the `uid` and `gid` of\n  extracted entries to the `uid` and `gid` fields in the archive.\n  This defaults to true when run as root, and false otherwise. If\n  false, then files and directories will be set with the owner and\n  group of the user running the process. This is similar to `-p` in\n  `tar(1)`, but ACLs and other system-specific data is never unpacked\n  in this implementation, and modes are set by default already.\n  [Alias: `p`]\n- `uid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified user id, regardless of the `uid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `gid` option.\n- `gid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified group id, regardless of the `gid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `uid` option.\n- `noMtime` Set to true to omit writing `mtime` value for extracted\n  entries. [Alias: `m`, `no-mtime`]\n- `transform` Provide a function that takes an `entry` object, and\n  returns a stream, or any falsey value. If a stream is provided,\n  then that stream's data will be written instead of the contents of\n  the archive entry. If a falsey value is provided, then the entry is\n  written to disk as normal. (To exclude items from extraction, use\n  the `filter` option described above.)\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `chmod` Set to true to call `fs.chmod()` to ensure that the\n  extracted file matches the entry mode. This may necessitate a\n  call to the deprecated and thread-unsafe `process.umask()`\n  method to determine the default umask value, unless a\n  `processUmask` options is also provided. Otherwise tar will\n  extract with whatever mode is provided, and let the process\n  `umask` apply normally.\n- `processUmask` Set to an explicit numeric value to avoid\n  calling `process.umask()` when `chmod: true` is set.\n- `maxDepth` The maximum depth of subfolders to extract into. This\n  defaults to 1024. Anything deeper than the limit will raise a\n  warning and skip the entry. Set to `Infinity` to remove the\n  limitation.\n\nThe following options are mostly internal, but can be modified in some\nadvanced use cases, such as re-using caches between runs.\n\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `umask` Filter the modes of entries like `process.umask()`.\n- `dmode` Default mode for directories\n- `fmode` Default mode for files\n- `dirCache` A Map object of which directories exist.\n- `maxMetaEntrySize` The maximum size of meta entries that is\n  supported. Defaults to 1 MB.\n\nNote that using an asynchronous stream type with the `transform`\noption will cause undefined behavior in sync extractions.\n[MiniPass](http://npm.im/minipass)-based streams are designed for this\nuse case.\n\n### tar.t(options, fileList, callback) [alias: tar.list]\n\nList the contents of a tarball archive.\n\nThe `fileList` is an array of paths to list from the tarball. If\nno paths are provided, then all the entries are listed.\n\nIf the archive is gzipped, then tar will detect this and unzip it.\n\nIf the `file` option is _not_ provided, then returns an event emitter that\nemits `entry` events with `tar.ReadEntry` objects. However, they don't\nemit `'data'` or `'end'` events. (If you want to get actual readable\nentries, use the `tar.Parse` class instead.)\n\nIf a `file` option _is_ provided, then the return value will be a promise\nthat resolves when the file has been fully traversed in async mode, or\n`undefined` if `sync: true` is set. Thus, you _must_ specify an `onReadEntry`\nmethod in order to do anything useful with the data it parses.\n\nThe following options are supported:\n\n- `file` The archive file to list. If not specified, then a\n  Writable stream is returned where the archive data should be\n  written. [Alias: `f`]\n- `sync` Read the specified file synchronously. (This has no effect\n  when a file option isn't specified, because entries are emitted as\n  fast as they are parsed from the stream anyway.)\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being listed. Return `true` to emit the entry from the\n  archive, or `false` to skip it.\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter. This is important for when `file` is set,\n  because there is no other way to do anything useful with this method.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noResume` By default, `entry` streams are resumed immediately after\n  the call to `onReadEntry`. Set `noResume: true` to suppress this\n  behavior. Note that by opting into this, the stream will never\n  complete until the entry data is consumed.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n\n### tar.u(options, fileList, callback) [alias: tar.update]\n\nAdd files to an archive if they are newer than the entry already in\nthe tarball archive.\n\nThe `fileList` is an array of paths to add to the tarball. Adding a\ndirectory also adds its children recursively.\n\nAn entry in `fileList` that starts with an `@` symbol is a tar archive\nwhose entries will be added. To add a file that starts with `@`,\nprepend it with `./`.\n\nThe following options are supported:\n\n- `file` Required. Write the tarball archive to the specified\n  filename. [Alias: `f`]\n- `sync` Act synchronously. If this is set, then any provided file\n  will be fully written after the call to `tar.c`.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for adding entries to the\n  archive. Defaults to `process.cwd()`. [Alias: `C`]\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()` [Alias: `z`]\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths. [Alias: `P`]\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noDirRecurse` Do not recursively archive the contents of\n  directories. [Alias: `n`]\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such. [Alias: `L`, `h`]\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n  [Alias: `m`, `no-mtime`]\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\n### tar.r(options, fileList, callback) [alias: tar.replace]\n\nAdd files to an existing archive. Because later entries override\nearlier entries, this effectively replaces any existing entries.\n\nThe `fileList` is an array of paths to add to the tarball. Adding a\ndirectory also adds its children recursively.\n\nAn entry in `fileList` that starts with an `@` symbol is a tar archive\nwhose entries will be added. To add a file that starts with `@`,\nprepend it with `./`.\n\nThe following options are supported:\n\n- `file` Required. Write the tarball archive to the specified\n  filename. [Alias: `f`]\n- `sync` Act synchronously. If this is set, then any provided file\n  will be fully written after the call to `tar.c`.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for adding entries to the\n  archive. Defaults to `process.cwd()`. [Alias: `C`]\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()` [Alias: `z`]\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths. [Alias: `P`]\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noDirRecurse` Do not recursively archive the contents of\n  directories. [Alias: `n`]\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such. [Alias: `L`, `h`]\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n  [Alias: `m`, `no-mtime`]\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\n## Low-Level API\n\n### class Pack\n\nA readable tar stream.\n\nHas all the standard readable stream interface stuff. `'data'` and\n`'end'` events, `read()` method, `pause()` and `resume()`, etc.\n\n#### constructor(options)\n\nThe following options are supported:\n\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`.\n- `prefix` A path portion to prefix onto the entries in the archive.\n- `gzip` Set to any truthy value to create a gzipped archive, or an\n  object with settings for `zlib.Gzip()`\n- `filter` A function that gets called with `(path, stat)` for each\n  entry being added. Return `true` to add the entry to the archive,\n  or `false` to omit it.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `readdirCache` A Map object that caches calls to `readdir`.\n- `jobs` A number specifying how many concurrent jobs to run.\n  Defaults to 4.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 16 MB.\n- `noDirRecurse` Do not recursively archive the contents of\n  directories.\n- `follow` Set to true to pack the targets of symbolic links. Without\n  this option, symbolic links are archived as such.\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n- `mtime` Set to a `Date` object to force a specific `mtime` for\n  everything added to the archive. Overridden by `noMtime`.\n- `onWriteEntry` Called with each `WriteEntry` or\n  `WriteEntrySync` that is created in the course of writing the\n  archive.\n\n#### add(path)\n\nAdds an entry to the archive. Returns the Pack stream.\n\n#### write(path)\n\nAdds an entry to the archive. Returns true if flushed.\n\n#### end()\n\nFinishes the archive.\n\n### class PackSync\n\nSynchronous version of `Pack`.\n\n### class Unpack\n\nA writable stream that unpacks a tar archive onto the file system.\n\nAll the normal writable stream stuff is supported. `write()` and\n`end()` methods, `'drain'` events, etc.\n\nNote that all directories that are created will be forced to be\nwritable, readable, and listable by their owner, to avoid cases where\na directory prevents extraction of child entries by virtue of its\nmode.\n\n`'close'` is emitted when it's done writing stuff to the file system.\n\nMost unpack errors will cause a `warn` event to be emitted. If the\n`cwd` is missing, or not a directory, then an error will be emitted.\n\n#### constructor(options)\n\n- `cwd` Extract files relative to the specified directory. Defaults\n  to `process.cwd()`. If provided, this must exist and must be a\n  directory.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being unpacked. Return `true` to unpack the entry from the\n  archive, or `false` to skip it.\n- `newer` Set to true to keep the existing file on disk if it's newer\n  than the file in the archive.\n- `keep` Do not overwrite existing files. In particular, if a file\n  appears more than once in an archive, later copies will not\n  overwrite earlier copies.\n- `preservePaths` Allow absolute paths, paths containing `..`, and\n  extracting through symbolic links. By default, `/` is stripped from\n  absolute paths, `..` paths are not extracted, and any file whose\n  location would be modified by a symbolic link is not extracted.\n- `unlink` Unlink files before creating them. Without this option,\n  tar overwrites existing files, which preserves existing hardlinks.\n  With this option, existing hardlinks will be broken, as will any\n  symlink that would affect the location of an extracted file.\n- `strip` Remove the specified number of leading path elements.\n  Pathnames with fewer elements will be silently skipped. Note that\n  the pathname is edited after applying the filter, but before\n  security checks.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `umask` Filter the modes of entries like `process.umask()`.\n- `dmode` Default mode for directories\n- `fmode` Default mode for files\n- `dirCache` A Map object of which directories exist.\n- `maxMetaEntrySize` The maximum size of meta entries that is\n  supported. Defaults to 1 MB.\n- `preserveOwner` If true, tar will set the `uid` and `gid` of\n  extracted entries to the `uid` and `gid` fields in the archive.\n  This defaults to true when run as root, and false otherwise. If\n  false, then files and directories will be set with the owner and\n  group of the user running the process. This is similar to `-p` in\n  `tar(1)`, but ACLs and other system-specific data is never unpacked\n  in this implementation, and modes are set by default already.\n- `win32` True if on a windows platform. Causes behavior where\n  filenames containing `<|>?` chars are converted to\n  windows-compatible values while being unpacked.\n- `uid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified user id, regardless of the `uid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `gid` option.\n- `gid` Set to a number to force ownership of all extracted files and\n  folders, and all implicitly created directories, to be owned by the\n  specified group id, regardless of the `gid` field in the archive.\n  Cannot be used along with `preserveOwner`. Requires also setting a\n  `uid` option.\n- `noMtime` Set to true to omit writing `mtime` value for extracted\n  entries.\n- `transform` Provide a function that takes an `entry` object, and\n  returns a stream, or any falsey value. If a stream is provided,\n  then that stream's data will be written instead of the contents of\n  the archive entry. If a falsey value is provided, then the entry is\n  written to disk as normal. (To exclude items from extraction, use\n  the `filter` option described above.)\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `chmod` Set to true to call `fs.chmod()` to ensure that the\n  extracted file matches the entry mode. This may necessitate a\n  call to the deprecated and thread-unsafe `process.umask()`\n  method to determine the default umask value, unless a\n  `processUmask` options is also provided. Otherwise tar will\n  extract with whatever mode is provided, and let the process\n  `umask` apply normally.\n- `processUmask` Set to an explicit numeric value to avoid\n  calling `process.umask()` when `chmod: true` is set.\n- `maxDepth` The maximum depth of subfolders to extract into. This\n  defaults to 1024. Anything deeper than the limit will raise a\n  warning and skip the entry. Set to `Infinity` to remove the\n  limitation.\n\n### class UnpackSync\n\nSynchronous version of `Unpack`.\n\nNote that using an asynchronous stream type with the `transform`\noption will cause undefined behavior in sync unpack streams.\n[MiniPass](http://npm.im/minipass)-based streams are designed for this\nuse case.\n\n### class tar.Parse\n\nA writable stream that parses a tar archive stream. All the standard\nwritable stream stuff is supported.\n\nIf the archive is gzipped, then tar will detect this and unzip it.\n\nEmits `'entry'` events with `tar.ReadEntry` objects, which are\nthemselves readable streams that you can pipe wherever.\n\nEach `entry` will not emit until the one before it is flushed through,\nso make sure to either consume the data (with `on('data', ...)` or\n`.pipe(...)`) or throw it away with `.resume()` to keep the stream\nflowing.\n\n#### constructor(options)\n\nReturns an event emitter that emits `entry` events with\n`tar.ReadEntry` objects.\n\nThe following options are supported:\n\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `filter` A function that gets called with `(path, entry)` for each\n  entry being listed. Return `true` to emit the entry from the\n  archive, or `false` to skip it.\n- `onReadEntry` A function that gets called with `(entry)` for each entry\n  that passes the filter.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n\n#### abort(error)\n\nStop all parsing activities. This is called when there are zlib\nerrors. It also emits an unrecoverable warning with the error provided.\n\n### class tar.ReadEntry extends [MiniPass](http://npm.im/minipass)\n\nA representation of an entry that is being read out of a tar archive.\n\nIt has the following fields:\n\n- `extended` The extended metadata object provided to the constructor.\n- `globalExtended` The global extended metadata object provided to the\n  constructor.\n- `remain` The number of bytes remaining to be written into the\n  stream.\n- `blockRemain` The number of 512-byte blocks remaining to be written\n  into the stream.\n- `ignore` Whether this entry should be ignored.\n- `meta` True if this represents metadata about the next entry, false\n  if it represents a filesystem object.\n- All the fields from the header, extended header, and global extended\n  header are added to the ReadEntry object. So it has `path`, `type`,\n  `size`, `mode`, and so on.\n\n#### constructor(header, extended, globalExtended)\n\nCreate a new ReadEntry object with the specified header, extended\nheader, and global extended header values.\n\n### class tar.WriteEntry extends [MiniPass](http://npm.im/minipass)\n\nA representation of an entry that is being written from the file\nsystem into a tar archive.\n\nEmits data for the Header, and for the Pax Extended Header if one is\nrequired, as well as any body data.\n\nCreating a WriteEntry for a directory does not also create\nWriteEntry objects for all of the directory contents.\n\nIt has the following fields:\n\n- `path` The path field that will be written to the archive. By\n  default, this is also the path from the cwd to the file system\n  object.\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `myuid` If supported, the uid of the user running the current\n  process.\n- `myuser` The `env.USER` string if set, or `''`. Set as the entry\n  `uname` field if the file's `uid` matches `this.myuid`.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 1 MB.\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`.\n- `absolute` The absolute path to the entry on the filesystem. By\n  default, this is `path.resolve(this.cwd, this.path)`, but it can be\n  overridden explicitly.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `win32` True if on a windows platform. Causes behavior where paths\n  replace `\\` with `/` and filenames containing the windows-compatible\n  forms of `<|>?:` characters are converted to actual `<|>?:` characters\n  in the archive.\n- `noPax` Suppress pax extended headers. Note that this means that\n  long paths and linkpaths will be truncated, and large or negative\n  numeric values may be interpreted incorrectly.\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n\n#### constructor(path, options)\n\n`path` is the path of the entry as it is written in the archive.\n\nThe following options are supported:\n\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `maxReadSize` The maximum buffer size for `fs.read()` operations.\n  Defaults to 1 MB.\n- `linkCache` A Map object containing the device and inode value for\n  any file whose nlink is > 1, to identify hard links.\n- `statCache` A Map object that caches calls `lstat`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `cwd` The current working directory for creating the archive.\n  Defaults to `process.cwd()`.\n- `absolute` The absolute path to the entry on the filesystem. By\n  default, this is `path.resolve(this.cwd, this.path)`, but it can be\n  overridden explicitly.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `win32` True if on a windows platform. Causes behavior where paths\n  replace `\\` with `/`.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n- `umask` Set to restrict the modes on the entries in the archive,\n  somewhat like how umask works on file creation. Defaults to\n  `process.umask()` on unix systems, or `0o22` on Windows.\n\n#### warn(message, data)\n\nIf strict, emit an error with the provided message.\n\nOthewise, emit a `'warn'` event with the provided message and data.\n\n### class tar.WriteEntry.Sync\n\nSynchronous version of tar.WriteEntry\n\n### class tar.WriteEntry.Tar\n\nA version of tar.WriteEntry that gets its data from a tar.ReadEntry\ninstead of from the filesystem.\n\n#### constructor(readEntry, options)\n\n`readEntry` is the entry being read out of another archive.\n\nThe following options are supported:\n\n- `portable` Omit metadata that is system-specific: `ctime`, `atime`,\n  `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and `nlink`. Note\n  that `mtime` is still included, because this is necessary for other\n  time-based operations. Additionally, `mode` is set to a \"reasonable\n  default\" for most unix systems, based on a `umask` value of `0o22`.\n- `preservePaths` Allow absolute paths. By default, `/` is stripped\n  from absolute paths.\n- `strict` Treat warnings as crash-worthy errors. Default false.\n- `onwarn` A function that will get called with `(code, message, data)` for\n  any warnings encountered. (See \"Warnings and Errors\")\n- `noMtime` Set to true to omit writing `mtime` values for entries.\n  Note that this prevents using other mtime-based features like\n  `tar.update` or the `keepNewer` option with the resulting tar archive.\n\n### class tar.Header\n\nA class for reading and writing header blocks.\n\nIt has the following fields:\n\n- `nullBlock` True if decoding a block which is entirely composed of\n  `0x00` null bytes. (Useful because tar files are terminated by\n  at least 2 null blocks.)\n- `cksumValid` True if the checksum in the header is valid, false\n  otherwise.\n- `needPax` True if the values, as encoded, will require a Pax\n  extended header.\n- `path` The path of the entry.\n- `mode` The 4 lowest-order octal digits of the file mode. That is,\n  read/write/execute permissions for world, group, and owner, and the\n  setuid, setgid, and sticky bits.\n- `uid` Numeric user id of the file owner\n- `gid` Numeric group id of the file owner\n- `size` Size of the file in bytes\n- `mtime` Modified time of the file\n- `cksum` The checksum of the header. This is generated by adding all\n  the bytes of the header block, treating the checksum field itself as\n  all ascii space characters (that is, `0x20`).\n- `type` The human-readable name of the type of entry this represents,\n  or the alphanumeric key if unknown.\n- `typeKey` The alphanumeric key for the type of entry this header\n  represents.\n- `linkpath` The target of Link and SymbolicLink entries.\n- `uname` Human-readable user name of the file owner\n- `gname` Human-readable group name of the file owner\n- `devmaj` The major portion of the device number. Always `0` for\n  files, directories, and links.\n- `devmin` The minor portion of the device number. Always `0` for\n  files, directories, and links.\n- `atime` File access time.\n- `ctime` File change time.\n\n#### constructor(data, [offset=0])\n\n`data` is optional. It is either a Buffer that should be interpreted\nas a tar Header starting at the specified offset and continuing for\n512 bytes, or a data object of keys and values to set on the header\nobject, and eventually encode as a tar Header.\n\n#### decode(block, offset)\n\nDecode the provided buffer starting at the specified offset.\n\nBuffer length must be greater than 512 bytes.\n\n#### set(data)\n\nSet the fields in the data object.\n\n#### encode(buffer, offset)\n\nEncode the header fields into the buffer at the specified offset.\n\nReturns `this.needPax` to indicate whether a Pax Extended Header is\nrequired to properly encode the specified data.\n\n### class tar.Pax\n\nAn object representing a set of key-value pairs in an Pax extended\nheader entry.\n\nIt has the following fields. Where the same name is used, they have\nthe same semantics as the tar.Header field of the same name.\n\n- `global` True if this represents a global extended header, or false\n  if it is for a single entry.\n- `atime`\n- `charset`\n- `comment`\n- `ctime`\n- `gid`\n- `gname`\n- `linkpath`\n- `mtime`\n- `path`\n- `size`\n- `uid`\n- `uname`\n- `dev`\n- `ino`\n- `nlink`\n\n#### constructor(object, global)\n\nSet the fields set in the object. `global` is a boolean that defaults\nto false.\n\n#### encode()\n\nReturn a Buffer containing the header and body for the Pax extended\nheader entry, or `null` if there is nothing to encode.\n\n#### encodeBody()\n\nReturn a string representing the body of the pax extended header\nentry.\n\n#### encodeField(fieldName)\n\nReturn a string representing the key/value encoding for the specified\nfieldName, or `''` if the field is unset.\n\n### tar.Pax.parse(string, extended, global)\n\nReturn a new Pax object created by parsing the contents of the string\nprovided.\n\nIf the `extended` object is set, then also add the fields from that\nobject. (This is necessary because multiple metadata entries can\noccur in sequence.)\n\n### tar.types\n\nA translation table for the `type` field in tar headers.\n\n#### tar.types.name.get(code)\n\nGet the human-readable name for a given alphanumeric code.\n\n#### tar.types.code.get(name)\n\nGet the alphanumeric code for a given human-readable name.\n", "readmeFilename": "README.md", "users": {"326060588": true, "po": true, "yi": true, "pid": true, "dodo": true, "dofy": true, "arefm": true, "akarem": true, "buzuli": true, "esenor": true, "evan2x": true, "kastor": true, "mklabs": true, "nuwaio": true, "omegga": true, "potnox": true, "sirrah": true, "tarcio": true, "tianyk": true, "itonyyo": true, "liselot": true, "mikepol": true, "yokubee": true, "alexkval": true, "heineiuo": true, "liveinjs": true, "rhansson": true, "rmarques": true, "rochejul": true, "wkaifang": true, "xiaobing": true, "evanyeung": true, "fgribreau": true, "qinshulei": true, "shakakira": true, "axelrindle": true, "joelwallis": true, "jswartwood": true, "pragmadash": true, "haiyang5210": true, "nickeltobias": true, "tobiasnickel": true, "shen-weizhong": true, "shanewholloway": true, "arcticicestudio": true}}