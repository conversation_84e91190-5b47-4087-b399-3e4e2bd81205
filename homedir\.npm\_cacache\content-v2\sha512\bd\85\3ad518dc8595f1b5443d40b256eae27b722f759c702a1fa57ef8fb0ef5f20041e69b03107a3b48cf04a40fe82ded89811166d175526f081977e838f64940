{"name": "gopd", "dist-tags": {"latest": "1.2.0"}, "versions": {"1.0.1": {"name": "gopd", "version": "1.0.1", "dependencies": {"get-intrinsic": "^1.1.3"}, "devDependencies": {"aud": "^2.0.1", "tape": "^5.6.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "auto-changelog": "^2.4.0", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "dist": {"shasum": "29ff76de69dac7489b7c0918a5788e56477c332c", "tarball": "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==", "signatures": [{"sig": "MEQCIBDoe9bXK8xSrmnY7XYTdbRAJEJVVA0b45+dVu3Eg9wIAiB2VNbwGpWsB1yrINoQU+ws9M1JNuQxLmGMAk0b5gsNLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYg1UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp80g/9GhDy6/of0kgDHy/13dC/BlTRUfBcftwssUMjnwLWOAAR2xQC\r\nGt59WehnCNUeGfXCzfnU9Y/jBnDi7cIMFLk75QRkqH45oapYOgm7Yu0eQSbS\r\n3j2dQiSCoDva/lTl0RjtT148pbSKXc692hodS1T9nIAxbtjB5EE1mpau+n2n\r\n6oECynnKt7bPmsYswzkyaut2Eu8qMCZzhLwmqNFoCTchqXWI62kS7ijfFoiC\r\nkhFmYC36JVOyxyZ/cCPo88otFPjixBvz2oPl8XJKYckd/JAJov62wx63YjYx\r\nAV8Vy+vzYLeiMNOm9Mca/Z6Rfv8s4pJnzMu8vJTjJwtSh9mnuLFnpiPAB4Ws\r\n84PQodRR6p1Ib5yzBmJFxX37oNt39KvBgpzDEkA5hJ7sEnzs8vsqcfa/heKF\r\nI4UyjrD8zXAfpQs/n5RIEaedyIpzY8eBCSE0rE5bzrp9KuOF54I3zk4rwWMP\r\nwc+zcvXoghXKtyMfFgL8IxrIyAmP/8jT/is9rUQNEHyFSCw/8TJJnZJofxkk\r\nBMGQu9RfmbW2kIAevM4DbBzqbI1O9Ih0tqYa4MbOuGN+Mmap3Dooa/CNVmXW\r\n9KTev2I/6JGBek5DMnr2vtkuweSFs1fDsP4kKIRKyhUU7IRfOVkE3I3bhz3N\r\nvd4oK+PvfP0FWnP7zVA1D/jaJZ0xZMk9xQ8=\r\n=Z17m\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.1.0": {"name": "gopd", "version": "1.1.0", "dependencies": {"get-intrinsic": "^1.2.4"}, "devDependencies": {"tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "^5.8.0-dev.20241129", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.0", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1"}, "dist": {"shasum": "df8f0839c2d48caefc32a025a49294d39606c912", "tarball": "https://registry.npmjs.org/gopd/-/gopd-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-FQoVQnqcdk4hVM4JN1eromaun4iuS34oStkdlLENLdpULsuQcTyXj8w7ayhuUfPwEYZ1ZOooOTT6fdA9Vmx/RA==", "signatures": [{"sig": "MEUCIQCYFfoSS2RtJe8o1roQ5J/pb+FBM20OWS2vaS79GgCargIga4Ybl23B69VWyCmHpedvbZfCenjmQ0/HqRjrHJn/E3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9648}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.2.0": {"name": "gopd", "version": "1.2.0", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dist": {"integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "shasum": "89f56b8217bdbc8802bd299df6d7f1081d7e51a1", "tarball": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "fileCount": 12, "unpackedSize": 9869, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5XMsZlbC6fTaTnci63FcU7n0ajefDF31NCM5hiSNjAgIhAKOtFohttj8T5Ejj+saoHldL3znN/rzau+8Ljry8gxan"}]}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}}, "modified": "2024-12-04T16:21:52.914Z"}