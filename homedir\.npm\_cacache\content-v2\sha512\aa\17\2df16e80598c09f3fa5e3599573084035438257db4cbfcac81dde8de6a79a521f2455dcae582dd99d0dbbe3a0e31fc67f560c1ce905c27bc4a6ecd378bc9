{"name": "side-channel-weakmap", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "side-channel-weakmap", "version": "1.0.0", "dependencies": {"es-errors": "^1.3.0", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.0"}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "dist": {"shasum": "531137685635cd42515ed7f6fc7ae19cd3ab837d", "tarball": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-lvUwe8p1cHNQKdC317/Nt4B6vD3/o6zs4Rhc6M1JwospZ87d8l/w3sDKOLbsPTBpCG7Xn5g1nVKgF5qOAe4AiA==", "signatures": [{"sig": "MEUCIQCnS0PqrDt5Q0a7niev5WS/w76umBSgdYq2G1Tv7hNPzQIgfPONnlYytl/U1N3va0jqpgOOYgySmUmW+qtr734yocY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14019}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.0.1": {"name": "side-channel-weakmap", "version": "1.0.1", "dependencies": {"es-errors": "^1.3.0", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "encoding": "^0.1.13", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "safe-publish-latest": "^2.0.0", "@types/get-intrinsic": "^1.2.3", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "dist": {"shasum": "3b08f3f51386da21f17e0cc1f78492368dad00f2", "tarball": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-1v3pa5azlNqqdoT8A1K5uIxdu6uyBCg3ulmYdYgpOTYQKrIVQTwly+N8oHm0/ALjFzAm+Ogg/UlMVX9nqwFY5g==", "signatures": [{"sig": "MEQCIBFrzlOkgctkx+KYY9yDbNRfw9xP1XB8fgskjEP0Fx9fAiB9wcPkKN/dGzNJuj7XvgaD3ALZO5TRS8Htlzvx5Va8IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14415}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "1.0.2": {"name": "side-channel-weakmap", "version": "1.0.2", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "dist": {"integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "shasum": "11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea", "tarball": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "fileCount": 12, "unpackedSize": 14667, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9ighOR+5F/NNtDuqeh0Xi+6vI25HMiyQQWkmnwmPvOQIgRWs5RN7qMoTC5yz3gvh9nCYcCuiFkvPsvv2sJFlReo8="}]}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}}, "modified": "2024-12-11T05:39:11.658Z"}