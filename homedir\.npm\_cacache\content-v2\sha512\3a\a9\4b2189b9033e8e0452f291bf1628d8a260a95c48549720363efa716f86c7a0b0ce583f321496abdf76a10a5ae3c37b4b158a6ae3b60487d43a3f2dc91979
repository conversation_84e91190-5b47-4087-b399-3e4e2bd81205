{"_id": "range-parser", "_rev": "45-839cefb5a684e7d767a45673f95f69cc", "name": "range-parser", "dist-tags": {"latest": "1.2.1"}, "versions": {"0.0.1": {"name": "range-parser", "version": "0.0.1", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "range-parser@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dec6a8b9792caaef485e733b75b5b73fc7095770", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.1.tgz", "integrity": "sha512-oryvInGgJiHLtv0ZcAbru1T1rILcRqNE1/ah3aTPiLAwp2629ElAtZ1ocJ485dvAmc+l2IgtRaKEXSLXd9HKIg==", "signatures": [{"sig": "MEUCIBWleRZSV0/K9K/1ksLQ3XlX1YYYb6xdDYo53Sbu8QMfAiEAsd3f0+LlTHtZwZdMOjL4hs5fsM35rgmMbT3XX1lMCQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "Range header field string parser", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.2": {"name": "range-parser", "version": "0.0.2", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "range-parser@0.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "034fafbd8b266f64d8effe8fa638392b2290b288", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.2.tgz", "integrity": "sha512-icKpYzxoZAOK1PT5UVxxYQLWdhW7xvEGFDKwBW3frjwKk916XpfmhTxx47bV68RfGhwunXLDWP7bXCRTy6cckQ==", "signatures": [{"sig": "MEQCIFmC/O3yYRQO6uxSMnWyyL3fBjuBALVT0mYyZAGCUF8AAiAzD0/UetXtaXfgweWxBHwkoij5SKYtqd4YHgYi1jEnnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.19", "description": "Range header field string parser", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.3": {"name": "range-parser", "version": "0.0.3", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "range-parser@0.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6e6488fb73843bd4bd626797f76b870da9765ae9", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.3.tgz", "integrity": "sha512-3yy/idKE9+XSKk5KVy7NkRmVTI1R5TAvRXO23CnO64+2vdmyhxhScD4pg/2ylh9vwJXeDFlFr9vJyOsBvJR1TA==", "signatures": [{"sig": "MEQCICD7ksWqF20hN/AKGotMp+Yy1Ju4PWn6Xv/6Lzx9/hFqAiAl1OXD/EsfkdL/OdlOX4B5mb7/czPiB5EA15fhz1u5Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.19", "description": "Range header field string parser", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.4": {"name": "range-parser", "version": "0.0.4", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "range-parser@0.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c0427ffef51c10acba0782a46c9602e744ff620b", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.4.tgz", "integrity": "sha512-okJVEq9DbZyg+5lD8pr6ooQmeA0uu8DYIyAU7VK1WUUK7hctI1yw2ZHhKiKjB6RXaDrYRmTR4SsIHkyiQpaLMA==", "signatures": [{"sig": "MEYCIQCsI8Y0uPSCTSjthi+z/nKPlsxr6QSnIOiSba7xZrRRTwIhAOTlv3N37L0LsoyDhMLP9FQDLFAcQq6vshJaqGtejnkf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.19", "description": "Range header field string parser", "directories": {}, "_nodeVersion": "v0.6.16", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.0": {"name": "range-parser", "version": "1.0.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "range-parser@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-range-parser/issues"}, "dist": {"shasum": "a4b264cfe0be5ce36abe3765ac9c2a248746dbc0", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.0.tgz", "integrity": "sha512-wOH5LIH2ZHo0P7/bwkR+aNbJ+kv3CHVX4B8qs9GqbtY29fi1bGPV5xczrutN20G+Z4XhRqRMTW3q0S4iyJJPfw==", "signatures": [{"sig": "MEUCID+EU4aRGjKrtm31tA+STZCWTG0aVAB5baa+Rz4+p5Z5AiEA1H0pBf1qAQDOiZ6Ekp3jQJXHYwg+idvz/DGW0Awfzr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/visionmedia/node-range-parser#license", "type": "MIT"}], "repository": {"url": "https://github.com/visionmedia/node-range-parser.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Range header field string parser", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.1": {"name": "range-parser", "version": "1.0.1", "keywords": ["range", "parser", "http"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "range-parser@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/range-parser", "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "dist": {"shasum": "f9da15b7451fe1b261959b63342dd92921d34da2", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.1.tgz", "integrity": "sha512-ajMpjaHx1EnsiUlv0SSn1ljlyd3BlssbmNPD5880IGeMbOQixcoz0KDx8hengN+XIEXG2HXr5sAfoJc9gjBEMQ==", "signatures": [{"sig": "MEUCIQD4QDbBaIdC92lMzFUwgPkxcS01Cnwpp5GwPlLNfYf5MQIgNBbMrH+s8zrbOaB7S8002gOTqZcMb1nh0y377B+DGTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f9da15b7451fe1b261959b63342dd92921d34da2", "gitHead": "9d84686c20af96aef0941d90cf254b31d6172049", "scripts": {"test": "mocha --reporter spec --require should", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --require should"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/range-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Range header field string parser", "directories": {}, "devDependencies": {"mocha": "1", "should": "2", "istanbul": "0"}}, "1.0.2": {"name": "range-parser", "version": "1.0.2", "keywords": ["range", "parser", "http"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "range-parser@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/range-parser", "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "dist": {"shasum": "06a12a42e5131ba8e457cd892044867f2344e549", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.2.tgz", "integrity": "sha512-jFJ4/7R56zPTiBtje6f0Zyzh0ippWiAeSzpOFRjqVeXVat15mVJkpn4Oo76r64JPGJVH6RmSLbxCvOh7ysijzA==", "signatures": [{"sig": "MEQCICm/T1H7dETb5jyErjXO7pGXcpGFRmMPXv/3LWdbxa9JAiAiXzCKLUCXBG4ELMYxeht8J8Y+XvMpDqunv3X+kMVuwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "06a12a42e5131ba8e457cd892044867f2344e549", "engines": {"node": ">= 0.6"}, "gitHead": "ae23b02ce705b56e7f7c48e832d41fa710227ecc", "scripts": {"test": "mocha --reporter spec --require should", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/range-parser", "type": "git"}, "_npmVersion": "1.4.21", "description": "Range header field string parser", "directories": {}, "devDependencies": {"mocha": "1", "should": "2", "istanbul": "0"}}, "1.0.3": {"name": "range-parser", "version": "1.0.3", "keywords": ["range", "parser", "http"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "range-parser@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/range-parser", "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "dist": {"shasum": "6872823535c692e2c2a0103826afd82c2e0ff175", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.3.tgz", "integrity": "sha512-nDsRrtIxVUO5opg/A8T2S3ebULVIfuh8ECbh4w3N4mWxIiT3QILDJDUQayPqm2e8Q8NUa0RSUkGCfe33AfjR3Q==", "signatures": [{"sig": "MEUCIHjU89Nf1hzEymNoxxudEhoF8HzI86CE7H1Am04SFhV0AiEA2IYe0lt9rS4pkv3Ytwx8dGCUmc6rlwaw6xtgPkAYmwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "6872823535c692e2c2a0103826afd82c2e0ff175", "engines": {"node": ">= 0.6"}, "gitHead": "18e46a3de74afff9f4e22717f11ddd6e9aa6d845", "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/range-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Range header field string parser", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.0"}}, "1.1.0": {"name": "range-parser", "version": "1.1.0", "keywords": ["range", "parser", "http"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "range-parser@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/range-parser", "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "dist": {"shasum": "425c2c5bf8b159d89513fe55f26c29d07b88512b", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.1.0.tgz", "integrity": "sha512-6rmDAROeyfqjdhhr9wMLh+6ODrbojLeMbDou5Xz4oUuV9gKKTO+P057EpPhTN1zeku5fbGuYAU0K8IrUfIWAfg==", "signatures": [{"sig": "MEQCIEEKreJu5PtWdt4uLH+ebST0oTFWyQCimQf3qkpL1AUFAiB9OoCGPFhE+6CIacIxsW1oAyuZoVpGbMSv0o7PWmcYpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "425c2c5bf8b159d89513fe55f26c29d07b88512b", "engines": {"node": ">= 0.6"}, "gitHead": "82089bc84646dc4165985f77798e91d3afc97f3c", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/range-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Range header field string parser", "directories": {}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.9.0", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/range-parser-1.1.0.tgz_1463166914433_0.4869228946045041", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.0": {"name": "range-parser", "version": "1.2.0", "keywords": ["range", "parser", "http"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "range-parser@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/range-parser", "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "dist": {"shasum": "f49be6b487894ddc40dcc94a322f611092e00d5e", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz", "integrity": "sha512-kA5WQoNVo4t9lNx2kQNFCxKeBl5IbbSNBl1M/tLkw9WCn+hxNBAW5Qh8gdhs63CJnhjJ2zQWFoqPJP2sK1AV5A==", "signatures": [{"sig": "MEYCIQCKdgQqHXTz5EcKiBj0cH9GLK4cnCTzd0G0Zxiq8+U1+QIhAPhU9I9SG24ieDGKCXNEqEygwu5eRk4loOZEwGM7nLK8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "f49be6b487894ddc40dcc94a322f611092e00d5e", "engines": {"node": ">= 0.6"}, "gitHead": "0665aca31639d799dee1d35fb10970799559ec48", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/range-parser", "type": "git"}, "_npmVersion": "1.4.28", "description": "Range header field string parser", "directories": {}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.11.1", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/range-parser-1.2.0.tgz_1464803293097_0.6830497414339334", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.1": {"name": "range-parser", "version": "1.2.1", "keywords": ["range", "parser", "http"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "range-parser@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/range-parser#readme", "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "dist": {"shasum": "3cf37023d199e1c24d1a55b84800c2f3e6468031", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "signatures": [{"sig": "MEUCICYe3UbG+guUpzX1hy4AXl6wAiaz8eWTcN1t/jr/EBvgAiEAhkdbwL0VfEJfp3+Ws7+8CRKWhmXmp9+poH/RiJOJmpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1hb6CRA9TVsSAnZWagAA6MIP/2aFx73ToT5iGRN0mgZw\nLy7BEmRQkblBxo81k9Cvjt8jucfv/PtDUllITbke596JI/jHnb5oU5XV5Fzd\nK1WklG8rJC9z1y9iEaEe4pRcnvkPvkHtFMmPcYS9dm5XQyYw9fYcApRl/6zH\neuV/1CtwzQnCCErvfmolfSQ13v1d4LyWwrMRcG79uOGYBm8XSuTb3fKrEbBj\n3Gms6SQA2mC5ntKrf6VQRXzWvGIvWp2Q3RGFkgI1fnEPFfq7DbNqvHiD+KV4\nXV2wOD6B8pBlpbpSdNMHfUBSVSMHqFv9rOJqJumBAEZeUcwQzO06/2kUw5/f\nWCaFTHYTMCvSNX9qg71EoRZuuvNS0E53quFagdmTxq2vf04vz01PgRc5G64m\nTlz07gVHos3CQ9fU4NP0Aim1rtgOLJj15IF+z/kSSQoQZ6DhW5aTs8zCa3AS\nk4xWFafBuzyG1ApCSJrRCsqzY+oRqHPyTSZx5fBv75qZIEKe4moBpJ6EkaqZ\nRc6EqCpJjwOZVOssoPN3RiP+f/g43ytkVmUoYSXTs2bpkwNQUJShg2ItsT55\nkxkfZx8YbNxkFKp8S/uwUGQjbmXXspWozrkKNHikpzWMgDmuj9k2fx3LPo0c\nDSEq295fDQlKdxcfuGpOkH1PSzMHuIef26IGNDjMlOyBSY2FrQJaxpYtpQYq\nw1FC\r\n=GGKV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "0f56ff8d4b579599f9f225f0a19f4ef1628c585f", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/range-parser.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Range header field string parser", "directories": {}, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "mocha": "6.1.4", "eslint": "5.16.0", "deep-equal": "1.0.1", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.1_1557534457659_0.5973624508825055", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-06-11T16:08:20.957Z", "modified": "2025-05-14T14:56:26.210Z", "0.0.1": "2012-06-11T16:08:22.373Z", "0.0.2": "2012-06-18T00:15:02.069Z", "0.0.3": "2012-06-18T00:28:29.754Z", "0.0.4": "2012-06-18T00:56:34.968Z", "1.0.0": "2013-12-11T20:35:49.123Z", "1.0.1": "2014-09-08T01:00:41.796Z", "1.0.2": "2014-09-09T02:45:20.606Z", "1.0.3": "2015-10-29T23:12:00.478Z", "1.1.0": "2016-05-13T19:15:16.506Z", "1.2.0": "2016-06-01T17:48:14.367Z", "1.2.1": "2019-05-11T00:27:37.805Z"}, "bugs": {"url": "https://github.com/jshttp/range-parser/issues"}, "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/range-parser#readme", "keywords": ["range", "parser", "http"], "repository": {"url": "git+https://github.com/jshttp/range-parser.git", "type": "git"}, "description": "Range header field string parser", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "jonathan<PERSON><PERSON><PERSON>@gmail.com", "name": "jongleberry"}], "readme": "# range-parser\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-image]][node-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nRange header field parser.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install range-parser\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar parseRange = require('range-parser')\n```\n\n### parseRange(size, header, options)\n\nParse the given `header` string where `size` is the maximum size of the resource.\nAn array of ranges will be returned or negative numbers indicating an error parsing.\n\n  * `-2` signals a malformed header string\n  * `-1` signals an unsatisfiable range\n\n<!-- eslint-disable no-undef -->\n\n```js\n// parse header from request\nvar range = parseRange(size, req.headers.range)\n\n// the type of the range\nif (range.type === 'bytes') {\n  // the ranges\n  range.forEach(function (r) {\n    // do something with r.start and r.end\n  })\n}\n```\n\n#### Options\n\nThese properties are accepted in the options object.\n\n##### combine\n\nSpecifies if overlapping & adjacent ranges should be combined, defaults to `false`.\nWhen `true`, ranges will be combined and returned as if they were specified that\nway in the header.\n\n<!-- eslint-disable no-undef -->\n\n```js\nparseRange(100, 'bytes=50-55,0-10,5-10,56-60', { combine: true })\n// => [\n//      { start: 0,  end: 10 },\n//      { start: 50, end: 60 }\n//    ]\n```\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/range-parser/master\n[coveralls-url]: https://coveralls.io/r/jshttp/range-parser?branch=master\n[node-image]: https://badgen.net/npm/node/range-parser\n[node-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/range-parser\n[npm-url]: https://npmjs.org/package/range-parser\n[npm-version-image]: https://badgen.net/npm/v/range-parser\n[travis-image]: https://badgen.net/travis/jshttp/range-parser/master\n[travis-url]: https://travis-ci.org/jshttp/range-parser\n", "readmeFilename": "README.md", "users": {"abdul": true, "m42am": true, "glebec": true, "hualei": true, "gpuente": true, "shavyg2": true, "mojaray2k": true, "simplyianm": true}}