{"_id": "extend", "_rev": "93-349e193a8633fe5cf0862d5a189321b5", "name": "extend", "description": "Port of jQuery.extend for node.js and the browser", "dist-tags": {"latest": "3.0.2", "backport": "2.0.2"}, "versions": {"1.0.0": {"name": "extend", "version": "1.0.0", "description": "Port of jQuery.extend for Node.js", "main": "./index", "keywords": ["extend", "clone", "merge"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "repository": {"type": "git", "url": "git://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {}, "_id": "extend@1.0.0", "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "dist": {"shasum": "126c072b00c6053271a0bf4cf1777c33be023f40", "tarball": "https://registry.npmjs.org/extend/-/extend-1.0.0.tgz", "integrity": "sha512-dHKYMYG9vh4lLrVFAPTlpezYjatsHLkzr5g4+r8E+JaHXhOfn5/gD0cOVyjlR9Mr+ElGKBlU+Qd9yd9BosHRFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAzxceC59+1pQdcSzL3YT+YojvJHWX9z6rAOW3uwru3gAiEA4b61IM18pSkqyQtUDG8HzA4yQ2Ec5pGxFe813wrH5dU="}]}, "scripts": {}, "directories": {}, "deprecated": "Please update to the latest version."}, "1.1.0": {"name": "extend", "version": "1.1.0", "description": "Port of jQuery.extend for Node.js", "main": "./index", "keywords": ["extend", "clone", "merge"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "_id": "extend@1.1.0", "dist": {"shasum": "278ce9efe504035f6913edfad2008f6522c0a1b5", "tarball": "https://registry.npmjs.org/extend/-/extend-1.1.0.tgz", "integrity": "sha512-AWA32kgL1mNK7DMYGFOZrpIapBQ3ArAIfMGcGaGT4hO7HXFEwPglAc+B9g9HHtTsh5sm5+QWM3z0C+BLENP7Tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFS/OMPBsR40+q8pE4dSduTmHaoav+i4UGqo3GTMJnmTAiEAwyCmc5e7DkN6CNU4RKbl5iRuy/B8Bc1lJq1VyTMIP0g="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "justmoon", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest version."}, "1.1.1": {"name": "extend", "version": "1.1.1", "description": "Port of jQuery.extend for Node.js", "main": "./index", "keywords": ["extend", "clone", "merge"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "_id": "extend@1.1.1", "dist": {"shasum": "feef6f0efa6f8e34062da79b5a46f8b1b5cf748e", "tarball": "https://registry.npmjs.org/extend/-/extend-1.1.1.tgz", "integrity": "sha512-lPvfmuHa/75CStD7pdPyoBPMUsmpTqNe6C3l9LGrQgb3i1qT4Fgnv4MvnqWGd54YqnKD0zmkXV7ENcVtP6NGMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEvPvOLq//SdxMnZV3LthvZY1F7PU6ouViq64MXeLVgsAiBblLnL7ky9Sjjh3bhrIdJ3Phkg7zWF/2OttMlpL+XyPA=="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "justmoon", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest version."}, "1.1.3": {"name": "extend", "version": "1.1.3", "description": "Port of jQuery.extend for Node.js", "main": "./index", "keywords": ["extend", "clone", "merge"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "devDependencies": {"buster": "*"}, "_id": "extend@1.1.3", "dist": {"shasum": "5ae3d12e33009879dfb574e911a2da1e3da29ef4", "tarball": "https://registry.npmjs.org/extend/-/extend-1.1.3.tgz", "integrity": "sha512-jy2kQ/9k4JW/YjmAo5Ex90LzAAGnM2ELsN3DKhUxpgpHdyZ8mhwFJdY/lUowoRcU23sB6oL6q9P2Hp9ZxFoVdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCP/fZReR8lJgktXxzpnRlB+szCHO+wyi6TKZyOVh11QwIhAP9juIi0sxf4CI3FZ88O13a5xl/HU2mUSnzOk89RGfj8"}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "justmoon", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}], "directories": {}, "deprecated": "Please update to the latest version."}, "1.2.0": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "1.2.0", "description": "Port of jQuery.extend for Node.js", "main": "index", "scripts": {"test": "node test/index.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "_id": "extend@1.2.0", "dist": {"shasum": "da1a81af472a5a3e7fd607f85cdeaf69c169294d", "tarball": "https://registry.npmjs.org/extend/-/extend-1.2.0.tgz", "integrity": "sha512-4P25JutzkpENEx6zAf/bsu8fpIfx6fvB3JNXAsaS6nHMJN8p+tGGaAKuOZfd8pOL+VP6Q/XqXTdzymwW4iG1MQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF3yTGwDIMl789OBVa2NknP5Cnwk6kvlKE03tdlNrwDQAiAMuxkBFgxRnqR70nl/tV5iY8liU8dZSLAksdpFDtlJQw=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "1.2.1", "description": "Port of jQuery.extend for Node.js", "main": "index", "scripts": {"test": "node test/index.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.0"}, "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "_id": "extend@1.2.1", "dist": {"shasum": "a0f5fd6cfc83a5fe49ef698d60ec8a624dd4576c", "tarball": "https://registry.npmjs.org/extend/-/extend-1.2.1.tgz", "integrity": "sha512-2/JwIYRpMBDSjbQjUUppNSrmc719crhFaWIdT+TRSVA8gE+6HEobQWqJ6VkPt/H8twS7h/0WWs7veh8wmp98Ng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNRG1V0873wX2b8glG/9+V/2VNNxYsv8Hh3L/5P3PYgQIgHpysTuNHn2mHvVmmWLrfE96VOA6fbdkgonjGmRKDSy4="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "1.3.0": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "1.3.0", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"test": "node test/index.js", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "~2.13.2", "covert": "~0.4.0"}, "gitHead": "30bf952975a8551c8aabc3aed5e812455847e37a", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend", "_id": "extend@1.3.0", "_shasum": "d1516fb0ff5624d2ebf9123ea1dac5a1994004f8", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "d1516fb0ff5624d2ebf9123ea1dac5a1994004f8", "tarball": "https://registry.npmjs.org/extend/-/extend-1.3.0.tgz", "integrity": "sha512-hT3PRBs1qm4P8g2keUBZ9bPaFHAcS78o5aCd9WhFTluHZZgBEkI08R+zYrpRpImyRTH+dw7IlqxrOp9iartTkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEnAyvsfih3vtDnms6hGPAYMUHBK2YreFCBuyY2BchLxAiBaF/klA/jaF+UKKIC5KGFs4ThKjlO/KElqHwU4rBePKQ=="}]}, "directories": {}}, "2.0.0": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "2.0.0", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "jscs *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "~3.0.0", "covert": "~1.0.0", "jscs": "~1.6.2"}, "gitHead": "1766f482fcf9ca83e07ad6fbeaa079e649d5db0c", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend", "_id": "extend@2.0.0", "_shasum": "cc3c1e238521df4c28e3f30868b7324bb5898a5c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "dist": {"shasum": "cc3c1e238521df4c28e3f30868b7324bb5898a5c", "tarball": "https://registry.npmjs.org/extend/-/extend-2.0.0.tgz", "integrity": "sha512-ow3dLb5N2d4T9isTntdeu+DRixQZ1jMUfc30292ryrg6ch/9LP35PR36uvVt2+Xq7RvpTVjFw9R/txhgQZ7sIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICGKZISLn7H3rZ0uaE6j8v8BoH4m1BdRy8Osu6EKiGg1AiEAvnt2wfNnOkgF1PSikS0tXmkL1UR9SKw3wy8M3/jkRMA="}]}, "directories": {}}, "2.0.1": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "2.0.1", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "jscs *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "covert": "^1.0.1", "jscs": "^1.11.3"}, "license": "MIT", "gitHead": "ce3790222d3d2051f728f74be9565f155ed599c3", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend#readme", "_id": "extend@2.0.1", "_shasum": "1ee8010689e7395ff9448241c98652bc759a8260", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "1ee8010689e7395ff9448241c98652bc759a8260", "tarball": "https://registry.npmjs.org/extend/-/extend-2.0.1.tgz", "integrity": "sha512-jSc9G6roNopaw80wsHngeywPtLOSZEqoXtZ6BdM8dovpF5V+qd/dAHLpQA5efB9D4KXwOh9kvGy/P7PY9WW3oQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmi2HQte+Lv0gNrFMZgd1Lm7R7fF33IfAV1fHpna0W3AIgShxS7d9brJGq4hUly9K1+zNvON1F0+m+bdXkbrD4tEU="}]}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "3.0.0", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "covert": "^1.1.0", "jscs": "^1.13.1", "eslint": "^0.24.0"}, "license": "MIT", "gitHead": "148e7270cab2e9413af2cd0cab147070d755ed6d", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend#readme", "_id": "extend@3.0.0", "_shasum": "5a474353b9f3353ddd8176dfd37b91c83a46f1d4", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.1", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "5a474353b9f3353ddd8176dfd37b91c83a46f1d4", "tarball": "https://registry.npmjs.org/extend/-/extend-3.0.0.tgz", "integrity": "sha512-5mYyg57hpD+sFaJmgNL9BidQ5C7dmJE3U5vzlRWbuqG+8dytvYEoxvKs6Tj5cm3LpMsFvRt20qz1ckezmsOUgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGz4ZdFqL187MdCErwsZP9/3/zbQ7Yj7EuXBbRyn5XxDAiEAwC2fhlzvb2+Ke9fXRilnMUBtpLCajngFy559N9uG0Zc="}]}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "directories": {}}, "3.0.1": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "3.0.1", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage-quiet", "tests-only": "node test", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"tape": "^4.6.3", "covert": "^1.1.0", "jscs": "^3.0.7", "eslint": "^3.19.0", "@ljharb/eslint-config": "^11.0.0"}, "license": "MIT", "gitHead": "138b515df4d628bb1742254ede5d2551c0fecae7", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend#readme", "_id": "extend@3.0.1", "_shasum": "a755ea7bc1adfcc5a31ce7e762dbaadc5e636444", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.9.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"shasum": "a755ea7bc1adfcc5a31ce7e762dbaadc5e636444", "tarball": "https://registry.npmjs.org/extend/-/extend-3.0.1.tgz", "integrity": "sha512-u1aUSYGdAQxyguoP919qsgj24krDCtaO/DJFNPwFmojMmKp14gtCTVsc8lQSqRDFrwAch+mxMWC8/6ZJPz5Hpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVPPdoiJtS7GvDUrtLJw/jeHrfKsvP/6DyxkKkzAQXbAIhAOZzJNUU0ntc8/VfpDydYlKe2nKiPvwLw0Db/Otymts1"}]}, "maintainers": [{"name": "justmoon", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/extend-3.0.1.tgz_1493357803699_0.1708133383654058"}, "directories": {}}, "3.0.2": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "3.0.2", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage-quiet", "tests-only": "node test", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.19.1", "jscs": "^3.0.7", "tape": "^4.9.1"}, "license": "MIT", "gitHead": "8d106d23931c0802e8b88188b0aac433e13358d9", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend#readme", "_id": "extend@3.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "shasum": "f8b1136b4071fbd8eb140aff858b1019ec2915fa", "tarball": "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz", "fileCount": 10, "unpackedSize": 23461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUPRbCRA9TVsSAnZWagAAQvUQAI7RuKHKR/BtJGQTRMjw\nQr46YK0xU36ey91h+EfjKesrI+6sPi6r3Fi10zQ/IocxbXoGCfnGt0XlSD1e\nAWSMSKVxoRbOg7txu9RiMbfHZ0kdXH4WU+i83FQAG1goqDVQuNAsj4UzktrO\n6ShU1GDjmeWRmZpVwHnlpwfITGTMdHM6j7RJNnLQYoFlssatgk8txFeOzYeh\nNdBxuxtki5H297qXc0inWoNa/e+ahsGRM6dgDRvaZlWaIvDoDr6vQgpx4iGc\ndacBNB6P7SYS9ArdzUtTivvTHv7l2MHCL2JlXk3e4hq6I8wBnkQFgMas8b9b\nVGD4mUCgFwC80wQ0r19r8t9DmuOhJGobcj/nGxuvaeM+mbnuHiADhLTOV3dW\nQbnZDlMRQnODmGwxDK2yyTqZJrBzFkZvb/yRdSucxQgHkGraM+NOkbb0iSMG\nnyJK8U0EyjEvyRMMJEXwO+jb+DfULrFZHLt4whkG53G8QhoeF8qjmbZ8r0Ea\nMxuS25wP2sLS27fljk516N9WMjMh4rhhBj6j8hUDij3F0ZVy/wGnnpDrz9cU\nB+ylJKsgQb6DWMXTmj2OZ/QJny1XEebCxdceIN8HcIvxHBjs+rWBlWT+QLXU\n/wHy1hEd4ZXcV/OFQHj2M+sWhc+sUpFKvtObZbxGOODSE1IDSdisVdjwFTx+\nMEht\r\n=SSFB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxZybFUK8W8pqP3zITcYY1oEc/KMJgCw+yxg7lO++A+AiAYxGbSC57/nIBPp4VKAPHLsSPJMXl1eFaZ3Kx3pxCp3w=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "justmoon"}, {"email": "<EMAIL>", "name": "lj<PERSON>b"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extend_3.0.2_1532032091383_0.6840703017754544"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "extend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "version": "2.0.2", "description": "Port of jQuery.extend for node.js and the browser", "main": "index", "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "posttest": "npm run coverage-quiet", "tests-only": "node test", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "npm run jscs && npm run eslint", "jscs": "jscs *.js */*.js", "eslint": "eslint *.js */*.js"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "keywords": ["extend", "clone", "merge"], "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^12.2.1", "covert": "^1.1.0", "eslint": "^4.19.1", "jscs": "^3.0.7", "safe-publish-latest": "^1.1.1", "tape": "^4.9.1"}, "license": "MIT", "gitHead": "a4394292a2514adafbad2a92c1823708864a57dd", "readme": "[![Build Status][travis-svg]][travis-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n\n# extend() for Node.js <sup>[![Version Badge][npm-version-png]][npm-url]</sup>\n\n`node-extend` is a port of the classic extend() method from jQuery. It behaves as you expect. It is simple, tried and true.\n\nNotes:\n\n* Since Node.js >= 4,\n  [`Object.assign`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign)\n  now offers the same functionality natively (but without the \"deep copy\" option).\n  See [ECMAScript 2015 (ES6) in Node.js](https://nodejs.org/en/docs/es6).\n* Some native implementations of `Object.assign` in both Node.js and many\n  browsers (since NPM modules are for the browser too) may not be fully\n  spec-compliant.\n  Check [`object.assign`](https://www.npmjs.com/package/object.assign) module for\n  a compliant candidate.\n\n## Installation\n\nThis package is available on [npm][npm-url] as: `extend`\n\n``` sh\nnpm install extend\n```\n\n## Usage\n\n**Syntax:** extend **(** [`deep`], `target`, `object1`, [`objectN`] **)**\n\n*Extend one object with one or more others, returning the modified object.*\n\n**Example:**\n\n``` js\nvar extend = require('extend');\nextend(targetObject, object1, object2);\n```\n\nKeep in mind that the target object will be modified, and will be returned from extend().\n\nIf a boolean true is specified as the first argument, extend performs a deep copy, recursively copying any objects it finds. Otherwise, the copy will share structure with the original object(s).\nUndefined properties are not copied. However, properties inherited from the object's prototype will be copied over.\nWarning: passing `false` as the first argument is not supported.\n\n### Arguments\n\n* `deep` *Boolean* (optional)\nIf set, the merge becomes recursive (i.e. deep copy).\n* `target`\t*Object*\nThe object to extend.\n* `object1`\t*Object*\nThe object that will be merged into the first.\n* `objectN` *Object* (Optional)\nMore objects to merge into the first.\n\n## License\n\n`node-extend` is licensed under the [MIT License][mit-license-url].\n\n## Acknowledgements\n\nAll credit to the jQuery authors for perfecting this amazing utility.\n\nPorted to Node.js by [Stefan Thomas][github-justmoon] with contributions by [Jonathan Buchanan][github-insin] and [Jordan Harband][github-ljharb].\n\n[travis-svg]: https://travis-ci.org/justmoon/node-extend.svg\n[travis-url]: https://travis-ci.org/justmoon/node-extend\n[npm-url]: https://npmjs.org/package/extend\n[mit-license-url]: http://opensource.org/licenses/MIT\n[github-justmoon]: https://github.com/justmoon\n[github-insin]: https://github.com/insin\n[github-ljharb]: https://github.com/ljharb\n[npm-version-png]: http://versionbadg.es/justmoon/node-extend.svg\n[deps-svg]: https://david-dm.org/justmoon/node-extend.svg\n[deps-url]: https://david-dm.org/justmoon/node-extend\n[dev-deps-svg]: https://david-dm.org/justmoon/node-extend/dev-status.svg\n[dev-deps-url]: https://david-dm.org/justmoon/node-extend#info=devDependencies\n\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "homepage": "https://github.com/justmoon/node-extend#readme", "_id": "extend@2.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-AgFD4VU+lVLP6vjnlNfF7OeInLTyeyckCNPEsuxz1vi786UuK/nk6ynPuhn/h+Ju9++TQyr5EpLRI14fc1QtTQ==", "shasum": "1b74985400171b85554894459c978de6ef453ab7", "tarball": "https://registry.npmjs.org/extend/-/extend-2.0.2.tgz", "fileCount": 10, "unpackedSize": 24128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUQzbCRA9TVsSAnZWagAAglcQAJizf5PIYvnM6nlvrOR2\nNrydT3eS7o7YtqqFKHodiDfXJjHHsR1gy5bQk9QLSmMu6RkrNLmsw0KBrkYv\nDwzBQe0dl9MKY6gQD/2y8pp36TcspitV9ypnuM1q5KqBWdJlWNZTEGc8w3HD\nHHUtZ7rkTls6i9/H1Yxb3OxHiqFfugQn2iW1/zhjzgVOuAMJF5RSNGaSOy5m\nhPcslvp5YELG2rvEGhg+ALjmR1i7Jv6GjWOh84pwFVXZI2q8Q850mIm+yxxc\nfs/A8boLlTyQ8ArKmFXPWafzKMMR6U8f1VQ0LhGVtkdus3iX563pUfi/TUoO\n7P3e2d8WdLUTDE6aK4XIUo2bUxTXVtdZivGHY5W1SqWmAPr18H+N4PV1bWl0\n1rDd84aQoaMLmynCulj/VsdI/iAnG7fQTp76x7zdtwOduTZYw/KLUtSQHxXE\nyJbs4M6wLcvz2gceMcm+eyVm99aEpVKiRK4ui9s4bYE+Ak3OJPkB3mKj62fO\nWts4mRO25sLriXXp2VoBlJg8B4x4ZyHJmfDeKVW+m3om521w6ZPxDJ8HUOZ6\nKFiKSRqEV9Btcz66sonnUIyctkd/BoEvEyx9zLJ4PCBbA6y9L/4TJb1kV4rU\nZrpIv9JJhLWzz2P30nCbMsgkqnxiDZM7T29z3wP+2SOwID9QpSAW/AFv8Z7c\nZIJv\r\n=DZd8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+63rBSVeirLUlksuAWNrZCX8ZXOAKtkgtFpZxF3vL9AIhANBY0ZJaZslO5+cTWU0JWF9hoV6qP8UClME5olEWs59P"}]}, "maintainers": [{"email": "<EMAIL>", "name": "justmoon"}, {"email": "<EMAIL>", "name": "lj<PERSON>b"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extend_2.0.2_1532038363411_0.9641277645083499"}, "_hasShrinkwrap": false}}, "maintainers": [{"email": "<EMAIL>", "name": "justmoon"}, {"email": "<EMAIL>", "name": "lj<PERSON>b"}], "time": {"modified": "2023-07-12T19:13:04.515Z", "created": "2011-05-14T07:38:58.395Z", "1.0.0": "2011-05-14T07:38:59.146Z", "1.1.0": "2012-11-07T20:16:10.765Z", "1.1.1": "2012-11-07T20:21:09.577Z", "1.1.3": "2012-12-07T07:34:51.684Z", "1.2.0": "2013-09-03T04:08:51.272Z", "1.2.1": "2013-09-14T21:31:03.965Z", "1.3.0": "2014-06-20T18:12:27.954Z", "2.0.0": "2014-10-01T17:18:32.586Z", "2.0.1": "2015-04-25T18:20:29.822Z", "3.0.0": "2015-07-01T20:47:06.834Z", "3.0.1": "2017-04-28T05:36:44.288Z", "3.0.2": "2018-07-19T20:28:11.447Z", "2.0.2": "2018-07-19T22:12:43.483Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net"}, "repository": {"type": "git", "url": "git+https://github.com/justmoon/node-extend.git"}, "users": {"pana": true, "grncdr": true, "jakwings": true, "nromano": true, "kahboom": true, "liveinjs": true, "knownasilya": true, "levisl176": true, "flaviodelbianco": true, "goldenboy": true, "paulmelnikow": true, "mlm": true, "amnzero": true, "vitre": true, "ashkyd": true, "iamveen": true, "roberkules": true, "nickeljew": true, "mrmartineau": true, "nice_body": true, "bacra": true, "sagaris": true, "sivan": true, "0x4c3p": true, "pandao": true, "shadowlong": true, "yuxin": true, "wkaifang": true, "jakub.knejzlik": true, "loselovegirl": true, "codeandcats": true, "nahuelhds": true, "heyimeugene": true, "geekwen": true, "gerst20051": true, "ahmed-dinar": true, "copitz": true, "vivek.vikhere": true, "mojaray2k": true, "mikestaub": true, "stone-jin": true, "hagb4rd": true, "anoubis": true, "aidenzou": true, "meekjt": true, "landy2014": true, "jherax": true, "jakedalus": true, "dm7": true, "cliff": true, "jamal-safwat": true, "ganeshkbhat": true, "flumpus-dev": true}, "readme": "", "homepage": "https://github.com/justmoon/node-extend#readme", "keywords": ["extend", "clone", "merge"], "contributors": [{"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/justmoon/node-extend/issues"}, "readmeFilename": "", "license": "MIT"}