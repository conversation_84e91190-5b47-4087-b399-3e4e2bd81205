{"_id": "vary", "_rev": "37-e9f22b887b677fd4bb447e1a46870ebb", "name": "vary", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.0.0": {"name": "vary", "version": "0.0.0", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/vary", "bugs": {"url": "https://github.com/expressjs/vary/issues"}, "dist": {"shasum": "9edfb6837236e6fa500788995cd85a11c62c482d", "tarball": "https://registry.npmjs.org/vary/-/vary-0.0.0.tgz", "integrity": "sha512-UMiXFkp/fpgXUzzku5kywQDrCAIj1l5afYOXcW0E/7q9kf3US3ajtXnLj0gPMXNZ0fXsV1c1MTPn6MP++FnuFg==", "signatures": [{"sig": "MEUCIQCznb+5TtlXC/plVkBKjAEzmeLLPPywAOOTn629g7V8SgIgAa0N0dJjPNTTw76oI4IwU3m5rofUuL75uFGGMJHenKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/vary", "type": "git"}, "_npmVersion": "1.4.3", "description": "Update the Vary header of a response", "directories": {}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10"}}, "0.1.0": {"name": "vary", "version": "0.1.0", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/vary", "bugs": {"url": "https://github.com/expressjs/vary/issues"}, "dist": {"shasum": "df0945899e93c0cc5bd18cc8321d9d21e74f6176", "tarball": "https://registry.npmjs.org/vary/-/vary-0.1.0.tgz", "integrity": "sha512-tyyeG46NQdwyVP/RsWLSrT78ouwEuvwk9gK8vQK4jdXmqoXtTXW+vsCfNcnqRhigF8olV34QVZarmAi6wBV2Mw==", "signatures": [{"sig": "MEYCIQDYP1rcIR1JXy90iiJgY7wV0H+SWjq84LaQvTExPsStBwIhAJN40FT0fdOQBacIO6lNykstbxNKz8iQfG1XNGsTSwnm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/vary", "type": "git"}, "_npmVersion": "1.4.3", "description": "Update the Vary header of a response", "directories": {}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10"}}, "1.0.0": {"name": "vary", "version": "1.0.0", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/vary", "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "dist": {"shasum": "c5e76cec20d3820d8f2a96e7bee38731c34da1e7", "tarball": "https://registry.npmjs.org/vary/-/vary-1.0.0.tgz", "integrity": "sha512-jyU3YP3IiUHjpoy3ZC83R+cxWPJWV2c6cKvNl6xvdMNvdjP507SuRm74niIaHfvsqAk5J9j9xa9ejWSxsHNIrg==", "signatures": [{"sig": "MEQCIEWsnME2/+msL6VS7lMaxOaqLt5RjP4N8KStLJma5v6ZAiAMge9tzym5f9cbGFREg4SdpEoRO5HxITAYWMpKcaYSlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "c5e76cec20d3820d8f2a96e7bee38731c34da1e7", "engines": {"node": ">= 0.8.0"}, "gitHead": "56acecd9fa20888132563b00576625ea02a69a35", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/vary", "type": "git"}, "_npmVersion": "1.4.21", "description": "Manipulate the HTTP Vary header", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.0.1": {"name": "vary", "version": "1.0.1", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/vary", "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "dist": {"shasum": "99e4981566a286118dfb2b817357df7993376d10", "tarball": "https://registry.npmjs.org/vary/-/vary-1.0.1.tgz", "integrity": "sha512-yNsH+tC0r8quK2tg/yqkXqqaYzeKTkSqQ+8T6xCoWgOi/bU/omMYz+6k+I91JJJDeltJzI7oridTOq6OYkY0Tw==", "signatures": [{"sig": "MEQCICpxvi9FubOROcQm0FcJ5sN2N9crMCyStTQp38x1rQq6AiAu0ZNWSJJoRaR6TpVTHR5qcwxjVRLdSJG+M5PocCFpIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "99e4981566a286118dfb2b817357df7993376d10", "engines": {"node": ">= 0.8"}, "gitHead": "650282ff8e614731837040a23e10f51c20728392", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/vary", "type": "git"}, "_npmVersion": "1.4.28", "description": "Manipulate the HTTP Vary header", "directories": {}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.17", "supertest": "1.0.1"}}, "1.1.0": {"name": "vary", "version": "1.1.0", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/vary", "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "dist": {"shasum": "e1e5affbbd16ae768dd2674394b9ad3022653140", "tarball": "https://registry.npmjs.org/vary/-/vary-1.1.0.tgz", "integrity": "sha512-uM/iZxl0TaIXDYreb7fo4zACmS3hk2ywle8HR44gJ6HlqZ0fb4gjEJnMBMAmH0T1HxdGU8RlUvY9ekkoLsV+1A==", "signatures": [{"sig": "MEUCIESAOnYsTMmEFHQpCwgHg6WYaM1MhXLekV54BwFxkGYfAiEApNkQh/fLRbpK7mu58eci8Ix5rSqtgKms/0tiToEtLqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "e1e5affbbd16ae768dd2674394b9ad3022653140", "engines": {"node": ">= 0.8"}, "gitHead": "13b03e9bf97da9d83bfeac84d84144137d84c257", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/vary", "type": "git"}, "_npmVersion": "1.4.28", "description": "Manipulate the HTTP Vary header", "directories": {}, "devDependencies": {"mocha": "2.3.3", "istanbul": "0.3.21", "supertest": "1.1.0"}}, "1.1.1": {"name": "vary", "version": "1.1.1", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/vary#readme", "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "dist": {"shasum": "67535ebb694c1d52257457984665323f587e8d37", "tarball": "https://registry.npmjs.org/vary/-/vary-1.1.1.tgz", "integrity": "sha512-kCupYIvF6ltl975bvRTPK/Ml8aAZ32u1JgZ4QpuL20w3lWEJYlD90NQmXIcnAliEDN62MA4V96A2uq5cBsHSJA==", "signatures": [{"sig": "MEQCIC+rZ6ffkWDHQNPdS5/8et2nNnWk60HJv+ZJXQeuD8PPAiA/Z5L3233XhuaSByqGoZ7jui25mqW89H7cg0tA0RbVSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "67535ebb694c1d52257457984665323f587e8d37", "engines": {"node": ">= 0.8"}, "gitHead": "ca7edac6b919a45bf9e2c5cb6ba31c1790e9f046", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/vary.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Manipulate the HTTP Vary header", "directories": {}, "_nodeVersion": "4.7.3", "devDependencies": {"mocha": "2.5.3", "eslint": "3.18.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/vary-1.1.1.tgz_1490045547529_0.9355870047584176", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "vary", "version": "1.1.2", "keywords": ["http", "res", "vary"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "vary@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/vary#readme", "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "dist": {"shasum": "2299f02c6ded30d4a5961b0b9f74524a18f634fc", "tarball": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "signatures": [{"sig": "MEUCIQDg0PySt9enrEc0P77erFx92eZC4jOr49PCaCqpTuOfjAIgT/p08nmZxMXfSaU3+V3n3rLT4C1MQcBTrPECNm34htk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "_shasum": "2299f02c6ded30d4a5961b0b9f74524a18f634fc", "engines": {"node": ">= 0.8"}, "gitHead": "4067e646233fbc8ec9e7a9cd78d6f063c6fdc17e", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/vary.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Manipulate the HTTP Vary header", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "supertest": "1.1.0", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/vary-1.1.2.tgz_1506217630296_0.28528453782200813", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-06-04T20:52:53.541Z", "modified": "2025-05-14T14:56:03.545Z", "0.0.0": "2014-06-04T20:52:53.541Z", "0.1.0": "2014-06-05T13:18:33.789Z", "1.0.0": "2014-08-10T21:03:28.918Z", "1.0.1": "2015-07-09T00:03:07.257Z", "1.1.0": "2015-09-30T05:05:50.045Z", "1.1.1": "2017-03-20T21:32:29.340Z", "1.1.2": "2017-09-24T01:47:11.325Z"}, "bugs": {"url": "https://github.com/jshttp/vary/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/vary#readme", "keywords": ["http", "res", "vary"], "repository": {"url": "git+https://github.com/jshttp/vary.git", "type": "git"}, "description": "Manipulate the HTTP Vary header", "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# vary\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nManipulate the HTTP Vary header\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally): \n\n```sh\n$ npm install vary\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar vary = require('vary')\n```\n\n### vary(res, field)\n\nAdds the given header `field` to the `Vary` response header of `res`.\nThis can be a string of a single field, a string of a valid `Vary`\nheader, or an array of multiple fields.\n\nThis will append the header if not already listed, otherwise leaves\nit listed in the current location.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// Append \"Origin\" to the Vary header of the response\nvary(res, 'Origin')\n```\n\n### vary.append(header, field)\n\nAdds the given header `field` to the `Vary` response header string `header`.\nThis can be a string of a single field, a string of a valid `Vary` header,\nor an array of multiple fields.\n\nThis will append the header if not already listed, otherwise leaves\nit listed in the current location. The new header string is returned.\n\n<!-- eslint-disable no-undef -->\n\n```js\n// Get header string appending \"Origin\" to \"Accept, User-Agent\"\nvary.append('Accept, User-Agent', 'Origin')\n```\n\n## Examples\n\n### Updating the Vary header when content is based on it\n\n```js\nvar http = require('http')\nvar vary = require('vary')\n\nhttp.createServer(function onRequest (req, res) {\n  // about to user-agent sniff\n  vary(res, 'User-Agent')\n\n  var ua = req.headers['user-agent'] || ''\n  var isMobile = /mobi|android|touch|mini/i.test(ua)\n\n  // serve site, depending on isMobile\n  res.setHeader('Content-Type', 'text/html')\n  res.end('You are (probably) ' + (isMobile ? '' : 'not ') + 'a mobile user')\n})\n```\n\n## Testing\n\n```sh\n$ npm test\n```\n\n## License\n\n[MIT](LICENSE)\n\n[npm-image]: https://img.shields.io/npm/v/vary.svg\n[npm-url]: https://npmjs.org/package/vary\n[node-version-image]: https://img.shields.io/node/v/vary.svg\n[node-version-url]: https://nodejs.org/en/download\n[travis-image]: https://img.shields.io/travis/jshttp/vary/master.svg\n[travis-url]: https://travis-ci.org/jshttp/vary\n[coveralls-image]: https://img.shields.io/coveralls/jshttp/vary/master.svg\n[coveralls-url]: https://coveralls.io/r/jshttp/vary\n[downloads-image]: https://img.shields.io/npm/dm/vary.svg\n[downloads-url]: https://npmjs.org/package/vary\n", "readmeFilename": "README.md", "users": {"mojaray2k": true, "giussa_dan": true, "goodseller": true, "jessaustin": true, "simplyianm": true, "wangnan0610": true, "leland-kwong": true, "netoperatorwibby": true}}