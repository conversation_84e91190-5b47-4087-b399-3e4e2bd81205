{"_id": "source-map", "_rev": "205-98cd2c220f38cd0d773c5d6bde8842be", "name": "source-map", "dist-tags": {"next": "0.8.0-beta.0", "latest": "0.7.4"}, "versions": {"0.0.0": {"name": "source-map", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.0.0", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "44220b0adf1572e603614d853727d3b05078d56c", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.0.0.tgz", "integrity": "sha512-NFAVRu1LLObrZFGIsM8SNgSqJtGcYbquqT06sI5ocWievxyiCBueoqT+yGpJ+e4vrULtaxhGEbWtjq/icGbP5g==", "signatures": [{"sig": "MEUCIQDlyGkb8ejOIvwU9qG/0YPf9GGqRw7bFb4SkYRGyCPXXQIgY46sfLRHsfz4zVhqQn4XFb7MLRgs1ivxB6bvPMFfm7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "scripts": {}, "licenses": [{"url": "https://www.mozilla.org/MPL/MPL-1.1.html", "type": "MPL"}, {"url": "http://www.gnu.org/licenses/gpl.html", "type": "GPL"}, {"url": "http://www.gnu.org/licenses/lgpl.html", "type": "LGPL"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/source-map/0.0.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"requirejs": ">=0.26.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.1.0": {"name": "source-map", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.0", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "229a5427719f1971be234b37cf968538b0600136", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.0.tgz", "integrity": "sha512-XnamEXxnJC2B0HVOfVYsEtRGdPt8Fu6v0YXSoXlLoH5unX+nXLB6NMvc4/pv9C4viRc7/HaBSv9SwiYTV7UMTg==", "signatures": [{"sig": "MEQCIAxFvythkQnW60220OudHMz9hds+xN9AAofDqMo83c5VAiBozfUQk+7ThMpJyIeUMmfceFYA1maFD8QJ49IwPfgf2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "scripts": {}, "licenses": [{"url": "https://www.mozilla.org/MPL/MPL-1.1.html", "type": "MPL"}, {"url": "http://www.gnu.org/licenses/gpl.html", "type": "GPL"}, {"url": "http://www.gnu.org/licenses/lgpl.html", "type": "LGPL"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/source-map/0.1.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {"requirejs": ">=0.26.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.1.1": {"name": "source-map", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.1", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "524b0c511d53c3b1a76a211c084361bde15da726", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.1.tgz", "integrity": "sha512-oCRWrHvj0njD0AY9FXc5YugZbX7BQ/3CUdHp5CD+Om/YBZX9X89KJYg6fI+g2tq4UPDq7CpM9Z17RjtRjCMV3Q==", "signatures": [{"sig": "MEUCIQCcpc3HlEsP57K757PoEv0P2mtOHLN8JAlxlVNC4tuQPwIgft6ct4X0NwuntcFTsHNpiloG75EtyogPxdH9G28arlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.6.19", "dependencies": {"requirejs": ">=0.26.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.2": {"name": "source-map", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.2", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "7132efc1fa95273bc71bff8f8202d7b4eed57993", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.2.tgz", "integrity": "sha512-2zlER8v057dkDIXq/hNRbmnVyL7Fpw8VpnJ4W8hr6Pnh3/2z1dS5lyNiBJkS3lVgnv4B+iWgHdh0yTKCx1i/HQ==", "signatures": [{"sig": "MEQCIDB6mhFxQOX32ldTOMT0GDTdkqT7luEz5kFCNNIK04KPAiA4WZRnvA8v6kq30+7qw/a+qL3ckCqRlMv3ZtHZNyQN2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"requirejs": "==0.26.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.3": {"name": "source-map", "version": "0.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.3", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "ed3165c4d0f0a6291e53e485cb95b5f1a0a25183", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.3.tgz", "integrity": "sha512-KNUc1sS93Y/OV2WyI9UaAHV3OvBK7/74LxueBPjgkRAGSF+f3FPmUf4lbQI/e4b54L/0ZAIYSlFp7mnnU7vv7A==", "signatures": [{"sig": "MEQCIGE2FzaCxqreWWLGpOwIuUWc79KnLPyjm8CC0JUXLtaqAiBatlz8YVC/GTLHACpJgKZjcQG5lv2nGaNxhBLEydWhEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"requirejs": "==0.26.0"}, "_defaultsLoaded": true, "devDependencies": {"dryice": "~0.4.8"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.4": {"name": "source-map", "version": "0.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.4", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "cd67f54e9c4fdc713c00a869241c419e9fedace0", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.4.tgz", "integrity": "sha512-IfyH6tzAt8gpC8MTfk5f1GG7Trd1CXMorkTEsOXgKbZ1dIhY5D7RYphFs7EQw4zNOmV4rLWqqxZO1cHyRQWM7Q==", "signatures": [{"sig": "MEUCIQDN3BxbQGsXUn8Q67FdxaWR3DEgFvOoqlcToyVZSQMJ1QIgaIykrrXLfHN81X110+jgkfzAKidKWv8gRVTQWN8Ck9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"amdefine": ">=0.0.4"}, "_defaultsLoaded": true, "devDependencies": {"dryice": ">=0.4.8"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.5": {"name": "source-map", "version": "0.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.5", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "63aaaee4c9dc9bea95ba9469a80775576927bf08", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.5.tgz", "integrity": "sha512-vvaEptThqdLPoeA6yD4uzq2PmV+0HRrPkiBZJxtODKEqvK/qOqeR8Vu8jQ0Wab4atLPxHLqIDPB75Kb4rDMfiw==", "signatures": [{"sig": "MEUCIF+ISOpBlcNYbjsyqW3/wEaaqCV0PdAAhppPVT1pULjYAiEAh9rb1fEkAQ7fUInGPQI75J6eUkAN7LBYOPbc1uL3tZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"amdefine": ">=0.0.4"}, "_defaultsLoaded": true, "devDependencies": {"dryice": ">=0.4.8"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.6": {"name": "source-map", "version": "0.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.6", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "0035ddb2d9a191df8c8b13a0362eb618d881ea34", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.6.tgz", "integrity": "sha512-28LTwX+tpqQxVz661uuGwNSXFv+lRTv0G5eYpzDfrrUgkg2RDSx2wlbCfK2H+kGpF0/wmzemc4IlEMKa2IvyCQ==", "signatures": [{"sig": "MEYCIQCk49y0BDblL+Zawfq/YnXc/JSvIuvctDYPxNPY3sMk3QIhAIsrR4WNEe6+3LcZqdljCzYvcDaehtV/1HEEiS/nadyQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"amdefine": ">=0.0.4"}, "_defaultsLoaded": true, "devDependencies": {"dryice": ">=0.4.8"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.7": {"name": "source-map", "version": "0.1.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.7", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "92da34014a5576d60676150bcf0f55cbd1f395c0", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.7.tgz", "integrity": "sha512-w+KE48PaQuM3nrnj1IXLnLdmGY/XqJKv9LEAD7j3uAnpLhQmV7OTIxtxz6XsH5p5c7xKq35i6yaL7w2JrGFShA==", "signatures": [{"sig": "MEQCIAjSezp094uUR5rXXUI3CnnL3EFSKglUaShAzepm4qbfAiAgOOHQCXcdbCZX4DXqWPWIhEmkj7nnqdwcFrja1JWTtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"amdefine": ">=0.0.4"}, "_defaultsLoaded": true, "devDependencies": {"dryice": ">=0.4.8"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.8": {"name": "source-map", "version": "0.1.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.8", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "0bcc088a50ed8c586f50c8da4833a27dc0cc0c30", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.8.tgz", "integrity": "sha512-cRNnzj32eB4RnnoUThDbSMvyvwx8vf7crdyjehMo4SsmIZBXLRswQf2atJozDN+Dcs4bom6Fezl1ihgZumuLrA==", "signatures": [{"sig": "MEUCICHixFSkEUp6/rkn3VcuINIqQpY3ONeVIuRCW8u1w9cwAiEAppEjuAU5dA8R68ALnIc5dpiE6jLGneax3j5jHh4THSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "mozilla-devtools", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "git://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.8.5", "dependencies": {"amdefine": ">=0.0.4"}, "_defaultsLoaded": true, "devDependencies": {"dryice": ">=0.4.8"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.9": {"name": "source-map", "version": "0.1.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.9", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "250224e4e9ef7e91f4cad76cae714b90f6218599", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.9.tgz", "integrity": "sha512-Mng4gipySnzGC2nAOJZ8FclGkAjRAVl2xXWW1LdgCTmPMNkZZ4tsvH2ncXbCF+8Zjwzw8E8FVnbOxDLX1NJ2Kw==", "signatures": [{"sig": "MEUCIQDE/Ie7vIYXqEEzUQjKsL7vv7pOachDIISNtJrKzN/s5gIgB3+pXrnO+Uipfnsi0GSWkP1N+ilR+SLYzDnQmJ7/U1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.10": {"name": "source-map", "version": "0.1.10", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.10", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "0bdafd2e1e97d147a862b7c7d1b26387ecc93aa9", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.10.tgz", "integrity": "sha512-YW/WjLPxnMkvpA7uGMqg0H6kLFQ//zK+C4h5gWWC9jujAQdTUGiUdgkMDLBxMynuyxcparVOAx4MN7v76aOdhA==", "signatures": [{"sig": "MEQCIFNHx/IDeW8Mdyq8k83MgsUmStlVaKMRvp6O9n+zVAekAiA42zt3N+t/ACQ74g/Kd9mXyBq2laglY8IUoWTqJxAGtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.11": {"name": "source-map", "version": "0.1.11", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.11", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "2eef2fd65a74c179880ae5ee6975d99ce21eb7b4", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.11.tgz", "integrity": "sha512-/32+GwjyioXGrcJ+8FK3/WAVIfC1EFo8pgeiuMouTFrCTHZW2pc5qBToOuZgBR4RLTSRnUhcPNK4Fx5KwHV9bA==", "signatures": [{"sig": "MEUCIQC1BVIjY2qtIcpigxANLRBLNdgVuv1lNEF76LOfPwADzgIgJtLckyOoqA6003Ziv5+Haxe3LezTt5rqbYF436hyqvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.12": {"name": "source-map", "version": "0.1.12", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.12", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "55d5a5c58d98ffadacc4a8a7fbd7c9d4e459abd2", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.12.tgz", "integrity": "sha512-xQVA+H4jiqGgAmkJIUFtAhLqLwFxzYn2QTBPW3bCXPlegOmEQ5QEicePbrZj0iprB6nlEEPiY6TN9ymWWoLaeg==", "signatures": [{"sig": "MEUCIQD6GbuPs4PDsIYgICkDf7c0n03CEE5pXpgZ7wX+YXJZTQIgTfbnJ914yVp9bdp2F2ktUWESilLfagzb5PxxgL4M+yY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.13": {"name": "source-map", "version": "0.1.13", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.13", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "cc3d10be14dcafbd8f8259546438f19b72995a1c", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.13.tgz", "integrity": "sha512-f/JBMURtDPyVWfllJchypSnV3thR+ChdVUwphyycImeLfRB5pwuiNLg0ArenxeZClQ3rQ6CKyf3//71gMX31vQ==", "signatures": [{"sig": "MEYCIQCnFLt46BDvzZcK2k1jtunVb5cXmHXlkmIYpPPGfKy3MAIhANWkY/OGLbHw9q/9FRYcshYP3KnfVKEOW71Zrs4d2ph0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.14": {"name": "source-map", "version": "0.1.14", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.14", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "81fb3b85f54abb1ad6a8e3f92e55b8fcd3eb836d", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.14.tgz", "integrity": "sha512-b1WHWKyPmI67BEjEUbqEEBFh7Q3uFJaCW+g8BYur7kAv4Smr98qS9qhIhs8mW+gNdm5Qrhkxl+e529Cju8PuXw==", "signatures": [{"sig": "MEQCIHChnkiyHUPU7l/Eu68Ht47YJZOKRezu8rTRnBvzVkQgAiBfGGP3fqGB+9lF49WN3RSFx7ZJr3Rgg2UVEpvGaPyZsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.15": {"name": "source-map", "version": "0.1.15", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.15", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "4eed4690db25839af3505ea4d018b6f5523f0807", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.15.tgz", "integrity": "sha512-QrEvXkIggRYh0/fUiPtm6M5voOYUwDeRfL0UAgp307Z+/PnepfZHmuYLn3D8yer1Mn0+crCtYWNepAh6uW0JMw==", "signatures": [{"sig": "MEYCIQCJxkOr0QcIBU/1T2dfsWNPCfT10Ggbkid17Qi5qqfOawIhAIh7Leu/5Ltc98Dl7DuAb/8LTm3QCUhAmSwzjOJxnvOz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.16": {"name": "source-map", "version": "0.1.16", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.16", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "4a1b371e9abd69a45fa698a1d281e2b213333dca", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.16.tgz", "integrity": "sha512-C9K3IisBzhg0YWJeErUpSjsRX8H2mAWGDPLM5DdXMIgnBfc6AGP8XU7wRiHCbjmsiMNnQAdLX0Y5hsaqzl/Jdg==", "signatures": [{"sig": "MEUCICVR87Kjfvq0/ARN4oHCWDGTPiUhN97S0CXPTNUlYUWWAiEA5q5esQVueh+jfTtWyOzj+gqjNGhfIBpXmegDU+Qwc2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.17": {"name": "source-map", "version": "0.1.17", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.17", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "1b34e5b30616d48137d604b996cf5585dc78e204", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.17.tgz", "integrity": "sha512-GZTADpE9eY07Hw7Hr4zDeOZy4vpVuBIBOpGXNOH6XPU/c/FaDuRk/VnBwP5LwBBMWRoPOZpTWu6QlYbhaU5ZFA==", "signatures": [{"sig": "MEUCICLc3TlCWC+jOuesjSfH6bAmG9Rk0zssCpXBvtU0zyptAiEAxQpmEjhDGzXEYnInVhTlgbwFls5O7sE6Sqt9RPSO3OA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.18": {"name": "source-map", "version": "0.1.18", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.18", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "6645e412fe48f748ba3a4ae56ed53f38a550f17e", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.18.tgz", "integrity": "sha512-NQ+E49peTslQTpOwkL8tZUVefUTiI1QKjynD2sy4Qa6GvvAJWP2y0pr441KgYM6pRFgonoJVsWIEqMvp+C4zmQ==", "signatures": [{"sig": "MEQCIEKHJYbvtGf+IoCN58rUnJjnHL2xsj7fUuxwsJ6KQgSuAiBF8dUDyiWfFSVontkXEpJIjtMHuEpulzdhxafDCmguoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.19": {"name": "source-map", "version": "0.1.19", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.19", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "61ce72499b6caaaa15484bd4b5dc718b0aebf76a", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.19.tgz", "integrity": "sha512-9+SEkrrPKHBdX9FCW7v59Q4vlfV1X4Yi4hgMt+e8Eo5wKPpQQC6Z+evh0/1wTtjbTZ3fg/HPHioCkuiGR6VfxA==", "signatures": [{"sig": "MEYCIQCjUDwr5E2tlJ5Ax9Ypr2TGf5TOM7XOIOaBW+kN9WC/yQIhAJD2IoEG/Wc3B9qr1XT7sKMCFil0nVT9NJ+78pEoTDBz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.20": {"name": "source-map", "version": "0.1.20", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.20", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "e41ce3288cac4c4f6eca181b53ae12558e7ad02a", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.20.tgz", "integrity": "sha512-QaCwjGTrTg+ubYG/d1pNkBo0a0EtNB0tkyNklaPgJr1Ly+8o3lwQAaJCSAzjx2MpdyKm95+K11ICURv+17LUeQ==", "signatures": [{"sig": "MEUCID2OlsKaaYO/f1o/IbkEEURN92RpbznPWTRHolwkq3JaAiEA01IcuLEpFDb1wLD+3YgXobZs3WaN2GVyADUGtSPB1Ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.21": {"name": "source-map", "version": "0.1.21", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.21", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "ecd4efc4c3579de66b95913dda08c72ea0256469", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.21.tgz", "integrity": "sha512-1xiMBfkTOUQR/bLyb0e8rAmQSiHkyqhPuV8gSKZ0ikd5RXt7JumOm/BXq6YC0NWoYewyzbLNiy2j0vCfbMZjgw==", "signatures": [{"sig": "MEUCIBnOd5LdBNBlX9TJPdXJEkkzWOSo2nUDYMr9runzZfWzAiEAgIQ6rzKV4KM55e3kLKopi2iGOKAPh0e2uWq5qSkGbA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.22": {"name": "source-map", "version": "0.1.22", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.22", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "github.com/usrbincc"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "425906162f81bf110552ccc9931dba079e9f1341", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.22.tgz", "integrity": "sha512-1Sgz8HqDxL9PI+43U01qMUI9Z1jZePsEynvzgfGcghVsNWDFh/Z5hQ4f12gQ6O6Ly7lHuj68KMX8csSCFyoXKQ==", "signatures": [{"sig": "MEQCIFWNf/739WZBA6GOJoSiYZ6mHoMBgAjM2cdGMWczs9wtAiAetaN+AdCclDWdRYMk5pYlU4vEpdUZtGWCZ3+d1+KM3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.23": {"name": "source-map", "version": "0.1.23", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.23", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "github.com/usrbincc"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "079f8fa7867f318b48cf5d4c716ab6d4dc859be9", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.23.tgz", "integrity": "sha512-50T9Uzx6Mw1Ikd9pxStyb5DIGhvvMQdVdwy93YlgVx3eS+Jcq+3hO5ynAv6TJGnCnIEwSM4itCjIvxoHk0GGRA==", "signatures": [{"sig": "MEUCIQCHT69giC0tmbGbqs/X1R9ADQehzrFQQyk3hIIMEAIQNQIgDAEhIZIav8UYN53MLiKIfDXsjbKx0hjl51uYdlZ0Ems=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.24": {"name": "source-map", "version": "0.1.24", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.24", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "github.com/usrbincc"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "5b533dd08948de2d8c3b002a9c31842cb1b35103", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.24.tgz", "integrity": "sha512-/sRNqTalV7TVLik0wM5G9PR2LjAbsGJh5ulQ5DtIoxeKvTi3TTphEgawVgmiQUvDct6Ja6WCQcxkX8R3wVvlfg==", "signatures": [{"sig": "MEYCIQDj8gHmcQEiDR/hH6tUZMh/j37+tFMjxV+ku/LzIiEwnAIhAM1eqQeuvMGaw+lnqPw5Z1yAvCYTKeei7s2deLLxoeM9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.25": {"name": "source-map", "version": "0.1.25", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.25", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "5851545c1f4a40243829065c20e6f40b023fba1a", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.25.tgz", "integrity": "sha512-3WIHR+Lnqu8KVh+ShT1Y9wSrF8tx/Df2zgDcnNhvE9M/2STspZE0cGIg0wQSG3cVwjSjbkgL5cF4g6FCCFB5Jg==", "signatures": [{"sig": "MEYCIQCTmDjFNRBFSHcutyhb1Kmu6IsH/vo+/UOpXe88TJlGygIhAIU3iyzYLmMhd3InzqScE7VkCh5WIOIOolmqGd+7rsvh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.26": {"name": "source-map", "version": "0.1.26", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.26", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "4cce2dcb5fa02cfe1b4cf32cc83962b1b3997560", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.26.tgz", "integrity": "sha512-s+AbKwK602CKyafNAufmKM2/nw89coKgesppy/uhN8OrZwOD7kQsHprCJxllmYjCfvJW/2AMxL5/wGUWyXgs/g==", "signatures": [{"sig": "MEUCIQDvnU/oN7ZCDax+O/p4zwTb+TJzPt98lG39SSXkz/eFggIgK5syM0Ywq711P6Vx5UujA8JQdnQn9shSIxYHrm7svnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.27": {"name": "source-map", "version": "0.1.27", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.27", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "f114e06a8b5c05cbc51aa1fa600e728162455eda", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.27.tgz", "integrity": "sha512-Q39uRzHeN1Wtw63pQXMErr4s8U7IEEZ0HPOlTTS1B+cd8zzU6PRMBUVOy4jHxaqJKVDa1/TFhoE+y31iVbwLgA==", "signatures": [{"sig": "MEQCIEYVBQdHBh5n03gg4YVYsEeD5S/CuSKS/+bci2UatOFkAiBVe044+guXiDrZB7AKxgCHv+hR9xntd+3LZWVunxudeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.28": {"name": "source-map", "version": "0.1.28", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.28", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "9cae9d9b8352fb030f77c4e12226cc28cb251f39", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.28.tgz", "integrity": "sha512-5eK1mz3ehRAIydz3tlkhPEZEZUZSCpnWevQzSq4d8yheVzFrsvDDJbhc4SUW7jpKWaZk52lbvO05tYWtZbqu8g==", "signatures": [{"sig": "MEUCIQDgajizBJTi0ewtn8AGexwUdN+dtMxRL5jmgX2OswSJlgIgWD1l9D5gLOKVB+2rNpDuWMauIAr7kIq0Yfz7aEfcUUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.29": {"name": "source-map", "version": "0.1.29", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.29", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "39d571a0988fb7a548a676c4de72db78914d173c", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.29.tgz", "integrity": "sha512-/XGYfd2ykeYNmXopQsGZK+oMxR7A48VBHK7u8610061aivyuopi86C87gG2yfQeTsQxQDw0N9cyP5JQIbnn9UA==", "signatures": [{"sig": "MEYCIQCajzK3SYylkWc/3JQ3hs7pldvMKa/warmlxjHW2/B0UgIhANADApxW1dVNFZefZEqBpB3G3lvTRSun2oWYxMYLm2Pu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.30": {"name": "source-map", "version": "0.1.30", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.30", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "182726b50671d8fccaefc5ec35bf2a65c1956afb", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.30.tgz", "integrity": "sha512-azjGocEF+vixFej9v+zXJpP1YrTdMWp+lkJbRDiMzEqxjdTaQIFeKFBWVUucB5bwIzovwuRiVjie/l1du4vKkQ==", "signatures": [{"sig": "MEQCIE62d9nYrM6Rma2KkiUJmRBdpt+AD6aBFOOnKzGh0xB6AiBE8ELctYd4iXj2htZL0kgVQpOf/CqUwP+GqognjU9vDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.31": {"name": "source-map", "version": "0.1.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.31", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "9f704d0d69d9e138a81badf6ebb4fde33d151c61", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.31.tgz", "integrity": "sha512-qFALUiKHo35Duky0Ubmb5YKj9b3c6CcgGNGeI60sd6Nn3KaY7h9fclEOcCVk0hwszwYYP6+X2/jpS5hHqqVuig==", "signatures": [{"sig": "MEQCIBoSgEWhK9mtNvwJGys8Su+vO3nlH39NyByvjQEZtvA6AiBRgdKuJZ6Pk/jCwMVHnN0GbkjNwckcbzwGdxQJX9tIDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.32": {"name": "source-map", "version": "0.1.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.32", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "c8b6c167797ba4740a8ea33252162ff08591b266", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.32.tgz", "integrity": "sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ==", "signatures": [{"sig": "MEQCIB/JA/aFdgRpQLp+4Hz1e1rwDVp8dI1BB6wr3yMhQ7n5AiBN/zhfbm3Lx4dxU+Q7igWItiaAExz8esCCwiCggZSb9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.33": {"name": "source-map", "version": "0.1.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.33", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "dist": {"shasum": "c659297a73af18c073b0aa2e7cc91e316b5c570c", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.33.tgz", "integrity": "sha512-bled/6w7dhwC1MDeEDF+y3ojAzDZsz0N2ki61/YaGVQu+dGsm+VOvtGw2ZLomckp66Fuas9D9zsQkQqqgXnvhw==", "signatures": [{"sig": "MEUCIQDJS+9fVxV/RWluyYYYnNPnvBnViv5Hfyx5SJy9g7cP6gIgW7EuDQmhp5rGR1O3tjA0ktkjirY85Tp9VKq8OyN+OTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.34": {"name": "source-map", "version": "0.1.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.34", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "a7cfe89aec7b1682c3b198d0acfb47d7d090566b", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.34.tgz", "integrity": "sha512-yfCwDj0vR9RTwt3pEzglgb3ZgmcXHt6DjG3bjJvzPwTL+5zDQ2MhmSzAcTy0GTiQuCiriSWXvWM1/NhKdXuoQA==", "signatures": [{"sig": "MEUCIA9s262sV6IT3s0JvjD+nUkmO997d9mF6NXv+zVmNmlHAiEAu1SGp0ljDsXl4mHNnXdO7zwPppfxP5xltmVfyTKPSUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "a7cfe89aec7b1682c3b198d0acfb47d7d090566b", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.35": {"name": "source-map", "version": "0.1.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.35", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "06a1b58b57ac8176476b74f3c1ece4925cea2e8c", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.35.tgz", "integrity": "sha512-dmnvgJQlZ1uj7hU1TNauhy3iFsXGQwgr5viox9EZKk2LWuwE5uzQT1H6fGarddmI3pcSquRDWONb56fR5iAkMA==", "signatures": [{"sig": "MEUCIQDzs+kPDr3PNUjPi54iyiHHCnM4kImj+B8wdBurNHmfkAIgKBamWvpwbIA5O1u2fiRwf3klAoCPy6n3wlltEY86N64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "06a1b58b57ac8176476b74f3c1ece4925cea2e8c", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.36": {"name": "source-map", "version": "0.1.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.36", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "7af95322decf58cc78a18d5a0d4ade258ae31c35", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.36.tgz", "integrity": "sha512-4XWYWF3SDexPHeV9BgFQSDat7ukmnud5pmgx6GtA7RiNOJQBmirFfY+P34mlaG3K/Fjc5HXogMi/wWEKBRVcuQ==", "signatures": [{"sig": "MEQCIEYM3FQ1y/L0wPt103iFRFnO9heK1DU+A/GRrCyXdxIuAiBwXe7XL0EigXkIOd1AvfVtjH3i4VgsXXvWyQc9DRz1zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "7af95322decf58cc78a18d5a0d4ade258ae31c35", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.37": {"name": "source-map", "version": "0.1.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.37", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "511fa6ed1685cb37e70ae1de2966405096054832", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.37.tgz", "integrity": "sha512-+ocd2JnHadJ62zqa5xp6IRu2lJvFKpP2SeuHII1o4QBf0OCLBZ5UnVEuIy8K3XWAp9KhHnFXj98/Bcfvo1lpxw==", "signatures": [{"sig": "MEUCIQD0SPTw+PfjR2fRyszEoRd8m0Cz5REezpwMW/NkfKb37QIgJ8IIzO0L9+tBGrI6SdwAMariW1NT/GIEP8K7lYbLoEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "511fa6ed1685cb37e70ae1de2966405096054832", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.38": {"name": "source-map", "version": "0.1.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.38", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "f93a6f9d96a5b9cf5494c043497d9542f9fa6b33", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.38.tgz", "integrity": "sha512-gATsj0h69ctEQbieEXcmysXmDR/eZ95fGPTiJMGCviBtSC9vf/Ljmvepv2RTpF8AiA4oV6PCujqQ40d2yvPkbg==", "signatures": [{"sig": "MEQCICAt8HinmUciUWjdoJLuRuXOfTVqwsxgUSOgxQqQVBv8AiBhmXUBrgIljS7T7sfLe3X5SsIV8qFVWsHZC/5HHS1img==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "f93a6f9d96a5b9cf5494c043497d9542f9fa6b33", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.39": {"name": "source-map", "version": "0.1.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.39", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "64ad329c4761ab956ff7d011c6b205aeb66a2d4a", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.39.tgz", "integrity": "sha512-MiCty8yXfwPQ42cqHbPUbS3M4DK6Wt+oq6MXitiShP0FI39BZdvIyjWY6+2DzUGfzbyZ8MqPtBqNfypsJWlSYQ==", "signatures": [{"sig": "MEUCIQDQ7aMI9asGAPEJBS4M7ZYddzTo7VnpiZJY/+Eqq3w+QAIgNqUW6CnmTw5Gqb3DM2EV2ijtKp23wgmnryRUqV++1s4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "64ad329c4761ab956ff7d011c6b205aeb66a2d4a", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.40": {"name": "source-map", "version": "0.1.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.40", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "7e0ee49ec0452aa9ac2b93ad5ae54ef33e82b37f", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.40.tgz", "integrity": "sha512-HqDIJdduqqp2qy0BKV+19pUcMBIuJOKTUfEnFWAtHvLxptZeqBl6jRxCxFYhQGrvq9fJE2HTICp2BWjKLdWPWQ==", "signatures": [{"sig": "MEYCIQDpUfCSDUo0Y7hkbws0WD6gHzzixn7CiaWCbXBasMdK4wIhANjPqM7SGdJz/MjTkng6U3vKpKrgs+xIDw8rjFaHYDNW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "7e0ee49ec0452aa9ac2b93ad5ae54ef33e82b37f", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.41": {"name": "source-map", "version": "0.1.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.41", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "d6cc9c8a9bb5d995dc69b38457bd4b1747327f5a", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.41.tgz", "integrity": "sha512-5+m6cR4TZwyCNqossuf4ngNJFawVzRQL8Tvm3S9qGBGd0kDku51MqFdZu0wDU3WsX/NJSA2wRpJ3YxiqsfzS6w==", "signatures": [{"sig": "MEUCIQD0/R82mRufBjUhqoxzJ2eCRo25a7rJxf844edI3DBRmAIgQpUTftIOOXny6ABiTqeaoN/gyV6kTrpIrQZKI0CIbZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "d6cc9c8a9bb5d995dc69b38457bd4b1747327f5a", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.42": {"name": "source-map", "version": "0.1.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.42", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "37c2958c79fee8d919173e5a7ccbd933b2ff835e", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.42.tgz", "integrity": "sha512-xgd1jQodLuEIH270zvt+ZsR2X+rpPDxhnzzpyup0eNZWMKAO0q4ksUgT6WBjIkiN6Zpqb+AVf9B73VDJYexEog==", "signatures": [{"sig": "MEQCIB8kyf9d/RxWFHMTNdRUP7oP4Pfi30uYzqdPWX9PsNmMAiBZvuS+AmIs6FrFXkM6ZsRWHmOvTVLyADiXgbAmoTzYIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "37c2958c79fee8d919173e5a7ccbd933b2ff835e", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.1.43": {"name": "source-map", "version": "0.1.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.1.43", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.1.43.tgz", "integrity": "sha512-VtCvB9SIQhk3aF6h+N85EaqIaBFIAfZ9Cu+NJHHVvc8BbEcnvDcFw6sqQ2dQrT6SlOrZq3tIvyD9+EGq/lJryQ==", "signatures": [{"sig": "MEUCIQDjg4T/nPQAmpg15lHvmpb/8zbPfgr4KwlYw/TD9IXIswIgcbmJOSXPUCq4DNtRi0TpMUmDmQ7g/FM5setINo+waIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "c24bc146ca517c1471f5dacbe2571b2b7f9e3346", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.2.0": {"name": "source-map", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.2.0", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.2.0.tgz", "integrity": "sha512-CBdZ2oa/BHhS4xj5DlhjWNHcan57/5YuvfdLf17iVmIpd9KRm+DFLmC6nBNj+6Ua7Kt3TmOjDpQT1aTYOQtoUA==", "signatures": [{"sig": "MEYCIQCoX05H1i+mIl1yMBO2rQG9PCfh59wUEyuMYN5KAqnqUgIhAMrT2hNkDv59J0wgI0/O9coRo0TrN/HFWjXcdBbk+4mm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "dab73fbcfc2ba819b4de03bd6f6eaa48164b3f9d", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.3.0": {"name": "source-map", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.3.0", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "8586fb9a5a005e5b501e21cd18b6f21b457ad1f9", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.3.0.tgz", "integrity": "sha512-jz8leTIGS8+qJywWiO9mKza0hJxexdeIYXhDHw9avTQcXSNAGk3hiiRMpmI2Qf9dOrZDrDpgH9VNefzuacWC9A==", "signatures": [{"sig": "MEYCIQDjbrxjUuPe0UrdYompEi5pTd971XDGUwzKhEiQXFLlNAIhAOmo6UXGHhxzQTpdIdEWlzSKnUP1h8xIg5OB/YX5NrvC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "8586fb9a5a005e5b501e21cd18b6f21b457ad1f9", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.4.0": {"name": "source-map", "version": "0.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.4.0", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "c6ddd824fca4ccad4329f8f37903c1bc902c2bb2", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.4.0.tgz", "integrity": "sha512-Dgg/U/7dtOK/VXKCu9UZlSPchwEoyJwhbic9qXXAe1fpjJ6HpmXmySrgsOX1oWgJupTkC/JYaZAe6/zTlUgMEg==", "signatures": [{"sig": "MEUCIQDEgOWYxLPalG+G2vnTsMw4CfsVzvr4b7otW4tk+XYWQAIgfZ8lkwH8nF8kh8JCRkMXxcwqPSUrbtVS8zMS8ZjL5xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "c6ddd824fca4ccad4329f8f37903c1bc902c2bb2", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.4.1": {"name": "source-map", "version": "0.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.4.1", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "db90f97e821c6cf5ba5efa76f33c1bb1c88dd32e", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.4.1.tgz", "integrity": "sha512-9YpkrzyaARBEr90PuJ/beaN3X7kmQ0OMzjaHtZ6sydGfBuGXLpFCtlRdKZkLT9B7QTc5GxK1K9Cp9250HFYdoA==", "signatures": [{"sig": "MEUCIHyXEGCN8SSdN6651B6cMJU1uZGf0KO2jBjXn3SG/GPGAiEAh4pDeusp7MD9jS15A+vgDIEOl7LleB9amAYWz4D0hXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "db90f97e821c6cf5ba5efa76f33c1bb1c88dd32e", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.4.2": {"name": "source-map", "version": "0.4.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "source-map@0.4.2", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "dc9f3114394ab7c1f9782972f3d11820fff06f1f", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.4.2.tgz", "integrity": "sha512-gPYyTI9C5vzUyavo23otNfekXxaSM7vUlpkPaymnD3mMtrvb9hHK7fukct2GuMboxmHjeoeJ0JelmZcV6DcAPw==", "signatures": [{"sig": "MEUCIEaIPBS4arKwPMewCwCjwxRJ/myQjenCXFsSzdDKws/UAiEA++UViS3BG6sn3o4LUBCRMW5ouOhkZ9LxYvCbq1KeeDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "dc9f3114394ab7c1f9782972f3d11820fff06f1f", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://opensource.org/licenses/BSD-3-Clause", "type": "BSD"}], "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.4.3": {"name": "source-map", "version": "0.4.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.4.3", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "8e8408163c922cf92166f3609156703b1c5d254e", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.4.3.tgz", "integrity": "sha512-x4Y6Wtdxhw1UdLq9nDNiGdi7+ZWXmdQxHo64tpFnFYADhpU3jb5nePxPKOvgOvy62dGRe7PXDv+1w69bxZUKtw==", "signatures": [{"sig": "MEUCIEJiGAkHH+Tva2ceTczQ/IHP5Fxb7LEq2vtJPRxHZyXoAiEAqK29fgpZHqfPsmPGqg/up7gfQ/i12yfzZNZ9C/I32OQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "_shasum": "8e8408163c922cf92166f3609156703b1c5d254e", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.4.4": {"name": "source-map", "version": "0.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.4.4", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "eba4f5da9c0dc999de68032d8b4f76173652036b", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz", "integrity": "sha512-Y8nIfcb1s/7DcobUz1yOO1GSp7gyL+D9zLHDehT7iRESqGSxjJ448Sg7rvfgsRJCnKLdSl11uGf0s9X80cH0/A==", "signatures": [{"sig": "MEQCIFuZ0MUCinsNRtdt0ILypEghXjN754uuD8HkHylfYaNzAiAelzoc4nfrf/iHmwRi+UH8kFoq85iqF2sYUoXNzVgd4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/source-map.js", "_from": ".", "files": ["lib/", "build/"], "_shasum": "eba4f5da9c0dc999de68032d8b4f76173652036b", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "node test/run-tests.js", "build": "node Makefile.dryice.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {"lib": "./lib"}, "dependencies": {"amdefine": ">=0.0.4"}, "devDependencies": {"dryice": ">=0.4.8"}}, "0.5.0": {"name": "source-map", "version": "0.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.0", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "0fe96503ac86a5adb5de63f4e412ae4872cdbe86", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.0.tgz", "integrity": "sha512-gjGnxNN0K+/Pr4Mi4fs/pOtda10dKB6Wn9QvjOrH6v5TWsI7ghHuJUHoIgyM6DkUL5kr2GtPFGererzKpMBWfA==", "signatures": [{"sig": "MEYCIQDfL90x1nhgcxsl/WJreILpgljuPak73sNkMPVUmgipBgIhAPpakZtuRlvCy1QB+pezNE+lsHVzPRjKCPFIacE3mdHs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/"], "_shasum": "0fe96503ac86a5adb5de63f4e412ae4872cdbe86", "engines": {"node": ">=0.10.0"}, "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}}, "0.5.1": {"name": "source-map", "version": "0.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.1", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "5fbd40a1cde485feab4d36460b8f496edb362ecf", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.1.tgz", "integrity": "sha512-tqh0zAvqU01jNbVwh9+BBxbX3BZWF9fhOUQcRSCqSbted8+MkQzwRnP/YGF82i80d9F31WEfzvaneWSJl7pY/g==", "signatures": [{"sig": "MEQCIDuuN3SvdI18ZHNGM2XUU2e07DJsjIUZrgwbkBxtHgBkAiBYhjN8EGaYpoDiv/BRY5LFsGo4CLX5nyfntPsSP3D93Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/"], "_shasum": "5fbd40a1cde485feab4d36460b8f496edb362ecf", "engines": {"node": ">=0.10.0"}, "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}}, "0.5.2": {"name": "source-map", "version": "0.5.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.2", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "ebb6e5e87424f497ad6f972c6389eacf3c0cbe00", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.2.tgz", "integrity": "sha512-pmmcvfMW3TBExWSMSi933VuLDKMpboWoD6ztiW62Ke2bFUrJnthmOAWwMyi4uu93skkhGFpGTQOQb+DtFmXd9g==", "signatures": [{"sig": "MEQCIB2n6btNwHp0HwQf40OVe9a6M8uzOMxE/78UU9ZKyh82AiBL0I3XVHlMKII8G4gyq7jOtJxND2kyzKtQgiwqVfdoyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/", "dist/"], "_shasum": "ebb6e5e87424f497ad6f972c6389eacf3c0cbe00", "engines": {"node": ">=0.10.0"}, "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}}, "0.5.3": {"name": "source-map", "version": "0.5.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.3", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "82674b85a71b0be76c3e7416d15e9f5252eb3be0", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.3.tgz", "integrity": "sha512-d/a1s6Jnb1bJISdw0+Q5YgNlczWabJWSL1jeKGH2p1Je4A0lIoiKI5YC/12RUgJ8ugm+XPoM9PCG7Kl0K9ABLA==", "signatures": [{"sig": "MEUCIQDRxlzOSZJ3gDIskyklLxZssQtZZ1FxzZwLtBWhH46heQIgZDb8ALQA3toprSYdVel4xMHLtuIpVJ4oR7RemGviUTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "_shasum": "82674b85a71b0be76c3e7416d15e9f5252eb3be0", "engines": {"node": ">=0.10.0"}, "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Generates and consumes source maps", "directories": {}, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}}, "0.5.4": {"name": "source-map", "version": "0.5.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.4", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "2beb18a2ab8abd88320a72178c671f0e4c5def28", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.4.tgz", "integrity": "sha512-GrSnGzRVA7tmdZ8LixSZWOjZJ6iSXV8UBl+p3pnFIOu4rQdh/zMWQVESPOD017+p4c9eRHR/mFTA5dO3D9zNKw==", "signatures": [{"sig": "MEYCIQCyBNgdIDhxReOgt1OjX8UpKPIlti3h+6SiwVIJMltbAQIhAK3Lynwev+miWy0zoE/t4n62HM0RsCCN/AqVrwoHoknX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "_shasum": "2beb18a2ab8abd88320a72178c671f0e4c5def28", "engines": {"node": ">=0.10.0"}, "gitHead": "7c4905497b5f0a62fd6a3d3f786ba8eac0db4615", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "5.3.0", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.5.4.tgz_1461355709333_0.3527497702743858", "host": "packages-16-east.internal.npmjs.com"}}, "0.5.5": {"name": "source-map", "version": "0.5.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.5", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "d9230c5a63dd59f1ebaecabf78e900302b892c49", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.5.tgz", "integrity": "sha512-ArRdlQmLJcNU2myOsSBipgolcBLAeYFpI0VA9jCa9Zx1Uta1+ocOgwH44b2JozO+As5TszFy85IRBCGefVCD/w==", "signatures": [{"sig": "MEQCIGXCzsF9g9/1R6/NAnHtR6ygngKJfRYDbVBTdsEKQ61xAiALPVt2Jgh7NA6qm7sDUhC/hR6kyPpxOzlG8j5MilBo+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "_shasum": "d9230c5a63dd59f1ebaecabf78e900302b892c49", "engines": {"node": ">=0.10.0"}, "gitHead": "d90ac8b806d4130b4bfdf2fc9482dfd5f7930692", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "5.3.0", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.5.5.tgz_1461602906442_0.309012166922912", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.6": {"name": "source-map", "version": "0.5.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.6", "maintainers": [{"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "75ce38f52bf0733c5a7f0c118d81334a2bb5f412", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz", "integrity": "sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==", "signatures": [{"sig": "MEQCIF4FQCi+iXehbkNNmRYrhyzkGCG8pfMlN5sPr0vyDOQfAiAYoHX2nkc96nUA8aBc4AfmYz86Ypf4efN7cMBgrZglmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "_shasum": "75ce38f52bf0733c5a7f0c118d81334a2bb5f412", "engines": {"node": ">=0.10.0"}, "gitHead": "aa0398ced67beebea34f0d36f766505656c344f6", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "http://github.com/mozilla/source-map.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "5.3.0", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.5.6.tgz_1462209962516_0.9263619624543935", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.7": {"name": "source-map", "version": "0.5.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.5.7", "maintainers": [{"name": "tromey", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "integrity": "sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==", "signatures": [{"sig": "MEQCICW1I02OehsiqXztYMGlzeQWUQoaKL2nfa7tWzp8mLrOAiAS6QL04viEV8jEXAMPlfc0EckEyB7ZAe2oLaOT7c0WEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "_from": ".", "files": ["source-map.js", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "_shasum": "8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc", "engines": {"node": ">=0.10.0"}, "gitHead": "326dd955a366569759d9537ef5f0f167c89d92d2", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "tromey", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.5.7.tgz_1503333015516_0.19087489508092403", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "source-map", "version": "0.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.6.0", "maintainers": [{"name": "tromey", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "36446016b0e5b626cf0315d6ff14b15bafb9dc10", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.6.0.tgz", "integrity": "sha512-mTozplhTX4tLKIHYji92OTZzVyZvi+Z1qRZDeBvQFI2XUB89wrRoj/xXad3c9NZ1GPJXXRvB+k41PQCPTMC+aA==", "signatures": [{"sig": "MEYCIQDke3n8n5rPWR5ApNMwjriJ6eEBLFI0aBcfVT3Clem6ngIhAP10r+S9+W/q/idMHCLtzTngHtwuD1h5xoNJc7oqrc1I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "gitHead": "83d389f62d80344aec4f60aaba649c76cdc002bb", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "tromey", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.6.0.tgz_1506522734674_0.8769479114562273", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "source-map", "version": "0.6.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.6.1", "maintainers": [{"name": "tromey", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "74722af32e9614e9c287a8d0bbde48b5e2f1a263", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "signatures": [{"sig": "MEUCIQDPx7x89gwCo+c9mch3nQQV4x592HZQjNy/wpijqND5dwIgdntgvrjeVvc+G3I58Ystp1p/nRlBvwCttzsVkDpLzVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "engines": {"node": ">=0.10.0"}, "gitHead": "ac518d2f21818146f3310557bd51c13d8cff2ba8", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "tromey", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.6.1.tgz_1506696150821_0.6614652345888317", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "source-map", "version": "0.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.7.0", "maintainers": [{"name": "tromey", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "ebf0d13a48f3619f91891816fdda932f83a6021f", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.7.0.tgz", "integrity": "sha512-JsXsCYrKzxA5kU8LanQJHIPoEY3fEH5WYSMJ8Z77ESByI18VFEoxB46H2eNHqK2nVqTRjUe5DYvNHmyT3JOd1w==", "signatures": [{"sig": "MEUCIQDCjkRjgzd9lYDcSPTWcFk856DR4rWBy+0pPkFueZBT9AIgTz9WT0sWv29+RnoV/CI51twqrLH0YknYKHLBsMrzpHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "engines": {"node": ">= 8"}, "gitHead": "86d105bfda30fc71aba95c21f2e665a384b5b69f", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "9.3.0", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^3.10"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-0.7.0.tgz_1516398028220_0.05363522795960307", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "source-map", "version": "0.7.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.7.1", "maintainers": [{"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "493620ba1692945d680b93862435bf0ed95a2aa4", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.7.1.tgz", "fileCount": 19, "integrity": "sha512-c+mPk+5wTnOd1y9oyVp0EQIo3N8k8gdxzxOo7jq1ApEN2+ew5c/UV+F1wtNHeS6Aa4JPsxiq5OU+95aMpRTGJA==", "signatures": [{"sig": "MEYCIQClfmVFimshQsSAeJ1qdH7lQ3ar0ISgTsOlGII155WBrAIhAL7D6/CIst0S2E/aFCp61aDYUEdqkShfyCgvMMAYP+Md", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295841}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "types": "./source-map.d.ts", "engines": {"node": ">= 8"}, "gitHead": "3181bacfb8d449ecdb7d3da8ffcd59e29bfe98d7", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "9.3.0", "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^3.10"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_0.7.1_1518634721569_0.5285039948740915", "host": "s3://npm-registry-packages"}}, "0.7.2": {"name": "source-map", "version": "0.7.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.7.2", "maintainers": [{"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "dist": {"shasum": "115c3e891aaa9a484869fd2b89391a225feba344", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.7.2.tgz", "fileCount": 19, "integrity": "sha512-NDJB/R2BS7YJG0tP9SbE4DKwKj1idLT5RJqfVYZ7dreFX7wulZT3xxVhbYKrQo9n0JkRptl51TrX/5VK3HodMA==", "signatures": [{"sig": "MEUCIQDb5nveB43dXq0796VP91mI1UhTSX3eYhRrZLY4njwxFAIgEemQdmSowTouJ6uhfpfkIuRgN2ucRnFMWRKZiQ/2+bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 316673}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "types": "./source-map.d.ts", "engines": {"node": ">= 8"}, "gitHead": "43b2687e6e2f3cf6c22926aa473d5031fb7df89d", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "9.3.0", "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^3.10"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_0.7.2_1519687802151_0.28165524412028553", "host": "s3://npm-registry-packages"}}, "0.7.3": {"name": "source-map", "version": "0.7.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.7.3", "maintainers": [{"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "mozilla", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "nyc": {"reporter": "html"}, "dist": {"shasum": "5302f8169031735226544092e64981f751750383", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.7.3.tgz", "fileCount": 19, "integrity": "sha512-CkCj6giN3S+n9qrYiBTX5gystlENnRW5jZeNLHpe6aue+SrHcG5VYwujhW9s4dY31mEGsxBDrHR6oI69fTXsaQ==", "signatures": [{"sig": "MEYCIQClsa96piySY8DM9nq9GRmJhR6ynCWyTrbK0JZBAjZnPAIhAJmJjDeVFhjCAyxMc9xunKAQLachXKctQldag1hbE6zc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/GqPCRA9TVsSAnZWagAACtUQAKRrpr4QdDnBj30YxzNF\nysVd9GiDMcrMmJ3EITM+EiG/DKn5iKcvWQvJbXtEwzK/rGHgEkBgnGEqBt+v\nLkVr1Tvp5PuuhDBdoI/neEJ06epEZp/dzXT/U9i6++0ycugsztlTWsdBzLe9\nXN904fr2QpNANVTbL38EMovO4Jr9tqROLCTwdq/dfaAsxYzy+UsoXFZW7BAm\nhx5Noptwa5AmqAnir5t9RS/IW1dT7v1HE+5gK8/o8OrH6fvVcQLBQUXFRrHR\n5Lsc4u6RXrjOYGxMkvvbb+DGKWsoiZy/DG3CCDux3Sbe0OzufRVNlUMd1aO7\nm1RNd/gKfhz2eBTTAr1fIDXbdL2tHBMrgA8gvdJf7v7JW7gzOSBWJjpfqOYU\nAIvNLfTUL+SseGWJ099EA7mH/xk3VxUcVa/r9C7odF+G3XTnZ6PsRfkYTpOd\nyrYqbcVZKORSGOSNkPBW8PqBeLwG3YXMUu/FJrIbJPvRCJQp4fCKYcHUuZIc\nDxMEIdKASbgZJWPu73kKRYM3ZiEl8EtfCLXjd5HJP7vdT7Bs/QfpOjFSFTvX\nBpJp6qKS8QAFsa+TJVvmcR++F3RFmF/TNqbi7qu5XQPKOwpno4xJf+Upb7DW\nAufKdhR3jLJ6thjsaEzpNevKt9QPgdyRtqG3LvDX6SJ4DLCKJ3jT1ZZPbtCI\nXGEO\r\n=0BNq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "types": "./source-map.d.ts", "engines": {"node": ">= 8"}, "gitHead": "b2171d58e90e64472b0e858013c0cc5f6772a83d", "scripts": {"dev": "npm-run-all -p --silent dev:*", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "lint": "eslint *.js lib/ test/", "test": "node test/run-tests.js", "build": "webpack --color", "clean": "rm -rf coverage .nyc_output", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "predev": "npm run setup", "pretest": "npm run build", "coverage": "nyc node test/run-tests.js", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "prebuild": "npm run lint", "dev:watch": "watch 'npm run coverage' lib/ test/", "precoverage": "npm run build"}, "typings": "source-map", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "9.10.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.7.1", "watch": "^1.0.2", "doctoc": "^0.15.0", "eslint": "^4.19.1", "webpack": "^3.10", "live-server": "^1.2.0", "npm-run-all": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_0.7.3_1526491788984_0.1999301401216913", "host": "s3://npm-registry-packages"}}, "0.8.0-beta.0": {"name": "source-map", "version": "0.8.0-beta.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.8.0-beta.0", "maintainers": [{"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "emce<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "nyc": {"reporter": "html"}, "dist": {"shasum": "d4c1bb42c3f7ee925f005927ba10709e0d1d1f11", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.8.0-beta.0.tgz", "fileCount": 21, "integrity": "sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==", "signatures": [{"sig": "MEUCIQDK8NK+x6HpgKNRM5lD5w/oRpPiOmBSuvlyG+AkiztdXAIgXPDB9zm6MOMlz/AM9c5bJCzLDQSz3W1a6EUE6TBPQKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7glUCRA9TVsSAnZWagAAackQAJo5S3Wx233LohPlLFz2\nDKQSr5lhlG/xB6G37pQCg2E0Bnzbb5zizov/XTPWtu38rnSlNJcBeT88kS6P\nS33zDNppTZR2aQJJ587L+gTOruSsob5d+RG7e5WcZpWSMdjpjpE8M7fR/npc\n5Bqt60FVHdp+kkIXT3daoIkKC/k9ze/rgj7UmVsqFBFz7BsBXUYAbqtBzdyU\ne+zEb9sKaSYxc+joyMnXKdbnLS4Br2cK7GmBtE9EYB32m07A7a6LITSFoN4P\nDZloz0nZgmd7ptR2gwfWDJerdBny3JTy4zvdLo56OkbZln5wJ9FVV5z3k1TX\ngcxS2fS4L18SPn745qZPU7GcN7S0GxEPQ1I8IEy4GKuu8uaq0bmi6lZ8qKc3\nG9fO4DQnlZHYNKGCe+KziPFgDgAl5Y+4o8EcdJ5PUZVmQIaLNsSOYIuE1Don\ntXP7kyR68yGliHMa3i5VSD+Fm1tgn2pr73lSTFjVVrk9+5sCHtIRwAe4rhuF\ncBXsv+uN+2JTBiMowU79EPC+EN7RsCu8Atp60PnD9/2NRiD79t6qdRNw5VaI\ns/l7A/1IidK3+k0by+aUskO6sDGhdTc+6cAd7oWc8WFHHw5sivUJvzphCTLC\nI8uvzgA0EJeRfpqCF5dTNQlTlvtc/5qsMZE3xlmbUK+3YP+0jdFx1DtDEQh7\nr3Is\r\n=1tFm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source-map.js", "types": "./source-map.d.ts", "readme": "# Source Map\n\n[![Build Status](https://travis-ci.org/mozilla/source-map.png?branch=master)](https://travis-ci.org/mozilla/source-map)\n\n[![Coverage Status](https://coveralls.io/repos/github/mozilla/source-map/badge.svg)](https://coveralls.io/github/mozilla/source-map)\n\n[![NPM](https://nodei.co/npm/source-map.png?downloads=true&downloadRank=true)](https://www.npmjs.com/package/source-map)\n\nThis is a library to generate and consume the source map format\n[described here][format].\n\n[format]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit\n\n## Use with Node\n\n    $ npm install source-map\n\n## Use on the Web\n\n    <script src=\"https://unpkg.com/source-map@0.7.3/dist/source-map.js\"></script>\n    <script>\n        sourceMap.SourceMapConsumer.initialize({\n            \"lib/mappings.wasm\": \"https://unpkg.com/source-map@0.7.3/lib/mappings.wasm\"\n        });\n    </script>\n\n--------------------------------------------------------------------------------\n\n<!-- `npm run toc` to regenerate the Table of Contents -->\n\n<!-- START doctoc generated TOC please keep comment here to allow auto update -->\n<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->\n## Table of Contents\n\n- [Examples](#examples)\n  - [Consuming a source map](#consuming-a-source-map)\n  - [Generating a source map](#generating-a-source-map)\n    - [With SourceNode (high level API)](#with-sourcenode-high-level-api)\n    - [With SourceMapGenerator (low level API)](#with-sourcemapgenerator-low-level-api)\n- [API](#api)\n  - [SourceMapConsumer](#sourcemapconsumer)\n    - [SourceMapConsumer.initialize(options)](#sourcemapconsumerinitializeoptions)\n    - [new SourceMapConsumer(rawSourceMap)](#new-sourcemapconsumerrawsourcemap)\n    - [SourceMapConsumer.with](#sourcemapconsumerwith)\n    - [SourceMapConsumer.prototype.destroy()](#sourcemapconsumerprototypedestroy)\n    - [SourceMapConsumer.prototype.computeColumnSpans()](#sourcemapconsumerprototypecomputecolumnspans)\n    - [SourceMapConsumer.prototype.originalPositionFor(generatedPosition)](#sourcemapconsumerprototypeoriginalpositionforgeneratedposition)\n    - [SourceMapConsumer.prototype.generatedPositionFor(originalPosition)](#sourcemapconsumerprototypegeneratedpositionfororiginalposition)\n    - [SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)](#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition)\n    - [SourceMapConsumer.prototype.hasContentsOfAllSources()](#sourcemapconsumerprototypehascontentsofallsources)\n    - [SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])](#sourcemapconsumerprototypesourcecontentforsource-returnnullonmissing)\n    - [SourceMapConsumer.prototype.eachMapping(callback, context, order)](#sourcemapconsumerprototypeeachmappingcallback-context-order)\n  - [SourceMapGenerator](#sourcemapgenerator)\n    - [new SourceMapGenerator([startOfSourceMap])](#new-sourcemapgeneratorstartofsourcemap)\n    - [SourceMapGenerator.fromSourceMap(sourceMapConsumer)](#sourcemapgeneratorfromsourcemapsourcemapconsumer)\n    - [SourceMapGenerator.prototype.addMapping(mapping)](#sourcemapgeneratorprototypeaddmappingmapping)\n    - [SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)](#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])](#sourcemapgeneratorprototypeapplysourcemapsourcemapconsumer-sourcefile-sourcemappath)\n    - [SourceMapGenerator.prototype.toString()](#sourcemapgeneratorprototypetostring)\n  - [SourceNode](#sourcenode)\n    - [new SourceNode([line, column, source[, chunk[, name]]])](#new-sourcenodeline-column-source-chunk-name)\n    - [SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])](#sourcenodefromstringwithsourcemapcode-sourcemapconsumer-relativepath)\n    - [SourceNode.prototype.add(chunk)](#sourcenodeprototypeaddchunk)\n    - [SourceNode.prototype.prepend(chunk)](#sourcenodeprototypeprependchunk)\n    - [SourceNode.prototype.setSourceContent(sourceFile, sourceContent)](#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceNode.prototype.walk(fn)](#sourcenodeprototypewalkfn)\n    - [SourceNode.prototype.walkSourceContents(fn)](#sourcenodeprototypewalksourcecontentsfn)\n    - [SourceNode.prototype.join(sep)](#sourcenodeprototypejoinsep)\n    - [SourceNode.prototype.replaceRight(pattern, replacement)](#sourcenodeprototypereplacerightpattern-replacement)\n    - [SourceNode.prototype.toString()](#sourcenodeprototypetostring)\n    - [SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])](#sourcenodeprototypetostringwithsourcemapstartofsourcemap)\n\n<!-- END doctoc generated TOC please keep comment here to allow auto update -->\n\n## Examples\n\n### Consuming a source map\n\n```js\nconst rawSourceMap = {\n  version: 3,\n  file: 'min.js',\n  names: ['bar', 'baz', 'n'],\n  sources: ['one.js', 'two.js'],\n  sourceRoot: 'http://example.com/www/js/',\n  mappings: 'CAAC,IAAI,IAAM,SAAUA,GAClB,OAAOC,IAAID;CCDb,IAAI,IAAM,SAAUE,GAClB,OAAOA'\n};\n\nconst whatever = await SourceMapConsumer.with(rawSourceMap, null, consumer => {\n\n  console.log(consumer.sources);\n  // [ 'http://example.com/www/js/one.js',\n  //   'http://example.com/www/js/two.js' ]\n\n  console.log(consumer.originalPositionFor({\n    line: 2,\n    column: 28\n  }));\n  // { source: 'http://example.com/www/js/two.js',\n  //   line: 2,\n  //   column: 10,\n  //   name: 'n' }\n\n  console.log(consumer.generatedPositionFor({\n    source: 'http://example.com/www/js/two.js',\n    line: 2,\n    column: 10\n  }));\n  // { line: 2, column: 28 }\n\n  consumer.eachMapping(function (m) {\n    // ...\n  });\n\n  return computeWhatever();\n});\n```\n\n### Generating a source map\n\nIn depth guide:\n[**Compiling to JavaScript, and Debugging with Source Maps**](https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/)\n\n#### With SourceNode (high level API)\n\n```js\nfunction compile(ast) {\n  switch (ast.type) {\n  case 'BinaryExpression':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      [compile(ast.left), \" + \", compile(ast.right)]\n    );\n  case 'Literal':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      String(ast.value)\n    );\n  // ...\n  default:\n    throw new Error(\"Bad AST\");\n  }\n}\n\nvar ast = parse(\"40 + 2\", \"add.js\");\nconsole.log(compile(ast).toStringWithSourceMap({\n  file: 'add.js'\n}));\n// { code: '40 + 2',\n//   map: [object SourceMapGenerator] }\n```\n\n#### With SourceMapGenerator (low level API)\n\n```js\nvar map = new SourceMapGenerator({\n  file: \"source-mapped.js\"\n});\n\nmap.addMapping({\n  generated: {\n    line: 10,\n    column: 35\n  },\n  source: \"foo.js\",\n  original: {\n    line: 33,\n    column: 2\n  },\n  name: \"christopher\"\n});\n\nconsole.log(map.toString());\n// '{\"version\":3,\"file\":\"source-mapped.js\",\"sources\":[\"foo.js\"],\"names\":[\"christopher\"],\"mappings\":\";;;;;;;;;mCAgCEA\"}'\n```\n\n## API\n\nGet a reference to the module:\n\n```js\n// Node.js\nvar sourceMap = require('source-map');\n\n// Browser builds\nvar sourceMap = window.sourceMap;\n\n// Inside Firefox\nconst sourceMap = require(\"devtools/toolkit/sourcemap/source-map.js\");\n```\n\n### SourceMapConsumer\n\nA `SourceMapConsumer` instance represents a parsed source map which we can query\nfor information about the original file positions by giving it a file position\nin the generated source.\n\n#### SourceMapConsumer.initialize(options)\n\nWhen using `SourceMapConsumer` outside of node.js, for example on the Web, it\nneeds to know from what URL to load `lib/mappings.wasm`. You must inform it by\ncalling `initialize` before constructing any `SourceMapConsumer`s.\n\nThe options object has the following properties:\n\n* `\"lib/mappings.wasm\"`: A `String` containing the URL of the\n  `lib/mappings.wasm` file, or an `ArrayBuffer` with the contents of `lib/mappings.wasm`.\n\n```js\nsourceMap.SourceMapConsumer.initialize({\n  \"lib/mappings.wasm\": \"https://example.com/source-map/lib/mappings.wasm\"\n});\n```\n\n#### new SourceMapConsumer(rawSourceMap)\n\nThe only parameter is the raw source map (either as a string which can be\n`JSON.parse`'d, or an object). According to the spec, source maps have the\nfollowing attributes:\n\n* `version`: Which version of the source map spec this map is following.\n\n* `sources`: An array of URLs to the original source files.\n\n* `names`: An array of identifiers which can be referenced by individual\n  mappings.\n\n* `sourceRoot`: Optional. The URL root from which all sources are relative.\n\n* `sourcesContent`: Optional. An array of contents of the original source files.\n\n* `mappings`: A string of base64 VLQs which contain the actual mappings.\n\n* `file`: Optional. The generated filename this source map is associated with.\n\nThe promise of the constructed souce map consumer is returned.\n\nWhen the `SourceMapConsumer` will no longer be used anymore, you must call its\n`destroy` method.\n\n```js\nconst consumer = await new sourceMap.SourceMapConsumer(rawSourceMapJsonData);\ndoStuffWith(consumer);\nconsumer.destroy();\n```\n\nAlternatively, you can use `SourceMapConsumer.with` to avoid needing to remember\nto call `destroy`.\n\n#### SourceMapConsumer.with\n\nConstruct a new `SourceMapConsumer` from `rawSourceMap` and `sourceMapUrl`\n(see the `SourceMapConsumer` constructor for details. Then, invoke the `async\nfunction f(SourceMapConsumer) -> T` with the newly constructed consumer, wait\nfor `f` to complete, call `destroy` on the consumer, and return `f`'s return\nvalue.\n\nYou must not use the consumer after `f` completes!\n\nBy using `with`, you do not have to remember to manually call `destroy` on\nthe consumer, since it will be called automatically once `f` completes.\n\n```js\nconst xSquared = await SourceMapConsumer.with(\n  myRawSourceMap,\n  null,\n  async function (consumer) {\n    // Use `consumer` inside here and don't worry about remembering\n    // to call `destroy`.\n\n    const x = await whatever(consumer);\n    return x * x;\n  }\n);\n\n// You may not use that `consumer` anymore out here; it has\n// been destroyed. But you can use `xSquared`.\nconsole.log(xSquared);\n```\n\n#### SourceMapConsumer.prototype.destroy()\n\nFree this source map consumer's associated wasm data that is manually-managed.\n\n```js\nconsumer.destroy();\n```\n\nAlternatively, you can use `SourceMapConsumer.with` to avoid needing to remember\nto call `destroy`.\n\n#### SourceMapConsumer.prototype.computeColumnSpans()\n\nCompute the last column for each generated mapping. The last column is\ninclusive.\n\n```js\n// Before:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n\nconsumer.computeColumnSpans();\n\n// After:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1,\n//     lastColumn: 9 },\n//   { line: 2,\n//     column: 10,\n//     lastColumn: 19 },\n//   { line: 2,\n//     column: 20,\n//     lastColumn: Infinity } ]\n```\n\n#### SourceMapConsumer.prototype.originalPositionFor(generatedPosition)\n\nReturns the original source, line, and column information for the generated\nsource's line and column positions provided. The only argument is an object with\nthe following properties:\n\n* `line`: The line number in the generated source.  Line numbers in\n  this library are 1-based (note that the underlying source map\n  specification uses 0-based line numbers -- this library handles the\n  translation).\n\n* `column`: The column number in the generated source.  Column numbers\n  in this library are 0-based.\n\n* `bias`: Either `SourceMapConsumer.GREATEST_LOWER_BOUND` or\n  `SourceMapConsumer.LEAST_UPPER_BOUND`. Specifies whether to return the closest\n  element that is smaller than or greater than the one we are searching for,\n  respectively, if the exact element cannot be found.  Defaults to\n  `SourceMapConsumer.GREATEST_LOWER_BOUND`.\n\nand an object is returned with the following properties:\n\n* `source`: The original source file, or null if this information is not\n  available.\n\n* `line`: The line number in the original source, or null if this information is\n  not available.  The line number is 1-based.\n\n* `column`: The column number in the original source, or null if this\n  information is not available.  The column number is 0-based.\n\n* `name`: The original identifier, or null if this information is not available.\n\n```js\nconsumer.originalPositionFor({ line: 2, column: 10 })\n// { source: 'foo.coffee',\n//   line: 2,\n//   column: 2,\n//   name: null }\n\nconsumer.originalPositionFor({ line: 99999999999999999, column: 999999999999999 })\n// { source: null,\n//   line: null,\n//   column: null,\n//   name: null }\n```\n\n#### SourceMapConsumer.prototype.generatedPositionFor(originalPosition)\n\nReturns the generated line and column information for the original source,\nline, and column positions provided. The only argument is an object with\nthe following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: The column number in the original source.  The column\n  number is 0-based.\n\nand an object is returned with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.generatedPositionFor({ source: \"example.js\", line: 2, column: 10 })\n// { line: 1,\n//   column: 56 }\n```\n\n#### SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)\n\nReturns all generated line and column information for the original source, line,\nand column provided. If no column is provided, returns all mappings\ncorresponding to a either the line we are searching for or the next closest line\nthat has any mappings. Otherwise, returns all mappings corresponding to the\ngiven line and either the column we are searching for or the next closest column\nthat has any offsets.\n\nThe only argument is an object with the following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: Optional. The column number in the original source.  The\n  column number is 0-based.\n\nand an array of objects is returned, each with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.allGeneratedpositionsfor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n```\n\n#### SourceMapConsumer.prototype.hasContentsOfAllSources()\n\nReturn true if we have the embedded source content for every source listed in\nthe source map, false otherwise.\n\nIn other words, if this method returns `true`, then\n`consumer.sourceContentFor(s)` will succeed for every source `s` in\n`consumer.sources`.\n\n```js\n// ...\nif (consumer.hasContentsOfAllSources()) {\n  consumerReadyCallback(consumer);\n} else {\n  fetchSources(consumer, consumerReadyCallback);\n}\n// ...\n```\n\n#### SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])\n\nReturns the original source content for the source provided. The only\nargument is the URL of the original source file.\n\nIf the source content for the given source is not found, then an error is\nthrown. Optionally, pass `true` as the second param to have `null` returned\ninstead.\n\n```js\nconsumer.sources\n// [ \"my-cool-lib.clj\" ]\n\nconsumer.sourceContentFor(\"my-cool-lib.clj\")\n// \"...\"\n\nconsumer.sourceContentFor(\"this is not in the source map\");\n// Error: \"this is not in the source map\" is not in the source map\n\nconsumer.sourceContentFor(\"this is not in the source map\", true);\n// null\n```\n\n#### SourceMapConsumer.prototype.eachMapping(callback, context, order)\n\nIterate over each mapping between an original source/line/column and a\ngenerated line/column in this source map.\n\n* `callback`: The function that is called with each mapping. Mappings have the\n  form `{ source, generatedLine, generatedColumn, originalLine, originalColumn,\n  name }`\n\n* `context`: Optional. If specified, this object will be the value of `this`\n  every time that `callback` is called.\n\n* `order`: Either `SourceMapConsumer.GENERATED_ORDER` or\n  `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to iterate over\n  the mappings sorted by the generated file's line/column order or the\n  original's source/line/column order, respectively. Defaults to\n  `SourceMapConsumer.GENERATED_ORDER`.\n\n```js\nconsumer.eachMapping(function (m) { console.log(m); })\n// ...\n// { source: 'illmatic.js',\n//   generatedLine: 1,\n//   generatedColumn: 0,\n//   originalLine: 1,\n//   originalColumn: 0,\n//   name: null }\n// { source: 'illmatic.js',\n//   generatedLine: 2,\n//   generatedColumn: 0,\n//   originalLine: 2,\n//   originalColumn: 0,\n//   name: null }\n// ...\n```\n### SourceMapGenerator\n\nAn instance of the SourceMapGenerator represents a source map which is being\nbuilt incrementally.\n\n#### new SourceMapGenerator([startOfSourceMap])\n\nYou may pass an object with the following properties:\n\n* `file`: The filename of the generated source that this source map is\n  associated with.\n\n* `sourceRoot`: A root for all relative URLs in this source map.\n\n* `skipValidation`: Optional. When `true`, disables validation of mappings as\n  they are added. This can improve performance but should be used with\n  discretion, as a last resort. Even then, one should avoid using this flag when\n  running tests, if possible.\n\n```js\nvar generator = new sourceMap.SourceMapGenerator({\n  file: \"my-generated-javascript-file.js\",\n  sourceRoot: \"http://example.com/app/js/\"\n});\n```\n\n#### SourceMapGenerator.fromSourceMap(sourceMapConsumer)\n\nCreates a new `SourceMapGenerator` from an existing `SourceMapConsumer` instance.\n\n* `sourceMapConsumer` The SourceMap.\n\n```js\nvar generator = sourceMap.SourceMapGenerator.fromSourceMap(consumer);\n```\n\n#### SourceMapGenerator.prototype.addMapping(mapping)\n\nAdd a single mapping from original source line and column to the generated\nsource's line and column for this source map being created. The mapping object\nshould have the following properties:\n\n* `generated`: An object with the generated line and column positions.\n\n* `original`: An object with the original line and column positions.\n\n* `source`: The original source file (relative to the sourceRoot).\n\n* `name`: An optional original token name for this mapping.\n\n```js\ngenerator.addMapping({\n  source: \"module-one.scm\",\n  original: { line: 128, column: 0 },\n  generated: { line: 3, column: 456 }\n})\n```\n\n#### SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for an original source file.\n\n* `sourceFile` the URL of the original source file.\n\n* `sourceContent` the content of the source file.\n\n```js\ngenerator.setSourceContent(\"module-one.scm\",\n                           fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])\n\nApplies a SourceMap for a source file to the SourceMap.\nEach mapping to the supplied source file is rewritten using the\nsupplied SourceMap. Note: The resolution for the resulting mappings\nis the minimum of this map and the supplied map.\n\n* `sourceMapConsumer`: The SourceMap to be applied.\n\n* `sourceFile`: Optional. The filename of the source file.\n  If omitted, sourceMapConsumer.file will be used, if it exists.\n  Otherwise an error will be thrown.\n\n* `sourceMapPath`: Optional. The dirname of the path to the SourceMap\n  to be applied. If relative, it is relative to the SourceMap.\n\n  This parameter is needed when the two SourceMaps aren't in the same\n  directory, and the SourceMap to be applied contains relative source\n  paths. If so, those relative source paths need to be rewritten\n  relative to the SourceMap.\n\n  If omitted, it is assumed that both SourceMaps are in the same directory,\n  thus not needing any rewriting. (Supplying `'.'` has the same effect.)\n\n#### SourceMapGenerator.prototype.toString()\n\nRenders the source map being generated to a string.\n\n```js\ngenerator.toString()\n// '{\"version\":3,\"sources\":[\"module-one.scm\"],\"names\":[],\"mappings\":\"...snip...\",\"file\":\"my-generated-javascript-file.js\",\"sourceRoot\":\"http://example.com/app/js/\"}'\n```\n\n### SourceNode\n\nSourceNodes provide a way to abstract over interpolating and/or concatenating\nsnippets of generated JavaScript source code, while maintaining the line and\ncolumn information associated between those snippets and the original source\ncode. This is useful as the final intermediate representation a compiler might\nuse before outputting the generated JS and source map.\n\n#### new SourceNode([line, column, source[, chunk[, name]]])\n\n* `line`: The original line number associated with this source node, or null if\n  it isn't associated with an original line.  The line number is 1-based.\n\n* `column`: The original column number associated with this source node, or null\n  if it isn't associated with an original column.  The column number\n  is 0-based.\n\n* `source`: The original source's filename; null if no filename is provided.\n\n* `chunk`: Optional. Is immediately passed to `SourceNode.prototype.add`, see\n  below.\n\n* `name`: Optional. The original identifier.\n\n```js\nvar node = new SourceNode(1, 2, \"a.cpp\", [\n  new SourceNode(3, 4, \"b.cpp\", \"extern int status;\\n\"),\n  new SourceNode(5, 6, \"c.cpp\", \"std::string* make_string(size_t n);\\n\"),\n  new SourceNode(7, 8, \"d.cpp\", \"int main(int argc, char** argv) {}\\n\"),\n]);\n```\n\n#### SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])\n\nCreates a SourceNode from generated code and a SourceMapConsumer.\n\n* `code`: The generated code\n\n* `sourceMapConsumer` The SourceMap for the generated code\n\n* `relativePath` The optional path that relative sources in `sourceMapConsumer`\n  should be relative to.\n\n```js\nconst consumer = await new SourceMapConsumer(fs.readFileSync(\"path/to/my-file.js.map\", \"utf8\"));\nconst node = SourceNode.fromStringWithSourceMap(fs.readFileSync(\"path/to/my-file.js\"), consumer);\n```\n\n#### SourceNode.prototype.add(chunk)\n\nAdd a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.add(\" + \");\nnode.add(otherNode);\nnode.add([leftHandOperandNode, \" + \", rightHandOperandNode]);\n```\n\n#### SourceNode.prototype.prepend(chunk)\n\nPrepend a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.prepend(\"/** Build Id: f783haef86324gf **/\\n\\n\");\n```\n\n#### SourceNode.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for a source file. This will be added to the\n`SourceMap` in the `sourcesContent` field.\n\n* `sourceFile`: The filename of the source file\n\n* `sourceContent`: The content of the source file\n\n```js\nnode.setSourceContent(\"module-one.scm\",\n                      fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceNode.prototype.walk(fn)\n\nWalk over the tree of JS snippets in this node and its children. The walking\nfunction is called once for each snippet of JS and is passed that snippet and\nthe its original associated source's line/column location.\n\n* `fn`: The traversal function.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.walk(function (code, loc) { console.log(\"WALK:\", code, loc); })\n// WALK: uno { source: 'b.js', line: 3, column: 4, name: null }\n// WALK: dos { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: tres { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: quatro { source: 'c.js', line: 5, column: 6, name: null }\n```\n\n#### SourceNode.prototype.walkSourceContents(fn)\n\nWalk over the tree of SourceNodes. The walking function is called for each\nsource file content and is passed the filename and source content.\n\n* `fn`: The traversal function.\n\n```js\nvar a = new SourceNode(1, 2, \"a.js\", \"generated from a\");\na.setSourceContent(\"a.js\", \"original a\");\nvar b = new SourceNode(1, 2, \"b.js\", \"generated from b\");\nb.setSourceContent(\"b.js\", \"original b\");\nvar c = new SourceNode(1, 2, \"c.js\", \"generated from c\");\nc.setSourceContent(\"c.js\", \"original c\");\n\nvar node = new SourceNode(null, null, null, [a, b, c]);\nnode.walkSourceContents(function (source, contents) { console.log(\"WALK:\", source, \":\", contents); })\n// WALK: a.js : original a\n// WALK: b.js : original b\n// WALK: c.js : original c\n```\n\n#### SourceNode.prototype.join(sep)\n\nLike `Array.prototype.join` except for SourceNodes. Inserts the separator\nbetween each of this source node's children.\n\n* `sep`: The separator.\n\n```js\nvar lhs = new SourceNode(1, 2, \"a.rs\", \"my_copy\");\nvar operand = new SourceNode(3, 4, \"a.rs\", \"=\");\nvar rhs = new SourceNode(5, 6, \"a.rs\", \"orig.clone()\");\n\nvar node = new SourceNode(null, null, null, [ lhs, operand, rhs ]);\nvar joinedNode = node.join(\" \");\n```\n\n#### SourceNode.prototype.replaceRight(pattern, replacement)\n\nCall `String.prototype.replace` on the very right-most source snippet. Useful\nfor trimming white space from the end of a source node, etc.\n\n* `pattern`: The pattern to replace.\n\n* `replacement`: The thing to replace the pattern with.\n\n```js\n// Trim trailing white space.\nnode.replaceRight(/\\s*$/, \"\");\n```\n\n#### SourceNode.prototype.toString()\n\nReturn the string representation of this source node. Walks over the tree and\nconcatenates all the various snippets together to one string.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toString()\n// 'unodostresquatro'\n```\n\n#### SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])\n\nReturns the string representation of this tree of source nodes, plus a\nSourceMapGenerator which contains all the mappings between the generated and\noriginal sources.\n\nThe arguments are the same as those to `new SourceMapGenerator`.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toStringWithSourceMap({ file: \"my-output-file.js\" })\n// { code: 'unodostresquatro',\n//   map: [object SourceMapGenerator] }\n```\n", "browser": {"./lib/url.js": "./lib/url-browser.js", "./lib/read-wasm.js": "./lib/read-wasm-browser.js"}, "engines": {"node": ">= 8"}, "gitHead": "11cecea2b64ead3f93cde37a5789dc7683685a7c", "scripts": {"dev": "npm-run-all -p --silent dev:*", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "lint": "eslint *.js lib/ test/", "test": "node test/run-tests.js", "clean": "rm -rf coverage .nyc_output", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "predev": "npm run setup", "coverage": "nyc node test/run-tests.js", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "prebuild": "npm run lint", "dev:watch": "watch 'npm run coverage' lib/ test/"}, "typings": "source-map", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"whatwg-url": "^7.0.0"}, "publishConfig": {"tag": "next"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^11.7.1", "watch": "^1.0.2", "doctoc": "^1.3.1", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_0.8.0-beta.0_1542326612158_0.2007835954375803", "host": "s3://npm-registry-packages"}}, "0.7.4": {"name": "source-map", "version": "0.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map@0.7.4", "maintainers": [{"name": "tigleym", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mozilla-npm", "email": "<EMAIL>"}, {"name": "mythmon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "factorui.npm", "email": "<EMAIL>"}, {"name": "project-nimbus-publishing", "email": "<EMAIL>"}, {"name": "gijs", "email": "<EMAIL>"}, {"name": "nchevobbe", "email": "<EMAIL>"}, {"name": "brizental", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "moz<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "knowtheory", "email": "<EMAIL>"}, {"name": "mozilla-devtools", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ejpbruel", "email": "<EMAIL>"}, {"name": "tromey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mozilla/source-map", "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "nyc": {"reporter": "html"}, "dist": {"shasum": "a9bbe705c9d8846f4e08ff6765acf0f1b0898656", "tarball": "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz", "fileCount": 18, "integrity": "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==", "signatures": [{"sig": "MEYCIQDMoXsZG1A0Q3vS0+ehewy31/0pCeca20yU9vBAfjCFYgIhAIzFiFPKPhYt2HkFy0UQSbdESaOhsqyfRssw+k8WKJ8b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 226000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJim+NGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdmg//YO8dM0q3bO/Fc+Uo1pvi+i7m3wa/p6b8CJXcmn2k9HU9+7s5\r\n4dcGZbK1WV7ErRwHCi1lR4Xckci/eK9V5x22auujGZLOT3QCEfRvxy4GvQUt\r\nz/I1YhHYKyBNnT757eS8LBc9eNiw4w3iIoNOeOyCIX6C4OVd85Ard6asK6ud\r\nSPEv47p+z5Cy+kAWz0Kltyjh10pVrKdERVaLnLKOcNzFvBGmI0H/Nn51tBFn\r\nN1rOepn5TI2+27y/sTkQzPLWLVfBZOr/28W7lq1bWbLvQjtUD29Y2lvAupmO\r\nGk4wiG5hWP1tLYtpa4UrJ9a9qvBY9zMhorcNeo8vW8XF74411QVgDRjxWWwk\r\nIlxtfZSkNdnRDNWgnYvOSkoyK4GX0PFjLFCKtFuD+2Iw0C1dPemE17OdNw8X\r\nYB/kv5mYacxfiL71e1ev3o05ThVUCQR9FVjwsKA37GM0OIhRPCG/Q5jNxMnN\r\n0NSfrYj3yeAA/tcOUATUK6r/UzCzRekuKD4rhSVKKEmTxi3gXII/qPkP3E98\r\nm9UTMZXLoP+SYeXrtTmRAhbYhjDxLfyfORrmdbQ8+3IZlKmb0xR0IqCJ4DJ/\r\nIZQYrmD0x9IbtDkio8/M0fMA/B3/ZTPMtGX1lnJUexl0SBNlG0x7z+JTzTFB\r\nl8iOxaPsGLwGTBRtxrNMyz4WZMy5gy5XMgQ=\r\n=QeLs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source-map.js", "types": "./source-map.d.ts", "engines": {"node": ">= 8"}, "gitHead": "a999ec31686810d8a6aa2e8ff76c5df9bc3bfdcd", "scripts": {"dev": "npm-run-all -p --silent dev:*", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "lint": "eslint *.js lib/ test/", "test": "node test/run-tests.js", "build": "webpack --color", "clean": "rm -rf coverage .nyc_output", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "predev": "npm run setup", "pretest": "npm run build", "coverage": "nyc node test/run-tests.js", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "prebuild": "npm run lint", "dev:watch": "watch 'npm run coverage' lib/ test/", "precoverage": "npm run build"}, "typings": "source-map", "_npmUser": {"name": "e<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "18.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.7.1", "watch": "^1.0.2", "doctoc": "^1.3.1", "eslint": "^4.19.1", "webpack": "^4.9.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "webpack-cli": "^3.1"}, "_npmOperationalInternal": {"tmp": "tmp/source-map_0.7.4_1654383429872_0.7539603373460522", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-08-30T19:45:39.150Z", "modified": "2025-04-01T16:35:05.105Z", "0.0.0": "2011-08-30T19:45:40.104Z", "0.1.0": "2011-09-08T23:37:50.688Z", "0.1.1": "2012-06-19T22:27:12.055Z", "0.1.2": "2012-09-05T16:30:51.322Z", "0.1.3": "2012-10-12T18:43:39.522Z", "0.1.4": "2012-10-29T20:16:21.630Z", "0.1.5": "2012-10-31T01:28:00.060Z", "0.1.6": "2012-11-02T18:52:59.069Z", "0.1.7": "2012-11-02T19:09:11.172Z", "0.1.8": "2012-11-19T22:48:36.067Z", "0.1.9": "2013-03-01T01:02:37.514Z", "0.1.10": "2013-03-19T18:27:06.685Z", "0.1.11": "2013-03-20T16:56:07.393Z", "0.1.12": "2013-03-20T18:37:52.714Z", "0.1.13": "2013-03-20T20:58:36.999Z", "0.1.14": "2013-03-21T02:44:33.993Z", "0.1.15": "2013-03-22T21:39:07.305Z", "0.1.16": "2013-03-22T22:35:12.720Z", "0.1.17": "2013-03-25T20:22:39.938Z", "0.1.18": "2013-03-25T20:43:15.224Z", "0.1.19": "2013-03-25T21:43:18.536Z", "0.1.20": "2013-04-02T01:23:25.121Z", "0.1.21": "2013-04-02T04:34:47.865Z", "0.1.22": "2013-04-04T18:45:04.574Z", "0.1.23": "2013-06-14T00:24:06.756Z", "0.1.24": "2013-06-24T21:29:09.782Z", "0.1.25": "2013-06-27T19:05:03.794Z", "0.1.26": "2013-07-15T18:51:40.129Z", "0.1.27": "2013-07-22T20:37:22.337Z", "0.1.28": "2013-08-16T21:12:52.163Z", "0.1.29": "2013-08-22T00:29:20.519Z", "0.1.30": "2013-09-30T23:09:43.379Z", "0.1.31": "2013-11-01T18:40:25.890Z", "0.1.32": "2014-02-11T23:10:33.088Z", "0.1.33": "2014-02-27T02:27:53.235Z", "0.1.34": "2014-06-09T23:24:45.292Z", "0.1.35": "2014-07-08T17:01:07.625Z", "0.1.36": "2014-07-09T18:17:31.168Z", "0.1.37": "2014-07-11T18:05:50.696Z", "0.1.38": "2014-08-03T16:54:48.055Z", "0.1.39": "2014-09-09T21:00:18.550Z", "0.1.40": "2014-10-02T15:33:22.147Z", "0.1.41": "2014-12-17T19:24:20.497Z", "0.1.42": "2014-12-31T20:44:32.510Z", "0.1.43": "2015-01-08T18:25:06.813Z", "0.2.0": "2015-01-26T23:09:58.598Z", "0.3.0": "2015-02-10T18:28:31.894Z", "0.4.0": "2015-02-25T19:04:22.094Z", "0.4.1": "2015-03-02T19:05:04.884Z", "0.4.2": "2015-03-12T17:19:35.745Z", "0.4.3": "2015-07-07T20:29:01.648Z", "0.4.4": "2015-07-13T19:07:44.340Z", "0.5.0": "2015-09-10T15:13:56.564Z", "0.5.1": "2015-09-23T23:06:50.830Z", "0.5.2": "2015-10-21T16:01:45.524Z", "0.5.3": "2015-10-23T15:39:27.746Z", "0.5.4": "2016-04-22T20:08:32.822Z", "0.5.5": "2016-04-25T16:48:26.946Z", "0.5.6": "2016-05-02T17:26:02.930Z", "0.5.7": "2017-08-21T16:30:15.907Z", "0.6.0": "2017-09-27T14:32:14.978Z", "0.6.1": "2017-09-29T14:42:30.948Z", "0.7.0": "2018-01-19T21:40:28.391Z", "0.7.1": "2018-02-14T18:58:41.826Z", "0.7.2": "2018-02-26T23:30:02.206Z", "0.7.3": "2018-05-16T17:29:49.200Z", "0.8.0-beta.0": "2018-11-16T00:03:32.324Z", "0.7.4": "2022-06-04T22:57:10.068Z"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/mozilla/source-map", "repository": {"url": "git+ssh://**************/mozilla/source-map.git", "type": "git"}, "description": "Generates and consumes source maps", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "mozilla-npm"}, {"email": "<EMAIL>", "name": "mythmon"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "factorui.npm"}, {"email": "<EMAIL>", "name": "project-nimbus-publishing"}, {"email": "<EMAIL>", "name": "gijs"}, {"email": "<EMAIL>", "name": "brizental"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "moz<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "knowtheory"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tigleym"}, {"email": "<EMAIL>", "name": "e<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "m<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "llisi-moz"}, {"email": "<EMAIL>", "name": "jdarcangelo-mozilla"}, {"email": "<EMAIL>", "name": "mozilla-devtools"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ejpbruel"}, {"email": "<EMAIL>", "name": "tromey"}], "readme": "# Source Map\n\n[![Build Status](https://travis-ci.org/mozilla/source-map.png?branch=master)](https://travis-ci.org/mozilla/source-map)\n\n[![Coverage Status](https://coveralls.io/repos/github/mozilla/source-map/badge.svg)](https://coveralls.io/github/mozilla/source-map)\n\n[![NPM](https://nodei.co/npm/source-map.png?downloads=true&downloadRank=true)](https://www.npmjs.com/package/source-map)\n\nThis is a library to generate and consume the source map format\n[described here][format].\n\n[format]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit\n\n## Use with Node\n\n    $ npm install source-map\n\n## Use on the Web\n\n    <script src=\"https://unpkg.com/source-map@0.7.3/dist/source-map.js\"></script>\n    <script>\n        sourceMap.SourceMapConsumer.initialize({\n            \"lib/mappings.wasm\": \"https://unpkg.com/source-map@0.7.3/lib/mappings.wasm\"\n        });\n    </script>\n\n--------------------------------------------------------------------------------\n\n<!-- `npm run toc` to regenerate the Table of Contents -->\n\n<!-- START doctoc generated TOC please keep comment here to allow auto update -->\n<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->\n## Table of Contents\n\n- [Examples](#examples)\n  - [Consuming a source map](#consuming-a-source-map)\n  - [Generating a source map](#generating-a-source-map)\n    - [With SourceNode (high level API)](#with-sourcenode-high-level-api)\n    - [With SourceMapGenerator (low level API)](#with-sourcemapgenerator-low-level-api)\n- [API](#api)\n  - [SourceMapConsumer](#sourcemapconsumer)\n    - [SourceMapConsumer.initialize(options)](#sourcemapconsumerinitializeoptions)\n    - [new SourceMapConsumer(rawSourceMap)](#new-sourcemapconsumerrawsourcemap)\n    - [SourceMapConsumer.with](#sourcemapconsumerwith)\n    - [SourceMapConsumer.prototype.destroy()](#sourcemapconsumerprototypedestroy)\n    - [SourceMapConsumer.prototype.computeColumnSpans()](#sourcemapconsumerprototypecomputecolumnspans)\n    - [SourceMapConsumer.prototype.originalPositionFor(generatedPosition)](#sourcemapconsumerprototypeoriginalpositionforgeneratedposition)\n    - [SourceMapConsumer.prototype.generatedPositionFor(originalPosition)](#sourcemapconsumerprototypegeneratedpositionfororiginalposition)\n    - [SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)](#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition)\n    - [SourceMapConsumer.prototype.hasContentsOfAllSources()](#sourcemapconsumerprototypehascontentsofallsources)\n    - [SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])](#sourcemapconsumerprototypesourcecontentforsource-returnnullonmissing)\n    - [SourceMapConsumer.prototype.eachMapping(callback, context, order)](#sourcemapconsumerprototypeeachmappingcallback-context-order)\n  - [SourceMapGenerator](#sourcemapgenerator)\n    - [new SourceMapGenerator([startOfSourceMap])](#new-sourcemapgeneratorstartofsourcemap)\n    - [SourceMapGenerator.fromSourceMap(sourceMapConsumer)](#sourcemapgeneratorfromsourcemapsourcemapconsumer)\n    - [SourceMapGenerator.prototype.addMapping(mapping)](#sourcemapgeneratorprototypeaddmappingmapping)\n    - [SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)](#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])](#sourcemapgeneratorprototypeapplysourcemapsourcemapconsumer-sourcefile-sourcemappath)\n    - [SourceMapGenerator.prototype.toString()](#sourcemapgeneratorprototypetostring)\n  - [SourceNode](#sourcenode)\n    - [new SourceNode([line, column, source[, chunk[, name]]])](#new-sourcenodeline-column-source-chunk-name)\n    - [SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])](#sourcenodefromstringwithsourcemapcode-sourcemapconsumer-relativepath)\n    - [SourceNode.prototype.add(chunk)](#sourcenodeprototypeaddchunk)\n    - [SourceNode.prototype.prepend(chunk)](#sourcenodeprototypeprependchunk)\n    - [SourceNode.prototype.setSourceContent(sourceFile, sourceContent)](#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceNode.prototype.walk(fn)](#sourcenodeprototypewalkfn)\n    - [SourceNode.prototype.walkSourceContents(fn)](#sourcenodeprototypewalksourcecontentsfn)\n    - [SourceNode.prototype.join(sep)](#sourcenodeprototypejoinsep)\n    - [SourceNode.prototype.replaceRight(pattern, replacement)](#sourcenodeprototypereplacerightpattern-replacement)\n    - [SourceNode.prototype.toString()](#sourcenodeprototypetostring)\n    - [SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])](#sourcenodeprototypetostringwithsourcemapstartofsourcemap)\n\n<!-- END doctoc generated TOC please keep comment here to allow auto update -->\n\n## Examples\n\n### Consuming a source map\n\n```js\nconst rawSourceMap = {\n  version: 3,\n  file: 'min.js',\n  names: ['bar', 'baz', 'n'],\n  sources: ['one.js', 'two.js'],\n  sourceRoot: 'http://example.com/www/js/',\n  mappings: 'CAAC,IAAI,IAAM,SAAUA,GAClB,OAAOC,IAAID;CCDb,IAAI,IAAM,SAAUE,GAClB,OAAOA'\n};\n\nconst whatever = await SourceMapConsumer.with(rawSourceMap, null, consumer => {\n\n  console.log(consumer.sources);\n  // [ 'http://example.com/www/js/one.js',\n  //   'http://example.com/www/js/two.js' ]\n\n  console.log(consumer.originalPositionFor({\n    line: 2,\n    column: 28\n  }));\n  // { source: 'http://example.com/www/js/two.js',\n  //   line: 2,\n  //   column: 10,\n  //   name: 'n' }\n\n  console.log(consumer.generatedPositionFor({\n    source: 'http://example.com/www/js/two.js',\n    line: 2,\n    column: 10\n  }));\n  // { line: 2, column: 28 }\n\n  consumer.eachMapping(function (m) {\n    // ...\n  });\n\n  return computeWhatever();\n});\n```\n\n### Generating a source map\n\nIn depth guide:\n[**Compiling to JavaScript, and Debugging with Source Maps**](https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/)\n\n#### With SourceNode (high level API)\n\n```js\nfunction compile(ast) {\n  switch (ast.type) {\n  case 'BinaryExpression':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      [compile(ast.left), \" + \", compile(ast.right)]\n    );\n  case 'Literal':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      String(ast.value)\n    );\n  // ...\n  default:\n    throw new Error(\"Bad AST\");\n  }\n}\n\nvar ast = parse(\"40 + 2\", \"add.js\");\nconsole.log(compile(ast).toStringWithSourceMap({\n  file: 'add.js'\n}));\n// { code: '40 + 2',\n//   map: [object SourceMapGenerator] }\n```\n\n#### With SourceMapGenerator (low level API)\n\n```js\nvar map = new SourceMapGenerator({\n  file: \"source-mapped.js\"\n});\n\nmap.addMapping({\n  generated: {\n    line: 10,\n    column: 35\n  },\n  source: \"foo.js\",\n  original: {\n    line: 33,\n    column: 2\n  },\n  name: \"christopher\"\n});\n\nconsole.log(map.toString());\n// '{\"version\":3,\"file\":\"source-mapped.js\",\"sources\":[\"foo.js\"],\"names\":[\"christopher\"],\"mappings\":\";;;;;;;;;mCAgCEA\"}'\n```\n\n## API\n\nGet a reference to the module:\n\n```js\n// Node.js\nvar sourceMap = require('source-map');\n\n// Browser builds\nvar sourceMap = window.sourceMap;\n\n// Inside Firefox\nconst sourceMap = require(\"devtools/toolkit/sourcemap/source-map.js\");\n```\n\n### SourceMapConsumer\n\nA `SourceMapConsumer` instance represents a parsed source map which we can query\nfor information about the original file positions by giving it a file position\nin the generated source.\n\n#### SourceMapConsumer.initialize(options)\n\nWhen using `SourceMapConsumer` outside of node.js, for example on the Web, it\nneeds to know from what URL to load `lib/mappings.wasm`. You must inform it by\ncalling `initialize` before constructing any `SourceMapConsumer`s.\n\nThe options object has the following properties:\n\n* `\"lib/mappings.wasm\"`: A `String` containing the URL of the\n  `lib/mappings.wasm` file, or an `ArrayBuffer` with the contents of `lib/mappings.wasm`.\n\n```js\nsourceMap.SourceMapConsumer.initialize({\n  \"lib/mappings.wasm\": \"https://example.com/source-map/lib/mappings.wasm\"\n});\n```\n\n#### new SourceMapConsumer(rawSourceMap)\n\nThe only parameter is the raw source map (either as a string which can be\n`JSON.parse`'d, or an object). According to the spec, source maps have the\nfollowing attributes:\n\n* `version`: Which version of the source map spec this map is following.\n\n* `sources`: An array of URLs to the original source files.\n\n* `names`: An array of identifiers which can be referenced by individual\n  mappings.\n\n* `sourceRoot`: Optional. The URL root from which all sources are relative.\n\n* `sourcesContent`: Optional. An array of contents of the original source files.\n\n* `mappings`: A string of base64 VLQs which contain the actual mappings.\n\n* `file`: Optional. The generated filename this source map is associated with.\n\nThe promise of the constructed souce map consumer is returned.\n\nWhen the `SourceMapConsumer` will no longer be used anymore, you must call its\n`destroy` method.\n\n```js\nconst consumer = await new sourceMap.SourceMapConsumer(rawSourceMapJsonData);\ndoStuffWith(consumer);\nconsumer.destroy();\n```\n\nAlternatively, you can use `SourceMapConsumer.with` to avoid needing to remember\nto call `destroy`.\n\n#### SourceMapConsumer.with\n\nConstruct a new `SourceMapConsumer` from `rawSourceMap` and `sourceMapUrl`\n(see the `SourceMapConsumer` constructor for details. Then, invoke the `async\nfunction f(SourceMapConsumer) -> T` with the newly constructed consumer, wait\nfor `f` to complete, call `destroy` on the consumer, and return `f`'s return\nvalue.\n\nYou must not use the consumer after `f` completes!\n\nBy using `with`, you do not have to remember to manually call `destroy` on\nthe consumer, since it will be called automatically once `f` completes.\n\n```js\nconst xSquared = await SourceMapConsumer.with(\n  myRawSourceMap,\n  null,\n  async function (consumer) {\n    // Use `consumer` inside here and don't worry about remembering\n    // to call `destroy`.\n\n    const x = await whatever(consumer);\n    return x * x;\n  }\n);\n\n// You may not use that `consumer` anymore out here; it has\n// been destroyed. But you can use `xSquared`.\nconsole.log(xSquared);\n```\n\n#### SourceMapConsumer.prototype.destroy()\n\nFree this source map consumer's associated wasm data that is manually-managed.\n\n```js\nconsumer.destroy();\n```\n\nAlternatively, you can use `SourceMapConsumer.with` to avoid needing to remember\nto call `destroy`.\n\n#### SourceMapConsumer.prototype.computeColumnSpans()\n\nCompute the last column for each generated mapping. The last column is\ninclusive.\n\n```js\n// Before:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n\nconsumer.computeColumnSpans();\n\n// After:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1,\n//     lastColumn: 9 },\n//   { line: 2,\n//     column: 10,\n//     lastColumn: 19 },\n//   { line: 2,\n//     column: 20,\n//     lastColumn: Infinity } ]\n```\n\n#### SourceMapConsumer.prototype.originalPositionFor(generatedPosition)\n\nReturns the original source, line, and column information for the generated\nsource's line and column positions provided. The only argument is an object with\nthe following properties:\n\n* `line`: The line number in the generated source.  Line numbers in\n  this library are 1-based (note that the underlying source map\n  specification uses 0-based line numbers -- this library handles the\n  translation).\n\n* `column`: The column number in the generated source.  Column numbers\n  in this library are 0-based.\n\n* `bias`: Either `SourceMapConsumer.GREATEST_LOWER_BOUND` or\n  `SourceMapConsumer.LEAST_UPPER_BOUND`. Specifies whether to return the closest\n  element that is smaller than or greater than the one we are searching for,\n  respectively, if the exact element cannot be found.  Defaults to\n  `SourceMapConsumer.GREATEST_LOWER_BOUND`.\n\nand an object is returned with the following properties:\n\n* `source`: The original source file, or null if this information is not\n  available.\n\n* `line`: The line number in the original source, or null if this information is\n  not available.  The line number is 1-based.\n\n* `column`: The column number in the original source, or null if this\n  information is not available.  The column number is 0-based.\n\n* `name`: The original identifier, or null if this information is not available.\n\n```js\nconsumer.originalPositionFor({ line: 2, column: 10 })\n// { source: 'foo.coffee',\n//   line: 2,\n//   column: 2,\n//   name: null }\n\nconsumer.originalPositionFor({ line: 99999999999999999, column: 999999999999999 })\n// { source: null,\n//   line: null,\n//   column: null,\n//   name: null }\n```\n\n#### SourceMapConsumer.prototype.generatedPositionFor(originalPosition)\n\nReturns the generated line and column information for the original source,\nline, and column positions provided. The only argument is an object with\nthe following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: The column number in the original source.  The column\n  number is 0-based.\n\nand an object is returned with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.generatedPositionFor({ source: \"example.js\", line: 2, column: 10 })\n// { line: 1,\n//   column: 56 }\n```\n\n#### SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)\n\nReturns all generated line and column information for the original source, line,\nand column provided. If no column is provided, returns all mappings\ncorresponding to a either the line we are searching for or the next closest line\nthat has any mappings. Otherwise, returns all mappings corresponding to the\ngiven line and either the column we are searching for or the next closest column\nthat has any offsets.\n\nThe only argument is an object with the following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: Optional. The column number in the original source.  The\n  column number is 0-based.\n\nand an array of objects is returned, each with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.allGeneratedpositionsfor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n```\n\n#### SourceMapConsumer.prototype.hasContentsOfAllSources()\n\nReturn true if we have the embedded source content for every source listed in\nthe source map, false otherwise.\n\nIn other words, if this method returns `true`, then\n`consumer.sourceContentFor(s)` will succeed for every source `s` in\n`consumer.sources`.\n\n```js\n// ...\nif (consumer.hasContentsOfAllSources()) {\n  consumerReadyCallback(consumer);\n} else {\n  fetchSources(consumer, consumerReadyCallback);\n}\n// ...\n```\n\n#### SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])\n\nReturns the original source content for the source provided. The only\nargument is the URL of the original source file.\n\nIf the source content for the given source is not found, then an error is\nthrown. Optionally, pass `true` as the second param to have `null` returned\ninstead.\n\n```js\nconsumer.sources\n// [ \"my-cool-lib.clj\" ]\n\nconsumer.sourceContentFor(\"my-cool-lib.clj\")\n// \"...\"\n\nconsumer.sourceContentFor(\"this is not in the source map\");\n// Error: \"this is not in the source map\" is not in the source map\n\nconsumer.sourceContentFor(\"this is not in the source map\", true);\n// null\n```\n\n#### SourceMapConsumer.prototype.eachMapping(callback, context, order)\n\nIterate over each mapping between an original source/line/column and a\ngenerated line/column in this source map.\n\n* `callback`: The function that is called with each mapping. Mappings have the\n  form `{ source, generatedLine, generatedColumn, originalLine, originalColumn,\n  name }`\n\n* `context`: Optional. If specified, this object will be the value of `this`\n  every time that `callback` is called.\n\n* `order`: Either `SourceMapConsumer.GENERATED_ORDER` or\n  `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to iterate over\n  the mappings sorted by the generated file's line/column order or the\n  original's source/line/column order, respectively. Defaults to\n  `SourceMapConsumer.GENERATED_ORDER`.\n\n```js\nconsumer.eachMapping(function (m) { console.log(m); })\n// ...\n// { source: 'illmatic.js',\n//   generatedLine: 1,\n//   generatedColumn: 0,\n//   originalLine: 1,\n//   originalColumn: 0,\n//   name: null }\n// { source: 'illmatic.js',\n//   generatedLine: 2,\n//   generatedColumn: 0,\n//   originalLine: 2,\n//   originalColumn: 0,\n//   name: null }\n// ...\n```\n### SourceMapGenerator\n\nAn instance of the SourceMapGenerator represents a source map which is being\nbuilt incrementally.\n\n#### new SourceMapGenerator([startOfSourceMap])\n\nYou may pass an object with the following properties:\n\n* `file`: The filename of the generated source that this source map is\n  associated with.\n\n* `sourceRoot`: A root for all relative URLs in this source map.\n\n* `skipValidation`: Optional. When `true`, disables validation of mappings as\n  they are added. This can improve performance but should be used with\n  discretion, as a last resort. Even then, one should avoid using this flag when\n  running tests, if possible.\n\n```js\nvar generator = new sourceMap.SourceMapGenerator({\n  file: \"my-generated-javascript-file.js\",\n  sourceRoot: \"http://example.com/app/js/\"\n});\n```\n\n#### SourceMapGenerator.fromSourceMap(sourceMapConsumer)\n\nCreates a new `SourceMapGenerator` from an existing `SourceMapConsumer` instance.\n\n* `sourceMapConsumer` The SourceMap.\n\n```js\nvar generator = sourceMap.SourceMapGenerator.fromSourceMap(consumer);\n```\n\n#### SourceMapGenerator.prototype.addMapping(mapping)\n\nAdd a single mapping from original source line and column to the generated\nsource's line and column for this source map being created. The mapping object\nshould have the following properties:\n\n* `generated`: An object with the generated line and column positions.\n\n* `original`: An object with the original line and column positions.\n\n* `source`: The original source file (relative to the sourceRoot).\n\n* `name`: An optional original token name for this mapping.\n\n```js\ngenerator.addMapping({\n  source: \"module-one.scm\",\n  original: { line: 128, column: 0 },\n  generated: { line: 3, column: 456 }\n})\n```\n\n#### SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for an original source file.\n\n* `sourceFile` the URL of the original source file.\n\n* `sourceContent` the content of the source file.\n\n```js\ngenerator.setSourceContent(\"module-one.scm\",\n                           fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])\n\nApplies a SourceMap for a source file to the SourceMap.\nEach mapping to the supplied source file is rewritten using the\nsupplied SourceMap. Note: The resolution for the resulting mappings\nis the minimum of this map and the supplied map.\n\n* `sourceMapConsumer`: The SourceMap to be applied.\n\n* `sourceFile`: Optional. The filename of the source file.\n  If omitted, sourceMapConsumer.file will be used, if it exists.\n  Otherwise an error will be thrown.\n\n* `sourceMapPath`: Optional. The dirname of the path to the SourceMap\n  to be applied. If relative, it is relative to the SourceMap.\n\n  This parameter is needed when the two SourceMaps aren't in the same\n  directory, and the SourceMap to be applied contains relative source\n  paths. If so, those relative source paths need to be rewritten\n  relative to the SourceMap.\n\n  If omitted, it is assumed that both SourceMaps are in the same directory,\n  thus not needing any rewriting. (Supplying `'.'` has the same effect.)\n\n#### SourceMapGenerator.prototype.toString()\n\nRenders the source map being generated to a string.\n\n```js\ngenerator.toString()\n// '{\"version\":3,\"sources\":[\"module-one.scm\"],\"names\":[],\"mappings\":\"...snip...\",\"file\":\"my-generated-javascript-file.js\",\"sourceRoot\":\"http://example.com/app/js/\"}'\n```\n\n### SourceNode\n\nSourceNodes provide a way to abstract over interpolating and/or concatenating\nsnippets of generated JavaScript source code, while maintaining the line and\ncolumn information associated between those snippets and the original source\ncode. This is useful as the final intermediate representation a compiler might\nuse before outputting the generated JS and source map.\n\n#### new SourceNode([line, column, source[, chunk[, name]]])\n\n* `line`: The original line number associated with this source node, or null if\n  it isn't associated with an original line.  The line number is 1-based.\n\n* `column`: The original column number associated with this source node, or null\n  if it isn't associated with an original column.  The column number\n  is 0-based.\n\n* `source`: The original source's filename; null if no filename is provided.\n\n* `chunk`: Optional. Is immediately passed to `SourceNode.prototype.add`, see\n  below.\n\n* `name`: Optional. The original identifier.\n\n```js\nvar node = new SourceNode(1, 2, \"a.cpp\", [\n  new SourceNode(3, 4, \"b.cpp\", \"extern int status;\\n\"),\n  new SourceNode(5, 6, \"c.cpp\", \"std::string* make_string(size_t n);\\n\"),\n  new SourceNode(7, 8, \"d.cpp\", \"int main(int argc, char** argv) {}\\n\"),\n]);\n```\n\n#### SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])\n\nCreates a SourceNode from generated code and a SourceMapConsumer.\n\n* `code`: The generated code\n\n* `sourceMapConsumer` The SourceMap for the generated code\n\n* `relativePath` The optional path that relative sources in `sourceMapConsumer`\n  should be relative to.\n\n```js\nconst consumer = await new SourceMapConsumer(fs.readFileSync(\"path/to/my-file.js.map\", \"utf8\"));\nconst node = SourceNode.fromStringWithSourceMap(fs.readFileSync(\"path/to/my-file.js\"), consumer);\n```\n\n#### SourceNode.prototype.add(chunk)\n\nAdd a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.add(\" + \");\nnode.add(otherNode);\nnode.add([leftHandOperandNode, \" + \", rightHandOperandNode]);\n```\n\n#### SourceNode.prototype.prepend(chunk)\n\nPrepend a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.prepend(\"/** Build Id: f783haef86324gf **/\\n\\n\");\n```\n\n#### SourceNode.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for a source file. This will be added to the\n`SourceMap` in the `sourcesContent` field.\n\n* `sourceFile`: The filename of the source file\n\n* `sourceContent`: The content of the source file\n\n```js\nnode.setSourceContent(\"module-one.scm\",\n                      fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceNode.prototype.walk(fn)\n\nWalk over the tree of JS snippets in this node and its children. The walking\nfunction is called once for each snippet of JS and is passed that snippet and\nthe its original associated source's line/column location.\n\n* `fn`: The traversal function.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.walk(function (code, loc) { console.log(\"WALK:\", code, loc); })\n// WALK: uno { source: 'b.js', line: 3, column: 4, name: null }\n// WALK: dos { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: tres { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: quatro { source: 'c.js', line: 5, column: 6, name: null }\n```\n\n#### SourceNode.prototype.walkSourceContents(fn)\n\nWalk over the tree of SourceNodes. The walking function is called for each\nsource file content and is passed the filename and source content.\n\n* `fn`: The traversal function.\n\n```js\nvar a = new SourceNode(1, 2, \"a.js\", \"generated from a\");\na.setSourceContent(\"a.js\", \"original a\");\nvar b = new SourceNode(1, 2, \"b.js\", \"generated from b\");\nb.setSourceContent(\"b.js\", \"original b\");\nvar c = new SourceNode(1, 2, \"c.js\", \"generated from c\");\nc.setSourceContent(\"c.js\", \"original c\");\n\nvar node = new SourceNode(null, null, null, [a, b, c]);\nnode.walkSourceContents(function (source, contents) { console.log(\"WALK:\", source, \":\", contents); })\n// WALK: a.js : original a\n// WALK: b.js : original b\n// WALK: c.js : original c\n```\n\n#### SourceNode.prototype.join(sep)\n\nLike `Array.prototype.join` except for SourceNodes. Inserts the separator\nbetween each of this source node's children.\n\n* `sep`: The separator.\n\n```js\nvar lhs = new SourceNode(1, 2, \"a.rs\", \"my_copy\");\nvar operand = new SourceNode(3, 4, \"a.rs\", \"=\");\nvar rhs = new SourceNode(5, 6, \"a.rs\", \"orig.clone()\");\n\nvar node = new SourceNode(null, null, null, [ lhs, operand, rhs ]);\nvar joinedNode = node.join(\" \");\n```\n\n#### SourceNode.prototype.replaceRight(pattern, replacement)\n\nCall `String.prototype.replace` on the very right-most source snippet. Useful\nfor trimming white space from the end of a source node, etc.\n\n* `pattern`: The pattern to replace.\n\n* `replacement`: The thing to replace the pattern with.\n\n```js\n// Trim trailing white space.\nnode.replaceRight(/\\s*$/, \"\");\n```\n\n#### SourceNode.prototype.toString()\n\nReturn the string representation of this source node. Walks over the tree and\nconcatenates all the various snippets together to one string.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toString()\n// 'unodostresquatro'\n```\n\n#### SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])\n\nReturns the string representation of this tree of source nodes, plus a\nSourceMapGenerator which contains all the mappings between the generated and\noriginal sources.\n\nThe arguments are the same as those to `new SourceMapGenerator`.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toStringWithSourceMap({ file: \"my-output-file.js\" })\n// { code: 'unodostresquatro',\n//   map: [object SourceMapGenerator] }\n```\n", "readmeFilename": "README.md", "users": {"285858315": true, "bcoe": true, "dwqs": true, "r24y": true, "flozz": true, "rmcgu": true, "weisk": true, "rocman": true, "anticom": true, "lupideo": true, "nbering": true, "sopepos": true, "ubenzer": true, "amorgaut": true, "minxomat": true, "nraibaud": true, "wkaifang": true, "mojaray2k": true, "pirxpilot": true, "qingleili": true, "rubiadias": true, "sadsenpai": true, "stone-jin": true, "abdihaikal": true, "adammacias": true, "princetoad": true, "shuoshubao": true, "flumpus-dev": true, "monsterkodi": true, "josejaguirre": true, "tuomastolppi": true, "chinawolf_wyp": true, "miadzadfallah": true, "shanewholloway": true, "abrahamwilliams": true}}