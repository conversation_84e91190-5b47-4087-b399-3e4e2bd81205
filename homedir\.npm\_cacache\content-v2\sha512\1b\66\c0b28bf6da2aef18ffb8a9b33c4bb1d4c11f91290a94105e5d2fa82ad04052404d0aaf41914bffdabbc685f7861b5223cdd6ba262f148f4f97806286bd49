{"_id": "less", "_rev": "688-5038d5f06f63cbf1275f3e0b9b6c4755", "name": "less", "dist-tags": {"beta": "3.10.0-beta.2", "canary": "3.13.1-next.1", "alpha": "3.13.0-alpha.3", "latest": "4.3.0"}, "versions": {"1.0.18": {"name": "less", "version": "1.0.18", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.18", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "30d5f6144b0b7148ac9732616c0016757bb436d1", "tarball": "https://registry.npmjs.org/less/-/less-1.0.18.tgz", "integrity": "sha512-uynxAsi6LoDzcpgJeEmcAHq84h42DYsnLP+lZragDUMYlZY3SOu/EmkVJVXV5/KKEz7i1jzh6ydcjEpRIMi0SQ==", "signatures": [{"sig": "MEUCIQCefAuW8sxff3KYz6nYjUzLZGpmmqvtBPXv3wqVaUfDbwIgDNTRNrjSouB7Ebhuk5Ce6XveZtd1oWicKeFCv0Ugg4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.21": {"name": "less", "version": "1.0.21", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.21", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "a9728759e192ebf9270666e2d8c2805fc72e22b0", "tarball": "https://registry.npmjs.org/less/-/less-1.0.21.tgz", "integrity": "sha512-NdjJwcqZxPcw1APUbiNgytsfq38bAGtDTuLA/Ivw+SsLV+xLzEoATwBOttsrU/OqBD/umywj4U31BwPVSqmB7w==", "signatures": [{"sig": "MEUCIQC+ksNy4nUeTQ15OySXGEEf+7zNhzDeOfkBSRERTSquYwIgLHqpUcyYTLFzaEUDFxZ8bI3R2uqjMJM4c0Kcq7DGuS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.19": {"name": "less", "version": "1.0.19", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.19", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "8db3980ddabcbf830e40a07ead46b079ce442639", "tarball": "https://registry.npmjs.org/less/-/less-1.0.19.tgz", "integrity": "sha512-YeYieywDYhk3YCnygU42nb7UVnsd43Jv7YVbHInvXv+2vGUaGT/Rn6lt1m0ArN+qSEDzRSwfaJOCleo/5lr6kA==", "signatures": [{"sig": "MEYCIQCRUYbVaRkKST4flgIYX9BKqdXXL0Pp/JHKEP3Y9ulFvwIhALWV9SVED76HJ2DG5ll/d4AkAiuHaYIDvES7rrMiFPKf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.14": {"name": "less", "version": "1.0.14", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.14", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "0c7c203d4fb59d987a417b6c2f6b516df4b9d66a", "tarball": "https://registry.npmjs.org/less/-/less-1.0.14.tgz", "integrity": "sha512-3A+9Kd+vFr7OALIEyGpPE/mTq1IZymgVJ71juT4YD+AUN0YoV3SvAUUy3bdwocv9ekAnaZhjyP8r4s3Jb0PVpQ==", "signatures": [{"sig": "MEYCIQDJtZIGdq4yj7Mu+bt00oJwFDb6OL7AQr0pzJpKtuzp/wIhAPlPP/CEtt8L5/Sel0mtLAOKYpGBz++cLVUFTmeHk/05", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.10": {"name": "less", "version": "1.0.10", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.10", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "07e3b9bf39e5d780ec1232a6f99a389f1e59cbf2", "tarball": "https://registry.npmjs.org/less/-/less-1.0.10.tgz", "integrity": "sha512-ILPuVu20MO5MN5epx+Rjimg35A851CQG0uGU4T5AayT4EMJaOLz9ocpC59vJpc1sE8EBB0KetgNXwpmS/dmquQ==", "signatures": [{"sig": "MEQCIH+VJyaoU4duKlz3HaTqhAv+ahLY8GgsYmj0ZzHmEJMYAiBRZM0dUWZ4dMhQGWLu6OlalTXLtuMKIse1Hipt9W1SiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.11": {"name": "less", "version": "1.0.11", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.11", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "f15edd673538d681f4a764759efb574776030efb", "tarball": "https://registry.npmjs.org/less/-/less-1.0.11.tgz", "integrity": "sha512-RJER4EOhMEgf24t0dLPnbw3X/JUypwYewXtrtjB5+cYYCUwgy0r/1aRPZS222bnEgvp49nEXXm+9rjPQ2Qe2Bg==", "signatures": [{"sig": "MEQCIDo9BEWtJRc9O860Qyp03sqExlkperTryvSVGZ4YwcqhAiBws9K23amtjY0ZMDtOGfNvjyJ/YzQzextEOD83NoPtog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.36": {"name": "less", "version": "1.0.36", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.36", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "d64632580d90ac278a857dbf27ab98017797bc21", "tarball": "https://registry.npmjs.org/less/-/less-1.0.36.tgz", "integrity": "sha512-Cyu/HvFkV+xE3fMuZVDhwLP3EjjQ9rQ/dK7q5c051laYHXQg5rgy4FQVVZXHi+y4ZgtpaU4y/qGTR2Vx4a2/ww==", "signatures": [{"sig": "MEQCICYoMMrd7HVbzoCZVaiLeAX9HmKnLwJ9FCAgjpL/GeHlAiBAs8Zg0/SUYXnAK8QklqHiBsDn57y9wsSmQ6ccWTlMgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.2.0"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.32": {"name": "less", "version": "1.0.32", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.32", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "0079c9d3b87b31e5d24926da5cded54730085797", "tarball": "https://registry.npmjs.org/less/-/less-1.0.32.tgz", "integrity": "sha512-2CWeUw6H9y69U9IBMdvfGBlbOlVlX9yejqdBAdVMfVgPPsyD/+njxIMnc/X50XnMcrUgtfl9K0zoFCfLSZEiWw==", "signatures": [{"sig": "MEUCIQDSej86P5NUm/HQzajiB5kIyOqLNNqiKwLgDhHZicABTAIgRWldaYLSlSRze5hff5las8BuWDVesAOlhn5Sb2J3Qgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.1.93"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {"lib": "./lib/less", "test": "./test"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.40": {"name": "less", "version": "1.0.40", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.40", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "02eebb15194173507b421af668713063099e08cf", "tarball": "https://registry.npmjs.org/less/-/less-1.0.40.tgz", "integrity": "sha512-EPR74JpWW08zf1OaC8i/o34ajc3xEDIC5OIdfmAw06aKL4cevA+PCTnUqMVCW+K6GuMGJ6AA9NFWrAtDKVmVQg==", "signatures": [{"sig": "MEUCIAQFqTYR+1pwkN22EEqv4UcNnmtwIQ0ZIvttSg1A3gUDAiEAp9F3TImL2ukkITMwbpA7DoQP38HgoSb5iJM850K6+Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.2.0"}, "_npmVersion": "0.2.8", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.2.5", "_nodeSupported": true}, "1.0.41": {"name": "less", "version": "1.0.41", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.41", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "c108f97a105887d6559be8ed6a0ce922c58da1bf", "tarball": "https://registry.npmjs.org/less/-/less-1.0.41.tgz", "integrity": "sha512-ME4EXthpaadkHfXBom1Q0o6LxMtpP0Qx4kzxPA/a2BEMR2ULmEr+UGq+5rJKx7GyKliAwCMdjmZWoNcKpcFADw==", "signatures": [{"sig": "MEYCIQDPNuatNpF4dNu7qUQB1wYf7HzAz8wYEF5eOQf78g3sNAIhAKwCcJM6U1F1arzQMTAqbBoVOZkFW0xARqN6jZNTLuQq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "files": [""], "engines": {"node": ">=0.2.0"}, "modules": {"less/tree.js": "lib/less/tree.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/browser.js": "lib/less/browser.js", "less/tree/url.js": "lib/less/tree/url.js", "less/functions.js": "lib/less/functions.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/variable.js": "lib/less/tree/variable.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/javascript.js": "lib/less/tree/javascript.js"}, "_npmVersion": "0.2.15", "description": "Leaner CSS", "directories": {"bin": "./bin", "lib": "./lib", "test": "./test"}, "_nodeVersion": "v0.3.7-pre", "_defaultsLoaded": true, "_engineSupported": true}, "1.0.5": {"name": "less", "version": "1.0.5", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.5", "contributors": [], "lib": "lib", "url": "http://lesscss.org", "dist": {"shasum": "4aa5f1e3005ca582480e33b90831d67f80b88536", "tarball": "https://registry.npmjs.org/less/-/less-1.0.5.tgz", "integrity": "sha512-q0LX9p1xyFQmwI9Mk0BlPLnuVknphJcwg/rgdwi037sLXyiER78mO/Pyfvj/Pv/YAurY8Sq4rX1pTqRo9COYDw==", "signatures": [{"sig": "MEYCIQDMQUuGb57ArNitn5JlxVl14sR/kxQbu1zDBO24Jvmr/wIhAN9lWuAMfoj3jishOTdPj3ZrEtFKry+/pghiKiOZdZLd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "_npmVersion": "0.2.7-2", "description": "Leaner CSS", "directories": {}, "_nodeVersion": "v0.3.1-pre", "dependencies": {}, "_nodeSupported": true}, "1.0.44": {"name": "less", "version": "1.0.44", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.0.44", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "f5438d1955e1bfbc3beae4f4266907de9a721ccb", "tarball": "https://registry.npmjs.org/less/-/less-1.0.44.tgz", "integrity": "sha512-ZfmeOL16uHaUBvzXr7Yhqm6ddlXk15uxtWo6hxY02zqrqX1m+KERbQql57y/2ffRquI6wUuJZbZEqt0i3Quq8w==", "signatures": [{"sig": "MEYCIQCviDYWUrsyBdWmM8dSkiTdmgKMhIAg2jT9dZ+yr5NWWAIhAJV9v9EpSuO+YvAo6DJBingvh56i1+utb9U4fzsCzlg4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "files": [""], "engines": {"node": ">=0.2.0"}, "modules": {"less/tree.js": "lib/less/tree.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/browser.js": "lib/less/browser.js", "less/tree/url.js": "lib/less/tree/url.js", "less/functions.js": "lib/less/functions.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/variable.js": "lib/less/tree/variable.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/javascript.js": "lib/less/tree/javascript.js"}, "_npmVersion": "0.2.14-6", "description": "Leaner CSS", "directories": {"bin": "./bin", "lib": "./lib", "test": "./test"}, "_nodeVersion": "v0.4.0", "_defaultsLoaded": true, "_engineSupported": true}, "1.1.0": {"name": "less", "version": "1.1.0", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.1.0", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "7c7dbdf1541158bf525d51a8eb8357400b72c888", "tarball": "https://registry.npmjs.org/less/-/less-1.1.0.tgz", "integrity": "sha512-Zj9<PERSON>umbwoEarCKpM2fRD18bIPmpJWcQjJ7oOJwYzHafk593/ZLsJ0dopMiavBjgnbQtr6ZJpwVyPcf7lvykOCQ==", "signatures": [{"sig": "MEUCIQCkntIq1itm6er0AMFo7yvLuLCGrzegsIN9SmvRJ1xXRQIgYnybilW5UpgXEt7Oo1r/VNbFOeRb6gAmVnnxoc4Pg6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "files": [""], "engines": {"node": ">=0.2.0"}, "modules": {"less/tree.js": "lib/less/tree.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/browser.js": "lib/less/browser.js", "less/tree/url.js": "lib/less/tree/url.js", "less/functions.js": "lib/less/functions.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/variable.js": "lib/less/tree/variable.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/javascript.js": "lib/less/tree/javascript.js"}, "_npmVersion": "0.2.14-6", "description": "Leaner CSS", "directories": {"bin": "./bin", "lib": "./lib", "test": "./test"}, "_nodeVersion": "v0.4.0", "_defaultsLoaded": true, "_engineSupported": true}, "1.1.1": {"name": "less", "version": "1.1.1", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.1.1", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "7bda147c7cdb16f1fc638ca59f2a29e21f7b20f1", "tarball": "https://registry.npmjs.org/less/-/less-1.1.1.tgz", "integrity": "sha512-/u8yE3by4NOkkgVsvW2GZRtvYGyjDvnQXPvHyf2E18328Wn3AvHKcDTXeJP9F2upKzLDQyQJuBDSqU7pdccYqw==", "signatures": [{"sig": "MEUCIAftM19hp7qrflMhS0Ou2Gj19UDMjjraCSPcGYhXD06KAiEA3TADywslFF0Ps78pts1c2NQElzboOORTGtWb/FYR1CA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "files": [""], "engines": {"node": ">=0.2.0"}, "_npmVersion": "0.3.18", "description": "Leaner CSS", "directories": {"bin": "./bin", "lib": "./lib", "test": "./test"}, "_nodeVersion": "v0.4.5", "_defaultsLoaded": true, "_engineSupported": true}, "1.1.2": {"name": "less", "version": "1.1.2", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.1.2", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "be31f2285d8534ef7a2b48532f0727eb190bdde9", "tarball": "https://registry.npmjs.org/less/-/less-1.1.2.tgz", "integrity": "sha512-gwEgEuPLN+DWSN+aF9iFRChKIvriqJG2habioonGrmU/APuGd5A7AgsfXqZo5w2avNcJw+lhqkKpHKJPRzIoIg==", "signatures": [{"sig": "MEUCIB6BNfL3eReHHsJgG32zBnKAlGUbHC99UkohqqTxiybIAiEApBiyIJXoN7vhTFWlp28d87+c6bbCeYj33ZCxGhEJv28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "files": [""], "engines": {"node": ">=0.2.0"}, "modules": {"less/tree.js": "lib/less/tree.js", "less/index.js": "lib/less/index.js", "less/parser.js": "lib/less/parser.js", "less/browser.js": "lib/less/browser.js", "less/tree/url.js": "lib/less/tree/url.js", "less/functions.js": "lib/less/functions.js", "less/tree/call.js": "lib/less/tree/call.js", "less/tree/rule.js": "lib/less/tree/rule.js", "less/tree/alpha.js": "lib/less/tree/alpha.js", "less/tree/color.js": "lib/less/tree/color.js", "less/tree/mixin.js": "lib/less/tree/mixin.js", "less/tree/value.js": "lib/less/tree/value.js", "less/tree/import.js": "lib/less/tree/import.js", "less/tree/quoted.js": "lib/less/tree/quoted.js", "less/tree/comment.js": "lib/less/tree/comment.js", "less/tree/element.js": "lib/less/tree/element.js", "less/tree/keyword.js": "lib/less/tree/keyword.js", "less/tree/ruleset.js": "lib/less/tree/ruleset.js", "less/tree/selector.js": "lib/less/tree/selector.js", "less/tree/variable.js": "lib/less/tree/variable.js", "less/tree/anonymous.js": "lib/less/tree/anonymous.js", "less/tree/dimension.js": "lib/less/tree/dimension.js", "less/tree/directive.js": "lib/less/tree/directive.js", "less/tree/operation.js": "lib/less/tree/operation.js", "less/tree/expression.js": "lib/less/tree/expression.js", "less/tree/javascript.js": "lib/less/tree/javascript.js"}, "_npmVersion": "0.2.14-6", "description": "Leaner CSS", "directories": {"bin": "./bin", "lib": "./lib", "test": "./test"}, "_nodeVersion": "v0.4.0", "_defaultsLoaded": true, "_engineSupported": true}, "1.1.4": {"name": "less", "version": "1.1.4", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.1.4", "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "cbc5714f77ff209d89db569cb01dc4b7266de135", "tarball": "https://registry.npmjs.org/less/-/less-1.1.4.tgz", "integrity": "sha512-MqCZPYvIwtyuXUIrcF2TDhmTcOeu44Ylku5PZxf1aly0kLrTXNt/4/B+jYYdOPKPPA5Fb/hzYve9KSRCL92NKQ==", "signatures": [{"sig": "MEUCIHJUfTm3hcu5CBbcCFPTKidUqOAG2xrgN8Mb/+Ej8mKOAiEAxgl4do/rMaY7RFQLkxyZZQrx3dWSLhJfnaXeFNipV1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmVersion": "1.0.1rc7", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.4.5", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.1.5": {"name": "less", "version": "1.1.5", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.1.5", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "802e9ceedd6b221bae57e1adb930eeb9efd31d6a", "tarball": "https://registry.npmjs.org/less/-/less-1.1.5.tgz", "integrity": "sha512-EVKkXwcdx1VrBEYeLZJxgUQWH8yeWZZT4VsIO294E2xRyXhXHcKIqWC8VBjXeTcKOKfUrh4ZWuJKSjTmygrkcw==", "signatures": [{"sig": "MEUCIQCH+7r/os5qov/ZSlb0pcLbY4OTBUTXdHAw4bEUL6RAAAIgScsGhvdNqa2ftj1P0c9tYHVxAWMjLiCRbcolG78ct9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_npmVersion": "1.0.105", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.6.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.1.6": {"name": "less", "version": "1.1.6", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.1.6", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "9609e99324286bd7049bc35649e997b6f90fcc78", "tarball": "https://registry.npmjs.org/less/-/less-1.1.6.tgz", "integrity": "sha512-SCMDyUIMlwrIQHvjlbrAPps+H+1z1V2p14FPcBTwx50jMOfoB8gfw8YTrRQFrj1uC1C0eOyxcVYbr1rUdM7BFw==", "signatures": [{"sig": "MEUCIG52d0Z7xFIPgAQHwGRudpIdJ/0Jv9bFXKgRzOpZMuWSAiEA+SNzfOc9KQGOiqk4V+JwGOsE0lP9esvytgcLS4hnrXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.6.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.2.0": {"name": "less", "version": "1.2.0", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.2.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "2762a23b042438cd0ced0cc2a74b66f7725e419f", "tarball": "https://registry.npmjs.org/less/-/less-1.2.0.tgz", "integrity": "sha512-PEDRVKj3Af3cZ1SPnac67/6nCIMUfXCH1PhGIyQ7lTlg1BlCSlozw9RQQefhOyHoYoN0vA1575utKTyvvAjvwA==", "signatures": [{"sig": "MEUCIQCgaaKc4RpjW3w3qFKhtsjnBqx2thmsvHRcifwT7obTwAIgOzV310axmwanR5HAnXVv52gBf7XvQwwOpq/Qhm9mf7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.6.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.2.1": {"name": "less", "version": "1.2.1", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.2.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "b76fd02c2ebc5d0805c59b9529951f22a45b65e7", "tarball": "https://registry.npmjs.org/less/-/less-1.2.1.tgz", "integrity": "sha512-aajDwOS9Z59LjgD6B2BMeekVh1MIZXaRwHGUX5mSJMH6rx++m4S079S3OTBw5T1cajRLCAfuCoaBgvtWRe6iaw==", "signatures": [{"sig": "MEUCIQDW+q5NPH2RETmxa1f7FV0gstyBcHiFxbPn3ubmTG/fWgIgVXaS9Kz/pGInDN1d3+QTLTqrHhochS3G0tbTIFVsISg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_npmVersion": "1.1.0-beta-10", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.6.7", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.2.2": {"name": "less", "version": "1.2.2", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.2.2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "0d6a59d640cc79d2a228b477c99cc4a45bd2c443", "tarball": "https://registry.npmjs.org/less/-/less-1.2.2.tgz", "integrity": "sha512-BvoSQd9WKn86SikksNzeRkcxrm4IzRey2VgBA5dZwWd2QTX5VqEJHvGTefhptegb8qiU38/vxjwz0GJA/SBQ9Q==", "signatures": [{"sig": "MEQCIDwIsBRScrwQuOglkCQ/nyr8vjG0iO+UmU3/Q2/MdrX8AiAwy1H+lpsF8J7lkRR0Odgw3JaT00JEdFUIkWCzu5ecwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_npmVersion": "1.1.0-beta-10", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.6.7", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.3.0": {"name": "less", "version": "1.3.0", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.3.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "5726170cb5661d7449942c78fb3a894d74e8103c", "tarball": "https://registry.npmjs.org/less/-/less-1.3.0.tgz", "integrity": "sha512-8BwS8thAMufSfrttJjur2UqwiNS/J7Z12QhrhU3kKpOK1bxYkz0Xf2aP0zb/PfPMRX0xycnj1+SELHtev1nJlQ==", "signatures": [{"sig": "MEUCIQCB5m8e0U6QZ+6uRN+w/Qf2joR2E2hF2sqiZky0WSPQNAIgXB4Ehb6/PFEpK1PaMYPJ8t2u4XrvtH85J6mshTSg0ZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "cloudhead", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "v0.6.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.3.1": {"name": "less", "version": "1.3.1", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.3.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [], "bin": {"lessc": "./bin/lessc"}, "url": "http://lesscss.org", "dist": {"shasum": "4cf72898b2081a96463f9c38dca5c89fc20a9ba8", "tarball": "https://registry.npmjs.org/less/-/less-1.3.1.tgz", "integrity": "sha512-ebyiYk9IdtoPThEDAu3PPhmslq+KKZfdLPtkKgHF5QqDQo0U52y67Qd6cfhNVPDhwalF3E6IervYMupuBOks7g==", "signatures": [{"sig": "MEUCIQC94DFGJc5bKR4V6Kk66KOFdkMlsZRVjrhlELY/NHPCwwIgXINu+zKOWouZ61iqV/eYOnXOfFR5LZhkLuDlP1fv4bU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test"}, "description": "Leaner CSS", "directories": {"test": "./test"}, "devDependencies": {"diff": "~1.0.2"}}, "1.3.2": {"name": "less", "version": "1.3.2", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.3.2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "25e86b65c487a6311a181f869337af3ad3081a2b", "tarball": "https://registry.npmjs.org/less/-/less-1.3.2.tgz", "integrity": "sha512-YkiZDzQk+MSX5dVk0WbTZhuxMpZZ/CR5e7Gqj/uY7mI+WWCJUPCTQOK8u2n+RQwz5ZQ6pImnh2m+YTJORKZLXA==", "signatures": [{"sig": "MEYCIQDhSOFohla6QqVvx1bbbvBEFxCW4ODsiDV7q5rzKPLySAIhAIOOUXGyWSt1lgiCAeH3x45pBARipMxUHjNJxbUDclcy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"ycssmin": ">=1.0.1"}}, "1.3.3": {"name": "less", "version": "1.3.3", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.3.3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "7ee8f300a41080f3544c80c7a70cdf6a61280cf9", "tarball": "https://registry.npmjs.org/less/-/less-1.3.3.tgz", "integrity": "sha512-y9el4KFU/bjCb0IvDoPX9ncwZcIZZtxKlYlNcXXqcfYbqzjfKM+TjLutrE9HwMQ+PqzBAPFI3NI6KRNbMBvc3Q==", "signatures": [{"sig": "MEQCIFJofkpm6iGDRAzOXQqr3An+oei4tx/kpt3vCZqVG7FDAiA5N+KlnP/YQtfKgbr+pvLwhKkKJzD5pseNXGo+xEembA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"ycssmin": ">=1.0.1"}}, "1.4.0-b1": {"name": "less", "version": "1.4.0-b1", "keywords": ["css", "parser", "lesscss", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.0-b1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "dist": {"shasum": "527744c9711d2cd7cc1eed32a39bce802314cfb2", "tarball": "https://registry.npmjs.org/less/-/less-1.4.0-b1.tgz", "integrity": "sha512-ZRsuHy4UDqsJqLBYmciPov6msMBweeGBeHQE8n5sJlRCLOLQuUD9wSIwS4LJ4n15JXgTzrYUL+agqXchwYOi2A==", "signatures": [{"sig": "MEYCIQDS2nFiCTeGeS2uzBJOEL6mPmb3b333QkMWwNJzS1YDOgIhALrWpeRySW0qUj5XbbXhJzz/GXocdAy1qO0AG3y0QEWW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.4.0-b2": {"name": "less", "version": "1.4.0-b2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.0-b2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "dist": {"shasum": "8bb858b02956ca8001e9e222b2ee00f050f8ec3e", "tarball": "https://registry.npmjs.org/less/-/less-1.4.0-b2.tgz", "integrity": "sha512-c3tm9ENaRiOx7fUxBeTsCZKgV5nzAroykDzA91S9zZNp/U8dagrhhXh1d/Kxyv3iuPnje5Kc8Ge/JiHJny4dMw==", "signatures": [{"sig": "MEQCIDG2xevzJyg6aWI5NKAtEG2CEv9O2YQ5okdX7JrB1u0FAiARGZv7h1i3Oc0QHSHecc+VqxZFrqZVSBlKFwTGiqxTpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.4.0-b3": {"name": "less", "version": "1.4.0-b3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.0-b3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "dist": {"shasum": "06f6fb3e676f3fd1b42173ab7a4a34f765e47f2e", "tarball": "https://registry.npmjs.org/less/-/less-1.4.0-b3.tgz", "integrity": "sha512-+F+z/aWwFf0zT/kc9aWkbjQG5fc+v3UVIHHci/q2gMsaza522Pj5i6DnJ3DCpZVaSCq5VkQMa4TULKQpxRdzmQ==", "signatures": [{"sig": "MEUCIQDUIGU7xezIjY3BmHR8ombGfR1ihHb4mSRM6lckWRO/pwIgDvZM+hhf0D3dzkmVAS7XCWjaR8Ub4prye3g/guJYsAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/cloudhead/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.4.0-b4": {"name": "less", "version": "1.4.0-b4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.0-b4", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "dist": {"shasum": "508782a7c1432f206e8a2c48ad6275da8d96c3d4", "tarball": "https://registry.npmjs.org/less/-/less-1.4.0-b4.tgz", "integrity": "sha512-raitVlCBLZrtG3LA1L4R+4eUYceA++Je6ii0d/XzSbu8uo5RAxRaF/ZMVMRntmbnbQFHmTb0BZZCBQMV54v+LA==", "signatures": [{"sig": "MEUCIQDCE5jWbAultKBrjXOgYwudGQdUatOVAzXpVdO/D6UH2gIgd+Z+byQMYZ31Oq3xF9Rb9Lqhi38bTRBLVqJf77bRaEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.4.0-b4.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/cloudhead/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.4.0-b4.tar.gz", "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.4.0": {"name": "less", "version": "1.4.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.0-beta.js"}, "dist": {"shasum": "63e02a0d41ccf4f26b3feb40021d945d6aa48b14", "tarball": "https://registry.npmjs.org/less/-/less-1.4.0.tgz", "integrity": "sha512-TlIEl0H5pkZ/fB9lG035O4B4GAAIqh+r84ljlKHsZXH4NcttqGmulLL6m3Oxmr1HBLXWC44QAtgFPvI2DMCI7g==", "signatures": [{"sig": "MEUCIQCcH23TZzqRp01Da/WLUeX05tWkMVfMpfKdqjLw985qhwIgDzMTdr0WU+rJEAx6e3GngaGfEH2CVSw9Iwr799+Z/W4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.4.0.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/cloudhead/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.4.0.tar.gz", "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.4.1": {"name": "less", "version": "1.4.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/cloudhead/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.1.js"}, "dist": {"shasum": "4a93e97abb5c33514434a8e80749fe9f5eb871f6", "tarball": "https://registry.npmjs.org/less/-/less-1.4.1.tgz", "integrity": "sha512-noEb1n/T/kWQsID13jxD6Tk/AgUj1f4AtuIljeYD43sQ9wx2EqWhZBwomH+NjYwLZGDseSO1LW8K9G1H+3SbiA==", "signatures": [{"sig": "MEUCIQDx7OQBTcbJNDa9cOFOcpXafpAycy2OD36Uh+h+CObJowIgWtVv/K+SaAyZoO91eqN1q9bDWOzBYzilNlyu4IbK8KA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.4.1.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/cloudhead/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.4.1.tar.gz", "repository": {"url": "https://github.com/cloudhead/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.4.2": {"name": "less", "version": "1.4.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.4.2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.1.js"}, "dist": {"shasum": "b7deefe98a3a87bee364411b3df2d1efe5a412d0", "tarball": "https://registry.npmjs.org/less/-/less-1.4.2.tgz", "integrity": "sha512-mJy2f4cQ5cjog3xErRXkUziR7X+99wpH1Zzt/KLEplVhWzCb5fCY3hwBhbA2xEHRVBR/rKCB0yyMUNiaKEpzgg==", "signatures": [{"sig": "MEQCICt3c4uG9+VCKV+4A6fEYJXCvpdohm6m1muzsr4cHnddAiA9FisErSKyVF4YjRmXqN7N6e4Gn/YUSUuB89GL1wjekg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "./less-1.4.2.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "./less-1.4.2.tar.gz", "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}, "devDependencies": {"diff": "~1.0"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "ycssmin": ">=1.0.1"}}, "1.5.0-b1": {"name": "less", "version": "1.5.0-b1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.5.0-b1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.2.js"}, "dist": {"shasum": "2231ce259ef13193cf888bdac56e853e388775fa", "tarball": "https://registry.npmjs.org/less/-/less-1.5.0-b1.tgz", "integrity": "sha512-DrWv+m6imCBNbKPH4BHjZNTcVN2hd8vEyEe9HDjrsBzcL/JSNRADEWAD1LnJ/h1Bf7bxGJ0468A6AUakt6mfoA==", "signatures": [{"sig": "MEYCIQC2c+37mZO8aiF+knGAuePzaauIRt+e0REyaFJMwuug3AIhANA+2Hle7bIzD+V/1fAaedZ+ENfGPm5TQee/0qZpulEa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.5.0-b1.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "make test", "pretest": "make jshint"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.5.0-b1.tar.gz", "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "jshint": "~2.1.4", "http-server": "~0.5.5"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}}, "1.5.0-b2": {"name": "less", "version": "1.5.0-b2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.5.0-b2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.2.js"}, "dist": {"shasum": "de5ede5587013986ab1841c8ac1517db447df7f9", "tarball": "https://registry.npmjs.org/less/-/less-1.5.0-b2.tgz", "integrity": "sha512-W17QZlFzywfHGMyHHbeimNYnPGpu+DW6GohExOqIFDkdHCk5fDnrlJ6Nlo6NPQ+Wax/G+1hVv7s7R6sjF5UVvQ==", "signatures": [{"sig": "MEUCIQDxitlUiHgpz/rYLj9/bKBBuCHDF/vIErH9A9SGrhk/eQIgdiu8lAw4+3K+nd2eOI6AmCUsdS7nO/MiAqjBw+qkJMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.5.0-b2.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.5.0-b2.tar.gz", "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}}, "1.5.0-b3": {"name": "less", "version": "1.5.0-b3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.5.0-b3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.2.js"}, "dist": {"shasum": "6b7a910a1b23181d6b314c78be1f60dc696b3b72", "tarball": "https://registry.npmjs.org/less/-/less-1.5.0-b3.tgz", "integrity": "sha512-xJmhC8GQaXT3cABcvI3Rn+NXHX8mDMqP9stXbK1EGX1eROVhybNX6MhfL8hdXlUx0Vnq9XxLc6vAp9oKGnXsig==", "signatures": [{"sig": "MEUCIQCChQ2acEgSobjv6mVb55QQ/NCOP6rzrWLR0s3KsD9+CQIgfD09HE2ylHOJ3jgVtW9nwsb3qYUo4Unb/MS/qHOAYIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.5.0-b3.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.5.0-b3.tar.gz", "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}}, "1.5.0-b4": {"name": "less", "version": "1.5.0-b4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.5.0-b4", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.4.2.js"}, "dist": {"shasum": "337b9443b43ff2b7bcd069d211a1ea430b026e62", "tarball": "https://registry.npmjs.org/less/-/less-1.5.0-b4.tgz", "integrity": "sha512-9j/Cu0fmOmHo5NJcPowdcDnqX7b1/LUMNmgDqeWbA1l9Xi5aTtVH9ZXBpcqG7kEOV3dIci7EnfQV5rjJLl4rbA==", "signatures": [{"sig": "MEUCIQDYrNCzUNVBmmPbeLWxiySGErDUkGsPyeR2WrdlanH3UQIgAbY1QIS3Fh51+RKHg01iqf0kGHLZd1kz9ASQizyaYO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": "less-1.5.0-b4.tar.gz", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "_resolved": "less-1.5.0-b4.tar.gz", "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}}, "1.5.0": {"name": "less", "version": "1.5.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.5.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.5.0.js"}, "dist": {"shasum": "a97be1d2607577f5f33632a7e098024ab828f699", "tarball": "https://registry.npmjs.org/less/-/less-1.5.0.tgz", "integrity": "sha512-8am+d7V0BwIMW8R8bduNdv7v4C4/j49GQghLllIsLAFZv8eu+1/JwCUyrJbcKpYFfarseNd83GtqdLfMO0dMsw==", "signatures": [{"sig": "MEUCIQC4vd3hu0cdwpJUaNfTmBw2rptNCFXNzcxjOzP2d2J5LAIgKvTfWvPobhSVMDLKK5kqVkzyaeo3hiTUsz1RufySn50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.6.0", "grunt-contrib-uglify": "~0.2.2", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.1"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.4", "request": ">=2.12.0", "clean-css": "1.0.x", "source-map": "0.1.x"}}, "1.5.1": {"name": "less", "version": "1.5.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.5.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.5.0.js"}, "dist": {"shasum": "633313130efd12a3b78c56aa799dab3eeffffff4", "tarball": "https://registry.npmjs.org/less/-/less-1.5.1.tgz", "integrity": "sha512-F4OM28EGxbZKH1FHEKAWNC6LfdbzLzA0AVpiEFwkh4a3NpSYZ0zqToehlmBxAnbXMUphnaakjecZUgPHEemkRA==", "signatures": [{"sig": "MEUCIQDhNmFRU5d+4LvZaHpWhFmW8fa1fJinLe1lsH2hLpACnwIgCr6tf5ie8FFUPeTj5jDWTUGBlJPYjAWxMbyCnmqViAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}}, "1.6.0": {"name": "less", "version": "1.6.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.6.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.5.0.js"}, "dist": {"shasum": "f0e7d314e53df05d9d52592c159f31bca4e62bb3", "tarball": "https://registry.npmjs.org/less/-/less-1.6.0.tgz", "integrity": "sha512-gg4Mw3IWiTBiaitDaLo6SKNWETbjrrH+LWJOu53Xd+HH5SkQvOxbOWdPUrc1SASm8r2VzLo3X405b3wPDXhqGQ==", "signatures": [{"sig": "MEUCIEftdkyWhex5/iryfsI0mmp6zCtUlkf60xMwxPk8ki2aAiEAsfQs6IrfZcg0tenOy81i9M5n/jmlVRNcGHI/w1QIy9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}}, "1.6.1": {"name": "less", "version": "1.6.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.6.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.5.0.js"}, "dist": {"shasum": "54e5e8d7b4fc43a14f8a32aaf0bae2d45ac287ca", "tarball": "https://registry.npmjs.org/less/-/less-1.6.1.tgz", "integrity": "sha512-jRBEOVHKFSYVZh7KmYLktt+SbtlhTUsveKXOQ6E/HSgrcD1ikdOGcX6JFlaFnnhA60qzIdlfpJd8PAfkeXftWQ==", "signatures": [{"sig": "MEQCIA6lVekwnbc0FL5hTcbFIx3/rqYe36X3nM/rkUfxImFOAiBBRwvKEGiT25ha/dXe1b9ibcOCZyhU/g9Ty5Xf3bKtxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}}, "1.6.2": {"name": "less", "version": "1.6.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.6.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.5.0.js"}, "dist": {"shasum": "86556e6ab8f9af4d8b853db16c5f262e94fc98a0", "tarball": "https://registry.npmjs.org/less/-/less-1.6.2.tgz", "integrity": "sha512-c76/CPoW0zRBpUwBq+E9X1IufXrBf4b7WGuYw6ycxPVKFybMGstjtfwiWdvKxKwc7+IeRdCTmA8QydAQ4e6XFw==", "signatures": [{"sig": "MEQCIDgrNm8CN7M4+sVLT/KVS3o71T7UaajauvF50GfL0wTSAiB3yQxsLzIbCUVx5MUx5QHKJUBJVokAUIDKDKe0SStKlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}}, "1.6.3": {"name": "less", "version": "1.6.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.6.3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.6.3.js"}, "dist": {"shasum": "71ce89ec30b774b3567f254c67958f2f2c193bde", "tarball": "https://registry.npmjs.org/less/-/less-1.6.3.tgz", "integrity": "sha512-3PkBx2CaCayPp9wiLImkCWD5b5LcbkAqEdtMO4x2VSaOVAjJVk7zTnx7Ypjly+sFNIAXa3M1Lo7TvtfNfSkQQw==", "signatures": [{"sig": "MEUCIQD83LSZL+Le6S5wNihl8TPXNP/15V5G7qfzWFUXHuGU1AIgFNWkaDjTeUpLjg/LDqMSthxO39/J3QBJkVWHHfEihwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.4.2"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.1", "matchdep": "~0.1.2", "time-grunt": "~0.1.1", "grunt-shell": "~0.3.1", "http-server": "~0.5.5", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.7.2", "grunt-contrib-uglify": "~0.2.7", "grunt-contrib-connect": "~0.3.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.12.0", "clean-css": "2.0.x", "source-map": "0.1.x"}}, "1.7.0": {"name": "less", "version": "1.7.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.7.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.6.3.js"}, "dist": {"shasum": "6f1293bac1f402c932c2ce21ba7337f7c635ba84", "tarball": "https://registry.npmjs.org/less/-/less-1.7.0.tgz", "integrity": "sha512-q3gcDV/z2DnBZCALaveJgurAngH23DQ8Sy9OPFkkTtUxca6ShMIes0327AsXK23ZVjMs4A3cnaVTyBn/y+e9/g==", "signatures": [{"sig": "MEQCIA7peZKiVG0HMxa+nIMKe2wMlIVo0UIb2Oo++XBcNlZaAiBEfMYBb0oSgeQ5uInKpchCw1x3q+62EffY1IRkDWlH9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.33.0", "clean-css": "2.1.x", "source-map": "0.1.x"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "matchdep": "~0.3.0", "time-grunt": "~0.2.9", "grunt-shell": "~0.6.4", "http-server": "~0.6.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.3.0", "grunt-contrib-jshint": "~0.8.0", "grunt-contrib-uglify": "~0.3.2", "grunt-contrib-connect": "~0.6.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "1.2.x", "mkdirp": "~0.3.5", "request": ">=2.33.0", "clean-css": "2.1.x", "source-map": "0.1.x"}}, "1.7.1": {"name": "less", "version": "1.7.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.7.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.6.3.js"}, "dist": {"shasum": "6f5580029b52c3ac590e85e2b079827a66f6cc74", "tarball": "https://registry.npmjs.org/less/-/less-1.7.1.tgz", "integrity": "sha512-vNh1BX/bd61p1pfBHkV4eaC5fMNqIktb23nVMgilU/aku9zTQry5Nhzi2zOHToNuosZ6EJnBypsA/7PW2EQL2g==", "signatures": [{"sig": "MEUCIAkeu2/M01nFfI28wQQPgyuvQ7w2FNZXf6MmOxW3GRj/AiEA9FItfeBZmN7/PuXJ6MTg6bUCwEGf3kj6YI/J5u1JnFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.3.5", "request": "~2.34.0", "clean-css": "2.1.x", "source-map": "0.1.x", "graceful-fs": "~2.0.3"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "matchdep": "~0.3.0", "time-grunt": "~0.3.1", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.3.5", "request": "~2.34.0", "clean-css": "2.1.x", "source-map": "0.1.x", "graceful-fs": "~2.0.3"}}, "1.7.3": {"name": "less", "version": "1.7.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.7.3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.6.3.js"}, "dist": {"shasum": "89acc0e8adcdb1934aa21c7611d35b97b52652a6", "tarball": "https://registry.npmjs.org/less/-/less-1.7.3.tgz", "integrity": "sha512-kHskFSbe46tgXgpusTiUm1UT/9kK0lAzCAWDXNfEcfO2ow2Kkqv7g2VPnUczerp24H3KSazPOO8gg3VqBy1m0Q==", "signatures": [{"sig": "MEQCIDfcwAho84fXcqlQKF1UZJsrXqgVs+rE1Vtfq9oK3Yj8AiAcgHMsaYX/H5rvpNWE2WftbojZQ/+jGcSNzSUtsXQE9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.3.5", "request": "~2.34.0", "clean-css": "2.1.x", "source-map": "0.1.x", "graceful-fs": "~2.0.3"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "matchdep": "~0.3.0", "time-grunt": "~0.3.1", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.3.5", "request": "~2.34.0", "clean-css": "2.1.x", "source-map": "0.1.x", "graceful-fs": "~2.0.3"}}, "1.7.4": {"name": "less", "version": "1.7.4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.7.4", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.6.3.js"}, "dist": {"shasum": "1db03afd0d1b848d898d10d2690d79ee3834026d", "tarball": "https://registry.npmjs.org/less/-/less-1.7.4.tgz", "integrity": "sha512-yqg3LfXitKducFfYR/rPJZXhYWaWC7ezb/xeh9UK0VYZMJGuTS5NTPMcxReK+2Aw3YDNEFxU+3tOx9VPsYS5+A==", "signatures": [{"sig": "MEUCIQCv/Mo1+FFODIiu203ZKbWCmmG5Xb480WDM99Q3lJ4RdQIgavCN+8LiCXMDAMbBftGLjSWoLa1lEsZb9Ps6xRQMYPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.3.5", "request": "~2.34.0", "clean-css": "2.1.x", "source-map": "0.1.x", "graceful-fs": "~2.0.3"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "matchdep": "~0.3.0", "time-grunt": "~0.3.1", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.3.5", "request": "~2.34.0", "clean-css": "2.1.x", "source-map": "0.1.x", "graceful-fs": "~2.0.3"}}, "1.7.5": {"name": "less", "version": "1.7.5", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@1.7.5", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less-1.7.5.js"}, "dist": {"shasum": "4f220cf7288a27eaca739df6e4808a2d4c0d5756", "tarball": "https://registry.npmjs.org/less/-/less-1.7.5.tgz", "integrity": "sha512-+cddvg94fFlyJUijF+jO8Dvrr1RWIAf3l/kXDUiQ65za+o468iA9jwal2dcKnmJTHTFqnQQGo2jT1pJqAlI73Q==", "signatures": [{"sig": "MEQCIEafuKJzMCwN1g9pgBaoAFoMClz7zuZOlWJjmV/Age6wAiAl+6ESJwQlkUq6lPc5kwVBMeb/KwKX8JfRtyYZCZaEyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less/index", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "request": "~2.40.0", "clean-css": "2.2.x", "source-map": "0.1.x", "graceful-fs": "~3.0.2"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.2", "matchdep": "~0.3.0", "time-grunt": "~0.3.1", "grunt-shell": "~0.7.0", "http-server": "~0.6.1", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-concat": "~0.4.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "grunt-contrib-connect": "~0.7.0", "grunt-contrib-jasmine": "~0.5.2"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "request": "~2.40.0", "clean-css": "2.2.x", "source-map": "0.1.x", "graceful-fs": "~3.0.2"}}, "2.0.0-b1": {"name": "less", "version": "2.0.0-b1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.0.0-b1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "3dcb5815b2052c89435c00def2aeb58ddb321b6a", "tarball": "https://registry.npmjs.org/less/-/less-2.0.0-b1.tgz", "integrity": "sha512-QPKHCRrvQrDqzf9KvGY9MYER2/QequHWEc+euH39bHIblvZaILcRzJAhC2d01ESUfEMDEShvkiB7jKW4zXpZtA==", "signatures": [{"sig": "MEQCIBmqPS0X+E80ynE+3AmoV7WlTg8EKjGy6yDB+V6ev4+dAiB8lDSRobCdseVP32xQ6sMI3bfTlH1Wit/IqlrSqThsow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less-node/index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.45.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "matchdep": "~0.3.0", "time-grunt": "~1.0.0", "grunt-shell": "~1.1.1", "grunt-browserify": "~3.0.1", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.8.0"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.45.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}}, "2.0.0-b2": {"name": "less", "version": "2.0.0-b2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.0.0-b2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "0cc866e0bf0093556b34879aee8ff7fa2d8c1ad8", "tarball": "https://registry.npmjs.org/less/-/less-2.0.0-b2.tgz", "integrity": "sha512-UoH18VFYXPbtRpsK5H0XZSvqyKzt5BU/n2Xh9y4dDHaGDs6ncAauUF3M0x5Lw7KF9qRU4rUYXJ6VTOFvr130BQ==", "signatures": [{"sig": "MEUCIQCT5+DaU0GNnjtvWKLm/867doGkcHArQVLe4oNKpsSh1gIgB3HrwOzcCXiYEHkxXMe5IPWM4AL0jROUsU1tDmga56Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less-node/index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.46.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "matchdep": "~0.3.0", "time-grunt": "~1.0.0", "grunt-shell": "~1.1.1", "grunt-browserify": "~3.1.0", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.8.1"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.46.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}}, "2.0.0-b3": {"name": "less", "version": "2.0.0-b3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.0.0-b3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "61869a02b29a88bda7b99e239e7646fe301f4500", "tarball": "https://registry.npmjs.org/less/-/less-2.0.0-b3.tgz", "integrity": "sha512-QXvVGX4bAB9UhZf9gUVnOhUokXooDS4e5VAZRkNxSNbmgWkSXEeNEeS3oitYuGPEtSHonpMKKuCQmXDH+vL9VA==", "signatures": [{"sig": "MEUCIQDUZJKv8H/WjdXSgvTyth81/EAazYYues+T1u6szBVQpwIgamoOrtJpcSG5M62uuiOwTxcBkFVEilt3IViYcJZwoLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/less-node/index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.47.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "matchdep": "~0.3.0", "time-grunt": "~1.0.0", "grunt-shell": "~1.1.1", "grunt-browserify": "~3.1.0", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-contrib-connect": "~0.8.0", "grunt-contrib-jasmine": "~0.8.1"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.47.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}}, "2.0.0": {"name": "less", "version": "2.0.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.0.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "a94c0aee11868729644d92a5eed6ea6dc7bb9741", "tarball": "https://registry.npmjs.org/less/-/less-2.0.0.tgz", "integrity": "sha512-jT3SSFYAL4EdRfeW/jTPsUgpHoG0yFLjtIwD1WyDJJnOhKyo9AUCMerbhqi+tHRf0GuYxZ9FBlIaDXavlflLrg==", "signatures": [{"sig": "MEUCIH2LTQh3A8dUIpL0VBXvs2wM+tDkqmbooFw7Iii+bJMsAiEA1Px5F8gaS4yl3CWDFw2EaS5mDWnyGQGSC9bwjIZEosg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.47.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}, "devDependencies": {"diff": "~1.0", "grunt": "~0.4.5", "matchdep": "~0.3.0", "time-grunt": "~1.0.0", "grunt-shell": "~1.1.1", "grunt-saucelabs": "~8.3.2", "grunt-browserify": "~3.2.0", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "~0.5.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.6.0", "grunt-contrib-connect": "~0.9.0", "grunt-contrib-jasmine": "~0.8.1"}, "optionalDependencies": {"mime": "~1.2.11", "mkdirp": "~0.5.0", "promise": "~6.0.1", "request": "~2.47.0", "source-map": "0.1.x", "graceful-fs": "~3.0.4"}}, "2.1.0": {"name": "less", "version": "2.1.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.1.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "535b37390b21d904ee1c4c78d4c676b9f3c8fb7e", "tarball": "https://registry.npmjs.org/less/-/less-2.1.0.tgz", "integrity": "sha512-piT1mBh/I98aZMM/QhSDFFB71FEgBqN61YQ7H6ABfHeUPD4ia0vUDcuwEx2Ko8CSD3LAavWdvLdAw06NfzItCQ==", "signatures": [{"sig": "MEQCIGi0Q5b2GqRAADoiD8BNysimcHkTye70nzCf6AwlSeJHAiBGXZpzg171LC+WJvRiutQu4omQA5h0JF3C4utPwCZ3yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}}, "2.1.1": {"name": "less", "version": "2.1.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.1.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "7ba1fd7698e7b4eb46286c3954aed43e54da41b6", "tarball": "https://registry.npmjs.org/less/-/less-2.1.1.tgz", "integrity": "sha512-SuxVDgZ6+Vm6vPvoIPgj7u1lS5t+V3V+9yvPOKoTmsY2IdCKT9jt2ZhvaT32JvIAF4WgAQWagXpEBSo3aNyUog==", "signatures": [{"sig": "MEUCIBcUnKa0Oj+DnIV4H9jTlmY7IIGEnHcBxW3cs2DP+Q6IAiEA17pPf0fSkleEpGt2Treb1zBZqrvrrurrk6Yb+QtNMVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}}, "2.1.2": {"name": "less", "version": "2.1.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.1.2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "29b1ff230323f7a5dddf451b731a3d9460e8e908", "tarball": "https://registry.npmjs.org/less/-/less-2.1.2.tgz", "integrity": "sha512-o9ZZyX5zgZJ5pSI4zx2wbO3OGXYxgCNPuguL/TyxX5WR0vrekhmFqSX0HAm8VFjfCDdBVwuxrnTr/LO3kt6wpQ==", "signatures": [{"sig": "MEQCIG4WNRKAonyeLwHzu4lhecisQ3igsYX3P6mpMzhwpVH0AiBJCl9uuY2JfBX/JmuJOeNdSq8FcdZqGeeqe6m75fMnFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}}, "2.2.0": {"name": "less", "version": "2.2.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.2.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "86eec2cad0e4c2a979929292d15750394056a7af", "tarball": "https://registry.npmjs.org/less/-/less-2.2.0.tgz", "integrity": "sha512-A/wcgYrrE00BoJvVhjRn2P/cTlcjSH5rebItpizNNHo2bIvNsSu2gnsV4VExRXfRCzKYgIItUkgaOa4yHlHkMg==", "signatures": [{"sig": "MEYCIQCg3TVSqns0D1e3xTAOkQEvh5+Yjwxt1pCZP2YNXoUPQwIhAJC3KTD0QTMar4Ev61vXqHa4KxyDiiyiuNyEdDHlKjVZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "image-size": "~0.3.5", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.48.0", "image-size": "~0.3.5", "source-map": "^0.1.x", "graceful-fs": "^3.0.4"}}, "2.3.0": {"name": "less", "version": "2.3.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.3.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "b5054bd1bfd9e94acb800b6cd1107065979abb39", "tarball": "https://registry.npmjs.org/less/-/less-2.3.0.tgz", "integrity": "sha512-dAwj6WJzHTu13Q5WJs7IP0BpWh2dJVVqEqLMDTVTRMym4/nxnWADR2cj6BFt4t7tau4ZymSYboR3pVxKMD9n+A==", "signatures": [{"sig": "MEUCIDMo4FdZKwoNKn0QpRojZX9TmSfmln/Zno9A4lDY0gOkAiEAh4EURtOhfi1+fckEoyWdJWMZ7JattrNrDa0EaAufb10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.1.x", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "grunt-jscs": "^1.2.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.1.x", "graceful-fs": "^3.0.5"}}, "2.3.1": {"name": "less", "version": "2.3.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.3.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "c10ec082b2f2950f6e1c750e4da14bcc543ed845", "tarball": "https://registry.npmjs.org/less/-/less-2.3.1.tgz", "integrity": "sha512-wl8v3Pj6Ke4EmIUoUwRHxW+aPj0t+GsqJ1Hv+3R2q7DVBsMo5lV8qv4KoOiqUKXlpzxBvlDnJcKs7K7y0r5dqQ==", "signatures": [{"sig": "MEUCIQD+3AZsyJJNwBH79wsTA89os22bq+Le+/s7xHSQ9hHnKQIgTYrI/zusSHzjxKYQ8j3htD7jw6qmAokrIe/5ficBlB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Leaner CSS", "directories": {"test": "./test"}, "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "grunt-jscs": "^1.2.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.2.0", "graceful-fs": "^3.0.5"}}, "2.4.0": {"name": "less", "version": "2.4.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.4.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "ce51b38f1c05a0cdd47982fac40dd0a39cec2031", "tarball": "https://registry.npmjs.org/less/-/less-2.4.0.tgz", "integrity": "sha512-g/l/5w2Aw2d1SKd42ImHk/9hvcV3ADL0sb5RkOrDNMIxA5oGQC2SO8iIqispyHc4sV7SD1N7vJn3A+iLa05yWQ==", "signatures": [{"sig": "MEQCIF4ppbcTHyF9xlqfQtsKP64XdzrOwgdnbZMTHGuTCZ45AiAoFQQF0NvMGTf5AKxCGIm1v64/ofbz9ie69ufWzP44vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "ce51b38f1c05a0cdd47982fac40dd0a39cec2031", "engines": {"node": ">=0.10.0"}, "gitHead": "6fd2a5751cc8313481913bcb1623bf6c50089df8", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "2.5.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "0.10.25", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "matchdep": "^0.3.0", "grunt-jscs": "^1.2.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "^3.2.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.1"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.2.0", "graceful-fs": "^3.0.5"}}, "2.5.0": {"name": "less", "version": "2.5.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "less@2.5.0", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "11d6d611586de6d5f808220649bc9bbfe7ad5e17", "tarball": "https://registry.npmjs.org/less/-/less-2.5.0.tgz", "integrity": "sha512-dQOHAh55lvWtP4rCXoCzrOIDdVHi/Qemb8lQ5UFuOv+pnIBc58g21fAGq+399YV5I4mS/lL02SlymdLVCmmlhA==", "signatures": [{"sig": "MEUCIQC+hhayfGtHLyGt/HYO7fLNGx1UFzYnn5DjBVFerY4uTQIgRBNW698CjW/lj6sKxwPFvsNL1/kDCZYIGnYX4GIzqTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "11d6d611586de6d5f808220649bc9bbfe7ad5e17", "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "gitHead": "9b7021a309a9010b04ddc89d5fd86f1d2d0b3433", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/less/less.js/blob/master/LICENSE", "type": "Apache v2"}], "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "0.12.1", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "jit-grunt": "^0.9.1", "grunt-jscs": "^1.6.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "~3.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}}, "2.5.1": {"name": "less", "version": "2.5.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.5.1", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "8b489cc01d021e49360fd5ae03581896e722b726", "tarball": "https://registry.npmjs.org/less/-/less-2.5.1.tgz", "integrity": "sha512-RrSVIVLNpQEwliRYYURp/436vdBJPJe9xw3kVojGkalldAz8eno3gqMRD7kmnR0JJSKbsPHU51Hgdj3sadYjbg==", "signatures": [{"sig": "MEUCIQDERGHpe7RFNELSPMeon5LqIl6WLUn+cIXVsJQOmCu3JgIgPcEYFHhaZaa3O7HVWkAGPIsDSsJLFRO+pzUAnLkji7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "8b489cc01d021e49360fd5ae03581896e722b726", "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "gitHead": "59c012c16a71eca2fd9f354b8db8fffaa5e14d6d", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "0.12.2", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "jit-grunt": "^0.9.1", "grunt-jscs": "^1.6.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "~3.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}}, "2.5.2": {"name": "less", "version": "2.5.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.5.2", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "fddc11a748beaeda431e646c1e2da380ce0467a5", "tarball": "https://registry.npmjs.org/less/-/less-2.5.2.tgz", "integrity": "sha512-ftD+An7op9IeMEVSFQbuPF6uXb0PTZUvbTWNjP0eqLP0A7VxjgU9Ga1L0QOB2aWJsGN1xgsAFaPIGrv6UC88Kw==", "signatures": [{"sig": "MEUCIQCmam6zyda4Q5/DAmxEQUZWlHffSZSMQBwJ/kr4eX5liwIgaDQ/7oKnCojE/sea2XUdZmJ4dKOcneqZPZtEe79A3MY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "fddc11a748beaeda431e646c1e2da380ce0467a5", "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "gitHead": "f0c454bc68599a9ecd5fe7de843b3ca1ae7b28ea", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.1.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "0.12.6", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "jit-grunt": "^0.9.1", "grunt-jscs": "^1.6.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "~3.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}}, "2.5.3": {"name": "less", "version": "2.5.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.5.3", "maintainers": [{"name": "cloudhead", "email": "<EMAIL>"}, {"name": "agatronic", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "9ff586e8a703515fc18dc99c7bc498d2f3ad4849", "tarball": "https://registry.npmjs.org/less/-/less-2.5.3.tgz", "integrity": "sha512-aQAnfDvIzYC3NsAuvhOKnGJ5Yvu5b/CNvDVa1DY/KTHEuaK+Ca/piJadvnanFDXu9e1wHczy/LOtVLI0m5ccCA==", "signatures": [{"sig": "MEQCIHWu5jVzu/+LbYeKvSdTPNclhb/qE7oe3+IPLJeZxDbMAiB+HGdnaFEWrMKo4aXEuLMSX1LNkY1gGlQKmVHTmNSjlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "9ff586e8a703515fc18dc99c7bc498d2f3ad4849", "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "gitHead": "33fa72767e7474f3aedbbff83d8343217c08f58a", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "agatronic", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.3.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "0.12.2", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "jit-grunt": "^0.9.1", "grunt-jscs": "^1.6.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "~3.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}}, "2.6.0": {"name": "less", "version": "2.6.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.6.0", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "ba4cbf4ce5a208f266ef59eef2ec409ad1044966", "tarball": "https://registry.npmjs.org/less/-/less-2.6.0.tgz", "integrity": "sha512-L1SU80cRxLmCzg3fEo5hF6gNVFhXXRvPQwAMommvRZo3DRzXUhH8b+ANGxIn1s3qMmiM9n9VblaLJwCdL/gogw==", "signatures": [{"sig": "MEUCIAaw6xfBPJ4EjioKjRCsHFuDw4Mg7+0UXrtoY7MojQD2AiEA1M2uQ359ETn2/xixBxYoPabt27CPc8tANzLYlXkpMUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "ba4cbf4ce5a208f266ef59eef2ec409ad1044966", "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "gitHead": "633e499566a05c5cb667a2183eb1478d31e6e92c", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "5.4.1", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}, "devDependencies": {"diff": "^1.0", "grunt": "^0.4.5", "jit-grunt": "^0.9.1", "grunt-jscs": "^1.6.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "~3.5.0", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-concat": "^0.5.0", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.8.0", "grunt-contrib-connect": "^0.9.0", "grunt-contrib-jasmine": "^0.8.2"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^6.0.1", "request": "^2.51.0", "image-size": "~0.3.5", "source-map": "^0.4.2", "graceful-fs": "^3.0.5"}}, "2.6.1": {"name": "less", "version": "2.6.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.6.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "658e01ec9ac3149959c6b6dfbcfbc0a170afda7a", "tarball": "https://registry.npmjs.org/less/-/less-2.6.1.tgz", "integrity": "sha512-iumo0OjAUoM/KCZm0TDudGWneR9lkjS1e7yhEJSYK0KXBzaXxzd6bnhYLdcTlPekTOlgWnc6AoJuphbvW47SyA==", "signatures": [{"sig": "MEUCIQDslYN6uheS0kwB4ownuRKuziCb9tAhyjFxzD/baicFgwIgfGsqmoGyS77w0MOLB+ocjI/urgRSBvViTGQc75gnvPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "658e01ec9ac3149959c6b6dfbcfbc0a170afda7a", "browser": "./dist/less.js", "engines": {"node": ">=0.10.0"}, "gitHead": "b550b2081f0af3b8905d31a2a03f595ad7b299a8", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "5.4.1", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.51.0", "image-size": "~0.4.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "devDependencies": {"diff": "^2.2.1", "grunt": "^0.4.5", "jit-grunt": "^0.9.1", "grunt-jscs": "^2.7.0", "time-grunt": "^1.0.0", "grunt-shell": "^1.1.1", "grunt-saucelabs": "^8.3.2", "grunt-browserify": "~4.0.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.0", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-connect": "^0.11.2", "grunt-contrib-jasmine": "^1.0.0"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.51.0", "image-size": "~0.4.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-2.6.1.tgz_1457109575213_0.41725030448287725", "host": "packages-13-west.internal.npmjs.com"}}, "2.7.0": {"name": "less", "version": "2.7.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.7.0", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "0ebda1046f1c24efc696e52d353bd8c18ef07b10", "tarball": "https://registry.npmjs.org/less/-/less-2.7.0.tgz", "integrity": "sha512-pDNeWa3emRQrHXBw55qecnNX5tvg8LJk76gnEofz9kwlEoLmpg36lvU5ftSv/OBHnrKLStI6Dyef+hpHvl6f5w==", "signatures": [{"sig": "MEYCIQCxfjUxM02h10KyrwBnDTvCjjmrNTe1dX/SmF/z09c1PgIhAK6Z+wJt2q3CuSSYh4KEnXyG5szi52g09yfzyCGyaZSg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "0ebda1046f1c24efc696e52d353bd8c18ef07b10", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "b76db1799dd0e91a1042d6f2620d0f64f1758f73", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "6.1.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "^1.0.1", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^8.6.2", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-2.7.0.tgz_1462671322090_0.2874688785523176", "host": "packages-16-east.internal.npmjs.com"}}, "2.7.1": {"name": "less", "version": "2.7.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.7.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "6cbfea22b3b830304e9a5fb371d54fa480c9d7cf", "tarball": "https://registry.npmjs.org/less/-/less-2.7.1.tgz", "integrity": "sha512-C7dSI8hOBbZ7qmnpjMFjpwWRs6bVcrJsRmap7onnTy4N14ye6KtB7UY0ZqY1ejHf/3a2JxzVYAd+yulIRdT1nw==", "signatures": [{"sig": "MEYCIQCb9RVpvmmXHztex0z048gcn9tge5QQxbZ9Z4tLrRCe8wIhAKLeisY68gdGrb1Rl1IpLZ1ld0yFF9K94g4kWuqFZeb2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "6cbfea22b3b830304e9a5fb371d54fa480c9d7cf", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "d3e1531370a36dd6e213c8b4da955d0a0e1d6eb7", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "6.1.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "^1.0.1", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^8.6.2", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-2.7.1.tgz_1462826329455_0.6074283130001277", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0-pre.1": {"name": "less", "version": "3.0.0-pre.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-pre.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "a00679751f075f538ad80f024fd4e8c58d7963ae", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-pre.1.tgz", "integrity": "sha512-KzVpgevxZejjOGlWl8Epyw0wQuJ6Bz2sXpDROy+5myA4hYsQvR/qzMHm/D6qzkdtp5/yz+nenKnjbGzNWaqliw==", "signatures": [{"sig": "MEUCIQCQ5F5HBlrMLelsQalEqLLiJuohKSFhRFoMZW5YfeBCbQIgSz9FmqYX71MbydzU1e3/VwbUhm8TIKU3MnhyKCzedM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "a00679751f075f538ad80f024fd4e8c58d7963ae", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "b39b36f7009d1825fd3d851d9a7bfe38c99db91a", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "6.1.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^8.6.2", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-pre.1.tgz_1468436661311_0.9046596137341112", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0-pre.2": {"name": "less", "version": "3.0.0-pre.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-pre.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "fc03c44a5c13691dbd3adfe43f3540c083ffa335", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-pre.2.tgz", "integrity": "sha512-mxPvapOl/aE/onshMdqvpZ5cGQqHIb2F7jO9KjeS5OuTpBRT/S4y2V5v1Oh5yWBXhsfk5RNTrio1MvK49Z3YHw==", "signatures": [{"sig": "MEYCIQCbhLlf3os4aJzZXtkOjNUbnqvW/zIkb/q0OVY8H46EmQIhANbNPNeTGSBhjFAHPbrk4xVfFSuFb0Yu53AFcmTrPoJj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "fc03c44a5c13691dbd3adfe43f3540c083ffa335", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "1136a9c050ea31544109cf4370fc08b7ff52fb7b", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "6.1.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^8.6.2", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-pre.2.tgz_1468524573368_0.21090799942612648", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0-pre.3": {"name": "less", "version": "3.0.0-pre.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-pre.3", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "831b3761c5865db758785b6d516c0d3b7502cfc8", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-pre.3.tgz", "integrity": "sha512-Fh7b8mE+Ib15mOn2/UAoNL2D+45egZjwKyEBYvHfZDBUsYNwvceEA+R4yJXtDh5g2dkDDYFV9k5Jirm6sDZT7g==", "signatures": [{"sig": "MEUCIQCx9VzKrBIfNdUg/Vpul5o5v0VFux4A0aOQ4jfk/bQHvAIgEIEEv3H7Alizn6PAlh51Wuxnk3S7WjNDbR44yITQVAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "831b3761c5865db758785b6d516c0d3b7502cfc8", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "a38f8a1eb7beed589d2fa734fcf411cf4461d231", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "6.1.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "promise": "^7.1.1", "request": "^2.73.0", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-pre.3.tgz_1468877422831_0.4808636319357902", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0-pre.4": {"name": "less", "version": "3.0.0-pre.4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-pre.4", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "67507b12e6dc93746f73d231ae38e3c043f1e380", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-pre.4.tgz", "integrity": "sha512-GC7q4oxSAaKoYbdITVqTAceE0mpbp28drYpxKZ1C+e/p85qD/I+zjR3O19ZgUCuS3CV821nXEdRHAYAHpMb9UQ==", "signatures": [{"sig": "MEYCIQDK6erlKCBRZvATy3d7yIYIZVwJwvmi+6iEjf9awPi2yAIhAKdiR+jHmQaEkiEoXQ9ShNsdbXz5FUZ/5dOqT51lCbO9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "67507b12e6dc93746f73d231ae38e3c043f1e380", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "26041d9aa577688b27ed276dabf49fbf162a6429", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "6.9.1", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "promise": "^7.1.1", "request": "^2.73.0", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-pre.4.tgz_1477093231853_0.10495221707969904", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0-alpha.1": {"name": "less", "version": "3.0.0-alpha.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-alpha.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "9615c66ab6ab1eaad9075906c9d976ea173835b3", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-alpha.1.tgz", "integrity": "sha512-pquyLsQkF0w0A9rENvfpU/brxNkFQHWJ+3eagB90K5dJrUnldYqCGykHgpEqoJtjO/bjtq1eluLH5vyZ12PACw==", "signatures": [{"sig": "MEQCIEaFMKVCMatmDqK61Kg02fsyXovmVac0XK3Kj6W0IZx5AiBzWEiwY0rMb4nEVxUvaWhjvZmZ+3gZg+xteFOjNivYrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "9615c66ab6ab1eaad9075906c9d976ea173835b3", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "c60c3225624da08cfa6937651a3160ef0ced5ba1", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "4.4.4", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^3.2.0", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "request": "^2.73.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-alpha.1.tgz_1483304575042_0.1714104707352817", "host": "packages-12-west.internal.npmjs.com"}}, "2.7.2": {"name": "less", "version": "2.7.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.7.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "368d6cc73e1fb03981183280918743c5dcf9b3df", "tarball": "https://registry.npmjs.org/less/-/less-2.7.2.tgz", "integrity": "sha512-rMNVkQrv/ySmHJdSoXfmnVIJFJbkd77MQ7UzflrT6qynF1E7/gmQ+L020WNgStW2bnGYL7+zpu10ZEHRrz22hg==", "signatures": [{"sig": "MEYCIQD7znkRiiYJGqcBTLQHhpgLE2okFoqLDylEXW0KLXhm/AIhANr4tZ5ET7cIv4mF4guzA0VALyOfSAgeYZmTM9+oEawA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "368d6cc73e1fb03981183280918743c5dcf9b3df", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "ceb54053e7964342e6d9be44e24dad701d818098", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "4.4.4", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.72.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^8.6.2", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.72.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-2.7.2.tgz_1483581489990_0.6281389200594276", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0-alpha.2": {"name": "less", "version": "3.0.0-alpha.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-alpha.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "f9fae6de065e0c76cc47b325de29726e695ad680", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-alpha.2.tgz", "integrity": "sha512-xS+QTUk6UJ4nP8N82rYV+j1Cvo2hAi8R5S+RdGmNvLuMSakN6ao4pckdg6P1WQtfvOpXEfZ87u6a41HmhCHfbQ==", "signatures": [{"sig": "MEYCIQCK7NJfZ/APYbyXABk12uCysngzisFsDmgPIUMVC1NnpgIhALP6/vleRzHGRBz6K8024IDuoREFy+EwcI0JeUey5bcw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "_shasum": "f9fae6de065e0c76cc47b325de29726e695ad680", "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "ff5760e0994fd1ecc45bf50c242eaece9f109603", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "4.4.4", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^3.2.0", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "request": "^2.73.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-alpha.2.tgz_1484099404507_0.8402867745608091", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0-alpha.3": {"name": "less", "version": "3.0.0-alpha.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-alpha.3", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "d3da9234fd16e60a47d6291e772e65a841ec8410", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-alpha.3.tgz", "integrity": "sha512-CtLgi5FCi2bhVsea0rAH9dap/ORMZYeHBjFUrKVj53RytQqdMsTRrmBZm8dWZ+XJPNBoqOs46QEIoJoFxrp3gA==", "signatures": [{"sig": "MEUCIQDCFdPmecSgUesGZnFbq4V1lfilJzcO0IGQzqMIWvuGqAIgLcFnE16hDZu9koqG4038sg/L3II3rca3fVrGIwk1ZwM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "488e0fcbdff2b7dc369837ee3baeaea18d855164", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-alpha.3.tgz_1507512894703_0.5878921463154256", "host": "s3://npm-registry-packages"}}, "2.7.3": {"name": "less", "version": "2.7.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@2.7.3", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "cc1260f51c900a9ec0d91fb6998139e02507b63b", "tarball": "https://registry.npmjs.org/less/-/less-2.7.3.tgz", "integrity": "sha512-KPdIJKWcEAb02TuJtaLrhue0krtRLoRoo7x6BNJIBelO00t/CCdJQUnHW5V34OnHMWzIktSalJxRO+FvytQlCQ==", "signatures": [{"sig": "MEUCIHLFgTyH+BO9GTyAGrjroCBjO52d9DtPWHoB/RSPW8tXAiEA010/GFNafHzqrB0oDZ1ToCE5H+zuTEBkTTV9w5dTp3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "60a5c3bd1f6807615d017a5019031da47e1f480d", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^2.2.2", "grunt": "~0.4.5", "jit-grunt": "^0.10.0", "grunt-jscs": "^2.8.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-saucelabs": "^8.6.2", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-jshint": "^1.0.0", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3"}, "optionalDependencies": {"mime": "^1.2.11", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-2.7.3.tgz_1508813020373_0.544631906086579", "host": "s3://npm-registry-packages"}}, "3.0.0-alpha.4": {"name": "less", "version": "3.0.0-alpha.4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-alpha.4", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "8b14efb1c1d21efa2253abeb2047c778d8bd8842", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-alpha.4.tgz", "integrity": "sha512-ggOYMz2yAFUuDl68/GEhbTvrCkIH60HwUVbwqDYKk599nyHpdKO8svGzXbuXEI8v+iGuRLvJ8X//c8G6kHd8yQ==", "signatures": [{"sig": "MEUCIQDYzzjBgHiBoH0C9rUSzjyX4epb1TOvhoAFNDVCklml0AIgfcjk0k2gdgtTOw3VewiQ4zHqy9bkdWzFIzpbgGbHFp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=0.12"}, "gitHead": "691f0f1aca76fa2c527fec6a34ebbb2556a3626e", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-alpha.4.tgz_1508814564754_0.5816749583464116", "host": "s3://npm-registry-packages"}}, "3.0.0-RC.1": {"name": "less", "version": "3.0.0-RC.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-RC.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "0f25d4f8e7cad8e940169e1ceae03ecbd0e19b7e", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-RC.1.tgz", "integrity": "sha512-S48XmVxyNm5h3AXFmxuW4QFrmAvg0wquuJgMLmfx3ay2EnKvxUc5tIuRYG2z+plMvARbVaSuWaOYgCosG9Cd3w==", "signatures": [{"sig": "MEUCICnBUOLVMeL4rCbps88u846ghIb0fxe8rd7Vw6Ap6u+3AiEAjae8eJhVuCciDkr/uzADVZ26p1zlAen5ypqUNojb11w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "8b4524f644734c5c57c00ea0ce484bf49c886346", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less-3.0.0-RC.1.tgz_1517768119215_0.5253662359900773", "host": "s3://npm-registry-packages"}}, "3.0.0-RC.2": {"name": "less", "version": "3.0.0-RC.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0-RC.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "926c4ca1094f4366b97e425824df5d18bec42aa3", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0-RC.2.tgz", "fileCount": 900, "integrity": "sha512-7vTWRh35axT3+rt2YtMB5X1rk8gLcHdL1+pxZ8Lnm9jnCU3IKWtOGAteNTL7nPZ1UWB9KnEn+2yqpKsqf2RO8w==", "signatures": [{"sig": "MEQCICUk4+kIeFBVaGtASA+l/5CBLXxZmowlH+k1YkcSAS5QAiBG2sTjlDPeqJDvEhDIPjcYJFqKxVrlZyqZTlanktZgBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2134044}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "a48c24c4dd3c13e00a20ece80323768496a96b36", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.0.0-RC.2_1518311216602_0.7590387543009658", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "less", "version": "3.0.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.0", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "d942ff9fe9f2c53b8b4e6b5c5f1ac434681f7aa5", "tarball": "https://registry.npmjs.org/less/-/less-3.0.0.tgz", "fileCount": 900, "integrity": "sha512-9H0u5xo49oycfj4iSqoPt3ypHCO01bMEbOhChaNPUeeYazEZb0PVYYmp0JXQF8ZS4aU2Bkb7+aMPxELit65/DQ==", "signatures": [{"sig": "MEUCIQDskcFC/jILsFAvfcXdiO/frzdsSvUCCZNSDAa/WmQfWgIgO1F+fIwThfhX760q50nqprGIwLRn1p9Os/zbu1dQiyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2134110}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "4962988db3c2b6ebc5c90da2ba08386b7d9a46e7", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.0.0_1518327758939_0.26933493741291037", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "less", "version": "3.0.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "ba2fea24a5632ccb8c84230d6043c0bf91855e37", "tarball": "https://registry.npmjs.org/less/-/less-3.0.1.tgz", "fileCount": 903, "integrity": "sha512-qUR4uNv88/c0mpnGOULgMLRXXSD6X0tYo4cVrokzsvn68+nuj8rskInCSe2eLAVYWGD/oAlq8P7J/FeZ/euKiw==", "signatures": [{"sig": "MEQCIBhohJF+UM6HMOkutYUtGsjhSjB4gm7uO5wPktxzcrfdAiAcl9b+1EJvY6yGHVjaGUFzMlRZg6AYgXU+jmiM3RKWbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2135944}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "4272871e078f9961561bbe9c1a866c982c48237b", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.5.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "2.81.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.0.1_1518655237796_0.7513382874344328", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "less", "version": "3.0.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "1bcb9813bb6090c884ac142f02c633bd42931844", "tarball": "https://registry.npmjs.org/less/-/less-3.0.2.tgz", "fileCount": 900, "integrity": "sha512-konnFwWXpUQwzuwyN3Zfw/2Ziah2BKzqTfGoHBZjJdQWCmR+yrjmIG3QLwnlXNFWz27QetOmhGNSbHgGRdqhYQ==", "signatures": [{"sig": "MEYCIQC+NstcM2wpjHT/5sgSRwJV8V1qmnib4eewDmA2oZoJRAIhAMvig3DWrSV95oSJUlOiNaCvR/0uoKB1/kFhKMZ7NxJp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1724112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa24IpCRA9TVsSAnZWagAALDAP/RGJ0f5JAkdtOkpyZqXV\nTP4MCVZtNWKCFPvoTPwoN6Gs+dwwn2Q00baaU9fN/ui9dIcR1YX+MMloodZp\nlhfcbUntytepPCGX/AjWKDaJtaptA/NyARqKfiDFVUcQF24yzs5KMbP4G+E1\ndb68o2NUc7GrMVOLa2bPbOGKkMyHC8rSYY/Z1F/YBg+LdnBA3zV0lhW5dgk3\nIyE0ZCXde0oISfZqEMk00TTqjW5jozJOqYiCSdYhLUGVvnfOrMms5NZndJly\n6u+XOXj67IEPM+nu8pN5COMberQ2CyCmfF1kSagH5uGaafSDphkp3VoyN3w3\nF8SnYmY2oV+l+eJtBVMArpklKGmHlRPJ12Md2Y/wIVFIJGSE4qKURR4HCr0/\nBZ21gnh2LuhZ9FsjzuJ8hTiPzQfHot87VN3R2q5THS3msBzI8pmOGnB6/ggK\n9EVLVWOLnqA6l6ZzcVobagUEhMCHT6xgNt9tvTHD7PlfNcWgIlwn7WCo20dx\nliU34K4NM8hw6NMJpS0ieflTBb86cajzxAVJzW3M/lzRtalAYmyPKdZ7aHLM\nTM0xrMiLRQ+2zWPBJ7vSb2dkhUVfZCoxit3g08iq8JwzQMbsUic2u+jvxAWK\nulIU3/GvwID/wRW4zqZPSc/7VJ26qVeRI1bIvznCAN7p7IOrzZSHr2j61Go4\n/xDV\r\n=cO8R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "b873737f5aa19c7b48f88743cafcf5aa47ac0ed2", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "^0.5.3", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.0.2_1524335144286_0.1462443646228988", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "less", "version": "3.0.4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.0.4", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "d27dcedbac96031c9e7b76f1da1e4b7d83760814", "tarball": "https://registry.npmjs.org/less/-/less-3.0.4.tgz", "fileCount": 900, "integrity": "sha512-q3SyEnPKbk9zh4l36PGeW2fgynKu+FpbhiUNx/yaiBUQ3V0CbACCgb9FzYWcRgI2DJlP6eI4jc8XPrCTi55YcQ==", "signatures": [{"sig": "MEQCIBsyQetQ9eff9JgP5W2+k64C0zGXw9F2LofETGpKElp/AiANFU/xj6Z4CndMgDx1+gyUmk5dSYkzKUjqPQbIO/AnpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2138166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa77GGCRA9TVsSAnZWagAAW+AP/1WCNZZJDupyTtHTGiBD\n0At8XSRg3vGkpCqHsdh2evrNML+FWVKt/HF/xA/6QZAE+mhZP4uzh3Zw3fz0\no7IoTc9Ml3XG6VWok228J8Se5jq0rMT2oeWmXJyfGYI/XTmgrtPrjMxznoRT\nXToflYQ11MsbAmRtvhHVaP2e8rqzAqdTiJ4SxLhiQFMNDGxstAf+nlH//OmJ\n1wq2T2n+unQPUOwGjqpvR9jKiFhHXLb3NEh+ZdK9P1dA9OAoaPv+bOmwu5Ti\nju2tATI8i1iXhRdNN76RZIPiIubScijak1VPwpxg5p/e3+YayUc+1uNgUsG6\n/iP4KpJfc+XmSrigCZ4z4BPw9DqkdWUcfmoWl3qhcKnX0Ts8q9bo9xSghGA2\nV5fa1IrZDudcofqaP2h0YTYa6n+bs91NCZ0noMnOS8pp/FQJAEKsU4BPCUoJ\nzBcrDQTKwWZlTSCtBpaDaq9lvYbdtNJugYtCVyhXPrYB5Hq+h82d3tfY9IIF\ntrDnaFQAw6/hMEmcJ1K7z/iTtKrf8QheDN3FWHtK8n94uJLyW+EnZzKkI5RO\n/VHxRf43oiEDdFx2/sCWHJ4is7b5W/v6sz2Tzl0xMPrsu9pPIqinPcgS6Nml\nvCL6SAnn9aElTfcJzWZo1rb23v/yvPgWGrfeSZCuptPxqVRBXACcL44fBNtW\nWCq8\r\n=+GCE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "e41a17b3ed4c09e3865a7e5e5580afeabb5f22cb", "scripts": {"test": "grunt test"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.0.4_1525657988914_0.13207110471112737", "host": "s3://npm-registry-packages"}}, "3.5.0-beta": {"name": "less", "version": "3.5.0-beta", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "2ab3c82573474f54b2b3abf368cec606b53fa187", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.tgz", "fileCount": 919, "integrity": "sha512-1WLgGZ8lHzGZmm7KT0JXebnAS7ZBP8f9wrOksU5doNXwWpJX3IiGYWKKUloH5yXwae6qd8BxDZa55nK/uUNhIw==", "signatures": [{"sig": "MEUCIHpwqTUX2Lr6STnvOYpILvUlAVL49gG3xUwmmQDyc1u4AiEAoeXDq93bSV6PYsuiyBEKoQyQdsCh8bg8TAgo03ZoM6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2168159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMF6wCRA9TVsSAnZWagAAv94QAKH9nLBG/ip6xdbry6D8\nYRk2RoxpI4mS6Qg3XLkXr0lSzO/i+v3heCyGZzZ8KfFIIy36Syf1AMh/HPV7\nJrD0arJEHM4LDFxZH04cM6mDFgjggpMsmgk8ecBX2DpIKm9hL1nl2yRVapIG\n1GNwhjT4Rj4g0v8ElhgunpF+rhvI/YXtLd70vMIeC4S2Q4jhC1+c0Czn71tu\nBZ5qBsf/mkUcTwVa6ZP63ZMERSx7KDGXHE9n3/4IlBEZZpIoTzpzpjHWWy10\n7aT8HlwQYjSigaII8RVHuJRBty6VcTNK4AhZ+gdfKRWJyMZrlGY9ADOZdXNn\nz8lBouSbqBNWPoYmTsXgFGMf4sb3BWM2spzKL8CdzGhNNoqKV23rHDix07FC\n9Mrn7tF+GyYgAGcA48gXY+y006EHdf6mSlyqhfFm+sAs3/jNIdbNo95bKoxD\njYUA0ohNucOI1geulk9Q9C6CRQEKydSGPG+3G1pn07NmtB0zgrNuJOvEpFq9\n/3RHSG991VW9cy0nWu6wTqXwPLXVjAbmAJ6fCE6xRLa0nRfrWcI4IZPuf06V\nxRQ1gGXqU+5JfQpcvLKhzCq3zLsYue3p2nDRmWnptwbWtYBMCk8hP60mxfjy\nZXd+32G6a0ePTCCDX+6y0GVsM1EJTJRHPEsziqZTrQRg5ybIsf4CGIUHT0O9\nbY2Y\r\n=JIol\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "42fd7dca20c011336bf616020654a1fd575cde5b", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta_1529896624422_0.21108653856425974", "host": "s3://npm-registry-packages"}}, "3.5.0-beta.2": {"name": "less", "version": "3.5.0-beta.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "21aa7939ffe9807e4af6e6dd2283fb72c50552bf", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.2.tgz", "fileCount": 928, "integrity": "sha512-4/DGURGCoqi477ppMLLQrMQnJUAUKdLiBjf2s2qPkHxHr7Le00R8baNDfqenmxyaZSNuGhlR1votJ2yGwYHVbw==", "signatures": [{"sig": "MEUCIQCbL1Skfnv2E4wWZbFySc4LtuIcMdL6YAuqgxmMmUfeRAIgIeo5D9LlBIuv9LQfFvmQXOnSb9BGWk+3wNWttahw02w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2191131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbM684CRA9TVsSAnZWagAAvzYP/1fVRVsraBRsT8hjjugY\ntcAboNBqy3qr5tmxZ7sZqU96RQlKVJ7flckvDxlm4ysqDbvwEwmo1AKd4QeQ\nPhC3dLFKQuCYR+FhskP1K3IpFuDt/6NcsGOZJ/U2MVeFZmWsWDf9RhBpjOdb\ndtXIqdd7oUkyHm3j++Y9c5GpPVLDJiaHjMybyIYOxeONBH9c/UvxVYQLBjx1\nFg04T9d48M+ZJX/ZL/vtBzPc2M/tZtTBieWKaZtcvGUBVAo+rj0S53gcgMR3\nRJArds0GcUfp1QMQMepotzICoOpWPr5dgSlbMqSrChP0nR3iu4d5vLVGJQQr\nQQGmOLG9z9ZMppCjVyAhSEgvMXg1dOvMeci9BkqnieVVh5YmcFy10EQjcmfv\n/wvyVq6dn0ZtvjgqfM7BzcRxdOFWs1aajcfUaecE3ararCsowEF8ag9r2Cuw\nzkjkwIF/tJyQD96o7+bc7BAwFkfC4zBK4m/fb/iXr+YArKe0ORMKlpDneycg\nwHkjNfQozrCBmtLO4qGZuKy3pcUbCYOdJFw78OaIXquHe34PsGPLNmqJKX2M\nqRFQU8OO8yqASXYfPUCjs71GfbUMYwx7+9avJTftSHerhVNCQhKbZcccc7mm\neRnDccTxOErh2eq0ejKWAk8dkokT0bVyw/fAzk0U9pEVQ+7YXV/W8oEWVKJl\n82pZ\r\n=CFYD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "b968e66f5f5bda234307476987abcd6010b18696", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta.2_1530113848865_0.4415451113432569", "host": "s3://npm-registry-packages"}}, "3.5.0-beta.3": {"name": "less", "version": "3.5.0-beta.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta.3", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "b4bbc565682a9e68dbf4fae5687901079f069335", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.3.tgz", "fileCount": 931, "integrity": "sha512-Ce16Uvg9esBALgNq3xVBMQNVtpPTG/NSwgvVAe7sS3J9OCctPl0Jb6GON8hZfLMbc75hEk7JTU1XV9Nn1vb0Rg==", "signatures": [{"sig": "MEUCIQC2PKeRKRgf4P8OYwOkUyW/bsno0iaIwZ4sW8iaSXgjqgIgX86V6mak5NBVGw43EY1yHknSPCZmPe1Gu1IJnJDOlsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2198691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNzRgCRA9TVsSAnZWagAAShMP/1l5EQEC532990jhhfkx\n+6SjxteDlj5AKrOW0WDawdzmXdyQ5yEseQnZY/jnKqcTw/WVIFSoNidy7fcj\nDW+sEpm9AsdQf0WzoNmCEEBAvG6JIj+FrwxmpsfrhWTe0P8jU/ofxwuuUcRQ\nq1KgQZl6QFW1O+Rk1BonMkcbyTqP8Up0mTiVVx6QpPeGoBNuov5rpPcRH64S\nIfHMM7bVNrqL+/sl6QWdWylpqVQbCpRIgvKJi9ObEaIcOxXrOdWIbi3T/mJn\nqCx3QGGL2HuuWc0sl1qLVhLO10lWM/20VfM2iCiise0JkHOl4Nd71CwXw4r3\nmVCgTPodVsog5+iHXdDykO0dmnxJOCAiMrDabGNNHLasXi8DL4WOUt/Ff2Rv\nNXmPyL0jHCp2MlnaqT35/EPNLF/aSBy0UEtie4ZlebiAWPBgA9VSGgN1D8s6\nShFApTtzBozmGb4hju5zEu2jApivRvuRTmaG+EuEcmo41kxIlz3FlZiB92TK\n970VR/A1QSFYmCDHqp6SDPvdJrB3U5uxZg07tv7+8J/x+pr7H9Ma3bjC4OOr\nka861FYdr63+/v705LbDIZ9PTidF0MZyUbkQO08XMn9VWuM6gPHF8Zbf7OyU\n9fXCDORPpbz/S3X8tBh9kLKMQ7wfCpuIuzkLbkdo2AtWZv0/GgYtE8KKgoh3\nuh1X\r\n=fWiH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "b94f041c65401630229fa0b6a480dca78f89e48d", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta.3_1530344544207_0.8359483293443228", "host": "s3://npm-registry-packages"}}, "3.5.0-beta.4": {"name": "less", "version": "3.5.0-beta.4", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta.4", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "0b821a02041b7b2aef1d51f65f2b778e4d1ebaa3", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.4.tgz", "fileCount": 955, "integrity": "sha512-VQRyWHvP18YrN+cHaV7YJmpGVLPFW51s03j40zj8sHJXotiuY5uxLQEuVIPMEvS8A0h+ASqowrZB8l6hSPJVRg==", "signatures": [{"sig": "MEQCIFiw4xb26RFm0/5DG5PdCFXIGbKuOLje2QJoHiBYmleTAiBrUnkXI5mJ4hJv77ztuJUSa6J2febCx8jPYcXV1K7myg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2224543, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN7IeCRA9TVsSAnZWagAAzgUQAJcOj4dgesA2QStmBX6q\nACR8ZrYXMC1QROwzTpzw3BlG0YeOZjt/aBmE83DA47nHcpEE3t8K3UaG/XYp\n/E1i2IaXQzkUTzcoDIcgi+StFtJfSGSYKN+/0CVP6MW9i/moqlVn2EFEyb3C\nHSJkAo8CddQSWRWiAWuzP16TQs/y11qEwT9SdtooJhnpOhoH+D1HOCa9Qt3R\nH23b+1DK/k/JJ3pNAAYINp+FjzU2jm7qMmNcKnh0fN/m/1dPKco6QO60jAj8\nzC094mhY458lbfKSwlsTAua1N7U0+XHjlqrrou4jrVtQs8Dj9hpssa9z1gFT\n8NmsgCzdPY2DS2o+yUIAi1q2f9ZjmEJ+PFAB+aL9W/l489HqmvXi2uRHTJnH\neSaw9JN//o+/C93X8N2CZdSnpPg8XNd99AUW5uJ9MyGTC7j3PLublxV7DNI0\n3ne2QJzt06dNy2+isTL1TuuCUsVPLcu2BaTW/C6+pHqv8buSlAIV53ddhKgW\nvMQaN2R+k5OCngNZoDUJvP/ui+xzWCKRmykly5nvN2IJRbzjX7kOMsnvnXOL\n8ngGRQ3oQ4dDFhr0+uzpkj0QVj6TQJpwn1+c3V8/gfdaQ0uExzVzeW8bBntD\n1pXtpVqtQ2f3i5Q45JAjH1PXmirGWhuS0bJeCu3+IINNWzsDurMKu334xAcb\nugG5\r\n=FbFs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "f11f8b077ce2ae0ff1e250d6b3101ac6d8178055", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.7", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta.4_1530376733946_0.8432385540243785", "host": "s3://npm-registry-packages"}}, "3.5.0-beta.5": {"name": "less", "version": "3.5.0-beta.5", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta.5", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "7f6a45bcd56e440f6d02664e4d4c763c3f5e4aee", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.5.tgz", "fileCount": 973, "integrity": "sha512-gPA42UHuIGuJeyXJ2IKatqG8rbuEty+UdEoO20/2fvgfp/wEJmUKHmNWBxVm+wxPDMZfQAcJECKiU1MSCC1itw==", "signatures": [{"sig": "MEUCIQCc65XkI9v83nbU1SJE4UA6ORyg+SUgZIR5vcA/i6vNsQIgfcrwOu/I+i24xLwiiF3sB7AIIkEiLT8PUckz+hzsSP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2240169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbOZatCRA9TVsSAnZWagAAZ4IP/j+sL/houejk/wbvmC66\n0edRNypPQUbDlUL3uHQaEQkk0Nv1CUlu5NUje3ifB0hoD+QKFAV1T4lnnfxJ\nqWZDUR+HESSY0J4TWh9DBhA2LpyIhX7F7WYnEnYq2mF1WI/M+KdVGtx7lTv+\nV043sJEfN5jzvfebbi3hxoHfXLXwOozj4yZ3UGcH5rhGEspwv4pa3APM/iO8\nXzx2yK8wPkqqh/piae+pSjIDgFBbvNDC25CAPZwx4RIMMGMDe2hbFOAE91eQ\nMZbTSkqoZ2EkItWxWopmw0QQhqxBKXRREbPjYP57GNS5YQ5mGwbMH30xdTym\nDzGD8Pb/Ud3l8NPZKPTD4bqcHLWzIQ6lToCDT7glxe+lnt86SI9IIBWTFCea\n3zFSMJHghE/dkB5ZCMgYKSltFNK+Nui5wxZWq9Y26ePptmpBIeDDDhp3pM4x\nErpLwmqOnXLJnkD+tY7/+lXlZCKnlB+P1Y609NmcIatOy7zejZ0mqtbp97Ib\nXaI/GoFo7+IWELVaB7nw5JEC3Wqi9/P9cGyRnn++8GYbO9JDGHdanR1aKdS3\nBcTP2d2VrbOcN/zXT3K+mR730KTq9C2Eo1yBsHqDOaWddKYqVVjoXnOWI7Su\nDPl4vjvRTLYZQTgI5gHhDKWnLwq4LDdjJ/LaJVJ6kfXPyB8MzVkAj1gEPS9P\nF8L2\r\n=9vEI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "29468bffcd8a9f2f58b1a0f46c131129b7dfaace", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta.5_1530500781183_0.02010831732484064", "host": "s3://npm-registry-packages"}}, "3.5.0-beta.6": {"name": "less", "version": "3.5.0-beta.6", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta.6", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "e4802eee4e211d1ff54cef76027951c3b80f4e37", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.6.tgz", "fileCount": 978, "integrity": "sha512-cWuGB3NUnHB3O3XvEOm1xff+cYXkbAjrvrv0cpd5NATQ4kDW1X4vAECy2tPgysIw9YuO/y8aOv+2222baV0cCA==", "signatures": [{"sig": "MEUCIQCIv/q1xvOfFNftX7ADle4h6XUeZuomArd6OLl1uMsXDQIgPVTLGbJmrYQehFrREJcKAUtHVDg1kXmHKXvNqJ6qAZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1808798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbOu0YCRA9TVsSAnZWagAAuKUP/A3XnTj6z0nXcKYcDxMV\nIEDwyq9NBaY/XrOdbYhW3YdL5zUK5CgH6guUgfpgfJ0JQTXEXYX+JmhYbtIP\nKJCdxP53z84qC2c7Vs9A/T8zHlMY9WWp5bwkz1wXUStGpBGJkM7nwOsDitq4\nmhwl9J9YNOuTTzoXMVx2KyEdp77Q8lLApLR3VM+KJgtF6Xx1EjfhYs7OYltd\nSIJXM6VJtPxOnxiTr213nX/W7DCO8vW82eR4c+kJmIpEdtx4IYPBLMI7KO7V\n/7unMe0iZiuXeAcaNE8kw55BK0Mve+n7ccDCjXrzHmZd0tA2O7bGC2N1kP2Q\noE10oUHh+RJ0Z+25fVEjHiy4Bq9cKQ9zkXi9BC0i3qjRpY9ksCFY9i+D2swR\nWo/HtP1z5q6QqKsdXbfRKinzAlc2g+957SUwVE+AMWTVR8eo8t1wZ86PpCP0\n35sMHqr4x3sxyva1fEzeSQVamhU9wCr4R/VthUvlb2+nEp1JGCgpT8I96IRu\nB38woB2I/Qcy/u7OtzhDg3ZGs+ZkFLY6oJmjM1I4jgdczYNdiO5Xk/i1LCf9\n0F+kvMZZNvebGJExyUxk/XINYa8FQSD8mR/1aO/TtC2j+RliyGY1OXmuEpvX\nPWbXiVr/aNoLjq9CjPnAYJcUw51i3M9HrwP7CbIYAcKfqrweKUPcLhyNQUWO\n3vHZ\r\n=o1S5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "d821a3754fdc5d8b338877329e74778f6457677a", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta.6_1530588440012_0.31560051683289636", "host": "s3://npm-registry-packages"}}, "3.5.0-beta.7": {"name": "less", "version": "3.5.0-beta.7", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0-beta.7", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "1886d9266713c452f4f8e71aa23bb1c77beeef3a", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0-beta.7.tgz", "fileCount": 984, "integrity": "sha512-vVN6DRe/o7g7ET2ekaLp6s6bLqX+pHgqE/12GRYttD/zWuUuiOmfiNHSYso+dYArDPL4XO0wRiaU6lDQPtoxAQ==", "signatures": [{"sig": "MEQCIH4wraNWx4oPXClEknLLFQ/+ri91H/Clkiba5BgmO2DCAiBi0fuPeqoYQAqmjBPfgfOXqXy31TERK99EQfsec3r8dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPHTkCRA9TVsSAnZWagAABAAP/0GrtRHT3vY8jDKcxew0\n/wMvJXhQfp56QFrnnKypc6FnRCw2BS3R19lUDZieJTZYgb0Kf+MzGh4PQCgE\nFMl8OFOCGYpxnEGqW5TOBa5A9euxp/xWQ/0k4AeJrNAkn0/TJi4XmEQesNFW\nDz1TvEe6Fo+0yHbvn2vbxpIJnQCIhRkhPm5+BkD/XV4yz4cK0yCuJyzH2zDg\nzw1ef4T/HvO7XqPAVncFa91WZDDFQ7WVZQvEXYtcP4NNEWXzcjHtchj5yN2A\n4jIgrGzu1DU6mCqQ0Co6x7mpYgkXK5wXCi93/jtWVzUsqvgy6sO21f45dyLW\nwJW8YDYY7ETxL7soc+dEEs890cIi905ywZQZTADM2G6zR8o+hC9Hi8HaeHpH\nOn4+VgKbwULPv2HGPP0dnfb39QvLMSp/3OnAM0oBHaujBrtIJZxZlzuut26A\np8nWBgRmNcuz2z3ChpgXofpRhD6/IvlOFb5aGIbtPyENdtFkrCHMTbUq6BUA\nsH+phXxX/fME1nPN6LzIPcK8s83Gu8WxsBUhLi9K2PDYns7bZ09hcDVkQDbY\n1YAhjUDfrGKBZtk9HYBTQvMMaxjdDvz1DgPJ1prvC0+tIRdbbiCTEnR9FsVC\nWz6XlvAHxv+lTpG2Dtv99rB3xZFuig5mQ8WSz0tLfVd5gpU/BO9AkCarM6Xi\nJOtK\r\n=B3er\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "b56ed27f0d1fe486bd9240501775f61b8a2a85f6", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0-beta.7_1530688740125_0.14971037732791048", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "less", "version": "3.5.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.0", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "86fba7899d00d2e35a78c9f911e177d368d85cae", "tarball": "https://registry.npmjs.org/less/-/less-3.5.0.tgz", "fileCount": 984, "integrity": "sha512-fLbJGxtoCV2FTDGeXlUXXUZ8sgMej2c5u8om8inOZt5M3tzXqF/aS7W0txWhCN6iA6qPtPGGzj0sp/vd8ztl6Q==", "signatures": [{"sig": "MEUCIA88bOgz4hpCXXSvTS4Kr7pZuRnVxu0WavCwxarbIOFsAiEA7J68REUiam9PJ1JDBr0ohXDYzewJHOtaObXIyvgMDa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2248179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPiyrCRA9TVsSAnZWagAAQMUP/03wmemx8JkfoYTU0u+s\npbDjVGB5wBuwhPkZmHD1bQlYyAGw5silDKeUujBI2L++R14ONoxFyGpcXeEa\nzO0X3Dn/GCGGfg/0AEZJnI7Hgg1yjqvhTeBM4iW7FE+PuErdSMCd5ohgOMmj\naya9S5+6m0Cp9koEZZupwMsXjFwGgvZWzGWmsVamspdmwD+QwAtPurPTazKp\nfmohnGk9KCQe47EQm5jOy9Tx7JuURYdUJ8iqZp5qapskOtKooKTM1TKqnUdp\nYLg7Jld/9dMYmNxPH7jfFq55bgQOr1qdrCfX2VIDjrceWQsa5tkrQ4btzJQm\nnbNa0typo8dLDhARhXyqTvKN4jUfzToG7IHJ3t0qrcfrQEyM/K8esPCfA4ax\nG5mynUYqyJqKipJMzwryA1r6Ux8dkiXZGlbqTv3eiyBkpXGhdzmIAtecVrPV\n1DD1RwA8uyMkufVS4lkTFZNYol67su811p0jDt/IAdbt6Y4c+gbWtumCKHhX\nTV7ydC1jSHXE54kW7GGyBsDquLIz22qMCcAkJGQVR8Y/XJXbr6wf0HrDThYl\n4Cu4LFMEqqSqhAc7GvQ/HXJqHCZ1KnFRRc2e1Idr+rfpHi1ahF8HY+ikEhzc\nHwvEsM2PtDGUT/4733pbTu8dMnUu0OpFxpHsFFm12IE5PttFsONDSnaEGM6x\nTQPH\r\n=Q1T+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "21ea0547ecf85edb349834010acf51aae3a852c2", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.0_1530801322824_0.34111388266365994", "host": "s3://npm-registry-packages"}}, "3.5.1": {"name": "less", "version": "3.5.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "3158ea5ef6cfcd6c6c3e2f584454a24033402a7d", "tarball": "https://registry.npmjs.org/less/-/less-3.5.1.tgz", "fileCount": 984, "integrity": "sha512-QkAcQ8rrIUn6LQajUsroVP3H6WWp7gmUXdCZi5HdSzv0VyGp815epPB5spJOf+sNYDyynbs45+hAmekdkJ+jTA==", "signatures": [{"sig": "MEYCIQD/f5bDBQqDzbuQxZOqSacQHQH4mN4VyBEeICJr6FrZ4QIhAK22fATulcT6CbdeEcvKU1xQZsbH5vGqx0c6Ne9Y8/ac", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPmsgCRA9TVsSAnZWagAAVBgP/RlsiKBIwpb966KqGN0h\nj5hETyp07KIrAXCooeDXn/LpXCkResWZkS5mkETluVAdwS9vWciKBGWoonvf\nBYDBWmcxLF38yu7kjsI2O0Aohb489qBh0nDhqoFHsAPHdar3GWXoMfJezzwz\nV1LvcwctXmsNL/SSocjpq1NHk+BmZAHBsm9kgjHBbPDULLZjRUSP2hn9grh3\npaKPKz8F2VaveVbY0AC7eTPDksQ9jKXm8TNmq44lLnzklwLenWC+yTFrDJIa\nZG5gpdsv4RUpdBLDP/+kb3h8Yc77Sb6AdjSM0tovyNDtuT/7VMu+T1RM94yt\nVKWS3otCDQWkoQrlqCmD/KJbgKxFxYF2xAA1BXsiEUTexvQBxLrnU/neiZKo\njgU/b7pP68pMC1IIuWMuJ0P87N4knzDQ/PpmH2o/c5icrQaxyLv8n/8/Dru+\nkndLpI3GZoc4fC1fo3r7WeVoHIhPgg6m9PQP37wZSoOiC6byax83aRrrOBXI\nglrYq5Zb/wrbgVPx4YrTg53LCfuLscYDSxXRR24uca01bZRq00Dnr/7aUO9Y\n4clNX9FJggR3/kL2K+IzKHC7f3hlKCILAdimZp1XOYbs6MsICArlvDGlaLzK\nuyM8MGuEogISOpXuIVOY+mgVYgd6EbH4QFdPcOsbqXd3hoTMpKubgP7hV0sS\neIwr\r\n=G4bz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "1bba66b52bdee09a75e215faac7c7ecf49341d0b", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.5.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.1_1530817312515_0.3010938420010463", "host": "s3://npm-registry-packages"}}, "3.5.2": {"name": "less", "version": "3.5.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.2", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "bf33feaaf75e0f6448c5672ce024d33e18d54e85", "tarball": "https://registry.npmjs.org/less/-/less-3.5.2.tgz", "fileCount": 984, "integrity": "sha512-KCFAmfjIxcmsf7JJuxki4XEJDRg2d08a1VnJa3CTKYjIyd90wcZmPc7NTQPgfSCvGKT3t5KW68HVOrQ8numg3A==", "signatures": [{"sig": "MEUCIFXIOgrmRWG+LpZftmIe2Lj9tyyDJ+4dXhyuSKGs/+5vAiEA06zO8B4/W5MLRGKLBc4VMo98R7xmN8ZHM9r3xBNg3TA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPtruCRA9TVsSAnZWagAAfmoP/28cWVwzVvCe1neJXcdn\nIafHjihk759VdTTDZNJgE2WZnkhejcRIxUZWyQVyw1TNWcGaV/PIkEPfqCJS\nW/yefuoXXReFn49+iAFaSLxAWo5yFyN000Qu2It1xdVN2oXw4PtBv6IExERe\nc0qpamDZWM6R8tMC6mQYMI4y7PmaD2MTYeSpOxLuad6BHp+jGXH3fHfZGQIy\nNODX+L6OTfGQikODXlQ55kW32voqZkQE7heMOcoXicAZ/g60ibRjdp+w/B+f\nKaVvvS6Cg9dRt8GHDunPZTx2ZNlIWk23xCVdGeqry5qnub32dIVR3rPasuJX\nRjN016BqyC0T222Pwwf+V7eIHa7MYBh43RWUmQQ/HOThqQo1j7yNGFiK67q9\nGKVGJ/ZrBhjs6LwYM+jAnC2Vdq4q9+jWqJrmJu5BqQUSHkWFe2ylexiZa0OI\nCSCWLI+OFMpfFI8IKQoM+lrklFiIRNk956JYoDJPCtkj5PaBeMeJJtGvccgo\nSacKO+dqS4gpKUFzA5JryYVTiAMgysgedcMt0LUZYfRAaCEX87Z5Pb2yrBNS\nwV84aYbycNAmtpurBMKugdTeM4CS9gwYtUkZz675IyvH2dpLrOVzjGMS2GBN\nd4pYQGC+zrXUz3gIK2w0/RIxsn1pfUkLUn6TNvP88vdSeXqwMfHEwgLHa8NW\nJQho\r\n=8EXA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "c20e8ce51a1b792069b81a8478e7b442162f5831", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.2_1530845934690_0.9874940802427912", "host": "s3://npm-registry-packages"}}, "3.5.3": {"name": "less", "version": "3.5.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.5.3", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "108f656b5ee09a281b757a603a80aa091acf6384", "tarball": "https://registry.npmjs.org/less/-/less-3.5.3.tgz", "fileCount": 984, "integrity": "sha512-MMGXfVYMORHyfwWGOP04HNnDxX874A329FMZEiMuho+QvOXJMyjlsmnf3qUxr1KmwM1E8mVo2bOPbT4A8aF61w==", "signatures": [{"sig": "MEUCIQCY7QGBNujlksZNXWGS8mRVbJ94weRT5d+F+Gvoq0ILNwIgU7lzf+lWwOdcC5wvvsycRCEHLcjxjcH5A+l4s/Hk9hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbP5EuCRA9TVsSAnZWagAATLwP/1t3aTkF2oHdZJj9Ks/j\nSAcJ9pCXs9hIlW3QEk8LMgZ7C8apzufe3ie7XU84VqLREpTks72FzMBXafQB\nPq6ZtKG6wckG2fEo3CzGGZHmF/8kZ5cvKpD7BygnTrIV6z74PFvat/QwVlI4\nSzzr0fKv4el3EEVvAfHf4qqplLj1hxWN9lgmTWr05XIMHBIrcQL1p1nFUuuQ\n7/+ZCK1dHHkLJbxp5/GUB2/XNyQcX8u3XTm+rme13oEdiVjC+u26R1cp3IJk\nAFVG55htMKi9b+4DWWOw4g6QHhHCORXJ74lLGgBpVYOCz103WgX/GepzlltW\nj86E8m2aZz4+VtNfsAoyupOn6xleQ2D2JlqTYjxLkOa9ZfZY2QwGPBhVHYX1\nOZeUcelcsJ2hLJDQn0Zvu+Cm2p4FFydvrj6Cgngws+7D7KyyZnxbywUzms8r\nAVJl6T8GZNYyX4e72h4Bm13lc69O4NYI6Rz/WU8wJBIVC2kWW+iKiB/k2q0d\nhQB5UroEIWCQ/jwdbelDsZwnklqT870/y5OUzrD0UfQvYVhYMHTuHSTVHjeV\n+7a215lT5xkJb3UudW0ymV4ZiNNak9eyDiooLzayOiUzVcqoybkoec6p4hZA\n7Nkj3LmpcUa4piFHs/pPXc/bb/poph1HqsXdGkDM3aykNPQlZXfA8aEjKJD8\nWKK3\r\n=MpOa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "91c2ddb2ffbaebdf799f2be8132b1aa2cf6badd5", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.0.3", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.5.3_1530892589897_0.9230916356257355", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "less", "version": "3.6.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.6.0", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "6a079e954256a3bebf33ab00d25fa39409005701", "tarball": "https://registry.npmjs.org/less/-/less-3.6.0.tgz", "fileCount": 993, "integrity": "sha512-9jCc6kgJ36E2EjZrx+V+UXUTOb4JgFO5l7y9VetoRUtoaDIS4+yJ0XML9Fdr006zE9ZgHIk7tdO+SMa0PLY0mQ==", "signatures": [{"sig": "MEUCIQDBbzXyDfRJZseY2F7K0jWAxQJJWy3DNfMOzVLq8uOXCwIgLbQFQwxWW+/ggwEJGaT+vZg0QMJzfCsyHL2CpvAvJRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRMEqCRA9TVsSAnZWagAAUgQP/i93YqLDcdnr5ciq+AdG\nJf4a/VEilDGlf6ddJDqvvsRDQcn9Q+HpoPKehIwcuZPY7GypxqUemionrmzn\ndEiUtHHm3WIVOEddMAmWqlSTIdelZvnRttPWjS1TON2xpsfJpjisSWZlAY4I\nlHpxbgEQldF3gUChBmSA4Qa8Zs4dsUXY/tsVuf5rQ9FMNqQrMltwrwpbRbvc\nVGJIO/xbS7fqTlfRSS4JlY3FWK2gV9ivcqHoAe3owh0jrI6UgdKbMTSqQvJv\nxNoxJoa8S1Q1kOyNjxbsQE8UdKo6/RtLyGRqHlleQcMcrwn907DtW0Zrl16x\nNAnk5nqkZL+P9Qo3mCF+FwwC3Zc2+/ePtxhnRDTUZHjDB2gCH5AGCfAhd/Gj\n9Avqdc+7ihz5HKnd5+I6+yRRhS52pReb857ssWeDwyQrlXVDcLhLzu3Xx0r8\nl9ISHtZitJH6CS+/b7/5lSILck8uX1VhLyTvAsmz7TNeMSsH+YfYgskO5Xin\nhw15kDtCxsDs0lRPMRADmmuLoOTY61ODKPL+op95intcD+Y4vAtnTO1V/hBa\nYRQoutMNDcGW8kLFTa2vZ5vz5wPklSmHSirHFYWv78Q7cv/Dgw67l5bnhrEf\nMMb08mhmtCKuIwLCDRFitTecmcW35KDZMVCCw2JFKABAopttjm51mKX3h3Mz\nGCWe\r\n=hhU3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "36ec7b85a675286d65d3b09c2a8d50b042b807ca", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "uikit": "2.27.4", "git-rev": "^0.2.1", "promise": "^7.1.1", "bootstrap": "3.3.7", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.6.0_1531232554685_0.8307881901963441", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "less", "version": "3.7.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.7.0", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "dc0429b4595a14a4cf7e7efd33f7378fe3edb614", "tarball": "https://registry.npmjs.org/less/-/less-3.7.0.tgz", "fileCount": 1029, "integrity": "sha512-U<PERSON><PERSON><PERSON>UjkUjKmieLDb7o7GiMdD7xtBte2Ix0wGcvuVcHjm8z59CWvczoiUxRF5D1l3PqO9kYlvVHMx6eOLnQWTjQ==", "signatures": [{"sig": "MEYCIQC0rD1593ReOjF3Lue4byt6fooJbUSV9gNV9yPui3C+9QIhANX3xhvSQgL8DXbsjx3dqJakAckonFdV+DQ65SrKR+tD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2759572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRaNECRA9TVsSAnZWagAA6ysQAIVvE0zXmg8GhDqBN2el\nRQk5ohdRaoJPcZSkbvJCqu3tFJwaDtWVJ8SBrcj2vdlgdABLah9nWCHCBEaF\n0hCr3Kf1vzo2z/t/vIKHV6rY05E9Pssdg1B67XJRSvm47+HNqRJJaZvdGK2S\nQI2OgMTpuVheRJQz+ZpiV7+BDWuXuS73Zt34xZZibI14rFhXuWu/dibvIGSF\njMAlB3xEMZnWkP/lBXji4x++x9DmfV1BpxiYBH2jptxgRtBARd//TjGB5tGp\nYJyQkPCYmdrIS852iTujXtSN8ktbnpcEFTUys1GBJUTH4yyte81a6nUABVfC\n+5ysmbxROa+b0xfCIr+GrkmHXA90eCTeseCWpmkBQWDGncOMtWrmTTyg79+T\nsnXxCOHHzhEkzyO7lUqDKfRtKqn/AOWheLgdbZAEnDRgVbGLopDevHj6kO2Q\njIienhwZ2EG7XYrQ40hH7j5/P3ixR/vzik1uQas1OAr9dnroLoJ4MA+5MW3y\nx7/xax8kGlHZAZmg2ToNnnZv82iquQ2h9rvdryTjXrNKa9gncM4DiOOU+xb2\nZu9XpcDYhOMMj323Qie/KzN7jjOSeDds4AmGEBfzsj42z0BQENmgXXa9YDk1\nBRt+muhrqSCItiiOUu+pF/rQ+oeFenHthY3KZHZfJRP9XEWW4t7TMvJpi6jU\nZQdP\r\n=qsN9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "3b5dfbbb8d19b60f50901d90d1e13976bfa90431", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "uikit": "2.27.4", "git-rev": "^0.2.1", "promise": "^7.1.1", "bootstrap": "3.3.7", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.7.0_1531290436116_0.08841739397873294", "host": "s3://npm-registry-packages"}}, "3.7.1": {"name": "less", "version": "3.7.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.7.1", "maintainers": [{"name": "agatronic", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "192e9dcef456ba3181a4e8d78a200f72a75e5c30", "tarball": "https://registry.npmjs.org/less/-/less-3.7.1.tgz", "fileCount": 1032, "integrity": "sha512-Cmf5XJlzNklkBC8eAa+Ef16AHUBAkApHNAw3x9Vmn84h2BvGrri5Id7kf6H1n6SN74Fc0WdHIRPlFMxsl0eJkA==", "signatures": [{"sig": "MEQCIDSOY2QtaFjCJLPrAddxgcAaxNYr05vz4vooqC5EJvh4AiBSjyBH6gy1/DK8k8DJDf0bpkfu8Y8gfbSMhG5EMzbsDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRoVbCRA9TVsSAnZWagAAOtwP/2c6eTst6Ss4Q3+GME/J\n0hbdBefw5gjdBB1BcSikrezYcR9npoQ58r1SPK6950an8TljYvHUlBvgEGPz\nPrP4DFAR55bXYSCmVCpDTDm6y+JNp7tC+uAZbs+9fWIw265ECx/eGFTU9/+J\n1mTzM+qKsq8HO9hdIhL4E485z7IARb1drJ4flzUbSwDY2KB0/2xYOqtDO5YY\nc4iR6oiCq0sVuNz+LwmPDwERXyqsdnp3fn44sWTRzJQj0ga/TFTc9tpFlr+F\nZCy/28YaxfAGoE9VTgc3Lj+uVTVa66bn1xbB6Kj1QBCOWeVgusIdKIT6Qlnn\nH+m3UI/dKiKiPoU2wMj9caA9SdTIGIMvnT8mhfAECpNlSw10WOtAbteWFUIH\nfqKGlE9MO6j2AxYnLIPVuYrYuc5bcCMuxeh1CCEYgm4RyWVGzQccUWlP9Cff\njqpWq0MkmrKLGV52JEtGvLqkpVpuY2HniHS+71gU0X9KRykPMORwWVLf4QuO\n8Bwj5y0wZEXi+MNCLAbmOZr039lmCH1FfHTeP4J80r9MtR/QD6Iknat2QH6i\nc9UDVhqSGvkK0PS1LViIefKkJF7cOPDHhCZYYQi0hc/TneJzHj7fcY0Ofuz4\n+ThO2op5Mb9ftP/7qBMHX6KNLHwEFtfV8pxm4uI/H8QnlBukd+UJrRXXNeP2\nCb1Q\r\n=Ih6X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "c9772a0f0549dcff88c0fb2687ff60a04b7d2d65", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.5.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "uikit": "2.27.4", "git-rev": "^0.2.1", "promise": "^7.1.1", "bootstrap": "3.3.7", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.7.1_1531348315355_0.02260317338704443", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "less", "version": "3.8.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.8.0", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "44785e40c23841c15ba3be741d36bd2775dd0596", "tarball": "https://registry.npmjs.org/less/-/less-3.8.0.tgz", "fileCount": 1056, "integrity": "sha512-746DPDyL+Wsjo7h/Z3t+A3Mg/mpDTaxW4puZyLhCQJjWJJvHggN735orjuCLIYgo7jKqv1zWLiQrxkuUOg5oGA==", "signatures": [{"sig": "MEUCIQDpcjpgy7EADrQ4hpz1n8kRDOcCThojRdFm8B2v23ddzAIgdsIW7FcsEWPSlFlfuBaUjxKj1jhKuJilPpqpqjot3Rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2783510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVXw3CRA9TVsSAnZWagAALAgQAJlsn33Or8o8NyBRvh7Q\nYe4mSKMKuDM7O8njfRiEJ/w4ETWsnbF9ZxCJT5dJN7oQtrYzqHRIklyryygT\n24XdVzNDS3jl0t294Lfd36R/8l8vrwVG8gYlH8ih0IIP5C51gWQL5y08G3Ng\ngzB5HMJwxMNWMcRrZhh9SdZMviGKqVNwZZcsacU7xPPB4Y3+vJlVIPqe+SFJ\ngmgTokLfN1udHnax9cmo0oRwUsoGv9gkojOfh8ixRkOyYNN8lDaJkY30adTb\noNvhjgQiCUzkvOlLOduJoHf6OwCkYzlAjFYaboV/lr0TDYWv9Z2lLSZnKFbK\nY9+Y43pKx/4hqCemW9xSlHmagTH+VCMytbzNJrTjmgUMaeEMsLk/tk66WiuX\nQt26RfIBY9JsZqXs1gI2wsGHk32wKHaPtUiZPw7RbYivGrgAoRyema3jNqdG\nTTMccrS7iAc0OAso8FDIA9yV0caIZWnvci5Ao7iOdQx+P11yYpvjWd5qeq95\n0MC7JaS4o95359L/uozwz2BAKIHvKXWrXJgi9leu+gR+jfR3fBaZc9fo7hCp\nYPocAyjlTbc1RPAT6AO4W6RfqvzvE6U9rMDC2UX7tivMqhe14H3w5xdRU1Kg\nwZSVVixeZuUlKxDPc8Eg/XOM7A6dwu6FjfMpXovrviSpnpglNCB+stxy0cId\nX2yq\r\n=MX3k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "59e919b3fc968a403405e39cf15237936b1a6b46", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "uikit": "2.27.4", "git-rev": "^0.2.1", "promise": "^7.1.1", "bootstrap": "3.3.7", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.8.0_1532329015823_0.35875916577753886", "host": "s3://npm-registry-packages"}}, "3.8.1": {"name": "less", "version": "3.8.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.8.1", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "f31758598ef5a1930dd4caefa9e4340641e71e1d", "tarball": "https://registry.npmjs.org/less/-/less-3.8.1.tgz", "fileCount": 1051, "integrity": "sha512-8HFGuWmL3FhQR0aH89escFNBQH/nEiYPP2ltDFdQw2chE28Yx2E3lhAIq9Y2saYwLSwa699s4dBVEfCY8Drf7Q==", "signatures": [{"sig": "MEUCIECMhTJaqacPNuo7cMF7gNx/rKglwNMwCCfMwj9fzdAYAiEA0zODg4nlR45G6SB6TIGSKwR7Pi2+YYbebe7yqvLsdPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2933984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbamfuCRA9TVsSAnZWagAAapoP/jKFH0FblrthIRFlH5A/\nP5e7KVGxqCHKbYcs1+hTS/f4GJKARTYvjOl3I87iPpgLC1JiapAJUfddFhyZ\nm82JMdA/Rt2fEVhieiJC3JNxCnDakntuxlhHt0roWFqJLUPrhpkGUr6SZpsW\nW6ntHkff8mT832qIXs7tKmzf44XzR8qZZlBY3O6IirB+8KJVKENiC4ztxyPV\nEgN4CC6cRjI/hJV4EvD8H90v80TA+cqjpuUdC1na4tHOMnaQnwZZz3YEA72L\nUUcoRqvTpZOuES4laRaeHEMZ9Hpgz4vCjn+FsRDmak2w6DhoNq187mK1j070\n7yopaohaI80H7JBpVnDz/VlQHt9rzZI5pBlxaSdwA9zkHwWtNSATZuYiwMfl\nV3pniBHainUYJbPG8K0//LgdAe9Zgjrlidy/a7/yvU5BReckHLYjXtc5v/9f\ngfDtK4bGlcWLCqP1Oo6AuYDUbMHBjWOqy481vECRYysr9lXZzpPwrv0pKspt\nGFjaY38ty8HvUcb3o4ony4E72YzdgjdZ0gtlQC3suXWHioP28lmA/6jL0Dqw\nKACvqUpc+0T8ytvT6fWCCsIEwY/GFxo4ASeKESbRWWgtQyNHZUdrKFZmQ4DG\nWZAuuUk+jyG54A2RwKSA9+bBUXUbwZA1PJIJ/qvkWjhtaPZMATucAsiehgU8\n/Li2\r\n=YYRA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "52304860c7933de3f219325cbc8ebc112850e4be", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "uikit": "2.27.4", "git-rev": "^0.2.1", "promise": "^7.1.1", "bootstrap": "3.3.7", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.8.1_1533700078620_0.45030875084988575", "host": "s3://npm-registry-packages"}}, "3.9.0": {"name": "less", "version": "3.9.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.9.0", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "jam": {"main": "./dist/less.js"}, "dist": {"shasum": "b7511c43f37cf57dc87dffd9883ec121289b1474", "tarball": "https://registry.npmjs.org/less/-/less-3.9.0.tgz", "fileCount": 1048, "integrity": "sha512-31CmtPEZraNUtuUREYjSqRkeETFdyEHSEPAGq4erDlUXtda7pzNmctdljdIagSb589d/qXGWiiP31R5JVf+v0w==", "signatures": [{"sig": "MEQCIBkZmw1i7NHUNTPYjnOxVB81ZS9b4R0jUWfIvfoBGSXcAiAMfjG9PsRtW+P8bZA5BRAKXesn0cLrzlha+g+QuV7trw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2669559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/4KzCRA9TVsSAnZWagAAs44P/R1y+iOSr0wHL2lQoEGr\n/5aYZFnEEbLRBO4r7mfKzKVB5qss3cspR3d58dYFdUWYoATG4JPe40Pk5ZjI\nIe5BC505jKWTCS0PJ9CkJIcH8OL8U7VUfEOePu6MrYDl9LhOJMFSvwt2/sgy\nafpgDQfRXptNFvJa8ykeqe6TsgqaVbtrelj4/AXerUX4oUsNIIaf8GSbRtmk\nA40y4fdqE/xho1XP0GOAzYNna22vYk2MOpAHn4UUNrvIOszX6QIEJFJ2M2by\ns17TFpCK1hjEqGS4dIkC0BgGijikdmRMr/IOp3jZdd9kC10jj41rabW1Qw72\nzpFYx++JZEHMB1JfDw0w8jjJVPwA72ZwybxgWs5d5loCGH8ae9AhScS4ZzUh\n78X0unXOEPTUUjvoxtZbhCvybvqnJMwWe22duMspGcnqcN9Z9VCePAVrcc+Z\nlvJJ4O62LHHAgyGNbuTN0lLJyF7QPV4xb+i4BnLot2hfKCfXbb5Hcnortg53\nEe4Kxve+NMFUZdac7cC6J1ZopMaQ2NVJ18FusK/qTDSwMcRzdUMeuxgf+88O\nXWbSXqpUEnWX73GREEGZA/0NVVl9m0OpTyECTMf2683i/3tl1O60NniweZyW\n3AfB5gwcJ29MIfLFgwHY7dLAiJzv0gu2H8WnNR/AtD5UwIWTxeskDuIdXn2I\n6pV/\r\n=uktl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "browser": "./dist/less.js", "engines": {"node": ">=4"}, "gitHead": "200169471231550d26e895594ac694ac5dc1a366", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "8.4.0", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "~0.4.5", "uikit": "2.27.4", "git-rev": "^0.2.1", "promise": "^7.1.1", "grunt-cli": "^1.2.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.3.0", "grunt-shell": "^1.3.0", "grunt-eslint": "^19.0.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "grunt-browserify": "^5.0.0", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^1.0.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.9.0_1543471794655_0.9449090389427963", "host": "s3://npm-registry-packages"}}, "3.10.0-beta": {"name": "less", "version": "3.10.0-beta", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.10.0-beta", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "61f40684a4de93ca6ac4688ba3f003383690f062", "tarball": "https://registry.npmjs.org/less/-/less-3.10.0-beta.tgz", "fileCount": 1062, "integrity": "sha512-RtKJIQ8Xxn89JKzUtPGnA9CdE3kPSHkjvX5CWNSrx3IfR9YFqCZ9cEOJzSuXPAIofo4/ufnkcC3xfYtVCUSI8A==", "signatures": [{"sig": "MEYCIQC49ayOkJN3pGGI+/nuRSCGnjwUXBLm9g6AdAQM0fpnuQIhAJmcF9WFoHnruV9x26OEG1us/SK3Torgzi5HSoQ63joi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3669807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRcydCRA9TVsSAnZWagAAeCoQAJ0zpLDceFWmUSkjacPx\nxHuJZUBrws7oq7Ta18EbtVKbdYVXXdM+bopM8eYU23UNrRK93X61wj48Pt8h\nLHmGKde0cvuHgsyvxDH44LB2IaoqvhcZbMxWa5p9Ys2Nh/3XzKab3M+e5p2L\numVCstFtszsIOIo8sErsAcvfePBEMtG177myHxlQj5d2g844PiIlljL/zBdB\n06CTzYXjtXbYcRQftJrpwpEtpNJ5HrCMzdm1+0E0LVTK/1XYEL3xWTPTVps2\nKMQG+Ny8rrxkVw68BF47/U3DUdaUkfu+Fw/AbETJdFcRB+f8cQHLi2W9vHU7\nByEc86veVGSbmXXECLHn3cw2priiXpJvAkX43539UsM2XijXzgsH1LilBAvf\nMuL+LhZvYvW6H9MD6taLZ6gfb7QY7eB7Dwr8g9o1zWiydwSQherED6ABdeGM\nO7HL9EtkSRJrppkOI/Vus29kB6ZZcQ7SgKZklSfiUvIyGbJKdKg0B3F/nJj1\nBkwYZw5d7bwvlZLF5ebe/Oh49ZQESTSxZNC8Y12I90eAdlSg3UxEddFGRDZP\nlI/NhWjCeoyURWLBY9FQa7dFmgAPIXC7UOLJzSHIR8RtaNZsd6RQMB4z7j7C\nq17Zj7w/IFpnoLW50FDrRGGZIpgxjCyOoRjfO367rw0IpX59TkkpOBCfpvg6\n/BMs\r\n=bJaf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "f077e2116877585336d138718566184af2afca59", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "12.6.0", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "^1.0.4", "uikit": "2.27.4", "rollup": "^1.17.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "@babel/cli": "^7.5.5", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "@babel/preset-env": "^7.5.5", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.10.0-beta_1564855452254_0.8433328877698438", "host": "s3://npm-registry-packages"}}, "3.10.0-beta.2": {"name": "less", "version": "3.10.0-beta.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.10.0-beta.2", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "6fc369097a2d8c018200d967b33f1f463a8f71e3", "tarball": "https://registry.npmjs.org/less/-/less-3.10.0-beta.2.tgz", "fileCount": 589, "integrity": "sha512-cTaPFTRO+c1pXtegrDkmdGQs9owJoQRfosXptArMGNfpPqMdzo6D5bk5m7pnzzI1zQwsNxYWtVHJuPjZ+9F7pw==", "signatures": [{"sig": "MEUCIQCsnZANqwRp/T6nNfdJxqzi1ZoBK/JwdpIxwsGg8ZnfEwIgIjIQRHnGGBpGHuVdRAEP5Qz47ue60pXPcjdyQbqDlE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2970555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdS6XLCRA9TVsSAnZWagAAGZEP/25zkVADbCKe6wrMw1hg\ncjiIzFfML0JLX5LcGe4kNdq39jIx4npj9Fkq38iu1x2hPcRszZJCT3ZczcKF\npvnVI5MC5+0cd61rEMncB9YMTX3yZ6RV+ZDXpr3ZSgj18BBryOhZ08bq/2qd\nc++qJqoOLvnE3yT2HlZlru0LZoy8gUC/xz5kk8JY8cTcX3YjVrB2VjFRu5Sm\nU3dRdl7jEb7DI9IvIUqw1Owuc/PynD6jkvDxEOezJtlm/q9CHwiBtosWc/MK\nrqkJV4Zvz9tEr7CoWEEeim/0CU8HedTAJL2whZYewb7XDUNbhe92Z3zRY6oQ\nRh018fj0LKUhW1g4UvnGKhIfgPlweYHUZ6CZCTLxgVHnMPES5v4ySm8LoAx0\nUhr/o4BxsYdgxshbF1Cd16latxviYCJHzopsHSYBO8tVJ4OR4m3isqDmmJcE\nf3WU6cbKoYz0jU1wo7Uz4iKWDtbPruDS6alvhvBRfLQHtRkBGsVuDw0yFExY\nBijckvbpE3t2Jswcv1P0zt3Jr5FQRut6srm4iny7qAwU9qD1PKPbJkmILjaM\nmdi5yLWHkezCqR0SGhAnVtXPYPvNqid9HGFnOqzKF6wPQ6ASq//HdJf071q5\n7jAIy1+7HW3ToHBm5aflh5A5TGTIsV14ObQgdQMBuria0FC6mNpSX20WdSam\n4n/q\r\n=Tf4b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "c0f5e976967db742b3ccb064cd6db00466cbfc40", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "12.6.0", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "^1.0.4", "uikit": "2.27.4", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "@babel/cli": "^7.5.5", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "@babel/preset-env": "^7.5.5", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.10.0-beta.2_1565238730373_0.23872111156958975", "host": "s3://npm-registry-packages"}}, "3.10.0": {"name": "less", "version": "3.10.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.10.0", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "1150f727651908516fd4b47133a8469b29b6c5ce", "tarball": "https://registry.npmjs.org/less/-/less-3.10.0.tgz", "fileCount": 591, "integrity": "sha512-+QCSP4vXVCXVqYSzqdT4eLoEK/hSFxFfD29E71+cRFKdLsCDMQ56yEU+fmpHebhmznA/9g9oXtvXN9foCOeIjg==", "signatures": [{"sig": "MEQCID83Du6K+kauGfxWHN3QdcAfFgR7aCzcUf0M7JfZd2+iAiAutmMaqzq4DqAFLX6jsmLKHRs9ygHlfgk/MUqzMBZsLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3746420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdV27ACRA9TVsSAnZWagAAwAkP/RfGTzn8ZjOMXlj0sVOW\nh8OhJlPTC1uOK7tAoqNa4qEshQVjieWyY7zzvSrqLhP30M2oNPR6jtpwLhD9\nzHBDlH/dIDBUWl+oTW9nM4V6nFxp4aVc0CycLBZviVvKlzpQP60heugrrdZe\nFTRsLKlHmK8Z4hkBf1XBNVP0UwzfJhxiPxnV/SSJldQNPFKMDKByvk9akoAi\nZOgSem663LA/4omBNTideoMeOmiKzbHvaPVtUymS/Qm+bT1rEvRk7PsGe4J9\nYRqOpCHJbkT5uRpkRfzupJnOgEBxYJ5Cjy6e4vmiw7YJtrz/F/oVJNuAcdJU\nOEHvVGYUdSr+Ypz67OxL2amZDKxGonLPUE+2c3RtJTDjoQz2Tel15QYNApaM\nz3iFZ1+KWalk3lQNtmWmjXm07FP4lae+6S3qT/+01XDhVhKI6fjU71lF5eCB\nejGhGu9mkSsjY38JFO8d7VFZaMCGJJ2j4WXI9la/cLUKcz6SjfpyknsjfsiM\nMU5MDhfmWL3DSjWh7Zr9bMOC4SF/oxJK3usYohijgy5YDdBYXjw+x1r4r72G\ny7BbzC9+pz2jxdtGIPSwcC4jcnL2Ta5oLyn0fk03rFNih1IALc0pCMgRWAin\nAJbbsNecbpStX2sv/on3Ei69ZVSwKS3WDc1p0dbXo2wuMJUi97BlNAgGsD1k\nnZDv\r\n=1RfZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "7e51ba14902680a98c27960a5370dd1ed49c0cbf", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "12.6.0", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "^1.0.4", "uikit": "2.27.4", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "@babel/cli": "^7.5.5", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "@babel/preset-env": "^7.5.5", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.10.0_1566011071306_0.6051078256154128", "host": "s3://npm-registry-packages"}}, "3.10.1": {"name": "less", "version": "3.10.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.10.1", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "a1c97e3436acdfd1757916333b9906f3af7d559e", "tarball": "https://registry.npmjs.org/less/-/less-3.10.1.tgz", "fileCount": 701, "integrity": "sha512-xlPx6vt5nAm1DGDnuv06SNQba4WnMKY5vZkgCrnyKXkDbs09kl9cc2nSS7OMufLT60IWsxUSnwr1Rkh4rkMFcg==", "signatures": [{"sig": "MEYCIQD+UQF9iSiE/5/B0fUBymwjhV6GGMYmcNBk9AjshzC+ZwIhANclVi2jqbDlW1qJbukuokoK7MSzhBrfJvrNksLG/sa9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4206720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWKwfCRA9TVsSAnZWagAAVeAQAIVgoF6uST9wA7AtETSD\nWwWvMppQnivdoEvermQPMpEvLyFsMQi9Ep7WZpJyE+/fk6rvJvEEw4Iubuwm\nOqHyPgXy+3WaZBmVhf+9oR79OX5edtGwWRj6dPWYKBbNQbrIfbXBqB9/gcXi\nrfPAa8L56Yr3j9Yism+jy634PIZyWm5izSPpE5j/CkbyuBAJXqHa5zrcshjI\nNLpdi5jHWw2rklNlzin3B4Yblf4mjwP5WS+6/IkNk0Ji1oJBwnSoyJpAqA82\nRkWv+BQ93HC6dm9P0PqCDRzgVVQ7BlPkvHjhKtbaRpMkc6V7cdYOBfosM0qN\n5tGxH2hN60+3u5MKJkFO/1X7oyS/Ayfs0wyLuUBQ2AWSO3aaDga/pinQ4IQV\ntnjdjAcqXvbWRwe0w83MZPTy7bOppC+b+iQ77p89XIKQDoqskYd4IBGXnB33\nVCur7YK4dXnc3YBpmWWo2i6QkW2YWMLvVc5GHAMcCwmuBk8qPz6bFyFoddot\ni4mKFv8q17zKaoUVJh61x5lzIofcVBUsdIhYkBYMQ+YcKvYJv8a/cX7bYvnH\nW49CvY/is/Gl30jO+YROj5E9eUu1V+GOK4jSpavbHi92ylIPVmsKScF8ajRC\nRWF2+Zz3xNSN4YUwrnEq2UjHwwIsNmO41MpSQIyc5gmv+VcVsktWHwAD0unZ\n/9H6\r\n=T/s8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "62d9d60dcb32338ec0f192b464f46369eddf8c30", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "12.6.0", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "^1.0.4", "uikit": "2.27.4", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "@babel/cli": "^7.5.5", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "@babel/preset-env": "^7.5.5", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.10.1_1566092318308_0.43145836454336894", "host": "s3://npm-registry-packages"}}, "3.10.2": {"name": "less", "version": "3.10.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.10.2", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "49b94da22e18ed112d9500bdb2340969dafee57f", "tarball": "https://registry.npmjs.org/less/-/less-3.10.2.tgz", "fileCount": 701, "integrity": "sha512-crOb5r8AnIYkWss6sJNccgb20UqvDfdZhopuDG9LGfcSQY+fAKIA/W4as5+ELnfUxnDruqC7iYyeDZmC44nMnw==", "signatures": [{"sig": "MEUCIQC/CMEclp4Sfr+CExdL47PWa5y5W/gPDxhefsqrUSbFGgIgI0s9P3t+e9tjLSQmbuuPmP03yVp/U2rRJwn0oynqqLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4273871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXMqGCRA9TVsSAnZWagAAnMEQAIBdfB4A41ag0e8vUlB5\nekrfyP0rZjmay9o4QJub35PAykYXwilbOxV38vKigElfKF7fxotdLM3V7FcO\nd/W+1P7z+pWklCdPvjitdbGFK/k6C3gloWFbzmio2Y6uYCUwJd9FXZgqirZX\nSmWXr4R23zhNe8Sgo5m9+hl8UxfPqRlCGxwGm2XyBb0cLFLJOI9uLUqXGf0O\nKab1/T5f4ssiwn/NfjW5x6m0lHEvyA21hJQptoAQ4hjnn5h62UD6L2rXr2xM\n0jk5i+2Em5RGyJ2mBUsTZ2/TjZnEEwTws9JfyugYEN0pLaLp0WXQBX8nvUHD\ndtwojPJy/nE8vLA1HuJgOpxhtXTYC8vVQIkKhridlzvAAVwXqW46HI6kfnjL\nl+dDbwkbEcrHkn1MLWJXNWdyrTTYJtxVZaf0m5H5ra326e/QldYw9gYN4lo1\n4/KDhwG1hGE4Vty7G5s0OunCnI2r/+ugz2zDJdFCcLHGzCCx/r6/xea9uKwO\nfdgUVDi/KLQzpXgc40G0XpDIEqHMS/CjcWNsri36fVjZYvrswyc0CSHBXSvV\nYumr+HjjZVBEbIwtfThv0dPoX/P7JZAqf6CXVrWKIL2J5vQnbUCpANeZZ6sh\nYXBc0BjwNJEiViEzN1gaCJaWJDJOsFwtbzHjjan/DIMVb++oFi4NTN+pzEBV\nLWpt\r\n=IgNV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "f454ab78c476f9478732003a9232627303341f9a", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "^1.0.4", "uikit": "2.27.4", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "@babel/cli": "^7.5.5", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "@babel/preset-env": "^7.5.5", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.10.2_1566362245652_0.8840894877628445", "host": "s3://npm-registry-packages"}}, "3.10.3": {"name": "less", "version": "3.10.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.10.3", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "417a0975d5eeecc52cff4bcfa3c09d35781e6792", "tarball": "https://registry.npmjs.org/less/-/less-3.10.3.tgz", "fileCount": 701, "integrity": "sha512-vz32vqfgmoxF1h3K4J+yKCtajH0PWmjkIFgbs5d78E/c/e+UQTnI+lWK+1eQRE95PXM2mC3rJlLSSP9VQHnaow==", "signatures": [{"sig": "MEUCIQCeuKPhzcl1JNxUiaZyO11UW8m1Gp+CcyOVsg/6bblCRwIgAXP3DQtmQbX8H7b531GQgy/ZpeNkO5hhKYyKqS9Fp/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4286777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXy60CRA9TVsSAnZWagAAnD0P/2vaDNAHDjaECs1FvE8b\nfYJKtShN/AIONVsIMEuGnyIaakiMHRZZburGDFnBWAOZ3tDlFQpS6Y8vhUEE\nWj0pVccVlkh4q2k6WI1skd6tHpwj+3Zq+LjzLlPbrGoaK3vhYG+2qHv8KCtj\njbrxsgpO6dAl6H/vlRlaxtvZj8967i6Oy76VfVNRVLlWGo3Cf8RAIFl3fAJv\n23Zxsgd3l2+DKrtAGCohEY/gYioBYFnxuW3RkHiN54FkS+SNgQfnLeA9idc1\nHcE72mkKHpDmLNjz/eHudLURFfV4lOTB23danHe44M+VC6PRzxLOknVLdc4c\n4rdQJZbcPLjs3M4NwVK1wd+FJ4FqqVGt8OKsKI2IjilwogwIHuF1L1hoYHwV\nDc9vp6IEDwyCj1XAUeowY6CgmLmFGZswBA3iYFx4inqs/ZhIaB680kWyJLLM\nKM3obDxgUZ/YClr3AXZHQv/PKRGaofbYqmJbBFLnOlATJ5y98fggLzEky3lX\nIwIOgV+9U989k+s8GFWkizrvrYssTi49Mnbug0jF1vbpj/6gcK4M26TrgQAl\nv1JtMwx525taqI6qvNLiYg+tzRNy7mBfrwq2mEqA1sSgnsjd1IIeBwv6OqJ/\nXZxi91AcK8E/ktM1yMWX0F98cWxrU5mNu5IKOhEa3co1UaIzsclt6sln7jDb\nHL0o\r\n=jkNM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "66a839de9fd28e5192591a6299fb92578f9145f2", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"diff": "^3.2.0", "phin": "^2.2.3", "grunt": "^1.0.4", "uikit": "2.27.4", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "@babel/cli": "^7.5.5", "time-grunt": "^1.3.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.0", "performance-now": "^0.2.0", "@babel/preset-env": "^7.5.5", "phantomjs-prebuilt": "^2.1.16", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-jasmine": "^1.2.0", "less-plugin-clean-css": "^1.5.1", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "phantomjs-polyfill-object-assign": "0.0.2"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.10.3_1566518963892_0.1967870715901745", "host": "s3://npm-registry-packages"}}, "3.11.0": {"name": "less", "version": "3.11.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.11.0", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "ce3b6010d4ecf00710d5a68915207de7541b4d73", "tarball": "https://registry.npmjs.org/less/-/less-3.11.0.tgz", "fileCount": 1602, "integrity": "sha512-dAui5qzfxuWY7BIEt9/gy5EbDhwDb44rqaIUFYeu8wEE8huMZ/PzB+gNFONEG5DUPrOrOGcAjGeYVg6AFiA9KQ==", "signatures": [{"sig": "MEUCIQDp+8rHmJrE/eAM5i3mn8zo2gpFO8r2y0e6H6e2ns+/8wIgKHGbxHPPOxiQ4ALuFRlV6ktm8ovDQw++Pj4opPOdSSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13523917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQIIaCRA9TVsSAnZWagAAHxkQAIjAfU0JFWSrvLfQtBkM\nyKV2GEzA61q+fHyZYUZoWI+UAKj7RUJUvksGgcAlvNB5UnNd4GBCnU6ZgaZF\nMpoHwStvgRvRDtCHZ0w5TL58M9tWG7XvjHVUuvCFeGRpQMMRms3o4sMDK4H1\n4gYHwLfbrR8e0kRcNrrJ8oXRLP2lj+Q3PR3hJFWVhFwacrzptWDid3no5lCI\nFiTMIdTvXsJxFps6ZX85AbbJEajunoH1YC4oMWOShwCgjM9FEeyYWzWEvYLz\n/+n9vVFb0StVeOcuqD1vWNmF0qVn0qb6gxSpkPH9+cT9ERL/SwhjSHxrCt42\nFe9J06aULyT+SaTbf48EKxruVL0R2HvA+DEekro79MbR/cHajPbw4h8ZXJYk\nPSsmaMEa7zC8N7Tm4XoGXMRsKHHLUZJDJspFAaXAu+C0mhh0udMZ2jauNqx9\nBuHKJzu6uLJStK4R9+uvmylfA7ljK5gjPP/nfZj2y5oqj5s47acFpN6nIvkY\nF6EmigkxmK0q/Wczwb0hx6ZZXaWWMT4df1HurSXi8RZVHnqh03zZO4a9hlw3\nxqGgMa33aPFbb4lLU4d06K822EiSnHmJ3XaZDbqJeON57+jG9JFI6XLCgcii\nv6BTSswydVAH1Vq+4ycjHsb7tja9lEs+5PL1JVrjMXVrFEOxl3IRuVgbf3Uu\n5Ras\r\n=rBrH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "1adaadb0fac1104296da297a2cfc1b91e5e9f7c2", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@typescript-eslint/parser": "^2.3.3", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^2.3.3"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.11.0_1581285913870_0.890537362225144", "host": "s3://npm-registry-packages"}}, "3.11.1": {"name": "less", "version": "3.11.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.11.1", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "c6bf08e39e02404fe6b307a3dfffafdc55bd36e2", "tarball": "https://registry.npmjs.org/less/-/less-3.11.1.tgz", "fileCount": 1601, "integrity": "sha512-tlWX341RECuTOvoDIvtFqXsKj072hm3+9ymRBe76/mD6O5ZZecnlAOVDlWAleF2+aohFrxNidXhv2773f6kY7g==", "signatures": [{"sig": "MEUCIAFPLicXycxC+oyjp64zDobeg2dzPqvyzcWggx+x8dqhAiEAmLmftXD9K4v5kIsku42mbELiXD2Q5M0/l0G637NE+tU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13519930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQkKGCRA9TVsSAnZWagAAUEEP/RFeSAzpywzi/p7WsTMc\nLav8wtb6Gd0SEDp6hgqnxF2euvKcQww07BQA8vLoJhSireQMC13t8sLGr9eF\n6s26xaOyMvmYhszIRnfcgD3zOlKmACSnBh943j7TOwQT1jj7CeGv6iutSl07\nQoO+NxDptqmrwDXM/kmMy8TTDwiusXPN/CiAzFh3cwcndAYIv01D5umoaUbG\nJkqrPvmQGDOevYBlRRXcOuQQ46shUBBK8kxNI1Q/vGWuD9rI3tWJYThiKFTK\nzy89o5eNi3N7U0cbjK/oBaTNXs2AqsDXki2HXQeKOvFa+12bkHV0TKyZPpeO\n3teFPPt97VOQg9vMXf9a5p4iEl0aYN1sssG/eYwylSj6rvEmBHEDAuHM2/zC\nyKAXRL3lTyRtSdVtRnLwr185U3El04fd2Ry4DaVd9f4dcu6TrHarxTpFzC6f\nLgebSAxJEeSrX/doL0wnNjglspHPvluzijY1wO6bjR9BjtPQaoWOXbHeFbAf\na8TzG/mZ77cTKw6PZbTnmxZgmuia9hk/7fVy0hs5iZVJZmKRJeLes7fifDiI\n0JsgmjTRgZ4NU8KruZVuqr1jwr/f40eqWrv5/F5hSWVOtXNHZeEX3TYcAfSH\n/B/j40AVgqol6fskBmQ4X5kWGz//A7WyXZ2pE6Iz3xNLgw9UhmU5l4ROPVkN\nFPIV\r\n=RSZ0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "842386b8db5cb5afa4edf5a4c81d40bcbf47a6a2", "scripts": {"test": "grunt test", "grunt": "grunt"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "tslib": "^1.10.0", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@typescript-eslint/parser": "^2.3.3", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^2.3.3"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.11.1_1581400709637_0.8072737893063786", "host": "s3://npm-registry-packages"}}, "3.11.2": {"name": "less", "version": "3.11.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.11.2", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "51a484e9017287f5ac3db921cb86970eb7506e81", "tarball": "https://registry.npmjs.org/less/-/less-3.11.2.tgz", "fileCount": 698, "integrity": "sha512-ed8Lir98Tu6a+LeU7+8ShpRLSUdk//lWf1sh+5w7tNju4wGItztqDHp03Z+a2o1nzU6pObVxw1n4Gu7VzQYusQ==", "signatures": [{"sig": "MEQCIFXnXvFPPSs4J5we9NEPXgqx9tROtmXQgWwSVF/Q/7XBAiBLdhC3GTj0Mc6wwT1JKLl7V8dvAg0LZ3eSZ0RqqMU6/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4029517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1F/TCRA9TVsSAnZWagAAfBUP/3WmSQWCID2Xvw1OTAu3\n2Q50VaY/9i98XnxOER7LlkMdtRWF58boMO8BgH/WHYNtgEziJo+0t8CkxNQj\ngBS/F7YGxErmLIx/MNdaSMguNhnY6nqm2VXPKRClR08jStiOToFrFeJkj5qG\njSMz3k+S4Y83Iwg2/ozLHSd75po29IoIZ2I7Wd96a9DdBwmCu4Ae4xX9o0z/\nsWOJ6G++PVxMaRzAbHBJ4EBXZNlqSamX6SK8GU9JZ+UmbhTcAfg80ik+jLDI\nC2dz0AxDwqbK5BTfInTy6PvvS9QS/+DnKPo8qPwEn3aiob435GTAYXPXDWMt\nOFt9a6N6uKbsetphtVnyV9q/3ZlIZf8RgpY5cN0QjmL3t6k4awr0M3XuuYPy\nF2heTh6MdBX5uVBlkT9pGoDLlFS1+XV6KL4U2TdfOC8PsvXnL9dGkuSIl5TM\no3cM7P8K6f20GwgtAmSUqIj86qKjpC1+Ri10VWNC5WCr+cg4tlbWsxOQEF9A\ncynv5F+/1+aUF5iYKJs1KXW/jWI1xV4sA/Sm/+gbS6f3Uj6XW2jJcmwl1FHK\nlfS8wGpYOCvALy3dhVI+I9ALsD/zHqIOincuSeTtj6AnZvKMl5RmkrXMQVr0\nTjmXAwbBnPenNWm82zEFp4rWpc7KeIeg/O4IcEy7YKD8NC0qsrLEXjjc88WX\nlGQv\r\n=9C6A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "53bf8771305fc7228991b3847eefaa1b9f7f63fa", "scripts": {"test": "grunt test", "grunt": "grunt", "changelog": "github-changes -o less -r less.js -a --only-pulls --use-commit-body -m \"(YYYY-MM-DD)\""}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "tslib": "^1.10.0", "promise": "^7.1.1", "request": "^2.83.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "github-changes": "^1.1.2", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@typescript-eslint/parser": "^2.3.3", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^2.3.3"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "promise": "^7.1.1", "request": "^2.83.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.11.2_1590976466852_0.4255448140274478", "host": "s3://npm-registry-packages"}}, "3.11.3": {"name": "less", "version": "3.11.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.11.3", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "2d853954fcfe0169a8af869620bcaa16563dcc1c", "tarball": "https://registry.npmjs.org/less/-/less-3.11.3.tgz", "fileCount": 699, "integrity": "sha512-VkZiTDdtNEzXA3LgjQiC3D7/ejleBPFVvq+aRI9mIj+Zhmif5TvFPM244bT4rzkvOCvJ9q4zAztok1M7Nygagw==", "signatures": [{"sig": "MEUCIQCSjMmtz9jaqmvnxED7zb1Jc8mAc6/cPvEq+Z/gwopn6wIgcWKIgNuc3PIp5VLmAGHjHf0kW4PjVhrSX4mtivOHeNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3576990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2pMsCRA9TVsSAnZWagAA3yIP/iSHhC/EmpkoxIXiqrri\nnj/y7G6mfk3EB4qHJzX0YTnurbEsybjXPzRMtejZBYrwFKVjEMPpgTF8DNUk\nq4sqJywXvcBKRoICm21nqbi4uA9aD9zjo2e8l7GdzvGM0/dri7bRGaG9W1O7\n8XDCnfTBpCVafwiYljFCrDO6LCEb9fHwsWpMUWKOJRUVUu2jS2zxW2v42hex\n2yHlJ5WaclZ1oVuM3CdTdpLLuNsj4o+W7HhIR42OteLt2lZvHMYl9kQCTPFa\njWgvjDxL3hPte3TIRNRzOeeYMbH1XdSgGZ3BuzW9hDHeYWiStWFnGECJOV9X\n+83mxUzx4ATLQpqoi9kstO5UoiPiOv+74SegSPWayXLYG/1QYIwezmODLnbI\ndfiuXUMRoX4RD7ju6ljCxrDgQtjhVh8RtoayUfOG8CNpYFW+HHrr41c+6m1p\nj0xSUfbjcbtIfazEMcHdZTSl23gqQptIcyh4jAhoigOSIr3EtiBJTKHGZJTg\niuSWT5SrJSmYlHSbc3XnxRY6mxNbnTf6/6pvfALiMUKF8bXWgjkAFEN8gxZ5\n2Fh3RvCa9xTuFL6t6np0FPfxoo9bhcAVBfaGRYTqBRr+8DT00LH+C5GkECI9\nxMif6qV4MgW3OpfMwJM0lHxHymk4Bcy2S1/v1b6N/CqgU53VHQ5RBvhkONA9\nZHUK\r\n=PmRK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "6238bbcd3b7c23ac095d90a7a0baaaeccc10b8b1", "scripts": {"test": "grunt test", "grunt": "grunt", "changelog": "github-changes -o less -r less.js -a --only-pulls --use-commit-body -m \"(YYYY-MM-DD)\""}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "clone": "^2.1.2", "errno": "^0.1.1", "tslib": "^1.10.0", "promise": "^7.1.1", "request": "^2.83.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "grunt-eslint": "^21.1.0", "import-module": "file:test/import-module", "github-changes": "^1.1.2", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@typescript-eslint/parser": "^2.3.3", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^2.3.3"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "promise": "^7.1.1", "request": "^2.83.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.11.3_1591382828135_0.6363315447946214", "host": "s3://npm-registry-packages"}}, "3.12.0": {"name": "less", "version": "3.12.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.12.0", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "f23f9bd94ba72495994865d84e3b5ac6ee8a0363", "tarball": "https://registry.npmjs.org/less/-/less-3.12.0.tgz", "fileCount": 318, "integrity": "sha512-3mmSHFRP9hGxxQgAKgChfau1LO3ksV/zyZf1qd2ENyBV778NA9Ids99wFRA20jE+5prT7oScKod8PoGlxSe1gg==", "signatures": [{"sig": "MEUCIGzi5jer/dNQWAx8YNr0djc1hTwdkRWEDeqimwO2NkAnAiEAl0nnJIZvwLvIu065hx097JmEmj+pg+YcBAS75O2ntj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2803620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDHsvCRA9TVsSAnZWagAAqdgP/2LMa3OpR4DLglKIYY54\nJjzyoIJ0y4iKP1N97McCVC8hZIEZAYZKhoXWVKQJ3uB7Dppoha1f2TVGAONL\n+Hwx2Pgb/cxIn6SNH5MIVIUyEghRrBXlhWuHOOT0+UILBow80Ix6xv4pCXC0\n/EJCuvzbPL11hxFH69AoDyU/czOFWwY+j1JRDlrJwQ98bL2cwXdkrXlSgb8/\ntbTYzM9fePCbMCL7VKj2S2lioIWVnnL3o6VTKdk3qvksPZ/sXN4qodwHlamM\n49npRjmDBCoqXzC+VpAAx9Ykz5aUOHoJyhmBhBfqO7g942BPDwRDfGv8RZGr\natDCfbv6aP2eIaiGO36155Bokaz5lvgT9F1xBuAJDKEJLZ9QNSciaO5aIanQ\nORjlFINAnDipXaPAee2QEE9g+VNy3HZ2mB0mwqUXCwZlK2iYyw0vpzTXM3al\noLQJ7lnQn40siqC1RRfZMbwEx7sYT0Yl91D2HMMlUVp8k88sm3Lhwa4jGYVU\n9XyDzKLaCWWv84GJwQnzwGDtAC035sIsx4IibY+Dmvd3dZB4vbDHLAWQo59g\nD/UHnLbH5q+uNmA0WQ+xXkBducO3TB+0pQmZZoLomjZozt7dO3PRo2bihJfN\nhbo0x4GbtaE0k+de5giwHv+os7Tag/CTJedb4A0akOxmU1bY5QO/1y587LCS\nEsHt\r\n=+cbC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "e4f755112198b758a3b22252a9f4f290b4f81df9", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.12.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.12.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.12.0_1594653487178_0.4668049871251829", "host": "s3://npm-registry-packages"}}, "3.12.1": {"name": "less", "version": "3.12.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.12.1", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "833a628d60a5dbed3865c1b42d9d8aaeeb8d8c2a", "tarball": "https://registry.npmjs.org/less/-/less-3.12.1.tgz", "fileCount": 319, "integrity": "sha512-lI1s2TO4BbMwS/S45/V7K0UuKlu87ie6stg58b0PTPD7x9MDXWwyOp+xQORin2lyYwn/HX7r2o9mjPJ4rYvUIA==", "signatures": [{"sig": "MEUCIAqGAJ65OBFBhCNacMGGmqtxp9VKUTD5hUunO3TcjbUjAiEA5A0aZhNTKOFUCsY/QTvEa59rbVf8p7TmLQTLsWa/TLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEGVvCRA9TVsSAnZWagAActsP/1k0tQAh1EcbA5hydwzQ\nimwBYm10Et6L9xAqtcqTJNscYGpYvSRquSwqYdIYJCHu+ktSoV5plny1xFGG\ntvOcjraUQmmemAXBr9Y3mob4e7z1UHnEqMvyyyc8O4Itmjk6bjbv7XC38ptW\nnFU23wI12hOrB2PplqSYNOQNmHCb0x+OxyvrHay/66bN1Wunl3zEpOdhYVzp\ngCYfcRYbg6Jbxj0oyE/zANBGrvoovEQ7NW1nf134RF/4MpMe6vqg+4se1M70\neR7m9vM/xVlnfO6lVbZI99Zqg2unPAenWCFnBfOvNV4HdYZK/7DrXt+spd/Z\nKVAQtWl+DNOeiD6yyuQFZfujjlvkGfvBf/i3D0s965yUIePhN1FzzSuHKk6/\nYA0fisyi18m2EO4h1JG9V6uOr1ic1uN0oq6Cn8K13x6QGra9mEFct2o7e8LQ\nene2wkemj2Vo2wd2TqqvT1+jM4Fdd2ZMwE3qAYS8cPgM8nwINxtk6gFBgiX6\nLU3QWzqkpim7vkVwry+DGZP/cIcqyosEfBjjsqupAqV2ia/v73QRQKg7Ul0+\n0j23Zk3wam7SPmDlC0k86Jd5y1dFKgBIkSg6hUIgIEoyzVKASxfeQpyE2TgL\n1XpvRD40fg66JJEn8ChGjQxDwQY7SdxKgrkjot4Cx3WRK49eZeIeHOYj7MAt\npA05\r\n=1uNe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "707e97f69c422ab6550b1184ecf76d862d6a431a", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.12.1", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.12.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.12.1_1594910062830_0.09756961425021715", "host": "s3://npm-registry-packages"}}, "3.12.2": {"name": "less", "version": "3.12.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.12.2", "maintainers": [{"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}, {"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "157e6dd32a68869df8859314ad38e70211af3ab4", "tarball": "https://registry.npmjs.org/less/-/less-3.12.2.tgz", "fileCount": 319, "integrity": "sha512-+1V2PCMFkL+OIj2/HrtrvZw0BC0sYLMICJfbQjuj/K8CEnlrFX6R5cKKgzzttsZDHyxQNL1jqMREjKN3ja/E3Q==", "signatures": [{"sig": "MEQCIHS47tkHAO6CXRsRgVM+W4W2MxB/4VyWqTTtjJx6MqxcAiAi5yPMMz1Aqr2jnww7mphKbsvalx+bDVfTzIHCppRobA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2805347, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEHqlCRA9TVsSAnZWagAAfzwP/jGmlCvaF9yPGgh0+74+\nPmJfiv2VAOzUfCLLLZWzlalKN272nPWeHmU93jVs3DIIIIJU6/pQhvuEvNDK\nhcP/a+JDtYz33ZG+3df9Ooen5jF+KYake5qSSWDNfCpoG1+gdWVb+O7ypx3L\nrmYy+RBgILpas5pKTMEaMy4FTFSKfSsQhxjiMRQg/NpAQcK3R6BnziiHw0MK\ngKv03hrQzYSTk1xTxt/m9c+9dhLn47XndaykyBp+ShJnfCfd4TZ4Nn88scKC\ng+/69DplZH6mqlu0fHRG5b/ENn9GZXsn4ldlNunruc8ZOisFhd5/F6N1V1fC\nn+gFrzlsAcwb9zskUrPsGXf4daYdntb0oG5xbvUa6P+EFQke36T6Dp9XUkXl\nQ43B4LOZeutFZEm+Il0QqftazAlEwfEg+JZXGITXOelk4C6FxLbVSDcbD1B0\nJbskqXYe8zOtXgjmwdUgjZkR/1suGSwnotSqmKO4BTjuarlAwXeFDVKie3/G\nSY6JjlqqPiOEL5yyERv76M1zztn/cTGAmS5g8YzGR+CW9y4+y+1FxvX2vMzc\nKap4jP+tV3c8HX9kBiE0lHWSUjLhQkDMmwFWCl7GGQ/OJSPOt/EGQRsDKyx7\nUUltrlUGbu32k7VgTF4E/prAhoxaNNKPTvR1U5JDZGu7zhD0jn3zoO2L/txx\nz/nu\r\n=ZgRZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "185762a999519708fd914c9fad99b61720f34281", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.12.2", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.12.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.12.2_1594915492912_0.5597479664300238", "host": "s3://npm-registry-packages"}}, "3.13.1-alpha.1": {"name": "less", "version": "3.13.1-alpha.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.1-alpha.1", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "1bea2e7f58434befe1b6ca43244e7a467c3222cd", "tarball": "https://registry.npmjs.org/less/-/less-3.13.1-alpha.1.tgz", "fileCount": 318, "integrity": "sha512-SNMq5YVUnl/AlztL1bfqH8EuZISImHzMEXSPk4cktstoBBdZ5M34Kbog6mW9/kKasvqn5pfVOmPx5kPmoz4SaQ==", "signatures": [{"sig": "MEYCIQD4pkCK6fAlDseYXGY8xCMG6lObd1o4KIX+Y8e5vyhHlAIhANptPH9DtxGzA9KPP1Busx4FUu4ltFxYTaNQ9sDL8NTW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2760851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfc0D4CRA9TVsSAnZWagAAKQUP+wX67p//or89m/uao1v+\nZk5BJkweQl71xFsEi6QCa7vJbSWy6Vm6RTzi+zpBpRlJ9IG0xYbrNurjxXqT\nOy7RnHZnH/SNgMnopU7QBFbeNpLJXwXAJeF+vLapnpaAoSnF2Gn/darBoqkx\nd/WID+LiW0tL1ZM1hwYi9IaCVfSr877yNXyHlT86JWym6mgOujXaDtr8awrv\nTgwBU4diafe6/OwnXzovE/YwLkylSClJZ4iod8aECSIRoKKvSG770K4zzrIb\nJ8CRFMYhWhAPxmUxOaJehzIH08l7zqWHUSGOv+DEi67jay0hRSgv+qQhx4vb\nFOZxUDywhho/aoM3jwuJuuLztWBEkj5U1Wluy9uSvFPdHrSKK5KcVLp5NBe7\nrs53g2UozY37b8iHmrazycw6IXh+OmZkwjx8QRocJb9iVesrTz1laSxr6Fn8\n2nR/Gwgt1sCKs0JYBKrPlWkIOIlXPnsgJWwbEqENt5fqJlY27l1flg+JYbGg\n+70YlIIVstirvHG3arDpXdB62YLwGRqsHeknrMMqYMWwSW5LU8jvlhSXr/ss\nJi4N41/Ch8pd5kaJMhaq3g1CGY6AFwaCERI7rR9Js6TulC+vOn1W8bJ+oots\nr8LrvXZLrqR2tRsxKYqFG+c4DjclZt9xCbG4e1p+GiN5fbExFINCPg/+ViGr\naB23\r\n=Gmc3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "84d40222b65701cbda0d5a4c218c6fb35f0ac368", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.22.0+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.22.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^2.0.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.1-alpha.1_1601388792217_0.5467929505178257", "host": "s3://npm-registry-packages"}}, "3.12.1-alpha.13": {"name": "less", "version": "3.12.1-alpha.13", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.12.1-alpha.13", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "6944168e8dc342e12c0928be542c2c89a49f0fdd", "tarball": "https://registry.npmjs.org/less/-/less-3.12.1-alpha.13.tgz", "fileCount": 318, "integrity": "sha512-wU8nac1ehDa7KoUbZzozaiqM5tOfOXkyz94nrfIg9EBBMl8Wi6DcM1N+vXP22bLMa8VnMMuIEJROL8a+ME38ug==", "signatures": [{"sig": "MEUCIC/vPJVpHO531dnVb4K8/QgEmN3TniPGC2DdCKRstek+AiEA4M/YCg2arsnu0YihCpBRp0q8DvTalNx1KEXw2AdkcaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2836116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy77LCRA9TVsSAnZWagAA5fkP/R9dCVSQ4bqXTfAzsuHn\nuXddSiIjXvsnEZCT2lDwAYMUVclkmnbq2ADYDoE7G/r0o8vINp3fxQLeWDfJ\nbNShEMmHLiyzVvHUmmo6ccwAj54/lQe0qXumimK1MxnQHjTdBE4Z3TdcstIS\nmkngCS9pjeFw7HiUrsgxsskVOBDJYEER3/Wkm9pTfSYQ/rQkMmojZNw7Drwe\n620/ub3w80U+nLOGpKKz8mDAEfQkdM9rEsS0GrJM9t1RAeYAyZLLqYE6o+xC\nuwF/ahgZHo6VHgTFAtyWLoxZxv9i2P30tSvrL3rCvdIuZ22gfCp8wRSxOA0L\nd1z9YCHo514k4VifzzejirUQsUXAQpOZdSbKkzzGfXIeugKTqo40e/6VCsgu\ndX00nEL8I2JlVUsalNhwJiXDzBx+0bwKu6NZO+ocKRlHbCYNw1EN6PKj5tua\nwLlEYdJ2EHGaLMrf/cP7Z2SQO+VHvZiX12mIjhA5lz9FNgzcq5qOP+idGGw3\np/7c4dAB7hnb2hc6ZjVoP+X4hppJByIy9hnks6dKD9kAEyGL10c/m3esTAkl\nMH0vpt/LB89YHkdebdhH6ASY24MNhxx2rf4Sz1PyAOSV6AStEnOshD+SIXiG\nyuBXWfU50zoJVACJhVDD15oJbw3J03VgtlHveLVB6g5JWtSoY+3h1sgYrpUZ\nJmqS\r\n=sCm7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "ed7340982be9404298c8713cf00f12b8a5ae78c3", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.12.2", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.12.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.12.1-alpha.13_1607188170471_0.5025158099554847", "host": "s3://npm-registry-packages"}}, "4.0.1-alpha.0": {"name": "less", "version": "4.0.1-alpha.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.0.1-alpha.0", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "c2059a13ea0ceb0a8e36633fd6a97528e7dded2a", "tarball": "https://registry.npmjs.org/less/-/less-4.0.1-alpha.0.tgz", "fileCount": 318, "integrity": "sha512-2gH5xvy68Ex0AKXl7BVgu0YniuS9rvUcZeo9CgvnPhcmmEc+pzqds+VgnoqDrm5v2auw6VYWkKXslkPNqUU11w==", "signatures": [{"sig": "MEYCIQDRCVqJ862wg38NHADoF3OeGnT9SJH+72NQB8M0nbAvVwIhAOsA25tpubFayr7mE8oX7OZCBi7Yz15D1OdpTd2sct5x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2835885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8DkCRA9TVsSAnZWagAAjbcP+wUA3jrZa9+vOd0CphHw\nu6EOijvQ6B1NZcFCZp8bczVaofH/YwTfEHuOqztYTGRMbTxD+7ZkcRs9Ilmd\nbf+lrd0G7fe1qwHgnzvOms7tGd2IaDOX8NfaV6iKnedjZDetEDPlvOKKTBYL\nWh9M9IZ3Cd7QVjCp2Td8Grt+npWZqJ7EUPLZFFYoUoRhydoc+eKds7Bf4Cej\ne187AVtE1ctDczY7f51p/+TYXIJ7ApVZbeDmBnrc6RXKwkT6FZ+rbzvoGF5A\nq05L6FQ1xLgPfWE0ldU4j1DGpJb8ZCxmosZ6n0d7Qogl0BXGZDoAdM6vfuRK\nHn5tLMXfvEbe68Swp1yx5Q+l614CZKXBlwP9D4GqDVz6tufK7nTmE+aU/PMA\nC9Ypzb7alJD9542vRH2flmdxvJP6XUO3n3QBcNrwEawJGMKSy0eNMdbAo2lG\nV8/+W1FBswReBtcoVROhkYQbEsrtuCHBx8cPwpuE2WOKXkpFvqX33hz8MMcG\n/45MBC0RCLTi6J05hnL2oSXSMj/gFIM3U+ZD+KAQiMZOha7Ij8+rP6eZj6zA\nXujY78CQXHoMWnhG7mSbGakUKwQJ8dAYbitJzalgRhDoI39bRYE0TKhvz4QV\ncIUxMFmJrzoAnMU5J2eBbp4iruWw5bPBWQP4d9WvEGoHUWQuUGzkY+A4Kx8+\nM+Z6\r\n=jgqO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "70e242c5bdb2245696f7ca63845b50a8d00cf5ac", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^4.0.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.0.1-alpha.0_1607188707535_0.8437988316839111", "host": "s3://npm-registry-packages"}}, "4.0.1-alpha.2": {"name": "less", "version": "4.0.1-alpha.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.0.1-alpha.2", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "b689a8f16e9837e1f91fbfcc053f9d579ce5b071", "tarball": "https://registry.npmjs.org/less/-/less-4.0.1-alpha.2.tgz", "fileCount": 318, "integrity": "sha512-2Tj5SJh1k5rySBiblxP2Ck3wxBHUoSLFffwLP4R6GqLg12clj9K/hu+4RCCWkFcn/kY0mGL8OoPOUzRqqpf/oQ==", "signatures": [{"sig": "MEUCIQDLZ6DBV2ox7cBwd3tk1f/gCIeUXGc125itM3x0A0k/kQIgaJnLKOrPbjbe7aRz+s/VsBlZH76hT7/nOefgYEnEucg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2754835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzAqLCRA9TVsSAnZWagAAW7UP/jkVm2lGgNoS8vxynhNU\nTRS7W2zvYCIcS1uaAd2KdkeXGigj8dq8234MlqE1ztDviOJ3QS6r7yXvKcAU\n20iQKhArHPSZOmAuyG8cglmkTzNL5iYum2agmqiElB7PpaZPCTInZvMwrxYc\ngrLfRPhiAq1KUcNTf9I3ejGhj4TTlxU3L/mupe8ArJRDiiV5CWdSID8lJd+2\ncyX8VGZnvUry7IFitO3YtbLbXHceDCRs5g/AiLIeU8G11Ut4nXzNbWywgV8z\nB+5YSROeggwgKsF2gq8e21/clIR2zZi0MStpZnDBcbchIR1XHqfMAY7ltPb7\nlYJoPkzrY7p49A37iMSUxBQ6XiS0QryTkcwYEHYMRF5BPQG5LaLdRIyJq69v\nkL9wzcJTOYooWiUSkdRuJ73MCo2gPsgEzM3+of9xAItJTJPjdIS9u/ufugrn\n+dwClPRRL15OeiqqiOjx6vHCjQxm6eiG/c4BYwaRZvERKDmdqLVmHIGS7Qft\nF3HbqhpmorxK8QFx5wPM/CrQ2Jd5+R0ANmPCEKksSXoDIUDlaIjCG8Y6DWDv\nnwK1eUNMfmb57fzxnioh6a/UAhh+L5xuTwbo1FrYds1DTxU1VA9QBGrley7F\nHx71qviPhLK+zTHLRARYSF5P9TFfuCGINjNososF5qg+sytuuO7H596ADbcl\n/euz\r\n=UxdF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "b2049010b387ae69abf4e656a415b1affb2e89d7", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^4.0.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.0.1-alpha.2_1607207562604_0.7129609031544031", "host": "s3://npm-registry-packages"}}, "3.13.0-alpha.10": {"name": "less", "version": "3.13.0-alpha.10", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.0-alpha.10", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "c52f49e3255bfbc860282460bda99a450773608d", "tarball": "https://registry.npmjs.org/less/-/less-3.13.0-alpha.10.tgz", "fileCount": 318, "integrity": "sha512-Ia74kTKKD+8FWHpCYd+YNJjr+4f/lFF1lHi9yvSDHG3OX2heArM80XfqFajBAgDE2wLm3YCWp37PSOuZOYHwBg==", "signatures": [{"sig": "MEUCICcQu8BYIe90dcaS9SyPQAF6ZGU3JqZH/Ws+lysapVIeAiEA8mwMnTNFu7VimrrBJtu2B5WM3cZ1j0Sbu2RqZSxtA9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2738830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzu8SCRA9TVsSAnZWagAAJkcP/iQkfWAgzWXIpk3HYivk\ndRNDCOCeuMvi8cgY9hcIu4MAuGGw0Xq7r4ty9z3KF11rsJgJYxWJkaBWpQq/\nm3cvJG87rPjLRx5gLexpMCoa6cmb5FHrXUlQyLr6pHRVDU4AgsbwjpVtau8C\n0gsmK8nSv7uVEvgo+IPA4l45OJ6s3c1VJxW8zIQPVzEq7dBTwac0Jvi64pMv\n9PgKL5Emlp+LFwrKquQHMqi1Q5U8+qES1o6zXy2Cl+F7lkZSFTgquDlbLK8G\n7LdHVJsKTPZk7l/8ovHyLakgaOd3Bphkx2JP/8bIXPWtbskMrqZuPBJmoTIX\nrdsTPkeC52FMSucbck+xR+lw2ukyhEp7WLGQBrJGbWG2/vjZyDSdPO9ZFTyd\nD6Wbm6qaTry9tzzZBD4FSZ2aXR426TBq2Otb+CrPdQINnT0jA3ka6lyBKJ7s\nI9yef2SSf/pBfLeCPHHVkAP1ZVMqb3ZDqcYHSl7fqqcCrRVLWmXC8w8tWPKx\nbqpQrVGOhjrgeBPC6Rx4rJiBjM7MDwyj4ALV5dj0FVgFlLR9Q/p5JST7C3U6\nW1GCIqlBgtVycxDrSezH1xMxc4jxGyh+I5pddolqLz6jmmjsgV0Eot4er5CW\nLg6OPqWwL2nJIiKbrzqlMNEcnO/pH5ht74ckZXNnt9wCtrVdn1B+bsJhUJs2\n8WWg\r\n=VJOE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "b1390a54589f7addae08550288f775eeb18f7061", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.0-alpha.10_1607397137833_0.15432987333835269", "host": "s3://npm-registry-packages"}}, "3.12.1-alpha.12": {"name": "less", "version": "3.12.1-alpha.12", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.12.1-alpha.12", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "9690f49b9622de2200ae22def120a3fa17c8e429", "tarball": "https://registry.npmjs.org/less/-/less-3.12.1-alpha.12.tgz", "fileCount": 318, "integrity": "sha512-VgGSqggObTC8QeTF4Puv/u/gAk3iDV59GvsrRItqOG7efYZ6mMygVMJ5I56zQPUeDxgjnwkvPvTGztWiiZw67A==", "signatures": [{"sig": "MEUCIBaULiLVRGJIA8U8AjcSRoqhTvFr74XNJnrcQUKrrkW7AiEAhVbw0gi+t99Xa/Kg3SfzOoQ5MgacZnf/bLyneH1Tu7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz2QTCRA9TVsSAnZWagAAML8P/3+uZP0rjjnKTgcrf+pY\nxz+fPPWtkPqd81c3nK4zdsmYseN1Y9h3wO5EDysLNwP6sa/k6c5EMFRiMiH4\nyIeybBN5Ok013wSuXVUcHvg1PisnSSPECLEJYZbEffJOKT9yu+QjUO6LX/zf\nNozJ5C260esfBs6/8ftyHLvBtF3thAwHoPGJBPfYaVPvV8Mh5yfJ0xtM4k13\nYXLhNJ4vE05HEre8H0yq8PtZcFizz6vN83RV6jr7RjUKSuEYSuSBggA5qsH9\ncNT5Lr1ZgpRTXJ+tByvgdZvfHR0Y9Y94YWUPw/lqVMvPOESowx/eLmYNsvrt\nlIFM4V8YQecsVhwvBOKROdrYjqIPohmmQvgqCuG+mIJkI0VDQ1KUfP81r6id\n2hm+JPMAUeM1G181oBWk6ULk9kBB7yCjkaCMkRWaImk9AHIyFltShmNt8XWh\no/CaM4uIhg3ABA/GaB0xb+Gm1pbVbEGDljYZY0d28PFlk258bLpcaUOFabhR\nT/Ysl99hq/cK3aXSxWbHuHKrvVyyyU6bu1otPoH5mT3pMkM5kIC2hGbYiirS\nbHj/RKhwuBwLx1pCfjqn1PiBLRf3ESC+8rmnVuCwwNz67kQ3HBFFGTEpGBKG\ngHnXbmHn3H56+tlPOUQB30SLDJ1PiWW9lx2Ds4/sJ1R7H5Cy2Nx7OM9Fw0av\nbzeT\r\n=/jDS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "e8d05c610f6e15b0ae9d49d662767a609236fd49", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.12.1-alpha.12_1607427090783_0.3620052622266128", "host": "s3://npm-registry-packages"}}, "3.13.0-alpha.12": {"name": "less", "version": "3.13.0-alpha.12", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.0-alpha.12", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "9a82c89c83ff8519bbe419ec8d60d610e8fb8941", "tarball": "https://registry.npmjs.org/less/-/less-3.13.0-alpha.12.tgz", "fileCount": 318, "integrity": "sha512-JB5p7lJcbeKUXZSvxr5Oy18ErsyAO1/B44SsOxXJ0Hg64NCWu1EaABBa70a32PmuU+iSmTnzW91VtWKp7BHrdA==", "signatures": [{"sig": "MEYCIQDx118iMrvx7Knt1Yb9bsBlGVWZTQxplCEsvUdAIzp9KAIhAMSPOZ9OSYuwv10qFxkdR0vS7cEqUdvChl4mle4c8UrT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz2S4CRA9TVsSAnZWagAATfAP/ijE3nvDJVPyrK1Uh5XB\nZL4nxvhJWSWpvPHwYMk+TNyaGeSBhDu1XVOkv0hACzlNyFn+Fs6CZ1T0k1Vp\nmcam+N1Sd0cvwU9gWbUZpkjyvq/jNgQchy/ZXl4GU9wsjflD8hYRP0M1G2+q\nFTioHf4d35GWeCS1wp1naIaeVF4LLMaDKKWCjY9RM7Jo4KyaEjqki4pK9Ci2\ncnCKwLRJomN2qs8Wk8Vsg6fcrHoJj+Q/TH51IDo1/1CLayZaOXLkpDJKSoKe\nlhxcWbZfWb4g0fLpPfEKSlSzJPKsJHm44/KDFKBBnx08LhL2BCuHnKlQiIxC\n+MWAARTzP/6M6INRJ8T4Dc6du5oHEgEH5nRkFyDz3vQg+Z8EzYJpApD+7Q1z\nsl9qhOdDAZqXoWnpCBqZqGxIHbnJzijfNuXke8JKerB4CSxnRgvt5Sx+Ij5k\nR+Z2Oys7uknm+Z68lBUrhT1F7+wRRMHha3JVMImbuOhWAJlLvhzhWtNyQuis\nrHhql1yLE+MzhPybHbAdJQlrLAYNuGblgH6h0HczJEwQzDRaV879mTFjH+iV\nf5NsJVTCOSaUPjlsDV0DR+A6pr1c4SMqsBwPF2iNrPo1pXncLnVU6GFZdR/J\ntPAFe2K0Mtw7cBUK70sG3PQnTTPBlbfOvDUgEz0LMnyI22T4qVnzb6NpUg+B\nBPxl\r\n=MKvl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "e8d05c610f6e15b0ae9d49d662767a609236fd49", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.0-alpha.12_1607427256331_0.7474565520657184", "host": "s3://npm-registry-packages"}}, "3.13.0": {"name": "less", "version": "3.13.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.0", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "6a47bb19d97edcf7a53d444b099275dd6b17c85a", "tarball": "https://registry.npmjs.org/less/-/less-3.13.0.tgz", "fileCount": 317, "integrity": "sha512-uPhr9uoSGVKKYVGz0rXcYBK1zjwcIWRGcbnSgNt66XuIZYrYPaQiS+LeUOvqedBwrwdBYYaLqSff5ytGYuT7rA==", "signatures": [{"sig": "MEUCIG5wRrWTWDB1UcXbgktG9PV1hL+b/nmsza912l0qGFm5AiEAoaznWs/lOk8AsR/kmV6M8BkHPk0vutErIVoKaqbjeG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2733652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1CVqCRA9TVsSAnZWagAAu7sP/Rn5GqkHPtMzSi+N61M2\nlQ5FwX+ooW11kUkKg6aySwTfvYMdMMRh4hUZOXlRORwF4vAsfV/+0nl0gaZM\nXMcHfKhTpxZBqvcen5NC8IMlYoFDEmlVsjaQCfZYeHyIgMMGYMP+deBYe8Eg\nx/CCMsiJf+pr9/5R7v+yLpNehrV7UgovyOH6dHQvfGdanSSb/jRIJjfVDQ+C\nXTOjK49C5chXDcGH4S/JWLtBYDDaq/3tq+FqVjdXHNNAzWb4ymovENL95HOw\noiSs/De2/0vppDCj4NLcGDu6H/CHr62lMBbPjgOYo+8j6j/ETR+EaobAEE/e\nQ976bdzm3hKFFFa/LJUnBHoLOci05gtO0Syeg7rrQL7kzu9L0mWy4TjBCTjB\nTpUch5zNWqNotGZotPJUP7OmyX2MyBhJTCXhq88L/C6urDzRZepK2mIodtb0\nnAw8IQBJL1s71lRrBHRdv+GLBS9g9XdX8vJThBPGQ2e8ABwuLmO2daKNVPnc\nEsgzFZ29k/pbZad7KlTMWVuLjOCA/gJmDjMQIGwD255Swmckj7Lef123xHbh\nQceG+Nh5PApkbXTrCk4hX9Hj9re7tNqCspZDq586l+MTv6XxKaccNK2kv6KQ\nitX1i0MaoCFqfoePA4eS0EOx71BHzHC+/+otnLVS7vXAW62i18OUXxO2dgNo\nKJER\r\n=+92M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.0_1607738729784_0.233459613710034", "host": "s3://npm-registry-packages"}}, "3.13.1-next.1": {"name": "less", "version": "3.13.1-next.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.1-next.1", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "96366adc2daa774d75325743aa0124e936074502", "tarball": "https://registry.npmjs.org/less/-/less-3.13.1-next.1.tgz", "fileCount": 318, "integrity": "sha512-vfwpjNVbump4H6MshQw67GwoeU7DBibULeha7MZRqObeXSAmIp/q6nV+eRTH7OCOtmQc5zCUwZcwbmPytHYuJA==", "signatures": [{"sig": "MEUCIDJgCIeLFG8Vh18PIAgcFua+B6oQbdigfBymACEAtEDZAiEAvZUIWjEJb7lf1z6YZFTIoGQyZU18EE5HrD9VVRYmQOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2761176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf254sCRA9TVsSAnZWagAAjscP/2VVhphmoDJ+UgLs5WeY\nq+p9CNYxfqrdlIftc4pkLAfvQoTi9Mky4rfHbSSWOEuqV30ps4NzH1soYBsX\nxXpvkqI65cRNquEyZ7wE9z4vD2XYhVcCav7Jx80L8yPtx3J//tUeb9nmDKhs\ntJ/0w5zAMC8zPCwKc21MvWCEG91PLOJxQvxcpyelTpWUjBFwGv1Nq4yeSF7W\nymhavK8LjzJnEj/rEXqfo4aRruOCg2Sv/xEaHZIJvM9NV5DRCGh1gJCUhSNQ\nYH0fPS/3xSDUdvluou5Xt5J0JXjhintu9OtunJyt33zoaXB+qeaWQKhqKWgl\nIZqQ0v1aHqdqa3efZCQ0UuMYPLhz0gl/DnfPY87OCZn8EOROs2T7b7lbdgi6\nNw6W/yVd4RzgWTfCeQIwVwdbjaOPFQQvq11kpoOpz/xPFpWBTHc2C1uBmmjh\nt9hNubfywfwOmKXyHX7g/AKiV9QpX8mggwlndIusduZJH9RelkfJNSgAoDk+\nY5e+4uQhs2gk+r1pjJmrfxQCGc6jb+xEtlxSUzM0v0Ct6GJH46z5eNNc+M9k\ntyeOyP4qlhOEmb9mprHJrmBKf9QxCc6WQ4PbVi/xSm23R+bIN0wCADLbxe49\nOu/S4Ezs0zyZQgLcPWF9wljwzx7fzVHSKJSJ+v0kjVf9UKSovVo9HGx1tjtU\nwO5s\r\n=MO9u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "458cf99cfe6fb3fab57f2998729b0d1a8dec358a", "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/3.22.1/node@v10.16.3+x64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.1-next.1_1608228395910_0.8294547584594298", "host": "s3://npm-registry-packages"}}, "3.13.0-alpha.2": {"name": "less", "version": "3.13.0-alpha.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.0-alpha.2", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "0f88792ac26fd1efa3c9942d20efa8fbf1e2b6f1", "tarball": "https://registry.npmjs.org/less/-/less-3.13.0-alpha.2.tgz", "fileCount": 317, "integrity": "sha512-mjoxQ2qF0wXA7QXeqYDA29qvOakrDOy6owAauq+CWgjsHzhLdoGpIHLnCiz9+28737jInf4H+hh63BdJHQ6g7g==", "signatures": [{"sig": "MEUCIQDflPg3O67Ny2Bx5AqljIOWpnK8KCSPrOG194Bqkyya7QIgcviCe+93FuyHZMEMVtNvTPZryc906lSNDKDGL/jLFkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf27lrCRA9TVsSAnZWagAAfs4QAKFw2yCMEVUDBHRKTw4W\nssmc+KnfwkS1/wI1NeibM9w4MsSqkenOUEqB+CkvUQ/IapDoySJBP2uC+Q9J\nij1x+1fKBt0EMYisM43ZhzuKahxjXYm8UNd7TQ90jzxwOhhTy6Ybc9pkYJux\npQbYKQbKGGoxwxstfPJ4CQXEbIuWN97dqHXyagrgVdDsgVx+7YeXtQrnCPIa\neQIcMHlGWLVIG/JPekbIfQDS86+6mlVbHUqJNUau6gWlmCjp4XQ4oleInoLE\n6+pitcuUwEI8uck3Az2oLiAmHlu9krPiVHuV1QRBBX4A4gShndCQfP67vOGP\nzc33kk7BqLBMklgbkAmM5BA5gDJBM734vN7emjMGdJUqsnwMvS8QKF/xi88U\nZSxLBUIhaTnKYdcaWHHkqhOz5ajg3PiC1ezYBqrliU/UekfIDmCHNqITr0Pa\nr4AI4VEJKuTO3TOafObGbJ5sxBhplUxbCiTjADJXKzKf03cIbp3qV0ods7DV\n/2z7rNdcnhKAgN2c/cBM+cD+Q9SoyMoA3TMkqEA2g3qJUl7L2bPCThpwDv+x\nQDVPScNgDV3U/suOsh3YCPJmi4o1KeWifkdAq7p01YyeF2kVXCLeNcpB1vRM\n8JITlVb28hk85Kozn3f+o34CSAhjnK2Ia3yFNr/p1TV3rEtOfDxJWK5E2Aah\nW/c8\r\n=EH/o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "copy-anything": "^2.0.1", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.0-alpha.2_1608235370918_0.4620683385668176", "host": "s3://npm-registry-packages"}}, "3.13.0-alpha.3": {"name": "less", "version": "3.13.0-alpha.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.0-alpha.3", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "10e82d5e58342313e9ffa0712fea734a59eb2d61", "tarball": "https://registry.npmjs.org/less/-/less-3.13.0-alpha.3.tgz", "fileCount": 317, "integrity": "sha512-WKf/hVhP0qPtUc+v6AA5kCAHNPLPx/19p4p+xHWCBduYAldDJbzAVUyYlvsWUZ4o/RFjLy99/0Rz4hnuINAEaQ==", "signatures": [{"sig": "MEQCICek88HYrYoAvQAEMAIqRmct9guvVhjKn33J/xXzEzn1AiAYXJVc/acXmUxysUoth2Gha3VBle6ufKDMyE0EyiFGnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf27+UCRA9TVsSAnZWagAAVbsP/ixxL2pn5Yw+wygObFS7\nSgTlA9yl97azCqDJwQsfCtaG0qygU5r5EKRPGMpcWkF1dbpQ1VhAx3Pcxkrs\nPfEn0GAKxHgArW4X3E8ucfff/HYiEG89vKEJYEXO0waqyDBH471EFIuVYZED\nVElL4I6+infIABCRd4Yd4sLwJU+fvsEuOk1HmygOsZppXH45bq7XaImDFuLn\nF1vUmhmkKdu6OiwtUKC1ujnx6mH+4z5O/qFRWVaRMNywxH3JX23caQdyz7uq\nSlQdZ9bIy74ZGzX4BsMnPJbOIrNBsA8Tne+elPFo9aZSccya7lLVAvpIrwkM\n5AvAoHx+6Igo6JTMMkFEdb88KJXbgsYWPgDYwAztsTk/v6H47uV4tZa+xpwQ\nTHr5cQVgWnw50KPFuCg3oi0MqDE0WdTLx4QdnZjru9M77SHTX1djoS5PaycG\nvOS1/CHrBacqO1N1hgQkbmSIexJ4EI1kynjUeEdffBeqOO0EJUuBCDr66dhV\nHWkE/9LyW1FYE8e8O6o0u8oA/mQDXS6M+XMeuow35+dYb1rzw2NKwC+0A2i1\n6v3vUPVkBI3jbegGtYIcBNMiPh9wfNG/V4QEOqvq96n8bwkt5x8nFXOSjulW\nzGmZmPyQ63+TFtgFI+ToKagBGuFsdcTUDiIGclocczGTGWpmVdt27gPz2Z7y\nuFQC\r\n=uWED\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.0-alpha.3_1608236947819_0.9201976158861755", "host": "s3://npm-registry-packages"}}, "3.13.1": {"name": "less", "version": "3.13.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@3.13.1", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "0ebc91d2a0e9c0c6735b83d496b0ab0583077909", "tarball": "https://registry.npmjs.org/less/-/less-3.13.1.tgz", "fileCount": 317, "integrity": "sha512-SwA1aQXGUvp+P5XdZslUOhhLnClSLIjWvJhmd+Vgib5BFIr9lMNlQwmwUNOjXThF/A0x+MCYYPeWEfeWiLRnTw==", "signatures": [{"sig": "MEYCIQDVSqkwWDZvS3vvRFe4lJzgTuUv5fFoLJ/8VrdsbjS9pQIhAPmLlIowocY7gi+bKvGwijPFcY8/4jDz81b2Rs9lXcDg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2751348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3M/7CRA9TVsSAnZWagAAZz0P/2arWodzip68x4Wmzljg\nAE7gwI1TBvAIJSn6sFTxaOn71r/4N/gfa6SsTFrOVu3Rw/Z2lZTD3q0J6I35\nHdOEBHk7ztJuW7hIhOnNz/KO0OF1hgy8Me2UEZ4MbKXd7EEK9YdFtxOA4ZQk\n3+QddOCGBJBRp2GeNlxo3sOp9KrQOu26NZTQbfivxAmp2U1hKFV5862yKgh+\neylbiloxiBFrxiJO2m4pW9GCMEUyKXjGnL/3s1W9c0UQjP8Rc7RXbcgwcjgV\n1XIylnP53JCXhKqA5r4gKsPsUF3eKTSaTJ4OaysRWsjqlvxcBvIDrflkCV0c\n5ND3l1pitR50BQs2WnvlQbhvXgMebQiXErlny2HCg48IfyLtTDXCcUP1zx2d\n1CwmxXpufQa/DsfmMa8Y+ZOyjWfDwwmqKYYAXLNri8Go45DAAkNh8vdudgiz\nclpxIr9MEW2kgWChx6EiMFONPYO01Vb2ejR8q/OJJ7cF6PM3FbmOzkdhQEsI\nnMVD7caesOp78TXZjLufzgw8awDJCuQFEJhkqjFqu2H64BhLtdse95Z1cTpY\nI7iZayz5hQX26PJuFrb8YPFBjGkwF5JrWW69KHcBobjhzCiVXnOq351JSy7p\n90Bc/6J/fIv6D9kU+pLKa58n8ESJVcPsrq0uTXztK6X6PUnPDoJm64nuOTVo\nmfEq\r\n=RuC7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "native-request": "^1.0.5"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "@less/test-data": "^3.13.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^3.13.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_3.13.1_1608306682952_0.6268203311751019", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "less", "version": "4.0.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.0.0", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "./bin/lessc"}, "dist": {"shasum": "d238cc25576c1f722794dbca4ac82e5e3c7e9e65", "tarball": "https://registry.npmjs.org/less/-/less-4.0.0.tgz", "fileCount": 317, "integrity": "sha512-av1eEa2D0xZfF7fjLJS/Dld7zAYSLU7EOEJvuOELeaNI3i6L/81AdjbK5/pytaRkBwi7ZEa0433IDvMLskKCOw==", "signatures": [{"sig": "MEUCIFpFQSzN4Njt+yotumAEfDGu8os/uOYm+v/c4kRIskLZAiEAx1ZOPWP9cEUz6HK0xTn5qopb/52Hxj4d1oAMpc4/CCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2768707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3O/XCRA9TVsSAnZWagAAU2wP/1oUTdTKeR4fCHnB484+\ndNq4+YL7w3TGnflvgIItfaSPHybKX6okfcjCqLKYyj7QbbAdbwyKOc39FexZ\ne4QpY4Ky+38PBvc9D/plQldwQdyXAqkkqjVIC5nfuJd1Zw0QlXwiWYHLgQ+h\n8tVksihaUG6N3M5uq57+VWAip/mKgkRaZzm5ojcqHYqmCRuDcDt/w5iAGDWS\n6N6hyjd+Cueo2U/hGcaXUK2LGGVBmKF9gOH/jNGQit6SecjAb0WF0sAOJZz3\nMRxjVyNij8zQ0Rvvpzz0pYIRWW4/tcFdHucLKS2r85N3cxyIZL3zeFGdgAta\nRRDyBNPt0fIWvdOxSCUw/ycstepwBKj7zfGVF+kVNx9JZdLR70b+4K70VxoX\nMjvw5kzwlUtlvyO5V3J/F4d6KRXMw+EiJsFQ1+3UOHr95qF/TwaiEThP316r\n4kpQ3HGdJ4Nqb9NGKFcnaA1tEuUQ+GaVMrOM40b/1nyJWC9FOp5Igh2U/hGF\nABRvXuk0NvdeDP8HknUsoPgYs8rkYE5V/xkHLkqsH2wLI0qYdJDLh6lxOXJE\n0CQhEv9u1R0R/96VI1fdJe2iIEFpZsGWtiasNLcKH+gySYSQovsbdgc7I/7s\njtpxcGCyz1Oi7L0KFW4jS4H2Hd1ZrvdLELnCuJx8AxdeGwDC4nhCNM2/eU56\nLN9P\r\n=3B8K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.16.3", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "native-request": "^1.0.5", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^1.17.0", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^3.6.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "@less/test-data": "^4.0.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "rollup-plugin-commonjs": "^10.0.1", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.24.3", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "native-request": "^1.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.0.0_1608314838795_0.20071932900747735", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "less", "version": "4.1.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.1.0", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "a12708d1951239db1c9d7eaa405f1ebac9a75b8d", "tarball": "https://registry.npmjs.org/less/-/less-4.1.0.tgz", "fileCount": 317, "integrity": "sha512-w1Ag/f34g7LwtQ/sMVSGWIyZx+gG9ZOAEtyxeX1fG75is6BMyC2lD5kG+1RueX7PkAvlQBm2Lf2aN2j0JbVr2A==", "signatures": [{"sig": "MEYCIQDyOXs+fNGkaXXJXuWN1cLx0gyChkETBYwq0fu0ITDrngIhAMJnvom8OJKaNbwI3r96sCT2zp7zGgLS4T/YTHRc79oM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2770525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+mlCCRA9TVsSAnZWagAAlLYQAJddycSlnumWi+rR2sVe\nxqgX5Gb7AXwLgb7OHQZCVlgEi0qzCq6WAqrVmVRYGxhLkOgGM12ihGSMXiUs\n5I4xhZuzmpURpc8tGhmIVi5fx1d5Pb6oA9cMABm8JU9rymgtrwyvQ653xvan\nlBA0SI+rYcbJulWf2l48Phsvu6kLHI4mkF5hMsyVwRZHsHKRhvblxeeuG3Ck\n9Wd28waOURGkws23QZCFFUqVB1b1B583K3ykH4XouaO/IfRfYT5LzR7lg8z2\nhaoJrQldv7xm26nyfLgyeW+zfMeGlxPfdBcQyn87YXO0KkttN+0+vIu+grYZ\n7VnVD5e+OXzaq8ibJZhSDEo8BP52rtmKTiO6tzz2ZuwcdB8eQGeCufqobdgT\nYJMIKaJXFErz729TkHExNFmY6pxPiCzcUBcsSOwHxvm6MsGJZ0/MAmGL9LdS\ntOsuWghtSHJsNifvgocwhyD887BRsMIgesMXQW/dD0amfzmkaZluxeRrUA/2\nvNMiy191rM6pQpi9XOuIBsYPS8ULDNCe4fe04fAE3W2/J62yx6P1fpBm028z\nPZpMoEILg/lOmAHUXeiTQo8PwKZg5Ap1tgJmcD88PO0Zvnh+cl9k9wwQMljy\naJsoUhb6CK//u1wufTymuahVgiIWiYk75YGWl6y4dGcGNWd1s4NahxhTdM/i\nQv6F\r\n=yjgZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.22.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "needle": "^2.5.2", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^2.34.1", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^4.1.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "@less/test-data": "^4.1.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^2.5.2", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.1.0_1610246466282_0.38356357054671464", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "less", "version": "4.1.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.1.1", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "15bf253a9939791dc690888c3ff424f3e6c7edba", "tarball": "https://registry.npmjs.org/less/-/less-4.1.1.tgz", "fileCount": 317, "integrity": "sha512-w09o8tZFPThBscl5d0Ggp3RcrKIouBoQscnOMgFH3n5V3kN/CXGHNfCkRPtxJk6nKryDXaV9aHLK55RXuH4sAw==", "signatures": [{"sig": "MEQCIFkaWinzpkeqg1BlgADla0d7rYl9npUxQhJ7U1F2OX7jAiAopSQB9Q1VEhDl+tj2Cm3OYIfDfJ6Frbx1C6DL0+frZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2771097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFin+CRA9TVsSAnZWagAAtzkQAJcrxspq7HPcDUuhaPYl\nF6KQgynu2ODdK1nONYf5bnBQh5UbvGetTP4A5iL9D0rq/qLb+BZh3lIXTcS+\n/q5AV1NkwCZqBSKQoV8NVAKilAKjB7LhCi0LNLYuUcfyO/bwxTONfUzhEJMk\n9pATltCuLsrFntjdOTF2fm0EbL5/6AXJrv5dGt5ht5d2Le2kPF7Ng5/uTSn7\nm5uHCplxJ0Ft+AKwbmlotuHiAYSREtoPhW03S5F7PYQQBYDDGrAJ0v49sM4G\n2hEBzJRsze55iVs5JPVIn9NVRIx6YoluhFjNJfy11lWWpDzQIkrGadeMsl8Y\n6BT+xcWrca8SNyXitDpcaYlxUWwCTDFB0bALchK6eovH+Ubj6ZGgwcmwbrKy\nM25KCRSwr2rz4VSNuq/dSiHEEf4/i+yG4KMh3TBH/bUA1FEwqICVf+40BDMn\nNDsDbYXWRy545S6+JmKyxhneN6RgVrBdyZ1bQOJhU0t5PpLn2SMiaZLCZR22\n+8ME0t0ZfIP6QnAXL/NEZxwYKoA8OeggBM2dgzosiyRSBvArhnLybJ2Q3+8H\n23wpJdOfjltMBB/Gsnm4ICuF/x2pAeI24fH0xZJ9cM2M2WduW/BCg/gbTGxg\no31U923nBdr7K4kyhVYx/gIHJ8ztu9LqBuGMxMnRMOLxFd11OL95RXyTDoSz\nzwxA\r\n=Gqxw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "10.22.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^1.10.0", "needle": "^2.5.2", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^6.8.0", "globby": "^10.0.1", "rollup": "^2.34.1", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^8.4.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^4.1.3", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^21.1.0", "@less/test-data": "^4.1.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^3.3.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^2.5.2", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.1.1_1612065277561_0.8750537617279655", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "less", "version": "4.1.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.1.2", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "6099ee584999750c2624b65f80145f8674e4b4b0", "tarball": "https://registry.npmjs.org/less/-/less-4.1.2.tgz", "fileCount": 317, "integrity": "sha512-EoQp/Et7OSOVu0aJknJOtlXZsnr8XE8KwuzTHOLeVSEx8pVWUICc8Q0VYRHgzyjX78nMEyC/oztWFbgyhtNfDA==", "signatures": [{"sig": "MEUCIQCAPGiwz8C9IvFgiJzQ3tHhFwOLwDHESyBW5g8naa5PXAIgCXBR58Vv3rZB8660uX7HK5N8UopZbUuGJcb6nFJQvMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2776559}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "12.22.4", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^2.3.0", "needle": "^2.5.2", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^7.29.0", "globby": "^10.0.1", "rollup": "^2.52.2", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^9.1.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "cross-env": "^7.0.3", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^4.3.4", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^23.0.0", "@less/test-data": "^4.1.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^4.28.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^2.5.2", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.1.2_1633303087124_0.3487690614287575", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "less", "version": "4.1.3", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.1.3", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "matthew-dean2", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "ca<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "175be9ddcbf9b250173e0a00b4d6920a5b770246", "tarball": "https://registry.npmjs.org/less/-/less-4.1.3.tgz", "fileCount": 317, "integrity": "sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==", "signatures": [{"sig": "MEUCIQCoTWs0FfxRqbiy3yot/HwfI5UnakcUT34JoiuT23HqVwIgP7x0ayOzmEVp2DFvDTu9pi0IVqEANzWDKQFqXegOwjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2782784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioPi/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxMQ//fFs8UKQkMKKoWk5njMqbFqY+S25xDJY5BQOb2+ItNYC93cMK\r\nCBTVsSDZ0TDbHbtnLolV/kMTwniOD61hrurSHDxjItGm5Uz8e28pyKAGp42A\r\n0KQ5bMr4JhWucptO73levKmTCC7G5Gi9mxQ5Cjcw9dEs0LzKchN7KX/kIJSs\r\nNxyzXauiHm2rcSoRR+kB00bZUBke44B79wLUBIRE7G6LpreKHHIGZkaVqRg7\r\n06DtK/VyHYna2bCZzkBrT4onWRN/WIubiu6+8GvlBMYwpBkyuo2fm9Ab0xiq\r\nHXeC9Gr/bEdabYoKlXgCCyvhg9zYzMFgRqjba/+XV49fNc1mBvb+UmmEAg2E\r\n9S3ARZm6lLYiHbAporGv8AvubJtGuvIcPP4ix/KZ7ewPH61wwDhl6hJRcDdl\r\nRHkQA6YVD00D5Qx+xcWS8aH+WokBimHQeF/eLv1LhvI5+OjrurC83QL97+Pa\r\nYRrWXquXQF6oJnnAnU9z36ifRTyDSdLzvBXfa4igu/XGl5JBNjAm1x3c2CeF\r\n+WBCifM/MAOZ20TDUn84ek8fhE7IViv9xyjMyAwNUqxyBXlF9BDVoTblafz5\r\nMzPXRxFc1Xl2Rylfz2NQmWwV4G+LIogCvOFTDQS4FCCQ+/lkKSFp/LSOHrKX\r\n3pt6apwCuLuEZFNH4BDzT7JNs56feicVzS4=\r\n=cQPX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"dev": "tsc -p tsconfig.json -w", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.json", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "14.19.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^2.3.0", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^7.29.0", "globby": "^10.0.1", "rollup": "^2.52.2", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^9.1.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "cross-env": "^7.0.3", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^4.3.4", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^23.0.0", "@less/test-data": "^4.1.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^2.0.3", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^4.28.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.1.3_1654716607254_0.4279865668758507", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "less", "version": "4.2.0", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.2.0", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "cbefbfaa14a4cd388e2099b2b51f956e1465c450", "tarball": "https://registry.npmjs.org/less/-/less-4.2.0.tgz", "fileCount": 328, "integrity": "sha512-P3b3HJDBtSzsXUl0im2L7gTO5Ubg8mEN6G8qoTS77iXxXX4Hvu4Qj540PZDvQ8V6DmX6iXo98k7Md0Cm1PrLaA==", "signatures": [{"sig": "MEUCIGGzbnDfEnux0fLT7bMnteU/QFYG/0HA0hiin09RAky1AiEAipIbenf2XMzabY38IWWozlG3hbL1vAzRuAapgiXZF1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2835245}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "scripts": {"dev": "tsc -p tsconfig.build.json -w", "lint": "eslint '**/*.{ts,js}'", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.build.json", "lint:fix": "eslint '**/*.{ts,js}' --fix", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "lerna/7.1.4/node@v18.13.0+arm64 (darwin)", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "18.13.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^2.3.0", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^7.29.0", "globby": "^10.0.1", "rollup": "^2.52.2", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^9.1.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "cross-env": "^7.0.3", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^4.3.4", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^23.0.0", "@less/test-data": "^4.2.0", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^4.0.0", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^4.28.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.2.0_1691255895467_0.8835848059926328", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "less", "version": "4.2.1", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.2.1", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "fe4c9848525ab44614c0cf2c00abd8d031bb619a", "tarball": "https://registry.npmjs.org/less/-/less-4.2.1.tgz", "fileCount": 330, "integrity": "sha512-CasaJidTIhWmjcqv0Uj5vccMI7pJgfD9lMkKtlnTHAdJdYK/7l8pM9tumLyJ0zhbD4KJLo/YvTj+xznQd5NBhg==", "signatures": [{"sig": "MEUCICqRiApA0Pi6oJY+6dbzY01sTWmSE2niODqgbqDSkrSlAiEAgdfWNJfqqPaNIG6nSnW0sQBcO81caD/3SdWyjQ8IgX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2856343}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "scripts": {"dev": "tsc -p tsconfig.build.json -w", "lint": "eslint '**/*.{ts,js}'", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.build.json", "lint:fix": "eslint '**/*.{ts,js}' --fix", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "18.13.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^2.3.0", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^7.29.0", "globby": "^10.0.1", "rollup": "^2.52.2", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^10.9.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "cross-env": "^7.0.3", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "time-grunt": "^1.3.0", "typescript": "^4.3.4", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^23.0.0", "@less/test-data": "^4.2.1", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.5.1", "mocha-headless-chrome": "^4.0.0", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^4.28.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.2.1_1732554385163_0.24856165099652205", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "less", "version": "4.2.2", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "less@4.2.2", "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "contributors": [{"name": "The Core Less Team"}], "homepage": "http://lesscss.org", "bugs": {"url": "https://github.com/less/less.js/issues"}, "bin": {"lessc": "bin/lessc"}, "dist": {"shasum": "4b59ede113933b58ab152190edf9180fc36846d8", "tarball": "https://registry.npmjs.org/less/-/less-4.2.2.tgz", "fileCount": 331, "integrity": "sha512-tkuLHQlvWUTeQ3doAqnHbNn8T6WX1KA8yvbKG9x4VtKtIjHsVKQZCH11zRgAfbDAXC2UNIg/K9BYAAcEzUIrNg==", "signatures": [{"sig": "MEQCIB8RafHmstDNAuJhXiZZUe7z8Hma9YAxVR65E7y8m1l5AiBbkkjAHIz4RaouWqmA/xtcprmg7JDFhEJiPu+W7p0xUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2877698}, "main": "index", "master": {"raw": "https://raw.githubusercontent.com/less/less.js/master/", "url": "https://github.com/less/less.js/blob/master/"}, "module": "./lib/less-node/index", "browser": "./dist/less.js", "engines": {"node": ">=6"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "scripts": {"dev": "tsc -p tsconfig.build.json -w", "lint": "eslint '**/*.{ts,js}'", "test": "grunt test", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "grunt": "grunt", "compile": "tsc -p tsconfig.build.json", "lint:fix": "eslint '**/*.{ts,js}' --fix", "copy:root": "shx cp -rf ./dist ../../", "prepublishOnly": "grunt dist"}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "rawcurrent": "https://raw.github.com/less/less.js/v", "repository": {"url": "git+https://github.com/less/less.js.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Leaner CSS", "directories": {"test": "./test"}, "_nodeVersion": "18.13.0", "dependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "tslib": "^2.3.0", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2", "copy-anything": "^2.0.1", "parse-node-version": "^1.0.1"}, "sourcearchive": "https://github.com/less/less.js/archive/v", "_hasShrinkwrap": false, "devDependencies": {"shx": "^0.3.2", "chai": "^4.2.0", "diff": "^3.2.0", "nock": "^11.8.2", "phin": "^2.2.3", "benny": "^3.6.12", "grunt": "^1.0.4", "mocha": "^6.2.1", "uikit": "2.27.4", "eslint": "^7.29.0", "globby": "^10.0.1", "rollup": "^2.52.2", "semver": "^6.3.0", "git-rev": "^0.2.1", "promise": "^7.1.1", "resolve": "^1.17.0", "ts-node": "^10.9.1", "fs-extra": "^8.1.0", "minimist": "^1.2.0", "cross-env": "^7.0.3", "grunt-cli": "^1.3.2", "jit-grunt": "^0.10.0", "read-glob": "^3.0.0", "playwright": "^1.49.0", "time-grunt": "^1.3.0", "typescript": "^4.3.4", "grunt-shell": "^1.3.0", "npm-run-all": "^4.1.5", "grunt-eslint": "^23.0.0", "@less/test-data": "^4.2.2", "grunt-saucelabs": "^9.0.1", "performance-now": "^0.2.0", "html-template-tag": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "bootstrap-less-port": "0.3.0", "grunt-contrib-clean": "^1.0.0", "rollup-plugin-terser": "^5.1.1", "grunt-contrib-connect": "^1.0.2", "less-plugin-clean-css": "^1.6.0", "less-plugin-autoprefix": "^1.5.1", "@rollup/plugin-commonjs": "^17.0.0", "mocha-teamcity-reporter": "^3.0.0", "@less/test-import-module": "^4.0.0", "@typescript-eslint/parser": "^4.28.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0"}, "optionalDependencies": {"mime": "^1.4.1", "errno": "^0.1.1", "needle": "^3.1.0", "make-dir": "^2.1.0", "image-size": "~0.5.0", "source-map": "~0.6.0", "graceful-fs": "^4.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/less_4.2.2_1737312419368_0.020686435163520978", "host": "s3://npm-registry-packages-npm-production"}}, "4.3.0": {"name": "less", "version": "4.3.0", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "The Core Less Team"}], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=14"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.0.0", "@typescript-eslint/eslint-plugin": "^4.28.0", "@typescript-eslint/parser": "^4.28.0", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "cross-env": "^7.0.3", "diff": "^3.2.0", "eslint": "^7.29.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^23.0.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.6.0", "minimist": "^1.2.0", "mocha": "^6.2.1", "playwright": "1.50.1", "mocha-teamcity-reporter": "^3.0.0", "nock": "^11.8.2", "npm-run-all": "^4.1.5", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "resolve": "^1.17.0", "rollup": "^2.52.2", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.29.0", "semver": "^6.3.0", "shx": "^0.3.2", "time-grunt": "^1.3.0", "ts-node": "^10.9.1", "typescript": "^4.3.4", "uikit": "2.27.4", "@less/test-data": "4.3.0", "@less/test-import-module": "4.0.0"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0", "errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}, "gitHead": "1df9072ee9ebdadc791bf35dfb1dbc3ef9f1948f", "scripts": {"test": "grunt test", "grunt": "grunt", "lint": "eslint '**/*.{ts,js}'", "lint:fix": "eslint '**/*.{ts,js}' --fix", "build": "npm-run-all clean compile", "clean": "shx rm -rf ./lib tsconfig.tsbuildinfo", "compile": "tsc -p tsconfig.build.json", "dev": "tsc -p tsconfig.build.json -w"}, "_id": "less@4.3.0", "_integrity": "sha512-X9RyH9fvemArzfdP8Pi3irr7lor2Ok4rOttDXBhlwDg+wKQsXOXgHWduAJE1EsF7JJx0w0bcO6BC6tCKKYnXKA==", "_resolved": "/private/var/folders/fp/sdnhgkpn4c12nbb913bxnpym0000gn/T/2b785adbaa2d8929eaa4758170138074/less-4.3.0.tgz", "_from": "file:less-4.3.0.tgz", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.4", "dist": {"integrity": "sha512-X9RyH9fvemArzfdP8Pi3irr7lor2Ok4rOttDXBhlwDg+wKQsXOXgHWduAJE1EsF7JJx0w0bcO6BC6tCKKYnXKA==", "shasum": "ef0cfc260a9ca8079ed8d0e3512bda8a12c82f2a", "tarball": "https://registry.npmjs.org/less/-/less-4.3.0.tgz", "fileCount": 332, "unpackedSize": 2940538, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHHpH8n18rTXElEmJZ75fzuPTTUi4g98xQebeUHidhq2AiB3lUu1/SHPEZ8Kn7Z+mq8ObHmqSXFNaX7SldvWetJYSw=="}]}, "_npmUser": {"name": "matthew-dean", "email": "<EMAIL>"}, "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/less_4.3.0_1743864741811_0.058989322317403836"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-01-23T22:28:07.815Z", "modified": "2025-04-05T14:52:22.215Z", "1.0.18": "2011-01-23T22:28:07.815Z", "1.0.21": "2011-01-23T22:28:07.815Z", "1.0.19": "2011-01-23T22:28:07.815Z", "1.0.14": "2011-01-23T22:28:07.815Z", "1.0.10": "2011-01-23T22:28:07.815Z", "1.0.11": "2011-01-23T22:28:07.815Z", "1.0.36": "2011-01-23T22:28:07.815Z", "1.0.32": "2011-01-23T22:28:07.815Z", "1.0.40": "2011-01-23T22:28:07.816Z", "1.0.41": "2011-01-23T22:28:07.816Z", "1.0.5": "2011-01-23T22:28:07.816Z", "1.0.44": "2011-05-10T14:05:51.397Z", "1.1.0": "2011-05-11T19:49:37.162Z", "1.1.1": "2011-05-18T00:54:06.341Z", "1.1.2": "2011-05-24T20:46:53.915Z", "1.1.4": "2011-07-19T23:10:19.340Z", "1.1.5": "2011-11-14T11:02:34.213Z", "1.1.6": "2011-12-10T13:20:53.491Z", "1.2.0": "2012-01-10T22:08:15.843Z", "1.2.1": "2012-01-23T10:40:28.587Z", "1.2.2": "2012-02-11T17:18:47.901Z", "1.3.0": "2012-03-10T19:29:45.907Z", "1.3.1": "2012-10-17T19:35:00.101Z", "1.3.2": "2012-12-28T22:36:23.199Z", "1.3.3": "2012-12-30T09:43:43.761Z", "1.4.0-b1": "2013-03-08T08:18:54.802Z", "1.4.0-b2": "2013-03-18T12:28:36.352Z", "1.4.0-b3": "2013-04-30T14:43:44.435Z", "1.4.0-b4": "2013-05-04T06:36:10.635Z", "1.4.0": "2013-06-05T20:19:38.635Z", "1.4.1": "2013-07-05T05:28:15.796Z", "1.4.2": "2013-07-20T20:20:54.906Z", "1.5.0-b1": "2013-09-03T19:42:41.174Z", "1.5.0-b2": "2013-09-09T21:34:20.103Z", "1.5.0-b3": "2013-09-17T05:16:45.721Z", "1.5.0-b4": "2013-10-04T06:13:19.287Z", "1.5.0": "2013-10-21T13:22:02.415Z", "1.5.1": "2013-11-17T17:11:59.226Z", "1.6.0": "2014-01-01T17:36:53.002Z", "1.6.1": "2014-01-12T12:00:41.207Z", "1.6.2": "2014-02-02T18:38:31.668Z", "1.6.3": "2014-02-08T16:53:31.138Z", "1.7.0": "2014-02-27T20:17:32.652Z", "1.7.1": "2014-06-08T16:27:25.288Z", "1.7.2": "2014-06-19T05:21:42.627Z", "1.7.3": "2014-06-22T15:14:19.211Z", "1.7.4": "2014-07-27T20:05:55.367Z", "1.7.5": "2014-09-03T07:05:37.254Z", "2.0.0-b1": "2014-10-19T20:58:35.331Z", "2.0.0-b2": "2014-10-26T12:19:34.079Z", "2.0.0-b3": "2014-11-01T17:43:51.023Z", "2.0.0": "2014-11-09T14:31:21.045Z", "2.1.0": "2014-11-23T11:16:09.502Z", "2.1.1": "2014-11-27T06:17:05.282Z", "2.1.2": "2014-12-20T14:53:59.229Z", "2.2.0": "2015-01-04T11:55:39.961Z", "2.3.0": "2015-01-27T00:24:02.877Z", "2.3.1": "2015-01-28T17:32:57.582Z", "2.4.0": "2015-02-08T11:47:53.977Z", "2.5.0": "2015-04-03T08:55:25.437Z", "2.5.1": "2015-05-21T11:30:14.931Z", "2.5.2": "2015-09-24T19:23:37.098Z", "2.5.3": "2015-09-25T11:50:51.988Z", "2.6.0": "2016-01-29T21:06:12.560Z", "2.6.1": "2016-03-04T16:39:38.745Z", "2.7.0": "2016-05-08T01:35:24.988Z", "2.7.1": "2016-05-09T20:38:49.919Z", "3.0.0-pre.1": "2016-07-13T19:04:21.870Z", "3.0.0-pre.2": "2016-07-14T19:29:33.859Z", "3.0.0-pre.3": "2016-07-18T21:30:25.637Z", "3.0.0-pre.4": "2016-10-21T23:40:32.107Z", "3.0.0-alpha.1": "2017-01-01T21:02:55.313Z", "2.7.2": "2017-01-05T01:58:13.133Z", "3.0.0-alpha.2": "2017-01-11T01:50:04.774Z", "3.0.0-alpha.3": "2017-10-09T01:34:54.921Z", "2.7.3": "2017-10-24T02:43:40.462Z", "3.0.0-alpha.4": "2017-10-24T03:09:24.873Z", "3.0.0-RC.1": "2018-02-04T18:15:19.366Z", "3.0.0-RC.2": "2018-02-11T01:06:56.808Z", "3.0.0": "2018-02-11T05:42:39.191Z", "3.0.1": "2018-02-15T00:40:37.924Z", "3.0.2": "2018-04-21T18:25:44.432Z", "3.0.4": "2018-05-07T01:53:09.069Z", "3.5.0-beta": "2018-06-25T03:17:04.532Z", "3.5.0-beta.2": "2018-06-27T15:37:28.967Z", "3.5.0-beta.3": "2018-06-30T07:42:24.308Z", "3.5.0-beta.4": "2018-06-30T16:38:54.083Z", "3.5.0-beta.5": "2018-07-02T03:06:21.282Z", "3.5.0-beta.6": "2018-07-03T03:27:20.181Z", "3.5.0-beta.7": "2018-07-04T07:19:00.240Z", "3.5.0": "2018-07-05T14:35:23.334Z", "3.5.1": "2018-07-05T19:01:52.723Z", "3.5.2": "2018-07-06T02:58:54.774Z", "3.5.3": "2018-07-06T15:56:30.014Z", "3.6.0": "2018-07-10T14:22:34.816Z", "3.7.0": "2018-07-11T06:27:16.322Z", "3.7.1": "2018-07-11T22:31:55.521Z", "3.8.0": "2018-07-23T06:56:55.955Z", "3.8.1": "2018-08-08T03:47:58.773Z", "3.9.0": "2018-11-29T06:09:55.074Z", "3.10.0-beta": "2019-08-03T18:04:12.861Z", "3.10.0-beta.2": "2019-08-08T04:32:10.571Z", "3.10.0": "2019-08-17T03:04:31.616Z", "3.10.1": "2019-08-18T01:38:38.561Z", "3.10.2": "2019-08-21T04:37:25.880Z", "3.10.3": "2019-08-23T00:09:24.075Z", "3.11.0": "2020-02-09T22:05:14.114Z", "3.11.1": "2020-02-11T05:58:29.909Z", "3.11.2": "2020-06-01T01:54:27.210Z", "3.11.3": "2020-06-05T18:47:08.418Z", "3.12.0": "2020-07-13T15:18:07.371Z", "3.12.1": "2020-07-16T14:34:23.101Z", "3.12.2": "2020-07-16T16:04:53.105Z", "3.13.1-alpha.1": "2020-09-29T14:13:12.414Z", "3.12.1-alpha.13": "2020-12-05T17:09:30.718Z", "4.0.1-alpha.0": "2020-12-05T17:18:27.775Z", "4.0.1-alpha.2": "2020-12-05T22:32:42.757Z", "3.13.0-alpha.10": "2020-12-08T03:12:18.124Z", "3.12.1-alpha.12": "2020-12-08T11:31:31.020Z", "3.13.0-alpha.12": "2020-12-08T11:34:16.560Z", "3.13.0": "2020-12-12T02:05:29.995Z", "3.13.1-next.1": "2020-12-17T18:06:36.107Z", "3.13.0-alpha.2": "2020-12-17T20:02:51.138Z", "3.13.0-alpha.3": "2020-12-17T20:29:08.078Z", "3.13.1": "2020-12-18T15:51:23.184Z", "4.0.0": "2020-12-18T18:07:18.959Z", "4.1.0": "2021-01-10T02:41:06.442Z", "4.1.1": "2021-01-31T03:54:37.855Z", "4.1.2": "2021-10-03T23:18:07.340Z", "4.1.3": "2022-06-08T19:30:07.532Z", "4.2.0": "2023-08-05T17:18:15.731Z", "4.2.1": "2024-11-25T17:06:25.428Z", "4.2.2": "2025-01-19T18:46:59.651Z", "4.3.0": "2025-04-05T14:52:22.026Z"}, "bugs": {"url": "https://github.com/less/less.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "http://lesscss.org", "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "repository": {"type": "git", "url": "git+https://github.com/less/less.js.git"}, "description": "Leaner CSS", "contributors": [{"name": "The Core Less Team"}], "maintainers": [{"name": "matthew-dean", "email": "<EMAIL>"}, {"name": "cloudhead", "email": "<EMAIL>"}, {"name": "meri", "email": "<EMAIL>"}, {"name": "seven-phases-max", "email": "<EMAIL>"}, {"name": "lukeapage", "email": "<EMAIL>"}], "readme": "# [Less.js](http://lesscss.org)\n\n> The **dynamic** stylesheet language. [http://lesscss.org](http://lesscss.org).\n\nThis is the JavaScript, official, stable version of Less.\n\n\n## Getting Started\n\nAdd Less.js to your project:\n```sh\nnpm install less\n```\n", "readmeFilename": "README.md", "users": {"po": true, "bat": true, "jaa": true, "pid": true, "vab": true, "zsf": true, "cdll": true, "d3ck": true, "dofy": true, "dpkg": true, "gesf": true, "nilz": true, "novo": true, "pcac": true, "pl0x": true, "rdcl": true, "swak": true, "wzbg": true, "aim97": true, "csbun": true, "demod": true, "fokko": true, "haeck": true, "junos": true, "kewin": true, "kosoj": true, "lavir": true, "lgh06": true, "m42am": true, "martl": true, "mvall": true, "octoo": true, "oroce": true, "osben": true, "piuss": true, "sable": true, "stany": true, "tetra": true, "ugarz": true, "vqoph": true, "agplan": true, "alex7r": true, "arttse": true, "awabro": true, "borjes": true, "chaowi": true, "code32": true, "emodus": true, "eneepo": true, "fbnlsr": true, "feyzee": true, "gberto": true, "hakuku": true, "igasho": true, "ikeyan": true, "iliyat": true, "jcdsr3": true, "johnec": true, "kaapex": true, "kabomi": true, "lcsisy": true, "migaky": true, "nanook": true, "neowei": true, "nexume": true, "nystul": true, "potnox": true, "s.well": true, "sajera": true, "snarky": true, "subarb": true, "tedyhy": true, "tim545": true, "toogle": true, "vsn4ik": true, "yeming": true, "yk0505": true, "yukoff": true, "zvikyb": true, "a7madev": true, "acterce": true, "ajumell": true, "alectic": true, "aliemre": true, "atheken": true, "bcawrse": true, "chideat": true, "chudnyi": true, "deftbit": true, "edision": true, "fedeghe": true, "gigante": true, "grantls": true, "hallaji": true, "imchale": true, "itonyyo": true, "jyounce": true, "kontrax": true, "laconty": true, "lixulun": true, "lupideo": true, "mfunkie": true, "moonpyk": true, "mugifly": true, "nimbosa": true, "nournia": true, "nwinant": true, "olian04": true, "quzhi78": true, "razorus": true, "roachhd": true, "satjeet": true, "sirreal": true, "superwf": true, "symblst": true, "takonyc": true, "tellnes": true, "touskar": true, "xeoneux": true, "xgqfrms": true, "yanghcc": true, "ybadr91": true, "yeltsin": true, "zumanex": true, "adnanexy": true, "ahvonenj": true, "alexblbl": true, "antouank": true, "arseny-n": true, "bob.cody": true, "buzz-dee": true, "bwade231": true, "chorrell": true, "clholzin": true, "demopark": true, "dennila2": true, "dissolve": true, "dom21000": true, "draganhr": true, "drveresh": true, "faraoman": true, "forecast": true, "freebird": true, "frezey92": true, "gracheff": true, "grreenzz": true, "gwilison": true, "hytonevi": true, "icecoder": true, "kileyohl": true, "kilpiban": true, "kingcron": true, "krzych93": true, "leejefon": true, "leodutra": true, "madarche": true, "marcoslh": true, "matthewh": true, "mhaidarh": true, "mluberry": true, "mmachine": true, "oncletom": true, "onheiron": true, "panboren": true, "pnevares": true, "rochejul": true, "rokeyzki": true, "shinelin": true, "shinyweb": true, "sprybear": true, "squalrus": true, "sreeram7": true, "stuligan": true, "thinhair": true, "thom_nic": true, "ttian226": true, "vchouhan": true, "yazgazan": true, "yhnavein": true, "anhurtado": true, "asereware": true, "azertypow": true, "benwyse11": true, "brentchow": true, "cannobbio": true, "cilindrox": true, "fgribreau": true, "fstgeorge": true, "gentlecat": true, "igreulich": true, "jimmccabe": true, "josep1992": true, "kingfeast": true, "leahcimic": true, "marcoslhc": true, "markymark": true, "maxwelldu": true, "mindrudan": true, "mr-smiley": true, "nanhualyq": true, "npmmurali": true, "omrilotan": true, "pepedders": true, "pingjiang": true, "rgraves90": true, "rogeriera": true, "ryanoasis": true, "sjonnet19": true, "snowdream": true, "sparkrico": true, "starknode": true, "sternelee": true, "steve3d3d": true, "stretchgz": true, "sunnylost": true, "txredking": true, "valenwave": true, "vonmauser": true, "aditcmarix": true, "annatriant": true, "aquiandres": true, "billysharp": true, "codelegant": true, "danielsd10": true, "degouville": true, "dh19911021": true, "dozierjack": true, "faicalbaki": true, "genediazjr": true, "goodseller": true, "jasonevrtt": true, "julienrbrt": true, "kuzmicheff": true, "langri-sha": true, "leonardorb": true, "lucachaves": true, "milanowicz": true, "morogasper": true, "nornalbion": true, "pillar0514": true, "ramsaybell": true, "rocket0191": true, "sanketss84": true, "sean-oneal": true, "shentengtu": true, "sotosamper": true, "stuart.shi": true, "superlukas": true, "warapitiya": true, "xenohunter": true, "yasirmturk": true, "accerqueira": true, "adamdreszer": true, "alaeddine17": true, "alexey-mish": true, "arleytriana": true, "codeprowong": true, "crusaderltd": true, "davidazullo": true, "diegoperini": true, "ericwbailey": true, "erynellbe32": true, "felipemena1": true, "flumpus-dev": true, "gokaygurcan": true, "grantgeorge": true, "heyimeugene": true, "ianmcburnie": true, "infinitycbs": true, "maximobelen": true, "mcmichaelis": true, "mrwanashraf": true, "mseminatore": true, "nitayneeman": true, "phoenix-xsy": true, "rakeshalhan": true, "rsmccloskey": true, "sessionbean": true, "sevcanalkan": true, "austinkeeley": true, "bittercoffee": true, "blakeredwolf": true, "brentlintner": true, "djx474068077": true, "felixfbecker": true, "happy2deepak": true, "joshdoescode": true, "keeyanajones": true, "matthewbauer": true, "mayurmakhija": true, "nickeltobias": true, "paulkolesnyk": true, "rahsaanbasek": true, "ristostevcev": true, "shekharreddy": true, "tobiasnickel": true, "chinawolf_wyp": true, "filipecarmona": true, "hasithaishere": true, "manojkhannakm": true, "markthethomas": true, "mdedirudianto": true, "michelebroggi": true, "montyanderson": true, "mwebsolutions": true, "renatobalbino": true, "alessandraurso": true, "austinbillings": true, "fvcproductions": true, "maycon_ribeiro": true, "micael-gabenna": true, "michelvdwereld": true, "paulrichards19": true, "shanewholloway": true, "thewhiterabbit": true, "gabrielscindian": true, "melvingruesbeck": true, "seven-phases-max": true, "ys_sidson_aidson": true, "mattthewcbelanger": true, "pendo.praetoriani": true, "adrian.arroyocalle": true}}