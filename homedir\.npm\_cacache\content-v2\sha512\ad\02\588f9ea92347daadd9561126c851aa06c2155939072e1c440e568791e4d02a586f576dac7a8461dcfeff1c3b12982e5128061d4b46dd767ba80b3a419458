{"name": "raw-body", "dist-tags": {"latest": "3.0.0", "next": "3.0.0-beta.1"}, "versions": {"0.0.1": {"name": "raw-body", "version": "0.0.1", "devDependencies": {"mocha": "~1.12"}, "dist": {"shasum": "5fdd13390c80a4ac185423e7b7bd10b5b789adb1", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-0.0.1.tgz", "integrity": "sha512-kol5HI/YtFQc3gHxfMVs9w0PpQmNBFzXJYCWXdXs/c3Whib1jg8G9QmlSkq1Zehuhx9YAseCMCDLs3yW0cKwNA==", "signatures": [{"sig": "MEUCIQD7n0UyWX9jO5XH3LYOnQ0QnC/0zIBOMpu8uvd73RBdYQIgfoxJs95qi2jhURELYNtVg8VPB9UsMMU42L9fb/NoGnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.2": {"name": "raw-body", "version": "0.0.2", "devDependencies": {"mocha": "~1.12"}, "dist": {"shasum": "319164ced50f628676fc0dd6a381cd52041a337c", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-0.0.2.tgz", "integrity": "sha512-qSIaqSIM/EHAG6wjWPXkQHm7NvEMUkowqOCL9nkGCsBCmBaNGt98W2SQTqKh2Y8XORbRKOLFL+MskE5LdYLm8g==", "signatures": [{"sig": "MEYCIQDP4i4AaP8H0p9mGhYmxqu6K4svxC4YXzFuCDn9zQgPogIhAIz8FvzUdOA8+V/HKagSvNFKsKQB69/2+bVRncnSfFT3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.3": {"name": "raw-body", "version": "0.0.3", "devDependencies": {"mocha": "~1.12"}, "dist": {"shasum": "0cb3eb22ced1ca607d32dd8fd94a6eb383f3eb8a", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-0.0.3.tgz", "integrity": "sha512-WjGZWnZVDVr4VX27TAaX7wTnHht4lqz2nX0xRjQFUVxooxYXPY2F7DV6bvlVJ+HSnn9BAhk5qteKL8Y/XnWKqw==", "signatures": [{"sig": "MEUCIALW040PCZtVx3xB903QIfr/s6MQD3//zCbHQnH+gkDGAiEAlqlp47dCgXXii9dUZozYKXAMJQz/k/B2paJXqr0PiRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.0": {"name": "raw-body", "version": "0.1.0", "devDependencies": {"co": "*", "gnode": "*", "mocha": "*"}, "dist": {"shasum": "6526df32068353d5c3e9d09cdbc5efda59b4a479", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-0.1.0.tgz", "integrity": "sha512-PaZu+m+D57BSzWf4FusSMDyVKOjeSB6posoVcVvalPWH0afrDeGlQ3r6XbATA2/6p0fozgKBsYwz4VtIAljdcQ==", "signatures": [{"sig": "MEUCIBGg9FemrFBCT3QbtQ3vh7R06WT4o75pgdxel+NkMfBcAiEAy42chO9KKCAwBTweZzpTzpuejMT3+bqCvyF4PDg53+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "raw-body", "version": "0.1.1", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "*", "gnode": "*", "mocha": "*"}, "dist": {"shasum": "320ec72bea7f602b4ed71c044bc0c88eb1124051", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-0.1.1.tgz", "integrity": "sha512-p/jw00w6rAUHev4jXWtxdzNr8G9UZzlay1RwTNKB/eqd5YtskjDfKz7JrJtwrAOxY46/gsBW5bzD12ntstefMg==", "signatures": [{"sig": "MEYCIQCw6dSU1lNnFagK/P+7SgPqIhbOhv3HSXWFSZxPXvR3tQIhAOjyAvNfTIWhzLayscqL5Ag8FXa/PNcf9a1EUTKmIq1S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "raw-body", "version": "0.2.0", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "*", "gnode": "*", "mocha": "*"}, "dist": {"shasum": "e77884ce593be387f8d36cb97d37c2e2e9a818ae", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-0.2.0.tgz", "integrity": "sha512-TMeT2IxY67Or5kpQ96jNexXdvDj394VogNHT2KCRLjjYDYKrsTm74lpW0g7DvEy3F8qW1+ReOv0dk0caMprHMw==", "signatures": [{"sig": "MEUCIHrp8LrHKkeyBq6Qz5hc5KSLJ66M2G5PWVTVucml+XOgAiEAr0/TQUapQ+aOezIias5/rtkrpPraUFMhzJtpDhhtnUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "raw-body", "version": "1.0.0", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "*", "gnode": "*", "mocha": "*"}, "dist": {"shasum": "a2ebd450b9d2833b73b110064f032b1a8109509f", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.0.0.tgz", "integrity": "sha512-7iUw9ACAfMW/Hzq07DgPcVP17OKZi/LyvuZBKbywJ19UyZ+hr6G3oMP3EScWkjfHi8LHkHArnPGn/lG8js2vbg==", "signatures": [{"sig": "MEUCIHMXwNyGKYFRuX+O1sCJiMJ1OsC2Ti56EM9quL56ZlUrAiEA4FjDTx4r8TWQQKAXjJhgi5vJvqwxPdvOGMH0ZpLVpGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "raw-body", "version": "1.0.1", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "*", "gnode": "*", "mocha": "*"}, "dist": {"shasum": "946c23ce4716180e5bdc94ae402b0d398aeb6c86", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.0.1.tgz", "integrity": "sha512-FPvu7aaNwwHR1cSAcOLayC548Qkj0qcgOdy6vYFCcgnhDHzkhv7Kw3bcHybFqLeo0D/ET1mzhgtjFB1/6gX8Lw==", "signatures": [{"sig": "MEUCIQDIzb7wIR++ZKDOAOUYkNxt59JHPCHElY5KRY24+jRTuQIgbfjvFXnJol8R7BX4gnm/GkgFKBYA7M3pFd94o13zhNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "raw-body", "version": "1.1.0", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "2", "gnode": "~0.0.4", "mocha": "~1.14.0", "request": "~2.27.0", "through": "~2.3.4", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "0a86c4864cc0773ba93ee31d102aa62b61fc818e", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.0.tgz", "integrity": "sha512-omtlPRAWCn4vnhM+03faRDCaUK2K0zhfBV6tRy6ozOTBLKvmq0ZkOP9dTbBovm6+7O57POtzarGycqNKxPhAjA==", "signatures": [{"sig": "MEQCIEvypxBL6kQ2ayS/AZwCkmeZJa8oP+g0X7/HrxBaBGmNAiBfNndmnG8tFHRX5bejaOrmzbsmyoh8v7BBfPnUM3NZDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "raw-body", "version": "1.1.1", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "2", "gnode": "~0.0.4", "mocha": "~1.14.0", "request": "~2.27.0", "through": "~2.3.4", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "915917d78595f7fc4c391c6563aef69b740cb960", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.1.tgz", "integrity": "sha512-H1ZgTPFuVsSpkqf6nzWpOE2kyZWHAcNTxtytIVQmtQdJamLAV75mEzkFSx5jl/ZJWtb2HsMTdzCxSR9mcVq0Qg==", "signatures": [{"sig": "MEQCICnDKAsFh7eeeg9m6L5qiau4AdS38nbrT1RFkinF6xmQAiBJbOZwLxu/r20K2+ysqw8Mj6OeMkjDr2vgyP5Ez+y8Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.2": {"name": "raw-body", "version": "1.1.2", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "2", "gnode": "~0.0.4", "mocha": "~1.14.0", "request": "~2.27.0", "through": "~2.3.4", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "c74b3004dea5defd1696171106ac740ec31d62be", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.2.tgz", "integrity": "sha512-9Vyxam2+QrtmNIc3mFrwazAXOeQdxgFvS3vvkvH02R5YbdsaSqL4N9M93s0znkh0q4cGBk8CbrqOSGkz3BUeDg==", "signatures": [{"sig": "MEUCIAgMWCWS9gRbMe9OA0/mLMjeDkwL9s7Ba/1Epi66puiaAiEA1OpH3X5hzxC7xRZ4FseX3X5024Vi8K66DdzhxYuQOuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.3": {"name": "raw-body", "version": "1.1.3", "dependencies": {"bytes": "~0.2.1"}, "devDependencies": {"co": "3", "gnode": "~0.0.4", "mocha": "^1.14.0", "request": "^2.27.0", "through2": "~0.4.1", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "3d2f91e2449259cc67b8c3ce9f061db5b987935b", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.3.tgz", "integrity": "sha512-NKclC/FmjQN0BQ0LXOimGACNMrnkGa0KFqptDEnwyz4pR5vQT/L4vz4gKhndnFh/Nx0BsKW8ubchby/relf9dg==", "signatures": [{"sig": "MEUCIEeMRiqwGSF1asc1/1CICRtMGrJCUqMYgo+oWoMR/Y/pAiEA99pIwQNXrLPnCRngtJ3BGkJZwtzqHzwP+j7bTCAZNRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.4": {"name": "raw-body", "version": "1.1.4", "dependencies": {"bytes": "~0.3.0"}, "devDependencies": {"co": "3", "gnode": "~0.0.4", "mocha": "^1.14.0", "request": "^2.27.0", "through2": "~0.4.1", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "f0b5624388d031f63da07f870c86cb9ccadcb67d", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.4.tgz", "integrity": "sha512-B<PERSON>wZAZe9kLUnOYUI5P//8Jc7HFoKTDaeLGFuoo9cmPhhzSXvIVovcsbuQT0wWtGBMMwEgME0CRQYRU6yUa5xQA==", "signatures": [{"sig": "MEYCIQDhr/YABRJG2Rt1dFDFkI2s3JYt37EfU5ODmOkjyncwVwIhANdZm6xyYAdMVsApaDds9wABRurAa7SzmpgQeupEQ1BH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.5": {"name": "raw-body", "version": "1.1.5", "dependencies": {"bytes": "1"}, "devDependencies": {"co": "3", "gnode": "~0.0.4", "mocha": "^1.14.0", "request": "^2.27.0", "through2": "~0.4.1", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "a54b735c205f0876d4b2428543ac9555d39eba73", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.5.tgz", "integrity": "sha512-eEuEvpd7ViYnoLPgg7vcaFOYPaCTu2BB3qHcxv0iVwU2+vnbqyLA7RYNmRSPJVqkLW8Q8fWCpOP560ufu450pQ==", "signatures": [{"sig": "MEYCIQC8j8oe+3sQ++hTj6d8ByMeYNRs8HkBP1kVKKT4fFTWkQIhANtYSrHlwWeT2734Y+xtB92hQq8cqUNXo/0uTlii2Dxl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.6": {"name": "raw-body", "version": "1.1.6", "dependencies": {"bytes": "1"}, "devDependencies": {"co": "3", "gnode": "~0.0.4", "mocha": "^1.14.0", "request": "^2.27.0", "through2": "~0.4.1", "assert-tap": "~0.1.4", "readable-stream": "~1.0.17"}, "dist": {"shasum": "98e9df9a7e2df994931b7cdb4b2a6b9694a74f02", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.6.tgz", "integrity": "sha512-XIfWE1upyzaQJ60xmMIY6/pZl7JFBcT59iE4LnXkLOUP4Q7I2SV4waIlH9Oh0VK67qm1IUMZ94V57g2W7V6RpA==", "signatures": [{"sig": "MEQCIBthNXGlIHkxVoaxV2OiTsYLYfPL5QVwziwY7FyL+/ylAiAX1/KZUETWAzRSUanm5YRAu5SMKbZwFMh6y9GQGZYR6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.7": {"name": "raw-body", "version": "1.1.7", "dependencies": {"bytes": "1", "string_decoder": "0.10"}, "devDependencies": {"mocha": "~1.20.1", "request": ">= 2.36.0 < 3", "istanbul": "0.2.10", "through2": "~0.4.1", "readable-stream": "~1.0.17"}, "dist": {"shasum": "1d027c2bfa116acc6623bca8f00016572a87d425", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.1.7.tgz", "integrity": "sha512-WmJJU2e9Y6M5UzTOkHaM7xJGAPQD8PNzx3bAd2+uhZAim6wDk6dAZxPVYLF67XhbR4hmKGh33Lpmh4XWrCH5Mg==", "signatures": [{"sig": "MEQCIExJSwZU4cpgYjhAe3K0fWnGyuqp+poqpuvpqJUejeh6AiB8d9+8bsX1THblldOGHVJh5sMLODpf48QQn4+70Kx0lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.0": {"name": "raw-body", "version": "1.2.0", "dependencies": {"bytes": "1", "iconv-lite": "0.4.2"}, "devDependencies": {"mocha": "~1.20.1", "request": ">= 2.36.0 < 3", "istanbul": "0.2.10", "through2": "~0.4.1", "readable-stream": "~1.0.17"}, "dist": {"shasum": "523e605803f9551a4314268ea6defd2b396c16a4", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.2.0.tgz", "integrity": "sha512-7WamcH0gSYen01EVCAnsKW2fTot7Vu0e5qTego1dsD1NKCyamdWiQ2F0TP772HyHHBjA0lqyb1W8XDiczbUcxg==", "signatures": [{"sig": "MEYCIQDEun64k0cY+Y6YbqLOiL8Wr7ytBcxadky4TV6987OqQgIhAL/tkJgtSd9I3Zp+s6GPiJS08MI6qIGmSVKWMU+9g1L3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.1": {"name": "raw-body", "version": "1.2.1", "dependencies": {"bytes": "1", "iconv-lite": "0.4.3"}, "devDependencies": {"mocha": "~1.20.1", "request": ">= 2.36.0 < 3", "istanbul": "0.2.10", "through2": "~0.5.1", "readable-stream": "~1.0.17"}, "dist": {"shasum": "3ff628df74ee2ad3632a061d3cd19698b1e23d5a", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.2.1.tgz", "integrity": "sha512-A92XHEq0sWkqxWJszPyaETrD4cfsMmotjtI1ruuhyFvkWIaz2HKY+dK8YULnCZ5JXJGL1PpCS+ySd/VzDnONuA==", "signatures": [{"sig": "MEYCIQCHWwgnr2xLQN/scB7XM7xklBW0suV1T65VzDivX/I3gQIhAJEJLiJE2B8/CMUPjtpl9XG/Zxxtmv5Yo3CD8PMh1PF3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.2": {"name": "raw-body", "version": "1.2.2", "dependencies": {"bytes": "1", "iconv-lite": "0.4.3"}, "devDependencies": {"mocha": "~1.20.1", "request": ">= 2.36.0 < 3", "istanbul": "0.2.10", "through2": "~0.5.1", "readable-stream": "~1.0.17"}, "dist": {"shasum": "0c68e1ee28cfed7dba4822234aec6078461cbc1f", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.2.2.tgz", "integrity": "sha512-52kUCLQKKfbzsJtWdlQmrWwhR8WPc8zsCmIDMEygfiEgT3E/AApymJo8eza+zgaLnDxbNRq+U/UXR79s4uX1qw==", "signatures": [{"sig": "MEYCIQD/ree5/nPtZqB8X9cOglH/mTER/9GkSMHFOPS8zEmhyAIhAIaL6nkmQsquFsXehMTvpoOFijcX/VXRBKciAcmbn1CN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.3": {"name": "raw-body", "version": "1.2.3", "dependencies": {"bytes": "1", "iconv-lite": "0.4.4"}, "devDependencies": {"mocha": "~1.20.1", "istanbul": "0.3.0", "through2": "~0.5.1", "readable-stream": "~1.0.17"}, "dist": {"shasum": "af497b1f1bb5ce77e20855ab9244f87eaa9220d6", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.2.3.tgz", "integrity": "sha512-jY+iTXPvadtu8uB21kPYkTkzim6byh2xO1VrxRqXbD3ogrk9GPnUjsSkTEi+By7HuA1QdfSz5wu7NtB4Q9KP6Q==", "signatures": [{"sig": "MEQCIAtxxifIeEplQ2S5FiNKyFEcMvYoB/Otu5qLLA1REbKOAiBDgI0+IazPP50SAl6MKchnIAql4fQGkXx3jZcH6fzX/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.0": {"name": "raw-body", "version": "1.3.0", "dependencies": {"bytes": "1", "iconv-lite": "0.4.4"}, "devDependencies": {"mocha": "~1.20.1", "istanbul": "0.3.0", "through2": "~0.5.1", "readable-stream": "~1.0.17"}, "dist": {"shasum": "978230a156a5548f42eef14de22d0f4f610083d1", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.3.0.tgz", "integrity": "sha512-iuI1bOSi9tEmVCrXq02ZysXatTrhAu+fSo7XOQHhMo4g87dSy9YB2W/9Udwhz0bPpFk4UcoLhjrHgpPbRD3ktA==", "signatures": [{"sig": "MEUCIQDSio+vApdnBZf6kcarQnGUoxqkG+64JNhhLHpwVCs5KAIgDRuYBFvvmkUFq+x8bVZk+LeJUeK75eQLeeilIUMyRD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.1": {"name": "raw-body", "version": "1.3.1", "dependencies": {"bytes": "1", "iconv-lite": "0.4.5"}, "devDependencies": {"mocha": "~2.0.1", "istanbul": "0.3.2", "through2": "0.6.3", "readable-stream": "~1.0.33"}, "dist": {"shasum": "26a1491059086fd121942232d16758cd2817f815", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.3.1.tgz", "integrity": "sha512-x8EmVKh0fEOk/mlAl4P0NsLW6zH90FeXNGiOpFlvd1JPZH/3q4Wcngev7FI5Z5z8pjTn/1or0sAtxvF0558Dew==", "signatures": [{"sig": "MEUCIQCPD7yBUJ3zEZ0StCTYx0Vd3w4c6QK9SOVnb+tJaaFUlQIgYbAzomYF0C93TKgbWM4pmF+3ro6J3vdcAbjQkdIRHNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.2": {"name": "raw-body", "version": "1.3.2", "dependencies": {"bytes": "1.0.0", "iconv-lite": "0.4.6"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "through2": "0.6.3", "readable-stream": "~1.0.33"}, "dist": {"shasum": "0e186f27c5fbfe326d8b3062774804564a0ecf93", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.3.2.tgz", "integrity": "sha512-mo8/xcztEzTWt6oNV48j4fDf+YyLUEGhO+tZ1XUvlya1XV4yR343PtP6a/1ozJmVMUoEMB0YcNpBuF3o5t8csA==", "signatures": [{"sig": "MEUCIQD/aFmYf+j/EwCxZsHBLPAv5ajkUTx3smq7htYH0p5ApgIgCR6EdKFFmlGkYLbWu9UKzx3imuserJRzeqnfHfHbQls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.3": {"name": "raw-body", "version": "1.3.3", "dependencies": {"bytes": "1.0.0", "iconv-lite": "0.4.7"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "through2": "0.6.3", "readable-stream": "~1.0.33"}, "dist": {"shasum": "8841af3f64ad50a351dc77f229118b40c28fa58c", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.3.3.tgz", "integrity": "sha512-Zi2HhcjxHoHWcGfcaFw/8kAeWDp9BPDyiWltUaxtR5h+3byVeNcWskC8WgfyFLYiU/NrGOinMbwQdxFQX/Cztw==", "signatures": [{"sig": "MEYCIQDv1XLwBZ4NNokQtK0GXj+1hB5GFTBEQs9Ozo3a1JsakwIhAOX1g0xzgQ+QF2XapIedVuU98FVd6A/o0MN6PQH5wkpQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.4": {"name": "raw-body", "version": "1.3.4", "dependencies": {"bytes": "1.0.0", "iconv-lite": "0.4.8"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9", "through2": "0.6.5", "readable-stream": "~1.0.33"}, "dist": {"shasum": "ccc7ddfc46b72861cdd5bb433c840b70b6f27f54", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-1.3.4.tgz", "integrity": "sha512-ZC2kBq6BsR0uK441H5SvGH9H+hRpTKVRjvlI6BhLOJrih3oGbGRYdFyyKKESw4/hjzJEr4cZkQgbuU52s63MOQ==", "signatures": [{"sig": "MEUCIF4L0H1aPB3LejYbo+JqXbagQGfTe6EUAJVodDVTIvbbAiEAlYST09LnzuWza4uPzQsUWId/n+EtywWFDlGpQ13vpF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "2.0.0": {"name": "raw-body", "version": "2.0.0", "dependencies": {"bytes": "2.0.1", "iconv-lite": "0.4.8"}, "devDependencies": {"mocha": "~2.2.4", "bluebird": "2.9.25", "istanbul": "0.3.9", "through2": "0.6.5", "readable-stream": "~1.0.33"}, "dist": {"shasum": "86ec5cb5863b82e6a57d3a5b442ddae8563d6dc5", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.0.0.tgz", "integrity": "sha512-o1sR8o4H7bS4DdMowfBh2i2cIXMakO4GsiprSKfDX+wYi69tf/EiZKFtr0tIxIxemGxTR1Yt/LBxSBYLxO781g==", "signatures": [{"sig": "MEQCIFciNX/mlCvAWeM71Ve8vFYdy1KjWAa+/IgSNM4eE4XpAiBiQ1PWJp2BCg+L7x39vW22YdsHw2sHCp3ePL7xh3uogw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "2.0.1": {"name": "raw-body", "version": "2.0.1", "dependencies": {"bytes": "2.0.1", "iconv-lite": "0.4.8"}, "devDependencies": {"mocha": "~2.2.4", "bluebird": "2.9.25", "istanbul": "0.3.9", "through2": "0.6.5", "readable-stream": "~1.0.33"}, "dist": {"shasum": "2b70a3ffd1681c0521bae73454e0ccbc785d378e", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.0.1.tgz", "integrity": "sha512-pSoQm7SV9fJ2xLZdnvAQi9besPuKVbgSUy+zKV404beic4HPN/ge7aaaBc0RnAGRliLY1zkqilcdXuZJSiUgWg==", "signatures": [{"sig": "MEYCIQDQ8hbLLdeIdtzfUlfhnx9N5pvNh9zUAu+naqYffFm99wIhALR5aofuu8luIkL9jRKl7nJuRNDUUHXuV6WKdvu5fQSw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.0.2": {"name": "raw-body", "version": "2.0.2", "dependencies": {"bytes": "2.1.0", "iconv-lite": "0.4.8"}, "devDependencies": {"mocha": "2.2.5", "bluebird": "2.9.25", "istanbul": "0.3.9", "through2": "0.6.5", "readable-stream": "~1.0.33"}, "dist": {"shasum": "a2c2f98c8531cee99c63d8d238b7de97bb659fca", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.0.2.tgz", "integrity": "sha512-Y<PERSON>rjFuWQDQtHaA3ACQVKS90Kz0YD5eIe92Uhy9s19LRKAEemlgYXRxmUr4Il0h/035DTPo23pDopJWlIsH100g==", "signatures": [{"sig": "MEYCIQCbED0HE3r92Batk0IKhpcz3KlngfKytFbq3H+e7yFdFQIhAKgT8KA3OraAAfwWCwWOpEaKvdBIu9Ez8VVdHnPQZhKS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.0": {"name": "raw-body", "version": "2.1.0", "dependencies": {"bytes": "2.1.0", "iconv-lite": "0.4.10"}, "devDependencies": {"mocha": "2.2.5", "bluebird": "2.9.26", "istanbul": "0.3.9", "through2": "0.6.5", "readable-stream": "~1.0.33"}, "dist": {"shasum": "8091f844de4380cbd2a7ef457d57091161d4af18", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.0.tgz", "integrity": "sha512-jM2Uz/5/8VtS7rfV9NPZibEqLjKfZl9acq8Us6YjW/ZQyCZOdVVcO0CTGCzMCCD5833m9ett7UBBYQ+8T/2YBg==", "signatures": [{"sig": "MEYCIQCQBPWvzyg6qrOhEsRw3ffdifehd2VzizbDnwxsTnYeKwIhANSgFYu8WQcvuorNT2yRI5sh9ZHwAWjqKuE+Tzk9FqEF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.1": {"name": "raw-body", "version": "2.1.1", "dependencies": {"bytes": "2.1.0", "unpipe": "1.0.0", "iconv-lite": "0.4.10"}, "devDependencies": {"mocha": "2.2.5", "bluebird": "2.9.30", "istanbul": "0.3.9", "through2": "2.0.0", "readable-stream": "2.0.0"}, "dist": {"shasum": "9b6378223aa2e2ef41348bae55264e44f2850417", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.1.tgz", "integrity": "sha512-xnPUOBgqB4AFBFWesK7fRCezJ3CaiU42Z0Tr7vTYBsA3Z9c66Fx9HG7FOqj4cYLqlueWjdB/eixBhkNAc20zHw==", "signatures": [{"sig": "MEUCIQC1i8O7qPwIpr9gv3cLg9p1h4bMFPoawpuQmXz7fU1LYAIgBmzSiKpV2u0ViBOlrGqDYNmYQpi+YUcEdL//z+7sN7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.2": {"name": "raw-body", "version": "2.1.2", "dependencies": {"bytes": "2.1.0", "unpipe": "1.0.0", "iconv-lite": "0.4.11"}, "devDependencies": {"mocha": "2.2.5", "bluebird": "2.9.32", "istanbul": "0.3.17", "through2": "2.0.0", "readable-stream": "2.0.1"}, "dist": {"shasum": "63481a805ba30ed7d59ad4433b20eb850f95e887", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.2.tgz", "integrity": "sha512-u4OiGQdGMaTkgVTgEyNa59BbgJPzYeydYIi4XH29ia2x5VLx+qwDj42CaEH/DiYNAwTNpxw6i5nS/QD2w5wqrw==", "signatures": [{"sig": "MEQCIG7vBpveybtyhnTFCKcKeRovt12ulazrV7da+aIc0XwcAiBK8PaPod5AoPuAmR7//gWcUsgCpvCSF/g9ZxxMsa20Dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.3": {"name": "raw-body", "version": "2.1.3", "dependencies": {"bytes": "2.1.0", "unpipe": "1.0.0", "iconv-lite": "0.4.11"}, "devDependencies": {"mocha": "2.2.5", "bluebird": "2.10.0", "istanbul": "0.3.19", "through2": "2.0.0", "readable-stream": "2.0.2"}, "dist": {"shasum": "3b3fd88599d7e361b37d4f2bb11edc9d28c647f5", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.3.tgz", "integrity": "sha512-Fvpi+Muih2qdVfU37wKD3QHL1jhvy0R0e72KiQNJgTZi6E7bTAj1tHSzCQSaytLW4Rw7c6Jl9oZBfv1brNh2xg==", "signatures": [{"sig": "MEQCIBDuZmDsPUaQs0hmVUbfaN0gVtNmHj5v/L/5MXc1I7KIAiAZMJPuJUMY3Ym4hfmfUoOzqumBAMEko8UPCPIF6fM7Yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.4": {"name": "raw-body", "version": "2.1.4", "dependencies": {"bytes": "2.1.0", "unpipe": "1.0.0", "iconv-lite": "0.4.12"}, "devDependencies": {"mocha": "2.2.5", "bluebird": "2.10.1", "istanbul": "0.3.21", "through2": "2.0.0", "readable-stream": "2.0.2"}, "dist": {"shasum": "dcc3afe2e1fdfc620a812376f8e0fc3d2e62cb50", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.4.tgz", "integrity": "sha512-11emYZPhx08P0rpiiVMmJro7HaNavZop7abw1PBZV52rKLGH0aphm0aZPKnR1i3DVfm/be7y9Ylv/55rE/UuSQ==", "signatures": [{"sig": "MEYCIQCttsDLYNegFH6LA4mJwOMgrsZpJemkIpteXj9JC/3a2QIhAJNlnH9Ml4FuLWMYWilIeiYbAsn+YFe9X3sXjJwcbThz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.5": {"name": "raw-body", "version": "2.1.5", "dependencies": {"bytes": "2.2.0", "unpipe": "1.0.0", "iconv-lite": "0.4.13"}, "devDependencies": {"mocha": "2.3.4", "bluebird": "3.0.5", "istanbul": "0.4.1", "through2": "2.0.0", "readable-stream": "2.0.4"}, "dist": {"shasum": "8be8f09ddefd0d72ad99d883ab7f0cc350420956", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.5.tgz", "integrity": "sha512-VAzOZ8k3+YE/0kx3j8KYaXslsrtvZdA3REoJVnYz6F3synp/6yH1PCcbWpEtUilJqgINtVQuOEcvTqPnHubt0g==", "signatures": [{"sig": "MEUCIQDF4gdRrKwVeUWb4p34zni8THVWbTx5hZpnc1b/RUbrZwIgCrfphAhMho9OnjpLtuEyA+WmNdDoz1r1538nvj09uJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.6": {"name": "raw-body", "version": "2.1.6", "dependencies": {"bytes": "2.3.0", "unpipe": "1.0.0", "iconv-lite": "0.4.13"}, "devDependencies": {"mocha": "2.4.5", "bluebird": "3.3.4", "istanbul": "0.4.2", "through2": "2.0.1", "readable-stream": "2.0.5"}, "dist": {"shasum": "9c050737fe07ced6d94a4fd09c61b6ad874d310f", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.6.tgz", "integrity": "sha512-7pee2zJGckPfGih08gXdF85oODjo4IACVIZwpD/GrsjBrIi9S+8iOmQpNtkUtLMSPWNRzNpxvQWsxCV5lq/vhw==", "signatures": [{"sig": "MEUCIQD2w7UnpfXYjf0PPjQ6QE9wPAa8Uy9X0h6FfDuRdsxrMAIgJVeN5Mle3I8tKpSx57wzkjvSKIIY5EnzOb5PC1yDrSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.1.7": {"name": "raw-body", "version": "2.1.7", "dependencies": {"bytes": "2.4.0", "unpipe": "1.0.0", "iconv-lite": "0.4.13"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.13.0", "bluebird": "3.4.1", "istanbul": "0.4.3", "through2": "2.0.1", "readable-stream": "2.1.2", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "adfeace2e4fb3098058014d08c072dcc59758774", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.7.tgz", "integrity": "sha512-x4d27vsIG04gZ1imkuDXB9Rd/EkAx5kYzeMijIYw1PAor0Ld3nTlkQQwDjKu42GdRUFCX1AfGnTSQB4O57eWVg==", "signatures": [{"sig": "MEUCIQCSrLiJJgLDOjKEwAlrLJFBDEp3QaDd3n9hncjWx+3LjQIgcvxaZ6aKHnXOxpImWJHD59BDEJI7pt7GJROzjpms+yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.2.0": {"name": "raw-body", "version": "2.2.0", "dependencies": {"bytes": "2.4.0", "unpipe": "1.0.0", "iconv-lite": "0.4.15"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.12.2", "bluebird": "3.4.7", "istanbul": "0.4.5", "through2": "2.0.1", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "994976cf6a5096a41162840492f0bdc5d6e7fb96", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.2.0.tgz", "integrity": "sha512-C6xnwM0GY3tP6cwSzBTjPIW/PgxwxxHAyDoO4q4Ajyf80TyU2e5IsMwumoJf5WXiAVG77u2SDEFUM/9T+9oC0g==", "signatures": [{"sig": "MEUCIGPN4///Kx+OgEt5Fmi/hcLrEqhob0qdWp/tiBRTR1mTAiEAnUJIbRCYcrv/uxnswKZZZQnkCLg3ivlZ/k2L+5iHFZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.3.0": {"name": "raw-body", "version": "2.3.0", "dependencies": {"bytes": "2.5.0", "unpipe": "1.0.0", "iconv-lite": "0.4.18", "http-errors": "1.6.1"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "bluebird": "3.5.0", "istanbul": "0.4.5", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "f79ce1acacaba5b6362d33454d785d7129f4bc67", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.0.tgz", "integrity": "sha512-piFo+RpLmYP+DaZCmawTddrK6MfWgDkU6VAgshAwS1ItctVwJ3cBnrMi36SgvYpUfUTHqXyeIfVq6WthpqkUHQ==", "signatures": [{"sig": "MEYCIQDRSmVtJ8CCNl0hAZAjlW819UD7cUeh5RNAjLQOKbQibwIhAK6zE4mjGIb4aTM4a5P8V5gGZ0fmPTc9ljnsm6f0XRy1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.3.1": {"name": "raw-body", "version": "2.3.1", "dependencies": {"bytes": "3.0.0", "unpipe": "1.0.0", "iconv-lite": "0.4.18", "http-errors": "1.6.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "bluebird": "3.5.0", "istanbul": "0.4.5", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "30f95e2a67a14e2e4413d8d51fdd92c877e8f2ed", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.1.tgz", "integrity": "sha512-sxkd1uqaSj41SG5Vet9sNAxBMCMsmZ3LVhRkDlK8SbCpelTUB7JiMGHG70AZS6cFiCRgfNQhU2eLnTHYRFf7LA==", "signatures": [{"sig": "MEUCIAOOes2VsvQfeFU+tM6A3KHT+vhV4ftnYYlFuUyO699+AiEAgHqOfrvuajDsKh7ljbPFFATSKID2Wb8E8ON5Eh4BjDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.3.2": {"name": "raw-body", "version": "2.3.2", "dependencies": {"bytes": "3.0.0", "unpipe": "1.0.0", "iconv-lite": "0.4.19", "http-errors": "1.6.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "bluebird": "3.5.0", "istanbul": "0.4.5", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "bcd60c77d3eb93cde0050295c3f379389bc88f89", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.2.tgz", "integrity": "sha512-Ss0DsBxqLxCmQkfG5yazYhtbVVTJqS9jTsZG2lhrNwqzOk2SUC7O/NB/M//CkEBqsrtmlNgJCPccJGuYSFr6Vg==", "signatures": [{"sig": "MEUCIQD5Zq+QSk4M+vcHoAGizqI2SpVMGY0Fs8EmGF9vJ4v1gAIgDxUnYGWO1wzk+KzpS02KtFo1f/oZeMKfpLuQW5AK3P4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "2.3.3": {"name": "raw-body", "version": "2.3.3", "dependencies": {"bytes": "3.0.0", "unpipe": "1.0.0", "iconv-lite": "0.4.23", "http-errors": "1.6.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "4.19.1", "bluebird": "3.5.1", "istanbul": "0.4.5", "safe-buffer": "5.1.2", "readable-stream": "2.3.6", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.11.0", "eslint-plugin-promise": "3.7.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.1.0"}, "dist": {"shasum": "1b324ece6b5706e153855bc1148c65bb7f6ea0c3", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.3.3.tgz", "fileCount": 6, "integrity": "sha512-9esiElv1BrZoI3rCDuOuKCBRbuApGGaDPQfjSflGxdy4oyzqghxu6klEkkVIvBje+FF0BX9coEv8KqW6X/7njw==", "signatures": [{"sig": "MEQCIGvTTee09erdO7YcRh59Bn4jAX3bqNwslny6OQkTeskgAiB+hPituAZxVInQ3uc5hxwLEhhL7UApjT0TLc0VSFw4ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8cS5CRA9TVsSAnZWagAAR48P/2RtvIAfwrPtyGIJAnrt\nJOSPTZaj3zQd0Ort5qbS+gNU1fyjbMtetTPAFt3Aw2BeAN8z7CeDfUkfg8BM\nhUkD1P1f2nwM9TEQzq39mdB9s1TrDiFobaLuLasZ1uFU2lb/X8LkXshrK3up\n+edfrVl552xoLkipHSwRB3X90F8JJHvOq7crw5PphMzMDYlpW9hzZwA0L6n+\ni1CCqTOOD+ER0ntZNoQmEjZGueK/aelOyTpAw4MLfZcure3ALwB5xiRVO/wY\neaVmpLJW6fAIJOH9MQcQ6k/il6HHplG+rG4Tk4tA4m9/5nlw/HxTu+Icb6bG\nfHnriKNW2qi32WtUHQbgg5SQaFIvLS6VA2up0xVXml+9tnQj+G0Z9SBlHpuj\nOyO3VsIQONB0yqu5Owb+X6Y6d+TM5FiofPT0XpIyM0Zn2GUYqtKU8XRUmreT\nrZAIOdXw0mGtQ2GcOMGHBapOBlHrE1YrjX68/f+4vmdNKj1sFUZaeoG5vu46\naYKIuzVV2Y6qf0GhTVRF1yaYMNTADM25WMm/+arTnmVHfer2keYLdViZwC3A\nEhu1489+RNCTpcw65RA8RaO8BfIM2xep0v2GbwNiwh3majiEvmLgV3z7PlwL\nOQG/HoamXgql/E6Q4LVBxPQ8fQzbWnSQr10M1dl6cabayDSc6w8dBvQDEWcD\nnJh6\r\n=LAnA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.4.0": {"name": "raw-body", "version": "2.4.0", "dependencies": {"bytes": "3.1.0", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "1.7.2"}, "devDependencies": {"mocha": "6.1.3", "eslint": "5.16.0", "bluebird": "3.5.4", "istanbul": "0.4.5", "safe-buffer": "5.1.2", "readable-stream": "2.3.6", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "a1ce6fb9c9bc356ca52e89256ab59059e13d0332", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.4.0.tgz", "fileCount": 6, "integrity": "sha512-4Oz8DUIwdvoa5qMJelxipzi/iJIi40O5cGV1wNYp5hvZP8ZN0T+jiNkL0QepXs+EsQ9XJ8ipEDoiH70ySUJP3Q==", "signatures": [{"sig": "MEYCIQC+UzjDQz0a2xwb8KlKpwUId3EvrdI6hel4X9PfwRlfkgIhALsX3jiKEfdiSH0IP7xT0KRGLnbRdGDWDb+FwlPtlHEr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJct+Q2CRA9TVsSAnZWagAA+AUP/j/tdf22dLOY3EdroYrl\n3uSn8SGTFCLW4Cn/c+tPggviFnSSxRTHADnDc72FLgOEf7Knz/VvW3dvkd4w\nemFCj9AuAO2zhc8BXYiJqAlQivL99dLM21OfBfn/QlLm+AM6dJgqQBp83lTs\nomzh8iu73RqjLKmAGwIXiGlubwq7FgXdEExZWwGIFFZaMw51PxG2knmtp0Wg\nJrbCvsoymvrhjkxDR7yLCdSb/2Z8FfgAFYuxSIMKAQjBQlb+CAbcImt+b4Jp\n5mGakb6XY8cz9+EhPxeodjBNcWiNt2TXt8rbbT1Zvwwdu8+GG76RyKSAChZP\nkVNAX77XWgiCVtpyIwXn729l42x6J+WL6jEjFjflP8pjPstgwbT9R9LL/xai\nn+XcPpUxJ/6ryyeS+cEZbYI371pAWMWt0LfFhJ2HJkEV1Ur5+lAqnI9JKHIu\nDZ1fsrivXULtTYAFjPSBPDl4SGblS6P88//kSWYkQfPgGMe/yGJorhmcexeM\nDb/kRxKUTi/j8ZIMNLcBvMtWs1xVkKIRdy03i0n1PCQB0d3mBseFMN4z9mfI\nn7yWVc8HluRSIjsLZBUrWipJ7UvTC+XQn+x0mP2cVXu4KTag5/727Yy225ms\nrx80AKAZJ9IqyFbGOKGAul1rsQwcrmrakko0+rzmrXyhKa2QUL9BOfSvme+Q\nY5u7\r\n=0DCd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.4.1": {"name": "raw-body", "version": "2.4.1", "dependencies": {"bytes": "3.1.0", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "1.7.3"}, "devDependencies": {"mocha": "6.1.4", "eslint": "6.0.1", "bluebird": "3.5.5", "istanbul": "0.4.5", "safe-buffer": "5.1.2", "readable-stream": "2.3.6", "eslint-plugin-node": "9.1.0", "eslint-plugin-import": "2.18.0", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "30ac82f98bb5ae8c152e67149dac8d55153b168c", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.4.1.tgz", "fileCount": 6, "integrity": "sha512-9WmIKF6mkvA0SLmA2Knm9+qj89e+j1zqgyn8aXGd7+nAduPoqgI9lO57SAZNn/Byzo5P7JhXTyg9PzaJbH73bA==", "signatures": [{"sig": "MEQCIByKYB8p070zFGu6XvhUAMJnWaP+2evjjYzSxjplyoD7AiA/p5x3WBAqRwY8TUQ4EtECtPhjCnnpO/N9M4ZZrYHLUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEutqCRA9TVsSAnZWagAAhlMQAJrPS2EH/dZOsGFOl6aA\nAtrK4/lY8PaEUFt4FwnCwkvOXtK70VFMZSDtXks7qDu4v+IzPuDs2LZwN6RO\nmdrQatT1M8MDQ6JPVEPzX4iXFL6j6vkXqpYypsT1dzk97ACpUtCNkyYdZDzW\n9FnyMmdjlCT+MlGu7m2NZeuaaJv5TCkAGqkgmbfDAQE/QwiOlIVHD2oQCju4\n1W60tWupFRxQQqNEYZL1Jt3a4q0AoFB5gagwlrI0la/hjNBecjXVaTULaa73\nmrhYY77p9wGAjWOg8ZAr1TRhKzkOzjkQ483+4mPRgDj3kwkWNZaIqo+CCELP\nKih6yQ9v18rkPtSryUTTkO1K7O0K1MFH9DBus/Ea90LB4F0gUko+4z7zWQbD\nICfZe5r4AEO54mXbZ7vD1NGstXm1vRsNO6xGysTWYIf57rX0dQQDXzhu0u/x\nGgAk2COBnwmtoCL97ND37xxZwF6vjfhTszUztzcIbCPzQbELm0gcxNa2XUpX\nCkvgyBGiCxuLpeQdECFP7q6TM1HCFSojLvp7RzacLaltoFBa7LwTNNMO86sV\nmq8RJGHUJO7h1K5PGdkQ1duN/IGgjVmk5JWuyHScbRsfz6ZjYABCWKAbep1N\nYNtuBEimhNtiDzorPNOZJZoAn5eVqP1sWWHqKefXWtrjtM7EVBtn8G/tAWjG\nxBgf\r\n=1FPP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.4.2": {"name": "raw-body", "version": "2.4.2", "dependencies": {"bytes": "3.1.1", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "1.8.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "bluebird": "3.7.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.7", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "baf3e9c21eebced59dd6533ac872b71f7b61cb32", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.4.2.tgz", "fileCount": 6, "integrity": "sha512-RPMAFUJP19WIet/99ngh6Iv8fzAbqum4Li7AD6DtGaW2RpMB/11xDoalPiJMTbu6I3hkbMVkATvZrqb9EEqeeQ==", "signatures": [{"sig": "MEUCIQC5Ya73VHn1ay2lan/MMzPW16lr/g3qMW7evNqdHREVnQIgOFhRmeR3iuo/WjD9DQ7KfSFkxwsgef2VC0+TwZaSuqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlGtWCRA9TVsSAnZWagAAQcMP/i5e7u2/2DascWb096jI\nQYhRbSqaSVc/COq1TQlJ4iTveORTml1nfBixIMuwMIsmIV5weiVnffgVEIIV\n2oj149u5HLBtsAk58U+sNP6aJKhbp+LIzyfi267lIECREprnPvfBDenRT7T0\nJgxxrk92T6YqHB5jMFO0yg6UXArSXGkQrMi0gn9GojGcWwTAiDo2d8e27XZb\nSgzeyZ4qgcvOHKirFsg2Q37Q+ib/dxogZBzNtg3pYkOraWfN/N7y0S01+Bke\noIThQcgirIHBoB494DTKtFSsRpvLtw8C0w+ie+5xZ6NhPJ3pufj2ijyLtFIO\nA+UlZJA8kODWbTNN+YPxObojZJma/nfthrjG6to7tY9xxiOvUvHxSR20Jr+d\nTj5ENg/vbGE6TqxGupnDwRqLKM3g+sEX1evh8Sy4uYJd36LiX77rb8Xotw2j\nai2jHgWEqTjt72jZHQvKXb9n2RiwZQnrTpRZtdwfnkbEXlF5uCiwHDxd8rjY\ne8m6QE4GD2DoiK45lGWd+m7GSUp6zQEklcx/t0iuYE5CySGu2x9K587/VFuN\n7ALaQ/r7WZi3vDWUbJ4DEIs9mzZ4UXtkmIDwiHgmNhWaifoS3/OoPQZmsyg2\nGf+jg367bmI6jk73++1sAmNxH99nCKCUlc+wOBM3nRacFwVHhx/dHsZrv3zZ\ntmvM\r\n=VrZd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.4.3": {"name": "raw-body", "version": "2.4.3", "dependencies": {"bytes": "3.1.2", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "1.8.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "bluebird": "3.7.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.7", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "8f80305d11c2a0a545c2d9d89d7a0286fcead43c", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.4.3.tgz", "fileCount": 6, "integrity": "sha512-UlTNLIcu0uzb4D2f4WltY6cVjLi+/jEN4lgEUj3E04tpMDpUlkBo/eSn6zou9hum2VMNpCCUone0O0WeJim07g==", "signatures": [{"sig": "MEUCIGoQec1iTSeGCqDN3KESuXFSjK4+wEsg8D+Cxs61atJ/AiEA7kDcPqfus0ov75IlwPfguOZlbly42M6QNJkpe7JvBbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCxtJCRA9TVsSAnZWagAAO7cP/RkuwLg0g4er3uM4CEWZ\ng37VRhEOr6vIfJdjusULCXjo/ywbWwUoLB3Z9HVQUfskdsnlHkC47ia7RHuF\nAmj08oy8LwagLHn3t0hGfR2UoQmVggkdMX3aXij78lozgT/0sxZ/PDMqHHnP\nOFk5wr2WdND5CBa7crN3X6M0hOVMArfnSXGuwMz+oWYDBZiVbkct47mW5iYo\nG2jCm405Cjz1MiwBDxB5NzWjh9P04/YkvDjXRJVdEWQQiTdlGdQSQ0qreGHD\nZ5TOp6oEbNHAN8XgKApW7XeWhMi6SuxLANeROU+hrr6sdJd5tcCJHC3S5Oci\n9rIhnIAzTsxE2MO8RFaC0U7I7xTTxq7bhkAElcdEgaMrPRUDpuo/C6OpJTN9\nJYfKmxPI6pgkkBpBZPdaOn1CIHMIVxcxlabyIQM7xU5DNVKxesAa6q8n39Nt\nKTJfWabzMYUn0b4t1NbMooiyYKZAKKLVXi8vmUHftE4mP5PGjvpDyRK60TZd\ng3DBrBbjyw6dF6J8Mdq6r9jtjeGgbQBRT/5Y7xtpg2DL8sZH1FGJFfanJxpl\nm7h4ZWCPPYZipZXjmpKGrYkgszXGKk3lY8WuTGBMerAFfTeK2Px8V9FnCc9H\nEyig6fJOPf8lmGqc0Q1UrBzbciXEF6owbTApaujWD9JRvzI1KJeB+D+q03YG\nvn5i\r\n=UBKt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.5.0": {"name": "raw-body", "version": "2.5.0", "dependencies": {"bytes": "3.1.2", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "2.0.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "bluebird": "3.7.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.7", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "865890d9435243e9fe6141feb4decf929a6e1525", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.0.tgz", "fileCount": 7, "integrity": "sha512-XpyZ6O7PVu3ItMQl0LslfsRoKxMOxi3SzDkrOtxMES5AqLFpYjQCryxI4LGygUN2jL+RgFsPkMPPlG7cg/47+A==", "signatures": [{"sig": "MEQCIGL339h86wURZdRRuj+4YhizV+HMzopLMVFJ0CX0xbyTAiBeThkpEAmkU7ZU7zrqDQc2tLhU02RkvlcWAMgwflaKbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiE8svACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1/w//SnJT8qj48iPmgKJ1349MU9Z+6XauracIz20VDsxHP0NY7gwg\r\nQNkOnYkL9jB4IX16IWU4b/onFQJoBKwRjYfuQObrSOmAcmAJfIJgLvjOwFpd\r\nQGMuiLQXJsduXq5rlSgNFGBAx2x9YR9DxrZjkQB2NWrFXM0B41HLtt+Jjf/+\r\nX3eISABe8nl4W+q1DS1RfuKyOZYb6R2Vxd5VJ6kn8+277AoBt5mgq6mb6nV/\r\nLPgqxF6hGyH91fS/1wOckjsQjIV8lUUukoPVdwWHWIrYYApQIS7oFxgMO0qY\r\nSKXNcwrHYu/6KUDB/V16/kGJ7+5emLzudmRYaR5r7xy94BIZXgTqBXDQ0T04\r\n3jZcOx/Om2mU8MKeftsGKnPGa8HRa+04O2TXv8FCgLwNVvGFGG1YTeI+UsV9\r\njD6LNreLo7C8gZUrVIlf0Y+zS7uU/nysYVPHrrQE0n4ew6RCW9cNqS13yxt3\r\nz1NRnV8lZsdkhNCM1mJdhZIBocOC93w1Jp5MDkgXtQPek7BrMVbPB54AeYPB\r\nAUuRWrzPlpwuygpq6Ccy81zeJsn6djjon8kr7JYKhyaR2d45U0G8YBQ8/dNk\r\nuAs0K1JdY50LB/rwjPmah4u0hiW5YRb/D34jFfdV8cdgTwMJ/KaKGp0wQGbA\r\nUBU6Ts/QnI6SAMNqYHbsCSzXIeJdyHUOxkk=\r\n=mNtZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.5.1": {"name": "raw-body", "version": "2.5.1", "dependencies": {"bytes": "3.1.2", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "2.0.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.1", "eslint": "7.32.0", "bluebird": "3.7.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.7", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "fe1b1628b181b700215e5fd42389f98b71392857", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz", "fileCount": 7, "integrity": "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==", "signatures": [{"sig": "MEUCIQCKfrtyMqhnf3pT/6igu/RG5zFsUmVnu1MDCMuSgKuJaQIgFFELRzN/HvXZ3DtOUap1g5l+ofk0HuBezpjosPR4U0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHULFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+YBAAjXyp5cbupqTAc9CJDK2khr1bsFdvhv8EWxsb8uagXwchaAfs\r\nKp7UU0VgCgUqQzXSBH6DKrTdT1wFXWqAxJDon8ggn2EnqePz044FQCgUuzbO\r\nRWA9iFOX2jcpsFtBMY4qXNRZaCKPb3gOq0M3LI1kMzQl7C79/uTP7Ecjq8ot\r\njmsMRaYHE3S+g+Ki7tOXKIfKFCt3PWA5XSa0Rjc/fbtc7+Db36XJKRpSsJkT\r\nAX9BXVIBrH6GhN5wIQ3inOLuy3q8DuG/XsEHZVh5yd+d3dQsHpzR/+ld5cwB\r\n2Cnq1whlLRJxc/8r98XnV8CoQUX51hVG1MtR4zj5q9YhwjfLEuMuSpOnsqxC\r\nj3mdgg2KyY+9TyeqW0xdPpXXX2fVvXUhtoHv/mILF5qd+a673rk+Cc5qGbEX\r\nrXZ0N//cMJOHK0BRlcn06gxu5OeMMiLb8ZmFJfPTaPK+mIqpl5ecVrrs/0du\r\nfeHGHMNxCUvT6puXMEpXwimAvTnatF1tLtzEnq8EIjt+U0rKcvLQMEEY1Lwx\r\nAWoKRfX7ZvGjf4SCjUxkysmc0g6rc3L91g0dEb86U3EFkxh8x2JlrUGvLisg\r\nssVewTKN+HKTswWY45wi2Uu2DjyswnAicUo+MzBTrKbHGZ1f0sdDmq8JcSAe\r\n3Upb+v54CWWAyk5opDBnRScbqr5TOD1ae1Q=\r\n=kX7t\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.5.2": {"name": "raw-body", "version": "2.5.2", "dependencies": {"bytes": "3.1.2", "unpipe": "1.0.0", "iconv-lite": "0.4.24", "http-errors": "2.0.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "bluebird": "3.7.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.7", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "99febd83b90e08975087e8f1f9419a149366b68a", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "fileCount": 7, "integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "signatures": [{"sig": "MEUCIBYwLnXXS1V1XIMK9R/muVE2U/yaQBnansn23omkAJ36AiEAzUEeHadQGTUU19iqci3JJBTqjW3kfULa7+NpHxj+0o8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9PoBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpocxAAnPqRuKovfst1vndEzYP+k/B8NScZJdukMOqIeA+/KoYzv+FW\r\nai8U5XrvvCaPDXwDr3X9GgjrJ7D91rjCfUmnWscu95WUxejBrZKLHuujBOGh\r\nvFrBMIWHhrmeORmBs7VuHWO4tpC0UPQ1IFOasds9I/c4jW63dXaBzXTiYEn8\r\nI6sI9yrWHOMGhrtqgCowhPZqIZGJKhiF0a6G6rhxwcotsBay42ShZy3AtwCb\r\n9VvjsHfc9nLDQ/v+R8Qg7v8cB8/nUWm3EnlhLppHvgp3j/swLFEUtMyFim6A\r\nD8giu4jrKRd7CcNhf6Yv3tasHG/u4c+TPp2qixOBkIahrYcNqp9lG7i/9SUE\r\n9Nxm6TIg3+C/TACpJqRP7VQPcWzgYitLxgYW52cFmDhKyRiyiOUjnfO+ZCT5\r\nhbqvEObLSoQnZzKqELgPXlw4Ms0f5FL7pAarWG1JQNgV5o9q3y776LX0W4rI\r\np1pgD4tVqPHoS2QCa0QoxnFCpJRNJg7pYT2jXcJ4meckpg0Ze4nghAST8nJ3\r\njkdEt1VM4ivX+6wX/bUnBG1wfkkbh9jsTYK87UUiaayFaKbuqg0K4ulIrHiC\r\nq4mnL+S4OMafmgdLZACrHrarsW4hZN1nriQUhf4rMjBS+XIJuYmgfA+KiJHV\r\nkpC7fD/NsXbgJK14bjN53s3PKpOOWTWEZhY=\r\n=dP5q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "3.0.0-beta.1": {"name": "raw-body", "version": "3.0.0-beta.1", "dependencies": {"bytes": "3.1.2", "unpipe": "1.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "bluebird": "3.7.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.7", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "15.0.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "b202356e7adb97dd5f35604aac992a6ccce3bbfd", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-XlSTHr67bCjSo5aOfAnN3x507zGvi3unF65BW57limYkc2ws/XB0mLUtJvvP7JGFeSPsYrlCv1ZrPGh0cwDxPQ==", "signatures": [{"sig": "MEQCIC9p6z3cMEf8gcXjkF8QNrIOsNDmuQH+LLaQ4wr2jnUDAiAUZZUzwTjKFG1gjWVzSqiVfwTpgWa0Yl/gaEXpLUFG3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9UYoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEpA//QaDO34h0yVtuOmuT5bG0qoyIU3knb3QHhZjNc/wRGWxGaCsd\r\nzLCDjJGA/asSCgWLb87z1KL7087GYda9BNmdD6zafLzlorUI6vTxkHNNsa6Z\r\nv/lCInPMVyuQ3AKAe6cq5C0WBbOu90g9an7kFq+BhepD4WHsEFWHYQDSitH/\r\nTLLEV0YKE4+/kfIE80I+S1xcZVErAETgr4QK2RQaFHD3qf5N9w6OSYQZm9cC\r\nsbnAWCLN5J4f234NtREJ/4pVQc/J8Tput0zZXdk1F2yVhBaEARaFk2koNxt5\r\n52mEStANg7WqR1AE9G1YocmQVQ8pONJXgb4uEkBrSVhh4+g6cTReSIloRYGz\r\nCYzzwofnVhD2AfvIVWYwWRdoVvpf2N2uVvVAB7ncZ+5zEE84CjhxRULgyBQn\r\nHNyB7E3mqz7XM+t45sUsY86DqDgJzPD4SlpZ6cbpKf9lDA0ZJFUSmXl5VX/n\r\nXYgO1oGpjSbdlJF+AED0QjCME30nhPIfSSEZ9M0nEhkdHFhNmxW6JiKBaTZQ\r\nLe61EVt2Yw4TdKYj2OVU4d1nm/8i+0J6Hg81E5FThQGVZltfs2p/YfV6Xer9\r\nPBYwgsfsgYHRYUcGqdpPUnuy0jZvUNwIPJY9giAPbYY+cVxuYs6fABQosnQy\r\nCG/xY/MIXa4fm42YDKNW+mbVTUHV/qeNw/Y=\r\n=LcMT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "3.0.0": {"name": "raw-body", "version": "3.0.0", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "devDependencies": {"bluebird": "3.7.2", "eslint": "8.57.0", "eslint-config-standard": "15.0.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.6.0", "eslint-plugin-standard": "4.1.0", "mocha": "10.7.0", "nyc": "17.0.0", "readable-stream": "2.3.7", "safe-buffer": "5.2.1"}, "dist": {"integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==", "shasum": "25b3476f07a51600619dae3fe82ddc28a36e5e0f", "tarball": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz", "fileCount": 7, "unpackedSize": 26315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRHm+2SgDfQ8LBIyQ7bG24YcIz8jMNK4ZnxDngIlLFyQIgXssTFYR6DhCMPIUZ313NUwaJbD7CUbqQl7i9NfJtbSA="}]}, "engines": {"node": ">= 0.8"}}}, "modified": "2024-07-25T22:18:40.194Z"}