{"_id": "jiti", "_rev": "107-08819301155b3ed8d474b090ff8d5393", "name": "jiti", "dist-tags": {"2x": "2.0.0-rc.1", "latest": "2.4.2", "1x": "1.21.7"}, "versions": {"0.0.0": {"name": "jiti", "version": "0.0.0", "license": "mit", "_id": "jiti@0.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "0be2f7857919ff102b49f3063f1fbfc608d74e89", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-lFGrsDfufUj57y+vSf7rOIuDIxVF6KHB1kk06yLTEzKYIoVAWG3WFGnjfF/EZWDxJOkp8psqhRJKJlysp9TNDA==", "signatures": [{"sig": "MEUCIGGto41gOVluSSqiOYBmNJQdZqVRndtV4EZBiIX4jtJAAiEA2rt5Gd8Ql1F2sMeXxWyW1OjklYLekiVZhUQk3SLXs+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2+xdCRA9TVsSAnZWagAAi84QAJOWlgC3bqs17qJCmtK9\nn8ywYTwVSN8ohh5jhkj8SeI8GTkN4bp7Ne3EHn1MTocEAU88q9oaI6qOGyQp\nTOoKgBj2gdc0Y/6/081Fk+sRfkY3CafAmwaZZvTxO8hTOF/9HekVPbdWInHp\nPpi48L5xDJPb99yT/u592/Ni+D8qyNBAnHXH8tU+RSiTD08zpMYCu0rCwZzs\n2G+khmZn4FNGYW7fLEIWDVm3rMOSakPBUao7WDWsrFonzDxH1h04OVi0mj3G\nnTgXqWw4MKPUlhGlFuVf563s6+LG8+tC8VZcD6DEubItR+45mNUTJqxk0pxo\nmMI8dxR6wyXvyyhMS4qBfGijS2mFkvf1QVwpiRlplRkXwy95nwpbiDQv5Ht8\nFn0lYnsjeC3MxVp9ssRolxB3tAuAIG0KoYpcmJJGL7osxtnoG0y+Kc2Fk97D\nG//NpWk1SbCpf2eZ3hZGZzdASKkrntpwAY5NJcZMytIwmBePEVhNy2aTNbiX\naBJ+is2oLfFZp4jdumN29FSy41LRizMuUuoflKBpvOwNK7znfuhsafidRjlD\nrjPbLOHGoFUC4FYOV8U0xplsi8MzJ2TQCeqaU4GnRNPfPagTUrH1wZTpbkkb\nHj9EDFPbJ8CI3VwnrLI+O07AXvv2eSnSwFfFoelLqbeOFmi5+eb+TF9DX+BB\nA+8q\r\n=IZWG\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.0.0_1591471196555_0.5130964863905125", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "jiti", "version": "0.1.0", "license": "MIT", "_id": "jiti@0.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "ed422d2920562fbca8dc13ea51f457cd765face1", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.0.tgz", "fileCount": 6, "integrity": "sha512-jqLJ/Qho9qmZyPMJlNzfRZ09luGCF7G1l753tCmMn5f1wR9orTlF5xUH3vXHDdyYe/KCtVK4kd8O78F7436uXg==", "signatures": [{"sig": "MEQCIAuy0S0V0bOc0h8zyYEQ1a5zjLTuKL4Pknp7AiTdnUMhAiAhQ3nzbeAyzJ19zTLM+UyRHAiDXM+rh+fHUtltEVQC8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1821662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3CgfCRA9TVsSAnZWagAA1wAP/RO1v0YDUQlMC0lvOWTj\nsS8GlT582qgwT4athq6opNXyd0nfKTLyFjPAh8OGW9urJtcPyOi/uiBGNZHp\nJ7SDoPaelzSYG43JeqyK+/Fm9RjRCeadKMVjzUiyEwtJ7alhJYLJy2CNgX7S\naeZ1+mfjtIkg0DorSNlg7XTYsjJE2VuOf+DEZ4nt9motZxmANVx9vLvED/oS\nBIRtH4Cudie3BsgZaLg6LJ0BOENO1hCoZInUBxYOGZo/Edd9GiE4gvalJ6bI\n5fQ5O+ujhjHAu5XB4DJK6e1SgBI9M73wN36moLlK2/0er7iOqaCki8zpOMK0\n+LOx4W0Jf4R0GaN2yF7E1DqHAQvJprnJFlG4XxurYQeDeSZHC/R3YPmTnwwH\n+8P6mmn+Ho53CKcNNIVR9RpVohyhlLHWwC7Fo2evBGuP2wy1xJdp4PbYQQ4B\n3hSmlVNy5q5jG8SVbqODAqqmY9hqpmif9oJ6i2K0y7DH9asSvDVpW7iRE7Ch\nQecnDNBQzrChov56Ob3AT3h/RClQrIF3YyVKUcazQgUg7qTU8tnTc6jHRITP\nPCosdKHfNa2O+lruF/fv3VQiZKNTh0hDVl0sZCcgeQ2Q9aUMhhOuGOSBs3dN\ni/9XPSO34TSwtRh7Gk+91OQzvFPC9S6fbQNO767Kxkh04QgGT9HbQyS73xFH\nPhQa\r\n=L7Ze\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "cd992c31799b781c74dd7bd4c75083f1b91ceea0", "scripts": {"dev": "yarn build:jit --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn build:transform && yarn build:jit", "build:jit": "rollup -c", "build:transform": "ncc build src/transform.ts -t -m -o dist/transform"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Require with just-in-time compiler for typescript and esm files", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.0.0", "eslint": "^7.2.0", "rollup": "^2.13.1", "resolve": "^1.17.0", "@zeit/ncc": "^0.22.3", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "create-require": "^1.0.1", "@babel/preset-env": "^7.10.2", "@types/babel__core": "^7.1.8", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-commonjs": "^13.0.0", "@babel/preset-typescript": "^7.10.1", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.0_1591486494931_0.9737097520851894", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "jiti", "version": "0.1.1", "license": "MIT", "_id": "jiti@0.1.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "633f6bdba768683d65c54c00b841c192e73b21c7", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.1.tgz", "fileCount": 6, "integrity": "sha512-UHwZfDjNTnBEjruX7w/kJ/MH2lkZrZ3rDS0emt6Fr+KGJgH6FfsoMFuzVkWrcV9lF8Uoq2N9CUHraR9b5pyHYg==", "signatures": [{"sig": "MEYCIQCcLJgmzdtqRUTSAcuZwtBHRTBp9uI7ns+NFJZ9TV7nVwIhAJS4Ymj+c+wna6E8Cbm+95ioy/ARZinculWlGkvSH0C/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1834809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3CnlCRA9TVsSAnZWagAAvtgQAJStN8belyGbqw6Zw6gn\ncx2geAER/zCLjzz16Ecreityk9HSJ9rCL9wu5IV7xpO8nOpTI74aSLB72qsA\nd/x8MW4QO0GxK6EGxq9R46vsqfl9NLUc5D6FALPkJW51TS2uPAsglROqsiwX\nE8/W3tL/mteR/2k/c8o2LOvTAaw1vdPCStnHb2JjieA3CLz00k9ywPrGL/LD\nP4mUlVs+ILQ4Qtc1FZFXiu6L/vcosbXM0BPJiOvRc4ufcMAdWpSJuaCTI6Ms\nevtfxHDiWypu48zy1JqLiYGXum8N8ywu8Niwgr4tpLO7ZYNool7aUIICqhOT\nvoZ0RayhoqABRROKQ6dw7DWW80MWHtv0eTz7KrW7cmO/XDXI37JTo4jlz8C6\nmuVuk2sgGyLsotUDmaYVjyL9ej/KfvK29SWow8yh8iyy2uO6iMfRIIYVvZnu\nraQJFEbmWjqmjjgyLY+1mb8HXVqxHX+up/yFG89psWWbjAzPMeZEzikcNL9T\nkhqBfZX9yBkAAbb5BEufcj4rxMEE2/e5gN7G3Xw0odNeDY3ztw6gP6h2oq3R\nKIJhQ1Z8Hs3lu9jANodte+S68dw5NtZvcTYdaYGjCBofD+NX+ITrGNkgoDTg\n2gJFf0deZgXXy/cjnkVNvq28cNPlPcGAdWREhYfxeoAEDd08A1TK/9FVc2l+\nPFlB\r\n=+AqZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "dist/index.d.ts", "gitHead": "a886f61083a13174046a52677a779f4082c1ba2d", "scripts": {"dev": "yarn build:jit --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn build:transform && yarn build:jit", "build:jit": "rollup -c", "build:transform": "ncc build src/transform.ts -t -m -o dist/transform"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Require with just-in-time compiler for typescript and esm files", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.0.0", "eslint": "^7.2.0", "rollup": "^2.13.1", "resolve": "^1.17.0", "@zeit/ncc": "^0.22.3", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "create-require": "^1.0.1", "@babel/preset-env": "^7.10.2", "@types/babel__core": "^7.1.8", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-commonjs": "^13.0.0", "@babel/preset-typescript": "^7.10.1", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.1_1591486948555_0.6259184768087931", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "jiti", "version": "0.1.2", "license": "MIT", "_id": "jiti@0.1.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "661d89ce0de40b2a3d6d9b21eff52ea1c5466673", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.2.tgz", "fileCount": 6, "integrity": "sha512-KtiYAg7mMBqY2AS26vzSYWnGwKnXRSeAW+/ZYQddW+fPK2yhxPrEIef0lHJE1+wicN6JCAkC/qF/ViBGUlMYkQ==", "signatures": [{"sig": "MEUCIBifoHd7l49Ed+Cx/FYRZO1zs4IxSZ+fAXJTDdIHlaIEAiEA/ZEOlFx9Jt77L6s0V8nYxJg3m5OREIyZNQ2hbwEUe1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1136808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3R3yCRA9TVsSAnZWagAAT78QAJsVMaHxXTbxRPKsbn1E\nNqSUeq/OKLfNor+c2mlJtry2Ii6DerP+r0uvr01qO9aZOy7J7NsiRsdRCQfu\nDJFkYHrfNXHivKslVf0ERtYhREd5RHZ6WerThSp8XcTXaAweWkMc5qAWGUtL\nMdCNyN/Ovkx6t33iiAzxJB2Gm0zwAduyQC6o6zOrNagZEqK56DJS13sFhkL3\njeZ2EsR/QamZ6wg8tdHxVJy4i7Duur1QtWCsyM2CwtEbgAZ23w9/28EVVIet\nCXe+Sa4sLLncVbrFvmUi0NuNbJEqkpt/PdEwXMwfeWT2a5iKokLW2vd37ECe\nnYZRHDSQ9Sv36i/3GifwT8GZwd+Xw8PjfBBPddlNluWfsYhIBLnW7yPKLFzE\na85c329dCkvmjI0X2wDIiW+L/2nH4fDqFMriqmwb49sw6pboWuUsRyoTxjVD\ng8q9EwLDtXb2ByQnTPLuXoFbAuZujdpsTv15kfwjTT5cCtT6RKkKeqaIYMEi\nYOENdzRnfri8SkjQ5R/Bn9Fu46VuDv+Gs8Z2vWB9oLjBG8GaBT0igCQj2wJD\nHCcBfH0ExU7WyQIFZ0kL2RybFL9zJDbdG7sTQ+ZP+F8UGJ8u+wDi36tpHw05\nFC/8EBrcdNG6YHKmNr5txG5qSgQ9kVICD9UaQRskjFhNr9zay4NDd/L3UXxu\nvJu+\r\n=qW7a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jiti.js", "types": "dist/jiti.d.ts", "gitHead": "b2da5406497f08088dcb3a096123a5eb4085133d", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Just-in-time compiler for typescript and esm files for CommonJS environments.", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "webpack-cli": "^3.3.11", "create-require": "^1.0.1", "standard-version": "^8.0.0", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.2_1591549426336_0.6837492685053765", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "jiti", "version": "0.1.3", "license": "MIT", "_id": "jiti@0.1.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "7d3722f542b7ea1678e2ea96af633fabdc92a31b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.3.tgz", "fileCount": 6, "integrity": "sha512-tsWnOUChON4mJwhIii0W6NvJm1E4soSGW8ImzqQwPwt6rRih0AERBRSEF3QCaShkJuuI6IPNsE9MHtVEWGNqrQ==", "signatures": [{"sig": "MEYCIQD/ehik6cb292yn3MQ/FiD4Y7AGffAuF7jwkFY4r0xTWgIhANCpR1koQJJjdJysvlig30+8LCi92Io0ZXudLIAmPdIf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1138875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3SSCCRA9TVsSAnZWagAArrsP/jJu7zJGDVPW24p2Nunb\nX5c78/Dllo7wA3bq2rmwoOCba8VXs9U08m+lgQyaULMYR0m8nxgxI7+PkVSz\nLY7Ub6UuUrmkfiWX0/hTnixrRu+cy8RhwcwBt3gKTuSN3Ub2G6qSW7bbThvT\nVyhiQjYYWfngDW5KVzS/RtJv5BHIc2LNhNNX7p5tN6At2WMkV4c9KM8sUQte\nJi7P2SHI3LsPj/rX0FMUntmr3XxYqGSfGD47M3keVj3Dc6Ta9WidZYjzhPSK\naetErVpnm4kfLPNscXeVA6f1G7bTDtMVQN0/9KbefO1Dv2YZfGaq0wcocCIR\nmwv5nkoOzAXcIIakXFNa+V4/nhJkOZqy19c5f0J7OlK54p8Ea7m8VeLvS6/N\nRAxFdJNjzdYA2DTumqTEiJLNdInJM/y6gfjRV8fBNXovOkt4DrhVsBc9emzd\nlRFcGaI/LBeH8V4a8wBxwYFdkjUFZB0F6a+3zj5WvTPYe6KfBOKnYFS3SgJR\nKPYjQtBGMLNULGU0th10/zS+Pnf2GX4gzdYWPoEo0P/RFdgkj+wsnS2WTTiV\nmZ5G/NlAxHjUaADWSJT6Cp8cVT5qql6tiSb5zYf0YDk8VzgXJ+oU6UClxLh2\ne4nRFNz/TDUkCYKvhvwVL9qTwvYny5v+48vliY3j+1mq51nrNNe3FsE40w3M\nNpJF\r\n=E+Rh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jiti.js", "types": "dist/jiti.d.ts", "gitHead": "0c944571a560bc39b204e69af14f765636c204bc", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "webpack-cli": "^3.3.11", "create-require": "^1.0.1", "standard-version": "^8.0.0", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.3_1591551106069_0.1681022624437003", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "jiti", "version": "0.1.4", "license": "MIT", "_id": "jiti@0.1.4", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "1cfcd2c3be69cabe36b424a56715f93ba38cff19", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.4.tgz", "fileCount": 6, "integrity": "sha512-p3kyV6vNnIknQidFxPxHnjVMSWnnrMo5jh2JQWz1Wgvsd+893i7wvJ4nF5PfxbZLFHcc6e6i9Xz4sFehdToIbw==", "signatures": [{"sig": "MEUCIQC1S7LBD9rpbGdBkTc9vf7IdupFOOaCNhYzCwtDYT+uzwIgJiJvHSQc7tj2As1BXieQp0RQhyW+iSO+aNMkL80cyOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1139573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4jwACRA9TVsSAnZWagAA3AIQAJEK35zpbaQMs7sQd2dh\nWJYp0oU1UgcPy9cq7SEwskzoai4o9rTG35D3dvIKJAzCje4NvGa4Svw60THy\nUAIa2esNLNprgwsk30al8aHVaxLolDJFklSGN5BftjTxp1rNzvCcUApBDAlV\nBw6cSjzdd54VjMYDp/3AIn/vBLARQVI6IZnwlUEccF42KrTAbSBVjjE/7GYO\njGJZWrFZVMkMHTydC+OqY6vFyBrycyhTceLSQSj2viDdNJ6R7zuMzLS/kb36\ngxlCbAj1tW634tpcNcf0/NYl72Y6qiSOi5IrzKGuBoM+5dX3Idd0upzdg/6g\nu7BgPpkOrjR6GxR8HPvZumpNkYmASW2MrOm9qJRkgqqyJ7q708LK2fjbNxD9\nv/GbsiY2iUYsirifu6agaDcB+2kabQGYDuH9noFKfsvecoFiNStCbOiG3Xp+\nw/SFsYWC0JMcC9717qEpCBwg32yhV6RVpmLxIZu/41j6BNfgPOw2OBxRo6At\nb5hKa4px3xT+n90pAFeLAbDRMG35o4XSNAuKDn+5VupaUEpJ0qVpOLUOcz8s\nriQnWvWAW5b6W7prQaDyZE+GJd5OL7e+xh96KLRm0h7AfHmikBjsn/x3DSr3\nU+HLxFA/GWRtDGKv0QoblWcRjdSdR7KzYcvBl/D+o+USYMqDrYTzw3i+2qWK\nb7Im\r\n=MUmG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/jiti.js", "types": "dist/jiti.d.ts", "gitHead": "d0dd67630b397ac3d5ed4434d3e239a57dfb7abc", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "webpack-cli": "^3.3.11", "create-require": "^1.0.1", "standard-version": "^8.0.0", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.4_1591884800063_0.6773379351877007", "host": "s3://npm-registry-packages"}}, "0.1.5": {"name": "jiti", "version": "0.1.5", "license": "MIT", "_id": "jiti@0.1.5", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "eaffddb53c6c0fa7f282cff5124f58d16ffe4bfe", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.5.tgz", "fileCount": 9, "integrity": "sha512-ebhBUr7KP69dsH2+RLoAHuFj9NcFenAwibyyZA6ftmCX0oJgCNwgpgGBkc73j1OHuPkO27OgulbLXSS9y1ih0g==", "signatures": [{"sig": "MEQCIE2d3xoAqqzL/y3kXG7MMjHUoMLSWpER4J5Kl9NdvNtjAiA0Y/jHj3HOF7/tVcQiVhCCO3SuBeyTq0HG5vKalOposg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1125750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4opPCRA9TVsSAnZWagAAmEwP/3q1Cbor6JMuE3dYoMWz\n/w2WI2l+r+vR4E2cpHY5YvZKpXsBGCKGG3+EJfHfqZgjJowdlrsRBCwAbkAY\nVuMd2e1i+qPkoE+Cx7yn0ybzIsG9S5sVWW/w2lhyQY2mNdwcxJ/7oFY3R2LF\nxTJuXdQAf+9pdarTYCJeftuK/kli9T0/CSARz2exjNWRMVIl3NQrvckdcd8m\nzvneBRZLk+mS5zwXXpiDz/hY92M+m1pXt+VMwXIWcGrVotCGUvXW+oA3J6Ts\nY8x2eFHNbVmMDQ9K5GjrQB6GAjAZNeAjZFoztyQ479o8+ZYQI0ACScKBmFCj\nmdzss7QLegTlmjzw7ZsaN0K5bnPAW8kGSc0t8U0TK1SaPV7wbHefX8e7SnLm\nq8hSna6o6PoOMJwvl+vM5X6qqncS7MSueYAU9A9NbtPHy4/uluS2x3tL5nfr\nGPfF5Vk2MAiczZCbFIBrowea1qF3Q9JuKGVQdFMCPMuqxnuyHAOnhc0xbzSp\nK7Kw8d8cIJ1uS2e8Wg1cVZQFvfhKn5RvaLQmb50b1rN2q+ZJE4lrw9p+r/Cn\n1Z0zGgjUqE1CEj8WYjz4qGLrIw+W2S84cWYG8HObvAJEk3FVTtUXONh2eAMb\ntfSJB4A2jC9t6PfABRtv72BOeJD6Qhw7yKmQRsGALOJPL4qjfpNy3gXLvWUR\n18/Y\r\n=AMUS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./inde.js", "types": "dist/jiti.d.ts", "gitHead": "7c45e86179506f431c583a3b6aa2cf60431eef27", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "webpack-cli": "^3.3.11", "create-require": "^1.0.1", "standard-version": "^8.0.0", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.5_1591904846736_0.39742116664701155", "host": "s3://npm-registry-packages"}}, "0.1.6": {"name": "jiti", "version": "0.1.6", "license": "MIT", "_id": "jiti@0.1.6", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "dist": {"shasum": "fc05680c932457ae575850030dd42ce0c4bcbd62", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.6.tgz", "fileCount": 12, "integrity": "sha512-lax0D0Mo1OTV7v3UXrBL3+kop7TUcjHx3FJaxZfmrwKQobCaF1qiETdRNxXhDEQ6tUdizQ3rGS5Eein+vKL5ww==", "signatures": [{"sig": "MEYCIQCPJSmdDRGrP40K82zHOqebuAg6JX+0DS5gezxNMNeVzwIhANImPdO+SJ452SzVEKMXrGUZZeZ1ZTKlp+jXCyMkULWA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1141746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4p5TCRA9TVsSAnZWagAA/IQP/3ei0oIaIVEoRTmKgS7q\nxe63FhqcEgWvypYT1lB9mghMxoeQe4WsEz7JoPkVltgKZwzWxaGSYWocb2bl\nCHtbIehfoWs6GVx7MTRL7BR601Nl4qGg3V7ijb88drKwDy/cKAorV0+WBP5M\npO9WWyIUSmHuaR9ts+jEk4YfsQ5n7hRNUMXIthaAHVaPe6OvfC0of32PXcva\nnwQFi5C12UlLRjBUNborbzzG4CGnEiu/ckTJtfv/OU5tXjh8jQ2hq9Db4Cnu\nzyqd+kolmNiTOOYVYgbDFSwOKlEROfprToMfSvLV6fRv/ZMa3/58VtlUtrt4\nC5ZyN6upqdPMwH3Q3isn+TVXeeIHpepLKNbAwlHbZgptEZQsmil2qELczjCW\nO4gosACnBloX9j//NA50ald68MaYkmlhK2T6tsFPcr9aa/yL2Zdet6I0gKoC\nbshHngkOqXvlRGtXhVCrL76qb0NDcg8LlCEX4h63Xn2RtFO7OcFpwsPPGok4\niT5nVhgpNxSP+8NVk8F4Ms4579Ozc57VxA3oG3/Ph6gRtZI6djzK6dETL+5T\nqjzFokyzisPjoH9IwTn2Wdg3HCuz3qlcfRRtyO1YaKqQie3aNQzx7Pqs9VKZ\nEPG1b2n8fx2rB9NGlyMOgEwb/w2VQE6hg6wtKyTjBHIgxDumtmkGqHSmqZAf\ncXCk\r\n=wFFe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./inde.js", "types": "dist/jiti.d.ts", "gitHead": "23a8e9fd460e2c207d81475820dbf3f01eddb3ef", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.1", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.6_1591909971161_0.2489744494133448", "host": "s3://npm-registry-packages"}}, "0.1.7": {"name": "jiti", "version": "0.1.7", "license": "MIT", "_id": "jiti@0.1.7", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "dist": {"shasum": "7d28d29ae69c91ff5e2cd3184f2ccbe0607e93ba", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.7.tgz", "fileCount": 12, "integrity": "sha512-Xo17OKUtmnevMRkUoyRZo6l3x8QOW58keRb2kOAF5rePnMTK+UmmQUejmEOxT/cUAeabfUzPRrjjFZoepKWngw==", "signatures": [{"sig": "MEUCIF/Od+spYeVtoZHMhmiHaqOwBk5pxiI59xbI3qUAzIW+AiEAlRje1HtCqn7zVNTHm38dPeJU/i77M9J/bgVwHVOjrqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4p7ECRA9TVsSAnZWagAA6NEP/iTbOJRfR0PZPbcHKe1y\nAtgLJJuTXLxAeHW7CKzhDr2sFK/BrIW1WhJUChJv8ffynJ61nHyapAtdZ+qX\naT9GiA+XSxR8BolRbjVzmFKIWyO3OCNs0iLYy63z1xHs/g11niHmdHvFJ8CT\ncuIK+NUkSvYoDdJP9/8TJfg0ys8239TzoquqqJTksAPLAQjvwjBtMvpUfQws\n7z6dOB9fLND1J0ISpCRyWMz/JyteMsvpjnt0kDzxiD1pCIOrHdRcv/W3gfsK\n/+xcFqnClCzFynZxhfMBFLFd8gSUb4wgW5gspyvMkj6/BYJNqkf4mrebymF1\nFyJgWt+ucEApOpWXcsjwE3eiFoZ3tN8t/48C9F0AvQBjL0Q7ZMdDPOulaRoF\nmwHgccSJCEFOpq+dkS/UaVSW5JcT7mldp+v2dnBbckcmpeqpZiUjtfUjS28m\nnk7G9/KztkO+Ks2QIcr6a0R/ZCYacFfJP6aMifRZHWMj0C9Yx9QAEYED5eSx\nio8rY5ebLpFbVOL1lnySqCCj+P5jjRrldMRr9cpfkarlE33YhIyUA0vMq3Q7\nRRnVaJO+OIl8SaEIdLqAh1pVFP37Ws6bijpFrMRHWuEuaVoEV98ev1+f8SK6\n/jJT9orYosP2thx22hxFwfjD/L1maNlWbI/Y82GFFVCKY/sZiSljusF6MN/U\nRYAo\r\n=yb/U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./inde.js", "types": "dist/jiti.d.ts", "gitHead": "8ce40e9f40174e35a37f6a01e8b83ed77a02b416", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.11", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.1", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.7_1591910083977_0.6032067476896357", "host": "s3://npm-registry-packages"}}, "0.1.8": {"name": "jiti", "version": "0.1.8", "license": "MIT", "_id": "jiti@0.1.8", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "dist": {"shasum": "43af51cf305509d703252aa09438d3ab174e5947", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.8.tgz", "fileCount": 12, "integrity": "sha512-tb4Tsi7TEZqdMGrqSiuY0l+f+w+sEXVPKvJYnpeDjhyjpQhpTW5lF0ZDXuooLB0zvg7SseLfw/0uJ2Fots4+HA==", "signatures": [{"sig": "MEYCIQCCtcJEzb5R/UAZrv3VPQFZmiRoXCe3RnESQzxumIG7sgIhAOYdlLtGwbntx4SjxRAXuxpSydVE20mnSEKZjPHJJInJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe43P8CRA9TVsSAnZWagAAOigP+wS49yKvDcsYhdZZZIkP\nLDCIJlFmSCywypbtvjBkx3vOw9bZX/MCl8q6TEqEIFQx90jnW2m0sOY9S0Pd\n/NvriYaVzyoHOW7IKXix8sTtSrC1aqOgdO2gyawmO24XPD3fUJ5Ipy4KLvYX\n1LTJniX962haeaBTi6mn1Tm6Qvc04K/Y+GOSKABPM/287XQUXRy256SMEvLi\n5PiKRNWGqxelqOwKKMfF0zDA9txEQ6xqZhX2TGUl+0CYiGh8uotDT0TIioxK\nxpEyOOivAb0ZHYxpW9uTDgw1ClrTNN99igDuOJVjZSVmVBuADmxaoOKQuYjx\negxfEIOEVQad/B2jVckaivyAb1yjIZKivCQ3u+dvZLFro7bmEI+uhUCm5pLR\n17WZSzLMpfCQnoKTMpWPMWtZbk1BqdK1DHwxrjNcRuf+cOeqH2vi+h9aiu4k\nk2XOCy+RUVt1Se33sPGfat+WDmnF6BY4Zcu3C7+rbOpVMIVVf02GpegONrTW\nNIVBdxQXbC8m22wvqAj2OHn50SNZUGyj7MAepzpST9DBuFQjkjh3yR15NEcv\nI4zjg/14jVwhNkMWyBLM3729I76utSPRrDx/fxPWtu/bxYw6FPxylSqQZ035\ndF3xY26hn8Dvn2PwGbmopt9JLt38lM9AtXNhf0dx5Xc34HCrbrggbsKJIzXJ\nMukP\r\n=Tcce\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./inde.js", "types": "dist/jiti.d.ts", "gitHead": "24a9e8dbee3435f4fa2a7f646b40d18632b1f000", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.13", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.8_1591964668232_0.002652584789114565", "host": "s3://npm-registry-packages"}}, "0.1.9": {"name": "jiti", "version": "0.1.9", "license": "MIT", "_id": "jiti@0.1.9", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "dist": {"shasum": "2567bb84fa101582716387f1d53b88dc03f0e1a4", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.9.tgz", "fileCount": 12, "integrity": "sha512-HR7z/lkCbx7VUdjWaAUVMU5gSDMg9DOB2NoKWAoN8oYMurosG7UlCVvmYCt1ADn+Uf+wvO7s9LsfiuPYzHMx9Q==", "signatures": [{"sig": "MEUCIGnYlUufW1WoTpFz+bSoeTBvZhFtZOb/MkN+PrtwDGi7AiEAzaMnjodREzN3x0hp8OX7KI4zoDJZuCA5QvcHuYNUu34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1142854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe48HoCRA9TVsSAnZWagAAyAcP/RqUNziBuDxbzMRrdjFw\nm89m/0OHnJFkHy0UGi2aEyCEtMIi3Yhfs25rAq/BdBbcmUgjrj+xU8WfLA7g\nu8xwMVHsrnHuoazAjLzpJjp9nSoOjPkrZqCG4TIZ1Fh7Abu/h/loXbCVosIX\noqtiJ2IU1BSBr2we09ULE6DLsrTCEB4mxPe2cdVj01eQpe/oussfEsIi25KA\nsV1KQ9kUF4Vx1OxIu/o/5RrYEHF9diqn50xv3sN0rxVuSl3hdLZO37Arvvdl\n5YCKONXoXEEHI74nijw++tiRFhA/Hosahe1hWu2B+6LhyVV9nSY0OPSxgvoi\nvL2t3hVc2sU64qjiBsZiWKvxNgWQCq49xByErV+ByvCCyatcNvn+G+tSg8ye\niYuqaooZwGCuYy2YrrDZf/6cdI5QC/gqkqIpYrhxrEikBD7X0Vy8OCsFkTlP\nI3Npyb7zZ1D1Tjo7uEoMA9225Mpd/Eq8qHcZVgxbVirmLds3e/uSqq31+LCm\nuwtY1FdSzU8NPG/Iy7EKh1ZoEHokZizEqfcOQvhv/PsFMu5Scrw+KrRgIUz1\nlEpdjoAMxz6RCITLVbeIvTFLZgFgdrH2wdQTSLUABQFZyHDQ8v9pvV7KA4/n\no4d8s1HD/lEy2lPiI10fCJrprW6E72rCYkC0au6rZ5ZmgmP/QTAkx6fWOjCs\nKzPK\r\n=IVyX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "14dd2e75ec32de24748476895bd6e1f40fac7ad2", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.16.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.13", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.9_1591984615871_0.7140184785216817", "host": "s3://npm-registry-packages"}}, "0.1.10": {"name": "jiti", "version": "0.1.10", "license": "MIT", "_id": "jiti@0.1.10", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "dist": {"shasum": "28be9dde1a2f6a06ddb536b0a8ae0c4c33a9b776", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.10.tgz", "fileCount": 13, "integrity": "sha512-y5cWkAiTN/3hwZ0cd2Bhv8nMKQ6q9lXXcmKe8p3XbDUJ7UPzb55Icz4ZORwj05Plz+n1fMGcHMpWC5YgzIFCmw==", "signatures": [{"sig": "MEUCIQDSXGsCPlpCHONqe9icjAMH1CKxEgXwxQhIMo30sIVLqwIgNHDa50nVG66h8Z3yixfRu1SwHYon0J0Q9/ce7ydINhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1143249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7NFICRA9TVsSAnZWagAAUsoP/1LqTpbB5SjtZLHKH789\nm+JcWb4hgkTE8xXtaBYi/LIH5omIyTcidluLPfct7rgraeWRXDxCRRWBnwjo\noKKWlj2Kkcc6Q+2rY76lEwKTqEdx0diqVgwHt+X4fEmkmLCYmmeKunkihuu7\n5LOK5vLhb0VZCSgQzNZs7E9LD+JixUePgFcvDUNt/lpXxAgO+jbpCzkUlAek\n/YPR3T/1hjkQD53JHYtIN/ZVv/UKVGcamThotXWFSGTmG0V7xHZidYlrr/rf\ny6dtEtlN4xLuzox6yeIfS23h9cjMeiZAUcmty3JrYkG1RW8mffmZigapogYq\nk6z/3lxv5JsggZEAXMTRhqGx36qbZeeIpffWCjez/9aDhrEpgzjxBgDwUyEx\n0Xu2JHzJw1FTEs250mLZHNGgyDIeC2Zg71i4cuGiT9Q2btd3hLJs/IHfZTyS\nC/nYfgzUipKZJE15ZfeDM0ReuwGo+DSVJzWyuZT91hABBtZGRnB540EKFDKq\ntj3ugGsaM0x/2Z25iz0c+ovrtMjOleassFHpajScD6yqaVf8sxJI8IBR0x16\nPevgIiVjLNsbWlG7I2Q8gfy737J4xFmBOi2BDVON2gMHDd42nQVrTAb9w2WH\nBMJ+m0RJgoID4Ea82OHObGq+OYbpxr+O0676frQ3Fw7e4uo78vi6/cQk/mVq\nVS/p\r\n=+3iJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "3dcdfca6e69f4db3b50f75e1e19f93edefeda575", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.13.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.13", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.10_1592578376184_0.26948515958574193", "host": "s3://npm-registry-packages"}}, "0.1.11": {"name": "jiti", "version": "0.1.11", "license": "MIT", "_id": "jiti@0.1.11", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "dist": {"shasum": "8b27b92e4c0866b3c8c91945c55a99a1db17a782", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.11.tgz", "fileCount": 13, "integrity": "sha512-zSPegl+ageMLSYcq1uAZa6V56pX2GbNl/eU3Or7PFHu10a2YhLAXj5fnHJGd6cHZTalSR8zXGH8WmyuyufMhLA==", "signatures": [{"sig": "MEQCH15xatwLpXrTJ7dZ7ER72FS0EGysXwp1Kslqr8+xNEcCIQCzZtUJYVMQrMas3ZVrNaCUzKo2TotL+Ril64u59GcEZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1143582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7NIyCRA9TVsSAnZWagAAeu4P/1/U5WcDQDVLf8SNE4iP\ntp5QaIH9boCZaL7P3IvAhm2jfPiFrcmTMPy+v9Q2vXM2qd61SHAs5QJocO1P\nE3OK0Aa+ZJ5vW1qQrtdPMmNzykwsHZikUqxh9u3NJNi+7s/sarfmXqQdsfkT\n5c5FMg7U+C7I19BFBqOMVqg+Cc9wL2saLQsUBEN4gJBKqZJ3U+SHTWpXzJhf\nQ7/sUMH8dogclSbMPpoEhLYtqj3GHi9nIGxUU8kv36cuD6Tb3QjxqZcnPQka\n6iP4bmwVuVoTc+CDHY8tcJbITn2n9pACBE4J7w7Krmvna/TY4qHUjclIwQcr\n/BDSizDdvRbHfAOorAMZLpAO2B6wkLsPOQ8Xjq0FGrcvmT+hhoxKeFaWqMsZ\nNKT88FrQSs2BZNgOrin7CAiq8ufeVOqkDVDquZsraI/LFNM83B5/vv40Yl1N\nlC51WOQ9XTuxmI2l82fRMcNdQI+QEWobbYZKZElML/ij1QVMFsYvnL6o6+CL\nNFo5AT+yrIW0jkVBR75ZxJZVEyaGx3hzUohuCwsBCS/Y4zApgoUjTWGSOdNz\n15gUIx/NR9qzpJpmwlqt+H1Xg5CMm15rIkHWRajWJKYXdksgNelPXSSHflT9\nYxyv0SsRxWBiEimeC2WnsPAo/2Fnn8q7LKIP2i9JwEKqqs9FDhz0NPJgCXZD\nS8N3\r\n=j6yp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "51309a1a5aab733fe98d4ecc50b76f1da5e27544", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "12.13.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.13", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.11_1592578609463_0.4546926647182583", "host": "s3://npm-registry-packages"}}, "0.1.12": {"name": "jiti", "version": "0.1.12", "license": "MIT", "_id": "jiti@0.1.12", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "dist": {"shasum": "ffe4a09266d739b77721170fb73115c50b680330", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.12.tgz", "fileCount": 13, "integrity": "sha512-rWtqsaABBGNzTzfQkfP7DUvE9KGEIQUd6s0nITw5rDW1L9OwwtBQ5dz9hMq4YrMZqrSJr8ArrMvZ1mV1/7S3Fg==", "signatures": [{"sig": "MEUCIQC1+//DYtgTzta2a6IvezppeV8B8IbUcofvpIbm3D4SlwIgPe2bsmpYPatUT31Z1OrJg+z41v/VPEE0/vC9GKcwZUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1143986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnyX1CRA9TVsSAnZWagAAD3AQAIwFKkXihj5lfsi7diCY\nWv6mFIUR7hvZQp/wKEa/IxS8/pQp1NnwaWZimzVFKTVKzh3j22libbe5z8dY\nwrxPjZWHK4ktm7E8UB0IckzWBfWu1P7+psbJiD5gl9ZMjXYNr5MyHyw9GaMy\nGS9eWK25T3x5h2p1xPbw4caR5KNOpJIv4MxikuK/LUiz6vzAe4eLEAOkMBGH\nxrb2lJqNWKUXGJvBDsQVjpS5dK4652aKrE0yLEuSL9ACd5yafx1IMS5E9Wq0\noilVRzmlhamfPRNiIcc61Vt6mWYHDmCjpLN0nURQFeq4wgmnRHXuDrN0l9kG\nk8TZ5cZJt8BqldtdIqizuEzPd2LKJPHpKYbasoUXwZ/cZinbAkv4k1vLIAfN\nc/knQfFpRNrpCks4ql5IQjVVEborQmUiz8RCkKzqRdJvSAROloHQGquHbTuj\n05OXNweDL+pUfHYKqJt5sI+Sq4F5hyioq8qEP7yORoiczSvCgpDh2PkYGqi1\nX09t3mizaVF7aO3K2rCIeBC9I0/da6VPUvvET0TJauU4aOPMgrmXHhol5bh0\nHZTToTbgnrL0IWe0Axw+50nFAZHQDF3jTiyfaAvKfz9qyXYu8BzEs17eC5iI\nucs9K6BKGCs6VnkOWVyHbTgUwhZpazyFaD49H46IW0Z3ormQSZCq+tdhtbJ6\nMrjf\r\n=PZS7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "4a17862cc87242dfd152f2b8bd7ca89e77911a30", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.13", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.12_1604265460391_0.5687354050312827", "host": "s3://npm-registry-packages"}}, "0.1.13": {"name": "jiti", "version": "0.1.13", "license": "MIT", "_id": "jiti@0.1.13", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "4c5cdc23f35c0dfc128c700cefe7a3ce64a3f38b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.13.tgz", "fileCount": 14, "integrity": "sha512-jU0cGhWMMMB3gRW2J2F/yJ8LU/i0HUvPLfXKEiwRiMklIo8SvtJOssqEfqDQYGgWh0BU54EnsL1w6rRKGZh3Ew==", "signatures": [{"sig": "MEUCIQCcXhnup4IX0driMlGhUW8ZOgYrZpO/KjOS969aJe+4fQIgJyFVMErSXqMv6cNh77nEUBpCBkjBjgAHIUksh404Eww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1144546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuYGtCRA9TVsSAnZWagAADGwP/3uTltoxtNrRrP8aDd57\n30gUFJL7FOWdni6l37N0oSbPJI3jDZkOlN1Lyufv7n0bPJIfRmTI9cKtDFyY\nap3Z8xaz/JfwLa/0g7hv6YR0bQ5RCjswDJ5Mhx1zZEb1dFEoUp7epqCyrelX\nEEfaHG2+LIZABZoCTvjTETayGVpvlDJBQ29AhJS8btEH+ETVQiq0LAbW8/UC\n9Tz00HFxYcs3ZLTI6czcPE0qD0bQMmc2lkYNoPnNOa9yqC36P8gm2MPU/hnA\ndUXriWsR1ZxPtvYs01Xe3g/aK1HSDjj4V2A3SCVQ9FEE+0x6t7HWYgxbCUYR\nuwVGS+V83B5mILxARZO4EvFPjXx3Q4o1O7EQ6Iir1r7uvqK2tWdk1eEnH52G\nJJdWSbvN7SEJvK55KIS6wTuLnuGVd6+YbGqV/JwVt7kL4xAF3CC2SJqOMFD0\nTfXIofsIUzFGnZzbO3s/cVGYdTZDakEXk1LJOW1kOlnue9aWsu/hWcqaHyEP\nDcmCKYun6f0npIoZ6w/whqUWgUD5XKNw9l0XcR5Mlt+XV8QINSOuDQj4AUsH\nHYlNrFpkbDIjOXx5pWW3TLO2yAff0inS08QCPH22jHNRBgHJBkxuCgf77XVh\nbToqUMWbpZVh+72SoXD6ZXqs3Ah92BchT7KnBKONtPrkqzhGwD7M9UxDAFPD\nFEbe\r\n=HTAJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "8266fe36c19a3f327e8b297dba98ee8ed4b07b3b", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "tslib": "^2.0.0", "eslint": "^7.2.0", "mkdirp": "^1.0.4", "resolve": "^1.17.0", "webpack": "^5.0.0-beta.17", "ts-loader": "^7.0.5", "typescript": "^3.9.5", "@babel/core": "^7.10.2", "@types/node": "^14.0.13", "webpack-cli": "^3.3.11", "@types/mkdirp": "^1.0.1", "@types/resolve": "^1.17.1", "create-require": "^1.0.2", "standard-version": "^8.0.0", "v8-compile-cache": "^2.1.1", "@types/babel__core": "^7.1.8", "@babel/preset-typescript": "^7.10.1", "@nuxtjs/eslint-config-typescript": "^2.0.0", "@babel/plugin-transform-typescript": "^7.10.1", "@babel/plugin-transform-modules-commonjs": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.13_1605992877237_0.7818800766233363", "host": "s3://npm-registry-packages"}}, "0.1.14": {"name": "jiti", "version": "0.1.14", "license": "MIT", "_id": "jiti@0.1.14", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "ed77008bebcdc8f21245021746ae56b5bd4452c9", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.14.tgz", "fileCount": 14, "integrity": "sha512-xr4fW9wNEIpo6uK6Xyq3pc26SNGlVH1HKNInWwQTIDaOVFXgsQZkUVWKLaZmbHuC59rABLQNwwNlZ4cfJaeh7g==", "signatures": [{"sig": "MEUCIEsU/Bj5Qrw9jLIbbisWrpfDPgsCGNBTauy+nHFtk7CNAiEAl0zl5bNSGhOi1qBZEdyC3/RPHzL/6rHHsvGY0Ym+iek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1158780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuYPPCRA9TVsSAnZWagAAn9wP/iyS700BfHnw0thJicUa\nI9mODD+NLSTClG+FvUElBz7I1ZiafHCwSSrj7SzX2tnpXPtVYNZJzHzqV/o9\niDtlegIptI7qjQLFfrTdP4+M3u3yCLvvjgpckYmRJ/cxLN1/kuYnL9DNtoIn\nte33skv3ExT8QJ+iXgDcmD9or0uunLSavxdauFzn2q7VDzZcmAkn/YrXPxQ+\n4plfb5jCk5o5u23xireUfCjB6pVgV3wtcsmSCuZdAH9cNQQ6o2sJmiWuTruN\nEYAEK2BXjL+TkCSKP1DwSlDHrlI0IacU09aEmGr+vJzvI53nZF8KDoeiUvhx\nlonVj/6/AAA84HW+DTnyFn8xzNV1l8L0kdQhx0TWLVWGYWVqNl5NmRVpnCrT\nlxarXyvYjxZw32d8JNYf8Gg7tfY/AXfCG8NhVS8/vTU7jVwtiGLWwA8pbMWc\nmaE9Jl1j8YQObLQRgTfHL8DIBXRKOtuZHa1JC81pETJHt7v2ACgatDl6FjuW\n83MwNKYG5IcEt78Ry/ooO0kXmjeFKlq7tv5Nob37sCbpOAfXTer7aNWcg9ks\n0NW4+J1/IkmtEd66GWfOxvTTazIRrb2/s4FSKKkqAvyx+1YZsrPQTm+v5tgu\n62aIOF/sJeFTvrYSva3RhMFMnNmvVkmvEVUQf5BOAjXizzulh5e//l8aagz0\nJuHO\r\n=4pdQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "7838c5eb37c4433511f781a3cef6fda0c37a3483", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "resolve": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.14_1605993422851_0.5416791901279863", "host": "s3://npm-registry-packages"}}, "0.1.15": {"name": "jiti", "version": "0.1.15", "license": "MIT", "_id": "jiti@0.1.15", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "4ab80092b5e14e6b4d41e645940e6b818de549df", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.15.tgz", "fileCount": 14, "integrity": "sha512-H2xOyAnmrHcl1KaJ5fDvcKgXbps852FSTMp9v0F5nBpX0NET1xgkLFUFHNHs4i83u4ZSBXLB0qDwvrV8/lOAdw==", "signatures": [{"sig": "MEYCIQDaBoS2iqKSCsqfpujFtGoL9nJUnCzqhK+FKctv1iIBRQIhAMT4+0SYZpIXmyM0Szfg+1DrFqrhexOCbRK5o84ZCaGX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1159574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfucapCRA9TVsSAnZWagAAre8P/iMqXeP617p34oWojNO0\n0ySjMRJWeOVbRiBIHJ6uib9W+gCjgMNkfaQMz3aZzCLQk2Q65PwBKdvj4BcV\ngTDJ40fMYeTyJ6n2G89522B7HB4H5SgZyUIKMhnhwUhlfJ2cSUbdxOmTrj6H\nyKGx1t1lHx0whG1nv9I4BxMznWSsIMwANK65GGyaNnbzQjKUY3pRizTrpLvF\ntkuYC6ifNtveaR12p8xyVlF2WBbh09+Y3f8t6eh3WW3VZr5TnfBktXB/HZDC\nW8gjrc9xzxkYCDOAGdx0DTE/O9ly0X0r86c8gFCoGuLdLEcQMWc3QetVNpSW\nNChqhWERpbPUzqCk1Z8jQVbrvE+7tTJa3XqCio+8aoSNVFOoReaBnFKO4/5d\n+5BVIjj5IFBK72nUZ1CPpmVhYhLH3O0gfeYkbc7QTKH6uVkHkbzRL3/2Jx/h\nJ63/9z80ffezOsAegomGheG1HTex1sx1ppd7q9bKfCpArjk7stoXamdfv0+T\nl1TV0VOe2n+oXBhxRHiF3g8pn12uAEKsde/KVGmCCIu+B4edBAnEYqCsXKe5\nIlgdIbRRqaMxaur7IH6OPXk3g3ySjpAGm7kOY+o3rRbcD5Zvjg+mjStpEgg5\nUk8CpsOlur5AygjjOW5nn6ktwJJZZHYQHjW0VOqjksKhP1yoNDI8NXXaS/bp\nHcB0\r\n=k8Pa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "30f2d36f64bd3a8670c473435a002415fde4dabb", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "resolve": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.15_1606010536559_0.5825856134594611", "host": "s3://npm-registry-packages"}}, "0.1.16": {"name": "jiti", "version": "0.1.16", "license": "MIT", "_id": "jiti@0.1.16", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "f8c7d73b51153edfb43d3e5a4984537198771650", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.16.tgz", "fileCount": 14, "integrity": "sha512-fyMkReB81k8jzf9V97W/aUs8FQP/c4+jcP1+y/7yG8K/I6yhNbKVK9yI/GqUAn9WGLIMqLK95UgtQPHX+j3M8Q==", "signatures": [{"sig": "MEQCIDLQ8Xt1ZYQ8bEK3icDqqQ1UB/kpqR1NHt8ROE78SU/5AiBX+OwV5ttmso+MGqSMCEkAJaN0YoGYWrxtaqhnFOVklg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1161181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfu5iZCRA9TVsSAnZWagAApKMP/RMf2HmCbMCtWxRUt5Av\n15NdnR1Lx83aLFtQeMOrXjFEdNfDI4vI/MQ6BzA+Mc/Qq1KN0f0de4OOt612\nnQLKSs5Sn2qqikJnzTU6tvyoDMnXwhTrNEs9Vzd/clW+DstxdcfchVPBBRxg\nPQbYk0Z3QCM5pVj5dvZ5bZPMRhTnfafStB2jh4t1hS5rTDf75xC0SBm8qft/\nAtMCJyQVSbVk1lQiShVRBdWuZf1EvhiGm6pcuF/AMsRg0FVMutYK/LlXbE/w\ngGdjirUok50HP/1l3OyYUZDFn1wRz/u2cG2dxAeOOozRsdHtSXAsx/4b3WvK\nRJwERd5igDtki288r64YcOXd0hY4Nmv3mJfIRijvC66hDQx2LGPFcNp678Pl\n8O255b15X1oyGkxC6RiklvX3bAWMgydPKLIDmLS1YGFT3xiDx7bGoBOqpHYy\nIK/za3nQ0lvM6RDnXsT8tE3c1O0oyeszNWo9mX5R8skTEGxSKxz0DNiMuKI6\nlLPDZx7ENeCtP0PBHTN9uIaLMC6FUMlN7mV2EDtu/juuwyF5113muwq6inNb\ndzvLSaCJy4ENh2Q23A4SCEcHP/8pZDkNUu/TwcEUN1UE5Ct+xMgUwijBhhzV\nK21mTjpn+TWWPQZ8kiZbrVPXHLa9ihej2pj3BxSiqmQ1WdHHLDej9qn0Hxxg\njRZ7\r\n=SChq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "types": "dist/jiti.d.ts", "gitHead": "2084232246f66df5165e2a155e59e4485361f06f", "scripts": {"dev": "yarn clean && yarn webpack --watch", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "resolve": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.16_1606129817449_0.2641875293887954", "host": "s3://npm-registry-packages"}}, "0.1.17": {"name": "jiti", "version": "0.1.17", "license": "MIT", "_id": "jiti@0.1.17", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "b693a29c94d0ca4f82a4624b40dd9915527416be", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.17.tgz", "fileCount": 15, "integrity": "sha512-IlUGuEHKA44dqJoSqpv1poIRyyi31ciEmpLlRZCmo9TasVSZhwfmaVUuQVs26EHuwYdx+NirOm41+wbykH/+9Q==", "signatures": [{"sig": "MEUCIBMnXEH3ftfYJM6un2o3+NGPjkl1fux5rzMf2pBjqFmYAiEA78/4lTQH2HU8eltuBRREp1hp6Ms+XFtW9iXHQSgjkt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1162057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwYD3CRA9TVsSAnZWagAAonIP/AyVKS6RGdPeEx3zv2R3\n88r2YN9u3UzuNE1HKiOBusa7HTzNKNSceJhnvi03IDL1e1Vr3w4w7g0q0NCQ\nSxDA8V2BNi+6IDlKbnwiZ9o4/UkF/8sVmR57smfwXwao1jis5XDBFJTJFBYN\n1hJwPkNrlRbP6PbHVuBPtnIdVv3HACBksZlwV3EzDZOW8rOmaZxyy/xRj90g\nBT0skfchwC9b/dNa0N2MOz/DvSGghy2ww1uA6pxymMorZACKkFB9Mpt6CSbD\naqK9ASJtrwuE5uArpmES+e97bp8gPHhq6D3nU9JMjQtwopEBtLhFNxYit87H\npsNu27IQL7A7SPdmvQ3T5cLsf2PJnty+SiHmsaTuaLvYhjOoIZbkaFGK1NZA\ncgkp3llobnidB0eAIXAZi5zHMvAdjZekjIiTz8odNtjjteZhgCovSdthHv2Q\nngVdA5coEH6c+25QeyKXO4hUOn/SF2ZNwUgEllSH50JHZyL0q9x0i/1Fs/za\nnWhH/D0DF4n5xgmydCG0Kb4jEzDPETvZ5A9tB0j2eaGdYSCG85K93CpjXU9H\nuzMxHXLnUvIPskRG/iP42NGkqVh75v71XXI/yfjN/Gd6NLGeoAb+P6XwsqwI\nkPvqDi+69pfYHApRFmUGmNiZWI6LGplW4t4HQeUt3Acpmm93aTVoyHuyTRzV\n+N/L\r\n=DsyY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "2e753c4bb0d812a3c04017cc86bb4f4fc0ef7b9d", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "resolve": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.17_1606516983277_0.9445353124690461", "host": "s3://npm-registry-packages"}}, "0.1.18": {"name": "jiti", "version": "0.1.18", "license": "MIT", "_id": "jiti@0.1.18", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "1120c59f538667ab128ae9d7edde96ab837d8f33", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.18.tgz", "fileCount": 15, "integrity": "sha512-UUoooMMbuKJivBJL8tH+Tkf4gdF4CcwdaswrBW+HJxme0L1NWjXEV8A4wxgHW2s5rJnDM5NoVmuUYT6PNSgKmw==", "signatures": [{"sig": "MEQCIDkMiDns8J4+V2ysaQcVeWtiw8MRxrCsk6BCeck2G6LuAiBBXte0R94N7SJhEdfnTOYIq2M4Gr7hkndvQDRUeEaHjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1154442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4kVtCRA9TVsSAnZWagAA3WMQAJQjLTg7GLJRs/njSXT1\nC8PEVLE17vWWgfendE8lsx/Mhd2t016Mk57iOHuMG9mSF229kD5RnxNsz7Ll\nTnJ8iLZVJ4cUZhAn51eV4OqSfDVhoYXGLyCo/FAuDWyG4Pv1NKUM3otgLq8S\nJFjK2R89CmfxT8zbHQ7h22eAXAmjKpGJ+UjR28ocLuA9Llrxnj5FemUvEXHz\ninD+1XgpZ445rQpf/C4z94ZA3kIhj4jFF2VT57TTTrRQBPGPprvezGoIqB5H\nQrIkgWn9l9lq63Ix++/mQo/3hGLdY/M7QKLpxd1C3fCzEonjnOhBz1YF6bMj\nCW5DhrQFcaydMHRIHf+OfJVD7ansid2p0ucI72u4AI2t7CH55ia63ne2bPv0\nGJ7J76E736yhJ376JYt/VymfSiEYpOI0GTCXNoDSFrqnAFdmQK2FpxxiiWpF\nAbH2GjJBh4p1bdWclc54XayWd25F5EZyUvcG9By8KvmBqvKdSz1Q6Iy2lp99\nkRBe/ar8zggbQURbGmSBHsAfN0/xjzYrNiRKa34jr2SzqkWJTD/2sKFtlffx\nfpHsxx3qWC7mytd/mhafAk18LTER+rfbH17acZUr0joFyCBDyDB7TOStRCTK\nymtnRBXBz1aul8GXwux3lhJOzvbayi6wV3i6qBOSM5HroM5m67TbLvNJOsdM\nPHdu\r\n=QMHs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "64df06d25f69a5879ebffd65dd8688818d2761cf", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.18_1608664429172_0.878937904944572", "host": "s3://npm-registry-packages"}}, "0.1.19": {"name": "jiti", "version": "0.1.19", "license": "MIT", "_id": "jiti@0.1.19", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "455f93e0c92f26c9cea0af48def3eb4f567bbbe3", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.19.tgz", "fileCount": 14, "integrity": "sha512-ElDW0w/0ATX6IQwReNYMVrYkHu8PqIOO5t3lluceP/saq4SYz4D3uSMpU8pbn/RX3H6VYVPc0PnKn2WfQ5kiwg==", "signatures": [{"sig": "MEYCIQDIzjxf6QXhdjtJWSJamq6dauwWnanXnbom6ozaDY8rTwIhANhu5ugNBosCAN5XLB6lJoejFiL/NXcIpj0qZUzAnlBT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1150464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7GBBCRA9TVsSAnZWagAA92EP/3+eYVCZjQH/yTSWt9HR\nkPh5V54kkjYPI6X91WCB5Lu6DqqlmbUeko4+sC6cT/ZG9wzQg3K0lGwJnLKZ\nKk9wdMyAN5NkCeBPOgk9U5Q7fLYmqz74CdTWWI1cz+Brty0q+291LKv3P3nJ\nvIPUfFyvXFoDKkuwluocAsmOzxC+X6gtV2oaPzzWmoYSo5VOqXOSCqpnrd81\nvGMllP/aBadMKVjSG8iFWTmuNZel/spq/lG329f1fsGyOwhP7UV+sxOZWVvB\n2CTA2l34W1qr0aXOuo5Z1uzmss6PDxn/en6Lj8kKzc6AvG2Nj7G0XYfVz6e0\nUppa2pD0em1XET1h9tUjXt1iaoYcICl22GwwHrOp3vsKoYomFN9W19Vsix4M\n/wkwZlvRipadUW3Cu9lM6LGGLe3OA88Q4xuBcTARpVpXqqkJpJqOi2z5qN3Q\nwJ7L31QcaRUaEXO2MX0soB9bOWIQPiwYcSHTiUXwPNzC+WjqeKl7744mSTEp\n84dP6z/Kk2UNc3TuexlotZZjYfPF8JE176ZkVWsNiwMTQYpTk+CaIA8oc145\nBGVrbDwwOSYDJz2jI6Uwc4hhYDDzpSQhVxRj7jXVXEmbMgZ0NJDZN9rUCK1Z\np3vGFCb7Tb2YHzSAE8MvCj3mCD9dCH9WcvH5GmLNxI2W0Cfoj3Odrkkr/7JE\nWaYt\r\n=nyE3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "a72e0f4ef6532de38e11436572749aabda218d41", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.19_1609326657342_0.028519463439000248", "host": "s3://npm-registry-packages"}}, "0.1.20": {"name": "jiti", "version": "0.1.20", "license": "MIT", "_id": "jiti@0.1.20", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "5fc8d4793f0e04e552f35c4a058ea62023e1bd36", "tarball": "https://registry.npmjs.org/jiti/-/jiti-0.1.20.tgz", "fileCount": 14, "integrity": "sha512-nlsuibooCG5yEjmGSVqxhjULy3rO1Gl0LDP+HpUMbzOSLcz5s1Gf5cPnjvHiei0JCG3SXX761HQArDzNIfdz4Q==", "signatures": [{"sig": "MEUCIQDTRL4hT5s0KjHs/yXdZ/2KNb89hJRoxho9UuB9Btqy+gIgFDHqnMssrkyujjIkxeGTmsd9DR9d2JVZwaNGzSAHNYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1150812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/cRNCRA9TVsSAnZWagAAxEkQAJYbz9I4VbWcgsS+J/eK\nH7gV6AKS20fyRlNUYxxhRP5l6t9LN9S2gmed7SoZ29mzM4LOQjeaQdqX+DDm\nvGzZAJkh6MnSrJ99MgvAT+IQDBtX2p59jK0iae7qnfMcZ+uZ8HK/RXDDGFET\nXYdA3cz384UjyotaafJFGN3YxjWU3Tfel7v5CndMsqrBE6OI/m24FqoYnM6Z\n4Wk7z82uvdX3GMn+F6t8Gx1oAxSPXiJZB2DAjFg2SOV2DU0ULLWwscySEtXS\niVS+1801u5phWz3nUrvFG98yOY0JqB/vmpIbNBm2oeDo75Nmt7f30PRQsNAa\nMuibWWcqX7NSpW6wG9Ahg7AiTDpGdypdoPaO3efXat9ClltwFplyThOFu0UV\nTTY7Jv1E2In3u8g+PrFM9fHO/f1SvAhwkKqpJpMkCqmetCKHBjxQntQC5DYI\nbL6T64xYoUYFzakoIHDBKqHjgNq7vsCcPgnhDGeAo2HDZ1qY7vIetuQIyvLF\nqcqA4Nl6piAGtOYegzSxqP8en5CkjyzOEWmwBakZDO0gXAssqZaw607TpCee\nUmM9HzJ6BYrnciZt+BGF1Rlzu+tNwXfpnZUK+kIqsS3WUJnr/OLJ3Kihtco7\nAfrB+OXxQTEZ2/Tu7EUqp7yCCAVeO0OGDfsrlUbBqY8/CfA9KvCjciSJnugS\nJ4I9\r\n=giCJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "bc7c8624bd258b63ca2dfdc8e8e9fd815b038867", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_0.1.20_1610466381352_0.7184578625275453", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "jiti", "version": "1.0.0", "license": "MIT", "_id": "jiti@1.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "508dcacb5f6cdc6b2248f00492db2b749684bf21", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.0.0.tgz", "fileCount": 14, "integrity": "sha512-TIU7sNC4Iuo6YgjOUjYFME1JR1He4j8da8jIyB0K1B+oNfzbLn9pnI7YTGcR8xTQ8w5wiukmSelo4qeUki01jQ==", "signatures": [{"sig": "MEQCIAPrDN5SoXHnL0VGFOGawBsYGrKm2hvupCI6xsz+vA/pAiAhr2rVJE2VIpEfSRzjb4hLE8zG4y7ZNuZZmdxfMmIbKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1150899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/cV2CRA9TVsSAnZWagAAoe8P/3p4zJoJWFltPMRa0Y1P\nMtq7+3AibQRkyOJ2Zssydywb0UxXfWZZgEKkw9ktdDPtt8mj+j7AGpl23Vgc\nc72XQsrG3IuydW5rd8Sat40Vgns3QneSZJN6gV8Mfl4i75xqpUP+YBpkbiHy\niarWdSRcV7YCNLWBgrOFoB7gSkJCTpx1A+cftDUzoRxu+vTuxqh+AtkDW12g\nkj0JNsQM1S2+89xTr/mxpn8730X0soKK3Ja1XmqCFrpUaTFflPpWf0Lqi5yx\nfFSKpmjGePRLtOc1ixMV65PlWRrFcF9PN0tIYalbCxHEGEHqms4HflxH60cW\nUGlOLghzVn/TKGYGMTSnda2EIZth9svOiNXsyQXaBQapSfzGYz+txRZoDZnH\n6XN7dKcA/yQrKOwvO0Ebn7/ffzBLFqIRLKtRBAUGfPXfeb8/WMnNc5AY/X1t\nYLRa59d9gjlOVl5cZrSL6j9yDGUg1N0tZG8uTCO1a4UDbqFMdQWI+dnRmfT2\nroX4nI84PS8ddFgq1AGzLzmj0VklMxDHgPkfmgkWAITk1tl+nMA+JzNyNx1Z\ni4wUxlFBbU+w/uWaoMFVCKgdqOK8MNRauQBmSIuYgJ3phZDLVnseCkaV8SmR\ntB2x2+Xzh6P7JlTN5tL8A9vjkQwIT3dhB34gbzrXy/x0YXeMK2MDKcbI2BAO\nu2sx\r\n=BslV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "a2e48dc2d7beb8dfd69cce97a2530c5ab696afb2", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.0.0_1610466677813_0.8537529841698845", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "jiti", "version": "1.1.0", "license": "MIT", "_id": "jiti@1.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "01b2d1b9ea01e80adcbafb08a7027d4681c6b270", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.1.0.tgz", "fileCount": 14, "integrity": "sha512-bNQ+heLBltC/62UQnFY3qYSHx8bJ1+gwEk4j/c9KAM/S1xzS/hzakuZPQztkzU69imuTVrihJRzg6utaqd7pUA==", "signatures": [{"sig": "MEQCIAS5BN5ZrEAqkDE3qN+dILBMAaCW7wiMA+NdEk5GzHZTAiBTD/TImSFI+BbatxDnbCYnPkjW/DAcMaO9Q3/NGIYO4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1151336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/tySCRA9TVsSAnZWagAAEz0P+wThPpzg0dos2z5886fk\nLsXRHtguMkmfefqNooBnRwhxYNI5qylGlRK2lDh6Ani1uZIXTU3O0yyCobID\nL8yIidheuCBwDl2E1MnFaiZsrC3XABkE4dh8NiQOOId2MEDjTGC5uk+gBpHE\ncQa9/PNMVeTc45Z7cVGAK4c5E5LDp33ny430FCP02NQTwoqbnsD9+zRHNi/z\nRtAMDvWO96Jprm6MFECk5lelQaiSSQLD7TREHnKsgVupAAlwIZv1rCaNV3bC\nWpOaxOM4Ma9QRj3qDIZUDmJIVGpsfccxrCE4GU6bV1O2ozpdtDaxb18z73G7\nuksoNSKAPs2U5NHMrpxTW5yQaUP+nUxRhkFln33xqlE/0JKPMis2Qb0UaqQI\n+nMTEUF3etREx17F57jjNlz92SLDm8ekAKV7i7vaVTtVe6SVneQ7j4YnDMKA\nbwLc5tyM4PgVUCoImgkMww/f8jjQRj5YhrdmFvhqGpKkEF34P742OPlaR3AU\n8H5WjbpARuuYxn+JtrrxnxV3Nl5B/jOxC9ldZlLDkXqK0hvdRUh0+ONcJ5oI\nIDVMUL13HZl0pB+k1wdO5h63UUHuBYz05OELMl7m6aeH25wL3G1TgTcIJytX\n1D1izBsUYUgukP1q7kac9yaUUhXkxMTt2XHovxF3MZQ8eMfb7jo9oPBUPHuY\nDqiF\r\n=Wzw+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "e95220cdc32b38291472aac2bfea194a7b24d4bb", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.1.0_1610538129712_0.4416236184343485", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "jiti", "version": "1.2.0", "license": "MIT", "_id": "jiti@1.2.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "15187ac6c49fb5ee4e04700bc05c1d5caed58546", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.2.0.tgz", "fileCount": 14, "integrity": "sha512-9YR5zusNf4qmOZAIMqja5pTeuojl5mG2sE4IGogwBoxyclX+Wm2yBZDo2nvuwkUOzUg/nmXwiI2Hz7d/d1UCyw==", "signatures": [{"sig": "MEQCIDforTaoSJj/XjWC5npXkaKsm2iQZEDw96o+6yUXxvAYAiAKRz3sdUVSy0K/0i6YqmDUhcowKPdlpfUaK0KI/YA8Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1151784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgADuwCRA9TVsSAnZWagAAIWIP/3ti8bUUZqfX1IbEY2A+\nk8Hm7ldPmDjYLFghuBRw3XLxmZj2LfK+NcNPnIRMI9CRuLuBgSJ/VXlMEL3G\nMReyaCrbLXtLmVu7m/zoibM2jpMSKtSz8/Fl9R//7rFgv6b4ObotG5K6OIm9\nbPpiiGTfr6Fjo55ADW1ue1jQVz5PuZ+DZTFUPnn3Tz7xwNN4D8R/Xo4rFs4B\nn+52GG9UoTKLoVOXq1Uc12oKfENcvufT1xEBO+1F62lnud8/6cAXw5zJHh9k\nA7mvurVnFa7WNlh8LZ12GAEXlqR+1eGTfalSXOFWVxEJwYOcfhhBbpXVpMJn\nFS7QKrM/EKxXl/J6B1Hvg13cu9x/N3ahzAXWK8YM9N1kSXgFwEmhxyhO+sXM\n/4VoAPZo3IPlWUps4y6dIg14exhSrg1GKYUaCd4i326zp9nEP9p3XnvTL/vp\nrHOBY8wZDB2+NYrChxn02BydAtj8snydYwdFOCljB54xNmt0X+0J8VjLtEej\nO1UDwaKAw3AVBOacKGn//msKWT3pMIT1Ze3u+GxB+rismaSdmT0EDO5gGlIj\nvI9CCXhV9qxdBQuwoWZcfPssK4dFZ9823BupSfHUZJ+eYR5Aj32xFqpJFh38\nH7GJtE8Ju+WRJZN3LS3j4smMjUFRQID+yvz8H6FuS3YUGsT3pnIq65HEArir\nMY7m\r\n=CU6t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "70911d7cd582307fd07608b06e224db26529482c", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.2.0_1610628016290_0.039922332765012", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "jiti", "version": "1.2.1", "license": "MIT", "_id": "jiti@1.2.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "e8d31d27e28b66db160da656b7189ee404f406f5", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.2.1.tgz", "fileCount": 14, "integrity": "sha512-boJtKVr/lTSMcRMf845TXfX3EOXbnrWiINgarCeOIifwrjs76g5iq398Qh8dAJiv0BGaHb8AmWjJRAVSuJ0mUQ==", "signatures": [{"sig": "MEUCIA5rQ1rXkvVAIzSw+uX6lkV5D1iMPD/dLETHeXb1Ha+xAiEAg5odr/2jOQlBAPdFvFrbjwMHttuV3LVIHgqYjd/Xxh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1152283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCB1HCRA9TVsSAnZWagAAhtAP/jEc+6P9+naabTwym43/\nCgrrxucSARZwK2LCWTUNl12Rl35HZhKMC+8FVhB0N8KUNNWxaCvkTFyFsFHm\nhgpRTNWGDZC1DT+coNhVBXIZF2ADQ0cbiwFKZHk2EOD3wE5cimZVqOwRT6kq\nr2nBjM71u5n5vHT5p2kOpdOfzpbk6iFRuiM/CffpzBgw8ZhJBgypTVQoe5cs\n8ZwKh2l9uKIEBsSL5wV2/FqTdlSfmI8Zy4k1PO6+pO8sgYhS1dlbjtbn/kWl\nkS5DhVp30K6YrTf50HdspMlz8CCWGZnklRW+aOx+LqOhaBvatLvfaFgLjxXJ\nVMLTos+287f1aGWmBMF4JH/idQtsZw2Onb298PFwbEfdsWpYcXkvE42SqwXO\nNhvkJbeTK7fyDFm+bA2W9ci8lnDG47T+zXEIb9zRBXmcjKufIJSlapBNUGv2\nRWT1NJBNHIityEgw9ex30xaOesGGInrunJXlYGS+bxLg1DsKBCnEWLTf8EyT\nYeqRU6CIG3ruMXDVJsCeduTjnBgWo+Z4w2iFvksTmNwY3dSlazqu0aygxenA\nf5g2RyLp6oeu5hXg/fAq2ytttl6udWkooaboLu+W1zjUFeW3Gozev32XCSyN\nFjxzf57EDbUmbPEedmVWS1ohaWjomkWvI/cb2TfCsmKga/qxXYmUlyQrH/tA\nyLFN\r\n=Pj6D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "a93e5926aead72f4a502b058c6fe7f7d88a6b946", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.2.1_1611144519181_0.5340568021059313", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "jiti", "version": "1.3.0", "license": "MIT", "_id": "jiti@1.3.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9c6b8bde5744f732f33c1aa27108fd03b9a49d0d", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.3.0.tgz", "fileCount": 15, "integrity": "sha512-CrRtGy3v7PE5dGWtMBe+FMaViXX2eUa7I+Cyo0WmyggVm61lfpEs1orBQalHx9KDIp0Kl5mNaXGHZCMgTpNDXw==", "signatures": [{"sig": "MEUCIQD45POgB97GilyAf2V6nMXVOom5xNRXyl5IUYVHnZNupQIgDMIHxOfYNUmcEVU/h/Ufxm5EVRmNJxJWKJw5UByhQzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1154908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCVJTCRA9TVsSAnZWagAAKIkQAJ5rPkLGnJT9LHVNI8xN\nJ3qmAfRYlbvMWXlZ+H2COqcweIFee5sh4Oyx72DHH7u2S/IkmS2EaegKVyAn\nvzAdV6HxbglXs5ffysePFDHgriZrhq9SU5JvpaGDGFtE0Vty0Uudk8FSsO1p\nvgOezZt+UTurqiY6fx+hbQuz9G1bTGGQmTItGCjElNGJNCIOCVwdsKwNSaKq\nohRNwVO3vi25hzIKN1aRJbDit4Q4eWCo+2v6q9E8T7gCOQ3lr+V9R5rKV5h8\n+RXDb9RkwolHk/xH7NO4vPm1pphLsde2/k46BLgQRjgnPazvcwJslQMfs83m\nnUQD4W0AIlHduzbK1M+TF1nzUXTkNT3Wgk11BUW28cPEqTSJxqGS8B8Rxdke\nuhZ85innr13z94hXTuO1U/Pi35K1fcahSuDGFLEGK8/zICFaQeq6iGTXSeX9\nMyohFJWRqlFm1KPL2ya0+ynkzi/wp38WtkLePGqjRfoMABao15r673F3tjS5\n5FwpWPxoxH0dZ1ZM/LnNc5MTkpudVtiDEamcdej3mBSrhovifwNOdPJJElv7\n4H5WYiT0JEbpUd4UsqqPiVPQznT3jkXzUibHwxMbgGHn9VCAatZ84326dbuf\ndYviVbyG+Z4Ft/VC0whvWe35kh5aKQDeevGUt/tgPGYlsXNO3ECx3WyiSeFh\nqNT6\r\n=GKPR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "e956a85c3e0b2e2264a37b0b1d76da22028ebf52", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-transform-modules-commonjs": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.3.0_1611223634916_0.9426293740853267", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "jiti", "version": "1.4.0", "license": "MIT", "_id": "jiti@1.4.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "2cc3df19003785cd700f5f5fd0292dc6ef1c2751", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.4.0.tgz", "fileCount": 15, "integrity": "sha512-riTFltg08xtufghxm4Ve6ITghk+rtg7gqD4YvH0LyjqMSxREG9sJmXGbJ2spNkUPdpl37QqjeQ0w0BtS70yNPA==", "signatures": [{"sig": "MEYCIQCw2uYnhI55efXjCOFnC0x/Ab5pIy1yJIDsIGsx8R/ygwIhAPrLKAtcT+R3B4BfsJ+vMj+EQKO8JP0CJm7FVooOEs8a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2632861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPMLoCRA9TVsSAnZWagAAN8QP/0JBKuE07ofADUkLp1sG\nvJqkViftr4JUmhQ6w2zyz2Bfhldg2Lmp9v3hhgWb+xFi3S74FWqk82smqOcE\nJV7bAr4roZD2MQ8VNEjR6QwoYIvbZe6NWRLx77tcQyCD1Rp2kEeF2rGdG1de\ngrusw53BDDDjKjANww+TS5csek7eLAnSdvyv1NStB7wO+x8urNLKu6VNUPs/\nQ+HFz1bUHQg5qP9bIiqyVWgQEZRfbBY/MXOL4eZBZ2LZhQkaAhNRJMDb4E0O\nNsclpWwInJr98RkTrN0rgHBmLV/vUS7O70XF0cEUhffBVyQxEyE6FagrblLu\nd0Aj3aZHul+sZ7ixNYEi+0EV6fy4afypsttR6yJfBEZOZloSkC3niFuJJB5H\npjLhdnVSqumvhYBCPOVCoskkFSIOqJr9FCkOrVC+UqOZP+T/XrvE+whHt/K6\nvSRQw7c38wSYqVio6Foo+wLkZ/mt/upmFkhL/3wURgtUo51x3tLO2SXmg1dO\n1RCAqIoUxrjflPnhZZMaV1x2p1TQWsZbZccDTvnQlnWQrsS1JEmiHG7IQtFG\n2HbFr4b8c9E0rhPE6QtS0UzdEP1POYFTJ6ZeU9KEjy6N8ttVAEwNjS+z17oK\n81VRcVFHXWhtDSGkUKbZD4KW8xrmO5w1F3VVZNC6/L+XjO9B1W1S6BSquP5n\nFeiZ\r\n=7HV+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "2882308e660b7ed0657d21025cd0e1e0e97ebd25", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@types/semver": "^7.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "^7.3.4", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-proposal-optional-chaining": "^7.13.8", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.4.0_1614594791987_0.6194113086162671", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "jiti", "version": "1.5.0", "license": "MIT", "_id": "jiti@1.5.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "f7b351c7c321be23a08191c63eab900376688f16", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.5.0.tgz", "fileCount": 15, "integrity": "sha512-7cvYWEJ9E3+664sLtKL1O4AsH90iKvZz/3ku1pcTRbuHaT8QdSNGwuBfSskGksQivVlmCe8LcNV1xms27sy1Gg==", "signatures": [{"sig": "MEQCIGIhGQ0juTA6Ctc6whoZ2dS2BPHPUU4ol1fPzsEzkQtSAiBbS+v91vo7UQiezsQ+0D3ak/QM5rtwbJUjybL3ZOR7Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2633521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+ZWCRA9TVsSAnZWagAAzZkQAI7wzys3vEHgeidZizjU\n2bzE4+M6c3UVJ+M92P9yU9hJcpHJts6qlC8UDJLUvFCjlpwxyMHq2iFcO/Im\nlHrsKFdDW55MEclZOcJmsTjS+6pUFhan+tNDEdlJgJ0iGwpNdancGUHBCU6A\nVJ9uR/1TouJJTW0xPnDbtDnwleWAnBNwEyi19Ly6znp3GSRogX4abYPhsNaM\naiMH9R6KVH3BEjzcSc07Hq7fka7hhjKdxY5PsjRMi004R0Qz4P9vIQMx7N8Y\n3lp/ySa2WLPJH3UaY09VzzCyN8TEglFkY+syIulMgeEO5Wt9waSt1koNJQGt\nebA1TncATvtNFSkxhBoHrz+/KstlyXi8Y9vuUb/1miN//E3oOgt+qcMI4vdx\nKbMHjq7v+UYaVPLwFtruRVTay07HDZQHWeLWgqLXVfWG5KoQfTnjNoH+Mi0e\nSwNykTONye9+qc/s2kXNnCWJe4DwkJTZ8WbWmvEVCfDmLvb/qL2xaVA8u4ID\n21WMRBLGy/2OkFA1WYtGO2xwL89hvgpOyxb+85r2C9/ac0HCQl/p7lk3mwjD\no062pY806IJO6vD5tNmi8A3PbzirL+K0gYu5vLmSav6YTWenMe+JAfn7a/ob\nfiFZfNVsiV8qao3c/HsMfy1AxSzN4anDvILk8J+DHtqa1QHnxMOXHNW/eZYS\nJMST\r\n=/1wL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "a6534a0186fadb9ece73e048eca1468e1a1909aa", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@types/semver": "^7.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "^7.3.4", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-proposal-optional-chaining": "^7.13.8", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.5.0_1614800469857_0.07518458350805157", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "jiti", "version": "1.6.0", "license": "MIT", "_id": "jiti@1.6.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "8888bf15ee18a810163b9ed2d085e5332965070e", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.6.0.tgz", "fileCount": 15, "integrity": "sha512-wZS9llKo0cjuWfUWhk11/y7Ihy1l7q1bTPCmIJo+MqWDy+Wf03rIlE+ZJHQBJcyjuE/ZyQfyPps/GH5EbDmF+A==", "signatures": [{"sig": "MEUCIGMIizl+0ETNbqCO6BB8swpNUj6d9jsGNBE+w0foAVo8AiEA5Ic4E4X9JcMwsSprPM+/slpgFevS+Wtz4twDaG+18tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2636286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP/TpCRA9TVsSAnZWagAAVqsQAIrmnJUoz+39rXxwywfl\nIW/jUYkFissCQE3Bgui8dbF7n/EBGGMyAe0pL68oxSCPvpPezM18Xa12pDez\n1t737euUUOLj19ZmLKrtaqddVSPFvtssqJ4vPYBQr9Gh3+EDqLiImvkIVW2Y\n2JyIkrhOGezgyO09D+RI236f32DfSDT04DVVSKUSZTqvgD7q0ednWWoPlglu\n7Ln/OYqrOn8PXzkXNOaWsS1aEhhmKTRj/H4WDQd1krKqIprNPSyyQVYppbOe\nkESZwps3lxyjdGRzjLJ1XVd/EmCVOJ08IuRP97iCxDeo054x9eoN0BBfEgcq\nEe4H60PWBW6FxuJOcQ9albxR/DrGZF7EYY7Y9z3h43vi39Q53iRbWCh4axO8\nUAisw5P8zBjqoepgY0AMfwLYjvcanctq6MYE59ijgeFAj0RnKP8pU1UWgfRy\nPMXpDu5RgvpU/9vzA0CN7HacbrfdOKJ7rKwWyc1+iTPhXkWvE1TRDGHf0q71\nIzs7PaDIbdtLKxpiYZc+xDJrQ8WAzgSe6IRyF0/cVBvWfgmthi52NFu4SiSx\nUc99lUaAZGpwLKO4ZJb6l1Hs5ruhDPAAkMlIvhDhUkh9sJbBXRBGZJnvvCzs\nB7Nbh0BnGbgvzjwXy26Ohygz7wX3rHMLZfM/Z8XP0UZlwCRk5I5naXsWorD4\nnm+x\r\n=waRe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "d12413a15b69dbf8fdca419fd83bd5d30ffa0718", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.6.0_1614804200737_0.191141412682581", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "jiti", "version": "1.6.1", "license": "MIT", "_id": "jiti@1.6.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9fc0fcfba4690add0cf830c92c0f92503842bc17", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.6.1.tgz", "fileCount": 15, "integrity": "sha512-nVY9O+o6LGLy7Z8n6HQn2T2gveccYBhJF4UbYYJDMPawWByvGKhk7suPJggc/u1JMjti7fh7o92cbUfXfXV0bA==", "signatures": [{"sig": "MEQCIE6BS65/XuQjNyCbsLkQKR3fqb7LI94K1s+GgpCWUAOmAiB08ajDpFaDWHYy9tMDCYvzqivqm6/oDaM/PPq++UaaxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1225883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQiH5CRA9TVsSAnZWagAAGZMP/iDlpRes54kQykpK6D24\nENyCwwfldI5wwJeegMIsM3Vn1hqscm+anvDwqI0FCAYocynjDn5B4/rWvGjx\n3cHUTYX3cLM4+jUeFKWXL/Ed1iOqXmMqktxkoRPBm4Wc11jBzilwYGpEHdjc\nUF2zfpF3BpcPXGpGC4hGo2lQYa+25lwimTzAzSFUQWchDIEU1XHfC0qtQWA5\ne+jVDs5c5SSQYXw8np7gz1ca+EgFi4Kq4XS388toFASqFztptquBieAk9O02\nPanwdpEN5QWU5TExqw1d4/SlbQ4G7tmbzN02meWpMASNu0hb6pChQsUM2BP1\nIzxNAkRUbBJR8hk3CfW6ltgYG9IsAMp9/3snC6+wa+6bDp0QLpiIFG8H4A6P\nkoYG37wx5JE3k5FD7ja5iO6Ps40NxLSmqr7y2XLOY4qTtPgKTiSFI+sp0+7j\njM7t/keV/IsTg2qbjCMO6LRRMU/CzWvE2MpRWZ0imVhLKOCufSLF6sky/lVk\nfHgLNgFswIl6JUv/ACwNsRvH8Ijyt8qoCvWU7TY96z9TETlyUWQI88QoB/yk\n/R4X7Z7WESNVlGkOv5LjhQ/5P9xCnjfIQ8xhboj62d8fY4MNKEy+9+wR9jWb\nB6FgFXb9jzCjRV8Py33Yi8kflKOU6QU/tcMhLFxo3aYrstJ6T+LFN/tomlvD\ngWcU\r\n=3q2E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "fe0d35c813c05c4442e1dbcc8ca5f8bf968b946e", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.6.1_1614946808449_0.40625043614437106", "host": "s3://npm-registry-packages"}}, "1.6.2": {"name": "jiti", "version": "1.6.2", "license": "MIT", "_id": "jiti@1.6.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "a04b768fad5506fe4e17e1397a8f535feba27536", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.6.2.tgz", "fileCount": 15, "integrity": "sha512-7/w7Oi5IC1WAT5Ej7KnooJuezBmhHMtva4VbJ2RP4ZCFCqZTvJawcPypPIvIiIksCkJYPwMxU8bBF/xNPBBR2w==", "signatures": [{"sig": "MEUCIQDi3r6u9EMD/VCaUN31BxDW7wCFw3BMY6sa9kCTpvbEIwIgM0zUlIxoULdP/BkBQIje9Qt78Ab8uv8lP9GAtgerH7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1226682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQlhVCRA9TVsSAnZWagAArIEP/j1X2x/uwYBHlyV5MiQa\nHyX1GSwz/A7Xum4hWafg3lf84E58mhipoWqQ/QHy+avkY2pIs4QV7ddUsF7F\nVM+LR/P7wEaeiguAJh/ZmJy4MssVGYWHCR9Myf5oLccqKQ1MTeHWOoxvqWeG\nBXOv4KexGfgUx+sGs/yv1PYIws4kmISA0t/pwSkxtPYpK3w750YVQXi0Ft/c\n9HOT0hDH2vV2a6UsXFt2Hs5Oml5fh7h+J/MlBKgS481UiZAzwTWPLIX6Vmd/\nrlIGyYIC0D9U1JF+khGvxtS+RnMZ4L7PbaHSqPkQ/w5dVwQvaOPc23gbvMkD\nku6IdQR5C9orUIrirTU3lf+iJZlZsCNWZWPHT7Y+H98C2atZcjacP8i8dXpR\n5l87SN1D8vkZoCOC8iOUl+aqWgLumMI5kVJCwpMq8SDtGGdkZPsT9dFONiLj\nIrbLRDOy8E1k66l8zn3b7JgPSHYbLOyDK5ynECSdYGPA2b9xDSybKuy4TYXe\nYTcUiFIDTC4IzWGf7n3l8PFiTdEbi8dENRQh6jAo6nAuSqFBOJGhH8XhjSdY\nXc+pAdaSo+rOyGzchyQbJMYCjmQNR/ixFHW88X69dSKW+laqcVXdzqxz52Hu\nXoSByZzP7zs6mtIOT38eDSt5QGiYXQ3CQv2V5ODpo3cKbOre2sIkY9IkNu6w\nBOm3\r\n=qogK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "5d5ee2895d6670044bda368449557a6a131f7439", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.6.2_1614960725060_0.9106026414178949", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "jiti", "version": "1.6.3", "license": "MIT", "_id": "jiti@1.6.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "722fb45ee03b01af87314f80aeae07762f9ffb7e", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.6.3.tgz", "fileCount": 15, "integrity": "sha512-4oOM9K5MgVx9kiv8zo2B/8SIpKQ/rkaREskE+H8ymvF08mC8PAKnd0bbZ1FV5DTyoOOto9mapMpvZWA06reWKw==", "signatures": [{"sig": "MEQCIEMgDiEUZMokAvs889IR15C6KwR4KeKfSSfV7g5nA2kQAiB9d8ZfgwtrSMW/eJ/6DzU+2Wfae9dxEwSvnsw3Zv8EQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1227008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQ+hoCRA9TVsSAnZWagAAkjsP/i/X4sGSSPFNHScx9wT1\nIbOEucd72oPU9xQRocuuJOpraAtGxEkLwNt9tnB6cLHdCjdN0gWN6oI64kjk\nYes/QjmnijmyZNP0ZPYX4xFwa+nQJm7fYm8znhyEYRexKhSXu0pQE98mnMQu\nlldGmrogTEO7TuLDzolW+baj9q3Y51S+jhTE71XIf5bp2bx7KsgLrmJp3qpb\nC+fcO5WVN8HGNsLwRPJ67VN+7z6SY0MhMQ4M4SuzdLLnitD2FCXV3gskMnOY\nh3Svpz4XDV3RjjeINaYiHQjKReX+1kPT/3OHFMFCQIKpnApW8+Taeb9cB12K\nwcuWve+t9TTbGYCEviGLfhGEM0TY4gKOjUqZlbCzWKZNa7A/AjnfXXI2lhVn\nqh66MYu48FWn4bUKYq/3zvPZOFoKRILSmsQwzp+rH0CmYr5VdajLEDk+xPz0\ncAUt1o2f929MLE019tKfOKauhSCwnDvI6qgIi+Liba82c9hAtVP+KM0FAhQk\nMevxLic3OvGaZsFZ0HowB30aA7+ca/VhuEbfKb3M0ND4HxjHa97LFQ78gxZw\nfEyUdbRmWSf9eEU+naGMAVHjpmN325g427OWKxV9L3drD3s2PiofVrNUwoqK\n/8wM6b+8G1wpYl67ibPDckz7hYIFf99PBqanSzMy0qLqCOl8at50JDN0IzHK\nsKlI\r\n=e7+W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "d41682b2882b02b2d1356e29f2a2ca4b46cb4f6d", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.6.3_1615063143576_0.21811166680107985", "host": "s3://npm-registry-packages"}}, "1.6.4": {"name": "jiti", "version": "1.6.4", "license": "MIT", "_id": "jiti@1.6.4", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "63453b602d0234f8bd7ce638f03f0e74ef99be12", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.6.4.tgz", "fileCount": 15, "integrity": "sha512-ICUtP0/rAyT/GaaDG0vj6fmWzx5yjFc7v+L1MAEARGl1+lrdJ8wtJNChr+ZGEdPoOhFwdhtcDO5VM2TNNgPpjQ==", "signatures": [{"sig": "MEYCIQCZCKc0wnsq090OyA/Yx0OxjyyxiGnoYhQuXyNb//o10wIhAMAC/VfCP8XKz04KG2R/nI2zRRjg8E1lorROoI4o1cm0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1227763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSnsjCRA9TVsSAnZWagAAhgIP/imhvNsxBKyoq8YJB4pL\nsYCVR4Why791Z00mF7sqHhbGDu9COIkuTFS3d3jgjabTNN0beg8LkTG80WYV\nMwk00LQ08rn200YOnrxqumMJjB7gxY4poB/27AauC79M2EClSHtVveiWNkoe\nrKgFEBj/FjJ3T6YKqGoq9vRGBSnsmvq958jzJDqTcgxWh/2kS3Wosolbg9So\n+GuJ4qPPX7xTjD3xmWfDG3pG+jb1pUFPCLpkznJg8c+HSwKI9GVZ2W9xrKw2\nmkCPmpxDnw2hBeMZcZIsQJBXYte96G0UDhWNjEiE3utE3jwy95UpvgBgkMdF\n6vOCTswJ3xwesrWVpq3Oh2QLndoxRjperugmejbiyuISMW741nWAodtmqEdp\nLQz5rnzrXXAKY5qLFWHOQst1HFqOGkJQ9K9OF2X6qUu1+r8VlBtyoCXaI0z8\n/lH9hQm04kzGtyvwL3jrQDxBBxbjjrhEPppyeDFf6G9WYJwBBqXIE/wlTrH5\n5WUp43RqMYY3NCLuXr4zoe9HlGTfFTEKAL0Cfue9j9VKhaeQvhfzzt+gfvSz\nEIta3WZdU0Xq1xAKCwVG/KlkJTr4atpmxdYe9VYIskHnDUxkmDQaxGzqqnC4\nUdxfCOmxg48a0nQJcVEJwahHC83YF8jkqo8L5rrvM6XmomhTOHij4WOwYhgE\nNz/Q\r\n=tiwK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "6ed82103cfa7ab884f6e47f511eb36ab47a1a128", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.6.4_1615493922782_0.7717516782394624", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "jiti", "version": "1.7.0", "license": "MIT", "_id": "jiti@1.7.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "e16426366376db9143e573b717572d6f2803b59e", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.7.0.tgz", "fileCount": 15, "integrity": "sha512-lntRupmBO+y6sHmlfQwiZDPPEPyG48Ky6VqtS2D80ToSgerlw1/j9+/bQNdmyZd5LO5XLi/G+1vpWju69GWQcw==", "signatures": [{"sig": "MEUCICPeoguhYJQWV66Ce35i6Ij+XTdJBJUCX2UV+UaV1ahVAiEA7CqKrYM+q3mxtyy2Rr48teaBwY9ueze5T+U88ZsSpfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1198315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcA7rCRA9TVsSAnZWagAAwP8P/ip1rG/bqFQWxO+YeCG/\nluGG5972uy+KaKLvacFWTpnOZIi+h/uBkrllVy67hH0SQqPLd2mBj22Z8xrH\nPvfcmXia1UMdSGOYftrXylvAIBeuR27nIwITYRHn7nEILSeTl9AOkJBfjDKx\n6REu5sGzgY4pp3PS3ARazYZGMJ+x1pgpL7tUafacmdvD1LVm70x9Zbhg4U3w\na/6g/LETxH1sqGioWFKz3QzEFdJFHdn4uP9WHSYtbm0dVh0rZE31X3BGD13G\n86umaW5C2ou3plbEgsF1lWexdI0gAF7b4aZSw9g4yTmwhh7MEpmZTSSqJ0Mc\n8+p5k3rYzcY6nbsZMG637Wo7Iv/FHiyc/oyPIaaWDBmb+EmVl5KP+Pwu2hyo\nEM/oj0kRNbXY8Ro85tcu+gFeV9xfq2K8ZyTHlC3g0xz713TGfrPGqYfktofz\nzeyXThWSQ0BkXsvpRxMT3VxsscNOuSHYQQXU4GSwxkyw6nDAPk5snSSKkn3E\nCjkTBy0qvEmUYCsqYQrPFkP2YtHdzKQTeyFNyQJ9fbr5R5eOZKfReYh8g+kd\nu3fUT/dt/qSFTfaWDCgcr3XL7a50gYXmX6WZf+GYoelQqoBou3DppHo/3HuF\n0bYv+SrxaOG2YrviahmLPVDRtdM+AZXs6FSU8D7lxiN/KUN0Z1pPIRVha0At\nocdC\r\n=ZI3K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "1e0d2cf8e94a75699139850297622f1650a3b3d0", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.7.0_1617956587439_0.2317740398697914", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "jiti", "version": "1.8.0", "license": "MIT", "_id": "jiti@1.8.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "8ddec5722b5785eb98cbe4298c0d8080ff5814d1", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.8.0.tgz", "fileCount": 15, "integrity": "sha512-qtwVKO+1YbKIssnbnGN/V6m9g0XvNTCKwk3mJWJD+kEgkxHTozhSZiV7Vb8nkjzKU7asL7m1xOowXBJvjwlZ+g==", "signatures": [{"sig": "MEUCIH6hbV95IPy0ePPiEl7HV9Hqf3iRb+oUvTmyhWDkM77oAiEA2KWERdrntJv92gxnLKPpQR5wySxhVUaxQ5QGw7MQUgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1205964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcB6+CRA9TVsSAnZWagAAv4wQAJ/Zs+vZxwDu8A5Pv03f\nJ7JWWmaXaKc6ZIjCZZC2+zeEuWli2zLp0eYUOG2i7iaTrb6x4ZCbrEA0jH/G\ncKa1rlndO22CH05mfSHsYMkbRewrvqVpj44EfWavAn83Ngm+ALNbgCy7hoha\n9oKrEUbPK/TdlvEOO4WjmYn51/9yM8n6Kysrby+Ce969YyWSrNB8GMqiOQAl\n8NHJkhsU71AaZJfPWEuWSqXzyYdNoSbETxcb+hddOdsdnR3Zg5WFvWblfnjx\n2JYgO7xLbnqNmeIsStM8hjYPzfoqNoPtW/5S/Kxc09GjNXbN6ugWtTQV8xL1\n3gQSNKchFVmAWeO183HOPCuJYan6TNhzULdKJg8XzUvdMjy6eAEjIiStSuq0\n2laK9qeyhBPOvMOFqq0h6T1OOecN8S/PAvMrD2wgBFbmt/7OHqty9SWO7Fxo\nDrO04nEV2og0ZGr4JI//xqySCpXbCMPmx+nQWXIgTYP8zo/UJ5fAaohFfStY\nqXMdwOFIwhTaua6gqaoNcx6lW1sQ/OF4Cz01nZ4Y8WQD/3JFaaoSB+qKb4eO\nNCbqRMv6FNNCiXER/EQpYpmx71EUOCaoC2ayAfKquErH7c9Rsq5lRuos8Pqc\nUL0MQdDig59tyhI8ErVeTetH4XkFTexqelCNEjnQ0kUA7TWmtdEq/e/UmK0A\njc2X\r\n=4rIY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "0828f673e7210ecda48b1a3c4f2910a2ee0ac15e", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.8.0_1617960638240_0.5215727303589703", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "jiti", "version": "1.9.0", "license": "MIT", "_id": "jiti@1.9.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "7b39fd870929171af1267ec4c0ac57787060a581", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.9.0.tgz", "fileCount": 15, "integrity": "sha512-89yTCLjy7zRF+oLIKM1bF/vX1kxl7OvdXCq55u3Bl5h+wjFG3jzb9tBY7uG9CI8jjx5MbpRNSGmclHDMfFSskw==", "signatures": [{"sig": "MEQCIGTcRHWVgVxm6UQaEUiIiSHR6vv0VGfqxby9YgMkTOFYAiAIqsIFdGBhKFQshnTFum5Y5zmyR2EBkZydd1Lv3BKKgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1206335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcC00CRA9TVsSAnZWagAAe1EP/ihrwSTlMMX6Uty48TVV\no3exrMZFo0zqcS8UbhBJiCX3upmeubzh53XD8vu3avDwqulkFTuEF4X/LO3H\nqvrZZr25GZn18sidziGd5itJLSujnB8wZX/Cd0XUROpZI+LzJs9aTvZB/Bgs\nzNC1wWoLowkPv3sM+WBNw4pXTibOP7DZfxfM0jAhKxR9PksPuT9V1wRLTeAv\nU/38ydHiCkL6McseRPYunIr83HPNX4rZwb3DrFhWeTEMNOWotK1EsnUxkjDK\nTdYoNp0JF1OSIRo4T5glXz+SWwep7fKKhR7fIYX1P22MAIGBPFGa3HLpwGv3\neQKBKcxJgP73rSGXy9xdfQc+AZ9yq5BeOHwxZTmLCGDZdTrhLmjV5eAjIC4z\nDzKIeD9w2zsGJRXi/7LuvM96jhqw0ZNa0QPY0ljQkrrN8RloxAUjv21S20kF\njGJqrMwfmta8hK02q+9FHZ6HMUAKa7DuuZeBpuBiCaygOPMKvK56zdXUEjpR\nYNlebEfYZlkitMJm4YhhoR0+am/vLnZZjP0HaeGoZG6BqPdXHlmh50jMI6IZ\nCPZrU6uWffCKz0QsO0pdLGklpALWJMB52ysOfNXv3Bs1RvSsxP1Jf37u2nRg\nlPRTTKGFJm804g+5GruM0L3OmMEcxTTwPss+wtUE6xYm9kTsd7veznGzRupP\nUXwr\r\n=bJpW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "01169c38f0b1a6441e049c6217c0f916e2a84628", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.9.0_1617964339753_0.6468238950974103", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "jiti", "version": "1.9.1", "license": "MIT", "_id": "jiti@1.9.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/nuxt-contrib/jiti#readme", "bugs": {"url": "https://github.com/nuxt-contrib/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "d9e267fa050ddc52191f17d8af815d49a38ebafd", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.9.1.tgz", "fileCount": 15, "integrity": "sha512-AhYrAxJ/IW2257nHkJasUjtxHhmYIUEHEjsofJtGYsPWk8pTjqjbPFlJfOwfY+WX8YBiKHM1l0ViDC/mye2SWg==", "signatures": [{"sig": "MEYCIQC3SWi7cgpgRdLaOzyCZd9eTuXg+AzgfIkoOmvV0pUJ8wIhAOGFx4+MnTbXDZMdqYvHjtNYxexL1XVSeoNDJjGbxPmF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1206420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcC75CRA9TVsSAnZWagAAp2oP/RDXF78Plr+mJ59TUYzD\n4k4XMD4E8loHPeL3ywvIXIa1QAb2C0X25GLyx6ZxyFnC9FvyqidNBSjeIGR1\nAWYsNdoVAWD6swhQ0kX0FRCxkW5stsKgkUuYfuAZjh/lUaGnHAhhrX6rxVt6\nqt2ASnlM1/9CQ4p2ZNYhqhMiXkCMKEiHUC7Kt0WGlIfO5vSbYyejJuOYkM5I\n1AWMFiQGZ1P8ZYmPM0g8ZrbI9icx6z2dw5pOEuEZIu4Fs6VxjHD2rpsXfKE/\n1/foz1TJ8Qyc9k/8+L3fdTtPICnVpR2paHB09LZ2bpwBTMwjHXsWwCpMG1eg\n8sf2Zh+MPqlCdVymitMn4NEFmA2YmL3BQ7mXdo/Xqec1R0jp+Ceu+/rr+zX6\n2bhsbnn0vid5y1ktY2NM0YuRhTNs2jbAb/55AL2EQdfjgOzXGeft4W4qU/B6\n4mTA1La4IzDRIyY7Nk9VsUHA4/YGyARkoEfeDEuO0A3RV3NaciIiizLYxpDN\no2xLxSWJ+PUy/3KdmwiSZuiBQqj3JrPESwz79M3c96n+J8fcaYtUfCU10KTl\nTAC6nKOO7wAakR7lKSXWvI13+WiohAIvb4kTjYJjmPtoNl8sqmwPM+iY2hNE\nc131psKiKz5Cov+EUNmaN6hFK7OoJpTj0fooDRjalVSILvK29jN7SqFaOQnW\nmN6s\r\n=Fk3O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "9b76886b6ebc643cbdedc1618ac1ef0ae79e266a", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nuxt-contrib/jiti.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.9.1_1617964793182_0.66393357873833", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "jiti", "version": "1.9.2", "license": "MIT", "_id": "jiti@1.9.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "2ee44830883dbb1b2e222adc053c3052d0bf3b61", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.9.2.tgz", "fileCount": 15, "integrity": "sha512-wymUBR/YGGVNVRAxX52yvFoZdUAYKEGjk0sYrz6gXLCvMblnRvJAmDUnMvQiH4tUHDBtbKHnZ4GT3R+m3Hc39A==", "signatures": [{"sig": "MEYCIQDmith0qvc/ipbKi/2BaC7q8R8S+6dArD8boMgQ9tqWfQIhAMBMDm5fAfKDsGFPzagHy35vDtJZsHgaqpo8WdPMIFNQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1196451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlGPCRA9TVsSAnZWagAAoyIP/2LhHbiMLkV5DT7x4oXQ\ndBuEQw11NpYb8+L31eU7SzJzqXt7ndu/8MS8hVwUs6xxw178zgGlD5AGUiVh\nEEKkEHtyB6+8GnbiQGttmVqvgZzuQM09XyHVsJmst7BCSRnJ/BaNac9rFPrK\ni1X3ipCjmJK+c0Kv979osZ3d/sqcjAJ7C5EZPA3GKw4K3A244NgvUnPF5oGp\nys+8ZHt7S+BMCBDtkFhu7xl97F6Cc/ftheOPXWlDTapVlOucmUTdfJDo5j8g\nEWhUPJTFbpydz0yuPg62Jgxrl1CXMpcvqT9VmdgILWlcnHlQECOOvIdRZLRj\n8IZ0rOyWytRg29f65TqSgHNrzAk0Cek9YURCSl3OHNSEehGnAUYCy6TKvdZT\nb65UqvDi5WT6AuxknsIG3m/DNAesG0egTw1Nb8hXCQyLkJngjZkQfY9YzS9V\nM8I5sxPLPQLrmGG4p1s5M8bqxtPdurYlKWyoDeuOTjhogQzroOG8nzs+p5rM\nuyuZtlJo7ZZRjqrkokYr1Rn8j6pBaIlWzEs8fy1uMMk90vcccEiJfzRX2yJh\nuCptfuX5nv2YENkhAE5VXERYUnJQRwuUMYlYmmhjeqx4JgoNwPJ9s9yfl/m0\noUi6OKWqyTfVLciXhN6wyp57E5Rk0Hx0UMTtapSuiXVJKw9axkhqv7UwQcN4\nLPgy\r\n=Tw8I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "2062b36292908f7b4324be3bfc8d02f4f96dd83d", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.9.2_1620726158747_0.7424131898625117", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "jiti", "version": "1.10.0", "license": "MIT", "_id": "jiti@1.10.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "00ecf6582471bdb654d0b59b7e0ddf9d70ad6ba3", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.10.0.tgz", "fileCount": 14, "integrity": "sha512-+fyyZJyRnZ4ek5HdWfFGVdXmdnLuyEL+i7vVFvB+KDlU1jfubpFMebJgK/Swgo+xCNev2zHCo9ZR4g2pciiL2g==", "signatures": [{"sig": "MEUCIHU+mRnkSs094gERIpO6+FJXEus7qmhyvmxlJ/qd/NVxAiEA6tQ/NZmBV6GoDqi4x008vkVvJB4IzpI8/DpvE8Z0sWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1169823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsMB6CRA9TVsSAnZWagAAnXUQAJsPTc/XpA4PKlBLSZbI\nUwL44/WVzGK6zYDNo0jEI3DxDgqsPG2c6VN85w60As4viIbhG840FcxSIWU3\nd3XWB1pxcH8IEFGfHYQjAtnpugOfekhBGuYCyqV9RHqjeUfC9feUGNu37lr9\ndaoOv5Re+BS60uaHF9OYGZZ7yfe7fekDvv+slOqyabqfOwXZaU85KXBnKK5s\nwV0Su6GZgdx7mydqstx2Pcfk3/01W5zfaushchLf86gpMvH+Eg4GKynKsx2d\nmUzN3P3yziAcQh2YOTTXvuQt5ix3DJiiymct8CSV3iJVCc6p224ISUGXutB8\nKxiNSwf0IK9hEy4cRt0QFQ57O1/4S1bw4907S3K0JHelu84Svh8Itzm5gpKK\nJXfbnhpVBK+lxn38ptgwMNvd/JWjUlWx9iUYm5jvpeis3+AognfdEEfa+RH0\n1F5TQPEXy961hqxvvbmyzbhGJOUxm0KMaxC6xkPIAIfn5dJD0QaO4mfzIBro\ndCpq1X5iubyINpcuURFjjeyZ8T5g3ClvETcBDV6J79cyVjSw5WKh/pbuxZkc\nZi9zbNVYQ3XjkOEcNYLmn2+7x91M9Yg4+Wj0EzRAk064UHr2pYwRnOSFZhD2\n5po33Q1PxPu8cPMUaQsYoERU7dQiTWDAbZtjQKOqe/L/2dfvXb2lYki4Ah6V\nlxpb\r\n=1ePd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "ceb9bee90a7d1a62d2daf761366726681a88480b", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "7.12.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.10.0_1622196345866_0.2992138994233642", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "jiti", "version": "1.10.1", "license": "MIT", "_id": "jiti@1.10.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "bc2a175b9435274dc8659d3d9a121a91c6b3a1af", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.10.1.tgz", "fileCount": 14, "integrity": "sha512-qux9juDtAC8HlZxAk/fku73ak4TWNLigRFTNzFShE/kw4bXVFsVu538vLXAxvNyPszXgpX4YxkXfwTYEi+zf5A==", "signatures": [{"sig": "MEUCIHsV0Zx5zKhHjw1UTuy2ZmuqmqLTCh9mzmAhUsqcd29/AiEAzsSPBF+cML2ur7K0fy4hV1wUvAlci6Q24SBVSz6jgzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1169825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsM8QCRA9TVsSAnZWagAAkO0P/jJesKhvdxanFdsYhRZK\n5ebjAFNxjFdlj7gpzdKfBsLQhre4VTz3b6Z1uHnn/obQqHBkzwmAqmJ6DJ+Y\nDB0q1kZAMvGfp63nPXqTLF78KwCO+B42DZS0NaMoTgXBM6l9gW2t2kp5BWgh\na/2ClgPn7gCbMFWGSupRF3jHwPdXoigaSn8lx6i5UmmTnr7Pb9IZWFajYTQl\n/2BOxBB/FyMVA/FkIfr1Q0xJKhzOaYJklNBrbu1DdvsksxGny84uwWu5AfBC\nW8CuFUuvCC7aHrStdfh94L2KKMc0hW9qmeygkfHTMpMWhowvmZRfjU0dyOig\n2iqMx+yMnSfNFhrxIx7PI2y5BfjaZBZXhO3xVcaG33HiPP82oQRG2v16H7oh\nER4nzilFMjEiQ15uqPjyOOvur06WaUzVPfJcEBcdJJZ5welamlOP55J0AgCA\nwOho3yD1n8zkd2gSK3gNXEJO1OyUW4UHvtd6pCC/YExtNQ170OLCgQjrYT1D\nzRztprWbFQoBPzP6BsO7tBhq49xhMJ/nrci+l+obcuFFyx/g6TZ2uEyg2HQE\n+prLt5WG71oCvrGFVOgb+ESSiOKo4anGEM5Q4cazvFx5SeL5vQcVMbVbc9Hl\nRp5+CAZ9v8rN4iTl2ttCmBCUFs7pBPFX/up/q+Pwdnq94fR+tj1kPNaWJmLC\nfhC8\r\n=933k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "57f0fbf05d27f98d6c369354b3f47d29f5732782", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "7.12.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.10.1_1622200079525_0.6454317524615913", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "jiti", "version": "1.11.0", "license": "MIT", "_id": "jiti@1.11.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "64120a30d97b9bf37b8b032cf4564dfadc28984c", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.11.0.tgz", "fileCount": 15, "integrity": "sha512-/2c7e61hxxTIN34UeHBB0LCJ5Tq64kgJDV7GR+++e8XRxCKRIKmB8tH6ww1W+Z6Kgd6By+C3RSCu1lXjbPT68A==", "signatures": [{"sig": "MEYCIQCt47raWmmdFG7q4uWzQlhax3asDDJUnS6Mx53v6dpKvQIhAKIfhYi1Xu6qzi0Uv7jAMFRScWvenQMNWx8bwofUK1os", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1174610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/psqCRA9TVsSAnZWagAACQkP/jeloKiZwAZBxPw7CG5U\nGxsCDC0lazsGeAgajK5WaRmxH99FrRi6CFtoVjI8EzXWsKgpSZldJu2x8LKB\nF9wxvqAXIEPheItZ1QuTco1hxjeC2rLPL/nf6EXUZTGX2OTDFiHd3PoHSYPB\nTon/v8n7+rxuVpJ35WXecLqboI9XLD56vzgDyMDRYvDkeNatZEz+firs8t6y\nHTG5jzPYbzqo+Sl9aiQV57d/ZW+6qSQ8n1h9Okcjqflior6fK0UfO2MGH5td\nn9IXS99qwwue3+abzcDvGT8fWsawn8Ix+5GlL7QoGnlrogmNY5nqLIWeeQ4V\nvABzbZ4lHs9vy+jQxLAOJFO+hZ6h10c7mJIvQcarpOipYeGs/3S/0sgxM7cu\nHNm/96XlHX1hyVjQgyy2091lpgqAl3mARjou74KQvprVLol5sixcZVZ7bZJH\nrM9gyeYKDNafZ0b3EVUBziqA8gdnD/o0QEPGgwr/hgJFuLjfnDBEo8no1kIa\nPRuktdxmG8gzSBJJAdeegx25Ds6AGYzWOdl0qZci2vUvWbAVQ5A8Wu7McNy+\n3DX9HFda0EHvKCiX3MTfrhRRa9Xc2EEHZkcNxjOy/XFuvW9cwfmqlZCkxZhu\nLrdJ33vfl1fAK9tLv6w/Wwm5yvlFJ8+FkElZZxYf5lJ9Cit5FXiJ8lItwZ34\nBNey\r\n=9dmd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "67adddfbba6f9434e694c963ebf424e61fdcf943", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.3", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.11.0_1627298602415_0.06427342435275851", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "jiti", "version": "1.12.0", "license": "MIT", "_id": "jiti@1.12.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "6f6e1908f9425075626400692f0b6d902db262e4", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.0.tgz", "fileCount": 16, "integrity": "sha512-0yGfdPjYZ+RkYR9HRo9cbeS7UiOleg+1Wg0QNk0vOjeSaXNw0dKp7fz+JeqEpHjmFuTN48eh7bY0FsizSYOLDQ==", "signatures": [{"sig": "MEYCIQDbO6lb3d18TXQlSLzOjFNPnmjeVhgE3qOEXUoMljvg9wIhAIMeVw2CUfHKH/vlL6qXj5/ZHZwzpIh5PjeYL7T5wGQD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1190478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPys2CRA9TVsSAnZWagAAYygP/RYjzvciRStVNfVm4xJU\nKwtU6TD+CqtVPj6NM2839QTUkAtCkKPaLHVgytBdCbf2M1gDW2NAyNfCic5S\nzst4ewB3cSh9uPehLG2QlfhR7ByF+6WVM5jCPdwnndLb6PtcHiB8rNALDwIx\n5EEUZ3Kt1d+J7tjwhv4wTyPjuw6Hto/CYPG9gIOu4wTf95FreOzmHSDBuJOF\nqJVBPqDjwPnDTVNClWDnKtKkq9/aFDxLnGha167zu+S/QMrXUhB6/7CF4MNQ\neWETAOxmNpVvwDU/Suff3CwML5aW6o/t7h2bTIyYsEoJW9p7IHXJlSxjSDwK\nX/mID6fYJsgUrnjfSzvV8qgOq/ZwYdAjcQG9nx+PkcPc/q9+vLn5LWRmgou1\nT9b7ergeAFVlD0GXMp6HJmVn+sd60BMujz/ugRpiHPLMIaRBnXAbIoBZdE4a\noN38Hq4fV4G9Q6/96L1EuznhfPLJvd/1b+LWXc6oE2sPFQER5ayc80kye06O\n0GSCBslEvreoMDWSDy7to2uv5rjdrAO7yaRcbeAitBI5/R8otuNqp9Vz07jG\nLT1np8Ebbg9r52UH5Xd+hCY8DtokxQyKHstAQzA+E2b+U8aXh2s8s0C7NBxF\nZa6o6A3bi8evcWA3AfmrjSPPBFW4riPMIAmoZZA66bS8SZjOsoqBzSXr5Lc/\nkxUz\r\n=vZUQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "1fac75386f82f0e0b49484b320d6e70fed07544f", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.0_1631529782799_0.47864960396812406", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "jiti", "version": "1.12.1", "license": "MIT", "_id": "jiti@1.12.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "ef5cee2a3086fb6044ae782ee73032a2017e4ac9", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.1.tgz", "fileCount": 16, "integrity": "sha512-fev4NxeNRaIlvAuGbb9GHsg86KdDKCHCvtMVnuO6sWB4g9PUIYECfaH6E9SWLl4e8y2RWGPg0AWDsngE3EpRfA==", "signatures": [{"sig": "MEUCIBPOWlQ21nYJKES7RPJdVWFWyX1/MVtv5TG/eyo0126yAiEA/QuzwgAOkzcrRDwe+67+RboMMPIavR5ap2DnPfCD8sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1190865}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "a59b5f17cd2b11a88bcb88741ce14b144b7c941f", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.1_1632242261728_0.8429439726073484", "host": "s3://npm-registry-packages"}}, "1.12.2": {"name": "jiti", "version": "1.12.2", "license": "MIT", "_id": "jiti@1.12.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "2a5e874f8a63c758ab2a598762f9a972e1de42f3", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.2.tgz", "fileCount": 16, "integrity": "sha512-1P4ujZHhzdGMRwVJt0p8+caSbGtsMgIncHFq93CVieQzJ8MgRiNKA8IhqN3PNbRjXmBL9w0VvtDYOMB0/d/wZQ==", "signatures": [{"sig": "MEYCIQCgdt/+/MM/Mr0yIsthB53Eye94ktSU43UJ6ZIErqKNqAIhAJo2F0DVG5tNSgpHetvOMRtLP1fgyIE2HZ2lg1UV3MrA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1191113}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "710a1940849c06df48f4536e6264183b102d3f76", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.2_1632247253918_0.6386417184901365", "host": "s3://npm-registry-packages"}}, "1.12.3": {"name": "jiti", "version": "1.12.3", "license": "MIT", "_id": "jiti@1.12.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "fe6f9cb066aa2c37981231dffb1d3f04ab4ebdb2", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.3.tgz", "fileCount": 16, "integrity": "sha512-p88jl9WzvZYekMS5ZOB61bJ1SPV69o7nEpAU+mFpGzXErqLEg3WvNz3jeXylAiSfLZzvqZssrAu08N3AuvaqwQ==", "signatures": [{"sig": "MEUCIQCqk1OessDP9beu+PjqfWmHKsapg1GBIAG6zT/88QwSRAIgNpjwLY/oKB+zyZ2LrnyBVWoAXQJnV/gmOpBipTjGz1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1191437}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "168284b2cdb2d516940f5485b8d9e643cc81677e", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "v8-compile-cache": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.3_1632248423838_0.01230269020872754", "host": "s3://npm-registry-packages"}}, "1.12.4": {"name": "jiti", "version": "1.12.4", "license": "MIT", "_id": "jiti@1.12.4", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "b91782de8e9441fc2b96f6477850bc298287c917", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.4.tgz", "fileCount": 14, "integrity": "sha512-i6V6PRT0Rc7QcWYgjz1Lpvn3IYW5kc4j/u869G9sQBBD4NqfMLzMDK+UA5ftC940umFS6c0ITwYVvVoHVGGpjg==", "signatures": [{"sig": "MEYCIQD1g6H+cdwePAvb2SQOX/dc/V5ZO3grnSZBURyqRKUEawIhAOjVVWQ0aXn0hDc9req5hSN1MCstCv4Ov2RRx3HjtyX5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1187117}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "0f121e9bea3eef58c42f4674d8fb0897a973b122", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "deprecated": "please upgrade to 1.12.5+", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.4_1632924325025_0.11211254420750372", "host": "s3://npm-registry-packages"}}, "1.12.5": {"name": "jiti", "version": "1.12.5", "license": "MIT", "_id": "jiti@1.12.5", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "67ee49e95299ed6c18ffdcc2cfe0d2f65432d22b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.5.tgz", "fileCount": 14, "integrity": "sha512-+K46njcZW6E/OPWWHImm3G1u+fYVcKU7vqc8b36oVtfi9l3moaSGtsPydaRf2gPuVxh13G1KSCVlW0DfyYUjPQ==", "signatures": [{"sig": "MEUCIQCc1KhAj7KSlM5FqWi7XeBZ1kXhrvbISJwSvKzvng0CNQIgB4EyZAs4vCThjKL9nQHa4fyZs2j6e1mXuPWNGF/sl/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1187328}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "5d953595b665bf1ebeacc1a786406eb48b8b7368", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "^2.1.1", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "^2.1.0", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.5_1632927010613_0.8652986600719177", "host": "s3://npm-registry-packages"}}, "1.12.6": {"name": "jiti", "version": "1.12.6", "license": "MIT", "_id": "jiti@1.12.6", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "8884d53a10bd571e0e85787994d97cfcc48ac6f9", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.6.tgz", "fileCount": 14, "integrity": "sha512-drQ/qnYriF9KiU47sRF0rTvfQmJo4JEmFMhCk2SJIsUj+hGnQaxkwaKfyvK9KenX20JNTQmVfJOz7VWe0cSntw==", "signatures": [{"sig": "MEYCIQDX20R7n0WIJ8XnzxeqnnmSBJ59yFjExLMewwz+3pGG4wIhAIaYyLN4Jl9bdSe4TFSg8GwWcsnhBhPBoj/vvbwpZPj9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1182417}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "997a02d75b50b781881b01c5c691291af23b8415", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.6_1633180854886_0.2333978080822603", "host": "s3://npm-registry-packages"}}, "1.12.7": {"name": "jiti", "version": "1.12.7", "license": "MIT", "_id": "jiti@1.12.7", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "745074d5a6f88c29152b32a99f4a2de23bd22a3f", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.7.tgz", "fileCount": 13, "integrity": "sha512-2v5iYsJp5l7iX6ettW/hD7A9qZtsib3gMBfxbQxASszzOpZ0dFZBZAUQGKKIQ780XGR3sGEp1L/8t1JyyPq5Fg==", "signatures": [{"sig": "MEQCID4OAqHFrQPIGH0ocK/XwEUUkBMhrZZ2HvbqjIVOgb/FAiAcsC1OJ7kaxkYKr4WDyTF4G6qSTXddq1ynTs0vnn44pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1168909}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "0a654723fb46121fb9d9c4860bf407bfb791738f", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.18.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "^0.2.6", "destr": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.7_1634043258943_0.6257258450938465", "host": "s3://npm-registry-packages"}}, "1.12.8": {"name": "jiti", "version": "1.12.8", "license": "MIT", "_id": "jiti@1.12.8", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "f9310bb49e290c29e1492e8002a3945c29e1c6c4", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.8.tgz", "fileCount": 13, "integrity": "sha512-sq9fK0JmDlcwfLkKv+lvqjOetzV4TzwmD2El89UyqjEvVFr3F6I3+2sUqYowRouZ6P7/Oybd8gPXUX1wMA+hxw==", "signatures": [{"sig": "MEUCIQCNPYTyJhZYXztUT4rSfsyraFI+0SwXBQB9AUpJK5GQfgIgO/j2RbTRiKktlenNY7zbp1XLpW4GvTTgTAB2pCf17SI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1170124}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "ccc149357c16342b0a146b0e8f4fac398c1818d0", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "deprecated": "please use 1.12.9+", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.18.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "destr": "latest", "tslib": "latest", "eslint": "^7", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.8_1634561509830_0.31349544648051153", "host": "s3://npm-registry-packages"}}, "1.12.9": {"name": "jiti", "version": "1.12.9", "license": "MIT", "_id": "jiti@1.12.9", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "2ce45b265cfc8dc91ebd70a5204807cf915291bc", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.9.tgz", "fileCount": 13, "integrity": "sha512-TdcJywkQtcwLxogc4rSMAi479G2eDPzfW0fLySks7TPhgZZ4s/tM6stnzayIh3gS/db3zExWJyUx4cNWrwAmoQ==", "signatures": [{"sig": "MEYCIQC0Vy1HSqfxIrY07QcbjWxUp9eRiCprOIWtjlnrAs8B5gIhAN4BSyDaXElrSrC1GJlmLBAtJScCFyiJ/OybwUXcGG6W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1170159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2wenCRA9TVsSAnZWagAAa3YQAIeW9wX0+z+tbHbP0VbU\nIZaPXGIbZoqihWzJTh7SuqCuVpLAO8z2hSnUafIS9aUww0qrO9bLL2r2Y5vW\n18HIeCL71tWVeuFnaIyQF/LleIPoZIi2XIfz6R7kKLXULUTOJXqtAh+fQcPr\nd7c4kTcrCTyHrtTvX/yxKB3ymHxzKOfvlBIc7xs4FWV8TkZfMefZfudzBzJl\ntrGyP1wKVayqj5yF5rifgZRZ0Zq2q5B7WcjQRpWs53fwaGMUKAScAlUvUUCX\ny+uKm7vXtP0h77hcBg5Hxy3zX3/9BrJKO/kRHt9BrEhEBFplYSw/3lrqCqYV\ndOIWJyjWCsIB6tavH2YJhog7KyIpRUethCdzxltecMQG20D1KOtMLV693o+k\njozEVBr5QvnhlT/ovuiP5NZkjXY+jYNVVpmu3Leg+kO+hX+C16D/Zw01ll61\nToLbDBCzWgnu8p87VGq3TGyYcgFkNAjGyfmFc9eR1M9inJ6Dzb5zUCkxYGox\n2Nf6K6oVJHuBTlguFuPIM3yLANBUzuf4XMvFqqP93qcixWEWxJO9Yg5SeC9k\nsba5ZFUEG41O9wIXjeDeqakHWMdljfuOaSa7AtoVMHZdi8PppNHSTFG1Dald\nIKSBmaiusXx4LUZ0cgVCT4rAd+l0Pu8yCXKC4QZ0PBlB+xOc8a40BUx78edd\nzE3O\r\n=ozKe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "d52165239c8d2b5809e6b9dac386d4a3de38194f", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "JITI_DEBUG=1 ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "yarn lint", "build": "yarn clean && NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "build:babel": "ncc build src/babel.ts -t -m -o dist/babel", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "14.18.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "destr": "latest", "tslib": "latest", "eslint": "^7", "mkdirp": "latest", "semver": "latest", "pirates": "latest", "webpack": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.9_1634564187655_0.29534641160416397", "host": "s3://npm-registry-packages"}}, "1.12.10": {"name": "jiti", "version": "1.12.10", "license": "MIT", "_id": "jiti@1.12.10", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "61bb48c002077f7146e3b77d0ef767e4ff597561", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.10.tgz", "fileCount": 13, "integrity": "sha512-sbaR90YFFOebPWUyqMjh2l/KlceE0UAFOqpoQzfeweXhm49Zm15Ug097JOnKgTSC4v5CF7kToFgyUPssRzpslQ==", "signatures": [{"sig": "MEUCIQDvR1VQf6yvnksNnsIDDYNUS+y+cCs3fUnb8LifPLJiMAIgeVy43O584yHO82ZCgO1+M07QivmlJQ/qFoBVVVlzDso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1301767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8A45CRA9TVsSAnZWagAAORwP+gJ7cKt23aQZq8YU3Q5n\nsvJuEII1iGISb7Ah6C4eOQYfox4iCuLIORoaBCLBOlET7rAUudncc3bTLXoJ\nycnchoDEOZvJJJq2hnx5Mbk8DeB5jF2nj+3nUjpHcqnZBFcnr+HKDIlLZZDA\nXJujb1oQE+URUoSH+HgUO9ZkjLLAMXg6VxAvs4iY3WxrJqV9uRWR5ZxuXhEp\n/vAJ1NT6dx3IpBHvdxtn/xLrR4YmLrHj5WWxcn4Zf/XHQ29+n1VCiuIETecb\nst8s6Jpm7YkCBzOLxv4EWT+q94NSKm3d5RZZwmeMwaZCWsMoClkF6yun+pBz\nB8NoleLpEwTzKhiQUldyFJnwnQ4majexz/W2wXPEUIdgKst3N+LfuCgK7d/Q\nBzWH/n6X8Zxz/ZOnKu0/37Qs8+k6nCO8fvhqyaWfvKgt9yDrsq3lcUdlfttZ\nh/VyWtkIFiK4VpkUCtqAmMnrkfuSDynEsSKQN3qpr4pJuiWMp0iAYQhPH8GL\nYjPTFQwtjZoOM/uV818MqW0WGO78aHuM/3P3LGy8Fvx7DUp5uTsoY3tOOmQh\nti7EB1tDtPXGbZhiJFk6rcYDcXMLkK1HMr+cPan2YUJjbUO4FDWI2Fj9t6ZH\nqSstQt2qMpAio8M8EB9TUE7ZkvgT9x3bk2lVIEtsjqxjoFYHIK6Ok/L/rjSE\nsw86\r\n=kB2j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "06fd2d4869caf6ab7fe3d8f07918b6f3f2909339", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest run", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.10_1643122233241_0.6668551206288387", "host": "s3://npm-registry-packages"}}, "1.12.11": {"name": "jiti", "version": "1.12.11", "license": "MIT", "_id": "jiti@1.12.11", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "b556410fe009293ceb68bf80b05f49c8e472af9b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.11.tgz", "fileCount": 13, "integrity": "sha512-1QGzY8y2fPqvWHNu3Y8tvhNSddSYwEoJC0MYvGDW5w22srN28OpyTWo2j7SbKFMEjtH0jkikemDTigH5H9cRXw==", "signatures": [{"sig": "MEYCIQDMaKsfq3UqFXLKy/Kf4WWpXLht8TBcBKwMgc7WB6dpGwIhAKhgfVW1nssel7aK4O8oie0X+bx95ydOh8V/jHNcrHCO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1731511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8BimCRA9TVsSAnZWagAAbe4P/iKSZV1IHJ4at08W3htr\nfplrfnmoruBjY4lsu1Ko7tFyTJgyW/p7DFeXw1gQVXSdgAdLbHudFbyjXts/\nD8JKDOpf7XUh5V2i70+ehkx1JzupmB8lK2BSFYQ9XP8bf4BynVRNRjZDjbCs\nLGG+NXZ4Icr9/DAkI5AwYHq9qlYjDBeImVHALl8oPYpWHgCY2asWBQqSmZjp\n4/uBheRbyJ+4+0QHphV2GyUYT5iZEHtoIwzgzbWsxXNtrt8GN8ZlUSxexnh5\n/K6eFS5e2TsuN64Fz8FKlhMXL8nv+qTSk3S25sANscJSS0TRMu5bk2k5eSnt\nHbnDM6Yn64mlJSoOCcgEZl64Jm6gU+WRZKs6NWz7CyVJP/UeVwNZAvXsI4bn\nleVcjF7Itbh47K4aDo990PWvhlHekBEdFFzslqrRcwn/itIFbibY2M/QxSZv\ntawXxNNCr1HKoqceXKPJwSJ6Q5y8HdZpkNgFb3m6FEqIXzNPpEAIuR4CNWJy\nQKoZazJQA5GoMIOompjzIsOi2dJZAo3IlGh5rmx/V/JUXDnlyHVGBEGshFx4\nVkvx8MR9AtXNS10DMRNF5rsaWKyJON7QM0AW4oGM/Bb/gXWEUM99mZR+1CJt\nEYv9WBMdqjx8FKDqvvAoQf+yEw5Gh8MBHVpXWm3JkN/gWzmUCKl3kLRT3nOc\n8nPa\r\n=XsVT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "e0db5f7406583bffa724f1e4217b89a83a95c52b", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest run", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.11_1643124901712_0.6601054939415001", "host": "s3://npm-registry-packages"}}, "1.12.12": {"name": "jiti", "version": "1.12.12", "license": "MIT", "_id": "jiti@1.12.12", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9d4992c9abdf669ad49b235d79b5c92893fb294b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.12.tgz", "fileCount": 13, "integrity": "sha512-7gdxH+yhHKzzfQj2Q38KsjRjt97HszKZE8XbE4JngUEnBq7T8eOpqE4h/wKD+TsdUCwSMw7hPlGMMaZbHFxfkw==", "signatures": [{"sig": "MEYCIQDgPNU5tR8vsRjiHtIjycuf39Chf/85hj8v/nDa0dUVIgIhANI48CvgKv27VTyS2B9yKDcYuNoooExk2Y521TeNqkA1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1731550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8BwvCRA9TVsSAnZWagAAazEP/36hjSmtRujgtVxv/T/K\nHKPLkHkJNHV95ZIMnjBsHnnyqANulq9UYG5+f1jTrwSo6A+a95rXVPj9EiA5\n5Z3Kn/PyZVrQsnBv1RwLabCRWuUP4/gG5v/b+qk/om7pthUOKbE3nhXy8+7g\nfzmVZObLSfH3/BUHh1g+NNg9vEecBcQLpJEU6bhTPJGsE7erUNZ4KKqLwxrL\n9Qm+F/13BRP83FAotKNurh83oc8ihDTtF5H2JQ73XmHHB+1ITyBR00vkYJM1\nq3fsNslXgNB16YhjQbotqJ6MN+xmm590WMAlmzXc0/lvv5xlkrQvWo3AIQMX\n+75b6pWhSz9CSyRlhoserflrM5+4iVcL7gMYqCpYvhq+L2X/AP+Kh/Gti7BL\ndMp6j3mPpgMBqqGVCKNE6S/E9ljPg7zYRWcO1rDE6jfzeHS6HzgO+tj/wKFm\nxiPrH2kyR7g4pP6ZjlxOcsrg2Y0+n4GsIiITJXlP9xLYhamxWYjqxI+oP79N\nqbhifxzqW6c1hExbf/ct+2O2tSYHsbV9SB4QcvEFqPk2F3XiH2gQe8lOOqEu\nOZP8Gu3wcl/2uVzdjTaB9DXHXyf08vVzJIZoapeU2bCO9L4UrHXO+3rFGXp8\nF9NWUsu7fgnSCDrK+xN6VdoH3Cu6Y0xnTVSYSeh5BO6Q6zUwGlTOeKtFdCPH\nHhmn\r\n=f++y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "9868198bca6565a65d55d9822d3cfccf1c0c7fa8", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest run", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.12_1643125807416_0.699954423423089", "host": "s3://npm-registry-packages"}}, "1.12.13": {"name": "jiti", "version": "1.12.13", "license": "MIT", "_id": "jiti@1.12.13", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "652a018606d1971e219eb122e26bdbdbac497a53", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.13.tgz", "fileCount": 13, "integrity": "sha512-OhsYlXc2HLjuHZML6Ay68/l0KT9lXMw9zo1+TMIaxW8c0TS+kJnxgx+he4yBPFiRHi7UIsG2ANZjHl2zU86j2g==", "signatures": [{"sig": "MEYCIQC6LUJ0GSSRyT+JXTsYH333KftcqY1k+GvvNeI57+8uRQIhAPs8T9FHIiMHcEYXn8yCJDdQxl5ysMfXPyjpEo4/H+J6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1731677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8B9xCRA9TVsSAnZWagAAm/UP/1Ns3Qe0l7SQ7AiQRZoY\nyXGbTvqEDFT4FA26apzVfC96vXzJorlqjU/BkbYkXZixgjOOfAcj7FsxqwI6\nfWgRqDi9Hvv3MhpiNJUfaARs9c9hWsbtiVsUUDNz15cOh5tkUt8sHZ5SJLV7\nTGpzZUhhuKScu+XcmUPsmrmhb12YO9eN6MN9YC3jMdUnPgG0thCkXlp8Pg25\nqdg8j+tw1j7CHkNL8j4Lrv6YEn2BZT2sJ2mC/uKFtQtmPqRY5CFGQR5aZhhU\nOZMTlvBgCYuqThA4CTOjomefDknsIs1BJZ0FWIdy+Q8B5w99mHbf1l8NsfC/\nUdmmZFcNEmw4gSwyYf9qyfPaok22uoqJ4HT7jUtwB5ZuL9waITIZu1l9CZAu\n7o/6rFkvjq6NuxD8XkWTTVSYhkzmTO8EUcN6drJuwmRoDB6Sb0mlNNFKOwKP\ndDWgmpLrkUrpww0HhFpTmOvQIFWX7nATlwN8I/WMNS1LVNloOMqAkKl3lZNc\nFLQFLFDs1Eg146euW1ry4SQyVnIgYEWJ+vrx4+ZuFyYTOcgP2jRNkCSQei35\nMlI7QJth+VKGvkq/RqyW1usyrO4bpQEQi6eQxTZNtIMvGDV0NCxG/VbxTFnc\nHXrLajZI2nD5JStHeJXu6qX90X9kNnR/LaAjbee9Ua4j3ZPtQGBiWrnJCZBC\njzX5\r\n=5Qa3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "0ea19c8f03f4b72edc29ff238bf6c374a6c10de2", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest run", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.13_1643126641692_0.19089824218940699", "host": "s3://npm-registry-packages"}}, "1.12.14": {"name": "jiti", "version": "1.12.14", "license": "MIT", "_id": "jiti@1.12.14", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "001e583136fce21d62585491aab96c9be1ea1f16", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.14.tgz", "fileCount": 13, "integrity": "sha512-rR+9GYXtYiIod1x+EHFYcTyqIPG4cAHG3WYGHkk71MVjzPCJwOI4E27BPOXQhUf1viglcLtqj3WotFIOJQwM+g==", "signatures": [{"sig": "MEUCICZAcFpTy/bsqjw3n1SbnO/eiUoztW2ehtqopgpDvvPwAiEAuH5gKY8C0WFLQQaBdgxP29fN3SKFlmjPmgI+X0OUIPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1731969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8TTtCRA9TVsSAnZWagAABxMP/2IPMQqHJZwwiPAm0YmE\n0GctWUxn5RoZQWU1PFSuXxLQClRALgXB4X6H6FH9rnEO77CWqQ7zsQ2Z1CAD\njtS/xwy/wXrXK18Q3C7F8mv8JThDGBAeIUetUrivjLz7VbW6gserndOUUTU2\nq0mSwk6epvYwpjpB7oYX9cFbv6m6/Gqd6hEYXOMSYtFHfW/8aHMrWLbHGkmf\nrFY06PbrJuoloKxnfvY5pSl5JEGPrP6cTwio9hWc8SfhTHqHvQKETkOAB+Z6\nZLHrh1OvTlTGNv7AE7/7jT5BHcUY/yJv8Y+6eSCLQLD1jB4iz8Q1oUmLSJq1\nbsx6e1fcR/5VJxzEtGGIdwTv0EvqXsuuHdYJcykpGf+9sySlj1si29Ts121m\nVFNFaro+EJ++EJNL1r4OuInbglhANeJfKzdkYLSODhs0fabVioMfxs4x2m1m\nMbn4Aa2zgkJdCqldsDeJQXVnbxYF/0Gwo1aBEK9mIFU8wasD3SZcdaTW1ZVS\n/HWnRHN30M70eWVSd8dMBT0v59JwPRo1sF5G6oNwwVZ3q8ETNxBV1zCm8Hot\nGbaTQLrrEs62wlainLLUrKzJJEoqv1jkemag7rIZ9f6obvQ/7H9ewbROjPgE\nUvYjgMvlClOKHtICdqTh645386nXEfu7KCtWm4Sq6LYMxoebfvFyffFridLf\nBPJX\r\n=b+wN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "a78949bdd3921e3bbbfc39ed90f2d6a9337c30b5", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest run", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.14_1643197677136_0.6753797021675614", "host": "s3://npm-registry-packages"}}, "1.12.15": {"name": "jiti", "version": "1.12.15", "license": "MIT", "_id": "jiti@1.12.15", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "8f6a141c06524ab32e05d5e3c9b33eeda54ae775", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.12.15.tgz", "fileCount": 13, "integrity": "sha512-/+K89y6KJA2nISbWrlc/773XdpDgSQq/LdQ+ZZyw2jRxUNyquPtbsDCCCMRzzNORUgroUGc4nAXxJEnQvpViCA==", "signatures": [{"sig": "MEYCIQDCBX45Zc1nUJjlzaTwzOdudWynPIivL81xf2eiyxHuvgIhANMd9oYkXCepjtlKrvFHj9LOIMKpZRtH/ZfyqCIT+pBS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1732012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh89xoCRA9TVsSAnZWagAA1UkP/RFgBTjIWqTQ4XhmeqkT\ng3UGLP5Hz8KpfaMTXcEyP8aHRZStVhCiJlLKQnp41M7VDzr7Zmod+Xh96VAj\n7nFZfuZE1mDkWQXMTDyfNXITIlS+c0pjbnpbNRBE6j+FZiXmjrZvIMuf6twn\nWFSjMGIOp77hVI1IYs1ol6jDOFgt1RHqrg/wHOPB4JC8Nz1RKyaEFZHZKxz9\nqQ3o5wdaVUtGjoHE2iqwcDGu+JTZUQ31lO4fBEET3Nf6Rmx4aRfFPQlukugO\nZARkPBc2P/b5DzSxSF94VnOIM9Vem+0jDl5iiaJEOGZor8HlSmjIjRuBQErf\nP3YkP/wej25jFmKdTav1OluGnLQavx+zp2DejtDBkR5bchPO6DIwYtM7ZCk6\ncPmqQIFf+i8s6jzqRkoQ0cxU9hyGjTURTXr9p9MfDjUTY2rUmvbrWGt4bao1\ncV7hW771+tOKCQfoE/hSSm/baBTZMZNA3IVA174ePlQznUyM7moQVz5W425F\nYd+93pA+75/2xWC3vRSJpWJRIhf+R8fAu0s6mVEDIDUEAocOpFYkS54RXVPb\ntKh2R1mmj9Ujh0BIyiZNolqKqqYiW/IP5VssLbGLPBNWs8W5KQgBP78dsqS+\nTw5168iP+GNYAG/mgGvp1Sw4u6UjoE0CH4I42dmtkEXFshFbJca50RVJuyj1\nrH/B\r\n=+9an\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "80f616f1a86c207736b5e37235509b5751f5225d", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest run", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.12.15_1643371623838_0.09633712731021182", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "jiti", "version": "1.13.0", "license": "MIT", "_id": "jiti@1.13.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "3cdfc4e651ca0cca4c62ed5e47747b5841d41a8e", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.13.0.tgz", "fileCount": 13, "integrity": "sha512-/n9mNxZj/HDSrincJ6RP+L+yXbpnB8FybySBa+IjIaoH9FIxBbrbRT5XUbe8R7zuVM2AQqNMNDDqz0bzx3znOQ==", "signatures": [{"sig": "MEUCIQDAlEyUSSFSfQ9AIwcbX7hZLI6kRn6KujZtrvj+V+sIQAIgTFsADgxTfw6nPCIxuCQ6ZLVns2OnSSnVg0hNJ3UYl7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1732608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD9XBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH2BAAlCyCTMgfAIypCAg2c+oG0E+tn3gj91pqLvzIVaZbD5Cnk7Xg\r\nFOFqWc86x3LfN3FexcICSU5IZ4kp8uoY1R64FcIi5VlBsCg0BTR2YixfW3cG\r\n72yrSHbHz5Z547ez2twL1XL+5QEKVqazykVaIdgbZ12lLwumFexvIctHKkW3\r\nKYXb/Z0tb17MCEW/O91Y0Hhs3+QNVsXDkma4I6J3Fw7LFIpRbSaDKW68k/EO\r\nkdafIC3+Cq0sCyylLE0AhDMfV/OLK+XNv478Qk95aq8Mw9qapYEDD9e/vEVA\r\nJrn49VQvy8xn1sdzIYauQpwzvMjViN2OuOe6O3A3A0NF8KuWxMfZK+NegCgP\r\n3tgU6QhEddjrhaBl1Y7k66YpN7sdkuBoKa/GiZlfkk5YK40Qh8nq89LEAzcw\r\ndPGPczJl83mmC1jxi6sHV5kT6zcTBCQdRlUTV+ywMi1Z6OxtySB9TL5Bva1D\r\n8spkf2wFnyZkfmW3cAIhatzdO9VSBnIMWgGNykoGc7MBUkcLsqkGatWsD54n\r\nJuu7N8g8BHpRZuKYiRcLrQW5ET2kz8TwDW/Gl6r660a0pbVi+qOLnYX9XPHa\r\nQMoAFujqCuYaNqTXlQaFLnwrhEO/epv7jCIpQlLn7Wghm3/YqL24hL0/xujd\r\nci+qY6p7x02Y3cw+bU/J4x/hRSIld0qUjI8=\r\n=5QdM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "95476f32aee10e464c5cb7db3e59fc06354593db", "scripts": {"dev": "yarn clean && yarn webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest", "build": "yarn clean && cross-env NODE_ENV=production yarn webpack", "clean": "rm -rf dist", "release": "yarn test && yarn build && yarn standard-version && git push --follow-tags && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "babel-plugin-transform-import-meta": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.13.0_1645204929668_0.40097228440532295", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "jiti", "version": "1.14.0", "license": "MIT", "_id": "jiti@1.14.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "5350fff532a4d891ca4bcd700c47c1f40e6ee326", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.14.0.tgz", "fileCount": 13, "integrity": "sha512-4IwstlaKQc9vCTC+qUXLM1hajy2ImiL9KnLvVYiaHOtS/v3wRjhLlGl121AmgDgx/O43uKmxownJghS5XMya2A==", "signatures": [{"sig": "MEUCIQDlXr8kXmMnx260c/y0tbwmDeKN6NJZU3+jerhCxgLiKwIgWXBX4DoYLyHNPo1tNO3/GBLzR/KsvIZlyAL8FK+UgOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1706457, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisG8FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYzg//ZHYocyEhk7hbi5s4+H/8dkbdCCAOG0d+GIuVaV6nsbinA4Xj\r\n3VekyRHybY9lMZL0Z1XAlRIeB1gkKIPtfEblAqi7BQ+1XcnG4WVB2qof2eF4\r\nyS1+DOfyQCvNSumtcxQiq0vKFgeCudP9p5BURdgpq9xEUOWGGeNbywX2e6Hl\r\nMP1FMDRtQV/JggrWsBhCmkVoSqwGynkGgBx3vqGqhLmEVhNlnTripqiKsfJS\r\nZrDCZHUQ0BJWHsVMeQCeecRvADZS588p38FE4URK5//7uzfqkeZZMTYUbWBP\r\nxYHPRI2amas6ec0oGRlsrzfgZl8ckLYpIZe58XMlpoG8xOUmcaCHt+n3Dc+y\r\nguFa9SziAAyNuU7XnEZ3BpPUsegv8DmBPsetkxF4ClBSIX40GvjoL5zFOpMM\r\nOFVwpk8T6C2olkSnAFx5IigfjORNFz9MrHavUTpmH2gFKAcT1ZofdPQaf58P\r\nJolcpKAScaZNylIBR9jrfYuL+941YuWjjuYCEBoAlF8BAe1Sr02gnwlTOBBH\r\novoxprUg3PYEquzOfnrF7T62r8lWUw1BqEpqPVSQVy1s9MaTFUtISKOYiiqQ\r\nzHM28WUolbaiuhFq9e9KAtH3iAGwDOWDrzyWiCgOROGbaU6P4+wpKpoFyk3P\r\nCWUjc9Umv5oxFijzS9rpUhnHvhLE2xjAyvc=\r\n=KgK7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "_from": "file:jiti-1.14.0.tgz", "types": "dist/jiti.d.ts", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm test && pnpm build && pnpm standard-version && git push --follow-tags && pnpm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/tmp/afedbb6e79c3e7bf1c0df0a0c56526b6/jiti-1.14.0.tgz", "_integrity": "sha512-4IwstlaKQc9vCTC+qUXLM1hajy2ImiL9KnLvVYiaHOtS/v3wRjhLlGl121AmgDgx/O43uKmxownJghS5XMya2A==", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.3.0", "devDependencies": {"esm": "latest", "mlly": "latest", "acorn": "latest", "destr": "latest", "execa": "latest", "pathe": "latest", "tslib": "latest", "eslint": "latest", "mkdirp": "latest", "semver": "latest", "vitest": "latest", "pirates": "latest", "webpack": "latest", "cross-env": "latest", "fast-glob": "latest", "pkg-types": "latest", "ts-loader": "latest", "typescript": "latest", "@babel/core": "latest", "@types/node": "latest", "object-hash": "latest", "webpack-cli": "latest", "@babel/types": "latest", "@types/mkdirp": "latest", "@types/semver": "latest", "estree-walker": "latest", "@types/resolve": "latest", "create-require": "latest", "@babel/template": "latest", "standard-version": "latest", "@types/babel__core": "latest", "@types/object-hash": "latest", "terser-webpack-plugin": "latest", "@types/babel__template": "latest", "@babel/preset-typescript": "latest", "@nuxtjs/eslint-config-typescript": "latest", "babel-plugin-dynamic-import-node": "latest", "babel-plugin-parameter-decorator": "latest", "@babel/plugin-proposal-decorators": "latest", "@babel/plugin-transform-typescript": "latest", "@babel/plugin-syntax-class-properties": "latest", "@babel/plugin-proposal-optional-chaining": "latest", "@babel/plugin-transform-modules-commonjs": "latest", "@babel/plugin-proposal-nullish-coalescing-operator": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.14.0_1655729925421_0.28794202233541943", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "jiti", "version": "1.15.0", "license": "MIT", "_id": "jiti@1.15.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "cfa7ebfe4a60d77cf3bd4f8630cd99225b638417", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.15.0.tgz", "fileCount": 13, "integrity": "sha512-cClBkETOCVIpPMjX3ULlivuBvmt8l2Xtz+SHrULO06OqdtV0QFR2cuhc4FJnXByjUUX4CY0pl1nph0aFh9D3yA==", "signatures": [{"sig": "MEUCIEv5FmRjRZeRGjkANPKe2vhaWCEe7yIiEh0j9zbhvePqAiEAp/YnTp+L6kmExGdK/kn2EvWSuroain5G03SGRMcGPSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFxdoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhRA/+PDjayaei6aOrlCHp5ASP3fVBPz0Qh3XPRRftuU4IfAR6zGgf\r\nACQYb41XHFnWl9B/STck6vVDBuLW95/e2Yo7sPPRRW0MWSMEx95ZobpdAXJA\r\nP5ZZSF1b2cpPiNu5pvAJVgYjCH1rUNo9RFeBoDWIwxj8DNcf6vSZ/xVBHKJk\r\nv7KugZ8FindX1kA1UyJUX33OpdfgBSvTjHfbEwQKp0Nkdlf8TsgfwcGEdPHU\r\n7nWCBj5RXEnUvJBvYHxWd9fzkf6xbRNX1lO/dbc9deCU+he08cTuSuQoQ1qs\r\nqMMlmYByEqguDURt7/AatNJ02mQf2HsU2tNI1tHaOvvv1kHyZzcLEgxvzaxU\r\nnixiRvECOfdYK1Zl9axTyL9Z6gDiXdlzSmSnUZ+78LRdlpedirlwZ9wcN4Tq\r\nSV7mV4e6PkfrL5UHUHogFxsyW+eKx50S9qOoQ1Yg7dfE5buiK0LV9KgYip+D\r\n2B/7Sva3kFnPQpxrrN8C+BecDdgHJ5MXmcmpH/dkGLiqhzBUiZdxRIW72j3O\r\nRANdXsTMfchhOTSyXj0igj0nAHLRWIATJvCcKzu3ZrI4c7ICV6njy/ct+e0K\r\nw0wC2UcPqQgUbIEuLV+Lr34FcBFtDWf5HDdNzU27/XBHCHXSqfnmCaH0VK61\r\nABmc6Lvjg6dp9V7KUjUZZ5IzBipkwPMLAfM=\r\n=eIHX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "_from": "file:jiti-1.15.0.tgz", "types": "dist/jiti.d.ts", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "vitest", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm test && pnpm build && pnpm standard-version && git push --follow-tags && pnpm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/4d56aa3320826e424cb4d17fba4cf665/jiti-1.15.0.tgz", "_integrity": "sha512-cClBkETOCVIpPMjX3ULlivuBvmt8l2Xtz+SHrULO06OqdtV0QFR2cuhc4FJnXByjUUX4CY0pl1nph0aFh9D3yA==", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.11.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^0.5.14", "acorn": "^8.8.0", "destr": "^1.1.1", "execa": "^6.1.0", "pathe": "^0.3.7", "tslib": "^2.4.0", "config": "^3.3.7", "eslint": "^8.23.0", "mkdirp": "^1.0.4", "semver": "^7.3.7", "vitest": "^0.23.1", "pirates": "^4.0.5", "webpack": "^5.74.0", "cross-env": "^7.0.3", "fast-glob": "^3.2.11", "pkg-types": "^0.3.4", "ts-loader": "^9.3.1", "typescript": "^4.8.2", "@babel/core": "^7.19.0", "@types/node": "^18.7.15", "object-hash": "^3.0.0", "webpack-cli": "^4.10.0", "@babel/types": "^7.19.0", "@types/mkdirp": "^1.0.2", "@types/semver": "^7.3.12", "estree-walker": "^3.0.1", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.18.10", "standard-version": "^9.5.0", "@types/babel__core": "^7.1.19", "@types/object-hash": "^2.2.1", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.18.6", "@nuxtjs/eslint-config-typescript": "^11.0.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.19.0", "@babel/plugin-transform-typescript": "^7.19.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.15.0_1662457703907_0.49870543709888504", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "jiti", "version": "1.16.0", "license": "MIT", "_id": "jiti@1.16.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "f72065954446ad1866fa8d6bcc3bed3cc1cebdaa", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.16.0.tgz", "fileCount": 13, "integrity": "sha512-L3BJStEf5NAqNuzrpfbN71dp43mYIcBUlCRea/vdyv5dW/AYa1d4bpelko4SHdY3I6eN9Wzyasxirj1/vv5kmg==", "signatures": [{"sig": "MEQCIEHxWmrhw0Gl9Swoj2gAnHXdU6U9kejmLp1+9xi+RJy+AiAnjOE//wbtUN8oVbz4SPQMiE1wdzYb0fc7LpqxyoFdZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2379603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKDxpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+aw//budimbay9s1p5xbfOzjk2fxWaR10QJdvX81GkrvHdPXRIX4X\r\nxUAHt9mOr52LtRRbGXnPRcNo0sQTLpVpj2DPqUoTkr0iXtyxkASGhaIVXvIs\r\nAdU8wGhOqPzaszbMidBJPAssR22fPYN171zjXPAXPnrWwb7+WiRmuWyBuscP\r\n/GuWBNzi/iXIAJv5BRf0ypa8p72HVQq6tgc3gkR1bjxeXuRRs7rXGDbtfsVy\r\ndy61Qvi1HfpsCir86C6TUSgG0KEoUoalKAPgfhkWsP5aEI1Vx8+6/eK9Q3qN\r\nbvzT0KFyflWqm+h15kfvRmnRWDcvfOo23hMOy6l0dLRitHF0dYsQd8NwKuZN\r\nNFcyQTckI2VTlhW+Nx3M83tlvI02bQR2i8ZvQxYiW6hi0XDeF0oRgDMLXw93\r\n2Cnu2Ot8n8mFllqVXEWVjyMkN2KUUYkcGkLEKjqxI2CA9syqwtNLdnqcqhVj\r\n+uaBtYhrjCd9BSVY3n9k1dm/4hr3iEmh7EtOvadT0jWlx11xmTHzMEZjFPP3\r\nSAKv77BZQ/j0b7RF61VqaqmRwxPkEAuoquG1ksQtE2QE51hRx5LdImd5hlGI\r\n9KnOpXFcFhlKhH8260Ff7biJcekTg7Cswoa21LqllLwB2NaZuDekROzvLjqz\r\n+mPNWIFnsS6E8NhSa1eLKft3yhGZ7qryEOU=\r\n=hfnK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "_from": "file:jiti-1.16.0.tgz", "types": "dist/jiti.d.ts", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js .", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/a1c02e9123acc36763dfe175dfc5a604/jiti-1.16.0.tgz", "_integrity": "sha512-L3BJStEf5NAqNuzrpfbN71dp43mYIcBUlCRea/vdyv5dW/AYa1d4bpelko4SHdY3I6eN9Wzyasxirj1/vv5kmg==", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Runtime typescript and ESM support for Node.js (CommonJS)", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.12.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^0.5.14", "acorn": "^8.8.0", "destr": "^1.1.1", "execa": "^6.1.0", "pathe": "^0.3.8", "tslib": "^2.4.0", "config": "^3.3.8", "eslint": "^8.23.1", "mkdirp": "^1.0.4", "semver": "^7.3.7", "vitest": "^0.23.4", "pirates": "^4.0.5", "webpack": "^5.74.0", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^0.3.5", "ts-loader": "^9.4.0", "typescript": "^4.8.3", "@babel/core": "^7.19.1", "@types/node": "^18.7.18", "object-hash": "^3.0.0", "webpack-cli": "^4.10.0", "@babel/types": "^7.19.0", "@types/mkdirp": "^1.0.2", "@types/semver": "^7.3.12", "estree-walker": "^3.0.1", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.18.10", "standard-version": "^9.5.0", "@types/babel__core": "^7.1.19", "@types/object-hash": "^2.2.1", "@vitest/coverage-c8": "^0.23.4", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.18.6", "@nuxtjs/eslint-config-typescript": "^11.0.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.19.1", "@babel/plugin-transform-typescript": "^7.19.1", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.18.6", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.16.0_1663581289697_0.4902727775633162", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "jiti", "version": "1.16.1", "license": "MIT", "_id": "jiti@1.16.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "48529b29c974f374a433f3a8a2760c2471e076ab", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.16.1.tgz", "fileCount": 13, "integrity": "sha512-kJUp4Bj44uTaZAwG6R2/GjbodOWHULn8Swue0B7tY8v5BpTkUvDR+zBM5tsbC4x/jCeYDZ+mAdrUIScwIo4oPw==", "signatures": [{"sig": "MEQCICPXNgxrhamdJ/uIqXiZomWIFgmNWcJoV8/BjgzCpiJvAiBKr633wfWu0hM+iWfqqMH85DKRZ5QDjeBwAEmw8HToTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1901593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtCfCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1VQ//Xjap5jiJReCUwA4L/y6PsqIn71/Jpx1INsL8TnHAO2NNs7qi\r\nEhShjVv0rDikDB4dizZP+Tp7Id+kQ881/YTLFfqDDpvoK3mQzTyIzKfcnLRn\r\nJ7jB2JT65/eUooFzZgYTVyD+FSmKhQHKWRGOzMHJtf3n5cl58jolmu9bxhM5\r\nbdnFD5BGAM9Md5tsckD4ghicLp8LPhqHUuFE49Bj6ipRfK+h6MXIzZLUVlH3\r\n6PlAxgT9lJKOKBn+gS8li/BQ2YJk8DLcbBgFhwwAQRPsp00yjbxfW5AbX7Lp\r\nqvSrRx0WuXwkKyYzvByorqi5J+oQpaBIVoiK6BY0hgDFJnM2XsgVu9EUnh5l\r\nsP4t+CAzKBI37x2l0Jn4jSTCdT+L+9059zlfh07Z+liWy3LsFWPB6x2RgXaY\r\n7IKdzzi7uL1ti7KV/UjkFFVX6x++YHmGw2EAKiVMrU0elZLqZbpqb77YpOS0\r\nwoXRZyL0MU7MrXMY92b8s0o0M7QwLMvec7tPsE7G2G0rLZ7IWsJ3eTEHsZRX\r\nOA0XENxGPJkMbzPoGZqKNka9yIEZ+CzeikSrnJAhUPr6SlBkOIdwGelShVgJ\r\nf5RdoPJxc7xR84Fe6/j8k4q4HVu/+Br1hlR9A1enytAS+7FWKG3coXIOXDZG\r\n/14GofegQng6zs5+obKjOn8J+2RH9bnBkDM=\r\n=yb5Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "_from": "file:jiti-1.16.1.tgz", "types": "dist/jiti.d.ts", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c .", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/5a8a102bb7d42aec7c3915ac98726847/jiti-1.16.1.tgz", "_integrity": "sha512-kJUp4Bj44uTaZAwG6R2/GjbodOWHULn8Swue0B7tY8v5BpTkUvDR+zBM5tsbC4x/jCeYDZ+mAdrUIScwIo4oPw==", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.21.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.0.0", "acorn": "^8.8.1", "destr": "^1.2.2", "execa": "^6.1.0", "pathe": "^1.0.0", "tslib": "^2.4.1", "config": "^3.3.8", "eslint": "^8.31.0", "mkdirp": "^1.0.4", "semver": "^7.3.8", "vitest": "^0.26.3", "pirates": "^4.0.5", "webpack": "^5.75.0", "prettier": "^2.8.1", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.1", "ts-loader": "^9.4.2", "typescript": "^4.9.4", "@babel/core": "^7.20.7", "@types/node": "^18.11.18", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.20.7", "@types/mkdirp": "^1.0.2", "@types/semver": "^7.3.13", "estree-walker": "^3.0.1", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "standard-version": "^9.5.0", "@types/babel__core": "^7.1.20", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.0.3", "@vitest/coverage-c8": "^0.26.3", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.20.7", "@babel/plugin-transform-typescript": "^7.20.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.16.1_1672751042141_0.7176412288962482", "host": "s3://npm-registry-packages"}}, "1.16.2": {"name": "jiti", "version": "1.16.2", "license": "MIT", "_id": "jiti@1.16.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "75f7a0a8fde4a0e57e576f7d329491d588db89cf", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.16.2.tgz", "fileCount": 13, "integrity": "sha512-OKBOVWmU3FxDt/UH4zSwiKPuc1nihFZiOD722FuJlngvLz2glX1v2/TJIgoA4+mrpnXxHV6dSAoCvPcYQtoG5A==", "signatures": [{"sig": "MEYCIQC/05fiM5tRD+NqV+GDjDXWVR1+CvnO3pigECvVyFgIaAIhAOimd9oAS4Eyt3Uwr70G2oJffDB+dXAnFNO2SU0udrYG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1901573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvUefACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmQw/8DO9tVtxadvIm9ZmOme8l4JTGItQWq4q25P8kZbnhlz4dz9pC\r\nbbBO2qXBWOdohJ9U2kB5MDct6Tt8ZzcykehFlWhUE8gXzyqJldIq1gXXMaJj\r\nYvIcJU8Xj6WLaUrRnjrm+VtySeBXdaSuQGNPRBXcSqprgqE7NSTZVD3xZkvk\r\nPR09oykUJsmSeIt448BKX7ivau9625ANiyT/O+EmSm4/o06gvda6w4dTRr0f\r\ntqj4I6hsonpdw8OR9DYVsmuIBluNfs5ZE9A2s+2h0zKsYQCfLclDioB0h+mg\r\nYw869F6xhIFnLVpUwCxv9R5aue2s7PoOVkfk/adFxy/LHSRbFD4Wx+Q9gLsW\r\nh19s7RFLemzdKWsPwAX9wklaog83vKfg/7JIDny0vOLaNps1VzIUolL828EL\r\ndUfBhloVMELq2K/XPp8sqxz/CvAjdU3to7BU+N7yyInvRO7/8qCgfU9cFYnT\r\niENRH9riP0nT7G0bW6zWvvrabzbKMcFfrVmSXf3mygTs0MPx78OoE+m2/447\r\n78F2BXs196eR9mQ22XatS/TQjP5eLgo4aXmWrOWvkUdIXX2nCoQHY7lMH4J1\r\niUQatBpX47+3DNSuHQefemd0AeEeEMD02YWKR86ihdMsRMTOA7fEWh1Iw8aX\r\nyduj1jdfdRSxwgwD1Q2TfC03CqKtJh+w9T4=\r\n=Wfwx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "_from": "file:jiti-1.16.2.tgz", "types": "dist/jiti.d.ts", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c .", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/c042f899d0a6fdd719867b1f8f3b6526/jiti-1.16.2.tgz", "_integrity": "sha512-OKBOVWmU3FxDt/UH4zSwiKPuc1nihFZiOD722FuJlngvLz2glX1v2/TJIgoA4+mrpnXxHV6dSAoCvPcYQtoG5A==", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.21.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.0.0", "acorn": "^8.8.1", "destr": "^1.2.2", "execa": "^6.1.0", "pathe": "^1.0.0", "tslib": "^2.4.1", "config": "^3.3.8", "eslint": "^8.31.0", "mkdirp": "^1.0.4", "semver": "^7.3.8", "vitest": "^0.26.3", "pirates": "^4.0.5", "webpack": "^5.75.0", "prettier": "^2.8.1", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.1", "ts-loader": "^9.4.2", "typescript": "^4.9.4", "@babel/core": "^7.20.7", "@types/node": "^18.11.18", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.20.7", "@types/mkdirp": "^1.0.2", "@types/semver": "^7.3.13", "estree-walker": "^3.0.1", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "standard-version": "^9.5.0", "@types/babel__core": "^7.1.20", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.0.3", "@vitest/coverage-c8": "^0.26.3", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.20.7", "@babel/plugin-transform-typescript": "^7.20.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.16.2_1673349022993_0.30084561987813907", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "jiti", "version": "1.17.0", "license": "MIT", "_id": "jiti@1.17.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9a4e1787b9d83e594a5ad27cdf9c9ab555112ac1", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.17.0.tgz", "fileCount": 14, "integrity": "sha512-CByzPgFqYoB9odEeef7GNmQ3S5THIBOtzRYoSCya2Sv27AuQxy2jgoFjQ6VTF53xsq1MXRm+YWNvOoDHUAteOw==", "signatures": [{"sig": "MEYCIQDCH8OeQ/0rUjn6UqdlyCzthnv2QUcZ7qmYSjE4PwiLegIhALoKDUBeeVA2/KqIDDYZbRD8ETzSnfplBL74UnDJ+FFx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1915798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5BdlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMJRAAjB+upvZbhrHBBCo0D+O8s+l+FpZcH9n2IBprOjvVcgNtj27a\r\nauuABZzd5HmcRkLAsLtwJQYel8S2jSFvuTuc6uAwKXZLDS/e3WCS20DWviuv\r\n2qQOxxnm3R2wzl1VUEtGUhu3nqf81zBYQGL3v2dW3gY1k9WRneWvd4FOEM5M\r\n9mE4HAMXE4dJi1jve6r44QdP/RYLdh0fCIZXypnE18/5HQjT38neJdGm9fAM\r\nwjCPT84Ss0JpbOiU5BGrBKdRGTFdaa7yIW8BgFksnKGUA+G+41nAdT3GkDy6\r\nqtTu0+ZITYp2wkhQ9emNKl8BGawWXkQ5QzA4TN91MIaP4pSFq52MQTcM9CKF\r\nglMQ1jdcfNZ4bHLQfWeA2ixsZEbeGEQ2f2fJDoJMvZql2ScIOrmSLjSY0UlW\r\nxr7cMXI4Ywdcgr/w6grEOB7Mmn6fw+lQs26mgQmVIY/I2+Ov+woJFvXyKeOJ\r\nra6sNb0s9DBUTL0L32seKTDB+o69dyHiIPQNUU/A2wLY2J1WACAVrbYaFvd9\r\nAVdUXi4fMB4roPNVpiEw/7TZYGJo62Qa1DpCwQYCJckCWA6G0Oy+egy9YlBP\r\nTun70YZgPtfDtWP5up8C7Ksxqt65UwDvV9MMKSaM0ouoi0eJD62z4x+ee5Dm\r\nFnT1rjKUUTqmpsyQy62cZadL3mHQ/SM32Lc=\r\n=0Cob\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "_from": "file:jiti-1.17.0.tgz", "types": "dist/jiti.d.ts", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && pnpm standard-version && git push --follow-tags && pnpm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/s0/k4lcb4b50bj9r4fch4_1h_l40000gn/T/2a073d9d4e68a90412a387f9f601dbf9/jiti-1.17.0.tgz", "_integrity": "sha512-CByzPgFqYoB9odEeef7GNmQ3S5THIBOtzRYoSCya2Sv27AuQxy2jgoFjQ6VTF53xsq1MXRm+YWNvOoDHUAteOw==", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.26.2", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.1.0", "acorn": "^8.8.2", "destr": "^1.2.2", "execa": "^6.1.0", "pathe": "^1.1.0", "tslib": "^2.5.0", "config": "^3.3.9", "eslint": "^8.33.0", "mkdirp": "^1.0.4", "semver": "^7.3.8", "vitest": "^0.28.3", "pirates": "^4.0.5", "webpack": "^5.75.0", "prettier": "^2.8.3", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.1", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "@babel/core": "^7.20.12", "@types/node": "^18.11.18", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.20.7", "@types/mkdirp": "^1.0.2", "@types/semver": "^7.3.13", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "standard-version": "^9.5.0", "@types/babel__core": "^7.20.0", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.28.3", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.20.13", "@babel/plugin-transform-typescript": "^7.20.13", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.17.0_1675892581109_0.15215048165070977", "host": "s3://npm-registry-packages"}}, "1.17.1": {"name": "jiti", "version": "1.17.1", "license": "MIT", "_id": "jiti@1.17.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "264daa43ee89a03e8be28c3d712ccc4eb9f1e8ed", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.17.1.tgz", "fileCount": 13, "integrity": "sha512-NZIITw8uZQFuzQimqjUxIrIcEdxYDFIe/0xYfIlVXTkiBjjyBEvgasj5bb0/cHtPRD/NziPbT312sFrkI5ALpw==", "signatures": [{"sig": "MEYCIQCIel0JVx6LcvL0f1AwyyfYqh2kaf+0deBXvBzYdGD2wgIhAOZj4dsIJzkumDvc/hvL1fuTpSVIuhppvzoZ3GSCcNG2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1909689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7sl3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo8g/9EH6QWx/lqQlag6yybI+W52GRxS+szePOpP8AJgHy7778fwrc\r\nBus7hUzzOF1ZWX6dGm4T9TvsSg7yIbxt/sjUP5e1pnVAuhDdBS6Od8hD+IdK\r\njn6xbqnywhSz98ecWZD8Rxp+E50mZGunihXqsZzXOrkzlRH+Nt0LyVUw+FT7\r\nyqpNzpeoNj0dMdG2+Z/aqhQ7ynB+aQ8jGn4BStZ5JpYWaDoLj2i1gyEbJbww\r\n6zP9+ZfMObNCV7LdH7vLbVePY0mS4HwBZt2D18dwWyfKn088EDJXlPPmF/Ty\r\n/kgdxGWNg8ljYdlOHXCANkan8T67rVYvgG9dyM0GLTkf/pDHQy9swZ15COIC\r\nM4ygrnpPypOWeWO3QLc4FZc9k90EIK6vUIyK+TUEkTWTK8+odZyQpIS1dgAi\r\nxyB62KXdoDTgULn97zR0B/h1Mc6kNBvjQTB2g99oJtPuEyFx/tosi6Uc9X+8\r\n8KVpg1s6JpAfKJ4Y/r+YMHFF1jNzwDRcNlF/tAWQryTyDPw07cB4Jx5qA+hD\r\nfaFp0daCKKI3nPHf9auiLnAEWoXs6Uc1k21Ec7LORNhnpgjigz7btBNecAn1\r\nS7GzEpJH3GwXqnXycW8hNa00wR/eAWmY8I1scB3LAkgEtCc08AFotFcWaZ9q\r\nzq0wU9rzCLlAmirfFaiI19G75vG3HndXM+I=\r\n=OyCm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "9b698b70392371486b988c1177ee598563874dfc", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release && npm publish && git push --follow-tags", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.27.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.1.1", "acorn": "^8.8.2", "destr": "^1.2.2", "execa": "^7.0.0", "pathe": "^1.1.0", "tslib": "^2.5.0", "config": "^3.3.9", "eslint": "^8.34.0", "semver": "^7.3.8", "vitest": "^0.28.5", "pirates": "^4.0.5", "webpack": "^5.75.0", "prettier": "^2.8.4", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "@babel/core": "^7.20.12", "@types/node": "^18.13.0", "changelogen": "^0.4.1", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.20.7", "@types/semver": "^7.3.13", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "@types/babel__core": "^7.20.0", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.28.5", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.20.13", "@babel/plugin-transform-typescript": "^7.20.13", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@babel/plugin-transform-modules-commonjs": "^7.20.11", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.17.1_1676593527252_0.39247597506202636", "host": "s3://npm-registry-packages"}}, "1.17.2": {"name": "jiti", "version": "1.17.2", "license": "MIT", "_id": "jiti@1.17.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9e193d171c76b1c518a46c243c410c0462fe22d1", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.17.2.tgz", "fileCount": 13, "integrity": "sha512-Xf0nU8+8wuiQpLcqdb2HRyHqYwGk2Pd+F7kstyp20ZuqTyCmB9dqpX2NxaxFc1kovraa2bG6c1RL3W7XfapiZg==", "signatures": [{"sig": "MEUCIQDy12YvSNP6bTjcpk8SMRBbB+bCy4sg+DW5ORRf/OJkogIgPUL2iXF7DXMVvgtmyrXZI/u0EFzBcjZKFwVK1s7vhvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1947070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBj4GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQLQ//dX5o7Dt4icRLtx4M9jkmv9FixkQM0V/5tcpmAXq1zsKJIKpS\r\nSL7MN9YEAlDPFG1z8CaRyKKMWTZlWHhgF72pdkBmMZ00O7gcBLMh8/BiMeXN\r\nHx8Vr2TClP2KE34f6ikCkwQmPl0PA0VLtA0URxX9D9vaXPFEwmIW9zndkqAV\r\nySl7hPLnyy4+GF+2VHvsFNJlVcbYFVshc6foneLFn0XEkPoYVt5Z/A2nSTpz\r\ndmwL1w2JNltjcyddhy6+3B71nhOsU3ki3ZzNgpVE5WZ7BqbWHjF4OLiEEOXx\r\nf89D4bk5sGb4XtW6Y357HAMXIx4iLjJuTICeFa1LMTbJLaRhMHkjb6eF6T/U\r\nAcu++DLD+E6jgPpMHMhQvtJcculKd/Fda1eZckXTm+mTZlSQfEeqJQdr4EBk\r\nRPvdyTPcs9Dj98+lA/rWmZ/jvFobGPTcQzQ32H4gZ44zNt4mb6wFxG6oDCWb\r\nzngqafvS0o1Q/fGn2DwxywsJEFDf4Vh5ODdENAoWPOwdzHkvkNi9mwuheSfY\r\nRkaMNdllTLufUTjsz2MgpM3ynDgQ1djtg1I5xsG/qH5vmNqmtxvb941vjNpD\r\ngpPOFN3EVc/cNfB8va0yji50wve9r2b1lmT2nHKpeYxjLbWDccx1x+SXL0jF\r\nM0Ne5HolNlm612QGSawdI2jwmdExfq3PJd0=\r\n=cf46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "671d06904a8198e379bb46b3723282571c672fd1", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.1.1", "vite": "^4.1.4", "acorn": "^8.8.2", "destr": "^1.2.2", "execa": "^7.0.0", "pathe": "^1.1.0", "tslib": "^2.5.0", "config": "^3.3.9", "eslint": "^8.35.0", "semver": "^7.3.8", "vitest": "^0.29.2", "pirates": "^4.0.5", "std-env": "^3.3.2", "webpack": "^5.75.0", "prettier": "^2.8.4", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "@babel/core": "^7.21.0", "@types/node": "^18.14.6", "changelogen": "^0.5.1", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.21.2", "@types/semver": "^7.3.13", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.0", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.29.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.6", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.21.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.0", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.17.2_1678130693984_0.41488722831240454", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "jiti", "version": "1.18.0", "license": "MIT", "_id": "jiti@1.18.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "87ce91460a807a38279ee1cc26a489ea313bd75c", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.18.0.tgz", "fileCount": 13, "integrity": "sha512-82fU0lIX1aWCJjZSbqjWlS8HlI286ARMmbx7yrgVzakqA2L9dlj7KDeSsTmz1zDWEF88kmCzU0YHScfJZYyMqg==", "signatures": [{"sig": "MEUCIQCuAHrZBxsTK207qcjJ5Q58IcWnEmw+MSpKSses6PhbaAIgEpZIxnbeAb312pYjUeh+t37AphttoCypHiAPMLCjk7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1946406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEeDUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG4Q//eK25bePddOdqj2mj0jXG0/eCAGbzvWBJnWZS2G2MC3isaPqk\r\nZ0fIHffTBgCeFdORkFHfzV8Pp6Yf4MSGBq84bbM2Td5FkWr9incn1xHtAwni\r\nJBRuduP88c1o7K9GL9D0ahl6mQLTJO81nmbzeqMvcb9o02w3Tma7HvduCuub\r\nXwIreXBgiYYYj/sJdFytBCgW9UXvzpU2kuQf8AS49SzMAf/kEYYQ9yZj4EBC\r\n3TaD7iqc9zquMCaxWDvYD+RlTm22/Me9nqZFNBWi7d6FMWhnTZX+OP2sbrYQ\r\ncfkUc9jm77Pt8qcG/U4en2py9MtQwW/Mew9JbgUyr0doTiKChQBX5M3GY6fn\r\nZsw+jfq/e0H+3pjJdtIg7r0163JbrgBXBrS53FZn56x7Rs+KfoLia4I3Bg72\r\nNAw5X11uyGyWeWaMl3Nm/01pRiHM/TSLs44K5EI/izHnw7uoLNBDV6FpIBEd\r\nQJY4Ez4+/uvr75CSSlRZE5spxzBwMt16yjDhRdVUd1shMYI29ZdBd36G0w3j\r\ncwOe2Jb59u7lQ9tA8op8E/VE7AzN6/Xk3CUO5OmC3A8HTFB1s2aKzBIgH7EI\r\n+46LBw9OgnKdFYul2Qut9ZoQGbF9dy1/J3/H/kM6ybCnv5AZPcZr7Xn8PN7z\r\nFXsWJPvTM0OjUGnwpjCOoufu8P1r9kpMJac=\r\n=rVrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "c65d92cb7ce84d3536ef56da970fb3dcc5624976", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "deprecated": "please use 1.18.2 or later", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.3", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.2.0", "vite": "^4.1.4", "acorn": "^8.8.2", "destr": "^1.2.2", "execa": "^7.1.1", "pathe": "^1.1.0", "tslib": "^2.5.0", "config": "^3.3.9", "eslint": "^8.36.0", "semver": "^7.3.8", "vitest": "^0.29.2", "pirates": "^4.0.5", "std-env": "^3.3.2", "webpack": "^5.76.1", "prettier": "^2.8.4", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "@babel/core": "^7.21.3", "@types/node": "^18.15.3", "changelogen": "^0.5.1", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.21.3", "@types/semver": "^7.3.13", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.0", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.29.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.7", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.21.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.18.0_1678893268508_0.07444152211380972", "host": "s3://npm-registry-packages"}}, "1.18.1": {"name": "jiti", "version": "1.18.1", "license": "MIT", "_id": "jiti@1.18.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "fb2e9033cc60de257926054fd3f0780893fb9e6b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.18.1.tgz", "fileCount": 14, "integrity": "sha512-3YoLqT3pzjfwhVaD3nvBKb2T7897c/X1IHctAX7Qpm5pHlaTmdhvXc8lfz2xH4dNIHRungp9CwTk+bnwBU6CQQ==", "signatures": [{"sig": "MEUCIBf72dV6TC5zMapSZzIcx6FaIz/xQAZJwP/R1zhEMJOOAiEAtkRPNF7cYYx9VS1xm6A2/iMxZK+ykdow151lICsv8ZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2706150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEeFFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovrRAAjIqpxRflEP3bGTAMyLa2iAKCb5+2UCY+iv3VQ/DB7999bf/a\r\nnEGRuH/njs5PNwjdIEDT/0g+L+wOOMXB5I8o73u3j9VZ03MYfwN9YwCGDS65\r\nfX2Z3AWUU0Jn3+6BCafOcbBMWs/c7p7d3+mfgAgW8e6P0DFAT1icI2TCK5B9\r\nvT72cVVPFAyueQZT+2LRSX/Ll7QWkDxImMKWVyx0NjM0A8jAZSHp7r67UGfT\r\njzLX1LWh3nNZ9ZbHjWxMSGelCCKrbAYt37I0RUkyyFAzMIFgC0sq0P8NVBHO\r\n7VnYrdwcoDRPqyW9+xKaDFZsNpXqtwfLCy9vznMgF/MGxRq2EtM/R9Nmj4NG\r\nBF8M5iLB7q6YonfAPebAbJVufYeQ/V33+EBxAGui3g7z2zUOuc3ict0MEl71\r\n+QsFIoUjqdCSpxzD5UEY8UIGtCB90oATVBLjHKngIgprE9ExJEKJ0XQWfWlh\r\nKbVjIZD0Ia0mM8/ffE0Fc0VJcviKzM1fPIEWcrdEIkLJN+oTzugNkzmQb6tc\r\nsNDFR2tTz+n9KBtPMnMmeI+xdi96LmteJb7E08W5oBbW0qp+LKwxrjaosO1L\r\nOnqe7C4bX4h7W2dL6iv8NoWTxGq1TYkXLlQ/vHonSF8J7cwDUgrCuvP+I9f8\r\n1e/7AaUcgwuFrNCfnV61U795VI8PTAhw9ec=\r\n=xKS2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "93512f97a92a40450c4908ec3870bbb579a7a3f1", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "deprecated": "please use 1.18.2 or later", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.3", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.2.0", "vite": "^4.1.4", "acorn": "^8.8.2", "destr": "^1.2.2", "execa": "^7.1.1", "pathe": "^1.1.0", "tslib": "^2.5.0", "config": "^3.3.9", "eslint": "^8.36.0", "semver": "^7.3.8", "vitest": "^0.29.2", "pirates": "^4.0.5", "std-env": "^3.3.2", "webpack": "^5.76.1", "prettier": "^2.8.4", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "@babel/core": "^7.21.3", "@types/node": "^18.15.3", "changelogen": "^0.5.1", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.21.3", "@types/semver": "^7.3.13", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.0", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.29.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.7", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.21.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.18.1_1678893381383_0.5618911374679363", "host": "s3://npm-registry-packages"}}, "1.18.2": {"name": "jiti", "version": "1.18.2", "license": "MIT", "_id": "jiti@1.18.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "80c3ef3d486ebf2450d9335122b32d121f2a83cd", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.18.2.tgz", "fileCount": 14, "integrity": "sha512-QAdOptna2NYiSSpv0O/BwoHBSmz4YhpzJHyi+fnMRTXFjp7B8i/YG5Z8IfusxB1ufjcD2Sre1F3R+nX3fvy7gg==", "signatures": [{"sig": "MEUCIDEgpdxWh6gkWAX+eVccqGqEfXBIw8WL6UlzHDr8MZoJAiEAjYh569x9cAkYJ8WAQwlX/v8ZwGdsTc44rW0xnw1luKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1948699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEeGsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpd5A//XqmTvtVsx6BAt3n/1r6qXIrAjmEfZm3T4ju9z9oytsNlpAGj\r\nwK8BAg2vtT1XUYPzMYer7Brh3qkVMtyMhtvxAN+rZU/zgK3pd81yyYz3/nDD\r\nP1L0ZrM1eKV5INzmLgk0gnva3l+fX9SiB7hO8VQGsoKMR1ol2ByKDPhCTOOS\r\nyjFmd67Ev7TOEC8neQuK0JhmLmq9wA1Yqg7g9E8rsJaaUxSww6v4z6oP6pjm\r\nXvKH4hY7w7wEB3olW6qezPA4aHsIs84tXngXZXd4owA9ZmSMLw0sGcKkP320\r\nKSw8Na5ByUWi8tHkBC+cRlC81pTPAhReZMgUKDI6+1Wri5r/ftXNvqVVwhmX\r\nHpfvNW1Zfw1MbMZ+F9MyRn80+knazeGDeGd+OHT8gsGUX/YOPwVe9DbkkP2d\r\nQorxTRvsx73NC081PoV6Zl1XC39JCDt5LqqeHydSQLX7whTL3wuBxcI0YCJ7\r\nHNKKEg5MbrnvBgQOtK0I6uz+3tpvMBR8N3ss3cia0UREt3ruLVZhK+p1GaQ8\r\nvDidQ+vKqquRTg5l98uhR7wYkaj4p08/DRNUQocjXHDdfZ4GYU4xlArnZ3yk\r\nKgdgAfM2impQnsFdcPRJcWzMRu+1OwzsOq0Ymeke1KPp/WZP/2GpZz9s7san\r\nVB6gOqEPUmEOIVhDf1QCR5sC3y7JGxtyqzg=\r\n=ASn6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "ce030f6fb98bbbb484c45f5a7fb1bf0745aa822b", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "cross-env JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && cross-env NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "jiti:legacy": "cross-env JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "packageManager": "pnpm@7.29.3", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.2.0", "vite": "^4.1.4", "acorn": "^8.8.2", "destr": "^1.2.2", "execa": "^7.1.1", "pathe": "^1.1.0", "tslib": "^2.5.0", "config": "^3.3.9", "eslint": "^8.36.0", "semver": "^7.3.8", "vitest": "^0.29.2", "pirates": "^4.0.5", "std-env": "^3.3.2", "webpack": "^5.76.1", "prettier": "^2.8.4", "cross-env": "^7.0.3", "fast-glob": "^3.2.12", "pkg-types": "^1.0.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "@babel/core": "^7.21.3", "@types/node": "^18.15.3", "changelogen": "^0.5.1", "object-hash": "^3.0.0", "webpack-cli": "^5.0.1", "@babel/types": "^7.21.3", "@types/semver": "^7.3.13", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.20.7", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.0", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.1.0", "@vitest/coverage-c8": "^0.29.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.7", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.21.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.20.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.21.2", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.18.2_1678893484300_0.2993525407626698", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "jiti", "version": "1.19.0", "license": "MIT", "_id": "jiti@1.19.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9bf45a47670f0f7d0c9c53325c8216f90353cd2f", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.19.0.tgz", "fileCount": 14, "integrity": "sha512-93OnYCvFp88SbkJstwqwGOxU1POpIqImAlGKZLAXuZRJrvfRtKUrcAwFaGuTcrlglkQXrV7QvHkLFizFWeRU5g==", "signatures": [{"sig": "MEYCIQDvpwXM1QpjXqBECqb2yF6t6elwlLvoikWJ3hvaA4q7IAIhAJ+BaQYOLYlX02fyi1vIuDsq01uHjtCur+bOzyOZC1Zz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1918881}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "adcde06eec80a7aa9df2b95f9a448953d2a7f5f6", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "deprecated": "please use jiti@1.19.1 or later", "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.6", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.4.0", "vite": "^4.3.9", "acorn": "^8.9.0", "destr": "^2.0.0", "execa": "^7.1.1", "pathe": "^1.1.1", "tslib": "^2.6.0", "config": "^3.3.9", "eslint": "^8.44.0", "semver": "^7.5.3", "vitest": "^0.32.4", "pirates": "^4.0.6", "std-env": "^3.3.3", "webpack": "^5.88.1", "prettier": "^2.8.8", "fast-glob": "^3.3.0", "pkg-types": "^1.0.3", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "@babel/core": "^7.22.6", "@types/node": "^20.3.3", "changelogen": "^0.5.4", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.22.5", "@types/semver": "^7.5.0", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.22.5", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.1", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.32.4", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.9", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.22.5", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.22.6", "@babel/plugin-transform-typescript": "^7.22.5", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.19.0_1688473284222_0.3719814179268235", "host": "s3://npm-registry-packages"}}, "1.19.1": {"name": "jiti", "version": "1.19.1", "license": "MIT", "_id": "jiti@1.19.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "fa99e4b76a23053e0e7cde098efe1704a14c16f1", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.19.1.tgz", "fileCount": 14, "integrity": "sha512-oVhqoRDaBXf7sjkll95LHVS6Myyyb1zaunVwk4Z0+WPSW4gjS0pl01zYKHScTuyEhQsFxV5L4DR5r+YqSyqyyg==", "signatures": [{"sig": "MEQCIG+j0X+94AbTTqmiHaJhktt8s9W91EF0XT0sS9ryOBdtAiBQKhTcBYpJ6xU9p0V2KePqSrZvtvDGDwH4qXD5AZCnrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1918888}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "79eeafb67b1768531010d41b59fcda3d46f8f730", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.6", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.4.0", "vite": "^4.3.9", "acorn": "^8.9.0", "destr": "^2.0.0", "execa": "^7.1.1", "pathe": "^1.1.1", "tslib": "^2.6.0", "config": "^3.3.9", "eslint": "^8.44.0", "semver": "^7.5.3", "vitest": "^0.32.4", "pirates": "^4.0.6", "std-env": "^3.3.3", "webpack": "^5.88.1", "prettier": "^2.8.8", "fast-glob": "^3.3.0", "pkg-types": "^1.0.3", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "@babel/core": "^7.22.6", "@types/node": "^20.3.3", "changelogen": "^0.5.4", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.22.5", "@types/semver": "^7.5.0", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.22.5", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.1", "@types/object-hash": "^3.0.2", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.32.4", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.9", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.22.5", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.22.6", "@babel/plugin-transform-typescript": "^7.22.5", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.19.1_1688478588583_0.5306607167510518", "host": "s3://npm-registry-packages"}}, "1.19.2": {"name": "jiti", "version": "1.19.2", "license": "MIT", "_id": "jiti@1.19.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "1bd97b4f40feb7810a14b820c2ceea62adc06cb5", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.19.2.tgz", "fileCount": 14, "integrity": "sha512-UgPQy6IN/a/5DRAVkNRgoyVQBC4BqjN/ALFKnTlCR6mJIBtEj0/X52QCMckszSNbY7RbSNa1PzhhxQzROiGHcA==", "signatures": [{"sig": "MEQCIFWiv55hTZH86aeARigVkxtqNIy2y9CBx+kgnxjDFpHIAiA5sVSmpINDWDomzCoS72dVxG9qJJ1qCWx0/9aShdkkKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1918236}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "035c879724f6bad821099dfe8aff4e9141703a3b", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.4.0", "vite": "^4.4.9", "acorn": "^8.10.0", "destr": "^2.0.1", "execa": "^7.2.0", "pathe": "^1.1.1", "tslib": "^2.6.1", "config": "^3.3.9", "eslint": "^8.47.0", "semver": "^7.5.4", "vitest": "^0.34.2", "pirates": "^4.0.6", "std-env": "^3.3.3", "webpack": "^5.88.2", "prettier": "^3.0.2", "fast-glob": "^3.3.1", "pkg-types": "^1.0.3", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "@babel/core": "^7.22.10", "@types/node": "^20.5.0", "changelogen": "^0.5.4", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.22.10", "@types/semver": "^7.5.0", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.22.5", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.1", "@types/object-hash": "^3.0.3", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.9", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.22.5", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.22.10", "@babel/plugin-transform-typescript": "^7.22.10", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.19.2_1692357279470_0.3931637834114208", "host": "s3://npm-registry-packages"}}, "1.19.3": {"name": "jiti", "version": "1.19.3", "license": "MIT", "_id": "jiti@1.19.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "ef554f76465b3c2b222dc077834a71f0d4a37569", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.19.3.tgz", "fileCount": 14, "integrity": "sha512-5eEbBDQT/jF1xg6l36P+mWGGoH9Spuy0PCdSr2dtWRDGC6ph/w9ZCL4lmESW8f8F7MwT3XKescfP0wnZWAKL9w==", "signatures": [{"sig": "MEQCIE+ZweO12NXvxTgGUdce5U3mRGUwWfA4glyeNmvXhWy1AiAsXdKzXfoH43dLaZjQ1VpIrgDMquetm/eAzTu9nOXCmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1918270}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "4ae58425027343b283a71a08cf964e4d7356c9fd", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.6.12", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.4.0", "vite": "^4.4.9", "acorn": "^8.10.0", "destr": "^2.0.1", "execa": "^7.2.0", "pathe": "^1.1.1", "tslib": "^2.6.1", "config": "^3.3.9", "eslint": "^8.47.0", "semver": "^7.5.4", "vitest": "^0.34.2", "pirates": "^4.0.6", "std-env": "^3.3.3", "webpack": "^5.88.2", "prettier": "^3.0.2", "fast-glob": "^3.3.1", "pkg-types": "^1.0.3", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "@babel/core": "^7.22.10", "@types/node": "^20.5.0", "changelogen": "^0.5.4", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.22.10", "@types/semver": "^7.5.0", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.22.5", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.1", "@types/object-hash": "^3.0.3", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.9", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.22.5", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.22.10", "@babel/plugin-transform-typescript": "^7.22.10", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.19.3_1692360071877_0.06302320475706646", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "jiti", "version": "1.20.0", "license": "MIT", "_id": "jiti@1.20.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "2d823b5852ee8963585c8dd8b7992ffc1ae83b42", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.20.0.tgz", "fileCount": 14, "integrity": "sha512-3TV69ZbrvV6U5DfQimop50jE9Dl6J8O1ja1dvBbMba/sZ3YBEQqJ2VZRoQPVnhlzjNtU1vaXRZVrVjU4qtm8yA==", "signatures": [{"sig": "MEQCIHXAJ++G4BqNKeo8uTV5Tx9wiiSGhNhdCZ7K04lGk9DxAiB/QGFeIiTqkVgimYofeVqx/mGh96VoqjJPgYMowzuNgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1899788}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "2f7ccf29c384d67a1bb131b7b599269dda77f2ca", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.7.4", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.4.2", "vite": "^4.4.9", "acorn": "^8.10.0", "destr": "^2.0.1", "execa": "^8.0.1", "pathe": "^1.1.1", "tslib": "^2.6.2", "config": "^3.3.9", "eslint": "^8.48.0", "semver": "^7.5.4", "vitest": "^0.34.3", "pirates": "^4.0.6", "std-env": "^3.4.3", "webpack": "^5.88.2", "prettier": "^3.0.3", "fast-glob": "^3.3.1", "pkg-types": "^1.0.3", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "@babel/core": "^7.22.15", "@types/node": "^20.5.9", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.22.15", "@types/semver": "^7.5.1", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.2", "create-require": "^1.1.1", "@babel/template": "^7.22.15", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.1", "@types/object-hash": "^3.0.4", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.3", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.9", "@types/babel__template": "^7.4.1", "@babel/preset-typescript": "^7.22.15", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.15", "@babel/plugin-transform-optional-chaining": "^7.21.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.18.9", "@babel/plugin-transform-nullish-coalescing-operator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.20.0_1694082524956_0.18578748470117912", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "jiti", "version": "1.21.0", "license": "MIT", "_id": "jiti@1.21.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "7c97f8fe045724e136a397f7340475244156105d", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.0.tgz", "fileCount": 14, "integrity": "sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==", "signatures": [{"sig": "MEQCIH8Zmp+KX72oGZBvtRfazTMLJcx5YuWR6xax4oNZ3zS7AiA4J+BahmRXMgTkdeuquNPQwgdUCICygUanbBXhW1khRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1913525}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "16d41590db10a5c8b941478f84be6e98b08b4f76", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint --ext .ts,.js . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix --ext .ts,.js . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.8.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.4.2", "vite": "^4.5.0", "acorn": "^8.11.2", "destr": "^2.0.2", "execa": "^8.0.1", "pathe": "^1.1.1", "tslib": "^2.6.2", "config": "^3.3.9", "eslint": "^8.52.0", "semver": "^7.5.4", "vitest": "^0.34.6", "pirates": "^4.0.6", "std-env": "^3.4.3", "webpack": "^5.89.0", "prettier": "^3.0.3", "fast-glob": "^3.3.1", "pkg-types": "^1.0.3", "ts-loader": "^9.5.0", "typescript": "^5.2.2", "@babel/core": "^7.23.2", "@types/node": "^20.8.9", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.23.0", "@types/semver": "^7.5.4", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.4", "create-require": "^1.1.1", "@babel/template": "^7.22.15", "reflect-metadata": "^0.1.13", "@types/babel__core": "^7.20.3", "@types/object-hash": "^3.0.5", "eslint-config-unjs": "^0.2.1", "@vitest/coverage-v8": "^0.34.6", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.9", "@types/babel__template": "^7.4.3", "@babel/preset-typescript": "^7.23.2", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-transform-typescript": "^7.22.15", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.23.0", "@babel/plugin-transform-optional-chaining": "^7.23.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.22.11", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.11"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.0_1698687215893_0.18731224621416098", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "jiti", "version": "1.21.1", "license": "MIT", "_id": "jiti@1.21.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "84639a2debaececa42fcb2a313b5b80716c894ab", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.1.tgz", "fileCount": 14, "integrity": "sha512-KMXpzEJMsOFyRj6ZpDTnnlJrdr9umUY+eut5vlRvjVixohitnRFIRTFw9MEu9zPlBxTHZo6xD5ftKYiQZuJYQw==", "signatures": [{"sig": "MEUCIBqNCqbT/pVOrVcbMi7HKO21rxRkFFSLz7jQ5Fgd9FTiAiEAhDv5UlR2J59QVsNT1yDHXE1ovKLKcy/Z/iQsHsl4Mlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1949031}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "9727cf0b5f655c2a2154d8062a77d566c7325f69", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.4", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.0", "vite": "^5.2.11", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.2", "config": "^3.3.11", "eslint": "^9.2.0", "semver": "^7.6.0", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.2.5", "fast-glob": "^3.3.2", "pkg-types": "^1.1.0", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.5", "@types/node": "^20.12.8", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.5", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.0", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-typescript": "^7.24.5", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-optional-chaining": "^7.24.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.1_1717546954868_0.9799424202303704", "host": "s3://npm-registry-packages"}}, "1.21.2": {"name": "jiti", "version": "1.21.2", "license": "MIT", "_id": "jiti@1.21.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "262ea386ac29b6d9e74ed0329ed6880845e1d25e", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.2.tgz", "fileCount": 14, "integrity": "sha512-U<PERSON><PERSON><PERSON>yi4HPsFoxcsJ0uV4qDVBDpPWVI7D0JOaeCUnhcEpjWFQIeSu8Ecpu2mAZS6ee3QGtFewR3UOp60YiveZQ==", "signatures": [{"sig": "MEQCIHLDyg5/DvD+6TR8EEgHlCmXvc0d24Pc6ijoLDHgQN+KAiBBzBU9lFu7vS8ww+GAF0rC5SCdcqf/JqYBtQbDYE4THA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1948248}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "9ca70a1d129f6a6af4d08f27faedbd40b5a4eb30", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "resolutions": {"mlly": "1.4.2"}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.4", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.0", "vite": "^5.2.11", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.2", "config": "^3.3.11", "eslint": "^9.2.0", "semver": "^7.6.0", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.2.5", "fast-glob": "^3.3.2", "pkg-types": "^1.1.0", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.5", "@types/node": "^20.12.8", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.5", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.0", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-typescript": "^7.24.5", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-optional-chaining": "^7.24.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.2_1717583555902_0.2303907850205864", "host": "s3://npm-registry-packages"}}, "1.21.3": {"name": "jiti", "version": "1.21.3", "license": "MIT", "_id": "jiti@1.21.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "b2adb07489d7629b344d59082bbedb8c21c5f755", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.3.tgz", "fileCount": 14, "integrity": "sha512-uy2bNX5zQ+tESe+TiC7ilGRz8AtRGmnJH55NC5S0nSUjvvvM2hJHmefHErugGXN4pNv4Qx7vLsnNw9qJ9mtIsw==", "signatures": [{"sig": "MEQCIEWCJxBMhn5V+kmXjaajJZPthGB12YS3dbPTGVd2lysdAiBQGE59d+HgSGfhpHVPINQe3/LgLnd3Ux2+VQX6QW1keQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1949115}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "c7872b20b768d94a21c9959ab4b2c7aa45279eb0", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.1.4", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.2.11", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.2", "config": "^3.3.11", "eslint": "^9.2.0", "semver": "^7.6.0", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.2.5", "fast-glob": "^3.3.2", "pkg-types": "^1.1.0", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.5", "@types/node": "^20.12.8", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.5", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.0", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-typescript": "^7.24.5", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/plugin-transform-optional-chaining": "^7.24.5", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.3_1717585841765_0.2716802687553339", "host": "s3://npm-registry-packages"}}, "1.21.4": {"name": "jiti", "version": "1.21.4", "license": "MIT", "_id": "jiti@1.21.4", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "69e9988f32054bfbaf6d60495eb3f9fce0662922", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.4.tgz", "fileCount": 14, "integrity": "sha512-DUP4hpE2Of384xS46IiRm1JK3r4Ix4ADQI5k65y4joiZF7Oeo2ySdYG7PbqioljFalu1ndzto0Sb5IfExYliYA==", "signatures": [{"sig": "MEYCIQCFq6xIHPPH19NUP5OngNzYE4kp3eHWfurb9NiZXoSUxwIhALlE6iGkr0KLv1ewUgtC6cnfTr1CZ7zj2nHEStgrsFPx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1947746}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "7faee9bb18d03f21c0a95d4133c1bbf50594ca73", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.2.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.2.12", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.3", "config": "^3.3.11", "eslint": "^9.4.0", "semver": "^7.6.2", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.7", "@types/node": "^20.14.2", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.7", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.4_1718026565817_0.7661058661250486", "host": "s3://npm-registry-packages"}}, "1.21.5": {"name": "jiti", "version": "1.21.5", "license": "MIT", "_id": "jiti@1.21.5", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "1b22e744691081f333ff9077773d1f3545b7e5b0", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.5.tgz", "fileCount": 14, "integrity": "sha512-JmvHYAZK3v0BifQ3fk+kOhuCeni40Ehqx1qdFJsYUeFZVL3kKeyWPRQ4NEY0rjklqgVZzLzqNHktzQRirst15Q==", "signatures": [{"sig": "MEUCIQDPHtwdXz7KBHrNge2jJ9CBwDIDQEovBRYKZGm3EnOn6QIgeop9NcSDf3mQ+9G2c1nElwV1DZymuKEBmmk6omcQ60g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1947717}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "6ea0aa388a6a7016a0f31288cc1b04729d7848db", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.2.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.2.12", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.3", "config": "^3.3.11", "eslint": "^9.4.0", "semver": "^7.6.2", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.7", "@types/node": "^20.14.2", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.7", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.5_1718030023381_0.0692330930455447", "host": "s3://npm-registry-packages"}}, "1.21.6": {"name": "jiti", "version": "1.21.6", "license": "MIT", "_id": "jiti@1.21.6", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "6c7f7398dd4b3142767f9a168af2f317a428d268", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.6.tgz", "fileCount": 14, "integrity": "sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==", "signatures": [{"sig": "MEQCIET4hMIEcoMI9NJu0oISsexfGvUL95m0kmK0z6fACNigAiBxzEZk87vHkSGaocM1Gl55C3P8anusAWiA41VgUfl54w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1947802}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "c091661aff55784bbe694cb65652637c3a9f9988", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.2.0", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.2.12", "acorn": "^8.11.3", "destr": "^2.0.3", "execa": "^9.1.0", "pathe": "^1.1.2", "tslib": "^2.6.3", "config": "^3.3.11", "eslint": "^9.4.0", "semver": "^7.6.2", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.91.0", "prettier": "^3.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "@babel/core": "^7.24.7", "@types/node": "^20.14.2", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.24.7", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.6_1718031630994_0.3925504902551509", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "jiti", "version": "2.0.0-beta.1", "license": "MIT", "_id": "jiti@2.0.0-beta.1", "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "f0f3577227e7452bbfece7026e3d9e676cc96dcd", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.0.0-beta.1.tgz", "fileCount": 24, "integrity": "sha512-HcvE7lgB3gSkV40gbxMhDmd1oGs47+0yXZD5o8McaYT54dT9Bf2dbwRlzGxlkINX9dVlLqn5PkQwHgWI6oii/w==", "signatures": [{"sig": "MEUCIQDYQ3Sb9o4R+xToaHkD6lwi5Oxveb3YfDmn2dfK+aFRAQIgFXIZsjCMRd5wNTtk4aQlN5bwVaSFbzoWs5OCGilSJrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1904804}, "main": "./lib/index.js", "types": "./dist/jiti.d.ts", "exports": {".": {"types": "./dist/jiti.d.ts", "default": "./lib/index.js"}}, "gitHead": "863c7ea86e563a438f7c568b518e9502105bdb57", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.4.0", "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.1", "vite": "^5.3.1", "acorn": "^8.12.0", "destr": "^2.0.3", "execa": "^9.3.0", "pathe": "^1.1.2", "tslib": "^2.6.3", "config": "^3.3.12", "eslint": "^9.5.0", "semver": "^7.6.2", "vitest": "^1.6.0", "pirates": "^4.0.6", "std-env": "^3.7.0", "webpack": "^5.92.1", "prettier": "^3.3.2", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.5.2", "@babel/core": "^7.24.7", "@types/node": "^20.14.9", "changelogen": "^0.5.5", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "webpack-bundle-analyzer": "^4.10.2", "@babel/preset-typescript": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.0.0-beta.1_1719587897574_0.3293016482090756", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "jiti", "version": "2.0.0-beta.2", "license": "MIT", "_id": "jiti@2.0.0-beta.2", "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "5316b4adaa6f2e7ced330ab475bc6f35cfe51aaa", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-c+<PERSON><PERSON><PERSON><PERSON>akiQuMKbnhvrjZUvrK6E/AfmTOf4P+E3Y4FNVHcNMX9e/XrnbEvO+m4wS6ZjsvhHh/POQTlfy8uXFc0A==", "signatures": [{"sig": "MEYCIQCmem3o9/4/gMwnz9zDJDX3tidGRSAxFL8c3IpgxBU9swIhAJDetMMydUn+bLqCYZ0+gKd88OEMdZNKamHiFL67034B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1895302}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.mts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "4490a8bd038f676987b3de521a220b398a56b4c4", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 ./lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:register && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:register": "node ./test/register-test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.4.0", "readmeFilename": "README.md", "devDependencies": {"mlly": "^1.7.1", "acorn": "^8.12.0", "destr": "^2.0.3", "execa": "^9.3.0", "pathe": "^1.1.2", "config": "^3.3.12", "eslint": "^9.5.0", "vitest": "^1.6.0", "std-env": "^3.7.0", "webpack": "^5.92.1", "prettier": "^3.3.2", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.5.2", "@babel/core": "^7.24.7", "@types/node": "^20.14.9", "changelogen": "^0.5.5", "webpack-cli": "^5.1.4", "@babel/types": "^7.24.7", "estree-walker": "^3.0.3", "create-require": "^1.1.1", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "webpack-license-plugin": "^4.4.2", "webpack-bundle-analyzer": "^4.10.2", "@babel/preset-typescript": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.0.0-beta.2_1719863332536_0.7420122525103274", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "jiti", "version": "2.0.0-beta.3", "license": "MIT", "_id": "jiti@2.0.0-beta.3", "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "8b97dcb268a7409c3fbda9242453486564a11c36", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-pmfRbVRs/7khFrSAYnSiJ8C0D5GvzkE4Ey2pAvUcJsw1ly/p+7ut27jbJrjY79BpAJQJ4gXYFtK6d1Aub+9baQ==", "signatures": [{"sig": "MEQCICfO3YX6vJUWsAghhhqk+GozMIBwaZqyoLJWvNUhZnAOAiBoh7+Mp4rqiACI0OBt1CnJmNdiPJJfb7/oWbw8KIasAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1897312}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.mts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "fdd6f8a407745cadab0b394da456b6a81595efd3", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 ./lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:register && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:register": "node ./test/register-test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.4.0", "readmeFilename": "README.md", "devDependencies": {"mlly": "^1.7.1", "acorn": "^8.12.0", "destr": "^2.0.3", "execa": "^9.3.0", "pathe": "^1.1.2", "config": "^3.3.12", "eslint": "^9.5.0", "vitest": "^1.6.0", "std-env": "^3.7.0", "webpack": "^5.92.1", "prettier": "^3.3.2", "fast-glob": "^3.3.2", "pkg-types": "^1.1.1", "ts-loader": "^9.5.1", "typescript": "^5.5.2", "@babel/core": "^7.24.7", "@types/node": "^20.14.9", "changelogen": "^0.5.5", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.0", "@babel/types": "^7.24.7", "estree-walker": "^3.0.3", "@babel/template": "^7.24.7", "reflect-metadata": "^0.2.1", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^1.6.0", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "webpack-license-plugin": "^4.4.2", "webpack-bundle-analyzer": "^4.10.2", "@babel/preset-typescript": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.7", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-transforms": "^7.24.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-typescript": "^7.24.7", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.24.7", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.0.0-beta.3_1719917648178_0.9708220284228433", "host": "s3://npm-registry-packages"}}, "2.0.0-rc.1": {"name": "jiti", "version": "2.0.0-rc.1", "license": "MIT", "_id": "jiti@2.0.0-rc.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "8f39e916abd5644ccf17e95fd41e132f4e5ac8a3", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.0.0-rc.1.tgz", "fileCount": 15, "integrity": "sha512-40BOLe5MVHVgtzjIB52uBqRxTCR07Ziecxx/LVmqRDV14TJaruFX6kKgS9iYhATGSUs04x3S19Kc8ErUKshMhA==", "signatures": [{"sig": "MEQCIB2WGDhN7I8FmM+POq/7/pgtiEaqdjob+alofRv24I3NAiBOhK0aKePXFqAshj9Cqy8SHscNGx5SCYfhm3OEVKyqjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1918206}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "fc98088e9a3b02707288d6302087cfe5fa5969fb", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --prerelease --push --publish --publishTag 2x", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.10.0", "readmeFilename": "README.md", "devDependencies": {"mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "config": "^3.3.12", "eslint": "^9.10.0", "vitest": "^2.1.1", "std-env": "^3.7.0", "webpack": "^5.94.0", "prettier": "^3.3.3", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.2", "@types/node": "^22.5.5", "changelogen": "^0.5.5", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.6", "estree-walker": "^3.0.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.6", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^2.1.1", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "webpack-bundle-analyzer": "^4.10.2", "@babel/preset-typescript": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-module-transforms": "^7.25.2", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.2", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.6", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.0.0-rc.1_1726745189777_0.9573466979187801", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "jiti", "version": "2.0.0", "license": "MIT", "_id": "jiti@2.0.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "ccaab6ce73a73cbf04e187645c614b3a3d41b653", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.0.0.tgz", "fileCount": 15, "integrity": "sha512-CJ7e7Abb779OTRv3lomfp7Mns/Sy1+U4pcAx5VbjxCZD5ZM/VJaXPpPjNKjtSvWQy/H86E49REXR34dl1JEz9w==", "signatures": [{"sig": "MEYCIQDfeWqrsMwz4KRBsj8yffPMBQ80Z/cm0/mYfvK7DW4m9QIhALv+NQIJmMJLYHg06ypaWPPK2/v/DyipS92OJ2QLHPN5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933642}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "fca09ee60ea0128adab135e7171d0e8dbed03baa", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.8", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.11.0", "preact": "^10.24.1", "vitest": "^2.1.1", "std-env": "^3.7.0", "webpack": "^5.94.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.2", "@types/node": "^22.5.5", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.6", "estree-walker": "^3.0.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.6", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.3.2", "@vitest/coverage-v8": "^2.1.1", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-module-transforms": "^7.25.2", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.2", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.6", "@babel/plugin-transform-export-namespace-from": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.0.0_1727288513813_0.7590541001612354", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "jiti", "version": "2.1.0", "license": "MIT", "_id": "jiti@2.1.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "54bdaa411f543554aed9bf3458351bbc1fa5acab", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.1.0.tgz", "fileCount": 15, "integrity": "sha512-Nftp80J8poC3u+93ZxpjstsgfQ5d0o5qyD6yStv32sgnWr74xRxBppEwsUoA/GIdrJpgGRkC1930YkLcAsFdSw==", "signatures": [{"sig": "MEUCIDgwVfiJ5BrI+LfbodIUrvrFZg2i22e+2vKbYbLPC2UFAiEA5PgzWSVQ8c2jxNDRcQCfJJPxYM+TdUz1hEKsbWogWWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2084017}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "f99a1d7df0064e201df760e17470cc34bb330618", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.10", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.11.0", "preact": "^10.24.1", "vitest": "^2.1.1", "std-env": "^3.7.0", "webpack": "^5.94.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.2", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.6", "estree-walker": "^3.0.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.6", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.1", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-module-transforms": "^7.25.2", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.2", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.6", "@babel/plugin-transform-export-namespace-from": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.1.0_1727818782288_0.6658355511138001", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "jiti", "version": "2.1.1", "license": "MIT", "_id": "jiti@2.1.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "4c2410723e6f29c835b266783ea312ac0b5da54a", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.1.1.tgz", "fileCount": 15, "integrity": "sha512-1BRk+NppnvjWLfEqPQtDc3JTs2eiXY9cKBM+VOk5WO+uwWHIuLeWEo3Y1LTqjguKiK9KcLDYA3IdP7gWqcbRig==", "signatures": [{"sig": "MEUCIDxKnCf+s03z+ok/IXUYDwCOXtOgbm9qvwe0rOO5iIzZAiEAniG4Jf2ZaXKMS66XWMlUWx6X9daHEoheQLIL6Ql2Rd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3464036}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "420787447bc3aa1c4da0d590ed82323cf1e49bed", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.10", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.11.1", "preact": "^10.24.1", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.1.1_1727941776484_0.9059349550199787", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "jiti", "version": "2.1.2", "license": "MIT", "_id": "jiti@2.1.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "2a37dbb3897d24767dc37bc29693a1033d368040", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.1.2.tgz", "fileCount": 15, "integrity": "sha512-cYNjJus5X9J4jLzTaI8rYoIq1k6YySiA1lK4wxSnOrBRXkbVyreZfhoboJhsUmwgU82lpPjj1IoU7Ggrau8r3g==", "signatures": [{"sig": "MEUCIA+JSoPwEhQQMaimd+GFNMBTJrRJ75nPJ6Q6dUzpvZNDAiEAwRhPA5VEv9xOb4WfrzSs63EnYD7lXUpmr6L5D5QRT60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3464044}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "103d165092f7d13e9243977a3f8edf9ec6e66b41", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.10", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.11.1", "preact": "^10.24.1", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.1.2_1727966097341_0.3897291492353765", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "jiti", "version": "2.2.0", "license": "MIT", "_id": "jiti@2.2.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "1b5b8934cdf8290920dc5f8cc6b836294ddc72a2", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.2.0.tgz", "fileCount": 15, "integrity": "sha512-XI3aImuxq2Qt9upi/5cWxVT7aP0FUmrT5jg9LAhl87tqQsJ7WRqePfI3SBktCRB6YiaHFl1R60PZ9s03nV3fSg==", "signatures": [{"sig": "MEUCIQCF2fsvnp+BnlnZsfuNCGjeDk+3anS2+1ntzOenCzaZrQIgVnu8CmgI4HTYRSNpnhFNCLXXd5pa7i9fNZ0KboOK2UE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112535}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "50ac5d9874f3c6d6198becb1e9ba00d422e5004f", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.10", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.11.1", "preact": "^10.24.1", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.2.0_1728060420117_0.7296113715924781", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "jiti", "version": "2.2.1", "license": "MIT", "_id": "jiti@2.2.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "0c968cd6c9b4baeb4074abd2f2f2dc42f6537a09", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.2.1.tgz", "fileCount": 15, "integrity": "sha512-weIl/Bv3G0J3UKamLxEA2G+FfQ33Z1ZkQJGPjKFV21zQdKWu2Pi6o4elpj2uEl5XdFJZ9xzn1fsanWTFSt45zw==", "signatures": [{"sig": "MEYCIQDSTQf/EZtFa3WJ0C/TDpwEni08WQGqkyc8qIScaKWH4QIhAN1Ru5AynUkKcuEnJvVW1+BA24PCVYDAZfDlfVuyG7qq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2112535}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "94b172b9794bbf16ea774b8d82b8016384bb327a", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.10", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.11.1", "preact": "^10.24.1", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.2.1_1728062131402_0.846769461773522", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "jiti", "version": "2.3.0", "license": "MIT", "_id": "jiti@2.3.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "cf2c0cd6337be162bd5ae14dff8c653c4833e5a3", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.3.0.tgz", "fileCount": 15, "integrity": "sha512-sxPw05kUBzmPclmPVH12lMDiUPUWVPQwZdULPxKljb9GDqAu9qHc/5jF2v+I4j7Sd8UQ7yehHmlReBBmdhEE9g==", "signatures": [{"sig": "MEUCIGB8k4UnkoFjey3cauDaHSBHwkw1XxkCXiI9hzeoGcvhAiEAoLk8W1WqCOAxpN9xROEMRipenmiAtg9ewus2pmlR7tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2113072}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "a39bd7eb1687059b57c169c3f5001f9d921f4a18", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.11", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.12.0", "preact": "^10.24.2", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.3.0_1728125082573_0.7379108960028755", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "jiti", "version": "2.3.1", "license": "MIT", "_id": "jiti@2.3.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "b81ff051075117768dd67829f47f5035eeceb5c7", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.3.1.tgz", "fileCount": 15, "integrity": "sha512-xPZ6pPzUifI8XDBBxIL4OB1w1ZKmBpmNEeKwNt2d0Spn8XisAIZhWrlOHq5seBrFGTxVx9PbrWvEMyrk4IO5bA==", "signatures": [{"sig": "MEQCIC47ST2BuFYhVGa//IC60B4xJ31TG8BYsHWmp9pfeXy6AiBacLPmRYae8+6EUYucMeaKYSRrHwpW2QoUv4tyFgDknA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2113181}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "c4697486207fcbcb390d172da619b7c1a7bac6a2", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.11.0", "devDependencies": {"vue": "^3.5.11", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.1", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.12.0", "preact": "^10.24.2", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.45", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.3.1_1728126315409_0.15792466821452233", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "jiti", "version": "2.3.2", "license": "MIT", "_id": "jiti@2.3.2", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "ea3620d59372cf4829458fa4b921b54255435819", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.3.2.tgz", "fileCount": 15, "integrity": "sha512-9<PERSON><PERSON>e7kYLCyWTAP6EIyg3B4ZuHy5W0gdy6y1rgrWrAOpTrUU+vKuKa1+OXB7MBkujyvm6a2b7i0ETHQDbgY98A==", "signatures": [{"sig": "MEUCIQCCa/yKbtMZEyR43AiZYjqMHj4Bwg8/R9DzKQkY1IL7KgIgBM3rwruLHDWxrifUDTveTKolAguiTD3jEmSxY2iE1Lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2114372}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "c1a24e0d9f4bc077c70e1ec72f9424b6b4e3b195", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.12.0", "devDependencies": {"vue": "^3.5.11", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.2", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.12.0", "preact": "^10.24.2", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.46", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.3.2_1728301505899_0.713551000155525", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "jiti", "version": "2.3.3", "license": "MIT", "_id": "jiti@2.3.3", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "39c66fc77476b92a694e65dfe04b294070e2e096", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.3.3.tgz", "fileCount": 15, "integrity": "sha512-EX4oNDwcXSivPrw2qKH2LB5PoFxEvgtv2JgwW0bU858HoLQ+kutSvjLMUqBd0PeJYEinLWhoI9Ol0eYMqj/wNQ==", "signatures": [{"sig": "MEUCIQCpzTfxTdGvjPn7gC/VLYIlUzqDnr/hbH+Gvh1dYcdXVQIgEVDm7HK9E9eOd4VMStUhtXPrpVQUjr1cZkA+ePBU5iE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2114372}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "544247421cce0349d58dc94d58e862a7bc12176a", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.8.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.12.0", "devDependencies": {"vue": "^3.5.11", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.2", "acorn": "^8.12.1", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.12.0", "preact": "^10.24.2", "vitest": "^2.1.2", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.95.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.1", "tinyexec": "^0.3.0", "fast-glob": "^3.3.2", "pkg-types": "^1.2.0", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.2", "@babel/core": "^7.25.7", "@types/node": "^22.7.4", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.25.7", "estree-walker": "^3.0.3", "@babel/template": "^7.25.7", "@babel/traverse": "^7.25.7", "moment-timezone": "^0.5.46", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.2", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/preset-typescript": "^7.25.7", "@babel/helper-plugin-utils": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-module-transforms": "^7.25.7", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.7", "@babel/plugin-transform-react-jsx": "^7.25.7", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.7", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.25.7", "@babel/plugin-transform-export-namespace-from": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.3.3_1728321214260_0.34152779070578343", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "jiti", "version": "2.4.0", "license": "MIT", "_id": "jiti@2.4.0", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "393d595fb6031a11d11171b5e4fc0b989ba3e053", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.4.0.tgz", "fileCount": 15, "integrity": "sha512-H5UpaUI+aHOqZXlYOaFP/8AzKsg+guWu+Pr3Y8i7+Y3zr1aXAvCvTAQ1RxSc6oVD8R8c7brgNtTVP91E7upH/g==", "signatures": [{"sig": "MEYCIQDZi3fTgdh1qCTN5L/VUZgSUl4CrrHdR2aPASc4AXoxEwIhAOP9x6oG3m2wg9vn3VFlywRGVoXjS1Gh2FRNd+p/ge2J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2130508}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "2f9c2376e70d951cdfbc06eb8ed046331deb177a", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.9.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.12.3", "devDependencies": {"vue": "^3.5.12", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.2", "acorn": "^8.14.0", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.13.0", "preact": "^10.24.3", "vitest": "^2.1.4", "consola": "^3.2.3", "std-env": "^3.7.0", "webpack": "^5.96.0", "nano-jsx": "^0.1.0", "prettier": "^3.3.3", "solid-js": "^1.9.3", "tinyexec": "^0.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.2.1", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.6.3", "@babel/core": "^7.26.0", "@types/node": "^22.8.6", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.26.0", "estree-walker": "^3.0.3", "@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "moment-timezone": "^0.5.46", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.1", "@vitest/coverage-v8": "^2.1.4", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.9", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-transform-export-namespace-from": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.4.0_1730459307933_0.028450881270881245", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "jiti", "version": "2.4.1", "license": "MIT", "_id": "jiti@2.4.1", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "dist": {"shasum": "4de9766ccbfa941d9b6390d2b159a4b295a52e6b", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.4.1.tgz", "fileCount": 15, "integrity": "sha512-yPBThwecp1wS9DmoA4x4KR2h3QoslacnDR8ypuFM962kI4/456Iy1oHx2RAgh4jfZNdn0bctsdadceiBUgpU1g==", "signatures": [{"sig": "MEQCIAL0/sWcenlsEQ560EGnViGz3agmWur9pxP3JdOnYr95AiAnjQKCA6hZnegjeo1JxVOkuiVXaFSARETt/U/aNJjxPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2130665}, "main": "./lib/jiti.cjs", "type": "module", "types": "./lib/jiti.d.cts", "module": "./lib/jiti.mjs", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./package.json": "./package.json"}, "gitHead": "ad6191f04624badf2112651217bc17008a9bde50", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "test:types": "tsc --noEmit", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.11.0", "typesVersions": {"*": {"native": ["./lib/jiti.d.mts"], "register": ["./lib/jiti-register.d.mts"]}}, "_hasShrinkwrap": false, "packageManager": "pnpm@9.14.3", "devDependencies": {"vue": "^3.5.13", "zod": "^3.23.8", "defu": "^6.1.4", "etag": "^1.8.1", "mime": "^4.0.4", "mlly": "^1.7.3", "acorn": "^8.14.0", "destr": "^2.0.3", "pathe": "^1.1.2", "react": "^18.3.1", "config": "^3.3.12", "eslint": "^9.15.0", "preact": "^10.25.0", "vitest": "^2.1.6", "consola": "^3.2.3", "std-env": "^3.8.0", "webpack": "^5.96.1", "nano-jsx": "^0.1.0", "prettier": "^3.4.1", "solid-js": "^1.9.3", "tinyexec": "^0.3.1", "fast-glob": "^3.3.2", "pkg-types": "^1.2.1", "react-dom": "^18.3.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "@babel/core": "^7.26.0", "@types/node": "^22.10.1", "changelogen": "^0.5.7", "webpack-cli": "^5.1.4", "yoctocolors": "^2.1.1", "@babel/types": "^7.26.0", "estree-walker": "^3.0.3", "@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "moment-timezone": "^0.5.46", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^2.1.6", "escape-string-regexp": "^5.0.0", "is-installed-globally": "^1.0.0", "terser-webpack-plugin": "^5.3.10", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "webpack-license-plugin": "^4.5.0", "preact-render-to-string": "^6.5.11", "webpack-bundle-analyzer": "^4.10.2", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@types/babel__helper-plugin-utils": "^7.10.3", "@babel/plugin-transform-typescript": "^7.25.9", "@types/babel__helper-module-imports": "^7.18.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-transform-export-namespace-from": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_2.4.1_1732875321306_0.41359100248920866", "host": "s3://npm-registry-packages"}}, "1.21.7": {"name": "jiti", "version": "1.21.7", "license": "MIT", "_id": "jiti@1.21.7", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "homepage": "https://github.com/unjs/jiti#readme", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "bin": {"jiti": "bin/jiti.js"}, "dist": {"shasum": "9dd81043424a3d28458b193d965f0d18a2300ba9", "tarball": "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz", "fileCount": 14, "integrity": "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A==", "signatures": [{"sig": "MEUCIQCUn1w9Q5AVb/1eB028gQ6EKWqX6gZqF+IPm/zzSgsuzwIgegG6eKGXdvt8vSmDc1GHUnBYhl7aCbIXuzuO9bx7uo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2003102}, "main": "./lib/index.js", "types": "dist/jiti.d.ts", "gitHead": "e420d48300a1d23a5468e5ea9239e2c5b96413ab", "scripts": {"dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_CACHE=false JITI_REQUIRE_CACHE=false ./bin/jiti.js", "lint": "eslint . && prettier -c src lib test stubs", "test": "pnpm lint && vitest run --coverage && pnpm test:bun", "build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "release": "pnpm build && pnpm test && changelogen --release --push && npm publish --tag 1x", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "test:bun": "bun --bun test test/bun", "jiti:legacy": "JITI_DEBUG=1 npx node@12 ./bin/jiti.js"}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/unjs/jiti.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Runtime typescript and ESM support for Node.js", "directories": {}, "_nodeVersion": "22.12.0", "_hasShrinkwrap": false, "packageManager": "pnpm@9.15.0", "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "mlly": "^1.7.3", "vite": "^6.0.3", "acorn": "^8.14.0", "destr": "^2.0.3", "execa": "^9.5.2", "pathe": "^1.1.2", "tslib": "^2.8.1", "config": "^3.3.12", "eslint": "^9.17.0", "semver": "^7.6.3", "vitest": "^2.1.8", "pirates": "^4.0.6", "std-env": "^3.8.0", "webpack": "^5.97.1", "prettier": "^3.4.2", "fast-glob": "^3.3.2", "pkg-types": "^1.2.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "@babel/core": "^7.26.0", "@types/node": "^22.10.2", "changelogen": "^0.5.7", "object-hash": "^3.0.0", "webpack-cli": "^5.1.4", "@babel/types": "^7.26.3", "@types/semver": "^7.5.8", "estree-walker": "^3.0.3", "@types/resolve": "^1.20.6", "create-require": "^1.1.1", "@babel/template": "^7.25.9", "reflect-metadata": "^0.2.2", "@types/babel__core": "^7.20.5", "@types/object-hash": "^3.0.6", "eslint-config-unjs": "^0.4.2", "@vitest/coverage-v8": "^2.1.8", "escape-string-regexp": "^5.0.0", "terser-webpack-plugin": "^5.3.11", "@types/babel__template": "^7.4.4", "@babel/preset-typescript": "^7.26.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-parameter-decorator": "^1.0.16", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-typescript": "^7.26.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-optional-chaining": "^7.25.9", "babel-plugin-transform-typescript-metadata": "^0.3.2", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-nullish-coalescing-operator": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/jiti_1.21.7_1734470449003_0.7864185939977761", "host": "s3://npm-registry-packages-npm-production"}}, "2.4.2": {"name": "jiti", "version": "2.4.2", "description": "Runtime typescript and ESM support for Node.js", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "lib/jiti-cli.mjs"}, "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-typescript": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/template": "^7.25.9", "@babel/traverse": "^7.26.4", "@babel/types": "^7.26.3", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "acorn": "^8.14.0", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "is-installed-globally": "^1.0.0", "mime": "^4.0.4", "mlly": "^1.7.3", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.1", "preact": "^10.25.2", "preact-render-to-string": "^6.5.12", "prettier": "^3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.3", "std-env": "^3.8.0", "terser-webpack-plugin": "^5.3.11", "tinyexec": "^0.3.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "vue": "^3.5.13", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.24.1"}, "packageManager": "pnpm@9.15.0", "_id": "jiti@2.4.2", "gitHead": "340e2a733c35df66c85667ef254eab672f5de210", "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "homepage": "https://github.com/unjs/jiti#readme", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==", "shasum": "d19b7732ebb6116b06e2038da74a55366faef560", "tarball": "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz", "fileCount": 15, "unpackedSize": 2131809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1Ji6WPArFC4SY3xSjRXrcDhXeLnuFIGfxphcjM7BzAQIhAPfFXfY+aeUV17O63vVSgmkx7QqwRKXz1beF61NVhjfr"}]}, "_npmUser": {"name": "pi0", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jiti_2.4.2_1734470591808_0.9662416788350368"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-06-06T19:19:56.555Z", "modified": "2024-12-17T21:23:12.246Z", "0.0.0": "2020-06-06T19:19:56.653Z", "0.1.0": "2020-06-06T23:34:55.098Z", "0.1.1": "2020-06-06T23:42:28.708Z", "0.1.2": "2020-06-07T17:03:46.516Z", "0.1.3": "2020-06-07T17:31:46.198Z", "0.1.4": "2020-06-11T14:13:20.219Z", "0.1.5": "2020-06-11T19:47:26.889Z", "0.1.6": "2020-06-11T21:12:51.352Z", "0.1.7": "2020-06-11T21:14:44.214Z", "0.1.8": "2020-06-12T12:24:28.396Z", "0.1.9": "2020-06-12T17:56:56.036Z", "0.1.10": "2020-06-19T14:52:56.336Z", "0.1.11": "2020-06-19T14:56:49.584Z", "0.1.12": "2020-11-01T21:17:40.615Z", "0.1.13": "2020-11-21T21:07:57.545Z", "0.1.14": "2020-11-21T21:17:03.071Z", "0.1.15": "2020-11-22T02:02:16.796Z", "0.1.16": "2020-11-23T11:10:17.603Z", "0.1.17": "2020-11-27T22:43:03.519Z", "0.1.18": "2020-12-22T19:13:49.360Z", "0.1.19": "2020-12-30T11:10:57.523Z", "0.1.20": "2021-01-12T15:46:21.565Z", "1.0.0": "2021-01-12T15:51:17.941Z", "1.1.0": "2021-01-13T11:42:09.931Z", "1.2.0": "2021-01-14T12:40:16.451Z", "1.2.1": "2021-01-20T12:08:39.483Z", "1.3.0": "2021-01-21T10:07:15.093Z", "1.4.0": "2021-03-01T10:33:12.185Z", "1.5.0": "2021-03-03T19:41:10.019Z", "1.6.0": "2021-03-03T20:43:20.915Z", "1.6.1": "2021-03-05T12:20:08.619Z", "1.6.2": "2021-03-05T16:12:05.257Z", "1.6.3": "2021-03-06T20:39:03.730Z", "1.6.4": "2021-03-11T20:18:42.948Z", "1.7.0": "2021-04-09T08:23:07.576Z", "1.8.0": "2021-04-09T09:30:38.487Z", "1.9.0": "2021-04-09T10:32:19.955Z", "1.9.1": "2021-04-09T10:39:53.309Z", "1.9.2": "2021-05-11T09:42:39.052Z", "1.10.0": "2021-05-28T10:05:46.032Z", "1.10.1": "2021-05-28T11:07:59.764Z", "1.11.0": "2021-07-26T11:23:22.576Z", "1.12.0": "2021-09-13T10:43:02.982Z", "1.12.1": "2021-09-21T16:37:42.015Z", "1.12.2": "2021-09-21T18:00:54.139Z", "1.12.3": "2021-09-21T18:20:24.077Z", "1.12.4": "2021-09-29T14:05:25.336Z", "1.12.5": "2021-09-29T14:50:10.881Z", "1.12.6": "2021-10-02T13:20:55.164Z", "1.12.7": "2021-10-12T12:54:19.180Z", "1.12.8": "2021-10-18T12:51:50.022Z", "1.12.9": "2021-10-18T13:36:27.856Z", "1.12.10": "2022-01-25T14:50:33.462Z", "1.12.11": "2022-01-25T15:35:02.038Z", "1.12.12": "2022-01-25T15:50:07.716Z", "1.12.13": "2022-01-25T16:04:01.922Z", "1.12.14": "2022-01-26T11:47:57.442Z", "1.12.15": "2022-01-28T12:07:04.238Z", "1.13.0": "2022-02-18T17:22:09.893Z", "1.14.0": "2022-06-20T12:58:45.607Z", "1.15.0": "2022-09-06T09:48:24.140Z", "1.16.0": "2022-09-19T09:54:49.953Z", "1.16.1": "2023-01-03T13:04:02.350Z", "1.16.2": "2023-01-10T11:10:23.241Z", "1.17.0": "2023-02-08T21:43:01.379Z", "1.17.1": "2023-02-17T00:25:27.505Z", "1.17.2": "2023-03-06T19:24:54.176Z", "1.18.0": "2023-03-15T15:14:28.750Z", "1.18.1": "2023-03-15T15:16:21.554Z", "1.18.2": "2023-03-15T15:18:04.541Z", "1.19.0": "2023-07-04T12:21:24.461Z", "1.19.1": "2023-07-04T13:49:48.769Z", "1.19.2": "2023-08-18T11:14:39.747Z", "1.19.3": "2023-08-18T12:01:12.332Z", "1.20.0": "2023-09-07T10:28:45.294Z", "1.21.0": "2023-10-30T17:33:36.397Z", "1.21.1": "2024-06-05T00:22:35.129Z", "1.21.2": "2024-06-05T10:32:36.101Z", "1.21.3": "2024-06-05T11:10:41.961Z", "1.21.4": "2024-06-10T13:36:06.075Z", "1.21.5": "2024-06-10T14:33:43.642Z", "1.21.6": "2024-06-10T15:00:31.192Z", "2.0.0-beta.1": "2024-06-28T15:18:17.795Z", "2.0.0-beta.2": "2024-07-01T19:48:52.765Z", "2.0.0-beta.3": "2024-07-02T10:54:08.386Z", "2.0.0-rc.1": "2024-09-19T11:26:30.030Z", "2.0.0": "2024-09-25T18:21:54.238Z", "2.1.0": "2024-10-01T21:39:42.589Z", "2.1.1": "2024-10-03T07:49:36.774Z", "2.1.2": "2024-10-03T14:34:57.620Z", "2.2.0": "2024-10-04T16:47:05.676Z", "2.2.1": "2024-10-04T17:15:31.745Z", "2.3.0": "2024-10-05T10:44:42.843Z", "2.3.1": "2024-10-05T11:05:15.695Z", "2.3.2": "2024-10-07T11:45:06.120Z", "2.3.3": "2024-10-07T17:13:34.652Z", "2.4.0": "2024-11-01T11:08:28.244Z", "2.4.1": "2024-11-29T10:15:21.499Z", "1.21.7": "2024-12-17T21:20:49.254Z", "2.4.2": "2024-12-17T21:23:12.058Z"}, "bugs": {"url": "https://github.com/unjs/jiti/issues"}, "license": "MIT", "homepage": "https://github.com/unjs/jiti#readme", "repository": {"type": "git", "url": "git+https://github.com/unjs/jiti.git"}, "description": "Runtime typescript and ESM support for Node.js", "maintainers": [{"name": "pi0", "email": "<EMAIL>"}], "readme": "# jiti\n\n<!-- automd:badges color=F0DB4F bundlephobia -->\n\n[![npm version](https://img.shields.io/npm/v/jiti?color=F0DB4F)](https://npmjs.com/package/jiti)\n[![npm downloads](https://img.shields.io/npm/dm/jiti?color=F0DB4F)](https://npmjs.com/package/jiti)\n[![bundle size](https://img.shields.io/bundlephobia/minzip/jiti?color=F0DB4F)](https://bundlephobia.com/package/jiti)\n\n<!-- /automd -->\n\n> This is the active development branch. Check out [jiti/v1](https://github.com/unjs/jiti/tree/v1) for legacy v1 docs and code.\n\n## 🌟 Used in\n\n[Docusaurus](https://docusaurus.io/), [ESLint](https://github.com/eslint/eslint), [FormKit](https://formkit.com/), [<PERSON><PERSON>](https://histoire.dev/), [<PERSON>ni<PERSON>](https://knip.dev/), [<PERSON><PERSON>](https://nitro.unjs.io/), [Nuxt](https://nuxt.com/), [PostCSS loader](https://github.com/webpack-contrib/postcss-loader), [Rsbuild](https://rsbuild.dev/), [Size Limit](https://github.com/ai/size-limit), [Slidev](https://sli.dev/), [Tailwindcss](https://tailwindcss.com/), [Tokenami](https://github.com/tokenami/tokenami), [UnoCSS](https://unocss.dev/), [WXT](https://wxt.dev/), [Winglang](https://www.winglang.io/), [Graphql code generator](https://the-guild.dev/graphql/codegen), [Lingui](https://lingui.dev/), [Scaffdog](https://scaff.dog/), [Storybook](https://storybook.js.org), [...UnJS ecosystem](https://unjs.io/), [...60M+ npm monthly downloads](https://npm.chart.dev/jiti), [...6M+ public repositories](https://github.com/unjs/jiti/network/dependents).\n\n## ✅ Features\n\n- Seamless TypeScript and ESM syntax support for Node.js\n- Seamless interoperability between ESM and CommonJS\n- Asynchronous API to replace `import()`\n- Synchronous API to replace `require()` (deprecated)\n- Super slim and zero dependency\n- Custom resolve aliases\n- Smart syntax detection to avoid extra transforms\n- Node.js native `require.cache` integration\n- Filesystem transpile with hard disk caches\n- ESM Loader support\n- JSX support (opt-in)\n\n> [!IMPORTANT]\n> To enhance compatibility, jiti `>=2.1` enabled [`interopdefault`](#interopdefault) using a new Proxy method. If you migrated to `2.0.0` earlier, this might have caused behavior changes. In case of any issues during the upgrade, please [report](https://github.com/unjs/jiti/issues) so we can investigate to solve them. 🙏🏼\n\n## 💡 Usage\n\n### CLI\n\nYou can use `jiti` CLI to quickly run any script with TypeScript and native ESM support!\n\n```bash\nnpx jiti ./index.ts\n```\n\n### Programmatic\n\nInitialize a jiti instance:\n\n```js\n// ESM\nimport { createJiti } from \"jiti\";\nconst jiti = createJiti(import.meta.url);\n\n// CommonJS (deprecated)\nconst { createJiti } = require(\"jiti\");\nconst jiti = createJiti(__filename);\n```\n\nImport (async) and resolve with ESM compatibility:\n\n```js\n// jiti.import(id) is similar to import(id)\nconst mod = await jiti.import(\"./path/to/file.ts\");\n\n// jiti.esmResolve(id) is similar to import.meta.resolve(id)\nconst resolvedPath = jiti.esmResolve(\"./src\");\n```\n\nIf you need the default export of module, you can use `jiti.import(id, { default: true })` as shortcut to `mod?.default ?? mod`.\n\n```js\n// shortcut to mod?.default ?? mod\nconst modDefault = await jiti.import(\"./path/to/file.ts\", { default: true });\n```\n\nCommonJS (sync & deprecated):\n\n```js\n// jiti() is similar to require(id)\nconst mod = jiti(\"./path/to/file.ts\");\n\n// jiti.resolve() is similar to require.resolve(id)\nconst resolvedPath = jiti.resolve(\"./src\");\n```\n\nYou can also pass options as the second argument:\n\n```js\nconst jiti = createJiti(import.meta.url, { debug: true });\n```\n\n### Register global ESM loader\n\nYou can globally register jiti using [global hooks](https://nodejs.org/api/module.html#initialize). (Important: Requires Node.js > 20)\n\n```js\nimport \"jiti/register\";\n```\n\nOr:\n\n```bash\nnode --import jiti/register index.ts\n```\n\n## 🎈 `jiti/native`\n\nYou can alias `jiti` to `jiti/native` to directly depend on runtime's [`import.meta.resolve`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import.meta/resolve) and dynamic [`import()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import) support. This allows easing up the ecosystem transition to runtime native support by giving the same API of jiti.\n\n## ⚙️ Options\n\n### `debug`\n\n- Type: Boolean\n- Default: `false`\n- Environment variable: `JITI_DEBUG`\n\nEnable verbose logging. You can use `JITI_DEBUG=1 <your command>` to enable it.\n\n### `fsCache`\n\n- Type: Boolean | String\n- Default: `true`\n- Environment variable: `JITI_FS_CACHE`\n\nFilesystem source cache (enabled by default)\n\nBy default (when is `true`), jiti uses `node_modules/.cache/jiti` (if exists) or `{TMP_DIR}/jiti`.\n\n**Note:** It is recommended that this option be enabled for better performance.\n\n### `moduleCache`\n\n- Type: String\n- Default: `true`\n- Environment variable: `JITI_MODULE_CACHE`\n\nRuntime module cache (enabled by default).\n\nDisabling allows editing code and importing the same module multiple times.\n\nWhen enabled, jiti integrates with Node.js native CommonJS cache-store.\n\n### `transform`\n\n- Type: Function\n- Default: Babel (lazy loaded)\n\nTransform function. See [src/babel](./src/babel.ts) for more details\n\n### `sourceMaps`\n\n- Type: Boolean\n- Default `false`\n- Environment variable: `JITI_SOURCE_MAPS`\n\nAdd inline source map to transformed source for better debugging.\n\n### `interopDefault`\n\n- Type: Boolean\n- Default: `true`\n- Environment variable: `JITI_INTEROP_DEFAULT`\n\nJiti combines module exports with the `default` export using an internal Proxy to improve compatibility with mixed CJS/ESM usage. You can check the current implementation [here](https://github.com/unjs/jiti/blob/main/src/utils.ts#L105).\n\n### `alias`\n\n- Type: Object\n- Default: -\n- Environment variable: `JITI_ALIAS`\n\nYou can also pass an object to the environment variable for inline config. Example: `JITI_ALIAS='{\"~/*\": \"./src/*\"}' jiti ...`.\n\nCustom alias map used to resolve IDs.\n\n### `nativeModules`\n\n- Type: Array\n- Default: ['typescript']\n- Environment variable: `JITI_NATIVE_MODULES`\n\nList of modules (within `node_modules`) to always use native `require()` for them.\n\n### `transformModules`\n\n- Type: Array\n- Default: []\n- Environment variable: `JITI_TRANSFORM_MODULES`\n\nList of modules (within `node_modules`) to transform them regardless of syntax.\n\n### `importMeta`\n\nParent module's [`import.meta`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/import.meta) context to use for ESM resolution. (only used for `jiti/native` import).\n\n### `tryNative`\n\n- Type: Boolean\n- Default: Enabled if bun is detected\n- Environment variable: `JITI_TRY_NATIVE`\n\nTry to use native require and import without jiti transformations first.\n\n### `jsx`\n\n- Type: Boolean | {options}\n- Default: `false`\n- Environment Variable: `JITI_JSX`\n\nEnable JSX support using [`@babel/plugin-transform-react-jsx`](https://babeljs.io/docs/babel-plugin-transform-react-jsx).\n\nSee [`test/fixtures/jsx`](./test/fixtures/jsx) for framework integration examples.\n\n## Development\n\n- Clone this repository\n- Enable [Corepack](https://github.com/nodejs/corepack) using `corepack enable`\n- Install dependencies using `pnpm install`\n- Run `pnpm dev`\n- Run `pnpm jiti ./test/path/to/file.ts`\n\n## License\n\n<!-- automd:contributors license=MIT author=\"pi0\" -->\n\nPublished under the [MIT](https://github.com/unjs/jiti/blob/main/LICENSE) license.\nMade by [@pi0](https://github.com/pi0) and [community](https://github.com/unjs/jiti/graphs/contributors) 💛\n<br><br>\n<a href=\"https://github.com/unjs/jiti/graphs/contributors\">\n<img src=\"https://contrib.rocks/image?repo=unjs/jiti\" />\n</a>\n\n<!-- /automd -->\n\n<!-- automd:with-automd -->\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}