{"_id": "import-local", "_rev": "14-83c5558e7615b39f4764f9cf0f6762fc", "name": "import-local", "dist-tags": {"latest": "3.2.0"}, "versions": {"0.1.0": {"name": "import-local", "version": "0.1.0", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "f6575ffbcd6bac3953def94ac91d12db2d603f67", "tarball": "https://registry.npmjs.org/import-local/-/import-local-0.1.0.tgz", "integrity": "sha512-nfEokx0iz4ryO5XzpHgooeHjYqTRkFfVdMenpUn3zJ3JMniFkv/IFZTQzCOXGE5TyFQ31OF4HBjt4flifHn87A==", "signatures": [{"sig": "MEQCIFuRiRAfB2RbUDXBIecLX6Hhqt8QBe0JSwfvrfMKYOjzAiB0uoSv2ibQDwUyOwWWQFboif/l6Uan6kuJYOeyb7DiFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f6575ffbcd6bac3953def94ac91d12db2d603f67", "engines": {"node": ">=4"}, "gitHead": "87a0c91a9a0ecea64471a95479b0366af296b989", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"pkg-dir": "^2.0.0", "resolve-cwd": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "cpy": "^5.0.0", "del": "^2.2.2", "execa": "^0.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/import-local-0.1.0.tgz_1493920790095_0.707845498342067", "host": "packages-12-west.internal.npmjs.com"}}, "0.1.1": {"name": "import-local", "version": "0.1.1", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "b1179572aacdc11c6a91009fb430dbcab5f668a8", "tarball": "https://registry.npmjs.org/import-local/-/import-local-0.1.1.tgz", "integrity": "sha512-SRad1ui9CbQ2qYd4nYb4TfeK4VpvmVpSFEJJBlNM3OCnZ9QWkz5sb/iLXENfueBTCxsCR6xAZzDHPUtTDAi+Dw==", "signatures": [{"sig": "MEUCIDT3zm2aMvdBoo2W5iEtLFE53hiylMLvP11BS3R+rOKRAiEAv5DZWQULNgmFTThfOPkqEcyfruUt31n74fwVWJCRY2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "fixtures/cli.js"], "_shasum": "b1179572aacdc11c6a91009fb430dbcab5f668a8", "engines": {"node": ">=4"}, "gitHead": "05e7e5d29ee3eec3a5ec535db802b9906f27afd9", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"pkg-dir": "^2.0.0", "resolve-cwd": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "cpy": "^5.0.0", "del": "^2.2.2", "execa": "^0.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/import-local-0.1.1.tgz_1494063512345_0.08312201895751059", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.0": {"name": "import-local", "version": "1.0.0", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "5e4ffdc03f4fe6c009c6729beb29631c2f8227bc", "tarball": "https://registry.npmjs.org/import-local/-/import-local-1.0.0.tgz", "integrity": "sha512-vAaZHieK9qjGo58agRBg+bhHX3hoTZU/Oa3GESWLz7t1U62fk63aHuDJJEteXoDeTCcPmUT+z38gkHPZkkmpmQ==", "signatures": [{"sig": "MEUCIQDiuLPJzPXKnWt6/t1/DFs80VerAIwJgpo56FOu05fH7gIgZ+peV4+5mRYekHikZYSliUXzPYj5FxWQv0Af1UYRlZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "fixtures/cli.js"], "engines": {"node": ">=4"}, "gitHead": "b3a40b30273907ddcf53d7c9df9687ef02d5d6dc", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"pkg-dir": "^2.0.0", "resolve-cwd": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "cpy": "^6.0.0", "del": "^3.0.0", "execa": "^0.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/import-local-1.0.0.tgz_1513333080491_0.8174301576800644", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "import-local", "version": "2.0.0", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "xo": {"ignores": ["fixtures"]}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "55070be38a5993cf18ef6db7e961f5bee5c5a09d", "tarball": "https://registry.npmjs.org/import-local/-/import-local-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-b6s04m3O+s3CGSbqDIyP4R6aAwAeYlVq9+WUWep6iHa8ETRf9yei1U48C5MmfJmV9AiLYYBKPMq/W+/WRpQmCQ==", "signatures": [{"sig": "MEYCIQC5jVtS4CUyZaPW51QGyFOzKQZ/Ts69f7WysYTzenK51QIhAKQ5skPGR9xW68LSaHZo4Bm6ydIJflsh6sI2N5vuvFqN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiZm/CRA9TVsSAnZWagAAqVoP/RvjCV9n5/YWAsXKXbdG\np3O+2MMA2dGA/UiXKus70KivRpQayAwCSw94LJkCESOA79zXYKfovzi4MJFE\nUlUwgZRD1/aeV+ujYHMaOTGjcMF1BAxm0xWLTcTq/HDKoN5ETdqGQMW6quou\nFHK5HJk6w2ZeBQD9+qd482nWyW1HbapuD2MpTZAWlTgc9LUj4jETPZ3LgcXd\nVSyQ8CA52FuLT7/LqXQ/DvXQQpKDx+LRfHP1iJASigOemJqvkFbLzciirm79\naf/F8a+YTh8z90fP9Zyo4mcA3OqwRCvDq8+/Ls3ZuKnbdofKmfjcM+s53YBl\nS5GVR0DfJA85Pv8sO0+/dRA/JDDHyy+vcmgYBCvTs6muSVk9eoMbBYs5PXpK\nMh/tSvNPuUD1/LLmE+tVvQ9NuriEQboH8WfmLUhQPZbtvBS4VunyddTNnMf7\nA6SVQjkHxwh5DR1gDFmc0k8jTnb50Vi1HnnTMz6tjrAz9WG0b0zhffpcfGRw\nTZ/D3DXSxvqLOqIfYHrl5c5wKgvS0+7Ed0TXR0lg8iwG+iavlOlTqvIz0jwO\nkugY+d+zJcGh9RHOIaNtduroB5nXAUTRPm0DyPvh3NfzhQJnFHTm8EMwEeSz\nRYZqMMO9PJKK+mdUTMNiU9M2wp4sAVSpT9XZIHCl560nDQfbQq0gRMh9v3CR\nQ8wF\r\n=BXgL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "ca0a08fe9b9072a73294a875e93970ac0634f452", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "cpy": "^7.0.1", "del": "^3.0.0", "execa": "^0.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/import-local_2.0.0_1535744447258_0.3296999336977211", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "import-local", "version": "3.0.0", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "xo": {"ignores": ["fixtures"]}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "ce45eb1528ac8fb921c99df7421326b8ee46c638", "tarball": "https://registry.npmjs.org/import-local/-/import-local-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-8Uh3mLtqDxb3jUxHRev+qSdJbSe7RD0avbWM36kHj87KwCTIXdM7b/OfmwrJKjIVcSBK4w1XL5YCatdfV10e3A==", "signatures": [{"sig": "MEUCICYce5a5zNoOcUFeE5pVHfWYHYIhDQxGQgcsFkpxKB8QAiEA+1Z28wipsiV5FZzVwcOytoVAM/ld8xul2rSOr3XawJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdF7TvCRA9TVsSAnZWagAAhbQP/3I4U2dV+MjCKi5CqK3c\n13VOU71q9/58b8j8p/ewDdQ/Qz9Ie7lsqgViddmkeozJRuNk3mc6gM039pvb\nRbn2xacYzhXe4063K5yu3IgHFlZmdMnlNdngiQzsyvmNPoNewQlsXHPUircV\nremSXJkY3JdaebmXxGcjZlN/XObwVXGYJ4lWvD+jqZHPMsL56IaihqZ3DZ10\nLYD9Ii8/oLsdWQmVspthR0sSJuBZMiI21q7qDWvKth7Go/jnUMVJMZw+6Dn6\n9ksVI/DvJrLHN6cH4YMeP9bq7eCvqeRhVZ66acnk/rtqDwXBWCu2lx81zvah\nM9dOEr/Rhbf5dWVQEiBdG0gGFV7ELgBIgPSuGX9R867IYLpks5nmoBZxfsZK\n+jH2IE/0wqOUQunP8h5Fa9hFZmItHp6aFRItMiqaVc5yLRkzyJcFdorxb3Kn\nICsx5ubKu3QrPw20lXUVt5c7NL+MM9Dj0aAYQFXOlb880awrZqTNsxiJllc3\nXbj734ygRLNxFn11SMNspIs4dtaDGu5Xwcte8+9KBkyOcB2NvlWQ7MtDsxrF\nIl/NfI3RKsMrLjCbOflZFeya9AzENrj3g32HHiodAEykF/4DBK1pdnLS8Y4/\nRrKJto/HUEaSA5a0iMGi36sbo8SMJXing82Ppb78M5emVwUAy7c/iJSZYh0Z\nYyka\r\n=eHQk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "77bc808f6fb2d11fc79b95dbcda06c0e1d3ef10c", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"find-up": "^4.1.0", "pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "cpy": "^7.0.1", "del": "^4.1.1", "execa": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/import-local_3.0.0_1561834734792_0.16239590100067613", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "import-local", "version": "3.0.1", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@3.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "xo": {"ignores": ["fixtures"]}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "0b0f61a207b6c6b005c931dc72951ddde8303713", "tarball": "https://registry.npmjs.org/import-local/-/import-local-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-XlabwTJ9tPOdyjnGdGvxUMnUhmhlnJhdYjp5e8UDb2fO+5Gto1Frlg66ixVAf1Os+zQlrKt3QlTCVodyxYBZjQ==", "signatures": [{"sig": "MEQCIBwxzvzlc3035lcudubCgFmScWbTe96MH1xnfQzqsj/vAiBNKFQSumfgJi8nyHB0+6SViSf6PDmkq+QqQSoq8oefew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIflsCRA9TVsSAnZWagAAQa8P/3YWYA83dfLiUkHGLzsn\naAfz3W/FIKciazvy/9UYxqBQoECCNAvCdP6DuSShRAPaPC9OuLDODA/5KX4D\n/0hqVlqs9lW5IlgX7wbaX673/ny77mCPte/OvgwNAEbhhhOZ1ilUpUA8TXyM\njaYfDJy40SHDToll3hGu+KHKcLv9BV8JidZ5bVItgw+Gd1RZPnnOLgubU6RB\nAUlLoZPmraNMZpo6iIMP5mR3GegeMFVM9I0AJ5SqAM53fIs4geMV1G1jAPuA\nJ5FJrGa1r2fgX8bs9Gic1Y9VqMLtkMp7zFPt/4o4jKYC1oIqneM+PC8z1b/3\n4txunpp1QA92jtJEaTgbLVNh7IUOBn1DIH0lF5ZgsEUNk3m6ebxM1qekF1op\nq7rw2GZijicF4Vf+rp1nmiPCSgzrCI7IUmUmzUID0Oz0pzoaoqaK/NwJvxv8\nZkwcUAfYCG4f6+OltzFMlL7am1/Mh/ScvoB6iz8pSooSUczU0cPS6ERLq1T/\nd5tw3iqEePz9KloQtfPTWZiYCAZhxHGlZc77UfaKWRqCaJ5uVxUYo9kygU76\n6tJ/+SZgyXi6UVF6Z+IIQcLBDvLJI1JamX3aJkD0brKCbOH0WzubfWAds8do\nyqgPmJ3Pxy4X6KongXNTssIkBntJtq5XRbu2x2H8VC4cHCpdjRMc9LA/oFAb\nyYTp\r\n=fE+4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "3b6d85bea895f8ef3acad55e7fb7d06a4d77a2aa", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "cpy": "^7.0.1", "del": "^4.1.1", "execa": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/import-local_3.0.1_1562507627368_0.3233282122265466", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "import-local", "version": "3.0.2", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@3.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "xo": {"ignores": ["fixtures"]}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "a8cfd0431d1de4a2199703d003e3e62364fa6db6", "tarball": "https://registry.npmjs.org/import-local/-/import-local-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-vjL3+w0oulAVZ0hBHnxa/Nm5TAurf9YLQJDhqRZyqb+VKGOB6LU8t9H1Nr5CIo16vh9XfJTOoHwU0B71S557gA==", "signatures": [{"sig": "MEUCID9PQSi+0yf1F7rA0u9gyHa/FKh5xM1cguLKOWyyAuWmAiEA69FznyzVJZ8gsL83aM292WgAX2kZQgbzplF+g06XyEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4177, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdN/u/CRA9TVsSAnZWagAA9IcP/iryGyKb6ZbQFewtzAO5\nPhTg6bBjk4EL4DwpXhWfKA30KsGC0RY3ki0hALG7tMY3PiV5iZmMuQKE+PHp\nOZzAGrNoNxju+DI9UwYKToyHElARd0fl3uPTmWl5kUmKzea7qpTmmKxC6Z/w\n4oizotszrJOqW40bNVRyU7KKutfyRxn2NdSJaYLpJU0RbzN/HFfpS+RngYGf\nVbgV4mcSiXjZmtOYkhuQl+8GHx1KDQSPOsFj3kB1UYaq8t4zqxl/NPsS9TxE\nVuJF2/noa/Wi5IT30+9iJTTy2fVA0tH+H15HqzLROUdJUs+wt6XZUrfOGC9i\n2bpOAHEp/CdpTXprzEMt603vioiuZApZIOVp+JvCAcMnG2b5q1ekVf94xXw9\nPDmBZp9q2QjGwoZ/zB3g3/NBgulR0OyhsTk8AROzoj96FDmeveBSksfHV7fD\n+IG+z1z/bP1lc8TxTKkI2nKhAINXGGXrptZoVncX6hBjpAYf445dL0abqcMt\n5FwjOT5gcSWx4r8DdDozOUHrlLOjQuaf5LKpzoe3ysVut4quUkKNehz7/ykN\n1NsAwXTy7aXGZKT3XLz8LuDHpYr68mQrLSK8W43ArkQt9BwFqxFosNRlLQKi\nRJE61AqHH4M6Ixc43Gir+Sys0jL49SSIRPECdWz6f5Eg/h7LDQcNdpD9VXmT\nJJ6v\r\n=/aKs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "101c4517c940f65799db1fb4de1d178f4c5b7b08", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "2.1.0", "cpy": "^7.0.1", "del": "^4.1.1", "execa": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/import-local_3.0.2_1563950014712_0.05855547209453382", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "import-local", "version": "3.0.3", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@3.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "xo": {"ignores": ["fixtures"]}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "4d51c2c495ca9393da259ec66b62e022920211e0", "tarball": "https://registry.npmjs.org/import-local/-/import-local-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-bE9iaUY3CXH8Cwfan/abDKAxe1KGT9kyGsBPqf6DMK/z0a2OzAsrukeYNgIH6cH5Xr452jb1TUL8rSfCLjZ9uA==", "signatures": [{"sig": "MEUCIQDnYT7ZlZe/yGSZKuOyDhx7v3Du+dm4Pr1Z+GEe+z52hQIgFKZRpm5Ttm8B6JmQ/ddN2aPzr9ZYkSaB+QT3pDSP5gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4322}, "engines": {"node": ">=8"}, "gitHead": "87e1ced6b9c2d723150ed9f3914954c44552339d", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "2.1.0", "cpy": "^7.0.1", "del": "^4.1.1", "execa": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/import-local_3.0.3_1633327281290_0.7959688664482825", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "import-local", "version": "3.1.0", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "import-local@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/import-local#readme", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "xo": {"ignores": ["fixtures"]}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "dist": {"shasum": "b4479df8a5fd44f6cdce24070675676063c95cb4", "tarball": "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==", "signatures": [{"sig": "MEUCIQC/2MuhglIV0m/U5PMRpxTz9QlKRLICEJqZED5v1Z2a/AIgAvmT+mPCZikYRfLEvsjcozoSvsreA9rEXGyKgvowCMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1qWQCRA9TVsSAnZWagAAI64P/A68BPmdn13Da5Xxc5Sn\npl+wcrtmq1HIFW5AEf4R2yxgte++LDnx/tlQOhVPXZEJYEX/OgFqtA2M4eMy\ngg80P6eHFPm97fpS1IHqDPTLGwECFHZu+40Hy8g1z1BSpYHbWMe4fmBJm65A\nFHxMj6ffRd8FXLHU8US++vuosD3WVQ5tAes6wecw6vZLQP8s+f+XgYh0yHnq\nHFYNX+BSEKQ4MvLhdByr6OmWsLLqok4IM7+CI9hWFRzXmEo4JpkYae9Zx1Pf\nE7Cl1RtOoTHR/kaK7MPpO+zc/RuN0fdJCt9wPOas155CtJOcVArv8jMPAKif\nUuT0DoIo2sEGmGPI9WIztDU2VGkHOCuqiu8nisyqSoTh5xdunMh4vWDCy3pK\nlpqIsNfclzFUkYVZ/G/Du5FpF9KzfUY5zPzdK6MiFzM4QzF3RoUwXoD5kk3v\nXeBzB1NrGwkB04fwoCrYmKlfww9Xqo1DrP1Yceo3i2q02SwwYzm0ri7/apeO\nryM7xKJnZnZlsIiE7kgVHYB3tWFDsp32TifrUMMjOYTfPQGAdRZqrA2ApmQm\nGukWuds45dZWdYvFEqSij2PHM+eo2SvfFjgrn7hSPZkPpqweuIvMY50Clrdo\nuRURsmY0XzUGX9Cm5atfnyXNlomeFVDIPHHMua4m7kIFr6sOnNpnmF7k+Iq6\nlr+p\r\n=hCvS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "b45c5a6b68488da41b69ae6a8d2ef5edadb3de85", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/import-local.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Let a globally installed package use a locally installed version of itself if available", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "2.1.0", "cpy": "^7.0.1", "del": "^4.1.1", "execa": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/import-local_3.1.0_1641457040443_0.08771197620841287", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "import-local", "version": "3.2.0", "description": "Let a globally installed package use a locally installed version of itself if available", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/import-local.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "sideEffects": false, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "devDependencies": {"ava": "2.1.0", "cpy": "^7.0.1", "del": "^4.1.1", "execa": "^2.0.1", "xo": "^0.24.0"}, "xo": {"ignores": ["fixtures"]}, "_id": "import-local@3.2.0", "gitHead": "ff3fb1541a40b629a22d2d21a4e1cf4605c6bb5a", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "homepage": "https://github.com/sindresorhus/import-local#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==", "shasum": "c3d5c745798c02a6f8b897726aba5100186ee260", "tarball": "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz", "fileCount": 6, "unpackedSize": 4730, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDTjEz7a2j6L5wH/U0uHQu1PfDkWPMYML+C9zB4TaCDJAiA4CB0iDbihzJxx3hL08kYui/OKOmpGfhopqbWNzW+t6g=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/import-local_3.2.0_1721645808498_0.8746776811717698"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-05-04T17:59:50.343Z", "modified": "2024-07-22T10:56:48.819Z", "0.1.0": "2017-05-04T17:59:50.343Z", "0.1.1": "2017-05-06T09:38:35.003Z", "1.0.0": "2017-12-15T10:18:01.261Z", "2.0.0": "2018-08-31T19:40:47.407Z", "3.0.0": "2019-06-29T18:58:54.926Z", "3.0.1": "2019-07-07T13:53:47.456Z", "3.0.2": "2019-07-24T06:33:34.846Z", "3.0.3": "2021-10-04T06:01:21.422Z", "3.1.0": "2022-01-06T08:17:20.570Z", "3.2.0": "2024-07-22T10:56:48.659Z"}, "bugs": {"url": "https://github.com/sindresorhus/import-local/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/import-local#readme", "keywords": ["import", "local", "require", "resolve", "global", "version", "prefer", "cli"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/import-local.git"}, "description": "Let a globally installed package use a locally installed version of itself if available", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# import-local\n\n> Let a globally installed package use a locally installed version of itself if available\n\nUseful for CLI tools that want to defer to the user's locally installed version when available, but still work if it's not installed locally. For example, [AVA](https://avajs.dev) and [XO](https://github.com/xojs/xo) uses this method.\n\n## Install\n\n```sh\nnpm install import-local\n```\n\n## Usage\n\n```js\nimport importLocal from 'import-local';\n\nif (importLocal(import.meta.url)) {\n\tconsole.log('Using local version of this package');\n} else {\n\t// Code for both global and local version here…\n}\n```\n\nYou can also pass in `__filename` when used in a CommonJS context.\n", "readmeFilename": "readme.md", "users": {"rocket0191": true, "flumpus-dev": true}}