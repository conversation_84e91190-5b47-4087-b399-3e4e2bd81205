{"_id": "@sideway/pinpoint", "_rev": "1-ecfdb43bac3cb547e4fb81b83a4f3deb", "name": "@sideway/pinpoint", "dist-tags": {"latest": "2.0.0"}, "versions": {"2.0.0": {"name": "@sideway/pinpoint", "description": "Return the filename and line number of the calling function", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/sideway/pinpoint.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["utilities"], "dependencies": {}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "d9b4e37a474bda32c1297a437726310cc9b0356f", "bugs": {"url": "https://github.com/sideway/pinpoint/issues"}, "homepage": "https://github.com/sideway/pinpoint#readme", "_id": "@sideway/pinpoint@2.0.0", "_nodeVersion": "14.4.0", "_npmVersion": "7.0.5", "dist": {"integrity": "sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==", "shasum": "cff8ffadc372ad29fd3f78277aeb29e632cc70df", "tarball": "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz", "fileCount": 5, "unpackedSize": 3643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk89zCRA9TVsSAnZWagAA/M0QAIvxuDr4dZ/7BaPNxvas\nAdu5At4OcZ3lmrYPy4BAtlJUgeHv6uxg67mw6UcXWYgGRGe0AHgD/D7zY8Aa\nK1/N2gq8G5v6xU4APqSyUAF5ZA59i/DMZc6tG5bCeGMysg3S1j+MaBPy4px9\nuNOGZLA2WxPL8UIhK0jDH0O1dXo+8s1H5s/gvV4ydoo2F1/WrktVppOSh4DM\n9QAJcHCeZwUxlsOo+a93k7cNwVrVzzyI71/7owpE1uakB/NAjiScJniDlTtL\n+PbqYnhND4AawnHlV/3sc9d6gnYPHeR79FTXMek7ZQjcR1Q67CW0HhbtAUJj\nLFdnc0MpFj2u630rqy2idYVWo0hcqlTOFa1euKz+FfRNC0CDI9h3T2aQRDZ2\np0X8TD5VLLgGbSAUkVajtw1/4RxZXoPwNrAevb+2g/3f5UQ3PYHsWFycBKvi\n/h3UQAzakmv5LgfHYo+q+QEJBrAGgzDObQ1Gr+T9yFTovtJ2U29ZMsrrRiKr\nv9rrv9SEptwz9igo3Th8nvMSDGbadJbvE2dm6ULutFlKDPE0eGo2Kn0HfbiR\nD5UjWJz6OsK4D/O+MQahOgpF2MuNoxubDD6WAQPHixRlYLladCXbmwCflshO\nIRZ8r/eqxRepVHCk8DlUfv4lSAXz4OV4ykw6V2khVT0le7RZOO+x9/fPqYkT\nHf11\r\n=7mfb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqZKkI/zpGijrGF3BmUQdpdoxEBhNbFUwyTWUko6iRlAiB+Kkuk1gqqGo8iFDJGuHyQ6Onh7nDlHHXiXG6DgKzQQw=="}]}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pinpoint_2.0.0_1603522419386_0.8906523449666282"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-10-24T06:53:39.297Z", "2.0.0": "2020-10-24T06:53:39.495Z", "modified": "2022-04-06T23:30:00.997Z"}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "description": "Return the filename and line number of the calling function", "homepage": "https://github.com/sideway/pinpoint#readme", "keywords": ["utilities"], "repository": {"type": "git", "url": "git://github.com/sideway/pinpoint.git"}, "bugs": {"url": "https://github.com/sideway/pinpoint/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readme": "# @sideway/pinpoint\n\n#### Return the filename and line number of the calling function.\n\n**pinpoint** is part of the **joi** ecosystem.\n\n### Visit the [joi.dev](https://joi.dev) Developer Portal for tutorials, documentation, and support\n\n## Useful resources\n\n- [Documentation and API](https://joi.dev/module/pinpoint/)\n- [Version status](https://joi.dev/resources/status/#pinpoint) (builds, dependencies, node versions, licenses, eol)\n- [Changelog](https://joi.dev/module/pinpoint/changelog/)\n- [Project policies](https://joi.dev/policies/)\n", "readmeFilename": "README.md"}