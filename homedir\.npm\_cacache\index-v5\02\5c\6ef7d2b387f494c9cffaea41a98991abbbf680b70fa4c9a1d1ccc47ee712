
b9df272960146ef48109946631e993c5208b59c9	{"key":"pacote:version-manifest:https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz:sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==","integrity":"sha512-C2EkHXwXvLsbrucJTRS3xFHv7Mf/y9klmKDxPTE8yevCoH5h8Ae69Y+/lP+ahpW91crnzgO78elOk2E6APJfIQ==","time":1750369198125,"size":1,"metadata":{"id":"lightningcss-win32-x64-msvc@1.30.1","manifest":{"name":"lightningcss-win32-x64-msvc","version":"1.30.1","engines":{"node":">= 12.0.0"},"cpu":["x64"],"os":["win32"],"dependencies":{},"optionalDependencies":{},"peerDependenciesMeta":{},"devDependencies":{},"bundleDependencies":false,"peerDependencies":{},"deprecated":false,"_resolved":"https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz","_integrity":"sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==","_shasum":"fd7dd008ea98494b85d24b4bea016793f2e0e352","_shrinkwrap":null,"_id":"lightningcss-win32-x64-msvc@1.30.1"},"type":"finalized-manifest"}}