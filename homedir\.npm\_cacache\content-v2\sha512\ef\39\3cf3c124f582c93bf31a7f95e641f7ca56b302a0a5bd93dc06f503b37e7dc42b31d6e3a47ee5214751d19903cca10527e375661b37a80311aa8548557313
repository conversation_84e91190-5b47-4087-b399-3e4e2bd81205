{"_id": "@webassemblyjs/wasm-opt", "_rev": "64-2683e715df8c15d10d05040ca2f3629f", "name": "@webassemblyjs/wasm-opt", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.2.7": {"name": "@webassemblyjs/wasm-opt", "version": "1.2.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-opt@1.2.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a85d6a80b47a7e5ff268a1a55d5a8dfcb751e047", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.2.7.tgz", "fileCount": 5, "integrity": "sha512-ISAWYmwC4pKKISKn+80VtCk2qRjA/mnTCn9yXIbJ18BgMAnxFaIXHXRwZPLoedhtOpZIPCKrSUfMqAwv+8sHvw==", "signatures": [{"sig": "MEYCIQCDKkXxqjCNO5H1vxSF9OW7B0xZ/UncY0t7uQdOkazPdwIhAKX3jG7tCODDEtlprNCkc5+O1wnFR8e8/gCtQiomOu4v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fDoCRA9TVsSAnZWagAA2wIP/jhhEym4LRX0aDvADYQ3\nejvbeiccS1WG3/KICyei5w9bJ5FnV4tJ+OQIEXJ12lsVBwN3LvdjRwbyEd/F\nZaC9lqnHxrWWlypbFLjLOVOVZ9p1n6P+gXVWGFuEggGVZgHfF9evEwR5Ailg\nHbNkcY1kPUaH6N/tUQHHTUbrcS+f6jG6+QtZsrrFEJ6QCTvzy+ikJRdkXZAk\n8+VHipYuaiAgP6qyKGmLq5HKSzW5TfJHyWEMJm47D6jtpcA0a5LOp5ZMUPeo\nu8w/jhK4OA1eQYL9+ekNoAKWaPCfLFVaXaP0QRZZvNxaS+Rgl1uyccz17KnI\nAeaUlN1Qb28x86wuX9W3shb2sYcdRrJynLqxGZ8eZkM6OrCPpOfXqnffRTtG\nR6vmZ7dLRRUhxEGJcGNzojq//CPFmBuwf8iEKaXaHAReKTri97pcMrRYwVNp\nYnvihjrcUhg3FJTWDmjDnGmDs0NgDXgU6lKzE9/Qmv36U8PMYAM+bPU9vqx6\nvhrbXMwrqfMg8VhYSkHO+n8WvN8/F7sMqA93SYEREHtKQHv4eGP7YIuPZGBg\nZs4p9LH/z+oOiW/qVGt1B4IiZhx3ATVsXG4bhsXvE0fkH3SmM92Z4WCYrsJ5\nvtTfTHFQUDveHgWZv8+ITvqQmvs77jnU+Y7xxEvjYQ/nCW6MXiBEUzE8rpyR\ncRH+\r\n=cE6f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.7", "@webassemblyjs/wasm-gen": "1.2.7", "@webassemblyjs/wasm-parser": "1.2.7", "@webassemblyjs/helper-buffer": "1.2.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.2.7_1525280999027_0.16108447689088368", "host": "s3://npm-registry-packages"}}, "1.2.8": {"name": "@webassemblyjs/wasm-opt", "version": "1.2.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-opt@1.2.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "684f5a280904a56993b7054841ba0441ed0cd314", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.2.8.tgz", "fileCount": 5, "integrity": "sha512-4Tc82X7CZvCOouhR5L5HFbTTl/ajag4O8iYcuitxpATcO3eyq4/kAixNE+lJ4gmFH9bYWvaX1mZFjBM60ePzpA==", "signatures": [{"sig": "MEUCIFMM7z+EsqB84ZF08UyYpg2sFQrmY5hnWvmtz4nWuDdjAiEAlnY7wfXuCRFoArcv23M+IpY23trWkJRj9Td/9iEqarE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fRrCRA9TVsSAnZWagAAMX4P/03+w6YUiVR5jrBfJ5Or\nLXCTwYjJ+1WGj6YP7bI/9qPqpuKM+jXOdE+ZMKvQLMk5k9mMt2mjZY01Mwd9\n5AtngeiopvsmZTkqCVkRxcjT0l6lfa+dyzc+DNmlwjMv7bAww5Y5axPwqiyC\nXN7w5kKBRMuK/dvpjdDboevxH0WNowGze4/dG7jB9vSpAjmR6LYhaFupBpTN\nu6jOCot5FslQboknBu5FTf90wzHYg/6/GCnalIsGBHKRzLUPJKFkuk3CSIU0\nmoVSAM2K4LEk7vr/tA2pkbzTCtiYHes4Yq6i2o8OA+BK+ejS53iconTO9MQj\nJAy+sqVKicwYeDyO39FQQy1G7NGXL9a4aFhvIsFPU75YY0+nUEOqVNARcjG2\nQ3GQ0Yc2vkzITd1O/GKAGuRphSXWRkbBTY+Qq5jNBFaGrMhFO+2FMxObxAwx\njKHmYdbI01QsAiX059x1C8sNVcZ88rup4BiS3ERT4DbVrxz7BPRU+AibcV9g\nKQ7M9tTvlUYeTgw/Mj6cZC9J1jbKM1OqFPJNhc6+2grploeBustAz6vEkPLZ\nmKNB8t1K1iUd8Qn7mpDGwFpOaubffv57rhZeypREUOHLPrNSHAWbFmdJ67w5\nHcBcMm28Dl927N44GfrY9hFsWRRLuURN+bXe4hFL89h83xpIt2RWVT1BoBXz\ny6eD\r\n=VVsJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.8", "@webassemblyjs/wasm-gen": "1.2.8", "@webassemblyjs/wasm-parser": "1.2.8", "@webassemblyjs/helper-buffer": "1.2.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.2.8_1525281899145_0.8919099330902169", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.3.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-opt@1.3.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "958150b0d631eb407fc9b85b9a852526c849c015", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.3.0.tgz", "fileCount": 6, "integrity": "sha512-uWtbbEDzsrv+jPbk8jzHyrmCcDMT8J3VmifEsLuD5bov6Q40GJwj/GMAtbH2kB28dvqWU0lIA+D5dUlJHMgAzQ==", "signatures": [{"sig": "MEUCIGj9/s26yH2XUKzAJaGRtoRIFDSMxkRYqe/WgMuWFDe7AiEAmiYiYyqXBDshRy4p36xrujA+qjkw6nAat5i1E6GUGt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GCiCRA9TVsSAnZWagAAE48P/Rdzk7LwOgOYHs3vULxV\nrPTykHlG2Ol72GA7x1ywZ9281VDd0SNq1cD2nPompREKjtshB1l3YYJTghGR\n/ouhi2w1Fikv/uRmRYvquHy3A3rRClm22kg5TXQhm7TBWg1zlQ6Mk1BwUj0v\n125ksoA0cTfFnZ58yH5LDJvqjzW2WNQtbwpmAlLf7UrnX/+Yv8Ji/aXmqFW8\nlP22pDTWUnpMfRLO3JNig1cSvtl961fdgV/1w+1UnIkPfOYUvswW52RwnQ4Y\njOIAyE6tkDA2ukyxgOv7LwMypDF6LZSMCsahtvRBJMdaMRv+tQsSFdP6DTm0\nvP22xwt4ve4c6vW2b/rEtrajDTqUYOXcVtbzpagdF5RBU+9QzhxpXpjhH+bo\nbUAfPLaVBqsHGRODudO4nN5egCjCISir4EpKTBPCMhi2vFO9aBd09qsqcDvr\nRRAhpCaqzPume/TLfzUXY1oJ9WS3s4sWOqaP/GlQMBasr/ht+wkyjUu1MSOW\nix+3gQ1K1lq4c5PNLKIcCdAZuXRAs5sMExHxZC48XGdj14lPlzeSS2X+T31b\nD/gLcj/OQPVQmSSzP5gfK1T8zv41eyqR77icA+klmJxx6j03KCTSETJNLZC0\nXfASQxn7Iz3NulS8occghqk6Y0sBlgqmYERQgIeQBm/jHVJTynhhQHbZYeWX\nWYwj\r\n=dqKQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.3.0", "@webassemblyjs/wasm-gen": "1.3.0", "@webassemblyjs/wasm-parser": "1.3.0", "@webassemblyjs/helper-buffer": "1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.3.0_1525440673968_0.0008000023767740227", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.3.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-opt@1.3.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "172601dcdaaacd6b0b002df1252033198c65eceb", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.3.1.tgz", "fileCount": 5, "integrity": "sha512-Eqi1he5VuGxMYKGhrBU+UxUhIwTDlnuvJmwrad3k4dD2UQpqu3HedRndGdJTQ68xj2GqDCdE6QiLzjytSN4KVQ==", "signatures": [{"sig": "MEQCIE6/UXeKgDBh92ircu2SqVpIFo1tWbcbK6BJWXmZAddIAiB2bcqPp/2zbuc4LDWaVEdMKnhs9Yyo5YOPUpEnISn1Ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JZLCRA9TVsSAnZWagAAmScP/0ymrflaXSTOO7aJS6/V\nlB43I8pL+Fq7qUhs8q/9xsOgnA9z5d6TPlbvarRhmWB7cyLNX1Wks7RRvIKy\nc9Ky9VLEAJoKe2Ip5+rn1hyAxE5BjZBGgYH8sVIrruYUb1XtLPAhIVOi1Tot\n7MIaqhGhK/Rp/SXaB/tDEGpWS1/tCSzojpCDQgIoo8Nncftx0fsxeI0kX5ek\nmnYOwpGVcuBOTsnPBWnEkKyYJgXqWB8vRNDFwnYHdLwgghuJcz9HyXr2FVrs\nFsJCi7Fvssj0UrbAxVaJuS85V0M/uyobkgcSnXIZyA5ESNUFrpe3ADR92t4Z\nUvQT/FVkYRSpAw88uHO+I8eiKUDmtWwSzCadX2BlQFBe5lcyZqBuvJwHqW6m\nGu2eTR538rEE0g7rS4W+ABDTFAONeGngy+7czE09UYUMjaTUmbPQhZKcELDZ\naqtS579nM61XSDDYknRz1qz0Sjsvho2KuR+N1Lutd2lDzxErX3UOJOXx0xEQ\nJDOWc26XmLXVY1uhzzbIlx8i47TmA2Yj+YH69kIaMCByWvHLVz9Mow90EcEw\nvv9tkzY5A/EvOg++/BowhZycPrm+r0pe50TojClseCOoPiC5yRKHKWr7zlMz\ntSYKpRO/iUV5vdQam/lAp3R6j1lmC9RFN2D6+SXDT77bH5krnrVoYej8pYPA\nYFxP\r\n=niUQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.1", "@webassemblyjs/wasm-gen": "1.3.1", "@webassemblyjs/wasm-parser": "1.3.1", "@webassemblyjs/helper-buffer": "1.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.3.1_1525716555190_0.007588843125715616", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "@webassemblyjs/wasm-opt", "version": "1.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.3.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3f72c0c59422fdc7511d77e97e5ec462dafe378f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.3.2.tgz", "fileCount": 3, "integrity": "sha512-mPOKWJRDsq11ptRkFMV/21Op43nwFWCNyQ1UxA7LZIfk6q1HC2EJJVwlnqLMeTjk6BHLLAccOH9hd8GeI5360g==", "signatures": [{"sig": "MEUCIFCIBe3MoFVw5s5xMW7LML8CueD6jfrTyEcO2Q38wkvSAiEAxD09tqePZ6E5h/eAO3kHJ2frugACNl8r0LLpqoymsro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fC+CRA9TVsSAnZWagAAPU4P/0M3erR1FHjPkeSBAIhy\n4KZ5gCYRBDYoRHqpoLr/gdIKSpq7C8Ya7VJE42DocQT16id3ArWHp0B/MnId\n/9/XNdyt/hhHDTfBUV8jQVSH6s6BmonerjXkS2xMnu7re8xtAXwPKw70T2e2\nUojryHEKsvFCW0o0cUBHQBz6EB1gjn/2/ejbU0rYJItFPfDRV7iznJg4O8NJ\npZUx/x9b/40JUbIgx9c43VfmUwAqMXcV2t9zQjbdXE/CjwLCSw0XN5zdPFnT\nKTL0O6LAT3dMQPxq/upka3L9B8bTC6fr2l+LMAnn/3T42VXFH0boWR0ShfzF\nmD/p3mG1QIBbH7/4tvDITqZxuYByjz1AEjMreDT62GJEWjGePa+hLdC5v5ZE\nLFflv9bTos+qu8HhXSUy8kroNShsSWm0qI+/VOJbIerY0nSQHEb6GAfrCvbp\nwqxlAnUl/FHrggo1S4/5xKoeoT5sOHFZ14Ra+oX8PYGPyVJTcVkDcVJhePoS\naendMpdAHXtew/nRyJfUOXbAmY7hRItC12JQJpPQgv8WA+A1UyY2IcuWDd2W\nMv88wgbR+nvYCoz4TAew1YZPMLxWBtdTfwqMf4QJjNdcOOLUH0LGrViEGVHZ\nYcOCtqqqrFpbR1Ek1l3ZFdL3yxYQkfQxnSxD7pgqzfyGOaE7vI52JmZUhdLp\nKjil\r\n=wMlJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.2", "@webassemblyjs/wasm-gen": "1.3.2", "@webassemblyjs/wasm-parser": "1.3.2", "@webassemblyjs/helper-buffer": "1.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.3.2_1525805245519_0.2848754205811064", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "@webassemblyjs/wasm-opt", "version": "1.3.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.3.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7e047731d4e5810dc3ea30db2166193616c8a236", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.3.3.tgz", "fileCount": 3, "integrity": "sha512-ElnpH2ldFQQmBZp6Z7las0nL/4HyQiUeXExNW0FC4We9TQqFVw+WVINIm5RyeGdUnXy3BhhuAf5yEu4N1D9xXw==", "signatures": [{"sig": "MEUCIQDOxJD5vT01vahK1VbykzO1ilhnYhN/6OuHOj6NxqzRswIgDNTA3rXzIbScsr0XAM3tubsy36J7lkq/MTjHEVj6b0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qjzCRA9TVsSAnZWagAAfTAP/3crACRq1CuFDsCs3GME\nhjKPK2d8fpWyUGvHW86vVwxrloK0kKFuqMwSotMwj/EwDKUGCtuxPb7xFItA\n65B9CM2S+hjX7CXYCm/dnl6u1DA5i8LgJtSmQNKAKqPSLQnahXchcsTAamZ2\nqeKkXnIsk4tMZffm/tyeX8hOka96rIzgBgAMBju2q2iD3aobyoflokCdGUyW\nW7elRd6UvLY0VtkyM44+Luv8vkNZwTdNgwZ4pl1KdA8Cc/5BcwJnz0m1Y8kB\nCVJwOzwCkl9CixnyQX6ZGhlvJabv62mNYpsS24ShYKMXdbeu43YWrGX9AECW\nU0ouyRsBdZmMTtNOH6aCmiDdDzm6CIGgaMpvF+E8wUMvqt/MDaoI6RvJf+Kv\nZxVqsg8BHFWaPUUSryTa3rIAblp3XSc/sDDcBbIYu89yO0ZBZQ5j2tgQwen/\n0/aZAYqg8YRWaE+4c51X4S1Vn0dtWcgrHRjhxmgIqj6KIrrEWrXCUUdzyM21\nlCyVCK0DNGZgslyC5sWilODAmp+yfFV940tlbHYngp2xcyVm1Wggo11kTNnY\nxDnAfCDex7893rdjAb4B1eovcz8H9ACUzAuXtiy+nCYPFlRs1xbsr4YYqxSf\nzvxkD/LjOwaVNoRQs1BXbCyeLmUiL5DqvGIh5pnTrQpgjkDUbrdaPBuXlTTp\n1IS9\r\n=D/Sf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.3", "@webassemblyjs/wasm-gen": "1.3.3", "@webassemblyjs/wasm-parser": "1.3.3", "@webassemblyjs/helper-buffer": "1.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.3.3_1525852402695_0.42764433637292876", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.4.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b7304cae781616a4987bab2e1804c98d7e29cc1b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.4.0.tgz", "fileCount": 3, "integrity": "sha512-/j04xhjq6pbB01jVm2T0zSLPoHF7jcj/lbECE4RP7MyIyXs+9UCak27fKqt5bM4B1lbXt1SzKRZaM2q0ylDVhQ==", "signatures": [{"sig": "MEQCIFGZCnQ4grYWgNSGm6b+k6+kFUNRRmzcUwqoD6n/YiHPAiAWGvt+F0+XgsOGjYeLv2dOmjAeDEPsqkMvZB6dwg1ogw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wyeCRA9TVsSAnZWagAA0+cQAIRJuGi4mDc4D9IQYeSY\n/1Y2sMRSyJifXSUP5xu1FwiD3XxqKAJwxwEOosZvhqfE3KYC2v/8F19u0k5X\nRH+u3ZJrceATgp1B3A1chM1VMuprB4ncSELAvrhRe5TGMdLRrZREhP1MDZWS\nBzdL9Wd85fPXQInJseC4kWyp3oi4QH3pqPdiNWhYmZfN+pewSv7EonTRq7QY\nW6B19oJUSVHBgx29VKZNADw+qFOrel+AddS5KLobH+PrGchVTurMGYny2orN\n4v8otBkYZB1bAnh9FLgLwsX67mU7DgxshXUrzUUvac4RgjD939nrTUcvwwFQ\nbo7ap5fopXKyWOIUMz1BSE2W44GXl8hqaItjKqhpxMwn/c/hfYtvO8VMtSh7\n0O96jdcIEhRfrwYZGk8hdHUQgtnRvolFMxlC1g7HXsu2JPp7kOpaYsWJhSuv\nLRlOYhgPouRkynT0mBIuVxYE9hLkoDFexsTLjvUON2X8vK4zaWICtJSwgC5V\n/IFWxCj3pmfAlN2xZxwPQSqA8nt8DUzEgkbLl8ssG0SdnP49DUgiJ5kjzU6Y\nchDtcL6BfKdBJs/g+96hGYGe0a8+r00sV58Ux2T17zXAkkTYD0LhfgOD0wQa\nqbp3ivgPpS5G2zqQ0RSWSP79dp3j7MtI6eGESxsNFMN3XB29YoE2O5RecKdJ\nNTEZ\r\n=byuf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.0", "@webassemblyjs/wasm-gen": "1.4.0", "@webassemblyjs/wasm-parser": "1.4.0", "@webassemblyjs/helper-buffer": "1.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.4.0_1525877917896_0.9829928988078238", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.4.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e5a30ec5bbfb4fe94e83bf616d3a99182ba76655", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.4.1.tgz", "fileCount": 3, "integrity": "sha512-ZgjfhBseMKf0uKvl3iqpDhjlZtJeMKulm+jXjLQVy40pvwl8BTqoLfhC4sfI3XrRyQKWeM2fg7JO5znRzVR1qw==", "signatures": [{"sig": "MEYCIQC1fyAyKP1FDgf5D0Blo7sxJimtSKmvTLgcz2AiLt4IuwIhAKvVgLMkiw1gFp7HLNH9kbxeMPm0gANcvmrdAChDSGV7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9KE6CRA9TVsSAnZWagAAtYcP/itS4vKTvbAkh6tZQjtt\nOPf8A2n38Ljh/oTG9ko4VxuwYts65NgRbodcdHzg3IZhvU4J7TIVH7GCp7AC\ncRE7WbZ4iTFtMIohMaP48Wnih0lgw95Y9JGJbtGovEfQfAXC8dhIXmt9CdYT\nAGC/N9h0n9HhV1ycCtrTpXIguv+RvwtPkY6BMxQjzzUd93f/6y/xVYNFvpQv\nGw28NXO5iFzOsc4xORSbupmtznj/Vj/5IldX8nAbMlmXZSgxxt0rpJRkVS0z\nJBgd698KJxKx81k+bl1nO4J5WFSbdrHmNVytKv8ig2R0Vy6lPC1MZIdf5Vod\nC9uvwhbGf0jWDufPjIbYMLjSvRYPmuy5WWCfgsJ3D3JuQQ6xI29FOAf8Y4yu\nKS6WKK5xjmKCeEIaRE+P4utEZjcbMzzx9x1jbalYIkcbK+J1OTEd9HCh5pUE\nILl0MnWPEne1LCJ+IGuU87uz4gWL1puBCxs+F4eJvCvB1aYuVsOk1scEwZIh\ntENNi3mwthaoEOlop9c6a/MnTKw9VkmQY8JTlc213Lp8aMQO9/I814zwtD88\n7WGhowDTmeiHTW5vEDNRz4BitafBbEIO2VwtLJiYlutkqH3qsKXEPJpi0Jff\ncez6bc3nvtut35JJ67bxXdixulUJlvi0eRGytHPm51q3rsyZkzlieKPcisY1\niDVK\r\n=LcRn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.1", "@webassemblyjs/wasm-gen": "1.4.1", "@webassemblyjs/wasm-parser": "1.4.1", "@webassemblyjs/helper-buffer": "1.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.4.1_1525981497757_0.5123464654967758", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "@webassemblyjs/wasm-opt", "version": "1.4.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.4.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c44ad48e109aec197e3bf69875c54537d76ba2e9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.4.2.tgz", "fileCount": 3, "integrity": "sha512-pXq9vuG2DoNJ7BmFBeZG5yo/U1QlBLWEasvAyNhet5Zcye2PQDzqVHsW0Z2ifILL6ZMmjvi6bAURhaRgCvwFeQ==", "signatures": [{"sig": "MEQCIFCgPASq1jmDDozqQiH7w8tMIZq/KVT6va1WDpbR79gPAiAY0LCGIa2bX/8dZ25rTiy/6L4/lnEQqAaOGsiHHg7Jcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aT9CRA9TVsSAnZWagAAFfkP/iXCTkV8Alve2BpY2DJO\n0rx7H7mKJL9oOEiKa0a/LJLeOPQtCjanzbJJveT3rk6QDD1VNM0xwgBMzX/J\nY0c0yB5g5aUS5e097lr9JZanNirxGrkmOJprNPptFkBcehCX42PeJaPih3Ru\n8uzleFqdyLHq9HyNc8N3aTcHcoa2NTnX0wNiMXB1MhYRdKsgxTgzTLT60S4x\nXs0hfyCnJgWdRmdGhCiT+oO2qK/4AyOmo4vxQW7PRLevRz8pz0phNZS+wOgu\nVG/suQAh9GyjUk6aSlBIDl/bI+Nc2n/GNvMWpSVvvGL5t5QyCe4i1SJcLpMC\nrkJCV6QjzqNbmiRkNDiyxEuZqncL58O4s4X4IHS4lRuOq/n8v6G7LgzTQKOc\nT15Viyz6zk4nsRxnifdFdmXhuPu656iMROt4yuXZa6Fo2uprEF4B3dQAjZ1e\nfTBox+VZRw7Qf64cbl6AZ+voCUVqNtYXR6bfddComCZ+3Be0PL2+t35Dcfru\nbce3FLq44NtTdmKHnj8QNOrEM/niZLU0Nj4YQy/MSUZoLARcZyhAsJjlhHq5\nKDBlZG03RNXOostllutCpjA6guTfSpH4hIkbMjzSqohMeurQlB3y5T4sshxr\n2cTjHzYXL/a/iy1Egt8PICYBKVIK4xarlGBzVp8L/NUDW0odEgNGEY94l98f\n4lE6\r\n=oFe7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.2", "@webassemblyjs/wasm-gen": "1.4.2", "@webassemblyjs/wasm-parser": "1.4.2", "@webassemblyjs/helper-buffer": "1.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.4.2_1526047996822_0.5888245455788481", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "@webassemblyjs/wasm-opt", "version": "1.4.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.4.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "26c7a23bfb136aa405b1d3410e63408ec60894b8", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.4.3.tgz", "fileCount": 3, "integrity": "sha512-7Gp+nschuKiDuAL1xmp4Xz0rgEbxioFXw4nCFYEmy+ytynhBnTeGc9W9cB1XRu1w8pqRU2lbj2VBBA4cL5Z2Kw==", "signatures": [{"sig": "MEYCIQD7xwsbNy4u3GE83vl+pmCchcKF3oU+FvuQwX4VdnROpQIhANFsuBD0L1FU0jn/eZaax9Bs5tTWG7xw5KX3v6WGRWxt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rISCRA9TVsSAnZWagAArHwP/iFzRc/OVwIGPBk9KMU5\nE1xiYaktqldzIi/DEhLAsHpycOxu9ubhngPVswrIvD6PIRhoXMDHVpo0Y8TV\nfkM1U1G9X4eCzglV2at9BEpYDam1ozpAaXFLTX5HJsAfVMYGiH3cbnpbKDYa\nvs6CEM2DD0Zk40534jtwygC851eZHKB4y7iWikoZgaU3uNIPIXjzcK78n8m7\nz0uNrqO4CBke598syfQEzp0NX49z4fV4qp1A6not4vD4wa7+PIX7AcbceuR4\nxl6LuDfPORIm//oS5RQ7MNu0qvBQEgeWwv1eTudewLcDX3b3PNnihDUvuS5W\nwePAxBhGNGM7ws2I7fScHA9VF8606IuUoc2WzpzwvPwXW3udEYIELh3+cto8\nPsYuiBZlUEOrC1FD2+ya8vpS5s0y5iyKYiuRpQvL+pewGM4skXRPLVWr2/TF\ne+o/mo9E00vgUdakYI7CSjv3vSBeiXHaldHZO8I7o0ZvhclgdDiG/ht5lxHJ\nUirqLq2vxxptHVyFe3NkCIh1eocAE6u4yDq0CBcWfsZw9uhIlltwjsKMbULr\nXAZF3dH2azTEK/sppdHSxwVCqvWJRtsGArc/qQCRc/A4rCQaoKhvkowZutWN\n0rQ3kOacyCiRzhgs4OHY6R84e0iIo214zxzEFIrds/bopeBP1WL/mpsWvtua\n40Fp\r\n=HuKK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.4.3", "@webassemblyjs/wasm-gen": "1.4.3", "@webassemblyjs/wasm-parser": "1.4.3", "@webassemblyjs/helper-buffer": "1.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.4.3_1526116881722_0.5847031336371764", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3aee98a45e5db4e10e712a48fcdf02d07f36f518", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.0.tgz", "fileCount": 3, "integrity": "sha512-dveGIfJ1OqI0zmJMmjZSZM5cJySHLPkPFTpo8MQoQSTsAcAZaoVcoDaz0QXagSPbJbhY7xirisKn8vgaRIoSog==", "signatures": [{"sig": "MEUCIDeDcraUm4gWUmcxlBHuPl1P5BpC2dsbA7tDhwpQIs6kAiEA2NkW13OgxD1jBkNESj/YXRtNJHdEjUUuZBrHsTNAwbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b2dCRA9TVsSAnZWagAAqEEP/3PVTmdGt/qSDWf412S/\nUSfVjzq+LhRsSJLQ8A/kKgSNfvHviwMgLuhnOr7r0MWPhCxnkkITofPOtquh\nn0LeRplfhtI833lUy8quXwjYxnnxdCK3m63MgiH9CdGAIlKNx8shyQHQvgpX\nez9dk2Iv7evAwGMDfbQGpT9Sp1vrBP/ii1i1UruKU18oiU6jDGQskCsn9u2l\nB7Za5beyv5M24vL/ctpVV5ppErireMHEZwPLYu6bvJSaywRUHirPmuOq7IWA\nNY8SFrPQMoYp7XSXIHMk2f8BlJTMxPl2+u14QS7+B+xDgVaYmnt91Yq0dinN\nbbW16B014FQf4N/FWw+mdfGZqPd19qeatCFmDqUSDQwV7bKFEeyK/bZurxpl\nzz/U7uoLcU/KlXLEKlr32fqaqIHHdQaOyu10+WxxrjhWO3OM50PnyyRw4MRX\nbaeado97Kr/ZoRJwbecynX3IdkAp3D00iSMVrIGM3//QqOKcc79cC3pzh6kT\n/d3GPgeYtSP1tFNv9V9tQQnTSCwIfrXuGuqVpPYiq2iQB+ZKLSduso93XcQw\noYq2y8yHc1TpR3CXHWw60c5Z7bES+tqFXCjcKaTx1GaU8U2ogSK4hu2iTdFW\nJGWYa9tVONbzOG/hhhcgMeplmlG+VeTTv/WN3+RboBmdVhZMlPHO9Xv2+zlQ\nK6uI\r\n=wjst\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.0", "@webassemblyjs/wasm-gen": "1.5.0", "@webassemblyjs/wasm-parser": "1.5.0", "@webassemblyjs/helper-buffer": "1.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.0_1526316444837_0.1896896352124544", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "01f3e0f5ea89762e02a374a8d34a1b418b6066ee", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.1.tgz", "fileCount": 3, "integrity": "sha512-far2lY+XsmO9yW2qZ0U4XLQNn+wtQvj25ADSSxJw7yFMzh9Zgwp2KA6iNk66qFdBL1xobSj47dzHDxIgys5ydg==", "signatures": [{"sig": "MEYCIQCWkfx0V8HFaveNu5GgbTEZIAzTS68JkWhSgNgfM8PEIwIhAMWjT9Jw8C6V356WRUY9De4/229UD9SHd9ZpnFda/7pO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BVCCRA9TVsSAnZWagAAhDAP/jr4/ZbFMZZZb2/adRjx\nRQymCpWR99jgDy8SYa93Kt00zhczip/bwYZfjPAbcNuZ3/s9cTLcSGcV9bC7\nwSCxQlHU7hMGIRhea9QgcUcNnufHO5BlZT+mOV9vdwCH66NXpZtSe3ceVgIK\n54D8KryiMXKpdHKEZ/N+vLP+gGZEceJIVuf74j2zxWnFCWc7KO6HGAbtAZ/j\ndNpinmAzJjCzvvZBKuIJcD157xjY1KyK/tA0eVJ5gbRirnAGTGM0mD/XzOx+\nIrmllZvexPjGjW5M3IGy6px3H3yJZoINhxT3VnkvbAmCe5ink3McH7v2e3+O\na37syqdj+9n1Ck+2CIw29P0Cyn5qWO4yi04doxVPCX7gAYmAP8CJXy8iHRVb\nN+RagacOJJbqCJ144OsNbpO8iQNIWa1FeRQY4g+eRDRYQ/qvvXHafOlKrcRN\n7T6A9JnypAWJQX4QH2EAcs5LY14f0T0cxN3kwbmzS7DP2wtoj3Ff/hhsYqUN\n2UbjS7d7YuZtb6CYLh3L73Rsvdms34FxLmihfS33dAeClRraxx1bNtfUL6+F\nEvICo97rZXYY+61+IWK5UgjODTYYorKhqf6nL3zAYY8Z0q13EvZzdU03pblX\nlcK1BO++YKBpcFGVf99LmvgOERB8EGNy1mTGbtsi0pOWW1KYhaH9kw0gz4aK\nsDZs\r\n=Xp3p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.1", "@webassemblyjs/wasm-gen": "1.5.1", "@webassemblyjs/wasm-parser": "1.5.1", "@webassemblyjs/helper-buffer": "1.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.1_1526469954399_0.7045934688285118", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d90efd4eac5a81afe8d48d37753869529900e38d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.2.tgz", "fileCount": 3, "integrity": "sha512-BP4zavbwvpJVn6JdGX7P5SmF6B2agOZ1Cd2CJCLnaBJmMPhQHny80hRK/fOXMXtLf4KYxQswL6VE1vf46EjZbQ==", "signatures": [{"sig": "MEQCIDbbEg9gbuf7xplJ9VJfYzUp1sBWbOe8pvzRKDMosIksAiBJlpr6S/pKL+xzYy4WXEwOA9MQLCG9cPl9sNSKUojdOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XqxCRA9TVsSAnZWagAAS/0QAJynwUxoWBRp666M1xKS\nGwOtjuqoYE4I3DT72G2ZSzT1UEQ9SQsInGsKr9JtTuo06rwHecPygZIir1p0\nMubD/wePakTGnonKX8yspuzmlHZSj+BFk3urZHOHTQOAj/V2JBXIbjMEBzGh\nZuj0x/F024w8p1bpiYQ7oxK+XXKmCP4/45l2RGPS4ZbG5nANi1O9Qs5RTvM7\ntJ9xKGmTRmDJEzrhi7dH1Ni0LPOlSoAxIQyor8h6lKgMark4hegk9WSOEbXq\n4NiJrQxhYiNoTaG3fdrALZ54B3g6ehIbEo3D74AmG0j+JGflYXnH8bLdbTVG\n9KtGXw7m3VdjirNg3zm2llWbreJDliwUbnnytJBZAtaYP0ck/s/KmundUW8W\nS/0yGQkJl3mMlIZgUg6UnkvKDYwD4NeLMEWoZEz7lf19f44dPmU4hFtBONA/\n4QGQ8+XkU07+1wkA7qF9/HjJwfYd5twntM9VaB0fvkI1ZfTnm+/E7jByYoIH\n55OqmIEHHRB5Bqi91zn6CNSbK6qB3S0S60v/4GeygppoWzI5qbkZS0kIAwEl\nbGha4jAS9odZAQKL1i7z4nrxW6hTWPa6u8P+NDGkCs/xlWS7AJBQ5aXlRx2n\n7rX7t2GGItJG/7he5eqWz779Lbeexls6bn3Su1bP3ivDje8z0xOLpufZ2uRu\nQ8jp\r\n=1/FL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.2", "@webassemblyjs/wasm-gen": "1.5.2", "@webassemblyjs/wasm-parser": "1.5.2", "@webassemblyjs/helper-buffer": "1.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.2_1526561457164_0.37344907397057736", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9df6a434d53e328cd294a8698f734dea501088bf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.3.tgz", "fileCount": 3, "integrity": "sha512-WgvWMDtECWePuTcQ1BWcKqyomrUo8+glRqiSGR7IWOGtkdQejZ2tMCyV8qy7JQJ8HUUmZES0Sz35o6p3LC6Kng==", "signatures": [{"sig": "MEQCIEXmf+B0+hKiOi4dKlHoksIpY6Gco2pu+1MTstLib7x1AiA0VU+Lmquqm0A254Wmyh7EZ6LnHtwYnhManOuZNSb2Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqi5CRA9TVsSAnZWagAA9yoP/iaprFV6AHC7uH8EPrgF\nmJJWWZ/JvHrzaSr3RVOOx4xJ/cFaHiE44+PXOCYLCZjitoW4r496V84A5B4e\nm0M13oTFiiF3cm0boByyCmZTsbHpT9kOkBPfZsQfqW/f0qxAQK4SYdthQRtg\nnvwATcruSIKRWPhd9NGsvqkG6E+xXwkPzTc/YaPNJ+rYHEfZ1grXSBY8YH6M\nYZ0wTTQgsxFKJEq+CFYthcrHbW1zib8O0J8WwCjSsikz4SAKZwRYJGz5Jvmg\nXexm1BK00OPK/ZtwgGvZn9kLUvjb2fa1QSzpwqhUcKF/3JVHnHrDJOsdgmrF\nhJ5V5v/1hA4zI7c2MTXir20+gmztDU+oe17YQZP5DPL9XZTEtsmWCwCeaAkc\nCupb5J/myxCN2sToDQJM5e019rTz3kiclscGCRWmCISWqdS/trh0EopOXgoy\nR1vCLbIgAU4ALp/8AAYoJJOO4WYRNZbcM4trjfZaBGSjY/IBdZx/QuFsG+7H\n2HpJeS4eCbuT9VNo2VAxyn2cq/xON+MIHDNsngxzNMV6Wq2BlmYwRZEDKaCO\nN2Pxig+dXGuVqD8mvcrYLqvRRGg1D/2IItONs+KKIpL9+rDzMTRvPkY5yr3i\nEVQPrGk0nB8dsetg507mzjqDpQkXH9o6txT+xX/4ABiov/AaQ5bvRP+r1f70\nNw1n\r\n=wCeT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.3", "@webassemblyjs/wasm-gen": "1.5.3", "@webassemblyjs/wasm-parser": "1.5.3", "@webassemblyjs/helper-buffer": "1.5.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.3_1526900921259_0.20781125067388406", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8abc137664f3bca68cf1d20597fd755787d6a380", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.4.tgz", "fileCount": 3, "integrity": "sha512-RrW+mMt51Xxfjowp8IUWWKJJR4UwMJGE35mc6lWGq+10uBC+TOx33O8MkAE0GHK/ypDxXjkcAgvrqmjttDDf0g==", "signatures": [{"sig": "MEUCIGmPEZch4YnFiu3a2jJ3fUT7nBkrdwqdB+oYEsX17U2BAiEAyJQJC70pHMdukheCBfEe6395q61wt2cofbl+IBXGDiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsIxCRA9TVsSAnZWagAAawMQAJhsGllWNkN58KBHOcY9\nlda/L/tupHTFpcwfOVTXIRopNfrjmKFDKRfFnuTxl/Hd3nW1yQCBK8seCW9f\nCwaPfR8qVfVYSviK9GY3qhv/kR/+dLzvTx1xhOgu4XCGwzKzFHRCABDCNMbj\n+30S4cL4IIzu6rX3+Prkyo2ypGw4XlM5AuGaAKmDqk2NlmzmAhY6Rk51IcBo\nkkuz6/BtNfOThjcukasN1LGWeazf3x51/adK4z2Nay1g1NZhNzAgfzydHWo+\nB0OuJP0W3f89FbalJBXX7RgxLcFuYk+M81/ms3AoiUHjK6TmnsYQd3uURkli\nLT45XagKrgWqGXlMZYFXcmJn/kGiF6aIV/UypioL3ww2vgEQ969YJW2rGnyL\nlPoZa81abAvMHI+0cdntaDvV32Kr/FpinL0R6fj5wISVaopPg5rV+nDRMcQT\nRnftcuMi5fG5MSIstqU4Y5sAUrggE8AA78yfmSm8LvrAezfsT0ZZfIi60246\nylxSafO/A/FhQigkZSMULPdt0QptkSjytXDk4sJfjTVi42+9kZjfXd7FKPQr\nb7kZrzrufHv7k7vgWvUtMIVd/CuupQM0fs1O4uaPMzeRt/em4BedgI4lr1On\nfvaHOjBWIW06KOMdin8M7hIlYP83/IxLA/cfpmJEuLj8Hn1Gvfo/kS3N6CwN\njXgr\r\n=UOYI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.4", "@webassemblyjs/wasm-gen": "1.5.4", "@webassemblyjs/wasm-parser": "1.5.4", "@webassemblyjs/helper-buffer": "1.5.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.4_1526907440127_0.7585602440078896", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "678dea4164c31d2731e8eeaf63efde6bf1fc46b0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.5.tgz", "fileCount": 3, "integrity": "sha512-lgBZiTU0Hh2klNmCV8HHf7DE6FtNAW9im0MZfWbplltdQ6co05QNKumMDsoBuIC1Q/xcOeEGnI7ELy3qcLfKgA==", "signatures": [{"sig": "MEUCIQC0VnP0/G+/ofZ/PNlVqifx52TQXnqwAS0s3eFF6cX9NwIgCBYwgRMPJfuQhEJyIAsej2wURWL1Zh9pV3+h/LvuAJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnGKCRA9TVsSAnZWagAAGgsP/1pSEKtDzWKJJANoukMv\n3bWBi/qAtqB2IJTKnIBd9ntalrwCV2oNSuOl7r4QKzvkcfBZdMy18GS9XD7f\nDHDp0XY0W1KSK7lCcUWdSYxdEqwUSClxk4qVFo2Nj8VwZafFmLIcmJClQxtS\nQpb0vtUY7G0EOwGzIAneOL4+28T58a1oaNDHALzhli6C/797a9ZaKTGACVn9\nZRhqiMFyKP9HXm9QYJfegmJGb8nOA/gzD7KJ2hAF7CcDh9EpkHJH5YK6M/q+\nayGqvy6paE0nQo7LTbLTPtCgxqOQmUkvkFgRvXjeRr8trnYmHYrKM54e+6yT\n8gymrpGMvidikq9EdFZILVaGxA+qNUnTlqQhjHiA29cpquotfTYUGE3ZuQ/T\nIQ+8++H/CpsLmXGn1Eps9qPG4/CGmhHn7DxVkif60oR6PmZaYttPL5+tATTv\nz4fEe49wGUdC/NVwI+ZzeWiO7sP/x9cEEWhX6Mo9P63ACdNyePXygp/8SsBW\nw9Mvwddesv+gFFjrLZ7vuTrKB0IQ0Y/lDs1Tdla0CBEhQcfpXNiZ3Lj4T6lf\np/51wIZ4hlisB5Mc1UwlvXzeYcxLGrnhgsFmmtjIYfOW/nX20HP7+rYbCrBO\nqXSPxhMSEQrtUO/dhiTh9UQiHjeoSbPPMfoWYKPCYxHn1t1zgW9H8aMLt9KB\nCZBw\r\n=Odui\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.5", "@webassemblyjs/wasm-gen": "1.5.5", "@webassemblyjs/wasm-parser": "1.5.5", "@webassemblyjs/helper-buffer": "1.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.5_1527148938012_0.9607979472713566", "host": "s3://npm-registry-packages"}}, "1.5.6": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c7c6a1965c03b6a2f78bf0360122c5d763a55096", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.6.tgz", "fileCount": 3, "integrity": "sha512-hPKfzJP8pVFzTRGiU3i84O8c6/u83K3CEHKhjHYSayzhLnEWs+3MHNb9frdfoC2ydTMSQ3/VPKeWw7xH3RDKxA==", "signatures": [{"sig": "MEYCIQCIwflgmwWasoAAiJgPLfAFl163zzGMXB3tvp/6PrZ2+AIhANwxMM0ZlD7tr7bE+TLghdc55XhAtKnvhqcGu2Oq/ETs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsqJCRA9TVsSAnZWagAAqnYP/1/1/KDMNR6zdBDCiFAb\nGZ4fOIE8P2D6j4yu+sW9lt0loJZg0w9bPv/XSYDut6gUPlNjN3J1WR5Bhgc3\nPmU4BwrBtB78hj8d9UONTqv2xWsBeJIKkMCn6O/xZmMhQ1yKyBU7B6yUOY7O\nXc1kaxifYwjS7WSqYP/89czK5saH6a2d3VmCeVH3FOvbWEjUeItXvpBagsMi\noBIsPlFmWcooMdawdJ1Y3SI1YkVvZbk3l4z1ZgVSxeaVZ54jaC6twl6AwApX\nnMU5V+wI8B6eaRPg+fLcYDqUDpDuCNgM1trwGdpLr/Qs+y0pdBwHzOCqWxW2\nn0R/jRxNQc/NfnoA5DTG1Q5hRhu8m97X3BXo0fbSznqSxxhqxz+OcgxBB4F3\n3hbODEVpaHZkgi6XuD0yB91IveY6EEVF7BcB+/jNI6vfggTv9hFa/ZNqHo12\npkWd6oE9dcQP81s4cCbI13FpBkoicjhEXlU9xadSvW+nk9cSoEcJU3rdIXF2\nopll4WSFewOg1TEYbxVsJqyZNe/nFPfALE+anp16TxfWm3OzjnVr8dQl5LUp\n4SDSCg2sjhisneWLd2tG44xWUIcIKIqENXAfDHBjSCNzWtDErfDEm3bJB6Cz\nBdvjEdFTv5vfA5fCu+T7muLNPynCFaJXIk86vPkATpf+jc0Wr6G2bHtHUeL5\n/0Xq\r\n=BSaw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.6", "@webassemblyjs/wasm-gen": "1.5.6", "@webassemblyjs/wasm-parser": "1.5.6", "@webassemblyjs/helper-buffer": "1.5.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.6_1527171720899_0.5947683324284496", "host": "s3://npm-registry-packages"}}, "1.5.7": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c0b8ff4241549aca63f72284cc945c8556d5702e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.7.tgz", "fileCount": 3, "integrity": "sha512-94OlpuaAeVdy/xOe5U89kor9BAg9EYQlKc3EKASRGxRcq/JUOms/wDkr93X/NA2V2sDI1hNPHiN/hMtIsca4fg==", "signatures": [{"sig": "MEUCIQCv+RMFC3yydD9LuQH+kRofX1r45G5MLIARDo1+yeupWwIgOEbqd6e0jwm5HtFqjQMzKbdqVu7VEsel87M/ed8cV7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAeVCRA9TVsSAnZWagAA0n8P+wVHXa6hOPk/vd6gzkTQ\nGonkSSn9eJaHCGYPnX4UzCpe4xUPxv/1n89XXEIWbP+HW0YCXCva5dMjSzeA\nKmxBnqD6OkC5gLe2smSCJhpdLa5lNAM2oZtgCmq8MbM8mOK4pyvP8A0Kiqkv\ntDLgnq/aBpEjIoNUVwC9OEk7uxBbRWT28HB59ZNn7Com8PLS5MKcY3/H+Xa7\nHBIlE/KOXgg6gjaucxUi3WxUEKKQ2u7jXA+QIQsGAZUi/hdYIBCRobJPomom\nLQ27TOfm+l/meaDGgtCRLFFwePQanEuGmhbyI3JGWjgh39G277M3IoN/iDf5\nqRYKnNbfHh1L3xaYPy/h8bVB473qOXG2IeX50Ki1Jo+GWKzzPsn6edWvaJiJ\nBqRPqOwlxQl1NQMfd+64mhIX/N2WpC8U7xqUqLzTlZwEvhPyY3DJAvKFXGAJ\nHwB3aEYQBYXUqKOzuM+NdYwuKXUpW97jHL/Koy5dZ6/WuOvWKe+65tIlZM44\nGXkkvzuT2Sp9ODf5Lp/UTAXCZU+yCWEJBj8cYBZEHDC7t2eDYiXjLVnXwuzQ\nnlXaxeqFHBmEd63J+oJjjB2QaUyxIjnbdsRuXiVBN5XfDKGnIJYVvgKUOuh0\nxOhy57VdKFR5EmSq7bhk+9WSiAh55v2t2zayITRzK1GfSqqvOnLM7HvqNxPC\nIpax\r\n=swZl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.7", "@webassemblyjs/wasm-gen": "1.5.7", "@webassemblyjs/wasm-parser": "1.5.7", "@webassemblyjs/helper-buffer": "1.5.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.7_1527252884774_0.31043301432818304", "host": "s3://npm-registry-packages"}}, "1.5.8": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a3a0d00d98dee0f3cf2ae41084eb62715a39242c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.8.tgz", "fileCount": 3, "integrity": "sha512-XBQfQnJen5uMwIT9jrbd4lqtoU+8dBFoC8Qb5Up30b7ShfVNDzxTHSySYgaOVH+1uGiQGNg79VSz6jabQ6eHlw==", "signatures": [{"sig": "MEYCIQDUtC1HIGIGwJzYgUmy0z8YCtoFAup4I27AybLtAqBgtwIhALHYDzlPhxegQpZ/Bt9bOEVYaYQRP+bJWN9ijImBar/Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/rSCRA9TVsSAnZWagAAx4gP/R7MzUb/U5rzf9SFOvkh\nhrHX4DNZZcBpSy8nZtgcbcdysDdDxQMy/LRqrta6pCdBiOHRr8/5hyhn2xLj\nrSewnNbdnTcUd4EbopU2TOjsqHJnX6hleYspJW5V+4a5oVJ7aYU7+eDAUzMb\nSjgWoKbZSkRsGbtGknhXvQnZMrEfaKK93xwGj0Da8SWHjvHdmrNbJnOwRpTL\ntjB3y8nc8gWSkdYlFW7QMAGJfdTYH2XOATGMRAUpSd+1RlQKBT2r0ZS2Bkmp\n4FscqlALKTK8WLnPgQ4zmYaZ9rh2V+pPKWZwRGqyqJO/I0JyzCNXfI3bQu4x\nmu9I/iAcCLj5LYBVFcV2spHeiONO37oBwDSyuhKTZ/61+X4vbYCNACT+qGag\n3YU542DjoNzvwI3Ride7WXt+W63eGqjI7DQJwNZa16+WF+s0WxQethx3w8dS\nCC5VjMu/wJ0pxmTLbw/3TbvY4xdiTmwb1A8n/3rOaMz+gCI9R0ofkflOu+ZK\nfupjLU14pEvjvtWh93emEL+Jtj4m2WEQ+RmyC99BClBMay2Bl8cR2gTdnTex\nGZkofUSnUB2PvqELWNkHBgaDs2fgrWNprCY+OXzr5V0tUmhrcxUPNLQGoDzA\nFJQ3CMWVhAU8UJp12BBYwZUKbH/eq4UrkB+4jF/yE0IrcIDyd0PjsBiqBle2\nkqgX\r\n=Uu/G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.8", "@webassemblyjs/wasm-gen": "1.5.8", "@webassemblyjs/wasm-parser": "1.5.8", "@webassemblyjs/helper-buffer": "1.5.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.8_1527511762504_0.27163594095003085", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ccac17c41a044c167bc95d3e8645cf889a137ce5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.9.tgz", "fileCount": 3, "integrity": "sha512-oQm84US3e36dPq5bOeybVKA2ZyzeWR4fereg9kJa0Y9XLKxHwlsBa2kFyNXwZNrhMP33iyXAW+ym7om1zPZeAg==", "signatures": [{"sig": "MEUCIBpDlRyvnlRDX4SzY/xO/WLQMV/Gj8Fots383EF4BSEiAiEAvB02Vh5V4XOgNl/x793vr0ZwpqVFJdCTBv2ztnZr0+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVIRCRA9TVsSAnZWagAAKfEP/R+1OgHiZEhzukF2/LVH\nYr06UlSe+MDs20dOsl0wepOIi6ydWh7bhWkBQeJn2zs9OX2bUk66yhK5rQWv\nbBL2wrLYonTEHpSETc/OQGV+eNW7/lSc+gfonyk3GzcLBz1xf6VvOMB/izdk\nhEGJiTJy9t138HHmSML5C3uEeiCXr3MBofaK5jly0rHgOit/pax8z/HVRAQ2\nkxZgMOxBEbqbCPZ6T0nM/a+DSVKpgJ2nc95qLsbp+hjWLMqnkU44IZv5V2KW\nRAzwn4rqreY+aHB/crhNrnbGTBBbjlwMGolvDDovmVs/gcJo2oHs1NJXAqKZ\nhWH5sX+z7d+O5po9Xo3ETINUMf1HQu6TtA1eXTIpw0babA/9eX9ky+sA4Tpq\nZ5W6R0Aok8+rbTCu4daKyF6RU8HvpuZAtfHaEgZvTHDhzO/7QRgpJuP2Jyn7\nhAANyUODW2T1yPTx+Vq1y4fOkHTrlL1Kh8/kKO5oxg/V/6Qq2DTEDg1Bbmi8\ngCYr0c1wFF4ea5hmXYtjh8HXvHlb3FLOiAzflP1YrHuxS/L9/MSvvIYyWfZK\ndZ07+4wb6zKgZ1T3FCxOJSiPgdAYBe06OuAIDFonDQZjGYjms9to6NzewIHC\nO2YnXDqsOEY2gUr7H6S7nDv86OFAwPja7wupyARfzAW5qUyXrYU8rJiqhZl3\n2LkQ\r\n=VH91\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.9", "@webassemblyjs/wasm-gen": "1.5.9", "@webassemblyjs/wasm-parser": "1.5.9", "@webassemblyjs/helper-buffer": "1.5.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.9_1527599633397_0.36363987781282736", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "569e45ab1b2bf0a7706cdf6d1b51d1188e9e4c7b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.10.tgz", "fileCount": 3, "integrity": "sha512-1A1rVPa1URgjCmEVZupRgrrbqwfCh6hJVkogK22JNygS+wn1gg1jgjN82Zd3NDhm738TwY61936n3y25GC+mfQ==", "signatures": [{"sig": "MEQCIEemnpQNRLLagh6e26A89LQei2Vv2sm3/oYboog9MioNAiA/R2c+ivni2w03aOY5PdczSgY9i3MqHYKTqUqwI4QdTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUhbCRA9TVsSAnZWagAA3QgP/RYWo6a53a5D/1ay/Kxy\nQJrEinC1nlr24Z773IJyqZk05Fvd3cNQopdd/qmmjLL9RDm5SRLLuTub4w0r\n8M75hUdluqUlhPL1l0OqTHCdBObhbHwtI0bc0ERDC0zVO4QqsgCXTHTLCPDz\nCaJ9sOeJSLzOh/oXtek0Ewm7/tnq7qIQJbmhu5PkckBhcmQbSF4XMJvBt42F\nRrhgeJg7UUUuBqZZfRKzXZ3BDS/p3/M+yk0GvHeTGw93pIf5QOD2PeBBRPBu\n+qDikMBmZ+FJfhzX+LbRzwxJVpcDtNgC7GvvsAdorB6+7d2IQSxRDn009aS6\nQ/LeH4/TaWMCzZ2DBahYODme5+mZSdmOW7QFAW67wq6D8qVRl36fD2ABD5vS\n8BIhWBS/87lt8LtrujJS3h3Jr1XxJwy8TqUTyro/dZzwq2XJjnM+MRUsBvL7\nFr1bR1Oo0VJyu87s0gepWggVlAYqKrcfUUGNfa+0xzTCUGkR1mIeoYnLRWXZ\napXYoyT9O25Hfe2J04YVFB5FpHlXcpUlWuiOq8P2hk9e+KDStfrI5HPxkJLA\nOyxT69DcStty8ycb0SVF8wUQ0Or4aIySh02dgWpPtIpUKWFpYWoi5Tfc1ERH\nTCH9pbZ3QUlbrBZ+tGU2mTHMIkzFB80CB+fXCeO/4fOTzKdZGgbeH1lfaaGn\nmwa6\r\n=sQpO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.10", "@webassemblyjs/wasm-gen": "1.5.10", "@webassemblyjs/wasm-parser": "1.5.10", "@webassemblyjs/helper-buffer": "1.5.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.10_1527859290704_0.8661737577196116", "host": "s3://npm-registry-packages"}}, "1.5.11": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "276cd1344588cf7048b5d142bd51e5d9984c052b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.11.tgz", "fileCount": 3, "integrity": "sha512-LsX35jW769z/AsVcVBviDUCMUyvq3Z7TPN8/kZZGhb3m7h9TA/ozeUKynz4GX0zdmZrIb5MdRQTwnTFG8/wUPA==", "signatures": [{"sig": "MEQCICJ0wIKQ9y3QxM5dh88GLLm2XhPln2lWzbuQLPOGvFDHAiAOTfOwPtYvESwGWAiSzBzZdIWrbO+rXZ/xQNoqxf/MFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6JNCRA9TVsSAnZWagAATFQP/jPzT8NHkv0rWa6No2iB\ngSEL4V01py4m+oVhxnEZd+Pt4p1tWBEGaepSr/NFgm4PAYfdwMQEOlIIefzc\nS+hLR2cGM3Vb/pr6Iesy/nxXddOxub6RVQ2sSMaOcsYfP5HxIkqmN5FeZ0yS\naJLWQKdBHouC/rvFMpIGB3MTdbKZUjItNX53dOIKIM3tCEb4gkkzkMyxc2VR\n5+Yn7ZqgmDHbN8If7FlUTkZ9QCcR0sfyfFiSkAEm501tyzhR92swtI/vIRRS\njjGwgxI6pH/NOHvIel5QKMeRy3nx0NRyBvSVVbb2b9FFuvoHcK+Lz3FReS5n\nyx4/1rcHMijXJk3B6Ce4QUYtsEsW6zgvGlnVDboFDi7NeW3L6wn7OGxPVpou\nRGG+H7dtbJ3lgGBwTJEY19fxD2pGTN2+qE9t37q0WR+5G2FbL97q/Z9YIbO/\n4pln4aI2fCVg9pqiaW6LIVluWj7upbtCrh4C9gx3Vgca/Ix9ap0ihZcCMnTX\nj/RdWImOp7SA83P3VFNyN0P8CtsjsWksgkQzIwlqbqJUK4KqCgl4ohiAGgzc\nxfE0wPZmLS7XWWO7wjL9rAhWV90v/mdHhGoNIO+oUWb8NSIBkYWG1xVhS9F9\nuCAHm2QdTGdwVDKyfmFQW4eVBKyrqZdC5rePr4q0hQl+9FZWrFYdv/e+ohQV\n+v40\r\n=pAWv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.11", "@webassemblyjs/wasm-gen": "1.5.11", "@webassemblyjs/wasm-parser": "1.5.11", "@webassemblyjs/helper-buffer": "1.5.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.11_1528275533205_0.6010815304000463", "host": "s3://npm-registry-packages"}}, "1.5.12": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.12", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "bd758a8bc670f585ff1ae85f84095a9e0229cbc9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.12.tgz", "fileCount": 3, "integrity": "sha512-LBwG5KPA9u/uigZVyTsDpS3CVxx3AePCnTItVL+OPkRCp5LqmLsOp4a3/c5CQE0Lecm0Ss9hjUTDcbYFZkXlfQ==", "signatures": [{"sig": "MEUCIQCgqkvwKrqU9Ro2DP55maPDrMHU+22g82JvA4c5lFsCFgIgG7zomFrhPdh4mwG3GjwWq62wQTzmnLWFll8Z7kCsgjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPoeCRA9TVsSAnZWagAA2n4P/1Pw40V4+SmHC73aH8pC\n97NTL6A9t0bwoJg9NeU/ezwmY1zqk5/jFaA6NHPxrPHfRbwveV1K2OcrTVQh\n6CrBCVimSBFASVqBtz6h0KShXM9vneOyuhb/xx2Rcp5TxshO2+Tp8PaKcR/p\noRu3A5el679glgyYduV43S7J6BQKV51mKrihhpLxVJkRFD52Lne/eZs3m8ZF\n6MMutcpGnmDqSiccOoyiWTEwefXzYl9bMfgl0xvx6EIL0WKiEWF2MaVivca2\nm7/Wa2y2MyZ6txsFN339zQ8Fka7o8/fiujE0O+0gCHGyenHUFRW49F1pkYvS\ncQnchspMVrfR6/ItCIVRiTFxmLGllNB4N+ze+sCzwpGz6EZrKBWAhqIJ8bri\n5HegUkY/gsOZ3SCYYNgjEhomIQTj4HtSfgEjylE5k9Tbsf0+U80FRUnQlWaS\naI+d6eQAR1hai1aZfeOcXL0igdEjLcIMx04jRr3jhB83qA/Ies/9X+xhEUqP\n+lp7kz7X9ULy84u3Kw6/A7iesh4sffnAujsxzYet5IwCPnUuq7zODtKhyJGT\nKshnfwptE1EqT54Ha1o2I+LZWX5UimtvnzszZMr/cEY13CcyVEjiBW+SGH79\n46LphFoK0uBerDjeRGHqy3D9KB4Q7fseVzHIk+pL1D8+l/TD3dYvrJcSND6x\nsbvK\r\n=h8CD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.12", "@webassemblyjs/wasm-gen": "1.5.12", "@webassemblyjs/wasm-parser": "1.5.12", "@webassemblyjs/helper-buffer": "1.5.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.12_1528363550167_0.5859208658923958", "host": "s3://npm-registry-packages"}}, "1.5.13": {"name": "@webassemblyjs/wasm-opt", "version": "1.5.13", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.5.13", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "147aad7717a7ee4211c36b21a5f4c30dddf33138", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.5.13.tgz", "fileCount": 3, "integrity": "sha512-IkXSkgzVhQ0QYAdIayuCWMmXSYx0dHGU8Ah/AxJf1gBvstMWVnzJnBwLsXLyD87VSBIcsqkmZ28dVb0mOC3oBg==", "signatures": [{"sig": "MEYCIQC0rL8U282LRcS0LHoJSw/3xtmQXyEmMRLSl6UlNFqCFwIhAJlMOMe+qUNHu5dqzKo4n6Y/BuhyMgKtLn2UpA2eXWvb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lcCRA9TVsSAnZWagAAcHwP/1OJGAJc6o7PxTAsmcX/\nPGhyPjUWGbtN2iB7V2DCTMipzXpaenmh1nhVFpStzxE3mbOuNljJspUOKQ06\nwXNLMjfiLYlVvqOnWjzWecqhSmXdX3iNJqV/g7eXPAE/I1zBOxMOg0oMxvdn\ne2xQpbTHxzflfIqQw7b3wO3+dz6XYLfuntPH/mETGVGi9vZduFIO/5CTtWfp\nOpW2SWiHPUkXHEIjadYpeNfu3JwIK8BU7Qq6mHJIlNIWgY18eNZxlHTnRqth\nRpBt/OwK1+BqZ2D2+IovV+BVyLBbZ3WN5lzUBwrrTHjhRRtV0IV/4NDhPiMR\ntM2FZXcmaZPpOS4+SpZHLvPyC/+4EUDWbeQov4UtkFhyDrRD+IY6KuS9dQmu\nuTXWaA6R4p1nT4eXiNz6J6kmSUKNiM8nlICAFLo4sluXkaaLDJ+fiPUIXyNv\nDb5DuDozGKTWynVg2IkYhzEqE3fvkrqhnTUwnyl+7AFdngNe6WJKo3yQRO7B\ncFEWrOfRAGATx9/y4M7mwhwyg8JhpnX5GRrGw8FOt+NTuNweUFmsfghr2rO0\neJlKg7RQsIdiHx/bCFOA1is67Tkq68cjtjpvI7+Dkdqowd/dgag1wZoUUmJR\nR4LC6tbBO2pyQ+MaUlxcCmG0WnEahWRXd/eiyHOzGzKezs7MG9yKktEO1xzY\n2yKQ\r\n=VFGV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.5.13", "@webassemblyjs/wasm-gen": "1.5.13", "@webassemblyjs/wasm-parser": "1.5.13", "@webassemblyjs/helper-buffer": "1.5.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.5.13_1530366300800_0.7722584325255653", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.6.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9d5a13c58cfe98086285cfc9d6df89a90e02765b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.6.0.tgz", "fileCount": 3, "integrity": "sha512-hxT/W7W91987S7PG+pk22YLxvDYiskHLafy/rvoScoSzDfeqG81JSFsG+P1hV7jQOMaC4I8a0goqt+kSUw905w==", "signatures": [{"sig": "MEUCIBw1RNt0HA0rplWnd+jNgTN/T6QaqkBRdeYRZaBZf3DnAiEAh3D8BqDzv2SiKPk7b5nZsPgZE5NOETWa0fJ0lxsAzLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/nCRA9TVsSAnZWagAATxcP/0xUyqQXxZb+6PMKASgd\nxlACpxkMYBMe2yvdwwPhTDZ0aRZYXfyeZhfp/dpJD7h1/n3aFliVVzewNWAC\nBUmabb+ixZiOenZGcZURWxhhcuJCRCRJOFiO4Op+t57jabCiM+m2qOs/6sAQ\nwAxTDk3oK4ByeJa8u5sJsNRKDOiBqUlZks3ojpBS+zAmHLkH/PuwJdD9AlWr\nM5YNKtAMX1jcIWWT5rB1remllIMwfn5Sd5+/jkcOmjPupJ2S8K0Vu+M8EeNS\nHou/iBe5Kayl1R/EDjnL9dgY9qS6xEiLfTaY3dpamvGWBpGbCvC+jTJE9FrU\nvaZxDht3D0m9pUGqCVXFpOfvk/TuP9RGIKEiHzZd1e0FW8gqsi4NC/fTcQpZ\nJ5eTU6JjgIkAhm9DGc0/nIr+A4s3yGwU1afT1u3vjwh8uQfIxuOwYbEEx0ru\nbhalXFocvRuRpr697kTNEaqBZrXfIkhIX+p7lrNlUgzw2ElHWi5/zA/E+OAa\nSvWXmTzdfx8VFsFBsZLQ58z8AP89/7D1s4ooAFYcxrsdnbaV1xFxFaLyNZB4\nhELSde3TCIYC2GfN6NA/maY+DxgKL0xad1fYyikH13xG4prol9jHJIQlwi4K\nOMJ2xkAAsmh8d9J3K7ckfV5Ta3lNXEkCxo8jLn8oXK65tD4Krn9k/19WtjQC\nfgVo\r\n=WQZH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.6.0", "@webassemblyjs/wasm-gen": "1.6.0", "@webassemblyjs/wasm-parser": "1.6.0", "@webassemblyjs/helper-buffer": "1.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.6.0_1531731943724_0.4970855936507994", "host": "s3://npm-registry-packages"}}, "1.7.0-0": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.0-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.0-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "191b7400fdfd845f5fd5ca383fc4c3289df4e82b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.0-0.tgz", "fileCount": 5, "integrity": "sha512-4HRi1vkQQRBT8mk2F4xGEYA/H0J2BcW+ddaOoUNF3eyX1P1ErOI8oiSFuQsRtrPjYj0//ScNq9G8c3oYnm6/LQ==", "signatures": [{"sig": "MEQCIDYNP60IVIAt5eMlS0eIPgaxv3OsEVAHXsNw/56gJYKGAiAyKjBQlFfAWxoSVpo/DCoSMkPoqvnnvkbBWvKLCwdTrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzl4CRA9TVsSAnZWagAAnkkP/25TUTyUqhTRJpyOoiSS\nc3ev2hd4Yc/5kYu8tKBkUkgm9WwfrX1cJ2eUEDoCCqZZAMFBwpdzsZ3KZPj/\nSjq8qOm1ZA/z1/5N6Rz7B1VW+UmsSsPVCEjzowb5qSWw8NmjPIyyEnBFjduU\nMqwb2tQT9r1y0Qsw75eaxZWiFiPXT3ta+SSDaTvEpWnjvKCrxidmjohhmPpJ\njJOSHuP1oHNjFWvJAUiBParFLXR8DFDqOzvS9/uiJm9G0BlJ0kC/fWcYTHWa\nCZ1JXfH/qqXXKth2chWLxUkRB1CdE1tNgp7+MevCOHYPQmUlYgiNqgWpps2q\ndXSNCJrfYKYO7658mvsCC6CNgHCXk8R9UPM9vyLbIX4ruvSayZybneqBKY7g\n5SABiXY2h8rYaA2AuzI7gbkXPLcrNdIunNpZccyt5dmGunjCMbNeWYsCWRbA\nU2olCvHkscKp7Ylz7Ev6j97KDuMe5CPjz0U6eoSUNSdPqr1fnfurpJFoSp2x\ndxEHRE/FARVVJw+UXOnEueTsX5tejNRx9+bHUnjHk8gl7lcKuLFmxYoFyAPE\nWUrbmdKdS9vPHOXWeiXPu2aytDVAgBVDEqWQw18/6CDpnoY3jb74PnPaOvJo\nvZs2Qhp431pqwNNw4vWt4K92f04onLEMPqrvwAgx+MX4cvTwYDthO1jXQUsa\nPkOL\r\n=xYJ/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.7.0-0", "@webassemblyjs/wasm-gen": "1.7.0-0", "@webassemblyjs/wasm-parser": "1.7.0-0", "@webassemblyjs/helper-buffer": "1.7.0-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.0-0_1531918712247_0.24915049021140012", "host": "s3://npm-registry-packages"}}, "1.7.1-0": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.1-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.1-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1f1df516aa223b91fdc3a75946ff2205b449ac10", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.1-0.tgz", "fileCount": 5, "integrity": "sha512-iwWAOicvdIWt5rtepnO81IXtqmybEGUlAkFfT504szR3bDPuIBLUgktVl7zXAj/BiAQsdyYwcwiP34Yq35CU7g==", "signatures": [{"sig": "MEUCICMAeDXGhJqu699wYy16aZON25DgI+7ceFxlKf2w+aFMAiEA89E8i6FZHjORa7EFZoNmqSsFBWgu0kFse+aPhLmdiNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzweCRA9TVsSAnZWagAAUOgP/Ax6w5Wt9KCJwEMSB2t4\ntFNvIdNehqIGqlhWmiqBrGElzODjhtqhbJTAjfM2P8Z1bzLUYcD3xd8ZBPJ2\nrFIX7HLQjLNfrogoxSyVtkjqiwblSyli6FxTA6W7WqFwlbJ+dNQ3CMQkOGE/\nkqBxJhxrIEri7vCGreULqUJbxT9YoyYf8Qn7puXj8dEVMl7cxj7d2JqErkgK\nntT0JBQ1J+FY2P0XelhFweyt8v4uKGwXbS7ssEpxwehm5QXupiTGu7rvhPkh\n3D6dVJN4HpQcT57rB/9DUEN8k4m64EHK10txowU0DWdsGrCGhMfo0DIoSnVX\nlf9hLlh2Oeomtd/WMF6W/Y9GdXHg/s2KzVkbe7G/5KXsqe8Go7HRIraleJTq\nzC5C8k4QRZVzfBGhb+vpZv9vxPttFBqbNXlT7kQCxeUqJHlaQwNLJ0EIE6Sh\nCODmJ+BT75FfoWICSyaJRXiprXmsW6lGPy9+sw32EuCrJSHr1HanB8Adaq+l\nMAr04KzLiqaeJr+mJJCbd9x6SxNcprqzOHdbnj/3e7ThXInVe/m63RBJj56k\nbyVq2HPSCbKPNPlS0yeaijo+W4lNB9/EuAaugLHtlC3yr3TONnIB5zdIoJf+\nWROuLgl2LTL+HJjUqxXRX3w8YKKMIaBSNYxL96CoMgVF7u8WSPNfAgKnInEU\nlvnE\r\n=cREu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.7.1-0", "@webassemblyjs/wasm-gen": "1.7.1-0", "@webassemblyjs/wasm-parser": "1.7.1-0", "@webassemblyjs/helper-buffer": "1.7.1-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.1-0_1531919390632_0.07423478486423862", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.6.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "83825345055b24de3cc8f39fdc8c9699597e6d41", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.6.1.tgz", "fileCount": 3, "integrity": "sha512-RyQJWG/RmVX6G9jS1Qg2JTdUvfSVrwEklhfOIhC8b2wpXh9Ht+YCCYHlonfd2RhPNfhHKknxYP3iq83Ay+xM7A==", "signatures": [{"sig": "MEYCIQD5C7w5M08MrlNbGoFXf6RlqyUATsP/fwbl4J993xzL4gIhAL8ScmHxx1tDYRTQRky03BdsHKpVDou3r8uCRV3jrtoP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4780, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0XQCRA9TVsSAnZWagAAG6AP/iHjm0d2EGIZnzZWgxq7\nqbPwUPG25iINWb6CgRIKb9m1P0ZJhlH8gM0yFrzBkXIVZuzT7sICNDzn0gcR\nlblDDp61n+3ACWf6qFHGG5VB4kMbhG3bN/UQH4jyuQMDeYXe7+39g3Vx2omP\nXwdvAHpVxRNwsJuahH4G9oqXUWetDU3VKM9wDSx/uBu7HJF0JbqmfvCxC3Vf\n00Y4zhYFWQ37UJ8/I71qBQ0d6nRGcU6Anrm0Gg0kGtFmVG26K3QQda9M4TYt\n7z57ICBtKEIVy/oM3iXhKyeZIi2CuA/ikq/OklTBOcoO2JXkLe2cp7qFw8qb\n4IkNf28EGZKhi3UiCsptyjBV4tWGB57RRDD4GgYDUjHkuUu4iNOpf+pUTZOr\nezMVF86fUfHBQnuqRivHQHziRk+yLqxCctDjahwF1YK/UdDi6e3LXtUXSw6C\nAZzavJiiWWemLqBWlMWIEvM8jdp5gLs0NVSl/5Ul3aXr+71C2RxtXXzQLztZ\ndh2LvQl8rwzCnzQe4YjT4aUryVmY9oBLw1gXRBbRZ6p6ElZPh+Lb8U+9tlAd\nRUAcYXUpHvwpB86/TG6TCpEv5HiPJ5Gfu1/jP3Ke7jd+suI8g4P9LvWLEs6J\nACT4mK66rz3u8qklMMbLMiNgG2rmgx2ONCfBF/ZtFGmf2TkdKCLU2ncYzRAM\nCBmI\r\n=3WGk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"debug": "^3.1.0", "@webassemblyjs/ast": "1.6.1", "@webassemblyjs/wasm-gen": "1.6.1", "@webassemblyjs/wasm-parser": "1.6.1", "@webassemblyjs/helper-buffer": "1.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.6.1_1531921872418_0.5932391866878108", "host": "s3://npm-registry-packages"}}, "1.7.0-1": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.0-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.0-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "479dca02a37a74f09e726ddcf55a52ebdc0e4961", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.0-1.tgz", "fileCount": 5, "integrity": "sha512-+gstlguLvWOKLihB3PElHEfbwP1sEnqJHdi7/f/xuN1+YX/QwNxm9fB4ac5RbKkWKxF+fgt6mEBiQG626N9ZzA==", "signatures": [{"sig": "MEUCIGG/anci6Yfn9AyiAjiyG/8Edx5eUtokIeNfxjTaQUgnAiEA/PCkvvsrDcCA9w2RAhfimHi00fo5cTWnx9Baj/mn/IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1YbCRA9TVsSAnZWagAARBsP/2Cq7j6mP0gYpYMN0hRK\nzMZjmQHAkct9gy02v5Dl5USKWsgZLkLMxLBvt8aEXB5EiaTKjw8Qm7mKxPJB\nE32cK8SndIxOEEtxwPXZzyHP0YU5CJ3gtYYkaZDsc93M/Rn46ezsmeiznofF\ndCMoynBQGow/5gYqDQ3KQ1EW1uZAgm/aaes2PGOlWItNPaZ332EAx4oHZXNP\nsrQ5bEcqVtRukvSE47GbqePDv3muoCdx5CTx3t4dosf8f2jj5QFzWEjNezVz\n0GwZvJEXG5gf2x03llSEob8RfZTRf/aFSDQvPLiQNoaWZPoA/lbkgVXhjmw8\nr6YTfPFsLVFU8olL1rRmRqw2OqmC+hhRJ4hubHb0KKTqcS6YGsHnbn7iMHJa\nSZWeewva1P+OvPaz+XnfPtDlPSokjWilIg+wYqvxiStew/5fxD2BoWMvkU9Z\nZAsNFc0/Vc1GvDkG+fU771Fdmk8s04XmbXmGSZNZKoK2fvfxw0UchSCjQOVO\nTAijXxPT4NIIkmEONtSWgaQ+m8r4QFC4uYbwso0s+JP0bxJsUX8GxtoM5vQR\nF9EgU93IDWkcja8Tu/5pJvPu61TW5SyON+mXyN4BQzCTfdbIonWv6QPfWUZP\n6jkcNd+rO+9eTyG556xj6SsHuUz84BCp3OEd16vY4P6cTIjaygaAHWslNihz\nn930\r\n=xQ1J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-1", "@webassemblyjs/wasm-gen": "1.7.0-1", "@webassemblyjs/wasm-parser": "1.7.0-1", "@webassemblyjs/helper-buffer": "1.7.0-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.0-1_1531926042930_0.5566973907049577", "host": "s3://npm-registry-packages"}}, "1.7.0-2": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.0-2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.0-2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1c01b44f65b8281c20fe7e3a2348cd37f3d35c1e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.0-2.tgz", "fileCount": 5, "integrity": "sha512-XoHd8QJ0I/HqM2zYAagfYxXEcgTY3msrOZa7jNovTGYk6AAnuRWy2F8Z8ulZT0thKJQCRN/K26Iaj4VeIKNtsg==", "signatures": [{"sig": "MEUCIQC4i8yLWkue4oIlsy9o7c+WBGqvWiV2oMQnqOVaT0/dzwIgWWv07HSjS4YwWdMlCpwwtn1SrdHbQPQvAG9XZVDk0z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1nDCRA9TVsSAnZWagAABoEQAJ06Jp9GRs/4IpiajHb7\nkN2BniDWC455FieMqV4fLeWIl/ktjqGLtzgEyARm0R651U3VA155UrSbnxnd\nvQNJ13NI5W2BryoRQiuZcKK/QGgnd2kqO6NymV/YZ4zwG83bOdHehO4iSk09\nF45Ce4NzF5SLYBIBPuindE7PHMsW4mK+chr869qsBePJFugfDn7N8J14V9VA\nUXQ3wZKASo/YTPQzpiDm3kNVQddJ3gK/fuKx91iIZQ86kkd0tllXfN3hhNqP\n0JYEtd4w6XgSXkQQDzddqT+GBdehrazMwh5bdb8OsoRPoKJw5E38iKkjhkjL\nA1Finbjo1sG1Zv/PoTehGrLEkmPGGr3nUkclT6WAE8yPT6ous03BVpScHZNg\nn144ykDcFgprD/Z0gL/F9mYAk63byoyO8xfnCVDNh+Kqoz6uPmCx3DD1t44q\nKDdW+w4hTSKyewEyXxmTfbZSWt0hiWWvavZGIF6wPKXw5BxrMdDpg+kbCfzD\nAmzxW4WsIWMSFr/+cQ68pTB64JK2iQan1wlA2Tn2A/TKwTXxEtbnVvrmiGJ2\nCBghisVva68h+UbCLzKi2AW9fBjwvH3aoDHiL+aETKv5wrW3EG9UejTwYcXB\ntYnzOFq62URTu3porIySyxSvPJMVNIRWAARHr9i6+OvT8h15mHe1ibyC6hMQ\nuMxB\r\n=n9vt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-2", "@webassemblyjs/wasm-gen": "1.7.0-2", "@webassemblyjs/wasm-parser": "1.7.0-2", "@webassemblyjs/helper-buffer": "1.7.0-2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.0-2_1531926977253_0.030636500713834947", "host": "s3://npm-registry-packages"}}, "1.7.0-3": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.0-3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.0-3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "4b3d234271242c5449853507b1f96b354de7bb7b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.0-3.tgz", "fileCount": 5, "integrity": "sha512-S9TUytVlzfYWACbp+uDVOj7mA2+rvYVhYJnQ6EWwuqXrGdZD+/Jpx3j1tF0wUVYwN0eZMbRpHzpyo/PJCoXkig==", "signatures": [{"sig": "MEUCIQDWB1Anq43KCQV3kHS/fzdQUvwXIYXQugUtd6s+j3zD/AIgXNosLRbWHDF93yrfkogEacs3zBeLwn2F6q4CkbzGWCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5FwCRA9TVsSAnZWagAA1OoP/1myNveuvymLk4npclBV\nt1KmgqXSRPLF1LBkF9NIcRc+BmAyaVNdkq6aD2tWd2kfIxoEyuxMGigK0par\nEuROR9R3ehZO9ws4kUavDT7gxo+oAJ1hc4bym8A2zP3agsZCEhU2hEaJWFFH\nschaYK1X0kByo+5UDA7sg9Iya9/xzbXnGnA7LZeTar25DUqQTxmr8ejY3gjB\nyhUyftbpRehcRkPizl599X2lcDu6bLg7v3b9U9M8i9VtKaknQW4Lat1OD2QS\nTkgBROFaMn8KUPbrVwTT8THnYTKJlsj90yGOnZ24bt20DGB09IqOJ6EQuVk8\n4lPuvw9pQ6Z0eFZ3Oe9uaZKraDh8nKoiDFVht/zLv5ZGV47jWaN1zSCydjwt\npMObK/xY8LKlwpIvHCNXbidQeakC66Sb4+Qnj0tFR5MPPrp68P4On407GAQQ\n6HPyYeakTBssShZjKnJilTK5oaISDzFn0MMlbplDFAJlXwVoq+XCIONyld9e\nFJo8QfrCJlUQ0ZYocoaF6TacALiYF5+f0TThsUHyg6wiWkwStY7qr7w6A0aF\n0Wy8CMy05AMsEdesZgndYAyyAhGVqdm5mwxb7E3fWLvYrKOWADKx0ld9BQXy\nyrmWesOMFDwnlAxtpx28W925VFVEVWV2N60loa2zOv35kuH3Uk1VP/XNYyNa\nrsdm\r\n=mVLd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-3", "@webassemblyjs/wasm-gen": "1.7.0-3", "@webassemblyjs/wasm-parser": "1.7.0-3", "@webassemblyjs/helper-buffer": "1.7.0-3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.0-3_1531941232077_0.2689027467164222", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9fc0c92560b13c993774ab5f823daabcc6a0f71e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.0.tgz", "fileCount": 5, "integrity": "sha512-ra46DpCMhObQNkUJOjz1PquduGlFicr3rPq8qPnyRDKJCT3e/Yvl5GCB/eeBTV6s9hrwJOoBbqWAaormznNnZw==", "signatures": [{"sig": "MEQCIBPclMrtXKh6RdA96mN0WoghFw326b0hspPl652pk1mqAiBYFLnjyXXv13xKy4gq0ZYQE9GHChKwBIs0xSDbG3elSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULXYCRA9TVsSAnZWagAAxXYP/RpxGyJkVLLylHDHGOAh\nLJWGZnhpm0oACkDRQ4/giVxPrEiLMn8ELjmRJ6mdmJ6+JTY/Wh8ChgJzdgfq\npPJt2vf9WFOEsNyIZA8mtDkSusaheCU5uBe5dAr39es7NeCBqP2Z4fRvrzmN\n7l1Mkh8TO+XUfaBAo2B5zYg03fggaX3BZScxOjMZyBQVtnbYOd2nRWfzMl0m\n7DcsonQVju3sOrq6HD0NGr9kZ2gOBgZb4minIjSaJ5S8kHZrYgkIZPUkNhQM\nRlixUc7sXBHQYoUkl3VEjRVmwVow40sU7wZoq8imqffMLN96cu1LbYPvSdnz\nWM4YdY7AXDdETpr6R44Rug9WubxzYU55wgzSDcDbrbtynpylS2aFxD0P1+ZN\nf+nMbIY20RR5izVcfiwG80/cKSuCylBFO5pGW7r+MedYQQfCkh5ibC8+ljeG\nlhVop8JwAsOmsiNINBuVjsLo9WWZPDrO+igolgCdq4J+4sdO5UovSrgq83dc\nhlQJz2dmMOQL4aWre8WheNxJDrlA+yhWTKKC6PeZGFKPcnGH5XDGN9GzAtbr\nn4TWTXbgXEQMPq6WGuiVC6fdtitYVt5cdTgKNrD1nsXF6fm/fxcA4V/AXmJi\nKTsR4DlL+7DTpg24GFUjorWDYhKAW0ZSjN/IcV449CryWB6dP7tGewegzQNT\nDm8k\r\n=ETXO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0", "@webassemblyjs/wasm-gen": "1.7.0", "@webassemblyjs/wasm-parser": "1.7.0", "@webassemblyjs/helper-buffer": "1.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.0_1532016088872_0.3991596521767422", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a016fa8d72425da83897b51a0b3d18f5b3adb52c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.1.tgz", "fileCount": 5, "integrity": "sha512-qhzGhIVlyutAI5MoQDj//ZK3GvX7gEaVxr+tOCOCKZrY1rOKMht+Cm5gAQvvz58EbQtCNF5cAgoFGF/c087M+A==", "signatures": [{"sig": "MEUCIF3fqf07U2+eNrgaEU5EdWTDbexb8rERMqy/OqYJk/ZYAiEA2WVpMpneoHqaH7kiuxhaluroVkYCl9SKNSOVNswxI/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUL0MCRA9TVsSAnZWagAAjp8P/Axm8XR7B94TWETGOFWE\nfxa1ufJAaA9ZrTjwoTQQnJ7Trkyv9YqY4uzwVw7gVvLFE2W5yBerPpr7RqD3\nLD0LvTc2wDkorV565UcpyreX1WIWhCgWHHYRhThohVBwRzmhhU36Q1tXwkaA\nmg2oLCq089JTwLvt70sRFY82RYh7NW6umhP+HY4FmSEhMfatYXWSaskZjx/i\nB4RVtjloSVwAaEnV5kPp27VrGvSoHAGUnioqZd+WvXNCkjGFmScz/WdLUJDp\nx/Cji417GuHLny5PytSw6lR1bYHyPPTuDOvTrHJZ6MFyIRrm6hkEJeXvCrxs\n40TS/5t281z2bUksMCUEq50GwYpJwSuGLaIzgLi3tH3KD2GMi3UnJ9lRtwZ/\nqNDHpMOdKJ3wYT/0m135F0+AQzh2XDMD8IS/hYEjJtI+Ibmn5sdcE6Mrrx6t\n8MK2CsnO8wIkdHQlwM4eftqRp0o/I+Jm68UuVfFAVsxMHkXsjOSNL+20NBdg\nD8mYCIkK9yxDM9C3gpTGTyA06mFfnk8KnoPs8uxc4SC38hHJEP1w3WUjkMKP\nbaahZSDwDQ2aL8YmLNVDn6mNm/uF6C1dvQ6rkfQgBO0lmrgTQ1FI95a+kUMp\nflCRY4XwelazsJFWMad6NxyIj6rPGXChFUYnvEnJl5qdbDg0zGcuN0S6D0Qr\nCZKA\r\n=suAH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.1", "@webassemblyjs/wasm-gen": "1.7.1", "@webassemblyjs/wasm-parser": "1.7.1", "@webassemblyjs/helper-buffer": "1.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.1_1532017932020_0.809099982652145", "host": "s3://npm-registry-packages"}}, "1.7.2-0": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.2-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.2-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "47eda23cc73082610b42f6bad4ba399f2fe64c2a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.2-0.tgz", "fileCount": 5, "integrity": "sha512-08LxFpyeUwobi31lCDHLkpkQZwHF7N2HFa+KYcvebrG05nH45kiOwGcVYOH+FZVur9wng6MvIYOohacxEpysoQ==", "signatures": [{"sig": "MEYCIQDJbHSg4HmDiyXdIPeeoE7CeWFCSOwI6WwUlS+hDfIf3wIhAN1/b7Eiw8kTqPCdsyVYZ2/bAOVJaiO3x/P63PyctoDM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNQLCRA9TVsSAnZWagAAlB8P/2D+uInAblaqXOXzHH4K\nslrtoge8GQP56bQBiLHNoEzC+N8SXw88uTVTBUDBrb4uEYcbZZnU5sv4bACZ\nj+FOcJrRZh446U6FN1Ox7pmHkEp6QdAVSLYdbJWBvpqE+Wqyy3HCSLWlrriT\n08A/eEvsOjKrH34d115cwxeQLSWPEubK3xpzQeAWU37xd7XizG/XCajgSxdv\nhhkH+uKMoK29yRYSL9ZDZh7xxcBcJexVQ7uF3cxDdKS98TK7d5Bs7wtBlmVC\nAzyQBi7mFloZdhjphZNfdmezWWcZQSmVJuzeQgcZuQWBUMZScZd5Me6q0d8Q\nabdOaCG1Oba2QIHC7nuJthDbFKT5MI40soPLaJjueGjw7FN27DQEP6k+i09Y\nI3Nn65c6IqQHSlP/mfKe3uIxeMQ/zT86Vod8GzmvTD2/67uORnakwz8ifLrs\nYtQkiQD+fXDE5h8FcXZNeBF7SiWTT5AvzOTPryUD49nbbMeUGL89IZYhL471\nYmukq9S2Xqv4RTLhYJqISLv7wkdniNy9l7hssrL8nMWFbGISP83ZXQH3JJlv\nKSJ5MTpFDVWsVokRBdVexBZOeW2PGogA1cdUF1l2CXZSkFS7dRP5kt3sqq4x\nsrsZZl6NRmFX8aEjf5aVSrdXVf/Ki1z8I2xcTPR+nVf5PEx4D39tCErHXS4q\nIUk8\r\n=UA3I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-0", "@webassemblyjs/wasm-gen": "1.7.2-0", "@webassemblyjs/wasm-parser": "1.7.2-0", "@webassemblyjs/helper-buffer": "1.7.2-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.2-0_1532023819138_0.4145537035353579", "host": "s3://npm-registry-packages"}}, "1.7.2-1": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.2-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.2-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "afbd62630123e7a69ccbe0ad52ed3829d2c1ed0f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.2-1.tgz", "fileCount": 5, "integrity": "sha512-AykiDW/yuJetOfVx7TQ2oYN+VycfN/sUIlJBGPRLz95FFEVdIf23AmUrAsClFR313ybpwYw6qwYtpjaOaM2Uxw==", "signatures": [{"sig": "MEUCIG8eHBgI7ruXzFm/rqZxcjWqk9cLa8Bfz3CSMf/iRlleAiEA5aOxELYzqxVLOAyRjJDqhcuZcTjwy3pw430Do3SB+YI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNeBCRA9TVsSAnZWagAAimAP/iqsCnosBchdOyLUXTni\n5zEMLxj6a1COttTi4hmQ4KEofHy0M82ENEp7h0K5XH7nPy0kIVsBZ/d/PngK\nD57twrtl3wcFaq3vLL/5pTHAhQY5kEdmNUD+DdfytzziGuJRjkZ2s05Jb+cB\naa39yfZnnrkkwyoo3oczOU3A3Ck8U4vLJ75Uow2bBqYM/gTXP5p91bDH1h9M\nly/+MpXzLYf63raSkn5Bof9LPMuWOQWFif4SHZaAagK5ApGJzDtMbFZHHoIw\n4kznDdPWWWblgBUJI0aw/qVKnHErqG/WNh26v0JaAOJoXnu+XWnWVVgFd4V1\nUYpFxFbPIz937FD7QONsm6j7BF93px/A9xpmSi63zHGnC/ltU/nktbWuP27N\nEXdwIHb8X4LT3BcJ0+gzhlKqP2+PklQYBCKJwFxiYSJS7C6NjqcRz5l64d9D\nncuROOyayyLgnvHEW9ViA6o3E03FzjsoFXPC+dYMCGTf49ibaTU0J4hujT55\nuky2nAoPMkP84atEkS9tTf/92VDefvAFDwbuUX80cSUrBhSnW6eRfAMexg/S\nKOgow0JTMrhzfvqUQKCO99hg+Labqv3RzZV5KeOKmdyGbA5s6cchDcRLzxLW\nZxiSzPGExumj2UeY0Q7WCYmz36nZjv39usIiZ4oD71Oxu1rPNWJYhbKZcw9/\np3S2\r\n=Nl/E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-1", "@webassemblyjs/wasm-gen": "1.7.2-1", "@webassemblyjs/wasm-parser": "1.7.2-1", "@webassemblyjs/helper-buffer": "1.7.2-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.2-1_1532024705100_0.18233850896033665", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "422e744f0089c902f0f071004cf4333f49e06683", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.2.tgz", "fileCount": 5, "integrity": "sha512-xnMsmJxuQ9glVnVHbj8IH6zorL4ozMks9RR/ISPxJ5l3encsTMQ2jeD+6ZagBbI1Jpiai010QoRidj6Jqj3SMA==", "signatures": [{"sig": "MEUCIHjMwXUWLxM+huJRlj8j+n8Mp/Op7sqfE//9RKCUerFgAiEAm15pbJcteJSQTrMG5e6Zyjv+qOqBq02nQGPVUK2BUuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmbCRA9TVsSAnZWagAATCQP/RKgW7B3kL1Jsp5ZZwpv\nzgtdxE0jxtGGOboN6ph1qrIglbckFR9J5Sv/ELP6T8mSjMPn7osPjjCbkGmu\nCwyggf7JWRElWTfPJLIHr1nIPGa/Y5o0LNYOzn1zSFJHPaz+ucvHjBpjzzuB\nWK2TMdtYxIYlJCUkVV11Po6+bkGzpBsuKbd84FxVBLLZ6bpUbhkdH5q47asK\n+UDVM5Wx/0pop836jmGdowgU7IxK8E28wI4y6LyCnQVivNve4DhRkp+6YdKv\nDCbiWpdXIG2TTp5ko3Z3GEwswn+PB2LrRg6vnewBwLaoKdxkyBSg2EyK1oe/\nuJSJUgRdEPgyQkr1yE7/NxWAQuVqY3s34nD4etjjCFmVsy2olXhanjhCO11m\nvQmNh/vm6E5eou4VpJnt6ecn3N6oAqgtf9LBRUYzCVuBuwLqj/G544iuwA61\noUWXTTYsmshGVcf+9YPQtf9Ia2SVPedkhMndyYhvAr0si8j0XM6lzbrub8qM\nCAK5nTSUjn4mAfaIjYGRunMsyu9yVoEBiiubOcnp8cQA47JbXTqPKe66UMrR\n824Q8jl6kfJ7PO2idMYNAYX00kH8nHEt9L9JUixAa1Flmb3z3I2PrCBjkkUU\n/UNaB4OYW9KemX9/NWDzg/Wgcaift9zfkfEdLD0nFqvhpTa1dV/OdOR9aIZx\nfQmJ\r\n=mh+D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2", "@webassemblyjs/wasm-gen": "1.7.2", "@webassemblyjs/wasm-parser": "1.7.2", "@webassemblyjs/helper-buffer": "1.7.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.2_1532025243463_0.28481232867322537", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8e1d73511a59d24ce6d9134d9ad53f7e6f4083af", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.3.tgz", "fileCount": 5, "integrity": "sha512-sGoayfD/DtcJRGt3SF7WzNwSiXunmFSicnIrgtBZnbyvAlmtuAxif2kKWoAeAp8Ody0RTCAs+syyBXCAfEq5hQ==", "signatures": [{"sig": "MEYCIQCCvUgW2i5sSIFvMz3TlDAIx6KSWnceM2TsOPJMvHT0CQIhALpmoDdMZOR0wYjbs+PIvZ2G6JCVklSNBFJvSkv8pDtB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2cCRA9TVsSAnZWagAAlEQP/3xsaN9e2b3PC61OnAT3\nBoaWuvGcej49GJjI7aYFjLVVHUcC+azpyuPlyIbkirhc8wwHQo2UNczUVZbH\ns2oz+Itl8uQA4iXwZ7XKOEiCTv4ATiGqaO7F/6YeQ38xWoJmVyruTcx8EHd7\nqqNbcE0NqTxZ6X6VMC5bhzo9MzCl222pFflsxNyDN7+8V00K/uWuxmIxKIZg\nk3FBxSLQxKMwdwfK3CVYltwnaUzuDrhuiEJoayK/Y6mT0kmeoan0SfzbxdgE\nDvXVBUseASLKozXxSDcuzgmCDEkQnTdED9gTstUyFSHv59zvsEmzvaiX0E3r\noJB5jb30Ig1HohGsii2Q2ismwWdwRMs3wzMq4VZLnRHeLSOGCjt/7Qnk855j\nJBqlB7++lKlXh6sxiyoww/nD5XmH/8bUn0BReUvMvk79Ke9LHBYSoOz9XELV\ny2wal5fMl77l/Tf38kFWJgT+GRPSott62yEIm82KXXlOra5/IcwFqAcv9zOF\nfEmfF3j2x2Ym51jkauoL+XWgRxKq5uNOTo5XJTAUZ5N9PqhePON8CzL+WAFO\nlj6YQV0j/1qW2gyiyBx/rGfTNLO8qcyoJdvGU5exNGcTwj3AzKGKtnEj6eK0\n0/FVTrXS81O3l+3zf9n0/HWSc3hCKD5bN86Y4lhIoRY893KzAerYg224gRKm\nyVuu\r\n=eTQ6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.3", "@webassemblyjs/wasm-gen": "1.7.3", "@webassemblyjs/wasm-parser": "1.7.3", "@webassemblyjs/helper-buffer": "1.7.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.3_1532329372509_0.18397615615321783", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c79457aff41b0f6d4db5b015506f7c050e23ab26", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.4.tgz", "fileCount": 5, "integrity": "sha512-Dw24FTjpdkB0q4m+0D9mxYp+3/wd/Q80TVjiW6lxLztqaJA19b1rqe97Cd0C5mfdvFOWRdQ/tuXMbhMdsCfNdw==", "signatures": [{"sig": "MEUCIAyc1BR/pPPDiRqBzbjlblPuQMBy57MXR2g1YY/FCcDJAiEApdIhbW8KBkV4+n6rnnINgO+PeDPmmJagmhuHaCgZ0DI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs8SCRA9TVsSAnZWagAAZ6gP/RwtL211WMKKjlL/eBVd\n+zHixe1SQO2KBW0l8R6qEc5JwRCrG4vtiuL76k5+xNNAf49Eqsp4vPJGBLID\nRoayHNrBQTAp5mnDOP1PchaKz44nn+YsiDETcMYYQQrySM1K4FDIdMQcxxvC\n/SS2yc28CNWmrLD7ePZyyH6mXJAXsGCYh54WcSJObn7s6XBsX34zuk/4Fq5N\n3tqSiIKsSeL1DuAq6sZPnp+CVcJclMFLbdlDb1NR8GGtlvsEGDcACUjtjxoP\nhuIxVXTqoMZ3/GecLA9ulZUJQ3CfA5nrdJ++xFpYW2CqXmrmFBE4hFSq3zgl\nCgyx/DLGf6bbDuJbNrXQWJ9tzfUeOtUHDhdSALQAQP3JJzErie95Z9vntgDO\nKLFZ/SCXFHqzNSIUovClqZ/eN6NzE1UEGGX+GFHJNqqSU+bfBvi1WcWJTWno\n/NC6WTneg0leRHklOIeO9vXDsudHVeMtgMn0pC2G0slM/lTYvEhhPxxNtj5N\nKTYfj74ROhq4L4iwKPXptgs/Z0KSuHCtnhEhn7s4hwM0Wffom/V0A20BUKLY\nGnWsGS3mislpp56EcAQ/rxDPgugFZB9YVu7S8Ns1veciVCYP13nZPhTvPDNE\nHdkOZxyuQc74DIDMQZchs9P1+6AWdn6OAVCcmGRUTaYiT9IacYivIcGaZRJP\nEZ02\r\n=f/Pr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.4", "@webassemblyjs/wasm-gen": "1.7.4", "@webassemblyjs/wasm-parser": "1.7.4", "@webassemblyjs/helper-buffer": "1.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.4_1532415761900_0.37616818363465576", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b4365c04945a0032cb4a0d8d1e42ca1b5c94232f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.5.tgz", "fileCount": 5, "integrity": "sha512-Lut0r6pnd8KcYtbMa77pe5lK1KiVGPKLkcQOThNH12KokZlzBJhR2qyZ9V46NebXvV8xxmZX8SqncvvbREKpqQ==", "signatures": [{"sig": "MEQCIGGwm8OfKo1QoTsU8eTvJvj4gQ5eDTefkhKdWHRZ7tPcAiBBvUwstNOFLLmHELBbUIFLd+O542J/fVX7/7lNfoPs8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZAuCRA9TVsSAnZWagAAFa0P/RfZsqfIoYD+VGw7ADIy\n8O9JMG9w2PlpolJ4kAVRDmGr8fAsYud4w2D9AGjWBOg5y5gbm0hr/ll7bn/q\n9gLoONLjqDx9Z58pHZ/cXfkMIluGnJCcuRDvEavlCEKgjzDUhrts2TrCtFT/\n9S4y3IcBL6yMtofWRk+a5aYwkZoNbV6uMqqs+fmLkpHbuPsL6JAdujfxl93t\nkJ6F22sDytiuZyNOmWOqY1ZqCBRLpfpHbkIJO3bODhbkEUJ6WGz1S7CWGX8K\n80dG1lCMZjMb9As+wF2i5KGvycVNvvlxdrH5YtyNQEX4IP2zZvGMy0HzZtNp\nW3x9i5u0hRtRIwQWJXPwnC+0RPMmSGUVZLwewe2Bt0q+clptnyCVG9sZo5hf\nqXMc6Mu1oq3BeLhRMZ7a7biisYe3Rg6m11/fYWBJCYyPdR31KLnnn5uWNLVy\n2pJCnR1RCGCZlMWbfLdJJmH87tx3VBF3af3QQWcgrOM6RXstjYWudBdZA+22\nsoTNuQb4nPyAA9e9I+kitoYPC87kbK2JprzJIqGvbvt6xWBkPZT9ukKp48sf\n97V0aelkKXyk01RKBJyQCwo0Ols33JNrX8y4VuB9+VgD5NWhT11jtboOfi1w\nQk3Si4/lfy/QC/8mmjVaLhw707tw+B1IR49whEkaSqWwv/P6FXxxjb++atY1\nssjt\r\n=RVe0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.5", "@webassemblyjs/wasm-gen": "1.7.5", "@webassemblyjs/wasm-parser": "1.7.5", "@webassemblyjs/helper-buffer": "1.7.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.5_1534431277958_0.08628678972988801", "host": "s3://npm-registry-packages"}}, "1.7.6": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fbafa78e27e1a75ab759a4b658ff3d50b4636c21", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.6.tgz", "fileCount": 5, "integrity": "sha512-go44K90fSIsDwRgtHhX14VtbdDPdK2sZQtZqUcMRvTojdozj5tLI0VVJAzLCfz51NOkFXezPeVTAYFqrZ6rI8Q==", "signatures": [{"sig": "MEUCIQDLv+mp+ucSCHDwi6mPnRzvK92d1vRaFmbMS0Yp0ewxTQIgTBaoF3oqjshJScXTiDbrVdmDhpx+wZHLTkB/mYulLUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbln0bCRA9TVsSAnZWagAAFnMP/REPdRAmo4+iXCV3g9dO\nI2wj1o0S4aCQOX1J3qAVneEYVjRyZAyPZB18Vn7+6czYjHEg0J2NXjA5zCjv\ncIHvSp90bne9W6xjDcw1DVRcey1W9KOgytT9avwPECA3RwGL/osQBdqJeGJo\nC0FXiDDgMwtsocupWJmn8zbec28k5YNXU0hDSqI0LtMkHD1QYxDpK4UEyQUD\nEbl3b6/FcOEqiO7ljIdEIfJlGN9ocNJMJniZpzlvn+LYqvj2wZpNKAZ3b7rn\nU3UeG575JMCkJFnjUh2/XQ+xHAHMobMP8OR11t3QNge0UxMLIjx4kXqq2awJ\n0OVFIY0ZZ130wk4Aam8Urg3DWkeq9ilG+xqBY1HnOtGxerzaK0P/T6vBvJPe\nbpasn5Rb0xFTYk2c213y27yfY8fUQ3HyypocgGT2WeZSSxMojZbmi/WSAyy3\nMkP7dB2pKjl3p+1A823Isg4K8ZVI9eYTjqSrEx5y+p0Ioh/FbDzVFwKO6VF2\nXs218tXzLWxyHvty7tNWsDVYWykW5vYhGYSQLgTEzLGA6BeImkwD5dCVYLQ5\n3KxPatrQ2Lut2g+Zrat1stmuyupWIAuvkfKx+7Iaj7Lt2swnSbz1XO4kIuGR\nWTNXnFwljSKZxSHfsl2KTp9WEFikrnADf9kz/KQMLqMGU5GmCr6vOUHqHBHD\nXJTF\r\n=8U3I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/wasm-gen": "1.7.6", "@webassemblyjs/wasm-parser": "1.7.6", "@webassemblyjs/helper-buffer": "1.7.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.6_1536589072823_0.780419114198716", "host": "s3://npm-registry-packages"}}, "1.7.7": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a3d692696618be0d823aa2bbaedf72ea6a1b3c95", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.7.tgz", "fileCount": 5, "integrity": "sha512-DlXmP4l0HH9C12/gUh4S3nNaGrIKAcCOat4NsIzb4c51aIejhepFktrIPubPmecR9lpTyMcf5aYOBIynQtMM2Q==", "signatures": [{"sig": "MEYCIQCqnVeE0BxdgHx3GC3Np3XtFn9BND2b8Enb3ImtF7YnogIhAPvng9Rd5moa7+IxJWdq1in7RBh1mCMRCzhDk9A0W5f2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeEaCRA9TVsSAnZWagAAknYP/39pgc2epY2ym1MetpYn\nHjUJEvinYTQ4jTbLGLc4pdiPdGKWFi9JhnKHKIjS0+/yExoraQ5rzZIoWSa9\nclKu/b3xuubYt2nbLcUiKNM+w9GqX0Plok00nj8dah5LNKt2UffH5QulwW2C\np70lI4nko3MDqFw3SnCn4YhK+orif8EmVDZrRIjiGhifLg06iAARN8kMLu+Q\n0AL+w3jOozeXFXKSXFMdy8a6eCVa91CwSCbuKU3rusTCeWOaiImfAOrZ93B7\nOPaTmLwghouIFYS5Na53u52WZjOT6HGgjwHSvaz5bvpwm2Ir2egj7mKZJiTr\nwkxc2Wpq399rZfdYNigi7IPbO9joWD18rdvtSNtoHBnVW49BDBE91CwVgFbQ\nO4FS4CBO33pVJVIqUhKmYwFsQZ6btv+c8/IIjdC45L/BkJ+eCFAE2Vs1SSSb\n7p1VKZ56Ck8TxL7lVkJtS5iy5ImtfrH8NpPKGnqbB+wOFheKBzpXA7AaKMVy\nYEPf2M7VqSI71331x3SQSSqSNbP4hBZG6/Rh/O7LcCMkSf3C62HKOXCVsZHA\nrQynaEq42ZnGSZog843mYOX/RJdHz6FhP3ewdam0WN4Az/6XIr5bt692gh6I\n0oUs4WKXCKS2iJUDrFXYtGsBdNjW3DD4xhxBdgM+3S2dqmDj92jZTGV68wFj\nV2H8\r\n=ZZFm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.7", "@webassemblyjs/wasm-gen": "1.7.7", "@webassemblyjs/wasm-parser": "1.7.7", "@webassemblyjs/helper-buffer": "1.7.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.7_1537335577660_0.802221473721372", "host": "s3://npm-registry-packages"}}, "1.7.8": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7ada6e211914728fce02ff0ff9c344edc6d41f26", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.8.tgz", "fileCount": 6, "integrity": "sha512-3lbQ0PT81NHCdi1sR/7+SNpZadM4qYcTSr62nFFAA7e5lFwJr14M1Gi+A/Y3PgcDWOHYjsaNGPpPU0H03N6Blg==", "signatures": [{"sig": "MEYCIQD37uJ8JMR14kxLlAat6yREnG6bF/fegDMqXqlwlq+QJgIhAPDdX0jGtICeAXwMywRvggZ7Ym3NUz03mSY7j/SIgBoM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9336, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/PCRA9TVsSAnZWagAAI10P/R0fZWCgh03+Q2xBAAXK\n6kM65Yqb0roAnQ5kiShKKuOuYh5+5wT+71MWgNMCo+Kcqb7mCSFU21z85BYX\ncZwOTOWF+9c4A+X69v+ybsPI621U33xV/WfbOm0MPXzMWFAvDWw18AD78rRF\nXNQoRO8fdknEHbLXsDsU/3eXBJmZFawkvAPcro9zN9rJ5//w7X3MooGP+Il1\nOU9p9+vefZbGQ7z0f1WDkeoPj1/6TbaN4Gro4K91kyg5w2+yS6/P7/h0bDzy\nXL2P5tusha+kzLGloWMqfrusrJMsPeJoUd1/0IrGXXAMsfYei3Gva38uSqME\nSjqFLyovdkxkArZVnyeH6RmeClRVRnU2M/lT0YhfLYe37wxGk5Ps2HkZLfQs\nHWEyNT0mLWyBNavo0oUg2TsQxoYDHQ7DYBOUHO1QCC7eJbvO/kUCaWFq/V0N\noV0U/K5aHj4ixGU7dKECASflA+QBiwHqt83zCfkWZhmE7eXyvYmxl/bm0QLs\nEajmTZuHPAph0TZzje9g0Yd4d/ZI+NPg3tlRL6XOAq6DhD5VdGeuHhVitPpR\nmQJYPbtP9XptVXbm73w7luxXRODgIzB5Uv1q6qRrXZtv54eCX/q4HPY1cs5Z\nFP5lKMCREgi72KBtAsqpluhVE6xOZ6R89I5nXbOCo/P4gMAGcF0aG7qi9l72\nVGWB\r\n=6tv2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "43b83b600939b19c48c3c27a1733592c493c4386", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.8", "@webassemblyjs/wasm-gen": "1.7.8", "@webassemblyjs/wasm-parser": "1.7.8", "@webassemblyjs/helper-buffer": "1.7.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.8_1537515471328_0.19483907399989686", "host": "s3://npm-registry-packages"}}, "1.7.9": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4246b70cec471e44c9421fa79598557cb2b30829", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.9.tgz", "fileCount": 6, "integrity": "sha512-i7wtl54hiNb6FtpEu2Knj1MItQy1K0ZvYZOulNiMFKTlpWcx+ySw/IFIMuz2CvU4ZurxkcVng/faoqoDUKyisg==", "signatures": [{"sig": "MEYCIQDjPIFbsc4sjr6/Ai6qYeLJGyu3e4SNqoVdP/YTHdKFVwIhAPaZqoFjU1/58z+PyuliH6B9zSxn1i/R1wAngnVcRiTd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgTCRA9TVsSAnZWagAA+wgP+QESDkO9YmlJAIgsFMME\nCJDI2nOdYRC+1uPLolJ6MeqpPdDC389Rp4qt529GZY1GZSloFOFtG316mBc+\nMqA/vFpzIYpA+9Kz8zdXZWIAEpXNib3VcQ5WqU/0EDGdbNoga0aVSuN+lhhH\nl2x8amrtHQGh9inWYmwdWNnCl4BgQoP9C2QvhUffiG+XIeheWFZrMmBsQ6PB\n2n96qQycgqbe1vls4PIm0qLFi+zaeGOKK7iKBrL6f64tZizRk74eZoQ5qRIW\nCplbQd2AbSulaJ3/qWgJ5fHGwd0ErmwyAGphznMkEkrdCdlRznipG2vDC3kq\nklN5QmlwlepI+6XS4okyzVujOIRK15kKwShWNVhnVrjCQcelhCaHT/KyAlVs\n3qeZ0IfQunRwe+U58aJ+OxBzKAUfvyCUpqjWt+zwhH3zklUlz9w+0gLWEkLh\nAeQygWELO0ey/MQTe51cjcTD33wKXFlQhwYa07bbR5sZColyuY7HbjBGBxG2\n7xB1MY3WBSeVM1i4EdFBneDHu5jxQxcrbEKzns+USKQe127CEGgyrifcIxJ+\nvILrvtNvKXU4cC9zONekIuve3QlMrP5Uzwh5K1NaKryLftNyg2g7ypMurpTv\nv2Rd1cqiKj+Nh0rGVs5jPJS0ohrhr/lcSaQNX5Z3bV531q94qy9Jt5mqKkBX\nliJp\r\n=qEgm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6c5bd6e21d734967e12bb7b7aaa38c80697b3b68", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.9", "@webassemblyjs/wasm-gen": "1.7.9", "@webassemblyjs/wasm-parser": "1.7.9", "@webassemblyjs/helper-buffer": "1.7.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.9_1539880978487_0.9583249145155179", "host": "s3://npm-registry-packages"}}, "1.7.10": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d151e31611934a556c82789fdeec41a814993c2a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.10.tgz", "fileCount": 6, "integrity": "sha512-R66IHGCdicgF5ZliN10yn5HaC7vwYAqrSVJGjtJJQp5+QNPBye6heWdVH/at40uh0uoaDN/UVUfXK0gvuUqtVg==", "signatures": [{"sig": "MEUCIHnnFUsJUESysqI4V3c5MaphoXjUeSonIB1vJn4RKDFYAiEAhwriePoREs7vIHCvOqXr+/M3G/38Z9K7uUdk60VYnZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuv4CRA9TVsSAnZWagAAq+4QAJ2GWp5JsmZ5wfnMARhK\nGxSOp7SyKKwzpenN/8xxxuHJ3Nfa5IqyvBVZy25on7S4C1IF+MWDL2YPYAqb\nvb5ag2zOb1JhgzgngXzecm/MAB8I+RCcX7q7l3GjRvfbtY8Km0t1z7L3rEnN\nE3z1jsU11Iii90INpwaCzwmimPnmP4No4bXFtTlkeiQ4Zc//YUhj3ZD9jcei\nAAtuZg4aabMG4Ut/566VRpPnyLZ/6+mKFWrOPS92K+IzP+FNunseFZMnlsta\nlUG+Q32koeuiNMoU98jp5/qJQV75EwHewvb9y+OCiDjRBxP0wlwu2wq233pr\njlx27KwcH+xKVayw4vBoAAmF2f3PuEaiMCo+NTsLjKD07CZQg7BpLess1c/T\ndb74CUpSJsHbX/XAefZW9AEyqZtCqJvudsVDT7y1eeQbAz5XvRKAIkOC6Nug\nTST8Vy/3zwsGzKy/svZPhOhao8u7ETwARxIQ2y3JQ8A7PyVLRYGR+gD3YuPg\nBYUZMdtgVIaxoQ1oj/pty9r462auUPjyJzAY7XIeWkbXknZ/NmM/MIELz5Jy\n8jLmNIluHpfGy1jIQ194TwEtE1KKtsennoGa/zt1jk4DBP4xCe8bCkZhurZG\ncVE/ctiVv0yCk4Iud6PsaAu/uNT34nKGFiR6VRpBNmUnbMxqpKT3b55k3Ggk\n9mNw\r\n=+qR7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f558c6c047187f24a2200ab04104f173de226794", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/wasm-gen": "1.7.10", "@webassemblyjs/wasm-parser": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.10_1540287480414_0.9389895918449858", "host": "s3://npm-registry-packages"}}, "1.7.11": {"name": "@webassemblyjs/wasm-opt", "version": "1.7.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.7.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b331e8e7cef8f8e2f007d42c3a36a0580a7d6ca7", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.7.11.tgz", "fileCount": 7, "integrity": "sha512-XynkOwQyiRidh0GLua7SkeHvAPXQV/RxsUeERILmAInZegApOUAIJfRuPYe2F7RcjOC9tW3Cb9juPvAC/sCqvg==", "signatures": [{"sig": "MEYCIQDLxQ4GDm6E62Q4IUaUjEg4tjgyzvThKgkkiH7WPqN7lQIhAN4DQQro0w3gU0QDfjO+z7HepkzbSvxunR/dt09DFFRN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxgCRA9TVsSAnZWagAAn6EQAIjZZ/i6wljB0lNDgTAD\nMdH3Uwk0UcJxBdUTtLrjowb8gy9zp6LGidCiQmUfEATNmP8sgrNyXDG0+TlV\n1M/OJ7DQeNYI2vwlo9EMwZJhNFW0OEy8l0zHRCZR3KdidMi9JoV18eNr+bxn\nSbBzeN2KCfQctgkkRyY6lOZ3BUfTNs+30PW5pcJZY6yCMwX6fxAWpYJf8tmS\nowY8G6rHpkjqZ44Ov30y+kYLOGCIC7BrlWWC/tnJhcRNC1og4ff+hJBn1R0R\nl9eCW7DDLv+BImk+sYMm2SP3Ai2fXRZEGLeDsBWJnb7Grk+5AJhCeDf0FJrl\nR2cOeYuYrTn66sWnkOnHyehKuRobYupYUBE2vtHJVzgtSasdQQH+HcfsKhYS\nvLwHtAiMvFIBMn6w5yCQ9BlbwhD8pfhN38MFAaXrMm5qbuvERgTtAqNWlKvF\nGm/zQg+cwu4MDj0rpmbLx8QiDI9UtMvEUWcklJrSc3g9AAeJEh2DXLwvihv+\nDKkX6s07XUSdxegbUgpEbLCY1gNwKjEKd2esN6XHJqASGetoEekQyBkijj27\n3pR3gcODa6ZtNQyPDm2qFFwS8PStNL5ceJAbh8rgWKF2SiIiJRSBGD54gWuy\nFLRNR1Cmwjbuz+WPOLwAH60VE8bIlUtrNJ9yXyjO8dmxjL4ZxfTjzKL1O4JZ\nMq2h\r\n=M7Ft\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.7.11_1540922463302_0.09568598181268095", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.8.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c8517e5035e8e35241e5c5d96b1de4fbcc486d50", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.0.tgz", "fileCount": 7, "integrity": "sha512-+UjYjkQfqj2vuSyzyugBkmS7I1yqNtPV+qzsG3Kngtq2fpekQP+CjfS0Ge5MxomcHdf96Wi+nMGw6fS9FWFA/w==", "signatures": [{"sig": "MEUCIQDZrDr/vIfiO3tZhnDvNB8rZuE/GhCEVDHQIcDwU67m6QIgAPlL1qqL/oyUU/6fwJIhO/HQ9pQ+dYotDsjpKqrxOg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2YrCRA9TVsSAnZWagAAjF8QAINZtB1tN32aLt1hrhcN\nA2msurAftGCokJRoYkrcXG9AnPKVW5QzIWIPMId2/p2HebEluxn0k8Gc+5rj\nELu1ua4UgQKoJBiuXS0Ce/xM+qk4tsthrAb8BZELU2YMMEqSSE9eG4UNKtyp\n0SxGpV/DHJHSbYSWISfA4yUm16tJfx+lzEiKZwe4KSYIFdM4SZUtlk2Zuigs\nKEphNY+lxBm6bLWG4GCKucyhQhAsq0GxU9SROdbr+BLf7bLkOnZHkxHU+2lc\nu5qs97fFO+ho9N59TFYM1TU6z7H/QuqDMjwlLb/x3HRO2fgpRGuPWWxjDnOO\nLH6uimBgVUEPy7xY/2MFMpvPr6eFOEd7BM4aTnDi+W8l2EHpdGCZoJMHfisO\ni7DiU2e5AK/hG7VbCm+Yh7DjyO2WQd5H2CTXHRn5nA3tDBH4FETudYZVeNW+\nNimw5qA4XMhuHO1tX+xApxb3aFF/j25l9t//rxpeqcnazALbnGU08/VIFOC1\nRRGOFJfGPk6ROydTfYkjR6HZ3z4Cyuly/IkJa5vAZRY9ZB3u6ednCoHyFRxj\n7J3AHHZRJBWbYkaVIuSxp2TBAJnEVDGufH4dsfDuJqBEDnCX0utIiLd7iP08\n6geLVVjxVe4Z4BXoC3Qor6CwA9t9bKPlI+JM1QMbHwueIUiW1KMNiYLwrx3r\ne0mI\r\n=muJu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "8b2d1afa793ea81f20ec63416134c201e39694eb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.0", "@webassemblyjs/wasm-gen": "1.8.0", "@webassemblyjs/wasm-parser": "1.8.0", "@webassemblyjs/helper-buffer": "1.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.8.0_1544513067051_0.46957000540913474", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.8.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.8.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "825fe99bea57d75f4836a5e07d3ef6457bf7fe61", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.1.tgz", "fileCount": 7, "integrity": "sha512-Re6tJ6xDlT+Lh7t4o/aLm66AgBWwCCJN7honfYRHM1E5yIRncFY8v7OulecvxgTgSCfF0tls040oOn71uLI+dw==", "signatures": [{"sig": "MEUCIBabODkHo2JPza0OyVDnucyD1eaOVXOdM/5qF8GJYSTPAiEAu5Nlrd3Y7lvUM4L1Kyq837zpHLt7n2goT9b8FrcBqyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgHCRA9TVsSAnZWagAAUQIP/1R8mzngeiFjBFeiEpty\nK0G2Ax/ctNFg+R+gwSDHjfgbFtA1Iil1cwoOxpr75VYD5eG35seCbZHusd9/\ntJH/DGCmRuq7wG/uN4UQ364e9XrpOSWSmDJ1YH3VQAbLqcAmj/aBZdG2GpC2\n0+l4BMiVnFNqK0HqMF62TkzefmEHrjE/sbZKopgT6sRXm7eWOyJA2U4rIOX+\n/S4hffdbcrM7a5aojZZKNKOsg6SY/+e3kVmYhZexXQsIYdwCtbms1S4dgvuz\nGR2OuRHYB7Jnw7tK9nyDdl7v8UFlT6lY7Zw+0ZBzCJK/45QjCP5ciCpTfczy\nUfS2LuwKOkv/M6qBQLFBfhdUwFE8IaKIDPdrOhdJRE8VJZ5Y8jbYTIBUSMeb\nwKewC9W2R4d1Z2F1lafM3Kva+xnujTunO+89fW2Kwa+c3cwofPLsNa231An2\nJ5R9R9bift/kYvsgfxllelbLPBgU2L4oZZc790wk+2yQdLTapNwETrEFaPba\nIVMeKQd8ZaDeqUnTWQXgH7cKKZ8MKQBivI6I9lnLMOkJUcJkrran0FtiZomS\nC6iBEhrWeqjuNR1YxOVNWwwaQETJ+imdwMDnzU9r0hpAx2Od1UO6Zg9nLOqA\nbPOp1Grq2K7tjWgzs2Xhjmr3QiICvEcBjHEIpR/LnjTRgEO4G4mgmuBE8RDa\n/YC5\r\n=cf9O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "a2f42245e9b597e3541e0f697253449d60fc4d79", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.1", "@webassemblyjs/wasm-gen": "1.8.1", "@webassemblyjs/wasm-parser": "1.8.1", "@webassemblyjs/helper-buffer": "1.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.8.1_1547540486526_0.46033059190110004", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "@webassemblyjs/wasm-opt", "version": "1.8.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.8.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "967019ac1b6d577177d241b912f61f01122d6b49", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.2.tgz", "fileCount": 7, "integrity": "sha512-tzXn0xNQNyoUBr1+O1rwYXZd2bcUdXSOUTu0fLAIPl01dcTY6hjIi2B2DXYqk9OVQRnjPyX2Ew6rkeCTxfaYaQ==", "signatures": [{"sig": "MEUCIAljf3vreBg5FBVbwZeB3qjaZDY46h1X/B18zoP+BXCzAiEAh9EzlgH/fohFKZc6CHJzKPg8BZTkcea66evu6lRDiXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWcCRA9TVsSAnZWagAAahgQAIfwTjb2AsolkeZmfTP9\n0MZ3EqoHl0cM0d2h+tBLAKxPkkqGUxUfAgkyVlosqmY9pjayl7QiubLe/Sej\nMOsB9ZrEslMrjZaCZlH9v1Jf7ZMUg4mooHgn069OQcxA4PGtdwYjGsW+izhB\n1xfhBUvIn67VP0brAc+OVltF9ONi7XM6KzQ+ZUsHFUfu6B7Nr5GSBp0gy4YB\nudhn2xTXGDIQ2d1gCmLVcZHbp0BpuPPjRi2kOJsPPDqE8RGq9HNCy1OET2/U\nQUnlRXpPLjPe9/kge/7Hx+AjLXvueIHa+8QOw5JSOqUzCQF9rnxZDiEIzOOg\ngCleXNwMzQsHAbzfJg9RG9pt/hkVW/NZa4PuxP65dQu3xtsxQ+8eCxQezn/b\n/v3eemR975icVhCG81ADUPseEhp994oiDRh969mxpd4y7w9C0FpfkccZnrQ4\nBjFqJbuwwkcvRym+kvNq/rtPhxpven6BbCr6EU6i/3SkEcLr8MTOMxP8bapd\nWXXKCq3P/YqWru6j0G4mowvDLnuaPmlwBKziEucxkRjMnHYu9jfo6L3u8ova\n14HMI5tPqSAcScvSkjIMamy1BjiOA960CN4zLWMqotl18QN+YKuYjuN6f5qn\nh0ZlgtsBSpCBs3O2Olge1lDkO7pp2fIw2cVvzgD2R2Bv4jdGgxaFH4VhTd1E\njgCu\r\n=Zusw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "02af462b507aa7a24f5d3201178434b181bcdabb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.2", "@webassemblyjs/wasm-gen": "1.8.2", "@webassemblyjs/wasm-parser": "1.8.2", "@webassemblyjs/helper-buffer": "1.8.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.8.2_1550161308292_0.6406853248842184", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "@webassemblyjs/wasm-opt", "version": "1.8.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.8.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "54754bcf88f88e92b909416a91125301cc81419c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.3.tgz", "fileCount": 7, "integrity": "sha512-j8lmQVFR+FR4/645VNgV4R/Jz8i50eaPAj93GZyd3EIJondVshE/D9pivpSDIXyaZt+IkCodlzOoZUE4LnQbeA==", "signatures": [{"sig": "MEYCIQCi7oG0EVBVoGJYsNKhZWvoKDkdVT48NNfH2wtRObqjNgIhAIiI0QrqsHVw45SPfvhJgYJkxhxEMIZ9y56Q/t2i/jke", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam67CRA9TVsSAnZWagAAI2IP/2FXC8JgsJw/4uKkTlKu\n4zLkyOuwSDXIL2nGUBPeuDbMbjS/k7clXEk4mHwTmw3sN9xeW0ExUT030Kiv\nkXXzuGKQUixRs9zzfngy3rgQ7Jr7UOvM/qJ8FRDV1ha/IccKusu9WQ5aqGKZ\nH/LPb6q5RBtogxmYFRdz7PnKx/T2nkAEFU/LuywbRIaeGboFmc3YNoGrjCHk\nKNUGID0IRxGUSa3UZlH2fPG5tLJcwvj9n+8egb1F1b3GoZ8m8X4/gDA0f97Z\nIeNZRnIeM7Aec1HIU+K26lqkOzG1Cf4x6uTnttm7nL2uyTsA60p6QIBZNZBs\ngHjGibkvr4bnJ4LJU2T+HBoSPNSeuKsFRl1ZIZt+AQAkh9CvEeoEL6iDA4Cz\n+9CP4NoIGc1g6C+9BmsEKs1uHXPAC5g2QRZzfrX+MtMyBlQ2nCvweePDD2xt\nzV47w5tTeDTsKQDaT4HYigzviR9802YFmc/QO2wpm9/AgAdxjQ7GlEAo9Aqn\njIrUveP6jeldw9gKpdXxHbd9X4I+1+ZT2mvG/ByhzN9mGaiP8MdnNFt4St6p\n7rGM7YqIXRQRiP1k8BzV6OJ6fO0yGsrPXDp54qYnsEeMBN8J97FWViYZKVz7\nRquNGUlMSlP9BHur9P42Py3vx8xkyHgGXTEACRje2gLWTk2vNfLwlyGgcxsC\nV5YO\r\n=7DEU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "e482c7ec291d61fc46e42c93d3b8ec7517b629e1", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.3", "@webassemblyjs/wasm-gen": "1.8.3", "@webassemblyjs/wasm-parser": "1.8.3", "@webassemblyjs/helper-buffer": "1.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.8.3_1550479034558_0.73024282586365", "host": "s3://npm-registry-packages"}}, "1.8.4": {"name": "@webassemblyjs/wasm-opt", "version": "1.8.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.8.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "238c11d2342fd1e366103799c64ce62f1238f89c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.4.tgz", "fileCount": 7, "integrity": "sha512-f0m+BuEYY4Ej+Bnfd5xSzfIVBx1v9LyO0PIESncEBEGlKEPlq06hcEDX8d9dO1ZIWJi2wCLK62j2lDsHhIzn9Q==", "signatures": [{"sig": "MEUCID+4q6q/hcYILLPGxdVqtLSjZONbYOA68cTeDv3i/2CiAiEAobZzXBsosLb8E78o4EY5VCGOmAvI+j0oQPUXV8IxKZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyACRA9TVsSAnZWagAANnwP/RSWktLKV7NBj3Xv3E4M\nwXd/GusGsCMZHNdXjtJ3B+krUTFBqITwu669Wlw0qLg/697qfjBXZrSOOCH6\nnZ46gZHc1F/n2CpeRjPwlbpDEve9+XUtQS1pz/l9JqQtuQ1ytatxZEetJ0tB\nsr7U7ogl3H5Ci4FR7nQXOAUl0jgTr9YcQoficQFRYhQAmVs2/Ry47UFrfOhF\n/ohxv5i/b8PKiHnvZwbM2NdDw2W0LS9HNQRzpnKs9/JC80jm6vt3IqrQ4M3/\nVmL5gqxhjR5RBc6g4j+4lqnb2VjriqZcM3wQH51l9QtEvb5BVny/fVXv6Ydt\nyqPmqaWQWOHH/xXpboVIx3uJBaKmKSsXaIojCBqcoWo5wSU5eGcvdEYaIn3h\nH5YAN6H77RrFr/jaPIevGyd+yi+RqMBl3NX3MVu71Tur3jCdMd0n6Tblik2E\nm+U1RQseHZ1YmKjt8qSOxD9qu6BXg15EhsMtsDtz0hWn1jaPsCzjz/sWpGLW\nMCUoDQWjdMDYosQZ78QlGZ9mHHrBq8wxvSeFWCu2wxzfOyIgKKbPAVnNi8Kc\nJLBLvW0L/T2VxoJMwjG2gdi0y6737gI4fIr1Aa4YPjYW9aGN2+vIUzygyDv0\nroceGMZs/KNWewhlqbo2kqQ72AmAhv/lBVDqdG8GYU8dBX671+dE92dNFPcA\nKASz\r\n=Hw+q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0154b989cc9b41c695724a361b3aa6fa19c5b032", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.4", "@webassemblyjs/wasm-gen": "1.8.4", "@webassemblyjs/wasm-parser": "1.8.4", "@webassemblyjs/helper-buffer": "1.8.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.8.4_1550597248223_0.2705296591161601", "host": "s3://npm-registry-packages"}}, "1.8.5": {"name": "@webassemblyjs/wasm-opt", "version": "1.8.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.8.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b24d9f6ba50394af1349f510afa8ffcb8a63d264", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.8.5.tgz", "fileCount": 7, "integrity": "sha512-HKo2mO/Uh9A6ojzu7cjslGaHaUU14LdLbGEKqTR7PBKwT6LdPtLLh9fPY33rmr5wcOMrsWDbbdCHq4hQUdd37Q==", "signatures": [{"sig": "MEUCIFaHBgC6k82WohibWg0MUor64W4XMIPtk9wbLXh5pjYEAiEA5VOsEANGu8YdgFLp56cnhyBibS4hma+ZzehQRgx98OY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnUwCRA9TVsSAnZWagAAf6QP/ivnXRKVtpFb4VmR775y\nPs1syOY169ECvTbi7Rn58G7YIpzKnDqIt5h/w1HXLnQhUxhVcpkJxzMsY4pU\nmqD96sUsYkNDVfB5Mnbh6IaqYwD/7MGb0ID+4gFRRz9hw70514qHkHa7aYmJ\nscCZN7DUhD5293SanBo/l2hAAhFFkxVIHBi+pszUZZoC4LdhNaRh57Zl5d8L\nka9IECwL1F/A9HPDdw95SVxytHhUPDGR8uFlgtIgs8RPSTb0d0ArrGNZcz8p\nPReZagyA0pXzpqnV6wYkpsdxSIMxr0eIE3K+v7Q6snnmNUenZyCdtsa6FED8\n/7liBmGeuV35T/PY21fQMq+blPAgqy1XEbXxeFW+uAoY8uezhTcP/IHxfEBQ\nn7+6EAvQuXB0IULWV65FLoUEUrzkr1+EUFbFDfsT6mnNe3EdNUEoLIDH4R9p\ntpS129ACnYlv0D+hlUwIXjThTPkLcI/7Na6DbB/6boAINTAG3VC7ILlKr/P4\n6PcdDep/nZS0M19eupkzrPXuM2EcPMYcf3/zsP0LMt7Q02ZggebzyJdg9Qz2\nc0PjneQeAO9XgnsnO5Z1CiIs5GLXHMzioecWQyCSHG88dS8s8GLP1OK6mUVw\nqEsKFDgF+3qLT88+lofonK8+ejpkbMnvdMHamUm1Xfoq/t5hssM2YTsv2u6c\npt61\r\n=QXB+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/wasm-gen": "1.8.5", "@webassemblyjs/wasm-parser": "1.8.5", "@webassemblyjs/helper-buffer": "1.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.8.5_1551004975896_0.7024777292661424", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.9.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "2211181e5b31326443cc8112eb9f0b9028721a61", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz", "fileCount": 6, "integrity": "sha512-Qkjgm6Anhm+OMbIL0iokO7meajkzQD71ioelnfPEj6r4eOFuqm4YC3VBPqXjFyyNwowzbMD+hizmprP/Fwkl2A==", "signatures": [{"sig": "MEQCIAf6StQDJHJdo11tDGqoce/gxmrLDNTJitUp5H+CHNB3AiAGtxVayvcKgYvFO9u4PiJ4uYU5/Ihcyh7oNILrauesuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgehCRA9TVsSAnZWagAAfTUP/j4KyLnsjf0hIzGO+Wj2\ncFcWn87EJWx55ZXIe31Qm2V51Mksoa3L0xKW1nRhk19CXXDnbNZJGLhE9z9V\nSfbI2iStXfAOWJWRQ5POEMUeacW5cidjtcgqggtlzHN6vcp7saE9fRPmJzbp\nTD3gj/Qnp/W1OUV8bP7YeAMgJ3dahsEK+rCJYonlJnS7+084+Lii+DL9J2iM\n5v/zZsk68nZVIT62SWPBI7raB6ukflHbyfy2Yy6QKPWEydpWhvYnk5YuekXH\nYuB07LaOudFq+5intwwS+98VZhGRnSQDS9t+6dtjVdgx5BMte3anqYUg0M6m\nSyG9WRE5zsJbDkPYrFIsJnrvQ5NPsQ7BBzifjVqy5La86D2XkZwGv+KJOXNF\n0EwiSvvxNPGttzVbamkfE/p/WxivkQTh9y/JyloUkl+1IdzcUpR3tJ2L5yGB\ngZsl9CTHsiFMDd39yU8oh0vzD52XCY9RW9+7CnBUyhKcDR02K+YPaPsEPCJJ\nccAM8Byo9xhCc8dYk+atZoSKlsAUnJFkkVf9GRsacgglo+rrMjj7ZLc3iY81\nZ4eAPdoj/Qgd8V9vnm0qj6i8xAYw3Brvmbq1/OEC2y3b9Wnnpu3UeB2oHBCi\num122hHUpDfE3Imuj7YsIPgyhvVq4eNhjB4Lp9Go52CwYEYkTLqY00/R1bPs\nQE8P\r\n=MHIB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.3.1+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.9.0_1580599200816_0.3457176348274207", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.9.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fbdf8943a825e6dcc4cd69c3e092289fa4aec96c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.1.tgz", "fileCount": 6, "integrity": "sha512-gSf7I7YWVXZ5c6XqTEqkZjVs8K1kc1k57vsB6KBQscSagDNbAdxt6MwuJoMjsE1yWY1tsuL+pga268A6u+Fdkg==", "signatures": [{"sig": "MEUCIEpDsYV94bHWdvCQLbKDHkXzggI0l75a3uACe7jXahpgAiEA1K5lobrFgOzr3tmqobWBHHbwvHIekLx7wQm9mLl/l3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNojCRA9TVsSAnZWagAAzaAP/3EC1vi9M+kL0t/gywIs\n++ka4C6ug1xRz7lB+3gQ5BGBg9ihk55gU5aqSFOIvvKfF26ZBmEcmdFlI0y1\nNM1gvFNCqKkZGvlrKXm/DNZD1KlBPvlOnXXQciSfLpkRovrmJR9O0KUcRrcq\n/y50+nMMk1hwEll/47CC3hshP67ssluPym9wLiYN7Xx4S0nbQ3jWdUJ56NEF\nCLi2XE62fAl5EsNWuykdLXlsrAXBovHLTU5JvCULMJh9xJvOAWCoAv68HCsc\nuGe9aau0ei1RK6f2lx+nYTxvb7E5/Ye2XKE5/O9M590LPdwOGor1M+8F3gwl\nFHk14be0gHF+5wjO2UmGr8gn8/PBFgr8HkHIrIqQctElOjFnohCE6MIbFlnW\nS821S9sw8aK/VvLOQaAqpBwOOeOaIOcLjM9ozWKKUhSf4Jaeoe9BYDgqSekV\nfNvhYzgPdvQ6H4DtAsqZ4xY8odxGlwQwcGOO7bZYKZ+dfwXnExf3LD9S7BNx\njVpRtRM0/06GO/wL7PRPAC0TDFKfcZE9sDbWikZYkN74OmZDj62x1MHIMgaI\nOLh1JzA1PnWjjP0Prma7pM2WI7L2tefxKfVnPYgq2A5mXD8UzOlEVTDQj/nZ\nqvUG0G0f9tmMkZ+L/rUYN/k3dRz0dbwahf7/CL1Q9xKk1ADtvxgGOG6omAKe\nyOb6\r\n=Eub1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "697a5f63048049e9ecb3205d789c1e80eaadf478", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.9.1", "@webassemblyjs/wasm-gen": "1.9.1", "@webassemblyjs/wasm-parser": "1.9.1", "@webassemblyjs/helper-buffer": "1.9.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.9.1_1601755683332_0.014333793516749571", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.10.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.10.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "cce988529fd85cce147deb46bae8d7edcde3222d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.10.0.tgz", "fileCount": 6, "integrity": "sha512-HWqV6fCU66gAwe6SdLedGsN6OWGlrsvnylvrtLjxnCJnQ7nHZghyASoc+X/oeYoIZbaVbbtfLwW1kBxyQC6jOQ==", "signatures": [{"sig": "MEUCIGJ5gxNfjeyfvJ0QE2CJKrjUHxa/Vpkw9/s0Fq4kkioWAiEAnowfIdtBa9Zdu9gkQFlivBp/RoMlK7BY1klUg4s0TBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zReCRA9TVsSAnZWagAAuCMP/25kQJpDkW+htMXNSnbt\nQNiSJYSe4rW93a7xX/pEKUXoLCc6S67+oJpgs6RTL2NyMMKfQfxclnGzrqmo\nHfXbp6ct/xzjEQyr6iaogjjompJ14rpA03u8Eltpg46u5F2CxcAl2jjzR32E\nyGbM6/0G+wl3Th3lcAtqEetIcQmfYg5dMBhEBZfCAZjBP+Js42mhJdhq19Tk\nIY1VqJLFYodpz8mmHmgPkinGw32C62uQN0dGCRooWwhGCivA+BlLEg5WDzm9\nOxJ/fL5BnhXNvLpTuOPZ9fy0Z77vP7UB8WpYfEipDS9yU+Vl6xtk/KW3xb6G\nQErspTHLMVUWVpGOQ4YVcY/BbXrDSljkTCDmxoWURDz6Hg8UOMTDy8vJdJAF\nNBKouec6QTzEmM4KWnt/sjdt3TWWUr2NwJBkfKLrJYw+jHHs6jXS/q3XKWAf\n4/hKLFw8B1+9YzEUkLkhrgz8GnQbPoAyhZV4pifyVxYlgNzyRZpTwAyHIStv\n/EXRZMIMsj50YtT3Qvvr43FDTek7nlyq7eysZY4jrsF/U75vIykx3U+C4CSB\nBw6BW7jy/OPsslSeGmEIzFK+SxPGBtoDJ6upsiOM3bCIsXggNwfu0yVHSwf/\nI9Z7XVVlCtAm2subJueln6s/6gru7pPbp/d14751Dn+bcaTfxv23n4nxouIN\nj3Jb\r\n=ZPdN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "d00b899ece0242275e2475e75232ae18ddd03aeb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.0", "@webassemblyjs/wasm-gen": "1.10.0", "@webassemblyjs/wasm-parser": "1.10.0", "@webassemblyjs/helper-buffer": "1.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.10.0_1610036317574_0.854581054318369", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.10.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.10.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a4c70664ade150f4a31ca9ec2f1e003290bbb33a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.10.1.tgz", "fileCount": 6, "integrity": "sha512-2IPKZPFeTFOECh/WY2MndUAc4twjB7Bgj2qYM+ElGkXMS7kHVucDNBIHhc8yrNA6zr8g9tHNynaROl/lHT8E7g==", "signatures": [{"sig": "MEYCIQDDLRNn3irWwTbhBpiHXescChy5jk/dip5BQssBGCoz6gIhAM2OS6RJkEyaBlFr41T3kMOXsawoMTeYxKcKrBeUZ7Vc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zquCRA9TVsSAnZWagAAXWcP/33Wo8LUHhCAJBaykkiR\nSOpMAW5BJsMQerDu2g1+sL/3iQSxN0dOu3u7nHDQTDD5xB4seKEQx3yuU+tl\ngFjosFWY0nP00M/2vRc1+CmvudC/j2acG44Ml8dAbpA81ch+2nZotGYZjRbY\nM+OXNlXEeyXlMrp5NdxiMvY2OOhpHIvLBnNuYl40Le1y4vEWXNS6tHw9xr+y\n5RR/7Cp9XyYUNHN54tE/ghVnSAI4hX3Psc4GHkimOAgdq5eH0m8WJO4SleB7\nAiuWeey6ODu27adS2JLs3YyzetCHcgiB/DipfO+Gfyftz524kW9aBF6WOcgO\nL8PtdW0Gfq3HRgMR8IzrLyhezwK2sUdgdTA4yTO+UkvVoPtZSPVo3NkF+S+S\nekyBi51B2qWYAcUNAuE27ovfHkoNL8xsu2Nj+klDyeOdczyYi815RZOUAtf1\neoW2zbpeP5KNU75cOepupWsm6oPV0g8nyAXOrO1IUxNM7kX+lbSbgXLfAk+x\nVQmXS3zDeb02IGJSv28w+GX59qnhsJ3/4K+BUY2dMsDoqVyC+/n4rSVHHNt7\nlgmRajc65XGmCMUxiJvhnQYx2PjQ41WRC7iZ3XSnlRjf+WXm6SCbODQWLdg4\nyXCNvCphzKLaac24RRA1eQQWaKqRrbLFM1yrFYBf9xbjS/a1YfoFqRfZVxdP\n+R3l\r\n=MJN5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f723f2cdd9bfccb5e199962dd8c5c09bdb0faca4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.1", "@webassemblyjs/wasm-gen": "1.10.1", "@webassemblyjs/wasm-parser": "1.10.1", "@webassemblyjs/helper-buffer": "1.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.10.1_1610037934001_0.14427471790524438", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "@webassemblyjs/wasm-opt", "version": "1.11.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.11.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1638ae188137f4bb031f568a413cd24d32f92978", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.0.tgz", "fileCount": 6, "integrity": "sha512-tHUSP5F4ywyh3hZ0+fDQuWxKx3mJiPeFufg+9gwTpYp324mPCQgnuVKwzLTZVqj0duRDovnPaZqDwoyhIO8kYg==", "signatures": [{"sig": "MEUCIG2xp0f4xRDagoi6uziDsJWZSoIOefjqB+sfS3O+5r+vAiEA9PpHbp9wUwgucAT28Uwa6IuuHBaKS5dMcMxeMch9zRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907qCRA9TVsSAnZWagAAYucP/1PAzsJtJ64NOA9Zzuin\nOnVnkkJB8tInzBeQacHr4LsZ3x3CxMITxZ/p/CzYRHaHGgu8BzVMBt08iP+5\n5Tjm2PZ+S9HB3UbmGw4lZjzuIPRqpi00lCOFzjpg+LICNcao127SNd/GjKM3\nHKnIN47SmTs4yd9uX3ksJ+0iOOyHffp2T+6zrE4cGgMT1tZXBDaaju+5rTPi\nZdN0zYoF00/gl09zK+5bveMyVUyewFSp8PrfS7qBPKwzNsci6qAnoG5fdRdz\nLcQcxzyLHMCRQTfM6dZ7VaGJBGwpshhg/zPULjSxamoFOE4ENygmMRdpT3VS\n6+WAMZm5d7g2KFkz8rJS4EeRn8HvrkTn9y8VZvFNIQ9tAL1nTCneImC9HtRY\nKZNvBp6JCfGDK3+sHLHRupDr15hBXfJNnUwfwo2a1r77wigZqVxxJqhzyuB1\nrGLFvj5cS6S2xRgq1LBHF1RCqq86wC+vAyEUvgAh0BucCPgx8686ENJj5rhh\nv7da40Qu8QHiSeY+cDpVgo1J0ChrbnJl27ya3vRCs9ZTWKMJhW0R8+IVLGNQ\n1sRSOoIroUQ0bKGSH5KCy6XzCgS41wJ2n3SqIcb/uK/YZYYazQUNYwLnwxCi\nBzb5Nw049sgoMw3YcF0XAq6BB9I4U62zvUQBsu6WZfaQrrtvsqPMvL/qVAKA\nwCTV\r\n=CxHz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "2646d3b7d79bba66c4a5930c52ae99a30a9767db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.0", "@webassemblyjs/wasm-gen": "1.11.0", "@webassemblyjs/wasm-parser": "1.11.0", "@webassemblyjs/helper-buffer": "1.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.11.0_1610043114183_0.29712582992637726", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.11.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.11.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "657b4c2202f4cf3b345f8a4c6461c8c2418985f2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.1.tgz", "fileCount": 6, "integrity": "sha512-VqnkNqnZlU5EB64pp1l7hdm3hmQw7Vgqa0KF/KCNO9sIpI6Fk6brDEiX+iCOYrvMuBWDws0NkTOxYEb85XQHHw==", "signatures": [{"sig": "MEUCIQC/Z2fXuXKmvtdnZLad6Sw7B+G+Xa/nc3LVgnJsFJIT5wIgXOMYGkhj+bmYyexicS1QZzV9RdxYziG4ycD9CoIkBaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sFACRA9TVsSAnZWagAAfcsP/iE7WJY1azL3eldZwEdO\nJhJo66LMjXApswfIRkiI9yOF5Mez5vIt9EsMic+zYz4asIpUfzEzYHfFoHP2\nqpO/a3P+fEdDuApDzr8jScjEX62oLZU5oxWMb6eEICvBqTxLWRgTo2jLwi8v\nVzqlobsMc97o/ODG+URQNwfhVU+aw0gDdNh65MLipUwdYd5jb1K4/Igk+w9R\nGXjU4kbRn7P7bnh0pl2Rn5cxJJPjH/y1/Q/LOyhCyVTAw6AS5n3csnLvnEmj\nRc4f1MoR0v3yBUqV8njTxYsQ1OSzBFNg6PPncZ5sCz73DDXNvK+/dKxzoPuu\nM1uk6m1qOqmvrstQhFmOuX1mc3YkcCuhqhYGwFwu0Y72qrFw4rQVtPE25YxE\nB2iHFilQURvUXAbQqV27SpV00qH3b1c8tDWrop3UAki/+hg4igYwIzcBUdXa\nRPHCnxzVZ9RHill76sE0GQtnH6QZkAYzmCdTR2fjmRVlxjlzRbalDCiofmAs\nypVLguhbwPwQJhIRhbauXq9A7M2nE+YLtuyd6E6bs3ySHET7KtLvitXyxe3l\nEFxC60mFjb07PSr/+cadna2mEaR82cayAElynQqxqz3EwhjOjOWsh2ZQsy57\nSqjp5kiXvkhTomBu0oWCqQXEog0YTtC+sQ0z+PiyR/fXvENbgO6tJurjZPRx\n1LaP\r\n=0dng\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.11.1_1625473343644_0.5059596766383152", "host": "s3://npm-registry-packages"}}, "1.11.3": {"name": "@webassemblyjs/wasm-opt", "version": "1.11.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.11.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fbb91be45495a22b56b821e15852a18cfd882a17", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.3.tgz", "fileCount": 6, "integrity": "sha512-3C3rvG/qjoFn6YF8ww2hP1i/Rg6fUBB40c5zEy9Mpf4UVITvMTVMOya1dgw0siPzJ8HFfReTCcjFOQBGR4eIdw==", "signatures": [{"sig": "MEUCIQDf69wHS8kZaC71htS91sq/ZBVcp+35GIOPZqwTPy2hvAIgN8gClVOrws/Rhb1ZG1inHuXsR3MYXub5SdvRIGK8Igo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAFxAAgpInXcm0/Bjeue7K5mSGJ1F4tH51QB3kdf97DNWDIO4+78q4\r\njVC/kOMdq+Jbgo4F8cxZ2pn60HxCzoz6hpAVuCL2jUX5DYVGSKCxvFPJ9O3W\r\nav+JUUeWd3ild+PMccAzHZ2mcLxNZ8CXiQD49qMfvU7GVk7uqUddms7DRg/M\r\nE7ewiLpyTsfrQQ5Kvtm8KkH7MudaBWCsCgBjSAFwN8iR/2Uyrh0cXsOJtMdC\r\nNAa4Tzf4YgwTkChc1K35hb125J6sO6rws/54sCqVbDvkIfXx2Tx0wwtrjbZ7\r\nY5gv9JFHZFaVZ72iT1yuLFxEw3xDPp2EKSMhdSCotgR7WG/BLodvUQw3xZ8N\r\nlYEtTxbvCMAkgJaZKoV5nYsjo7m2Dty1fVxM4+FqVspCCwnK+F3r4ZIF+Iv6\r\nA1kQZnkqMTG+wUUdgBAruEny0rV5G5LAZUwaWhid9Kq+ArHHRLsuaZROBiih\r\nphzU0Z72jjd7LUAO7rOioF01TDyy8bi2+TvLGmMYYhT3mVP8T+Gytwfs+tGF\r\n3uytJB/FuGOhaHOXoipFH0a76vrzUlLt0dkQEmzccCDEGQQq6fcmnao0aNnD\r\nZwBibgbrVsXeQYVcprrjBSeOKYBcfFUzfVXhY91HEm9P+d4V5Pi7hY5+OLhW\r\n7k/76hnmEBJAGqdp/B33ucVqIOssRSBFMSM=\r\n=HquL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "5fd2425602b752576bbe8089c343d5d70ebc861c", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v16.13.0+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@webassemblyjs/ast": "1.11.3", "@webassemblyjs/wasm-gen": "1.11.3", "@webassemblyjs/wasm-parser": "1.11.3", "@webassemblyjs/helper-buffer": "1.11.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.11.3_1656762777971_0.9050328843517357", "host": "s3://npm-registry-packages"}}, "1.11.5": {"name": "@webassemblyjs/wasm-opt", "version": "1.11.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.11.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b52bac29681fa62487e16d3bb7f0633d5e62ca0a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.5.tgz", "fileCount": 3, "integrity": "sha512-tc<PERSON>wlIXstBQgbKy1MlbDMlXaxpucn42eb17H29rawYLxm5+MsEmgPzeCP8B1Cl69hCice8LeKgZpRUAPtqYPgw==", "signatures": [{"sig": "MEYCIQDVIUF2FDGQMg+mBAgIU1Qz6BD9zMDy8dl7D5a45bHqlAIhAPK8FMrNdnJyUIERk1K+UqpY1A7/ichqMJ9mRmiEPJIw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6BzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr50w//VRdZC+XBAK1tnoNvzNxpFMpTKzL9kABFOa5s+07BAx3qcMBv\r\nBdyVcoX73Cxp62gy+K/FdBIE8Z6zO+6oFU5szyN1qtz2fe3xVOaOqSb5hbm0\r\ny4d8kZ4zCWPW1O3hIDDap6VhsjAChz7648X75JKSdGa+7T+EZRoKUrF+Wswr\r\n89BSzBO4d69ov6n8p2lBeU57NSOZFIad3+yimyRNFgyYEmjl/C9DoCFBpIIA\r\nsoLoy9MzCKPtI/P9mnIvq3hGTflcnazAO1493AV86cG8mwLCa0Ky1GZRf4Nq\r\nh7p9Z3LK4YqU6z2FFv9OL4pDgjFCKV1MPKMKhzd4ourTwWqO9Cldl5r5JXpd\r\nWtu61ufYOcHb7z/+mPu5HUohhlj7SQ/KNoHFk/MWw7L1wC1jCnBKsONhRHwE\r\ngDFN/9wRzqDXQuj34KAArybJ0hg0QyDUqHEr46Cg/yBH6O/JF+zCppQjcq/B\r\nxRhYdJNeps+TAjJ/sVOrQWFBySMSq5MEiB4X0GJ49HzKdWwp919vLnn6kTFR\r\ndgY4BpLpDvnBCq0CxXvZQ5z5fFMfFj0Abesy+tQjATSQZNVILOFL4UgJwt+5\r\nACkibp9/dbhd+h/l5qD2CFBXciKDkovOJPuyPE1YxgmP7eb3Zap1enQEkmeD\r\nWtjQCXpLjyAEWYlf0Csik4WYuklFQuuE0Fk=\r\n=j/ua\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cc856f3cc847a69c31e92ceb2c6527e1d30a9511", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/wasm-gen": "1.11.5", "@webassemblyjs/wasm-parser": "1.11.5", "@webassemblyjs/helper-buffer": "1.11.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.11.5_1681629299703_0.38160572388895897", "host": "s3://npm-registry-packages"}}, "1.11.6": {"name": "@webassemblyjs/wasm-opt", "version": "1.11.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.11.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d9a22d651248422ca498b09aa3232a81041487c2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.11.6.tgz", "fileCount": 3, "integrity": "sha512-c<PERSON>r<PERSON>uLRE7PCe6AsOVl7WasYf3wbSo4CeOk6PkrjS7g57MFfVUF9u6ysQBBODX0LdgSvQqRiGz3CXvIDKcPNy4g==", "signatures": [{"sig": "MEQCIBLOhbVge+rn8dgyKNhv4zwyur0L5opOx2NNw6aIC0IYAiBN07jyLVn6Sup57+wJBL/bwssK78V9W6RmoEwO2JlnNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7081}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "58d40904ea7de2dd17f6f8d894ebe611b812a4db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.6", "@webassemblyjs/wasm-gen": "1.11.6", "@webassemblyjs/wasm-parser": "1.11.6", "@webassemblyjs/helper-buffer": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.11.6_1683645086668_0.9274828963581176", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.12.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.12.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9e6e81475dfcfb62dab574ac2dda38226c232bc5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.12.1.tgz", "fileCount": 6, "integrity": "sha512-Jg99j/2gG2iaz3hijw857AVYekZe2SAskcqlWIZXjji5WStnOpVoat3gQfT/Q5tb2djnCjBtMocY/Su1GfxPBg==", "signatures": [{"sig": "MEUCIFg7cFkqRPWK0psbofm7eOpsF5LirrBsMIvMn872XRHbAiEA+OIWKbyVS1iDdxgIM5zuT8NdQWUvm0zM5GMQ3NTsvAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14437}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/wasm-gen": "1.12.1", "@webassemblyjs/wasm-parser": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.12.1_1710325160119_0.7267140643668759", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.13.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.13.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "eaa4e9946c46427fb025e837dbfc235a400c7581", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.13.1.tgz", "fileCount": 6, "integrity": "sha512-SUMlvCrfykC7dtWX5g4TSuMmWi9w9tK5kGIdvQMnLuvJfnFLsnAaF86FNbSBSAL33VhM/hOhx4t9o66N37IqSg==", "signatures": [{"sig": "MEQCIAMnl0orSaWdounMm090xk2/Y9AjlPBhbj8z3LXgqs0RAiBnXam4M5YKZUqoaK/t1IDCJbEtr1yjTOrb4TdjZDFm6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14437}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cfe35c57093d414839b9350398369b78d97815b4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.1", "@webassemblyjs/wasm-gen": "1.13.1", "@webassemblyjs/wasm-parser": "1.13.1", "@webassemblyjs/helper-buffer": "1.13.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.13.1_1730912132648_0.790260967513317", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "@webassemblyjs/wasm-opt", "version": "1.13.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-opt@1.13.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "125c96f83fff103775516113d4412478e7c14004", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.13.2.tgz", "fileCount": 6, "integrity": "sha512-d1NVTNbUqFL+NOHlsL92UjNCCVUqtruYm2017IfvmIbWwih16xLraRixSd3oY/rbllkO1sWSB4OxhZ9xv3kybw==", "signatures": [{"sig": "MEUCID0s1jDbCMLTHiRYhsL+GL2L4W598csjBbb1Ms0kGkN+AiEAjd+770e4pEzrxuUtQ5hGCB5OZDHRKpuH1RRlxLf0fMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14437}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.2", "@webassemblyjs/wasm-gen": "1.13.2", "@webassemblyjs/wasm-parser": "1.13.2", "@webassemblyjs/helper-buffer": "1.13.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-opt_1.13.2_1730929437959_0.1344111218568964", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "@webassemblyjs/wasm-opt", "version": "1.14.1", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "homepage": "https://github.com/xtuc/webassemblyjs#readme", "_id": "@webassemblyjs/wasm-opt@1.14.1", "_nodeVersion": "21.7.1", "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "dist": {"integrity": "sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==", "shasum": "e6f71ed7ccae46781c206017d3c14c50efa8106b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz", "fileCount": 6, "unpackedSize": 14437, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA6wdPV6N1i4m06b4q2Dj1rnPEWo+fJxJtBsgPeFzg1qAiEAhWzXfz9NR1ahXQ9zoJv4LC/9wmLdyWKgOlZAOFiK4MY="}]}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wasm-opt_1.14.1_1730930020566_0.21990666538575487"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-02T17:09:58.975Z", "modified": "2024-11-06T21:53:40.949Z", "1.2.7": "2018-05-02T17:09:59.158Z", "1.2.8": "2018-05-02T17:24:59.213Z", "1.3.0": "2018-05-04T13:31:14.213Z", "1.3.1": "2018-05-07T18:09:15.265Z", "1.3.2": "2018-05-08T18:47:25.600Z", "1.3.3": "2018-05-09T07:53:22.854Z", "1.4.0": "2018-05-09T14:58:38.044Z", "1.4.1": "2018-05-10T19:44:57.838Z", "1.4.2": "2018-05-11T14:13:16.936Z", "1.4.3": "2018-05-12T09:21:21.873Z", "1.5.0": "2018-05-14T16:47:24.912Z", "1.5.1": "2018-05-16T11:25:54.474Z", "1.5.2": "2018-05-17T12:50:57.300Z", "1.5.3": "2018-05-21T11:08:41.431Z", "1.5.4": "2018-05-21T12:57:20.931Z", "1.5.5": "2018-05-24T08:02:18.133Z", "1.5.6": "2018-05-24T14:22:00.956Z", "1.5.7": "2018-05-25T12:54:44.906Z", "1.5.8": "2018-05-28T12:49:22.665Z", "1.5.9": "2018-05-29T13:13:53.462Z", "1.5.10": "2018-06-01T13:21:31.019Z", "1.5.11": "2018-06-06T08:58:53.364Z", "1.5.12": "2018-06-07T09:25:50.217Z", "1.5.13": "2018-06-30T13:45:00.885Z", "1.6.0": "2018-07-16T09:05:43.793Z", "1.7.0-0": "2018-07-18T12:58:32.321Z", "1.7.1-0": "2018-07-18T13:09:50.748Z", "1.6.1": "2018-07-18T13:51:12.540Z", "1.7.0-1": "2018-07-18T15:00:43.006Z", "1.7.0-2": "2018-07-18T15:16:19.966Z", "1.7.0-3": "2018-07-18T19:13:52.138Z", "1.7.0": "2018-07-19T16:01:28.945Z", "1.7.1": "2018-07-19T16:32:12.075Z", "1.7.2-0": "2018-07-19T18:10:19.197Z", "1.7.2-1": "2018-07-19T18:25:05.172Z", "1.7.2": "2018-07-19T18:34:03.566Z", "1.7.3": "2018-07-23T07:02:52.628Z", "1.7.4": "2018-07-24T07:02:42.664Z", "1.7.5": "2018-08-16T14:54:38.075Z", "1.7.6": "2018-09-10T14:17:53.274Z", "1.7.7": "2018-09-19T05:39:37.785Z", "1.7.8": "2018-09-21T07:37:51.473Z", "1.7.9": "2018-10-18T16:42:58.640Z", "1.7.10": "2018-10-23T09:38:00.545Z", "1.7.11": "2018-10-30T18:01:03.415Z", "1.8.0": "2018-12-11T07:24:27.169Z", "1.8.1": "2019-01-15T08:21:26.636Z", "1.8.2": "2019-02-14T16:21:48.439Z", "1.8.3": "2019-02-18T08:37:14.804Z", "1.8.4": "2019-02-19T17:27:28.331Z", "1.8.5": "2019-02-24T10:42:56.067Z", "1.9.0": "2020-02-01T23:20:00.967Z", "1.9.1": "2020-10-03T20:08:03.483Z", "1.10.0": "2021-01-07T16:18:37.718Z", "1.10.1": "2021-01-07T16:45:34.109Z", "1.11.0": "2021-01-07T18:11:54.288Z", "1.11.1": "2021-07-05T08:22:23.748Z", "1.11.3": "2022-07-02T11:52:58.155Z", "1.11.5": "2023-04-16T07:14:59.850Z", "1.11.6": "2023-05-09T15:11:26.862Z", "1.12.1": "2024-03-13T10:19:20.282Z", "1.13.1": "2024-11-06T16:55:32.866Z", "1.13.2": "2024-11-06T21:43:58.152Z", "1.14.1": "2024-11-06T21:53:40.767Z"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "readme": "ERROR: No README data found!", "readmeFilename": ""}