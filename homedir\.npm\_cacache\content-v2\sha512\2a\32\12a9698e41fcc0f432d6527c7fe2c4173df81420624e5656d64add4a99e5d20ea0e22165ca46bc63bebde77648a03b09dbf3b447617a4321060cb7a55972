{"name": "loader-runner", "dist-tags": {"latest": "4.3.0", "old": "2.4.0"}, "versions": {"2.0.0": {"name": "loader-runner", "version": "2.0.0", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^1.10.3", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^2.3.4", "should": "^8.0.2"}, "dist": {"shasum": "d8bae1bf75761ef160a687ec92347483b57b152f", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.0.0.tgz", "integrity": "sha512-7pJFkhmjYHU9IIbQ9NEgV1JVlKRvJl92rTQXK9jzSgvFvtMtrPatIcjCBkE/SG64jAfZqvm0m912LWEa/+niaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHujlLnRFkCMfUS3Dsn1yuxYyiz59+ti/fljBq21lhnbAiEA+wwnFG4p1Jo3BBl2ofR/nWUphzDKBkuBiOCwNOIqFag="}]}}, "2.0.1": {"name": "loader-runner", "version": "2.0.1", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^1.10.3", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^2.3.4", "should": "^8.0.2"}, "dist": {"shasum": "504717c0634a484bc7f676feaee6178644cacee6", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.0.1.tgz", "integrity": "sha512-8RHSLuopKBXdI8p3Br1zUIl9aNnXQosfKZhT9L9qZ8v2pqvjQDInqTYTjKdkZ+wUxwVr1feHUA8TeG8Dpl1Dmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXWMTVcKPLVLoIKGZ3u6dB7ozWu6dJjvogfttiIUD6gQIhAOYdJYK/eBsFoN8zNdU88j69Um0J3kBK5j8mrFOaYaI6"}]}}, "2.1.0": {"name": "loader-runner", "version": "2.1.0", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^1.10.3", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^2.3.4", "should": "^8.0.2"}, "dist": {"shasum": "9d09f7bc6ebee9a13c4fd688c4d15c1c83875e76", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.1.0.tgz", "integrity": "sha512-PTacu1C06YhMJrgZIDS90SPrLirgNmZBglEQa3C7JrTlxBTqM/bqR+5o5SfDKtGJPTWt+12R+sH2al0GsvhESw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRa5R8RQ8alwfou9X+hdnO8s4+Dv3iOGbRp7LeF0njFQIgLgAJ0DasH0E8orMb99X6xkIjXrsqZMoqy+XcJb4NRQQ="}]}}, "2.1.1": {"name": "loader-runner", "version": "2.1.1", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^1.10.3", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^2.3.4", "should": "^8.0.2"}, "dist": {"shasum": "60a30f2a7c8eae79f0805b5848fee134f8ade519", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.1.1.tgz", "integrity": "sha512-31BRP1m/zc/IScL5qyz403k93S6sG+LegcwkQ3dGAIIO31y0LFKUcCrZQBIujOASky2CAZ5q2INVPesc3mablw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF5AuBgQYQ3P8pHFjlHEJn6I0pcg9zroQbXUxw856pn2AiB8sei6400grOkIhoXeI6DsGS6WAR8y0orXm/Rkb0mOQg=="}]}}, "2.2.0": {"name": "loader-runner", "version": "2.2.0", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^1.10.3", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^2.3.4", "should": "^8.0.2"}, "dist": {"shasum": "824c1b699c4e7a2b6501b85902d5b862bf45b3fa", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.2.0.tgz", "integrity": "sha512-N82KYsJ3Eak7gx7RbIsS4F/baYusD7vaHpuEhWz8BcO1giX+6GB99M5Sfetju5LuRqntOtW0yf925uCkzScCXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDu9illURta2PS+fGLL8IJPIR2piKHNlYU7mqr67Jz7WQIgegwRCV/ywR9Mbb5fTYQNUUjGVmbGjui5TfAuuVZsFWc="}]}}, "2.3.0": {"name": "loader-runner", "version": "2.3.0", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"shasum": "f482aea82d543e07921700d5a46ef26fdac6b8a2", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.3.0.tgz", "integrity": "sha512-YeDPSODHmJecKtOhggftAbDOJBUU8BcHtuz0xpsRH8EaCiv8SyDlUQJ2yLam/5stbMLYeEeqW5Ilm0SrXxG7dA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC0S6a+KhF0sLCUnIA2nCBe57Wo4sGsu/DYJNJGGoX40AiEAv/AKvIVSjtk6nlrE5JZUgy4KOweFm8WYud3eVoXxtU8="}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "2.3.1": {"name": "loader-runner", "version": "2.3.1", "devDependencies": {"beautify-lint": "^1.0.4", "codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-By6ZFY7ETWOc9RFaAIb23IjJVcM4dvJC/N57nmdz9RSkMXvAXGI7SyVlAw3v8vjtDRlqThgVDVmTnr9fqMlxkw==", "shasum": "026f12fe7c3115992896ac02ba022ba92971b979", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.3.1.tgz", "fileCount": 5, "unpackedSize": 16037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpMj+CRA9TVsSAnZWagAAFeQP/RXP47MF76LnkjViyKlT\nME1zYLtvlRAFaKV5BNVTc0L+H+kL72bkavQ4KVgJ3e574IlM/Fvdh8UKgtov\nSZ0W/rQB0lrY87tEridc4y3cZT54nV0nYk1N43hUcmLSt4PEgqxRcFN71Dgg\nF4JZznkbJzH6H+ZfuF8DlgFIRkFPukhEQj+c+SUDFU7jlv826iQ41F2cwWrL\ncML7kysIvvFnlVLT3HxMhzmNieq5KWNeCQ6x1iFOwJiOxFDhURgZq0tOCADV\nIQZ+iTEQdmuAkV7hri8126JBpHvHdi4RkY8lAjuEDAGJwvqEDHgqV1PEqdqx\n1UdhUFWZWaRO8HlyL+4Lp4jnHJa6rzM/ZFIM8CZxgUa7bj9kXiWaaMDglFUF\nFeOVHiEIjiB8BvivxPzS0RG6K2CwiqPddnRaOvu7FrquIQsGZrWO1n6kyu9P\n5RUEVZURcjbGv9jVZE1OMELClSJ5q/VMXbvDociSfk8FGFkr8OrQIX/riK3Q\nWtRzCIdOW8yvPU1+Y0H5l/12pEn+rONSGSPLEHFaxIcqD0GGaBpd+vGhkbEp\n1L3rwjGWZBanvrv7AvE974KbBuzblw4SlXqxbe0bX7XzUQDuqcVh0DZjLOn4\nAfkTpCoFu3ptibh0GZjZuef0cq+2OBHglZh/L3xwfUKc1rLkWo2qc3dOpaoq\nBZkq\r\n=VAJg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeBC9MwletCQkFEZq+Q6L7mXIVMg11JYq1rNkh+EwRbQIhAMJTYsMrOWO1Ov51e9ccERTz6x2khN8WycYApxe6L+Hj"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "3.0.0": {"name": "loader-runner", "version": "3.0.0", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-BlinBEAJYOUubaKH9hMxshD1FltYLTd3FA4DoxEh/82C0Rc4cmcqI61bEUj0/osbmW9FCTXTpMAO/jHZDZC5Fw==", "shasum": "c86f303af2dacbd27eabd2ea3aaa957291562e23", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-3.0.0.tgz", "fileCount": 6, "unpackedSize": 15941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPcFpCRA9TVsSAnZWagAAg34P/3HarCfikemUhqyrjyN+\nlZ2nbapVHWyRnTA7ho+Z/HsJPrb5ub97YB+yMrM9u+lzrAJrQ6cderwJ9Jox\nk5ZYIe/SHwZ/9PjXjU/pH7BvCyMN0b/VMP7YA+053hHYVc4B2swf0D47ar7d\nZS3R2prg8AAkJxio/V0U8JvLvRVeg6EFxcWWKA2bQ2SzHaoDlDLk7UAdonr5\nanDNeX8azf/JqpXRbC8gCp349Dky/qw5mm0L3ez6IwfONC7eMaIQG9byp92I\nPyxKEsy3064DYNek0rb0R2DK2myI087tRSug7kYW5ofsFNpG+XZJNN2JpZqd\npR2HJtoc4fTCIcQoBElYNjXKWyGqu9EITADrGoqzrDYjI3IszN9T6nNezBvn\nrmZ4a/eDfuJma/CQ7RujsWSqhFylA0IPnMbzLrLdIQ95Sv5ruX2rVADDVhqm\nsiazHTty+YJmwZD2J1a1WGnSq29XoADBJpAu2q3xpq3izyU4hTVgwcQAMndc\neTjkimLP2A56XYM7amdI/qXsUOYshj4GZ1PPb9bfWQ0am5P1lX4ESjRi6H/n\n4u3yYhmaKbBqdOtVPA3hmiG6wUJ8iY0O2emhub9xoHX+kIXIClq3hkLV9IEP\nTGVluClwzzxd1nNlXLzx7HP9f8D/MQLx53cCDL0zSmuWOr8cHU7faeH9vfLz\n/+Ny\r\n=5zVI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+zzMsia6W89LW6U3hbBTrdbl6cUbvHOmjPZeY9YRx0wIgcqoxDGWiyDrt2p6GTIE1WQ5ZV5FSURMO+I/N1lkMUDc="}]}, "engines": {"node": ">=6.11.5"}}, "2.4.0": {"name": "loader-runner", "version": "2.4.0", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-Jsmr89RcXGIwivFY21FcRrisYZfvLMTWx5kOLc+JTxtpBOG6xML0vzbc6SEQG2FO9/4Fc3wW4LVcB5DmGflaRw==", "shasum": "ed47066bfe534d7e84c4c7b9998c2a75607d9357", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-2.4.0.tgz", "fileCount": 6, "unpackedSize": 16285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPeRPCRA9TVsSAnZWagAAooEP/jd+6k010eAZbJtXYYaJ\nFDURrIqHpuSiq1M3ErGWJZZsm7GctTKhyuxmI/Cbbs+2bEaj+nBN0vEzH6Cu\nlCyxRxRn284e13XYkAcskh+9JZAof5ut1XmTmQvDuasz/7mAzPWXRXeGDOSo\nexBE3vK9g1mjyRd4EhQPp14vEBV2on3qSWVON+z6xSj9kF7uhduBZ29I2Baz\nsxjNrbhDc8r2pioLO4xfDLtFnr65dq0uS8RCyVCFhECwndImU/NFIqKgTx9Y\nINp5GD+9dSz4rRTtuokmXgt0niToBwly78v3LrLEVMAY9AQXDDjuG4+eUf0U\nZt/qMkqmlxMasU0sgpqknSEbtiMUkM+ZPTU/28Gm9tV9ccSQua39PDwuWRbJ\nfztKxhaurUqcdajoJn0+9GsTuqCKE+J767Pb7QwzWn7kUUsFOhVmr+pLClJb\nvUlssEjB+Mvgl3VyKmACwhcaByB8pQLTIVdLgy/07QUczUQtGLSZfTr8E9Rv\nMYY7SkUIPrz8j3VabPdUUw+u8RQXdkG9kwUhxGcCtaMxulMJeceBgQIXBudC\nfQApB9/ep0AJnmKWHmWo9Qk+YLNP7Hi3zPUjOEnW8BAXE9Oc6yltJhvXsDAE\nwviO4Jy6al9JMzeO3yuNZ6suHAhKRFqAzM9JLHtXoJ/pCR9NZ/su7sMpFLkV\nEc2m\r\n=17l7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmEzcif9t5m4m+rJZiJKhjvp0dIPu7ZnqEN+znwOtl3AiEAtJeONFQKoGTXVeyAxlmXz/ZmGJC0W9JN7KlC+JG9wEQ="}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "3.1.0": {"name": "loader-runner", "version": "3.1.0", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-wE/bOCdTKMR2rm7Xxh+eirDOmN7Vx7hntWgiTayuFPtF8MgsFDo49SP8kkYz8IVlEBTOtR7P+XI7bE1xjo/IkA==", "shasum": "e9440e5875f2ad2f968489cd2c7b59a4f2847fcb", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-3.1.0.tgz", "fileCount": 6, "unpackedSize": 16335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3DMECRA9TVsSAnZWagAA200P/1JMHUMVQsDZKyVpC2LT\n8JVK9vUIJVj7IZk3VLIuPjjnAJHmeGkk7K2EMKtwmc868Cup7TpPFeDkbfP0\ntETFxZbo04wE1/Mdp3I3wWKQafubw64g/w45Jg5fiaiW+1/Og2VtD4rGl8Dh\ngcnGMvfKkiCMV6lN06/rkfbiSXBQoNHLfjL+cU2Q8ClnrN0XjNYzJjaM7p+I\nZSbjQFt/Nh1GCk4OC3Y2Zd+zNZFdhkE6xldhpYvij8ZG+ngdnAECrzhqIiyk\ngSWEyyLa/DOtyX3aIdja0OTamqMJQZWeuMtpSB4oMgfh7HI2Aw8lJCWxjiSn\n0rdWdGFLcl62O65UdfiYe0WXUhfzH4AB9lnFga29afuFqkF4d99AIJQEWBPi\najMmfCTFCw3dYmiyG/hoWCxCdasxAlrkdQUTeb1EWURks7OczQpEZM1pgNQM\nWtg/ZGbdKYe9D5V62j3nBJJioYlrkbzLqJLz/gEbYT7llNm1MsyrByEOahaf\nlXIlBXaoYV1UJZOxcZ+ITsWA3yJbTgWJVsavrH6rvM8YD0sMi5xPZ9T2bMny\n5zC8o3E4vaNIsM8PnhVpnTil1B5rBwOc7HEC/R/usmroCEbYn0/8Pq25wm4S\ny9m/ecidibpgfq5hh7NkEVuhgusBgfAd0D5kFsig1agRao9q3gGrBg6W4gPS\nNpv4\r\n=+MLk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyWo7S8CK33F+bS89pAx+PMUkaEaS4e/5hBgoycLXOCQIgKs/+4+754NZeULuyIy2NQOfZLskZC1vNUiF2+gfzX9k="}]}, "engines": {"node": ">=6.11.5"}}, "4.0.0": {"name": "loader-runner", "version": "4.0.0", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-Rqf48ufrr48gFjnaqss04QesoXB7VenbpFFIV/0yOKGnpbejrVlOPqTsoX42FG5goXM5Ixekcs4DqDzHOX2z7Q==", "shasum": "02abcfd9fe6ff7a5aeb3547464746c4dc6ba333d", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.0.0.tgz", "fileCount": 6, "unpackedSize": 17550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/0V1CRA9TVsSAnZWagAAH7YP/2pFuDJ2bw52Urb+IQDz\nXVRufic9BSLPIRcdZHEjd+UfWpfRXhagGJ/xR45//E2DbxCGNOyanKZQNTW7\ne+rCMitF1AFi0kRh/Orhnq7K/qv9gwt4B4ZGPTJrPz6v1tj/B7hry+bwNMxr\novy76Cn8U56bURRYzXXYrga9213b5Dm1dOE1A5o1myHjpJe2AVrrejJbsbXz\nYrdSypx+spOO7LEPAp4FW6UJtrrINN1sc+BLGQPi9CCbmpwH0eojaFg4qaOl\n3kuhgYTIlALcPuLIA6FHwGlE1Ha0x7zfinW8h8/pst69nMBeRqSqhjEjxhqk\nL15C/CH+CneqWxLZPjAxRaPq8bU5vJUD21c1aQk5H+pKzPG7rcLUH3C81lz7\nV06M9x4vTmv5jm0KmvaZMt30MoGdcR4B4B0+xVQwkXA1k7IoOBpzAFyB9nQz\np67/dQVk2SiJTxTpy02Ko+5uwAO7yaLXh/qgikn1r/pCm2nkLme1orQMlhKg\nsCxWL1+hGg2jnMcLLTcdN6rLUgsOReNqNmU8RGhGUx6wzrNfnbYuHjh+2hAP\nYyKmVJiWFXOCcokaEWlvx5uOcBcf5no38Mt/z45G3hhqXyFhnYUyj1aH1eyS\nrEbFtYvKibKwGT+52qzTUojyaPPTGUA0C6vAyfckY+j4oVlJLnIl+W7175a8\nQhGf\r\n=ouTp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1/S6ATPC+pHrn10uu861Z43NlFKc/wr+fbpAAy5HCtgIgMwfUS8U55ebaQ+RqzrnCAdwZi4TXUCedOQCYHbe5lUA="}]}, "engines": {"node": ">=6.11.5"}}, "4.1.0": {"name": "loader-runner", "version": "4.1.0", "devDependencies": {"codecov.io": "^0.1.6", "coveralls": "^2.11.6", "eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-oR4lB4WvwFoC70ocraKhn5nkKSs23t57h9udUgw8o0iH8hMXeEoRuUgfcvgUwAJ1ZpRqBvcou4N2SMvM1DwMrA==", "shasum": "f70bc0c29edbabdf2043e7ee73ccc3fe1c96b42d", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.1.0.tgz", "fileCount": 6, "unpackedSize": 17729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdDK/CRA9TVsSAnZWagAAaZ8P/jmMEJvDh858fmy/1Pto\nEYFIq/bORENH44kpg+0I7hFvpzj5oXdFl9IoDuS1lIzBmsALhuJkNTo4BBi/\nm5Wo1Q3r+IlzFhlrGotalbJWUvr0s42SeaDBnQTS+X+mBqWRtMo+87H4lIb8\nTBFK6ddje0jI0OmJDZDiSqnCeGKyqpbQC70F7c//4uEhZG+wHqs8kIQ9FtPt\nq0ws5k8ihGwU5d93/KRpPgRO4oZ7qHP0pqrdTVbnWMvPJgfv+8hTGxTd4LMe\nxOaOVTfHFgYvvW6hPhwEh5Dmt7IfHgH4RMECc4A4JXqmigKiXquRjSquk0YX\nQlR98Z8rWd+Wgvd7qIbyV8aBS3FJa47uDmw5rP5962oUNGspdb993mURr3N+\nt70spV1O4mU/lwyX+VItThQIDNhWqwA4d7WR4UKShriJIFISMmMbGf1XEILZ\nk5rrj0Qvl/JwYMCW2bwZnKoDxQjq5bKaJoQt2oaR4mYV2fJTN3R6GahuDkEi\nyDyEreJ6ilJqF9i5vCG3HuMWnTjgvYOsXXyCBprIdo5PGDGUIwpJBFGkrsC5\nL0c0gK05zv0rWNq89rv8gMUEOg4Lg0tR7XKJ/4SC62Vqov6RMrrpcfA0Ngu+\nzCN19fL9J7h6YPT1KIz3GIkAMRRlwHqQpEddFAm7sSg2hMSdiwTbAjc4k2K0\n7Kcg\r\n=srNq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIfCJGqeghB4m5993BLT1WoLwKaMvYVi6VNh18d9/pwAiA+LZhNFCbHYDObA7Q9EJDS+dozKfzQrTmo2rtvkFHolA=="}]}, "engines": {"node": ">=6.11.5"}}, "4.2.0": {"name": "loader-runner", "version": "4.2.0", "devDependencies": {"eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-92+huvxMvYlMzMt0iIOukcwYBFpkYJdpl2xsZ7LrlayO7E8SOv+JJUEK17B/dJIHAOLMfh2dZZ/Y18WgmGtYNw==", "shasum": "d7022380d66d14c5fb1d496b89864ebcfd478384", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.2.0.tgz", "fileCount": 6, "unpackedSize": 18341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2yLQCRA9TVsSAnZWagAAnGoQAJ46xvOu75lGvhfGV6Mq\nXUd5yrctLGDcmImHRvdVmy1dciRKD4GijG7CS4EZAdAele6tZN8XZgpFfwYn\nsQEX1zMxYWPugoiLbN1fAZtYZjHFMtqJ53jLUeLebgX4/ese/9Ej4e9pALQa\ncDDQY6G0dZkZQ9MgbiyVjxH+iHi+NQ2gfzkd9pUopi+3GCjeNMnGpXhV0sWs\naZbdBg/mwyZQ1SLc+5s/d65pivvC8rxD6zC1Y5lyYiPML1yGJEM8v034J2/b\ni2z733b3VkG83BJxjbt75r4PI1YfR14si01kMCPsWBNGZP5y9O/Q6HOTi12B\nuanDmOEadtyDuN9vQ5Kuxfb+1CxYBMLdlGW+v6KTd6HUI6DkFJlepiAGUb7O\nknEsM1hnNVnTm3H4PdWRAx7o+TCgKvS/d9rZgbk3ZCX9XyAVQsNfgUcRMzEA\nEX6/mMxRVtM0U1YXyC0B7R49S8E9hDi63zHjPbykBurwAdgNsr01FlN3NG2Q\nO0Erc1kzJihhix6QM9glt0mfHsFyYW7SSYErujxXmscCr2Srd1/GTP51yaTW\njHUeRwHTp/jorLEU+lWwGx600ZJnXsIboT8TIfzIw4t6yaON/vajnbjqpQRY\nfiqqlJSADQfh9jZNjqR9NEhrOkHFCiZCRz8lpFdtv+EIOs8xOBUZZSnQQDb6\nTmfw\r\n=bHyJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjnYNIpYKY434MrZ+gE1Jcy+4L5rdakl6N55ASCssZjQIgZ1V3djt/Yy790tJ59olShYqbHlc9m1xicG0ycYmwJsA="}]}, "engines": {"node": ">=6.11.5"}}, "4.3.0": {"name": "loader-runner", "version": "4.3.0", "devDependencies": {"eslint": "^3.12.2", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "^0.4.1", "mocha": "^3.2.0", "should": "^8.0.2"}, "dist": {"integrity": "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==", "shasum": "c1b4a163b99f614830353b16755e7149ac2314e1", "tarball": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz", "fileCount": 6, "unpackedSize": 18386, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPa2WnC3Sbk65ea1fzbmGECkN34FOyt6Dj4UijVrdTkwIgLz6ncHBySJ9KqR72TYmY7dRn8bDzLqj2XCU4LUNQiis="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUjPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoazQ//aiQJ56BBO4XTDYrQJiLTj6M3xFO1mgPd1v2ae7d2H3GqPXhT\r\ng93LxcaTuDB1YuDJYql2xlAZgUkKobls4x78TMYwf7Q5SxhHhRjA2BgKfQ4K\r\nMSCXaxhFM5R3yTY0D9dLAsJZdX2R0ZPFQZ6vvvNZpQc2RXSp+7z67e1I+A9r\r\nYJ+pRpql/mqxidfNebIrj9Kz6D5TzDs7PSNEYGtof5uAJXbesGyKtp+hCsTD\r\n7bkTUr/Xt0Cnv/Koc5+xebMuXqYPFgFSNlYAW614HvlWBnUKBPHaxsXwv7qE\r\nvLG+7/9p1YYq3pWrhNmDI5kuqJDPhxuaakuzeR7Lc0E5kIzpvuQeL0iN/NJM\r\nWLqycRvjtCCBQ2nbjCcO4xA5R91l3Ta8VCM7oTuZ+Sfp9gnUE7e/CKa5Mu+L\r\nhwFgQEzDXgqgMq54OwsI+EjSEeKr3RUjqK5NveV/rClGDeETKIK4xt2DHL3H\r\npD8s3UfS6FfjmiNhfyte8GrdLi4JzZ7zLbKeN5Z8TX+xWygvn3Wky7BetPpK\r\nOwepDH+ylPStdF3sknEg7ioXl8DqLJ3R+puDKysZXf6kTDFDpArfqFA83S5R\r\nJOAhL5lMhBdd8d/nCxp38aezkBb5U+zBxQ1maa6EofxnAQZa38SEnvk9j+En\r\n18YlXsqRRUR0/RW+3wOa9qEqLlZ2ohiGR2g=\r\n=QIF5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}}, "modified": "2022-06-19T13:13:54.881Z"}