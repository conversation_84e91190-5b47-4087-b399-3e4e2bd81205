{"_id": "fresh", "_rev": "55-1fc889a060a70c5f9f193bd42c0d9bec", "name": "fresh", "dist-tags": {"latest": "0.5.2", "next": "2.0.0"}, "versions": {"0.0.1": {"name": "fresh", "version": "0.0.1", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "fresh@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f98a0a1b9001b6e227fb9c65ff3927bdb7b404fa", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.0.1.tgz", "integrity": "sha512-fb5BxtuLck23DPZpj2H3Ck8YUEo5su5UBmGDbs171kkYQz/7VZ8pz5ZovJBZOhn/5vYkD6TnyMk/eR1HYUXXjQ==", "signatures": [{"sig": "MEYCIQD8Bm6Iy7YB1j/q5hJDqn8qmqrTfA2IvQV0UMdx5Lie7wIhAPudyII9GZTquB0h7A7Dw/TRA66ConF4yMBtlAUDwKMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.0": {"name": "fresh", "version": "0.1.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "fresh@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "03e4b0178424e4c2d5d19a54d8814cdc97934850", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.1.0.tgz", "integrity": "sha512-ROG9M8tikYOuOJsvRBggh10WiQ/JebnldAwuCaQyFoiAUIE9XrYVnpznIjOQGZfCMzxzEBYHQr/LHJp3tcndzQ==", "signatures": [{"sig": "MEUCIQCUD6V6REKUxUEkpsFap9Uf+bFAsnHok5r+w7OWUodTPQIgbAC8KWJucKfjkEgBprd0zRW3HzJUhq1NT0CWfR+wNMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.24", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*", "should": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.0": {"name": "fresh", "version": "0.2.0", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "fresh@0.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-fresh/issues"}, "dist": {"shasum": "bfd9402cf3df12c4a4c310c79f99a3dde13d34a7", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.0.tgz", "integrity": "sha512-ckGdAuSRr1wBmnq7CsW7eU37DBwQxHx3vW8foJUIrF56rkOy8Osm6Fe8KSwemwyKejivKki7jVBgpBpBJexmrw==", "signatures": [{"sig": "MEYCIQDs0BvdNtg4JLARwcoBoESSOZW2Z/M8+WwCjgF3RaHVQwIhAID0dDzJrruaijtkkYEP4hcuKgj8upQsDRbXg/ZUqQqn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/visionmedia/node-fresh.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "HTTP response freshness testing", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.2.1": {"name": "fresh", "version": "0.2.1", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "fresh@0.2.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-fresh", "bugs": {"url": "https://github.com/visionmedia/node-fresh/issues"}, "dist": {"shasum": "13cc0b1f53fe0e6fa6a70c18d52ce3c5c56be066", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.1.tgz", "integrity": "sha512-6xM6o6wNg8wlfSvwohtdUBhCOA4edV3UsObLD/SrG+ffCk4RxnnHysLRD77v4pb+FEf9OE/5BaVmleEl1GdWnQ==", "signatures": [{"sig": "MEUCIQDXUVtLQOuTOkeAu6i4ABAkJk8D7RApbRFh0asG5owgFQIgDKnBsqGO43MqKZieXO93735uekQXHqOKsGJa2DTlaug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/visionmedia/node-fresh/blob/master/Readme.md#license", "type": "MIT"}], "repository": {"url": "https://github.com/visionmedia/node-fresh.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "HTTP response freshness testing", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.2.2": {"name": "fresh", "version": "0.2.2", "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "fresh@0.2.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-fresh", "bugs": {"url": "https://github.com/visionmedia/node-fresh/issues"}, "dist": {"shasum": "9731dcf5678c7faeb44fb903c4f72df55187fa77", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.2.tgz", "integrity": "sha512-ZGGi8GROK//ijm2gB33sUuN9TjN1tC/dvG4Bt4j6IWrVGpMmudUBCxx+Ir7qePsdREfkpQC4FL8W0jeSOsgv1w==", "signatures": [{"sig": "MEUCIH3CiJCS0nRuWmk2s6QcBhRP+9WU9Sr+bfcyDuyVK5rZAiEA4loMJlBbU+htN02j2aANWsiBXuEWm6GhFezsbuCmBDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/visionmedia/node-fresh/blob/master/Readme.md#license", "type": "MIT"}], "repository": {"url": "https://github.com/visionmedia/node-fresh.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "HTTP response freshness testing", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.2.3": {"name": "fresh", "version": "0.2.3", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.2.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/fresh", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "2db40d43bc63830f418519380879d6bedde2e845", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.3.tgz", "integrity": "sha512-bbmrimlze7/XywucgiYX1k0CGai+9mutJ7IbAsxKeJpgeEA8nB83IYN+JvVcLCWZ2B9J2rDYvIYLo01znAmzAw==", "signatures": [{"sig": "MEUCIC8Zc0ZRERh12jwIhdqnX6EE4cWLqY6V1f7vi+yA7ibzAiEA9btGiLX6ZqyGK0AMkyZCR0i3Y44VD234zgyxVNiF460=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "2db40d43bc63830f418519380879d6bedde2e845", "gitHead": "a94cacdf94f85bd6a1e1210c5928e4b0d8518043", "scripts": {"test": "mocha --reporter spec --require should", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --require should"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/fresh", "type": "git"}, "_npmVersion": "1.4.21", "description": "HTTP response freshness testing", "directories": {}, "devDependencies": {"mocha": "1", "should": "3", "istanbul": "0"}}, "0.2.4": {"name": "fresh", "version": "0.2.4", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.2.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/jshttp/fresh", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "3582499206c9723714190edd74b4604feb4a614c", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.4.tgz", "integrity": "sha512-mnBGgIFRNu54GtbkXy6+QKPYW/b5joAURorA8ELeJc/5BBNph6Go1NmHa9dt08ghFnhGuLenrUmNO8Za1CwEUQ==", "signatures": [{"sig": "MEYCIQDi5I/0m4r0kpijz77GRa+tjai7EynIs4/qx+nWcZyoHwIhAPCgK/KWi2yuDcX5oKcmRmxte5wFUDd6343eHocp4gIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "3582499206c9723714190edd74b4604feb4a614c", "engines": {"node": ">= 0.6"}, "gitHead": "8440a4ca75fb091dec06e88654b3b1c31d7e7164", "scripts": {"test": "mocha --reporter spec --require should", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/fresh", "type": "git"}, "_npmVersion": "1.4.21", "description": "HTTP response freshness testing", "directories": {}, "devDependencies": {"mocha": "1", "should": "3", "istanbul": "0"}}, "0.3.0": {"name": "fresh", "version": "0.3.0", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "651f838e22424e7566de161d8358caa199f83d4f", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.3.0.tgz", "integrity": "sha512-akx5WBKAwMSg36qoHTuMMVncHWctlaDGslJASDYAhoLrzDUDCjZlOngNa/iC6lPm9aA0qk8pN5KnpmbJHSIIQQ==", "signatures": [{"sig": "MEYCIQConFkUNRclV3Fb1+hbqcK0N9Rd9T9871WB7nrPBbbGtAIhAL981HcpsuGUiwOo5lCr5AuDoZEE98EisanNRlwTR3xY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "651f838e22424e7566de161d8358caa199f83d4f", "engines": {"node": ">= 0.6"}, "gitHead": "14616c9748368ca08cd6a955dd88ab659b778634", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/fresh", "type": "git"}, "_npmVersion": "1.4.28", "description": "HTTP response freshness testing", "directories": {}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.9"}}, "0.4.0": {"name": "fresh", "version": "0.4.0", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh#readme", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "475626a934a8d3480b2101a1d6ecef7dafd7c553", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.4.0.tgz", "integrity": "sha512-bvbLUkH8QOn/QM8BBDVvgej8pIaT/gZLIEi9jwmuz2gMAX3cmEPZKzX2pTN8qPEgHxhG7dWNqwk22mZ1baq/xQ==", "signatures": [{"sig": "MEUCIFmZ743EPYMjyXguq3lkfLwp2PUxwSsar7nZrPQ2dHV7AiEAvltdMWqgnnABT5yGv0idg6HARO2w5c9DOLxutLaoR1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "475626a934a8d3480b2101a1d6ecef7dafd7c553", "engines": {"node": ">= 0.6"}, "gitHead": "c0af4deba75d95d1f5d56906b7dc45b849cbaa21", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "4.6.0", "devDependencies": {"mocha": "1.21.5", "eslint": "3.15.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fresh-0.4.0.tgz_1486346746830_0.760833503678441", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.0": {"name": "fresh", "version": "0.5.0", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh#readme", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "f474ca5e6a9246d6fd8e0953cfa9b9c805afa78e", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.5.0.tgz", "integrity": "sha512-FveevZlqNSIBombLEB9L3WyWg74r6d0sk+unzqKmt33b5egY0YXDFWIQ/t2Fzr9mgBLL0BlL0/rdg8Oq1C0SVw==", "signatures": [{"sig": "MEYCIQDBjDXFwn0RiePXdjRTl1QRukYmyPHpPLWKCmDr+snNOwIhAISugPozjB/i+NU0Tkr11WUPwFBiXV06y1syrMDuhAht", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "f474ca5e6a9246d6fd8e0953cfa9b9c805afa78e", "engines": {"node": ">= 0.6"}, "gitHead": "b1d26abb390d5dd1d9b82f0a5b890ab0ef1fee5c", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "4.7.3", "devDependencies": {"mocha": "1.21.5", "eslint": "3.16.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.2", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fresh-0.5.0.tgz_1487738798128_0.4817247486207634", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.1": {"name": "fresh", "version": "0.5.1", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh#readme", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "c3a08bcec0fcdcc223edf3b23eb327f1f9fcbf5c", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.5.1.tgz", "integrity": "sha512-itI/dEMy9O50obvdroLCBDD/83GzxqZLV673itn56Z4JdcgQd4T98Zb2XfbGVHFfNmKToPVP44wYzEA31BVr7w==", "signatures": [{"sig": "MEUCIQDRpwqWppMR3mtK4TNHpqNpy7dHYZJGhQfwutQ7Y1bj9gIgOrP9N0BGFMx0SjZu9dC3Sbul4owAD1U3ftKAVS7iVbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "c3a08bcec0fcdcc223edf3b23eb327f1f9fcbf5c", "engines": {"node": ">= 0.6"}, "gitHead": "e8a4aaffc75b6169a6f57168ac79dee7a7f02c92", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fresh-0.5.1.tgz_1505187168525_0.3156159908976406", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "fresh", "version": "0.5.2", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh#readme", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "3d8cadd90d976569fa835ab1f8e4b23a105605a7", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "signatures": [{"sig": "MEYCIQDCqawaTDV3DedLtJ76gUv97Ea9YgeHNYTmAFl9p+GbRQIhAK8JiMCdSkiq/Ld6Hr+0E8jDjv8T558uA98rBIG+Of/N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["HISTORY.md", "LICENSE", "index.js"], "_shasum": "3d8cadd90d976569fa835ab1f8e4b23a105605a7", "engines": {"node": ">= 0.6"}, "gitHead": "02df6303ff260b6b7da0b479f3e42222e8157b47", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "bench": "node benchmark/index.js", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fresh-0.5.2.tgz_1505365391149_0.7952043106779456", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "fresh", "version": "1.0.0", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@1.0.0", "maintainers": [{"name": "jon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh#readme", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "95336eef8f1419546bec657ebaa3f8cef48d3b8b", "tarball": "https://registry.npmjs.org/fresh/-/fresh-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-CXbd7Pq2ZU4uK2K8pIsduMqFH27c6DhruWq42tmJjQ5a3ouq1pZNjdSOaYuT2Vx2UpU6st5Cx9ye0GWnnMMnSg==", "signatures": [{"sig": "MEUCIFThuI+qvqDWJ+wwTWNilFQ/3nXQRmqBDmCn+YK9xYsCAiEA6+M0/xQ5zCNtskTwZMuLzKLzW2HMCylIffO1GZ3u7BA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10226}, "engines": {"node": ">= 0.8"}, "gitHead": "64b8dad952beae7834e092e9ed3c01f7aac356fb", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "jon<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "8.12.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "6.0.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fresh_1.0.0_1725488205701_0.18854086244144042", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "fresh", "version": "2.0.0", "keywords": ["fresh", "http", "conditional", "cache"], "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "fresh@2.0.0", "maintainers": [{"name": "jon<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/fresh#readme", "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "dist": {"shasum": "8dd7df6a1b3a1b3a5cf186c05a5dd267622635a4", "tarball": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "signatures": [{"sig": "MEYCIQCBFJPoFuukcOo1fzNYCDNO+SDzgPf0eKjqmYjOYyNzkwIhAPOR+Beygb9z6PiclNlPz2vyI3RnokWj9gPVk0RvZa0f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10289}, "engines": {"node": ">= 0.8"}, "gitHead": "f185ef1376c0337d366f9e35bda92b053983fd81", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "bench": "node benchmark/index.js", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "jon<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "HTTP response freshness testing", "directories": {}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "8.12.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "6.0.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fresh_2.0.0_1725492288192_0.446838953143202", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-06-10T19:09:34.892Z", "modified": "2025-05-14T14:56:19.208Z", "0.0.1": "2012-06-10T19:09:36.227Z", "0.1.0": "2012-06-15T23:07:11.778Z", "0.2.0": "2013-08-10T21:44:55.387Z", "0.2.1": "2014-01-29T20:16:08.494Z", "0.2.2": "2014-02-19T23:28:15.565Z", "0.2.3": "2014-09-08T01:09:11.214Z", "0.2.4": "2014-09-08T02:50:40.194Z", "0.3.0": "2015-05-12T15:10:38.713Z", "0.4.0": "2017-02-06T02:05:48.973Z", "0.5.0": "2017-02-22T04:46:40.181Z", "0.5.1": "2017-09-12T03:32:49.448Z", "0.5.2": "2017-09-14T05:03:12.205Z", "1.0.0": "2024-09-04T22:16:45.871Z", "2.0.0": "2024-09-04T23:24:48.361Z"}, "bugs": {"url": "https://github.com/jshttp/fresh/issues"}, "author": {"url": "http://tjholowaychuk.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/fresh#readme", "keywords": ["fresh", "http", "conditional", "cache"], "repository": {"url": "git+https://github.com/jshttp/fresh.git", "type": "git"}, "description": "HTTP response freshness testing", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jon<PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "", "readmeFilename": "", "users": {"eyson": true, "m42am": true, "ckaatz": true, "jovinbm": true, "oldfish": true, "moimikey": true, "mojaray2k": true, "ninozhang": true, "wxttxw125": true, "goodseller": true, "simplyianm": true, "wangnan0610": true, "nickeltobias": true, "tobiasnickel": true}}