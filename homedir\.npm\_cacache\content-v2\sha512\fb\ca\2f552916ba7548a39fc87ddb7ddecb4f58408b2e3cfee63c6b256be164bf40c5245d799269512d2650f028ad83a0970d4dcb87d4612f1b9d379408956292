{"_id": "is-number", "_rev": "43-3fe877efbeead423ee53a12d5ca44395", "name": "is-number", "description": "Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.", "dist-tags": {"latest": "7.0.0"}, "versions": {"0.1.0": {"name": "is-number", "description": "Is the value a number?", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE-MIT"}], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb-tag-jscomments": ">= 0.2.0", "verb": ">= 0.2.6", "mocha": "*"}, "keywords": ["docs", "documentation", "generate", "generator", "markdown", "templates", "verb"], "_id": "is-number@0.1.0", "_shasum": "4407a37aec259352affb5548886744ba4903f8bd", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4407a37aec259352affb5548886744ba4903f8bd", "tarball": "https://registry.npmjs.org/is-number/-/is-number-0.1.0.tgz", "integrity": "sha512-WWl3iAruYOePW5iMf0IMcPiTsOTOu/FFsPyfx/ywixw7VmN6dI3/8/rgo4pdN6kVVjC5ByZ0Zk8xHLGEUSCdYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGbjmJymNMyy+kX9QMWNiNDpyXcRARCBG/qWG5AH3BHwIgYPJPxD0DLlmMlQ82J2/kw9xjNGwnqmZwv1iu2dhCtQo="}]}, "directories": {}}, "0.1.1": {"name": "is-number", "description": "Is the value a number? Has extensive tests.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE-MIT"}], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb-tag-jscomments": ">= 0.2.0", "verb": ">= 0.2.6", "mocha": "*"}, "keywords": ["coerce", "coercion", "integer", "is", "istype", "javascript", "math", "number", "test", "type", "typeof", "util", "utility", "value"], "_id": "is-number@0.1.1", "_shasum": "69a7af116963d47206ec9bd9b48a14216f1e3806", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "69a7af116963d47206ec9bd9b48a14216f1e3806", "tarball": "https://registry.npmjs.org/is-number/-/is-number-0.1.1.tgz", "integrity": "sha512-la5kPULwIgkSSaZj9w7/A1uHqOBAgOhDUKQ5CkfL8LZ4Si6r4+2D0hI6b4o60MW4Uj2yNJARWIZUDPxlvOYQcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGOUnPaovBe5O7zmgOovRl4e6Dzx6aMobYRtKHOiWV+AiEA4Qufb/miuS6rDGp7Zd20/e0JoxviUgTnqu21xGF2DhY="}]}, "directories": {}}, "1.0.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "files": ["index.js"], "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "gitHead": "3183207ab31bb09c65ad8999c39090a3c0530526", "_id": "is-number@1.0.0", "_shasum": "de821e3936a8996badeb879ca6f93605e769d498", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "de821e3936a8996badeb879ca6f93605e769d498", "tarball": "https://registry.npmjs.org/is-number/-/is-number-1.0.0.tgz", "integrity": "sha512-chlxkgJp4PZIiff6kUe/MWLp5+soELWNYA2IsOTus1YwKj8d9JZS6QsU7Ryqwhb1f4i0nQ5SsoUj6d5kGgq0KQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2AOAMGpyuvqtfmVFBdULwtBCwI1cATJ1E2SXCu7KrqAIgN3FVeMU5CjRuw21s50tzEalrZmgvbJxkjizEvjLl+Xk="}]}, "directories": {}}, "1.1.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE-MIT"}, "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "files": ["index.js"], "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "gitHead": "3183207ab31bb09c65ad8999c39090a3c0530526", "_id": "is-number@1.1.0", "_shasum": "620db9e22fded44d43d8e3e47044319083d31855", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "620db9e22fded44d43d8e3e47044319083d31855", "tarball": "https://registry.npmjs.org/is-number/-/is-number-1.1.0.tgz", "integrity": "sha512-zRllTDrNKDRvVEhjEqDVkT9eKzC7HJ+9SFbfBdZnbjBVt9t0SwbEn0F/NSwdk5GkkEdKZA6ZQ0Hfd4akty5Pfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnVnl9/SgNEemmb1M0T1tlfm4YRTqvWw3ja961FsqrVAIhANd7v0o1uT3CVfC9pzaFfvmwZcKBnxD+UHWlKvlIU4SJ"}]}, "directories": {}}, "1.1.1": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "1.1.1", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "gitHead": "5184d76c622b97d45486340a85147c6d59d14e25", "_id": "is-number@1.1.1", "_shasum": "e393e7ec07c17770dbb67941e7f89c675feb110f", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e393e7ec07c17770dbb67941e7f89c675feb110f", "tarball": "https://registry.npmjs.org/is-number/-/is-number-1.1.1.tgz", "integrity": "sha512-3/f2eabVTjIGjSVaQIIv33g1d2tYdSUnCNJuPpHcjwRzx8A2IPtCB2ayK2R3aS7SCF/LHlTjdaRjNmfG+RpFlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA1MN/tZFRjbwiG0H63P4dD6Qsem1lVPlOlJuJfdJP8iAiAoX6Lk3FLlI0dqigZPFyFZi1CXFk0W8dN9yJw7P2IW+Q=="}]}, "directories": {}}, "1.1.2": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "1.1.2", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "mocha": "^2.1.0"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "gitHead": "a902495bca1f471beaa8deb6193ba628bf80c0e4", "_id": "is-number@1.1.2", "_shasum": "9d82409f3a8a8beecf249b1bc7dada49829966e4", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9d82409f3a8a8beecf249b1bc7dada49829966e4", "tarball": "https://registry.npmjs.org/is-number/-/is-number-1.1.2.tgz", "integrity": "sha512-dRKHHq76sZAXFf823ziHIOx5fXbuV1IrR892LugLDmyEBVMZzGoske4sY6lf+l3YPH/VyWNiKzNDXAPiQhx9Yg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpH37H0WboBtQM2deQbMGTh2WMRYWVosLWdpoz+wqyoQIhAIutpIYs8sNTY2MaxDpnJmvhoUfxrNVDIUs4+J5JffHf"}]}, "directories": {}}, "2.0.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "mocha": "^2.1.0"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "gitHead": "d5ac0584ee9ae7bd9288220a39780f155b9ad4c8", "_id": "is-number@2.0.0", "_shasum": "451c78bfe6c427f37bc2a406226e0cde449f3b5a", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "451c78bfe6c427f37bc2a406226e0cde449f3b5a", "tarball": "https://registry.npmjs.org/is-number/-/is-number-2.0.0.tgz", "integrity": "sha512-LRme8p0WzIN9lrlP9/wJKs6242c9KYvDj6JlW9N9Up28wFxzyLNyNtnWu1j9nIuoWliquNRQx6+/OZ5mI2yVpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEJz5PkAW3hcxfgUxotR5OLxeQFXF+vEsj3qRlIMuZj9AiAH3YXd7HMtFrJZqPGBE7DOjTqFWYWiPbm696CW+v1zDA=="}]}, "directories": {}}, "2.0.1": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "mocha": "^2.1.0"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "dependencies": {"kind-of": "^1.1.0"}, "gitHead": "bb9b2e19a9aa2ed4b1e7a5d27e43417c8c9570c0", "_id": "is-number@2.0.1", "_shasum": "a3754e651f0df489f290ee0a1102c87cc5a0db02", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a3754e651f0df489f290ee0a1102c87cc5a0db02", "tarball": "https://registry.npmjs.org/is-number/-/is-number-2.0.1.tgz", "integrity": "sha512-lDXqMCs22DpNe5F5HqfrhwdCWXjlMccky+nhZ1+iFjnGvL7F7R7Q6Fd1FswLUrqueFab/6/k1Wd9LkIXJcBSWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBlSPRMBjwALDvd9fjWuuBR4PSdICIZh8q3IucTdXt6xAiA/4FirIMH2gtXtxkCZ/FLtO2+W5CJQVYHnvPGUY6nIaA=="}]}, "directories": {}}, "2.0.2": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-number/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "mocha": "^2.1.0"}, "keywords": ["check", "coerce", "coercion", "integer", "is number", "is", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "dependencies": {"kind-of": "^1.1.0"}, "gitHead": "63d5b26c793194bf7f341a7203e0e5568c753539", "_id": "is-number@2.0.2", "_shasum": "c7542a0f420610655834cd3825bc2f0eb72afe21", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c7542a0f420610655834cd3825bc2f0eb72afe21", "tarball": "https://registry.npmjs.org/is-number/-/is-number-2.0.2.tgz", "integrity": "sha512-MzbGCUNsyTujsn2O9E2wL8n65cXdeWV1Jgd8khIA8VuN1q9ExsYQD6CwUwX6i8VGNLQy1skQugZ5RkvM1vbeqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9XsZ2zEA+6vg8VayjaUNxJIUDkYbvrvxC4uW8u0L3iAiEAgmolYRoJJEsPK3k30eWySMUIHqn3GHaYsWysUhMsqjs="}]}, "directories": {}}, "2.1.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "mocha": "*"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is number", "is-number", "istype", "kind of", "math", "number", "test", "type", "typeof", "value"], "verb": {"related": {"list": ["kind-of", "is-primitive", "even", "odd", "is-even", "is-odd"]}}, "gitHead": "d06c6e2cc048d3cad016cb8dfb055bb14d86fffa", "_id": "is-number@2.1.0", "_shasum": "01fcbbb393463a548f2f466cce16dece49db908f", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "01fcbbb393463a548f2f466cce16dece49db908f", "tarball": "https://registry.npmjs.org/is-number/-/is-number-2.1.0.tgz", "integrity": "sha512-QUzH43Gfb9+5yckcrSA0VBDwEtDUchrk4F6tfJZQuNzDJbEDB9cZNzSfXGQ1jqmdDY/kl41lUOWM9syA8z8jlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBsAMHxq3xfm/burDf3PmG8CCLKR4UncouEdSDdRwglAAiEAxMTfaixUr2K2XsrRDCg7R6dRYawh8RqjO/VkIJSFNA8="}]}, "directories": {}}, "3.0.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON><PERSON><PERSON> Reagent", "url": "http://www.tunnckocore.tk"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"benchmarked": "^0.2.5", "chalk": "^1.1.3", "gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is-nan", "is-num", "is-number", "istype", "kind", "math", "nan", "num", "number", "numeric", "test", "type", "typeof", "value"], "verb": {"related": {"list": ["even", "is-even", "is-odd", "is-primitive", "kind-of", "odd"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb", "verb-generate-readme"]}, "gitHead": "af885e2e890b9ef0875edd2b117305119ee5bdc5", "_id": "is-number@3.0.0", "_shasum": "24fd6201a4782cf50561c810276afc7d12d71195", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "24fd6201a4782cf50561c810276afc7d12d71195", "tarball": "https://registry.npmjs.org/is-number/-/is-number-3.0.0.tgz", "integrity": "sha512-4cboCqIpliH+mAvFNegjZQ4kgKc3ZUhQVr3HvWbSh5q3WH2v82ct+T2Y1hdU5Gdtorx/cLifQjqCbL7bpznLTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEpkuzJnkIAtuDMAI3VLqzFNwNANap4m9nyeUneW72kRAiEAtsctBWg1OtEaj2FSH+9ViALZU1O0hQFCVzwGMWHfVnk="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/is-number-3.0.0.tgz_1473555089490_0.21388969756662846"}, "directories": {}}, "4.0.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^2.0.0", "chalk": "^2.1.0", "gulp-format-md": "^1.0.0", "mocha": "^3.0.1"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is-nan", "is-num", "is-number", "istype", "kind", "math", "nan", "num", "number", "numeric", "test", "type", "typeof", "value"], "verb": {"related": {"list": ["even", "is-even", "is-odd", "is-primitive", "kind-of", "odd"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "0c6b15a88bc10cd47f67a09506399dfc9ddc075d", "_id": "is-number@4.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rSklcAIlf1OmFdyAqbnWTLVelsQ58uvZ66S/ZyawjWqIviTWCjg2PzVGw8WUA+nNuPTqb4wgA+NszrJ+08LlgQ==", "shasum": "0026e37f5454d73e356dfe6564699867c6a7f0ff", "tarball": "https://registry.npmjs.org/is-number/-/is-number-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA4Ju8vfTa5OmCg3qagXNvXHR0wp4GYpv4Jrzzx1Tr6yAiANs8hwximKkGyecUuMhc9/ipdFOP6u5xqFY1kkP37rHQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-number-4.0.0.tgz_1508219035603_0.08690439746715128"}, "directories": {}}, "5.0.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "5.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^2.0.0", "chalk": "^2.1.0", "gulp-format-md": "^1.0.0", "mocha": "^3.0.1"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is-nan", "is-num", "is-number", "istype", "kind", "math", "nan", "num", "number", "numeric", "test", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["isobject", "is-plain-object", "is-primitive", "kind-of"]}, "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "7ed43573445149edf10f56967e8d943520ebb1db", "_id": "is-number@5.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LmVHHP5dTJwrwZg2Jjqp7K5jpvcnYvYD1LMpvGadMsMv5+WXoDSLBQ0+zmuBJmuZGh2J2K845ygj/YukxUnr4A==", "shasum": "c393bc471e65de1a10a6abcb20efeb12d2b88166", "tarball": "https://registry.npmjs.org/is-number/-/is-number-5.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRVCaHiES/v9QeFGm0LXGwQmnzQ7rRyYgSkAV0/RfEIAiEAovG90UvuzqTelvVuQdatpyiQ0H43CXLZupcrA2vb2Bs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "realityking"}, {"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-number-5.0.0.tgz_1517195201147_0.7769706172402948"}, "directories": {}}, "6.0.0": {"name": "is-number", "description": "Returns true if the value is a number. comprehensive tests.", "version": "6.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"benchmarked": "^2.0.0", "chalk": "^2.1.0", "gulp-format-md": "^1.0.0", "mocha": "^3.0.1"}, "keywords": ["check", "coerce", "coercion", "integer", "is", "is-nan", "is-num", "is-number", "istype", "kind", "math", "nan", "num", "number", "numeric", "test", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["isobject", "is-plain-object", "is-primitive", "kind-of"]}, "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "b0953635829711e7dceec0eaa92bc56521a546eb", "_id": "is-number@6.0.0", "_npmVersion": "5.8.0", "_nodeVersion": "9.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Wu1VHeILBK8KAWJUAiSZQX94GmOE45Rg6/538fKwiloUu21KncEkYGPqob2oSZ5mUT73vLGrHQjKw3KMPwfDzg==", "shasum": "e6d15ad31fc262887cccf217ae5f9316f81b1995", "tarball": "https://registry.npmjs.org/is-number/-/is-number-6.0.0.tgz", "fileCount": 4, "unpackedSize": 8960, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHw0y05qyjN2G0Uw0DINiC25SvWlVSgd2UD/ddJM8scPAiEArxOqwwCq3PKUhw7VEm21DI141kJs/ktPkaANlO5cGOs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "realityking"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-number_6.0.0_1522515759840_0.005598684369539919"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "is-number", "description": "Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.", "version": "7.0.0", "homepage": "https://github.com/jonschlinkert/is-number", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.12.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["cast", "check", "coerce", "coercion", "finite", "integer", "is", "isnan", "is-nan", "is-num", "is-number", "isnumber", "isfinite", "istype", "kind", "math", "nan", "num", "number", "numeric", "parseFloat", "parseInt", "test", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "related": {"list": ["is-plain-object", "is-primitive", "isobject", "kind-of"]}, "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "98e8ff1da1a89f93d1397a24d7413ed15421c139", "_id": "is-number@7.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "shasum": "7535345b896734d5f80c4d06c50955527a14f12b", "tarball": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "fileCount": 4, "unpackedSize": 9615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPOMKCRA9TVsSAnZWagAABmIP/iUfV7gQnAsn2vRNKWIu\nlxkfXFutnV1Bo5P8mlv37Zg+PN5/Ri/RATtKGEosAvHL/HK2HkFH+rLYkupg\nCYaAVXNOJiFN9vK61YYp0TB0PBtGDQ2UwcElxDQ8OP6Djni4fl2fLjVZ+JeN\nPF1bKPE6oMevNg3GtNMrb1QKZNGjplOKFmlmm9fe653LHwIH1zPYy6QQ+cgG\nQ8Czmhdf+NuX8WXqR1h5mHTKCCVAqzcpJ21uX9292aoWlzlijzg9cc8KEPEY\ncbUjJf4NMLs89/8G6tcpDP5qoVjQnWIgfM0mDXt4P7ESdqjusdyNa+3eS/Yi\nsQuevXmth+41kvQaVy7rLbeAssWNbc7IJ+X1qpgnUqTz4rtiAaRnZ6rzAE2s\nrr723lN2H7ALlnDVm5qzXLTTjHwbdYMYuaYHTOMovytYWeRnwhfNCpKeQcEe\nUYR3sm2tEdxsMI5ciu5ULGj/PEKnSOsQcq2y4QjNFz4F5ReXiv+CtphQubAr\nVkKwrjIovN97YXmyrkfJB1zCInCMUz11YgRY0wNaCJPJ4E0V4KdfWgCEyt+F\nqKcdylewrOAma06WozqUXzRH3uNNbMTfsE1nGqbcofJPNBre/VriCwF7zS/H\nLhGfCe6LJuY2Q+2Eel1vtwnY+vAoaWrr7QisjcvywYHvqWlRob2IxC1Ygp3A\nvUY+\r\n=M7jx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGex85Xx5rKhEgNSkg7z5bBu7vbcFbJnWJZbV3f9Td8GAiEA5wzgLXU41sE6ezCKGrmzsyMtm6M+2OhefJJhkzPEAoc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "realityking"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-number_7.0.0_1530716938183_0.28831183290614004"}, "_hasShrinkwrap": false}}, "readme": "# is-number [![NPM version](https://img.shields.io/npm/v/is-number.svg?style=flat)](https://www.npmjs.com/package/is-number) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-number.svg?style=flat)](https://npmjs.org/package/is-number) [![NPM total downloads](https://img.shields.io/npm/dt/is-number.svg?style=flat)](https://npmjs.org/package/is-number) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-number.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-number)\n\n> Returns true if the value is a finite number.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-number\n```\n\n## Why is this needed?\n\nIn JavaScript, it's not always as straightforward as it should be to reliably check if a value is a number. It's common for devs to use `+`, `-`, or `Number()` to cast a string value to a number (for example, when values are returned from user input, regex matches, parsers, etc). But there are many non-intuitive edge cases that yield unexpected results:\n\n```js\nconsole.log(+[]); //=> 0\nconsole.log(+''); //=> 0\nconsole.log(+'   '); //=> 0\nconsole.log(typeof NaN); //=> 'number'\n```\n\nThis library offers a performant way to smooth out edge cases like these.\n\n## Usage\n\n```js\nconst isNumber = require('is-number');\n```\n\nSee the [tests](./test.js) for more examples.\n\n### true\n\n```js\nisNumber(5e3);               // true\nisNumber(0xff);              // true\nisNumber(-1.1);              // true\nisNumber(0);                 // true\nisNumber(1);                 // true\nisNumber(1.1);               // true\nisNumber(10);                // true\nisNumber(10.10);             // true\nisNumber(100);               // true\nisNumber('-1.1');            // true\nisNumber('0');               // true\nisNumber('012');             // true\nisNumber('0xff');            // true\nisNumber('1');               // true\nisNumber('1.1');             // true\nisNumber('10');              // true\nisNumber('10.10');           // true\nisNumber('100');             // true\nisNumber('5e3');             // true\nisNumber(parseInt('012'));   // true\nisNumber(parseFloat('012')); // true\n```\n\n### False\n\nEverything else is false, as you would expect:\n\n```js\nisNumber(Infinity);          // false\nisNumber(NaN);               // false\nisNumber(null);              // false\nisNumber(undefined);         // false\nisNumber('');                // false\nisNumber('   ');             // false\nisNumber('foo');             // false\nisNumber([1]);               // false\nisNumber([]);                // false\nisNumber(function () {});    // false\nisNumber({});                // false\n```\n\n## Release history\n\n### 7.0.0\n\n* Refactor. Now uses `.isFinite` if it exists.\n* Performance is about the same as v6.0 when the value is a string or number. But it's now 3x-4x faster when the value is not a string or number.\n\n### 6.0.0\n\n* Optimizations, thanks to @benaadams.\n\n### 5.0.0\n\n**Breaking changes**\n\n* removed support for `instanceof Number` and `instanceof String`\n\n## Benchmarks\n\nAs with all benchmarks, take these with a grain of salt. See the [benchmarks](./benchmark/index.js) for more detail.\n\n```\n# all\nv7.0 x 413,222 ops/sec ±2.02% (86 runs sampled)\nv6.0 x 111,061 ops/sec ±1.29% (85 runs sampled)\nparseFloat x 317,596 ops/sec ±1.36% (86 runs sampled)\nfastest is 'v7.0'\n\n# string\nv7.0 x 3,054,496 ops/sec ±1.05% (89 runs sampled)\nv6.0 x 2,957,781 ops/sec ±0.98% (88 runs sampled)\nparseFloat x 3,071,060 ops/sec ±1.13% (88 runs sampled)\nfastest is 'parseFloat,v7.0'\n\n# number\nv7.0 x 3,146,895 ops/sec ±0.89% (89 runs sampled)\nv6.0 x 3,214,038 ops/sec ±1.07% (89 runs sampled)\nparseFloat x 3,077,588 ops/sec ±1.07% (87 runs sampled)\nfastest is 'v6.0'\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object \"Returns true if an object was created by the `Object` constructor.\")\n* [is-primitive](https://www.npmjs.com/package/is-primitive): Returns `true` if the value is a primitive.  | [homepage](https://github.com/jonschlinkert/is-primitive \"Returns `true` if the value is a primitive. \")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n\n### Contributors\n\n| **Commits** | **Contributor** | \n| --- | --- |\n| 49 | [jonschlinkert](https://github.com/jonschlinkert) |\n| 5 | [charlike-old](https://github.com/charlike-old) |\n| 1 | [benaadams](https://github.com/benaadams) |\n| 1 | [realityking](https://github.com/realityking) |\n\n### Author\n\n**Jon Schlinkert**\n\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on June 15, 2018._", "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "realityking"}], "time": {"modified": "2023-05-26T16:12:33.057Z", "created": "2014-09-22T01:58:51.592Z", "0.1.0": "2014-09-22T01:58:51.592Z", "0.1.1": "2014-09-22T03:37:27.931Z", "1.0.0": "2015-01-24T10:16:54.470Z", "1.1.0": "2015-01-24T10:33:21.598Z", "1.1.1": "2015-03-05T18:37:07.633Z", "1.1.2": "2015-03-05T18:57:33.841Z", "2.0.0": "2015-05-02T08:11:57.926Z", "2.0.1": "2015-05-03T04:27:13.630Z", "2.0.2": "2015-05-03T05:59:55.976Z", "2.1.0": "2015-11-22T13:56:56.624Z", "3.0.0": "2016-09-11T00:51:30.912Z", "4.0.0": "2017-10-17T05:43:56.559Z", "5.0.0": "2018-01-29T03:06:41.225Z", "6.0.0": "2018-03-31T17:02:39.953Z", "7.0.0": "2018-07-04T15:08:58.238Z"}, "homepage": "https://github.com/jonschlinkert/is-number", "keywords": ["cast", "check", "coerce", "coercion", "finite", "integer", "is", "isnan", "is-nan", "is-num", "is-number", "isnumber", "isfinite", "istype", "kind", "math", "nan", "num", "number", "numeric", "parseFloat", "parseInt", "test", "type", "typeof", "value"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-number.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-number/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"jonschlinkert": true, "antanst": true, "rocket0191": true, "papasavva": true, "456wyc": true, "maycon_ribeiro": true, "leix3041": true, "snowdream": true, "fizzvr": true, "jameskrill": true, "rioli": true, "gugadev": true, "fearnbuster": true, "flumpus-dev": true}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://i.am.charlike.online"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}]}