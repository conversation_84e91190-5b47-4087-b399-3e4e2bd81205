{"name": "named-placeholders", "dist-tags": {"latest": "1.1.3"}, "versions": {"0.1.0": {"name": "named-placeholders", "version": "0.1.0", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.21.4", "mocha.md": "^0.1.0", "should": "^4.0.4"}, "dist": {"shasum": "ed9ba6d747589dec25a3a978cfe41cb7a4a8de9f", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.0.tgz", "integrity": "sha512-2Ry/2+X+LjhoCY9GJLGAHGV23H46hpfl4her0DQjDKcLbKMwSowkr6ZiDlAsFMLTf1C5sv2a+kC+k4F6gPObfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCd38eHSpUlWNPVKBmo5nmGYomTiR9YgPcgQu+on7o+agIhAM/RcWzNF1dDRJxre5oCFPLgmZk++rvgErwxwu/WAsl9"}]}}, "0.1.1": {"name": "named-placeholders", "version": "0.1.1", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.21.4", "mocha.md": "^0.1.0", "should": "^4.0.4"}, "dist": {"shasum": "c6765b3cc1cd37a609cb0fd35b7978b2ca63245f", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.1.tgz", "integrity": "sha512-qwNcQBcVqawr6NfRox0+gqxsFe7n8xjsK/kBzDRqkep9ysWHhyIa4MDnM0W4uTu31vuCrqb+1IpnMOtCKjs48w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4bgk/mVXuyucMK/Zb7dRk0KvHg1S4vznLKmtNAQgXAAIhAJjcDxGdwpFTCfSlEU435DuOKbaYGz3bW0bTX7lW5WMg"}]}}, "0.1.2": {"name": "named-placeholders", "version": "0.1.2", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^1.21.4", "mocha.md": "^0.1.0", "should": "^4.0.4"}, "dist": {"shasum": "7ad139c223d821a216ed3e6ef40760e291c8c8be", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.2.tgz", "integrity": "sha512-kKmyKxui5Re4a1Jw5E1whkGEsS6Mh0GqbONJwhjqlT3UjYrbKi8WjghaiJNRWuzIcyq7Sen6MMGSadTHmYl8LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMU3kwCbF/8UQMU11yIReKU0rN1pPWSG+o+ARaj+MySAIhAKAlleM/MKtNshplKmQCwZ3Q8UNZ8eoWaL5JCmcVtqOQ"}]}}, "0.1.3": {"name": "named-placeholders", "version": "0.1.3", "dependencies": {"lru-cache": "2.5.0"}, "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dist": {"shasum": "353776ee259ad105227e13852eef4215ac631e84", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.3.tgz", "integrity": "sha512-Mt79RtxZ6MYTIEemPGv/YDKpbuavcAyGHb0r37xB2mnE5jej3uBzc4+nzOeoZ4nZiii1M32URKt9IjkSTZAmTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCURVtkqLMwvTPCd1VJotl20CsMya2+taKSEKm2kdfedgIhALeFPYuYL1RX7kmdaHEizlVah5gRBekjW6gWsAq5ZO6n"}]}}, "1.0.0": {"name": "named-placeholders", "version": "1.0.0", "dependencies": {"lru-cache": "2.5.0"}, "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dist": {"shasum": "7116209b234655ec698755932592912aceb516d8", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.0.0.tgz", "integrity": "sha512-foJkCS7Cg8K2lnaQEU3M/7WnTq+RyPjOZZD1sgFRY2FTvRZ7Vy7jB0uU9Bb41ZYpl0n6VIvneK8G24xu/9leHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiGwX81oZZfWtEMeU56zXaK0GsRSN2/E2fTK0dxb0vqAiEA1kH1XV6HqTGzVbg1b9LRVQ9mzmqAB7Ttb6/yfXe8SYQ="}]}}, "1.1.0": {"name": "named-placeholders", "version": "1.1.0", "dependencies": {"lru-cache": "2.5.0"}, "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dist": {"shasum": "d23ac8d83a399ef0f33046522594e2f0eb0af34d", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.0.tgz", "integrity": "sha512-zpW8KFscm4MymGWcl0ekp/LJ/NJq4Ubc1e4fYDkVhCBfYPQyaA9jiT4Hpz6HzkiVPApNRmdA9OfGjggjnCiqHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCak1TDmHCuR9XlxQWixeOheQyjI3dLkrrpt2wQQozvlwIhAJa73jE6OcXckdioeJZxGItK/PEO1ODeCvd2OCxrHO8n"}]}}, "1.1.1": {"name": "named-placeholders", "version": "1.1.1", "dependencies": {"lru-cache": "2.5.0"}, "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dist": {"shasum": "3b7a0d26203dd74b3a9df4c9cfb827b2fb907e64", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.1.tgz", "integrity": "sha512-6vPe09wFoa2xILUP20S3wO8IC85owY6RB+Zt/U52mKLcc60EFwOEypWpiOtpfWN88otl1BbaqwSWf8ymrcSLBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwwc1YYdEuLQm6jI1yYEVgf+MqTQMU07EvdAltB9jaRQIgDdVRS5erMZHGV2IKa7S7b95ImXrjQJB//ziNxdYI4Xs="}]}}, "1.1.2": {"name": "named-placeholders", "version": "1.1.2", "dependencies": {"lru-cache": "^4.1.3"}, "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3"}, "dist": {"integrity": "sha512-wiFWqxoLL3PGVReSZpjLVxyJ1bRqe+KKJVbr4hGs1KWfTZTQyezHFBbuKj9hsizHyGV2ne7EMjHdxEGAybD5SA==", "shasum": "ceb1fbff50b6b33492b5cf214ccf5e39cef3d0e8", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.2.tgz", "fileCount": 5, "unpackedSize": 7688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4lejCRA9TVsSAnZWagAAsr4QAIck7Q5XhmoCGX/KC/1z\n2sQk8jespnFJXfHnghqmdKXefQ0e/d7ezBaLAiTMbAM9x60RFaon3DaXpiNj\ngnPvGJp2JdLm3x35E3h4HJVeD+iQa5KB+ZdPEPq+HaF+sv1s5xq2V7oOOMID\npGuT/roDp1nS2Cg6ybZfgRvld+uPCxHVZS6VFt0BXXzeo0UiVJfzhOhVsyNm\nwCYuaDHT/BCGWd7qFRO2PGzdpaqQPivUJwU+iuvCxM/KzRYXzpWNpk2e7Vbh\nbmgqAgrVMYC7AkAsVnY7CcM/2mrsRy6IcS5zqqL3aNJtx4kIoyaDWxw2YpIw\nFmQOq/u5i4sB9iw4wob4ac11tYIHKfjIvw+0gHNx3JzrZV17zyWZQ9WgKAtM\n5Bqi1WChljfhLMg/fKuslsU8xnkqccKGaHYTTdZcWBAawqNO3gNEC2x1ly3a\n4Y/4bZ83xq02dLbRuJQSB1WoL4v+4tEnBnEuDj5UHQEC6jzFkXWz1zYygExE\nUSLyrC1aOzmIl1oN7BDTA/BK1HMkXZ0T5T25/tqPICQAs9TVWueSjfWi5lU4\nue7m5HjywHicHbAnpbXV2wn6TrzKfWwL6GwkUf1SRPBV7it7BfmyDMAxmN4z\n0omWS0KEnDx0syv4gKptlPP3ZdxR6rBbYTL0bgevk0bC/Bfuk/kuCHAp8F+W\nYftv\r\n=mENo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB/DxTE9CkTzr7Oq/Mg/l3we9MBcVnB6dqPzF+m8yXwrAiEA0s+timX9IbI4ZNG8jsSlIbhvd2TRlvixYMI1rAx0Y+8="}]}, "engines": {"node": ">=6.0.0"}}, "1.1.3": {"name": "named-placeholders", "version": "1.1.3", "dependencies": {"lru-cache": "^7.14.1"}, "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3"}, "dist": {"integrity": "sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==", "shasum": "df595799a36654da55dda6152ba7a137ad1d9351", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.3.tgz", "fileCount": 4, "unpackedSize": 7152, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGFa/QP4Ggp+J38zy7PHMgAJVz4E/6T+PcHaWmMt0GOAIgI+4RTnNzj4AzLbQwCXBSbyPvvSxeXRDRY/7IfoEhAUM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjv4HWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlHxAAhfWTcezycreVqIUOzHvggi8x/SOavX+aAS+wSr9aMLwZqbmc\r\nwIWyAhsqpUmye2QUx4n1vTLPlmJWjFsCOW0iHl6wch4k7w1NIIaRwJj5H6w0\r\nq5jnuXIGTucUWYRqVU4HigSxIwpr8OIM5Qo1DEdJb78lfjLajFbBJ27enwVy\r\nTWfwJZ1/gzEHQq02bp4+K15ex6f0tI0sLEtsl6CTtDSIq8XCaevr+lThkMg+\r\nvR8lScd+XDk2ZrGGiUGZQbhsaQZkCaSHNHOoSeSYKt3TlCxILoOaA5qsxXxh\r\nsjUuS0lkcMiaNErsh/9D6h5rVOwEdLS5StG13D/QSnE4M4RmVU3X/2EwoIM5\r\nT0ZmdemI6qmk/RlKY5isFv18kuurnn0QfhrLQNCTo3iAJ1njxadkSwXMg/TH\r\n/ofSUi2Taa31bnLL4n7lgd8qmp5myVZi4nb55CBEflFCPJFhbFfq7rkEC0Yf\r\nzl7p+u1/AEWPw5q42PFWLpbMubW1if0DgtHKWDh4yjqPaXZSK5QxJpwtscvG\r\ndJgF6CQDyqFkQObQO4NqbNsSSIR/ZISmRt0C41XZ0RnztBy57twweiJhScJ7\r\nhaY6LP08tYnEfVZnIqD3IcQAcClORdcMwHMjychL/3QWTaHzu9UjwU+6ETHP\r\nhNb2iKC6TdPJESM+tEeZCAeChh0HfDBCXaM=\r\n=1Y7V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}}, "modified": "2023-01-12T03:43:18.899Z"}