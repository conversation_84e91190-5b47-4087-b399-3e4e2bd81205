{"_id": "@webassemblyjs/helper-api-error", "_rev": "52-136ccb8434f31787a90d827ea57e709a", "name": "@webassemblyjs/helper-api-error", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.5.5": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ca1e64f99cd2ff39b3f8dd8661631663a45b9684", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.5.tgz", "fileCount": 1, "integrity": "sha512-BhX9M9ryxpCIPDAKQXXUZWCqs3nZNT0nxP07+DO36s4blEDrNYIuHJ7B+dd2PObS8Hu6WVpZA9Hp9mdMT+wXkA==", "signatures": [{"sig": "MEYCIQDb/5p7lh8GLrfJtZ9LyzhjmlJRT0F0vIyeZjVYIjGPiwIhAOHdbahUF5UJAeSKtWzV36h6fISbg375MCA7TFvSBotX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsmoCRA9TVsSAnZWagAAwMoQAI39IQykpU2qc3hfac0p\n+BFI2MEkGpIxs1sG9dj7m1h9dqtkKsPDsmp+Pl//NNjVyPGjkoxDsv2kiNVR\n476rTCIW/3YkLc0/19VErMHza3sJzjrWF4CWiRq49TxMaw3Fzkrk/0TVV1G6\nD8ctG7cU7g80uiFDM7Is5q+aDW+Xa2enXd6WvflW/XsB0Q/aIlzgtIGDbsWT\n5wcI8pZNIYzwFrx7Kll0mxtStWVUEbDEtzEQP1JZirOVbVLuH4PNBl095u+Z\nNk8HAl75RH27nN6akTweUQ/JpBQZsm7mi/MKnfn2/c1gmAomXMQ/FnY8MmeM\nEX87iwF+NLzVj1gdFqdhMg1NjSfR334SXQ6SHadDssbCTGBUEFuSErKFjsmV\nx54agx5BLG2ZVgC0Vsbkms3GHzG8ER4JQ4YbP39FLMIgOtMfaPYX2CfTg4TG\nC6vHHRo6bjTlanLZxKLDfRs1dZKL4+n5C+LDsmuDEO8r9KVSKxMZw8TpOFfb\npLYGyZXdgPWqWfT3SjMowCj7aKGNwFfDVPqnZ38/2WngTG0rRJx767GeYSHU\n0R7LOZUwmvNIttWZmz6qUsIXSOMnIbEo8SG3DyMjEuVRXCH+r+HFFMEsWdsZ\nXNNYvh6JgDEeoEbRV7bpTwaJIGSBQQZy5M99S2ShG6ANtXipuYxNk9icWjX2\nhmtg\r\n=jzz5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.5_1527171496081_0.5369540163096163", "host": "s3://npm-registry-packages"}}, "1.5.6": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "395a9f1889f7e57d586ef68343d4210b68db87e7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.6.tgz", "fileCount": 2, "integrity": "sha512-sxUfxfWbQXWy/6bHIxsdDVz3U488iHaPmFBuW9LK+sylPc8kTKU35tInj5XnbIg1czrFOP3UEljywxkHOZwSKA==", "signatures": [{"sig": "MEUCIQDQGOzuHUyCeoEHGxwY6yXHtcNXYGw02x93Llo1FdlMVQIgdKsFAqagtBZy/yYC5D19K1BgCCg6c5Rhf1TDKOm2ypY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsouCRA9TVsSAnZWagAAduYP/0s0oOZRfQtolu4Un+rI\nAQNfP+xjX/gzIeFjkgEzdG2HeH6EmVV9vDo0NaOBWI4D+lgMNnUsHhE4lwSr\nmKgLhH5LNQWi4ZWPU7i8gyNOjUDiTy0z6trl3umtihaStnw/apBdMsRMgsZR\naVYc6lTfyDmhE981zQy6BvfZxvggxZFXYCyKI/kLrP5Cc4sWEAdA9pAY6VKS\nmqA3J2G5FsTg+cDSAb5xLN4ZtNXMbwNX2N+3WUWQmGMCJYKd1WTLWcCgdTeL\nLWKTVXwxPyA1GQJ9HkiIV1dexzXbraQH7Bq3IHiXcQbnhGbViB0gFViyHVoI\nuzQLTLDjb+d9xQR6H7Wi1Z9JO+MZMPxdm+S2HkToCkePoWu1vC6Sf3Nsu6CG\n6vhEO4qunWSuKnQ78Pu7Z8jCFEzPI3N+2TdsbsQlHm1bZx0Pcr4boFs9xqiH\nhQbDG2Rpxr+PfBzCIioB0y5SF355hX5lwFdQ4PPAB7Jsd3L8Kudj1dnUV1fg\n+OhpUNRAPD0dld2nj9uldoGyKE0E0BO26UDnBSzNwWCUiSLcZ11yBFy9qcOQ\nUosBpLgXiWA+3ha9bQ6Pwzt40ldVfJOI8Be8Svl3G6v8qftTto4s3sDoTS8B\nGSGMmJwt86u+Xdf1KAwcPoZfhj2hvLhWaBmzPOMwF6/ubwKRPNy8hKjz3y5d\n8h0L\r\n=ilyo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.6_1527171630470_0.2037339430685876", "host": "s3://npm-registry-packages"}}, "1.5.7": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b5030962e196767ee30645ecfc03866857855ed3", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.7.tgz", "fileCount": 2, "integrity": "sha512-bHbJWjfPQlsIPagFxvkKYrQW7A3aBYnMioYaM5FoipAMFtHM1yWOEbxOjWuF9DkTl2dIlfUain8Z4/mOTIW38g==", "signatures": [{"sig": "MEYCIQDD0wiqNmIgZnJgzwuUyMmLW4Qvh/YRvsWuew2rFtU9hgIhAMX4KCrgT6+BUkWeOMZjUA4fzshZYH7ICPheo3gcD4x4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAdrCRA9TVsSAnZWagAAHxkP/1e+h4L6sBENLMk6wBqx\n/wtHbwh+0U6yr0wtUTdV5P5ah9BRrvZCv7t19bT1rCXTKYKwIfrKsPe6Zj5v\n1UA8czFY6St49NyoXeXCu9deac9IW10yXkGhO65gZZC+zO4MGJHNeQHDZSNF\nJZRZk/PD0GAoMEYQsScNVTyQltB2wO/C64cV4Kd9eevm71CyH/48sczMAvDS\nzwkpZ9oyzor4i94NB4H3pvIIf+yhFsJ693CPZHUWNh+QJtQwDNUxill85Sz6\nDnfka26a2Q0jp0vSTpTirjrR0wMVbDHmlDF8xxSPrnBEr2bIqSvioOxYgcdc\n7Gx/y1YwhFQQrFKR+3JO5rOMf45Cv9VWJMqVzKDDsUiOgTMdHUhoSa1Z4QMF\nreW4IsAuywj+UwjcKJ7A5OF88A9qmnJmWLzuwaQxMyQav0Auf4dkU768FaSX\nAIoiEYSiczI84KtpobA7UNxZU16HorQPHtUKXf6R7rnfkKhfoMDI9qo6ixYW\nRRoPsIhsE4OfjzSAzOCCiU6Mw6DaOIzmHIFrUMO7PISTx++vN+BClWDJ+BJH\nswcSvQsL+zzR9EO9wdSwoXg74Ppj7+OTMNYaxYfldjsMHn1scLUJ02UqnUND\nrXYwB483ytykVme5mcnJW/FeOUBKHyultNtG87NmfJTC2gSHhH+H6SaHuPnL\ng/R9\r\n=UASF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.7_1527252843265_0.7027145452046395", "host": "s3://npm-registry-packages"}}, "1.5.8": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f5570aff60090fae1b78a690a95d04cb021da9ca", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.8.tgz", "fileCount": 2, "integrity": "sha512-a9GoI/KeK0JPx3IzBiA/qLOMxS8C8MST4yaR81jCWhNqyAxlJSxoJFD5L6VnXl8vlWe6JbhdlZWFd/V7k7am/w==", "signatures": [{"sig": "MEQCIAssENbX5nhKCGme6E6kQ7RDpw5lU89e17KJP0sw71TyAiAMu3FKFlTpa9aMgFrAbcj1sSyq3fbfvfyW7IBcWUenQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/qeCRA9TVsSAnZWagAAX/MP/jhXDw/8IHdh2sbaxyrq\ngxZ4DSxxewQTSe1aqZ9wPq6pt54WmwSmwxi81HWZ4XBzuYRK4Er7wfasPuyt\nvA57WmVNLo/Dv7bQG/TOordjrttbCz0PJmP3blbaPolI/K6rl/Dj6lwL1G3b\nPG/VZ91qeqERztYgT9tbKy6h/3bQA/uQgWNRHAZJomWQPdaXB78DNh6mPA2L\nFy2UGJNk3ZowbfqY0JtlHzryDo2Dijtu2d9QY5QvJ6Z3wnehOZya2zSrrUCj\n8KA8y5BX4tPAByNco3AwCgnDHPwg+wbhyBtCb8AE50414Q8u9laG+rL4rWga\nGZxVYhKSW8ra2c0cgUtBIu3VoTpbsOIV9JLFOy+5JWqjnoe7z3vj4k6/E5cJ\n0G54APlVI+EifijTZ+TNTmqS/knEs/0daCRdLPDIy2uzZ+pHemyz+SFMMcHB\nrF+kA4eiTIvNQdx+vl867bHFMCoL195eUWy1ITcuqpt95BkPVlx5DLK1YrV4\npnvNdGZrKl8RBokrj4cDgG0xfbIZ3YqPTMsPV2JdZqA3srrOVVPwhUaS9M/5\nLVYzkM0vECM+ddmVKPCgvZUVtkhpm50UMZeaXO7j4CTO5uPKLzcEx0YFYNg2\nArDf9epabA24bLMsGGBGTFcll25ErsjgHv73NVZsxghQGgzIYPiAC2ObUOh5\nUvq3\r\n=iuKw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.8_1527511709807_0.4565770856154854", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c80e204afe1ae102c23b0357f1ec25aeb61022a2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.9.tgz", "fileCount": 2, "integrity": "sha512-tzGdqBo7Xf3McJcXbwbwzwElRzF/nELJN+G4MGGfm0DGRQB6UTmMe44jFIOQYT1Za89Aiz5DMQJotdnnLheixw==", "signatures": [{"sig": "MEQCIG8lErKsT801Mt/AzV1bGG0xtlNz/J5Cuo+eMBfUp0TzAiATx4Vz7tMsnIzn+9dlG9qcnyx/sA65q9SACVDnpmJrBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVHXCRA9TVsSAnZWagAApzEP+wfkigdKV9MLIOpXbjle\n4HrY9nvsUk5h0bHCy9wYKKBSEzTa608H+na3UV9DHpHbfWNgRh6lj5i1j4I4\nKuWiTNVYdQsOAWUjwCV3ElXx1Lr7wfkqoFx2NVDhUhgjiMfTUeAK0Bwd/bJp\ncvqWGnxLNs87MnUnT7Ux8cazLX7exM5dX1xpZU9Vpu+wG4n4kMOXzgxyZ/Nz\nggLU+sgpQ1OuN4ZpWfe+H3WWUrfHmDgM/9NWnix9bxJY+Dpg5LSrMVkiCbjb\nEh9ECR/xtRXDDrqhnBdYakO+Y0WfGWlK98HXbV2653BZrrJ0CHi4So+pPFyK\njRsZd8F+LGWQHEmgAFWd4n6zyIqiiXl7utQtaCZ2dzOzdnoKkgzHvN6Hs9ig\naw1PKoM8lKwSupAUQMjvh/5yxk/puWKkDwAom53U7sICRWXARs41pLOWmlde\nnpZKOWdtiifL4ZTf38rzbByx4TG2jo2TIFpE6sZxFAQRzDladslgsQBFbXXA\nyqTvvxLfv7tRaYppQ1nroQEYP2jFohGhRfAkFF7LzKtyOEBKWuQjnPen1Kds\nLJxR27O0+Kg0E74SCkZmv+npd0tc6ZkXP1+s0bKLcyzRWus00p+NY9f41XdI\nNZVHqVHq+B62b7SZh8jwzGIeVmPvg+x+Yohjn9FGgnlfofJZvMwr7+wIcnP2\nX+Zd\r\n=mP4Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.9_1527599574773_0.9127913255246503", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0baf9453ce2fd8db58f0fdb4fb2852557c71d5a7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.10.tgz", "fileCount": 2, "integrity": "sha512-OeWjB1Ie44sg5Nr8GVot5l+uclK4fWEQGH1b+HQ7x9GN9UxcJUIG3+u5dj2MTkthneQT1hUo09Wtpb/bY7zfXA==", "signatures": [{"sig": "MEQCIGlkTXudAAq1F1ErdVjXcxaqqp3blEnOKVXBpVDaXB9bAiB3OoWlm7q1Hk86VL8s2/V7yN9F2MwD24VP7pn4sgHeYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUgdCRA9TVsSAnZWagAAim4P/jvOgucmj1Arpl9qm57o\nGqL4Jg1kuIFgnSvhFE1yfFvLxPwoP2dD1/mbUTRhhNuWIMx0JCgde7cgfpfS\nJNPSDK8dL5Aut2xZxGM/beSgcZuATEosm4/VywBdqvegrxIl38maOluTnhTa\n+CI/d81b35U47kduNwpDjrfzh/Jr0sgiGIIU30TvH63FbLR3N6xyb7n7Vw/9\nE6Nsezpt7LYZOljhVIZeQzfY1odVTb16M8CCJs7cWbioPHIbuF6V1p0kMSg9\nmjFnG14WGx8UQ1/cqFkJjODGUXqgTGc0ZEPYDUki94JRJCfUPRjVVO1VScgb\nWplT9TF7IweSefdYvh3l1jQojMNDf3D1bv1ajVCTXv5oYgqlGRHyIAlgS3C6\ny50PZtYsuSmqXotivrIplavdHJtqrWZTbEm18I3MFtqemYteuzuqdQgWoe0I\nki7n0LcwIP0b+aRBgBaY5NBe4FMkCV5Z9cRdeGG6aF+gjszmn4TqG8dUTXKD\nZ/lUSC445sOIVmTrutyVlZh790hrbx2MYaGbmTDTh9yV11MjyFYHg726wft3\nER2k8cdf/kkoPteN/YzK/0754tNUaDs2qFPKmMXRvuKrIG5G1goZ8NnI4ycO\nqIrAKoayrqBJzZWAm4SdYP89CnLu1jcQRzxgeNuhQjUS5IS5BFs7db0IsFv4\nJTPy\r\n=qYfX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.10_1527859228985_0.18030421017384435", "host": "s3://npm-registry-packages"}}, "1.5.11": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5d8e37b29f673f311bdba10a7e1991f4b61bd9f1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.11.tgz", "fileCount": 2, "integrity": "sha512-uyRr3x+s2HNj7Wp8ieL2avehI9NCuI31O2vvoVGGos59G6aaKDP0YXeO8NykcDKsfuRaUQLHLt000gIOXqqaWw==", "signatures": [{"sig": "MEUCIExDyE0io3vo2d3C5CBKCSXoGSzdm8ZkW3eA3u72rRzQAiEAzmnUg/5UPhWLJUKW65+Y+an8rYIP3qTxZ9of40V0EuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6IRCRA9TVsSAnZWagAAYQcP/19ZaYPvhPjjx8RbJYXQ\nRdu1PBGQNkBH0r+AISZ22m3GxNGQkfLFT8qi24id5Fwvxu3B4Um97Yrvg/hw\nCCtqZ+Q9aR9tliZ8kQN1HvL5rzsSeRPFeuiyI0R5jksXdrSg2iB730p1pdV0\nImuFrdF4haZMIpOuwa18LdXCp2eIaM7ejF95P0m5Wo9xh8Cx+jxE1TZAn1d/\nUhK0MXgMaQ8wUhDEsslDtFEP0gBaUqKViVtlUPSTwJQ3G340rZhKqk+RaFH3\nasvDjcdQxhEibWDIp4ahwr3c17+y7kdXB/dTVMfTAoPzVhy5k0B3W91iQ08v\n6SXqqfS3zeuU8MN0LwEl0ERIZCimVDekMt6KAy/QQoSTZkfPnXG0LPHVeLu/\n/GH1eiifwvml78hw23xItWd5NkE71X8UNkCmRnoHjcPEWJhkNQuA7B+fgXVR\n3Q4lY0oQI2MUUrLbj1253sjVMIUmYHCj5N6DBcgy30+yXPJQCS61TsO3oS8J\nv7hbznb11YfeKNLsGLreisrNWZtXNOp1VJxQtrrMw/mQfqMs4hlyv66leozf\nTsxt8cfDkmrkfgy7SHo85L5tEq8YwuHwKy3kYUtDpyLGNQSPwqs+A+VwquG7\nys8ols5u8RF2sJow0t38fAYVyFvOIszfi+SqVl5lpjpJbhNvZW093/frz62r\nh+Nw\r\n=XmVB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.11_1528275471331_0.015687709100258518", "host": "s3://npm-registry-packages"}}, "1.5.12": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.12", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "05466833ff2f9d8953a1a327746e1d112ea62aaf", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.12.tgz", "fileCount": 2, "integrity": "sha512-Goxag86JvLq8ucHLXFNSLYzf9wrR+CJr37DsESTAzSnGoqDTgw5eqiXSQVd/D9Biih7+DIn8UIQCxMs8emRRwg==", "signatures": [{"sig": "MEUCIG2V/LfZBlkJ65SDdC6fB7LSuZ0KpQ6QRqLzM0QIRaP/AiEA9fGjO6D85UX5d4FD5gl1F17nwodSTzYcaVOpQncaSWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPnpCRA9TVsSAnZWagAAkfIP/irvwFpk876fHFXB1eIv\nfVgapD0HvoOp+IHWEz/72dtjmkIIy479uMEMjTzOqyhBU6Ci3h5OYcK5MjYD\n7sCVqieappRfw1j4mfe2pHU4bxuU4kprkPAi3LRL3ghGLKChYgrVOtCUS/18\nwqXSutcmMrBOQJ8dbsxn4jh5Jx6qAjBIo+uTgFKqmkJQ8TZ54M+5QHl3OZdu\n6mnQDVnYY/QGpJlsgujT+gl7k7mAevwk9TbSt9/G+rOruSc8LnloLKjJ14dk\n2Kk6SaP4zJ28RJP06Ml93wR3zF+SBwFuZks1F8xV4MMD8u8ePJajvTM2d11t\nbuMoo+65RB1K5iKgQrQGNPMk77qXqBzyfV5vRcSLyXbgAJlM1PhRMFtvlvp7\nYfiYxTGzUXNro5bbMI9YJjn/biPEGv3kV2ehp2X4LJrMipmOQxXEVlcnhP+E\nL2muo0leYJ7UUs8hNu1igIzRESDJGcx4aNMQmOLYuSRQSoPMFScc5N46PjAk\nW5RHuI28qD5klQLhs0ntFw1wOG4EdKlEyfFRErgEM6c42c3FB8q+oT58l6V1\ngpVuFbe4SUL5kH/Cz0RtpaJeWaIwO9Ca168Xcjd7CvYxTRU4jb4HDrukX4Gd\niwgZN7HmsHiIY92YQU8cbHYVKpjUFvO3yrB9FPkNCDpDnlLLMpJoua0uZVL2\nJ7X/\r\n=fyb5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.12_1528363497275_0.8414940969853197", "host": "s3://npm-registry-packages"}}, "1.5.13": {"name": "@webassemblyjs/helper-api-error", "version": "1.5.13", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.5.13", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e49b051d67ee19a56e29b9aa8bd949b5b4442a59", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.5.13.tgz", "fileCount": 2, "integrity": "sha512-dBh2CWYqjaDlvMmRP/kudxpdh30uXjIbpkLj9HQe+qtYlwvYjPRjdQXrq1cTAAOUSMTtzqbXIxEdEZmyKfcwsg==", "signatures": [{"sig": "MEUCIQC8/VOR4EhKKsIrH1c8+Buar6D3YUUAqC8ON4hgGVWJKAIgFFHGwkISToQsRk4UmjXh0MFcV2Hsavfbo3l/xvOAKJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4kxCRA9TVsSAnZWagAAegMQAJGk/3enjUHqHWMkTP3C\nNbGtrU4QWL7p57QsJhy1cFi4IbPdqtVgEqgNACy1W41dsH8t04sXpZpaiGs1\n2fXB/RNEkRA7fjEPj98UE8KOUoWawASL72sCTbwALpdgss+uJMDhjXnMjDAk\nusgwpyNl5woF+E1pAXIX/jH2wzo3FxSU2CAbRCcYLAWMmljj+PmYInXk/NkH\nrb3hiYZXvhqPWBpBsTC145GVPQPg8Gh2IW9lzV51aQ5YIA2YUN9eyr0QfVP9\nov3yot0qXUYvwDTuG7aC+Ki4JU5S7uRbuIEsna0p/Ym97Ir62pzYU1D52oIL\nSmzcjUPqMO2OCtGtQkoEc6yId/L7ZA+VPzzqXbtd8JX8pu9VOhvAoODh0y9S\ny1Vja1UCdKk6CZBNRRvAbaVkYbbjYpvPq84khH8s0cUpWK4f7CljX/PC2ZRk\naLeU5Ti6Cn6pSAUwP05+wYlLY7Ts2plQ45HL1U9W72SkEk5A4EhEv5ol4VY8\nMnIUSsnOjuKb1iCQv7XjvD8BfFrgHRfD8g9iedWfJkqlljXYnpiidg57XBMz\nIjESNrM5z8P+zUhoTSmTaICELerzeCzpJgWwtWDP966Jaucrq22IhTINpAfO\nCNuZb9WfDzJGbUHLIbPO4BSVV3q07fPitEq+h55tsyXMTouQjgFROUZ6arOk\nTpAe\r\n=cpQQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.5.13_1530366257146_0.7062849495878374", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.6.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "39ce66c87d890c63bc9821316b2fcc89179c3dc4", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.6.0.tgz", "fileCount": 2, "integrity": "sha512-3H<PERSON>Zj0Le8+AOSf6M+9dOaz27lBVS9yGHRoCDzGx5SBysDUHv+lAeA8Sh32mpUbXKNO3bMRARxtoFnxaz8kM1Sw==", "signatures": [{"sig": "MEUCIACPjAs6MYjEFicjxA+mFeItdvwRXspEiRrk7vOZ9dFvAiEAlYxsaMO/gojz/7SZBg2SAX3EjuDkKNkbfiC7UXW3SiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF+uCRA9TVsSAnZWagAAoDMP/jX7mfmFn9X9HQyH8iw7\n+QY9IO65UrxbLmfWuyOnaNM45CUgucT54Nnd/BxY5EaIEdWe/G5m6iolAomz\nroR8QiB92QVc6jw+Hr7ETFn8CmuwLw8Q8VYTYMARTZmi4yqIRhnZixLcCcvl\nb5G38Pozng99mwn2RiSIe7xK1SRTVsp8cztG3dyBsJiAbGaBe4R92/K6XaAH\nj2DHI2yh4w2R7lZ0VKyI0uaR8SG3nNHCiQJ6VC853DIAcDcXIsfz1rZ1ev3I\n5kXTcx5nTSlK4n2DTHd9X+gOHK3wCvEA1ndZOIEIn2omRjzhCxTTVVbtPqPR\n3CUvKTqTQdtTCZ3gysIRV2kVlBVlzLNlKE/7z27PWD349UnMk9ceaBee7jrR\nENN3s1ojEMRfLYIxdk6+h6n3jh3xT2O+0TBZ3bhASB9Pcj0iJR69Hyv5RQBu\nHus/WiaMVi6qAWL2VInHEAFaMX28DbQlxPqbo7YY6YNAj3I+GR7Fx5XkQ3+S\nSjo+3WQBKRAIrULy+lrIhfiqKFdqXTStyRUUgfyTPlodt0OsBEgzKdMlJ0Az\n6PIgS2lhpzKeCm1wC+kTgaxF0qLL2dy0RvZUe4F0FNUx+DM1FgPrFtOy+3RV\nGDDzR7P23L/FfZrFAtmxtobgFMl5XaXSTyMiMZIJUdw8Pd5ZGXcok8hLwySC\nLGxA\r\n=TpEH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.6.0_1531731885918_0.7173362621529829", "host": "s3://npm-registry-packages"}}, "1.7.0-0": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.0-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.0-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c06f8fe8c90eaae61ae37f9aaf5af61e7f271a0e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.0-0.tgz", "fileCount": 3, "integrity": "sha512-VCYqfbdngDOnHl0uB81vF8kDttEfPNoiluSLlTN1uZvMTVj63wk00yMMUoB3THah4TNNl5GA/TOimwuU6d4euw==", "signatures": [{"sig": "MEYCIQCiNX3XdbK7JNxE++FpOE3A04YsCC9F+VlJPkMbSw+0qAIhANB8hqw0YRvtxfc6deFVzm+aAGHphFLTKyJobh6UkOPu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzlICRA9TVsSAnZWagAAhpQP/3UcQxXQMF1FVMaZHHD1\nPw2+bYsLyA947MuatduwAkhwW47pViw77OlIhsXh72z2h8ZChuUpGacDuCj/\nEW2ZS7hxQItlUrv0NBoKYQdrposkmnHgUXEcHbub0w/GFNig3ro9blR43zSk\nwUx73bqunN6eahKpr51YN5zjAD6FUKpBs8Y1LvxLsD0cTBiUvW+mLHZsZdGv\nYAKO/DNUc3kml7LyMQsP83BqryUVcNCWJbFY/9UPAdkBDdu1t4ylGX6KUGab\nZd0j4OjkyLmAYF6EHs2q4FhHTUQYUxouFtjfPhtrSwn6iggZyt9naGy/RIR8\nsiKz04QvKjMp83/j8x5fMpe1WyOqsPO+mwnvmpuqU/0Rb9BKS1gkKPWheyDx\nRNRkNRsIApKQF6ZNd3PmuWpAuETa+ga6B/CnjGINUskhfunV1OpG/rNnNBp3\ne6ToE8OCVWzwbYNtAbHmbHc03NY7acTexeg9rJBpaQkWMXpjhcZ1NjnxY+Sl\nkwxiwB51GTI4AJU8r/uWOycDNT4/hNyAp7c29lNSfohpfwiNl2M6YRbC2eGq\naqdL5WPxXpSagAGNYZtZwkp2+ahg91uS6N4EHu+sm5TVKE7lJnn7JrmjQaT6\nw7IrgGGUhKw5e7fTCRxsyRAuV0R2JRH8H5z5M/Va9JNeXtwmV7teI+QsQyGA\neitv\r\n=OnEw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.0-0_1531918664640_0.4310039453792942", "host": "s3://npm-registry-packages"}}, "1.7.1-0": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.1-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.1-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bbb3ed39ffd13545573542d485526a32b4650868", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.1-0.tgz", "fileCount": 3, "integrity": "sha512-OZvs6qw4Mb52LChq4vl6Ge6AFuqeWEfv9+nP5PVyeKIh9qhg3WlRF2EN3OBx7Z74z6CyWUgk3n9ca5339t09vw==", "signatures": [{"sig": "MEYCIQDTG2d0LELZvqAbAGWZWfl7jK4VGKLNvpc15UHrhH4U7AIhAN1P5SC0V97a2dAhiXr66OjyPHnz9qeUyGGf2jrvp4OO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzvqCRA9TVsSAnZWagAAgCsP/16gqC6LHIuJqs0VNz0i\nRvw9MlG+Nx7/VLb2tt13V1giuKY++vZNRaCQK7wOUKbsheQPogHujrlxiDZf\npqGI6NQGh+SbiJ/20xdVQe+rnMKJ0XnC/jSOrkxFwdgERb9YpuHUN1Q0NE+M\nqwRnDtoPL063LSd739/E4sa1Xls9jDnRCU+zkzMXgkcfws3L4iRics6moO9q\nxVMAuVtfxwMZR+aZBhduerevKcE+jZYB3gtc/3qjrrZ5fIkdD9X8NsRiJi2i\n9qZjYnAtP03G/zRX4J5MOKmHH76Tzm1zAd7h4EuAWm5ig5dcfbMEEJzZzvG4\nz4Lw1f94MDPJPYSGzWhKqII16FMeVrs6rqXXvDMH8us0CJfiWOCZdZPGNY7W\nXVofU2s9eJGN4t6VTv2DkkxdvlexAf8opSEQLShfJJeqmBJhRjjCyOu1fy4h\nz3zgVRQjQaYOmDOTuROfqIXFlyouSIh3AdpKADHWuz6nPDzm8Cw80Id3IUMC\nfMCt6Ovgx/dKXdOPNRSrWwM92YHBbWqFU103b8vFIWT48pDmCPNAcrKbR4Q5\nsk34s+NbH9Q9RD6lxfJGtmDgWxyh99yo+g6lZXcylATlGh/AZoTT/pgLacnK\nruMZ/GYqBuX7P1PmflyDsMJVUhKTV/eRlKOzR6vSAJmjOGkpe9ZTwqIcEUnv\nCOdD\r\n=111H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.1-0_1531919338593_0.43543393541261266", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.6.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "28e98f3c58b30851adf058dd5f2381752cafde89", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.6.1.tgz", "fileCount": 2, "integrity": "sha512-XWh6uHWEmV3+oJcD99fcPubw7iKGUgVCPNtNjptW+NmBH01Ndsczdg75pL9G57fCJhMWffcLzfiiAsDVZUPp9A==", "signatures": [{"sig": "MEYCIQDYMofSnkE7vasTm99q5sa/WcO6bgaOjqhmHVnEt72JZgIhANF1kjTu+f7ypPCsM+d45gnmGV31UaZaP2x82Hi647Ks", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0WOCRA9TVsSAnZWagAABCIP/jKcBtgBi3OeIg6tfRAu\nuwPNmFYY6WtCSobzLCg98c0OZFFAP/HvmY4GqNX4aVqiCGoGOClI68kiqoMj\nN8xcFMgV9OkpeSsHcOALxjHwKpuPr1IAv+F7VONJZJ3PGmf330KIZdYGzOlR\nnm1zhX2G0ho1+3aQKcOxz/UTIezAtYQRSuC5IqAQqPJFiEBgWSfZTP4oEH89\nLe9ce9iLJ4cW6pDTwGi4zYgNszzuylMuaTxswHHm+thJ7L7m538JC6JcOjmf\nPc+FGgqQN4nv6bBEGjQYxdHi32xZzICP5gEOnsBIeso72ljyCZlLYUmjx6JU\n0agfsTieGFqcDPz92hPNqyE0h6qIXgGCCqYR+rmo66FbgrJPHQD/twrXfrpL\ngxAEvFWdsFNZrVborKHl+zNPu99cTwB6lpHD2pcTPxn20x93FIPBQCjDl24m\nRfVCN2wSIn41V4MZ3XzkGbTF9IG8/iQdLSp0xXGW4sMr+0vuhM4GIjcEPykp\nA0JNhdsK+ATxaMP2B8k5kqtuCA1fYnwCLUJafpJLZvGdf/SbhMOBnH87Qp2M\nZMZetUfXfjl0QXO71GsGeMr5v0oRuLX1HzPYmbAzsEVgcpKeEdYWHjE0f9CW\nkp5NBS+inoOOUsqT29Qu1jxF7NQE6B9aVYlrhglkt3nm9BY+dV9lmWZI5L3N\nlfJ4\r\n=EviT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.6.1_1531921806294_0.7493493875320749", "host": "s3://npm-registry-packages"}}, "1.7.0-1": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.0-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.0-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "8668f979e1660696f8679ef52fe49894496d1594", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.0-1.tgz", "fileCount": 3, "integrity": "sha512-TVS81J3JeDicmoc40z7ekVhpyY7rvwbdjwnG2zyqBf1vul5EJY6THxhuK2M5Hv9I4aeAN9adudlCnxccQuNrYQ==", "signatures": [{"sig": "MEYCIQDhBbs6wcAumAjupZYEZZMwxzNCUGsy1U8N1VoTVGUXIgIhANA/9ut/UTK3YnadfLLvx5+3mfu4dqhNRtkthk+A7cz7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1W9CRA9TVsSAnZWagAAYlAP/29xpqmmpaqcDOHkcSdZ\n/ClillB1MycQ5eZea9nYkIdA8pu54mnt7talNukKMCG+lxyJZDi9CZtyJCdg\nFzA36PbFQ4JX4OUkJX46oY4Fb1ZTIWFG8P53hlfsaLpmukMDZZbDIO3QrmRE\nPZVOt1IuJ5ezq7t0jch6RC4NHa/U5WFzHh46u/u83djL9OEp3igJYk6Zjjyj\nL3kc+vHocqb4Df2hdouw2G+NMTaJSzP835VlZn0Anu8vBBm/smC9BLIkV2fZ\n4r95jwbpjqhzghPCb0AyFd0putJty/R8DOTN5ZJFm7U1zze0iPykvty19oDg\nRf3z1hXnD48WOpSIdfrxYVgUSNT9jNkNTDURFcdJdbO96ViABHpA1o3mnihG\nbj9Nh5jodIMW194R+JsT6wuzBwT/MHouVOmkFIeaPux5cQZidG4yOcbsoRbf\nZ6J/RNOu4NMxfYjZzfPw9jLxSQhC+ZN04FEuxAb6z+XX4g7sAzIZAabGDdJu\nYyBI2o08xCh4OjAQcByeeDOAgsTBsOQb5KGnRxRTmnwFNtHYEWgHtk1XIiuu\nqZkde5J54Wwswdj9UcsQyp5J7JAitO1zibRVy2+OUGWdVY141QtjXS1mozt9\nwVf5ddW6L/+sA4Y49kjJFHxpMaLmHj+flpCdvgr3PI6g2lPyvGZMoxmJYp1J\nM9oL\r\n=lTgU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.0-1_1531925948978_0.7023850763997712", "host": "s3://npm-registry-packages"}}, "1.7.0-2": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.0-2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.0-2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b5a26528c284a3eff0c2a03be94d0b5c7e9f118d", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.0-2.tgz", "fileCount": 3, "integrity": "sha512-4gXyQ8kKlhFqk21868R4yUGdN7gqIuU+AJuZIm+7d/C0vJ8S5BUIshUU08N4YFg6i8jTM4VPi9Ddm8h8RydMOg==", "signatures": [{"sig": "MEQCIFRVV34NrRL5LY4SP3ln5CENaI4OX4y3brWaPpvJ+8NHAiACvNw4dOiBQxI5GB+kLHYrEEi7lk1OA0qv3Yq0ZRJt6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1lFCRA9TVsSAnZWagAA2oMP/3c4JW0l5gSQuGizetEX\nPZbrZn/x+6a8KPGcSgo/dgX8yxHNkpwmIoMxmszxIi+ddzb1YjbdHrDGvTdl\njVSVmsdpLqA4brV89uQVHqcZFiILX8xNqQCNkAR/RNkCUi43ighN270teLho\nT6ZJqb/UaiL/QVUIAHWlMaz/O/OIGGU8+L22ZaiMuEBLmotpu4l3RPyY+oB3\nfNlPl8Sr/IXmbJ0IQE4fI6bld7+cVBz4Kz9/rSzTgp5XqKujsIlcH3FapYL/\nWfDlBRZFb4/X+8DR7EhAUqkkQj4Z2S9MeF7YOh23w50agjkg/J4vwz7m6WgS\n6lfC8SNJieKLsjVedPpyg/wOeWyZbXk3+nl1KTjNnqn5rkFDqsGVF/mF3FQX\nDyeXOAfGxIuVRI74a83ByiLpuptdKHuXESfzgW89rjvRAfhVn7SbijWWrbGi\nhSASj0ut1/E6e/2fDmi+TYgUEo8mYiqzA9ecr8rKAaVH6HcGyzRSUBTKWR+l\nU1zGLGDDXqnZ/d7bLIb4lylI+78RkxpRsWP0ZGGrqns/EUivpPbyeRTRp57t\nOKG89M3DdbFtGH8HqVVoVeeFpQ8ikY5enFGyxaC7xhNcnWs3BLMbutbXqOpH\nuhWVrqdFWMhFDfzFs5B5IIJpyNguzXvEdeB7diza0YJtDEAtM3aM8V33lJGd\nArMR\r\n=c83R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.0-2_1531926852979_0.048652223318489396", "host": "s3://npm-registry-packages"}}, "1.7.0-3": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.0-3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.0-3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a4b30a03b446f8acbc212fd25bc5503e31fd1ea2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.0-3.tgz", "fileCount": 3, "integrity": "sha512-MQoaUHElEs/q/vpRAKlU8ylo6G3LJdF/TQsF8P3MJT1pqbYIFK1h/Zz60i6zRhEAIB4EDWb3wFIPPHKvmhcALQ==", "signatures": [{"sig": "MEUCIH8AlH/Vh7B6eyyUuLgy5MtH7khvgBQanvNMBN3OR6W/AiEAyKuepa1UvV3u4Zh2lD5gl5jZAbpHrNdzclOrbmDiADo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT48ACRA9TVsSAnZWagAAjg0P/3BKZagfiel79Q7Woj2y\nM6SDCnFd3OOJrGDQzl0IBTl2Pcc3hohPcsenf1cbu/HAOiY0xe9teGygRAwv\nsqPVcUUWahJZpSt0eRQ2jD2lFW6163/eM6N3x9cZdsYlvjcJugBk0uk622Uf\nAyf8+kMVkwyxx5B/dTURpKOwixdLxm+onPIY4p+UcpyG/oqZERgdvySTLoh4\nbK8xam0tSQjXOPohBQTln5yGFjpXCDBwjjO4o9ZWXkpNtNPYLeeZzfkIFvTO\n8572xqEUNoAnQzbwqQ6JMe0CD/ghKrnzFv0uZmnCzYxNeTr+iYDI/ktsiFNF\nJjCKfIBFTfpchRna/IuiSEqGoA4OCTEHct/24Uga5UCUKR7ZZKncsgioUexf\nqkBnoH43zEVYiOOHz3Bm2wnFvly6Zvl56Ci5zrAM02/nkPhwRu+HhVQ+QQy9\nAjLl0Z3VWjkHaR/ayvKMR6U57TfwESnMZyG+UhGQvA4ZgzNp6Ba10vzFK+A2\nGyI+Tup/yOGhyLe5E0djFnGOAiAtMykLmoYo5Mzvr6KxbBA3U5Y5a8YbLmAL\nonr9ypsDWRr62tVw+tR89MDG7Iv/VID3enqsDkxUAmoghGPXdR11aCwvi61C\n7Ag9OKkUktceVLXgb+SSN2XZwS600Lz2MQntdFv0+8VXhsMpxOWF7Gueqsdt\n7YfW\r\n=238j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.0-3_1531940607933_0.5593178177523377", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5c6f502792e72b1219e7c0acc93c0ca0d44285e6", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.0.tgz", "fileCount": 3, "integrity": "sha512-TdAReYXeyx7QPtpZzCgFi3AC1ZuteMF6HARWniLFGkrhb9d+VMJ7eT02vCUdD22om7dkxwzH9fdYBFM2KEeq4g==", "signatures": [{"sig": "MEUCIQCESpUO3iH8NjlShwVTnVteWcMD6+cBPUeaNTzLBJPVAgIgQxfM5Aj+rQVX0NXpUX9xm2uXSObNQknsCrGZQ5KZ7WQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULWgCRA9TVsSAnZWagAAf5EP/3+SQQgW4LYdOLKaIxfC\nAB1dGld/E9Am0ypTiuPV9TtrmG3SMQRj/smAXEeaaJbVh9h+m1NcNhe2YBrn\nm8lQQNN6bnHdLJcI6KSr7ekRuLqB8hexaaInf/+/8SaLHjBeB/yzYdQCphfs\nubMVppSoBmm9GHyugrDSFithoVjI9/NY0UVyrsY/NIA2Gze0gSqIvmUhwc5X\nqrP06Y4HHwgoAc7V1v95P5V9fMzSoanYxTdY9w5WfL7BzMXMAgMC5ziHEniq\nTrR4AhQID8mqbnUoE9gBmPrIKKj8NElvpzUoX/grjwQZkqJGzDQzrO6G7cvG\nWMVwA0/gkxiqYBtqP/GuLwB0GWmrMYn0IxShMhTOko0kgs3BHpF/92Qlb6f4\n06YCPHc2hiYtdj20OOBAltVMqRFkX/gjlkpw3nrOQyvGZzCKOAzyuMQYo0yy\nW6WCqMC/Q2OS+f8p7j+Oxn8r+tMPxdQDLljjOcESp5MoiTNTdY6TsWwCf39m\nP8rQ0FDAEuuhqNQpgY0JyfStli3NGlpXRPW8tkmMDPjISjTmxwYicSOy/C/H\nnxp+OERNMfzk/2uulhyuWEZ2vSnOUyON/FlCYK/2FFWVHtZx0ExEM2i9B7/r\nbod/WrfMiAIJapaNPGcZ+pn94h4sqm1HQBkeNyuA6RwWwVbSXBZMNus6mnwC\nPUrT\r\n=bzC7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.0_1532016032144_0.8134091612568093", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2ef4d26d2cf76700ca0588ffd483bde479a7e7d1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.1.tgz", "fileCount": 3, "integrity": "sha512-szBdvNw7eqx52/xiesiLLsh8eSFM8jGk98yEpJipclVD0FhjFD21Vn3VdjLKu76O0zJp1lRfnltmToM2AbgEog==", "signatures": [{"sig": "MEUCIG547JtMw1ZyjM7z/QJh7ljcPgREYB5dfnkuxyBVgtxAAiEAt34Qy+8cUbyau3HGaGVenYkQ48OxhpcQVJpIDx5qeOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULzZCRA9TVsSAnZWagAAe6IP/i2+OAfONqUjZ9EdHHBD\n4McHSxNyPLih+xJh+oAXb7t7zpU9mgviY68nIXlHF0tGTYrM3tY0CUWeOC35\n7IIFLUUmiEdRiOXcVenypXMCzERf+GnXfOIxfaiJhcWzQ4ptxoEmtBBwmrXS\nYFPL/Y2hMX3J7g9F3KepxUhosUg0XHRqkj3i1ZRcRAEfWqaBM7cRHg9ICm1d\nIKD6RqkTrqQ30BeJlQZWxj+cpvgPtM0Oz5ePKT18YXImGzfsseLiX+/5ogsm\n2HyWbXtaRt78t5ITRN6jgS6CU00Bbt4OIy0o2t6/xlD8xse7tXsAcliyCFP0\n6ogJgOkHCHbCmj7CLh9A67v10vniHaDRtt5bNU2jD/sqNZ4TEnsSYP2UyDFc\nyyCDbWLbR6wmwVuKYBpapqCM1tsXGdSLP97IXp4O9wypHMa/Pu7+5F7gzHXW\ndUrHDxwxHWEr7Brn0i+tDi24TkOiNmcAPq98ljaWUNTwDHvxLpcBJdieESmU\nSwo1NxnntXTfx1lI3lZjPuLJsWjKrlcUH8z8NXkF3UmyjXT9B1iDdCpIi5Qd\n25cvYKzVc90Qn7zdPJhieM2nteY0PkjIYmq83pkYCw2yPgdL9+LOhBjqAy5f\nuHUGKhBkSkYYAY7TNzSLZDBf7VWAfL9hArp6nZNtDNTlkUlzcF5tZ6uMwDii\nYVEz\r\n=yFYj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.1_1532017881015_0.37923961780704785", "host": "s3://npm-registry-packages"}}, "1.7.2-0": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.2-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.2-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "985007b449a4522930c34d4caf0f3620da35a01e", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.2-0.tgz", "fileCount": 3, "integrity": "sha512-jBOGCP2pxASgRyn0R/0ROOVMjVcQ1dLrjNR4EoQKiIc7/mWgrovoVDoqfP0TM39vxJwXu4/JF0gaIUXx6YfYgA==", "signatures": [{"sig": "MEUCIAtm0XivNfQEYNoRK76EnsFAL06CmGvHzsSuim3MKvMzAiEAyJcJVpOjVc0LeQ2b/tA+U6rDL19rGGuqwcMfIkxb2RU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNPYCRA9TVsSAnZWagAAl8cP/1M6Fr3EQI0BavxldV7r\nSZrco1BtbjnpMPwNjXA6MpsRPl9dRU3RpjYf0H+KCy2QOkY1NXR65n08j32J\nidU7uJCutLV1+U0ZVUg2YYfynGD5cn91pvl66K7CRWuLDk3JvKax9me67Cka\nzDIpF2HDsz25qXC74q7uBBv7Euv8h1ULjujlCdkSIe2P4IyfSef3OzBT1rgS\nU65E4NycCXas5LL0UL/uIJcPB119GxldCP06O/UI4NBqGNCRYv3H4HogADbM\nIo8ShQKO0NqjPYkcgp6H4NV3bFSMTKk2nh8wvmvZnnxjybL7M5gGuAyhKGxr\nyGGk/IFFNnLCzGqg9QTcigjdnG1EmUPHLSIt/SR9jlSDVH/Z+PHLOfSXsf9R\nSlw7wuiLyoZapXQssY4S/SzPIICR40NuXrIMllzDID48IODZNbrgRyn6V7L+\nHiDoR/l6QPSQnVpBWhIG7Qj1MuoZpE5kivN5qISg7Qm9hRrPPAaNbvuEdK1G\nEG8kh4IWiiaE56gm67QihuAhP5SXWiZyv2hx+YD8YOmsVuBKp/YBghw4ivDx\nT1u+9cfotMfbJGKbgD5WNRV4vpPvsskpUMNUWsNQPi176r1x3Mmmfi9zHxBl\np347lny1ttzYFobhlAxFSnnuptf6VVJkrfelrEJP99s5D4YmSnE+yNMO/9J1\n5wMv\r\n=KXqe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.2-0_1532023768117_0.3360878003108907", "host": "s3://npm-registry-packages"}}, "1.7.2-1": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.2-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.2-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a4a201920a0469add1652d226eef515bf89c05e1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.2-1.tgz", "fileCount": 3, "integrity": "sha512-BdoSi1kDnOew671iDUBKeGaTF/dgMDShIIghFK/37hOO6kZhQkn9SYa8BDY5hnU7M1wqvXUxAs66xV72arQaFg==", "signatures": [{"sig": "MEQCIHrtdjzeCKokn+hcXpdbACXx+iLqQAzczHN4HGVrYPvIAiANkoBhZFeDnuqTxXg1V41HrQcpXnYHbwhCPSKppTMIXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNdHCRA9TVsSAnZWagAAT8cP/00U8AGbHwHFr9MF4Ju3\nyqUqrJIHZWzlohAHPjzN8L4UINaf/nsEkk039xeKm/pD8Jl0JF4aZiYfslgj\nOPfYudwXUZaJbl4jR7KtwOt+ivLOcO1ybhmBhx2WPuk+oD7agxyjjmxpIjXj\nxCfSbXFOkYpSBDZaGYceXaevbzur9s2/M1ncVV+uxY4Pq8CWsM6eJTSPacbX\nVjNXQhMMAVcNkm9ecLE+DUjqR5RTTAr73Gro9zHcma7AvviX4JVsYhjgQxSy\nHcSmI0rRB2YNTpuh0GYU/nrO3sCbILJSzzMbVycP52M5mFskJhmWMrFw9zOI\nwNoLhfFFRGn6wahh90rUUri1aKXziC0yn3fv5orBSVJx7hSnXH/PMxnh//zB\nVjxR1sTZvkUCP8/C8UMWs8S3uwm9Eg3Qm8d3iQFQDBzQMW5FaFQbBADrca49\nUB0laDgsKm0XOGOMdrmNRCVBAJkRr/xeR4VP8ql4iKyMFRzkWUfzjSOzVekB\n8BMB+GxoM4drlO4BlTpzTagAkytO+fItDEckBjbKNOARqY+zCAzh5YNcmWky\nnurgQtTTEJd6hsnwYigLLMwpoa7gVabDsWCQP0nVQxsaMxj2NpG3CfDjP8cG\nY3MMs6RFcd8JglAQeHQ4Y/m7UqSqHO7D2WE9Y5tNAHtrJxv1aGb/jpBAIzSQ\n7Mq0\r\n=+SKo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.2-1_1532024647249_0.3495742100490009", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "91df5557ec99f624d6dfb604d5d4d5e72305e55d", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.2.tgz", "fileCount": 3, "integrity": "sha512-5WvxDKzBsijl3oVPTbHRl3XD8crnYBFkPpuIhs6w8Cf3NNUeIQooXDSf2PAy6hZ3Xyk6rwyqBPcx0gNsOVul/g==", "signatures": [{"sig": "MEYCIQD6zcvHkDZ2EGl4acKjb7HcqXqZHnToJcY/EnRB124x6gIhAOm5jazRf4vj+JhCXdm26q+YTHxJ8LUswd9/IXj6zgBN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNllCRA9TVsSAnZWagAAZLMQAJVSzhOtzdC7vPHYEx7G\nmcn5ufUBixDOmMHV25BIPhc7SXQxNO+rKF8/uqmkSy0SRsOwuklc28X0pEWQ\nyd5R8iAQluT4rzdc5BLQTFgEwY7ZgGMsUhPvqLw0rMkg4Y7PNGrYZxTiiZuQ\naHBR27uVT3Ss3qxXpyWlAqgv+bdGB+vcwPEgprT2reJ5AMXRUtUmSgwV11u9\nxS8OAcqQPLkhsTkM7qhTwg+YZYXYmNV09OQJ1/Nnh1GFNq3NUTf0oK+6+wW6\naMso475U/lnxTWkbu4SRqfvJOWsetMU1Dygiy9UIjSv5Uv8aDvbJTTVDkolh\nvjKcvHoMkLbbxcF/UmMkwYmu5JIYMwg60mDD3Mj+2eEdvswFgIUFrt9hm9vZ\nP3kV4+ET8RZGVCs/RMxUdTUnzijUnmCvf/bBaBtv3Eey0uGhrIgl1kg61Fgf\nvwqtHH0XsfG+4e8nDQgG/7K7oUJEiP11SYVt0GTA+M6cVG3UueKsQHinTSNN\ngY6Z7CFu1/2bF71LoZsg0/8SETD0SaAVCiXNwZ0rYWVWGg+fk+lRpLa193hq\nbMZCBvX5xkKubJnoFsF2QxhY/yGkCPGyw66olWn1ds44mYLDkd+X5LjnW5ZE\npO0uUfPXwdLl+K7xpHykFq8CoWmTwk+pEBi+uXUmLxVi0aNGK4TfarZ43zaf\npCjW\r\n=hi/c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.2_1532025189689_0.26894987282838834", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "75a1268afa5e50579de4ee2970b5054b0c0f7fb2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.3.tgz", "fileCount": 3, "integrity": "sha512-EbqG6l1bW5AHyUCGRFSzzuRFr1iiskkJe8xIKcbDuJ1LzHWHPrHaY4cuGTDsP2/8TUC2n5eH+InZhxlb/Y1UMQ==", "signatures": [{"sig": "MEUCIC3mTiqferBvW084xu3CN3fK6owRJcz4B2GssLpKeqN/AiEA+sNrTc2Ymd6c3FUt68Df7hF53/0XT50tLcCWPOzvdOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX1vCRA9TVsSAnZWagAA3q0P/i+XPljI7WdLr0dwV8ZK\nUGwUQk8hJCcg2HctabkI1bJg7E5wBBu/VQXiO6BSoOU+cFjSv9EDdjWHqyMm\nKZEQt77ctQdGaLf1uqzpkFFVeKgL0pyFNOu2TWH2jR3OaQhta8Ub9LAYevev\nGIY5rZuc+xobMv1JdOchGDnK/f9iDFIfV5GUUM54liMuCKRJbX8Dv0H0niBJ\nAmazJaseK2/R06NV0m3BroXarOvbL154SG+jCaOCPiqXlI2gYyHIfoXLxc/d\nkAvTOSpn/LP/E/uxdAlfNzZEWP6858QLA+QHM/SC8SaenmAiuheq+lkblsQE\nlbDPPOwBNLnKnj+l1WwaMKPYs7dc84B7b4CZcqTx/nmglUfRw6LX82XOr52N\nYt/Jf0aPbzGEKUGOT6FAOucyfnxMpL5z/kZlFjG8O9lAa1V5bPRQzj5i6+r3\nXnkDB4jMSUwjALBnPImHo9fZ+HiaQsNwfCaNeO+y6K0vK14Fdxf8Y0DOPzMc\ndHoIYmH8WMOkHU+LyMIQ2/7O4T0/cCLbAsYXQr49PMRwRKDPJ/e5eP04z9pF\nz/MZLV0ojERsJKqYgWyEUMrQepCgzSS2en/k8ki8gc5KW50PihBzxuyE7rhW\n7gmnJgNg35nXIHWbGOBJ2n+75oCegEXVXGfRrLP+oIcd3X8UwfKeAWhvOm78\nL3Qx\r\n=Kzx7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.3_1532329327934_0.17430645600241546", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1e865551aae8167ad5597736f6eadce3bb4fe169", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.4.tgz", "fileCount": 3, "integrity": "sha512-FCFmYQwbGzR4WgamMFGo+JasZIcVRcXldEmwH3ByMffPBe+ow6B9b1v2MPt5bgNM5QmBfwysFvQ9nCHvdk65gA==", "signatures": [{"sig": "MEYCIQC3gjzAqSkqEKqr63gxTdzoLAQVr6+zqKDBqzKWNyhzTgIhAJvx1yR12jbwUcwmTjyPj2yIvwCu+tNaIn0940D7H6cc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs7oCRA9TVsSAnZWagAA/nAP/0GgoRihCIvv3qIIVmC+\naPfsHpKZvfkoe+Y4R+yJ2plwZmKMOmhWfN+KXcqedO7dDv7TZVMB5I5p/3oz\nVPlqdJVdQvgRFNViECgeOf35iTQ5UxupNsj/C4eyU7Pz7ynp7ximJxJ4fx/0\nuINgzIi+uG+AUA+Alvpf6Jli0CkMt2C0Evp2NkmoNr4WkZgGHo9R7vNsS6sa\nF6cFgMCmMkhvBwRZ3gpCMJ/ToXI3YNQUPbp3X5jC80+lV4OkAcDJwsN12I7g\n/w7d3GmXttrW3hIrtONthYl5972Ph1nsiqw6y6jjndbuoTjQQFgXDiPkxouG\nFF8geLlItSWNbLCG+3RT9k5XlmhVWEHGhP/s+R/7Q7+P5JhqUfW+FNkPuOyc\nQn8TGlyvJlVkYTrlfWUezYdSKBQBubOrSSg27u6TrOmvqScxij/RPd17F3K2\nd3l6czmadCjKvqieiMoGBcIArENZZEN3aa6TLjQ43yBK4sGFg9GbFJVY/RWS\nWKtTSU9k4N+F1Aygq2M5vvB6IuZaWrU+WvWCGBwMfnZimSGUuQp+eJwBeKdy\nLTKaP7EuB+p8rBVt/CGSLWkX804KOocd1GaUPa8fkuSOXZIrOVhMMsZW0Ib0\nsMCReLGT0pGRyTiu7cskf7f6YRw/AAIRl2ujqQBif0obCD3keuOC6LcoPyFy\nF7Pa\r\n=yksD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.4_1532415720441_0.6919775202843788", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6e01bccf462ebf9b96a3def27263666831d4e461", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.5.tgz", "fileCount": 3, "integrity": "sha512-nhv6diC6hOGCLgUX28Dtvy4gWjDkpsUDwBy/z0VX5mfpa2O7HoM5k8I6FV1V5NgcwSuHNnKUvxzajIiSBXaOhQ==", "signatures": [{"sig": "MEUCIQCiuAMg9Udoekr6nI5qeAJxGiaw/c7v+lczjdUgneqO/wIgMFhVa/6ZOeyy1Nbd9XgbKk31WCFdisDJ8O3v9OVrfAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdY/gCRA9TVsSAnZWagAAWn8P/36nu5PeH5wC/MHw17rh\nt0pzCUo7bi9Id3GW2nLH2WNLpx+/KDcKDkg+hrxHdHoYdPP3O1zd5ox6Dfhp\nMA1OWPVT4Iehfg2ELeZbNV6uuh0WwG9qWkp6muKZ862uW0rj7TZT12eGyOoK\nhpQ0L80L3vPXKlOXa+K7XE4H9eFo+r2qqVAmZAlYf5l5Rqj/sUeKptb6P8KJ\n50mhGEO/LP5rfUdLQCbG9RL7eIZBg4KGg5xOupBGWfyDOXwOOM7enajdyJo7\nEXRND+2A+MjtOdw+CeOXv8Ax6JRodqRj2vWW5YrxTLaDu61szbAmSHRgYPYq\nwrLmM6gbqYa/23SggDroNEieolnVJO8t0fHmegO/uUkJERpDg2He8SVVS3a6\n8xHMYcfJzxcQ5rFWANhGB+IdC1/Aez7iJ/CM088rQBmovOiegGC37qGEfNxC\nHYxA60U+URTqC3O5utt5uHH6N2w4+tTR2NqcT76FsIYMFF6rUtLewWNi1pD7\nAj9yRjIT3bvmlzSeO2ag6tsFmkLdGuJV1ph4pC5BbDpwDGjtdlKvNuL1VIEy\n5bBLOAsRnl1ZGtXzIiC2lk27DBmhsiRXgAyEmQDZlvUsm2uxhHqRnqx7Yki/\nCzu8lZBNuQ6EtfxaHG5Czz/0E4zkcwR1Mgy2JcdrtFTweKBny+Oza139Ytr9\nd5w6\r\n=wRtR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.5_1534431199358_0.7280978588832405", "host": "s3://npm-registry-packages"}}, "1.7.6": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "99b7e30e66f550a2638299a109dda84a622070ef", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.6.tgz", "fileCount": 3, "integrity": "sha512-SCzhcQWHXfrfMSKcj8zHg1/kL9kb3aa5TN4plc/EREOs5Xop0ci5bdVBApbk2yfVi8aL+Ly4Qpp3/TRAUInjrg==", "signatures": [{"sig": "MEQCIDTNyZxj17//pRfEwjlGFIIuoBMpDp68PG/ihWHqSgpSAiAyRym7Y66GomySJBgmDOC6l1L3rOAqgayRWBPv/UBXAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblnzjCRA9TVsSAnZWagAAwHoP/1rSFxgWcYSAnwelsO7s\np9zRnJK+9ZrFeYgMRulO9GonoY68XT12tKnmbgkYXOUScmv/EQCvxPXgzuKU\nSdINBGkw8zJkZqN0QS/l6ZYIUCtbX1hcTGbtizPfpGupN4g2tRgBwEGaMTmw\nUmqtj8q2gAUxWmag1loQXFeKPnL0M1ttPC+me69RWgTAcrLPJcz0IMWSCUSO\nt/DPjOEa/rrG2BKtPHO7eF8+vO8jWnjo71cbtfVSS5MbjK9BHmFjciTwoI6A\nhMAQ3ml1rdO1xas+1ox+12bvYcD+OPRIDiiV2Fxk8Sqq2fhzw1Z/noyBfKpU\n/w/ghvXrn1XTY9kG7dgzd366SuwRpwrrJpS98tyYQXlJW9sSmIkiXhFZ7FIg\n/oyIPbIIzQkfNGzBYdu3iWb8eM5BqTJZX6nz8o+TFVa0V/gEfWuVwqs98bAN\nw9tZa+WIf5Y3mhL9i4SFptMVUNg3VL6sX1NwUvUbh7/4JW7FxjwJ4jftylPP\nnmZJqRGVWxV1uCTxYVAysJlv6qbwY9ED8L303rLY7ChD0dB6Xk5VJ5fvx5ZD\nDRxpCwLocShMmM1GqD3uAJEwExeI6YCf22UUE/SvUSR7BA+QCh6fiPZtFu/m\nyGlQo7b9O8wTGv2K6Eui7OT8e0XRPTKsgmt1N7BCvItH93FBb8lNo55P6RsE\nRwBD\r\n=PtQ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.6_1536589026722_0.15103382836652202", "host": "s3://npm-registry-packages"}}, "1.7.7": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ac3fe3310530155ca9eb365638d0e1e0603e3834", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.7.tgz", "fileCount": 3, "integrity": "sha512-Cd5fPqoMVuyjRuRkEvgClHR1M3IjrUmTpbZQ5qPN0ZSWzTxuQEPoVd75DeIUkZ7jnvPf5Gq8OoYey0C8T6rj/w==", "signatures": [{"sig": "MEUCIF46fjys/Wake1fBxjp+RnFTB2LbtzNOc/8TC0aTRNCCAiEAtffW+9YYjzKm/maj2rcOalVQ+nOYLYdOmAhOCKe5wE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeD1CRA9TVsSAnZWagAANaYP/23rDrljaoBLa/sZC/ze\n2gzePVLdsfukU7T43NsAbKaxCBmKYGof0/YPlMPdlFFQh+NQYpkdy8sgEWEl\nW4wLqgTRFTOMJSnJdcX0xBflm5UWh63f4WinwD919Nizs89evVemXP8+BXyh\ncqwEaE2fHsL5sadf8exMqPrpMn+9tMPVPwpTV+Y4+XMYzS2xl6XxTqqAmpSV\nz90lXXs8Xk9lVVtk58059FW0fkoCIpYQrar64aeE+vmqc2raY54VmxSTwnMv\nnFcPDzifFLKt0FWOWSDoZ5DzyFhmFfDzPV7A4To0WcEvoBbwMgLgsjFdxpz2\n6TCL9D6aMvDGS1NF/0Uq6606BzQfFltZys16j+0vzj6mkay4gNQA9KcNEmoC\npkOUNmhnWVFBy/zzB02DhZ21Syr1Mcquo83VDXQh1sD6cyd+6v2WAlp4QeGp\nLwyLFO7ZS7rco1tgNu5765coik/j10NgDFGoYDMNDN2eBL7/rdc8XDVD0E0X\n2o/MML7ZHuJ5FSfoq3Eq/U1Xivi1/T8oVM1TrifjExyJwsKBLWgMXqWUb42P\nkpIJGcwvtdc6nDV2vC+yy4WEO13grA9dKXkJJ42us5v+g0A0+YoZJ5IWsIwS\nrq162xaSQfPf9/Tl7BuoeEgmAXNEt0nIi5O9Kx8U7XYq9CZZwLKN/xSM1tMM\n9ula\r\n=mVnX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "Common API errors", "directories": {}, "_nodeVersion": "9.3.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.7_1537335540708_0.9800959195777108", "host": "s3://npm-registry-packages"}}, "1.7.8": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a2b49c11f615e736f815ec927f035dcfa690d572", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.8.tgz", "fileCount": 4, "integrity": "sha512-xUwxDXsd1dUKArJEP5wWM5zxgCSwZApSOJyP1XO7M8rNUChUDblcLQ4FpzTpWG2YeylMwMl1MlP5Ztryiz1x4g==", "signatures": [{"sig": "MEUCIQCIbVi6WqRoNXN8w5G6PMdJ5u+9T158QDrb9mERgOUIrAIgPYzOebb9D9Xw/aA+dA2ZDn0vmaoj7YVyr79tBcBawC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ+sCRA9TVsSAnZWagAAXdsP/idmwnf4wd5f6sZiRY5A\nAFPErJlvDeIReHyldNl2k2EePQ+TUpKok3qvT/NATvcM1pjcFRK6o9xi3S3e\nw/RlM2FM7LO14v2qTWJsxMwoCbEZrKeZFqfbIE/sZVIzF3flyKtu8p7LzTYx\nf32zAU513M4poakx3oaSx0ZUXrfTynH3PhXXQDlFH7dxdvbqQ/nwAsvXIXRR\nFnannUE2o9PHQUN5ERdOzUmwD/tEw+idNDL4WeFb4Ycak4JIMkUEJho4oni6\n3b//2vEw+jSRppHdBFN4Z+3MCRslmQFu1zT3m1V9XMFnJ62PZcDlLoyA7lto\n32jggNxqgQO8prHr7ojwhkEvHmyrNSo2+J2hIH2YCUJslL1+ualqeZtcp49N\n5f2IA59EjnU1RIB2a0JZ9IlBDUQsJrTLLzdjqWw6HaWOq/hgZRUPu1xQklXU\nQpfo38w27px1fSwTG/eGvgbo0IAmuYy5MvZ/5s5Euk6AApK+0bzKWXpAZRJR\nXmqTn49/OKHlMK1F2i13KixVwyort35PZOJEftpy5HbKq2GjDcBo01AuU+OD\nvPE16LaS34LW0kfX+hy0UskEf4uvDf85BtYVG1HJR79MMKhKl79LamLhBCln\nu2RAJz9XmHSyQCvtIYcX9IXQKIc28gp8Ew3hdQmPyzBCzrPlwZILoitKZILo\ngaFu\r\n=Muyt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "43b83b600939b19c48c3c27a1733592c493c4386", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.8_1537515435744_0.15057556800809624", "host": "s3://npm-registry-packages"}}, "1.7.9": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a5d7739ba33a0bb1c4e6fb3618cec88544dfe155", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.9.tgz", "fileCount": 4, "integrity": "sha512-asYEnclGACX0OuN5xBKNvSsTRUPH86fDEWaBMcdXgLYdExWBLIgxSemlwkcuA5UsS389Ah8DsA7gHvdsXSHdBg==", "signatures": [{"sig": "MEQCIHx20N8ILEh8E7e75OIGNX0nxigr2whfBIfpYKIL/1MkAiBtrXUV1ikxQIRS64jj6S12dBJViiuD2Cs/OmprQtqYCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLf6CRA9TVsSAnZWagAAmb0P/0ZELJbQvMRp1UQ1KJ+h\nJSdQgV4J9REc5o75w1nj1zR9Cyv1rtkig8/RJeznJyBYA3C2stTyqBjK4M72\n+mzEddQS2TvxVW7hCmvtCqFAhyXKi/0qJ9b2E7nNThE8cD4NZps0IMgLtpnH\nQyX8/b+3NqcunXq/7QWXhK4fjMdsQulQY7fyWJ+d8+4bgQ4qBiMI7pcSK8od\nIr9Y48wkHH5dq1Vn2zpt0S6KMjGPPP17cdPhzF6pO6jVCee4OOqobijGPsRB\nDvHJ87I4obRp84/WrYRUheBGu/E9Sfvm7fLZqwPN9y2kW1YVaiFlbtqcrtbA\nyQCY1R9MeTdeQ0j1A08mLk++u2q00YW6f3VDiP3ueKpPHIXASzzZ6lBJ0Qr1\nYjm/Ik45SVE7rN4Z/qfa3CrX0wKqDZtApj2rFSm9fPexV9ujQngLGDtI8fWh\nRWoSHCzUDvZ2EmlkxQA8L7T89uQQW55Cas7Nhs2dUSFWH3/EHXgWJeAHr0Tw\nqRU+SgsmSfXKN4L9356dQ/L30OOHBeqNIOhRPH3KisaBHyKNFyef0pqGwB9y\nOb1GBwfPIQuNtIjJyjcNxbHozImo5rVJhUTfKBRkmd43lON800Md0T797PLQ\nA9jcIZ6H89IpqiOmY8IGzIe2Hq3/9V10oUT2TnLXgr9VSker6MIrRZKiHieW\nDBzC\r\n=k66q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6c5bd6e21d734967e12bb7b7aaa38c80697b3b68", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.9_1539880953763_0.21222963450402155", "host": "s3://npm-registry-packages"}}, "1.7.10": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bfcb3bbe59775357475790a2ad7b289f09b2f198", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.10.tgz", "fileCount": 4, "integrity": "sha512-DoYRlPWtuw3yd5BOr9XhtrmB6X1enYF0/54yNvQWGXZEPDF5PJVNI7zQ7gkcKfTESzp8bIBWailaFXEK/jjCsw==", "signatures": [{"sig": "MEUCIQCget/qllDwy/YMf6eb3gKD9wdEOFuRKHB9LNJC46o4oQIgAMkwGg5RkeoVOMihCWBefc7KUM1Q8bNl7aJjg2wdfLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzs44CRA9TVsSAnZWagAAES4QAIAHRD3dKysCzXjkLt8M\nlFWPa6uqhDIVqojEZT2rpBcWR8ZsFxdGbdeiZfkqVbKbwb2AYn2gXaD+xihp\njoAinVRNe7D+90D8C7mwiXq95bjhRiHNXxoV2Y5fhlKvchMM5Me453pNjZKP\nzvJ07Cu6DN1v5M1ThTSzw0V3TXN2j3GRU9ynuzmkOYZqsFWKZqxsekSi2S7J\nF27xNUFY5PrZ6L5g6hf9ECQSkS2vkBrZ2miGhKrmbNb1EojjOJ0b5GR0GY1p\nYVD3YQgmfh+xhrZUI5Oc+jvhKqjW9GSEaEOMLO3JAXVodkA8u5DWH3l3aiTq\nCux+r9fqg5f5czotJ5f6KJBgcRLJllJIQ3axo+nm4E/p9AvNxF1ERoltbZAv\n/XjDw9ky4bHEGmecKznaqq7KNE8+R4HsnWpX3vSEqtqYf6kMo6D6NaAi7gk2\nIt0osd2KiaU5I3FPJF+HjiBHGsVsLRpI6Cdw53MNRmq8Po6U32Qnb4jCSjkl\ntq6pLzAI2YBjtvzaThAAh+ZlTX5EIjw11iFLwbjyuiJkL6qES5xaLqCf3DCv\nIUHL7Bh4NcBdC2iqDdVz8s3IUyqzDJhKVhdX7jR16YzCU0tNYRwNo3rI7Tej\nIlj6CAETN6+ffzujimRly3nDNw3E3mfkVYhAFSSCuXDS/tUiGLWRfZexgBzA\n/VbT\r\n=eGAw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f558c6c047187f24a2200ab04104f173de226794", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.10_1540279863921_0.08911755126565302", "host": "s3://npm-registry-packages"}}, "1.7.11": {"name": "@webassemblyjs/helper-api-error", "version": "1.7.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.7.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c7b6bb8105f84039511a2b39ce494f193818a32a", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.7.11.tgz", "fileCount": 4, "integrity": "sha512-7r1qXLmiglC+wPNkGuXCvkmalyEstKVwcueZRP2GNC2PAvxbLYwLLPr14rcdJaE4UtHxQKfFkuDFuv91ipqvXg==", "signatures": [{"sig": "MEYCIQCNl81DtNogN/aqsGy6aZjb33PFNDLF12XHZ2FqIlm7xgIhANuO9NvWYKWuXYoYObdA49PQRMjYHikeS0LtVKko7+gH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxKCRA9TVsSAnZWagAA5E4P/iLccjMpyh8ltybaRA3v\niJV2iN7IUxbXLc4pyiO5WCJBSVVKVluHQjt/YpzIl9W9cyRfG7pSRHXbXvgK\n4Bl1w+zcoAUgeVwQhZiCpBwGTzUjmz40CVPLur8fOaMRqj/TFWQOhzJ0GF2x\nSTijFMlrszdaueH5aTn9dPIP5kujXglInpJs9UNa0EY2HrHVQtL1gRzZyCe4\nmGtE5cxxV4DUcdzlHWlLfvyP3C5cjK0Dr7jnGtm2RxmXPmh+pRySzCMp2a0b\nOdgP0JDS8kRMyCC/UwdZE45Ziin5JBmYtPp90cJL4iImylMPvk+/um4ds4FT\nG6GDQ8tu5i3XEDpgCajka8DGueB+LucI8awK+LENDqGpnJG4KQ7dzaLOKjFB\nRp0YYcgiVt8VMNKy0i0yGWyhGYcSHqg7nq4O4ty2yNsxoJV/Qx6HIVlSAWAa\ngKs7kMbTuBSN5xj2WXgb10VcB3KrTJE8s7+nRoayGWpbG6YlQlYb+V+0Jsz1\nnY/b8c3PozGSVZyOvaw+kmUk7YxolVkCBVltg1pOGvKyVMT1xZHtBRMIwLRG\n3KCsGDncA/fFIsjGuhMSpiz/Wrp0EiuGu0moqGpPMbiLcPh6ChGzw3NP1Kax\nNL6YlycRQ5NaMu0Vo56IK6GqH6ktOoaIXDtFhGBJLeQQvXOnpYmnSVm2A6dh\nSQ9Q\r\n=p0OD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.7.11_1540922441961_0.023420438338639604", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.8.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "15d78ba7a1706ba0136dad1c88e0e2e5c6488715", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.0.tgz", "fileCount": 4, "integrity": "sha512-uze8iIW5ljV2VhEGJwhfRxDEPl7s+iWFUU0r8yKJ9uunen1ACtKUyHIxbgqPkvWSSQVHp7buPtUPuZ1jZ44kmQ==", "signatures": [{"sig": "MEUCICx2ueRHg1OsSllcMMnztDGayTIfA6apnWQphakgj9ouAiEAxsvGPnxA/dfxMh8kdk/q5Sv2NjEOb/ZksWtYYLPKP5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2YWCRA9TVsSAnZWagAA8AcP/Au4cZH7/4xaj6pN6UIJ\nN5rSm19h4jgJGiWe/IF/tTfEFn01GFCHHy8Apup1Lj6gFNMUs5JIQZ2OqAXV\nDmjcJCzo9lXxdzXzMNRn3V1cnxUxHg6AgMCGzN3ki3mu2LSUz+o0mFyBj3ju\nh9979fiTDb/7osXgNZYOHUAzSr/RvYhyQY2UF5KqY4yrrH9gxWl0RstspkZp\n1XfnvEPg8zwuhHWIxrmE6trvvtwZUme57sbobXyEEnq/cO6HJimqicmagnl/\no/6t0OU/scCQmWkFLa1ohJARzGa8obGS7IQuzvj9WDj/0drQzBvu+J/M8f1y\nyc9+Bvy+vcMZQ69cakCKnD9XudO1IOwqbnV70Uk/Cz8Ypvbl2sFKDQoSD1Dl\n+gGklmoeSsFaTqsnx/v6E+SO9kKgwHpI75Ps7guuwfL++OaHHz4EzWdopitl\nPLgGAxaE075HpUTzP0vWFyMvQXmlQ8/0DY7WuVdzgdiDmDquRAz4WMzZ28jW\nwyWiL8UkraEECy002g5n/yK3A7MkbrHVJMx58qreo0Pngg2p+pF13Zjf8bPZ\nFra8Li0kdpWJXY0O+837UWdwzGcDt9PUnp90xku4GyVnvjkrBawoW1Z646Um\nWDKvJK2icj4PmZhubAMRV5tX7DGmMDLXZvwuYC8j9UKC7ZPNhSnN88akvcmk\nBnW0\r\n=6GqN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "8b2d1afa793ea81f20ec63416134c201e39694eb", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.8.0_1544513045345_0.23904837242940435", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.8.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.8.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "78b4e8f9f21cd11e5e499557f0a28414be50e766", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.1.tgz", "fileCount": 4, "integrity": "sha512-79RidFwQOl8vG+Wv1uQWfCw4JQO5XR8iQcNGKLum3oPsSG8jkuEK5ILT6NxT3MNOa+xwSd3d+YqVFB1V0/W7/w==", "signatures": [{"sig": "MEYCIQD7xlMVatayLP9nsVZf2/vw6RqcLV9Q588jnNHtDX3cHwIhAKVcsanYEeJ+eguLs8Vx1z5+zOiD1QUrTSXsTgBW3oNt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZfzCRA9TVsSAnZWagAA7OkP/2UBvm7OWQrlCtprLvBq\nQhHZ7WzsaTZQLXhhlnUCuQirAcL5A//DPRZzgSDVhYVsXVZBMV51uhkMdafa\niENZADi3DLDRrHKThsAgNi32n2OOBLQj7HTIYGBtT2sOC85f9x8n7zRYOueD\nVZ/UjIfdVNj/xXN49txam6xtDtGYunVLJvkEp8uGi7MxaCguAAuaobph6xSE\n1EFeoI5WvUY6CtPYC+hvkphbTPWue7WsKpsHZMxEung6BTjt7ECE9UvHs9cB\nEk5mHGNe22gZCRnIc8H48qCPbu7/IGD9K5ys9BKqZmbs4+mi6q016Vg239jx\n7lD/XyNGpSkMKr19q77ZzP/wjcY/J1LsfB5onDT6H2rC3M0mDDLdspr/sOTE\nO0LIv52CvXb71w1tUHL1UbX2F9GTyyM1uPKQaXgpDqQ2iJ8qqv+Mh2eCaFXn\nvpRts3pRVKWHDtmQsX0pM5MzPZZ96yCdnwy3yVl3qVcFqUlB24vqifUv1OCB\nGd2FeHXSBfNKg1ZWWBloblXsmZR8agPoeWDRT2it8O4uCmmUDW8xsjv1i5mO\ndvQaeLm/qV8BCbuWplceXO/dw8+4+ktm4SpZPf6sg0IF2k/Tv1vCHhuiEIhi\n2cr0ImvToaacrBQyBV0ZXcvQVJRCMSe+ChWAD5/bN3Q95Y6Ur69YZM2HZnfZ\nxFYa\r\n=goW5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "a2f42245e9b597e3541e0f697253449d60fc4d79", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.8.1_1547540466233_0.3439185389637962", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "@webassemblyjs/helper-api-error", "version": "1.8.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.8.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e4c5325b264f08513bcf0e48cf3c0a3ea690b508", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.2.tgz", "fileCount": 4, "integrity": "sha512-TJBDJPXO9DSC4qf5FZT0VFlTdJSm4DKxzcoyWwVike1aQQQEbCk167MJxYLi0SuHeOtULLtDDSZL7yDL3XXMKA==", "signatures": [{"sig": "MEQCIFM+4bMe1av6sPl/UjY1+I9R4htycurR4ufyJ0XlXTZ4AiBqypP0jOOuyAKnA6jwx3Q2Lh49RT5A2z9EUt6dHLNwag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWGCRA9TVsSAnZWagAA4/kP/0TsJpaqjpd/iIPCgs8q\nq5hsOs7QD4S1+hDJy4fugdSnmOO+uTXS56Y1+GDYRda2p9iVXlC73lAdiYBU\n3PrL14UXcZ4PWBSvjiSNZdnv+clks1dMEOKsHq+LypqWiBA3YsLPw2oUYj8C\nuSuLVuQwF0W213QqjwCUCBRnCwBsA5LgrYatij9+lsYF3njYj9Ltcrv1QORt\nxpim1x/I2dCGfILZ86NllHiZr98w0FZq59CN5ktJz718id30E8/BGx93egkg\nqMXY18CuTxlNWeZQfSd7ynreyQwVXFaNgOocBwM5iY+Ch/FGjTIu+vRyuhbq\nLoz6eqofP5VjvPPmFVcHDHTWtfgkISQh4IVUo8b5a6p+XVSaBL7BbEFVOKop\nSEPdgMgXaiTTPHKhJ4pWrNC47bKce0NlrFXEjVT9jMPK+ZqyXQlAfGX3JyCK\nghTLyKC3WMHGR+XFBTxyN3WJHqcKG87Ljq/+sQAlyYQjtO3KqhJOjgUaLERx\nZbfcam+KKwmoum3PP20GiwyW6rY4xDEs/QcduMqIc1vFtrqxGGZyGk5UIAXl\nGUdILbsFoc7ztBwxkJtEAaix1SzYHgFiNE/zvQ9g2iMXWBytcz/AncxkfYzl\nxgiVMGe9Hx84MsWCQ4++gQ0yFcjY4yFZKUTpAfQnGakfIjVkP8PMW4jU/Dnr\nVpvC\r\n=yUtm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "02af462b507aa7a24f5d3201178434b181bcdabb", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.8.2_1550161285583_0.7478443017567846", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "@webassemblyjs/helper-api-error", "version": "1.8.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.8.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3b708f6926accd64dcbaa7ba5b63db5660ff4f66", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.3.tgz", "fileCount": 4, "integrity": "sha512-BmWEynI4FnZbjk8CaYZXwcv9a6gIiu+rllRRouQUo73hglanXD3AGFJE7Q4JZCoVE0p5/jeX6kf5eKa3D4JxwQ==", "signatures": [{"sig": "MEUCIQCKKV/3ftCvrDSMoVGLWv33s5knXeVI3OoODvgR69afUAIgNdvf2YsDWJo37cd7Nzc5BmXKb6RQyuEM67JnLIAj/ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam6qCRA9TVsSAnZWagAA6RAP/0Yk4tHd6KgL2D7DJi5/\n0zr+0Cd03ob5zHsCyge1qrJaeqpMF4qbkOGqdXEL24dAYkHYi5WJu8lWb8Ww\nbtcoumMFuwrXkKoZYwCiz4eOBskyjZ8ZsKeAN6LUUuZWNBnSAhxq0kmir+vD\nLipuBGdhlAEzgKzYT2V6ukPsF0xNTDjFI0agZcq/PkhT+/DCwI7eqcTNgRzn\netfns0vL3UVcfhXEFCzFmZEFxwZuOFj9WZSEOTWHil7gTDX2lOSHOD0Y1Dn0\n9lnAwRI7JY2ZBgw8xoAPtFcovB+uSLxVjdyVNS2VY2BDC+k378wcwQMvLh6j\nYo2k+WDb0elD0s8TrpJJXIwJu0OlWpZwyIlojMy4KN/942x+ythXoeuZ1OHd\n2NKYBAmdze4zEDHUUBrbTp0wotU4yde7wf1NeJX074XjNWnOYxj+jjtE75wS\nqEfpT5P/UijfXg1S6x3Rj/x/MaAUm8Y88XjUjMU5zyXLMbUb9Py5YptTA10L\nbniJPwQILUB7FJfS9u7cVYAj2977q89ihdfhQJcH1Px9saNYSS3n35PCXQbl\n3TS4edixOW7Rsxjv0kIDdv+BmbCGKs0ZadKbBhBbD4O2+2KC1uc/MnsSKsvK\n2esUUbOaW/Kft9Ny2Nz0brHT1EgCd+J5UDsCEUud12RrGnodGYyDfwfx5z4X\nC2Ee\r\n=AIjM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "e482c7ec291d61fc46e42c93d3b8ec7517b629e1", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.8.3_1550479017859_0.0400559920465835", "host": "s3://npm-registry-packages"}}, "1.8.4": {"name": "@webassemblyjs/helper-api-error", "version": "1.8.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.8.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "222f925bb05b1c3ca6c85713d22c671b47c34a3d", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.4.tgz", "fileCount": 4, "integrity": "sha512-nxl3QSDOxa3J0ZHrswPvHiCejH0XtfNmfEeqxz4XL6foe/yReVvDn9LseniDlURtxWr5KhNh2CRJ1NnNvogbIA==", "signatures": [{"sig": "MEUCIGL2wG4+GaghKmBVVQN2hdvO+3nTrZCDafy4DqSxZn46AiEAxW3cEe5yZndjwUOcAhuecVZbt0TQRs9LqLn53W+G0kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDxiCRA9TVsSAnZWagAANnUP/RsGmzK9hBiCR3ERxSUI\ngKzpQhNWznUESizXPyg8oCmNvPrTtywdzoHfEblnO6yVtjr7m+3a40/BgCD0\nqnwruPlyejEwIdKhEKoaazXbT4obVFNg3z9vC+0us0qFKUscGZafoycO5A+V\nR+ViT5AlawN0P4ulgy67RdD0oLpPw2SSUn+6xnBh9WdN2p0P5KRVCx4tlQT9\nTv4R5xq/i0fLGoIaT5mKA/2t8DBtajey7c1qHItldqdxfGW6kvpiS4RaYVLa\nmXij/D/LJyYNnZOxTKrktFLFAqFIZv3Xrv6/TETj9xllWGjm5X+xigPVhEqj\nWn6VRp/ioFJqnRWG4se5JjmPk0RkooihZmM27ltbSbOTT4L3FqnKSJ80TwbU\n2NYOxgoQGiRf9v/UBBzSpHzN38PX8MU0esd8nEarvo0knOK8mjfLdQEFvyVd\nnE+hnHejZZ5dEYibA8a8p+UsbEocLwBAHtQbazvemc3w4y0QrP4ifcNGq5g4\nzlxbItI7KsgGQ97IZjD29uq9ZBLbiClK7VNTML+/kRI38ogC3WjoPZvk+BPM\n1irN+owGGUPAgGlGFs/UARTS+cLs9a0PtF/C0estXGzdAVD1j2kFjf96Komj\nGtML/g18zeihxZ5NYSqR0kqGL/BSlSrYCWHjLXG+Zky0G0rXF/z5w1ptGnlT\nb32q\r\n=i1Uc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0154b989cc9b41c695724a361b3aa6fa19c5b032", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.8.4_1550597218137_0.12349279364263777", "host": "s3://npm-registry-packages"}}, "1.8.5": {"name": "@webassemblyjs/helper-api-error", "version": "1.8.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.8.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c49dad22f645227c5edb610bdb9697f1aab721f7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.8.5.tgz", "fileCount": 4, "integrity": "sha512-Za/tnzsvnqdaSPOUXHyKJ2XI7PDX64kWtURyGiJJZKVEdFOsdKUCPTNEVFZq3zJ2R0G5wc2PZ5gvdTRFgm81zA==", "signatures": [{"sig": "MEQCIBDz9AwjqSUZ5354DJ9mRkGJrMnuqBCgeUS7k4Ikb/yTAiAF+/30pSTwAWPtpvrQFabXZHdmPDES54bAD23Hvt7thQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnUfCRA9TVsSAnZWagAAXfcP/Aqv86hutQls66mnqSLy\nkmysdQ1z329yeNkfGPadU4YG7eNuJ0GL5Az2QPsRorzCGSZec2nIU/u2myCo\n08xMnciL8z/QN6KXyt4TIc9xMMtvVr+rR3phE6EYfEmJ1oACDHDL1C3suPXT\nb7UxZnS96fbuv8jqo95wUb6mHgeKzpoDP72WV0Z8z5D9bgDuEOMbskSVnC97\nayLt4+kuzwCSPF74nWdEIXTMRfJvw+g4B5baJufN9obogP2Lfk7TBKvKcLLJ\nNolhgY5wSqqdRHrZIB9pXvC7WlcFnsWtNRFeREZg84otTkfOUPF1vspqaOtc\n32e6tHMKqLNm07YCeFaLoXA9IrYdEOjZG98BS9CxoH5WUyMakxO0GrnmZQzt\nuJ/CCd4YBmQXGKkt2J3JFASGlR1MUw0DBx8gDDfShYHadnsOhKUlwgjNktYB\napiMB1fdOq6shE1dtLzJFUvdeoEtpEYieYRttgtmJqy4/5mcTiYJImwgADKV\nXs5GB4cNWeIN2FD/4t/ihhZTcEQz/yKllg13t5dAoZ9gF+Lk97dT0f5JumP1\nPoTQly6EbhQM3E4SXbOtFlDlbJmfT/LgMaZ7l0Nh2diG9PSu75poqGhZr/Fl\nqAr+O5jQ/UaPGb3g9tXiZMwztstvnE8YNznIuzTjHKo205iSZYkUkLPevqh9\noUHX\r\n=hI5E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "Common API errors", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.8.5_1551004958874_0.6142079913891874", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.9.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "203f676e333b96c9da2eeab3ccef33c45928b6a2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz", "fileCount": 4, "integrity": "sha512-NcMLjoFMXpsASZFxJ5h2HZRcEhDkvnNFOAKneP5RbKRzaWJN36NC4jqQHKwStIhGXu5mUWlUUk7ygdtrO8lbmw==", "signatures": [{"sig": "MEQCIEhf/qx1X3sizwN9IwkcmFzsDOvWM1U3oX4OpCqKX2PoAiBwdLXcrkd76rIr+OEOecmgO7thl17Ju/aXSgTxcIBk/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgeQCRA9TVsSAnZWagAADJ4P/R8KYlPNosXaD8q4zPmo\nbJf2Ja2I68NLicSD5ZYFzX5picm1thQyLhixPJhmPmW4tdZEQ3jCnap8E7np\nTYn/uwUAoXhEbwbsGTjpUOByPaK9/50ggR67v6L1nSqHxS2jEuDL16jZBhBA\nt2+VTEwu0bbMA/mv7wSqjF12dc7hcNczWEoLMnlPFn6k+Azm3nVGGsTE0L5J\nHVXNP2vKctRqcYpYIdzdNWe8FV/xJTQ4fneTSU3Kg5MUtHcHoDky6eJiYxDd\nGWpmPe7dJd/UVkWRu+9mCd3fcz1175rOvl/DvuIZqAOxErgcUT9D0FAxyH5F\nbU346Ed/nZqE8j8/1N6mK67yHrlM18Nw/goxU3G0C6RhdxKgY03oliIC7csd\nMai1VJIvyYoLtS+1DG3tytcfqMGuAa+5VXnFs/YplhrMQhokncGRq9Q+RPu2\nr+sMaf+EwOxI/jJbNNQbpaYnN91sczUNhFaiO57OgXPj0DlXyMvJYXqdEu3o\nHAYKlvYCNGS0L30eNAhXfCaMPATKVMWE2S+8ITCnY9m9a5Cg8hV05ITeZrPA\nb56DHh9dxJiZ4BWdyT5z5EhqysVAMfs24RN0Dee7RMj3L/duGlpZ8Di8kZgV\n8S1K4hqjGWbrQfxdHm7B2tPEBrWqarvMOn2+lBODMCkkDQSPJIzocmtODlBs\na6zx\r\n=ZdtC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.3.1+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "12.3.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.9.0_1580599184346_0.2879540430700225", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.9.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ad89015c4246cd7f5ed0556700237f8b9c2c752f", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.1.tgz", "fileCount": 4, "integrity": "sha512-y1lGmfm38djrScwpeL37rRR9f1D6sM8RhMpvM7CYLzOlHVboouZokXK/G88BpzW0NQBSvCCOnW5BFhten4FPfA==", "signatures": [{"sig": "MEUCIQDsmobzm9z2i0gbsBTvcjuZ8ejocTzLzGzkz7TaVbR39wIgR6TGDOnHWKEQ/iT03md7PJ4x/G+qvE56By+HJ/yb4fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNoWCRA9TVsSAnZWagAAy7kP/iaVzxaGJd9aFzf1gdY1\nRPOPm9HA5HrMs084qGYxS8q31LBIycGJUB2J0e0y7nf25SASlWkdk3wkelJ0\nTs6NqrV7SG+D9qGcvgfisjejTZoe2L0psZGr5T4mnhG+Sti/ly7W4418z3rN\nQ5Hm3GuND0a9Ay7Zf+kPCEdpb8Ua+618vwt81eHti57NTUtv9uywuo5Uwgug\nv93mY0BlYDhpem+RM2QdOBOJrMPfe/nUUx6V1qCiFBoGai22z9RyNjoS/ccp\nl0/P093fB6Alx98OgpCUPpB1ef+dnBrU7MdEy8T/LHgPGCNfntyC7Uop1UVR\nnXkeBxkIiXwu2vDdAZhzF1YEjM/oeH8F2Q6D/vqGMnnxedHYa+tzJDAwmXKh\nFMkcpo3E7T1DvJwFN4Sr5Xj1zYNly06EYMsVVOyCqc4Kewz4nQTQ+Gsheaav\nTqgwp/OLHk+pgvxLBKlpV8VQem5HyL/ir1ZYceIBBUnE87OhBPpsjQmBoRM/\nJ2zAFS2FwlqGsJ6A8YSbRHah9X6p6VEaGII7WqFqEP1Cf24wh5yek1Jj1XPe\n9Y53n5rN70ANbcLSgaMBckJG+itZ8DjydCKSbdgPxSaG3G0uS9wUxhQ5qw0g\nC/GH3c3GY3+KdmyAKF6oMjNGZQSNH+XAE20Qi9FRWh+4WVDW8G9GaCgLiKuL\nJpsE\r\n=F7Ou\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "697a5f63048049e9ecb3205d789c1e80eaadf478", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "12.2.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.9.1_1601755670549_0.757581364892939", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.10.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.10.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "28042d677a30535c2977f3e1bb493754275cf0b1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.10.0.tgz", "fileCount": 4, "integrity": "sha512-nFrptMHRJGqU3MUgOiMPG4NXIEIMVca1HaH/8Q1P/Sx2nEMUy3AToiOgJE8DgsQh2q3o2/mB4W0eUNGOutGH1A==", "signatures": [{"sig": "MEQCIFDKq5E+9FqmXtDSWRpqjVGxH3BjjEFYEaSYvfb3YJ7TAiB43wxCpJFMubAN2xiMyjxFCFm3VxH7ow3Ln2KDLdUE5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zQpCRA9TVsSAnZWagAAFicP/0craYYyUzhdK17203vc\nG6E6ymXgO9s0rZPpT9C6cDNaX0z+YICcQ7wrtmVlmXC3nt9noCFJPwlYb078\njKa+6+Qoh/RSIvIbrX7j1P5o+tJ5O69KmSEqw72qsXVVOpXneeB4n5BFXJGR\n8TB/KsdSINm8zOY0PmdjkkEfDl1x1T2bxO9H4dD0XpqnWeLA/8qTvoWY2vXn\nbKSPRBGuD1kQ6bICtYq+dxnHtSW+WKE3vSwwiHTJeeJT7PYA8g6sfPHh4qMg\nFTShq8ZPVUMQaitmZVsg8WZd5VK3WzVrQM1DwViVwB4Y0jQjWy09cotU9NCT\nSV6fAx+Qlbl9X7w6u0Y/UMwuRp6SzGyUqkw+PgWAQAuYRnihVOtcJzlKCIw1\nXPSw0Ky4u0iDKqTVMrL1eloPhzBsK7bWhXJfpBB4qoJ5VmGrzV/7GUOGJEsM\ncWrHKtljSRFV260dWY2vBKq/VBDn/nZExrZ4NrQ0tpab/Pj+1oUj9Jf0GvoF\ngubFho80obyyGkMER6doKzYiFkQD1Yy/AnXd+EzvmAdgSSWRXfZHoOY+D8Z7\nzCpHdsQrleqw+7w3wH4b5vXE9SG/W9/1lOXfkiFmKgmAE5fDW8+3CtcJnVlo\ntcHzITl0WLG6jH1m8vn5VKjBI7mfXjB9mbPpPBOG9SfDlsIMJpdX6oSkTNXl\nWG53\r\n=AUZp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "d00b899ece0242275e2475e75232ae18ddd03aeb", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "12.2.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.10.0_1610036265300_0.7111307426092452", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.10.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.10.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "aec6a25ee4bbb645e029c921beaadabfe97ac9b2", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.10.1.tgz", "fileCount": 4, "integrity": "sha512-MO/qA8I1gN9y3k+0KS8EFzlEJsrtXDTv6VZzsNt1Yibv21fROltpAKq+zQQawhGqxE/NpHWCILiNCtZbf3bE6A==", "signatures": [{"sig": "MEQCIFvuHBaPcLrSt/NIINB/HNiDan0DzJ1jhbmtWRQfHkkrAiBSuRBcNRAkhog12DO/lAkY0jZ1rltgDjZaL6esxi9S1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqRCRA9TVsSAnZWagAAdioP/islZ/818/wqlc/igTZj\nnoScYQbMhlqF+dqEQVTXIE7R6mMjp470VFofaBT/YQA8UgMtKvcNM58g5xa4\n85pZNm3ARFhECSuuympaX35mvMkCEyQZDNyEVg3iP17Xsv5WljCnU7TvMSOi\nXFU2nNmVBI/qqNWVcEi8QUJmuiS76FNHaniEvqSRl0VHfBoyeqQrWXLk3jfW\nTPcoeY4yiYJ1MjyqpF8/F63hCd9QTwXJRl03g+LAoL/NswGI0AgM/lt9Yr7O\nDHA1VLq1c4jjFYa3dTDZ6V/axOW+CxbO1v69xMMH2W2wD/HaGya8bN2zICAV\nMRQYq8es+rc85YsDf9TnoggCcNDFAKFQ0R8DrGWJ9Grz5nCxNk88+Th67ETT\nlx27mGXGGdbFvMuYy8oJNH9QmHtYwg7MAlDL/qvVhCNSid4skeQA3nr2jGtL\n136cgs9ZDAoFhluRqbwG0uSh+RJbNIsbQf0KnxUaNPsI1Kmyxf4k4s5pI3NC\noJfVYYUsW17rw7Q7ON4weDJnYKNhwJCbmjFM4mXAsj8xuad3LB12bKuicO8k\nxuOT7oKXy8nWTArZ1mqOUKxQb1YI3T+Pk/epoKO2mOkJnthuqBIZnW05eF13\nSMur4JfAi3U1PppgIW8rqxsbB0LD2U12u22QQG7z7VgAyJbe8oWQG/Zr5tiA\nLHXN\r\n=KQ2P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f723f2cdd9bfccb5e199962dd8c5c09bdb0faca4", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "12.2.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.10.1_1610037905010_0.7561720269392891", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.11.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.11.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "aaea8fb3b923f4aaa9b512ff541b013ffb68d2d4", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.0.tgz", "fileCount": 4, "integrity": "sha512-baT/va95eXiXb2QflSx95QGT5ClzWpGaa8L7JnJbgzoYeaA27FCvuBXU758l+KXWRndEmUXjP0Q5fibhavIn8w==", "signatures": [{"sig": "MEUCIHahbbVb+RNU4HjkbGwnfk5bNDow6LiqN/BoS5jB3xqIAiEAg3XqkIQBufVCkN06Vp/QuGYE86Ko0IwEXI/cxft02s0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907PCRA9TVsSAnZWagAA5BEQAIJU5AJrC02UGVi0tDnD\nwUUCwCMXcBjyuYY4YWRRYBA1k4mRF0KEZ5PINcrcPZflk+OavE+OTo1JTab1\nCkqAXnfc9c4+M26Q7COEjV1HzPWbw1Gky9U+RtfMONoKwGMX1OjrYxnp/Pfr\nG3EA7L1FM0+ux7u5X+KzQUnutTGzaQyUQkzpAp3X5qf2v1JHz9Ag0Jcv3amC\nF4DIren86xqQ0CDoAykt25EnCBSHaK5YOPInnpRV3OESeF1UF/RQ5phjJEn0\nx5TUCLOVOSMNC6jmffucWfQ97c/eqpS46mz3YtRQ+pDBWFT/LtjVsl387YOG\nD1R9kjuCyJL7MpnFaXXLmkStNgr+8y038frkhs52Vlx3z24fMdLSYaB0E7w1\n4rhHFyIO3mEXtVP776qicRjGathv3w+erl+Q9FUKsp65yiDNs8S1TqYYvFzq\ny+CyTJjdwwXvi+moLap7iNErz/FS5U5z2evOIRN1yjq7niptWmt8KXROXdrb\nqAXg/PuZEQnB8OmF5vr8NtwsjUB551bIKyoTJ8tdd9FwRm3aDAnygATss6bw\nFsJbZLYCIjaIHaL0EdruGvf0u4XI3Gxnr5luyUYnc8qyhCmN1XpmEBxIjTI8\no/Av2gZV9VSu3dQtoj1xSF/JCruaal7qBy+5vcBsddITKipE8nyOJ2vblmme\n5uIv\r\n=Lg5x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "2646d3b7d79bba66c4a5930c52ae99a30a9767db", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "12.2.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.11.0_1610043086685_0.5481480761303781", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.11.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.11.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1a63192d8788e5c012800ba6a7a46c705288fd16", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.1.tgz", "fileCount": 4, "integrity": "sha512-RlhS8CBCXfRUR/cwo2ho9bkheSXG0+NwooXcc3PAILALf2QLdFyj7KGsKRbVc95hZnhnERon4kW/D3SZpp6Tcg==", "signatures": [{"sig": "MEUCIHj47NoBacA5bPfpeuuNbtCBnfg+B77dKba+8gYuQ6YpAiEAv3Y5uyQC0gbveKS9RiZ7NUWRIdnNXlkFTFLs7qKiufk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sErCRA9TVsSAnZWagAAWAkQAIYVQIiYR7rYYDKYlx4y\n39BhjAysl3Rzp0yg/eA8zvXTgua3ZJKkWZaNewOXdF65tUFD3lnS8M4BAnEI\nDJAoQfs3a1olQ7w3NBklXUZqBEfdwAsNrrVOv5lO8XLUYxRpoDfmSjg/4WFi\npkEn0AeTmtkZPzSX3MjvaL+Dd5/E3ZIbKQVZ0D3H+Voz9gePXlFviw9b542c\n4/XwfWNokc6/G9C/YsQpB4892fKPoLPzMdSP9CmcBpyJw4eaXHgsYaK9LuNG\n11QcbSxsHIUhS5I/w7xcCJK2t8xCZTuP3rxp+9Q7nvijP1H0NYDpkoj0ALaU\njiRuGsKdEo/NOgjgBMry1Ho9pRCu27xvqXuI6pgZIZYZcIZvE8basikO/NgZ\ne/b6B/PmhgfcN4mYFHZHO+LwVPsoiGaIIPyw3aDRNTcyT7FWDAFW+zWVrMNR\nlZayoZ9Wg2+JyEYBokQiHYPeHNkwzOBtQjjoTI7qjKTXTpdtFxXTQC6KRhcc\nZxgRMDmP8bV38iLcwXkrpLKWV5HuumswJZxFZsQGWqz5YXv8nqmV9aW6L2G6\nV6FCG7px6xC6HmWKhmOrvqP03ewpOe9rOzVDRmYdenQLuvah24E1jqFU3IvQ\n4/ewOHuAuAWj47/mbgl/jp19/LEnTfl1NWbBvJUIohm3vLTf9tLDaXeycUMD\n28mP\r\n=fACH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "12.2.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.11.1_1625473322588_0.6732154672375719", "host": "s3://npm-registry-packages"}}, "1.11.4": {"name": "@webassemblyjs/helper-api-error", "version": "1.11.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.11.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "85a7e704d0ea184e17c7a1317d4d10f7d43ae152", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.4.tgz", "fileCount": 4, "integrity": "sha512-IKwN8aTLugQvTQvXsUIXAA4ApTWIQAGANP5fgpO4+fUSO0afro8qkp9LX4Nl2sjMd67caZy73i1JkwFoPtOMmA==", "signatures": [{"sig": "MEUCIGLjwmeWPnhPyhIK/i/ObiSAGAxFkxdRtM7waIOKwFvlAiEAtzXBbH3b89i7w+hOxwnUn+hTnmqHE1Nw1q1Z+J1M9qQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Ww//YsOO3ZimLDAunu6Cfe+UbifJgjVpFqKJYBbIoFZXOWHUDUIX\r\nyHCfSoNSmK38jnIsNxRG3zgiQDuvCt2Pf0BPMqVB3clNZ+pxlH6MPb+dyFWg\r\n1bhNUxGbsW5w/SbX2f0IRRHucT0f2x3ajN9MVz2qOuigXY8NdkHh74Fp3d8G\r\nebndAsJ+hhiJw4sCEvGrcJQEjHEhE+PmGCriedOmGUD9mZIQg8L9gjzcGvjS\r\nP/IJhGJGe1cuY2Z1XwH4oWKn7iUxaf9v2knBKbuQ4D1N1cOjFPcQvfgDwUIe\r\nRW8V9f18S5eH/ss635utRMCTN3T8enwrGWKlpR9DAQcmh2t2RW6FhCeBs6ii\r\nyqSjyX0GAT+LSP9s7LjpwnyxeC5exsruXE8lW4bty/MvUVNfSn0oZ/cjROu9\r\nHWdLwNyRIo8nFypeMGC+ThlQpJhZu8IcJ1WnZrVazux7ljeL8sfa24W0UWKA\r\nLdHtt/AN7Stx+13wL6SLtCpj7LABl8V3fC2LxnnqFYIrhNYUxi7S+mLc4YJO\r\nyCGAmc2uo9l9Jw3Og64QCCWbDUpdNdYresoWOKrgVTWhWYH5JeH1LKWq786p\r\nSleBQMIYBKLiqAfyb2A5wNa78PQndIGydchjw0EkQH8xcaAGQgF25g0xtqSe\r\nEC8cE31u7vmoP14i4/Q5v4ch2rkjLIqkVjo=\r\n=XQ08\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "5fd2425602b752576bbe8089c343d5d70ebc861c", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v16.13.0+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "16.13.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.11.4_1656762773663_0.3898874685450564", "host": "s3://npm-registry-packages"}}, "1.11.5": {"name": "@webassemblyjs/helper-api-error", "version": "1.11.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.11.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1e82fa7958c681ddcf4eabef756ce09d49d442d1", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.5.tgz", "fileCount": 2, "integrity": "sha512-L65bDPmfpY0+yFrsgz8b6LhXmbbs38OnwDCf6NpnMUYqa+ENfE5Dq9E42ny0qz/PdR0LJyq/T5YijPnU8AXEpA==", "signatures": [{"sig": "MEUCIH8+/lb16ntgzzO4WMlfVlhYtuYKEpWuugoNoMeaNsZbAiEAzvNrSZDtJXsaYiSEFtBR4MYFdSp+EDnojSNV6S+Z4yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO589ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLsRAAmogXVVX5LWhj8MS9K438ndgBdfWSLh4Cxg8q0tJAahwMGBzW\r\nXJ8Bqt25mnUBaGym4qXKAZag+twj0IKp6AlV4SEcI9UhpY96pkSoM8rs00He\r\nBz1587cVTzfm2mKC89xOW4HJTVK5Me5mRmu3zpCC9ciFyl9iSMSJYwmZsXk3\r\nIbIt8HdQzAWd9J6Dsa5eecMCJvaJIfC9a5CXuqS3fIN21AH9n2EDBJ1lyMj9\r\nwuF9rG96UOjdbPldoBvr+bo7TuVhutbJ3LBAc9q6YR9pyC9HgmHf1UneHSiH\r\n0TSV8q2HYQGabTVj5jrC5GVq4q0HU88XeWJbl/tsDY9DYLO9iwk5IRn2LR7U\r\nHh/4LAVT6BEqheUnX9vrEbd4RWnbZyKMqmRw6SSK/0nImYc3J7/ztiOwd3ep\r\nCqMZ2h0JEckb9YOQ3OIc3PpZssjL9GK24OYdSK3zMUEt9egmAyTKuJL/PpKd\r\nZvMG7ifQJ9sr0t5oK1Oy9daODS0eq4gSK84mbCaLE9BrXx96k/4U7GVprP0u\r\nNtnPINVnk3nrcmfxlmyD+UkXwYprpuL2a7WLcQcL+GZ2Nn+8K20KNQlrXCJl\r\nNeu3+biCfae2/NIRvujZkUpCatIvFvgpVWVdvDHV7YRRFjDTIKdvgEyrabs6\r\njrCEp7gpn2sDrsB/ycP+rqBVbZVrqV5Z9g4=\r\n=m+xS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cc856f3cc847a69c31e92ceb2c6527e1d30a9511", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "8.19.3", "description": "Common API errors", "directories": {}, "_nodeVersion": "19.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.11.5_1681628989205_0.604085538051395", "host": "s3://npm-registry-packages"}}, "1.11.6": {"name": "@webassemblyjs/helper-api-error", "version": "1.11.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.11.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "6132f68c4acd59dcd141c44b18cbebbd9f2fa768", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.6.tgz", "fileCount": 2, "integrity": "sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==", "signatures": [{"sig": "MEUCIQCR0XS631ZVcBLxS58st+U/pvCq+60BelQdOwPtfIi3fwIgQN0qFuTO6OsS6LU4AVFekSfXHnNMl+JDCmlLaDPnNfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5400}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "58d40904ea7de2dd17f6f8d894ebe611b812a4db", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "8.19.3", "description": "Common API errors", "directories": {}, "_nodeVersion": "19.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.11.6_1683645023152_0.3850073103078293", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.12.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.12.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "319f815585362598169aeba40e68817c7179ca86", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.12.0.tgz", "fileCount": 4, "integrity": "sha512-k/Zl3EpojCRavYtg6Kt7JBelfUJnDI3kHkCTgi+yWJaXgDFv9MjBImRRpj1ZkyD/NrblZnujpGN1XHxNI8bamA==", "signatures": [{"sig": "MEUCIEK4lMvUmL/1vv9rY/dwPCh82xZIif2a9sZ0X6UUT3F3AiEAtmD9Eee3JPIkvoICw+xAbrlG+XSgbum9H4HU4D1CkVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6d1606bde5ab7ef21ea4b25715bd2fe58e8742cd", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "18.18.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.12.0_1710325116871_0.2336801639666144", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.12.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.12.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e310b66234838b0c77d38741346b2b575dc4c047", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.12.1.tgz", "fileCount": 4, "integrity": "sha512-czovmKZdRk4rYauCOuMV/EImC3qyfcqyJuOYyDRYR6PZSOW37VWe26fAZQznbvKjlwJdyxLl6mIfx47Cfz8ykw==", "signatures": [{"sig": "MEQCIE32mteoiXhr9dNCa/l3e2kiLokASCl7p530Rl1LZYeHAiBUOUAqdq78I2XP0YGbJRhd2wiHFUI5m12XPDMjrNMLtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "18.18.2", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.12.1_1710325152846_0.39471032231879355", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.13.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.13.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "46c547337a9cc2dfc5d7936182843a2992d9d118", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.0.tgz", "fileCount": 4, "integrity": "sha512-t1+e8GOUQQV/K8zJgXWaXiv3kMbrIq4hJ556ej+JAQTXJYoYFwfMvsiPMMLAsZRN845KY9kA04SSlOl3AmqsCQ==", "signatures": [{"sig": "MEQCIEwh1Hk26JQEWzPShfAuUPLZG70inkT4fkmWYn4yp8U2AiA1rCws5Q0RC+KoRTj8DrrsSr4o5uMsJEe02gSkg9Xtlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "9b0c0c1c1e035e3336edbda8c330827a1f267513", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "21.7.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.13.0_1730912081970_0.9091748684845045", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.13.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.13.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "54331fc912a3605d6c39baaf2086b2cd71108639", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.1.tgz", "fileCount": 4, "integrity": "sha512-gO40LwtUJXTTGDdbTZ2kDlHY7LceJWQl9jX/5r0oxyRfZmvIpVviR+oFDQNjqerrormB7L9fiPOPQ2oqVuDMYw==", "signatures": [{"sig": "MEUCIQDqaykXZpLLKgmvZxfcV1V0U+16nXHaJjLw+61NLQ7JNgIgL1XNskqsf3kgHHQmH3iaqXERwAz5UEY1MLlLnAK3YQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cfe35c57093d414839b9350398369b78d97815b4", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "21.7.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.13.1_1730912127780_0.1692961350678086", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "@webassemblyjs/helper-api-error", "version": "1.13.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.13.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e0a16152248bc38daee76dd7e21f15c5ef3ab1e7", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz", "fileCount": 4, "integrity": "sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==", "signatures": [{"sig": "MEUCIHZDfaQ+CQboEPIKhcvQkPEyvhHnL/hs+r7m5v823zZOAiEAsHoXzOYNv7KGcKBAgd24pVA/FuLYxY4D5cK9V/fUIcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "21.7.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.13.2_1730929429060_0.46152631367029806", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "@webassemblyjs/helper-api-error", "version": "1.14.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/helper-api-error@1.14.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "4f66d0675e29f916be5113fa03d4378fb411c19f", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.14.0.tgz", "fileCount": 4, "integrity": "sha512-87QELGgWmIXwnc2zeifoMP9Lkh6zCB8kS1MKGswmUHsZaMUq74eFXiJpJWSjz1zpii1qvwMg3NQA565kEvSXKg==", "signatures": [{"sig": "MEUCIA+ca18dWAUM5p3PYkmuGckk+092+btRzQlEX/ud9ablAiEAvOesuqf4siwbnXA7XdALHSLot0I3fyYrpkgYEf5c8TE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11298}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "76babfc909478ebf173fc0269881878ca747dc01", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/helper-api-error"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "Common API errors", "directories": {}, "_nodeVersion": "21.7.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-api-error_1.14.0_1730929987726_0.9079303112555164", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "@webassemblyjs/helper-api-error", "version": "1.14.1", "description": "Common API errors", "main": "lib/index.js", "module": "esm/index.js", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git", "directory": "packages/helper-api-error"}, "publishConfig": {"access": "public"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "homepage": "https://github.com/xtuc/webassemblyjs#readme", "_id": "@webassemblyjs/helper-api-error@1.14.1", "_nodeVersion": "21.7.1", "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "dist": {"integrity": "sha512-qjyR8atrL3jhGo6YwhRUvqbw9a5ls9c/AQK6baoIu2iw2IHGnSmV7SaSHq0QQllV6tTS4eJ0t5uPTt08LPkYMw==", "shasum": "6852ed49aa3b7f8a744ae4f9d877f615d3360c2c", "tarball": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.14.1.tgz", "fileCount": 4, "unpackedSize": 11298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsb+vqflwB6Q9+frpKLsjtAKE++Ht28I+n6+PC5LtFzAiEAjEv/KLeDMfciedu0VL+Asot+GJVVBe7AR4M7qUM2mS4="}]}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/helper-api-error_1.14.1_1730930015780_0.5640115141218447"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-24T14:18:15.869Z", "modified": "2024-11-06T21:53:36.145Z", "1.5.5": "2018-05-24T14:18:16.169Z", "1.5.6": "2018-05-24T14:20:30.558Z", "1.5.7": "2018-05-25T12:54:03.359Z", "1.5.8": "2018-05-28T12:48:29.912Z", "1.5.9": "2018-05-29T13:12:54.860Z", "1.5.10": "2018-06-01T13:20:29.038Z", "1.5.11": "2018-06-06T08:57:51.476Z", "1.5.12": "2018-06-07T09:24:57.313Z", "1.5.13": "2018-06-30T13:44:17.213Z", "1.6.0": "2018-07-16T09:04:46.229Z", "1.7.0-0": "2018-07-18T12:57:44.707Z", "1.7.1-0": "2018-07-18T13:08:58.645Z", "1.6.1": "2018-07-18T13:50:06.347Z", "1.7.0-1": "2018-07-18T14:59:09.101Z", "1.7.0-2": "2018-07-18T15:14:13.034Z", "1.7.0-3": "2018-07-18T19:03:28.014Z", "1.7.0": "2018-07-19T16:00:32.212Z", "1.7.1": "2018-07-19T16:31:21.087Z", "1.7.2-0": "2018-07-19T18:09:28.163Z", "1.7.2-1": "2018-07-19T18:24:07.351Z", "1.7.2": "2018-07-19T18:33:09.772Z", "1.7.3": "2018-07-23T07:02:07.993Z", "1.7.4": "2018-07-24T07:02:00.522Z", "1.7.5": "2018-08-16T14:53:19.438Z", "1.7.6": "2018-09-10T14:17:06.859Z", "1.7.7": "2018-09-19T05:39:00.811Z", "1.7.8": "2018-09-21T07:37:15.848Z", "1.7.9": "2018-10-18T16:42:33.931Z", "1.7.10": "2018-10-23T07:31:04.029Z", "1.7.11": "2018-10-30T18:00:42.122Z", "1.8.0": "2018-12-11T07:24:05.993Z", "1.8.1": "2019-01-15T08:21:06.502Z", "1.8.2": "2019-02-14T16:21:25.730Z", "1.8.3": "2019-02-18T08:36:58.224Z", "1.8.4": "2019-02-19T17:26:58.281Z", "1.8.5": "2019-02-24T10:42:39.086Z", "1.9.0": "2020-02-01T23:19:44.441Z", "1.9.1": "2020-10-03T20:07:50.684Z", "1.10.0": "2021-01-07T16:17:45.425Z", "1.10.1": "2021-01-07T16:45:05.173Z", "1.11.0": "2021-01-07T18:11:26.815Z", "1.11.1": "2021-07-05T08:22:02.727Z", "1.11.4": "2022-07-02T11:52:53.853Z", "1.11.5": "2023-04-16T07:09:49.404Z", "1.11.6": "2023-05-09T15:10:23.296Z", "1.12.0": "2024-03-13T10:18:37.025Z", "1.12.1": "2024-03-13T10:19:13.002Z", "1.13.0": "2024-11-06T16:54:42.154Z", "1.13.1": "2024-11-06T16:55:27.954Z", "1.13.2": "2024-11-06T21:43:49.225Z", "1.14.0": "2024-11-06T21:53:07.899Z", "1.14.1": "2024-11-06T21:53:35.964Z"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git", "directory": "packages/helper-api-error"}, "description": "Common API errors", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "readme": "ERROR: No README data found!", "readmeFilename": ""}