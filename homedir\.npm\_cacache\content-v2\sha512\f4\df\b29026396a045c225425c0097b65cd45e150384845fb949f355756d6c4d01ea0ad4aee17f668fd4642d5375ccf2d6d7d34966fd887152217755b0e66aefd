{"name": "lightningcss", "dist-tags": {"latest": "1.30.1"}, "versions": {"0.0.0": {"name": "lightningcss", "version": "0.0.0", "dist": {"shasum": "a377b4657296f851761c9dd8f3795ef0077e0cba", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-0.0.0.tgz", "fileCount": 1, "integrity": "sha512-tYo+7zQFwWzcUlEXC91mVSASNEUn6bZFzPI7KMAgIKaaJfyQyYY6nlDHc6tVoiCXMGOP5v/kVCU04lycN2zlIA==", "signatures": [{"sig": "MEUCID8os5M/GU3o4D1t8Y1ZrjOvUu4BLieL6UzLWdOhbw46AiEAnmBA/7XghfWxzfXLEpJZR4HdLr6BD6IBxWxXYGq6hnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBBJaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhtA//dDFfbOr2sOI0dUVWI0xvjZHv7STcEYAYTOWCy0AYfVSD7Axu\r\naiq337lQKC5A3Dn08gobxSj86dLuS9jrAWEumLBf2lSiK7Jpb7ZdY4iqvsZc\r\n9PP2l+Umw820nanuyNOWCRBKHBqnnQT3CO1YbFevsabi6H0gHiVGuVpa2tAe\r\nlopergaXClyF+hHWYcV+GH0S0tNxQwLHgFsh+DZGg6u7eaBiXejTOw273mjq\r\nlIqaMXDoxJkrmvJFbjWUf0JKQw1GyaVfoJwEj0NaGTXRTcyL9oLbmkMfsglU\r\n3LqlEVC+yCBVRotGWSkvbX26VOEgBfgFUl9QNe0eduvzEKzBSjsYqKPg6u4q\r\nQP5s0Q16cmilpBMfS0bjf5Ef3SIABuRWn4hyF6POfDYJlUz7FbDSzd8lLpyr\r\nSt7EDr3TUwKy6EpzsT9fuuBKDnh+dO94JJTTyq5su6xrwz3sR8xdXq44utco\r\nknSx06mhqkx71Zp0pr1Jcs6URCuNhAWBVnvcpAKKGb2zt56SiRM4OKmwr9yb\r\nAygFvYdxOQMqyG7PBsp++DVUejCVqT/NTwbSPAVonj8/dKZLipdqS/E7tnLZ\r\npZl/tOj5Cwf1ZyPItm9tKZVSnEfV+FEWeAkZmopiBykmP4KGDfor+BCyHjcX\r\nu+R6Aa9tIpzAc+ZdCH8NXFAiy4pHccIWbFs=\r\n=LbKI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.14.0": {"name": "lightningcss", "version": "1.14.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.14.0", "lightningcss-darwin-arm64": "1.14.0", "lightningcss-linux-x64-gnu": "1.14.0", "lightningcss-linux-x64-musl": "1.14.0", "lightningcss-win32-x64-msvc": "1.14.0", "lightningcss-linux-arm64-gnu": "1.14.0", "lightningcss-linux-arm64-musl": "1.14.0", "lightningcss-linux-arm-gnueabihf": "1.14.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.14.0", "lightningcss-darwin-arm64": "1.14.0", "lightningcss-linux-x64-gnu": "1.14.0", "lightningcss-linux-x64-musl": "1.14.0", "lightningcss-win32-x64-msvc": "1.14.0", "lightningcss-linux-arm64-gnu": "1.14.0", "lightningcss-linux-arm64-musl": "1.14.0", "lightningcss-linux-arm-gnueabihf": "1.14.0"}, "devDependencies": {"parcel": "^2.0.1", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "84daa8abfeeff9fc9dacf9a1ea9ee6c9bbcc597d", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.14.0.tgz", "fileCount": 9, "integrity": "sha512-Vwa1JWiLGRLntf4TXRu/zF2RaHRfJjbZwUzkl2C4LhnRQzsEwkyeAQjAxtpJ5NDnVVUf10q1olEzkFF4AWZLOw==", "signatures": [{"sig": "MEQCIACOc+F6gbQcIB9fKq1O+rfyyIbE5DNgHbaJcC399cX8AiBChUUD2XEFw54JL59YZiZezPAZCBUF2Bn61Y2eoPT2bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjGXtfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmok/w//S0KMVmaubl9DAU93NllDaa03pKQg73Pf8l3lIoI8mSFW+UAh\r\nPhEgZnEdNV/HfIuaas7pR9QSrRxnreTVyXhjbMBve61PCL6RF1LcXCcbD+d7\r\ng0TP8zJGuw5r0KGSQRuYTF8XKxWfVuKUtidtmBd6mH8WssLpy22GQWkIpuVj\r\nq7llgKdj26/7zQPPDL3Lh8Y6uTnEX1zIFMTnWKS02tZmm6V2AOnEWtu/uvxq\r\nDp+aGoIzmBLFdoS5okWgPstWKd16mqaVioPVJu3LFVo7idQkqNHto2Gc4Maw\r\nAwa56BxzYLhpPcOvWRfqLW5B/QJo0mQoMV2eQ0KnU+mFHxaVioG0V6Kwdh4E\r\np5p8kkgDFwBKPwf57OzxAlBsJThm96VbZhZgmnfw7h+iiV7b+AM4w77eKIaH\r\nDEojBgO76XdVa0yYjgaunj+XwQ7l4VXP/2VCXXQfXe5BXFpLelx9cx+d4aEr\r\nadpOTznPh51dYY2MJr0vKH4RGzcqtyhstWW68bmjfCH35ca8gz6QdIPAv/98\r\nmeoE+bEKTZtHp0KIx5McaFyWW30/oL29nhbNmvEtYUsv1/Vpn9e2uJNEX1VV\r\nVTiz6l/38b4/9f29HknMj76NFtQZWRAa629CUQc2BNW2Kk7Ps58nCi68ZA+Z\r\n5k32BAo4U1t59D5lpLWyQnJhj8TYfxXlfjs=\r\n=1sTC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.15.0": {"name": "lightningcss", "version": "1.15.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.15.0", "lightningcss-darwin-arm64": "1.15.0", "lightningcss-linux-x64-gnu": "1.15.0", "lightningcss-linux-x64-musl": "1.15.0", "lightningcss-win32-x64-msvc": "1.15.0", "lightningcss-linux-arm64-gnu": "1.15.0", "lightningcss-linux-arm64-musl": "1.15.0", "lightningcss-linux-arm-gnueabihf": "1.15.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.15.0", "lightningcss-darwin-arm64": "1.15.0", "lightningcss-linux-x64-gnu": "1.15.0", "lightningcss-linux-x64-musl": "1.15.0", "lightningcss-win32-x64-msvc": "1.15.0", "lightningcss-linux-arm64-gnu": "1.15.0", "lightningcss-linux-arm64-musl": "1.15.0", "lightningcss-linux-arm-gnueabihf": "1.15.0"}, "devDependencies": {"util": "^0.12.4", "sharp": "^0.29.1", "parcel": "^2.7.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "path-browserify": "^1.0.1", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "a7e8b4e76718f6a75c504f324563628d2d8dfeb9", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.15.0.tgz", "fileCount": 9, "integrity": "sha512-FF1SDM8Mo43lExnWkrXesDMoKFYM5iDbqbLl408VH6VyB4TdMBEQpWW4bbUrE03Vhl7ioIUMEJfgzAV7vaeYkw==", "signatures": [{"sig": "MEQCIDsKeXlBEdD7UGsCqqgwOrLIANu9wh7tq7vwjkjIJoeJAiBg2NFDfpHw8FZxW5FWzn6j22zqbNsXpwikzUi9YWbV/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIqcnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5dxAAnuQG+VKf6dPhZLmrNgFjiqJpUYWUgwGNA58IaIDraJC+grRW\r\naCOGzf/kct6eIvvcz8l/7Dk+BfCyJHBkAgirN6Hu5YaPHr2+6SdwuI5YymMw\r\nO3rNpOIglSu6zf4lJN1FPSNsXprNLz7bkJdfl+pgSo4nr1JsxxmK0ZgD+r7e\r\nIqpZkMfgCun+UYZVDy4QZzPz64nCyBPwa6iU+MpasGd+HfcvKsQR7qKDgOnp\r\n2yzMrfDJJv0E8fdVXF2j1wbv8Vws5gtP0M21K7B9C7gZTH/wYTtcP1d2K3aW\r\nTFs4+Xc6T+f8cL6JmoSKmqwRROyaKR6bRdnMjI9mnXy74zqLCHG8GeUFB7Kb\r\nXAPICaon9LJ3P+dk9PAQrTEj8TYYM6C23nuLEV9SOtJZ4y+56lH+ekszQDVY\r\nO3rVQtT5pDS91H2i+mFJMKPzz99Te5tvMy1qVsmWEam9YuBTyZiBm/7Sq1dx\r\ncxiK38S1T+aHqzDOwMOe61imV1sLGAlQvhMn5KGsVG51SpwtPtWNd9PkXkke\r\n7jvRUygwp7P4x2bJMvMXJeej31nir5LG2c6BXSUeoK56yfquWw+gi/Tok12k\r\nAlzn/obljULXCpBKMS4ft5FyFIkUnLiCOHXPn2/uzBu678Qo/Y/NkdLQ5qem\r\new6k3si9SKQUXq8Wfy9yfYCN+ZxjFr5fpnE=\r\n=xj2L\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.15.1": {"name": "lightningcss", "version": "1.15.1", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.15.1", "lightningcss-darwin-arm64": "1.15.1", "lightningcss-linux-x64-gnu": "1.15.1", "lightningcss-linux-x64-musl": "1.15.1", "lightningcss-win32-x64-msvc": "1.15.1", "lightningcss-linux-arm64-gnu": "1.15.1", "lightningcss-linux-arm64-musl": "1.15.1", "lightningcss-linux-arm-gnueabihf": "1.15.1"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.15.1", "lightningcss-darwin-arm64": "1.15.1", "lightningcss-linux-x64-gnu": "1.15.1", "lightningcss-linux-x64-musl": "1.15.1", "lightningcss-win32-x64-msvc": "1.15.1", "lightningcss-linux-arm64-gnu": "1.15.1", "lightningcss-linux-arm64-musl": "1.15.1", "lightningcss-linux-arm-gnueabihf": "1.15.1"}, "devDependencies": {"util": "^0.12.4", "sharp": "^0.29.1", "parcel": "^2.7.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "path-browserify": "^1.0.1", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "a951a0ef651a0d026de4f3ec03bd2a6feae25da4", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.15.1.tgz", "fileCount": 9, "integrity": "sha512-d4fqKl8sqpdM27OsseAbKKxhD2otiu6stS7yWlm/DA7wOQAIDKMu/dke54cEN8ED19v9H2pEzMPGTsRfnq3Rdg==", "signatures": [{"sig": "MEUCIHS3Ela7Y5dRjUXSzM7dQCocnHSiSi9C/ecGvbmWT56aAiEAjaqN5nLPq4aMmKLvF5L9sfm7FI/CY4Faxa40aeFqETw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjI/JLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm8g/+Kz+iey3G9nl+7ivPzSBF7G8h0DEpvkqG09Ay6Ikybe+F3Q8M\r\ncfN4ls37mJPmU3lk+t3PbNRfmL9cy70LFWBsNckdAkVUst73Z0yFhcTxF9tb\r\ntVeV0AvL1aCRlBd4xLX2J0XBTwQF1aJI/rCD1viI0QAarj4SaGyQGpHvgib5\r\naz9k4ybt504ieti6OmsBmwADcSMd5FFe3zrPekogp7HUyJMdWBerozK5znsT\r\nM58OM4e7AORNXPT5aK3QsiG6kth86ovU4xWx8rdFGAAz+dFPqvqt4eR16lgV\r\nGfayvewkm3mUyVjUezzaIWNKW5pZKYV245QeJn4CuLhppSUL8U7jnuZMWGeh\r\nDCKXjk3/V0tWQXRlABoapvrj/bUtdvpg2TPkUT5RaVf5OMDiLWhmeLc/2Ykv\r\nAGfJNKCTRRxCJ6U9j+GZ6wIPiPkOG10Bwc+mCcj9EL/fjYrg4Bpl4FDt1kcD\r\nvgIdiSrtV8d7PHIMn08K1Hl+kIroM47BhlfU3Zk2/YyJiAjbIzPI7it2IF8/\r\n3d+U7GfE4O/lXJlTNbDpzjEvEfymUiiYREI84UOhvNaqn4vS104H9kj0KYfT\r\nhvOOjXCOIot53B6b4UPynIBarQh3aVHq/NTsRt0uAvYpU2MTRPchnfVokGSp\r\nq7xzAQnvHfRCkj7sivV/HVWF3M5UVtyejtc=\r\n=2nyn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.16.0": {"name": "lightningcss", "version": "1.16.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.16.0", "lightningcss-darwin-arm64": "1.16.0", "lightningcss-linux-x64-gnu": "1.16.0", "lightningcss-linux-x64-musl": "1.16.0", "lightningcss-win32-x64-msvc": "1.16.0", "lightningcss-linux-arm64-gnu": "1.16.0", "lightningcss-linux-arm64-musl": "1.16.0", "lightningcss-linux-arm-gnueabihf": "1.16.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.16.0", "lightningcss-darwin-arm64": "1.16.0", "lightningcss-linux-x64-gnu": "1.16.0", "lightningcss-linux-x64-musl": "1.16.0", "lightningcss-win32-x64-msvc": "1.16.0", "lightningcss-linux-arm64-gnu": "1.16.0", "lightningcss-linux-arm64-musl": "1.16.0", "lightningcss-linux-arm-gnueabihf": "1.16.0"}, "devDependencies": {"util": "^0.12.4", "sharp": "^0.29.1", "parcel": "^2.7.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "path-browserify": "^1.0.1", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "4c8e4ef8133d54488d1482a115d3758259191c52", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.16.0.tgz", "fileCount": 9, "integrity": "sha512-5+ZS9h+xeADcJTF2oRCT3yNZBlDYyOgQSdrWNBCqsIwm8ucKbF061OBVv/WHP4Zk8FToNhwFklk/hMuOngqsIg==", "signatures": [{"sig": "MEUCIF7PkYozn9e4n2i2MUe967ntP3r/GpApq84ETtkpmlaYAiEAkOeFqHhxNWPRjRdXLYeOuo6zkNCisNl4pJHket5EB0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKTIqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOZg//c22Vv3A3+bw4EvWsDF+sK5SipztBknmChmbipa3TqaqlDPkD\r\nS3RJvNzN7SU1WTUni0kTp7G9wNLqQgBo9DkH6/xn02J1wfVRY2LshaxEP41p\r\nnJNVf0lVMfDeCs3vqVDibzmyeIfRnbucDUSqezpzMgYsuB2w2dR2ni6Xo+0u\r\nJT8z6Vd4K/ydxFfcd9pkaS0qyh6cUYNvWZGpCinOM+k6qxT6vM3w1FPrgHWd\r\nIdpLWuh1dDd/+ZjNA3Sg09/6bJoC+bByicLoLqOp4cUva5cEBHOR/lAp4Ojw\r\ndfISaikcY8Q0McaCtxC1Nhu+cyVTZAyoBBVXjhsvCNe4JGOcrIQ7KvCeKZGE\r\n4Q9Fb44Y53I7/vsNyYtMTFcV28t9YrVo8Gcx33X5gkdvjh6SxYsTEdkrp8k4\r\nEnbVNJ1IEuJ2tyXr+sljdWRdiALdA38ctsTnZ3TdP5CLHq41ltxW/4vdntMs\r\nYC1qo/Me3qOaX5UWsgLwjddRVcjUAjp0np7ANYi6ncwHsfzaDBOiYYfpSuRX\r\nq0mETG6QIdMuqZfc6OT48yuvudjxmoHTUicLWXe36YrTfNl7DQKE2iQXstrp\r\nWgXA3FAmCB2/Z0VQHCt456quWl8X7uEFDylY1YTWDyRuO9HJAX8raLecLgnl\r\nhdvObwGO6MpV9EOiOKTTAC2HqN5+mNj/Yys=\r\n=U1z9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.16.1": {"name": "lightningcss", "version": "1.16.1", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.16.1", "lightningcss-darwin-arm64": "1.16.1", "lightningcss-linux-x64-gnu": "1.16.1", "lightningcss-linux-x64-musl": "1.16.1", "lightningcss-win32-x64-msvc": "1.16.1", "lightningcss-linux-arm64-gnu": "1.16.1", "lightningcss-linux-arm64-musl": "1.16.1", "lightningcss-linux-arm-gnueabihf": "1.16.1"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.16.1", "lightningcss-darwin-arm64": "1.16.1", "lightningcss-linux-x64-gnu": "1.16.1", "lightningcss-linux-x64-musl": "1.16.1", "lightningcss-win32-x64-msvc": "1.16.1", "lightningcss-linux-arm64-gnu": "1.16.1", "lightningcss-linux-arm64-musl": "1.16.1", "lightningcss-linux-arm-gnueabihf": "1.16.1"}, "devDependencies": {"util": "^0.12.4", "sharp": "^0.29.1", "parcel": "^2.7.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "path-browserify": "^1.0.1", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "b5a16632b6824d023af2fb7d35b1c6fc42608bc6", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.16.1.tgz", "fileCount": 9, "integrity": "sha512-zU8OTaps3VAodmI2MopfqqOQQ4A9L/2Eo7xoTH/4fNkecy6ftfiGwbbRMTQqtIqJjRg3f927e+lnyBBPhucY1Q==", "signatures": [{"sig": "MEUCIF/in4jOiujRGAcjQRwNqlhsceFWBEp2p0jlk3ePcSyHAiEAyjpGM7dvjCBiUCBEUahjaVt0FaGZifZgrIy80cwR9qE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ/anACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Kg//YMEGmP5eWpKliNPHAr4OhylbraVu+Sk1FhO90wTOJZ4yAZgi\r\nmdqi3xZKIr6Eq6aw0c694MRXh+3bIgnoK4We7PZM03L4BavtTP7p//3MxprW\r\nAb0s4bik3yGymX70zl3yWnHg40hw5ZbwjKW5PO01SHwZqbeIBc4HQovbozzH\r\nagBaBbzrWQL1Yz/MaARY6MrZoHwI8ziDoDBygSMRTIgD29qZtLdw3+JUl3/v\r\n9gIyxJSjdJUDggNqhRND+EQV3r9K7hjyYwOuFM6pCOLfj4WsNxZCma+DNbkw\r\nv0ElGi7CUjM+lYv9c6Zjn0ObebtFq3hY5sX5/de+r/8bQkgiwpfFquNFMvOS\r\n2vIjmu0fpVK8ZfTTPCAPQf4Mt7HS8hQht7XhBlqhPjJ67VWYo36Tkse5F2Mc\r\nPG3x9bQDubwJBJyv+uCrNFz9Co3U+7EMEBIjAf9EVgejmhojZa1TA5c0PJkm\r\nH2MBuuE1uGBSBudaPZ9Z+xVJIzTZ1eJZjdVbrkEku5MmCBffila45hOwVEi7\r\nxdZ1sBZ0ileBtJGtvT0lG9J7hhIpuUqSpIMpwQhN1RzNgtzKD/9lnIM0hCOb\r\nknRfd7ohi/VQ2YuBlYfm3Ew0WqVEdIF+Ltz8SBZOEgaD8zEtTXoQ68irjR3t\r\nExSnoMncuDX6giW6YMsvh7PcE9UtnA8+M6w=\r\n=SBjs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.17.0": {"name": "lightningcss", "version": "1.17.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.17.0", "lightningcss-darwin-arm64": "1.17.0", "lightningcss-linux-x64-gnu": "1.17.0", "lightningcss-linux-x64-musl": "1.17.0", "lightningcss-win32-x64-msvc": "1.17.0", "lightningcss-linux-arm64-gnu": "1.17.0", "lightningcss-linux-arm64-musl": "1.17.0", "lightningcss-linux-arm-gnueabihf": "1.17.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.17.0", "lightningcss-darwin-arm64": "1.17.0", "lightningcss-linux-x64-gnu": "1.17.0", "lightningcss-linux-x64-musl": "1.17.0", "lightningcss-win32-x64-msvc": "1.17.0", "lightningcss-linux-arm64-gnu": "1.17.0", "lightningcss-linux-arm64-musl": "1.17.0", "lightningcss-linux-arm-gnueabihf": "1.17.0"}, "devDependencies": {"util": "^0.12.4", "sharp": "^0.29.1", "parcel": "^2.7.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "path-browserify": "^1.0.1", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "32fec5f10def6bd32a608ae302d0266ba7d90332", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.17.0.tgz", "fileCount": 9, "integrity": "sha512-flO6jocjeV0MUDkYCtg1+jwLqAlqrV/t3n7rUrJ6/tcntm5zfBXsewwOj7PSSv9iTNqFKJX8zIkx/M1W2AG3kw==", "signatures": [{"sig": "MEQCIHj+CTHYKisC0my5PGAYYglt0kKH9CoAT/S1l/CzvfoMAiBSjxVI1k06IqYMt2grBeayuo5Nw3D4fO6iTO9k/0aEBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhjfNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfUg//Ya9cVtAgmYDFspP6xb65Dtda52S+ob1cu0P16Pr6mioJ8jH/\r\nalzmdvBGuFjG1N66sEoA94KHPH7x75Vt9dGUWrN4EI5BvoRjcrRywyCHo1Af\r\nFZm+3y122WiLgdwev5XhiLIRsHdZpGvb+wk4BYvJ31IRmBtuaEUCoQrD8fRr\r\nJl+xx2zixQpBpRi1OiWGLuGYAolNg29ak+xPunjZIFY02pXke7VPi4vtOL1p\r\n2GchKfzA2+sUYM6MMjnxh+hIljozz1AyEnlY4kABG2SjJcCVPOyEh5jGFwYe\r\nBzY4wpjwuV7ka6FC5KgKwmjswPh6bvCv45aVf4ZSkuTi6WAcIxrSludbG3gS\r\nXzt2Ti64ACSNbuLAefD3osYEyL1k0piVxVHQHnh8ep1WjuEi9LbVRw1RXa0y\r\nXMQfJrBhTSIs+d4RpzrWHx937TWZkmL1NSBRcrfocqaKMSvp5wiB9LDUxKga\r\nVs3S1zgZ45timN50w+fRPFx7zMDCPVnd9trngzkIyPNYEUn11qLukDMtUcAU\r\nqGy5ti7cX+WLB8gXd89huYqvT0qMl74+0yNAl9M3WuybwmkhAZdz5eCM7wiO\r\ncO7rGAZMRsZydPtwFFAWWnmjQUC4LdYwXNcO2ADAXEPmAd+YHcWbkOmncRDO\r\nMriDCFsyXzeyXsycKySUUen8Cosj7TJxYIY=\r\n=Vstt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.17.1": {"name": "lightningcss", "version": "1.17.1", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.17.1", "lightningcss-darwin-arm64": "1.17.1", "lightningcss-linux-x64-gnu": "1.17.1", "lightningcss-linux-x64-musl": "1.17.1", "lightningcss-win32-x64-msvc": "1.17.1", "lightningcss-linux-arm64-gnu": "1.17.1", "lightningcss-linux-arm64-musl": "1.17.1", "lightningcss-linux-arm-gnueabihf": "1.17.1"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.17.1", "lightningcss-darwin-arm64": "1.17.1", "lightningcss-linux-x64-gnu": "1.17.1", "lightningcss-linux-x64-musl": "1.17.1", "lightningcss-win32-x64-msvc": "1.17.1", "lightningcss-linux-arm64-gnu": "1.17.1", "lightningcss-linux-arm64-musl": "1.17.1", "lightningcss-linux-arm-gnueabihf": "1.17.1"}, "devDependencies": {"util": "^0.12.4", "sharp": "^0.29.1", "parcel": "^2.7.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "puppeteer": "^12.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.6.2", "autoprefixer": "^10.4.8", "caniuse-lite": "^1.0.30001373", "path-browserify": "^1.0.1", "@mdn/browser-compat-data": "^5.1.6"}, "dist": {"shasum": "cce53acf117a6f9494bc77e8ac6550286d621243", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.17.1.tgz", "fileCount": 9, "integrity": "sha512-DwwM/YYqGwLLP3he41wzDXT/m+8jdEZ80i9ViQNLRgyhey3Vm6N7XHn+4o3PY6wSnVT23WLuaROIpbpIVTNOjg==", "signatures": [{"sig": "MEUCIQDACC9dVq9rZi8blG1qK/qUBBOMKzuRxXvh8I66HrqsJwIgClFRl9hBWu+yOGZG+PMCIZJP9Ny2uZcixg0qf3SEeso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjh5NPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmptnw//QSX7at7VIP3xec6/RiuwVdzz5P8/K/mArrfHhW0oFt+CZT4i\r\nJyfPAucBvOCaNBfNAvrC6DE5zLx1nVTd9j4hkKDPsVI/8h4/IymmlB+LDDhC\r\n4vL7ouLDnFgl9wbup5h6cfLA7fSa/RYJYtYRrXEb/00nyCrFXDe3fEvC7qoh\r\n+qScXe16XvWc1+NEhvJAdsxEETm9pEyxe1HD0mHfxJKfhA5PC4+NLuvK09vp\r\nUHFHovGy9NwVIsfOZGx1KspEDyKAvlYq2IxJmquoYHHXVNnZWIlkcu861Hc0\r\nMQyaBVxYmTztP8bWBDCP0xsgtSrLsmy6qGgHbu5BNr7tkac/7GbMVuTbG+WG\r\n2ScXk6HLEoarg04Bmzk0dRY1RPdOqDr3f3BuQ0kZC4G+wIbtvExPLuKf6IHE\r\n3u8di1cmowPZjtscLfqXSByjBXaiGHLoAY7gO3NwCm7WbOWScQQNtdIu62vz\r\nir1i7D6hNt3HskUo2pfDlOvdvl5m22iAw+UbHFxTZgcW9UtYHX6VY8clwKuE\r\n5Ng9yNjtJo4whGdYJZ2nMuycW2/2aSoEVHHlK6ih2Mkf6KcrPZBGuVgIr700\r\neIY06jZdDvgX4O9KtpGg/D/Ylso0acLRizOcoiLxKxQQpLnA+6ggai3/2nm/\r\nmCqAG6m9FPtZQxR3eycKUYCtUPWTnHxOEAc=\r\n=UVg1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.18.0": {"name": "lightningcss", "version": "1.18.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.18.0", "lightningcss-darwin-arm64": "1.18.0", "lightningcss-linux-x64-gnu": "1.18.0", "lightningcss-linux-x64-musl": "1.18.0", "lightningcss-win32-x64-msvc": "1.18.0", "lightningcss-linux-arm64-gnu": "1.18.0", "lightningcss-linux-arm64-musl": "1.18.0", "lightningcss-linux-arm-gnueabihf": "1.18.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.18.0", "lightningcss-darwin-arm64": "1.18.0", "lightningcss-linux-x64-gnu": "1.18.0", "lightningcss-linux-x64-musl": "1.18.0", "lightningcss-win32-x64-msvc": "1.18.0", "lightningcss-linux-arm64-gnu": "1.18.0", "lightningcss-linux-arm64-musl": "1.18.0", "lightningcss-linux-arm-gnueabihf": "1.18.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.8", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.1.6", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "ca3327a1a7571a83bbb9733ed4e4cded775bdadf", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.18.0.tgz", "fileCount": 13, "integrity": "sha512-uk10tNxi5fhZqU93vtYiQgx/8a9f0Kvtj5AXIm+VlOXY+t/DWDmCZWJEkZJmmALgvbS6aAW8or+Kq85eJ6TDTw==", "signatures": [{"sig": "MEUCIQDG7fg+eIQNIb0mXCG7vDfr5fpz8PKdcvlwNU+hztxMJAIgZTaYvGQVBYciEawNkFPYLXkj8t9cwv9LwX/1zztWshw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 445038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtbDHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQvBAAgynOIdOywEwAzatXmN/bq8DuUS8wr3WaGirYP6KeGN/fEz1S\r\nM7UH9IHxIXYJcMCEAog3NUjnoqVtmGHtLWQkhYuvzNhK2iQkxP50dsU/IM7l\r\nBl4VWkTFs5znFHbCL+vC5tItaFKkPuxmAXF2wQfR74IuCQfAkoUBWTNofAIY\r\n/9Ze6UuOoWOiIZulvEMmcJpJVTugjtaMpeFFoQVCL6eAAGKgQ7lOoz73mrsa\r\nx4z7+RcOEnvO7wHDztwrTx9gf9IKi64t5hEpOqe1iYbmD2LltTiQiFnZCBlX\r\nYL9K6GhURMz6/DflAeHcouVRNOlx+OQcox5h0Y0mJ4QiJCml6gbwb58lhllz\r\nO6xVBhGIopLMt1RaiHVYsrpBEN9gyfUUzvjyrzbzCrVwpPqrG/7OzQiCzgcQ\r\n3/JEiQA8AUBGhNCMUrACEqJGvIFL71XEu6OK/NBT+yg9DNPNpezC0kQjBtCW\r\nlx9qwnPqOGvJcbJek8d8UFsXAavd9rffsO23qMDLWottmTgEyrvi0KpR8W84\r\nzbyDoBXTvz04EbyYqhXEj6y7SkSLRGi9yXtfNe1oBGqFu8cZeJasgS/O3iiF\r\netUTNDVLcWOE2J5g8dou8m2jGTa+XzZvFhbUx7AwedtQFnPVT0mC+QHUMY83\r\nxTRjwDRXh+0F1MCj1O7f2fOnzaNBtKAJs7E=\r\n=dotC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.19.0": {"name": "lightningcss", "version": "1.19.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.19.0", "lightningcss-darwin-arm64": "1.19.0", "lightningcss-linux-x64-gnu": "1.19.0", "lightningcss-linux-x64-musl": "1.19.0", "lightningcss-win32-x64-msvc": "1.19.0", "lightningcss-linux-arm64-gnu": "1.19.0", "lightningcss-linux-arm64-musl": "1.19.0", "lightningcss-linux-arm-gnueabihf": "1.19.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.19.0", "lightningcss-darwin-arm64": "1.19.0", "lightningcss-linux-x64-gnu": "1.19.0", "lightningcss-linux-x64-musl": "1.19.0", "lightningcss-win32-x64-msvc": "1.19.0", "lightningcss-linux-arm64-gnu": "1.19.0", "lightningcss-linux-arm64-musl": "1.19.0", "lightningcss-linux-arm-gnueabihf": "1.19.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.13", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.35", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "fbbad0975de66252e38d96b5bdd2a62f2dd0ffbf", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.19.0.tgz", "fileCount": 13, "integrity": "sha512-yV5UR7og+Og7lQC+70DA7a8ta1uiOPnWPJfxa0wnxylev5qfo4P+4iMpzWAdYWOca4jdNQZii+bDL/l+4hUXIA==", "signatures": [{"sig": "MEQCIFDF1+Q9Usq1cQhuT60OMahuLsv9+Ju+vYkHur6enHqOAiABMzTTzNpttwZRm28E9v6Y4mCRRjygBRuhVxev4poK5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 454123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6l5qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmGg/8DbRlMaabhivPWQm0R2MRoUB2vU48sRNS/GnvwYsWlcNpmFz4\r\nKFr6jAe2H3SkhW6HMm6fsWzC6FAftRr7jsu9eu2j7OZPvWAYuMMQg+n2g7XB\r\n375PsJLmw7HHqcnaXaDbdE1SNt7hIRa+39SXvrjmB6yCirXfSUzdZInoHo2X\r\n8dPA08qcpslOa+/QuT3JcWP8vvWu9yrIaVH6O7pbSPGxOSmT+tud1nebRkmK\r\nn1SAPm05+KI3Zv/bowEOZCwtxWYthGI+tgu7EdXIng949xaQZeWZsrbkZzU3\r\n7uoydlxRM7AO/jt+W/txZN/5AZ8swVNg9SYS9e1qiT7FuiwDjBXD98PhIocH\r\nyDHnkpnqnsQ67sN0SDrhjwCHOf1bDYkia2yTnZ+rxXsrJjBTE76sz+8qyfL8\r\nBecBL3JeUrsUyaUDHgmIqYWfMVoewxswzVlhB68HZ5HYnd//3tcYrbe/blIp\r\nZPFZvTV4L/iW4ysUeVUJbwrlGbtV9MKmvjvC39NiY84/T1raLPHeLWE/gTqm\r\npW8D+tfgsYsZV07QAO5oA0XA3dOUT7k4q6nIW3Xy9s9blPg10XePGcKgfioC\r\nJh0C3NRsui9vzBkgJIAhLy5Q5PUMPzSYPtidWYM9RfHG88KC20Cf3V9H6m0P\r\nb7HTvde86Y23hIUv5Jt7irrRC/Ap1P0278A=\r\n=7a+k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.20.0": {"name": "lightningcss", "version": "1.20.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.20.0", "lightningcss-darwin-arm64": "1.20.0", "lightningcss-linux-x64-gnu": "1.20.0", "lightningcss-linux-x64-musl": "1.20.0", "lightningcss-win32-x64-msvc": "1.20.0", "lightningcss-linux-arm64-gnu": "1.20.0", "lightningcss-linux-arm64-musl": "1.20.0", "lightningcss-linux-arm-gnueabihf": "1.20.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.20.0", "lightningcss-darwin-arm64": "1.20.0", "lightningcss-linux-x64-gnu": "1.20.0", "lightningcss-linux-x64-musl": "1.20.0", "lightningcss-win32-x64-msvc": "1.20.0", "lightningcss-linux-arm64-gnu": "1.20.0", "lightningcss-linux-arm64-musl": "1.20.0", "lightningcss-linux-arm-gnueabihf": "1.20.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "efa36a52feae9b0c8537c8e650a7819f549a4a23", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.20.0.tgz", "fileCount": 13, "integrity": "sha512-4bj8aP+Vi+or8Gwq/hknmicr4PmA8D9uL/3qY0N0daX5vYBMYERGI6Y93nzoeRgQMULq+gtrN/FvJYtH0xNN8g==", "signatures": [{"sig": "MEYCIQClv7Us5FIEFyUBhxCK+ywOieBqm0FtEW09MwWG5/YdfgIhAPPzYy55FZPFEqGx08AsVkzG+NVndU6wY55AHvgRJRxI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQGRgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRYA/+MSJQAZduAICNqYoelfK4hPlWPeNBraHtoTD3xPgVNUJqicVV\r\nh31D/d8BtE4IAKqjRFH1uhgXtNKnSeSjjuhMeiMzQZQ2j5BXCe5b5I3taulz\r\noRobsVxQmwVu8x4LL9gosh1ciNNaVxGAJS2oqRMenkO6Qhi5bJ9l7+W3oZ9u\r\nrcq6b4zcmLc4yuU/QYWfGmTcX1gROmEkJs5Z1iyzkvVyIg/Jg+g1EkK03ZJa\r\n5p2a1WwfDzvNA9ZTsLjcYcbHkHMAlHZ3CarVTM2GLG1DfHONsjME7CdOkMGr\r\nJt7r47et15F2ur8mTBigQysqRYQuhDlO5NR499Sj+LK5fc9yJu7TZlhRjk78\r\nqiPP0TVGAhCdHn3sG9uqm6LdQ8zsO5jcSr9PGLbCETCYd8Wb/R5310v3s8mm\r\njfwCjvkp0HaoukUWlDVHnObYd5mwoi3jjxcMlbbyCQet4cDG2CyhvvcC84ER\r\nToJ1E8hrCzvwECDNu7hFUPR7V0vwWa3YarD/VXqXCw6o9U8PIyMFKlFG8DaW\r\n7TyDW9R80KpG5T/2PIVWhGDciAWujkR9uhpFs2n+kol3PzT5V0gjOdeOO2FF\r\nD+AbHxKySHTaIFJK/UYYvY52hbrcAGX3dZTG2VH1DhpvbnZ3zm+jHouoFE33\r\n+gyfEijDbD+RFz5wZZhEA55Z4Fh11IpWGw0=\r\n=P0dZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.0": {"name": "lightningcss", "version": "1.21.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.0", "lightningcss-darwin-arm64": "1.21.0", "lightningcss-linux-x64-gnu": "1.21.0", "lightningcss-linux-x64-musl": "1.21.0", "lightningcss-win32-x64-msvc": "1.21.0", "lightningcss-linux-arm64-gnu": "1.21.0", "lightningcss-linux-arm64-musl": "1.21.0", "lightningcss-linux-arm-gnueabihf": "1.21.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.0", "lightningcss-darwin-arm64": "1.21.0", "lightningcss-linux-x64-gnu": "1.21.0", "lightningcss-linux-x64-musl": "1.21.0", "lightningcss-win32-x64-msvc": "1.21.0", "lightningcss-linux-arm64-gnu": "1.21.0", "lightningcss-linux-arm64-musl": "1.21.0", "lightningcss-linux-arm-gnueabihf": "1.21.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "31ebf4717f42e801e622186f28cd58db7c914ef7", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.0.tgz", "fileCount": 14, "integrity": "sha512-HDznZexdDMvC98c79vRE+oW5vFncTlLjJopzK4azReOilq6n4XIscCMhvgiXkstYMM/dCe6FJw0oed06ck8AtA==", "signatures": [{"sig": "MEUCIQDNQJF6k7KUQKdshyV59ACXYbXsLMp/ASCXK7Ct6uKtHwIgTviHL4IgPtPwyzmJePTpXvW7vXGpHoypZwd2Yn2LQd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467632}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.1": {"name": "lightningcss", "version": "1.21.1", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.1", "lightningcss-darwin-arm64": "1.21.1", "lightningcss-linux-x64-gnu": "1.21.1", "lightningcss-linux-x64-musl": "1.21.1", "lightningcss-win32-x64-msvc": "1.21.1", "lightningcss-linux-arm64-gnu": "1.21.1", "lightningcss-linux-arm64-musl": "1.21.1", "lightningcss-linux-arm-gnueabihf": "1.21.1"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.1", "lightningcss-darwin-arm64": "1.21.1", "lightningcss-linux-x64-gnu": "1.21.1", "lightningcss-linux-x64-musl": "1.21.1", "lightningcss-win32-x64-msvc": "1.21.1", "lightningcss-linux-arm64-gnu": "1.21.1", "lightningcss-linux-arm64-musl": "1.21.1", "lightningcss-linux-arm-gnueabihf": "1.21.1"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "4d1f87a08db839009af1a24fd7cc13bd24a5acb5", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.1.tgz", "fileCount": 14, "integrity": "sha512-TKkVZzKnJVtGLI+8QMXLH2JdNcxjodA06So+uXA5qelvuReKvPyCJBX/6ZznADA76zNijmDc3OhjxvTBmNtCoA==", "signatures": [{"sig": "MEUCIAoj2cF0AL4SJiuEMDMwaCecg5AIpkrvN5NS7YkzU6upAiEAhvppftC8rxiZ8Hz0cQMqLUEDTV2qU9l8GM2KzUDcslQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467632}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.2": {"name": "lightningcss", "version": "1.21.2", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.2", "lightningcss-darwin-arm64": "1.21.2", "lightningcss-linux-x64-gnu": "1.21.2", "lightningcss-linux-x64-musl": "1.21.2", "lightningcss-win32-x64-msvc": "1.21.2", "lightningcss-linux-arm64-gnu": "1.21.2", "lightningcss-linux-arm64-musl": "1.21.2", "lightningcss-linux-arm-gnueabihf": "1.21.2"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.2", "lightningcss-darwin-arm64": "1.21.2", "lightningcss-linux-x64-gnu": "1.21.2", "lightningcss-linux-x64-musl": "1.21.2", "lightningcss-win32-x64-msvc": "1.21.2", "lightningcss-linux-arm64-gnu": "1.21.2", "lightningcss-linux-arm64-musl": "1.21.2", "lightningcss-linux-arm-gnueabihf": "1.21.2"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "dd64ba83584560f10dedb195fbca70b8fd85890e", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.2.tgz", "fileCount": 14, "integrity": "sha512-6GoGcH4CHGLN6hq5lcmtkKxolXw8nsmgoaYsy6AilwH0vS0GUpKlgFegaf7LmDZqxawjV6LmymQFBZYe+sS45w==", "signatures": [{"sig": "MEYCIQD2k/UX+DDN8bKnYwt0S8YoMWU/CZmLi8EPM1KgjzJCaQIhALd8CSHNeL1Q64Oy11S2UBfZolBhxwP4LYE74EldbNh2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467655}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.3": {"name": "lightningcss", "version": "1.21.3", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.3", "lightningcss-darwin-arm64": "1.21.3", "lightningcss-linux-x64-gnu": "1.21.3", "lightningcss-linux-x64-musl": "1.21.3", "lightningcss-win32-x64-msvc": "1.21.3", "lightningcss-linux-arm64-gnu": "1.21.3", "lightningcss-linux-arm64-musl": "1.21.3", "lightningcss-linux-arm-gnueabihf": "1.21.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.3", "lightningcss-darwin-arm64": "1.21.3", "lightningcss-linux-x64-gnu": "1.21.3", "lightningcss-linux-x64-musl": "1.21.3", "lightningcss-win32-x64-msvc": "1.21.3", "lightningcss-linux-arm64-gnu": "1.21.3", "lightningcss-linux-arm64-musl": "1.21.3", "lightningcss-linux-arm-gnueabihf": "1.21.3"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "cfa1b1312b600f1d326fa89207097026c0994c97", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.3.tgz", "fileCount": 14, "integrity": "sha512-15I4wc2jLMpzP4OW71UIR1PmRlOliW6ol2EQQTz1kgG7Y8uKo+c7MUMWhBCO1GCmv4G/YHZlOq5Iw3PyhylgUw==", "signatures": [{"sig": "MEQCIFZyY+LTocbodhrXmeRQLXLkNsW6sZzY82ImsTdXOKJ1AiBZScp50wsJcgrShxtEbaBO+MhrSHexMo2guDCdZbW3xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467680}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.4": {"name": "lightningcss", "version": "1.21.4", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.4", "lightningcss-darwin-arm64": "1.21.4", "lightningcss-linux-x64-gnu": "1.21.4", "lightningcss-linux-x64-musl": "1.21.4", "lightningcss-win32-x64-msvc": "1.21.4", "lightningcss-linux-arm64-gnu": "1.21.4", "lightningcss-linux-arm64-musl": "1.21.4", "lightningcss-linux-arm-gnueabihf": "1.21.4"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.4", "lightningcss-darwin-arm64": "1.21.4", "lightningcss-linux-x64-gnu": "1.21.4", "lightningcss-linux-x64-musl": "1.21.4", "lightningcss-win32-x64-msvc": "1.21.4", "lightningcss-linux-arm64-gnu": "1.21.4", "lightningcss-linux-arm64-musl": "1.21.4", "lightningcss-linux-arm-gnueabihf": "1.21.4"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "f6e9c4cddef5cfd950d02126246d0bc44aef0e0b", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.4.tgz", "fileCount": 14, "integrity": "sha512-/MhQpBRQgs5j35eC1ZMqOOQqDZyNCUKZRoMp3ftprEOb4vVkjfe1VoknD9BKskCT+bbqBf3Yt9XglJLXXZ+pfA==", "signatures": [{"sig": "MEUCIQClT9ofrih+f+LTtvyte0ttXU0UPLOgoaNPc/ySkAfEoQIgP2tdb+05De9B6U5+pNHbWT8VHsUwBOmLFFy5KnX/ryk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467632}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.5": {"name": "lightningcss", "version": "1.21.5", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.5", "lightningcss-darwin-arm64": "1.21.5", "lightningcss-linux-x64-gnu": "1.21.5", "lightningcss-linux-x64-musl": "1.21.5", "lightningcss-win32-x64-msvc": "1.21.5", "lightningcss-linux-arm64-gnu": "1.21.5", "lightningcss-linux-arm64-musl": "1.21.5", "lightningcss-linux-arm-gnueabihf": "1.21.5"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.5", "lightningcss-darwin-arm64": "1.21.5", "lightningcss-linux-x64-gnu": "1.21.5", "lightningcss-linux-x64-musl": "1.21.5", "lightningcss-win32-x64-msvc": "1.21.5", "lightningcss-linux-arm64-gnu": "1.21.5", "lightningcss-linux-arm64-musl": "1.21.5", "lightningcss-linux-arm-gnueabihf": "1.21.5"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "1a175329048912f6480c1703ff2957aad0dcedb4", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.5.tgz", "fileCount": 14, "integrity": "sha512-/pEUPeih2EwIx9n4T82aOG6CInN83tl/mWlw6B5gWLf36UplQi1L+5p3FUHsdt4fXVfOkkh9KIaM3owoq7ss8A==", "signatures": [{"sig": "MEUCIQCv4F6JagsJ0pt+xd7T5Q/o5KM7MutIUUq1nDmPtpBRmQIgIBZ0qJiT08Rs7hZTVOHfk3pZBquvMHC8C28bGtegqAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 467632}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.6": {"name": "lightningcss", "version": "1.21.6", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.6", "lightningcss-freebsd-x64": "1.21.6", "lightningcss-darwin-arm64": "1.21.6", "lightningcss-linux-x64-gnu": "1.21.6", "lightningcss-linux-x64-musl": "1.21.6", "lightningcss-win32-x64-msvc": "1.21.6", "lightningcss-linux-arm64-gnu": "1.21.6", "lightningcss-linux-arm64-musl": "1.21.6", "lightningcss-linux-arm-gnueabihf": "1.21.6"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.6", "lightningcss-freebsd-x64": "1.21.6", "lightningcss-darwin-arm64": "1.21.6", "lightningcss-linux-x64-gnu": "1.21.6", "lightningcss-linux-x64-musl": "1.21.6", "lightningcss-win32-x64-msvc": "1.21.6", "lightningcss-linux-arm64-gnu": "1.21.6", "lightningcss-linux-arm64-musl": "1.21.6", "lightningcss-linux-arm-gnueabihf": "1.21.6"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "fc98c0e4c9d1b47f05cf7ac426823d7f71f81590", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.6.tgz", "fileCount": 14, "integrity": "sha512-6J1/sHNgeLDiq0x1uPwKNLP4VWxdynH4+ewmJX28ivF4x/EmBAcBtIKkDGWZKgpy2Zq6FhgVWRWG7pf+fWybHw==", "signatures": [{"sig": "MEUCIA2Wypr9iEyxTxz4SWbnlzvnxtAcswgN6ogI2dPqgUUNAiEAo6xlkajShVHrtnOtop0UMgPdS5yn5+O/xEXT7fLeacw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470890}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.7": {"name": "lightningcss", "version": "1.21.7", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.7", "lightningcss-freebsd-x64": "1.21.7", "lightningcss-darwin-arm64": "1.21.7", "lightningcss-linux-x64-gnu": "1.21.7", "lightningcss-linux-x64-musl": "1.21.7", "lightningcss-win32-x64-msvc": "1.21.7", "lightningcss-linux-arm64-gnu": "1.21.7", "lightningcss-linux-arm64-musl": "1.21.7", "lightningcss-linux-arm-gnueabihf": "1.21.7"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.7", "lightningcss-freebsd-x64": "1.21.7", "lightningcss-darwin-arm64": "1.21.7", "lightningcss-linux-x64-gnu": "1.21.7", "lightningcss-linux-x64-musl": "1.21.7", "lightningcss-win32-x64-msvc": "1.21.7", "lightningcss-linux-arm64-gnu": "1.21.7", "lightningcss-linux-arm64-musl": "1.21.7", "lightningcss-linux-arm-gnueabihf": "1.21.7"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "9b864625a4ad734aeaecb28649c20705e335e199", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.7.tgz", "fileCount": 14, "integrity": "sha512-xITZyh5sLFwRPYUSw15T00Rm7gcQ1qOPuQwNOcvHsTm6nLWTQ723w7zl42wrC5t+xtdg6FPmnXHml1nZxxvp1w==", "signatures": [{"sig": "MEQCICVbE3SqKsicKND4/xvPHTJoik7f3Cmr6Zi9yIFVlH5AAiBNgjPDgj6IJnoIPkwmUibvG1rLtKZJ9fyVRWH22aQamw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470890}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.21.8": {"name": "lightningcss", "version": "1.21.8", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.21.8", "lightningcss-freebsd-x64": "1.21.8", "lightningcss-darwin-arm64": "1.21.8", "lightningcss-linux-x64-gnu": "1.21.8", "lightningcss-linux-x64-musl": "1.21.8", "lightningcss-win32-x64-msvc": "1.21.8", "lightningcss-linux-arm64-gnu": "1.21.8", "lightningcss-linux-arm64-musl": "1.21.8", "lightningcss-linux-arm-gnueabihf": "1.21.8"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.21.8", "lightningcss-freebsd-x64": "1.21.8", "lightningcss-darwin-arm64": "1.21.8", "lightningcss-linux-x64-gnu": "1.21.8", "lightningcss-linux-x64-musl": "1.21.8", "lightningcss-win32-x64-msvc": "1.21.8", "lightningcss-linux-arm64-gnu": "1.21.8", "lightningcss-linux-arm64-musl": "1.21.8", "lightningcss-linux-arm-gnueabihf": "1.21.8"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "a02e4a8979208ffb61d7c6deebb75c4abce0b5d6", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.21.8.tgz", "fileCount": 14, "integrity": "sha512-jEqaL7m/ZckZJjlMAfycr1Kpz7f93k6n7KGF5SJjuPSm6DWI6h3ayLZmgRHgy1OfrwoCed6h4C/gHYPOd1OFMA==", "signatures": [{"sig": "MEYCIQDyD5c/zsCUR2vjh5XUqLNz1ctHUpTlNx6YxjvcJPI1PwIhAMIp0XedR27LXxSB1wiE6X4XUwCuKD0urPINkWxa+CHC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 471114}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.22.0": {"name": "lightningcss", "version": "1.22.0", "dependencies": {"detect-libc": "^1.0.3", "lightningcss-darwin-x64": "1.22.0", "lightningcss-freebsd-x64": "1.22.0", "lightningcss-darwin-arm64": "1.22.0", "lightningcss-linux-x64-gnu": "1.22.0", "lightningcss-linux-x64-musl": "1.22.0", "lightningcss-win32-x64-msvc": "1.22.0", "lightningcss-linux-arm64-gnu": "1.22.0", "lightningcss-linux-arm64-musl": "1.22.0", "lightningcss-linux-arm-gnueabihf": "1.22.0"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.22.0", "lightningcss-freebsd-x64": "1.22.0", "lightningcss-darwin-arm64": "1.22.0", "lightningcss-linux-x64-gnu": "1.22.0", "lightningcss-linux-x64-musl": "1.22.0", "lightningcss-win32-x64-msvc": "1.22.0", "lightningcss-linux-arm64-gnu": "1.22.0", "lightningcss-linux-arm64-musl": "1.22.0", "lightningcss-linux-arm-gnueabihf": "1.22.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.14", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.2.49", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "76c9a17925e660741858e88b774172cb1923bb4a", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.22.0.tgz", "fileCount": 14, "integrity": "sha512-+z0qvwRVzs4XGRXelnWRNwqsXUx8k3bSkbP8vD42kYKSk3z9OM2P3e/gagT7ei/gwh8DTS80LZOFZV6lm8Z8Fg==", "signatures": [{"sig": "MEUCIGkeUYDnIc+FnkB6llAbtV2C7/NX1V9QhdCRcieEeyrdAiEA4U47dmBiK8OLU0OJI1OaqDNg8rM9GFawZJ9pB8tHTN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 472252}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.22.1": {"name": "lightningcss", "version": "1.22.1", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.22.1", "lightningcss-freebsd-x64": "1.22.1", "lightningcss-darwin-arm64": "1.22.1", "lightningcss-linux-x64-gnu": "1.22.1", "lightningcss-linux-x64-musl": "1.22.1", "lightningcss-win32-x64-msvc": "1.22.1", "lightningcss-linux-arm64-gnu": "1.22.1", "lightningcss-linux-arm64-musl": "1.22.1", "lightningcss-linux-arm-gnueabihf": "1.22.1"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.13.10", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.16", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.3.29", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "8108ddecb2e859032bdd99908abd2b37515b1750", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.22.1.tgz", "fileCount": 14, "integrity": "sha512-Fy45PhibiNXkm0cK5FJCbfO8Y6jUpD/YcHf/BtuI+jvYYqSXKF4muk61jjE8YxCR9y+hDYIWSzHTc+bwhDE6rQ==", "signatures": [{"sig": "MEUCIQC6Ig5XqW1tATPIjcNbDd8dMiUjyHtv98C4NPq05/Rn0QIgRguyU2SoomnEAOMADT2BqKSFTAqqnMUIZZCgT8DXLF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 472252}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.23.0": {"name": "lightningcss", "version": "1.23.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.23.0", "lightningcss-freebsd-x64": "1.23.0", "lightningcss-darwin-arm64": "1.23.0", "lightningcss-linux-x64-gnu": "1.23.0", "lightningcss-linux-x64-musl": "1.23.0", "lightningcss-win32-x64-msvc": "1.23.0", "lightningcss-linux-arm64-gnu": "1.23.0", "lightningcss-linux-arm64-musl": "1.23.0", "lightningcss-linux-arm-gnueabihf": "1.23.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.16", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "^5.3.29", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "58c94a533d02d8416d4f2ec9ab87641f61943c78", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.23.0.tgz", "fileCount": 14, "integrity": "sha512-SEArWKMHhqn/0QzOtclIwH5pXIYQOUEkF8DgICd/105O+GCgd7jxjNod/QPnBCSWvpRHQBGVz5fQ9uScby03zA==", "signatures": [{"sig": "MEYCIQD3BnZDYk5Y/Cmvy3xseL2q2EPlzLRazwOokXW2kTfwBQIhALaBd6KswsuDNplVQeKkcr2pI+7E+s4WgmYkEJjo20Nb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477293}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.24.0": {"name": "lightningcss", "version": "1.24.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.24.0", "lightningcss-freebsd-x64": "1.24.0", "lightningcss-darwin-arm64": "1.24.0", "lightningcss-linux-x64-gnu": "1.24.0", "lightningcss-linux-x64-musl": "1.24.0", "lightningcss-win32-x64-msvc": "1.24.0", "lightningcss-linux-arm64-gnu": "1.24.0", "lightningcss-linux-arm64-musl": "1.24.0", "lightningcss-linux-arm-gnueabihf": "1.24.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.17", "caniuse-lite": "^1.0.30001585", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.5.0", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "41bcdf5de381ae5fe1e7f9a2bf6bcaded556082c", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.24.0.tgz", "fileCount": 14, "integrity": "sha512-y36QEEDVx4IM7/yIZNsZJMRREIu26WzTsauIysf5s76YeCmlSbRZS7aC97IGPuoFRnyZ5Wx43OBsQBFB5Ne7ng==", "signatures": [{"sig": "MEUCIQDp0eOFWeLqPlk3tL6RGkqlNdFG8tZl8PQg4Ov0TZP1uQIgdg7IpdRnuJMdl8DbzchdU/o8PnW0SYe8Qw+wGykT9eM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480094}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.24.1": {"name": "lightningcss", "version": "1.24.1", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.24.1", "lightningcss-freebsd-x64": "1.24.1", "lightningcss-darwin-arm64": "1.24.1", "lightningcss-linux-x64-gnu": "1.24.1", "lightningcss-linux-x64-musl": "1.24.1", "lightningcss-win32-x64-msvc": "1.24.1", "lightningcss-linux-arm64-gnu": "1.24.1", "lightningcss-linux-arm64-musl": "1.24.1", "lightningcss-linux-arm-gnueabihf": "1.24.1"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.17", "caniuse-lite": "^1.0.30001585", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.5.0", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "8b86a5ee6e6ae9e035ff92892bd047b8d687581e", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.24.1.tgz", "fileCount": 14, "integrity": "sha512-kUpHOLiH5GB0ERSv4pxqlL0RYKnOXtgGtVe7shDGfhS0AZ4D1ouKFYAcLcZhql8aMspDNzaUCumGHZ78tb2fTg==", "signatures": [{"sig": "MEYCIQDgU/zO8Hmdc/h1T/aDoNUOXQcwNWC9l9N8eUiniffamwIhALtzCFBF37ZohaSlUAvX3XE7fNljWF1VIYPd+EH8/FLN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480081}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.25.0": {"name": "lightningcss", "version": "1.25.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.25.0", "lightningcss-freebsd-x64": "1.25.0", "lightningcss-darwin-arm64": "1.25.0", "lightningcss-linux-x64-gnu": "1.25.0", "lightningcss-linux-x64-musl": "1.25.0", "lightningcss-win32-x64-msvc": "1.25.0", "lightningcss-linux-arm64-gnu": "1.25.0", "lightningcss-linux-arm64-musl": "1.25.0", "lightningcss-linux-arm-gnueabihf": "1.25.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.19", "caniuse-lite": "^1.0.30001620", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.5.28", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "6d2409ebe90c60bf420499fadb3ac72e81df44b0", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.25.0.tgz", "fileCount": 14, "integrity": "sha512-B08o6QQikGaY4rPuQohtFVE+X2++mm/QemwAJ/1sgnMgTwwUnafJbTmSSBWC8Tv4JPfhelXZB6sWA0Y/6eYJmQ==", "signatures": [{"sig": "MEQCIA3BTUoU4TtFdgfIsKD6uZ8awN9BK6yt9dC2lERtre9EAiBt8nEtgAouB/c+71mnkNVqDgqTh+J8kPXugSRo9xIi/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486606}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.25.1": {"name": "lightningcss", "version": "1.25.1", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.25.1", "lightningcss-freebsd-x64": "1.25.1", "lightningcss-darwin-arm64": "1.25.1", "lightningcss-linux-x64-gnu": "1.25.1", "lightningcss-linux-x64-musl": "1.25.1", "lightningcss-win32-x64-msvc": "1.25.1", "lightningcss-linux-arm64-gnu": "1.25.1", "lightningcss-linux-arm64-musl": "1.25.1", "lightningcss-linux-arm-gnueabihf": "1.25.1"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.19", "caniuse-lite": "^1.0.30001620", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.5.28", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "6136c166ac61891fbc1af7fba7b620c50f58fb2d", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.25.1.tgz", "fileCount": 14, "integrity": "sha512-V0RMVZzK1+rCHpymRv4URK2lNhIRyO8g7U7zOFwVAhJuat74HtkjIQpQRKNCwFEYkRGpafOpmXXLoaoBcyVtBg==", "signatures": [{"sig": "MEUCIQDQ7Ljx+GdemS3JSWkkf1oPSLQKrFUb0tpMze7F1H027AIgdl0j768tFOU8c9e3vNooF/k3JFj9PttyzDzCzBTs4Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486606}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.26.0": {"name": "lightningcss", "version": "1.26.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.26.0", "lightningcss-freebsd-x64": "1.26.0", "lightningcss-darwin-arm64": "1.26.0", "lightningcss-linux-x64-gnu": "1.26.0", "lightningcss-linux-x64-musl": "1.26.0", "lightningcss-win32-x64-msvc": "1.26.0", "lightningcss-linux-arm64-gnu": "1.26.0", "lightningcss-linux-arm64-musl": "1.26.0", "lightningcss-win32-arm64-msvc": "1.26.0", "lightningcss-linux-arm-gnueabihf": "1.26.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001649", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.5.44", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "ca2ff6ebabb044c6e49e57f9491a6f85db03b256", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.26.0.tgz", "fileCount": 14, "integrity": "sha512-a/XZ5hdgifrofQJUArr5AiJjx26SwMam3SJUSMjgebZbESZ96i+6Qsl8tLi0kaUsdMzBWXh9sN1Oe6hp2/dkQw==", "signatures": [{"sig": "MEQCIBhmenwb22sMuaCisr59mXna/UWZsebXhH5nk3Qf7qm7AiA/Gm90pkqDeFVRdFva4dU58BdwYT1AheHhEc6j3WwgfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 491160}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.27.0": {"name": "lightningcss", "version": "1.27.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.27.0", "lightningcss-freebsd-x64": "1.27.0", "lightningcss-darwin-arm64": "1.27.0", "lightningcss-linux-x64-gnu": "1.27.0", "lightningcss-linux-x64-musl": "1.27.0", "lightningcss-win32-x64-msvc": "1.27.0", "lightningcss-linux-arm64-gnu": "1.27.0", "lightningcss-linux-arm64-musl": "1.27.0", "lightningcss-win32-arm64-msvc": "1.27.0", "lightningcss-linux-arm-gnueabihf": "1.27.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001660", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.5.51", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "d4608e63044343836dd9769f6c8b5d607867649a", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.27.0.tgz", "fileCount": 14, "integrity": "sha512-8f7aNmS1+etYSLHht0fQApPc2kNO8qGRutifN5rVIc6Xo6ABsEbqOr758UwI7ALVbTt4x1fllKt0PYgzD9S3yQ==", "signatures": [{"sig": "MEUCIQCFSo/YdAOwZieRkUAOMJZ/rEQhy2wZtCE54scDHuTibQIgHTg/n/EXPl6sgaLfVcgRvw3gPA9LNABp+1thJAUCWBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 491858}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.28.0": {"name": "lightningcss", "version": "1.28.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.28.0", "lightningcss-freebsd-x64": "1.28.0", "lightningcss-darwin-arm64": "1.28.0", "lightningcss-linux-x64-gnu": "1.28.0", "lightningcss-linux-x64-musl": "1.28.0", "lightningcss-win32-x64-msvc": "1.28.0", "lightningcss-linux-arm64-gnu": "1.28.0", "lightningcss-linux-arm64-musl": "1.28.0", "lightningcss-win32-arm64-msvc": "1.28.0", "lightningcss-linux-arm-gnueabihf": "1.28.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001677", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.6.12", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "d91231a8653a1388e57051faf602e5d580572d54", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.28.0.tgz", "fileCount": 14, "integrity": "sha512-wKfbUqWC+/vM29bxFlfuVHlhSRg8dNKhA5RjuoodLpZx32uozV3+z0SAXLHCynbBOiann/VVWOIyxP1QZs/GLA==", "signatures": [{"sig": "MEUCIGGUtDjn+mGfP3YBBv38/Vm2y2ejYQkSVES9Hvxe3VuHAiEAu1dlp25UFf/81BV5+0hi9HlHoR/iwxDfolG+Kfo7Ufc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492062}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.28.1": {"name": "lightningcss", "version": "1.28.1", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.28.1", "lightningcss-freebsd-x64": "1.28.1", "lightningcss-darwin-arm64": "1.28.1", "lightningcss-linux-x64-gnu": "1.28.1", "lightningcss-linux-x64-musl": "1.28.1", "lightningcss-win32-x64-msvc": "1.28.1", "lightningcss-linux-arm64-gnu": "1.28.1", "lightningcss-linux-arm64-musl": "1.28.1", "lightningcss-win32-arm64-msvc": "1.28.1", "lightningcss-linux-arm-gnueabihf": "1.28.1"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001677", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.6.12", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "311b44052e4dcb17e31929a584a9a68864a456ed", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.28.1.tgz", "fileCount": 14, "integrity": "sha512-KRDkHlLlNj3DWh79CDt93fPlRJh2W1AuHV0ZSZAMMuN7lqlsZTV5842idfS1urWG8q9tc17velp1gCXhY7sLnQ==", "signatures": [{"sig": "MEYCIQDMelfmWBWCF1l6vx9xMD9s7kJveuuugBOzmairvQwhEgIhAKt8C2+n8yqg+AFBkoZBVo8ioaTF5zfO6UDrYFyj322i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492062}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.28.2": {"name": "lightningcss", "version": "1.28.2", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.28.2", "lightningcss-freebsd-x64": "1.28.2", "lightningcss-darwin-arm64": "1.28.2", "lightningcss-linux-x64-gnu": "1.28.2", "lightningcss-linux-x64-musl": "1.28.2", "lightningcss-win32-x64-msvc": "1.28.2", "lightningcss-linux-arm64-gnu": "1.28.2", "lightningcss-linux-arm64-musl": "1.28.2", "lightningcss-win32-arm64-msvc": "1.28.2", "lightningcss-linux-arm-gnueabihf": "1.28.2"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.31.1", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^5.0.8", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001684", "@babel/parser": "^7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "^7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.6.18", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "cc26fad9ad64a621bd39ac6248095891cf584cce", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.28.2.tgz", "fileCount": 14, "integrity": "sha512-ePLRrbt3fgjXI5VFZOLbvkLD5ZRuxGKm+wJ3ujCqBtL3NanDHPo/5zicR5uEKAPiIjBYF99BM4K4okvMznjkVA==", "signatures": [{"sig": "MEUCIFbmFspnn2OklwheLQQEuibLaVEs5QutV+s8CNYOZtOrAiEAwO/7sOWu0X8kktTXq8jn4PNtqRWMvnPnaj3BcGt6yF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492062}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.29.0": {"name": "lightningcss", "version": "1.29.0", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.0", "lightningcss-freebsd-x64": "1.29.0", "lightningcss-darwin-arm64": "1.29.0", "lightningcss-linux-x64-gnu": "1.29.0", "lightningcss-linux-x64-musl": "1.29.0", "lightningcss-win32-x64-msvc": "1.29.0", "lightningcss-linux-arm64-gnu": "1.29.0", "lightningcss-linux-arm64-musl": "1.29.0", "lightningcss-win32-arm64-msvc": "1.29.0", "lightningcss-linux-arm-gnueabihf": "1.29.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.33.5", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "typescript": "^5.7.2", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001690", "@babel/parser": "7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.6.26", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "f7b376fbbe8367091a09529ad48e715ff09ad98c", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.29.0.tgz", "fileCount": 14, "integrity": "sha512-4BkG0a3g6ZJ5F5iQ22lZsod0KJrIzYdbiBCMo9FgpDrxCx+uUapwkZmUVgisiZvHUj53BJGQiWWT+YXr1ZcZQQ==", "signatures": [{"sig": "MEUCIQDcykaNDaIwL06iie2oCMN5lH5TGjAmcpNz+9Fga4EXJwIgUqK3czyX/Lg0ZNLzK/KWhTM3gUqj4yoDuzx1glZgdks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 499892}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.29.1": {"name": "lightningcss", "version": "1.29.1", "dependencies": {"detect-libc": "^1.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.1", "lightningcss-freebsd-x64": "1.29.1", "lightningcss-darwin-arm64": "1.29.1", "lightningcss-linux-x64-gnu": "1.29.1", "lightningcss-linux-x64-musl": "1.29.1", "lightningcss-win32-x64-msvc": "1.29.1", "lightningcss-linux-arm64-gnu": "1.29.1", "lightningcss-linux-arm64-musl": "1.29.1", "lightningcss-win32-arm64-msvc": "1.29.1", "lightningcss-linux-arm-gnueabihf": "1.29.1"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.33.5", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "typescript": "^5.7.2", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001690", "@babel/parser": "7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.6.26", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "1d4d62332fc5ba4b6c28e04a8c5638c76019702b", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.29.1.tgz", "fileCount": 14, "integrity": "sha512-FmGoeD4S05ewj+AkhTY+D+myDvXI6eL27FjHIjoyUkO/uw7WZD1fBVs0QxeYWa7E17CUHJaYX/RUGISCtcrG4Q==", "signatures": [{"sig": "MEYCIQCS8R4BqqAQrIOfoQPBtA0BqISGYXtXhZXxCRQQY14s5QIhALSAJe2NaOGSTv7yyo+1TQ+0WXYTSvEEa7xWj8vCDewC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 499892}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.29.2": {"name": "lightningcss", "version": "1.29.2", "dependencies": {"detect-libc": "^2.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.2", "lightningcss-freebsd-x64": "1.29.2", "lightningcss-darwin-arm64": "1.29.2", "lightningcss-linux-x64-gnu": "1.29.2", "lightningcss-linux-x64-musl": "1.29.2", "lightningcss-win32-x64-msvc": "1.29.2", "lightningcss-linux-arm64-gnu": "1.29.2", "lightningcss-linux-arm64-musl": "1.29.2", "lightningcss-win32-arm64-msvc": "1.29.2", "lightningcss-linux-arm-gnueabihf": "1.29.2"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.33.5", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "typescript": "^5.7.2", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.20", "caniuse-lite": "^1.0.30001702", "@babel/parser": "7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.7.0", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "f5f0fd6e63292a232697e6fe709da5b47624def3", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.29.2.tgz", "fileCount": 14, "integrity": "sha512-6b6gd/RUXKaw5keVdSEtqFVdzWnU5jMxTUjA2bVcMNPLwSQ08Sv/UodBVtETLCn7k4S1Ibxwh7k68IwLZPgKaA==", "signatures": [{"sig": "MEUCIQDC+NKc9nztmk797hhpOESJGm+VJCLZKrwusiiNvFIHKAIgfb8MpadVCALVoI90o8/KxKZ9YWDIJhN526OUPLhsIDo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 500013}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.29.3": {"name": "lightningcss", "version": "1.29.3", "dependencies": {"detect-libc": "^2.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.29.3", "lightningcss-freebsd-x64": "1.29.3", "lightningcss-darwin-arm64": "1.29.3", "lightningcss-linux-x64-gnu": "1.29.3", "lightningcss-linux-x64-musl": "1.29.3", "lightningcss-win32-x64-msvc": "1.29.3", "lightningcss-linux-arm64-gnu": "1.29.3", "lightningcss-linux-arm64-musl": "1.29.3", "lightningcss-win32-arm64-msvc": "1.29.3", "lightningcss-linux-arm-gnueabihf": "1.29.3"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.33.5", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "typescript": "^5.7.2", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001704", "@babel/parser": "7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~5.7.3", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "ddb8c6367f6d63432a4e81278421f2a9b3ac6efb", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.29.3.tgz", "fileCount": 14, "integrity": "sha512-GlOJwTIP6TMIlrTFsxTerwC0W6OpQpCGuX1ECRLBUVRh6fpJH3xTqjCjRgQHTb4ZXexH9rtHou1Lf03GKzmhhQ==", "signatures": [{"sig": "MEUCIQDC16fbDV7AWQhq3NbDIo7YbZv8ZqeiWulzke9GGgDLbAIgY4NykSvTMshOwXKEWLH2+mYUX2hXHXTeAuEDU7p3py8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 500181}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.30.0": {"name": "lightningcss", "version": "1.30.0", "dependencies": {"detect-libc": "^2.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.30.0", "lightningcss-freebsd-x64": "1.30.0", "lightningcss-darwin-arm64": "1.30.0", "lightningcss-linux-x64-gnu": "1.30.0", "lightningcss-linux-x64-musl": "1.30.0", "lightningcss-win32-x64-msvc": "1.30.0", "lightningcss-linux-arm64-gnu": "1.30.0", "lightningcss-linux-arm64-musl": "1.30.0", "lightningcss-win32-arm64-msvc": "1.30.0", "lightningcss-linux-arm-gnueabihf": "1.30.0"}, "devDependencies": {"uvu": "^0.5.6", "util": "^0.12.4", "sharp": "^0.33.5", "parcel": "^2.8.2", "recast": "^0.22.0", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "postcss": "^8.3.11", "process": "^0.11.10", "jest-diff": "^27.4.2", "napi-wasm": "^1.0.1", "puppeteer": "^12.0.1", "codemirror": "^6.0.1", "node-fetch": "^3.1.0", "typescript": "^5.7.2", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001717", "@babel/parser": "7.21.4", "patch-package": "^6.5.0", "posthtml-prism": "^1.0.4", "@babel/traverse": "7.21.4", "path-browserify": "^1.0.1", "@codemirror/lint": "^6.1.0", "posthtml-include": "^1.7.4", "markdown-it-prism": "^2.3.0", "markdown-it-anchor": "^8.6.6", "posthtml-markdownit": "^1.3.1", "@codemirror/lang-css": "^6.0.1", "@mdn/browser-compat-data": "~6.0.13", "json-schema-to-typescript": "^11.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@codemirror/lang-javascript": "^6.1.2", "markdown-it-table-of-contents": "^0.6.0"}, "dist": {"shasum": "aa12c41502c3c8c80d1bed6829930e5d7218a812", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.0.tgz", "fileCount": 14, "integrity": "sha512-uuurN2onfoNwQtaWnX9UYLz6DlZHnUd88SceOXDAQzQ5+FJ+ELPgcC/EVtRJoFOveXe44zRE+foh2KMD/vQxqQ==", "signatures": [{"sig": "MEYCIQDDzgRTl1sELNndPulK/84O2c9j20lEkNRnc6rvopZuMAIhAKuAjRbWEae24/zBuqNnV4uXJYGyO1IVcTw0JD2qSEBP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 501111}, "engines": {"node": ">= 12.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "1.30.1": {"name": "lightningcss", "version": "1.30.1", "dependencies": {"detect-libc": "^2.0.3"}, "optionalDependencies": {"lightningcss-darwin-x64": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-darwin-arm64": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-freebsd-x64": "1.30.1"}, "devDependencies": {"@babel/parser": "7.21.4", "@babel/traverse": "7.21.4", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.1.2", "@codemirror/lint": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.0", "@mdn/browser-compat-data": "~6.0.13", "@napi-rs/cli": "^2.14.0", "autoprefixer": "^10.4.21", "caniuse-lite": "^1.0.30001717", "codemirror": "^6.0.1", "cssnano": "^7.0.6", "esbuild": "^0.19.8", "flowgen": "^1.21.0", "jest-diff": "^27.4.2", "json-schema-to-typescript": "^11.0.2", "markdown-it-anchor": "^8.6.6", "markdown-it-prism": "^2.3.0", "markdown-it-table-of-contents": "^0.6.0", "napi-wasm": "^1.0.1", "node-fetch": "^3.1.0", "parcel": "^2.8.2", "patch-package": "^6.5.0", "path-browserify": "^1.0.1", "postcss": "^8.3.11", "posthtml-include": "^1.7.4", "posthtml-markdownit": "^1.3.1", "posthtml-prism": "^1.0.4", "process": "^0.11.10", "puppeteer": "^12.0.1", "recast": "^0.22.0", "sharp": "^0.33.5", "typescript": "^5.7.2", "util": "^0.12.4", "uvu": "^0.5.6"}, "dist": {"integrity": "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==", "shasum": "78e979c2d595bfcb90d2a8c0eb632fe6c5bfed5d", "tarball": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz", "fileCount": 14, "unpackedSize": 501111, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDiezUFJ0hmsgVUWaxnULkdyh4cY54citJvvbFj0CrE/wIgNCoT8VLgCehN2R28gsQiUaHFwn3u7yHJ6fy4verfw2U="}]}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}}, "modified": "2025-05-14T03:39:08.866Z"}