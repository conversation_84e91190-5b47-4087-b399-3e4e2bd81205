{"name": "clone-deep", "dist-tags": {"latest": "4.0.1"}, "versions": {"0.1.0": {"name": "clone-deep", "version": "0.1.0", "dependencies": {"for-own": "^0.1.1", "is-plain-object": "^0.1.0", "kind-of": "^0.1.0", "mixin-object": "^0.1.1"}, "devDependencies": {"chai": "~1.9.1", "mocha": "*", "should": "^4.0.4", "verb": "~0.2.6"}, "dist": {"shasum": "97c4903824f1af8db9dd455ee48662929d265cd7", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.1.0.tgz", "integrity": "sha512-iEur8mCzm1BTNOmQCEaX8HBBaqdFcYmzRe20hP1W9AHU7k8pWx3uzYoRgjhWGI+UI0Qs4aN5hwxSnz0mq2OMyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICe5D1Si7f2o/PI9/x/C/2wKSLOjb/TH4Div/eRAVW10AiEAoS5Y83P7SLZx41GZPp96FS0ZMwCFPa+sHTEMMDZHQ6Q="}]}, "engines": {"node": ">=0.10.0"}}, "0.1.1": {"name": "clone-deep", "version": "0.1.1", "dependencies": {"for-own": "^0.1.1", "is-plain-object": "^0.1.0", "kind-of": "^0.1.0", "mixin-object": "^0.1.1"}, "devDependencies": {"chai": "~1.9.1", "mocha": "*", "should": "^4.0.4", "verb": "~0.2.6"}, "dist": {"shasum": "df1d5ff47582788e8b8629a4ca8a7fc4f34fa8cc", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.1.1.tgz", "integrity": "sha512-6O5sRiLdLD2ggz6IjwFxeKMj/wTF/w97gV64d0FzzT3TNesPqtcgW4R7NOZsp69ARy7IKgeN4M1ZqOT/hz7TYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChj7KpV0qMMoiNBdAmUJSOiMquXs6FfS6Z8k1vfD9leAIhALsi1ky/P2FGsld9P43hnV3W9WrbgwI/2mS3Qy6NzTbz"}]}, "engines": {"node": ">=0.10.0"}}, "0.2.0": {"name": "clone-deep", "version": "0.2.0", "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "mixin-object": "^1.0.0"}, "devDependencies": {"mocha": "*", "should": "^7.0.1"}, "dist": {"shasum": "9f8e3becd32ec332dcf722573d954a025615c995", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.0.tgz", "integrity": "sha512-syAvhsRYJutufLoIzW8ZGgyNpqFMJhFHDjYq4nUGaM0v5gxsQJYPGfPFOq4RMRwt4akjYmSiLgIYeTdz9+6b1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVrELvaD7m3nVGeCTBaxRfmqRFfiJOMxc2vbGOYEm06wIgYMuH9dhxcfcafwkMucNArSPfQqpwET3+1hikTRQOZKQ="}]}, "engines": {"node": ">=0.10.0"}}, "0.2.1": {"name": "clone-deep", "version": "0.2.1", "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "shallow-clone": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "^7.0.1"}, "dist": {"shasum": "791b49cfaacbc9da2a3b931199c677a8d5d9b236", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.1.tgz", "integrity": "sha512-W0RDPPM2q01hLyLnLbx2N0RnTK5iTIfWscTz3q/LdgkTEP/g3y6QlpX6kyhH1bXKIckAYdrLKQA+jCVUO4ZCUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCU3fJbQnM4dFStt+jzItvDGCFaKAv9/1v61FRUxS8AHwIhAIUjV0t/0of7WTCSGK4N0xZhYqwy0ZqqCuMr1CxEWEOg"}]}, "engines": {"node": ">=0.10.0"}}, "0.2.2": {"name": "clone-deep", "version": "0.2.2", "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "lazy-cache": "^0.2.3", "shallow-clone": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "6b263e5c8e2bae6b793ed3e2dc750a2de69f98a5", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.2.tgz", "integrity": "sha512-f1VmAXy6JJz2SQpoFcIhnaQRKzuFgXfgtMNd9Oi3XE6JeJFJwvrBWX8lR9G5TOpjsUyu+tU1fKo2wCZXYQuIgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIsJoQUqE+/Tgg01tglr+Ew/BY1Fv96ahP444HdfYyeAiEAh6nsf/cWlW10POiFh73ztR5gWv4XWVWEMPqnV84fEOc="}]}, "engines": {"node": ">=0.10.0"}}, "0.2.3": {"name": "clone-deep", "version": "0.2.3", "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "shallow-clone": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "42fc31f753e3e3d82c4a4e3ebc7514314c7d65ed", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.3.tgz", "integrity": "sha512-tjCqSb0b2pGnCZT7pTaHRxyvXVQeAunsViCw81OBEU/n77Y+Ci4aGoOLI9oWR6JUWKuzFFlmjrMAnLggljuwcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFr28C8R5WDkb8NmUjuUBJSFzcD9HGISh4/SYWDym7WQIgU9Ky0Hvsvk2AEVbsTcsHEFr2OEGGJFZf0LTvMzmj0QA="}]}, "engines": {"node": ">=0.10.0"}}, "0.2.4": {"name": "clone-deep", "version": "0.2.4", "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "shallow-clone": "^0.1.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "4e73dd09e9fb971cc38670c5dced9c1896481cc6", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.4.tgz", "integrity": "sha512-we+NuQo2DHhSl+DP6jlUiAhyAjBQrYnpOk15rN6c6JSPScjiCLh8IbSU+VTcph6YS3o7mASE8a0+gbZ7ChLpgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8LAx2hOkua6rXCH3PRHVdjzi4qWZbxvHSBseCBFx+DAIhANqN1TFTaJcZlosYpNdV64A8lXQlwMQhQPffGuKNm0e1"}]}, "engines": {"node": ">=0.10.0"}}, "0.3.0": {"name": "clone-deep", "version": "0.3.0", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.1", "kind-of": "^3.2.2", "shallow-clone": "^0.1.2"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "dist": {"shasum": "348c61ae9cdbe0edfe053d91ff4cc521d790ede8", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.3.0.tgz", "integrity": "sha512-qp1do6NplYZZcLU+TQ93+A735g1kNcnH75QbC1olpKJphe36CqkjFEK0PyFSoZlI3/1jO5KnDg64xdwWiEH/zA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDR9eeW7sa3aKZxrSHRqfPQxzEyuCG5tohd2lHXQrMYzQIhAKHZQa27n0A2QXQLdz6BsVnGqYpXE12LdO5AXNK1faya"}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "clone-deep", "version": "1.0.0", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^5.0.0", "shallow-clone": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2"}, "dist": {"integrity": "sha512-hmJRX8x1QOJVV+GUjOBzi6iauhPqc9hIF6xitWRBbiPZOBb6vGo/mDRIK9P74RTKSQK7AE8B0DDWY/vpRrPmQw==", "shasum": "b2f354444b5d4a0ce58faca337ef34da2b14a6c7", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhqRcyPH9zFjp8m2fytLFfDaa8x+OGSoH2qzV95rzjJwIhALgHsypGpBl1dXlsr+htyooNBwAecT4SK9HhyOdMm2mJ"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "clone-deep", "version": "2.0.0", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "3.5.2"}, "dist": {"integrity": "sha512-EkjnhytZJ3BVXm0bPwiVO1CfhzE8+K+4u3SUT232FVNqQlHcTh4zrvffAMPWXDdEOwqP3fPYm31oZ3wqqRJvEg==", "shasum": "628211770a402b0f12641cbe46d185db9053ed1c", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5Sgov/4Xzxo/2i7SVJf05ZJYI7oHujdHxBSmVQXEYiwIgIsyuWEQ28AKcCbe+ORyivJ+bGTqNs529iVRd5wkvqQE="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.1": {"name": "clone-deep", "version": "2.0.1", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^2.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "3.5.2"}, "dist": {"integrity": "sha512-odS1ucsnBgQ15htQZQ++HaoO/T5lbOKTL1pRlwdUv03T8vhyNTBZjr7JIZfQhyYl0rBIT+gemHDMSPaXMK6/CA==", "shasum": "661de96d4c6c61b5e8a29c3334941a0eeb132cf6", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDg8hexv7INcNV5AJWubZ0bhbu2MVD3zgGmVEzwyruIPwIgbWRZqfTNKWLyktn0XMN0T/v2kGz4XV91zGe8ESwOWN0="}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "clone-deep", "version": "3.0.0", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^2.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-GTgvzzSrUEt1vDfEHXeBiGr24dmuxnGHKYCUx9+FxTahF9EuOtkcw3HUVDqHIs86BJoca532OdRWCPTXbsgKRw==", "shasum": "c42a3c3489bd82d0726467bede3ccfd3756e1227", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIKJf4sClYyEnUPPwP1Ar9QWYWrqvh6XbtwsQdQrVBcAiEAzMcQCms0jqgwtUl02uvwG1f2KHTEqnHSYYPDXEK5h4E="}]}, "engines": {"node": ">=0.10.0"}}, "3.0.1": {"name": "clone-deep", "version": "3.0.1", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^2.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-kWn5hGUnIA4algk62xJIp9jxQZ8DxSPg9ktkkK1WxRGhU/0GKZBekYJHXAXaZKMpxoq/7R4eygeIl9Cf7si+bA==", "shasum": "7d1a4b88a3cf0bc2da84696ba712b349a6506a44", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-3.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvHd11sLEPuGNXmwvJEyDQTngQNDVll0BmsfulJ+Td4wIgYL4t5iE4kVy+uXRjqGaJRLSxXX22aCB++UtsRQuRsGs="}]}, "engines": {"node": ">=8"}}, "2.0.2": {"name": "clone-deep", "version": "2.0.2", "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "3.5.2"}, "dist": {"integrity": "sha512-SZegPTKjCgpQH63E+eN6mVEEPdQBOUzjyJm5Pora4lrwWRFS8I0QAxV/KD6vV/i0WuijHZWQC1fMsPEdxfdVCQ==", "shasum": "00db3a1e173656730d1188c3d6aced6d7ea97713", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtdgxupY4JuWvnwB4XXq+ymELvHX5RZOh7hHifTP0/LwIgSRP/igAAF3CY7qfiGM03sQcJKodmjc+9jJPBu3aL/hk="}]}, "engines": {"node": ">=0.10.0"}}, "4.0.0": {"name": "clone-deep", "version": "4.0.0", "dependencies": {"kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-aNJ5/7Bz2IYBb7nIj34TLGk78lBXpXUgV9qsLngtTvJ9+scsZNnlU0OX2S2N4ax/sUQt7sDBkXiGjGJEmNbXOQ==", "shasum": "a41ae54db9048b407d9c73e703297a12e1dfd932", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.0.tgz", "fileCount": 4, "unpackedSize": 7393, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBBwds4tkhuMPnlWEoEa29pipvMQhWJVu5T+C5LT/wD3AiAoMYGkxj0UWp/lcbeyegqtOHHWf6hJx2SkyEMTiJoLFw=="}]}, "engines": {"node": ">=8"}}, "4.0.1": {"name": "clone-deep", "version": "4.0.1", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^5.2.0"}, "dist": {"integrity": "sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==", "shasum": "c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "fileCount": 4, "unpackedSize": 8000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9fGZCRA9TVsSAnZWagAAWR4P/Am6wEjUDVsgo/xIPKfk\nyqQ/612FFOi/0OlX/VL+lVfn39MSFiKcFEe9Ud4K1Yup7O7+DRQ4sjAtj30x\nSW8CRL2z7GkwCrNyQVGHvh0gc2nWJrg4dANzL8ytB/cO7Vx6hicyMpLxPesR\nuwsVTlGOk6vYYufcVJymuRRkgXb00P88CbVvpZXzAuwWnC9lxj+YuOBqgSe9\nAyYipfNB1L/QeFtvOaOnWAVFw8Yb/T90cxrzVJff71T9TC7wYmaBz4K5H7yl\nD5TPrqF1KmeGSGRfhE3Fbw85Rpb6a5mkAReQF9wKiNZ+ANhEhAvrUQHdoscD\niBKFqefnh82TMTTyrK6HldXHfOH395B3ryIB0mgZJz7WC2vSUFua0HnkCSwH\nEo62zDMiDfJBm5anm2wBQJKawPUJXEmWWU5hYV5wqTKxi7lwu03zE5PWNBcZ\n9Mo9iiOiqbjdex70t6sDMnSbpKYan/JMiJx1eZLIkai7FGuUOdzU6t/q7n5f\nftZtILHM85m6cLe6uRPYNPRZLXeg5jzWDo/I/3n1y71UvMfjgAyxjUYfeM6g\nb3e8IjA7EHZcImisOSm0BRdvSxl6e6V71MsshcitVbq7PMdk+iQcudJQPvdk\n2dtWVurEUEJvFXT3APmHS/JvyjQDUKyEMNydHw+MgiVa/qLbo5rpERE3CMdP\nJ8e+\r\n=HG9n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBNrYGDt3+h8Eb1qw48UZAfTHDMucy6JvHEled331lE8AiEAq2SVH8iwIyghCXecinEtzqbEVGzOeDXiRNtn3jie/Zk="}]}, "engines": {"node": ">=6"}}}, "modified": "2022-06-13T06:11:32.512Z"}