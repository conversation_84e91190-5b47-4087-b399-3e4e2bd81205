{"name": "@sideway/address", "dist-tags": {"latest": "5.0.0"}, "versions": {"4.1.0": {"name": "@sideway/address", "version": "4.1.0", "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "dist": {"integrity": "sha512-wAH/JYRXeIFQRsxerIuLjgUu2Xszam+O5xKeatJ4oudShOOirfmsQ1D6LL54XOU2tizpCYku+s1wmU0SYdpoSA==", "shasum": "0b301ada10ac4e0e3fa525c90615e0b61a72b78d", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-4.1.0.tgz", "fileCount": 12, "unpackedSize": 56632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk80ZCRA9TVsSAnZWagAANHgP/2Mda5mFt6OU5DN6+/w7\nN5rnxQxAIddI4ybmngilZG6XI6TT7mEG82Vp9YBY5d+Ql3wN5R7WVzY91ovG\nvCddw2D6HwylhLo5pi3IfgfpERqywJxfxKPZBZQQJsg1jQXHlSWPXvmPNyOD\nNbZRSkyA/ewJ3HxmBMrMYyCXKcIgAN4pbSdwlFVKfZwLLcg0Biu+ushHcrlD\nD/gucUSs80DcV4J1Rf9MYV3NLK8U3JgNC+MkvtoN6py0g+sTKyZl5MTM4GtA\ngjuZ2plnhAIg4bsgwH8pccdSGQi/bSPh3vHuboNcRZgKSymWd3XR+C5vt/tT\n7FBnojW+dFMEMDWfIb4ofWIFYpxPaj6GjWR8puNTvGqOXppre+/OY7SfhmFg\n8HWUFTHMHc24/zo45b+NLdh3m8Vrhuvg7HlAuTy68K10MTlxvSIPlFsS1CNe\nlsl3onPPq5onupTa2pnQY9DIgaBEu1Z+qwNikJgn9KZHa5Rx1D+OPNgLCX0O\nPOxZbqpJZcoNYKvuEahIWpko2Ad1DaT/PAzLiw4KP8tS1IoZAqCf0oSxmrsx\njJ4j3gHB8gIvVQ5ynVB8lLbff0sduuiJb53MTFddpvGqyOE4mJlYug+NHEYv\nryW0A8tAI2/ErfeMaxnUomdwSW/q25gjtocukn4O76gIg4BmWE+hyifiiUWG\ngNpO\r\n=/Drh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLQri2ZVk/Qt4G5MXZLyy51S9WvTBLVVB9x2lug8s/OwIhANCKRlcB01GUzX05q3W/zvhY+vREU3fBU8cFQl3L01VK"}]}}, "4.1.1": {"name": "@sideway/address", "version": "4.1.1", "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "dist": {"integrity": "sha512-+I5aaQr3m0OAmMr7RQ3fR9zx55sejEYR2BFJaxL+zT3VM2611X0SHvPWIbAUBZVTn/YzYKbV8gJ2oT/QELknfQ==", "shasum": "9e321e74310963fdf8eebfbee09c7bd69972de4d", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-4.1.1.tgz", "fileCount": 12, "unpackedSize": 56314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgII2TCRA9TVsSAnZWagAAmd0P/0WZ+eTtcDn7Xyz18Ksz\nZundW4dg2PCfi9IeqWztMA15Kg+Vy9skdsv+oYe/7NpB8dqpRm0HFlFHXjz/\n9GHrpvF/gu7r6l9sIKwPpW2ytGEpUr+VBUhTygIuM/NGYzqrvALbcIWbt4RI\nLkBXYT998bWYBkvBW5cw7D7PQEU+YNkr+ZsGaIBH5q57rP2vPLwFERxFjUHd\nm6XRmdpYZSVAStITqlG5Zv4hx3tVO626axtqEVUv5oF16DopxnWY8rzafhaW\nM02Cxn7Bsr/rLUzOe9UsqTBJe0vUP2Guv8kUqJyW5cCBhtK2IeMK4sXsRkrq\nSigeQHnBUx6DaC5m4037Hrq3mHVg2k5TZq1e3guPvZ00XZo4D2JZ/tQJtfF1\nVz1mDIJqwMLY1t+TJ32ftIPMVvL+MrQ/JDC6FGBhzvlEmV/8yj5A6aM1VYSU\nGmbYDjqrJZUq9ZuBHHRecekqdluqCwsshBNHbvRNz6qu/H+JFx/mOciN+740\npmy0Z7aauMa+VFcn4MMgNrRWs6VifVNaMhSHrWpoq+Y1bRMXsgPU6xvJek5P\nPMfc7zdk2h1iRmQu03kTtIovIoAApbE4k62sEUkwMTy4Me9RfnHzLDZ7DbzX\nPlC6hi3vnx1uoFTMDP3hE0WB3uY7wGjAK6jFTob2JPvOXB+3dJEy/XmUxCJS\ncxG6\r\n=G22K\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCND1LRhoVXhYHx1ZBtr4rpZWCUwwUrux+9RKLY9gedBQIhANllTdn7V3VNTHyoS0BCSSB2MErs6ly7YqweJoQi/VLi"}]}}, "4.1.2": {"name": "@sideway/address", "version": "4.1.2", "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "dist": {"integrity": "sha512-idTz8ibqWFrPU8kMirL0CoPH/A29XOzzAzpyN3zQ4kAWnzmNfFmRaoMNN6VI8ske5M73HZyhIaW4OuSFIdM4oA==", "shasum": "811b84333a335739d3969cfc434736268170cad1", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-4.1.2.tgz", "fileCount": 12, "unpackedSize": 56314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglNn0CRA9TVsSAnZWagAAPIwQAIpnhTJtFS4a7Lln+xK0\nlG1ew+jNNSbLJ2arC641H5ybhQY0DkPOPkxDP+gJOQTWtiXzTPoLGJgRMNuw\ni3cUZbilEIG3X76m/QYKvDxRl88s5fILNrKFCb96+qk24Jq4o8+CF8P052mv\nW3pA3sCVJGbY6SvchVLH35ZUBEeJBVfC/t8oC5AhDqZ4RIxMG5Wsl5oCY/rI\nIzTu4XxGfzVJa7MjQZaTpAWqwvrVhH8ggQx1e2ZJ9qUv+vByqYUFC/YObSYI\n2dBrnOrBSGmi0ZWqehNccfRpctWu3BLOIiAilkZp7cnLcjM1pgOzjBZxFxaE\nDSs45MSORFlvPPvIMTo3mcEP/hbu53y0WyPAUZYHaI/sxayHJgRL5zxUuNWp\nYLsbWTviG6xxIP1m5bYbsUxq2KitQjSpiC6Nnb8FL6L7o+X0yz/Z8DUVO1KU\n1VePbTsKoGlHi4rt70mH85ObQrVHA94MczFXEg5ST8Bz2hqtnDmW08u4gEul\nADJUH3iHf1x2l4cA8Jx/UhgIfTchXFRg7y5TybKJnjXnZ2zoV+X69yPwgFx4\ncHTi9Ua86jLU+zO06WALnGI7+7HXQX7ADfqi/DJyQu5895Y9zXS2MchN9Hsh\nTsco7GN/M3PHUya6DKxfYEz/cq8ERkc+vbMMo5zCb8LEQ+gy/yzecU/D4Gn5\nZW6y\r\n=xBAn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE4UI0ilhQK9FbhTFrWQPM0kw21VgeJ0CuOVLH7qleWZAiATs4f8ewM1A4H54xm1EUXdurWeZjBpDlW/ODZ+CQ8MHg=="}]}}, "4.1.3": {"name": "@sideway/address", "version": "4.1.3", "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "dist": {"integrity": "sha512-8ncEUtmnTsMmL7z1YPB47kPUq7LpKWJNFPsRzHiIajGC5uXlWGn+AmkYPcHNl8S4tcEGx+cnORnNYaw2wvL+LQ==", "shasum": "d93cce5d45c5daec92ad76db492cc2ee3c64ab27", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-4.1.3.tgz", "fileCount": 12, "unpackedSize": 56681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpzR9CRA9TVsSAnZWagAAemEP/3iw4TanCl/xWw9VjHe/\ntuBeh7+XQftHUsxH/enhe+TI43rPc0Ldcs4PiR2xabJP/fF2ov4P6mKH9kb8\nnl+5wsY2YwK9VAgOnjjAzQsJjce9sfF3sPI/y5yAYP1RBeaKKkaZ7Kx2cWv3\nyx4B1YK6V1LjRQNbp5hxzehRvgPBGMFvgQr8CjVhkPntVorIZvCXswVOQJnf\nFf4f4e9/P1jV7+mRRj3xHpN8zDZ5iCnrBvITBV8o6BtEPV80aQNK/Wl+U5o8\no0tZtc8/GxKj9qULnpQIsJVeVfLhxJ9Akn+0RgB1x8HbOSB5AUvNcpUKaeLz\nIMQAP6ZGTuXGyM4sZhEYBw7x/43nOi/tunAORd9nq6o02nV+2yEJWlGPRQcj\nLfYRIlNLJ4tYAQbe4ipymOWg3TGl2EY0xu3RnD8qtQcETjZkDGorklIpUySn\nM1R3GBX4RSVNWitzA1o3CSrGLmcSNJo/TyJzi/zkyLW1p36tBjdI/Dl2NyZh\nNcsRSF1DkXxtcVi2tux3w71j9E2ASgoJMcdKrZPJp7yQS/Z6kewdX/byFxLo\nBJ7wsahrQdnfU0SnsaovihivRgdTzB3Xirg/Jsopw/GSt8eeHpEm55ganEC0\nEzzBBUXP05ityQOSdS5yvRXGa2gE8DJgjaVLO6YnOrGui0y5Q5U14A2qcTLA\nXePu\r\n=03DT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEonLDEr6ybLRXRpgMUbDsEzuuEyxbb25ODvbsv8uEeAiAVg78OPhBkgmPBYfCo4Tdy+LWlcAbsOyXLWbTs0y5k9w=="}]}}, "4.1.4": {"name": "@sideway/address", "version": "4.1.4", "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "dist": {"integrity": "sha512-7vwq+rOHVWjyXxVlR76Agnvhy8I9rpzjosTESvmhNeXOXdZZB15Fl+TI9x1SiHZH5Jv2wTGduSxFDIaq0m3DUw==", "shasum": "03dccebc6ea47fdc226f7d3d1ad512955d4783f0", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-4.1.4.tgz", "fileCount": 12, "unpackedSize": 56400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOlpBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodMQ//QKSwHuBmob8bbEX9FIPVfm2hXoGFY+0cdx/YgjmNIe+u0LnW\r\nf8KHTSL7vp84AQ0/7HaZaINBsSykucSC2FqYO4oTkokSbSN9mgMyY651np9S\r\nwGNeYx9DxkYn34bnksP86ruye36+UhzVGCACsoQxnkc4WGGA2/tps/pIsQxM\r\n0CKnFS0D9nwrQg5/YJmoHSis8BYM9oe43dWd0JH2HwqDz0PAg7cqluFwrkRc\r\nY4H0h6nOOXJ1CCeaGasPGNYcV2PlutP0NJ/vVIBVPbn6eWUZ7TlQpv6sJzUS\r\nF8/xK4f28caXZjAwr/NzHK2X8R5LqQtXBx3VzVHwacQIvoedS5fDyMJ8H4Oz\r\nhk1qLDSivfIz1sgCRVWf7m3Cplr9uUW2N1PCfrktpQY4z2iM0s89qcqdw0GC\r\njqtkJu42zXSRn0jynxbnVOdoH1i2lM0UxXBaAlbOa+/ZoK2CVRHs/VODkp/M\r\nn5u4mPhJwkkX+/1HQqtwpKmWZ1DpO7yqOjD5/aylPPCITa3Pt6hPCCpWkpgk\r\ngEKduE8VfN17/koSq1SQZ3dTX0+k7b2dS+5dvpokBDzqzfq0l50mkpiL8Sz4\r\nWWkdWXrMdnc7uJ92sBc2i+cApsTIEUXTsOBM9FtxYrwrVhtEk1t5AjemnmWL\r\n0hgfMRV2i4402o35fJk/50BTmN20mzVCn/g=\r\n=/SSn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBPab/TfiSFngXA8nMwQ7lgJwH2cMK9yY85IOe1ZZisFAiBJciDcAYNpxwFwcx0Gy23DUNSeeNfei3Z8glJxF561Gg=="}]}}, "5.0.0": {"name": "@sideway/address", "version": "5.0.0", "dependencies": {"@hapi/hoek": "^10.0.0"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/lab": "25.0.0-beta.1", "@types/node": "^16.0.0", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "prettier": "^2.6.2", "typescript": "4.6.x"}, "dist": {"integrity": "sha512-IEZ3Gi972M1yubSPhcpzpVTT/Vb46F9L0W+K/GhqvWv6aAvVbNNVsYFekXWEemHHFfTVrxFcURrzsPGPPKkxKQ==", "shasum": "015f191a4a29e2b2f9ad1aabe7465c3088241536", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-5.0.0.tgz", "fileCount": 52, "unpackedSize": 144967, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIChuDVbir7BIqumI8aSp+bzMyJCK2bvMYRw6YOiZfGZKAiEAoo+ZWGX24jZ/ArRe8iuB8UHQ2DBQPGPQIGGajIGuAlk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib29XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+bw//WBxcnH2QFDUupzNCxUNZuleR3r0I7xlLs+eki/XK91pAJLlW\r\ni4h3VTVdujxDRLPSzW5dMiF/OlpnrryffihSN+PVgp0ovJCGMuY+sqvfJC3q\r\noCejdjya/I108MkMai+6KytEdTtuOnEXtFCPQu5PZhhO7DEI0kFCyJKgQdfU\r\nSDsrVK0dcEajviZ2B7gGQQ3rEtL3TNYB0e/pKEJU76yXwJ/Si0S6uoPwd5p5\r\n246e/Xsoq26sFsUoPpdtZpHTBlNsrMsM8V7Yvi+lFgmEaBPwU9lDuulTRUyC\r\nkk8outA2FPVwTkXMo7y7lTdmaH328qV+ZBcVB17ehUX7bwtczDh06GArqqEF\r\nGobmkBdPtXaViM47n3vdi+qz0Vmn2mZjdioRcPPYhqeV2klr/3H/S15ionF3\r\nzvw6yWkfCou9favfAY05V2vsB9lwq55XLIBzldGEohNTzeeLY3f/HTa+yjp3\r\nTCVb/hiQ4RP1GI13EkPYAc7ipZ6mq156y0ozGdaArIEUtV3FQfQRHxPZREiR\r\nx7xY9f23mk0rDgonrb4nJE1kwjfpcme5wx6jEhlktQNCOpSVOtvZqp7MLAeQ\r\nGmKZbe39Z3AcQBrY2mcCqXvbSAwjZeVTJz/8Ff0+pdJ+UbA2AjMWGHdeaYjv\r\npOd+QMD9hgyWPucOgTPnI9WwMsjeajfg8d8=\r\n=OBcE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.0.0"}}, "4.1.5": {"name": "@sideway/address", "version": "4.1.5", "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "dist": {"integrity": "sha512-IqO/DUQHUkPeixNQ8n0JA6102hT9CmaljNTPmQ1u8MEhBo/R4Q8eKLN/vGZxuebwOroDB4cbpjheD4+/sKFK4Q==", "shasum": "4bc149a0076623ced99ca8208ba780d65a99b9d5", "tarball": "https://registry.npmjs.org/@sideway/address/-/address-4.1.5.tgz", "fileCount": 12, "unpackedSize": 55851, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGuM3gGeP2b20IZiXEm+9LXskEs6fNOEB/k14VzyHY7rAiAqa9F/kmvUiGH1Mat389atI10RbRbJDEAzRXsuO/gRsw=="}]}}}, "modified": "2024-01-29T12:41:44.521Z"}