{"_id": "json-schema-traverse", "_rev": "12-3ad8dd1552d4e8b99758d847f27ceb73", "name": "json-schema-traverse", "description": "Traverse JSON Schema passing each schema object to callback", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.0.1": {"name": "json-schema-traverse", "version": "0.0.1", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "gitHead": "c0a0d74b106d56a61e953f5571ff42739b4ca3dd", "_id": "json-schema-traverse@0.0.1", "_shasum": "d241e25481ad2fe8a1fdef61240d727fdee61283", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "d241e25481ad2fe8a1fdef61240d727fdee61283", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.0.1.tgz", "integrity": "sha512-P3xS/mSXzflYybh30MoI1fF1nFJuyVnNKlluipufn3Jeg0V2bMxsQ4GzF+L7DYud3OSH0QLR7wxgETrF+i2L3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcHoqEB2KrJcOWJjZB3/3+1KUeg9DRhkXoLv3rrj4W8AiEAv51Bi28kYL/xuhv38ir35R2q3cY4aLNIi8EBQPUjUS8="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse-0.0.1.tgz_1496601736961_0.5534928701817989"}, "directories": {}}, "0.1.0": {"name": "json-schema-traverse", "version": "0.1.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "0d928d60085ef32d11cec52fd2adbb1a9cef80e9", "_id": "json-schema-traverse@0.1.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-1MtXJHDOlTbfMgXZ2mL1DvViXyph6ISlzKIk5On6KJbcMrmdOmwTsWkFDhy+9CJsstbuKlQHohAM3UIDNzQQQA==", "shasum": "d88e207b83fd8b71f09f5b0c11734de5a0a2d203", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYvoTSzjq4AXufqip4HqomwT98HXIEZIHep8LTM/MlcAIgFiTIx13nnu8bxYpt9SbfWJeW5I80ag9VkIsXMNt/U3w="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse-0.1.0.tgz_1496693959875_0.9615707800257951"}, "directories": {}}, "0.2.0": {"name": "json-schema-traverse", "version": "0.2.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "70465d279ff702ae915106cb5bfbd851712d37d0", "_id": "json-schema-traverse@0.2.0", "_shasum": "486926926c876f74dd0f7d696ad072118c75d03c", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "486926926c876f74dd0f7d696ad072118c75d03c", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.2.0.tgz", "integrity": "sha512-W8OfVxI/WDsrJXsDzCRfcH4UXFaxC+W/MuWB38urdbLZAkgxV/Qi+YeC6tTmx6+n9sbFXp36oe95Fn3FFNyMRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDTJg4X0USb8FZj84XtVy+05viKTY1xkEW1zuBrydrrAiB1v6ydur38P+xh+R3eeWji2yU5p0ogZZX3Di8qNoP+Pg=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse-0.2.0.tgz_1497475368098_0.03620475740171969"}, "directories": {}}, "0.3.0": {"name": "json-schema-traverse", "version": "0.3.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "0c2808d402f8d57b6360f24ad8cc0fb0662e17c7", "_id": "json-schema-traverse@0.3.0", "_shasum": "0016c0b1ca1efe46d44d37541bcdfc19dcfae0db", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "0016c0b1ca1efe46d44d37541bcdfc19dcfae0db", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.0.tgz", "integrity": "sha512-rAzOwA4cQfuDsZtnNbop0anQyosa+EzNt6bafT8lDMQA+xFmlVnhG+Uc6Eri/pOI6nFtA5Nak9ORyvbCZ4KFNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPhCdw3Nvs9Gid+yGpm4jif6WGagbu0JZPOWzJ9bgvvwIgK4fIwQ6MVtrJQor+dCvZ0NxCXe9/xL2BBlDxgU+wWn8="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse-0.3.0.tgz_1497558177644_0.5944948405958712"}, "directories": {}}, "0.3.1": {"name": "json-schema-traverse", "version": "0.3.1", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "f0a6627655525debea519dc8ebb4cf35f3c8e85f", "_id": "json-schema-traverse@0.3.1", "_shasum": "349a6d44c53a51de89b40805c5d5e59b417d3340", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "dist": {"shasum": "349a6d44c53a51de89b40805c5d5e59b417d3340", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha512-4JD/Ivzg7PoW8NzdrBSr3UFwC9mHgvI7Z6z3QGBsSHgKaRTUDmyZAAKJo2UbG1kUVfS9WS8bi36N49U1xw43DA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEEUvq6T+SmKN/KLhRDONA5sJia05n3oYTphNkVZZl+UAiEA7ITFoYFGXSzOvfcziBq7b/81vfczQk8HS+n0k+rgWzo="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse-0.3.1.tgz_1498261856588_0.601617609616369"}, "directories": {}}, "0.4.0": {"name": "json-schema-traverse", "version": "0.4.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "c1fd5208eb7fce760867477aa119c8bb16972df3", "_id": "json-schema-traverse@0.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1YoNcmKmUIOyoF4DdbjA4qeZz9sxGJ3O0OAZI7J0b6K9A7jiE/24azJPEE/4ZDZbXsDU6biRoNcC8QWwCKegkA==", "shasum": "e8467bf83020ba9d2a47b2e6b82e1ac056b0ad86", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.0.tgz", "fileCount": 9, "unpackedSize": 19547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fygCRA9TVsSAnZWagAAdn8P/09M6Zwu8Q608VPdBomC\n34JdJK+UNPU/k0NCO0aC84D31U4M/fsT2zVAWF8XcQ4X+9qFYJyKwRyvA+b0\ni2hxPdlBife/3aJKxFugOGlHQv0QE46H9BI1Yzr/auKlc/XtBPbK5NgBxQua\ngpOZKY92+jRE42eZaQ5AL1K+W6pzkE5/zOfDCeI5CHJos7/EAdQdsS/5Ferm\nJV/i+A2Z1VbdFtZYAiqgvdldqt4gP6TA77uoep1aLPtf/Zvm3T7qXk7hencf\noo6AilfwwTUOWvIsy2ph4qaq1zwtM1PQ8/27/YQx0notvsegRfEJvh/8rtQD\nuOk/6nWtiG9Ls/Tbh/ZqwQBikcuFsU5EA+Zn/ZSnl33EhXSahSHP7l2BQA/Q\nx7jDqts1ATpwQvX9Zz09h3s7RsHtTNftIrahUGqJ5owBw8vi48RAO2pcgxqr\neLdCJkzUf/ECyQvoroxV7Ek57V2It61QlNankKGEB3rHlH2O1aCldTjtSsqB\njv9F9dCCufHMp7LwWibaiZseLqdOn00NSY2GvOnmrjM8sU3OBIq/bwNt4CyI\nX1SI866T56JMBCT6B9KCDMdysfEKtqtho2vXqtjWbRY7LVXiycHn+HR9x5v8\nj+2+c4lYY1t5vMjOdh6Om0+eUGK1C04cERu5yIvKNbaOtrxOxNo/wzamly5a\nkEpm\r\n=ZBqZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFIhICbuOP7yZkZHWsL7vBkiNhAHTUZE0fTGPF8yYtrtAiA3VEMyl+zpOLpWbvLHqA2ZnkOHRCSCFTAq8OitS+MRGg=="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse_0.4.0_1525808286399_0.5813937245567184"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "json-schema-traverse", "version": "0.4.1", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^3.19.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "bc898eeee723833fc9d604523e00014350bcc4e1", "_id": "json-schema-traverse@0.4.1", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "shasum": "69f6a87d9513ab8bb8fe63bdb0979c448e684660", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "fileCount": 9, "unpackedSize": 19564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHOReCRA9TVsSAnZWagAAVWUP/0MJPYc1bhxL+UjZWR+N\n0w1lIESRrtuAzcNFPDBfPvd8bBr3gxJlaLHMkpvzyEZGN69bgQP2hsp6I+Mg\nxvZz/mSOnK5Yj9/VTytxEjlwqW0GDK24x08GXNAZenNidRht1iZvBjugZbJa\n0hxF0z2Xjkn9VXgtOI0QiBLj9t4mutLrte3kFFDNpLTzoRx5r08JeA3L4k4I\na/Svzav/z4c8AsUFaeY/eAtGPm9KqwLzX0eLWQ7ueYPR7YgtY7WUWuSSh8+f\nuk9UJrG0OVS4GPl8YvwMvjf3Di9nUDwoQem9QnsIJCaXJ+Gbknv8B/Lit/9a\nzmjaNJDDNOF8t2iUfwUZWarFBGxYsZBcoECDPrU/tGMj/8IfHXMNHVWraqAp\nfZ9pvYbJ7s8DTlpJ8pkuGUDPrZIX4POWYcsuYVjUUpRwV73HnCIIAJibctmm\nOf1HzG0WaF5OE0ZU06UcGD6SUiZTgtyqaA0jU9vExTYhYSobaWqGKxA1PspG\nMATdwowCSsHWrUx+jXpzuJnEAyDBLW0iVh+rjBmWWvZHS+fub3rE/yT5FOPI\nk1y4qSwsF2I8T8R+Moo1OewFQzUBbaCjdpnOsBluUWmSvkRiwr/lcwZznt51\nBCTVCgPAGPQhcHbHtM/cK8tqpGEXeFTKtHZ5fHCGsL49sLtGPTj3b948t4z/\n1BiZ\r\n=77vn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDbOwAHkjLehseSNqU5TdSlk74ifhLpOlyMdfnCn3piAiEA0+22VOsxdSfplXb4J6DDiQvQd4rDYLqynJknCRcTsBE="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse_0.4.1_1528620124976_0.4290201343994062"}, "_hasShrinkwrap": false}, "0.5.0": {"name": "json-schema-traverse", "version": "0.5.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "types": "index.d.ts", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"coveralls": "^3.0.1", "eslint": "^7.3.1", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "666a85fe56ccc27a07f4d40ba52cc172406f8f17", "_id": "json-schema-traverse@0.5.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-x+TRJIQFskrNnFKE2Viz9FCSjK1vIh+H/uaBiOYszh/IcZmAFneQ35H4osWDJp1NPXccuV2I0RMXmi2ZS6Kqcg==", "shasum": "1069a6097f9e9a567bfc6cc215e85f606ebf6480", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.5.0.tgz", "fileCount": 11, "unpackedSize": 21098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfU+ywCRA9TVsSAnZWagAAo4MQAJhqEwwNhHZFLPcknIez\n/fD01Vms7E0C65XAiKct2X2Pkiqpv9LqMk3cBBaPXDnVUMZeebjIjXacuN6z\nuuyIAQTjxDPZez3j2qd91lFPMSW5QXuWDK0xpnUqCINkZfrAH0g2ctZY81vW\nGVY5lutIYk6uQStlsSet/iVDWdJX4C81lmDZzew7EnNcTUi0O3TQtghOSuVG\nslnT7qpdweY+z+mNzrBwkFeMg6il5YygkHmmYz18HssNncV6+PoVdFn+XJK+\nZZ/QGXjS9IcBh+/iCgz0+2Az4bGr6UVcf2HleKBMIfjOQduLQ0zMiBy+lDMx\nhxyLmtevrqefSdkeVX7Y8tAIAhHr8Se7WMeNHjMbGQe2T+dB7PUh18zLSO8+\nQZqtOsNprGm23n5avkLORrxA7PnqGVq8BBPY3pisPv19P8ljJYbRtSrsH2Vz\nMoMUupSVaLNWeFwiUA/64WkXPVMFcxNjTQK6XDUL30JwGuE6PN/p1FGIYPXv\nByCLsZ+sumBDYF8V0jIb1AfERA143Nvgl2Pz6Ye27yAKw1hdR+d+5MTMxUWu\nmPSkSNAni33H9VMybozn3nulLHIwb+YyGJieR/iLQvoyemduoMAhwtCjK0fA\nnfRehA0Qp2hefE0AfyUb6IcPV3ROZddrDGvMgpJ5CbMNeaDoXy2T09vG/a/7\ntDlM\r\n=6n/B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHaYYtSpbSyhEv3zHtQ/JRHfNMkXDufaaoXD4+ivt36BAiEAnCnCZR8CFN2gs5RehRrsmfjwXkBtC0cA6XgRhWgvMmA="}]}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse_0.5.0_1599335600219_0.22633217841346598"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "json-schema-traverse", "version": "1.0.0", "description": "Traverse JSON Schema passing each schema object to callback", "main": "index.js", "types": "index.d.ts", "scripts": {"eslint": "eslint index.js spec", "test-spec": "mocha spec -R spec", "test": "npm run eslint && nyc npm run test-spec"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "keywords": ["JSON-Schema", "traverse", "iterate"], "author": {"name": "<PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "devDependencies": {"eslint": "^7.3.1", "mocha": "^8.0.1", "nyc": "^15.0.0", "pre-commit": "^1.2.2"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "gitHead": "6b45983cd76270042cc79527da5c8972f13599ec", "_id": "json-schema-traverse@1.0.0", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "shasum": "ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2", "tarball": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "fileCount": 12, "unpackedSize": 22220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1fN3CRA9TVsSAnZWagAAOvgP/RW4IyNaG+9p/oIa6WhS\nu0try6lP7Hym+dFd2utH5S19/K2IPoFXP7CFvq1blPVc1adUBBRdfdz7SJ9J\nVJr0Hdzpvrzhkzfod9SyGcjmwOIZDGmI7mhwxTuqYCN1kOWpAY0gnzfV+n5p\nZxDGKLieCqCzmKc1TsSb/aUtWndUmcs8UsUZI5mjqSTajcqRSX11Fillmor8\nZVFzyKWhwEmDFRQlFRR3cTEbytvs55sqTdpWxaZDh7g8UrxdhUQIoI0SnQFO\n3pWaMCf+fM9SGY2JUlMLhr6n3ui5OBqkgjfw5AUtld405fyNbPQlJu7QxATo\nMU7npmYHcoinMOpciVD/tLNxD+OQKyOZ/NlL+XXBdCWQhLIuF+Rnb+raUiSC\nw8q8W5Vba/JN9/pHs8x+3P1eZFIM+cxMQk9RYFuEqtHkGhDiVthpyMsK5h0K\n2Nu7nLBVsnEEid9UggwPep2ua1JO5YETsAtwwo0RFuhRhs3I37eKloiI+NmJ\nCBASUczFiT2vE5ySGPHWMteTjL/5uUU+a+IZhqA/o2Wsmnw4Kxln5npF0kjg\njAQnn/t7QQcWhNGqsFnKEg2rv6cUgj9MoZdxHsyyyCnZ4qEBLByv83zOpigR\nfyxhFwV8uGKg1IZmeNQn+Kj/pBpifoUSbR4LGr54MVSuUrkjIdBv/sCLVC3c\n3vaz\r\n=T5IW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNOIFNJRe2jcKCXH4ICrcSvZnowa7w+vu8ftVn1ma0LAIgVov161AURGFLhlj5B6eSkke5RxOlmvY3avjHP8IvUTo="}]}, "_npmUser": {"name": "esp", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json-schema-traverse_1.0.0_1607857014508_0.11357423963069646"}, "_hasShrinkwrap": false}}, "readme": "# json-schema-traverse\nTraverse JSON Schema passing each schema object to callback\n\n[![build](https://github.com/epoberezkin/json-schema-traverse/workflows/build/badge.svg)](https://github.com/epoberezkin/json-schema-traverse/actions?query=workflow%3Abuild)\n[![npm](https://img.shields.io/npm/v/json-schema-traverse)](https://www.npmjs.com/package/json-schema-traverse)\n[![coverage](https://coveralls.io/repos/github/epoberezkin/json-schema-traverse/badge.svg?branch=master)](https://coveralls.io/github/epoberezkin/json-schema-traverse?branch=master)\n\n\n## Install\n\n```\nnpm install json-schema-traverse\n```\n\n\n## Usage\n\n```javascript\nconst traverse = require('json-schema-traverse');\nconst schema = {\n  properties: {\n    foo: {type: 'string'},\n    bar: {type: 'integer'}\n  }\n};\n\ntraverse(schema, {cb});\n// cb is called 3 times with:\n// 1. root schema\n// 2. {type: 'string'}\n// 3. {type: 'integer'}\n\n// Or:\n\ntraverse(schema, {cb: {pre, post}});\n// pre is called 3 times with:\n// 1. root schema\n// 2. {type: 'string'}\n// 3. {type: 'integer'}\n//\n// post is called 3 times with:\n// 1. {type: 'string'}\n// 2. {type: 'integer'}\n// 3. root schema\n\n```\n\nCallback function `cb` is called for each schema object (not including draft-06 boolean schemas), including the root schema, in pre-order traversal. Schema references ($ref) are not resolved, they are passed as is.  Alternatively, you can pass a `{pre, post}` object as `cb`, and then `pre` will be called before traversing child elements, and `post` will be called after all child elements have been traversed.\n\nCallback is passed these parameters:\n\n- _schema_: the current schema object\n- _JSON pointer_: from the root schema to the current schema object\n- _root schema_: the schema passed to `traverse` object\n- _parent JSON pointer_: from the root schema to the parent schema object (see below)\n- _parent keyword_: the keyword inside which this schema appears (e.g. `properties`, `anyOf`, etc.)\n- _parent schema_: not necessarily parent object/array; in the example above the parent schema for `{type: 'string'}` is the root schema\n- _index/property_: index or property name in the array/object containing multiple schemas; in the example above for `{type: 'string'}` the property name is `'foo'`\n\n\n## Traverse objects in all unknown keywords\n\n```javascript\nconst traverse = require('json-schema-traverse');\nconst schema = {\n  mySchema: {\n    minimum: 1,\n    maximum: 2\n  }\n};\n\ntraverse(schema, {allKeys: true, cb});\n// cb is called 2 times with:\n// 1. root schema\n// 2. mySchema\n```\n\nWithout option `allKeys: true` callback will be called only with root schema.\n\n\n## Enterprise support\n\njson-schema-traverse package is a part of [Tidelift enterprise subscription](https://tidelift.com/subscription/pkg/npm-json-schema-traverse?utm_source=npm-json-schema-traverse&utm_medium=referral&utm_campaign=enterprise&utm_term=repo) - it provides a centralised commercial support to open-source software users, in addition to the support provided by software maintainers.\n\n\n## Security contact\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure. Please do NOT report security vulnerability via GitHub issues.\n\n\n## License\n\n[MIT](https://github.com/epoberezkin/json-schema-traverse/blob/master/LICENSE)\n", "maintainers": [{"name": "esp", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:32:38.879Z", "created": "2017-06-04T18:42:17.844Z", "0.0.1": "2017-06-04T18:42:17.844Z", "0.1.0": "2017-06-05T20:19:20.802Z", "0.2.0": "2017-06-14T21:22:49.143Z", "0.3.0": "2017-06-15T20:22:58.966Z", "0.3.1": "2017-06-23T23:50:57.513Z", "0.4.0": "2018-05-08T19:38:06.477Z", "0.4.1": "2018-06-10T08:42:05.056Z", "0.5.0": "2020-09-05T19:53:20.342Z", "1.0.0": "2020-12-13T10:56:54.719Z"}, "homepage": "https://github.com/epoberezkin/json-schema-traverse#readme", "keywords": ["JSON-Schema", "traverse", "iterate"], "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/json-schema-traverse.git"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/json-schema-traverse/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}