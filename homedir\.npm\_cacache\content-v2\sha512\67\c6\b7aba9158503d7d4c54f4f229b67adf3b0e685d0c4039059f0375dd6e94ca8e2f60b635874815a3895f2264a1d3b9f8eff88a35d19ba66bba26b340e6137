{"_id": "named-placeholders", "_rev": "18-c5949f08f4a60b42c421775dd7d5a230", "name": "named-placeholders", "description": "sql named placeholders to unnamed compiler", "dist-tags": {"latest": "1.1.3"}, "versions": {"0.1.0": {"name": "named-placeholders", "version": "0.1.0", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/sidorares/named-placeholders"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "mocha.md": "^0.1.0", "should": "^4.0.4"}, "dependencies": {"lru-cache": "^2.5.0"}, "gitHead": "8aab747ebf8786304a3227ac879088efbe554f3d", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders", "_id": "named-placeholders@0.1.0", "_shasum": "ed9ba6d747589dec25a3a978cfe41cb7a4a8de9f", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ed9ba6d747589dec25a3a978cfe41cb7a4a8de9f", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.0.tgz", "integrity": "sha512-2Ry/2+X+LjhoCY9GJLGAHGV23H46hpfl4her0DQjDKcLbKMwSowkr6ZiDlAsFMLTf1C5sv2a+kC+k4F6gPObfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCd38eHSpUlWNPVKBmo5nmGYomTiR9YgPcgQu+on7o+agIhAM/RcWzNF1dDRJxre5oCFPLgmZk++rvgErwxwu/WAsl9"}]}, "directories": {}}, "0.1.1": {"name": "named-placeholders", "version": "0.1.1", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/sidorares/named-placeholders"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "mocha.md": "^0.1.0", "should": "^4.0.4"}, "dependencies": {"lru-cache": "^2.5.0"}, "gitHead": "7e4dc90792e0c9e823a11017601422283089ee33", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders", "_id": "named-placeholders@0.1.1", "_shasum": "c6765b3cc1cd37a609cb0fd35b7978b2ca63245f", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c6765b3cc1cd37a609cb0fd35b7978b2ca63245f", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.1.tgz", "integrity": "sha512-qwNcQBcVqawr6NfRox0+gqxsFe7n8xjsK/kBzDRqkep9ysWHhyIa4MDnM0W4uTu31vuCrqb+1IpnMOtCKjs48w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4bgk/mVXuyucMK/Zb7dRk0KvHg1S4vznLKmtNAQgXAAIhAJjcDxGdwpFTCfSlEU435DuOKbaYGz3bW0bTX7lW5WMg"}]}, "directories": {}}, "0.1.2": {"name": "named-placeholders", "version": "0.1.2", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/sidorares/named-placeholders"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "^1.21.4", "mocha.md": "^0.1.0", "should": "^4.0.4"}, "dependencies": {"lru-cache": "^2.5.0"}, "gitHead": "bc547444bb049415762c0749699f372bd9ba5fcf", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders", "_id": "named-placeholders@0.1.2", "_shasum": "7ad139c223d821a216ed3e6ef40760e291c8c8be", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7ad139c223d821a216ed3e6ef40760e291c8c8be", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.2.tgz", "integrity": "sha512-kKmyKxui5Re4a1Jw5E1whkGEsS6Mh0GqbONJwhjqlT3UjYrbKi8WjghaiJNRWuzIcyq7Sen6MMGSadTHmYl8LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMU3kwCbF/8UQMU11yIReKU0rN1pPWSG+o+ARaj+MySAIhAKAlleM/MKtNshplKmQCwZ3Q8UNZ8eoWaL5JCmcVtqOQ"}]}, "directories": {}}, "0.1.3": {"name": "named-placeholders", "version": "0.1.3", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/sidorares/named-placeholders"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dependencies": {"lru-cache": "2.5.0"}, "gitHead": "78fb4ea2b3b5279e4778bd96c232a80fa18b0511", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders", "_id": "named-placeholders@0.1.3", "_shasum": "353776ee259ad105227e13852eef4215ac631e84", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "353776ee259ad105227e13852eef4215ac631e84", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-0.1.3.tgz", "integrity": "sha512-Mt79RtxZ6MYTIEemPGv/YDKpbuavcAyGHb0r37xB2mnE5jej3uBzc4+nzOeoZ4nZiii1M32URKt9IjkSTZAmTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCURVtkqLMwvTPCd1VJotl20CsMya2+taKSEKm2kdfedgIhALeFPYuYL1RX7kmdaHEizlVah5gRBekjW6gWsAq5ZO6n"}]}, "directories": {}}, "1.0.0": {"name": "named-placeholders", "version": "1.0.0", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dependencies": {"lru-cache": "2.5.0"}, "gitHead": "9d6529e134d5701c4827bcc5e74abd30b7f3f8ac", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders#readme", "_id": "named-placeholders@1.0.0", "_shasum": "7116209b234655ec698755932592912aceb516d8", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7116209b234655ec698755932592912aceb516d8", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.0.0.tgz", "integrity": "sha512-foJkCS7Cg8K2lnaQEU3M/7WnTq+RyPjOZZD1sgFRY2FTvRZ7Vy7jB0uU9Bb41ZYpl0n6VIvneK8G24xu/9leHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiGwX81oZZfWtEMeU56zXaK0GsRSN2/E2fTK0dxb0vqAiEA1kH1XV6HqTGzVbg1b9LRVQ9mzmqAB7Ttb6/yfXe8SYQ="}]}, "directories": {}}, "1.1.0": {"name": "named-placeholders", "version": "1.1.0", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dependencies": {"lru-cache": "2.5.0"}, "gitHead": "ac3b6d83331600523ffaeb6d23179e8f19407997", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders#readme", "_id": "named-placeholders@1.1.0", "_shasum": "d23ac8d83a399ef0f33046522594e2f0eb0af34d", "_from": ".", "_npmVersion": "2.14.3", "_nodeVersion": "4.1.0", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d23ac8d83a399ef0f33046522594e2f0eb0af34d", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.0.tgz", "integrity": "sha512-zpW8KFscm4MymGWcl0ekp/LJ/NJq4Ubc1e4fYDkVhCBfYPQyaA9jiT4Hpz6HzkiVPApNRmdA9OfGjggjnCiqHQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCak1TDmHCuR9XlxQWixeOheQyjI3dLkrrpt2wQQozvlwIhAJa73jE6OcXckdioeJZxGItK/PEO1ODeCvd2OCxrHO8n"}]}, "directories": {}}, "1.1.1": {"name": "named-placeholders", "version": "1.1.1", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "keywords": ["sql", "pdo", "named", "placeholders"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "1.21.4", "mocha.md": "0.1.0", "should": "4.0.4"}, "dependencies": {"lru-cache": "2.5.0"}, "gitHead": "89fd88865c39a6d825a0e2b54c5ce14e493c3876", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders#readme", "_id": "named-placeholders@1.1.1", "_shasum": "3b7a0d26203dd74b3a9df4c9cfb827b2fb907e64", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3b7a0d26203dd74b3a9df4c9cfb827b2fb907e64", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.1.tgz", "integrity": "sha512-6vPe09wFoa2xILUP20S3wO8IC85owY6RB+Zt/U52mKLcc60EFwOEypWpiOtpfWN88otl1BbaqwSWf8ymrcSLBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwwc1YYdEuLQm6jI1yYEVgf+MqTQMU07EvdAltB9jaRQIgDdVRS5erMZHGV2IKa7S7b95ImXrjQJB//ziNxdYI4Xs="}]}, "directories": {}}, "1.1.2": {"name": "named-placeholders", "version": "1.1.2", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "keywords": ["sql", "pdo", "named", "placeholders"], "engines": {"node": ">=6.0.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3"}, "dependencies": {"lru-cache": "^4.1.3"}, "gitHead": "d5134a305e9cdc6604153aa08275942e15895936", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders#readme", "_id": "named-placeholders@1.1.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wiFWqxoLL3PGVReSZpjLVxyJ1bRqe+KKJVbr4hGs1KWfTZTQyezHFBbuKj9hsizHyGV2ne7EMjHdxEGAybD5SA==", "shasum": "ceb1fbff50b6b33492b5cf214ccf5e39cef3d0e8", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.2.tgz", "fileCount": 5, "unpackedSize": 7688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4lejCRA9TVsSAnZWagAAsr4QAIck7Q5XhmoCGX/KC/1z\n2sQk8jespnFJXfHnghqmdKXefQ0e/d7ezBaLAiTMbAM9x60RFaon3DaXpiNj\ngnPvGJp2JdLm3x35E3h4HJVeD+iQa5KB+ZdPEPq+HaF+sv1s5xq2V7oOOMID\npGuT/roDp1nS2Cg6ybZfgRvld+uPCxHVZS6VFt0BXXzeo0UiVJfzhOhVsyNm\nwCYuaDHT/BCGWd7qFRO2PGzdpaqQPivUJwU+iuvCxM/KzRYXzpWNpk2e7Vbh\nbmgqAgrVMYC7AkAsVnY7CcM/2mrsRy6IcS5zqqL3aNJtx4kIoyaDWxw2YpIw\nFmQOq/u5i4sB9iw4wob4ac11tYIHKfjIvw+0gHNx3JzrZV17zyWZQ9WgKAtM\n5Bqi1WChljfhLMg/fKuslsU8xnkqccKGaHYTTdZcWBAawqNO3gNEC2x1ly3a\n4Y/4bZ83xq02dLbRuJQSB1WoL4v+4tEnBnEuDj5UHQEC6jzFkXWz1zYygExE\nUSLyrC1aOzmIl1oN7BDTA/BK1HMkXZ0T5T25/tqPICQAs9TVWueSjfWi5lU4\nue7m5HjywHicHbAnpbXV2wn6TrzKfWwL6GwkUf1SRPBV7it7BfmyDMAxmN4z\n0omWS0KEnDx0syv4gKptlPP3ZdxR6rBbYTL0bgevk0bC/Bfuk/kuCHAp8F+W\nYftv\r\n=mENo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB/DxTE9CkTzr7Oq/Mg/l3we9MBcVnB6dqPzF+m8yXwrAiEA0s+timX9IbI4ZNG8jsSlIbhvd2TRlvixYMI1rAx0Y+8="}]}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/named-placeholders_1.1.2_1541560227018_0.1282692527775109"}, "_hasShrinkwrap": false}, "1.1.3": {"name": "named-placeholders", "version": "1.1.3", "description": "sql named placeholders to unnamed compiler", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "keywords": ["sql", "pdo", "named", "placeholders"], "engines": {"node": ">=12.0.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "^5.2.0", "should": "^13.2.3"}, "dependencies": {"lru-cache": "^7.14.1"}, "gitHead": "8b78fcf6142355a2bbe6835a9e49caa9d1550ef7", "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "homepage": "https://github.com/sidorares/named-placeholders#readme", "_id": "named-placeholders@1.1.3", "_nodeVersion": "18.9.0", "_npmVersion": "8.19.1", "dist": {"integrity": "sha512-eLoBxg6wE/rZkJPhU/xRX1WTpkFEwDJEN96oxFrTsqBdbT5ec295Q+CoHrL9IT0DipqKhmGcaZmwOt8OON5x1w==", "shasum": "df595799a36654da55dda6152ba7a137ad1d9351", "tarball": "https://registry.npmjs.org/named-placeholders/-/named-placeholders-1.1.3.tgz", "fileCount": 4, "unpackedSize": 7152, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGFa/QP4Ggp+J38zy7PHMgAJVz4E/6T+PcHaWmMt0GOAIgI+4RTnNzj4AzLbQwCXBSbyPvvSxeXRDRY/7IfoEhAUM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjv4HWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlHxAAhfWTcezycreVqIUOzHvggi8x/SOavX+aAS+wSr9aMLwZqbmc\r\nwIWyAhsqpUmye2QUx4n1vTLPlmJWjFsCOW0iHl6wch4k7w1NIIaRwJj5H6w0\r\nq5jnuXIGTucUWYRqVU4HigSxIwpr8OIM5Qo1DEdJb78lfjLajFbBJ27enwVy\r\nTWfwJZ1/gzEHQq02bp4+K15ex6f0tI0sLEtsl6CTtDSIq8XCaevr+lThkMg+\r\nvR8lScd+XDk2ZrGGiUGZQbhsaQZkCaSHNHOoSeSYKt3TlCxILoOaA5qsxXxh\r\nsjUuS0lkcMiaNErsh/9D6h5rVOwEdLS5StG13D/QSnE4M4RmVU3X/2EwoIM5\r\nT0ZmdemI6qmk/RlKY5isFv18kuurnn0QfhrLQNCTo3iAJ1njxadkSwXMg/TH\r\n/ofSUi2Taa31bnLL4n7lgd8qmp5myVZi4nb55CBEflFCPJFhbFfq7rkEC0Yf\r\nzl7p+u1/AEWPw5q42PFWLpbMubW1if0DgtHKWDh4yjqPaXZSK5QxJpwtscvG\r\ndJgF6CQDyqFkQObQO4NqbNsSSIR/ZISmRt0C41XZ0RnztBy57twweiJhScJ7\r\nhaY6LP08tYnEfVZnIqD3IcQAcClORdcMwHMjychL/3QWTaHzu9UjwU+6ETHP\r\nhNb2iKC6TdPJESM+tEeZCAeChh0HfDBCXaM=\r\n=1Y7V\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "sidor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/named-placeholders_1.1.3_1673494998658_0.9036744692151635"}, "_hasShrinkwrap": false}}, "readme": "[![Flattr this git repo](http://api.flattr.com/button/flattr-badge-large.png)](https://flattr.com/submit/auto?user_id=sidorares&url=https://github.com/sidorares/named-placeholders&title=named-placeholders&language=&tags=github&category=software)\n\n[![NPM](https://nodei.co/npm/named-placeholders.png?downloads=true&stars=true)](https://nodei.co/npm/named-placeholders/)\n\n[![CI](https://github.com/mysqljs/named-placeholders/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/mysqljs/named-placeholders/actions/workflows/ci.yml)\n\n# named-placeholders\n\ncompiles \"select foo where foo.id = :bar and foo.baz < :baz\" into \"select foo where foo.id = ? and foo.baz < ?\" + [\"bar\", \"baz\"]\n\n## usage\n\n```sh\nnpm install named-placeholders\n```\n\nsee [this mysql2 discussion](https://github.com/sidorares/node-mysql2/issues/117)\n\n```js\nvar mysql = require('mysql');\nvar toUnnamed = require('named-placeholders')();\n\nvar q = toUnnamed('select 1+:test', { test: 123});\nmysql.createConnection().query(q[0], q[1]);\n```\n\n## credits\n\nparser is based on @mscdex code of his excellent [node-mariasql](https://github.com/mscdex/node-mariasql) library\n", "maintainers": [{"name": "sidor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-01-12T03:43:18.899Z", "created": "2014-08-30T08:21:05.775Z", "0.1.0": "2014-08-30T08:21:05.775Z", "0.1.1": "2014-08-30T08:24:07.878Z", "0.1.2": "2015-01-09T03:19:03.243Z", "0.1.3": "2015-01-09T03:55:14.864Z", "1.0.0": "2015-09-14T01:16:48.665Z", "1.1.0": "2015-09-21T00:45:05.973Z", "1.1.1": "2015-11-25T04:47:08.681Z", "1.1.2": "2018-11-07T03:10:27.123Z", "1.1.3": "2023-01-12T03:43:18.816Z"}, "homepage": "https://github.com/sidorares/named-placeholders#readme", "keywords": ["sql", "pdo", "named", "placeholders"], "repository": {"type": "git", "url": "git+https://github.com/sidorares/named-placeholders.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/sidorares/named-placeholders/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"asaupup": true, "nuwaio": true}}