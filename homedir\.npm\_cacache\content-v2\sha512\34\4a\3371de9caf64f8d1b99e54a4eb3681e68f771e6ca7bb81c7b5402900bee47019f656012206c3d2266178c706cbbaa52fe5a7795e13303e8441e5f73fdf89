{"_id": "css-what", "_rev": "32-9dbdf1c428ab9e2bed0cebf88dc35944", "name": "css-what", "description": "a CSS selector parser", "dist-tags": {"latest": "6.1.0"}, "versions": {"1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "1.0.0", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-like", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "quotmark": "double", "undef": true, "unused": true, "trailing": true, "eqnull": true, "proto": true, "smarttabs": true, "node": true, "globals": {"describe": true, "it": true}}, "gitHead": "d54c2857acbb22d56190fc998b48744597ddd730", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what", "_id": "css-what@1.0.0", "_shasum": "d7cc2df45180666f99d2b14462639469e00f736c", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.0.4", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "d7cc2df45180666f99d2b14462639469e00f736c", "tarball": "https://registry.npmjs.org/css-what/-/css-what-1.0.0.tgz", "integrity": "sha512-60SUMPBreXrLXgvpM8kYpO0AOyMRhdRlXFX5BMQbZq1SIJCyNE56nqFQhmvREQdUJpedbGRYZ5wOyq3/F6q5Zw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFQKgP2CYm9+rPch7jXw6YED49vKC0ucEEllZdbnFSohAiA6SQhLk64K71MzGWz1T5T5NPr0p21KALQZknoKsW6krQ=="}]}, "directories": {}}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "2.0.0", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-like", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "quotmark": "double", "undef": true, "unused": true, "trailing": true, "eqnull": true, "proto": true, "smarttabs": true, "node": true, "globals": {"describe": true, "it": true}}, "gitHead": "f46f4aac8ce43a6be6279d8894e7bc1e4195a1db", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what", "_id": "css-what@2.0.0", "_shasum": "107fecc449dd4e4a5df950262364def6014e5f30", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.3.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "107fecc449dd4e4a5df950262364def6014e5f30", "tarball": "https://registry.npmjs.org/css-what/-/css-what-2.0.0.tgz", "integrity": "sha512-JEewO/j97r9jFEWvgGUiK6M9mqTJtu/xjueRn1X5Eb7Gs8A+Ic4TFQPNyS0O3rX15Wtwb6S/ZJAlXrhMEUwjEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCELmJATU9vX1rAYXI0d2zSHZkcisb2IaB8IQaUjBjzFQIhAJDvJhdJAkkYjUGXwSK4xoCg3Gud0D/mSPwqOQ2vftBP"}]}, "directories": {}}, "2.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "2.0.1", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-like", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "quotmark": "double", "undef": true, "unused": true, "trailing": true, "eqnull": true, "proto": true, "smarttabs": true, "node": true, "globals": {"describe": true, "it": true}}, "gitHead": "e411927d535b5904bf05cf32ba68825d8071a061", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what", "_id": "css-what@2.0.1", "_shasum": "84546261ea504e3cdf0bf1fbc3436c9180fce4d2", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.4.2", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "84546261ea504e3cdf0bf1fbc3436c9180fce4d2", "tarball": "https://registry.npmjs.org/css-what/-/css-what-2.0.1.tgz", "integrity": "sha512-u1AG4aj8AVpG+VwVhSIpfD+JST+N9Bd3IDcs6ZvUwoDUxxqMhx3MA/K23XYgq+lrfRNcS9BK2tZvUmSvcSQxtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+xBSIfsf4DGo2dMcS+lojfUuIlq7v6NUtYK/zNKMsEAiEA6Hec+bbitC8GHSyxvPvDkwXEMe9zk4XR3L3aDJcUYM0="}]}, "directories": {}}, "2.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "2.0.2", "repository": {"url": "https://github.com/fb55/css-what"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-like", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "quotmark": "double", "undef": true, "unused": true, "trailing": true, "eqnull": true, "proto": true, "smarttabs": true, "node": true, "globals": {"describe": true, "it": true}}, "gitHead": "1ddb673f1c7425dfe286a2dcfd1609d06e633105", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what", "_id": "css-what@2.0.2", "_shasum": "555f87ac386768db09cb43962571fe851409bce8", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.5.1", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "555f87ac386768db09cb43962571fe851409bce8", "tarball": "https://registry.npmjs.org/css-what/-/css-what-2.0.2.tgz", "integrity": "sha512-iYcSiAIJNZ9qmGWafk89UErocz5MvaRbFZL9t+rcmup2o0nTG+8aU+GaoTwqgvWkY62aiz2EWNtGxDzbu1BPCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEipJktsyIpYcUhfMQoMhx3aBwUKlgOenVxmfGYNLseGAiEAyQCL5T0tt1W/TP61X8FqsFLMGlixpkODizj4rbZRKfc="}]}, "directories": {}}, "2.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "2.1.0", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "./index.js", "files": ["index.js"], "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-like", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "quotmark": "double", "undef": true, "unused": true, "trailing": true, "eqnull": true, "proto": true, "smarttabs": true, "node": true, "globals": {"describe": true, "it": true}}, "gitHead": "fd6b9f62146efec8e17ee80ddaebdfb6ede21d7b", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@2.1.0", "_shasum": "9467d032c38cfaefb9f2d79501253062f87fa1bd", "_from": ".", "_npmVersion": "3.3.9", "_nodeVersion": "5.0.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "9467d032c38cfaefb9f2d79501253062f87fa1bd", "tarball": "https://registry.npmjs.org/css-what/-/css-what-2.1.0.tgz", "integrity": "sha512-HtdI8TqiYjVrBI0C2VLF8fwphIa49EaHPVo64hrX+QHCPBtjp2FW1g4+jNeC6v7bBSr2z7v2USnQ6GU+VPOzgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+Wo9dPU4FmFWtFEXyszTXSjSODumspA2894NEdAIUvAiBnQxCHcqzJ/xYEU/6qf/4kyv4zd+3aMHpBYiNZiYAiwA=="}]}, "directories": {}}, "2.1.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "2.1.2", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "./index.js", "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "undef": true, "unused": true, "eqnull": true, "proto": true, "node": true, "globals": {"describe": true, "it": true}}, "prettier": {"tabWidth": 4}, "gitHead": "b2a2117cc0732f05d3345e15c1884645c8be280c", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@2.1.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wan8dMWQ0GUeF7DGEPVjhHemVW/vy6xUYmFzRY8RYqgA0JtXC9rJmbScBjqSu6dg9q0lwPQy6ZAmJVr3PPTvqQ==", "shasum": "c0876d9d0480927d7d4920dcd72af3595649554d", "tarball": "https://registry.npmjs.org/css-what/-/css-what-2.1.2.tgz", "fileCount": 4, "unpackedSize": 10642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzPsyCRA9TVsSAnZWagAA74oQAJDdEHbEsWHqxh4LvNlZ\nxpjs3CpREixkxvmJiXIweqnnIPzUcTGWaouU0V4zZJxJep2W41jssgxm4U45\nuM4moPFqFPxJOwDrUlHwmg2dm9Nk+oOhos+UUsWq1Ii7NpT2BtYW4jYuYrB5\n2Bsbn/4dNL03nMFfx8EMAbPXIDG09bZ34mee/4WQaXhLqazbKDHDoM2kJ2Q4\nmM9ZuP3fmahvrfQD0q1KXF4Kdfxo3I1Z5x1ecXZfc5l/6QodTqJDY7Un+HGS\nbZnUlQRg7j/AcLs7Ys0UVltgJX3/h4A4es2klJm5x3F1aEJIIvJsyF2JNnyR\nC8rhdJUl04okvTXlimnP2zomxy95Hv//+Kqh89Aar9BYpkfziq5HuEBJUqZI\nBuJQjnX0SRIoGhGD8Ti6VwvhyGgsrFi5V2znv24RnUUu68KESPZpltjTbod4\nrG5pfdiFDaBKzfjVlQ2JRdJRmPRFgL9dYlIv9L/Xs8bpIXG0E9i2EpqNoZbv\n34qECWkUlEm+pD5UY5MkwwIZ21j82OaONke7OpWtFw7rA5R6GPS5fHPGO9cc\n32AxBkaTmXvdMZUSCPSfX4KYoNLdWNKyV9PVodLmMGne7+sfBL2Oa+GAID6/\nbr9vf57YyPw1wvHC+LjNQeJ9B/DAVSGe96DabyVEfF7zCpPu+EzCsSkUMvyH\nwy73\r\n=NcCw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGuwEaWtVJ3+OvyO8SFy73aR4nw9rI9CbuSb21VTnoTjAiEAlLvZZrXyLinLUNWl0XKfUdtk+27jBAiNugouVzbLSTQ="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_2.1.2_1540160305964_0.11466746951668294"}, "_hasShrinkwrap": false}, "2.1.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "2.1.3", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "./index.js", "scripts": {"test": "node tests/test.js && jshint *.js"}, "dependencies": {}, "devDependencies": {"jshint": "2"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"eqeqeq": true, "freeze": true, "latedef": "nofunc", "noarg": true, "nonbsp": true, "undef": true, "unused": true, "eqnull": true, "proto": true, "node": true, "globals": {"describe": true, "it": true}}, "prettier": {"tabWidth": 4}, "gitHead": "2db00ca221922c5b5131d798614aa043f2f6f80e", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@2.1.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.4.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-a+EPoD+uZiNfh+5fxw2nO9QwFa6nJe2Or35fGY6Ipw1R3R4AGz1d1TEZrCegvw2YTmZ0jXirGYlzxxpYSHwpEg==", "shasum": "a6d7604573365fe74686c3f311c56513d88285f2", "tarball": "https://registry.npmjs.org/css-what/-/css-what-2.1.3.tgz", "fileCount": 4, "unpackedSize": 10642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZbIXCRA9TVsSAnZWagAAkxwP/jhu5ZCkQyOjOB3kH1T9\nOGsaKA0ZYCgcDld+KeDuoex4PDyxcl5//x3mybVmd/tLPZ5n+4SXC3Zf5FEZ\nkL3Mri2unAhYdLD5E+s/NFgMtSsABkKWct2Fna0VI25eapHnc61znrZW9pWG\nM/WRlkj01kgQFWM4m3I33m+wqSpLzkm9zIr+bi7zwmQ2zHO+odbb7SobBh56\n0srZRJCaI6xOutpfyLEdWDBY7ZFCYZSPbkVMnsTKZ8LYzKWmghDvLfs9GJz3\ngcBC87uYX8qGjnmWOnMUrmxdznUm8TtIKoh/crGd5qEX9UBjxj5hrugEFhzp\nBum7w4pWiSbm3JIyom7FLP5LH4JkrfgGjhu3ExlEtan5gnmU7DiF76gVGYnd\nQ5wRDJ6E8N3AbU+jHjE9hf7mkd1jNzCoV+DL/M6BPXKR0fA8GrfFBZONkNCR\nXgYeLcA2q62mwvlIUcZC8EPTSVKkrR0Q0DdyWo1snjY8JnntyIooinnY9VCw\nnMwv0X67hk1gbCdduuRR1S4zrjo/QlOBtu3mzJ43d8PsGjvuCvgYN8gu4fNl\ndgiAvymKJmf8lm/zGciNz/I13kpZLwSxtV1s2nWstwCMi16QbKiPHD/59OZm\njc5rNhhTq3//3e6NZ0cjDxI5nehgCxgA+jmgegV3L9c7Ga7v+vW009V0WixX\nJQiO\r\n=wylv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDU/vV3FO4lpZi2vSJN0si8I0rbV6AQyQPmgkKxX6igBAIgOuyzSp/wKMzPSFdJjjToi4b4UYQAbMP0tzzHbMQqQ8M="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_2.1.3_1550168598043_0.9607567623567257"}, "_hasShrinkwrap": false}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.0.0", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run ts-test && npm run eslint && npm run format -- --check", "ts-test": "ts-node tests/test.ts", "eslint": "eslint '**/*.ts'", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.6.2", "@typescript-eslint/eslint-plugin": "^1.12.0", "@typescript-eslint/parser": "^1.12.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "ts-node": "^8.3.0", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "gitHead": "7009b7ef2c909aed216797412977dd2ec0b16206", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.0.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-9pUHCef12ZcOdCdN0+KKOkBD6ILCmTcXIdTmFPq1750xEl7hGIAbl5EOAeUK6O/RQPmc72SdxXYd8Bw77ciojQ==", "shasum": "dd631b3ef8d4714917a1de033186181cc7bad9cc", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.0.0.tgz", "fileCount": 12, "unpackedSize": 20416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKjGfCRA9TVsSAnZWagAANRMP/1MRDH5sue9C7KJPmrf7\nXq2TomdxiayOL1qzGTpj8BXLe32SFdQJr/jGqIvxWNnibsfoLnoAVm9NK7rz\nTFC+ea8mAykrf1nN+QZ0gh9tKc9fb/vw4GclKe6RZvLxQ7jgTtwhyupnB9W/\nI8BicXeJv9jSlVQDSRhmA5LTq2vAFES6ZHjAgSalhqrFqEywKVh4jFjAgY/g\nmXR9Q5qCqPXvRR+l1b7WLdXjubr9Mfp+FNyz2raD74UoCQZ6G7F19XobI/us\nf1NtAX+5hGlgiU9Ah4cjatkF41RtuL8VttrChpT87XfDHl9taZ1dDkS0rKo2\nswiaQvcoNcPbzUzU6XxbhOUE+qFsjZo3r9cZpRaQVwfrpb1vnHdxAYlu9thD\n3CSfJ++BuH3ktyjnGngU2Lciesrx8j41G6OYsFxvIWVLq2XZURnRuwfLMzEY\nY63hiPQ5+Wj3u/lHugaoZfOudAbU0FeeIonLnr543nFTwHNAzNhlrGFAqQIL\naKNhxgWqBc/rIU6SUIoYGq7M/0kn7ElS/fam/5phubiF/4DJ6cbo21Wc4J6b\n9QQGUni4LkGd4RuxqehZ0s1uEuvns7l8y3n1TPb+820z393g2fe+zo5F+EqY\nXcJQ9/27FCigwLEs7wQ9pOOmy9k/OAT5JCu/F6VIbgQ2S0V8/D8QkKTQJLzA\nP+fo\r\n=pKVS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBNVDlq0qCPU5bmAvqv1L/8QpFCKECcKNQVgH5iJvWPaAiAGgOgZBuVv/qXWx4Y/IGREyDQuybGjueO1zsBbjnuzUA=="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.0.0_1563046302127_0.7712229632341447"}, "_hasShrinkwrap": false}, "3.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.0.1", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run ts-test && npm run eslint && npm run format -- --check", "ts-test": "ts-node tests/test.ts", "eslint": "eslint '**/*.ts'", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.6.2", "@typescript-eslint/eslint-plugin": "^1.12.0", "@typescript-eslint/parser": "^1.12.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "ts-node": "^8.3.0", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "gitHead": "00ea1c90247c25142b79a8a284684904cbaf4471", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.0.1", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-ep6ZHGmjY/z6DybNzlu3QmC8+9KgKjSvoPX7uE8Ue9sFx4SDsTOwOMPpdjTNcASuhP9C1Yiuvyw+s69AuPUuLQ==", "shasum": "880b1ffee440578296ff27987bad7a623cc96350", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.0.1.tgz", "fileCount": 12, "unpackedSize": 20399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKjKkCRA9TVsSAnZWagAASUYP/2o/AS1fbbhBSRakHzuf\nnsO6iYrdG78S7ftspu7QhcuI7pMIFWylvQedxcGCFdaT26RLIjYIdTWqcint\njqve27dOmq6vYJrpEZdmYKxhTIca+9KnSY5KVKVbmIGhyCSRYvpF6F8Yfcxw\ntJ4qxLqWh9ayOo5xO1GwxjJGuJJ/jkcp4ck14HrKd9ehkXV5Lietpxwn7puN\nLupP+GcvvcNyJQS1psMA75xmwAec+6aLwwXy5v8W7hl6ucczjkQwqfV51mCx\n4pK14kdjjJ6sSsMry4Cbx+baJtOFYeu7vXi+Ds9/8gJVaohIFCH68S23BMCB\nKGpgp1ajrSN8DDoUyOcH2t6AxmGLkUGUQN/A3dKLiFH8YEQ220o8ALApwkfk\nnC86mH/UGRzmzlt0VpNjapPCl0hI9GVdNlIDFTw6vhhYmUc5j0PuC8OBR5d/\nx47V3SEceLZ04bz5Kwo/LPMUc+TFleBMqQlTXPdVMsB+O9kk9kKH/NMDiADk\n+zQzqkAkD+75nXPOuxVBmjD87NtQaX8pTX3u7DSnfJCxbUFAfxAIuZcoZEOp\nAW6MuRMrhOxSml7kyUQIHOBuvQPrRN7DfTYy2exg09lsZmxrriHfq2dG4EMe\nAXOkVzoFmTl3/Yrx/D/9InUjSSMr1x626X3O9tPAenef+gEW3CeGx7ZFI++r\nJ0Se\r\n=IjuS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFZui04odOH+eW8Zc/fol+I75GPfmL0EwIBk0xGlUnHtAiEA+7RBLrLmB6eCHSVmGyCsNR6zmh35yotp7Sdqitgwuw0="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.0.1_1563046563670_0.42916446731205693"}, "_hasShrinkwrap": false}, "3.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.0.2", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run ts-test && npm run eslint && npm run format -- --check", "ts-test": "ts-node tests/test.ts", "eslint": "eslint '**/*.ts'", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.6.2", "@typescript-eslint/eslint-plugin": "^1.12.0", "@typescript-eslint/parser": "^1.12.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "ts-node": "^8.3.0", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "gitHead": "9b9a4d8470334559a2b193f9e1847ef1b823bacf", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.0.2", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-racDpdGaKHD7qCdzAGugPEplAk2jOX3XtY6Px2HesT07Us78eA/badqq8KTEV8nrH4j9IRcOq0kRs0Iak973Ig==", "shasum": "655948f53a48fb567d735bfda5b5cb9986f40b37", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.0.2.tgz", "fileCount": 12, "unpackedSize": 20416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKjMUCRA9TVsSAnZWagAATD8P/RAGVIdM3oXzftZndEBf\nQlZ8kOZluLgWLgE+yGkU6Kisl70pUwOC53jyvKV7HHu7RnsPNPYqiiP8T3nx\n7ufVZ+OPaVG+evXICG9Cs/KrWujS0hnWv71THDCsPqgXii+FHMTxChvmhsfa\nbrYHtcw57C7ZLs49FUMsSexHErQn3lKSSrkOihYhsFl0SoP04HkDDHoLc1Ue\nAw9N/3KuvxPqUacUg9aM3y47tm2UGeTJg8ISa4y0zrVho6U1yrS3z/heklCQ\n0a0ohXaKQfDb3lnLCbUIl4vS9U6tbkKbNnayVPtn3t946QpO8Fl4S1rupcDa\ntRtGXCizh99ZTA0ce+TehhtbvwYAcgJjGBQd+88eiX0An2DzCbf1pOwi/W0h\nxBgqkbxtTWivU3c+TEnsmWLbO+NOVm6jSaMFu5+J+sxav3SpyjVl5B8igQ2w\nJWce3CPRpzAPU8EEoWII4CRxjoFoAR2O5Tfn2fcsq37Pl3Z59JnOcyw1cTh8\nfyHNOSCiv8MAeszdjrHId/Wsn0ICFndRzQ/Ic8dEF26v+qLq0rCyTkR+ePic\nuizgQoHi/baJm53BgwmGFZC2P4nc6GkDIHYPuHvyEp4oMpZr6ISZkA0zP1rx\nTKqccMtAPY/sZ9VPfayBu3dMwlijJQOGS6K4IZg6yHs+2PzqyOQaRzwqJNzg\nqiSu\r\n=eYl9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhQb+i+1pd6f9WbIOD75QqaMFhJYAiVFrKqAx64s8j6AIgTWR3aD9kkN2GutksUaW0omzscAhjUq9U41dATxRP/dk="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.0.2_1563046676214_0.9501119600956538"}, "_hasShrinkwrap": false}, "3.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.1.0", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run ts-test && npm run eslint && npm run format -- --check", "ts-test": "ts-node tests/test.ts", "eslint": "eslint '**/*.ts'", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.6.2", "@typescript-eslint/eslint-plugin": "^1.12.0", "@typescript-eslint/parser": "^1.12.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "ts-node": "^8.3.0", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "gitHead": "e20b8a61a31cef811d3471d35c3de68a0c1ce9b0", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.1.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-S069NLquxuiXFk89WTK+gm2OuL6VZ0Zet+Ob4nAY/rTR6eohCgjDwNUeHbUUilsNpr/at6xFlp16ALNChGspuQ==", "shasum": "4254ec60a07808f383f91cdcb2e83a164bbe65a9", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.1.0.tgz", "fileCount": 12, "unpackedSize": 20564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNl6pCRA9TVsSAnZWagAANekP/0/wK1JGOuH8Z1M9Q866\n1ZxfSp4i/NViBrJM7sSW6mrPtrlVmOFwQwxLVvOA9HHFmC3RmAdQEXCusbn9\nUg0XKfoLJbHuxxx1unh7YqLtP2Oy+8daYl0yHmzjPrO4fDm/hgffMlqJXXnw\nkUPD+g5ag9wz9hD0GKz5yJ/D3nPSikLvr8bWmiVNTv1qOhlpKxKjkTBVEhOt\nH/GgtEVFXWv4paWKVgehKGn0d0InlHRI4gcb9ODIKRBEhrUW1qquEC4L1dMl\nN6f2Ine9f/d+5MeDKsu6oqL8Sz8xnjh2QvyePuzyVwWBi6LBN5geJbRnaN0w\nLPRQei0O1LdGE6netERnZuNuq4QvDRKUssGsr3lar78bKkcdiIMl+OLlipU/\nuQrohUq0rqX7OwyXGM8qxIzSp0p/6Ys052TP1v4G6fbQhBnhJA6GzDxxCLv+\nqvwOAnOB5ezpFaCT5hW1LwcDtoydiW31pRsmgi4IqmqdrXpFEYcBIiC99zBF\nybRbJZMDHYX0/5BmFNPeJEPXzEInJO0jucfkk73mMr0r6Xgrx/xvoj5zmmep\n67lOjw5XVMkGr4IGEPNwampW8H9Q5/T8jT2hXfuzRBrMT2X8hs9XuGLnnRqY\nkHdJt0Ca4Uk4Re+yBl5bt+wqMHLlpGNo5SL9mJEVEeL5eLOt1nzH5ylcSGOu\nPFCR\r\n=Kkl0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWIBI6uORBBHgYae5GBPLqsr0SNIPSugvLsZsK1Mst1AiEAkJMhK2fEFDNy5e488kh8VHiznU4Vw5QKCR04AjlWYRc="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.1.0_1563844264802_0.4718894424955744"}, "_hasShrinkwrap": false}, "3.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.2.0", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run ts-test && npm run eslint && npm run format -- --check", "ts-test": "ts-node tests/test.ts", "eslint": "eslint '**/*.ts'", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/node": "^12.6.2", "@typescript-eslint/eslint-plugin": "^1.12.0", "@typescript-eslint/parser": "^1.12.0", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "ts-node": "^8.3.0", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "gitHead": "fd814bbd3869fb9568063a510697a059c5f883d1", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.2.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-lukqnlbswsPmDZ5+ViDBCcrk+1fyPBA+ZoHSAQhRuEeXBKUb3Lj2kcTwMqoiFrJAnEeO9u3Oc8X617SUm3apYQ==", "shasum": "0ce44ada45f97bcbbec450563a23ef04ee2572ef", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.2.0.tgz", "fileCount": 12, "unpackedSize": 20808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRI3mCRA9TVsSAnZWagAAoGUQAI6bqQpceNTGY+OSAY8n\nzSfHdR0eDqp2fS7dCqa+b7xPhPrQDbBaBwTxBEsv3KWZo57dTBNhaLs7eNvP\n7IBmjvIjkr/xB/M8lq3C12onNvUxi28j5bYIEFv8RsZAAAR8zhB06L1+b0Qf\nHNQadL90Dnm4khHOHnCW+qqQ9g0yIAdLsID9hLKKanefRIRtION6Rn8jMcNb\nl8hEX1fQWGmZcWZUfbC8rYhV6lMQgz+9YzFUZ5MnAIT/oP4eX7ra7jDIrnkR\nfx4cNSb4Gl/VgepK6fusR4gHaCnAgLZmxvANSy3e1ayIK8TI0xvVXvyPWUv2\nNU8lcqGQnSBmFnOrJUJUMCbDg8xbubX13FYaK38KdBqB4gysm0u0d+g0T4sP\ndaSRqa/P5FF0KpXncFucDUUA2k6oTjv2hTs3hFDM8Ks05+1cwRhgQuBomtzU\n+MkHfpfb7+BnNT044aJMZsZ+2czkkUJktw+j7IdUnlbGfbjniEXon4syEV5o\n61R6RMAc3rpWFka2lRhOQ6GkFIyyRUKc55PThQywjqaMiLZzDJfpmMTRGdWE\ns1j6lyS9QfbZRMRGpxTsz1Ybl+925ugDXBDMyAr3GGfDasVaAg7bnJedCtCU\nt2VHC26nleXh6b8kqAaDHG/tkoa07YCwvByLQp/Ceh6caKEQ/S3tTrwzSUqd\nADgJ\r\n=yUcW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJ+kk4DRXc+HRtgAc7UT0IPhfmd7NuELAFNC+xSYZXyAIgfpkrFHl95o7q9wRzM3ttFeO7NlcorU3FqpZGtWkEBeU="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.2.0_1564773860738_0.8302602418166052"}, "_hasShrinkwrap": false}, "3.2.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.2.1", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint --ext=js,ts src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.16", "@types/node": "^12.6.2", "@typescript-eslint/eslint-plugin": "^1.12.0", "@typescript-eslint/parser": "^1.12.0", "coveralls": "^3.0.5", "eslint": "^6.0.1", "eslint-config-prettier": "^6.0.0", "jest": "^24.8.0", "prettier": "^1.18.2", "ts-jest": "^24.0.2", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "8c5462a9b982bf7ce1b6cd8fb3bb1b90843605ac", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.2.1", "_nodeVersion": "12.4.0", "_npmVersion": "6.13.0", "dist": {"integrity": "sha512-WwOrosiQTvyms+Ti5ZC5vGEK0Vod3FTt1ca+payZqvKuGJF+dq7bG63DstxtN0dpm6FxY27a/zS3Wten+gEtGw==", "shasum": "f4a8f12421064621b456755e34a03a2c22df5da1", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.2.1.tgz", "fileCount": 12, "unpackedSize": 21453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdx0AoCRA9TVsSAnZWagAA0HkP+gPjZDxtTolQtwIwDXY9\n2vjMzbpBekrKkFImS0WhDT1abDayMxb7kiK/9GhjRMdGmQOdq/vaJ5Ws0Or7\n5MGAwjBX4iIQvYRXuY/twVyXvj9/0NGYG6peBNwW8VKYVePf9oq6W8wo45dV\ntnsHKc0sJeQyN7Innlb0+vz+wy9Uk1LNCf4KWkMdeLm0AQODlCz4oUfKJ8nf\nBzD4cXEDoD29s0/n7k2zCJnD3fMUKDqPw/O/O3AWGqFC3JNmB0EjTzoI7+qs\nHKIOuvB3Szf2HtsPy8rNLaXPUCCkb4CvcmrnG5llgvzeC9aoXDu2Ts5UGw4x\nAauTtlIAX8ko7LiyMVNRdFW2bxpTQ7XiOdpyEVF/IlUQgwtONWdQRMbYB4Ub\nNEQX9R1F4wGk9aK793zwH0EKGA/1HGo3mdG2SctA/lf7oLTMBqzQ2OXZpMY7\nFDnr+eJwWo4IvlKxG07lOENtMSw3IJ7pEmPU1Ra29STRmucC64xyi0gHCjUc\nPzJf6OKKMHtIV4GrXPOHVg+Bn7J49smHq56xde/0si+cdnqfAeJQMfsr0Rjc\nm7MnQ7dHv5oKR9a0rpeYUQ5VhoEM+3KvpzoSJ6G+38DR0/a9Lvdmn/iAW6ye\n8tTx+pUegfVdJTZawj5MiFS9bU7rWASs3cJIT+ffKpSN+hKsMTBnKJv7NYYd\nUlKt\r\n=qs3X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCz2Fnv2/FQUqnLWDLWcQCTTA15yGMpbpLMTFh7nB35SAIgZrWIWT+OFRsLHkvrU7AnKkafpu23juqaateNZdS1pH4="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.2.1_1573339176106_0.37499740005408944"}, "_hasShrinkwrap": false}, "3.3.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.3.0", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^25.1.4", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^2.31.0", "@typescript-eslint/parser": "^2.31.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^3.5.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "67cb3a64e47fec928c390480aad4b693f3f95d2f", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.3.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-pv9JPyatiPaQ6pf4OvD/dbfm0o5LviWmwxNWzblYf/1u9QZd0ihV+PMwy5jdQWQ3349kZmKEx9WXuSka2dM4cg==", "shasum": "10fec696a9ece2e591ac772d759aacabac38cd39", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.3.0.tgz", "fileCount": 12, "unpackedSize": 22100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1B4aCRA9TVsSAnZWagAA7kgP/jTINfDgOYRav2ycTBJV\nqHgMh/5xCK2kW5Ic1fY2rVBAEYp+EVcYrDrb5uhFTWROzjDKOtXI17we4TJg\nMQ+Wa3xmDMboZ8+4IO1Wn8knPJYZf7dkV1d/EN7dauoOGhmRcTZ+0JB9RXXy\nWBB94bWygxYodzmFTP4NKMefDLPZ0VN6sLScHfHXgcEuPHOmWF3IMBVXAHM+\nKRgqT6gu38cMC5BP4ZzdRzx1o/E+91WKBJsBmTTWQXpOiku+9dEQR23eF3a5\nNYKTNP4F3WZdONrp1WnEz9ArO1rGcFb9nkxRGWVdJ+MS/J9DIRsbCIwLH7uk\nzbcrXTKLQ38GxP6W/yQoUolMzXnloJ9yovY5lwYHqgB4/bxAeh2I/uxN9InA\n7I8L+ehE5+EWDMvdhpFnLNWVBl0IWwPGchllW+i14KgyekIg7huwzf0g14jv\n3+MT/LSO1tDtPFEHeyWmVXfbtLg3RsXKECHPPp8eGraTQ5Sx+rz2AkPnwP+s\nrVupkvjfr7iszUtCnj98doKGMxDcSGJYP3vC8nhq8xppZkZ8emoy4TRKNPpZ\nlrYyIvap/YubKueNqDeRkdOJDxFrtWMoiK3BrMx8NgBUS1aof0pPfmK7xZR4\n7iSIDHpm5lhu4jFSs1SMLtalEUtP9ztLpZg3PKHYXqJ4MbEB7k/LTqT+3o5l\nbVaV\r\n=xBiu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJ5upNzwImDks2M2DvpMWmR1/EzNA4qMokpY/ZIttyKAiEAzkAFR5WrKwpfj5lobgFkOtfyB7J/5U6bAYB50EUowgA="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.3.0_1590959642348_0.2053251422399851"}, "_hasShrinkwrap": false}, "3.4.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.4.0", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "98ac048c2ad5b66d83f4da025ee9982390c98191", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.4.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-HA4iK1F5BEjbfSAguPk03XboiTvzS0tsqkoSJhkZVf52TgigRpIRGXtvSGRVgHcr1ln1Ubqx2flxKFwtY3kX9A==", "shasum": "f8d042a1ebb31a8251e64d2a0f0ec3fee9a05267", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.4.0.tgz", "fileCount": 12, "unpackedSize": 23485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdh0gCRA9TVsSAnZWagAASnYP/jpNTLOuHFW+i0XRTB36\n4AsFEpu4yhwgfnuErBlEH93o/DxQvOsA9ywy81f8yTyCnCVRG+IVpOaqgPXG\n38hUWhSF0YzrkSvSralfDrY/OmIDxmMErSASzGXBs77CpZnqNou+0F+jNzmU\nAYimet+Zn2mBer1yr59jzRR1qm0hC2hgZZPVPpT6YyD4suGpKa721HDCZI+P\nyEgQdDeaQEG1FlXr0Mrb93qAeKb2VtDIlBJ0O7VA21B4K/WnuhQaDQuJBimd\nwzeSATuiEtRYdjO7SNgGvcoXDO0aMx3V0YmP9i9CfK67LjZPX1ECEO2K2FkS\n94Zvkbp73Wm6k/8n0lNbhPS87t0nn85KGgAnqKjBdIh5Z4qv4f0liFW/wF9s\n8peXejd9UkfnUmIUbxvCS1nKRxvB7C863FsxCg5qR0JoWrYKidemwWovpG0X\nOa9wJLz/y6pI2jxppTvuaxdARAFqKzgcwS7iQjUkGoVjBjoSb/eMTch5xOvk\nRg4NP8TXnSEK8MGYLTNQOKkB6HEUMSuTT/opQiIXXLPhy3xx1hUUj0ujdcUi\nGLxAr8UQdBCzdZpfmvU19jEpW84qzw69BnNlHY1p/lYmnGG1lE6DTLWHDkLW\nsMjluC9/ew6aeOUUsbGqxKY3DD6IsPKrwGYH7DL+aC94enCGqWdusZsPmFYi\nLnMN\r\n=Zz9c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAO3StUXhjBFpb9fdFtrS8xID4e8dFTJip7ybuGfemDlAiEA+/KoF6lyGsOvYv8Hm5BIIeecPQJfCxIXfq4k+KrPsFg="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.4.0_1601576223435_0.634638971635717"}, "_hasShrinkwrap": false}, "3.4.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.4.1", "funding": "https://github.com/sponsors/fb55", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "eefc98a05fa29a402c6645e375df47d7f3958dfc", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.4.1", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-wHOppVDKl4vTAOWzJt5Ek37Sgd9qq1Bmj/T1OjvicWbU5W7ru7Pqbn0Jdqii3Drx/h+dixHKXNhZYx7blthL7g==", "shasum": "81cb70b609e4b1351b1e54cbc90fd9c54af86e2e", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.4.1.tgz", "fileCount": 12, "unpackedSize": 23558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdjFfCRA9TVsSAnZWagAAOWIP/1PRpxf8tqlRLshrHzY8\npRbHJhhbuQsacFvEBcASnyZzy2VKVPU7gda8NL80rhjK5xl6lJbI0Jcs78dU\n1nXrZzjekGHdDX0TODF1w3VshMBAShuFqhb22ForyuQPZwdopenyC7datqju\nEKMOer9Qi9raatWfPjcdxJueSmR2eJy2M+LfTvusTUOnpFXR5jdtKnFH5orA\nVK6hOs1ptd0pPUdZNbq7opewl9qQAo6cwMYY8J+dzMKNphKBNvtiAkr4imlJ\nO00hg3fPGyNlxC9V4ACOsd4gTegGG4ySey/S/uYtO8WAYx+mposqoOmF/faf\n/SzMJIwKiwz+CgNXF85sOf9cSZRaOicge+6TdCyu0hSSKkWUr+8WD8y3xVZd\nt8Gj6EGqk+qIadLFXQOZCFcLcQRC5xgdSl3FU33JKCnRX8usgtKsQOuVLRQ2\nuaqp/hba7Wb7hwBwhSbHK5loeMb1WhO/N+Q2WoqMfMsS2HMJKHJHEfYFKAOw\ndfH0If7SFQIygc+lR0AeJq5yvKxCssX9OOWhGRCPzIPlBgyG5w60IHC+985T\nKVsPzHAoMdbHyZoFwP9y+ig2QtZJoLHgNLh+chsQhdl1Y74jfzjKz9cuWtpJ\nsqDdQGfP7mOjf3yVI0HP5NuBoax71cs9dhgrekTjVEprq6vf0j6j+1VRm8U1\nO+96\r\n=RKB8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtnJ4AXiaSUpd3nn1zTbUFkILwTbGzaRhhnGNrEPdkUAIgGYB3x0S3VkMObt78pdHwVrysuZ9GmLr91nckR+EcsKg="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.4.1_1601581406579_0.006261648034125988"}, "_hasShrinkwrap": false}, "3.4.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "3.4.2", "funding": "https://github.com/sponsors/fb55", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "e4b589dc5c4de9d3d94890216425f888f2909ca4", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@3.4.2", "_nodeVersion": "14.13.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-ACUm3L0/jiZTqfzRM3Hi9Q8eZqd6IK37mMWPLz9PJxkLWllYeRf+EHUSHYEtFop2Eqytaq1FizFVh7XfBnXCDQ==", "shasum": "ea7026fcb01777edbde52124e21f327e7ae950e4", "tarball": "https://registry.npmjs.org/css-what/-/css-what-3.4.2.tgz", "fileCount": 12, "unpackedSize": 23664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgK+4CRA9TVsSAnZWagAAiBoP/ArBVb07zbOQ6q3CV21b\nfqf+Z+u2Xs+n+TSTZBjX4sbd7Ja/393iWxogrOz/ZyZFBChUja9M7EgWoHwF\nCdhzTRRwzFOjb1i7vVPPP0bf4GlA+ZJsGh9amntB+DsZ7W3HlFvUGth9UH0K\na3EzMffh7+Yall//OSHXQLN2Ar8SRiQxjFO+5uqtEo/AR6jp5SmCNHuOXRia\nhZUUKjQs0su/Shc9S5Wv9XnNHp2V39sLbIyE/jn70LRre1XHbQ53U7ni/02m\nrKgEaAJdbsXh+lwnqgoHqnbAWg2a1WUOWGQe9yJmQXuacJNXPeykd49uLf5S\nHudLTWhaPWcLEp8ztlCOrHh9SFoblisOIEOczdhBV2c/F/ew5BuOVYGZVz2W\nOf0XpOi3eiXdMwJRAJMi9TYEpBAE8x+MYLIthbnO3p73X7Fh94/B9eKJ4nAJ\nBSkNB3h422Ws6DjzgKoCgbs9SfJWhrwIDjSJyJZ2o0UqY0E0Ojz0RqpEBYgD\nmiaY+9aOnRrK9kBUZPvZhqYLktIUt5IoBfKlgM8kn0B2J+COjqkjohB1Y8QO\n9KtyvKzfpGU+oTPT0pENOFqt3bV8kmkHp6gFCLZK1+Wf0LSzBz5gEi4UXl21\nWBkqtRh5xC5Sn0Gk7mxv/kdcZorpwYJGLHnub9sJ+cYr4CHJAHWREuGSqlvP\n4+Wc\r\n=WEkg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtoL2TC1uUNc8rcxqdgLEMXqUgBTfPOqfV/FV95c0ZHAIgBIOv3BytGa4ev2Ke2cyZWcyST0XLM0tTb77ytkXpZes="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_3.4.2_1602269112251_0.5490925095163195"}, "_hasShrinkwrap": false}, "4.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "4.0.0", "funding": "https://github.com/sponsors/fb55", "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "a868f3e0b284883721da97eb0deaadb0e857e630", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@4.0.0", "_nodeVersion": "14.13.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-teijzG7kwYfNVsUh2H/YN62xW3KK9YhXEgSlbxMlcyjPNvdKJqFx5lrwlJgoFP1ZHlB89iGDlo/JyshKeRhv5A==", "shasum": "35e73761cab2eeb3d3661126b23d7aa0e8432233", "tarball": "https://registry.npmjs.org/css-what/-/css-what-4.0.0.tgz", "fileCount": 12, "unpackedSize": 27927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhVGcCRA9TVsSAnZWagAA9wQQAJRBikpGNRIuH59vwlbI\nGI3NDiccXh2gt2i4rm86QLuXQa/P+/nU2uGcP+nnMbpe2IoS/w4cBl875o2l\nxIXp5V+Qfij7CUHErBELpinUfBqKKCjq6OwR+2ZTCDW1ffF8lKKKGR9Dnk1j\nc+EZBUtw+9xQLxbzTFp725vvcvtpmqVnkXpOtoN8SvfCdrctiPIuKAvwynll\n1aZ/V4ytxv00y4UtbQMHwewAmdVbxrbwMSDlgMGr6FevPgwvR33il8zC6M1Z\nzgtCJuYob0nl1Bintu+TuAAXwu3usOtUrAHJm6ZnMLo9gxr/4vbG2+UAVTcI\n72f82Fh2CpQSQ1Vi8l8yP96AJ+wzE0gN+QQnZMpanbnKJG8ggiM5Ryg+CJD3\n9MUr0fWSFBUev2y5zDI9nu+MP1MRdVOVCMVURxPL4y6p0XxL5v6OHOL2Prnk\nUt+eRjW5+EA7TnhCyWky9/VDoref0RjEOeHsvjTCzfFWzWO9z/tT1Hexr74H\nBfCsSmx33xEnkRqMISbiex7r93grghGdBk1zAK9i0yPJSwESz1x3+rRF0qMY\n+8BSqgFUf22TQP3ofBNXSUernWWzEFR2JNv8qj3kFdYCsCQDa+yyKiksJzpL\nmltNrQKy5y3oXAYRIc+ajz/Q0BWbVhU7URIJRIM9C1+troAuQbezIZu8JKMi\ncvf4\r\n=mlt/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoD1tzcCq5YtVCVHnO4WB6xaO0/JVy1mJH32pVvdk02QIhAPMwR0Zoh79qXez5G+FPoX2tEmy8ZWgfUK/dqQv53VXn"}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_4.0.0_1602572699724_0.3497209904412859"}, "_hasShrinkwrap": false}, "5.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "5.0.0", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.0.0", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "15227a74654a4dd39b89416959b6d179f021d551", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "dependencies": {}, "_id": "css-what@5.0.0", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-qxyKHQvgKwzwDWC/rGbT821eJalfupxYW2qbSJSAtdSTimsr/MlaGONoNLllaUPZWf8QnbcKM/kPVYUQuEKAFA==", "shasum": "f0bf4f8bac07582722346ab243f6a35b512cfc47", "tarball": "https://registry.npmjs.org/css-what/-/css-what-5.0.0.tgz", "fileCount": 12, "unpackedSize": 29885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRvNhCRA9TVsSAnZWagAAQvIP/jVVaCO8Z3dveFHiHk5L\nK3FhT4G0eBZnmRr5hjIUyVV1gLRKemxsMuKVa1LNOQfwPAcqj5QK2+djUvcB\nYSN7JD4D8zysYBVrpMsCYFHQ7O28Dt5iY8mB6rJ/2h5ZbWw7T15HxCJUhYGV\nNP0v+3tpg2rCVJLeRVLEYy38JcYj4kWJON+d43DeRnmy66urrD3ACxNhdV9H\nAjc5G4gxPyo3iUH8Nu/2zZcCbZRjSoeCEomQPStMr+gnIuDVYjm7fuA6bFv/\njtHQVIKOB/bTcGk2FdLMDcQ6eXhzDOXupiFS4fEeNM3CvONqzg5a2d8jpoiX\nzEwW6eHplQtCAHnVujSFf0ZuR1ROSyLDIZgf1uDvJ/RF5LVHXepRKk46QwDb\nHmeoBrK0t9jQc+2m3OGPMFWTInfHCFeN5QOl5vyaD9drjK/lgnGIGzMYkHlQ\n5MhNs99zJcX4qbqvWI3feLfMf1paT7ogZqUxzGCVllKSPv5FSFzgwJ4L36Zn\nmvCmnPFNPrr81Nn1DAVKRw4hkzVtXRcOs9sCvWUYjaDK3AVleCYwlAcGWOoQ\n1x9JSl/6BJUoeLae59PU8gntBGzmR8ld0EGZc0kMS6Dn02ZRhPnM3l6VL553\na0DMH3h7qvTB5CBruWZiJnq9tbNoWS0qkAWIM7zAClnbOUjrSOfSUGucguwP\n8OYF\r\n=wCCs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyTd8ZjNq+VTS/WKAG5Nnz9SyX1jkf/yxjkMSQeMEXnwIgWf+prhKYVLty5k639uUiyLP3xj8WXhNAQ4NQId9/JSc="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_5.0.0_1615262560920_0.6354673999691913"}, "_hasShrinkwrap": false}, "5.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "5.0.1", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage -u && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^15.0.1", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.0.1", "prettier": "^2.0.5", "ts-jest": "^27.0.1", "typescript": "^4.0.2"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest"}, "prettier": {"tabWidth": 4}, "gitHead": "e9106aa26ece8efbdf85e3219a9f8513897160ab", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "dependencies": {}, "_id": "css-what@5.0.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.14.0", "dist": {"integrity": "sha512-FYDTSHb/7KXsWICVsxdmiExPjCfRC4qRFBdVwv7Ax9hMnvMmEjP9RfxTEZ3qPZGmADDn2vAKSo9UcN1jKVYscg==", "shasum": "3efa820131f4669a8ac2408f9c32e7c7de9f4cad", "tarball": "https://registry.npmjs.org/css-what/-/css-what-5.0.1.tgz", "fileCount": 12, "unpackedSize": 32436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsTf2CRA9TVsSAnZWagAAbIUP/2jfJd2uN06p8DTUOKhC\nSqzC7FtoaPYMlAI+kMpPNMOmXWYz/cRtNEdIp93m3ywn+TMjdivyEosSdTUc\njlCj49wMQmsboDrxtriOxIaF/SFiEqiBVFiZDR3tMSY3BcLDUyPB/3oabIlQ\nJbSAOdcQxHiLre4UhmZ00GLbwjaeDnJb3z4qx4RxzW99jpVy02at3KscacQ5\nDPsQB50uJ1+dsJQTIYpSbVj8yKUZQy9p6Luo/eLxHzGoUY916/sgJ7pfI58/\nxDINDjMZh6cAJEu/7oe2EmLQE/IgReUF9lcMX3Vu1tkr3YNk0+VTNR2df+5g\nIXvTmUMMSLHrdW57Qc8pFz7l2bYeBQpZixdmjmFEXCNrznM4dqBr7w4wSRcu\na/tiyeV23PjbR8IZ/QzbMDeTXz93GSEznq3dPOwwGVfgX1Evh47FfBHcah04\np7o585wEa5if4jg9zOlvTIuPIATKXF1WlHxM60h7MO+QyyFTjrn3gUAGnAWo\n6JjML+Xh2cXsKzNViJaH6JEJb6xLkiMLMrdLV7Cx7pIwQamses0OluoJQ94L\nurXlj7jco3/t6TJ1cECpyCuef/f1Oh2SMZk90lneNQkvjgIH9OSqQGgIal+/\n79iaA+Y7R6mlvJuTUWddTCP/xEjdobEGMxPvP3CXtAa3OC+D8Ll/6l0Lvc8D\nj3Rv\r\n=D6fV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQRU7piRM4sMQDA2KsLrD+VTU4NcoCm7olW6HIiZQdIAiEAted94dgDDlCQn2EtMiKJ/2j1pNFGbalEQT/gcZx7dV8="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_5.0.1_1622226933845_0.4780503023942684"}, "_hasShrinkwrap": false}, "5.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "5.1.0", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^27.0.2", "@types/node": "^16.10.3", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.2.4", "prettier": "^2.4.1", "ts-jest": "^27.0.5", "typescript": "^4.4.3"}, "optionalDependencies": {}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest"}, "prettier": {"tabWidth": 4}, "gitHead": "f445a743793c9a5ab45c9f554e29adc38422246a", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "dependencies": {}, "_id": "css-what@5.1.0", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-arSMRWIIFY0hV8pIxZMEfmMI47Wj3R/aWpZDDxWYCPEiOMv6tfOrnpDtgxBYPEQD4V0Y/958+1TdC3iWTFcUPw==", "shasum": "3f7b707aadf633baf62c2ceb8579b545bb40f7fe", "tarball": "https://registry.npmjs.org/css-what/-/css-what-5.1.0.tgz", "fileCount": 12, "unpackedSize": 32764, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTvVzERuRjBpUOh3vPrl1u6Ifm6l2zD/jS6JBPr1upHQIhAIzRp+YjlN7XdhrXT5PZS6t/sH+ZbeqTHt4/gJYouxjM"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_5.1.0_1633863945244_0.38855566646381523"}, "_hasShrinkwrap": false}, "6.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "6.0.0", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/commonjs/index.js", "module": "lib/es/index.js", "types": "lib/es/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc && tsc -p tsconfig.es.json", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^17.0.4", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "eslint": "^8.5.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.4.5", "prettier": "^2.5.1", "ts-jest": "^27.1.2", "typescript": "^4.5.4"}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "roots": ["src"]}, "prettier": {"tabWidth": 4}, "gitHead": "b528a2034b5a48d874db890d22da5334151e2943", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@6.0.0", "_nodeVersion": "17.2.0", "_npmVersion": "8.1.4", "dist": {"integrity": "sha512-NjlwaGsDJLNK2G5HBa6FefsaOtCobT0a6LmwW/vYzdDJvKI/rcqL60P6yXi+WtUJKmuNB9pQEIJA6YhKo7fBSg==", "shasum": "e76a145a454bbe876f7ff8e6b6a2162faa2cca0a", "tarball": "https://registry.npmjs.org/css-what/-/css-what-6.0.0.tgz", "fileCount": 27, "unpackedSize": 65672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhyPZnCRA9TVsSAnZWagAAlYMP/iMcnSsVYDeOv7/DxPHz\n2pWs5UMgkhwcYnOQE1JhxcSyUyjb4Aa5FcrCAkodmNhG+RCYeB3KmcHh2sba\n3tjJZBEQQuayaWzcF7PcEiBHC0W7BZoza3g1oYE2ygdFnN8msAWciX+jIStF\nljTsfdp7IKWYvBcgjmVr1mn7bUh8teav6GaPKnUXExwB+mR6212Jq+ssghLS\nWRrcWhOs8PrcNDCPzvyfOcGmpVxll+fuOaInB02D3b/pLwaNfqBTn1fcRmyv\nRXBd6wmm0Zpy7h+kA+TZ0i/9vsqaM+N0qAqC5DxzGmHlMmGrA/yAkXgPGTtL\nprYSKCK0HFAZLNB1CVdxUyYQTQiRcOiTQ7yTtAiZzzCCnvBNykM+bGfXK0Y0\nn9boloRTc7SobEsNcAD9b4Oj/mi24nRjAQ6fggRbjgms9Vb+GCNF0MoBAvgO\nvdUSjNljZQ+cx2l/8Ja2DMR6ZvEZVury9kDHB2/c/NlbQCjltWfbMkmVfTsm\n0bWJVCAilueYXR9qnZB5ci2d+oRv/sstHj/L+c1oC3QL9y6KkOU09gkPUkEd\npumroJqgk0UWzMnE4kw0PtjXBaDh9uA6KzH5KNXwRxRHkUcm+orWTytllc6e\n9fnUIdAM2St5zVF15USTxKZIHBiVC2OMBm4UwLaZDMYbS/rLGYnRG8gXiC2l\nK8RI\r\n=Pz/e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAf9mWV7fEdq90uarvY+U1rhNGJ0jIntSmSPgvoRu9wiAiEAhCib8wocJ/wLeUCsRs7uUrRCxAM/buBD33AEEoZyomE="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_6.0.0_1640560231841_0.9029463980707182"}, "_hasShrinkwrap": false}, "6.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "6.0.1", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/commonjs/index.js", "module": "lib/es/index.js", "types": "lib/es/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc && tsc -p tsconfig.es.json", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^17.0.5", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "eslint": "^8.5.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.4.5", "prettier": "^2.5.1", "ts-jest": "^27.1.2", "typescript": "^4.5.4"}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "roots": ["src"]}, "prettier": {"tabWidth": 4}, "gitHead": "035ed701c1fa7679a1288bc970a24c1da8004845", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@6.0.1", "_nodeVersion": "17.2.0", "_npmVersion": "8.1.4", "dist": {"integrity": "sha512-z93ZGFLNc6yaoXAmVhqoSIb+BduplteCt1fepvwhBUQK6MNE4g6fgjpuZKJKp0esUe+vXWlIkwZZjNWoOKw0ZA==", "shasum": "3be33be55b9f302f710ba3a9c3abc1e2a63fc7eb", "tarball": "https://registry.npmjs.org/css-what/-/css-what-6.0.1.tgz", "fileCount": 27, "unpackedSize": 65834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhyZmMCRA9TVsSAnZWagAATmYP/38eiLI/gaNJOZOHS16H\nfUWzsjrjI70uKajWKD/+oCJu9BlZXpPbja/7Nwip2ez40PO8lx47V87Q8pw3\nUv68nYLgxeUVnSoqIZmL3mcy/ULpwvnG3uLsAQwXLCCLdNe5cdmVfr9MuYDp\ndUQvihL6ceeFkeRvSSMQZOJwn3xJ2yzWCJdqpp8sU1aGX/kCS4IEhnuu/+n9\ngbP5QRG5iqSOHERIzM8WZQDcx+ETKQYXVFVMT/jseepDDVgDnc2yelUD6LJg\nAdJCXNP7YFhTYYpW93tnGAx8TXS+OKsNeVWjTXnk+zDH1v4oajByptp3MZu8\nwFvzJ0qItffjwwZMqfYpUETWqX7cw4s2i8MrbvsjKFZznb4Xy4vj+k6+65gj\nMCATedC4MgVF69YeetQdd2mWMBHDN6C4gh6fukLf8YfEgjqMau5UKBUfU3gm\nQ/aKTXz7+tqACdVH+FRc4Byzw5wCpGunc4zBJHzAyabnLuWQI6DnHnFBwnO8\naL/SaunoJA91/VlZGLSjtm4WFcSlUcDSV1iXJdROP5RWsTnd1fl6lFOcHXDV\nyB12VC0yDHDgXIczH4YHC5ICCCYTqh7ZsW7kTWTjZgoMbN/td8BOTIRa+nJj\n69HPCaj3zYltAIOEGyCpTFmOevuDoNzUQgE2VDSOlC5Fl0QHp9Ujk4dfrEx/\nCHGl\r\n=HJIR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEEkQHTG+70aidD9/dR53DYEKfxWWyZNECoWRrCtqOqaAiBlKf8f1stGgH9ev+CVhhgASjcF+Ag997xmC2p87DvM0g=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_6.0.1_1640601995845_0.11577943014022285"}, "_hasShrinkwrap": false}, "6.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "name": "css-what", "description": "a CSS selector parser", "version": "6.1.0", "funding": {"url": "https://github.com/sponsors/fb55"}, "repository": {"type": "git", "url": "git+https://github.com/fb55/css-what.git"}, "main": "lib/commonjs/index.js", "module": "lib/es/index.js", "types": "lib/es/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc && tsc -p tsconfig.es.json", "prepare": "npm run build"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.5.1", "prettier": "^2.6.1", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "engines": {"node": ">= 6"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "roots": ["src"]}, "prettier": {"tabWidth": 4}, "gitHead": "ee41ddaffce571498c4f39a4da7894c15b9da65e", "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "homepage": "https://github.com/fb55/css-what#readme", "_id": "css-what@6.1.0", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==", "shasum": "fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4", "tarball": "https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz", "fileCount": 27, "unpackedSize": 66038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFEjiSCBFtfoyicWS8RKj0usAsu32knHUsJDCzpxhJ2lAiEAvUIL/+BpYZORHKJ6aPWo387JkOgkBuDa57BOFhEiY4A="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRaqpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpn6w//ZTZ79PwMWW7lRy2/oitbQhkqY1SkZm1yzkwwJYWEtJ8zALjz\r\neayj1JfXSTUq/c/H8hPPYx6WY4IRZObBEuMmz40CGsn5IcRhkSPoL3i25nlT\r\n45Bs1W6gEJy7Jv5BIKd2vpqleTdoPuRry3kKst3ANuqbciaBANmzAuyRZfan\r\nUiP6AfFVrK6efDIPJFrsbYik0o53SkEtPn/4YCOStM8DqGZXKGALVpyp+S4H\r\n2/nLrZk+69k/SZECd0Iuwiv2RJkU7+18LRoaRertI7nyDLdU2ICZZo1FCBTi\r\neCI9cRjUPoifYnUFrri5qHE0dF+JPiCNgM/5aivoLlZ2JWAeOBGPpM85C2nj\r\nfcSBhwLdsQyhFtAa1wnQjOle2BQa/JE2NJwwHEvoXgDjuaaNRxigv5RA58Tj\r\nf2Hsgb+pgL77SiABsrOMrBdQDHT7Web4f2M9NJWNo+YTTmuuO53S+S9H5Z59\r\nQyV2cRCQ8uBcexwDkAuV60K5TLehLjU149r59dKguf44Hh43KdDm9QyvsnN+\r\n8dviOQggkJwx4RVI47Ym+UH0dPGX2efHYkSFHN6oycBMwpD+yZMQc3KRsh31\r\nv3wg2t0YjqcI8qrxbuX0MqKz1O/GG3h8gLZZ6HXT4BHn51MHey0nAUi2nHXk\r\n25FNMjVwGQapxlu05d1XPnxk87dk+usk8VM=\r\n=ahNB\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/css-what_6.1.0_1648732841505_0.6690128002347251"}, "_hasShrinkwrap": false}}, "readme": "# css-what\n\n[![Build Status](https://img.shields.io/github/workflow/status/fb55/css-what/Node.js%20CI/master)](https://github.com/fb55/css-what/actions/workflows/nodejs-test.yml)\n[![Coverage](https://img.shields.io/coveralls/github/fb55/css-what/master)](https://coveralls.io/github/fb55/css-what?branch=master)\n\nA CSS selector parser.\n\n## Example\n\n```js\nimport * as CSSwhat from \"css-what\";\n\nCSSwhat.parse(\"foo[bar]:baz\")\n\n~> [\n    [\n        { type: \"tag\", name: \"foo\" },\n        {\n            type: \"attribute\",\n            name: \"bar\",\n            action: \"exists\",\n            value: \"\",\n            ignoreCase: null\n        },\n        { type: \"pseudo\", name: \"baz\", data: null }\n    ]\n]\n```\n\n## API\n\n**`CSSwhat.parse(selector)` - Parses `selector`.**\n\nThe function returns a two-dimensional array. The first array represents selectors separated by commas (eg. `sub1, sub2`), the second contains the relevant tokens for that selector. Possible token types are:\n\n| name                | properties                              | example       | output                                                                                   |\n| ------------------- | --------------------------------------- | ------------- | ---------------------------------------------------------------------------------------- |\n| `tag`               | `name`                                  | `div`         | `{ type: 'tag', name: 'div' }`                                                           |\n| `universal`         | -                                       | `*`           | `{ type: 'universal' }`                                                                  |\n| `pseudo`            | `name`, `data`                          | `:name(data)` | `{ type: 'pseudo', name: 'name', data: 'data' }`                                         |\n| `pseudo`            | `name`, `data`                          | `:name`       | `{ type: 'pseudo', name: 'name', data: null }`                                           |\n| `pseudo-element`    | `name`                                  | `::name`      | `{ type: 'pseudo-element', name: 'name' }`                                               |\n| `attribute`         | `name`, `action`, `value`, `ignoreCase` | `[attr]`      | `{ type: 'attribute', name: 'attr', action: 'exists', value: '', ignoreCase: false }`    |\n| `attribute`         | `name`, `action`, `value`, `ignoreCase` | `[attr=val]`  | `{ type: 'attribute', name: 'attr', action: 'equals', value: 'val', ignoreCase: false }` |\n| `attribute`         | `name`, `action`, `value`, `ignoreCase` | `[attr^=val]` | `{ type: 'attribute', name: 'attr', action: 'start', value: 'val', ignoreCase: false }`  |\n| `attribute`         | `name`, `action`, `value`, `ignoreCase` | `[attr$=val]` | `{ type: 'attribute', name: 'attr', action: 'end', value: 'val', ignoreCase: false }`    |\n| `child`             | -                                       | `>`           | `{ type: 'child' }`                                                                      |\n| `parent`            | -                                       | `<`           | `{ type: 'parent' }`                                                                     |\n| `sibling`           | -                                       | `~`           | `{ type: 'sibling' }`                                                                    |\n| `adjacent`          | -                                       | `+`           | `{ type: 'adjacent' }`                                                                   |\n| `descendant`        | -                                       |               | `{ type: 'descendant' }`                                                                 |\n| `column-combinator` | -                                       | `\\|\\|`        | `{ type: 'column-combinator' }`                                                          |\n\n**`CSSwhat.stringify(selector)` - Turns `selector` back into a string.**\n\n---\n\nLicense: BSD-2-Clause\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## `css-what` for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `css-what` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-css-what?utm_source=npm-css-what&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T04:57:42.604Z", "created": "2015-02-03T22:10:20.029Z", "1.0.0": "2015-02-03T22:10:20.029Z", "2.0.0": "2015-03-01T13:46:19.791Z", "2.0.1": "2015-03-05T17:47:57.399Z", "2.0.2": "2015-03-12T12:34:37.829Z", "2.1.0": "2015-11-22T13:25:14.930Z", "2.1.2": "2018-10-21T22:18:26.101Z", "2.1.3": "2019-02-14T18:23:18.507Z", "3.0.0": "2019-07-13T19:31:42.303Z", "3.0.1": "2019-07-13T19:36:03.831Z", "3.0.2": "2019-07-13T19:37:56.308Z", "3.1.0": "2019-07-23T01:11:04.982Z", "3.2.0": "2019-08-02T19:24:20.868Z", "3.2.1": "2019-11-09T22:39:36.281Z", "3.3.0": "2020-05-31T21:14:02.469Z", "3.4.0": "2020-10-01T18:17:03.581Z", "3.4.1": "2020-10-01T19:43:26.703Z", "3.4.2": "2020-10-09T18:45:12.341Z", "4.0.0": "2020-10-13T07:04:59.892Z", "5.0.0": "2021-03-09T04:02:41.226Z", "5.0.1": "2021-05-28T18:35:34.029Z", "5.1.0": "2021-10-10T11:05:45.401Z", "6.0.0": "2021-12-26T23:10:31.992Z", "6.0.1": "2021-12-27T10:46:36.076Z", "6.1.0": "2022-03-31T13:20:41.689Z"}, "homepage": "https://github.com/fb55/css-what#readme", "repository": {"type": "git", "url": "git+https://github.com/fb55/css-what.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "readme.md", "users": {"mojaray2k": true}}