{"name": "@webassemblyjs/ast", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.0.0-y.5": {"name": "@webassemblyjs/ast", "version": "1.0.0-y.5", "dependencies": {"webassemblyjs": "1.0.0-y.5", "@webassemblyjs/wast-parser": "1.0.0-y.5", "webassembly-floating-point-hex-parser": "0.1.2"}, "dist": {"shasum": "6a2ddb9345d6ef19962a63da6265462374dcd807", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.0.0-y.5.tgz", "fileCount": 9, "integrity": "sha512-q9mr+pwpZs5/j/wYrW57WyXml24ftIszONQdaMKuEL1Dr8G7Cc95d6+nPHvQzYlT+43CP/pVpdnRS3LZsQU+Jw==", "signatures": [{"sig": "MEYCIQDIoyRL2TlRVmNbPXqR8EUrV7mU+mptGUJ8fkia9opkagIhAMWmppQnuHoZ4LIrPYuAfjNfvhhwzOO8vVeBZsKqMUAy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50787}}, "1.0.0-y.6": {"name": "@webassemblyjs/ast", "version": "1.0.0-y.6", "dependencies": {"webassemblyjs": "1.0.0-y.6", "@webassemblyjs/wast-parser": "1.0.0-y.6", "webassembly-floating-point-hex-parser": "0.1.2"}, "dist": {"shasum": "0e8b4e848cef00736b00b201d8aecd424e586d56", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.0.0-y.6.tgz", "fileCount": 19, "integrity": "sha512-fOGoZIKfncu4v/WVF0bYJ6zwkrsvLS1Z4H4eVkxrcMS3wfz0l9w6tG6B1W6qRCxKSg89dHRFMzO767/HYTySYw==", "signatures": [{"sig": "MEUCIGQf5YHQOppDI4sE3r4eOn0R1kbgcGdFrqPsTxImQWTLAiEA6Rnb1Lsj5kuyAC28vKiKIC2gerhS4LxUXIpPx7MY1V4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51710}}, "1.0.0-y.7": {"name": "@webassemblyjs/ast", "version": "1.0.0-y.7", "dependencies": {"webassemblyjs": "1.0.0-y.7", "@webassemblyjs/wast-parser": "1.0.0-y.7", "webassembly-floating-point-hex-parser": "0.1.2"}, "dist": {"shasum": "081dd61dd50030d4008aac9baf0ef5ff0cc2cab9", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.0.0-y.7.tgz", "fileCount": 20, "integrity": "sha512-mGqAVxMVON10SriM10hc62+NXXx9Tr8MLUYOxjnB7GfiRJyVbM1JIOgjRjCLQg1/wuf7D5KkDJxkM4WJv+RtuA==", "signatures": [{"sig": "MEYCIQD2uUlkYdHBEJAS0lI/7FjFUpdhQPZ9d014oLmOTx9yyQIhALSb/9EKzeSnlaQQY1O9lK0D0qR+MpFOmwG3AyBBGDSx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51760}}, "1.0.0-y.8": {"name": "@webassemblyjs/ast", "version": "1.0.0-y.8", "dependencies": {"webassemblyjs": "1.0.0-y.8", "@webassemblyjs/wast-parser": "1.0.0-y.8", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "06b6a1c0d8f2db0892c2a703b80766e44af69c71", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.0.0-y.8.tgz", "fileCount": 10, "integrity": "sha512-3ppLmaO70QsKabteM9Cs3oJJLTkZjyq+GG51KV7RkN4bHOG1z8tYDmQ2qxcC3vXLWUWTl2fNEDJZ9sz+XR/ZZA==", "signatures": [{"sig": "MEUCIHcvalU8WovBUKbXFPaKpw18aPLh0mOMIzAYbLXyL7FFAiEAkyAKp6JngjuG/S1R7UQJ9TDiFf67tGc7O1q0mnqWagI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53162}}, "1.0.0": {"name": "@webassemblyjs/ast", "version": "1.0.0", "dependencies": {"webassemblyjs": "1.0.0", "@webassemblyjs/wast-parser": "1.0.0", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "e6953dd785b6827ac5ce2fab479b1358f35f7df4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-ISa1fBb1ZDNBQ1HPEJbSJj29GQB4605iF8N9TYbFRoXuPHsycVJOn76F5F/m5gQlYmlzYcZO5IH2AT0FmFuS6g==", "signatures": [{"sig": "MEUCIDtbZs2Rj45pYIng0sZ5GzAbDhje6Arws0FKORDo4au9AiEA6qY1Mxap69yaSd2uWZ9R1Y1GJubcbyX5qVymsfsvnX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53150}}, "1.1.0": {"name": "@webassemblyjs/ast", "version": "1.1.0", "dependencies": {"webassemblyjs": "1.1.0", "@webassemblyjs/wast-parser": "1.1.0", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "8fdd28d56e73470c645900d196fcf56d260efed0", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-ohr3yx+X8EHX9i7AeGblalmEq5vwNS3PbfKO2apx3xdYscTkMxrRqEJLpBxJqSLIdmAD4AkTJvfxnAA7PHkDMQ==", "signatures": [{"sig": "MEYCIQD/Y88JY9jmarRwTuLYXW8LxsW/Z6iMYfXKwzOKDmXYRQIhAOlWPRXPjS830jROpSZGqN/wv6A34lMpWeTGjU/WW+aM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58648}}, "1.1.1": {"name": "@webassemblyjs/ast", "version": "1.1.1", "dependencies": {"webassemblyjs": "1.1.1", "@webassemblyjs/wast-parser": "1.1.1", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "58f655d45d521387ac263ec6702061e8f75bc437", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.1.tgz", "fileCount": 12, "integrity": "sha512-V1XVMSLve41w7849s8IWqDbApugjwBbEVe9qI33kigriisUArey6DmvBw2BerstCF2tJiaTP5fJViScMYp5rEA==", "signatures": [{"sig": "MEUCIELSqXw0R+nz/t7QtrHB8u+shXSnxwUW6awisW9OGwtoAiEAvsjLv5UZwsHIYBYWdcmxju/7MLTEe+0xBkuwmKIw780=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58648}}, "1.1.2-y.0": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.0", "dependencies": {"webassemblyjs": "1.1.2-y.0", "@webassemblyjs/wast-parser": "1.1.2-y.0", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "138d5ba9f554542b0222da1b6a969bbfab525f1c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.0.tgz", "fileCount": 12, "integrity": "sha512-mK04NjspcATCe8AUKY2wEV5v1CCJlqKN/H6l2VkpAo0F+zFVRgmn+EYsRt9aoHYyHt8CdalUGko3idB3tc+CUw==", "signatures": [{"sig": "MEUCIDTVBvhZnpE1FdT3DpRhV8GmSkdmj058tsNWc3z92JKTAiEAk5sGJNONSUlbNyQTYzqMT+2ukZPXgPbZa2bkTyWZkSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58660}}, "1.1.2-y.1": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.1", "dependencies": {"webassemblyjs": "1.1.2-y.1", "@webassemblyjs/wast-parser": "1.1.2-y.1", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "5b1c1fbbe35da860de16c94fb676983125a41b57", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.1.tgz", "fileCount": 12, "integrity": "sha512-8se9TuhSG6xFbrSiQnDQqZ9gWq+kwPB3ISuWL66NK8TM6jT7cyj4Xkpai4gGW0EtLsQnFc8jqAmYFzgobvzqGQ==", "signatures": [{"sig": "MEUCIGSa7IbFsG+7Fk3y1eZ8okNcqsZ0eRorJLD+ANnVvqd4AiEA+lJ6N1/v+O7e3bXJdEWzie4BrVGTDPhO3MAI/Q88K4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59005}}, "1.1.2-y.2": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.2", "dependencies": {"webassemblyjs": "1.1.2-y.2", "@webassemblyjs/wast-parser": "1.1.2-y.2", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "60c8d0ca447c12d148a571e2477b0fc8af124015", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.2.tgz", "fileCount": 12, "integrity": "sha512-8R0GS+oBHUdc8pgAE867ywKCsqTrxip8qQ06CUhvxCfWn0hFFkqKlT8cAQFSyIlZttlf98NgEHyH9xlXF160KA==", "signatures": [{"sig": "MEYCIQCJiYLmLs6/7WUSobQFiAeHVw4cfRX/19cvfOqubbEybgIhAN67RZwoGHrrrqdfE1pCUV/AbeDOvLxn14SeeZ9F/vx4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59005}}, "1.1.2-y.3": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.3", "dependencies": {"webassemblyjs": "1.1.2-y.3", "@webassemblyjs/wast-parser": "1.1.2-y.3", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "4f106848aa72ce36883c923667147195<PERSON><PERSON>24c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.3.tgz", "fileCount": 12, "integrity": "sha512-01mXk0GSU6Wlbo10tMhZ1bihVOZeCs1VV3OucqhvqUc2nzQ/vlQVhBtLM/6edEVofq+glAPB6C6ppjrltiQieA==", "signatures": [{"sig": "MEYCIQDLH5A6Yee5AsLhIyqq0UvPblD8UGyWXBQoZpZDsjsuLwIhALbSqMiIY+CqwX7+fqRGm2/PrAZ6TCNeP98HF0xTf0FY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59515}}, "1.1.2-y.4": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.4", "dependencies": {"webassemblyjs": "1.1.2-y.4", "@webassemblyjs/wast-parser": "1.1.2-y.4", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "40dfb1a2e211591c61d9b3a2b7e71dafca9f304d", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.4.tgz", "fileCount": 12, "integrity": "sha512-H1O+5+4jO0msipuykkdZeN9+cBfFPtDo0bl1I4I4cC2DhPwj4UQwfzV+dMp9yi3gc3r5zyOjOcNxaFjGeUNVEA==", "signatures": [{"sig": "MEQCID6h4PoZiHK/h8YqwLbfaVs2xtRp0FH417jbWJyofzTEAiBjN0oSWoGvIeWo6aNRdiKEjxOVOJZnlVbSnvxAmo6H0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59515}}, "1.1.2-y.5": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.5", "dependencies": {"webassemblyjs": "1.1.2-y.5", "@webassemblyjs/wast-parser": "1.1.2-y.5", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "d12f687cddccb461208c4ce2408fbaff79570046", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.5.tgz", "fileCount": 14, "integrity": "sha512-mS6HjxuHobSPLESWMH6i2/cOwxYbJ/4Mf5IWH+EouIGfOGPQVbKkn0Dnmlj0axJm5/Ly2MedIf9j+bE3r5aN4A==", "signatures": [{"sig": "MEUCIFTZn5ilziStTiaqk2UNw6qDFZjpfqBxQXt3J6xGtOxnAiEA3WMqB98SLz3jUjdvI+9jX0BviL+qTH/LsJfM8z1KgQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60816}}, "1.1.2-y.6": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.6", "dependencies": {"webassemblyjs": "1.1.2-y.6", "@webassemblyjs/wast-parser": "1.1.2-y.6", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "48cd46498cbcf3ad0873c311a68ef11c6bd98ef1", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.6.tgz", "fileCount": 14, "integrity": "sha512-XMM1iE90kGN2VVRVtCLycyM5hpjbsLKlTYngxphg+BtKAj1LqLMWOBITKdOG5Z2ciMf21TdKeqQ8jBMuZZX53w==", "signatures": [{"sig": "MEUCIQC6k6+CyynFfkph6RjcfbkJCZ5yc3QRe1FJVrnW2TVf/QIgGgmwAp1ob7aC2HAUYG2Gr7rd0NeedmtGkvRmDH85CaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60816}}, "1.1.2-y.7": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.7", "dependencies": {"webassemblyjs": "1.1.2-y.7", "@webassemblyjs/wast-parser": "1.1.2-y.7", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "bf651c9f94a1c11385b4e4cba9a1b3585412db14", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.7.tgz", "fileCount": 15, "integrity": "sha512-MPhuByJxkg77Nx65Ngg/PiTRMN6k2ceY8oMb0B8snJX9yHY2eUygBv2N2jRo0cddcRMySmnzOd19BVq4Hq6FYQ==", "signatures": [{"sig": "MEUCIGy5IQSXelvW2QzaJFtVPg1UB3jrtA/kb5op1yq8/ejXAiEAtoBTvrSSDkGJSxN5V6zDoGEpBlROQB2zfWaXFe39x30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63629}}, "1.1.2-y.8": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.8", "dependencies": {"webassemblyjs": "1.1.2-y.8", "@webassemblyjs/wast-parser": "1.1.2-y.8", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "941b8b74e8cfe2a755fbfa73f34f5e870f50f4b0", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.8.tgz", "fileCount": 14, "integrity": "sha512-qfSyvdd9Xvvgm5NAjnJpzy0z5dUhrJlAI5hm7r4io+LZrkd6K3uKab1k8dGkS09YwnSuOoIpla1qkueIy/rSJA==", "signatures": [{"sig": "MEYCIQCmPA4u9zzqEfUCX3CiCo+p8UbXd3qGYXzqvm0WMRRi6AIhAPi1E0ZxKM4U6t/X3khKTakPdoQ1O574vUWuQsnihFZz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61102}}, "1.1.2-y.9": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.9", "dependencies": {"webassemblyjs": "1.1.2-y.9", "@webassemblyjs/wast-parser": "1.1.2-y.9", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "10c3062918bdacdf1a852d178c2a5ce83cd90fdd", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.9.tgz", "fileCount": 14, "integrity": "sha512-z4YWQ+6eEBW3yoLWFqZVoj609S50ugGGz1cTGGGWiDgRIriRAykxPWsKMqnf7jmJluPiJ3aaszi0YfxZAlcNqg==", "signatures": [{"sig": "MEYCIQCnUkTkIM1M+f4FporMU1nV5UEFNgGWsnfLoY0DoLsz+AIhAPjQ33ODwrV97Q2rD6w7FTgTxkeKP3UFfJgCgvkBAxHG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61162}}, "1.1.2-y.10": {"name": "@webassemblyjs/ast", "version": "1.1.2-y.10", "dependencies": {"webassemblyjs": "1.1.2-y.10", "@webassemblyjs/wast-parser": "1.1.2-y.10", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "a7fa537cbbaadee92d4b6348eff1349bb0a192fa", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.1.2-y.10.tgz", "fileCount": 14, "integrity": "sha512-BDRZ0VAWzOPE0VSg6OPPYx7B714tux/0H8VYv19S+iSkpRvTgQuKQJ6BSAD9sjrj00V3fNS5muQXGfpZdTvbTQ==", "signatures": [{"sig": "MEUCIQCQmKyePCIcVKYzRi3Q0dT4GK4JkjFh9ur1I/B01GR9XAIgKklQAT4EP2NLWs+NUw1bqg8qE4nynVhlBG0b5HAQJWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61566}}, "1.2.0": {"name": "@webassemblyjs/ast", "version": "1.2.0", "dependencies": {"webassemblyjs": "1.2.0", "@webassemblyjs/wast-parser": "1.2.0", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "3532c69d5e84fbaa8c8bd4fc9c82d73bac1b5741", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.0.tgz", "fileCount": 14, "integrity": "sha512-g4X9orFbSy4VKLE1O9hxYu2Z7y3yDUtSJc9dpZS6vUq2II2QFQpm1GW74ub/ITCm5Y4U7/IvZClukJS98Srjpw==", "signatures": [{"sig": "MEQCIFYc5CnxalhNSzO801LiE+2NF6iBe1wGlQH2W5x0kOyvAiBTMN3nEsr6/CYm4MpmG64Ox94gF0NRp05yUETsTDZygg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61551}}, "1.2.1": {"name": "@webassemblyjs/ast", "version": "1.2.1", "dependencies": {"webassemblyjs": "1.2.1", "@webassemblyjs/wast-parser": "1.2.1", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "08fbf3d53df12616269c5db2765f4910ee18188a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.1.tgz", "fileCount": 14, "integrity": "sha512-ie1UfY+5ruGCa4BfKPjq0vzJUf+G1jL1COiymWtMrweZ1mYjcnEP5D6+gzV+wJVndNHfm2Pb/GWMPbs92ayadw==", "signatures": [{"sig": "MEUCIQC2URXmOI0i2+a6IEL9zylXaKaifHd/ErkOg1RftnLhOAIgYaJ+/Ed7Fjp3rD+rsJcaIT0uwlCMS0h5NdzhdUjtFic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61551}}, "1.2.2": {"name": "@webassemblyjs/ast", "version": "1.2.2", "dependencies": {"webassemblyjs": "1.2.2", "@webassemblyjs/wast-parser": "1.2.2", "webassembly-floating-point-hex-parser": "0.1.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "7c82efc467f5d0305a2ecba246e4129043d62bb0", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.2.tgz", "fileCount": 14, "integrity": "sha512-hHfKCf/V8udqQuTUrtwFArsQGMKrOW9B+9mFTS0oRx4F0L1CIkkNBSZ7MjRO2p8Jkcc4HobiJ+K56DsCg3p2fA==", "signatures": [{"sig": "MEUCIQCkzGx6kaNU39i4IJ5WZapr/fFnhVCu8bE9T0iX8pI/ogIgSlj1FclbCLq7huJlrCSwL3rgim+1Mi0paHV6LJN2/b4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64187}}, "1.2.3": {"name": "@webassemblyjs/ast", "version": "1.2.3", "dependencies": {"webassemblyjs": "1.2.3", "@webassemblyjs/wast-parser": "1.2.3"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "e15f5a321379632d5522acb29f49e609b777a43d", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.3.tgz", "fileCount": 14, "integrity": "sha512-JiWrna4eMRYY+qkYaogATgzFpttIzWQTlBJq3HFyxxJINUQi0nYP1B5h7IZREj+oLYW165AC0+FMd4GlxMHa1g==", "signatures": [{"sig": "MEUCIQDFmmKdwSp5M5P1nEbcSZvWEnsfVTNPxJ2iwiGBeUHYjgIgYcX++lfDFDd1TnAL+uqKbpsUojIEXLRVcH3Nw092GHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65232}}, "1.2.4": {"name": "@webassemblyjs/ast", "version": "1.2.4", "dependencies": {"webassemblyjs": "1.2.4", "@webassemblyjs/wast-parser": "1.2.4"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "175e7e11d602d666e423535cc4180dbc80ed7893", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.4.tgz", "fileCount": 14, "integrity": "sha512-y+O77NIVA2y6e+eCfxAb7o0cLP+V/peLXv1ys0FU+UsKDnjk4OChmkQM2/9/a6Mc9fuXE0q5UOy+Ro4dBCxBHA==", "signatures": [{"sig": "MEUCIQChbmTg0i/k44+xAWINgiINZ8rxWP6b9PNCInUFuUGbWwIgCB+JaCYIDjMaRE4qNb6fMPkrcRBdXqeqUioScTsPJTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66954}}, "1.2.5": {"name": "@webassemblyjs/ast", "version": "1.2.5", "dependencies": {"webassemblyjs": "1.2.5", "@webassemblyjs/wast-parser": "1.2.5"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "d8c403fa54dd8d9160bfd87763c8b43aa610a2ec", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.5.tgz", "fileCount": 14, "integrity": "sha512-UWV2wuLcD6fBgAqXiiQ8TmiOJrCXrLJfjtC+woigwgBhqxYmz+2TB80ML/433P6aYWO6AjVGVKh4OK54b18jaw==", "signatures": [{"sig": "MEUCIQCJD8Rd/woXbP0H0HkfLutfEAhYJ0M4QShAPOG5C9AVtAIgU3IAyMJYrgud3yZ0q8R3X3vjWbMbpkHmw95VWuTJNEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65232}}, "1.2.6": {"name": "@webassemblyjs/ast", "version": "1.2.6", "dependencies": {"webassemblyjs": "1.2.6", "@webassemblyjs/wast-parser": "1.2.6"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "e777dceeaa81aa5035cec69a1e83838152f01010", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.6.tgz", "fileCount": 14, "integrity": "sha512-ZndhC4WLrI84nHFv2bVQWErkxFSMs1GcC0JjVxz5WpHWsQcb0YjG10KZylbVneZagfFPwyURzxcRbivg7bvz8w==", "signatures": [{"sig": "MEQCIFwgc4GaqTuXQkULWkpC0j2DmO7AvjDhNXJYS/JCERANAiAIpcI1UxYl2VsDr2uo5I9FQtAw92RmN/Y/LpzffhmlLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1hCWCRA9TVsSAnZWagAATnsP+wWxLiDipH8MNAHg4w/j\nUb1lT78owdsCBkXqiiDipCQvvakarT+g5V0LyuYuaWLgPr6ypHFf/aHi7xVx\n+trJU0RzQobWODLKR3+QLZ6l0hHNBBZDyUS4Z7WAkGya9mXQVOjdc0exZrP6\nr+Qk1RI+IjKQqVue/JxNoF6WQi5VMfax57u5mK7cpKyqj1dsYfQB5QKPrB1K\npkqmEwPL7q6cFkO4Cg8Tmv5RMzP1cj9fc1yJ8wppRrAM777SXaTKztTMUgLA\nQQOFHrdVsO7kZ3cYzrz2rF9+zbZN+P7dq44Ew32f7MPoj6PY1NSE6c94QnU7\npU8FWMdAJFRuz+TJp9SlWoJ0PYQ/UI6i5JSrERjufj81b9qUumkrVlYmMr1u\n5mwCQfZNPhh+tDrgMV25oORoMd4+Gr2zSWd6sCIsvdOi4PLwYuMYSSwMzPIu\nKa2vNVl2aICxcOVH8qM98Cj6aHi3dj3yVvu/jrT5l3MXbZlzzZyAL/H2U72q\nIB1BfdvdeO/03GYaDblLiGiO/KXnsFBv/WS1PE2kaS6Jqkyf3TEJKCOlFVC5\nefd+BV1G0iWC0yslCB1eu0EXECIF3737NY1ryyiNJyNiskhs7mSGkHV7eiT8\np2LzqEMZhxT0BFq+3CIqahgAPQ/TBVP5DNUzjcIFrMHxUtzOfkCEB3TA/eTT\nmnYu\r\n=Mn47\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.7": {"name": "@webassemblyjs/ast", "version": "1.2.7", "dependencies": {"webassemblyjs": "1.2.7", "@webassemblyjs/wast-parser": "1.2.7"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "2643dcda911e9dcb18a1ca40ac8f0a23dd1eb176", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.7.tgz", "fileCount": 16, "integrity": "sha512-J8UmVIThb4wadSkrYAjPJ6d9vCJeJN+f2bDiKr2AhuQMJJw+T0cD6ZWpnw6hnmGbQ9fI494qNOXKguTeJy8yhQ==", "signatures": [{"sig": "MEUCIHVH9fhVEkFqcLXq/ZpvjHao0+ni8R7CaZeFFg/BlHcdAiEA0d23ejS1DiuQ7RC5j14dUz+uA0POJrs0YRteH96kbdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5yt7CRA9TVsSAnZWagAAIcYP+gMKVQ+P/7QTRmWNSVGL\n3XZO5wF1UfZEWcjroKsmJjUpyn9gukECOdCkH7V0vqegHlDoixzqATSRwVZE\nCxQjOc5FEp5ZT/rT8S/AJr/XcYd3ql58b2xoxTupyEx5ufjJQpakGesCdg9l\n392UohtZErfPN7cMEpErdycrLbJLd0OPmX77SYF7Qyz6kJi/dGKhHWTg+FIW\nKdjBFvLEdHER5RAy6t6d2yoKOdiBS8TT5xBd16adKkv4zcdCZ5Io8udhHc2K\nK+mzKxuytUb5EZ91XsThBCbZl1ak03E3Y3DZ1uPNuR9mtn6rBk/OLpMM8yjE\npUah220zJCEB9gfJcfldYEnQ9o4AqwtrZDJa7fzuBY1Ka7Axe8fawrppHzvC\nXZNX/xGIEZ3O1nkVW3U5Pi+QrWE/EmFlca6dbcIoTmfqePEN/2I+8EOhfk1B\n7LfOa2SybBuHgpNKS4nECBYtrflQrmHLrPoD+ZqR80lmc2D1cFDIzw0+1MV+\nLe5qAmLVXJ9YxPVNzn5unJqtvpei03rQ+GcOxcvhWeZy+IMoz2UlamRufzFn\n5YAtACp24bo0XVG6aPgcZko/HqAA/7mVkSvpGUZdJhMOHtatWmQZoCH9Eh0i\nAZgqcH810RlslpX3KDn869HF7swHfGxWBhh0fk1UUAoP6FsvpIQg8Je4vKG5\nYhAk\r\n=YO7h\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.8": {"name": "@webassemblyjs/ast", "version": "1.2.8", "dependencies": {"webassemblyjs": "1.2.8", "@webassemblyjs/wast-parser": "1.2.8", "@webassemblyjs/helper-wasm-bytecode": "1.2.8"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "715e95c1df0a29b1a0c1f2a9535525b2bdc1add5", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.2.8.tgz", "fileCount": 16, "integrity": "sha512-Sc<PERSON><PERSON>eejEhxjKy88YvMnP6SQ7sdE14E142BdWeRY2Keg0YLmGP+/+tKlJ96m6fGjNoCqsKhmERbX+GZh4XLlcJw==", "signatures": [{"sig": "MEUCIQD2c+7yAWhGOybMFaoSP3z2RzqdgCZMnm8/VdntCqQSbgIgI3PWa0cy95147ixXoz2nu3uwsz6MHx7bK8GDSdmPhsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fRWCRA9TVsSAnZWagAA/+UP/1yxu4wU8fVeMD72wiTE\nF+2tQMUefv4EhTHT/KQlsAxLBdWSnrKtILEDMeIrNcDknnUWWNz2/pkPPhiG\nSRj6vY0iAt7l2UFNio/iH/uup8/RCuhLuMo5D8HIEUHQRRqDfJ7pcJFSUsgO\nD3ubxY7WRzGDatGp4uyAfNLwiV7PVpBVdtPQCMQqY64C/IS5w9UzL14DZkYZ\nrOac50Xsc71zZJ+JQjUL47pcK1jiHDTdrw7WvhufOZowwO1SgOt2Rm3thgFm\nDcsg2WnQQ1PqqA+hV/niaWBjNN4BpgX+Wo+AQoXUJ7BAa3zdD5h+ZyPdeH2g\nyglB39+Z6cRCpA7LOVFcuh5ECQkuCXz2HfTktafGkYY2dxJs4dvE9AsoQdi+\nl4X5bZFSpbebPRivbmH20POpTxi94wyOSXOU1/d5gkjEpbEgdAYy3r3dyNr4\nEo7BjsO3pCUpN19jWTnK8xsClxX4OQ9aWvKsQoC+KX80ZKJ/s3U7l5UqA1jr\ns4Feyq+9JnuviMRZ/7woqjdApMz/WcT39mb06BgzsWsfpOvFdf19Z3RvKskU\nH2PPaZHk0g6Q/w+je5wUUqcstddePytrzWkhZJaE/uPGyUFunxOC5aer0viw\ncIDVW/D2682EliLlMMAt0lK1nauGWLI9zgHrs7909i3g4trDzMK/qsWATCW9\nNJ5I\r\n=EcGN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@webassemblyjs/ast", "version": "1.3.0", "dependencies": {"webassemblyjs": "1.3.0", "@webassemblyjs/wast-parser": "1.3.0", "@webassemblyjs/helper-wasm-bytecode": "1.3.0"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "524246cd578c30ff792d0c7b49bb0a0f89191dd2", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.3.0.tgz", "fileCount": 25, "integrity": "sha512-IXMtAT2u6SEAHe8iZW+CdtC7K3xkBhvMp6RY3GQILXeXq9pLsgCwnVLEAO/pMkDCsoX/y83K12quA/CxGbuHew==", "signatures": [{"sig": "MEQCIFjRV1aMLMcGjieVWYytMJn5MOH+r96dRVdKhtR5a5FDAiAtfpD8afk6HeyZ4Zc2GVyMWUQdbuSv1vySiswKAe4yuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GCJCRA9TVsSAnZWagAAV3oQAKNObLSZtRUODmurqhX6\nsFNIGqhjizPPS5kiXjfO69PUGLNzL4cELs7sgdeemDJZscOLa7wgBjkPUVnb\ng8Fp55rcUJJmw0219J4b7hDUuhL8LGi/tLsi4j8qQJLurw97c2kMhLyYzATh\nzyo17wOrKCO71gSwijaiUUY4wqJeYBJVj2BFheVZA8igRvoMDx7dDeolQDQN\njL78yR4CDdKb1y8iInmLPS40cuKIsBy5j38/SWlyYT6a2yUx43RydCf2tm6x\noboOC+dT4tk4AQMWCbI0Ma47keBfve9bhWQ4SUnKxXJJnodJOq+b2vcrs449\nQCnRM9Y6x/ZujqDmUvYJWEZclKzaM6gA3vlySNNL3kKYnAx70X9xVHnVxRnf\nh4+kZxcB5F91b1o1mBnBVTRatf0jJvbwVq25sdDw9LZZ/WX2a9Xp9jwvRm2k\nFAnr97CkRVO/7VcZm7cPjJz2QoAInKWs+nzRdtA3A2lZznK6ykErdLJ5bsfo\nKBh+pTVJl93X9IDZ6YDsE/r4LONKl6XLERUNE+JPuZCx1Tz/gODAy9aKg0UX\nsReVamMqZtgDEzq/p6DpyMj6FjTQw1EPTAms2AmUrwJpVgqEe9t8pnLqsxpg\nhk1hgVTFEANxZ1s6ZhJvOAoEInFPZJoNi4L4h2Ja18Urt0RNzw8kQsTOuDM1\nqXYz\r\n=wAQA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "@webassemblyjs/ast", "version": "1.3.1", "dependencies": {"webassemblyjs": "1.3.1", "@webassemblyjs/wast-parser": "1.3.1", "@webassemblyjs/helper-wasm-bytecode": "1.3.1"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "3081b4b3ff0af733aa5ba573af998f33711293f8", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.3.1.tgz", "fileCount": 16, "integrity": "sha512-WJKy500MK6cM5LNcOjKlLHnYtTU5PcmL06iXErYriA90GLn7W/EzIyknZRx0Bj3W6FjYKTPkHADdE3GM0ZJk1Q==", "signatures": [{"sig": "MEYCIQCZ4/zDF83Y1vCAGXZBTx05WONGeBxLvJ5jkyX9OC9zOgIhAIDkXK03nGewpl44Si/H7vIGMyt0NiK6dITe8tnBvqCT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JYyCRA9TVsSAnZWagAA2CMP/jZrZnzEEhgXCoXFzBVt\nV+vfGcxdPqcwioK9cTi63OZ5x+7Ic4rebbMIRdnPCoYKa+nGrblMIYyiNTF1\niqWKB23jna/8tsRcBuLlFwzahxyQckzGtaGKu4NfS9YcMm11ko7EuVAEyFko\njxfhPIenlzWhFeBAt1nw1RqoYRl2gYFackTC0uRP4qV3wZQCrRPkfa/k9v4z\nMxR8qvEFlHm0FGGEkN04txbPX/RRD7Gzny6Y346qaxJsrnSEL37T+oqy0iCZ\nAtzZUeInUiVEd3GO5EbfoKDMeiGiFj30PvjPS7TPwGwHt08xdwKsdX2vfoUJ\nWmw0vlQP1QwaboTVuwGBAttYTFNDoyQgKe0BrqpcWeL82oWWB2GjsV1VYJea\nS0tSVJuf8357bV921TCnlezPZQ/BKyQTsBafN6qWBTP3t3SIDnB3wvNanh3g\nWDpLrvwjTijn16fkehpeVvHa55BQdhs5WAbPCUSVsvGIeKyt95VPddm3rGFO\nPwZzVzFgU/loRCkDiGLSh1eEufLtWx68wvGTEU5uKDS0eX2QksXp6XzTT3Sy\nw3yQDaMDspZTYWWBJSNfP8NqM3aVEHcgsPewON7Te0SpDYXnx3AHb6gJ7ZXo\njZM4/LWVBd3XN7F78RpfiYQUFfn7QXSFKAqjAJynVlDhGLBdrcgYZB8sueRa\nTs27\r\n=pRZW\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2": {"name": "@webassemblyjs/ast", "version": "1.3.2", "dependencies": {"webassemblyjs": "1.3.2", "@webassemblyjs/wast-parser": "1.3.2", "@webassemblyjs/helper-wasm-bytecode": "1.3.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "3312d6f81aaaa1fe74d2e1a42264899fe802828e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.3.2.tgz", "fileCount": 9, "integrity": "sha512-ZgfpR1zmWzbrFJYgCnBZGcntsen/ptvwTJ4hKcvo3DrcW+uvZc0q0tiA0HKUbujnkh7nZsEOtxJnoFfLAZpKYA==", "signatures": [{"sig": "MEUCIQDlraA5znV8BSnp+iY7vD2oAH7BiC08ZPdvbW2k9cbqCwIgEPzVNHrRJfHuLQzzCZP19nBny8qlSwp19SO8xIVkFgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fCoCRA9TVsSAnZWagAAuj8P/jjSsrhqbX7OgH6fOUrG\nQ/E9ckOHKBGdySDaaRQX0hL75CLr+UNDp9DhGl6JsXRthml103LzXnQX2+Ui\nSVJQHz1yMQmM7A1gvm3XEXmHGuk5VZGIBZjcVAY9IW12d68BhO9q3eQaBnU2\nvquSdLfshTOcpCcFOPoW0PNPuLenG5JFKKIRlFnh5XR9zo1BUvuh330zUVlP\nAQXWEvFX1U/k4GisWu/hpqwtGM5YM1us2PWiTa3oGvUO57ipHTY06ww6QVN3\nZbQsmxnji4LuR+h61nHROdninsqj7KNYUHl6xnriYHGRzUKkUwofosMFiTrd\nMcsz0Wl2157eI7o9HPcdQMgBVIdpw5gUHHA08TZmVZj9osD6Q1+CTR3stAzt\n/T+wgVNAb5d7f3oNf4Awp8a/X+3dz5I/Y0qKO7Blff5IBcVk0kih3J7GbsXI\nOgUKnfLYY2IfrRZ41FsoKlbk4+OZYBwtVN8cdO/XhLolAxVpYIosfyA5UAec\nmrnnxWsfxO67Ad04/1sBkSGYSWPG2GO9AmsWun10Llfcmxtd+Zg4d/0VRXy0\nNF5037KEXp2MFtGPh2QzKZn39UFlVVR8h8q/l3aWyFtkd8Bn3qyDnf4NCSAN\nZ1c+3WwsLKp0CGv0WJkdyY1JOv7/m19AtXiptCoYrEUCsf7XRd3WXhW2dYE7\neYvr\r\n=RniM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.3": {"name": "@webassemblyjs/ast", "version": "1.3.3", "dependencies": {"webassemblyjs": "1.3.3", "@webassemblyjs/wast-parser": "1.3.3", "@webassemblyjs/helper-wasm-bytecode": "1.3.3"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "fcb7c0492d91b263a7ae96cbe3db6c935d003d7a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.3.3.tgz", "fileCount": 9, "integrity": "sha512-UvFp1gfJyf6qoUyN7HiWH7b8t5tGJPbYHwRHbW7E+PPtXvDL9QezhTvBjiD9/ewP33sSAjegtpnTDWu2XI5vgQ==", "signatures": [{"sig": "MEQCIGzoGUv+iFaWmNLXmQyA6yYZUp3eFGcLtU8w8qPokbu6AiAI0rkFOeAlU7Rs3dVCOnUqm1cRWSGMqk2IppXvEp9EoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qieCRA9TVsSAnZWagAAHhYP/33UTnDyznCVfBux0+4+\n/rLeaiVlWD8P2GvFuzAjZvW9k5vIzNkF9dyOBPUDaDHddCRklo91VLO40xEU\nHSSr3/aZQDDTxAAt4UzDk+S0RwnEtUm54hS96X7l4i65wo/fMGWbvo/NCCGa\nlX1gIo7TYh1/0mG7ZL3En6GhKYv7keZnmh4IkTQLywVlBJZ9FjEbj/TBss5o\nrAe99IVj6WwC6SCToumOWRA56P18Z0qXTHepQTNp7Lju0JsqHXFY4ssTGpLY\nrG5M2Q2Uj6FlrgiTACDuknzf4VuJN/ZCXrmOOKwGRgMjHTLspIhhjDxYPkK0\nLvcNeTkJCPo3muXLjNlu6lmgkvQGv407ncpeMZ36zteD8jNNEcAJCwo3cxtc\nOmjiT4qAY4kHteTgh79G2GTrBpKaTORuwI39Fr1LSTGOyOCsmo1m4OXjJPPb\nlI4eQyP7g58Lw7NljUrz7iBui4P0aTGA/QrErH7Rr/ebeVOy85/xOAHwAZCY\nIt/2IMrT949UfrIq4fynb0q260d3ZpBq9baBbkewfEuErcZtmTwJa0Z9PBhn\n6zv72W5egh0qOoatks/58/q+5Xc31poJ5W0jLbWyUuJAk6NqhH1yfYB3N6wi\nm7dJjDdrJsJX9ld/VmWAVeM5V1Z3ps4wPoSCxreB6ZF1Wa52oJHIWgClvP/q\nJepR\r\n=EcKI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "@webassemblyjs/ast", "version": "1.4.0", "dependencies": {"webassemblyjs": "1.4.0", "@webassemblyjs/wast-parser": "1.4.0", "@webassemblyjs/helper-wasm-bytecode": "1.4.0"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "7c633ad9ddbb9001ce2088e729b7000587f04ad5", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.4.0.tgz", "fileCount": 9, "integrity": "sha512-i5fat3/DyoTxiuHU9Q4fROTx77atdLdTRjIl3kTVT6NtijvP9dJa3MX5oBL+tGT/9BtLQaw0PdIiLRUuSuvuzw==", "signatures": [{"sig": "MEYCIQDiaxb+gPpFLQFit+lzRDmgJpi26B8EWUeRKu96cRq9SQIhAOSp9HsqGwjo5e+eewYXGcFCRoNHnl0uYGH65bWtFOVu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wyGCRA9TVsSAnZWagAAp9YP+gLR5VUI3yx838lmJXcm\nLx2aS7Bd0jP5U5SgXnU2jN++EpKpK+CESSQ0gEG2f6q22XiHJeD9ooljUEhv\nsu0BdvgZV6Cn40qBgMNJGxL2UoTAuCOuROmfqD4qnaOjJhQQdDlQRMvZNsSb\nNB/7sEEA+/yhHIMgv+Wq0riD6WLNyXYBx8iEqITgNtEd2DT99SVgqaVqXWPU\nPqqsD4w7BWOMqrIR3V+yVAwzR0oEmYvIYWlp3XypznPIxw7YVGwZXt8Fzq1e\n1Hi5F7DESTPdOXaFOgbJmV6p68ZOE7njqPI2wloj/tU3MSdiKNerRt+cW1LI\nSCSQoAPXa+5i6qitkgttPD8a0dPU3OkvOuqcxG/NGB0+MkdoWWXTi7j7rJFr\n4E/pwXht/2/ktPQqVX36g2rdBbBqHzX6ezntaMyBGjnmPVzlCZaicV/yvXyV\ncTt/OcjRhxNFAogtDqYx1XVkm+ALOB6ZHw6M3eKJibQtbidUInm+sj87c8nD\nYOQ72xb0sLgghWzALy0TVCtxaalbanw/FG76vOo8w05LsE0QAmfoTr4AsSky\nnrk83013VB6PYZzYoRtcEzNg33gaik6AHYgNrLXd/3TMU1z1/FzZHIyZMxNr\nCMVH4uxX0QhtoQZI9zIMOKYYxvTOsAwjABXOxoofGjxFt0xR63CxJDjpg5W7\nD/hW\r\n=+PDC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "@webassemblyjs/ast", "version": "1.4.1", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.4.1", "@webassemblyjs/wast-parser": "1.4.1", "@webassemblyjs/helper-wasm-bytecode": "1.4.1"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "8d44bd45cb3e39c33ea0000ca7c54df4b622f0c8", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.4.1.tgz", "fileCount": 9, "integrity": "sha512-QUaNgUsm4ZrH+V5TVVkpVUmrpzxv5vJoJy5r8rECHZFgrUfJDsvGMUYJpHHlHim5AOfFt0NhkNSfFuf2znPlzA==", "signatures": [{"sig": "MEUCIEUJpfQflxJk+dTpy1Z3QgEK9a0We/+GY7QBIUeoX5HnAiEA4YrpaclfETWCCeQTNgdAFLOpKpNDvqe1irYv6ABQxa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9KEgCRA9TVsSAnZWagAAHikP/2TIT2QdbZjvj6vnTR7a\nb6hNyfKy5UNDCVCCThoaHcFYFgpPKA9HRPS9EPO06kKa4MbF8BqVzDKIpl92\nJAixHO7gkujEOYZj9ahB7WOxSyWIjO2Woo3HnpyELdo7PmdCLkQJGXtHcW5g\n8mX3PxniSElRG0kH9StpQOk+hwQJ7YN5E4NW4fQTY0HJFpFUYd5FAaOqq9X9\nzUJXtWotiLQXp55YFJeewkT94q6MYTuDyBvcGCH5zjGvYw6F8hOXkf06oi3Z\nUFiTZZYQSYNm6M/SjYfD6t5Mwc1kwNG6N9WytwqF4AGQvYhFprEhe585/ump\nUYMIB3EwWix/KF7Z3kgI7l4CYENnG4NlBNempF2bO+56q2KyJTqUyTb1nIqN\ntzd6QYeQMrupcLh93PU3Zamw9soRExlLEG55FyO8B+BqyvhS0CW/JnU8Z4jJ\nks5SjiFEbrb71uAsRRPdboInNIsKBCOy1dEbrx88iWfmmCEwTiIQS/vnrJsl\nXFUKQ/73LoUiR1cTEKneJVtmCVVzoqkzhKn3RPQSMmWLV4Qz+nH3N/WP9hHg\nn4pu+5xzYllpFVPVOfrgI96PlBfnik1hrVdRIqZXMqYTdwB9D4UzcDkhhl98\nQJDeTf1gHCAgmcu0vRZpYS547CY9vlNuMMfbwsuvQKbNV2EwnRHHnza+i7P0\nlpEU\r\n=S2h+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.2": {"name": "@webassemblyjs/ast", "version": "1.4.2", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.4.2", "@webassemblyjs/wast-parser": "1.4.2", "@webassemblyjs/helper-wasm-bytecode": "1.4.2"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "ab715aa1fec9dd23c025204dba39690c119418ea", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.4.2.tgz", "fileCount": 9, "integrity": "sha512-0o7DdyxnNsyvc9LO7Z/mvuYR2BNfwO2qB+dk3gHnfQIjsIlwv9bspORBewPsvwF29PX/aX90Rjqy5u5RYwf1lg==", "signatures": [{"sig": "MEYCIQCtAB2xEX4SKvXrMkZVSTt4HMUI0gnQmZ4U/us9PsB1zQIhANgQi+QxBxGrwB1Sx1/GI9SBYV+v0t7JxKQnYEgpqKs2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aTjCRA9TVsSAnZWagAARkMP/iGeIfJmu0lRcH8niKis\n1Vel1WrOnL1H3pX6bVrLvxTOFO/wA5tH5WXB8kx3e8Yw5XlKqWFb2cOwbW9M\nIU4pQfnaGUenBrxYVTmh6Ae+RFcZlzzzxyeIOR4prKDwbqLLiC4ojzmo06G1\nRSscIC4SjTd5v1Z1egIJWeAsDW8CjEKkYHOokE0KLT3uXBhL0GFcMFVelct3\n/cnKuwezAzSHwpuODLJZ91JjPbdij8ruCRFo6QohskbdwsHOb7A0iq447Mpp\nX/rgQHv8hgfWjU3rkra7gJ9sCqL61UTZPWQy++JpmnS6FYJkZ6XauTEcUmqM\nSRgnues+7Ojt/xV0v/13j48rxBWzOTwbFAc7o2mQOXbDT2s+IND3nJAsOnVN\nENWxrj2PwVZj7p42/WNOq0i36+4RJfsWat8b/aEtmjZi7e4REMzGQtPXmI8G\nTdmkCGv6gCELKevq/tR6iBj80MlPOjRO5HisEFxN8G1gQWYeM3pRoFnH1s39\nPK8J5ZMdZtG8GWXXqECaeirqOacCRU3FHuKiq4gn53B6l/TOrxpB8Nc/KJMM\nMvl7CerML2eZypRe4uEmd1R1PHViK1c2ZTVSwqZj259ZIbyR5NOsZIrhP2KY\n7ttXbUS3jgdY3r1E4jZEetN3OjP77S/lRTInEc+rTRcsgVUEg8M7w/Vkg+pu\n6kjJ\r\n=5J86\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.3": {"name": "@webassemblyjs/ast", "version": "1.4.3", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.4.3", "@webassemblyjs/wast-parser": "1.4.3", "@webassemblyjs/helper-wasm-bytecode": "1.4.3"}, "devDependencies": {"dump-exports": "^0.1.0"}, "dist": {"shasum": "3b3f6fced944d8660273347533e6d4d315b5934a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.4.3.tgz", "fileCount": 9, "integrity": "sha512-S6npYhPcTHDYe9nlsKa9CyWByFi8Vj8HovcAgtmMAQZUOczOZbQ8CnwMYKYC5HEZzxEE+oY0jfQk4cVlI3J59Q==", "signatures": [{"sig": "MEYCIQDoFQI+b/7BOssU3/oPLKzlMG3rwCsOxa8uMwfkI2e4VwIhAL2eU1L7bTtzjziWv6JI+6GO7saxYByRT0IO4r3OPVw1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rH1CRA9TVsSAnZWagAA6MgP/1khe2QUdEd6RmLE4n/9\ngPcsTwif+POF1eb94YEY/+YZ0CI72oRMdg0NBYh6AkbvGfQvix0LNSRpW2H+\neuU5xxl7IVcBYMXCduaIOdkTXPGkiS/SBEDXdWvXeqTIFvr93BIgeQcy3oU3\ny6avrb5nxeqxOvYTDEtMHCTuBpt8nUW8verEpazOv1byM6fjlasU6NFPBQkr\nP9UrdmNL/rTDC06OFtrvfff9x54UzTNNp7FEr8RTSguePbIFGMurePAqv3wj\nJWi3aqyZ0bHmEvIS7R7Eoe/FntYr+btoaVwbP1g1HVU4pl+Vq0fqqmGkG/gv\n6M8Wuwceuuq+X71SMl0cnST1X4aeDqzHBjwu83ERHSUxMJByIMFzVP23Oek0\nzcIMMxIXvT9AaHnY+90K4Nwe4RImRBGfwp6c4JBsxxNvjT5JMEa/WOl2N5+8\n5F/VOFawbSDl5G0D/3mwciSo2arVjdS0ZxPXumlPnCnROsvd8U/IeTkTteo2\ngaudtCwRdQluBlaPk7EgpHyF/AyP8h+NxQiciDcmLF+itzgpSd+SCA7vApr+\nivppMJnwM62eHERt79FFTTeie9X0Dh176EgD5AkM69FxBm1A8SU5AbGbx8gX\n7+QfalcG1nbT3mR1E7Tvmdv8croC5FMjL58VB2u9R5SIijUgUMgC326E5QTi\nJeFQ\r\n=KA5M\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "@webassemblyjs/ast", "version": "1.5.0", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.5.0", "@webassemblyjs/wast-parser": "1.5.0", "@webassemblyjs/helper-wasm-bytecode": "1.5.0"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1"}, "dist": {"shasum": "ee70576d035377392c1d74dba3810a0e82f035bc", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.0.tgz", "fileCount": 16, "integrity": "sha512-0AAjzRLaCDhGBWDoMFnS+c6BTlHo5MTb9dj5aDpHgwFo1mC7Ij/MIOu3SSy6T2aKX26hhMkczPl41hR+/0UFcQ==", "signatures": [{"sig": "MEYCIQCOrk2g0uVA1tj0ljb99L9F0ASgJikIi+xN2GnJjU4mOAIhAOqOE0vxQRIrnHjcE+dOPyZVT1KiO8ZZZgyvrq6oAFRc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b1+CRA9TVsSAnZWagAAIDYP/0RubVTYB5pAdaLn9EDK\nsvW4WcPrPaGAG88rewoVcpnpxsS8utjkkb9dwqCAFbzjYD5hT01Tnhan9LL5\nwtwUkb3qvGATr+2hCqJgrwLnjBLeWIRKFDMDkSEEyVAMza/jYAQBu9C2KoHs\ne5JkEoPmu6sBXOIS2RQvAMn4skKM3RV3zp+U5XGWqzCwS/+8JB1tcjOTx0bR\nD8vljtD4klE5gM5Ws7ZgQpPmaewmGchkM4egQuT7u08Y5BpethX7ZgV73T18\nOn02rnhz6IaY9wkJRc3+bYklBIQdSHKa+3TfXnUrn3NVth7hiiqC03NPOa8h\ntpvfL5+ckmdB4d3sduIpB//CCqUejQs9bXELRRXANgZk6QJXrAm3CvfjygPh\nJv56czN/94P3scYMRYUFgJxx1vXn8QxDDlnI74N2R8UVMH56j413T2JsNgC9\nT89cFLE6pcqRQJWpUAzNaAYoIgCoFYyJ5DY/3oZ7aUyDN9rqNy9TFuq5DWVC\nOVcUReTUbRYDrM8McMqnEGmgRKin4zJnxz+jN+b5ujUwv+/1Zzgtd3/jlZK5\nbkkRJ19CMl9TGkN7jMuzJMwtCdpepl0bBOWm0hZpMT0zS/ZEUxjydzJ6FtCV\nOesMIS9n6JfcivnB5TlDctuoURNXuP6PHdy14d0znLO8QNqPpwu0etSeYLQc\nja7w\r\n=r3T/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.1": {"name": "@webassemblyjs/ast", "version": "1.5.1", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.5.1", "@webassemblyjs/wast-parser": "1.5.1", "@webassemblyjs/helper-wasm-bytecode": "1.5.1"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1"}, "dist": {"shasum": "b77382eed0c334418508fb08b73f1eae198c6960", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.1.tgz", "fileCount": 16, "integrity": "sha512-JvSwXKvnvhzRLJ6ANRWA1F6Qv2SeuN+fXYM/3xXeh9KxAntaAi0ZAu2vgnEXZuBwirbHGsVXvYVT7i6x31r6sQ==", "signatures": [{"sig": "MEYCIQDUjOLKQQ/CPu4M4v5/KteL9pvraiYdJpjUv4JuAb61yQIhAMkCrk1L1vbnliO76UbYTM8aslz1ddwbhbAf4C1G3unP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BTtCRA9TVsSAnZWagAAvekP/jMznendzMbv9fTXmyR1\nc/GB2Ljrsqbo49caSgCZ3220GmhuA4R9tsEbWJ+Q0iRO3mgzBC7WVh4RVGdW\nInBZDJi/H/p0Q11VT43Tz46sU9tdj0LB2TR/BzqQteqCtrEiRG8AjnNCYHtC\n/eBoyDtJzGdTQ6YaKMm2mVrhMCNkb1pRgACIbzOb0Jz7ymFmLS/8TJ8rQBUS\nQ5/tWaPsvbX/3qcNcdbyd32tHVuHoHXWJsX5F13Ndlt/OG+ec8Opwc/Giqsp\ntYF7lw6oM2newJbkzr0cm0UtuAMudeluER+CJUAPk+PoVQh5r+SwjFi7jd0S\nVdBuHo8JWjpVYskw3lIXQwMe95Kaf4vwyTC/hu5HFCoe2ktbT7+eELnswunw\n5oNzfdr6hP9UpZ3NuesL0DHEFsr1zZ01zBwGGPTa0O/qwF6BAQ6fHMz6oRIB\nC9XpLjaxqS2Kh9eIe6OyW/KL2obrSK7ax+QnkaHwKsPG9FfNPnVOJP49SYqH\nhcMxK9CsDQnVS7Er4P/7tqq/fylWde6AlavDfF1E4ZHdU+/BQKNEKsu4cj9w\n6uroTxqFYXx+cZNvmJGa6AeKev1N2YB9ZGP1zQ5PKdKx2gPUv3UWAFpRvqG9\nr99rnvOrjyuNBDmSQGkGGQrb2daU9EiscZiQc5ZqltUz4pijEdA2SqZobzP6\ngkjw\r\n=l2kk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.2": {"name": "@webassemblyjs/ast", "version": "1.5.2", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.5.2", "@webassemblyjs/wast-parser": "1.5.2", "@webassemblyjs/helper-wasm-bytecode": "1.5.2"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1"}, "dist": {"shasum": "997de7eca709ce58f3f7d7c97927159666fff381", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.2.tgz", "fileCount": 16, "integrity": "sha512-+f0VYYDXMvgi24U6J0xjXsXR5ColIvNGl7r7K+UmBZ4TT8CUO5TxRYr+/nJtRpPP1+M4U0K3kwppNr0vSDR7ug==", "signatures": [{"sig": "MEUCICF4T93rUNnpaM8fiNEZxOK8puVpEQomHtMmuJXhXB4ZAiEAl/WwNQI9/xBlMheav+KOrBoCanxnWNAoHuTnEAEow3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XqFCRA9TVsSAnZWagAAnUEP/ir79gr1FsRxkcTfqWJP\nlsPY20hEgNNyBd4wUh7/pAcaC/+MI50g/spfAi32ToJOHUwivdXpHX+aBU8k\nMmYAzEYLkLeDv5nAQBeDJAAjuSZY/bAwFLblWKhPqw5wW13rjyMepchNIOcj\nKiafkeLFnJ5TwZngrgslD33ZoHrBFRqCVb46jInabr9XuNSr5HOGNczlquI7\noITKEMxr0WszZVscRIGoFAQ1Nd+zUyjltZYzxcNSFDbfuloWsNqK8S/Iq7b5\n36KskpVcXK2g3XYG+bFbH+l97G41tn1YVSmf/gDe7r5ykQ47ZJ0ph6LAjtoi\nuNA7nxh4P/cPiN6gj80Km16Opt90Qtavw6Cnvfzm12q3QCeUxyFllcFfI8Kf\nZdiEZzJGMjmznI1lc1lbXufJs2WztLqLcYfst+MccUhD5uCNIpcBHvtH4++n\n+u8hbf1Pp7/IYPabjZtKBvx6R79A/sXStJ6XOEId0wVDC1FaKvDJvBKYb3/o\nuDNFCm4ahFzJUOSo4hXm9cX3YHjxjSAiBJqI+M+gQQW51Cv+POpWhtAxdNC3\nS4JplS/1/T2f8WplaIYe/KgnhIxGDFnKQNvIz6xg8VF4Gl1eBvzwGEJEpmbf\nbBsM0urPgJIVTbRDw9E+B60hGRYeV2TVLieq0K2aSD6jZTpv/RUDttm7xTe1\nkbXZ\r\n=QCYw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.3": {"name": "@webassemblyjs/ast", "version": "1.5.3", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.5.3", "@webassemblyjs/wast-parser": "1.5.3", "@webassemblyjs/helper-wasm-bytecode": "1.5.3"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1"}, "dist": {"shasum": "90e48421e9adb0927d986124fea306418c18561d", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.3.tgz", "fileCount": 17, "integrity": "sha512-ak2yRkFasSjTEBhPYsmYV8FzE/0BO8fjQt6YDRluM8QAjzKg9Bgek7kuarbiO/uSRa3Uo2X226ara50DSMtbbQ==", "signatures": [{"sig": "MEQCIAsumIb6EfAbe+UKAuU8KAxdJKWZbYjZ6kz2ud8vE4r5AiBx8ND/tcD2g3wu90u9xRWAjKeLZ8ejtVOoE3huMFCNoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqiXCRA9TVsSAnZWagAAjToP/i/l1poQ9JCZY1CBtInd\n6ijR1JVbl15YjzVQg/ZaqaY9nhdlkbDuJi5fiYiCp8drg5So6/zr9AiP6+QE\nWWJ/3cgv/MV3mHTiC1w8fo2e6C+joFiMxYIbWg4kg02iR+lfg+2X7tsxw7sD\nCwltWUTV08uIKRnn9mIMXS4YTJGqVeoIUM6LpJLsucdpmkicRq0X/4FX5EqJ\nPB0Ir642P9V3cGg7Y/+0cauVNJD2GWX0f0Up8CnAC7IHb8JCvFpxhqi6YDPK\nwZL/cPI5pvZ3713DFLxnTKRlWCRsvuygR4Nzkupls6XgF7CJ9MAf/mtuGHPj\n55YMjK35eBmVb1329lz64C9wnZaJ8EUPiZ3DdvhJrQ3f3L4lbkrz9qr+K/Iu\ncLtSzl2JpQr2ecdSejmIazs43HH2wu3MAwihPFgBKecRLOiSJB2mdQS2B245\nt7+mArLvPLb4TPrjX7JOW3C7iaPR4kqTzmVex9hq5LN7hj0iaAtSQUrOj9t6\nMnYnAg52UBaTG49Vxi8uetR3l0d1yQuNCW6simqR25xIgPUfMan6Gmd+H/FL\nOUK3f8quBG78v18altsz4HHgzRe2ZjF1NysizYQLyaJyF4Z2yAJFa3nMbRGK\nf1tTOHAs2/gbcG0o/HjdvfyuIg2co6b9W1gYPVtNb+RzjXjWISvbPFxmw6Hq\nqT7Z\r\n=cDVY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.4": {"name": "@webassemblyjs/ast", "version": "1.5.4", "dependencies": {"debug": "^3.1.0", "webassemblyjs": "1.5.4", "@webassemblyjs/wast-parser": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.4"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1"}, "dist": {"shasum": "b459d032edd00c98dd3de46d6850845d5d588ad4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.4.tgz", "fileCount": 17, "integrity": "sha512-JMuKUcm0zf89J0WJdNZLaUdDjpGZp28hav0Wq1MR15qFGN7hlO0Ek4FNxPtqwust9ShVKNnIxCG8DmsdiajL1Q==", "signatures": [{"sig": "MEUCIQCrn+hmXsalgM/o4RVg1CQ4b2AjUH728w8+23jsg2UKSQIgeMWvB0/fTNuGF6D/N2snmPB/sG8h7foXeTjgp9/+000=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsIZCRA9TVsSAnZWagAAhjkP/1MtgHi8qH3NWdk6mCIv\nB93Y6KhWOO2qQ6nuHCJ375frBTqBih9VE22B0EVGOgBZ3kq9iTt5kqEcz988\nnSAOfBZ0u2SXBNKrvieGp5p2CAXi6Q3JmHi43kVxLp1Y+eD0xSbBPjSEV/1M\nbhZJyCqP9X2Oa2giZvi7XfizJYhPd7EwwU/BeH0cWyN/lsnoNa92esRgdmFx\nZaLXpmR/yBWu6AUURWpv7y9vEjUgmtgmxQLYp/XAwh5QVG/9XKV/lH5pF4e2\ne+J31DW7JP6ujX9QmiM/NMhL6kgouWOfaxlRFLBvhdaXWo7nGMcuK+pG0pe0\nXgoLagNKlMFl1DuC6YrZdZYPceubAsBaagMYCBuM4VR6W/MEpH+6d43OHh/X\ndyX2I+RNYW14TUSyxkw+NUxWuDMVojLB5Qf73atJM2W/sga85ujf21p8jd6g\nbn42xVbmGuPLBPLuvZroHomG9Tg8EWHpqNcvJcId9od6D2iHQxa7/6jdGhjq\nGhE434G1YDqUP9xuarEkxWlrRpgfrojGYb2338H4qkCuEzNPuofV/qnsVu+5\n6vtfPeL8vPlBRHKt5tsNbqkdg8M1dUiWT+UvFAa6edpcljI6VNE+/OOrTZ1S\nMIBaCEPQqb2Tm0izcS9WAk/Nh+jUUgn5mCo27uGK7phW4l72FQKHZ0kFdjF9\nc7RS\r\n=lxd+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.5": {"name": "@webassemblyjs/ast", "version": "1.5.5", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "webassemblyjs": "1.5.5", "@webassemblyjs/wast-parser": "1.5.5", "@webassemblyjs/helper-wasm-bytecode": "1.5.5", "@webassemblyjs/helper-module-context": "1.5.5"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.5"}, "dist": {"shasum": "baaf69242810b55e051b0a66c327f86af60d2b79", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.5.tgz", "fileCount": 17, "integrity": "sha512-QMbm05c2w891xNC5n/H68b/N8vWnsw7BHfpYrtJiSyTla/H+Gxr3jqKPcpwg2JaD/fTTx0Z3Sx3HGG44p3bBVA==", "signatures": [{"sig": "MEUCICZemD6ilJPXGNRayaIpbkXa3a8VOXlJ+XYnTi7f8jZ7AiEA/6uTgRwGalSOUNjJxlTvH3SHI1M4CPsomum/6926AVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnFtCRA9TVsSAnZWagAAOikP/10pIs+ameAJhYi9zVy7\n/pnUNIXbiXvoeJOilYH1VIXnRJa60OLobctfBuvR0ja1t4RBIODu5nlFTHaC\nQMxMgmEKavo/6Ae7B0Y2GD+LNSsCtHTd7bJd5MqUelmOVh9WZxoRBVSUUk1u\nangUVKuAJ6Y5G57AGUTLQb/xYbpbbfpD7gHlRCzhAPfAEIABSaBBz+qJ6AAC\nNXU4Q7eQxXQcW3XElN/AmJZZNWd10xEV6A1j6FSKpauyUKIhZ4F8JIUU5WYo\nOEtP83yPpGdbcPgCYRAPcPpjKChsbFV/J0OYTiuP8y8Yc7IwdG0QeooBgaao\nSkAsObe53NOZ8pQFaq+qLWItoNACeJ+HSxpoTbmpj7SAtowEKsu7Lb65R1UN\n4ppRDo0xaibCxPv22FQO2kG8ero0/L+Xz5ztYkRjtsoEKrEyYRSU+WsgfvvG\nGckixpOPktDNKyLltx0OsMkWjBXZhiKW8z7TTo1r6IXmKvrVDfU7idtZqotV\nE3++ueIog9yoL+NrouAWLGUy/aEy59eyY2E48hBmfysH+YgxiTv6MvqjghT0\nZSPLWBpKyCtkwbcF3T2XwDpBLplLIWwrr8RCiOhaEGdNPjXqCV1l4jSt/tBX\n353LbpiIaXJIou5fxlG9ANfUpa4yLbjlId39BpjOodS14OxS3zKVmDXPNwGn\nhRah\r\n=Z7eT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.6": {"name": "@webassemblyjs/ast", "version": "1.5.6", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.6", "@webassemblyjs/helper-wasm-bytecode": "1.5.6", "@webassemblyjs/helper-module-context": "1.5.6"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.6"}, "dist": {"shasum": "99bebe4782ccdb98a0aabc11ff1f55792c9b35b5", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.6.tgz", "fileCount": 17, "integrity": "sha512-GJH6RJhDQ5qD/hyg92Fy3wBausDitZkVdExIX7PAUWzbLfU44ViyYX3Cko1rV7m17Pxw6AtzFeUYGip0r5AXyg==", "signatures": [{"sig": "MEYCIQCaTOBoeuT178Qe8aYn4DZ72iUkNi3EK0W/yvhDAFQinwIhAOpbTbXfPjyJBqVgx55nZv777/YRtawbgV0DBL9zqmj1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsphCRA9TVsSAnZWagAAA/YP/3gA9XFdomx/9gfU0UvF\nQ5g4iiDld9O1fPg5uUa5aVYFjFqUhX8kY/lPAf81lcKy8TdI+HYdvM9CVT5I\nwEOGsrPk6JRNq/KQzntPqqTcWVSpewkPq1AO40MGsbHeezpD+7wAuDHbMw3V\n2aPC4aQAJX4x6KcsdXY77fl/dH2Krage5/dPgED0Gh2NrkgzYanhoa3xLYgR\nRUfVMA2lASXmryOUg6/vAN0PKbXJOGnZ+VETEGOF8jBM2Xe4NY3Pj0ez0E08\n93/SDQK5PSIbVMksWGiSVVS0hd7hfccC1kXYMUR+mjY2tnUBg37LhcxscowX\nUvRxSbc7+UdKZIYa5SfyAkUVZTPPYhlLKBMMM1o1eTIB3QSb/yPBXzXj/ZBO\n1AB0f7h2X9GzaM+fzj8MX3mehdUhxZUdXAMibG/68emTxE3Sm5qlIOfT8nXp\nAaZFWwtH2suvbVZE+7itJdWpKybNKmHJt3VwayOstnDdO4D/BlOsWZq9kEPG\ns4im+cmosR7uciY7Z69SfluGHGxn0XRrJxykAoyNrqlvPC2oA58/YLePg/V3\nOGjVYQp/bmTo6WYDDyywLKGVu0PnlQP+6IKUgmcQEDHY5iH6A+F303zOQK47\ntWgBkojkZeaEnIuj45BEP2+3Wf5zsSwGl0WNrrjGK8g5Wr/8hPdgWBQyeuXG\nk2pl\r\n=j/kb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.7": {"name": "@webassemblyjs/ast", "version": "1.5.7", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.7", "@webassemblyjs/helper-wasm-bytecode": "1.5.7", "@webassemblyjs/helper-module-context": "1.5.7"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.7"}, "dist": {"shasum": "af544e995a4e9132791987c92d333378afcfb046", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.7.tgz", "fileCount": 17, "integrity": "sha512-R7CbrMb+Y1fZyAnzwFSVoTQ5KywJUGGOknINK8RU/iG71EpYer8Yw0iVpHJvQLkLKrecYXfMosBT2rEQo0zGjg==", "signatures": [{"sig": "MEUCIQCL0miOo2r3hOhFGkWKIjecbyUJcKe1UUY/DXnzFBSOoQIgNruSj+AM4YFsJ5SxT1vqH5rWFC9OD7L6GIAciw4Cpxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAeACRA9TVsSAnZWagAAx60QAIowYvkxN4d16Q6jLBQs\nxs7NEPSbiL+lKdieLUDTpIxjDfbqZ2qiPVrJ2+abQ/2B25wYcCkjnS2outER\nlYw7tNpcnqqYF7WksD1sNXPR6b68EcHV/N+7Pc38DmZrJgGgETNNS7/drMQE\ncbARL4yDds/1/5DQWB+UFpVqz4RU0nRMBcdmybHCC68rFZyMrhzQuhpQr2xK\nXxd9y+RXXPcuwqP+HDxYg4NOS/PDkPW5UnoY49f7oXpdnHfJThEkQkGTfYrF\na1lLSnt69qXmO9Cx76OXOxybX/EgiM8F1vzgI5wR/Oal6Mh0ITddiWuJFHYG\n3yyw1oDbne/yS5KeNcHFpwIJZkfm+Dt+rx+2+XUlC+1+ajOJItN6JbzIaJAL\nhFWjtGGQ/n3M++rfe7yzOzHtS6jKygsS6k+WCIOi6jG1SDBJAFgV2aF8wOVI\nHVZgt35PjW/MvtPNyskaUN3Ktmwc/LvaRJaqyfDfSBjO7Ii2+DLBnISmsY3i\n+YGOCeJsLwXlAk4bj8Uz5/XCO0a/jpLZqPv80Tdfkp2hBY4jTlx7GEDpMHLP\nI+B93ZsUAdNojssvt1TuynnQzq2tOxT7KOlbsXosQ+S0TEnAN1CGJk9wSNN3\nUJqguyY6DzCZNtcniqaVYpIvauc5hs8GbBxkPfI6yjird06CCnBnHlJhDKFT\nFz2L\r\n=i4M7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.8": {"name": "@webassemblyjs/ast", "version": "1.5.8", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.8", "@webassemblyjs/helper-wasm-bytecode": "1.5.8", "@webassemblyjs/helper-module-context": "1.5.8"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.8"}, "dist": {"shasum": "f75ac7e7602b7833abd5d53951baae8a07ebb5df", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.8.tgz", "fileCount": 17, "integrity": "sha512-98D3ztcV1qa5NFvMOA3V0tSbmqbMbIU38+xXeD0y6WIUMW1IOIANXxzVznZekKHorYo2eQBQ2HMYM9MgmFV9WQ==", "signatures": [{"sig": "MEUCIQCEtUxKhUPNM4yWbwIoMP+gaktkAPyNLKQjGgr2iLwMswIgTxR16JnIXzfIJU8jnH0wu4/2ZzNL2IfWmXI+hRBSuQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/q7CRA9TVsSAnZWagAA8sMP/2PSWTgB9WejryaY9xAC\n73FTG5C5XsEbK+9TdiOreHwe7suO8S/8wBA6CZwFgOGjdPflRJUSdy7LV13o\nnc5K/sJg1anKJZ4UHpi2L77lF008AGTHfXCzKm0xaBfd7QdR50PxWV1Hvi4p\n8K8rkb89g/Yvmx4hyTFD6F9VOwR8XF7mEhnW7Dv7aX9VXJlPv7yNVdjpQXkL\nIpg8+3AYN8YzMbkKSVJprjgXAjKqhpoxEY+vaK/jExlfm6bkoDZvCgAQF+7d\nbU9Lx5CKNGQ8QGp4q6IVIi0ZZMFRIq1WTUg1FO3yifHhZ7gO6eAEJyYgvXH9\nGToDEpYw0pm9nxqsSF8p4yI+yCtPevDo/ehQMaFZqnDNz67bLgiJ4tBegMLw\nZc9Oxdx14Es8pNz7lOA3ZluZNi6BDF6dnufG46AhjTv+2apRSF959I4XVX33\nEoXOCfvXkH6xvh3E81+CBKFY7QXcMPnYPD0FhHEUAl1nNq5vQtm9580SaG+E\nWt1XUtM4VF8gxOWZehgTAdWEzzspQFVLBEJpPKdc6Zjsb9WfvzaJDypQt0/2\nRJp4YbjFdNJE+7Osvo/JGvlmrNf1swYkKP5WpXwCsQD34WkeqOb99vGVkC1u\nVbN4xSisuW1BtBMn48WC1+6Hg185n0gsI1T7A3qX6uwrCW4QPpvLRe46gepM\nw1wE\r\n=2Nxq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.9": {"name": "@webassemblyjs/ast", "version": "1.5.9", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.9", "@webassemblyjs/helper-wasm-bytecode": "1.5.9", "@webassemblyjs/helper-module-context": "1.5.9"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.9"}, "dist": {"shasum": "b2770182678691ab4949d593105c15d4074fedb6", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.9.tgz", "fileCount": 17, "integrity": "sha512-xL3hC0TOc4ic1UNG8ZZNeaiPf1klozt6rqajcy7hfO/qqfkEhLff1AFt5g2LJkTjhw+QSEYVMt7qOaaApu7JzA==", "signatures": [{"sig": "MEUCIQD5wM8AraJ5DlWKcjX6MG7YsDRk2duYIxBUlZkzpvpL+QIgWWONUq+1TOqkf6r0ZFDY6RfN2eUC0OBnzNODE1DQaqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVHxCRA9TVsSAnZWagAA3KwP/jPXe+Q3B+kNSFEvXL5P\nkzWetVs5HKgBdvJ7OvR6Uzz55g3UnAt2vMXXxGzy0LWpWZdup4fI2RsjcO/l\nhLvu3BUWgn3B57587GXE2b3kxBX2ocGr1Mr3aL3grIFSma+FVzg40dJJvTsz\npTwJxlXgzOGzVpTW9UF4yRmnd04hgbJc35V/f8dl0IwEZQjzeUElGKccBPD9\n3jOfXRx8k8+sh8uCpKswSUSgTJafEEzM5W+v1ANDU1ClmuGnc89aWiklndoI\nKti3s0zS+u2yfwpop7yCPvwp01jNM78mlSkSVovAva4jPcZS02uqV7gmW9Pm\nNLlEXvcGvNesTcKs7qsPU/wmgMw/avggdcP2vUgm1lwbanrq1r4TGHoZHUyN\nOwZNE8BTNnqubNwp+bk8tBQbFCWHipYWGc+kpIzv4UtQSQznE3wKrEVk7U77\nNuc2rX85IPgTN7d32C07mN4N/J1tR06D64HmJMiJET7HMVirzc9jG3q6xZoF\n0OGf2YPU6rS2f91v2YEqmfSDz8VX1T/S7yfXVUbsPsTCD+PR/D0n/tZupVm8\nHIGlcxyXIILuF/w9y8PmFgSzxZ7NBQWlwFOIRbmXpF852BSt21wkMsXlDYLn\nuhYvo0QO3i5sQ4UL/Yw/vtbLkmj+LunioLD+TVCgAtwXc+DHVokLPhxoKClw\nrZUj\r\n=rOlx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.10": {"name": "@webassemblyjs/ast", "version": "1.5.10", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.10", "@webassemblyjs/helper-wasm-bytecode": "1.5.10", "@webassemblyjs/helper-module-context": "1.5.10"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.10"}, "dist": {"shasum": "7f1e81149ca4e103c9e7cc321ea0dcb83a392512", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.10.tgz", "fileCount": 19, "integrity": "sha512-4BObuKRfeAnKdz5PfTp6MqSoCdj0z9EXu00PsQLzqcC55Htw5r9OXebS+sPF8T5tRTRI5/2w0CR52s/4vJ2fkw==", "signatures": [{"sig": "MEYCIQCQrWx0tNSAT9zhCJ3ox7pouBgGZAP2eTgYuX8JCwXChgIhAIHAypo/gN5LxOhcRS1wCr4t3c3RiXz3KAhJgX+eqjwc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUg+CRA9TVsSAnZWagAAEj8QAJxeAwc+A1tmuAbkZlv6\nYkCXx4T0E9F7+PWPeVin4sRoofznii8Yb4ZAoCuTOUrBmO+ZuyHva+uOLw6k\n/WtwBT7Pu5RoPi5SlJNFEMSzcmIow5eDvZq08B2+TlvSy2EAIOzkR/wbX/op\nf1T1+U5rvNGdy+9g1hYhWDntj3rqaA8ZPqrMRw9p3uH4Jke2liBOi04iK55z\nW+4h3BNgLGdfcEvb9v4azarbHXIZh1Ne7mtIFyjjGsx06VUxvZhtk7iNCbxf\nHtXXmsvhsEfUkKX1RzC00kqUqyJh/goQyvKxep1Asv9zrzofaP94bi9qxqak\nrN+yn3xvD01Z+OITV+1GujRbgAmliHpvxFhxRsh9sQlTZR7bRLyA57Uyp9XS\nFo+QF94A8c3Jh6jdIJjbqKFPu+LLLRARuv8M4594NWyaidEOU5Sig/csAVl7\n9DL4ubd+k4mcTCsHuS5T75YfCaFAvHGQ+TX922mz2qodER4YESe1/JXSxiez\nyo6Wd5j/46tqVf0DCaF77Ne/3EhUG5CoclDEDyrAjiYKpCOvZj13xBSoadQ5\nEdML8rcCrBKksjbmO0pyek/mnb0TDGBKf/qxbiVrSosyX2iX4WMmw4Wyoor3\n+GJOVkZeALAsVm+ZsGoFTZzJtvgAsud4kvtP7bzX3ObAoZF5uYoUBTLYtMC0\ny9BP\r\n=9G9z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.11": {"name": "@webassemblyjs/ast", "version": "1.5.11", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.11", "@webassemblyjs/helper-wasm-bytecode": "1.5.11", "@webassemblyjs/helper-module-context": "1.5.11"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.11"}, "dist": {"shasum": "af17dc7f6d49565ac0d973e83d61c19cd9b3c598", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.11.tgz", "fileCount": 19, "integrity": "sha512-+iIJRhuYWavDWGk8EV10gPNjqqIBGwbEGEY7Y74K2cMaoixKUVNI7aYG7eZgbufhrnuye99jjYozdhiw4oc46A==", "signatures": [{"sig": "MEQCIEUXrelAmQWxheNRuPqz7n3S27s/RRDHT6wpGx67m8VqAiBBvKu+05PvAQG0eP1s7m4DUWZGeJPMDP6DMxi5h5OyfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6IwCRA9TVsSAnZWagAAeHcQAJv9W2AIJaBeK/QE0eQj\nxQJSou0AbknAQBU9S6e40SzVVZyUZn5sHi2NN+A8x7ZByj9+BJBFMzxb7/+l\n2e+uwM26iSB8Io9/wdbt3J4mEJ0kuwrZCYIAzAAxokbkBHx3I1FHIVOLR8aU\n/VPDjGT7RzAjn321B4zucdiuVH9gxyVj+ddKMH61vERvzMSjPMLAJ+zQ4BY4\nk8z7ezxHZJGqlGgdOXV+z96vbBvPXCSU/wC4p3hKLZ6q+wAQ5GTmZaINze76\nd5ohMnCwE8FZuFe2OLCsWADDgnt7GyNTirJ1o5CKotA0A+Lva0SnG4ELLdhR\nEfpzGnpqOyvSp0MlkvTXh9ObctWkkrDcgVgRyPC64wZ87HK5WUGO6sq0+i7V\nmxs/J10B67q3ZpQOXB9a89LcoEOU4uB7gDm6DWMQ8b/u80rhc+yidTE4boIs\ndKLg9QsGosyIFmKIKUYA3K7U6jwjO/phMxhnlgdbINFr2sU7wvQ37XGjavOe\nCuRfeoOOrO5sbt+ej8gSC5tiCMdTgPfEyk8+dImGHd1to19pSGebR/YFe3UC\nt6NhwKEWWxHlCRs+rx0JZz+2hzDozPQhli6GqDD8u1mEt0Mkn2HAIHtqXWok\nSx+ORgXJx2qkLMq8gefBp4lmvTcmVu+42+2EORgvYLekT9fkZKXe6L2hUFjw\nek9p\r\n=tcmu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.12": {"name": "@webassemblyjs/ast", "version": "1.5.12", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.12", "@webassemblyjs/helper-wasm-bytecode": "1.5.12", "@webassemblyjs/helper-module-context": "1.5.12"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.12"}, "dist": {"shasum": "a9acbcb3f25333c4edfa1fdf3186b1ccf64e6664", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.12.tgz", "fileCount": 19, "integrity": "sha512-bm<PERSON><PERSON><PERSON>uuhSU6dC95QIW250xO769cdYGx9rWn3uBLTw2pUpud0Z5kVuMw9m9fqbNzGeuOU2HpyuZa+yUt2CTEDA==", "signatures": [{"sig": "MEUCIQCliRMJpjzWcS94WWGkuQwtAwnEXjpJinN54z4thiANMAIgB2xSoReitYDDGYqmmVqAGGWMeByuSJRESoWgo0dl748=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPoKCRA9TVsSAnZWagAAglYP/A0PA4Gh3cy4hfy5PI1F\nG4DbVrECqVNbDXfa5lpCGucfOHGmGvbU0O9rJXnBuzVDASZhpKokzuvzty5H\nZ0Cqk0F+bSWf7gkq5w0xyJ//Tot8Y4x4GhBtR8QHzROvDH9nxdLA+WZ5InQ7\nFQOgvVcddbAS0Ym1DdiTqHbgUo51VxtTtWsabnuYlzGjLZsn6qV9iogoTa6g\n36Jrbh+b3iQTGPcwfTn+9ViXgAX9BD7i0W1NBShUPgtJKXY2upBWiHpkI75h\niOIpCplGlaKZR6ySPj979k3cqiSBQkkpR7FD/0XKkuPH77ataxtwZ8866M1t\nhcjRXc+IstvB54tvevlStkCN9ToX5RTABatZGxk+Axr4TRKCY5S21wC+02Ks\ntgY+OZqP6iUPV/AFDlLqb8jQ8GZEybm6A+ek1JJgjwvpJIyeuFYEcAioUpMl\nsYctI+rWsoe5ORC/gaRZyzfy1zX2T+92oKNgqx2p1A4P4pYOFPByfZ/9e5gX\nabsCbBJ0ysVh7FD5RE0G3HQ3Sb1BuRoOM7NnmPRbEr75H2JJFV/790fikC1M\nkm5VbW0OM3JY85Ry+fi/ejYmgALuZ0n7r7nLwUnZ6NlCWMRYS5KgneQ1fft4\nKXwJB5eC3BexSbWOeSOSqvhn5L2sRUd2oXM+iPodPf8qaZL6OIQ1A9CkXYDK\nGbyT\r\n=9Iq9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.13": {"name": "@webassemblyjs/ast", "version": "1.5.13", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.5.13", "@webassemblyjs/helper-wasm-bytecode": "1.5.13", "@webassemblyjs/helper-module-context": "1.5.13"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.5.13"}, "dist": {"shasum": "81155a570bd5803a30ec31436bc2c9c0ede38f25", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.5.13.tgz", "fileCount": 19, "integrity": "sha512-49nwvW/Hx9i+OYHg+mRhKZfAlqThr11Dqz8TsrvqGKMhdI2ijy3KBJOun2Z4770TPjrIJhR6KxChQIDaz8clDA==", "signatures": [{"sig": "MEQCIB7+fLgH5zKOr3QXiSSRreRXRdIuV3E6Hdq8ERXCQ4MBAiANcE+0mN1A708qivyLXMi+F9NPiEFsA6hE9H8VbqVfSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lOCRA9TVsSAnZWagAAGK0QAKHrD3XTwcdp34ArLF/I\n6cNfwmiZkh0hr3PNrIKCsHA4hbEPnHzXb+d+oAnLJMDWFhmJ1Yv680O3RlGW\nbhTKybTkIemnWNJedTUTX/N/0qHfHYdAKNHgc7fMwPxII+FSZ8WgoogVwrTh\nyk+QRbSxqAQP0CF4vkDo8UfH0fR48ZKRW5WMYiGsjng4b9hRr4CLsdIEE5dE\nKwxa8db5hOtdzb7sJzyW/YTR9ODPpweV5DfU6WzPSuOCAI8wVs/akqw2UF7/\nXFTdCR4KOINZtIuuo5MF9Bh0yfulfjn5z4Ie4Q3JjWEUM4L4ugBCQem2gjoA\nF2O5bIcXBICn3MtdgNXEmPWnOWqxbpoL2fsjmqGe6qr1i6HHrxJt8vVLihW6\n7e/GNhf47qVp3KBOdfYC9lMdOsFsATgMI0ehz+HpYDcY52vkQ8dIL3MgdH4Q\nWuhHDiDLMuhoAvbITqsZwMpDTCeIjAUuLbtm/fqc1C9MasYSsUMcrtk/FdSi\nw1Y0z54N/nkKRc5BYaP/k1zcQx03ht6XKcxWzjKuJwuELdSA54vP3FEyRJTU\n4P+hm30dkdvMvgmgrdA8ocZXXobCuXaUMZ6QqQn6NTzIQMv+xJVA1IDEetna\nJuq3jpAn2PWWh9hdDRQCXKAJ6maLPxi+iwi/Prulp58uSxTYlJ4SBocLsJv5\np005\r\n=cX5q\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "@webassemblyjs/ast", "version": "1.6.0", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.6.0", "@webassemblyjs/helper-wasm-bytecode": "1.6.0", "@webassemblyjs/helper-module-context": "1.6.0"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.6.0"}, "dist": {"shasum": "ec9473faf583aa9aeeabddba2512a3b085533381", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.6.0.tgz", "fileCount": 19, "integrity": "sha512-bv00b0j1hwO/WK3Ciuh09n8LYKx0T3jKy+qxlkfpydBC+AtJoJM4H+RxMidXFtjwp9nTbSR0k5Deixx820DZDA==", "signatures": [{"sig": "MEUCIQDcD4gsMBsjws7VE9gIA3OCd6PJkVC1JFgBnTpu+shTHwIgQ/VIFGr/jwDQzeY+ni2g3WHp3wY4UEfAyLI1yr51SOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/WCRA9TVsSAnZWagAAqJUP/ihzZybYniPgk6nv6fsy\nym3W0AFu2FiCLDtCouVlzolV6DLVFzdVY9JXv9phObMEl5vtYyV9w1yVmSuO\nrEUYKYWjzQDcEZwwhbQy699qEL77i30X92VtUuJWo76TH/zwXaqhjcDSjbAR\nOQFeHktXFcUZgcWGAZpz4gri0HZ5gvxWoNd7IY1hB2wdBhYEAgrVFvMR2iE0\nRM0nUDTs+BhAny8DtOjujaasOFBCgLs39Q4vyoNR+UOWroFVJ6RssvOV+gve\nQYZMMQt41iLaZKWQ4TrRPYGjX9m/aOcQDzMaX2LhgAnpHrcb3kLuzu+hv1Cp\n+NTPBdyhuxdr/SaMUKKRK6hw4knSwtRDVGNj6Qp0Xwud0JRSABdhxJmLvmi1\n9hxu3HQy4V7E9Fyvjb4xnitCdkBfI19B+pthd5VWZKsUX4O30PNx0yi3pDEb\nswM0b2O0E5/q9cQIw/nfM7RiKNv4FIfKopWBbBcq5StRYuPZHBHbvb1iP2Nd\n4CINPKCFa6O3fLoCKZ/rNUg0AISEY46d/zzGc803wZtHz+VjGIcjeKqlh/kI\nb6E7Zr1FJydVqrqffxc0jNEd7lnN0sgiFaaVNrZdO1NfZaZ1pdAmagdChOgK\nqjStFx5B7KlpZuKTC1rJSfMgK4RVulEn79DkN82SK+q8/BeGXkNyJJPLHnd+\nxxCu\r\n=merO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-0": {"name": "@webassemblyjs/ast", "version": "1.7.0-0", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.0-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-0", "@webassemblyjs/helper-module-context": "1.7.0-0"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.0-0"}, "dist": {"shasum": "8f294f8fc0edfdc8239d458156316971db994849", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.0-0.tgz", "fileCount": 33, "integrity": "sha512-/ZDUp7XXeggebmnRUPbOVKqjkzSUsOxbNwet1BZm5D83iDYuQRSNy3GbWk5/bfgPLCcqsCpdvv1C8M2CeL3Ybw==", "signatures": [{"sig": "MEQCIF+HDXbUut7ux29qFHAhgQEbqxQE8QIETN1JtGMVrQeUAiA/yeLcwrpL5phhVlcbmdJ22w1IOFk/E0xmZEWawff1uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzlnCRA9TVsSAnZWagAAHL8P/1k7fcuDxZ7i9zeGQ32c\nCfWGpbayCay9QswlUsUWeshZTpbqbQHD/b+weFaZIWEr9DKtkGOHBsJsVQYt\ngu3mJMX73hcFgaROE/NYFn5zvWi7DcqCeF/JFDRMbaQV1LiIZaurs8qEkIll\nYkZ2OhQVzNH8vDpEtUJZPAnC3WGnC+pcVrsX8tUDvhiKee8My+7tz2DWvp6Y\npoztgY3uftc5nrxQbMP8s32Gx/N1gWfq5KMM2bTvXfEHBvngEA0kbKsPWFhz\nYKNgcXEB2FFGGalQHMTyGw8TPJLBUGI1OTmFIjN7VQsHdMiE4YGX9HMXiYEL\n9b/44uOUyu/Ck4n03ZV5vPWaLDTTP7X4gJxcnPtvjsj++0TXZ91NWzU4FOip\n1WEXmz6Q9jLEXTu6ZXejkHI0dNknPfFlx9Ke+Kin6rup9O6KtCC2lzfqz+Gm\npiq2KxFUYC/mlzs/pm09I4UnfJcD3AizrIrd9SJePJA3aHjWNlhibBXOWbza\niNvIEQCWmYn/kWFYAfKeMK8XOMHb1qCAM6neEhBFNzuui7vHlDtIFSpfS6Pe\n9HPGUU6JcEM/iwSHknb2t1W947D7SmM/ZWRtLpMpxtR+SuWAwVNCteKNjYYx\nig7ERruNx33iMdahwcNJWFBruDdp944+RYRxVK2kclglBWlvzXvmsf3ky0Wg\nXvoe\r\n=be7J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1-0": {"name": "@webassemblyjs/ast", "version": "1.7.1-0", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.1-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.1-0", "@webassemblyjs/helper-module-context": "1.7.1-0"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.1-0"}, "dist": {"shasum": "fd4b207009372613b2d9657a67c71e2753986a79", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.1-0.tgz", "fileCount": 33, "integrity": "sha512-Kgg1kqXJMywTKxcfYb5DHI+A3Q4xoZFh15sHdSZVwdB7+Z57SP+BvIrtXps0GoJgc0XK/XewNa9fw/n6HJRk5g==", "signatures": [{"sig": "MEYCIQCPcBFIn4v0KLH6M6UmlmX6bwNhH3LoC88+jVMz9qPoAgIhAJSIJt14POvMvcG7Dx6KtLSAB9oyhpMGYtntzxTIvmkT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzwOCRA9TVsSAnZWagAAiWsQAJCdvLYTU+VHKjMxC8Jm\nmiQapsl3tbV6jvuy4O13hb78Nvu3u25NGbcMN4+w7WgKQUK+0ptAl/+4pej3\nlcJMONbLVaXnp8EHNDVkNr6RjQXz3N5wmXspzbF34eqVzK4LKSl1Eh6Dkbdh\n6OP2sMvd6x8fRNSX0H+JXAyTkV9Ts+pY+5BmSl/nnakUE2WnWtOJFLAUV/8+\np/rnRk+OLq9eIDDfOVMhMDDs2oGlh2jpr1s0MrfuqMJdJLbZQrIdpFzN5HZX\nn2FTG2k0h0+BeIpEcEBZa6UD1yGnNH+DjXv14Y93eUkfmSw8zpWaQV19khpn\noZzTQXGOTH7AdxydLUKsN3WjskDrcVhXenKcGAP15o3mpr1Vr0kOooqWebCz\nms+y/rT4cCatG5z1xCM919vn0iGFHrexpGLs5rYJR6mYVbpNDa5sIej+e73o\nr8ntgGHJuLXHHGA4Pt+IO655C4mr+Ag47Boy57yEgG8jwAheEPM/CHsOech5\n+opn8EPm1s2l93Y74GxrvVlfadgIg4LHJh1BPBXWFLmYjaOFyhbf/KEsyp0o\nYZ6zTXEwzUan7m+gkyMB022bw73pW6Gns3xSx4GK4DAWXLbDUstYtacwxvlK\n/AVFDKVpuP2ThntPs58aFGp4I9cBxe3VLd3cxFhPFPMgVh6REfbr6woMGcat\nYPRd\r\n=aWEz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "@webassemblyjs/ast", "version": "1.6.1", "dependencies": {"debug": "^3.1.0", "mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.6.1", "@webassemblyjs/helper-wasm-bytecode": "1.6.1", "@webassemblyjs/helper-module-context": "1.6.1"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.6.1"}, "dist": {"shasum": "3858df394cbb653b58a73c5049970766ec75be82", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.6.1.tgz", "fileCount": 19, "integrity": "sha512-4c8YsqVXPsnH/rympLzKr94gRCS+sx9iFANCd8BoC2a9SXzqGQiy8MCKerBmbHTWSq0guc3MTFdrAmBMIVMeGQ==", "signatures": [{"sig": "MEQCIGnkZ82yh0NAE8qOyAMgd7n0vkSX39lETogTpS+kvNzpAiA/bFqmtzHrVo7ZDn1s0auRujsfFz4LwogL4vyE+Rw6pQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0W0CRA9TVsSAnZWagAAdRUQAJKV3jtlTBYXrCTpNdpS\nAAbA2knLfEn7zvZh943xYs2wnoPNeCOUaJKP281f5m6RHuB9A/pabHWx4cdo\ns1aoMibRGTEPfZxSvSl8ud5P/D3FK2D0OWYkUNWTIy7XDEWRuKUtF42kWLDv\n5lIHAjVmOEux1ymMNwL1VC46sd25o/aJu563PmG0cv4Exstg7EzcILZgC42L\nBt7POyKH0y2Zy53AfvJkn9dGs5ZH+ZtAxE9Xo82b4IvjMorhRpdr/TcFjTCP\nUzRvt/rrZpEPx2rwmR5CxvSWzOgHQiZCHM6yqZmKeBmgrqzfZc7L2VYP0Lme\nTjvYOmm6WNwiER52vDwzqqU9q6i6VXkL5nvGCRtZ5wb3U4yzWLu4sDGdAb8p\nyFbO/7OqYCZT0h1G+X/8jXzVf1o2SWpdzC9+yf0rxWzw0ofCrTqLcEfsu+YM\nNETlx5YE1BrvtY/BeFvwNWBskxQBK1lXTcuUoqj6zVTZyGWYCPJSKLvf8LFr\n1VXrw02a19nfBhX3DsTW0dGX57iOA2c6CrmtKYWj6anPyV3wK3Sn/dtrZEJc\nM77K3AiXo3zdgmtfa0i4H6QorYOAPZ7KEMGi8YaeE+lyI3amLmhjDUyw62e7\nnVHKk71QV6jefC59+Y6bHxbLQtwypoMPDV5TYCBaAktUBQQRa2USepm28U8O\nATYB\r\n=XoWB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-1": {"name": "@webassemblyjs/ast", "version": "1.7.0-1", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.0-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-1", "@webassemblyjs/helper-module-context": "1.7.0-1"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.0-1"}, "dist": {"shasum": "5e7fc43f03be30f5ef71a4e1a1c78563c889403c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.0-1.tgz", "fileCount": 33, "integrity": "sha512-pPJ54vS+6q7dYiM+O/cJxn/zOHymgBiBmFeCLffg/7U98BN2MDhPqNC7MTdODyIpFqnJW5qAtEBhsc6OtFhDyA==", "signatures": [{"sig": "MEUCIHwU1ZAynwQDLkTidNDYIhKSQU/F4XUHT8arD5S7wEfDAiEAwA+fAiT/Ir7/OAD0oP0kz0aq5WDkxrmYbi1gPfE95FA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1X8CRA9TVsSAnZWagAAhwAP/j9r7d8hlZj+tt8CAPti\nKGxkK2X18l1u6MKjurDUpBT4ctPIpkh6tXGSn+24AOWIApUIhwZR73GxnLqk\nZBP/Cl21oAI18P2qedxsXDOuHQ/weDYTZUaK2SOPf39HvzyCt33gshcmYVdK\ned4gecZ+jgC2qDwj9/nGt2gg3JuQHVrFbuk1/9thFMaLV6l15WFRri2u6afF\nbsSD2Q0DA3HW4kvJN9f2fuRGdq7u0Vbr/mBrvGesPl47RZs4FNao3FQVhu8G\ndqbGmIJheK9gFfDkB2i17BihL+pz7ZOvn+AIOtev26fmWAJcbn/Ir5fphs7T\n5kDkMc/CJCm6tV7+tuOoZ2EldFD1eEJEGfw0eQHj5r2p0RuK6UxUsHCtG0S+\nauPgryDR7oDYEYwnte0aL03FFbxYeZQi92SHspwGk0rmfhecQUqdGZSZ1tTc\nncDBUdhvNlTRBijMUtDvHW+vfCwjLCuAWWWrF8aPbFSoIQSuv3aF5/DU/3qI\nqIjPwyQkFzCmeqWoRKmAo/JwTSHkBBYHn/s1BKQ+Esx0R8VaOdvlbobng4Ou\nOueqqGT52rvq6IRPFZNUQ+so2FN7TJUA8AZ6uQiJvXX4vUsEbtVPiM/swpGA\nqBIUG/9Bs+eGkMkfVFKaL8X2e5xBq+GJA/jIE8iOqi24QO0khYfWjJRnx3P4\nmTSQ\r\n=4Pca\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-2": {"name": "@webassemblyjs/ast", "version": "1.7.0-2", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.0-2", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-2", "@webassemblyjs/helper-module-context": "1.7.0-2"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.0-2"}, "dist": {"shasum": "a136ee18992b0dd073ccc78379858c89f28f61fc", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.0-2.tgz", "fileCount": 33, "integrity": "sha512-fVPwzo8ozGDWSJvNHJX9r18casHdFtJjeY3GjNPvPOGUJnOnC9NfaURzlJELilIdKaJpp03DFAJjTGdkp1cHdw==", "signatures": [{"sig": "MEQCIBGpiVQhI8NnVcVNiKcDjWv72sMwNZ0nNvGI90GKRLm7AiB3OkRFxK08gOX3tRnSn6ken6rVQuiTSp9oZp63gL5Tbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1maCRA9TVsSAnZWagAAqAkP/0mtUfdgwa7iqxbuBf/s\nvI5ClzJ6awa+sE/yIEdcxyqxy274D5VC/2vuAA5cdSU/PyZ5pUkCD2+LsJeS\neXHuv/x34LM9CkBjPJAvSdLHyT65Vo7mcu9NBQYOg0WosqwuvaiO7D2svuq9\n2Y7nrrkgEnIQn9R3D7AGIFiJ/7N2Scsjr232SkytBnntUkur6vOh1zdIEbqc\nuvb8dFALjqOstiEzRZVsuz9X+zk6l1dn/2I+nRyag8PeDTBDYcY4wKg8JpoM\nIUUiAb6aH/tGi1eourIJBidK22CUjfYK4iH3ZtfMYZ7yGkL6fvhN5B9ubvh6\n2gUGgmmS8fJKUf9PDrddKgfWgiGFc2w4oth7Rwjy4dNUEYpSDlAzC/6bZX/I\n+4mJstw/2YXnRvFjBcUteGZMiU2EeBxuqflbkIwvH/+R0dEleph4CbrIwWcj\nAe4Xw0imRyyfOFtt5BUjNyJjdj18CAwIY74pn5XCWLv9IFOCklhIXIO/cTxF\nrv+fxMjVoxK4pdWFvfRmqhZoWvnZne5+2QZDcx6UWyLJ1BX4GiigN7tf7yfc\nWMFu+eT6KF0zKL1ybuX0jr1orrwzziK+Cbv39/vfUUmeCRyBo2XtM4g1dmTM\nWVGJVg5kcbGUuH7oiGyV2D4HIqxS8DwUqkm8KoKh6XFnfpBpdlN/LRnrBkQq\n7kIu\r\n=b4Dg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-3": {"name": "@webassemblyjs/ast", "version": "1.7.0-3", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.0-3", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-3", "@webassemblyjs/helper-module-context": "1.7.0-3"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.0-3"}, "dist": {"shasum": "19e15a51edc28e9caa6d3e2b35dbe417ef11ea8e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.0-3.tgz", "fileCount": 33, "integrity": "sha512-lgcfHb59BNtcoSq+6rIzTiBNlVuEZB/ba1KzOeP38an6hBJRLEaG0RVeKl9H9rpvD5kv++sHPeJgnty2Ba3r3w==", "signatures": [{"sig": "MEYCIQCnlNbVTy04PJVB6paPI6cMZs6yDUJcAKaIjyWtj5skAQIhAOohM27FL/v0sV2enSqrbS5NO1WGhLY0Lc3lbQHtXDXm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5FWCRA9TVsSAnZWagAAU3cP/R2HCMa/ONTRcvB0e5av\nIRX5kR1k6B0xMv2aLBRUbOINZG11iKNtDhXYn4f8vLIGpHhlaN7kurrKM+Qa\nfLzyxk7csI69zHkvxPX1czy+s1wLgowVodgKGo9r2Yz+DKrHJJUGYPKDMYtt\nB8sHLKl6VdTgYNa5sjfkt8ioAW4JG1iWQXHtFi0Wp0cNtijy3HSTtxPFDT37\nX3A8EQS/lFppfcdvQlCqBUKOHygsJFOi4loWvYL3uqm4r+pKOvw2oBeEaVF2\n9cbt55IqrfwqfzA95iJ/WniUArLN6BDxsCl4TDocJ++w2h7gMSsyQj8P/RAc\n+eDI6v1Ol+WxbG2g7iu7J+S9bPnpNPSYVzwkUYikbM3yjdDfaGp1eQMZvDbm\ngIcX5w2a/8A1TLGWUdh2SSkAhh1eZdMWb/BMv0evz+s5ci+EsFJ+vHzgKEF/\nRcIsegK6y2pSMV5gmh5TGB1Vst2u9P/f8+EVKBcw5kwuC6uTTPWsS3pF71N1\n1tbEU9fn2OEQvR2y6I8JtINeQ7+Rd+Ry9+xAK3bouCZhQaSFk5XPM3FFZ2Vd\nQh6CWWi+O4nm1gg4DxI0EkRY/kQWpaJGAxyJC3Bsrbw2FolKBqbB4RBhr68d\n5Q2E2qaPdI3R1PuhEE9TgkiVBfj+ns+KUOGV8qVDUOqrdVk2UuJZoAIztGSZ\ns0Z4\r\n=m9ZD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0": {"name": "@webassemblyjs/ast", "version": "1.7.0", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0", "@webassemblyjs/helper-module-context": "1.7.0"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.0"}, "dist": {"shasum": "d26d60866a1cbcbfc853d2398de74bb8481475c6", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.0.tgz", "fileCount": 33, "integrity": "sha512-0uRFMPM/tHvmO1HQlh2CIIlAVg6kTQkl0K7OAtYx9FS6Fx8qwMthIkZTgeuF2XHYEcxDVKn54w0mH5tNvX22FA==", "signatures": [{"sig": "MEUCIHvreuE/teII/bEB8BZQvCv4luJDv+b9RE2vyEv2zc9RAiEAxsqk5fULNv1IrlE++O9w0zC/8YO51jJBFq8/gHDWxiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULXJCRA9TVsSAnZWagAAaNAP/iHlzJX0THw/ltCQtd5z\nOhYgcej3sToDC4BNerlGn7OlF3i2YT8aR6kVYnd/LjjNfs2oagzA1no56nLo\nkUIYJvLxOCxH5S1Bjnt2AvlOOXOkoSTN+J3kxjwC05Ac3aLI7YR0yHVjHpAR\nDwnk0iCUi5Np33bIsHOxbErgd5NsF0FAI4ZzxIh703HSnH3ItpFa/Z3ffJ4P\nRk0XIlpgBj/aTmNyv/GKjVYAMBCGp9mRUD5Z3yy8w/3Pom1Z9qTnEpqinTZz\nBg0QoE9Q3Z7xbNLf54ZUD965Be2goZkJI4lWZYDAxSPthKuk4L/yZQRqi+6w\nTe8YnSNJM7+PWCMBA3YV29ma30Bo7xMjjt1kWS1L7WxgVoIkE5N9kOaX4Xof\n9MkSMGRkekPP4VRkFBBKZCTSjCCz5IdWrvMmsLjihhbu9sfE8HSs30vX7T01\ne8SBjWV5a2SdfWDP8UGNd8TgIWesGv8A2D0xQlQUn2mknvyozlmz1kLQUzjF\nnwCKGMq2q/qCxFdTo52iPoHkE8XMVb6Xmjfgo/b78/tu/huPIQYekdDftiYc\nw4vOFzLlt7LOuRoGyoviBbdZeFA2FkN/AC06mfhcT/KQ0kxxJGkhoUe2bXn7\nqRMUYQ6mzSXT0VKx1SFOj0W8V89zXNQRf4p0mDdmwuHRRH1sDPWO8RiR4zGL\n4JXU\r\n=R+ql\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1": {"name": "@webassemblyjs/ast", "version": "1.7.1", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.1", "@webassemblyjs/helper-module-context": "1.7.1"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.1"}, "dist": {"shasum": "b751e8e7d4dec439547a241541b7e8a923fb6970", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.1.tgz", "fileCount": 33, "integrity": "sha512-2K8nCVVmBFZESnMdVgdP9iJ4aQ4wsv5yb3E5EDvUzc0nDeuLzPECDyaE07gTtfxNl8J7BVqadTovnkGZ46FAJg==", "signatures": [{"sig": "MEYCIQCxSXqv+bXh8P8XeQ2lOePFJOSiRBKk2U1/hLxKNbX5hQIhAJwyf1xylv/tFmIL8b6dUYrQChDYMaPPmCB7IdrAiMTb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULz8CRA9TVsSAnZWagAAPjAQAJGgZfAR0AIQT3/iEKQJ\nJFNwzZe44/6Xzj2R7CB4pHj1SwPvZ8WGch9x6CZ+fk0yxuvHmk07q54aooL9\ni45Yj1qYNVocajK2XkAJjHkRR0lDy9LoHIgTPjZBGLCsz03pIlehkWfOrlfL\nSGujRH01vemqiIc7tYJjHL1UlTmJolpm3TyYheD3ekrlhbI3qblpa+VCQpBX\n9SvqQII8CgYrdM8nuBMDQX/qc496sevRWsBWevORqtrUqsXA8TZmu0FiAVto\nQ9xaa3/pdAhsGPgVO2zkK0qeBwe9SHOIypurUL7qS7tPxPIdJ/PwJhdyYoYk\n1E97O1wHcTepz/AmPcT7wlRJfcEkU2SY6ZUiRGKep0bIRPeQEbHp7yTwo8ld\nXHlCcRpIZ6w0kDq6aOWZuM8IMxwPA+zpibx1d3LL23r1aoJ/R3Nb1MkHsrr1\nsp1nCk45XjbYjg2TNXJF3vIT8LXaMpyMCKAQH1hDX1Cs6+KevA/tOceW1H65\nAsMacEez1UBelucTAbRLED638WvlpuPMRLjK5cUFNiV1k0lGwjdjUugv2Q4l\nkfeRDFhDKz0OLw5oFU2ia9908he3XAb5JnmOEtWCWxRXtcFOwFrofmlzQxMZ\n4+4Ce9Y1OJ3y/p4q/xfQcc6tdk64+jINqnMmplVCdJzCDlF0rDBCpF1CLRDn\ndxJl\r\n=kEcB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-0": {"name": "@webassemblyjs/ast", "version": "1.7.2-0", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.2-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-0", "@webassemblyjs/helper-module-context": "1.7.2-0"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.2-0"}, "dist": {"shasum": "6b48bf163ca66c9be779fe4c844f89767cf22348", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.2-0.tgz", "fileCount": 33, "integrity": "sha512-Z<PERSON>qGR9SwGc5pp9AbxlQOgfz10ma3UjWduY0nsHL99yGKHV0AchRpbHez6eLyBGvJbpaX73IoOVWrnT208O8Cew==", "signatures": [{"sig": "MEYCIQCY17WSXkCiMZg2ijrArKMHww6fJOnoD3OWp6tTORgn5AIhAJeAmExMs4eimrVAHm8+mXeczf/5KcxG1WaIupUI11w/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNP5CRA9TVsSAnZWagAAbrMP/jg9qbkx842iQmY9AxpL\nqxxzOBUoHn+SvkghnnfkOGvFHJHI4KjCUc3ibew9xLTzZ6ixQZAWjJLXiya0\n2h8XdOyenMP6CyBjBs52gUywnIuCphFr7+VZu7+tklBYrfC4KPmeAsEOBwMD\nb9rg1RTV2M/lxmdcoHjD0So9kgLesZu+ZdIFUcIhRgf00yLYVnBqkMZnWDAX\nnIQ+UZtMy4UKiuEilv3oeF0cZKvA9AHKoq4mdzKLykzCa3Oy44SWP8qShjGm\naaf0Me7jw+O836YLiVDYcpedd0Jhf35TndenQIaqCV502HKO4R+3vvVqUu3Y\nZhdkeoWNsRFSF7jSniFFOIrX2h3PNhTAvVWXAJhwLlnom6OAIiU5qUAQhHeM\nbzOggglphAE3E6qP9Ta8xDUAoecRQZfh6SE4A2LUgBiEOdi63aTB09ljmXfb\naQ1QGfH6xBGIPjpqe+OVzfnpcgfPKjzusDTVVUAkj5ylQrLf79ZA4t8ae+lx\nCAB1fmxuxkJ0upktRadG7AfJA2sDPmpEJgBokSYQvYcYNlA3aJw444eIyton\n/Acm16pqbBswPdd9em4v9pfa8PGIJ+impoVhhD38VR7kcAjvz8C+H+hlnE27\nAdywXraErtNCFqiEUzS27FvfJwLmDGN3oE2LRPcGX3W8xGwWXq2ks1RPkedV\n71xR\r\n=P/ho\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-1": {"name": "@webassemblyjs/ast", "version": "1.7.2-1", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.2-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-1", "@webassemblyjs/helper-module-context": "1.7.2-1"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.2-1"}, "dist": {"shasum": "7bcc884d8a6d140a657d4d44e15afbc25097685b", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.2-1.tgz", "fileCount": 33, "integrity": "sha512-WbDPkaGx9nnzz5+bo35YwdxViFoJTwOJL3ipDOMCBnrjBiNXpr7EH1bUXWdmnrJvSH9dVS7y08Xkmb+tPKM1oQ==", "signatures": [{"sig": "MEUCIQDWL87NA6qrRFbqHR6QfdWcW+syoLIg5Za4lL1FtaumRAIgQectaXydmXAClzNd9mnx3CAcVX8/vxFDIYPXdgLoo+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNdwCRA9TVsSAnZWagAAVsQP/iCAJ3m8oR/ccj9x9Q5k\nOrIRqCSGKOvfXxyYHSz8//3QGNmbJjxUfOQ3FmmdBjVvadWbH53/SYzzhgWf\nyVvvOJklWSlN/3I5UhK/rFyKibEnn1o0DtoeKUtYvJlGsbqfXPR30TGBWlnT\nsQJJ169ulCoZk9+M80F2cKqn1Pfo9Zmo5nspHLuVX3PPQ0sJer112X2pcSmC\npWLn2IUdeQH+JrpY89gMowmimQYCWD6MROmg7+vQs7pPvRArnzrHMcV7yFIX\nd4tTzsLL3kHc+RsX2mb/nYvtLPYT85a0hGzqfXchphM4aGCq64IzyxTmT3uC\nPpwB5fxWnR68QC1XcR3+dXXNWBxP/+y62HF5Dn5H+mrt7BT615OzYtlKvI/m\neoz4QlGvixvR7R8Sp3GIH/A3laWmu3tzLbQV5P3p+VXO3sV8vKtITnlrIqnA\neGgU6HR66oPNFIyoS1aBgnznWYWLrFCn/R9nX8UqjXb8ZzpNd3vkfWeYY+H5\nLUaMWozU7HgHSKCclyb9bsSMB76huiR9BHF4+tl+K7a/aRQ2ksTUqq+gfuV/\nE2kW+YgVGpMANKR0EAsFYQmNCxswtMZZ+RVlERe68db/j7ALwRS/b/Hk20cK\n/2oI/rlYoAH57Uda44ILbDtmjMo18KX4L/cZbwxVk0Zy8aKM+Q1PLeWOLW9g\nGnmf\r\n=VpFF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2": {"name": "@webassemblyjs/ast", "version": "1.7.2", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.2", "@webassemblyjs/helper-module-context": "1.7.2"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.2"}, "dist": {"shasum": "e3696253f1815db75f7b11ff4db1763b8722948a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.2.tgz", "fileCount": 33, "integrity": "sha512-DFLN9HK5tm64cqXXrpbF/IJKYVLXDUfBbCjSo4liPBcBlnLxbfF9V6U8i9kIApHxmGZHqfyvi4Lf/VKMVcb4nA==", "signatures": [{"sig": "MEYCIQCH9Gnu+Bsjywo0LRHvT89jIRpNUcapATNSDqHLAdQQ2AIhANNi/poBjKf2qQvCxFafY5XXvePyeWiy1airzAKNc79i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmJCRA9TVsSAnZWagAAbnwP/iTeOQvt7ikFlfohz6FQ\nAwBPwQlKm7ONlMso1Vil7gNrXCyAhmtGMe9oUeVfLOw/7J8Gze/9i/RDvvtm\nT/6raHEC4X5Nwc+3mfhSu3d627n2YR2GI5r5jaH/8heCMuPz+Svv+NowUpXi\n+pgrGSijlYcmXWwhVQtYKu4eAHPglHWe1GpiaqFHkghcTJqzzLrlQFUagdvB\nFvhlSJrZWE7eRBtSewJYZFJ7NicIt+i2Sgt1RymrddbthNc7hWp6Bghk/TFe\nxD5B1qyz89RyRE45gyFNCrw8lOL6lx6Hqg/fM0najsG4PzAaZZKf8CtvL4Rx\n8orlBmDviPR+uUjYVgbrXlE58wxu/c/X4HuYWxSTVGR2Td/YM9XrEjBqsf9G\nn4ZbqIrsSGwVOsCD1w2llQ3oBLSbIJq/V4aU8Vs9CxpMvFw9fMEy4dP5qwIL\ndfE8bLX9S0lPAciC+Khs9kTv2Uwppek/s2O4OOkSSSvGGCl1W9Vew0/hBKuC\nq7Zlfytvq/WDkXbMghfbI3zW4/4kD1WuDMAiK/IEtzRhkYZ9oi6IU0eoIlnt\nbZXrzKovm4cIIXKSLzUNvXvA11Y0BIEByMZ7tYfzz9FioZj2rZux9EW/hCR8\nyd9z//qcbDtyPOGwMaXpOP4I7pLaw+PZgikmKmkUgk5nB0IrpypEEVISYD6J\nspLi\r\n=KuTF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.3": {"name": "@webassemblyjs/ast", "version": "1.7.3", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.3", "@webassemblyjs/helper-module-context": "1.7.3"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.3"}, "dist": {"shasum": "aecc6f26eabf2e3f2d43544a70ea1631556641cf", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.3.tgz", "fileCount": 33, "integrity": "sha512-hpCxqUgZKvmc0v2qgrTAWyU4czs42PZ9IjZb4F/w4frbhKy/6pu1ncarGIgy6L//tU/DoqF2cTIJaju5vk8XXQ==", "signatures": [{"sig": "MEUCIHRSrBIQwiv+2MW4BBzfqfy4R+Ii86yI6w/i7GL0qxtyAiEAgaPCp1hK4YVaJexwFS+3nqGYStixDe5PEfBf/apvm9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2OCRA9TVsSAnZWagAAuEUP/00Ofxvxy5YaTpkfmNZ5\nj2TfLL/tOlO+T6N0CiojxMQVBR+2qN0BAvCqZbqHDARkYNhYahCu163ydTd5\nYsd2lSbU4HY5s4excyhcXVSjemR5ORhtASZT0rcF6ad6ZMTHeYSl5HFLvzPv\n1SS41krRXKPCPynlVxtU7OYmkzZ625OTwpQ9FNdhf2hdMO23+KpNC+/iVYNS\nqWYl4uBVUP6RMAa2S2AcC0uIWLe5pMLXqOaxv37O9iIz7ii29uSOWJ4fiXb/\n2sxOE8lri5fbJ0dPz+f0xwQEr/fH3kHaQIbnsDH6TxkHei9HVeHyOErvljA5\nWD1dcR7eJ45O19SN3ziPxta6rknkrEbWuQiH0OcEJ3eVPeB33lxH8Y4uEBTL\ny07D0yUl0/mowB6uZBxRxYvFOylvSDvyQwSm8NIz/ZJBs2dzK6R3o8vnMcyG\nbpEXEfErJ05EKCW7YFfypREmiDlD6J+u07QhwqfbrlaNSb15cIPzPhecHXAp\ntJVmOZ9jhP69sFaSr6NFtEEPt555Zgp5hAIZL1mVH7IEb9Il4f67VAaKL35T\nE9aXtgF1ueQ6Igl+vQtDubgKdub//R38zLry2pFaYxfUiqyBqFHr81jjrC7U\nD6ShPyE+M08vY0KFKCQ17VzHp0z9fYmJFo/psX6GOL2xvNLIjlZljsMmjSNL\n1/R0\r\n=Cv8B\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.4": {"name": "@webassemblyjs/ast", "version": "1.7.4", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.4", "@webassemblyjs/helper-wasm-bytecode": "1.7.4", "@webassemblyjs/helper-module-context": "1.7.4"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.4"}, "dist": {"shasum": "ce4cc5bcd7accd73f361cbd5eb1c32618b3b4f85", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.4.tgz", "fileCount": 33, "integrity": "sha512-SazqjmTxyqVRVKNjn5qiarxswvhTtY0uR33q0co5UGyVLs3vnoYRs+iT72/FmNs3XCt1IMZjWa5c1CqCrpVnOw==", "signatures": [{"sig": "MEUCIQDeiuQKce0RRACB1GyUWENjY0lSF2OUliJ7OOCULUbJOQIgJvcM8dTGCotAiIehDUtBK0wVpAUhxqcjCwOWOtzpelM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs8ECRA9TVsSAnZWagAA3YkP/j1R6teyKaJLvhPMR32X\nlvq3shlVf5YH9fRh8HpahKVEdeO95KpmrnBVUGNEDXC/BuSTA95GaI2dL8+2\nkkaUSSoJDzTgvilld4qmofvzgpMgoGNRU638QBbkT59Du8Mc2racwC1e8jJj\nHUyE77LAg63rUBcyinFCFVZ78m7afDIs+nLltCp8o15Fp7Dn6r+VrgKV0s91\nCq2z3124Ledx8b6hocjeiX1zV6/CeyejDOMAddjs4F+U5BnO9Cmm2J6PtwOE\nSdsODj8BDId8+UOk1LAqvcXDlNMrFr+Ioj4ZV3JEN1IJrz+wGy4WRANIL/8d\n3qbRXRvBRkYqqBRxFA9bWEmdE9aQSUOV43ccU1oQQY55j1jJhtmDaStjioYf\nR9+26JaMg0Q3ubIJQL/MWhetBV4mJt5AGZpsP/zvXYfiTT6Uj6lb8CJZrUeU\nstoIMPnSIAQhk7yjNtoRic9HHPYMslPSFjrNyHhQy8SVR51OZnVBUbvYJhdx\nNR0YbTfrjE3qgj7WsaKWjqliRwb6kXFqWwx7W3v/h+6rSa1phoEqHPOCI5qT\nxolWkvbwhinC1AIIqT89yT7JV69x0xyVwUb7IGe7Fc8QExgU6Je4827kOLLJ\nL9A/nCBPNIv04wDFcji+voZssuR2b/bCbw5SZkQcAJryJ8N54ruvzl+XqYFV\nR76C\r\n=ju5g\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.5": {"name": "@webassemblyjs/ast", "version": "1.7.5", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.5", "@webassemblyjs/helper-module-context": "1.7.5"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.5"}, "dist": {"shasum": "c703ae02a230ee1e47fae883949b1252bef331bb", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.5.tgz", "fileCount": 33, "integrity": "sha512-etszn0WD5aoHJpAB5BCHsChOasq1oUCJEbKpT0oMTOQrc9/aRoWWktq42OoiUSAy4JfcsRaTIdcZI9GXtN63Kg==", "signatures": [{"sig": "MEYCIQC4iWV1gP7UwBHiuyUAT/pqc4HAebENLZsXo+pBTwndEAIhAIsARXu7qL/PRihj4XaEC8GaOp7ZyYO9/HRfZvNfRYU9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZAiCRA9TVsSAnZWagAAGqwP/3FLadnOIIgtm4bVBvXM\nlS9vQAaRn3wr2n6ylMG2pQnqmuyThUxF59TDE0aEEV9zCUQDW9/461w2UjTz\nI1a53S27thunpbs9tPvNPsXWDKBlSjY3fadLInQtHGJ06oOvI+Q6x+ckljSm\nXzQSYM/yvEHgYbjpGAfh3tR34/ND919uZSujW8KyrGJ4u9KiKQHcaTuceR/i\ntIefAs+gmc0CEihHb3xOYoYZXX0ZTYbQsR/GGgV7LSi+n63kyF+JGJDDWyE/\neRARujGGzHazVmsTedVs/qNtEX/VJLJOhhT8mCQBQwevMoCudwkUKRmlni+B\nyZhSS6XQZIXWFwWWulleiXDkSe73S71OI+DGcGd5YBfz3E/UmsLyOAybNYrr\nG3dFGu0MK29xWbH/IQGomLM25LXyBZOo74d8mqhJ/w8hLe1aDCwzNPn6z2V2\nBRCdEYBdq65M8oe3Qw4sa4PSfg9m8x3/RUE3XqZeIM/cqET+JGtzvI14XVse\nZ5yJg72VbXKc//paR+JM9AYb+kqifkguHofK3apPVZEWyp8MWdygFw7DneWa\niQ3Pd2N9thNUcK7qachXNIHQQQ65FcFux58CGflbfogLcx9KQei1CJdmA5Jl\nCD6RRm9YvwT2YXi+MpDTrcEZBytS6mhTu3iokubPkTQQ7mrbqqpJXyXw9LG/\nt84I\r\n=nxSx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.6": {"name": "@webassemblyjs/ast", "version": "1.7.6", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6", "@webassemblyjs/helper-module-context": "1.7.6"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.6"}, "dist": {"shasum": "3ef8c45b3e5e943a153a05281317474fef63e21e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.6.tgz", "fileCount": 33, "integrity": "sha512-8nkZS48EVsMUU0v6F1LCIOw4RYWLm2plMtbhFTjNgeXmsTNLuU3xTRtnljt9BFQB+iPbLRobkNrCWftWnNC7wQ==", "signatures": [{"sig": "MEYCIQC0qM0gUO6Gt4RA4SQ3cvgKZoSV5IbeEFmOICFefwEQ1wIhAMT4sSRNUBntaTICfD/mPh+LuqkHGh4keHq+v/h4NclT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblnz/CRA9TVsSAnZWagAAh1MP/2naBosatxB01qusAYeD\nkzYCbdGrKn2yZjXc4JhD3gQ7sOceThRiV2ZYmS/Sd7A4FgMqa+LJLh0usrX5\nTEsvJUtxWUKFDiCKvt3Q5AL/bKpxqpErM9A6RRGxxSAnDRMan0JI18tlQZnf\nYVXuX0XnggXVKki/ebpZ51qlHdOUwYuA9CHzZCVJOC2nZ6ytgzqo+n6ayRtj\nX+GkIcLmGv6PMPhNdKYBNkvUspk7s52q4eG3aIyaQhCs73vYnV0W9LRe+X4y\nJnPKf4mI0TQppOHBcLaGBA5dart+CEWnhxouEeuE3/lcKqLG9FpNf2PauNkU\nf1KJFL0S763CYXQmJWQUtJzJpXIj16cd0oo55tUIqjlv5088JDhWGa8PkVJX\nIOgWsGtgYgz8zFFo5fJ9wj+c+7dXIzsa1YrwjtN1C96Lqlb+n5H4/q1ZKH+a\nvpaeiQ+rV5XJwnrZEILR09S7Pm12ukVLJtdw/yuCPiH7L2MZkzfna/zF0o1i\nght28y0nZtygx/qlQYNgsGoR8CF05pgWVfA8Stw7haYdBliklbvzESQ30/nO\nTZpInulGuu6l61KiarfVbbu+4f8fVzuM9nPONqEFlNugbFPiaN/6SxnKtjTT\nS23qEoxd+IyCBpjKXpRFJGLVl9Al+YJqAlNZyvPrn5FbKnMXVCkDqtteUely\nO6O9\r\n=Bx5k\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.7": {"name": "@webassemblyjs/ast", "version": "1.7.7", "dependencies": {"mamacro": "^0.0.3", "@webassemblyjs/wast-parser": "1.7.7", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/helper-module-context": "1.7.7"}, "devDependencies": {"dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.7"}, "dist": {"shasum": "d801b79ab0d609ca087278028f6b7dd4f14ef883", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.7.tgz", "fileCount": 33, "integrity": "sha512-lkBrLxQRMJNMuwWzFLbCVMe4/dGzDeX/pgqq17dznwe6Kq+cqOTn0DJjPxpbnwgWy8ku+5W31zAz+dBz+pzVeg==", "signatures": [{"sig": "MEYCIQDj+bDcPvL+MaM6ObohwDKPNSUbCrsB/i+T3pBlhvmUUgIhAPosc84jrHJO54ZU425lenvk376FQO7wrLKqgsXsOPb7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeENCRA9TVsSAnZWagAAowwP/jK0IEhbaMR/zDaNn1n1\nkwQHE0/LGUGf+B1+pV8ED89KdDc99yokoDABlTwzx+D+w/u2i8XEFNwJIsRF\nqwVMsrdJ+gIdOBAefJjGj1pAMzfInVJkxWtlAwlRQaX4Nq5hmUFHHqKpBE85\nurQceOEhs1ML4HoxP+y0vrNhfTGed7X06rr4/Ll1ID5yO+wDUzZ47/HJNxZI\nfsSs7K6QYvw3okTxd1/8GhKEsHXf8tPf0MvpnlUgB8G8Z1NDClFdev0c+2Q9\nXMWsVUL26se8gP8noSvaLfHnyyyUmt1Lk11pKrz69avtjplMq5W57Plb75oS\n7M7xyYblwmlG5SHUrg4sUXxbNyDeyCYWwYYUjmE36+m3YaBxnozCrU2sFDwK\ni4LIFAi1tjmRwpfQ2H4a2FuCwk7Wpk2Pibemcy3TWwelgeVUuDCH15c+4YMt\nYRpVdiTHXT2lCWbdpr7eV4xvxyplQjeY2uwZNCaA/FBK70eutu6OUf9MbQX8\nBr3DpuG3F6CwCzT30ccumh2r0n7Jh7GWxc6edVfL6HslQJ8gH3hMQKX4oWpq\nFiyQ/c48gkCdaKoV5/vyj0T2GAI3adgj1BUevbEcLZJQ2gvQ/BzIm3RuSE2T\nfdu/VJEGqEtiyMtSCBEewLw0rc/BwkibxM5qatvaa9hZ0hB3ybamUqaIU+vC\nUjHm\r\n=ySrQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.8": {"name": "@webassemblyjs/ast", "version": "1.7.8", "dependencies": {"@webassemblyjs/wast-parser": "1.7.8", "@webassemblyjs/helper-wasm-bytecode": "1.7.8", "@webassemblyjs/helper-module-context": "1.7.8"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.8"}, "dist": {"shasum": "f31f480debeef957f01b623f27eabc695fa4fe8f", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.8.tgz", "fileCount": 34, "integrity": "sha512-dOrtdtEyB8sInpl75yLPNksY4sRl0j/+t6aHyB/YA+ab9hV3Fo7FmG12FHzP+2MvWVAJtDb+6eXR5EZbZJ+uVg==", "signatures": [{"sig": "MEUCID3oc5MOw1g/UvO5UOVebypPreSvaDuvB85+qsA7syJ4AiEAlecvEmAm/IkQ7gRn/NaLCIApJ6f7LLET/R5+E3g1T2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/bCRA9TVsSAnZWagAAxEgP/RAOZPJq6pjHM+Bw9rcJ\nPXwGx1e4pgV7quFAhpdY9GLdPmySS2Zk87jpiLm+bEUwnwQgULfG9qvW6WWZ\nILFFV89jxogNyG/KSb5NycnCKh3DyM5jP+FRLBDqAfORx99wuHKjn51scUA2\nVnE4iCt/h9vlq4jshPxEwDEz1JF0hyZP28/KYCRowOxrBio8IGJGDtXk9uCe\nKO3QshqINU98Il2GwThGG9q/ivFu1Ju9F4FoeDDHikJa1+DQpxZtQZ3eDuSB\nrpKFogFyAzdWC/vhhKB04N4nKzwzvISSeFR3eZAEag7TVf5VW/xSKZGnhdNV\n90Z4kYrDQYl5ErMijz+J0X14PgptdkpC81UzFUW5NaRYtCw1T2rZjMR5FfYv\n+VN1gguwkpHdVfyUgjj9+65//TTTpgTM1kGDaNSDyyu72H+EXtmRKFoB4FNF\nFnKLeom85IDiVDspVeGYdEjm/OYZZ+9S+QOyAZRkGgcLH3RE397sBoeQz8ya\nmw6qQfb1CVD1oCXZ0IhwjJGo7G+lGNlNi6Msqor/lvDZ4srOr54jAi8dZDlP\n30fQj+GViAI1P10I34kk4c6V6RuJocWkxxyitBnbTrQi3u4eRlvnl8OHr7BQ\nGqZXPo7TBAC8ImSq53F4kBWTfgIH9wGNa3GkaGybC+RDUg13vGGAFNgW2zcp\n1VTt\r\n=xW9m\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.9": {"name": "@webassemblyjs/ast", "version": "1.7.9", "dependencies": {"@webassemblyjs/wast-parser": "1.7.9", "@webassemblyjs/helper-wasm-bytecode": "1.7.9", "@webassemblyjs/helper-module-context": "1.7.9"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.9"}, "dist": {"shasum": "72b2335207314f4b4ca1fa2a3ea1c910c0ccfcdb", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.9.tgz", "fileCount": 34, "integrity": "sha512-VGzr6Z7PChsoJ+9/RBDtkdE5SNG0CvDZ2eXxDkBBWXPnlKgJBSBrYlpEfK17a+dl+im7xCb6PVmd1xpFYW8x4g==", "signatures": [{"sig": "MEQCIADi8dGAK1U4JOrcb66+eezC083ZD/gHgyNf+OpywfpfAiBod7J/Wn7yj5rtQKttBo1RZO3qJmF3ckcTA1LHfwztaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgeCRA9TVsSAnZWagAADcIP/ibH6fbujHNbe4RTTDf6\n6V81gdgSsXRZ/2FJkx9cOMOiJj01OEFyb4v2/KbvUK8IBE2MVCiiPGpyBGjy\n3BnbNjn0yY/j0ScuR0JahO28eA2m4uBxMrsluaIqNg+KnDAvbNXGPBlRIU/y\nNAg8GWHTuR7XJKCagWCxxgz2PbY/+91xPT4KsGzZjjGX1+maPP7UiiLykdSb\n5GG1o0nmBYk8tD0ZWjY7fqUICIa6Qk6XDEZfs0uUN648Nh8MOK4AQOncJ2+g\n5amXhkmSev8yOz/l53bFJvGWILrxC80S/32I1pYVCCQtxkdWJojWlxFTqCta\ncnBt6Qu6/9xnX70GMt5uWqy5Mn4zjqntcVOruktfGp/letsUO76/Ejj444+u\nchuYvFM4VN5xN0Mf+w5Mi0H8uec8qbTS8B25cPzg9eMzPZKvOG2wQyuux5pC\novzyWjgq+z9z2xSpS6rHdhLOFH2ODYuQV3DM/0jjXFac2h3/u1fwUhd4zomB\nZFGKW+WiZxLnqnmaRvqF/Z0oQf+bjpsr4l3WvxChBzxE+7gwAvRkWnf7exAp\nUc8dMBUMg1qsbDiQEDqk1lldFTkdjCdqk3RFHpzGQcR2tDgy3odak6JeOuhz\n2HHGQGmYmtCqN1UpdMByFT//rQQl4jyMnwXv0NYkKrQEzBHSiFlrOWkNZnod\nALpV\r\n=eiPC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.10": {"name": "@webassemblyjs/ast", "version": "1.7.10", "dependencies": {"@webassemblyjs/wast-parser": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10", "@webassemblyjs/helper-module-context": "1.7.10"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.10"}, "dist": {"shasum": "0cfc61d61286240b72fc522cb755613699eea40a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.10.tgz", "fileCount": 34, "integrity": "sha512-wTUeaByYN2EA6qVqhbgavtGc7fLTOx0glG2IBsFlrFG51uXIGlYBTyIZMf4SPLo3v1bgV/7lBN3l7Z0R6Hswew==", "signatures": [{"sig": "MEYCIQCSzjcbXmLluBFDf2qYJFCbnix8ZjjyIh1jwCE+ScmVoQIhAN1H2NY/6XFR0AOGrVSm/osmqO0OwMVPcvMVsXi+DeYM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzutVCRA9TVsSAnZWagAANksP/3cBEapG26ynE9UMjUaB\n4gSoUXzKo3JBizniot7rBwSr6nrRAess5w9yhGFE23Lk+4PGDcdd+I+08GD7\nk2e6XCkelCFU9BnibkV9ZQfl62JhYrLfO6mtR7eRszYyHOTBnX+MFw4HjnRu\ngA+3xfm8ZjlWfRWP2dVYNiTovf5uoBiqBBZ5fyWp3KFStR/qlnSJ0s9Q2ZWZ\nmAw6OzXWNPI0Doryud6AAze8ojpMxmKLM10qIRSYcCMglJepXtDl+0CNFYuM\nw2M5qflY/nRFhEpyzmMkiDdAJAAX5chiDfPwjPVX4S0hc7CiDUVcsJUcT+9Y\nKL5EezL/1KRORgoxmzWpO/dEzg0Z79qFkT607ERmpM/dgi09bctopvltiyZk\nXXLKDpf8kQNM/AiKPfr1nd1m0mvzcjmb+fKqybb7Pki7+eSJshzc4HglOtwk\ngF/sc7OmptBF+BhBvGdEPTgEWGvrFGvgDZt0CFbK9UGomAnLt5Fg/lQf0PBV\nGqwg/BjKelAWQGV62BEMvVvqgnQ2XL1vlbwGQW9LKqaLjKBRgT8l7wO9sJRY\nqyCHMftYyR9JOcb04Gt6qD3gC3X6SGwANGwauR1cxaj9OfYcKHvb465pjSj7\nQPjlMU2rfWVPC1/p/v+ZgIE9bEdbiLS6d36HYaOzQjkz6zdIDEOcmlB8q0Ee\nK2ZW\r\n=AqS6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.11": {"name": "@webassemblyjs/ast", "version": "1.7.11", "dependencies": {"@webassemblyjs/wast-parser": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/helper-module-context": "1.7.11"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.7.11"}, "dist": {"shasum": "b988582cafbb2b095e8b556526f30c90d057cace", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.7.11.tgz", "fileCount": 35, "integrity": "sha512-ZEzy4vjvTzScC+SH8RBssQUawpaInUdMTYwYYLh54/s8TuT0gBLuyUnppKsVyZEi876VmmStKsUs28UxPgdvrA==", "signatures": [{"sig": "MEYCIQCMTNWgPqmtXSb4bHAe4PSxIhN6XgeRentLomFbIKDddAIhAMqG1k0FIO3s+z7iyfyHF9jg4i7bws6A/gYx8/wXfajc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxsCRA9TVsSAnZWagAA1h0QAKEeAz6YzuGSy2tXokYJ\nU1/cNgSEK9W/9R/kwexO3b3cuJ+Clgjz95v0kvR/XcIPBIsbXlslGtz5Qb/e\nJNhLTqXCqldwcHJgSp2wrlUoITyl/GvUj/NqE9QEEJM7qRMb30P9tIb3DpGO\nThxB9l8VnLdNtY4Fl3bTYmHMqsqAbanWcnONRoMvohm5ih6DAl2jHA1s6Nyw\ni+j9Cere/lriSy21iYka7tKYndZ11+ToNDB1sDvhSEyPq5tm4F4YARoBID35\nO+lJf0IkshXSZHx1RgN9pjM5AMpwXflDkvlpk4/6wh/R0SZKiH/bHB7ISuc8\nwrU176HPb8/2pCY9vghXtRby/AZGdRvXk5+kAxDOU5BeDYsADhCn/gInVdkt\n7Ohqj8+bSDMZOzMZ8m8S/qhtJXX8KJB0FV0/bGEws3yJZ3uXJmrNFp2cU6jq\n7TKpVnZpyf8VZhGOnXed8slRi55sTTtVdzMj4cQXy3CA0Enf95iHKJEgFmQI\nEGplIafWHec5c2LeVjOG+x9GnGCmlCJUFPhDfZhhL2UTG5XzByXA5EvBrCDw\n5oRj7aCJcG2PNHB3ktFgsF4FFy/lYbFFr+01S6Ukj7G2w8PNExus5U6/d1sz\nT47E6nN6RIKSe07zXTiFjCUE1ZBnZ3Ohc3+pKcEDKKTr3nf9Ht+Xc605a74E\nqYsR\r\n=z01f\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.0": {"name": "@webassemblyjs/ast", "version": "1.8.0", "dependencies": {"@webassemblyjs/wast-parser": "1.8.0", "@webassemblyjs/helper-wasm-bytecode": "1.8.0", "@webassemblyjs/helper-module-context": "1.8.0"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.8.0"}, "dist": {"shasum": "5f20a3ffe4254e6188dddc347191ea89d157138e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.0.tgz", "fileCount": 35, "integrity": "sha512-4TXeabdNuTg697sflERTFiFRMIP/2MFvSD3F3+py4UjT4Ym/NbQbbFHGgXqVIN1bA7FwFQQezveP4/5UW2xBMQ==", "signatures": [{"sig": "MEQCICfXEnYB4MOx25tkN/DVHZ13biLQxDnlPyEzj8YQLMLlAiBg8ExR20qU0gsCDLrx1kXKDlMXAQ3UZPAvC8Zp7qm1zQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2Y2CRA9TVsSAnZWagAAGz0QAJul8cWohvGr8uKPIUD7\nHyvD6ABOuFvqncdpmubNxftKdH2tB66h4AvT9Qn9SEBFuyy8y6w1hRLw5vST\nwOEyOv0bOHGuOxJ6nWwrYBz3PCMGK94VgLwiEWyro+VDnIHweJdBRP5yT+UA\nIj83BNdYF589gOKTxsgfDMnfeAuq2z2k7lfZrnn8wJwCbqiBid641AA2fSX1\nGvyQM0F7KhcA7FmI1gOFiyvABkhpWuRF2TPbRs40PZxPwKtBdJrNrJyieyUR\nxrpdFJ36TJYQxw4TBpDsjwT6NZsGamcsEGHEgdPbh6jb5hK2hGtupiojvbvR\nBTxwjluNJgOK/QjtkyDqi6kspY3l9oTTbzEyxHYywz4U1+ya9LbHaeK/VT15\nyeUa7eIAkRVYC9us2HWvt6xBArxGs85scNWqQTQwemIN88ePgkOx6pzB1H5b\noRUuhIzDL4QHmxwrHDkAENd62Gy5jRSHNK4Mvs4a8nScnqNfPc7wsg1G3SFs\ntcoOwCtJAjtAQgdlzwDwJkiKXWB5ElZWLetq/6rpE80ZN1y/xnaUH/GO/8Vx\n+FHvrnHJkswJ7k1Ul+aceMDsr6Fy4FX7x83uJgQweKi5Odu1xhc7bhy/6wLI\ntbRgREpJ5dJMt2sT6AvOMNnyc9NT2pnr8to0XLlsiZMeTcOQ3da/8tIbP8QP\nAGXC\r\n=SfVh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.1": {"name": "@webassemblyjs/ast", "version": "1.8.1", "dependencies": {"@webassemblyjs/wast-parser": "1.8.1", "@webassemblyjs/helper-wasm-bytecode": "1.8.1", "@webassemblyjs/helper-module-context": "1.8.1"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.8.1"}, "dist": {"shasum": "3e2e82f0ac7e112f6340aa78e16c8ca2f308686f", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.1.tgz", "fileCount": 35, "integrity": "sha512-gDrC14Ae2b4gP9vYdCzx6ytY4LuYoH3I0h0QzU9RPifGPgjXz8F3s5g9632P7Wf39vQQg6XQ0Bfv29rc5RoTmw==", "signatures": [{"sig": "MEUCIES/KJ8mLhnwcScRFU0biKsH5A1s8HaqSn4aMgV1rLZXAiEAv4aIEs0luCAZbE+jXk9p7+D/47AhdBh/ExWkzVBkE10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgOCRA9TVsSAnZWagAAuqUP/RiIA0PYMPgzsGOxIrqS\n/+k+Kar1xg8vEI41OAXcvVc9wpjqVvJh7EBYhJVYtaFh5A9Uj6V0n2yOM+M0\nFgYC3CvLONHSuTYCCfx2NJoYsAPE+dE3ohzB2Hj/2WwjQE4l1jgSdU1t01yY\nKQhRvfbyMTDtaqfOXQBfDvZnE2tAasxjpL2NC9HeE1F2w6cIRi7oSeimN9Uz\ntaZlG+GsNmuBoI/NujNVYCxGzL3nWzlvPK27PVNFta9R5kkE7i5F4fRtdIud\nQ7DhIJztFkXlqGmn7XwLVIcjkJOMiAltQV8adw6yRVVK54uKPAgtU+5tohOH\n5oJG6WBYgVqRo2GpTkzCLaB051p+qLOTIim2l2M6PG25tLpQHNCVGnhq8kGp\nRHNrUmazo2sGAw9coSHbTTJLFzeGgNypdgjj/OQdY+93BKCMSnTWnq/ljT6a\n9794MbsnQMXsT++pGHmR3Tc8CVuRZPxKWtNEp26YM8dvUnYgsnlN/AUc8dVa\npLLwa1Ro6cb/B8RhMw14GPK/Nv2EUJhoP7ujPOR+9R8UTNC+PitZLywyIZqT\nbTKS/qsQdmHhgv2efc17fPmwtCbLAxnaj2P95m6P91ysj/kT6plp2ueJO3zO\nn3sqBVjoOJJPK61VPwby0BMdD+CCPjepQXSPcdz/Kdwdc87EIu0qxIKX+eC7\nFHL7\r\n=mZ3Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.2": {"name": "@webassemblyjs/ast", "version": "1.8.2", "dependencies": {"@webassemblyjs/wast-parser": "1.8.2", "@webassemblyjs/helper-wasm-bytecode": "1.8.2", "@webassemblyjs/helper-module-context": "1.8.2"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.8.2"}, "dist": {"shasum": "89297cd602cf8148e08d5fe1efbbba0cdb913ca7", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.2.tgz", "fileCount": 35, "integrity": "sha512-5LLqqVsXZAhAJN0S7fTi11jwMJOjfR8290V0V7BWKgmZ36VVE6ZGuH4BN3eLt7LvNMIgyuYwyrPwiz6f3SGlBQ==", "signatures": [{"sig": "MEUCIQC8K7MHb6iR5d9Kf7csEy161XEr5eeOeLq5Ri9pJIj1bAIgFyyQtSvyJ4ywN8Fef90ODGt1bAhDb369lVK5t9DUosg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWqCRA9TVsSAnZWagAAji4P/0+qket5zqQtAwiPRWSq\n4YAEsEAUp22+abn2wGee/7JS01ExSaAgrjwxwKLLBxznFMoeRTroQYgu/IcG\nE7VDvVracvt4eKLgvxuhKldsmHJZPjw/NcLHkUP0VrBGOuZd46t92AI+02C4\n6EgxoNi4B1zbnGlmccrPiyqGkqFuXzPvYTQqTtpX95WyA6ty7U8UP7eYkfvQ\nMGzoxvwK7f7xoNkX0GkLtYSoRb4RclxMUqbRhEm8prS0SBxN39eiqtNg084V\nU/lQP2QqlLJ0AE3DLJRQa+QNw/r2+F9rQHOL8L9k3ChnnGShrha6SUVr2GHo\nz20TDpgsHy5NOTh5EmX+yuSkyEe84ztMbBgM3PLz93MX4KB6Ms0j1xczu54d\n0A5K24n0biOihtKibflecibgwKRhNbISbq/mIo68XgN8OQx6rY2DYH/p6eVT\ncGmMEAtDkTzjYNGxOjPEds3Zsoim9sVZYwY7EEfyqjk0accg8w8Sd/9fRWwS\nNqdrR/gjFDZ/r2abcRUvUSlbxNFsRR87w3RUupkbcOZMgfcl76suBZpHLUBB\nuS6CPazcYpmi7Ff2yA1d4wSQIf1/uflS5kk0G9dti8Pv1dPfzbhivYsYs/Vi\n+pH78L7ShXRppSV3d/JnAUlHBq+D3J+I5APj2DU171X1VSFqVDSEdwYhuvHj\nfsLd\r\n=QR8O\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.3": {"name": "@webassemblyjs/ast", "version": "1.8.3", "dependencies": {"@webassemblyjs/wast-parser": "1.8.3", "@webassemblyjs/helper-wasm-bytecode": "1.8.3", "@webassemblyjs/helper-module-context": "1.8.3"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.8.3"}, "dist": {"shasum": "63a741bd715a6b6783f2ea5c6ab707516aa215eb", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.3.tgz", "fileCount": 35, "integrity": "sha512-xy3m06+Iu4D32+6soz6zLnwznigXJRuFNTovBX2M4GqVqLb0dnyWLbPnpcXvUSdEN+9DVyDeaq2jyH1eIL2LZQ==", "signatures": [{"sig": "MEYCIQDWcXk1dosk38dS/HKq6pU1b2J31WuXGod5qFxXH3StOAIhAOl4uZlcHmJRqz8k7it3YyC9zMyS3Qan2fp8qXgw8w4g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam7ECRA9TVsSAnZWagAAkhIP/3v8eJq/nF4KqcMALewj\n56SYN3IASx3Ho3WVEgi4fT0yV0FlNXI3gzuC7QXJOg24SfKOoSbez57I9ptS\n1cKVzXsYyog3OLf7QTWSm3SLJDa9zecfM1DBYum+j+UDiE3n3TROCqgfTJdJ\nvTlIchnkhGYJWVj+0gnNQYSpfh3bDIVwRnt18rWVJe+R9AiT3N3FHAP3X19D\n5zzEyKn7B2HHkfhn6vU5rPlkTDHtvj18KY3PCSDi6O7clsXn6hPQ6KGflLW3\nVOhRZHYzfhR7SmrgQV62hb8HD5lFBrgJXgcSRlwdlv0vMDGD0iAThpJ+qNGa\n7UU6jbFz0eUJicB9bG3fZY3UxIGDZzXw+h8JQ50a5U4IMpDmSszRynohH8A5\nWnxH5Ya30lo98Kjxry2lQK7jVdiWu6Mw6bjcmQeoWhJYTISfrx4BYHeq5Ide\nOsDinYYlfOWdf1MadCN1gBHRrurUHRCyVtZtI0T96ztCIMqXni1XGFX1rsYV\nl0s7BK6XTmMZmYZ0Bp4xchMZkJ9NdycdIV69ZrO7eutEZjxETR4gCXSMbtVq\nim+sAWHggC9yGN8WzdNXeJAYQ/0tF0JQnfuKNvX6U7c6baOccFzJIE5tAuNI\nQ8cWmMTAuyAvARK54hAnK6lUnEgi7OAj34zB1xc4SC8cA/Ori16tL5nBi5+u\nqbk7\r\n=QPXK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.4": {"name": "@webassemblyjs/ast", "version": "1.8.4", "dependencies": {"@webassemblyjs/wast-parser": "1.8.4", "@webassemblyjs/helper-wasm-bytecode": "1.8.4", "@webassemblyjs/helper-module-context": "1.8.4"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.8.4"}, "dist": {"shasum": "aa8e32f493228dfe1803d37fd4c631c221ed5d81", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.4.tgz", "fileCount": 35, "integrity": "sha512-<PERSON><PERSON>JJsFkxI6hVK03p8guYwa12EEkQOMIoQTUSSjTYj6zD9leLgDY8HyPJkHnU5UlwxQvlMSoj30Dft8mu4rbqkg==", "signatures": [{"sig": "MEYCIQCwP4EsNiaYOuOSOFnD3qVPAyHBm7VY4MQTZSYdSKY7jwIhAOK/uFxCl0dfgpdzJEbN8SWBTkPdzCQMrrEBccY2FNRp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyKCRA9TVsSAnZWagAA1BgP/jJudcea5pjZrfj9ALm0\nTEuea9+UEeqXx5pr59pmEq5bSHk2osT+1LJLz4AVRDhMQnnNQ6SFLezgyiW1\nvSKG1/alrAwc46CpPbKSiCRRmhiWN0HtWXTG1TQE3nQ4kInhRiCKOLnPCkOw\nV/jpLeWmWZcJ8YfM6FWXqC8vvMpSUmW0+I+ffnklh8roQm3V8F1uG2tAHkro\nR6mPcZJiV5VrDFB0YKUvmiWCh10nf6g3w3QKjHC+mEhlH8jlGZNT7veg0ZHc\nmZe9Wvo+v3nrbOwyaMu5JI6YLJNTHwstN1QkTSNr37LRiYojOxdEq0wF945G\nz9p+ypd7UAy6rpsIntG5Trq0EBX7QXTZjiR//d6reOPRJ1Hn9JdOkTlrQ7TK\nzKjypPuOugqiIx0WmK7fRJSFElP/5wei8nzfaBtSAvwic2+j+1XZ17Uq/Etg\nYGCG0zhMGdrzWS/pUJcBslSNLsCW22OiBVK5xl44DDgz63g+zV/kOsrLL+We\nLvXtLGvGvsY0AENWuu0jMBLOcuzS6vgIY7YJisEtWZxEoyK+yLbJbUseyjdy\n0znh18Ln0Rk4PbnjZ5tzav73Xd1wP86Z+c2NbkXjJzjg63vm718wfjIo43vv\nHCu7eK7ssv6ZjfJBQzUC3+8l1MLiXNqHDMsMDu3oGwrAkp7HzbZ0HSXrDvXm\navyC\r\n=9Lmi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.5": {"name": "@webassemblyjs/ast", "version": "1.8.5", "dependencies": {"@webassemblyjs/wast-parser": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5", "@webassemblyjs/helper-module-context": "1.8.5"}, "devDependencies": {"mamacro": "^0.0.3", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.8.5"}, "dist": {"shasum": "51b1c5fe6576a34953bf4b253df9f0d490d9e359", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.8.5.tgz", "fileCount": 35, "integrity": "sha512-aJMfngIZ65+t71C3y2nBBg5FFG0Okt9m0XEgWZ7Ywgn1oMAT8cNwx00Uv1cQyHtidq0Xn94R4TAywO+LCQ+ZAQ==", "signatures": [{"sig": "MEUCIQDv8zSmFVNfQOikVkVIlsy2Plo+TCwhmfcipEodOdvuwgIgdCIhwZVv3Hj7W/4a4FAXLGvxJIUMBql3IK6q9y5UioQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnU7CRA9TVsSAnZWagAAabMQAJGVoFbBdFXKpwKCpdu3\nPFRFSl3nmQQSvqtSOvRgibQghTmlluKc+XbYfp0VQjJHQjnzBBV1B8O2QT8X\nsxXmW/FR5CTGY9juGlQCs5fyNpyiX80ws9nXFQeluovHy9FyN1CcnqPabSbC\nLubEsl8mn0YIFzc/t2Uu93JWwB834xnxxoJ57fWnqNgWjETa9uyBI+D9r/U7\nkK4BV+p5obj2tOG4XZTOPWJu+Jpm05Q1+eG6ls2snZte15wmX1Tdt1beKvSY\n+TJ6V95GobKBAcD2zFQIAg4wplhfJUEaJxp/rF23efQob/5ELQ7UDc3nueeE\nyjuRnahuWHQrFR7ETuypeVtuo7xii1auZMm4PsqK8pX7CY8Kb/m6GTQzbgbK\ns+U+WucvmmjAT6q9zLEwhDLaKEG+5eunOfuTcpz63hQpGRuYdOk7IrPHgRK1\nPBLrPL4LwOE/n/I67Y6af6hRlNv9SWUdZVKd/E6qNx0fbBRjcAnXsHVJzU0k\n5q47omOzYeUVMW91Tly0p1mOeKhTK76p4MNZmvXCgQN6g64d2M4XRDZM75JC\nwC26IyHvDKjff52efutHxJrZTEZvlBHfXvKEof0XHDuLWdOoaLyyi6nt4+HS\nXGKwjswwY9+oevfAbvnWxy3X0h/CDMlu3wSZVvys5l5Ll9JueMRWznXibG6R\nE7JY\r\n=Zsov\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.0": {"name": "@webassemblyjs/ast", "version": "1.9.0", "dependencies": {"@webassemblyjs/wast-parser": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.9.0"}, "dist": {"shasum": "bd850604b4042459a5a41cd7d338cbed695ed964", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.9.0.tgz", "fileCount": 34, "integrity": "sha512-C6wW5L+b7ogSDVqymbkkvuW9kruN//YisMED04xzeBBqjHa2FYnmvOlS6Xj68xWQRgWvI9cIglsjFowH/RJyEA==", "signatures": [{"sig": "MEYCIQDAGZfDED+qe3puDFoH1VCbvSO6SyhanenzqROcdDc4QQIhAP/ZQHVIcloqSxApq//ZngsOGPMI65DGFLPuc2Im7BKO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgeqCRA9TVsSAnZWagAAeUsP/R+J40p8gyTADW7xbtGG\nPOer3pI3jYwWOVnYlGsFxuC2RauyMLneB2GfbKz/yuvifrqA4WojU2YbUKIt\nYkhEyH7lZ+5yqgZ67SDx+NZqUQE1A7ZTu2dlpKfXtMkh/WB5MPK0PaXKOxgb\nqTRalCa0FHfRJzwNquKoZqmFQad9xzNGXmMtUp+aC2vrfbSoODcR70WNioX9\n34zqhzlWwSBfdrHoSjLlWsfGiQxQuZJBsv7BRqMKEWEBjP5AZ5GdFDf+UT1x\n9J6F62AZk0l7Pw4NUg47HZ9CzolBdfa5iC0bJwSjAKqNiNEDde8FE6hLxaO3\nYPdAtVaq5H4B0ZmjFJoccaZd+7D6ZtWE1RlriZ1NQdTvOEXi07qhSz0tSjmY\nyj1yRn075VKCIBDDsUCuO6lkPVmpro474SoGliEykrt/GpQoVsW14dHV3htz\nMRU6LHxu+IAfLeOStVerLy4M2ai5R3n6RW2O6AGc3/Wui2/g2lJwEOe5O+YH\njU6/8ZSenhNsKrVJh8UfOabgskvEvxiQfOfY4+ZBvBgTZdDMdQl98Gq38Bz6\nSqEV+/HlQl9k38jIfVNBEnxs0F1ehbBDLY5XgCaNlRv/CZtMM8BxdNAFtDhQ\ns+XnbI6HIiQZ7CwKCLsp2FxPMJ6izMX8bgGYBfM+A+j8/wSmtj6+4kf3wiO1\nbr6h\r\n=rWMx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.1": {"name": "@webassemblyjs/ast", "version": "1.9.1", "dependencies": {"@webassemblyjs/wast-parser": "1.9.1", "@webassemblyjs/helper-wasm-bytecode": "1.9.1", "@webassemblyjs/helper-module-context": "1.9.1"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.9.1"}, "dist": {"shasum": "76c6937716d68bf1484c15139f5ed30b9abc8bb4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.9.1.tgz", "fileCount": 34, "integrity": "sha512-uMu1nCWn2Wxyy126LlGqRVlhdTOsO/bsBRI4dNq3+6SiSuRKRQX6ejjKgh82LoGAPSq72lDUiQ4FWVaf0PecYw==", "signatures": [{"sig": "MEQCIFV2QKQFKJi4qnOpbyEUOWWFlrGbC0isFivBhL/CZ+ZNAiBtK0kcXo5+UAxy6c/z83DhO6kve/DBc9Z7yg9uCrbB9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNoqCRA9TVsSAnZWagAAPIIP/iFnjNBMt8BYiR1uinJ5\nH1MZiFZGj3ZMr22kR1dOoiaJiaY1NKVNTOt0S5Jf2NMPre7XlU60nErt7mTR\nfn4QtByI2QkkOHaSWDeshtuJucJX8IkwZOlMR5BDw80iHotXYDtSf8HElWDX\nmGOtUt8kt2GuSBjeZpGWimSJw6rQ569tX/SCabK6ax0dCpP8IQR1R+/rg6Us\nwh4GbEPRt8jDoh6cANBbYzs/DBMI4rRyK48KtJ9e8my6eADB+hdIBKQOC2ZJ\nrjkfoawVu+JMUv5oVe69Uokb5AzlQ5IhdR2VK/EaWoWrwYC9YVS224X4GCdS\naer4wsOJS03rcaJZ1uCQ01rDKGWhIZgJoEIOpK8L9VORxafbIBX/pubuistx\nuuPw94abCUs+tAnaAdf1RU5Y/4ja5OfcSbb1vJjR44N3YxKLJP61sAQ9xFrc\nO2QtTzOPj7MF5A+jypvw8exbSCO+7QzyR2vP1uxc5/4K55S1NnrN2TKr7umb\ncB76GYNOgq5S1oNEMcaUkpkZ1YNNuX1jZ3TF5PSwFwvNPzM0Brjc99bS0Y5R\nToBD7rortqhs2YbM7BQtyDKtrlyZXs1j+mEqSnaEF7Z375+vCGdjv3LSzA+N\n6jmGnGx2jSMiG0xC+s3fTMmSh/ffG6pdmjMRmbuHtABvAyqgR+DbwlmNkOSc\nCSPC\r\n=ZyvJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.0": {"name": "@webassemblyjs/ast", "version": "1.10.0", "dependencies": {"@webassemblyjs/helper-numbers": "1.10.0", "@webassemblyjs/helper-wasm-bytecode": "1.10.0"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.10.0"}, "dist": {"shasum": "0acdb71009079b15c2d77a53e555474398b0668a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.10.0.tgz", "fileCount": 36, "integrity": "sha512-P676TKXikzAyHJMZXOfTmwH2FkO8jUDbGeVH9Yi3oPjaVRVoa9NI4/NG+K/8LQXE8ix9rLyvAe1qpRsVak36Zg==", "signatures": [{"sig": "MEQCIEbKR4JApIeXfPFxdJVIcIciy0aHE0pJ2S8DgMbKv/3uAiBRpm1OVrbb5PROzYZqhDbUuOyfYIj25cClZ9OEIopOOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zRPCRA9TVsSAnZWagAAVEcP/2E1JOhr7/E6uYgNlBS1\nLjSEREES5kNTiIBhtdYehwk3l7Y722LYcgraS82cSLQCFXv+5HYyRZ6lUw06\niR9p5WxnI4/XsHH2FHxE9Ce92hO/c2CLqwuoVidWlRMPW4Tj/BM02h5kASpQ\n7zu+7+4i6UpfEV8rqAckiu1XhG6Zx1Axpn3ZCGPYuRdRhMdTYgCquNGGVcEc\nVdyUoqpQl+wpPBDqIBuyz+fNh8qxHjof7RBvdVhlzRirUNom5FChVnW8BnjO\nkyo3HN0SYpYzfqXdCL0Yqqcwh+zA3nN1SJOhM2YgQvMn3TJ2cLSgOZgY0wv1\nh1eMWzE2qHvwKuoTPqU/ZTPMiFhU8LiwbKQgx6F9RYJ1rRsNNMF//jDvBJY0\n5AGOMx2/FPmFMdlWk560TUX+bBL/j8k5stJV/ajQ483yvS+6UzlNF93DEkkI\ndpsgdqUiZHTAhkkPFb80PXCLOa43Je/Jgvu3SlYuSE5bGro3s0K22aspR2tv\n05z4HomV8Ij90HfkQ3EzEkDJKaAMXHVAk5Xv+7an8VZnKSPWwkjn5RlzuATx\nfJMPTh/DWb2t/pT5DOony5IW2tmBXSNvDR/bntgGjF+qPOab4d45sjCRNHzM\nUv8UYvxj1cC5vEG+c44Uv9VQw8V7HQ72ncIN5yPhyKkjJrpFhz1ziqpEQVey\nCNh1\r\n=Tm41\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.1": {"name": "@webassemblyjs/ast", "version": "1.10.1", "dependencies": {"@webassemblyjs/helper-numbers": "1.10.1", "@webassemblyjs/helper-wasm-bytecode": "1.10.1"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.10.1"}, "dist": {"shasum": "0749914a076f41348c29344794168925e8b75b0a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.10.1.tgz", "fileCount": 36, "integrity": "sha512-YtKe2YczbBMlKrV9Ic8NherT3Dtjf0mlrlW6iFBushuyH0oXI8qWzWtGriznJNLIG9NR4B7goYj+ZgjXEwOHCA==", "signatures": [{"sig": "MEUCIAb/ToAvxz7Qus1RtutGX7iQNfAfusn/0KRlrPb+4H0EAiEAk9cbkPCYxIM9iLkJenVAeJy4jE8FLbHehnIQHvH77Ow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqiCRA9TVsSAnZWagAAdRUP+wdz5uRuNCWkO/5llY61\n1xNE5bUVAZpf84EY2FDN1PZzHiwAHcF2P0Z5Tdkpnoc7Ns1iJ8jHtbV4usCY\noDEAjyT5hW68bCXsmKpL2IE37xeVZsLaQMGGrxo+24WHamxVvsASA/4gG26F\nmr7uRxHtgBXyveS3fHme3uY5ryH8qI0W32puDEyFWxDcMea6znxKF04Mvb6C\nhlbq0LQtQ7PIBvX5DN4HW0E+bMrYHSExehbLsrKqu2qYNCD/9Z7mRGaPBI5i\nJBYF9Z278iICha0st0uqU5x3TIFpSmRYW0+NtzFf0R0MWGVnJYaVTDPyua0x\nf2DPpqJhsIDjpHbfOCInVkIg6lBL6u8YLjCuypelELBx8ZCsg0zMtFijCCGo\nOI9R9eH9HbwDbxTwit+lTAs1cThY7BZBVY63ST1oZGP2srGRYA2tqN6xiYVw\nXovJfrnFyQN9ra6/SSMENejl0YaIJ1LbyIBtlIS5gO2laP+0yK8vo7K98gBa\nh+IHnlHkRhtaqaH2KURAAKKTe2jRO/5nLUHtcBibD5bI1Nw9R6eKvh/o3E9A\n63cCSm0vAKVLEfQ2Ix6dFBa2avUQtx4TaLW9zLJUG7c0hbQFpa5tUpb4YBQp\nYd7l32jiLiURDmEsAKdE+AhKES/WUc6FxePra+bSKyiovC2U4LAvqHzL2pwi\nkjKX\r\n=mtZ2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.0": {"name": "@webassemblyjs/ast", "version": "1.11.0", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.0", "@webassemblyjs/helper-wasm-bytecode": "1.11.0"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.11.0"}, "dist": {"shasum": "a5aa679efdc9e51707a4207139da57920555961f", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.0.tgz", "fileCount": 36, "integrity": "sha512-kX2W49LWsbthrmIRMbQZuQDhGtjyqXfEmmHyEi4XWnSZtPmxY0+3anPIzsnRb45VH/J55zlOfWvZuY47aJZTJg==", "signatures": [{"sig": "MEUCIQCbKHXrhOSe3uSYfUcDIRBsKn1tziJbMJ7yz4//UAdzaQIgVJXiwUV51iAzH7a+HCAW0wo2K8B5L0qmkQfQHBgNKik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907eCRA9TVsSAnZWagAAndIQAJJC5AASUmgQ4tTGawrT\nqXWqyXBmP1EaX6rmKnIN9aHtLTUz+B2LSFi6+P5BfGX3Xm7RwfKDG9xnCbVc\nDyOaDu4irhreaw0dCHsPkYychNyM7GDsAJbQx4ZXGxbRasXGLrD+tLBunHH4\nhM9OqVTabpLnTLpzqzLoX5ZYJJrIN8zxOWltQoNNhdqqq/GC8Mv2993OIxzV\npRS7qT2aGXac6MvVf/ueOZ7YRAKRncvjsS5ZpNJLjQOh4m/H0AP+R6xoDMAL\nuDjUTtlC2LQGq9a42J2vGOA4eqM+dSCVE+9hV/EnfyihoFLEWmR9v5wdCODA\neJ8hK3IdRbTwdI18Wb7r/9LyvYd8rlymOiWIoLpABWVXRPudCTmhTvp78PUm\nwmz9lvZIZkRCV/JVSJ9jnOUxVjxb9nk1HZyx+pSvOSr2JkpvVXXRQtvLlRwE\nHvCuvk0NLmnnmj0OvOoeBQvhQL+H/3SBrhWpdZW+XgeUsxBydJkNDSOPqBKM\naYRHrCVKdS5xr1G+GL9KCLb7WdbDKHRippSJQKUsBjYuxADz0CO9ZABuQU+Q\nXZD6odH0CemIyJ2xlHIs7JSmGRMoTLSbIvTiy9vn/Bf0wRwhr76U5f6R5Lol\nnBsDqVbjEDPTaeKO3j9I/J6Ft7Io4/tXT7PWuWiRdGuPMa0ZlhsVVexJUIXF\ns0qB\r\n=Ke1V\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.1": {"name": "@webassemblyjs/ast", "version": "1.11.1", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.11.1"}, "dist": {"shasum": "2bfd767eae1a6996f432ff7e8d7fc75679c0b6a7", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.1.tgz", "fileCount": 36, "integrity": "sha512-ukBh14qFLjxTQNTXocdyksN5QdM28S1CxHt2rdskFyL+xFV7VremuBLVbmCePj+URalXBENx/9Lm7lnhihtCSw==", "signatures": [{"sig": "MEYCIQDLz6mY8E00UJUOawb3VNVP/uZTrbUKR6VV6xjhCuK9fAIhANMGoUPr9LF20+H342vm+oSpfl44bD5r/LNJKCH93D5K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sE3CRA9TVsSAnZWagAAbtEP/jpR3gQEe/RZSus3ARVJ\nu+DO7VAcGdocUbHQopaQJ9U86K/eT2wHs4tHLSOdkJASB8uiDHIm6Jklk1Xv\nEnyazBwzf208Hm4QaJPqemj6ZuSrYZGXUJSbHXkLuZQFc6zUvog1vZcj/flu\na9VDkSH3Nugw/SqadufIYtdgreiBrG9BguTtu1oNAs3dICQGjLU80O0VJJhf\nL04Py8fw+eI7CalJoRc02bDE/zGFcX+ehZITAAd9hxsDQAhIBVVBQuKrVeJg\nvwk1rwWfnQ9YHL0JJNxKk3lWRP6njx/eZeTQZxTEdvMttZHIJVljuKEYooFa\nx4gYEkyIq9l0e/vkIQF9QrE2i8qdsTK0e+xrdZdgfSHgHBQuKNbI2snBCam2\n11b7sWmgU/8KX7guyAV+J3/0mb03pbPOE8v7E6ArcEo8+YgnWSvW3bEM2IQj\n2BU1usfDL9Jxo+NrYJh/9qhmZR1IfxgUlnFlORQvyeAQI8IPCfusvfMOvlIQ\ngN5E85RIc8h4f33Nzkc7FlzRNA9gOUf3iAErIHvu0YJpxaVHBZ0ydXG7LRTr\nNFNudtsbYmbAIvZ1Al04T2CADXWvyyoFZJVslcCEEsvqfQlE5o/jT/Y9DWRt\na/p0iVZD2gLpYBAF00R6MOTS0KN7g4xjGjfcs1ZRuKjp6RIe3A8Q4A+4yFmu\nWmUN\r\n=sA3r\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.3": {"name": "@webassemblyjs/ast", "version": "1.11.3", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.3", "@webassemblyjs/helper-wasm-bytecode": "1.11.3"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.11.3"}, "dist": {"shasum": "d66a7499d7d90d617e97f0183a5ddff229693019", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.3.tgz", "fileCount": 36, "integrity": "sha512-bc89LwRDgLLz1/73y0T/4+CmvEECPZdXSAkuNUyeRXulYfBfHmmBJcAfMyqnTFvFEaC3mPJdwgaPhELGPPIsAg==", "signatures": [{"sig": "MEUCIQCkJX8rjOTFyU8kKPNDnj2Dh6KaGBLjWB9A/4HIRudzIQIgJXROWUw0z6AzmkT5Ue3DMoNTrkt95N1IV6faYe35rKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV3A//aLvE3t4Co+18UfjwbR7kRBw2tY9SzVWByqEsCl/BwgDDcn0v\r\nt1QxxK0vKCGk1BQVA3+j8R/cXEwYpDPVb7yxm5rFqr0oGzPFJNP1PTdkoZSD\r\njUEBNA21/HJIEcsjAGHN6VvEevUblJl5pBFXY8B+udFj71u+7spLYliXqHBZ\r\nCVaPqPt39srTXwuzFnfMmvwU2IzXJ8kd084QV99kJeRGdt5DcDf6lG6obhGF\r\nfBeQX5gu1nlh6oRX2th5fRCIQCvwY3zkZEuiyKtsvt8po6FIa0aiNEDZFrTk\r\nCrWURBBvmqrULtCnMptZmKn7V95+/OephcBjYdPnxKFetp1ix4Gj+ukClCyp\r\nnqYxQZnHg596bWlyjjyDxoFryKjdmpLP9PbZBQRfFeCplociN5qKuBPIATNz\r\nbAZxzgdAS+PcB+o6PukOK6KMKQaNAGVTvE9/SaANxCuz1HakQxJT2BdErhSK\r\nX4rmH+RzaLbVN0WZaKX81wIeOaKXx37fs4d6aZSGkfMKFYyIuHLdC//oLSLn\r\nlWs5pZ74wU7amVmIBYu09FRD2MIaA0u6pDd4SceJEMZVjwAjie6vCWIy+P8Y\r\nFgmR1yOvoTO3P1MaZaaoO4Q9NEBU4rftIjYfOIchcxrU5IDoFfD9lUh9tv/w\r\ntRyN9svhiNjw5NinqbwVTvyMALOa42SVN6g=\r\n=rY5v\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.5": {"name": "@webassemblyjs/ast", "version": "1.11.5", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.11.5"}, "dist": {"shasum": "6e818036b94548c1fb53b754b5cae3c9b208281c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.5.tgz", "fileCount": 20, "integrity": "sha512-LHY/GSAZZRpsNQH+/oHqhRQ5FT7eoULcBqgfyTB5nQHogFnK3/7QoN7dLnwSE/JkUAF0SrRuclT7ODqMFtWxxQ==", "signatures": [{"sig": "MEUCIQCZcmWuLKeC3plvvyHXb6O0CVi2tW0PfBJPPU0LtIlreAIgSbjYUhaSlcvZJAuFZFognvkMMoSDVS9SAy0oB1pQ5Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO5/FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp44Q/+MMwUrajestwj1hvGZVSjaWkecxzq48jr5L+cH1XuZQS5yZRS\r\nbdT83FL9rvQvO3H2/3iHSLErUchsXiqPrSM4HVv4qapvsLqty8MGuiqC9rY9\r\nEQTwXTMSqx8epdLWtGwGzMs07/XWDfAenAeVD2h7hu76QPRIr1kR19OISfgU\r\nS4EwjTIG1zH8e8sVb2L5FhlPXaR3OH/RjhfnN/COD/EH8AhBYsxttWI0GfVt\r\ng1w1NTIF3MFIUBqBPV2PUs/FqgeCcAjfc8Rc0nz2hB4WN7Bcq7+swdUH9SLj\r\nHEnu2AqkScFe4GyiW1wmu4NZSMYzbiV+YZND92gtQ+QBKmuKcQN2jXbiE0E8\r\nxGL3HLhHnJvVz3OL27HplN296IvvoJoRhlLyPrYHvCMqyMgLB3Qc8/OJfu8l\r\n71E93YyvSe/sxOEbP9s6fTmZQOkZd2vJNCBK/8cx+TBQRDAbISOsN2AdrjTJ\r\nsk+tBbVyXGAJqKgGu7xyhAu256GaFA8mjIL8cDGUHYGBTbJFYwN1X2mm7RaP\r\n12IFOpIcGHRjHtpeZ3Als+67stu4o904VKJPlo50N6ln1LGxccMLlZ2SADHx\r\n45VZoS6KwV68Mc7xOb25liZ5FZFd94asKedZaSRtKthoftbm5XuFb3+mHBaO\r\na0FwPKRHrcv9etT54bz3f4VU2He4gGmqvoE=\r\n=S8+/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.6": {"name": "@webassemblyjs/ast", "version": "1.11.6", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.11.6"}, "dist": {"shasum": "db046555d3c413f8966ca50a95176a0e2c642e24", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.11.6.tgz", "fileCount": 20, "integrity": "sha512-IN1xI7PwOvLPgjcf180gC1bqn3q/QaOCwYUahIOhbYUu8KA/3tw2RT/T0Gidi1l7Hhj5D/INhJxiICObqpMu4Q==", "signatures": [{"sig": "MEUCIEqx+Ob4wvikzEL1pP+3gz3Uv2M04b4JfOQukeYebI02AiEAk4BbrcS3cqOEL899H1glsgw8dNpiRcdDTL97x7wru8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116970}}, "1.12.1": {"name": "@webassemblyjs/ast", "version": "1.12.1", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.12.1"}, "dist": {"shasum": "bb16a0e8b1914f979f45864c23819cc3e3f0d4bb", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.12.1.tgz", "fileCount": 36, "integrity": "sha512-EKfMUOPRRUTy5UII4qJDGPpqfwjOmZ5jeGFwid9mnoqIFK+e0vqoi1qH56JpmZSzEL53jKnNzScdmftJyG5xWg==", "signatures": [{"sig": "MEUCIGazs0idrUld3k7jU1i8wIIBrIA8zJPyljnoYZn3P2GfAiEAt4Lfw+bc0ZC3kdv490LvXQhcRDf3QtZr7XXupr+oRe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207436}}, "1.13.1": {"name": "@webassemblyjs/ast", "version": "1.13.1", "dependencies": {"@webassemblyjs/helper-numbers": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.12.1"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.13.1"}, "dist": {"shasum": "4bf991409845051ce9fd3d36ebcd49bb75faae4c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.13.1.tgz", "fileCount": 36, "integrity": "sha512-+Zp/YJMBws+tg2Nuy5jiFhwvPiSeIB0gPp1Ie/TyqFg69qJ/vRrOKQ7AsFLn3solq5/9ubkBjrGd0UcvFjFsYA==", "signatures": [{"sig": "MEUCIQC97VdfVSb/M/f7xjsGCuJiMzV1qBtOsjkT6v62W+FuLwIgd44Tdqm9fIjCLNm8XiflstvzzypTQn7R4AoOxmpUggM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207436}}, "1.13.2": {"name": "@webassemblyjs/ast", "version": "1.13.2", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "devDependencies": {"mamacro": "^0.0.7", "dump-exports": "^0.1.0", "array.prototype.flatmap": "^1.2.1", "@webassemblyjs/helper-test-framework": "1.13.2"}, "dist": {"shasum": "69580293aa0aad97187f7b54b52c094f64022046", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.13.2.tgz", "fileCount": 36, "integrity": "sha512-Ju9l5kll1EvyeaZTytcnMgdt8Mng+VpZP2cdgzJgLFbOYyHgLyaHXCvqtkU3AOKJKKNQEwKZoUHSw6kYbxzA4Q==", "signatures": [{"sig": "MEYCIQCsHpBa9B3Ac9+CYy5xzswhhZxTWsYSqAjwJbUjQOJ3mgIhAP4h544UCONEpy7E1lUoVQ5wWAa8SEEwhIFzbAv7GtgI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207436}}, "1.14.1": {"name": "@webassemblyjs/ast", "version": "1.14.1", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.14.1", "array.prototype.flatmap": "^1.2.1", "dump-exports": "^0.1.0", "mamacro": "^0.0.7"}, "dist": {"integrity": "sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==", "shasum": "a9f6a07f2b03c95c8d38c4536a1fdfb521ff55b6", "tarball": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz", "fileCount": 36, "unpackedSize": 207436, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyBnKQeKfWXSLpSyg3Ll9GWomP+Ig/6ibi1UTzVHy5sgIgIxt4o/i747PATsegU+TTf2kIBCMrSxdaVCbSH4SpU0E="}]}}}, "modified": "2024-11-06T21:53:36.477Z"}