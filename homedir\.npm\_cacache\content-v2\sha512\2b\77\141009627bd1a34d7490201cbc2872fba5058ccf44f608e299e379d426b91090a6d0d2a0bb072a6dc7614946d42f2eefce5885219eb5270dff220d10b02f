{"name": "readdirp", "dist-tags": {"latest": "4.1.2"}, "versions": {"0.1.0": {"name": "readdirp", "version": "0.1.0", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "dist": {"shasum": "62446d51e27ab2066d5a1831c7f9907846727b6a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.0.tgz", "integrity": "sha512-IOlv+B+KMPWo5PlKEAlenB5UYkGw71+Owl8YCMmi7Kggngk3XNSZL5GEJ+qvjCwvj+EgevQ9H6oSk57DGNoK1A==", "signatures": [{"sig": "MEUCICH7kYZmBNZ8uEDxwJrVgbnjbjE8Oer6VWEcwAUWP95lAiEA9R8RlYF26CAD7Wl6SFhAW2Z3zRxbj91/KmTKV25T9u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.1.1": {"name": "readdirp", "version": "0.1.1", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "dist": {"shasum": "9d2f892b8605b5aac44cd01da0606ac52745f1b5", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.1.tgz", "integrity": "sha512-qEbml2NfuFchx6ulOYWE7ry7k1iamtfy3NZBS0uxZqxcdmXQKcfc1HH5T8Ioq7TtnsObyNVcO/p7nN2JLk/q5g==", "signatures": [{"sig": "MEMCH3c0yk17+UlttpF0XVvSj39h5K1uFMCEd0YMf/xJ9K8CIHoQRxQL08GS1v45tAfUpHHD5J37sHLWSbTLHjNKAN1r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.1.2": {"name": "readdirp", "version": "0.1.2", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "dist": {"shasum": "f7245556bbae9a8c6d0ac31c51d6fa81cfc09d36", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.2.tgz", "integrity": "sha512-XQUVP0gLO+Mown+w0M/Pjv/rV4G/2btWEL0QjTOz2oDrPXssd/ck3HtMKTfM67lq9j8H/ijlLMRDfTuBNEXEfA==", "signatures": [{"sig": "MEQCIHE8BC3fq/xc6SJyEZu6DHR+zE7UWe9iU2kFRGwHssV9AiAvQWa0GMCYKaI2BeGNytotKwQrNiY2Yej+pEAegqJwbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.1.3": {"name": "readdirp", "version": "0.1.3", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "dist": {"shasum": "c852a0a090f72a1c026ec092e993165f11d2a613", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.3.tgz", "integrity": "sha512-zS+MAvJAKxzNlUqTXHYVgu6oqeXvFGK25XtlmlvjaGMcFlE0R0u0pgnBXenVTXViM5DVkmYFlkjnPHmkqkjgfw==", "signatures": [{"sig": "MEUCIQDr3sBYabAuQXOR5s97/hKRvManQU5QP2dnK09aRbBtpAIgRTWeKp/bw6j67Fe4YT3NZcfsJN3w8HREb4RL0c3sWeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.1.4": {"name": "readdirp", "version": "0.1.4", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"mocha": ">=1.1.0", "should": ">=0.6.3"}, "dist": {"shasum": "0f7f6095bb2bce3968f7f9ed03da168ecd359933", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.1.4.tgz", "integrity": "sha512-Fxgb9AYjUINt7qMXWpeh0CHWMx1J8wHBGePNlT01d5X49624ogjFunCBKa0lHHLioQ4MKN4pUG2UyJ4MI0onkg==", "signatures": [{"sig": "MEYCIQDdmR7zakYMIUspgHL1wfFAYz3PUTWFqWIhRiI7jV29vwIhALIwjFQUoQ1yBsDcVrpsArmfnQr3qBwFI5kduggmOhol", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.2.0": {"name": "readdirp", "version": "0.2.0", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "ba20bb1517e7c9268989e2692fde1d8d2712614d", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.0.tgz", "integrity": "sha512-udHI4w8vXfqpgEKNHMgUCI0KsnSyZOu89aTtmbnMJhRb85qNuHLLgppJGLTRC6YiVB98mcJOGJgXcfQMM/cmxw==", "signatures": [{"sig": "MEUCIQCVVAcQacfDDVIE/RPATMB1imeQYM3ko7jsYLkkMwKgEAIgdpdCvPRJIgl/5mGYLQFit9lIAUXtvXIUsMSQtTggIfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.2.1": {"name": "readdirp", "version": "0.2.1", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "679497bbd6b1f8cdf94ae5bf29c803c3c1b582a9", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.1.tgz", "integrity": "sha512-D643zKxHNB10M9ORzlET+12Uzatk3yBs77bw9+xGGLL1uqvaJAMFx0W7x0wiwC2YgSNVbMIoNn/r1je6OnlvWg==", "signatures": [{"sig": "MEYCIQCk6/Dn7p7f5LbOAdOLrijd+htf2Vnmp5J2zmlT8dO/qwIhAKp8MrHQiifG6/5+oDWWsNfs0PtOtZhp8glLIKGzCdU1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.2.2": {"name": "readdirp", "version": "0.2.2", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "2578a30daada8c4aae9042a3911eb0a301b8bf07", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.2.tgz", "integrity": "sha512-P0i6V6OJoTLQ9YP6CG3yxv9vaFQ1ntos1mNcG7UFEBtAtjSDprbZP/61N6L3weNAeC33Je9Ax8R8gguyVHNSFg==", "signatures": [{"sig": "MEQCIHHFWInAW9rYSHI43gZdhQqEOMaqEInxo8GLv+T066goAiBiGjCrdYdrXbU7+oHDmC6xvakLeCjf5PoIZR9ss2VSWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.2.3": {"name": "readdirp", "version": "0.2.3", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "51c51b33bdd05a5968d508aaae984136ae2c4cad", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.3.tgz", "integrity": "sha512-kMMWr7Uk5TXlBkYRZtMqktArKltNhJ3U8IPaML5RcWxMbkDwgR1ahHc75S8suN7BdWZucg276W/KeZ1cP/soTA==", "signatures": [{"sig": "MEQCIB/fYzEJ55bW6Ng6OHbPMLaS5Qz1x9QWDuU++kX+rx50AiBRgjT4xUrnxRffTehEMVKd5DugOAGI+Mfs29hV6TAjGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.2.4": {"name": "readdirp", "version": "0.2.4", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "469a896cce3fa70b856fbbd10e3475760060008e", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.4.tgz", "integrity": "sha512-YWkRlc18TRd+9rIMqz/vibjAIa7Pl0KzCqR3fwEdIXIfnS3iEwLEk7SMpgbfGKLo5ZyBSLxlAsMgKS6yy1N0Lg==", "signatures": [{"sig": "MEUCIQDs3y228kCLqvlrxE2BpmmOXtb4bnA0Zske6SmjtuMNewIgdSsmXw6JZ3oERQ1H5RoInlbNbmAzx13dsax97MBXkp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.2.5": {"name": "readdirp", "version": "0.2.5", "dependencies": {"minimatch": ">=0.2.4"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "c4c276e52977ae25db5191fe51d008550f15d9bb", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.2.5.tgz", "integrity": "sha512-j3/RLxRiCZSei/4UaYaOAqHa+rQ8ZL9vpolGO9E7mLXiVTb7Fu99eTG74ZmaB/4gCGgy7Veq+U6vw8y7sKJiTw==", "signatures": [{"sig": "MEQCIDbZiauhlmkRxtR1cJQuywjAc5PcLbffDsBV//sUTQNHAiBRXAnkZrIEwRS97JyV6KBYh8GhQecWW5L+J/QL+G5ZJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.3.0": {"name": "readdirp", "version": "0.3.0", "dependencies": {"minimatch": ">=0.2.4", "graceful-fs": "~1.2.2"}, "devDependencies": {"tap": "~0.3.1", "through": "~1.1.0", "minimatch": "~0.2.7"}, "dist": {"shasum": "daee0f17dadef1904f41884288045ee01b889e23", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.0.tgz", "integrity": "sha512-YyU/tkbG2sT38c+d7xKHIP894wB8Z6UtMoJkqxqYtFmxxAFYm6ClSxjmgt0EIrwgmvK85DCUeFmlo4lsRfagQw==", "signatures": [{"sig": "MEYCIQDkTh8pNFUitR13qwiHNE0Wh4eP48YzYzFvrKC5sZbVLQIhAJwT2KvIUYl6q4+P4ckuxWVKs3l1NoDkelH5D39+Rlno", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4"}}, "0.3.1": {"name": "readdirp", "version": "0.3.1", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "dist": {"shasum": "6a77e1dc33f20ca8e010ab981ca2319f882964ad", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.1.tgz", "integrity": "sha512-0EqVz84OgUo+znxZP6g4TRjIkBvce1mQWr7fMAGe7+GolgSxijixLCZnj+BQkr27VYxNGW5iBuAXEg+EsCKH3w==", "signatures": [{"sig": "MEQCIHjNNJchIb1MJXDiUlk2hqMz5ZqVjhVtf+Q56NtzIBpVAiByiwwPzYbl/lAL2LJa1Hr5n1rmzEJXW/PgEvv+GKBKsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.3.2": {"name": "readdirp", "version": "0.3.2", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "dist": {"shasum": "f6b4d142f2089d67aba0106f19e7b2d0da748be9", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.2.tgz", "integrity": "sha512-6X2q2bXBH8N+nc6KpYBrOatBoN6yYA9T/KluLvJVO6DC1jGE1LR8iLjN3iYTAfQ0A1vHUDi690qKpHutuiBAAg==", "signatures": [{"sig": "MEQCIAbz1Fh028gTfE8J0kJg93c/FpcIgYVJgf3Iid6OcP0zAiBjK0a6hcH99hXQfDAREBrxRL1F406Az0EmMnhyvgPeAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.3.3": {"name": "readdirp", "version": "0.3.3", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "dist": {"shasum": "552105525a105739a6198bfa98bcbce64b3d3818", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.3.3.tgz", "integrity": "sha512-FY18iOcywiypt0hLNh5H5CRnU9A+ps5pUnxV3u/LKPEcu2iUdteEJhokYKlPu7kmqZlU2gnrzS8Ltwy3hW16dQ==", "signatures": [{"sig": "MEUCIQDhrBQT8GJvBxZsvHWFiSc9OgVs0179bHOyruGZ+v4qNQIgFgxCKpgmroVyJ3aWANwy9SUOUSYRe9dUXUcKYi7HRXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.4.0": {"name": "readdirp", "version": "0.4.0", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0"}, "devDependencies": {"tap": "~0.4.3", "through": "~2.3.4"}, "dist": {"shasum": "ec0036fa0eb33c71cad70d9ca6082e52e2168725", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-0.4.0.tgz", "integrity": "sha512-iyYL8mkS77LE6DqmoBZXYePhbwBXRqhPPd4nSvv7KkYwDB90pc71aBk41eJEFq+CPEpy6s1Zni8Sdkpauaywbg==", "signatures": [{"sig": "MEUCIQCmS/DbaNNoW7rHEd+NDjx9tt2JSsz9PULuZcIOegl8ewIgR6wzz8kIqicmodFE166HjYoYoeFAS+YXSiPJfG8riqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.0.0": {"name": "readdirp", "version": "1.0.0", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "dist": {"shasum": "b8ce62a269bc4dc68134f86cc964f027e47e1771", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.0.0.tgz", "integrity": "sha512-LEJpbMOGkqXPGzyVDKbPDecMGtDtxVW2w8iIuTayUqz7R4+kVybWgzuus8u1+sVANgjoWcu3jTmt/e6SkOtZeA==", "signatures": [{"sig": "MEYCIQCGjZJnYqFqnhUTfxtGU2/8s/PJpoa+5x+q6jcGvaDSdwIhALTW68zJmphvaXW08X0oh+W1KXTi/ouDSZufc5SJaAV1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "1.0.1": {"name": "readdirp", "version": "1.0.1", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "dist": {"shasum": "16967d390300346a67ffb30a3867bb4b6173934a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.0.1.tgz", "integrity": "sha512-JxCNmsvrUNs+rNg3k3j0daqZlQIsKU4+ktKagvyNn2Z74hz/67Yew9zLSt/TPPQyEDTjEYHLKLyonVf+IHyAvg==", "signatures": [{"sig": "MEQCIE0AD7xh5LtIPZIcusCCqWJ2lVlnoJ6PmyTpf6tiTp7eAiBXpMYsM16zxGN5ALTFssK60cWv1S+rS7plCyPP2I8Hzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "1.1.0": {"name": "readdirp", "version": "1.1.0", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "dist": {"shasum": "6506f9d5d8bb2edc19c855a60bb92feca5fae39c", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.1.0.tgz", "integrity": "sha512-aYbLJ+sPWx5YFUAvPJXX1fGhsKGk80vqSrvJDZ4nvH/dvRbc4roqkBRlal/ct7tUNzTM1h6JcA5UgUoHj2UlaQ==", "signatures": [{"sig": "MEQCIDKeru+Dj/flsclmMs8jsGkqrlAJTndUSeuNPS9kgqk5AiAQ/8TdypTqiAep+VfHHB+wSj8JxCe4uPio+35iVWpQ+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "1.2.0": {"name": "readdirp", "version": "1.2.0", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "dist": {"shasum": "7ece25c8fc0ccae4461fe28e8a8b30b4d518cdfa", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.2.0.tgz", "integrity": "sha512-/Kkzwz/fq06M+FjuvhaGjj1q3kGe2NBJqq6LlOubCWYg4thc8WOIHMFCE0H1Ubfrfrx707R39WQ3U9mjuhVPvw==", "signatures": [{"sig": "MEUCIQCqLmD3Qlg+gwPkYW8VkSVnqmO8hcCmD51RiWVkuqAEmwIgLnZJRmr7t42FU66vBy9pELlfdJciPRIkh2wKmoVwivk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "1.3.0": {"name": "readdirp", "version": "1.3.0", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~2.0.0", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "through2": "~0.4.1"}, "dist": {"shasum": "eaf1a9b463be9a8190fc9ae163aa1ac934aa340b", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.3.0.tgz", "integrity": "sha512-H1BGeo9VW8nmdwGo64SKRQgNNZwEuqtVUHijOoTDYIpqJGNKU65JaRXL3iqa/8tmVJ9jfoKY+soTznq0cOruTw==", "signatures": [{"sig": "MEYCIQCwdJNcE8skcmZ0PZ4DQC+7ZBMyOyJAhK34v/KPTbkVwQIhAJmAFQIV+JRsYtHFFtHnLwrYwfd4cRdPV81LdK/iuH/z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "1.4.0": {"name": "readdirp", "version": "1.4.0", "dependencies": {"minimatch": "~0.2.12", "graceful-fs": "~4.1.2", "readable-stream": "~1.0.26-2"}, "devDependencies": {"tap": "~0.4.8", "nave": "~0.5.1", "through2": "~0.4.1"}, "dist": {"shasum": "c5de6fcb3dec80523c1c70113f1a190d8af82c89", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-1.4.0.tgz", "integrity": "sha512-QOYlYH11MJ1RrkxHOBZDSvXOgRy/gKj6xQIvShAEf2KAaHn03BetRpakZdC6GWUEOpsGf1XXalXeIXgEblZbLw==", "signatures": [{"sig": "MEYCIQDajOhJLyBDytIudteqU3W6mKvfLBUSN4JzeB8FenSHGwIhAOdyrU2FLfAotBhoDuL49PXhRGfLa1teG7ySBW7flwph", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.0.0": {"name": "readdirp", "version": "2.0.0", "dependencies": {"minimatch": "^2.0.10", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}, "devDependencies": {"tap": "^1.3.2", "nave": "^0.5.1", "through2": "^2.0.0"}, "dist": {"shasum": "cc09ba5d12d8feb864bc75f6e2ebc137060cbd82", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.0.0.tgz", "integrity": "sha512-4mfs1i2diwg7SgZjBHxU+mAXg07dFxmIFbzFX8R3/KO/iGD2mW4lvOer9nH5emlwvXUn473enXJoFZYi5W8+mA==", "signatures": [{"sig": "MEUCIQCEvW3QCdYW++QoPw78oyJjAuF03ajFaNSITk8tPPvIxAIgHTMMNGIxW9u/tZc5eCQxtj12ztU+7NqxYGjQE2aMAAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.0.1": {"name": "readdirp", "version": "2.0.1", "dependencies": {"minimatch": "^3.0.2", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2"}, "devDependencies": {"tap": "^1.3.2", "nave": "^0.5.1", "through2": "^2.0.0"}, "dist": {"shasum": "672aa0c5013e7942996bbf3a392bf69aef89d5a5", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.0.1.tgz", "integrity": "sha512-4ccmLnjcaQg1sBtWsolBgCbtjCv26taibhIWtFXQB0QRwWP8kK1HKMMJdBO5mz5EiTN/0ObXHsNfFGfGt8uPgg==", "signatures": [{"sig": "MEUCIQC2suXgAnZabd5h8SBMn8g6gT/69bYoo6wBIEKXNyLgsgIgcI0rkUmOrrLMr+fj68Z6HwKrC493tQmghnlS2HQ9E5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.1.0": {"name": "readdirp", "version": "2.1.0", "dependencies": {"minimatch": "^3.0.2", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}, "devDependencies": {"tap": "1.3.2", "nave": "^0.5.1", "through2": "^2.0.0", "proxyquire": "^1.7.9"}, "dist": {"shasum": "4ed0ad060df3073300c48440373f72d1cc642d78", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.1.0.tgz", "integrity": "sha512-LgQ8mdp6hbxJUZz27qxVl7gmFM/0DfHRO52c5RUbKAgMvr81tour7YYWW1JYNmrXyD/o0Myy9/DC3fUYkqnyzg==", "signatures": [{"sig": "MEUCIQDYggKgYuXCpPonY03Lmk+AmlEZQumuSECPWMIIQiOZFAIgA7iqFJyRrNudb6bm1iCa+YsvkT5vzuvyG9LaofEO7eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.2.0": {"name": "readdirp", "version": "2.2.0", "dependencies": {"micromatch": "^3.1.10", "graceful-fs": "^4.1.2", "readable-stream": "^2.0.2", "set-immediate-shim": "^1.0.1"}, "devDependencies": {"tap": "1.3.2", "nave": "^0.5.1", "through2": "^2.0.0", "proxyquire": "^1.7.9"}, "dist": {"shasum": "cf040e9cb125fc921e6e9771647496edde3666fd", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.0.tgz", "fileCount": 5, "integrity": "sha512-iJ1qI5H8fDSgViLIHepwTeV0D9ISMvGQlmKlU8WnNVzqb3KrvGZG/sCz7LxNKvvMU4JpcfejU9uxGn30zTVlXg==", "signatures": [{"sig": "MEUCIQDPw2ITrJnPzhKy2cIFekCBTAsLsCEd73OYXilvj1IoOQIgCOevyLGV7mx+3yqzrABMw7E8rHQ+xpeyWIi5lxYnAbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmonlCRA9TVsSAnZWagAAQcEP/2lfS7fqucDQRlGg66J7\nVQ4CEEqMkcdWuE+ouI/8/DQekycQzJVr+tqq+ocWeV+hnONscbZsnUZ/F+mN\nWAebXI0L4XanlMnTzaVC+AwwFBAWJADiQAXOnMlQ1wccJeud0ak9SFR/0cGD\nOS2+Xpp6XMs42YhNKCBBeSeJy4A6wLYotfmtK15+8zKov0QWq8+99mEmTIX2\nzPSj8uTSF6+mMXbICsdlicD8aNrMva8QSnCfIvRcRgYoxe6h+1EAnfk9v16v\nw1x6f/yxPRjPK9MkhQ7tXUQ1kcdF42j7eXB45x9FrYBDK57GT4W96DT4RGf2\nrfl64uthDVqsuAQ46mF4eYLFrNKc3hMHkYyiE1l3KOZJy/ELp4suStBru8Ax\n3uT89m3UnIuOEDfPb46iFXRonAE/RiLCTPRXrQsv2EjgdOkUPhC58AH5t/q8\n8erecHAJnOrXE6SELf/YseyG5u/QEbbMU4NyvHYgFy4f/Z9Kll8lAKAZvu3i\nNYP3OH4Oa77aa2nwCj6Aug6R7Blj/W5P1su8CJNeRwOVazuzJYn5zN9JSZs1\ncuJt/uHpER3StmdKq8fCMMJiEFCbilXsIoZUJWOg8arTNXO7CoiOV76WGUPk\nzADhhPrXxPyGecY+R1utoTLo7fks89URQTVVvp/B8W/PjUzwqUW81PA5JUsW\nDyGr\r\n=M6Ra\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.6"}}, "2.2.1": {"name": "readdirp", "version": "2.2.1", "dependencies": {"micromatch": "^3.1.10", "graceful-fs": "^4.1.11", "readable-stream": "^2.0.2"}, "devDependencies": {"tap": "1.3.2", "nave": "^0.5.1", "through2": "^2.0.0", "proxyquire": "^1.7.9"}, "dist": {"shasum": "0e87622a3325aa33e892285caf8b4e846529a525", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-2.2.1.tgz", "fileCount": 5, "integrity": "sha512-1JU/8q+VgFZyxwrJ+SVIOsh+KywWGpds3NTqikiKpDMZWScmAYyKIgqkO+ARvNWJfXeXR1zxz7aHF4u4CyH6vQ==", "signatures": [{"sig": "MEUCIQDNICfI1G6RPtsAu2OuKyIECFL+9ftutE69EPuDhxP6DgIgdudbv103Bzh1Aq8L7yUKLHwJQCTA1NagagWwgH2TdAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmpoWCRA9TVsSAnZWagAAfksP/0wK6B4d4pn98vFuBWFV\nUhHDrg547zhR9TGGiKuu0FR6PfWSOjoTFsRJjBv+wWrvkicROn+YyV2DSTPY\nSGL980bRRIXeU7SfuX4CDiTBt0kioR8rEaw34MgKur0EMzwcdzgRx0lQ1dhs\nX1eKZ6nDOBFPBKI16Fle9aUIZNsBSIqo0hPcU+rmudEo4NJE2vkGFe3Fq2Ph\n0IRuWtMRS3zCfqL0baJ+yNHQ0iiQRDimCqPl6/5CNznPq7yTwXspVYgMU85N\n3cR2sciTdoYHtGDw1DojL/b3W++kg1P5qBW6pU8dtx9PoiVNCbYDw0GrUcKf\n0GbZ7LNzj4+MS6Z4AiFwWYJZRrcRriDT5MxIvrA8bEXweua2mGmx7IZe4YSQ\nf5B/DsNKoYeL8OuNBscxHsnRH9WSprZtlk4sZjfukV2UMVkgWzE04UMufZ46\nVlpPw3hCA6Cd5k6Q2jJ5ID0jDJKbCxmiWNCiraKqOwHL/aho61Un0wbAEMl7\nVC6brtN/IH40NY9dUHGFjbIX6hYKW/oadEZ1glS+iraspBWK1DaqFZIrOZ1I\nQ/wVur333b3JVZvmeM4BOIMmmyeG6Wqfz45SMMLwLGs54sN7U6Ji8CyGqk2F\nWukXq+ijjaDcquNNoNlz8DiIEYzs6EO1wARaeuLsFtAsWfU9EkjVvI4YYmUU\nPG9C\r\n=pWI8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.10"}}, "3.0.0": {"name": "readdirp", "version": "3.0.0", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1.7", "@types/node": "^11.13.4", "@types/mocha": "^5.2.6"}, "dist": {"shasum": "d528ababce0382e06e2ab2db6405dc131d2efed2", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-XJUBnlLkO37C7eCO9fdYd1HGRnnEq87gGyoQxpvPKLCLc9usnkAjkjvXCcLzrwsgTtjnPd1r1zA7ylUZdFZQow==", "signatures": [{"sig": "MEQCIH8+yHufOPJvN0w3trpslwhl44MHNAVX2zvstcy2bW6zAiB6DlK/axRTZ5H+W8KRoyLNik4+l6TtjFwbaWTTr946VQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJct2cuCRA9TVsSAnZWagAAo5sP/1nt1Xa7iCmHIwbYiPhm\n3fp2tlYjvb6r33TE7yLTvCMwBna28c8sZoInuXKgSXDbPfTF+CvAZSPxuqUz\n+ZFw4bJG2wazQyvEoypac2hI7Is1qs2tbKE/vUFkGglW0tyNNN2r+Gn7rlnI\nPnaKugIevcssycdOF1p4dJpu64lr5O6Xa89Vjc4nSFT1ckpgZur/ee/vanaT\n3I6aFtvyi4w+GIDHw4uXj6HyiZ+NmvmltzuvOuJm3pJNo1GbLOeAdvXvTsCb\ni/mIXsRvFWnVSl77YcbcxVbhVc+TtYI6ed5eieBijT+fODVOKHFcuaOPiV5f\nJKIs1JWg1OeZLKFMVOeQRdbks0QLcy6FY/jMsbzDrdCg4p4MTacmAETLF0Lz\nL4es+Je63QLXtO0q4mGPymMcli4Qo9jaJ5THRs2stZU6wzcN05C4ysASAZrR\nbrIzXK2R+Y7DX/roLohXKg3eSEigbNxOwtQ7M7N7OzMSslmbFKbikgZSNh4p\nVjmMtZqqaExtupzVmSix/f/iSV2MS/0R1owkDRVfb7TlatX107hU3vy2zmQc\nWKIRDuaBra1SIsv/XjX5Avo/TLlHZPMci5hVKweESsNZFbAaxIDvUL2yOqoS\nd1AjW4BTl6tY4vSqch54CYWqL1O+R8HpR1JDyIJNmMp1gnp21R+5gGY90xor\nF+kq\r\n=HPa8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.0.1": {"name": "readdirp", "version": "3.0.1", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1.7", "@types/node": "^11.13.4", "chai-subset": "^1.6.0", "@types/mocha": "^5.2.6"}, "dist": {"shasum": "14a8875883c5575c235579624a1e177cb0b1ec58", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-emMp13NEwWQQX1yeDgrzDNCSY7NHV6k9HTW0OhyQqOAzYacbqQhnmWiCYjxNPcqMTQ9k77oXQJp28jkytm3+jg==", "signatures": [{"sig": "MEQCIE8S1+Cgs7ma1RukX+EJCMwIBIA163uICtcf7wHhvzTEAiBmi6yhAz8Aw8r3FHMH76Zrha1YbBstFDWnw+4NAyji5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwNuhCRA9TVsSAnZWagAAKegP/ihNYeX/mRdh8xc/YrQ7\n+WbYOYNCfL5VfzUlMhm+ptYdZ1gCsiOxY8z0ROZfCIH1S6wAXcL2ds3u3XMc\nUAjAXBwCDV2IOZkumIXaQ7fF+bzf1gcttxEFEpWJfMMENiopqPMKf4dz7Lq2\npFeE0Ucj3hW6mAqbzKYfea9iQ36Dxa8shga2P8yNkH/K5eGkzIMbVIWF4nbB\nBxa33/vkdAn/pzBP0AoX0rQiOkpvtozBgZc9NwUui+q6pG9RPDbG7ynxc2Jo\nIx8Hd+fcHYwbBY9rZPgrq9D1Gfqw3eX4zCfwNqWiQXHTSas+3JmZJ+LB/Zex\nggSDrkxd9Bicn55puukzJQwU0mi7W701gMvB6PN4zFKtRRRkDO+dcEfdEfnS\nGGZkAeQC+3ejB63FBUY3iW9Vj2M4TXGKb77VUL0fW2JiJE6PPE3HCJuTVwJi\n1BtEdFE4BPpNt2C0vTay1saO4M9L2sec+pe4MQSoYbxVM3/dMcO7b1KB+myP\nVDsmx7W7IbSx1vtP+d3D48lGnPP4cfQl3eu68c/t2TK3G0dQ57JRyzumJxNG\nKW7hRmnmIyJcwfw/hVj3jERpoqeOv9n2oQEgkLM+P7HArU1OF0LD6DU9AhQr\n6ALo66qSjWk426vSyjBf49YKIW5TAqia7mqnRviKP4qxz9xuNSirDJ0/Tjbx\nqeUH\r\n=8tbN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.0.2": {"name": "readdirp", "version": "3.0.2", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1.7", "@types/node": "^11.13.4", "chai-subset": "^1.6.0", "@types/mocha": "^5.2.6"}, "dist": {"shasum": "cba63348e9e42fc1bd334b1d2ef895b6a043cbd6", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-LbyJYv48eywrhOlScq16H/VkCiGKGPC2TpOdZCJ7QXnYEjn3NN/Oblh8QEU3vqfSRBB7OGvh5x45NKiVeNujIQ==", "signatures": [{"sig": "MEUCIAMAWMait6MnOfdv7hCN7OtCXsFJ/Ye+6W9tI+eLtwk7AiEAvRhMw7BVP7PWuwrfaQG4zIgtA77CblTk0tPq1txJabk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9ESsCRA9TVsSAnZWagAAVkgP/0WHdx9d7yhkdVsCn+y3\nlLFRVjr8SH0T9Nh68kqbmdFuuDjgD/SdXBOjoUIhO1ZIos4iXBy12Lxw3naq\nP5z8txkxl0dUERLhsL0zW2U+eh+aVqF69JIILp+U5OmyF+QenOIaGb+PwRum\nmOkIspBOkhFo6L+BJIrv0N/egqOinRTE6CDtPyRhMXJV3lfxQUfUFORC2RX5\n6nSgJKgD/nvvvSReyapFrN4GkFcn7Mi1AaOyqrK2oRDbVVD73sS/Be5+oojE\nlEhBLOvGi3RPAjWqrF+NbDtfw9Xe1ZnxG1R7Rcp/EGMzR5PJyGvJd+hBbTjh\nGGE7hAp6YDPTlDTBt4AkVcPlJX3vKnypNJ3BcfFBOUT9x8+lzBTOEZWvJmZJ\nnUod9WiSK46wjlz3mmG+eWgg+oCy5TPyrsXPEiPiorQEi58iz4A4tvNl3isp\n6aiERWoQAeUFAnliSAVftLDRhE+lE2hbBsGxI9SYnbApGEVeYatRh6Dqf2Z2\nRMeyWp6T7yYdWaKGIQW6y0LJuhNuqm4GeKcxBxD76EH3aporaGyq5Ft/BpSX\nnKS1oAiEeUvXzfmJOCrtX1JMR34A8TJ3XpB0SaHIixH9dOhuSXx3dA3A9koQ\np53C4+tnCB6bNaA1GKrL449YGM8dVpSCIXiiKtkBcdNyoKClPf0wr3fkuq3L\nwtAh\r\n=HwHv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.0.3": {"name": "readdirp", "version": "3.0.3", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "dist": {"shasum": "6300e1ca8e3ec6fcf064b7cd09e6e4b948d42e27", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-ucnUgJo2W/AC6s+dT0RS0rQOQi6PmniKgSqkF1Ung2UAkAqEUcqRVTzi+eWvTMbxicWDKHkbHJvvk98y1+de/w==", "signatures": [{"sig": "MEQCIFumupHntRmdjUmflXJisAKR8JDbMBh0GAcOFj2no1QGAiAYi/+qdw8zhhl0ugApGeCSsN69SR7HMMkF0nMe6Dfe7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+F6CRA9TVsSAnZWagAAYBQQAJ51F9RNkb022O/XHhFo\natkA1FAld5JrYLNSBtD1/zZJaiOTrv4HIn1RTigwIYMUY6lSiCYARUzh8+rR\naDAmJlmOBEeEYSTFgSMpcexPZ3TOzrRkWaJbP6+lggSeNAQHKxmwN3llrhhy\nFhpboE/GCjLDlBeMvXZD4XjxH5nbY4ITnpAmb4sYt10LZsTxmzvtBbDI6/d0\nwOkAxlbcvoh0PyZe3+VdvwBn9l5bddzrTvhJxnqM0n34BS5pGU663NdtKXVA\nncPFGjUxenuw85qM8gGw5i8axeKS/ynIwo2Fe0zrQVVjg+HGRhWYkil8/lDu\noFyDbl/ET55ZcbMiWNpOeJ+wEQhUW8s6CBaHt2EXFe79pgS8REmO3/Fn/Pdy\nfA68NY8/xeKAWv6zXH8JR/H0fIkGIFSwN4NVp1AIzCDf2P2rHZuAR3CFD9Xl\nAuQaOVGzgq90EVQlEy4YhpqnY9c2H/E1rq7yfLJDkMROT5Rxmey5rn5gqzky\n+cBM8fF6hmpraXWDRg0f/V9AVru7qVaA62WjfbJr03S/Zyr3Maj5ca93okHK\nUlqeqIoS9JHdmTl1W4BBcM+jckQ0GytvuLGSXvJyNUnvYo2ofS+oBLtIyBpo\nuxVEQPIv2iiyzSyf4AjpvkxoSCwrkjursN3t5z9aPUpS9a8AS17CB3tKDCvp\nseiH\r\n=M0XO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.0": {"name": "readdirp", "version": "3.1.0", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "dist": {"shasum": "c033ba515a2c77c0e81ffadecda6eb36ac19df7a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-iIj0CKV8k1WgcKZrP310W9Sme9qZyL/pldfGGnX2LoxMMXDYn1smRyjAIZibHXjCD0sTYx6Oqw7H2vWrODGlLw==", "signatures": [{"sig": "MEYCIQDXfG2hDfgPFtuzBIDXwkNhfY4u2cAzM6vNiG/ddJKyogIhAOGv+LtX1V/sYIj89s5fJqKX4pNxK5oRhuStGwCU6oqU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdITOLCRA9TVsSAnZWagAAo4QP/0lzKBFQEHrlsgKFQkKh\nfMZugIm7Ya7NjFW+q+iXE8GCU7lcFTxiT/sDPp7hDQZDvxd0Y4V3jequOAFe\nZEC1KB1zHz3Cgs07DsApZUXS7Iacxt9K72q5Q0w7CMrknZ+LvCCLKvJcTN7M\nbmqB9+ynzNAytvekTQ1PL5fsl1hwMXEPLvPcDXN54MKsCrx0LQmu4MrfKtM3\nxYqK0ko9xvVXe5dxd02ScfFxE7lNvbUZHfU4wvnZUCIlHuwx691zuYaUL8k0\n7ciUIByksmlu3Te39VhHWlP7bRofAsKGtIwPZnK6AgZjZPHTfRAuCvEgCajA\nsXEXlC01psd48xUY1Hp1QhDOnnTqMYT7XgwWUjcI8UO5uLwLO4xZZ/pqJGjS\nPx+TahQX2CHoE6Y6w4HdQRvMZF+VEAhCCMukl1JTvgZS2v6UrlgDYcftcwBu\ntCyxopu3AikMzGxhZ4ZAIycw6vfh2PSP39PicqwrQhBJtn6+f0O3jxT/e0oy\neMIp5am7oIs28+PmazhrmeWU8lZi6zVzC3gPtdVmnIz8xDrZ1I+Nzjaa9lH8\n9L+I5N1RvE50l/630C8mR3WFKgVWw3+H/He6Lw2f7PYl5kx1XeAATIm0Tcwu\ntG5LZd5CR2P25WYcnp0vC2ZEEg2eTnJE5F5NyC7/odTS2hBVKJo2NPWyB9UY\nf9L4\r\n=EYTO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.1": {"name": "readdirp", "version": "3.1.1", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "dist": {"shasum": "b158123ac343c8b0f31d65680269cc0fc1025db1", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-XXdSXZrQuvqoETj50+JAitxz1UPdt5dupjT6T5nVB+WvjMv2XKYj+s7hPeAVCXvmJrL36O4YYyWlIC3an2ePiQ==", "signatures": [{"sig": "MEUCIQDix3+Vt2x6v5Tycgwhd/zhIysTuE5PUbDU8+LVEMa+CQIgKiTjImva+TzhOxjiMUyjrErY2WR3Jc6emVTc2XH98J8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdITRqCRA9TVsSAnZWagAAM7QP/0vewo7MZXR++1K3MnT+\nXDfNaU+YVyhGgZ2a19wzXc+IqmlGKU110FjRq0diJm25VCq/D8WftsiAvxlB\nJ3qmajhi2MbGL05EjIWp95fDw82Zwz1Nfcp8m6NjnWwmbTThkLLyooiTilvR\n8necJQv0eedTKE0Pf+XfNhjtk/eZP/tIdKkLdp9IdD35C9DRXpKqZB67d4jO\nFl9SoWIIJnpcfNAruSJk1bN8WzS0ijl/QZTb1oGHnNSmE+Hv1AAlA/CUwkM8\nyEwTPPyfmXNEl3zpfWM4QGZ3UH1xctk725YhIDnQwZCVsy5qbS5DTEYn1i4t\nMOSNHYm+O9FUArnxBHui4Z0Nnh+oDlcBeUwfXnqu/kJbO5M+54WYbf6kGHu7\no+4QVpdi/3zB/yunYyXwl59u/GV94u1sDeubVN+2a9biv8emCeHvx/2odfka\nzbj7/aMjW5ogmIOnzG3krK+BHeM5/jfE5OThcO6+OkQe67nY+tDd68C31ueA\nPokJcPOG7pIkcS8v9D+LNXik/sA+g1gi/3XH7YdA0a9YULm/50JvwlKHcZul\nV7gx5QuaoGRMQxTPvxvnLejJLllDUW2WoZVcNbuejhG9ZwhHJPv2EsUT4JTA\nx6Kt1p3qXP9rt6HJbRe+FcaSvjX0lxRGdImLdOpthKH02lQrz778QzbEc6N6\n4J0T\r\n=ZVFc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.2": {"name": "readdirp", "version": "3.1.2", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.1.3", "rimraf": "^2.6.3", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "dist": {"shasum": "fa85d2d14d4289920e4671dead96431add2ee78a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-8rhl0xs2cxfVsqzreYCvs8EwBfn/DhVdqtoLmw19uI3SC5avYX9teCurlErfpPXGmYtMHReGaP2RsLnFvz/lnw==", "signatures": [{"sig": "MEQCIDbuVChfqnbcBoiIR1ag4OeJuTikgqDkxKoRf9xjb5ObAiByEUuZe2+UUNU3KMnCAt+Y7h6QH8QtUOfyTJ2bcgQMPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVBS/CRA9TVsSAnZWagAA3coP/AxLq4OtPKo2w2gTecNZ\n+PmPF6snX52WJyTBHr99UQxVW5ZnCY11m25fs+o0hwp0at6s7SmFdVCCsMti\nv+5Ns8XMTFotHjOB868Zl5Lwdi2XD0WIqxV7tzCAk0bk6LMLx2KsJXQVNEja\np19ukoojJLT6ETQuZwIeg9K2fCu6ho4Hil0Iq8CHsxUwejXhfcrTDq0CNC13\nZUJHT4xvFQKFHio8OB37BjAfKYvnLQeo8tAYR3pfU1Z12tvQQ0Vw2E64lY07\nt8aNePxvi8Fno4C+EDm5Dy/QSkjQOJdOpJjz3OP8eWgZPXX1EA7PPfp0jpvE\nQRox/tWpaB7iwL+2WsgueYkAGydNFXfFCsq7dPXjtNOdQ2L3n6V/zJRRdyFE\nlGiquXVMWEkPfqyfI6/QmORO3OfbcyoUsOGtYX8v+Be9X1Nml9fiK1PYIYXu\nXy5Io4qA6s07aLmYcbinHZDaLEmdvc2RW6pELiED4hNu0X95l17juB63gYGv\nDf3XvxLV/kFkmCdCLZetFUt3U6vnzX3+h161jj7oDtmCl2a9fKXomrfd4rV3\nGZXzBVDBsbYs9XyBFe0riBdeT3elFttIjF89F0dOIfKG3kU6VYe43/bizNI8\n7x4F5H67VuIasFApMib5yBuOYTmka2L46u6098n74SW7ml30Is2nipJ3Y2+n\nw14P\r\n=jBwp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.3": {"name": "readdirp", "version": "3.1.3", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "~6.1.3", "rimraf": "^2.6.3", "dtslint": "^0.9.8", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "dist": {"shasum": "d6e011ed5b9240a92f08651eeb40f7942ceb6cc1", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.1.3.tgz", "fileCount": 5, "integrity": "sha512-ZOsfTGkjO2kqeR5Mzr5RYDbTGYneSkdNKX2fOX2P5jF7vMrd/GNnIAUtDldeHHumHUCQ3V05YfWUdxMPAsRu9Q==", "signatures": [{"sig": "MEQCIGgAlolvCKAOCjukX5RQNGlJxWAjXYQATf+or+JTjIcbAiBqsu+ON+PmbMwIVwGW0UMtiuyGodmWK6ViG6OzxfU4pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkroLCRA9TVsSAnZWagAAquUP/0hQ+/OqqENhiwzMbKmz\nrFf6svv8qngzG5SOyQaCRaQJFsAWkdjYFLxzMFC48j5vUacwEExjZ5DlJUkW\nXOTzXEmKL0pL7H97+65xOTph+9R2Ev8PpQg7TvCS8vzeauN7O0P9zbLUUXGf\nxatc3+TuAa6qq/ib9q8O0jPg97slaCdJgbXWBQPDqZjt+qEIKC92A7E+AgJl\nq9Qsuuz5lcPV/r6giF2W25eS4i/j+cKcjnDMYPwHxKVmAArS1/WYp6yt7JEG\nlr2uOXkxGcvlU7nAckm6SqyZ0I4mSnq039AYo2tC6sZ1JKib36ui+eg3OaRr\nSMpPIMQFTSheEA2eQqsu78Hph9cIjLxOIZ7IMTyykjJfCIXkAWinWbMs3W4R\n2FzohLzI7DXlVvyseRfI8fKIjomvrYgHjK/In7egOYYomaGyEEQVIxqITV3C\n6pu6Gitcv2HPegvPSKCLiMmdjby8OjogAT29C59yvB7noci/kQ79YO+6K+7F\nKWGBAbwQFtwZDTnpxrmAE5yOTnZ9GXtiQUbIXqzLuJKemltVATBOAYrNeudV\nn2eWkkRS6E1JhH0AD+VEKJEAoDEYSSIh9Qma1jC4CVuXcjeSkVQO+sPzaT2h\np2YAWd/nT7fGDVpTgOrZBam77iWBI6kEFpHO/yQbzBwV2mVP0QzkHY3mfjrw\nS2k3\r\n=lGM3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.2.0": {"name": "readdirp", "version": "3.2.0", "dependencies": {"picomatch": "^2.0.4"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "~6.1.3", "rimraf": "^2.6.3", "dtslint": "^0.9.8", "@types/chai": "^4.1", "@types/node": "^12", "chai-subset": "^1.6", "@types/mocha": "^5.2"}, "dist": {"shasum": "c30c33352b12c96dfb4b895421a49fd5a9593839", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.2.0.tgz", "fileCount": 5, "integrity": "sha512-crk4Qu3pmXwgxdSgGhgA/eXiJAPQiX4GMOZZMXnqKxHX7TaoL+3gQVo/WeuAiogr07DpnfjIMpXXa+PAIvwPGQ==", "signatures": [{"sig": "MEYCIQC0f1hFI2zsKuvwMSFobumQ2/g+8ZJNpDI3XwXEQnoawgIhAKYi2c7cjmCbKeRDxG1rY9fqoLSpLcworV4d7DY1Xr5L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdo7ulCRA9TVsSAnZWagAAeZYP/0xjXDuMmT+ANQyuUn+j\nK3sUR1/5mmQhRQbF8RF9NWSPftb665Jtwd6f+sx8DFlGEsx288+VZ6gwEkiE\nC5jIyi19CF33MOAfF6QJFNoMlnmaA8+4upLqCE1DlyrYp/p22QzvEnAEzQ9t\nQ68+y/k6bdTxBLSWiRhNt1fScK28hiEYRN/bUxSwiDABpYdYxEhCnl4S/9/C\nUrPoQL1KzypR0VqfEnODPmXgW43VtGn3Ry0O/ACA6aW9GjNSu5BG2uefbMpg\n8xqFdSUbwvMnjCcHB88+HrPkY/Xth2ALUiiPVfDQ7WiWcNJ3W+pmHWq3NX+Q\nJvlnjHiahWxb/iiYdR1V81WRxU3LUeRopRCb5Ux13YAWomlnwvLA3XyhpETl\nOGrLTx3nSE6rYQpRDjQvBZQUnnySYPCmKgntxP7tiguVI74pLc8QTP38MucJ\nhTkdvK/griwUwv+/qNIGaQ9gsas1z5bbO0igUB7iVgokRq6MtFtok8pap4Hd\naue/NIOeOoREhTyx4IH3FmgRWFpAGfo7ckRnd912vVSZRzgbZWPzhBdBYPdl\n/C+o+61W0DmF/keGaJY2S54u8itySqn94AQQG0kkxVeCMxQ8jt45OeNDq62T\nVZdr+MOVtfyZ3tP6wTuSHITZjcbK3X3/ZkrIy43AGzuvt8n6XPIvVhjfG8GQ\n3ohn\r\n=BUQ1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.3.0": {"name": "readdirp", "version": "3.3.0", "dependencies": {"picomatch": "^2.0.7"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2", "mocha": "^6.2.2", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "^2.0.0", "@types/node": "^12", "chai-subset": "^1.6"}, "dist": {"shasum": "984458d13a1e42e2e9f5841b129e162f369aff17", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.3.0.tgz", "fileCount": 5, "integrity": "sha512-zz0pAkSPOXXm1viEwygWIPSPkcBYjW1xU5j/JBh5t9bGCJwa6f9+BJa6VaB2g+b55yVrmXzqkyLf4xaWYM0IkQ==", "signatures": [{"sig": "MEQCIApdIXf7nM60b1/ogfqByVIOuilU741C6rdKlJ7oIzolAiB3jOIZvzDEEntULnaw8o9O/Rl7FPfJMDMfE7m09qCjNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6jPnCRA9TVsSAnZWagAA1sYP/R1EmbDs2cTv7FR4DEy1\nbPQGtaKwDHT8kaNEj8L1SuyEs8KxzfoQbOejh2gURoMSLXz3QTIOhT/3rEP8\n/FUrvPaK9NSFUBvbjQzXOwiz1gfsdPhpYlhnhbM5Fl8fyUqpexpwGV0CXBVv\n6aw0vDGb6AccI9rLE6ClK3Kx3+kRXtQJSTMxD5HgoofXV5ZQEtamoWK/XSyN\nwyeKelyVYK+ADvNn7T7kbilKrZ6j3LSGx107/N8liQvxhR1AsN9nzFZWvXsl\nH+UKCkT66YTPUFnr3BkpsEt4BaHn61J1KiGIMxfwTbV636WMFAqBeVCNnG+Q\nNAMQZFS72z60Ck4KqqBdJWSNFq3twyt2750fmPJDx8cm16yuVYNGFlUVDi51\nqbDHw8bO00T25/QzUpKNAi1I9UT+jVat1YB8PNQpKdQN5yLgdPHYoNZ0l751\nneRghmEPogyrJZpD3qIHj83Rhl/lahNuPiDp3onuhow3SPFgALG2ogWaKFX4\nUr4EC/bQd5YyNnH3SOvy3mwK6LFS0NQDoDW5LYHNsvpS/9oVz5Gc2jDeV7M5\nTwj2ndLUTOQgJrZT57M/XDFAKzKVytcIFYnv7TZVBoUlK93wvHCC6DTtIQ/4\npkkl+19PzJrF6uY8xtvupapocUWS8c2GxvrcOvs+FCrpg2a9dOue3od0STuL\nz5Ts\r\n=A6up\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}}, "3.4.0": {"name": "readdirp", "version": "3.4.0", "dependencies": {"picomatch": "^2.2.1"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.1.1", "eslint": "^6.6.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "@types/node": "^13", "chai-subset": "^1.6"}, "dist": {"shasum": "9fdccdf9e9155805449221ac645e8303ab5b9ada", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.4.0.tgz", "fileCount": 5, "integrity": "sha512-0xe001vZBnJEK+uKcj8qOhyAKPzIT+gStxWr3LCB0DwcXR5NZJ3IaC+yGnHCYzB/S7ov3m3EEbZI2zeNvX+hGQ==", "signatures": [{"sig": "MEUCIQDurecjRlyNEtgy0MGyQ1RlsW0tlu6bvB8H8pfoW0DHUwIgIgiZ/JhKVY9TwtAFbCaQlUM2AgYbwk0tMcABUEyV9Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJec0KVCRA9TVsSAnZWagAALVkQAJ+3qhvu4xMlqtBj+hWj\n4tQbLIy98JPnVOUCzF09Xd3B4DHHarcbFkMIf20k9jQNtmmWmeQC5ehKXwLA\n7DiUP4fZnIsDK0MHw40LUx4NrQKe+K8s+h1Os7wcmspnAEF5mwKppcI7NHDW\nU0Gvs7txyqG5Rni+BB5EZgyibbUYBrbtxRQdmURp30cbFyXhX6GG9BqOACUp\n9dYsCXmE/uS41lDnbnIsQTpYOqS1jqyzfqoyMmMrY9nvLM/r9jkXJVXzSw61\nklzG7CzAE4m/cIUgh4PQlDqCKMYmvxCss4kHOkMbHhVXuGnme6HQWT2r61dz\nKo9KqePIcF/aZnXMSuZxibsTyBO2kAiZwAXhslX+alGJ9JUGZFKSl3tSKnph\njtoy/TMY8Uy8egAfzrNVuhsi/aH3mKJpcUIkU+XCvw9QytGPu7XO9k50x5eM\nr7ODEjfjn+ND9IdBL+cNzwJJUFnQU/RcYBUdPiqr7n1uFwz0yJBOVb92eR6Y\nvGU0Fy6GqkY52eHouvOz5t+TShqKk76TJ+B5YCcu96FdTm7gPQPgz0N07kG4\nIO/s5XXSuZ1ah4yFKEX/9bnCewzi5FB8EveL7h6NF46hyBUmGTiYeo8uCiO3\ns1T+mOTTJysOeL/pCbHeKwRkXLeLXZSnRE2iwinVkTENStU2CDpMk471m0Ot\nnuTk\r\n=z2Qn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}}, "3.5.0": {"name": "readdirp", "version": "3.5.0", "dependencies": {"picomatch": "^2.2.1"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.1.1", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "typescript": "^4.0.3", "@types/node": "^14", "chai-subset": "^1.6"}, "dist": {"shasum": "9ba74c019b15d365278d2e91bb8c48d7b4d42c9e", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.5.0.tgz", "fileCount": 5, "integrity": "sha512-cMhu7c/8rdhkHXWsY+osBhfSy0JikwpHK/5+imo+LpeasTF8ouErHrlYkwT0++njiyuDvc7OFY5T3ukvZ8qmFQ==", "signatures": [{"sig": "MEUCIQCV6ACDyNjInrf9FDk07zDzd+qoFtj1vmYQfOyIcVXQKgIgZU9M93wfiSXM0PiAr0sLiUPIgr6yej9/bR4c61x1iiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhX7WCRA9TVsSAnZWagAAj14P/39OW6eSQNZcF65ocDKQ\nsbcDmKYBabrh8A9UGks3EQimH2VhukrNfXaSYeCB8lb4WIIev7CTpC5s4hAE\n3sCKD5aJ5zY/LUcj66+tnU8qpdR8PnwgAW3hQgKTp1sin/jk5Oh8PIFTdY5j\nro0v4HWqgu81khLwBoT20EVi2Y6ijRa5CGLR1uiBDjVDywgtM64kV0ItF9iZ\ntOtQjvap42PiXp7uD8Hxy/Dbjsrr8jxb0IuPV31XQX0Nlj+RlEKfyrEtXYzO\n5F5FwERSj+SgjcVTgC3F9WNN5WIWrtbFvnRWMxbBVfRrCqz2cYsbHAlqOI+7\nI6OJGGnP1e0bmDTd6g1ORyWH25E5C5rv2aTajEY9fWDrAFKUsAzdLShtYFfF\nNqF2/xKHXkBSXfgq7MY01BF6DnxtW3TsUCiunLuD7AUsZJf+5h3Lg87unkTs\nbYelOYYqs1sk8o7kEWbloSXEm/O/Ao7X3sNc4c/QD2pWI3asVBUFJYOjAS9n\njzzBOUh91zoMHJVp8p5u7fMA5KHccHKi5aF3RsATgTTNYmbUxHJTraVwjOtr\nHwdTkwUZBBz0TIM+3k0l4W574MOrisePGt9oF+tWkREM98jsRMQYi3uzq2/W\nzxuezMOlC3A7cfrBICNOT0o8ZKnRJ4qsE4AGURVrzT9KMwU0Zq7lDSsYlCYy\n7Tqz\r\n=qceD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}}, "3.6.0": {"name": "readdirp", "version": "3.6.0", "dependencies": {"picomatch": "^2.2.1"}, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2", "mocha": "^7.1.1", "eslint": "^7.0.0", "rimraf": "^3.0.0", "dtslint": "^3.3.0", "typescript": "^4.0.3", "@types/node": "^14", "chai-subset": "^1.6"}, "dist": {"shasum": "74a370bd857116e245b29cc97340cd431a02a6c7", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "fileCount": 5, "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "signatures": [{"sig": "MEUCIAMyqngGcA4dviNNK2hVJNVzbcjS7/I4hmoHq/P2CT5wAiEAr7LAs/ppzAHVkTZMBekYzabKfZANfLt4GaVwiCYfAvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTePnCRA9TVsSAnZWagAAAOMP/3OJB0jCHM9D7hKnWj3o\nrxyOUiiCf5rKrligrEh/BrvH78FvQH+CmP/npTXmuik7IgyWUjgTB1vnoRtG\n4ZKAzNFtJINn7nwS/7VrfeZ1EFsbb1trB1w/iCASDo7uQGU11JULh/eBCjON\ndwH3faladGaoT+sFp7JYBcHSqfRBvCAFhl+eFnDnd/8KQa8PMRpm1dtJNk7J\n2tLwA19yy/3vDp1uhFyH7kmcpPBHl3vvN+zdg9LvyTicXyid6YdxPDMLCJS4\nHZr7btHymUr9hBWrlMuJS+su+6GWBgZ9h3DWayNdymzX3zEnNVI2QFarIOQN\ntxRlAyvd7U5uuo1pIsrAzYyOZ0m14Qj3BD0ee1uVL7OnpijmWwrG+M06o3bE\niqza6VgqX4B+5X0VTJ8cGJgcBD3zYMCGu23a4MJClcb6kOXKMgXQfQ4U0YHz\nouLr5yWgaPvTacQQy0koL4Jakf5CS37BvMUduBMhfcKxUSRxkq7nk52uHSH6\nCqsUmbroKL9RUe1ME+H2EOcHRInGP/HLCeWR5to5GpyI4SM3yUEtK8uFi2jp\n4bdh5xqu2WmpQuMuHwyUqufKgRZpQVvKzkSSjQuxzlAALcNByFoH2mfsu17w\nXZX8Ona42sApzoO/J9J7HDDZcWM92XqqcfFlYrhNzjDcihpyYuGht6h4n5nI\nOz/f\r\n=FJ+9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}}, "4.0.0": {"name": "readdirp", "version": "4.0.0", "devDependencies": {"nyc": "15.0.1", "chai": "4.3.4", "mocha": "10.7.3", "rimraf": "6.0.1", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "@paulmillr/jsbt": "0.2.1"}, "dist": {"shasum": "aa900a6deedb59274f3c616021a89129a00a1196", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-vhk1KSlF6K9P+pvrsz1g5oKkSgczqWhhQHGZLLLrPs0TnnNQTF3twqgTRWwaBcjTGmwwebnVolVpXwLKxBE3Kg==", "signatures": [{"sig": "MEUCIQDteJ8pxhfXL4BJL9YPkEH07tQ2wuKygKpCET4GLJrzLQIgOUEiqDmZ3tX11mgWcQQupuRyuvl4HScMV8VAtCRWyVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52773}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}}, "4.0.1": {"name": "readdirp", "version": "4.0.1", "devDependencies": {"nyc": "15.0.1", "chai": "4.3.4", "mocha": "10.7.3", "rimraf": "6.0.1", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "@paulmillr/jsbt": "0.2.1"}, "dist": {"shasum": "b2fe35f8dca63183cd3b86883ecc8f720ea96ae6", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.0.1.tgz", "fileCount": 12, "integrity": "sha512-GkMg9uOTpIWWKbSsgwb5fA4EavTR+SG/PMPoAY8hkhHfEEY0/vqljY+XHqtDf2cr2IJtoNRDbrrEpZUiZCkYRw==", "signatures": [{"sig": "MEUCIAnN/rxw9dI06fNCSIRkLOUS3ub2PXOvlBSaeBIjtRIeAiEAk1bfD0dDZplywuxMEDeW9fpxvM6WT6gTBwH4GffkxEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 52816}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}}, "4.0.2": {"name": "readdirp", "version": "4.0.2", "devDependencies": {"nyc": "15.0.1", "chai": "4.3.4", "mocha": "10.7.3", "rimraf": "6.0.1", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "@paulmillr/jsbt": "0.2.1"}, "dist": {"shasum": "388fccb8b75665da3abffe2d8f8ed59fe74c230a", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.0.2.tgz", "fileCount": 8, "integrity": "sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==", "signatures": [{"sig": "MEUCIAvq5GJPHpgS+otEhGDL7E1gh6OqfXGJH3boDg7KG5bGAiEAxCb90QplQU9xdL7IJSygVC1VFRHE/5xFVsP9kKLezQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 32182}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}}, "4.1.0": {"name": "readdirp", "version": "4.1.0", "devDependencies": {"c8": "10.1.3", "chai": "4.3.4", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "micro-should": "0.4.0", "@paulmillr/jsbt": "0.2.1"}, "dist": {"shasum": "84f8c468aebc665a83fc423c332894f35e50db49", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.0.tgz", "fileCount": 8, "integrity": "sha512-4+hHiVsxlm4OVSNFpAIrOGyGeG9kNLGcLMqvSGL5Rj2NOYBDQiQ6lJRViwAZ80i8SNbY8kCpdjgJy5PNALARew==", "signatures": [{"sig": "MEQCIBNjKIz8PYtETLPe4uO8HgvNBWwnwgzIrhh9KOVn1wNOAiAfiYaIK//a/CBupD6denEKRtrBucI7MTlHAqagJBl1rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36253}, "engines": {"node": ">= 14.18.0"}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}}, "4.1.1": {"name": "readdirp", "version": "4.1.1", "devDependencies": {"c8": "10.1.3", "chai": "4.3.4", "prettier": "3.1.1", "typescript": "5.5.2", "@types/node": "20.14.8", "chai-subset": "1.6.0", "micro-should": "0.4.0", "@paulmillr/jsbt": "0.2.1"}, "dist": {"shasum": "bd115327129672dc47f87408f05df9bd9ca3ef55", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.1.tgz", "fileCount": 8, "integrity": "sha512-h80JrZu/MHUZCyHu5ciuoI0+WxsCxzxJTILn6Fs8rxSnFPh+UVHYfeIxK1nVGugMqkfC4vJcBOYbkfkwYK0+gw==", "signatures": [{"sig": "MEQCIBm4aKY80a6GiV1tSaFs4tVwGkN0ycxy9tGglski8hAfAiAm02Euo2CfXdBkYSkvkFz3NalIyRPm9UvKQEhyLb+C7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36253}, "engines": {"node": ">= 14.18.0"}, "funding": {"url": "https://paulmillr.com/funding/", "type": "individual"}}, "4.1.2": {"name": "readdirp", "version": "4.1.2", "devDependencies": {"@paulmillr/jsbt": "0.3.1", "@types/node": "20.14.8", "c8": "10.1.3", "chai": "4.3.4", "chai-subset": "1.6.0", "micro-should": "0.5.0", "prettier": "3.1.1", "typescript": "5.5.2"}, "dist": {"integrity": "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==", "shasum": "eb85801435fbf2a7ee58f19e0921b068fc69948d", "tarball": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz", "fileCount": 8, "unpackedSize": 36127, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/readdirp@4.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGjf5FoFuZxfJj6XC2bxY6tndvl8QUc99XNovpnGRE+9AiACxQyKUU0lPvL049n5J5BBw0BehmSci/LuWdQ8GT35uA=="}]}, "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}}, "modified": "2025-02-14T17:27:22.424Z"}