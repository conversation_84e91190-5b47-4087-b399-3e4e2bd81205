{"_id": "domelementtype", "_rev": "32-dbf733e089f3111dcda4d777086c0897", "name": "domelementtype", "description": "all the types of nodes in htmlparser2's dom", "dist-tags": {"latest": "2.3.0"}, "versions": {"1.0.0": {"name": "domelementtype", "version": "1.0.0", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domelementtype@1.0.0", "dist": {"shasum": "bdfe35bfeacb8ee61b19abaf473883f6b7227aca", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.0.0.tgz", "integrity": "sha512-SvYc2cmxJwtg1BCgacD1YA8hh6BPcTupLJt4dRHkk7tSUeiEvDCct1+KQx99upAntvL/YFTT7oOXAiYuhvDmWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAihBGSjCbNbvHgIjpCYB4A3fkdpcdYDpzNoDxgzDDzzAiAHbfOokiE262mpX40tEgORxKIlIf9EFuAZ2lCBqeh+AQ=="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "domelementtype", "version": "1.0.1", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domelementtype@1.0.1", "dist": {"shasum": "70150a1bc6336298b0d6c4cbac27dcb66e06d86f", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.0.1.tgz", "integrity": "sha512-4C7fZI0XVwjj8OVrAupCr75XoCYB8JgjTAtgboZhV/Xt2b+XUEeNlfun05qHcs2wdJy+CPcT67dAixHVtpQcPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICS63NU2KtLGlqnBH8H7g+qCSzZdRTXlAUkaFp+Nz1SDAiEAq/05bK21RbiNcIcqxJndpQaJ+VdUHEEVGgURm7RauPk="}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "domelementtype", "version": "1.1.0", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domelementtype@1.1.0", "dist": {"shasum": "e200c6b5a2f67cf5296559647bf726d63714f5be", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.0.tgz", "integrity": "sha512-GCPx8oUhJsrxpOOBRFGmOTf/WY0doa0LjGmZVWJdfN/ZFDhDlSKKU/csbDaA9wTzfuzhgmJ/DtvMrlY8BSDU+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDZSqnIZUCaooJcIj+ogOHjdfi92g1X0oQ8bwAIZX9DoAiBZ1NYK7iDpUwCPx86w+Lj9c0Wn7aALMSuNLDBKvT8fZw=="}]}, "_npmVersion": "1.1.69", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "domelementtype", "version": "1.1.1", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domelementtype@1.1.1", "dist": {"shasum": "7887acbda7614bb0a3dbe1b5e394f77a8ed297cf", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.1.tgz", "integrity": "sha512-2lzWJZduJDR6nNqm9e0IcwYwIsbHXKUKagesDVzpBs8K5FvrUmO7IYSXFsKhBqY2avQzeuQHwznPjs4Lz3VOEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlIjPbJKFPa8Oy2N8+Th5a+bZBimKG/mQzteKIguojHwIhAKhel5yEh9mpIqltk78mdMtR6Iwk41VkZmnPBGgwxmyF"}]}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}}, "1.1.2": {"name": "domelementtype", "version": "1.1.2", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "gitHead": "062ff89a17121c0a4f67ecb534f1677fbb50501a", "bugs": {"url": "https://github.com/FB55/domelementtype/issues"}, "homepage": "https://github.com/FB55/domelementtype", "_id": "domelementtype@1.1.2", "scripts": {}, "_shasum": "795897af1f24cf2f4d287770698210bb0e826b7a", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.32", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "795897af1f24cf2f4d287770698210bb0e826b7a", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.2.tgz", "integrity": "sha512-5PSq3lM+Z2j/8Fybl1vs985eCTFMWY4sWO4jTMsDVkeVnkclYWsjTt5g+wOpBuFejSYGj7aCBnqWmHVOhiYoug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD71sd3wRRbECbR0vbhSARGipMs4wf7zf3W66FXsQSiHAIhAPlEFXIqHszOZRJvSkKga8j+f/IbJS3uA4JL56UbK9o7"}]}, "directories": {}}, "1.1.3": {"name": "domelementtype", "version": "1.1.3", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "gitHead": "012a97a1d38737e096de2045b2b5f28768d8187e", "bugs": {"url": "https://github.com/FB55/domelementtype/issues"}, "homepage": "https://github.com/FB55/domelementtype", "_id": "domelementtype@1.1.3", "scripts": {}, "_shasum": "bd28773e2642881aec51544924299c5cd822185b", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.32", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "bd28773e2642881aec51544924299c5cd822185b", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.3.tgz", "integrity": "sha512-zEvAAsFY0DeHkrqWBRkSsmgaE7yADgpez40JUFjISb+uzSinl2F6QbG4lMEBE4P06gCGF6VnsykmbNgu7ZIHzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzgat/rfjhTtixhkx+TJh6ClzK8NAor5TCkELcnNCZpAiBZvUVIdnkCpyDn+blXD1VW1S9u0WtXoN2KqjZWUHaYAg=="}]}, "directories": {}}, "1.2.0": {"name": "domelementtype", "version": "1.2.0", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "gitHead": "440f8830c8be5013de62363461f1149c7b419e72", "bugs": {"url": "https://github.com/FB55/domelementtype/issues"}, "homepage": "https://github.com/FB55/domelementtype", "_id": "domelementtype@1.2.0", "scripts": {}, "_shasum": "03d93b0a0ea6ed3fb19f8dcc6ab9fb614720eb6c", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.4.2", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "03d93b0a0ea6ed3fb19f8dcc6ab9fb614720eb6c", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.2.0.tgz", "integrity": "sha512-G39YrPd5BapeSkaKAEzsjTVAppLndIC47GqdOR3Ta7FIXUckDHk8H3W0yQOTgoYgtKjEFg5CRkhdxgc8awY3Og==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBwoGMOXVaxgU7C5OVfmbtdV1Q1N6r2n0GE5fW6ucea/AiA3M891+gFoy0UG3knlLNBfRZLl6OdACb2YLinL06YlMA=="}]}, "directories": {}}, "1.3.0": {"name": "domelementtype", "version": "1.3.0", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/FB55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "gitHead": "2a95eed4c829ef479a88984d117cb5f4b379e6e8", "bugs": {"url": "https://github.com/FB55/domelementtype/issues"}, "homepage": "https://github.com/FB55/domelementtype", "_id": "domelementtype@1.3.0", "scripts": {}, "_shasum": "b17aed82e8ab59e52dd9c19b1756e0fc187204c2", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.4.2", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "b17aed82e8ab59e52dd9c19b1756e0fc187204c2", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.0.tgz", "integrity": "sha512-hhqPxYi0xK5i9fBMHEgWFxicJy62e5nxy0NdnjGE+DqovMcUsUbIPSkBzZ2O6PwYuwNGTf7bh/DMKmMdATSsTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGP0NPdMbFCVW12CnAS/FoVDDjRJDN5AQwvN6h9XesEHAiEAjI8r4UcNIMSf0Fd/7dXxUp9h6X038km9mCV+yuFIiY8="}]}, "directories": {}, "deprecated": "update to domelementtype@1.3.1"}, "1.2.1": {"name": "domelementtype", "version": "1.2.1", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "gitHead": "e34c657d2257df46ddf568cfee7cc83b7ae7eee2", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@1.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SQVCLFS2E7G5CRCMdn6K9bIhRj1bS6QBWZfF0TUPh4V/BbqrQ619IdSS3/izn0FZ+9l+uODzaZjb08fjOfablA==", "shasum": "578558ef23befac043a1abb0db07635509393479", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.2.1.tgz", "fileCount": 4, "unpackedSize": 2072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzQ58CRA9TVsSAnZWagAAJFsQAKBaqf749TplqQVcky9A\nrOzfwJ3z6NtnFAZcZr0ub5uLtWB8Emr9MwyOZHS1+2ixDXPB5V6ubXfG/Qr0\n53edaBBRG/vZivVJz8lv5QuaREr3i3hV/oYmgGVgduwx5KJq5zMefOkXWvYt\nNR+N7cyELsKLOuWbM4v0U0t9FXBtUYmGlqazF4IBRr/UoM5VaFQqhnmZ5gE4\nNEiZ48uurCeKWlbo2qs8omGOuzsQndVKUdpAE0iWMyo3K/e27pMM23rSYXqb\nQP+mzrU962/nLXT4wC/BDYwjFbuZn2LCwfPf51Jnk3lf6hzOX6qhRpe50xZw\npXkRLBNTbwriBN6PRY//vV5QWrd1ErDkfNc3+guiPgaTh2oneX78hoU5aAcl\ndiq7xDXGXWBpeLw4vde3hwTEK/NRdG9h7vu4ebxdExu4W0JkMUYoyU9VKyoG\nuERYzoguMsypVJOPy3ZO/4wWChrE3/286LTn/Dq+seqiA+2/mtKjn3Jcw8yK\nspTyF62Yeh3Rq78bE3/jXD7EsxqJFJyNV9sEr38zqJBTwksxx3IPVNIWVIVY\nwGLV16qzTQGhzCIeGM7FBaRgOaC4JZch37zn9ftiym6/HRxVSj4foLuEcYET\nZgHGWh7/ljoN8E68CEat+RQsv4T5Ifj11dRwTJbtqjTPeHyWEpDhqr4lPSSU\nmGjC\r\n=U04y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7Oe8uIG6+kyOn2553er6y9HBfi5qY2YytzKwZxwVtcAiEA5A41eI6QsXJvJf5V3Mf4eK7omkUv9lGJl4XpW2hH/EY="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_1.2.1_1540165243735_0.9041202397017374"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "domelementtype", "version": "1.3.1", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "gitHead": "19b2491101a4de3679b59db8eb7fdd9aa0fbc60b", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@1.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.3.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w==", "shasum": "d048c44b37b0d10a7f2a3d5fee3f4333d790481f", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz", "fileCount": 4, "unpackedSize": 2072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCOuGCRA9TVsSAnZWagAAyyEP/i0IsOJYtr7ACAsPru/T\nqUkPekzIRjgNhQj97Bx/qc1uV23hvvcIACqY8c7QX0X3xwxtrmC43TP5+eGD\neU1BasHGTPtdEFHGAd4hGxh02Mh+ABWPnxo3RTWBaMFSq72qqAuwr2RS+Cia\nEsI5zme0CdGfpnBUF2pAYChwbqPpLnVTGNC3HlakmV57h6OwzfrlyXG1SkhT\nYItha7gNbIv7AuydH44S3AHyyWsw4BQlWjmC+cuasfvrEVsarocYUqTLHWLO\nWpMu/NP2Jct4H8nQzNWCYSI5Hu5F7GDd3wmqBkPZ2Q/GzjQx5tQP7C6ynwGA\n+wA9jVw21xVVzXfRu8d4PBZURppnYSCph8AraccelSYNoqwFleFlP/6EE32b\nj0TLsR4yAQFOy7zqHTmh4ew6mkc7mJGg+lbOsAYSVl4GaVqBiVwqj0zW1CGX\nwj9MSmqBfmM7NIp4lb8oLk1/5D1mtpS5Ng4tyfCE5HK5XGsdVAYKqjamAsPY\n4/y7ZAE8Zb5rglkTJZk4TY8/+s6Wu8IAYyuTD25mLaRu1Z4Kx6uPnZXDZKKw\n2JjTszTpinpNQq50VuhJnQcEyEyNJBra/4aSKgay8LfVqXNUBl+sVBwPJ6l5\nN0e8Vna3pGbeizO4QKm/7mxZnIKAVMZHi7LZ2PFh3LJ0XHIeMuYOsrEF3SKv\ngm4Y\r\n=pGU3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEFi/hwencpBIc9T8amQm5Vc2f+7bPsxkWMynppWQ/QgIgNXBLns/r/rFg2lveoF8LlHX2MasRVXcDTVP1DBpEShU="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_1.3.1_1544088453224_0.16246904901637227"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "domelementtype", "version": "2.0.0", "description": "all the types of nodes in htmlparser2's dom", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint **/*.ts", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "typescript": "^3.5.3"}, "gitHead": "14fa78d35f2708fac49b9268bf2925ee1597dda7", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@2.0.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-D/ZjX/OhhFSSM8RFrNoVhWBkaIK9YYoigoIoFmPJlouf6lee15CPe0A5vj7n6rIgXXuJxOr/JRmeViCnQC3gzg==", "shasum": "4a4d74a2a478923cb3a6db6755023e9201f21ff2", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.0.0.tgz", "fileCount": 6, "unpackedSize": 4566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQI0lCRA9TVsSAnZWagAAqWoP/33p9l38Pl6KSUadwG2i\nFKFYsB2Sn/RJ+FiaT1tfR19ufGfT8Ox3q8BM2Wea3oT8yTEkwDS8pDQNjdtK\nc0jDGbOGNbNvvmFycGHAzYHaiC2+VUZLmxREJ6UXttxFUFeElgRKDsovjuZV\n3Y6GfyabnSVLXiRJIxdMMLFgeZglCOE/XmFQI2v3RBPs4EbpfUwRQLi94nQG\nTvhjCyMZ/s4+pukKLNANLpTJuMcp6nNVhPYkEJkxdSZmn6z4ynoAyBCh0nx0\nmHZJcYOjIT+Qyc5F4QSbydCy6YhZFrGwticlrvj1IWkrIePMSTxlA1Jwvv1d\nSjwaAlz/IYHOT1nS6XmIIHBJQynNvJSrjpMWnD8ILHuHsyHfBBAVfrvkYwyk\nehXg5acBIn+rMRKVrgOPDPPTBOkqmJr2opgNzo0cKFonUb2KlavDUyIomweG\nDR6V6b0lbwj3otdNR9/49gdtLgGcY9Jw60dX7c/QSD5hwXIFM3EN81UmVsF2\nXMQU2xkQhbq+90uq3ZIo6WbsB65SyrQ6kqiDpWPdYOUVS45dzk0ELXR6kk+b\n0LW6WRWj/9BL6peimPPiU/yJDVf35kvtlFwW7w6C99JxVc8d8GIXqRGYAxOd\nXwUeZEeODyH/NFpfMCpyGS9knW93pIrmi4/lV1BBw+gzhzhkJ/8BHq3NqTaF\ndLZt\r\n=8p0Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpc4kB9dhuhLATTxLSDyRQcTBvYSYuFVj7if23itxFBAIgEYh4KAO4wzkTTTx1lUkri9aL5wWVeFr0uMAuczgjbjs="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_2.0.0_1564511524080_0.6257425819943703"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "domelementtype", "version": "2.0.1", "description": "all the types of nodes in htmlparser2's dom", "main": "lib/index.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint **/*.ts", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "prettier": "^1.18.2", "typescript": "^3.5.3"}, "gitHead": "e5d7d8a55fa6143334a7de90787abe01a178a351", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@2.0.1", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-5HOHUDsYZWV8FGWN0Njbr/Rn7f/eWSQi1v7+HsUVwXgn8nWWlL64zKDkS0n8ZmQ3mlWOMuXOnR+7Nx/5tMO5AQ==", "shasum": "1f8bdfe91f5a78063274e803b4bdcedf6e94f94d", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.0.1.tgz", "fileCount": 6, "unpackedSize": 4601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQJk1CRA9TVsSAnZWagAAmSQP/ifYuXaJnDM394+m8yD0\neVsL/hTHpg9bDJ8ydDBradZtkWwcdjKhfPXnYg8cHGbl3pS8dAx83Qwlx3xh\n6xknbuvUNdkEe9udzpYZubys0hqw2zsi61HQVyq977xkvPYCMyswGtUTpNAr\njM4wyiKps6ewOlqDdqwby8iH3fJRyBon6SiX2/ah4/4VrVHJrMXTClX1KEAe\n0AnwmJfgCw7rYWOrc/rlZyrlO586srK8RWTuRQCTivtab6sxQ3glI4jB28tt\nVCWU/xK8oAvQS8gGIYQprV68lOaktLMfffYDCjiYtHJYwD5vnHa9gFI5naLI\n8l+oVhu6/bwXE98etibOoC/90SnwYBGN19j7p6/W4tCP/zpBDgm9eabrmrP3\n0vcbQLZP4l2zYQdjaNSlU6o08Zxrp7UCF4e+xyYfITewwwQNek3kxWEqlxn8\nkj3z94V1Jw9gEKcSUYTOHCXThfDAcU/aJwNpokJMAbhej23n8FTib6bqM3sP\nKR/vxH5z7JPF8zvIVt+8HJhxQxkv7LzNsMdpQH8sh/cS801eK1I1D9OI7qly\nEAa9yeeePNPgl//Fn2/LdNCLcMtsr/maebwMl6qMk1F96AWCwGjzXSDgfvem\n+qvuu1Abz3+GZnxiTh4Ppe14PLzyOVjwSDwsWxyBezUD3EkGcrwArFB8rI+H\noE29\r\n=BDln\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOrk1UHORmD7uizgb4W7GgOd+VA3VLjreGhyGrCF9YsgIhAKSw6hRxiypd7PVYndkf4F/Ts49/jtj+OvW+ujIDN4NE"}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_2.0.1_1564514613068_0.8904122755116697"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "domelementtype", "version": "2.0.2", "description": "all the types of nodes in htmlparser2's dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint src", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "prettier": "^2.1.1", "typescript": "^4.0.2"}, "gitHead": "a819e5684c935a04ddc567442ebf0d69e46f4ee2", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@2.0.2", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-wFwTwCVebUrMgGeAwRL/NhZtHAUyT9n9yg4IMDwf10+6iCMxSkVq9MGCVEH+QZWo1nNidy8kNvwmv4zWHDTqvA==", "shasum": "f3b6e549201e46f588b59463dd77187131fe6971", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.0.2.tgz", "fileCount": 6, "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXoXDCRA9TVsSAnZWagAA+DYP+gOhpOYk6m21r6vAeglX\nN/sp0VE2ujJmG2207JPQrHIqZbd2v3iynnAQi6yPmGTaXDooTP4Dgsw8vsPo\ni4YA4BiCE0fT7NpYkkDeX1+uJN9Pl7nQyEL+jClNLdJ0OFJCAvBf4iJNsuyw\nqX00qA5HomA9xC+cM7mEeqXuqmzkM/Tsb86Fipl6W3t78YIjJXzfDLl2nXyN\nkD5YY2Pnbg0I2iifhvm0e6MgGS9qicl6Z0DIpbrSV3oKQARGHQxR/T0Eo6Xm\no++WPP81e1AbaYjXWIcrxPTx0McTaEjqktzRaR67Q+/DrzSOToQEaGyGcJyR\nOFxGh1saXiTLH7TQDuyqhgywcXf3IJivxgxvzvFg7No32C8vX4VSOZDRIMJt\n5PDqOIX9o2RXjjj0g6f3+On+cMFzpeHU51BqwrCjfJN4OBbWjDUioAY2GKvF\n3KXr1fnL2Yj4bNPP4CEQBgdgvSV2dHwhvkUQ+5ZfoZ6NRvTurhKIyPz0axa8\nGuFQyMyFT8ySOz17HJEnUC/DF4n9wdpOjPUiKpZ1LkEc0iV0fwaESgKuA1vj\nDQ7MU1U44w0hlAO31NNAL5po8DBSFyYRx5KNy09fAmWbDKakcKcIPA+pi+0A\nAvwNm+OEGHKDKyBU95ObDhwvPn2Xr3aoCTRxSBoxFiAWtGqwwJBJJC8Yw3i9\nt7Ux\r\n=x2mP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmiKRilD33rQKu/FNfo7ghmtprf6Gtu1oJfbhPQ96U6QIhAK7B4Oa99MIdQAduTPhO+I8E6+EVzNfEFiQkzOiU8NLb"}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_2.0.2_1600030146627_0.3544962487969294"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "domelementtype", "version": "2.1.0", "description": "all the types of nodes in htmlparser2's dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint src", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "prettier": "^2.1.1", "typescript": "^4.0.2"}, "gitHead": "71814099d360d9ced25fadcadd44acf22d827455", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@2.1.0", "_nodeVersion": "15.3.0", "_npmVersion": "7.0.14", "dist": {"integrity": "sha512-LsTgx/L5VpD+Q8lmsXSHW2WpA+eBlZ9HPf3erD1IoPF00/3JKHZ3BknUVA2QGDNu69ZNmyFmCWBSO45XjYKC5w==", "shasum": "a851c080a6d1c3d94344aed151d99f669edf585e", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.1.0.tgz", "fileCount": 6, "unpackedSize": 6066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxttmCRA9TVsSAnZWagAAb/QP/1WRYeTxcE0Rhun4d6VB\nhiRHrt4+0DAL6d/ubMD2IB7m3vV7379AqnVr4nTD6aPjZ3DdWwl5eR93itEQ\nSyJ8MDAQMJ3xpZo/y+PUIB3RAmXvZPEmiwL1FERam8CyG6ednUjOBk5hcT+D\niGpX860lT5Cghkm287E9TDBUz9mZ+o/3cVSesP34tcMArt+i8kIq3pNp0og2\nE+qv3ViE2wKnWHcRzKCudLvCAZ+DQn76EbTwfh2Hy8pH3udKvhPFuMOCcUM+\nxZlH1sch04XQzIwQ9TiG38x85V0dI4ATZbtxMVOt7Licwj4A5+LrHr7JgsM9\nLWhYR1nJcEiP46BEtutQdOzf+O8sBTmsmHD19lciNgAhrMeKnytg7CvNBJAa\ngXJ3e1inVrtntakHauEh68Wn0fuoULmh9SpZRWU5YD8nkEI29xjR+DU98U6C\nCcUGhozG4gO2Qjx7xOv/ha3gAabran6oeZk1h8fs55LCho4S4zUc2lXtWhLg\nc/EFfw/QaPXpGM66UEw4XZaF762jb+qXrJbGBxyDiFhKX9fU8eeQ+Fz9XFMu\nwRM7j3Y+8VmpP01ohWJVpBOzV+cQfa6QTunCEPxP4hlS+8NYqwdKZE+n+7vG\n1F8j6XD3AJpOC4n/J9T8wemhQbUq37bJOG2RrbzLV6odPrwHtFOr1EePUah0\nIYRu\r\n=BmCB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB0N0AqNRxwybxl1u9R2IfMX2mx1cQYlRlSTB3ltRrusAiAgA6Pdmwh97iRHOZfTjeNuuVhT5KfVB0STrLVdK5NNtA=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_2.1.0_1606867814333_0.7827205238386654"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "domelementtype", "version": "2.2.0", "description": "all the types of nodes in htmlparser2's dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint src", "format": "prettier --write **/*.{ts,json,md}", "build": "tsc", "prepare": "npm run build"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "prettier": "^2.1.1", "typescript": "^4.0.2"}, "gitHead": "ccf5cf830b57c8a8da865cd992366f3ab8d675fd", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@2.2.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-DtBMo82pv1dFtUmHyr48beiuq792Sxohr+8Hm9zoxklYPfa6n0Z3Byjj2IV7bmr2IyqClnqEQhfgHJJ5QF0R5A==", "shasum": "9a0b6c2782ed6a1c7323d42267183df9bd8b1d57", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.2.0.tgz", "fileCount": 6, "unpackedSize": 6858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZgLlCRA9TVsSAnZWagAAqZIP/2cyNp5CWzmjtolx6pXf\nvQpf+bPwZEIceso01FAnY6efTi1pN2gB5uIYfow/ODeRIN4b6bVVYTs5gnAF\nXJEyaQAWnSXq+lF2CQLYHzk4doNe2g4AsmLIoKr/jRRpEtKH8ud6CHy4Ir9r\njEfkfsvZSg5viaXBH+uYOGmTUevEd4o5/4hkeOPpG4WXC8DSpy873n3zBUOT\nyUxwMV3E8dXpXx6yZVtavkYGN3vEgSs8Aa9las/KB9iKTQsr51FvwA1eyUps\nFRPH8jc3HkKDk5acYOhEiPOW78rRD4XVhf1BFpouN+z9pzPEgyJPvVMgAI0R\nGeYmjXerUzvmeZQlnfQgHZzMCYNoFjmaMXgF8tQMliTrBSl2lrcEOLauBk06\nJQ3Vi72ovLBenVo3glPtdvai0wYcDb648PAxenjDBC/AjMbKbfT6dwKLpzC+\n3IQNk40XVuBxvYtERebsLRq7aZpeydNkJ5f8qrYZ6J6rn1WTQ+ZdJ8lceOPO\n9d68vjoWSBMt0XwzRjI4KFb9pDw/R6jbYabuSSSmQdiZpOOnnP0fKUkkRthh\nSTM7va3FXc8BKlwvpLEPwBDhEOwqossXBiWRe3y1w3l6O4BObDpc6URiYB9F\n4avw1rOLkfdJ20n0PJPkYBK/D6gov1ZASSFDnWHvo2CZbVNvIjkWRd2M+kJk\nhuC6\r\n=7vnh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDTGQnkpnU5JyfjwiQris6j469xo7JuXuAtxWoKWLiZrAiEAxCMklW2jNajx/eH5pmqS79GjPqXH2bY1UpqxR2odifs="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_2.2.0_1617298148799_0.5729971109996348"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "domelementtype", "version": "2.3.0", "description": "all the types of nodes in htmlparser2's dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "sideEffects": false, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "keywords": ["dom", "element", "types", "htmlparser2"], "scripts": {"test": "npm run lint && prettier --check **/*.{ts,json,md}", "lint": "eslint src", "format": "prettier --write **/*.{ts,json,md}", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "prettier": {"tabWidth": 4}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.6.2", "typescript": "^4.6.3"}, "gitHead": "687e8625885a3656d44c41e7640c86925417d91f", "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "homepage": "https://github.com/fb55/domelementtype#readme", "_id": "domelementtype@2.3.0", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==", "shasum": "5c45e8e869952626331d7aab326d01daf65d589d", "tarball": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "fileCount": 10, "unpackedSize": 11359, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2uaqgv6jh8+WxpNj5scGHlwrHNwC9w/uLtM6LjPlErgIgFYRIcONVZDcunOhww6tppCxLBQ7UtS3RhOA3CkmptVo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTs3gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Gw/+MW5sc3nV1DjP8F5It9ia+2ghptGwDvqONLq0KHpfGsUKd90y\r\nfrL1jGcrS1fRZPbSYW14AQRS60wxJiLX8fsMEYUkgcQu5pDVFGurUHYUBM+5\r\n5K8+b47OpIncfOjaGk/Ry0nUn/otCSrlIhEFrIK8G4cjL/5m3ef9ZQkEBCDC\r\n/lcvckLz8J11Loix6fDiW2bGln0QG2wTAoeKvptj4L3epZ2Uu1NKchXoGHdG\r\nxHYx8WFGwPrcOib8ZGWa5sM1Yt06cb85F9KU85URtI7gLxKxWXII8hK/kN5n\r\nP9bYQFc8hNoEokYEvoiDQR7NMlRYmb5s4HvlvdKIYwe7iNMq9dEYA1d3CsxP\r\nvwPoiu6lYP2K1lVylgDEWCx8+ME+4BW/qDxpu+/HSfcGb8nibPGkl3dTwp7A\r\nABjrFOVzguGnkNdrR3nzedZJt8zKZDDaugpG2cGRBoHcHEl1dLE6Typl5v1Q\r\nMaeTqwLvieLBcc4DXXevVkLiFm/LYXhTxmHscpK0aOoEpZDXLPi88Tnuqd8F\r\nAs0C1diKe1pcTXJbZZU31oezP7GfUs3Ihs5+69nOqIqDhDvhGtPU2zHoYOXy\r\nzxL+LoS6aanF7iOxq/OzNWcOexZF1JuzTbcjSgcXPME1YS28p808mZzTaFRc\r\nhtQ6HN1ad48vrDPlhIPVXj5KnTMhaTOe6XU=\r\n=XS9a\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domelementtype_2.3.0_1649331680189_0.39931220175384996"}, "_hasShrinkwrap": false}}, "readme": "All the types of nodes in htmlparser2's DOM.\n", "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "time": {"modified": "2022-06-15T21:37:24.595Z", "created": "2012-11-10T13:34:54.867Z", "1.0.0": "2012-11-10T13:34:56.751Z", "1.0.1": "2012-12-04T14:15:50.441Z", "1.1.0": "2013-01-09T14:48:09.118Z", "1.1.1": "2013-02-15T07:43:13.702Z", "1.1.2": "2014-10-30T11:01:56.102Z", "1.1.3": "2014-10-30T11:04:12.175Z", "1.2.0": "2015-03-03T09:57:11.527Z", "1.3.0": "2015-03-03T09:59:23.974Z", "1.2.1": "2018-10-21T23:40:43.885Z", "1.3.1": "2018-12-06T09:27:33.391Z", "2.0.0": "2019-07-30T18:32:04.234Z", "2.0.1": "2019-07-30T19:23:33.163Z", "2.0.2": "2020-09-13T20:49:06.776Z", "2.1.0": "2020-12-02T00:10:14.499Z", "2.2.0": "2021-04-01T17:29:08.951Z", "2.3.0": "2022-04-07T11:41:20.402Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/fb55/domelementtype.git"}, "homepage": "https://github.com/fb55/domelementtype#readme", "keywords": ["dom", "element", "types", "htmlparser2"], "bugs": {"url": "https://github.com/fb55/domelementtype/issues"}, "readmeFilename": "readme.md", "users": {"mojaray2k": true, "shuoshubao": true, "soenkekluth": true}, "license": "BSD-2-<PERSON><PERSON>"}