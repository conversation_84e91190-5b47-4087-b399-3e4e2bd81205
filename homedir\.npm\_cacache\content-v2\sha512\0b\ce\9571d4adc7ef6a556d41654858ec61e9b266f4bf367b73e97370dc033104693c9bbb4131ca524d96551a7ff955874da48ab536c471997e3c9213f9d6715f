{"name": "http-errors", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.1": {"name": "http-errors", "version": "0.0.1", "dist": {"shasum": "caa1ff00ef680ee6cef845d4dd5e23aecc2617e0", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-0.0.1.tgz", "integrity": "sha512-gESXK83CZRUyfGJ4Dm22Fh2S3ZXN0D5k/TsDhGggqGLVhC7h7neF7kytK/7KljwagQuo2TCnW/jTTPJJU6a+1A==", "signatures": [{"sig": "MEQCIHsvlA0eM2Vq5BIuifRbNQtpMY4Z2tKS0CGEG0iXmoxSAiBeZWuwXp30u9aNp1y4GttJFyLfm5fKl9qPTDd1IwC1xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.0.0": {"name": "http-errors", "version": "1.0.0", "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "bd58a089bfec699480300e472e0d552211bce27c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.0.0.tgz", "integrity": "sha512-b8nX9FQzRL9ky985pqFJ36YoR+lwNfug6Er2Kr6vwPfxv5MHUuHXSU4IGY2BXxVByba0HPgarM37mUEG6VH+Ig==", "signatures": [{"sig": "MEYCIQCbw6JPHnklnaBh4Lm9pfsX5piB/0qCWCW8MFArTQcj+gIhAL0M1knsjiqHYl+Qg21pgYNbDGdapgg6a/HJfW3d3hT3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "http-errors", "version": "1.0.1", "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "6b770d23b04759f166b0904200aa50775ce2b4a8", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.0.1.tgz", "integrity": "sha512-y+3r3ktrZrONN3d9DIUUo8+zwNbl776Q6Mp7xMMzN7SFPHAy36IJOp2I5pPiExMI7JOlY/8od5KMYQir1jvpgw==", "signatures": [{"sig": "MEUCIQDUU1kcnd+Ueifzvm2XbBunT1Jb3S1mLGm74mLpFu+unQIgCZWHlR1izDhOKanehNRXnKZLfjA/+R4Ulw3QljWv460=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "http-errors", "version": "1.1.0", "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "fc2efe9e9ddead125e6b82023eee7eb3e7785dc1", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.1.0.tgz", "integrity": "sha512-BFDQ68zAgC2gpW0AZWnFEZlkPG9Xv6eoEtvuKFOI0WEq+UMeitGT2IT1X73fWL5qBwoQYkhK9nFlSlX9oy42bw==", "signatures": [{"sig": "MEYCIQCX0mAu+w05UPJaFC7G+PuarhMZNJHLRZe3t+Od+kJdqAIhAOxITMjOwSAeCBcn//sHWVOBUzd72D3L0K4UEpFaKGRj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "http-errors", "version": "1.2.0", "dependencies": {"statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "936739e42c3e9b778d84b30bce32802fd5eb9c75", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.0.tgz", "integrity": "sha512-qK1h4snJa1/uRH8wJR6oqOj0JgiJD2AzjPCg+JTHv7vBtPsVAwkvONn1SIm2Q8Wk/CLr6ENig3hKH0PD/3+H5w==", "signatures": [{"sig": "MEUCIQDEVBdDIm5yajDlbalOHRK0Vk7Vo2TMPh+7RBIeST88owIgAh0C8QqZdr6oYIkTLKeB1eM/zVaMsYxhEm5lGO364JM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.1": {"name": "http-errors", "version": "1.2.1", "dependencies": {"inherits": "^2.0.1", "statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "ad25618756c76137f6f28d6aac76224559daf7bf", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.1.tgz", "integrity": "sha512-+Hc8NsyWP4VRF6AUKOd7LcalNv0yvkT546Ki65PycaodKssei7IMhsl/qN3ZEhOI3z1v8NzFcItUf9c5ExENug==", "signatures": [{"sig": "MEUCIQCctYjXpUPQmEU5HI6mLP8RI6fMBggRPBRjHr7Fx9yaFAIgSYP0dH0JG//eNSk56NnkQ5GIg9HqmrB138dt9iplAqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.2": {"name": "http-errors", "version": "1.2.2", "dependencies": {"inherits": "^2.0.1", "statuses": "~1.0.4"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "ee6fac5b7711f7d5c74c8d8e9ac3d9bb68697540", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.2.tgz", "integrity": "sha512-Pnij+HeF8I4tmQGkjahjgkVTb83jZ3h8OHadmANWGh5D3EOhgEKT05StGWVlrsG3DHxR7ay8C4Au9vfqEu298g==", "signatures": [{"sig": "MEUCICLPcv315lX/vClP5prHsdGa+I8b3wm4fVQhGlh2WcFaAiEA5tyWVjTuCBKlie7c782XEGGgm9up4wwYqBlOd2FbMW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.3": {"name": "http-errors", "version": "1.2.3", "dependencies": {"inherits": "^2.0.1", "statuses": "~1.1.0"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "1e08daccbebfb175edfb1a11409f9d687fee488c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.3.tgz", "integrity": "sha512-WnbjnFHYr/RugJC1ER253rPoSGastIwrNZZnufgDJ8h5gvrX9r8Viu/gTYvtml1Dhqn60sW932hebYuC+vgZzA==", "signatures": [{"sig": "MEUCIQDvdTYYRUGO6fw9utS23lt/LBeK9SD5KSPKq66S0AA9xgIgQ2btV0MxPqqkcy1MLgfHwJYZ7wbiSxHLarc97oGWmGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.4": {"name": "http-errors", "version": "1.2.4", "dependencies": {"inherits": "~2.0.1", "statuses": "~1.1.0"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "5d7d6d2d27b1917777ad1869bab742b6c53699d2", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.4.tgz", "integrity": "sha512-YCl6D+af8Z4J+kb2mW5gKaelM+T4kgeONlyb9e1SgitNKOq5k2KctSheC0HJ5XpWXmf4oFNq9JjsKTLSQwiqvA==", "signatures": [{"sig": "MEQCIBNgOXPV5bzh6c72/zLHQx6ePN3oZiJ6LdU6dTNj0gWDAiBDe3YC+hRJk6M1CLE/PYJMuR6CsEm7Fc50gs4ZUQ83yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.5": {"name": "http-errors", "version": "1.2.5", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "61da92170b47c12bd11083653e9ed44a9b7abe92", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.5.tgz", "integrity": "sha512-+h7kD3pxhns0JcajJE7VYAhyPaVkdVbbKNNUwaO1W/21WkMxzOpO9CoiT/AEoQBcBXz1dRUuBHr9lDlJlQDXvQ==", "signatures": [{"sig": "MEUCIEIVEXzAKOgXL32w+06RJpc0hzYLZh6xU83i++IqCEL6AiEA2tFY4pyrLmIHlA8Lo112BDL7OBMiGHwCd+Hv/sTcRFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.6": {"name": "http-errors", "version": "1.2.6", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "7dc790939e6dc6fb90917b12456a11ad8cf05af0", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.6.tgz", "integrity": "sha512-OAruy0HNZg8Z5VrG2z8INh0ZmQWwrHDlhM/vXVnKKHRBmbQAX2kl3OvC0H28sRvai7XiMo+eAUO9enIoj+oVIg==", "signatures": [{"sig": "MEYCIQCbWB+AgrhODyRy0WEVJ2bVN+AjKAm23Lbbs1pTWicrzAIhALV1qzj6yl73r7w4b4nETmAHuXddRef3f0CkUc6qg90j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.7": {"name": "http-errors", "version": "1.2.7", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "b881fa12c59b0079fd4ced456bf8dbc9610d3b78", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.7.tgz", "integrity": "sha512-Gay6jtYBa3bAJjfnvuLFQ7BFmU1JfzuCPgL5Noe52NDFFPnSRdtwUKqcWITi2Hekk4lsz3JfLnylVXvlSO2c6w==", "signatures": [{"sig": "MEYCIQD3jn25lqVOREOVUAKnRg5LHQAnsSftp6TETEdnjNoJBQIhAL1YVf0ifJahOA8QFt/E27N8zjiVOLwxW+sS5ZXr5g8o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.8": {"name": "http-errors", "version": "1.2.8", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "8ee5fe0b51982221d796c0c4712d76f72097a4d0", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.2.8.tgz", "integrity": "sha512-zqmsJxOyxtIKiQMXiL3PyIlo5d2PWxBzZWAiR3V6Lo/tWX8/n0xrJ5JIr5r+BLeI3obeqi3hoaZkNBCbCcWPZw==", "signatures": [{"sig": "MEUCIFaPERc8tvTVsptjJefz1XXAHxHaY27NFnIZqDaspYjdAiEAoKXLw2+UvSApHIkPEizf6ZVVdmvOV6OBFAhCNkqYVmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.3.0": {"name": "http-errors", "version": "1.3.0", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "239d3bf15d98ea5b3ef553020d60314a2beb2288", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.0.tgz", "integrity": "sha512-YLQqN5ayr5ADoZZJ/mChGs9vNyRKPDNMVr4tFFUaic4a3sx/OvD2SV7L9JQNzMB7+2ZaHpVxmVM4vZmMs4nPkQ==", "signatures": [{"sig": "MEYCIQCeg952Idtz4bhZTW5UKaSg6uGk4tXa0WXqnpOA+Oua6gIhANoSsjhISZuhcsW5BzOmKJdSjCP4m9tQzLbMWOndcyUv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.3.1": {"name": "http-errors", "version": "1.3.1", "dependencies": {"inherits": "~2.0.1", "statuses": "1"}, "devDependencies": {"mocha": "1", "istanbul": "0"}, "dist": {"shasum": "197e22cdebd4198585e8694ef6786197b91ed942", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.1.tgz", "integrity": "sha512-gMygNskMurDCWfoCdyh1gOeDfSbkAHXqz94QoPj5IHIUjC/BG8/xv7FSEUr7waR5RcAya4j58bft9Wu/wHNeXA==", "signatures": [{"sig": "MEYCIQDBDRhT3VNxND2UqkbcgCyPtxlqMsqWC6/c9kLHZ4Ne4gIhAM91FsjyuxnK4AH23WRHKPBxSOmPmXs8QzyRwp7YmQpF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.4.0": {"name": "http-errors", "version": "1.4.0", "dependencies": {"inherits": "2.0.1", "statuses": ">= 1.2.1 < 2"}, "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.2"}, "dist": {"shasum": "6c0242dea6b3df7afda153c71089b31c6e82aabf", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.4.0.tgz", "integrity": "sha512-oLjPqve1tuOl5aRhv8GK5eHpqP1C9fb+Ol+XTLjKfLltE44zdDbEdjPSbU7Ch5rSNsVFqZn97SrMmZLdu1/YMw==", "signatures": [{"sig": "MEUCIQC57I1dghaDZRrjWiLRN/bMzx/kCY/+vLVoxJUnY5P0UAIge4RydDpYIFbaIIvVbXB2d9eQgFxG3dEdyQooBTHE338=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.5.0": {"name": "http-errors", "version": "1.5.0", "dependencies": {"inherits": "2.0.1", "statuses": ">= 1.3.0 < 2", "setprototypeof": "1.0.1"}, "devDependencies": {"mocha": "1.21.5", "eslint": "2.10.2", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "b1cb3d8260fd8e2386cad3189045943372d48211", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.5.0.tgz", "integrity": "sha512-m6dVUhBY0MfvCPNMadX4gsNsII/zhgRSXTy4BnMjVF3XTZT72t/+RREzH39T+Nma0YX3qywcTS7Y33DPNOlm4g==", "signatures": [{"sig": "MEUCIQCkqt6dxC0nnFGhhdTDONiUqOVU4NvCWyPbx/Oe9ce8oAIgKuLTN2FZ/dp+IPWscZQ/U/bH2wjw9xVGuglc4FqI/jA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.5.1": {"name": "http-errors", "version": "1.5.1", "dependencies": {"inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.2"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.10.2", "istanbul": "0.4.5", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "788c0d2c1de2c81b9e6e8c01843b6b97eb920750", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.5.1.tgz", "integrity": "sha512-ftkc2U5ADKHv8Ny1QJaDn8xnE18G+fP5QYupx9c3Xk6L5Vgo3qK8Bgbpb4a+jRtaF/YQKjIuXA5J0tde4Tojng==", "signatures": [{"sig": "MEUCIQCZRnMIVqX2BvBSo885e82lX7EdAQ8w4UlKEk6byKPhGQIgahAeE9cGe6HylZI1zvM6GykIIml5Zl4dLY7EBngB3ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.6.0": {"name": "http-errors", "version": "1.6.0", "dependencies": {"depd": "1.1.0", "inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.2"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.15.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "113314b3973edd0984a1166e530abf5d5785f75c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.0.tgz", "integrity": "sha512-iUinoMS3zSVRD9lG/jRYQGxxFV+w0NAJ8KTnqfOv6BLp5xLRT0DTae0sJSdViXcEPZgEpgaToJekeHRmaF76Iw==", "signatures": [{"sig": "MEQCIHXDc+QX1+rFki4qVP18pKk9XuuHmFVz6YipfQdCPgp9AiBwWCruXJFZaQ/XM27Uw99PzEPdXgtHnTLvvpq/PqWbIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.6.1": {"name": "http-errors", "version": "1.6.1", "dependencies": {"depd": "1.1.0", "inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.3"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.16.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "5f8b8ed98aca545656bf572997387f904a722257", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.1.tgz", "integrity": "sha512-O/lLS06486+l1FVAXghMHAJB3tCg2R5jvGnIQ47X4K1FzfsVs51djHpmwPSzkJ77qIihOV8ONelhSBgfHGG0FA==", "signatures": [{"sig": "MEUCIGOZt7pJnbbtPfCWRpSU/14LYRgcg+qQwAscQbhQH7kAAiEA/p5F9FgA/i+0Ig+er3IZkYPQ1g9eJsSZbsSk0U09WyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.6.2": {"name": "http-errors", "version": "1.6.2", "dependencies": {"depd": "1.1.1", "inherits": "2.0.3", "statuses": ">= 1.3.1 < 2", "setprototypeof": "1.0.3"}, "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "0a002cc85707192a7e7946ceedc11155f60ec736", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.2.tgz", "integrity": "sha512-STnYGcKMXL9CGdtpeTFnLmgMSHTTNQJSHxiC4DETHKf934Q160Ht5pljrNeH24S0O9xUN+9vsDJZdZtk5js6Ww==", "signatures": [{"sig": "MEUCIQCO57DN1uCGiTmOhdMCAexhxIArEoHLukvrRct1KjeHnwIgDt4OEP4b4GcHIWOM3siOy0ccyUfZ/ZYH7siL45hyHwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.6.3": {"name": "http-errors", "version": "1.6.3", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.4.0 < 2", "setprototypeof": "1.1.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "4.18.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.9.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "8b55680bb4be283a0b5bf4ea2e38580be1d9320d", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "fileCount": 5, "integrity": "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==", "signatures": [{"sig": "MEUCIQCDb7XpbhTFWjlgkxUQbLszNdKnhxQXPx7/z21o0K07jQIgX7HTwWNigrt0bx27Up/V+3Qk9vIkjRnn0JE0U7zkTb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15829}, "engines": {"node": ">= 0.6"}}, "1.7.0": {"name": "http-errors", "version": "1.7.0", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "4.19.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.13.0", "eslint-plugin-promise": "3.8.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.1.0"}, "dist": {"shasum": "b6d36492a201c7888bdcb5dd0471140423c4ad2a", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.0.tgz", "fileCount": 5, "integrity": "sha512-hz3BtSHB7Z6dNWzYc+gUbWqG4dIpJedwwOhe1cvGUq5tGmcTTIRkPiAbyh/JlZx+ksSJyGJlgcHo5jGahiXnKw==", "signatures": [{"sig": "MEUCIBitCM8MWDRxOZSSykR8EhyXWoYy4TzwimgGVSNwsdHcAiEA7x6xwmyTozPj/4fjRXFzXukzowd/CxnGZCoQmHD7SFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX0gUCRA9TVsSAnZWagAAEfYP/i1jvg9LMStbGgVRvHX/\nKCmUlRm/7/qHjTWab9+78OKfSc58omNZcLThSY9qjTn6Ac9eVxUMwwcoTfnG\nPMNrz7utKjn8WAyaJDeFKHKdFwOW9CJjRhnp5cSgCgchl5a+KER0XsPyA2FH\n2mzoiWImToSE+2yjFgRuSYWiAs36qJHuDz9w/w4ina1AoGK16s1xHG2dfPm1\nm9vVRRMEY4R0wXaVboNvLa0CIMmgNRvjA2NOJhYg5/5zgTnDGyf7LdfSamQn\naU4prOmZw0e4xG/AVL+A81dkVURmpmwXckIPfg3BQXUWLQdcpJ4K++AjLiTU\nDljWxRsbExveGYSWiJ9D/LgXI6CGDmpnqyWUVH2eywFYBi40TxAnhDoKwZoe\nTmLFTQpFDXN+y/YA1YnWJxMy2/o6Cx3veGsPEKUVM+DPGccADg+KhDoK4u3Z\nWvh6+zQCn+g93SMhmWabJiXqdPQzgvyvF1gmqhf+EYtV15ODwBWa1dVc40hz\nSaRH62lu82aIT+zg4NADTCFggjzuhvMsyoHSEiml9J+dZs9gap1figWLtw68\nFxcc8PnE6o5ScPlu4NjmocvmbtUOn3ruX8PWU2d+cqyYmoOgDHxf58gOy8i5\ncu32x/mGqTL8MgKN5zbb107hV9Cs/yewBcq+V1FmyZbtx01k4ry6lQ9Mjk6/\nnyJ6\r\n=e9ve\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "1.7.1": {"name": "http-errors", "version": "1.7.1", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.0"}, "devDependencies": {"mocha": "1.21.5", "eslint": "5.5.0", "istanbul": "0.4.5", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "6a4ffe5d35188e1c39f872534690585852e1f027", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.1.tgz", "fileCount": 5, "integrity": "sha512-jWEUgtZWGSMba9I1N3gc1HmvpBUaNC9vDdA46yScAdp+C5rdEuKWUBLWTQpW9FwSWSbYYs++b6SDCxf9UEJzfw==", "signatures": [{"sig": "MEUCIQCVyFlV43mkCsMNJwFkJxClRyS3Bc1mJyh0nXTdOjTvQQIgCU9YXDYTK1qirvhFLj9Rpgxu9NxQg/YbATfLXEqVRgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblEpBCRA9TVsSAnZWagAArHkQAIG9Rs9Uo3u3p9nJTVYY\nNziZEMW5gOnu7oCxvSO+1iZZlgiQnhtiHWrTbnCOh3yKhBtENyDxdJ95Lnm6\nXkld0fnCntbysQCODV7eGf6qSkSLSieVFiyl6wHOxx7YIYY0n5JOr8o1NlM4\nSQh6U19IOdnz2Vjyhd4nSaszRhLwfiM2zqObqxTl9wd6S3sXP9pmAMY7EkDA\no228jIJGzYrCJyR1wDUAbKCCvwKsKCRaJobCFyqSzjF0agcy8RihbjAIMOQ+\nnhSjrM/nT+JjzpYHCUceprcx+fi31bO7V579RT4tU2FwHungVISL6KOYYyZ7\n0sr1pZ047ExXz6iy+4O5VdpUcnPmKgl2lnXJaumCS9+0j5bSiQ/cRGFPbRyd\nDu8nX6WPI4i3OMnVUYnxy8Hbw63XxgxGEwAMG1mOEIadKQdm/pyuFTYtO+Ri\nDje6i8p1BZ3SPjN3bxQgzY4xuxOS99zKUeojKNcuxwEqGEekfTe797gstYm/\ng2ePtRL+V2IuzXTISdwIJyjt3MdPBNhJ1dXf7dOCGU6JMWYyog/T/iyHVUt3\nZFfTPR+0PIU6V0ZYIRlZYeiP/RWzEvAAmk5eHwyCdSey1wErVmjS4g1k4KbB\nm5mh9e6In/IR/crze1xUQ6zACMt26W0MwOjh4IHAoa+5jimBMgTxUs+SfnWb\nMVyw\r\n=Y6c2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "1.7.2": {"name": "http-errors", "version": "1.7.2", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.1"}, "devDependencies": {"mocha": "5.2.0", "eslint": "5.13.0", "istanbul": "0.4.5", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "4f5029cf13239f31036e5b2e55292bcfbcc85c8f", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.2.tgz", "fileCount": 5, "integrity": "sha512-uUQBt3H/cSIVfch6i1EuPNy/YsRSOUBXTVfZ+yR7Zjez3qjBz6i9+i4zjNaoqcoFVI4lQJ5plg63TvGfRSDCRg==", "signatures": [{"sig": "MEQCIHXL33Xvmq01+Id4i/WVvPpyL63KhHvxzEh7+rFXL+hCAiABX0cU+h/z8ru8YovSsJKsIuAuWHN/4WQTI5oCMScfQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17086, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaw02CRA9TVsSAnZWagAAM20P/RYOFi+Y8YJaNae4ZdMA\nx1cD68fmYF8nt8GgOx6MopK9YbsAxJUfpYYdIrg3IaRw7aqHwQGPmaDCa8fL\nUoDVy7ZcqlJLOBGkaJ4YaW4ZX4NFLsSBPhj7KLbmDVBhtQfHPisboMDT+LPk\nou5F2X6QqvQVq4VDlX+zI+Zm0FJmsMabdBJTadofNRoQnJrP70UxyqdoOQNT\nfs96MjO6+FQBE31ln8ZncEsI7MRlq46VnKznbsQ5ghg19MKl5+9czs/jaoaK\nWKf/pfChqFqxvibhmEU/9a09hFqWJLYQ7P6GJENBieWOpPybsOl/JcmPzaUH\nW66tvwTUJFGY3nI89Rqb8s47vj4ToUadzWuz0sZc/VVBahF4Que7riQKzDlw\nb4W92jDyILs8UOlTeHy28CVbA/fm1huLTNtDwjM4iZ9SutZMj/DoxjArGLpR\nOcdLmjlv7nL3p5Sieqsvx5pc06pgGpJH6MCE5XGd11NM5ukkocVi2Kwuu986\nuTKRcXVOXETUYcK/BlkLDwWTsgCcE9pPl4Dy6WOb0ibvcRlJWKwLVBFL40Y0\nMl4//MJidrAl4cTWXBP1e7ILNFS2MSJwSjCUF2eHBvYLvnKo6vS3uPfW35ag\nQdo0pRBwKHczPRzi7gwJSgPCQ/Oi6RQZfxyGe/oYArCQfr4SMSJVjN9LA2Br\nMXTr\r\n=XLau\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "1.7.3": {"name": "http-errors", "version": "1.7.3", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.1.1"}, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.18.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "6c619e4f9c60308c38519498c14fbb10aacebb06", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.7.3.tgz", "fileCount": 5, "integrity": "sha512-ZTTX0MWrsQ2ZAhA1cejAwDLycFsd7I7nVtnkT3Ol0aqodaKW+0CTZDQ1uBv5whptCnc8e8HeRRJxRs0kmm/Qfw==", "signatures": [{"sig": "MEYCIQCo5lAVB5/lIzSkLMddvUa+irYaCp4HyGXJOT/xiBRUjQIhAKNbhgBEcQfwzVGzhZAabe+sBrh0BjTON4w7So+0r37k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEVruCRA9TVsSAnZWagAA8wUP/Repl5BiN8sPmq7Iy7Yt\nOe1hCpAJE64L+uYRPZm8hV+Wkli7J4/4UyLf9k20JWiU89axyTQzFT/XpLYc\nsw+knAnUwzVDUQvlyNDUllLkLkdhw07VhM7TeL8yym9tem9ghsg8UJZZS3H8\nPXWIqDgVbVqVcWomSiq543tXO+5NG3sqX2pVoGKGISGn/rJhiJvDcBvjYT5i\nv5hwFDK/av11koTioT9As1FR+Pdltj2KetA/SZ7IeIzRc0FPulj2k53c4zjQ\nzazKmL6dnEf2afZuN6clQd+clO8fz1+S58DP+EJj7Cvom2ANvpOlQs3hZchB\nvQOrInn8kcebNzgkTh1n4HdIwHeMEjOgS8c0TxEd2WiQvLRyvPPeTPzEglTx\nmpEhUAWo9xs1nkqQ7/RZZHTni2jRkCUM6saG4hC/kXdx79fiiu7q6BLhF5Wi\nAqb6egAxgmHsaBbDajk/pJk8nsaq+el16Aq69M+heSDCQXV1uzsxCzdimljL\nO+dRFBLKhgSOulIjwM6OraKXh8uIKABPqgAUDMsctFI5gru9ZeXqTNvzNj0g\neFm2elBXn8yjrAOShsx/HUDysNsZ3Y8W1sx6tjFREYmWppAIYjtMOocVIwo6\n7BxMAq9+fl7mSlTBZmrFj0oCfFrGGL3ZzO8vKv1hGeKpslHohoePMiwgiDmE\n2lpi\r\n=LjKs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "1.8.0": {"name": "http-errors", "version": "1.8.0", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0", "setprototypeof": "1.2.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "8.0.1", "eslint": "6.8.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.22.0", "eslint-plugin-promise": "4.2.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-standard": "4.0.1"}, "dist": {"shasum": "75d1bbe497e1044f51e4ee9e704a62f28d336507", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.8.0.tgz", "fileCount": 5, "integrity": "sha512-4I8r0C5JDhT5VkvI47QktDW75rNlGVsUf/8hzjCC/wkWI/jdTRmBb9aI7erSG82r1bjKY3F6k28WnsVxB1C73A==", "signatures": [{"sig": "MEQCIBLUJj0jMfj7QYSqPzixQLsa2mrc0tpbVBE1LEkV61RYAiBkKiqDR19Yd8lQRinMyUyWGLOpSsMeVuZkDdTWtnI3Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+X79CRA9TVsSAnZWagAAv6wP/RjIshvYJXVRbRP0mb4a\nOvN2mK7/B2nqnD8O2FzgEQ0p1Llb1K9j6TlWdq/cJInioDoR42yQgfyj4FEL\nonR3TgUlE8EjQFhH/ecYF6eaLVNaCTerd3CwdOXAXorzPcCE3g8sx46aB+9p\na/QIvOnNxt9UuDsJDDkpWvVGksr0JwWW0ZEjx0Gw8b4j1GOLXjPzFd4WqOKr\nVXG8KcAs76ZCX4eGIczwRuTpOe8mXITrtqkgyzdM98+PONQsIWEWwjt0tzm0\nMNn9cUfgB0A2UXrHQK0OFuP3TV/Ms3kTvLkvFBkiaxcpuGBlwJXSdK2/j6wA\n88Xqqxx2g70mlCgHfeHOt8GHOnYiCrsCLO5wyhJXkeSipeJM9r/8coZfZEV8\ntGXkLBxAKPInBMzGaIPT9PNsOrTy2JV1Qz2MV1OQU//LokWLLbwxsB54vXVh\nuuQSjjwUYDzA5V52/uBgfPThcM8yC0nYvU7qXV9eb3f2j3f5UJdcVkV/Cr1Z\nHtOqzHPPzY/dDubMfCoufMc3SnFV6fK5IFRmzjlRWLoKy1BZ1bLbmWxhBFeK\n88wD4p0vvSNuPNmXK4ObgxCH6NSUft+4IaHRnpBjPShoV0uyuTktOG1oLxyk\nHgG01wBJcVsUE76idV2fc4VLAMs3l1IiX4kb9+qgEYecsHA5LTuEAHl2aGPr\ngTBl\r\n=OG/P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "1.8.1": {"name": "http-errors", "version": "1.8.1", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.1", "setprototypeof": "1.2.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "7c3f28577cbc8a207388455dbd62295ed07bd68c", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-1.8.1.tgz", "fileCount": 5, "integrity": "sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==", "signatures": [{"sig": "MEQCID89qas+iwQ7DmOfk9Qma9V3ud0rDp5ksfAbEQ1Aa7sBAiBxAAok+C/98M+dzJ4UQu0EiRPEwNCrogENX9THyG5x9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18316}, "engines": {"node": ">= 0.6"}}, "2.0.0": {"name": "http-errors", "version": "2.0.0", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "statuses": "2.0.1", "toidentifier": "1.0.1", "setprototypeof": "1.2.0"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "b7774a1486ef73cf7667ac9ae0858c012c57b9d3", "tarball": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "signatures": [{"sig": "MEQCID1XrfQ8ZFxajv1NEQ/9Riw7BeBDOJ01mbr8+OpDAncpAiAt33KnKPmlGRwT7hlvNBgWimpDHlRcvgL8/zyrUq7VZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvVXiCRA9TVsSAnZWagAA4OwP/3QPkXOVfeSayHzOjAvN\nl54inTVNaO1VHEg6sIfaSq3mfqXcN6vdISilMP5/VAxSNAxGI8FqX4wc4FDp\nrC1rQc5V9cMNC0PioqkjZMImQin0JYrbOQUUxReMjZozddY7plN3A1YOfAXZ\n+HtsavBLOdHQP9XKtbroQZhxptbo2dG8Kk12RAgdKz4Ypfe9r7L/FesYJ2A8\nrMZDL2JnMSC0TeQUd9yKU6rMlH7yQg3UmARgqTtr+BVLuYsgIRXUpfp2Sxd/\nKelg+XwKtJdykq4aF/1cYGXX0uWh6iNUlSmabXSxwKhRD7UB0aIBPn+Dr5jl\nQsK88LTHC4F+aeTUCiEvlcGb9WywsK1wJzV8P6aoP/cmlGZqUThZHZ+ZRboo\n/Mz/adRVFsiPFiYcfJXlsHhFqaiZm5aF/ZUU7NjrxWWAiqIIevl0ysUtCNpQ\n2SICRsp+h9QiwfmhjjAI1Om0FH1DRY4vlohVUzqiaXGBbOQinxKk2dqUtfS3\nyIyUdZDauccKrhMnbJH0wVky41EGUAfehtbiiHhnLg7fnqxejmyCchEUFrfo\ns9g+vhN6Svn+cjjbl8F8JjqMmtanMONlg/7vCC3fSN2l+2OwMcBzDzRz0kYL\nk/FdknbGitDdcPc52WAwXG1vydD+9B+E7yYX2ERY0fJoZjJ9ugpbMGEfL9n9\n9B41\r\n=5cFj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}}, "modified": "2025-05-14T14:56:20.905Z"}