{"_id": "tough-cookie", "_rev": "128-ee4790910fd6ce44117b2feb8c0a585c", "name": "tough-cookie", "dist-tags": {"next": "6.0.0-rc.0", "latest": "5.1.2"}, "versions": {"0.9.0": {"name": "tough-cookie", "version": "0.9.0", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "b09b191982dcd7b0cccd78e176c2d6842810c9c5", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.0.tgz", "integrity": "sha512-7GOflgzmE6usQhUSZwstSlYRSlCkIa+HSWkKeshLjervEXl01deSAaZ5MIHjrWciUf5j41BVZXcJkygHXBaTow==", "signatures": [{"sig": "MEYCIQD8LhUoX7gC1v8SSLXxZoxArr6LAYPDxmL1n8p86ieLfgIhAPYkpxull+QuYrspaWNA63/mn/X943KWcbVYa1QIuaUb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.100", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.1": {"name": "tough-cookie", "version": "0.9.1", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.1", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "0f882fcc572567283eef639b0ba1c1a4785c72fe", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.1.tgz", "integrity": "sha512-Mu0u1TIY42OtXbQfN7QTPgLdvZNqh97nJsoc7Jsg7SG/3gYNwaAxHt9WxeHhSr0ITMZQJ/DNLBEwqmt5wtwo/A==", "signatures": [{"sig": "MEUCIQC5/HtKEHjVD7qLM8LE5UwACvjjO+KUm9gnqvE0A16IswIgF/Zw/AmHS5IFgZenNM3VYANeazX5JmboJ0nLdPuvodY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.100", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.3": {"name": "tough-cookie", "version": "0.9.3", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.3", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "57017d596dbf03e4c7c79cf2f8c01fc69e9b70b1", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.3.tgz", "integrity": "sha512-o20HYK9A6pBCEZZNc35MIMAebLtrEOuTEHQso7L2H8vdtHCtqcQ7Kk1X7tWp+GxqLCINQhzi4aJaWoQKWsdfmA==", "signatures": [{"sig": "MEQCID8vHH5ua/vDcn0i6C2gJ0vlxqIHq5SSV5fAw+cj5+HgAiAkxYV3mhc01jeKkMspm6voEeopRNhbX6eSlKMQXfcd6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.100", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.4": {"name": "tough-cookie", "version": "0.9.4", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.4", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "e8a768ccb452dd3a9b8b844fe3a2ab2d319f0315", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.4.tgz", "integrity": "sha512-bqNqgDiSD7+9wjRWZLSNg9UzGY/vjA53KbxM9Q09ENmBUhsRQ6Fz8KHiaPrQnMGAZdkeAHaEq5/z4U0oc1uNJA==", "signatures": [{"sig": "MEYCIQCWlZcvzDCRbJHjLMhpsa5cK+jhcNYFpTN4roeVMJzE2wIhAMbfoQFTwdAmoGc65nqF2iTm9ex9YZSesW+fV4mwxPaQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.100", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {"punycode": "https://github.com/goinstant/Node-PunyCode/tarball/master"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.5": {"name": "tough-cookie", "version": "0.9.5", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.5", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "a189208232ec5ee8b94f2578769b21155ab9b131", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.5.tgz", "integrity": "sha512-5LZ/FHsKPY7+3A0HPe+pqTR9Wpaue5SpQzpN7WLuhk0AeYMvtv/rqD0YA08B9X2ZfKrMT6brRTpMcwdOIMICEw==", "signatures": [{"sig": "MEUCICRL3ZsDhsgSXm5PI+JOfAcNvSpRualLOthLZUa0KXEdAiEAxmY4bRWJR+XP2rQRsFjTh+jdLlfQ2nh3KFY5V3ZHSLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.100", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.6": {"name": "tough-cookie", "version": "0.9.6", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.6", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "c63b55e3862676e5d394a9f102f7dd599539af15", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.6.tgz", "integrity": "sha512-2GLlGQmMUtMGR3PMiDu9wxPfvGav4Cdtfvj/8vvVJjq2pwqObobRJ8cXF6mUDu0XM4fX4+BhyVnRecWI582Vqg==", "signatures": [{"sig": "MEQCIB2EBM8lcoHXX5iFMOf7BtXcrGmkKPIzQAFMzk9+9so5AiA8lblH7uXY85fxxV8CKv/pc9rKDB+P29HWr2wuAtBZRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.100", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.5.11", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.7": {"name": "tough-cookie", "version": "0.9.7", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.7", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "ea037e175d326574b0afb196d658672c7912bd45", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.7.tgz", "integrity": "sha512-bSc8TKXfQSZbEwWvtrBP6BFt6xDkIsOuAIC5r9539+IG3QBwT3FlVl75BvbARvcexeChDKWxY8gr/rq9NgnsoA==", "signatures": [{"sig": "MEQCIHDA9gGNYY54rVmIc46NT13zYaNHdqNJjhYVHDwV9VgzAiA8jSrORCopYfKsW5g3R4u4Wrbft8UiwhP7OVIVmM8VsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.6.3", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.8": {"name": "tough-cookie", "version": "0.9.8", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.8", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "435991f917d7aa2d1e9e1ed93484f19a430bfd15", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.8.tgz", "integrity": "sha512-ldoi2HSCy5qy7nwDWIX3Bzj04RNcckPELdsneuq9w5aKUiGzs2+ymWMPDydt79+UuBhslu/DV9JQVpUGJP9SxQ==", "signatures": [{"sig": "MEUCIQCHK/E+DG6AL0FdKWG8he/NeROePI3GkpXf4+HrKvQcKAIgIjq2+DIPEixz0Vevte/wTAGuRHBTDksjlb/E8Pld9h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.6.6-pre", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "_engineSupported": true}, "0.9.9": {"name": "tough-cookie", "version": "0.9.9", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.9", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "e62bc00ee4bef4dfba957dd7177218d0327ef595", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.9.tgz", "integrity": "sha512-+Po0qsWdrntbmk8z0SeVtMG2hooLThSKd1jLMMnFMgO8Fh998odHrv6zUI4XTalFAMV4ow5p8Xho3IOcUrtNQw==", "signatures": [{"sig": "MEUCIQD6fbY6E41WOAqoEkgxLs3VjGuP7URIreVCOfik9GoOsAIgebr1ngA519B6pozS/YThNnpljoQR4l1MmYAv5ZcqWrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.11": {"name": "tough-cookie", "version": "0.9.11", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.11", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "6f45ee9f494967cbfcdeb97efa5174e1be9b87bb", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.11.tgz", "integrity": "sha512-2yQ9Jx7m6E5s2iW9w86EIZzwhQrPJvxhZMId5v6qdd08sKvFS9/7agGRbrLAgEUQiTU4YOsKoZRkHcr3tLUKgg==", "signatures": [{"sig": "MEUCIQCbZ3qnNeW8iQLnt4WAg0c/KGVWN+DBhtiGks67UM4y0QIgPdt2/2pvR9vZcV2PfWx1pAyegDKowzgG3mq/DQOonOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.12": {"name": "tough-cookie", "version": "0.9.12", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.12", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "9dec1931bb8bb6c46252ed2c9ed8ca00abe11546", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.12.tgz", "integrity": "sha512-NRJ2CyeF4l9v2ZgEnREkOFMck0MYpppALfT3iEqIUVXHb6x02ROzHFaBK+zVenp9VmqDgfYtcbhL2oP5mWMvkw==", "signatures": [{"sig": "MEQCIHAQttJAwgm/RlloRmYFZxPYjOx1wNy58iJiSXfBy9CXAiBzwi6JNpgEBcnr4w8ZBR1Di9qs7nDikKhJUcHUc6vPGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.13": {"name": "tough-cookie", "version": "0.9.13", "keywords": ["HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.13", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "8b3b0e536d8b1ee350aa53fca99273d03af50a56", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.13.tgz", "integrity": "sha512-RBev1ZGhVU6xdDcdD7BL1X1VgoAvOZ7zkzypI5L2qKWVe/Tjclt4htkKiJ7sUTb0qeHK0c3vcE90799Q7abtPg==", "signatures": [{"sig": "MEQCIHSe1QdObOsuo9hrBkhtfjD1sZN4ThGJfrBroWkwO+XnAiB71laeJih16eFrfhCudxU5kGxCBz3flPafmMA8SYPcqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"punycode": ">=0.2.0"}, "_defaultsLoaded": true, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}, "_engineSupported": true, "optionalDependencies": {}}, "0.9.14": {"name": "tough-cookie", "version": "0.9.14", "keywords": "HTTP cookie cookies set-cookie cookiejar jar RFC6265 RFC2965", "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.14", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "fb2276385802fda1b3d25c083dcac93f2057d35f", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.14.tgz", "integrity": "sha512-hCEJR0pAerpL7bGrvtKSIjkbIn4zIga85vRpxWXqx+vv0cqMl9bvY0djAR7K8h+5IWLDh+FVHwExiPmSunxeBw==", "signatures": [{"sig": "MEQCIAtye5mEfANddXx4vsB+ErXu8uTmQng8ftfZgaKmXSiLAiAv+Lu9BXxvt1P03xpe1J2x7FpAvB45hreH0MYEqQpWlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.61", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}}, "0.9.15": {"name": "tough-cookie", "version": "0.9.15", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"url": "https://github.com/stash", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "tough-cookie@0.9.15", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "75617ac347e3659052b0350131885829677399f6", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.9.15.tgz", "integrity": "sha512-j4C2GYjRuBjEGRgF5eIvxSvUkTXk7KJDAX6SZtlDWNioUlCcW4lLSlETSWWSiVbXS6SOB+LUK7PYn0RDxgJ3pQ==", "signatures": [{"sig": "MEYCIQDgPhUt5WRDw97q7x+qQex7/P6pqbXQmkXkNJ660fmb+wIhALKgSUsn49PdKzOwD6g3/tMEVgFk59EjRwQEtgZ5RZQJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": ">=0.6.0", "async": ">=0.1.12"}}, "0.10.0": {"name": "tough-cookie", "version": "0.10.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "MIT", "_id": "tough-cookie@0.10.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "0cda4e24b2e6c417ad44270c48c9787f560aa1bc", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.10.0.tgz", "integrity": "sha512-jHFp3bNlSM+ugQ2ztGEf4pkCiZlYko9ShMboFS69OrTgvra4HHJ+hC4VsHaS821HKaxPaoMPCdRPLFgHOYz0eg==", "signatures": [{"sig": "MEQCIA1c4K+/cpWCbv2X4bIyqK6MFn22ESRD1lDbmdvrN+vFAiAHZfCgP+7bvc9vMfaV6diODhqkfSc7gK25tGhESElOYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "0.11.0": {"name": "tough-cookie", "version": "0.11.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "MIT", "_id": "tough-cookie@0.11.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "7d8ef58b287253c98f50fa21e9b14408e07db323", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.11.0.tgz", "integrity": "sha512-PBpcp5SnMfjIIbofOyDDxBK5nvaVFs259fVIfr2F4iqbqr6LlfvS3nDfrcyS8p5TG5TmmQSgStvoKAdYi7OmgA==", "signatures": [{"sig": "MEQCIEKjeOaoDMFayQKVun6AXXKK6sU7q1Y/yNg9CiF3L2sdAiA80SwaL/tbzXVx/OUyCpEgySif85FrwyDen31U1gLa1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "0.12.0": {"name": "tough-cookie", "version": "0.12.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "MIT", "_id": "tough-cookie@0.12.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/node-cookie", "bugs": {"url": "https://github.com/goinstant/node-cookie/issues"}, "dist": {"shasum": "713ccd8a77cd987ccd10b6f6938bbd96ecba36bf", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.12.0.tgz", "integrity": "sha512-DX/q+v1Cn/QDElfxph0HrK2w6XEHI8Sv9+mDlQdb+SEVD6SmU78EviK67HbIOqbZkeA0E5LqW0FqvpR4PdbGAg==", "signatures": [{"sig": "MEYCIQD+E9/yIHBIfgH01SwFAr1z3y4/QCMh3z0Tms66EwJdJAIhAPzaa8wrgCyw9mNJozrUHJd/N2zLsOo7rgiWbmexDFpW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/node-cookie.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "0.12.1": {"name": "tough-cookie", "version": "0.12.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "MIT", "_id": "tough-cookie@0.12.1", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/tough-cookie", "bugs": {"url": "https://github.com/goinstant/tough-cookie/issues"}, "dist": {"shasum": "8220c7e21abd5b13d96804254bd5a81ebf2c7d62", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.12.1.tgz", "integrity": "sha512-+gd4PklNJsxzu1NoNjhGRfOZZ5llND6VtQZGuaDXdmI0Ii79V5+YCa2sLx8Q6lYhYN2+9frCzUwOLQpuwHvO4Q==", "signatures": [{"sig": "MEYCIQDR17EfeJNy0MhX5uRZ8i3KER8iAS+RytmSZIFblpnGbgIhANJkRxuWG7Uhq8O0/UYCM2hz1cXS3FemqsdQ6zgf9ghf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "engines": {"node": ">=0.4.12"}, "scripts": {"test": "vows test.js"}, "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/tough-cookie.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "0.13.0": {"name": "tough-cookie", "version": "0.13.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@0.13.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/goinstant/tough-cookie", "bugs": {"url": "https://github.com/goinstant/tough-cookie/issues"}, "dist": {"shasum": "34531cfefeba2dc050fb8e9a3310f876cdcc24f4", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-0.13.0.tgz", "integrity": "sha512-VS439wmO0qE49JLHhhG6tC5dMFfmVdyryd2PTjkwsDvOMElMebPX4Lo3OLBBqLrOHnvcNdzQa7MS/SkNvO0Idw==", "signatures": [{"sig": "MEUCIFyUOUwuRIbxdW/DZ4SKQyGwvlJ0g89p+g/Yvkc6Gv08AiEAtj8CrQ8TcECA2ezmK/WJyNy19V0HYP7MeIchop5xQSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "_shasum": "34531cfefeba2dc050fb8e9a3310f876cdcc24f4", "engines": {"node": ">=0.4.12"}, "gitHead": "264a99e770ff606ec9df126f5443de5f799ec500", "scripts": {"test": "vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/goinstant/tough-cookie.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "1.0.0": {"name": "tough-cookie", "version": "1.0.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@1.0.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "56ae180f2a88bc5db462c479229ee36e071e0296", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-1.0.0.tgz", "integrity": "sha512-hFWvzFc9JSGIVtlEocqLwSc0FGLrFb7itTfO/5a4kYSipu8U68eq6RcyTaL+H7fhoxg0OUXbXXIWM2huUUECEw==", "signatures": [{"sig": "MEYCIQCwYYLglTEem6Q/kf8K4YoVtmr/Ba1LhUjOJPxaDfjCiAIhAM3AcWG+TZGtE6fxojREVX7c7nsMRD+MUXpn3/Z4id4J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "_shasum": "56ae180f2a88bc5db462c479229ee36e071e0296", "engines": {"node": ">=0.4.12"}, "gitHead": "c2a65279a4d5c303d9efb885983dc06796d6ee4f", "scripts": {"test": "vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"punycode": ">=0.2.0"}, "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "1.1.0": {"name": "tough-cookie", "version": "1.1.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@1.1.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "126d2490e66ae5286b6863debd4a341076915954", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-1.1.0.tgz", "integrity": "sha512-7RU5PzHLrzl7RWnuBgiduEcGHIVVZG4pRyRhZwhvH7rVBcxOtQKUEM9Woxa9lf2OOTCV77/WbFuUO3FkpJ0ckw==", "signatures": [{"sig": "MEYCIQDZ1Psi+/DOla9fp56zidWMq7pwihirEiBHICQG9pUV4AIhAKbUrd3B+0/r1lExnwNCyb54wQPlifi3/acifUIZadgj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "_shasum": "126d2490e66ae5286b6863debd4a341076915954", "engines": {"node": ">=0.10.0"}, "gitHead": "ddbcc02c8c24726c68e36a67d5864291acfdf57d", "scripts": {"test": "vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "1.2.0": {"name": "tough-cookie", "version": "1.2.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@1.2.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "9b7e9d98e769e80b5aa899d944fe44e02ebf82ad", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-1.2.0.tgz", "integrity": "sha512-u7gvM6OgRHEQorTvPL+S0e6g4hMrsBUAQbh1mKz/79VyetrtavIHwZBFuvzkiJIucMMi3yfQiIr+Tl/UQXa5PQ==", "signatures": [{"sig": "MEUCIQD/5BrfKCHazs/iqKX/WmgTSJ9j0CcnIGlSluKPIXChJwIgWnbZqaEk18uZDR5kNlEx+PY6GfzXujK/Vx5/JHaOBEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "_shasum": "9b7e9d98e769e80b5aa899d944fe44e02ebf82ad", "engines": {"node": ">=0.10.0"}, "gitHead": "74e59de50b719bb9a1b01c8c9db57fa31401ed1a", "scripts": {"test": "vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "2.0.0": {"name": "tough-cookie", "version": "2.0.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.0.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "41ce08720b35cf90beb044dd2609fb19e928718f", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.0.0.tgz", "integrity": "sha512-qYeH1zA+4+36nVi2waxBoFcbL54iInWYs6NuMQztwijcfhPZqeCm/fjRkDrnEtkYzOIh19SkKrjs5A+VDx+5sA==", "signatures": [{"sig": "MEQCID5g8TOaUF5mGeVq+JRtIeQJJs+Zh/oTjr3L2JlG7URiAiBiYzu29HKbT48u7aUvVlcHkrYm7U2cFE2YEMNXAyw8TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "_shasum": "41ce08720b35cf90beb044dd2609fb19e928718f", "engines": {"node": ">=0.10.0"}, "gitHead": "a3af6104da7787c23bb98910109b0e0e8a10153c", "scripts": {"test": "vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"vows": "0.7.0", "async": ">=0.1.12"}}, "2.1.0": {"name": "tough-cookie", "version": "2.1.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.1.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "4e66609e3e4360a93aff0d4d64b3632e966e8613", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.1.0.tgz", "integrity": "sha512-jd1ifZarNXBTPmOH8+PDhw26AN3KjFzr01nrrQ2g3fy5xeZdT/OvcqasKW0riUcQFq+XFagffBf01584FBvD3Q==", "signatures": [{"sig": "MEUCIGXjNCcQDBaTNVVCh0NGH/R4jiOlbA7/JwUX8oxOF80oAiEAwy4m6tqTqb6e81SFTY1mBrdXuEPjOFf28AlhL0ETeZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "4e66609e3e4360a93aff0d4d64b3632e966e8613", "engines": {"node": ">=0.10.0"}, "gitHead": "63045791429fa8fd644e68676074da480a925448", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}}, "2.2.0": {"name": "tough-cookie", "version": "2.2.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.2.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "d4ce661075e5fddb7f20341d3f9931a6fbbadde0", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.0.tgz", "integrity": "sha512-frs4Ftha6JFRPQyqjgjx9UbInbzE6MZmJBDe65/3kbyuTR/WNLsYjbD4mH/KboKbGEQe0ICZ1xmbU8D4FYeVCQ==", "signatures": [{"sig": "MEUCIE5YOjEFQQjtGum7cbvxfmPYlRaJvqjak2pzf31UCi7ZAiEAp0x3ckksUiy3MxyDQolWGYslIWVBuRNlW8TJM7A03EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "d4ce661075e5fddb7f20341d3f9931a6fbbadde0", "engines": {"node": ">=0.10.0"}, "gitHead": "fb1456177c9b51445afa34656eb314c70c2adcd2", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}}, "2.2.1": {"name": "tough-cookie", "version": "2.2.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.2.1", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "3b0516b799e70e8164436a1446e7e5877fda118e", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.1.tgz", "integrity": "sha512-eEYYegvebFzOPfrJK0rcwfhp1oadk6zvwOiySQn88/xiL1WSK7Jdgrmq1MPqWAWvTgaTWiQN658Hsu/2ccjERQ==", "signatures": [{"sig": "MEYCIQChyFXYglyI1G4rJy6BWfn8oOCo6IHBLPn+FYyr3Ae3oAIhAIhyruyXNUoFiyeL0JFMZO20czzuNqychw8R6+16GI/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "3b0516b799e70e8164436a1446e7e5877fda118e", "engines": {"node": ">=0.10.0"}, "gitHead": "f1055655ea56c85bd384aaf7d5b740b916700b6f", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}}, "2.2.2": {"name": "tough-cookie", "version": "2.2.2", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.2.2", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "c83a1830f4e5ef0b93ef2a3488e724f8de016ac7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.2.tgz", "integrity": "sha512-Knz9Yr0hlBoWQgUKzOIvRg5adinizAf49i2gHRhj6cLjlM304zRw7uyiY22ADniDxnPHXfIeyQD0EAkgpIz0ow==", "signatures": [{"sig": "MEQCIAZwy0RJP+GTJNtL1ZDYcuVKWHTtpJvMqkS0iJ51r776AiAWwc6RYfLa3vpEV1xM4PjlsCv+nTlvnDdAgIb3h7aIiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "c83a1830f4e5ef0b93ef2a3488e724f8de016ac7", "engines": {"node": ">=0.10.0"}, "gitHead": "cc46628c4d7d2e8c372ecba29293ca8a207ec192", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "deprecated": "ReDoS vulnerability parsing Set-<PERSON><PERSON> https://nodesecurity.io/advisories/130", "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "5.1.1", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.2.2.tgz_1457564639182_0.5129188685677946", "host": "packages-13-west.internal.npmjs.com"}}, "2.3.0": {"name": "tough-cookie", "version": "2.3.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.3.0", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "goinstant", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "38f67cc4fc1b3acd9f8643b92aa3cf138e4acbfc", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.0.tgz", "integrity": "sha512-L9ufTLFvuUouKJn1vq14PaynFl09dv5o54Ie/t1GD5JXg1kXbOdlIc5FOyHXEI2Akkdrw5CcASpQjstoZOjqMA==", "signatures": [{"sig": "MEQCIHEWVcNpBIv/0KQk7Z3ka8cE+HFiQ0lYOYp39UEHq9PQAiBzD34mgagzMplIPG6AJ5seQkspxanvp0tOOoj6OW/0zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "38f67cc4fc1b3acd9f8643b92aa3cf138e4acbfc", "engines": {"node": ">=4.0"}, "gitHead": "5d155798aef3c256b3814e3fb1d5a821c2c0689f", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.0.tgz_1469126657901_0.29259776300750673", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.1": {"name": "tough-cookie", "version": "2.3.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.3.1", "maintainers": [{"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "nexxy", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/SalesforceEng/tough-cookie", "bugs": {"url": "https://github.com/SalesforceEng/tough-cookie/issues"}, "dist": {"shasum": "99c77dfbb7d804249e8a299d4cb0fd81fef083fd", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.1.tgz", "integrity": "sha512-3C8DIrKkBO8q3BIqcUB6zPuqua6wCso6ddmRmHjbJ5/OfJ6rrw6zTf1DFDsBSZbKNKC1XzfVJRxZrdcxyjEOjA==", "signatures": [{"sig": "MEYCIQCwN4qJMHnRcHugeQYl2rTK3MJHeyY0NpwVvKGm0ppVfAIhAI3XSvM36tUV8QTdX3BEid5Nq69anNEpQE6OJhOAvMnJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "99c77dfbb7d804249e8a299d4cb0fd81fef083fd", "engines": {"node": ">=0.8"}, "gitHead": "c11a2d11d12348a35ef595c809e30e641a804a7d", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/SalesforceEng/tough-cookie.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "6.3.1", "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.1.tgz_1469494891088_0.8524557144846767", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.2": {"name": "tough-cookie", "version": "2.3.2", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.3.2", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "nexxy", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "f081f76e4c85720e6c37a5faced737150d84072a", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.2.tgz", "integrity": "sha512-42UXjmzk88F7URyg9wDV/dlQ7hXtl/SDV6xIMVdDq82cnDGQDyg8mI8xGBPOwpEfbhvrja6cJ8H1wr0xxykBKA==", "signatures": [{"sig": "MEYCIQCy1INmcPZBCecr993z2pi4qNlcaWvpSZ5ZBCyR14O79QIhAMnex5zsD8jrcmNyAFBd7/ApdcISk1YplNvZ/o8hE28t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "f081f76e4c85720e6c37a5faced737150d84072a", "engines": {"node": ">=0.8"}, "gitHead": "2610df5dc8ef7373a483d509006e5887572a4076", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"punycode": "^1.4.1"}, "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.2.tgz_1477415232912_0.6133609430398792", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.3": {"name": "tough-cookie", "version": "2.3.3", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.3.3", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "nexxy", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "0b618a5565b6dea90bf3425d04d55edc475a7561", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.3.tgz", "integrity": "sha512-WR9pjSY3qO0z3yC6g33CRcVt2Wbevh0gP1XiSFql0/xRioi9qbDs3C+g4Nv2N8jmv/BloIi/SYoy/mfw5vus2A==", "signatures": [{"sig": "MEUCIG2Oweq4BNJRNtgwmEN8QbtTkSXKfd4YoJJdFw0AolgiAiEAtdfwVkELEIEoGzBQub2p+fevNEXRLSvtfdEQ1ZI8T9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/cookie", "_from": ".", "files": ["lib"], "_shasum": "0b618a5565b6dea90bf3425d04d55edc475a7561", "engines": {"node": ">=0.8"}, "gitHead": "12d426678f77bd34dd1234b7acbf47b299f50439", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"punycode": "^1.4.1"}, "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie-2.3.3.tgz_1506027901957_0.13104762020520866", "host": "s3://npm-registry-packages"}}, "2.3.4": {"name": "tough-cookie", "version": "2.3.4", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.3.4", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "maratto", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "ec60cee38ac675063ffc97a5c18970578ee83655", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz", "fileCount": 9, "integrity": "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA==", "signatures": [{"sig": "MEQCIG2iW01+3jjm5npCBMwBUjHZbzfUo/kZijmWZL+Cs8OwAiAhU7UjqOnKnp7aFnAKA4uaZKlM2UQtKl+TbUXu+mrDzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245484}, "main": "./lib/cookie", "files": ["lib"], "engines": {"node": ">=0.8"}, "gitHead": "e4dfb0aec5d25e9e982805417a5d936071badc17", "scripts": {"test": "vows test/*_test.js", "suffixup": "curl -o public_suffix_list.dat https://publicsuffix.org/list/public_suffix_list.dat && ./generate-pubsuffix.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"punycode": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.3.4_1519684165015_0.21877903579709823", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "tough-cookie", "version": "2.4.2", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.4.2", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "maratto", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "aa9133154518b494efab98a58247bfc38818c00c", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.2.tgz", "fileCount": 9, "integrity": "sha512-vahm+X8lSV/KjXziec8x5Vp0OTC9mq8EVCOApIsRAooeuMPSO8aT7PFACYkaL0yZ/3hVqw+8DzhCJwl8H2Ad6w==", "signatures": [{"sig": "MEUCIQCkMJCrxKyKK5Y3aBRD0GZsJcVRbPc1BDBgiabHk7CltwIgJr6XgXavNjYPi+pqNZUi9pW29MVsJ2mJhtn7Ni5eW58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFcXVCRA9TVsSAnZWagAAebkP/3vI33XnffCWzttnMUuy\nlkEvl2WA4AEeGUWa9M2DQbMEWOMejI07yGblzrL8gngjTz1unuRNosrKVVwW\n8Bf0fR4ZzZamD3UzjhJpslqrXaXEcibK6MjYbdZD5EaN4GyYuzyVp77hQE3M\n5nZqNmgvI5/hgkLmtFJhuxg7IuKL1FTpWmSJYMu11Mv2ZlV9Ki6rGKcjuIAQ\n2UIomwxTfyKguXX5zeqH4F/J0Dq4f30ick8DfpHYwDwdCclLqVtIojVex961\nLNt5dsyVTVgMV9UuWWsYnNG9Z3vOT31z5ib5BxP0nG2Xzw17if4pS7R98leV\n9WQjJYxc7wpRv2E/27xSrLJxYu+5R6YiBhYyqv4jhxXfSchJTIRuUzRXfXEZ\nrn/gg8Xz1uKPOBGFu+wu6a+oWinOWQ7k2OTgdmIhu3YJqHN7kHvDfnLnQsXQ\nbQQ+fW+SDd0mHIKBHhYgnLd6mpLEHAQmaTON5a2ivQrRdGhm7htOoN7A3fhP\nTw5tq7AJLkpm7gIsswFwjFBrMNqnEWhVI71iP+UqN7AdtBVgbpjj1YPmJcQE\nHZ0ntcp4roksLzvZwQMtCKS8AGIXEFTWt7W/0LVsGTz72+EnB9Sw6l9IxMwp\nJaYswwVvCBfe010Nx6+LGp4ZBgGt19iirfHCesMFRm8Hs/lDNFm3uzCfiJoQ\nZ0Bs\r\n=Dc7H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "files": ["lib"], "engines": {"node": ">=0.8"}, "gitHead": "fae3ca83b100111b6b725c10107c3e5c112a60f1", "scripts": {"test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.4.2_1528153554661_0.48044532567468257", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "tough-cookie", "version": "2.4.3", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.4.3", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "maratto", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "53f36da3f47783b0925afa06ff9f3b165280f781", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.4.3.tgz", "fileCount": 9, "integrity": "sha512-Q5srk/4vDM54WJsJio3XNn6K2sCG+CQ8G5Wz6bZhRZoAe/+TxjWB/GlFAnYEbkYVlON9FMk/fE3h2RLpPXo4lQ==", "signatures": [{"sig": "MEQCICetFbo9YKADTmuQ0ii1aDWfGQbdb3yGQDhVX+XGEWqvAiBoGt2+lr51EM0MG4heI5fLinoJivS5ikY7p7lBpbiiFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMVcQCRA9TVsSAnZWagAAzX0P/2COkbzb3dOX/FTOYIVL\n7oHge7BvGODNkEHTjBtNsR0jO/AA/gVcUOq6CSmFH+UC14LrmCyNDemQvD15\njnYuJXUIiZsSZO7BGGbOFYrQF+DKLiwgf0LokrIM8KasZ5nxc5xS1jYHG5o+\nwCqutfhlRTqlPbZsexB+eRlIuYNvpsbRAdSDYXrhLJNMZgx49g/SZ7IwunGN\nJ/5Fe041MZqqdi7MAavM3T7vX9IvVRJJ3f4xAEVxY1vh4s0dN+h4fG2eMQzL\nOiaulRUIahs4xmJUYHZgZESc9eTIrI8K+YT7i3r35TGc0Du7ueB2KYxBqy7q\n6AUFbdX9Ep308kTVsTf/h4wE0RvdxKLfZ0ww7T1ivADX9xMi2oIvVtyhCQHo\nSIpyBhaav/fTufF0/Fu4Lcr7PMJxyvF0UcNRqrPmEQTw4UZkFKfT8HN2P5cH\nBvJsFwpeEjt84tt5pJ/bmD0j0EUCLUXLGH6fbA84fHpZGifO239zSf262Qdv\nSXeygU7YxnK8wOCnvlauZx/gp+/WTMfMdODxiX0QnqwS5BctiPCgGG1cKJcC\nqvTECaAEmxMVf/gEW73oxVXJgUO7f6jJaWoawV7ka+wR91W9Bp0pKGOOAxAG\nU72rmnwQKid9U7ly5833sb3D+lbzKJXGNIqohegP4daKZfNNTaLWZXmKE+Iy\n7Vml\r\n=8KpS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "files": ["lib"], "engines": {"node": ">=0.8"}, "gitHead": "671ad413b38d7464352886772f57a7ec417d4760", "scripts": {"test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.1", "async": "^1.4.2", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.4.3_1529960207843_0.4882910670728131", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "tough-cookie", "version": "2.5.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@2.5.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz", "fileCount": 10, "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "signatures": [{"sig": "MEQCIEYFbv3CWBGVbI7AMMc+Dlq7X1lqNc10as1vDexIldpEAiBkSdt+ctje2SmxUV7ShsCOifWPkP0EAcQrkBQGya0fUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/HqnCRA9TVsSAnZWagAABFsQAJO1aOX7ja9l926/wykB\np/V8EJ1GC61WAB7vpYqz8spGAX5Sc/RsMgj2af9BFIXzvj+jx/suXlcnnspP\nuti6WLq1J0xA+IC4HsDJrsw0N3c4ycPyi+Pg5R24nDGUaTbg5f8jcwy8pbmD\nBl/kwdxp5Z5qDmbyFtyk4wMi/i+z0NPBHi2Le9kwK+y5kArxgqpktgtRZSQ0\n//LkVmDHy28IGHSyDOjgGS/fcUfQUaHncbt642qvNa+T5AkSWBtb9fExByEW\n2EF1QOwN9UEfCT6VHf2SvtqCOskBr4ub45u2OChHdZICgkEXiSguQ+2HtU3E\nUzUy1gPCS6j39L1+t8nAlWaI6OAizY0iqg2uiRVS8N38SZrLYZk0hkA2Hlgj\nx2s4DivIs8Rg/0Ma1CRY3cMDbiZiN5/5G7ekGLhiI+eMvQkD4xV2MPIEE4cC\na8mncey6W59O8CnTrFePVx4o07TQarkGN/FU8RRhylOrSmLLnZqmwy/NgLIF\nL9d5kVlea+gDnCYx0EzJ5T1zeCMQMHvdavbf+3n/kLMjrlhDahlWkXII391I\nVjafX4/JzDMJcaF69CG3sVsdGTAhNYQPFDevJjRW5TOh3yF9dEPj3BJU37PE\nXhTEqYH4cZZrNwvz5QC5cBMIILXckxcvIitdlFRx8nVH1hJCSQPCn2izHFMa\nIGH7\r\n=NGDH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=0.8"}, "gitHead": "7c1fdf1322cbd1442b0bfb161aef2ac6554af19d", "scripts": {"test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "version": "genversion lib/version.js && git add lib/version.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "10.7.0", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.2", "async": "^1.4.2", "genversion": "^2.1.0", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_2.5.0_1543273126735_0.12462257759858342", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "tough-cookie", "version": "3.0.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@3.0.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "d2bceddebde633153ff20a52fa844a0dc71dacef", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-LHMvg+RBP/mAVNqVbOX8t+iJ+tqhBA/t49DuI7+IDAWHrASnesqSu1vWbKB7UrE2yk+HMFUBMadRGMkB4VCfog==", "signatures": [{"sig": "MEUCIQCaZ/iMALNDbmvaIPuTqka7rhC1ldXGYSZ+KlvV9sKeKAIgRctUJD7Xz6zxY7cnNvKjs7s9jjCBZVHAQBOFl+Zva5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87070, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNP+KCRA9TVsSAnZWagAADf4QAJi7MF7vm4rLLpmyny6X\nD+T4VwBe8XwV0PbhUeiGzKxYfGn1UEX5rS5tlU9ZhL4WxQ6u1Ay3ToXW/2co\nos1oDufOmsOs4Ws3OmBDJqucOFQ4N7S3gUgmm0bwsgXGtJiJ4Wi3FOjyT8Bg\nr5k13amk7SHWwirTrhPRe1uIa5sytnDrixsqOgvOp3oyBHAkgW48EWVXFjhQ\nptKKDCeHbpcT8tu3GsleoIfcp+Qy3BwqkFQyalsXxNAdOZz0nkLCEfbedIVF\nrA4q5kvTItaMNz1h4f9US01ppzjiq9AN100FGG8SRRvNmnbVfrwPjb7fSgIc\ncnaRX4r3liMpZnVjhbm8ODGAHnyubFvpSIoy9fWGhBbtR+OfDOM36DE3fylc\nhZcAI/ZjFthODwuSIbbGjcXXlRj666PMdpbpb+vK8PR7pN2UcDJueLdK+AZo\nLER9tpfiLHmoEq/dj5i0VFQysjZmEiY54Ub8NhClrYRctUz7A7DotINjmvk+\nIDCUqBy1b3o1OoBka8A/43FfEXfZWbWcqr5bF7JGT95qLQNqzAlFjiHXpn8x\nYuTpI5TO94GWTrd4srB3S8e9xskHR/+9eESxZJsRNeCdvE/IHKEtnE8yg30z\nku2fXb95Y0Wd2vW9UTYDCTFhm4deaXRXO2TBGcD4XbHFq4y520OQce9a2TqI\nZQOl\r\n=T9IA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "05b47134f8d62916c5a9f5ac0ad39a45ffd47ab4", "scripts": {"test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "version": "genversion lib/version.js && git add lib/version.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"psl": "^1.1.28", "ip-regex": "^3.0.0", "punycode": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.2", "async": "^1.4.2", "genversion": "^2.1.0", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_3.0.0_1546977161410_0.5851680674999464", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "tough-cookie", "version": "3.0.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@3.0.1", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "9df4f57e739c26930a018184887f4adb7dca73b2", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-yQyJ0u4pZsv9D4clxO69OEjLWYw+jbgspjTue4lTQZLfV0c5l1VmK2y1JK8E9ahdpltPOaAThPcp5nKPUgSnsg==", "signatures": [{"sig": "MEUCIQCSO6PKoMdqxvFYATUmRqzRMRf4E+o3K9DxlojbNTGQOgIgcDi6jxBtowe3g3CcuhwbqwMmWEkC55d8lh+5JUuJubU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWP56CRA9TVsSAnZWagAAsIAP/RlcRoDPI26puHk0sTVv\nlXuH+3rlk8xqX8w4ERWdhISu6ZznD3qBdSnGMPEI37QWUmC6WhQLYAJUkCf7\nJ7PNdqN7fQFLtpq4jyEzES5ccuASUzFN4qffJKVLRODQFvr/SAuP/hR7u0OC\n+x9Q5Z+TsYCKuDR31yaH+eTsgxDIfd6O3Z6PqunA5lJN169PDDNHhCcofZyU\n21D80ky/ETA6Z9PqTuO0zNdKog7xdEPuhfa3CZ7EqFBHpN61nXg3uaDkQjiy\nWFy94EbLkt/43ffku7xFyr043wE4n/bvtKHvZIYV8ysZwS2x96O6/cimrTy7\n9z74OFYGXDI75Phtb7lOqA8nQG6fkeEgIAKmw4PpCpEEW3oG+4jSswxbLVWs\n45G4mNpLx1+2UGTLQKoZzp63uReQfeOlkzkAy+n5yf8EEeLCKjHjFADw3po2\nTNAXyCtamUIPGm/z7P9IPxCCqTmdbO0LhCzv6RgjMSSi7nkpWC17y+FAo5st\n1/f5+ZSdR56nVDzhwjvK0fLJ9SHL9gQs1xIQKeI+vUbWefymRr0ql28DUheC\nckKdO+SwYSmVAC6qZOVvDCb1YRLzJtBOXNYOON4jKaYGRncFsQNWPPBlXI/B\noHpXZaVXybbF3fKIpZkoJAjzOIKbqhKL475XHy7Xpc8YZMLEsyWSN1CwABA8\nFR63\r\n=dLGc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "43507052a70751501d52aad38bd837bb2edfedd8", "scripts": {"test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "version": "genversion lib/version.js && git add lib/version.js"}, "_npmUser": {"name": "j<PERSON>sh", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"psl": "^1.1.28", "ip-regex": "^2.1.0", "punycode": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.6.0", "vows": "^0.8.2", "async": "^1.4.2", "genversion": "^2.1.0", "string.prototype.repeat": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_3.0.1_1549336185536_0.7227095442472071", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "tough-cookie", "version": "4.0.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@4.0.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ruoho", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "d822234eeca882f991f0f908824ad2622ddbece4", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-tHdtEpQCMrc1YLrMaqXXcj6AxhYi/xgit6mZu1+EDWUn+qhUf8wMQoFIy9NXuq23zAwtcB0t/MjACGR18pcRbg==", "signatures": [{"sig": "MEUCIAQCtnCbyPSjpZPPJBXuCaL5Q5mFYYmzTDWfoSMSMahpAiEA3+uUvmcDNRrJyZybJSRlT0qzllgFZX6YCzJvfwBGFqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJec8X6CRA9TVsSAnZWagAA50wQAI0neQjP1hgIBfW4V38C\nAz8VbNBRaMP/RVn4a2mWBS6Y8Spl4/vrgipcm4gHnY97GPsOahxJbvgPXyFV\nPjDKe0IcjReFMr7qTZJcg27Z99I1QqjNm0lSQffm2Kx+2CTzmM78qPFHCQLl\nQFjb09vmchVSIA29vcE1wsv64fP8XkaYQnjDBP6bb01mv8PBgNcnPQ+R1K6L\nFCzTtje3qayhlzMhLdcUp0nyCAge4XVrS/ihslnZyq/L5xgFPG+KiLTRSJH5\nQDXX5ZskkaHiRM4zSRb8lGY+NrefYQiE/I48d4Xt2G/6YNdmZ17J27iK7CKJ\ns1Wo/BZ87bDBHxHYS4YsSJKsyn3R+A5Wz1skjqXAXc2uuyGVaGaGcp4TiGGM\n2EEau0+/EkOxmW35uCxZEaJzgFnRZiHWNkrBXOSIfaws8dwZeyZ4YUvrz33H\nrkoeMqvABX7FqymFxHjOTgOvvXwTRaISj/RXQ0xLqtJZATS1m4Q+gOImYQ6N\nin6FdaBg912cnQsedw2NXxQYt0BfdboJ1pRN+9k4zAKROWzaiberMag1Wgzu\nvDvcXw96zKC3WVu5zmdrb8KVofNfaBFlyYW5txJEO4+D+BB/JQF3/KybFIk4\nR38qEmxeTJis696aIObBnD6OI/GVQbghX3Gk0Yw9/efPviCSvIq3O970QcRe\nKerl\r\n=LEUk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "2524513d49b7fab37639dfb7c6b87994c2bd7791", "scripts": {"test": "vows test/*_test.js", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "format": "npm run eslint -- --fix", "version": "genversion lib/version.js && git add lib/version.js", "prettier": "prettier '**/*.{json,ts,yaml,md}'"}, "_npmUser": {"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.0.0_1584645625620_0.2913485438151173", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "tough-cookie", "version": "4.1.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@4.1.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "039b203b2ad95cd9d2a2aae07b238cb83adc46c7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.0.tgz", "fileCount": 12, "integrity": "sha512-IVX6AagLelGwl6F0E+hoRpXzuD192cZhAcmT7/eoLr0PnsB1wv2E5c+A2O+V8xth9FlL2p0OstFsWn0bZpVn4w==", "signatures": [{"sig": "MEUCIH7xTNiCTc4n+A1zqf0zoCu7Li3R5NxzZqWdrQRBSb00AiEA5zLPz4Q/xy4dVJ8DSqfsc8MGMNRHCepmTIkGq/s104E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110945, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA753ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoU2BAAhlzXflBVF7ApbHwodg36nw9LoNAYU67PY6u6aJm2DJNjEHWy\r\nntLXUkvf4da7v1hgQ9ruDnRHOgcmBfgQF1hhDUw6Gcz2Q9UmZz8VOd0boTWQ\r\nsrKE9uGZzm6BO4QVBMd0gnQlfGI5ygwy8Z9CjIUuRhjTI94drPOngvyItu4m\r\nVpRj+9wMqhIwZ4rNmsW7dZSSWhLMtKQ3zjIPGj9IaiEufLeq/IkNnNn2zDdl\r\nNiQsXkx/vyssAiN969ctsc+hVCccMEljoFjAosOuQqHhAXeNNnEP3m4vVEB0\r\nz6muYFirvsNknByoW6Mr42XSd714PjPYzwc/7UNgBc+iX3QslwKyPEfi1JFe\r\nFSiJXZeo1a9huINCDJ2A6SmIcWPIhnlMVb6oJ74gj1JDTOY+awJCbwTCrIX8\r\n26VK8w0xaZDvjKJAvjleOp0yFAwd5qY0N7TcOhLpBJOEfcYTQRt1ySPCY8e0\r\n126gEGLEG2jjj86qoc1+UM6zqZD7f3Mu7Cl6eI3lmFhoyclDlibgzgTBFmBu\r\nKAejSYZw3AhwSy0C11NY5S7mW+CGOY0CkzHQZCKrt3K9jnP1/UxggiDKFJ7j\r\nPelZEkb3TbMQyV7QhBPNKJCBMEKKZdaQhgyt46DSl8mMSdsNCFI7kLXP0UNn\r\nRtg4JI6PlZr/pb9nOtsWmsbRRSHM3chQVZ4=\r\n=NI9r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "b0e46d17e9f84f29e5961455c251e58bf32e1318", "scripts": {"test": "vows test/*_test.js && npm run eslint", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "format": "npm run eslint -- --fix", "version": "genversion lib/version.js && git add lib/version.js", "prettier": "prettier '**/*.{json,ts,yaml,md}'"}, "_npmUser": {"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.1.0_1661189750923_0.06681954356680042", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "tough-cookie", "version": "4.1.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@4.1.1", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "3600960f1e1c83545f130ac80043b9de8e5d488b", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.1.tgz", "fileCount": 12, "integrity": "sha512-Ns3k8QxkEzIfLZbRwLOrMPDqRa1BEAl4BzNNAOYY4BhBmEkf+HvP467F4NrD9loK3NcYflWOpUH3LJg0ehq/rQ==", "signatures": [{"sig": "MEQCIAfoAhE+ryFWXRiRHEbJYExk27Xva65vMgt4iqqYO7mTAiBFTg0D+wDTNxeBxBSeqn7+8+nzFl2ucCmH3oNlxxfM8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBn4xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMcA/8DK3/t7wu2242cGOcdyBJDgiqXqD4OFrELjaqPo/V1l+KNdno\r\nTsJO/o+orPqd7JzQYVN7QpttgrlOS/5XXyc4LyFeq+VjbtSspSBLeKTxNDJN\r\nFTlcdNIR7n5HwLNCIwsDElzh2j4+ZpSN5fW+ZRrOOSZBPI/7CknyaGPogWR/\r\nsICzxDkl8LJMTsOUsdFKkHNBUVTIz59obFA5dGcqwCRS+3iBNmNEkFj9Jv3m\r\nnM4KrNAkDmPLF5VR82f/4YsSlo9h7MVb6oiXSSoFQh1NfJbOGljQNusZXq16\r\nZ4+sLuaOIVKR0NCOfaPB9rt9XhfHUfO6Sx8cYRt7ZkqCIoQMVlpSDGPcEmeU\r\nShLSZMChLKfZ3qcO6tYYR8LDujN7K9TVLsiimUe9jaB2Q24Ccf4gRMe0UCrp\r\nGDtaGLVsGpzjCyKGX6KaJGcSdrqwt/i+MZ7UBIKRB9Nicg3K+vAikjTCpF4O\r\n/Keic74Sh2YbSVpiiZZwKcOPRyrhK89cQuYMOkVdttJIzE3UAOBYNhKTJIRd\r\nJJDDIMLX4s+NYd89oYz2ufXz+SPzK1Zeu4vQA3cObhqAgbxwIYimQfD04MbU\r\nFX18PqqoE/31xoP5gRqOAXLJjTQ1seDmBrQw5H9wY+DMav/3pARjPt06PS3A\r\nwElCO0WyNhrewr9vLYTZueHHXdgTugTst6Y=\r\n=spPn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "ec707966e68a48199e646e2fa6b3055df6a280f0", "scripts": {"test": "vows test/*_test.js && npm run eslint", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "format": "npm run eslint -- --fix", "version": "genversion lib/version.js && git add lib/version.js", "prettier": "prettier '**/*.{json,ts,yaml,md}'"}, "_npmUser": {"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.1.1_1661369905709_0.06618894296530997", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "tough-cookie", "version": "4.1.2", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@4.1.2", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "e53e84b85f24e0b65dd526f46628db6c85f6b874", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.2.tgz", "fileCount": 12, "integrity": "sha512-G9fqXWoYFZgTc2z8Q5zaHy/vJMjm+WV0AkAeHxVCQiEB1b+dGvWzFW6QV07cY5jQ5gRkeid2qIkzkxUnmoQZUQ==", "signatures": [{"sig": "MEUCIEUy+pxJNMBHOD4GkhPqjOJcehTbIJ0yaor75oZhm3AFAiEAhZ2tTh5u6TgISSHV96ABMlH50z/CPMLqdwJGRyaK4ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB8SbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqz6w//TBi0Ctct6cZws8JksB34lSki0wEC1cnhTbJ/ualMjNICM1Jf\r\nAb+OWdIsCe3PBGWCfgTgIs6qY4gJ02OeD9XKAXrPpw1ZnPyqjfsLTdSZqx1p\r\nPaoLaOI4CH56eeV7nsQb1WgDfuJ0hHRVTPEdfm4VI4H8uYL1FEbBjfqnJLAv\r\nYj2sbsSL6QCBTe1O0XHrdi+Uq4kXR2UdYniKo9oAyZv9m0g4nuToqn1bv9a2\r\n3dNK0gAYj5nGQ5ZuuFjhpeYXe5V3DGI5slfB3oObckjmVm0JnRGL2W/+BwyF\r\n0wUfKEepixlHEBgRC07YOTcj/Mg7+z/9iQ8elhuLU8sLmirCllFk5TU9hgkk\r\njt+r2U8XAvbghgu07tZAocf8pjuemNcO7HL/gFxYAQZV5GS+c/8Lif7p94rX\r\nZzFIVhEgBMZz5oHTxBnYXI1dxcAvLGO4R/lslLyGSR/N38NJorBi8c/PrYPy\r\nrfP9Q51/fEiQjcK7su70SgZcGcAWr5zIU48rV68PFJZiW2lXAvATJYUTp0Gp\r\nsn5V346saQHBMLRViSR+XZkrc11uKXzKPpHPyanK6oAcQo+L609bOFufJql+\r\nDHekjeick6DniFQ5PraGKqHGWkWLpY2jtXs8OAkA6nb1G6J3vz0OXN8YI1C4\r\nxBR3Gq9O3L/u+zdW5EL3xboZ9sHtRJUWE3c=\r\n=nD4R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "b1a8898ee3f8af52c6c1c355555d9f50ebe626ce", "scripts": {"test": "vows test/*_test.js && npm run eslint", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "format": "npm run eslint -- --fix", "version": "genversion lib/version.js && git add lib/version.js", "prettier": "prettier '**/*.{json,ts,yaml,md}'"}, "_npmUser": {"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.1.2_1661453467031_0.7757909706599775", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "tough-cookie", "version": "4.1.3", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@4.1.3", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON>sh", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "97b9adb0728b42280aa3d814b6b999b2ff0318bf", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.3.tgz", "fileCount": 12, "integrity": "sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw==", "signatures": [{"sig": "MEUCIAZVXfCk1+POemg+4/Bq/dKZv3MmPUc6CHSGq8Uoy2RkAiEA25Q3TsaeQ8yzBNfscUbA7hD4QJ6pE43n4Hu3YpayOY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111456}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "4ff4d29f6cefd279a412b8d62a21142ebd410b36", "scripts": {"test": "vows test/*_test.js && npm run eslint", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "format": "npm run eslint -- --fix", "version": "genversion lib/version.js && git add lib/version.js", "prettier": "prettier '**/*.{json,ts,yaml,md}'"}, "_npmUser": {"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "16.18.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.1.3_1685986361330_0.4220147634345712", "host": "s3://npm-registry-packages"}}, "5.0.0-rc.0": {"name": "tough-cookie", "version": "5.0.0-rc.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.0.0-rc.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "c847afb84d51923d3a114fb72109de77061f3a56", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.0.tgz", "fileCount": 12, "integrity": "sha512-wxZlZr3cZNegRzh+4R2VYJvtpX81BwGsoDUREM2ndENZJzvXBnAKO+IWumePhYGpB8n76LfAFp0Rl9an+QKd4Q==", "signatures": [{"sig": "MEUCIQDjhaNibfGK695H9+sfdkjHSlmMBmQIvh1F8nkrOzVaOwIgF0bIuyZncu3GSygzL1P8fZisKErTj4MTMepHAO21mQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71779}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "5282a62d775b9d253777f2a670ec7b99228178ee", "scripts": {"test": "npm run test:ts && npm run test:legacy", "build": "npm run clean && tsc", "clean": "rm -rf dist", "cover": "jest --coverage", "eslint": "eslint --env node --ext .ts .", "format": "npm run eslint -- --fix", "test:ts": "jest", "version": "genversion --es6 lib/version.ts && git add lib/version.ts", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "typecheck": "tsc --noEmit", "test:legacy": "npm run build -- --declaration false && vows test/*_test.js"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.5.0", "vows": "^0.8.2", "async": "2.6.4", "eslint": "^8.36.0", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "prettier": "^2.8.7", "@types/psl": "^1", "genversion": "^3.1.1", "typescript": "^4.9.5", "@types/jest": "^29", "@types/node": "^16.18.23", "@types/punycode": "^2", "@types/url-parse": "^1.4.8", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.57.0", "@typescript-eslint/eslint-plugin": "^5.57.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.0.0-rc.0_1695830808215_0.49519212351455444", "host": "s3://npm-registry-packages"}}, "5.0.0-rc.1": {"name": "tough-cookie", "version": "5.0.0-rc.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.0.0-rc.1", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "a27d86bf7ae96be7acfe527cdf5db247ce0cfbdb", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.1.tgz", "fileCount": 41, "integrity": "sha512-2XI8gvpALkNg6hFxQfwllhjmDx54u6KQojske77JGNmjRGvF1UAfF5lPaOp1Vm95VKZp2k9J6YBR2RiXcgPHvQ==", "signatures": [{"sig": "MEQCIHiewU9EcjmIgorU0bc11x2QmGWpfRcFrHGGcOvH73L2AiBb9CORNQIkSIofYgpXKKnpqGujKoKpZcDxFgiGwFOK6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166577}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "7e9db61b10934e7622027638d3edf7f4719a8ec7", "scripts": {"test": "npm run test:ts && npm run test:legacy", "build": "npm run clean && tsc", "clean": "rm -rf dist", "cover": "jest --coverage", "eslint": "eslint --env node --ext .ts .", "format": "npm run eslint -- --fix", "test:ts": "jest", "version": "genversion --es6 lib/version.ts && git add lib/version.ts", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "typecheck": "tsc --noEmit", "test:legacy": "npm run build -- --declaration false && vows test/*_test.js"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"tldts": "^6.1.0", "punycode": "^2.3.1", "url-parse": "^1.5.10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.54.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "prettier": "^3.1.0", "@types/psl": "^1.1.3", "genversion": "^3.1.1", "typescript": "4.9.5", "@types/jest": "^29.5.10", "@types/node": "^14.18.63", "@types/punycode": "^2.1.4", "@types/url-parse": "^1.4.11", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "@typescript-eslint/parser": "^6.13.1", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.0.0-rc.1_1709230871172_0.20212696356923665", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "tough-cookie", "version": "4.1.4", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@4.1.4", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "945f1461b45b5a8c76821c33ea49c3ac192c1b36", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz", "fileCount": 12, "integrity": "sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==", "signatures": [{"sig": "MEYCIQCudAtt/UQBKzfveaDCOKQjGa5Kqo8hHdjOltvnsIvceQIhAIGpaDQ63bpISPTPWUGQYJ0howz1egmDQ8Qx2jBY/vXL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111787}, "main": "./lib/cookie", "engines": {"node": ">=6"}, "gitHead": "cacbc37936bd4824693d885e1e65dca626ed3c8c", "scripts": {"test": "vows test/*_test.js && npm run eslint", "cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "eslint": "eslint --env node --ext .js .", "format": "npm run eslint -- --fix", "version": "genversion lib/version.js && git add lib/version.js", "prettier": "prettier '**/*.{json,ts,yaml,md}'"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "url-parse": "^1.5.3", "universalify": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "vows": "^0.8.2", "async": "^2.6.2", "eslint": "^5.16.0", "prettier": "^1.17.0", "genversion": "^2.1.0", "eslint-config-prettier": "^4.2.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_4.1.4_1714399902824_0.6785346316271987", "host": "s3://npm-registry-packages"}}, "5.0.0-rc.2": {"name": "tough-cookie", "version": "5.0.0-rc.2", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.0.0-rc.2", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "ff21e01f13c1223880e26886989355a18bac5dab", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.2.tgz", "fileCount": 22, "integrity": "sha512-dwe5OkVHGe1NinilLDJWapqyQyHBtxlai0qWhIjysY7nSnATpsqhnfIEkZTTJ5HUdECz8V14zf015GF7V+xACA==", "signatures": [{"sig": "MEQCIARQ42vAn4AWlBXge+vc1A2d+EaXvhuceSdR7bk2IL88AiADmPpybRyWeOAF78ExeXHlBZ3FeLwAO7Gqb2g70xuAOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142745}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "42270f50bd96f7b1a16f372eb06681f10555a96f", "scripts": {"lint": "eslint .", "test": "npm run test:ts && npm run test:legacy", "build": "npm run clean && tsc", "clean": "rm -rf dist", "cover": "jest --coverage", "eslint": "eslint .", "format": "npm run eslint -- --fix", "api:dev": "npm run build && npm run api:extract -- --local && npm run api:docs", "test:ts": "jest", "version": "genversion --template version-template.ejs --force lib/version.ts && git add lib/version.ts", "api:docs": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "typecheck": "tsc --noEmit", "api:extract": "api-extractor run --verbose", "test:legacy": "npm run build -- --declaration false && ./test/scripts/vows.js test/*_test.js"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"tldts": "^6.1.14", "punycode": "^2.3.1", "url-parse": "^1.5.10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.57.0", "globals": "^14.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "prettier": "^3.2.5", "@eslint/js": "^8.57.0", "genversion": "^3.2.0", "typescript": "4.9.5", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "@types/punycode": "^2.1.4", "@types/url-parse": "^1.4.11", "typescript-eslint": "^7.3.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@microsoft/api-extractor": "^7.42.3", "@microsoft/api-documenter": "^7.23.37"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.0.0-rc.2_1715889780005_0.517700868152305", "host": "s3://npm-registry-packages"}}, "5.0.0-rc.3": {"name": "tough-cookie", "version": "5.0.0-rc.3", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.0.0-rc.3", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "dist": {"shasum": "786a62bda1de3c4eeaf2dcd3cd596eeeb4c8aa27", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.3.tgz", "fileCount": 22, "integrity": "sha512-vPx5Wz/Vz4NwE0uFUgwZfZfmn3RVXSnofUAmFPQNMuA2cNxFcPyX6f2d3GgpQWyGswzzZeZSv3G64DaRkjmQdA==", "signatures": [{"sig": "MEYCIQC1QgYepWY3T2EHyNi3iVNbS+Je1y3+imSGjJvp4SUQogIhAJaFvIJn8tVOB2ZufiD0Le0wWAPoggAV1CDt71iMholk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138592}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "a1c19617bfdb99dedd1c48e854d355a50a75c7ae", "scripts": {"lint": "eslint .", "test": "npm run test:ts && npm run test:legacy", "build": "npm run clean && tsc", "clean": "rm -rf dist", "cover": "jest --coverage", "eslint": "eslint .", "format": "npm run eslint -- --fix", "api:dev": "npm run build && npm run api:extract -- --local && npm run api:docs", "test:ts": "jest", "version": "genversion --template version-template.ejs --force lib/version.ts && git add lib/version.ts", "api:docs": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "typecheck": "tsc --noEmit", "api:extract": "api-extractor run --verbose", "test:legacy": "npm run build -- --declaration false && ./test/scripts/vows.js test/*_test.js"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"tldts": "^6.1.28", "punycode": "^2.3.1", "url-parse": "^1.5.10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.57.0", "globals": "^15.6.0", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "prettier": "^3.3.2", "@eslint/js": "^9.5.0", "genversion": "^3.2.0", "typescript": "5.5.2", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "@types/punycode": "^2.1.4", "@types/url-parse": "^1.4.11", "typescript-eslint": "^7.13.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@microsoft/api-extractor": "^7.47.0", "@microsoft/api-documenter": "^7.25.3"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.0.0-rc.3_1720617798118_0.28452030128009786", "host": "s3://npm-registry-packages"}}, "5.0.0-rc.4": {"name": "tough-cookie", "version": "5.0.0-rc.4", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.0.0-rc.4", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "dist": {"shasum": "3a894d4ec329dab4bc4a39cc04f58d3f0c6a8459", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0-rc.4.tgz", "fileCount": 22, "integrity": "sha512-EN59UG6X/O6Nz2p21O6UK8R97zvLETOZ9+FGNdo56VuJZ8cftVCZ6tyxvedkQBfcX22avA1HY+4n04OVT2q6cw==", "signatures": [{"sig": "MEUCIQD6/oQpPR7M8HBcfo/M64/NEHmGKUfMI73g5+T5MzmYtgIgdXbI91f0rPvdajwUDP0qP/2sS+VLqcN7Ljabf1+aaPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138979}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "a9090908ce105dd365bc96e6d18a5b1d4d72cad8", "scripts": {"lint": "eslint .", "test": "npm run test:ts && npm run test:legacy", "build": "npm run clean && tsc", "clean": "rm -rf dist", "cover": "jest --coverage", "eslint": "eslint .", "format": "npm run eslint -- --fix", "api:dev": "npm run build && npm run api:extract -- --local && npm run api:docs", "test:ts": "jest", "version": "genversion --template version-template.ejs --force lib/version.ts && git add lib/version.ts", "api:docs": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "typecheck": "tsc --noEmit", "api:extract": "api-extractor run --verbose", "test:legacy": "npm run build -- --declaration false && ./test/scripts/vows.js test/*_test.js"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "22.5.0", "dependencies": {"tldts": "^6.1.32"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.5", "eslint": "^8.57.0", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^7.16.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.0.0-rc.4_1721410940642_0.3547248218809458", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "tough-cookie", "version": "5.0.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.0.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "dist": {"shasum": "6b6518e2b5c070cf742d872ee0f4f92d69eac1af", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.0.0.tgz", "fileCount": 41, "integrity": "sha512-FRKsF7cz96xIIeMZ82ehjC3xW2E+O2+v11udrDYewUbszngYhsGa8z6YUMMzO9QJZzzyd0nGGXnML/TReX6W8Q==", "signatures": [{"sig": "MEYCIQDCO6TMg6HJQ1gWs358/m6vJnSoxNOazm8CXqS6JFgJygIhAP54FWqqiZNA7Yy+C77RK5d+Fk8S2Qas+MY9f5g+NAM1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224687}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "7ed1b8a8ee8fd650da9507b25ebbf99bc1b36111", "scripts": {"lint": "eslint .", "test": "npm run test:ts && npm run test:legacy", "build": "npm run clean && tsc", "clean": "rm -rf dist", "cover": "jest --coverage", "eslint": "eslint .", "format": "npm run eslint -- --fix", "api:dev": "npm run build && npm run api:extract -- --local && npm run api:docs", "test:ts": "jest", "version": "genversion --template version-template.ejs --force lib/version.ts && git add lib/version.ts", "api:docs": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "prettier": "prettier '**/*.{json,ts,yaml,md}'", "typecheck": "tsc --noEmit", "api:extract": "api-extractor run --verbose", "test:legacy": "npm run build -- --declaration false && ./test/scripts/vows.js test/*_test.js"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "22.5.0", "dependencies": {"tldts": "^6.1.32"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.0.0_1725900028652_0.777149806341334", "host": "s3://npm-registry-packages"}}, "5.1.0-rc.0": {"name": "tough-cookie", "version": "5.1.0-rc.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.1.0-rc.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "dist": {"shasum": "9d36d4c3e230316e7bbdef84e4bfa4c622c9cbb7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.0-rc.0.tgz", "fileCount": 41, "integrity": "sha512-DGT34m1GwNGWor4+fn7RwYYZ2O0DkAkkjteHj355Vmnto7JXUvKxK1o3DJolNm6NuzQ+bX2rqpQOu+Z3A4e7aA==", "signatures": [{"sig": "MEYCIQCFIcmoM1dIUPUFn0laeBfjihByNiq9+3SAX4FE8nD5tgIhAM/YXHPMyJNhiHtHd0FnW4liH5iguRjlZPOrWlqxsTh0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.0-rc.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227329}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "b407f60a44f5d05e64e6ff9b7adf2529230035b3", "scripts": {"lint": "npm run _lint:check", "test": "npm run build && npm run _test:ts && npm run _test:legacy", "build": "npm run _build:clean && npm run _build:compile", "prepack": "npm run build", "version": "npm run _version:generate && npm run prepare-pr && git add --renormalize .", "_test:ts": "jest", "_docs:fix": "prettier ./api/docs --write", "_lint:fix": "eslint . --fix", "_api:check": "api-extractor run --verbose", "prepare-pr": "npm test && npm run _api:update && npm run _docs:generate && npm run _format:fix && npm run _lint:fix", "_api:update": "api-extractor run --verbose --local", "_format:fix": "prettier . --write", "_lint:check": "eslint .", "_build:clean": "rm -rf dist", "_test:legacy": "./test/scripts/vows.js test/*_test.js", "_format:check": "prettier . --check", "_build:compile": "tsc", "_docs:generate": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "_version:generate": "genversion --template version-template.ejs --force lib/version.ts"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"tldts": "^6.1.32"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.1.0-rc.0_1736362662085_0.28283880063400013", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.0": {"name": "tough-cookie", "version": "5.1.0", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.1.0", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "dist": {"shasum": "0667b0f2fbb5901fe6f226c3e0b710a9a4292f87", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.0.tgz", "fileCount": 41, "integrity": "sha512-rvZUv+7MoBYTiDmFPBrhL7Ujx9Sk+q9wwm22x8c8T5IJaR+Wsyc7TNxbVxo84kZoRJZZMazowFLqpankBEQrGg==", "signatures": [{"sig": "MEUCIQCTfRH/dlcWhoj0Cz3JI9x6Ky7gnwPAXEerrXVyf73OKQIgEs9QM2rgnMnSJEHBtml50rP7br2fo6CK8+OikfjwjGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227531}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "f27648d488efb746046a8631c41c70eb7c84c0df", "scripts": {"lint": "npm run _lint:check", "test": "npm run build && npm run _test:ts && npm run _test:legacy", "build": "npm run _build:clean && npm run _build:compile", "prepack": "npm run build", "version": "npm run _version:generate && npm run prepare-pr && git add --renormalize .", "_test:ts": "jest", "_docs:fix": "prettier ./api/docs --write", "_lint:fix": "eslint . --fix", "_api:check": "api-extractor run --verbose", "prepare-pr": "npm test && npm run _api:update && npm run _docs:generate && npm run _format:fix && npm run _lint:fix", "_api:update": "api-extractor run --verbose --local", "_format:fix": "prettier . --write", "_lint:check": "eslint .", "_build:clean": "rm -rf dist", "_test:legacy": "./test/scripts/vows.js test/*_test.js", "_format:check": "prettier . --check", "_build:compile": "tsc", "_docs:generate": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "_version:generate": "genversion --template version-template.ejs --force lib/version.ts"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"tldts": "^6.1.32"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.1.0_1736437016677_0.7065386195345622", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.1": {"name": "tough-cookie", "version": "5.1.1", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.1.1", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "dist": {"shasum": "4641c1fdbf024927e29c5532edb7b6e5377ea1f2", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.1.tgz", "fileCount": 41, "integrity": "sha512-Ek7HndSVkp10hmHP9V4qZO1u+pn1RU5sI0Fw+jCU3lyvuMZcgqsNgc6CmJJZyByK4Vm/qotGRJlfgAX8q+4JiA==", "signatures": [{"sig": "MEUCIQCp2EuNsKUEbxXoDICrrll2cT3eIN5cyC2SWVOizIRhMQIgUWtsOLIIvkBD/jiT/jKHevkcUsCagHsqO4Vp2BX270Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227729}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "28993365a0049b0025d9d67f6b11224c68d8476c", "scripts": {"lint": "npm run _lint:check", "test": "npm run build && npm run _test:ts && npm run _test:legacy", "build": "npm run _build:clean && npm run _build:compile", "prepack": "npm run build", "version": "npm run _version:generate && npm run prepare-pr && git add --renormalize .", "_test:ts": "jest", "_docs:fix": "prettier ./api/docs --write", "_lint:fix": "eslint . --fix", "_api:check": "api-extractor run --verbose", "prepare-pr": "npm test && npm run _api:update && npm run _docs:generate && npm run _format:fix && npm run _lint:fix", "_api:update": "api-extractor run --verbose --local", "_format:fix": "prettier . --write", "_lint:check": "eslint .", "_build:clean": "rm -rf dist", "_test:legacy": "./test/scripts/vows.js test/*_test.js", "_format:check": "prettier . --check", "_build:compile": "tsc", "_docs:generate": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "_version:generate": "genversion --template version-template.ejs --force lib/version.ts"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"tldts": "^6.1.32"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-plugin-import": "^2.31.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7", "eslint-import-resolver-typescript": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.1.1_1738949546657_0.5904139029798894", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.2": {"name": "tough-cookie", "version": "5.1.2", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "tough-cookie@5.1.2", "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "homepage": "https://github.com/salesforce/tough-cookie", "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "dist": {"shasum": "66d774b4a1d9e12dc75089725af3ac75ec31bed7", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz", "fileCount": 41, "integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "signatures": [{"sig": "MEUCIGFarTXljj47LpUT1Q4IBND0NiHP9QPQKBsaSQAsVSS0AiEAh5LJWLi6BcsNEA2g81q1VGkFI+TIYiXg7nrznBfDlo0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/tough-cookie@5.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 227724}, "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "engines": {"node": ">=16"}, "gitHead": "a2c72efe202a2050e9e201d5a911680fb7e1a19c", "scripts": {"lint": "npm run _lint:check", "test": "npm run build && npm run _test:ts && npm run _test:legacy", "build": "npm run _build:clean && npm run _build:compile", "prepack": "npm run build", "version": "npm run _version:generate && npm run prepare-pr && git add --renormalize .", "_test:ts": "jest", "_docs:fix": "prettier ./api/docs --write", "_lint:fix": "eslint . --fix", "_api:check": "api-extractor run --verbose", "prepare-pr": "npm test && npm run _api:update && npm run _docs:generate && npm run _format:fix && npm run _lint:fix", "_api:update": "api-extractor run --verbose --local", "_format:fix": "prettier . --write", "_lint:check": "eslint .", "_build:clean": "rm -rf dist", "_test:legacy": "./test/scripts/vows.js test/*_test.js", "_format:check": "prettier . --check", "_build:compile": "tsc", "_docs:generate": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "_version:generate": "genversion --template version-template.ejs --force lib/version.ts"}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"tldts": "^6.1.32"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "vows": "^0.8.3", "async": "3.2.6", "eslint": "^9.9.1", "globals": "^15.8.0", "ts-jest": "^29.2.2", "ts-node": "^10.9.2", "prettier": "^3.3.3", "@eslint/js": "^9.7.0", "genversion": "^3.2.0", "typescript": "5.5.3", "@types/jest": "^29.5.12", "@types/node": "^16.18.101", "typescript-eslint": "^8.0.1", "eslint-plugin-import": "^2.31.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.1", "@microsoft/api-extractor": "^7.47.2", "@microsoft/api-documenter": "^7.25.7", "eslint-import-resolver-typescript": "^3.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/tough-cookie_5.1.2_1740767269730_0.7461357982271695", "host": "s3://npm-registry-packages-npm-production"}}, "6.0.0-rc.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "license": "BSD-3-<PERSON><PERSON>", "name": "tough-cookie", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "version": "6.0.0-rc.0", "homepage": "https://github.com/salesforce/tough-cookie", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "type": "module", "main": "./dist/cookie/index.js", "types": "./dist/cookie/index.d.ts", "scripts": {"build": "npm run _build:clean && npm run _build:compile", "lint": "npm run _lint:check", "prepack": "npm run build", "prepare-pr": "npm run build && npm test -- run && npm run _api:update && npm run _docs:generate && npm run _format:fix && npm run _lint:fix", "test": "vitest", "version": "npm run _version:generate && npm run prepare-pr && git add --renormalize .", "_api:check": "api-extractor run --verbose", "_api:update": "api-extractor run --verbose --local", "_build:clean": "rm -rf dist", "_build:compile": "tsc", "_docs:generate": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "_docs:fix": "prettier ./api/docs --write", "_format:check": "prettier . --check", "_format:fix": "prettier . --write", "_lint:check": "eslint .", "_lint:fix": "eslint . --fix", "_version:generate": "genversion --template version-template.ejs --force lib/version.ts"}, "//": "We only support node 18+, but v16 still works. We won't block v16 until it becomes a burden.", "engines": {"node": ">=16"}, "devDependencies": {"@eslint/js": "^9.24.0", "@microsoft/api-documenter": "^7.26.20", "@microsoft/api-extractor": "^7.52.3", "@types/node": "^18.19.79", "@vitest/eslint-plugin": "^1.1.40", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "genversion": "^3.2.0", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "5.5.3", "typescript-eslint": "^8.29.1", "vitest": "^3.1.1"}, "dependencies": {"tldts": "^7.0.5"}, "_id": "tough-cookie@6.0.0-rc.0", "readmeFilename": "README.md", "gitHead": "3e642dc777b6f56e47521d88ad6fefbb5293e474", "_nodeVersion": "22.5.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-HT9g/ttJnTxeqHtiEQvcJlftobAsjhNyNhY/S0SIYmE/U6VBoL4gvULFDzQ6Z4zvirG1nYvqLy62Jd80YqBaEg==", "shasum": "8e5a22714d553d95de6b5d7a6247f2dd826c5548", "tarball": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-6.0.0-rc.0.tgz", "fileCount": 43, "unpackedSize": 225735, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCWXdbeByltrEIaqtzz5KzCaeOZLP1pzXOlHTNqfCOE3gIhAN+9TjDVmQCC8A50234tY8CYaeHOBjAa71vYtUQF+Nfh"}]}, "_npmUser": {"name": "ccasey", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tough-cookie_6.0.0-rc.0_1746193842950_0.6890586086950925"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-10-21T19:06:03.548Z", "modified": "2025-05-02T13:50:43.367Z", "0.9.0": "2011-10-21T19:06:03.972Z", "0.9.1": "2011-10-31T20:14:14.089Z", "0.9.3": "2011-11-07T22:32:46.899Z", "0.9.4": "2011-11-23T15:55:51.823Z", "0.9.5": "2011-11-23T16:09:51.761Z", "0.9.6": "2011-11-23T19:26:54.217Z", "0.9.7": "2011-12-01T23:14:08.946Z", "0.9.8": "2011-12-15T16:03:48.038Z", "0.9.9": "2012-04-17T02:38:36.426Z", "0.9.11": "2012-04-23T17:02:57.971Z", "0.9.12": "2012-04-25T16:48:25.708Z", "0.9.13": "2012-05-08T16:21:30.704Z", "0.9.14": "2012-09-28T17:45:14.613Z", "0.9.15": "2013-01-25T17:35:07.444Z", "0.10.0": "2014-01-10T21:47:05.533Z", "0.11.0": "2014-01-13T18:21:15.484Z", "0.12.0": "2014-01-13T21:32:46.961Z", "0.12.1": "2014-01-16T18:26:00.164Z", "0.13.0": "2015-04-22T01:25:50.445Z", "1.0.0": "2015-04-28T01:38:47.750Z", "1.1.0": "2015-04-28T18:14:01.870Z", "1.2.0": "2015-05-25T16:49:39.201Z", "2.0.0": "2015-06-10T22:13:55.825Z", "2.1.0": "2015-10-02T17:49:06.735Z", "2.2.0": "2015-10-06T23:18:58.173Z", "2.2.1": "2015-11-13T01:52:06.393Z", "2.2.2": "2016-03-09T23:03:59.570Z", "2.3.0": "2016-07-21T18:44:18.142Z", "2.3.1": "2016-07-26T01:01:31.341Z", "2.3.2": "2016-10-25T17:07:13.167Z", "2.3.3": "2017-09-21T21:05:04.804Z", "2.3.4": "2018-02-26T22:29:25.118Z", "2.4.2": "2018-06-04T23:05:54.750Z", "2.4.3": "2018-06-25T20:56:48.465Z", "2.5.0": "2018-11-26T22:58:46.850Z", "3.0.0": "2019-01-08T19:52:41.586Z", "3.0.1": "2019-02-05T03:09:45.657Z", "4.0.0": "2020-03-19T19:20:25.746Z", "4.1.0": "2022-08-22T17:35:51.223Z", "4.1.1": "2022-08-24T19:38:25.911Z", "4.1.2": "2022-08-25T18:51:07.208Z", "4.1.3": "2023-06-05T17:32:41.498Z", "5.0.0-rc.0": "2023-09-27T16:06:48.398Z", "5.0.0-rc.1": "2024-02-29T18:21:11.363Z", "4.1.4": "2024-04-29T14:11:42.984Z", "5.0.0-rc.2": "2024-05-16T20:03:00.248Z", "5.0.0-rc.3": "2024-07-10T13:23:18.288Z", "5.0.0-rc.4": "2024-07-19T17:42:20.873Z", "5.0.0": "2024-09-09T16:40:28.848Z", "5.1.0-rc.0": "2025-01-08T18:57:42.291Z", "5.1.0": "2025-01-09T15:36:56.870Z", "5.1.1": "2025-02-07T17:32:26.842Z", "5.1.2": "2025-02-28T18:27:49.925Z", "6.0.0-rc.0": "2025-05-02T13:50:43.145Z"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/salesforce/tough-cookie", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "repository": {"url": "git://github.com/salesforce/tough-cookie.git", "type": "git"}, "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "maintainers": [{"name": "a<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ccasey", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"cath": true, "etiv": true, "mrxf": true, "cliff": true, "axelav": true, "asilvas": true, "ezodude": true, "jovinbm": true, "mygoare": true, "sshawuk": true, "xtx1130": true, "cdokolas": true, "porkbits": true, "alexminza": true, "allen_lyu": true, "gavinuhma": true, "milfromoz": true, "mjurincic": true, "mojaray2k": true, "rossdavis": true, "sasquatch": true, "mattkelley": true, "monolithed": true, "richpowell": true, "marcelotrad": true, "david-martin": true, "reecegoddard": true, "giordanocardillo": true}}