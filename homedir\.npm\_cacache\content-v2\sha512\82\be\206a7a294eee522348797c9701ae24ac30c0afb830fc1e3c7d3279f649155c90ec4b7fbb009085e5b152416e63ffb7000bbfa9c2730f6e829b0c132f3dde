{"name": "wildcard", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.0.1": {"name": "wildcard", "version": "0.0.1", "devDependencies": {"mocha": "*", "expect.js": "0.1.x"}, "dist": {"shasum": "59bd7157156a039c318223b135e6f0d9c389b326", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.0.1.tgz", "integrity": "sha512-EmgdXd6xFM+vHMEEiB0+s5Z/NkLg5ooXzjHqj0hiEsdAELf27zhUnLjJdm4x5dVmXM/Jw2Qnl9c/jG+EgeigAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDST1VDOpiwjH36tAEEZuBtlQXxXvNMId9Ch7zE97PJ6QIhAItRnNRcKcCHcvX8fGxlFHvnzFYcvQPYD3HcpjRN6QOn"}]}, "engines": {"node": ">= 0.4.x < 0.7.0"}}, "0.1.2": {"name": "wildcard", "version": "0.1.2", "devDependencies": {"mocha": "*", "expect.js": "0.1.x"}, "dist": {"shasum": "704b7639baea4dd805532fa22fca84f6bf45e595", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.2.tgz", "integrity": "sha512-SvoRa/52rh67wYOLR39QyC5pLFdDkB8dowWUznY8hGk2U85PtmqgTF1mp37KGs/3tOJkikRtiSKEeaoTgmw5Bw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvwlTrkB4hH/U7ZKi1LIum00/Zc3XvBSjKYbZ4FAALXwIgEL7NuCD7bibcC0JaYVHKXmM6EUBQSfroHvk+ppPcDig="}]}, "engines": {"node": ">= 0.6.x < 0.9.0"}}, "0.1.3": {"name": "wildcard", "version": "0.1.3", "devDependencies": {"mocha": "1.6.x", "expect.js": "0.1.x"}, "dist": {"shasum": "581963b5c937bc1f0c624372079a04a3d6d9a35c", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.3.tgz", "integrity": "sha512-EGAKb/75SczLYbuG2RnZIggpDjSLfPykN96cXe0l0QGVPnZjAL/dHLX33Lr+aODMh6gEjzn7jfgdPlAGLor3oQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDILkbP/wnlMVNKwVDDHu0kyBD9x8mEZ3lYai82pCvAkAIgV/rEyqx2NPsOxq9wJsrZliQ7Di4QmkwIwCTFHgw+D80="}]}, "engines": {"node": ">= 0.6.x < 0.9.0"}}, "0.1.4": {"name": "wildcard", "version": "0.1.4", "devDependencies": {"tape": "1.0.x"}, "dist": {"shasum": "f14bd497676e80098122cc3ce91b2401f18450df", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.4.tgz", "integrity": "sha512-HN6xUvGoAyCRPUTSPvDub7kTWj0vq9zGsecWdAtOH5xtRxQU7esRuRqrecDIRlPtSej1tDzJqPiFWedi1LhSpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAqaDK1njLaQcRJKQYXANJOHxal+taNaUOS1YyyI8tAwIhAMvinVFQWaboeVMWxGmvEJ8oR/Cl6l30aNwgAaNjFhWC"}]}}, "0.1.5": {"name": "wildcard", "version": "0.1.5", "devDependencies": {"tape": "^2"}, "directories": {"test": "test"}, "dist": {"shasum": "408513bc14f58cd3aacc7098a6820b2ef25b3dd0", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-0.1.5.tgz", "integrity": "sha512-v+G9sqvXldof9hnzO1NUvVgnQShNKOOmU3M4nUv6QD058oZF6AfHUCz24zzAOFYtvunu+thbLCOXgspxARMZgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBy20WVoFLBeGEGZe0O+3N4tFK0GOA2kd95Fiam4uJagIgMWIjPn82YAth63P/tDw1jMmHYtiIBsQzLAt2sxdgO48="}]}}, "1.0.0": {"name": "wildcard", "version": "1.0.0", "devDependencies": {"tape": "^2"}, "directories": {"test": "test"}, "dist": {"shasum": "5d39a502e17c0664ef9b2f2a02cc8213b12aab13", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.0.0.tgz", "integrity": "sha512-ErioUxHw0Fs2TagejzKep3XyXMq9OQMv6QNDw+LUmwKjm6nHMBIYgHeW6KbAho8EQ+B6GcORevY1DJ+ykPyuFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBSbEHL+Ee832xlPqVnCGrgNwkItbOn3ksT0FT6JYM5zAiBMwdvHgs75NALvVm/vyc4Q0hcYkCWokfxMqCRqCCZttw=="}]}}, "1.1.0": {"name": "wildcard", "version": "1.1.0", "devDependencies": {"tape": "^3.0.0"}, "directories": {"test": "test"}, "dist": {"shasum": "05cbabca1949373ab9ec87434d8b7209145b68cd", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.1.0.tgz", "integrity": "sha512-KSSYoSDesQ9Bon5BmIGqluMbjJJa3QASEFFBF404XE1a1V6XM9PvZmhH0GGySAUv4unVmv872cKIQQen+hAs4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGsqaRqr5OPy76ca5OoIuwTGf2+P9V8qwI5UfZ8DVDihAiBvKAIpsgfhOqU99JzszuqgQPKO80jqA3vl76tIkf6+Pw=="}]}}, "1.1.1": {"name": "wildcard", "version": "1.1.1", "devDependencies": {"tape": "^3.0.0"}, "directories": {"test": "test"}, "dist": {"shasum": "692bffc4d2cff00035c6b662c0f0baa6e72f6112", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.1.1.tgz", "integrity": "sha512-pAQGSkSDDwJ6eM34DB/qJPVCxOpjbc/txr1y4MNvs7Gmg4anlq6EHklmT0g2mIsbDLMltJYrilo3MNOby3LC8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyV4Nu9GSe9wdtUlSI8Y+SrE7UuLLXyasLOpOT4zrY6wIgO2NeBFs57lC31DVAwNR2sogTsMr824ap2tEJps7ZMgo="}]}}, "1.1.2": {"name": "wildcard", "version": "1.1.2", "devDependencies": {"tape": "^3.0.0"}, "directories": {"test": "test"}, "dist": {"shasum": "a7020453084d8cd2efe70ba9d3696263de1710a5", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-1.1.2.tgz", "integrity": "sha512-DXukZJxpHA8LuotRwL0pP1+rS6CS7FF2qStDDE1C7DDg2rLud2PXRMuEDYIPhgEezwnlHNL4c+N6MfMTjCGTng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAghKML7PO1qL+TFHRMlqHfeZzSSETKy9iA9AMolQ3kAIgDkEdwA+pkYXbjb20ykrbdh8tJ/vNActQ7de4u6bLMZ0="}]}}, "2.0.0": {"name": "wildcard", "version": "2.0.0", "devDependencies": {"tape": "^4.6.3"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-JcKqAHLPxcdb9KM49dufGXn2x3ssnfjbcaQdLlfZsL9rH9wgDQjUtDxbo8NE0F6SFvydeu1VhZe7hZuHsB2/pw==", "shasum": "a77d20e5200c6faaac979e4b3aadc7b3dd7f8fec", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.0.tgz", "fileCount": 13, "unpackedSize": 21670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEJzuCRA9TVsSAnZWagAAbksP/1GZq5W2mVHhvBY8p/ex\nFOHkBvE2OfQDGVc7qF9Haer6OGlyx2QzFhEE/OGLcV5ytVFQGRdiqysEAHTE\nz55YnIzUT/gA0ACKyWv5HqRBIFrPJJ5hJ235JEomEmenh95fuUP8SoR5mIFO\nW3o5kCWuy2WsCL7WwubewTsXbwoQwVcz1UDIgd5ZWtdQPtphOuOjk4rClIDg\nOB+9f3VJplPWvHZ/ArDiv0x8hrozORsyZmZPCFWGkNPsV+JWxfL9bg0Sr1eS\nUJ8mHXdc9r/xjjwBZfki+2Ul9byF/Osv6QrwO5PLK+ZteiOxNuo+NPihXTjz\nPMGkmqp6g3G17bYi3zBi1Po4FX++Vohu5s5331BePP/pyM0nH5teLNbrvJvw\nxQAz/dg86PX8V8yv2Kkn10hYjr8hF+P4GAJaT22pxgHgsTRjlb0cGjaBbWR1\nBI6y5YFj0egyeS6EAUb5x9SFn77+sMKmmAVZAufUhjsvJFcu0/+gTsdeUmTS\nuLDLpHNQOhVdHphEAEBRXq2A97/H3a1f1KpWQfZhbeSNZ1tqVy+33mXrD1YX\nF+dt27zh+JKfEcNlrmsxNT+bHIQrXcUlkSqVYLnVXhRaXZfj0WhIrQAXpHiW\nqJfdC7uYVLsw+NhT9Rlj8qdmfaB9kXnlJK4yZaEHIKgTAdHn4Zz9d2s1LE3w\nVVqG\r\n=ivsv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnYJsVMgoC6/UH/Wy0bwVjPyiUDnI6jxREk7sIu/yStAIhAJdm+NJLiWYm4Yj+qaXWSVX4SLj8HcRWpLihFUkUubI2"}]}}, "2.0.1": {"name": "wildcard", "version": "2.0.1", "devDependencies": {"embellish-readme": "^1.7.2", "tape": "^5.6.3"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==", "shasum": "5ab10d02487198954836b6349f74fff961e10f67", "tarball": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz", "fileCount": 13, "unpackedSize": 13509, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICpBn65bzlTrKv8xChWv7NqkZ4bQvUW8stHb7aCHymBVAiALgfk7HU/OQZ3JBdbWAWaB1YdbFKLGsExX38ryXcqkgQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRouUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq20g//anxHsuXsGWfK7JHs6I3xw6h0aq03ztTljyMb828Am0DwaleI\r\nGD/lXEnIuTOpLR23Jfzs4ZZtYbDeUvAkxSQPisY3Z5f5jteczThvhqQMvWiH\r\nT+nLifkr/Edr0fPuytwU/VsI8kx/Pnf5Py42nZQKKBSSTRYGuEq0DUe2wBs5\r\n4LD8mEEFWA0t7TA0sUWHXooSoIolN8j2HbKrIg643rmiuUkh4LpEQnLE4UdZ\r\nxoksKo9yER7beyvRVzK19NEIVqT1oMp3pLyO8yy25vBHPZbfl8EaMaM+fxxL\r\nFMa6fhAwwKRJYxOSk6RI3977bgUlRXKOsenrwSkYpTBAJzgYcCaeRoFDyzuE\r\n1xIeSuBpldxh1o7SIQdlTXq02Dfrv53aDqnF2C2h8FTdo7MHDi538P+g+Ey5\r\nZkQXlwyH59Gr28ywabECzEx9Hkcj8BVz3fT8MyJt2KqC1VXj79Bp3OqGHNYg\r\n0yEzCmQxba7EuBOYakxWYl72f1kxeQqqz5xPx09Xtem+8YcVQg1CT0Kwm0hz\r\nz7sELBqVrz6q8qw1nBmohMPIePlVvxeGG1G2E9l0UrM/Gt3ythYOK+s8lY5H\r\nCfl89aZoSMvhYU5SLvPhCAJNkRQuO5bMnl6c9yqhMmTteFmtoj7Wp4YfHmt9\r\nOLmmB1/51eUOVM8ttXHeTe/6PLCgKQopsyk=\r\n=Qogm\r\n-----END PGP SIGNATURE-----\r\n"}}}, "modified": "2023-04-24T14:00:52.088Z"}