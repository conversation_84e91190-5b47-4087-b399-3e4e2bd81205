{"_id": "p-limit", "_rev": "33-7b2315ed8bb6c34a5294d952423b5b11", "name": "p-limit", "dist-tags": {"latest": "6.2.0"}, "versions": {"1.0.0": {"name": "p-limit", "version": "1.0.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "1b6d069a70cbb89c54c172765680eff803a1e0ec", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-1.0.0.tgz", "integrity": "sha512-J/cZiiZctdtx+MKlMP53tzYPWtP2JeaAB3keN+1auJNYq6lnOwvpFDMK7JMGdUw+deQFPPAdGUACuBNd0EwnmQ==", "signatures": [{"sig": "MEUCIBQd0DoBEJGx1+JQ+wcmmROa5lAAcQUFGhWkUVc+nb8JAiEAhGSKWvnUBNn1/elD1dKt7kjm2Kc/KTfEUvr3WQaW0jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "1b6d069a70cbb89c54c172765680eff803a1e0ec", "engines": {"node": ">=4"}, "gitHead": "b364100fada683f5d43640fec2fd48b6ab4978dd", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "4.6.0", "devDependencies": {"xo": "*", "ava": "*", "delay": "^1.3.1", "in-range": "^1.0.0", "time-span": "^1.0.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit-1.0.0.tgz_1477036781404_0.05086738429963589", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "p-limit", "version": "1.1.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "b07ff2d9a5d88bec806035895a2bab66a27988bc", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-1.1.0.tgz", "integrity": "sha512-sFSFmsGcVho1dNzsPGyiL1xs4KxZlM2QlznVxCDg0loLefThSsVkZPyBZEehQSci0nLwkgPZziJYpMGa59Vzqw==", "signatures": [{"sig": "MEUCIHYBAjyIyAk2rCg/Y19Dj5zW9GCRUtFA9gx8VkXl9lv4AiEA4XF8zG+MxZudf+GizE9v4Ve8KcnxqdQ7DVEY1AwJzfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "b07ff2d9a5d88bec806035895a2bab66a27988bc", "engines": {"node": ">=4"}, "gitHead": "276b0aeef73ac9fe0b80622d8261a2bbe4f9f74c", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "4.6.2", "devDependencies": {"xo": "*", "ava": "*", "delay": "^1.3.1", "in-range": "^1.0.0", "time-span": "^1.0.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit-1.1.0.tgz_1479711587647_0.4504568234551698", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.0": {"name": "p-limit", "version": "1.2.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "0e92b6bedcb59f022c13d0f1949dc82d15909f1c", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-1.2.0.tgz", "integrity": "sha512-Y/OtIaXtUPr4/YpMv1pCL5L5ed0rumAaAeBSj12F+bSlMdys7i8oQF/GUJmfpTS/QoaRrS/k6pma29haJpsMng==", "signatures": [{"sig": "MEQCIE6sxsCtPpmsNsn/vdDzBUFpsNRy9JGVWYGnLLaI3DiOAiBze4lrTowCDwNE2WbGmpPPKfaHMpDIIbsG8/uLpKofIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "4b3ecd3e9b43de4ae687505bc946b41d496065f1", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"p-try": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "delay": "^2.0.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit-1.2.0.tgz_1515025456369_0.8515273337252438", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "p-limit", "version": "1.3.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@1.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "b86bd5f0c25690911c7590fcbfc2010d54b3ccb8", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-1.3.0.tgz", "fileCount": 4, "integrity": "sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q==", "signatures": [{"sig": "MEUCIDA02fEVyn8rp+sk3hFaFCnc2zTFuk2dW1WkCGXS2MyhAiEAnBnmjsA3oICDiLaMQeH/IdhT/RS5Nd2XSZxoE+HqBIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGAVjCRA9TVsSAnZWagAA8tgP/1ybyg51ZIGk4PPoB+Ku\nJom0mpGjv3UTxa6C6wUeakkszs9k2vCGCueUvOnNdVg7675D7MdUmRKE7P1+\nCrnJ0YeVkdfajWlWkEA9+nNMEONBDWvOtPYpXa/DYFV19XVxVTdmN/VSTIeQ\nJ+SEMV0R9s4LvLz4o4ua0HgdRVyDTrX3rf4HDJfWkHINF7/Vggr72XuKoB3/\nyciRX51Wn5fWEkMpbmXAq0K5QrI+nKWRZ7S1HMsW22AEaoUHnjUNfwn6sQah\nPLNpp00mi4QDQXoT9RGC51D3acVs4sUS9VJUiRtt4DvaVv6eJF4z3R2iZDlI\nhKQI2cItT8AS7Wf3gTHmeZKsAZ9WOjxBF+N6lVB1BBNiWHHqKbpiG8Hd6j01\nK6cOeN1l3dZw9an/tsHvB6DqZ09s3MSzZc48fRhYiJD11Doj1wwIGUAaY1ht\nNjodGrQGZD16aQXMSQxwC3VSoX+8PHKv6A+OHWEEBKHLuB+OTJ7L71Hp4ner\npfnZOxMiGtg1nNVd2nOdMaf7MAoRQsklUb0Zy1qvb6E/wcO/VuTU9nU4WZXw\nnV1FYtLowrp7ThA7zOWdCPgKPsSTfy+L6kfrMcseMdE8WO9FtdgjhSVWuxNo\n0YV9PwTxK5axwNrcJS/YFMLsUk7knkf7Y7xv0K4SMyXXFZr+ZouGnny8Vzj/\njd1T\r\n=Msum\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "cf076d73844ebbfda8ae4e184fc436396998ecb2", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"p-try": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "delay": "^2.0.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_1.3.0_1528300898695_0.9429035390514915", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "p-limit", "version": "2.0.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "e624ed54ee8c460a778b3c9f3670496ff8a57aec", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-fl5s52lI5ahKCernzzIyAP0QAZbGIovtVHGwpcu1Jr/EpzLVDI2myISHwGqK7m8uQFugVWSrbxH7XnhGtvEc+A==", "signatures": [{"sig": "MEUCIBuF6Kr7Gk/5BphCaLlY1+nIwYsnBpDXZFCGJZqS2zCfAiEAplXvamkudxjhDgF4jA8trvi++T0FPTYhWNd34D0ID+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIql1CRA9TVsSAnZWagAA/bgP/38r6Dcrqym8D77K6u36\n8t3MsTI7AqnSfz0srYU6Q/Kwi6c4lc1hQOZ+FSI6rRdtAYPMCn5zelbFBrWh\nhRu4vJnApoYr9KLjICl6hLhnKEN/QD0K3FxbEQGrvklQngMOSyWzNbdnGVhx\nhgDZB11LJBuqpF+RrO+JeHCJrDskgqkNn2463szABwvTGNYGNYkqxmSR3jCC\nqtfyx3ulq+PDqhuXkZ4PmrlNYB2bdWyFntdHo72LkaXfw1CvwlNHO/HQjUi5\nUM060kiBuuFWtkbVakhO5QA344n8iXn0VbMrqYRfnqkRoxdezb1evtwHit7p\nw/CYylDEVpKRQb9U9gEljfXtElE9LameJUbunCTWSBkBpiuUPXcz1l/gLix9\n5fpA6mouJOQEUszxE97vACaQM7TO90d2UEwmA9ol/+wodQHKHEah6lyvLYx2\ng+fu77+fyTFkKFGu4Zz2nOrZJHOuCIRPMfcc3ijiEnGgehQ5bN81rSOY5AIA\nUFmc8rFa1J9S+Xcj75GyeRJ+1DOnf7no1mrY8vJ3UFBuq543mhlIiZ6cYsVd\ncc//iuSK1vnDkaiAQSsVKTz4k6RlGzjHA69Ath7mT+FxuljuJc253QmKCPBA\nu60jjbaijuIyhPhY2a0fNtLPwyNscDid1fGbvADOSH4aC2Giz3UHLPego8t4\nT/Iw\r\n=KPle\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js"], "engines": {"node": ">=6"}, "gitHead": "0eef70fffc6503a84d8a768b6efe42e227169dc6", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "delay": "^3.0.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_2.0.0_1528998260071_0.8291032578012119", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "p-limit", "version": "2.1.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "1d5a0d20fb12707c758a655f6bbc4386b5930d68", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-2.1.0.tgz", "fileCount": 4, "integrity": "sha512-NhURkNcrVB+8hNfLuysU8enY5xn2KXphsHBaC2YmRNTZRc7RWusw6apSpdEj3jo4CMb6W9nrF6tTnsJsJeyu6g==", "signatures": [{"sig": "MEYCIQC9OTxx7pn+LVL7Ujnl5zezMB1aB533ANyt099uGAoldgIhAJwLhkUL4EKx8LQ2JHdVo6FV8x6Zd0Xby6myYNaK0Ce/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIjuyCRA9TVsSAnZWagAA9KMP/R+lPLjx3+1/0HBOwvic\nj8F3y4kedwqOfpPoPYSmMYV1WCOe/TI/g9Q3+WGscEIrZvMohwy/E5PexakR\nYaPWkSoXGQ9ikVxHBSOtL88YssvjbttS3ZukgdUyx9andMLsBwXrzDlXix5O\n5VG5i0BzU2L+nkc2+IvNfBkUxLY8+OKJdlbgA6HXWUej+yY2+oYFGXAhE4Dt\nTnMZC4otdpefUynsoyd5n8xFVjaw/cgdrABFgFsLGOPnhP42+EkcHA1oH31U\n7tpJgKQi6AQ8bojlV/sZmMrBZ4bh3EKnw1LkgLK5gx/kd5Wd03YwfmLtPF0L\n4L6nqleRM2aY3gc7Bc1kcLaA4vqnhSh30s41F4SVZgPU/mMvNELI20iHRZw/\nCrv9XB8jWVEx9WDTrKhyiiOPX93NyG7KqxgSo1QQp/ASrMHmtAcwKjG3o0O7\nobAlO3CwXNMe1aF9q5LqIZVyWaQaGGlT7LMkvIQbg/xbTUcQYu25hsCF6vSZ\nj0M6YtepH4fmhbZXGLaqQzTfAQB6B12NUNWK9Z3W4gK5nSFh7Ysh1wcfrZUx\nTxKq6a/RWfxeKbBzb2fMUkeXorwwASzmmTtXGzNANq08GZXNFzz1ozclBI/b\nPv5FuzHpeiy7M0TrRLRii0nYQhxD3t+RnN4LlPfPjt697Ak1GdYOQFx5scLX\nYDwh\r\n=QeA7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "8517860c047c5fc485d92879bfc01952e1417d19", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "delay": "^4.1.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_2.1.0_1545747377882_0.09658731145738386", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "p-limit", "version": "2.2.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@2.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "417c9941e6027a9abcba5092dd2904e255b5fbc2", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-2.2.0.tgz", "fileCount": 5, "integrity": "sha512-pZbTJpoUsCzV48Mc9Nh51VbwO0X9cuPFE8gYwx9BTCt9SF8/b7Zljd2fVgOxhIF/HDTKgpVzs+GPhyKfjLLFRQ==", "signatures": [{"sig": "MEUCIE/U4Lp5kcxhntX+wTuEMDYXgx73DH6MRFOZEDniysX1AiEAhRZExciI153JTpHv6f4vVoIPoZS2DSw1uviQVpPngts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceteqCRA9TVsSAnZWagAA/FIP/iuBAUVmHWwLVpBIBoqF\nIdjwVBtPFMeaK0HmyqXpiFybcoKTDdvBelQVadnvZPXGSoyvOTvCl/ajb389\n69Rzqf/QTQ/Jf4B1j2fG6/VLANlpUIjhUplczpSE5YzMGWQYdeEm00Nh2Hv6\nQiLPTvP8E525mXRszDQOl22jVx9mFfhnIhz35RjAq9p7XdFcHv65BJTgB6e4\nHQI6x9fEilPxRHtHEqzrw10omW03k08UFet5faSsnlj5gRyI6tAwtrUWVBzS\ndJKwRKq2IjlVJJNe+BV/iTuS5HII6MBhC0VmP4Xfa1I5OLFc7qGXYWALikmu\nz9OdGh4Fh7UkEd+jJBFFcS2vXNeQ8RoWuSaBjXMDWEWe808iaqY6Z4+SA54i\n8bmPD2vqjvqx2CQhCz3QN/OuUWN1j7nJdkSKktI2Zz5aA4iLT/r0U2urxM7F\nG1FbhNo3MvEHxuvQ3xSPeiKLVtmRAbmS7uxLaOWzTKsa3AVz5qgt872egxlA\nTaqpNnkDvi/swk4dl4/8ZfPzVG9W5i+jnmv2FWFxTf5LU/RtDsIJ4G7u383t\ndIaViZ9U8XlTvpnJOYDhzH3PtrmBhd8euDbeUs29SSVgSX7SPRPrWyzduZb6\nplIzjCHgbokM34y2MZ9iSNqwJy/p2zlwWFNn/4kCwephyfkFlz/6on2tAvns\nojg6\r\n=jlw5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "0320e8b5e9b9ff66db20f98aa4cf235146768bcd", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "delay": "^4.1.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "tsd-check": "^0.3.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_2.2.0_1551554473384_0.12635434710499815", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "p-limit", "version": "2.2.1", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@2.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "aa07a788cc3151c939b5131f63570f0dd2009537", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-2.2.1.tgz", "fileCount": 5, "integrity": "sha512-85Tk+90UCVWvbDavCLKPOLC9vvY8OwEX/RtKF+/1OADJMVlFfEHOiMTPVyxg7mk/dKa+ipdHm0OUkTvCpMTuwg==", "signatures": [{"sig": "MEQCIH4WPD74xSbQfTQBA4/LGOYWALy3amnLd3wUy7rFSvVdAiA+gh2ZgRVqw+Aa250erbvx9RmRN/dqIKc79xgAGChzXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWZ5kCRA9TVsSAnZWagAANJkP/R+DHfpqOGtMlgnN1n1N\noxY2KhgWvaUzZW4SnkTFqGE/pQBj+5azCtTfApoV39jKVthAAkBtsQsfJSww\nn8Zlc9iMmPPnIFw4pACZImecOcDoNCXneako74IMyfsNC/sVrkeLPkGOcL67\nTHI/6TkCii/59WW5a+B8CN9pWjE2iS5KgJQECQGK4X/mAryLsim0zIL6XDEi\nXBLdMZ31kFmQwRYRVCuzhQU3Te0d7AnUNoTZJex4jN6addDyZ9DJq4tdE81A\nfOg61RM6b8LR96oS+Q/thkB8pvRFDAuRxx/7aDLn6n1qbuoIzrhwIasZCriM\ndz99uc//Qkyg4l3A38fj1y0nWVF/d31HZfbQ1KYllYgx5JBWm0kaNDcV8OeN\njaVnsWaWoaK3QdXTqnIpfhZQUUxLBxcwAzoaErpKK7GNEiFjIRwUHyPwxvCX\n/DyyjhlgrJYMu+Mr2bEtajMeQo8P5PNLTGfC36lqps2+Om6xN051t0LdsRNL\nSQLrbUO4EOqsLgDLvJ26bbTFMd5mrIlULRHV7ptuScOMg1UAGEYXdZ9tBdXj\nM3Lqvu2gX7kOC/Ux1IEFnqzNRUB4Lno6T1V+qdFO/+Vs9GXR2NUp+8U0KcmP\nGlV3rvXenL+J+OUcijd7WIdHgfoT0sBhjBNzBbpspBJBKer/pUXxgVEGfcZS\nNM20\r\n=gCXI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "056c0f3674041b90ecfb69c808ec1b910eb1e993", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "delay": "^4.1.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "tsd-check": "^0.3.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_2.2.1_1566154339491_0.7648657346726373", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "p-limit", "version": "2.2.2", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@2.2.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "61279b67721f5287aa1c13a9a7fbbc48c9291b1e", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-2.2.2.tgz", "fileCount": 5, "integrity": "sha512-WGR+xHecKTr7EbUEhyLSh5Dube9JtdiG78ufaeLxTgpudf/20KqyMioIUZJAezlTIi6evxuoUs9YXc11cU+yzQ==", "signatures": [{"sig": "MEQCIEzJ5l5+c+mTrm/qzfMXAXMhKwiG1FCt3ZS/DMULL+tjAiBwxjTz05KTxXN4ReDzS7inSjaXueTIaWZxHi9wnPPqXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDOgYCRA9TVsSAnZWagAAa04P+wdFMSlg3nGNObuyNS5E\nWYej8W2JeK2JI5bMLQnRWQC+emvMnRo5wGGbTQnBwaLx2nc7trM7v0e5kWnz\nWGtXh52tOHj7z1DhyawY4GOohG9qdISCosLGy4U/4T9Px58drBco3PgJ+laU\nx8C773sbxPGdaupf7faKwjK+AVa7FKp3ZEl/byl99OiB+R1E4NbGx5lLCp2i\nzeVFwpqKS73SdOkPvrvQ86Vc0om0it37R0DLX2yRkHlp9emOfluNEvtjvvr3\neKiab59mVOjtPi1AgdfFHoKT2IkXLwUiALmOM0yQSuzT52fIl1BnwaOzkfu9\nMilJI5mK7avb4lpo9DOyMF19WH319anK1+9PBHIGL+7z31fROIik21bDW/nJ\nr/Sh3A9UDN5At+jAAgJgr4SaBu4nEnAAaclNvhQhwRNhQCeioSDSnbfWM77r\n0ZUgsEtGf7RKHReSdHrsY8BCnUx+lQOuDtnhFqkdHDO6/W6Kl/YQ2JXuUfxO\ntvzfuIKyuG5xZRF3x/H2my4Yae8u1ux5W/X6TLzfC1i1IU9IMZsFfcB39CLi\nBqhLeOJyVIJkf9aQmVm1iEbZxO4XdGwLr7n5TaQYIPWFcjQgUodfA5Lwmhva\n/z17joRZTAroGDYilxKMGal5sK4SWkhooWD+yqmgRosUE3Cdu7C0PX6NQqTT\nyNyU\r\n=fyOG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6d7a2253a33248572aa87a56bc4af9e3fe247232", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "delay": "^4.1.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "tsd-check": "^0.3.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_2.2.2_1577904151691_0.7571350708733962", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "p-limit", "version": "2.3.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@2.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "3dd33c647a214fdfffd835933eb086da0dc21db1", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "fileCount": 5, "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "signatures": [{"sig": "MEUCIHlt9hYbHxGW7zloRi9Qq0+GXUbc70zfkBA1JDaXzzryAiEA7VoBzXgYGN4f5jYZPmlarzfWIuqRoF0Kn9YzC4aGzNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeifv9CRA9TVsSAnZWagAAC1oQAJgu/ArafOjG7TW97Uxj\n3Y5d+GdiC6Fr40sWDExogt4uS350422jaisNcfU0W0tlucaPqY1OabpASQbe\ntUOGGJWynBIGSgZuLwQslIsXEzcEBxiFj6hC7CtmWAbeFJVuCLAeBIuMXbLS\nBQSzx3z2dJCF/n5ttBJw5CyRSejMKdu/52XgHZzi3Nfdd+IxQMIL9b4Pit2Y\nNljkobCSaSKSKXZE1hd466v5bpifLNjoxQGiag4mFPOPFOvX3uqhvBPyQDLn\nyNCC70Kc4UHGVFA7gnBHgX4YcMyj2HG4FyrrRGtg3OepeOYnIoymKBjHpSzJ\nmLCjN4LO/zaSJC624//GfMzganoYehGNVbz5H080iRa4lashXgEQexQBjdt4\n5Ef0H4FbKbKVyXCBt5gPkdHwhGIVb1iaXysghXQFCkSkCWtjhreVh0VhQ/+D\nOSnoMxeJDLKN5YMpa7GkvNOdxOlkNAKX4ZSA509Bwkx4Y8fKewRjLRNwjsGm\n6vXNVay6B48uhCJvupE0SCXr1+Xe/7aFSrrem80daFyMKAbgGaZ2YBcVjIUJ\na5dfD2Q6bm5L4Zpm0r5qsDgAaqQ93kUZnDRYTe/thkJuJYPO7JiL+OknPk/e\nkgCFHZH/cqeK3YSP0eor+u3UyvhTqG/Jsp261YEtBe1go2gbW6zY/TQrzbrH\n3EKE\r\n=JEtU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "a11f02bc5c04490b7c3de2663d866c211fb915ea", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.2.1", "delay": "^4.1.0", "in-range": "^1.0.0", "time-span": "^2.0.0", "tsd-check": "^0.3.0", "random-int": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_2.3.0_1586101245000_0.8128440121358671", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "p-limit", "version": "3.0.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "8a9da09ee359017af6a3aa6b8ede13f5894224ec", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-2FnzNu8nBx8Se231yrvScYw34Is5J5MtvKOQt7Lii+DGpM89xnCT7kIH/HJwniNkQpjB7zy/O3LckEfMVqYvFg==", "signatures": [{"sig": "MEYCIQCOBxrtpkp6WZ8Ak7aScQWC3Vs8gM3FSl8ugyEg1YuerwIhAK9LzC9y8zvVchxiliuAibCWLtiodhb9LDXQxs9JKlxQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe24OfCRA9TVsSAnZWagAAubsP/RzOmDrcO/XuSpB15tU9\nJWUy5lp+22Maav96oXZQbgNEO3ZXUjAazRzq5cYp9onGT7samDQjxyoVrjM9\n7QvzyyiQKWXmCnXeFKEvm3wxtjZRE+MY3p3Iz/7c4WEYF1GZZrB6tRlSUnYO\n2LwElgrsJJ03ho3VyAfxnJb8rWsGb5OcDzGv5uGB8dxeOzJJyCQAEPSxM2m5\n9IjHcWxaVnDdh7DG3zbw1vtvBffsWEXyt1SJm9g/f47lb3MKTpHf6NWvAXCS\nn1564e1gOvLV+tAzVjRJpnR2EszA5NNPB6KI3KDQy6psHmhpQ3a/Rvr7Et4T\nfr2fsnWsJkYL/F//TWuKNFKv1v7cQf05lDigJeTyA2Uq6YC+y0mjp7bMjN69\niyDOm4c9DNVrgzXk8rJyCql+a3knJCVZYTHf0WIQ1Dj2F/06gFZA7ni31Bck\nIpm9se3DdwKFUuQOBpvm15Ehb8V6FPNC2CPOV2v6XOQWH3Hwj0UYlAfxottz\ne1xJN03X7P98jfcXOBC5nnIdLbNMYv1GjiCTaYr76m4Cp68STDCmC354/u7y\ndIZa3cveGT+VwgLKgmDHTyggWF8VTtAlpfA78sT+OpHqhddJpDTBPyC22x95\n7p8/menZ89vElR9rNd7Y8MtUC5CbII3BRle63bKWKdE2RQnAw6OijQmR3B/Q\nU2FL\r\n=eDds\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "0038c559658ad75445de4b3a8c6e0167129e813c", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.0", "ava": "^2.4.0", "tsd": "^0.11.0", "delay": "^4.1.0", "in-range": "^2.0.0", "time-span": "^4.0.0", "random-int": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_3.0.0_1591444383325_0.9378092234424682", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "p-limit", "version": "3.0.1", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@3.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "584784ac0722d1aed09f19f90ed2999af6ce2839", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-mw/p92EyOzl2MhauKodw54Rx5ZK4624rNfgNaBguFZkHzyUG9WsDzFF5/yQVEJinbJDdP4jEfMN+uBquiGnaLg==", "signatures": [{"sig": "MEUCIBRiO6LM1br1/5CQvIBJgSiyQ+eEzXbCiNrHX7TOiRxEAiEAj8UnHd0vA9bVUoprXSH3hPxkWiDEWUJxHrRqDrNQods=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe28KSCRA9TVsSAnZWagAAsQAP/ReF7FkaHlYVgMFJtGJ3\nHKPHyPOOHmXcAtVelZZR1xZKsyexxLsrd/LdrruNDbIGOxuWu9BSgp6IeMia\nO8baSdP4Zj5/EdGjjI11cqpir9MUdCe6kvHQTookjOTid7doGljBIqqRXqru\nbNk0LHWlCXdxn8tooPzJvygA3i09s/ZuJdj+jeQHnaEFJ8BpfIFnXy5zK4oe\nVUqkCplolh3rfvnOe0aYcA7+Ijj/+Mg9bXntIDSGF9vXgcKOJvO1lMQvXfEU\nU4DKxfZsLyoc3A1JlvNvVfiUhK9QSiDaqZyAbOk/pHLC1K5XGtxBNhER471K\n3/Pk3i7emjTMz2tYNvgNuamBGBMaqG3uTparE4OThHspxK6Tv+DqYC2MT9ol\n1eMSaI17FcKEf2LyplckdoF/ARVuVECtUppAUAZiq620eePThSXd2IKSf7ak\n3ZZrZQZxb0F+HprumBH+2g3aS03sBO92R0u67t3aJFcj1Q+wEc8RjAIJGXtt\n3aHVKllUKB+luEY6pjnhmnF/cQL/jMMRVsRMgMzOdhhSUMpLHXvd1GB0155G\npX92tsclP8o9L1PUu616L5g4fyzGriCKNww99lVAVww+xrtcBcf9Ic47Rfad\nMpMIMTVKh4uwFDnZPCNv9ptk+cw1zpYjG6iNUxCoLG2/60bBBm0IeaHqSe4i\nkvMt\r\n=2GUT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "824edb836eedc9929ea383457cd82d8367f7322e", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.0", "ava": "^2.4.0", "tsd": "^0.11.0", "delay": "^4.1.0", "in-range": "^2.0.0", "time-span": "^4.0.0", "random-int": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_3.0.1_1591460497710_0.05351684556450653", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "p-limit", "version": "3.0.2", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@3.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "1664e010af3cadc681baafd3e2a437be7b0fb5fe", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-iwqZSOoWIW+Ew4kAGUlN16J4M7OB3ysMLSZtnhmqx7njIHFPlxWBX8xo3lVTyFVq6mI/lL9qt2IsN1sHwaxJkg==", "signatures": [{"sig": "MEUCIQDKw5yLuToeYXRX8DNvK6IwHBXs4QBdwJklYv8NYfUVVwIgFE8u61f3+0pMfIQzejQnVCfcy9y1kCuE1mYF4nrtEQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCxKpCRA9TVsSAnZWagAAjbEP+QEORVpSBOWPFptPiqx6\nN1dTg0YF1S8oxnkT2t6GDs2lQOAnFkWAr8I8l/kiBRhNPfpW8GYp4fCGFv1i\nEzI02zwGdbmtfeP7FnSIPJOJC0P7AC5/b8VB3kpsM1dkD3PsCiHLdqkn3oB+\nRfBVv4ROBGHIHxwNblkbW8KEl1xSPsH7v3MoBY4xBJtQi9ZGAfB+DApwd75t\nl2Ix5RkmbV7DtRtZFdWCjpQcroUCKJHO6t19Y974tElC5J2g9DBJLyakjLhC\nUE6UOxvaO4TUZnZhqxNi0xyIkgTEs+f2sDP/cj9cr5l+E9VcOTjIpDxDPDJ2\n7hpdnm0+CTkghhdJCSN+sdHs6IYYlx+cu3XjPQcMhJia6TqNteyyoAQ8F3LJ\nA20jxz8osl1lI9gbS3izkr9GXSpfR5CZObY4HQmcqhA9pfwyHcx3UuGEJ471\nCyHEkhDdGKLBo090hQ3nrn0QSPzR8ToGAZ9SZ7Gv+Dkfwha37WublPQv8XnN\nf0qWveTJvAO9ko9ZUcXrzSUADVUS698/QIesPJGbvC1toxJwYd9Hn9ADMU1Z\nLRxVTvsGSy04aNFCdu/DHVaa4/xaE4tbTnu+4KJoUTBgRuU/mnE5+MYOOSmC\ngWULrpu7v41gl3UwfvxVQKEIM5MPXQnHIcnQVpLu1yFk6g9Zi5fPREkG7XW9\nCPsK\r\n=FDwT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "b702cc1aa737ae834b4deaf87a537812d8d5418f", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"p-try": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.26.0", "ava": "^2.4.0", "tsd": "^0.11.0", "delay": "^4.1.0", "in-range": "^2.0.0", "time-span": "^4.0.0", "random-int": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_3.0.2_1594561193541_0.8922591638953525", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "p-limit", "version": "3.1.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "e1daccbe78d0d1388ca18c64fea38e3e57e3706b", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "signatures": [{"sig": "MEYCIQDj6f0gOPmuqZmrLdYlgq8Ycqg2UPnJ9WWwPctwl2dgrAIhAOB6ppqRCB108nbygLAHh+EKytFKunCMNH1lAkqx3UOq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvgrtCRA9TVsSAnZWagAA91cP/Rty0kzeGLDEPw06wRNG\nXfLT31fYxIXpYJn3x0DDEv1yEw1d1Ac49yHG0nhwov4Ik3BccgIcBeuIWrWZ\nY4rMMicnGw3oQFErGuW/VphsnzzCM2mhkSIjEm5rywsjM972sZyvSYxuUJTF\n4S41XzYgeHlkhWu5wlPtHAKeLJvLspnXtdG/jpOgp2KUBFV+BJi6U5FvS4ZT\ntKSysVlRZSUyweOkJhXd2oBbSsF3K7JuILou7DqsIzVJ87agpd1DMo76QrqM\nMoqTWyPJC9FI9H33Vh5uK/TYNUO9hgw1z/N2XqC9DXBugHwgmYMCB2RKvVHn\nga/WnWLV7jFtXkS+bsmnZJuSv2fTiiyQLqpZY4SGodyD/C6G3jegzknKah4z\nzJWch+O2HfPpveHj/UUdjrol0VO6ioC0JOMcpLiASQ4q6iMc0HKnP2tXoKpD\nzyAXw0Kyz+8/dOO7Y8B9Kd8a3coyd2Z3W6WHtuL0McELezRRz47AeIOxw9Yg\nsAgnQE0L0r2zPSn1qjK7zhKdIxa2fHw8+O8oraOmWc2Qx3bz10wWPyOqZCm6\nuhsUiuVr5VnZVVIUlAF6kr9ZoczYGigbKiCMNRewbo9qheTLYAtqtxn3JLw+\nVcuUxl3/CfVTHwth5Oo3c6fz7tYEipTHgzNHS364gByCRfXDH7LOiUsx4EGb\nQhvD\r\n=DH62\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "e316fac4e7aeede98beeb87e3a7de4ffc3d8eebf", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"yocto-queue": "^0.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "tsd": "^0.13.1", "delay": "^4.4.0", "in-range": "^2.0.0", "time-span": "^4.0.0", "random-int": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_3.1.0_1606290157244_0.5907879843343353", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "p-limit", "version": "4.0.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "914af6544ed32bfa54670b061cafcbd04984b644", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ==", "signatures": [{"sig": "MEQCIDHYVau8UQhMsGXoOYVCA8msrTXJl8/wbiR4sc3Zf2CxAiAacOynm/s5ks48Ld7X1tBolWngzKjgHewQxw97G9pvBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFbWfCRA9TVsSAnZWagAAXTIP/RzOhey8Qr8e1wFaJwy9\ncjHbik2Dz0j3QLXUMNUgltlJKH4ds3DaCAkM2S/SgsIKkrYe4hmYN7kxVb3R\nAovNeBMz9ITGPzomcUXFUVuThgRbTn4Dxzm4AS8AvFDWhG8zi6vaQpkZsRpO\nexK4u7GywFNd9Y3L91B9uSxBbDNhmos582+hFy776kBljdfqGFlwyIfG7p+o\nTWdCLOXvK8YX/Hf++ywsTjFN1/UrlJ5P5cYG33umcCpaic4J2hUdENavwe4B\nIMCY0rXGomytVT4TqPU2fNVqpaHVCXbMpPOJJ72x45fivPyRbkk1ZRL+UQsL\nk5XQsun2YxM//HYnjxJjhQAirDAQ7nFfzu/EKjCxfj12JkWHAbam4xSiJDJK\nMY9R6qILBqXhc9yFk3YTp1aZBl6CJwxGWCGVFzYkXKeb1eQCvLB6rOPw8+e8\napO+ZpGwqBQo5VRcflBCU0zOTIY6A1j7/VjZ9Tico3+e6m1Muc4HR1z/+myu\nWd6EqkyeowBKAQVZSupJlVn/M4ruOE9gWJla8yahZ3VyuhUNOUaD8xWWW9zZ\nA2gkq4duHckh+lFCZSpSfwjmid3nzTKHoUiRKBqGswESSv6RLs6luE59SVVT\nF+OijqZncTFN2sj34hGDMwvIWawH8r3NfQysHKmGz3nbA1elHbDBxTtQcXgt\nl1y5\r\n=VfSZ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "38a6773e552d24f4c9eb2d79d57b6cff017c587d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"yocto-queue": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "delay": "^5.0.0", "in-range": "^3.0.0", "time-span": "^5.0.0", "random-int": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_4.0.0_1628812703302_0.6491971469999389", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "p-limit", "version": "5.0.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "6946d5b7140b649b7a33a027d89b4c625b3a5985", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-5.0.0.tgz", "fileCount": 6, "integrity": "sha512-/Eaoq+QyLSiXQ4lyYV23f14mZRQcXnxfHrN0vCai+ak9G0pp9iEQukIIZq5NccEvwRB8PUnZT0KsOoDCINS1qQ==", "signatures": [{"sig": "MEYCIQDmMEQ8XPYVOTSYWs4660quHXLwHj2scm1eNOZOtJZdaAIhANq0xbhlPmLXaMRuxV1SPVwxHnKJDJsnP9ByZN/do8x2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7747}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f53bdb5f464ae112b2859e834fdebedc0745199b", "imports": {"#async_hooks": {"node": "async_hooks", "default": "./async-hooks-stub.js"}}, "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"yocto-queue": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0", "delay": "^6.0.0", "in-range": "^3.0.0", "time-span": "^5.1.0", "random-int": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_5.0.0_1698820264163_0.08717723629406482", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "p-limit", "version": "6.0.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "c1e3aae949b4c53a2936b2d7e38a40bb87720011", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-Dx+NzOuILWwjJE9OYtEKuQRy0i3c5QVAmDsVrvvRSgyNnPuB27D2DyEjl6QTNyeePaAHjaPk+ya0yA0Frld8RA==", "signatures": [{"sig": "MEUCIAeHawLo8x1AeKew1ixEkLv4JwwKa7pAmrn3yZDHLo3gAiEA3cB8Ntqk6z++O0sTSsp1E89ntgh3t+xGPLxZoOqVnIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7654}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "850768f5655dc46547802789e13af3cf35c3d7b0", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.2", "dependencies": {"yocto-queue": "^1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.58.0", "ava": "^6.1.3", "tsd": "^0.31.1", "delay": "^6.0.0", "in-range": "^3.0.0", "time-span": "^5.1.0", "random-int": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_6.0.0_1720094153626_0.8405826224508739", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "p-limit", "version": "6.1.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@6.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "d91f9364d3fdff89b0a45c70d04ad4e0df30a0e8", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-6.1.0.tgz", "fileCount": 5, "integrity": "sha512-H0jc0q1vOzlEk0TqAKXKZxdl7kX3OFUzCnNVUnq5Pc3DGo0kpeaMuPqxQn235HibwBEb0/pm9dgKTjXy66fBkg==", "signatures": [{"sig": "MEUCIQCoW8tbBJRJTan6bOEsfYe7wmTNn/zl4rBqrOkjus5t1AIgGEyAC4D7CKSLCH2lOdaq5sEPBIVMNla9Ts87rrqwA9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8230}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "98c8fef5562fabd900dfd9172013962889e13df6", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.2", "dependencies": {"yocto-queue": "^1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.58.0", "ava": "^6.1.3", "tsd": "^0.31.1", "delay": "^6.0.0", "in-range": "^3.0.0", "time-span": "^5.1.0", "random-int": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_6.1.0_1720440244518_0.8585718405141409", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "p-limit", "version": "6.2.0", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "p-limit@6.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/p-limit#readme", "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "dist": {"shasum": "c254d22ba6aeef441a3564c5e6c2f2da59268a0f", "tarball": "https://registry.npmjs.org/p-limit/-/p-limit-6.2.0.tgz", "fileCount": 5, "integrity": "sha512-kuUqqHNUqoIWp/c467RI4X6mmyuojY5jGutNU0wVTmEOOfcuwLqyMVoAi9MKi2Ak+5i9+nhmrK4ufZE8069kHA==", "signatures": [{"sig": "MEQCIChD0ByVg/3T5/wvgWX2TMFc3pj1opQbet4p+P8Ba4Q2AiAf66z8PLZGW/yXNt0fuwNAlQNUPGYwYX++jg4/wLim9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10346}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "7d14b3f04aa990f621a853e24e2a4b796baf1ec9", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "dependencies": {"yocto-queue": "^1.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.58.0", "ava": "^6.1.3", "tsd": "^0.31.1", "delay": "^6.0.0", "in-range": "^3.0.0", "time-span": "^5.1.0", "random-int": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/p-limit_6.2.0_1734623197970_0.6282986687976977", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2016-10-21T07:59:41.638Z", "modified": "2025-02-27T17:02:13.000Z", "1.0.0": "2016-10-21T07:59:41.638Z", "1.1.0": "2016-11-21T06:59:47.875Z", "1.2.0": "2018-01-04T00:24:17.334Z", "1.3.0": "2018-06-06T16:01:38.768Z", "2.0.0": "2018-06-14T17:44:20.143Z", "2.1.0": "2018-12-25T14:16:18.049Z", "2.2.0": "2019-03-02T19:21:13.574Z", "2.2.1": "2019-08-18T18:52:19.615Z", "2.2.2": "2020-01-01T18:42:31.824Z", "2.3.0": "2020-04-05T15:40:45.137Z", "3.0.0": "2020-06-06T11:53:03.481Z", "3.0.1": "2020-06-06T16:21:37.806Z", "3.0.2": "2020-07-12T13:39:53.636Z", "3.1.0": "2020-11-25T07:42:37.364Z", "4.0.0": "2021-08-12T23:58:23.464Z", "5.0.0": "2023-11-01T06:31:04.353Z", "6.0.0": "2024-07-04T11:55:53.803Z", "6.1.0": "2024-07-08T12:04:04.684Z", "6.2.0": "2024-12-19T15:46:38.158Z"}, "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/p-limit#readme", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "repository": {"url": "git+https://github.com/sindresorhus/p-limit.git", "type": "git"}, "description": "Run multiple promise-returning & async functions with limited concurrency", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# p-limit\n\n> Run multiple promise-returning & async functions with limited concurrency\n\n*Works in Node.js and browsers.*\n\n## Install\n\n```sh\nnpm install p-limit\n```\n\n## Usage\n\n```js\nimport pLimit from 'p-limit';\n\nconst limit = pLimit(1);\n\nconst input = [\n\tlimit(() => fetchSomething('foo')),\n\tlimit(() => fetchSomething('bar')),\n\tlimit(() => doSomething())\n];\n\n// Only one promise is run at once\nconst result = await Promise.all(input);\nconsole.log(result);\n```\n\n## API\n\n### pLimit(concurrency) <sup>default export</sup>\n\nReturns a `limit` function.\n\n#### concurrency\n\nType: `number`\\\nMinimum: `1`\n\nConcurrency limit.\n\n### limit(fn, ...args)\n\nReturns the promise returned by calling `fn(...args)`.\n\n#### fn\n\nType: `Function`\n\nPromise-returning/async function.\n\n#### args\n\nAny arguments to pass through to `fn`.\n\nSupport for passing arguments on to the `fn` is provided in order to be able to avoid creating unnecessary closures. You probably don't need this optimization unless you're pushing a *lot* of functions.\n\n### limit.activeCount\n\nThe number of promises that are currently running.\n\n### limit.pendingCount\n\nThe number of promises that are waiting to run (i.e. their internal `fn` was not called yet).\n\n### limit.clearQueue()\n\nDiscard pending promises that are waiting to run.\n\nThis might be useful if you want to teardown the queue at the end of your program's lifecycle or discard any function calls referencing an intermediary state of your app.\n\nNote: This does not cancel promises that are already running.\n\n### limit.concurrency\n\nGet or set the concurrency limit.\n\n### limitFunction(fn, options) <sup>named export</sup>\n\nReturns a function with limited concurrency.\n\nThe returned function manages its own concurrent executions, allowing you to call it multiple times without exceeding the specified concurrency limit.\n\nIdeal for scenarios where you need to control the number of simultaneous executions of a single function, rather than managing concurrency across multiple functions.\n\n```js\nimport {limitFunction} from 'p-limit';\n\nconst limitedFunction = limitFunction(async () => {\n\treturn doSomething();\n}, {concurrency: 1});\n\nconst input = Array.from({length: 10}, limitedFunction);\n\n// Only one promise is run at once.\nawait Promise.all(input);\n```\n\n#### fn\n\nType: `Function`\n\nPromise-returning/async function.\n\n#### options\n\nType: `object`\n\n#### concurrency\n\nType: `number`\\\nMinimum: `1`\n\nConcurrency limit.\n\n## FAQ\n\n### How is this different from the [`p-queue`](https://github.com/sindresorhus/p-queue) package?\n\nThis package is only about limiting the number of concurrent executions, while `p-queue` is a fully featured queue implementation with lots of different options, introspection, and ability to pause the queue.\n\n## Related\n\n- [p-throttle](https://github.com/sindresorhus/p-throttle) - Throttle promise-returning & async functions\n- [p-debounce](https://github.com/sindresorhus/p-debounce) - Debounce promise-returning & async functions\n- [p-all](https://github.com/sindresorhus/p-all) - Run promise-returning & async functions concurrently with optional limited concurrency\n- [More…](https://github.com/sindresorhus/promise-fun)\n", "readmeFilename": "readme.md", "users": {"kah": true, "yourtion": true, "knksmith57": true, "rocket0191": true, "flumpus-dev": true, "mcalavera81": true, "zhenguo.zhao": true, "karuppiah7890": true}}