{"name": "@webassemblyjs/wast-printer", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.0.0-y.5": {"name": "@webassemblyjs/wast-printer", "version": "1.0.0-y.5", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.0.0-y.5"}, "dist": {"shasum": "1fc15269d579755b2345967303eb7570fad66c3e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.0.0-y.5.tgz", "fileCount": 3, "integrity": "sha512-AX2aMT3Y7tob7nB5SyhEkPhh3fNS2LLvEvGx3ur7vcRdrij3EkBuHOhv3impJLWU8pXdGUU7QHAr4/z9GncUqA==", "signatures": [{"sig": "MEUCIDQUUNiABxK1TQtZTqvO6iuNtTVv5XHpJQfIbQV1ATW3AiEA2ZV1Y5aeq+qevqxXFUD8hTDZJXAI/YrftSJVKgB+VwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24679}}, "1.0.0-y.6": {"name": "@webassemblyjs/wast-printer", "version": "1.0.0-y.6", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.0.0-y.6"}, "dist": {"shasum": "1b58b20ba598ecd367257494323b3909b0c96ab7", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.0.0-y.6.tgz", "fileCount": 13, "integrity": "sha512-Nm+YTt4Hk4EQvmQm6e1hRHrMMHVEDfjZDF80M44XT1RP8gap/JiJv7+F7+mj/VNUCmDCjdXKh+yf/I02EGRl6Q==", "signatures": [{"sig": "MEYCIQC8bIWFpLc3SUAsJevX3socXfKPIveUjSK/8sgqkf3I5QIhALqu3J5SrJ6t2OXu9LfMc5tZx5CrXFlU/xUYMAzZBTcD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29525}}, "1.0.0-y.7": {"name": "@webassemblyjs/wast-printer", "version": "1.0.0-y.7", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.0.0-y.7"}, "dist": {"shasum": "61428fee2ecaa6e75f96bb01e60a25c7d14c42cd", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.0.0-y.7.tgz", "fileCount": 14, "integrity": "sha512-lipqdxlVRwHXSJOTW+TtpesTOEq4jKonoDK3WxXUjg6aQzOLf7CLz7jUWCwPqCoHV5YTG8ZJjzBRJjBh8R034A==", "signatures": [{"sig": "MEUCICRi2y50SMHQGVY8fdL/3veYaJzfiYLzzS5xofnFyWnKAiEA8o0n83LKIYMYytiHv8+he+QoZnSVkD9H4V1+dGZf+yY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29588}}, "1.0.0-y.8": {"name": "@webassemblyjs/wast-printer", "version": "1.0.0-y.8", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.0.0-y.8"}, "dist": {"shasum": "87397dc4d7692f58e8bc71b4ed54878937d20ee9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.0.0-y.8.tgz", "fileCount": 4, "integrity": "sha512-5zYMARtlVtDldCBPpTMLHd9lOb0Qhx7WczOVBTZgejLuxZAKTptEDbxx1sSHCkJCjvit0PBYBenuyf1Y3vc1Ow==", "signatures": [{"sig": "MEQCIGwA7J+BhZmzA9xchcOf3FU4TjN3G1FjsZWsqwpBk+0DAiAtqWlE/qgqRxxbzkM44bMZLOBoD+k4NC6eNazg+PMVCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24881}}, "1.0.0": {"name": "@webassemblyjs/wast-printer", "version": "1.0.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.0.0"}, "dist": {"shasum": "c6d27b88ec1569d59beb16853cc73b608ebe1131", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-NZvxn0AJwf5I4ndd6iV9ohLvcFPLy0Lfxllio4esNXQBfkmSfSdA46cXTLl8LsBPLInt0giX1av57n3SHCXXHQ==", "signatures": [{"sig": "MEUCIAXTHB+oscSpH4zyMNrWjWQcRbGLjKX9lVlMtA/f7QNFAiEAtnoL2o8j/OFWITWA7Mowka5DNcqdWgsbro7D26keFSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24873}}, "1.1.1": {"name": "@webassemblyjs/wast-printer", "version": "1.1.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.1"}, "dist": {"shasum": "ed43b83a1d00e1ac969c4f88b195f7f0d5b2c66b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.1.tgz", "fileCount": 4, "integrity": "sha512-ZPTpWkgDpTt7Qj3rnWBv31Q7e/ichIOzQQvP0EVPLV5UcmZT6HgbUSJV1XtWHLzF1JGrpcpb+nuBQGgRJc/3ow==", "signatures": [{"sig": "MEUCIHh/1CIHWcX+1BC2pwX3qLI5HgU6+Nb9OBdTEBaxQ3arAiEA7C1gf81vERk1b7utxoRqepWSsBmufwhUeZL/IzbHurs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24300}}, "1.1.2-y.0": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.0"}, "dist": {"shasum": "9212f81e9e8b25a7d35f51d97f43b64c1e7eb655", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.0.tgz", "fileCount": 4, "integrity": "sha512-2Ddclv+LEjCQIyZ5nsqu+g7R1E9jsuIF0uJoPRFLdYEgXaqwAv5N4IOJcknIQJwWrbDMOUZ1xwVP+0S42zYo1Q==", "signatures": [{"sig": "MEYCIQDQ4k4+lSq/3JtsmIYqb4mzlTOp4F/VDd40RV4L5rz/5QIhANNjZXHD/8agewO/zosg7+LDxJ9ez1ZW5zEQZGA5Ybtg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.1": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.1"}, "dist": {"shasum": "637836537a365a1b95250e783eed937f325d8377", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.1.tgz", "fileCount": 4, "integrity": "sha512-yiHJCZmCa72Gg+lmxIGbxWMsiOrFcknypfs164rei1ow8qmwcdhmgqlnG4dwK+HdyPnhUeFJWGKFDVJ0iP3NKQ==", "signatures": [{"sig": "MEYCIQDLro4WfP3JsXlb1dB/uw9Na51HIm8eULZYZW+qVhvMXAIhANuhuoEOz4qTKEYGcL6HyirNyN63HGwu/UbmeODEJb1a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.2": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.2", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.2"}, "dist": {"shasum": "42c8f99ca9e61f449d523b50a2eee35a8f6c40bf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.2.tgz", "fileCount": 4, "integrity": "sha512-+QH1u5FGhgLF7MS+tR0A5tInyHGJW1xZdoTpq5eV9lY3Adqvr1SPl0oTxlifxjBZRQzalsiUa1+vtFIpx5HVrA==", "signatures": [{"sig": "MEUCIG2B+Sy6GcWKaci4SklfkWIhzvpwC8HIbCsOP7aRUu49AiEA0nciBSrlcNFo5aHpXYr+opc3gUsoqvWiZ4wGMiJ4zzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.3": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.3", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.3"}, "dist": {"shasum": "0fb74f8a25ac50b64bad9c8e584759db4f4fae5a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.3.tgz", "fileCount": 4, "integrity": "sha512-QJ6HDoYlOTRJ2dv3cCIq/FPSodU3Jeo8ZnucxCU5EOSIee5uA8rAWd0CnWBLfFDWqHw55cIVzit7ih8i5c9a2g==", "signatures": [{"sig": "MEUCIH7OwCAKty0Fp32pOsGdmFlDkQWlwczpvMrLbCE8rKa7AiEA3lYv1+uJJaps4f4kpHizRDrsuNusAzggfve/on6uS2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.4": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.4", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.4"}, "dist": {"shasum": "878e202aba23d7a63c1b00ef432dc63192fd2367", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.4.tgz", "fileCount": 4, "integrity": "sha512-quMa/OLY8nIPcpMzprsXDFWjCCqWhkQIXXFQi3IpVDGxnSSCLAF4Z4jwoWJQnTBHV0oK/Uvbc1mKnfKtH0g/Pg==", "signatures": [{"sig": "MEUCIQCzsd53EZEJKm/8dwpUM1mK/9esn+ySQDOwkPKEUy0Q+wIgPknQRy4wwoFVBp73q7O/CuES2U3BRHhba40iTkQtP6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.5": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.5", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.5"}, "dist": {"shasum": "22ed4a8899f1315345ce22768c20b575f07ba7c1", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.5.tgz", "fileCount": 4, "integrity": "sha512-yP<PERSON>ey6GQZ1eZ0mLRVunNj9zYhi72IUKC9wov4JyqOwUX3ixC6IV5Q9bIzLgO+G0norca5SIzIKWHLPnHRSm7cg==", "signatures": [{"sig": "MEUCIQCqVUYJskKRHXh1UVkRHrhGPrbiXN40Jlc8/432sDXjRAIgIFtq4hvZ4b20jKRUNTaCb2nGai2z3Iuw61UQmYWEwA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.6": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.6", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.6"}, "dist": {"shasum": "5ee22d002854a7a6e55e9e6e36fc9224ec650fde", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.6.tgz", "fileCount": 4, "integrity": "sha512-xVkbypzjIh3+cDA6LyFnHDZHB1ukPNzNpEZQJuHEPmp99y9frSXg8I2a6Kr2ToX4Jy4nJsP2rJr5vQGQGvXu5Q==", "signatures": [{"sig": "MEUCIAd49NRw5GEjvOUX+cUHH5rAW4kKxeGrKS+mhNKv23IHAiEAvVe39sP59jXfuVG8DPuYdQXzNA+6ppKsqNpCj+2wWi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.7": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.7", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.7"}, "dist": {"shasum": "c6ca3338d82d62b6ce001c5b32bc87524823c7e7", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.7.tgz", "fileCount": 7, "integrity": "sha512-gQXrErkxE6As2Ihwz8vmSyOZWAefdlSW+RrSq2SQW6q/2vtO+S65me+0ZEIWj3+uASMFNbRxXe7Vff1lI9Gy/w==", "signatures": [{"sig": "MEUCIH+JZrTgWSDsSFYkwBwsU8rV4fDTBib4ZwCp0+X+TAMXAiEAqUi1dH3RhxnZw5EcDIESfsjCcBsq+WvbuS/7LFQj13E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28908}}, "1.1.2-y.8": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.8", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.8"}, "dist": {"shasum": "ecde284ee0ffc0f0d924ea0e808939c3a35c37ee", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.8.tgz", "fileCount": 4, "integrity": "sha512-yTXoen/7pWDEVTM2kLjxRp12M8oXGTxRqf7WHDbwdt845ulMkZn6aZlwA3MHylXVC0RfBxXiXsEO+7ys6G9WIA==", "signatures": [{"sig": "MEQCIGABh6fd4S1a6kwa4xgkRBIqW9xD0J5M7Zyt9M5S3YmaAiAKH16Qa3BMK8+DFqcJyTNb2Lv1Ad/nzRpYQlqrFOJxgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.9": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.9", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.9"}, "dist": {"shasum": "0adf059c482c7525f3e11879e18d172b59b306d2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.9.tgz", "fileCount": 4, "integrity": "sha512-pWCDJHfECaQsYi7lwFshLjiKGyNd7HjY/cR7XVDUjIukzxuxWuYC6jl29yfznP0ChyM4VBYkXtqpScuJwkbwEw==", "signatures": [{"sig": "MEQCIBleHQ3sYCkLnEbxWDy2AEsFQUcT2C7jCZ<PERSON>4cPSkB3AiBiby4jnVtKl0iYR/JWM01ybMKJztYCmP/a8G4hg5I88w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24308}}, "1.1.2-y.10": {"name": "@webassemblyjs/wast-printer", "version": "1.1.2-y.10", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.1.2-y.10"}, "dist": {"shasum": "6c7a41d40c5ecf8a13f963cf2144fc2f64fcaf0c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.1.2-y.10.tgz", "fileCount": 4, "integrity": "sha512-DsIk0yO2WDZ50dpnWzDohwlvYaQY/dhOEDan43fJcgEGILuNDzHKtawXJg59vg38I2tdbLSNSzidbViUlIsi7g==", "signatures": [{"sig": "MEUCIF7ubLpyRTLEk0bv4Hls9XBMmth2yE9olJ4R0DuM8x19AiEAo6jZPkLYwIXfU4tJgdwyC00W4S2VxEfyxeKFCKnpqLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24310}}, "1.2.0": {"name": "@webassemblyjs/wast-printer", "version": "1.2.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.2.0"}, "dist": {"shasum": "e48abcf41b32eb5cfbe44c72e800f31d44d604c5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.0.tgz", "fileCount": 4, "integrity": "sha512-pn5/oXhEsbydsviAkUO/Mbii8CjLK2ngZUAs47uAmhwPGrkC1ZM3/mB3ga8mo8SvvytmldRN7N+YExiu9tQQ8A==", "signatures": [{"sig": "MEUCIEaQ218uCRub0nqCyaWwVA03g+OUC2rNScwtC4CJc+vFAiEA9NgdrtK/RcQL6GFruhC8+WIw4jee9fSqtasHsOZ/FiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24300}}, "1.2.1": {"name": "@webassemblyjs/wast-printer", "version": "1.2.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.2.1"}, "dist": {"shasum": "b7b009369a2fc25da62cfb03fca4b0790db3773c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.1.tgz", "fileCount": 4, "integrity": "sha512-XHWtrhG/P6EsIIemLCC5BVBirgDcaCHJs3lVovtJY9pMksKqXySXHZghKzwXRQ+enIJWR/H5lTWjYHTyIly4mw==", "signatures": [{"sig": "MEUCIHMp4pmBPZ5GQ3JpyRp9+13/Dw9z2AVlxfMyITkhf1A6AiEAwIy9ka9dwec6j+jcBWEoGAHmDjbBvfsy6sv352AAcSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24300}}, "1.2.2": {"name": "@webassemblyjs/wast-printer", "version": "1.2.2", "dependencies": {"long": "^3.2.0", "@webassemblyjs/wast-parser": "1.2.2"}, "dist": {"shasum": "0db9c25719348a04e3b02c07867433e5ef3c71b2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.2.tgz", "fileCount": 4, "integrity": "sha512-8JNV8qa9P2Y8zsWUG/oB+j+QJaLpVLR0yiYwlVzoZZB1ZACiwHvySrYjHS3RDg0QWSXCAWzxIIBuSi0emgmb9Q==", "signatures": [{"sig": "MEYCIQCrtwnt7Ct3Y/EwfiOEk4Van9dJnNhEQDEJvL5e3DRXnQIhAJrdKW0fE4IRzWuZfU+tqRaVdxIOCupzUbmcsaXOgMAZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26900}}, "1.2.3": {"name": "@webassemblyjs/wast-printer", "version": "1.2.3", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.2.3", "@webassemblyjs/wast-parser": "1.2.3"}, "dist": {"shasum": "301c4d8ad339cd1a69dada5967a4a728cf78cab6", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.3.tgz", "fileCount": 4, "integrity": "sha512-jKKMHBdNxwxlXPg/1zlG4HmmRq514L2La3fWGgPQ+4Y9lKpc4hPwFvRg9fhl1vm0aRNZPg0xo1GTn0romoJv3A==", "signatures": [{"sig": "MEYCIQDVUDG87FxprK6v42DQb5bOlQrcGWXhIT2uqSWJfgl0qwIhALP42voMi9o2DrO+fzw/JIfU2SAYdSSSysPhtFZbQ1Lz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27673}}, "1.2.4": {"name": "@webassemblyjs/wast-printer", "version": "1.2.4", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.2.4", "@webassemblyjs/wast-parser": "1.2.4"}, "dist": {"shasum": "f8c74a72b7a1b7bc057a147db7806482bec5e845", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.4.tgz", "fileCount": 4, "integrity": "sha512-+aOJFwVRm5PTtIV90xXgD97SiqAJ/a2iOvHBzA9Ak3CqyXjsb6ovOkesvTl7Y0IQaXPaSXgs037wqCli/ErnCg==", "signatures": [{"sig": "MEYCIQDylRNaM0iH0DDCJYFLj8a2mBCKnRUwMc7XYwDOW9AjaQIhAKMspdp/DZUlBESxh54mLmqPxNBkM8qCsxUYzxNkGih5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30539}}, "1.2.5": {"name": "@webassemblyjs/wast-printer", "version": "1.2.5", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.2.5", "@webassemblyjs/wast-parser": "1.2.5"}, "dist": {"shasum": "c9524aad0e9bcb06917d9cf24620d6faed5e7fcf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.5.tgz", "fileCount": 4, "integrity": "sha512-n7HqjhX9kjtDnuKQTC2+58Wp9O4o5bHjvsZSnXlc4yqtdhvyIGF9rGVgwQ51+LjrZSDFVIENTSAASAA1h9EkkQ==", "signatures": [{"sig": "MEUCIQCb29rKT+U5jZYVUanWy0ibppRXRKDYR2VV1FAzKwtP1QIgefXGe2EinTalu32S9GBPXObcX8WNHB2FKqZwwDpOvX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30539}}, "1.2.6": {"name": "@webassemblyjs/wast-printer", "version": "1.2.6", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.2.6", "@webassemblyjs/wast-parser": "1.2.6"}, "dist": {"shasum": "93cf30b1656ddb685273c8eb55007b181521b74e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.6.tgz", "fileCount": 4, "integrity": "sha512-/6qkf4l0vqfop7mfYeY9wEi4QT4sQCY8jKt51HXuAwbTNM5sZ0IqD+VfNzepuoXu+GHycScIn7WC0pJ4wzNHyA==", "signatures": [{"sig": "MEYCIQCLphRVg5tG6BfZ3altkLutn7VSVmhWWTg/hsL6AbR+SQIhALLS96sEWo0DmVI317w3uI27cd9w8W4abbC3q1Tga0yr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1hC7CRA9TVsSAnZWagAA+VMP/2lMO8cmuBm+R9HHNOdg\nW2X8Y2stlhIw6SQv0+4tGsHRxYzT+Vmy08Yt1vemAbN9jpyYnhl6EDt8AYlX\nhGkkN22qLzMC6hLmXpAnmvf4b4Bg+XfZmvVI/sbUKB/1iqEbS5WiPI8G4q+6\niWlbnmuFXLU1h5BV/Y4Bnbikk+GApyAdxCFjMvom4ShgekeXU9/oPp7UNOfy\nvZSmN8E17kCR9IhX7Jrb0Go+sTrDSM04LimJiY/CGso5T0GH3pRJcv6EXqUi\nDo94W9zHXiTz3k8yTL3Pbblg/Iyf7tzJ0BoKUrlYpCnxMAnigT4vs03x+K5w\nV5zy+WaO/5vFPaxu5uyzbIdaT4raZRRDuppxQp0G66JHqCIzUhC90ZjnPCQe\nPf3Wnl/U60qeDWTu3Cjg/+SGK5kGvLnOrgJr4bZIqMr3OMWIQjlLL6D7JdFP\nYbtDlhP5tE4fJsokcLLbsEeJkC1LiqZvU4zKWTubqcEWA6G90EnDTmctw5wp\neSDvahG8N3xTvzOC8pOkj8xJNznMsT1VpOfJlXAe+JDoa8kAI+k2viwikFZa\ntN6ahaNNOAym/VrexbvbhWS/aJKB/oB4xdnpHygIca1MB7jrqETcNG/sGxOy\njjIH69p20dv2ayGBXPX9qoyKHwwgZj0dczaZtA1lShhWnVp/TracbRCDYuJX\nRbBT\r\n=MXhp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.7": {"name": "@webassemblyjs/wast-printer", "version": "1.2.7", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.2.7", "@webassemblyjs/wast-parser": "1.2.7"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.2.7"}, "dist": {"shasum": "22cc81b123a9c9f4f2166994c737c8f9cd47d1b8", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.7.tgz", "fileCount": 4, "integrity": "sha512-nCGZDwTGa/hkZssDf7BpFIhlYL8KsSG3mxKo+2y0PD4DbrsgXxRMuoeCnmkRgoFkSZlRqlC5BUxQamzJeyIIsg==", "signatures": [{"sig": "MEUCIQCkj51k+3EPTIFpxH9NWSW2thwXhbzoZebjNXVQvF3YzAIgCoUW1fJOlFDYZhCyePigLnbXGD9gudsI9WcW9GcWAtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5yueCRA9TVsSAnZWagAABfcP+QF9nql/4SSrWqYHK5aP\nLn/ZTGF1TX0YS6qO+JKpFAu41hwJiTaE7nstFYD0opwO7KSqYu3SN5eFS8k2\nQlWnb73npXpcS8pFtd0HhFRf5AcvIfJgOZDBnvwl4CJwV1Lg0IXAHBskQYRm\nYlcn/+rjy0+mmO2PijgbsDgTesDouj9HDRMH2C8xZCrp/Gj3HcBnYhBusxcZ\nKVP/6LxTgtKP2dBPu/b5Rn9hitTCfhWVeLrL8IvDgRoYNLwQ8UZGAdtg++Yz\nGEtgC3IsS6BVJVtOZYpFtzT2AcTKTesMNhTTC2aK+EY2p4sirLmiumEm0xpL\nxlBsGouuS5LI0NU1tMmjS7jQ8C/OdqdiVfyV15ALmbB70BcL0H/rrEf5TYG7\n1jxiEg0Pf3aSaO0yIdgq1eMkys32gYB788tF9twUimW5Q0qob8cqBebjgjbf\n0YZKGvOYkd0enNLAjyGbgDgQDVH1hxbpNi1IszI76C0/Ju9iF8KDv3zMIrLZ\nso22zcnXgy/H5l1QTQxqBH0MIuzDZSPWMQEXUqDSAz6jMbKKTb5bdKcAmyCx\nMigwXkuftIkDcnwRH4sZpY+R7E3wRh68L6p82+OkinBoXOYWATAmRZ7OsY5e\nAk15Sx3NnEB4H9J6lBe8DG41QFnkSdvsByJDj9yyMua3elv7+IV7kW2WeNx7\n7zSG\r\n=Lhx7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.8": {"name": "@webassemblyjs/wast-printer", "version": "1.2.8", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.2.8", "@webassemblyjs/wast-parser": "1.2.8"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.2.8"}, "dist": {"shasum": "4ab8c103bf45e8280f12cea5ad9975a59fc63ea3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.2.8.tgz", "fileCount": 4, "integrity": "sha512-14zuE4vspNzI2N6T5yXl2diT184Vy6syS6B8IZknVdt+OzoY80RvuRR/gtwBRKeoEoB4dfE8pwktYtJ0/MyA8A==", "signatures": [{"sig": "MEQCIHXuZBJHbcIjfYCmov1h2G7D1N6lGn+T18Ow890vQ/jIAiAT12gzwKHcAilWWrD5JeF8/i0CFhUm3+9L5QQj9g7YeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fR7CRA9TVsSAnZWagAA9WkP+wWSOyTubCzJ1RxgU/vC\nsthdkDoTqfV70i+YWx0keQ79T/ImYJkX1QfxySGHAijIfcfgJBHXA22PUtM1\nmhUJq+GH0kNRtkrK8RlE+3/o0JRZDXEkbLsHtj7PRv8YXbNE3rKHrpRSmted\nNsn/cLk3hlBgBgkwf7QJ/91rG0JIvb74S8IdzDnfJSIqXlsYsZiZUcPIiUdG\nDEKUqe0QtlzCJ7I0sif4qKPLo9sszx6sVdriMFQc/GF3iXOZWLBt01PAy6ko\njaW4bSeIXYdFHh9mmYuC3O1Nx7UtMWTW6FXfwVLZmG7tgoZkgCrZupuH8mAN\n2kK0H21RoFneCN/O6vzvqHQS/6curyT/PjUCn2we7mWFMt/XOBR2e3Z3g69q\n4sAJM0kXyCvXvmBojxnAZ33MwePkXA1NtZHOIytNwrXcROtXVqYo0YfhMVnJ\n6tZKyH9en32eusrZMjhlTyb/w+PLuNoP4qLW87br2XPKhnCZTmO1i9JJOMUk\nkl701mUXO9LKBXlF+LygUnZnyqLBU+spKcd/67/F9dOPCyK1Cy908nyqJ34+\nHOh2019WVoK6234wCRwQF/93trE9BsBaIvNolplrhl6xyDVPLbtjQUtAFRq+\ndKbrRuft2UdGc1+pnG38kJPi0/r9HUZVTs/WQCqelUQl7zyg3i7cUQ2b0ae/\ndoqG\r\n=8H2o\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@webassemblyjs/wast-printer", "version": "1.3.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.3.0", "@webassemblyjs/wast-parser": "1.3.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.0"}, "dist": {"shasum": "b4ed84f0fea9f222d540e25b262cd5eabfee84d4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.3.0.tgz", "fileCount": 7, "integrity": "sha512-K7iOU1PY2fPviDwZU7f0i/75r2baXlJR3VYXEpwQp608tQiYJY6/4Uji9kJ7FKsf+gqZSop2umCK0nhuDrU7/w==", "signatures": [{"sig": "MEUCICQqSOPNdfJP7zeQ8oTc+5JCDDMJNyxrbjYw/9itiuPYAiEAo2CWUHb2thsAXgV0gfnMYr18f74HDoCgIs8taFenVN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GCyCRA9TVsSAnZWagAA8P0QAJkU1vHjLmsz3W5Bduse\n8ig7yUbzHfaDku8hdO6ZPaMX896YXvnUKPppTFHUA1nT8UlKbxUoCU9JSFVI\nvqDv2jVO6U4YLhTKQ6J89d/7XwoThy4t3a2Y8ySJzwIbS9kWDaO84QBRbJq9\nz3YyRySo+sVE1Ojq7R7CoDkoDFjuHMdsUgNjdS77eOw2/D3jDsjkBrx48DWX\necCMCAP3AjIFK762T0KoscipuA/EChpxDu+uldQDISY28as/uTxFzU607G6d\nPkWDcp4Ou67Ig9h30Q6jc7Rp2BaihCs+h93bvO50Afp8DR1xe+W2B4EwigFw\nRNA3PTezZymipXa3+9MLK/33jojM+0qF2Tlx5k54dN5hofHwF1ZRk2Rzlmec\nd1Dww4yRkHLx5I74Dt0BY9Tspo+ElYAmQaG24nK9AE7GI+fNJGlsTRTfahD2\n5s/cSWxc07Bpk1W04gyT0t0JCv6lXBmT6xBxmkvPOZFU8dsnrfyOSuQVkavD\nG6fT676jsiIr0fid+uvi65z22bLXaFK6Pk+3PuSTFlL7vd0aBL+OUqt2L0vd\n+S4H0SgLcDFx/z8jiKwUdKp+MtQZFsuneozQ9XiZiTugaFa3AU/XrZ6JhZ02\n/hrGoOlwe8bojEmqGtidmdJG0ZwEWoVzCRDb4gQZIuZ9SlXewC+MgwViZjkQ\nTpCO\r\n=32DY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "@webassemblyjs/wast-printer", "version": "1.3.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.3.1", "@webassemblyjs/wast-parser": "1.3.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.1"}, "dist": {"shasum": "3e75b889e6f1ba2dfa854e4436b4287e7687e82c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.3.1.tgz", "fileCount": 4, "integrity": "sha512-V2SMByzsrTT2wGQOjm/3ctCiy8mdYOSLDk+EkOpNHVZWB9ISJc+gZWzniTgihih03UWqqNa1S/0XpyVz7ZEYSQ==", "signatures": [{"sig": "MEUCIQCeFEp0OfDyndZwB1e0NkbRKsE0Yd/JX/dfH/KOlODTdgIgdB57ei8veGNuOua0xobstDRpYyNoB06xZCzxK6ZDn9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JZcCRA9TVsSAnZWagAAqBgP/0wvlMjMoEmAplw+DG1u\nxCRmB2DSjdCf/fIfkinh2DzxhU3rtKqXg8N8OAAHkmhzawc510/SYas5KELz\nyNKg3pTq/W73k+l+LUkGC3OIK2M+2Vs3I0nVj6Ae5qEwcmf7jRlAUeB3Ln8B\nSZQvSNQ+EqLrKUUe/pGDU7I+Yjpdw9vcbRSMAz5v4tSth/pQjJd5QXC7nq17\nDQg97BzySowIjNFJbTvh+A/Xbu1U+ukLvlVZ1m6+eAGsD9dQzRVquDEr4Uy1\nOf6/kNDd+Rhe2UQ92l3K0QMC/sHh85XKBRvlKfl+9j7LYG6WLZ7c84hWZC1m\nRRrj9x+DVss/e3wicGPk30NOE5BN6Q7f8sPlI2a+QAsCbSY/y6ipSyoQcpFK\nZ6SLunny17Ot3gjdNyC4+mJ/hmY+0mlAgiF494XyJIiH7GwQPUfDwx4fcAHK\nL0LyoumdgEoHcD5ppA5Kfw7iclfQtkZIFYZCBkhmCnAsmhY69lIOZUM7663O\nPfD+obLdxXJ1k98KqoCw0gdslnLLp4T01OR2we+FvSqi9huwUY3hMr9YhWOT\n6KWPVk2YB51XDhlh9/dYYFC5d9M7tYIXADOYAP1GzkSTktvO6PrvhDOG6zZD\ni/x/fPYSrl4r1lrsy7r1UZCz+8REwnVZ4Kx4rnOvnNGDCEVrGtWfOpu3rGuj\nxXWH\r\n=yG+I\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2": {"name": "@webassemblyjs/wast-printer", "version": "1.3.2", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.3.2", "@webassemblyjs/wast-parser": "1.3.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.2"}, "dist": {"shasum": "6f3392bb9582bf7537e77704e7a2a84176305d20", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.3.2.tgz", "fileCount": 3, "integrity": "sha512-TbJxHVxyjB76qkmsT37IQeDnw1khKOI80/sLRluadzre/yQ9TqNHiYkLFzepVevtu1Ds4wE7IxXNdx1/y4PEtQ==", "signatures": [{"sig": "MEUCIQCzRsbjFbHVHxx+qCia8pK4W+ZzSB282gvtuBVw4C+bmgIgSFGuII0OC2X217kwTQtA74X/JqSDshuPpc3556RIluY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fDMCRA9TVsSAnZWagAAxv8P/2F5LrTa4HBcvmYwCODH\nvWKRFjuI7OGx9AJJ/xn2V1OBRXx0VO34Z01fUsseZEGqd0uovH9wGehlS8oB\normPeYdKqLy8HBgEUrZDhx/ZLrFiBBbwdV66t7Dz6erdnIkicqtNbTv4iL1H\nExxP7Ei7mcC/eiBK1q2SGGu9pWkVWUn94gg39yBxnPL7UxwLtmOy+s7bQhfz\n8IHvlbZJB4nZVVzG0bLUTLsHeOfhh6eZcGJjGIRjwtfLK6meKNiaogmRLdFo\nnWg4cv8wgyVAZcwm4VR0XPDT+RW/WVvK2SefKUcvW5SiClnlDQG4Rbx0bhpz\njB75bWdTCpn1vaDH0G5yHJNG5cFLoz6Fwh3Si4SzJfBmChxZMxPLb5VRrQw3\nz7vkDAbmu0CWCMImGcXV66h2/MTgZ/OngELHcZxkdhnxXOP0h3XjyeBdJDbt\n/3wGlIRpMNfeQKszrEgrYSAMl44FpNgcThk4kLqNJfRvYUrMu2OB3jVHfs0/\nsCAyL7D/5Cxbvd5t9bcR+ghODJO0wkz7HYPS3Gr13+fgrE/iOhnVtZ/i+VhY\nauGCowEdLANmXDMkTNHNFCRcWrcwnzg7aUBztuU2zqRaKd1FICrjlfUxagXI\nNLlVWwbdG6sdxWD2OCDCd0yHIiqo/IS/r4Epvh9pOJfpo/Vx7ADpEn8fytc/\nkDoD\r\n=xQef\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.3": {"name": "@webassemblyjs/wast-printer", "version": "1.3.3", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.3.3", "@webassemblyjs/wast-parser": "1.3.3"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.3.3"}, "dist": {"shasum": "e55753635e1962eb2e6b6ea442cc0e1daa37c29b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.3.3.tgz", "fileCount": 3, "integrity": "sha512-jPy0aH82RTaT9OE2JVNQdnM3G0HGCmgtISrKsvIAGd6YjlR4F2gsTX7UqwyugoR3HPUZlrL6wvKjI4r7DMgm4A==", "signatures": [{"sig": "MEYCIQC92Q2ReHi8j89Kt6bfvkJXPuoFn/KO9XXVXXfTaSKkIwIhAMQOMpsz8BxfwXn+2nvDP5FPfEDKaeDfVKar94Xxk2zc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qkECRA9TVsSAnZWagAA3HsP/0M/Ac0hZOnXWqaPp1LU\nW1iN6KzmtDNa0Y4CVInPMR6chKLOWoR8gzZ5EKwUwdb14jWZJNvsL7iWjR09\nKy4WsioiNDsDU+9NBr0SmlVdQFB9ZdnqIRPOmS93UIGRcyTjtwwQKtpU4NY0\nPKDZwzg2L7hDVMU3exOQiwjM4GnZDWFl8hVsst7+zm0Gm366zcRbBieZ7vJD\nzBfK6QJ+CpZMfSsAiuNZRoNN7abpEZXnsedl/2MbQkKrvBs8S5FpbBfY2Leq\nBgaMnoVEV8mUIVjjslh5y419IPgDuF6T75+dM48jbSg6rMm7cVWskOenpPms\n4enmmm+cpZ25QNW2bgQGWuA52JZ4fAk+qoo+1pdD5GRHp0YKPz0prt3zEsc0\nBv7ydr6mYPtFs6tMrdPMS/AwgmHSOEAFyJ4+OFNJSNiYiG+SjvAgxw8MyEiD\nY6MEeW7Aqt7mh0U1CgY1fCXxe/YIowvPcPuQOFRlLQRIJ4ldDy/oMYPBnaUP\nxN4Gf2hxt3/F5y9m4I6Lj7ZAFPDr8xzvaxjUw2AbIKFwr5TZSwCpQbpYyrYi\nDh3izG8cxNYTmu1UloNvdwshb7miaS63dUoq18aFyWSaTnpMzcjBjvFR5GlI\nw+7tM79SG63h4SRBUEuQ5hrU1W3yYXfv2N8r8lENZ5oVVk/ID/7EGry8DNlU\nD284\r\n=qNZ9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "@webassemblyjs/wast-printer", "version": "1.4.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.4.0", "@webassemblyjs/wast-parser": "1.4.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.0"}, "dist": {"shasum": "7223a32cde1fee681e9160ae50b1c3d8d4b50f09", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.4.0.tgz", "fileCount": 3, "integrity": "sha512-+rxvZW31nZFtOhjisT/axjJ8hXebgwELlUTD1VTQwrKqX/Uk0JrSiPDsMTJgAFK/Pf58S54n4K1h3vDkiEdOTA==", "signatures": [{"sig": "MEQCIDrtELcRXRO2yEavGysnRkXuoX3W2Bvwgh4WMqVMfPmyAiAOJXfQ/8/ogPT8nl0pzUnlQ60RowpKSMhgl95dBiC33g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wzsCRA9TVsSAnZWagAAWvgQAJHS3pf6HpbsMJmX1N1K\n55lKTODBXcLRtUg/jwcxsWsZ3EadftR7el3isykPm2xoCESML1A90hHwkj02\nJbigu6ZekEWV9+VpV0jbP7v+Z7GODLhe28lZcKGGfQyZKhEyiQw8J5I8qz6b\ng/HbdtVGvTQ1ToxRzxLNmzbKfteqISJTqaOEAAz1bPupU3YRxsEV/sQcFGbT\ndMuIiyocGgE3xJDpqoNzcg5CSgMYh9x2wg9RLklj9CPamKLXuHmgedGyJwzx\nrOGGT95Op8V+o4UjyAYhAgSOpUHc3oBpZG+e6mAujJmzKLyFYS4kUxG3Umv1\nbbVgZQ4QEf8TvubC9nZEOYiP+XWdU7Bt7cXO/Xv4I++zm1Tekw5UC8ejOuML\n2XdC9doG3SWDW7RVzdIEnjg770WlczsKEjw2Fc4Bm/6RcWM/I8vAHhgNmMA4\nigUbXbRTl9MvBPIXFRsHsak5AWFh1sRsWqwWQw43j863EMZb+3wmgGYahbjB\nI5FYnF4L8RXjAowdMBMTwoFqW0yKaZSSz07yEo51/gqunjF6J13KTeKI15Kf\n0Yr4BB44mlgqALHVKjzgYv2OXzG0heUWGMYHl/yTtwlSEV2+mkO4142cNUn6\nC6QAd6RAOElHhv4XKNXacTdGuzMndFe2x5zzVYGXTNAZTotMeTZG6qp+IgzB\n0cQE\r\n=DTzh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "@webassemblyjs/wast-printer", "version": "1.4.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.4.1", "@webassemblyjs/wast-parser": "1.4.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.1"}, "dist": {"shasum": "d9288e777aa40dadaca9896c9e92aa535ae4212b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.4.1.tgz", "fileCount": 3, "integrity": "sha512-PJ9qh94PMoyP4gfp7TPp9Jkzf0SkxabJBe2ycwdcTqmB5DD3hOnobK38ii/3sdPU2Ax+UO+uZBC3J6qWivolRw==", "signatures": [{"sig": "MEUCIEsbU/L0Q26kW7SwrqXjUcbCBgLZ61xl7mdjq3HdlczEAiEAzHuaLrx8e6dWM2tLz7UldbJTSrsE/DN4iWvie3UW5lQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9KFICRA9TVsSAnZWagAAr2gP+gN1p3pUPrSBzhUpoyO+\nzztLxI1Os10WKlqmAPD/VCSMjfJ00HojqI+QruBXmK+U9VCs+ViQ1ronJkFu\nJ8MK8Ppuv4SbDH1gcC+fEDkJohz8iHRK8gwml8mWX7ltTCpl8t65mFzShYB8\nAF2KHAme81abtn9YWUm8pWIJ681tmoa5ms//jyfnh64ZWqngWXpHXYr4nZp5\naUHSuJWXkvc5hDDU9LPykD1ml//PTyi4hE/eSJ0AJXLWAsiPXqA0DAAMbnGM\nIyZ/Txuc8V+yvdPbvFBuRMSchhcjBx1Qgyu0OQ6kjMEHj2znIUuuIDdZDcuR\n1Z0PysfXOFZzwssZlxefStlANfS+sppNzbW22okB/qethHgO4vMYla8EvCsV\nSJ+sj/o8rpamWSfAnUjFAPh0EAOjfufeatqqASuQ+hMqYHiMqIjeDR53Bfg1\nvqa3K8NtaE7FTvfB+HzzGot3VDExGgcUVRH7i994O52k/45Nr6DG+4Q/q4Yj\nW+wCmGRP8Ivwz8qF8HtzZGq8AZ6LzxJIGxa7zs1mUHaaFenZpqO+3BWpXJnZ\njU74n5q8SpTyRe8FMrBvh8VgmZMJ4Da3xOLtLZfSDkspQAXYJewWEJJlVHTO\nhBBDQ74oCDE+7pdqRMqvSGZTk5FIZ6X68ap8W7LEc54cUgyig3FSU4iyy67+\nwRsE\r\n=T9Rz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.2": {"name": "@webassemblyjs/wast-printer", "version": "1.4.2", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.4.2", "@webassemblyjs/wast-parser": "1.4.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.2"}, "dist": {"shasum": "ee70a828f0d9730b55b9a5c3ed694094ba68ba57", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.4.2.tgz", "fileCount": 3, "integrity": "sha512-ZxkLg779XKIL852qOcskwI7a0Jz4hDIwN5LQgzIqZ/9Hw1b9yq92PBzickHd/tYyZhrrKqz4IbfcqMt4rIrvqQ==", "signatures": [{"sig": "MEQCIHCteLCpndPK91aIdevx3+qh+SPEbOjfVxFx7hrZ8ePFAiAHaci5MojcC1KMjbF5Ubc4Qhj/tluFmt1yoYg+WkS2nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aUUCRA9TVsSAnZWagAA/Q8QAInvCMuOQ061NsRsy70U\nues3cqpaCiB5KkXuaEcDAEPdSNQzQtwYCnqPJLEcprEb1Nf6xumDnLgPruRX\nZrzaph6BZnY2yPjD1nlWvH1HnEh//sfYf+ItCUraKgxN8FlHPNjNFaiXvap9\nUFoe1Z5GtnWMVOjdiQfMfDNiF4HvWoG0Eig2AOclRKvpqEjimdgTXCmaXmvj\n25iNLoGmd6UaLUf1Za95iMv7/l3aWWTKpBGiDRsu/6mFiaWv+j0LWQ2eY57A\nYwUuXtYAmNJCMbRV/+QKzFb1IxaAIdSyibzu054jt/0IaQwBBhU7p25w3Rkq\nNesRvPs/08r0y1rBBeMbm5yCnhOkohqT1sK5HcXRVGTYGyJRbT493mk2FHXb\nOs4Py5lR3Jozs6KtAtlbqt+h9HtewBtL62HdD4Uz/u0o+KsvRKerbe7FS7/H\nlOH+6LhrwwCc+KGQ+VDOZjktiTvPx8Y6CWKmr0aE+NRnhNDl0kBQs09Pel3p\nem870DaoodSb9Rnm/laj3+HGjbZ1uk7obUrg1hj60TmGBYn35wQ2xAj+Pfo1\n3i/SC/HkNnxZKDJYxl5jxGaNouonjiThVMdlwfCfRjv9HVJ2Wsz0Euiu7Itn\nBOEkdx4+Wded+Quqq9aJxQ4RzlJ9y09S6MySi0YD/Dgi4AXm3ohAlXS/Nsmg\ndZke\r\n=NSmn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.3": {"name": "@webassemblyjs/wast-printer", "version": "1.4.3", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.4.3", "@webassemblyjs/wast-parser": "1.4.3"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.4.3"}, "dist": {"shasum": "3d59aa8d0252d6814a3ef4e6d2a34c9ded3904e0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.4.3.tgz", "fileCount": 3, "integrity": "sha512-EgXk4anf8jKmuZJsqD8qy5bz2frEQhBvZruv+bqwNoLWUItjNSFygk8ywL3JTEz9KtxTlAmqTXNrdD1d9gNDtg==", "signatures": [{"sig": "MEYCIQD+HojAsW8rhRp6TIVWiOQvSiz0+LkkWHc04hroEgHC9QIhAL+fbi9ZuhYw839Ug1Pd2Uj8H923pkZb/FQEU+Rgp4s9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rIgCRA9TVsSAnZWagAAJJoP/3N5vKVDKdyBGBLMeIpO\ntz/ek0tmeAnD74bdKn6II1brS5nKW3uWzx5z173KtXRw1NtL2t9y5xc5trfi\nRCG+uwucTtInOv8eVcejT3bQzAGhYL1desszlTa4sXLPIwz0inYsnoxoTnrC\nxytALYEWvV55oBwHXQ/9n33xX9XW02Ti44bxDMi+NKJh7hAwuLmudr07L/a3\njiD5JxzITiDQxDupC8e9kS0sn2DeHym2jxmkhTzJ26hPRV5oVmMFN0IjUyJY\nTQCNyX+G3ACEPOCaM2NP2tBJ2T+XjH/InbdPpkR37gMrgJvSzB4cBIKdTLlI\n65IOxBJTxTeiPOSQi7E3b55vo3Ad0/qSwZ+BLXiXja0/RXNZ+fa9FBmRzzxL\nYKsBKKl6tUVTu6ON+qgcb2u2OveBLE81FlvjNlLfjF7fY8JDc8QfQ3MzHy6y\nWCkNhTNZ4NmMQzVpenWIioMij4tv0qBshpSe6RJP+DTyh8yFtEJScb2D4UTn\nRvENS0JZ3C7j0xLTcavkWXPCrZYEat2PcyrWjNOvRNBgQum17N3qjXc2+ZMF\nVMlK++kzCaHXiLFnl4XeDyw0cdoLC86cEgtfivq3/Q9E9gh+lzhwiY4QeHwf\nsMqV6BDHyWQ3DufN4UbKvTb05iXSTdCsZMYlDaGTUpYUvCm7fn30qQUNOT0z\nBa5p\r\n=1RmM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "@webassemblyjs/wast-printer", "version": "1.5.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.0", "@webassemblyjs/wast-parser": "1.5.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.0"}, "dist": {"shasum": "a2204d71a187936df86ce8f6fbd7157fb229eccf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.0.tgz", "fileCount": 3, "integrity": "sha512-PfHCsDy57OtvhWtcMbw/Gtz2bTC6zASuuJNNL2V099es/t/6x6x3bezWfiw9cOdTCQLWQk4LFWFivWDv61OtHA==", "signatures": [{"sig": "MEUCIQCI60gBOpGiXwfcRitly5wYo2F5issxgkmL3zxYDP+oPAIgH7ewGgK2ZlNLl9ghpEX662TaeHhITLZnNrVggTdyAu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b2sCRA9TVsSAnZWagAAMsoP/3WDl4S8mci+mgSs3elP\nnL+C9RVfSW0JJ2iGYwbkNmNB6iDkNKNeJ4V7uq0jKvkFD7njbaaQWMXSYEKp\nHGTNqlaMY3ksixrDanYCGADsIpBEsBhAY37G4etKfw8FgQTTFN/YIhnQMGNT\nv1ZkPQlf5JTcA9mRlvMMBuQKcq3iJnQllFWOZHpYxK+qT2P8u/XdeMW24+P0\nzzCPdD+9kn1EzOOCtR3UP/UGAKxPg0UyNDGlPI+obyGMi9vZGiCGCUW4GdJc\nR3ouF<PERSON>33cZKocaXG9qhm6/cxjQWw0lSJ9rgJufgIWymX3zjSxA5tatNaj2P/\ne7XHUeDCUceV6ImbP3AdYVGmlEjpnMimNN2fPiPermrOH9QwEh1xlqzknbP0\n2jPkWlATktiBmTJ1Ob0znWGiD1TAvo5Qs3JLFX020yaF5Vjmxy7A16LuwTgu\nsXEF2mEjB8MvWqwH3ATph85lgr+V8jPXRIHYOtGNMA1Me2zwEalodRpB6VFs\n1IoS8FPOqjTcteFNeIPPGwBvvMe+O4nAqElHG81/rn7MGcUUDsn5+D8Inrck\nid2QFIEGsqt5ZSTmDxPvKMY1PeUxqd8AviB7MynWA6qas7c3s6jayxu8cpan\n9T1yL/eat9olflMvtLROe8Y6hFU1qyw2l7mdu4JFsRPhCb7tbMON0zEl4+wm\nQRZA\r\n=ZaUB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.1": {"name": "@webassemblyjs/wast-printer", "version": "1.5.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.1", "@webassemblyjs/wast-parser": "1.5.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.1"}, "dist": {"shasum": "91f52975decdd98051d28bf7fe89c1faa978d9a8", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.1.tgz", "fileCount": 3, "integrity": "sha512-Ei2ikTVLHQAC3A3hDZ3kGQqZAa/5/RlwtNkXbp/T+9JMa+2dC2/DQWy/NAy5pLqXRBxiU6tc7b5bVmVSnZtynQ==", "signatures": [{"sig": "MEQCIGVvOpll3QU6eG6tC9+ITbt9ftdOuRcWChdYbybOVhbIAiBOtHyCEHac0Vz48ZjyUuzu7JRr/cCv1ILz6Av3VtV32g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BVOCRA9TVsSAnZWagAAL6AP/0E1qSJHgnV5fOybgyJ+\nHkgdF0iOt7wOs1tR9dLbPqdgUK3yPUR0ItI7t1ouAyJttEHJHl82JK2N6ESU\ndggAeprKUnQgQQ0b0V5J5VYwTys49f+gw/r3Qz3BcW2H6PUuBCs6xoUeFgNL\nGpaqewsQJia+IL1cM1nr5Ok2lV/Zng/3Jw4DsffGbNp4n3vITfxRHclzVNX2\nd9kD6LD/xYgU3YZDcWBpdRPrXmT63jwdVOrIUljNlNW9XeeWukH2k3P2qtGZ\nu6u89yMVOAc/CbTnVnGE6042AJ2GN+xP7dxpSxCpoIjicjhNS3/1cZmJ6ltW\nZvZxJB+sP3sW1dNc3jaYNektuuutpwwQKHCQkoedf3ubU3fcdtJbDtNYE9UI\n8MUt/ecARqp/d58HkyTNffZ+E/OdH7G16aknlcKF4ANHfZILVoc6HW7gRifJ\nLhRL1nFExRjKRQ00UYjKp24OEUuAHrfcLQZzBRjIWhIb1w3YU00TTjdLrt8b\nLGzRqRpxAacPkdT5EX/SA4lDVKJM145zV38t1yY5pUoi9BT1wtnwVu5EzKV9\n3Om0/V9PNTO6YWpPxjV3pUB3JO5K2QYT31R2rYf0nsfg80tdCOfPuhcnGcdB\nS0F7JLW8kA7MhlZNPeDFDZykIV1H/K0+7N90dYvPcHk/yr+OzTWJL+Ea0iKC\nJ5fC\r\n=F87z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.2": {"name": "@webassemblyjs/wast-printer", "version": "1.5.2", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.2", "@webassemblyjs/wast-parser": "1.5.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.2"}, "dist": {"shasum": "eaa9850ab975f17728e3bd5412fd765ecfb5f47e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.2.tgz", "fileCount": 3, "integrity": "sha512-jVGzmiEOh0LV7li+9Ea4iYcmoxsGc5iPW2Paah9PjCCjaXnsBT3HTRnx4BlX616QuxJz/eHFEv89PujGh1gEWw==", "signatures": [{"sig": "MEUCICsuR8hntabBsfPdJ3yV6O6XA9AfsJYTUBeSpm5QOU8wAiEAvQ7WCIcYPO0Aq1VZ5PQAtjsaKcl7YogLr/PQz5BfMR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XrHCRA9TVsSAnZWagAA4TcP/3NyFkJ6zJ4wikAAOh9i\nX+dJhFJuXVmeo8ot1P1JrKntoGwjP6cyng6XXUwpGwl5viNNNZu/GrzjozGH\nqkMe/QIacvtUlbubFOw/1nmihTEjbC17oofhovsb9wBeZc3FL6e2Kp2Z8uuS\no/bDDoBfXcBNal3jR69yzanPvju2eZgEO6EcMhNKPlP5+pG3yyUXmZ9XdRIF\nzLXj/6/lIxx0LEqbnacklAId5ZcisBh8Suu+CnOfNJ7V8E/qKhUclSIprfWd\nhDihXpeGpmVI8HfvG0n+9JxzF5hOXCdiOYWvs7VgvxmEjivWLlVgdkZxaJGJ\nsVS6YBfnuyabfgnZvrPsvQ+sd/FItWK5HY1JjrvLAPM92fkZd0DQDvr7B4fN\nMJ7s2Db0xyVG3sRaiSIYpulAaPRnOljfgWEf1ICiAKcLGt5qJyBvJkXXfxf5\nG5U/7eTLqtK6WlzeeumzelaYUUv/DUD6EXxBrwSeUTT8BYr1YQx1O3xanTcz\nk2lCK4uAuEDFf+LyHQeoht2XEpFhxDPfmgjFPrUmAdjo0pQnDtfhxxF4rcZb\nUxJtGEQbU/OoSy7osvceCo4R7wXgdegVvOguW//H0xNHiTa11qbbel1L/kiY\nnArcgAtXKsW1KJLjYKucWMO5Ny2gHO0ASKCKGhzpGeIAX+I/ZteSRZYPVlYV\n86+a\r\n=6PoB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.3": {"name": "@webassemblyjs/wast-printer", "version": "1.5.3", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.3", "@webassemblyjs/wast-parser": "1.5.3"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.3"}, "dist": {"shasum": "593ed61a4a42db17cfa8e778472dd067b12b1a89", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.3.tgz", "fileCount": 3, "integrity": "sha512-Acz5kiH8IPEx1KIJF0vpXJTqlYC6lMDjw706O+JzN+gpVeGZ3+epTk77i0z1E2l0zDkf8ayHtvVzWfiEdqLfoA==", "signatures": [{"sig": "MEYCIQD6iz51DahPWwUsgEuh+ORKgRTgi0PPtoCuHmHU4fNeSwIhAP796wpt2mPl5EuAxhXjn6bKAG+7gNVOB5QcnUs8KOAW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqjJCRA9TVsSAnZWagAAzZsP/jhWusxYUa11Tx/lPcEf\nOP8fMQnTBWejlGLey80vb8bkLfAvawA3Kyy+KZRpQ33zBgove7Z3OgRDiT7h\ncPAdcyIFasQpj5rNj8rWV08J4PPEaLmd2P4lyOdLUKFkTOucesHUvDo5fvnR\nBcNFhrWJ7Ap/XNYCc17EAiYapAdSSxyHT4sjWlxYSDB0v4p2GmQ8FrwHSYO7\nFxtcZD7d3DMoUuHZk5LLwQxxJFN6fHaQLiKUN6VJLKg0/nUPCmfawWPlW/p+\njwq2XhKtxg5/T07W1QVoBj34xJiGJSZ80ErHPR2AlmC+HHfa2XAgVFKEHCb8\njGR2mJSx7OHoU9SOK+cVMLMheak1Xxg4crL2tQcnRfzK8FqUSl14wf8gKVeF\nJm73tVCKWoCnKB6MYysQZ+oP5HXpJ+IhTrxMCXYqMP5+arijLK8J5TJXh9jx\nnKex/Y/jGEzGDkoldjDNH4Dhy1Sug8gfYIP4s9dVdYoBHldKKcPYzIh9K7sv\nReKgERVbgLUfgXOFYD0t81/Hd6//pFMviuC6UHC4AeFO2oUzHDmwsSlG45OM\ns+Dmca2t5WWQukFvwF6ExVxe9X5d6TbyFvDzrNEEo8maDcu69+9fgtGDJ6sm\nw1ghZJVzncEteadpqnDs6MbkgNxnMynsU7ALwjoSwMF5LCpcgFssfbyg4z3s\nJiun\r\n=sWw5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.4": {"name": "@webassemblyjs/wast-printer", "version": "1.5.4", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.4", "@webassemblyjs/wast-parser": "1.5.4"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.4"}, "dist": {"shasum": "c02284c96735537f15944dfab5150df3935bcb8d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.4.tgz", "fileCount": 3, "integrity": "sha512-xs0fQAAK+SHxp0+I5ucD5QSA7uo67q/AT8HB60h3+ePAC/Y+Kp/O8ytfJtzJTWbKXTS5AehNyI2/YcaQJKhCDA==", "signatures": [{"sig": "MEYCIQDhDMpzXjesxBKF44ZGmFk23FT0mhlUSUTyF+PTXc1qcgIhAKmT6hz7XNp+oW0lhJnw5ZQvHgqhs4qXc3OacyAkBOHy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsJCCRA9TVsSAnZWagAAnPEQAIEwdaMuAGxaobBz6Hc2\nfd/k6cva4wiUDMvEgiv5tTwiHkxgViVQH81BmWzSyECBSApo45g7ToOrQbex\n4+1Ru0pZQxDNYsksAqumnSMVNyzeCSanIyRk/79TDO/Yr7REqalHVwx6jn5W\ncB4944xYAV3qIFGx+j3iNSmJA7b30iywcPoAJOechQNTMgrjDAj0Q7vSjCNO\ncRD7NWEcVsyAxFHBQDZ/F9VsEh4xZF78LswPJqnhigkCK+17KFQWgV8MXQMd\nBrM8NNMsGCk6UO9UzkoVC4tlcQYv+6Mfy3TrbDXuTsS8QCHeOqCJ31X+AfjE\nzqzARCj8y6dTxlhPIwltpbqeA97cyTE+oFIZd/W0fhgMjKkuMV01TYfT+MOA\n1ZXNREcjSyTAP9kV5csGTu7Ln3epqwfeTfCdbcebTg/CinrXkzipNcsXQl35\nMxUolCy+wu/j3/L3o6WhpyUjnROwXHgM/mLZAhwdMOhhbNzxQi7MDavhTuA/\nrwXei+lGOuADwVL3BAJX7N6l3uLSJLWT3xwCFz5RnE15tM40HSxu0QZFVCPK\nTPgcWwN5YQWj3lDO2B2iw6/j4a+TLA3iIZaPNX1HbOYBW+AVPkxKWP2/mEZT\nLI6YX2ibKebam9z/Mk97ypcbJml/HikEttqqKlxBngsuCPy2NVhpKOTh3dfx\n48z+\r\n=ieqO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.5": {"name": "@webassemblyjs/wast-printer", "version": "1.5.5", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.5", "@webassemblyjs/wast-parser": "1.5.5"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.5"}, "dist": {"shasum": "b5b875074107fbf8b6c6ef5787eb8c949d3eb235", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.5.tgz", "fileCount": 3, "integrity": "sha512-Adqn6rCjxdolJDrZkF8/gMS9hEHFWAzAf/PC7eJtwDEiRm8tkfMiTaYVkBFSGg+IaICUAg/4l/mPuJ0OMkWa0Q==", "signatures": [{"sig": "MEUCIQDD/JawjUSItxV6NqNyvULMEgN4umwzRQkFfRS3OclfSAIgYxwaESZWgsMYDPrf+C/ONvdkrVUUpaXgstDGEzylq8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnGXCRA9TVsSAnZWagAASCcP/ApK5O482uDXon+zRhuO\nPilPJoiGg5DKr7GixSfd75wqxSP8GqVjHtHNBl148yD344yr5Xnv6zvZ68qP\n05fDKwwOWAOMViIXAQ0q4oqO8rYgUg/7PAFWS5tfN6S3AmV5wueTRWbVGbMI\nWBUgz9Hf/ywkF643f3rdbolbcGkruhYFOoo/jcgbSqLDHjfnFF0qrgiEo7i+\nB5hb376RAWxYIQIi8hcD0AtXecA8yUjDidCZ9e5+4Gh+T/myGTwfILXCChr/\nCAihKPooV47wNsYNCCwslMVxgsws+ikC5aihw2XyIjF4opvZJr8uFO7WnNUy\n1fWKRS7ZtQJe9v/G8Trf8DU33mKdQVFac8KrphAocmQUVAr5s+gWRcgQj3jl\n2lS2OXtNNVlZDlNYJgpOcB6WE9joP2i+asM+DQhqoK9OOxk5GVXUJPGd+wG+\nM/3CnipG9KNlsxyR1FM74kdd1f6q3j+OMkWZunQ8imnoiQrKy9bYWFCGpFvI\nXlIDttHnT1PLjXykgnZ8Oc68ZJK43XwVV6cUrGW+h6C7kQX9aEsNLwdp/kao\n7FMU0K50NZHZVMXT9A/+YgIjPHAhGGXMhxzV6vQ/uKetNa79aA4Ee+qedVwD\nG18cWIgZ3H4nNxQnCp9YMIwj3K8BJPsM2DNGmy1IjF1/l6DFIGJb8Qi70hah\n37QR\r\n=XKuC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.6": {"name": "@webassemblyjs/wast-printer", "version": "1.5.6", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.6", "@webassemblyjs/wast-parser": "1.5.6"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.6"}, "dist": {"shasum": "363bf9a2ee700857bd69f94140de6fd93d73dc7a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.6.tgz", "fileCount": 3, "integrity": "sha512-agcRUq6BjSivU4dd8hP6c99pwynqE98a/yn4zFRLKmt1F4fmEVlzf/nlcH4rl4uH0NAbR9Iq8DoCtXEsjBFOQQ==", "signatures": [{"sig": "MEQCIDVaTbb8hb++1yV/35V3QJFF2vD5+4fFBTh0nSmd1U+5AiAC8nalo5gJY8TWCOajm0ndNtkbqvdPChbdskYX0sTclQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsqdCRA9TVsSAnZWagAACEwP/3rGof9LjsYMo/YXwDCq\nKnJUf+43XIwIxxA3/eMrfOFRnqlJFhstVfmRQINJac7xhPWwTB4qxsv67dT+\nMiWEELlezlX8+thbEiUxw2gM6Q+QnxIR/s66kDYF0X48tN4utdY0oMU2BPD1\n5Rlsyf189n8FL58aXA5aTOyb3r67r1vE2zGt+1A9Zf3zMYJXwMdGpxBHvr7J\n3D/ETAmQaIsWGZab/pBrHKp3XBkXfql2u+dbC+uv1eCPePXtf9G1rEkHidep\nh9rwKxnTUuxLESo/lcvuOkC4tYplhAKvCfxmNmF2KIunrLoVPiNMZiTAHE4m\ngzQgSR4oCcEEHq5vctpqpp1wByn8+K2Xz6oVCVrHXUxYQWt5u8vAckr5YNVu\nADPLlPIdQebxOyXPWR8z5rSDQYuyB7wT7kHvQ9ISnxOuFSJmKLDU3d07DTHI\n5V6FXI1k2Yu9CefWA4dw7u7mqwHeMrPfCsupj9oA18t01aGo8uMnuejE36QO\n4W4wdxurKzzf3jra70Wgd2YXPhKZEA8qd4E2Cl1sLfw6SV1HaIo57VEppUED\nJzWsywzE2Sc76mmVdaM/zFxcycuf4jGsYBjMS/q4ZgOl77NLHZEb6x0NxAjK\nyqNRSydMtUHSE0kfUMF8A0HGLJkLD1bSsWe08aTIMM/Uu9f75A5NQHxk1CK7\nQupm\r\n=ShKp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.7": {"name": "@webassemblyjs/wast-printer", "version": "1.5.7", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.7", "@webassemblyjs/wast-parser": "1.5.7"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.7"}, "dist": {"shasum": "89d74f7fbe0ff5560e87a50afd28183074b6327b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.7.tgz", "fileCount": 3, "integrity": "sha512-npeYrFrSQ2J61jfg+s79UkQuhEEVKyornXFFXplh8+vV+MA4u0D9IkI4KM5yJHAH1BqmZHnJu5ygfYwgpsm32Q==", "signatures": [{"sig": "MEYCIQDO6hRxNeo3UhPpcTN/vuDy7u0CosBUAvTQlLCTSOo0tAIhALCMdA6ZBXArQIT6O7bH8denPIL4h9PjtmjfCHuHd3wy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAejCRA9TVsSAnZWagAAh8IP/2sCJdd2eJ1Ugmefii46\nr89Qv3X2gCoGvLEn1BhPBaQjgIj+deBcvIg/gpNZoWHpPxq51k5PTIb1uJBD\n5X7BI1yqUDv53ZJWDYGAGJS3DY8453S/eRoc3CkRo9vuYbEyhcna1iYWKQ/9\nz16tQO0IobQ2AFqGsWMSQXACdO5BI2RRTD2AZ4Z1CZmT2h+Z3tPD5LC1jPF4\nde1x5qmIvdbL+RV+Nb5yEXNanoGNdib1PliTL0l551gspMA7/CPvnXKIZUht\nBK+CuHgx9Wo8EBjxRGISmeX9dB2aYqVXlzPk5c8bJ1UkV/FmPkYAzsg47X7F\nurWOFucOb5LsQTYXHfHGZlNvU8DhBRWn7X86Hi3/WFEpGVv/Giiq6tAl/ps+\nEk4ec0IjhDcb+P/StLsaZMlndxRqVcXFRd7FIcvBrjiar6KA36zDVkysrBUh\nQkndHKEmcET8BDsfkW/J9reJRHRzN1LyPoakhO2Qm3kIGr3sxxBbkql1ncXT\nOmKXWhYzPiMR7joJmXU1PqeGSsR1lcb6o/XGJtC5VhLRDqKhteymy2Lzs8qO\niC+RXdzh49XIvCpxFEEWeOCiau+UUPggZWuibww7bGqtFKZLFDajDrkgkwIk\n5KPY7Oyus2ahgTKsqbVt0nv0t0IINvWevrx4kUbFZkG9qnl8u1jUucxIq4VQ\nWGxr\r\n=dmHj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.8": {"name": "@webassemblyjs/wast-printer", "version": "1.5.8", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.8", "@webassemblyjs/wast-parser": "1.5.8"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.8"}, "dist": {"shasum": "0f83aa67eddf377dd1d6205d4a4ac976db60e1f6", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.8.tgz", "fileCount": 3, "integrity": "sha512-5YHpCSw/KiP9pItfKRgbGlF6ozUZSiEd1mR6CjnaYdzLoippQyAfz1qyxtH7dMnD7TADBKpYWMe3wPnK2B11Zg==", "signatures": [{"sig": "MEYCIQCg87wmcTpGaDQlco+Q/3/Z28BAHYLJ9A/N5+sYA9Ni4wIhAIFRFWTVodLXPUw4PjJ72Yz29GV8lDnzAdnQGCleMJaZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/riCRA9TVsSAnZWagAAWcEP/RQbEe+H9RtEdkBGAub5\nOlv0nHZ4x7fN3O3JZopMZsZLJeNnpzgWqQhm4PT5ZJkj875rm/O13FE7zcC3\nvwlXVBaTy4LyrjD6yc5tm9XAr+M5POTk1Sc8xwVpkmbb1Rqzbx7/uts+oKKD\nWSUztUs0cg9gaBmhQ+2nWpWvOKx+ZnXXnHGvXRGcaV4S89QSFeShla86Bt3Y\nKBU/LmgoKqd3whzGFGJTzUo50O6MYA3KQwp5US+1tUtPv4yh3fxJJI8FlCpl\njpwsPnqHDEu2DI0MG9rnpmyQA78I9GgyCwCoXe3wWF/NTO679DgIMwbtXm0I\nTONsJXTQ/3bCmZ5KL/jd7XxF36JIL4JA/ooxgzvNQV/dS6tsOrDCIRUq2d87\n0V4wAgTyuPyGJxmG2h7wus52lTqBCesD4glqyk5lkmSqLfQ3T4EkCmCMMFq6\nE6xL9X8hHSpPgy8o+6fAGXzamraNP6YbGl75lRwKaM6+Bz+OU+sk2UrUq4Rf\n4LdBMduffFjjqz0Uo401TuHIdnTbLcVOrexAy3kwjdkijb7S07efE1v9slBO\nsFiXR7reTxs8BbNFicSHf8iRv5ORIG4V+b9bb5yV91qgza/3lw+1/rNoVR95\nZtE4emYsciLTqlXtOm9tzxkznCXmc4gem6B3TEh4zLjPXditcu4HPXbdbV4k\nv2WU\r\n=4OWE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.9": {"name": "@webassemblyjs/wast-printer", "version": "1.5.9", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.9", "@webassemblyjs/wast-parser": "1.5.9"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.9"}, "dist": {"shasum": "16605d90a481c01a130b7c4edeb2bce503787eb4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.9.tgz", "fileCount": 3, "integrity": "sha512-04iV32TO69kZChP3DN6W8i6GCa5UtEn1Lnzb4sQGe5YNjIFz2k8+KZLxbovWIZgj9pk06k3Egq/wyD98lSKaLw==", "signatures": [{"sig": "MEUCIQDTKN1n/eUG9UbzIBjr9jjd3Dwozlc4nwFBdwslbUn1vgIgRQTJeZHXW2ebNW2AvWdfhmpzCMY2f9oy/F5xRFdesFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVIfCRA9TVsSAnZWagAA/xsP/3zf5R8j28dtyWRnZ9te\nJwR/4Y808c1YM5nMjYMJnSZRzHz51B+bIL0cHZTcEmJvOP/UGWnGb5GKfssl\n8HXEP/8Xw6X2Kob1y65NJMkfJe3Ch4BSiBotXaBqa28dYVEsww+KfJm9O/Ai\nkze0A8IMdeSwdtIOVyfQTBkQ+LfKCaXHK585kFFLOfGc7vrMDgLpa72VHZp+\nVUs5iYbOfqlL+SPWAGCpWNJUcR1KRPcrxyOr/WWY0wfPgBpkBvZi0aCCrC9/\nMfgH+OuwmQYCqYpzpIgO63+C3c1Y+N+AAa9c7unVHYKReQH7CakxZuKa52TJ\nGN/YuXXO5hPSwNVdEyQdYH8D9I1Gp4Lj+bXK32jUYVCyyru8sOJEmA5kLR6r\nAADFiXRoXhWgkT3zWJT2Vn2iWtVuDVLHj8yDPrY2hpeg7iuhXWACxLIGN/5v\nWfPtqd1eVTsyrZheSkCMmwaVXG9tx5QUgdr40xDDckRIpl3MF9wxMk7LJNDg\nTLF8ZTte+IFSCGtRcDt2zxx9FK/faOhfD4hsk+z8A5iiX1yLB0LCAcas63a7\noP4H27L4CGCVaS0OrSNjVgGlauszVAiUcmY1cLjDpdZrSvBBeoEUNNw2xt2t\n5xPH+TLDPPgZoPGCn816yTmfgSXKpv4r/fg4TpIYUHSPYKuGabfG+xT4jmit\nHfDN\r\n=wAcp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.10": {"name": "@webassemblyjs/wast-printer", "version": "1.5.10", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.10", "@webassemblyjs/wast-parser": "1.5.10"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.10"}, "dist": {"shasum": "adb38831ba45efd0a5c7971b666e179b64f68bba", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.10.tgz", "fileCount": 3, "integrity": "sha512-n4zZJmnETVc4RRs9wAZQr3dXUwC+Yyx+xwkaWdTk36NqgM89CPVLBpw8htKyKG+BX/tgk+VOXRwO+1x5Cf3J8Q==", "signatures": [{"sig": "MEQCIHxJw/T33Pcb4+Qw3TqN/3wRkffr7y0rwr4oEV34yq3tAiBkdpX78D6/OvSbjHTTLZARUfdy3xgS+/Vpc8YkbST3Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUhvCRA9TVsSAnZWagAA/kIP/0DsUoU1eHH4eQP8533I\naqVG33bncRcIaSs/8fFFUvj0QjHKWTvzqAPORzEp4xoDiL1YBAgIDL/e5Yim\nOFHL6G/dVYBJtzZUhHSJYXmFyNek3Ti9J0iHbXYxVoGYsmrKXZw+yuLqWEFN\nYFRHGm/JU3G2Tgr0NaqaPUOVIS5533FCu1zCACY0ffhlhGJy0/FZHFTn0tAf\n0gFdbuT7IAtBHbV84Lu9+HvF8tAeMDGayRB/Nyx4FqGP3A5FfDErQj+0W2UQ\nWOVs9FxPGHwcJToOq3iCsKUiGiG1UOuCtnC4LpwH0SytbCMK/RIQOwEaR2/p\n/oUiyMxYZQHmtp4VteeIYQJDxS6S5rlyld68BouOJxv11TehlO6/0dJI7fcg\nXGj7W1kwnB/K8QcKrUkf+KIlJXEp3gjNJbGaGhIWCHX7+dDP5pSej9pGuMB7\nPPuYsPAIeSu4C2/tyDfS/R+RjVBpmugV4pm90GP74jOw29YUeKTSnAY5cYr3\nhxJ/v3dMxjP66A/GAee5kL0plxsYSstKuN+QyE7RYF3Zn2PDgHPcvYjjrfd1\nqbKfnsf1fgJ1cqSpWyVtiCrnJeA7Oc4BfFADyRe1txKEdB25cgZsXzVjPJR7\nm3qf5sbfgtTfVrjGa8hL3bPrWLS+moYsi8zAtjrlGkFbpXklT6agyLvyb196\nQEtD\r\n=Zvo3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.11": {"name": "@webassemblyjs/wast-printer", "version": "1.5.11", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.11", "@webassemblyjs/wast-parser": "1.5.11"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.11"}, "dist": {"shasum": "f7b477751609d7c36d262eb4f699922785f543ee", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.11.tgz", "fileCount": 3, "integrity": "sha512-92OiByBpEm944KGaLyFXFtIeJnZZveUTLHeTmac5XRJXwRsLnmRxNqs3DaSfC30emnZg775IoKweSlZdQrrPdA==", "signatures": [{"sig": "MEUCIQDRnT2EktM0QHOkw0zxlQWxl8J5aFVt5d8kSLMDRtFT9gIgWkXG6BIqGY0YBausGd/cUZmg3EUp9SdQ2e/SApepISQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6JfCRA9TVsSAnZWagAANKgP/2Q4nRLE9+L2ZppkoCSH\nz3JAIQwhskw2LRiv5dN42GY2Vcyr3rn4piui40ZtHITsz7wma3EZvB+J3pxW\ngAVvfGJxzWkzs7eKkbEBeSY7gRdh+fw7Mo7TmUC89lYmeiMmeoThrXqkpG6w\nVH9i/Hg+8plNvCfY/sDjz6FVTpSj5VdK34jZyhZ4A5mVbbFrfYmAh/ZxhYWG\nGUE+YqSbkNPh+lptjTlvaXikL0xRzTGPH4nv+pfLOT9metuJIFVDj6dW9Gtx\nnKpTtSWO92p6Bn3LYCbmGQ6Ud2yY7AyROJ5eLGVRTTBs+WSSuHYZcQs4amv6\nGGpplPwG6qAmSnRI2vm2GHNAhOVnLacPo4WL7Tvjx/IjMmDEJ4M6pXQiz9uq\n4uJ4vVyIB/gna+4ynxN8pivrhGxh5KoKJXtwQNAQCLJyX4Iinpd55xwqA+SC\nBC4Bblg0PGLg9JYF+Dd/ZE8CZWaM3magddNhpghzQNTePY+g+vmsopbu+I3A\n925mrlzew9ADe7rGwACMIMET/uzFWPxtVSWfP8HocmZGfGFKJDODtGZcDruP\nq+C0G+DdJNHmPkigfBbOAEgkAInATAsImlP0a4c6GYG+ifU4NBJZ+Je7B0xE\nme+JdP+JUd1sWfNOlL+OrUrJ5AsaSvSzlJCwO+Xa9JqruM3iddPhorKAVky8\nJj+o\r\n=UEFl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.12": {"name": "@webassemblyjs/wast-printer", "version": "1.5.12", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.12", "@webassemblyjs/wast-parser": "1.5.12"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.12"}, "dist": {"shasum": "563ca4d01b22d21640b2463dc5e3d7f7d9dac520", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.12.tgz", "fileCount": 3, "integrity": "sha512-XF9RTeckFgDyl196uRKZWHFFfbkzsMK96QTXp+TC0R9gsV9DMiDGMSIllgy/WdrZ3y3dsQp4fTA5r4GoaOBchA==", "signatures": [{"sig": "MEQCID/cQcq6X51nDIRjgYKmCfDYB58Nu3IGt8/hZJ2o//aoAiBffhvBEtvq9IjVVO7sFlpHZDpnbX03OM2fGh5A3yw9VQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18025, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPowCRA9TVsSAnZWagAARYUP/0PfpC3imEjiCJjAUJC5\nye4r1grXKnoXMaGcgr0k4ogpuqxrQgJbJuyRAFfyyd2p1hqqy/GDy1aYuK3G\nuQDWBNUr79eajC2Ze/M/Wp3nMbqSKGyK47jcwaP6xkacA4aKLmaw6w7hMBht\nizTeQDPKICcihU0Lg1qZ9o94DoiNbgsH2Sud4dyDj2mQnEloEgPfZBKQUhVQ\nPNnOYEsSB6s/Gbs2D6WfIUpOMiVCTtosSNOxYE1P33BhmnfZBx8cLHxwVvYQ\nc3WpuAd1TgSrb7/CE6LES+Wile79wo7Kh3ZEZI+ePwbQRHVUECbKM2s6eAbd\nAD891mIdtxDk5dwbqT248CpeHELEF/HDpftBY8uJZWBYF7+p+2eMiS3pDhLF\n6ZlPOTHXf+6g06FohPkKlXEsEVF3X9r8r/Hc9cb5TqX8eRMVySQhlviD+Y/D\n98c7RhSd4MKpRq92158Sa+kL3Sr6r/OWFhpgtqP54sWEY6YuXflD/FSUA5FI\nXLIC3Z2um8uqo5uQeJC+8vAJ1qdasaRdkbzVu0Red3QZ69kZdipERkIf0Cix\nWMmOym1lw/9vYpU/b7sCbd6QTXT9zMbGI8fSXqKWgr+1RQN63B6wJwbs+R/V\nDnjVOo7dbvKooE0Q5MEeWMWiUwVsnLay01w+eAS6SZ2v8aHvRDUtSzYByDAe\n8MIv\r\n=Sd36\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.13": {"name": "@webassemblyjs/wast-printer", "version": "1.5.13", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.5.13", "@webassemblyjs/wast-parser": "1.5.13"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.5.13"}, "dist": {"shasum": "bb34d528c14b4f579e7ec11e793ec50ad7cd7c95", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.5.13.tgz", "fileCount": 3, "integrity": "sha512-QcwogrdqcBh8Z+eUF8SG+ag5iwQSXxQJELBEHmLkk790wgQgnIMmntT2sMAMw53GiFNckArf5X0bsCA44j3lWQ==", "signatures": [{"sig": "MEUCIA3FS2/q2aSHshVdD357v71IpjA0+BpX1hr6fGsrJvtlAiEApzOJJ4nNTRiakFT3S6gQG8YhdQ9yP66P6XsXIc14tS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lvCRA9TVsSAnZWagAAMycQAJW/rHWJ1nei0oVJj634\nSVwxR61VBQteORfx1powECT58IPGHTWcc4klGo47c3IklTKgQeiJ4/vzWhmB\nLx9xAVNGHDSO0nstRk/L0lAb6wBh3eU0b31wwMdtQ+5dLDbDDXOPN+IbaQOF\nGBCjVKgOQTdKMNfxPmnY3Ut7x2Us5kLBYU3H3+/TBIhe6BssdXalGXGN2tZ1\nhYykws3znurhX0sJaQzHCIEt0Sempq6GJFR4FhmI1Gb2w13VwLQCWtU11jZJ\nVlCxj8V2E7Tewzz/kQVAX6oXiDrjJlHwuCKO4gWxQWtqDtwXLeCoKYxHmGSd\nhkwIbLkoJkloh2pbGaVBnMaC7tzgCUnj0z/SBnpAkauFq5eQTsT075MCoqZT\n2CsS/Jqei3FgrI6u+Yoe/7/+hvLjmnLiPsKDXLdPZz0pHt0DnoL2+AgOKIyg\nlCHDKYcAp2ia3hveDM6yJpww2M2lmLLT4kXW6AdwyRjCpfVklDPN66tDS9EI\nV4BGrpSqhfts6psuljAdn0hLiEAwSaoCBpzt0NMddV55ep4ItnxbhPXopqC8\nLdywuQVmjnAT2rJmyvoX1NjSlJFuwHabwmZR2wP51su9R1F2nbHT0LzPAebp\nRLXzrUWZVzXbhTaXjjIVJz9mg2Q+UaSfWlmtelk+AqDp63yoDeoormmo8RuG\nLQb2\r\n=leyi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "@webassemblyjs/wast-printer", "version": "1.6.0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.6.0", "@webassemblyjs/wast-parser": "1.6.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.6.0"}, "dist": {"shasum": "a7df8e36b58eb3fea0c9389a71b22fe03977e56e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.6.0.tgz", "fileCount": 3, "integrity": "sha512-1ey2ro7coz7Ee+qBBUXN+3r2nWOnbXRs/dmCbRuQYXNpxoF/D6y1PRiIJc/p9S8KSuRJjEN7xoqRIm/u+Y7fEw==", "signatures": [{"sig": "MEYCIQDC0IY27MOQBYyrNv7nHIX/PtRy5dUCuZ6AyUNfgPThSAIhAJaYFCP2h+ynd/ybuFG8G/mP9IypA+sDUYhauaPi9vzE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/8CRA9TVsSAnZWagAAOHwP/0OBFcDCb59hCCssmRND\nyc4W1E+Aum+JHkWB8MyPw7dACShwRp4ZlZB8iUaz8afk2BCAQlNRjVaIfZ6N\nbKXXFJ653SOFJiGOKmDo3NrfDFHdrFALAd2s4U/eMiFO1/Z+7zuZ4NA3yNkb\nu/jxLeHlS20dxZNZ1rmtULiCI5eEDq6zkle92wYRrsRLTxbtTM1Z7U3Fph8u\nR19U+qI1nANiTU4IP9wKfpctaFkdAOoJSmbr069DxuiSSaAEQYx2a+EVFM2J\nthyTkXKJp5+guXLbj+JN8eulYYIgFTZ3/WK/dDeVlf+J6fcwUrEmiOyoNY6k\nrZscth14R9HrXx3onQb24cEXXa5aFe+cmuZsD5FXAl6i0xxYmYG6AX9VVpQp\nbdjK1ewjHJV/DubhhaZckwwRpCtJ1msYNuhHZg3O45WMWTHekdnL2C1OwAFJ\n3kFqHCwbsytA0a29wrgAA7op0XOgi1y+76C3aKIa8UapK9QqHFLHIzaAYRGL\nbRn3J0e4kHiiWlrpSab3XCXMHgGSIXPIzRWB37H4M/Dozddncah014mM2hiP\nLr6YfjJCtpZjhoPj5dRLKxcFrckAdVqfL5/layhNKhu8gP6A9ZmweyPDjwYJ\nKhA/vDOy94CEAen+Ls1DjaVB56sh17t3C54i2yiGijijAIMsm2YFcfX3T3Qc\nSGbA\r\n=TVM+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-0": {"name": "@webassemblyjs/wast-printer", "version": "1.7.0-0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.7.0-0", "@webassemblyjs/wast-parser": "1.7.0-0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-0"}, "dist": {"shasum": "5684615c9d2606f3aea2e5bff1e3cb768e290cba", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.0-0.tgz", "fileCount": 4, "integrity": "sha512-X+w8vvu3AgY/DH2pBF1xTN5/+ucCCTYFuq6DUGDLhP43IzAgE7z5esrGUks2Ch/+JV+1F2VcsjPdh1TxHrwlQg==", "signatures": [{"sig": "MEUCIDI5lpP4KBqRQkdGQZToEz5J1UC8xDlMgVUGtPKG0kDNAiEAxeO8Cc3CexrEvKZ52RTR7sB/cZFEkNtHL+fCr2jknqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzmTCRA9TVsSAnZWagAAbLcQAI9+Ny0b2WkBJsfYCPgq\nHbWigSihRzPUb1T4W2/21tj5FxZfs4F4ihKAZRwyEdfS4b4pBpFPbJxKm5aM\nMoxuF882BkvkoX2G9sZ9ni6V2hBZXejnJfTWorI43BPriq5PFgRBkGBwTxCe\nJMObcxR8G4kof78JAVThycs/vU9/zqSMIKlIsKgjul292eDgtpc0diB+Olt/\n/FPm19JdKJfgV3WKvKYRydc+b7VnTdUbJOuq6nt7EfPzDAFe7afd5S/vL7kF\nwgLObMMjJogW+kGMwiGnSnl42aSZYLzvogdIHOBeJhLQ96tiUJRYs1duxgSU\nbbt1nS+9dq3k0U4vaIqEbndnKc8BpCYUrStf+XGUpi264w+q/ytDDGOh4VZd\nagSoDpVmgvvUXltrJBhbT2vYPWGT90pYYD71q/U49SP75h5XxoHCNM5y08wF\nI2lvV/dOOgw16moPFXH2L6dwVKecOoIZrK4o+GdN556i3GarfDSCABOu7A1K\nyijtDaXUmlyzXf1ZU/hvvmnWaDagX1DoHNSgF9Hwn1Dg5rKgXhJ7A1nrMEyR\nCADRynt/VtVfG70aBtiqi11ioFJjWwsQ+gYIsnmTE/IR+9k0ehfAcLK++trT\n0wkQkFk4xFBOWPSlgyx7+uAx2UqmujMF97qsTgOtKfWO86rJ8PQIxPVYjh3x\nex+w\r\n=uCPK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1-0": {"name": "@webassemblyjs/wast-printer", "version": "1.7.1-0", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.7.1-0", "@webassemblyjs/wast-parser": "1.7.1-0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.1-0"}, "dist": {"shasum": "7e8c22107a198fe27e6d970573b5b9012f3a00ec", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.1-0.tgz", "fileCount": 4, "integrity": "sha512-brGkfDXxx3fAnfYP98V0YD8gTMzHRPs7OIPN33ylyKrx26vLU0O24muZuTI8Yamdog98lm8iUvChPCxFziFFVA==", "signatures": [{"sig": "MEUCICsYyfEjzOCyP+6F5dJcoj7JgrID2yJn5RFd0+c6Co2aAiEAsCo8QmE/w2YdAN4npIhO/KXQdeJ9HmrZczXEABPCDj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzw7CRA9TVsSAnZWagAALKAP/3F2qCG1LNbPf6XwdPHa\nwEI2gyIfOV0IGCx5RwvZ1iEsALO6wkYatBmN+eKY1+CgmKx3NfnwdU0jq//1\n5iFprZ6hJ8Pex5jT/OqugpPL53jsgyd/od4AuybP8uNeO03Hh6SX16LVVYLN\nA7El+vNFtHiWOMjAQrCRaBYQIdAfBZSh40ezyZh5tJ3d+oIZy0PeBKPIWzOy\nA81TFU7EQZTGYkgYbyEXpAZpWeeTKpvzLIoT1Y9uH9XTRKSsivzJpWBnJMT8\nTvWQCFY6kuUn8NkDRT8qWSWdySWh9YCLt1jPtFvsjBv5/MhFRfQO9TeRsAkz\n3Z3dk4WP4mmAvVQunGkZkxbCaX00gudDzI8HM1XQ+CbqMn/TdnITz7qvLJuc\nSgiDnvAK6jBRd4DBd96pFXj+J5hQSGDYUXqAK5civP3n9XNpbJTGCAg0Gzdo\naxyBnlw0WPhG3j/aVOLoQCkkQDVlwnSLdIkfnP7sYtYn5PNPgEqd8hZlw1JG\nNW3BOODHQw5DSKzymU+B98MpZb4L9QQZVDo9v1tEZGYErTtpP4s9RWwpEYn3\nHpyouS9BzKlu9q/q1/PcCQzphc7CoJFs44hX9CffLQHjymoFm4tkOeVSdCr4\nfHEvJ0aS7iW1cBpp8x7pk3506cLDiv6wQvvTS8MV+N7vJ6itwgWdnnt1mWNH\nFf9C\r\n=c69i\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "@webassemblyjs/wast-printer", "version": "1.6.1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.6.1", "@webassemblyjs/wast-parser": "1.6.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.6.1"}, "dist": {"shasum": "23f297d8df4a0c4cfef6d1d472352b21ce5e2786", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.6.1.tgz", "fileCount": 3, "integrity": "sha512-oGZE0YkZJAl6WpibS88cm45sjyUid3xXo/jB/Ri/OMPss8gQi+/AC0r6j0z6pseULTuaTe+tmX92ITvnDguqrQ==", "signatures": [{"sig": "MEUCIQCEDuvmYIhp7K3quFZcxXuvZFEbUd8TfbGugksvgNpbdgIgMhWcrFzaVtJTjYZjsGtbrouaHT3+xFLTm81ZP1AMhq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0XlCRA9TVsSAnZWagAA79sP/jUihRgSz3wwu9AxjcLp\n8k/3xpFFhXixVj9mgdvihHBFCqjQmJZTamZvr9moONsvgc0GtVbiESoAOwY4\nmLb8z5XgymQhrB5YI+LSDnhRK5Icv2VZRkMi/GdEO9rdtQHpYSeRoGvDOxHF\nGxZN3QrFvoMbZZPLwwRs8yOn7jMns1CPtfu3JxmefrLUMW55F7qYvKqH99Lm\nwkw7ipnjOvU7FlgLyYkO1xSh5LE/qW7rj2mr0aSJlmkdDsxrbr/kYQH6cf3Q\nsCfohVBG7CLf1wDoNjnTHoomyj4oIzLA9jykf+DzTXF25pH0AlKOF1JhYWF+\no05mdHhIPpA7Ima9FDfRDH7793QYd+dFvNzuVXdf2eXXjhqy82J324QDZetC\nDtFTfiQJudQCkHLA70XkiHL1lcDRh5peJClDjfdoDr8ZGbaHzk7xEQlZU2sA\n3AUPkAGxWCANwfcJpw26NMzk4FMh52NpDFuqEpNh5fGUAnkCMfl8pfRdZO99\n20XEwtKN+EFHueCmgIJ5Amg+btGA4gixPnmH8+ome34miOrPFnjHwtGqNHwM\nQPjA/htPvCq2JbpCi+Aa2NETNeAsVLn2RnHzPoejkhPEWvPDxaILT/HCi1Qo\nnKdo72BlkCIjsbMlAP8Pv0cbnOpgOqwyZS7Ky3Z94y+UcSV/jpDqsJQ+17Cv\nTKPj\r\n=4w+/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-1": {"name": "@webassemblyjs/wast-printer", "version": "1.7.0-1", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.7.0-1", "@webassemblyjs/wast-parser": "1.7.0-1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-1"}, "dist": {"shasum": "bb38d39bce1d9569be32ecefd48711f373b0896b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.0-1.tgz", "fileCount": 4, "integrity": "sha512-2AdxCEgaLY1YD+bsLuUvFWaf89P6UpQJdd8VtZncCAyOzWhraKTkhHZwhWCdIVjjx6BQctjdCR1Cy0dWFlHnuw==", "signatures": [{"sig": "MEUCIDnlr0ayhrHCA8zcdwD3HigqrIV4DSaUPX0LqYZJ+/uVAiEAtBZOpCHr9BoOUF66WM3B4rslg40EyT83jI1UGcz483s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1ZQCRA9TVsSAnZWagAA5SAQAJyRBLGuc5cSHsWrCVZH\n1S3bV+EayEHmeui2XmOsaZPu3UZAvU7QYniuzcR39UyojpUoFa/JAhzZomnU\n06NB5NX6pll0ewGYXREXWWVtTeZn7d2yyVYVm0mjIvL66hbggXHHkEYhOxrt\nNKJaPt9UFJWx7JZz/3lxzEV194ALKw/Xuy2unfamF3/jycTXqAwu8P2Y5Wz2\nlltEozR3de31bC0IHManuhg8zRhoG4Lvz0xsmpWQqB9+IFrvfzkMZpZ1ADH6\nhd7HGucEJTGfRfdNQMcgZI5GkwEZcUzj5f2hdeu3F/iByvFsAmktDdzoOLQz\nlgEWlmuj5NOcUWQdikaRIH9/Hr8WSwIfrXueUSdoS3fCT4DrqIYXDO63gDeG\n7lQizK8AX4zTan6C+7wRtHZax8Np3BPcnvBmSMc1rSsm6qAb7IABxnXvSIg1\nqxN72Ch35kI1fpBbRlD/zNG0gBUslQrMEKn98ihRV+lUMneWKaxCW8XAGm5a\n1qBbrJbr1qsEP1Bzzy4+81SdA3QeT6jvNHTr9pRqR0B6u5HURCHLf1IRg0U/\nUewttR67mkAUpHN1vVhM6EpZAlCXiNVRS5meiMnwG1arP9w0vYKZB/S0EEkK\nWfx1HSI4mthAsLei/ey73VBWZ82O7Nr7JwuBJdRQk60cNx60EpJhkXruN5xg\n4Wv2\r\n=8OPd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-2": {"name": "@webassemblyjs/wast-printer", "version": "1.7.0-2", "dependencies": {"long": "^3.2.0", "@webassemblyjs/ast": "1.7.0-2", "@webassemblyjs/wast-parser": "1.7.0-2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-2"}, "dist": {"shasum": "b7c524b53a84f40116800bf80823d99ef2fd0703", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.0-2.tgz", "fileCount": 4, "integrity": "sha512-IP9NzWmXGJavIjIyQbphR0PG0UQIXZ4NFAvalj5EWW/Ale3xCPW3JCGlHZJMe93bc/mLDGeHmnMuphoeFrnDfA==", "signatures": [{"sig": "MEQCIEhal642UYxsOordanG7L5aPMOzi/NrahxaQJ86WUnxbAiATt1HbJHPGVRkPuRKbY/CNpBMVQeGgqal2d/Ej39xoqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1ndCRA9TVsSAnZWagAAcb0P/31j4gVTHuaXdGyi8WZj\ns31EQqCD3gadUYj2/aqGhwtgmp/DEXGziBho4US9tdS7TOz5zpzcti1njROp\nKk55Qdm+YAoLiTQRGZltRa9AeDgdrGdwVF9qseK95Dmqf3vELIWQjL0O0Yyf\nqqjF8Z4G9b6lpHI/7B14VoZqUzL1BE1EM2RFbe7gVsG4YUihMq7Ik7cNQQbI\nw+0mEKhb4xVdxTtXQjEVqsafgnJf2Ov4EVfgMipkWIwGEuGxBMwD+KQrfMzJ\nNGTVjsog9GyriXTeAlJvWCpHFPziwO6s425DeEtzDSOyB3QlI3hVpxkachNB\n0DSI1a1DK6cNPOfuMUB5jjAKZKoCJ0cZPkPut5X/Z6V4gFb3EBzgprMXAY5K\n3U3GWL2d4NxjCzfI2Lbs/xmR5/iQ5Jaye2671EJ0DYXuoXEKXz7GpDRE8rIu\nJmMpGi4fGgs1ut56H3jqZB3o7MLv9l8Gzu2luMKYtpRlw+FiacloYVfoo7Kg\n0sadgucFLi4/BZHKwcZIOR54op1fc9dXgPSj3DsuvMilQWWUuwknfDZXDwMA\nozLWWvU27SrslG6mw4Odm+e0+0H/SYsDCVNibO3wuVW2mnhveO1e4Dyq2XC0\n8IdXtnX30V350fJa5mTGRZeZqO2LnQbSiuCV7dAIFUNMoqSj1qOYwhBf2Mrw\npaBg\r\n=TnvU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-3": {"name": "@webassemblyjs/wast-printer", "version": "1.7.0-3", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.0-3", "@webassemblyjs/wast-parser": "1.7.0-3"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0-3"}, "dist": {"shasum": "e6011a552bd6e4ea4938862034d2d08b5594ab6b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.0-3.tgz", "fileCount": 4, "integrity": "sha512-csy87G+L+lGW1kxId734fm6HQeQ6PSrPHVuGJX37bPVjzGPJe1IhWu+mlqY0/tbl9LQ9gGKsLhq6bA5zu9R6Lg==", "signatures": [{"sig": "MEUCIBIV3gzQIIPY5y91Kpy/fggAgk8BDoMNCS3UKLSBhcW7AiEA/uk+XHgRWFEjUtIDxVKjPZrkA+Y858f5fPb10TIeFIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5GHCRA9TVsSAnZWagAAkB4P/1f0lpse0tiDdT5usC4i\nZWz4HIAhhQBizEp4T0Y6Vb7B37Sb6/K1OWRJ3uzcl7lvZxRE/cXtvXhA1TG1\na055Fh/x43ioyD/DSCI1EQteQD58bDbaIof0e5BVvLC6/t1FjsnjxdMTKOXM\ncXw3f1IDh1R/Kpm0YDokdx93wiD1Qxb11RuypnZB1Xnqj7PWQfs+rRjfLtR1\n1gNKvXytddtakd7P2MEPrLkTLgg67K5Q948kyGVFtbD7jWkpC2AEwMEEIDxs\nqoIpKfAWRiQxo893CnBBjUuApik7hD0Hq2Bt+arCSrDhQ1DzEl7MSYfSn6Hi\nvcMB76C0Wn1VtoyIwY46Yg+1RTuB6QjxmFUXn9PeHqUwwaWfMTSodSm97QDu\nUp5HFHeO6nf3W9Y+njomVVuEgbwdjKqNaXvuJiXjVCj3Px4fubn8zXlop7VI\nYyuEMkwAQKKP+E9Lu3IF81u4+sfKMccMI1GWOg2tNe2lc9I5MtkNuxV2B46W\nmwb1W/BGejb3toJgPwUO+AaxR2T0TP3dGKG/DJ3bMgI0CdsTCagPoPHPoo4P\nuNPLOuBER5UQaU4UK+RETq1nfn/3puwIIS/kP5KE/EXQjEJFSQkSrVWCA65N\nBORmYFv1M5dkqHonEAywTQrNOPx84QtbRn/I6DN1p6Nf+DpXoy6KNlQr4+52\nVlCX\r\n=37VL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0": {"name": "@webassemblyjs/wast-printer", "version": "1.7.0", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.0", "@webassemblyjs/wast-parser": "1.7.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.0"}, "dist": {"shasum": "517546d6d797df16ad8ab28b6228865f9cb0da18", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.0.tgz", "fileCount": 4, "integrity": "sha512-/JfU/xHHXP4WEkmnaZNO6Lio5f0466ulCMqyHRJEV99VFjw0jqxCBwTPHdKv3K+czpZMx+HDKiTXCF/ZczrQnw==", "signatures": [{"sig": "MEUCIQCRvp0a0l8QVvBrzlqGp5FVOolSF/N3p3oWUZ9zB1FoHwIgTi2nXNrIBh+1lX0oD7Q0SBzHbkkkxtwn+lNdM5OJTts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULX2CRA9TVsSAnZWagAAkgQP/0DHdyaUwxunT8wUOMXr\nFy1T/jNmUDoIzbwZ5TH3i5D74XBIWNHscrho8gchCMZ/+CATOUfr4ZSv5jHy\nKMo4u+EhU7eThazym4VebCUOJuDVgxB7K5dFj1/EyPCBHHXSodQxXVpF49HT\nFanUInZNRSeL1Uk/86aVxbOGEk+vU1CVrgoogRKi4bXmSKkKI9kyEzI2A5qv\n3SyF2ZvtXu2TaKrnc/Wy6aD+v5/svkt41BKv4Rh+UpTqmfN9Gu3FwUtMxqZS\ndb/7j2CvZkaPReXiV10D3ipLNtAj6o98caxhHrGnCogzfK3FT7xPD3m8GS2F\nVr18gDWSAKJlVO6NhlrS63kGAfc1aUxlSQ5yjvIX0u5orRkq9+8h7LDK7znO\nfb73uY1uky4APvzhqMTTeS6SJ2UE7evi4cxnbUU/qVNxCuM82vD4eYlXeq6w\ngyYQmzt97k4Bdoq1EJ2kVXFmWhSi2dsx6Cagxj4sulNEWA4j1PwU3xx0VqSg\nfSE7sVdm3NYCpRjGZIHfwgYnUAT08nTr/dxVmYn9iB9VRmc4eN1PMAk6v/kS\nGWaDwAX7XgF764C7jig6ayeZauDosa17p+1dO0wgtMKjOVuGJNPU8vcx3AkB\nAYgV4o7wFfv2xBg2BxNk4R35k5XVDqvhG0ihg9qAhOQy09NHLkIkabIg1Tfy\nYPQg\r\n=VyaL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1": {"name": "@webassemblyjs/wast-printer", "version": "1.7.1", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.1", "@webassemblyjs/wast-parser": "1.7.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.1"}, "dist": {"shasum": "e26dacc11b0c0839907cae8b3320958c64c4f732", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.1.tgz", "fileCount": 4, "integrity": "sha512-DpFZxXJRXw6I+bp9QhkG/9N+XBp9C8c1KAYUo4JxnC5iQP9eWJ7xuPRc8wgc0bv2YwaFq9b5AeL9vhhFTvjXaw==", "signatures": [{"sig": "MEUCIE24+5lotbKn6pFzjJqynmCXpeCD8DCV/AfswxKkWzUBAiEA2wO81ezHD5gDLUXzmmkXgOseX2kKfxTXEUIbhp194Xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUL0mCRA9TVsSAnZWagAAHCYP/368iJSJpkMEaCzkdE5T\nYrb4GWcWhopHtKGJDc/VrpI7hICYjdm6gYQpp8QcKW6kaDTYRzlwUc2xUOkn\nSEBBNlYP1SBOzoPArixWZlWrBMKqfZV3F+ce8vpJDT5djlHmht9nla6M4QFd\n+wXat91/vGqa0Qswhsg1I/JQ8MwTtadBTRblJm0YoC77yZHxWIVf2xmRmrMH\ns6SgAW6tASVYh2nr/hYmuJP2ehG+xkevSvYghTOTz5GfeQtDyGi+CpaKV6u8\nZ1ANuP+9ZxaLDuC5BLaAAd9Yws/BuMw6DrW0IFy+u3/diFWHMoSQlRaPUFaD\nXw8Yy2vAgI4TsJoIsw1cmoF4v5h5RUXHw8hqHbN5Fp96P043pUPI/t8RnEQY\nQ2EgffOVH0BitPgbsutbCI3U8Cm2VBiIa0G7Jqs++buLmpxNO0jLbcTtZR/F\nUvKPJpwbjn3QXNoWtJEL89WWuDIikG5AdUZNuck8ZZhlapxJiZQJhHvAf6DC\nJpYOhB4tydDMWoPzh37JtjkM+Rfl9qS5iZ7w33CAvayzpFKq8zJT48OM8lkL\nZwd/eEHJicHnV+KnBzqdi09fvYkPm9CvYX/2Lf9NHQiDDzEsOhS/W0b/z1/W\ntLI4d7O8wqs2RwHqAg31Vr9XNR5SIcwI4hoWR8lsYK0AybxtuUP9iczp/efW\n0GRI\r\n=jTIn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-0": {"name": "@webassemblyjs/wast-printer", "version": "1.7.2-0", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.2-0", "@webassemblyjs/wast-parser": "1.7.2-0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.2-0"}, "dist": {"shasum": "5e3986352286b3d418f77e26d0c528aa77b0f5f3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.2-0.tgz", "fileCount": 4, "integrity": "sha512-72coXUTIK/wdM2Z9kjLjP67UhTz8PEQdVGNS5WSli4mngYildlVclNwxzWEbZyc1QTG8ST5gqRNEIjiaiHQiKg==", "signatures": [{"sig": "MEQCIAVY737hQA+N7hSvBFc6NbLQmZczyEoqOViDdQwX3jarAiBd9UxMug0YRF5fSk5iq5nSc/UQqQ1NaIFio4vzRMIl4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNQnCRA9TVsSAnZWagAAzqgP/A7+44IT4lRtsBuTIEZr\nah1O5lYKaNL0C1iHXYGmB/wq+A5mw7Hy+cOX9BdPn0hqMUaEv5gb+G4WfKXI\nXt1EQdFS1wOv6npmRabSssT2mQkb8WQRzXUVzjwp8xfx/z6sB8vzpyRYsLYt\noSEAhIl3ELObP59+du8PJcryNStybPYp4T98upXD94q0oDRZKX+ss8U/85ZC\nPUr/KDlWoHQ/jJv2KL+WCHcdI+X3aDTiWI95eJhyT5bEeCTft9WJ+atf+cX8\nPYkPUaFek+7khSns5CyYMFUNzeRATrAImDtO2qwE0o53fDxQX5OGTqMF7Et7\nTtFiDE2mA/9UJDLZ3k6Z94hEP+JbZ0QaBTAxYSphzLPsolx+4E48NPHwBijS\ncscoQx/qKrSCFGugt/pSpBX3qT5O+CVs0gotHB/3Z8jkroLUCXZQkOgMZPVd\n8pJWMQ839cmyQRiGp9jw7VCUStt9miqrQclx2KEVoRnyl2afLf98JOzVvOEJ\npWVN7srAQKMNHLU+0+3v/r6/6OGXDxLakI98+bXHW29+CRkDLmKgeIHYI7Zy\nnxRexrHV+dGdTNa+ywZX2EGl/iXaVtcfdiaX+LZXUt8h2sO5WVi2nCAwcOvp\nQUIkaqkCPZlM8sSOH1yJmBhM7hMWXf2b6kkM3GzrH1qog8A492VSIPJltag4\nONVR\r\n=Qv4L\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-1": {"name": "@webassemblyjs/wast-printer", "version": "1.7.2-1", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.2-1", "@webassemblyjs/wast-parser": "1.7.2-1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.2-1"}, "dist": {"shasum": "7bc912ade8b4784fa68137707bb884b098c4d6f9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.2-1.tgz", "fileCount": 4, "integrity": "sha512-7e3y6busfrlW2OnMGIUQns1Ge51Yy4AVHC9bQA5Z7m4nT6kwFnUCCqRggh/q6v5/dyW9YdQyos45ukZm1PTp/w==", "signatures": [{"sig": "MEQCIA5jeMwUQmY3/N4t0BsjaGTe5zwhj/RCzAbDDQtfDqKDAiAngsVCBfTX9LDP3hj+U5bNXLEepauOCX3jkb04XA5wTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNelCRA9TVsSAnZWagAApKEP/0bxu0Hw6l4aLH0MrWHX\n9pk4VHgbeHIlVmnXcBA5YjdB0QX1eySs1ryJDsDhS38fkxt250NmKfyAZTZi\nRI/YiILNUNo3TNoS9PE+eF3xbLISvVdkZgtPgcNC+LN7OGbqMiolAPcEJ2ud\neXbZATVyLX4R0ZUhwkyVV73hoX6rzV1HhmlGnex8xp6KMpdfIUGukqYWtQxO\nVmNDwQYQolOJDjXGHuO+G4Chf1tDhrmEW9Vsrz9qcAyu5O4KdS9cRqlsq4m7\nVZPSgfKsCIplOcNTlzFnBd9mZogHQdRRTbvpaIwoYakvd6KWOfbFvYVZ7vXi\n4umqjnoH/OSEnUpMR9hW8oFDQntWA8vd6a13JlCEFYyShIBPrN6d4H2tTBwx\nIWL9fX3Koel1XtpHzV6NtSlRTr5tyLKdknkVN8y+5uuLLbgDapG+vCTsGAtF\nRqpWyxUG/IvV3Na7rAtmTf1lpxxiO1728YE2DP+wy/AGB4OdDBWzU+0u34Nx\nX7fctMDhSibfuXLHPg+SveQIN90vYahK03Z3NBxUPm8B5NAEdgToD3N/RKDL\nX4BOiCPo4wyLlG9ebiZUiaPC3JlMYAzCLXIOZZZMg3DTjKKJpptLSxzDNAJa\nGj2XpgAz4kFK7SSh0QRComZlvNAugrwoM8l/h8JUZg9vms4nREPpCb7WcGE7\nB+WD\r\n=WXF+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2": {"name": "@webassemblyjs/wast-printer", "version": "1.7.2", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.2", "@webassemblyjs/wast-parser": "1.7.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.2"}, "dist": {"shasum": "acbf251f6b17c6735e4824e086cd19ca54aa28ec", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.2.tgz", "fileCount": 4, "integrity": "sha512-LKw9sv6sqR4Q5CUr3/gL5NDn9EAgm4KU0s7WK9x8Lyj/34jhh6kkYFvFOjBLJM0C27lCHm8F2T9OV65/7W43vA==", "signatures": [{"sig": "MEQCIDubBYvShezDanDzA+dU9jN3cETk6rksfZbQoinX3GvaAiAwhKxNRhXRBQEUqERxoAtqs6Em+jjL9pi8IJ2143yNPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmyCRA9TVsSAnZWagAA7OwQAI22w1S2G5AGSpuBBPgd\n3KOp+JBHkJgIjO/9hawkGSTVy41UhbKkTAivZ2wwLl7+6pnecXL5vYx9SV8O\nUUWDdH6nzQZy4d1tFAPEUx+reS926J9X/IbsFzyG1Q/pfTa4uVYxwy9a/pXR\nl3ce1mmPFolp2Qgs9dgOvFzV08EjhnDBIXbSVix/nPXQaUoQ39/vl0tm7Em9\nttqs81UKYi+vH5EC6OmuP8qQsrDtNjvEqAUyDs+whKaJVkOrhuMS0xQkpuey\nHBOeFS6JlWyZ2yFyXq4aooz2Bvc79ymXbt540KNQIAgp4eF31EN0Jx/Bcx9y\nbOU/9XcgAoDbW6vE7LvBJq9o4tM3KCGsxalGeW2MuQiJDUsRuUVi7fYh1tBl\n6XOVdqDCN+nlvv1ZhDieejL9FHpj7sn3iK23z8Ea1kwKhZXhljlOHp+vkWkf\n9BxtL8zjHzrkxfmyA59rNhpd/v4rSeiCxyajMe/xMeVt2iGMgNXagwGjf3cr\niYNhU5vGQgjmb6EHef3CJIsat8NfslzhUICFaxNFfRTwvfLZBzsmRYh2ZKsL\nUUbSy81PwJPB95kn42mhKpIqHO45NLjexMYgE77LiBXh1tAwGrlDgu3rwhu7\nCvCNSAMFTZcI3tFfZtbyTMRAPD9uzW5bfiP8KACgUL0PMn4MvCCBGjvE1z4p\nW90Q\r\n=SxMt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.3": {"name": "@webassemblyjs/wast-printer", "version": "1.7.3", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.3", "@webassemblyjs/wast-parser": "1.7.3"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.3"}, "dist": {"shasum": "3851aa98cf4d5e51b93cdf014ea494ac0f98c81b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.3.tgz", "fileCount": 4, "integrity": "sha512-2RokQXjYDwKLNj+0xZLU2gDRKKRWCOr0ltFgmsErQUpKXHwV0jetD0lwc2QEH3qCdQp0alPLtgNrRD3x2QjLZw==", "signatures": [{"sig": "MEUCIFGrfdwmJ1r0W2OTR5aVfT475km18gLx4tL3H2fatF4MAiEA1GY1hv73uKkdjZrw5mIHjczEegNShbn3QvloXrLJ25Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX20CRA9TVsSAnZWagAAxR8P+gLfsU00RT3A+2ha2NcR\n0z72D0dViU28Bg4lp2oUOFrR//zx3ogXpbv/vOqdInjpIuD8ywzmO29Had/+\noqJKBlwuc3a/lynfrhZfQmdYFplQF4KIUstM61qAFyKH5xXM19+J6sTTkQEO\nShfv9Jb0UEaXp6e/QuGgXjjJ1qC2hSGB5z1frJwXbdzeoSbHj743z6Lc/kuO\ntHSDJVFMOY6ZQ48lp2j6t5EPFIBLSx1u9+29Gr6w8gAIqPNlRrIsT0Faovjy\nX8e979PTeCeYmagUL0hIQFdnGDKRwGRRPb/YcAlsdTrf9o0d4PHNjvY2gRyV\nOYWSU3/gbXbD1o7wG3kIOuTD+Ly3D+Cjy2wRUw83ZqtSvz1qDM1OTxXOrHtr\nXRdbSoG30EoBsfpFYQVJhwDx8RqCP9WfezR8Fe/YmeWiSoxejISnAXUQMESg\n4WS9ifp/2ilehgzrysMnuX0MbskMYmI9vIKEWW5cqWyOHhpe3ZZd5c2BTwl6\nqRvOdxM6rNpW/nTZZHirG45exWaZe8NjblFfdnAEoMqMfDDxYgh3uW02RJuz\nsxop6QO2DUrjNHnGi8J2XxEvHCp0qS43iocp5KqlMx1ZVYXpvAsnfPVY6rtg\nNIowNbVkLzr01v+UJL4ThdgoAQ7t1bnjUJ+EnHBAYU6uhQ4P9Eb55iT4mUJK\nsXeU\r\n=9KFd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.4": {"name": "@webassemblyjs/wast-printer", "version": "1.7.4", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.4", "@webassemblyjs/wast-parser": "1.7.4"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.4"}, "dist": {"shasum": "84e8bdcf21c37b4a8f5476878ade3967d6adac99", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.4.tgz", "fileCount": 4, "integrity": "sha512-F+Fcpa8OUnObMFY8+MAEpPP7I6//D4uAUVAdPqq0+HQR+19M/44TBhROq7c+v3OQ2NqbWEb3znmb9DJu3hcjnA==", "signatures": [{"sig": "MEUCIQDvjDoBf9aqQd6QVE43RKto9ZZGr9oebwcLrPKziWGCCwIgW5WrW9ma1VAwkGJTntaRpUa5Jo5/3tQ2Vbwr4xy961c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs8nCRA9TVsSAnZWagAAj4cP/jOXd3iU3vHzoifYtuTA\nOrdVIMO0cwXm0cb7XdzHDcw7LbZSx5NBU71gMiFtBcohhvfuVwqLkbGMjwMZ\n+P+6msjvGN+bVbHN4E6Rad8g3+k9gCWhm2O7vimfeFszGzM7pnDJixYCV4Lb\nr4iu2THMF9ZNwTeHPLBO1pBZnInztQTZpXWYO4xu9FN4XH8jFoW6REjzd1wU\nZRktBjzkthzaQ5qfoZrVNYWlQKo5UGEY4xRpDn1QA1/sT957RJLjn8rpHF7R\ny8rkXn1F4PtmAktJDJ9sShKUweNjOBA0jahIyeVyDGu92Qvgutvw+duIcf1i\n7aVEX7BinMXsrwikclgXjj97L5dZl5deSspAu1uJSvQDhmprRZZ0ElEor5mv\nGdZ+1aFodDOAQj9szfqWb8vcnNmDvDtNaDt+oCUJgXvtEohR6UfeoFtP98j/\nXPOKO9aRFDNfW5EBT3Kycm70RwL/ZnjftcHOgQPH0Gm/1BjxDQIfdPEOcDlT\n5Hc49QrBLlduAAw7cJVva9rYdWNR0BkEJDxkHxhXiGMpIP/vSijPlj/ZnoW3\nofTJpBv2GWald1mHLO3XX74Tw2DW2dDM6ZrXlfJWGMo+rLBcHOi6MJb8jGbT\nTgui3FxTXbjds8FM/cE1b2+KxR65DeJrPxAxtP8NNtLzFvPuaQr/PskBY2fS\n+5jE\r\n=7DDu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.5": {"name": "@webassemblyjs/wast-printer", "version": "1.7.5", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.5", "@webassemblyjs/wast-parser": "1.7.5"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.5"}, "dist": {"shasum": "3946b531ac98475b7f5efc6564941ad8cf562d72", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.5.tgz", "fileCount": 4, "integrity": "sha512-V5oz/QGFnjDOYXbmY0SAkXqKTMgiHYPmmVQtyEpdajkL+xrRNdasOCBCZR/Bl3KXX+R++VE3jVWgzA6zVcfNaQ==", "signatures": [{"sig": "MEUCIQDLNIjBiJ25q0RMsrP879Pt9ZpPfVmmqIr8titvE+meRQIgVc0KPLIOcah+hO50hBMvRS8t0QrKLAo/zjG9mOx4P2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZBECRA9TVsSAnZWagAAXGkP/0Hj4TlRDJvCmlQY2BrC\nuLKdMHZwFdMTbs5dbEYUBlCVbpyxLynFmxrry+pMQSQSOXkQuNV3J9oMYW9I\nHBzlOFhwgtz2310RwEsbwu4bd9fW/sofNgQVIllCtmR684WaYNTSdmcuju0x\nlFjMhZX1ge+ucdKUaz6w2ev2fPEeqPr8K06StYfkiwPjGhqHZKDC0l+GZTBP\n3RJAQ2Eto6C1IyNjEm+uSrN524NZEttd/0qoAz8lDutY+F0s9pLpccxwx5fA\nYhuyZAdspf6Hj/0E4d6QmZ54O0w1FKr+ZLm4OtR1BRx5hbsjwjAhHpG3YI9K\nwcaYKnEogBC4hbMToRP1Uq4cNAIeRhBj5E94upBzJWFs5XG9dczV1R1eyFyB\nEd6nK20/W9BgN4OrOX7XGkSUzSNkD24F0ZRaIwaHNyviI3Axr1wnDR6BOIyH\nj1O59AvNVP1zj3Yn1qwFEwgKyjzoLA9kP88GQk2NvkGt1qIQrMaoRSFwbhBF\ncTOFD39r35zb62PQX1xIIGrkZFom0vB2a3ZyNxqycFapp6BdGF/9iOuDc0Ml\nvX030WKr3ObKM070o/JRy2p4vC3MC8JWfvl1F1DgCulEYmUlSsLkTYYrGZ6D\nGAN48TR9mYGF3CDSJTgbVhjWSFIswM4k5HVtMn4ock1X9dKRk8/XO/kPZVgX\npL8B\r\n=lcfa\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.6": {"name": "@webassemblyjs/wast-printer", "version": "1.7.6", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.6", "@webassemblyjs/wast-parser": "1.7.6"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.6"}, "dist": {"shasum": "a6002c526ac5fa230fe2c6d2f1bdbf4aead43a5e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.6.tgz", "fileCount": 4, "integrity": "sha512-vHdHSK1tOetvDcl1IV1OdDeGNe/NDDQ+KzuZHMtqTVP1xO/tZ/IKNpj5BaGk1OYFdsDWQqb31PIwdEyPntOWRQ==", "signatures": [{"sig": "MEUCIF4O0vnLr+eMDhBr+K6y/XzAP4KyQhHRU+QpNE52T5RKAiEA6duQuKk5XEJL0qEM0/vt9Y4h0D4WEbK5O/ErcsJW61I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbln1FCRA9TVsSAnZWagAAkKYP/i8qgQJizxRad9sat0pA\n99vwfzqF36FX7rBOxShUED8mSOxeoCPM/tkNcEw3h/pugz420JVlYE3mKGDE\nbWVzhYvpNPzGBmu4UuKmKdLMlAO7+OZF4DvFR0CEz3Tgo1Er3pDM54IlF8iq\nVpE8ansqiwmafdMx/JHIB17wdaJavu8utUUWjeiV6JorxHUG7aDNys3DBYGj\nXQ1oa4UJUz+nQA2Tl0xrg5Tlu+yo/fxE7ZCXrO54n9ZUoFP6gTokFPgvsYE7\njRcfsw0CmDLwfCh2kDnjo1Rb/IIiisOovL9T37VqVq2HOodUWRbBDuMRw02M\n8PKr1iPBUMC/7x+vBFiaNG3sypAvWPfQgF2GeknXLf7tORFSMlhrx0foMY8t\nty085dt3tbP4QHs69xA7nT6IHK8Ba34aoPcMJwVjq2Sq214ImHTZmH9d5SD3\n3Qc6+ezyxBTttFDth+Ht93p/4Cjy/3ZRCGmM8x63kJvGWWu4Vtplz2SKAMpM\nyl/WaGXqmVXdNd/+feRfK3zvL+wU+QDxoQdCyCRjtZsbiO7Ld2z61T2PcUeT\nYaIiPLjU8QZIkEdtnR1sbMeCCtDqB63HMsamKdL8dDrknki0xNigyBfIg5V/\nA7f86s06JhWzltQQqu/uJcr1AdN998LBX+nGkyYyshii/cb+jLX0s4S2VUcU\nCsIf\r\n=Gz5J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.7": {"name": "@webassemblyjs/wast-printer", "version": "1.7.7", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.7", "@webassemblyjs/wast-parser": "1.7.7"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.7"}, "dist": {"shasum": "2b7df79d0a925b9dcb11447fdebacb4e3a91238c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.7.tgz", "fileCount": 4, "integrity": "sha512-M+AaJH7pdu++JccG1uF4Z5aEFyvc18IHW1SxX9nojkHhx9gpU9veg7ngOCGe413m3t6XjLWuNuaDeqge6hmzkw==", "signatures": [{"sig": "MEUCIQCFtLqkoUPl7eMz9ldTDk2NGtFHkn3NRlBx7Q2skalj7wIgaJbU8z1OtE+0EIVGDO8tftDRe2qYm5PTtVi706KJ0ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeEtCRA9TVsSAnZWagAA/jEQAI0lnzizdbJft+ks0k+5\niEsDFVoPTYkxC6+q+cOy4H1iXFyNC8jbZKB7DdacGQkLeqjwavmpHiSJxmgw\nKzW4n5o58QPw+ARbUzZFpWnal4T34ogMmQ5wN+a4Z3cLiXprOJmtFpIYMGSs\nnsDSooI9SHQJGzwoLIEOgdoBz+TaYfagiYenO9WBbGXz1J/x7ruQTiTh0cgX\nf+dGVi72+gTAmckCdFZvi4YgGcp0P/DbAyUK3qZQOrD5SdNvhwdHSvB0SFfC\nRglnpM9GTWhWJP1+68IJ9EjAJ5qPe7wE5dDUzz/37/D2oHmFIJMef8zgNZao\nN2l6VAUqeR1DEor8ZYVSP6fHXLdMoPu8EpNYvi/HG2f/G2C9rg29pEGaTtXR\nqicdjvYd03CLR+CY6RlJA+sD8tB3xz3+zhDbQqf0VOvgeIqlp/ZeujEx25pC\nAUh8q1kTPkJx5Uctbt7Thad3W9r+vzUR9nGwa/rIWdwBGHSfOAtLzWb6r165\njzqbSF/Jl74mEjTODVXSCQz5fEDtMMNWNF2UjV0ShE7MKacOKHp9ziP2hPkS\nKGziF5p6LjH7lIYbesuTzkV0CQzhrn8cN7G2APV7SqiD/852oZb8cGYrglMd\nb/ZprHU0mVNGIzHWIVt9REyv3aaNp97LgDmhVZs6aGc/xdhzGshIUnGfZ+sq\nzydE\r\n=KpTE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.8": {"name": "@webassemblyjs/wast-printer", "version": "1.7.8", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.8", "@webassemblyjs/wast-parser": "1.7.8"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.8"}, "dist": {"shasum": "e7e965782c1912f6a965f14a53ff43d8ad0403a5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.8.tgz", "fileCount": 5, "integrity": "sha512-GllIthRtwTxRDAURRNXscu7Napzmdf1jt1gpiZiK/QN4fH0lSGs3OTmvdfsMNP7tqI4B3ZtfaaWRlNIQug6Xyg==", "signatures": [{"sig": "MEUCIQCEw3yodBFl9+mOpzVbPjRWp4LHKrEhjWnS89tGAn/LwAIgPYrjgo6Cug1l7RaRJL2aAnnNx8Aj7ppeQhfj728dgoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/gCRA9TVsSAnZWagAANxEQAJXkEWZP1YSsbgyvlOEL\nrRNZ4gszNkH1+HVXMw2keO/m07YdOhR5l+I8hM+sECmm12e9H6gybusg6x9W\nXXJP408l+w435Biq6wA3rPIxZ5rTe2JhPtucqrlkMA58sf+As0HKm8Gx4pfy\n/nLT52eqzwxjxSejmSgjB7nxDv67Rnw4aIYQWKqGg4O1Z7rGFBAVRGgCtvCX\nkZ1BIcscDxrVjvIfYjdeEDZkjThpHJ/jbosbTf2uRyBjexfdaeRVdx/b84mA\nOx0BOV3frIwlRcegti2kG2oq37QrdM1nCwwy4I6uMK0zueT77hH5RxYyFkzZ\nw30mVBBMuHBz8IPo+TzrnV6ZXHjkwxR7pF9qUz+pcbEQ2+xrn8tzgBsur4Ek\nKkbocRz1R+4Jjo98wXpVpVU4sV2ETk0yZb6mhXa1FSlFR1krkuxbsN4rk45b\nTrWEIPqRBcfhcFOR7zZu7Zmub869vERhZM0Tx4lLAmHDbsdMFZ5h+6YSx0Ar\n4DxxUDTEeDH4hfMTsDSuVIaUOzyqXMqNHvnJXJ21DngVkRX6q9wcUba//XZd\n7xWh2lj6xpwBaOnF9Zd1OogAyZxteyYoSjLo8lG/pgOeQD8g1nQFSuLV7k8Q\nX72cmTkUjq+iad6xNcgnCytyRck92jrKl6pAJBL3IqS39kTVb52iXujo5HAO\nW7v5\r\n=946W\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.9": {"name": "@webassemblyjs/wast-printer", "version": "1.7.9", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.9", "@webassemblyjs/wast-parser": "1.7.9"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.9"}, "dist": {"shasum": "479d1afb1d2d961695e1c4d4f364c5659786ae74", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.9.tgz", "fileCount": 5, "integrity": "sha512-dhhbDZIhwXB2wBAwXAfeJUOkuk8NZYubcf90+Flt1QfGJxfAK4ofx+0uBkv+ZCfgtg8JhFv8BiQ7UUh2DQBY2A==", "signatures": [{"sig": "MEQCIHLgSjmezUJ87LNTaU4QItb2J238yEtHN+RKbDw7l314AiAEmm3aF/uvlcuJdNqIo+bfw5XytsCaLXrNSFoEntWvRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgiCRA9TVsSAnZWagAA4z0QAIkYnNsK5CGfNgmegIcn\nBKGnOySsbHUBLmcc9YLDYf2Axq74WEcVTBlC7qvADtf2eec+qifwYOS4ckoU\n0RTPQ/TtmShggJxFDL8gEVKrcpGbpZIII2lPhjU9IDV62GmGyJGm6a9IFta1\nm6wjmGPYlYlpmJJS3fzDpEDixaWMHgbz1u8nLNly26hfgsBtJ/zLsLmj7dRh\nmCr0lqf38rVANILjwy0AdK2oLLlJ/D72Zj5Ec/BWh6JbtBjkOZvFeG+h+nUI\nm2qHfiN4C1+cTP//EA/79Tcc5ZzuGlBp+Voqqfr9tRTGfS1OVlH4h5kddhU3\nRT+mpupisRi/qI/pcHOWZ2YqSKbitiLQftz+eh4qJ87Dix/6/q79NCvxmEZI\nlgTpzWnoKac4GO1SHde6D2+LQsOEXJYitATQSWywpmzqcZGBGieBG6+7bDK8\nnfM4NMqIQQ7yfrnhDkfyi3o+o+tnLwVnp0M8jpVQ5i0TFbVB4FblD50+dFaN\nELfPRLrRwuhUODalq4AK0jwt8LNKThuH1+ySL34JLLr6G0vjqbWKrNXBSWUO\newC0bodgvNqL4G3SKm9krmVsC+WtfQayn99wiCIzDsPTnQ2F/n6+R4FHgRPD\n2gdZmUUdPqoCSiHb5NjZbCuWNxgkVdchBvRRa9h4EkZwgVt6YdLC2eVeYFdM\ny6J0\r\n=WSaQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.10": {"name": "@webassemblyjs/wast-printer", "version": "1.7.10", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.10", "@webassemblyjs/wast-parser": "1.7.10"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.10"}, "dist": {"shasum": "d817909d2450ae96c66b7607624d98a33b84223b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.10.tgz", "fileCount": 5, "integrity": "sha512-mJ3QKWtCchL1vhU/kZlJnLPuQZnlDOdZsyP0bbLWPGdYsQDnSBvyTLhzwBA3QAMlzEL9V4JHygEmK6/OTEyytA==", "signatures": [{"sig": "MEUCIQD4N109t/T+NBNioB9KvRd4I7ly06wp6QfPEqwnfjAqFAIgbF5259mAFUXVw2c9nelSo5Fv20t5hks69xXOfKhbb1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuvlCRA9TVsSAnZWagAAyhUQAIkbHmFJhNcqQK146Vcc\nfcxcJ9AlP5LmQPs26DEAotIrMmZ4ZjQtp1ORH/Q+kz7dNCuXdA4fKSMyQ9Aj\nt0M5U3WPJqhwOJvyRhVv9/E4A4vti8eLyZGobNxpMBvmnPILpRQ6vhz4VBvi\n91/3TV3ZjMTpx/2ncMdxIfNroO4Qn3fhxpvt4a81c+3rNU/wfLz7TS7pO7/E\nqsGG0ZmQulh4BS0PFhK84QiXIDyaHVX92S2BSYiwJBgz2fLWRrAqv0ShTv3f\nx4ldPygmwHx78uOCetc2p3yPBhc13NHX69HnuhHUmBqH83TdXxNSKdZE6n8r\nR6ElJfYp4sFPZ9svmnXbOa248ZvA/1TPGe5KI/KqRztau8kYIO5dApPgrfQ5\n3BXzoS4w7pGihyMMYhvqI4/RfPBbAkg9IWt7ZcdMj0kJmUSmp6Qt1mRTJOG0\nAVGicrnE8BVyLeUr1aIoejtPwSOKxwfLb/ol7t2WWa9ANWZBUvIhZvy59vPG\n0GSpdPHc29g/mciloag6tKErljl9C4m6PCbl6DGZcFBNlLtDXhr69YoKHvba\nZLsV2q2GPW3U5jTwgmaGSX0lLk1aGdNqT/pcN3+/p0/eplWmAcVhEy+QrcY+\nA8oQ2TAX6n2wGcfvl9SKFvvExrFrzgO/Z1W2CxBtuhYddop8MNMsVjWaf1et\nravg\r\n=exfy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.11": {"name": "@webassemblyjs/wast-printer", "version": "1.7.11", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.7.11"}, "dist": {"shasum": "c4245b6de242cb50a2cc950174fdbf65c78d7813", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.7.11.tgz", "fileCount": 6, "integrity": "sha512-m5vkAsuJ32QpkdkDOUPGSltrg8Cuk3KBx4YrmAGQwCZPRdUHXxG4phIOuuycLemHFr74sWL9Wthqss4fzdzSwg==", "signatures": [{"sig": "MEQCIHeEzHa6MV//HFKhXnYy0teL2nQMttBREURjEaGotL2gAiB46RWjFThRQdvwzITTQvFezB1ZdtAifU0OFdIqSEc3WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxxCRA9TVsSAnZWagAAle8QAKJUWrtauMmGoD+1gZey\nF/ej2rDyHzWk2A36vrjlP4lkQgZaUymSmroC7HQtSiVX44eEqGpdXgpwB+t2\ngOFJ0NV2jlLTlfPUOFilVKi/8123JC+SWyoAgCuejW8TZqcCCKQP3n/twkc+\nTza5hHmHjTGsjIqHsXt02qJ/jao70GHX8ecF1sl5mddJAQbYUlDEkuabZskk\nRh1eRcCxqpy56XtmQm8qfGeFG9mDDQfiVsgEJNVwvR2MKaa4aK3uKP6E0wVA\n9x7oWoNdixu0O27WlChVsOSVSoxYgWN+TCCowDcGre+wEoEVUhcf0JufjcDT\ns4Q5GAXFLoLWjZJoOuKrIJ4i/B9MPIxETV+dSZjvrE3iU/9/AUTCUIDq8XGR\nAX3TSG4jQQvjWLhKNk4qyquNbSKggtQoUVfYuxUSNeGGlwIdLQB1DpQqup24\nw9P+hd5G7eG9fuCob+Z7HuykbXrp9fYl55IRyvezoO12CMfT2i1zn+hsjsIi\npu/eq450fTFIctM5ybe4BU5gZSVkw4/u0V/4AAdn15rtHq6Ue5AQ3Z1raNuA\ntBPzyVL4rZYGRONTyVd76MGWLMIuk8fVmt0e6GZYCXGApITZxKxGijAn3Buy\nDzHyHLXe+LC1F8OH+ey+3LLsO/etljRHXt9fkTIAhOKazj+ZcKcgUclcu8C3\nMx2T\r\n=X6bO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.0": {"name": "@webassemblyjs/wast-printer", "version": "1.8.0", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.8.0", "@webassemblyjs/wast-parser": "1.8.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.0"}, "dist": {"shasum": "018dd14dfc68f70cf6368cf6a83104391a01d1e4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.0.tgz", "fileCount": 6, "integrity": "sha512-IZvvwoAXUhC3+Y/4vaNYo6rG6QiQmNabtKgoR76pcj9QxNqrKRxIh6ns6jlAybeEPw1kSKmgx791QV2o4z3G7g==", "signatures": [{"sig": "MEQCIGx3tIjmZCeq+6Ec3ud3EZutFbdwIf9WTxsBhgK1qELTAiAXMQIk2zVHAPdYT+bJ0O/+0icC/c7j+jbNLxDm52CoIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2Y7CRA9TVsSAnZWagAAwpoP/2fquySL5k8tMeyuQeoj\nNqCnwsPrjf8QEhyfXgFfzfkhSOZAYm3pjq4sfpwnbz7kxf1lNd0irwmr+cyc\nclOvlyFgoMQ0StiId4weCI/hHD+45wUzkqfHhUW4wEf+Ga5XRdLZ0oNhFizx\nwWcncmLmLaF/k/6zYyeCFxDxPHwQEFDz+VoYY3zGmUOzVXBFbbc1OAkkmWBX\nWE+WHYu8rEdQ78Uj/EdZHw5fRCiKjc9sd8088wCm0bGFgcrZstd5ONssVLo3\niDxWJYq8UckGfgVcc3evJ/SbSBm1TnT9lYaXBlll3DgRSvhg9ySu0UjHoQuM\nADG/r9BqimS4e+cUFThDsgF4JjuwebwF8IwuL+lOdMlWVJQ/WshDi+sUi180\nASfHcjAnhw8aiGELZCe/t+xoIu4O3fKozoESyFco33hRNvZTGc+Ym/59Hq8q\nsZutkIBTQ6JVKm7LkgnxDyPSZiWaoNsXOuD+K6OsjCT4I9307dc/fncivN4i\njsw2TMC9RR2e/VMafOPQyK2VAPiPdtQkAwTMs/k7MGQykGc63xFHjOvIcMB4\niMZMvJW+coW2MOTIl9oPhBZTbRdnTWX2/IMP0TbAQSvkJfqe4FWiJu4KGUjW\nO0ZxB/o8n/qQ/O3yT87cPRFcNOhIf0UsGUbPI2CzDeUMs2VzyzdplP5+/X6t\nbUth\r\n=Xo0h\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.1": {"name": "@webassemblyjs/wast-printer", "version": "1.8.1", "dependencies": {"@xtuc/long": "4.2.1", "@webassemblyjs/ast": "1.8.1", "@webassemblyjs/wast-parser": "1.8.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.1"}, "dist": {"shasum": "a9e16d558d067c8a4ebe963aceb65d0130984d0c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.1.tgz", "fileCount": 6, "integrity": "sha512-YYRBpDCBLeYJBO+sVapLRkEE/+wrjv1O03IEybkqyls3sCZqhu3ZXjJwMSMCgFEyYP2MrdZvqL/dz2RBnULTbA==", "signatures": [{"sig": "MEUCIQDB2+zuz20p4jtAEcqojxZkcPbeIFhzd1g6jNPZj8TqmAIgU4YDP7ResgvYOLuFWCSUxMA6h8b5tdHLPpMxIyME5EU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgUCRA9TVsSAnZWagAAISYP/08WPnDhcg5cgP1TFpDd\nJfpvEP+X+Hswq1IQUg8EG18DW03Q2wa1wHrhUE5PSyX2GF1t2x9iO7ADnIV3\n3WPr3VFadt6U96RXRt4B8pbTAG791xN8rNqTnGtg2tr4lYz93/APKKNWHcVd\nqaSzTjDXyapQMdE1CrErAZ+q5t6Fb3Q2RMT3PfNR+/X7DaTdXbwTbKUs3FJh\nBYIzw2synnyJZUNa5HVJ/zcBv8tSgtMdjJ6mbNi5ZAg53twLjwiQvR+d+pBU\nt3p5ZVAC4ET3pjoe4W1mU2/1Vxkkmt17TfzGYaRq1xXX/7V8cl9o6qnIfFRi\n6xQA899we0ezi1DbyKoj1NWG75BdFRbWQ7m9y0+fm3DTEqr+zcqZle2fgOgP\nHXjZ1V4ynFDeY10zbFEZnh9fMfHxJ370/znsMAS1tVp+TEg4zsRwZflci7Ni\n45XH+6M8V47QDqBZRzBkOk0qnmUsfKp9Bd4n2POFk+UQL6zIwn+gmvWOGjSF\nFW6Wv5EDtV5oHIF695ZtA+uOlirEx/vTcNGxRul/qx/5dA4tAkKqu5apDZ5s\nAo99L1KynryjctW/RdkAamE0/ayWKW8vi4Bbh9m15yeCK36NuyVjCW2/Qx7H\nT44bm8d4v89leDz94j+SJnmpHqQLjgzz1Vs4+MdyqY3f25Q3ZEaNj5yO7VxC\n9ANT\r\n=Liju\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.2": {"name": "@webassemblyjs/wast-printer", "version": "1.8.2", "dependencies": {"long": "git://github.com/dcodeIO/long.js.git#8181a6b50a2a230f0b2a1e4c4093f9b9d19c8b69", "@webassemblyjs/ast": "1.8.2", "@webassemblyjs/wast-parser": "1.8.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.2"}, "dist": {"shasum": "a7dbb0e6eba3721fd06a949ac1917fd289b3db4d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.2.tgz", "fileCount": 6, "integrity": "sha512-TENFBgf5bKKfs2LbW8fd/0xvamccbEHoR83lQlEP7Qi0nkpXAP77VpvIITy0J+UZAa/Y3j6K6MPw1tNMbdjf4A==", "signatures": [{"sig": "MEUCIBmwJDyz9gOzp2a53OW4RXkxoswj9RO54S7gsEc35pIgAiEAl3TYyuQCZ5YXzlu4tHH5raJNVDgvs884mHFfIZG3Azs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWuCRA9TVsSAnZWagAAk2YP/j0KLataZlCqRkVhIIlE\nWLnSS3cZ71koX8TJ9T3z2MU+XeqeJjrvHsRsa3EkF8ObAMu8ufkIWoNcY4IJ\nIOPRCDhVtMqFD/cRooQwSedYUVxeZoMb8nHRopfKGNOGv1+X/55xJ9ugHgPQ\n48iMMbruY8CJqaHmGRko7QJFXdDe0UQvgiYpa6LEHnGX3dkmx8U8qwwv5847\nZwXwb/72wPwhpW0JnH2giLDHjIg0+AF+EsuITrclTvje0KIlRqALxFJvcKGp\nMXmBXqkxXJynai72DwYRs1+8bA97IkE9w+5Y3TJ+R4gW4737kI2Kbixs46ua\nqBR+nnSbCrFYVwI4XO2N4o+KBF4Qm23heBL3niQVkxdQygpMKq/ungnYybn1\nzAT2lyerjQ49CKN/eq3IM8T1vzrXogikEVTqW7s4Jk4lPkIhV+TEMsiWmoeH\nq+9ahbUaBUmTS50SmN6DxVUwjx+TwjyFtgBAd/UrrbmXaRqcFMX+YwwiFqyg\nNAb0ZTcdAZdgGc15KCP0aeQKKpmCpheTzZh3wYDZkV03m7YFWQnZMh1lbW98\n5fE/S/5/JJERpoM3XLAOmh6/BW2hTGppQLKmx1X4fIRGbz2Wh2JVyYjTN3MD\nAHt0d5FXayn9DzajfrGrT81KjHwv6PDYYy+7kX06JD6CtoJUgW88m5h/SlbJ\n5i2N\r\n=Cxxc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.3": {"name": "@webassemblyjs/wast-printer", "version": "1.8.3", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.8.3", "@webassemblyjs/wast-parser": "1.8.3"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.3"}, "dist": {"shasum": "b1177780b266b1305f2eeba87c4d6aa732352060", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.3.tgz", "fileCount": 6, "integrity": "sha512-DTA6kpXuHK4PHu16yAD9QVuT1WZQRT7079oIFFmFSjqjLWGXS909I/7kiLTn931mcj7wGsaUNungjwNQ2lGQ3Q==", "signatures": [{"sig": "MEUCIFvanLSW7rOeUV8Oh3qq6NgwtEz4I8PJQ/f4YMQgw3phAiEAp266plFXuJwZd9ilD+OgNjJR4hRe/vJj3SKIxeP756o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam7ICRA9TVsSAnZWagAAq2MP/3AHlBk4XG/1wGmZsxZf\nVZZglh2JbPO2iEsR2Ibx825GRMSZkzr8pWnyC4zHWyPI/6wSaN5GYMQPwHtI\n9Aws+IxpMcqJMy/gRoKQz0so4eZ9gz7WlK7YRO5R4SBN0Ob8gjzSpsnKkfua\nf3ez/tlkvGTmreOZWvWYZtIdeLioOdWR4JaDHWbEtXBjJvTFrFbvs1fCAI9N\nKeoaGRM4NM6d8lwbYvmrsRqd0nX31pOe0FvZ/yIksi2F6xSZzH7nwAI8Llcx\nmmr9Ywb6CkLQt0qIHVn/kUcVx83/cgKfdcbpAh4TWexrBNITz5sBT701mJQr\ntWuGDl9Iz71moCvuN6zTGnyA27akNabYkq43Ni13k/b9ETEgu7wg9u2pnO2B\nGjmzSsiPpIqU6nPHfY2Z5qMKYKkyz8vFFiacitgrxzGR2+Oc/vdGZbuf6WIu\nigPouh9e+sXaZmcoJeuSIk4qZvBaylUOOIPRASsxXHyIEm/84UWiPzhpwADU\njLy1tboKFq5fUYZJxGOAcQ02gYYd0P4R2ia66jlzrrz9IG8EYtYTghLMA+nq\nthKEJwSQxRtrg/p6JKNEpzUwYAXytTYVKg+lL7154AN/zWxrslstcTKnBDIr\ncOBulK2FyhklpPmBAJy7uTv/O7gl/PPHG2gDl5YgejyxvBCyuPNZm14nTXcH\nEgJz\r\n=etKT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.4": {"name": "@webassemblyjs/wast-printer", "version": "1.8.4", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.8.4", "@webassemblyjs/wast-parser": "1.8.4"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.4"}, "dist": {"shasum": "974cb1642d8d3b2b7edc4e0823017b2cdea50d29", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.4.tgz", "fileCount": 6, "integrity": "sha512-KYTRcJVhgk6BExe/tFU6HxtvdQVrLuuElGAmN9XKSSz2YfpkWIfAauF2rzVHNnMx6NBkoHaEOX6LosRk/IhkJA==", "signatures": [{"sig": "MEUCIFyOD5WBsieATcSk4d30qWEpC+fMZ/uHZZw5R4LmN0NPAiEA5moVCXm+mQyfepaVVBTpEuAPETxEKn/ATZFHbeQfDe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyQCRA9TVsSAnZWagAAfPYP+wfY+2utxVrFwpUEaU7f\nqzMX9QDjDX/w5VpM29Mikdlf/oatfwcyK0Jn5wyF6fnkaq9lyy2Dhreb5adL\nzDJxge6xFqfQ/ALErX8WzLJgpgxlmojhPSZa7XiSFVL893hCDFei6UKLu5lt\n+t+8sjRP6ZwZuwm99tbHpguH63PFayOL9p41b2KGm1pubjUMPBABIpHernnU\n2NjfFCmxOUD2JkeRsGseKOIlxDJR6weWCfnp0RHjA0hdyI+4FyqE+QCPkRbc\nsvr0aiSH1A0diag6Rh0P89i2ZhZiQlsfjZ3Mm3z2EYcNqjAPt1XKUE/VvMdb\nI7kfvWah24ior+GhR9kBVpSULSPfEnqCK4CrxW4OEIdz3UUp0k30wHycKRWq\nTY8d1zd1jZ+kAkXd8Gh+/4NM/ekWWo+Svkd3GTR2oJyinWFoQTxgDwIdcqXQ\n52X4VhnpGXy5ZMONsJudMJr5B57DEw7a2Pm7fL/MdYgKnc1ucOzHkDebc/O2\n/dZ3cF9pyacVQ24GzCboCKi2rciHSn9vvApQXrf/0VzXZfn7s7wSjxIFltB0\ny7BXCO84o5HDuyK8nyD8YXUi1KuBTOYZhlQk+tF76+4WG0yg7Gyp0FeWSyfI\nx2rOQlY3Kj++5//jdzhUdCQSGQuc77mWUK04MokVmKyjUPqL6r8GYujHzR+3\nr23i\r\n=wvWu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.5": {"name": "@webassemblyjs/wast-printer", "version": "1.8.5", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.8.5", "@webassemblyjs/wast-parser": "1.8.5"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.8.5"}, "dist": {"shasum": "114bbc481fd10ca0e23b3560fa812748b0bae5bc", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.8.5.tgz", "fileCount": 6, "integrity": "sha512-w0U0pD4EhlnvRyeJzBqaVSJAo9w/ce7/WPogeXLzGkO6hzhr4GnQIZ4W4uUt5b9ooAaXPtnXlj0gzsXEOUNYMg==", "signatures": [{"sig": "MEYCIQCErpskaYLQNHVjY2zdqmDX521SEjHZVXVcMEtS+OSsBgIhAOho9WmhI/ivDEPGSFTWjAv7HtHGJKdjSQyW1pCJUQAz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnU/CRA9TVsSAnZWagAAq7AP/3dRE0b39ih2ggaosdP8\nxv4SGUEaGapHf203LZj5upgbkJahbawQrwkxzsYQSRtJK7bCjqXLOCre8gku\nqrdQ3ozVdXFg/GQhHOnGE8VSSvx4f1ZqPm9uZjBE4lVLXYlkejMXQrTrNEzE\neXWsfb1zBoeplNOcCZO0CMv9bebV3VRjbzLIUNrj9FfY9+RxihLh0GG91eY9\nBx81P/21mEFquIayyzb9jqqep0ONiyc4uHJLpidYt80BwRzXjIXdXeqycxfw\nE7LD4Mjoy5qzCx3x8njOgUJzjyczxfd9LFfzav2LtwM9wfCWQ2l1M8H1MvjD\nJjmFmpg2GmlT5rebUwJAvDt6kQUaWHZmsuBsXX4+KH+LM5LamDGCUvBv21Yy\nOK8tHd3J3E85yiAzVo/BrINKLtilJe66aUPiORCAi2Xwf63Y1KCsTNOTi2yq\nIMAC6HJKhEDbwcmkta3wSlbxzhPxVln4Bm4ZXfOgs44bBFC3uczgSLGBZuv+\nx/0Nd2XGYVS80g+ZMC6r5dN++nTwel+7WyWZy6GalfyisW5NH3jmv66IkJNB\nlZ7zjFf0paFzgI0JYpqwsTNQ0hAqfJJNVD/181vKPKsAtOZV5g/xGH3S3sFj\njnDL0+vpVOiB8gYq+FH0K+zV6JGkiwblkGX5uJX3afyR6Y8smRtM36acX29k\np+86\r\n=FTgR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.0": {"name": "@webassemblyjs/wast-printer", "version": "1.9.0", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.0"}, "dist": {"shasum": "4935d54c85fef637b00ce9f52377451d00d47899", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "fileCount": 5, "integrity": "sha512-2J0nE95rHXHyQ24cWjMKJ1tqB/ds8z/cyeOZxJhcb+rW+SQASVjuznUSmdz5GpVJTzU8JkhYut0D3siFDD6wsA==", "signatures": [{"sig": "MEQCICS9NF+t0RKWBFYfc2gH9JTwP69rFaIPiCKrxIOov5YvAiBkV98/A2Ou80yIjTeSjI38rbo3N8T94UHsKKw5CF4lrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgeuCRA9TVsSAnZWagAA6mAP/37Larra7CopJg2OuwVw\n0dZ2owzx5r9IUrrNQsjMvJgoGDARpt3svUwhbkTlyhpBuYZWjDcmegsf71NS\ncNhCtSYo0vf4sncrgLWYGC4zq0Tsy8wVBoF8FRplrYgKCAYtWUEynGFjClJQ\nHb+6HXkl4/G9tJZ4jPfkw+EK6/zGswBcdrX/o8byuiRU0NcPDdOizi1EuETw\nWa1BuIdLsNEE8eAyLUGxnnojfwPyIk2XNHDh1Hnenx4kK9kHy0zqTqGkCVYS\nSOQQ6LGvDQgVtZEStyScCT6wbzz7Qf+rGjs90KmHPDpfRoBmuF5sQdOorPbM\nI7nZPtuFl1hOu0kf2oUw1Gp8UDbnj0wbQxweaX80ZYs+gWmxc5vmuqeimeVS\n8xmsGWY2yG8pGjW6MhtJwRHiFz+T0bircqQnYcfGidl0k/KUPz1tkZR977kC\nBRJnDRVWegzIMvKjYw06w+QHtkMLHGlqGz82kS0ub3Nlh3WicJa6/cBQ6wp9\n99pkus8aRaBGk5YAC9oSsfzDLDSPg8NRvUfXdmTsTKPK8Msb8LhXdPYLwkVI\npcP1cmIw7ZcnBmh+RK9BoZyhmEdOBl3FbVVdsMM0nJYHElJMfi61eOKXF0IC\nc8PoE7ZOSzKyl1i8xuppORknf/aRzGdJDQ5Wx0/zQHn1BEDjybLb+g8YIykq\nJKNk\r\n=O/Hg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.1": {"name": "@webassemblyjs/wast-printer", "version": "1.9.1", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.9.1", "@webassemblyjs/wast-parser": "1.9.1"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.9.1"}, "dist": {"shasum": "b9f38e93652037d4f3f9c91584635af4191ed7c1", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.9.1.tgz", "fileCount": 5, "integrity": "sha512-tDV8V15wm7mmbAH6XvQRU1X+oPGmeOzYsd6h7hlRLz6QpV4Ec/KKxM8OpLtFmQPLCreGxTp+HuxtH4pRIZyL9w==", "signatures": [{"sig": "MEUCIBvxktug/x4baD9OfUNM2IbSbFFgplESkaXEwigqUelRAiEAuVcTssiINsb9DAYdVjBh5Ig0zi81M5Syy7ItaOTYQMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNouCRA9TVsSAnZWagAA5UwP/jXPmTV5EiRPqbrdrAs9\np7QaZ2IE9ADzCGgsiUf4daXpi/qJVsZx3yxsr2lqazqhyakuqVHCdmnEIAde\nxgQNpaVGu3RCUUVMTCogtkSmIto+nN2SnEdG423ez1qFzNAb302RZMynxBKl\noNCZ4cOvlQAmhgFP8ze3eIjyu1PlSftsdt5hROt3c1IgvPEbot5RSBH/vj7N\nhDCkLYl9bGCypYls2dGvAHmEwcsO7P2Oh42IZTyt06YEO98ouG1rqqgftpPV\nyn0GkJKfNY6t3onF542n2WvChv5HZLebCCqxng56C6oNQ+ma/6ht7u+kIf7+\niFyTh/N/8rM1vN/AJ3vOErIpEgkRF5533vtQ8YAogvUG9pOekZuzXDMhYb6i\nzdw/J/ZiBpy/PtXxlcjNjmDD2ShlNUmyZDXoLFep3rM0PgvgMapeumF1sKH7\nIgKYYiCKdabbccAbNmAbLIUfgvVRPvwpHhw/LPRybiwJMqOzwBqq34rLkNvd\nDM5oxni8vGl58Yr/74ur8HeweDLqzCapeBWrqXZIilvSHE5fa2ZMGXzv0tMv\nPJcFPfjDMsAhzhwRG8MuyFkZ0d/FgSKF6/0+9J5X6npAlUWhI36A8hB809kU\nLO6FjaGddcDa9TnEAEioSFx/SaeFwtmisbzalq0CiuL6hfg46SKKd3sHiUKi\nHsHt\r\n=IsUE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.0": {"name": "@webassemblyjs/wast-printer", "version": "1.10.0", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.10.0"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.10.0", "@webassemblyjs/helper-test-framework": "1.10.0"}, "dist": {"shasum": "05cd84e9882aa415f163535375eea1aaa5487ba6", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.10.0.tgz", "fileCount": 5, "integrity": "sha512-iSAy0zuzQfft1IgR+3/jB0uLxjtUt/rSkdb972xQM9pvzdewyQb+3YsueWHKHuwZksVATSrmpYANTxoQL9SXiQ==", "signatures": [{"sig": "MEQCIGwlsmRFRFCuORvRXMx3Ae+sYlQtmaR+Dzpt4iX6ikEZAiA/YtZlCrU3+FD9X/c8/e/gz+41pcs5npwFaVSwV5Sq8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zRXCRA9TVsSAnZWagAAvIwP/jaRTGWxWhk8EKETafZQ\nsmCj2oHW92NDEtEWKQS84jtKuooVz6Q7UM3hmOEPpwRcyWY6JnY/LwVnIBv+\n6MB9ryI8TGWjGtojO/TZFEO+nTV1/YxXxXVOhcL06DH7C6AeyFktnuDkvnid\n0xRwRnFpXisr0rTRszGvyh+TvuM+kPZi9574BCRluHNQBFBDpYowlbxmn5H1\npManVEvWMXSsS7PLsYmt355cHq5rPzt1tpVNQ0AMFcMSz0iWELHYv5gusO6Z\nA+pm9qG2qUdSO5wx7WS1pxOLGWvFvFMncYouX3PxGhHQakKWzsqk8uOc5Jpw\nlL5Yb44fcqyrsSspXUjP5T7r6ku2aD2Ix7n7aCqF0nlUPjTCSbhZiAL77F0r\naw5hrkoODxmjU91Gw0HidCcx8fmRLOy/wj5O9/EMh4DDkm5whIMeo/Rzxr+c\nl+F58LWzR4cTXAcSxZ6yVB4dZI1K4pCMYG11NqfJ8odbC/mJEJriRSyZkqb2\npca2vcwO5KjZaAQVYQ0c+UwQYKWANO76hNNiYpAUIc6GmO2uSv90jgjmrKOM\nmu72Wl2f/8gjShfIjGlXIL+hXf/GeaXDM+eVx/ZVNPXYYv61bNDlV1f6M+g1\nd2IY+KWxSx+BRgxN2oNoTzil3I0WUJP0Zm+pWAICjWgUAGzTS6e1iPniSs3P\nVXuC\r\n=q+Uc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.1": {"name": "@webassemblyjs/wast-printer", "version": "1.10.1", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.10.1"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.10.1", "@webassemblyjs/helper-test-framework": "1.10.1"}, "dist": {"shasum": "b10d703f50b38d970b5321dc14fdb8a74354f040", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.10.1.tgz", "fileCount": 5, "integrity": "sha512-RicDLSHIjthYSwUxvWeJV6iU2cEBxA6OmMgLhf+oE8npFit5ZqIJbJFFSfiDdE7+AgqYXOhonLgMlLDx8w/vAA==", "signatures": [{"sig": "MEQCIB++zTqoG147Zow3rbzsPpah7IX4gUQQ/Cinm6cEXpRlAiBBFWnnjIBdSF2W2bMsfFGmS+htRjh8JAqL/cZ0Sv7Dnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqnCRA9TVsSAnZWagAAvPMP/1mUJZRYOSSh5p1rt+7b\ndA83s4zYejrH2hIoQHn2SFFVuuGT7IIfEN/Rw6LWFI1OwiQMiVX42YrcObBV\n4A3UYLvZe0E7rsgzvPpnsV2BCKSua/LHseN0znvZcPZRyUbILB7yA/T7cqJP\nxE4WC+bXJsfiIbQDgMNvJhRq8OABuNuAvYhfO/c96bXj3ZBi49SWSYdtcJbK\nehJguSWl41r6zZUs1Mzb0JwwB+h1TnInv9ePt0nmU2IGxkEZUYIQPQW/0VAI\n/FrTYeYSZCBII01Y5+GH+J5d8YBhl+bcutX6a5sNuz4wT01DJv4HcRXjzwvA\n9ikkECpDUM0YYaTdpu6VXHvMICgIYQM1p5Huzr6wsuAPPjdjp2tPvcPfI2oW\neqvIzGe6wGHXTVypZ2OLSuW6pu7zntyRPbeXPSS1zQzLEBEiBrDK/Xct2Oxn\n+5E7HHEPy/KEE/4Fkc/tZV0EBH0lyeIUlOeRAjIt5Tq5ESSLlaW6AB/X9J6P\niKUlgHFUQLi8R8BWybdf0MEyZ4F41w0aLFgnkBOaKLotcXZSQZRZksI4IZX4\nDA7GgYSocG1G4Q3ZbZvuFYPcje2GD4fayqodfv078B8YvU/o5hlB33P34hBX\narUZYiEQiFs9+HYMoxETFv505+KgMrWyZish5pdsfuV/IqPa0dJEi/KaJyGA\nEqQJ\r\n=MEkQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.0": {"name": "@webassemblyjs/wast-printer", "version": "1.11.0", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.11.0"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.11.0", "@webassemblyjs/helper-test-framework": "1.11.0"}, "dist": {"shasum": "680d1f6a5365d6d401974a8e949e05474e1fab7e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.0.tgz", "fileCount": 5, "integrity": "sha512-Fg5OX46pRdTgB7rKIUojkh9vXaVN6sGYCnEiJN1GYkb0RPwShZXp6KTDqmoMdQPKhcroOXh3fEzmkWmCYaKYhQ==", "signatures": [{"sig": "MEUCIBnIFdcMS+ud4oTUAcRUiSBtpUsm2IIyewiWittJ9OypAiEA0Oncwhvt4qKOUB3gX4764X0j9/95EA82HGHLtWFrJYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907iCRA9TVsSAnZWagAA3RsP/2B9iTq2cbfC0DaPWEX5\nELyndz5fDgdoPVjVnwuqUGH86ZNF7CHZA+j8iaMTeBcZQ4o3fX+zluo3OuRL\nie5rT5D2IF89kqImAH5E8J+dW9FbzA8+XWPybFQ0BqKZY6NDRpacWxQHNAnZ\nMb/j63nOpXL5mkD/9Dx9Np7uZnrpFn2JqB1G9W9aJEBvZuKSPHPO8KH5/3hK\nkKLznz2TbyjX0di5Kz4DqSm0nJZ/xXEVRjrsQwUpRY7DohaNZwTuR+6QjtHK\nr8h9OMBCm6yfjJuOPbMZ2h9w2CfVnctcQ+MlgotbX4lpmX/8pm0a0BEGptEx\nzVniTWYKe2q5n/N07+volkC3awjdPjhIxas5U8NVVfWZROUdYXlqvwWBQJyq\nRF6N/znIgDZqrVDEj+rMPn0nskoni/7bxFzlLKcMsTi/8rzXMhdDI8tnHIWs\nbpim6BCXBVIyA1RD9uPq3hBTAMq6znch7oD02PxinBR68eMSW1Y8byHaO5Aq\noMFsO+KnnnvJ0d0tCdxy9f+jldRoOaa0PLAr7sS9QttV3TcTqX4Qo4ZvjIdf\ngAzRqUBvzoE8fU7PmGs/hwxjrb/XqaVuXWkd+5uBflUtIqknrlMNrbHI2Rf1\nAriUXfp7NcCCY2XcFuqM3hTLhV2t6zjzmWhQfywJdYmG64s/umG86MEvfXdY\nyPS9\r\n=JKhQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.1": {"name": "@webassemblyjs/wast-printer", "version": "1.11.1", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.11.1"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.11.1", "@webassemblyjs/helper-test-framework": "1.11.1"}, "dist": {"shasum": "d0c73beda8eec5426f10ae8ef55cee5e7084c2f0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.1.tgz", "fileCount": 5, "integrity": "sha512-IQboUWM4eKzWW+N/jij2sRatKMh99QEelo3Eb2q0qXkvPRISAj8Qxtmw5itwqK+TTkBuUIE45AxYPToqPtL5gg==", "signatures": [{"sig": "MEUCIAsFgSCSm3DXR3rP71rIeAvQH+Kyd9snN1r7YQvX8Kv8AiEA1YIe3Gm90Y/oPe3xQBOZe4Y9uB65vBW78uL2H4SQz08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sE7CRA9TVsSAnZWagAAIDwP/0ojLPLy0/d0bgFdr0wn\nfWwr3HShcD/i2a12EuVkwaIrp1yDLtBnqnvcnnFhnHCHswMb6wwNTT6ArcHl\nTBWpoAK+KVh1RhaQrl+waWEXTUqbYhkfNMVc529/iNZZy1oafcjkgmeCv0WA\nd1A4u7vZZNhZ4xLN0JibHXmWAZTCE8ngboRZkrj5XKSUj0WJfq1V+A2fta1X\njn9pwS0h8XtZ3HW5rVO+z1KLix9864DYARBMg2CjJrqPvWVHQf0NAzdrcldL\nAfH0izz6HLcX6jvZ3Fw1bhKXa/GTMwCSAZfwNAT4jliql+juoW/Vz+ffeIdr\nAVaw4yzlkcWQ0Za/gSvZnP8xTz2hNJzBDkgsx+Xs0JoHY6qvklrwCGpzOaPd\nU2KvYSPHslZiL0LL3MWGdwoUlUICW0B7sgIcedDTiDJPGjGRiudDJ3TMT1At\nuP10m2m2vo8/cYJWJ7IJT71FpKsc9HUqZSX6dbs4okWGmFdP0XTYdrNjwEDy\nv8HBTCdD8gN+Hw5pc8bTgIdiK0kvwX6ezdbja7lHJvQNlDD4SWJ8/MnPSLdW\n8YREpb8eMQCa4/HxcdsH9hOyJEi3CSYpH9OhBOisqCcBvHAfZN1P9fAsbHOc\nG6iLcDXjcGvqCtgA2iGIIn9meWcZdEKrLOZwI/ctKtobfYKuAa5uIlz89iYu\nFWu4\r\n=Brfi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.3": {"name": "@webassemblyjs/wast-printer", "version": "1.11.3", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.11.3"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.11.3", "@webassemblyjs/helper-test-framework": "1.11.3"}, "dist": {"shasum": "39c34f32a816d7f8c7b4f8219ce680b7338cb0cc", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.3.tgz", "fileCount": 5, "integrity": "sha512-kmjyNBu6w35c33LADVqSupAEIGQfZE5hUM4TPVVCcFm/mv72j5SVTMfc6S0nv1RQZEwSqi9ykadr5nIgK1NYLQ==", "signatures": [{"sig": "MEYCIQC5gjkvhUSpyVTU4pp89ljwCTCyMRUkkmkOp4tEyBG1nwIhAMbfazS++tkPKdqAKkl8e8lGv638/RP40LzD71LIclZv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSSA/+IJBReITkfLjfTLUTY+MDxePysgWAie3g/wdp5mDixn3fgFCy\r\neoQOHjm7PpAezGPbHJ7rt4Rb65DEnbllFRPLJhgOozusu6DfPFv52A13q+i0\r\n2mmTBhxem5engFCeeZX5HmdNmRxiMob+HPPjWiyOD+HIforhTtOYQ4BylHOQ\r\nIugxeG+78O3C0xkX7WS+yTmiRFabMYiFgjLXEhA+ImD1SQz1S1KhA4J2m1nw\r\nxDxh1+EnoJyYcnTuzYDOEetOg0gk5ehW07gY7nHsfJAsgKqAWYxvKbdAQ9wp\r\nEH5Iskqt+Oy2CKvLc9Oz3Vs9k/Beg7K77s9COJk7jAKo9luqIc8UEytCJlZK\r\nOMRTb0X/iO6lYv7bjdz+bsKsEEV0/0uO/I4+HbPJSg3pnUQ7Lf0yLOcESHXP\r\nK8oIcvHnP+A97IV405C85Wk4Lt3c3358j7E9icxQstY5qdrZnVCU+mh3MY3/\r\n+owTKKvnXZg+nWmV8IYaHkBlw3xa94ddbAnetRmkvUAWNqj0RWKdgbe2orMu\r\ngW2+EkwuIfZmdBwWYAE/BCbutbcouXzRuZ3wywmfZgrWOy3XyK7owhAdBX7j\r\nAmwMdFoVowzar3QrKLnEkryZzu2gsJII15nuLD7IcEtt6d9EdHHG78DgSkf4\r\nHvrr3odF/mW/llI5fsi1IVhHSvS+1nVFm6A=\r\n=ebkg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.5": {"name": "@webassemblyjs/wast-printer", "version": "1.11.5", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.11.5"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.11.5", "@webassemblyjs/helper-test-framework": "1.11.5"}, "dist": {"shasum": "7a5e9689043f3eca82d544d7be7a8e6373a6fa98", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.5.tgz", "fileCount": 3, "integrity": "sha512-f7Pq3wvg3GSPUPzR0F6bmI89Hdb+u9WXrSKc4v+N0aV0q6r42WoF92Jp2jEorBEBRoRNXgjp53nBniDXcqZYPA==", "signatures": [{"sig": "MEQCIBa2ISJZ5K0KK7MyhB/MkR2OE961b5fQ6WKvruNIcuhEAiAU68OtTXZ0S7CW8UrwYzv9O0smqInRUnn7PzfGPs90/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6CHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Gg//cxhMW5/s8+d9AuQsK7T+VF4bCVb91DUdj635A9uRFnTDV6Cz\r\nRZTOK9DR7BQWOR5DcphK3niuYlaR1bkMnll8CqLdZ1DsxW7wuQaRNVZCg3YF\r\ns7rB5GNmIKBbUpITHjoiaa6R8zsy2VjRlMk/gZxbrJZzBtyS5MWbHbo1Rbej\r\nqM4oF6QUU1kCdQfdrg9xwQq1RLf28ylzxDtqgWUe2vso1It+SdSO4ASeIGla\r\n66eGX1X2WB6GSjCJbgwLi0qDkgCB2ApadLpMhqq9yofdIPVGoCk7hOYIGqNC\r\nTLIwAnvHAOb8RR7fdA8EWlLAky2xOhjIC45nmeKE4RARFzifyj7Qm4vkkdmW\r\nPFUEUSN00KswpibJEgbLruwWWmeJbPPujVHy5LNpwcNeU6839u4bBKxTvep7\r\nAlnUzmneSx1vu09FezTYUEk5koSAUnp1dm1LqlFBNgNcgO+lh596ZEbTBiTo\r\nwStS9GhYs7oweP8DAfaey9kGTTD2zs8icrqA2y5m6VqqnTy9fCB8gb0GBjix\r\nMGoWNwgvupt1JFuNbfurbxVPamd4Nf6AkvCdmInCWo5163qM5rAINUTVXnM+\r\nwS/tyFGEwiqXSuUNlAzbWjVyXTafXHcMYfoyMeyo4vC7ITeEx8qmACa1SobQ\r\nRCJ00cSxn0MZqr9lUEDLEZVU6O97/Re+Wls=\r\n=gzIx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.6": {"name": "@webassemblyjs/wast-printer", "version": "1.11.6", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.11.6"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.11.6", "@webassemblyjs/helper-test-framework": "1.11.6"}, "dist": {"shasum": "a7bf8dd7e362aeb1668ff43f35cb849f188eff20", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.11.6.tgz", "fileCount": 3, "integrity": "sha512-JM7AhRcE+yW2GWYaKeHL5vt4xqee5N2WcezptmgyhNS+ScggqcT1OtXykhAb13Sn5Yas0j2uv9tHgrjwvzAP4A==", "signatures": [{"sig": "MEUCIBNtcItR4nvyR90nZa+XfRN/fYe++Yasu1geBxpSGrhcAiEAwwXPot47s5CAyLbSgLx8BBiUJvYPKzRHv6ViKToamdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19847}}, "1.12.1": {"name": "@webassemblyjs/wast-printer", "version": "1.12.1", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.12.1"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.12.1", "@webassemblyjs/helper-test-framework": "1.12.1"}, "dist": {"shasum": "bcecf661d7d1abdaf989d8341a4833e33e2b31ac", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.12.1.tgz", "fileCount": 5, "integrity": "sha512-+X4WAlOisVWQMikjbcvY2e0rwPsKQ9F688lksZhBcPycBBuii3O7m8FACbDMWDojpAqvjIncrG8J0XHKyQfVeA==", "signatures": [{"sig": "MEUCIDfOtQhFQBo6Q51KVbBan+jbwDz9SPfRpIEadHEem6adAiEAuQzuovRYhsckJ/BTPZYY7nndKoBz9PhpIlpjcstCylA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39576}}, "1.13.1": {"name": "@webassemblyjs/wast-printer", "version": "1.13.1", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.13.1"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.13.1", "@webassemblyjs/helper-test-framework": "1.13.1"}, "dist": {"shasum": "a82ff5e16eb6411fe10a8a06925bfa1b35230d74", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.13.1.tgz", "fileCount": 5, "integrity": "sha512-q0zIfwpbFvaNkgbSzkZFzLsOs8ixZ5MSdTTMESilSAk1C3P8BKEWfbLEvIqyI/PjNpP9+ZU+/KwgfXx3T7ApKw==", "signatures": [{"sig": "MEQCIGU27mgNmKT4wMpBQVolxURTat/LJRmXXLALpqnzZV5OAiAzbI3mNIh6CUGa3qMP/boFZnRaD+L2xDWBWjTlOQZgfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39576}}, "1.13.2": {"name": "@webassemblyjs/wast-printer", "version": "1.13.2", "dependencies": {"@xtuc/long": "4.2.2", "@webassemblyjs/ast": "1.13.2"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.13.2", "@webassemblyjs/helper-test-framework": "1.13.2"}, "dist": {"shasum": "8f8cde22101c98eef87ad86f015fc29c80946e72", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.13.2.tgz", "fileCount": 5, "integrity": "sha512-TnhBRK82OYvRL4hD7ZinzVnjWr41ROBT7tPR5Aq9bNLyAbCkAMQIlv+X+PRBifxdTEV4NMXx8TYL1QVutJ/+2Q==", "signatures": [{"sig": "MEUCIQDrN8O4ih9NrNSc505r6XVuFrPJoY+v5yvA5dWsynrrAgIgLVSLqfYwpKPwb3j+c/XsrpZb0MzLvEO6mcPWa1n3nrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39576}}, "1.14.1": {"name": "@webassemblyjs/wast-printer", "version": "1.14.1", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.14.1", "@webassemblyjs/wast-parser": "1.14.1"}, "dist": {"integrity": "sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==", "shasum": "3bb3e9638a8ae5fdaf9610e7a06b4d9f9aa6fe07", "tarball": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz", "fileCount": 5, "unpackedSize": 39576, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOio74XT7SW3CCWX/h5+qva/q+Jy2E7iNmSL5+NodSRwIgew09S9+GjbO4/8z5J7OwoBp8a/7N9BsAtIWDwwQsIE0="}]}}}, "modified": "2024-11-06T21:53:38.783Z"}