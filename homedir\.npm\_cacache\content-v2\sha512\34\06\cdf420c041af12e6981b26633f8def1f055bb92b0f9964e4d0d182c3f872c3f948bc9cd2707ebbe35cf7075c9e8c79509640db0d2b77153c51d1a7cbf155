{"name": "@webassemblyjs/utf8", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.5.9": {"name": "@webassemblyjs/utf8", "version": "1.5.9", "dist": {"shasum": "b5542d0d03bffa79f2e764764b01bb5ef40f4fa3", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.5.9.tgz", "fileCount": 7, "integrity": "sha512-U5675PA+aoZvIZO9Bj9B/Ed6DNWlrBAdqLk+oNIuwkN7/QYoQBw8XyHJFkjGuM6BwgJShUF/kkbZchQnGnax2w==", "signatures": [{"sig": "MEUCIQDcTFRVrunb/4jXCHMiY7I9tO6cnvwzgwqg5+LMkioKHQIgbaihGoVIQW1fS+qnvVJEaW9sMmAzjT+K9wLqXSgJPxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEAeICRA9TVsSAnZWagAAHPUP/2+VtDawkioQ8blYooPD\n2p6aA1FQ8I/wfBs//N+v9actGkXNj4zYpmkSamb8K5RWrknxJ1R1l751Alw4\n3Bt3vUbRJl3lRBf8Lpn+kxFjVEWzHzPg8+0p4jaxOt3XJuJPyz0PnsNfo2hF\ndBtrwT/rS29LnxZUzY5QDImLtaoicG8JoDuH+/j9YoO/SX2a118fGkTSuWda\ndM9Ih7EfY2DGNbQF8z6Qy5CTWR9DUNF2KEvDhIkG9pTQhrzLBQWF6PZD94Js\n6Z+9GnjU27vy7WSO+yFDwX+uIgaTfwTVnEv9wIcKvOUzFPuCJ6Da3nCtpOME\nF4xuBXrxFtQBhrYss4gWZNo9hr1fClQWD5APeaq72xfy8fmGKNWkuQAF5JPe\n3EIL1dyf7lMgXy118S/Z8DWuGZdrQyw5xFJeCd+S7ATZJJOd10DmTA0VhtRg\nwE2RtQJ1wfeEnvFJpuY++ec2m3QvqG8TMqPKqKMdt9XxD4tIoa5XBd422A0k\nYH9KKdTPLGBI9kkXY9kt2Stq2vSxzuHC8SB3KcKBML1yduXp5fBwjphanpq4\nKDdDSR/ZwVWKni6WXQaFarKssxiFn3Rn+/ie+knk+qYwmaFoBfjZh2Igtk1c\nNwWzaw2IrgHrwK0oCHkU/lMpSjAXpoaQUvxb9bayYGYhpqt3S2lE2L4odpsV\noKeM\r\n=ae86\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.10": {"name": "@webassemblyjs/utf8", "version": "1.5.10", "dist": {"shasum": "0b3b6bc86b7619c5dc7b2789db6665aa35689983", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.5.10.tgz", "fileCount": 8, "integrity": "sha512-MQM04pZd0DoxukOPBJD4uaeVQ4iaWzRqsq7iDvJQBqcxIIEwi2iAPv+xjL2PbVwosCvwkh7FzKK3FHVQUjTlTQ==", "signatures": [{"sig": "MEYCIQCr4+K1a9LpxbnVbZ/K1UwhMEYxQm8bNUhtQB6fQVnZlgIhAM2OBygZtSAw+0GDbjTKO/6aDKYq1/ISrfqvcBDv5LfY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUgyCRA9TVsSAnZWagAAPpYP/RJeu7tcC+82SMVRQit3\nmFuYjp7JpBaeD0mJ175+JgEYU+FenX7nc7N9im8HqbC77CU9biAc0dS4W1ZC\nidQ1FBrE9PCAXyvheFvR6wRd4F5dtJXM5NJynRku3gwaCNdyNh6iud6RRGyA\nYHyB62JtR96Q5WvlcgmMyFfiToW9h+PS57dhYutaiUTIXCcx4qkMznSn5z5W\nroME4LLmAyEno6xV8BrjnuPF06+Vqjh+FfiZOn6pa9HMLD3JJ+6Kwrs2Nj1f\nGh45YMhqojrVSIYj7I6GyHQIR2jRqmkdYp0MA48ivXLXJbraOvFKi/oWNz7B\nJn/ts1aEx6WDzU5+ztvJG3AyECHNv+SuqnfnkhVXvncRFcp+0Z2Qg6mDZJBh\n1aSZTFPvCfDdfb5JrojMBAtPthkLHpBoEYfXKcl3VH8e+VoQlhaWe7YtWY0E\nsbIBhULI9Y0mPu+1JrMZJq3eTfhJmUdwBQJ6lnxeaw8M07fbXhZ1K2HRyYWg\nM/QIk4rKYfFzOQ36myrXWiZPJ2K2/ZPJaeLsCtvIirHViZ98Di6HMChmmZw+\nr+0ZVujigH7m/aDAHFEvhCLRnh5c4dUE5/MY5xkxb1/4tAjCFtqQrZhd8ZNH\ncnxKP0jSWqSNWCw4ga4e98xPK7Ola3EatDufqCOkXdxeVEda/uZdFLgMtYDy\n1EmQ\r\n=Cun9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.11": {"name": "@webassemblyjs/utf8", "version": "1.5.11", "dist": {"shasum": "f8de902e935f8ad34e5a0536970565f889318428", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.5.11.tgz", "fileCount": 8, "integrity": "sha512-S+SM/1yaEbWujCW2UVzZv5nlhqxH8BVf92elZ6c2aedWBDJucta75zV9qpDHrmQedLYHYtHINV47ndSI1xBR5g==", "signatures": [{"sig": "MEUCIQCOQvA/TtWf+BOOvl9c0sZsmBtUMWMrlxYLsnzInsUOHAIgW2pT5eSR7ZDiagMrTb1dnlAgyHth7Ygo4EGQPhOLbio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6ImCRA9TVsSAnZWagAAefoQAKMQWoTK3z1SF2pmTUwd\nmuaFI1n5QEnnx4tXGPfFJetuC0U/r5AjgzhDYz1GZOf5PVxeLUjyqxs98mug\nqOw74O8UUhHHRtLY/Tt0V80b0G7QEykVqfdpVOO1pU1uMqja0OUqvm7u00uq\nNlhmAbSgjHX1GFA5+MCYyuk+a8VcUwbz8tJEPtnpBn8udaHPqi1GDidbYyId\nvOxDUvfsZ/rfrrXc8LNW3QjhQPfETr+oQ2MD9U7AxnkUiyYlaLpVyrY+kT0u\nUAfWBSZccFiYubI1gz4s9fEm2dePIX8S9d+QX2OIz2MmLW/+pMvWcffNU9pF\nw6s8faJaC21PzLolao8Tg4yUkfzj+ppVoOs2KPAyxFDEnS0NHakCrfT980GG\nfY82JtnFjv/yBAaPR/BaDEUBONtmKWcr2SARzEL02cHludsJ6PqU1/7eYMQp\ncWfYqwaOUceFAAsXouioSAzor7bjHtJtTH1ON5FCdZZ825/kxxrdqjbsrey6\nDTSh1oQCcSaPmgg6IGVH6JtHylYy0+WaOoHdeufLH++OUBVYpK7HiP8xwx2h\nRsFwDtVmCTVuEpPd43OAhzoIjMgehk9iO7bqIk+C8UTaovasgaUCSHoNEpvw\nnHhCmrfOoHHBuKKJd3tOnpboqsXGwVGM9Am4ABRxWYhKU+cY8nAMUjeBMMgv\n0bkj\r\n=qXGI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.12": {"name": "@webassemblyjs/utf8", "version": "1.5.12", "dist": {"shasum": "d5916222ef314bf60d6806ed5ac045989bfd92ce", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.5.12.tgz", "fileCount": 9, "integrity": "sha512-FX8NYQMiTRU0TfK/tJVntsi9IEKsedSsna8qtsndWVE0x3zLndugiApxdNMIOoElBV9o4j0BUqR+iwU58QfPxQ==", "signatures": [{"sig": "MEUCIHz3dIsch5FgvhtBfhWBLI4x6CNCQQrlUGZ1yRF/C3OLAiEA/Csiv5AUXBnVOAM8moMiAZDl9EK0dbBWxRdHFkBXeXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPn7CRA9TVsSAnZWagAAFsgP/RQCaL93V8KPspZI4bNi\nQ3+Yy+eJhtT4iG1eqFkvnF9dipmS2jYne7G71ccwiq0ZtCNntg7tYZNY4+C/\nHwdrRJ8IEjrqo6DN+ON+TgUh/5rydLHZPBk5/ehYvmZ6+7Pmp0SclGj/7LnG\n4Ytgzc1M8sGaVgp1Y5eZE2eXtgzsj6N+6kdTX7HHI+M0Gue5A+D/Vv01SxAk\nWxF8gtfYdBFNlHt4RhxwoVDFwWXEVihaOQi0UIn1Ulpa2ZhiZXnoiHbPuen7\n+XpsEe/1j0Jrky0Quob1qpb7RvOX4NQoUSI1gOzYMdm59Yd0LaeCYI3Qlv5N\no009tJsErOAA2D9ldmd3jSYEUfbFvuxyrEP8b8bq+/695BryfkLcvb2K68KN\nGDV7CO/wrEfyHXDRbnE3CZiaL5CoCyIjTMH986JbpQQG4XiVUY8di/Ry9YNQ\ns8OjW5wgmp9N8ZBskngQ3dz7oCWwPbHVX/uPIVUBlfb8f24+B8ReUlciRkab\nFxN3RPyznEGRnloFqLxRONfI31xDkRu/QfSGbidpqCFnI2VEq/Q8GSThEmKx\n0HNMcapJgg1kBG9L5GhG0nRgXh8xAkwgaV5sShATsdDNxWpojx4fNwTAch+J\nTa0jqe4SVwr3wP6ca38mNNekJq3Tcpc3NAWKA2OGuWWC9u7UZuGRk4ME8iSe\nsYlu\r\n=DITQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.13": {"name": "@webassemblyjs/utf8", "version": "1.5.13", "dist": {"shasum": "6b53d2cd861cf94fa99c1f12779dde692fbc2469", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.5.13.tgz", "fileCount": 8, "integrity": "sha512-Ve1ilU2N48Ew0lVGB8FqY7V7hXjaC4+PeZM+vDYxEd+R2iQ0q+Wb3Rw8v0Ri0+rxhoz6gVGsnQNb4FjRiEH/Ng==", "signatures": [{"sig": "MEQCIFNKZIeNyNZsnGPU7rEZbn/jhfJSV6tEHfsRjCxi071yAiA5CWxnZx8GfQkgDyLStfT6jcmoZdwTyqztVux+JZxVrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lGCRA9TVsSAnZWagAAHWIP/1RAbvs3u14NJo9Zcv7+\nmarEQhldRlK4ZPkcU6RXOls3oRFThB/ZY2EOX0HvdGJnflXTmAQ8Ya1+Ft3j\nT2GDf9RZLHHDZ9GyCx+c3Pr1iUTL1GwLpDKsR5j7+jyusx1bpPyAeolbDLAK\nujvRXBmD3R7kve6L7UNvoac68/ho3tMs68bAbJdSAkr5Ai1n7Slm3gmzfmpw\nPQxnLXhTlPq2iqGqIhv48C8Fq1S7sA32YClHXGqMCfKDVY3eFGpA7EmcP8EO\nnBSqhQE2JB2v2yJULIJlR0lPfTg9hUip9NE9UvSC98cXUDXsGwCqDVI6Re6c\n3+mseT+BweXRdlgrQxV/x2rqsDUxD96+GXst4XX+HEfEGJdJtX/mzB4IBcs+\nSlVj/Pv+FmZB1v14cJr8LcJm6yCOo+1GEV666jrzw64/EdByzG6peqvlWZw0\nGLR3q1R17tzKE6ogGmvvnyBdLlgTy4tH2TLzty5HpIPll/uxtirqIl/B+HJt\n2pK0tvvI9Lw8yHMbvopKHZJ12ixIr973WqgvbNrZnqt9h/CAM0WhDyoyvTV3\nY1USeo3cT0bgR9tW0zWdJZwYmFa8AjzwnbR0dELPG/SNpU0WUjP3YLypzfsT\njkvUp/0VnvDD02JttIHzU28l/zeDRNdqX7bqaQhTli4VKNdo2Cs2tUO8+jzw\nJruA\r\n=uB37\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "@webassemblyjs/utf8", "version": "1.6.0", "dist": {"shasum": "7379d36c0557c22003193d51aa7573f60dc3e1a9", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.6.0.tgz", "fileCount": 8, "integrity": "sha512-K/LW/gd9OYGbYnAtnfeKNTu+dGv0GvPCddZDqVcbtzbDFIIEQcQHs42HOWPycTTiC5V0sdvzxzpLmmjomMI7lQ==", "signatures": [{"sig": "MEUCIQDwcujmSuElqwQKB8qDWK+OsItfsBdUDXkEWoo2corb/AIgRu7LVHOa5StJ+3MckUuvHjHbOLTRJ7MJrRM+2Ki4+8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/DCRA9TVsSAnZWagAAWoMP/1n9F1pX4viGtuBN3oDj\negcfHnHlOrNlqEpIDI1gDSrE5GLPbk0+JQSP6sMk+Z+J2JAqy960/hskTbyG\n2ESbUC2hb5yQu7Bf+STcWUcyhQQEXfhYcAw0YRFkTMfKlvAkgoqKLAC3oqp6\n8bkUneqi/mbxzO3Q6oORXui90BzXFhrcotFGcLoD7vheEU3ZgNTNV5daxM0l\nCsOz+YuPblk4/vnX311VYoZ++hesbHZeUa5RkG2OTUK1Dt0C2OydalJhslCz\nE2ehqI4cEiWUoDuXrTYQrWm1kmYq1gNpsEd52zOi8iqkEAEmDzfnVbEf23Ak\ndyJtJeZ+npd6ckmOV+5Aip/ocHBMjmQkVdZs/Y4mymBV9NEBbcviIyP+mhn1\nZavOwyKlXLFdRX0vRo02V1CDySPNS615X3tzs9kssgqD2JmZCxaIJrg8uCSc\nqgzRXUxewNfMolYs2AkuEBzkFOkVcWtEULWMqh0KekCIjJVzqdyCNPct51fo\nMIh3LSL/eXmSboSl5hV4YNp60ps9AlbChtIAiwNPI9HHJhAHz+yuOBPNKVWS\nGgjXujxvEws9aTSSlcU3w+yc7vfmneGk5+W64ZjuC4hoGbkIIUIqA9bsZI7s\nfmFp9gE5Ba0FN7NIJpDDcaBncSAGBKze49Ztfkq8ICFZk978ZEpnZMyYcNPV\nP6Yw\r\n=1WJ3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-0": {"name": "@webassemblyjs/utf8", "version": "1.7.0-0", "dist": {"shasum": "d41a349553f8b775cf405ceaca788980640659bd", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.0-0.tgz", "fileCount": 11, "integrity": "sha512-lyAXl1rABUcYYqKPSBwBEonaSoclBX8OOi4oeI3iJ6EykB1kB8b9OHi3JFi99H8dwxROntxH7kTbiixdiVKIng==", "signatures": [{"sig": "MEUCIQDKtNSAtF4aCFbGf5SrGbodV9HqtRWsCq0Hts2Gy+wT7AIgbDIVt3PTeR5Gg4AZZifuZp3UTlvAxZOadqPjN8jlRfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzldCRA9TVsSAnZWagAAxp0P/iQiDXLwFIO4gBCCElFW\nLuNaa+6pNUQb3FDUwZmkfLt8z41hX+wcDhchziEXkweNsOlcdbQe09YgIbqt\nBxfGDk39g+ruOIBrzcWPkWipN/d6GxAD91ltmbDck2UvUX4ehEC91vI+iYcC\nyBQN/idF+sgbZ7P3drlzy3H4qDXuCtY5ImZZVf9oqKkFHj8tzF4+O8AaAbpS\nUV2xjZIH/H5fI0WRqhGswx+6eFv5TxH/pExaESsLPCcAAHipgb+5suoteINF\nkkZ2PdPylMYv74f2jY89kc5rI4Z6BGHAq3ur9bySsd+Oc98kQC1G2Nr5liU6\njhH0TsCvbUhFayHSZ8vSVw1uaOUntmcdXO1qRHn0P9TrzouYFRWeCst/jDJi\n3AWCBpyywupgvIq1HuurggCc3tmYviUvYHyyqjU3gThW7Qk8IITIDrPnfl4i\nPP/mFE1rnAU2GvYLk9gysNUwLHjAIXmy5Ile9icLPNXu7YmiMIAWrE6vDJQE\nCIGbaZES6CWWKp3Pg9B7DPs2Dfn3OcC/8avO130RgAEnpnNdOCGmxpD4rRm/\n2BK82wubZ9H+7Qr9bgr68iGjk4aEWYHTQ+HkVsfgdd+oN6TeT4eKQFGUhIKP\n+fkb7urVP9qcjWg6FpaaQuTivg7+aKQO+1glTlm8byujNv3wtzH48paHzOst\n7Lpy\r\n=Sxjw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1-0": {"name": "@webassemblyjs/utf8", "version": "1.7.1-0", "dist": {"shasum": "b7fc088bc4fc1029e0c29d227aa9836c2dd749df", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.1-0.tgz", "fileCount": 11, "integrity": "sha512-PY+YoMW2bP/58+Z5+icgrAUWJs5hwwKoLCrSAFqJfQ9PaKwJLbdcbr2F036csJV/EUF02tBCOSyglV9OkiVdkg==", "signatures": [{"sig": "MEYCIQDLSo79mUD31r6TldYEdvxP+pArAAuZz9y0CRSo6z7kBQIhAKCph+5Aepk7glRRetensCmssP2JKglqfnpPJqMtmRhd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzv7CRA9TVsSAnZWagAArTMP/3b7Vtg6OQD1HpWWqc0b\nejvc4yrbVNJ8mnsxvGKzR7lLKmbACDDPv24Xbb5x/Y5VktSCba0KYzWofFfH\npJ5IQPX/q0niZS/EwJ9gtIDP/4i32eYd1Ops9R0DtD9N7QfNwaeWTolQjcz4\ntzZL9opTXmREVFjrQIgVI7PVzQ9s7u1FqLTxFKFf/WGsbPaeSEzrthn4JFII\n3KCFTUvqwXm1VLhyM6R/gDH+uPZNIJlOhY1EWcPyl0GrlQMqrlrCim03BIlW\nD69stUJc4i2tg6Q70Hlb2P906iiFkrS+dneCLETRuPB2EoTKJnJou6fr09Z9\n3090vw9oD9YuSVcTc5F/I2TomJbNkDz5ehOvbJNdvdhDdRj83GQahXfGTgiy\ntQmSu3ttd4d98czV7xC8526bxjV7Pz4oRavVDyYf9F+jFsWN/FeaFkvDby3y\nULJYsMrpenrVutQvU3KkpcJVANWIUXOqpmw1BtZiUBhTuYYE7xrvsurHj87m\nEaH8DQOrnSzmk13Y5mfJTxsthbnpprkQh/49SaZVeavlv6oLM6wjwcgYyAea\nWjqae4wH9zmnYFp0xm0jj5dM1Q3VM7Xb/mwNoGAr502Uv/xsr2n63RtJx6HF\nQh5AweNAXHP8jCdP42/JVLTf27Z+6pcUrrrdaanuD64aTGxoBx1ALtEFxEDo\n+cs1\r\n=qKZy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "@webassemblyjs/utf8", "version": "1.6.1", "dist": {"shasum": "446f1cf28c3e32d80b7ffe41f84bc03c9cd07850", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.6.1.tgz", "fileCount": 8, "integrity": "sha512-zqxoxcUZouf7rEuhwfjD81tXqoKb0SKC73mB3JZs8W603emDcyy4By38v0h45FchuPUNv5NKz6+IW8MBtfW0nQ==", "signatures": [{"sig": "MEUCIHqCfaSa9aGj0X66wW4fq42rVHAVRhnmZFhRYW89lKoxAiEA1YuJk+DP9YVkEE0rUTVUdn4G87fFkLvic3tEBjqWuvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0WoCRA9TVsSAnZWagAAzOQP/Ayyi2bCf87azWwF92Hl\nX8rMNZByu4wQFoZEZFNkWCNete522Vis/0TnOA4gCOcJDNm524DIfzXDiE8X\ns92/5GfDjCqe1xDcbL1egvSijozraCuhrF9RHP9c3FFnmsQt0V9WxEVAYftp\nlzEg3OWKTBf37HvqdxZd3SWB8en7slfJdUhIEBg0C5ryZKnM+NI6SHZHmdn2\nmZxDHwjNgIXsMgScKkkabQPWhgKjvoSqTb2PMQ9ZjhVhGIxmw3P+EeE4Xrwo\nkYUyNXhBjfmJti8z2SfCfCICQ813HkqswmRoI0ALpENqAq9RP3M6Q8usaVHg\njfO+oe0ve0+mzmUTJGgl6URnPpgMAXNY6u1/BlwUehHu/1hCisd7prcUx3J8\nTGEgJhzpuoO4WGPHjQLyziGjDgkLVcN3xT7YnO0zB+TYUcRK5lk95RIbn6mN\n2+qoH8JipO8A5jmOl3a2Jeul9Rx3aHXhJmDQiJwsSD+y5jZHt1gXZEiGufoT\n+ETfZ74NLHlu5ECmOGWqf/7iPZHu8CT4aCpgOoWevOnDkNqWdf9cSesfa6W6\n7Mx+ymbrcuu6ZcOYyLEtHkecgPwNT8YVDnHVZagVLLd9eOsZoJkBuxXhLBxJ\nMsCy5bg2iHFU4EIo4SbMsHPtG4EG5ahKSYOLg3hc8CoUmCz1/7ifPHeK++TZ\nOooJ\r\n=cVUE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-1": {"name": "@webassemblyjs/utf8", "version": "1.7.0-1", "dist": {"shasum": "d57ce83bbecf47fe7b6596f770c6f1af6ec32799", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.0-1.tgz", "fileCount": 11, "integrity": "sha512-TtnnmcaeesIKl1mMb+gGkFlY1GtDG5FCDbJAupRrd7+cXUHqvV6c3wtqY1dau/gCYgJFdnLfFdWlvphr2WGRtw==", "signatures": [{"sig": "MEUCIAY2MJ4gXBc+g1mRWkbfULxALNPec8taG5O8i0uU7O3UAiEAioapmIg72N8C3U3Lc+xtMiKsgFnxV1/Zck0ba0R+j74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1XkCRA9TVsSAnZWagAArDAP+gIS2ownMELUhNuiqAuf\nXZoc9hGdLJYS5yaFkURzQPtVWyOMJv6xIzfXsBHnQI2ShRXhKxy8rHNLgqHX\nPxiN2v55XamA4z/+vfkaXfYNyvg3U7sPrF6dWi/TwUfVT6oI2nthZ5OIRQKL\nDvAUAgDvRlMzzSaSr5/we4gL4CVp9qaloSOMqe/vvGV5WRATj8MBG2IIcfFY\nIl5SA8lgPQbq/VCdwvM4XGf7BoU25Cz3OZBoZ/RnLQ28KO8+itNOtSWyF6dq\nMnB2165eilp6Z+fg4I+gHtsYw04iUul+xqM4q8Lwr+gwawr33M8ieo1TRUpO\nm7TaY+PPb7zFaWEgyLyfoOHVLVBkFNKgi5CCaShKmBbCeB/Vzry+BqyUj5vN\nkUm1rFhKsokbAEY5FPk6tPtZLL7lVqbAuibG5KNg7e/Zf7Yn41mhHxxzj60V\nyD9d3ErOz2jlmkZ3+4Z+8QnvWZrxnnI0eR5BfyJDb3oolzFCqWaQYlHAyG0b\n0WvkxKq4JLoW5qud3YwBPQgrPY8MI9NEixq9BdClK/2qSa45ve0YYCM/iDat\nbSTStLMmwKWK4saziIsUBafkH+tZ5m3jRB3zxml5UU8/WF1+YCFnEVfHHe+G\n3490/NRFuW/lBblwrls83vsY1tk1aDyA9CReM+KwzAOBimSjvYCP+dRly+og\nbzgx\r\n=KCip\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-2": {"name": "@webassemblyjs/utf8", "version": "1.7.0-2", "dist": {"shasum": "86e708c2258a07ec69163007ff7d6f36d203db64", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.0-2.tgz", "fileCount": 11, "integrity": "sha512-HGn+kvFitN9Ju8SDvpB8qNyY2HhuCwnlSAauVKHmgCr5EXooVxuPtuJwN4ZrwD43StmkYE1NMZi2SkOn1/OBOQ==", "signatures": [{"sig": "MEQCIGZt0udNDFjgcYjr7z9P5/Lzjryrt/6BIEAXVRYSrA27AiB92oDIqhiUbbv7Xv6oNLgdGZxWhl6VkL3euSfQfyfFbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1l2CRA9TVsSAnZWagAAelsQAKLlJXpVZbtqMpDlPdC5\nK3b1ZfxJNfhfe4zov4Wmd760ZkoQIXtgORjomGzgl6/zkDABdz+liXqHA/Ol\ntJdajJyPZve4+yDYqoEy7ad+jF+AIvpJTSGOYvBodEayCFZP9rmaZRl/xi2m\n+8GKkzkM5qctkl5tFrizwaUfZde2vHi6r4Xiee0mtJZbbLn3rXnMtOH/6kEa\nS2xQwnSMdMs+vEX12FEppFXAAIMi+sNc77Z6l5b8KuSg4EdXVYHZArtIvSva\nGsGKuUYhYko4KCUdZnxR8vrirXAe5MscL2+NGodVO/dQL+4sYweiA6Bz6e8S\nMeIHx6iuU0pPKK48FANDfos97wOaq6xdWYbYDLIBj1X8Z9GiK9NoBlM8HVQm\nDSxlXu41BiPaVsXC9YDgOgvI5aGJSicSVBgTrMgUCE3gHzXW20g8zoo9Yncr\n1TlaSpJJnMDTz00tt/jDknYxONPKq2OZnytAl3zvXf1bt/G8YK2naau07slp\nmUHkVcBrIC1uip2RIo6ACxmCsqPNxxSqG23WvGL1eprhWbHhTlz9lzbZYtHW\ngnzd1wUZz6QUku4h2LVaPBDMjPMdisfjMm3ub2eHkHYVmj29MN4T9UnX3sms\ngT7KuhhTrj9JHO4atjc4ycxzuNsz48Qbc1+lxaHaS6NS3dckrAewu9NMafcM\na5fC\r\n=1J5Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0-3": {"name": "@webassemblyjs/utf8", "version": "1.7.0-3", "dist": {"shasum": "1fcf92ac8403d54a7a83295d4f6b35ca721a04aa", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.0-3.tgz", "fileCount": 11, "integrity": "sha512-VThB1pBqSd2j7ASH7cilR+P4mNguWg2EHE7e0+dpMa+JzGO0JbMGrA9Xe0wBbrgZLet25ucclT3xoXrw54Hy4w==", "signatures": [{"sig": "MEUCIQDeaAeMRhatzAWto4a8nVf/F0S/1ICI+EC1sC4ziKB2ogIgLPLO4o1PFbOsgONZg4XftPqv53VsjJZ7ad0VZ2Lm7LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT48lCRA9TVsSAnZWagAAESAP/RxDCNFdb7SLqQBE1JDI\n0jcZo7699XwRio6k4bRhYSc+v+wRKgIt64+hbP4mYXn8Ud0cK8vif/LQkafg\na/01/jMWatUov5z4uUhDgD6TSnMK2goZ9ZrPF5YmVghukDOrw00SLqXvEKKO\nJiit8h7rP5stn9f75TTVyc/88DgVfgtVfeXVbBZxrCRxNSZKlybbCb6Obc2H\nWPFBLq095iZKFYMtNtG+eB5loP//pb1JFoNGDoL6lBNvcVv0iVA4AhwwgYm+\nU00VxeVVxY1KHSVTmX09ysKe394Nr+58yehWg6CAb315+F8Y5GL8E7lL+hR/\nPe11QFUZRlH9Y1AGIzNO6SyR42dk55wIWguGOCX5OnJflsfQ4k1gvSQ15uUw\n9nQi5YwCFpSYUWrkSihYlTpADmwWF/FF2JEYw+Ynmt1YidV+HhkFSRQjA1nV\nLTx0p+MPFgjORHkzrim1GaW+RL1X9TRnIKEB2aHqmzcvi6HXfEEFzelFgl2n\nLUzoaT0OcblDRw9m6gglaeKt4GNvbflOm414LQ1GLjWtQs6PDrK5Z+FsqEaD\nFFW/LmR2E4EiYptOTg+haNjkVQzgQGGXyydM3/Ur8+tSht9V1z3Q6FMu1euU\nRkvg/oGXqBFrgKv1Mee6vzyG8hwaNGfau7zGZijkALnRxilNF7qjuf1468Pc\nma0K\r\n=9jen\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0": {"name": "@webassemblyjs/utf8", "version": "1.7.0", "dist": {"shasum": "f5c5e63c67aab356b7bedd7ecde7c01152afeaf0", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.0.tgz", "fileCount": 11, "integrity": "sha512-FV/VUX2IKA6RTWAo6jtlrP2U9KmbgS7UyAt5KDXDpyucqjX2qtZ1eTE5Z+lzxL3nniT3REMRg9c9sDtZlxQ8KA==", "signatures": [{"sig": "MEYCIQCYQQjPOBPxeWAE/3xUM1XLkJH7anXXYOm/RFl7H2V7sgIhAOhDE0aKlrAJhVhQ8kLPl5QT04+frlQ5Htdgf0oY2B5j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULW9CRA9TVsSAnZWagAA8l4P/iDl6W7AJbBi3t2BnW1u\nSSik/v3qocVBaX16h4aoE1OEGSYB4CpmUD1Q3Rc7oS+tQD/dZ6PWTpKZ6zLL\nIU1P/QbIADMN95HGIjwh6nzlwMMATKFHli7KJYRsHDylKHnniuxGVbKRVAwG\nqxzJirc2n7s1IXJMAVijtPeFOj2rynQsz+fXyjuxe1sduJNYGdmj20u2AKU0\n/5xkyVAlR2v7eHzlXgw8bBUtOMxtkJA/YGw63fb5i6hAI8H6YLK4wf3bPVS0\nYwogFwtnf2k0mju/vmRmfOS6GR/w4rnLo1vzmaXE0hJ3VMdTNTs/rHd0bCT1\nkSpiryszlX1oxSddHBUudQrdpkbX78ZIawlzrJUInlJpx75fFqkQP/KG8WwD\nzXlhNfCG+Qpgx8quzX0hnRnmzHIPXHtIKsMWFhNIgGNxnMCihVvUo03/9l4g\ngAGUZ0sFsI1Kf7eWmOYOzkbJyqCYqxJK1Yy8x9vu6VI1DO0UsHCxLJj2dM3k\ng9horMDXTBd9s2O6SlK/lfg7D14g9Dxwf13Xrio3sGR9BeU8CT5myFXajdvd\nANUjpKWk5CsdJAnUfcewuhi8W+Dl/MGo4bgUhamqbLyax34OnLEZqgTdur35\nFSd5QcDXj8u/WbG1zm1kWKs9i4MWx9Lr+8o14Xt1g/Z5P6/lnTMWs40jTR44\nKvob\r\n=fWb8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.1": {"name": "@webassemblyjs/utf8", "version": "1.7.1", "dist": {"shasum": "dd4b77775a07ac9d5c1a743ec3ea025c875b27f5", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.1.tgz", "fileCount": 11, "integrity": "sha512-v7JL0Ekq6LvOanqxEUz5OPE/No8hV1xqXpn/KlfyY1jAnFjS4NiqB9qYjIs7zOXRJPQkdr8L4uQjo2xWHUJ+Gw==", "signatures": [{"sig": "MEQCID1JPfcXnsdgWap/KeIHHSJQB2apN4SfdybsLUW7i2SbAiA5r8OEgt0TF4yXXSi4BL9dyzPniEWqmAndKNfeZsTdNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULzvCRA9TVsSAnZWagAAx5kP/1d3h9hFHAcvlbB0Zteg\nRrpa9dnn6w0doRdFymvVedIWuem1Jx/ZOl3zeHNBbyzy6pYieZ0Iu8oS2sCW\nfVfD2srkDtydNWSHG0gxrTzSMggj1BcWzif0J7XMfO4S6vBtJ7vdEVWwafy5\n26Vdo7OQDlZtg7e9PxdwiCKiLNj5Ic50AKpZr0QXm3gEv27lX3OjbneUrdhs\nRf214DSuoAhcc5dPJ2VZpEg/5oz3hoo8wxldq0zPO2EWar2239Bng6bPYbsi\nHlqWrLHLuKJF0ct4grmgNP3bVRPk8BX0zmVPJK0A3ycTpireHoM2xOzGT68A\nYS8eGuwSDye1bILqdK17YZOtdrQyWVmD6ceVlHcOTh4DG4hlbViWswIquc7m\ndVO6WTiov5vIFj6cvpStxFhr4T2ZH1ohksIStIhmP1ZpmrZs9Nu9R0GU10av\nJoI+9neLXO/1Bqf0heGY1jA96c0X7j32HlNpl1wBgz+F5sIGSdZkvv3s2wdD\nutkLqWURpxWJcYziMPVL66wffs6hXCLU71xopFYuiYK7HHYYRa6PdJUtlzh4\n0kTbPRY0QD26uOtL9FkIVi1pFItoo7fap1+5cAVykfq0fMxLVfvk++1f/qu5\n4w3ctvWbT5RRUz3MhS+byJIuEaM20aWXolwrHLtqHxOiOx0UonoZMDUc2XWR\noFSM\r\n=HtZr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-0": {"name": "@webassemblyjs/utf8", "version": "1.7.2-0", "dist": {"shasum": "969dd1b45de442c8b9999d6b86c9560d85fe43df", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.2-0.tgz", "fileCount": 11, "integrity": "sha512-TtvgbfYbENEp7Mi7hJKNCfhFrZ1HBwLniF0002MV7ICqBtupfvZFhI7bxDQs3gNn7LZ1AB1QYevN37eA2J+XNQ==", "signatures": [{"sig": "MEYCIQD8MTevt0cZmmk1on19bXanl4sAu/k9YhgvsHx9X2lR8gIhAJYKuI1JCav8s72oysqwUWWfhZifJD5oVzDhaDGFBbtA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNPrCRA9TVsSAnZWagAA/vIP+QFT4eWziAJClZJKR3IS\nwRiNk0d6p2NNj8lExsrUnb/ZyQMS6kq+KoQ5jn+9LjRqhbjrVCbxdd74QE+d\nuuT+5iFIP5JdTf9xK7R419zcOlZPuCNjliAWLKPnCQyU1sd+/fquK04ECVQW\nBAY/BdA/wm1JUQEKoQXu9p4a6Eqx0RxlW7T/f56ArlfxKL1NNCO4Dj+HpsAz\nAfann19egL8wKJrRHgR99fBQyWGO85OLpI91LW84jp1SiQ5pzISJpjCeu86r\n6A9oYHk15PRAaCOLVwxy3BxXZBP/MphkHykClk8Zc0gZ2w1iSDSa3RCXj0EW\nK6NZCmsIT8NWjBpqxi9dtj6mQs71n7+IdovbNGeSxNHAWIIPUuwBR9+r7L92\nLqQKr/cICSqH8Z6zqvnlHqx94AXK4RaEPL/WL5ZjalTlpHWy1I3I28BfQdXb\nbuR8Ull0aI0VDzXucPCRaeu3cEx0qRXF9XCAFitb2PodrKYNdylM++yESvIa\ncMV2wVGX2yUF8TkuNc34guizb4/DL/Fwpi6vKII8TDBrJsaxWQ96ScS6pNcV\nUxYvRnYG2N9L9fC2y7gAiF2Sqw1arxRf9lah8cDqLSN1L1tvvLl6aHV3kJTn\nl0oFvoYxdqkeXfvtnz4BHaQ3FpvRzjDQ8NuBkf2Ff/sJY8hBsAOeTi3iysFO\nsqWU\r\n=SUEf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2-1": {"name": "@webassemblyjs/utf8", "version": "1.7.2-1", "dist": {"shasum": "18d9d1a08d45b88e5c2394bef40ad2fe57ba2cec", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.2-1.tgz", "fileCount": 11, "integrity": "sha512-BuD8J5eW9h1WdGvQVAXetWsJC64aoBAfPeA+btgvc/XrhH6vPVVZ89un8q9xjgx1SZYsvzIwYsk3ncFl4H3Z2A==", "signatures": [{"sig": "MEUCIFToJM0eTXYwkGPDhlNtBEKRn48sAxtDkavTPwrolmnuAiEAkKE9pNjAe9sahnjM71p2dX2vJmqTPAcKTe8DX8eU8rU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNdjCRA9TVsSAnZWagAAGCgP/jEJibraAAtFyo2Ke8J+\nMo1NM3EQlbnqlFFNoxHWGSNZvIxmOXZNOFuK82Qbytk+uHipZEeTPecnFzXf\nau5NOKDVJ6/v7joAvqRLoC1auX2Jv5YacZwqqI/r9hn8UvO59GwUrfi4UdjN\nRt1bAcAB1ad19pXm4bcUUkUmQJDSzQ4z3BkUWMwKOvbRAYm47P8373l8Y81Q\nA0RDQAK8yaPihxPlWZVqpowPkOlbH4XQtcm04dr/6b7NVzx6os5I/tWFLMKM\nCpkBhjCf00QKX6SmBZPvgxNFWlkrqgsqAUJdngKk84BKGm2etlKmj4t3O9tm\nDpYPzaAen2L1OuihItieFJb9OTBzMLDyNzHorI6XIg5SR7uW6cLwdoFwjqhJ\nKLVPm/qZhVMO8vpiWR133/JXG9xz8UYPsZ0p8nYJISPBR+azbBPLbx6IRy7E\nvSt0FOOgZSlRiQ7eo8FxmqxNZVhy2RvwYLEZgBEUGd9GzyzZ3/el3oFLbVTB\ngtaM/02n4UGJd6YoQFeQ1OCsqY+Q++I4weZTBl9Z9DdmELHB5EWH0FsKrMDp\n+bbzIMFeJahaXmi6pGgCpN0rE6B7uxt0pKyOaXYH1/sz9ZpslNZKZq/fxPwF\n6im7S1oloHvKRPHHPZ2waOIzNoPYRiBEwF2ppaJvkrTIx/bZIeBNV8OYnIeH\ni6fD\r\n=LPY0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.2": {"name": "@webassemblyjs/utf8", "version": "1.7.2", "dist": {"shasum": "bf000e86a43fcd2b910c56ad04e5b84c7e2eaa80", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.2.tgz", "fileCount": 11, "integrity": "sha512-nwfvJ1HmGjwGz3djhP+5PBT6SSh98NLOuPEFkgcdiPQzqvjHIoU3sb8xXkprtrAUp/fqVieLtZGJDdcOjKYCXg==", "signatures": [{"sig": "MEQCIGuk9nT/vJsuF4qSZbXrwqZwGq4ttW/DJ95LVdZAPCsEAiBIHH8P0/jUdOCfpCu/ibmzxDrUg6kCJNyUR0BtfyouMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmBCRA9TVsSAnZWagAAKykP/2I5GWVIceEnIoHeuK8B\ns65gvSjHFMf0efgCe3FMHXz4rx+IV/9GG/PSuDeQz6+78WsQm48991VH8Iym\nfrlZuiKibl0FStgrgb20aHT1M8+Iu+crO3hW7hObTh/6qk08Z1orXltrlty9\noia0CZ127kj1DayrzjrN9tujOE72HzRbBFtreXj01aUvUThPaCnrVl1bNSkV\n//KNfeQ4mM5hbU+d26S+z9Onler7A0BuNGLyeBilts+szxUczoawfDvwoZBO\ntsGCYfNXEEq5bud8Ef8gslUBUS1JLYgFRkdeuZhK+iducINFMgsv3Uc+5HKt\nPUuHbm5eRYzzi41bee+mEIiqv1Vr5dyK4Uuf14Ry2Orokf4MkwSXuniXHz64\nfQ+HwP1AzWDdENCQCzBTlc6AlHjQbWEcXa9ZERt3hqGtzYggg2cRkpBFwvyW\nP76FZwr2PMkCgq3gtDuakOmih3WyPSS7ClU4j+r8umppXpy23fqaksc+GIpZ\nf+11Ob5cvStnSV4uQMQ64wAbE0rtY0rRtfEuUHqt+ds0g/qR/CKFAzfutHtX\n1qY6EdilU1Zn1juTwTOzrJE3CRTma+A+jCkb6CEfRY4fK9jA/vKD/ZetR7/Q\nq2oSfjrG9fkp5s0SuMJ0EL3CjA3NBUQP4Q3lygx4pD6gYxIdnbRDZJHWLt3m\nWBcP\r\n=XC1J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.3": {"name": "@webassemblyjs/utf8", "version": "1.7.3", "dist": {"shasum": "ec748cc4d61bbe4d239b09e08db206fe75339a9d", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.3.tgz", "fileCount": 11, "integrity": "sha512-RFL+8T7qmjBDdvJFM8L/Ar9Ld2rpXkgvEpLDkwAKmdbvbvyfNG0T76Uu42J0phUxXAf28LUJMseeP3NtUbSRyA==", "signatures": [{"sig": "MEYCIQC00NAka5zUwxUWV570YG7h1ymK59SaELmHQdEubw9/9AIhAPwrjEkSZF+YxWWD+Qtzjkg+NYplWeTC0ZpqnvWGPeAF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2ECRA9TVsSAnZWagAAi8UQAIeZX22+Zv3abE3yI2XH\nUcw/TpYQI1oM6zFfVwWxT7SqNFcVYXkJcd9gs9CZOQK39TBbptSm3sza6Bod\nXZpY1RpY5b79tuCspl/Qqi0WbqKYhQQkLFsoIeq6y9V8fQQh46vaPou6kjGU\ngcYtroWjUlRqXhmGM/RCrFPjFR3bWVUvTSkVMtPZIMY8pbk4ZPVeUez3195A\nLncSwCIGExRfFA5BZgLI29we7KdWfs0bftb6CwAOoxQLMTUp31JqD4I7VDH1\n9zZK6I8te16AK/nyEhU00hzB+fVHn1lEVoSBPZx5U7tMTJkqqKSk1CC6mr/l\nLRfV8Z1w0CIEZHBIEShwzDqHX3RR9AKtsu/neqioGqkE1a+nRysUCK9R+qc4\n3VzWDwB3vrVkNuvwxKVie0gPQNabongrvxf+g9gjQXjApyx4L6bkEf5Dyvzp\nRnfr96d4KIXkKngt2P7Ey2OZxhD17fbRhY1YCTlVTOEbplLL+DP6zLG1/EyG\nw1ZRjUMu/btTjVa9ni96xdYUNhJzPk60orJagYPtCeoaVlxXtBSCjMV51X9S\nYui+n3Og9hEWaXov0HkSCvK1XzDpvU9pEmxVLxbRWJIhGFlQ9seIBOqWe1uV\noMky6ruaBGB48Npw6ibsF5yDI5j67CCsyYNyUD+8YwvQvOXM5jlfUISiCGzs\n8nRr\r\n=xgPM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.4": {"name": "@webassemblyjs/utf8", "version": "1.7.4", "dist": {"shasum": "666d62a359770c25647e3ecff0990e5b13fe43ed", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.4.tgz", "fileCount": 11, "integrity": "sha512-0QugLXbun7eflVk6IHgh7SQoNQ+TJpRFeZUTmfPfC8puOwQ7E4VfGpAftYtU9nRRfFQVrB7V0oKyLxkXb8A9DA==", "signatures": [{"sig": "MEUCIQDoLhAk32Qhqz9CFKnmU1RFzSDj9N4h1lQEwENY9pwrXAIgTrMfk7LAQr8d8HS8+Pag+r0v4pM4JDK5Kish8mdxvLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs75CRA9TVsSAnZWagAAfGkP/jiSlhHIRIqRnw3ZMHVc\nEVV2WzESuXPLL6NLYvpVWyRNXARp3+j69ijndoeByuKDC0Iuh3h3oeOutgwb\n6PV98CEhxHcdSgFLf0tco9phWPkvY028UQ9AI29A/kcG2MgIvrQtCFEpH0uO\nXxv2oVTDQFQuBDV2fhUhd/u6AtJf6475kGQuu7NcVot3q/4n8CZ9oDGJpZUe\n+W3IUOnLYiPb72FVhZdrl7uOddVTh4B+hbVIhiYcu5Pv5YpzIsDVHv4/hVtA\n+N/Ih5homHZlZdd+KEEgyM+oztMmU99GktWx4HOXMlb72mKjgpziE+APxuZE\niNa7V8f12b5P5q2+6jQwEPYS4ms4DcMdQy9GlF9eXUSw/uJobIo8zXL+P7Y7\nxIj342QiYFea8aW7QgR30pIeSZYQhOw4fFaur54c/BSWPOJ1qQiGaC/W9nuM\nuYvYCVIHc7S5pWXm73mvWbb4J0/4uTL098GpDH4j25vbzvgUfYgXc0Atc2BM\niZKcCqOwj6q6UWkIuY8Kn3NNOkvr9BYRWLFWnqCqNlJEStXZSgepGGKBYTrC\nH53T7xe7SIpzCWXvmW+YkPTliJg/vZBAocMHx+2WsfXE0i2adXh7ZsQqOBKL\n33OxHV1vOoZtYM6C4PwDUPPG732eJYR28TOyWxEHQkljijjsm8LR6AlPFOab\n6cw2\r\n=ZI/J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.5": {"name": "@webassemblyjs/utf8", "version": "1.7.5", "dist": {"shasum": "c9d90e6d6b345ac928a86dd2976ea2f3a0d133e1", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.5.tgz", "fileCount": 11, "integrity": "sha512-6koi9YB/I5Yo+rEHT6JbKq/IfXQ4lXaPq32yFEVYhBxBCJBj8fM3ALx883s0wZ/PueBDiRZ/rb6NNSg2BsUZ5w==", "signatures": [{"sig": "MEQCIH01nsMca6kEqAOAPZi01Gr09FtNEMgKIeAb1ttWrJTiAiAwY+UmGbYqCENdxlmzP+eUhiwQhN1/VepMNtZonh/e1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdY/4CRA9TVsSAnZWagAAKR8P/RndRjJDZ4zeT4U7Fntj\na9Qzh4cfpYvmoIRdhpJ6aJ8iYSwYiqHghzupFyXxl6Y8YCAqOXRJ/pVwHYcM\nySUrvPni6S4SlSOlES3WZgn9xx7a8CuzYZ6pVkbsO1ob1G5FG2V3Ow7tWuRD\nsYBO5jokvFtZgdQtHM+jKzVuDhdzhx0sw16EJWEpR5kVUpjkqmqMGvgpI+Ql\nFK2a9HSymStqbV39rAm5eZf8MG6rqQSjN6fxSOkH7Rzv4IlVMslxJdpIt0cW\nNn1MsHZAebUqTL4yJmmSkbYOndXUV3e5sGNmM2avnh67tvHmuGm4akRAlN9Z\n6u4URJsNA4ZQz6RcDE3iOdAFwBqZdrGapLiNQCzxr1yzNAAr0dOGCtHXqDTU\nKRqJZOoA8JQQr0Z15F+AEpp7IBKyKUXIpXQpFv1TGsrc838IZyi9SpRRxf8h\nD5Onz963HIM47WMdXi0xPSi4oclxnSAWIsrwndC3dKuA0eGPVHVVjbiIYnLu\n5psIoDroHRxgAgjYc/D7QMaHqmm+uIoj9IqnDhYpV+XIcVGb6i+7Ng6rmeJ/\nd+MwHY6OsDT5vTYcirV1ZuE6wUPc95ZBK+EXoxBxU/69szEhrbQwsbvZGz9U\nqoCkrcpZfUdI5KQH84wgWmp2zryxgHZNJ+Ortv0kt0r6GDyb6KF1j0J2rQ/d\n1+6P\r\n=U0a8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.6": {"name": "@webassemblyjs/utf8", "version": "1.7.6", "dist": {"shasum": "eb62c66f906af2be70de0302e29055d25188797d", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.6.tgz", "fileCount": 11, "integrity": "sha512-oId+tLxQ+AeDC34ELRYNSqJRaScB0TClUU6KQfpB8rNT6oelYlz8axsPhf6yPTg7PBJ/Z5WcXmUYiHEWgbbHJw==", "signatures": [{"sig": "MEYCIQCCVe9V2yilaGTVyc8nzRaspP3uZFVoDUtZ4+KAVQzxpAIhALouytPUWiZDYDilmsgvKzAH2oHXXz4FNYa93itTm/Tm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblnz0CRA9TVsSAnZWagAA+0wP/3mfMhyP/aPYxRsjo2Wl\nJbMQ9FxaEskhgoq3mOD4wpoWuHD1LhzX9TGKotHKeOzclUNwLHcxVSwlV9WN\nQ09V8EwKVVmtc39w3438eZoEzYnqGHfsUrJ7nB2Eg2qh9N/cBHk99/mkgF/k\nju7ZI23ickQ3PSDzsMrr7b7n59bJWWFJ0qwhF6xtt/mMdQ/0+J0uQR7B0iqi\nS+qP+tcOF+QfGYRLzxvfz/ZvhzfuvG9+MaZqF1ym2wbL+vPdwJtRrtHp0KeZ\nmkgxJ3uzvKqUWvIhbTPzW+Nymb9cxemW2x9ZXXyziUBBCdFvPdI2cFVN6eWo\nS0UDT+whLsILXP0D3rTwH2JUK7i6jMHpFpvmftuMNbAhLLxDtWb7GC40lE9S\nvhmv5lZTlWb1OkdAfC/dv/dsTMrDrFp1PcHYHPcQWWm7TIRD5ulcqkRBYV2x\nznEkoqCWowIcqIy9+d3Db+hN1wkt2eAPIHuqiCkphZvn1NCrEg6mn/pH2xnM\nXe4yREIjPRtAK6qqyZhIhkiqHObh3s+KaxTCMtxnYz9fjJ2DAx08jG3jEZsH\nwoBLl2kKu35KVRLyBR1AcmD/G5Q5DKi3abnDJNKo4ru6wnmpje94awSl4vh3\nAD/U9zneT5p0iMvZHoA9wXf1lQOi2jTzC7RNmeW9ShMvF4QNIG4iVyQ3JZz9\n+hvv\r\n=Jpny\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.7": {"name": "@webassemblyjs/utf8", "version": "1.7.7", "dist": {"shasum": "cbbbc2299192eb3ca297573ff1cfe227c966ba0e", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.7.tgz", "fileCount": 11, "integrity": "sha512-Zg/kh9xfeOHomU7Df7Tc+H5+yxA+t3tgbeARWcVbbTxkKFLsoekH/kqogZR94a7dVe//4y7EVMhmHo1Ai4AZ+A==", "signatures": [{"sig": "MEYCIQD/iFoUfs3qJiFeN/5oqOWOM4v7unQr0H4V/N5WhiepfwIhAL1dYuNqLqy1ap9KM8jLY+yC/DfTq5tZY968wyPiDnq1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeEFCRA9TVsSAnZWagAAKUUQAIZXWIPv7aVapxtGD0Gn\ndpAMw/0cYdcMWEhkYiwbMxjJttKiQoWtnJ5G8e2Vwr+myWLmHQT2yZ0U1SlN\nfdJfjwsGJPI+/GqlQq2R6UpGlDfoPAsGfYNraHh6amu91327ihhvHkSzQzjR\nGzKf/NdjUuHbJw5us0GEqHg7QXyOKBbo0sVxcRqaJ+WuRcMQrRJiEnWT8uhP\nNaIXlQkLpI4BQbNZjy8EAVZmpeR6BT+Zj58IfQCgOzwPHmFp9CRFlyiEJb4m\nI3OQ2CTdqRvvpoU5hiMsOx6rIRocsvOu7+AJtP/CvYQ1RpjX1RaOHphWiEqP\nN0x2xagVcOD8W5TN3NJF15MlHl9CScVlnW5WNzGXbMmKi1kFeUBl5C5bOyOl\nFPuafLh4oCcc7h7DyjemwcHSEMagO0XeLxpbwnF8S1IlbRxt3HhKacJ9oxxE\np8X0Mj5ZqEbGc4uzmZlweXJmTmUcee9RqlHTsgH4hemExWDcP5Dl0zOAA9jo\nxXESWaAEu5yAFnPNQV7G0XObAkwoG+9HmBXLrauzUlFkeFyzAkGo54FAkJw0\nBGzyNx45LmvWLMaxTRtm0eT/MLtz8MJc/zRSYv/Jsu4hRqZxHYMjrLhKEfVD\ni1NX9c5qVgBXJXU7krpMDMXKZT3nFnggBrRgJM/6+h0uMyb3QlVVN65DhyIy\nSKEJ\r\n=0rIn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.8": {"name": "@webassemblyjs/utf8", "version": "1.7.8", "dist": {"shasum": "2b489d5cf43e0aebb93d8e2d792aff9879c61f05", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.8.tgz", "fileCount": 12, "integrity": "sha512-9X+f0VV+xNXW2ujfIRSXBJENGE6Qh7bNVKqu3yDjTFB3ar3nsThsGBBKdTG58aXOm2iUH6v28VIf88ymPXODHA==", "signatures": [{"sig": "MEYCIQCrMW6vRXnu7xvA/C1dUKfoPRBgRXMevVjPwA5kQIyNOAIhAOjJOhcPf7OjYF9jF9aBA2BOJ/x+ShPkvEAeKMSio3Tw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ+6CRA9TVsSAnZWagAANIMP/3x5nywCVeURbGP/lLbR\nfmceAoZDcDXl0VcPchS8yjpTYy1ZL8nHgjGBNViNhskU9vFX4MQlcDgoXXsa\nVkdpIvGGhUkj3DctkPDPOr7b6juo4q0yj+mp2tISjYfEqgIU1yoGI/DdTJma\nkGP6q9dOTjYIUVERbMAA2hzaFjhhXr8Gfe5n+MfmuafbtgbHw6V4mRTu5+UP\nDD7lTrSKm6suQo6eOcxQ+0hXYhUl7W129rBTL6CiE0cY3+wF69LaKZo645/x\nwD2sxdKP0X9XJYCXijEBqUTQjVxz8DN3i2XCyZ+u1B1ALbkbOrp33u/3qfBN\n/DdKZFdYUoSGDN28pDm94M+4LUxd8MubpZKATUq+Arji17ZqtBqjc5pP6nLa\nJdQOQvKx3lewQMzsvPm/RsjTkApRBSyTqmvAISNjA2fxvYKKa25VsXAJiYeB\nWlphd/73+wYcCQDuwmpyb7vaR6ufylDVC0hPRchWM0c+O/dRVqj7qdrI0jly\n3xpuOk8pnsAHvl1zpTIez+k6Nvz8nwuQx5GiqKMV0VeEwieiJBoV1RRIVy9S\n8l4kLwzoUuDJsyL2bNtlO6WhsbRe95oPiPsp/dFuQVv8NrASeBVd/u6THsWz\njznO1Vm4geTSHjjJB690CAuuOt98h2gsGqnY5g5hNrBP6Zimu1iHc4OYDTSh\nkUyQ\r\n=yAXM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.9": {"name": "@webassemblyjs/utf8", "version": "1.7.9", "dist": {"shasum": "5e59bfbdb17375d9a723b40c11024a60742c0af9", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.9.tgz", "fileCount": 12, "integrity": "sha512-ZYOFhEiiyWl7I64cxcimX0CzdOg5CrJKPxVZarPy0boij0HG8p+m3vdDQoh37Th+9UenbTgJl5OVpj43R3z3aQ==", "signatures": [{"sig": "MEUCIDyDcgCSNgFW9BfkBOaXIJ7v1yLGl/y2+PUWk/MsGIWqAiEAr+5Fw29u+Qgy0g3mm3VTephZjowEKfL/M97EqbTzzKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgICRA9TVsSAnZWagAAm8cP/jhdTVN9kEduyJjBkV0u\nXai2D6SO+jmiDHoZvi5tooKo2AtURqC6jeXQ2MqhrI8TyyusspaX8r8SuL1U\nncihrnBtDwUVhVC+y+i1VAoxWBh6407GzVQe5X/M7s3ZzlY7hs4oFZFA+pyx\nTxFgsjJ+/WDZ8+jM832OQYQv8hX0zKT9V+sfanCT3FmI8NnlNQYhP7T5/ZLP\n+DrJJZBYz/Vd5gylk/D4vT8czmLsE8jg1h9jp4fIH+0RdcRa6gxzddr1JeyO\n02xuhBfQDYNHMSA1hpde4P1l9TgRC3bpo+A7AWEi1AHzdS5Zq0ZiNi6jMi8L\nSt9AvlUvqQOtapf8Z7C7zwZ7FTppMPySN9AwLKbB0TMAlW09oMpXcDr/t0D2\nhSSv21d3SxGnP6gOphnq//BqGPBXhbmU0+o+dd010C/ex/EtQJjF8F36QmKA\nGCKUXIGa4MUm8qP3qBkBahphna+cPTkf8Df67cQ7HjvNeNCXIDhA6Z3z2YQp\nOQffUTcKA4uDYfjIUwC11zCsazW9hulpNo6rLSZ9wWJLBFTQfHW5y1oDzFKN\nEWXcWOTc4/Ev0dgIrGR1fdmSLhJ2epoak2dF7JXdUHKMRw2FqNPQeFlaxtSd\nI/nEEXLdl8scSMJCBIm6lERb7BVQGcXmOmBrA2HCXzOMfINGxWULz2oznqZe\nCBwW\r\n=4aJq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.10": {"name": "@webassemblyjs/utf8", "version": "1.7.10", "dist": {"shasum": "b6728f5b6f50364abc155be029f9670e6685605a", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.10.tgz", "fileCount": 12, "integrity": "sha512-Ng6Pxv6siyZp635xCSnH3mKmIFgqWPCcGdoo0GBYgyGdxu7cUj4agV7Uu1a8REP66UYUFXJLudeGgd4RvuJAnQ==", "signatures": [{"sig": "MEUCIG6IAs8PWaI4qEjz25p4tPpA21+MwvS6u16ijgXY3rRXAiEArbqRzIgnUEyDSQQ+iGBFkEvCv8ittmaKuEM+dCbnRJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11250, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzs5aCRA9TVsSAnZWagAAoU8P/0s0KoG46O6UObj+IclB\nT6fr9bIdk5lAV2lT/gVf6C96jax5A4Tn7Iz5E7ywcbJpiQdhvdoOBWQpD8xJ\nXH4SGX1NnIdDqIHi1H5/ufqEt9wLYxIhw0Brf18eeBm9uuE+aePGQuZEnHcP\nPtNWlNy+IGga/ns6616BySGY7oWfiQEcAayGEqin3Mc7GzfrC6u/Ij5a3jvB\nQOOt9GBieFPBK9K6xkVfg+uCtLl8XTo2j8LH3yEiDENTGL6e5aW50Hdr7JaY\nyEiEcJ/9x71JqRdqHHsuJILOL9Rc93n9/VAhVeFBMXMmv2LYCnSdh5IehJJm\nVzPYnvj6QeAMF4hhLA/WUV9DouDbRWpLaURwXOHytKQOurmbvWxRb/vtgi8Z\n3EPAXV7+jyoGaHuxj+cNXPDTOqs/4tQsb+GAIDP5O5RUtn50M9nhQQIYdCpC\nPl+7OfDb6jiE32qQFNuG5PWp+fP6nvaHYmwusZ4k+mdd3yiUAhJD4k4qzzGp\nMyXE2X4n2T2wm+gh6ECxatIy5AxbAVEfjIZdSRZVhujxjl3R8TquPz0FppXZ\naUme/etJnF+FhxdAu8RpbFsiu/t5UUcih1B1ZN0C5JJuKEHqRqbHfkWDYiwx\nDp06ZuILMniAsfx4SUqD35aTs1IdB+4ELLMRpwWa7Xh9OlJxxwgAx0bqn5Oz\n4Qei\r\n=MQxc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.11": {"name": "@webassemblyjs/utf8", "version": "1.7.11", "dist": {"shasum": "06d7218ea9fdc94a6793aa92208160db3d26ee82", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.7.11.tgz", "fileCount": 12, "integrity": "sha512-C6GFkc7aErQIAH+BMrIdVSmW+6HSe20wg57HEC1uqJP8E/xpMjXqQUxkQw07MhNDSDcGpxI9G5JSNOQCqJk4sA==", "signatures": [{"sig": "MEQCIGd0OQtNP0uEMd8QnbsY0mE0ODffKMh5k7l8aVmH/Ui/AiA+8Bf3hxHaXqx4+tGmIYtmRYlodnKTN/t3cfzSBaFp9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxVCRA9TVsSAnZWagAATckP/12r2ob2gP804qC3bTpb\n5pzZp0IEeDe9746l0OY8XQAZ35hUbD3rYlN/2rDVgNBlDFSEkMrdrjtQkt3w\nwqlRADlQ0LWYnMrt6oGJnCBvlNl29QyFAksRaRdn9BysIbdmF3lrrRu6R/mM\n2awH7Qt4ieUHnTSTV/7E7WqLZBgdxMJYQVUu4VCA6xmlZlSmCKXd5WFjmxgZ\nYaAXf/8b7WGhlJmbnqhkIiKKa64OE2klOe/EKihqMFL4vE5iuCg9f9rE/oMq\noJLFUXbFdLvwnvplRf5TO2Y/5V9SLCEIbtGsfDz0qkHHS1PXou+YEpS6cYbX\nA+U7fkikJJXl2e/Cf/0cNDygHb0WOg4UOoXJf/eUYH8JCZhtEVPB584clVAF\nT/2v5EpX+Bwg+u6qmaJL2DgCXbotUkyb/MZSaCUBq2JakKvjToFoo+r0kWlZ\nn1rkJOu4r3pLN8LaYgvrPMdAfHFcA/LRoRp4G/qWgXKIIT6x7bYoMOlDaBrG\n2dvHhgmn++D6E4saYO/5U2keL6JHMGAY+e1IpqM6r3A2UDH+a4VQr1CejOxq\nn3c9IwTlCGriWZ1yrYbRXigRjivcJpxjwdX8ysEjc7kAoRCCKOzgxiBApFYx\nCJ2yjRMrA9oRc6Hg+sZR9TH0QPsGuqVpJnOGdryrD8h8NN9c2qHUHusCCj46\nSqPK\r\n=5/ej\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.0": {"name": "@webassemblyjs/utf8", "version": "1.8.0", "dist": {"shasum": "8eaf6bbc380c2a78f367c9b5db15341339b4c364", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.0.tgz", "fileCount": 12, "integrity": "sha512-Ug9jgJWk9nCLn9Tc/bKxdwCLIk3reofT1M53M8x2FZ1E1wfJRlU+46t8eGii1dz8nBwMXZV5JlZAUVfT9e1Mew==", "signatures": [{"sig": "MEQCIBeDGo99WWL+mMZf9j6/BbL+LoVlayq58joc7hfmnEAiAiABc0+gRV3Qf2pvHawnLEAx8a11n04JF0tqod0TQqD9fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2YhCRA9TVsSAnZWagAAWGMP/3lIgEj5pF/VoNw91ROc\nFvXiKpi/s/Bh78slOEvtN8rkPobLSqFQZD4QY/lSB1Q2Ed4lIFPv+/sOgRZu\n12oa6XQdYYfu7Hk3adI0qKvQphi8mDPB1aSyP3dM0IQ0Ca0d1LQ63yhXUopk\nzavrEY0Qz2XjOS78V+Ss7Xtuf00yYhSNx7ZGIWWXCOWqs3+rhES191tkMaib\ndyXtcLbL6b8dzrHgBz32G2e7kTBNQXiTV81Z+xLapl+3stfX7mPiBgDkhV8d\n2JcQeSiJh9q3SGh9A0WsKnIIQMLuhHx6thLo09XSVxMJARpwiKsfOJ+3Wb6v\nDA43iP3IGXmT45fp68aFr8syhCdsYTs0BU0LpKF/zXUOwST+S3lVNY+EzEiC\ncOtt+Ca0L0+4m8RByxrUuBARbTzLwdBlmgedKXW2LB1cLLc7P5JB7sE7tlax\nqNGOmpCJEp7zaBBcu4xGNaHI/UqaTyxKNMWEBiYX5ZiIkMTr4MHAt9XEvYny\n1UHUa7j+7fl91Tmuv1bwpYYCkTM2Px510lfZwwUN/dFlPSHfyt2xl3uzAcHg\n0vMqR9Wg36L1jcdF8jPTVuqf7wTXWO+4sZrK1AqKsLyJ2a6qUJDefXpajrMd\nvxw9+tGthqtwyqn5+kSbv6JYXC51pBiH6SZ42NXKwy+OuEL1rJELOBdjafF9\neJFh\r\n=E/0s\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.1": {"name": "@webassemblyjs/utf8", "version": "1.8.1", "dist": {"shasum": "65335c75576f2a318999695dd6ef80374c781075", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.1.tgz", "fileCount": 12, "integrity": "sha512-I5QQEb5ajQ99ARiyDrVQM/2nvyFFG0tF1TX2Ql7dOjw5GRT6P4FF+gRk7OeAUtI1CLyffUNWbIvpJz13crGSxw==", "signatures": [{"sig": "MEUCIQCmmDXpSLOBamH1ZCiVoea0vj/eAp9bF4OQ+8cHcWb7WwIgcL2MpWhXE8hDWu5YmAckui6Rl9EUZvSneoZe49VFBOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZf7CRA9TVsSAnZWagAAxf0QAIQSIEt30iGCJZX9R9I7\nDRL48koahendNw3YoXDmg5toXqg4p6aumwLWuEVNQcHwNZfsv6rNhAN0K/VU\nR/FNemjveTJwFBG6o4DHJdWFTSRXql5N32YKy0jiBbEKa+mZy0kZ0H1PAlsO\nWuinTvJPsjYU7wLB5bISEKy5XymWmU/HGoFoqqJs8oucgJoNtrw19zfPxmw5\nVcAYp3Kcqa3gOFNg6/umruqNCSZ9tbqBaAYcGB6Ms51lgKhk/UEJboseafss\nCtemXAJSUNRzqSe33S8pYEUcSFEM5ScDgczQSGMYs8NughXcnpcljQTkPtn1\nGpqgOqI0PrOFz38WMqOwGf+dqbpb4x1npFH6W/ZDnpuKktRsRYCLbXmRqG4F\nZ5arYtQPMsAe6cFSISt4m1fmqsZJiRlTfoti/x8n0daWsWuwaPr49+LpKeBF\nvZCEAiV7pUIfjVtlWGGS/UnHPpxstcL3v3wOzG2Oyh1GJ2d57Mc5s5Eqmmj+\n//Gl8QR7lPoNSuYeZXSvO5jDF+4vKhnTydWPIyWDeG2HLauTrLT0cqC9+uYf\n04029qYbOxRqLVbSs2NCvKH8DReNkhsc9KeYni50m1Fd07dp3CAFw7GHrlbA\nSWD5/3iFrO9lS/ax4h+1DNW6P2fAL698/WwzSVatQgO1K5r2DfYgMzIgEY8H\n03Yy\r\n=ewFA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.2": {"name": "@webassemblyjs/utf8", "version": "1.8.2", "dist": {"shasum": "6b262c1cba9052a8fd01a63eea50265ccd7f3b16", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.2.tgz", "fileCount": 12, "integrity": "sha512-fP2Q4igo9/R82xeVra+zIQOjnmknSiAhykg//fz7c1UjghzoutQtldcbKOaL0+0j31RRFMDHgrUL+12RQExOYg==", "signatures": [{"sig": "MEUCIQCqgVbNeAYCGMgp8YCMcl2t9/96Yuzvph9KK1igIAtF2AIgMYGdPFdpAYmxpweI+DyUjDORapNN6fPxRmG8wRltdQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWSCRA9TVsSAnZWagAADHoP/1SvHxu00/mHKDS0bnWF\n/FxD8CCbqa9+QcgWHPb9dfVenlU63Rwn4psen1BjT1mi5zf7rhXWiByqCplS\nfqPYl4e1kTbEJhx4hQG73UCwUw5l200ys8inVu+0GSMIX5kf26CggGgJUyle\naAo7+dPEiBq+HkdLn8DTiMoxiMxmLpbCqREodnufsV3hsSYu1ncJlwbwQsQF\n+gqiR6xIewr5kHaKxxhBMdTZ6CKixHvdWSSVqTTPhwGqOoWTflhKg1V4kKRC\nrYix1i3aK+hqKEYV5Fox7SonulWPw6cFRxbytj5JQdI8xAZ9OVwkowjHGwaY\nNgU7Pxbsd2QuhH1rOVASRMmNROBEhRAKPTBeAjn8+BJiSkGNp6UxfnXfvWoo\nqECeTvhndzEn6Mct77+9B02eYXOUvyAg6815GEo6royaT9h8o65+6IN2MRmD\ngtQt1K1SDKPc7Lk9M6erg7nly/VYE7VfX5W0m1Y1xO6ygdfuh07y0c9BUbhu\nfskkzFEuAh0Ju1rQaKD/V80ny6ku6Bvt9OfBbR7sc9OBHGwT66zKcv5atSoK\nInWCucD/BDXGXmyjaH+OTUI7VbKLoryq4WhuVvL9dv9EX3gQZCUJO36TFLVt\nBENAbjbX9W9g6bO8MjXSxIEsr0YT861W3k01illxty/SNXP8Pt1sAaH1tp9y\nHxPQ\r\n=4CBs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.3": {"name": "@webassemblyjs/utf8", "version": "1.8.3", "dist": {"shasum": "75712db52cfdda868731569ddfe11046f1f1e7a2", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.3.tgz", "fileCount": 12, "integrity": "sha512-Wv/WH9Zo5h5ZMyfCNpUrjFsLZ3X1amdfEuwdb7MLdG3cPAjRS6yc6ElULlpjLiiBTuzvmLhr3ENsuGyJ3wyCgg==", "signatures": [{"sig": "MEYCIQCrlZo6bMwtVCs24DSy91xHSoB2qzaxaVwEWsM27QpNqwIhAKe0+wPvjpxvNEIJxXMBg58Kmw3WLKGjVIpmgYBaHiEd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam6yCRA9TVsSAnZWagAAfBQP/RavSdkYcDsIu6tNpwq3\nqdf0S2M5sI7vGsO8IEpND+n2nnAR5ASrA8uIxPpF8aOw9XOJglpy6ROCRDYQ\ntPnOfGXg1CpcE1dLIBIbvx7eYPRuTBypB3C7qu8RXz3ns1x13k32pxGkEpgw\n2IuCV64zJglOgq6M+ATCOeUsYVLEUbbJH/pyLds1wGoeBF8vX7WNNwxoIxR8\nDAKc3xzkQ7MK3uNz/PBbvcwbHpfzbasEyb8/SxYiaT3ZqxfKEA/3GNy7YGCv\nU8a8ohcuqnb6o2/9ZLA3QgaBsfGIq7Wb7RvKdtn9+9YIufYoJ+qtjDBXEnaC\n0S3/snt4wzrYcCQtGYEab8s69gXCnAXF0XtmpkEVd74/N5KO2GwyEV8yxiCq\nJeI8Ll17ntbluEfsP5zlUL60YtPtkrQoYmQJoolQ6gPnBLlWEgYGxZNrv1l5\n5vkvEhaAhkkb10nDjhMzbVWcBsKnLdxMGzfRP7N37+YJa2/pEp8FyO0qvRbR\njV1WIls8Eu+vvBhrzLFODzUEHF/QwNP4msTwcP5FSm5zXWlHr3W5CDR4bKdd\nJHzIqOw3Crt17lJccclCzAmVlcwcH+eX7Bmud/1S7WD2j6rTBfVsOXBzoKY2\nJ1LUeGNX0GUWc2xJEoO6rnEBua6Wkc5EMedAlKEa7qrqf3N8WwpuNRT30okx\nchxa\r\n=pQpV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.4": {"name": "@webassemblyjs/utf8", "version": "1.8.4", "dist": {"shasum": "8abd8078853ac52fd0259bd42a37eb87081c0e90", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.4.tgz", "fileCount": 12, "integrity": "sha512-f50OQaWB555SAx95YBtA8TxwgeIQisticYryYRrPKqzTzjQI+z5R8S5t9OEybtN6vyJ3iL61YTBptRR+WOxPcw==", "signatures": [{"sig": "MEYCIQCGciqRFkTa0Lo66z27Fv8Dm9wtkzG8JCHmK8AfJDLOGwIhANcHbC2kU21/PIwEaT8xf/+zYowHbOgiM69+uYzCkH8J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDxzCRA9TVsSAnZWagAAQGEP/R0UyIx5WsfixTLgqTxH\nccM2FHWwVC4KeVcg26YLLAW7U5Of/chNO3A20rCs+wZkdTJM5HVb7vzi+XHx\nW5OyjbJpVlos2ObRXJKQBQYE6yzHioGHjWSlZwHWb+WJdoNrvNm/3t1YSpzH\nl9bollG8nI7ljJEkXyVj0VXqrIRdRl+iadgb/lH2iKo1Hdy0Jkhb7fzmzKjc\nOBek69Z6F2Kw5PEwkm1O+yU48Py84FOPx27YsFVRl/vk21eqoSoWgi/XCCxZ\ndfnmRmusJyWmL1HU3oloYDI1mR2dFJVRuYCelc+5v1Gax8ddig2+A7++teoU\ngTWQd8yHsK/TXMSd51q8Wji+DKvsX/LySnyF6hv+1HyrPU6WUSv/seILn6vA\n/NO+JqxvwmPzZ8P0EPfZv0f2X4APMdrrJ/Q8rHr3DNnimlvdSyKxQ4F9XdFU\nVqtNvCdorTD4mWgjg+H3MPDbEn15FtyP2fxZQZUfrNYZ2nCOJVmkxYKJ5dKc\nSbpoeX1uuARsHNP1gvf2jzsjQVPLI4XDi5VqmILqbLX2HQ1EJn2yfwYAzK3l\nHuH0I8S4Q5F0vkGIzbdPqCNkjPha3NaFEFNC0BflqQ0txwlSfwcsYdoSz13J\nF9jJBolhnCyJQFa09UAtH+99NHIkg92jH5oVB3AbwDRVd4tngKDYBqBRyhgY\nMHPB\r\n=Uqdu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.5": {"name": "@webassemblyjs/utf8", "version": "1.8.5", "dist": {"shasum": "a8bf3b5d8ffe986c7c1e373ccbdc2a0915f0cedc", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.8.5.tgz", "fileCount": 12, "integrity": "sha512-U7zgftmQriw37tfD934UNInokz6yTmn29inT2cAetAsaU9YeVCveWEwhKL1Mg4yS7q//NGdzy79nlXh3bT8Kjw==", "signatures": [{"sig": "MEYCIQD87u78Q2t7Cqt9LQwkyhreb3U9M0+fQl/oEtFBol0B6wIhAPvIkPlAaZ8H2jmUPytG9oBRyABxWhWwRvjBXGsIX34v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnUpCRA9TVsSAnZWagAAmHIQAJJguFmUrAsVfafPShLX\nMORMSpIeacnDK7OQZBfAB4zLOGxWgeMh4uVNG5oguvWbTndlGxP4Px3pWC5j\n3dqcdWdB2mKH3MZfXOimU4dCkfOfDzTlrREs0pjdYw+OBnkS9FyM2txeGSfc\nIWAj3W5ozTBCEqis9EKCZudtPFjVWtiOXzEBQN6661sN0Zcux28aR4kx+NQZ\nCwz0oMjhowzEJqJNLQzBXV6hsSPIoPbJyt0EjYK8bsAZ0lGYccTZSWGzjwTC\nUivfD0o5JOZSwIYgRf8pw4+59vQQZDbKgcGSj1bVn5AyI0PVKWqWGO5uakut\n5KGBSJzxh9KYs0DTenR/cPLyT/mfb/ap133slmbvgl07VfxpEgAabZgmjnG8\ncvBsRMmwr5B0Lol7pVxIeL7WDk3zdgxU/FyHjqySAauLHVkHOsayg/uoEyGV\nxYiRQspMtbOdjw58u0IZPU4TRIw5ouY5CNAfXbMiR29XVZvnVuMytUn+rj2n\nWUNt2PxINGewrvpLCfs4QUlEHzldkk34MHnVs11rX0VV/O2YUT2QahHiBxDL\nGsRPtsDvDkOPCEHl8xD6d1bfo0/fIydyJdG/l+u+cad3o2MU2eXyXtuKLruG\nGJ7avyysYcONtmhNbmkf/6at5yyrUxFrIkEVMQ1WFI1aL+q0UhdFAg44Kmm7\nOAD6\r\n=vNj6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.0": {"name": "@webassemblyjs/utf8", "version": "1.9.0", "dist": {"shasum": "04d33b636f78e6a6813227e82402f7637b6229ab", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.9.0.tgz", "fileCount": 12, "integrity": "sha512-GZbQlWtopBTP0u7cHrEx+73yZKrQoBMpwkGEIqlacljhXCkVM1kMQge/Mf+csMJAjEdSwhOyLAS0AoR3AG5P8w==", "signatures": [{"sig": "MEYCIQCZeJgB8Q9/M/SibGsRet7zlq2S0+A4xSF6ixyVGZcOtgIhAOvyCVyGs8+WuqfrIUw+WkvF2Ty/W5Mu/eALdFCiGre1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgebCRA9TVsSAnZWagAAOCgP/0MSTI98dcnpOrS+y4u3\nAUYuKJTnvuWe9joCtT9XKC3ALRjtj4mHuo0qnMTbD8vFvPdSWXbFSszMDl4w\nH5EPqun6IKStZh1a4T+BLzLXs6BcV+dDuybgzZYmY7IpN4Z1nb/1fXJ/4Kq9\nS6IM51DJFv5iV1AL97c4uOjDAIzSzvLfIURP2cfmQsb8XXctIavuduiyxid0\nG+pxcjcB4HXm0qBcWQXoE3YBk8huWUkqDoLJ2v7FXRI/5KVjWWdtac3Pl0Ir\nIxQ27tBeF32MGvHdV+rKflR37BcdOrkUOQMS6J3cChi05Aj9XUj8Exbp/Mnw\nSrh8me2g2tPSZUFUYfzjkkNLVSfk1wq6pvkQ5zd+jDHgQ7QP9WGxLy16v9fN\nkKBUTTmJRkJWw+bQ+SuDIHjv94shtfgXMr0J6DhGcgU2QZLVE21Qa9SENO++\nmtIgXdRE9nQ11+WNsD+WVFmb2Fyjqj/xLt/LtgnkKQAt6kA2SiAq4/Zk1XSV\ncPhtuWovDpmSh/ZL2KtKU9Kmkjscin12GixNTv+Ci/q8YYb1vzZTWFz5st9J\nRSTte6JlQjj/gqpscxAFM7s+hWA04XrHgDjhX3+usx5/jRbFpRhzVkw/1ZU/\n9uu63TJL2OQi6yS9MpLWmA7IMcWH3g8KAWDfoqGV8ExWGijNe2OdS1tiQ+4D\nUufp\r\n=uKbO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.1": {"name": "@webassemblyjs/utf8", "version": "1.9.1", "dist": {"shasum": "d02d9daab85cda3211e43caf31dca74c260a73b0", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.9.1.tgz", "fileCount": 12, "integrity": "sha512-llkYtppagjCodFjo0alWOUhAkfOiQPQDIc5oA6C9sFAXz7vC9QhZf/f8ijQIX+A9ToM3c9Pq85X0EX7nx9gVhg==", "signatures": [{"sig": "MEUCIQDmCkT2EkghJnPrpxLWP7hlcp2DzZDTmo6POKdqY+iUZwIgGHB9luaRjGxjoPza/ng2r8nVhJ+0kue4H4I6wTk7B+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNocCRA9TVsSAnZWagAArUEP/jWRWWUtkCMRja/SPqN3\nEDTPBDttEumiDyHFI5rD6Y42I7hjrX6AoXbKXoT54/OojDKDbDGUtMEiLzX/\nVY4Z9qn7b5T6wGocsQ36o2eLsdZ7IqeQQfphjFORGwKhbuAUrfUupjWsdo8j\nzgB2JZ9XEh3RAB/Jog5Nw719NwUeZVEJtksGOLnEbPR40cXXfTQ0GCjTx7Gv\n+LvYUtoN37LCIvM7DePm47B4wil12MIxHY1mljGetiZGHoCatWI0ra7xya26\nJTbyd4Oe5N7fZqpPBLa7yaGMN/Bl3Ksi76/ODcH7UGtbHvF8xu8UM2IIo0J7\njXhfDYwkPB4c3nRg58yp6kCMNsJDuZqXzF+9wQbieRG9YSXN0W23JvutculC\nsWWxzGAnSlMpTnlBbhG4w0FDt0+Y93v/VO9pmI7BeawR7GWAsNavcuIylsCl\n2aMybOXtW7svSOkw2H2LEXpkH4pQ2wlXGe5zQ6YGfxdMXsm2sL609pjSnjSS\nBuE8bsXLxkSxTe28TnY6Hj81Ef8YC5AUzA4fnu8kaRx3Mw4eJce6hyr+Hnz5\nQF404fr2uTOW1VnSo6bydvELfhhRalXngx+yB7huyYthEX4aDbsEP9u9YD9D\nDbXHKDn1YOrNqYqq9cUQRDQSRDSJ5AL82fBVcMumpVuzzA+b3R/OqTCO6OYo\nmIBC\r\n=RKC3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.0": {"name": "@webassemblyjs/utf8", "version": "1.10.0", "dist": {"shasum": "ba1cccea8b3960f223e7bc5e48cd87984d2a1b27", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.10.0.tgz", "fileCount": 12, "integrity": "sha512-+KzAEXhOkLitpeFrwkR7BwLpFxzUP4jUHiFTHgwtxgxOl8btwkZ6HGpN7eOM29huC1STfbwey7A6hZhbmTopig==", "signatures": [{"sig": "MEUCIQD3uX9tK1uY2c97Pv8fjE8o5WnAcB4lAYpcC33q0YpAcAIgPu5r5HqsvQl2wMLAkgOEWN+pZd6XnA5IvfX9AMQY0Ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zQvCRA9TVsSAnZWagAAc24P/RhKoJJL45jC3Z74XFYL\nVdLSVl5YRIZysLQUa6Obkry0PmUQOAWEfuyGFbP1Na6WSq2+1ZAQ0xBMZSGO\nZRvnq8CPt/o/xFpTWOBzi2NdB21M12PxYYWPBpVK+5PLKsXt6X4TiU6DG+cE\nHTjX00LfWsXA6KmdOjBCpd3LmjhCyzwXMEPzlvUCU0o9SSaGwGM/hdwZB1fl\ni/42+F5BY8RdqLu0AWbIm4HmkCwtSuKZoLE/9EugZnD9tQxjclqmjI2MiQP1\n861m7gmreZNFOKBnlH3RpCf2lDCl/K0zJyHmy77cAAVPjaABHoq1Im/JbF9g\neWxlYErzIO93p29nl0rLo4d6ivcYuT5XsiHrApXPYybaLeqm2U3WfgCIKPv0\nrNWVaPLjN8ROAOWhd2s5i/c1lnmZOxs8ugdEBS14T/m7wrm5H6u5wLtiB8Vz\n+gHdMghRJc9Kg0Lebw3piGrBgYKsLksn4Sb74pCyVzfQvhMHClJdXITQyTev\ng9LIBAN0M7VLRXz7qhYc10LzjGzcRYarrfJQkdWErZ+4ir3DANihwX2evUtH\nazu2jBPWAto77VJrueBiUYco9vSGBm2EdhhzgeZCdJkKARY/0+BxVqLbgdYg\nkRKHDsiK+ZRNQHjtF69QXNVUaRFfB8QUXl5znjYlW4gsi6qmlkhFcLJl8HSK\nl0bh\r\n=qUUv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.1": {"name": "@webassemblyjs/utf8", "version": "1.10.1", "dist": {"shasum": "dabff65e2b036428b1c9ad81c6e7d23299514615", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.10.1.tgz", "fileCount": 12, "integrity": "sha512-K+T+rvdFdrQk1Ylx8Ju9nso0bNgwSl7wkniK/G0kuc9YTf3oq3sfeLJjKtdM2Fne54N2ht4b2/MkTrhcdQyhbg==", "signatures": [{"sig": "MEQCIGP6AllMyfAGOc2HJ45s8h7ghWbcx4DDbP+nO1Pvp06LAiADbA/67JCwiYDulB7fpD0BOlXuSI17dYkza8l/61ZxYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11250, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqXCRA9TVsSAnZWagAAalsP/iU03PLqLJFAZFFYShqP\nq3g6hp7gGM4FwX7uh5SsIN+AFziqZKZDn8H3+FltfgOxnijEwX8GI47ANX8r\ngMCBMK02dl0UfSh7FimE0WXVfRrxVvwP3eedCzIY7ztpH2O0vdN/r/Xgd5Vh\n3daqG5pkk1qhgoYWRF/mAw+YG/37+zrtd4pASU5CpKCF4mfyL1hH9Y+ITGvZ\nKWIzlNXaoGXsDk4zAPw4qoiE0YJ8zrARlAnTT8ssmFh2WHV5OY7r5HEHd+3L\nVADynCaqP0lAkfpIGcTDAcmWeoT0RZnjN/8E1zRJt9UO2Aq59/LQihpUTpx3\nJMiDQTO+AO/lpA6DjuS/OLZlvwXPWt1MKM6MKWWEGr2wuds5EWifwtlsQ6vH\n6ew7IOnY3iYMbdzcdLjAMKzImj+iCMI4v1i98p6a3JF88+CP0cTH6rU6TW7X\nY7YhubouVPPfrd10D1J8SvJjl6aLkXe6K2t01D/F9FCOzk8V73pnsUAZfTSR\n5jdJVHxHTjQmjFklOX7a3QqO00gERbLUY1EXiQmciSS5dxgsNgK9YYZEDgPR\nehpAQpZsYjWHSQUOUN8XdY5BYXJCv35OPh9ztTj+5TPmOmhMVWWWzXrv2PIk\nIQWH5oWy86bm+kVJotT4znRgv52q6uliH0SO3Z6zX6C/ZnUDpVs2r+fxr14O\nvZfX\r\n=78BK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.0": {"name": "@webassemblyjs/utf8", "version": "1.11.0", "dist": {"shasum": "86e48f959cf49e0e5091f069a709b862f5a2cadf", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.0.tgz", "fileCount": 12, "integrity": "sha512-A/lclGxH6SpSLSyFowMzO/+aDEPU4hvEiooCMXQPcQFPPJaYcPQNKGOCLUySJsYJ4trbpr+Fs08n4jelkVTGVw==", "signatures": [{"sig": "MEYCIQC16GZoMwhhybu/BJPHmrepgogQgQgqlTo7IZMN1GaarwIhAOzDWvV7cTRK/Sc4OUjdxarbppn/nN4R4glft26H0M9A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907UCRA9TVsSAnZWagAAn90P/2BT+ti1g5qEqB7WCJJc\nnMW7Sk+dwwGWFTznMe3bDXn2i+sUnrCdl5IoKLBudBWkcSkr38FZ1McCIrDS\nEiIvzEQA6Gp2jPrDK6wRFcTcYMYBIsFB8UbYYwPjFoz5Mvr2+jPuUFeGhP3u\nlRbziR5J6Eg0uiLogAvKbS1InD8pt45jwMd7PL4oT4bOEJfwF1PewnpD5jOM\nAg0V5QlYVG9i55okyfCaLH1IB+XCIMg0onzfWE6GypmfqmMJtKJr4fw6q0qP\n7THhm3Cjdb2UszfReX/tuS9DuVmKBQBZcnRc+74gF52k9sRRHy/CE4L+AfBM\nho36fdKFi3rsVhnHiigN/8Zh1WJFnRoBjj+Ud3yL8tQnovyOT29wP9FAi2MS\nrDp5Iha4kwn7jouj/7lg4HrW4fdHNkY/KHWyon6hUy3nQc0DQJZxVHAPMg+g\nbQuLjDAghPVwGNT7wzTqFG4Ar+yq0a5gsNjbnbf+f3XCXUVZor16hNW5R2Uz\nmC/LAE7Jtfrxu38va6bPpfMae/zUSnPB/MEJMM6KxgJCt6IZ9UdicCTCvndH\ntVq/qNl+ehDL2Eq+mMIOYk5ICiHnSBVpaCGb2PDAJjWOJJL6f+lAgNr6GpTL\n6fyllTmdjzyNgOvkAcEDDqmjuAo+E9nkcVSaFVCUSsL2ZfVXqVXLNeLfipcp\n/z7r\r\n=g+pr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.1": {"name": "@webassemblyjs/utf8", "version": "1.11.1", "dist": {"shasum": "d1f8b764369e7c6e6bae350e854dec9a59f0a3ff", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.1.tgz", "fileCount": 12, "integrity": "sha512-9kqcxAEdMhiwQkHpkNiorZzqpGrodQQ2IGrHHxCy+Ozng0ofyMA0lTqiLkVs1uzTRejX+/O0EOT7KxqVPuXosQ==", "signatures": [{"sig": "MEUCICpZKi5CfX0P++aRIaMjUbtHmVBvxl4XfXGMAnygHDnNAiEApnBWM/KjXzEgI1t9KMUn3Rc5yrdXd0crMPagBq0olfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sEuCRA9TVsSAnZWagAA+DoP/2NPQPMH76yIhOXHOt52\nL9YLJZZFFhXI5jxU+wXh6Pufhs+X5Rl231fO7+iaYuFDhDPW9j8S5RwBKPWO\nwiOUS/qP2T306YJ35DOOCX5jcFkaQWdTqk/RfudjM+NxQ722jfbAdqeuW65o\ne/LajvzNLR8xJpHRUD5M1JZsQshTngqmpTR2EAoM5YBhv5p78AEzRYyjdes0\nYtPPXHrkB+wKIuOH1Im43HPsF03n7bLAZD/WxylnI4Uy/KB7IYwVV8qbgzg5\n1lqQCFYCRVywtiSUelPdBzd37pZbNSi6s0J9QFSYGy7oxCPAKLa50eInXje+\nV9nfzTBKYZnPiHLbOXeGnz4YUMgBMx0U/0RTulhUhyJBu3VKDwYMFeMGM+2J\nzXSvr7IGi7SQFeI0b3zqkyhlHx+tq6MCLuzL9/KdFSiHWty6T0S5DI0Ab4Bf\nddoDd0vOGOUQt4FJLJEcppgxQKd7YRBC7Y9umQ3fHqarTJsT/Auz5rZ6R2BB\nMRDAsL0rNdWEsfYZkXNiDpwb7IpugDNoS4VV7NR6WoBBa4rr472VmaOiZBYd\nKu3OXOunESMBYU8nHk0q7ovTFNXCmW4KyuWFTzu/m7tNkHJn1PG5xxx7KxNy\nIUxqtjptlDlx8Eup1gWJhR8cNIJ6AC3pJmYPjXNh1W2UQRm4UtgArwepiBet\n8C6C\r\n=muW2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.4": {"name": "@webassemblyjs/utf8", "version": "1.11.4", "dist": {"shasum": "4c99959b6e96084365c3288adbace60425f8ac97", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.4.tgz", "fileCount": 12, "integrity": "sha512-f5vdb2IHu4t1Vt68H8ONGNH2JVHtfNwCgpxiA7xyURhp6W0LGo6wEOqzmlenHuqCp00MEU+NHk+ZDd/YqEV7/Q==", "signatures": [{"sig": "MEYCIQCT4yKYp6Htyo3JvUpZK+fZOC3Rpdt34quHN9VBb2T4QgIhAOOjnjfCopHpfpe1rTAR7xxkPGJTtXrVkK9DeFdbI+4C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMnA/9ENwyP30RI6XtdioTZqYrAQOPseVge5NSQXYShYHaUpWcNkA2\r\nLCrurRs3eq+xk8alPTQt3ON2yFt4BEZh2srJpNU6NnEPwWnb48APw+k0ibXc\r\nxg5bvn2S0AcmxiOmkMyMufKNci+RCdASlVX/Kbx8u2zcPxXwHOXZDJ7OKFzb\r\n0EF1aKCjZrHPR0LZMPCUsnjoXQO/ydDCadngm05+y8dp1r+p7LPzkDCa5tP3\r\neBcGUY+B3tbsyWcCTGnzpVNyCMyLHvW7R0ScS+v3Ew29m2Jbxkj3lBHnXNDb\r\nHI33zT3kDBIk3A/G4/kRnDcbAVCTe8pD4R2Fout2UP1ElR52sTQ3FTfggrL1\r\nDmC4agHR7xhBOQnKiiZxAThbdEJVdOWp46czBSTMqrqEnM6eiFWqCwOzXGAm\r\nmCkaX1RKNY+OEe8xkA1QetEsrWcUPckeB/vaWz8SxG6qp4y+Yv8WPsS81FTF\r\n2ING53UkWPwF6T7XlKF6TB9M5l2Qe/YRbrywmONEJwTSnJJoRXEzIDFEby1c\r\n5O0KhPVYv3M/babw7COCy7fM3GOZb1pVd6eLoPsRsUOHnZGPLCWU4kG2D3qK\r\nEAIob7MSKTBu0Fm/0ihXq+gTKrxU6mKjl3c15ptWpObvR01HawkxyU2MZ1uK\r\nX7/WbSpHJszbbp1tdZ7cP6Oo2ZS0vqH+MBI=\r\n=yiBy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.5": {"name": "@webassemblyjs/utf8", "version": "1.11.5", "dist": {"shasum": "83bef94856e399f3740e8df9f63bc47a987eae1a", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.5.tgz", "fileCount": 8, "integrity": "sha512-Wi<PERSON>hulHKTZU5UPlRl53gHR8OxdGsSOxqfpqWeA2FmcwBMaoEdz6b2x2si3IwC9/fSPLfe8pBMRTHVMk5nlwnFQ==", "signatures": [{"sig": "MEQCIFChMpb8OsJaxivaQiu8uRaiKg83UexSU/FlpxaVNztAAiB6upJf6qrAEAwCNfzxV0ebNcu8DkMCNR2u2EN4xVtTAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO589ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtXg/+IbK2Exilnb3DN0heLBK9SnOvnrnTWlKwzaLrk0yTcAslyhe3\r\naKiU2icd0eMsIhooH9wCoMb3k5+7/dIaSIud4MHAhUSdrwUk6UvBBHWu4mEe\r\nhjGu0xvjv2QZ2Y4Ad2ITGVQYJ4HjaN2L4V4PBfCNazTRSbpzhEpWbr9uC/fS\r\nBM58LpgeDDi43Id/MCN4FVNrhhCQAYf+PFudMscJxZo15K7n0pTwBmZ3zIBJ\r\nFl8SCrXd3WpzxdvClDCFSzuOyiCkYLR7J5pSlhn8ubWxfiP+v2XZeNTLhsI3\r\nzkYjR+fPJkYD4bH/2sOKR/gxSZLLc/LgootN18B0C4sNyTbDjDpZiiy7PSao\r\nOoUm7gwGOHNzdGGhOFW5b9rIspk62pOn/FF5U3j6Pf8Vk1qigg0+sSez7R0U\r\n9M/nE+PrSVDxMQzzILX2ExI6rkHbZTMhZcxWmqMhW041MjeOQ84dx/Odhw9c\r\nzLcmCl556zoDzQxdQ1B7cCSacxpSDfI5qJHEawsiwNGUIWZz+hyQROFWaDbc\r\nuUk0UgP8T2mUqgyNpRgSIpOa0ZjRqocp867xT6KT7fGfTsYOSWRUp12OY+fC\r\nzbhKKZ6cNhTmJjbE8nUvXZSB7kme6EbO2GwqGcEePqXta54hyNfrlu1l+2OI\r\nB5SWZEkL+sBDlMsL+EkM4zlm7S7w1X+GpEw=\r\n=fWvh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.11.6": {"name": "@webassemblyjs/utf8", "version": "1.11.6", "dist": {"shasum": "90f8bc34c561595fe156603be7253cdbcd0fab5a", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.6.tgz", "fileCount": 8, "integrity": "sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==", "signatures": [{"sig": "MEYCIQD5VyvEV2GqY8XIGbxlD/9nf7nQz8z5uExfYYB2Ev7fMAIhAO+TEFOJVwGm5N0P1zWjSxY1xP5dVhLzMUHl1+E0HhJq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7311}}, "1.12.0": {"name": "@webassemblyjs/utf8", "version": "1.12.0", "dist": {"shasum": "a1e47ba09e9cb2c7aeaa07961d0c99b9988f30a8", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.12.0.tgz", "fileCount": 12, "integrity": "sha512-U+OUlBerGi7ptSg66CRPnxN7FNaMjxcbwIZIM/1eDk1CY3RspYe9uxzROrbNgJZErvd6k9rj9GeisBAyx/jOQg==", "signatures": [{"sig": "MEUCIQCkDs2AojIEjSA9KN8pc0gJ427cikPD7LcvlXaVbHIy9wIgDd89ajPyn5xuYc4JuzAHKpEU1qBtd7nrov1gBuhWkqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286}}, "1.12.1": {"name": "@webassemblyjs/utf8", "version": "1.12.1", "dist": {"shasum": "f7f9eaaf1fd0835007672b628907cf5ccf916ee7", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.12.1.tgz", "fileCount": 12, "integrity": "sha512-zcZvnAY3/M28Of012dksIfC26qZQJlj2PQCCvxqlsRJHOYtasp+OvK8nRcg11TKzAAv3ja7Y0NEBMKAjH6ljnw==", "signatures": [{"sig": "MEQCICqjFic62dfAy3NEbZEk6DYtxJp3DgLHacItuUxiO/YPAiBZsGCbjSRBJ1erUx6pFRfsp0q6cHP3DZbRBSkllv5pwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286}}, "1.13.0": {"name": "@webassemblyjs/utf8", "version": "1.13.0", "dist": {"shasum": "13b78a0bc8181d8eb6aded3729dcdb39ddcce702", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.0.tgz", "fileCount": 12, "integrity": "sha512-MqXx8BXSEBcp2/ssHe1rZuRvCWKqyCO3MZ2H+ecigOWSG0XB1F1CZeHp6C0e3NSwy7B/4uLS3Ti+gBOP6z4ptg==", "signatures": [{"sig": "MEYCIQC0kokDaXSt2nc2Q2LX7OrB4m/RejHF88PYBr0cltATSgIhAOFC0YW5/+jSvqzi2khdZffSz90dRp9ZX0qeJad1M7G2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286}}, "1.13.1": {"name": "@webassemblyjs/utf8", "version": "1.13.1", "dist": {"shasum": "58e85731bf2b8e10e514b567e87a74ee486d6916", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.1.tgz", "fileCount": 12, "integrity": "sha512-kv69vcSpibcV9YdfOV3KneQOBd07Hg9tMuSshB2FQnRZeAnty4wa2QG4DPdBa1R6+wNIW0wOWYRLMQV+A1zBFQ==", "signatures": [{"sig": "MEUCIQC/+1UkHmHpIz8L5PlSAUGnsG4pavCQExUaLck2OdqIqQIgaPedCQ8/8PQzH+mL/batoOx4SuJaIy9SiQRRuTG7j9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286}}, "1.13.2": {"name": "@webassemblyjs/utf8", "version": "1.13.2", "dist": {"shasum": "917a20e93f71ad5602966c2d685ae0c6c21f60f1", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz", "fileCount": 12, "integrity": "sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==", "signatures": [{"sig": "MEYCIQD241zCNoRyGKiXZ1EBXMhJAZy8Q/+4oayKusxmPfHnKQIhANUXEyPoCg/rVrvlXufLOEpFd7wQ1vIuUXUzGOSUYpDu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286}}, "1.14.0": {"name": "@webassemblyjs/utf8", "version": "1.14.0", "dist": {"shasum": "0bc68a17b1bd1a1f8536e32242370a2f9c7763c7", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.14.0.tgz", "fileCount": 12, "integrity": "sha512-ykSoIBLNrfAWb15/c5Ic+zTInZ7NH0IkpH+GAgahGoNL9JBcpVowQO31GuT48hB7cR0SM/CjLLlpGrMgZJuQ/A==", "signatures": [{"sig": "MEQCIAJa+A4kdo6SIFbVFQWjMCtWW7jDVwjWti8v97tOdb9LAiBJWfKSVREuW3XQ1vKhT6Gqn2lRaI2XgIkWSzQfg5axHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286}}, "1.14.1": {"name": "@webassemblyjs/utf8", "version": "1.14.1", "dist": {"integrity": "sha512-ppmr21h5laouMyJRgAG26qHm/bueeSuiOBcDh9TiyO5+XfGlzi5AkaysAD4JkmrqHtf/tvc/GZmc+5O+qWhakA==", "shasum": "d91637b904fb8de715b99e36c68269a9c57c7458", "tarball": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.14.1.tgz", "fileCount": 12, "unpackedSize": 12286, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXHiRYzRCoSpVl+jU21GS7QedJkxR37HTnl4owg2hFTAiEAipfUt8o4lyp6gYBfQACdNrPA3rUh3gHXqMTJWO1Zj6c="}]}}}, "modified": "2024-11-06T21:53:36.288Z"}