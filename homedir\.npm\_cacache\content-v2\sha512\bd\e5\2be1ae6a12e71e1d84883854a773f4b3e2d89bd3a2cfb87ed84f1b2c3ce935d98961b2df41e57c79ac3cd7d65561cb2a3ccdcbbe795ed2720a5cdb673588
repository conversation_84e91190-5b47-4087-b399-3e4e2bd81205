{"_id": "readable-stream", "_rev": "229-0bba4edba62f80a4f14e08c47efd2798", "name": "readable-stream", "dist-tags": {"1.1": "1.1.13", "streams2": "1.0.34", "backport-1.1.x": "1.1.14", "next": "3.0.0-rc.3", "legacy-2": "2.3.7", "legacy=2": "2.3.8", "legacy-3": "3.6.2", "latest": "4.7.0"}, "versions": {"0.0.1": {"name": "readable-stream", "version": "0.0.1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f3efb2ca59b6e7c615f14a2e935ffdd042a46845", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.0.1.tgz", "integrity": "sha512-vGUTkKrE7N6+NYCHdfBsy8S3G+uygl7fu6JFoNuw3lxgHQugQD+FQSGLpxS+Cub+5NkyOZMPB5fEvW0b/J5uig==", "signatures": [{"sig": "MEYCIQCFhXrWZmEkdBWP8N365wEiWJvCkNoq/j4rc/qq7moGogIhAOv3hP/SG+qt1UMZnG1z3mwn3psXEHD5kj+pwv92LlJX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "scripts": {"test": "tap test/*.js"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.0.2": {"name": "readable-stream", "version": "0.0.2", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "cce59fac644aac782792573201ebb346b257717f", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.0.2.tgz", "integrity": "sha512-0ysi9X2xocrwRZBI5ejmXszgc2gtNy+QTejRjqpkV7CIu+z1QavR0z9hOUtBM6fc2hdrGCKgXyzhP2a6t8tfig==", "signatures": [{"sig": "MEYCIQC4aNshow+8zuF5stnzbeeJkQxrKb2yrVErddRvDnHcXwIhAO+5JKgjjDT6B9RpKJTuoIZAJabztbxlYddzTVbbroYa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.1.61", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.0.3": {"name": "readable-stream", "version": "0.0.3", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f2fbbe635e3455bf1c7ea05c5e2ed8bf7eb8de62", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.0.3.tgz", "integrity": "sha512-QLdnghsrNYFuJJnx/Ple9CH3kQqke3CBo1PIvYHRjc7Pb++mB8mDnIBJC/lIrbfmbsXQq6UmuyqD+r3+fXICyA==", "signatures": [{"sig": "MEQCIHO4r5AndxIRyxJT2qmvpfELO9Qyr2J6RB/ZU/evJk4SAiB8h1qPhRLAq+hhTwVNvBOlj1wKFttss3myLJEfn9UrcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.1.63", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.0.4": {"name": "readable-stream", "version": "0.0.4", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "f32d76e3fb863344a548d79923007173665b3b8d", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.0.4.tgz", "integrity": "sha512-azrivNydKRYt7zwLV5wWUK7YzKTWs3q87xSmY6DlHapPrCvaT6ZrukvM5erV+yCSSPmZT8zkSdttOHQpWWm9zw==", "signatures": [{"sig": "MEYCIQCKE8+ccwdFf4I4hgT0DKxfAnx3/NmCa+cV4oGiE0rOaQIhAIs+u0rpBhVqfmeN2gHwwCoi+1Ld/40WZzQT0kBOF6wl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.1.68", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.1.0": {"name": "readable-stream", "version": "0.1.0", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "35476301be1584653b5a0101f288cd40b33cf39e", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.1.0.tgz", "integrity": "sha512-UeB7DWfR/wCBFVDwM91zaeGupMDjqjSDtMcTJMDAqlc/LxwtPpPCob0YSbNib0sIMdAb79p9Q5e+X3lLjtU/jQ==", "signatures": [{"sig": "MEUCIHjvCJJVqUSTWmHPZ5H+cKkBVV34ZHu/WFH/1e9PjqbkAiEAnXaVj9QA72VhAwXuMgHyHNcez/1ym3u6m+oxAeMsdRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.1.69", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.2.0": {"name": "readable-stream", "version": "0.2.0", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "c237bf773b27acf9472a576435a9cb9b71504e0d", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.2.0.tgz", "integrity": "sha512-COnYM+cZ1cj0+C7uNPjG0grKQXctqRiKItUUer/nMQt4AAQ9Aul75XuxVP40+ahBhpxhE6L48AOAjfc6q4XOJw==", "signatures": [{"sig": "MEUCIGmpofasgjaCUMgdWzv5HV71EkSwxU1lFf5E4VuZKwg6AiEA5W7JtQFyY0Nc6Ip5KFMk5qNgyf5kiys/HwyHrDAu0o4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.0", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.3.0": {"name": "readable-stream", "version": "0.3.0", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "9ae8f414672ded571ca65b5ce12e7f03e5ec4452", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.3.0.tgz", "integrity": "sha512-iiyqiRqxW2CCh5nNLF93BgOie0qbKlvVIYWw9maalN5og1/3oKMZUGsyk42KDo0RxqLMDORDHIW97yiCG+A7/Q==", "signatures": [{"sig": "MEUCIH2A4uuMKH0n0LM1QgceYPzyozwe4Ho0VBpIKZuubfm7AiEAmhN9ZdmEZ35HP7/K1ZmNHQOk6tTNXU3iDCq1LeD7Eos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.10", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "0.3.1": {"name": "readable-stream", "version": "0.3.1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@0.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "82476ce7bbc0ae58e1d5e11f17df86ba6d76c23f", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-0.3.1.tgz", "integrity": "sha512-d6ypllazkk1E9ldpvyOijeOuacNSsVeAaYwnkOzBjFB/nKthiE2o+WbXAkmKTNs32y8sU1EH2pvRGdtaX+hLaA==", "signatures": [{"sig": "MEQCIG0IpHR53Cnfltsz8dOz1GlLLWMNCsHhyXt2YoSWz9JmAiB+02cuau7MnN07xRHACITbYKYk3sCuGO+q3ikZqQuzog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.11", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.0": {"name": "readable-stream", "version": "1.0.0", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "805b14feb790400d17f314e337d12affcc3549bc", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.0.tgz", "integrity": "sha512-PZBV+VQizqaCvlwE1dbdeHZqRQE7cmO0bPKvnuY62YEurSEUjYrht9VB9qCeapkE1dxKcZ5q9acqcoU15iBoig==", "signatures": [{"sig": "MEUCIQCBRQieiCxDdaUSMf7WQNwUJneA0ZNICR8R5f4eNRtjVgIgLbAyfdblDs7l1V1vVPLLVI/f0jgHAKYewSJFC7ImO7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.14", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.1": {"name": "readable-stream", "version": "1.0.1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "04f99de4c0697c86ab65679330b0c9d167b2b9b3", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.1.tgz", "integrity": "sha512-cQsyCjDJQJe+NstXSBTLE1mpjIr3I8y7XgBtp5f69dalZItt8BdC0sKYAM7VcoTrkXHPY4Fw0CLOteY8A5j+FA==", "signatures": [{"sig": "MEYCIQDl3zuHY6Q0KgfaE2dTbf5QsGV6EuzN+r9LRfivmPSIqQIhAO9jzgtpcL+GoIkoMw7gleztOpyN5P06l/ObfZdJF0fy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.14", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.2": {"name": "readable-stream", "version": "1.0.2", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "213ce36864fc1f0d4e98e03b9eb92c64042299d4", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.2.tgz", "integrity": "sha512-El0AJ9aGxDbvoPzWx9rlD84bzmrhdoxytBbN4R4+fb9Wjx2UHdY9ghDTQPIFYw/eL7KwaKgyrTv2iH6IvCk3Ig==", "signatures": [{"sig": "MEUCIQDLSscP6CvTwPbrJV8lf0bn9V8kyvN7S0UGaDPu5GFucQIgPfM45IX9vHWcdS1g8nWDJe7EY0xg+kowI7AVfefcM8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.14", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.15": {"name": "readable-stream", "version": "1.0.15", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.15", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "a2c160237235951da985a1572d0a3af585e4be95", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.15.tgz", "integrity": "sha512-q3EGWOWCA5NpvneQbIN2UUQD1ENagLfUhnxnAW6YS4Xsl0ciKgtXfXnJs/UJ2lWwI76XRr1QX5U3KCX/XeJV6w==", "signatures": [{"sig": "MEUCIQCK0zNc24FydR6N9w6S16FDF7Ja/H1iiXTFg2tQDGLKfAIgb6P/lbLnt5PFDYDfmb7cneCtuBrQ6ig/AsNaYDlzvM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.7", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.17": {"name": "readable-stream", "version": "1.0.17", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.17", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "cbc295fdf394dfa1225d225d02e6b6d0f409fd4b", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.17.tgz", "integrity": "sha512-iuCuTSS+/4tLfpTiC36pfRhybzxwD02sxjdhpoImfU3RL2rMqnYFFw3GLjKSI+CYSJavIuTQiI3bq7LKb7O5Nw==", "signatures": [{"sig": "MEQCIEIGuDTcqiMaT+yLj9QQHHBZ1VKa5JQc16lxG66QnXKXAiBrbxbhG//xJj4JdOn8zqwlkJ1JSPS7X7+WpxmqVDHP8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.9", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.7": {"name": "readable-stream", "version": "1.1.7", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "9fac85f615c91d8123d6797a37bb6492f3400d34", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.7.tgz", "integrity": "sha512-+e1ulJloiuQ4ILvT2O9wFQWO2hQGcdyDhiXJ7bnOMeIEdIycqkA9jz614v3sOh2o7ucOGluo2ERYLdHFFvDb5A==", "signatures": [{"sig": "MEUCIAQhM9p/XhPSSYiU5BMPGnfuulTjz/il0tO9iQA1KCxuAiEAh+XeUC4XP5ASPzh0HGpIbYxUD6LN/Wnfmk2kCkK2G1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.9", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"debuglog": "0.0.2", "core-util-is": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6"}, "optionalDependencies": {"debuglog": "0.0.2"}}, "1.1.8": {"name": "readable-stream", "version": "1.1.8", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "28d42847e0feedfd2013931f37fb326fc4e25b7e", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.8.tgz", "integrity": "sha512-IGYT4DZIUZg2mBHJnh55TqaQw/EjHh4yGNFsxJWoNnqxVQ6jvjl2FVj2Lx9/c7Z+bwOKq2LqzxqA+H5fMUtAhQ==", "signatures": [{"sig": "MEUCIQDv3F4bOqOXo+1BUgOm1rz5On1C7IdMje4PHcIbQlCeUwIgJu2OyXuwllGkp5qNXsT3ALfqpbOk7WffbBwm0GlgIs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.9", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"debuglog": "0.0.2", "core-util-is": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6"}, "optionalDependencies": {"debuglog": "0.0.2"}}, "1.1.9": {"name": "readable-stream", "version": "1.1.9", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "d87130fbf8f9ee9c3b4058b3c58a3e30db2fcfdd", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.9.tgz", "integrity": "sha512-Xz7W+ck1vQnTauU5Uo2zYK/Ae8Ou5mRJrKkGFDZX+VF921HwsjL6lz3ga+vT8jQa6HT/0ZbamKffh9OE7xJ4vQ==", "signatures": [{"sig": "MEQCIDD+pgBdwWdNZd+NhjE13NTtcuHl8hvbzlOkDhllhT6zAiBgRMPF5Ga64cBIMScfasGTgCBW1YuXO11uITT/U/2I9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.11", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"debuglog": "0.0.2", "core-util-is": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6"}, "optionalDependencies": {"debuglog": "0.0.2"}}, "1.0.24": {"name": "readable-stream", "version": "1.0.24", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.24", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "b5659d5772cd06992dffe5a3bee2eec480f1c2fe", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.24.tgz", "integrity": "sha512-L564uxjF9jZqL5fBMRxxIst12eO/7ps0763dWImiO8BtUYARPTcgkZrCZC+F3jFKl04y1DDzPWR1PWcO64M+Og==", "signatures": [{"sig": "MEUCIQDLtrh0/QkIrcw2Oj51ejiPwRTlm0pYgoxA9Jr0qJ6ITgIgJUy0qaHU70Fn1ktGyVkDPy/IfcC0sbqEayUYTWsF0YA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.2.30", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.10": {"name": "readable-stream", "version": "1.1.10", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.10", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "d4dc2e5319e9c90d1e71c69390ef62cd90827f65", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.10.tgz", "integrity": "sha512-NUIS+hAoYjArCJhqzb9Yb8dde++603S14mSjV9mWOa++cxNiJfy52w+0eY+Gt8Qbtds3H2XujzI+ZMWVfksFlg==", "signatures": [{"sig": "MEQCIE9W/2nBoWdpgJ3VpRIx3Eife7SQlpm2TENKVEnendN4AiA7SZzj/I5evFtLGHt3cuxO2BKXYSu+VOSAs17HaF3I2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.24", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"debuglog": "0.0.2", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}, "optionalDependencies": {"debuglog": "0.0.2"}}, "1.0.25": {"name": "readable-stream", "version": "1.0.25", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.25", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "df19f64e6f74fd37ecf9d3ab8dbf1e2d11c9a045", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.25.tgz", "integrity": "sha512-289ftV5JMls3dvsIPwWxpXXfYyqGcCR7yKM5TpJ9e+aV6o1iuaZgAVvZulTTYq+/mks4EynT+9jt0nnwZpf7RQ==", "signatures": [{"sig": "MEQCIHRsrfOP/UnZYz4PcXIbKHY<PERSON>ou+EA007tWGGN0C5wakGAiAY4Um2RtFD/EKaBhX4Rmc6SBUd5F1V0AMM48Fll7BxAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.13", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.25-1": {"name": "readable-stream", "version": "1.0.25-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.25-1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "fec786621eed34dcc3fbe5aad596c915c14683bf", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.25-1.tgz", "integrity": "sha512-/B7uXboOrlVHqVHjDLSiudBLJRm9aZdsOovLaqSwiFuugb5BElnwdvgRG+20OGgDlXbKq9d6AWycYslneXkiMw==", "signatures": [{"sig": "MEUCIGqSN1dhMxZV/x+MCKwXmnMFrrKtZWnZqX8dpBrqDD9uAiEAhtnpRscYe4qPdrss8u11DY34MRMGqXTEWGnNr7U37Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.24", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.11": {"name": "readable-stream", "version": "1.1.11", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "76ae0d88df2ac36c59e7c205e0cafc81c57bc07d", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.11.tgz", "integrity": "sha512-QclHy2aDDn4nQXOLZAc3sWmQWlxvOg1PSqgQ0kknNqnwe/Rz7hQkalPXSfAYFUhZ5sq/PXCvYBOo4HrYC2ibSA==", "signatures": [{"sig": "MEQCIGFpkQ6JHJyR6mMQqnEDlod4HAC/pvW+NhUN62js5paOAiAb3lyP1c+Tn/VGT06+SpoPkceiGdObnZFVd5vIq54TjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.24", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"debuglog": "0.0.2", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}, "optionalDependencies": {"debuglog": "0.0.2"}}, "1.0.26": {"name": "readable-stream", "version": "1.0.26", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.26", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "12a9c4415f6a85374abe18b7831ba52d43105766", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.26.tgz", "integrity": "sha512-PwRbr564soD3g0aXUsOOklaMX7ePDoVWQDKsvqaexvDigffU2KS6zzzI7OOQKvUA93EVz7hRRQrye8vHCLRUUw==", "signatures": [{"sig": "MEUCIFCySpRyCL0MCuTTZCg01rUlXVP1mvWnY7FPbkEmu1kHAiEApcDBCzUB6goKeI7xnfesWy9CUjZnnofd9MUTXHKPnBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.3.24", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.26-1": {"name": "readable-stream", "version": "1.0.26-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.26-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "94a13c24ef8cc52af8a4fa6b3e1ef12444f68a0b", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.26-1.tgz", "integrity": "sha512-Pnmd6lmAO1+QOi+Im7iSNchwfDawXj/ca7tdl4ENgmz0kTnJqBcEyD7mR+3tqAK88YxzJg9Ds2Q0CnSQoEiUYQ==", "signatures": [{"sig": "MEUCIHLUyQnU2/CwtPdA7d2KCaZ2NMP4HVdRQhJIJrN1TAwzAiEAlHcsTdG5iAfV6XWmKJUUAQvAlb2R5K/Dgy05RrHmzF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.11-1": {"name": "readable-stream", "version": "1.1.11-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.11-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "a06bc4edc4861e1efd58a70d38a44f6c6cc9f3fe", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.11-1.tgz", "integrity": "sha512-uJbGAkk5tOwLDGTTFvRh1nDC6JdPDfazslhB8ruwB4qpvrFcnI/wtjOxBRpmm7kZkw1rwq1x7TOOGkKWNPB7hA==", "signatures": [{"sig": "MEUCIQDpMI17BzRzKj321YpYdQmkZZtB/80/xA9a4pZ3UvukKQIgJus8hiMng4m0+q7/SrBYfjAIruYksu34J8n4bT9GtaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"debuglog": "0.0.2", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}, "optionalDependencies": {"debuglog": "0.0.2"}}, "1.0.26-2": {"name": "readable-stream", "version": "1.0.26-2", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "readable-stream@1.0.26-2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "7883b21c63c1ce9373adfb1c23aa78c2906fa062", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.26-2.tgz", "integrity": "sha512-AXvfgs0YbNqvH1KNz1IMqtVd/qI3EEn+DGMEyk6BGe6BhmyV/LUVPnQh89L2uxBRHG8ME8xQms+04yVy3E7Jqw==", "signatures": [{"sig": "MEQCIEqpw2ffgxA+jIM9ujgYhr4nDufN7xHatEIaQHc+2XeDAiBjAAk3rfwR5SAVCJBPlBteeYtadI72qIi1D/12GU1bgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "An exploration of a new kind of readable streams for Node.js", "directories": {}, "dependencies": {"string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.12": {"name": "readable-stream", "version": "1.1.12", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.12", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "960d442bbf5f6690b6b20550413931021af8e506", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.12.tgz", "integrity": "sha512-Snw++KdmO/nOg1fHfSVffLg5qXpnNlqILPUYlQli9/AHcYomSrY7Dz20E1mmEsQ6o5fcmNphRhNSoc+rkRce/g==", "signatures": [{"sig": "MEYCIQCdXhE6Mz9oRBR8yjIoPctdfL4j+Smv4dwoyxvNmDeoXQIhAKfasCUZLdLT6HO9Dsn6NBH9YqNtE2Vwji0mDpaBsVRq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "Streams3, a user-land copy of the stream library from Node.js v0.11.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "^2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.26-3": {"name": "readable-stream", "version": "1.0.26-3", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.26-3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "58c1ef8dc375793221e7ea823e3328a7e7868e77", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.26-3.tgz", "integrity": "sha512-METxtz4DXBvBPV9kxnhAjxCB+f5q8M8EHy98MvnEtMQVvtETOfVXXmGRHT8+zSgph52e0fY8eIxuqCXLDM+PFw==", "signatures": [{"sig": "MEYCIQC17orbEHdbdoCILUNXrkp5pyn49ueT09NdiOph/J/e9QIhANGnd8DHxHo0p6MuBuVWgPYgPRCkOPpmd8RzwTUgRv3W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "^2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.26-4": {"name": "readable-stream", "version": "1.0.26-4", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.26-4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "1768852af9df316572227710fafbfa93c90fe3f9", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.26-4.tgz", "integrity": "sha512-cTe1EqvwyA7YzBv0nPL/Mb9P3YUzbL5OaQdKPuVDWsaNnp0fmXWe/TKjEdYzlpPNUXIdyqQzhmUDZdIywTE0cg==", "signatures": [{"sig": "MEUCIQCroQk9k+fPmY8q+ge/2nAZdyt+viUZDkRKd3rfSbNfrgIgMAiDIWcyBKCWXYgerMMMdUujCsgCRb/YJIg5Js1o9h0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.12-1": {"name": "readable-stream", "version": "1.1.12-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.12-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "5a1e464880bb91ef70005e7ee850d56f5b9e012f", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.12-1.tgz", "integrity": "sha512-dOROPmRhFsAOUU7L3Nxgie2GNLMXdJd4+0zcOnsT4HKahJ71uuRM1P2jnjcIXTXGWCfnT0snjGrqc6OsXJfRdQ==", "signatures": [{"sig": "MEUCIQDdMgHhPy0PFZU4jD0raYUDOmfpxi0qKkxwaZO2cKyCpwIgJWDamZaxN9c26CZuUAj4S8Vbkk2ZOQpHffIFKoLDoOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "Streams3, a user-land copy of the stream library from Node.js v0.11.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.27-1": {"name": "readable-stream", "version": "1.0.27-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.27-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "6b67983c20357cefd07f0165001a16d710d91078", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.27-1.tgz", "integrity": "sha512-uQE31HGhpMrqZwtDjRliOs2aC3XBi+DdkhLs+Xa0dvVD5eDiZr3+k8rKVZcyTzxosgtMw7B/twQsK3P1KTZeVg==", "signatures": [{"sig": "MEUCIHsiRCJlDso/1fhnWMeLceGlmNQP8duqyE50p7N5h/EsAiEAme6KM1KsDdd4jSu65+BkwPEQ+1inVFWqi+KmoGMiNUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.13-1": {"name": "readable-stream", "version": "1.1.13-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.13-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "fc6f04f3366bf37bae21bec2e411c1b4d2cf1a46", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.13-1.tgz", "integrity": "sha512-xHKWwNNwp0QrN1/INNcHZJoUDSrko5CswX2TXur1lr6yFfnwG9+9fwJQjU5TFVy352hJNKBC9eP2tkcWoprreg==", "signatures": [{"sig": "MEUCIEnt49RGzkfqW20cvW0xjymWGedMVIXRAWAaeIyAn2YxAiEAyA8/E/WS/hvb4rX7E8Ms90Jx9QOlqaCTqmQbUarjf+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.3", "description": "Streams3, a user-land copy of the stream library from Node.js v0.11.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.31": {"name": "readable-stream", "version": "1.0.31", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.31", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "8f2502e0bc9e3b0da1b94520aabb4e2603ecafae", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.31.tgz", "integrity": "sha512-tco/Dwv1f/sgIgN6CWdj/restacPKNskK6yps1981ivH2ZmLYcs5o5rVzL3qaO/cSkhN8hYOMWs7+glzOLSgRg==", "signatures": [{"sig": "MEUCIDy/ujm+u4IraPMDbsoMa4HjJ214PWRUabDpIkE0lrXaAiEAjZthB6WjTdFY0YgN7t2rreNzZBJ4w07emaRUA3zQYqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "8f2502e0bc9e3b0da1b94520aabb4e2603ecafae", "browser": {"util": false}, "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.9", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.1.13": {"name": "readable-stream", "version": "1.1.13", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.13", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "f6eef764f514c89e2b9e23146a75ba106756d23e", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.13.tgz", "integrity": "sha512-E98tWzqShvKDGpR2MbjsDkDQWLW2TfWUC15H4tNQhIJ5Lsta84l8nUGL9/ybltGwe+wZzWPpc1Kmd2wQP4bdCA==", "signatures": [{"sig": "MEUCIQCBzVWpjISwQlnHMY/m46mm8aYFV6LHN3zyk8Fh7dGeNQIgY9z2kChDKHeq9welqgmiD+RCPMnnntca3lWzZwiSfuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "f6eef764f514c89e2b9e23146a75ba106756d23e", "browser": {"util": false}, "gitHead": "3b672fd7ae92acf5b4ffdbabf74b372a0a56b051", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.23", "description": "Streams3, a user-land copy of the stream library from Node.js v0.11.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.32": {"name": "readable-stream", "version": "1.0.32", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.32", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "6b44a88ba984cd0ec0834ae7d59a47c39aef48ec", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.32.tgz", "integrity": "sha512-s8EIwr5CzlS0LpWjJihLZ2suTsuxasivxwSN+NCJ4LN0lLq31GaBg0IKMmsXAJvXxHiFJmeVTX7pwBwJG4eDxw==", "signatures": [{"sig": "MEUCIG0qj24GV5Y6jC2HRPDzQZKk8icLB6qLtxxzHi9604j7AiEAt90uN4OH6uoeN0R+4XcZu3VifYQPtDY9iWCOYNxaOHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "6b44a88ba984cd0ec0834ae7d59a47c39aef48ec", "browser": {"util": false}, "gitHead": "2024ad52b1e475465488b4ad39eb41d067ffcbb9", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "2.0.2", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "_nodeVersion": "0.10.31", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.32-1": {"name": "readable-stream", "version": "1.0.32-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.32-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "069b9f04e0592d0cb2a3fc515108c0624b1facdd", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.32-1.tgz", "integrity": "sha512-ZA82xqr5ysmo3+zF7QiKsgysKSxWdw1oU5L0iw+3FJGNFhgTQpDJ9L7HdF8oKaSXeP+ZLCXoHCM2zHcNnWKNrQ==", "signatures": [{"sig": "MEYCIQC/H/d6Aa+3Pf61vaL0RF0etMjMGqh8/Zu57zpwJNcKBgIhAI/CYWXOX2qfRtJCKQIwzl04KaoQpXNpEvMbl5LWd2DV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "069b9f04e0592d0cb2a3fc515108c0624b1facdd", "browser": {"util": false}, "gitHead": "ad328399d5ffb05f54455744d7c1a4848d1de5c0", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.23", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.33-1": {"name": "readable-stream", "version": "1.0.33-1", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.33-1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "40d0d91338691291a9117c05d78adb5497c37810", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.33-1.tgz", "integrity": "sha512-RYMQiCQClNL6KuunW3XbvkwT66XVsjr3Npuhy5JC2Io1QBoKenn+TV6OQX57HZzFYOdZpMgPPaUec/9G53QCxg==", "signatures": [{"sig": "MEUCIC+fjP598M0WEKWIKXP5Kz/YQn4gpX0nExOFsc4p69uGAiEA9WPKsPksF7WSmZwTozWVadHJ4r+WNa2BN8pXOmaUv/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "40d0d91338691291a9117c05d78adb5497c37810", "browser": {"util": false}, "gitHead": "cd9bc3fbcdc82211c5649b5170706b10c65c8479", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.23", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.33-2": {"name": "readable-stream", "version": "1.0.33-2", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.33-2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "d89cbf08789ebd903d24b86bd1d7e502c88af8a1", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.33-2.tgz", "integrity": "sha512-cDDVaSiF52jirCn7GCcm0I+xBNk2haSavYvVeae7dYn1lc6Ct5cp1v4OdCPKFT4K5IsrhTIWmDkeo/c1OHSxag==", "signatures": [{"sig": "MEUCICBlQ/BESH5223X9Ukgy6nQQ6VQtEQyQ6879oo5N6AuaAiEApZqBCLQss5Hppu+yYsC2jo+Ox5ZkZQlYFlCDpmESn9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "d89cbf08789ebd903d24b86bd1d7e502c88af8a1", "browser": {"util": false}, "gitHead": "99b64f21f49229b53f30f3fc5e9cffc4fcb84cb6", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.28", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "1.0.33": {"name": "readable-stream", "version": "1.0.33", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.33", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/readable-stream", "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "3a360dd66c1b1d7fd4705389860eda1d0f61126c", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.33.tgz", "integrity": "sha512-72KxhcKi8bAvHP/cyyWSP+ODS5ef0DIRs0OzrhGXw31q41f19aoELCbvd42FjhpyEDxQMRiiC5rq9rfE5PzTqg==", "signatures": [{"sig": "MEUCIA2BbmuIOjXwvOn4TEZ9titZO68g33RNgCnmHw1SY0U6AiEA4wUxYnxgZoqfFiBKrgHI0aEU7r/W0nILYZB6U4xpan4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "3a360dd66c1b1d7fd4705389860eda1d0f61126c", "browser": {"util": false}, "gitHead": "0bf97a117c5646556548966409ebc57a6dda2638", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "1.4.28", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}}, "2.0.0": {"name": "readable-stream", "version": "2.0.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "ab6e292d8ae392ef888151310554e7078fcb38ca", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.0.tgz", "integrity": "sha512-xA2xLn/L0fGIWox2NKRwET7pWLUJy73/Ne7l5NwcxyXYqaNQ92YfbsJu/9UFnUf16zqK+z2b2XBOCMvTQz4CzQ==", "signatures": [{"sig": "MEYCIQDy017pajriRsWSN1e71cYlhEKpSEjQk6GlJaen0mXPZQIhAJD+4hJfhXZNQZ+sVZvaNC8duOz5hssyHknZYRvQLF/z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "ab6e292d8ae392ef888151310554e7078fcb38ca", "browser": {"util": false}, "gitHead": "91173c7289ad37914745c42e37573273885e21ec", "scripts": {"test": "tap test/parallel/*.js"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "2.11.0", "description": "Streams3, a user-land copy of the stream library from iojs v2.x", "directories": {}, "_nodeVersion": "2.2.1", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6"}}, "2.0.1": {"name": "readable-stream", "version": "2.0.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "633479b7bd2fbe7a1e869825b40a0b333b9f2bfc", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.1.tgz", "integrity": "sha512-ERd0Db9N6nlAZJuSIbPDi+1h2wBTdOB48DhxKV+lLYicAU4wm6osfhyspw2MGxju2vpP/UbkEVueyDUPUGVvDA==", "signatures": [{"sig": "MEQCIDhsnUos5V/PZf3fxiVCkde2gx6F4mRpkC/3OfR29I4JAiBjifudv9yhs/1Gbw9IyZuuvIz8VBrLIdsjjjBhY0yajg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "633479b7bd2fbe7a1e869825b40a0b333b9f2bfc", "browser": {"util": false}, "gitHead": "d175d0f68745a5014fc9c41b25b8e0e959269126", "scripts": {"test": "tap test/parallel/*.js", "browser": "zuul --browser-name $BROWSER_NAME --browser-version $BROWSER_VERSION -- test/browser.js"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "2.11.1", "description": "Streams3, a user-land copy of the stream library from iojs v2.x", "directories": {}, "_nodeVersion": "2.3.0", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.0.0", "zuul": "~3.0.0"}}, "2.0.2": {"name": "readable-stream", "version": "2.0.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "bec81beae8cf455168bc2e5b2b31f5bcfaed9b1b", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.2.tgz", "integrity": "sha512-n/GNF/p6uPfc2oqUsX+HMYlEGm1tA4KZfQHshHETDUatTt2siNtARIxNdsiqCaWsurzjLGarhbKrbA9x/GhcNA==", "signatures": [{"sig": "MEYCIQDDI6H6xNrzF3zeX21FRYteHAz6clqNhlREPNa1UavkrwIhAPC6YF+Q0/arvQho5Y9H33K+TJPyzUd/vIweWu9CGejG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "bec81beae8cf455168bc2e5b2b31f5bcfaed9b1b", "browser": {"util": false}, "gitHead": "1a70134a71196eeabb5e27bc7580faaa68d30513", "scripts": {"test": "tap test/parallel/*.js", "browser": "zuul --browser-name $BROWSER_NAME --browser-version $BROWSER_VERSION -- test/browser.js"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "2.11.1", "description": "Streams3, a user-land copy of the stream library from iojs v2.x", "directories": {}, "_nodeVersion": "2.3.0", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.0.0", "zuul": "~3.0.0"}}, "2.0.3": {"name": "readable-stream", "version": "2.0.3", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "4ab16b2aba452374b542a3f4f528634b9b45bb5a", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.3.tgz", "integrity": "sha512-y+wLZJo7xEaG0nxf4gp5cjb2UHj0hX3T8VMMwZYPY5ZyVELqYwHjxYql7j37ogIltYrb2ac1X9JJRZ9jlIAoVg==", "signatures": [{"sig": "MEYCIQDM5ZKYSR7pIOf0hvZ3uAYyVJVLS2Oz2dm6DcwKyTCxuQIhAJPbP+GTOvs1UnbpzjuIsy+rwe8ENxh7jcwDHxsyZ8xj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "4ab16b2aba452374b542a3f4f528634b9b45bb5a", "browser": {"util": false}, "gitHead": "58099551e1ea7d99f8d13495740c5ae3f5d98053", "scripts": {"test": "tap test/parallel/*.js", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Streams3, a user-land copy of the stream library from iojs v2.x", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.0.0", "zuul": "~3.0.0"}}, "2.0.4": {"name": "readable-stream", "version": "2.0.4", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "2523ef27ffa339d7ba9da8603f2d0599d06edbd8", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.4.tgz", "integrity": "sha512-irlzmIullL3n7q+INILlTg7MbFqYP3f6kNUzhNuWtAp8O0ZGZm64J6NurIJb9HSi3ypxTeEqieiOsB+RqwIjWw==", "signatures": [{"sig": "MEUCIQDB2poryc5+WLR4uo3QQTOKfRS5wyvZz66fmODLINDoJQIgbDUCJ8p8fTmZQ9zYL4RBtEY6Ptl3hLuBPvb4SmqmKK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "2523ef27ffa339d7ba9da8603f2d0599d06edbd8", "browser": {"util": false}, "gitHead": "f2a4f4a659bacbe742a494b7d2aede64fab0d4f9", "scripts": {"test": "tap test/parallel/*.js", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Streams3, a user-land copy of the stream library from iojs v2.x", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.0"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.0.0", "zuul": "~3.0.0"}}, "2.0.5": {"name": "readable-stream", "version": "2.0.5", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "a2426f8dcd4551c77a33f96edf2886a23c829669", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.5.tgz", "integrity": "sha512-a2vWwaZC9d3kKrNKzEEETumWJxa18TgQiEB/A7fTDuHj6+GUa4RwMj8II9yogc/bbN/WO0/XlOLNYPbI8JpIsQ==", "signatures": [{"sig": "MEUCIHNOoSqEhhAOWwZ0HwAngxIP2IPpVg6EUmlv/SJaqzYhAiEAngTs5jH2u8ilFxB76UWVfbmZJxcdSQcgVXG5RfVwGv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "a2426f8dcd4551c77a33f96edf2886a23c829669", "browser": {"util": false}, "gitHead": "a4f23d8e451267684a8160679ce16e16149fe72b", "scripts": {"test": "tap test/parallel/*.js", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Streams3, a user-land copy of the stream library from iojs v2.x", "directories": {}, "_nodeVersion": "5.1.1", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.0.0", "zuul": "~3.0.0"}}, "2.0.6": {"name": "readable-stream", "version": "2.0.6", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "8f90341e68a53ccc928788dacfcd11b36eb9b78e", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz", "integrity": "sha512-TXcFfb63BQe1+ySzsHZI/5v1aJPCShfqvWJ64ayNImXMsN1Cd0YGk/wm8KB7/OeessgPc9QvS9Zou8QTkFzsLw==", "signatures": [{"sig": "MEUCIQDnQx1KpqD5aMPiPUqG1A0aFWckajIhfHG8SQkWHHbIaQIgHeiAH3IWyTZ3yImNg6sv7vPg7/zyn03YGPoN/6nui+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "8f90341e68a53ccc928788dacfcd11b36eb9b78e", "browser": {"util": false}, "gitHead": "01fb5608a970b42c900b96746cadc13d27dd9d7e", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.7.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.5.1", "zuul": "~3.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.0.6.tgz_1457893507709_0.369257491780445", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.34": {"name": "readable-stream", "version": "1.0.34", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.0.34", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "125820e34bc842d2f2aaafafe4c2916ee32c157c", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz", "integrity": "sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==", "signatures": [{"sig": "MEUCIAYQNM3uug8+Cy6pO3/NyDrvcxuzrfea2OQ5MiMS6CSkAiEA1AdoFUPPjj+UX7jEm153BzCpSi9VcnwOgWYT9eVlg9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "125820e34bc842d2f2aaafafe4c2916ee32c157c", "browser": {"util": false}, "gitHead": "1227c7b66deedb1dc5284a89425854d5f7ad9576", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "3.8.3", "description": "Streams2, a user-land copy of the stream library from Node.js v0.10.x", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-1.0.34.tgz_1460562521506_0.019665231462568045", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.14": {"name": "readable-stream", "version": "1.1.14", "keywords": ["readable", "stream", "pipe"], "author": {"url": "http://blog.izs.me/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "readable-stream@1.1.14", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/readable-stream/issues"}, "dist": {"shasum": "7cf4c54ef648e3813084c636dd2079e166c081d9", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "signatures": [{"sig": "MEYCIQDflaIE0NT276ryJHdXc0OcDNY97pfqlufn7L7tGlk7RgIhAMgmhXmKVEqGhk1erZVMkrjFpHyq0+H8U0BbbpfqLrMa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "7cf4c54ef648e3813084c636dd2079e166c081d9", "browser": {"util": false}, "gitHead": "52550840cb1d6e8a98ef9a909a4bea360bc6f7da", "scripts": {"test": "tap test/simple/*.js"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/readable-stream", "type": "git"}, "_npmVersion": "3.8.3", "description": "Streams3, a user-land copy of the stream library from Node.js v0.11.x", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"isarray": "0.0.1", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x"}, "devDependencies": {"tap": "~0.2.6"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-1.1.14.tgz_1460563293219_0.5682175166439265", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.0": {"name": "readable-stream", "version": "2.1.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "36f42ea0424eb29a985e4a81d31be2f96e1f2f80", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.0.tgz", "integrity": "sha512-bgvBjXKqFF0zIEp+ISH0ZotkKOZu0utDWhcajnNifD6f4TZ2FHbSfmMouVYZEizg4BXLXz388CRvrHPqlQG4mw==", "signatures": [{"sig": "MEQCIGgC5lWF4J2BgEeiBC7vmD5NjhQ2yomQm7OfABLB3A51AiA42X+FaEvKX+gR0ilnttLRtpGptwBi936bCcrBsDbbGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "36f42ea0424eb29a985e4a81d31be2f96e1f2f80", "browser": {"util": false}, "gitHead": "4c2d8e2639ffd516b12544ce0c117cc0345daa3f", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "browserify": {"transform": ["inline-process-browser", "unreachable-branch-transform"]}, "repository": {"url": "git://github.com/nodejs/readable-stream", "type": "git"}, "_npmVersion": "3.8.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6", "inline-process-browser": "~2.0.1", "unreachable-branch-transform": "~0.5.0"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.1.0.tgz_1460568003255_0.9190005895216018", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.1": {"name": "readable-stream", "version": "2.1.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "cbbe2f857ec4fafee422a8addbf31513e891c8ed", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.1.tgz", "integrity": "sha512-Ituj6avWyukL56JMRv1VXPuD8/nF2/hn72rsxG58LmtwlVBYZC9Ao8vpOuixqfwslrMAufVApWi+hr6pV197Rg==", "signatures": [{"sig": "MEUCIELUWb0sbP8u40zUw2xAns6lzcsrWs5upUqexOiDZqlSAiEAl9j1bHqnbs5OEVdkqEiJpmmfGLH2oJdSsSgzqYbWUWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "cbbe2f857ec4fafee422a8addbf31513e891c8ed", "browser": {"util": false}, "gitHead": "c3d049d11dc8a57af3135a9b8af3279316603a42", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "browserify": {"transform": ["inline-process-browser", "unreachable-branch-transform"]}, "repository": {"url": "git://github.com/nodejs/readable-stream", "type": "git"}, "_npmVersion": "3.8.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.1.1.tgz_1461931256244_0.31011770688928664", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.2": {"name": "readable-stream", "version": "2.1.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "a92b6e854f13ff0685e4ca7dce6cf73d3e319422", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.2.tgz", "integrity": "sha512-ZNQuVTiooNPYb9jKt+qqubFyqlaSeaebn0fBAB+5K3j00qu7aIf+76A3ijmB4jrwWXZYj1waij4eoan7CPlIjA==", "signatures": [{"sig": "MEUCIQC9XjgyYfVjIQ7JC/jMbFYAL1zr/InUaBOFkutH8E1e+wIgURNk4bartLhabt5O5sv8wh737zgwnYyN+8qRovQxsKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "a92b6e854f13ff0685e4ca7dce6cf73d3e319422", "browser": {"util": false}, "gitHead": "06754eed4f2b882b589f8667ecc8aadcf916045f", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream", "type": "git"}, "_npmVersion": "3.8.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.1.2.tgz_1461933796258_0.5075750169344246", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.3": {"name": "readable-stream", "version": "2.1.3", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "9db8ec4025b4c71e69aec60b453b590c8afeb0df", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.3.tgz", "integrity": "sha512-3gHOgqc8Dgl2LHg4DL6PsPVeB7lWDC8qjpx3SwGPJsrsRc9ZJ9q/7SFHwGAnu/YefD3ZMx5mESPrr0b1Y/bamw==", "signatures": [{"sig": "MEQCIE7pBhLc21+nzqmotzwHIwB9BEGHnQbQrRp+aXEwHaiCAiBIuhwnKyOjVgMBERuqoZ5MxQXASclPO7/g6UbEaQPUJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "9db8ec4025b4c71e69aec60b453b590c8afeb0df", "browser": {"util": false}, "gitHead": "abcff84645534aaedaed4192c4ce788af9112bc2", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream", "type": "git"}, "_npmVersion": "3.8.6", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.11.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.9.0", "assert": "~1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.1.3.tgz_1463587875388_0.811288726516068", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.4": {"name": "readable-stream", "version": "2.1.4", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "70b9791c6fcb8480db44bd155a0f6bb58f172468", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.4.tgz", "integrity": "sha512-TiHcMow0nmwtdagrTgs1Qt6wuuqIoMLlpROSS72dLrU+5qx/3xcaTW1bOKkfMdgknHzrLl2RguLQFhxfPJh54Q==", "signatures": [{"sig": "MEUCIQDKzCOuyfdbUYNv0wFprN4YWCT8KTHJ/SRR54tyPardhAIgHT4NZPzO0DhwtNE2ctU2Zr3DsWTJKs7L0v0/6w+XOgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "70b9791c6fcb8480db44bd155a0f6bb58f172468", "browser": {"util": false}, "gitHead": "7752832fba237929388dea6c96911a0a6379abfc", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\ntunnel: ngrok\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream", "type": "git"}, "_npmVersion": "3.8.6", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.11.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.9.0", "assert": "~1.4.0", "zuul-ngrok": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.1.4.tgz_1463679605032_0.6917394688352942", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.5": {"name": "readable-stream", "version": "2.1.5", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.1.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "66fa8b720e1438b364681f2ad1a63c618448c9d0", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.5.tgz", "integrity": "sha512-NkXT2AER7VKXeXtJNSaWLpWIhmtSE3K2PguaLEeWr4JILghcIKqoLt1A3wHrnpDC5+ekf8gfk1GKWkFXe4odMw==", "signatures": [{"sig": "MEUCIH8cWR0TPJ8Xg7JDTRdDte10v3H4LfZlEAx0My0kq8K3AiEAjoZ3FxH3G6ZKZhgJdmt5MAPsJEmG9jcIHgq4t4Y8Zto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "66fa8b720e1438b364681f2ad1a63c618448c9d0", "browser": {"util": false}, "gitHead": "758c8b3845af855fde736b6a7f58a15fba00d1e7", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream", "type": "git"}, "_npmVersion": "3.8.6", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "5.12.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.1.5.tgz_1471463532993_0.15824943827465177", "host": "packages-16-east.internal.npmjs.com"}}, "2.2.0": {"name": "readable-stream", "version": "2.2.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "bb2d810631416edf095ebdadc246f68cec65bf2c", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.0.tgz", "integrity": "sha512-FiMVkR8dX4WDFtEo8P4B6hRxRX6ZazKY0q9nsu1m5VUPVOazD30zE5wCy7uTwXxrqc+hj57dPocLUuOHavdhhg==", "signatures": [{"sig": "MEYCIQCaf382zT6PRVkbcoJSba+464TC65m4jGAw9ryfUHFUCgIhAPfOSWuyzRkXfMQ0eWJCqXuU4UJaDOS6v7XwAPt53wUF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "bb2d810631416edf095ebdadc246f68cec65bf2c", "browser": {"util": false}, "gitHead": "54a2d71a68fbd182854976675657f5a26d61d497", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "bundleDependencies": ["buffer-shims", "core-util-is", "isarray", "process-nextick-args", "util-deprecate"], "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.0.tgz_1478788237800_0.8810451354365796", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.1": {"name": "readable-stream", "version": "2.2.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "c459a6687ad6195f936b959870776edef27a7655", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.1.tgz", "integrity": "sha512-h4oVWeuEpxz0vrFkkYEpaFZm4fkBdN/0pKLA6oYMuBV/H57/03OfARoVBzyhDSuSwEmIQawwdXDVeuanY7soKQ==", "signatures": [{"sig": "MEYCIQC7rpfZ9l1gJVwQNyCtgH0jBj3uw68Gjx61rT0Bh/2AuAIhAMdDiANCWZzz4iCDreW/5Grn54m2wxwUOKhKbIor8wfF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "c459a6687ad6195f936b959870776edef27a7655", "browser": {"util": false}, "gitHead": "8f97e1d45b8e67cd37d8736ab4bb69053b7b18a3", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.1.tgz_1478793239873_0.6319354753941298", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.2": {"name": "readable-stream", "version": "2.2.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "a9e6fec3c7dda85f8bb1b3ba7028604556fc825e", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.2.tgz", "integrity": "sha512-WVJE6ORa9FY7MZXXhjm70pLl1hfiB6gPQrmcRtPH7gZlQDSSE/bnuC/PLD6nNNRUU0oeGp1bwmjvN8h+PH0yDA==", "signatures": [{"sig": "MEQCIDY9gMuYUiOH41cewNszJKc5HF8BhaN2mkRY/qCvcIsoAiAOvyA67iHlVIBpux2KU6XqjOJhGZPU7sN1NKTC1doo/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "a9e6fec3c7dda85f8bb1b3ba7028604556fc825e", "browser": {"util": false}, "gitHead": "f239454e183d2032c0eb7d79a1c08f674fdd8db4", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.2.tgz_1479128709230_0.5291099038440734", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.3": {"name": "readable-stream", "version": "2.2.3", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "9cf49463985df016c8ae8813097a9293a9b33729", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.3.tgz", "integrity": "sha512-OLfrM2DhFPuL2UdCg0m5yCPBpFXOalTvoIslspZaUAF2apO11BVVelpfMgC1d9M2SSQFbPlHwIbvz05We07HiA==", "signatures": [{"sig": "MEUCID5iZ8WK1Fm2JgTw2QKEjJr2T6lPTuiHbNi0BVoBUsffAiEA33W2COYO9zJNLRAdr6osd0bCW6gRtqoKC4aBsfirarE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "9cf49463985df016c8ae8813097a9293a9b33729", "browser": {"util": false}, "gitHead": "4dbe6e20051e44025d3cbec3ed9603483d8b27cb", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "react-native": {"stream": false}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.3.tgz_1487688066228_0.35690787457861006", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.4": {"name": "readable-stream", "version": "2.2.4", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "1a8da72653a38450ba275eca1f3c602027afda0a", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.4.tgz", "integrity": "sha512-xqvvN46wpkuRtzQQWX9/Ph4K6AUpuVoySQ6CRBb/SZDenQJxDUaF3qTbHaRIS3bohHRYat/oiWb12TdOGdSp4w==", "signatures": [{"sig": "MEQCIG5w22TqSALI/dvsxoDa/EM33tEflzxEgbskHNG6dgeyAiAH8Bvdez2sxlUoyAP6Rld8ihpukg/WOdAEo6cYAyD3vA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "1a8da72653a38450ba275eca1f3c602027afda0a", "browser": {"util": false}, "gitHead": "c271a6ab33645adc17eca9ad4592cfbe301c973a", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "react-native": {"stream": false}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.4.tgz_1489498241718_0.3233698245603591", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.5": {"name": "readable-stream", "version": "2.2.5", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.5", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "a0b187304e05bab01a4ce2b4cc9c607d5aa1d606", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.5.tgz", "integrity": "sha512-ajDyH3vmKzosIAdtc3Yzr0GurfXvZXbqa+hLBbG5N+0qfIQbePC137hmXFLCt5afY4EGlwVV8rs0FwADlahz8A==", "signatures": [{"sig": "MEUCIGC13AnQxmuoM4/uCm4ipU4t06vyR18aQOgHJg8ja7shAiEA2G43xcBS5vEC2oDsJ0I47DKMucMeVL/yy6AagChlIsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "a0b187304e05bab01a4ce2b4cc9c607d5aa1d606", "browser": {"util": false}, "gitHead": "b062fe03ee8b1a88afacd468310dbd11bf811ec0", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.9.4", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "react-native": {"stream": false}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.5.tgz_1489505323144_0.6119352562818676", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.6": {"name": "readable-stream", "version": "2.2.6", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.6", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "8b43aed76e71483938d12a8d46c6cf1a00b1f816", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.6.tgz", "integrity": "sha512-stKjqcfdLrCTp8+25wOo2fZeU3dQ1e968LX6qknqzd3PKj/e+CKJTskQ3kPlTW3udhLqj6om9VqXEw/VtXZYrw==", "signatures": [{"sig": "MEYCIQDHmFAyiFw04L0SdcgEh4yJHl2U9gM4r226FDXAEPe1AQIhAO1ZFhRjiN4l7Oyiy/Mka/KcUKBECOUzwI6kfot2J9Zg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "8b43aed76e71483938d12a8d46c6cf1a00b1f816", "browser": {"util": false}, "gitHead": "f94eebc1c5b637e6575735e6923ec79ee3139284", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.9.4", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "^1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "react-native": {"stream": false}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.6.tgz_1489651345676_0.6984770004637539", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.7": {"name": "readable-stream", "version": "2.2.7", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.7", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "07057acbe2467b22042d36f98c5ad507054e95b1", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.7.tgz", "integrity": "sha512-a6ibcfWFhgihuTw/chl+u3fB5ykBZFmnvpyZHebY0MCQE4vvYcsCLpCeaQ1BkH7HdJYavNSqF0WDLeo4IPHQaQ==", "signatures": [{"sig": "MEQCIEF90x04RLkqqdas3IbJeRoqHABoxDxICx9cmePVrJbgAiA5oAZk15cf9fDN+MLrqLhG18zLQvkTXsudEi1oT2IaBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "07057acbe2467b22042d36f98c5ad507054e95b1", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js"}, "gitHead": "847d79ea060d512ddd07f5e246c12a38520da308", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "~1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "react-native": {"stream": false}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.7.tgz_1491551100653_0.5321758626960218", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.8": {"name": "readable-stream", "version": "2.2.8", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.8", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "ad28b686f3554c73d39bc32347fa058356624705", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.8.tgz", "integrity": "sha512-C/PmMW8FaR87QPQ2pHqDu50pf30kMDRsQXg0sg5G+S0vxWWNFiNy7llD9ZZsh+puPqoO4OBjsMz7sWpTKxG0BQ==", "signatures": [{"sig": "MEUCIG03wgClEpE+fRr1HrGn3zsu1y+ZFJlxARKjqvftIsJ3AiEA68Z+JtogP4kE/ZEjFexE0NU4fmBPgafCpaBBpwA1GBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "ad28b686f3554c73d39bc32347fa058356624705", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js"}, "gitHead": "2390aec4ee4f53c0a33155b801f0c97cca5c8bb6", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "~1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "react-native": {"stream": false}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.8.tgz_1491574243126_0.4595953382086009", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.9": {"name": "readable-stream", "version": "2.2.9", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.9", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "cf78ec6f4a6d1eb43d26488cac97f042e74b7fc8", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.9.tgz", "integrity": "sha512-iuxqX7b7FYt08AriYECxUsK9KTXE3A/FenxIa3IPmvANHxaTP/wGIwwf+IidvvIDk/MsCp/oEV6A8CXo4SDcCg==", "signatures": [{"sig": "MEUCIQDxLC2+n9PnUvLMREYorRBWANceh34KUAQyUcTm/6rV8wIgH36I8f88bCCXkFbOjIt4bJX8mAqYXjZ0kBNJ8OmTYRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "cf78ec6f4a6d1eb43d26488cac97f042e74b7fc8", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "16eca30fe46a937403502a391859ef51625bcc53", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "buffer-shims": "~1.0.0", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.9.tgz_1491638759718_0.9035766560118645", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.10": {"name": "readable-stream", "version": "2.2.10", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.10", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "effe72bb7c884c0dd335e2379d526196d9d011ee", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.10.tgz", "integrity": "sha512-HQEnnoV404e0EtwB9yNiuk2tJ+egeVC8Y9QBAxzDg8DBJt4BzRp+yQuIb/t3FIWkSTmIi+sgx7yVv/ZM0GNoqw==", "signatures": [{"sig": "MEQCIGqJ9N8Jbpvvs77ISEoqZeRYb0mSjwI645pzE7/iXE7+AiBUt4Vifpylyy/pxid5ADxe1TS+Ozv2dKfEShHxsJUnCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "fc20e297b1e0a2ea1c7b466be604a4e9bbb9a946", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "safe-buffer": "^5.0.1", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.10.tgz_1496387728035_0.7847163598053157", "host": "s3://npm-registry-packages"}}, "2.2.11": {"name": "readable-stream", "version": "2.2.11", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.2.11", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "0796b31f8d7688007ff0b93a8088d34aa17c0f72", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.2.11.tgz", "integrity": "sha512-h+8+r3MKEhkiVrwdKL8aWs1oc1VvBu33ueshOvS26RsZQ3Amhx/oO3TKe4lApSV9ueY6as8EAh7mtuFjdlhg9Q==", "signatures": [{"sig": "MEUCIQD6i2wbPvZSfZCnMSrVkRznDGQmqo/SuD0C75uQmuz1+QIgVSNTQ0+kZp7rF1YVDth248iyU3L/X5DRJ4MFpK78V3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "98b5c7625364b302e418d0412429bc8d228d356a", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.1", "safe-buffer": "~5.0.1", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.2.11.tgz_1496759274017_0.08746534585952759", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "readable-stream", "version": "2.3.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.0", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "640f5dcda88c91a8dc60787145629170813a1ed2", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.0.tgz", "integrity": "sha512-c7KMXGd4b48nN3OJ1U9qOsn6pXNzf6kLd3kdZCkg2sxAcoiufInqF0XckwEnlrcwuaYwonlNK8GQUIOC/WC7sg==", "signatures": [{"sig": "MEQCIFH12aqHIUTmRdJN7Uo7/xJ2S1jjgU8OZsHikd8lh2+pAiBJe6xjUNdHF8bDbCmLWdcrfM83FFpnwBzt4LRHHsPNbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "f81f716335c7c5f500618c7ee8fd29e08b1cf2b5", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.1.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.0", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.3.0.tgz_1497878792655_0.276895274175331", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "readable-stream", "version": "2.3.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.1", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "84e26965bb9e785535ed256e8d38e92c69f09d10", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.1.tgz", "integrity": "sha512-u6cxIvtbZcjq2HH71Zc/SRBUl7vbv62szIqmqqGpK3HY5J1c0kR/LUzKUpeoFgMzapvVAlBD+QY56ilWmHi4Nw==", "signatures": [{"sig": "MEUCIQDV5NSXg7B+PhheCFYaFtT1JdCzoiGCv3KzjW8wzyVvAwIgT9WZXM3Hlf52yx15ZpU9IXjkUq1j0/K2edU+LqkUSUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "46770aba1db21c164ee0df8b576576102700236b", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.0", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.3.1.tgz_1498054038106_0.8029541240539402", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "readable-stream", "version": "2.3.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.2", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "5a04df05e4f57fe3f0dc68fdd11dc5c97c7e6f4d", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.2.tgz", "integrity": "sha512-SfBHKFbV8XMg3KaaCxkdxQZ76Si5/ELNRSgg+FDa0fbSLwGxvQDx+aJey1bjEa4FYo1svwdf9q/BsgPuvvkH7w==", "signatures": [{"sig": "MEUCIFjyOXRwp6ZGeG1ZftqboXoM0gBatyj8iaAOrFbv5GTpAiEA5ZPtgt6wLDJo8CKaxAcH+iY93MC54TIVLpFv+Yeo5nU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "_from": ".", "_shasum": "5a04df05e4f57fe3f0dc68fdd11dc5c97c7e6f4d", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "dfbb3377d15aa42500c9fdedbcf3a2819204bf7b", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "6.11.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.0", "core-util-is": "~1.0.0", "string_decoder": "~1.0.0", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.3.2.tgz_1498139300782_0.7375865990761667", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "readable-stream", "version": "2.3.3", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.3", "maintainers": [{"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "368f2512d79f9d46fdfc71349ae7878bbc1eb95c", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.3.tgz", "integrity": "sha512-m+qzzcn7KUxEmd1gMbchF+Y2eIUbieUaxkWtptyHywrX0rE8QEYqPC07Vuy4Wm32/xE16NcdBctb8S0Xe/5IeQ==", "signatures": [{"sig": "MEUCIQCohmEyrrhhDW0bhDlsEqFa65+SbpqHdid5PaWlsS0c5QIgQKJtHyGmSOmkK128nH/CdtCgEFYqgxVbBqPk0GGzYzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "cd59995050105b946884ee20e3bcadc252feda8c", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.1.3", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.1", "core-util-is": "~1.0.0", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1", "process-nextick-args": "~1.0.6"}, "devDependencies": {"nyc": "^6.4.0", "tap": "~0.7.1", "tape": "~4.5.1", "zuul": "~3.10.0", "assert": "~1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream-2.3.3.tgz_1498745872585_0.8663316275924444", "host": "s3://npm-registry-packages"}}, "2.3.4": {"name": "readable-stream", "version": "2.3.4", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.4", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "c946c3f47fa7d8eabc0b6150f4a12f69a4574071", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.4.tgz", "fileCount": 24, "integrity": "sha512-vuYxeWYM+fde14+rajzqgeohAI7YoJcHE7kXDAc4Nk0EbuKnJfqtY9YtRkLo/tqkuF7MsBQRhPnPeyjYITp3ZQ==", "signatures": [{"sig": "MEUCIQD30uifjpIuDDbk041W29EG/uF1dzvYW9vBZXszjzk+iAIgLOqxQdhvUsYAy+6keiOi8YNw70KKdY1ocmxmdEh806s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87478}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "a8c2df1d27dfcf050f66b8c0a08bf2680761b3cc", "scripts": {"test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.1", "core-util-is": "~1.0.0", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1", "process-nextick-args": "~2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0", "zuul": "^3.11.1", "assert": "^1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_2.3.4_1518211892601_0.34993334324647996", "host": "s3://npm-registry-packages"}}, "2.3.5": {"name": "readable-stream", "version": "2.3.5", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.5", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "b4f85003a938cbb6ecbce2a124fb1012bd1a838d", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.5.tgz", "fileCount": 24, "integrity": "sha512-tK0yDhrkygt/knjowCUiWP9YdV7c5R+8cR0r/kt9ZhBU906Fs6RpQJCEilamRJj1Nx2rWI6LkW9gKqjTkshhEw==", "signatures": [{"sig": "MEYCIQCNsAks8TgUU9h54MB2tqQe45gcR81Q+TP/mbj66jhbdQIhAOKnGPaALw04pFXLiOlD/5OURkTgablE+lcMtZEfoaXe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87474}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "a47240c0755580e7706bdf89de6dfa79a4aae762", "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "local": "zuul --local 3000 --no-coverage -- test/browser.js", "report": "nyc report --reporter=lcov", "browser": "npm run write-zuul && zuul --browser-retries 2 -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.1", "core-util-is": "~1.0.0", "string_decoder": "~1.0.3", "util-deprecate": "~1.0.1", "process-nextick-args": "~2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0", "zuul": "^3.11.1", "lolex": "^2.3.2", "assert": "^1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_2.3.5_1520094127168_0.8054664870434038", "host": "s3://npm-registry-packages"}}, "2.3.6": {"name": "readable-stream", "version": "2.3.6", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.6", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.6.tgz", "fileCount": 24, "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "signatures": [{"sig": "MEQCIAf8QA+KojUODAvwRxK0aysFoPUrshsh39bvMJiyZNJeAiBw0yfTgK19w0859fj4uIzSPbjg9QUYZLxN4dspvgB8ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87961}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "b3cf9b1f46eaa1865930ae03b96d7a4a570746f0", "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.1", "core-util-is": "~1.0.0", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1", "process-nextick-args": "~2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0", "lolex": "^2.3.2", "assert": "^1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_2.3.6_1522856414086_0.18438932255173968", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.1": {"name": "readable-stream", "version": "3.0.0-rc.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.0-rc.1", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "cad3839e37fba66b912ae5de7cac9c24c8f3d4cd", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.0-rc.1.tgz", "fileCount": 27, "integrity": "sha512-Kq/y65C0TDab9C9ZPedaBS+LAJpqptU/DhjlIGs+ggoxF2akk9xfZn0nr7hp27RZ1W3RvZw37Ss/1bOPcRIIRw==", "signatures": [{"sig": "MEUCIGUENVTufE72b3tZhYtOvRkBioBRWu/Ezb+rAOctrvIcAiEAxM34wZWaw+zzl07R9QkwytV8Uln4geQMpnzrsSGXDhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQzQXCRA9TVsSAnZWagAAnMAP/1BFA1IZDdX+Q/2A5P7v\n8cBDCr4n/SRyGWBjGvnSSjh8jYulWbOceLg/O4FwJL4Q2Xb4RtEEHMTP2Lu7\nTXeQQgxTLhVLMw6nZSRuA3RDScxw4lwXWSOh2jslPfa2cWmPL5PfsGHvNxML\nKWVT6d+TLmrqv8gaRW1/WrLEI2ViYRQqfR9EMJ5eNFR9ZCnlP1zsuq0LGdqD\nAI/bJW50ubHJfCgkXo7Zv5c7IdFpuYH+SGYxWQ1cLq8RXNI+psO/bSfaQsV/\n1YHr2xKWYFf2K4nFtoQWmjhhOBC4hQfR3J579ADeN2YH4bchE84Bg4PX/WHm\n2cw22KD3hfKF5WKTc3gdeFSpglIY7FXmcC/+Yzk8E4CHidY+tgaYv/C9iy5U\nW59RFW/tUHMSGcEuMJoME6JdTpBRy0mhKkNUyFvvwRInve5D3vapBMXr7uJS\nI6VYjtCtazlPMltLK8g67zvzzelOIn6e4nNxm1tGbqPCQgnzMOAMhYlLBk+M\n9apHsZCWyZVWfCdIgDGh8ATDIbbapGKdibsGmKKAZADRwmdSKsoYaMLPpkmH\nD7r2RES8h3UoE0Ydj6UjsplZGvX0Xx5D9zuDJoF5B2MS6E2cel0r+SlArbY1\no3k8kaiz12wMNbAxdxWzOhe7oNeIIQmgzteKLiAeoC++hUIXGrxKDgxSn7I8\nXzMR\r\n=rCfz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "54a6ee16554e7d08dac3633c815d9bceed7f8645", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1", "process-nextick-args": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.0-rc.1_1531130903222_0.9870442928568228", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.2": {"name": "readable-stream", "version": "3.0.0-rc.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.0-rc.2", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "39bad6ad7741f92402ddb7d969774958b09657f5", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.0-rc.2.tgz", "fileCount": 27, "integrity": "sha512-X/afjHsbkiQQKyBlmUQt7hJRvkb14ClOWOiCeIALxNB9NGqv1ckDc4Z8Dl26FOxAEjxWKlqiw2tMEVqiE1VXtw==", "signatures": [{"sig": "MEUCIEyz9bhyAldgGxJe+tYofQfiF6VBBJ1oQSweNxjz7v/gAiEA6tq5sweXhIa0U25SvT6NLhSDQnJV3AeYFZmc1JujGrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRM6DCRA9TVsSAnZWagAARgsQAJwrxs6VJkm/Oyu+PO/a\nLcgLTex4p71RpSCN/mCZzDWNvXBf8ftrpbdhMUG9WLRg63VaxU8lvxMRuqKi\nXbzBX61fZUwYewS8Drd1oBedd8pWSfITZUytEXgczkCcpl01C2VJuCslaRY/\nVaKRcIrpoL2Ty2tED40A2h2nx90Q/nfrRogY71dsWGnueNuJWTP3VrlqvcrW\nuqQdc8sJ5FDBLCoG+KUQ2oNC4EJw2Si2JAOXRGYkC2a6j0TYxh40HkMiyAAU\nRBNG7+Jce6AMWLKCNOjzRSOe0JYzzL6aO0oPH+w3PI2qFBSfYGhaYf6Hubjj\nfNR6/v3lG4t3JsqXYkBO6tyDmp79YtPnwEfiRA9/T4beG2jLfRS2b8iWbKi2\nDR6gknpfjL6iHXze4YkkcOSsGB7cYzZAFl8MtNS6VhJ01vgVmDAYNhx5F+uf\n5pqfHJbaV9C2ADmacZ22W3fNbSaVtwbpl8KDLWSdRf/NCQI+RMJKf2/Ezw5o\n0q5kFBTDf6sYH/+AtknUVif4xcI0789ze8BTBJUYoeLJII18JVhbS9Rqx+9v\nh4TQBCcDTRW0nJFbBTlYGsq8d+h83Ny90RPF324sd4DOFaVSN6S6KuyCeETb\nqfa/HekDWqum/21e2kSlZkqitSt9I56j4qJ/jf9ko378jRen4/CPHj2o9AYu\negD7\r\n=9Utd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "118e27767779368ddaf70cd15479bc4cdce20472", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1", "process-nextick-args": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.0-rc.2_1531235971639_0.11101696487567891", "host": "s3://npm-registry-packages"}}, "3.0.0-rc.3": {"name": "readable-stream", "version": "3.0.0-rc.3", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.0-rc.3", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "4c61e5cc47a206021b190cee1eced28d7031e4f3", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.0-rc.3.tgz", "fileCount": 27, "integrity": "sha512-II/H0W4OXI2/hzSXVKzeZ0+QeeVuMccfuzYdVmXM3oggQm43EEcWTc6VoaAcbdFKqnKrgfGom8F2f9zHXss9xw==", "signatures": [{"sig": "MEUCIQCJcKrs/btkwpXjUlzChzu3A9iw8ys+4XhBOYklkcXTeAIgWCJnlCn165t37Y8kW7Rnj6F7ftNajrWeXpocfat1T5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaD/eCRA9TVsSAnZWagAAntgP/1CrrpL3cXfavvDwmPlT\nY9ArS+FyzUw2lK9tGLYQAZajIxZKE1Y/mp3bs4/gTibhr1rykXrwK9dFQa4+\nXjqBGmOt9NmZdYlaghTyyCMxV14jfDIroT+GPuzdSQrsOD2peRHMRhurFSXu\nV8b3FqgGI7iSaPVE7kghRp3T3ycd+4g1gAhEHhkxeqldg0xQftU1r5iS4hYz\nfDI2+BKqFiow6JR7ic+VquNTY5tyRCcKzYH+fPHNl8h5EorTXYg8RdB4GmIF\nSfDYD8+Qk1+5U4mIXDsqQLZusGGiZ4b3rxOTKh7i/cznriHbWGWqqpJNYXN9\nCK5/z8I5+9jU0JjrbYZPz7T9BPxWQHgjJXRdvrSGIS2sBm6vNw7RmcD/Qej/\nVd0LlMXFj+utwAEvTPwGETmliPBkTLDupq83rODDTyaR/FqNiFeoMQzuBrvS\n/W5DW42YlgkmkqJSY0RcJphijAtcli2ogUoIqL7KzsDJzJtFyQcGiiz9oBDP\nFRroRew6kKS4aNSaYmGFXqXIPTzEAlflno/D2jCJphfPCROEDl7QEiM/j8Dv\nOKY3HM6T2xl4ZA/TNOWC5dmtIVkKnNBP7YPHPVcAnTV443nVFNbWjltVU5Wd\nChDGV06lnKpWISAZfTD73AHtaz/EIZvBbRyR49wdQvQ9sUSkQJoQbhyd38B4\nsk7Z\r\n=zgzJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "113564863845973d5766a07d62bb799f6abb42a8", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1", "process-nextick-args": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.0-rc.3_1533558749681_0.19498015946064107", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "readable-stream", "version": "3.0.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "c9ccacba70df23b191233bab429e5fe47b149e19", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.0.tgz", "fileCount": 26, "integrity": "sha512-WwkR0zP3pW3uWHqUGml9nQ7jecmeUt8N+eP3+Kmnhqhdl2QMjuGaGgWa3lToJxM9e4PcBU9t1dkabunS+rncjA==", "signatures": [{"sig": "MEUCICTJxXOODDOGpt2nIwBtecqB0Hh7RwnZRX4DHL1Y2R6yAiEA0ex3drvXAZQjqmF6+MM4CBxrU6d0tVLz9ihsK/hcjKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbbutCRA9TVsSAnZWagAA5yAP+QA0qy11rc9RHBXwVMYA\ncNjlUR9o/Op1Hs0hZgeU4J9Tk+YXEyh+mJYdc4FZ8lM16e8wacIuPcz2ygD0\nFUMyoa8W6qAGvvpNURgOkI+tVUsbTUt7/oXfvTKl+CZfbnNdlpF5/K1cEiWH\ns6zpBDFslym1/mEG+bHvUqfrjkzinYVoSQa2jhT0HTcxuFcxSQZ3WfPnqqxU\nxrVSKRkiAW95VCUMP6ZF02dDN2hGje5sFdvBO6n6ewPmwzGw8apbv3ZoGAbQ\nu4nOPOEkCOuDSNxuAGeJijTGI7TbozkJfVS8/jjAkrpOEnv/YEypXAQQ9bQ1\nu8WzPYCarRtK72Sr8weVycGzSWJafJv8XkRm6ax8e8jHQqKak7r+zMOpH+KZ\n/mSe+ecThoWXlQxwqzxryAMWo/zEQ4S0SFKoCWWIeUiBULlPd0ettLxUs+Lo\naeD+uR5LiyvwUkVR3FtcWk1+nPrNeekJ2QW+cOskACGVec67KeQnsjHorpkq\nC/v0zhOy1x4wt0d6smcRID2b75e39m9ASfCAwLz5ywg912FwshJF0/PU3C9t\nNDs1qJqgTC/KE5to7feuAhDw71V07/1WQ4P9P/NiFFzEkMcO9OfPCgOCSj55\nqghp4pji+WP0nbEliG+vfWGfXr5e7rt7dLUe4c2EP65MufGOKUiYH4stKzvq\nLa8D\r\n=1DLZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "9004c8120f9fea9aa5b4058c5bd220db95c4ed43", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1", "process-nextick-args": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.0_1533918124892_0.4459895711414681", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "readable-stream", "version": "3.0.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.1", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "a209a46ad933f9c8146591ac2c56ca0e39c141cf", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.1.tgz", "fileCount": 26, "integrity": "sha512-pLwLmaW5H6SSNaLY4LAARgElGSv4mDUNz62qfh7ktO5EYk9BsJiPYXoZ6ShQR70WkOrw6jKwwOg699oV1medig==", "signatures": [{"sig": "MEUCIF20NvNuPLvxOnRGozyM8V6Ms3wSXJ1qDuRO3q8P6V2nAiEA2nFPiBch9ptH4kjkRpKoAz0mmtjWPwfIjLxL7/RUa5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcV01CRA9TVsSAnZWagAAogYP/itcdSTDgkXlaE7L+wRp\naXIysORnbwxxSQN6hFVE7mevcSMCfqSwnWFt4HgVV5kabLuEr2+wV9WxyQZS\nr495NRX6GsCgHkiVpDWWHmwWLd03x1B39HEAaqh0GS4vwwjPTJsrrqjRR5Ri\nKNsj/tZpOKlaHfoXczF7hEmxTLn5LZpv1ZJIYJ21lhIiW+WOpvZQ/4v7uQjk\ncToiW/eCNeZ4ZosBXtZxWX5I8eGJVxygjcY0GScaUpaLmSykfcHQREPEsIUX\nvE87sv4iQeMV182XLZ3a25/iIUQAYjGF2dX3w6V2FRlDDYn2ft/+EM3sgI9A\nMFiRF3KLKiHyskO2Vy+EDKoz47rpFAjFlgZ9bLuHw2wI6sdmWuH9r7v/lXuY\ntqsgwdAeMefGtmG9hr4QvhqTz2gCvFPrOvwz518rZLpF+7QYhZ1b60bNVJ9E\nEaUz8/JXzRgaJjPjLOdAEeZ/0pKks6+yJuzavAMHbyxqYwTpBu52zzXQpwbg\nBSJQkUAfDE4RtQosxIzS+ssR0Z92hVUB1OqS86Ne3/YkJBCNvy5JwERH4rKz\nWdE+buwXweQfi5ZEyl2bAgFkmWea1bzKR6U7+lqL4QweKhCnqtmKp1SLJz4u\nuxNAyJmgMuwJ69+9Gc5fiJ9hp3ZlH1ytzvNO4Ty0FAN2CsULJmk5DWHaK6Jb\nq4Jo\r\n=BhtS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "ddce1afcc6c31208d18d8b1b6c8d52d4b84d608e", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.1_1534156085134_0.269032780750067", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "readable-stream", "version": "3.0.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.2", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "ca0f4f8e426bcabd772df73096b8d3f6722b31ff", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.2.tgz", "fileCount": 26, "integrity": "sha512-E3RfQoPuKDBhec0s8fOsgFXDVFlEipbsheGBX/NNByfKMfzPEEQJXcLy6fJ7bDD820HG/d+eXQ1ezCj29KoAJA==", "signatures": [{"sig": "MEUCIAFSSkRYSELlIMBlTiyt13SoOZXqRGKMyad2+6ZPBuhVAiEAorkBsNB/tslfylDjUt1jUmW942tOT99CMtqN7IjI36o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeniqCRA9TVsSAnZWagAAV3sP/0wNdbc9D9jwfgAA/XYs\nsN9TT50tT+hTEtEn2vHogl0ZLsgGn9A11fAPx/Vh1OW6YObb7q6h/rLVjnAm\nptbk1wWDU6riVEB9RFfF6uaxBVVlNbmlO6pBm7kSUA5KrgZQ5iTy/ICAa+UH\nNgl8KokkL8LYOP2dxskaiG5eyYerOmgNSP/5ebad/ENYwUPo20KQVIvK4xYc\nSfIGMedQGwdiScraktR8Wt57OYp8zVS/yStQIya/bIDQ88Tdn0yj74Heifuw\nFbiWA2/Je+ehbmFWQbM23UIG/h4SBXXbaBpNKmNrrPoIKn1VIly5cLdUZmuA\nElgUwCr3IgWOm7IqoDgig97ImJfDNFx9tS8RfCC2JRyha09KOphBgyBH5y+k\n83RynyM47jNpr5Xp6MxcplUZqGLcfUlxF+74VA1ReKKuKuzBTPTwnwo0yenr\nvfw3ZHbsOPypDVBd+62J0TLpxG2LL3V33hCUkaBOiZOmf3iVkDPm8j778qrc\neTqMn5/awPJRAMF219J/R1RmyFSMGePJu0rqplz/z+i4svapbwZ8nsi+bxvL\nSXN1YsZTQZ9vGsbDjHtYRBIRdJO29UUtjj8n/W6FCUdZfCvFdX4SKTWdWkzF\nPQ+tjHujecm5o/VC9yicLMlyhLsEZAYPG2wdCbC9fWgLUaJtRTzkjWy86Sp1\nWBIp\r\n=ypjS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "a27de71ba3d1b49e1bb1661f98f664db7aafb341", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.4.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.2_1534752938481_0.9740590378360285", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "readable-stream", "version": "3.0.3", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.3", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "a4db8813e3e0b87abdc01d5d5dbae828e59744b5", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.3.tgz", "fileCount": 26, "integrity": "sha512-CzN1eAu5Pmh4EaDlJp1g5E37LIHR24b82XlMWRQlPFjhvOYKa4HhClRsQO21zhdDWUpdWfiKt9/L/ZL2+vwxCw==", "signatures": [{"sig": "MEQCIBKChT3YEg3Om2CuFtNXlyjZOHVKdFZ+RnHShQM4hgkWAiAWFK9XmbRcIwwP0sWnE3XSnagd3YesAyx9AZ6TRXYFMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblqmDCRA9TVsSAnZWagAAv+QP/iQLrXHzhx9EnNACfjEm\nuse39PvEktVDmTFUCk/SWcoTraxeQpLElIKY1EvEVpSsWfLZIQGZPlQb/g0I\nPpPy2lP/Ix75uSI0E5AJ/LX4tmeN4E8lis/pFRIRt7uxtXAf8VrpfoyIXdtG\n4ogcC/wq+vgXEWuhZzPDobQIahbDSHOWYyoaegWvBAlG8q/jGpKJl9WvpMtw\nZIGtYysFGgMsfP+FITM2mTH/f5QERRoYWeGb2j2/lm2mEYoxDLrOARITpQ3P\nBQC7+I5udNVHb+S/NfPlMaJUABGYGlWdSIPR6EZmHICcjcllD01hOGrAavnB\nfsVeXlW201YSOMw+QXmj2RxIvbNte+nEq/N5RlZSoLP2G+Pj0qqAgYllfoE5\n9dDc1GtqewhENp39MksVLhd9lYz/4QmuDBsfuhAtg8Mkl7IG54AsfjiA5dFM\nTKa5nKy1NhWwprfNnuUbp/TTd4t9WWqa1J8SJBwXMAfHYRa1n5JtoQtQGOR9\nZAKjk362kTSHtC3xksJSnY7VzmV4OvN4oPeb7JoI1eNMRKYhK4iynRQxam3G\nyHckeeqs2fAXs5LPHrKXldXzdYbVxru3ByzQR91xTaee8vDXzt0gImCsy0Zm\nqH/8WZWQKEJ3FemTDC7PuBYJbhfBovbb+rLH0ufGNMA+SpG+Ng+Lz9FrOYgd\nqOBA\r\n=pooh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "e890201f99f448c97d3d0bab36c403d8d3f30407", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.3_1536600450762_0.46887025954001005", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "readable-stream", "version": "3.0.4", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.4", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "cadf560461e40672e77b9e1904920a17250d089f", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.4.tgz", "fileCount": 26, "integrity": "sha512-Bu26LYkSFQY9PkY4yH5cg8xSkbAIGnKCJu9nMU7jrrOOHb5ERrbLMF3ACLIgODR01RvSjBeRcepwMUj0tCWFSA==", "signatures": [{"sig": "MEUCIEPNpi+gJITFKB8ylLnc38Gpm/93W2bFcSVaqE/ifkxGAiEAx3lJaQJV4DH9DEolX3c6nGGZfpCLup0C3WCeotWa4Cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsnGACRA9TVsSAnZWagAAoswP+QGpmewfGlFMI5lrHsZb\nFcBu/48KFgKgIDOtlz7Ak4l8rnvas7r7CYZmhognoFp4hXyOYZOW1DWnCSpL\nh11nFcHnll+c35nLWBEsOLmgs4g1UqNNShwEtyFArQAU4m7I3TNbkp3f9RO3\n5aqXrT2TzweG8W0cZAq/MFp0mWTismAawb0mvn080d05YI0rQvBUlBIM5fPj\n9YB9gKhCD5IdmJOs5VirONxQz7PomKuzSdbgTAdbUDVu+gfeKONNoXNCLS0N\nhbti5k8Zll4R/PK+9BmFc8IZQqHD1j6g6aWcXgazkjIsla25y9SMruxbVmcb\nIJG8TFly2o7GbGZuv7RaTzI5DEAiO97IqmlkchjyzWmc2rRPNvuTTDqULkSv\noqd3B48P81oAM4hDXNKntTnsJkthIlM+WzT9Q8fz+QNABrgydT+08cK6h3Rb\nveKQ3DDBift60Gzj6k0W4a0fZYJDeZj7CifGXqhU7HVmtDvdWHyplgeaexml\nkYLqSu4Qyu+Lv1Pt3ZL268TaEE5g3PbtOi3xPq03zGeyF6LacmmXneqMkBtN\ndI7JpR6n8EqtdbxhIx4lDtOlx9k9j6jZBh2PGgVioBWLLTopkjfSEvfMa6gR\nr/ZzzYSeOBZeZnTaTAyVU6sFOEECjwK7ZKIHR7DPfT+PNg5T2fsjxXIDLG3O\nnDoA\r\n=kMg0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "6a1a62271d96b291e8e9c3bf803b623da7c51ae5", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.4_1538421119647_0.36235329688501583", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "readable-stream", "version": "3.0.5", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.5", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "7ba2dd8cf56172bd282dc3a4ecd8eb638431e551", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.5.tgz", "fileCount": 26, "integrity": "sha512-GhwZX+/WaPjsMW41u9xKsec6NLsrx9d2a+V1qIK4V0gWfsqZC8a2VJDgMV1WQGP24zW65aHxx9NmyaLL5a7esA==", "signatures": [{"sig": "MEQCIDyuYlVBcEgTO3SiqVLJ/MXSLGQR5NhbpgItRCeT+kghAiBwCj/JnQfUILScm5ZWIqcXtvvECoUBGbuhHkMO07WfzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs3xbCRA9TVsSAnZWagAAKagP/Rl26MOVJLUT5FifwkQx\niBzwVwn6BItfALKJKHfZ2SAsJxZAJFTuFZWLiS4hLLc7Qy5gCY/q28qATokI\nqdgN3f3v4r4zy8t4L5uCfh1z2Owd5QPcS6w+wPPt7yfuAlWjgnoDn2vtLliw\nuSatu7goi4OAM4mDtQ6O5AL15Un/3lotgCBGyDBQoWI+/GLL9SQUFrfjU/zn\nh/a1E3DfvzjKryH07GC8OLONYM2OG1CNaqT0NeuHAQRFCyPDWz+PskG2arSE\n1Zqb6Q0/1bBfMbrZcL0uhvj38frCBSh4GR/JRPNKdgX6n7lnqXBbBGH2DlW/\n78tKpPFeP3cqga0XJDZJo9XT1PZkra8t+Dbw9nKdq0u7nHy4DEtrJ8hAUef6\nU/D8niDQGPwu9nj1SIxYOzG7PNd4spk+wmV+9kkBrLVsGIHk+QrsgctJYFaY\n5Lcvn9lEmbVly/uONGivOp9Y652/DKAqOBGuO4GEtqEI5QfXPflc4BW3yn35\nND5OxXpeoljYoLS/E4NBy7QEqKWNYK5vKvsVpPDUNSt3wlOU9l7kJsHYA/8y\nNysRBHMAMKHobmSFZfVEiewqjlez83I63PrKEjkbPAXWNJJmOy6ll1IxJyvv\nZVW3tdQrckvHG9iEV8kcVjx2pKJbQLPBn8DuUckEB3UZMzxerhXgM+t6+IFh\nNB3u\r\n=DtrT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "681958b8cd95d50b1d21e3deb9040f1233f0fdd7", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.5_1538489434561_0.3130838259776314", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "readable-stream", "version": "3.0.6", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.0.6", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "351302e4c68b5abd6a2ed55376a7f9a25be3057a", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.0.6.tgz", "fileCount": 26, "integrity": "sha512-9E1oLoOWfhSXHGv6QlwXJim7uNzd9EVlWK+21tCU9Ju/kR0/p2AZYPz4qSchgO8PlLIH4FpZYfzwS+rEksZjIg==", "signatures": [{"sig": "MEUCICSPO38Yr841nuLfdzSROJM8aHFu31Za8MAPkWLWuMXVAiEA38lu6aL7xRlOJcKOcmPHtSF+3UdwuVh8txEC47m9PJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs9hzCRA9TVsSAnZWagAAye8P+wRWe6g0oNkkRpVmywH0\nIG0tLDHMza/KTSqa9Y5pFF/zF7oJRhfztOP4F2tFN9TcjLV7pBemzPiZx8aQ\nE/18LTavHRKtIuOji1ZcIUjqmSbnH9M5hkqaLtxfiTH2Qqam0H6Gm0EfFYsj\n7g0/n5DqK4uUrNMg2UM0kp7I/W+5VN1DEiimeRBH2EvV2RNzXwTcBEvv+gQs\nNGlAOesOc1HDbRfadoZWVhgr5kMkM/Tv6weTgEtwxwq6Se7LUDx1j6nn+aCV\nNdbClWoxsFdMw8bR8enz7HF+OP3avBoXUsScnghxTGrIZfIVwlaVympVlVYg\n/RcUj2q4v/1lx53PAWUnTBq/gjIRXHJyYeUZoVU7rEe/6QzdMbywtzBHIEM/\nflMkgx/YBU0tfU43FD24UgCJ55NO73hrbIpxR6KQYNDHMJp1zlkACjZ5SO8K\n9CwHW4l4vDWZZgz5nhUWpibVhZwL8r8d+RbgOQVpYo/+RDTxEmJ4sckG/ovj\nUUIgR7ico9hthoN1gU0DLVH1yC6F/BL7CfhP/tNcYkKHP4fVUngW9RLS0dV/\n7swMi6XScTrKthp8m0NUDAjobgUe9cPTCoxFnm/gHuBpO0hWdmGMWCjnrjWr\nJlf81FLhudboIHouy+qTUUoykZaYijqpuqyoxa4o+8OQd9b8ftKIjK1meFq8\nU1es\r\n=eMYH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "09a71b16548054ff439ce3f716b01faf70f78c5d", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel --presets env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "buffer": "^5.1.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "hyperquest": "^2.1.3", "gunzip-maybe": "^1.4.1", "babel-polyfill": "^6.9.1", "util-promisify": "^2.1.0", "babel-preset-env": "^1.7.0", "deep-strict-equal": "^0.2.0", "babel-plugin-transform-es2015-for-of": "^6.8.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-es2015-classes": "^6.24.1", "babel-plugin-transform-es2015-parameters": "^6.24.1", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-plugin-transform-es2015-block-scoping": "^6.26.0", "babel-plugin-transform-es2015-destructuring": "^6.18.0", "babel-plugin-transform-es2015-arrow-functions": "^6.5.2", "babel-plugin-transform-inline-imports-commonjs": "^1.2.0", "babel-plugin-transform-es2015-template-literals": "^6.8.0", "babel-plugin-transform-async-generator-functions": "^6.24.1", "babel-plugin-transform-es2015-computed-properties": "^6.24.1", "babel-plugin-transform-es2015-shorthand-properties": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.0.6_1538513010874_0.7420527308054166", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "readable-stream", "version": "3.1.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.1.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "19c2e9c1ce43507c53f6eefbcf1ee3d4aaa786f5", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.1.0.tgz", "fileCount": 24, "integrity": "sha512-vpydAvIJvPODZNagCPuHG87O9JNPtvFEtjHHRVwNVsVVRBqemvPJkc2SYbxJsiZXawJdtZNmkmnsPuE3IgsG0A==", "signatures": [{"sig": "MEQCIB61Rs0UOJ++e4InKoPgQoTPqYPZEHtVk/mS7UWgx6ETAiAjT/FqjGcnMcwr5EcM+80HKYI00yHYH0nqTwyec4+BkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFkRqCRA9TVsSAnZWagAAB9gP/Ay4+WkMN9Dhg6diDFqk\nybk38EkbvduDWAvAyBbZcP+le7o5OKKeVRrfjPEFR1LkR9qQlJsg+lAmeCqW\ntdAc9t8A9nsfA0qmo/H2mMJUFfIOyg29s0GDzuEIBLvbtBhWgs/paqmTKdSr\nxv7XnHWpTFBxYwrL7tBnhPdx+CbIW1ZhhcbafzCDFWAQADMpi0FqvhpOu/Wd\n9B06XWuj1pwSo05XPyRzjCYg8OZt/neTvUReNsM+3QLTn1IGpWSdvXXN9Kfq\ndVPvn9HgDbrelLn0wRBGvzfhEO4QfAXorEX1nOzrKgW6xfPqkmIialdCjQcf\ndfjUzTdqpYg2qD7DM/HlsvnTaFDX+J6+K2PwOQdKDl1MmXT5oyyayI5eR0U9\n8UJd95w3mvBS9IXtDyv5aR1HX9jrMJkNxmsz7V+94Y/oVllgijxRD0UrpsUO\nZfPwydWcss7aZavyzLvuVLr80qzxlyJzO9fQCpOk181OEER772FJCEZuFg4w\nzy5jidmw7yDjWL9wDerkH3PBiOoJGMknPiJqUefmnGssfDjj5nMQSy8caJEN\nzrr50SJtw3GrwASlqU3QiA8YhDd9EunbKwtcGw9v1MOAHoDPg5bbAYS9tjZh\nX3Zj50jJrSS5yW86GV55uBQf/kmartDUUm/HzJIq2lxqzLACafCwtdcx9vwV\nvT7C\r\n=dpsu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "2323e49c499a75e144b0ca64a248f686ad8d24eb", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel --presets @babel/env -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.6.0-next.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0", "@babel/plugin-transform-for-of": "^7.2.0", "@babel/plugin-transform-spread": "^7.2.0", "@babel/plugin-transform-classes": "^7.2.2", "@babel/plugin-transform-block-scoping": "^7.2.0", "@babel/plugin-transform-destructuring": "^7.2.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.2.0", "@babel/plugin-transform-template-literals": "^7.2.0", "@babel/plugin-transform-async-to-generator": "^7.2.0", "@babel/plugin-transform-computed-properties": "^7.2.0", "@babel/plugin-transform-shorthand-properties": "^7.2.0", "@babel/plugin-proposal-optional-catch-binding": "^7.2.0", "@babel/plugin-proposal-async-generator-functions": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.1.0_1544963177442_0.0001817958167984468", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "readable-stream", "version": "3.1.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.1.1", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "ed6bbc6c5ba58b090039ff18ce670515795aeb06", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.1.1.tgz", "fileCount": 24, "integrity": "sha512-DkN66hPyqDhnIQ6Jcsvx9bFjhw214O4poMBcIMgPVpQvNy9a0e0Uhg5SqySyDKAmUlwt8LonTBz1ezOnM8pUdA==", "signatures": [{"sig": "MEQCIEnd5Ykxl0C33fO+4+3QKF3Kl82tyGOTW9gK8W4X+EnMAiAOkUFEfa4pgovEmnHOuzgp7ARsEs23XJdVVU/aZWRhvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcH58PCRA9TVsSAnZWagAAtM8P/1bFAUi0PpX8w/6az2CA\nipeEKhOddruD83Q/uAun28BHuu/uazbdbTuoe9gCdghijqQtj0zD5/8T+YaL\nAAVwevhBqElEvVhhUgu2y0JIzXyTncxD9fQh01a9yT5QGzBut0xDgpjGs4r/\nKVeOyItEZIFiW2lePnMFQ0m1iwxTCrE+1p8SWC50nQ4sm+VXJW4HL7FtZwDU\nBbWEsYxSSQA+L1e9UvoauiuYkrINAy6WaUPs1/nhi1yvUNfgUZ0VfXbr2Awx\nsdZSypkWTCZVs06hZbl5McZsOOkNM3P9RLeLe3eNxrPMsQECnb5fjNVRYjwU\nQI1OHYNh54znj1pc5UpPpERbOPWsQTWKz8uoBGESebN/+vPlLjfHJ3jxXS69\nh7cXWqSY5xlhXXzXf3JyprM/tkk1YHWidP8H5Ve8o30Y9+KYJYj+1D5kJdiF\nHdnzAZlqZKl6OvCJXJFF89SvFfllabYFPeLwSIA1ZxEQrgoQw+NEzexlh6m8\ngkoe6f/FdX8EE1n/ZmBtCbnJqco5LRTLa6lGLbOjtCXjfvpQ2e2kLZRkqHWL\nahgYW4AXmmIHpAGKN1Q2Wa2KSe7fBd/dlMkhJ24Eh7l8qNQ/CCczt3s/wtCA\njcjZ0HfyxKjuWITBG0ysjRGx8hl4PrSJyMZvkiLMmT3OpczHF9Ucv5hoQcm4\nLk4J\r\n=N1Si\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "1df8b0cf67af5c3f154d37d88085f4b9385a4738", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.6.0-next.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.1.1_1545576206962_0.8004316356153864", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "readable-stream", "version": "3.2.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.2.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "de17f229864c120a9f56945756e4f32c4045245d", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.2.0.tgz", "fileCount": 23, "integrity": "sha512-RV20kLjdmpZuTF1INEb9IA3L68Nmi+Ri7ppZqo78wj//Pn62fCoJyV9zalccNzDD/OuJpMG4f+pfMl8+L6QdGw==", "signatures": [{"sig": "MEUCIFvVidTa5rGlr3gj4Z6KfuvC+nQH9DtliJyEx33Vh/p+AiEA9nehmRrMf43m7JCKcyG3htiokO8yIcNfzUg+ib08naQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceCWvCRA9TVsSAnZWagAAvS0P/iD4G/B6BvR7ddQXfzqh\npN1Z9C/JB1YrJ/z393UcxdVx7DGJqtna2FHLSVHdXH5Mk+JUONmJym7mtlDH\ngWQVvbqqWYRy9XTBsFeEcizT1UdVXnkhF5jJJe0bwc1uhPMR1aRUxI197Oak\nkJf0QJIqmuaML/GRzcGOexYIX4ruk4h2qVEucBaPtT5g08QCu1UBzF1dMBhf\nXBi6JIe8Q5t64HWTz/fSby/Ci0+EvBSCLzyna6Tdo2NAlzxEcsjdk4q4zs3I\nph5S8NvvO41jLwoJtDZqJYB/rCsqcliVWRKLU9bAtWVHFlgBEL4zsvX/yYTu\nyHuuZ+RT7XuuoyTKGFXa3Ta11yNQBqTPgWch21diNPhzPf8X+gIPXeck0Fjs\nXOrDvQie8yKHsz0KYBfMwULw7ObrQ8LgSCHnFBE8qtQcJ1IdHkOyIyO+eNEo\nvtUMVuNrPAxtfVu8EqEncNIAd+n+7IQP7t1uIGCLGXlksQ1vK0DRQBE6Lmwm\na0y3OxVXkZq4QHo2hmZ3DfJr/QMvyJ865galbnrXOp884ULZ0XrN/+2qhSU8\nC4RVBf84Qcf+XQpT2QRxjpEepipl7MNRlr/lSj7SFhS22G1EUj+MRyBJ4OVP\nDUYSY6vm0Jpp4ytrEzFEE7NZYM0SIwg2nVQRA/IiXmJmLz58ZFBm0Cw1djOA\n5+ho\r\n=dyfJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "5b90ed2c1141fc41cb1f612eb88846473369298d", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.2.0_1551377838741_0.14789233101133448", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "readable-stream", "version": "3.3.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.3.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "cb8011aad002eb717bf040291feba8569c986fb9", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.3.0.tgz", "fileCount": 23, "integrity": "sha512-EsI+s3k3XsW+fU8fQACLN59ky34AZ14LoeVZpYwmZvldCFo0r0gnelwF2TcMjLor/BTL5aDJVBMkss0dthToPw==", "signatures": [{"sig": "MEUCIAwzRM3XhzxOIUW8LExgLLWeBtj4G+/lfjZOpCILNUAeAiEAnjAmD3x0Kq08iiF1tbZaXEsmiPM1iBHITHoBuVhhWyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcobmvCRA9TVsSAnZWagAA06oQAIv0IHngzLHdJhAlPrZa\nTx3WOvVk9H6VSI7LC8SbL2sBbUVqPt7f2QMkpEy82UcgqDul9gdLqTCTfE1Q\nKGtSqj9Z/7at60uIkNURyagI77opIA9iMEliAH+HrlWO0eQS2akZ3+EPzhtp\nThCYXgIaIhonRFsyt8pOX3YzPOF3Yu7LASULBBo+PvsoeR3X00W4EjM4sMoR\nxJ2F667MSSlp3MfrSXFh5BaanseHTkv3HLuvIaY8okFtwAzqJjsHRU0xSXII\nKuuy0VsujhC+0sMMl7bpOl6OatkX1YDmnB1GL+8zUEBDvZLO5RwSOKmPC0ft\nG4eF0mVj/ZZM5g7ccepsAaacBcOHialJubmxqhwR7QtOVG+61PUw5b1CFQYQ\nHz/UpgY10wWA3rVf19vG8spdjxb+Ym6Iq0TiLlekMv6ymLXlNAVZdNuXZBfc\nfn0lQQizxquLGLraLnrlKdPbkn5935G3cjAlvDpCcTi6G9+i9atP5qFiOCA8\nTAqP6RowjrZxUvEgUBSxMleCck6pfQ84r0AvTzDRqrTHCFRYpYwGY3tGGDL3\nstW2/Cj+45NMbX7BMTKlNHgnKh3pkUJ5KQuTPVlBrJJcWwxvIlt8YcnDCn02\nGFMjCS1w/u/M74dKnayhcfbUu5vm+Gvh8jHJrQMxaGAbeIYDpF+qx7cGnwzP\nfeSD\r\n=oHvo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "49a1c4fe8e6ffd3793a7001aa702fed255264e87", "scripts": {"ci": "TAP=1 tap test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -j 4 test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^11.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.3.0_1554102702084_0.28562879483481285", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "readable-stream", "version": "3.4.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.4.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "a51c26754658e0a3c21dbf59163bd45ba6f447fc", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.4.0.tgz", "fileCount": 23, "integrity": "sha512-jItXPLmrSR8jmTRmRWJXCnGJsfy85mB3Wd/uINMXA65yrnFo0cPClFIUWzo2najVNSl+mx7/4W8ttlLWJe99pQ==", "signatures": [{"sig": "MEUCIQDxIbyPtfKtNG1KZHfG3I8fysTvsHobaX5GpacFX2NpQQIgcbA+5DzvZ4SF9WQDwbGjNwV7moiGklCAY4cRTOgWORU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7M3DCRA9TVsSAnZWagAAr+EQAIstPU5/5ZkzFqB4tbQ7\nMQuZ0u1qMYSXcLQ6Nt0W8y2g3eHdr/r0urnUzGJ7b1WXDYK1t6R5WSibnENL\nkbAbquY6U+lyYli8kSKqDpbgcTqdRkF3xPDtMcJJ+FgD9GrCR6med5JzMlvS\n7veyr5hbYRefajAXWuFKge89QP2muoweT5U2PVJM95irTy8RVihzRO2yRddR\nfs3keKfaCuupgrol1o0bFAY0TD3I12QDltmTD4pIgDxSNFyIOfnOtT63DfcG\n5Oslj+G7rHA5OTtFbTsDVZGn3pQ2LExtcWCT3tgs5DsbRFWeIY9PTXW2wmda\nNHL4+OwCuq24ilQ3sIg8CK6urRrRFU9GFR+rZIzlk2JEQWwltlOjxWGn22Ne\nHugQRQdCtOu7ccRTdfjP+sDBs+PZ1KnvYKQ4Lf+uVdvO1AwZtAuwGBEFlyY8\nL3DMh4wlerd3+hClVWEh+ZkzQLCfgue77MoSCRA6kcF4SLrs+bnwR931A0JV\n3Ea/kG+7k/W88kTtn7aJc4acVvjfJQRU5V6N6ZfT3sJIY2mWrUYBLOpI0IdL\njIhYRFllRsZkvNe+513STsb7GixI7AnqDOCrPhJunhVX/AzVFsEnHMNjjD4o\nr8zefZKl0IJ25eU62/e0BvXj2PtIlsQSlTLgwBNZkQmMKarzyBh6hn6/IRnW\nsb2J\r\n=ai34\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "4ba93f84cf8812ca2af793c7304a5c16de72088a", "scripts": {"ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^12.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.4.0_1559023042434_0.21796649445302085", "host": "s3://npm-registry-packages"}}, "2.3.7": {"name": "readable-stream", "version": "2.3.7", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.7", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "1eca1cf711aef814c04f62252a36a62f6cb23b57", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.7.tgz", "fileCount": 24, "integrity": "sha512-<PERSON>bho8K4jIbHAxnuxi7o42OrZgF/ZTNcsZj6nRKyUmkhLFq8CHItp/fy6hQZuZmP/n3yZ9VBUbp4zz/mX8hmYPw==", "signatures": [{"sig": "MEQCIDpPRyHLJQRjXB93tiCCuIWpzD8W1s2XA0Yr2XVldqskAiA0Oop08KlIrIr/t0kln+kKOkCudSIweaZgm3QfmDdRmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEhlDCRA9TVsSAnZWagAAUqkP/33VFphjNq5vk5/8TqHZ\n6T6ItDSwoY1VyDEuN26uFHRBZRXztM5BC5spq5q8h/KcQiVxshlTjL+PoXI9\nFgXB1kkm57NY95FhCYcVlDw4QmBl1IRlzVWglN/0J2YQ+wSpYiXLXTcWSAp9\nPt5Abrb68H22oI8+KqQ+T9WTFyDG75yTJHFWBQyXcYG98OU/v0bk3nnB/Oh6\nztpy2pD5EayiEY81YTJ6vySHIKYxAySWdesIO9gQKwaqDdQRmfvsaEra90vf\n5St+XpRx1M0oPFCBjjj6aEhRgvq5rRm81GUQOKFvIWZacFFgdwpqJn1kzCrM\n6tQKONWApacjucn2FeAOwPno1HRQ3/T4NzWVwUcc4fuwi6fXa1E0zcalRSws\nlfuL+mtQkxv7fPanzqZu8J542hgss81ZahaHR1p/d705GTkdiZV3CYBN2O/a\nQDSCgjPUAQpyfOUlkSsNnB8GNWGU3hVOabsSpH3BPrGlMdBm4ML9ZiSaSn1c\nqb/EldTv0pGkwfx4+uOxH8QXMZLwd7nKMluEoBZQtFeAEIiaFwqgV1rB9Pi9\ncBPAMrDIlwlHV9CxOHiH/WsVBb0Uc/vBeRerEpgaEP3iglkkF3w4kaD3PRhG\n9GTiyJcjpDQugMRZehfcQODpj5mlYpYgSxK1xkRQ137BTZ4Px4T+lgYM16Nl\nQ1gV\r\n=Ka0O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "2eb8861e029107b7e079e22c826835cad6ae7854", "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.1", "core-util-is": "~1.0.0", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1", "process-nextick-args": "~2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0", "lolex": "^2.3.2", "assert": "^1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_2.3.7_1578244418937_0.3738956004243521", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "readable-stream", "version": "3.5.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.5.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "465d70e6d1087f6162d079cd0b5db7fbebfd1606", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.5.0.tgz", "fileCount": 25, "integrity": "sha512-gSz026xs2LfxBPudDuI41V1lka8cxg64E66SGe78zJlsUofOg/yqwezdIcdfwik6B4h8LFmWPA9ef9X3FiNFLA==", "signatures": [{"sig": "MEUCIQD8MzRhcmp/GwKNHCXkceF4VAoT6tFUdlFueMNJZ+BPNgIgNkMLL4Q5oA6YFrKNouM3h5uNYY8zrhXsnuQEpUz49ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIeebCRA9TVsSAnZWagAAjkEQAJAnRitiw129NoCfZ9J6\nIhEfRdiJ/NsDYc6GEjzaqJ5WR+yOFBlB6honP4KRirGtSbxe9AgmucFSAgW1\n/DkuwrAcZypr/2ZkB19DVbtAYGuMRtMcn+e3ZWys1ifRdf9xHDpBcPWOmAB5\nLYoLefqtY4ib6c+/HaBEAT9PYOj8s75zJEa40hpq4Bk8QrO3l5QD/aGY0ZaW\nUifqtkvgY9RsY9Rng+b7w4edktFpstkbW3k96FAe60l2aqCBX5LMf+SwR3c/\nGVNIQEESR2tX/q74KON4ztZgr5u7JegH+Y5oPutQhxtHqjPSSig7kcFFRQc5\n6XCw341TLzJP0KcgCNv8+dlnmoVD4npW0pH5eq9KVCV+emFS8Zkz8ID/KEKm\nWmjYrJEMj/xOHeRqsursiD2mvyCYBOyHspnQF2W3m5qqqwnnB/f+cCAk5N00\nsdLLvStx0/1ZdyV800GqN5XLKtFsXQCUB/dE9KFvE/9cRgfGWEOfvvXE7PTu\n6JCiUXMmQaoSgDpPk99SoIK/tC2+ee+QHpJyaX0bjFqpCZwYvj1ZEzrgVatm\nC4mbOYmqAvPOZwhCirrwvC6Uz37GKQD9P2kyZbuhVfeAly5TmAgXR7Ft93QN\novhP03+SYGLnRahMDEl89XUC+srU3/ClcOHD6C9ycJ6nLjr9w8f7r7gCZ/TD\nDLtT\r\n=nZWz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/from.js": "./lib/internal/streams/from-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "7783afa2660a02bd17438af008b4066adae1569c", "scripts": {"ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^12.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "events.once": "^2.0.2", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.5.0_1579280282704_0.501421529787142", "host": "s3://npm-registry-packages"}}, "3.6.0": {"name": "readable-stream", "version": "3.6.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.6.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "337bbda3adc0706bd3e024426a286d4b4b2c9198", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.0.tgz", "fileCount": 25, "integrity": "sha512-BViHy7LKeTz4oNnkcLJ+lVSL6vpiFeX6/d3oSH8zCW7UxP2onchk+vTGB143xuFjHS3deTgkKoXXymXqymiIdA==", "signatures": [{"sig": "MEYCIQC32vDJWNJwBpMukTU1+hiQ6pyIbCNqX9FbpyUThQl2sgIhAK4InEToXSLS+YdQR7Dss/71luBY4aK1r2FSXQ6RfG4h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRaaNCRA9TVsSAnZWagAAZ/MP/Av2Rq1HzjqXLgZy+I3k\nraRg4I0h+1aG8Bp0tOZ06OY+VeSyJg9pff6Y7VxTQOFLQW4q0dkHXw90JTAE\noX+OAImYKLS3KqzZjsVaDHHwqBMzrLZqrWRTcNuH8zFB1fZr/QqQFwZmmqrt\nCHw/6F3Q0fNtNw9mF8LYVnKs8NRzfo61C9LmUfOj/+aroHDMxBzYb/ZLO5rw\nIIQVyk8+4KrGfUa0wrgQJ7XXRl57cz9ZJ4ZQS4LBa6RGuJ/vrqAmYNJbFI6Z\nH0cVNHDn9IPv/FmfJdPyFqQ4IKaEIrrxcHa8lAYW22omVRSdA37yzGpAf76N\nQnUiUFiBXKoVfM+Q1FhhMUICsuHmVeGHh8wML5fjuEas3N0Y2nGxcYMKef2I\nmbVNG1Mv+/ZeSa7F57rwqD4VypMKw4o7qCbZdHUL1e0jO1r/TDjZzePKKbZ9\ndya/SzpiKlzVOKbkiRkFdL1OXxRThZB052q8B7KBJJdiT9BZut46nJG/OTXV\nkkTGp9Oqep60+wd5FVqTySEfRIpS4Txs0dk8eqYDR32ZElEfexwOoG+J/Zwy\nMQAkvM/OJi8CaARjz1ddaMmJJRNjrYupPKNoQel9yi/0J23o3Ll0/dbGXL28\nt4gd2xsevVNwci+AZYfn5TuDy7N+pyWSp/OoVg1PYqEh5iSe99q4gJjfAwY1\naOcI\r\n=Yu68\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/from.js": "./lib/internal/streams/from-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "bed7ffa274f5b9e6d0d5c22369e6fe825ded03d2", "scripts": {"ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^12.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "events.once": "^2.0.2", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.6.0_1581622925333_0.5761297287614828", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "readable-stream", "version": "4.0.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.0.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "da7105d03430a28ef4080785a43e6a45d4cdc4d1", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.0.0.tgz", "fileCount": 34, "integrity": "sha512-Mf7ilWBP6AV3tF3MjtBrHMH3roso7wIrpgzCwt9ybvqiJQVWIEBMnp/W+S//yvYSsUUi2cJIwD7q7m57l0AqZw==", "signatures": [{"sig": "MEUCIQCUmKcKdcFcNroC9tdK+hTMG7iaNNIV6GiNMlliWG/EjgIgBMoaKFf7LYbk2mHdXj37Q0se8vGsqlsD6SpJdttOlk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqQG7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmmQ/8CpYDqejehb1Yd9y17k37XttHVaHYZ5tp+lo8VW5jnjdXbk+r\r\nT4zTXK6rATtJzNAluz/bO/nS7em2fTWg1773aDj0SdN/TLk7KE7FjU8S8Thc\r\noktwPKsZEpFuqOU6RoWrH1GOmBGt6Xb2gXnh7r2KZToPGSsNxjQ09atqcwNV\r\nwI9k/TSgxUaFn2GzOadN5xHhnkJvxb4LDOewYu54PeLL9G9x0N9PdlsDd7Mt\r\n06gF5z8puRdnwBWD1wNh0pDrQrXpbQrN8F88ZOwtPfSc5FZ9QQuchNIHmGkY\r\npJS5SeZhVlpSz6tZSD5I0rG2Q6HA2AfkS8rs4JUFBVHK3oiO3ZkZ6RWlC/vL\r\nbzq/rnvnD1F99TkxPkOYUO2ESxYzMgOoBirvPKtAJyj2LWAoQWZJ6YrrjL6t\r\ndirQve5/F61iY+DUJ6KqJiXVaTy/w4EgTuBdvTOAp+NGBeCodcobyMYOmKiT\r\n/M7CsQPbChceCqon3lTm/2OevCqup/5eNwGMOOdIVorHOHR9s5KLLaxSAJgx\r\now5f7JV65qSy0AxGn9W/VeZPefm/2RpnUFcLFkeHfzELSzE0/P5uQ/IrBN3h\r\n1XD5clHtXo4jNqkBIAHrlmNTZ4W0P+eM4yOmEKnLLOGatqMUSXqoz3Hv+3QW\r\nP6Swir0P214r7dBStVD7mhmyvtJ7TZpddDc=\r\n=vS17\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "49a45a92f4ccacd6ab496c5b5143b117bea2fa8b", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "buffer-es6": "^4.9.3", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "process-es6": "^0.11.6", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.0.0_1655243195143_0.3965491447099485", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "readable-stream", "version": "4.1.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.1.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "280d0a29f559d3fb684a277254e02b6f61ae0631", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.1.0.tgz", "fileCount": 34, "integrity": "sha512-sVisi3+P2lJ2t0BPbpK629j8wRW06yKGJUcaLAGXPAUhyUxVJm7VsCTit1PFgT4JHUDMrGNR+ZjSKpzGaRF3zw==", "signatures": [{"sig": "MEUCIQC6EzlmH4DUxNfb0c64q6dABxMxVZZhHF8ETPHdCOWywgIgENPnSJRrRW7U0Eq7igDxu+MFt6Tu0UOX31X4uAmBZyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiy+PtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxWg//UCPazknHLNjkRrBEIrsT2bvtMTV/O+tjuD7xNAiTgVVzPkwl\r\nqAwyiYCJ3Fq5Uh8rbPtywiXHkGKI4D/AQ426/+l0MB2SYbI0FXUViEsLdeZk\r\nHqAVtSnqu8KZeHtxpzZYQiZ6kajJzQWJ4KQAfa78gDSDXySd7tEtCIJTuhK3\r\nRtSb8xvLbfNCaWmsfg1kALYZWVs3QEcNrU8FsDm1rqDmdaV03bBFdI9CpF9O\r\naOPtZjnIEN5xPNsfHz+i04I0KWV+5yi+DW6d5IIWxTQTIjG/mTWLOfYqcNiU\r\neEMNI3WZC5aX//w4epabdtUa5dWAGgcNVNNXI2hoVq3T7i6BmWJN84V7U8TF\r\nkfW/RWrMFDge9Be7Ch/WTSHdaKK5V+93BuoZ3xMD5gscVrcrcCBamhAzr/Xt\r\nZ9998bq5Z0p17tgix9+Bu9isW0BkIgmpz4KtwEwn6Sh1pV6ethoSJNyrDh6P\r\n50D8yD1W8/cmI+mLjcnFGRaetgD9+x0O/bPnkS0jK34t+7AlgzzovgL9ApET\r\nsicXtlkZunx2t8zmItxe3VbWmfN7sQh64+GjzSAZ6py2ap3vX3linDZd4Q7J\r\nBXNm4ClVvrnENpX2wfIGJ2HY9qbqFUnWMm23EortNtinIRbwaj4XoBdvLH8t\r\n7aLtszH3cvMV5r+uZF/At+93J5pqo3BteNI=\r\n=dFQi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "77e0378b1a8a46c42fc3fce43969b83abf69ca36", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "buffer-es6": "^4.9.3", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "process-es6": "^0.11.6", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.1.0_1657529325757_0.09438910921852739", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "readable-stream", "version": "4.2.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.2.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "a7ef523d3b39e4962b0db1a1af22777b10eeca46", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.2.0.tgz", "fileCount": 34, "integrity": "sha512-gJrBHsaI3lgBoGMW/jHZsQ/o/TIWiu5ENCJG1BB7fuCKzpFM8GaS2UoBVt9NO+oI+3FcrBNbUkl3ilDe09aY4A==", "signatures": [{"sig": "MEQCIE+WmkpIamp55vjM6Tm4ENX3+nprUJen2Fztq1/MHbu4AiAsCgK25J/fTYqeAi3gbOpgcpx75J0uCc0ZN7c3X2HW8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLYbwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH4g//SRJYMpI3G+SdwSAFTqxS0HKmGeUvXJDC10OMhmwn6LWGtQ+8\r\nckbxrtl9/w9iER77fu9PIOBgHokjCAtBWmIc1rFBEDzL72iHlGAcpJK+9wy2\r\nn0jgQ0OWmTHrUAZ5/Yj1r/7mNGACfrsI34xHj85YVShqK7iNwFdA0dW+MSBd\r\nGYVrLEuFLYpLO5/1d2U3AJp/IS4WKLzFME6m+1CPPMFnnUWgqAWyJYkxX6mc\r\n/34tKJhM1i/XSs5EE9PN/clXIaK6gjb4W5kyWCYbC4Es4soqAwv1bste5iYd\r\nTZrxV/x7QWnkeO12HMfexAOeYte8wh3KCcfPMj0ugsfYagKRil4WwvkQrSN1\r\noGQ7vTrlF8yLk6lQFLXj34bBBrN+GHOfnnSchieqohJmoyd/2HMUoSgiVDrz\r\nC6QthpPwNeHWkoNBzCzgebRFkwNHt69h+piut7w+6cGvJX2vH1/uvj2XGVAw\r\nAuYhnNkMNC884hcFfU1nXs2S42wgIZ44VYeFD4eYg2G4mTFyai73BALc0beb\r\nG8b6SrXPn2hO7IvZIC2VJz7H6XvD0zTikaXEmLob2krX+VKLuovy8TKJ9dqj\r\na96uHr9unc/xH+44KQ+urYUO71SIlSNb0hsxTfdrILuuofQfPkT9zkCzAE0W\r\nkfxkP2HrC6Gpva/lDRkKNEU6CRxGL85Qq3E=\r\n=ZSsB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "7fdb3b4ec5135e586d5c6695f7e08d04f268b1cb", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.2.0_1663928047937_0.5809596116946465", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "readable-stream", "version": "4.3.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.3.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "0914d0c72db03b316c9733bb3461d64a3cc50cba", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.3.0.tgz", "fileCount": 34, "integrity": "sha512-MuEnA0lbSi7JS8XM+WNJlWZkHAAdm7gETHdFK//Q/mChGyj2akEFtdLZh32jSdkWGbRwCW9pn6g3LWDdDeZnBQ==", "signatures": [{"sig": "MEUCIQCMngFMAy7M/Av1xu7U5DfHlilsiCyjDEZ67mqhcBtCMQIgf7Yeg/9pYuJpPt9WLO5U6Az/GTVYkLaEA+hraWMD6A0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsg4gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9zw/+KAf/C4TMWWQIST78IYR2lzNvJ6ZlA9bFR/ba9NHNtxrzGTaF\r\nz9lqKxd6P0RzD2BJvAfGXaY3h30FptEVAk7uyNzgt6VHKiNeBuiemVCiQbFI\r\ndnshbyhO42esvjCeNMBsqTwBa7gt4E/IZCVThQLUUqreYyqAlSZA9lCOFWdC\r\nU3hshDiescvW/pXvMuC343FZGu+cUo2hxS5XAWT9NylVcrWMQAdIYgnwZyo7\r\nKoC4NRv/PmMBnz3lvDMw0sQ+6RUhFX/4tminOeWN47tn+4HNcOjuf1MYuaGA\r\n6jwpIqYIDfPqPppETxatEbHkA41Fv8k1g6HBThrycYfcm66y9m+3Ldw2P4N0\r\nnM/sxsZKo/VF6tafXAZfP7/3EmkBTLmtxYHyKXZ/lVz67FhVtUQukRMEE/Z/\r\nIsB6uncFiFjByoxuE4NatKdMkspollib9rQyQx4xbEpcmf3drEAfhDvIBb4x\r\nKmpeNsrfK7j9djcLfuS2YGVSYgojH4V6qQnVyM7emgfMFXRbxu1pfnYx2J9s\r\nXSMsmJx3u0Tpw7qnhp/B0ByzZXBYoe44CxbYqioY9nwkJBJwsyCEr34C4noT\r\nEgRM/vHo0nhlmL11dR5fXkYeFiLIitucavXQgao+5RJ7GfWscuLAaNXmunkd\r\n3mIpJzjKtxhdsc1iheXf1OEbh5ZZbAyR2Vc=\r\n=pIDZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "232e71154dbdc34f6a97a0449f902455ec495373", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.3.0_1672613407996_0.006939253823645686", "host": "s3://npm-registry-packages"}}, "2.3.8": {"name": "readable-stream", "version": "2.3.8", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@2.3.8", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "91125e8042bba1b9887f49345f6277027ce8be9b", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "fileCount": 24, "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "signatures": [{"sig": "MEQCIANo03Ui9GmTTg6ggMBtQx5q9qjkQQ60U8cScLRhP2RrAiA6eJ1LZmQRIcVZm8QuZfPsi07CZGtueFi4F+xr1n72tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9zoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkCg/+IHV/F8d6OLDjFTfUzbQM/I558PM8SWrXigxb8Y+Tfr4l7V5E\r\nIpp/3jyCILm4/WY27bkKvE6/iiRsiS6m2Wvl2tbwTLR3kcfiUNPMLezD8WVZ\r\nAFwUvZSYnd79SxkC++/JTvC9qcnfcq87apsaO387nJ0Vcjydkd1rj82EVx+7\r\nk3JA/msFr8JHoZ1LSCCe3gdQzsIsyryO1Kh605+CTgJNh9LkhzFut/zMxpt7\r\nI0Fmbk+VE7WJjTZH9YOZ07rkSPVsL8ZnL0RL6vacO3DBJAGSQb0iaK1DqNge\r\nmbIq+jyfaxgeta4VmEQLnJzF+d9LrVZ80syEneoad64hQY47DxQLa4LaGwMB\r\n7AfzL3Wx3fk806vIf2i3V7VGefzlRhF/Ul8VTEGCdWOTcWKfff3Avcz44zxY\r\ngi+laqrvmIlBXRU3r0dGB9wMmziHHS3LLSEIPGb671JQ/kAMwlbsOaKytNZA\r\nMFu21BrVIZswXvF8vJ7pjPi1927rbSMnqyp/pPklEtPlbnrggzAPeALU3J5t\r\nntPxWeVz7K5eQrFlBGv5q9ued601zMzM6rA59dJ2oa+CGbZPXjcGpifcP8Jt\r\nFJdQlP6Kj0KMkfQyH0dUQjweg0+S85ifX/+AOntzunLCxBAzWreto2bZx1is\r\nd6vHYcIvli8HCCuSApAeLKZy1XWuBm45w+A=\r\n=5rTp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./duplex.js": "./duplex-browser.js", "./readable.js": "./readable-browser.js", "./writable.js": "./writable-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "gitHead": "c85db76d4c41f64fd082c9263c3a918bec6f38a0", "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "test": "tap test/parallel/*.js test/ours/*.js && node test/verify-dependencies.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "14.21.2", "dependencies": {"isarray": "~1.0.0", "inherits": "~2.0.3", "safe-buffer": "~5.1.1", "core-util-is": "~1.0.0", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1", "process-nextick-args": "~2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^6.4.0", "tap": "^0.7.0", "tape": "^4.8.0", "lolex": "^2.3.2", "assert": "^1.4.0", "buffer": "^4.9.0", "babel-polyfill": "^6.9.1"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_2.3.8_1677146645963_0.8011812824012474", "host": "s3://npm-registry-packages"}}, "3.6.1": {"name": "readable-stream", "version": "3.6.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.6.1", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "f9f9b5f536920253b3d26e7660e7da4ccff9bb62", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.1.tgz", "fileCount": 25, "integrity": "sha512-+rQmrWMYGA90yenhTYsLWAsLsqVC8osOw6PKE1HDYiO0gdPeKe/xDHNzIAIn4C91YQ6oenEhfYqqc1883qHbjQ==", "signatures": [{"sig": "MEUCIQCg7wKRsHVSarXSabTHGbuJ7DWCuFGY/n8qVWkzQbrAIQIgB9infMyKJLGVhnY6db+FEQ/dt+ktUfL+j/fjB4fq9Tk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9zq/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGbRAAi2G1eO68KUjtdZMY4pu7GoIfeWWcptjnb+4jjiXABmoAmJxa\r\n7YPvGLu2R3OPEpEPaPP5f/xX3ffqvmhCXo4OhMhQFw8dlSRSkRNVNpEQUpru\r\n9AtMs8RmHtbLQL+pT1OPwaYFLx9Zsv+KOgs7yufUw3B+v+Sgj4IQUZbRpWXp\r\n0DVRtfW5AphW5kUCah56PHPkkYWxki7M0s0MILZcIONOmnizBcmev39A11X+\r\niW7jyvn+aS5cZTi6UOFy32jUkYnWWvMx4BIZ6tCQeK7JpYdI1DiyYZTnsnKN\r\nYWMM9jB8NSMjwZtd17gwSIUcRxEK1FFpA1rMwuP7kmSJY7qhY8b3z/E0toZN\r\nfpysUR230jgSB6LEzeXufR8IFNqD/csH03DHOfu2lziU7Gg/8iXWahpRi6hp\r\n7lDypioZ6kdAmJb25f8SUmJ9Oe2tYIuWiwgso3Ilr1uWe1paUXYYIevZk1iY\r\npyAdfiGPEU8cR++q1eoT8dCouFsnlNfKPVhZSQViCNcCWVCNEqjWcjls1JZs\r\nN+uTSiu77A6UiysQ1YcgPPn3g6wlLqPzn6A7ipFU7cgoYTA4wrSfo7NefYYu\r\nlb2NG4lVfFjL2vPDXzkPO6iNz+VCKYlomFWeVxD9SUuHS2U62W35DxHxxG4k\r\ngyg86ddvnfiVIyJTIb2fQa4WzmRx4yKelUI=\r\n=VqTM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/from.js": "./lib/internal/streams/from-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "24b22f69f7b31eab1d24a3b8b136cf88f9cc7a38", "scripts": {"ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "14.21.2", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^12.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "events.once": "^2.0.2", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.6.1_1677146815291_0.9747257486807643", "host": "s3://npm-registry-packages"}}, "3.6.2": {"name": "readable-stream", "version": "3.6.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@3.6.2", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream#readme", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "nyc": {"include": ["lib/**.js"]}, "dist": {"shasum": "56a9b36ea965c00c5a93ef31eb111a0f11056967", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "fileCount": 25, "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "signatures": [{"sig": "MEUCIQC0YKDN/Q8H5rt454/tAIfuMxM9UAZ7G+IY3ElTnlCNBAIgXKc1Ne6MdqYukoNCPqn24MDVqVQWJ773NQn0n1UHtDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCvIqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8BRAAlpnB0dXevA3EDFV/gh/M1d/L/cv8s3zvbELaFyVTGB9JyZPr\r\ny+FtJrpDFdH4P7RrH2O8B+BEbr8ztHSOq+PxvRaK7YHoKIgIYQLdBQAc3/W6\r\nPQZCaxPzBKaywmT9cRnPnIecajCwowiJM2oQxBfHkQY6PIoKvbvQmyOsm6xb\r\ntfVgy89zhJ8mQt0nM+dbl4oT9L2bX856qeEisAZD7OkOL7DxmYZ4SBS97XUH\r\ncDcPS2t7yPBhmCW+Zx4jWcG9KuIU7FI88I1rZTvm21XqZYtQOYs7kPyOHtb3\r\nBvNLyssrzzfvn1F22ytCvaueLLTx9J4ap2nSwAz11l5FPOCZ61yf1cf0hJ75\r\njtfJA2lV2nBlrLIvwctXiOmrZrZDg3rJw2UDAAqd7rpEWRk7iZozY7iZFBHU\r\n00sXqA/63Qm2KToaVQOGDEVUZMLzR/5H9oy2lE1FZG2tVA9RqOBCw1d6ofKg\r\ntkbY/ZFtvncrwoXNdlrn/H5BYFIqjTNVUnEz9bmtXphSRdld17Pc6z3dPD03\r\n9EzvrdDJTwswsVSKMG2bi1jZcbR32MwIwbXCooP5MbB/EvENR9D9bTcs8r/N\r\n0PLnAzNvrY8Wwp1LUIVq70WfjkyPXe0dXqP776zHKXdxAQrPdSHOEw5W0AUs\r\n9zvFi4CphM1NjIzsb17m0RwMs6AQAVXUqNQ=\r\n=JBeM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "readable.js", "browser": {"util": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "worker_threads": false, "./lib/internal/streams/from.js": "./lib/internal/streams/from-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "engines": {"node": ">= 6"}, "gitHead": "6c32003bd8607da54f8ca1b096c4411778b060bc", "scripts": {"ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "test-browser-local": "airtap --open --local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Streams3, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^2.0.0", "nyc": "^11.0.0", "tap": "^12.0.0", "glob": "^7.1.2", "pump": "^3.0.0", "tape": "^4.9.0", "lolex": "^2.6.0", "airtap": "0.0.9", "assert": "^1.4.0", "rimraf": "^2.6.2", "tar-fs": "^1.16.2", "@babel/cli": "^7.2.0", "hyperquest": "^2.1.3", "@babel/core": "^7.2.0", "events.once": "^2.0.2", "gunzip-maybe": "^1.4.1", "util-promisify": "^2.1.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "deep-strict-equal": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_3.6.2_1678438954199_0.9597227284730689", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "readable-stream", "version": "4.4.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.4.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "55ce132d60a988c460d75c631e9ccf6a7229b468", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.4.0.tgz", "fileCount": 34, "integrity": "sha512-kDMOq0qLtxV9f/SQv522h8cxZBqNZXuXNyjyezmfAAuribMyVXziljpQ/uQhfE1XLg2/TLTW2DsnoE4VAi/krg==", "signatures": [{"sig": "MEUCIFhxSu5KHVNnQptyNm1vXtdbvL+yk5kVAio2OGUiomOaAiEA15CfNq+YTbMAX3a8CztBZP2WEf9Z4+EfsLOCT77CNUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210127}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "3cd3295a043fc4e68bf0713877df38f885d6590e", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.4.0_1683562554015_0.20400120010920642", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "readable-stream", "version": "4.4.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.4.1", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "fa0f0878c3bc0c12b6a82e4e58c5dc160e1faaa2", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.4.1.tgz", "fileCount": 34, "integrity": "sha512-llAHX9QC25bz5RPIoTeJxPaA/hgryaldValRhVZ2fK9bzbmFiscpz8fw6iBTvJfAk1w4FC1KXQme/nO7fbKyKg==", "signatures": [{"sig": "MEUCIDisLZcRF0+6xWWZ94x5T42NOd5bprRADEOuMUqEFzUBAiEAvrwmNA82/A+OLnhAQQFPMMpSKhHh0uWU/8Lts/l0SB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210221}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "ec65f43d95b8dd505f9059e0bd35aac309fdf19e", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.4.1_1688028422116_0.4213632578751825", "host": "s3://npm-registry-packages"}}, "4.4.2": {"name": "readable-stream", "version": "4.4.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.4.2", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "e6aced27ad3b9d726d8308515b9a1b98dc1b9d13", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.4.2.tgz", "fileCount": 34, "integrity": "sha512-Lk/fICSyIhodxy1IDK2HazkeGjSmezAWX2egdtJnYhtzKEsBPJowlI6F6LPb5tqIQILrMbx22S5o3GuJavPusA==", "signatures": [{"sig": "MEYCIQDVEFNPA5L81V1whgwkZd6wWaPhFUzcx8ZlS4XJoHW+tQIhAKKByNuhUyh7ZbKd4QknF1oQSIG96GvkiyhKf6p2N9qC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210253}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "268229d67620d092ea4d64de5416f55997eadbaa", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.4.2_1688389117468_0.32823739454411793", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "readable-stream", "version": "4.5.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.5.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "fa1d129db30926e1530324f8e307ae99b7f917ee", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.5.0.tgz", "fileCount": 34, "integrity": "sha512-AeYh93VyUwnNI/HCB4XdAaP4N/yGgg3rci3ISEUSM0jN95yWpbL9tSuRIwHzCq7e6TzYwJ6Vn7viUYTsfIxBlQ==", "signatures": [{"sig": "MEQCIHFRpjP6w/zac6VBZSlRKpGQW2qGPQRDP3J2hPz9qsjVAiAVyrWol/8D4BqN4JHtVE/JSb98YmCrDj0w8BWdv7Nhrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217248}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "700d28b674a440ee939d0d88491e9040c45de642", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.5.0_1702895277448_0.6538613939519224", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "readable-stream", "version": "4.5.1", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.5.1", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "3f2e4e66eab45606ac8f31597b9edb80c13b12ab", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.5.1.tgz", "fileCount": 34, "integrity": "sha512-uQjbf34vmf/asGnOHQEw07Q4llgMACQZTWWa4MmICS0IKJoHbLwKCy71H3eR99Dw5iYejc6W+pqZZEeqRtUFAw==", "signatures": [{"sig": "MEQCIFq2j8NBrsEOsi3wMt/24T+u8pm6O3fo4/WMEsTdqvRZAiAn1cGDFUNWjS7x2aR/Dz8Lc127WxKeWFuQqCxdAsqVpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217256}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "149f1053e06e47af4570041529c674cd41b6f6a2", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.14.39", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.5.1_1702914856671_0.3464446031026216", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "readable-stream", "version": "4.5.2", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.5.2", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "9e7fc4c45099baeed934bff6eb97ba6cf2729e09", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.5.2.tgz", "fileCount": 34, "integrity": "sha512-yjavECdqeZ3GLXNgRXgeQEdz9fvDDkNKyHnbHRFtOr7/LcfgBcmct7t/ET+HaCTqfh06OzoAxrkN/IfjJBVe+g==", "signatures": [{"sig": "MEUCIQCIAq4ckm/ZDWmXxZsZ21umZG4ZgvLDhgTCdBxVDNJiGgIgDvK/u759gm4hRPzsvBtj0bmx2WZhBykkh/7c+lEngnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217255}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "a2e9aedf4aeee4a5e4d8efcb175edb67e2817eaa", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.19.9", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.5.2_1703714446756_0.12784963309989483", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "readable-stream", "version": "4.6.0", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "_id": "readable-stream@4.6.0", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/readable-stream", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "dist": {"shasum": "ce412dfb19c04efde1c5936d99c27f37a1ff94c9", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.6.0.tgz", "fileCount": 34, "integrity": "sha512-cbAdYt0VcnpN2Bekq7PU+k363ZRsPwJoEEJOEtSJQlJXzwaxt3FIo/uL+KeDSGIjJqtkwyge4KQgD2S2kd+CQw==", "signatures": [{"sig": "MEUCIQCgFurZRTKLLcN+3zqGv8c3XrraUxeZZskYfSUN8soGLgIgbj09omoxKYDiQmXHqC4VFXWIxmGiYTdxQGs0ErRipnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217255}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "gitHead": "fdfbf827bc29ebdcb26e93d3566891dc18f06d9e", "scripts": {"lint": "eslint src", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "build": "node build/build.mjs", "format": "prettier -w src lib test", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "postbuild": "prettier -w lib test", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "licenses": [{"url": "https://choosealicense.com/licenses/mit/", "type": "MIT"}], "repository": {"url": "git://github.com/nodejs/readable-stream.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0", "abort-controller": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.2", "tap": "^16.2.0", "tar": "^6.1.11", "tape": "^5.5.3", "eslint": "^8.15.0", "rollup": "^2.72.1", "undici": "^5.1.1", "esbuild": "^0.19.9", "webpack": "^5.72.1", "prettier": "^2.6.2", "browserify": "^17.0.0", "playwright": "^1.21.1", "@babel/core": "^7.17.10", "webpack-cli": "^4.9.2", "eslint-plugin-n": "^15.2.0", "tap-mocha-reporter": "^5.0.3", "@sinonjs/fake-timers": "^9.1.2", "esbuild-plugin-alias": "^0.2.1", "eslint-plugin-import": "^2.26.0", "@rollup/plugin-inject": "^4.0.4", "eslint-plugin-promise": "^6.0.0", "eslint-config-standard": "^17.0.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "rollup-plugin-polyfill-node": "^0.9.0", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/readable-stream_4.6.0_1734596369314_0.9393929715200833", "host": "s3://npm-registry-packages-npm-production"}}, "4.7.0": {"name": "readable-stream", "version": "4.7.0", "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "homepage": "https://github.com/nodejs/readable-stream", "license": "MIT", "licenses": [{"type": "MIT", "url": "https://choosealicense.com/licenses/mit/"}], "keywords": ["readable", "stream", "pipe"], "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "main": "lib/ours/index.js", "browser": {"util": "./lib/ours/util.js", "./lib/ours/index.js": "./lib/ours/browser.js"}, "scripts": {"build": "node build/build.mjs 18.19.0", "postbuild": "prettier -w lib test", "test": "tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "test:prepare": "node test/browser/runner-prepare.mjs", "test:browsers": "node test/browser/runner-browser.mjs", "test:bundlers": "node test/browser/runner-node.mjs", "test:readable-stream-only": "node readable-stream-test/runner-prepare.mjs", "coverage": "c8 -c ./c8.json tap --rcfile=./tap.yml test/parallel/test-*.js test/ours/test-*.js", "format": "prettier -w src lib test", "test:format": "prettier -c src lib test", "lint": "eslint src"}, "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.17.10", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@eslint/eslintrc": "^3.2.0", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-node-resolve": "^13.3.0", "@sinonjs/fake-timers": "^9.1.2", "browserify": "^17.0.0", "c8": "^7.11.2", "esbuild": "^0.19.9", "esbuild-plugin-alias": "^0.2.1", "eslint": "^8.15.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.0", "eslint-plugin-promise": "^6.0.0", "playwright": "^1.21.1", "prettier": "^2.6.2", "rollup": "^2.72.1", "rollup-plugin-polyfill-node": "^0.9.0", "tap": "^16.2.0", "tap-mocha-reporter": "^5.0.3", "tape": "^5.5.3", "tar": "^6.1.11", "undici": "^5.1.1", "webpack": "^5.72.1", "webpack-cli": "^4.9.2"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "_id": "readable-stream@4.7.0", "gitHead": "88df21041dc26c210fab3e074ab6bb681a604b8e", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-oIGGmcpTLwPga8Bn6/Z75SVaH1z5dUut2ibSyAMVhmUggWpmDn2dapB0n7f8nwaSiRtepAsfJyfXIO5DCVAODg==", "shasum": "cedbd8a1146c13dfff8dab14068028d58c15ac91", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.7.0.tgz", "fileCount": 35, "unpackedSize": 217898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH4/n67lXbKHNuwuy2AsdsKb0Mo6t8509l3Dlajg25ymAiEA5HyTAyJOBcTiNa8Jl3+jv6va6pahYqOcaxOU828PHLk="}]}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/readable-stream_4.7.0_1736241338713_0.435837370565447"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-07-27T04:46:57.281Z", "modified": "2025-01-07T09:15:39.036Z", "0.0.1": "2012-07-27T04:46:58.911Z", "0.0.2": "2012-09-10T18:32:22.628Z", "0.0.3": "2012-10-15T18:50:21.287Z", "0.0.4": "2012-12-03T05:37:50.997Z", "0.1.0": "2012-12-14T00:10:04.841Z", "0.1.1": "2013-01-11T23:30:36.295Z", "0.2.0": "2013-01-11T23:31:27.220Z", "0.3.0": "2013-02-14T22:59:03.141Z", "0.3.1": "2013-02-20T19:18:23.014Z", "1.0.0": "2013-03-09T19:16:19.726Z", "1.0.1": "2013-03-11T02:20:24.538Z", "1.0.2": "2013-03-11T16:31:26.752Z", "1.0.15": "2013-08-02T22:03:15.036Z", "1.0.17": "2013-08-26T22:38:58.256Z", "1.1.7": "2013-08-26T23:14:28.483Z", "1.1.8": "2013-08-30T20:01:01.457Z", "1.1.9": "2013-09-13T15:00:47.024Z", "1.0.24": "2014-01-09T02:20:53.648Z", "1.1.10": "2014-01-09T02:23:03.021Z", "1.0.25": "2014-01-23T23:17:57.824Z", "1.0.25-1": "2014-01-27T22:03:56.772Z", "1.1.11": "2014-02-20T03:54:13.407Z", "1.0.26": "2014-02-20T03:54:55.627Z", "1.0.26-1": "2014-03-03T03:12:37.608Z", "1.1.11-1": "2014-03-03T03:19:04.045Z", "1.0.26-2": "2014-03-03T03:19:46.573Z", "1.1.12": "2014-03-30T09:01:27.971Z", "1.0.26-3": "2014-03-30T09:03:46.744Z", "1.0.26-4": "2014-03-30T13:49:34.616Z", "1.1.12-1": "2014-03-30T13:50:13.330Z", "1.0.27-1": "2014-04-17T09:03:01.624Z", "1.1.13-1": "2014-04-17T09:03:21.224Z", "1.0.31": "2014-08-20T10:26:55.144Z", "1.1.13": "2014-08-20T10:55:14.998Z", "1.0.32": "2014-09-22T01:43:26.059Z", "1.0.32-1": "2014-10-09T06:07:02.866Z", "1.0.33-1": "2014-10-09T06:07:43.858Z", "1.0.33-2": "2014-10-25T10:09:17.264Z", "1.0.33": "2014-10-25T10:11:42.356Z", "2.0.0": "2015-06-10T10:08:29.243Z", "2.0.1": "2015-06-22T09:31:22.112Z", "2.0.2": "2015-07-16T14:31:16.619Z", "2.0.3": "2015-10-22T21:48:30.092Z", "2.0.4": "2015-10-31T16:41:08.789Z", "2.0.5": "2015-12-16T18:34:28.164Z", "2.0.6": "2016-03-13T18:25:10.008Z", "1.0.34": "2016-04-13T15:48:44.049Z", "1.1.14": "2016-04-13T16:01:34.959Z", "2.1.0": "2016-04-13T17:20:05.024Z", "2.1.1": "2016-04-29T12:00:58.765Z", "2.1.2": "2016-04-29T12:43:18.822Z", "2.1.3": "2016-05-18T16:11:16.151Z", "2.1.4": "2016-05-19T17:40:07.425Z", "2.1.5": "2016-08-17T19:52:13.718Z", "2.2.0": "2016-11-10T14:30:38.334Z", "2.2.1": "2016-11-10T15:54:00.467Z", "2.2.2": "2016-11-14T13:05:09.770Z", "2.2.3": "2017-02-21T14:41:06.916Z", "2.2.4": "2017-03-14T13:30:42.407Z", "2.2.5": "2017-03-14T15:28:45.064Z", "2.2.6": "2017-03-16T08:02:27.637Z", "2.2.7": "2017-04-07T07:45:02.608Z", "2.2.8": "2017-04-07T14:10:44.977Z", "2.2.9": "2017-04-08T08:06:01.717Z", "2.2.10": "2017-06-02T07:15:29.062Z", "2.2.11": "2017-06-06T14:27:55.010Z", "2.3.0": "2017-06-19T13:26:33.669Z", "2.3.1": "2017-06-21T14:07:19.168Z", "2.3.2": "2017-06-22T13:48:21.729Z", "2.3.3": "2017-06-29T14:17:53.575Z", "2.3.4": "2018-02-09T21:31:33.563Z", "2.3.5": "2018-03-03T16:22:07.230Z", "2.3.6": "2018-04-04T15:40:14.204Z", "3.0.0-rc.1": "2018-07-09T10:08:23.296Z", "3.0.0-rc.2": "2018-07-10T15:19:31.730Z", "3.0.0-rc.3": "2018-08-06T12:32:29.762Z", "3.0.0": "2018-08-10T16:22:04.975Z", "3.0.1": "2018-08-13T10:28:05.235Z", "3.0.2": "2018-08-20T08:15:38.545Z", "3.0.3": "2018-09-10T17:27:30.880Z", "3.0.4": "2018-10-01T19:11:59.861Z", "3.0.5": "2018-10-02T14:10:34.720Z", "3.0.6": "2018-10-02T20:43:31.048Z", "3.1.0": "2018-12-16T12:26:17.545Z", "3.1.1": "2018-12-23T14:43:27.073Z", "3.2.0": "2019-02-28T18:17:18.903Z", "3.3.0": "2019-04-01T07:11:42.350Z", "3.4.0": "2019-05-28T05:57:22.671Z", "2.3.7": "2020-01-05T17:13:39.106Z", "3.5.0": "2020-01-17T16:58:02.847Z", "3.6.0": "2020-02-13T19:42:05.463Z", "4.0.0": "2022-06-14T21:46:35.420Z", "4.1.0": "2022-07-11T08:48:45.926Z", "4.2.0": "2022-09-23T10:14:08.151Z", "4.3.0": "2023-01-01T22:50:08.191Z", "2.3.8": "2023-02-23T10:04:06.163Z", "3.6.1": "2023-02-23T10:06:55.482Z", "3.6.2": "2023-03-10T09:02:34.449Z", "4.4.0": "2023-05-08T16:15:54.251Z", "4.4.1": "2023-06-29T08:47:02.331Z", "4.4.2": "2023-07-03T12:58:37.772Z", "4.5.0": "2023-12-18T10:27:57.694Z", "4.5.1": "2023-12-18T15:54:16.879Z", "4.5.2": "2023-12-27T22:00:47.039Z", "4.6.0": "2024-12-19T08:19:29.544Z", "4.7.0": "2025-01-07T09:15:38.877Z"}, "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "license": "MIT", "homepage": "https://github.com/nodejs/readable-stream", "keywords": ["readable", "stream", "pipe"], "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "description": "Node.js Streams, a user-land copy of the stream library from Node.js", "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "readme": "# readable-stream\n\n**_Node.js core streams for userland_**\n\n[![npm status](https://img.shields.io/npm/v/readable-stream.svg)](https://npm.im/readable-stream)\n[![node](https://img.shields.io/node/v/readable-stream.svg)](https://www.npmjs.org/package/readable-stream)\n[![Node.js Build](https://github.com/nodejs/readable-stream/workflows/Node.js/badge.svg)](https://github.com/nodejs/readable-stream/actions?query=workflow%3ANode.js)\n[![Browsers Build](https://github.com/nodejs/readable-stream/workflows/Browsers/badge.svg)](https://github.com/nodejs/readable-stream/actions?query=workflow%3ABrowsers)\n\n```bash\nnpm install readable-stream\n```\n\nThis package is a mirror of the streams implementations in Node.js 18.19.0.\n\nFull documentation may be found on the [Node.js website](https://nodejs.org/dist/v18.19.0/docs/api/stream.html).\n\nIf you want to guarantee a stable streams base, regardless of what version of\nNode you, or the users of your libraries are using, use **readable-stream** _only_ and avoid the _\"stream\"_ module in Node-core, for background see [this blogpost](http://r.va.gg/2014/06/why-i-dont-use-nodes-core-stream-module.html).\n\nAs of version 2.0.0 **readable-stream** uses semantic versioning.\n\n## Version 4.x.x\n\nv4.x.x of `readable-stream` is a cut from Node 18. This version supports Node 12, 14, 16 and 18, as well as evergreen browsers.\nThe breaking changes introduced by v4 are composed of the combined breaking changes in:\n* [Node v12](https://nodejs.org/en/blog/release/v12.0.0/)\n* [Node v13](https://nodejs.org/en/blog/release/v13.0.0/)\n* [Node v14](https://nodejs.org/en/blog/release/v14.0.0/)\n* [Node v15](https://nodejs.org/en/blog/release/v15.0.0/)\n* [Node v16](https://nodejs.org/en/blog/release/v16.0.0/)\n* [Node v17](https://nodejs.org/en/blog/release/v17.0.0/)\n* [Node v18](https://nodejs.org/en/blog/release/v18.0.0/)\n\nThis also includes _many_ new features.\n\n## Version 3.x.x\n\nv3.x.x of `readable-stream` is a cut from Node 10. This version supports Node 6, 8, and 10, as well as evergreen browsers, IE 11 and latest Safari. The breaking changes introduced by v3 are composed by the combined breaking changes in [Node v9](https://nodejs.org/en/blog/release/v9.0.0/) and [Node v10](https://nodejs.org/en/blog/release/v10.0.0/), as follows:\n\n1. Error codes: https://github.com/nodejs/node/pull/13310,\n   https://github.com/nodejs/node/pull/13291,\n   https://github.com/nodejs/node/pull/16589,\n   https://github.com/nodejs/node/pull/15042,\n   https://github.com/nodejs/node/pull/15665,\n   https://github.com/nodejs/readable-stream/pull/344\n2. 'readable' have precedence over flowing\n   https://github.com/nodejs/node/pull/18994\n3. make virtual methods errors consistent\n   https://github.com/nodejs/node/pull/18813\n4. updated streams error handling\n   https://github.com/nodejs/node/pull/18438\n5. writable.end should return this.\n   https://github.com/nodejs/node/pull/18780\n6. readable continues to read when push('')\n   https://github.com/nodejs/node/pull/18211\n7. add custom inspect to BufferList\n   https://github.com/nodejs/node/pull/17907\n8. always defer 'readable' with nextTick\n   https://github.com/nodejs/node/pull/17979\n\n## Version 2.x.x\n\nv2.x.x of `readable-stream` is a cut of the stream module from Node 8 (there have been no semver-major changes from Node 4 to 8). This version supports all Node.js versions from 0.8, as well as evergreen browsers and IE 10 & 11.\n\n# Usage\n\nYou can swap your `require('stream')` with `require('readable-stream')`\nwithout any changes, if you are just using one of the main classes and\nfunctions.\n\n```js\nconst {\n  Readable,\n  Writable,\n  Transform,\n  Duplex,\n  pipeline,\n  finished\n} = require('readable-stream')\n```\n\nNote that `require('stream')` will return `Stream`, while\n`require('readable-stream')` will return `Readable`. We discourage using\nwhatever is exported directly, but rather use one of the properties as\nshown in the example above.\n\n## Usage In Browsers\n\nYou will need a bundler like [`browserify`](https://github.com/browserify/browserify#readme), [`webpack`](https://webpack.js.org/), [`parcel`](https://github.com/parcel-bundler/parcel#readme) or similar. Polyfills are no longer required since version 4.2.0.\n\n# Streams Working Group\n\n`readable-stream` is maintained by the Streams Working Group, which\noversees the development and maintenance of the Streams API within\nNode.js. The responsibilities of the Streams Working Group include:\n\n- Addressing stream issues on the Node.js issue tracker.\n- Authoring and editing stream documentation within the Node.js project.\n- Reviewing changes to stream subclasses within the Node.js project.\n- Redirecting changes to streams from the Node.js project to this\n  project.\n- Assisting in the implementation of stream providers within Node.js.\n- Recommending versions of `readable-stream` to be included in Node.js.\n- Messaging about the future of streams to give the community advance\n  notice of changes.\n\n<a name=\"members\"></a>\n\n## Team Members\n\n- **Mathias Buus** ([@mafintosh](https://github.com/mafintosh)) &lt;<EMAIL>&gt;\n- **Matteo Collina** ([@mcollina](https://github.com/mcollina)) &lt;<EMAIL>&gt;\n  - Release GPG key: 3ABC01543F22DD2239285CDD818674489FBC127E\n- **Robert Nagy** ([@ronag](https://github.com/ronag)) &lt;<EMAIL>&gt;\n- **Vincent Weevers** ([@vweevers](https://github.com/vweevers)) &lt;<EMAIL>&gt;\n", "readmeFilename": "README.md", "users": {"285858315": true, "seu": true, "ymk": true, "isao": true, "j.su": true, "ryanj": true, "dudley": true, "fotooo": true, "kcando": true, "monjer": true, "redmed": true, "ziflex": true, "gyaresu": true, "hearsid": true, "itonyyo": true, "kahboom": true, "wenbing": true, "alanshaw": true, "chalassa": true, "coalesce": true, "cooclsee": true, "edloidas": true, "leix3041": true, "stanzhai": true, "xiaobing": true, "aredridel": true, "fgribreau": true, "mojaray2k": true, "morogasper": true, "nickleefly": true, "pengzhisun": true, "simplyianm": true, "xieranmaya": true, "coolhanddev": true, "fiveisprime": true, "flumpus-dev": true, "jefrancomix": true, "kodekracker": true, "michaelnisi": true, "phoenix-xsy": true, "sheikhsajid": true, "tunnckocore": true, "dpjayasekara": true, "brycereynolds": true, "markthethomas": true, "derickchou0129": true, "joris-van-der-wel": true}}