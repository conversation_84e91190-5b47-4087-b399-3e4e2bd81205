{"name": "css-select", "dist-tags": {"latest": "5.1.0"}, "versions": {"1.0.0": {"name": "css-select", "version": "1.0.0", "dependencies": {"css-what": "1.0", "domutils": "1.4", "boolbase": "~1.0.0", "nth-check": "~1.0.0"}, "devDependencies": {"htmlparser2": "*", "cheerio-soupselect": "*", "mocha": "*", "mocha-lcov-reporter": "*", "coveralls": "*", "istanbul": "*", "expect.js": "*", "jshint": "2"}, "dist": {"shasum": "b1121ca51848dd264e2244d058cee254deeb44b0", "tarball": "https://registry.npmjs.org/css-select/-/css-select-1.0.0.tgz", "integrity": "sha512-/xPlD7betkfd7ChGkLGGWx5HWyiHDOSn7aACLzdH0nwucPvB0EAm8hMBm7Xn7vGfAeRRN7KZ8wumGm8NoNcMRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7H5tm+KsPwGnsCRHu/8BrL59syqwrqvPIw8HjGphAlQIhAOmoEp3c9nD0kQpq1SneMu6tSIa5c99xhUJi6S4RU1XJ"}]}}, "1.1.0": {"name": "css-select", "version": "1.1.0", "dependencies": {"css-what": "2.0", "domutils": "1.4", "boolbase": "~1.0.0", "nth-check": "~1.0.0"}, "devDependencies": {"htmlparser2": "*", "cheerio-soupselect": "*", "mocha": "*", "mocha-lcov-reporter": "*", "coveralls": "*", "istanbul": "*", "expect.js": "*", "jshint": "2"}, "dist": {"shasum": "b51ec9c7d0ab50b9fcd61e529504387b1202d6c4", "tarball": "https://registry.npmjs.org/css-select/-/css-select-1.1.0.tgz", "integrity": "sha512-Y6m6VdI+mb9Uxv9dOnh6orJmLNZCtdktQGszDHipNg0m8zu3aaa8oVeigFZHWDtoAUb0eT6Zc9Z7nPQJCSAEyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzXfl5bfTzHSRkLdVEf+0XVmUKZRV/FSLx5ISFFQNjZQIhALA6LbJML99KqkO+rxirfwTuxS25s3wapKfzVZvLb54K"}]}}, "1.2.0": {"name": "css-select", "version": "1.2.0", "dependencies": {"css-what": "2.1", "domutils": "1.5.1", "boolbase": "~1.0.0", "nth-check": "~1.0.1"}, "devDependencies": {"htmlparser2": "*", "cheerio-soupselect": "*", "mocha": "*", "mocha-lcov-reporter": "*", "coveralls": "*", "istanbul": "*", "expect.js": "*", "jshint": "2"}, "dist": {"shasum": "2b3a110539c5355f1cd8d314623e870b121ec858", "tarball": "https://registry.npmjs.org/css-select/-/css-select-1.2.0.tgz", "integrity": "sha512-dUQOBoqdR7QwV90WysXPLXG5LO7nhYBgiWVfxF80DKPF8zx1t/pUd2FYy73emg3zrjtM6dzmYgbHKfV2rxiHQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFfIaW9zJ9f2aX+93hzD/4THO/LnoeUvOhzPhJ4SSzpuAiBIyGipQtsaCkz6tj3RnHA+MSwfKOZcE3gngqXwQGpReg=="}]}}, "1.3.0-rc0": {"name": "css-select", "version": "1.3.0-rc0", "dependencies": {"boolbase": "^1.0.0", "css-what": "2.1", "domutils": "1.5.1", "nth-check": "^1.0.1"}, "devDependencies": {"cheerio-soupselect": "*", "coveralls": "*", "eslint": "^3.0.0", "expect.js": "*", "htmlparser2": "*", "istanbul": "*", "mocha": "*", "mocha-lcov-reporter": "*"}, "dist": {"shasum": "6f93196aaae737666ea1036a8cb14a8fcb7a9231", "tarball": "https://registry.npmjs.org/css-select/-/css-select-1.3.0-rc0.tgz", "integrity": "sha512-sPFsHUnX17suh/D+JnvAg9CP8cXRYp6GqpTvXjBLGnNfSoRwRW+yZ89ABL/+Ea6Ey+53/B/xwbt26qNDxd7HBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICSqWIKUhcoCHRWD1UNZ78r5O+WWUl7Xd4qpyJJmFWzbAiAb/AXHiXNv3aw56m1UPw+mTY4/7tyfWVGQjdlO1xJJVg=="}]}}, "2.0.0": {"name": "css-select", "version": "2.0.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "2.1", "domutils": "^1.7.0", "nth-check": "^1.0.1"}, "devDependencies": {"cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.0", "eslint": "^4.18.2", "expect.js": "^0.3.1", "htmlparser2": "^3.9.2", "istanbul": "^0.4.5", "mocha": "^5.0.4", "mocha-lcov-reporter": "^1.3.0"}, "dist": {"integrity": "sha512-MGhoq1S9EyPgZIGnts8Yz5WwUOyHmPMdlqeifsYs/xFX7AAm3hY0RJe1dqVlXtYPI66Nsk39R/sa5/ree6L2qg==", "shasum": "7aa2921392114831f68db175c0b6a555df74bbd5", "tarball": "https://registry.npmjs.org/css-select/-/css-select-2.0.0.tgz", "fileCount": 11, "unpackedSize": 46285, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHQC7ngzXB7+KzAA85Y6M7Z/2ShgBaZK+BtDV9ojIHn1AiEA75Tm3NYXkokgOdhcawDxdfaV28SsY8j4LkYyzr+iHQw="}]}}, "2.0.2": {"name": "css-select", "version": "2.0.2", "dependencies": {"boolbase": "^1.0.0", "css-what": "^2.1.2", "domutils": "^1.7.0", "nth-check": "^1.0.2"}, "devDependencies": {"cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^5.7.0", "expect.js": "^0.3.1", "htmlparser2": "^3.10.0", "istanbul": "^0.4.5", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.3.0"}, "dist": {"integrity": "sha512-dSpYaDVoWaELjvZ3mS6IKZM/y2PMPa/XYoEfYNZePL4U/XgyxZNroHEHReDx/d+VgXh9VbCTtFqLkFbmeqeaRQ==", "shasum": "ab4386cec9e1f668855564b17c3733b43b2a5ede", "tarball": "https://registry.npmjs.org/css-select/-/css-select-2.0.2.tgz", "fileCount": 11, "unpackedSize": 51557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzQKHCRA9TVsSAnZWagAAZIcP/3CbPe9cbZBCwQul3WnK\nVZKVlN61pUHy9FAaSP4oQtxa1EWwEgeCz3lxFbpWaYyx9wy/pdm+en8VgQP4\nw6MELT9jY61/n5p0PKzr9U+sbXxAJMh8eyjWaLLYvJhTZm845gUTqF7l/wRM\nW0X+bsDfLBLgfFXxLWO6EC+ZPhm0oz+KW/gtAAGBmk3phTo4tcu/WWCZns4l\nQJ1etRXDFXV217G7hVOXC2eSzeFfnQGRVHHNoc4Ci1g01o8KMEra873+Za0D\ni9S2ynYVGJlxDkLli09FbLN80VBYHHOXUzGoI6kFq0mLXLdcQCIRc3/bvwWr\ncvFtIYDobkZ70/CWA4ytD7oiu83AmQHidDpJQXXN0jcrd8iHs3IT9d33pc4S\ns1gs3GwyDoM3C6iXR3ENtnZNoEeGXM8DkCaBjgNTnXMyhBcHbhXsZY4M8+Rf\nh9wyw1PMvcq70CScp0n3N2T5XBk+QE+KStxDpf/bAagFQWYjaDMx8qwI1Fm1\n3AlNYxXaarc0SHMxZXOxL+gRhF8px0hPt2KML5ZynqYh+kLAw52tRBxLju/g\nkSlcScamOKDzp7U5RtpifMbXgTe1+Xypdj0buR+YU0vDMzBi4DvtUWLk7oTv\ndbJqSJkdMzTqSGLBWNSmiKqlfXowUXM+sFQ0Tw+D4GHAfPk0pGadZ7JYnxCg\nyv4V\r\n=HMRf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIElEBcRDDGSXo7R7on297qY0emJQIsuXQX68ex/t+EVaAiBPIqeBk7WqgBvIp1bCXlB741VgpZs885mSnzRTztZAhg=="}]}}, "2.1.0": {"name": "css-select", "version": "2.1.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.2.1", "domutils": "^1.7.0", "nth-check": "^1.0.2"}, "devDependencies": {"cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^6.0.0", "expect.js": "^0.3.1", "htmlparser2": "^4.0.0", "istanbul": "^0.4.5", "mocha": "^6.0.0", "mocha-lcov-reporter": "^1.3.0"}, "dist": {"integrity": "sha512-Dqk7LQKpwLoH3VovzZnkzegqNSuAziQyNZUcrdDM401iY+R5NkGBXGmtO05/yaXQziALuPogeG0b7UAgjnTJTQ==", "shasum": "6a34653356635934a81baca68d0255432105dbef", "tarball": "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz", "fileCount": 11, "unpackedSize": 53685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdx0h2CRA9TVsSAnZWagAA9JIP/jhzllnRmbqMaQ0rPDig\ncgJfBg+60SjBiX/KAXKnkENqFEVGNE8C5FQeQ1C5ic6TlWEpVUWXu4q49rMF\n06eyA6UellZajWVpDTcNyZ7tvyeftEC74XsqPJrdJg7dG+Ow8EUzod5p/Acu\nvTgEHXJWxGPh6AhBo8uIAyuu0YAIGntlYelMc9ERym8oTAtcq2/5QrZT1UZ5\nm513RBRUk4UV4a39DPYe4D8yEHQ6cEwy7D4QuYc7pHRkTR1VOSr6fQ3ZXsYl\nvbs5y7yQgMxUQJZbgpkqy957CmV24FMVbjIwedopJW59Jnrzm6cSU1HML1/5\ntUiJEx8jDICdH2lYCvBjvXAz+wbsJ6cAOFYB49vBBr1SRq71ujhJedFODEnJ\nzgwK7jacmiaJyCBrSI5bNBAeq+OrLa+F3f93MPHUA+2yYQ4nY6yRw0QdNuFV\nkkTkGYm+7oXOcO71WbtqclRZFMeNibg5Pn2l34HEi5AkLYTjprIEtTrkCO75\nSt1jNDz+FFuLBblCOtfdCJCK2NpQ1QZeDZEb5A1Iw4DJvRVYj6oLTNSgOIh1\numVnOsjIbCXyJRfunTrF4cIkzrX9XXaSaJT/cjdUrddJiRZwy5I8lfwiMaRT\ngUuHxaT/DHqjHlsx+41GQwKJHgVRq3bkNjlqB+ylFO4eL8mDmOn0ASSdBI5K\nxoer\r\n=BT/9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDW4Dp+WhrXLh1SsTLeCMYQYpwmYzEaivHgv2DBubJYfAiEAvTaERJ8DFDeLCiGLGWjUkEGDWFOF5/7xYM5+6nvObMw="}]}}, "3.0.0": {"name": "css-select", "version": "3.0.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.3.0", "domutils": "^2.4.0", "nth-check": "^1.0.2"}, "devDependencies": {"@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "expect.js": "^0.3.1", "htmlparser2": "^4.0.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "prettier": "^2.1.2", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-T432xuZlBtvAIaaRJyB5DVknczqr4bIEhfr09uC3L0DW0FQAK4+Yp/AAhTALNGoE7nZ/hGq2v11M8FOZ8amsUA==", "shasum": "7533e0e600f6a234c7cf151e3c94671a04fe9624", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.0.0.tgz", "fileCount": 3, "unpackedSize": 13200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa7dUCRA9TVsSAnZWagAAcrYP/i0xjGkzfX+fA5DOIJAv\nSpEocpusI77P/z2Nv8Zp8Z6+EqzjP0cYkAtrZr1F24md9QcTIvJHFa9sqaA0\nvhQs9bDai3N8v40mV8Whnl5GmKPxYzpMESdPX5HQU1o37UYobOCp5lYiK3Ds\n7L+OggIwiaHVlBj+5jwopSh+3Z4WVJ2Y8SRPwuyH0ec4IrdlckWvavwPktJY\nDprCjSXpmXUbWL8huoJCm2K06p0pzTmMvZ7chJxwF/XEsn4J+VM9ejIJ2kyX\nMCtxLVx0PqhTY0wvWvLaKpQC29jHtswBSNU2SnhYKYcIcK9moeQJZ7hA42po\n92u2iPdrBMOU5LJGzkdw2FxJHjh9AHajoySZb7gKMhzpgq2LtLn7aQUaTePj\njVcBb1NFbhwXWVA2HhyIWgMW/8BrQnYQXm6gpWlaf2zsYMvIWz/TTcQimAhu\nXQ2wt6mwArAmeapoU4jidcC1OLMRbCCT0BUglDpD5YxzeUExzVK2RiIhExyL\n8fOLQZQRWyRI7hlWNdNDvgndR/ViMGCKUNXIZ+gGdk2X/2DigWEMY0QbsmUX\nadeMXUtM1kcLcKJ2mKMdDkCeHRzn7BOqo0nY4qU3gzo9sS6Zkph1b5YUL2tI\nAtqxJui7SUS+tOhWz2iIduKJbR6TPG8iLCOym+9lYLwCDhExg9cV5eksnJYK\n8GOB\r\n=bP9A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBPHYXXddMCHTn5/UcM3hDxpoMZfX2q2VCBln51JY8xyAiEA3APXcBSJ7ZN9uwDIt6qBN4LmznWTJzuHXgifEUtVd6c="}]}}, "3.0.1": {"name": "css-select", "version": "3.0.1", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.3.0", "domutils": "^2.4.0", "nth-check": "^1.0.2"}, "devDependencies": {"@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "expect.js": "^0.3.1", "htmlparser2": "^4.0.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "prettier": "^2.1.2", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-nzgvzFgkhKeoM0P69AFMgVjlt9b3wxbi1Cai1EAK4S/897edtKcPVgbdWPjP75JanlIZPIccd0y++ROysnXaYg==", "shasum": "98bfdf4d43756f4b8db7b897bde61aa7572d59b8", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.0.1.tgz", "fileCount": 3, "unpackedSize": 13200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa9xoCRA9TVsSAnZWagAApFoP/jD5/PQrkAppJlfsTCp3\ncX9bsWeYCsn0NIO8XDfCehf4SvqeBmCoR3Mr6S8KexCdF/gI3tmHOQidfPzJ\nmKyZYTJtKHK2L7c9R2Ddb8jxE0H+EaR3//tUPn8LblIA2jfWwyWSm1sNC/xq\nuvXi9PdMZTa2gr7CX3/Fp3G8pf9C19P1mzXJ1LUAaKNpXAKsyj8BlWVXyXHI\nPC5+r/MdzVld0KKkR7VSMdyzHXnyf3C8XB8jAJ7MYn9EJW1HrCMPRAVxSrKG\n+BBys1NwFTvc4jTE+BAKncgwVhqCkPOrDoakmSOOlwjtun80LsyodDfecub0\nteo1cuWAO2fE2YJ5pApaSq9bRPtsrGNQw5tioj9FoZoG23hFARBP2vvRfUvS\nBL4IY/MD5dT/Q0y2eWaKlVFTKZj7os80ypatIvybHTtLEGTeffulDyYRzQnq\ncQh4SXadWA+lFbkPQ5qSPDktInxpzLZhas5MUXFwnZdgreerHH2JtYldOmKJ\nVglUyhZrJ/Qx368xgfg0kQ4WSfBjpoGeuXPfXZ9NXFc2q7IvHiKkAGUOSoA4\n/nfv8G+9EQpgjIRgULADiTTD0GxPdNVtmjS4x9cdd0+EzlxIye6tANWonN11\nZ1l9Fv2e2zV+jmVcws8lEti4nkq16jfhdP8wuBdqjJH3zYI4poYAl6i8UJK7\nvlEQ\r\n=WMOE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEl/BB62YmrptGPidSJa6gTigu1fapgdRC8QANOY//aYAiBGdIcm+/4rAeMlI9iKGotZAOX9RLE0Ggb4F+dwIim6dg=="}]}}, "3.0.2": {"name": "css-select", "version": "3.0.2", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.3.0", "domutils": "^2.4.0", "nth-check": "^1.0.2"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-nShlQ1A4mK+cjRJM1WCiKdPqJR3ldnqG2I3lGUy2nuQ3n7GBxCCnpHteQKM45ExGM8gwxdV/pxhXFUJVQ5XK2g==", "shasum": "ab1d484228a77e135e12423c2ecb679321457c3d", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.0.2.tgz", "fileCount": 36, "unpackedSize": 78629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcOoGCRA9TVsSAnZWagAA7okP/iblgAidO/PR5kYloKNv\nttfBgF0vNmxbRPnYm5ghgP+U51sNh+dN09L9eeGdsBmVssUIs/QaXls+9v3s\n9eImViF8SEtMca9ecS+cqY5CP9q3cNuAAdyR3zuF8jF3A122MCyY2T7RYLmW\npfW2chzTlXqCq7zvRoebStEIpL232hLNfK/7VpgnLWwj4WOMh9u2AFsBdZqB\nwdj9S1jjZ+lirxlTpWe4kjq81h/UWyiZT0+DYQwWIq7uCT5DQIhVrm0jHgq8\nF4HjdxuTXH+mv5UoGQNPDbBMqFmBHgvfcezWKzoAll9UhSqM55lYD/SRDZwl\ns+t/LLznyWdaHG1uhttnukHZZ+urhJ1RQxCedWylaTsdTMu7rCFzt5d0HfFJ\nMaPvxzNc/TgNjpKNGCzN43+nGLHE7wnvkToyQIcy7FEC9NpWgWun9G99raSY\npUPKVGc9cKQ1tIWRV4nuwfONvtEjnFXME5D909r49gOQcFOwlELqHHW6v+HD\n4L9kUq1FybLImEr9CfPNUThjIFw9oZbp8ckPUM1f9Vuu6QRwYMTYZQGM7hTi\nIuMfkQ8ASCVRPmk0V/YSQ33ll939i3/gV5hReXPqW6j3DqcjRIC1nKbGkBnj\nzXlAn3UraTfN4EdpBL9hFHWagMbm/BzG0tziaqa1J19GAU5OwpkUpbNzluFJ\nwctI\r\n=gMNy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChPgh/mPRofC/hMWvZEPmrHoRfAjABEohknSUPkjpNywIgIL6e0yUQ02AuWcypiDuvnAuxb5lo/RQbexKPdQ2nh0s="}]}}, "3.0.3": {"name": "css-select", "version": "3.0.3", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.3.0", "domutils": "^2.4.0", "nth-check": "^1.0.2"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-RYXdGOA9iODBH7dDt62I/Gj6Hgj6IRo8CzlzsKeKk5H7I1P4BAXeD3ayIH0SnQkEfX0UWnDrI4Ws6+z8s10UXA==", "shasum": "bd4a1ace5953d6c9525251bde45b88ababcb639c", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.0.3.tgz", "fileCount": 36, "unpackedSize": 78633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcUo4CRA9TVsSAnZWagAAHXkP/jZfMzGbRIZ8anCv2wM2\nXs8HRbX79QgdVq6B8fuGQtvM8t3MGoZRS1yPIOK0VEB5OSdZQXXELKKPiInV\nQgxKn8tDgABtTfVJNAAipfa/Q3CwLLVt7utMYnw/YjHeCnBWs3G9K3bh3efg\nrRFPg85aYmlLz0QmZn27ZQygc9hcO8FTnofoLGNAs/QLhPoLq1DSWaTPILx4\neShyWy0g9yYir1/h9q2b9db6H2FQ0ct/gfq0T/tAbAmfHRfTD3Q8n9a9JNwP\nWDrF91HKmcOahGqtD/h4BFZ5o3IugJSiqgyJvJTA/36CtDrEuwc7uVrWTl2h\nJ3KFgFec+r4H+yvIUvvx1tsuu5GjrWPinK/vHUUs0VXwbeWTr1W2iQRpth9P\nuC6WL4H9bTzUKhaq0PgryRHVx/gk4jVF1UfWhxHl8HSvalA9t1odz+uMWnb2\n6MYng9tB5cHCnvBsFC1ieiPxbOxKsbFr7dC3bv7X9WRx6xxZX2hXQ5UUbXyr\nCdBeOw1OLOV5NCryAR0FDd2OAzuo1nxPx3egblkzAFtdxkLmYrWC6wKpr9m0\n1AuEAgq8kSxB5lGpwQ7eibHcgEaQz70G6mhfkXXaoSp3329Npp9PKR/zSDsk\nqGmHDuMp8iLcm7dS3RzLNFf1duJkYmCvtt+qnSmgJscSVb/vCLDo+JfAUBLm\nR0F/\r\n=z7bm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrYdh7AmSLRb4Dj87Pio1m5hOWT0QpQVQgKAkSWIhFBwIgTsXcFoZS6fvzN0OGl2GwEGl+7ZNfT4CyB5aaUgiiPTM="}]}}, "3.0.4": {"name": "css-select", "version": "3.0.4", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.4.1", "domhandler": "^3.2.0", "domutils": "^2.4.1", "nth-check": "^1.0.2"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-8Teebc01Oz+oMRo1Fsvvh9QjMGgeGEc8KN4o3VqBwpzHlU3iW9+ucJsfQOfdRY1j3R8r8rtEbHF8KAKKC+NlPw==", "shasum": "642fac470515cafd1cf40e3e69808afe00838902", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.0.4.tgz", "fileCount": 36, "unpackedSize": 79006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdkHhCRA9TVsSAnZWagAAbC4P+QFYfPsZ7kh9EeAeA9s2\n2xJMIgq2rmDIq6WtqExL5foNCDPGqE1ZLRMoP6EamurNnp87+gz2HOiUaKca\nlOdNtovkgLv9uF+Jd4XJu9bNtH0DLsOR1KGszYiTfiQAuHELN+JZtBZTStaW\n7+LtIsTo7EByKZqxO4gzHyZhfrUWUTaxWHU3dJLcS3SLQgtj8JQqhq03NQOc\n2POVpWmEIztZD9jK/NCftj1NAj3JdzKBLRyNZyl6Zj2CDLknkUKNDJ40akTV\nzqi03ZL9s6XAObIC2NEYQtlJGyx3XRMiSxL1zOtiNlA0tzYK4+iEQDkn4hcg\nlxdEjoDyMm0o75FXwiFqTXXiOlOSONA8GjcDKzUSoN18hC4744S7DJphJgz+\nlZlPijwwEV1UU8ExeipFaxu2YRlLDc2mh8vdqNMTe7b2NuwjK5bIHZigBYZZ\nBORntwn16otlVJtBT7S1vJ3zGHoRzQeDCpNMm/jx4/U/vi/30O9TBvfLV49/\nC5dAkSGCnKbvFPatiCsUJ0P2iuoCuYyPe2DtnTv4T79w0Z8cDofQCE6mlobe\nqwgPsm++FAkJJ42VxdQvXCyAvmWEZQKY9+dYdWizggr4LD4Ze0C11ql358hr\nJY8PtJPK6o3tpUOl8NBSYISYOjo3a1pn7RmZN6/DBQTQFBnnDF0IG5Ooxy6X\nswDb\r\n=W8p5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5OOuCD5qS5hU2RDFWORvULGGTzzo1asz2xiONHzZPvAiEAsjurU1G/jo8PkSyhmG0zDWx2K9Zy63Bs45NBPYvVOic="}]}, "funding": "https://github.com/sponsors/fb55"}, "3.1.0": {"name": "css-select", "version": "3.1.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^3.4.1", "domhandler": "^3.2.0", "domutils": "^2.4.1", "nth-check": "^1.0.2"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-tVdCXyNpLLvy23s6E82sYq6+wOlaRyrkT9Ff9XLW7cl+xwZXS6h23qfEeDHna4U/W/IKe+X55tJ9BUnh6RwOGg==", "shasum": "7e684316f184e5307d9e04d80a0d626333a9de8a", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.1.0.tgz", "fileCount": 36, "unpackedSize": 80019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfd1xVCRA9TVsSAnZWagAAm+AP/0UiRFrqPE60UG37jkLh\nTOPMOMy7789hyyWPQFBBe7Zu8Q4uB53dpcFVe92IybBMw49m/2rV6JLq/NzA\nG46B6x/7LzQInQpnmy4Vkr/i/hrEMwxfn87OXFFPf/71YgvL74WsWW23NPXh\nQRhnk4zd2ZV0/PXnSksiRcVNvR2OyLxEVJJw7V1CrMhd263AQFPb7MilTs8v\n6x1Y4UrGWs9E85EZHlSYiv/2/I7LNenwCqGyca7QaOwxIgRV7dr2PBPPnJpN\nnW3Gy6ixhGpGQK/bBl94eazWnA/978AAr4xuTjIb7Uy7UNi43XCIrS5f5eBe\nPBe/LNIuZBLlrt2GBWYNfCj199/EXqyWUWqV7zglpXn8SMDiixK9ym2pLe2b\nG4kvKviLwIlK1ARssa79IV/G4G1KM6gD7ym2JkIlhWwXQBRpKJYKQK812SWP\ncPcN9dlpJf9E7YURFIBsMzKu1hhUGOyg4NQTRDOAvS0IygL1V/Y59LucjNeb\n7ppGEKLSQTQ1jBgqZN4JvQpq7ZxLWNi8KuEa80ed54jeiq6yU7e4f6J7Rx5r\nqNLpRoT9l5DAmgZeMokc2zO+UHYUzxNUtNd5Q/r89s+nNroA5zjC+/hWJRUy\n8RQtHSiqvAMqpSEgqVbuk6hV2bMXfuMnU/YYZRPQ5P5QWizY5WHTXrx7YeYf\n5QKY\r\n=TEjr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTZDEpibpthQeBzP7jxTzBtVVfs7ozssfFkxc+3srrLwIhAKHoGqWBigjEUfkcmXpjbSfTa8G9+RSiShpFRIVvYrQs"}]}, "funding": "https://github.com/sponsors/fb55"}, "3.1.1": {"name": "css-select", "version": "3.1.1", "dependencies": {"boolbase": "^1.0.0", "css-what": "^4.0.0", "domhandler": "^4.0.0", "domutils": "^2.4.3", "nth-check": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^7.0.0", "htmlparser2": "^6.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-6h3ECMuVhTcISNgmavw2YqWiuPY0jurQYWdob5au8z0H84xkFE23Sv6ML+Y42fMNqXsaUB69+3MTpX4uFEMY+g==", "shasum": "38aef130c85a803e5a2c2cf25a59edecb4e6130d", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.1.1.tgz", "fileCount": 36, "unpackedSize": 80155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0AvbCRA9TVsSAnZWagAADQgP/RzpgwJsG7s2AUsVJ8Gl\nME5f+vjt+AV6sWSbff5J1/YX05ShoUCyOSpIs4xCi5yP0Vgr4RGh2SmTYGRq\n9cBbQG07JLxj1I7CJLGSTxfxujG7Rs/8DFSrAW3MeOD1l/oCyxzOoTzpN9rh\nobFZltQRdHEkVm/wQ5g8wqbBSWotQIcxUcmnWxiRcrECt5XAHrvgyTHnmjZM\n6mH30w2DqkaCHOukESzon2b7lc2MupRdw1RyThvub8dakox7Rr4wMV+5Rq7N\nPUEUmBp7+6edC8UPIei6RT0QnmMO/HHh8SI5YCHUOuv5R48voGdyGSeqCrCP\nQhjQVVAMTIlgOdhMw2gJKoUTFNW0gzAVgYbpyXRoFYytBg/uWNcKohy7vUOU\n8Vh52l9nrAeylAOcXmrbiGL4IqjC9zLASuQAq1VRON9xY7kQG8ifePWUUS3/\n11gGTctkJLc4ogWAb03U2r/5rgdPAfDa8gG1BVlhECh5UEOJtmURjqjH2FHB\nrcqqEgPO4CH2BA7/bTL3U+Fgaku4x6SUtrn5z9rd/SERuUB1ZgEtClzQcXJl\nfWzgkRSJKB0FN5RjsF84dJh7EJixOwSvjoxhAS2kBCMgVX74H5KqvIzyqHXa\nL3vCHuIL/Amvx1MRAYJkWvrPtTlvhPIb3OVYQ6o9AgRtoF8ijTbwLNU56ytI\nKuJB\r\n=7Gtn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9du0tXLfbDKcSMsVH3Dd3U2oeaVPYm8OMk3ThkrYEfgIhAMr14KqX7Uj+AoYmdj1xqstul521oBu3CckT1Oyk947g"}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "3.1.2": {"name": "css-select", "version": "3.1.2", "dependencies": {"boolbase": "^1.0.0", "css-what": "^4.0.0", "domhandler": "^4.0.0", "domutils": "^2.4.3", "nth-check": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^7.0.0", "htmlparser2": "^6.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-qmss1EihSuBNWNNhHjxzxSfJoFBM/lERB/Q4EnsJQQC62R2evJDW481091oAdOr9uh46/0n4nrg0It5cAnj1RA==", "shasum": "d52cbdc6fee379fba97fb0d3925abbd18af2d9d8", "tarball": "https://registry.npmjs.org/css-select/-/css-select-3.1.2.tgz", "fileCount": 36, "unpackedSize": 80486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0osRCRA9TVsSAnZWagAACo4P/AiU7o/9d90Nd1cWHx3T\n4bB6AUrWBKYpymMKw6fU0p5CeuFiXuSadyeHo8LhZjadBszEANg5K8Kkx92G\nTvuw/P7mu4fklwguLNOT2DKfrPfoO5uc5b4O8EXIBojGwtErw5YGRBhKAVMw\n+V1VCziQ8mpMKNjetAc2iwYzEkSXBDajMlkPeAoO9gZROItEJ05Nra4k6FzW\nsjpFJ2imDzcCBkkTqVkm1p17/K3dG420rNW/jcc+q3yLWF2DBQGD8mtnYWJy\nU5RyB9fvTwwuw3IQePuI7g0FLoC9wvxUPK/hZmc5DLuobm6e7osujfqS/uaI\n94uoSrfBOYdIdsIlPDRg/SfV0BeGGdHhnbMHglRU04Z9W0JG99PJ+80Hc4J7\n2/kRgrd61ZSjBM7bb0eyJkw3vg7chT+TBdJZ0XIpMM+TiIO/pTVf3Y4+60XX\nAk/r4QSb9tvZlUAmpMfZGTwsjIkJ6I1bWKQ27Jw2GWtFcRmqLioOogJw2+G7\ndP02c23aOv6Ru8K3zGUrFvLkhnqwk6N5A6K12lnlNHCvGrZQDcYEj9JT6I6s\nrBeq8Px1/5N7AG0oNttlMqwd0RthMgWCf5x2bT1z+eI07eAlGW17LnphE2RY\nuRrSUoxDudOosQ7OTBpLUNqaSEW2qA+dwCGokahhWImhn36xP7NQ65t02/Y3\nXouG\r\n=79yU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAdjZHfqXHXnz+GbNxhH2UVAE4V7hiP533Me81z1UvFZAiAzPUJ0yXgyXcbfOGEkjzoiZGWh0VinOH1nzgjilz4sjw=="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.0.0": {"name": "css-select", "version": "4.0.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.1.0", "domutils": "^2.5.1", "nth-check": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.0.5", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.0", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-I7favumBlDP/nuHBKLfL5RqvlvRdn/W29evvWJ+TaoGPm7QD+xSIN5eY2dyGjtkUmemh02TZrqJb4B8DWo6PoQ==", "shasum": "9b7b53bd82e4b348a6e0924ce37645e5db43af8e", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.0.0.tgz", "fileCount": 39, "unpackedSize": 77467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgaCCcCRA9TVsSAnZWagAABwIP/0fRPCtrw1zpR/h9qHon\nEaR4qcYh0E6DTusZlvqde4kHzQjC3f9KiX6d9VqI5mZgdLeKtrAhDIWxQZoi\nVutnvbtjzfULg5CQfZSwtPZDBPrO94y2BeWDhqhKI0Q/lUp3O/cjkv2ClwzY\nJ9cXGoYZcSuijkcfKe/wB9sbdvosvZV8cgk7fPXLS9JXCNfDiCKoz+zZi2IU\n/Pl/8FfcNQbK6qyhUgI9CAUm+47QH6AzXO4qHXs4HQnSeKeiwybHfJTYKIYC\neQF2LKANNVv/L1Z5NI9bcTzMsk7sk6vBBwAuole6yugPCHCXvJGWkTcwb/l9\n+A6JQewk0sHsVt0ome1sOC1Ri5hYOb3a/9bBQByCi0EUhWqVlZHreeXGHijH\nMX1ma7AS5cmevjKWhjzN4THtW3IslQV4bAZVh13VRCeFTKWWlGy8oHRO7MFe\nQSf+rJF4ZKRrdjUP5tmJqLIx9c+D0mrencRrrXlN6SFvsj3e+rYuOjI6SbVa\n9knJa/wmKWRiDFjQklB5szw4iPnBV4fEBvX6/Xib5mKwKAikD1ss1AXvOM+N\nQBx/J3jjfUHjhnAHNklmFMWUaAmcyJ5m1sp2P7wfkqh98s8Aur5C4T4d4xZ1\nJPWrcV4neWL33XLRKqW8FRWrIAYHREX1ruUBtWcHEXxLX0YQpPTAXvK4VgzH\nSL5d\r\n=+UAK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCC3o8efFty08+D+h3ON5NFw+M9nvaSHCk2kmRKTMY7eAIhAO351yUSNg84WRRHivp+HKpJMll8JvRu63uhMujEA4B2"}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.1.0": {"name": "css-select", "version": "4.1.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.14.41", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.5.5", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-hcu+hvJvtYBjfI15n7/9T2FFGiF7V/92jLhrfTLLq3aBTCZ6S/QTwlUNALUAhQLRANdwWaE0eQ7x3a9IMs7iAg==", "shasum": "895d5167fdaba20a2cebfe8c4de35de90a833c40", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.1.0.tgz", "fileCount": 39, "unpackedSize": 77643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeVdbCRA9TVsSAnZWagAAacUQAKCbQIXm1eXbbgJfqAyf\nblIHWuYvfMOu1Sa7zdwrjw0ffl+qsAeUhYwz+6lrfa27n5imhRkZfWaB9dSY\nH5F/ZEr8Dm07Tloz7lmBM5Nive1jJmEmcfA1yY6bV3TV7zUSDlH7G+pUt9fC\nUxASaHJeLr4QPBaVnT3cfsK2vydQ/QUMxVipWRlNHeFZkZ1hlQk1eRYdMSSP\nHIMEl4Qlyg8N6xB+lrsKdfDbg4RksZQKn7T+mNvX/t9Ufc1kv+C7GbFXBuOs\nKCdklG3COhzXE6ny4vveuVfat3KJBI7ivbz0j/PGv2BreE+UgqPkj256Y74b\nNRscfYZ1rL2/raqXOxpB3QOYZmmgHISH86paPMjOvubFm3Fr86GmQtn55iIZ\nbhVtq0wjmC0H3yA2Sn2GCmzEurQgMfGuC8cPFs37C2PYjv4rXVjmjnsp1dwQ\n+vtSlN/T0GBfeDF1q4pBnZEHPQzIqyCWIlveFj3bXTNYmrGs1L0z0aav3zQt\nvKhJKOtbHlbr9GiWWdS0RjdphEdV6AXKiUpd1+yFR3bumCvMdg6utGYymrg+\nnxmDrmTTqKTaUherUoSb7v+4D1douWh7DvJxRGn27JDEaZAhmtNgrQMHuu+G\nO4A9mSN3kT+U209NfTsVMy/E1lIPF/wUNvDIy3rnF44bqVMlrX62Hqj9/93y\nwowu\r\n=Nbi1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAK+GndDSCyX52g24V9yUwWFJVZ66TUf9HCk2qCzhEXvAiEAs0Fl3J83o7GdnofH0/UqYCVJ+jA+fk7fLAAY5HJ5lN8="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.1.1": {"name": "css-select", "version": "4.1.1", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.14.41", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.5.5", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-VUozdFh6znMkBlbJSsXYaFhUeVTuP3/NoXmLOQPah11rCWPbCI2RKJ8TUena+y3W6pWuXBDe7tBJeVJlYwWezw==", "shasum": "71e7f9136d39540d4414d1f69c7fa752eab56e69", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.1.1.tgz", "fileCount": 39, "unpackedSize": 77643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeVowCRA9TVsSAnZWagAA/yEP/R79lnxFi4/lDv3+rfcj\nZ++SnFoQKD8k9ooauK1gF8AVv4K+UAIdzNMCjQhXP5oPP1a5V2vMruH8Rmnt\n6322kMwCo/AiQyiS+KdokAbFUpZlutOSI3XkpTq6VV/Xye+JFB9GiCD2oGik\n4YlHn1NxeVn1ixEOkxzY0iKq+wgpRxYoGaMNL3BCdIhAk/vJxZJnTYPVauMo\nF3QZnQaGq7LXyh5rMLHn9BmdQ4AKFjaEG0QCw5kSch7l5B7Mj3JVXEyVrhj5\np22sh2af1+q0XF/9+MPx9OFlPR8V2xLH1MwbpXuNjZrl7nmiVrIFp9mfW+U0\nC11xCS0vYeDKs4sbfCM7ueTExGyrUxar/n6bxAhLC4kQWRUBjWeDklku+xq9\nQFvBjZTaFvXGV1MBjFgv++X/x05Bp0B/Y9knCzxV6RBtQ01j7XKyQWYYx8W8\nmGyVhTePiiy8qfDOGRNhnddqwp3tPmFU/YG9TAA8H5jD59CH9fxY9M3vlcRs\n6NZCT+lVgizaZyed2yemzbnpthnOT4p/z7IsGyKXT9GNEYqV86KEk2PY+pRk\n+2kgrFSpD0+I3su7MXNwRUHd4fxMLlfVuX18apAmN/JTtd4OtO3tRYD1t0WL\nbBzoZj5j4zsZUTCFvqbTAxMqrqBR6dT3a9LF5DTQHQ613YWxGLNvV+MHKfUd\ncofm\r\n=G5qm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE5cCTepQNeyncJBQepCNZmuJ3xWeLj+W2Vz6PwjNiKYAiEAyC6TFpgsuRkUTy9gWgJ7wOk1USo6tvmoKkaoZxY/dt8="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.1.2": {"name": "css-select", "version": "4.1.2", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.14", "@types/node": "^14.14.41", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.4.2", "prettier": "^2.1.2", "ts-jest": "^26.5.5", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-nu5ye2Hg/4ISq4XqdLY2bEatAcLIdt3OYGFc9Tm9n7VSlFBcfRv0gBNksHRgSdUDQGtN3XrZ94ztW+NfzkFSUw==", "shasum": "8b52b6714ed3a80d8221ec971c543f3b12653286", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.1.2.tgz", "fileCount": 39, "unpackedSize": 77674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeVwNCRA9TVsSAnZWagAAnvgP/j1ueZKCY+X6TdSjtqvO\nPMMgHUm1WxxBiNUxwJ+swKuEw2usamgLHKGMHFrJV/P6ZqIBv1Lh7bDr92Km\nyCJ6HuxaVs641qxTTxv9s+E6zkMqF0yhBFoeejeiLUQjAriP1h4pmcMD2wtr\n35RrscWbfxnhp4t9pbpQzsOUw+rj2PkV4YkPL8K1DnzOQPkzCyNiingP6KeZ\ntt846pHIMefW74rVYp3fs84k26YI6CqydUG+paKZmvfCZzs6PydDlI1+8jln\nSeiQfhxj9CK11QdU72dzBdaf0H9XTEjcQI69VibC3i8quAZ3Bar0Z5CUqLk7\nOt0r9d+c8grvQ1mocI4vki+3dvk9UeqlV8N4mshKHnPGkD4Q/3hL1zhNskZ/\nrH4yvPRq2Ns9v9/egMSeDO4tMZl/XMm03Rc5Y3+Bwofo44dw6+3l7drWEHzZ\nRylh0MsB0XKkxGskdbqlsMGa31vpDSP2NPVzX3fCMsKKsyZXBamsHxFZqfFa\nyOmFAVNZ1j7/Qsx/q8tZfMtFXNpd399xZCdhqPEahCoxbcfCsZrfk9s8JgHY\nhnVjUm+MbTipMjRYCcJSZ6KQHWZ9HM2i0Fk6DVrzUQnGeT0SvCq6HwmMsTLY\nniGQDtaxF+EFn0w7vWzV9Gfe44nWThZGTosv8N/LKn8nG54Zpqv1NJydv55J\nFF4j\r\n=Yq0s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAtbVT5gaq8kBqusnYdmnR2VPkfpbIfWzDCI2o+6569+AiEAxBo4LejePHHFxQ6gbWOl2C0DQV7UQEe2IRSNv2YvTGU="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.1.3": {"name": "css-select", "version": "4.1.3", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.0.0", "domhandler": "^4.2.0", "domutils": "^2.6.0", "nth-check": "^2.0.0"}, "devDependencies": {"@types/boolbase": "^1.0.0", "@types/jest": "^26.0.14", "@types/node": "^15.0.2", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio-soupselect": "^0.1.1", "coveralls": "^3.0.2", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^27.0.3", "prettier": "^2.1.2", "ts-jest": "^27.0.1", "typescript": "^4.0.2"}, "dist": {"integrity": "sha512-gT3wBNd9Nj49rAbmtFHj1cljIAOLYSX1nZ8CB7TBO3INYckygm5B7LISU/szY//YmdiSLbJvDLOx9VnMVpMBxA==", "shasum": "a70440f70317f2669118ad74ff105e65849c7067", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.1.3.tgz", "fileCount": 39, "unpackedSize": 77706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvO0pCRA9TVsSAnZWagAAgQgP/REfKXeE0Ij8iUhE6mpU\nSUWccjqT3mU8vJghsg2pegL9P+pgIiQg4QkU+DMsOhambj7eM35Lc8jKXu9v\nafGMS6BdEaJeN3KKDAFBxYXd1OwkmCbPVSn/nHxttgZXHjDZHf39fY9ayKO+\nnoB8rS4O9WHZpbomvXCUtxDZsKluJGiRwedCyY8XdWmnD0Fq2sAfvxMuJQXE\nQwSV1XizsI4lvPj9xXuc8nbEpnYLJpannXYGt3LZzP3ckbhssOgtaMJt/DSY\nTB1Xh9rFt5uYCPGDy2eulOiXR1GlZ7KkVuAkr/VFijU6W+yFqGtWBR96aqe3\nzyGlk7P3HEWeGzNFtBGtTNYtogzElh+uhBVvNjMASRbFYoSe9NfuOXCze6yI\nbzncylIUT+UO1rvvjQwL+b/z3VpRo0It0gsNsYj/EyVjBI06XfeIi4GjQv0i\n8vxzU2fBZkSuQ/DfCbJU/zfTywBfulNqfeENEyR1bPYxC0/1Zn+vFmdZWsLe\ntG2npfkmFI5r6rTvuMG3uc/ORaiBONaKOqqlVhkxJx+AbuTvGUvt6ld9kWQd\nPHlW9TwljG0dNoymYh8Pmb46J/WUoD0xGdt22oYSoA9oH7xeZo4hPWn9k4Ld\nkSd+gG+z8gzwekP+Gz2keJzj9VwhrhjVzjdf2hF3tVxa3VbtLHcLTl1wk5od\nJUMY\r\n=0T9m\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDD3hLAbrNO5+6UGvmzafJhsoXVzDrBRtaIqsTR5Azw+QIgOqWBwijBpECFhTQlJMmkRhLIyyKmpXLt1wl93C2K11s="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.2.0": {"name": "css-select", "version": "4.2.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.1.0", "domhandler": "^4.3.0", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.0", "@types/jest": "^27.0.3", "@types/node": "^16.11.12", "@typescript-eslint/eslint-plugin": "^5.7.0", "@typescript-eslint/parser": "^5.7.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.4.1", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^7.2.0", "jest": "^27.4.5", "prettier": "^2.5.1", "ts-jest": "^27.1.1", "typescript": "^4.5.4"}, "dist": {"integrity": "sha512-6YVG6hsH9yIb/si3Th/is8Pex7qnVHO6t7q7U6TIUnkQASGbS8tnUDBftnPynLNnuUl/r2+PTd0ekiiq7R0zJw==", "shasum": "ab28276d3afb00cc05e818bd33eb030f14f57895", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.2.0.tgz", "fileCount": 39, "unpackedSize": 77986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuL/lCRA9TVsSAnZWagAArSgP+wfTxLWt0OmnODSJFM2Q\nGLZOSc6XmiMMEgrOO+tpXjqrEFi6YhOcwg5CNZveKxI5OQxAjReAPVaWdrCo\n0ckWzautNQ2kn52yOv/9gvv48wtU8CymTxC9I9NcDUqoRWSN6zS0AqxNmLOR\nR5yZjBfGjZO5lpuxh2crYRms2E0s+QbZbJF4c5DtR3KSqrgwt5/acNfX8neq\nftQy9AdeFHDDbWUNw+oHtznxdc+/DiKcXeKzRNHaxHyIAr+qZr9DPVsRGP3T\nirudVsYsey2ud6ZOIC6cniA9tC3ByB9nuGf6MGWTiEi+SvZwU8TzA4xda55B\nB3QYupC1BPD5bDlL55vPnOPc3wEdvDtOhZ0osABibT4dSpNR8zJ4aEGrhGaY\nYP/mfaF3BJu5phwfiUwt4srhzqEnCYZfp4rQqGsGY/7p/ByArtRWu4sLOlvI\nJY5FH+Hx8kScFBeG2EJKlEK2n0mqhtt5yWT1ntC1OdhPppKjU4NwKU4oO8mQ\nb+HVHsXlaRUzst1q4QGrX/Ef/z3htGnlqKKrqYOQg2MiQJQVPXrYXmHWRH27\nN9ooSmpGQm11Wth/2+nIXMfeoHavycpB0XU+iToDQ3usPEe8wIuKzVCp0bvz\nvW6lbAIyI8dZEyJeFvbRDgQlo5VMpkfwWwkZH1p6thVPPcxtxMvCHKe4kCtE\nadTb\r\n=J5PK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkATNGjf9TgSojxsJIotsJq3Bn7tBJGvztULIhx/gfRwIhAJ6yXrI0EW4MKZzJoFvEtRVw7ml/4cZB1JExpo43ok11"}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.2.1": {"name": "css-select", "version": "4.2.1", "dependencies": {"boolbase": "^1.0.0", "css-what": "^5.1.0", "domhandler": "^4.3.0", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.0.3", "@types/node": "^17.0.4", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.5.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^7.2.0", "jest": "^27.4.5", "prettier": "^2.5.1", "ts-jest": "^27.1.2", "typescript": "^4.5.4"}, "dist": {"integrity": "sha512-/aUslKhzkTNCQUB2qTX84lVmfia9NyjP3WpDGtj/WxhwBzWBYUV3DgUpurHTme8UTPcPlAD1DJ+b0nN/t50zDQ==", "shasum": "9e665d6ae4c7f9d65dbe69d0316e3221fb274cdd", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.2.1.tgz", "fileCount": 39, "unpackedSize": 78491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhyFRECRA9TVsSAnZWagAARCwP/3HYP+HWfWNVmSpGMzSd\nIPaAU4MhkEE0DXvxU7G52MKmknLICxNDvnoTrL+BSdI2uxxjKglWq1sAngRP\n6mxwNYxPawpRVI+ucyl2EWUnL4KCZZgPSdR2equVGTCh6OxTgF1Cpgid2q4r\nXgJfmw4ESaSTsyvlhNsUaoe9oid6bn9+5j8JzQa7A1HvGqkTHbQLNai4U7e9\nT/Edqw0My5KiZ/RMSuOHBrRnameBthpWX1Yag1agmqDV//bcnc8mynQ3l9lo\n1An3DVJmbS+dTTyKw8Ofj6Fvh6paffmcsseJr/1kigkOmwC62f2bpE79PpOB\nwDyae+xDRtJSbQ4UKZKvmdzobTjgBO0ZlJE3OgBH3h5SjIemf/mkycaap5fB\nfW6NkaQnzEBBldmzsEDp4ShT+/LHKNUvmfDV6y7zIwR99c0Tvz4zgWxjZ4AS\noGEZZAPQGRRPNmRndprZUS0KD0hStyyYxy19yGZHLwTupEoytVDngfXW5MIr\nkyiy7vfLyItmy6JMmSeMjlOYOpkLuYYdW+2wivxnrrEKuU5TSEI9ScReHgWY\nRSD1oPSpkUzvD+9nNbS6n/zaK/CamWeFHcyYdgBJlWyqilkuGsyf94aeNFUx\nZHdbXor5QDjs1DX4wS9Kb9n+uvK8XM/GxkBpngAvZDYeGBEU2BbxpYXFpClD\neuRr\r\n=GXEI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTY95a8l0/qskuSEpDXAQnKpZnEtT0/z4VgDrW+n0WuQIgTEguM7Y/jF0sr9HKZuwvsFiEJ+HJzAqFOklVgE0eZrk="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "4.3.0": {"name": "css-select", "version": "4.3.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.16.0", "@typescript-eslint/parser": "^5.16.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.1", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "dist": {"integrity": "sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==", "shasum": "db7129b2846662fd8628cfc496abb2b59e41529b", "tarball": "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz", "fileCount": 39, "unpackedSize": 82444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQb62ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW+w/+IbhfaB8fuAW7LgaV/0Jdrfoi9DeWisJxJFrW9iYkvLvg3XjF\r\ntBOK5ZI/beq9XjXsdDPJFKjKkMTQwvZox/i74YUGrdHc0IDfeBfApjZ1LbKH\r\ndYez0ZdDDswXQWJ2xrHZXyav8mdLeSBUCWtNenbQ9HyRd7boaI2JjliI540w\r\nEg6GhIrjIi6vojlMu1SicoBHYFsESyE1K8elpFpne/Y5DCIIco38FadiKfT2\r\nvWn3eCBi125nJlrzS5eXz/jR4juvyE/KepLYG3EoJewBOdrMWnkduj8En/mP\r\nffUeppiziDP9KQ60xCznH8k+v0KbAOA3xOoRxARL0g+gi8+3rvvgwDQIYGY8\r\n/4zSUEuBD+9L3CiMZX/hl1AZvjTIs5NDA8NxY9hAhaoNLnDeF/wEDmAXT0gE\r\nI8rDUZ2CCuVR8q2aqDSIYCtDFQfu6GKFuqPGO00GiTAWiCOJcInQje6Bs0gt\r\nqNZZTO+BTQiX+8kq0XjB061cv+g2LIqjGxNgZsMrnzi3oeT//MBFcP8yUwp2\r\ntA0Z7JNm9sEYG/zhE/PhhTMiwfWOELrdHMo0Uz6Aibh3JvTdRWoNRJ8FJEX3\r\nJ/0zZsNzQa3MJXthEUHDuN+HIvMSrJQoSPFJ5CkpuZAHt3Rb6tt/XBMPStK+\r\nor7ZP+n0YEUirejoa5yrat43oWa+qkFBRgk=\r\n=uYki\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHxQTHH5yr4xYv+NhcntvVjSVFjRIrdRJWSF9+kTgogLAiEA1rlLKxWpgqQP+xfJ24gI8/JyXEIpOXKuYPoiOIcX1wQ="}]}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "5.0.0": {"name": "css-select", "version": "5.0.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.13.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "dist": {"integrity": "sha512-3/vdFG8XSUWknhxAZ6WkQbyIw+kFY1kS0nVU9TBhtkEb+ba7HWzxzyfZhConzUie+CV4lZH51/4/hLPVs7HX/A==", "shasum": "3528a9bf67f0830cdc95eee67f759c239240f588", "tarball": "https://registry.npmjs.org/css-select/-/css-select-5.0.0.tgz", "fileCount": 92, "unpackedSize": 220591, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnq9ovxW1yexe76RCmt89bhx75ghhCFENnYVu/bEgz7AIhAOmlqVVG9P2m0zXPge4/tB1ZfbglxxQS5rIfS5y7MI98"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZZZ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdbQ//bHS/InDj5lI4oGt/D7Afp0Li5lyQLNNNV6+vDv2QzLqznUJF\r\nu59N4CRQLoCRF3vg+lrpITRtkWl/2oPDus7QkYkPbSB+Ippje82pY1fS7dBL\r\n+vTq4fOtME1be2NzlqYCtoaGa7+77pVT3G2Zc2zkMvCBLbclufKwFGGh945C\r\nKASOx3o1B1PqeTLjosxw/AV8osiWoQH96aMzGAL1Cq3Sn0Hfxnt2T2PqWaAK\r\nceGSLQWpVN6XXOaNGXYAEqjhdb3qIBY/GlkYEDY/pRMo+Vmw6XomIABXEI4Y\r\n+zonNuxhzg1wtsCwdnkjNkqhvUoSKtmtZ+cTLNRVNJa1jY2wMqmNQADV/XoK\r\nDqgaOXcCOZ1a9GHtfkXpAYWqsS2WVvZtgpikRkaC84jtoAcJbJVGoIY6+wIK\r\nGQGsWOQxZyPobIkePo5t/jx570irI5VdwENqlYXZwqVoIjLzdPC/eeTbLkxz\r\nI+76ZOMDsI7rkIY6u0sAmJUG3Y4LxgXJ/i1MnvGz3UIwhbufUdJR7ehA7XR/\r\nCShqYW3lcECQ82imrKYXFBHAfJeQb6S82xGtOBuDU9DkmaAPCHQss5+X6rlj\r\nVvIj3JxIP9Yhc9ocNIlBynafMMNU21S0QKrj3kWYyBaB7CZ2YN6k2j5zisK9\r\nbtayXf9dd3NghuR0J8pY2ADfS66zgy/exzE=\r\n=UOL2\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "5.0.1": {"name": "css-select", "version": "5.0.1", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.13.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "dist": {"integrity": "sha512-0QQZQta641EzzHl+6+iyeKDVXvqvmhvwnZrX58Yf7mlmiilb3bbTtb86DYiCtxhUp+h9g4lULyIXMnOVPBhGhg==", "shasum": "32202b44ae905e06537c7e7b44c3cf7cb9baeef9", "tarball": "https://registry.npmjs.org/css-select/-/css-select-5.0.1.tgz", "fileCount": 92, "unpackedSize": 221521, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFe/vEkcn+IOWXNO2KhWD/VspJi5ORuNYHX5/1FRaRMAiEA+n2ytLS9PTMt5XjDvTudkSsMcbktD2BdfruhISGcOJg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZoysACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBIg//X9ZkouZ9/1GsbCNun3xCDOtA3Rb9dJIXx7jw0mor4oRbeoad\r\nTOIHklIRHpgqccgl2JxuzxEj0txfZijlcOZ5CQhW/Tjbx+h5a7hp1FKHiuQ8\r\nhVIxERuUoGt2tqIOx+VZlXMGSVVSSMASH2MTbwvvWqy/646GnhQ+4m7mwAe7\r\nPEtkI1daHNY8Lyzqit8FT3zeuc2YSXk+lhySisTaTSZV+PDovRfnbjxLp7tp\r\n1xawJ6/bXsKpjFv1g9BPmOka820WUR6h+Uvxvo7AfC/VGUpjUeZeUhMLXlqc\r\n4XwXC+bNiSWZQA/NJKHoPGiY40wLNa05ui80ajPYyRlTgg11aB4oIT1FAnGe\r\napArACAEK02DSXhQzr7fVJvKflIaUI+e5xMKjlOUqOVnhfLOUAW2UA1nm9Qs\r\nd/NO0PlNSch/8cpRlUmSG+BdLU80Es2JBmKHodbVfX4Tq+tQ1qnmQkJLyxj8\r\nIhRjtiBP1xK685fswIoz/LZgvnPBXulACfb/4X6ChGlR5vLjIxwezhy7YDp8\r\nhnZlyibjppGyHZRUDhFj975MTLgIWliE+1VRhgsiP3Ml236YHgfyQMDNOV7p\r\nFnH/UBpMzsNhJ13T7HTWBbMLdibDJaincWZsM1T2QwRB/bj3N2RKDSVzxVfw\r\nPUq3i79D+meOIsfpaD7ZPOfkld3ErTZbPdQ=\r\n=qZs+\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "5.1.0": {"name": "css-select", "version": "5.1.0", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.29", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "dist": {"integrity": "sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==", "shasum": "b8ebd6554c3637ccc76688804ad3f6a6fdaea8a6", "tarball": "https://registry.npmjs.org/css-select/-/css-select-5.1.0.tgz", "fileCount": 92, "unpackedSize": 224084, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjzGYuLVctiCVeaz6NEAjem4z1RMECXEBb5ZS5qAQgSwIhAI+v/79Yd7lqrbh6YuRntgI9CyTP51jIr3vD7e8kk3Y2"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaYuRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqX0BAAl5u7Nnsjk88dm80aR1prAuZipAite/DewumQ4EFiporxMfxD\r\nIThszDtyhvx1uQh/Ewd5yIj4fClSgGmPsc99xlBdlwaK2VoI6CDzTtIau0h0\r\nCzRqn2J8o+MohiddzNXMQSRKG8dyRtxNaeJEjlFniiQGiWDZ6whVfU4qCFg3\r\niVWHOhr2DFw6K8hDZE6B9n+U5bGKp7c7Sey4PjK2GEISiUJ3v1nVsPaHViRG\r\nAcID/NMu5cEKWWz7Sk75/8OcQYOQ8ZJ2RGf/bvzG9ZBlUgWn+miZPGp+lSdM\r\nfrkiitZV4yfJYrfMEzlKECIS8wkx9IK2XG9vQN1tM4HkPfSFHCDss7ypH+Si\r\nvtvD4ZYBGWU1k+3dTU/a60lYge3FjzjtHiOKWs3iQtncafuXzcgdzvAKYNcN\r\nHQe/CmI8iuNBgXXvDTDHBYDbgGldpPPEQus47kYRQ7MUvcpwTuCbDC46lV9R\r\nIvMWLsrsb/BbKy+pFuUj//nzbn7px8Nrr7whDZE05oaEbAu2GzVNMQlRQSUd\r\n5jJEGaj+2hJS84RIq02y3ZYVKhOvi+xLa1SNYVXZ3wh/TiAU/sJfd63OX5/y\r\nqi0TdY4g6LztTWhsUj9r4RvUBwmnCSoM7Ru3fvfntaou5AQeExYJ7UFV/E9K\r\na5ZEJ1OOyu/145LWHQ0bVYCgkZPXJ/IWuYY=\r\n=WdK/\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/sponsors/fb55"}}}, "modified": "2022-12-16T10:36:34.309Z"}