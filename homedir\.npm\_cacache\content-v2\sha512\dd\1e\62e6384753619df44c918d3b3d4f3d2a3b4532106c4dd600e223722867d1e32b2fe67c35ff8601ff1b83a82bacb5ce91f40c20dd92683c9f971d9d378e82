{"name": "@jridgewell/set-array", "dist-tags": {"latest": "1.2.1"}, "versions": {"1.0.0": {"name": "@jridgewell/set-array", "version": "1.0.0", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-LcqVnHCjOAj8BTCtjpwYZCMTn4yArusbdObCVRUYvBHhrR5fVLVyENG+UVWM4T4H/ufv7NiBLdprllxWs/5PaQ==", "shasum": "6a8e62049ab198c5f7daf8047e71947ef46c11c8", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.0.0.tgz", "fileCount": 8, "unpackedSize": 11660, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBYqrz2zY1VXVjR7YnwF7rHQ5NLxKCJWADSaZ3eX6CgRAiAG6+s8QfzKq2TBwWyEemvy7YPSPX001ezB6DvLblp7Jg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZHuaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLyQ//UyQEKDfJ9irToKg69QcNyracnQthpBoV0I7iHOfBFFaZuqYv\r\nACn9UkkT3WQYXeLaEP+/Tb2FYcQUEc7tWTfi4IxxbtMyCyHRb4p/fd4Mxu3z\r\n0rBMa0RExFFV++Hy/ufaU+Ewh5mdS+0eLXl/8iZeh9e+DlFHe+KGgo780Zok\r\nPG5lg5O6jRjZneAErsZI9st9tiWDYZ6tWmcV1xXSX8h9blJzZ3d80Z9u2487\r\n+e9O3qACSODy3ClEOIqw1Hdke/BhPClUey/HIigsSh6pVlKSmk6Rc+0XvgL5\r\nQE8wABD7j3G3JXw42QXR5947l57Am12CeaZ92hcLiXHBHfG/35eroIveb51q\r\nJu3CBQNZIOL/SMMwqn+HnFtAKVuPqAsyZ4OwCgOQiVbHBgQuk1H+SIO60noz\r\nLifCTv+ExIP9iyWyS/oH06C5YtYpedKCFziTkhjIOgAPUYFOwazTRu0MgvPB\r\nfWGpSkPQUKi6ZfwBjqjsk5eo+***********************************\r\nj1Z+JFSCukbtckktTz02VmqQSgCunviq+kMp1vJhR/npAwKuhwe0HfjrYQfF\r\nRoK9ilMdpyrM01Q68XUlHTCJjoWXf64x9hH7KYaOS3cc8jQNjPkvRBHivk2k\r\nVa3Ph5IBcJL2ltyDrU+bTpO5sXBxot/ntEo=\r\n=PNEn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "deprecated": "incorrect UMD name"}, "1.0.1": {"name": "@jridgewell/set-array", "version": "1.0.1", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-Iw06mKX2+V76Rg9Pv8jwhz5PR/8pZyhNZtJvvbgwAGQ9K7CYful9T0sr3cLwPnGEFVgxiklHEc/RSfGsFfmTWg==", "shasum": "bea80659de61bed6d9a7e2e4ff61cde37090f10a", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.0.1.tgz", "fileCount": 8, "unpackedSize": 11660, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEU9T2Ve+T2MysWHIAO4uUk3AlVnw1jM3zNq407uGrRDAiA2gWZ8MQZQ2zmShwmGVAGPlTWzoVUJtuW+CPb1uSj/8w=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZMl3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4vw//Uu7KGkgDOH12toIWBowzMOAzFmtjI+5wXpf3IH3RGIGchPw3\r\nSM8bRlhyLAzpw53+gNByGEZdfvkJGfILNDK1IvBG/celvg2Fql9qItQZvfAf\r\n9myJDrVsScoPNJLbcz6tbBdGnkRgXFj3yLFFYrHBWDeai5h4JXKB/aPJ9CeY\r\nGn/FifNt8R75aNS+WsF9s01TqLRl0Y/Pbs959lluGfozmUtcosxOSjHu9tUr\r\nHM0vSU7i4UJ8E8ghq3Du0Xm7oAlONREdw3LR/QJSQrwnlgGBcoDD1dWMHu2x\r\n9XLcBSpis424NnOPF9nRAaZ4WHfj5E48rpv1UB+KmWzbkmntl2gJ/0sDQnjR\r\nmAj8V3c1xwzvbaPBwmw1In7XHEIf1bAaD9fWE1ofeLeWncfxUVTcvAd80Qpj\r\nPfQMQBdbShFN2iYk8HvNQGggiOPUZ3NDqaArTd7/tJYIG+n8ln1//zHljhy6\r\nSamVNiRBOBqIYTpRm/WB2mDKUvqugd0Qw3lzWGC8GciIq3Z3vbPYQNhcct3b\r\naq4foWv4+hWH9+bkkx4MvM8LvF3h96+TkREyrkb+tds5ZAIAzmyc1UrlS3XX\r\nwgdNiXOMgN3x3SwzqvS/xBHCbstWBiLskVdQ2OLFUmhiB8jTFPj9AMK8e8+S\r\nwWDwZ5OBLJ69Rp7eHxWdoTyGR7ZY2Y6Ke5E=\r\n=pII8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "1.1.0": {"name": "@jridgewell/set-array", "version": "1.1.0", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-SfJxIxNVYLTsKwzB3MoOQ1yxf4w/E6MdkvTgrgAt1bfxjSrLUoHMKrDOykwN14q65waezZIdqDneUIPh4/sKxg==", "shasum": "1179863356ac8fbea64a5a4bcde93a4871012c01", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.0.tgz", "fileCount": 8, "unpackedSize": 13610, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmiShJfnM2HXJT957/yqUub9MEaV/gZuBNkDmd2YjxAAiEA5Q3qk8pCTVw3t/Qj/0CS8ZbX8N9Egst+qAM8r9yqZSg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaNKUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpofQ/8Dp6xD6oTyl37t3EGGYDyjeGQeFb+5MzQhTBN0A2JU+IHQsXE\r\n8uKadbZWv2Oq4Grr7cJBhqu/e4GkBA+vhNQ6hyuCIdLnn9tNInSF5Jb9FU+p\r\ninDeOsfYiP0eBUwU/RX/GMs3a7iUPcxId5Lihv/HybF5DCDTOKpMqu2htQdl\r\nsUx4pWSyrFxsS+ni5MMKFPHRPt0VFbEeIyf3Mozre/sslwJwIZQKa1SCtKHH\r\nT3bbEzjlqdlxWcQQoA1NWoNYteBpS6tef37WmgqfFGc/eDfReeIjbfx8v7hw\r\nMVfEqGhD7LEIUwOUtcyHYWNwBH51XThKEjSkzmO3oIn3BbbJGlhyU8SpjhdV\r\nfKFLR5GCJEL8dctzu7lgoHHWle3n3ZyOCFCUL+wcpVJcATHU4yqPi16GReKR\r\nDCoGEvrU0V8K4tiQOJC7o2Fw6kcA8suPAWpc21XEvQZF4oDNgENkwhddCkCT\r\nagGgQcZDCDlH9r3CiejPfSbB1dHKJPqnkp0rMc1142ef4omKjYQkVy/MYGus\r\n3cF/TNjvIS7lCBkKJlWJfErxAgwgLLKF7I9dmvsTldS3ol5sw/63lBQOFPPM\r\npdRGP/sclLUjhNvHwRMNTC7RXU/okCT6Yrznntg0tppxWzgsQQLfAZifNHNl\r\n9qAhJk070B+pwsTBZMaQWEV1/vvMOgIszuQ=\r\n=l49e\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "1.1.1": {"name": "@jridgewell/set-array", "version": "1.1.1", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-Ct5MqZkLGEXTVmQYbGtx9SVqD2fqwvdubdps5D3djjAkgkKwT918VNOz65pEHFaYTeWcukmJmH5SwsA9Tn2ObQ==", "shasum": "36a6acc93987adcf0ba50c66908bd0b70de8afea", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.1.tgz", "fileCount": 8, "unpackedSize": 13777, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2gAy/Cb5aOkp9fPvLHDIms9VjJLVYbo2iifdDImipkQIgZrtphHsB6UVQw8kSLmiOsr36tqTSDKgqUxMu7fIl42Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidEiAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4ZQ//eYba/4hU9YrJVoVAOQ1m8uCpKXAGDKWRPD9L9WOU69oh0jem\r\n0YeSEqNaP3Pyba1cNJ67BddZpSjYv+n/0lDvyMyWvTdeTt7H7pY0uYUqYR5y\r\nVoK/7YVy43Za/6UQXqfz4fPBB6dORGptyC69KB7kpNcJlXW2AWTdbtVXAkTu\r\nSCIQjz8PcYQaLBfTkwdEE2vulqjk7wBGGyvLg7XRzlMfrV5bghdYsBdYyWp4\r\nu1n9c1m8ZadiRl8aSbes4QnuphgeE1/EQG0sIuhRc4dCeppHNb8Jp8WyEOFy\r\nO7uISAhSu+e0TLMGCvmEN+/sStEnMcP2/XLcRQvpiqZ21ctOOkABgQiVWT7v\r\n84uDYDcgwr/4LSI/tCultnP6cfhGjSpgHmBIidooz1d1TWP67t9PuFe0pspw\r\nG92cMcYQS67cYACcwXxRDTeqxf3yE5MtDDJB/danpWcHXm8APArowLDDunoC\r\n6NSM26nD9eEIhxhQvaGZEm+6ONtyT8fcxotWq5mMnKVIxhntC7O6wLZfXJgP\r\nGPajJofTs3HfnpDj1OxqJ7L0Q3YSn6Rb+nDHGY8C/hwivHj48TiXwBYYb3Fz\r\ngB1BWs1K2z5M9axTCFs3cmqXocLKoi5fn1wmsjFveCwiYnyTmUF/wI7dFYd9\r\namcH5AYDXq7j/Cb5ItvT6qbeZC96BPcTDcQ=\r\n=FeyK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "1.1.2": {"name": "@jridgewell/set-array", "version": "1.1.2", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "shasum": "7c6cf998d6d20b914c0a55a91ae928ff25965e72", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "fileCount": 9, "unpackedSize": 15487, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDay7ktBwWQ4OYGzdQoTo0X6M4WbnlFs9n3BGmL1bgLWAIhANQ5HA+jGmEO6mKhte3bPqStjdlaxLC1P/EgRQ2PK43j"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuH7JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbUg//br2z0IYqPNuY/Vc2hZarQXJhliCWYsME3/8qrgkAuDsMeF/l\r\nsKQwCKUtvh/Nz2Y2vxBaO3YFI/US2q2vsjU9j2TxkXVD8qRyG55Rm4OARvdy\r\npjOA/wQdZ5ZLZ0SmjeZKwuwC/xX7YsFpkkdQaWk2/ghmScKvQxKS40g+/XJE\r\nCmfWwrmRIIVFVejfP1azVGsNdAVRAO9Jbe2d5taX9i7dXZEp/Ezc7UU3ygMX\r\nMThGdlfFDVyEGCQKoeZEwFT3WdrkeGAwdrIeMVyV+QCbGqJDUV5xFFdvD0X9\r\nqMmMDA5i91D/MlfTG0gtIr6Uj66fiJOO5N9XMcNYGSY+ZKxhwgyZuZ2vapwr\r\nNhtWkbwb0XvbNP6Q4v4wc51gAINsBtjI8neNREKg+KdTKKbsGRgJFoYbDSaN\r\nN1mWSagGvfY7rjFLfP4psCqLlZSTYZKTPRf+UECLYCr4fqQeAVJcvTk95K2d\r\n0ud6Z3PFrpNnyJJuisYWIUwQvvpApl7Lt75B3LpMYG8854rtXAiNZmG/jlY/\r\ncEngKpySm3aXOPZqXLvFVrzC2MPBfANVnCv8QqTQZQGYp9qRG1nh5ga++mxt\r\nI24+jFhuNPyOzmnfCAxEHf/EjXsqXjbqDe0ssUSBVFx0okDRE2vQjKOT1xzT\r\nUfGc4DVwIymIJShEF26WEEobnigfk2jn0oo=\r\n=zxBC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "1.2.0": {"name": "@jridgewell/set-array", "version": "1.2.0", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "tsx": "4.7.1", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-0MQgv8btjdoiER7W+VRWJIfg3KBeu3zEZp0Nxh/y2Vx1x498DqteGoBUrJ61OK5qvABTS5kOxFRxwgSqnAJKFw==", "shasum": "2eb73c4d1bd9a7fc1ba5f98e18f412487561f080", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.0.tgz", "fileCount": 8, "unpackedSize": 17927, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCi3+jBpH43XTCjSqrJnZ5Oid4GPLjLqmt0ZpsXIMGmmwIgTAECk4v9ckLrGlPjq8NK1bckF4JN96Xnn9RWvHFbCCY="}]}, "engines": {"node": ">=6.0.0"}}, "1.2.1": {"name": "@jridgewell/set-array", "version": "1.2.1", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "tsx": "4.7.1", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "shasum": "558fb6472ed16a4c850b889530e6b36438c49280", "tarball": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "fileCount": 8, "unpackedSize": 17945, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDOArzPAwDUEMdwJCoH/0haazWzN0i5xT5hW1bEALYqcAiEAq+juCuDHu23+Ph33JNJ6+UaPhL0Yc5Cm2kMzZgCHgHo="}]}, "engines": {"node": ">=6.0.0"}}}, "modified": "2024-02-29T06:03:33.546Z"}