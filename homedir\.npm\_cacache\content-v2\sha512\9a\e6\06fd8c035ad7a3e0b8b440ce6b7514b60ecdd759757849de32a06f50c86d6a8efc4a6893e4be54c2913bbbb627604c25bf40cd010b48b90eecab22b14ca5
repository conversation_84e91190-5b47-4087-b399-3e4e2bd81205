{"_id": "@webpack-cli/configtest", "_rev": "15-7d2e48a1481196df987e965226e7fe7d", "name": "@webpack-cli/configtest", "dist-tags": {"latest": "3.0.1"}, "versions": {"1.0.0": {"name": "@webpack-cli/configtest", "version": "1.0.0", "license": "MIT", "_id": "@webpack-cli/configtest@1.0.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2aff5f1ebc6f793c13ba9b2a701d180eab17f5ee", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-Un0SdBoN1h4ACnIO7EiCjWuyhNI0Jl96JC+63q6xi4HDUYRZn8Auluea9D+v9NWKc5J4sICVEltdBaVjLX39xw==", "signatures": [{"sig": "MEQCIESxJN7xNcVa4WeP5BGF8hH3x5XGUJoKYz3J2PqvPjL7AiBobznkEbr9VmoljVWl9O5id/Chb9xEtPnruBZHmWwkhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBsJACRA9TVsSAnZWagAAlrEP/iMu55LH24bdKji0Zz5J\n1Rb+Kz1CYeFNhLLwfUsa6iL8cZnVc7RUXql3EA8czK3J3tE6IVoYEUkh+vxy\nyHHXxeoRRH+Sq+B6fGbMn9oEcZJhmM2bj37yFD2842qOy7xI2rY6V16p04KA\nRg9CLkkPV/I0CJ0YoFOn5Y7GnrJGBS4rqdnKXz5vylt8yu2dpigzOywpIyX9\nohnZJkKZIFd/esH6KznIRLHPmsn+WtZJXGg0RDB0NliDhUu86vIgaQvO04Fc\ncRYcJxZicHBC7JSvmuSt2iP3kslbPZ+1dESA93lZdEVbEKtEojyyipVKWDCe\nO89RY9BeRZBkNJF0eY0DrYxJCJF73ZFOreB9u4pvW8JS81wVghU5uLMQakxk\n7Pc17I5WS5nWq32Yjsk4KO0o003XiNSt7wS4Xga6CKyizhBxJl63CN6YrCN9\nqug+p2YxWjyWHa547Kq2nc+x0UqzZmLK79GNxkXMNPitr/tKtqipw7E40OMT\nVjvw/Xm3N+y2/Ni3Ao5vclXHjO/7cwU8knm9sH5q67iinuQgJmomcSFannlO\nQvzbCtYnC9cLm6I9cua4VAQ4YPXZyQe3E26tBfB0wEAKyna5Z/4wECxT91Kz\n8VVh6r8A6vYMfsJjIsAzzgQivcS3zMUmVtCP6BJ/04Ia9OjYqWgnu13Yfq9S\njUHs\r\n=Sor5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "f55d44d243d6e101f7e7c88ddf0e7c567e33bd08", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v12.20.1+x64 (linux)", "description": "Tests webpack configuration against validation errors.", "directories": {}, "_nodeVersion": "12.20.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.0.0_1611055680125_0.1915210643759706", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@webpack-cli/configtest", "version": "1.0.1", "license": "MIT", "_id": "@webpack-cli/configtest@1.0.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "241aecfbdc715eee96bed447ed402e12ec171935", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-B+4uBUYhpzDXmwuo3V9yBH6cISwxEI4J+NO5ggDaGEEHb0osY/R7MzeKc0bHURXQuZjMM4qD+bSJCKIuI3eNBQ==", "signatures": [{"sig": "MEUCIQDJfEWWpKsQJgbyTNnzTmPilyWlOcsXSysnSH6cwLf2LwIgR4MfoTFpVm1YPh3K+n+zDvya3D+jxg3GrMtg6DREPXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGYXtCRA9TVsSAnZWagAA+bQP/3tr156J3/7+LjWQPpWQ\ngfSS9LcewQsywCKJubistG6mqAI8dMm/jMdq2jN3ZaaBx9c/PCP+Z4DF5hlJ\nsDGouTccwxSOPXijGFJyvkq8fQeMBBVcZcCHAjTzRudcYDM65zPsnqknIgfq\nQkPlJtUVXdyiax67Rk63xkNHgXliwlZf2JKXh+dPYPBfK4jzkJa2uDQ+mdn8\nAfue+jb+JsKbztnNzLaejrW/H2yFybP9DOp9wqb2rFBASwwSpmDJjNHG65Lk\niR9lDm+907E3yF8lPnTqaXGi48iELzdjxTIOS2+Ssclue3FfEXjmk/Ocxmys\nmjP2DaWqRNL//V2M18Mb8rGorlbX/VdSrE7fcJijnXzdfEwl4NoKwSRxbnLb\nCSdc6eUKFOHA5IaMxul85h3Nv7CJT1Pcb7jzfS+eLSIY/2QOgw39J7u/xokc\n+csXpVlE+mtrDUiclEbT8cbTMTudUTdXLgfeLS7zDi4txTB/kI1mwpfUhiIl\nmevIWi6xN1TImeIMrbLPVsFAPmsrho43/cVDxXyB5VEeMRfEoGSy8UE23yR3\navtleTcTAWvMO6EuA1i2xyR0xWUSbc+12qW1L6f2cukDBgCKz8ybw39C73iB\nUmUgYEIQBrOdRhoa2IOSuRmUehpSlJiHd4jINYx7U60F0XNqI9vGtZGxUxJF\ntccH\r\n=X9dQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "3bbda71e9637b7d20f3f49f9e080e27d8d8ae929", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.22.1/node@v12.20.1+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "12.20.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.0.1_1612285420849_0.20386851496012537", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "@webpack-cli/configtest", "version": "1.0.2", "license": "MIT", "_id": "@webpack-cli/configtest@1.0.2", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2a20812bfb3a2ebb0b27ee26a52eeb3e3f000836", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-3OBzV2fBGZ5TBfdW50cha1lHDVf9vlvRXnjpVbJBa20pSZQaSkMJZiwA8V2vD9ogyeXn8nU5s5A6mHyf5jhMzA==", "signatures": [{"sig": "MEUCIQC2bbE1MYkc5rJMGiM1OctyBiA8+ZP8UQE/BjKd8VjsjQIgQtqGG/3PMYCb0hZq14BS8AWAlcUHfGeMhavT/fU1be4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX1JHCRA9TVsSAnZWagAAqAEQAIqw8I1LlC7cicMyda0J\nGvFZrdAKvf/KZdPRUlI82r7fxh3+VbjS8+XFGGctO78ZaGUv9j/p23dYibhX\nrcHvVMl2/fZKvEa56e0XS1kRnghKkV6DJvmW8HTSEnlOQRoR5UkcBXQIJbk3\n4uxDlSJAbMGUrH3/qK+Jk3GRcNo/2CfaSCmm/ihdxsLnUObJolHJ+OdYQO7F\nlhppglWYQp/eWFfeLtPdVwudpx0YV+a/zDzgrYci3RfrPo1WZX3GKAWBfGHT\nM8tj/NQ5Fik43sUNHbzCvrj7uD4fBVq1cSQmVAqi4+EW6CZ8u3Bgy4d1cEkb\nU7BfNGcZMf5VrEUbJIE1zJSOF3mnq9rwcTuvn74hPFfoe8hWKvxkvvPzbP1k\nfIaTdQiV1RFwyyjUXmMHB32eFSa8fFvdI3Vui/uYtVmux07sqHT8vQOXm9QP\nhutwhY0V5E5YrFhpFhqH+TrNXTKnPIrSdtaV5PLgK92TAtKpupQ4fXS/45Ar\nQSRQBW6Xn8BWP+a3aRWQXWt1+ZOFbM/MQ97Qm1p9LISmfqBCEwW5+bT7g62j\n8ipRsonQBVoQ38AI5Aus7d7dT86ducom0pfJEsbFXD5CUGDy8OW8ePll9Nc2\n7AXzd617x+5fdvXtE++ap/qr+SB9Rtk9sQNvfnza1VEds/R6jbzvXAJ+YhEa\nNJBu\r\n=n2eM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Validate a webpack configuration.", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.0.2_1616859718560_0.8718577460488288", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "@webpack-cli/configtest", "version": "1.0.3", "license": "MIT", "_id": "@webpack-cli/configtest@1.0.3", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "204bcff87cda3ea4810881f7ea96e5f5321b87b9", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.3.tgz", "fileCount": 6, "integrity": "sha512-WQs0ep98FXX2XBAfQpRbY0Ma6ADw8JR6xoIkaIiJIzClGOMqVRvPCWqndTxf28DgFopWan0EKtHtg/5W1h0Zkw==", "signatures": [{"sig": "MEYCIQCws4NgcbgyWJAWs8hYocbA05qDyhRBR5TQdC6Dj8HbgQIhAMCHiGkgBAvaR47WHnryjv2YIDH1nnTVWxx52g1fvgMp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgk+rsCRA9TVsSAnZWagAAnUUP/jvf1CgoEcz5HepG+5TH\nypBD9yOemAABY6E/y6nODzJT+RKzdzJSX5MsvO4TJHMdzG6F2roCBIhICChd\nJFx4b2Fa2hINo5FyZRA1qLcXysD1yE9DhxilpIodLE3CwFOc623GJhky6gCB\nE2LevuWvHX1a+iAoja4ijEsnBSTXHnVtOh6ja/XNBFj6ZvyiXGsaE3rZdaOJ\nfv0FhH/a3zaIrkXNwB1+MDoM5pp1TdGcXpqSgedSBn+moZxXRbWWmnMiz27G\nkulZh1Ovav54RDNSn7+EdoWOk6ZNq0Yfaa239YdxsEITF3rwyhgExcpZuOLF\nwUdsBxhD+KqqoRVDowhXvhx8+ewwV1kXLWkTvuGfG+nTkWRpwzZI88dAPjpe\niJvEYWUZdMDsjz/tpP35vg35nBLGPFSTl8Y3v9d7QB/vuumPmhc1aK85HzAh\n4nHS8BRgqXCqglKmrHPVx9tnkibDyZjRbgEbzaQGextqvbr3hjkCdrSo5bOw\nsMRmmXKWFKt864nNrJzTfwlJRPK3RFkjR23o1NHP4YENMg30Xz7GDBZHsnWG\nJEWZVlzjsR0URmdCN36Db+jrm/M9beMTsec9+Y3Ik6Urb5TYiv8gZ8ErLLos\nXPNY55TXXy0vBG1KQLEEPX5IOSivzAHlS2uZ2KeD6TiAdOqylPgw0isFqEx6\ngJtK\r\n=nB+S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "4edf3acf1541344f71cf7da7c3c654347f19aea7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v12.22.1+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.0.3_1620306668434_0.3029344629063606", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "@webpack-cli/configtest", "version": "1.0.4", "license": "MIT", "_id": "@webpack-cli/configtest@1.0.4", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "f03ce6311c0883a83d04569e2c03c6238316d2aa", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.0.4.tgz", "fileCount": 5, "integrity": "sha512-cs3XLy+UcxiP6bj0A6u7MLLuwdXJ1c3Dtc0RkKg+wiI1g/Ti1om8+/2hc2A2B60NbBNAbMgyBMHvyymWm/j4wQ==", "signatures": [{"sig": "MEQCIEc1bfQrDcfqgZWjH4/HLrIhx+Q2GzkdUlV7zWsbKWlpAiBnhPsZh5fLWKm5/n2VC1JTtbTM3/oDKamQC3Jwmplg7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvhoBCRA9TVsSAnZWagAAn/IP/RbUtKxMv46MD9eQkzN1\ni9jHFdBldhm6z9NfNpYtIl4MzixLfxPkfIOopVHNenFmG6MvUTZ1Vbqjak1p\n6K1O/JlnH69RMcSFfrAGpalWETyBxGu+9HP40A7+Vkbf2wSBprrYxk52iswD\n62BLGxDnxRAZsUVbk2dbdxfHJDIwCuuAbYq2n88+NmuWsG5ocTeS3WWkM6+9\nMB3ZwGn4sb0gpOYFXcqIrget9f+a2B7i1kdKp3Ecxgx8O0vLvUGdXNHCBcm4\nKaGnIZAvOcWgciScU5CE2PmmCHnAURbSXzvuiG1CGOVYFAUX+yeb4q5C0uVs\nPNwolTzHYftK4n4k/CfzX6GZRCiSJr56aPewkJckJpcnnwyd5/aFG0gab9c0\nqgVkck5ITnxL1nMSnFf5z0NvdM/nlxdkCpoYu9puhCxOQIZZ2uA82piui8/d\ngl7huhJiHTPnzvxQAsVpeLjZx6i9rVDfddk9f8kd/y8wPRyFu0SsX5UGfi7G\nkD3cZg/5RIP04bx069hTTbNlAZQkgDjs7um/aFVXZhVe3cCBIk8Ry4+wXnjA\n1wFIrol9lpSvJYr4H/Kb78EMlLXoQ22R1swbpKPzQ12aoSzcZLZycjF839fs\nsUqheyffExlmAM6LFgI8dD3EPgYaSm9iV/cpI1o21n+9BJ4cnjVpWPak8BtG\nrx/P\r\n=Bjhu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "2be9b9254009598e021b830091fba8832dfdb57b", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v12.22.1+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "12.22.1", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.0.4_1623071233633_0.6585911860796558", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@webpack-cli/configtest", "version": "1.1.0", "license": "MIT", "_id": "@webpack-cli/configtest@1.1.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "8342bef0badfb7dfd3b576f2574ab80c725be043", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.1.0.tgz", "fileCount": 4, "integrity": "sha512-ttOkEkoalEHa7RaFYpM0ErK1xc4twg3Am9hfHhL7MVqlHebnkYd2wuI/ZqTDj0cVzZho6PdinY0phFZV3O0Mzg==", "signatures": [{"sig": "MEUCIACbF5VTF8WQT3hfbfER2sEof8suZX6FA+B9COL2y7CyAiEA3x+73ZJaFMV+TbcvxRRajM18WmR+Fe0JXORZaziN5+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2hJBCRA9TVsSAnZWagAA5yMP/1QNqedQD25DkNnfYR0j\nuDl+Ua6ZCN+7WbMiv2fiHMd+Z69fnjIlGWcouvGDi7+MLOkFpDW8UW1YndcU\nEw1jjVLHuSZGwANhWrwQ1XOXOFOB9qN9HJgyZfPrPUk187mhJSWmCcGd0fap\nIgmUqKqWoQ6vECLo75B8549m9OvVvdHy+p3XCS/V8dnKwTgm2AOqPBarbqcS\nugIZZC/XvSl6w27XFnXN5klCAhK2gwStRwT+kDISWx8ptTCk/duPtHUpi+Uk\n2b8oacDD1h4AHk3Cp2TlywuI7EhDLH015jRGTBeJphOMziHX1F7WQ3SYeQyJ\ncvhCWLjCoEKcdDhlwDnCsnO/PeoK7KqX7Io84tercINVDmjzd0MOVF9+Oa0F\n+5V5Zjztv4fzn3XipVwfMcKbGi1wDBOz35ahHq30RLrv9EE3YKhhWO3pmevB\nZbqwdiyXqZXewU+eisUbf0r6gh0Q+HqzG3jPK4XYB1B0IuVXtIsNSwju1UkX\nsxpFMVfFXIESGNxwCErKLbsJEKcoFvs+SiCCXLFLj1ILS+boEqr+FrZ4nxFD\nHzTRMwfvzCZn60GFSriqNg/z9GCFKS+Z9BGLutizHZCj2aE6Lf/bNQzbAtFI\nOnvsIYzcY4GW9Rk44ayZhC16LzyZqQIw9Hqi+mtRazn7wmAxx2W9CXIKB/VC\ntqmg\r\n=UnHM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "74089ce03c3cb4842d188f0a3a749c2c173ac84e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "12.22.6", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.1.0_1633553224100_0.7213447405822304", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@webpack-cli/configtest", "version": "1.1.1", "license": "MIT", "_id": "@webpack-cli/configtest@1.1.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "9f53b1b7946a6efc2a749095a4f450e2932e8356", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.1.1.tgz", "fileCount": 4, "integrity": "sha512-1FBc1f9G4P/AxMqIgfZgeOTuRnwZMten8E7zap5zgpPInnCrP8D4Q81+4CWIch8i/Nf7nXjP0v6CjjbHOrXhKg==", "signatures": [{"sig": "MEYCIQDQariPSHz+n+oCn5v8neNR/Lt6UsQ0WISo24m06uWrRwIhAJPeyLkGD7IKw8dXmhzbMUb97nITlxcMCscPObIKXgv6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7qi+CRA9TVsSAnZWagAAP4EP/RwpnZ7DrkPJi5tnmdS7\nVpKUiWHfUTwz2qo6Y57ZJhiWw1IsJFvjnxZgIennKSxqphWMjryuifGQ+ytH\na3YBN2jjyyQBHoPwv2GSIb4laD7H7GKCoSaJBlnnncY3s+c4/EIFkYXK4BVt\nqJXZBUv4csuDNuO3gOw9gEQ+JQQQYGQZZHcFZllNuAgpkrpF6KaBA4hPlMiz\nndAKuIRlqYAZX6CodrY/qHzuXs5KqUYHu0MmC0OBpdRgDTjdKLnkRaS/HW7j\n2asG+UyrH628e/U7WJkVJP+5Ump14+FP3ChLO6nW3WC5cFhmU212xwihkobQ\n3F1BXMbiirbNoLEHTtA04GY79i3Lr/iM0q5aYHvqFzdZZ4HBspAQw19vig80\nudkIJSEcgCbZZ2YXnzPWlkvlNwWKC2lOhLVYLw/EztoHHEd1GB0PO902xARp\n5AeOY5IiRBrISIxulxdX/gbMcA2hKSUtY10DLcJRQWtVK9pq/aTS6N5ZKR9k\nZ4iPZBg9Ih2jPTZV0aMRN0Mwa1lxONT8YR+032QueuiRF05tadOkDYIqiBwW\nZHeHAWozX86a1BDEZOFisVaE8iR8egzaCSVy1WN/ftociNWIjQQOks161S2+\ncWtbrylNLz1mbW7Dmt2//l9GPAlnKB9oyJuYWv4wYqIbb2D4c988JwQvcz/3\n1bVv\r\n=Eb5Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "4f9c55ea0ab30baf7089514853d70b28377a0b1d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "12.22.9", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.1.1_1643030717949_0.024545562288462497", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@webpack-cli/configtest", "version": "1.2.0", "license": "MIT", "_id": "@webpack-cli/configtest@1.2.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "7b20ce1c12533912c3b217ea68262365fa29a6f5", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-4FB8Tj6xyVkyqjj1OaTqCjXYULB9FMkqQ8yGrZjRDrYh0nOE+7Lhs45WioWQQMV+ceFlE368Ukhe6xdvJM9Egg==", "signatures": [{"sig": "MEQCIAjtXl3MT45ZpvlPeiHwskxcCaHMPZ10VS0IMefqhUF6AiB68r6CqPUA3ZGo+92hMWW0gA/pRyzVFi/U4q6HkP4UAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipodgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCPBAAn4F8edb5/cwtl8G59A5XUGhfSu16h8kxP6hnpLIfYW+/jFpo\r\nmSbw1fRCFyOzQeoGKGPiezQXEJzz7twRn53AOX6qsFfl4acyAj/emdGnUcqp\r\niNR06yxrhDI+0b3BF2SRGr9ETkDXUtrU1x+YEw8sgz4S7Pkf+aqawmyv6EQy\r\nYmjdl2Cdn0LAP/9YgtBG31o9rDJ0lUiZDYr7KQ66PUrXs1WftDAkYP84vwuj\r\nFqKUxaf1bjTclBLIPnp2UBkpR8b3Op8T0lM8HM+fRlpdHg3GwDLrHdBH9kR+\r\nyfgauZTUem+AcP8wCe2ROde/UODIy8JPH/4UjyWjdc853K/jxgIvBwt7oDdd\r\npBliIqgxd24wndRO2VujcIn/FKvC7zDiUxlJu9Vd0W0ixtm9U7P9CM4RiX9l\r\ngl4X1cgBV/TwoVHW/AaVsBW5lcFdbFNGoE0HnGBhet+B/ozGyrBoN4MkBCms\r\nvVDQrzCZ6BJI6Rp1tmaX0eqVRIBwv5s/VWoj5OVAf3ViEQpC/FVbmr7vNhC9\r\nq0N6RnDIoUz8376CZ1cU//AtSDT5MM5Wx3Q3BT035qOar2HE2vntvujyb0+E\r\nMF95jBaPSfZpDD6nvK8OzvkoWHYKQc4lJCtC5Ab2pUgx72cw0VoiXlHxftdn\r\nCx4EP5y64hbPsv+o4yrU2iDUmh05uKpJliE=\r\n=z4o2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "20882d463450d010bb76e0824fe555e9785e9561", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/4.0.0/node@v18.1.0+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "18.1.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "4.x.x || 5.x.x", "webpack-cli": "4.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_1.2.0_1655080800490_0.30762958668543394", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@webpack-cli/configtest", "version": "2.0.0", "license": "MIT", "_id": "@webpack-cli/configtest@2.0.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "5e1bc37064c7d00e1330641fa523f8ff85a39513", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-war4OU8NGjBqU3DP3bx6ciODXIh7dSXcpQq+P4K2Tqyd8L5OjZ7COx9QXx/QdCIwL2qoX09Wr4Cwf7uS4qdEng==", "signatures": [{"sig": "MEUCIQDi+QEWH3g/0MMdnS35b4Spfgb8Wf6OGmPRH1YZNOO5lQIgNDinKXcZ7Ew9bXU/tsmFx9yD7OUeel3kw1crDvPBIMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdbmwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTkA/+NQ0Q9arSOuidvxFFfQQ9hAOb1/8TCuWX6D5gCO/+esEmWayw\r\ntAxorGtBMXPhPlBGCNOgSGSAvZz2dcKymNq8zI/epljITSrceHwrmkxgqPrq\r\ndrn625GsCMIbBqpaY7qfGzuXFxBIXcVdw9Brt5Ikurka/xi4V/QHfeCCfda/\r\nKH3ZKc4EMbia2dpeNsrYS/k/QATSAwBc+AQUdhyQaFWjcUl6xxG8WtYGMx/e\r\n4ocxe5e0iPsVVGTWzsGBfKqeBR03Ylr3oPt9sbTV4WFdrxbvzdIEked+3Dgx\r\nwBEhjDY0yzHQm4zWqMt+88qza1ZIl32+zbDAxRyhKMjdwnsMKclgaxzIqcrY\r\nQgMFC2o1ClGItZZOtKW5gq9Bdi3fd4G2yhoJ8Qdr4Bt7x1fiQOms/6QGoeQs\r\nqv7p24lTgrSGcLs08vt0aikg/NqPKc0hOOGhKo6KdiYl38yehrsLtccrs8wM\r\nwU0Y5OjW7YZWOgYFYJx8y/a4pDBfsFEg9oV2QFqw0OASRb5geXkLyeKiupte\r\nyOHFhCeLHb77Y2hTBqSOu++Gi3tY8MIF2ZJE/Pu0ykET9kFhcC3LNWixrSV0\r\ntx9MyRtIFbJ29agH3rKY8BOL5pR1jGNg2FeLCgXzHDMlAE6n3XuK9qU4rPG0\r\nwEaOEHsmAzoH2zma/9G9SbfmesZrBe/EbZo=\r\n=Xyij\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "1d6ada1a84c68a00e56c536d2f004f60939bd946", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.0.3/node@v18.7.0+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "18.7.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_2.0.0_1668659632306_0.2694747364446035", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@webpack-cli/configtest", "version": "2.0.1", "license": "MIT", "_id": "@webpack-cli/configtest@2.0.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "a69720f6c9bad6aef54a8fa6ba9c3533e7ef4c7f", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-njsdJXJSiS2iNbQVS0eT8A/KPnmyH4pv1APj2K0d1wrZcBLw+yppxOy4CGqa0OxDJkzfL/XELDhD8rocnIwB5A==", "signatures": [{"sig": "MEYCIQCbGipeFrdE9M4kzJqCzYs70jJ2O2rglp33REtYRSi/HgIhAIK89ie6iS2T6Gv0DQCAY3Lf6Ij59r7euLb8grz0QgI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjitvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYIA/7BeGkU+IMDSxnFLUt3hnxV377WjZRTmEIstkkcoOkfEDa4wmF\r\n0babKcpNwv3yQ25yDEUV095CohdV1FWoKLcKSVeT3gw9V8fzhTi52OdNq3ss\r\nnO0Fqi+EM5coHY6ha0wCQAZ5kOEhdDGMXIO2kmdwvseRDeOWLJxI7MvfN8z6\r\n4hjuNH+zney7AJpnENgeutZ2JK8vU0oq6no0Mh6sqVIglOXqNXjDcM0E8bMN\r\ndxrPsF81/UB8hp99SD0QWm6haQZJuwk7n2G8hbn/4mlyyvdrMS/W94RH2OLs\r\n4OQL55JcCOkrw+Tr4P64mDgjdQac75ppQROwbry6gMn0Na125H+J5BdgzfmP\r\nEeubyKkeki85lcVYp9TmFQj9SSISfDVHFH9ygROp9uq+Zi1ZwUihsfjmYB2z\r\nGT5IOP5uZ9EW1iulbn1HOywor8I2hFs/AUmrlSZFeMXlrR0EF9SZr7MIlmI6\r\nHkczhi5ZocXW1xqAg8/B5J16MnFtQ3IC2uyJ0iUFahlr0mck1RheqJ10C39A\r\nTKRCHtsjw3WBe/hD+MNIbZ6u2ctzGIkynbFUnys4pDgLmdPFFieOwxMi0lLa\r\nrNvZn8YHQ+FZmQjPDo61jGP11uiP14SiXop0pLBXVk7hkd+yNkzfUotZv+bR\r\nXG/Qa8Y8aIS4QrFxCpsqeGzqlGLst5RdANE=\r\n=yyo6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "4a0f89380b3f920652bda7dfc0c23405099b94b8", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.1.0/node@v18.7.0+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "18.7.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_2.0.1_1670261615682_0.49631350665055884", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@webpack-cli/configtest", "version": "2.1.0", "license": "MIT", "_id": "@webpack-cli/configtest@2.1.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "b59b33377b1b896a9a7357cfc643b39c1524b1e6", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-K/vuv72vpfSEZoo5KIU0a2FsEoYdW0DUMtMpB5X3LlUwshetMZRZRxB7sCsVji/lFaSxtQQ3aM9O4eMolXkU9w==", "signatures": [{"sig": "MEUCIAW6YmUVoEOIZeXT+z65gONQObN5Z+Fkjh1MVSkHf2vwAiEA8HsIk4P5OS/4W8G2gFOjkRK8vOSVt2XrxaA1udhmvwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "5aeab7cb04cfbf42bf23dbb32e28a1e78c941887", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.6.1/node@v18.15.0+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "18.15.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_2.1.0_1683421296886_0.09008051256968463", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@webpack-cli/configtest", "version": "2.1.1", "license": "MIT", "_id": "@webpack-cli/configtest@2.1.1", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "bugs": {"url": "https://github.com/webpack/webpack-cli/issues"}, "dist": {"shasum": "3b2f852e91dac6e3b85fb2a314fb8bef46d94646", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-2.1.1.tgz", "fileCount": 5, "integrity": "sha512-wy0mglZpDSiSS0XHrVR+BAdId2+yxPSoJW8fsna3ZpYSlufjvxnP4YbKTCBZnNIcGN4r6ZPXV55X4mYExOfLmw==", "signatures": [{"sig": "MEYCIQChsBAhS5Nbs+eR4zKwwpJUAOo5hpic9hNKTGKCGMhDvwIhAPrTsE6NrsEoX1Sx7hsDJ/8ThmK7MJAmkADtMDKbWa6b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4550}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "gitHead": "e879ce4ef91a9a89ca5ef74f533391cef5ba009d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/6.6.2/node@v14.21.3+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "14.21.3", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_2.1.1_1685844835419_0.63554992612077", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "@webpack-cli/configtest", "version": "3.0.0", "license": "MIT", "_id": "@webpack-cli/configtest@3.0.0", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "dist": {"shasum": "40a18b301e255ca8b90338cb82bbf8b506ee54b1", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-3byRXqOvwm/zGM0OhSbq15aJeX5ZUSe0RS7gfzH9wtX9UX6foShghZKxNOq+oJ59s5dsZrvBk4WHLfSnaBJJWw==", "signatures": [{"sig": "MEYCIQC1jAZ7um+76ACtTCJjJntLyt+XqkKkIl7ifMfNB7RK1AIhAIP2uHwP0oJvDj25nLVyDspvgzI0YNL2soycD9na8ZrV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4550}, "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=18.12.0"}, "gitHead": "1bbeaf0479e9b911f80b302c60cda2339a80511d", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/webpack/webpack-cli.git", "type": "git"}, "_npmVersion": "lerna/8.1.9/node@v22.11.0+x64 (linux)", "description": "Validate a webpack configuration.", "directories": {}, "_nodeVersion": "22.11.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "_npmOperationalInternal": {"tmp": "tmp/configtest_3.0.0_1734625972157_0.6990319984662723", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "@webpack-cli/configtest", "version": "3.0.1", "description": "Validate a webpack configuration.", "main": "lib/index.js", "types": "lib/index.d.ts", "license": "MIT", "engines": {"node": ">=18.12.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack-cli.git"}, "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "peerDependencies": {"webpack": "^5.82.0", "webpack-cli": "6.x.x"}, "gitHead": "480b33d23b277b3a55310bfc6dec8bcd3d4ed404", "_nodeVersion": "22.11.0", "_npmVersion": "lerna/8.1.9/node@v22.11.0+x64 (linux)", "_id": "@webpack-cli/configtest@3.0.1", "dist": {"integrity": "sha512-u8d0pJ5YFgneF/GuvEiDA61Tf1VDomHHYMjv/wc9XzYj7nopltpG96nXN5dJRstxZhcNpV1g+nT6CydO7pHbjA==", "shasum": "76ac285b9658fa642ce238c276264589aa2b6b57", "tarball": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-3.0.1.tgz", "fileCount": 5, "unpackedSize": 4552, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmpTayuGUPdKMlZ7L+/sbXp/YplxeRiZIr9s08sgk8IgIhAKiG/MQorDi2aeXETYhLG/LTKjaiV8jgjyOXjgDd32ge"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/configtest_3.0.1_1734703927067_0.9888368167842203"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-01-19T11:27:59.876Z", "modified": "2024-12-20T14:12:07.461Z", "1.0.0": "2021-01-19T11:28:00.242Z", "1.0.1": "2021-02-02T17:03:41.056Z", "1.0.2": "2021-03-27T15:41:58.688Z", "1.0.3": "2021-05-06T13:11:08.564Z", "1.0.4": "2021-06-07T13:07:13.768Z", "1.1.0": "2021-10-06T20:47:04.253Z", "1.1.1": "2022-01-24T13:25:18.084Z", "1.2.0": "2022-06-13T00:40:00.606Z", "2.0.0": "2022-11-17T04:33:52.537Z", "2.0.1": "2022-12-05T17:33:35.871Z", "2.1.0": "2023-05-07T01:01:37.025Z", "2.1.1": "2023-06-04T02:13:55.591Z", "3.0.0": "2024-12-19T16:32:52.333Z", "3.0.1": "2024-12-20T14:12:07.256Z"}, "license": "MIT", "homepage": "https://github.com/webpack/webpack-cli/tree/master/packages/configtest", "repository": {"type": "git", "url": "https://github.com/webpack/webpack-cli.git"}, "description": "Validate a webpack configuration.", "maintainers": [{"name": "ev1stensberg", "email": "<EMAIL>"}, {"name": "ematipico", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}