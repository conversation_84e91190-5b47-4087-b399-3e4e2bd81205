{"name": "domutils", "dist-tags": {"latest": "3.2.2"}, "versions": {"1.0.0": {"name": "domutils", "version": "1.0.0", "dependencies": {"domelementtype": "0"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "7de6c599b4e6276d259310957acf548da1ccc16d", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.0.0.tgz", "integrity": "sha512-Ph0l6MIRpBECZuxcMqAncqQ1KluUjoQs9sU6hlF93fOhRKlnheNCooccPzTeUwR0F+ZBctw55XEzXaU+CgX+oA==", "signatures": [{"sig": "MEUCIQDSQFabZpNqK4s1LyjsEF31mBrERIDb71Y5ROv3mlqzYAIgN59gUOkiPyea2+FeuIzqsTo3vEh/vAhGSCeBHDCruzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "domutils", "version": "1.0.1", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "58b58d774774911556c16b8b02d99c609d987869", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.0.1.tgz", "integrity": "sha512-EtnpDnVNvVaRBHmc5PV2lGHlgic4IjrYgS1f3U/M7CjihJIKfsJFvwj6JfAjr7f7wNDcloq3rOb08xAiL622gA==", "signatures": [{"sig": "MEQCIGVDfeBCJbo7zQ/bEwpE1rc9ra1IIucUg7yl08h3GUgqAiA/uuP2sKbl8C+w7pmVwfRg6CrAvqo/7gD48lRceGntCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "domutils", "version": "1.1.0", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "81c91e0697dccfdecad4f56c097cdfa051d083d3", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.0.tgz", "integrity": "sha512-IDfGFRpWcm73hBYjNR3Ir3tPy64t6GKPE/A7kUkvspReSDikUA2fwy8AGflh912tP+urCp6WM4SMTZUZMM0Ovw==", "signatures": [{"sig": "MEUCIQCnUT3vP8C/hqMeZjNH+DHGO3EfLMwjWALljm2SUtxIeQIgMHk04uT0wXteefMVlt1KWYxM4Mn+wDxGQFWIp12zyDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "domutils", "version": "1.1.1", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "64751938ed7121a48017b1e14fb6b1042cf359b3", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.1.tgz", "integrity": "sha512-Oj7fbFIp4EMjxpzwtE08pfR27Vh8bphDFRvzBbJJEPo9hMvlRzyWNbTs3kEKOvdsm3wjI6sLkjNtQZ16wUUTrg==", "signatures": [{"sig": "MEUCIQDpFNhtz6wfygS9LXaw2VAPEMnLbh2v3rKKTAmrESZkkgIgNv8dtxGXRmd+ibIKxe8nSyclOiGHlD7K0G4b/phtdlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "domutils", "version": "1.1.2", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "fcf1d3596cd419818041cdaf6f7894a8d127bb07", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.2.tgz", "integrity": "sha512-OBBZTj3DMtzd0XLjlfWfJp5/VD8EkzEj/bop53+KruJ484giuHj+5TTkMxbR1uQSpYE6KjK81ykDxn4NC27GQw==", "signatures": [{"sig": "MEYCIQDXES26y9mDdB7ENOvyh4lDPU/vyN47kLD4v2tFiU/PBAIhAIr3YXErt0b0dsZLcHXAFbmxisE0OzbenOpkqyyOckJh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "domutils", "version": "1.1.3", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "e86a40882337a614b650259a39197f178690a8ea", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.3.tgz", "integrity": "sha512-Ls9zGf4SyGFllxAXYic6xe5bBgWK+/rOdVmFhUvPKKLoRFew/mRvV05VO8pqDCnH6DATmV9GBk6iTD7CIvUIkA==", "signatures": [{"sig": "MEUCIQDZB2vh+gUOuFvoeCgRF9ZPVJ4Fitm++M47l/2yxMbBaQIgZ9fq9sHllKwhM2TdB2Q+uwkABqd2wYPH7vlsZN3Fg1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.4": {"name": "domutils", "version": "1.1.4", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "14b774276187066c76f80141f7eac47a22f77248", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.4.tgz", "integrity": "sha512-iqNemKoMqryKyVCUy8/oboQU0XpI5hly7Nhb1T8dCdyfU6XIZQ+hK8Llwxh99BAqJeGYiCoV7PTp4i1vrx3pqg==", "signatures": [{"sig": "MEYCIQDz9s/KD4W9XrCse3N45bn6Vv9oRFTrMyX/s40cf2g+HAIhALZLK/Yie1k4tNMRp2TTVBn1vKfTdoOVGZnzVYYINgk1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.5": {"name": "domutils", "version": "1.1.5", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "6d3f86d1444993951afdd228a46f73cb2f688328", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.5.tgz", "integrity": "sha512-hpeUEsOt708AAXl1AORl7DIEs0SHxHTrGXf7b/SrEaJXQmhFXS/zPr//yFzhJ3Ijzin3s1ABkWYt3nXZCfrC3g==", "signatures": [{"sig": "MEUCIG3z40eSauVO3jPUHIcFGAm3RCSXOzfu0lrBGuu4OptJAiEA5iAVg/kX90GvZ0P5U/tgTVUEeNKeQL2pvh+Md3sJMsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.6": {"name": "domutils", "version": "1.1.6", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "bddc3de099b9a2efacc51c623f28f416ecc57485", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.1.6.tgz", "integrity": "sha512-ZeagMzMKyk9GSFMqV3x3uHgRN36hLpSOF6LIRXmftce0UUqFsAx/azJAJ4Jc+9DYKmwROH5HLOcOu1OPARWwNg==", "signatures": [{"sig": "MEQCIB6NTR5CG0kyLbfEnTzgvjRiGAV7BDTiuATn6R5aKNsUAiBxE8BL/Zqk2S3xOCkHKmaQw4kHjGXgj3QzcHQ0H49CoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "domutils", "version": "1.2.0", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "4533940831c143ddecb73be4877a0ed8d47b3ba3", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.2.0.tgz", "integrity": "sha512-Y+WDgwhWyd9iWr4gTpkJVbeMk7/y8uKYXerCRTMGRbgpqDxa3Xcu0scT1CTRnIOODhhsEil4O0qMWd7pQcdECQ==", "signatures": [{"sig": "MEYCIQC3wIsthsAm5mLBX9e40YOLhDzHYQxyCmXc9QJ3diYIVgIhAK8eDbvagAo+x11NYNuxkgwACqSpHjFdS4j1WAAUOBl6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.1": {"name": "domutils", "version": "1.2.1", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "6ced9837e63d2c3a06eb46d1150f0058a13178d1", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.2.1.tgz", "integrity": "sha512-hJT3ozz517/EGIDSJ1+z1Up0w3ubNLIBChKcTOONs1ZM4W3mm+gfUfslyuRmb2CwaCFXCN8j0NG406NH1JLX2A==", "signatures": [{"sig": "MEUCIQCf3I6RoSEK7gBPt7r7bnGzUJLDt6WmbA/kx99LdaFS8gIgQLMlsI9f4I1R1pqHer/H+aW4CaYAHw1NVGP37yV32K4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.2": {"name": "domutils", "version": "1.2.2", "dependencies": {"domelementtype": "1"}, "devDependencies": {"domhandler": "2", "htmlparser2": "2.3"}, "directories": {"test": "tests"}, "dist": {"shasum": "bd59d149cf2c034fec81a1ce063bc20fb572a2ab", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.2.2.tgz", "integrity": "sha512-/qlLe5dzDgHdCl1t0OZYSoke4c+2JzSFENEvpemP8Ey2yaY4A1cVXRlcTy8/TtgPsmuOSFznnLawMOwfim6NYQ==", "signatures": [{"sig": "MEYCIQDSoFqfYbD4axbIEpDdVxEXHrQGaHw4lGoaMy8/qXal6AIhAPEsg9y9/xSBqglOQl0Uv919zM2NeL9CEp1eSP5TOcGw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "domutils", "version": "1.3.0", "dependencies": {"domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "9ad4d59b5af6ca684c62fe6d768ef170e70df192", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.3.0.tgz", "integrity": "sha512-1UdPmldjSGewOuWE40YYFZB1Q4im4LZoCMXGYeTeLz3R9hvxrDYJPRcPHXR4yBbubQebgGNCY2hwpJxmAiUMzQ==", "signatures": [{"sig": "MEYCIQCddoAo70aHyPFl+RmtgG0NMswPl0ED74SiD8xUlwpOtAIhAIVjcvw09tIj6dFwCTqoGEEHCKEFkHpDIwDLYPeHhHY5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.0": {"name": "domutils", "version": "1.4.0", "dependencies": {"domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "330b95212944e340c6e0227e9ec3d35242d4dd05", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.4.0.tgz", "integrity": "sha512-HuWk/fRZ9QOrmRhIE0/150vnJ+Ohw+RREPtv0mFgcL1ECKcQ5pa4bp1TUbOZx8ofiaf5M6ffsCekrPeQyVBDsw==", "signatures": [{"sig": "MEYCIQCVlRGPngBqRWoXQdI/+xZbXGW5fzpB2WdHWaoBSNUurgIhAO8j+WIXgOJtHeHDAwBBWC7xezZkT5WkVCcFpH8ZDgbZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.1": {"name": "domutils", "version": "1.4.1", "dependencies": {"domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "e716621c245561dd03c1ecd5667f70e8ddeb2f59", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.4.1.tgz", "integrity": "sha512-vbe6LZrsWC8x7Venu9IAn1jfQCH817jmdFIy9fIokMODfEtM6WEj5hWWizqId/plwFOyYUKn1MZKJ85N7KBh8A==", "signatures": [{"sig": "MEUCIB9OkOuNwp21ilyU4mFTHdIjHFGhQi7D++gjgiONIPA5AiEA8Ke4YazJfsDkE17Cn7Hoxxd2EB1Un6tVtHvFAiTc5R8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.2": {"name": "domutils", "version": "1.4.2", "dependencies": {"domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "d7fcca9c8d52cc80e3f1e9169c90e52478b6c31e", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.4.2.tgz", "integrity": "sha512-P9BJKmjdIaNMkq9m0wvMu7nmGLjTtbxzWDsrL1lNPXiWfCCh9NxEPsKLnxl9wdmN6Z+YSu37ZlSDeE23XmfT1g==", "signatures": [{"sig": "MEYCIQCyqnFfKi/HSzIdmtbg6GrOY14z6/9yh4EVmj11I6XvcQIhAOB/lGljouM7jCW9EnJa+B194mPSnki2pSafg4pToIXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.3": {"name": "domutils", "version": "1.4.3", "dependencies": {"domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "0865513796c6b306031850e175516baf80b72a6f", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.4.3.tgz", "integrity": "sha512-ZkVgS/PpxjyJMb+S2iVHHEZjVnOUtjGp0/zstqKGTE9lrZtNHlNQmLwP/lhLMEApYbzc08BKMx9IFpKhaSbW1w==", "signatures": [{"sig": "MEUCIB6Gly60YyYPgARcOIffWrcR7eQwZEtkKTajnJL8aq5eAiEA714Xbm79VBNOyaSJLQO+heXCwnPavUPW05WUnqZ+PtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.0": {"name": "domutils", "version": "1.5.0", "dependencies": {"domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "bfa4ceb8b7ab6f9423fe59154e04da6cc3ff3949", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.5.0.tgz", "integrity": "sha512-389qXukJlNZvjy3XHPpMc295cZsUus5XklwdkeMqJWlro86MbHg7WH8FMoIEC4VeAlKjFTdGeDgRr+mjJ8bZFg==", "signatures": [{"sig": "MEUCIQCq8Xr63qEBZqLn/9X1FlXPVZfCqsw93HkJCCA95pqe6wIgKL3v4MRx97vX3+KN4629mc1id/B21rpJj2bE1LMi06g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.1": {"name": "domutils", "version": "1.5.1", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}, "devDependencies": {"mocha": "~1.15.1", "jshint": "~2.3.0", "domhandler": "2", "htmlparser2": "~3.3.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "dcd8488a26f563d61079e48c9f7b7e32373682cf", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz", "integrity": "sha512-gSu5Oi/I+3wDENBsOWBiRK1eoGxcywYSqg3rR960/+EfY0CF4EX1VPkgHOZ3WiS/Jg2DtliF6BhWcHlfpYUcGw==", "signatures": [{"sig": "MEYCIQDdHlAyilni/SWdSovItT85Hp8ksk/rfaYFYZuR9LohtAIhAJjBaSYXDeOTy/W7IO3hWzUBdDbq40L+eYxvIPEQYlqb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.6.0": {"name": "domutils", "version": "1.6.0", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}, "devDependencies": {"mocha": "~3.2.0", "jshint": "~2.9.4", "domhandler": "2", "htmlparser2": "~3.9.2"}, "directories": {"test": "tests"}, "dist": {"shasum": "853de07f013287f976b7fe0461740222ea14ecbb", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.6.0.tgz", "integrity": "sha512-dWs4oDYf9GkuNJmAnBXPlozX1CiRPaauXQvWpa+UcRHMQGt+nvsSbGcn4k3tp7omsrf1tUhPiJ6d4DPbn6kqkA==", "signatures": [{"sig": "MEUCIQCmFMZjKeiTnYRKGB61Oncum6WFezW9L//5MN4GeOEmJwIgEc9KSLMYgowBMu4fIRtsI9r+agomyZ0sGZUtG0aYb88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.6.2": {"name": "domutils", "version": "1.6.2", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}, "devDependencies": {"mocha": "~3.2.0", "jshint": "~2.9.4", "domhandler": "2", "htmlparser2": "~3.9.2"}, "directories": {"test": "tests"}, "dist": {"shasum": "1958cc0b4c9426e9ed367fb1c8e854891b0fa3ff", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.6.2.tgz", "integrity": "sha512-XjD7wlrG7XCt5cQIPKYVD+m7XNbUvT27P0dQygjjRrw1z9gma/aMI2PfSh2KGkppCGGDgTSVe4GXQducWgeufA==", "signatures": [{"sig": "MEQCIDQ+mcNLMuEU8GyfZVdhAxs4fr/nZb9DAdCS2K/nbKDyAiA1oA60B1d64zXoGbb/IpDoMyT/3VL5GKlbpDFl9fpixw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.7.0": {"name": "domutils", "version": "1.7.0", "dependencies": {"dom-serializer": "0", "domelementtype": "1"}, "devDependencies": {"mocha": "~3.2.0", "jshint": "~2.9.4", "domhandler": "2", "htmlparser2": "~3.9.2"}, "directories": {"test": "tests"}, "dist": {"shasum": "56ea341e834e06e6748af7a1cb25da67ea9f8c2a", "tarball": "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz", "fileCount": 16, "integrity": "sha512-Lgd2XcJ/NjEw+7tFvfKxOzCYKZsdct5lczQ2ZaQY8Djz7pfAD3Gbp8ySJWtreII/vDlMVmxwa6pHmdxIYgttDg==", "signatures": [{"sig": "MEUCIQDG9DN+/wDzoaG6ttFz2hvKBa4GsQn5evjyb7oP0sJbGgIgNf7tt91MbYUHjIIX3i1SEdEGlOGAZfQ5IrMk15NLtrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20546}}, "2.0.0": {"name": "domutils", "version": "2.0.0", "dependencies": {"domhandler": "^3.0.0", "dom-serializer": "^0.2.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^24.8.0", "eslint": "^6.1.0", "ts-jest": "^24.0.2", "prettier": "^1.18.2", "typescript": "^3.5.3", "@types/jest": "^24.0.16", "@types/node": "^12.6.8", "htmlparser2": "~3.10.0", "eslint-config-prettier": "^6.0.0", "@typescript-eslint/parser": "^1.13.0", "@typescript-eslint/eslint-plugin": "^1.13.0"}, "dist": {"shasum": "15b8278e37bfa8468d157478c58c367718133c08", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.0.0.tgz", "fileCount": 27, "integrity": "sha512-n5SelJ1axbO636c2yUtOGia/IcJtVtlhQbFiVDBZHKV5ReJO1ViX7sFEemtuyoAnBxk5meNSYgA8V4s0271efg==", "signatures": [{"sig": "MEQCICKtpaUlD4ESGt1BEvqn+omB+P2wBVe2vAOMvny45b1fAiA9es0T1piSzz0Ldjkl+A3DpMbEQOXu1FvQAm10j2812Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQ5vbCRA9TVsSAnZWagAAft0P/3J/nYjihLMUEgSory7I\nLwofrzygKp8GAWEk1DrTXihHWaFS2YTOW8QBgjKyHrE1Lw416kDYrJHuhMpz\nCtZ35Sy3J2ozQBuLh29S+VK4v9CaaxCADYtn+Ph3qxQQemsG/ylI+iBBeCee\nbTPus0FFVuuHO5b9lW8y4htTCc5vxAfV5zi8CO0Y06gsUKDC8CeTjCSqQdsX\nxBVk4tsf8gX1MPbQcshrDwjdz6ap5cGL0DQauIrYDYl2HdYS8ldcjwkSeJHX\nqiYrfI/YjW2UfOUD784+7NGUOLFgjGgnG3OmtHwJ+cFs5PfxJqa2UMl2OvmS\ndpbyQ3J2KPJki5gFkE6BN8b4oYeALyOrcQ02S0ll4KtTol1YmohOPOdpWYsc\nPp9OUQtMNlJ3PSyM671unLe7qVvqhLb7wX/J0Ee3YQa3bgaL2PsAzjIerpoQ\ngLcMErurCq7g3lO59PaG1bbXKsGm4A1nej/vY/QCgQk3yHbeSQO1FTiLLAKI\nh/E9lWJ+aA+TGMLRrEAhXSV0/j9p/6pDf5bAiJtrpp3cZFjreriXGpvAvyHz\nDLzr2IbolVLtbyYhi0YydX7sWGFakzWn8qJjG2p6xMgyFgSatVjlsQAbhddL\nzVFclhRx3QmEmXQj7MOgAIsVowhlwsawJ5ZfifJC6NofOQo+U4gTpkKyPntE\nhk5S\r\n=09J/\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "domutils", "version": "2.1.0", "dependencies": {"domhandler": "^3.0.0", "dom-serializer": "^0.2.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^25.3.1", "typedoc": "^0.17.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^3.5.3", "@types/jest": "^25.1.4", "@types/node": "^13.9.1", "htmlparser2": "~4.1.0", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^2.2.16", "@typescript-eslint/parser": "^2.31.0", "@typescript-eslint/eslint-plugin": "^2.31.0"}, "dist": {"shasum": "7ade3201af43703fde154952e3a868eb4b635f16", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.1.0.tgz", "fileCount": 27, "integrity": "sha512-CD9M0Dm1iaHfQ1R/TI+z3/JWp/pgub0j4jIQKH89ARR4ATAV2nbaOQS5XxU9maJP5jHaPdDDQSEHuE2UmpUTKg==", "signatures": [{"sig": "MEUCIGTd4fngD28Lhb6Tq9Eh8EUQAOBNjfksXVi8Ky/D7rWUAiEA7gnP3+iZ4+Kps6S0KPX12hLIPr8idYJP6qnDQDMNky8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetwQ0CRA9TVsSAnZWagAAqeYQAJuBI8X55JdQS75jw4N4\n/x0A2USYahr6unR7bf/71NtkpVXtaRMhxXWxK6vAQxaod6QaQibYLAga21yg\nrbRohznCp23LQ6c4G2nxflE8yTN9pg1pTjdVfUSIaa5qFxJeDSCaiU9lqIjt\na0hrKVVtoRma4JwQA9Tjic5+ySkFktIABQUDUJgti0iDOtVKBWeduT6H0ew+\nRnpUGONdh+T3/2u6skhTWJbJwnpuMWiCvsDs8vQ9aPNOHL6skmtQgaVvmylS\njmyTleMGdFvvk0n0ACohqb9QTJ3gaiN1tuyuJ37Ps5LwPflg9GHcBvESlTU/\n/HRIO74kCqS2k5y2Qo+xWjMSM8iccImtYaCDbly6XrOnEv0inI2sjUVaMvJ9\nvxf9UDnfGDp2SrBGEW2dZ5PlwCw/KPWMPl1Akc4YfqGEXz/bHMaKsehAHsvB\nK7euJh04E7DbjkTfwJ9Jk0IRvyDYBObvOC6/urpbbOvmKn1KeQWV/ezw9a1n\nzpAComX4zyu7MQVnHyT1LVx0W3Y14vB0Jo4qD/os68IX5kFtAGTu7VY/bL+e\nYtMbnleamuRAoAq0FDSkbRh9dXZ/5OeK6ZRb2xUyKnOcO9SBDfer+r+LWiKd\nzQT3g4DV4KuyEiFRtiyzTkzg2EsAK+4d1Ylm8LvrbKCJZ3TuCLr1yc994+DJ\nU0P1\r\n=Bl7y\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "domutils", "version": "2.2.0", "dependencies": {"domhandler": "^3.0.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.17.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^3.5.3", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~4.1.0", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^2.2.16", "@typescript-eslint/parser": "^2.31.0", "@typescript-eslint/eslint-plugin": "^2.31.0"}, "dist": {"shasum": "f3ce1610af5c30280bde1b71f84b018b958f32cf", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.2.0.tgz", "fileCount": 27, "integrity": "sha512-0haAxVr1PR0SqYwCH7mxMpHZUwjih9oPPedqpR/KufsnxPyZ9dyVw1R5093qnJF3WXSbjBkdzRWLw/knJV/fAg==", "signatures": [{"sig": "MEUCIH9N1uVgyUed4xTNS+H2+hA3SXpI1g3rv0EgqpthNh8yAiEA8xGGHOUKnIyitA7f0L3kV0lg4xwX8BIs+E+n68GlpOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOrIyCRA9TVsSAnZWagAAj68P/RJtceTnCiGZil59QM/G\nUJ9jr1yMCn8YSg1Geh9SevuTnzJkfQ3ptmUzegjNt602/nAobZHVw4rQNUG/\noJqfiv7FSQtF69cchsdKi4q1sHkobJs3ZJImgSkx5pQeaKh5s3rYqHYHV2s5\n9PaTxj5oPou1X0xQGxUTcc7nzHNQqa8fqGnkOCL/m/R/aGreEkgB4VNUNT+r\nl2mm+FhmJ86EhtA6Bj5ZgNqlLGA1/IZAVI/1RbE9r4r9WAHwkMo7BCoCnIqC\nUviDi5O6aLuvX1LNkfw3by60O8zbRfIX4QUoCkCEyXX4vM/A3HwhOAbWGT+P\ndDVOUc+xQtddWJtcFQHV4rU/Zy3ZzGn3XMisLVM/13g5h4GYAMliPTfbJdew\n2RajQugeFceeo3ciwo1/O+GrjHSWsbSTKiQoUmt80ZAJ2s/jXkgCgKFtg/aB\nvB1AeaYjCMyn8uiMhHZ1bhiNnITd7HbIrbaqKoxfZY3pcwYY7eUKyMJCnZ2R\n5Gd8ND7jAK1Z51U2rJzgmry5cSjaZyb7w+GnU7fgHNucZ8wrf/9bYEO1+YpR\n4x1OvfROI7YADyNoPZDOEqWa84NN/qrnbn+xEpQ0ink4USG9bEfpeZ5MirC3\njgOEddSD13wrKywmsOSZ9Krn7uUJD1UMdX8SDSYNk3/ZftoJcAO/+ac18P95\nw6Cl\r\n=GTpO\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "domutils", "version": "2.3.0", "dependencies": {"domhandler": "^3.0.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.19.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~4.1.0", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^2.2.16", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "6469c63a3da2de0c3016f3a59e6a969e10705bce", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.3.0.tgz", "fileCount": 27, "integrity": "sha512-xWC75PM3QF6MjE5e58OzwTX0B/rPQnlqH0YyXB/c056RtVJA+eu60da2I/bdnEHzEYC00g8QaZUlAbqOZVbOsw==", "signatures": [{"sig": "MEQCH3kEGyCJINBlUBeY//cgFkQy322QwGTvyUvlffYX7lYCIQCNrEJzMGC3U9qSoNuardJakz26wKJoWqL3w2nps9IweA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXm5qCRA9TVsSAnZWagAAG6wQAIomhIyfLxJa70rGt+g3\noPIwXhzcryRtFTBz/hKFroeqfJoCjuWRdakd8B3VPYmPCIy/qsOUwdjVOsIj\n5rGp47Fu+rsprWrU0zcQ1FZPnKUoUJXlNSJsw3lO9J7hq0xp6BLX/pzY3rY9\nuyNfCdWP+gEBQwYeixtW2vaIxUEcJjOPpYPjv3CkTmARQNnzveKbs/ZAH0kz\njVDk+fqsB1aKhYqMCY7w9hUHqTOrWJJrLOWqsAT+SF5hBztijlk1kHoxG78z\nG7QmSZYr2UWdP/f3i2x6eRrWbq3yawfy5ajI8J/bzQO0dgKFM+hYmZp1qwHs\nV0Zw14qwf8q6Hgf0/7tHoVoaNGOnEvjUEREmV0wRLQ0/PBibPQ04tTo8EEVy\nAbNl+/I25+4L0cvhMPd8oEPe88AauhxJP8Hw9TWMb+R5ZTZ/ONwcQbr6lLUS\nUBRxZYeAjA5N8m0WsuUwGeZDECfu+UxvJ7nF+nPqAMOPiApT3zplXOit74by\nkv0OMKI3FbONEEKklwzWn5jem/HKEYfByVZ0o87TwF8be2f4mY4b8pee0PkY\nyTLgR9AXMrkrSQuHU5U81z4y/CDY5tDHfez+Zzs+w4cNB1C05hBOqhPAlpQ7\n+e7KviqHTIkFNQASzG68M65tHgvQ+eDT/9rreh8bKxwrrcg93t5IxTkO+m+I\n3NiW\r\n=M5QZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "domutils", "version": "2.4.0", "dependencies": {"domhandler": "^3.1.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.19.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~4.1.0", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^3.0.2", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "60b85585a0e96113dff3d04d735196bb2bba49e2", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.4.0.tgz", "fileCount": 27, "integrity": "sha512-rP+tl0A3YekdmSpYOEtm4C/aSzwtEOU5XrkdoGwb+Hn/941f5S5kZwHr37AEIcBbPug3uxD8z9PKQZd1R/R/tg==", "signatures": [{"sig": "MEUCIQDXDvyeDgi/+N17vaZ06Bd/lESS0EhvQowFNqVE4feVyQIgGppHYsXMnc6YkP6XT2X3OGqM3o4RMl+RF7awD/psvCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa7QUCRA9TVsSAnZWagAABxwQAJbC2riIyFIVpBeGIJnn\ndXx8PM3N5oGaKkniq3y/i+KjyaFzoz2rFtg5uSeh8ABHaWEg+2MrfYdEBZxt\n9V+/2TGFYywTTnxF8D30WZ0NGVWeldTvaivI/neC56CtlXRs5pCxeBWewHEP\nqImH2knXqr8NYGoaDbMQ8PuLBd3u4hmLNlBlr+XD5NzdfPu1zRy2QII6G64+\nqOqup/itR9sUgHlZB4a3EqjYtl3Hd06+l0V9c/QdPLWtjLqeDS5zWF4xI7cw\nJPzI0LYuC6Xr30Xq0EhoNS/TugeHO6mxlti+zuu4qsIP0pucBY1X+42+2tYM\nBH2strsfZC9gh2kqPUIIEBfwi9lnSJyof++6RQOrDxlxi8zWiRQsvD9A3BmH\nc7H2UYLznGqEsoyimKPcvizIzwlCNT0kn/HIRmLhMpd8cjxTn2FekJ+/WRl+\nuExq0zRbPOlKM/9eJhFBpoEQNNNpWHGQ2tKWWq8GvVGxjTVAOR1i67kR1Zz+\nlANDaAeOZAoI//dVvKGSxZY6FECdSqJVHV6gx+80cdbSy0sEFYoGt6X4OVxz\nf1VwUNOG7Iw8liM06nIqwN68hxgM3mmvr3wK76DUh0x/HqVVmIU6r67JxYTS\nBElL/0r5JkCeTiytyd/gq/W3CNvq8aQya0rME7tN6dgQHCEpoyn5n3eLKBA4\ndlLN\r\n=Last\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/domutils?sponsor=1"}, "2.4.1": {"name": "domutils", "version": "2.4.1", "dependencies": {"domhandler": "^3.2.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.19.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~4.1.0", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^3.0.2", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "73f65c09eb17943dd752d4a6e5d07914e52dc541", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.4.1.tgz", "fileCount": 27, "integrity": "sha512-AA5r2GD1Dljhxc+k4zD2HYQaDkDPBhTqmqF55wLNlxfhFQlqaYME8Jhmo2nKNBb+CNfPXE8SAjtF6SsZ0cza/w==", "signatures": [{"sig": "MEUCIQD5YfOMVPF8XMR/Vn1dTUiKDo+CRL0S+bVMsuZYQtknaAIgYTXbYdzpshKCsNdku5lcfvmsqBhUvo4LbbvbTxetJAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcU7xCRA9TVsSAnZWagAAICMP/09rMIsp1eaTlV74ZlQw\nWgXO29peSuLf6nOf8UUiqWguQivlQeVmxVqWhQRWi88IwW+sen1F5igxgwK6\n7EMjLHK1/UQIFNdR7da8gpKXnsXrsPweBXykBaMdfMGs74DjruH1jVpseFWq\nnCEZAJEwu8H4Zm9WMgjnLXQaCQx740awlQi3MA8gOqyJ8mDGyf32wn4cayk+\nJP/F0d3E+wzUZ6ZqnXGeOQimZGAEDvisA57M4IItxLdQQs8keqlJhX9boWxD\nsIIjhPyCAtSePyfASZYRQiT7nR+tx57AHsh4unm4aFIcRYTKuhV3DGOLeMQj\nNvnsm5UnwhkzVySVQ6RwGf7mDFE7HI4VerMKuTpXBVPYtLbag1SQbCXfcSpy\nZeXOH64Nd2Ilh6jYdtk5xaaQmJWQwIQ9SlLFwRQDrFs2v5OAKYPEN4poqeT0\n5KFkEnqyrwriu+7Dk9gJnbSqdbGqh3xL8Xnb3o/zxBzC58ujL0IsvsjNoUx6\nDz9y8HCyA6ifyugCAZ3w265RcnHeuiWo04S5J2onwBUigUrSlr0baP43jMfB\nvVWhayrWNqPmx8M0+Ms3F9p0Sk8XQliCPwCP7uxwbM/rAQsMhTqSO/oWKThP\n5bEpHVC41ogEAZl9QaGtDhTNW2PgNrn8lMFJ6WC4OOf8refIufraw4P5y+BV\n+doV\r\n=YQOr\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/domutils?sponsor=1"}, "2.4.2": {"name": "domutils", "version": "2.4.2", "dependencies": {"domhandler": "^3.3.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.19.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~4.1.0", "eslint-plugin-jsdoc": "^30.6.3", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^3.0.2", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "7ee5be261944e1ad487d9aa0616720010123922b", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.4.2.tgz", "fileCount": 27, "integrity": "sha512-NKbgaM8ZJOecTZsIzW5gSuplsX2IWW2mIK7xVr8hTQF2v1CJWTmLZ1HOCh5sH+IzVPAGE5IucooOkvwBRAdowA==", "signatures": [{"sig": "MEQCIAKtYuspzb/SBgnrE3Ebjz1AWDCleHb/cbs0UzwRxEYrAiAwvLkMiJrFQM1UEI2uxe51lg+9VdTs0d5QIBrVF7WhWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeO8fCRA9TVsSAnZWagAAj4MP+wR9jFtKbB9knmZ/Efui\niXF/+YgEhVTI9zCaXgkE/fgaiw2JO4753aRWMPFdxJgbu4RHwYRMZn8M4cIo\nORBVcHZlC/pe13OiJEAp04GeIsMrJqxaM3oQ9gKPBzhDJgBocIpvUWdMAtaM\nPG/unz8sZIQ0U1Bc64iKQCtyvNRZtK7iGM/kPmSY447Ea7v4HG+wtoFdJ2WS\nhNbGBplN8UvlXrqqRWxicIBzSRwyw3OOyXnGxfndzMZ9smwfHa0luG3PaYQm\nLyM0BW3waE5Up0dnxQFjfL6To0HdLQiQf18dwfAcM9qqKuERH7U8nZ5D6aEz\nlxOXISCnXlCEsdoz1e0Uv5RytsSO+foTb5qIaebMTT6sjARghBF87MzvrQze\nWdeCgQBWAthd3sONoaNSKKTM0ATjjg2qYRA96igkCONTy7Pb0eOcAWqZIDAX\n7LbhV6i0NRIHKAOV0QU2uKT9oyV3w289yLybIYWesgQd8wUbRqJbqHB/boxc\nYuKoGQZ8o9x7sOYWIW05wCEIs+QWubaVV5oP1e953VYjMvzNZgIOt0uQ/bTB\n7eDWRF9GADVYzg+9qLF8jZKSDyt2g9vH4TdmATN8TQGPQ1QTE5B4XAjvKa+S\n/zElwY16RRlzRsfNcXXeBYOWA1FghnkFo49z7J8noD6L8p/sawqA8FXjPZyF\nqG7u\r\n=QGZm\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/domutils?sponsor=1"}, "2.4.3": {"name": "domutils", "version": "2.4.3", "dependencies": {"domhandler": "^4.0.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.19.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~5.0.0", "eslint-plugin-jsdoc": "^30.6.3", "eslint-config-prettier": "^6.0.0", "typedoc-plugin-markdown": "^3.0.2", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "b8ca888695db9baf65b58462c0eff46d2d5cd85d", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.4.3.tgz", "fileCount": 27, "integrity": "sha512-MDMfEjgtzHvRX7i21XQfkk/vfZbLOe0VJk8dDETkTTo3BTeH3NXz3Xvs94UQ+GzTw/GjRYKsfVKIIOheYX63fw==", "signatures": [{"sig": "MEYCIQCvxTOq5taJwWj3/soje1uFChNQjd/slJv8qilqpchzogIhAPm51Frl3cUCD29BpBgFZONnfPhud+x08gLNlpJ1ATzD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfx/R8CRA9TVsSAnZWagAAvyoP/22zhvfNVl667vvZ0kpn\nhzjGbCHz/bmGsb60EzGyg9dNY3ajjSdZQXRho1A8r8Ur0B+JVnVeMk0Sg5iM\ng1sgqEz0E1Ja6zguACuMrNpNr5+p0ZC8gz3Tm2BvBt/x1QVuSiFusxZ55Hee\n4gjG7aCJRAX4G+u09ZmA/PiKW7fHitydBztIdeK9A+G7C4p8HY2wBrD5dMWF\nRfaE4qiOXcXb1v1DtwU/eS54uCKDRndyyqAgH1tqezc06DS6U4ZrI3A4ugUV\nmvQyzT4SUVHnFilaj8uPlgxbDTjaoN6NxFRRZ/qzdoP7cMgeltLs7MpoYWYo\nwL8U8jvFwqxXYpXOl2A28vhe1NqiZDtAyPqQKvlyO6lnUaBg5vRhdLtIT/Uj\nvV6YjSmWC5OrziSR1GZtjs4i1SYMQNPCVEY6zaoX2WimbqVJehpaf/Py+x6n\ni7orYNkF2uYdmwO6XN1Bbsc935l7+sJqvDwkYP1chrvLkJ0L1VXa/OHvNGzQ\neXYf/EeaVD8+z8GlCjjl+RiQxR7XwPR/H2uKjJE+THchkGKKRhJuv5YMrh7g\nG8GLowCsRngD9kAa+5Db/u5ifwjS71uaVJNlUQYFo71HW6y3QzEMzAkKEkWw\nIKsHcLGUQ5029XZbZiN37eWzbCPMebp81jmEz1HuGRjqIYTtktUBdCRCee6h\n1Vnh\r\n=cCv3\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/domutils?sponsor=1"}, "2.4.4": {"name": "domutils", "version": "2.4.4", "dependencies": {"domhandler": "^4.0.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.19.1", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~5.0.0", "eslint-plugin-jsdoc": "^30.6.3", "eslint-config-prettier": "^7.0.0", "typedoc-plugin-markdown": "^3.0.2", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "282739c4b150d022d34699797369aad8d19bbbd3", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.4.4.tgz", "fileCount": 27, "integrity": "sha512-jBC0vOsECI4OMdD0GC9mGn7NXPLb+Qt6KW1YDQzeQYRUFKmNG8lh7mO5HiELfr+lLQE7loDVI4QcAxV80HS+RA==", "signatures": [{"sig": "MEQCIGK/LYuNcZ6bx9w5L7924vbx72kSeNOyvPnIvsRgBNFUAiB+eFP9o0zzJiLr81L8XIjmn3NBUcUeBENXHOCXcr3xhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzuN3CRA9TVsSAnZWagAAzLMP/1xD6H0i81GrRapLLmEc\n1QzGf3m1vEmM5oy+1LAKQLZCfgvZnWd3l3Aw+3oSwX+YAHyR0M9Uvx9UIOZU\nn9WgB+43bkuyIe2FAAMRAelM5uJi3AGqiANergrHhWhRiNQIsdcH4CVWKZLn\ntza/CuYzZ6e1XzFB5aJK0yP4BlJvL7ASUwQ05ML+9IbDTlVIcuXRkV4CGlGG\ni5Ce35M34a3Vo4us+Xv1PS7oRvSV9tUS8uY9fMgVwrb4qqL/HiHr6QTcNXWy\ngi3E+1hUqm+kvIIbMnCS73XCI3Vl0F/wpYuqJo2bzqfjUCgKVJu8yqNf6VPT\ncWg9/7My2IMT+pkaMkuto8JxbOARibG8VPBAdZbINVmFZlFFEVGTQVOgowQq\n+pEsHFgJkyUj/OVl1Ea4TInbT/VhnOPXcAqKTpbXcV0PhSRiXABxpJDuYo6G\n81BgSU2EKE2SS0ICjRViTgZ6wncplC3LvP/0kP0L80ysoMgczx/sYiZOVGXw\n+d0BYYiI5fiusUUOCZaiUG8j6PAGLujjGXvs4FbJPdk7ER/PjCbDzWBhRDqR\nnzdkM46viAk6rKDm1VaQzb35JFrEhWaFxSfBApmspiKtQUgUVHSmSee/aS7h\noL0d0KbFCzbHyeOrkeoXdus8dSoRGvm/hwmP66lG+St3lx2NLlZ655UxSegB\nU4i7\r\n=/JQQ\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://github.com/fb55/domutils?sponsor=1"}, "2.5.0": {"name": "domutils", "version": "2.5.0", "dependencies": {"domhandler": "^4.0.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.0.1"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.20.5", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~6.0.0", "eslint-plugin-jsdoc": "^32.0.2", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "42f49cffdabb92ad243278b331fd761c1c2d3039", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.5.0.tgz", "fileCount": 27, "integrity": "sha512-<PERSON><PERSON>rzNMOFk2fPwChGh3D2D9OEHAfG19HgmRR2l+WLSsIstNsAYBzePH412bL0y5T44ejABIVfTHQ8nqi/tBCg==", "signatures": [{"sig": "MEUCIQDUJ85tuRah6mxEWwezlq623wtBfrM5hF11Rpp912ygJgIgXGY55WW58LRp58l3WcCtrg3/4SqJnOliOEeSv/xnjPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSVqcCRA9TVsSAnZWagAA4RUP/R8Q+AHROkb03obW3FEz\nQKghRwILTha7lgQFG6yeGh5A0MAK18bSJXOgt2efrCJIhqqZjDVnf4LWCEh9\nG6FfLEl8b72fhnPPN77jdFqfu40wD9z0jxCO/BXhCRgkTkxfy5Wyxb9V6v98\nBK829xzzb94XzmZEmTWzJ953Nqcay/WbqsGMeJQUL/9c5bdbh7knWrWv7w2R\n70IW4jMfeSFLhIsIYU+t1FZLXp5KhwyVW9jnDEWtLJjLXGZCxe1fyGACyMPe\nhoUS7i6JhdnPU9Eg3FMOTg9BS8yrmUTCczPCIDr+keA3olzQwQgpd2fhEdoV\nRQxs30RpCHZwx25bRY2L5xybCSK8Rf3o5985CDA+bfFg3SHG70yAFIaGRad1\n8K6e9Sazd1TugcY9jcuoc/d/LkcpE9XhIYS7AW2p1SY6tCe24ESkfY6H0rvG\nBFj8O0+rzKh9YfHDum5w6aSJMwg8eCltO0Ste4f7xbNMIMHtC+lcpHu8+Uun\noWzQEi3WwXmX/9+SkcxAM2w4OQwsM6dafWv6a8mGOdBBeqFe9D1ZnvESRnmV\nQ6GhqyFKvsqHYUP8nTCiZG1WODJfW4Xck54WyvWiq2V/Xtl3wfjKwu+rTs6O\nY9K7hZ4Bb1taRM+pC5cO5nuwvgxDgzS+0iGs/LQO7kC61GZ/onU0MLImwR+5\nD94d\r\n=dcy7\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "2.5.1": {"name": "domutils", "version": "2.5.1", "dependencies": {"domhandler": "^4.1.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.2.0"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.20.5", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~6.0.0", "eslint-plugin-jsdoc": "^32.0.2", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "9b8e84b5d9f788499ae77506ea832e9b4f9aa1c0", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.5.1.tgz", "fileCount": 27, "integrity": "sha512-hO1XwHMGAthA/1KL7c83oip/6UWo3FlUNIuWiWKltoiQ5oCOiqths8KknvY2jpOohUoUgnwa/+Rm7UpwpSbY/Q==", "signatures": [{"sig": "MEYCIQD0LAMMFEoAv7Ydne+VEBo3E58qqI+bSE7wsMYu7nM3LgIhAMd+wE+vFBoI+SMEoyHJLFC5qMsVwiJ56mYFdoedeYky", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZgfuCRA9TVsSAnZWagAAUzEP/2KwbO5icAcBr9NTrIot\nIlrPv0OVpCIl7ofcWJXb/MvUoKWsCR0Se1OPgghfrMrkgFy37tx9c6/ORZan\n3NZUMOYibHHQMb2zrsInmLoeZgTiXRijzeAKh4ZamoOXEYutehrYgGmilPq4\nHtLuOQ1ZG1mBbalbIzgD31GQ1Q+psCBT4fLGi8BXVc9OpWfv59OPcvlU02WG\nXK/+KcR854BF/yIdDLvSjKGxHH2JogU2ZbGQy2V+Z03p1r6p604yOrBoa5MQ\nKGxQJsnF8Tvs0c4pTitr6Qnu8yAYo2Q0c4nvsVEq3RD6XCfQ1l9vtXozuswo\n7uLdWCc+eRAm0yLsKT6RZeU///xjKEr4JjxmfClqwSnM6R03FSuY3v/4f5tv\nUuLD+EmihoxrF4C0CZA/2w92hqlXqMf5rv2ryACpAE48lBRnxF1zjCNf2N4T\nDj0VYGCcCX1iB2ZrUX+OZA76qIbr7MlcB0joOJ1G+lwu/SUlYwFIjJAgf57x\n+0lvbyvdkKYULJr6mpSlFP2u0uCGFC3V/SSiXGvn6dBoHQ3zoWqvAM0Purlu\nOStc4zP+WE7PBwkVdSys7foncHmXE3vXnXWTdH6al+Eq0JG/hSVWaXcCia+E\nEmcymSis8JBmxUGs4XseJnG8wDJHlayugTHxbOCdmSls+J7GWlWAFTDLhhQV\nu1dS\r\n=pbP4\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "2.5.2": {"name": "domutils", "version": "2.5.2", "dependencies": {"domhandler": "^4.1.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.2.0"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.20.5", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~6.0.0", "eslint-plugin-jsdoc": "^32.0.2", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "37ef8ba087dff1a17175e7092e8a042e4b050e6c", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.5.2.tgz", "fileCount": 27, "integrity": "sha512-MHTthCb1zj8f1GVfRpeZUbohQf/HdBos0oX5gZcQFepOZPLLRyj6Wn7XS7EMnY7CVpwv8863u2vyE83Hfu28HQ==", "signatures": [{"sig": "MEQCIGfXzyoVHitK1fOeUQBxBiKuu7DeIe20wkh8ZU6tefbmAiAq5+T0a1KHnbFWAzhQ+SGVTsPCFP3Cf29cYAhzhGZC3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgb2YxCRA9TVsSAnZWagAAfAwQAJqNy2gHwJQqx0B4oKtc\n7uDz+Y2UXWC9mj4mKvkpDErHCugC1dQowZ1mO1whXL/ZAE0WVNU5ltDG5pvd\ne6MLZu9kfbVv9lLQmj+bXeB0l3whw9ANJG0psTY765Jdd3EVeWg7CLorEBcD\nosxtQMRNkFMR2M5JpjLcG6R20+0PA6lGJAxAilmZLaIuDTHJ01OJ8ae/8oq4\n4Eq4U0E5I7k0r17u3rwh3ZHlQhYxdHpWee71aXUuBZaVZMonE5XbUtQAF3EL\njzqPdO1c9dcoYDsIQOFg8aLH9fbk44BcW18+/hrDjocMi5KGJAvzqgu8hf1P\nWFTnFekMVN6gFfWje8GoRtUETZGnONGgFONJ83Y3WQOvXSJLhmgG3IpneD7G\ntbFfhSnpx7kWDj8z5cxK4YBXY1zufSHhAujATAeq1WdpszxaUcZNgjby+DZ/\n91vBWCRwbCusNsph2chddy5jNzQj+Nvtn2DpMgqbIZD6+MsbeW7SduqTUnUo\nyyVCaNoBQuNd1bBdkdt9Mt0++TYHXSEGm55qoNouaylR/zip3LRGAMWEHyt+\nb7Jx8npoi2snvDdxCLV7H/Z9yaPkQxu8iMWFzhOn7+NZMcwMHyb/ZQozsr/Z\nIKo2SXvuxX/VZubtQAQqFjNdl5vdqnzsDtxxYSJUDjGBmGqBMRyXxwoqOUlu\nbvEL\r\n=ZYx1\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "2.6.0": {"name": "domutils", "version": "2.6.0", "dependencies": {"domhandler": "^4.2.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.2.0"}, "devDependencies": {"jest": "^26.0.1", "eslint": "^7.0.0", "ts-jest": "^26.0.0", "typedoc": "^0.20.5", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^14.0.5", "htmlparser2": "~6.1.0", "eslint-plugin-jsdoc": "^32.0.2", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "2e15c04185d43fb16ae7057cb76433c6edb938b7", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.6.0.tgz", "fileCount": 24, "integrity": "sha512-y0BezHuy4MDYxh6OvolXYsH+1EMGmFbwv5FKW7ovwMG6zTPWqNPq3WF9ayZssFq+UlKdffGLbOEaghNdaOm1WA==", "signatures": [{"sig": "MEUCIAGaUjFndB8EMHxBwjmRnemysTd36HW34qrWUgCSz0pcAiEA88Lvp1TfsSKWEUy3gf+Yi6n2/vdK7V7i03aeVszlnA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeVZyCRA9TVsSAnZWagAAnVgP/iDeefIVTABP14ILgwzd\nRR9InUaerXfw8NC5XhZyxZEnC+gWFRKXR7LpFn/ciHj9bIl+6H5DMaruLKhg\nQEUHV30t2MV2Qdi3kuJefhyvVEDc/rl2KxwHbh/G5hjIn5OIuNInvVJI5g7b\nOKmbhYPUleBRyTYbdUMiTJ1RiHJvsnUQuvkX+ElI6LLfhL4/zLb/jj+1/eI7\nywMKu9I1wR9/fSxJya+VMyXdyTHMqFN1ZR6MycTbF2VZlAw6VDDPc8vfdzcn\ns8aI0LlbhqMb0ZxuHyKPIVYxbqxErPQOlMRx1cthLlSkDsD75nkOCTvYiOU6\nr8/OF6kMlc+On/mPBWM8VcmET6O6cXolAbrfMr51CUcosM0URvS0L0MzzvmP\nL3biBrrYw2H0DIWESRi9gfl1YIbOxlf7D29GerKPik+SuAFDTDXGYjuJOMEq\nIM/5BdJTX3ndghpquo9+M5otvifo8462SnCF1w6qGiX8CxFBTQNkXEuRBfGH\nPR1NgF4nHP6j3SVBgH20AGvQaTt7WzDdxR/YVthjxoIyq2AS+feNsgaBdvHC\n2XHPnv5aVxbfFCQJedRilzmS1QTztDH/diIcBDY/H37/ZAil78Mm5G3KYpT9\n8mM9ROJfp2oes6MVGORe/hVnwH/xojV+WMixFxJmfhNcE3A2eCMMo8fAacwj\n8B6S\r\n=QaZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "2.7.0": {"name": "domutils", "version": "2.7.0", "dependencies": {"domhandler": "^4.2.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.2.0"}, "devDependencies": {"jest": "^27.0.1", "eslint": "^7.0.0", "ts-jest": "^27.0.1", "typedoc": "^0.20.5", "prettier": "^2.0.5", "coveralls": "^3.0.5", "typescript": "^4.0.2", "@types/jest": "^26.0.0", "@types/node": "^15.0.1", "htmlparser2": "~6.1.0", "eslint-plugin-jsdoc": "^35.1.0", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "8ebaf0c41ebafcf55b0b72ec31c56323712c5442", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.7.0.tgz", "fileCount": 24, "integrity": "sha512-8eaHa17IwJUPAiB+SoTYBo5mCdeMgdcAoXJ59m6DT1vw+5iLS3gNoqYaRowaBKtGVrOF1Jz4yDTgYKLK2kvfJg==", "signatures": [{"sig": "MEYCIQCw9s4hwYcSsZSsM8X8x/nWeQT6bJH0C1THglWXzWBUvgIhAPzc0Tk3A81fFr5YBm6K70oxnu7Z+b+rQzbjPCIh1zHR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvPCVCRA9TVsSAnZWagAAv90QAI69WA5RXV4Mt3bvmI5r\neVxNWAyu+LYsPGRszK/C4mBsEPKIpyXTZrp+16/+e4Tm1z2+IJcvQ3FWTxoA\nhXTeqBBq3cDvUM7pwagjYJaPZp0ezyuF0xRu+NxOv5hJrbzsau7ILdLs1ZyA\nKeLRWg34S4LJi0I6wOu6ubEA66mdn76mS4/+Fk6riOw3m758PlGwc6hi4z/t\nwW8S2Md3mVRMKVKAjpWKY8kvjrMOJtHQf8LS9OC33q/TBAoehEMf9FIitYft\nqlvrlaI+3iR8X2Yic6CdvrXMY/if6S68LKo75XAL7Nnuk2RDqA+KQ6waEnrB\nqcWJm4ZJITh1rwb0A2s8dEUF5TzreCF/9Bt8ay7oHfMn9NVbBScTWcovwcK/\n03j0F+vY4Nr1ApJe+MCRfWLqmoV1p+PE9dprng3M8/FdjWsjf4vS0n5j/fDx\nxqfSoHOwqkhId98PPGQoLXJZYwPXptg3QqK/Sglfbbol/FPpa6wmqNx81EIg\nh3UemIHyyFv1EZfgDO3RHmcStZeq67dsw8nt9HVjU2T2dcQ0FLkN8+jylR+c\n5zQDiEk80beXHql8wnwfjAdR4my2TKm9qYYBHgzf+InNkq8p4f4WQoQPiKJN\ni6OsWT7o/EcU3dzNHorjo9QuXAGDAHEDb/Ts9lsK84TIzR6dEO0vKkMMuhi6\nyhmQ\r\n=pKLE\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "2.8.0": {"name": "domutils", "version": "2.8.0", "dependencies": {"domhandler": "^4.2.0", "dom-serializer": "^1.0.1", "domelementtype": "^2.2.0"}, "devDependencies": {"jest": "^27.1.0", "eslint": "^7.32.0", "ts-jest": "^27.0.5", "typedoc": "^0.21.6", "prettier": "^2.0.5", "typescript": "^4.4.2", "@types/jest": "^27.0.1", "@types/node": "^16.7.2", "htmlparser2": "~7.0.0", "eslint-plugin-jsdoc": "^36.0.8", "eslint-config-prettier": "^8.1.0", "@typescript-eslint/parser": "^4.29.3", "@typescript-eslint/eslint-plugin": "^4.29.3"}, "dist": {"shasum": "4437def5db6e2d1f5d6ee859bd95ca7d02048135", "tarball": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "fileCount": 27, "integrity": "sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==", "signatures": [{"sig": "MEUCICLMTePa5bXHlNoSC+FFnUGYUs3cpmDf+m+5H8t9mvN2AiEAgjMlHHd1nMGGGP2OU8UWrXv4E5atDYpHzs3/ve6Gtww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKXOPCRA9TVsSAnZWagAA8p0P/16dYM08QSXHIbGPdgwj\nxLCTU52MUAYVkC8p8co+QppALghvt0Hb2yDnapA+4dCyfttgtiKx+uG2JWo4\n/j4TeQ6N1SMZvJfAUMg5eBei0icRaOQHVD58+wVnHZGlYiSZfUF0GBjBysXv\nsu01qJ4/MO9ZE4VE+LH4/m4h8QY34l9T5PmUX1sW6347vukhu0k5+RnTzwit\nvaQYVzHZOh3AU6qLPsCk6wD+LFrJ2IRdZQzYWCXbMECkTkWPdRozamhQc3+m\na9iDIzE8om7s6fCFAJNwI1cM5ubkBv5fUMk4bflJKzEd0hZoHZztpBQQGlq6\n+fKU+J0Y/MOTwBy0lokKJ9t2dYQywD9MDrJgmgTFkX65n91SwNQ7T0mQqt+E\nhOde7VfTJRYiEw+WuC+7dH7KuL13+YBlyLl2bK7wAtmTKenbpJnB37DT7uPO\nA5YldXPRM60JX7jmwZklNcwgv16MAUFOVuvIzVbXVPcel/urOXevKZ/6Pyyx\nwQ9QZyp7zWcHK292ikwhwD4SuM2rdSn9K3a1dH4kxff0BHQTWwGeOe3RALdw\nfgVJhmZJhhD79W3o9fsggzzoNdl7qObISuRKqHS5yfg/cmLoxX1DxetPrCKm\nPpYzgKPFeLbxlJyyfFZu/+DP6mX+S2Iqj/JsEcEs45o+8L+cpEUnAmo67UDP\nKEWe\r\n=PJan\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "3.0.0": {"name": "domutils", "version": "3.0.0", "dependencies": {"domhandler": "^5.0.1", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0"}, "devDependencies": {"jest": "^27.5.1", "eslint": "^8.13.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.15", "prettier": "^2.6.2", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "htmlparser2": "~7.2.0", "eslint-plugin-jsdoc": "^39.2.7", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.20.0", "@typescript-eslint/eslint-plugin": "^5.20.0"}, "dist": {"shasum": "6d77bd48f9c1b2f75863e86f3f3cedaeb0ccef29", "tarball": "https://registry.npmjs.org/domutils/-/domutils-3.0.0.tgz", "fileCount": 68, "integrity": "sha512-lnrXdVDjQW/KpTOk+eDK7CnrR7A2Q5hzanVshqaLi1OFH1l+WsRUUANkUtoEOEzy8n01VtHaXMf9E7hcQVmhjA==", "signatures": [{"sig": "MEQCICG+vJ8oZjfH6STradDXfpjXAgbCSAFWpkMSs3gumdKoAiA7oliTT/Iz9ZX4PH5OzCXRyc/dKnk+GgmyzGv3oqeJmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYWagACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMLQ//XcBrdVWz0Kq9azz2Jf+Gi0xL8WIOLu0NK/pODa6sahff6n0J\r\nretscjUUWRK7Xxx6AUdmCd6iBWrqxprp/BxMlcZ20rePyXVSF6Vf8t7lDqKH\r\nb4K+AqmNMTTgOdCrqx5RXPgAMBj5tR6lCH2sy9U4/J2B7u7q3KbdAcoF5uH3\r\nxi8xiwFJBQIAznek+Qt86fSx+jPCbo978f5uHKWjT0qDLJ1CbDJ2hXd8itvu\r\nXQJ2xkJEtED1YFxMJEJCzLaMWbWh8JpHYIgnIlYYBV9LP0ugMeftaeap+Mi2\r\n8fTBdAaxyIkh1euBI+oL/7u3pKPxPw59MVn8J8oiHj9AklqMsEPggy0XYu/s\r\nkkeTsLUw8xgRWIvMyMf7q4O/DvUPYB1QzjhODpHFohTjuFDnw8A+uPABqb3x\r\ngPFBOgUfFRTv/HzdwTdfeQNXEZxMaNqR+VIj9DgciMqBoFu6QbOKMe+bwZtW\r\n6ZoKFnWQcMPBCmkQoEOoIoFTlreruDCFk1z4N2CmKxxm8NbEVmKOHEN1SfQm\r\nJLyYPFDn+G9OlT2TjzQW9DVO1MOiykwlLKpx+O/iaboPiVi9q0wiiNhfo9ci\r\nZwyoUn3TlOFFNqtgIoQIOc4MhctJj7NR4o51lmQQBJ+wy0ZR/mAAqHHVV/bW\r\nyRDI8aW0Zhz3Fb1pEdlj5weiemqfxrA/XpY=\r\n=hKsE\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "3.0.1": {"name": "domutils", "version": "3.0.1", "dependencies": {"domhandler": "^5.0.1", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0"}, "devDependencies": {"jest": "^27.5.1", "eslint": "^8.13.0", "ts-jest": "^27.1.4", "typedoc": "^0.22.15", "prettier": "^2.6.2", "typescript": "^4.6.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.25", "htmlparser2": "~7.2.0", "eslint-plugin-jsdoc": "^39.2.7", "eslint-config-prettier": "^8.5.0", "@typescript-eslint/parser": "^5.20.0", "@typescript-eslint/eslint-plugin": "^5.20.0"}, "dist": {"shasum": "696b3875238338cb186b6c0612bd4901c89a4f1c", "tarball": "https://registry.npmjs.org/domutils/-/domutils-3.0.1.tgz", "fileCount": 68, "integrity": "sha512-z08c1l761iKhDFtfXO04C7kTdPBLi41zwOZl00WS8b5eiaebNpY00HKbztwBq+e3vyqWNwWF3mP9YLUeqIrF+Q==", "signatures": [{"sig": "MEYCIQCTg1z5Alz2xtt5M01XQx/Fbat5FaMaPts1pvY/hRF9cgIhAOdGtwDY4/ZKa6UAELoZySzYvkP9ry88QuiEomK8HhfP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYooAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWaA//VpXA5X7AMdu7W4QeNWDeu+vJqJYEGTZhYTXufHxhCCLjGhwL\r\nwBJznYKGoagxFCkZN5lf/fdH2IbSCZFA7CN4R6HsJj2xlkkCr3Mgu/omL7kl\r\nAYAM51rjbmVAnRzFSTg0kj8nVS2CjqYo6Qs/TqQM6Qiid6DSVrzpoFl0Rz09\r\nohG6iRNXWmfz5FNobUWv3ODfMHRkuVy3EPW2/ubI/T3tVUC4BWVpOykUtTow\r\nqFLIwxURUFFyYmHobZ3x1LCqXIq2e8lfRiGL1pM9mi1LXs9xxdYnOv+yfeHZ\r\n9WJQWz1D8zzGwygOn7KH5hryiLASunRO7U4se6+h3wso1AnQoLPxGmQ1SGz+\r\nc0Rr67IGLbIauOCtKxG/7Ta0ICV44eOoVNeRXE139xSV+YUGtC//VHojycnV\r\nNAw3Kr2GOC7fdwRt1qegpifUguE3b8byBVDcz8r6w7gC3rCotia2290cJeij\r\n6n3G3bVBeOB0UqPXt61brfW0haAi6zMXKusDvsqcSacJYZonv1/5qWfvio2w\r\nAxEDZlJKv3h9elz0+ItGRccwWFH0JqBUZIYKYqIY9f24DFfqpeYAHlObXMnw\r\nvcGdwyhEUyD+5UeN1xVYNnWhTBXWI5VnN2IkzyFInzXDM9D+qHkYjoadkvir\r\n1hS+BuOlvxBBSDl5LA4Tb83kRwb6gwcqzao=\r\n=RyMC\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "3.1.0": {"name": "domutils", "version": "3.1.0", "dependencies": {"domhandler": "^5.0.3", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.39.0", "ts-jest": "^29.1.0", "typedoc": "^0.24.6", "prettier": "^2.8.8", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^18.16.2", "htmlparser2": "~8.0.2", "eslint-plugin-jsdoc": "^43.1.1", "eslint-config-prettier": "^8.8.0", "@typescript-eslint/parser": "^5.59.1", "@typescript-eslint/eslint-plugin": "^5.59.1"}, "dist": {"shasum": "c47f551278d3dc4b0b1ab8cbb42d751a6f0d824e", "tarball": "https://registry.npmjs.org/domutils/-/domutils-3.1.0.tgz", "fileCount": 68, "integrity": "sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==", "signatures": [{"sig": "MEUCIQCJJbLH8XI+HjJ0wK1jZFBP0yFG2bfF1oeARCTHGYkzbQIgO0RegH93qGSL0mBiwAQAs/nD/CQ8kHMLsN4tXri5Vk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTOcVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovZA//VBZNpUrJszVbFMlR9mMFQ1akwLM5MjUiPyzgsEUaz3RATi22\r\n5/+de+xc2Otw8ssIOS8k8eSbfsA32/SII1YLvFzBLy47yYGyeSgtWAjXN69y\r\nuUIwBnYouSsmlDzWy0O/5Y6xpl6cCCMwFd8kwOdpe8NNMA9SyFqKR3lv69gG\r\nJ2I191q2GBDvwSoMe+0bUcAEibwodTDl/7Cx9ngvFtx2sntsXO+6UThjJths\r\nivAqHdDmzOVLWvIPgdhwHAnCLIYwNUznOdAcyrwAFrntIIsak1FCZSATn68Z\r\nlN9RwXexRhogGWh7vNmkpkl+C6yFEQz5555u8B7k/ZYcyeJtLrw2W/0nSSzl\r\n7p0umwyA7wWWmkGGMGGquGaXgBHANI0yuzNP//Tv/VcEBQ0lkJAXov+i1jGf\r\n7aXBcxfVuA3M6SB4nqH1S7tTohkExvY1Lz9whz2hjHYteO3zKy/sBLSR9Q4m\r\ntPbssLHBmD+duIwpUZi+uDWViufB3EVQLjRgksy6uKYictJ2r45Kno8BifEF\r\nGHXswdSL/GVLUSCdj9ACqMQIEsPbB+AZydysl839HFhphQCreuQxtXH7GaUc\r\nXdI2SibFKbo+KEBuGIsXVa1y5p/efEbya4Xr0KEC8zWs8RNK+HqAj2m8SPD9\r\nrWWmq9fqSrUvM1Uwd+vPbO2C9VtMBypYZH8=\r\n=6i3B\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "3.2.0": {"name": "domutils", "version": "3.2.0", "dependencies": {"domhandler": "^5.0.3", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.57.1", "ts-jest": "^29.2.5", "typedoc": "^0.27.5", "prettier": "^3.4.2", "typescript": "^5.7.2", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "htmlparser2": "~9.1.0", "eslint-plugin-jsdoc": "^50.6.1", "eslint-config-prettier": "^9.1.0", "@typescript-eslint/parser": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.18.1"}, "dist": {"shasum": "895db10256d7d6631bd9cb368e38e5110fde3448", "tarball": "https://registry.npmjs.org/domutils/-/domutils-3.2.0.tgz", "fileCount": 68, "integrity": "sha512-pmjBRsZD5Fz3XR8NlM1zTBDlHjB5OvLjMkGMGHrLabzoECJNPPOCiCNEgSSLQn2pYIOO4Ytfia7zIehRiit+sQ==", "signatures": [{"sig": "MEYCIQCJKWLXgOgtA2d0YevoiUHz+A94LCpQSxfW4YhWBNdRVAIhAMcLAicRUzPfT2XPNUsm0NJKnCyS5Q/vVxSck17c3jPI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165368}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "3.2.1": {"name": "domutils", "version": "3.2.1", "dependencies": {"domhandler": "^5.0.3", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.57.1", "ts-jest": "^29.2.5", "typedoc": "^0.27.5", "prettier": "^3.4.2", "typescript": "^5.7.2", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "htmlparser2": "~9.1.0", "eslint-plugin-jsdoc": "^50.6.1", "eslint-config-prettier": "^9.1.0", "@typescript-eslint/parser": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.18.1"}, "dist": {"shasum": "b39f4c390a1ae6f6a2c56a5f5a16d6438b6bce28", "tarball": "https://registry.npmjs.org/domutils/-/domutils-3.2.1.tgz", "fileCount": 68, "integrity": "sha512-xWXmuRnN9OMP6ptPd2+H0cCbcYBULa5YDTbMm/2lvkWvNA3O4wcW+GvzooqBuNM8yy6pl3VIAeJTUUWUbfI5Fw==", "signatures": [{"sig": "MEYCIQDYwVZN23LfBRecV5dnj7pI8r5UpuDsjQNkQ5uRnDsAAwIhAJoCw5LtUr8AEZOKZE4O+2UpyxNoUM+34g8CLjsJPCs1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166526}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "3.2.2": {"name": "domutils", "version": "3.2.2", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.10.5", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsdoc": "^50.6.1", "htmlparser2": "~9.1.0", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "typedoc": "^0.27.6", "typescript": "^5.7.2"}, "dist": {"integrity": "sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==", "shasum": "edbfe2b668b0c1d97c24baf0f1062b132221bc78", "tarball": "https://registry.npmjs.org/domutils/-/domutils-3.2.2.tgz", "fileCount": 68, "unpackedSize": 166738, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2yubRCrI6TfNaiWRR+/+ywvGlvyUgokHzE5Ynio9nWgIhANGkqMeBpwLaH7sNzZPB4agzSsNSZjTuus3S2nEHjX8M"}]}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}}, "modified": "2025-01-06T20:05:27.693Z"}