{"name": "range-parser", "dist-tags": {"latest": "1.2.1"}, "versions": {"0.0.1": {"name": "range-parser", "version": "0.0.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "dec6a8b9792caaef485e733b75b5b73fc7095770", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.1.tgz", "integrity": "sha512-oryvInGgJiHLtv0ZcAbru1T1rILcRqNE1/ah3aTPiLAwp2629ElAtZ1ocJ485dvAmc+l2IgtRaKEXSLXd9HKIg==", "signatures": [{"sig": "MEUCIBWleRZSV0/K9K/1ksLQ3XlX1YYYb6xdDYo53Sbu8QMfAiEAsd3f0+LlTHtZwZdMOjL4hs5fsM35rgmMbT3XX1lMCQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.2": {"name": "range-parser", "version": "0.0.2", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "034fafbd8b266f64d8effe8fa638392b2290b288", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.2.tgz", "integrity": "sha512-icKpYzxoZAOK1PT5UVxxYQLWdhW7xvEGFDKwBW3frjwKk916XpfmhTxx47bV68RfGhwunXLDWP7bXCRTy6cckQ==", "signatures": [{"sig": "MEQCIFmC/O3yYRQO6uxSMnWyyL3fBjuBALVT0mYyZAGCUF8AAiAzD0/UetXtaXfgweWxBHwkoij5SKYtqd4YHgYi1jEnnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.3": {"name": "range-parser", "version": "0.0.3", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "6e6488fb73843bd4bd626797f76b870da9765ae9", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.3.tgz", "integrity": "sha512-3yy/idKE9+XSKk5KVy7NkRmVTI1R5TAvRXO23CnO64+2vdmyhxhScD4pg/2ylh9vwJXeDFlFr9vJyOsBvJR1TA==", "signatures": [{"sig": "MEQCICD7ksWqF20hN/AKGotMp+Yy1Ju4PWn6Xv/6Lzx9/hFqAiAl1OXD/EsfkdL/OdlOX4B5mb7/czPiB5EA15fhz1u5Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.4": {"name": "range-parser", "version": "0.0.4", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "c0427ffef51c10acba0782a46c9602e744ff620b", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-0.0.4.tgz", "integrity": "sha512-okJVEq9DbZyg+5lD8pr6ooQmeA0uu8DYIyAU7VK1WUUK7hctI1yw2ZHhKiKjB6RXaDrYRmTR4SsIHkyiQpaLMA==", "signatures": [{"sig": "MEYCIQCsI8Y0uPSCTSjthi+z/nKPlsxr6QSnIOiSba7xZrRRTwIhAOTlv3N37L0LsoyDhMLP9FQDLFAcQq6vshJaqGtejnkf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.0.0": {"name": "range-parser", "version": "1.0.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "a4b264cfe0be5ce36abe3765ac9c2a248746dbc0", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.0.tgz", "integrity": "sha512-wOH5LIH2ZHo0P7/bwkR+aNbJ+kv3CHVX4B8qs9GqbtY29fi1bGPV5xczrutN20G+Z4XhRqRMTW3q0S4iyJJPfw==", "signatures": [{"sig": "MEUCID+EU4aRGjKrtm31tA+STZCWTG0aVAB5baa+Rz4+p5Z5AiEA1H0pBf1qAQDOiZ6Ekp3jQJXHYwg+idvz/DGW0Awfzr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "range-parser", "version": "1.0.1", "devDependencies": {"mocha": "1", "should": "2", "istanbul": "0"}, "dist": {"shasum": "f9da15b7451fe1b261959b63342dd92921d34da2", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.1.tgz", "integrity": "sha512-ajMpjaHx1EnsiUlv0SSn1ljlyd3BlssbmNPD5880IGeMbOQixcoz0KDx8hengN+XIEXG2HXr5sAfoJc9gjBEMQ==", "signatures": [{"sig": "MEUCIQD4QDbBaIdC92lMzFUwgPkxcS01Cnwpp5GwPlLNfYf5MQIgNBbMrH+s8zrbOaB7S8002gOTqZcMb1nh0y377B+DGTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "range-parser", "version": "1.0.2", "devDependencies": {"mocha": "1", "should": "2", "istanbul": "0"}, "dist": {"shasum": "06a12a42e5131ba8e457cd892044867f2344e549", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.2.tgz", "integrity": "sha512-jFJ4/7R56zPTiBtje6f0Zyzh0ippWiAeSzpOFRjqVeXVat15mVJkpn4Oo76r64JPGJVH6RmSLbxCvOh7ysijzA==", "signatures": [{"sig": "MEQCICm/T1H7dETb5jyErjXO7pGXcpGFRmMPXv/3LWdbxa9JAiAiXzCKLUCXBG4ELMYxeht8J8Y+XvMpDqunv3X+kMVuwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.0.3": {"name": "range-parser", "version": "1.0.3", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.0"}, "dist": {"shasum": "6872823535c692e2c2a0103826afd82c2e0ff175", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.0.3.tgz", "integrity": "sha512-nDsRrtIxVUO5opg/A8T2S3ebULVIfuh8ECbh4w3N4mWxIiT3QILDJDUQayPqm2e8Q8NUa0RSUkGCfe33AfjR3Q==", "signatures": [{"sig": "MEUCIHjU89Nf1hzEymNoxxudEhoF8HzI86CE7H1Am04SFhV0AiEA2IYe0lt9rS4pkv3Ytwx8dGCUmc6rlwaw6xtgPkAYmwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.1.0": {"name": "range-parser", "version": "1.1.0", "devDependencies": {"mocha": "1.21.5", "eslint": "2.9.0", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "425c2c5bf8b159d89513fe55f26c29d07b88512b", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.1.0.tgz", "integrity": "sha512-6rmDAROeyfqjdhhr9wMLh+6ODrbojLeMbDou5Xz4oUuV9gKKTO+P057EpPhTN1zeku5fbGuYAU0K8IrUfIWAfg==", "signatures": [{"sig": "MEQCIEEKreJu5PtWdt4uLH+ebST0oTFWyQCimQf3qkpL1AUFAiB9OoCGPFhE+6CIacIxsW1oAyuZoVpGbMSv0o7PWmcYpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.0": {"name": "range-parser", "version": "1.2.0", "devDependencies": {"mocha": "1.21.5", "eslint": "2.11.1", "istanbul": "0.4.3", "eslint-plugin-promise": "1.1.0", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "f49be6b487894ddc40dcc94a322f611092e00d5e", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz", "integrity": "sha512-kA5WQoNVo4t9lNx2kQNFCxKeBl5IbbSNBl1M/tLkw9WCn+hxNBAW5Qh8gdhs63CJnhjJ2zQWFoqPJP2sK1AV5A==", "signatures": [{"sig": "MEYCIQCKdgQqHXTz5EcKiBj0cH9GLK4cnCTzd0G0Zxiq8+U1+QIhAPhU9I9SG24ieDGKCXNEqEygwu5eRk4loOZEwGM7nLK8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.2.1": {"name": "range-parser", "version": "1.2.1", "devDependencies": {"nyc": "14.1.1", "mocha": "6.1.4", "eslint": "5.16.0", "deep-equal": "1.0.1", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "3cf37023d199e1c24d1a55b84800c2f3e6468031", "tarball": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "signatures": [{"sig": "MEUCICYe3UbG+guUpzX1hy4AXl6wAiaz8eWTcN1t/jr/EBvgAiEAhkdbwL0VfEJfp3+Ws7+8CRKWhmXmp9+poH/RiJOJmpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1hb6CRA9TVsSAnZWagAA6MIP/2aFx73ToT5iGRN0mgZw\nLy7BEmRQkblBxo81k9Cvjt8jucfv/PtDUllITbke596JI/jHnb5oU5XV5Fzd\nK1WklG8rJC9z1y9iEaEe4pRcnvkPvkHtFMmPcYS9dm5XQyYw9fYcApRl/6zH\neuV/1CtwzQnCCErvfmolfSQ13v1d4LyWwrMRcG79uOGYBm8XSuTb3fKrEbBj\n3Gms6SQA2mC5ntKrf6VQRXzWvGIvWp2Q3RGFkgI1fnEPFfq7DbNqvHiD+KV4\nXV2wOD6B8pBlpbpSdNMHfUBSVSMHqFv9rOJqJumBAEZeUcwQzO06/2kUw5/f\nWCaFTHYTMCvSNX9qg71EoRZuuvNS0E53quFagdmTxq2vf04vz01PgRc5G64m\nTlz07gVHos3CQ9fU4NP0Aim1rtgOLJj15IF+z/kSSQoQZ6DhW5aTs8zCa3AS\nk4xWFafBuzyG1ApCSJrRCsqzY+oRqHPyTSZx5fBv75qZIEKe4moBpJ6EkaqZ\nRc6EqCpJjwOZVOssoPN3RiP+f/g43ytkVmUoYSXTs2bpkwNQUJShg2ItsT55\nkxkfZx8YbNxkFKp8S/uwUGQjbmXXspWozrkKNHikpzWMgDmuj9k2fx3LPo0c\nDSEq295fDQlKdxcfuGpOkH1PSzMHuIef26IGNDjMlOyBSY2FrQJaxpYtpQYq\nw1FC\r\n=GGKV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}}, "modified": "2025-05-14T14:56:26.210Z"}