{"name": "webpack-cli", "dist-tags": {"4.0.0.0-alpha-5": "4.0.0-alpha-5", "beta": "4.0.0-beta.9", "next": "4.0.0-rc.1", "latest": "6.0.1"}, "versions": {"1.0.0": {"name": "webpack-cli", "version": "1.0.0", "dist": {"shasum": "beaaf95e26ba4c15c873e7c684077ce22e0b8f2e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.0.0.tgz", "integrity": "sha512-fEOxiZ8/14PeSe6O1BrQqXCvYIfBWZ9bGFiAp+HqV4ZjqjA8r/G7zX8Qq7lsHxC+XRntsfYVSKhmJX1jTnQb8A==", "signatures": [{"sig": "MEYCIQCKNudZS0w4FFfTRrJskZuVaELpuoH5PoNgTWjRuzgGVQIhAOVsEUHpH9cO7Gzrq7aI7s8BEbgfKRg/xs0eHXHsXCRv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "webpack-cli", "version": "1.3.0", "dependencies": {"got": "^6.6.3", "diff": "^3.2.0", "chalk": "^1.1.3", "listr": "^0.11.0", "yargs": "^6.5.0", "lodash": "^4.17.4", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^2.2.0-rc.0", "inquirer": "^2.0.0", "prettier": "^1.2.2", "interpret": "^1.0.1", "babel-core": "^6.21.0", "cross-spawn": "^5.0.1", "jscodeshift": "^0.3.30", "resolve-cwd": "^2.0.0", "loader-utils": "^0.2.16", "fit-commit-js": "^0.3.1", "p-each-series": "^1.0.0", "global-modules": "^0.2.3", "supports-color": "^3.1.2", "webpack-addons": "^1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.0.2", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^1.6.6", "babel-preset-es2015": "^6.18.0", "babel-preset-stage-3": "^6.17.0"}, "devDependencies": {"ajv": "^4.11.3", "jest": "^19.0.2", "eslint": "^3.12.2", "babel-cli": "^6.18.0", "babel-jest": "^19.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.2.8", "ajv-keywords": "^1.5.1", "babel-eslint": "^6.1.2", "babel-polyfill": "^6.20.0", "eslint-plugin-node": "^3.0.5"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "2340e7cc2bc50eac59abcb08828ca5f67d2581a1", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.0.tgz", "integrity": "sha512-T0DBKVg0SgzGu90/SCW3hhYvq9xFAfncFa2XKap6UzGYSrMjgGaKST1GKyt5V6eJf17lMuurNygkq08qlPgdSQ==", "signatures": [{"sig": "MEUCIHwrOGtSZUVSuChL7GzbZ8SIKPMdyXPTWT8GGkByI6lbAiEAuiHIg9BnK47Ha4f0HDnCYsUYjselB7sb+F99TIzbXQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.3.1": {"name": "webpack-cli", "version": "1.3.1", "dependencies": {"got": "^6.6.3", "diff": "^3.2.0", "chalk": "^1.1.3", "listr": "^0.11.0", "yargs": "^6.5.0", "lodash": "^4.17.4", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^2.5.1", "inquirer": "^2.0.0", "prettier": "^1.2.2", "interpret": "^1.0.1", "babel-core": "^6.21.0", "cross-spawn": "^5.0.1", "jscodeshift": "^0.3.30", "resolve-cwd": "^2.0.0", "loader-utils": "^0.2.16", "fit-commit-js": "^0.3.1", "p-each-series": "^1.0.0", "global-modules": "^0.2.3", "supports-color": "^3.1.2", "webpack-addons": "^1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.0.2", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^1.6.6", "babel-preset-es2015": "^6.18.0", "babel-preset-stage-3": "^6.17.0"}, "devDependencies": {"ajv": "^4.11.3", "jest": "^19.0.2", "eslint": "^3.12.2", "babel-cli": "^6.18.0", "babel-jest": "^19.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.2.8", "ajv-keywords": "^1.5.1", "babel-eslint": "^6.1.2", "babel-polyfill": "^6.20.0", "eslint-plugin-node": "^3.0.5"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "88e9c9155005a95c02e4e01b067e6a192a279b9d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.1.tgz", "integrity": "sha512-IjojEUihg+NKTM5xV5ED/XAVbnOWSaET2m+yWdYbsP43k36fCuAtmAiQvSHxKzvWl6yUo30mU1r7Xj9gcwmrQA==", "signatures": [{"sig": "MEUCIBVLa0jRySyA484utpcTZQkDFVPZnlXD60PfaaAjY0ljAiEAii1AgVVsXSI6+mtEPv9vWYZmIAsFdjgIQaVArNNWV/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.3.2": {"name": "webpack-cli", "version": "1.3.2", "dependencies": {"got": "^6.6.3", "diff": "^3.2.0", "chalk": "^1.1.3", "listr": "^0.11.0", "yargs": "^6.5.0", "lodash": "^4.17.4", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^2.5.1", "inquirer": "^2.0.0", "prettier": "^1.2.2", "interpret": "^1.0.1", "babel-core": "^6.21.0", "cross-spawn": "^5.0.1", "jscodeshift": "^0.3.30", "resolve-cwd": "^2.0.0", "loader-utils": "^0.2.16", "fit-commit-js": "^0.3.1", "p-each-series": "^1.0.0", "global-modules": "^0.2.3", "supports-color": "^3.1.2", "webpack-addons": "^1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.0.2", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^1.6.6", "babel-preset-es2015": "^6.18.0", "babel-preset-stage-3": "^6.17.0"}, "devDependencies": {"ajv": "^4.11.3", "jest": "^19.0.2", "eslint": "^3.12.2", "babel-cli": "^6.18.0", "babel-jest": "^19.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.2.8", "ajv-keywords": "^1.5.1", "babel-eslint": "^6.1.2", "babel-polyfill": "^6.20.0", "eslint-plugin-node": "^3.0.5"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "caec956bccaf2c9787ba5033f413adfafb2c260f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.2.tgz", "integrity": "sha512-ve/ms3m3LMDVRtUP3waDbxpTXBBaT8sF5mFEHAN5J9fj1JnFMz2tYUxcpGzVf8QEUJSjNpS6ZjQqkAGHmRwE6w==", "signatures": [{"sig": "MEYCIQCsrHaJYewLxqzPyTqUxlh9r4L7WOOWMu2zo4kSqSn3zAIhAPUepf7PcUS2URiDeFpPysg+RBEMbiO9hbTug2WGs3I0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.3.3": {"name": "webpack-cli", "version": "1.3.3", "dependencies": {"got": "^6.6.3", "diff": "^3.2.0", "chalk": "^1.1.3", "listr": "^0.11.0", "yargs": "^6.5.0", "lodash": "^4.17.4", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^2.5.1", "inquirer": "^2.0.0", "prettier": "^1.2.2", "interpret": "^1.0.1", "babel-core": "^6.21.0", "cross-spawn": "^5.0.1", "jscodeshift": "^0.3.30", "resolve-cwd": "^2.0.0", "loader-utils": "^0.2.16", "fit-commit-js": "^0.3.1", "p-each-series": "^1.0.0", "global-modules": "^0.2.3", "supports-color": "^3.1.2", "webpack-addons": "^1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.0.2", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^1.6.6", "babel-preset-es2015": "^6.18.0", "babel-preset-stage-3": "^6.17.0"}, "devDependencies": {"ajv": "^4.11.3", "jest": "^19.0.2", "eslint": "^3.12.2", "babel-cli": "^6.18.0", "babel-jest": "^19.0.0", "pre-commit": "^1.2.2", "lint-staged": "^3.2.8", "ajv-keywords": "^1.5.1", "babel-eslint": "^6.1.2", "babel-polyfill": "^6.20.0", "eslint-plugin-node": "^3.0.5"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "fb63ab0a7bf91c8467f09362bedb58f82f293094", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.3.tgz", "integrity": "sha512-gP3NiRBeB6u9kw/CtkAicpJ671tvZdKuUnTT/4oWq2ZVKEK6C9xUc/3ZjTpOlyvYRd6ZL3/Y2SeBNKsVm4qaxQ==", "signatures": [{"sig": "MEYCIQDVv67g5nqCPerm2NE4iq5S4igLjG537wq+MxYfVFRTcwIhAJ3sSK++KsZJF3JDGyY/NQDmZiQCBg09aCvfk4QONGCk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.3.4": {"name": "webpack-cli", "version": "1.3.4", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "b4b05c13f458f8b48dba1175d2490d3718813add", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.4.tgz", "integrity": "sha512-LtwX3jj0yzIkrPNTUv+zJxFtg0KRdVFY8NL5WaeEIRdfXGIeBWVS5umxY0BYfQmviT7Nx1s6KsveYfHDGUeqPQ==", "signatures": [{"sig": "MEUCIHD3bZko/E3LzSKKsT3rk53c6Ja7SXdsWk6kBBihSQ7gAiEA6vu9k/HL+YVzikitLIK1NockN/GCoXWgH6tDXhHF25k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=5.0.0"}}, "1.3.5": {"name": "webpack-cli", "version": "1.3.5", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "124236576fb65d1c1dd7a97e4591c7820846c0d1", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.5.tgz", "integrity": "sha512-K6f+GSUDRPKY4u0eohhLVZmdpohzMQqRYVhHaf1+8gm4wceyHuNjt4HDoknhEpF7RyelILg0NVlZr9Tmfp7EpA==", "signatures": [{"sig": "MEUCIQDBjvMb1HMmjWkO3ziYctfCDIC9NH/k4RH2MiOHiAA7xQIgbgMd+iOpTEcaDFAt7/8DdXMt6ntW0c3DeGKx8GShPqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=5.0.0"}}, "1.3.7": {"name": "webpack-cli", "version": "1.3.7", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "1489b4936086b12a10a0fcf977a8551673f91f7a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.7.tgz", "integrity": "sha512-T27np3xp+hascYH8ElgpZADTgD1+u5XhLFp1Z8P7ir8uua9UIYxoh3Gb9N8HCqlJwr+c74r7cSj2+N1I1PRxFw==", "signatures": [{"sig": "MEYCIQC50kjBhrry8OsQsTMUQ+CaG8itjSWqjieVEDmVqXBiNgIhALRQxeEczYFdEW27dgJP40XY3zkKVDeYn0XOretSN42N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=5.0.0"}, "hasInstallScript": true}, "1.3.8": {"name": "webpack-cli", "version": "1.3.8", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "be86edd06ab4b9c31875b81f456d26bef95a5488", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.8.tgz", "integrity": "sha512-I+UQ2S0mka6T+Zvml/uI6j/xnoMFO8I6hGBFhrpPUOXl6U7mPHoKaQqPPEnlJhPSsq6IIQ7NN+PErXS/rsJB+g==", "signatures": [{"sig": "MEYCIQCCSy3JIB8S8xz6TrwAyDmTduGUAKtHfs8TSY0AwOXLMwIhANimnKOBaqoO/kq1JM9eMxXXuOmYB/8EFuJcOAMgTLaF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=5.0.0"}, "hasInstallScript": true}, "1.3.9": {"name": "webpack-cli", "version": "1.3.9", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "d024587639465f353b35ea7a62724e19d1288e15", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.3.9.tgz", "integrity": "sha512-VxY+iB9RLNWobLe4pJBftCumbDFTjuDfcPojI/xtKpd2HTaNyFczgHsEPeI4GL4v2I9pSOVPgMrvXcQNY4Ci0g==", "signatures": [{"sig": "MEUCIC93W5n9YE0PYN1ZvJ9ydCq5bwn0i+qEEiS11o2KAQpkAiEA6TqZi5UGYGKD6GnKe8OabaaKrXw/TIzKL9SZCtd8AJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=5.0.0"}}, "1.4.0": {"name": "webpack-cli", "version": "1.4.0", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "310fa111c7389b7aed27448d6347f1087f45dd09", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.0.tgz", "integrity": "sha512-C7t3xCMxGBjs9ZT0O8sGJecpj2S788+ljjmFC1T+/bOa/O8bOKNT6L9OMzt0DgymeSJUJyqvMH8Dq62VpHCygQ==", "signatures": [{"sig": "MEUCICyjPeJmb28x+Pp/8ZVroPZzKvgpgJEXwjpCE/nr8xyCAiEAqMUwiaJ2cz2Fxz/wXhvZdkGV7HRPl7bWNzsnAb/6O+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=5.0.0"}}, "1.4.1": {"name": "webpack-cli", "version": "1.4.1", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.6.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "b1849e69e9519cfbf717dcc14fc666b961a4a5f4", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.1.tgz", "integrity": "sha512-L8DnA3SIecfDdVR0p6IdNk/qwk3PVBcokrjBIHukbOVTMlFhmrRkumZJBkJqZ/bGItpXF9pNXcHjGsS/iNLmGg==", "signatures": [{"sig": "MEUCIQCUoiMawLZrzyUm6Aq3Oh9nyIJgSHqW7oNAfi7qbIxU9gIgWHroFXt2+ZscXv4Cbg4r0GlniaKjzIHuGzrHSuvosfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.2": {"name": "webpack-cli", "version": "1.4.2", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.8.1", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "webpack-addons-ylvis": "0.0.34"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "d73a5d536b0310a46721e88ac9e3fdf1f2bde278", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.2.tgz", "integrity": "sha512-ANqDXUNzuparP8UoAycVoy5/gDIlAnJcM7tdiwj+JgdCMzSUylWMpVNy0xusJEX+DxS/oVPf/0POqF1i7U5Zow==", "signatures": [{"sig": "MEUCIQCxIqYhr/QMzyQDcFKYDXkwxxD2zFSxJU7sRaC6OkzcDwIgLvz884umsCtLFUnycgaqqNYmjZB/u+YuGHHh8h6pwio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.3": {"name": "webpack-cli", "version": "1.4.3", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.8.1", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "bd07e882b9086a16baaa8d9ea439ea29b65b00b7", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.3.tgz", "integrity": "sha512-<PERSON>lCD0DEyxUkKdgEx6N8s9YKgQz5IJZCgh1btB1RvAnmuKFN5gN5WYWjt2lq02hJf3eSowNAKNJ92u5S75LQ4CQ==", "signatures": [{"sig": "MEUCIQCS9xEsmmrCw58hIpeBqr1M93RpQ/6Lkubh3PK7QFCMtQIgCzxROnkWFNNX+L9PgMIHJ8newi69QNrqRqhXx68DVgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.4": {"name": "webpack-cli", "version": "1.4.4", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.8.1", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "1e77fa248edfcea9693779d7d8457282a3157b4e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.4.tgz", "integrity": "sha512-MXPRKwyOp0f1E2TPYsftoIpiNTL4KaMbtuYKWIWyhRi1OYZSwlu7MuoD9g2k8qZ6LOggrhyOqrWZO3pg1cXpWQ==", "signatures": [{"sig": "MEUCIGLqQN5MtBZjCKYoiRFslA2MMg7QjzsjG/fS40L16LdzAiEA0Apv13+mcCNe1Cox69fyPDdufgaG1MZz7ZIlG6zfOxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.5": {"name": "webpack-cli", "version": "1.4.5", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.8.1", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "2bee0b6622db4f4e82e1ce48f3ac36210db3839b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.5.tgz", "integrity": "sha512-RzpoTyB+5Wm+fJC40039mXdLXTVgnAPloZ5Nk0ZisjkpDggoW1Brq1owlUbc/B+kCcFuyC5lA7zWgA0DFLu6Kg==", "signatures": [{"sig": "MEQCIEz+GGG1IkaytqEf3PmU0dWMamghphW2qnmISs8Z8o9xAiA+mxdXZl1trwLKMPcpJ2gHMOEOJhHyxuwbolD9cFFpcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.6": {"name": "webpack-cli", "version": "1.4.6", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^3.8.1", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "21b8495d4ac1752838e8410ee1f8ad05e1acc15e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.6.tgz", "integrity": "sha512-wiVJQljxLITg8M8z8mtpFd1IxwA9aKkFukDzDvSuLH8WQ/FGma0lK2IDq8SZ/cGguE1WeLb3odhgpMxCLXR+ig==", "signatures": [{"sig": "MEUCIQCMWR+0tAMnNa8owG9Hk+XfWW/F82i1KYElqpNFELJMVwIgIAN+sndMkNgxxSaSnGErvKzIPaX/SvCweGnbCC9/B2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.7": {"name": "webpack-cli", "version": "1.4.7", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^4.0.0-alpha.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "945df8f7dc76dbf44371ef645edc3640f8595a9b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.7.tgz", "integrity": "sha512-NliRzH5GIM9kKo2Mx3omwPSi8MI1bH6mtgPsHJqdIRCyXyGc5AF7rr9sJMGjZeO7N0pVxnQADeWbTN7i/DeUsg==", "signatures": [{"sig": "MEUCIFPRR3pqLleczmlnSTfxKpdsrSO0CpSU+p60NmD+5XeGAiEAk+lVB56d5/5e82Kz5XL2xv/GvSpjnskNaOz1n0Pt+PM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.4.8": {"name": "webpack-cli", "version": "1.4.8", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^4.0.0-alpha.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.3.32", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "968864c7daad8c8e70e45e726855fa56be25f2b4", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.4.8.tgz", "integrity": "sha512-x/zHw8c5KIye44YT49s/P4TiBez18quklNi2fh83igxKyL9I5gOH3FX03eZjgLV/4ilz4OmBL6GsjrADgAELFQ==", "signatures": [{"sig": "MEUCIBrc+fu78tFPYCUYrLVS8n5cKUdczhHHuVLFDqo9qqI6AiEA/3D0LkSVaYwHwRDB8L6R/EuBrx6hb1jmRCY1jv7n94M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.5.0": {"name": "webpack-cli", "version": "1.5.0", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^4.0.0-alpha.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "1.1.2", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "970abf6d956ff3cc99872649c2709e40cf15ad6a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.5.0.tgz", "integrity": "sha512-uNF5vXASGOpt/9gRNreJ82GvveBCK6/bNta1YjiUxy7UWOWcEJ7/+hohGW5YrLRmbCCJuZIfFlWgD/9ZwRlSvg==", "signatures": [{"sig": "MEUCIQDnZEte9JwZGiqc1tFgredHfx6g2SAFS+c4FB7Qqz6zuAIgSwtGS0ykBzp5unY5EpNv8Fj0d4kJrPTAmh5nBkscRts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.5.1": {"name": "webpack-cli", "version": "1.5.1", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^4.0.0-alpha.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.3", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "7d4963e6f3098d5f5cac050e6c7019f4f1a04d93", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.5.1.tgz", "integrity": "sha512-69cAGnXISytSXOqClv/qU+8QSiDRjBxWoDS4lt81ri41vEjJsIgsUK/kyD3NUDc6fXDO4AXeA6N31ByM3xnFyQ==", "signatures": [{"sig": "MEYCIQD3uDY7mMyBwiNkfpA0E6I7O9qRO4ybBe6/wp3KB/hItgIhAMBbrGoT1I/gjYYhyhmxBmZ575ofUZod+UVsLV4htJIi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.5.2": {"name": "webpack-cli", "version": "1.5.2", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "webpack": "^4.0.0-alpha.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.4", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "37de75cfaa3be40043bfd73117b4b13143a92d54", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.5.2.tgz", "integrity": "sha512-+InMoA9EEN4zCB/PJiaorlas49OT258hulnphwqF0cTNHibFcatfgCGXigDw+2VSNBTEEKJSBwMCzKfN1MNxAg==", "signatures": [{"sig": "MEYCIQD5HQaj6hAlPRuljpWhXPT0ywL7TfQS9Pz6Q7WKy9IL3QIhAO+zV5iDQyMruYmx5ZYq/IO61Ia4DhCO/HENS7QmFKPT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "1.5.3": {"name": "webpack-cli", "version": "1.5.3", "dependencies": {"rx": "^4.1.0", "got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "git://github.com/kalcifer/recast.git#bug/allowbreak", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.4", "babel-code-frame": "^6.22.0", "enhanced-resolve": "^3.4.1", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"ajv": "^5.2.2", "jest": "^21.1.0", "husky": "^0.14.3", "eslint": "^4.2.0", "should": "^13.1.0", "webpack": "^4.0.0-alpha.1", "flow-bin": "^0.49.1", "jest-cli": "^21.1.0", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "ajv-keywords": "^2.1.0", "babel-eslint": "^7.2.3", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "prettier-eslint-cli": "^4.3.2", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "e49883b40249571ff79415196b3d76cab1f7272f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-1.5.3.tgz", "integrity": "sha512-TiI0Ad/G4dGObCPsnZVOcHAHetMs2g8XSgMlhFMTS2q2uDtO4NNp4dskfKqEIc3ttI1MkGMRGWQ/Vkbupu+3hQ==", "signatures": [{"sig": "MEUCIHK4yTFDUeL75CD+qX0ugqeyD5jrKD58OwSyDS079AF1AiEAkFHX6Qm2qtliuJnWJAmH95KgOXNjcVcShYbzl3e9iNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "2.0.0": {"name": "webpack-cli", "version": "2.0.0", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "babel-code-frame": "^6.22.0", "babel-preset-env": "^1.6.1", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-alpha.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "babel-eslint": "^7.2.3", "schema-utils": "^0.4.2", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "aca880d0a0762475e2056b7b5275a4c743b75da5", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.0.tgz", "integrity": "sha512-vuZcS/vRrwYJLLAxt/6K0+X2ZVL4FFqc5ynYEwi/zqDOZfXg04UqP5wHP8LM/I/+RVI3S70NSQYaNebyugfM+g==", "signatures": [{"sig": "MEYCIQDfeehw9277l3LFuzL266nQfmkF7q7sMEvTT5Dg17bQ5QIhAOt7atjqKAsBPADlIDpHRW/pcp0nFbE1PwsJrLo0SzDD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.11.5"}}, "2.0.1": {"name": "webpack-cli", "version": "2.0.1", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "babel-code-frame": "^6.22.0", "babel-preset-env": "^1.6.1", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-alpha.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "babel-eslint": "^7.2.3", "schema-utils": "^0.4.2", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "67b559c5c049fdba01ac12e1cd700850d88c4f15", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.1.tgz", "integrity": "sha512-c0wMF5twhKhBUVp9Ge1lVFv6x0yA3bPhbIeptheXgOYrchbuq8Z6FHuD2L+bGGR+yub3OM13n1F03VuzJRGtaw==", "signatures": [{"sig": "MEUCIQCDM1wYKQKutiwBzL4SAK/YB5d0X9vMSoRtexZ1erYY7QIgEiqqj66tqazN8LjDd/cg6YVgPcQVYkDAjXmtTVkjpWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.11.5"}}, "2.0.2": {"name": "webpack-cli", "version": "2.0.2", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "babel-core": "^6.25.0", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "babel-code-frame": "^6.22.0", "babel-preset-env": "^1.6.1", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "babel-preset-stage-3": "^6.24.1"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-alpha.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "babel-cli": "^6.24.1", "babel-jest": "^20.0.3", "lint-staged": "^4.1.3", "babel-eslint": "^7.2.3", "schema-utils": "^0.4.2", "babel-polyfill": "^6.23.0", "babel-preset-flow": "^6.23.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1", "eslint-plugin-flowtype": "^2.35.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "669c0984e8d3d743b59c9f759666e7fe7a25c736", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.2.tgz", "integrity": "sha512-zsYE3zMTnFTRH+a1fpCHXwbeInHv4fCNZvcYf1fb7kKklBAjYJHTzWYl2L1hTDVY4lB4Y3f9CLg8ZuZJuY+SAA==", "signatures": [{"sig": "MEUCIEwf+tQs5xzGGdeldkZFEOrCBDAqt/I6ZawEkS4cT8ZXAiEApFVA7AaUReLnYn1lu5oC3BjF67mOaqerXv5bjlv6wOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.11.5"}}, "2.0.3": {"name": "webpack-cli", "version": "2.0.3", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-alpha.3", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "9d053ee7bd1ac7d6679cef09de7695799eebb96f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.3.tgz", "integrity": "sha512-/WbB69zPXb1neIa/tR613HFfylFCC9IkyxrrFaO1G2IDe+X0MzQEUknZJyeSl1ZJaPsWccse8lIGNJIhgredfw==", "signatures": [{"sig": "MEUCIQCt909D+udkUdb7dyWULh2L1diANAdglh2A5PC0GwQFUwIgHY73QeKrKWid5sT+Fv2OaPaepk+7VRllZgonRxgA6fM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.11.5"}}, "2.0.4": {"name": "webpack-cli", "version": "2.0.4", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "^9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-alpha.3", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "44d8f706690b82582fc537a8f088d85eeb4b521e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.4.tgz", "integrity": "sha512-+R5UlCAP36RzuoclEVSikgM/C0aXp2deyC8BYHZnxfjsf1CjYWqSLToltoI5z00oAEFRjeucE6OZJwa0PA0Bzw==", "signatures": [{"sig": "MEUCIQDw+LFqTTE8NYeilnd8KUFiig+WCp9P+OyKJtIJY506mQIgHxNqQrpU/iW3dFCcZue7/PVXsGSaELwpwcnpW4KdT90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.11.5"}}, "2.0.5": {"name": "webpack-cli", "version": "2.0.5", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git+https://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-beta.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "cce0cc2d078228eb23328fedd2a6664e3532a8dc", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.5.tgz", "fileCount": 104, "integrity": "sha512-G8dMbVFN60M6Au2PCJwKpvC7NFbhiCOc8393KmxqU0iE+w++/JRwVWfob+6aAT2585Qe13KcJJbrJYq6MPsQ5A==", "signatures": [{"sig": "MEQCIEG3i/9XZdl2q655B6wcpZv7N+cb4a8MiepfDTSwNQt5AiBzjF0GIrw6oXBThboTI7GcRe0CgDGeSYZjksP6JTsSXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488256}, "engines": {"node": ">=6.11.5"}}, "2.0.6": {"name": "webpack-cli", "version": "2.0.6", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git+https://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-beta.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "f0939f8a226a5cd2027a3e1f432e12017eda8298", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.6.tgz", "fileCount": 104, "integrity": "sha512-f+oqwAna5I/12NtccVcyqS+EXW4bCm08lNle8MDdxpDamkqeT4Z0yAUZE6O7/vXYLOWwkbHelze9+BO/5RSAiA==", "signatures": [{"sig": "MEQCIBV60qIMmOWsTZXY4xwCuyLWv+cZub0wARiW0aD0PpWOAiBC5V28S9ikmORs3GcLs2Ejt8U2zyqex7iVydce10AAJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488257}, "engines": {"node": ">=6.11.5"}}, "2.0.7": {"name": "webpack-cli", "version": "2.0.7", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-generator": "git+https://github.com/ev1stensberg/generator.git#Feature-getArgument", "yeoman-environment": "^2.0.0", "uglifyjs-webpack-plugin": "^1.2.2"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-beta.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "@commitlint/cli": "^6.1.0", "cz-customizable": "^5.2.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1", "@commitlint/prompt-cli": "^6.1.0", "@commitlint/travis-cli": "^6.1.0", "conventional-changelog-cli": "^1.3.13", "conventional-changelog-lint-config-cz": "^0.3.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "d2cb12c322de47ac022c46d6813b711926663b0c", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.7.tgz", "fileCount": 298, "integrity": "sha512-US6bO3dcDtkLahBSIMQBzkHKZT8YDImwTSTkk66SAMX118sBuDU+v2MlaSd5VmJMYJdpEsb2LHwMVbfKVZfJPw==", "signatures": [{"sig": "MEQCIElHr5odg5rcfvGhtthuZlRz1FaWZYBRhI4dY8l3wfG0AiBWDvoc0kcWV1Cor4XvluG2hVUrWexs6tRlLGgbB1/q6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8650681}, "engines": {"node": ">=6.11.5"}}, "2.0.8": {"name": "webpack-cli", "version": "2.0.8", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-environment": "^2.0.0", "uglifyjs-webpack-plugin": "^1.2.2", "webpack-fork-yeoman-generator": "^1.1.1"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-beta.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "@commitlint/cli": "^6.1.0", "cz-customizable": "^5.2.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1", "@commitlint/prompt-cli": "^6.1.0", "@commitlint/travis-cli": "^6.1.0", "conventional-changelog-cli": "^1.3.13", "conventional-changelog-lint-config-cz": "^0.3.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "c6220c1429d0901e1dba36f81f7e44c642d317d0", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.8.tgz", "fileCount": 288, "integrity": "sha512-Uu9IRuFUpRYe38KG/6DGp52Dbl2NB2Igr/iGvhdj3FFCKWUNLpmzIFUoAkDW/7HYV16291qt1rPhq6tb1AwFGA==", "signatures": [{"sig": "MEQCIGat+61i0j736p4eT9qF0+3JZYjBnzeZ7KLufiQCvAmRAiBv3KZCx8Mr7xMtNOJIV5xHk4qRlYKUQh3wE5f8AYmu7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8185345}, "engines": {"node": ">=6.11.5"}}, "2.0.9": {"name": "webpack-cli", "version": "2.0.9", "dependencies": {"got": "^7.1.0", "diff": "^3.3.0", "chalk": "^2.0.1", "listr": "^0.12.0", "yargs": "9.0.1", "global": "^4.3.2", "lodash": "^4.17.4", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.13.0", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^3.2.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^5.1.0", "jscodeshift": "^0.4.0", "log-symbols": "2.1.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^4.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^3.4.1", "v8-compile-cache": "^1.1.0", "yeoman-environment": "^2.0.0", "uglifyjs-webpack-plugin": "^1.2.2", "webpack-fork-yeoman-generator": "^1.1.1"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^21.2.1", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.2.0", "webpack": "^4.0.0-beta.1", "flow-bin": "^0.49.1", "jest-cli": "^21.2.1", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^4.1.3", "schema-utils": "^0.4.2", "@commitlint/cli": "^6.1.0", "cz-customizable": "^5.2.0", "flow-remove-types": "^1.2.1", "eslint-plugin-node": "^5.1.0", "webpack-dev-server": "^2.9.7", "prettier-eslint-cli": "^4.6.1", "@commitlint/prompt-cli": "^6.1.0", "@commitlint/travis-cli": "^6.1.0", "conventional-changelog-cli": "^1.3.13", "conventional-changelog-lint-config-cz": "^0.3.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "0310fa04f4cad69714560cc0e4da5c7682eb4d9b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.9.tgz", "fileCount": 298, "integrity": "sha512-KIkOFHhrq8W7ovg5u8M7Xbduzr1aQ1Ch1aGGY0TvL5neO81T6/aCZ/NeG7R92UaXIF/BK4KCkla35wtoOoxyDQ==", "signatures": [{"sig": "MEQCIDEnKzNohU3l6HPkm1O4sppyjynAeIijn2/GGTiLA/DnAiBP8KOnLlO9ggv+PLE+isD5TIFJSB1SeFB2o8D4f/zvQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8717039}, "engines": {"node": ">=6.11.5"}}, "2.0.10": {"name": "webpack-cli", "version": "2.0.10", "dependencies": {"got": "^8.2.0", "diff": "^3.3.0", "chalk": "^2.3.1", "listr": "^0.13.0", "yargs": "9.0.1", "global": "^4.3.2", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "recast": "^0.14.4", "codecov": "^3.0.0", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.4", "jscodeshift": "^0.4.1", "log-symbols": "2.2.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.2.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "github:ev1stensberg/generator#Feature-getArgument", "yeoman-environment": "^2.0.0", "uglifyjs-webpack-plugin": "^1.2.2"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^22.4.2", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.18.1", "webpack": "^4.0.1", "jest-cli": "^22.4.2", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.2", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.2", "@commitlint/travis-cli": "^6.1.2", "conventional-changelog-cli": "^1.3.15", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0-beta.1"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "09b888fbaa0b4288ba4b94c4462b6f559dfcf51e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.10.tgz", "fileCount": 229, "integrity": "sha512-PQWEOoXkhjBV4svPuESghZRc80VvDoSSRPaLiInWifDlRJgoPWpiLCFXyMLQTTaug7ApLrSEW7BcuwwY6DEv5w==", "signatures": [{"sig": "MEUCIQCX3hJO08TXnpSOzQ4cm04BQaUU5nWH1N25v8eIhtyN6gIgAxR8MzdYRDmdwfkcCkOsG8lhnoD6K33nsPDCm04EO8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9087892}, "engines": {"node": ">=6.11.5"}}, "2.0.11": {"name": "webpack-cli", "version": "2.0.11", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.0.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.3", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^22.4.2", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.18.2", "codecov": "^3.0.0", "webpack": "^4.1.1", "jest-cli": "^22.4.2", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.16", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "9493fad75fe948a80cfa38c72e56d2f812a42145", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.11.tgz", "fileCount": 104, "integrity": "sha512-+WwfDmsHB+V3OcbtfbIbKM12dDBysNwwMNulYjVSf/UIUhdMP7ZL4EmvCbgwBl0Xlf5hSELOkvS7sKwVDbn7TQ==", "signatures": [{"sig": "MEUCIQCg8G7t+Ua6IS1x7uKwydP2CvL6+Hw1W6nTfkNKbkaNJwIgI274CfJvJBztq9LBD+CWVSV96sVz8Sb4KO4HiyOMHes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 272865}, "engines": {"node": ">=6.11.5"}}, "2.0.12": {"name": "webpack-cli", "version": "2.0.12", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.0.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.3", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^22.4.2", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.18.2", "codecov": "^3.0.0", "webpack": "^4.1.1", "jest-cli": "^22.4.2", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.16", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "64db876d044f03d8d6544281854b71a3a3c77dd3", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.12.tgz", "fileCount": 172, "integrity": "sha512-kMi6NquWwUhmQok2IFrtAEIbaVvujzYvtDGb5WElkwylbLboDsCgizv8IjSi/Q6SQRJ8Crayl1JCBnIJ3rU4Rg==", "signatures": [{"sig": "MEYCIQD7i+CraEywBFwRrU18M3o8X8o3hdk/Kb9fQh8uLGaC6wIhAN/nFyDEXBus2yjAtmUlIH/IK5EZrlaXrPVkzjBskHC1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5318465}, "engines": {"node": ">=6.11.5"}}, "2.0.13": {"name": "webpack-cli", "version": "2.0.13", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.0.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "resolve-cwd": "^2.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.3", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.4.1", "jest": "^22.4.2", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.18.2", "codecov": "^3.0.0", "webpack": "^4.1.1", "jest-cli": "^22.4.2", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.16", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "6e2bd9ef91345344737217e22e29001ad8537518", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.13.tgz", "fileCount": 261, "integrity": "sha512-0lnOi3yla8FsZVuMsbfnNRB/8DlfuDugKdekC+4ykydZG0+UOidMi5J5LLWN4c0VJ8PqC19yMXXkYyCq78OuqA==", "signatures": [{"sig": "MEUCIQDIMMKu8UKlbrBGqjTLitmBDv3ZizC3r4741YYz5Cjz4wIgRabd8OYJ8YGmfZV85c8Y7I1ALya3SM5aQdGHJyLMTGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356510}, "engines": {"node": ">=6.11.5"}}, "2.0.14": {"name": "webpack-cli", "version": "2.0.14", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.1.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^4.4.2", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.3", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "71d03d8c10547c1dfd674f71ff3b0457c33a74cd", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.14.tgz", "fileCount": 262, "integrity": "sha512-gRoWaxSi2JWiYsn1QgOTb6ENwIeSvN1YExZ+kJ0STsTZK7bWPElW+BBBv1UnTbvcPC3v7E17mK8hlFX8DOYSGw==", "signatures": [{"sig": "MEUCIQCeThO1dRhHKVulJA+bZ9QzWCSmmvEL2BOKzayHcHmjoAIgKaLXNxiNUnxKJgGEFm+yd6bUT0LaIYWl82ZadV4paXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358006}, "engines": {"node": ">=6.11.5"}}, "2.0.15": {"name": "webpack-cli", "version": "2.0.15", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.1.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^4.4.2", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.3", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "7532066556b03bd3292285ac08537e28844616c2", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.0.15.tgz", "fileCount": 289, "integrity": "sha512-bjNeIUO51D4OsmZ5ufzcpzVoacjxfWNfeBZKYL3jc+EMfCME3TyfdCPSUoKiOnebQChfupQuIRpAnx7L4l3Hew==", "signatures": [{"sig": "MEYCIQDLtPVovhGPplq3nd4j02O5N70v+VRSJYNDgLsEurWLPQIhAJ7AEyTxbNSXmfjujjUEo/fHmeUfmlkcVkzd99zgvzFx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2toZCRA9TVsSAnZWagAA0J0QAI9NxZcyNh9q0Sy+2mg3\n+Dxrsny+Sc6dIBFofJiI33GBXi3JNhr5m6qgkx+wVWFBVZHkJuKhsdHJ49mF\nUj+yN84aQ/nE9/WdH+zSShkKXK5LgkEMQTi3ewWqRsZg5rVWnKWfjCzS0c9E\nl8S//lgrGAfl6ivdy98/EnBqGsyxSOCbKSiBF4NjVjBliEQ47Clnihil7OdM\nHgjrTJYniW8akKQamVFJ5vZC99QvCMxOzEyk4K8+UwTThl9Vp1NxqaUEDcsQ\nisMpOlWo3lWTxHAImdW4rQuLy/M49kNREVSH8uG6TzYnHc+vQXBCzgBjHzUe\nICmDdqwFlhjQtDZlIHwNuc2UkYEAkREukpEp/In+bI6fP7dWZm9y2fo8ds2V\nb3QjQCCwb/ATmJIl9xCV10hCwPRegQRxBJgv52eCbVbYfEmKU6g91KJWZihL\nOm5UmH3PljOPm01wpPzWvC5YyKS4wHrwtPOfEqkSi8JwGmyVJ3XQxATP9MaD\nQHZMvJAtI/CpR7dblEbPVsB1j54JvcPZPQf3cca7T8wEa/DnlKgEoV2XySsw\njoSRkqzBnOcs2/L0F42quAupBAnaZG2Y65fYZtT8YTEENOJVPQ/vlKYVek0f\n8wTy2Kvwyzkx+vBzX3+OEEkXUbcQ6ABiWmAqKGL98zaQVu/o7xUgRIRGzcxP\nY4PR\r\n=Bw6Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "2.1.0": {"name": "webpack-cli", "version": "2.1.0", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.1.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^4.4.2", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.4", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "b2b63feaf209a0d177acf50f3a6415db1d28a61d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.1.0.tgz", "fileCount": 176, "integrity": "sha512-lMfE/A+kgHEuK5Y8aB0hvDY3j6+FyMeM7sadmzaAI92HChr8Ba97VQzyXK4Xyo/lnkv6KWkdDAEhAnOATQshSA==", "signatures": [{"sig": "MEUCIQCGBcrHJ+fic8YdVEsLSZRRHOJ9obvV6JrKun/zNwCzYgIgJ5WrcGjnGSsk6vaEsNgBDNV9bngSB991bstkzi8FYXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5trlCRA9TVsSAnZWagAAOugQAIl/sMsj0rKG40048K5z\nn7sc+8k1sslKBeWprp+g6E3NkM8oOl7NVCMOE6yVXWd162zOP4reDDf5Yfao\nPMuG7MJIupV7YELTZc7+x8pTjes2uXuJRQoemsvsXCunGZ5cZ+fUI3FXpcu0\njunCW2NNtiRnI7xUmEzexxs7T3n6N02hW08F5eMcUCp0CdpB9om7ppdDZUsk\njHOm5xa5DIJEwy5z+4iqMt8OWo6TZZYyfmqJ9HMuqClrKXjeP+ALL9q2y/XG\n8hMl1ktgM7CgaaDKSndHXXntBl1LrWq0DJyJgKNgv33Z+NhaZhKZgn1El0sW\n+33ZyV+fiEAa9tH/ofJZvpSpN1AiHZEWDqB5Nmu1ST6JK4LGv775y7CjMufH\nv/ym2a3LPs0HPT8ZlMXYNB2QRtzMGAqgz/ILrHNjAZsOiTNLJJWIF4H5NHq9\nkj3209qGMx3WGDYD0IbJ03ARmefmJEWfKv6XTp0BS4CABZEKwgJW8OQEyfLs\nxBZroYUgvtuPjAQbsBZTpLhWO0S6QlufKkemeQhtFiQKvdkUULaq8HwFHn4M\npOde7C5r0rUY8eeUV78qdOYy/B+vA56AdJk0qIEIBX+GK8oo09I5Kqm7OWbl\nTWJbxuO4Tc4eG0xYucuMicw3Xxg11obGyt9EuEr13XcAEgIQHGAUaYkpF0GL\nv4Y3\r\n=0o2a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "2.1.1": {"name": "webpack-cli", "version": "2.1.1", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.1.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^4.4.2", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.4", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "e2fa01d1e4f3aea7b7a7541101706327d5c42657", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.1.1.tgz", "fileCount": 176, "integrity": "sha512-zoUeCfRkNnME0JdYNTnmmxRdJIcmFfCGdnUlHSLiS9U0E5CCp4MY31ESB3ivOmZ3ewZ18WofH6IRNmU5Oae56g==", "signatures": [{"sig": "MEYCIQDzEtjJO7VUItMlV51n9tIMDdQQOzR6Gji27+Vij+s52gIhANRQ7BvHHIkBtEm0chAGVVnmTDkfBTO4ly3wEinTEpSx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5vv8CRA9TVsSAnZWagAAKc8P/3hMdDQUINKquNSSCuar\nTsbEVsMHb9i0D3uweiy6oL2YGjWeAfDnzfoUPS5GjqRgNfF5wX2ClYfWFg2b\nLJ1yHJGSAW/fjWA6uNW7jq3QVnIcsKP4pCPFR6RA9kcZYZqERxgBEv0naBak\nNRBVplWGz/4Sxiy/N21JV+cjRRLG2V/pJcDvpR4n8kwMVUDTQ0El+ZJTuk8M\nzzsVxvnYmbTUkn3AHG/w1WZX9GG4KsQlAP/AQe9SjjyT557NUvXmZ1/QUQKd\nCcKqXYETvdE5gAY2IMKhcs0+3XY1k7bTaHNgyMUZGsfTX5s2VFvItOxtVTLt\nFFXj1TVkGb1+HjUXo0KL24FVNqNcjEYaeyGTz59X9uUBJ+itfiwmVqaywLNH\nCx4aq5JviPCqYL+Py6g4tQQJV+x/PEMLsFGe/t2phm6gABcLhqXcYBQpUZ9u\nJmUyDa55+r7HwRa5Vkg1JqVxlxvbKxLI/Ok6eSzKVq/owltTu3V5J5nYXrAJ\nIJNlD/grTz9Xcjm8sNBVND8vqYvg5f1+5X7iYbaPx5AzJsWdAMr4CvdBV7Jv\nZ9wp+LR1Fa01nRcELqTpSzjKBmwtRsznxNz43+LbUtBPXQgdIQs52qCFn2L1\ngytglLhiuMEBykgroU0tm3W/KT7NV+ziQ12Bm5ih9oAclDasgVe53t2u1TZL\nPxTe\r\n=Gtx4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "2.1.2": {"name": "webpack-cli", "version": "2.1.2", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.1.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^4.4.2", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.4", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "9c9a4b90584f7b8acaf591238ef0667e04c817f6", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.1.2.tgz", "fileCount": 175, "integrity": "sha512-2C6bs9gORlzCSgkNZTnj8hnXMxe3g2v+yqiUdB+1l/I3sI36ND4zZStV00yq0eGjE5CNu0eqOQr7YYe+42H2Yw==", "signatures": [{"sig": "MEQCIAjje/OAiL47QUDAdMcLHnUv4UKz2fOJxZkRwo/R8mQsAiATDHm2JO5aygJC3mwVIQp1er9Uvgfwa8kJM5Cu7rRUsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5wGgCRA9TVsSAnZWagAAZ7gP/1kGLVx77cN+D6ksByCI\nQtlvDrY2HQ4KiZYjP+OEjFr+IyVDskpySfnp0NG5IdB1c2iJT8+YxLDzXXPe\n4sxsHSBl47iUIzCifqX/fkHZR7Bd3n5X6Z1z1FnleTwNQhgykqcVRcX4spBX\nGMAxztFyL3nB3Vot85v1RiqqdJpu+QYj/sQkewIFeMONecQdnn3OMZB/YTik\nnxnO7+I+P8md/rZTUrWwo2zpdD0fu4IDWcRKD4+51ZACf7RF60KAtJSZYmwQ\nAjLMOBSk7yiJf2XZK8BIHqTCITbxAo8ogkFUP9qaC3vJgkOKeWAI7i3fk90w\ntVsstcMml4CuPradkR9IUef80yonyxwkv+NlNXTj4ZW+68BH9c1pplIRzVFM\nzhy5pxoIp3puSpSei2v/F76odysuIh3X6h55ioUoFUAJpQFi4ZPY8Nm1urRd\neqD/ubdz5t0OlzvwZSujWWssZl8/jXSKCps11T5vPeNoo7NApdu21wJZnps9\nWpM8UOoLKhlWKHLHUpXJSXITe8w1nNZQ5W0R8Ml54q/a4dHQILpM2ElyNIdi\nAs52ll5xl9Yp3AI0yD2LoyUYQ+N+l7/Ry//CplhwqLi7AzIe8P2YKv8wvCL8\nf5ZrVcFe4hnRcTYYQLZHIaRvEXUSCJ/jsrUgB12ZD0c3wX7YPZd+pVa0rp9Y\nBj/3\r\n=ufuW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "2.1.3": {"name": "webpack-cli", "version": "2.1.3", "dependencies": {"got": "^8.2.0", "diff": "^3.5.0", "chalk": "^2.3.2", "listr": "^0.13.0", "yargs": "^11.1.0", "lodash": "^4.17.5", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^4.4.2", "glob-all": "^3.1.0", "inquirer": "^5.1.0", "prettier": "^1.5.3", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.3.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^1.1.2", "yeoman-generator": "^2.0.4", "yeoman-environment": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.16.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "65d166851abaa56067ef3f716b02a97ba6bbe84d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.1.3.tgz", "fileCount": 175, "integrity": "sha512-5AsKoL/Ccn8iTrwk3uErdyhetGH+c7VRQ7Itim2GL0IhBRq5rtojVDk00buMRmFmBpw1RvHXq97Gup965LbozA==", "signatures": [{"sig": "MEQCIHn+JLWz/2TSE1DB2E3lTkpmDuiBI+H/ZGTMAP/FB6D9AiBCCyxMaRtLJJKLRsMRVsHzia2N1WtpVSjNY25Huu2/7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 262527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7x0QCRA9TVsSAnZWagAAL4IP/01f4d8MS1mSwv5KnREl\niRTQv/D3VlTa3gk6LJbtzcuIh5qLwpE8FntUOmofNMNrevivM5ZxRT+r6Y1x\n7dRnrWXYvc5M7XkNWh+0OTqSvRx/Gj/CS0h6enqKLf4vAg9jfqPWcjj4YhiS\nAFbrnF4B8uw/OpNNJ/+fn7nDlCn5mkhqsmlwxbF0+FIZPFuFOzcnjOcNPITN\n3k4qyAJIvpcKAc9ZDHrHHlQemcS4YzxlPcy4F/D4OfYa3JZZQxb1lr3vxPa1\n2h3xJZLlpweS2HNdmMZkWHnzfAXKZj8sJ3rDsHDzpoET4x6k9KopMFsBiOid\nH+/FUFFlIGIFdMb9y/FLr19kBoFh2saTRvzhvB2zJCGVrnv5AdMVBHCsIFYi\nItQGS8EdSHsh6/INg57RDc1YNa70PAwvwKeRPUgXZ5i/nQ4jIkNXNo1H2gBe\nD1VrCsBHPtDkfqMoznMhQf2tJId1eePUrz7SyFYY7xNxOJ74W1W4LVk+gleP\nQ30RI2N9b+6Pi3XIpK1XLlDlq9UsybRpN3waYZfXXDrGefHf6A9q/euShCcf\nGX0YZOFBfxwKZe230RffHB0aw3/s4BB3J4T4W98NYCf41ekgeFDHaKWpSnf6\ne63FJ5Rocr0OkThsLwGH1guE5wQtyiNqHWNEILwDhLjgkNOhwX+uomxhdUh8\nih7i\r\n=rQQf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.0-development": {"name": "webpack-cli", "version": "0.0.0-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "d1fa51924576ac46c02edd3c0341cd3807b6242b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.0-development.tgz", "fileCount": 13, "integrity": "sha512-Zhej8dXKw6iZwA/7g7fv/Zlij3a3SG2WDfrSsrIKTyABKasCELOtvzt4zh6pdKr+N2RKotOwucQDyUiAJNjo/Q==", "signatures": [{"sig": "MEUCIEqF4SywfyRC69xkNs7Zdpxx+sGskLeuvxk7T4v/39lHAiEA80kJ3TphE8pwVWzOql6yFYCknufpbcjRKzKt4bEG73Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/YltCRA9TVsSAnZWagAAhkkQAJRzgjrV6lDNDkA+GsXi\n6DqJLYpCR7i+Mb3mMyOEmoj8A0E2LRWh74HfoWflVAkmpM8LRO+oy8N8kxDB\nhr9c2au5LSbFpym35J3yR23nnZ8IkMSYqp095V9gEPYi343+EU4Fv/ismqZl\nz1jH6YakseAYu77+7V1AgiusPWrooOXEuJ3/QIaVZ/S6g7k3qSftBf8Gjk5c\nR4Wx6AqLwoQTzuiHiERFB64GwDUZ92krElUDxNQwKxEXsrZpPFFVGPskYJzW\nE053rw6oa65nAkIEZQsfXvpk7NSPO99MrClgzRnNmFGnsVEMwOZ2K4RjhrJm\n8ubWt4cz7952z+A/w4+I0+dyoDL/LFKGLLQ4FOAZP3fxQgQeIBHHtbcuD75C\nSlQlC26T7q5C+AoKhavHc5XOW6iI28UJ4elnd+8s4SjwaeKzFWR0/z9HswP9\nQHqD60Wj/cQDmyQ5gVt+H1kxSVyFy3H3tlec3ZRVePjrK3Hnc9/chPHfFT27\n+SH28se0QKkibnFtVgSgHVxaQhfDaM21AfLfk5+z8L+90J684U3TKzHGiweO\nVk63Wjiwz/DqeGXH56Padcj0cg+MLUsbWRdpCWdu/2P88HE5RX5UdyX5iI2v\nNjb/X1N0gyJAVtCVBoUCTsHT6nhQFvDmfasgHhcCNWUuQrS/2KaQq9mrFpfB\n5gsB\r\n=JqgA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.1-development": {"name": "webpack-cli", "version": "0.0.1-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "574ee17ff3440338b08fe8e42be9bc01e079dcc1", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.1-development.tgz", "fileCount": 13, "integrity": "sha512-Ax0Ek9xcTrPY6L27a44hY1iTaYlYuzRTkxB9/hXbdIX+XgT/YGMrw90E7A76RDVARhljMcRXSNWr0s/Ru8WrdA==", "signatures": [{"sig": "MEUCIAQvNBdb5rOLy0PhUiKzCmrt+KAJCjS627+SVebLpZc7AiEAp6sgsivBqfO4g5ANXujXp92yyREUSNae5uVAiGK3VLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/Y1/CRA9TVsSAnZWagAAfH8QAKCQw9UCZJU0K3VQTMWN\nPlD9TpGIr7jktmgC+eBQ+x/4UCO2qqDMlUWJ8yn5e5npYCKN/B4ZxzRCJtig\na2ERokgBIcj/qWE23okcGJOE/HYNiuU1738vaHVn+J/N5rPvHX/OKgoPnliy\nEVHXJo1b32NJyhG6ZeccK5rOnzhrQH/1ONrxkHwObxy+oPYiVYlQT1nuF9mA\nIgrlfQeUvMaQ6OLTFFFgI7uXFS6shjN42VqoksDqvIoEUYEoPXgHgl69xH2Q\nC3iCdVm7etXyeLptTx07J4PZCtz5PtUgsTDzpAHQhpD4e+rEDlpVgpTLgY7M\nEL9V/9LEUNNcHxJn/wE9H4dL4yg/NDjm70ltJhJF8hjsR4kPY+frw/ivSniq\n1uJuMr7eeO3KbGhG02r/c0R4K3q0LX1hM9XfNAlPpTpfNr/FavFFhzMH2D1t\ntPadB+7Rj+3WuTK8HZih2mUgmNB302qGKADyYDKWRqEM3BYSjjh2XKZPO9+a\naVCkz0xa9LhJoAiwAyRal7sZq8zwrrYlx1hZXnROzGBoSlqZ1J+pa+XDA0A3\nhtIZ4x24bJyl3a8xa97aOGCzjyUjWBsSh3RWtQigE3w0wuGb1ID9sDVQrSF0\nGBeSxS5Zx/oQ3sVaEqudtIaIdabg0xT0b+7nPYeB1E5+8uv5HX6FvIv3EsyM\nnEFT\r\n=6kqz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.2-development": {"name": "webpack-cli", "version": "0.0.2-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "c3e1308a8914ac4957d511c27a5ab86d5bab5af3", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.2-development.tgz", "fileCount": 13, "integrity": "sha512-qn2Cim8xDcIoJsGaB5pWtCp4XI1zTFzrQz/0AaaeTT/yMdJUFBMMvJHqGqgSM4HDF0Qfb9vjnSBLlNMUA0iqOA==", "signatures": [{"sig": "MEUCIHsvez48uXooKKsslKulgF0BwwhvdZ0kVvgNuAqvH8LdAiEAm05MqKbRYqKl4liwPb6XyMWNPP3GvbNP+9T4i7Ez8CU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/Y46CRA9TVsSAnZWagAA3BwP+wcKg4KENn686SzfoT0Y\n86EGI978KT15PW6WNxdyuL/IKkyRpU9OX9RbiPLYVBiYwybzK+RZ4xjMBIN6\nzu9ac/LrTG1fvxzPK+e7GQN6zI3aqn6AWkQ2NkMINmUYfmRZSCbL90k8hGgJ\nqWv49FdUcNrZ2Fs4NrCy1AqHevaz8cMhfz2NSWyTposc7WNbuxi7xdliUMx3\npnwO172BRvervgp6BwMMkZy4pVStQqvPRF9ckpOJ/y/h3Ldqmg0iBU6bfb2g\nclPuL7URp1q3SZK4q9Qe02znBenFvU9ZJgkxbRx08WckZv1qlcHK35+zbKAT\nbtGToSgpVpvxNpvNIGeGDGltFpg3WGsd1tGwr+hn5iF51BAH/JV3a0xH0wWF\nnnv/z7zc1unbSDWO9h7KGQKaf/UuNKYnEcq7B3G3acPtpld8s3eO/G0Aa0vW\n3/NxMwRyhfwmtD6ZE8fnWhoSEkV551QgW+3BmX08aWw27Vgwd0xkzpnC+pac\nGFgg4rUaNRN0W4burgY2Gu/TJHW2cJR3bg8Dawl/DXKqBPDuGJyORx1YuxSZ\n726UHtr6/tJp5g8rykrorQYSXEZ07Eb8jZ1Ah9RQUorczrrLc/NbpJoI+HZX\ngRlD3bHgy2Y9uZOqCJTyuRf4HmDx1vqXIRss/Bs/mcV31gSjLNKETR1rfsN3\nEXHK\r\n=uoCQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.3-development": {"name": "webpack-cli", "version": "0.0.3-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "5662d062a2b54a912805bf42812fdce7afd7265d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.3-development.tgz", "fileCount": 13, "integrity": "sha512-WF67InMvvruOoQ9sm5qrAPsJ4a5QSR65mOax9+Jp6W5PDewB/1EcXXGdjtMdkJC4mmhRCu5UpevfUcgjPaol3A==", "signatures": [{"sig": "MEQCIFslM7xZV/UizzwgXEGkcTORPbVXVqAf4fkYlvlFoMvVAiAKke6MIxwRIdh4ga6XK/L7Ru/YFoS9JsF+JqjV/JfBkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/ZM/CRA9TVsSAnZWagAAhvEQAJHtP/gKQsAKwTATnGYT\n1lacXW30CYB1hQvqWiDbPeXzW2XG+b5OO4RCDUxT8fKeeNMym5a5E7Q/bveF\nosCfAqpgWSHE0qDJn11C3EX6a9nMUF98dZkPMHgZPoFhPpF71suINnye6sSo\nyGa2rXG6vS7reO1HBW950snzHAlaew8RcICn4wDmvp/iG3mi3PsRUKRQkc2y\n1yq8WFuXCI0QlJMLo2XOq8N70kBtyKAdIf8S5+6tbhG3x1G6vw10h9t11Int\nT2EPhGV5zymxoIGCuGw210GgxsRHuJ+oodqUYwMFA5ayfzfmcBmIAHayvKDQ\nKQGQqsLz4lymD9GmSJSOs4lRge7+fNNTxul5qIZ9IgdiAEMbClZ3Cz/K73kq\nd3ZOCm3kCM5fUtrTRQ81hOeKYC/wz1EmVkSejcTJbZmmmBHWdPiH8MliOZ1/\n5CURlXgeCmYhLqjYhltICgvoJ0iZFES3lqNfcCNryO1d/Wgf1fVgxhjZ/bJn\ncHtLWZO9H/BopYE6QzP6ja2z9Hz2oJQkOuJqFBpYezdFn4rABEb7opFmUL8h\n3f896RUVvIJsM9YGxD7FWAMYIFqDfPbhQaCzwItl05vFPhcNjoCFNDplCPo8\nGiITEQmmqMLYJirFYekg+fnDuy8fNBXnoUx0wVgksFwlIRSMNARzASrFbMAV\nVFpk\r\n=kZ2F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.4-development": {"name": "webpack-cli", "version": "0.0.4-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "9e3ccc6d39831294f88a9b290ff842dc08ba02e9", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.4-development.tgz", "fileCount": 13, "integrity": "sha512-rEUSBe/4Enn6oS94Kxex7ImIsG60Vwzb/P6WVLGD2DALgoHLBJBU7EbdHD5lFVKe4X8x0Mo61nQSs6Og0U9R0Q==", "signatures": [{"sig": "MEUCIQD7GYkvdfhVVJF08IvPfTxMHxvu3f5zuyNL1zcassN9cgIgZiAa6xoM7WIXCYU3hSj1R1X6BG+kNsnDP/8JZUmP0l8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/ZSkCRA9TVsSAnZWagAABe8P/Rvgo5G1s1jYN/Bs2BJL\n3mq6hM57C2in1ZhWk+UtTRvgsSjiuQ0MW++4aiEef4nAfaxq1D/FBy+qEv9m\n4I50ovrUxv5g5S4AqQSt2qBPSoej0a3Ly9uVuwZlq2mu+3nsXywq6jWEs1De\nJFPdNe2WJx/AJNwGQfNzePgY8kj0gnmKUFlXYH+g3MGaLjqWbLJRqu3ZS3tN\nI+ueQMy7/Gaf9tJnl+p+nFgqjX6d9DefeCIVpjfDrRNlq5FeYdpWLlvoVsSf\nCzWsCd/umH+6wEHFi+Dgbt6vaORija3RFDpKVmcdX7XOVlAoohBhQKM/G9C/\nNZiHXkUTzal1ZittZpvxXHPRnLiw0kkmcDXw1w+62Vz1Or98TSgJEtYEkbSM\nv+u5hYzhVt3hNIgCLhfJaKU4sqME0Ir1ngjtqupnja7iuUrBSzARZtLlmkRk\n7f6Eqjz/6LGF7NNn9RFnxbAwr0W5h55E+I9wx8XFrbsk0wABb+pF8isH0LaN\ncnL362JNeJo1GESFWXVPGqCJs6u6fXuhq37yRyKkU43v6LmyJr0j/q82m4XQ\nB0WUxH/lkP/WHpfn0Fzh/VXYPaG7gjZ34QWNdW8IS80hNwxQjhsnwoFOShYJ\nQPPSTWKhtgCEuwMIp7UmO4mOQ8aVlRPI1Nr/aNmRDFP603RWoIscKf7YVFU9\n1Bux\r\n=PBZp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.5-development": {"name": "webpack-cli", "version": "0.0.5-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "2ab49f6558d845027d1ceeefdedba2c5839575a7", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.5-development.tgz", "fileCount": 13, "integrity": "sha512-OTV3sbzhSX0HKCGBlvlphhuDi6L7RY1RsX+fQFVYWB+mpbpqx+a5rej2tjU3hPw3PRnxCBImS1o86ub97iho+A==", "signatures": [{"sig": "MEQCIHlzKxHDcOCqQsYYnVopmV35v49A3bBCWo3Si8K/JdLNAiAu2CE+c/Nh7NcW61/nHCmAvk78pTeo/SU2UYXltbJkOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/Zo8CRA9TVsSAnZWagAApCIP/0e4hqFGAUD9ohowKfzG\nhyVgXOouyG5Y6HdypW8z0I01NEfQXIXKsxCIKpOsXGA/Q0gjUSxcNXBsIIc5\nZkaY1i1D7E/efTVloK1U2q60Sr0dxNLFebqViBbvXtnHAwXgONhSqn2q0edn\njF6FLDdPKM1JQg2PrdwKK1tbT9mivUmHzgBWmUp/R55bxSoo0/c9VUYBhcxr\n4wlvXxooytiIl3KrbU2sKVAbEjHYFT3JhPNxx06jeLn9RbUEi1yKgA4wYzcr\nJzDboT6eItGwhI2wuSxPc6ZJYwqcgqiyDdGX5xf1fAHeuEW3AXhxypyRtcu5\nQoL58w5kyEoFvTbAdI88Ktib1Ji1A9g/1h6xCERsocQPVhca51t1mUowrMs6\naAFXvzxuedbtnsznT+WSleilBq71JTURwcZJmwNy3PUzTKnOqNukrWq52s/B\nqAN4j8086h7H/7zRWZ0gsEX4ErqAYx+IyieRQhCx1/Gpm9jbo3LDJDWbI/RM\neP844toqGXBPgYTIBitUCdQ0TITaxEn0KfJuXZSc8r6Al3OuzLlLsXl5GY44\nSlXIzaRlqYMaPi//16fCnKobyX8+DJ/mcZHp9npAHf5b/hRimZXgdZ96Gz6r\n/B/uiCHYgyhLlHkyqyyri8ro+DpymNxuWTgtaPFdlLaG7Q5MZ7HLQzPRmXMq\nuQbM\r\n=MuhN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.6-development": {"name": "webpack-cli", "version": "0.0.6-development", "dependencies": {"chalk": "^2.3.2", "yargs": "^11.1.0", "inquirer": "^5.1.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.0.0", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "f34da41510da785a76812cc474eecdc8174a867a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.6-development.tgz", "fileCount": 13, "integrity": "sha512-OxflJEuIKFhn3aDs6CHfl2f8lwNVk3Ptb4PwKb2hkRC/4dD7wx62W+feWAWtCe/6I6mHZ7BfU0KWSwj13MP65Q==", "signatures": [{"sig": "MEUCIQDXQCMxYNfWG3bie1qio5hor3fjvxNusqfn1uWeREY+TQIgcF330TDG+WFNJai1sxhttbzokvQUuuGezpujQHu6g+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/ZxfCRA9TVsSAnZWagAAnwsP/RwBdmilTbAOC7/bCFS5\nThgZXvOnskhK026UVmQR3kDPh8WnUWciHKWyi3Yp22OoKleUj2Ac6F97mvTU\nquhU+6+p3H5x3+U/Pl/+NiZsarfA7QBUqwm5QGIdxrgX++w22hPhorfLP1FS\nAjiiUj8t3G4st8BQ+QMS/CUWQTrHtkZfNIz7E7JEV1KUJPFVlwBX16Sx/YIx\n80DE7LT16mEHjedqv22AZ5vxrSg1BJe6Gt19PbBV+wfpVEf7GOH/FgHPFuyI\nEPhJhfu5dZCBjL66lqD7jvRACsLucRyrQOjXlmCW1TORF1WF8W7AJYupVPqU\no5Xai4UuN/5TWFHFfFN8eCkCa5bgILe/cWRFz+pgAojk/v5ecC9OYx/EER35\npi760BCqOEoLCSy+bL74xNwp45kd9bDWeEXu0Lv5KiqdkywUMGvBFH4R93+W\nyrZfFP5CkYearISEeS9mdvinFsJg8rs2v/2zpmQja0Gg1DnEJs8TET60YwGh\nEjRFumSs2NzEiRTDSg/AlP6fJt7GzlQw1mFZllYEe3jARPmtjpeWYpDRIEPO\nYcRSJuoSRlcsTEb0Jk/tRAzQP0U+3gvROuHJSgLfZ7Ao3bAOUUYAa+P2u7Y/\nylI7LnPI4lOPrwGLzL7LJtu0wjW5WeAX5TtbCwgJeP5oSGUdjcJSWeXE30il\n7r75\r\n=BMMs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.7-development": {"name": "webpack-cli", "version": "0.0.7-development", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^5.2.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "a01134dec41614f427517e09bb735b4ef296df56", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.7-development.tgz", "fileCount": 12, "integrity": "sha512-s6sNTowhTpwnVd7H0BsHt/ppcqpUDzipUh86o1LZCGLzFoA6POA+FxgSY0zrfa0L5WVMJp0eT+XRnpFvdp9qLg==", "signatures": [{"sig": "MEYCIQDYOmU+SWivBL1Q4GNNoIuzRBu9ROz1JD4IvjcBWqSu8wIhANAuCfkT4qe+0r0jGCuS6HjQM1uSrJ9z3FXg8hkupJHf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAtxQCRA9TVsSAnZWagAAHP0P/1ynpeo8Ies1tnoZXzfg\nBtk6HtmaYNiZtGUFz1pkNRhqTQBCWK743zUY1YzzdIsv1yrwIvcHrxrXZTPo\nKWNrVFAgjhtT77FkONvgfOgIW+6a7hPW6irzebFKyoKbuvedpA27QKHTLf3C\n+2TOelkhF4y9E4H4C/TxstkvgHZSA77R7VR7NONrdriBxpj8l2ASaQLnYgVp\n6kJlCcuW5vL+DbDyr7KYgbo1+u1XJXrBWrzVKW7MSeG/elEaLrTAxvx7m9OY\nKxFovlYGbJWviEdgE0whv1IziBUXrqKvzisX+or15rz2Uvj1vGwWEsjt40o6\n1AFb7IJIsKfjNg4vgB5xY/JuQd6qn/en+oeMfomfLJmJdKlkPvDDf1vcGIHc\nL6bYSwAqB4RUwRPiSdHTOsCpbShw0A6M90aqMwCZB9lz75SH8Na0tuDabxoE\nJq0HTbLPt5X8tTW5o8x/1o/duKT9jIHJU47qmVM8mlGvFqddhK1imuVBK8SP\nf07FxwL76g3onbMhJbcEwlMZ8k1ILIazZrw6+1U+6N9r7j3x8q7e0wH6w7gJ\nVJJ3RZ3AaAP6KhrDlwudn5X4njRy9ZLyQwRmX1uZkyW4W/kMjqfBkJFK+lGD\niu0SYQEQ86MRYo+un0ZpftB35QEStPQWPUxSOLSZPj+HxpSdMD7cHoGvW6X0\n9fHw\r\n=C+S5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "0.0.8-development": {"name": "webpack-cli", "version": "0.0.8-development", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^5.2.0", "interpret": "^1.0.4", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.3.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0"}, "devDependencies": {"nyc": "^11.6.0", "jest": "^22.4.3", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.0", "webpack": "^4.2.0", "jest-cli": "^22.4.3", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.0.0", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.1.3", "cz-customizable": "^5.2.0", "semantic-release": "^15.1.8", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.1.3", "conventional-changelog-cli": "^1.3.17", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "8669f3305b42a6624b4b6e4bf38f23bdfc69a6ed", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-0.0.8-development.tgz", "fileCount": 12, "integrity": "sha512-/hgqRmR1eHf5mooHUTsM6Bc6HJPPqOF3561wg5nyQZ/D2+kPpLZ/BMAE98OpCa/wZ+dmnlcX9Mdv72G3ZSaucg==", "signatures": [{"sig": "MEUCIQCOKPm3K4YgdppJbjvwhG8Ks/q0Fz0/DuheYzQjDl+lqAIgHP2DPpVttx9lxpK4B3HgcYYrRE89G/WVQkOJP8HqWKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78210, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAt7+CRA9TVsSAnZWagAAvK0P/17A3fv20SghBtWtNOER\nuEnsSX1dDw4V4xQv37YIFatM63Vz3J3eiEsmRZ66Jt5oluFdqtQzw4aANIg3\n5Hmpux+4zyrxBNvkGJEheuEcyLFj8DxhA4L2VdB6VnWXnVp5wEmqLlelwP4I\ns743ldoHocNCKonGvXkpuaalUiozKjRnc4c8aq8FDE5Zm+29Zftx8qOP1pe6\nAyU7THH3CTHVMtfzcGqsFS28V8BMEmEK8ZuoF6o6lGMoqs1GuGfE14HCtSfh\nwUHE3SlD1uC+t5KBpRqWbVVyA7SUSBuuJM1GcbqrSAtfnqd264sdxdVUeA8/\n0/jq7khOE7Qq2B1HrbqLbFgRO398T7CbGSDVA1taz5ici0PJDmXOwr9uLT+i\nvNzZ4pKgE3Cm4Mw/sjMJXjpINvM3+LAOjQekZsw5dzN4kedqVAtFuTfdcRzC\nxY9j0y8A9kfG1slG7kK9IphEhuMFGluGcAwFdPSSf+xN0FyP69X3HCBKPYpl\nIg6q3D7tKjOK7evc3fupeQ9/v7I7KuOBmvh4TcIxge1ocQ0q46A2mxbnwZS4\npnCMbfLURJTM+eE2iokT6Wc3hE7G6647gAEjphrxYm8HAlRmZ60IFZdoKIAC\nW18UF/wFm8lkCjmbsrhoIHwAYBcQSPc5ueW8Wd2DK5ESoOnBYaUECxY3As4r\nzJAp\r\n=C5UQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "2.1.4": {"name": "webpack-cli", "version": "2.1.4", "dependencies": {"got": "^8.3.1", "diff": "^3.5.0", "chalk": "^2.4.1", "listr": "^0.14.1", "yargs": "^11.1.0", "lodash": "^4.17.10", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^5.7.0", "glob-all": "^3.1.0", "inquirer": "^5.2.0", "prettier": "^1.12.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "yeoman-generator": "^2.0.5", "yeoman-environment": "^2.1.1"}, "devDependencies": {"nyc": "^11.8.0", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^22.4.4", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.2.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.2.0", "conventional-changelog-cli": "^1.3.22", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "cab81e79249127384fb69b2fdfe2055f9c771b76", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.1.4.tgz", "fileCount": 175, "integrity": "sha512-dcxBcTPhKczWHYE9jh8MoHGQFuJxfqshZ3XSNFZ8o34heVvkqNvSRbMKy17NML+XUea7CXLzHWDg7a0GsBp7Pg==", "signatures": [{"sig": "MEQCIEedxorrHjaopIKbPURga1zxUHm4XaAz+ZzrpBEHYKL5AiAO3Ugt4O0e8JntEAoBTGqlLLhgxFnZowE5dRv62n2IPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBZQ8CRA9TVsSAnZWagAAK+AP/0+Ohx5o1naLd05Xaf3f\nZudpmgOMY3jfa4ZZ0V7ogKR76wbS7AfqtyeIvo6jHBO/p+hRsZoJd/tecFX2\nm39KtarEhxg3vlnkOJ4/Gf8zaQBiK5o7hWN/YjukJIGDmej6Sc7vhWqfn4qy\ndq1vMkKLVALFcwdJo/IilJXH+5o6iaHsEr2IBkAQFWedVZWMXThXS0zv7HBr\njynXfRJzCIOxnFXv1NRdm+nfYJ3t9Fb6WEav4unQ22lP5HTc6RRwEkk0VP0C\nTYxOzHay/kkIMTfTiiS07sgJJcix7tRUVHegHQ1UhBfAC6LXERd4wQqU1gOO\nmcIoM6B6RxwJLmduucmaFLs2kT8DeS/a/CkUkkfM9ekZsBtDw9c9l5Wo9A6x\nR3iCKuJ5gFpaOVpv4fpOtCW+BzF3plbFrXsgcESt3nVQcU9Zi8olkLGgMoJ+\neOURP8ZgUipdTZLqAOPrbZkWhG9x0cW16RSrqc+57JkHZsUG8YieEkE8xEsu\ndsiZu1pL/0XwJFCblU0KQr+VY/fQ1BJRecLpt3EECT6ufJwv1g6gS98VE1nX\nePJpCSwe/tRC9xHMw7G54O480M4eBdQnYyn9XYVGwarQ723UlJnCAPrBllP5\ni2hT8u9p0KOGYBLKxtdlRSggWg5lY7KUGSG6k0eTgQ9nkYXsnR8gYd6/qEOj\nFnGY\r\n=gIjy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "2.1.5": {"name": "webpack-cli", "version": "2.1.5", "dependencies": {"got": "^8.3.1", "diff": "^3.5.0", "chalk": "^2.4.1", "listr": "^0.14.1", "yargs": "^11.1.0", "lodash": "^4.17.10", "mkdirp": "^0.5.1", "p-lazy": "^1.0.0", "envinfo": "^5.7.0", "glob-all": "^3.1.0", "inquirer": "^5.2.0", "prettier": "^1.12.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "jscodeshift": "^0.5.0", "log-symbols": "^2.2.0", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "p-each-series": "^1.0.0", "global-modules": "^1.0.0", "supports-color": "^5.4.0", "webpack-addons": "^1.1.5", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "yeoman-generator": "^2.0.5", "yeoman-environment": "^2.1.1"}, "devDependencies": {"nyc": "^11.8.0", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.9.6", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.2.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.2.0", "conventional-changelog-cli": "^2.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "bin": {"webpack-cli": "./bin/webpack.js"}, "dist": {"shasum": "3081fdeb2f205f0a54aa397986880b0c20a71f7a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-2.1.5.tgz", "fileCount": 175, "integrity": "sha512-CiWQR+1JS77rmyiO6y1q8Kt/O+e8nUUC9YfJ25JtSmzDwbqJV7vIsh3+QKRHVTbTCa0DaVh8iY1LBiagUIDB3g==", "signatures": [{"sig": "MEYCIQD54JUaHo5+XeeMI0DXUPdc8e6x286cjeNAhihwxSbDUwIhANeo3ge/jJFagXd6X7ayZC/9WtXtaO8JVOGcamYVef5w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 263414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEXdWCRA9TVsSAnZWagAAJRkP/0twQGF1g1wscWewNgG2\npjl04jddCccsxOEb0xGdL7Hwck4/b7+ITdDq0UPd1vaGvllzDGAV3y3yBHu/\nb1MvwcRAYzgrJsyM7TWklvVhHno0OMj+P6//Gq1j4uXkLeprOcQWRhPOFXFX\nN8O96NUDQ8W5rt/3YeDVQxdsmrUE+LbpdbzhzMGMdABu2oQWXAZM4i2iCpeG\nv9T4iMdogwRHXe6UtdFHT4FGHQOpz2pdQTVyz8Dmp1CbuszB7ROLoTOIH4S4\n6fUU0Lxg4aZfmrW3FzioyPn6Php2FejEV/RNHs5VBEofs1A3VvO7eWWiZTp2\nkewaWCix5Zlf+wUNM3RloTMiTLiq0vNiKZEcJakWrZIQ/dDijcYyHppE4znD\nTRrf9bwe6H88eg4KCNsypNAv7kakJc49B/MyBXpldwuWICETfYlvNe2Hj4Nx\nv+c5JxjFZ3MVeJ4y7eAG4XjQxwr7dnl3nIQjenZTLDq3rzn4DGepY1DggyKg\nsrVLkt8l6PIr4DEypnSvgFp4HMgd+E1AbIyEztcCwklcLe31Bo2liRRn2k52\nWgwmLvn9k/WVlUQMi3bWvuKEH/+R223kcnGtRMKUqKF9cSDgImNHexaL52RL\n/VmvpH00SL5y5hQn/EkOkLvLX/xDDXbz/WI+2DI1X8QVUqiHT/1g7gATj4YD\n8vaC\r\n=rb1j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.0": {"name": "webpack-cli", "version": "3.0.0", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^5.2.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.2.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.2.0", "@commitlint/config-angular": "^6.1.3", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "7bf9372f1f096d92132a076623ce41af8943c222", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-emiwOmmEQGBt+fOp6SzUPKSadr6XTb6Cet/+n+fpovcV0zU6CWQ4nnwr9YpGfiyQK0EPP6MkLeFUJTjhdeuVTw==", "signatures": [{"sig": "MEUCIA+WTqS4P1POsrjeth59ccgy+hUUxuZ1C9eZLVS19PW9AiEAy0QUtPv/3DWaAjZ44ec8RI0YdeIHE+E4B3oEOKdfq9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEgKUCRA9TVsSAnZWagAAyZUP/jVpnUGszk4tsszQyJOx\nuG1SCks0xPCZSF5qyqr7G7qLvRmTogMJOpIAaqhFEitVab47g4YZaF8/oqYi\nYFNIKlqxfbOjC0ZwuGo6I+plpD6TrgkG9Taj5HWk8P1GFsNO2J7ZJk3B3m9i\n4IFU0VOCDWPzg+QB1DLZYdxJvVStHfpHX5QtLqLYpEbv0LnHxqIcDb0w0i+V\nXxb3nUqwLYwZ7YcTFXChPCf+wTvweF445VETKXEcfeldK5YJOWAMK6JmaoWa\nBIbss2HgidQK7+SadjkPARbrvZjTEB9c9aNQ4BkzM/Hem473HG82GTxMEfWs\nJlqSj7ZdBBfgnrDxFl648WdHq/7BsX0Usp4os1tW8AKCCNFxhK/tB0QZdNsO\nZVpp+Z2ca37xAG3IxpA6iW1W26hGZZFKh9PtYGNPltoK8YSV1sMa38gH20WY\nUnKPPiufUWolXxdu6XzLRFQ+GPt2leB44EfRwxnGfdM09LzJ0H/+5963Oj0B\nYqcwOU6WDEokbOklEwfjdPh/UGl+MgLa0fbYY0MA2zXdRE5cAASSZikD5j6y\nsRSnrLCPpT294lB4IbM8kMJR6XmI7B7spSNIUnHPmlR/UmY9LgMy6ilr0LdE\nTUJaUJZ1TSf0sXOkHyvhjDPPHCcrsIF4ghTan1J6C7F7X+eDGnxNf2alSmvz\nPIrI\r\n=utV+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.1": {"name": "webpack-cli", "version": "3.0.1", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^5.2.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.2.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.2.0", "@commitlint/config-angular": "^6.1.3", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "1d894ed039268aa1a17a9b80f8810c637fd0df63", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.1.tgz", "fileCount": 12, "integrity": "sha512-eAfjI1QNB+fe8F2K7uTWeuhEzd5uu7Dy8PbgiwYbk2LFWlI5k+vVXb5I6ugJcHQxQpVkiHIHw2v1a69AGWcCVA==", "signatures": [{"sig": "MEYCIQDQFaMtXN20l9RbIp2nGbtMuFWPuXUjy/QKRohrfZj1YgIhAO+mDW5Cv0MwFu4mM/23euAaLjRTWIMFAWyy6HKQBU7W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEsGvCRA9TVsSAnZWagAAGLwQAJOWM1clP/ud3h3KE4Qx\nm1rMUBdvJvEUuuk9Kjg4lqMHVknIKO59NNXXlbICdbP0vXV6+OFLMRW8Y8yK\n3g5zCFy/j0s0sjIl0BpoVwRN3Gf8OsAuEA5NocwI/M5+ofbSqFGzq03XgGzn\n+Rm5uASX0RHrDoJmq9/bfio3/uNrmk2QB6f6paGP+nmRpw+68GqYkqCV0CIh\n/UYgmOfyvhvffoftIZHfaRKh+l3jjorc0eRyewkhjLtieQBbqV0HjaNEcVEw\n11E3dg/MeWw+o0b77KW14lGvxPqVtQRsgntRFCtI8J2hXmJFACVhsbpOp3o4\nv6gjnXKI1yaWGbOudZ0s4KtJOAEA/PvfsWWVhcVGR+nnJiDYxryC3r5P9pjU\nhJNkdKJGMyBqsSibKBUsD1Mfu9oraK51A9V4yAoqdlUgFDdlaw8NBBkJdQtG\nz40pmY0e/ElGTLWhNbfxz41F9sFHlpo4tIbHKtPmMUfIAFMpROrZNBIPLjPj\nnDPOoiUquA63AyeZrMKXD5iDVcodw72Pgmj2Ul4sxAaP1sCkDqhV3b7POwhA\nQYzZ52fDmSkyYvvjKzMlydUZo7JRvcfNYE00CawHsYnOf2arQ0WV2xCv5t48\nyGfX5ZQHLqJJ9DVfMtNfaN065b1hrunfQ0Ty64BZCCjF5714uv9XX7rcNEzQ\nyVuH\r\n=eeqq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.2": {"name": "webpack-cli", "version": "3.0.2", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^5.2.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^6.2.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^6.1.3", "@commitlint/travis-cli": "^6.2.0", "@commitlint/config-angular": "^6.1.3", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^6.1.3", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "e48c5662aff8ed5aac3db5f82f51d7f32e50459e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.2.tgz", "fileCount": 12, "integrity": "sha512-XJnOWrGOVnxAa1ry0iL53K2RRhwjndR4x7ePfOJZbXBn0iHXumu9U94ZMssDyIt7I4qdKvyVuuSEjXjeAcAldg==", "signatures": [{"sig": "MEUCIE/kfjomxAVBMMH7LC2xO8MUJnsW+AVBYLIBKxdUTypPAiEAjR59fVcKxU0UMrvmp/irPXW/niRAzu6u1/fENY87aog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFZ4PCRA9TVsSAnZWagAAaGUQAIBZCv71p7GI/aDypFGV\nVVR/lOhWKDiOWuxJrSVId6UcfmF/v7xdEHqzkXp8TKcVrqgTNZrC2OGPWAfe\nBwLrEkESNOk9CTXa5v34TVKyeNVc3VzH6DdrHrGV49OpR4x4+kFE4XT8h8vL\nMxb2RtGuCZkNoMgzNwPyuiCpNNb43DXMbVRbT4xbk6CpLWvRWdsnQzoPUxqa\nQqv0ecSaUipkhdBTagFKBmSLSmrhqokAYgwRrbZ5DADqWb2raJ8YqBPCwKyH\n/nlMW/rF0MiKhD99LjGwxGShFceC0IgoGUHuPPJUhOQIrxK+92gtHVK2Cwb9\nAlnjz4NbjyY3X9XrSsVqSpTqlQ9OCFhOngCO1IUYEyeKQFvXBz+Nyv3/fVGr\nqskJWJNtYTSjeZP7pqj1xl0GYgIWxlcNHmuw+wwqVfhtssH0HN1KyEDzU8Yh\n2PgHMFJOx29z7+qWHxewXskkxcscXwR3/cgJytMJPwRRQ9KqWEBmkggKIXY0\nDA1uVVGoTTGrSCOmqcWpCEcfpg8vTNUtpieHTYinoNwX5ZCoNEvPnlNWLZqU\niCRq86VC80wPdXUI2E3ZYcXyH/b6/wEw+O194ee4veZcL3kDMDFHG5orcxiw\no3mEGKqxYWxA+ZRldeAvj84tlY5l4jYQlHcof1XfxjFVqhGQRvpvxNf/+m4J\nT80e\r\n=tr2a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.3": {"name": "webpack-cli", "version": "3.0.3", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "1a8c6e09dee1fd45305f3b3828cf081903cee0f2", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.3.tgz", "fileCount": 12, "integrity": "sha512-65a3T3SDIozJjRU4UJMdK+LXJt73gNs2qpdjsOeq6jIrfBvAKApy59Glof1qDG3wYEo38HRxb+KrwsrsAtsaiA==", "signatures": [{"sig": "MEYCIQC72xhjrkH+pIPnsPwJObEBekHoH5gv3z+TsHKDig3kCAIhAImihnIiyUq7z1/p6CIkdtCFEj6ZKmEapr7+5LOO4NWz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGGGUCRA9TVsSAnZWagAAXVkP/jxpcQvUCOfU0AlfMjqZ\naVBcrtJLVIH3W3XkXOZaO1js9Ykiy0cPh1BlU2+IGIFPWdXf7KxEtDt18OYs\nMf0qO63ELEIJxCiZZf48Ju9hH2KUK588MsDOJQNYbOrvSJ7e8m9YfU+xBLiE\n2+JU6OQO/ao6635oZkgy9+GahZUyBjTjJ355UchiIVRf9CDp4mMI2BiqBmH8\nZgb6ae7ydmKsc5vLsVWVd6pPNMbqnqIx3YqDmRUiplTEq6Y+fKQew/OmiyTe\nCq1Vknp5q8HE9nJ78VfIQ+bsNTJUXD7LaPSnILn6/SR+lXln4E8A30pfqDE4\nGZgDzqsBUK8+qPW43LodCUd4xV0pNRtaGs2XT1S3qzUCj958PkWAmXDVN4la\njPmMOAaRoZoJOVPZHdKFX2YkZLH+Rz6fMJCWLSrblRDvF7OklCIynRw+ol0O\nNZQT7z21yZqsc+XxAo7nn6/8ABD3WmhRyovI6VP5IUfBMXzSg2kA5Rac7oWA\nX2vQnWZOodhC8NBZEiaMiH3ep2fpYlsvjfniObP02cH8WpKE9bCTTXRsukLN\nkg2S+g0odnICQ0I1K8jx4dKq8GMJD3Hrl1TVijeFdNpVltUsLvMU+nRP9AsZ\nrBNXrA5j5aoeId7aa0B1GSYLyotl93ov3S2LZhkpWbdGOi5htuSm5cgB6uAT\n5pkc\r\n=oMXl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.4": {"name": "webpack-cli", "version": "3.0.4", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "55d6ad2cdd608de8c0f757dde5bc4bf5bd2dec68", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.4.tgz", "fileCount": 12, "integrity": "sha512-r5R0hMck4GxUS6a3TXClwi1KhQfnHZRtIfXqsSytQZG4kawKMhTtd5//uNZGoGks/h61Zv5jMnR6jwx15Qf8dA==", "signatures": [{"sig": "MEUCIDR7Vpb9kI0hVgVZu0tqrKlELYJSV2RVhi29cekdgOLTAiEA93kucMvguiI1VSmaAV1k4m9yx/+8QKD19Hf27P32Ahc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbH7FNCRA9TVsSAnZWagAAPfkP/1YMQV4JMatJTH8URbbR\n25CtfyrJJBtRniBXymrVnutxCzw9vYCzqgtDufbchIOHOX4MaTFIKeLYshIO\n5/WxELRBd7Gs9fwJNJw97NyXdzJ0qQtATc7qMJaysHeQ4dHZaTct0eiU4sse\nfpY6JNF8+lYdUI8uyfOr/DJCoOHtiS99cfQj59Y2rBwYrxFToiJ6YYEAXLnD\nLd5XXZNLfX1Govbo9Vpnqu9z6G7cG/j1kAhqiggBwAnn67r20zqWvTATh2px\nxtm5PUky0HUw0pP8tUB3766/BY2PW8qJSwt+mVNlI6d46G4CFWyLEyv07DFt\n4cIwkeWTrqxex6PQrJda89QCFPuLmg21NcP96zLDVe7J38Hws/IZOpBu5qPm\njdP0qaUYlnvbcai7xFewnRMZFbdWxxJDD/rIO42tO9aWAugU8TWiSPL7wKD+\n/61RxBymHUGEzeXrvOWvfp2HNzuqznvYlYhUw2NysrqSBaQFhSA/eNL9zO9z\n/v8SSgHEbJYUGjjGJh7jnFpX5YCp9B1o1w+SVeYmJKZDtDMeltmY+QXzR5H1\nKPdMJiY54iI3gidG72lZLFZFJeAd5bRTyiEZQJw7vdwXVT5XpegSaL/v13JM\nobOYZ0CtxEYaDgeAfENgGhj8HjnisD15rE3QCK46H3Xuh73MIpUmMWDmcEDm\nn5rZ\r\n=dcnT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.5": {"name": "webpack-cli", "version": "3.0.5", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "0523412b5b6e5b0d6fa799881c58081318ec5eda", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.5.tgz", "fileCount": 12, "integrity": "sha512-rrEr92/wx1zAqnPSRc1JmT7RhwrzwLYkoQeDVLzuueLz+WN7UWW0S64zdHgm86h4ogF2rCFgpX8awfvdtWvq7g==", "signatures": [{"sig": "MEUCIQD9Aq4r4W6Fwi3MmqCbw5CjpmKVZxKNyEnLzGcrS1sv7AIgD2N6tLxcjgEx0UEGSjPSZEp2fLMarxIZc4xPuzxst9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIVGbCRA9TVsSAnZWagAAmiEP/ip+tgBSR1fqt+w2+YwV\nZfLs+IuYR4avlufkQVxLSsg8VlfRVfFce7F8y0amZPiAx4e+fTa6kNLk4wG5\nh6/6TL8zm3Z3aCLSSlneUqCI8bdzbeXjebrDqEmZ0wkb3t3G7PiY7tl/29Mm\nImixn492BYCKKmfpmUZxd8HdpwEmO1VX3j6ibzbkXto4AxG3F1+MCr4v3d/a\ndLQLF3L+eVGii719d3MW1sWCQ94EzpZ+j78LekpIANhW1f1GO5i+Z9Jg0Gik\nLw/YwcgTfiQUio7/1eOlryKmocsZTa+rWy4CNZr1YHbNoUVpcQrTLqsuPlS8\nRjQVjqYejESuocB2blv7AkZLhw49YgEFE/GwW1di7ihjaEZVqut7iWi8Kkfq\nMGVUlkROKH6aYahlKOCGVtdJxmeJRHtKN0EaRXCkdBC4CToimHodlnyMzCWn\n/J9ghwgNyvRfRLwmQDqDTVH22sBfSxbwTXJd4nB8aFyn1/ZcAOhAwzFgdFZ2\nKq4zw3oRYOEiqJCgJzrbsMqNAq3e7oh8sOLa1e0IB3bJcRRfFZ2DxAb9FU+I\na07ONqPPPGADsA0zBOE5ulfRYcH2MrRqWeYsNLcwEg3H+nx5qbHcZhwqwdqt\nx001oPzZ/FM1as/L1Prly4Yp9FwTIEJHH2mzNFC1GNcZB++osMWIXrnfUxHS\n13OZ\r\n=cloL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.6": {"name": "webpack-cli", "version": "3.0.6", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "5d414e66c6576d4050c1e6b7ab3ecf9969106525", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.6.tgz", "fileCount": 12, "integrity": "sha512-WzUnmSFTRVhJzQFCBXeWCuBgBEPkOPVkC08qe5RIXZEtAvqmS7OXFTgd366gaa5SryGY3Io7G24uwY5qAu8Haw==", "signatures": [{"sig": "MEUCIQCo0iqc18ycAiXY/kjM0Na4VbrOT0f6nqn9cRdDimkwcgIgXgGE9v8RwQ9ZyD8s8h5Wu5iqx3zTp0QJNFgUxuX+xuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIWTjCRA9TVsSAnZWagAAEg8P/3DZNw1OlF6DT/2dTETc\notcggKi7aAXyF7m2ULw1XlOe84dZ6qu3pnZO9Mv06jJzzNj9RdUhIepbJLKM\nTHxORVS/5tmvtnEPS9D8OWrXEMrZsDewsdfiTJ91BYVef3XrVUmVL2NR1J+X\ndETzWYEjv3rBKLUV74up+S1XMcRPh0rLk1fV4j/jQSlt42Zhc9LiJdJ46vqS\njeE9z/sn+XgLhBO9873iipthQEYk/+O0x4XA7WfXUW77nnqiBH1yTq3R46Ga\nNFR4O37Xl0KEGckMW+OE8dQGlPN0qehAiwYWOd58XZvNvlHPGxWFo/aFifNu\n5eOJuhMrPqKU5ajNWHH0Q0XSSB3arnKJa1TGcHeGIydalNZP8sYmutv6cEbl\ncnOGHIg4y1Mw61eaQRlFLnwkJjyweav9RyBLLU1mSAs1hjgVxQ13OXzMKGIq\niLZFrmUH/xchYmIC+XZiFDIRmKwvcxmJsIVa+3NCJfTjg9Xn9Fz0vcjTc722\n8wlZ7xJl4wcb7F5DKR+pzzdl2ujltgebD6SE7HJ3CYEDmv662+yKqyQZR3/n\nNeXR5fBt+1DxG/8KTv5RQXo2b8qCgsCDyWuTIzzbVQ8KH/UTNZYI1ap+2N4u\ntXs12wuYGhEX5JOwf5HiTzPMZUQqTlavm7QPZMWTRGiGY1l3NFeR3WbcCxJA\nzQwC\r\n=Yoo6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.7": {"name": "webpack-cli", "version": "3.0.7", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "d4171aa6a52f28d3a40048db3d0764e1c601c029", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.7.tgz", "fileCount": 12, "integrity": "sha512-xj62CLfy0yF7eoEcXYsHmCw6isPVFDGG+CxhHsgpHsuzMBNTSHyFrenK1j8MgNspsq16xchly1Vlx6lihDhsBw==", "signatures": [{"sig": "MEQCIEWtdCJ5KrFxvCOEXooguX1xk6wdniKCbtepLXB1FhVAAiBhoreOyA8i0B/dXR1Pp9oKy2twfQ2J1Pb+rOuCMIAGVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbI2u6CRA9TVsSAnZWagAAGHAP/jTWaMhSflHJ0lXqeb9N\nM0d54tEri7VWA+mKHpJfBng4l+HJh/d0JZTkPf/umplG7g+m/iwF2VDaJDJZ\ndeIPtXEaJKbNl6UdznI3L8jn7BAumaehd+nNuSTURFJTGRsK7IxS2j112D0b\njdneKwojhw9cBQav1f0iOBavm7Ez072LHSzIY/XbESg+BjS5lRrbXUloDQmL\nSfc4X9R4n+pb1RDeooHWU/a/MkHuCFapWuJ4To+jMq0PZmmpJQXnYJmDIQWN\nrm39L93HgKGfndT7M9i8u3SjzfEpSJOB3Sm3U9UV+X0NMLAgsetCLrgqh8ON\nlb3sy3Wk86yF74WLEDlZ/h0GzmdYcDQTPwt21fw/sJwXvx1zEQPLbrgK4v3a\nmqFNxKI/w6xU99TZY78okgjufwr9kOVin1VblcCW/EGmwv/I21LZa2jtgXfS\nM4jMYIkJRAXK5AN5GyEQdN0NGycrPbchEdOapJGJUm3bAJRdglU3vxkrYv/Q\n8ypPijU8cQNyCQjfNQKeD3rTFcR41SLvS4Hpr3F+ZIP3QRp5iTl5ipmeYRsO\n3SZtWTTchbEnoqa4qxd4S9YNF2s6bebTj6rDTsHre9Dxmq94dMEiLGdW1Ewn\n32nH+cCSTQYvIPUQN/UUWUqYOsBsUBQcpT/IB2IrU6Lx3sIZSwAAJ5N5o42R\nTkTM\r\n=CzsC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.0.8": {"name": "webpack-cli", "version": "3.0.8", "dependencies": {"chalk": "^2.4.1", "yargs": "^11.1.0", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "jsdoc": "^3.5.5", "lerna": "^2.11.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "semantic-release": "^15.5.0", "eslint-plugin-node": "^6.0.1", "travis-deploy-once": "^5.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "90eddcf04a4bfc31aa8c0edc4c76785bc4f1ccd9", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.0.8.tgz", "fileCount": 12, "integrity": "sha512-KnRLJ0BUaYRqrhAMb9dv3gzdmhmgIMKo0FmdsnmfqbPGtLnnZ6tORZAvmmKfr+A0VgiVpqC60Gv7Ofg0R2CHtQ==", "signatures": [{"sig": "MEUCIEadfl0QznEFnqMBILpTH7lSvXkkD7QZnaBIE25hSkOTAiEAubwsZ/nYEaEopeyG6ihKbzkKJb6CIVizZjoja4nLbC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJRojCRA9TVsSAnZWagAAW3oP/jvoQh3W0eW9kGYY+xiE\n9cgzwa2ESyT704OAaCrlyCLJ392MqV0o5R8xoWA5xBcJ5Vup7RAb391lEXiw\norbbV4iJIQ6PfYV6ppRzy6+sjjYwSN6gXjst/nbAw7TZqkJ0aavcSf9IgstI\n2Ju5Do0PCqd8T/FWb6J+2mS3LN61tw30YNNDpE7MFZCZad4vkFkOO2/E/Jj3\nFbsCwXue97TfYXEUPZdIOOAvGNRml6NjA/fCsAaJesPqEnzxedkHd/fZWVNR\nXBEoum6j4JCAyEFNvbLeE2jZrVrsj/ESp+UrjA+cXUMQJ00ZmqurDc53uMY7\nF6JM9zWwBBDRi9/clerVwj5H4EqNI4ZhlXwp5KjD+fbjFuaHje2SDT/Fr5Eo\nduP0LXy+XJPQimT1FMpLyBlPeFk0SbTSzl0NADhKIM4/sv9MOxHlbCdgH/q8\n7Aq3rMC8rfkmuBFDxi9A0yOw045JEwu7AZ8kd8gdkZtEoJYDd9tO6bUn56aK\nQqKP0EohsVfVhc875SWv65riznEm82oqVp6cPltRn+mwvg28MZ9U2uSJVC58\n959ADxZwJ3CvV0t9UNtWUDV1MGJGEMTCVFER61YtNnYfgaGBxEf7luEaMCbp\nzI2TZ6NIEAtI4dIB9rgpbt5uTIReHo8NgK7G6QQK7w5Rdo00e5h83ZxsahXB\n3a73\r\n=kZMR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.1.0": {"name": "webpack-cli", "version": "3.1.0", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.1", "inquirer": "^6.0.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^1.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.4.0", "enhanced-resolve": "^4.0.0", "v8-compile-cache": "^2.0.0", "global-modules-path": "^2.1.0"}, "devDependencies": {"nyc": "^12.0.1", "jest": "^22.4.4", "husky": "^0.14.3", "lerna": "^2.11.0", "eslint": "^5.0.1", "rimraf": "^2.6.2", "tslint": "^5.10.0", "codecov": "^3.0.2", "webpack": "^4.8.3", "jest-cli": "^23.0.1", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "typescript": "^2.9.2", "@types/jest": "^23.1.1", "@types/node": "^10.5.1", "lint-staged": "^7.1.2", "schema-utils": "^0.4.5", "@commitlint/cli": "^7.0.0", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^7.0.0", "webpack-dev-server": "^3.1.4", "prettier-eslint-cli": "^4.7.1", "@commitlint/prompt-cli": "^7.0.0", "@commitlint/travis-cli": "^7.0.0", "@commitlint/config-angular": "^7.0.0", "conventional-changelog-cli": "^2.0.0", "@commitlint/config-lerna-scopes": "^7.0.0", "conventional-changelog-lint-config-cz": "^0.3.0"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "d71a83687dcfeb758fdceeb0fe042f96bcf62994", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.1.0.tgz", "fileCount": 12, "integrity": "sha512-p5NeKDtYwjZozUWq6kGNs9w+Gtw/CPvyuXjXn2HMdz8Tie+krjEg8oAtonvIyITZdvpF7XG9xDHwscLr2c+ugQ==", "signatures": [{"sig": "MEUCIHGKffQzayFMy5vQWYhHQqcOHWYQHSgJ4fin3ZVopsRLAiEAhddOUCpIrYvQmoxAwi8LpEHBXTi1xcRMo5Xt275wCnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1VdCRA9TVsSAnZWagAAJzMP/A8IQ75B7ALfVvH00JOr\nQNJU3ZPYHgIordR47QIjkpUmSTLmWZHiSMcwtqck6jP/lhLsIYBRbKhOs868\nphxiYQFEMfjHQlRX2GBKucDBvquhw+mKpUbaLaEFlZxFzieiplqQJKIx5Q5h\nPjx8lIeBES3E36rJsfSVTjsETaXuebL+qe1FkYHnzR7BxH3vDRVIAPpqdJYq\nP5t2yYjmEyGBKeVXtsLOojCLD9Cn/YWR2El+EbSqdX7PE4oWisPijlALRokl\n8QznCWHIINt+QIUWaHYLmdRgFTjjvaEcMvS0hNAwL1Wqno0YKKYCthKMIYYP\nxBgueh9k4WerTJ5tsgO+KywwJOyDEqclexQwBja4emrFcn1h0vKRqk/kuApo\nKtqkFjxo6gvGqV/Y2CiqKr70igengUJvPfUEx2h//CtYqj3zxO/eg78EFxbd\nrjBObMSvIOhrc3ad6NlttxXxUKUJXZGHrTBotnEOO+b5iRrb9SrIswq39Nkc\n18baKNsp7SbOd2dlK0piUoKDWtpTyVvBxQKPfvQQqVMD6rXxZF+6gBv4PXTX\ncYSmFb0+t5GKAhzONgRquKaNZV2c1nNI2T25OyOjuV82uaf+m9/J4gkfCqH4\nx1MZFf0KECBv9YNTgw4RbG3scdQzcdeVQzHx1GFHQsWuDAhEiaMZOLiDiIg8\ngwNA\r\n=whWa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.1.1": {"name": "webpack-cli", "version": "3.1.1", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.2", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2", "global-modules-path": "^2.3.0"}, "devDependencies": {"nyc": "^13.0.1", "jest": "^23.6.0", "husky": "^0.14.3", "lerna": "^3.4.0", "eslint": "^5.5.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.1", "typedoc": "^0.12.0", "webpack": "^4.19.1", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "typescript": "^2.9.2", "@types/jest": "^23.3.2", "@types/node": "^10.9.4", "lint-staged": "^7.2.2", "schema-utils": "^1.0.0", "@commitlint/cli": "^7.1.2", "eslint-plugin-node": "^7.0.1", "webpack-dev-server": "^3.1.8", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.1.2", "@commitlint/travis-cli": "^7.1.2", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.5", "@commitlint/config-lerna-scopes": "^7.1.2"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "92be3e324c1788208a301172139febb476566262", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.1.1.tgz", "fileCount": 12, "integrity": "sha512-th5EUyVeGcAAVlFOcJg11fapD/xoLRE4j/eSfrmMAo3olPjvB7lgEPUtCbRP0OGmstvnQBl4VZP+zluXWDiBxg==", "signatures": [{"sig": "MEUCIQCEGDgXPWlQnlRzkhQr0MlqswWhKb+YxMwQFX3YNExERgIgLaYAiWa6rYs6jxFPI2vHGT5c5G+WiZrCsG6gE2y7br0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbp1cBCRA9TVsSAnZWagAAjGgP/0Xq1IuvWYpiOimaPmVw\nXZtb4kf5087kOYm2pM62Fok+bYiifh/ykp/NrgaQGEC8GLtllDmPDueGoHhV\nnuH97QEWVR2+nMSwx3t3ixI+TB7jFNHHWw+kWuhJng5DYbhZ3y84uILu/nw7\nLuyh2+l2nF3CguQNpPfxInUIZXniZCJl3/ueEFaaHpY2t0plDjBOlxAYdIta\nU3bmKdASyVee9d4H5Bar1u7C3BnabeLGzn0YNwdfslRRNfpoFZsdQ7Gt4uzB\nd805hDzITpjHiQ8wzbBbh+41m6dr6ZwDyQDqZgB6l3joz7WutEr/zr19xcAR\n/LaoSzbCJnkJldiO9hIAkPJo/o2FqP0RTFt7vlqYGbl2UtGplH3UXeFnzzrI\nYjErMMu0NB5eGyJccF4lTLbdQRCVVHfMWqPrxWVo/wULReTPDGh7dQFGLJTg\nf9BR5xWvkHKdoQNBP5kWnz58EhcUHyKtHr1o4S0SK9yVMUgrAGE/WkgGiMVF\nCqdLMHlcZhx5+btWeEdLGtyjLxf+S4RxFe2O7m05KzGoq9Iv/646LwhIPLfv\nyTBvUGcmjHmh29rTU2CYV9tfBTY20LGKktOdZRB+vmy3Jz+CluS7cdm7iu0l\nCYiiKNDk/Y1Dq30JkgtCP+iXgfnAPFujutXo/nXV+e6HGUkmAFiCgY+o6zYw\n9Gmg\r\n=RoNA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.1.2": {"name": "webpack-cli", "version": "3.1.2", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.2", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2", "global-modules-path": "^2.3.0"}, "devDependencies": {"nyc": "^13.0.1", "jest": "^23.6.0", "husky": "^1.0.0", "lerna": "^3.4.0", "eslint": "^5.5.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.1", "typedoc": "^0.12.0", "webpack": "^4.19.1", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^2.10.1", "typescript": "^2.9.2", "@types/jest": "^23.3.2", "@types/node": "^10.9.4", "lint-staged": "^7.2.2", "schema-utils": "^1.0.0", "@commitlint/cli": "^7.1.2", "cz-customizable": "^5.2.0", "eslint-plugin-node": "^7.0.1", "webpack-dev-server": "^3.1.8", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.1.2", "@commitlint/travis-cli": "^7.1.2", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.5", "@commitlint/config-lerna-scopes": "^7.1.2"}, "peerDependencies": {"webpack": "^4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "17d7e01b77f89f884a2bbf9db545f0f6a648e746", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.1.2.tgz", "fileCount": 12, "integrity": "sha512-Cnqo7CeqeSvC6PTdts+dywNi5CRlIPbLx1AoUPK2T6vC1YAugMG3IOoO9DmEscd+Dghw7uRlnzV1KwOe5IrtgQ==", "signatures": [{"sig": "MEQCIBEq8zb+z7nDmn728aZr746PkgUDh/OfpKi7v/qKOL5NAiA5rmIBWP5DHbHmlGXzEz+OojFU7s1pyMCW2H+0eXrMcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbr7MSCRA9TVsSAnZWagAA04oP/iimf2DmpDk4UMlPd/85\nB/VB8dsCeWsUigUVdSHHeJWJeTHP44Ozd48/dJUkwk55AaFXilKZfqVN5YVD\nH0Zf8lDTQKBA76k6mQ+yMVwv3PjH//Rq7aQiaFkMKZ+9jd0vWIl57wJx7MoM\n/TWd9veVj3ihFTXh2BCALy/B0zkCk+kqqGQuuGdXzt9EO9bPk87lLslChCwA\nPLBhxBKb6HzyEk+eNICbveNDKN0b2PbUwnfI1p1f+5LN25laRvnS9c1arCm1\nC/vnyXAWwtYCzTs1DygpNXQQHC0Z9KXun7OanoUOmvjYcSZjLh0au7oGdjt8\nH3NywRSglkSo91X2VlkLLobrn7OF2SojOHmv0CoTntW1BaEbYkDUHsl6DnOJ\nFdPi3EwHXXomFFU2N0SN+WuE5szJbzGHPT+A1PduvIrymLeJGfYD0VshI5MY\nPp16HT+0IbgHggdlm5eu7vGnT3ZeKSgqPb27Ke3wNp5U5eZUbVWMKivMdOGO\n+bZLbPZC6+n68MaKpzAVazX4c9/k8NpxRXun84eJENnHDR0auw+nVBrA0knZ\nnulHHvuYuWqKJ08QxLTgwDC84jmtkLPLwDl4H5cOsIt+26J2wnNW+taHth27\naRieNhhF9xE08QOM2FOKirUflD3VO8OE30snhANTkqpMBWoAS1s115rmZIFZ\n8Ne9\r\n=mRHD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "4.0.0-alpha": {"name": "webpack-cli", "version": "4.0.0-alpha", "dependencies": {"chalk": "^2.4.1", "semver": "^5.5.1", "inquirer": "^6.0.0", "cli-table": "^0.3.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "webpack-log": "^1.2.0", "loader-utils": "^1.1.0", "webpack-merge": "^4.1.5", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "v8-compile-cache": "^2.0.2", "command-line-args": "^5.0.2", "command-line-usage": "^5.0.5"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.2.0", "jsdoc": "^3.5.5", "lerna": "^3.4.3", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "typedoc": "^0.13.0", "webpack": "^4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "css-loader": "^2.0.2", "jest-junit": "^5.2.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.10", "lint-staged": "7.x.x", "import-local": "^2.0.0", "schema-utils": "^1.0.0", "style-loader": "^0.23.1", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "terser-webpack-plugin": "^1.2.0", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "hard-source-webpack-plugin": "^0.13.1", "@commitlint/config-lerna-scopes": "^7.2.1", "optimize-css-assets-webpack-plugin": "^5.0.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "f22ec1795e43d1a13e2d5121d1c72895acb3e0fa", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-alpha.tgz", "fileCount": 20, "integrity": "sha512-7Mx0Eepe9b40oS4lV7KJ974uUcFVDPUOzyTY+FLWLv2d4Wio4XKmxLaV6Ud4sTbdGhhCEN1JntbZlwHPViZdpw==", "signatures": [{"sig": "MEYCIQDd60fCDH211uADpd8TihNq2cB2Zf+7/Irj6J/k7cB1tgIhAP6vy1QN7Zba9o8Zk++Kt73IuAqLrsfbxfsxvFSpUVmx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIqGLCRA9TVsSAnZWagAASyYP/jfapli92M1yuZodhjRx\nwvXu2tLck66nOX6FbU3mEP1Byig63Yl0RF+B/SXQYNB3OU8bjDqiKnsBgljz\nllq5N1OotQ7oho3tzSlFf73mkUZhqLH99RrC/pc9FLZD20g/GfRiU0JxItEW\nRqXDLdia19M6ikwqsLriLjrB0HsVdP72D9ksdrcYGvSOQNLysAAOI1JCKPwv\nmBhcwS2l2W1gyv4mo3b6zm7mtW7NHYhYM1tLx385b7K/rZDo5FZ2SM4yWy0z\ng2Hg4Q2I+uB61ef9H/eFqkDgaWwIpvb5oxYXawSJLcTk6Yva1P6aR9+FiMez\nTlb9EXQNZzK8YrqDgAqOISnfRnUpGDcTuuzxEbFS+0jAzRqXEjyBkM7rl52U\nKMnqeB3PXHOkITxA8bpjxOF6kDmWZzu3vl1Mz0YeyHZaODs8TraHA8N+64UE\nrlGcY5w+R79pRFuZarFqmAznPsCuKpShrmtNKfn4hAdxFAM7uAT9LtrgryHt\nM6n5m7o/QpOk1SXLOlKlS9mDKVWez/tjgyEH8DPGtFaQhWtVTXLzlu78egKw\nDhjAgXMA0TApWB2+h4XONYkAyJjVVygLCoPuXjoSY9GI3U2EZfRE5XYrkS7Y\niVy6mnXFKJBTt/oO55BoIIcSB3VQBUK5rCMmirErGuMCPjvazdg9pg2Jf6mt\nFRoH\r\n=xY1T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "4.0.0-alpha-1": {"name": "webpack-cli", "version": "4.0.0-alpha-1", "dependencies": {"chalk": "^2.4.1", "semver": "^5.5.1", "inquirer": "^6.0.0", "cli-table": "^0.3.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "webpack-log": "^1.2.0", "loader-utils": "^1.1.0", "webpack-merge": "^4.1.5", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "v8-compile-cache": "^2.0.2", "command-line-args": "^5.0.2", "command-line-usage": "^5.0.5"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.2.0", "jsdoc": "^3.5.5", "lerna": "^3.8.0", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "typedoc": "^0.13.0", "webpack": "^4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "css-loader": "^2.0.2", "jest-junit": "^5.2.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.10", "lint-staged": "7.x.x", "import-local": "^2.0.0", "schema-utils": "^1.0.0", "style-loader": "^0.23.1", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "terser-webpack-plugin": "^1.2.0", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "hard-source-webpack-plugin": "^0.13.1", "@commitlint/config-lerna-scopes": "^7.2.1", "optimize-css-assets-webpack-plugin": "^5.0.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "18f7d60f43de8516a64cb59c10888acc91fb3e9f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-alpha-1.tgz", "fileCount": 20, "integrity": "sha512-D+o+iLrMaOmOSvYzQaja0/PUQA/ZpFk9rNfYplBEu1lil/Sal23wSJ+0spN2fGS7rEUtBH8DhT+/7XX3veeXgQ==", "signatures": [{"sig": "MEQCIF0C1CS7uiQcAsXUzNTcBlnWr71meW3S9ZktQuvUhTIgAiBN20DgC1xSlbndDPLPwKyt/Sjtp7Csbah3DF5szdfxOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIqKXCRA9TVsSAnZWagAA0XcP/1rjOcKTY3IDsI0bbtlp\nwhoWrtQJ/yBJcO/pMOjQQrBDw0aLOjSWH1Un5BEm+tCUWMEerXEI0Rp7y7L1\ngsShl9BTTcA9N7MKyYEniT2kOWkispnlXnGUhrTx9YekEpIt8sAROqa4rHmx\nZGuZxYfOpA6NlRNKPPgfvBCm19NelD6phSpAv4l84SuINSQx/wm7gp1d1I2N\nmogScL9TihOJB4rKrZ01FRYy8OldZbtY3UW1886qaJ/hDmLXgVGH3cLbFjSc\nCupzRjRdLX1MxFlPJ8hvR15nqNtWBUw0lt+sOkHBKPl1f1zIBCuqL8jAadBi\nXyyDS9ns7TDZTDLKaFDi9kO0yzfZGPowDLTe92FwGNkcxQQ8sjYJcvTNBeKe\n29e2w6kqKKxA7eVVljDXK/lJ3Vmvkg1WMUUpaEwj3ktMdJ4nW15iO5KWppc0\nZsMnUx1DrU4QU36WzTrOHMEdvmpDMT4oCZ8/wpdIDyzIT0SZ1f3xznKQEc0S\nMTEA3L8gtkyESk5giwvFOhqpxbFQBJKaSJpP6hpANGUWGdEv4xhXSXwfeHCQ\nyjY8kw2XVA01rAY7eqltF2n8lE9/gIkCp9zBDWcJ1ICpoh/OhWH834JVMpHL\noyFHpusucQzNJjfodCb/GnwRZcPbXdw+CwiDNsqd9Xx2zncgEo1cDhbAiXWO\nhHdc\r\n=gWc+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "4.0.0-alpha-2": {"name": "webpack-cli", "version": "4.0.0-alpha-2", "dependencies": {"chalk": "^2.4.1", "lerna": "^3.8.0", "semver": "^5.5.1", "inquirer": "^6.0.0", "cli-table": "^0.3.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "webpack-log": "^1.2.0", "loader-utils": "^1.1.0", "webpack-merge": "^4.1.5", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "v8-compile-cache": "^2.0.2", "command-line-args": "^5.0.2", "command-line-usage": "^5.0.5"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.2.0", "jsdoc": "^3.5.5", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "typedoc": "^0.13.0", "webpack": "^4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "css-loader": "^2.0.2", "jest-junit": "^5.2.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.10", "lint-staged": "7.x.x", "import-local": "^2.0.0", "schema-utils": "^1.0.0", "style-loader": "^0.23.1", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "terser-webpack-plugin": "^1.2.0", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "hard-source-webpack-plugin": "^0.13.1", "@commitlint/config-lerna-scopes": "^7.2.1", "optimize-css-assets-webpack-plugin": "^5.0.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "21ca7c25291806b718b27850bfc654fb6170522e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-alpha-2.tgz", "fileCount": 20, "integrity": "sha512-J1DEcvdO1RneCnvaFONfaHZZLiZDjT3G6kS4KaDFu6nReVwWTaILIBg/r0NSTVQyQ9SRqjlEp/0QxlXlKL7IcQ==", "signatures": [{"sig": "MEQCICjBszIiqjmpNVUB5EgHVdS7bxVsKfaP9/VUUKNEmKjUAiAag2CQEUIRp9osT4BPFIp+kWTzp48+Bj1i5C/Sg5HSvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIqNUCRA9TVsSAnZWagAA9xoP/10f+HHKC6Nhs09UEzMe\n5Q+n4xBE2gP8kgqVrEmZozXiDajaxbNtfqNVildD7PWqHvRMAiE+OOdSKQUJ\nLRLZRq8V09Hl6/UwSzMWyNBtLkTgvqIij0bCVC9z71993FJ6b9GPe9C2+9+b\nGW0LI+f3dEg/mhhP/hILMwaQF0wElsTi5aVbuE3xDy0SCbS8FQHp/kZlux1U\nYSkpcJm6d7QpJs7aOqqvfa0k0iLeOcqO5c0lQYKJX59VbqrblNx+zHChMyTV\n1IYK8gD0S13THMxoAcGKEz3np46WvCFpuZ7gDLl/FbfNP22ijlWs19bsauMq\nOEETcMCRZ9YR5T/SXm05SMagmUgu2TK6ZDAj30Xk6VKi8lt3vmBX/Qe/TV10\ngKiYeLq7q7m+6yoVCUhKOuqePVqlsCwbJz2MSD3loKqjcUwiFROGbBCDao13\nCUz/nZGLV/4Ti2x3owBxjQXSpELjEHLAeQkzTE+OFighDsttfz5B4CmM4HiA\nt6YxQCxp3fZhnu/riNvCyAz5vz9nongh/qnRJD0UDbgt8m84JlzaWGi7jG01\nlf5XFKjNHGxzTxB2DDAPDyvBdBzU9HONoVEQCPq9UYsLy4LZmuyeWfhVgTcW\ntYIO/xx6xW4+FoJlNPHfSGLZ4gS6AmBsq6MPl9OalSKEBXWVa0FKQtkfsXIT\nczRq\r\n=THmC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "4.0.0-alpha-3": {"name": "webpack-cli", "version": "4.0.0-alpha-3", "dependencies": {"chalk": "^2.4.1", "lerna": "^3.8.0", "semver": "^5.5.1", "inquirer": "^6.0.0", "cli-table": "^0.3.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "webpack-log": "^1.2.0", "loader-utils": "^1.1.0", "webpack-merge": "^4.1.5", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "v8-compile-cache": "^2.0.2", "command-line-args": "^5.0.2", "command-line-usage": "^5.0.5"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.2.0", "jsdoc": "^3.5.5", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "typedoc": "^0.13.0", "webpack": "^4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "css-loader": "^2.0.2", "jest-junit": "^5.2.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.10", "lint-staged": "7.x.x", "import-local": "^2.0.0", "schema-utils": "^1.0.0", "style-loader": "^0.23.1", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "terser-webpack-plugin": "^1.2.0", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "hard-source-webpack-plugin": "^0.13.1", "@commitlint/config-lerna-scopes": "^7.2.1", "optimize-css-assets-webpack-plugin": "^5.0.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "86c6546b90a443ea0e76b78e47cc3b56cf1fb9b7", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-alpha-3.tgz", "fileCount": 20, "integrity": "sha512-RL4Bl5c4IsSIJUSb1Ei0qcKyEKQXZk39aSyum9VyFyWkfER3QCIpaLHxWzoKHFGaeD9NAhTRbTQFlMMh3fR4OQ==", "signatures": [{"sig": "MEYCIQDujutyh31jAxVxCeewzlFeEa02Abvtv7UjRinCmf8H0QIhAJB3PvYyeajIq2IQdEWQyAyJsV9dIhYJHgp9IWwxBJ0/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIqQbCRA9TVsSAnZWagAAbEwP/3YzY+YkuIsEKZTyyA4D\nVU9boRc6eRjveDO63s3NSdwZ6FKKuWPJm5Jt6vfs+T9zeq7PDEyxVj7mXnDv\n8ZIsVVGvxE3hwpI0xVVtSMYcZA9md+i2WPruY+rcmnEi1cdzAy8VCbo9l7fL\n4/owZcAlG86X5grVngxXvH92sPJYC813RN0kgMZggpsGPSloVkYXKirFTt/Q\nstZTVytOkc2vu1SbXFWrQT4fG2zmUBidQ6VHC13L4ydREfsgCafWZe3DkvON\n7urQ5kK34hhzdXmclVFl4slz6KvXF9BAXYJ2k+RvkdZlDNHBLxarjQM3FOkF\nz1A2YFjjYj9LKNA7cEUbV9shgnOqsZVVub1puxOLx6VV946FmotVTAAM/i0i\nRwam0hRfqDnWLHa4e9Wdqa0xdDnpOhhDgA0iyJWhlF1rJ3Hc0EHjFpxGSIO9\n4A0HZprV6dYkzazJC8uGmL/HJ59ARed3bH9kcQnEAYXvhvdKxIjlC3is6o52\nGau4hDQLYN8kanrr1X/naagG8L4RWHXtMX1uzvz4JPrnkMv8xQwL2XOLwj+o\nayY1BhBf4zuWs4B6HMiAguc1qHjBeWPPjSqhcNblUA2FZtYzTMuGOkzJ3NoB\nEQdkjFS+SQ6tUL/mEAR6Nxay/PJcIAe7sNzs60J0/aUUz5NAco5G7Kx/3u7c\np47J\r\n=TRuZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "4.0.0-alpha-4": {"name": "webpack-cli", "version": "4.0.0-alpha-4", "dependencies": {"chalk": "^2.4.1", "semver": "^5.5.1", "inquirer": "^6.0.0", "cli-table": "^0.3.1", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "webpack-log": "^1.2.0", "loader-utils": "^1.1.0", "webpack-merge": "^4.1.5", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "v8-compile-cache": "^2.0.2", "command-line-args": "^5.0.2", "command-line-usage": "^5.0.5"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.2.0", "jsdoc": "^3.5.5", "lerna": "^3.8.0", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "typedoc": "^0.13.0", "webpack": "^4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "css-loader": "^2.0.2", "jest-junit": "^5.2.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.10", "lint-staged": "7.x.x", "import-local": "^2.0.0", "schema-utils": "^1.0.0", "style-loader": "^0.23.1", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "terser-webpack-plugin": "^1.2.0", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "hard-source-webpack-plugin": "^0.13.1", "@commitlint/config-lerna-scopes": "^7.2.1", "optimize-css-assets-webpack-plugin": "^5.0.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "eecaa8c482e06a22eaeb0dbb6091b034d48b505d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-alpha-4.tgz", "fileCount": 20, "integrity": "sha512-wrZVWWe98yzJiP+19Fk+XKLC9tFMa0lrhpwvSTVnwPHz8uGr0KGF7CfAxhCWefPRd8rSqPQSRb3Cvu+qo1n3sA==", "signatures": [{"sig": "MEQCIDuG4Hzdooh7XixUovodB3fQlAEdHLxGfHYdzghDlADWAiBNlzNqVGOTisAOrYT+Jzhq2p348oO7XFfZw4Z/lxhNQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIqUWCRA9TVsSAnZWagAAd+UP/Ao7+qepgvIMzXJA2qlr\ndg0rGvbSvznV8TuIawxM5YlpqpG6osCEaXGVYlRnTghzaW6uZBzu6XAA2PNI\nznTzONZauwa30B1LmJa/FIaJayoyDGe5pZsnvGRm1vcKOqWz8yRyDVpS+MFj\nXkz8bKPJfKEtNl/9GV+tS9RmG/dCCrMjWTTvEtbzLkgxrU/dIkaUgaWAw3G0\n/fMKuFV71VnMF5I/yMQxDbg03biHgV+5pCpJTyE+leAzl4oIw2ztgRhEeM7O\nqrFnhBpKNvNW4AKNcGBoO4Usm4r090q3+sGuZCEXdsbDRYGDohWMZbLfafEq\nZ+1Uew3KICFvwRSUj+eicHx0ztQuIlEO2u2qlI0tHF9pYc3LAqVOvVLPrY3u\nxw4plsxWwOYxV+q3nKMlpcVo+Eds+Qkpk5jvt13JU7S1nzClS1/Yk9m/0UCo\nWkAZyV7hPgdoNtKubnl8KJKDeMZ6LE4fMpOkZsuAGGiriHuyBm3eYPF+mGuo\n1AvC0D2ijiPNr8aaSpaoMghm4KwV6y5PJYvLMPz0klx3zdSlHheONSd7gwD8\nzeaAe+kOHOlXCGPAKMTaMzczmlX1JlyHaPkFReAHjuE6QVck6es58CIpepj8\nZLbUwW7wEzRvfNx59EJpKAn/mzJ4hez3RRAM3p0pmUhJUOm9yXlbC5pEgZz3\nUbHF\r\n=Lpmh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.2.0": {"name": "webpack-cli", "version": "3.2.0", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.4", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "opencollective": "^1.0.3", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2", "global-modules-path": "^2.3.0"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.4.3", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.4", "typedoc": "^0.13.0", "webpack": "4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "jest-junit": "^5.0.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "9648cb6d65060f916e63c3d3b55387fb94c019fd", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.2.0.tgz", "fileCount": 12, "integrity": "sha512-wxnUqH0P5ErcwGIKMZbUqix2FjuUmhpS2N9ukZAuGmk9+3vOt7VY2ZM/90W9UZetf6lOJuBNcsbeGU7uCTLdSA==", "signatures": [{"sig": "MEYCIQD1ZIkF/uSzkIsvCxwnzNdm+33GUREZnoOyC61lWgwVJwIhAJTzHa1BGMcb4gutDA4t0/HMxErYE5lvSKMotv6EmyAx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLZgGCRA9TVsSAnZWagAAD+EP/iXSMTmLhBoN9CaNQaLM\nAFvxsiWRlaZitqKvunp5soGaedcHjirltEWnlzrBjem8L6c3IRFMIVAI9KB6\nDqZ1yJwxa0pUw38Pu1X1xkCK6KnY0NxL7Bwhd2BE7td8WtLXP/qwY1EbvlmX\n7SfCWYryqiAeuj+NZVdpZf1x1Mc2frPBmJcqqXuqUDuC1qKLuz+iPbXCWwhm\nQjejg0x1Cie9ArBb/LFjHmhwdTK9zY2zDPe5j4TVux9CM+Xv6ntYVXgCd7l3\nCr+1nFojJXTGqa8qYZoCL6AbuGrAdK2zADixhceIVhmCadVGFiCnDFHOQhDe\nzGEzcB2lvFaLfpS8Pa1hWG8Nq1SjySMgtkPftecrfs3PKS+eUISCus8vfnHa\ndO+sPXXZ/uwoW8bXjVDKSRFABZrgoReGTUep7VL+lMLuaHy3yqqtpmrmcZgc\n64smV/RMzbTYmut2A/5wusWP5Ih8gZyvwLtmt054H1ItTLkVTmqPVrDIuZvB\nVOPPLwAooVUaERyFn1NZJBs0ap1TvnHpdUkALglMTttlQmrwZ0v/M/MP9GIH\nGaXWco8UmMnMglI+vG/8vDM0F/oijxhA5VFw7RzzwEBW9kvJJOVUDo5Cqnw6\ntU1ocU4P5az2T/UM4woCSZh9jxfUxBWU1JHtA6PNT+hyybDQW9YTuSNUF901\nzssU\r\n=MNgt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.2.1": {"name": "webpack-cli", "version": "3.2.1", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.4", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2", "lightercollective": "^0.1.0", "global-modules-path": "^2.3.0"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.4.3", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.4", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "webpack": "4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "jest-junit": "^5.0.0", "typescript": "^3.1.6", "@types/jest": "^23.3.9", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "779c696c82482491f0803907508db2e276ed3b61", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.2.1.tgz", "fileCount": 12, "integrity": "sha512-jeJveHwz/vwpJ3B8bxEL5a/rVKIpRNJDsKggfKnxuYeohNDW4Y/wB9N/XHJA093qZyS0r6mYL+/crLsIol4WKA==", "signatures": [{"sig": "MEYCIQCao1z7TSkN1X/3MOSrMQiA4wH2oQocFKeVrky8mg9qjwIhAPY2f1nBiOY/NI+39nSVoLrKq8IyMhtm+dLNx3uxA+dQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcMw8+CRA9TVsSAnZWagAAaboP/iGiydNTm0yBRaKzzehe\nXnpx2IsTzfb4onNFSN9MWLfCx3l8R1SB8dC9C6RZmKS7jghQEIIBYmNYNXzg\nqktxSoo9lphoM7xY+yIwU4RZkPlq9X+23GhPgIpCf/EdO8AxHDUPThm763CM\nTxevfyrdsUrA8/WQ1a6mcXuWJq4leRU6MIExn8jlKOy6EVlO9+LZ6vH5qw3z\nkuwVAEX0NsmJSmSLOoGgTy3KUcXeDNCs4LhyF2PkvXQ9cOUa7HKQH9QluGtp\nu8HWBO8p3jFL/cj+ezccSKV4WUevsg9dunNVMyQX2exMMaWBFA4gFIzid7Rc\n5lP2ScZp2ZP+P4keaYrZXvUhK2v/eNDmPsQYBKQnfiLob6CCXmQwvJEZUFBg\nG06QsaDuq908cmvQciXnWyOpP7Nf0lPECdot6HKJFGutdKk+6Wyo3gFnshdF\nF5qyLz3Z0j3hRkGtQjplhC8wLMMkc593ZINznHUmEiVFE+3/LTkKoN1vUGD4\nfzM3VT0WrLWqMy09tnslNDzp9ldLcFMBbv5Y184kaJtkX5sHTBX59H5Kwayb\nRQpsX/bWxiQF1WERJkYQKo6MTE39uQ+QcLJ93SGhuecZQ9eY9S7Higlw7rop\nBpuAT1KLqvJ6irIJquSLwCQXkTzk7gPvaP44xRgygTAu9JcN0hZcGTPmsmYM\nIaeC\r\n=wmcT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.2.2": {"name": "webpack-cli", "version": "3.2.2", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.4", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2", "webpack-cli-scripts": "^1.0.2"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.10.7", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.4", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "webpack": "4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "jest-junit": "^5.0.0", "typescript": "^3.3.1", "@types/jest": "^23.3.9", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "1e6a638ff81d491d0bec7db11910a55450e4189a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.2.2.tgz", "fileCount": 12, "integrity": "sha512-RMYcJeEjNgmCi5c3UwpdWjy+QIxuN8W/u/Dq+FUxWScCwXpOdfSEhqKVov3GFbsB4cS9/hdciJ4Y36vgTaHMWw==", "signatures": [{"sig": "MEUCIEXYpwgbIN30WwxsQiyp5fDMUXOT94cwbTBh0UrN4QguAiEA+i7ZFjstJALmI4yoWQvnviHcbQolyPfnGTMxOETHU0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWPHYCRA9TVsSAnZWagAAyxgP/2M6/TNVCvjE9jZa1PA3\n7yEW41CwRd/0ZTQVuQEUttw53UgYJGepUGI8e+7gYtw+5h3yC0sw6HCcnds8\nJgSDkxBSBO3xzMLbcrF2swAOIbAuwvyIaLgMqctGNk9CNpTHMQvHQpJ6+JTS\n0QBb9arzPAVJj9K4ZKDSVwaMSS4cPGQDNOHQWzpdz3ABJo9w6u2sC13tCXZk\nuUXkklhqukHTVCxc4RWAxKc4xQJIUlurQAmVPbWleYkiWJJ8PXCOb/5PXLyq\n7fLdlqjY5+sh6PZGCe4vdnLMbhtgl9LSQLSTqL5aYXL26tf1LIk8foZ1ao2i\norRaNavVZ8nRr0wwbdMYxmy2B6fuU1tfqPlx64g9QK3FZl8/57fUWRTo5deO\n1nLZ4jBpno0ViMT38Gdty+h12aIJEchSwa8XKcGSlCBYk1/tE41RNrYMutrK\niyEBJ+b/eBTQ23vERFq2K/RbJqrRAg2ZFqnyRWBcXqna6CkaJ/7wlrilSdoa\n3S8fm5qkLc66ZMAHNWtXlGOPExYdas7WYDLLetC+8dGRGD7nVygJfHjJg5IQ\nLnTxrpx/t596LFUhnI4GxeZbR8JRGAeKY8sY4x5zZ6Q0nLId4ftHhwK4fsrz\nG1msq1T1BDfFy3+jJFWFUNwAEuQM5iIz6cbP8QBHIjvmwYfbMdy8DX/qumzZ\nZoK9\r\n=WBds\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.2.3": {"name": "webpack-cli", "version": "3.2.3", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.4", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2"}, "devDependencies": {"nyc": "^13.1.0", "jest": "^23.6.0", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.10.7", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.4", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "webpack": "4.x.x", "jest-cli": "^23.6.0", "bundlesize": "^0.17.0", "commitizen": "^3.0.4", "jest-junit": "^5.0.0", "typescript": "^3.3.1", "@types/jest": "^23.3.9", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.2.1", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "13653549adfd8ccd920ad7be1ef868bacc22e346", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.2.3.tgz", "fileCount": 12, "integrity": "sha512-Ik3SjV6uJtWIAN5jp5ZuBMWEAaP5E4V78XJ2nI+paFPh8v4HPSwo/myN0r29Xc/6ZKnd2IdrAlpSgNOu2CDQ6Q==", "signatures": [{"sig": "MEQCICTQ11fpiXkKr4FtYinHFl6Fm+DLyJ38ufI/mYE05ycdAiAXYaQUITvS4OaM9bNJ+mv3t2BXmGu48i8awBdxY7n5VQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWP5kCRA9TVsSAnZWagAAJ7MQAKOVrw0KCIqb1eeqfRuo\n4GMiqIzEw4+VAkZWLJ0H0RMjbOZMRNkceOAFMKhW+PU7ru4n+4Z+dIF+bKsQ\ns3WgFth5+86VV+9OJqdd9Pxk6GTQZFoTdhPf9506JYrl7rT9H9qOBG6654kD\nM9fErjXW4if+hzltYv9T8x/n4HNA3RfJbQ3k/Wj7tP631lYfmCQaHf02zXVH\nnl83rpSwvlu9vEP3QmY8Z2w9RMmjj2j96q0Orrn98828aBCqf2jJOi6vIKPZ\nePp4yIvvjuqzyByKLZ3+0fhcIBX6CYd2ML94ldrEyqrOsZgX6xG5H0CpKvgw\n421AAjdSBYv1Y5ivHSs1DQlw/ZDv143ogL7cR381THdE3lxm+nMVTv5JlG24\n9MNsdT4INkm/wk2SbITxhjqmnRFIZxo9EwIXdSyOlHS6W+oy2IZtysbAxBYS\nuyvqpvCxGcIBzNiry2yUcwJb9weIVz3zgn6/jw5gN7qvGyTAkZEgfytdxFDP\nX+mpk7SSBwNrMFQi0j5l9/nSXj+tKngSSoVqZgjD+PWmhmWbYcnMUa3osUA9\nC26CmDNxbMmrKV/kgFg4rywqC7RE+mpG5mmcbTMUb0q/e0leme3ihwQ49IUf\nxHRkeNBZpZJyY87lD19FCTklFRH9rqzfpKdoNNsazSLXwJDUKksxyJPaFEhL\n4p6Z\r\n=pOP4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.0": {"name": "webpack-cli", "version": "3.3.0", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.5", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2"}, "devDependencies": {"esm": "^3.2.14", "nyc": "^13.3.0", "jest": "^24.3.1", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.13.1", "eslint": "^5.9.0", "rimraf": "^2.6.2", "tslint": "^5.11.0", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "webpack": "4.x.x", "jest-cli": "^24.3.1", "bundlesize": "^0.17.0", "commitizen": "^3.0.7", "jest-junit": "^6.3.0", "typescript": "^3.3.1", "@types/jest": "^23.3.14", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@babel/register": "^7.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.3.4", "babel-preset-jest": "^24.3.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.1.10", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.5.0", "@commitlint/travis-cli": "^7.2.1", "typedoc-plugin-monorepo": "^0.1.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "55c8a74cae1e88117f9dda3a801c7272e93ca318", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.0.tgz", "fileCount": 12, "integrity": "sha512-t1M7G4z5FhHKJ92WRKwZ1rtvi7rHc0NZoZRbSkol0YKl4HvcC8+DsmGDmK7MmZxHSAetHagiOsjOB6MmzC2TUw==", "signatures": [{"sig": "MEUCIQCZWBWW6khVKvuEPvhWXNYMCcx7C0t8jApU8RRtr5HDRgIgZnChz+/UoJx935pn2tkVnUxBAFQ4v4OmvwNomLzN2no=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJci/V2CRA9TVsSAnZWagAAFxcP/1U3CnD3djdosf8g84R1\nxz0S9Ck+g9tPwmaDq5UJI4Nxo3NjebK6VVTihiR7QfvsQ2QRqob11Fu4ibh3\nYRvHv1W9tgpeRe/tFfrCRdcUMHAsS17gqRL3IfIpBCYZCC50NmdjNM4oFqPJ\nbTsNRhrBtJuQxkNS76PJkcYfksRmKcfg/zTEGv4a0MdO8DfDD4YnjZFQ38WW\n30qxlVuu5eW2NujwQWZljp2nTfdO4Ty7IFCAsGoj4cLHIIy3jBYur19vmytp\nilnPvfpkWnas4yMQFNs4rR5Mbj9Tt8xl4mMj/HnaDAxKLCxL3p8CBDKFY0Ah\nLqkKFmJ9JXwV5/n9y8aRGw3SjNgcgE7dNZjX1fNxIAG82C8ThbsEtT/uWGGR\nDsXcjFyJtXkazD7fleiEY4IYE8bJmZ+MVO8sfhtYtLB0MBLr+htEXUQHRkXa\nOSVtniRoQ9hpfxwe26VaHJyWVXHM7UpZvZsUIVk1kEFCLjOG9oPLMdHObv3x\nMR+vLEbCl0veJEXuU3fKImnKq/+73HFn4hD3+ubddu+fERigqm8Vhb6vHL2B\nST+v7qKXzZWizKiav6aEmbfW8M/0tbVt/EWhHT1K2f6gyG+gdX4TUpMQrHkD\nzyorASbidHiA/aQZTCpzOejhNi5rpCdnsoCsbZhYUjJWxFkeTQvMcVfIDGhg\ngUnH\r\n=Rl//\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.1": {"name": "webpack-cli", "version": "3.3.1", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.5", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2"}, "devDependencies": {"esm": "^3.2.14", "nyc": "^13.3.0", "jest": "^24.3.1", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.13.2", "eslint": "^5.9.0", "rimraf": "^2.6.2", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "webpack": "4.x.x", "jest-cli": "^24.3.1", "bundlesize": "^0.17.0", "commitizen": "^3.0.7", "jest-junit": "^6.3.0", "typescript": "^3.3.1", "@types/jest": "^23.3.14", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@babel/register": "^7.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.3.4", "babel-preset-jest": "^24.3.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.3.1", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.5.0", "@commitlint/travis-cli": "^7.2.1", "eslint-config-prettier": "^4.1.0", "typedoc-plugin-monorepo": "^0.1.0", "@typescript-eslint/parser": "^1.6.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1", "@typescript-eslint/eslint-plugin": "^1.6.0"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "98b0499c7138ba9ece8898bd99c4f007db59909d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.1.tgz", "fileCount": 15, "integrity": "sha512-c2inFU7SM0IttEgF7fK6AaUsbBnORRzminvbyRKS+NlbQHVZdCtzKBlavRL5359bFsywXGRAItA5di/IruC8mg==", "signatures": [{"sig": "MEUCIQD5t4J6f26lmt9HgIpe/fZje3nMVD/BBdwycfgNZ7ulrAIgINsOn4tV2Tb+ngLH+l0en1UFLRNdhfRaJQutejGtHzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvLz9CRA9TVsSAnZWagAAU/sP/3RBLEvAlp9tpScxZri3\nDHLBbZ6+f+rQaz+21IdAW/FDRDmcyjNfi8Z06CiWGuEQ8BpGCqvw7yLUmie9\nClj6vhxf9cxbtFa72hJLwijs2MoO+8Vu56+1lw7jo/LXVT7ii0oKuZ5DuGnS\nlf+sLdVGfAegz1AqDAfTmD8jzdctIaEYDrqZUzuDvHneqpPL+tPSyBumAh36\nli6WQDGKQoifQraqKBAcGYDWq55jD+7r27KXyn3SnwIrHgBDHHmXzbvVgiHk\nnk99DLu4o0bLmdTpzWOsfxtpK9e2F94BpvqkiTLmJKzq/ICoKoMf1XsJbGA6\n+1RH1mVgoKEy1udriKfd+ltr+bsbSMsb2ww10QtZKZqj61m7q62CGnNm1sgT\neUm6fAK/0kSXNgH07gCYXaluvkoUxybxlv3RzXL+NXoBUa973drPfVDPrw4K\n6+vSJXpD/fqRybkjT2zx/BRGG8jt3JegvSNWTwzJqv2COwdmIJANsoHlafXp\nlaGOkyLTj6WWoWz6gs0YI07XCARFR7KUrEwva1mpT+S6X5gA64AAkRLTUSSj\n7M8jJUVJgUTE8u0bvrBi/ERKbBx7tlIbBvc23KiEVZJNELyAPNPnLZjz+M3y\n/93OIV6QKOPLsO/S5yi5dPbVR8OvV0QLJDnMBHAsmMvL5HWi31VDqOEBW7Dj\n81GY\r\n=p4oB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.3.2": {"name": "webpack-cli", "version": "3.3.2", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.5", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2"}, "devDependencies": {"esm": "^3.2.14", "nyc": "^14.1.0", "jest": "^24.3.1", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.13.4", "eslint": "^5.9.0", "rimraf": "^2.6.2", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.14.2", "webpack": "4.x.x", "jest-cli": "^24.3.1", "bundlesize": "^0.17.0", "commitizen": "^3.0.7", "jest-junit": "^6.3.0", "typescript": "^3.3.1", "@types/jest": "^23.3.14", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@babel/register": "^7.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.3.4", "babel-preset-jest": "^24.3.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.3.1", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.5.0", "@commitlint/travis-cli": "^7.2.1", "eslint-config-prettier": "^4.1.0", "typedoc-plugin-monorepo": "^0.1.0", "@typescript-eslint/parser": "^1.6.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1", "@typescript-eslint/eslint-plugin": "^1.6.0"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "aed2437b0db0a7faa2ad28484e166a5360014a91", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.2.tgz", "fileCount": 15, "integrity": "sha512-FLkobnaJJ+03j5eplxlI0TUxhGCOdfewspIGuvDVtpOlrAuKMFC57K42Ukxqs1tn8947/PM6tP95gQc0DCzRYA==", "signatures": [{"sig": "MEQCIEA+zXMBCpo8zX0UO9p68wQ4hJR+zu/xeKQEQ3DySRswAiBk+5rgtEpU+83W2oz0UHXf1b1LAXPCIQLBWIH9f/fZ+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczfG0CRA9TVsSAnZWagAAO4wP/3BtyeRPyBT1+gA3SdCe\ndbWhFi+KkFbE9asNUF/3uZYI1UBAkmPPfis8ybt68FnKD8PGYEqLCHk2IvGy\n4Osxbb84UMI7UOkQ2noiZb2irp4fi53v4Z4F8WlNUDFiV9eZGman2mNamssD\nheYNsacftP2PljVvqrsuCZZYk+WQNvcsbHDyFSTtxqOPxpl73TNTWDGYFZsO\ny0pkuP8ALz3pkgsHsILjAQPYfblpMVDngCLS+cyrKN26QMtL87Jdc4q6YAN2\nYEsNR6vHNyEqljeQ+LUp+XRApGV6HwBw3ikCcNxoyWHQ1ufhztM0p4X4Or+g\nho7zm34N3Mr2h08g6d+sDC+96bmbd4EbY97KCeMIaOLwOybcXvWcMeqSrgSw\n83o4XDK1yHYzNpDc9mCKiIBq6QydnFLzFNfXYalsNoubrZ5Cd0rJuz5AvgPq\nsqd3r015CogLGzQeOo2JGJuDMXvo0cRSMq/g5djBpA+HAZF0j7USJs2bR1kx\nR3MbMUdZdnC6WhI9kA10HPd+3IhPFwqKC41YTQ0EcwoGHKE1NL7QYATVk0iE\ndZIzzQP6HIcw7nHrDD6QhGA2shFVI8C8V4X57/v/wZUwD+dnJnt2vBcZBJh9\nWS3Az5o+K2vvbX5JZqjdacsyVsgigIk7vtApuBeWZTWkmaHO93wv0V/qk9D5\nvH0R\r\n=NKSd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.3.3": {"name": "webpack-cli", "version": "3.3.3", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.5", "prettier": "^1.17.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2"}, "devDependencies": {"esm": "^3.2.14", "nyc": "^14.1.0", "jest": "^24.3.1", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.14.1", "eslint": "^5.9.0", "rimraf": "^2.6.2", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.14.2", "webpack": "4.x.x", "jest-cli": "^24.3.1", "bundlesize": "^0.17.2", "commitizen": "^3.0.7", "jest-junit": "^6.3.0", "typescript": "^3.3.1", "@types/jest": "^23.3.14", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@babel/register": "^7.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.3.4", "babel-preset-jest": "^24.3.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.3.1", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^7.5.0", "@commitlint/travis-cli": "^7.2.1", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "@typescript-eslint/parser": "^1.9.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1", "@typescript-eslint/eslint-plugin": "^1.9.0", "@strictsoftware/typedoc-plugin-monorepo": "^0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "8b7587dee369a838eb4722f6cfa711c779011e5f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.3.tgz", "fileCount": 15, "integrity": "sha512-/qBxTvsxZ7bIFQtSa08QRY5BZuiJb27cbJM/nzmgXg9NEaudP20D7BruKKIuWfABqWoMEJQcNYYq/OxxSbPHlg==", "signatures": [{"sig": "MEUCIQDxsHHPQXbuAU/kA54/nnmwsW4X/BG+lknvXxMoJVxiKwIgegO9AWe60kWAqvS9JYcYeARIO2s9AjvSc4/LZdMMAjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+lD2CRA9TVsSAnZWagAAZboP/2z7JcnQcCNAi/MH4NRL\nP5eLr+vIXBWBpghal3SZ9QxVsCXN807TfNjNeaOHuLFDtZX0gkuQbH4BtmpV\nDgMEAZDP+jWJSsENcT9Qw2tLp35JSeVbhtRt7RON6Ii5N2bvHXUx7R7AgOPw\nZFmWbiilj9o/ggBSU/7csnKgrltE6aRw6vUpx2WrpBEpAzZbJvUPG35wKq4J\nZkdxRUNJBPh8J6NTLALbhxWzieJfzjtYdfAzNyzPI0GKv1nTMtzqTm+IRQBa\nJO7wgmJulWDpy3NKyeHmM3TAa+R9AYorqvg4lWva/7kdJL/U/zTgkS+oYh1u\n58/Wn4i1oBDXQT8UG8JYHwuy8n8qZevBujV0cfmDSaLiXyCQ0fcLnV9KDRxc\n442kM3l5as/smtDExk6nSzgnBIiV9yyXdwyzjvjQZCxjUfGrIfUKL+xXGoNK\nLAIkKfyVmXh+R+MUwtgc3QlcLZxlwdwVc0XxVSGt/3tNT/3hvj2l4apbAvkz\n+3uzubsoxK8oKXprNHSVIu0mGMLGshwJ2S2Ps3KAJMesYXJNxlyUE/XxKAy2\n7zBve+HPVwLjfux7bGfpVtgxptXNpI4iA+ZkZLgj7dh0WMxJ5a5pNsySTEPY\nZVDJubgSsnl7IY46e9wsD2OC5WzoYwsrMRUJP9nbtCIljLe91KN3jWhnssND\nPmwj\r\n=BY7q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.3.4": {"name": "webpack-cli", "version": "3.3.4", "dependencies": {"chalk": "^2.4.1", "yargs": "^12.0.5", "prettier": "^1.17.0", "interpret": "^1.1.0", "cross-spawn": "^6.0.5", "findup-sync": "^2.0.0", "import-local": "^2.0.0", "loader-utils": "^1.1.0", "global-modules": "^1.0.0", "supports-color": "^5.5.0", "enhanced-resolve": "^4.1.0", "v8-compile-cache": "^2.0.2"}, "devDependencies": {"esm": "^3.2.14", "nyc": "^14.1.0", "jest": "^24.3.1", "execa": "^1.0.0", "husky": "^1.1.4", "lerna": "^3.14.1", "eslint": "^5.9.0", "rimraf": "^2.6.2", "codecov": "^3.1.0", "ts-jest": "^23.10.5", "ts-node": "^7.0.1", "typedoc": "^0.14.2", "webpack": "4.x.x", "jest-cli": "^24.3.1", "commitizen": "^3.0.7", "jest-junit": "^6.3.0", "typescript": "^3.3.1", "@types/jest": "^23.3.14", "@types/node": "^10.12.9", "lint-staged": "7.x.x", "schema-utils": "^1.0.0", "@babel/register": "^7.0.0", "@commitlint/cli": "^7.2.1", "cz-customizable": "^5.3.0", "readable-stream": "^3.0.6", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.3.4", "babel-preset-jest": "^24.3.0", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "^3.3.1", "prettier-eslint-cli": "^4.7.1", "commitlint-config-cz": "^0.10.1", "@commitlint/prompt-cli": "^8.0.0", "@commitlint/travis-cli": "^7.2.1", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "@typescript-eslint/parser": "^1.9.0", "conventional-changelog-cli": "^2.0.11", "@commitlint/config-lerna-scopes": "^7.2.1", "@typescript-eslint/eslint-plugin": "^1.9.0", "@strictsoftware/typedoc-plugin-monorepo": "^0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "de27e281c48a897b8c219cb093e261d5f6afe44a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.4.tgz", "fileCount": 15, "integrity": "sha512-ubJGQEKMtBSpT+LiL5hXvn2GIOWiRWItR1DGUqJRhwRBeGhpRXjvF5f0erqdRJLErkfqS5/Ldkkedh4AL5Q1ZQ==", "signatures": [{"sig": "MEUCIQCE2UYXhiMuvP9lOtx/YWgf6+Soe9h8kIm5/2c7s1gRcgIgDVGjeoHf+vl091JwUPcAF52PKnB4bvDPZpuUjb3Baqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/2ufCRA9TVsSAnZWagAAb14P/3JRc8h1LuS4ZsS9wVDy\nGu907YkGTsZ1xi091cwfMLaIlg2C/1TJF9acwkQWYfiRqQmSEseVJprYwEgP\nQi/K12/PO8yHcts9HbGmlMZY9Qco5X82SCcyELX8H4PFJKMtYivfJG4d/zJe\nQHQVqP5Bv4P/auVCtcLmgYgOFia0JMTqU4ZI8mCKgEjUm1N9COm0O+DZNqYC\nlhRM+o2IpEhlzcQ0tX9160JbLmLTksNErOswVr6EQ7gBfOC2f+Xu+MWnbKqr\nDkUL2+Jivs0w25BA61SaFWMHtD07o4yoo1EcZxLYE25R/4AAkNpAaOjdFqje\nrnfoapM2ppVP5icxysv4+q2vLaU4QsRs+0BkSLCa25ZQfiM+vkrsWte/NjY7\n9ybfmUNtsd20BZWoQ51vZXFgmK1TfV/fW8TxAuuGpBbW4c+lLtJQ810Oczxc\n4XlHUKMSRgbdNl3G6WqNYPlOw0cO5/pWdnAXADubdZ6WWHxxIeMrV0m0YXLO\ns0s4DmkQcdYJh2nkXZK+g7ooB0Xzxl6hME1Ps28wRU3Flf8xDsTDiDTYy219\nkMBb6YlJV7GSosMfQi5fSe2I9v8VzfWzCM3iqpI5w8z4PRMiLwhchILuxbpp\nh4ArAqP9dfRYua8q7LCqgcAOx10u54NeMv4+deNfG45iiD5kOIgPwmuBmoWh\nUi1R\r\n=u4Qu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}, "hasInstallScript": true}, "3.3.5": {"name": "webpack-cli", "version": "3.3.5", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.8.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "typedoc": "0.14.2", "webpack": "4.x.x", "jest-cli": "24.8.0", "prettier": "1.18.2", "commitizen": "3.1.1", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.0.15", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.4.4", "@commitlint/cli": "8.0.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "1.7.0", "@babel/preset-env": "7.4.5", "babel-preset-jest": "24.6.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/prompt-cli": "8.0.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "f4d1238a66a2843d9cebf189835ea22142e72767", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.5.tgz", "fileCount": 14, "integrity": "sha512-w0j/s42c5UhchwTmV/45MLQnTVwRoaUTu9fM5LuyOd/8lFoCNCELDogFoecx5NzRUndO0yD/gF2b02XKMnmAWQ==", "signatures": [{"sig": "MEUCIQDuLylOY+6l5rgNpsve19tldTl5MpzCRLCKZCXNo4GT3gIgZaQhp4uG6mJ5/mSokx9Ts40PvTnP9iyRi/QQphwISws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdD3RVCRA9TVsSAnZWagAAseUP/2EUmez0L3KUAzMJXgXe\nJnZFEpGLpk6rfaBmDRuDS0/F75Sv9dPY4QAyD+EQffr7S1RHH7PgYf82qIBq\nyFmOH0KbogGXpLDIVzn6MUdgom6kUJ3NNOgknZ7xNxEGEwSM1l4wOx6Rj6Zy\nkEk2OI9kUrcFrEMEgYbbyycrt/LlkKEJ6MW6piGFhv9Wy1rGweD9hc5Li5wa\nekfHr1pXXlUf+wZjTUxSiYzw5dHRs4NLrm2REZ+TfA4eHobfTYHgAQInpYP+\nQQGex0hS8CfB0ePdWFVp0fSFLoOu+qP5tBJ/l06ZBVdqsX89//OjR5xt7zdm\nBXAHRaWvco7GkH+2lh2asixRrtAIfU2hCQTD7MLdQrLJpcGyc0l+u4YD4RCy\nrDTuX1oQcRorg13SXr9eGhrUHGcsTK/sNJ+G3gXdKX/t9OyY+YHOYBv+9cV4\nqLefk6El6cTSxjQaPyAO+Q7ZlmcZspS3tOzxixOdQLD/joAPp8swsKNibV7B\nS23JflrXt0QsbXQty25ZIamTOLuhP07tjQgPset7R5cSmnsiZGb8XF7ZhIXU\nNvEnohUsk3vFN27bbkcbVQbgeyDHtnfTEIbjRHt+7JF79YLGFfgOTipUeC/Q\nnqQw5gnOWNATetl1SL1iBQLlH4KGwbQUfV4TVmRCWJZv6ycBLqkri2Xkp+b8\nqOvb\r\n=FZKh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "4.0.0-alpha-5": {"name": "webpack-cli", "version": "4.0.0-alpha-5", "dependencies": {"chalk": "2.4.2", "semver": "5.5.1", "unionfs": "^4.2.0", "inquirer": "6.3.1", "cli-table": "0.3.1", "interpret": "1.1.0", "memory-fs": "^0.4.1", "@babel/core": "^7.4.5", "cross-spawn": "6.0.5", "webpack-log": "1.2.0", "babel-loader": "^8.0.6", "loader-utils": "1.2.3", "webpack-merge": "4.2.1", "supports-color": "5.5.0", "update-notifier": "2.5.0", "v8-compile-cache": "2.0.2", "command-line-args": "5.1.1", "lightercollective": "0.3.0", "command-line-usage": "5.0.5", "@babel/preset-react": "^7.0.0", "webpack-bundle-analyzer": "^3.3.2", "babel-plugin-syntax-dynamic-import": "^6.18.0"}, "devDependencies": {"nyc": "13.1.0", "jest": "23.6.0", "execa": "1.0.0", "husky": "1.2.0", "jsdoc": "3.5.5", "lerna": "3.14.1", "eslint": "^5.9.0", "rimraf": "2.6.2", "codecov": "3.5.0", "ts-jest": "23.10.5", "typedoc": "0.13.0", "webpack": "^4.33.0", "jest-cli": "23.6.0", "bundlesize": "0.17.2", "commitizen": "3.1.1", "css-loader": "2.0.2", "jest-junit": "6.4.0", "typescript": "3.1.6", "@types/jest": "24.0.13", "@types/node": "10.12.10", "lint-staged": "7.x.x", "import-local": "2.0.0", "schema-utils": "1.0.0", "style-loader": "0.23.1", "@babel/register": "^7.0.0", "@commitlint/cli": "7.2.1", "cz-customizable": "5.3.0", "readable-stream": "3.3.0", "@babel/preset-env": "^7.4.5", "eslint-plugin-node": "^8.0.0", "webpack-dev-server": "3.4.1", "prettier-eslint-cli": "4.7.1", "commitlint-config-cz": "0.10.1", "terser-webpack-plugin": "1.2.0", "@commitlint/prompt-cli": "7.6.1", "@commitlint/travis-cli": "7.6.1", "eslint-config-prettier": "^4.1.0", "typedoc-plugin-monorepo": "0.1.0", "@typescript-eslint/parser": "^1.6.0", "conventional-changelog-cli": "2.0.21", "hard-source-webpack-plugin": "0.13.1", "@commitlint/config-lerna-scopes": "7.6.0", "@typescript-eslint/eslint-plugin": "^1.6.0", "optimize-css-assets-webpack-plugin": "5.0.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "5c7ab43d7a48f927fb77795800786ce0af17248e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-alpha-5.tgz", "fileCount": 22, "integrity": "sha512-Q9l1pPrF091SrtPt1iWtGAK0dYPa0nxhjy21WEQ70Tw9V3uqWFPgcMRUkw2btH/YlOUQQioi9FEExsBu+QbnYQ==", "signatures": [{"sig": "MEUCIQCzVvDuuDnSRV9XQg/P3sjFPIjFJn0o/zlbUm0d1dM63gIgNnCoZwku8qPQiPDtwVaSzEt4Dtr2cDAt5SgEepK305k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIzeTCRA9TVsSAnZWagAAFg8P/3gYbSMnjMi9/t78qOzz\nP8JCwKlDN6D4MfbRXSp17cQKwe9a1q3Al5jub1IjpyGXAUzGR+xl66kWo5FQ\nk7gHyx2AIahINKwDV9ck72BhMENEHhTDD+Xo7HCsghFagdgNFsis261qFR2P\nZCfM7NeF4f8wZf3lk83d7KQ756P06ifZ9S0GL7GZfXcOOEaHKOejVU9pvsgG\nhc2v/MedzRu6+aA5XTFekvPiPpPEY2gU6vOrFZ4DbrjRZUTrwnqpxECClA+B\nynsYcYyFI0OXfwE1kxCFU3vmTIlOHs816B16Z+xcCkbQZhJrqvMYU2PhISH0\nl94vTAqv8Ow6TP4kBacWXVoSD8wQ4mmKvW41TlvcZhkbEp0STvG+ShgygqVG\nhaKHvl0ktnWN5mOLmrwxc0WKJLU52DKr+JorJmy9//hjI8/wGR1/t9OZRIbI\nLPhE4+i7ZBvhPJMquY3l7nobnenVH+PLg1DWE/P1p1OIJh8OWLqgXkJSlaeF\n07FcUhhun8oQLiNM7y4ccax5v+xBWJFwXH6T/eLZebMX1ah7Kv7k+s51mB3c\nnOAgQxInU/r97R2z621FOfNWslej3ilSJd1aJko4s4PzeazmQh2OY26a8KH/\n+xSUBseMwgtP9yNpSk1kD5/Andfi8c+gaB0runs1JnXrlwqJ451qTHKvncNY\ncC7i\r\n=bJmF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.6": {"name": "webpack-cli", "version": "3.3.6", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.8.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.8.0", "prettier": "1.18.2", "commitizen": "3.1.1", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.0.15", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.4.4", "@commitlint/cli": "8.0.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "1.7.0", "@babel/preset-env": "7.4.5", "babel-preset-jest": "24.6.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "2c8c399a2642133f8d736a359007a052e060032c", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.6.tgz", "fileCount": 14, "integrity": "sha512-0vEa83M7kJtxK/jUhlpZ27WHIOndz5mghWL2O53kiDoA9DIxSKnfqB92LoqEn77cT4f3H2cZm1BMEat/6AZz3A==", "signatures": [{"sig": "MEUCIH4dWk2opWuMM8uun34pIDqxXWLzQKX3Vo/9F3HKatl8AiEA/L8Rbcf3Gn3l0jVBgk638oTrabKLeEzBo4W+zFshNaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKyhJCRA9TVsSAnZWagAALm0P/3U1KU3oefne4GzUTuPw\neOe5VOvhFIH/fNdoKLPh1bZMhU+efqExBn8u/X6v2QCRLO6iB4tveMxslEPM\nN/SxfHrhTiJNfYTH/NFdXFwDjbVcE7jaGz15IBM08PE5jvy80FOk7ZsYMu0v\nVEDCnWvKw/F5HCSiZaGCpXhme7AUafKTO4Ezf9EHnYPcJUFiJO1pR0lynOYP\nJvPWc7VqXvRl1HvcylQFNGCoPgk7PZoZBrKRrK/4ZWVb0HLj6kn0VwaZtaQv\nIfgPZRhCp0L4rDV+PRAyLg5+x7+UMtjPXbkqlPsiL92dLLmrXqNhO2Azuigk\nUPER+gpXP+GG5cJfNNnRMDVM3EX1OMgSKDgLGtonZqn6Y52jF983USDEkCn5\nXdNfkPGEgYTdeariL76f6hz1GdY72kEKb1f7hrDjgR/FxFPonBK8u77Ub5Od\nYlFGH8ATECqsc4YwzuwvMAJ3WOGDKjhbCW98hwhgdJS2AsWAgi1ntfVKh3+g\nBQ5ISGgsiA4EviNfZApP0XAuNLW0kcAr2c5KFBGJBmqVUqrVWMAjNje0J/sa\nysMn9cjxBgxyRUJyqL7CXgbknj27n/77ChHYTu+XF+2iVYdAk2nix+278d+z\ngLHJ4RM+AWnHwhOL2orRjp6skC8bn7cmjMPxlEGVTWsr6nGIT/EPXSvvVTPh\neifG\r\n=zKG4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.7": {"name": "webpack-cli", "version": "3.3.7", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.8.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.8.0", "prettier": "1.18.2", "commitizen": "4.0.3", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.0.15", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.4.4", "@commitlint/cli": "8.1.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "1.7.0", "@babel/preset-env": "7.4.5", "babel-preset-jest": "24.6.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "77c8580dd8e92f69d635e0238eaf9d9c15759a91", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.7.tgz", "fileCount": 14, "integrity": "sha512-OhTUCttAsr+IZSMVwGROGRHvT+QAs8H6/mHIl4SvhAwYywjiylYjpwybGx7WQ9Hkb45FhjtsymkwiRRbGJ1SZQ==", "signatures": [{"sig": "MEYCIQCJakbU7DGpP9AXKTS77/LOPmLxwAUioA4/UvFgLKHtCgIhANAI8T6239525WsyT9X0oP5s0RBIt8vOZhDB39oXJ765", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWOMxCRA9TVsSAnZWagAAv80QAIjqW474GQWuQ/FRklxH\nG06Zf3uPXtQ++k0U6U3jr1DWiC4bnRnSrUSunV2rJapu5QGSMIPl/UbZFJ7S\nqiNQHwfLr2vffqf0zycC66yUVbN0cK0lOB0mmxXjFGVRgHn7OgVn5gUuaIA5\naexmwm9gKMSz6Trzqtvp3IMpv6c1DF8TvjxtgD7HCpJ56Kryd/x09NuWgkBr\nUaq+SVsI95PmWkIv4TDIcOp/cSPgEL4gFvk/cVlJlXdwOFIRnKs04BEUa/uH\neUEZV/3AF/OI8MyhM5482Nj7OBOeU2KIf+Z4wCf7uq96l4tHp6mMSVW5gkCl\ny2KQT+J5Nq//UdMMLgs0rE5tTZ3M6VIoPg4a4oN5bggLKplcXxHdCMxVlKkh\nMHWFlIASE3FmGA/x87rxwyHPHvx2IE0Kho+1mUdbSMcO1BZTwgVzCo1kag8b\nwbBDJo9Hvrhj82yKvoSPNTMdPzEHnWIGhsUhNv17iJMdClyhb2Hhll2WF9EA\n14K/j3Yste+ff9QlNPehP8AIJN1N+1ipge5Wo2PXq4PffViYvPmqzWK9saJX\nufwlE2KDB0bcDW6cF9RsyowmZe+huEan+D4xRBwurEhSA8slt8lRAgMWskCg\n7KGd8azepzJVQuMhrVf6rZgUeseyRu/kjWVB5nYPHCQtsq0juhC1pkBaMBTy\nCRZv\r\n=BUoc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.8": {"name": "webpack-cli", "version": "3.3.8", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.8.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.8.0", "prettier": "1.18.2", "commitizen": "4.0.3", "commitlint": "^8.1.0", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.0.15", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.4.4", "@commitlint/cli": "8.1.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "1.7.0", "@babel/preset-env": "7.4.5", "babel-preset-jest": "24.6.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "caeaebcc26f685db1736e5decd3f01aac30123ec", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.8.tgz", "fileCount": 15, "integrity": "sha512-RANYSXwikSWINjHMd/mtesblNSpjpDLoYTBtP99n1RhXqVI/wxN40Auqy42I7y4xrbmRBoA5Zy5E0JSBD5XRhw==", "signatures": [{"sig": "MEYCIQDJA7m+2WaiyLu3krW0ZPXRXSqo1qgtlwXUv81eTDM5KAIhAN10wh2EErGrLy557NnFZlnxGKD2hsU4HOQvRaQyJUKU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcT9MCRA9TVsSAnZWagAA0ooP/irlx2a4/jC215AQazcQ\nl7d11iQ5L00gFM2TS3Va4Orpd61AkOpJU/E1IhX9LD07XIkm5LGd3eE73d1a\nFIQGpd1u+RIodr2/CazKQ8jjiISbwSnu+Xb+6T0vJJB8+gegya8HUobTKfxX\nLsE/zZ+vKqTAHjLXydmSY4CrLlI6DAzXPrT/kNmc7/fENpaq2n9gS8y2MDIQ\n3lIoLmrLzGHb0sYo8wpe2IELzCXcwEXo0b+6gADszy2oy137uUkXdFrJZYuy\nJfwWPGyVEFB+Dro6taqsiPTH7ydlLg15W39cPjzbeM9VEG+tkRg+NF31cJEv\n6q7AM0XeDZcww/4pF4xwb7+N3zKxnm9xIjI22N/DtRJKlVGL1AkmKCXhhIbx\nls/wRfaxKLbhpinKVXhG4pCffMYH7F95yzb9gXrZTS/wp6+VpVDhG9H9CD7H\nncOQKqDJTfrLdEoxj/JEVoZuLzS3LlG6OzgNAbEA3knn5/SsBf6s+MBTdQEr\n6luXxI5ZRU7aJADSmGskShu3tkR5dB/NUaTukx2skKVErhDrKghiv9Jxav8j\nDrKXCqQ5GiyPLgRbJQP1lfFEbrLCS0U4P9/yJxB+rcy5LmdETKDQGDPfB2oa\nE08BdWZZoszX3nku+MlYpZqOjoNmNwWLujI5GhB2GLdHY6Tybw3+49qYlnPW\nTIsI\r\n=bHe6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.9": {"name": "webpack-cli", "version": "3.3.9", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.8.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.8.0", "prettier": "1.18.2", "commitizen": "4.0.3", "commitlint": "^8.1.0", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.0.15", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.4.4", "@commitlint/cli": "8.1.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "1.7.0", "@babel/preset-env": "7.4.5", "babel-preset-jest": "24.6.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "79c27e71f94b7fe324d594ab64a8e396b9daa91a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.9.tgz", "fileCount": 15, "integrity": "sha512-xwnSxWl8nZtBl/AFJCOn9pG7s5CYUYdZxmmukv+fAHLcBIHM36dImfpQg3WfShZXeArkWlf6QRw24Klcsv8a5A==", "signatures": [{"sig": "MEUCIQCn9E18EqsqmeP7Nk/nJU72XJS9OWQEjfXWwStt66ErVgIgDaZvPnN5ZSMjW+NUevUIzAWoJ5i8LGAEQMDYrPzJim4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgIabCRA9TVsSAnZWagAAR1kQAJOOswG41hR/OqUBLG1F\n+FBFE7egZoCH/FvZiJFTph1y52uqh8pv+Jh1YqonvakFsN8obCuZ81229q9i\nzrlU8YG8Z66xg5tS8mYTbn1/0KyAciGOBiKhTP/Vgs23A8NqRQm1WTsexZr6\noBeXkxDhTUJ1tXCKypF3OXwPDY2e5pDb7bzCoTYxxGmquaRnoDBIIKnZYQcl\nfGW/mVVjlXnqx67bk4H4rlFRIPuovu6B+j2a8xignEAY18Qnlzzk3HC7s1Ht\nCNkKfZUtf26XHNUNsaweiCz7N+fhxr6w6Oa5W/KZVfk4OZbjG7rE/TJ9bXsP\nzh6KFgC0m1f8Tzd32zATG/bQaShoYDvgBXO3Zg7RjQgPuMJdyaVHRn7ZOzRj\n7vmasbp3vsWoNAQZP5JQ3/5gkFiPCOl2v6PT1NumX/UrdXjVW3Pton7gffta\nlYlsxwA91DjnaMbb3SZea50OC0WFxwdybkoFQHIHPS0Vxbi4jHW9JckF6V0d\nEHGqHow08A43wZWf5L54w1fc4N/oaulJ3iXHyW+XizVthql6Nsr+pUX48Ea3\nMxpY2etCpcmWoPBZZ+pqoa8rahI2Py3vAWjFGKwEYkfAYLpQiWL/L/RmABc1\nbFWsm8RR0uY6yFgrWlCBaHPFYOxtlTph6sb9urayCOBd4ta98i/FY968kBxC\nF0eu\r\n=iEUi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "3.3.10": {"name": "webpack-cli", "version": "3.3.10", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.8.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.8.0", "prettier": "1.18.2", "commitizen": "4.0.3", "commitlint": "^8.1.0", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.0.15", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.4.4", "@commitlint/cli": "8.1.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "1.7.0", "@babel/preset-env": "7.4.5", "babel-preset-jest": "24.6.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "17b279267e9b4fb549023fae170da8e6e766da13", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.10.tgz", "fileCount": 15, "integrity": "sha512-u1dgND9+MXaEt74sJR4PR7qkPxXUSQ0RXYq8x1L6Jg1MYVEmGPrH6Ah6C4arD4r0J1P5HKjRqpab36k0eIzPqg==", "signatures": [{"sig": "MEYCIQD95adwyDAFw3euSXcJGKe1y4r7p1BHD9XSuZZhcxjmdwIhAKzJ5Z3e4wbH1awMr0RAYmqvMSkNghqFk4bsWyHuIM9e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdumflCRA9TVsSAnZWagAAooMP/j1qgnPCT514IUJOCBIM\n5KWdrsyE+7DnHF7lwwr3mNgfQ76JVURd2CZneg8eCmlaSNxW6ZxWHCby2UPG\nshBvJSeOCfeBorsn2VGZUfFZexXDQGAOVKtyYQJ5Q+GDO/FxmEhJM8TZM11r\nbyHbc6ZzzDPKVMPXhTQ2AASWVw61MXZu6IIbYzA2eHC1HT1ux4Pxam7jJgaX\n2wRScqUy22S8CpaqoHucHJEzZhPaOXplgDXPIvhK3vXybjh+0/KEQxm35jP1\nAT1RPjX3sl9jx2Lo2NjwDYaUKyhgEIzuwTlkQX6MD3bBB43+bkldwSV762+4\nDu9Kx7OO3JbZKO7QLPOXHn4TJ77TdQQY1sh3jKmU7X8MHlH6ocGvTwl50h8S\nwFG3UwBGpXFwTHuKbuRvplAI9cFLOtRAr5qqeuU/fcxSIoWbkSm/eXkFMD0w\nfz6cq04pAav20NEmi/UQQwuF2q4U/tzhLO+Hj6PBEDV4zSO21tsZEnm9ShE1\nOpMr5VrEqrmnHI6c7lSZ2pyiJBhElhAAAYRHylTUPwqmD4aHG1gSG77oIixi\nW+pLmF/wPlSKvgonAolwyl6tjmwvI3siV48k3bfy5vta4lv1H2NAXWWHutBF\ni4fbV4mOivjNq0h9pNse/cGUeLvFzg60QDHzgmQcf7P29vg1+qlgAjK+o/GN\nWLMB\r\n=yANI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "4.0.0-beta.0": {"name": "webpack-cli", "version": "4.0.0-beta.0", "dependencies": {"webpack": "^5.0.0-beta.3", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "update-notifier": "^3.0.1", "v8-compile-cache": "^2.1.0", "@webpack-cli/init": "^0.2.2", "command-line-args": "^5.1.1", "@webpack-cli/serve": "file:packages/serve", "command-line-usage": "^6.1.0", "terser-webpack-plugin": "^2.2.1"}, "devDependencies": {"nyc": "^14.1.1", "jest": "^24.9.0", "execa": "^3.2.0", "husky": "^3.0.9", "lerna": "^3.18.3", "eslint": "^6.6.0", "ts-jest": "^24.1.0", "typedoc": "^0.15.0", "jest-cli": "^24.9.0", "prettier": "1.18.2", "commitlint": "^8.2.0", "jest-junit": "^9.0.0", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "lint-staged": "^9.4.2", "@commitlint/cli": "^8.2.0", "cz-customizable": "^6.2.0", "@babel/preset-env": "^7.7.1", "eslint-plugin-node": "^10.0.0", "commitlint-config-cz": "^0.12.1", "jest-serializer-ansi": "^1.0.3", "eslint-config-prettier": "^6.5.0", "eslint-plugin-prettier": "^3.1.1", "@typescript-eslint/parser": "^2.6.1", "@commitlint/config-lerna-scopes": "^8.2.0", "@typescript-eslint/eslint-plugin": "^2.6.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "5637d4062e35de50d0c79e3cb18823e4931b418b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.0.tgz", "fileCount": 17923, "integrity": "sha512-TskcbmwVWBRcQw0YPX3/eAvSopapKMh5cBoEi8TbjK4G4XArr5XWrkrsxSViIPF7+9ffhRdh1r1AekfV2T4kZA==", "signatures": [{"sig": "MEQCIHYY95Deod2jkQRO1P7d0wMcmohJokWR6tKDOkpcKA2bAiBJthc0NsLjfMaxY/N9tIvULJId2k3brMU0zeHdfQgo/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148873616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzr75CRA9TVsSAnZWagAALEYP+wQ+OoKXJgeiUf3qRkSr\nUUjrQT2X+DFAgeGZ0or44yZHwD8yIKg2B4HUxkz21+RMeu9jqVx9/jCJqlJ4\nAE8Wko5vpO186ZINQI6wYKTg2tX9bZLlD8qp3EjUoaCaLaupP2482B4bWRkW\nNIQ2R6weLThhGljKi/GHV0jp5RDW8Ro1NIVZMvbZBc8pjVO9Z8/TlJKfWUd1\nDm0fNHoS72e4UN6BCHxlo6gwukXCx74RNYSI+MS7Vw2dM6T9eQ4iJXr2Qxzq\nX/Q0eSQZM1Byczeiqt35kNaa1qoT833LdJYc1qgH58jdOmEVs7ge7+HTGhwk\nQcPK/Inov3jJVowdItfs+MoKeQDBandexTxS3gQdVusXPD+fCSPR1C6WCDoo\nd5TEWk557aJAOqI3sgJWYyiNCEuCQWeh0FKMNyVXDehW9msAnUrq2uFbzV8J\n/I4FvgCTRHMYYddPZkn5Rgzv3kLjt/Qc0aDmagFYnlAp7q2RMkL9IypYcEi0\nvfbybfvhjOOon48x7FoM7nI9n0UtjSc5KSh2pUTKhYi3LfxIHxrv7Y/ugtyn\nZ5IT/kVSF+fKRswHbPRgUs1/gRnecfhepg6av1ilBNpjMShnayio9gI5BPmv\naTfmqrXm2f1plny92LxvLQmmsekuEjRc3qBPFktju/CTcJYbUk+5e6lBkKNy\njVR9\r\n=YE3k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.0.0"}}, "4.0.0-beta.1": {"name": "webpack-cli", "version": "4.0.0-beta.1", "dependencies": {"webpack": "^5.0.0-beta.3", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "update-notifier": "^3.0.1", "v8-compile-cache": "^2.1.0", "@webpack-cli/init": "^0.2.2", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "terser-webpack-plugin": "^2.2.1"}, "devDependencies": {"nyc": "^14.1.1", "jest": "^24.9.0", "execa": "^3.2.0", "husky": "^3.0.9", "lerna": "^3.18.3", "eslint": "^6.6.0", "ts-jest": "^24.1.0", "typedoc": "^0.15.0", "jest-cli": "^24.9.0", "prettier": "1.18.2", "commitlint": "^8.2.0", "jest-junit": "^9.0.0", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "lint-staged": "^9.4.2", "@commitlint/cli": "^8.2.0", "cz-customizable": "^6.2.0", "@babel/preset-env": "^7.7.1", "eslint-plugin-node": "^10.0.0", "commitlint-config-cz": "^0.12.1", "jest-serializer-ansi": "^1.0.3", "eslint-config-prettier": "^6.5.0", "eslint-plugin-prettier": "^3.1.1", "@typescript-eslint/parser": "^2.6.1", "@commitlint/config-lerna-scopes": "^8.2.0", "@typescript-eslint/eslint-plugin": "^2.6.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "cli.js"}, "dist": {"shasum": "83f4934827781a59809a438b3db00df5ca78a496", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.1.tgz", "fileCount": 23, "integrity": "sha512-qFktdWQtXOeeZqZm+kYcEiNhab0zIM4sfUwDuMHaPXXOIos5+r/yCbNCSxQo36RuD1Tnz/dPprzkzevBRL2lVQ==", "signatures": [{"sig": "MEQCIFKtH+eb5msD71Xa8YQjzq/dvtKRNS0Bw57Wwrn08scBAiAah4KHBwRBryMohT8gWlMLLGlE+cnPpmgzmwetSHAWQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzsQUCRA9TVsSAnZWagAA2eIP/iM7rvD8yR0loHXJ2/a1\nBrwXljf11pc1bAa9t0XB6IgTwh9TAevCrBYm06//jpwsJHj8nHSS7bJZvgWu\nMbQuBrRkwP6JslgbFJrPXOb7L/W/pNXqxwP8/JCVlPKc8aEdAmjoQuH1Eqcj\nkvJeLWx8Ob1n8oNMFMQJYyhiU+jubAbJCMR0SRtLAujf784D+aNIHvbgrqV7\nqYupMRJPZHepoy3tuQWm6hp4JeKy8Y3ZqOe9dexF7fhrm+Ky569dyMEglGOf\nKTMK/4/goYyyJKGOvDk0EcJQzXOBaxu8KhSR99zC2YRTFJzyHBysVcsR6yms\nyTjYluzwOlVstJEZZZnlV2RoAidJQuuPGtRGOLEGlkz9+X+N1ORBqsfgaQWO\nv4vfSrWwndA5iA2xTA/4wU7W25LFzS4kzHFd6TXpkpWMLODfi6I2RCGbqlUs\nKOXh1RcPocuDYBF7tdN/QhrtA9eL72oOJ7mfngmTVlgkll6UXznp7DprAH9h\nB+i6uK1jobh7kQ4udO7S0ErMI4BIMlFCZb8Qku8mjgAWKj1HmAx1IKCp5vf/\nJL+WUo9wkGuhvrmL76z5fKU70BNDwcfM4H67pVK+fCobDoIBabZzPhXZGmFi\n5DQqrZ3oCnVFVrDgp+mgnBOJKlUxoK9o6d8ca8ZC/xzBn9zyLE3vwbX+NLIl\nVYBD\r\n=ibzf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.0.0"}}, "4.0.0-beta.2": {"name": "webpack-cli", "version": "4.0.0-beta.2", "dependencies": {"chalk": "^3.0.0", "execa": "^3.2.0", "semver": "^7.1.1", "inquirer": "^7.0.4", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "update-notifier": "^3.0.1", "standard-version": "^7.1.0", "v8-compile-cache": "^2.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "terser-webpack-plugin": "^2.3.2", "conventional-changelog": "^3.1.18"}, "devDependencies": {"nyc": "^14.1.1", "jest": "^25.1.0", "execa": "^3.2.0", "husky": "^3.0.9", "lerna": "^3.20.2", "eslint": "^6.8.0", "ts-jest": "^24.1.0", "typedoc": "^0.15.0", "webpack": "^5.0.0-beta.12", "jest-cli": "^25.1.0", "prettier": "1.18.2", "commitlint": "^8.2.0", "jest-junit": "^10.0.0", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "lint-staged": "^9.4.2", "yeoman-test": "^2.1.0", "@commitlint/cli": "^8.2.0", "cz-customizable": "^6.2.0", "readable-stream": "^3.5.0", "@babel/preset-env": "^7.7.1", "eslint-plugin-node": "^11.0.0", "commitlint-config-cz": "^0.12.1", "jest-serializer-ansi": "^1.0.3", "eslint-config-prettier": "^6.9.0", "eslint-plugin-prettier": "^3.1.2", "@typescript-eslint/parser": "^2.17.0", "@commitlint/config-lerna-scopes": "^8.2.0", "@typescript-eslint/eslint-plugin": "^2.17.0"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "c97739228d2b89c89130acdafa591b1814b566f8", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.2.tgz", "fileCount": 29, "integrity": "sha512-FpNDKnwYtYOJ1dlqbv75rNpGcGnf2AZoq7eqxuxcojtfFAjUVp3c3GhKCN2pXiM7TXi2r+EQcbG5KE39774C+A==", "signatures": [{"sig": "MEUCIFYwO6M4Xxc47Q+0SDl8RowfB3PrQEik65Y18XnfkE8vAiEAh/huSMw8iJ25luMmfXJPzjQYVSj+qZvE0pghZYyYZBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123611, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQm72CRA9TVsSAnZWagAAJ+gP/j1t4/SBbrMiqmh20jtM\nLubNl03IbrBrKoR+kuBvGY3GP/r+aNTkNFMnd3cVxMoZpuN9kJ5jjDBQOgVb\n8MvgT8YRzhKusL/8fb6HaIc2wIqRrOGSYATqAM0YsC4Ih8ghQROleQED4eq/\nInmHLLqaJxU0RQK6dotiBlj+3DVKMDxUx4psvvITNgFjlVikEzIKXJ0ohFeo\nzIq1YqsYnuFENwHEAedvTL36abX2GHCIOKZAikFNGku/gB5IkivGi74HQg2z\nyodzTcgT73v/25L2maV23zPMsHa0a7h2RoASRYYMjLRd9gnc+JlGIWVtLf7r\nKZgA6oiHQLlBRIa/iovljVA1QFgQaz3gtxYv4GPTTWxrakwsFHA4sPybPmZC\nHWSwkbiAmkFHwKVBJf/5/LZdiyNeaxfzLahAtp/qA0DQ//hf8U49VE7C6JZN\ngGJ/oTyfHwtiU5vWWoeN0m8myfZ3z51nmxFZ+gShUrw6OLuLi8cLnDzcKfoa\nhG/AemaH56mx2s79q240nNPMISRu3Sd/BxycTNUwPvHSnF2gqtn6WaBz0bWa\n96rBRVWfTqTUnvHA8mYQhgp4Bxvsg5ZjNItbIFzjon/F5OYZ1Da8uwqERJDB\numVbDtCbQOm8n9Vv+dOEdMJnuI5da2q9/rxy/85kOh0317PaApFkpeORi8n3\nLJAl\r\n=P1KA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "deprecated": "this version has been deprecated due to bad publishing"}, "4.0.0-beta.3": {"name": "webpack-cli", "version": "4.0.0-beta.3", "dependencies": {"chalk": "^3.0.0", "execa": "^3.2.0", "semver": "^7.1.1", "inquirer": "^7.0.4", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "update-notifier": "^3.0.1", "standard-version": "^7.1.0", "v8-compile-cache": "^2.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "terser-webpack-plugin": "^2.3.2", "conventional-changelog": "^3.1.18"}, "devDependencies": {"nyc": "^14.1.1", "jest": "^25.1.0", "execa": "^3.2.0", "husky": "^3.0.9", "lerna": "^3.20.2", "eslint": "^6.8.0", "ts-jest": "^24.1.0", "typedoc": "^0.15.0", "webpack": "^5.0.0-beta.12", "jest-cli": "^25.1.0", "prettier": "1.18.2", "commitlint": "^8.2.0", "jest-junit": "^10.0.0", "typescript": "^3.7.2", "@types/jest": "^24.0.22", "lint-staged": "^9.4.2", "yeoman-test": "^2.1.0", "@commitlint/cli": "^8.2.0", "cz-customizable": "^6.2.0", "readable-stream": "^3.5.0", "@babel/preset-env": "^7.7.1", "eslint-plugin-node": "^11.0.0", "commitlint-config-cz": "^0.12.1", "jest-serializer-ansi": "^1.0.3", "eslint-config-prettier": "^6.9.0", "eslint-plugin-prettier": "^3.1.2", "@typescript-eslint/parser": "^2.17.0", "@commitlint/config-lerna-scopes": "^8.2.0", "@typescript-eslint/eslint-plugin": "^2.17.0"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "edad120dd70a41412ef071e49e6cbe340e5d610f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.3.tgz", "fileCount": 29, "integrity": "sha512-fWdJNrS5fi5H4kdDQt0lZpEc1Vj2OYcRSwSIrvu0lh3kYqIwBj39wtKfswqQcvDBkkoOAB2A1R+JfCdLjliogw==", "signatures": [{"sig": "MEUCIQDbs/iNerJ2kHW0zdru0FMqnOmNsd4r213awfgYFqD/5wIgflxQLkCNi9Sj+6rBKixTotfSgZD9jiCzpzpa1s6r+RY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQvdFCRA9TVsSAnZWagAAH3QQAKCWay0DTGZAjMFvvYxH\nVBIii2fJ5aV4m4PKULk+VlY5JnSrunHd/ESZtQrJvWruryz+4tR/fK9xoA+W\nncY9JM/SHfWV5dQv9Idx1Fzq6xOpJkmUN5E9CH2NFLWDmMezPAHX4rFHc2TF\n94qIQwj9ViURjeRzIsGKxDAHwHvAxZQil4U/qILWfpzGevS+JeL3m/OQGpc3\ne7u4o3jYY+jp5MSuhpyuyVZxeNJpzWUyzvfGWdjPt9mDoYvB8GVhwdZ6FP5e\n3G/KJt8kuRaLuC8gY8hjEQEiCcK6Y2sPgbk7/YrL55cm+e5BvL7vih9aKVsa\nExSSvPMva2wn9QUWTKxXT4UcHYEIiHypxAmtdLKYv/poqqAAwc/xKMCZvLQ7\nSmE54H7Yc2HP2YpV/8JexKFX4w9Kp/5Ze1zaxFbumQyOjsRBxnkbDoWjC23+\nCR+3fLRtzW/WgQHeRU7sWKCyITA3OvDm7oQYUhioUWOeIaGO+lUHACDq0C4C\nsyB7GQQSJQ9DPYhysokmXv9YUNs4yLOXrAdKPPbG6cmHoRrL/Unuqc6LKRGt\nKZ6/kU3oLjv/zeZPgCj9ixrq6G6hw30Z5l07KmEJ3SmYfgHzoFAByZEzArLp\nqn7p2eXVm/OTqddqMwQ6TK5oNn8ZRME1hAHzOFtUeYWBcuwchBpakZcustpO\ntBBt\r\n=D5xF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "3.3.11": {"name": "webpack-cli", "version": "3.3.11", "dependencies": {"chalk": "2.4.2", "yargs": "13.2.4", "interpret": "1.2.0", "cross-spawn": "6.0.5", "findup-sync": "3.0.0", "import-local": "2.0.0", "loader-utils": "1.2.3", "global-modules": "2.0.0", "supports-color": "6.1.0", "enhanced-resolve": "4.1.0", "v8-compile-cache": "2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.9.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.9.0", "prettier": "1.18.2", "commitizen": "4.0.3", "commitlint": "^8.1.0", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.9.1", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.8.3", "@commitlint/cli": "8.1.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.8.3", "babel-preset-jest": "24.9.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "3bf21889bf597b5d82c38f215135a411edfdc631", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.11.tgz", "fileCount": 15, "integrity": "sha512-dXlfuml7xvAFwYUPsrtQAA9e4DOe58gnzSxhgrO/ZM/gyXTBowrsYeubyN4mqGhYdpXMFNyQ6emjJS9M7OBd4g==", "signatures": [{"sig": "MEYCIQDh9Ew3rUYppLf9svh9MuR78oQq7YUMAHL3DSG0Nln+SAIhAJtzdQjteerEGMY4xOdMGpLIOyhwRR6AliqvZs0Dghko", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQvgICRA9TVsSAnZWagAACwwP/1n8D5CYomoXd1dM+Pw2\n/Tazu2wGBqQrxLUu0YbUuFAWruBapKmInI+7C7IftX2HLYLsOiCEUGaUE9ks\nW//EVoUccx5I1uxiatDGQcu9I7nJebbMFWRfg1yzsAMI2jJl9s1wbtt+5ZSU\nEOhXvbgjJUX10Vp6AzSfvG0WmZM6LsBeV/DbbcJwdgF66h+ls6RmxCv5yMq/\nEKQ8Y/obabDyJ4UYM9I5fN3bVMwuINPrnZ+OZ36bKc+T4D9CjLuhTyv4dzYg\n1PkndHyoYACgxb1NQ3H4EgSrwlzu1BD16NjHKRItpn4jaKIfIa3oAKq3IiBs\nRSS9RgC7y0uaIJrxHHeEX6pHJnl4tY8Q/yoGcy2q+GbvOR/pm1JRrZdfkh6v\nYbbXpVztDDW6ZFFl4cmwUTdnEUtDzzy8fj1LQG0/LVWTKtQtKuCKGZfwYMRi\nN+9ogHG0/aHFXrYug2nt3l57xg4aZMn9L5ziWBfD+i8+RoCRLUiqG4amFnI1\nG6Q+8cSoHM1fjKq0IoFw1lfFp0yI72YvWfNBCg3urojwX3DPJkmRVetoJp0c\nXK/c/WLsN6R4QR2xV7U2gMLAm0oTAaeRbYRjSei+pc+vfEZGX6dHaJSKuz/F\nFSCebgdVsaiUOQxgAsCSzkSOve3B8+qQMnGZa3dw3hjjQ9zEmky4hDpB2cgw\nXZ2a\r\n=mced\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "4.0.0-beta.6": {"name": "webpack-cli", "version": "4.0.0-beta.6", "dependencies": {"chalk": "^3.0.0", "execa": "^3.2.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "@webpack-cli/logger": "^1.0.1-alpha.2", "@webpack-cli/package-utils": "^1.0.1-alpha.2"}, "devDependencies": {"@webpack-cli/info": "^1.0.1-alpha.3"}, "peerDependencies": {"webpack": "^5.0.0-beta.12"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "20bba4c8f9f850f4d43dec3ff75fecd39875a0d2", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.6.tgz", "fileCount": 28, "integrity": "sha512-0+mubZpbDs7JAexfRIxmoTTpu+/DLctiaEopzA7ab7r4ami9RNeqrw2/Mf+vcVAHBuUB9QveDP08VcFYZeQo9g==", "signatures": [{"sig": "MEUCIQDTn6noAmBrRGX5kx+fY1RLCgPgc560+iuZyQYCm/AxoAIgQqx2QptC1RxVDMom25Yf6FoKBWXJVSCgXSX0XfbM7N8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUlUWCRA9TVsSAnZWagAA0q4QAINllILf+pti4b5m7bMS\n/49BK627Zks0tLrfF7jhMEMT3jXZnTJS4UOmR2Xg+AHtVsS9be5DpEyJwm5q\n3ZHEpQZwzAZCayyzyLbbvEC9nMoFg4ESWLdHRqOUnsaFuBhaPE8bJ5Qh9am2\nw9wwdYmdbvvGRvXQA+QBvoqiMLOXy+IrGMJafsfMK0h14dd+LivvVNInPKOi\nCcE53X5DkYklh3oqBESEk4YFPQOgZHiwB5FLzjDWNiPCgfeOTgEtnuSVkCUW\nZu4vCVU14jxXZcX/IGh4Iu6vZobc2+MwMPGXU7XDSNv0iTeZfumYPsNO8N+L\nGmd0dcE6QgZZMSmbl3AJMOuUl0TMSDSRE4AyjqhUezGmVhIhXX9S2tQfoMHi\nMpKywsdJStHIfXTozV01/w6BNdTv1u8gBBLzPLMJ0D+9rnEel/iwjplDlvZm\nrm2bG6ga0WQslctNp8+gr74TmXkL0b3c6z0v63ndLoJeGNQP1qhSJAo1b1f/\n1Nr+VscMBDpyZpcUJneQEUfJ18wYHTRxagtU2JVh/itR7uzf1oMmjLw1bb+l\nEmfB+JAomf4eYiFyQYLVIAUXyZDomy+8l/PMK7eqxGyc5xYd97/4WDJTlKvB\n8L3jHJvGD041ZpsznBoWNj7G4YKVRjKh/j8ySJUsLmja6MudlUy+zq0Gxq40\n3R4e\r\n=evjF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.0.0-beta.7": {"name": "webpack-cli", "version": "4.0.0-beta.7", "dependencies": {"chalk": "^3.0.0", "execa": "^3.2.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "@webpack-cli/logger": "^1.0.1-alpha.3", "@webpack-cli/package-utils": "^1.0.1-alpha.3"}, "devDependencies": {"@webpack-cli/info": "^1.0.1-alpha.3"}, "peerDependencies": {"webpack": "^5.0.0-beta.12"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "1b3b1ad82b2758ba81fc2f07d1ccb777aa05a19e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.7.tgz", "fileCount": 30, "integrity": "sha512-AYeC9ntoOLuMdR+ZVejrYBpt130wrLNhvNICf4L5v6O0YQK6lIBTwAtBafbVH3KR8XVBnQSXhTCT3r/OmVIKeA==", "signatures": [{"sig": "MEUCIQCKU1M7PUvpDxDFjZ18KGU996yc7fYO7CmikZKM9X7d/QIgYO86uWNWSCioZmvUChSMdb2Tkh/jnL3HzRW64xuzUr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWjiVCRA9TVsSAnZWagAAdvwP/is84k/CZel2CS5a0t97\nVcGY6Qtp8WfuKBM/GRAiMJsVFxJIEd+BxKm96cq3mb0o2jS6M9DAWmpeb1bj\nlwErP44yJ8KopZzw0werZD0eF+d0QsAWEuDZe0u60VnrScxOO1stgIUi+oYP\nWxrTiPXco5/qXgldgcVw78IZYV/UHms1E2FaO6xv5+jX9pDWDMLAVgKEJqKx\nb4KUl/KHsDVoQRo07Pw6ylkbHY3x9ENqsKhRJysefIfkOd/3kG/m9jXi5B+k\niMQBinLkxnvMHMipV3j1goW0jY17Oi3sin3RJxIaIb8llAw4tL6U9/WTJcR+\nT5rcWSxTQoyNPuyvkdiwznefb9JVoJxXTnwhszHwKZPKIRv5M/oCccyCupOC\nHhogoWtmHjEn0G/LXFYu+oloQ3Rg7arhQTmtDkcginFEJ8o6BNyu4hmMI+W+\nd9l8Ny0oCcaIz+rhB0NjdRUUI4jSWX76IJqZMNBuKFBj9+Qo7xzZHJQfdS16\nHXe8StUU6ZjokssJWKIC0DW0irWZRjikZTVwwgYM+pp33goteoxZ40AYDTzh\noMn3PILhZNVa1fwSoAq255zzNGSaLASqxfJUqYq6aG4JydBmhMmBN8+VAssr\n+KqXDX/KMm+reqKcz1dhjx1KvP72DDVfX55ku0e5bBt4o5bVIS/GjOcUiI9C\nuZ8M\r\n=TeBs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.0.0-beta.8": {"name": "webpack-cli", "version": "4.0.0-beta.8", "dependencies": {"chalk": "^3.0.0", "execa": "^3.2.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "interpret": "^2.0.0", "cli-table3": "^0.5.1", "webpack-log": "^3.0.1", "ansi-escapes": "^4.2.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "command-line-args": "^5.1.1", "command-line-usage": "^6.1.0", "@webpack-cli/logger": "^1.0.1-alpha.4", "@webpack-cli/package-utils": "^1.0.1-alpha.4"}, "devDependencies": {"@webpack-cli/info": "^1.0.1-alpha.4"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "62dfd9daaba6d46d2035f91d8aa08fda4ae2fc9f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.8.tgz", "fileCount": 31, "integrity": "sha512-kitIlz9sjCw5OSWePZYlWopmZGxy4vK7gA3MTAll5/J96cTjXGCxUBO2j9beN6saCv46cpPp1IE5V0eTGK1S0A==", "signatures": [{"sig": "MEQCIAfJq0kVhKbUg400A6LD4mZ5gJEVlZkypZQ3ixGMwIxQAiAPvLgtzXW24xR02tOjPo6RDH3a+BNsYbrcURUunaVapg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXSrfCRA9TVsSAnZWagAAMngP/j1DyPBN9dW4nE1q+1b7\nnf1SUYjUcxz8KeSHlXHKXg6H4PvocGikGk2edH/H7cQrvwM93ha2bcHScyCt\n4YlePPUsUtVASLpt/iOft6XydmKCz8Uwy4SYx3CrL4cjXD5f4iGZL36Ok00q\nIBloSUSzFrHUZRJwszEijexcIsT7/9WN2E76exWxrWbj4mNwQl1mAx9doyr/\n06+flUOJ7ssr+vdRtE1jF+SFvpwY035InYpLryxqaqDOjQkd0vF+lqFyCRYz\nOl5vVqKGrtXK6Y19iTSg35oh8ENSCGsyuW7JUdOYjpfv/GazPr004wXmrHzo\nbpSh6NTaz+/QZjF/4WLpwbWWF5ADCLLCwP44YEpTUZGxgpmmuoBHWJ9/z8RC\nOd7hDgbeJC5l5hfyC9ZULdkYH2r/dFw7zwSRyA2fOpLU+5se9+YqsUzf6Kpg\nOb8scJdLnJWatzjE69tzsX65ZNRNU01hEul4L8iMSIyETQ+hx/ufZSlbU1zd\n8xjGDGaAHigKfD/Iu5HzeltRa8T+NLT200fjeRfnKvArqRlzjpXD8lpZOo6F\na4cmh/sXnZ2YtYtmvIPBmVeKFpA1lFKCThdnT5HhnAE7GiHq2bej2CtcPrS2\nC3DKkwzOEWpVzRjjmXSdL9Gic5AZb9Z6Dh8fxbBSQGKJV1l4kxn2yuttd2lF\n0hMR\r\n=QTP9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "3.3.12": {"name": "webpack-cli", "version": "3.3.12", "dependencies": {"chalk": "^2.4.2", "yargs": "^13.3.2", "interpret": "^1.4.0", "cross-spawn": "^6.0.5", "findup-sync": "^3.0.0", "import-local": "^2.0.0", "loader-utils": "^1.4.0", "global-modules": "^2.0.0", "supports-color": "^6.1.0", "enhanced-resolve": "^4.1.1", "v8-compile-cache": "^2.1.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "jest": "24.9.0", "execa": "1.0.0", "husky": "2.4.1", "lerna": "3.15.0", "eslint": "5.16.0", "rimraf": "2.6.3", "codecov": "3.5.0", "ts-jest": "24.0.2", "ts-node": "8.3.0", "webpack": "4.x.x", "jest-cli": "24.9.0", "prettier": "1.18.2", "commitizen": "4.0.3", "commitlint": "^8.1.0", "jest-junit": "6.4.0", "typescript": "3.5.2", "@types/jest": "24.9.1", "@types/node": "12.0.8", "lint-staged": "8.2.1", "schema-utils": "1.0.0", "@babel/register": "7.8.3", "@commitlint/cli": "8.1.0", "cz-customizable": "6.2.0", "readable-stream": "3.4.0", "babel-preset-env": "^1.7.0", "@babel/preset-env": "^7.8.3", "babel-preset-jest": "24.9.0", "eslint-plugin-node": "9.1.0", "webpack-dev-server": "3.7.2", "prettier-eslint-cli": "5.0.0", "commitlint-config-cz": "0.12.0", "@commitlint/travis-cli": "8.0.0", "eslint-config-prettier": "5.0.0", "eslint-plugin-prettier": "3.1.0", "@typescript-eslint/parser": "1.10.2", "conventional-changelog-cli": "2.0.21", "@commitlint/config-lerna-scopes": "8.0.0", "@typescript-eslint/eslint-plugin": "1.10.2", "@strictsoftware/typedoc-plugin-monorepo": "0.2.1"}, "peerDependencies": {"webpack": "4.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "94e9ada081453cd0aa609c99e500012fd3ad2d4a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-3.3.12.tgz", "fileCount": 15, "integrity": "sha512-NVWBaz9k839ZH/sinurM+HcDvJOTXwSjYp1ku+5XKeOC03z8v5QitnK/x+lAxGXFyhdayoIf/GOpv85z3/xPag==", "signatures": [{"sig": "MEYCIQDQaXGcnvvt5lrWg2fYD7sBqUqqUn0Mmtjq27Xp0V8nDgIhALVbVkCdw1neyXR30tDHqB3w5HTgOhqf9TBA8T0jNm5D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe61+bCRA9TVsSAnZWagAApg0P/1morVyPvf7D3Rfg489C\naJCNAZbQPszBnFgoKXL9LlupdjrlUei1f01d+KEuHo9Pdg2VH5EZrBLPXEtl\neV53uUvj/oYrkgRCcv7EU8HQOdJ+z9eLYCrgVeoRiqPQAQUDfMHetYMs2S1j\nNfyYxkzw4VbNqzZPcMC2yWQVnEoZeGm7JG9usGXwwmmUssOQteYJ5xPUPwzy\nigwUG+cq6NAodR3+ytf+Ne8WY98tAn3W3wEeZEKcC402EiFisYIfY3nb+2+7\n22ZxiaQDW2LSfE+UUAxDJsKuT5l6tgthrM03C5KE0Ke8M8mgn3GUlmiOwMw2\nfWTvMQwl/iMugFeL0Dz5uQwe+RsC9zd1towtohupcetsS00vBjZ+5KsgozIX\nMLEPZ9Ezmf04Iy7IVXaceReT9wqRyvLwD7F7ky2wVkXkSsUujOi1hYmA+mRQ\nsAVtupem0yQVlmQsH9lr6Ie5lv+gHRuGACiPpUfuzwSyQvYJT2eSwtSezkqt\nQjgUjkNXRU6snEzkf6aWDOt3iO/Mernh6Wi3gWFKEbQp3e4m+WmkEoiM3sog\nQeyHFK/3uHFusewFWoTP4hlS2tIsOFjCzKfcqLJUmE/EGlGDHm/B8+tFMg6M\nzhOovp00hzMpM2eqyrVWbUcEKu2UMBj/ixvFg0UC0qvgWdja/Qlg51+XXAKH\nQNhY\r\n=UyUn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.11.5"}}, "4.0.0-beta.9": {"name": "webpack-cli", "version": "4.0.0-beta.9", "dependencies": {"execa": "^4.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "colorette": "^1.2.1", "commander": "^6.0.0", "interpret": "^2.0.0", "ansi-escapes": "^4.3.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "@webpack-cli/info": "^1.0.1-alpha.4", "@webpack-cli/init": "^1.0.1-alpha.5", "@webpack-cli/serve": "^1.0.1-alpha.5", "command-line-usage": "^6.1.0", "@webpack-cli/package-utils": "^1.0.1-alpha.4"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "82d626ea6c7a61573416644ac54d6f5213bc040e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-beta.9.tgz", "fileCount": 28, "integrity": "sha512-xBzg9ScF++iOy1+J2VJkUlFEM5AvlKiJK1LLU8gOk4w8ncTX5XwQA4ipWOkZGGscG2jb9PE8o8tQUwXBLf9s1g==", "signatures": [{"sig": "MEYCIQD6ziGRYShhpiJTUi0XvakUpj0QNOet8sRZezaiNJkzDAIhAKGCfadZ4Vzyug7fCxqBtzRjuT14ersTA40fOwq34NMV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZkfaCRA9TVsSAnZWagAA1dAP/1C4Ui7BQWiU13gMYxfO\nOuOihCycZslrnnftGU+GFS1hV064G4ogs9m9Y16WEOqY9t1BLWA/7JTEWpDi\npp6r0OE/kTb/l0wssQMjAiRYBF9VrotxpV5xZx1lcOjkT8tLktQBaMFeLfVR\nxknx6o6Vv5iMxBIhNHsw/CUhgKx8zyf8VpJ5qRWpHzMFLzmswOMuwVOYLxIW\nbm7DW/qJnwbUyoYfuFw/4gASRKF8n0Cl3BoU2NfyU5WJ8XxK0oPXYqBZO9lt\nF3TnfuxWlU2rpiu2GJYye8cWP/XUPVK+JMLATYthwW65IZagE+QlsM1HBDYP\n3S13zVua/HOKYJ4xkXg0U3gC8TTkdSK0R24rBfIW01gAWpSlA3hfIojOZ0mm\nhIU/+YbHrdKLH+xnuXKq7QdYbL9+TYOeDRJOJ3CJcMfcfzdmxwRstEVLvrnl\nyxE+fASgzHgEobJ25KZmCUFM/pobGDmRwjeX9pTcw+1Iju1Ph688sFHkM2bD\nW3a1QEYDWsV8PUEl1iuw0PHISFDAJI0oE8ChReV5Vz44dljYvEYKxYXYnF7q\n9JhEYsyv+3e2FtLdylLGC4NO8phQtZanFMZdSr6D1RCV4XebsKmF2SCP/BYE\nFFgURbApY71BCHxj7EWjjAxbjxB4dYtMZx/I1OGJVEdYQFcri9k3saMhxjLj\n548k\r\n=emhq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.0.0-rc.0": {"name": "webpack-cli", "version": "4.0.0-rc.0", "dependencies": {"execa": "^4.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "colorette": "^1.2.1", "commander": "^6.0.0", "interpret": "^2.0.0", "ansi-escapes": "^4.3.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "@webpack-cli/info": "^1.0.1-rc.0", "@webpack-cli/init": "^1.0.1-rc.0", "@webpack-cli/serve": "^1.0.1-rc.0", "command-line-usage": "^6.1.0", "@webpack-cli/package-utils": "^1.0.1-rc.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "67f5d60e0ce93e73761624e53d6217d3604751bb", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-rc.0.tgz", "fileCount": 31, "integrity": "sha512-bvi7fxX0VvkF/ONWmdnagJG+PNPzfvE2xl0xzvxHJ9nMkvm+wAUuiR5ddOdzOTO3g54VsRyyef3HyzRsvF9RDA==", "signatures": [{"sig": "MEQCIG37bQa9qwYDR+EiFu3XoFgPaAghopod6MxXDB6JKYQHAiANlf7kCS5+MDRpkA9VkSBJE6FvSIvm3irMeAwaLz6zTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfamXKCRA9TVsSAnZWagAAB90P/jPwBj5OQ4hNhBS+jFln\nxAmQrYVbxc3teJnbk7bPrNwjQn5Dm2Ol7LawJjV/LNbOfM3R3CfIMFAv4ESv\nl5WlvYcF6ennSXAZzd4cYgf4d9bZO2X7vk4CAboXsBEGXNVzmukmUmL7dnee\nUYofeLC3Ypp9pmc8VJ2Mm/ePV+FZt/V3sy2Ie7KQbi1iPxdC6SjsobNcwaPN\n+REQNQkY7ifZ5FwOSBLP48/Vd42lK0wbQHhxPqktYD2Po4UyveYowC2HJgoL\nfVv7d2wN0hQDD7IyJqXjT9Wg6fdwbZenvDniiTgFsO1XzhkAxmpjH2Hbmn4E\n7Fbu4PjpeHlw91S56+Y2Y3yaMJWVdsrHbb72IMg4sNy7BUxz9KH9+d3hBZiV\n1O87qCMbC7FAglCbbge1d8TQp4mulOuV165x9eI5TD5tjOHCH7Fw63+Ph8y5\ni9+yNxk039E7fRUiMSbYd930GTLwCXgvOrArNfwfyvyqaSIYnMNLWbnKC5TO\noZ3HWrlebtoVWzM6+L05J4M+yz964zeyEaz670BTnpJJOKsXcf3Ys3165GeG\n+x+tyoue2wC14UuhqJipkQPFVhUdzs9RoyhPn62s7bU07xQGBdOImJ2cUlYG\nQvZfUHM50zXECobpHlg+tzx++oyBCkVFS5x4WSfYTjzRofgIuTPYDwNdpnRk\nY8B9\r\n=Ocz/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.0.0-rc.1": {"name": "webpack-cli", "version": "4.0.0-rc.1", "dependencies": {"execa": "^4.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "colorette": "^1.2.1", "commander": "^6.0.0", "interpret": "^2.0.0", "ansi-escapes": "^4.3.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "@webpack-cli/info": "^1.0.1-rc.1", "@webpack-cli/init": "^1.0.1-rc.1", "@webpack-cli/serve": "^1.0.1-rc.1", "command-line-usage": "^6.1.0", "@webpack-cli/package-utils": "^1.0.1-rc.1"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "e89be9ed90629e1ff97bdb8a7116c6371910a8a7", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0-rc.1.tgz", "fileCount": 32, "integrity": "sha512-GJLMgrv/m7n+2RDC1iCOWa+J4ITipjT1BGO4Sbz358DE3kAzNgXHbTmnwt/m32llLNeSDrnQyQ+PQ31Ui1yX7A==", "signatures": [{"sig": "MEUCIDuBAchRFzJ/jrydoC7NKyKIKIQVa8FJwIUm4ikNDvCnAiEA2o0D6JJBdHfsAip8ycsvZC3uOYUc9MRDSY0Oe4TAE4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffKyeCRA9TVsSAnZWagAA9tMP/R6ryrQh3AeaKWLf8CZ+\ntUBmUc899UKxBd0WtmdLF/IXOpqwZK/Q1rUTqdyZNbMLKlXRA5PAfT6p7pMv\nO7USIm0L3UqJa3a9kTkzc9umMF961tDoj9vENuxdFjC5h5f9EkVTaIbqea3Y\nlsRkqceoLb/PlzcP8YZWZ1HgKkfX/xWJ3srI1CMsIvzJ4fZBl+trtpd2/sZZ\nZearuz5mV/MIG8qaR0tBL5ZSZAJtbahQ2fhX6zfPnxXElcbcvpVhEFOZoRif\nlFPX1D4eVH7oIvkm3WwuXnfbI8M6r5NYa2yuodlOHY3LXPVaHU+Ankr9RCFJ\nlmY6qubGvsUqyHdYRMQrDTkVKkZyfk3z2JjXsnGXsU/2Q31YdQiYb+avHmn/\nEzZJEXajNvl5Q00i4MAPTfWXLXmh5iH/2xMa+ducHIkATj1+fqbbz2Wb7Wf+\nhnBztYQsGO4pJF3+VHV4gMcvgfiAuPv7U502cm/faiHl4eg5GJwwjA4w5ZPS\n78GYCn8PX9xAg2OLQ8CbZbGMrGUof6bGrebgCg5YOOG/+qsPCkPavdRWxHuE\nW1qQvAxG5pzvEKJa1xJhcleSOkplJLgcXk+OFOdyRbdKzYDnsIqn4CbCfVih\nAZjPpiRwgnP1Y0NdXqbczhfRSMf+0aFKmEdvbonmCaQA3KYdX0CBvMWPhldh\neFah\r\n=G1/5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/migrate": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "@webpack-cli/generate-loader": {"optional": true}, "@webpack-cli/generate-plugin": {"optional": true}}}, "4.0.0": {"name": "webpack-cli", "version": "4.0.0", "dependencies": {"execa": "^4.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "colorette": "^1.2.1", "commander": "^6.0.0", "interpret": "^2.0.0", "ansi-escapes": "^4.3.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "@webpack-cli/info": "^1.0.1", "@webpack-cli/init": "^1.0.1", "@webpack-cli/serve": "^1.0.1", "command-line-usage": "^6.1.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "77ded6f871145439d87797cd39a65f9f782d4f93", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.0.0.tgz", "fileCount": 43, "integrity": "sha512-c5NOm8jDp3qWa+Q4pDZTcT5IwPcPXdjU1ejN9e7LojHQN02sjNr4tzLrt5pwkY+zN8pQL40m14JsbC2Dh+ZJ/w==", "signatures": [{"sig": "MEUCIQDgos/SobNQaUDaJMk4HFIYQJV78xElicS4Ef55oyu7vQIgTJCFLBp4+NfWe5vXibrTcHUl4rXaqX1mpqsWkviWztw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgfpxCRA9TVsSAnZWagAASasP/ihyBsdGT7AjuywrbnrA\nIqBHanlsb2JKW3L3B8yrU7k3K/Hr3HW6MnV0u4j8yBzLGc605A0DoBtnom+R\njIXmvEC9wK/A+EjNNr48HQ7NPC4q+cxwFgA/LfDwlFHN6jAGrnKC+eiUY+vP\n13qEz2YZk295KiSIBOI6HcPFl4K156akyUogRmJ9umTC8UsgJKClBAyVKRlO\ncg75JF/SFWRBLhUxAZpMk/Xkn6a20l6jS4M3/zuOD2lYMNaH2DueUOZmWiqr\ncgcn1I9By49snosWad3dda3mzZnIJycfo5krb8gkLdwkPTEul3DxKcQiiAME\nnIRF22fyZ/YgobHP9o9y/LLT0AGVlwdja1XshgwnEyIhvgPpcK3tRjT0Fspg\nAkW20g/eyKSG2abKxcabnsFioTW6IM+qPQsS+pmv8g20q+Taaca4weldPNBD\nneiOjOIwzXNiAokS6y6zq75swbSZAHlpjdMBnw5qrF1b4Nq9nerGAKEdwBN3\n7K/4pDdPAIo2NI/HlSyTsoP+ta5R36V6X0BWV7MmZ8lnWa2/xYFNYB3ziNOL\nnxik9s5vT9NZHfBm6I3wY4o/9xFeqywzYy5CHwQzphDIlnOT8U3ncdUP7lmu\nDbUwGcEN6eSy+xK8x7Xsi9fxUy7jMxZ64QFQdkVmVlBra/tdUAAaUayMhmhd\nl2/G\r\n=NJoi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/migrate": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "@webpack-cli/generate-loader": {"optional": true}, "@webpack-cli/generate-plugin": {"optional": true}}}, "4.1.0": {"name": "webpack-cli", "version": "4.1.0", "dependencies": {"execa": "^4.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.4", "colorette": "^1.2.1", "commander": "^6.0.0", "interpret": "^2.0.0", "ansi-escapes": "^4.3.1", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.1.0", "@webpack-cli/info": "^1.0.2", "@webpack-cli/serve": "^1.0.1", "command-line-usage": "^6.1.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "3a8fe05326015cc92b67abea68e3c320d418b16e", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.1.0.tgz", "fileCount": 41, "integrity": "sha512-NdhxXMZmoik62Y05t0h1y65LjBM7BwFPq311ihXuMM3RY6dlc4KkCTyHLzTuBEc+bqq6d3xh+CWmU0xRexNJBA==", "signatures": [{"sig": "MEUCIQDTCtcsnUJBDNkCLyJ0T3TR0XmJxUpCxP3t1c9TCjh7CAIgTA1OZYEKP3trWEx62kaVJra3an1JPR1HCUAkPHx+0Z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjY4fCRA9TVsSAnZWagAAIyIP/0ob4V04V+0IvaGGsO6H\nXVMspKUUbxRX7NdG+7mMDWLtJwho3QsoTjUQdGqeW/c8fgA59HPcpxxxQPJD\nyRFGn5RDfl2ZeKDfZKDYWG2XevFBb3m0gCIqFyreKQV6UDam3lKvDYLgDzHQ\nTd7MviMWQVYr3+g1Gz0SDd0PhC5NLs2ouu/Zin58dqOIMgD9S73+Ur4x2xT9\nySbkLEUbCKMrb6f6tUJoEpsdGTYs6q1dnf0c+dS+o+WrJcFULZp6cquptzQ3\nkfHhEt+2oITvx2VKgZTDmju2BVwUpZtZMSyPrMkjDZdjifxyUOYnVIH1u17+\nkORCEZRqZkTXNkkb1qk1qPPvFTgjhxBWRvIK/vMH4DiSg1KMNGwDEu1Q9DYF\nH6u2iADYRC20mC6C4ArN4WnqNN29IHMc3IUd+dGITOiwsanCm+vXtnFL38Ek\nJF93mHwursxmgJ+YJhvPNIb9uYvQ1KSXYiAQi8S6lXXCNku/CxszUOJdXb1a\nIpL7dVKKYvzHGY8r5n8ZY/zauhjy04j/zpcHdUrjOi+ZUuVWGbE1EzMydUAp\n6X1xgyXCf2TzX+kA4vv9wJaFOBaIqLuVcgEY63EsVxJrbAGx5WSMEMlrhJH9\nm6BN4uFTvgH6ekYV4aHTlDosz9n3D8MP/xcQSLvzVzlNU+xxWHvlk8AJeV87\nQqB9\r\n=wedu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/init": {"optional": true}, "webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "@webpack-cli/generate-loader": {"optional": true}, "@webpack-cli/generate-plugin": {"optional": true}}}, "4.2.0": {"name": "webpack-cli", "version": "4.2.0", "dependencies": {"execa": "^4.1.0", "leven": "^3.1.0", "rechoir": "^0.7.0", "enquirer": "^2.3.6", "colorette": "^1.2.1", "commander": "^6.2.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.1.0", "@webpack-cli/serve": "^1.1.0", "command-line-usage": "^6.1.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "10a09030ad2bd4d8b0f78322fba6ea43ec56aaaa", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.2.0.tgz", "fileCount": 40, "integrity": "sha512-EIl3k88vaF4fSxWSgtAQR+VwicfLMTZ9amQtqS4o+TDPW9HGaEpbFBbAZ4A3ZOT5SOnMxNOzROsSTPiE8tBJPA==", "signatures": [{"sig": "MEUCIG3anT5AfCa9lAwOUlWbrCVP4jK9yJpM5h7fVFyK/9+uAiEAv9D9pEGrcXR8MXz7JUljEcDY3T33TqnG5WNEsyctiRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfovZBCRA9TVsSAnZWagAA4IQP/0CzXeNYm8bDjuJ3ua4X\n+PCzwxYUHGgdkLOuqLUAJInw1AFBjOEVC1g3cmOq/upzE7vBk1QHoqrRV6Fc\nKVVr4VGbsuqDtTOtiunxabXu7a7HwezhYJotiaDtbkP9uReXu+kabrR9F0qD\n8Xp43SyKUa3Q2IqqjjghZ30AZ4hSIVGu7uuF/mcf5EgHR2v7LRZWa/idCVFb\nP0lkjIWz4338Wj/9iVTb9Z/4wkv7XNVhgSxEM/mB4i5p+d63nvIJEXYMyjt0\nvonrhA0rjDqB9owenkXqK6y8IN4coehvLDcfh/gyeyLRK/dJhBL9s5RcaHs/\nqQFU5BJnUsxwww1wFY8a/Xa8bBZBNuZWO3sSGf25BpyaPbC9SdSaJVWakUPb\nJsotE5Ucgkak46YsUtEtdszXXjHodMScukrR0SDA3BEhh94HN3aBTWyAUX1Z\nNSy2Af9n1L5YiUPSiRM5VxxBIxcDAshU4/YI+TmQeDjbG+fHC3q6HRWM8NUB\nNTCQ8WJfGt5P8BrQLwLTxjd6SBI44aRxq+mOgGd1QwKhuoZrl3W9iNXRHaXE\nHruKdO0q2mD5zaCTov4pMUlQXTqjLkoh4kDEmN8svzV+f3qD/gX2RgJcNbPn\nxCOJUTsHDqPe7MB/opuSOET6U8yfTAG+O+ZQmonBU17JrL1FY54hNljZ/7zK\no37u\r\n=ITZG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/init": {"optional": true}, "webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "@webpack-cli/generate-loader": {"optional": true}, "@webpack-cli/generate-plugin": {"optional": true}}}, "4.3.0": {"name": "webpack-cli", "version": "4.3.0", "dependencies": {"execa": "^4.1.0", "rechoir": "^0.7.0", "enquirer": "^2.3.6", "colorette": "^1.2.1", "commander": "^6.2.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.2.0", "@webpack-cli/serve": "^1.2.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "e39303bf9f8002de122903e97029f3443d0f9174", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.3.0.tgz", "fileCount": 30, "integrity": "sha512-gve+BBKrzMPTOYDjupzV8JchUznhVWMKtWM1hFIQWi6XoeLvGNoQwkrtMWVb+aJ437GgCKdta7sIn10v621pKA==", "signatures": [{"sig": "MEUCIQCmLDdEIn1YLugBhDxZVfidpxaFJ7L2gG4p9Mm9SaDIfgIge7TTCh4FC7rG5Umu73XEav3S+pHNd9A22iZgR6SdqbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf5iRzCRA9TVsSAnZWagAA0uAP/14R3KksJbKufecu35nK\noLVCzvx14s6zZQaVowS33SJI1bRoUIbIbjGBjKqXa3hEAbrbWXtOByerF9Ce\nQWXLl/XyVBXe58h/lHXZxrFw0QS4bLFasjvxMp2bAXDFuo9FFLvm+rl6Ng1X\nYsXagHhOYcSQLwNDWNwmfS5Mlgo58c8kHR+CAbgb9P8ZgvCDeCwv3wtYCKwI\n5EkZFl4B7To8t1/N3QNR92rQjXK9jcNGRd0mKBPzPi45dm3fv0xHCR9xc3s5\noFVHMVVaY1AkM3tJho5QRGsEo2gELZgLEeIQu31zDh3y0jclVl2ggRL8jO3G\nWt7GXnsFQOVFQv0r5v2kDlgZXEmYEaIoYg31g1fmGXHRIEphPOjCqYEZZPK1\nS3QsDJ/vcAeF+tjDuodtx72AeUfz83g9fED8RA4zD8boIERTu2r43123Xfyl\nqlPtktWicRcTOAUIjG78mEd/2mszCzplZppphv4vrBrQFsbO2RS0/pSTX18s\n8vN52GG5HQyZwO5PrECjAY7kk5V36Vod7waHOZwPsrLu1A+nAFjaQDUYi4ky\nZW2ehg5L3CT7b67v7y7TpHzknYaxjKbQ2TbUC4TCFzt/9WhNNQnb1c7JnyoJ\n0LdBlS5MIxnPx3pIhDWWg1FzjdtcE6R3aphHcYcs90yEe8wPSl5KTPh61gFo\nFYaw\r\n=KKbP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/init": {"optional": true}, "webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "@webpack-cli/generate-loader": {"optional": true}, "@webpack-cli/generate-plugin": {"optional": true}}}, "4.3.1": {"name": "webpack-cli", "version": "4.3.1", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.6", "colorette": "^1.2.1", "commander": "^6.2.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^4.2.2", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.2.1", "@webpack-cli/serve": "^1.2.1", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "87a7873bc9c6a4708aa657759274b691e72a04a8", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.3.1.tgz", "fileCount": 29, "integrity": "sha512-/F4+9QNZM/qKzzL9/06Am8NXIkGV+/NqQ62Dx7DSqudxxpAgBqYn6V7+zp+0Y7JuWksKUbczRY3wMTd+7Uj6OA==", "signatures": [{"sig": "MEQCIA4YuyR2Vdp6vATGwFRNpH6e96+mBzDrrbwlZvBHOMzOAiA6ZJw/i+mhpsv3ZldB979Y/l6qiOczZgsipMmZOJMu0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 216319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7gs5CRA9TVsSAnZWagAAPtYQAIWW3fCRbUR2FqGeXd+L\nq35EF3ryZX///G9BlhcEsOjR+oUhdaF4YD/KlYuy1BRIW7d8t9L8dKeqPzSl\nFCG60LzVKBRKUzvCZojA6aITSYjgzL0vLI5f9RUfKKBtT1RygkWylmYjd5Zv\nUczzvui59k75zUYAwFuoTKlobz2a/Mb33FlTHvk1TvwvoKCRP/sfPbUzVsRL\n6RxPF596XS7J2FgcnAL5FXpJ4oepJ/+w2BzehX39WgCFNzdeUtTk4bMJAGxq\nQu4bUjEM1hAGRye/gC32lc8wCmZ+9PEq//405oLaW/a+GklO4xxty4nLj2x9\nv0XTaW4FURlbUdVkwmsQYh9SmOWgWMKQVKSF8X9lHibPzEFGxV9isrUAu0YZ\nGiKt2rol+bUDdo3PXOf9hnfjmqGArtweTZtd1WgMy0fPrE+WTaX4UgoBPvql\n5Nzbk1KVEciyVPyQUpLHJMy2Hrs2YGo0zfCQh9j0WOIu7UgvffHQC86L70A7\nyPmnQ6Lh6z3QwL4UtoRSG0jtKxUOx/mfM3a464wuWwaTugN53fcFONcoDlgW\nHh13STE1bFIsONQAFWkcXqNtJFgRjKGgo4AVBqwNoL1d/BMzYZ3/QiELKgS0\ngwLHKgOYPaxOJWTYm99rCivQJWlrRWug6ni4KY+/rGmaBBFxfFhw3zsQqxUm\nxIcT\r\n=Yco0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/init": {"optional": true}, "webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.4.0": {"name": "webpack-cli", "version": "4.4.0", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.6", "colorette": "^1.2.1", "commander": "^6.2.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.2.1", "@webpack-cli/serve": "^1.2.2", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "38c7fa01ea31510f5c490245dd1bb28018792f1b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.4.0.tgz", "fileCount": 30, "integrity": "sha512-/Qh07CXfXEkMu5S8wEpjuaw2Zj/CC0hf/qbTDp6N8N7JjdGuaOjZ7kttz+zhuJO/J5m7alQEhNk9lsc4rC6xgQ==", "signatures": [{"sig": "MEYCIQDzD49fzU6WmlU2NXtO4AcgKtRkPQXYx83NL9QBjcBZtgIhAOrkP3rleoKc4dTnnwqg894eTN+JU2ppfZvf/bG176Rm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBsJECRA9TVsSAnZWagAAjqYQAJOcT01aSQJUcyoArJuP\nogq0IxBMm+OfVzSe/j2Rhn+5o3JjnXODFSq9gJ4hOH+N8TKGRqd2DNam/rTW\nvMtd2f17zr8HyLvP8X4Aql6MTUrPJtEge0frjW0VzXt0IjXwbxKcNi8nSD9u\nGykp1SKbSnAEx5Vtfwo5j9yKxqdn7iH5A5Gf4ZBf7YbiQWwvWDOxc/j+iyay\nV2STIN+6l7RZUTRqeopjnB4jKuq8F3NtoCUCGijoyaNzPLFppznVmem0Ljj/\nqQkqSCNSLtkO4SGzsZcUACyzks8juyNlu7KMYohWya11A4WL98yjIUd0m2p3\npIc1FkuENrG6ybY7R1WajgoqdDaNp/WaB1e4JZH8c25ugnoGV00v8z8LzgxX\nxJvP4tfxljbj3HBUgZT2XJyYYqbv91cd9iTiitdKK0i00pCWSBXRi53n6Bkx\n2Jc3GWevxwp4WPJpDGGu2DTrPZizIPO/tnWNyk3nF1sZIdlzM70o54qntfXJ\nl6DKPUTCOwqBpTaXYWxocFnNz3Wrz7uuy+nlMn7JJLsbJw+DHm9J1LFwgemG\nx740+AFT3ifXIS920TFq6fRSd/uoESlI9pQ4TgbmkAyGDZmwiD9hHlzehET0\nDmhumnNlaWpIa3x65KRGH0q8kuhNqoaRyfyx8H4PwtcxLsFMJajE+xWIt5C9\nSchx\r\n=+/RU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/init": {"optional": true}, "webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.5.0": {"name": "webpack-cli", "version": "4.5.0", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.6", "colorette": "^1.2.1", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.2.2", "@webpack-cli/serve": "^1.3.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.1"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "b5213b84adf6e1f5de6391334c9fa53a48850466", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.5.0.tgz", "fileCount": 32, "integrity": "sha512-wXg/ef6Ibstl2f50mnkcHblRPN/P9J4Nlod5Hg9HGFgSeF8rsqDGHJeVe4aR26q9l62TUJi6vmvC2Qz96YJw1Q==", "signatures": [{"sig": "MEUCIDzB4wc+nNd7c4yr7x9+DhugsJSX/ZtyKufNFKHoQUxfAiEA2TiXFHKrS3me3BHUDpMpZFB4gQ118qCxSZ3XTBOyLCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGYX2CRA9TVsSAnZWagAAZj0P/0yIBIYempFQWqbiAv5p\nK62ajDaIid4NFx8/5UAhYLZ4XRq9A0RvfUAtWWIUtec5wycGwQX6yDIC8Ap7\nBSTbx+U0whj7h3rENtEYv+Gw49PZRTUHk0iTUJXIxLVd9WfppsQvXZv+4ldv\nHWPyfcGCTGldU+wm2H6gPa4T5a9mwm3C70SDbOIaiuAJH5T5rXnKZNlI4Xue\ng9MX6DOkia4UVNpStvzyA5Yo5DBaXKwT878pCP7xegLa+LCUcdTX2VA+31Ju\n7z6+NnK8/RyRRia9BecNwpxkKYFcN1IYV8goZsK9rhoe1/7jWV+YaVnakvX4\nfx3P25G5Rtl9oN6utav1ZTyygn3DxGEUAGpodeDVe9cbl3MKqYBpT3OtJ0WS\nNfWHV4UeqGhJr4B+0SdLZc8biEOrl6KGD3CvXLokVGE1CXJaWXPj01+ziYpJ\nNGkSIhqqjUQDb5w48wWzFaBk72WdkMls92yw/0Esh3JKAb7oJqY+uzuX1w5C\nmZhmyat2ZYjSgsG5NXeQRVyub9/Fk6oc/CiZsR4bN6sNu9r+O54X7P52jocy\nE30WEnxTGyhlHk+2Bzzpp27RijNpKoNHGu78FJpM8jyQ/xj4aBooRfzqtaUE\ndq1Hu8eEZqIsDMwtt1B5BGDgmnzJ3xEe/WiwKGjiyT1py+WwZNHTXOMEy6Hg\nXH3U\r\n=frq7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"@webpack-cli/init": {"optional": true}, "webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.6.0": {"name": "webpack-cli", "version": "4.6.0", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "enquirer": "^2.3.6", "colorette": "^1.2.1", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.2.3", "@webpack-cli/serve": "^1.3.1", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.2"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "27ae86bfaec0cf393fcfd58abdc5a229ad32fd16", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.6.0.tgz", "fileCount": 44, "integrity": "sha512-9YV+qTcGMjQFiY7Nb1kmnupvb1x40lfpj8pwdO/bom+sQiP4OBMKjHq29YQrlDWDPZO9r/qWaRRywKaRDKqBTA==", "signatures": [{"sig": "MEMCICVJFRDw50gtsw198pzvqUQSA59kRjPXNkGkkh867mnkAh9y961JF9HrdX0IOjpDog5b74XQ/gm6x3OaMqt+G/Ln", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX1LfCRA9TVsSAnZWagAAmqMP/0y1ODzTYQSAQa4ez32o\n3uDnzl8hjvqIUq4QGBG2PbRfxkMe3+isQH2eFB6XhGKGqr3bQT42Uxf2VFib\nDRB7S2kUs+NPC48YJ+O6VT638cDejr5af3RfAGTHR1rgCrgaFXIwu0LF9cTD\nfBllDOtVjKCUmx+xMr/BFlGcDKeHpJFOI0AoJtISe7lgyAeAiCdY1nzPMtrI\nLDTfRV5lS+HoW9gE+BTcaN6kMPfLOvzlTiEl0owKpzjI48H0A+vIXQv3Dm5s\nazWstZIbu+S8+fVDECUneNL26StVDS355hv6wS6bnEc9gwxSSiyHoh73AIwf\nMyFVb8CFtjs6S619kgHfWtTiQQ88iqK6wHjk1cnA1PKO3YCY2lSv8+hlmKzm\n7yp1GRNDqfqqqC38gVIopCEfNRvRcnw7GZVrPMgYJ3LLzEP5B2mfo2UOd+9n\n+B8ran4HpXlvaKBJ68J9cFVZjHY6TfmIBT9dzT55mzweiJ2p4cpVMX1vnT4y\nUOZvm1VT/bYyg0qiNZhCMG3rBQvR9wZp0mmsYj5vpDDiF6qpzaSA3i98owaH\nbYrBSgi2K5KC7nK/ckhQGz+NPA4Hr70rbFoER4aTP7ZY1ci9mQ22+3tvf+lD\nL3IMKV0aaPC0zgce4AErrYyog/d1pI4QHxJWTPUPfAqj1jNqB0ympvQCmRkO\ni27d\r\n=+o7m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.7.0": {"name": "webpack-cli", "version": "4.7.0", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^1.2.1", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.2.4", "@webpack-cli/serve": "^1.4.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.3"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "3195a777f1f802ecda732f6c95d24c0004bc5a35", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.7.0.tgz", "fileCount": 19, "integrity": "sha512-7bKr9182/sGfjFm+xdZSwgQuFjgEcy0iCTIBxRUeteJ2Kr8/Wz0qNJX+jw60LU36jApt4nmMkep6+W5AKhok6g==", "signatures": [{"sig": "MEUCIBnk4ClOBc9IavbsgrSdlh1eK7wy6MxkB5eQm+m4UDaDAiEAnoZsXc1DhTCRPubTAsrRgbYMkdq8HdqxhBNKGNs/W9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgk+ryCRA9TVsSAnZWagAAcn8P/RcJXkWgeI1BVO5CCO0y\n1XTXfNS25QrY8jyKUQj14e8SmcEpopIHa4qdSBpGRyzjy2RbpXSMzvrOfOSs\nKiLWmu0f2cDoJZxBKP5WjSbWZFQuFJfcp43/5mfbe9XvEiO0+z5e4nOfaFMT\n9fgC6o/iTkeP37JhICWGMRuyUpbgM5Gp2b3ydHy7b6n8KSV5FY2w5Ys2OW9u\nF1KaNHphXX+rC6O6kxiaY5+joXUC7eqsRHnMjywtltCQTEqhIn3kZF5advF9\nD3+eqXJ1iTKpQeBkrYIdgqwxUz9LPwUyp8ct6lhT/tWgUSgFOIvXhuAloXa/\ng7DbQftWKz/UwmRI4wbeDJ5SnNSK6cH9HejJ7E732ITj32/EITFN8uMQnN43\nesJGmnZkWkQ23CK7nkXIyR11bbEjUIztVYYUPAsA+Pluz8+qt7T6CAY0/AEE\nZud6K7XFs4cepXw+MYhn2eK5ljXYpeydK9KufH+RKWs8yWN9xwYms6Gvk++k\ngYOmVNZXsmIRcCvXWkbVcU5tLtlopGGhDL9P1xGxifoumocZtfWBMYNQhlQy\no+5Rvh2jrF9HhuCvI8uI1gL4+AhznAaA/8Q+sEWjASAUnh0SPReKfzrMKQL/\n0CMsdE9weYKgQJl2KVFtW+UB+Wh0mxmBP63ZCx1K8AjdxTqkxVOOnnYZOYEI\n8Ud8\r\n=sThf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.7.1": {"name": "webpack-cli", "version": "4.7.1", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^1.2.1", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.3.0", "@webpack-cli/serve": "^1.5.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.4"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "3fc7c2699078acf8e013d8bd2c1bd7a596ac57a7", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.7.1.tgz", "fileCount": 18, "integrity": "sha512-DJPd63AY53KXWOaD8cB8CaHR0epVP4O4GBIAk6wCPQHJugrAQ0B5kUkCg0c9vkIrD2kA6CXCmtWqKQsiVTo15A==", "signatures": [{"sig": "MEYCIQC6jJb5CraGHwDtHI1XZ6UHiWTrSc4BtQotU+auq3KFDwIhAPtph1ZRYQ3+lXGtgiGQijELR2IrLwwEvI49WgVellyF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvhoICRA9TVsSAnZWagAAB/cP/3ZFAs4HKHgP115pkygu\nAtTeCxUEzFUitg6qjLNbNbAJKi14d+d9Z8ezWe58oy+WkEXkzgFRh7oS1PKG\nNbmxAEMsRhd0Wvf4+sMjDCs8V5wdS4O34xd9c9b8didkiEl+BQELza2oPrlr\nw4qUIUPzWqHOJvIrp44eZE/uKIba6/+vNdZD4+LIt4boWv8E4MAVcqminBWZ\nGl2dPanTtZY4+zbvI8MHitlYzaCPQxphi6nQ/FbCHS6NAyHejAbOvkI0lP7X\nER4Yd8RPN8Ru9P4INX+vPlsKffxFWmHv5PK0d9HmEHh4h2WP4QetesA3uybE\nK6NTkvK6zmcOnJXg9eHNQVaegwBkN1q0QpQ1LfUwK6BkuXIE+o3lkphQSHlb\nizwe0uvBCsDJkxEDh4SfHxn/oUpbCXyQnKaff3WTGAaaV33niafMX9cRPxs3\ny5VVBsEyLgM1e93XQ/M9ykYJ4Bq9q0ehHeZSLAdoKzz8WEG4YN3tXeYdXlNL\nPuCYQI9C/Zh3oT5M3NgXaqZYHhhhiX5UHyvtnmQrZQuJyh0Pwkt/sqBMWYVc\nF4Nyc52qPTCfghenkovAdaRGhTfgpFHsTgZsCAanHTYUvMPcAw+493E7OTyV\nX0n0my1/voCp9XKuHtt3BMhIJqM1SNcjgklfUncOOPg/BtXEA4+IUDVMf+0m\nvu9N\r\n=Ruep\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.7.2": {"name": "webpack-cli", "version": "4.7.2", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^1.2.1", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.3.0", "@webpack-cli/serve": "^1.5.1", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.4"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "a718db600de6d3906a4357e059ae584a89f4c1a5", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.7.2.tgz", "fileCount": 18, "integrity": "sha512-mEoLmnmOIZQNiRl0ebnjzQ74Hk0iKS5SiEEnpq3dRezoyR3yPaeQZCMCe+db4524pj1Pd5ghZXjT41KLzIhSLw==", "signatures": [{"sig": "MEYCIQDSHFxXeiv6Pdsmiyx7xZTE/vW/fJd44f47BNDvWyv+HAIhALZCiqGPlB1uauA4mlBliJNV6mf9W/MBzTyhT7fqbvPq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvkuVCRA9TVsSAnZWagAAKoAP+wX2LU87Pb7/S4O0AzFH\nNxrvLnFi85ZuLT3h4zWm4EPtjfdEJrZFNclFdBMHEnaeoymi62RZsJJjJsAr\nSXvaoXuaPXQMMEmFdOxkRwtTFShdn2CY30N7EZx1k4uqqIes6/yGLSfOwwbA\nN5esfpSvVVO3JcEKa5QzVG1WZkM7uZOqUV+6TN+8wgdiPsEm0phvN8kxoCU+\nKq/4d4VC9IRQHdbwAMJJLBBh4pKm5JmXOTW81VnmeLgeAZKOZwUO1EpgJ/F2\n3V3dqp8Czwh/4ZsbyqUzMM//zL+a20SUNrdtiPWCeYIh8lYABVnWdQ6C9d4Z\nMzyEHJaB2PXNg4EM19/4xSs74nvLMC4tN/xZDw/vge+IPR/KlL4sfebodI6R\nRqVmgCk7xu9u0q5kd4W8kQjx3Z5NdehSS8n56YdFaYXdhi1srdKHEdg5Xo2d\np0jDcMRmr9W6eP+92+6ff6xA+0tLetmCsTi8v9REOmzwMDxqJC3AvyGkz47u\nY9uKXoeEVuktb48qA00rijOaBhvh9XUxQNqNM9YHZW++AWYU6b7VB7+cagVE\nm/wMJc/P/2rEkc8hz+KAzOuOUBSX7EAFvB3gzkcxt5hRVYaq2427rYwjYzi7\nb/3R/iVJrIxLs0LhQUPYegthHwKLu3zjcmVaJ8AkRefA7wQAPbHK4f9oa3KW\nAON1\r\n=GVBH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.8.0": {"name": "webpack-cli", "version": "4.8.0", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^1.2.1", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.3.0", "@webpack-cli/serve": "^1.5.2", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.0.4"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "5fc3c8b9401d3c8a43e2afceacfa8261962338d1", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.8.0.tgz", "fileCount": 19, "integrity": "sha512-+iBSWsX16uVna5aAYN6/wjhJy1q/GKk4KjKvfg90/6hykCTSgozbfz5iRgDTSJt/LgSbYxdBX3KBHeobIs+ZEw==", "signatures": [{"sig": "MEYCIQDdJbCJZyMTf0Zmf09j7go74rFbXHkgDTIlKnAu4z/F2gIhALRxST5ih1lwQ2CVgDiRzgiDgPfWS+bEMun2xPBHphX2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhGXOVCRA9TVsSAnZWagAArN4QAIV7/65+h6eqeNPrT9oK\nB6y3zRu1gA2Cxqp4oGu8lme15QNizoa9AUeG1sS8g6Ly/b0lxIdkfCmXbqkn\nY58IvuXobOItYExeuapbgsumQDVkrmuSbLcUOyW3tgvWETeTfwBjipsB70gs\nRO8qiz1vdotc7PNNy/yaTY7pmUSZmNiNbkuSKC8YexlyA20emkMNF3U90jtC\nFNDjhs64AN3RMNpuwzpvS9fN3V18wiGBgrJ4ym9igN3NmWlHoy67Qz25xNje\nXwtxVGl1duB46z4y0PZq+oOcs87GwzeF0bDHDeIfF9Bee0Pk9oomjVOTGo3M\nO5qE6LjoAjXw4o/ouT81NdLWSuyRZ81826rhA2f3DObcvPWvOEWnEHNZxiMi\n+WHT+0uRAiTWg93WbaSGLPb8iB4UUO2kR6yq7nW9McGjJigP5sS9u0b40qhS\nOU5DFGfmLjYucofVGnYsDZ9BFV5UYTdF7OdPl5gaXQU0WFplq4lIBuOh/C2T\ncMYFBnkNhlPBC9GBXiENuwGiFoYfvxv8ckVGyBaAHjhEdeJWlzcjHWU5TK69\nWbIkd+8ZcuekhTeTyA6Q4xAbzR5QSbq+twuTDNFuCzSRSUSWyNYhf6d9JRoW\ntOW77ebUlPdCBugjgoT7CAjEyCEiok0yQCZIaA3WeREic4ep+4BteR2DdWDM\n77mc\r\n=EutX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.9.0": {"name": "webpack-cli", "version": "4.9.0", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^2.0.14", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "v8-compile-cache": "^2.2.0", "@webpack-cli/info": "^1.4.0", "@webpack-cli/serve": "^1.6.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.1.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "dc43e6e0f80dd52e89cbf73d5294bcd7ad6eb343", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.9.0.tgz", "fileCount": 8, "integrity": "sha512-n/jZZBMzVEl4PYIBs+auy2WI0WTQ74EnJDiyD98O2JZY6IVIHJNitkYp/uTXOviIOMfgzrNvC9foKv/8o8KSZw==", "signatures": [{"sig": "MEUCIQC2Exx6pKe/FZ3V7srM0HPsh9OizaZEOCbO1dtfdEL3DgIgeJgg1O4lR4OPQOv+kdWwM+FKqhHut8bv1YPexPzCJmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82455}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.9.1": {"name": "webpack-cli", "version": "4.9.1", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^2.0.14", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^1.4.0", "@webpack-cli/serve": "^1.6.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.1.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "b64be825e2d1b130f285c314caa3b1ba9a4632b3", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.9.1.tgz", "fileCount": 8, "integrity": "sha512-JYRFVuyFpzDxMDB+v/nanUdQYcZtqFPGzmlW4s+UkPMFhSpfRNmf1z4AwYcHJVdvEFAM7FFCQdNTpsBYhDLusQ==", "signatures": [{"sig": "MEUCIQD7ka4H4bKNArJd3k622HGOPy/40yFqVqBEuSWyi8Wv9AIga9uxt0vtZ4hSNbf6KOq7IRIANXG7FtSjr9SOknilG2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BDtCRA9TVsSAnZWagAA3RIP/Rk6mmccJ77Vn9YAAkov\nSxKQvpdcVuIclZS/HTp+UHGBLw9m/7qMLTsAgwo7ZmxyxFSrIiCAycfK33X7\nRh6TFX08VaF7mAmEghjkE+ciFzHEzT+rBIm4iX1tsFPt8ky0/MekTE8oheW8\nlS3OGY1ykVdxRan7bSW+kgmt3m03GALREl8XqeGYFOvb+a6/KFH322xWlZOQ\nQubxbZKaFhvJsRRx//MKtUY1qz87txm9V+La6/1djaDemUwz8yJ23SrD2rQq\nz+OgxMg3Dg0NPNJMNHe67yLjjrZ1ZikW8gwo1QY9/ln3aEuveqbpGAXEOp9d\nJ2WO0ig/SHVu5+mR/sedKKek48thJfWq5p76cR5mE5fVa9dHMWGPS6Yo4sE9\nrtTgTrcBrYVSDWPsjWjjFQ874k0+3g3OYRKuEQit5IiabraxAtEFLeCsV4/t\nRV9NfeqY/xX4kq0x6ByJ0uKN1vfMj8rKSyb7iUTHNiRaYnuNoxHfy/eJKt2d\nVAHt818/UQrLl8JyQDRmIPSv3cNb0FR9/3ifb485VlJto/sC0o/3oCVH7M7H\n5HxF42mkLyeGhQV/EjdoKn+nE6oVsuqwAPf8VUKwVkqzqmb1ygINMRosovzZ\nhwvNFKlQjm38Fn8akRFq7QJjzg1SIQSRg1h8QiNq/v2blvY/gqjefwWOH4oq\n/CI2\r\n=v/pn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.9.2": {"name": "webpack-cli", "version": "4.9.2", "dependencies": {"execa": "^5.0.0", "rechoir": "^0.7.0", "colorette": "^2.0.14", "commander": "^7.0.0", "interpret": "^2.2.0", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^1.4.1", "@webpack-cli/serve": "^1.6.1", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.1.1"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "77c1adaea020c3f9e2db8aad8ea78d235c83659d", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.9.2.tgz", "fileCount": 8, "integrity": "sha512-m3/AACnBBzK/kMTcxWHcZFPrw/eQuY4Df1TxvIWfWM2x7mRqBQCqKEd96oCUa9jkapLBaFfRce33eGDb4Pr7YQ==", "signatures": [{"sig": "MEUCIFmIIHkxLSZmzKNv2SiTabk7cLjzH2PsndpmwLXuZmANAiEAjkcxCMx93ypHwqbnjiNF3TrF2b8JJSyXlZ0kwYcyW0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7qj0CRA9TVsSAnZWagAASO8P/Rm1vQH5o2WGmlp2QCxg\nITfmNvGgyjyNmwSXE1SqLKV2Dz8FKRGGaYaoqSuAeXb1N0d4Vvt27g1rkcC6\neZ7SXCeeI/RWTfbBqLBon9FNbZVkq5mUHfvExr97JkWYhoQ0fBQdmy3PaRXx\n7YZbXNYNhi/hSwUnObJtPzTGflO6/azqF1E8+vutd8Tsv6MOrYVNQdQGrcxk\n3tCc2Cs8JUfCWCmAw4WjEKAkqH4xagt9zWH1fXE23jFFUC6Lf2Pyi8AfgXhV\n1+DRFhS2UkSUJ9azAnnUYE50uVUsry12GkgO3PqoTNHFGaSIM9zqB0xyfxqM\ng4OVQ3yESukKJywZZW1/SCJnHOq/FGWt/fgNimgNj3Apqje3KacwO/2CvUEZ\ngNw5aeq1TvfJoSPGb7RwwabTuryKwLA8t9fXswqMnQX72La8bJ2r1b6cTh3Q\nCuRK3KcG/M6MHyhPAT/6MU+k25AH1YCfU8jQEF2BMHxrTYYebc5dbKLmy6IQ\nRjU5tOOcYcPeePmcSuklntDeSGE3zf2XuKFjM5TUdOcRywwa8KFwNapDehDH\nEJvA1M9fzwlpI/ptx0lNDZuTYlfGjqvUmthP++gQLNM19dATqw8wftFsX1mw\nwW9tAQlZzZS3HptxguwqC7XZQ00ZiiswVnc3oel4ZBJR4zn+uT3MiUMVQ/UG\nWJ7B\r\n=vggE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}}, "4.10.0": {"name": "webpack-cli", "version": "4.10.0", "dependencies": {"rechoir": "^0.7.0", "colorette": "^2.0.14", "commander": "^7.0.0", "interpret": "^2.2.0", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^1.5.0", "@webpack-cli/serve": "^1.7.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^1.2.0"}, "peerDependencies": {"webpack": "4.x.x || 5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "37c1d69c8d85214c5a65e589378f53aec64dab31", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-4.10.0.tgz", "fileCount": 16, "integrity": "sha512-NLhDfH/h4O6UOy+0LSso42xvYypClINuMNBVVzX4vX98TmTaTUxwRbXdhucbFMd2qLaCTcLq/PdYrvi8onw90w==", "signatures": [{"sig": "MEYCIQDOHlAmqd3bJvoZum6HcrBGYQeYi1k/4Cvlh734XJ6gwgIhAIJnThgMq+Vav7iAclT3GjYRL53EnR3t9hyBzrMHEryH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipodjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrXw/5AflOc9jtUibN9oIAKZ4sJ22Qrrss97AbezZlCKdT/GZwptSw\r\nOk5nT3/u0vgxofZqmkLmgK6UKR6dAbO+5i7ewB2dkM+VB6jbMbujwACD7CYk\r\nBoyFhfnJUZulDjo0yezmj1ECedZQg/hLBMusBAMkb/KvfF/+V+WNJjzyiXGj\r\nc6pwS4UWtBdWcKfDkuDZGE/d1j7D/+Eivlq8b8AMcZYdd9SFuDDrtNcGtz0Q\r\nXurn5i7EXKNs9kPmc1Y16ZDg8tRz0AK3I072/wL+0qNZOJ6Du/Y9PiO5lNiQ\r\n6kM1dcLGx8a4krqhO7VuDkKh+/cAZLYk1qpPxIF0bcl8TM73nJ+eIaeZa9w4\r\nEYNP4VfyFQiwB2xAYphbvZlPEmvQkj93QgXXoHjweQD5yMvu4qHwLew7Bbqr\r\nf/TyR+njdtQ0QpxLpzkcb1TLzb2+5Bku78UT9Yl166EFUMVkfUghCv+AhQ/L\r\n2+LFzxbBmENTkDZMt+t9pgwOK6blo1OUH/DJqm8HNZAlvHhnI6wGymb14A0P\r\n/dbrhwxVj8/rpRwaKqiNtHuDrAmEtZ1/G3xsEKSKbMGa8xH9kszBj5gF2LWM\r\nvJEDQooDdE5QJpTgYgTlROvXGL0iwf79CgbZ0ySSNs/wLXAZXA4fRG+BmIdp\r\n3Fa+b5JQmsFjRCDaVNSeli1nxCwM+yW0NgY=\r\n=4kT5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/migrate": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.0": {"name": "webpack-cli", "version": "5.0.0", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^9.4.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.0", "@webpack-cli/serve": "^2.0.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.0.0"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "bd380a9653e0cd1a08916c4ff1adea17201ef68f", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.0.0.tgz", "fileCount": 16, "integrity": "sha512-AACDTo20yG+xn6HPW5xjbn2Be4KUzQPebWXsDMHwPPyKh9OnTOJgZN2Nc+g/FZKV3ObRTYsGvibAvc+5jAUrVA==", "signatures": [{"sig": "MEUCIHE0V+cgy8abogz7gtYPKbZGcKTR0lClQeMQVEZdXGFTAiEA/1db8cZvTtqSy/Jq293zg+klaQ6+O92vUZak9WmQNBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdbm0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfyg/9HFZfNWlbR6o9Sz8tv4KZuGYvVDlHRennzJF+J83b/GTFiKvS\r\nA2lyRHjAJR87MmLt8SCgmTW/LFlhFucB/2p/eCadOj84x4ME/heF1bVXeYsX\r\nCQ6yRsz2NOygyQlv9hyoqEVXlzbKHUwadRWU12oEQNi6WvlxH2f4VjTUK1OQ\r\nyphSKX61pGPoXWBIIclTtDrlkuCpgkcSpiQsFfcYkFJX0QB+zolbIxiSSobq\r\nSvlRWbYhwNncRvg1pev9e9g5bhO6ry8xEuygmRbxjsfbnOZ3ivyfBP0a5JXM\r\noiZzdqsCJaRMwbg5XEnR6kZRTzfqfLdSAgiMjFiRmwgb/2SkZJh52j5RqgDw\r\ngkxjMcwkJzIzY+CXwa5bEKwV3/kgLeChtBGkQIE5Zi5+lYoh6QRqFEvxSmuw\r\nPsk4HplezMCMpjfQTseiUVUk1ttr7DwwvtptdhXzUyKHyjk7WIU0VekYeaMQ\r\nNUHFcIK7ejAVmpAJnUgdiyHyZnSnb2YnResHe9aC8ShEpPpkTcBjCGMlyCHH\r\n+/IbqWkP/3DPY+ECcite/xYYkxd87HoEBisNB85U8X9A0wJ6C4Bi+Lda/E6Q\r\nTBkjrVMGfH0y3SzDY/uANh+2lLYnFXomVqOpjzx9xjL3uL7cShcYPXIjhnof\r\nk3Dyj9nqMFqYFvncEM55qCgUs9qYLvNYpEc=\r\n=8Yiv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.1": {"name": "webpack-cli", "version": "5.0.1", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^9.4.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.1", "@webpack-cli/serve": "^2.0.1", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.0.1"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "95fc0495ac4065e9423a722dec9175560b6f2d9a", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.0.1.tgz", "fileCount": 16, "integrity": "sha512-S3KVAyfwUqr0Mo/ur3NzIp6jnerNpo7GUO6so51mxLi1spqsA17YcMXy0WOIJtBSnj748lthxC6XLbNKh/ZC+A==", "signatures": [{"sig": "MEYCIQCyAJwdj+50VfmwW5yXZU7yQaLgW+rWKvn1VFImdbivrQIhAMueD5b1zZ17tIiQiObKyfbn7D+mf8IURZk1J6+miZ33", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjityACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxiA//f6vPgy9JSb/tjgUVW6BnVkCsNwt0uszpVg51PZfFagMvD4Cg\r\nrPlq9sBql1FP7qHdeUdFJn1JORVKDT4XsK+5LFokfM8iJsdb83kKUjx5YcNZ\r\nXvc17Ph2XtlF8j+j7mmmbFt3buCLUbs8patsKzJMxf1u+wbkOB1flZWZimoD\r\nQRsNiMSTr9mZFHiGg/+0ZuLqK65DA/0SvRD+AthnsadpcGE73WmGJsokc8Vn\r\nvJOGY+L81KF3f9u8Oi/1TnffRgFU7RGsEFbbm1IRieETs/smEZ8R/gPKdTEn\r\nv8k2pkOy/zWW8pXkahNZNJFEBdBaYHuOgCSTunmdWiF1kdYioOgelolESvcO\r\nYeywdWfc6f1zvpkvwyHkYejyDzjuHuow1wwCvy2UZQxq/Qav+WjBq8TPovk6\r\nyIvfE6mAx+6d7ptavDk8TC4GjB7EyxRXlkKS2pfKTcTauREzjTmMb+uV3YiB\r\n5PGOyuYFR+CMgjQr00lp7Oq/yCfoolmgz83G2tQ0GqsnaoBq3MWFAY7h1EQW\r\n5DRs64WyItoFJzRq1+kQfEjP7ngj+6XFMHmky2jKn2GnLOTI+bSnJwYt8V4Q\r\n2wZngYbirxmB3baAZPsEG0cKiapxhX9L9lzkYAGU51P7C1toJKIUbavxT9lI\r\ntFoUpFXXsUpZP34i7gJFluSJRq+9xqfNepQ=\r\n=3HxJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.2": {"name": "webpack-cli", "version": "5.0.2", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^10.0.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.1", "@webpack-cli/serve": "^2.0.2", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.0.1"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "2954c10ecb61c5d4dad6f68ee2d77f051741946c", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.0.2.tgz", "fileCount": 17, "integrity": "sha512-4y3W5Dawri5+8dXm3+diW6Mn1Ya+Dei6eEVAdIduAmYNLzv1koKVAqsfgrrc9P2mhrYHQphx5htnGkcNwtubyQ==", "signatures": [{"sig": "MEQCIDwQiPO8ztcn1MbzbutQdhxuslYfeyPh9UlD2t2jCYe0AiAwLVObuVIEnwBrsAEXJnzZmjHGGpqeUke8MEjQTiothg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQrShACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSEA//cs27vjAVGe0kyH/bE8m1CPBTgTVoUIMFxuFDkRrMDBbfC0qC\r\nXOmlqnPVZAoMsbk4IX1MK0Zz7bmsv/6cmO+jTilxR7SqS2q9gsLdSN/cNjNO\r\nj73VRiCHJSlEdjhyFsDCQAwgqB+uSGpLq5vD0dvlzy2znbfuZGIU+7R7tbIA\r\nPq9maeaCqYkcAriANNJti4kkPze+fDW8yyPvqC/JZSQTJ27mifDkb/YyfGgH\r\nbnqQWxqFTnw7DHkhedO5Xb9tQTip9H9ZyismbFTCVThyFRL/OZaSFY0As1eA\r\n5fc3+0/+9Fu18xNBDC8hRcDVsDXIJR0daamQMcsZ4VRdEMySRwM+jCWP9n4s\r\n1QXIdn6xKYGZw4EgXQBWpQw6yFp6Z1gOBmX7dETdlsTEIZvGo5FvMGE2lb7e\r\n/FWlbpgc8l7XBBGkQl6qn3csMtQ8xlrzd+jy+KhXfvtZeVHmFfuU/91SIe/2\r\nAFz7dFoS7fUfiYj4lx17v/qk1phcHxXPbPAJWIRC/DKkelLYgXaxaX+Tc/tv\r\nMylJ4Cpui6XRstXzVIHTh6rdxRHMW7GRbpOD2THp7NEs560RIS0mKj9rRine\r\n+P0DPbzTzc4cHGGH9WpMf3hVYyxXwfhs0XxBOF0aHpVPi4gTtJEEu9PC/7XR\r\nk7aeeroT2/GQg3wVV4YNYc942V7Ojw0Wu5A=\r\n=011q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.0": {"name": "webpack-cli", "version": "5.1.0", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^10.0.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.1", "@webpack-cli/serve": "^2.0.3", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.0"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "abc4b1f44b50250f2632d8b8b536cfe2f6257891", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.1.0.tgz", "fileCount": 17, "integrity": "sha512-a7KRJnCxejFoDpYTOwzm5o21ZXMaNqtRlvS183XzGDUPRdVEzJNImcQokqYZ8BNTnk9DkKiuWxw75+DCCoZ26w==", "signatures": [{"sig": "MEYCIQCwrC1GQQ7tnBX9B13vf8XFwgatPbwfsQENfmaVRHTngQIhAIzOdTD0/UckoqUeAoDiOPruh/kaf60RYtl6sZzEP+qV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107036}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.1": {"name": "webpack-cli", "version": "5.1.1", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^10.0.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.1", "@webpack-cli/serve": "^2.0.4", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.0"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "c211ac6d911e77c512978f7132f0d735d4a97ace", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.1.1.tgz", "fileCount": 17, "integrity": "sha512-OLJwVMoXnXYH2ncNGU8gxVpUtm3ybvdioiTvHgUyBuyMLKiVvWy+QObzBsMtp5pH7qQoEuWgeEUQ/sU3ZJFzAw==", "signatures": [{"sig": "MEUCIQDTx9FrTV+yNEPM/iSXSjYUYvVtZl6oncKYGuGWUITpMAIgcwhq3xrp4B8x+qM9lU6ogEr18VV45KNjJ++rGfB6QEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107046}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.2": {"name": "webpack-cli", "version": "5.1.2", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^10.0.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.2", "@webpack-cli/serve": "^2.0.5", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.1"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "08d083522b1340ea2ab58a66b961c911292fb667", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.1.2.tgz", "fileCount": 17, "integrity": "sha512-RI4KfVpjX1qdy5Sq4A1ycCxgTZ2rLLtrTJDBYh3A3DpSSDZ+WP4oBlj/CuD70oXz4wB1WqVjg+lMxH/MPYWb5g==", "signatures": [{"sig": "MEYCIQD7lmo5SJNeTVnzBorsYgkrhGr0qCWiYPvQozde+CyTowIhAMJkQh92K8dB+QdvXsiuihlQmIOQnfv4jashQ6J9ywdv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108648}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.3": {"name": "webpack-cli", "version": "5.1.3", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^10.0.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.2", "@webpack-cli/serve": "^2.0.5", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.1"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "6b6186270efec62394f6fefeebed0872a779f345", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.1.3.tgz", "fileCount": 17, "integrity": "sha512-MTuk7NUMvEHQUSXCpvUrF1q2p0FJS40dPFfqQvG3jTWcgv/8plBNz2Kv2HXZiLGPnfmSAA5uCtCILO1JBmmkfw==", "signatures": [{"sig": "MEUCIQDnmZ0K1w8NQNIS3+J3udwwaLk1IywkAhjNANUHbQFj8wIgEjOPGXwk3HqBIht7MhN7Wuqm0Vg/4v6nFH4G5Z5FC+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108691}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.4": {"name": "webpack-cli", "version": "5.1.4", "dependencies": {"envinfo": "^7.7.3", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^10.0.1", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^5.7.3", "@webpack-cli/info": "^2.0.2", "@webpack-cli/serve": "^2.0.5", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.1"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "5.x.x"}, "bin": {"webpack-cli": "bin/cli.js"}, "dist": {"shasum": "c8e046ba7eaae4911d7e71e2b25b776fcc35759b", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-5.1.4.tgz", "fileCount": 17, "integrity": "sha512-pIDJHIEI9LR0yxHXQ+Qh95k2EvXpWzZ5l+d+jIo+RdSm9MiHfzazIxwwni/p7+x4eJZuvG1AJwgC4TNQ7NRgsg==", "signatures": [{"sig": "MEUCIHJEDoO2VFbp1qybaUj22yAPIZZem6bPmlV2E8Wem4D+AiEA4RVrcsAxus7vkmZMTcqRGWa7fA1Q9dnjHEcxqDT3r3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110451}, "engines": {"node": ">=14.15.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "6.0.0": {"name": "webpack-cli", "version": "6.0.0", "dependencies": {"envinfo": "^7.14.0", "rechoir": "^0.8.0", "colorette": "^2.0.14", "commander": "^12.1.0", "interpret": "^3.1.1", "cross-spawn": "^7.0.3", "import-local": "^3.0.2", "webpack-merge": "^6.0.1", "@webpack-cli/info": "^3.0.0", "@webpack-cli/serve": "^3.0.0", "fastest-levenshtein": "^1.0.12", "@discoveryjs/json-ext": "^0.6.1", "@webpack-cli/configtest": "^3.0.0"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "^5.82.0"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"shasum": "d5df04d0605e9f4adacf1969d88c38df2fc3c3ab", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-6.0.0.tgz", "fileCount": 16, "integrity": "sha512-4MxiCcVjpl5h88mdrzz+ZQRHiT0JmwImP6Ss3xz0LkPYFR61qxuVx7/IPnwhUyRvXen4v/aLXlJONYmfhgVG7A==", "signatures": [{"sig": "MEUCIHMTlQAHaONSL5EXT7kxDQ1/ymRHvv+gsV5beQGsEgChAiEA69HXOv40gWp6xwgL/FU9jVli8pCiIZZA95FF7XmgpAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109562}, "engines": {"node": ">=18.12.0"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "6.0.1": {"name": "webpack-cli", "version": "6.0.1", "dependencies": {"@discoveryjs/json-ext": "^0.6.1", "@webpack-cli/configtest": "^3.0.1", "@webpack-cli/info": "^3.0.1", "@webpack-cli/serve": "^3.0.1", "colorette": "^2.0.14", "commander": "^12.1.0", "cross-spawn": "^7.0.3", "envinfo": "^7.14.0", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^3.1.1", "rechoir": "^0.8.0", "webpack-merge": "^6.0.1"}, "devDependencies": {"@types/envinfo": "^7.8.1"}, "peerDependencies": {"webpack": "^5.82.0"}, "bin": {"webpack-cli": "./bin/cli.js"}, "dist": {"integrity": "sha512-MfwFQ6SfwinsUVi0rNJm7rHZ31GyTcpVE5pgVA3hwFRb7COD4TzjUUwhGWKfO50+xdc2MQPuEBBJoqIMGt3JDw==", "shasum": "a1ce25da5ba077151afd73adfa12e208e5089207", "tarball": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-6.0.1.tgz", "fileCount": 16, "unpackedSize": 109562, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9et4eq46phEKcqjmZZFrvfcqY2u1nO9ftkiwuuQY7OwIhAO/hmRCk5lvo8M+yhSvNI0QJ1yRhqwAQjP3xmpJvX+nr"}]}, "engines": {"node": ">=18.12.0"}, "peerDependenciesMeta": {"webpack-bundle-analyzer": {"optional": true}, "webpack-dev-server": {"optional": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}}, "modified": "2024-12-20T14:12:09.437Z"}