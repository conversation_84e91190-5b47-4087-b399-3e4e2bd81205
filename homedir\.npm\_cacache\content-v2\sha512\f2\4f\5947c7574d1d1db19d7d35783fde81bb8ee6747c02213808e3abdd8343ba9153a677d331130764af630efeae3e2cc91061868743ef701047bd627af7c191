{"_id": "binary-extensions", "_rev": "43-1d0fadcd4df1605a922964eceea67eba", "name": "binary-extensions", "dist-tags": {"latest": "3.1.0"}, "versions": {"1.0.0": {"name": "binary-extensions", "version": "1.0.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "2f4124ba6a3fc598eeb0206f89aa365aca6a0a50", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.0.0.tgz", "integrity": "sha512-0LNl2TPFpa+nbB/cGPgvYzBT1tZjBN6vXW0KSt9FbqGwaI0G3wd3kPvnQS3K1LjhgHzSUN1MCRQdADPjzHAPxw==", "signatures": [{"sig": "MEYCIQCPdNLfscFoRXxwN+WzzlSsa5V3PFpTbCeKbVCUts2+VAIhAIE9+oQCUGqL/WjslXfpuA6Y3aXFKUAxl/Ppf+3nIA7O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "binary-extensions.json"], "_shasum": "2f4124ba6a3fc598eeb0206f89aa365aca6a0a50", "engines": {"node": ">=0.10.0"}, "gitHead": "79100b9a8305177ba3d5908a396eaf5f9f0c087e", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "1.4.28", "description": "List of binary file extensions", "directories": {}, "devDependencies": {"ava": "0.0.4"}}, "1.0.1": {"name": "binary-extensions", "version": "1.0.1", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "ae59c1ee647de4e10525a272fc8b8e7ab30fa6c7", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.0.1.tgz", "integrity": "sha512-GuOZkWMa+WdmfCDQD5xNB5a1yEKHIr4bZveq+4G+ucxfZ1aBX8B+khkg5Tia7dSiYBh5REyIQtRmge/8oEMH+g==", "signatures": [{"sig": "MEQCIEYv1lcO/9P6SM81VI+AfSnXPBsUEYtdJ+F55Q76qi92AiAvGDRzSFqY7Dj52ISXM25Jk6GmJdAmaJkukTGxCbxNWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "binary-extensions.json"], "_shasum": "ae59c1ee647de4e10525a272fc8b8e7ab30fa6c7", "engines": {"node": ">=0.10.0"}, "gitHead": "d0890f88de71888c42e9c7f13a6d814973c454fb", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "1.4.28", "description": "List of binary file extensions", "directories": {}, "devDependencies": {"ava": "0.0.4"}}, "1.0.2": {"name": "binary-extensions", "version": "1.0.2", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "af27a3a2fe4297610af9b2ad63f140a2a613df90", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.0.2.tgz", "integrity": "sha512-5U/VGPPNVF5AV0MYgqQiqSudl2QVjoTS1NAf9yI5LWoBb/7oNxFtckqMtindeVX1MLQW0w0B3i+m1LyZJ8CraA==", "signatures": [{"sig": "MEYCIQDkFBI18H6Ne2UQivR0gUmjdc6/zssOIw+hL/Hc1QbFrwIhAPYzA+44y4rwD9YdjLbBsTeWoSqibIrG7CurLVU9gNrQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "binary-extensions.json"], "_shasum": "af27a3a2fe4297610af9b2ad63f140a2a613df90", "engines": {"node": ">=0.10.0"}, "gitHead": "7cc15fad3d8c07459674ae7c69914c191e91190a", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "1.4.28", "description": "List of binary file extensions", "directories": {}, "devDependencies": {"ava": "0.0.4"}}, "1.1.0": {"name": "binary-extensions", "version": "1.1.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "74f42755e8d719b30440dac309fc02972fe23863", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.1.0.tgz", "integrity": "sha512-XErwf0y3LP5lsvg7djUd+5G1iH4CJgPDVgwOFJ7qOeCwghlX7swSroVtQusMgXK+zOLKzoBHemUGfl/L+9+2RA==", "signatures": [{"sig": "MEUCIQC9G6P14HfT9MfGtauRloVqjZf3KWWi+qcshyvIPaH0XQIgcs5kMDIRDrqP6dJHkW3qIMZ8RFzIIXOfefa6i7RundU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "74f42755e8d719b30440dac309fc02972fe23863", "engines": {"node": ">=0.10.0"}, "gitHead": "ecc4b8b122db160b39472d491c7db86d5f9dca84", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "1.4.28", "description": "List of binary file extensions", "directories": {}, "devDependencies": {"ava": "0.0.4"}}, "1.1.1": {"name": "binary-extensions", "version": "1.1.1", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "68935364a1489a3c62a741352e2f28c2ef2b62d5", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.1.1.tgz", "integrity": "sha512-Qr8IFyKJ1LCVd3NAe3Juv++iEmJ5XAlCYL9C3VNNfMHwEpfFHQgsi+T5WxpZq3zcDciFGmGMujtDtMfmwUjFVQ==", "signatures": [{"sig": "MEYCIQCQneoXHvRkwxOYVxVs+HrTg7MX/hYVV3RkAeML5ZwF4QIhAMaWNj14jyXSA1sRsmFQaQ0sD3XMfKMLen2RaeC1IgJ9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "68935364a1489a3c62a741352e2f28c2ef2b62d5", "engines": {"node": ">=0.10.0"}, "gitHead": "e2b71deb7bc9ac6f5e46efa654a7033f0bf4dc27", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "2.5.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"ava": "0.0.4"}}, "1.2.0": {"name": "binary-extensions", "version": "1.2.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "ae679b33e90cfd73baf244d03467c055a2902fd1", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.2.0.tgz", "integrity": "sha512-gKz8JDEaf1BF3IQyuJTZBuE7XX25udTBKCDBi8GjuBITdft5s6OjAdDgYo/hGRwo1mD8/Na5dqL92yGUWfwemg==", "signatures": [{"sig": "MEYCIQCBkUrCHtD1vbFdEWC+NRezyfN2h6LvDxjm6/NB7xr2vwIhALvjKpQ1ORCZu2wdypE1wkKVHTlioyO2f2Wq6zjUy+Qv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "ae679b33e90cfd73baf244d03467c055a2902fd1", "engines": {"node": ">=0.10.0"}, "gitHead": "3a4431eb9f751b1dfe76ede9156c8b8a216fa84b", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "2.5.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"ava": "0.0.4"}}, "1.3.0": {"name": "binary-extensions", "version": "1.3.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "8c0fd7fa1bd27cfe1e6f2a1d38721647c80b2029", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.3.0.tgz", "integrity": "sha512-KZqOBqS8szBothF45hUgiIPmHsUpwaTPwSFmb7dki3vkXDaUlHx+3hcNooE4mazduL9r5272z8RdiTZUZPlC9g==", "signatures": [{"sig": "MEMCIHf0LDxHwen2aE0fe86F7/65WPnWVDBTlAwYkXpZOcXqAh8B++L+ggD0X+SAeUcfkTUgK5Lb/RsQtURA574DLCEE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "8c0fd7fa1bd27cfe1e6f2a1d38721647c80b2029", "engines": {"node": ">=0.10.0"}, "gitHead": "6eb14380b5f89f92d70305124abc388bc0a55af0", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "2.5.1", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"ava": "0.0.4"}}, "1.3.1": {"name": "binary-extensions", "version": "1.3.1", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "32dd9ed2a7c69acec56f77f6cd80df043f78777a", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.3.1.tgz", "integrity": "sha512-AgTiCTJxJfefwr5bKYrMb7HHXx9c+ouWP7uxu2vemNye6rwLctZRRVegYThsv6JV1O5bcmN6LbIa7l5VOggVIQ==", "signatures": [{"sig": "MEYCIQDeTyHk5uIhzFXktJ9GyvZA5tQ98qLMnwTsHrgq6t0dowIhALxxQz70VmeOtyCC592pQGvqHClwxgyRt3o9RN0nAvL+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "32dd9ed2a7c69acec56f77f6cd80df043f78777a", "engines": {"node": ">=0.10.0"}, "gitHead": "38f603a7822c488436c32c67f9f5f505aca09cd3", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "2.9.1", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "0.12.3", "devDependencies": {"ava": "0.0.4"}}, "1.4.0": {"name": "binary-extensions", "version": "1.4.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "d733ccb628986d7b326d88656e0ddbd3aac351b7", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.4.0.tgz", "integrity": "sha512-bb60G6d5cPHXSHqyDg7QyOsp+qwoxFlscyFqKAWUJad7oxHeFpkpxY+c0Def825+9WtsR8qC/mFI93exoSFIVg==", "signatures": [{"sig": "MEUCIQDZEZNmO1nGFV/p4ocFtUiRFS+TXs8iCTHGDHgmDN+dVwIgI/6JnB0w9lhVVv2qrUn7z9rVqfiYpO4/rOBWvLT5Ovc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "d733ccb628986d7b326d88656e0ddbd3aac351b7", "engines": {"node": ">=0.10.0"}, "gitHead": "8c3a781127ff15ee516c8bedda97807e4dddf1ff", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/binary-extensions", "type": "git"}, "_npmVersion": "2.14.7", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"ava": "0.0.4"}}, "1.4.1": {"name": "binary-extensions", "version": "1.4.1", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "be94c78b44d9f9c336d0de3088545d5efa8511a8", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.4.1.tgz", "integrity": "sha512-J9uFULXTn+TDbs2gksf2i/h/DiQZBdrcUYwQ2Pw1r6wKC//PQi9/h/QCYhb5JgjUoPfzujUev3cbipcCEKPsYg==", "signatures": [{"sig": "MEYCIQCDtVGdRYZSxSLe86GGEijCMWiquKouSJ0zt+KZdT++YQIhAJsNNpGf7+PxgcZ0uMY0zj6ZYl0USSgJsQSMbvZkQkzy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "be94c78b44d9f9c336d0de3088545d5efa8511a8", "engines": {"node": ">=0.10.0"}, "gitHead": "151b647b070a838be56acbd04d6e81d4a0b71815", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "2.15.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.4.2", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.4.1.tgz_1464152617488_0.08830209239386022", "host": "packages-16-east.internal.npmjs.com"}}, "1.5.0": {"name": "binary-extensions", "version": "1.5.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "e6e2057f2cdfb17ad406349c86b71ef8069a25f5", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.5.0.tgz", "integrity": "sha512-QGUyP8K8qwHZgycXf76bm0u8WNZq/PNX7GVu+sjQK3rTGrOEoW8Wv3odmug5VQqR9vdNc0WXp51sgC7l9WY1mw==", "signatures": [{"sig": "MEUCIQDz1rRzLeIJlDqcX8uXZO/eFNVM115A2/d6tkNeB90dYgIgbk7OVcz8ZMV0U2Jd7SADVatObJvQaK1nlMCxmCUAF7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "e6e2057f2cdfb17ad406349c86b71ef8069a25f5", "engines": {"node": ">=0.10.0"}, "gitHead": "639f0353f0ce0b59a94013a1d4caa02622bacbe9", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.4.5", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.5.0.tgz_1467202210211_0.12345921993255615", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.0": {"name": "binary-extensions", "version": "1.6.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.6.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "aa2184cbc434d29862c66a69bf81cc0a3383ee79", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.6.0.tgz", "integrity": "sha512-/1hnP+L3fsyyNvQAFzF3eCp43RFjcPLC5lv+E9AX7tmsCi92g3d66HlfZsltqJvPAN7e9F99idYoBBJaD7yjEQ==", "signatures": [{"sig": "MEUCIQC82UJRxDB105+QxAD6jHf/PAq7l/YnvbvsprjEL6fs1QIgRci9CmUheUCIflQKb+GBaoqtQUnHORi3FaULSyTiX5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "aa2184cbc434d29862c66a69bf81cc0a3383ee79", "engines": {"node": ">=0.10.0"}, "gitHead": "9c6368c4a6bc0f185e9abe791751fc0c8faef774", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "6.4.0", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.6.0.tgz_1472800510782_0.5894664500374347", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.0": {"name": "binary-extensions", "version": "1.7.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.7.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "6c1610db163abfb34edfe42fa423343a1e01185d", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.7.0.tgz", "integrity": "sha512-l0bW08/6sBgvKvuJclslViuVckdibHv8vgPJtEJJEqnuuJfOW0Fiddv4IScujvWitEjG0z3r12mjJkbOshNfXw==", "signatures": [{"sig": "MEUCIQDiCzU0zlVy7HP0LI9rZ1blufYMN4UKFBvfI+GTjNkroAIgMdk3LmygnswBmTY1d375K6bJnOnss75In/DcgZjKGbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "6c1610db163abfb34edfe42fa423343a1e01185d", "engines": {"node": ">=0.10.0"}, "gitHead": "6bab08ef2d6e6f1780eaba7607d347dae51768fa", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.5.0", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.7.0.tgz_1475458290589_0.34751846990548074", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.0": {"name": "binary-extensions", "version": "1.8.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.8.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "48ec8d16df4377eae5fa5884682480af4d95c774", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.8.0.tgz", "integrity": "sha512-3WXXKEv/uJx27dQVWm5nZnXAU3FFymNhLPcI9j8G7i0QuyJy+f4ocGHSQs+pae+3FOO51DmDYqgFBhno3MTaiQ==", "signatures": [{"sig": "MEYCIQCvnD1RopFg27CmD2FuQTdwnZl9SOlrnZ/U+AEms18XnwIhANqhLTsKMIEFpfEGymv1NkiXRMcsmv8M5JRZZ+nCLu+H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "48ec8d16df4377eae5fa5884682480af4d95c774", "engines": {"node": ">=0.10.0"}, "gitHead": "7fbb033c001a17b5e41aa321f0c2398dbab5fc91", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "4.0.2", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.8.0.tgz_1480785169667_0.7267967818770558", "host": "packages-12-west.internal.npmjs.com"}}, "1.9.0": {"name": "binary-extensions", "version": "1.9.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.9.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "66506c16ce6f4d6928a5b3cd6a33ca41e941e37b", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.9.0.tgz", "integrity": "sha512-LS4dfMdNA5z1xPtMw6DwxxbdAo6Fa0Hceg39UhwUnVUnyo4dqs/4yT5RDD//rlLnQpdwIocP1Hq05fV3o6w2qQ==", "signatures": [{"sig": "MEYCIQCopiPb5BpkR+rFqECYvf3+Ms+X9S1iEpuu/IIrHUCyJwIhAKo/Z2rTNEMnQv191SPR14hUKPw1Qqzl6l9MBiQcMkIj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "66506c16ce6f4d6928a5b3cd6a33ca41e941e37b", "engines": {"node": ">=0.10.0"}, "gitHead": "bbc9908995011f0c2ed76fb7b729e4e6142a2980", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.8.3", "devDependencies": {"ava": "0.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.9.0.tgz_1500760448369_0.8683658370282501", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "binary-extensions", "version": "1.10.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.10.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "9aeb9a6c5e88638aad171e167f5900abe24835d0", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.10.0.tgz", "integrity": "sha512-9qcKglN0jJGYb5PyGkrS6Dw6RuIjWNxn0h8PWaQPGcocINsoFxkIjpmosHXmLjKWAlk5HSaksQTBgtqr0SRnCQ==", "signatures": [{"sig": "MEYCIQCYTXSi2bF9+H+jLxklFk0ZR2JDWhN1fCRLyJ64tLt4ewIhAJ83JNC5MiyOfdXV3TqBFpEiVqwLg3u3oC/I3u5zMHdc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "9aeb9a6c5e88638aad171e167f5900abe24835d0", "engines": {"node": ">=0.10.0"}, "gitHead": "2daf7c36320166a095c145d73a01d9155c1b40b6", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.8.3", "devDependencies": {"ava": "0.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.10.0.tgz_1502712661636_0.25032695080153644", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "binary-extensions", "version": "1.11.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.11.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "46aa1751fb6a2f93ee5e689bb1087d4b14c6c205", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.11.0.tgz", "integrity": "sha512-9XXbu17rAPSov41Lvd1NwrwA3Wvh2WRpxJptRjVTw55R+ZIQ9QrzuEPMMPhxXry2GTpmmUXwd8rSvp50H+7uQA==", "signatures": [{"sig": "MEYCIQCiOALrLNZxbbQMVgEy4hhlG7c6stjcdLsgSGuxkDjhlAIhANtFssCiuw2IpBJgwd66xuHEKvLZMl4klMgD2KDyn17a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "binary-extensions.json", "_from": ".", "files": ["binary-extensions.json"], "_shasum": "46aa1751fb6a2f93ee5e689bb1087d4b14c6c205", "engines": {"node": ">=0.10.0"}, "gitHead": "a2370df9507ac5c618c32334ea259370b34ad3be", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "4.8.4", "devDependencies": {"ava": "0.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions-1.11.0.tgz_1511001002966_0.05597343691624701", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "binary-extensions", "version": "1.12.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.12.0", "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "c2d780f53d45bba8317a8902d4ceeaf3a6385b14", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.12.0.tgz", "fileCount": 4, "integrity": "sha512-DYWGk01lDcxeS/K9IHPGWfT8PsJmbXRtRd2Sx72Tnb8pcYZQFF1oSDb8hJtS1vhp212q1Rzi5dUf9+nq0o9UIg==", "signatures": [{"sig": "MEYCIQDwiBKkzIiWa/Mz/iCGALeENarHuhhRUMVbgyZUEWT5xgIhAJAFcPe5Yu/NwI7K6Slo2nfhqFHB4jp5iebXMAPcIHcA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboIqmCRA9TVsSAnZWagAAHyIP/ik6gA5AMyqr5V2aGhwZ\ntH/aPFBaVwHOHhQGhxeQ2JYo20XIxy/2ECr0bWAxstEjEY08Abqzd3kXItaI\nnvEvKO0X27Dm0jFgVqKY19p8R7oXqu7UkUEAQRE+mfO1D8QFgP2orcIIkcxF\nva+L76BMjDFTkYQ2r5wpoZ+saklZw5yUh7JUbaoXF0k5hWleT4k58AF7VcY8\nPcrUtWrIyIgzKCE7IH3UYjk2HObA7COKNbLDinB2RhBC62vOUxFl7h+n9R8p\n7A2uR6Ymu/Nk9CpplT8Klzr9q+uvqhef0l2SxxC6SA9dxEgJ7qZ5BNrZFfbv\n8utm+7e8o1sSTw33cn7TCuma4oLQzy3qtNkpiWd0K8e/yXvpvyeED/tEbVJR\nrbW09GVRZF/DH+C3PTzcuOU5TJvNVKYtmpnZrJWd/Z6t2tFRt+WpiXsntifd\ncHtM7DOKeVyPcX4L5h0nCvairXmhYNE1mzsVaaHRqATXIrB9jFfEHx0YTVXT\nm9o6EJb+Tg8JNZ1JzhdJoFrTsshwX1lqf/86stySpcjYIKlU2JRKCCxWv5wv\nZX3CC//gyQQnYtA2ngfDR43hw4DVz/5RUMqhpa76hrx0qpZ9OPq5DMkWU09a\nXrjuWyIDS9ZxJgflmq1iAKiLY75Hd7MYWWz3FOoMbTdbj/iF4SlOQ/fI44lr\nugR5\r\n=Vyf9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "binary-extensions.json", "engines": {"node": ">=0.10.0"}, "gitHead": "15014048f519a3aff2ea70670f6a50a2287650b1", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "devDependencies": {"ava": "0.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_1.12.0_1537247910313_0.46846812658774106", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "binary-extensions", "version": "1.13.0", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.13.0", "maintainers": [{"name": "art<PERSON><PERSON>r", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "9523e001306a32444b907423f1de2164222f6ab1", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.0.tgz", "fileCount": 4, "integrity": "sha512-EgmjVLMn22z7eGGv3kcnHwSnJXmFHjISTY9E/S5lIcTD3Oxw05QTcBLNkJFzcb3cNueUdF/IN4U+d78V0zO8Hw==", "signatures": [{"sig": "MEUCIDE+U0p+ZGRCS4OMUrKUshQf/Ff7o7gpW0ZEiUddUWXZAiEAj6xcCKGtIkGHSdT01B2w9/jTlvh7H1bRxKKLLQUMMuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVrx6CRA9TVsSAnZWagAAWHUP/R9tfhIklelAkZ0Q4kvR\noHYXMRQ/9uOAptLhfs1ozM12riM0poI2UFlnBAm9RtVtSyOUky4FkXOH8keV\n4B5KIYJQuzz2m75QcKeEbDQ5pI7hlGO68ovpYsnx+sSCrr/si5zvC9FL21j/\n0ZcSEYokBMaZPqdThoKSerJigWzWMtZiMgawLJoTkqalv8ndbObnDbLOpol+\nAxNk7S3AL/1p8D4ffbA2rDlnOIZ5Qxi4isjINjD6Cywhk4c4jPOM8wc1dVFy\nCKbsvfvX5ZjPRk7fPRlFfLwv14yF26YOc4pvle3fFGYkCBrsW4u5tv8VzM/z\nhueHyjiKY6MqpftBUqmEXRbLQbRTZix7r0LLuSUHtlMil2VLE4a4Zc8of/B2\nWy4N2HvUsih8rkYb4Np/M8irH7gTvN+J3Kd3En9EXIhvPewFVk0OOz5rRVF/\ngg0U4kxXPA2sR6HbU1new3nZzCvdpl9MZL2M80JjqMYaSXxRCY2j24IQ/uKV\nDKAbZhVB2/NJBewFNzYOJozI6WatJ5EkBjPHSZZnyMUlXGQNxJwHXvdaTslK\ni7NwqhTfwTFyVzqF+7NLIA9cHayijs7B+NTkiplnGzqp7INqJHpPf7EDmbRL\nxGg8B9DQa86dPKYjw1JyYq6o8RdQwgy3Xz2n++w39rs5khiQ76v3NwI1R6yP\nm/pm\r\n=eYou\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "binary-extensions.json", "engines": {"node": ">=0.10.0"}, "gitHead": "81c838d6913dc9a0884a4d909d0c9f7b5dc20366", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "10.15.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "0.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_1.13.0_1549188218124_0.8110681433774438", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "binary-extensions", "version": "1.13.1", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@1.13.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "598afe54755b2868a5330d2aff9d4ebb53209b65", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz", "fileCount": 4, "integrity": "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==", "signatures": [{"sig": "MEYCIQDnj+DUwyTgRYv+HKIQ6sGiij0GhR5STOCnf+T9AsRVLQIhAN45XPrjZYRONSOWTUal0ZF68PyE/yfnzv8NU2LFNcLk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcm2wmCRA9TVsSAnZWagAA7WIP/R14BayWpIFc+ewAO6pq\n64RKg1xqeT/U/T7O1QB+d/PQv7PE7pImO9XG3F4dzyg6CHa02gHv90apqWC6\n9O5IezUuqEvOIQnSAH7UmKKokg5/SjiD1HlG04QaY679hFp1v1RL6+lW1Ckh\nZNSS3XzNCu00AcxvhgGHrXVj3g95Lrp+9XnzOYO+MH+dkgM+7XCQwXI6StCJ\nA/05RPQrBWmepSXZVDUgI7jtRKhbsn/Vnuoxes61/yOuzpjAha5fxohuIorm\nu+wn/qWudrwB4X+aPj30nXpy0pSNkSBJWc1i929Cf/cnrfSM1RtWaGncUrI0\n03dJmgMu88bOuondboQS9fMqGFPJmiJP4VvuQvKI5Pl/0O7mRZThBHKNDVlf\nCFFG4Sp5Z9OPw+JvNHEavQhK7IEcBdQCq0bGylvdujzTAnV9AjZeBoUOEht5\nPi6zG/9Zs35Uwkjov1pA5GhXSexGf7yB/3hCkUhSyhw1DAmx+zs0jwcCVTMI\nQriYXTh/WbK7OMPpQkEy+kUew//pK3R/Pg6pA7C6A6SMOp0bHh5oF2Jj8xDx\n4PpZ8pqrGUYxgPk2pFkl4WZQQQtuUV8StfFHj+CRwHPKApM73VHarcrV/5Wg\nJNG85avkZXjOPjLXIEp8r0EZIG7UsKwmXNsQp1rXXVhvZaxbIgAsVzjWfj50\n7/np\r\n=PqBz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "binary-extensions.json", "engines": {"node": ">=0.10.0"}, "gitHead": "187f5ab83698150abdc4ec8020c233ebe7303f0f", "scripts": {"test": "ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"ava": "0.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_1.13.1_1553689637901_0.7239438825810172", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "binary-extensions", "version": "2.0.0", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "23c0df14f6a88077f5f986c0d167ec03c3d5537c", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-Phlt0plgpIIBOGTT/ehfFnbNlfsDEiqmzE2KRXoX1bLIlir4X/MR+zSyBEkL05ffWgnRSf/DXv+WrUAVr93/ow==", "signatures": [{"sig": "MEQCIEjzuv0leMEvE8sQoxfhnzmnMwAjpEGnGNUkKejp7jJpAiA53j5MeLbOuOKtca5wX1NWeV3E17XOwpTxeDggB8IBEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuMblCRA9TVsSAnZWagAATlQP+wSYvlsF5PR2hiifHDau\nX+dQx4OBD1pKyMfsyOA6LnED4W7jqbaECQPfaCi/cxESzaF/5Y4bLycxgRko\nJYa7oKokxjdoUdTMuSzPbi+HJFQeTh91K+SHE06o36VZ8qRG9zdF4TjYHuoV\nI3HcqayEWkSovL5APwMYZlFX6Ix9RmxYNcoWfcxFbygjPVuM5vTLDQ98WrD5\nf9WUfkxN6fHplWvTddROV0IBJV6L8dYfp727T2mAOYpWmuFBzSvL0ijOGcgh\n/5k+Aj8+yLBnLLoxBbAh2ymhYI7/L/KZSrqp1yrQzgZPwvdYeMkYd1ctlIbC\nh1A42DxCb21DvM4QQcIbK5+1LtzM2zdPWIUb6bn+bGXOTzXP+BCayWzvCWcx\nhsfwqHhttqI9ltbnxPtwvOCN07RZWdMtkCP+NpTRcbFJHVekiaCP5j9CHeK3\nef+RIGjm9AYpnapoCnn0isAm9pLh4IVKF4+6D+6wjOIZXKNYkJj7gL3PgMHv\nA0+JUv9/Fq6C4QIy47dIpNhum+LKK0T2oxoUnDuib1Pwvv20EsVlpXr4rtJT\nZ0+/HS5FWrAO0d4bWWM2Xfr71hZOn0/HpGBCOBFYmYQf0dQUCEvN9mk1ihsY\nz/oNZWvzmVrsZlt1NfcFUFDA4pBahTI7Mj/K4pQ/K5+UVG/3k7Yo+g2aR0Vj\n0Wu3\r\n=7h+9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "11e076d96c41369015cc3c338428f7f456d3a92f", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_2.0.0_1555613412740_0.3072676738149025", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "binary-extensions", "version": "2.1.0", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "30fa40c9e7fe07dbc895678cd287024dea241dd9", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-1Yj8h9Q+QDF5FzhMs/c9+6UntbD5MkRfRwac8DoEm9ZfUBZ7tZ55YcGVAzEe4bXsdQHEk+s9S5wsOKVdZrw0tQ==", "signatures": [{"sig": "MEUCIQCRIAiQCTtj+2VasL14PaexvMOF82KGi/6U7aHMThfCLQIgI0WaUksrtL6k4YJTo76XWRzWn2cueAYqrLx5FeIBbjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe90MuCRA9TVsSAnZWagAA4vMQAKJIZ0x0hYZ9DvGCTdlt\n5q6/fXjlovvtL/FWssy/yasBueDf0fWk9IEJzvq9d7rozlI6khacRjiVa8c8\nij86y13/HNubK6ohhsoowxmMv5ncxnKNOP6hOmSHc9Qphv06jIGbZ2wiadnk\n3+OtvWAJXKVtyv0Bq0oPIikxBM1QK2sqhmf3GCcaRIWc+aOUyOdQow2cO+Pm\nP6KE6VwlNUkjL7oZoiHt2BZFa7NSd4DSCYFnkafF7shjWT6chHN7UcH88FBt\nOO49Pmuvop/l8pJDzAE4tjTH2354eZhV4fXDVYkDIUFb+2gNKK1r0X0VTmWa\nZQp+UyXnHdLI7qd7kRIy66UNxrmvZnb+Ek1SRCbNljtMbeHd7Y5pGjMH4/fO\nUSbdNRlql9cXKCMJzokWfn4M96Uc496CEKYZgacDcZD2Go4C6xlqljoLc0Pz\nrZYHbjYgsZ5Grx3rzA94f32IIzrxKTS5bf4MiGCetuzsYG6Du4eTQA6etbQE\nDOccbBTsGue1IOvQi3Uzhl9yq4N9rz+taFCu3dcrJ5M3uV1vmG1HClIIe/8c\nrR7IMyuO7boBwJU64r8ZXMAO5yXKwqzXrzY7M94+lm8DEcav/W4cByUk/tRK\nY4s+HacItX6lFml9AnnKLe4jOucokVQqf1t5vQcRpcEqCEh0GPVQGri6gEYA\nsxJd\r\n=Ll59\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "4c81c9a9f3a4921f35bcedeb3b91e18902c859f7", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "14.4.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_2.1.0_1593262894344_0.6539073910328448", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "binary-extensions", "version": "2.2.0", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@2.2.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "75f502eeaf9ffde42fc98829645be4ea76bd9e2d", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz", "fileCount": 7, "integrity": "sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==", "signatures": [{"sig": "MEYCIQDSP2jzBfVF8beDUgsEiNNLp/lx7Qb8jYG79QVrpxoAkQIhAKU+nll4MNVCxscm8gxvddNizJWpzARJHXCBud6PcbgK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+z7vCRA9TVsSAnZWagAAKtwP/jQgq5vbEi1C/yTTppV3\ncgtyHHHEr4tRRhXakaWsi+OFI0slr4GnVjVAAS+N8GvrJi0wgz/pJQBfDLSi\nSalRBZZrcmszQapZfzshxmmsE81R8tCGGYG1ltsaMhGJYSEGKnVq8Df28+i8\nIfU3zqWD5s0QV4AtjeH0M9shlTKQFpPvrRGGaAvUqIGEduJbmcjAzujIvUzm\nSNfOewkK6lxh85oNZsDIhBM8mDQM4BkCYCpwSUehYx9rh2tqiooS9jX6E0Uv\nPBmE/G75lpEYP3pOtytaq09L0q8x7ayKHugCRCkIkOxl2r6nILFvEWXjuPX5\nCYsDzFaJDQ++DY42AiPVAwdRhKv74LsBFaDS98Lf5jPHvIeypvRp+HQILgVA\nPyM5JkMqKLFNctKIIs0SZ/OnlHFSgOt7zuPMVt26Q1kExCJJgDBSv6uWfvcj\nM6fOaJsmiS3oPBVzVMAdk+i86NLuwCzaWITzv7YLaByN4KRqtaRVsN/ncyep\nrd0SrqfOVCdPH3IfBZQlCs7w3oIXjxiGrY90OlS1fRgYuH8yTzd4WIYyxk3D\nUdnADmSYffRWcpd0i7rUJ1ygLsmPl/ZgyJ4pqt79+ofvw1W2ibX229+Nbhri\nhxC6jHAwEyEYcTpR42EIJ1RsxaL/e07c29VhbYdSVH9aEfHD6+oopiqdebDC\nPOow\r\n=WkTs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "40e44b510d87a63dcf42300bc8fbcb105f45a61c", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "List of binary file extensions", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_2.2.0_1610301166693_0.997078796273527", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "binary-extensions", "version": "2.3.0", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@2.3.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "f6e14a97858d327252200242d4ccfe522c445522", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "fileCount": 7, "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==", "signatures": [{"sig": "MEQCIARO6w2g8OARJpz+EHBsmlcgYx9k6EBEa7yuXwFW0DjrAiAf31ap7Zxf8FIDx9SG4kIdvAqO8aFmd4Rlvv3QEeN2xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5030}, "types": "./index.d.ts", "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "3ad032e195467df417009bdfc847856829a00085", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "List of binary file extensions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_2.3.0_1710439839590_0.04358125891660469", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "binary-extensions", "version": "3.0.0", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "binary-extensions@3.0.0", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "dist": {"shasum": "14ce687f80e3ebab2a2fb78bb8611584c29f12c3", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-X0RfwMgXPEesg6PCXzytQZt9Unh9gtc4SfeTNJvKifUL//Oegcc/Yf31z6hThNZ8dnD3Ir3wkHVN0eWrTvP5ww==", "signatures": [{"sig": "MEQCIDRkt4U47mJJeIFSclk9RqnSDFzW9WiYNEM3+dgz6iiYAiAwsZWtQmUuuhh4pUl/x0MyLQHK+k4z8BEr/zWpESBbKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5084}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18.20"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "2d838768d2e138ea9f617707719676bab1da0af7", "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/binary-extensions.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "List of binary file extensions", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.58.0", "ava": "^6.1.2", "tsd": "^0.31.0"}, "_npmOperationalInternal": {"tmp": "tmp/binary-extensions_3.0.0_1714468533443_0.6266219269300546", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "binary-extensions", "version": "3.1.0", "description": "List of binary file extensions", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/binary-extensions.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18.20"}, "scripts": {"//test": "xo && ava && tsd", "test": "ava && tsd"}, "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "devDependencies": {"ava": "^6.1.2", "tsd": "^0.31.0", "xo": "^0.58.0"}, "_id": "binary-extensions@3.1.0", "gitHead": "4c749ce789957f40335d6c5862e3e37880aaeda5", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "_nodeVersion": "23.6.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Jvvd9hy1w+xUad8+ckQsWA/V1AoyubOvqn0aygjMOVM4BfIaRav1NFS3LsTSDaV4n4FtcCtQXvzep1E6MboqwQ==", "shasum": "be31cd3aa5c7e3dc42c501e57d4fff87d665e17e", "tarball": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-3.1.0.tgz", "fileCount": 6, "unpackedSize": 5092, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEOKWbu57mgBm8E7iY0T0lWQjNTX7wR6idzezZ5VfjixAiAwsyZIEHUevjDFhKOOVfqi+FL+kvdgZdXM4xxmREFlaQ=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/binary-extensions_3.1.0_1746343778580_0.4140915183426708"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-24T09:04:29.927Z", "modified": "2025-05-04T07:29:38.916Z", "1.0.0": "2015-01-24T09:04:29.927Z", "1.0.1": "2015-01-26T10:56:06.676Z", "1.0.2": "2015-02-06T00:27:38.427Z", "1.1.0": "2015-02-06T18:36:49.046Z", "1.1.1": "2015-02-15T04:26:26.313Z", "1.2.0": "2015-02-16T07:23:29.790Z", "1.3.0": "2015-02-16T21:10:14.299Z", "1.3.1": "2015-05-17T23:39:55.173Z", "1.4.0": "2015-11-18T17:00:05.622Z", "1.4.1": "2016-05-25T05:03:40.065Z", "1.5.0": "2016-06-29T12:10:11.507Z", "1.6.0": "2016-09-02T07:15:14.131Z", "1.7.0": "2016-10-03T01:31:30.814Z", "1.8.0": "2016-12-03T17:12:51.759Z", "1.9.0": "2017-07-22T21:54:09.279Z", "1.10.0": "2017-08-14T12:11:02.540Z", "1.11.0": "2017-11-18T10:30:03.052Z", "1.12.0": "2018-09-18T05:18:30.443Z", "1.13.0": "2019-02-03T10:03:38.268Z", "1.13.1": "2019-03-27T12:27:18.018Z", "2.0.0": "2019-04-18T18:50:13.054Z", "2.1.0": "2020-06-27T13:01:34.492Z", "2.2.0": "2021-01-10T17:52:46.844Z", "2.3.0": "2024-03-14T18:10:39.723Z", "3.0.0": "2024-04-30T09:15:33.617Z", "3.1.0": "2025-05-04T07:29:38.752Z"}, "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/binary-extensions.git"}, "description": "List of binary file extensions", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# binary-extensions\n\n> List of binary file extensions\n\nThe list is just a [JSON file](binary-extensions.json) and can be used anywhere.\n\n## Install\n\n```sh\nnpm install binary-extensions\n```\n\n## Usage\n\n```js\nimport binaryExtensions from 'binary-extensions';\n\nconsole.log(binaryExtensions);\n//=> ['3ds', '3g2', …]\n```\n\n## Related\n\n- [is-binary-path](https://github.com/sindresorhus/is-binary-path) - Check if a file path is a binary file\n- [text-extensions](https://github.com/sindresorhus/text-extensions) - List of text file extensions\n", "readmeFilename": "readme.md", "users": {"faraoman": true, "flumpus-dev": true}}