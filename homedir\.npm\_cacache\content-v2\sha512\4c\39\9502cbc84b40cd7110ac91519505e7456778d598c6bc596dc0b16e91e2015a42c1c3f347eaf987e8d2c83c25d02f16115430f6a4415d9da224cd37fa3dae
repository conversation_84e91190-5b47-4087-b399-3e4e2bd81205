{"_id": "streamsearch", "_rev": "24-6b361c8de7694a0f405f15c3830feeee", "name": "streamsearch", "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.1": {"name": "streamsearch", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./sbmh", "engines": {"node": ">=0.8.0"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/streamsearch.git"}, "readme": "Description\n===========\n\nstreamsearch is a module for [node.js](http://nodejs.org/) that allows searching a stream using the Boyer-Moore-Horspool algorithm.\n\nThis module is based heavily on the Streaming Boyer-Moore-Horspool C++ implementation by <PERSON><PERSON> [here](https://github.com/FooBarWidget/boyer-moore-horspool).\n\n\nRequirements\n============\n\n* [node.js](http://nodejs.org/) -- v0.8.0 or newer\n\n\nInstallation\n============\n\n    npm install streamsearch\n\nExample\n=======\n\n```javascript\n  var StreamSearch = require('streamsearch'),\n      inspect = require('util').inspect;\n\n  var needle = new Buffer([13, 10]), // CRLF\n      s = new StreamSearch(needle),\n      chunks = [\n        new Buffer('foo'),\n        new Buffer(' bar'),\n        new Buffer('\\r'),\n        new Buffer('\\n'),\n        new Buffer('baz, hello\\r'),\n        new Buffer('\\n world.'),\n        new Buffer('\\r\\n Node.JS rules!!\\r\\n\\r\\n')\n      ];\n  s.on('data', function(data, start, end) {\n    console.log('data: ' + inspect(data.toString('ascii', start, end)));\n  });\n  s.on('match', function() {\n    console.log('match!');\n  });\n  for (var i = 0, len = chunks.length; i < len; ++i)\n    s.push(chunks[i]);\n\n  // output:\n  //\n  // data: 'foo'\n  // data: ' bar'\n  // match!\n  // data: 'baz, hello'\n  // match!\n  // data: ' world.'\n  // match!\n  // data: ' Node.JS rules!!'\n  // match!\n  // data: ''\n  // match!\n```\n\n\nAPI\n===\n\nEvents\n------\n\n* **data**(< _Buffer_ >chunk, < _integer_ >start, < _integer_ >end) - Emitted when non-needle data is available. This data is in `chunk` between `start` (inclusive) and `end` (exclusive).\n\n* **match**() - Emitted when the needle has been found in the stream.\n\n\nProperties\n----------\n\n* **maxMatches** - < _integer_ > - The maximum number of matches. This is especially useful when multiple matches exist within a single chunk passed to push(), so that this module knows to stop matching within that single chunk. Defaults to Infinity.\n\n* **matches** - < _integer_ > - The current match count.\n\n\nFunctions\n---------\n\n* **(constructor)**(< _Buffer_ >needle) - Creates and returns a new instance for searching for `needle`.\n\n* **push**(< _Buffer_ >chunk) - _integer_ - Process `chunk`. The return value is the last processed index in `chunk` + 1.\n\n* **reset**() - _(void)_ - Resets internal state. Useful for when you wish to start searching a new/different stream for example.\n", "readmeFilename": "README.md", "_id": "streamsearch@0.0.1", "dist": {"shasum": "5350ebc548378da45f3727aefebac3aec5dcc345", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.0.1.tgz", "integrity": "sha512-l+XPdaQqIZo9By5FZFnK8OskgaU+j7x6Ia5DQOo+407ywlA0Cem1/jFU/sTx68bBn+YTZEpqtC5M2wmbLFFaIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEy3XvyxBkmRq/MN7km3hqKk48oSXo8SRUi/9DvjiqkkAiEAlgOHQaymojRR2IjTaLxnbGmyVErcC/HzTFfde8235yo="}]}, "scripts": {}, "_npmVersion": "1.1.66", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "streamsearch", "version": "0.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./sbmh", "engines": {"node": ">=0.8.0"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/streamsearch.git"}, "readme": "Description\n===========\n\nstreamsearch is a module for [node.js](http://nodejs.org/) that allows searching a stream using the Boyer-Moore-Horspool algorithm.\n\nThis module is based heavily on the Streaming Boyer-Moore-Horspool C++ implementation by <PERSON><PERSON> [here](https://github.com/FooBarWidget/boyer-moore-horspool).\n\n\nRequirements\n============\n\n* [node.js](http://nodejs.org/) -- v0.8.0 or newer\n\n\nInstallation\n============\n\n    npm install streamsearch\n\nExample\n=======\n\n```javascript\n  var StreamSearch = require('streamsearch'),\n      inspect = require('util').inspect;\n\n  var needle = new Buffer([13, 10]), // CRLF\n      s = new StreamSearch(needle),\n      chunks = [\n        new Buffer('foo'),\n        new Buffer(' bar'),\n        new Buffer('\\r'),\n        new Buffer('\\n'),\n        new Buffer('baz, hello\\r'),\n        new Buffer('\\n world.'),\n        new Buffer('\\r\\n Node.JS rules!!\\r\\n\\r\\n')\n      ];\n  s.on('data', function(data, start, end) {\n    console.log('data: ' + inspect(data.toString('ascii', start, end)));\n  });\n  s.on('match', function() {\n    console.log('match!');\n  });\n  for (var i = 0, len = chunks.length; i < len; ++i)\n    s.push(chunks[i]);\n\n  // output:\n  //\n  // data: 'foo'\n  // data: ' bar'\n  // match!\n  // data: 'baz, hello'\n  // match!\n  // data: ' world.'\n  // match!\n  // data: ' Node.JS rules!!'\n  // match!\n  // data: ''\n  // match!\n```\n\n\nAPI\n===\n\nEvents\n------\n\n* **data**(< _Buffer_ >chunk, < _integer_ >start, < _integer_ >end) - Emitted when non-needle data is available. This data is in `chunk` between `start` (inclusive) and `end` (exclusive).\n\n* **match**() - Emitted when the needle has been found in the stream.\n\n\nProperties\n----------\n\n* **maxMatches** - < _integer_ > - The maximum number of matches. Defaults to Infinity.\n\n* **matches** - < _integer_ > - The current match count.\n\n\nFunctions\n---------\n\n* **(constructor)**(< _Buffer_ >needle) - Creates and returns a new instance for searching for `needle`.\n\n* **push**(< _Buffer_ >chunk) - _integer_ - Processes `chunk`. The return value is the last processed index in `chunk` + 1.\n\n* **reset**() - _(void)_ - Resets internal state. Useful for when you wish to start searching a new/different stream for example.\n", "readmeFilename": "README.md", "_id": "streamsearch@0.0.2", "dist": {"shasum": "0d446c92b56bad4aa96d588aeeb9d90e1c4af8be", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.0.2.tgz", "integrity": "sha512-zXdmbZ4zab4qeVytS+zewq6c8Y2Vk6NbUJtfGPQVDbFFMGzER2ObbLU7MK/fHK09aPkKTkZV0EvUDibKU/fG+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsjDTvNRkWad7e5FyOZYz8TI0fMvDKq+iJOQ1WhEuyWQIgVUoILHZGwaS6SMo061/DECfRuvwcWImReTGO688Tvac="}]}, "scripts": {}, "_npmVersion": "1.1.66", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "streamsearch", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./sbmh", "engines": {"node": ">=0.8.0"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/streamsearch.git"}, "readme": "Description\n===========\n\nstreamsearch is a module for [node.js](http://nodejs.org/) that allows searching a stream using the Boyer-Moore-Horspool algorithm.\n\nThis module is based heavily on the Streaming Boyer-Moore-Horspool C++ implementation by <PERSON><PERSON> [here](https://github.com/FooBarWidget/boyer-moore-horspool).\n\n\nRequirements\n============\n\n* [node.js](http://nodejs.org/) -- v0.8.0 or newer\n\n\nInstallation\n============\n\n    npm install streamsearch\n\nExample\n=======\n\n```javascript\n  var StreamSearch = require('streamsearch'),\n      inspect = require('util').inspect;\n\n  var needle = new Buffer([13, 10]), // CRLF\n      s = new StreamSearch(needle),\n      chunks = [\n        new Buffer('foo'),\n        new Buffer(' bar'),\n        new Buffer('\\r'),\n        new Buffer('\\n'),\n        new Buffer('baz, hello\\r'),\n        new Buffer('\\n world.'),\n        new Buffer('\\r\\n Node.JS rules!!\\r\\n\\r\\n')\n      ];\n  s.on('info', function(isMatch, data, start, end) {\n    if (data)\n      console.log('data: ' + inspect(data.toString('ascii', start, end)));\n    if (isMatch)\n      console.log('match!');\n  });\n  for (var i = 0, len = chunks.length; i < len; ++i)\n    s.push(chunks[i]);\n\n  // output:\n  //\n  // data: 'foo'\n  // data: ' bar'\n  // match!\n  // data: 'baz, hello'\n  // match!\n  // data: ' world.'\n  // match!\n  // data: ' Node.JS rules!!'\n  // match!\n  // data: ''\n  // match!\n```\n\n\nAPI\n===\n\nEvents\n------\n\n* **info**(< _boolean_ >isMatch[, < _Buffer_ >chunk, < _integer_ >start, < _integer_ >end]) - A match _may_ or _may not_ have been made. In either case, a preceding `chunk` of data _may_ be available that did not match the needle. Data (if available) is in `chunk` between `start` (inclusive) and `end` (exclusive).\n\n\nProperties\n----------\n\n* **maxMatches** - < _integer_ > - The maximum number of matches. Defaults to Infinity.\n\n* **matches** - < _integer_ > - The current match count.\n\n\nFunctions\n---------\n\n* **(constructor)**(< _mixed_ >needle) - Creates and returns a new instance for searching for a _Buffer_ or _string_ `needle`.\n\n* **push**(< _Buffer_ >chunk) - _integer_ - Processes `chunk`. The return value is the last processed index in `chunk` + 1.\n\n* **reset**() - _(void)_ - Resets internal state. Useful for when you wish to start searching a new/different stream for example.\n", "readmeFilename": "README.md", "_id": "streamsearch@0.1.0", "dist": {"shasum": "2c106c4b8e869f9762bb0c362c5af1e8d3b4da74", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.0.tgz", "integrity": "sha512-fs9+MyAFTYEF3ydWy1OI1VM4GFiqR5M5punqt+n/tg2fuh85X6H4huut9tn9CcPG2dfaWin9hgEMDRGlaISN+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPjiF0cDTKQdcv0PKkWqVtqxiF4fNwaS5qdIjA2f8kPQIgTPxKVtwIrh+I5UMdS+3kDn+/ufZXEIARhoX3MbVJ3Ag="}]}, "_from": "https://github.com/mscdex/streamsearch/tarball/v0.1.0", "_resolved": "https://github.com/mscdex/streamsearch/tarball/v0.1.0", "scripts": {}, "_npmVersion": "1.2.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "streamsearch", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./sbmh", "engines": {"node": ">=0.8.0"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/streamsearch.git"}, "readme": "Description\n===========\n\nstreamsearch is a module for [node.js](http://nodejs.org/) that allows searching a stream using the Boyer-Moore-Horspool algorithm.\n\nThis module is based heavily on the Streaming Boyer-Moore-Horspool C++ implementation by <PERSON><PERSON> [here](https://github.com/FooBarWidget/boyer-moore-horspool).\n\n\nRequirements\n============\n\n* [node.js](http://nodejs.org/) -- v0.8.0 or newer\n\n\nInstallation\n============\n\n    npm install streamsearch\n\nExample\n=======\n\n```javascript\n  var StreamSearch = require('streamsearch'),\n      inspect = require('util').inspect;\n\n  var needle = new Buffer([13, 10]), // CRLF\n      s = new StreamSearch(needle),\n      chunks = [\n        new Buffer('foo'),\n        new Buffer(' bar'),\n        new Buffer('\\r'),\n        new Buffer('\\n'),\n        new Buffer('baz, hello\\r'),\n        new Buffer('\\n world.'),\n        new Buffer('\\r\\n Node.JS rules!!\\r\\n\\r\\n')\n      ];\n  s.on('info', function(isMatch, data, start, end) {\n    if (data)\n      console.log('data: ' + inspect(data.toString('ascii', start, end)));\n    if (isMatch)\n      console.log('match!');\n  });\n  for (var i = 0, len = chunks.length; i < len; ++i)\n    s.push(chunks[i]);\n\n  // output:\n  //\n  // data: 'foo'\n  // data: ' bar'\n  // match!\n  // data: 'baz, hello'\n  // match!\n  // data: ' world.'\n  // match!\n  // data: ' Node.JS rules!!'\n  // match!\n  // data: ''\n  // match!\n```\n\n\nAPI\n===\n\nEvents\n------\n\n* **info**(< _boolean_ >isMatch[, < _Buffer_ >chunk, < _integer_ >start, < _integer_ >end]) - A match _may_ or _may not_ have been made. In either case, a preceding `chunk` of data _may_ be available that did not match the needle. Data (if available) is in `chunk` between `start` (inclusive) and `end` (exclusive).\n\n\nProperties\n----------\n\n* **maxMatches** - < _integer_ > - The maximum number of matches. Defaults to Infinity.\n\n* **matches** - < _integer_ > - The current match count.\n\n\nFunctions\n---------\n\n* **(constructor)**(< _mixed_ >needle) - Creates and returns a new instance for searching for a _Buffer_ or _string_ `needle`.\n\n* **push**(< _Buffer_ >chunk) - _integer_ - Processes `chunk`. The return value is the last processed index in `chunk` + 1.\n\n* **reset**() - _(void)_ - Resets internal state. Useful for when you wish to start searching a new/different stream for example.\n", "readmeFilename": "README.md", "_id": "streamsearch@0.1.1", "dist": {"shasum": "b29160bf407ba701ae9326249cbb3245e46a37c1", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.1.tgz", "integrity": "sha512-yf+nQUyqxWC4FsrLAPNhHWbKqNEzOpHHYkc8jcI4qp1AMlf3nwgXj2RyZFISVZE9aBk0/EC1ovTeR5ijNNu+9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5fGHXkCKwNP2Geg3MBKqf+3mPX4WZzL//oVtctwOXFwIhAOPnk2qOnQRzUaj2A0s7L6o2Mma04ayAgzy1bJOSOLBd"}]}, "_from": "https://github.com/mscdex/streamsearch/tarball/v0.1.1", "_resolved": "https://github.com/mscdex/streamsearch/tarball/v0.1.1", "scripts": {}, "_npmVersion": "1.2.18", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "streamsearch", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./lib/sbmh", "engines": {"node": ">=0.8.0"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/streamsearch.git"}, "readme": "Description\n===========\n\nstreamsearch is a module for [node.js](http://nodejs.org/) that allows searching a stream using the Boyer-Moore-Horspool algorithm.\n\nThis module is based heavily on the Streaming Boyer-Moore-Horspool C++ implementation by <PERSON><PERSON> [here](https://github.com/FooBarWidget/boyer-moore-horspool).\n\n\nRequirements\n============\n\n* [node.js](http://nodejs.org/) -- v0.8.0 or newer\n\n\nInstallation\n============\n\n    npm install streamsearch\n\nExample\n=======\n\n```javascript\n  var StreamSearch = require('streamsearch'),\n      inspect = require('util').inspect;\n\n  var needle = new Buffer([13, 10]), // CRLF\n      s = new StreamSearch(needle),\n      chunks = [\n        new Buffer('foo'),\n        new Buffer(' bar'),\n        new Buffer('\\r'),\n        new Buffer('\\n'),\n        new Buffer('baz, hello\\r'),\n        new Buffer('\\n world.'),\n        new Buffer('\\r\\n Node.JS rules!!\\r\\n\\r\\n')\n      ];\n  s.on('info', function(isMatch, data, start, end) {\n    if (data)\n      console.log('data: ' + inspect(data.toString('ascii', start, end)));\n    if (isMatch)\n      console.log('match!');\n  });\n  for (var i = 0, len = chunks.length; i < len; ++i)\n    s.push(chunks[i]);\n\n  // output:\n  //\n  // data: 'foo'\n  // data: ' bar'\n  // match!\n  // data: 'baz, hello'\n  // match!\n  // data: ' world.'\n  // match!\n  // data: ' Node.JS rules!!'\n  // match!\n  // data: ''\n  // match!\n```\n\n\nAPI\n===\n\nEvents\n------\n\n* **info**(< _boolean_ >isMatch[, < _Buffer_ >chunk, < _integer_ >start, < _integer_ >end]) - A match _may_ or _may not_ have been made. In either case, a preceding `chunk` of data _may_ be available that did not match the needle. Data (if available) is in `chunk` between `start` (inclusive) and `end` (exclusive).\n\n\nProperties\n----------\n\n* **maxMatches** - < _integer_ > - The maximum number of matches. Defaults to Infinity.\n\n* **matches** - < _integer_ > - The current match count.\n\n\nFunctions\n---------\n\n* **(constructor)**(< _mixed_ >needle) - Creates and returns a new instance for searching for a _Buffer_ or _string_ `needle`.\n\n* **push**(< _Buffer_ >chunk) - _integer_ - Processes `chunk`. The return value is the last processed index in `chunk` + 1.\n\n* **reset**() - _(void)_ - Resets internal state. Useful for when you wish to start searching a new/different stream for example.\n", "readmeFilename": "README.md", "_id": "streamsearch@0.1.2", "dist": {"shasum": "808b9d0e56fc273d809ba57338e929919a1a9f1a", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHsFmyhBGzskd9VOZ6EoXTVivWbuhaoWX8rBGoyRUIgAiBG5ySQPIY5u91CI837BlPPmScJjlBV9y7qHXoqSe7DBA=="}]}, "_from": "https://github.com/mscdex/streamsearch/tarball/v0.1.2", "_resolved": "https://github.com/mscdex/streamsearch/tarball/v0.1.2", "scripts": {}, "_npmVersion": "1.2.18", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "streamsearch", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./lib/sbmh.js", "engines": {"node": ">=10.0.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.0.0", "eslint": "^7.0.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test", "lint:fix": "npm run lint -- --fix"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/streamsearch.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/streamsearch/tarball/v1.0.0", "bugs": {"url": "https://github.com/mscdex/streamsearch/issues"}, "homepage": "https://github.com/mscdex/streamsearch#readme", "_id": "streamsearch@1.0.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-irkOuAxxhc6BjGfH09Zy36+LJhDOj3ElPVmHpCeRmZbuHI3AF+HgEt6y6k7STvCByKPa2OHFqtS94EEHu4/j3w==", "shasum": "2efd0aab4bb054b3490d06633e9cff92191e54ec", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.0.0.tgz", "fileCount": 13, "unpackedSize": 13155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrZLfCRA9TVsSAnZWagAAWYUQAINTKvVndWARsfqtgCwk\nvSwrNjJH+LaGNLmyvxBDufOXfttO4/EtPsN8GM6KJ83wCU+Hh4ti6jDX5rS5\nauJUpxto5vgHok9FhOU/G+BsVVmAxwQiFu8gvAOnOl2Gcp2U07o0urh2tzuZ\nmbGE3rzrAkSaVwiAT3dbFySImDddg22rIfLWEQMsHfzlIWQ/T61e9aTfa6F0\neCfaiehEKsFwvSEBewaN7m32PjCxQjnxFNPXM/XE5ctGPt0mkeL4Mc7O4pY0\n7CM1pngbvWq6Sp68btV1aM+2GiswsMkFQDZQUA3A6vk3ZKSc5NqxZC4X0E9K\nLqGN6Vzhn5RGkev3dgL3BER+QG3gM67zw9zOMuJkoka99X2TZHAA9xlNsQxD\n001bJDrY7bm7ufyj7f5anKeVnVzDheIvmVNidybQEY7ftqLnYf1VBowGWfWL\nwvp4+AgDGpE6+GPJkjvNo6YCSlRF0iRGK7lPl4DQ1wildaM55LOc1iZYzgU6\nqu5zIUvUM96vxu9j6h7oYhzx95X0PcV6w+qUtNM+94KHXDXDtY91Nr77YasQ\niu/ppxlGwex9ZDxVLecnvfWcp7TuZCsCJr03QK5kERjugDBtcIKjlYyPcFSO\nmo39FK61iovcqieAKrSKcI1yYMWWNU/IjktuDuU05webBxl3IQkArD204oc8\nDZZa\r\n=WgoY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYbeDFIS7RJN/tJ00VGrrC5tNn3p1mPUFtDcuNBgRyVQIhAKGub/mJSpyM5xofYx45KbkEPbqd+83ekaLSMOUEgOcP"}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/streamsearch_1.0.0_1638765279733_0.060673163816512776"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "streamsearch", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Streaming Boyer-Moore-<PERSON><PERSON><PERSON> searching for node.js", "main": "./lib/sbmh.js", "engines": {"node": ">=10.0.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test", "lint:fix": "npm run lint -- --fix"}, "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/streamsearch/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/streamsearch.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/streamsearch/tarball/v1.1.0", "bugs": {"url": "https://github.com/mscdex/streamsearch/issues"}, "homepage": "https://github.com/mscdex/streamsearch#readme", "_id": "streamsearch@1.1.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==", "shasum": "404dd1e2247ca94af554e841a8ef0eaa238da764", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "fileCount": 13, "unpackedSize": 16575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhv4QUCRA9TVsSAnZWagAA1PMP/AruwrATdOkXpas8SDG1\nvPulhrOsNeawr/n0NUQ/TOudoPzYV/3mnfHEgpdWp9+6ecAY/soacK835Pc4\n//ekLVhw5nX8p+PFJBTkMEG/NU6+jJimNFw4V6jceONSml+39kb6yxSO6Pz2\nhaMa1sZVno6VH8IkhHPuGMKQZEUqvBvgXS+JgZjZgb4Mad7DQGcerfrPDdvy\nLjIG31H/tMojS+ZGVU5e7GEDSjw008PsJGDXRjToLcJYET5JzyA0i/edOypa\n2d8R91kM0PHsi5gJsut0H6MDaCxhVwKN5nIDyOfWpCyRPKbKPJat3ox8LSCk\nXDz+vCtCzbKgRNlw/Iw2tPTnO7XOZ1WUaR+whXhBluelqulaOSatlSWfymXv\nr0/KVUHFvywpBOt6cG9Vh/30BL9tHbTk8YTP4733J3VXyNtHHSXk19u82udU\nA/EkVxUZohXQJnNpSIS71I12DyVkKLR/2wCRpUIJodwJiBU/kqDhk7qyd90I\nWtxamM6+9wQT7Z56at0LOeAJaJ2WnPJjLCIPGIWu+MrEsq4O0K6J3Gd5464i\nUAyc5hbWicgQBgNv+dbg5ZWxwtu7wL912el0CIdODVd2iu9fBaWLBNJwRV6H\nOMfyyVYIP8UznO9eLWi7WNI6X5EvnoGOHRmTZBsT54MQm/p2Bup2iENwwzHA\nuv4W\r\n=Obyb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDM3wtpkzcQNoBpgTJd003vDizv+QVozM+wKJSQcVDLywIhAJducrsvaQLGyvGETu2sFBjWLhs1D+3tRV5RGt0zOjZA"}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/streamsearch_1.1.0_1639941140459_0.2920136163263838"}, "_hasShrinkwrap": false}}, "readme": "Description\n===========\n\nstreamsearch is a module for [node.js](http://nodejs.org/) that allows searching a stream using the Boyer-Moore-Ho<PERSON>pool algorithm.\n\nThis module is based heavily on the Streaming Boyer-Moore-Horspool C++ implementation by <PERSON><PERSON> [here](https://github.com/FooBarWidget/boyer-moore-horspool).\n\n\nRequirements\n============\n\n* [node.js](http://nodejs.org/) -- v10.0.0 or newer\n\n\nInstallation\n============\n\n    npm install streamsearch\n\nExample\n=======\n\n```js\n  const { inspect } = require('util');\n\n  const StreamSearch = require('streamsearch');\n\n  const needle = Buffer.from('\\r\\n');\n  const ss = new StreamSearch(needle, (isMatch, data, start, end) => {\n    if (data)\n      console.log('data: ' + inspect(data.toString('latin1', start, end)));\n    if (isMatch)\n      console.log('match!');\n  });\n\n  const chunks = [\n    'foo',\n    ' bar',\n    '\\r',\n    '\\n',\n    'baz, hello\\r',\n    '\\n world.',\n    '\\r\\n Node.JS rules!!\\r\\n\\r\\n',\n  ];\n  for (const chunk of chunks)\n    ss.push(Buffer.from(chunk));\n\n  // output:\n  //\n  // data: 'foo'\n  // data: ' bar'\n  // match!\n  // data: 'baz, hello'\n  // match!\n  // data: ' world.'\n  // match!\n  // data: ' Node.JS rules!!'\n  // match!\n  // data: ''\n  // match!\n```\n\n\nAPI\n===\n\nProperties\n----------\n\n* **maxMatches** - < _integer_ > - The maximum number of matches. Defaults to `Infinity`.\n\n* **matches** - < _integer_ > - The current match count.\n\n\nFunctions\n---------\n\n* **(constructor)**(< _mixed_ >needle, < _function_ >callback) - Creates and returns a new instance for searching for a _Buffer_ or _string_ `needle`. `callback` is called any time there is non-matching data and/or there is a needle match. `callback` will be called with the following arguments:\n\n  1. `isMatch` - _boolean_ - Indicates whether a match has been found\n\n  2. `data` - _mixed_ - If set, this contains data that did not match the needle.\n\n  3. `start` - _integer_ - The index in `data` where the non-matching data begins (inclusive).\n\n  4. `end` - _integer_ - The index in `data` where the non-matching data ends (exclusive).\n\n  5. `isSafeData` - _boolean_ - Indicates if it is safe to store a reference to `data` (e.g. as-is or via `data.slice()`) or not, as in some cases `data` may point to a Buffer whose contents change over time.\n\n* **destroy**() - _(void)_ - Emits any last remaining unmatched data that may still be buffered and then resets internal state.\n\n* **push**(< _Buffer_ >chunk) - _integer_ - Processes `chunk`, searching for a match. The return value is the last processed index in `chunk` + 1.\n\n* **reset**() - _(void)_ - Resets internal state. Useful for when you wish to start searching a new/different stream for example.\n\n", "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "time": {"modified": "2022-06-27T00:55:17.099Z", "created": "2012-12-11T17:37:58.473Z", "0.0.1": "2012-12-11T17:37:59.897Z", "0.0.2": "2012-12-11T21:04:44.829Z", "0.1.0": "2013-03-04T23:21:08.956Z", "0.1.1": "2013-04-14T01:01:21.348Z", "0.1.2": "2013-04-14T17:49:09.358Z", "1.0.0": "2021-12-06T04:34:39.895Z", "1.1.0": "2021-12-19T19:12:20.627Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/mscdex/streamsearch.git"}, "users": {"sessionbean": true, "nachbar90": true, "mojaray2k": true, "reinoudk": true}, "homepage": "https://github.com/mscdex/streamsearch#readme", "keywords": ["stream", "horspool", "boyer-moore-horspool", "boyer-moore", "search"], "bugs": {"url": "https://github.com/mscdex/streamsearch/issues"}, "readmeFilename": "README.md"}