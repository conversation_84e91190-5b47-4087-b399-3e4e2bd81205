{"_id": "accepts", "_rev": "103-542215bb290b82282180ab55f6766ab4", "name": "accepts", "dist-tags": {"latest": "1.3.8", "next": "2.0.0"}, "versions": {"1.0.0": {"name": "accepts", "version": "1.0.0", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "3604c765586c3b9cf7877b6937cdbd4587f947dc", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.0.tgz", "integrity": "sha512-2GdyQ5yRXA5MZi1gmU/XUFQTo7FtXsR2Jt90pl1pQftbsRdXQn69Mycn7xNGxVVmu1xdzZKe/GyMHnDlcV7ojg==", "signatures": [{"sig": "MEYCIQDpHxJ/p6O5unkh8APAsNJtvQAmZpE5sLoA2+TzOJrRHQIhAMUdBt2vXH2KmzZ9bu2SmynYbnuB5Ec5sFiPK6PBp2kX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/accepts.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime": "~1.2.11", "negotiator": "~0.3.0"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.1": {"name": "accepts", "version": "1.0.1", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "c1e06d613e6246ba874678d6d9b92389b7ce310c", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.1.tgz", "integrity": "sha512-EsBDjbbGp44Tq6UGW6YE4cgD2xF1MIIyK7Vn+sElHW3EiSgqoIetsTiwFytb9YxODl6YVc4IIY28zeQ0JKGCLQ==", "signatures": [{"sig": "MEQCIFvkT3ndGz8l2fvAbNsrndK5yYrBSmprMuL2vBbirX4EAiBR6ryS2Bgv4vzxsu/PzmBsnFKPIrkzsKGKlPUZTPJQIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "make test"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/accepts.git", "type": "git"}, "_npmVersion": "1.3.23", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime": "~1.2.11", "negotiator": "~0.4.0"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.2": {"name": "accepts", "version": "1.0.2", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "96266ace1b4c03f9637428f3acafe891959f3883", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.2.tgz", "integrity": "sha512-Xh6Ep4MHKfHswT/oJFBjT0mOQJwncjYV1FoRa10IsdqYWqwqKoevgMHI4wHgqbGd9Wt4VXdDoFOyW6X8wXKKug==", "signatures": [{"sig": "MEUCIHO2VcH7nZCXCP/WijFeyIzCtmdPBV3lKoo2N26eVnhcAiEAsEFfV2qk12irq/9yE5iJpzAQlXeKduKSmWm9pUUC7Yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter dot", "test-travis": "mocha --require should --reporter spec"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/accepts.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime": "~1.2.11", "negotiator": "0.4.5"}, "devDependencies": {"mocha": "*", "should": "*"}}, "1.0.3": {"name": "accepts", "version": "1.0.3", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "92b1db0d4f3db47b0530df6e15ae97db514dc2f8", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.3.tgz", "integrity": "sha512-cZqKqO3VXtuIZ5vQLVc8M6JDFVTZoVwZrlmTCA1nH9EoN5v6ZWWStKvd1A5RWpduRVXD55px3t75TvS7JdLfHA==", "signatures": [{"sig": "MEUCIGjxY2unab761ZccWiXG0BAokgcXTE2Ut2cT4f42Tt1hAiEA01HzqDuMwV+qDNwUmgSerIWyOaGgFuqJm2T2eD8d1QA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "92b1db0d4f3db47b0530df6e15ae97db514dc2f8", "engines": {"node": ">= 0.8.0"}, "gitHead": "b17538c271b31cdad481c29d1623d76561a69d81", "scripts": {"test": "mocha --require should --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/accepts", "type": "git"}, "_npmVersion": "1.4.14", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime": "~1.2.11", "negotiator": "0.4.6"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.10"}}, "1.0.4": {"name": "accepts", "version": "1.0.4", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "a01739f55fbd67b26056ac5bc26537166a0707ca", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.4.tgz", "integrity": "sha512-khC9HBgKUtW8mzOMe3XbQ+DKUgRjy3+wAPoc4J6gLk+wrFbRov/SBISz1In5AXLdg8gmd50fiEt90L3RD6lSpg==", "signatures": [{"sig": "MEUCIH7cpK3rWg4kW9WpMQIkEosIbvoOKdhtqyg2KpysStTWAiEAoozdPheyuuubFtnX+EY2W5sX5/mNQqjq5vNUqV+PYz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "a01739f55fbd67b26056ac5bc26537166a0707ca", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/expressjs/accepts", "type": "git"}, "_npmVersion": "1.4.9", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~1.0.0", "negotiator": "0.4.6"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.10"}}, "1.0.5": {"name": "accepts", "version": "1.0.5", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "3a484f1870a8264cfa4266cf6fb0197d6bc86bff", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.5.tgz", "integrity": "sha512-EZlLQHtVz0iGCt6z8JvvoAbm0zOq9OGX0q0NA6nYxucLi5CLpdCls0wbSv+osGt9AHpmJcxD5FX+tUuph778tA==", "signatures": [{"sig": "MEUCIQCQKt6JiaEzn8W34r2KPFBXqgB6jkz/tyrGwVqPNJxztwIgV6R8rLVarvs3tZQoBHtQJcdbOvzjilzoEsY6ULxNrxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/accepts", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~1.0.0", "negotiator": "0.4.6"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.10"}}, "1.0.6": {"name": "accepts", "version": "1.0.6", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "8cbbf84772d70211110d9b00b1208aae01f15724", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.6.tgz", "integrity": "sha512-kjEb4r1hzsTFusUHL/BV+8oKmEeGL9qMMA4vk0G69pTM7ufuiyBIXlnUnZYrXR5kTSY07E0BXVmPHoV4B+ycOQ==", "signatures": [{"sig": "MEUCIEbCJ3/z23KFIoc93PLGgSb9MjaN4A/q6Jk350qGNVKNAiEA9HOEh2gN8ivQRZ0Ys4KIHlhgW9K/1PjsnNtwoK6TJI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "8cbbf84772d70211110d9b00b1208aae01f15724", "engines": {"node": ">= 0.8.0"}, "gitHead": "77b5766d9233a208870b7cd4e92347c2b9cafc4c", "scripts": {"test": "mocha --require should --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/expressjs/accepts", "type": "git"}, "_npmVersion": "1.4.16", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~1.0.0", "negotiator": "0.4.7"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.11"}}, "1.0.7": {"name": "accepts", "version": "1.0.7", "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.0.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/accepts", "bugs": {"url": "https://github.com/expressjs/accepts/issues"}, "dist": {"shasum": "5b501fb4f0704309964ccdb048172541208dab1a", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.0.7.tgz", "integrity": "sha512-iq8ew2zitUlNcUca0wye3fYwQ6sSPItDo38oC0R+XA5KTzeXRN+GF7NjOXs3dVItj4J+gQVdpq4/qbnMb1hMHw==", "signatures": [{"sig": "MEQCIGOCO8Ccmpz1HbZ07QhJ3Ui//R5rNX+ij48i26geq4EmAiB5G8KSMsZtdQpBy2S6O8hAfPVfEmQ75nr96Tkwcp8G2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --require should --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/accepts", "type": "git"}, "_npmVersion": "1.4.3", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~1.0.0", "negotiator": "0.4.7"}, "devDependencies": {"mocha": "*", "should": "*", "istanbul": "0.2.11"}}, "1.1.0": {"name": "accepts", "version": "1.1.0", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.1.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "43ba6d946374c80f91823eaec6bb43dc4955500b", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.1.0.tgz", "integrity": "sha512-48hb/+P+hPgRJ4EGkS2o6+ah0jGDStsIsOi80lvipvf0cbONH6S3/aELXHPIMoqQIElg+JKJGfLtjdhYyG97Ag==", "signatures": [{"sig": "MEYCIQChsMojJm3yT+6UjCz8kJzIMKEpeB2Q/7HHOp3l68+4gAIhAN01wBqPi6wVb3RuGc8xzfXaEHmGUdpMX4bi8hxhGvEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "43ba6d946374c80f91823eaec6bb43dc4955500b", "engines": {"node": ">= 0.8.0"}, "gitHead": "564a01f1d1b4864365029a55773b765fb9e7756a", "scripts": {"test": "mocha --require should --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --require should --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --require should --reporter spec test/"}, "_npmUser": {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.21", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.0", "negotiator": "0.4.7"}, "devDependencies": {"mocha": "1", "should": "4", "istanbul": "~0.3.0"}}, "1.1.1": {"name": "accepts", "version": "1.1.1", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.1.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "3b40bf6abc3fe3bc004534f4672ae1efd0063a96", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.1.1.tgz", "integrity": "sha512-rIqpy4jiQKsiG8oedtzz1+TB11+WBJrFQzai/6RXq59d4Tm4Dx78t0BpVKUp+Mn5OxL7FAtVRsz0FzwZAcGLLA==", "signatures": [{"sig": "MEYCIQCEs7Hu3xOP6xMLfxQtND/duPZ4gV1o9YbIQQy9Yfny0QIhANvYne8V+gmQOk3g539kGvnxLQW6YZ3Sr+py0+2yMwpP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "3b40bf6abc3fe3bc004534f4672ae1efd0063a96", "engines": {"node": ">= 0.8.0"}, "gitHead": "57e2960cfc6e8863c258613aa7e5947169e1d824", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.21", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.2", "negotiator": "0.4.8"}, "devDependencies": {"mocha": "1", "istanbul": "~0.3.0"}}, "1.1.2": {"name": "accepts", "version": "1.1.2", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.1.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "8469a0a0a215b50cb0d156d351662f8978b00876", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.1.2.tgz", "integrity": "sha512-MV7jqwWN6bSz3wA5q+Oo/3sm2GnfvMmiU1EatcF7jH+zcNdu6altVfHhJYFidAmD/QZkc2gCHRGiVGhjjCbmjA==", "signatures": [{"sig": "MEYCIQDkvguU7rPh+b/idV0SpV41he3QJtxbBnrjglEXxFZp6wIhAOaVzU7Alzc5YkBtZTVQFHpor+eXc1MPYzI3CTMw1Lko", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "8469a0a0a215b50cb0d156d351662f8978b00876", "engines": {"node": ">= 0.8.0"}, "gitHead": "026a08f23ee1aaa1bb0fb874fab49fbc00b6d898", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.21", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.2", "negotiator": "0.4.9"}, "devDependencies": {"mocha": "1", "istanbul": "~0.3.0"}}, "1.1.3": {"name": "accepts", "version": "1.1.3", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.1.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "14d99f8ee3ea69f8709d4bd17ffe153bef0f6c6d", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.1.3.tgz", "integrity": "sha512-xlEUjOIHeH5L6GOzoOtaZiK6HipR6FOA8paZiwYCQ0tfqjLaUcZWsykej81BKyYsvrvs4Z3lpw3YDgPbF1+I/w==", "signatures": [{"sig": "MEYCIQCoavGY1i+v6AyHpHz6gNT4jnYtcuZufogfdn4+TeFN8AIhANeuWOBsMiF/r1jBtbx5Z693FEuEC1c5aHAXrZuvu3Gj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "14d99f8ee3ea69f8709d4bd17ffe153bef0f6c6d", "engines": {"node": ">= 0.8"}, "gitHead": "8c3267ffe54e657b00dcd019ce6fdf8b342377b6", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.21", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.3", "negotiator": "0.4.9"}, "devDependencies": {"mocha": "~2.0.1", "istanbul": "~0.3.0"}}, "1.1.4": {"name": "accepts", "version": "1.1.4", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.1.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "d71c96f7d41d0feda2c38cd14e8a27c04158df4a", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.1.4.tgz", "integrity": "sha512-8EKM6XlFgqSpDcxkT9yxCT8nDSWEVBD0UjgUWMCWh5kH9VU+ar2MhmDDYGxohXujPU8PPz88ukpkvfXFVWygHw==", "signatures": [{"sig": "MEQCICN8OU5gyI5386HGosspB7d4NlLObonX+Kx+jAd4jWfTAiAHCfbVpebakiumULLrRMq67AvVFAsEL/dpcMXvgU1lhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d71c96f7d41d0feda2c38cd14e8a27c04158df4a", "engines": {"node": ">= 0.8"}, "gitHead": "df66414d80f096627b28f137127fce0a851d7900", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.21", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.4", "negotiator": "0.4.9"}, "devDependencies": {"mocha": "~2.0.1", "istanbul": "~0.3.4"}}, "1.2.0": {"name": "accepts", "version": "1.2.0", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.2.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "6dabb991bfa82ad0011f6e970b99151d6e109966", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.0.tgz", "integrity": "sha512-YigC9ZJDWwFfQsICgvx31RmqPGvWcnNKvQ3BaqmOiXoATPEv8HcS/G9aP2DoH/F0b/Nsq33JfKoKVeA4DoXOKQ==", "signatures": [{"sig": "MEUCIQCHzBUlkARuaHLJ8op7qtiPbUytfmW6u7sK3rzPjA5RegIgJtv93sxONXUrmfBtbyJulZ3FnFo8ec2B7Tdj+EimInE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "6dabb991bfa82ad0011f6e970b99151d6e109966", "engines": {"node": ">= 0.8"}, "gitHead": "2e889c93fc7f7907fb89468bafe23d352f9cdc9a", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.4", "negotiator": "0.5.0"}, "devDependencies": {"mocha": "~2.0.1", "istanbul": "~0.3.4"}}, "1.2.1": {"name": "accepts", "version": "1.2.1", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.2.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "07f17ad3e9d8f0cc6097931c310079d6c1eac704", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.1.tgz", "integrity": "sha512-vc+5DLobMzMdyqBkb6pj+vANMdvhqSSlOmkgw9bGYHounBskX3cy1iNaEqN4y57Ko7VACrPJCIB3aAPikfQumA==", "signatures": [{"sig": "MEQCICWJeMwcqVumIMDw7owJ26SQ73GVarOpoVJptr/WdYADAiBMH+9fU+I/xeeGyfC0HQOk8B7E+cFi50CJxON4nVYsRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "07f17ad3e9d8f0cc6097931c310079d6c1eac704", "engines": {"node": ">= 0.8"}, "gitHead": "b517171bbd972803dbbe6c80050a9c795288265f", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.5", "negotiator": "0.5.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5"}}, "1.2.2": {"name": "accepts", "version": "1.2.2", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.2.2", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "9bc29b9b39f33a351e76a76058184ebc8ed7783f", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.2.tgz", "integrity": "sha512-Ws+h5jNgCMZHdqtbBxu7WBwR3dkah4RnBRJmrNAOE0aMESzjIysQyh9OIBANw3DZI2wu0zduVPrFzA0Q9srRFA==", "signatures": [{"sig": "MEYCIQDahbBUY4pxMAFE9xW484EHk2CUQV3SDhTU8M6njRxK7wIhAKCxreiCv3dQ4cXKfUYBIgUCEuY5qyaLv8JYCVGghabE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "9bc29b9b39f33a351e76a76058184ebc8ed7783f", "engines": {"node": ">= 0.8"}, "gitHead": "08c807538789b4908ddb5f6ad58550b2d0c3c261", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.7", "negotiator": "0.5.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5"}}, "1.2.3": {"name": "accepts", "version": "1.2.3", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.2.3", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "2cb8b306cce2aa70e73ab39cc750061526c0778f", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.3.tgz", "integrity": "sha512-PjsXv+l8+TbXc19FVlj508gTJ/VYLSPii4OnbiB9E1q9GrezF3RPXXzSAM6MaD2hnRVwiCfV/lwinMJmeqmyjg==", "signatures": [{"sig": "MEYCIQDpEVAvnLGy224WZPXNS8bpvA4aiC94p2fSq6BNfa4cbgIhAJtPLpmrisyi0HZKXcOEmwQg3Z0648dNE8WHQKhGHzNl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "2cb8b306cce2aa70e73ab39cc750061526c0778f", "engines": {"node": ">= 0.8"}, "gitHead": "b4f616ff54790683759280244384cbead0742095", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.8", "negotiator": "0.5.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5"}}, "1.2.4": {"name": "accepts", "version": "1.2.4", "keywords": ["content", "negotiation", "accept", "accepts"], "author": {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "accepts@1.2.4", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "f4e6c66f4faf69c76bd7a63a1ffc5bd2dacfb2ac", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.4.tgz", "integrity": "sha512-1eX3ZyUHw9KtpyIK45DiKsmB/zBm/hK/M/zJ4/GSQk4OGmRyeHzdGjlLWywBb45/wgBEBYOquDKe3bgp3saaZA==", "signatures": [{"sig": "MEUCIQCbitTZ6u0zenguKFXskLSCZKb8cAQjmkSQCjuoKqCvRwIgPR/j3Fbzjs74ncCKGfcBgc/FRfockoVe8NHnnzqhbkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "f4e6c66f4faf69c76bd7a63a1ffc5bd2dacfb2ac", "engines": {"node": ">= 0.6"}, "gitHead": "dfa143a31879bf5fb4934bbefc5741504a1cc15f", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.9", "negotiator": "0.5.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.5"}}, "1.2.5": {"name": "accepts", "version": "1.2.5", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.5", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "bb07dc52c141ae562611a836ff433bcec8871ce9", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.5.tgz", "integrity": "sha512-fjPWjM5vy2baCynmcFC18CFoGOzbPVyVhWttJKP9ldgFReRGiTcGwMqZ2oAwIfX8nnF8YIBus+3aNhp5SYTjyw==", "signatures": [{"sig": "MEYCIQDqdj6OPmmzt92GeSm4+5gbdhbaA8GamXbjZYs6h1X1cgIhAJfylLc7D1fhY5vfwCoEYdU+33WWzYsdbgTUDYzKIFRx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "bb07dc52c141ae562611a836ff433bcec8871ce9", "engines": {"node": ">= 0.6"}, "gitHead": "e74f846e885aa70fceba1af6ce96e3952e6782c1", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.10", "negotiator": "0.5.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.7"}}, "1.2.6": {"name": "accepts", "version": "1.2.6", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.6", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "8f6c694267f0dc2f722d8b1752f56434e58be469", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.6.tgz", "integrity": "sha512-+xw3lqHp/H0j0qosNkA65ESEuQ9C3oPgUnqFdFQ/K3VlGds4lv8OqgAUHn+ZlypCg9qnuS1i6Xm2ytIqYPtCWg==", "signatures": [{"sig": "MEUCIGq0vjQSGuslm0zxdmfOshR9D0YIuddVtD+lB4VEsVETAiEAgL6EdAl6+GNUX8YMybz+rXWpcrRNpE5scUkd/bPJZUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "8f6c694267f0dc2f722d8b1752f56434e58be469", "engines": {"node": ">= 0.6"}, "gitHead": "bebfc8d4f557d68662184751af0f9c64bea6da01", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.11", "negotiator": "0.5.2"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "1.2.7": {"name": "accepts", "version": "1.2.7", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.7", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "efea24e36e0b5b93d001a7598ac441c32ef56003", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.7.tgz", "integrity": "sha512-zyPcXezXGJvFKShBjN4RWAO6/4I1vPr9aAvKIVJRXV0rxEQjBuVxgubcnRZFrhh/QVwA1cIn678AjeCsiViMhg==", "signatures": [{"sig": "MEYCIQCEdgX1FUViu3GuB2w4Mx8bN/Yk0DVBG7LVgKcBZaPmLAIhAIor85G5qnyaHTUutH/EQk6hKlKs5l8mJqErm3TCV89n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "efea24e36e0b5b93d001a7598ac441c32ef56003", "engines": {"node": ">= 0.6"}, "gitHead": "067cd4c96d517cf3299f0d9c67733e752d0257e1", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.0.11", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.9"}}, "1.2.8": {"name": "accepts", "version": "1.2.8", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.8", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "6ae87f81ceb551258163531988b435142cf927e2", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.8.tgz", "integrity": "sha512-H64alrVbHd2wkp0Fd2Wzb1hKztKW8tyU/tjW22gDtWx06xp535fjCmVOyELp+/1V9SyCi7cregdGiPZPtEO5Ag==", "signatures": [{"sig": "MEUCIAIXVOUydHaaloUnOQwizG89utY3Uepepb7qWdgoPKfuAiEA9lvT1DuudENnJylQIzgJRieCO1nokFR+WyVzjPv2/cI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "6ae87f81ceb551258163531988b435142cf927e2", "engines": {"node": ">= 0.6"}, "gitHead": "0d60f3a79aec8f682ebf86dd871c8e69c72b5170", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.0", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.14"}}, "1.2.9": {"name": "accepts", "version": "1.2.9", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.9", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "76e9631d05e3ff192a34afb9389f7b3953ded001", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.9.tgz", "integrity": "sha512-Ykq/9L0l1/m8kzVhYkjhtKgsYDvnPLyOujT4LcRII2AKbeem61g3ozjYIAMg6P3P8C3ZSCH34KM9/K6WqoD83A==", "signatures": [{"sig": "MEUCIDJVZFRmNyOnDlg45sMYFyMSDilDJWbbSXfBitbQI1+8AiEA4hSaCjzayxrHRJ0noLMI0IaR9PO/cwqOgDwwFOuczpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "76e9631d05e3ff192a34afb9389f7b3953ded001", "engines": {"node": ">= 0.6"}, "gitHead": "66496bc5b99bfc99aa0fa96d160f8b5eec7f9b5d", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.1", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.14"}}, "1.2.10": {"name": "accepts", "version": "1.2.10", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.10", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "f825f151c0960914881625be845d04940691ef69", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.10.tgz", "integrity": "sha512-6k0cDFwsWgbfV/5mUdrcNM3cmTt77HympLdNPExO6rPsM308kLImO6OeuI9FTGq4o8sJKbX+oWMyOvnnyvdIUQ==", "signatures": [{"sig": "MEYCIQD5Ofd6bZ0yGAAmokY2jxeELihxc5QRATO/32gVo4oeVgIhAPTtomBTfZpPhMAB0TIoHt1RRXFMMsecCnRf+J8JJ/36", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "f825f151c0960914881625be845d04940691ef69", "engines": {"node": ">= 0.6"}, "gitHead": "9afac08d5ab40aff6af007121672adc83d85ebf1", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.2", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "1.2.11": {"name": "accepts", "version": "1.2.11", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.11", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "d341c6e3b420489632f0f4f8d2ad4fd9ddf374e0", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.11.tgz", "integrity": "sha512-LBbBhXOL8DwseLjyV6wBfV1jO91KgB0hyXmb4ic98Dpc31D0Yd1g6khnALphS+2fdD1Az1Zfhqc/fmcEd47+Kg==", "signatures": [{"sig": "MEUCIQCAsvjC7gRICH0N1ijLN+k7cFibMNSUNZXjLuVjVGW3VAIgbwTKrLKnIuooqRvog/Y3d4uT1HQTVAvYYreui9MIzRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d341c6e3b420489632f0f4f8d2ad4fd9ddf374e0", "engines": {"node": ">= 0.6"}, "gitHead": "c9c8adea7bb8395089ead858fc059a38e99ac3bc", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.3", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "1.2.12": {"name": "accepts", "version": "1.2.12", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.12", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "7e6d880f473b5c48d46e3e35f71ea7c3b68514c3", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.12.tgz", "integrity": "sha512-RAscEZl8nsB5PDR1vNIqC3DoLD/bCIbAA9iDfaimpccq/zFSLCC/MHLKHbBJPOd1O17SiHPfdRAk9yk/y9KygQ==", "signatures": [{"sig": "MEUCIQDWvQCdY6/y8uJdgsEgE2J8BbpClVkP+1BMuQYW+A4ukgIgStoQCVtUPfDIn06zU7nveDfH+558CaLl6/3dOKfT2sE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "7e6d880f473b5c48d46e3e35f71ea7c3b68514c3", "engines": {"node": ">= 0.6"}, "gitHead": "f01900aa33b1089575bd29caea851a8a241df07c", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.4", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.17"}}, "1.2.13": {"name": "accepts", "version": "1.2.13", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.2.13", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "e5f1f3928c6d95fd96558c36ec3d9d0de4a6ecea", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.2.13.tgz", "integrity": "sha512-R190A3EzrS4huFOVZajhXCYZt5p5yrkaQOB4nsWzfth0cYaDcSN5J86l58FJ1dt7igp37fB/QhnuFkGAJmr+eg==", "signatures": [{"sig": "MEQCIDiYR97BZXIZfXY4mkXSF66sa8KThzmoCI+bIO1Esl7RAiAOKuVkgm36fKskkgbVYSOqsyzkbOyK42me2Y0dYY3Sbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e5f1f3928c6d95fd96558c36ec3d9d0de4a6ecea", "engines": {"node": ">= 0.6"}, "gitHead": "b7e15ecb25dacc0b2133ed0553d64f8a79537e01", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.6", "negotiator": "0.5.3"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.19"}}, "1.3.0": {"name": "accepts", "version": "1.3.0", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.0", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "2341420f16d0b2d538a5898416ab0faa28912622", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.0.tgz", "integrity": "sha512-nXoBsIgIkisLARFQeZuMhHqbauuFmPg4EUHchMsVR0UrwB21NteA15uDF9jVG4/DM7T8eeeV44hA05MYFaokzg==", "signatures": [{"sig": "MEUCIGUlVOpEVu0yDBjoBmkjsGy1310EhouOp3z0FXa66LqRAiEA2BhD8NMufze3R3oln1kUQOFqK3OLEoo4XxYcWTruUQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "2341420f16d0b2d538a5898416ab0faa28912622", "engines": {"node": ">= 0.6"}, "gitHead": "f4a54dfbc147808b2ed89428a52db858be0838d5", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.7", "negotiator": "0.6.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.3.21"}}, "1.3.1": {"name": "accepts", "version": "1.3.1", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.1", "maintainers": [{"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "federomero", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "dc295faf85024e05b04f5a6faf5eec1d1fd077e5", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.1.tgz", "integrity": "sha512-Kd7nxxRGDQplvxvAgwNf6yKd538zkQZm0qpaP/aAhSLjHVbgAblWLkMiqah7HsuTXmRb9Vuj2Nh8vnTF3LS2Iw==", "signatures": [{"sig": "MEYCIQDW0bxGK1E8C2XYxLQqHJBf2XBSGX5SVLTMhLdIBaG04QIhAPTvfuBmDVeFGRGIEVRfp9u/47FV0mXDIvdEfMss34wX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "dc295faf85024e05b04f5a6faf5eec1d1fd077e5", "engines": {"node": ">= 0.6"}, "gitHead": "6551051596cfcbd7aaaf9f02af8f487ce83cbf00", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.9", "negotiator": "0.6.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.2"}}, "1.3.2": {"name": "accepts", "version": "1.3.2", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "9bfd7ddc497fdc1dad73a97b3f7cdc133929fac1", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.2.tgz", "integrity": "sha512-Sykpc36hXNhyuNBbtd9S5OHAG4Zh/ljPLr56RMWooz48H2JPm9+viqww+L49yda4c9o2U+HVj2xWIB++d/lhog==", "signatures": [{"sig": "MEQCIA8yHKGVyNXvnTxi8Z8RmH66BVg8n6JL4FBnkDw+QzyJAiBrhcXyENvJQyS6MtZci6amXkufbwCrWarpV7mI14PK+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "9bfd7ddc497fdc1dad73a97b3f7cdc133929fac1", "engines": {"node": ">= 0.6"}, "gitHead": "abc38f70222e9c3b73d2f74f2259fbcc3fdd09ca", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/accepts", "type": "git"}, "_npmVersion": "1.4.28", "description": "Higher-level content negotiation", "directories": {}, "dependencies": {"mime-types": "~2.1.10", "negotiator": "0.6.0"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/accepts-1.3.2.tgz_1457497267109_0.11459392495453358", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.3": {"name": "accepts", "version": "1.3.3", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "c3ca7434938648c3e0d9c1e328dd68b622c284ca", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz", "integrity": "sha512-AOPopplFOUlmUugwiZUCDpOwmqvSgdCyE8iJVLWI4NcB7qfMKQN34dn5xYtlUU03XGG5egRWW4NW5gIxpa5hEA==", "signatures": [{"sig": "MEYCIQDapjQ8MKCLO9q0kQVMT62GQPpr6yCG/FtI4OSz6fwhEAIhALN7c71gq6JrYnSqd300XTP/1uwCjxT/IEMvOSgGm0Qh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "c3ca7434938648c3e0d9c1e328dd68b622c284ca", "engines": {"node": ">= 0.6"}, "gitHead": "3e925b1e65ed7da2798849683d49814680dfa426", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"mime-types": "~2.1.11", "negotiator": "0.6.1"}, "devDependencies": {"mocha": "~1.21.5", "istanbul": "0.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/accepts-1.3.3.tgz_1462251932032_0.7092335098423064", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.4": {"name": "accepts", "version": "1.3.4", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "86246758c7dd6d21a6474ff084a4740ec05eb21f", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.4.tgz", "integrity": "sha512-2wzn4SzQLKl9o0pwlXIRG3YBZj8aGUGfcJTIjhrOWusCZ6c5mjjvUnpf+07pjdheuuOtRIKYAd/3CX973Qv/vw==", "signatures": [{"sig": "MEQCIAMoGvWgtnKhPUqQ03A8SJs8TjO/YaXCssTVe/f3ZEyGAiA5n60VleLyTq0gIO8qZP1Td5hzrMDHJrq6IgjfWn1zzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "86246758c7dd6d21a6474ff084a4740ec05eb21f", "engines": {"node": ">= 0.6"}, "gitHead": "71ea430741d6eb5484b6c67c95924540a98186a5", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"mime-types": "~2.1.16", "negotiator": "0.6.1"}, "devDependencies": {"mocha": "~1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/accepts-1.3.4.tgz_1503455053008_0.43370609171688557", "host": "s3://npm-registry-packages"}}, "1.3.5": {"name": "accepts", "version": "1.3.5", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "eb777df6011723a3b14e8a72c0805c8e86746bd2", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.5.tgz", "fileCount": 5, "integrity": "sha512-pt4oTticGUEOZCvli0I7ekmpXopaX+Xvb/TQRaTKnvZNIH9Srs0VWi2NGBfsRscAgwtIEtxW5JOB9sI0oN3cjw==", "signatures": [{"sig": "MEUCIChLBO9ih7mK0ZxC8zchrqQebLTpUYQd1yOqa32BJbKXAiEAx+FWZfqYeCyzDrnqWdu8Iutg19Zy9iNiM0q7YS1IR40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16555}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "eb777df6011723a3b14e8a72c0805c8e86746bd2", "engines": {"node": ">= 0.6"}, "gitHead": "c38d0e968cdc1526f7cc7a718977ee76655c84f5", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"mime-types": "~2.1.18", "negotiator": "0.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~1.21.5", "eslint": "4.18.1", "istanbul": "0.4.5", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.9.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/accepts_1.3.5_1519869527663_0.6663620712347182", "host": "s3://npm-registry-packages"}}, "1.3.6": {"name": "accepts", "version": "1.3.6", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "27de8682f0833e966dde5c5d7a63ec8523106e4b", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.6.tgz", "fileCount": 5, "integrity": "sha512-QsaoUD2dpVpjENy8JFpQnXP9vyzoZPmAoKrE3S6HtSB7qzSebkJNnmdY4p004FQUSSiHXPueENpoeuUW/7a8Ig==", "signatures": [{"sig": "MEUCIG5r3/zrH9WMKFKGjvPIJA0/07sJOJM8cjjzC3bUzG6PAiEAhNCNxX30pURH385smxBGQSLxgEQ0K143jhvKstJ/x8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxdY4CRA9TVsSAnZWagAAwVsQAKRxXFaYDKxfJF2Uo/Qc\ns5SyR5DD5cZNA+jAvBvbbHPMCN1Unqkyqvy7wqzyvK/4NbISi3zgkUqKBRfy\nt1twSdfeVorBx7RuL+RuzL9xB4xu7puksd7GzV/43gaclLN1/XHYgKfJnQTj\nUsqqhXLDWYo8GDJtKPh+i5wjOBNyLEa1Ygyhl7D/7+SlgK8fqRthR4UqRYg5\nOTsrRmYJTzMKKE+rmsPFHkYMK2VM0nITBzCBAqAr5dIQjOG0fkUPOqQKAJ4M\nSR1+mcrWZfU7qEUKcGtVpRaZufRYpdS9DfB4I+8nDIjuSoDLjkLD5NsZddZt\nvHjDzQMttHTlnKBhTCLq1FtbXgZ+IrEbRwzCYhbCmQIJXX39w1eDWUydUHuQ\nElPS2kYBFtrSWMrrtuVjXC2eXf3vbGX+i+iQyoN8+ZJgqFydLjXT8e5oFCki\nSQNK5x9MyVJfiXBM2tv363GEPfd3r7eW5Uo6KjMTS/OrzLFCZImlaoUsUy1P\nj/4gDyUWlYrOO7500hVj+4+a0aqvK/IarS4ozBYiWMQLENlLvJilcPNTINv2\nE2ljP0xi0P/c8ryFghSB3cpvMbAe+goCo8WGq3CXXcmlIbHnc1+cZnQUv2Jp\nMdVdCmhEfCZE9iTiRmJOGJrmWHU1IOLJVlEsKJT4twNY1K9bz31KeIuCnxG9\nmvTH\r\n=JCfs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "9073b101e04d52711ba05918b19f9aaa8ee93f64", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "deep-equal": "1.0.1", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/accepts_1.3.6_1556469304437_0.6314449329354344", "host": "s3://npm-registry-packages"}}, "1.3.7": {"name": "accepts", "version": "1.3.7", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "531bc726517a3b2b41f850021c6cc15eaab507cd", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.7.tgz", "fileCount": 5, "integrity": "sha512-Il80Qs2WjYlJIBNzNkK6KYqlVMTbZLXgHx2oT0pU/fjRHyEp+PEfEPY0R3WCwAGVOtauxh1hOxNgIf5bv7dQpA==", "signatures": [{"sig": "MEQCIFogk5Cj1K5QLXRJwecZHADURUzWuiiJ3HcxrXd1e7giAiAXS0qqyk7i+9hNwBAOQg++PsIRS2NYU5zSLQB6D+SQIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcx8PPCRA9TVsSAnZWagAAM+kP/jPydIHPrA4TftraRNde\nnxojlC9prOP0Sn9FxBvevf3S9zBFEa2sa5fVUP4LUkNmG57fcmroDAaXnllW\nof8elLx8Al27QtOUi5lZ36AZAJ/aYHtGcTPnLjZejZOido1Mi2h8em/4Rk7M\nK/1RhYxG48u6B1Q/ZPXyJ23r95/PqfBhzAmaAKUfYBrcCMU/WT1SPS6DLCKv\nQZ6Oj9DFFlK7R+L15vRG7U1qmyMjkOVgK+oaNev7fpR0qVtc92xhfomgfrSK\ngqTrj05bKu4KIpJwH/T5GieWE2w7s42Q5TlmgWh/OMJNUFs9rltoe9tyetJE\nJcpTPFysR2lX5DS3YYwjgyguy515sseGMOIts0+92oE53OCKIC0FzE3IbPQw\nmXQCsUXK2IR+p3JwpIUz0oMswN4JDZ4I+BLNIy6LLibTiWw12NKdg1BWK/Yw\nJqZ5cyUW+45S3i82slyGttRABPS6WXq3CU5SqVp8+EUnwKqMceglw/b9dLfk\n0OiaPGGqUU48012PNNkqu1ERWqbb0JaGAlSrmaQRofGnceuAXvv2lCvAdhyc\n1hD32bl54Xox1ejJMCihiFJQCEOpTXrIEfXUEbyJFzSIZwaCW2uIP1OkYs9W\nPLWCaBiMcE12foiMMqv0cO1QrLYRyW1OPPttUhQoxbk//uKTMlrKPUjZM5PE\nR3Kk\r\n=HEy7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "2a6e060aebb52813fdb074e9e7f66da1cfa61902", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "deep-equal": "1.0.1", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/accepts_1.3.7_1556595662948_0.6750107293886682", "host": "s3://npm-registry-packages"}}, "1.3.8": {"name": "accepts", "version": "1.3.8", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@1.3.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "0bf0be125b67014adcb0b0921e62db7bffe16b2e", "tarball": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "fileCount": 5, "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "signatures": [{"sig": "MEUCID2zBfNvrinjSufAzQBgTsg5Jwjo17kwubk2xUFL1vGTAiEAvKZQk1nUxb52kz63YkSw6o8i0vBCa9/cuIABnt32IQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+xkoCRA9TVsSAnZWagAArCcP/1aVcPia9RUJZlCO2yDo\n0PpDE9011KnkaWFzGlgOwY9O7G7IU8SWeCViWTtV9RToPpwyNdfXdENw5ZY/\n1tep001kZkMoOmRjI19HoC5jr2qOju69v8I56lrF4eiBmpGQU23D+CGcAB75\nNPyhe67pGZhJcNuLP3NxLg1tRZ6n6cVgqO1lb16ZjVioG0YjR7GlCoMuyNrL\n1nXsDzhTgA9gczwJSGXKlTrB00Zzjb4xL1YZ67gQniMvBXsIJexAztyasIbi\n2jVfUfeFzWOc0Wh1iyYY/D9CmXB0SWAQjZtvLv9V4feSeONjwiZeiPTVKEei\nUK3eA9uF54IYbe2j2HMBwMlH5RaTi8NnXlQTx2SkSSXigTSHdS1bn1bDo2cv\ndTiziRcCX7wnG9rax8CL/80RQguKinTWEytG5mhMnwDFjt5S3O/eqykfBx9L\n1eoGdsWposIXvv8UoFcu0OFY9IPz/X3w4IGoT1bZeFDEOsOTPpyf580iAXZ9\ndgs1h78kMmSMfhUlx0yabFIcAdmySxtv5BFBTtOLE2TOHGw+i0jM6bLVXVKx\nibjOSiwF+nAw22MiERbiybOLN/cnQkgTNBvPym6NTSX+cB5Y5JAjcm8tsYdY\nQHmlZNziFoDeXuX4Y6Pa/Sj4NOma+LK2SE+q8RIkja9n/GFtcVfwlaRgp1mx\nY5cn\r\n=htPt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}, "gitHead": "f69c19e459bd501e59fb0b1a40b7471bb578113a", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "deep-equal": "1.0.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/accepts_1.3.8_1643845928595_0.6911698324758517", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "accepts", "version": "2.0.0", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "_id": "accepts@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/accepts#readme", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "dist": {"shasum": "bbcf4ba5075467f3f2131eab3cffc73c2f5d7895", "tarball": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==", "signatures": [{"sig": "MEQCIHZ5Tos5rXzEAFqfbQFQQnIMCWmZaFBfDC5jeAVLg5JLAiAJ/hw3cOeKl5XZw6D1Y2zRzrfk1sdXq9vPmSR+ToHu+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16917}, "engines": {"node": ">= 0.6"}, "gitHead": "e1c347ee7fa91a74f21484daefd805c0cdc4f949", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Higher-level content negotiation", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "deep-equal": "1.0.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "4.3.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/accepts_2.0.0_1725119405542_0.5350510385908038", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-12-27T21:42:44.260Z", "modified": "2025-05-14T14:56:12.314Z", "1.0.0": "2013-12-27T21:42:44.260Z", "1.0.1": "2014-01-18T10:45:26.867Z", "1.0.2": "2014-05-29T17:39:16.522Z", "1.0.3": "2014-06-12T02:20:47.089Z", "1.0.4": "2014-06-20T09:14:35.360Z", "1.0.5": "2014-06-20T18:09:25.636Z", "1.0.6": "2014-06-25T00:41:01.419Z", "1.0.7": "2014-07-04T16:44:27.323Z", "1.1.0": "2014-09-02T08:42:07.312Z", "1.1.1": "2014-09-29T02:33:19.048Z", "1.1.2": "2014-10-15T05:46:17.738Z", "1.1.3": "2014-11-09T22:54:13.630Z", "1.1.4": "2014-12-10T20:46:16.678Z", "1.2.0": "2014-12-19T18:39:58.872Z", "1.2.1": "2014-12-30T16:53:59.673Z", "1.2.2": "2014-12-30T23:03:52.908Z", "1.2.3": "2015-02-01T06:44:08.286Z", "1.2.4": "2015-02-15T02:26:32.170Z", "1.2.5": "2015-03-14T02:00:21.101Z", "1.2.6": "2015-05-07T13:19:34.052Z", "1.2.7": "2015-05-11T02:38:21.186Z", "1.2.8": "2015-06-08T04:36:45.130Z", "1.2.9": "2015-06-08T15:33:29.234Z", "1.2.10": "2015-07-01T20:17:54.682Z", "1.2.11": "2015-07-17T03:21:56.994Z", "1.2.12": "2015-07-31T02:33:20.250Z", "1.2.13": "2015-09-07T02:59:01.117Z", "1.3.0": "2015-09-30T01:30:22.953Z", "1.3.1": "2016-01-20T04:49:25.286Z", "1.3.2": "2016-03-09T04:21:11.635Z", "1.3.3": "2016-05-03T05:05:33.253Z", "1.3.4": "2017-08-23T02:24:13.961Z", "1.3.5": "2018-03-01T01:58:47.742Z", "1.3.6": "2019-04-28T16:35:04.574Z", "1.3.7": "2019-04-30T03:41:03.144Z", "1.3.8": "2022-02-02T23:52:08.772Z", "2.0.0": "2024-08-31T15:50:05.694Z"}, "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "license": "MIT", "homepage": "https://github.com/jshttp/accepts#readme", "keywords": ["content", "negotiation", "accept", "accepts"], "repository": {"url": "git+https://github.com/jshttp/accepts.git", "type": "git"}, "description": "Higher-level content negotiation", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://jongleberry.com", "name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>dd"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "", "readmeFilename": "", "users": {"akiva": true, "eyson": true, "kkuehl": true, "kungkk": true, "quafoo": true, "tedyhy": true, "flyslow": true, "santihbc": true, "zuojiang": true, "snowdream": true, "goodseller": true, "jessaustin": true, "magicadiii": true, "qqqppp9998": true, "rocket0191": true, "simplyianm": true, "flumpus-dev": true, "leelee.echo": true, "nisimjoseph": true, "wangnan0610": true, "mobeicaoyuan": true, "program247365": true}}