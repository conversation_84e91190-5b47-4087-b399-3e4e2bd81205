{"name": "streamsearch", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.1": {"name": "streamsearch", "version": "0.0.1", "dist": {"shasum": "5350ebc548378da45f3727aefebac3aec5dcc345", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.0.1.tgz", "integrity": "sha512-l+XPdaQqIZo9By5FZFnK8OskgaU+j7x6Ia5DQOo+407ywlA0Cem1/jFU/sTx68bBn+YTZEpqtC5M2wmbLFFaIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEy3XvyxBkmRq/MN7km3hqKk48oSXo8SRUi/9DvjiqkkAiEAlgOHQaymojRR2IjTaLxnbGmyVErcC/HzTFfde8235yo="}]}, "engines": {"node": ">=0.8.0"}}, "0.0.2": {"name": "streamsearch", "version": "0.0.2", "dist": {"shasum": "0d446c92b56bad4aa96d588aeeb9d90e1c4af8be", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.0.2.tgz", "integrity": "sha512-zXdmbZ4zab4qeVytS+zewq6c8Y2Vk6NbUJtfGPQVDbFFMGzER2ObbLU7MK/fHK09aPkKTkZV0EvUDibKU/fG+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsjDTvNRkWad7e5FyOZYz8TI0fMvDKq+iJOQ1WhEuyWQIgVUoILHZGwaS6SMo061/DECfRuvwcWImReTGO688Tvac="}]}, "engines": {"node": ">=0.8.0"}}, "0.1.0": {"name": "streamsearch", "version": "0.1.0", "dist": {"shasum": "2c106c4b8e869f9762bb0c362c5af1e8d3b4da74", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.0.tgz", "integrity": "sha512-fs9+MyAFTYEF3ydWy1OI1VM4GFiqR5M5punqt+n/tg2fuh85X6H4huut9tn9CcPG2dfaWin9hgEMDRGlaISN+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPjiF0cDTKQdcv0PKkWqVtqxiF4fNwaS5qdIjA2f8kPQIgTPxKVtwIrh+I5UMdS+3kDn+/ufZXEIARhoX3MbVJ3Ag="}]}, "engines": {"node": ">=0.8.0"}}, "0.1.1": {"name": "streamsearch", "version": "0.1.1", "dist": {"shasum": "b29160bf407ba701ae9326249cbb3245e46a37c1", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.1.tgz", "integrity": "sha512-yf+nQUyqxWC4FsrLAPNhHWbKqNEzOpHHYkc8jcI4qp1AMlf3nwgXj2RyZFISVZE9aBk0/EC1ovTeR5ijNNu+9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5fGHXkCKwNP2Geg3MBKqf+3mPX4WZzL//oVtctwOXFwIhAOPnk2qOnQRzUaj2A0s7L6o2Mma04ayAgzy1bJOSOLBd"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.2": {"name": "streamsearch", "version": "0.1.2", "dist": {"shasum": "808b9d0e56fc273d809ba57338e929919a1a9f1a", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBHsFmyhBGzskd9VOZ6EoXTVivWbuhaoWX8rBGoyRUIgAiBG5ySQPIY5u91CI837BlPPmScJjlBV9y7qHXoqSe7DBA=="}]}, "engines": {"node": ">=0.8.0"}}, "1.0.0": {"name": "streamsearch", "version": "1.0.0", "devDependencies": {"@mscdex/eslint-config": "^1.0.0", "eslint": "^7.0.0"}, "dist": {"integrity": "sha512-irkOuAxxhc6BjGfH09Zy36+LJhDOj3ElPVmHpCeRmZbuHI3AF+HgEt6y6k7STvCByKPa2OHFqtS94EEHu4/j3w==", "shasum": "2efd0aab4bb054b3490d06633e9cff92191e54ec", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.0.0.tgz", "fileCount": 13, "unpackedSize": 13155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrZLfCRA9TVsSAnZWagAAWYUQAINTKvVndWARsfqtgCwk\nvSwrNjJH+LaGNLmyvxBDufOXfttO4/EtPsN8GM6KJ83wCU+Hh4ti6jDX5rS5\nauJUpxto5vgHok9FhOU/G+BsVVmAxwQiFu8gvAOnOl2Gcp2U07o0urh2tzuZ\nmbGE3rzrAkSaVwiAT3dbFySImDddg22rIfLWEQMsHfzlIWQ/T61e9aTfa6F0\neCfaiehEKsFwvSEBewaN7m32PjCxQjnxFNPXM/XE5ctGPt0mkeL4Mc7O4pY0\n7CM1pngbvWq6Sp68btV1aM+2GiswsMkFQDZQUA3A6vk3ZKSc5NqxZC4X0E9K\nLqGN6Vzhn5RGkev3dgL3BER+QG3gM67zw9zOMuJkoka99X2TZHAA9xlNsQxD\n001bJDrY7bm7ufyj7f5anKeVnVzDheIvmVNidybQEY7ftqLnYf1VBowGWfWL\nwvp4+AgDGpE6+GPJkjvNo6YCSlRF0iRGK7lPl4DQ1wildaM55LOc1iZYzgU6\nqu5zIUvUM96vxu9j6h7oYhzx95X0PcV6w+qUtNM+94KHXDXDtY91Nr77YasQ\niu/ppxlGwex9ZDxVLecnvfWcp7TuZCsCJr03QK5kERjugDBtcIKjlYyPcFSO\nmo39FK61iovcqieAKrSKcI1yYMWWNU/IjktuDuU05webBxl3IQkArD204oc8\nDZZa\r\n=WgoY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYbeDFIS7RJN/tJ00VGrrC5tNn3p1mPUFtDcuNBgRyVQIhAKGub/mJSpyM5xofYx45KbkEPbqd+83ekaLSMOUEgOcP"}]}, "engines": {"node": ">=10.0.0"}}, "1.1.0": {"name": "streamsearch", "version": "1.1.0", "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "dist": {"integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==", "shasum": "404dd1e2247ca94af554e841a8ef0eaa238da764", "tarball": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "fileCount": 13, "unpackedSize": 16575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhv4QUCRA9TVsSAnZWagAA1PMP/AruwrATdOkXpas8SDG1\nvPulhrOsNeawr/n0NUQ/TOudoPzYV/3mnfHEgpdWp9+6ecAY/soacK835Pc4\n//ekLVhw5nX8p+PFJBTkMEG/NU6+jJimNFw4V6jceONSml+39kb6yxSO6Pz2\nhaMa1sZVno6VH8IkhHPuGMKQZEUqvBvgXS+JgZjZgb4Mad7DQGcerfrPDdvy\nLjIG31H/tMojS+ZGVU5e7GEDSjw008PsJGDXRjToLcJYET5JzyA0i/edOypa\n2d8R91kM0PHsi5gJsut0H6MDaCxhVwKN5nIDyOfWpCyRPKbKPJat3ox8LSCk\nXDz+vCtCzbKgRNlw/Iw2tPTnO7XOZ1WUaR+whXhBluelqulaOSatlSWfymXv\nr0/KVUHFvywpBOt6cG9Vh/30BL9tHbTk8YTP4733J3VXyNtHHSXk19u82udU\nA/EkVxUZohXQJnNpSIS71I12DyVkKLR/2wCRpUIJodwJiBU/kqDhk7qyd90I\nWtxamM6+9wQT7Z56at0LOeAJaJ2WnPJjLCIPGIWu+MrEsq4O0K6J3Gd5464i\nUAyc5hbWicgQBgNv+dbg5ZWxwtu7wL912el0CIdODVd2iu9fBaWLBNJwRV6H\nOMfyyVYIP8UznO9eLWi7WNI6X5EvnoGOHRmTZBsT54MQm/p2Bup2iENwwzHA\nuv4W\r\n=Obyb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDM3wtpkzcQNoBpgTJd003vDizv+QVozM+wKJSQcVDLywIhAJducrsvaQLGyvGETu2sFBjWLhs1D+3tRV5RGt0zOjZA"}]}, "engines": {"node": ">=10.0.0"}}}, "modified": "2022-06-27T00:55:17.099Z"}