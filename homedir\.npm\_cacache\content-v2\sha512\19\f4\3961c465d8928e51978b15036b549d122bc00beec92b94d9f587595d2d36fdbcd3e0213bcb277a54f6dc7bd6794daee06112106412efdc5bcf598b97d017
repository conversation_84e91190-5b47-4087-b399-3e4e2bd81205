{"_id": "keypress", "_rev": "48-3bfadf49f99f7ba5572feda31197a234", "name": "keypress", "dist-tags": {"latest": "0.2.1"}, "versions": {"0.0.1": {"name": "keypress", "version": "0.0.1", "keywords": ["keypress", "readline", "core"], "author": {"url": "http://tootallnate.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keypress@0.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "95e7108bb11835c4e41438697b2a82dae40029ad", "tarball": "https://registry.npmjs.org/keypress/-/keypress-0.0.1.tgz", "integrity": "sha512-A/oFptkBL2fGZhdSs0GVY6QnafiOds1G7I+ArboLtNWVkQADBKINqPekt49fXosJBI3fTEQFmzq6i9viiae0QQ==", "signatures": [{"sig": "MEYCIQClE0GzpjjoTWzltXZQvh49v2WRuGgimV8BC29TIyrPggIhAMjWdmHLDIHPYEB4I9ka+S7trtaS3BRDTCXIk3oBxsV8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "readme": "keypress\n========\n### Make any Node ReadableStream emit \"keypress\" events\n\n\nPrevious to Node `v0.8.x`, there was an undocumented `\"keypress\"` event that\n`process.stdin` would emit when it was a TTY.\n\nNow in Node `v0.8.x`, this `\"keypress\"` event does not get emitted by default,\nbut rather only when it is being used in conjuction with the `readline` (or by\nextension, the `repl`) module.\n\nThis module is the exact logic from the node `v0.8.x` releases ripped out into its\nown module.\n\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install keypress\n```\n\nOr add it to the `\"dependencies\"` section of your _package.json_ file.\n\n\nExample\n-------\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"keypress\" events\nkeypress(process.stdin);\n\n// listen for the \"keypress\" event\nprocess.stdin.on('keypress', function (ch, key) {\n  console.log('got \"keypress\"', key);\n  if (key.ctrl && key.name == 'c') {\n    process.stdin.pause();\n  }\n});\n\nprocess.stdin.resume();\n```\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan <PERSON>lich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"url": "git://github.com/TooTallNate/keypress.git", "type": "git"}, "description": "Make any Node ReadableStream emit \"keypress\" events", "directories": {}}, "0.0.2": {"name": "keypress", "version": "0.0.2", "keywords": ["keypress", "readline", "core"], "author": {"url": "http://tootallnate.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keypress@0.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "90a371646c12d1d943fced4377b6ecbd8a01bd89", "tarball": "https://registry.npmjs.org/keypress/-/keypress-0.0.2.tgz", "integrity": "sha512-pVMv2SumoZcDNl/kfOmPxvrb/DEWJYUxXS03Cl/nL4Vp512Xfbmv51cM6mqhBEzY9Ud0wa9k5be2mA9di9yt0A==", "signatures": [{"sig": "MEUCIQD8Nz2kByVF9HbA/YOPMNqxcmmyIj4W1TACF7qOo3V33AIgBfWozR8JhP4JX05w7dLOvTEOBIraU7byDnI5KUo4Gm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "readme": "keypress\n========\n### Make any Node ReadableStream emit \"keypress\" events\n\n\nPrevious to Node `v0.8.x`, there was an undocumented `\"keypress\"` event that\n`process.stdin` would emit when it was a TTY.\n\nNow in Node `v0.8.x`, this `\"keypress\"` event does not get emitted by default,\nbut rather only when it is being used in conjuction with the `readline` (or by\nextension, the `repl`) module.\n\nThis module is the exact logic from the node `v0.8.x` releases ripped out into its\nown module.\n\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install keypress\n```\n\nOr add it to the `\"dependencies\"` section of your _package.json_ file.\n\n\nExample\n-------\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"keypress\" events\nkeypress(process.stdin);\n\n// listen for the \"keypress\" event\nprocess.stdin.on('keypress', function (ch, key) {\n  console.log('got \"keypress\"', key);\n  if (key.ctrl && key.name == 'c') {\n    process.stdin.pause();\n  }\n});\n\nprocess.stdin.resume();\n```\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan <PERSON>lich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"url": "git://github.com/TooTallNate/keypress.git", "type": "git"}, "description": "Make any Node ReadableStream emit \"keypress\" events", "directories": {}}, "0.1.0": {"name": "keypress", "version": "0.1.0", "keywords": ["keypress", "readline", "core"], "author": {"url": "http://tootallnate.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keypress@0.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "4a3188d4291b66b4f65edb99f806aa9ae293592a", "tarball": "https://registry.npmjs.org/keypress/-/keypress-0.1.0.tgz", "integrity": "sha512-x0yf9PL/nx9Nw9oLL8ZVErFAk85/lslwEP7Vz7s5SI1ODXZIgit3C5qyWjw4DxOuO/3Hb4866SQh28a1V1d+WA==", "signatures": [{"sig": "MEUCIQDjhCR1aOr+rCkTNk2XiXf5TcMoSQqiZoYTkP1LG6OmQQIgPJ7cn045QcmevuREnVWGPT18TmPui3QO4GUvhcrfRxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "readme": "keypress\n========\n### Make any Node ReadableStream emit \"keypress\" events\n\n\nPrevious to Node `v0.8.x`, there was an undocumented `\"keypress\"` event that\n`process.stdin` would emit when it was a TTY. Some people discovered this hidden\ngem, and started using it in their own code.\n\nNow in Node `v0.8.x`, this `\"keypress\"` event does not get emitted by default,\nbut rather only when it is being used in conjuction with the `readline` (or by\nextension, the `repl`) module.\n\nThis module is the exact logic from the node `v0.8.x` releases ripped out into its\nown module.\n\n__Bonus:__ Now with mouse support!\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install keypress\n```\n\nOr add it to the `\"dependencies\"` section of your _package.json_ file.\n\n\nExample\n-------\n\n#### Listening for \"keypress\" events\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"keypress\" events\nkeypress(process.stdin);\n\n// listen for the \"keypress\" event\nprocess.stdin.on('keypress', function (ch, key) {\n  console.log('got \"keypress\"', key);\n  if (key && key.ctrl && key.name == 'c') {\n    process.stdin.pause();\n  }\n});\n\nprocess.stdin.setRawMode(true);\nprocess.stdin.resume();\n```\n\n#### Listening for \"mousepress\" events\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"mousepress\" (and \"keypress\") events\nkeypress(process.stdin);\n\n// you must enable the mouse events before they will begin firing\nkeypress.enableMouse(process.stdout);\n\nprocess.stdin.on('mousepress', function (info) {\n  console.log('got \"mousepress\" event at %d x %d', info.x, info.y);\n});\n\nprocess.on('exit', function () {\n  // disable mouse on exit, so that the state\n  // is back to normal for the terminal\n  keypress.disableMouse(process.stdout);\n});\n```\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan Rajlich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"url": "git://github.com/TooTallNate/keypress.git", "type": "git"}, "description": "Make any Node ReadableStream emit \"keypress\" events", "directories": {}}, "0.2.0": {"name": "keypress", "version": "0.2.0", "keywords": ["keypress", "readline", "core"], "author": {"url": "http://tootallnate.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keypress@0.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/keypress/issues"}, "dist": {"shasum": "411c8fa37db7762e7d4d87d1e729513a49cd56ab", "tarball": "https://registry.npmjs.org/keypress/-/keypress-0.2.0.tgz", "integrity": "sha512-HUvYYij/QQL6+exYbVh6AFieQVGcKU4FoVuOfvLFRezea/LjEDYT63Kr/Rv4ycTCYHZhY4XYo2ygsGRcDpbVCw==", "signatures": [{"sig": "MEQCIEzk3h/VqCETEaZ+rE8vysUNrk/Rjn/Tn7wagr5qSoF/AiAqvoydPzhyjDd4z2cZqE3/cDW6KVSDyd0aNOOLlwwE4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "readme": "keypress\n========\n### Make any Node ReadableStream emit \"keypress\" events\n\n\nPrevious to Node `v0.8.x`, there was an undocumented `\"keypress\"` event that\n`process.stdin` would emit when it was a TTY. Some people discovered this hidden\ngem, and started using it in their own code.\n\nNow in Node `v0.8.x`, this `\"keypress\"` event does not get emitted by default,\nbut rather only when it is being used in conjuction with the `readline` (or by\nextension, the `repl`) module.\n\nThis module is the exact logic from the node `v0.8.x` releases ripped out into its\nown module.\n\n__Bonus:__ Now with mouse support!\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install keypress\n```\n\nOr add it to the `\"dependencies\"` section of your _package.json_ file.\n\n\nExample\n-------\n\n#### Listening for \"keypress\" events\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"keypress\" events\nkeypress(process.stdin);\n\n// listen for the \"keypress\" event\nprocess.stdin.on('keypress', function (ch, key) {\n  console.log('got \"keypress\"', key);\n  if (key && key.ctrl && key.name == 'c') {\n    process.stdin.pause();\n  }\n});\n\nprocess.stdin.setRawMode(true);\nprocess.stdin.resume();\n```\n\n#### Listening for \"mousepress\" events\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"mousepress\" (and \"keypress\") events\nkeypress(process.stdin);\n\n// you must enable the mouse events before they will begin firing\nkeypress.enableMouse(process.stdout);\n\nprocess.stdin.on('mousepress', function (info) {\n  console.log('got \"mousepress\" event at %d x %d', info.x, info.y);\n});\n\nprocess.on('exit', function () {\n  // disable mouse on exit, so that the state\n  // is back to normal for the terminal\n  keypress.disableMouse(process.stdout);\n});\n```\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan Rajlich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/keypress.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Make any Node ReadableStream emit \"keypress\" events", "directories": {}, "readmeFilename": "README.md"}, "0.2.1": {"name": "keypress", "version": "0.2.1", "keywords": ["keypress", "readline", "core"], "author": {"url": "http://tootallnate.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "keypress@0.2.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/keypress/issues"}, "dist": {"shasum": "1e80454250018dbad4c3fe94497d6e67b6269c77", "tarball": "https://registry.npmjs.org/keypress/-/keypress-0.2.1.tgz", "integrity": "sha512-HjorDJFNhnM4SicvaUXac0X77NiskggxJdesG72+O5zBKpSqKFCrqmndKVqpu3pFqkla0St6uGk8Ju0sCurrmg==", "signatures": [{"sig": "MEUCIQDXNCoMgL+nS1c8PwgcHyqWhT2ER66QLUwG65Bn1h7XKQIgbF3wd2VSdiasqN0Ev6kOzWtzqgxKmX1q0lhQn3O9gV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "readme": "keypress\n========\n### Make any Node ReadableStream emit \"keypress\" events\n\n\nPrevious to Node `v0.8.x`, there was an undocumented `\"keypress\"` event that\n`process.stdin` would emit when it was a TTY. Some people discovered this hidden\ngem, and started using it in their own code.\n\nNow in Node `v0.8.x`, this `\"keypress\"` event does not get emitted by default,\nbut rather only when it is being used in conjuction with the `readline` (or by\nextension, the `repl`) module.\n\nThis module is the exact logic from the node `v0.8.x` releases ripped out into its\nown module.\n\n__Bonus:__ Now with mouse support!\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install keypress\n```\n\nOr add it to the `\"dependencies\"` section of your _package.json_ file.\n\n\nExample\n-------\n\n#### Listening for \"keypress\" events\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"keypress\" events\nkeypress(process.stdin);\n\n// listen for the \"keypress\" event\nprocess.stdin.on('keypress', function (ch, key) {\n  console.log('got \"keypress\"', key);\n  if (key && key.ctrl && key.name == 'c') {\n    process.stdin.pause();\n  }\n});\n\nprocess.stdin.setRawMode(true);\nprocess.stdin.resume();\n```\n\n#### Listening for \"mousepress\" events\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"mousepress\" (and \"keypress\") events\nkeypress(process.stdin);\n\n// you must enable the mouse events before they will begin firing\nkeypress.enableMouse(process.stdout);\n\nprocess.stdin.on('mousepress', function (info) {\n  console.log('got \"mousepress\" event at %d x %d', info.x, info.y);\n});\n\nprocess.on('exit', function () {\n  // disable mouse on exit, so that the state\n  // is back to normal for the terminal\n  keypress.disableMouse(process.stdout);\n});\n```\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan Rajlich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/keypress.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "Make any Node ReadableStream emit \"keypress\" events", "directories": {}, "readmeFilename": "README.md"}}, "time": {"created": "2012-07-06T22:10:37.502Z", "modified": "2025-02-16T08:13:42.205Z", "0.0.1": "2012-07-06T22:10:39.002Z", "0.0.2": "2012-07-06T22:37:46.988Z", "0.1.0": "2012-08-08T21:32:04.471Z", "0.2.0": "2013-06-17T20:03:45.827Z", "0.2.1": "2013-07-06T04:58:05.119Z"}, "bugs": {"url": "https://github.com/TooTallNate/keypress/issues"}, "author": {"url": "http://tootallnate.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["keypress", "readline", "core"], "repository": {"url": "git://github.com/TooTallNate/keypress.git", "type": "git"}, "description": "Make any Node ReadableStream emit \"keypress\" events", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "readme": "keypress\n========\n### Make any Node ReadableStream emit \"keypress\" events\n\n\nPrevious to Node `v0.8.x`, there was an undocumented `\"keypress\"` event that\n`process.stdin` would emit when it was a TTY.\n\nNow in Node `v0.8.x`, this `\"keypress\"` event does not get emitted by default,\nbut rather only when it is being used in conjuction with the `readline` (or by\nextension, the `repl`) module.\n\nThis module is the exact logic from the node `v0.8.x` releases ripped out into its\nown module.\n\n\nInstallation\n------------\n\nInstall with `npm`:\n\n``` bash\n$ npm install keypress\n```\n\nOr add it to the `\"dependencies\"` section of your _package.json_ file.\n\n\nExample\n-------\n\n``` js\nvar keypress = require('keypress');\n\n// make `process.stdin` begin emitting \"keypress\" events\nkeypress(process.stdin);\n\n// listen for the \"keypress\" event\nprocess.stdin.on('keypress', function (ch, key) {\n  console.log('got \"keypress\"', key);\n  if (key.ctrl && key.name == 'c') {\n    process.stdin.pause();\n  }\n});\n\nprocess.stdin.resume();\n```\n\n\nLicense\n-------\n\n(The MIT License)\n\nCopyright (c) 2012 Nathan <PERSON>lich &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "users": {"luk": true, "mle626": true, "bracken": true, "jovaage": true, "kontrax": true, "sopepos": true, "cantidio": true, "gazzwi86": true, "hugovila": true, "krickray": true, "tommyzzm": true, "abuelwafa": true, "darkowlzz": true, "guananddu": true, "lmanukyan": true, "wolfram77": true, "cbpetersen": true, "quocnguyen": true, "daniel7byte": true, "demian_dark": true, "flumpus-dev": true, "mimshwright": true, "tunnckocore": true, "ctrl-alt-meh": true, "about_hiroppy": true, "fabian.moron.zirfas": true}}