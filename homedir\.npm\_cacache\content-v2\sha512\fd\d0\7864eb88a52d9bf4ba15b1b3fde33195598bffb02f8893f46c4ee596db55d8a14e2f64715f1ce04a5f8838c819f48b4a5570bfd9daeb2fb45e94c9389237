{"_id": "typedarray", "_rev": "25-3b734b6f1268a2b0456bfcfa390cebac", "name": "typedarray", "description": "TypedArray polyfill for old browsers", "dist-tags": {"latest": "0.0.7"}, "versions": {"0.0.0": {"name": "typedarray", "version": "0.0.0", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.0", "dist": {"shasum": "86e544351e89c298684e7e199740bc8c54e680ec", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.0.tgz", "integrity": "sha512-D4pvdIGj/HUa9dAJfANWHmSLtJDLrFHK1ZlNtXKFUUInzyMiR4r60z+Vivc47mx6Dj75ivwQTNL2tq9iVttnpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyvR3Ow97+N0+ulCUUJN5RmGbsjRFM/0lsBDlYcAnX8wIgczNlJCl14fCpd1L5mXu4GaB+3Ppc8icBNXYxb0OTogY="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "typedarray", "version": "0.0.1", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.1", "dist": {"shasum": "6d5a80c347487282655ce62284711195553b5fdb", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.1.tgz", "integrity": "sha512-EShIi1n21V6kbPzFop+LAtPhSDGMI5NFJFs5KdM8QWx+saLHiT5H9VVpgHhsncKkj51yq/tn6jPW5tFDi0Fe5g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGi98JBOwHxYFf2DcYUyTVEm4yYZFHFE2Iin0YoAyX8dAiAtpHZsFFdB9yWHQ/1uE5PuyvW++Ao4ersxVWhvgKYRlQ=="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "typedarray", "version": "0.0.2", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.2", "dist": {"shasum": "09b4153d88ec5c0a8e7ab3ff84cfb7082a88c866", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.2.tgz", "integrity": "sha512-TQi21qVyMpA8xUQ/zHrG/a/smsYPt/E4QpfpteNBG+s90gBUvaSR7PpgZ+W88ednSciX9wmcE2bXA/ZirsQrMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH1HxHEPoi60D5aqkKV2H9boGmHgun8RCvV1Ub61SoqFAiEA4PVB+wrpoKAj2j2Q6U4ceBPh7ihkqp5tXyc8dkZd4NE="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "typedarray", "version": "0.0.3", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.3", "dist": {"shasum": "adb1d56ce9bb5750da4f721dbe79641fda11c754", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.3.tgz", "integrity": "sha512-4Sp2Yu3D7DwuKPIdNg9fT7LzGHCDNA2edwp74XXNnIKo9qGZ+lcwxxy/23KPu6wdVj8s4lAObI4ka5tetOM2ZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDT6o5QBWhXdnU8yRdzvt0feZryVqq2MzANLfq4MAusSAIhANLQqvZy0vSHSF02yQIChtCm3mEBWbIIOvJaMYsOH+ck"}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "typedarray", "version": "0.0.4", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.4", "dist": {"shasum": "df144a3231958a1735ea243435cf9deb04f37115", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.4.tgz", "integrity": "sha512-ie/DRmZs+LZsUXG87001UGxGwm22RRBJV7beyjGuTtmi6zGoNxk2cdWW6+vf6Ob/jnYrQSm7GdfUkpQ5kpEFAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0SWv6UH0MPtzEL5cvqdt9DiEUZ5ZBxIj0QgV9fKSnOAiB701aY0ICi6K7DD9MYYYooj+FIHhZR5c/WkAxRZ/XhmQ=="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "typedarray", "version": "0.0.5", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.5", "dist": {"shasum": "c4158fcd96c8ef91ef03cc72584c95e032877664", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.5.tgz", "integrity": "sha512-DaLV8SGy6mdtrSL/pmg/aXqmBANpHt2KB4sx1sOiqNTwB8tol1LDihu3JufoqGsU6HQgKSQn3J8U8uAzghVovg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrBDurxo6Shg6hzbfxI4lqfNkn8lYv3aGSzRoWgaIrjQIgN8fimzm802VGO5JCEsJJObXSZAL+aeJG3L4rNCRmIe4="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "typedarray", "version": "0.0.6", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~2.3.2"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/substack/typedarray/issues"}, "_id": "typedarray@0.0.6", "dist": {"shasum": "867ac74e3864187b1d3d47d996a78ec5c8830777", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEdX4WI37OIkiA40YDwRUS7L/BZ/UIsr48OOD64NeqSqAiEAywM1DIvwoElvWQDaUO5gRSnaeTMt8ZXUyccvHnr8Taw="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"name": "typedarray", "version": "0.0.7", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/es-shims/typedarray.git"}, "homepage": "https://github.com/es-shims/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "bfa163321b8ccab9c5351ff7fa71826a9355614d", "bugs": {"url": "https://github.com/es-shims/typedarray/issues"}, "_id": "typedarray@0.0.7", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-ueeb9YybpjhivjbHP2LdFDAjbS948fGEPj+ACAMs4xCMmh72OCOMQWBQKlaN4ZNQ04yfLSDLSx1tGRIoWimObQ==", "shasum": "799207136a37f3b3efb8c66c40010d032714dc73", "tarball": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.7.tgz", "fileCount": 10, "unpackedSize": 33089, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICNvkCh0nKyjVZ9bdDp2OQu6kWdk7izoBlrbSyk5ziZbAiAr8NqoXsEcIUxXmcYGI9bf8Xf6CRZMEkRptpvmwcx15A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRmzkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbkhAApEQbX6XB8MHqxp/S9dkWlT5b3Ior0vEIKjRN6erta/DOZ4lk\r\nWViBgB0p0uEPgwAGe88IJk4Le4rT2PQOZqpFatTlYDTxrJuN1VCbD373ziRe\r\nb6vkcUhr7yFLc3nQTaNHz/4OVuhOlzsdIUChP9tPhIV1r7MyhwJQs0Pp9KX6\r\nakQIzPt8BtD/IaGiD7vzChQz9xMyShSZBsZ/pGcdDw/2rCoSc3nqpO6n+GkE\r\nowCk8cXSjaoXNlWA1UvqRAGoSV2Go8I138xeZ1EI/bnCAB24acasisma9tlV\r\n+34UC03Fjarwib/GYz6YUtNpM4MqL/A4jMYiNzLESR9E5oqR7pDd+4uvxngk\r\n+2eHiAVojxjsbGdipTV5Sp99yeJi1l482JzGDdlzi6p9Opf9L7zW0UAeyNxk\r\ndLf+4cWCMdeAJyyvbzC+oi/zhvjxbkEZf5Z7JS+cN19Jt4tY9+vAkwfkFiTa\r\nG/C67m0nPsqveY5CYqGrFCIwMEXiFgyIeLivpBm3hUNG9Zt2v7C+LTUKgnDL\r\nf0Jkh5a55HZ9Vk+mLxoq2wD+3rUty2siStPbtqso46DtKlS2VbaVDZl+CCNN\r\nkZ+2Cp6qlFBvzjcfdOYgUyXBSyCA55FgR+B063SBVj6Q8H/imzEzEKQI5LUt\r\nSEUHCzI5cgok1z2B9Gi0lyQ+JoUAhlX32Oc=\r\n=tqbr\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typedarray_0.0.7_1665559779811_0.7431697697830648"}, "_hasShrinkwrap": false}}, "readme": "# typedarray <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nTypedArray polyfill ripped from [this\nmodule](https://raw.github.com/inexorabletash/polyfill).\n\n[![build status](https://secure.travis-ci.org/es-shims/typedarray.png)](http://travis-ci.org/es-shims/typedarray)\n\n[![testling badge](https://ci.testling.com/es-shims/typedarray.png)](https://ci.testling.com/es-shims/typedarray)\n\n# example\n\n``` js\nvar Uint8Array = require('typedarray').Uint8Array;\nvar ua = new Uint8Array(5);\nua[1] = 256 + 55;\nconsole.log(ua[1]);\n```\n\noutput:\n\n```\n55\n```\n\n# methods\n\n``` js\nvar TA = require('typedarray')\n```\n\nThe `TA` object has the following constructors:\n\n* TA.ArrayBuffer\n* TA.DataView\n* TA.Float32Array\n* TA.Float64Array\n* TA.Int8Array\n* TA.Int16Array\n* TA.Int32Array\n* TA.Uint8Array\n* TA.Uint8ClampedArray\n* TA.Uint16Array\n* TA.Uint32Array\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install typedarray\n```\n\nTo use this module in the browser, compile with\n[browserify](http://browserify.org)\nor download a UMD build from browserify CDN:\n\nhttp://wzrd.in/standalone/typedarray@latest\n\n# license\n\nMIT\n\n[package-url]: https://npmjs.org/package/typedarray\n[npm-version-svg]: https://versionbadg.es/es-shims/typedarray.svg\n[deps-svg]: https://david-dm.org/es-shims/typedarray.svg\n[deps-url]: https://david-dm.org/es-shims/typedarray\n[dev-deps-svg]: https://david-dm.org/es-shims/typedarray/dev-status.svg\n[dev-deps-url]: https://david-dm.org/es-shims/typedarray#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/typedarray.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/typedarray.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/typedarray.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=typedarray\n[codecov-image]: https://codecov.io/gh/es-shims/typedarray/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/es-shims/typedarray/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/es-shims/typedarray\n[actions-url]: https://github.com/es-shims/typedarray/actions\n", "maintainers": [{"email": "<EMAIL>", "name": "lj<PERSON>b"}], "time": {"modified": "2023-07-12T19:11:35.411Z", "created": "2013-12-10T03:47:02.422Z", "0.0.0": "2013-12-10T03:47:04.285Z", "0.0.1": "2013-12-10T18:28:05.085Z", "0.0.2": "2013-12-11T02:48:58.496Z", "0.0.3": "2013-12-11T16:26:31.308Z", "0.0.4": "2013-12-11T16:26:44.325Z", "0.0.5": "2013-12-12T23:49:44.956Z", "0.0.6": "2014-05-17T02:52:49.854Z", "0.0.7": "2022-10-12T07:29:40.069Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "repository": {"type": "git", "url": "git://github.com/es-shims/typedarray.git"}, "homepage": "https://github.com/es-shims/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "bugs": {"url": "https://github.com/es-shims/typedarray/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}