{"_id": "dom-serializer", "_rev": "31-d7ee63aa6ecae8fa4a50a24a20979e74", "name": "dom-serializer", "description": "render domhandler DOM nodes to a string", "dist-tags": {"latest": "2.0.0"}, "versions": {"0.0.0": {"name": "dom-serializer", "version": "0.0.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "dependencies": {"domelementtype": "~1.1.1", "entities": "~1.1.1"}, "devDependencies": {"mocha": "*", "expect.js": "~0.3.1", "lodash": "~2.4.1", "jshint": "~2.3.0", "cheerio": "*"}, "scripts": {"test": "mocha test.js"}, "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer", "_id": "dom-serializer@0.0.0", "_shasum": "2a207dcae76ce52fbb1793f34f5df202fd1bdc4c", "_from": ".", "_npmVersion": "1.4.10", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "2a207dcae76ce52fbb1793f34f5df202fd1bdc4c", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.0.0.tgz", "integrity": "sha512-UpHEEu/MZqLLgILfcGZelQ7VyCWPQoSdw551xlXjoD4Zy11v3pVYrRniwDWzMNSK/opjVfBC7r3lkZxEPoif5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEQbWnLLwTrdCSR5uuPaA0DBziIP6Q1iLJdmIC4ClHlqAiAVktBzsnJiGI+j9ewgFyUIXoa8d8Np4qD2R/xiZRALGA=="}]}, "directories": {}}, "0.0.1": {"name": "dom-serializer", "version": "0.0.1", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "dependencies": {"domelementtype": "~1.1.1", "entities": "~1.1.1"}, "devDependencies": {"mocha": "*", "expect.js": "~0.3.1", "lodash": "~2.4.1", "jshint": "~2.3.0", "cheerio": "*"}, "scripts": {"test": "mocha test.js"}, "gitHead": "d678d0face5a3eb6d338da949a29ce9b70cf901a", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer", "_id": "dom-serializer@0.0.1", "_shasum": "9589827f1e32d22c37c829adabd59b3247af8eaf", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "9589827f1e32d22c37c829adabd59b3247af8eaf", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.0.1.tgz", "integrity": "sha512-evvizoLtT5uMpDT3iKRAx1NmTCk2ZdOD7ATqmL27QJkCv8XQmGKv/eFuvAjHhySNBACREAcXGBqozYhV1dOdag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN5DqFQk5GiFB+MdD3boRsmkNTvU06tk0EsP3sQqETcwIgWohEPFDnokotjKOHL9YqKA+i63gR+bXIYdN1W4qtus0="}]}, "directories": {}}, "0.1.0": {"name": "dom-serializer", "version": "0.1.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "files": ["index.js"], "dependencies": {"domelementtype": "~1.1.1", "entities": "~1.1.1"}, "devDependencies": {"cheerio": "*", "expect.js": "~0.3.1", "jshint": "~2.3.0", "lodash": "~2.4.1", "mocha": "*", "xyz": "0.4.x"}, "scripts": {"test": "mocha test.js"}, "license": "MIT", "gitHead": "249b9a921e6ba318c52b87de21e8475bcb4050e5", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer", "_id": "dom-serializer@0.1.0", "_shasum": "073c697546ce0780ce23be4a28e293e40bc30c82", "_from": ".", "_npmVersion": "2.4.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "073c697546ce0780ce23be4a28e293e40bc30c82", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.0.tgz", "integrity": "sha512-Fql7PX6CmQNVmoLfp7DlmvFMIL5cwLbm302SycA2iAMr95t1ITX4ilIsUG75rYtMiVLb4EMC5b2o7ApEpIXROg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDicGXo5cUikwHn9k8mba6gEyV3ZLkdKV4TlRGNkEXpSgIhAJiNZptbgUJQW2jZEgRdmuNzyEYBGZMSmNgSIcdB1R3S"}]}, "directories": {}}, "0.1.1": {"name": "dom-serializer", "version": "0.1.1", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "dependencies": {"domelementtype": "^1.3.0", "entities": "^1.1.1"}, "devDependencies": {"cheerio": "*", "expect.js": "^0.3.1", "jshint": "^2.9.1-rc1", "lodash": "^4.17.11", "mocha": "^5.2.0", "xyz": "^3.0.0"}, "scripts": {"test": "mocha test.js"}, "license": "MIT", "gitHead": "1b9eb87c621a184b97467b03600b50d08e5a5086", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@0.1.1", "_nodeVersion": "11.4.0", "_npmVersion": "6.8.0", "dist": {"integrity": "sha512-l0IU0pPzLWSHBcieZbpOKgkIn3ts3vAh7ZuFyXNwJxJXk/c4Gwj9xaTJwIDVQCXawWD0qb3IzMGH5rglQaO0XA==", "shasum": "1ec4059e284babed36eec2941d4a970a189ce7c0", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.1.tgz", "fileCount": 3, "unpackedSize": 4687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZbTtCRA9TVsSAnZWagAAq7sP/3WfWjU4JLXJRltwo+c0\niKgc7PG19mWJOvmkeYBff068+eSaydMPeVwp3eNSEiJucaSu4DiMjLQJeNaG\nOq27iRmhzqllbKQnIVaL1N7w5RgTjNclpppIucq+CChaSvgR+bbdd0I0dGEE\n/JJVQQe8bleUviK/1TMlwU8/E7N3A0jOVc7zkFVZUfvNjv9NFYU2vhV99AN0\nGPPkAaTHH1ZksSAHiXBHfhumOdY1eMu6iYjQ//3BOPSpg6YApKVzk9IMBZiA\nznZewTgKUv0fqhyvgZb7G/veLm0LQlfinCaAr+OZZ++4WRBIctVf6NfBfKMm\nJOhmu51ySnGCztquLManGIFnepQCse3Ej86yxzzMvoCntZs0MSvmr/PU71mM\n3SeCANkcyoh/6GZ8XsVC+EhiR8vuxt3n8f1bN/Uno/WU0aXvET/SlH2a8Ou8\nIcM8Gr7vaWfeHaKK+7XafLX7D59ro3dwdSa+CRGQ2caZubcOEEVLi2uiEk8M\nWPDY/vzzK7m1Oavf60Ac2nBKwkZJ8vOZLU1q/CurSPK+PLaIi8t4H1roU8AD\nukv9maIj6FfL6LGd7+TmCojkznP3/C7xsjNkOodiqLhPiFrO9PvUbl2aFWum\ncjLA0Yr/OqVv7HHiF4v2P2d4jJXM7X8gYplR7NKY8HlNjJ/dYCTrjX+ufG6C\nvY+A\r\n=Ni1r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9KK8yNM6eWFFlSMV9njYaGGK1tC85le3eifKsUEk7agIhAOn/2QpcCGMK2aeDMnDy8KYxiD7iGL+HQQWWvROBcv5y"}]}, "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_0.1.1_1550169324395_0.510742122552571"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "dom-serializer", "version": "0.2.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "devDependencies": {"cheerio": "^1.0.0-rc.2", "expect.js": "~0.3.1", "htmlparser2": "^3.10.0", "lodash": "^4.17.11", "mocha": "^6.2.0", "xyz": "^3.0.0"}, "scripts": {"test": "mocha test.js"}, "prettier": {"singleQuote": true}, "license": "MIT", "gitHead": "985d6f91c53ed8b9ed9bf20b4468e328212b523f", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@0.2.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-VSDJfax2yhDLK7pBbisf1xuH0qbkSV6A9JdEzesZCNPL5Nv1FxqJsJ8yV/zIa1dCa/ZJfLgXZh6bTQDSSc+57g==", "shasum": "fd7beb2e6356ae93c7b5a41754d9e42c6ef00d4a", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.0.tgz", "fileCount": 5, "unpackedSize": 9061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQ5aoCRA9TVsSAnZWagAAMFEP/jPUEFTL6gjB8gBaIn1l\nWwYMRVRC9ZfJQZD/HLL578FgdI3ib6Ucv70JGMcHysJ8l4U3gmbMipypa/FV\nGotSzzV/8pb00PsGmj/VaQhUiz5Y3J5+d2kq299b0s7x6cJ5FqM5FXPAIRzc\nGxwucLBW917sBMsWbDZpbkPPIum4VBh/JdGx2dt6PwziLK/7K9x/1wF4urPJ\ndxFLtYd8fFTK18o3hJZB4fwVa3gwBrc5J/LycT9Ud26BsSqhj/OhqrDGT6oB\nA7zXJYfFyyKiq5gIdMPpRJQf+POB81PQAGfLnGgr5EleGBJlKlx8H/3PJK+3\nbJd/ZrHASt1bsTQxK4kVX/bBXKyST0yXwiiQYiWNkF/2nn2OP4jcVfpCQbZQ\nNq1K1nfgNZNcHA98uZMFD9Y5B3RPdeKOX3Ax5WDgTJxvzhiUlFh/ITtShzaO\nVY3cJVPRLJe1HHNb2tERbfZfG9k1LueKnFh3Fl2siSNgTvQ/hIXkraxCws3G\nP5R1fZjkixTY/vZVgAKGKOAdFut9unwYRYAxZjTYctHZuq+uwUgAPc/q/GbV\nle0b3C2J5BIuA51fp27QHzBqND8+OnHy9+4PU4WtEC+zLQhUeiZJhc4ohW6n\nzi4707Q1OSgFPOKAxIFFCSUfxNKv9DY6t0yilc0JiTslAFpyMBbFQPnSoOLF\nHn3+\r\n=ATS8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHpRjtN2MaaoUTKATMV3ZjCR6wWPB9c3HwCCFDKMTUNTAiEAlvdype72nIBW948eCtJa+mLdOLr6XlL9ojcEJXXvdD4="}]}, "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_0.2.0_1564710568096_0.5451040558312688"}, "_hasShrinkwrap": false}, "0.2.1": {"name": "dom-serializer", "version": "0.2.1", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "devDependencies": {"cheerio": "^1.0.0-rc.2", "expect.js": "~0.3.1", "htmlparser2": "^3.10.0", "lodash": "^4.17.11", "mocha": "^6.2.0", "xyz": "^3.0.0"}, "scripts": {"test": "mocha test.js"}, "prettier": {"singleQuote": true}, "license": "MIT", "gitHead": "321eebeb82abbd34c29ce8f877e8e74c64fe36ea", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@0.2.1", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-sK3ujri04WyjwQXVoK4PU3y8ula1stq10GJZpqHIUgoGZdsGzAGu65BnU3d08aTVSvO7mGPZUc0wTEDL+qGE0Q==", "shasum": "13650c850daffea35d8b626a4cfc4d3a17643fdb", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.1.tgz", "fileCount": 6, "unpackedSize": 9521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQ5gpCRA9TVsSAnZWagAA5MAP/A9D4bcjGfQs6hUjiIqj\nQ1wA6+yKJZ5zulDx0PXaLfaPlVzMUAbx8+5F46tEy6ywfSJ+EFmfZptclaUL\nTVIAgqVSZakTcJlHPFrB/FkpbuU8BObxwb+qQqTP0SPPI6/Gqfz7dM0D2Vkk\n69rqR6a88wskXkXl/cD355UzAj0MldGVZqIGbd5FO5AAA7f0rDdFIE7Y8r5f\n8xKm7aZI3VD+6kpglX/PXtdv8g3f3FigJiSmSX48b8lh6Ochg5BpuO0PKXDo\nX+ZWeJfuNaUnAwzRMN8IxOhbR3grxE4OkCM/hvzSXj6oaCPtXPzOSV091Srp\nQ3Zmn4Dk93eKmOBGvO0GUVrcQka+5hfH4n/NVWHCqKW5vDQizi7DvESqWyH7\nyJzP4j67RXbncATKNLNoe3QUR1ZDkxaLtJQHFDIvqUJl6pFFHf3fJIA54HUS\naVi3AeGfY9U0BMwbDwQbwUST2Dw3FWsAM6GqjVCuoeQ5WAzTw0AeEN+EJont\nE+wiDrP1VAPBLm2+AzjMKpgS8QpGn8KgfeeP6KxCyntVXL9QOiTVPRQfmD/T\nu9lmFoYE5JWtyhkpo7OTgCmeT8Kw+Rj+EwBZ4zoguwDU3am6NA69Vhi6WnOW\nDisMDaudLyNEEDEE9myyuWyEKVCmUck3VzEBkTWa+ceTrtBqr+sszExj8twc\nZbnY\r\n=cMvp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICUsGs9KR6iF7r0wBzI42u5TMzMv6FAuv0wDR8lOUNqWAiAGTszUpg+ph7UeRlgDlCHgkKoODJnrSLZES7FqLEUSmA=="}]}, "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_0.2.1_1564710953041_0.9348214874583851"}, "_hasShrinkwrap": false}, "0.2.2": {"name": "dom-serializer", "version": "0.2.2", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "./index.js", "dependencies": {"domelementtype": "^2.0.1", "entities": "^2.0.0"}, "devDependencies": {"cheerio": "^1.0.0-rc.2", "expect.js": "~0.3.1", "htmlparser2": "^3.10.0", "lodash": "^4.17.11", "mocha": "^6.2.0", "xyz": "^3.0.0"}, "scripts": {"test": "mocha test.js"}, "prettier": {"singleQuote": true}, "license": "MIT", "gitHead": "1451bf260586d612314820be41b2b89528f01155", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@0.2.2", "_nodeVersion": "12.4.0", "_npmVersion": "6.13.0", "dist": {"integrity": "sha512-2/xPb3ORsQ42nHYiSunXkDjPLBaEj/xTwUO4B7XCZQTRk7EBtTOPaygh10YAAh2OI1Qrp6NWfpAhzswj0ydt9g==", "shasum": "1afb81f533717175d478655debc5e332d9f9bb51", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz", "fileCount": 6, "unpackedSize": 9519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdx0J+CRA9TVsSAnZWagAA9soP/3q16Xe/nwKzZIRrty52\nq1SjnP4U6E0vC1QVD918cJNgcISe2DgXZzY53UiXi7G45YQKTrCFgk/PMNcP\nrYT42oK9CAOPmU9NEEzg1c5ZubtOV8fdCUh39pnZi/jwRL+FH2jlPQZwtCsa\nV/WGdSXHZ7+aKmeDvrGTHXE5MuVGgaWxjA5cBcS4pZhrUqtTyVdBkCgRiv8/\nTlziW63qb7jE1pLZ7mjw++F82xvEWI/kAncD3BEFQ3uwU5XJOeFFKvGmbCx1\nRvXWvJtQbcduFbRIBltPcvhud721p9tMbM4iWSX8XQPQmr7lnUJbXT+mE6rA\n9d3gMsRljoOG4IvDQe7eiUs6TJe8qmozOoZKCqbU9ZhVgNqSrieFO9iCQv//\nMuG8T+1ZaxDz95xzkbeg6SebObku6ykoStrka9ogT7LhWQ7fDAh8dJYKwOpI\nYs1JUEf4XjskgyKdtUtR0sibCfSwbG+wdCKKFh5cgcxVNijPkgn5FSxhvpK5\nnVQGqmKoYT02bN8dMB6RJCsoLyhyappHLIU75bOnh8BsW/Y+LVqmtHGmdGEQ\n/vdO+Zq5jhzGv6KaCTLJ7EEGiUGOS70w+R5DCeJtsbs6sy9GOrt+MV9yMmXa\nfWoMVKAy6kO1qdXS+LjW4iOYFOZlJsZn2eYYohH4qZ5YnDfG0Hb2E1ME/BKo\ne/As\r\n=auXY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICrUgp3AWFO55OFzW3jTeHZrsHkIwfl6v/22eGX+MEvCAiEA6UtI7z7R4JlKn3I5oRoXdjX4jFFEfCbOhyYKucbYnEo="}]}, "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_0.2.2_1573339774440_0.8054830648355711"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "dom-serializer", "version": "1.0.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^3.0.0", "entities": "^2.0.0"}, "devDependencies": {"@types/cheerio": "^0.22.18", "@types/jest": "^25.1.4", "@types/node": "^13.9.1", "@typescript-eslint/eslint-plugin": "^2.31.0", "@typescript-eslint/parser": "^2.31.0", "cheerio": "^1.0.0-rc.2", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "~4.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^25.3.1", "typescript": "^3.5.3"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint --ext=js,ts src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "license": "MIT", "gitHead": "7f406252f413566972175318ab8f46b1549f51ef", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.0.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-ardGwTHT5fU4RJW+/o/Gym8ioyUvlj19YrwryeQUVsyfVe+dZ07B9qmjqZdNomrQbUkjT0AGYEHJr1yfduugKw==", "shasum": "7ad5031be731a95673787e94567d216b83973eab", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.0.0.tgz", "fileCount": 12, "unpackedSize": 22588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetzPfCRA9TVsSAnZWagAA86EP/3KX1sTOQ8BKBg6vaGRw\n44QiqUTYq9sgaMD6WHkA+1wLr6CL2L6bl3cffTNC9cxFtGD3HnJ0iIoQeL5e\nLzcMcLlQMP0v3jLVNgOi4pHTkI32kVXLobSblnkqZvGsxoiza795O/S/7lq2\no37Txu24Y076EfJ8JynrvtwiR3uBS8iz1geRaB4fxRMkYlVdPkJnxlST9U4N\nKiMsOYTEoveKfrmNSbLVWqPM4Asppp1geOcWx6PVpieDD9N+ii7axq8t1wOi\nvmxIjbCwIwlVeEYlbeQh5abNniNOiyiAJtveeBRDdu3loYNjSXLu6Kfn0Zxz\ncrqP89IxSlNo+eEeWvrQicGoyJlP51YjAiNZOJYO/VztKNlTeSOfItO45+2F\n36Zlzj+WnNncVE1VtmvDSkUFeqVfdko28ZzsFwrgBXa2jsjuMbMiaBASwWBc\nmS3lArGmvbGaHjtDBDgdv+2qp7KSME89uv0LRrkpvMUA+oOouF0WFSWTD646\ntaPg8Nc8elfuv/LEbM4qaapOLhP9ZYb3obIKAI1zgapULd+rCObRxPlybzao\nKlCpoyaubs5yJQ0yRdzK6fc6gx++vwcJurqPUEGhFm4crWXKrt3EOzscooHh\nPyEMRyYmNU5THA3vcSkYAlmTtvqcSNzfD+PQ/WOzY1sZFUfIYpdGHwHQCfV1\nt6fA\r\n=7oqx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFQpAVk+oTgkmqHWckylqm6lxPlvhyKDxYhywNNKyr0lAiAY4PlLfI4RLGomqOUtqz7ifSJQ/Qemnrvk/d+TJqfObA=="}]}, "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.0.0_1589064670820_0.9231405736157936"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "dom-serializer", "version": "1.0.1", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^3.0.0", "entities": "^2.0.0"}, "devDependencies": {"@types/cheerio": "^0.22.18", "@types/jest": "^25.1.4", "@types/node": "^13.9.1", "@typescript-eslint/eslint-plugin": "^2.31.0", "@typescript-eslint/parser": "^2.31.0", "cheerio": "^1.0.0-rc.2", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "~4.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^25.3.1", "typescript": "^3.5.3"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint --ext=js,ts src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "license": "MIT", "gitHead": "06b0a79d224fdea75c2485e9930d1e7bcfdb94a1", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.0.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-1Aj1Qy3YLbdslkI75QEOfdp9TkQ3o8LRISAzxOibjBs/xWwr1WxZFOQphFkZuepHFGo+kB8e5FVJSS0faAJ4Rw==", "shasum": "79695eb49af3cd8abc8d93a73da382deb1ca0795", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.0.1.tgz", "fileCount": 12, "unpackedSize": 22424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetztMCRA9TVsSAnZWagAAThQP/17M1vDPTy2l7s2wZh35\ngws8K0wbkAITFOn1LThe0KHxOUcBtyhhbPkh/8kPRKUp0vQScquRveBbsPWH\noz82tZs4wlawHkeSLEwBBMl1lNzfThkyyEEA2RlpN85xPZ6UgBBof041h7U7\n+rzoC8Wf9yMT5Lx0hr+YWq7M4v/KEL3UaFMOHDasDagJhyBpEC1Kj7qZzfO1\nSHjL9PE6wd/kuHsb1ScmC146QoZE2Sv28NCwWJ1oUfp2NlTnJoDKbB6RZGys\nqJ2Fh83hGoR2DN8uem6E6D41ft3uCwnJ2WJ58qEH0UABq88dZMwwMq430NuB\n4l4NFlhqbIOT4op7S5gyA05cJBPU22hzc7VD++I15EYSONxUEs6Ct5H20FFC\nV5KtxR+Q/6VS400tICcHEcO+G580OIxIq7gXJdG2xhHUn0byUz6g1L79sulP\n1KqeWhPckoGQU1kSbzTslQ+AVFx7crHGDgDFEleOlFEWYNL5AYFo1KIx2sdZ\nmyDMoMgJV/C13/S3uOaNMqoiqdC+AywHzFaYLbts4nFXEssucLxOmKeeKR8E\n+eANCaSF/nHbVRrVeCJmw726RFDxn9s2u0WBG36k2MFJgT6F1vLX8rzRjlTn\naEBmQkMNKWQKIJAs314o9fQM3SbyH02GChNrsgg474aJTr93mAL701VbBvZE\nAP4j\r\n=ORUG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFgA/4qpycqHhU/oMNjvr7DB7SAPN3yFpHuZyNsSQloSAiEA5U1uiUA480EC2e++4t7MccMos6ecH6EOCrYym2coh74="}]}, "maintainers": [{"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.0.1_1589066571854_0.8762503221850786"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "dom-serializer", "version": "1.1.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^3.0.0", "entities": "^2.0.0"}, "devDependencies": {"@types/cheerio": "^0.22.18", "@types/jest": "^26.0.3", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio": "^1.0.0-rc.2", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "~4.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "d5c991582a8ca0d84341441d8e4fcb252248f325", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.1.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-ox7bvGXt2n+uLWtCRLybYx60IrOlWL/aCebWJk1T0d4m3y2tzf4U3ij9wBMUb6YJZpz06HCCYuyCDveE2xXmzQ==", "shasum": "5f7c828f1bfc44887dc2a315ab5c45691d544b58", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.1.0.tgz", "fileCount": 12, "unpackedSize": 23137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXn19CRA9TVsSAnZWagAA5coP/RHORLBkn3kDmy/KjyG9\nYSbENHzS00lU4A2OD9c6A2WDVkeXheL/e3+FnmBlXvXkowZWqrBJ0a+Jc5eq\nFwa886Ie2kmnL1nTKQEgC8ApKvvdMInyaBmkW9TnZucBr85M8vBJCmOPFxzs\nOPjmGJ94xiK90R6aLznKT3mas/Z3P0/ZAzv7m3hd/61p9DGIg6VOjgKEwkDd\nQP85uKOm8ZVvgTBHS1/xI2DuKQuzNPhllUx84WCDKoE+kp4fjuE5kAE0T+EW\nQ54HpeH9tyfbR/qhgaPBW/NKW7cPXdk8IB3j/KVqE/j3CF7kK/r8QKBzeYTl\nZTh7DdhW9N/QrsgllvJdsic/5csbb4Uvvz4s1JAQ+gpT/wEjqy00+ZwPiQ2T\nspcEGDm/hQ8HQfSlHdYytkG0tra0qYDuYkdAFSbyms/LMgXWCB4uePWzsQrg\nI0OqvgbYZsbwcV8y6Tc6Yoppe7NGjzFb2NObh+03H27DQzYk2IUK+3Vgf2ZF\nt98DbvDijaynTUo25u1fEoDrzDj9GWo7Vh87iPuwKmI8eZzGKi4ckzOc525H\nuJ+MPGHRxC/+m1Y+GFqMNGBMfxrJbS3IHUHgC2YFyMIqsjg7TUcgtdIpeV0L\nvgCyKuhFdygaEotBhuMAWRZXh2OtnqF6ZW/Bqg0Mm3AhB72epzz/NkFQIrk9\nkNiD\r\n=TwNb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRsI6tFeGfgkgnQZA02DNKcQGnsKqPV41ebkzK+TqVGQIgHCr6RiPElXxhyaek5Ueg1xR0YmcvE2jvl6+uTU0nWq0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.1.0_1600028028732_0.003768259291941467"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "dom-serializer", "version": "1.2.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "entities": "^2.0.0"}, "devDependencies": {"@types/cheerio": "^0.22.18", "@types/jest": "^26.0.3", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio": "^1.0.0-rc.2", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "~5.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "80c5a18794371977a31cc7278e5d3cc22055704b", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.2.0", "_nodeVersion": "15.3.0", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-n6kZFH/KlCrqs/1GHMOd5i2fd/beQHuehKdWvNNffbGHTr/almdhuVvTVFb3V7fglz+nC50fFusu3lY33h12pA==", "shasum": "3433d9136aeb3c627981daa385fc7f32d27c48f1", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.2.0.tgz", "fileCount": 12, "unpackedSize": 23250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy9jECRA9TVsSAnZWagAAQKoP/3x5B1HSU5zLOyV2vi24\nS8fxwug+wvlVfZYF81ww0hYhCoGutZ3mhPXHvi+ECHglJdjRKvJKzLgDxO8c\nksVf9MfEk0e1eWok16jy/6LVnKrt2yHvPy7s0+OVwahea9KnXUurA8DjDBm0\nHaPUHL+flj03I4eJTAlgPh11M7E0g5Op3RPX+hFZnQ3NSepPv2quPkTXBFiU\nby0VGRnJ1+vyURCWkYPNk2FVl8aXw+nW0OIDIwmtql4K/WbBUxlc6N02vbiP\nqiSP3joMs1lgdRj5IrkYnDR/TnCq8SSHva3joLb/WaRajk12M2Osp+9DvB43\nHW0YrQ+l4u89U2nwiHk61BrtBLLscNa3e3u8pe8iVZaZecUc/m0+eOvgMAvk\nQucpyZw7rAYJdpOOOzUTt4xurHSvKB1yjerbZU2GjQI4UMp5OMPshQ0+qwPq\n8DoMkMWnRMMA2uRx5XZN/OqUU1d5smcYgdzSk32nuGnuZlsKlc/gJi0kFAOj\ndNSIlx7Wu0jGhZGKT4OgGVmqWKX4oZ2Nt0l/1Iql17lRG2pF5RAH2MsYxtTc\nBEmR9kcKaKhO7WmdTQkqSSdgzs7OgTGRC4V4Lp4Hem5U+Q7d7rakKQ9n04J7\n51zSmoztNa4Hrw9IGGXaTcbc3Aweslbs1r3qtuCA7hl4IsWZWPc7HezNOGOo\nHTYf\r\n=jKXE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAu4/rXAetiF7xhOweZKRgmMZA2iLfTu2JHlAjYzOlTAAiBEbYofdocO0gys/qqF/4H+8Uq/6W1cG93KwPR+F+V14w=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.2.0_1607194819886_0.6311502704563536"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "dom-serializer", "version": "1.3.0", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "entities": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio": "^1.0.0-rc.5", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "8324028f8491c2146d44f2f87e33de683e49a55c", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.3.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.9.0", "dist": {"integrity": "sha512-1p5P1tu8dgmLAgRMpk8xkYirLYmy/EuWQ/7P3rJBVBIEd6nAQDfeCXIF5GbUdetf4o1Q1DNsc27F7VSWmJiP8g==", "shasum": "69b5877b7a7dd1b4d9516671a155bca66b8d9e71", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.0.tgz", "fileCount": 9, "unpackedSize": 14032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgb1GyCRA9TVsSAnZWagAAAvIQAJqTRr7gjK0ZU7MZ9r7h\n6m3Fw8WZD8CIobzjh+eXWhUbSeVa4R1ksOq5knTKW6o+nq/yC3eEDZNfSFPb\n3NTB5VD6s8BVEEb5hXA6ntbr6PplFaCUZqwQtAtH/uj0hMbvhNrFE8SNXvb9\n+FOM2TSh73Rh8LwTkCdB9b9R2kUKgLVTP0PaV+limbq46Twtzu5uMhZLeLtv\nptLefM42tlK7DFjGthSOSH8vOkwZooEM5wP6klyfhdxI18QkhEPHMUO+hEs4\n1CpSm7TP/nYW6nOI2oSTK5jErUly7G0CE5+MVlFoVUHVQFAesv0YIOv4ime6\nnMK789Gwmz4tKM8k0NjfnqZAxu+HRv7sN2H9ohc4Xe+jXt/Doh9gpq543pXJ\nMoilFds8TugHoThikRLJVjOaWUaRkTeA1NMuKRQ5sgrcQb5HjgdEjeaCv3/b\nzg8GHJyks7OqOk+lfeGP+pOV2fYg3PbWBlwCB8YcmhAS18jbvx08qffa8Rv6\nVQx31viUXLKo4bzFEOnE5pg2UFl+iTJ/eA5WNGUYVXn8OulnpOeZjY4ybDKj\ngt+yzrny6Bt+r1z+KyMVcNplQAHtLoMWJMgNyZhsMLvj119HsSK6XwdQGmid\nUBpuZfzFjlPz/SKrDx33jiI/0+Ri0hYxgw4t5KZA6jMHbSPCUOMKRQ/44UOv\nq5UZ\r\n=ihQz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3F5Hla/fY9At56W9Zd9einkSes6yT3+PHR3b08eJaHwIhAPIH8Wraos49l4QvLfFJyeXCc+/vd/W4EkL8mYkwPk3u"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.3.0_1617908145322_0.2997037594412644"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "dom-serializer", "version": "1.3.1", "description": "render dom nodes to string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "entities": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.3", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "cheerio": "^1.0.0-rc.5", "coveralls": "^3.0.5", "eslint": "^7.0.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "a8edae8a8d2123f263e024fcc613d3cbc8807a33", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.3.1", "_nodeVersion": "15.11.0", "_npmVersion": "7.9.0", "dist": {"integrity": "sha512-Pv2ZluG5ife96udGgEDovOOOA5UELkltfJpnIExPrAk1LTvecolUGn6lIaoLh86d83GiB86CjzciMd9BuRB71Q==", "shasum": "d845a1565d7c041a95e5dab62184ab41e3a519be", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.1.tgz", "fileCount": 9, "unpackedSize": 17057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgb11yCRA9TVsSAnZWagAAyScP/3i1z6pU+/+Hh9TOfd0K\n8XMGh/0wRjaaEK7c8OMUf13+BS6bccoHyFTruDUL6PSfHKHbMcnAT/ggk6Xz\nRjldcg26DAble5I4SCcNaa384LwdBZ9s1CRGIEqY+SxuwCpuOPoFOeVeKGUB\nYbsVUl7Ww+Gfp+zEA3ldbUxILvB84Jl1OoZreayrBc0W7rXPH8z9s56XLwWy\nfFKm0oe31m9DkOGjpHqxnvuBik6OZwAgmgECwaXmZrlk5eGl6FLQcY7/FPtp\nUMQh5Q8NYtmDoYND4xj2SDrv2i3RdND0cpnY3z4jO2aTpcFc4dwd136ZZ4ba\n/2+Ne3Gcws37oRyfXMuiBp93K7I80gV2SmFSu3jJziEP200yR7Sio5xwiWMn\nfQywU53bdQtRfbmz5G+hRsdN9NY1yi32fv7Ml71Ks2vZ+0kdaI9uG0v/vm+O\nM7f6GQR1cmOCqNyeGjv73U9weC2+yRPR7Jgv9FhkGe+8rInMKp+6nXPivT8j\nkhUSosQ4tTC4jnwAWZXwn9eBBJmnMS9YRBNyWv2LAqUAxx6GJzVM3uHEuUFL\nkKY3q7DpC4jPvBGlxeg7YUn2fKWXYDWpNoNhPseDx1FjTdgqCCA2qHpZ5zx8\nq7FU+oM/V1ps2noOioaE3roQGX7QhEoXqSx5GJ+OHMWY1EW8L3boldeLkMQA\npEoI\r\n=bofs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHiwBwMd1TsapKxsAcMr9BuFvTetL5jUUuoE7bTLks6WAiEAhH+Lnb4i62nGE6RsKsrP5+fFqC6GTiOZALy7n258LkQ="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.3.1_1617911153541_0.7678794205493724"}, "_hasShrinkwrap": false}, "1.3.2": {"name": "dom-serializer", "version": "1.3.2", "description": "render domhandler DOM nodes to a string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.23", "@types/node": "^15.3.0", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "cheerio": "^1.0.0-rc.9", "coveralls": "^3.0.5", "eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "htmlparser2": "^6.1.0", "jest": "^26.0.1", "prettier": "^2.3.0", "ts-jest": "^26.5.6", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "d8a06cb8af6888e315d1dfbe432388473a2c57bc", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.3.2", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-5c54Bk5Dw4qAxNOI1pFEizPSjVsx5+bpJKmL2kPn8JhBUq2q09tTCa3mjijun2NfK78NMouDYNMBkOrPZiS+ig==", "shasum": "6206437d32ceefaec7161803230c7a20bc1b4d91", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.3.2.tgz", "fileCount": 9, "unpackedSize": 18131, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgompkCRA9TVsSAnZWagAASmkP/28FpHU5FA5opzp/R/Xz\nAhxW4x3BCQXh9E+5gmbBuxzxZls5NlChGPKVJR1xCgUD38WEQj/ftazn8GyY\nlTAJWm6Iy4VaUxKcFXc+hfQeha96veetos7C2DRPQ/uL0HA07R5D/1+LbdAQ\n0Q2etXG+O8k0P8sL+rXz9u6I21/kL+XdkD3+PTAfnv5TV5XDKjTC/Y6IPGK7\nkO3EYjgb02QsLcHHpAcCJOAVrOAcvr03N37Zfv3ayQM6HGmOFMDFUBmKkcIf\nwMSzBGW5ehVFBeeP/Rdc5qzTDE26JLRnCgiqxaquxXKCjF6aZbIRRMLLGE9t\n+coaNHvL3cN+4/1E9ipO3cpK6g8TJUy6KxZubiKc7kWQgWGbkSxfIGzICnvU\nsVNPGFIuvyGtBKLRnxoYFfbUzcbU6tazEIw5+3xLFJ1YamKp1Y3ft/xaJWmq\nor2xMNqNSKPHCAfELyUizx/a0m1Exbhj6F4A1foeP80TUNsW5J7w0Z3S1lwJ\npi08HovNpj0WfbXS+QwduL+UPtAWuQ25mnkfAP+ukosSwJK5a+jHamAMgNuz\neXE2fqRG8cvREXo+8xwVylsKO5Nu8KneMZ0wCX1YRH6tVIya2DldBEnSDmXK\nrq0JvgTIfrPbIttkND6Ic4HIvMJ779+OYaIEXY0+cDGxD4a48Om5pZZBFoZ4\nwlEi\r\n=Ight\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgzDwqILl0Xmy8nlrPfzKY4doksxGzNLWYjxEzaTgxFwIhAJDx996sj8YTTZJWZoVOq3QG5eh3g/MBMmptWBks8Bfg"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.3.2_1621256803871_0.9788975367977142"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "dom-serializer", "version": "1.4.0", "description": "render domhandler DOM nodes to a string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-serializer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "cheerio": "^1.0.0-rc.9", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "e6e269593cb0f28f0d5ba5d63e487324b0d1837d", "bugs": {"url": "https://github.com/cheeriojs/dom-serializer/issues"}, "homepage": "https://github.com/cheeriojs/dom-serializer#readme", "_id": "dom-serializer@1.4.0", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-rGKtTZYjECBmq+POE/5pmpG7SoAN/rXD7OETipfmVUsgPhC4B9069cgyADMA5SARCX7a35JhWtxivqR7UofIvw==", "shasum": "15440f190d074a72d29d2f70ddab1286a27be5ac", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.0.tgz", "fileCount": 16, "unpackedSize": 28813, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDuTdTadgys/LjRmcpWhH+ajS+v67XufVDd8gypNsrfZAiEA1wAGxCti1c1bcE5r3NbT1kjQRKIXrfn2VVW4QixBkPI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUbzUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfFw/9F39f7PqGR4PEbPMAH4Mdg+ujqMeA6LsB1IxBV9EMpqtbOPpX\r\n43KRm5hUo9oxx8TBWib9/t13mCYBVWqNdNFpSQ+su581SewpvBvDgcWNzvdR\r\nPeQq96YnLoPeyQPGgOyb0q3f8WB4okEHt3NnBerUCk+Kg7V2ezQur8KSK75q\r\n4lC3l3iyrK5GIx2cFlmv++l0g3JJKK05LcCO79BdHkQ3MdRlYVRrvtyNVSbM\r\n/qttstGC3DKtweF1s1hyYYLki0+qeGhT+ILfPIJ8I5bNyL/JDN0S7cr1k1Dj\r\nlmx05fqcqgz2Xz1a9jqANoppAnNzRDEmr5s1nZXOzN8P6BJ9zAjinX39zMKq\r\nA7dFRq1nIGlB+ELTCDbokjzgIdPTl+AKUX52jLUTpkDfaq74mym+fU71ywTb\r\nINggK1OOmMo8HxqmzKBLjIYpxz34q4c+9dsx6PdfdlTXM+BxZYSZpjcY82w1\r\nEV3aZ17DOelDyZjaFV6ZwqhswXfefLhFZhMZLSoHzKWdyRKR1kgkLflJPLgh\r\nsbD9EApIwN98VylJ+/yPOX5yacNH1NaspR0G3qnIRkLmpOfyB2GMox15uMuY\r\n12bM7X/TIO6yY2dpBYSplCRQZ0np4sxG5KnHVWSz0YfXP8Jpb80eCt2P+XKj\r\nbkH+Wyjg0wqc1ozu2O1RJwG3tJZnM2Y5C14=\r\n=TDcP\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.4.0_1649523924034_0.06470525369226876"}, "_hasShrinkwrap": false}, "1.4.1": {"name": "dom-serializer", "version": "1.4.1", "description": "render domhandler DOM nodes to a string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-renderer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "devDependencies": {"@types/jest": "^26.0.23", "@types/node": "^15.3.0", "@typescript-eslint/eslint-plugin": "^4.23.0", "@typescript-eslint/parser": "^4.23.0", "cheerio": "^1.0.0-rc.9", "coveralls": "^3.0.5", "eslint": "^7.26.0", "eslint-config-prettier": "^8.3.0", "htmlparser2": "^6.1.0", "jest": "^26.0.1", "prettier": "^2.3.0", "ts-jest": "^26.5.6", "typescript": "^4.0.2"}, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "5cf993992c1f01c56dcae231119b1479788671b9", "bugs": {"url": "https://github.com/cheeriojs/dom-renderer/issues"}, "homepage": "https://github.com/cheeriojs/dom-renderer#readme", "_id": "dom-serializer@1.4.1", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==", "shasum": "de5d41b1aea290215dc45a6dae8adcf1d32e2d30", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz", "fileCount": 16, "unpackedSize": 28312, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFYKsHTmADNrx5t2mIH7BjcRBnegi1IrYhW4aDvZgOPSAiA6jU3T+H7GrHnZYfOD3zY+CESToceeWNOrhXVmZCkasw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUcAjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3kQ/9GN2dyWtEIGmgO5aaJjZD0D+IXZnlG5qnzYjN5AgQqD2DzaIV\r\nd4bsjCtV+IeO6dCP8eSDNamkilSHLvinckgTPo/QzON49rGqFnNTgmzP4HEz\r\nr5h9pYR2uhzb0sj0iZ3vUZw4RXOYo4H6GTo2u7qkh87c00aGbBK/FFW1ntJ0\r\noVATpB+tzCREuBl2Cxyw1NnLbPVS15hoOX9gOSkSsd9PEHSW3ggebE3ew0CR\r\nspJeCoWSPN/Rf2DdeX2uGYPLg6WHeCpj+0MLX2dIwdUiDUAZ6M1jj/hrjtql\r\nZ7iMyOYX5fYtD8uaC294lCH8SbxL+vsKdL0V85/JD/R7tjI4PHclM38wLSyy\r\nDKBRROJyaJCK0fl9f2jmIYhvQzThxMiTl/IabU+/TDbQ7TqvfIBAeHgGDOZy\r\nBtiMI+irM+vk4LCUMYjNqYx5WlhnP7/MVcCaWEZuraSdMIR5GyDPlstHOXu6\r\nnWD7D+7KMerxbamRl/c6VJpgSg3er6iYpN9iHWckvyznp4MfaoD5n7dgNkC3\r\nEwOz7L48grwGbOtwq7lJcOgc9lCkBRuwUCeGgwlYSnREdQuuzoPV6KKjvPa6\r\nW6ItvsiqepHt64deoxl7LShvId7Od6ghY/KdHUyiMjjNLSpHRmApaNlFPX25\r\n0e00egeFYP+hpV+YQTV4ANO9QUE6MNLiOmE=\r\n=ff9b\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_1.4.1_1649524771723_0.782564599652648"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "dom-serializer", "version": "2.0.0", "description": "render domhandler DOM nodes to a string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "sideEffects": false, "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-serializer.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "cheerio": "^1.0.0-rc.9", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "license": "MIT", "gitHead": "add044a9615207b80a389b44619e2ad4cc4b8cae", "bugs": {"url": "https://github.com/cheeriojs/dom-serializer/issues"}, "homepage": "https://github.com/cheeriojs/dom-serializer#readme", "_id": "dom-serializer@2.0.0", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==", "shasum": "e41b802e1eedf9f6cae183ce5e622d789d7d8e53", "tarball": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-2.0.0.tgz", "fileCount": 16, "unpackedSize": 28813, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICiA31uTgHBNtLV9aaXHs5I2hmgK6n4WVZPWyzKgz8iKAiAhU+FG5S+wS8GhI5m8WIdC1ipreifFh7AIoPquR70FAA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUcCsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrX9Q//XIDHTlKB+WVODBq0i2ZjA7l96Ej07eX3SUaj4pg+6A5F2fBB\r\nxi3uZiZLqNnow2mLBRXce7KQe9O96KG+sn258KGFlvetJNMaCn34AIrGPQ3G\r\naMMBS7XsYs4HmzAlNtfWYUfqM9gRQu6kl0Vo4dvEZJvvPvRBcz39VBhx2Ros\r\n7bEov+bgRirWwGvFXKWX/dJ1yqPN5owFVZl95QHQMvfcMwGqvRmF9jE759xv\r\nkoVIFB7qqPt+pKNzEyiDl6MCvX4TET7cyfhL2aRQEb4x6pWkwzAeG9L3v9Cc\r\nOxn0+enTB4hiZhM8cIfdhI5spnNp90itQJJtfjWrwjcsgseEOgNotvlMHBPj\r\nVMI7srbQO3Y7drraCqM4TP/e4F1NYTOWkAoS+UCJyDqB9AjmhLqYnluZM0cM\r\n3x7VCusQ1zR/JYCq/w+rqkVWcoAPipkh736dWg/8F/vGTHHVcxpzHEQCzuxY\r\nBgTPaHa57I7uA+q6zY4Z+8jHqzWNrey1WHfTeBEhWhHqUn8UBnWhDbGtcABv\r\neHdsUXnd+Oo6fZnxXotj1t1PUN00UMPeIND/9tzxjlabmzqJ/m2RosOgEpM9\r\nnlirTZnV4El3drN/f0KG8kSPIIP+8H3aRoxTnoTXNiMqcWmulTBj7CrWjejQ\r\npdt4s4+UFvvVJFkHZWuIUwNs3jfU7NpILlU=\r\n=g78A\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "david<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/dom-serializer_2.0.0_1649524908310_0.5316488052499551"}, "_hasShrinkwrap": false}}, "readme": "# dom-serializer [![Build Status](https://travis-ci.com/cheeriojs/dom-serializer.svg?branch=master)](https://travis-ci.com/cheeriojs/dom-serializer)\n\nRenders a [domhandler](https://github.com/fb55/domhandler) DOM node or an array of domhandler DOM nodes to a string.\n\n```js\nimport render from \"dom-serializer\";\n\n// OR\n\nconst render = require(\"dom-serializer\").default;\n```\n\n# API\n\n## `render`\n\n▸ **render**(`node`: Node \\| Node[], `options?`: [_Options_](#Options)): _string_\n\nRenders a DOM node or an array of DOM nodes to a string.\n\nCan be thought of as the equivalent of the `outerHTML` of the passed node(s).\n\n#### Parameters:\n\n| Name      | Type                               | Default value | Description                    |\n| :-------- | :--------------------------------- | :------------ | :----------------------------- |\n| `node`    | Node \\| Node[]                     | -             | Node to be rendered.           |\n| `options` | [_DomSerializerOptions_](#Options) | {}            | Changes serialization behavior |\n\n**Returns:** _string_\n\n## Options\n\n### `encodeEntities`\n\n• `Optional` **decodeEntities**: _boolean | \"utf8\"_\n\nEncode characters that are either reserved in HTML or XML.\n\nIf `xmlMode` is `true` or the value not `'utf8'`, characters outside of the utf8 range will be encoded as well.\n\n**`default`** `decodeEntities`\n\n---\n\n### `decodeEntities`\n\n• `Optional` **decodeEntities**: _boolean_\n\nOption inherited from parsing; will be used as the default value for `encodeEntities`.\n\n**`default`** true\n\n---\n\n### `emptyAttrs`\n\n• `Optional` **emptyAttrs**: _boolean_\n\nPrint an empty attribute's value.\n\n**`default`** xmlMode\n\n**`example`** With <code>emptyAttrs: false</code>: <code>&lt;input checked&gt;</code>\n\n**`example`** With <code>emptyAttrs: true</code>: <code>&lt;input checked=\"\"&gt;</code>\n\n---\n\n### `selfClosingTags`\n\n• `Optional` **selfClosingTags**: _boolean_\n\nPrint self-closing tags for tags without contents.\n\n**`default`** xmlMode\n\n**`example`** With <code>selfClosingTags: false</code>: <code>&lt;foo&gt;&lt;/foo&gt;</code>\n\n**`example`** With <code>selfClosingTags: true</code>: <code>&lt;foo /&gt;</code>\n\n---\n\n### `xmlMode`\n\n• `Optional` **xmlMode**: _boolean_ \\| _\"foreign\"_\n\nTreat the input as an XML document; enables the `emptyAttrs` and `selfClosingTags` options.\n\nIf the value is `\"foreign\"`, it will try to correct mixed-case attribute names.\n\n**`default`** false\n\n---\n\n## Ecosystem\n\n| Name                                                          | Description                                             |\n| ------------------------------------------------------------- | ------------------------------------------------------- |\n| [htmlparser2](https://github.com/fb55/htmlparser2)            | Fast & forgiving HTML/XML parser                        |\n| [domhandler](https://github.com/fb55/domhandler)              | Handler for htmlparser2 that turns documents into a DOM |\n| [domutils](https://github.com/fb55/domutils)                  | Utilities for working with domhandler's DOM             |\n| [css-select](https://github.com/fb55/css-select)              | CSS selector engine, compatible with domhandler's DOM   |\n| [cheerio](https://github.com/cheeriojs/cheerio)               | The jQuery API for domhandler's DOM                     |\n| [dom-serializer](https://github.com/cheeriojs/dom-serializer) | Serializer for domhandler's DOM                         |\n\n---\n\nLICENSE: MIT\n", "maintainers": [{"email": "<EMAIL>", "name": "feedic"}], "time": {"modified": "2022-12-16T10:28:35.783Z", "created": "2014-05-20T13:20:43.000Z", "0.0.0": "2014-05-20T13:20:43.000Z", "0.0.1": "2014-06-03T17:14:23.674Z", "0.1.0": "2015-02-11T22:37:38.358Z", "0.1.1": "2019-02-14T18:35:24.541Z", "0.2.0": "2019-08-02T01:49:28.252Z", "0.2.1": "2019-08-02T01:55:53.155Z", "0.2.2": "2019-11-09T22:49:34.548Z", "1.0.0": "2020-05-09T22:51:10.992Z", "1.0.1": "2020-05-09T23:22:52.025Z", "1.1.0": "2020-09-13T20:13:48.831Z", "1.2.0": "2020-12-05T19:00:20.031Z", "1.3.0": "2021-04-08T18:55:45.719Z", "1.3.1": "2021-04-08T19:45:53.695Z", "1.3.2": "2021-05-17T13:06:44.016Z", "1.4.0": "2022-04-09T17:05:24.224Z", "1.4.1": "2022-04-09T17:19:31.927Z", "2.0.0": "2022-04-09T17:21:48.457Z"}, "homepage": "https://github.com/cheeriojs/dom-serializer#readme", "keywords": ["html", "xml", "render"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-serializer.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cheeriojs/dom-serializer/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"mojaray2k": true, "shuoshubao": true}}