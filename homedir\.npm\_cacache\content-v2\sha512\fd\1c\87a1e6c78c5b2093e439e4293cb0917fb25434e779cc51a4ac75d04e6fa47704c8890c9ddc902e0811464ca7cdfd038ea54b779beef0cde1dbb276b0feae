{"name": "@jridgewell/sourcemap-codec", "dist-tags": {"latest": "1.5.0", "beta": "1.4.16-beta.0"}, "versions": {"1.4.9": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.9", "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "6963babca1e1b8a8dc1c379bd4bd2bf9c21c356a", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.9.tgz", "fileCount": 9, "integrity": "sha512-iKsUDLGOrC5pSdVTyb8zJI/f55wItTzGtfGWiWPWTc8h2P4oucax7XOGSRq9V2aA1nwE8qMaGvwdXk3PZRtgjg==", "signatures": [{"sig": "MEUCIQCOb9yMqcfn9MO0xXY06eyS4OwrGx+73fvmbGnAzUOEtwIgCYnnRfjPdp1HfvQ6S+tgyyEQeekZRMlHLtuhFrmTmiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/gJHCRA9TVsSAnZWagAAMDAP/3IJYi21SNJ8AtP6iMzt\nuWZ5MOYxa2Ozzpk6OTSDZDtsSu6ecfLzL5s1gHoYdYIRQoU+4jyHUi4Aa5Ue\nkaEVEsfrq0QclgniIxGYM8PycY+YXXfnFV4IUlYWEUuUBe37uxb5q0z4y1qn\n7KOFsojHATLhB2Z/DXdwCx3NAUsAfu4KHfm+4dftxKiz4bAXujoCDmYYRvYu\n1M+lxaA4Y45Vij1z5HMsXFqRF/Wp4TCbYH8ZxR84jmiJWO7rhUVgsuRl8fq2\nzFYO3H147C3bFc6N22UfM9A/72UXueUi9EGsi57x+UZRb1gE0mZKwROJ2Hde\neB3uPYc+5TFn5YyxSyHfNM1qZWyMk0gHRMglwOiH0nC0HPQ9lfZhV+MFJiaG\nceSaSsG6F0rlj4VESCDuDRa5H9+NEXau/PGG0vEvFCi+wEkdtWnvVJ0U++xz\ntSIxI/MKG3fOTFx8bcA8JUeWMaZxUSR+KphybtVU8mKQc1+tzkFf8OWInDE0\nXKp8vYPbVvdkZeQMF2MzpCwD+/IxH05ZoJamzrMxBHZHymPZe8gLmtPI1N16\nct3bFh8tK5ZdCNs5roJt7hohb7tMSN9HhcLckAm6Rccl4alGynsnZQ/HQboi\nSIv6Tbatlh3jbrbz+bN3wskMvAK5XaMJjPcl8Nf4Yly4BQbmSde0A0tJkc2P\nsWVM\r\n=Uw7z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.10": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.10", "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "baf57b4e2a690d4f38560171f91783656b7f8186", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.10.tgz", "fileCount": 8, "integrity": "sha512-Ht8wIW5v165atIX1p+JvKR5ONzUyF4Ac8DZIQ5kZs9zrb6M8SJNXpx1zn04rn65VjBMygRoMXcyYwNK0fT7bEg==", "signatures": [{"sig": "MEUCICrs6smLQTjkwTfQMeyeeXBjsQnpMGbNNr9rwEHeusW/AiEAzDW2dhfMep9twXLtzn4lQ+zMntZezbs+a20xbBj7gcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/mDiCRA9TVsSAnZWagAAcVcP/1BMO380a1QMToFhod5y\nF5WCHq6hjABJLkOU8c0K+Af+Ps8KzXl0gWtzH6omeLLU5iDo+D8mbWO9w8/C\nGhpZcbMJGK5ck+iBq21ApP7B5U7fv2/c2OXlLvHJXgE7X4uq+lPz6ppRKRho\nbnpj8NPQJeffDLhWu9D46OiM2MkvI3j9XSSbmqR9+r6GNxCAU3NiDpDb3pDJ\n59sPr3YDUaiCk5iIZs6gjXWaePsO/eipIxLEjlOONzfKMRVz8SRHOgajFjrV\njU047Qr+CYnvFjAV0xGkLszpPHMiw2v92uXVo2jNO0Wfjb2CAMC/O4gDUQjz\nOSWWE3di1zC5vjZX+A4bGe5FG8DUfG+GoAh5Zxq573qKwp+hB1Eg0WbbhmJK\nBUVksyX57fROaoiJ6PJ2Tw/eJd0XlhiAjclEce1Wx0vYiFJsXG0+8rgG/iak\nF31yTtYxDT6yq1seo3Xr04kUYxKKRlvSJxfgjos8EH0N/paha/gkwMohSwVf\noy6tx/XfPGs1PvEClBgbMnFm2sU+RF8FM+dn53FCnV8ZIPt5Wf/DCm6jDuPn\nmqlgLkPiR1Ad4CkxxkurJjcu9KwgJY7mzBrpD75Y4cgqc7mg+ZNgMO2g3Qs+\nRtqrY8BZjMH95nEpaHPvk/1HpzCSPc/nfV0yEaRWlp/0qJywIX3ogNYCEnQh\n1rln\r\n=zPKh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.11": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.11", "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "771a1d8d744eeb71b6adb35808e1a6c7b9b8c8ec", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.11.tgz", "fileCount": 8, "integrity": "sha512-Fg32GrJo61m+VqYSdRSjRXMjQ06j8YIYfcTqndLYVAaHmroZHLJZCydsWBOTDqXS2v+mjxohBWEMfg97GXmYQg==", "signatures": [{"sig": "MEUCIQDVlw8j1iC2BoY5fJeodZLmGRry6e8RFNa5BrBnrod2uwIgAwZyv/Qzqitny7r8l9AQePm51WRrTsFyqdaft/mawa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBiDMCRA9TVsSAnZWagAAcGwP/3IskiJbF70fM9yRsUmY\nu3h6KYfMKlkPU0FLkRkSbZ8xYIZecF9SGgTihOnJguErOFnnM+qlOWX+UeIb\nPkamVxr5quu/CkOMEa15atMwbG1/A0OjsXJn1Gdy1Krv/m+hxKOeBe3AoEA8\nPEmD7JztWHZesnGiFfRc9rG5G0FuQoAAsDgP3EaIXZ6359af6a1MLkQ4HS6u\nvYrhlikfw67B8IdTEcZAWo4JGfb8TtxceO0Bt+XWs0HFWiKNtk0PrblBDgXd\nkIhoA/NkysEzDapJgTeXu6skVOQrajB1AIiYOvWVnuNKs+SR7wOJUDOJ54yN\nv3Ul76m+Hc7ZpFIrF+H/IAZ9unH4dYX4Nva2f5X3trr4iTzgUvanoJ1tm2p6\nfrrW/n45fvzxtPJy62dcAShKnIxTJ80uKPO3DG7Bqf/WwGgOPulJrf6Q+gSZ\nMT1Z8VBgk0UV6TWu7UIwlF8xHgcZ6Hgi8W39Dj+CANcsup9MttxANoBmg2ZT\nEJNhxMYKgq0A7Wod1JzHUlFgwcVEvu9zzX/rr4mKzyAnuNzYTGRFYsBHezo/\nK0lqopxSdEWvzqT4WvsULoJgwm6E+PK90uGkR4XS/3abtXLJqvwsL0OdCsC4\n4sn2SZAdIV12KmFljQzaVedGimtrWo8jhgRS/TvdLeXlxbzKvk/OIXepiQuC\nML1N\r\n=Q+sZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.12": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.12", "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "7ed98f6fa525ffb7c56a2cbecb5f7bb91abd2baf", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.12.tgz", "fileCount": 8, "integrity": "sha512-az/NhpIwP3K33ILr0T2bso+k2E/SLf8Yidd8mHl0n6sCQ4YdyC8qDhZA6kOPDNDBA56ZnIjngVl0U3jREA0BUA==", "signatures": [{"sig": "MEQCIG98xO0zpj7lc/uDTk1BOV5Mcb/H4pYB5cW5oJuSMQJoAiBKOgy2qzdEOuenKSmuA+w/R5QUP63eNM2zDocRRv7Yfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicEmHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY4A/8Cs8OAf5geGv8XIA/piYkF4C1Z1ZI9bKFe1jGD+ULZNiGBSqu\r\nt/H+0q79ZpTXK8LdNLF0FkN2yYQk197i1IVF86q098v5hqrWawqtQ4YrVmYW\r\nTE+QhUngzB7GLHBqzGrjRb+uUN5IwDOLuOTA98z+k9e1P65gFu3jKQo83SU/\r\nhbZ5aIa5FVwws512jb8gR7W68CYnx486v2eDcUpMx8ne1G5xkJfYKOWxxMA6\r\n+Qps2I3mc9nsWDiEBQSjKtfbefk54QMUzc6rgrBHduNlfIBcqSwQnhdahO6U\r\n2EUX8TXFNeePOJgoOswTy1/l62lsBL9kuz4OTjYcjP6Vt3vcgYa5IYt4f6VD\r\nPeehxF373+L9CcvznqVkcFmgWX3EfjjJ5iN0iuk/nkZPCo6NO3Y99gH1Qh7a\r\nNVAE8t/dcg0NbNxniXM2eKS087vuifO9eZJCWvjve1Qp4oTrEXTWXAqphLe6\r\nENX5irxVLhKj2KmalsJ0vr3HYc9R9t79cs6SsMTMAbsMznOmFV7ZoRE1Qv94\r\n3rEGVNzHQudCk/AEmE8lOoXB4RP4oRz38C26aRZT7Nfn3xO1e0q47nHVh0Og\r\nAsYeR0SQdn2/6gypfL+bxu/ZJpvWNbw8wno4s4DZEnAwJkJcZb2yTm5TDvXd\r\nyUTgbkk5vB6Q2/XzxOSd4gCeVdmg6qrlyPM=\r\n=NWMX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.13": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.13", "devDependencies": {"c8": "7.11.2", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "b6461fb0c2964356c469e115f504c95ad97ab88c", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.13.tgz", "fileCount": 8, "integrity": "sha512-GryiOJmNcWbovBxTfZSF71V/mXbgcV3MewDe3kIMCLyIh5e7SKAeUZs+rMnJ8jkMolZ/4/VsdBmMrw3l+VdZ3w==", "signatures": [{"sig": "MEYCIQCRqj+c2Xuqs//p86aEtoh7FcSb7eXlbBZEAKEvcWDqRgIhAPWWIt01Wtpce1jcds5YtDYPENx1C87G906FpVxHCxe1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidEPYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquJQ//aI00sgDB+5E3Q29YU6HcO/MmqhqKCPrPVfYi/01dfADIrd4B\r\ngsNf9Kxd33JX3U7oaNXGtvzue8hD4ZE0O9GMsxRwOsY3rweyXikv9sJ8LLdo\r\n2ydjii88JxzPVXflKa37x8abZleppooVfyMXKjokfWR8eeEj/engQ3MdV6/Y\r\nXDZ5Y3mVWLpXmXDNVi3zSl4ZwguP6u3g8e0VrZ03BPsF/LiYQnXaRYxkoV6Z\r\n9lbKsiHbpGMH0QS0FpH9sDpcldSpLJRt+YT+AjixaQZu4Y6x13G7m2mOhdGW\r\nDvkeGUzwYcHciFeciOV/cDYzMh9a4gxjEnLtjHBOgPQVDSP5ryevAIZWTnMc\r\n6wcTh00szPxZL6cm3FIiwZv78b2ts+c8rdtlVYZBVhsWnWxIWIKFmae6Ur57\r\nKHP2UWwCteh8L/Ga3jHcPAWWmznuzh2tDUUzYj3esvwbKXTjIYcBtnRS75KK\r\nAS6MPzEcbkIwweiSCPRXrOQSo+X/UTA4v6BFbL91jFtPQKSM5yzk1gLHwKLq\r\n3RE6tf5c3ZMF0bKOiSHjIJBZdZx2k143QvIi//v5ffKigCPodTwqasurgaU4\r\nGEbvl/sGfT9tkdDfzfP0BRnKYTtkG2eNA5Fa8RXEtd1Lx5jNHYb57hsYozYQ\r\nrMXGc6PHpUok/8lrmPzdS5MWDBPmZgs499w=\r\n=Q1dK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.14": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.14", "devDependencies": {"c8": "7.11.2", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "add4c98d341472a289190b424efbdb096991bb24", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "fileCount": 9, "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "signatures": [{"sig": "MEQCIAHlT4768yuL9BvR7EN1O0sBMLfC1XI1wCwietPQyvBeAiBN1Uno9Tmcrlk6U/9UJlXC2DxgHHXxyHBWrTj8GX2OnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuI8PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7cRAAoN7LwbvCS1RBm+DBFO2drGfI8qLoKIxS9tPbaUO+kxDjmW1D\r\nYvJD+KJzHBlW8yOtPcecntUOahT37jDXEBCI0c221MZRO9N+rR1Hq7iNIEC0\r\nqXviwJ4hKC9EsSkiWrh3mTLoc5NCtaSA5mmt3UiVEd1KJeO3PSlKT2X0nqKm\r\n0FvQp4NsIbl75Lr/ENzwiL2EGycc8vnAOYOrpQFdIA3XlYH0gW3eSJG9zI2T\r\n9CCJlq5v3rTMpgn0PWsS4/3YHjY9yUkUG58L7SHCZxTFUCk24rLixGH2/eoR\r\n1UgaEswUvyTqus2ibZgy0O+6yIzTZTZ8hDUVZT6TUizhyVxMfrSsBz7aufzp\r\n+tRC7Zj3iPiR9oDu5QyAbQ4kxljtMoQ+O8mjyq9sK05ZECbb2thdaoLXeXPa\r\nv76Q4hSae+1UAE2V93py2hWbWGL4Yld2Fua2IOh+hmj7pQIKedNpvE7GtIlf\r\nljFHeI5l2uN03J7h4pbKzcSEub6kGDoFkImqEG53ZXwZMQZix5JP13zhNNju\r\nHH1MOEA83tJ+nWjdq4RMckKy1B/uNXtyENf0GHpqm0BtzQRRHDXDofzZLFv3\r\n4Mkw+KgnzkG4Px6voxw7nNv+YNLI0eGOwRfV3NT7TJuz0NanpWx1ePr/fKPJ\r\nuP4PCGTM7HQ5H6LEe663kVIJGYLdr7IJtYg=\r\n=OLiK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.15": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.15", "devDependencies": {"c8": "7.11.2", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "d7c6e6755c78567a951e04ab52ef0fd26de59f32", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "fileCount": 8, "integrity": "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==", "signatures": [{"sig": "MEYCIQCdtqK56anvpPA/m2Ai72LGn1gw+0ahc2XRHEq0HI91cwIhALTiTnS6bn26kJhLcTVIzCHsHfc5nu0p8toDqtXzwPpH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4M1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqODA//W+0XyABM6jaoX+Qy513esgwRWz68vBIIJSJipgRIrD9JkrFS\r\n78BNoBTyUDo+9U8fQafbA6EnlORZ0hkkc970o+eZE1VraJweTyIrAT966u4y\r\nxvOLdZv+FGjs4nO3exZsONbQ2t/xzVEfHWAT2+z0ANqRmoisEigVz51JAzfc\r\nbq3QYr41IzwSpjaQ8IJ16IyCAQIeTDYx1NhHT21XyvaO9HXC3CgPX0ogleI1\r\ny2h2w+cBMUR2EAkhV2mapxfyU3SDdddvDj5QlU0g5R00RbIcLLVRPUVuUj7y\r\nhW3SJodibrObt5DmjqoJjV9AurQwZXHQCOwO32c0x0TdiVFmSDbAnydlfHZG\r\nFFpDoJYgWfUg/lIRpu4oEpmsDYgAvEDUp+8ToVzLbC8qJAEfPRJ3t/ygjqIP\r\n13DOY1tooKJtfpA8lyiHxHLROobi2uXWJ1bZpzKkFGKP77f3pDjOSpdOi3YH\r\nHWJV/Kx99Mh7upeBkdAh3q8hpeDnKKisAsx/G2hrGVOLPPiVxOeA8YI6clLh\r\nJ5oCyJZ4eOflxa9Itq+0OISFzCPUyZ6b/u+JT7z0aQVHtrTjro5Ap2UJ+KUy\r\nvcXvSzqDxDiBMoFCypxkbwSZCvA0IvCRonnNRG7bHO2yPDqiVaHy0sctKuIX\r\nQEgjKGqJn98M7CZVXC2nf5cFLZRLfDxvV/o=\r\n=ACMx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.16-beta.0": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.16-beta.0", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "@types/mocha": "10.0.6", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "5037f25018ca5d896fa438886b75616bcaa8bdbe", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.16-beta.0.tgz", "fileCount": 10, "integrity": "sha512-qiZJiTfyb00BApxRU7Apz/3jtlp4gKgOmCXlGQRlIQ5zg6U0uYIb8lZBfbiJ+TxAEJ+rczfY07+CExd8sTRo5w==", "signatures": [{"sig": "MEUCIQDVo7V+KV09KzwIr0BHCb+WO6FbfL5ZgSmjzSB9XDpQTQIgY3V+0KthOEnXEFQ/3jcx6MCaQEJCsAtjKTACG8YcFCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133970}}, "1.5.0": {"name": "@jridgewell/sourcemap-codec", "version": "1.5.0", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@types/mocha": "10.0.6", "@types/node": "17.0.15", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "benchmark": "2.1.4", "c8": "7.11.2", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.64.0", "source-map": "0.6.1", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "tsx": "4.7.1", "typescript": "4.5.4"}, "dist": {"integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "shasum": "3188bcb273a414b0d215fd22a58540b989b9409a", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "fileCount": 11, "unpackedSize": 112815, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTKlVwpijmiYpDTegYxRDpI1KCdd96r1ALsKPOlAvH+AIhALOiPy+SkgREuRW/t4xOBLy7hxTbOUcFvcha9pjPMB3Y"}]}}}, "modified": "2024-07-09T19:28:13.061Z"}