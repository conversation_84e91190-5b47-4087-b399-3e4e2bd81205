{"_id": "@rollup/rollup-android-arm64", "_rev": "152-31264abf090eb88996b81c53f78ab4ec", "name": "@rollup/rollup-android-arm64", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.0"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "a74f9361f86f10d0689139b74140f5bc230cccf9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-mURXKi640KON7A1TkCoSHfrnBCpkgOrZgKvohGxfpvJNRvvD73t2Fy12zvyHaB6s+gkgvuMJhuPky7/Y/4JVcg==", "signatures": [{"sig": "MEQCIHXM9I7iKtuF6EjP1SzoKi9nM9iAD/RGrahlSR6d5pujAiByPD3/6PDgdmFo/Y3KK108dcJ/59axqSjHJZ8pZxOQqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723}, "main": "native/rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-0_1690831072201_0.034100970479768566", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "8f20da81456c5121817d6cd981af56cdca2f7667", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-yRGZinDmoLJ3ieBpnmMpljZLVsmfZm2T6xzJ+CbtBBGotkJl2spJUrVVZgyV78B1+n1C76EXAu4b4Sq6jhI8fg==", "signatures": [{"sig": "MEUCIQCCxvkkzDa5TKJgd2N2sPxbJye8sDXOXa2IDBEv5/y/1AIgU4ya2eL+U4pfYOGgJCCe/WjeaAtK7O68zbZN7aTLiuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2314132}, "main": "rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-1_1690865335837_0.8933983604513083", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0adbf672ea61612a7af72b1579ffb6e770d8059b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-d6OTBmepSLv2AfpfsziT0DsOFiyztXwJgV68YqeyGl6TyR+B1RCdxRZEY09D/nhgyWgWhUaGh5k0bikravu55Q==", "signatures": [{"sig": "MEUCIQDfdttQnHO8PTjQnp3IDTcmc6HoOVgmtNynLx4TF9PjDwIgYld3lLz/dwFPylbOg0sm9g84q2HADoIdaShBx4M/DWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2314132}, "main": "rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-2_1690888588641_0.036158396925765945", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "18d47c6698514d064ceaa1e11082dbc9cb4d8199", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-EGfEXBQE22zs063LZ399WpRd9Hq6pOFIJbKTHHnHAc9/mm7lcwD9OSpV0NY96s/9vEfGcydHJCpo7KPHPziOOA==", "signatures": [{"sig": "MEYCIQDjaf9XSQgnTgGABGnDXlHWs9hh40b4bsnKYg5kkpsPkQIhAMl/pBR1SnF1KAAtq0znJclblUKzruhhgB5wxwbl2FNi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2300476}, "main": "rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-3_1691137010269_0.01701957573024404", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "2d24d359a3b53685ed5c508daf3df1e48923d289", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-7EkIHu6Rp/7btHX0kkKF2ys8YLa1U6XW3BH+rpjmPd0Bf13XTVJbv21o1uF+94MZXOYbbNqcX5y4xB3kM2tCuQ==", "signatures": [{"sig": "MEYCIQCQj7Tw8C1s8LNFw3G8nhbCp7f2XCvIPrUNb2x+4tVcYAIhANDwyVGHsMKnCcGmRynIQiooskbif8YvIKOybbWbIgto", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2300532}, "main": "rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-4_1691148991420_0.41066123365102647", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "720e3fb501e40318455350e2c96d0c5c4e4043dd", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-X5CU7mTClPbSSdQSLefd3/WZm2UnYLYvLyVOb3algqLYb+fs/S+PPtUzM2wvRqvHDDO9CAK6XNDD8T4vBAEfKA==", "signatures": [{"sig": "MEUCIAOmTlIMm1QROtqGRL7AOQ7HqQ8DkwT8xM15H9J7GN4cAiEAmrmYyMO8ta2HP/rZhyNl2TW4SOP0LOhRgwZ75Acdd/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2654991}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm64.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-5_1692514604941_0.08277866690301616", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d91e1993b1de03dce19bc81cf4518789b077f9ec", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-NjVp0pSrWjc9yrToU/4jbBS9D5hNCdDZQOITzMdEsXGm6oC+WixLwqx/jR29CKe+lK9zT0G8ySMnyatKjIuzjw==", "signatures": [{"sig": "MEUCIFrspRNCkuSnOtGdiGKMQhiJtmztDsl/YUMHlFWL75djAiEArasWyJu1Nsfc3Qj5rS0m4TNzIb4Hu1pQrK5Tm2E4utg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2654991}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm64.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-6_1692517894331_0.39389146265137254", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "123c22ee9cdc782cdd106303616938c4585be20b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-v1z4b6W7DhKgGYsIiQTsgJVbBVZ/swCdcyiKSekU8S4r9Q4Yc9kJwD2yKS++zgJtiK3loFJR6B9iqrxh/mxOxA==", "signatures": [{"sig": "MEUCIQCo8cmfYTOV8iP82D2cMJTVDQY4YZ7jTaFqcUykYsg0mwIgI4H2mP2ddVKNHuVxepsMbSpkwAysgjv2GEoYfwjPPFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8109913}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm64.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-7_1692527599032_0.21919912887885729", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "27d3a68ea2110b5ea8d176e6a968cd2fd96795b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-rGWtzoYMl66MjKRxcp5FDiWe084HbcdP1ZI9U3YpWoyjRStQxU+Puk/90XkjNlRy06xodDy7FDkJmjw2tc2wEw==", "signatures": [{"sig": "MEYCIQDLzkf0N7iVMDLoqxVnR1gQ6j/c5udSJlNB/w+JFotL4QIhAPhhRi8X2fNfZFJhw0De1RWzwYfq7Lc/zH6zvaaduJ6I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8109913}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm64.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-8_1692530529821_0.38621169240486863", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "bc5f63934c376ee780dbf0f710a4a7ac6576c8cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-9QjWDfjW3u4/9cKuAEkdC8zUrvdG3xUvhnHwv4cm4r5/fF1Ur5lg8cj2v0qza2AFBZJQSCdRjCbvjhKW4zuFcg==", "signatures": [{"sig": "MEQCIBrStSZOSbo4CSZwOHij4nhnUJBzN8ms9Z3+SuTTiS7qAiBTcEJmWCko2Xerp45yn63TwMK7bdyLIrUyr/xIQOWsyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8109913}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm64.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-9_1692541736045_0.37084965041559537", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "ac7cba7c74f6d8d48babb2841a523cdc132da244", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-uo4dXd3qFR3MuEl7T1H9GPvhAPdmvUC3Mvs4ClsvwfSzUZfPqd5emNt1O1aw6H5qZpEqj+z+PK6X+rkEX2CF6w==", "signatures": [{"sig": "MEQCIE6Fvq4UDFRdxSlS0Iez6Teij3xqyJinyilpyvyIDy8WAiBYJs8/CsiuJiMjQEMUzLfqM5xEOlsU++LTxatGg2T1Dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8127029}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm64.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-10_1692631794503_0.5091205920229815", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "4a7747bf16f0685e6b81a1fb868165f2d9f9f7a2", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-9zYlx8A9a31v77EnRc3zow4xT+nCNZDhk7NkkmrWYf+t8hndTEU7V6MHxqrBFWbX9Q9Y0qwDvBAM7kZPLc+7Nw==", "signatures": [{"sig": "MEQCICMpZRsyT/lmwX0g7gJnsNI04On0tlDDOeaEbXKyIvvnAiBiQloYMgxD2J+GS+EEDeiLi02hM9ZsHoqhXCYJrZk1KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2656976}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-11_1692785743253_0.17716589475155375", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "380c03a11ee5bfd54a3b615cf2b67026f2d1f37d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-svi94XFLjcVGSGmcRFS7Nu0K2SekmeWJhBRKogPMIaFrz3cL3kpa7BlmhpuMzPSfsQ6FgvkbukjuO7x9m0X8OA==", "signatures": [{"sig": "MEUCIDIOkvMFwFTntO7BNvKztr8jmpCEoZ1vNDykJNqJxid5AiEAkTucF8CVki8m9LSPrtirlqiKG2uXS9Gg71cEBax9J/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2656848}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-12_1692801619009_0.4963091362754042", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "f90be0af2cfb4fc5c38c2ac9aa19af504076ec53", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-AfiMuhixnxHEPzVpYaszo/P/46cUSP9wNRHJMLUac+nWpZ0OnXj1x9+lICAnwTrPhzFg3w69PiCBToZCe8mFHA==", "signatures": [{"sig": "MEUCIHID2c9DZTFXPF38BQLL7gtXE4xjve7L9nrMggnahMTRAiEA9K8StebH0+naXuQ+SwQEryMTcUkzHRtongAv/k5McMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2675144}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-13_1692892109980_0.6930204268665374", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "62f50cc14baed1b031bf53de850be797c8e0854d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-xLUxekk14EwSmoXLeb9L+a0T6wlz1rC1O78e977P8Ng9GDRxlAihou4WP1lQYjmUhRcYmdaOEeaGRMSmHKMH9Q==", "signatures": [{"sig": "MEUCIQCoRnSodzW5ag/zhKsVT/2i0Tlq+6D3FLhm3bxlLEoLEwIgJXrAn97dPeMNgq9ptPr2x75w5hgqDgosQMtk8p/coyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659640}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-14_1694781262195_0.5383268467672917", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "9cda392102e051e5b15099157f49810f751cb2b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-muK/oG8GwAs1ONuS/b+F+YnPEPDOhkdGzvmAnYRq6+tOnCWMMebPckqQWP3a/vqerFIieHtmTxi5pTOv6VK0Kg==", "signatures": [{"sig": "MEQCIHzuyFNR3Pdjm1x3zDRUKBPFTl303EfTFjFWEemc/No7AiA++Tg+wG+Gmp53OalWc6gJpZgTQfqIBXAKgNb7124omQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659640}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-15_1694783209157_0.985196525345112", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "c3b84e9700398f565d429278f608e9b3cc3dc980", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-t7oa+GIv9LGxSPU7oHE7U0NNpAB4C6CBGC/6ep/ouUvorNhPbwM0tOGwwl+KObh0M2fQd701uNmsn84bH3oQ8w==", "signatures": [{"sig": "MEUCICwuQK5wVw0ESrE/F9VBetYkjdMUTtcNboPJXCBCpXqxAiEAhh611TNDRxgn29QLQ1mieNbcuTQNwNwyS9JMMM49b3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659640}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-16_1694787431295_0.030133060309461834", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "7581f7eb816a6f632f38f34638ab6b89a78406b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-YEfkhv9N4EX72QTND3RXjU5WprAghELAKlxqQJU+jHT66uwUvby3a4uBhoYbu2A06qv22fyH56yHNAeUbmXLng==", "signatures": [{"sig": "MEUCIQDH0/EzshAXWVNruBvYkYVpFWsXGXd8kiHWILgEC1XKXQIgTBlufYEg5eH8jAiic0yQo+jwaUIcw2b0FKkxxi3wVVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659640}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-17_1694789942763_0.31036550903528637", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "1d89796fbe90dae5081bc3f4eadc4467a7334a48", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-/VveJNGF95NkBDfMgqXHW0ytj56qS/ckslXXGN/kmgVzhVtuI9uFrK+c9lpXBeBjIs4TYYPflunwEUPbWtwO8w==", "signatures": [{"sig": "MEUCIGVlOfT0eMwty37O2KTtijfMGLMOJRQ3l/OcjRmvibW7AiEA8Gp2SRfGTtu19gWXzvt0XpXVV7cFQ9SoE1LYI+4W8DU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659640}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-18_1694794194292_0.4931165194214926", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "ee7e01ee61ab037bab9657e945546bd7af10f131", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-+0EaQ8aorkIQSC2t9db+scn9w+uPUT4xZIAI9CQK+anXo3ux8blo8u62m5O+YZSVV4IOAaD3uZ9HuPLuA0/e3w==", "signatures": [{"sig": "MEYCIQDitUZe4HQK81ycFP6JU9+ZutvKD4MB4v4jkoK+vtvIngIhAIL0qbH04bOqqJaUSMerUYClKZEF2b0yW0AfB/yNp+7i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2659640}, "main": "./rollup.android-arm64.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-19_1694803853706_0.3672838785569663", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "4c74961f98305bd7bed869a76fa3a8c2d02c5ffc", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-A672sDApM24bfRJWDVQagYZcUGZHZj4x9DcV0iUtmBar/3aktCW+foaelnlp4Yql3edu70yil1TDQMrvDIdH8A==", "signatures": [{"sig": "MEUCIADALqk8fERnEdch/fWMgF5Yvaat2tQJbM/JarWmyZtHAiEA/Ru7eAz66We1sXf2UkYNUIl/133czJ8VlcD0vk0afwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649838}, "main": "./rollup.android-arm64.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-20_1695535834137_0.7801875640959186", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "4ca5b120077fecef920e4973a3aa5f551b1617bb", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-Wi5DJ2XU+yPqn/xLdfvxo296uQqmlgZWC9KC1mAjNtyCIkKPyQcqXsrr4EXgskBOe0rS0E8MfUeQtbloCRFNuw==", "signatures": [{"sig": "MEUCIQDG73kRPL0lcR1vSI6quaLnfhzR3Q7MI/bmvlsjIyyYPwIgBn2qxaCKp5QbIbjeAIJQ9UM5MnrR7Q9rmworlVDwhU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2649838}, "main": "./rollup.android-arm64.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-21_1695576140269_0.8979117941120722", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e1969533a8073e18edf3ae0f99606a415c3a7c49", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-49x5mp/GVgrLlDwMpObBTQ/KbM4Wsa2kLkAvjhkW/T2d+Wpss6PL04uKkNcQecO467YH32AhOM543UJ+YtDKkw==", "signatures": [{"sig": "MEUCIQDANd5F+fOrzz0yC3oEcjtK1wxOel+3MgUlw8BkpKGBaAIgArwqj5GfAEh/eOg1FQpGfz8aS1Qj3egv2/1aPGtXCn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2652886}, "main": "./rollup.android-arm64.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-22_1695745028737_0.6572723450762554", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "1efb480309e8d5c9fb2ca48a82a024cd5bcefcb1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-iyygT9P3wLXqVnKFE7OpteL32ijdZOnDbJhQ4vsOMfyZyE7ce0fEDMM/VsLirUMHqCH8sQO6lxFq/qBkj3sPLQ==", "signatures": [{"sig": "MEYCIQCvU+b16Pw9u4bgW/JF4bm1PbLmNLpKPcHdr4fOujCB8gIhAJhZfxi97rEMNkjJrkW9Co7R+hzVJCM26AThLBKSvCVH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2652846}, "main": "./rollup.android-arm64.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-23_1695759259230_0.1374495028865006", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6d350887468c0c97ba876c9b63d39de480502f35", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-ftTp5ByyyozDsHfmYGeErrQmBi4ZEVZItC4Siilwretkf+cMv9z0s0Ru8ncd28OZpaO0cr9b7Afm+DIRDyE8Kw==", "signatures": [{"sig": "MEUCIB920KFjee+kzlXmOaYN/OReEZQVrTI20QTwAPvLqFnKAiEAuXEjOyPC7eaHDIQnmV/lIvGV1aQOvI2FZAsbFmbd71M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2648662}, "main": "./rollup.android-arm64.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-24_1696309961658_0.8063248483721277", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6bc0a418c1e3e49586d01b1ea88025ec60a41a17", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-DfeB9B27MywgKmI3feFbXUmiaYE5lzOyE74dXebEJYKGXSkLG6ieU0qfjNU4uAA5SecorvhbX+xQz0i7UQHXng==", "signatures": [{"sig": "MEUCIHcjwIgY+EhAgSwl8uLU/HAp/aI+8zlbwxHS+vDj+E62AiEAi1AE8koE5RYQojjaidx+6iluJEe9Ir9d7k2DcTp+mdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2660134}, "main": "./rollup.android-arm64.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0-25_1696515160160_0.7257780263534896", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-android-arm64", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e4fc4213a7d600f7a32ccfe0affcc2c7a799e52e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-dcdg6Zp2bqIS/+2FHhdSS+lbcySufP2fYYoXkDa4W6uHE22L15psftdQZtFhxvvqRWPD1HsK0xIj5f07zuujkg==", "signatures": [{"sig": "MEQCIGFZ4uA2I0pxQle+oNNQpEdSgrg1aaIVy7AeNXNWHWgKAiBBV/E5oASdgP+iElZxQDXBcAbgMuIB4n7FOWlNMZXxnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2660131}, "main": "./rollup.android-arm64.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.0_1696518866632_0.27005371550349744", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-android-arm64", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "9cd6c359ecaa04c85a4439b241a10a3f78bbc020", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-cV7bMb68ipnlgxNjV2fvJRLeIHaEnJr6MrYo6+Re4rp5+B3iJMeOvd/ICjneWcutKzEiFtiyK55FSyu4A1juMg==", "signatures": [{"sig": "MEQCIELXjwiKLvvGjasxWqDsJprQBoby4k4So4mY3xeGEerIAiBXsa3qnRSwuG+TCZ/Pd3ru6K37ZuD8veZ9BKl2UrKwjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640587}, "main": "./rollup.android-arm64.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.1_1696595795096_0.6013637148781834", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-android-arm64", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "46327be24d75c7c53b7b065af29a62688eae3f1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-lqCglytY3E6raze27DD9VQJWohbwCxzqs9aSHcj5X/8hJpzZfNdbsr4Ja9Hqp6iPyF53+5PtPx0pKRlkSvlHZg==", "signatures": [{"sig": "MEYCIQCeKM161lELa4Z63hnzn1VKBnxyHEGKOVloQ7bumUjwVgIhAInNOc2CVBRIffu7UJQNK21m0Uh+CVzJvXTxurjLfe66", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640667}, "main": "./rollup.android-arm64.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.0.2_1696601916712_0.28923938845214003", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-android-arm64", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "bbd1f4a35e289f0781b914e3876916063e0e844f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-pIi4Awf/YFwdc3H0VNYZMTS7FA0J00rS8AKoSfyB61GDVo+r7eOjSofoUPhDFXU7pfuTBiZ/4VAGa/qXGm5wcA==", "signatures": [{"sig": "MEQCIAoG5GMAv4xM28ZT9ORf35iyn2Sd9/Gd2xUbiyMfFhdFAiBB8TEZPLlVQXHgF82gWrl1UCqnl/oTvWD7f4bW6tJdSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2701459}, "main": "./rollup.android-arm64.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.1.0_1697262729639_0.9475568042080948", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-android-arm64", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "8e72cf1d1eb5ddb9fd858dcb6af0c703625be7f5", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-bvGQbel/3YsTznK4FLPb+ZVIoYuM1SHntnJYJjlP2QI9AFqwYQqGRPvW27ZefTpDvFfy05Fdg+EhVMxU6uDvtw==", "signatures": [{"sig": "MEUCIEazsIoNrl0ujfkBZkWUMXVU+INR0ERw99V8gUDHIoAeAiEA+AsA3TVzR7etrdAvvPeGNEnjbrj9i2nsxuC0QhtdcQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2828507}, "main": "./rollup.android-arm64.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.1.1_1697351500750_0.3895312534862032", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-android-arm64", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b72d0384e080ca99f8a9d5941b31254eb36559cf", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-gO5j0qqT6ETdSf10gxTBeBmsKPC6yK80StTxHr4pvTYpPDfI7/mzSHy/3ez3OQyjxcBXs3i8tSF8aknwCkzv6Q==", "signatures": [{"sig": "MEYCIQCemgluNyKr90Sp1BJc1tHQEB+VMMD0gVQxDGIGYt+mGgIhAMs2mxwGPixnx4ERuGIeRCAXSSVDVXBD9Y5viHsVArgE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2828507}, "main": "./rollup.android-arm64.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.1.3_1697392102320_0.34343097978450365", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-android-arm64", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "50c4e7668cb00a63d9a6810d0a607496ad4f0d09", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-D1e+ABe56T9Pq2fD+R3ybe1ylCDzu3tY4Qm2Mj24R9wXNCq35+JbFbOpc2yrroO2/tGhTobmEl2Bm5xfE/n8RA==", "signatures": [{"sig": "MEUCIQDo3j1Cfh8PPD3nzW37jPAOBke/RCkLYQuheeAyadv46gIgQMvv7vNXQWm4MPFcnlxXFNEl/UHPrZU0AWj70Ndbd34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2815707}, "main": "./rollup.android-arm64.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.1.4_1697430845114_0.7665059092663946", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-android-arm64", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b17d7262f00654296e8167b8b7a6708b79a29dc1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-tmXh7dyEt+JEz/NgDJlB1UeL/1gFV0v8qYzUAU42WZH4lmUJ5rp6/HkR2qUNC5jCgYEwd8/EfbHKtGIEfS4CUg==", "signatures": [{"sig": "MEQCIEZU81PcesYhCWzUlnLM92uXm0ls6fmHLbdS6HeVNsK9AiBZ0MzECaw4UMc1v56dcaCHBiXIxe0UD8Zx9HGmCGvF5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2816283}, "main": "./rollup.android-arm64.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.1.5_1698485006784_0.5017892251941491", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-android-arm64", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "05e121d2c5c6bd33b0d661f0841f5749daa000d7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-A5eGcv5Jb99lt93z2yeW9Kb/nvnYCvb0e1w5b+oKjDno4k5rTppWuQSvgZP2LZAdhRMoBBzZ7bOVrRdHb1Qczw==", "signatures": [{"sig": "MEUCIQDToHuuKCAZyfbzm7IzbWtJCPrBVOzsk7rh4XJL3DZnDQIgUbQWx5eQN39IFe2rAGKon6H1jkue+mKVZOcRJjNqjJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2816283}, "main": "./rollup.android-arm64.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.1.6_1698731109747_0.3855005301067196", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-android-arm64", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "416abdc076810cde6f84f05b0fc9decd2d7c319f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-+71T85hbMFrJI+zKQULNmSYBeIhru55PYoF/u75MyeN2FcxE4HSPw20319b+FcZ4lWx2Nx/Ql9tN+hoaD3GH/A==", "signatures": [{"sig": "MEUCIEZwZ0OtTcJbeOcoMSNyswSAnqbCcDYQvwLULir8edkoAiEAz0FrRxZa+hc7NYvWtGopRQNiLuPlpfWbQiLPdZnQoUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2832691}, "main": "./rollup.android-arm64.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.2.0_1698739835668_0.037016601172050745", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-android-arm64", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "17b0f412034d14668c8acc8b7cbd8b1c76279599", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-nLO/JsL9idr416vzi3lHm3Xm+QZh4qHij8k3Er13kZr5YhL7/+kBAx84kDmPc7HMexLmwisjDCeDIKNFp8mDlQ==", "signatures": [{"sig": "MEYCIQD9/Id7q2xkmt07YAFq3vEo3O/VI/mCtVAc5qHHZkT8qgIhAKPbHR3nnj8gwvUyxOUG/O0iwI1OI2qqmHDeUmbQ+fvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2834483}, "main": "./rollup.android-arm64.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.3.0_1699042378148_0.8970610266824155", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-android-arm64", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d94f0d47a5d321c43958bd289b16e48aa6dd9311", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-3UbtU+7ocBMxYoMCDymHnFYB8tALVaEOjTe5pzAB65AJwXfDFAxADYGCJnBzDXD9u/G+7ktoYnMGYhitYphFkg==", "signatures": [{"sig": "MEYCIQDORDEryhrQE2kAAGoXVNYcqFuvehomgX1DDQsfKdCm5QIhAJ4Y2xb1ZmQnn+NZFKOARh25R4isqNNg7s2qLeRmiY33", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2774755}, "main": "./rollup.android-arm64.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.3.1_1699689470919_0.3558364707804822", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-android-arm64", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "38a90aa6be6ee7a3b78cca8dd919bbca8c426570", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-PlqvhzFxy5FRTB3wLSsGgPhiakv9jrgfu8tjSojLJFP0CdhfZSRDOFvQ2emWLUEBOSCnjpL63XSuFVMwg59ZtA==", "signatures": [{"sig": "MEQCIGiOxwTn/kXQvdhAha35S77qH8XD6F+dmBsrLhXMgXZuAiAaU/G6m4rRidcbGGZm4b3bFjjfbtVQDYzwDCUuFTqWhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2406467}, "main": "./rollup.android-arm64.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.4.0_1699775390742_0.5268285311834449", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-android-arm64", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "f0492f00d18e1067785f8e820e137c00528c5e62", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-sRSkGTvGsARwWd7TzC8LKRf8FiPn7257vd/edzmvG4RIr9x68KBN0/Ek48CkuUJ5Pj/Dp9vKWv6PEupjKWjTYA==", "signatures": [{"sig": "MEUCIGZpvACVniWyQ2BvzXAv9aIN9D7pRbAXseJFWB75l9f4AiEA6wGkRq5F9W77PgsfeTinIE3Ul/XNEjG19eaJZZme9vM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2406467}, "main": "./rollup.android-arm64.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.4.1_1699939501966_0.4206806913137209", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-android-arm64", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "8456a8c623cca4042ae4bf2ce03d875a02433191", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-UdMf1pOQc4ZmUA/NTmKhgJTBimbSKnhPS2zJqucqFyBRFPnPDtwA8MzrGNTjDeQbIAWfpJVAlxejw+/lQyBK/w==", "signatures": [{"sig": "MEUCIQCgvkPD1A9v0bua1XlQFOtX+yE0gjiZPxvMKbivcWQE9AIgW4wTch97KtoQhQIEe5E2ZDRsS6tZu9t9ybw1Qy1pLHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2407875}, "main": "./rollup.android-arm64.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.5.0_1700286728665_0.975230705044638", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-android-arm64", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "cae505492204c018d1c6335f3b845319b15dc669", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-n1bX+LCGlQVuPlCofO0zOKe1b2XkFozAVRoczT+yxWZPGnkEAKTTYVOGZz8N4sKuBnKMxDbfhUsB1uwYdup/sw==", "signatures": [{"sig": "MEUCIQD588D+f1CiZJ7JpD0dU+Y9tLNeqvpLjuRffl+Jc+4PhwIgHsc4HuH/uPJ2UCluwodsQ1Z/2moWgxO7M3qhId0oDLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2407875}, "main": "./rollup.android-arm64.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.5.1_1700597585621_0.8474942881031158", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-android-arm64", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "961089fe117ceca642b6432fadd093048da93ae8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-xOuhj9HHtn8128ir8veoQsBbAUBasDbHIBniYTEx02pAmu9EXL+ZjJqngnNEy6ZgZ4h1JwL33GMNu3yJL5Mzow==", "signatures": [{"sig": "MEUCIEZ895/pbR+Wv+9/UO3lFB8mWMyKxZ19GifDaHPeOzwVAiEAsGq/42Q9M+BlufCAhszsHDe9ySCxm9iwDdkD5Do63vU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2421187}, "main": "./rollup.android-arm64.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.5.2_1700807384128_0.5232253443661334", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-android-arm64", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e0cf96960405947c1a09a389467e6aa10ae1a226", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-y3Kt+34smKQNWilicPbBz/MXEY7QwDzMFNgwEWeYiOhUt9MTWKjHqe3EVkXwT2fR7izOvHpDWZ0o2IyD9SWX7A==", "signatures": [{"sig": "MEYCIQCDwVJsQMn7QdpU1TeGpPDvcLE6d7sbFR6mGviReyhKrwIhANUdnDpz997NBDykCgA3nRD0Yy/3M7IlUwP2SYkucuY3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2421187}, "main": "./rollup.android-arm64.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.6.0_1701005950580_0.7675892325409788", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-android-arm64", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "27c8c67fc5de574874085a1b480ac65b3e18378e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-1TKm25Rn20vr5aTGGZqo6E4mzPicCUD79k17EgTLAsXc1zysyi4xXKACfUbwyANEPAEIxkzwue6JZ+stYzWUTA==", "signatures": [{"sig": "MEUCIEI5FHkSPUK5e0MaqPXYHXAhsm/vqpVsOKtMriks9qdjAiEA6o8pflWrubzrK4nAxET9lOoK29y3B1qmHGECXjSeL38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2421187}, "main": "./rollup.android-arm64.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.6.1_1701321783423_0.5027343363871843", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-android-arm64", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "4e05031399a9c795612c9694827ec4ba55771bec", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-/EBw0cuJ/KVHiU2qyVYUhogXz7W2vXxBzeE9xtVIMC+RyitlY2vvaoysMUqASpkUtoNIHlnKTu/l7mXOPgnKOA==", "signatures": [{"sig": "MEUCIQCpkdndpllvssRK0NTf+VWzOQKVPv70rUk27dqQoT4wpwIgZuQUf9XTm21ae0UfCKlvj+MaLsMxF//XV9eNUwd7B3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2431427}, "main": "./rollup.android-arm64.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.7.0_1702022276757_0.6871719061746648", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-android-arm64", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6c9fe8f9eb0cd9029be93b822b1a1c2d6b31c275", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-aiItwP48BiGpMFS9Znjo/xCNQVwTQVcRKkFKsO81m8exrGjHkCBDvm9PHay2kpa8RPnZzzKcD1iQ9KaLY4fPQQ==", "signatures": [{"sig": "MEUCIQDdwjxu8kTvcZrWnV86hOCW2GxW/hmEf8xT1F0I9pMKuwIgbomNWHMf9LSub/NV/q10wal2o/RdI8oT3m2676kJxmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2431427}, "main": "./rollup.android-arm64.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.8.0_1702275889865_0.9555021371572683", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-android-arm64", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d4c14ef9e45d5c46b8d1f611ab8124a611d5be5b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-im6hUEyQ7ZfoZdNvtwgEJvBWZYauC9KVKq1w58LG2Zfz6zMd8gRrbN+xCVoqA2hv/v6fm9lp5LFGJ3za8EQH3A==", "signatures": [{"sig": "MEUCIQDGqlDLEbxWSl4qc4p8+TNGd4Hi6PnxQRA2STDo1B2IRAIgZd3EwVng+WaSV77O/ZiMDINePiTqWwLmiv6BtzoZh8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2431427}, "main": "./rollup.android-arm64.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.0_1702459453410_0.16508658241946428", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-android-arm64", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6f76cfa759c2d0fdb92122ffe28217181a1664eb", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-Jto9Fl3YQ9OLsTDWtLFPtaIMSL2kwGyGoVCmPC8Gxvym9TCZm4Sie+cVeblPO66YZsYH8MhBKDMGZ2NDxuk/XQ==", "signatures": [{"sig": "MEQCIE3VvPjoC2n2kkXYnISRzhFTdqm5UT6pZesgwRm0PhM7AiAn8rfp/7XkqbJUIMgm1sm8RtM/gIl+MppiFa2aiL4W+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2414211}, "main": "./rollup.android-arm64.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.1_1702794367786_0.12688934093418647", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-android-arm64", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "21bd0fbafdf442c6a17645b840f6a94556b0e9bb", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-yZ+MUbnwf3SHNWQKJyWh88ii2HbuHCFQnAYTeeO1Nb8SyEiWASEi5dQUygt3ClHWtA9My9RQAYkjvrsZ0WK8Xg==", "signatures": [{"sig": "MEYCIQDsGEsTjG1iJAQwU9BOTAyhGlvxW/4w6L7S/OEZvN9XJgIhAOVeH8UhsPkVQV+QSRZ6Dw9RPf7/5cFRpdQTInK9S0rW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2426307}, "main": "./rollup.android-arm64.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.2_1703917404180_0.5909585794672465", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-android-arm64", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "5bde956d84961bba95ba3c98ffb8664946617d91", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-kffYCJ2RhDL1DlshLzYPyJtVeusHlA8Q1j6k6s4AEVKLq/3HfGa2ADDycLsmPo3OW83r4XtOPqRMbcFzFsEIzQ==", "signatures": [{"sig": "MEYCIQDxWf+rfqUxX/6cR4CXOsD8OmgRkhZAWkfnJa7U77Z0jgIhAItydlJRjKOS6h2vS4wRyPkowdjmV6Zi8fsyRBMZW8zO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2422275}, "main": "./rollup.android-arm64.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.3_1704435643447_0.11923906290030106", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-android-arm64", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "96eb86fb549e05b187f2ad06f51d191a23cb385a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-ehcBrOR5XTl0W0t2WxfTyHCR/3Cq2jfb+I4W+Ch8Y9b5G+vbAecVv0Fx/J1QKktOrgUYsIKxWAKgIpvw56IFNA==", "signatures": [{"sig": "MEQCIDATO42O1z+IU8Y9QmJO6eEeY16rWc31swLs9jilND3tAiBCehWJhK6yHV+3Y2blYVyUuweT5SrxmdiQIrMwd+QEVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2422339}, "main": "./rollup.android-arm64.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.4_1704523136549_0.09208659028105481", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-android-arm64", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "33757c3a448b9ef77b6f6292d8b0ec45c87e9c1a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-f14d7uhAMtsCGjAYwZGv6TwuS3IFaM4ZnGMUn3aCBgkcHAYErhV1Ad97WzBvS2o0aaDv4mVz+syiN0ElMyfBPg==", "signatures": [{"sig": "MEUCIHNA+5sRNQjiWfQZQKzRjxVZKV0c+43Hx8zNzWzMt6OhAiEA1xYgWpa8lAqsyEU2iZdzgM79Ff2QbBNgO14W2Ry/kSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2422339}, "main": "./rollup.android-arm64.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.5_1705040170399_0.060630503881570874", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-android-arm64", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "46327d5b86420d2307946bec1535fdf00356e47d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-T14aNLpqJ5wzKNf5jEDpv5zgyIqcpn1MlwCrUXLrwoADr2RkWA0vOWP4XxbO9aiO3dvMCQICZdKeDrFl7UMClw==", "signatures": [{"sig": "MEQCIEWhpk6Dyv32SoNCDqz4ffDOVcsMpSlHs/WJ7zkq9MUpAiA9ENIANXg1WauPpHWkDJO+8SDZ8GOx9bfj0rbgwOQtOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2424771}, "main": "./rollup.android-arm64.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.9.6_1705816335703_0.9375921104036722", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-android-arm64", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0114a042fd6396f4f3233e6171fd5b61a36ed539", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-lvu0jK97mZDJdpZKDnZI93I0Om8lSDaiPx3OiCk0RXn3E8CMPJNS/wxjAvSJJzhhZpfjXsjLWL8LnS6qET4VNQ==", "signatures": [{"sig": "MEUCIFB821AjM4AauK2NciF3vRZfWPsnesp3zwhvTvkgq2r/AiEAqeNWOTgvOYBuzIRao23zA9hliDLPTwCqiyQBJr230yA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2495124}, "main": "./rollup.android-arm64.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.10.0_1707544718148_0.27566604576602427", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-android-arm64", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "772cb1856f720863d982c17f25d3fbb930c946d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-0ij3iw7sT5jbcdXofWO2NqDNjSVVsf6itcAkV2I6Xsq4+6wjW1A8rViVB67TfBEan7PV2kbLzT8rhOVWLI2YXw==", "signatures": [{"sig": "MEYCIQCBzHP6IyGuXY2OF6bu8lgpxxdWGUaj5fh5CpsXugY79QIhANIloM/b3mQEXi5DosmtdX8ll771JC54gBmryLGri5yg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2495124}, "main": "./rollup.android-arm64.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.11.0_1707977373386_0.15976047303997443", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-android-arm64", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "3822e929f415627609e53b11cec9a4be806de0e2", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-OBqcX2BMe6nvjQ0Nyp7cC90cnumt8PXmO7Dp3gfAju/6YwG0Tj74z1vKrfRz7qAv23nBcYM8BCbhrsWqO7PzQQ==", "signatures": [{"sig": "MEUCIQDZf2KVw0djbsyGVoTUFgGtOpnWqN+Jn5A2E3tGSyGLKQIgfFQ9KLHVDiRiOnT5R6drEvPqhTnDYbi0X9N/O5MmxTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2491604}, "main": "./rollup.android-arm64.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.12.0_1708090331066_0.657819741210919", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-android-arm64", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b1e606fb4b46b38dc32bf010d513449462d669e9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-wlzcWiH2Ir7rdMELxFE5vuM7D6TsOcJ2Yw0c3vaBR3VOsJFVTx9xvwnAvhgU5Ii8Gd6+I11qNHwndDscIm0HXg==", "signatures": [{"sig": "MEUCIQDpS1xV7kfSXSn3P5+HR7hOr6M17E9sE2K7FYzVL2qqhQIgJsdWhAL8lbiHM4j1mCVhRGqhj1AzSz+JP1FRV3xlAk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2489684}, "main": "./rollup.android-arm64.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.12.1_1709705009296_0.2924995945384046", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-android-arm64", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "8833679af11172b1bf1ab7cb3bad84df4caf0c9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-BSbaCmn8ZadK3UAQdlauSvtaJjhlDEjS5hEVVIN3A4bbl3X+otyf/kOJV08bYiRxfejP3DXFzO2jz3G20107+Q==", "signatures": [{"sig": "MEQCIFZqk6MxZgR4Qg9D4ISqt4cipMMCPoUa9ZvJb8uAyHCXAiBy2Xxg5a/z207DaJy6UGrkqB3T1kXi0krCttmjn/rxPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2488276}, "main": "./rollup.android-arm64.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.13.0_1710221307406_0.47086175270516195", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-android-arm64", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "af4967c97656eac6a6a2ead3a3fb9aaa12629dff", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-LwFHuPj6EN8DoflWz/J7DKncbV8VrPhJeweqDIJabavZ9tgDd+LONX0IT4CxxbgYWHbRl/IlGJnW6cWaYaH7VQ==", "signatures": [{"sig": "MEQCIExoviAdkTnatNudB48EFXK5tLXWYoQkAxFUPBe0aTbHAiAi92827b+c0AcmExpzVPN3+tfCftfnPfPXH9M0G944EQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485398}, "main": "./rollup.android-arm64.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.13.1-1_1711265956004_0.6806146010212806", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-android-arm64", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "c89a55670e1179ed7ba3db06cee0d7da7b3d35ce", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-TrTaFJ9pXgfXEiJKQ3yQRelpQFqgRzVR9it8DbeRzG0RX7mKUy0bqhCFsgevwXLJepQKTnLl95TnPGf9T9AMOA==", "signatures": [{"sig": "MEUCIBKy8r2Vez2jx6bqSKr0FqmZKrJ57qloo4S3lf9i97LDAiEAnyFxbX23P2h+5gT6icQ6rpkqlzXqCrv9lwNyHP1iqes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485396}, "main": "./rollup.android-arm64.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.13.1_1711535255253_0.9636238099093142", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-android-arm64", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0d2448251040fce19a98eee505dff5b3c8ec9b98", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-GdxxXbAuM7Y/YQM9/TwwP+L0omeE/lJAR1J+olu36c3LqqZEBdsIWeQ91KBe6nxwOnb06Xh7JS2U5ooWU5/LgQ==", "signatures": [{"sig": "MEUCIBdaJre5UYgepvgi7wybPqSJV4s+k+o4lKML7cwu2qAgAiEAn2+DpYDgrP8u00rP2/SLAPSnsBdPgKZxUkWS4+53v7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2485396}, "main": "./rollup.android-arm64.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.13.2_1711635211224_0.4620369965442084", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-android-arm64", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "81bba83b37382a2d0e30ceced06c8d3d85138054", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-fI9nduZhCccjzlsA/OuAwtFGWocxA4gqXGTLvOyiF8d+8o0fZUeSztixkYjcGq1fGZY3Tkq4yRvHPFxU+jdZ9Q==", "signatures": [{"sig": "MEYCIQCtQfE+LYuxQGS9cCII6DWmzbrWoxS9RwlpKaihW+VGnwIhAKwgfodSR4RliYDvj5nCAHVCY/B/IjhwYMtY87bWp4fj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2494996}, "main": "./rollup.android-arm64.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.14.0_1712121764191_0.2276901440583412", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-android-arm64", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "154ca7e4f815d2e442ffc62ee7f64aee8b2547b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-Y/9OHLjzkunF+KGEoJr3heiD5X9OLa8sbT1lm0NYeKyaM3oMhhQFvPB0bNZYJwlq93j8Z6wSxh9+cyKQaxS7PQ==", "signatures": [{"sig": "MEUCIFRfTE9dfx4Ytc4pvca6j0BATzRiz8zbGikTCTm/cSLxAiEAsOgHRw/PtlB2GBcvh3pAy51GsB5ERW4Qu/sAg4IMmTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2508308}, "main": "./rollup.android-arm64.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.14.1_1712475335512_0.3266433139314504", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-android-arm64", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "08a2d2705193ebb3054941994e152808beb5254e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-lAarIdxZWbFSHFSDao9+I/F5jDaKyCqAPMq5HqnfpBw8dKDiCaaqM0lq5h1pQTLeIqueeay4PieGR5jGZMWprw==", "signatures": [{"sig": "MEQCIEvn0RYZJi9s+rPTZA8WQfNgTRKzL+fwcVnoDWFwQ05YAiB9W5BmUjQ3jfxILgx4CYbgLo6NZghjpLTZwROykXOlYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2500180}, "main": "./rollup.android-arm64.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.14.2_1712903015546_0.05296136677033547", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-android-arm64", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b26bd09de58704c0a45e3375b76796f6eda825e4", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-eQK5JIi+POhFpzk+LnjKIy4Ks+pwJ+NXmPxOCSvOKSNRPONzKuUvWE+P9JxGZVxrtzm6BAYMaL50FFuPe0oWMQ==", "signatures": [{"sig": "MEYCIQCEA+T72gC2QGoXLguzvbFqNNudnklIhwv30NHBaeBrzwIhAOtpxhrxO0Q0f+dxDmOyHJajoxRPqv6um+LSmykclaCa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2505412}, "main": "./rollup.android-arm64.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.14.3_1713165507986_0.0294291404299305", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-android-arm64", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "a2bdafdb753ece571956289a5ba8c37af748bd0c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-5UywPdmC9jiVOShjQx4uuIcnTQOf85iA4jgg8bkFoH5NYWFfAfrJpv5eeokmTdSmYwUTT5IrcrBCJNkowhrZDA==", "signatures": [{"sig": "MEUCICtVIHADr3I7lhVWFw5EzG/IEJTPqe0Y2eL1KvMT6oX4AiEA9/CMu1LGpJUkgVaxgCXaiBQqJD4PAl5jCa80X9Ra9pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557444}, "main": "./rollup.android-arm64.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.15.0_1713591428530_0.0359154142677105", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-android-arm64", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e81fa0fd632ae1c9e6837565b929c95e7bfa0c5a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-JltUBgsKgN108NO4/hj0B/dJYNrqqmdRCtUet5tFDi/w+0tvQP0FToyWBV4HKBcSX4cvFChrCyt5Rh4FX6M6QQ==", "signatures": [{"sig": "MEYCIQCzeDy+dzYatQY9HoePa5g8B7ZXQq21aS4Yv0D8VSaOLQIhAKMKum2FoMweWnbQ2+HwCItv0FobZ4uL45pk1s9Ktbe3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557444}, "main": "./rollup.android-arm64.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.16.0_1713674505522_0.6978039254064465", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-android-arm64", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e7bd4f2b8ec5e049f98edbc68d72cb05356f81d8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-ttWB6ZCfRLuDIUiE0yiu5gcqOsYjA5F7kEV1ggHMj20FwLZ8A1FMeahZJFl/pnOmcnD2QL0z4AcDuo27utGU8A==", "signatures": [{"sig": "MEUCIFQpii3QSYiqCW36dM5sG6BJ58rBM2GnxFlLXglYnKhDAiEAsLqWNR57YFZesRqidVCsyD9TdklPdu13A4bfMkO7dYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557444}, "main": "./rollup.android-arm64.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.16.1_1713724193705_0.8268136367830183", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-android-arm64", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "f50f65d0c3b8b30d070d8616b2dfc0978dd588bd", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-5/W1xyIdc7jw6c/f1KEtg1vYDBWnWCsLiipK41NiaWGLG93eH2edgE6EgQJ3AGiPERhiOLUqlDSfjRK08C9xFg==", "signatures": [{"sig": "MEUCIQDTGNscyZgowuNDVrq2/0UfyQieRygh6t2mMrppciRs0QIgeDxxw86qkREWZZ2xoSXVW9xTcVU0L9/Fbjat8axghPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557444}, "main": "./rollup.android-arm64.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.16.2_1713799150064_0.06421275672431226", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-android-arm64", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "c0a15028fc76573503b83e257fcf30748df7ded2", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-vGl+Bny8cawCM7ExugzqEB8ke3t7Pm9/mo+ciA9kJh6pMuNyM+31qhewMwHwseDZ/LtdW0SCocW1CsMxcq1Lsg==", "signatures": [{"sig": "MEYCIQCO/apDPi1+NK0YKZPqj21Pc81sezn61Kuy5L0v6l4lPQIhAOvbjPYPNYrt9tifR0sYhvvWu4wI6eboFyNKMi52ASwJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557444}, "main": "./rollup.android-arm64.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.16.3_1713849150803_0.3465061688189326", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-android-arm64", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "ffb84f1359c04ec8a022a97110e18a5600f5f638", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-Bvm6D+NPbGMQOcxvS1zUl8H7DWlywSXsphAeOnVeiZLQ+0J6Is8T7SrjGTH29KtYkiY9vld8ZnpV3G2EPbom+w==", "signatures": [{"sig": "MEQCIFodGNoQwJp0n9DVLBOLe7N3rI6iIoezj5++ADyswnzdAiAKgKdmr5mWzBly+aNj9muZXGmeyFJsdKn9LcKBbYaUhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2557444}, "main": "./rollup.android-arm64.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.16.4_1713878103277_0.2837691934496718", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-android-arm64", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "be0cac9af51c9c9b4b064f335fb9886b95b1df8a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-+kjt6dvxnyTIAo7oHeYseYhDyZ7xRKTNl/FoQI96PHkJVxoChldJnne/LzYqpqidoK1/0kX0/q+5rrYqjpth6w==", "signatures": [{"sig": "MEUCIFVzkhGbc0lSSJHtBSuyAbfGBs4LscywekEKic9FbU/aAiEA3wXETvZ9sqdiU5TREZOZPYV43mODUrQYf96pTbnCCFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2570116}, "main": "./rollup.android-arm64.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.17.0_1714217388407_0.2644193044775507", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-android-arm64", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e32d5e6511a49ddd64c22f8b3668049f63b7b04c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-piwZDjuW2WiHr05djVdUkrG5JbjnGbtx8BXQchYCMfib/nhjzWoiScelZ+s5IJI7lecrwSxHCzW026MWBL+oJQ==", "signatures": [{"sig": "MEQCIGIxJnIJbn6/YyWyDr1EgxqAMzjCIGGDYSIlgFUJZnfQAiA3KH4fnWZJz3pmK6+GPqLM/GV7H8FdG+NLhpsLelNk5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2570116}, "main": "./rollup.android-arm64.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.17.1_1714366669327_0.5079199678716195", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-android-arm64", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "5aeef206d65ff4db423f3a93f71af91b28662c5b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-yeX/Usk7daNIVwkq2uGoq2BYJKZY1JfyLTaHO/jaiSwi/lsf8fTFoQW/n6IdAsx5tx+iotu2zCJwz8MxI6D/Bw==", "signatures": [{"sig": "MEUCIQC/YTpNvQWSUV2b7Ym/WW8AA2EPv54XZVp6P0v1hpBT+QIgBD4A7a6PeW4i/ePwp3hddHc3pgxZRnUtJmQhy9pSAeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2570116}, "main": "./rollup.android-arm64.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.17.2_1714453240631_0.7055604571419383", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-android-arm64", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "97255ef6384c5f73f4800c0de91f5f6518e21203", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-avCea0RAP03lTsDhEyfy+hpfr85KfyTctMADqHVhLAF3MlIkq83CP8UfAHUssgXTYd+6er6PaAhx/QGv4L1EiA==", "signatures": [{"sig": "MEYCIQDn/XW8kjAGFtiRfEyCQ4EkSpMhz7wTnQ0kBQT+D8uhsgIhAMh9lgJ7R6tW/HpH+prmkO4u7/JG2FtV9ExQc74IkBjY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2565708}, "main": "./rollup.android-arm64.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.18.0_1716354219087_0.921191547440773", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-android-arm64", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "82ab3c575f4235fb647abea5e08eec6cf325964e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-F/tkdw0WSs4ojqz5Ovrw5r9odqzFjb5LIgHdHZG65dFI1lWTWRVy32KDJLKRISHgJvqUeUhdIvy43fX41znyDg==", "signatures": [{"sig": "MEUCIQCJqN0Y1QfuX7VVLzqpNkVmHBSMTqp3eEd+0kzj+GjFNgIgaQHFB6wCgZU0cdQxbfczy4BSKEod0UgDMJmVFTpC5Ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2271412}, "main": "./rollup.android-arm64.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.18.1_1720452308160_0.2744288818474847", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-android-arm64", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e1a6d4bca2eb08c84fd996a4bf896ce4b6f4014c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-RDxUSY8D1tWYfn00DDi5myxKgOk6RvWPxhmWexcICt/MEC6yEMr4HNCu1sXXYLw8iAsg0D44NuU+qNq7zVWCrw==", "signatures": [{"sig": "MEUCIDcW7DjvNahWy4CR/95me+eTnRa0peoks/JPbvYemgPXAiEAn+GgBabwclcoinim9WrVx0feFG/WtMEqii3JavaC5fE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2241076}, "main": "./rollup.android-arm64.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.19.0_1721454371243_0.5059348381430184", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-android-arm64", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "93de4d867709d3313794723b5afd91e1e174f906", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-thFUbkHteM20BGShD6P08aungq4irbIZKUNbG70LN8RkO7YztcGPiKTTGZS7Kw+x5h8hOXs0i4OaHwFxlpQN6A==", "signatures": [{"sig": "MEYCIQCZxP/McWtOmnoPQuXg6ljXwqzi0rsELc8Vi8osW9OEugIhANwc39v2mzgGk9L2WJ7OKKPrn6MV8kRMqr9ABBnRGiW2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2155060}, "main": "./rollup.android-arm64.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.19.1_1722056037834_0.3483589711179551", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-android-arm64", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "5d3c8c2f9742d62ba258cc378bd2d4720f0c431c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-k0OC/b14rNzMLDOE6QMBCjDRm3fQOHAL8Ldc9bxEWvMo4Ty9RY6rWmGetNTWhPo+/+FNd1lsQYRd0/1OSix36A==", "signatures": [{"sig": "MEUCIQCa1d8AgRLWoy5v5I3Tx3GTy+vPKJc6w8OD3z8Y8/MnawIgfgvsuvG2GsXE1oeG9kjL2itsJCFqOwM8nI5xoVj2i7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2155060}, "main": "./rollup.android-arm64.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.19.2_1722501170201_0.12586707424354215", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-android-arm64", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "64161f0b67050023a3859e723570af54a82cff5c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-u00Ro/nok7oGzVuh/FMYfNoGqxU5CPWz1mxV85S2w9LxHR8OoMQBuSk+3BKVIDYgkpeOET5yXkx90OYFc+ytpQ==", "signatures": [{"sig": "MEQCIHPnn39Ok5uY8eziIvTkafNfcjHFElHHrxLgSd1W0lE4AiBfxoHl9JdHH9RkBixe6DEGxR+xAOt71hg+29GoKc5TCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2152708}, "main": "./rollup.android-arm64.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.20.0_1722660527667_0.1766018396669995", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-android-arm64", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "7e7157c8543215245ceffc445134d9e843ba51c0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-a1sR2zSK1B4eYkiZu17ZUZhmUQcKjk2/j9Me2IDjk1GHW7LB5Z35LEzj9iJch6gtUfsnvZs1ZNyDW2oZSThrkA==", "signatures": [{"sig": "MEUCIQCiO6sJh+iULwmSGkD6sZa8VXSgmu7PhAqacz+l/g7VBQIgJDbMdwF9dwLkPeWini5QL9v1IoKsSICYrM1izVPJ+bA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2106628}, "main": "./rollup.android-arm64.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.21.0_1723960531765_0.7701309954639484", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-android-arm64", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "fa3693e4674027702c42fcbbb86bbd0c635fd3b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-t1lLYn4V9WgnIFHXy1d2Di/7gyzBWS8G5pQSXdZqfrdCGTwi1VasRMSS81DTYb+avDs/Zz4A6dzERki5oRYz1g==", "signatures": [{"sig": "MEYCIQCE+9W5La6ZgK2z96xuDfVaiTrWxrwZc51YqKNcuVaazwIhAKXakjGOK3UF+RrYW6fxjkW/3kO+ZPAGH6J+0JLboBVM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2100228}, "main": "./rollup.android-arm64.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.21.1_1724687650268_0.36515582423633797", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-android-arm64", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "baf1a014b13654f3b9e835388df9caf8c35389cb", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-xGU5ZQmPlsjQS6tzTTGwMsnKUtu0WVbl0hYpTPauvbRAnmIvpInhJtgjj3mcuJpEiuUw4v1s4BimkdfDWlh7gA==", "signatures": [{"sig": "MEUCIQCiWUq9i22MeaiiNxiwlxu/J1+NrfCuMksu5yYFvGuNdwIgVbO/5e/BzvYexvxeVUwNwOLMoHmjW630VMc3H3ubU4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2097988}, "main": "./rollup.android-arm64.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.21.2_1725001462953_0.47319863400771256", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-android-arm64", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b94b6fa002bd94a9cbd8f9e47e23b25e5bd113ba", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-zrt8ecH07PE3sB4jPOggweBjJMzI1JG5xI2DIsUbkA+7K+Gkjys6eV7i9pOenNSDJH3eOr/jLb/PzqtmdwDq5g==", "signatures": [{"sig": "MEYCIQDwzPw1V5TIrFgKfdpeHqWcmSDtMJrRXDzKLcY4cdjGfwIhAID7z0HsdvkNbnmFMEPu8yzFk8vje9VYOuoCRhzNnGfW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2050852}, "main": "./rollup.android-arm64.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.21.3_1726124746502_0.539882672756643", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-android-arm64", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "7a44160a14017fa744912d7037c7d81d6f8a46e7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-ETHi4bxrYnvOtXeM7d4V4kZWixib2jddFacJjsOjwbgYSRsyXYtZHC4ht134OsslPIcnkqT+TKV4eU8rNBKyyQ==", "signatures": [{"sig": "MEQCIBD3sf50esIH0p6sqOovPWrzl9+JfREjntUi2qg7+7keAiBU5KzY2GKeISl03ffrX0S6DYd84ALnKr5+8C2DwBY7Fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2050988}, "main": "./rollup.android-arm64.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.0_1726721728535_0.7595177155062254", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-android-arm64", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "80bcf4a0d65c7c837a6c8196eee3c10052617e1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-Cr/dpKRc4tjK13SCZJrSDXSaKjL/fekn04BWMCJ+Pj4vPCp8rixvtArrnWUYycOdRNi7kx3MSClcvEP7C2nvCw==", "signatures": [{"sig": "MEUCIG3sptHkBAwarE9utFcETkC+FqR0qai7tfMKA5/NbDyaAiEAjzUywmuA/DT+evRW+P74kK9xZhH5w14wZS7TEkUpVZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2051820}, "main": "./rollup.android-arm64.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.1_1726820510477_0.9508128985874043", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-android-arm64", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d97ed02a950061adc2056d6d2d6df8f05d877ae9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-I+B1v0a4iqdS9DvYt1RJZ3W+Oh9EVWjbY6gp79aAYipIbxSLEoQtFQlZEnUuwhDXCqMxJ3hluxKAdPD+GiluFQ==", "signatures": [{"sig": "MEYCIQCB5ykC2BDvG4oKTViwr6wxofcErUmo1Dz740xsFSOgtQIhAOFZiIEoG3bsnKkW5/iX5+JrhlBldEcOTV9XW590PSSq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2051820}, "main": "./rollup.android-arm64.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.2_1726824822221_0.8892680988826835", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-android-arm64", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "c5d07d73abf36a963d20961115d672e02093550a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-/VeeTIa8AvGO2yTqKG1Yrg3EW5EmJMgLC9gd2c2mlqaJQF0U+v5f8V0ZTK5rba8VzXvAQ96tV7aZhhckOciHqA==", "signatures": [{"sig": "MEUCIQC7dvhIBSao6hj+RDQEW9Hbxa1F+ziY4FlPq62cFoJNSAIgcfbYto4H+//6PMH87bl3bGHwTqCmtr8Fe743kK4Wo80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2051822}, "main": "./rollup.android-arm64.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.3-0_1726843674425_0.4075610255137203", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-android-arm64", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d1e3f51a2b17bbae91d5bc2c3cb5901a6075fb48", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-52cRgrMaz2JxX0a/wfnnrkToHGzMWrI18X5n+e73Hz/BFPEV4QhglW5z8cOs6kyYHB9tQPo7kjMjFhxwj72SXg==", "signatures": [{"sig": "MEYCIQCKMdztY9TKuhlKGV6NB3cHywrCCigI2whep0JwRyRhtAIhAMEr5z2C1wpN7r/qJ8D7qOLvUaOWVvV/0avSdEyiU6V4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2051820}, "main": "./rollup.android-arm64.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.3_1726894985631_0.7422389324522587", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-android-arm64", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "654ca1049189132ff602bfcf8df14c18da1f15fb", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-VXoK5UMrgECLYaMuGuVTOx5kcuap1Jm8g/M83RnCHBKOqvPPmROFJGQaZhGccnsFtfXQ3XYa4/jMCJvZnbJBdA==", "signatures": [{"sig": "MEYCIQCCXT3ljW+3Qm/o4YxdL3yojvL7dVqbGYeHYsLA5VjkvAIhAPqoSHb7FRPV3/4co+sBWngWCCHQai6yNXYXxt+GBm5I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2051820}, "main": "./rollup.android-arm64.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.4_1726899079462_0.14405384188084724", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-android-arm64", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "08270faef6747e2716d3e978a8bbf479f75fb19a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-S4pit5BP6E5R5C8S6tgU/drvgjtYW76FBuG6+ibG3tMvlD1h9LHVF9KmlmaUBQ8Obou7hEyS+0w+IR/VtxwNMQ==", "signatures": [{"sig": "MEUCIHPDJeyYAVTJE/BIFVa6cgswo/arRVlazN3p085HaexbAiEAztwBdzyYpjbSFAAA2zbWTGUHyKtWSDFet9VM16OlyXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2054124}, "main": "./rollup.android-arm64.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.22.5_1727437692678_0.15506188216866268", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-android-arm64", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0594aab393e7b13c4cd7f21bb72d953c128cdae4", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-rEFtX1nP8gqmLmPZsXRMoLVNB5JBwOzIAk/XAcEPuKrPa2nPJ+DuGGpfQUR0XjRm8KjHfTZLpWbKXkA5BoFL3w==", "signatures": [{"sig": "MEYCIQDQ3pf6YKeZciu+NVjX8xN4Xcn7iSCNUB68qrQefXwsagIhANYAm/LQ7TG4SsnbOZKtJfAfBKTXzzyLWd+6qM1oLJ09", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2054124}, "main": "./rollup.android-arm64.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.23.0_1727766603278_0.3014750637066952", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-android-arm64", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "2ffaa91f1b55a0082b8a722525741aadcbd3971e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-ijLnS1qFId8xhKjT81uBHuuJp2lU4x2yxa4ctFPtG+MqEE6+C5f/+X/bStmxapgmwLwiL3ih122xv8kVARNAZA==", "signatures": [{"sig": "MEUCIAFCNMjKztJP/7lhBi8etsOmaQA3HABDX2wgvJBbIu6MAiEA4KzUDt3Whj0I1H6jrAueYPO+tNybesdTqx3pr+e6VSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2062636}, "main": "./rollup.android-arm64.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.24.0_1727861836919_0.636535351552231", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-android-arm64", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6f8fee7fa7dc4840c95cf2a270e37faeb0512a30", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-y65R3hM9sJVAXV3qh/dJ5o2OCVzwy6d994qmi+rGw1i1onYY5AoV9dREDYoizaZvc9esEqOs07CyFgPzz4DBqg==", "signatures": [{"sig": "MEUCIQDjRktxEVd0UM3aFdFw4nK+KuNt9mJ6AuL4jBnoTvgrRgIgMBzMuBsBoGH1kRTiiqW9bvUqfTSNtDyZ3zP9AF8Eq9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2233788}, "main": "./rollup.android-arm64.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.24.1_1730011379282_0.8749667129664866", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-android-arm64", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "160975402adf85ecd58a0721ad60ae1779a68147", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-iZoYCiJz3Uek4NI0J06/ZxUgwAfNzqltK0MptPDO4OR0a88R4h0DSELMsflS6ibMCJ4PnLvq8f7O1d7WexUvIA==", "signatures": [{"sig": "MEUCIQDvaoNjwWNeKt2BUhJ9AQ6BpigPK7V1nPvIoVUHnHmqIgIgcnEXfCbdwcRYwiqrpyCYCoE2WdO4SO/ZQRT5L/yqKKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2233788}, "main": "./rollup.android-arm64.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.24.2_1730043604906_0.3989607417404504", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "fb4978a05cae9926793f6ff0b74404b36277fc7c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-00AtPMShFaBiWyXAZzaWEzc+P0cAFVtl+I41njsOCOkVLl7Y4qVmUVkvlYdQ8qSBXAaSRD8GsWpBNU4lvgDgkw==", "signatures": [{"sig": "MEUCIQD1glAzKpKEnIWVcB6nrRgcgduGw0qw80F8ujgrhTUeDQIgRPBnIa+yUn0603NsLIIb5p3SIVB2afodcloXFFyF7HQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2233790}, "main": "./rollup.android-arm64.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.25.0-0_1730182507050_0.6589198460705343", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-android-arm64", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "197e3bc01c228d3c23591e0fcedca91f8f398ec1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-iAHpft/eQk9vkWIV5t22V77d90CRofgR2006UiCjHcHJFVI1E0oBkQIAbz+pLtthFw3hWEmVB4ilxGyBf48i2Q==", "signatures": [{"sig": "MEYCIQDb6ak13gB5fgAhu/RDybIx16+0H4dfZfDsiJ64/V7/ygIhAPvNBGC2JYyvr6gZ4GZri3aqWRFVIZBjQu/A+KxJiiqE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2233788}, "main": "./rollup.android-arm64.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.24.3_1730211244516_0.9338505513389832", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-android-arm64", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "96e01f3a04675d8d5973ab8d3fd6bc3be21fa5e1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-j4nrEO6nHU1nZUuCfRKoCcvh7PIywQPUCBa2UsootTHvTHIoIu2BzueInGJhhvQO/2FTRdNYpf63xsgEqH9IhA==", "signatures": [{"sig": "MEQCIHM2YD2FHb8b8uQJdXH1DjYEUh9deAPo+g6ZLER9Mt+zAiBnvfTntDUBTioUOLn+P0POM+P8/oyOVhw/BItsHVwpkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2224124}, "main": "./rollup.android-arm64.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.24.4_1730710026238_0.21897442428066882", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-android-arm64", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "04f679231acf7284f1f8a1f7250d0e0944865ba8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-/Y76tmLGUJqVBXXCfVS8Q8FJqYGhgH4wl4qTA24E9v/IJM0XvJCGQVSW1QZ4J+VURO9h8YCa28sTFacZXwK7Rg==", "signatures": [{"sig": "MEQCIAkpJdrxiKw00S5d/zf5Na/OtHo+GUmqVJiy9Sw7bP25AiAUKEEIdSQvEBFvhukHH0In5hZu6hTdeUn8wuaCY50aAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2228732}, "main": "./rollup.android-arm64.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.25.0_1731141439417_0.2754948626835616", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-android-arm64", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "196a2379d81011422fe1128e512a8811605ede16", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-YJa5Gy8mEZgz5JquFruhJODMq3lTHWLm1fOy+HIANquLzfIOzE9RA5ie3JjCdVb9r46qfAQY/l947V0zfGJ0OQ==", "signatures": [{"sig": "MEUCIQC5S+nuK1kihEBdC2NJ4IuDaBZxtwoWC6Cgc08OaJSzkwIgEoLFCtdLxCdzxNc710RQp/d8f+Cq92n2WNR+ErpwUks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2228732}, "main": "./rollup.android-arm64.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.26.0_1731480296725_0.12758932743096207", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b4d5ef574b5583177ec2ed70a943fa5abe727833", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-0VIktojlbmJL/+AWJj8Qz3Dd6L3uR7rSzJ292XLb0bUB9mumsd9mWhNSOLCHpqhfaM3cgbi2wg1kJEKmwHTsbA==", "signatures": [{"sig": "MEQCIAtO5ujveHSYflDlP+YHGtjXXCLg2A9yJqYVg54X/qLlAiAltZzYGVZiVUiwLcNUUDVAFmgNIsKa/os4bkCI2jQS/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2228734}, "main": "./rollup.android-arm64.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.0-0_1731481391587_0.08564498625549177", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0c5534d443f440589a84fe424951f5a24553b997", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-cx2VlRa6Q7IA0ma0E9Ufh8jGaaoyrR5tFIg+E4vJPmi2UnxxqkXGyrxTAn62DQD/zz8aegPfl2MJz1CFtJZ0Pw==", "signatures": [{"sig": "MEUCICsMyhQTm1ueKmMfy41w3R1HbtBecpXFqsi+wYFlnVPEAiEA+vyRbv+glg+z8cax7fRm6GlI7ticmVNG5OVPJhPqB0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2228734}, "main": "./rollup.android-arm64.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.0-1_1731565988326_0.5662679670426896", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-android-arm64", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "298172fd28b17bc745efad8128a4a1365e80b747", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-cBUOny8GNXP++gN00Bo5L04I2oqUEFAU0OSDb+4hqp4/R/pZL/zlGzp7lJkhtPX52Rj+PIe0S8aOqhK4hztxHQ==", "signatures": [{"sig": "MEUCID1LSFA9FIS2JTXjWV8PU3M4F7Pq+WnbeabxTAy8PMGnAiEAizxR+vfZzxYCJ+EMcJIqTn3qUTKx3KboiG1CUzhzsUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231740}, "main": "./rollup.android-arm64.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.0_1731667233601_0.5257661846832868", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-android-arm64", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "32e941a4d6cd0d09ecf3a4b435b7292d3f67bef3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-Bytc1IdUMhbNK6CayAW2+W8/AzwrA1p4cCvw4SwVLKoqulfm81IbvPv15N0uYVRaq8HTwfvI/5XcwxRMtdRRwg==", "signatures": [{"sig": "MEQCIBbes1pnbsgbf/xaWcmJloG8wYAkxFeI02vQZ6CzFzLJAiAP85asTeTSun5jNqeBmDG+Rnmk+Pc8lO1V1bidnvsLjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231742}, "main": "./rollup.android-arm64.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.1-0_1731677284968_0.42149830637322516", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-android-arm64", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "fe61b1a6c1203c201fb7b5a91811c951095ea993", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-c3lA8NyEAq4ceVIJgXtjdVCxYR/Xp4y6sxAfCKZhM9Ve4ME98yG2/RvFdJOImO3eKtxqRAJRX1JZCZCnlcxcpQ==", "signatures": [{"sig": "MEQCIFrgqbp6cQi2mR9IuDsBodhUTsRqPENmV+8PkqJ/mI9eAiBeT9vGKKDDgwHxPHGvlvwIHTRQxsnu2DNGps4vECM1Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231742}, "main": "./rollup.android-arm64.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.1-1_1731685079682_0.796434418202028", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-android-arm64", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b4486a7cc1378ff89547e51a7021f6dd4b6eec59", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-WXrtqF2zOOTGjE6pNDF5oYPBlwpopSGaQPIZULbMKvchT7OyYzmUnEim0ICNAlz4qHYs4vxJOn1S4aLd930EKA==", "signatures": [{"sig": "MEUCIHp8JN0Pz1J4FdvhV2CBEHOArFaAA2CYUZGB/VqhZ1P9AiEA7B8q+GoY/BKyeSDiMA2TcZWIJx8iIjtgx5Qs4H6XCJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231740}, "main": "./rollup.android-arm64.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.1_1731686859112_0.2080017475563345", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-android-arm64", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "272fcb6416c60b2225192379fa2c5e63b48f19dc", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-xsPeJgh2ThBpUqlLgRfiVYBEf/P1nWlWvReG+aBWfNv3XEBpa6ZCmxSVnxJgLgkNz4IbxpLy64h2gCmAAQLneQ==", "signatures": [{"sig": "MEYCIQCAIOnXKklP4RCP3y5TpNpAOz7j/d6NN0zQSYOZ3J5YcQIhAOalXIeA6QUyGGlFhZEfDnknnq1Tjc7kJ3DspkFGE2pt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231740}, "main": "./rollup.android-arm64.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.2_1731691200310_0.943570590161529", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-android-arm64", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "de840660ab65cf73bd6d4bc62d38acd9fc94cd6c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-LJc5pDf1wjlt9o/Giaw9Ofl+k/vLUaYsE2zeQGH85giX2F+wn/Cg8b3c5CDP3qmVmeO5NzwVUzQQxwZvC2eQKw==", "signatures": [{"sig": "MEYCIQCXy076pzAccqT2vEkr3rcwlGhgbIDKfxJ4ebwIAoKNFgIhAI+sLbn+00MeXwNB0sA2cTkRnnlMRmKf8KzbOjsqSSgd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231740}, "main": "./rollup.android-arm64.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.3_1731947973656_0.5157224851403006", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-android-arm64", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0474250fcb5871aca952e249a0c3270fc4310b55", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-wzKRQXISyi9UdCVRqEd0H4cMpzvHYt1f/C3CoIjES6cG++RHKhrBj2+29nPF0IB5kpy9MS71vs07fvrNGAl/iA==", "signatures": [{"sig": "MEQCIEgighi9+m8MWpyOtz+5gW1oceZcUogTByTFgQeya7igAiBPmCwx/BUDV03GbCHhPiNSTGAW1dANAjrUN9PBDz/YMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2232892}, "main": "./rollup.android-arm64.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.27.4_1732345218748_0.09955926445444274", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-android-arm64", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "78a2b8a8a55f71a295eb860a654ae90a2b168f40", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-eiNkznlo0dLmVG/6wf+Ifi/v78G4d4QxRhuUl+s8EWZpDewgk7PX3ZyECUXU0Zq/Ca+8nU8cQpNC4Xgn2gFNDA==", "signatures": [{"sig": "MEYCIQCP7OQ0BXc/3Hrp6L40jVQxkAcbyBRkas48tAVz2Mi0LQIhAJAUbtwFgJPvf6VAzx9HlDR+Dv9R98EOuUVhsMC0EIrD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2235492}, "main": "./rollup.android-arm64.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.28.0_1732972545029_0.1506303589077458", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-android-arm64", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "17ea71695fb1518c2c324badbe431a0bd1879f2d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-EbkK285O+1YMrg57xVA+Dp0tDBRB93/BZKph9XhMjezf6F4TpYjaUSuPt5J0fZXlSag0LmZAsTmdGGqPp4pQFA==", "signatures": [{"sig": "MEYCIQC34a/ZDbvA3VjnVTvQuD1pAm8i4WKPUheTkU3+y+61gQIhAI2SUgIoD3M//02E3A3MP6Ynk76nVF2Eq7+GzEj84blT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231332}, "main": "./rollup.android-arm64.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.28.1_1733485494269_0.871678849031668", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "5d2a9574e0721f67108c4456de2e8b92c1074fb6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-eMjbeyy3kIiS2WWeHkboMdCTMYlIBU2ocZHhUU4UEpxcAe+vW8j7u8vOSIqXEHchLShjIFqBvUFi9frbSCVMZg==", "signatures": [{"sig": "MEYCIQDwl4ab1r1X5uQW13ZWHlFW6D/FsXxbNvsVfNg3UwqT5AIhALvsWkihGS/9yXJ+Fr2bFWQPNI7e4nzdM1uyUqnttM10", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2223270}, "main": "./rollup.android-arm64.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.29.0-0_1734331190994_0.014896512416635366", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "21b06d6f1705de53b8d433282ea69b6ea277902a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-6fxkd9JXTBHnOiq2JjcuOm6sPOtLOAc2NTUc++qAxfY20sLslOdZ/cxzNRMd6iu3Ra9ZV0vntz7I8KRZFSgEhA==", "signatures": [{"sig": "MEYCIQCcfNxdhUmh61kKRAzOgF+dCxetd0tkr/BguCOgfxnvHwIhAP9Y98kDcRVTIqN3BzM2BvOKNUe0xEyTIjkHRp7Elnqk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2223270}, "main": "./rollup.android-arm64.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.29.0-1_1734590249554_0.6901523073032452", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "0649cf001102808c241366b568b09c75fb8468cc", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-bp60JcErQYVcm5vIznWqWdJ/JvsGiOMPpn4q6UNLsZ3+nuf5Ognm+SKZD5bsT3/ye8ZiGynMiodU48pF8qnH/Q==", "signatures": [{"sig": "MEUCIHlkqLjBNTuGoGc2L8n5mlN5ZyV3S7lxYd5PriQSI6ejAiEAuaZ7LkUv9yQeJTpExXJvsFS8tB/6E1F12WDwKs5C2eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2223270}, "main": "./rollup.android-arm64.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.29.0-2_1734677760249_0.7436522444853646", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-android-arm64", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "039ab290f7a11a49ec6d5e7cb3031554f3e78372", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-L/7oX07eY6ACt2NXDrku1JIPdf9VGV/DI92EjAd8FRDzMMub5hXFpT1OegBqimJh9xy9Vv+nToaVtZp4Ku9SEA==", "signatures": [{"sig": "MEYCIQDDUdv51DX5uSQNc38wywaFtZ3NUQG6lGXtJ9DqRk/BDAIhANY1sNigacf0kHIyQ2xvMj7eLBSi1qXcQQhsULjdmzoh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2232420}, "main": "./rollup.android-arm64.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.29.0_1734719842317_0.9690313048824604", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-android-arm64", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "bd1a98390e15b76eeef907175a37c5f0f9e4d214", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-CaRfrV0cd+NIIcVVN/jx+hVLN+VRqnuzLRmfmlzpOzB87ajixsN/+9L5xNmkaUUvEbI5BmIKS+XTwXsHEb65Ew==", "signatures": [{"sig": "MEUCIGM/X8LULpI4tuYEybButjYgZmnvtD7H02qOYPKOyuxoAiEAoFLLfbexbcu7tzri4fkHluo2fbuMs5inUDKw72sZyqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2232420}, "main": "./rollup.android-arm64.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.29.1_1734765360089_0.016137248045506647", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "74ad77bc039e9e1d3ab48a5ec24fdc984f6b7730", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-T5ZlI7GED9SmYrmz3JBVTwpE71gvs9f6upodLjRwOwbEs6Jlpj9uRpOLVJq/8sZJi0k5K0dyKjW3unobPDR8Kw==", "signatures": [{"sig": "MEYCIQDUVpov8dd3/fgdaPnroN/l7Ak6jVVBGn4e0ZDOOWx8fAIhAPvyXmU975RsYMfBo5ZrRdh5KCOGbyofLt6dAybXSr/z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2232422}, "main": "./rollup.android-arm64.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.30.0-0_1734765432497_0.6682178305957569", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-android-arm64", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "76091ed17f622e0928c2ac2800340bc9e27a1d5f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-/i98j75INBv+uz6Q31rqf+CSFIxDBghCH7xJ9ZjJIdpzt/5Y+WFA8tZzO44ylhOlZ3EMLcTZbhluogASuDjxKw==", "signatures": [{"sig": "MEQCIHE16wCINJSNng0mmui5Xyj4NGLgZdtx9JNAAIbLbYnvAiACN8kFzhj7oMKdhzNlrei4318ueOqSCDdWCOLq3XSDAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2230438}, "main": "./rollup.android-arm64.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.30.0-1_1735541534643_0.18495486156156837", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-android-arm64", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "cbc7e636a7aab984161fc045039bf3c6abb50083", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-mKRlVj1KsKWyEOwR6nwpmzakq6SgZXW4NUHNWlYSiyncJpuXk7wdLzuKdWsRoR1WLbWsZBKvsUCdCTIAqRn9cA==", "signatures": [{"sig": "MEUCIEFOYMJ8jHJluQAdg6EfQGabWF4jyY7zb/WTT2plOVAyAiEAv+X1BoREZCe6H/81ivi3MS6b25TAVC6lRl43FRNz5fc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2230436}, "main": "./rollup.android-arm64.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.29.2_1736078860483_0.6088588582290888", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-android-arm64", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "7e5764268d3049b7341c60f1c650f1d71760a5b2", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-vqrQdusvVl7dthqNjWCL043qelBK+gv9v3ZiqdxgaJvmZyIAAXMjeGVSqZynKq69T7062T5VrVTuikKSAAVP6A==", "signatures": [{"sig": "MEUCIQDSTYxddmPW8OkAMzIxUiEGlbuZ0KwKKvNfdToeu++NGAIgI9cGgNtois3qAp4Z2Rwi1NxLoQvwmzwzgXHGoCLQiS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2230436}, "main": "./rollup.android-arm64.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.30.0_1736145399213_0.5558140592790926", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-android-arm64", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "9d81ea54fc5650eb4ebbc0a7d84cee331bfa30ad", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-/NA2qXxE3D/BRjOJM8wQblmArQq1YoBVJjrjoTSBS09jgUisq7bqxNHJ8kjCHeV21W/9WDGwJEWSN0KQ2mtD/w==", "signatures": [{"sig": "MEUCIQCLhXzWhnYHwYwOKDZrcBUMli8s0A0KiiORGylyC10CMgIgaEJaxeRBB9wXCyYS0RI8Xi5qV7ipqyTxgLyVeKmw/b4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2230436}, "main": "./rollup.android-arm64.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.30.1_1736246150467_0.2660670908749456", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "cd6bcba2a52134298a0d73c2d6027c982acd4340", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-dXA8prsULJy0lJfNUzKI47pGVJcwlnutl7wIBV4zkW7+GtY9c2KygEO2jzrXZwjVP0x3FxVB6d8mzgR9x97LXw==", "signatures": [{"sig": "MEYCIQC4gFE0LoaMS7XDc8WUdrioqqtYsH1o4Dkzi7ovA+WlIwIhAJXEEF34mSVvZUwN6niKqoz8Ry3ZiVWcfqtpSkTYmlLv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2249126}, "main": "./rollup.android-arm64.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.31.0-0_1736834259784_0.05172144638639997", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-android-arm64", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "25c4d33259a7a2ccd2f52a5ffcc0bb3ab3f0729d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-iBbODqT86YBFHajxxF8ebj2hwKm1k8PTBQSojSt3d1FFt1gN+xf4CowE47iN0vOSdnd+5ierMHBbu/rHc7nq5g==", "signatures": [{"sig": "MEUCIQC5jgLdiwqM4hOkt3xZBbxy5kUDf5Yorz5ufTEWDIHWFwIgFhus/PHsQct6K2gTjZXZ/RX2rFlglM8p8k7Mm9QHNyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2271332}, "main": "./rollup.android-arm64.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.31.0_1737291405176_0.8412498667586892", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-android-arm64", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "846a73eef25b18ff94bac1e52acab6a7c7ac22fa", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-qhFwQ+ljoymC+j5lXRv8DlaJYY/+8vyvYmVx074zrLsu5ZGWYsJNLjPPVJJjhZQpyAKUGPydOq9hRLLNvh1s3A==", "signatures": [{"sig": "MEUCIC2+hHLhg498ZS7ggowBiijBZu0RSCuWFLGR0i3x/KgJAiEAm8kBHNGMR96TBLmTrA5ztiVABvnSXTDioERW/td/pD4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2272484}, "main": "./rollup.android-arm64.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.32.0_1737707253204_0.5742157236869534", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-android-arm64", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "069959281ef5a952a263d7b5f9b1d3ea4d4aa93d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-F0n3/jvk9zRQXUgoUKjt6K0oKwc8g0EFh12vfgNWmOCQ7n+Ogp/AXcyLbNYQ+y/4vxrGCu8POfJIlKgmuw5AoQ==", "signatures": [{"sig": "MEUCIFf/RnDxVsw8H2ofU0va1kmQ2XUO0PkLSEJx8+OklOluAiEAzQYD1b4aUCXHxYI+BvXh7Cs7401vY7KcWIytYwED2sM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2272486}, "main": "./rollup.android-arm64.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.33.0-0_1738053006737_0.7739420191816417", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-android-arm64", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b5c00344b80f20889b72bfe65d3c209cef247362", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-If3PDskT77q7zgqVqYuj7WG3WC08G1kwXGVFi9Jr8nY6eHucREHkfpX79c0ACAjLj3QIWKPJR7w4i+f5EdLH5Q==", "signatures": [{"sig": "MEQCIDBmMTuwfab8+2PqbDhBcqHWMZWenyFejnxDJ2CDakBNAiBO1RZEGO37Ow/+RSCOFpZNo9hCIoaaawni9uqDEL4m1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2272484}, "main": "./rollup.android-arm64.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.32.1_1738053195575_0.33088579939890694", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-android-arm64", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "530049f68ff2454435fecd23b2be356bb50994b5", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-CALerXF20lsoIVAEb/FNjnMIvF7v79PUq9NDL2y2sv2cPFC8AFJzE23BbaOvS0CPqsawaAcc+vwlju5v+mw2Pg==", "signatures": [{"sig": "MEUCIH0YfRsTyweV1xvY2Bb0rTXE8hFTuyRcWAE4aEowKY1KAiEAhRzAeqsRgW2Z5S/8VHh8fGA73fOihCNmbNBDMYlacUk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236}, "main": "./rollup.android-arm64.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.33.0_1738393917041_0.47014732848551044", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-android-arm64", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "667165775809f35ca1eaa872b07ec4b3ab92ea90", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-yVh0Kf1f0Fq4tWNf6mWcbQBCLDpDrDEl88lzPgKhrgTcDrTtlmun92ywEF9dCjmYO3EFiSuJeeo9cYRxl2FswA==", "signatures": [{"sig": "MEYCIQCMkkC3hSoKrWcUcdEynM+/Y4P48GOBp4qeWO+105yO8gIhAOJNCqoLWJ+GRyBEZxaOj8WUaPpRGPTQP4d9jzE8MDdH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236}, "main": "./rollup.android-arm64.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.0_1738399221487_0.061529830871402735", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-android-arm64", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "cbb3cad15748794b8dfec7f1e427e633f8f06d61", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-4H5ZtZitBPlbPsTv6HBB8zh1g5d0T8TzCmpndQdqq20Ugle/nroOyDMf9p7f88Gsu8vBLU78/cuh8FYHZqdXxw==", "signatures": [{"sig": "MEYCIQDVFrDgt88HsDB1RYy5NcwiPTGA6h0NWFbyRCs3k0ZiOQIhANorPr6kghBy9FGlfE8k35OR6WqBgV2VnZ/l5msTkm0D", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236}, "main": "./rollup.android-arm64.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.1_1738565891688_0.7098209722001447", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-android-arm64", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "17f9a9a9ee57e47839a697275d9149c065f8b7d7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-K5GfWe+vtQ3kyEbihrimM38UgX57UqHp+oME7X/EX9Im6suwZfa7Hsr8AtzbJvukTpwMGs+4s29YMSO3rwWtsw==", "signatures": [{"sig": "MEYCIQC+pp0Z1aNvib1oNED0N6+ukiRSSzSAT3Z/MM8qqDtsTQIhAOHzMIiMc0/RKmNnyaEjrAyxvfMGO/yAtaMrh1RAsD+N", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236}, "main": "./rollup.android-arm64.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.2_1738656598984_0.12078921721912383", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-android-arm64", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "850f0962a7a98a698dfc4b7530a3932b486d84c0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-1PqMHiuRochQ6++SDI7SaRDWJKr/NgAlezBi5nOne6Da6IWJo3hK0TdECBDwd92IUDPG4j/bZmWuwOnomNT8wA==", "signatures": [{"sig": "MEQCIGEuWFYHTPb0B76JUK54L5fwhwGGh+tGVjzCKISOCkiKAiBK4ztPnOzz4hTiWs4ktEVzy5o8CFROAlwoMjyBRwk1bg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236}, "main": "./rollup.android-arm64.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.3_1738747322133_0.9553003838859675", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-android-arm64", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "88d8b13c7a42231f22ac26d0abb1ad4dd8d88535", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-1aRlh1gqtF7vNPMnlf1vJKk72Yshw5zknR/ZAVh7zycRAGF2XBMVDAHmFQz/Zws5k++nux3LOq/Ejj1WrDR6xg==", "signatures": [{"sig": "MEUCIQCd+qHQWD0cZRqW5QRyaT5+CLTAg2d+vuT+ljClGbCoUgIgZzEaQA4HYsNm3rLaPQ8llliS5EbsFcu3rLLy3V6MtXE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2252236}, "main": "./rollup.android-arm64.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.4_1738791068852_0.31373599439482414", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-android-arm64", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "500406f6ad1d8cf39cbdb4af9decd47d8b6e46c6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-9/A8/ZBOprUjkrJoP9BBEq2vdSud6BPd3LChw09bJQiEZH5oN4kWIkHu90cA0Cj0cSF5cIaD76+0lA+d5KHmpQ==", "signatures": [{"sig": "MEUCIBo07/71R5uiysWQLnSA/sUPuvg6I2X3MN5lIucH+IJsAiEAldCqjosByhsjgGPY4p/SFyRLbPvEIdRqRyjywvb0Mss=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2245004}, "main": "./rollup.android-arm64.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.5_1738918379472_0.6488829732282451", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-android-arm64", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "88326ff46168a47851077ca0bf0c442689ec088f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-E8+2qCIjciYUnCa1AiVF1BkRgqIGW9KzJeesQqVfyRITGQN+dFuoivO0hnro1DjT74wXLRZ7QF8MIbz+luGaJA==", "signatures": [{"sig": "MEUCIFYDS9PEokrsqSB1PSbtO1eg83VM5jNEr7AiJPjKULNuAiEAj3fExmqZLx5q/5d+qyOYrGOr9yGVaVfjC6B5JlFKCRI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2242804}, "main": "./rollup.android-arm64.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.6_1738945925518_0.6710103698281769", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-android-arm64", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "b1ee64bb413b2feba39803b0a1bebf2a9f3d70e1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-<PERSON><PERSON>JpFUueUnSp53zhAa293QBYqwm94TgYTIfXyOTtidhm5V0LbLCJQRGkQClYiX3FXDQGSvPxOTD/6rPStMMDg==", "signatures": [{"sig": "MEYCIQCwc7xJ7GEDQwnJFYvK9e9NAGeWjd2n4xd3ntnG2xbpxAIhAJutG9Xr1ZmVsFrs0vFKmWdaAqrXjImYvfiqj17hjUhy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2242996}, "main": "./rollup.android-arm64.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.7_1739526836918_0.32431437277091013", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-android-arm64", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "4bea6db78e1f6927405df7fe0faf2f5095e01343", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-Gigjz7mNWaOL9wCggvoK3jEIUUbGul656opstjaUSGC3eT0BM7PofdAJaBfPFWWkXNVAXbaQtC99OCg4sJv70Q==", "signatures": [{"sig": "MEUCIAQ+LGJVPzGeHV31W5lMLo9QsE+hZz8r6E/m5Pv7rbMHAiEA9VpFbXI/QA+KsgEFfgdc3i0zatMG2hPqjiIVBAsMG/w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2242996}, "main": "./rollup.android-arm64.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.8_1739773583344_0.09990261959900582", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-android-arm64", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "128fe8dd510d880cf98b4cb6c7add326815a0c4b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-4KW7P53h6HtJf5Y608T1ISKvNIYLWRKMvfnG0c44M6In4DQVU58HZFEVhWINDZKp7FZps98G3gxwC1sb0wXUUg==", "signatures": [{"sig": "MEQCIF9fQG87PJyUNNL45KMfncgdpEwnC4JFTa8NRYLbsV5nAiB9jFw07swE1xzww04hMQxDBUYUb0WYgcfiKLSG+fA4Cg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2349420}, "main": "./rollup.android-arm64.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.34.9_1740814354464_0.2771228933979908", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-android-arm64", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "fa6cdfb1fc9e2c8e227a7f35d524d8f7f90cf4db", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-FtKddj9XZudurLhdJnBl9fl6BwCJ3ky8riCXjEw3/UIbjmIY58ppWwPEvU3fNu+W7FUsAsB1CdH+7EQE6CXAPA==", "signatures": [{"sig": "MEUCIAaGmNG1zeT5mUjr3hMhUic7QgjVvSivzGncRH0NVmD3AiEA0vXkLRYE+HTGgF9vN7Fzp5cDo+myGR7GAPq3oMpcZ3Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2357484}, "main": "./rollup.android-arm64.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.35.0_1741415081687_0.7419028646465202", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-android-arm64", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d38163692d0729bd64a026c13749ecac06f847e8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-NyfuLvdPdNUfUNeYKUwPwKsE5SXa2J6bCt2LdB/N+AxShnkpiczi3tcLJrm5mA+eqpy0HmaIY9F6XCa32N5yzg==", "signatures": [{"sig": "MEYCIQCitYeemzW/FUtiJuuaW0E66xbXAAWK/fSxWYKX23IPmgIhAPi6UtZhzZWgyfuaqbVdmOIaFMmfH1foiZ/jPAowId4L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2364332}, "main": "./rollup.android-arm64.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.36.0_1742200541053_0.45643593022680395", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-android-arm64", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6edc6ffc8af8773e4bc28c72894dd5e846b8ee6c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-6U3SlVyMxezt8Y+/iEBcbp945uZjJwjZimu76xoG7tO1av9VO691z8PkhzQ85ith2I8R2RddEPeSfcbyPfD4hA==", "signatures": [{"sig": "MEUCIQCTty8DZ37xV+Qi4Zw+7TKjD+CRwYPtGwtifmOxg9z6VgIgA5lesqTqP4raBiyX5yp/MTVWm+Tq8IspU0pk5HJPp1I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2356844}, "main": "./rollup.android-arm64.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.37.0_1742741824856_0.9588454549122201", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-android-arm64", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "c8806f88fd6727d3cf144c4ffb00f40d451b6618", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-VUsgcy4GhhT7rokwzYQP+aV9XnSLkkhlEJ0St8pbasuWO/vwphhZQxYEKUP3ayeCYLhk6gEtacRpYP/cj3GjyQ==", "signatures": [{"sig": "MEQCIH1Up5u/DXqByeaJ7YhDgEdpnnUFUvEpMouZjzK0VgHHAiBi9tAVjdRLlmPK4UaFfeW3RZ+NMqYMwXgjMbSvAL5wQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2364780}, "main": "./rollup.android-arm64.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.38.0_1743229742791_0.42439130612761233", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-android-arm64", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "9c136034d3d9ed29d0b138c74dd63c5744507fca", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-It9+M1zE31KWfqh/0cJLrrsCPiF72PoJjIChLX+rEcujVRCb4NLQ5QzFkzIZW8Kn8FTbvGQBY5TkKBau3S8cCQ==", "signatures": [{"sig": "MEUCIQDuSMQfaYBMBAaJapefyIH94QRnO7T5lAUWHowl8ygkqgIgOj0UArxM32KeAmVvHkNospOrA977/5gr8TNDXOitMKo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2364780}, "main": "./rollup.android-arm64.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.39.0_1743569369302_0.9031476670306502", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-android-arm64", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "9b5e130ecc32a5fc1e96c09ff371743ee71a62d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-PPA6aEEsTPRz+/4xxAmaoWDqh67N7wFbgFUJGMnanCFs0TV99M0M8QhhaSCks+n6EbQoFvLQgYOGXxlMGQe/6w==", "signatures": [{"sig": "MEUCIEsE15DGAqReOWKD3aHVvmoSAjvlKZYUjCw/E9TjUSjYAiEA3yjzXmazhLEAjgU29m7yjkSWxMeMHl8Bh587lLnTkWM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2401260}, "main": "./rollup.android-arm64.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.40.0_1744447172677_0.2465755126853333", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-android-arm64", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "37ba63940211673e15dcc5f469a78e34276dbca7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-PPkxTOisoNC6TpnDKatjKkjRMsdaWIhyuMkA4UsBXT9WEZY4uHezBTjs6Vl4PbqQQeu6oION1w2voYZv9yquCw==", "signatures": [{"sig": "MEUCIA29Ir9N0eZTjFUyihUo7FWal2DEGQmVXSMfrBqwqH+0AiEA9wBm/1lKVWz4pUdIbvpy+IgejilPZzk66pJknKEUv08=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2429252}, "main": "./rollup.android-arm64.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.40.1_1745814921192_0.4181250317610661", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-android-arm64", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e2b38d0c912169fd55d7e38d723aada208d37256", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-13unNoZ8NzUmnndhPTkWPWbX3vtHodYmy+I9kuLxN+F+l+x3LdVF7UCu8TWVMt1POHLh6oDHhnOA04n8oJZhBw==", "signatures": [{"sig": "MEYCIQCYK6gclSs/j7b5jWFhoZPRCkptQL7bVIsP+xBW2rHJdgIhAKah5dM0xRqy8C1aQVYfHFI+H6w4AwvSAPoIg/maQBQF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2435716}, "main": "./rollup.android-arm64.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.40.2_1746516410571_0.01686806478982672", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-android-arm64", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d73d641c59e9d7827e5ce0af9dfbc168b95cce0f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-yDvqx3lWlcugozax3DItKJI5j05B0d4Kvnjx+5mwiUpWramVvmAByYigMplaoAQ3pvdprGCTCE03eduqE/8mPQ==", "signatures": [{"sig": "MEYCIQCKYZ9TvnQ+QwcZZKvcf/MCQr7xcbROgQzXKIuFe5hCRwIhAMK2dxpMnCG9HQoHyDxJc77SYXkXXaT4QfpYCNec6Am9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2544636}, "main": "./rollup.android-arm64.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.41.0_1747546410774_0.02227937287065629", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-android-arm64", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "d19af7e23760717f1d879d4ca3d2cd247742dff2", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-DXdQe1BJ6TK47ukAoZLehRHhfKnKg9BjnQYUu9gzhI8Mwa1d2fzxA1aw2JixHVl403bwp1+/o/NhhHtxWJBgEA==", "signatures": [{"sig": "MEUCIDUoNYVj3Locpo4lKfuhNjH2VVSzcSY+sU7zxEoykZVgAiEA3YwKdQJBT04SQTvufm2HNg4KfYnD403l6eOTE0gpBgo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2646292}, "main": "./rollup.android-arm64.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.41.1_1748067268501_0.06835730897384651", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-android-arm64", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "e083aed199dd18263e3510e83ff73ab74c27414d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-8AVWhLnN9FteevGP+9pj/Y79vqE9TdziZTe5XkN5Z9+9QY7TEBbr4iz2te8/vXbLSLEdmaQx+o2GWXrLXDKGPg==", "signatures": [{"sig": "MEQCIHmcRTAfwmpsEgBac1n5DfSHlh7rNUp6BzvslavL06wUAiBijs2J9ib+pi1Tsc5daCl/9LwsIBJv3D073bGaAqeVbQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2644564}, "main": "./rollup.android-arm64.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.41.2_1749210029532_0.7366986451695043", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-android-arm64", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "6798394241d1b26f8b44d2bbd8de9c12eb9dd6e6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-bpRipfTgmGFdCZDFLRvIkSNO1/3RGS74aWkJJTFJBH7h3MRV4UijkaEUeOMbi9wxtxYmtAbVcnMtHTPBhLEkaw==", "signatures": [{"sig": "MEYCIQCz8djcpUzLr6JIHOV9/dJABd9czRNh9ni2UcQ1mlb3DwIhALYtQGBfwCJU335T8VFkh6POUiv9Ldv/ju7NgDa+l+F6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2644564}, "main": "./rollup.android-arm64.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.42.0_1749221289906_0.6082750440644089", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-android-arm64", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm64@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm64"], "dist": {"shasum": "f70ee53ba991fdd65c277b0716c559736d490a58", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==", "signatures": [{"sig": "MEUCIQCOypNijEDL67Xc+fs8CW+x7mS2p9DEoOjV9WzljSJ2wQIgB0LsQxVI7ECdRC8R6z9wH6tof5Y3BLgp1xBHyLo4lqk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2644564}, "main": "./rollup.android-arm64.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm64_4.43.0_1749619354949_0.44978924870140546", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-android-arm64", "version": "4.44.0", "os": ["android"], "cpu": ["arm64"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm64.node", "_id": "@rollup/rollup-android-arm64@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-uNSk/TgvMbskcHxXYHzqwiyBlJ/lGcv8DaUfcnNwict8ba9GTTNxfn3/FAoFZYgkaXXAdrAA+SLyKplyi349Jw==", "shasum": "63566b0e76c62d4f96d44448f38a290562280200", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.0.tgz", "fileCount": 3, "unpackedSize": 2638036, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCGIfGlJftkg0Pyx3EY5BHPcwO+aOyePQpijlodrliEqAIhAO9QCwWO/nl0MjsvuyS/0J/sw+qIZrYyhGlPVPC8kQiO"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm64_4.44.0_1750314173968_0.3308945921719251"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:52.145Z", "modified": "2025-06-19T06:22:54.500Z", "4.0.0-0": "2023-07-31T19:17:52.470Z", "4.0.0-1": "2023-08-01T04:48:56.022Z", "4.0.0-2": "2023-08-01T11:16:28.883Z", "4.0.0-3": "2023-08-04T08:16:50.501Z", "4.0.0-4": "2023-08-04T11:36:31.566Z", "4.0.0-5": "2023-08-20T06:56:45.168Z", "4.0.0-6": "2023-08-20T07:51:34.585Z", "4.0.0-7": "2023-08-20T10:33:19.280Z", "4.0.0-8": "2023-08-20T11:22:10.236Z", "4.0.0-9": "2023-08-20T14:28:56.248Z", "4.0.0-10": "2023-08-21T15:29:54.797Z", "4.0.0-11": "2023-08-23T10:15:43.497Z", "4.0.0-12": "2023-08-23T14:40:19.335Z", "4.0.0-13": "2023-08-24T15:48:30.214Z", "4.0.0-14": "2023-09-15T12:34:22.442Z", "4.0.0-15": "2023-09-15T13:06:49.426Z", "4.0.0-16": "2023-09-15T14:17:11.532Z", "4.0.0-17": "2023-09-15T14:59:03.049Z", "4.0.0-18": "2023-09-15T16:09:54.453Z", "4.0.0-19": "2023-09-15T18:50:53.917Z", "4.0.0-20": "2023-09-24T06:10:34.535Z", "4.0.0-21": "2023-09-24T17:22:20.510Z", "4.0.0-22": "2023-09-26T16:17:08.976Z", "4.0.0-23": "2023-09-26T20:14:19.487Z", "4.0.0-24": "2023-10-03T05:12:41.943Z", "4.0.0-25": "2023-10-05T14:12:40.344Z", "4.0.0": "2023-10-05T15:14:27.030Z", "4.0.1": "2023-10-06T12:36:35.428Z", "4.0.2": "2023-10-06T14:18:36.993Z", "4.1.0": "2023-10-14T05:52:09.981Z", "4.1.1": "2023-10-15T06:31:40.936Z", "4.1.3": "2023-10-15T17:48:22.508Z", "4.1.4": "2023-10-16T04:34:05.384Z", "4.1.5": "2023-10-28T09:23:27.017Z", "4.1.6": "2023-10-31T05:45:10.065Z", "4.2.0": "2023-10-31T08:10:35.897Z", "4.3.0": "2023-11-03T20:12:58.460Z", "4.3.1": "2023-11-11T07:57:51.113Z", "4.4.0": "2023-11-12T07:49:50.981Z", "4.4.1": "2023-11-14T05:25:02.282Z", "4.5.0": "2023-11-18T05:52:08.912Z", "4.5.1": "2023-11-21T20:13:05.887Z", "4.5.2": "2023-11-24T06:29:44.544Z", "4.6.0": "2023-11-26T13:39:10.888Z", "4.6.1": "2023-11-30T05:23:03.659Z", "4.7.0": "2023-12-08T07:57:57.077Z", "4.8.0": "2023-12-11T06:24:50.172Z", "4.9.0": "2023-12-13T09:24:13.670Z", "4.9.1": "2023-12-17T06:26:08.035Z", "4.9.2": "2023-12-30T06:23:24.418Z", "4.9.3": "2024-01-05T06:20:43.690Z", "4.9.4": "2024-01-06T06:38:56.789Z", "4.9.5": "2024-01-12T06:16:10.573Z", "4.9.6": "2024-01-21T05:52:15.928Z", "4.10.0": "2024-02-10T05:58:38.335Z", "4.11.0": "2024-02-15T06:09:33.560Z", "4.12.0": "2024-02-16T13:32:11.273Z", "4.12.1": "2024-03-06T06:03:29.534Z", "4.13.0": "2024-03-12T05:28:27.651Z", "4.13.1-1": "2024-03-24T07:39:16.201Z", "4.13.1": "2024-03-27T10:27:35.480Z", "4.13.2": "2024-03-28T14:13:31.454Z", "4.14.0": "2024-04-03T05:22:44.455Z", "4.14.1": "2024-04-07T07:35:35.817Z", "4.14.2": "2024-04-12T06:23:35.787Z", "4.14.3": "2024-04-15T07:18:28.202Z", "4.15.0": "2024-04-20T05:37:08.751Z", "4.16.0": "2024-04-21T04:41:45.780Z", "4.16.1": "2024-04-21T18:29:53.913Z", "4.16.2": "2024-04-22T15:19:10.364Z", "4.16.3": "2024-04-23T05:12:31.025Z", "4.16.4": "2024-04-23T13:15:03.555Z", "4.17.0": "2024-04-27T11:29:48.552Z", "4.17.1": "2024-04-29T04:57:49.585Z", "4.17.2": "2024-04-30T05:00:40.875Z", "4.18.0": "2024-05-22T05:03:39.309Z", "4.18.1": "2024-07-08T15:25:08.356Z", "4.19.0": "2024-07-20T05:46:11.466Z", "4.19.1": "2024-07-27T04:53:58.045Z", "4.19.2": "2024-08-01T08:32:50.418Z", "4.20.0": "2024-08-03T04:48:47.872Z", "4.21.0": "2024-08-18T05:55:32.026Z", "4.21.1": "2024-08-26T15:54:10.447Z", "4.21.2": "2024-08-30T07:04:23.266Z", "4.21.3": "2024-09-12T07:05:46.689Z", "4.22.0": "2024-09-19T04:55:28.775Z", "4.22.1": "2024-09-20T08:21:50.819Z", "4.22.2": "2024-09-20T09:33:42.450Z", "4.22.3-0": "2024-09-20T14:47:54.707Z", "4.22.3": "2024-09-21T05:03:05.893Z", "4.22.4": "2024-09-21T06:11:19.677Z", "4.22.5": "2024-09-27T11:48:12.946Z", "4.23.0": "2024-10-01T07:10:03.524Z", "4.24.0": "2024-10-02T09:37:17.130Z", "4.24.1": "2024-10-27T06:42:59.567Z", "4.24.2": "2024-10-27T15:40:05.148Z", "4.25.0-0": "2024-10-29T06:15:07.356Z", "4.24.3": "2024-10-29T14:14:04.822Z", "4.24.4": "2024-11-04T08:47:06.454Z", "4.25.0": "2024-11-09T08:37:19.686Z", "4.26.0": "2024-11-13T06:44:56.953Z", "4.27.0-0": "2024-11-13T07:03:11.766Z", "4.27.0-1": "2024-11-14T06:33:08.575Z", "4.27.0": "2024-11-15T10:40:33.908Z", "4.27.1-0": "2024-11-15T13:28:05.232Z", "4.27.1-1": "2024-11-15T15:37:59.937Z", "4.27.1": "2024-11-15T16:07:39.333Z", "4.27.2": "2024-11-15T17:20:00.550Z", "4.27.3": "2024-11-18T16:39:33.928Z", "4.27.4": "2024-11-23T07:00:18.996Z", "4.28.0": "2024-11-30T13:15:45.262Z", "4.28.1": "2024-12-06T11:44:54.527Z", "4.29.0-0": "2024-12-16T06:39:51.189Z", "4.29.0-1": "2024-12-19T06:37:29.883Z", "4.29.0-2": "2024-12-20T06:56:00.500Z", "4.29.0": "2024-12-20T18:37:22.548Z", "4.29.1": "2024-12-21T07:16:00.320Z", "4.30.0-0": "2024-12-21T07:17:12.727Z", "4.30.0-1": "2024-12-30T06:52:14.865Z", "4.29.2": "2025-01-05T12:07:40.719Z", "4.30.0": "2025-01-06T06:36:39.426Z", "4.30.1": "2025-01-07T10:35:50.707Z", "4.31.0-0": "2025-01-14T05:57:40.004Z", "4.31.0": "2025-01-19T12:56:45.414Z", "4.32.0": "2025-01-24T08:27:33.424Z", "4.33.0-0": "2025-01-28T08:30:06.955Z", "4.32.1": "2025-01-28T08:33:15.767Z", "4.33.0": "2025-02-01T07:11:57.263Z", "4.34.0": "2025-02-01T08:40:21.710Z", "4.34.1": "2025-02-03T06:58:11.917Z", "4.34.2": "2025-02-04T08:09:59.360Z", "4.34.3": "2025-02-05T09:22:02.403Z", "4.34.4": "2025-02-05T21:31:09.109Z", "4.34.5": "2025-02-07T08:52:59.830Z", "4.34.6": "2025-02-07T16:32:05.785Z", "4.34.7": "2025-02-14T09:53:57.160Z", "4.34.8": "2025-02-17T06:26:23.632Z", "4.34.9": "2025-03-01T07:32:34.711Z", "4.35.0": "2025-03-08T06:24:41.956Z", "4.36.0": "2025-03-17T08:35:41.346Z", "4.37.0": "2025-03-23T14:57:05.116Z", "4.38.0": "2025-03-29T06:29:03.079Z", "4.39.0": "2025-04-02T04:49:29.571Z", "4.40.0": "2025-04-12T08:39:32.922Z", "4.40.1": "2025-04-28T04:35:21.421Z", "4.40.2": "2025-05-06T07:26:50.833Z", "4.41.0": "2025-05-18T05:33:31.032Z", "4.41.1": "2025-05-24T06:14:28.754Z", "4.41.2": "2025-06-06T11:40:29.739Z", "4.42.0": "2025-06-06T14:48:10.134Z", "4.43.0": "2025-06-11T05:22:35.190Z", "4.44.0": "2025-06-19T06:22:54.172Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-android-arm64`\n\nThis is the **aarch64-linux-android** binary for `rollup`\n", "readmeFilename": "README.md"}