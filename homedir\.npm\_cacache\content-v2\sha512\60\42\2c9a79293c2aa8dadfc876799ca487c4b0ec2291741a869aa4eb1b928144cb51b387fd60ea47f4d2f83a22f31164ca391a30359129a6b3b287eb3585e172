{"_id": "media-typer", "_rev": "24-979431d31b46eff8d2975f8f86fb4dc4", "name": "media-typer", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.0": {"name": "media-typer", "version": "0.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@0.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/media-typer", "bugs": {"url": "https://github.com/expressjs/media-typer/issues"}, "dist": {"shasum": "4ee71136eb8612cc771ecfa2bf25ddc153084f5c", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.0.0.tgz", "integrity": "sha512-xThAi0oNneSuMjpVgV+Ml97ZzaBWgzyiDPv3Ol9V2OvgNAoEXXbnR+vBXvCMDJ41hUh9xzw1q7hxQS0p3WKPsg==", "signatures": [{"sig": "MEYCIQCPDPE4GNUfNUBVwxAVI5ZgCpka7a0/Q0gqLi4r9hyMtAIhAOREorwzdOKDKvYwnEcpEoE37XSqPMxcROhrxZhahHTq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/media-typer", "type": "git"}, "_npmVersion": "1.4.3", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10"}}, "0.1.0": {"name": "media-typer", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/media-typer", "bugs": {"url": "https://github.com/expressjs/media-typer/issues"}, "dist": {"shasum": "00607eec8005776e49ea593202f5469f703e8060", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.1.0.tgz", "integrity": "sha512-rQzgJCOLIC5eiHyyKatRNkYw6DLxA/0eRiDtDIWMs2jbBy78RlrzhE4FXl4XtjrbVpjsUTIeD6xGfyDrW4gBtQ==", "signatures": [{"sig": "MEQCIBSOaursCkt4fd6ZXQ0remqSON0E/kwl+qxAARvX1/pJAiAwc3by0fvb5vs1qnvAJAjjyKeK7K8DUlUwbqKWWIa89A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/media-typer", "type": "git"}, "_npmVersion": "1.4.3", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10"}}, "0.2.0": {"name": "media-typer", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/media-typer", "bugs": {"url": "https://github.com/expressjs/media-typer/issues"}, "dist": {"shasum": "d8a065213adfeaa2e76321a2b6dda36ff6335984", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.2.0.tgz", "integrity": "sha512-TSggxYk75oP4tae7JkT8InpcFGUP4340zg1dOWjcu9qcphaDKtXEuNUv3OD4vJ+gVTvIDK797W0uYeNm8qqsDg==", "signatures": [{"sig": "MEUCIQDhRz3yZcvoaVLNm4mBSHnXaLEL8O1SNvRRejIrrwlNUAIgAJaz1NACvTEodzCMB0acPAzLTZyNUWYKgMSj1KJ75pQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/media-typer", "type": "git"}, "_npmVersion": "1.4.3", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10"}}, "0.3.0": {"name": "media-typer", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/media-typer", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "dist": {"shasum": "8710d7af0aa626f8fffa1ce00168545263255748", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "signatures": [{"sig": "MEUCIEzGAZiWrq2CnMK6wYcXnCpDHR1URZRMrKJ7AefzH7gVAiEA+nI2pPIszFxnWvefnYD8yTaK/BG5Jw77Jt5RVNxWnBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "8710d7af0aa626f8fffa1ce00168545263255748", "engines": {"node": ">= 0.6"}, "gitHead": "d49d41ffd0bb5a0655fa44a59df2ec0bfc835b16", "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jshttp/media-typer", "type": "git"}, "_npmVersion": "1.4.21", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2"}}, "1.0.0": {"name": "media-typer", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/media-typer#readme", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "dist": {"shasum": "ca89611f8e31b936efcb3aa90826772f135e3341", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-MKXtlq1YKUow2wpJlJ7ep1MU6jQR0BaKpkDyrE294cmNSbhoOU1yziJ4UZOU1guFxcEaFt5t4LHsL/t891D6BQ==", "signatures": [{"sig": "MEUCICaSUkB/C4F+bQXkUVi049XPD885dVMyqLEKd270vYAuAiEAoZYsIqoCjFD98VCTM/htSY/jpMOGwqay9FreEZ2XLYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJby98nCRA9TVsSAnZWagAA4d8P/A+KHvncQuvDCGeEBhDT\nNS32nytG/grOSzaMcv/L3KA9zAHBWyYa0EmBQ5Iyvd2A1jk12yKW5FsonQNO\n2Q+FMWW8fzo2pJtV6KB7FGgTJ6oWfViT86c4gjZTnJ6sZBe5z30Ai/5hSk6g\nAv749aM7qgX88RysFGRsbq38rKwmV4LIvLqYFT+knDo+R1A72rFjZLtWSe2a\nfrjdhfz3+f5S0Mj6GWdtgmn6zARFlIi6SyJrrNjoOumQXDRHQt3rgicEgBCR\nztwlnBjFXPCBj6+xK5mIqNP4XJMv1W0DRplP1WWfChTUy+8SU3PMTmE3n0gV\nvx1nBWWrFaZFRsBSp6l6qD5Am4JO/RlFV/GS09OxL93+R/gL+BnGY6uEZhcr\n5d2SDrLAzzznBz6q5KCI3wV8Fx0jNQyibw1XaJ75BL72HsCV1TWU9ZVeGCau\n32/J7ZuRsPkn+adujX6ffIi5+oeFBHkl+NuCvtEZT8nknPw/CsoPYEZ7SA3F\nX+yBPf8fPXiNIk0FrdVtMJvMT2ueaoEw3+hzzmEfwC6Lq25VYTOqdpsbyTiw\n0+Uor6Oe9sYwIZy05pc25PJdJvwHn3LaDLA8Y8PAjntDWNMd8nW2ZnXGVtLE\nia74S0Pqi55M/ARn9DiGe4H/4n77GEQyYuSWIiJheo71mY2/Y8M3OgHGLY4r\nNlif\r\n=3F90\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "dbe811acb83855b086667271010d3b105aeb6e34", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/media-typer.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "_nodeVersion": "8.12.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "mocha": "5.2.0", "eslint": "5.7.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/media-typer_1.0.0_1540087590178_0.03604841955092852", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "media-typer", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/media-typer#readme", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "dist": {"shasum": "e39d677e19a011c52d2681f430d1adafb299dd41", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-v42gdPIuqYCoDVH5OiaKsVrv6aJqdMWJzl8KCyDs/KeDyBveYp3Wxq4UWJfsWjkSZUNC0xlLfDlLCPa1h/oo+g==", "signatures": [{"sig": "MEYCIQCG+1J4L/K42JRYsTrcbZ5Ig+MyRMSOVr5Z+YfOnftu8gIhANK/LK3TvOGXGAPTdQs5WObiLFQLawoYzBgiw2B73YYa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJby+HDCRA9TVsSAnZWagAALRcP/jsONa6K4aEKiHKqqmU+\nNryRP9TBIgMxtSRaXP2lU+2BqeQRAPFtxa7H4SaFyyM6QLI/I3G3C1LDltXx\nKcmaWPzGHNJZ6aFVqiNgn7nia+/vGiciP/ZeEg1AKy6vFbkKVMuk3AOQVYO0\nMhByAAVLTKBRqMtuMqv2AH7tHQLSMNJCvHQMelQAOruHz8wbE9Pg189uWaud\npm0sD0eqVw6L1VD7A8XtA0TcyidcnytIIgq/uSr6sES6sFIBTtCafbFaz/X3\nrdm4Fiv1jynEN5Q5PWO9sTCIBeJ3gdlTm43EkXS98ggR6pPqYTujTdI15SCJ\nnM1qaMUR3k+2BAZttjD1v+gvtI5U3nl/jgdTWCt0oQlQ3GgO8TjO43RHKYmR\nP4LylGuve7oih6Sf6/MDOi/QPReLiQW4FBdAxCB8jkKjiVV5YEZ4XmlaO2sd\ny/YMcoxBrROebWcjmlFXROfAs2ianwxTD7lhe7YnjghGIf2m+BxFWJF0xLga\n+5op2MR/GCbf93BiV9RguUnfyaRohgmo3acIc4Fm/+M52nMrMS3dyiOxvCRD\n+cjiLJxHUsf192Y9vgbXMxlBqUzr6lGzSQAPpDScImHZzxC6AU3LVNOVCCDU\noMd/a96idMBHmCUC0dXn4WhYGDTjNRAcaj8PTE8vENp66fEbp4/wWFfWVM4h\nuvVC\r\n=0v4D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "1ee51e645afab21855a35fbf95fe6b1e4c852060", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/media-typer.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "_nodeVersion": "8.12.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "mocha": "5.2.0", "eslint": "5.7.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/media-typer_1.0.1_1540088258485_0.9356881090496065", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "media-typer", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/media-typer#readme", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "dist": {"shasum": "e64b0a709d52c158ddedaf38ebebfa00d8e02c14", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-/ky7iFD18Y2mN5BdOS4zotSSgu11BsIR2l3L7eK2bTaRWQidoSBmSxGgMFd/XOSGyivlhtQUdDLoUzlr1PWb1g==", "signatures": [{"sig": "MEYCIQDQxQHKR5v8iKrSSGbb7BWn7JgmERGaWfaExkhnAtoDugIhANpq8hmEacUuHeHRgo+WgmC/xKYRSS86iqEEFvuJKRKf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcukLZCRA9TVsSAnZWagAADM8P/RDVhBWP4o9R/32p6fdI\nVr8PGyNgx0K6s0A6KVOis0x2YwXRHCZKYIP/DXjsstTd4qQ/sT2QQVDTkS8s\ndeTYEQ2W/nvjNymWxPtbkfgZ4fdGFa+QFNoZhQCjg3fbrze2Ym1MPehlAi9G\nw9Li0tIxKQgy9LcXyBC/lPiQdm8Ip5wOvihfVOd4Jm7dyT4NgKem41oKNF8L\nLQIbguAHGE9ET/s8nvLe6WI9IK3CUD7BN4fTLs6ypOJKm3leLDCmzEkwd1Yq\ndvI4WKg/sZ1jYi436OJBaYfzP/kJZDYWT4Hghqh1p4y/WQF3GumRhoPRVSfR\nmBARnlBniHYPoCchPXmSLr4NbKO8ceITFeelxgi42AJzAY4UQU1QP2jjP8LS\nCBo6jmMiePIPLuIN/oOKejtIX8EDsWZelAXDH1jE038PqXp22U2j0KmqPc0m\nmkXBLDOAECvnsqvMqawEJzjJi639EBuRf3tFBnQZGqLoNSDPEtJNJxxMy6zA\nJjjR5zsrlvfX+07NeJHjNB4vutas8sOb/T3aZxc/7VWFgJoDApP+vsoxuQy9\nlvhLPoc+Ew/uXqOt3GwZM27g1caqdVmJolrTzpjKk7GLUR9GoedWcsTCTJZy\nimhjRmbJT24q23z99eeJO1GaraRtxOe3KaLwYpjhTFC1aKJcuH3Yp5CDF3Sp\nYhBE\r\n=e9ga\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "95189ea17b0ff63132f027c746ae6eb1b2c783e1", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/media-typer.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "_nodeVersion": "8.15.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/media-typer_1.0.2_1555710681221_0.029165072776340617", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "media-typer", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "media-typer@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jshttp/media-typer#readme", "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "dist": {"shasum": "6ab74b8f2d3320f2064b2a87a38e7931ff3a5561", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "signatures": [{"sig": "MEUCIQC9UI2Z8NvAhUvnplJNMlz8uTaMjzgungJS7hhHBFNc6QIgFQaPxCjC5otSzLU0zp6KOyOdQ82VoCi0sttAS568jQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwSZ1CRA9TVsSAnZWagAAq2YP/iVhvTj/MN3o7Aq8zi5d\ncqBXtKn6MQPCQFT6MlyUSEyXZkR76fZJtpqJLuqMtaICoUcFbt2eAJDqLDY2\nFB0E/q9Fcz/3sz5Smi6q3TBeJhWiMn9VhxkE2WzLBXDl6o9LD0iWAsDgaVse\nSTKTP2wO0xxwUxJKNGWDr9KLCweldS0/5SNMUgGkk7nYfQRaBRiW9t3QI/SW\nYqEllnjxH1r4UZOGSGfX07W5oNhPmBzutKkky+VBQXo51U46qcRV3f6NAObA\nshoOzlCCreCkF3mZuLzG8gfwFULM5b6Xud3/m6Hr1JxH7kVjaa/w2BhUhj3N\nsbgHcBMPHxOqzv1TdA8LAv0J/+sF5brRTLZW8Tp5WKoPvJDp6nG46rmrPm1H\nL7xzDwXKUnL3bZ+KtrSwgXY0viF6uMQlejAjkpXVQki5pc1pOEpnnQ+nfF22\n4myltUfRq5TGBmqqAZV0+Q+vIHf3lMBi/PMVm1ZI1QspY50B08fAr1iH81bI\nCredpoZbf5Avd2eNZnDFQUbzXVh7FW3Q4+q5667AlAvb9b3rZ3wEFgSDW2bS\nnUm/VQFkvRXQl2gM0lQJWd8uOzRFInCH4S4TsPQezBBILBQtmH44qnw/Kqn2\nsDw6GU8MifAKlOfPTl7+WPlALvhmkjrZNK0HukXlowlhisxR1UMLJRGIaWDO\nzTFe\r\n=OUQb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}, "gitHead": "1332b73ed8584b7b25d556c55b6de9d64fa3ce2c", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jshttp/media-typer.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple RFC 6838 media type parser and formatter", "directories": {}, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/media-typer_1.1.0_1556162165232_0.28874376896252363", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-06-13T19:25:11.301Z", "modified": "2025-05-14T14:55:59.100Z", "0.0.0": "2014-06-13T19:25:11.301Z", "0.1.0": "2014-06-18T05:02:41.908Z", "0.2.0": "2014-06-18T19:00:35.939Z", "0.3.0": "2014-09-08T04:32:24.363Z", "1.0.0": "2018-10-21T02:06:30.337Z", "1.0.1": "2018-10-21T02:17:38.605Z", "1.0.2": "2019-04-19T21:51:21.389Z", "1.1.0": "2019-04-25T03:16:05.379Z"}, "bugs": {"url": "https://github.com/jshttp/media-typer/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jshttp/media-typer#readme", "repository": {"url": "git+https://github.com/jshttp/media-typer.git", "type": "git"}, "description": "Simple RFC 6838 media type parser and formatter", "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "readme": "# media-typer\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![Node.js Version][node-version-image]][node-version-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nSimple RFC 6838 media type parser.\n\nThis module will parse a given media type into it's component parts, like type,\nsubtype, and suffix. A formatter is also provided to put them back together and\nthe two can be combined to normalize media types into a canonical form.\n\nIf you are looking to parse the string that represents a media type and it's\nparameters in HTTP (for example, the `Content-Type` header), use the\n[content-type module](https://www.npmjs.com/package/content-type).\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install media-typer\n```\n\n## API\n\n<!-- eslint-disable no-unused-vars -->\n\n```js\nvar typer = require('media-typer')\n```\n\n### typer.parse(string)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar obj = typer.parse('image/svg+xml')\n```\n\nParse a media type string. This will return an object with the following\nproperties (examples are shown for the string `'image/svg+xml; charset=utf-8'`):\n\n - `type`: The type of the media type (always lower case). Example: `'image'`\n\n - `subtype`: The subtype of the media type (always lower case). Example: `'svg'`\n\n - `suffix`: The suffix of the media type (always lower case). Example: `'xml'`\n\nIf the given type string is invalid, then a `TypeError` is thrown.\n\n### typer.format(obj)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar obj = typer.format({ type: 'image', subtype: 'svg', suffix: 'xml' })\n```\n\nFormat an object into a media type string. This will return a string of the\nmime type for the given object. For the properties of the object, see the\ndocumentation for `typer.parse(string)`.\n\nIf any of the given object values are invalid, then a `TypeError` is thrown.\n\n### typer.test(string)\n\n<!-- eslint-disable no-undef, no-unused-vars -->\n\n```js\nvar valid = typer.test('image/svg+xml')\n```\n\nValidate a media type string. This will return `true` is the string is a well-\nformatted media type, or `false` otherwise.\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/jshttp/media-typer/master\n[coveralls-url]: https://coveralls.io/r/jshttp/media-typer?branch=master\n[node-version-image]: https://badgen.net/npm/node/media-typer\n[node-version-url]: https://nodejs.org/en/download\n[npm-downloads-image]: https://badgen.net/npm/dm/media-typer\n[npm-url]: https://npmjs.org/package/media-typer\n[npm-version-image]: https://badgen.net/npm/v/media-typer\n[travis-image]: https://badgen.net/travis/jshttp/media-typer/master\n[travis-url]: https://travis-ci.org/jshttp/media-typer\n", "readmeFilename": "README.md", "users": {"mojaray2k": true, "shanewholloway": true}}