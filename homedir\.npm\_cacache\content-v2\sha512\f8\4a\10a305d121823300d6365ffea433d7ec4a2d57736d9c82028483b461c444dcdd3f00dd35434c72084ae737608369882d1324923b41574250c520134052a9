{"_id": "@sideway/formula", "_rev": "5-2a5354a5b10d396bd504a67cb8bf2ab9", "name": "@sideway/formula", "dist-tags": {"latest": "3.0.1"}, "versions": {"3.0.0": {"name": "@sideway/formula", "description": "Math and string formula parser.", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/sideway/formula.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["formula", "parser", "math", "string"], "dependencies": {}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "41ae98e0421913b100886adb0107a25d552d9e1a", "bugs": {"url": "https://github.com/sideway/formula/issues"}, "homepage": "https://github.com/sideway/formula#readme", "_id": "@sideway/formula@3.0.0", "_nodeVersion": "14.4.0", "_npmVersion": "7.0.5", "dist": {"integrity": "sha512-vHe7wZ4NOXVfkoRb8T5otiENVlT7a3IAiw7H5M2+GO+9CDgcVUUsX1zalAztCmwyOr2RUTGJdgB+ZvSVqmdHmg==", "shasum": "fe158aee32e6bd5de85044be615bc08478a0a13c", "tarball": "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.0.tgz", "fileCount": 5, "unpackedSize": 16937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk85yCRA9TVsSAnZWagAA4mgP/1Fj8kLjU71wGS9o0M+3\n5f75pMf31zPJydyAWP51XoVyrqREl/9UJ0w+R/MhFez6R4g3VdtusRIAxcM4\n1dM4ebPSCcs3MAfGKZvgf558faqs9cv0bfxJ6nqjY7ofJMx9i/BbaQzo9r5b\ncwRFhXy/ELHTMuVrmXxfW6FhLXNQTkfIReA8PXTpA69+hyjEA3kBYtd7X9cg\nTPv4J+6q+HVrXn/XiimTuN7FRgflADq2AO1kU7QPiIIOv/EaR8HylNkIIwxo\nVEzKti82MYKJu5Bt02XMgdVeU4rTXcp6Ky9VWCd2onQ3MeF4q8xYJnQeNenx\nY10KGkALHcQRE2/8lbovdam5OU+2+VXYWLksP9mwqm2Pz5kFdwGAAEhS5SP0\nvEcHURasri1POGEgzgTqAzCxGxV2s+e8kiVr8RXR9S0J0OdQhCLahrOzauHd\ngTl2eJZwo7jfMuIeBnQ0BZsuuJ484oxH3eS0rWNFTZhAyI0z7z6YeM1l0X2t\nVXqUPEkUUIrOaLmmDw8CjwC4YpPJS4P6TnRFfZbvo/HPkslQieOhrF2FGKoc\np2FLR/r63tpT8I53m5jVOU3cNM+P45kL0rNNZLiuFF35L3SsDQ3GTGppdF/s\neL4PpX6xtKBoR5G4S+9EghmEXpkHc15Pv2UIOLG0LZ+641We6Mmd8j2poOWR\njSQG\r\n=cfiQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvknWvqipCf/GynKh6kbalPnPy5OD8ww8A2B3vLlr7kAIgWVBWV3SUas41tKDKAE2L5TD9oCowrr93vzSOfQ0Y2eA="}]}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formula_3.0.0_1603522162455_0.9906089169658288"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "@sideway/formula", "description": "Math and string formula parser.", "version": "3.0.1", "repository": {"type": "git", "url": "git://github.com/sideway/formula.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["formula", "parser", "math", "string"], "dependencies": {}, "devDependencies": {"typescript": "4.0.x", "@hapi/code": "8.x.x", "@hapi/lab": "24.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "5b44c1bffc38135616fb91d5ad46eaf64f03d23b", "bugs": {"url": "https://github.com/sideway/formula/issues"}, "homepage": "https://github.com/sideway/formula#readme", "_id": "@sideway/formula@3.0.1", "_nodeVersion": "14.20.1", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==", "shasum": "80fcbcbaf7ce031e0ef2dd29b1bfc7c3f583611f", "tarball": "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.1.tgz", "fileCount": 5, "unpackedSize": 16939, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUHWFDYhzj/83ylyEonL/HK62mIfI//5GL0LSvpbJwtwIgFqzkzI4ti9Y2wooQgAYr5J6rV2fv3MfksNAfoCBTfwM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnIIrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrARBAAgfv5Bzdw+ByWfLnDAOsLoOMK6Qeo6rLUIls+83HEDMy3IvQk\r\n6kdcxXINXdLMtAXIE373vlZvEjr1cWsjsp0S6MUvbcrlxlRMoqIJNR0VIlKU\r\nf9Fh4wKbODIO1urOQZbKmXB3DyGt+zFikucKoukSBjF7jw01fmh3s2njUnuM\r\nRBSCRpDvaDvAG6oQsqJ2psLIDsQhm5Cs8u+5byvUbpNyFDJldE0GQpy+uvah\r\nGjKw+/GJ9hF3+6f9hVjAtMG5lV5wO0w8AZ7xZ9aO2DY4N9ql4ktmIIUKnthp\r\nLlUZcApLJYRz7qppagAMcrGtA/sMgD0cf4anSBcQvK+TMl3BF1vkiQZhK146\r\nHt7Tgu1xgxC7KS7wJIX80FSQAqxNxY9362P0CW5AV+1H7LMtst7jTxVJRGvz\r\n+8MRfWaJ4DsU0P8uCQ6vpByfd1F9Fju+qfkp/0L+cPbPx1cZhQOsEJIfv1gt\r\niW0Q+Syijj/Wvv8Dam5sG026EqVfE5myC8jbAilFau/FJ2xWkHAyKFS4dvHM\r\njys2E4zmmwUF7r74lKWjO40jUaB5+cSJTRAzoF8vb+xJBEGSDGy8RpAF5eU7\r\ncgkkhn/9FzhTcZBqBIjnz4kYIhVyK7tEsHkRNLddwiHcweeofFauNkAdniel\r\ncNCHjfgTz1g6FsasmCQmeeuWEk1EyJniGEQ=\r\n=ECod\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "marsup", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formula_3.0.1_1671201323483_0.9094757425487872"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-10-24T06:49:22.363Z", "3.0.0": "2020-10-24T06:49:22.571Z", "modified": "2022-12-16T14:35:23.717Z", "3.0.1": "2022-12-16T14:35:23.617Z"}, "maintainers": [{"name": "marsup", "email": "<EMAIL>"}, {"name": "hueniverse", "email": "<EMAIL>"}], "description": "Math and string formula parser.", "homepage": "https://github.com/sideway/formula#readme", "keywords": ["formula", "parser", "math", "string"], "repository": {"type": "git", "url": "git://github.com/sideway/formula.git"}, "bugs": {"url": "https://github.com/sideway/formula/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readme": "# @sideway/formula\n\n#### Math and string formula parser.\n\n**formula** is part of the **joi** ecosystem.\n\n### Visit the [joi.dev](https://joi.dev) Developer Portal for tutorials, documentation, and support\n\n## Useful resources\n\n- [Documentation and API](https://joi.dev/module/formula/)\n- [Version status](https://joi.dev/resources/status/#formula) (builds, dependencies, node versions, licenses, eol)\n- [Changelog](https://joi.dev/module/formula/changelog/)\n- [Project policies](https://joi.dev/policies/)\n\n## Acknowledgements\n\nInspired by [**fparse**](https://github.com/bylexus/fparse), copyright 2012-2018 <PERSON> <<EMAIL>>\n", "readmeFilename": "README.md"}