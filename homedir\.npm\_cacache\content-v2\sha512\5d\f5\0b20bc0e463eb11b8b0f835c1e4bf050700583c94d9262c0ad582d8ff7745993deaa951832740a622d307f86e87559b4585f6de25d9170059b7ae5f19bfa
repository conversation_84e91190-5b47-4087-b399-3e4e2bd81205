{"_id": "p-try", "_rev": "8-78ffda74c30c419c3b41832e29eda1f5", "name": "p-try", "description": "`Start a promise chain", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "p-try", "version": "1.0.0", "description": "`Promise#try()` ponyfill - Starts a promise chain", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "8a6f2c232b80e12c138714e553fc8dd6313604c2", "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "homepage": "https://github.com/sindresorhus/p-try#readme", "_id": "p-try@1.0.0", "_shasum": "cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3", "tarball": "https://registry.npmjs.org/p-try/-/p-try-1.0.0.tgz", "integrity": "sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE464tNn69UJYHkwjJVtWOqS0EdidOs9mzcReUFJFARFAiAfUqCPIdKuzWxB5b8FPvjrVRhhzxhSnw1D9MFOLsE80w=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/p-try-1.0.0.tgz_1477030603238_0.5670752932783216"}, "directories": {}}, "2.0.0": {"name": "p-try", "version": "2.0.0", "description": "`Start a promise chain", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "bba0e1dd227a64c91d16840e0985be9603c3ab62", "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "homepage": "https://github.com/sindresorhus/p-try#readme", "_id": "p-try@2.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hMp0onDKIajHfIkdRk3P4CdCmErkYAxxDtP3Wx/4nZ3aGlau2VKh3mZpcuFkH27WQkL/3WBCPOktzA9ZOAnMQQ==", "shasum": "85080bb87c64688fa47996fe8f7dfbe8211760b1", "tarball": "https://registry.npmjs.org/p-try/-/p-try-2.0.0.tgz", "fileCount": 4, "unpackedSize": 2987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIqGkCRA9TVsSAnZWagAAMBoQAJN8/dFWjtMshgIsBTwp\nU5BksixW+WTd2gwX8gwnBfE0e3ZVDILdE60seM9FflibHNzFUD0qnfyvrvw2\nOr88f2WYA2PpnweQ5mC7ZSTl1m6FiXhgAnA4q2HX+QWhMkrYiUzodR0LjQ3P\nhIcPHQAjXvYMFDM4tD9HCR/GB5+nhR1phmKpXNLocjFG0/HOFNZf5LAX5kBj\nAYQZisPKISNoN0mtdgdZYhLssrxu9uxT5jyRRyf1awpo/FYlHrWWiyuKUeBq\nla0ke7ooJp4kXzfgU333gzO27nhRuU52G4wv5EVTt/2du03KcBLZvsuKRTHC\nPmC0frxa+nn+jl8cXQlVF8sRxhLElx9z8ou87AhFJoKAmwAF3I4he7cNo2l/\nSat5eEJwUZefFHDzQ8ki6bbzpmpEog07gnlSu3pZLKABLk4o8lCI5pZe/zyE\n0b6OiEAcYaB4nXpW7zmDhkP4qmhN9uSIxZdTt8OTnnhuJDV7JMnH70n/vfPs\n3gUObscw9NV5rbFVzmtMx/O+vDdTnAPCJ8nInCbC7GZjmheezLy545CXCWTZ\na6pJ/NXpufXsHvGoC8IvqAFvQ3cPiKThiGSjR8DvuLkPKM99uf7jMGUT2kBC\n4VUot0pEyWKcmvpfcrdmPGG0VM6Arbu9DkyelxPatk4H4uIN+fwHwwHrZopw\nm9tq\r\n=aQEU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGh5vGRhgiIUiqcTu5ctT3c7MOdIoeroPB/XgP1wKLTVAiEAt0LKsADjygcc+TBPZ8ddHjOPh0weFW40nnhAPtkRJSk="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-try_2.0.0_1528996259608_0.5084892289403542"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "p-try", "version": "2.1.0", "description": "`Start a promise chain", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "49fe9f6558307544a992f314132711b71445b901", "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "homepage": "https://github.com/sindresorhus/p-try#readme", "_id": "p-try@2.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-H2RyIJ7+A3rjkwKC2l5GGtU4H1vkxKCAGsWasNVd0Set+6i4znxbWy6/j16YDPJDWxhsgZiKAstMEP8wCdSpjA==", "shasum": "c1a0f1030e97de018bb2c718929d2af59463e505", "tarball": "https://registry.npmjs.org/p-try/-/p-try-2.1.0.tgz", "fileCount": 5, "unpackedSize": 3960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj9XHCRA9TVsSAnZWagAAa2YP/iCbddLF716pKFOY66nS\nI2i83YaSc4f3C4kglcgT0X93EAHrLFBwmB1Ncy7kNZWJ7k/9SpD+Z8wa598M\nuCQgt0aIn/i9s75dhm/u+0n29ieRBZVf7r+S+HpAnLsIMeNbSJHXJz5F0oAj\nPD8q/S4uljVVo2gI51lv3RVKhtR+/662bZfweYCiLyzGh3rzeOX5U6dT95OR\nNQZ1XfuLwDgo4XHS5IgwMvbCi4ZVKCCiUux8xoelQ2KsP6OgL3ufKH3QXcYN\nvkVVmWwUCC8RE9cPlV0GsaHwlHFA1zyQeyKHbFzuconAAJmRLZLl3CDa6BDj\nce6cFnr/LY3W9LWsfn8eDpiksR+KPtkCZoVom9bjYiuIHNKOivAZIeasHW4E\nfSMuT6wU8Xq8TKfpDVGt00NrO2Uemdpw9B506t6Piid7lzhacrszSKmbDyeD\neEj/6yl5DPrTCybT6s5gVIzq5cJSK98bZhnAhAdN0YwqIOjdQ5AASio1lOuy\nAK2KmAzlkepoiwth4yHeWO3YR+N2EbQFgZS0Y0hBudYXyR7lS/QocA+ka5xX\nwBO2wr4a95LxREkJJw02Izk0Dg7ifRd15nLsA8XbjxmUJv5UAUIKKew3sYXp\n08tRORjrpiXc3heDyeWg6HlQ18I665fBrY1VDwp1DG93hAiFTwGC0Dn3OVzy\nb+Z4\r\n=aVpq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChnLduKN11xKMuOuddxGz7wdDgC6o1Egm5UCa4f/C6yAIgHD7rRk9rSNmWGc2ctXtDesy0vsPcUVp/HfVqWRNqeao="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-try_2.1.0_1552930246946_0.0368995873413136"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "p-try", "version": "2.2.0", "description": "`Start a promise chain", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "e80e2e130b2d16807345be02aa03541e6e085952", "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "homepage": "https://github.com/sindresorhus/p-try#readme", "_id": "p-try@2.2.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "shasum": "cb2868540e313d61de58fafbe35ce9004d5540e6", "tarball": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "fileCount": 5, "unpackedSize": 4371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoLPbCRA9TVsSAnZWagAALrsP/ie/GNjXnqYiGeGV/bKQ\nudud/fo7L0Brvq7zY+bNfSDNeyIDc5fosGrrCvaCA7JpK+KYnN+ww6lTy2pa\nY3njArVn6eeqfw2ZnheHrJOduMvftXw3+9EJ2Y0ulJ+OfDxvFI5n3HsE4bia\n4M/hMfvo4ScI4MmDnkcGA0rZEEPnfHg8Ud9u5tJOyIsftV08oY4Czg+cGyOV\nWuLge0A7+BHXhmVz44NR7edufsq2hbbmSsvl14SMyWYEjKV2r9Q3fEwL5XHP\narq39bqA9X+DnD0a12oWtYeT+v3r5ewg4h7H7QtA9Bvxdns/usJw/6KHy1za\nh8haNrRmBn1ze0sF/kNSKeStFcIsH8fp4Iiu4w8mniipVcuSt5tK5mvh3KOv\n0p8rBWzxGEqcxXxsxmI1rLEKGMy0ZthgvLOtRsnYl8a/DG1cxE9ZN5c2oaWA\ngUSDavh3tnwpl8PTag013nAmYSDYTNhA2TmHbWbfTzSCFvAjhivKfbIrcyF6\n1iQX4AckEZgtSTvbzbggC87gQS4xMfM82pi2rmkJuv3i6Iu2RkbDeFrQWphl\nL0FftZLanQQ39WeOiHZK1zNiBLaOxXWH/VWQKLG9CwgYSqFNGDbZIQuq2HkI\n/lJ6uIP2LZs+dMpDlw37K4BUOIS7wxJvOGfbwZmyYHN6gA2AeCA2wYZh4zVc\npEVP\r\n=gHtm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFqJShDf2xjNgk1aoDGnPwZwG+G7HV52faNfi6pOpE0GAiBBrH0yhwIdb+P7KpY/+oabo0DWJVmv0pL/crggmNyfzQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-try_2.2.0_1554035674934_0.5387262637396022"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "p-try", "version": "3.0.0", "description": "`Start a promise chain", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.45.0"}, "gitHead": "8cb14e34e1031dbdfc385058c36b47f90910332e", "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "homepage": "https://github.com/sindresorhus/p-try#readme", "_id": "p-try@3.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-nD1xrH3cV2epQsO8pv6p28i/TdrzJQ5g73c41a+fu+3uhABdf7xujY58ebIuSrOQYxNb8WqNDpX6HjYl8P0KfQ==", "shasum": "9d56ac76f66a7396c6f0c81407cb39e3375be3c2", "tarball": "https://registry.npmjs.org/p-try/-/p-try-3.0.0.tgz", "fileCount": 5, "unpackedSize": 4125, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+MMfxCGDlITl2HYhGkT9890d/syzKaiUM621H0agRXgIhAMt9sbpMv455yr6hlbs7M+3ZOVql7jxH+YHiaSAmhv9P"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/p-try_3.0.0_1633364383167_0.2080334787200102"}, "_hasShrinkwrap": false}}, "readme": "# p-try\n\n> Start a promise chain\n\n[How is it useful?](http://cryto.net/~joepie91/blog/2016/05/11/what-is-promise-try-and-why-does-it-matter/)\n\n## Install\n\n```sh\nnpm install p-try\n```\n\n## Usage\n\n```js\nimport pTry from 'p-try';\n\ntry {\n\tconst value = await pTry(() => {\n\t\treturn synchronousFunctionThatMightThrow();\n\t});\n\tconsole.log(value);\n} catch (error) {\n\tconsole.error(error);\n}\n```\n\n## API\n\n### pTry(fn, ...arguments)\n\nReturns a `Promise` resolved with the value of calling `fn(...arguments)`. If the function throws an error, the returned `Promise` will be rejected with that error.\n\nSupport for passing arguments on to the `fn` is provided in order to be able to avoid creating unnecessary closures. You probably don't need this optimization unless you're pushing a *lot* of functions.\n\n#### fn\n\nThe function to run to start the promise chain.\n\n#### arguments\n\nArguments to pass to `fn`.\n\n## Related\n\n- [More…](https://github.com/sindresorhus/promise-fun)\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-p-try?utm_source=npm-p-try&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-23T05:53:30.738Z", "created": "2016-10-21T06:16:43.459Z", "1.0.0": "2016-10-21T06:16:43.459Z", "2.0.0": "2018-06-14T17:10:59.660Z", "2.1.0": "2019-03-18T17:30:47.159Z", "2.2.0": "2019-03-31T12:34:35.198Z", "3.0.0": "2021-10-04T16:19:43.327Z"}, "homepage": "https://github.com/sindresorhus/p-try#readme", "keywords": ["promise", "try", "resolve", "function", "catch", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-try.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-try/issues"}, "license": "MIT", "readmeFilename": "readme.md"}