{"_id": "source-map-js", "_rev": "9-f59e26d4e882a952c44862f41985b2ad", "name": "source-map-js", "dist-tags": {"latest": "1.2.1"}, "versions": {"0.6.2": {"name": "source-map-js", "version": "0.6.2", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@0.6.2", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map", "bugs": {"url": "https://github.com/7rulnik/source-map/issues"}, "dist": {"shasum": "0bb5de631b41cfbda6cfba8bd05a80efdfd2385e", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-0.6.2.tgz", "fileCount": 20, "integrity": "sha512-/3GptzWzu0+0MBQFrDKzw/DvvMTUORvgY6k6jd/VS6iCR4RDTKWH6v6WPwQoUO8667uQEf9Oe38DxAYWY5F/Ug==", "signatures": [{"sig": "MEYCIQChmRXzH5EFK/8o3qL5x4EAv4c9XHwhWHssou36eGtzqQIhAJUkiUgi//X04sbr47IifXEIbPMWRK0TvjLJUwVOweCm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 779932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIGFuCRA9TVsSAnZWagAAJ8IQAI9n0X3lSjY8DFGfEl30\naJnfN/jH+Kp2ZZ6CsO8p6LqaWvvfLHjTENfGjLZUXoqw44zU9kua3dcE/5j2\no93o2C1Q4U1dx1APPlETjkZ7ikbe9/v5zulWkfjNTHoTcdm+8FMHGT2Atz55\nG0u1MFo+j2h65ZMsEkM4heMubsAeT6qPmC92jbSjXrN4bb9LpJuM/qnYDG5C\niP5tMD/520+5kNRO5L0mvTJRbUQbgd4/SUmbQdclrVm8rS6JuiDknwpe52OZ\nHBBU/VZ5Gt/HRJu4xunQlSI4A7TqZK1aHR92YjHWeDWgCzj6HXHysOLX6mmn\nbnansYrGGE7hMamLwr9ndpMbLeBO1LSupaojV512BnMTrnXIVpGuDtRATt+4\nvtsi7tZOq+EMeMew9YSL++HkDqzlal+w+9IaGBjvOkle+XpWgbRn87mPBvSo\n7sPi81LAD4k9OfFbiBgSWxgFGcLOZZ3ifzz64xrdvOuybwLzi1GtxO1Duoll\nDWEUo/fLSNvqs2EvNROmda7txiA4xqu1Z2KkzOsayhnCajThFebDRjlBpN/p\nK93OssW7WVCoNACOcFI7BB5dGizxV4u5FH1C8RLDGCde9TJjoQeYYb0iwdgq\npFp4YcSGVG9A2j/70oVi3T6SWekSE3j6tKYGr0aQ9z5QMtZM1EMMHyykljbh\njrlP\r\n=aBi9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "gitHead": "a14498cda8215caa8473f25b5ffba774ffc5da66", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/7rulnik/source-map.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_0.6.2_1612734830191_0.07420900108913053", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "source-map-js", "version": "1.0.0", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@1.0.0", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map", "bugs": {"url": "https://github.com/7rulnik/source-map/issues"}, "dist": {"shasum": "213f9a99b0e1a7895bbbf6559297ea9666280d7a", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.0.tgz", "fileCount": 15, "integrity": "sha512-eFjddZXcbqnQTBqzHRaA62imIAPJFDrfs4cQzy+9CgjrxJVmZEDPwyWzV+AClDYP0/ooqwDVpv16Vat8SvrZhQ==", "signatures": [{"sig": "MEUCICF64ZtPoTNSDbLO7uK32ELCpFGLu7Vk3XRjsIVq3MfIAiEA8UQXrtPEgPNIH6JeiJTC8zcC+vZvDrfeyDb1TfpCVcQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98811}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "typings": "source-map.d.ts", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/7rulnik/source-map.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_1.0.0_1636058948818_0.48838609245767706", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "source-map-js", "version": "1.0.1", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@1.0.1", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map", "bugs": {"url": "https://github.com/7rulnik/source-map/issues"}, "dist": {"shasum": "a1741c131e3c77d048252adfa24e23b908670caf", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.1.tgz", "fileCount": 15, "integrity": "sha512-4+TN2b3tqOCd/kaGRJ/sTYA0tR0mdXx26ipdolxcwtJVqEnqNYvlCAt1q3ypy4QMlYus+Zh34RNtYLoq2oQ4IA==", "signatures": [{"sig": "MEUCIFVxnaYclSAhZEreoCyXnZN1bwcF0W1Z99zi9DNB1oU3AiEA/n9eAWecDd2MkPW0A9aMkfox6gT+ZACx5LcI2bZLY4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh27n6CRA9TVsSAnZWagAAAIIP/Ra+qhAbFVjFqr4V2WSj\n1Bifzalm9FndsfIKoMSoe4xMU1/DLOzumIyYYfFRHEABC/0If8D9yXFuJP77\nhAQRJmwemjOHRh6/TwRAOCXCUBqtFGlUaWAp12VUniFQlqi07NCGc3xyvpcR\nn6N8VdIyN3nNmfDC3jmB2Ff6ezm1EK5FY+ITQLN6wb+Rs5AV7HENjb4bWbgi\nZzZmC3Knx4gkL/i3mQS2fA2lgDCbsK/v59OtRro1+0AyA5Y9DV32ORnw3a2v\njAMDknxQXl/zYcHOg++jNViLPuUR8RNXNYqpPCB1zazDfo3NNdzKAsqpn8BU\nidVhaD8JxSGWOHII9LSjwhBNif3jqjUonygtfmFE3sRObmnzuEqNnMRXXOHi\nNOLDGCw6i/ys98YB9lwJ5r3iR/7coegZP7nEE+0I/yLfu4IUHWun4WWimTgV\n3471IW/0Nkf8eipc8Tzuv9stlFiiFAj/uBS6dSUKnWvpV/q4v6/xYp8+fKjG\nUQ4M1Rabs8Vw4wYtJqAnz0uAeOo/InCIkgDv+oOvZJEQdrL00guw97xcMBIl\nA5LHYGtW+UVwid5TXxvEZgVuOOMYskGEIoMnOIqVLXmo/bmlJpkPbctIwm+4\nlpYFWwTOqgTfRg3dD0rkhp7vabtwxwgXQJ2QCxglyAeF6Aa7mmKYBsNFMpS3\nF+kF\r\n=DoGd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "typings": "source-map.d.ts", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/7rulnik/source-map.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "14.17.6", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_1.0.1_1636400712170_0.9803699835039048", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "source-map-js", "version": "1.0.2", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@1.0.2", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map-js", "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "dist": {"shasum": "adbc361d9c62df380125e7f161f71c826f1e490c", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz", "fileCount": 16, "integrity": "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==", "signatures": [{"sig": "MEUCIQD3iJq3pz1lRdqxJMTNnnc2/7pNChwmigaptIlftPAlagIgZxoXceWfWlyrYGdJ4AqJgfGZXF+T0YXVxu+VS9cmizo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5umPCRA9TVsSAnZWagAAMqEP/0DngYla5huM0HizcOJ2\na7DMFSMKl9SD8IOSBhIiUmI1IfdYpBiZTjipCE0RaSub07mtvpdlerfssISd\nRpCdnwS+igzz9MSEJl2e5C1HRFojOLsK7pxw0YMs78GLOga9IkchM2wcFXz7\n8If5+e8S+jPtr2Cl8CbGSmPM+gRDbCOSZICPqSK8akRNdiEADxi1szPJBnId\nfrfpefcav4hmNLB3P33xsXUEi4yEmlPunoodCUpALx9isYRjDTTeVPc2G3UE\nuFmdLtKNR009Ol49jkY4msGoKTuTTwNpaRS762KaF0Of4P1z0Tv17wrIs2CG\n7aNfqk/Jg/Rh3Qxz09FfdpIwUR6G8JCW2DP1lD7mWYBh2zA5WfNlqQK81SvM\ndJOCOdJ3qjKOVO3L0hymzO/dgl0dPzr5cU+VCULynLKPcdeyPNyFGoI5R/vv\n8SvxNPk74F/N4PsGsmz+8ZowRYX5YO2bu7P78dFvfBKKmNHejeMeB6SAlxT4\nzsjg7CQDS8ywvOot/lkVqKamRPdqasraw3hZ/cVDsTq2UpV8U44Ikmi6cbTn\nAguQmlI+GXQy/nYJV9CGdNrVXzyjeE97da0kzb+ny1WSJhmZOML4FT4HaIcQ\niOUUTKrxnfSRg+x9+5SnHvTZfn1Cla7eBuxv+hHgFUh2DRLXoyF2TEuNK3E5\ndUXm\r\n=0pTz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "gitHead": "04907d5b2dc88bef63e5399452fa6a696f90bf66", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map.d.ts", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/7rulnik/source-map-js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "14.17.0", "clean-publish": {"cleanDocs": true}, "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0", "clean-publish": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_1.0.2_1642523023379_0.8529708253561041", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "source-map-js", "version": "1.0.3", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@1.0.3", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map-js", "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "dist": {"shasum": "bbb1906b3af3657924d27edcdaf1dd93ca462eec", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.3.tgz", "fileCount": 15, "integrity": "sha512-H/5Vyeo/I1hqIdOS0m1Q63OPr0yFdZLaiVkr8ap/WyIVGdU3PyoOV/HXY8+PJE2M43+n11FY5zWQnNRNnD1UFg==", "signatures": [{"sig": "MEYCIQCAXwuyJbGaIl+YMKLVS31yIDSzAtoMsgSqHjjGeKIy1QIhAJIvsA6FmLolCbV9vHFcknZE+jRRtEv2OzevpjJznJpM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139243}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "gitHead": "2ab15d9fa829677bb627fb6127f658f319e0cbcb", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map.d.ts", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/7rulnik/source-map-js.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "18.19.0", "clean-publish": {"cleanDocs": true}, "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0", "clean-publish": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_1.0.3_1710700342359_0.6254678412353447", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "source-map-js", "version": "1.1.0", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@1.1.0", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map-js", "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "dist": {"shasum": "9e7d5cb46f0689fb6691b30f226937558d0fa94b", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.1.0.tgz", "fileCount": 15, "integrity": "sha512-9vC2SfsJzlej6MAaMPLu8HiBSHGdRAJ9hVFYN1ibZoNkeanmDmLUcIrj6G9DGL7XMJ54AKg/G75akXl1/izTOw==", "signatures": [{"sig": "MEUCIAmGLNr3vEookFo5apbIhv6w/7ACXhha5djU93eIL5fgAiEAkUMy2TyiOABqgxv5FA1SDpFmQEpc2u4PtUyfb5IwlEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139889}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "gitHead": "f0a16e5ffea9c8f928d99c6ab31a8f71aa394be6", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map.d.ts", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/7rulnik/source-map-js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "18.12.0", "clean-publish": {"cleanDocs": true}, "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0", "clean-publish": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_1.1.0_1710703962241_0.046240106532583036", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "source-map-js", "version": "1.2.0", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "source-map-js@1.2.0", "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/7rulnik/source-map-js", "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "dist": {"shasum": "16b809c162517b5b8c3e7dcd315a2a5c2612b2af", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.0.tgz", "fileCount": 15, "integrity": "sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==", "signatures": [{"sig": "MEYCIQDgQU79A0iSK9ywWnPVgnUH22ecIpUXtPObC+v4sKyt+AIhAMzFFmKuTqHgiRTl3heX4m0Zy5AN1J05G+KtSOXPmFFO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140124}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "gitHead": "9b69fab7227431b5973ce65d3f7a38c85cfdbc75", "scripts": {"toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md", "test": "npm run build && node test/run-tests.js", "build": "webpack --color"}, "typings": "source-map.d.ts", "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/7rulnik/source-map-js.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generates and consumes source maps", "directories": {}, "_nodeVersion": "18.19.0", "clean-publish": {"cleanDocs": true}, "_hasShrinkwrap": false, "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0", "clean-publish": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/source-map-js_1.2.0_1710865296608_0.8064973646371418", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "source-map-js", "description": "Generates and consumes source maps", "version": "1.2.1", "homepage": "https://github.com/7rulnik/source-map-js", "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/7rulnik/source-map-js.git"}, "main": "./source-map.js", "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"clean-publish": "^3.1.0", "doctoc": "^0.15.0", "webpack": "^1.12.0"}, "clean-publish": {"cleanDocs": true}, "typings": "source-map.d.ts", "_id": "source-map-js@1.2.1", "gitHead": "428d49f6b1e1614f082b7706fa879a3d9c64f728", "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "shasum": "1ce5650fddd87abc099eda37dcff024c2667ae46", "tarball": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "fileCount": 18, "unpackedSize": 139872, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF7zHaog7N5XY2DykQjfGxUeAeVvRFqUB8sMKuD4ChEpAiB50o3vma1R3cIXK4qnyc0QLZHy4+HJnMWjhJr9mK4jnQ=="}]}, "_npmUser": {"name": "7rulnik", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/source-map-js_1.2.1_1725812575486_0.17773916047848104"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-02-07T21:53:50.190Z", "modified": "2024-09-08T16:22:55.834Z", "0.6.2": "2021-02-07T21:53:50.369Z", "1.0.0": "2021-11-04T20:49:09.163Z", "1.0.1": "2021-11-08T19:45:12.345Z", "1.0.2": "2022-01-18T16:23:43.673Z", "1.0.3": "2024-03-17T18:32:22.535Z", "1.1.0": "2024-03-17T19:32:42.478Z", "1.2.0": "2024-03-19T16:21:36.869Z", "1.2.1": "2024-09-08T16:22:55.645Z"}, "bugs": {"url": "https://github.com/7rulnik/source-map-js/issues"}, "author": {"name": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/7rulnik/source-map-js", "repository": {"type": "git", "url": "git+https://github.com/7rulnik/source-map-js.git"}, "description": "Generates and consumes source maps", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "7rulnik", "email": "<EMAIL>"}], "readme": "# Source Map JS\n\n[![NPM](https://nodei.co/npm/source-map-js.png?downloads=true&downloadRank=true)](https://www.npmjs.com/package/source-map-js)\n\nDifference between original [source-map](https://github.com/mozilla/source-map):\n\n> TL,DR: it's fork of original source-map@0.6, but with perfomance optimizations.\n\nThis journey starts from [source-map@0.7.0](https://github.com/mozilla/source-map/blob/master/CHANGELOG.md#070). Some part of it was rewritten to Rust and WASM and API became async.\n\nIt's still a major block for many libraries like PostCSS or Sass for example because they need to migrate the whole API to the async way. This is the reason why 0.6.1 has 2x more downloads than 0.7.3 while it's faster several times.\n\n![Downloads count](media/downloads.png)\n\nMore important that WASM version has some optimizations in JS code too. This is why [community asked to create branch for 0.6 version](https://github.com/mozilla/source-map/issues/324) and port these optimizations but, sadly, the answer was «no». A bit later I discovered [the issue](https://github.com/mozilla/source-map/issues/370) created by [<PERSON> <PERSON>man (@benthemonkey)](https://github.com/benthemonkey) with no response at all.\n\n[Roman Dvornov (@lahmatiy)](https://github.com/lahmatiy) wrote a [serveral posts](https://t.me/gorshochekvarit/76) (russian, only, sorry) about source-map library in his own Telegram channel. He mentioned the article [«Maybe you don't need Rust and WASM to speed up your JS»](https://mrale.ph/blog/2018/02/03/maybe-you-dont-need-rust-to-speed-up-your-js.html) written by [Vyacheslav Egorov (@mraleph)](https://github.com/mraleph). This article contains optimizations and hacks that lead to almost the same performance compare to WASM implementation.\n\nI decided to fork the original source-map and port these optimizations from the article and several others PR from the original source-map.\n\n---------\n\nThis is a library to generate and consume the source map format\n[described here][format].\n\n[format]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit\n\n## Use with Node\n\n    $ npm install source-map-js\n\n<!-- ## Use on the Web\n\n    <script src=\"https://raw.githubusercontent.com/mozilla/source-map/master/dist/source-map.min.js\" defer></script> -->\n\n--------------------------------------------------------------------------------\n\n<!-- `npm run toc` to regenerate the Table of Contents -->\n\n<!-- START doctoc generated TOC please keep comment here to allow auto update -->\n<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->\n## Table of Contents\n\n- [Examples](#examples)\n  - [Consuming a source map](#consuming-a-source-map)\n  - [Generating a source map](#generating-a-source-map)\n    - [With SourceNode (high level API)](#with-sourcenode-high-level-api)\n    - [With SourceMapGenerator (low level API)](#with-sourcemapgenerator-low-level-api)\n- [API](#api)\n  - [SourceMapConsumer](#sourcemapconsumer)\n    - [new SourceMapConsumer(rawSourceMap)](#new-sourcemapconsumerrawsourcemap)\n    - [SourceMapConsumer.prototype.computeColumnSpans()](#sourcemapconsumerprototypecomputecolumnspans)\n    - [SourceMapConsumer.prototype.originalPositionFor(generatedPosition)](#sourcemapconsumerprototypeoriginalpositionforgeneratedposition)\n    - [SourceMapConsumer.prototype.generatedPositionFor(originalPosition)](#sourcemapconsumerprototypegeneratedpositionfororiginalposition)\n    - [SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)](#sourcemapconsumerprototypeallgeneratedpositionsfororiginalposition)\n    - [SourceMapConsumer.prototype.hasContentsOfAllSources()](#sourcemapconsumerprototypehascontentsofallsources)\n    - [SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])](#sourcemapconsumerprototypesourcecontentforsource-returnnullonmissing)\n    - [SourceMapConsumer.prototype.eachMapping(callback, context, order)](#sourcemapconsumerprototypeeachmappingcallback-context-order)\n  - [SourceMapGenerator](#sourcemapgenerator)\n    - [new SourceMapGenerator([startOfSourceMap])](#new-sourcemapgeneratorstartofsourcemap)\n    - [SourceMapGenerator.fromSourceMap(sourceMapConsumer)](#sourcemapgeneratorfromsourcemapsourcemapconsumer)\n    - [SourceMapGenerator.prototype.addMapping(mapping)](#sourcemapgeneratorprototypeaddmappingmapping)\n    - [SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)](#sourcemapgeneratorprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])](#sourcemapgeneratorprototypeapplysourcemapsourcemapconsumer-sourcefile-sourcemappath)\n    - [SourceMapGenerator.prototype.toString()](#sourcemapgeneratorprototypetostring)\n  - [SourceNode](#sourcenode)\n    - [new SourceNode([line, column, source[, chunk[, name]]])](#new-sourcenodeline-column-source-chunk-name)\n    - [SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])](#sourcenodefromstringwithsourcemapcode-sourcemapconsumer-relativepath)\n    - [SourceNode.prototype.add(chunk)](#sourcenodeprototypeaddchunk)\n    - [SourceNode.prototype.prepend(chunk)](#sourcenodeprototypeprependchunk)\n    - [SourceNode.prototype.setSourceContent(sourceFile, sourceContent)](#sourcenodeprototypesetsourcecontentsourcefile-sourcecontent)\n    - [SourceNode.prototype.walk(fn)](#sourcenodeprototypewalkfn)\n    - [SourceNode.prototype.walkSourceContents(fn)](#sourcenodeprototypewalksourcecontentsfn)\n    - [SourceNode.prototype.join(sep)](#sourcenodeprototypejoinsep)\n    - [SourceNode.prototype.replaceRight(pattern, replacement)](#sourcenodeprototypereplacerightpattern-replacement)\n    - [SourceNode.prototype.toString()](#sourcenodeprototypetostring)\n    - [SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])](#sourcenodeprototypetostringwithsourcemapstartofsourcemap)\n\n<!-- END doctoc generated TOC please keep comment here to allow auto update -->\n\n## Examples\n\n### Consuming a source map\n\n```js\nvar rawSourceMap = {\n  version: 3,\n  file: 'min.js',\n  names: ['bar', 'baz', 'n'],\n  sources: ['one.js', 'two.js'],\n  sourceRoot: 'http://example.com/www/js/',\n  mappings: 'CAAC,IAAI,IAAM,SAAUA,GAClB,OAAOC,IAAID;CCDb,IAAI,IAAM,SAAUE,GAClB,OAAOA'\n};\n\nvar smc = new SourceMapConsumer(rawSourceMap);\n\nconsole.log(smc.sources);\n// [ 'http://example.com/www/js/one.js',\n//   'http://example.com/www/js/two.js' ]\n\nconsole.log(smc.originalPositionFor({\n  line: 2,\n  column: 28\n}));\n// { source: 'http://example.com/www/js/two.js',\n//   line: 2,\n//   column: 10,\n//   name: 'n' }\n\nconsole.log(smc.generatedPositionFor({\n  source: 'http://example.com/www/js/two.js',\n  line: 2,\n  column: 10\n}));\n// { line: 2, column: 28 }\n\nsmc.eachMapping(function (m) {\n  // ...\n});\n```\n\n### Generating a source map\n\nIn depth guide:\n[**Compiling to JavaScript, and Debugging with Source Maps**](https://hacks.mozilla.org/2013/05/compiling-to-javascript-and-debugging-with-source-maps/)\n\n#### With SourceNode (high level API)\n\n```js\nfunction compile(ast) {\n  switch (ast.type) {\n  case 'BinaryExpression':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      [compile(ast.left), \" + \", compile(ast.right)]\n    );\n  case 'Literal':\n    return new SourceNode(\n      ast.location.line,\n      ast.location.column,\n      ast.location.source,\n      String(ast.value)\n    );\n  // ...\n  default:\n    throw new Error(\"Bad AST\");\n  }\n}\n\nvar ast = parse(\"40 + 2\", \"add.js\");\nconsole.log(compile(ast).toStringWithSourceMap({\n  file: 'add.js'\n}));\n// { code: '40 + 2',\n//   map: [object SourceMapGenerator] }\n```\n\n#### With SourceMapGenerator (low level API)\n\n```js\nvar map = new SourceMapGenerator({\n  file: \"source-mapped.js\"\n});\n\nmap.addMapping({\n  generated: {\n    line: 10,\n    column: 35\n  },\n  source: \"foo.js\",\n  original: {\n    line: 33,\n    column: 2\n  },\n  name: \"christopher\"\n});\n\nconsole.log(map.toString());\n// '{\"version\":3,\"file\":\"source-mapped.js\",\"sources\":[\"foo.js\"],\"names\":[\"christopher\"],\"mappings\":\";;;;;;;;;mCAgCEA\"}'\n```\n\n## API\n\nGet a reference to the module:\n\n```js\n// Node.js\nvar sourceMap = require('source-map');\n\n// Browser builds\nvar sourceMap = window.sourceMap;\n\n// Inside Firefox\nconst sourceMap = require(\"devtools/toolkit/sourcemap/source-map.js\");\n```\n\n### SourceMapConsumer\n\nA SourceMapConsumer instance represents a parsed source map which we can query\nfor information about the original file positions by giving it a file position\nin the generated source.\n\n#### new SourceMapConsumer(rawSourceMap)\n\nThe only parameter is the raw source map (either as a string which can be\n`JSON.parse`'d, or an object). According to the spec, source maps have the\nfollowing attributes:\n\n* `version`: Which version of the source map spec this map is following.\n\n* `sources`: An array of URLs to the original source files.\n\n* `names`: An array of identifiers which can be referenced by individual\n  mappings.\n\n* `sourceRoot`: Optional. The URL root from which all sources are relative.\n\n* `sourcesContent`: Optional. An array of contents of the original source files.\n\n* `mappings`: A string of base64 VLQs which contain the actual mappings.\n\n* `file`: Optional. The generated filename this source map is associated with.\n\n```js\nvar consumer = new sourceMap.SourceMapConsumer(rawSourceMapJsonData);\n```\n\n#### SourceMapConsumer.prototype.computeColumnSpans()\n\nCompute the last column for each generated mapping. The last column is\ninclusive.\n\n```js\n// Before:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n\nconsumer.computeColumnSpans();\n\n// After:\nconsumer.allGeneratedPositionsFor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1,\n//     lastColumn: 9 },\n//   { line: 2,\n//     column: 10,\n//     lastColumn: 19 },\n//   { line: 2,\n//     column: 20,\n//     lastColumn: Infinity } ]\n\n```\n\n#### SourceMapConsumer.prototype.originalPositionFor(generatedPosition)\n\nReturns the original source, line, and column information for the generated\nsource's line and column positions provided. The only argument is an object with\nthe following properties:\n\n* `line`: The line number in the generated source.  Line numbers in\n  this library are 1-based (note that the underlying source map\n  specification uses 0-based line numbers -- this library handles the\n  translation).\n\n* `column`: The column number in the generated source.  Column numbers\n  in this library are 0-based.\n\n* `bias`: Either `SourceMapConsumer.GREATEST_LOWER_BOUND` or\n  `SourceMapConsumer.LEAST_UPPER_BOUND`. Specifies whether to return the closest\n  element that is smaller than or greater than the one we are searching for,\n  respectively, if the exact element cannot be found.  Defaults to\n  `SourceMapConsumer.GREATEST_LOWER_BOUND`.\n\nand an object is returned with the following properties:\n\n* `source`: The original source file, or null if this information is not\n  available.\n\n* `line`: The line number in the original source, or null if this information is\n  not available.  The line number is 1-based.\n\n* `column`: The column number in the original source, or null if this\n  information is not available.  The column number is 0-based.\n\n* `name`: The original identifier, or null if this information is not available.\n\n```js\nconsumer.originalPositionFor({ line: 2, column: 10 })\n// { source: 'foo.coffee',\n//   line: 2,\n//   column: 2,\n//   name: null }\n\nconsumer.originalPositionFor({ line: 99999999999999999, column: 999999999999999 })\n// { source: null,\n//   line: null,\n//   column: null,\n//   name: null }\n```\n\n#### SourceMapConsumer.prototype.generatedPositionFor(originalPosition)\n\nReturns the generated line and column information for the original source,\nline, and column positions provided. The only argument is an object with\nthe following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: The column number in the original source.  The column\n  number is 0-based.\n\nand an object is returned with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.generatedPositionFor({ source: \"example.js\", line: 2, column: 10 })\n// { line: 1,\n//   column: 56 }\n```\n\n#### SourceMapConsumer.prototype.allGeneratedPositionsFor(originalPosition)\n\nReturns all generated line and column information for the original source, line,\nand column provided. If no column is provided, returns all mappings\ncorresponding to a either the line we are searching for or the next closest line\nthat has any mappings. Otherwise, returns all mappings corresponding to the\ngiven line and either the column we are searching for or the next closest column\nthat has any offsets.\n\nThe only argument is an object with the following properties:\n\n* `source`: The filename of the original source.\n\n* `line`: The line number in the original source.  The line number is\n  1-based.\n\n* `column`: Optional. The column number in the original source.  The\n  column number is 0-based.\n\nand an array of objects is returned, each with the following properties:\n\n* `line`: The line number in the generated source, or null.  The line\n  number is 1-based.\n\n* `column`: The column number in the generated source, or null.  The\n  column number is 0-based.\n\n```js\nconsumer.allGeneratedpositionsfor({ line: 2, source: \"foo.coffee\" })\n// [ { line: 2,\n//     column: 1 },\n//   { line: 2,\n//     column: 10 },\n//   { line: 2,\n//     column: 20 } ]\n```\n\n#### SourceMapConsumer.prototype.hasContentsOfAllSources()\n\nReturn true if we have the embedded source content for every source listed in\nthe source map, false otherwise.\n\nIn other words, if this method returns `true`, then\n`consumer.sourceContentFor(s)` will succeed for every source `s` in\n`consumer.sources`.\n\n```js\n// ...\nif (consumer.hasContentsOfAllSources()) {\n  consumerReadyCallback(consumer);\n} else {\n  fetchSources(consumer, consumerReadyCallback);\n}\n// ...\n```\n\n#### SourceMapConsumer.prototype.sourceContentFor(source[, returnNullOnMissing])\n\nReturns the original source content for the source provided. The only\nargument is the URL of the original source file.\n\nIf the source content for the given source is not found, then an error is\nthrown. Optionally, pass `true` as the second param to have `null` returned\ninstead.\n\n```js\nconsumer.sources\n// [ \"my-cool-lib.clj\" ]\n\nconsumer.sourceContentFor(\"my-cool-lib.clj\")\n// \"...\"\n\nconsumer.sourceContentFor(\"this is not in the source map\");\n// Error: \"this is not in the source map\" is not in the source map\n\nconsumer.sourceContentFor(\"this is not in the source map\", true);\n// null\n```\n\n#### SourceMapConsumer.prototype.eachMapping(callback, context, order)\n\nIterate over each mapping between an original source/line/column and a\ngenerated line/column in this source map.\n\n* `callback`: The function that is called with each mapping. Mappings have the\n  form `{ source, generatedLine, generatedColumn, originalLine, originalColumn,\n  name }`\n\n* `context`: Optional. If specified, this object will be the value of `this`\n  every time that `callback` is called.\n\n* `order`: Either `SourceMapConsumer.GENERATED_ORDER` or\n  `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to iterate over\n  the mappings sorted by the generated file's line/column order or the\n  original's source/line/column order, respectively. Defaults to\n  `SourceMapConsumer.GENERATED_ORDER`.\n\n```js\nconsumer.eachMapping(function (m) { console.log(m); })\n// ...\n// { source: 'illmatic.js',\n//   generatedLine: 1,\n//   generatedColumn: 0,\n//   originalLine: 1,\n//   originalColumn: 0,\n//   name: null }\n// { source: 'illmatic.js',\n//   generatedLine: 2,\n//   generatedColumn: 0,\n//   originalLine: 2,\n//   originalColumn: 0,\n//   name: null }\n// ...\n```\n### SourceMapGenerator\n\nAn instance of the SourceMapGenerator represents a source map which is being\nbuilt incrementally.\n\n#### new SourceMapGenerator([startOfSourceMap])\n\nYou may pass an object with the following properties:\n\n* `file`: The filename of the generated source that this source map is\n  associated with.\n\n* `sourceRoot`: A root for all relative URLs in this source map.\n\n* `skipValidation`: Optional. When `true`, disables validation of mappings as\n  they are added. This can improve performance but should be used with\n  discretion, as a last resort. Even then, one should avoid using this flag when\n  running tests, if possible.\n\n* `ignoreInvalidMapping`: Optional. When `true`, instead of throwing error on\n  invalid mapping, it will be ignored.\n\n```js\nvar generator = new sourceMap.SourceMapGenerator({\n  file: \"my-generated-javascript-file.js\",\n  sourceRoot: \"http://example.com/app/js/\"\n});\n```\n\n#### SourceMapGenerator.fromSourceMap(sourceMapConsumer, sourceMapGeneratorOptions)\n\nCreates a new `SourceMapGenerator` from an existing `SourceMapConsumer` instance.\n\n* `sourceMapConsumer` The SourceMap.\n\n* `sourceMapGeneratorOptions` options that will be passed to the SourceMapGenerator constructor which used under the hood.\n\n```js\nvar generator = sourceMap.SourceMapGenerator.fromSourceMap(consumer, {\n  ignoreInvalidMapping: true,\n});\n```\n\n#### SourceMapGenerator.prototype.addMapping(mapping)\n\nAdd a single mapping from original source line and column to the generated\nsource's line and column for this source map being created. The mapping object\nshould have the following properties:\n\n* `generated`: An object with the generated line and column positions.\n\n* `original`: An object with the original line and column positions.\n\n* `source`: The original source file (relative to the sourceRoot).\n\n* `name`: An optional original token name for this mapping.\n\n```js\ngenerator.addMapping({\n  source: \"module-one.scm\",\n  original: { line: 128, column: 0 },\n  generated: { line: 3, column: 456 }\n})\n```\n\n#### SourceMapGenerator.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for an original source file.\n\n* `sourceFile` the URL of the original source file.\n\n* `sourceContent` the content of the source file.\n\n```js\ngenerator.setSourceContent(\"module-one.scm\",\n                           fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceMapGenerator.prototype.applySourceMap(sourceMapConsumer[, sourceFile[, sourceMapPath]])\n\nApplies a SourceMap for a source file to the SourceMap.\nEach mapping to the supplied source file is rewritten using the\nsupplied SourceMap. Note: The resolution for the resulting mappings\nis the minimum of this map and the supplied map.\n\n* `sourceMapConsumer`: The SourceMap to be applied.\n\n* `sourceFile`: Optional. The filename of the source file.\n  If omitted, sourceMapConsumer.file will be used, if it exists.\n  Otherwise an error will be thrown.\n\n* `sourceMapPath`: Optional. The dirname of the path to the SourceMap\n  to be applied. If relative, it is relative to the SourceMap.\n\n  This parameter is needed when the two SourceMaps aren't in the same\n  directory, and the SourceMap to be applied contains relative source\n  paths. If so, those relative source paths need to be rewritten\n  relative to the SourceMap.\n\n  If omitted, it is assumed that both SourceMaps are in the same directory,\n  thus not needing any rewriting. (Supplying `'.'` has the same effect.)\n\n#### SourceMapGenerator.prototype.toString()\n\nRenders the source map being generated to a string.\n\n```js\ngenerator.toString()\n// '{\"version\":3,\"sources\":[\"module-one.scm\"],\"names\":[],\"mappings\":\"...snip...\",\"file\":\"my-generated-javascript-file.js\",\"sourceRoot\":\"http://example.com/app/js/\"}'\n```\n\n### SourceNode\n\nSourceNodes provide a way to abstract over interpolating and/or concatenating\nsnippets of generated JavaScript source code, while maintaining the line and\ncolumn information associated between those snippets and the original source\ncode. This is useful as the final intermediate representation a compiler might\nuse before outputting the generated JS and source map.\n\n#### new SourceNode([line, column, source[, chunk[, name]]])\n\n* `line`: The original line number associated with this source node, or null if\n  it isn't associated with an original line.  The line number is 1-based.\n\n* `column`: The original column number associated with this source node, or null\n  if it isn't associated with an original column.  The column number\n  is 0-based.\n\n* `source`: The original source's filename; null if no filename is provided.\n\n* `chunk`: Optional. Is immediately passed to `SourceNode.prototype.add`, see\n  below.\n\n* `name`: Optional. The original identifier.\n\n```js\nvar node = new SourceNode(1, 2, \"a.cpp\", [\n  new SourceNode(3, 4, \"b.cpp\", \"extern int status;\\n\"),\n  new SourceNode(5, 6, \"c.cpp\", \"std::string* make_string(size_t n);\\n\"),\n  new SourceNode(7, 8, \"d.cpp\", \"int main(int argc, char** argv) {}\\n\"),\n]);\n```\n\n#### SourceNode.fromStringWithSourceMap(code, sourceMapConsumer[, relativePath])\n\nCreates a SourceNode from generated code and a SourceMapConsumer.\n\n* `code`: The generated code\n\n* `sourceMapConsumer` The SourceMap for the generated code\n\n* `relativePath` The optional path that relative sources in `sourceMapConsumer`\n  should be relative to.\n\n```js\nvar consumer = new SourceMapConsumer(fs.readFileSync(\"path/to/my-file.js.map\", \"utf8\"));\nvar node = SourceNode.fromStringWithSourceMap(fs.readFileSync(\"path/to/my-file.js\"),\n                                              consumer);\n```\n\n#### SourceNode.prototype.add(chunk)\n\nAdd a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.add(\" + \");\nnode.add(otherNode);\nnode.add([leftHandOperandNode, \" + \", rightHandOperandNode]);\n```\n\n#### SourceNode.prototype.prepend(chunk)\n\nPrepend a chunk of generated JS to this source node.\n\n* `chunk`: A string snippet of generated JS code, another instance of\n   `SourceNode`, or an array where each member is one of those things.\n\n```js\nnode.prepend(\"/** Build Id: f783haef86324gf **/\\n\\n\");\n```\n\n#### SourceNode.prototype.setSourceContent(sourceFile, sourceContent)\n\nSet the source content for a source file. This will be added to the\n`SourceMap` in the `sourcesContent` field.\n\n* `sourceFile`: The filename of the source file\n\n* `sourceContent`: The content of the source file\n\n```js\nnode.setSourceContent(\"module-one.scm\",\n                      fs.readFileSync(\"path/to/module-one.scm\"))\n```\n\n#### SourceNode.prototype.walk(fn)\n\nWalk over the tree of JS snippets in this node and its children. The walking\nfunction is called once for each snippet of JS and is passed that snippet and\nthe its original associated source's line/column location.\n\n* `fn`: The traversal function.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.walk(function (code, loc) { console.log(\"WALK:\", code, loc); })\n// WALK: uno { source: 'b.js', line: 3, column: 4, name: null }\n// WALK: dos { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: tres { source: 'a.js', line: 1, column: 2, name: null }\n// WALK: quatro { source: 'c.js', line: 5, column: 6, name: null }\n```\n\n#### SourceNode.prototype.walkSourceContents(fn)\n\nWalk over the tree of SourceNodes. The walking function is called for each\nsource file content and is passed the filename and source content.\n\n* `fn`: The traversal function.\n\n```js\nvar a = new SourceNode(1, 2, \"a.js\", \"generated from a\");\na.setSourceContent(\"a.js\", \"original a\");\nvar b = new SourceNode(1, 2, \"b.js\", \"generated from b\");\nb.setSourceContent(\"b.js\", \"original b\");\nvar c = new SourceNode(1, 2, \"c.js\", \"generated from c\");\nc.setSourceContent(\"c.js\", \"original c\");\n\nvar node = new SourceNode(null, null, null, [a, b, c]);\nnode.walkSourceContents(function (source, contents) { console.log(\"WALK:\", source, \":\", contents); })\n// WALK: a.js : original a\n// WALK: b.js : original b\n// WALK: c.js : original c\n```\n\n#### SourceNode.prototype.join(sep)\n\nLike `Array.prototype.join` except for SourceNodes. Inserts the separator\nbetween each of this source node's children.\n\n* `sep`: The separator.\n\n```js\nvar lhs = new SourceNode(1, 2, \"a.rs\", \"my_copy\");\nvar operand = new SourceNode(3, 4, \"a.rs\", \"=\");\nvar rhs = new SourceNode(5, 6, \"a.rs\", \"orig.clone()\");\n\nvar node = new SourceNode(null, null, null, [ lhs, operand, rhs ]);\nvar joinedNode = node.join(\" \");\n```\n\n#### SourceNode.prototype.replaceRight(pattern, replacement)\n\nCall `String.prototype.replace` on the very right-most source snippet. Useful\nfor trimming white space from the end of a source node, etc.\n\n* `pattern`: The pattern to replace.\n\n* `replacement`: The thing to replace the pattern with.\n\n```js\n// Trim trailing white space.\nnode.replaceRight(/\\s*$/, \"\");\n```\n\n#### SourceNode.prototype.toString()\n\nReturn the string representation of this source node. Walks over the tree and\nconcatenates all the various snippets together to one string.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toString()\n// 'unodostresquatro'\n```\n\n#### SourceNode.prototype.toStringWithSourceMap([startOfSourceMap])\n\nReturns the string representation of this tree of source nodes, plus a\nSourceMapGenerator which contains all the mappings between the generated and\noriginal sources.\n\nThe arguments are the same as those to `new SourceMapGenerator`.\n\n```js\nvar node = new SourceNode(1, 2, \"a.js\", [\n  new SourceNode(3, 4, \"b.js\", \"uno\"),\n  \"dos\",\n  [\n    \"tres\",\n    new SourceNode(5, 6, \"c.js\", \"quatro\")\n  ]\n]);\n\nnode.toStringWithSourceMap({ file: \"my-output-file.js\" })\n// { code: 'unodostresquatro',\n//   map: [object SourceMapGenerator] }\n```\n", "readmeFilename": "README.md"}