{"name": "webpack-merge", "dist-tags": {"beta1": "1.0.0-beta1", "beta": "5.0.0-beta-1", "latest": "6.0.1"}, "versions": {"0.1.0": {"name": "webpack-merge", "version": "0.1.0", "dependencies": {"lodash": "^3.9.3"}, "devDependencies": {"mocha": "^2.2.5"}, "dist": {"shasum": "325dfa2980284bb3a5e4e0d78d9346fc8f716dd8", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.1.0.tgz", "integrity": "sha512-K18VJokiXCfTSaX3E+QojWgy61ZsRl3bCnYwJXl4pzGbC9gwJdilKtdY431X9YjRFlGutd4N/5jRTktZDTi/+A==", "signatures": [{"sig": "MEUCIE8KZPCIfRB+DwarGol5EMMou5q08XORWAwjpW1+pEGHAiEAhHFu8LWHog6+NpV6BRuh0Nm9JoSvdox2wwbiCrMCE10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "webpack-merge", "version": "0.1.1", "dependencies": {"lodash": "^3.9.3"}, "devDependencies": {"mocha": "^2.2.5"}, "dist": {"shasum": "f6c38c166ff7596097632cc7660d9578eff820c9", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.1.1.tgz", "integrity": "sha512-UJ0UaeidtekiEPBcHUWhCPlVP+KJJ2mdgLn+To+mggRs9Q9LVGezMVT658p1grIebAzLQx23WHgALU7929SwTw==", "signatures": [{"sig": "MEUCIHjGmGElBr5hqYzUjDKEt3ziy1A2jmahjPbJsnzl2LokAiEA9JzGudbtIE5pu/M2BHMuwLfTbulA2bksuzmLigb5syI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.2": {"name": "webpack-merge", "version": "0.1.2", "dependencies": {"lodash": "^3.9.3"}, "devDependencies": {"mocha": "^2.2.5"}, "dist": {"shasum": "613888836b34f8bc1ec96dc55df9cdad0e3e4038", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.1.2.tgz", "integrity": "sha512-jBPS6wHj/6RMxPFX8CljzWwFfdmbuvW//9nbpi53Hq6HFqSbbCPrSeJW/te32CN5yY5j4WH05Viv6dkr7hql9A==", "signatures": [{"sig": "MEQCIFkPeIqxvNrCOjwZmM/ZUvPppAviJLIfjWM9sZp4zl8UAiAaea42eA54BNkCSvijaHsRpjxndDoqd2Hhxt1M08/Biw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.3": {"name": "webpack-merge", "version": "0.1.3", "dependencies": {"lodash": "^3.9.3"}, "devDependencies": {"mocha": "^2.2.5"}, "dist": {"shasum": "2ff374ab0756fe466ee28d4bd26aafdbc8978561", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.1.3.tgz", "integrity": "sha512-nC2VTJK0cpEbXu3cuijG+k99O9mVzXCwG76GPcaLjDaEyJRnA/eA+EDQY14tEDoqnaW51k6FWT+Tso+KZ0P+ew==", "signatures": [{"sig": "MEUCIQDaeb2xkI+iBI3/SANl1GFz0x5SW0LnTqKTzA+/DVqfcQIgFOSLy/xhnyjTKZhKpwjTl/1fOOl647ZRamBO9DT/qnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "webpack-merge", "version": "0.2.0", "dependencies": {"lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5"}, "dist": {"shasum": "c160e50c86910bb1d67d90d5485957a1bc035632", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.2.0.tgz", "integrity": "sha512-AHepSTpoH5PomhVBWWePnAzhwNl4G4Srig0SB+Y+YZzNRADbSe6KUD1E6O/QIkWzDqvT4JykBHu1PlFUCKRqjg==", "signatures": [{"sig": "MEYCIQCL6dGi3Db1LOp0tIyhQoA5qOLjqjuwdiEkjoC3lUCpxQIhAIWUpjLaMgjhZ8SrFYQJopTKXZ625lV/euTMOwd9mZkb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "webpack-merge", "version": "0.3.0", "dependencies": {"lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5"}, "dist": {"shasum": "9f29d4766e75417e4681de84e5526758305ad098", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.3.0.tgz", "integrity": "sha512-J8po5D4Vqu6qZYuBVYmTllC7GaxN3HKbd5sBz3zY3/IzpO6TZZqsAJM4povOj/DdAjH+k4z1m6X3L1SeYcmLLA==", "signatures": [{"sig": "MEUCIQC4cRUodT/aefI2eur+R/k+Ebtpujka0qh2QzvFslzawgIgALH96vHX+GtDKfxGPwujlPGvluA9BU1F/eoHamZl4Y4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.1": {"name": "webpack-merge", "version": "0.3.1", "dependencies": {"lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "changelog": "^1.0.7"}, "dist": {"shasum": "14831df0998b3c65a844fa6b29287298ba0f20ae", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.3.1.tgz", "integrity": "sha512-c9VZjfEiSwGiNEGvbhHbfuKhKrGxplqwiR8uUkmEULLR+hZRwqB+zFeMnpKHdDd0qP0Ip9iRsmlWO37GMCD5sg==", "signatures": [{"sig": "MEQCIAuXgKAwacygDhVgHT9/oZ4tyITKyISVI5jrf+ZntyqaAiBYpQ0bDq8ayICRLmVui3fv2EN0MJu8BcmtdcCpEJomRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.2": {"name": "webpack-merge", "version": "0.3.2", "dependencies": {"lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "changelog": "^1.0.7"}, "dist": {"shasum": "5dfb3d5f78b14dc57fa41a011cec413bb935421e", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.3.2.tgz", "integrity": "sha512-9ixhApHN6lxA/EshxMl2hX4nGaiY0d078Lt62xJdzniWpLlJTYEJpWZZXtKdXjP1+y9tJPaIthaHYRc14RmKAA==", "signatures": [{"sig": "MEQCID0HyAqycVi74BP7F1ShMR/mhitOowWyK6mjNeaxuj0mAiBMfanC4Dc75gsN7tVGTsOxH9og+puGxFZpVnVinT0Bwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "webpack-merge", "version": "0.4.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "43b8798c8277695e2b4fa459e74ed1a4d39bea60", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.4.0.tgz", "integrity": "sha512-QwFKjRsoko8Ny5NlDXjE/OjDz0LyYqr1C7c5nzLZ+w1EXJb262TT9P/bhQrkh1SGH+IIlqP4mBjH5XKv42ntvQ==", "signatures": [{"sig": "MEYCIQDqYy8IHiHohdiM/jCRTwKYnzxuMTxXaUys//lEiiCvuQIhAKigD6VBZFsD/QZRpadSNbsGjSnK405SFm/TFIBqPgRO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "webpack-merge", "version": "0.5.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "663d54d82794dc4edd04f184107e775357119c0d", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.5.0.tgz", "integrity": "sha512-ZujVRLYTqFiyGOp/BkshBIPHa3lhR1UvwrVRK+P30bwQNUjJL2z/5C0Hg8vQyCf/tijiU7G5MjbZJmzEnxH5Cw==", "signatures": [{"sig": "MEUCIQC/fZ1MwOpu17cHmbzBvXVlUNlsP+AbNW4GcyDrm7cYpgIgDsvO0wCfk6OanV308XL5MMVYEP1Twfd1FG5pUrXwC4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.1": {"name": "webpack-merge", "version": "0.5.1", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "fe46f72ca876475c3cb2613c49d7aa904e55d3f4", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.5.1.tgz", "integrity": "sha512-M3VWulVZI2vt3FiPbJjB+09+aUH5YnieZkgePmrynmsotxtHoB7CHX2Asd2LvzRaciYU86JJDOuRYKcS4JkrGw==", "signatures": [{"sig": "MEUCIA/vvjYdzxQ/22nQ8lFo9fyUjUdivwUV+9vqKHGGR5WzAiEAhTYVaCjCd6ki9VwyIqzqjhAySPMgHSotXKVh8XFIwBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.0": {"name": "webpack-merge", "version": "0.6.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "904023bb88d3edb8cb447a6929bf1c708222dc11", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.6.0.tgz", "integrity": "sha512-hokXqGr92ftbLTLqzfOrB+CrVWiPtJ/HuVFm6RMP1p+DkYVDm00W4ylSF0LQHqnJp641JfzGFwQGx9QjLZbMvQ==", "signatures": [{"sig": "MEUCIQCd92TNw6Vpl9vfIu6fLlo8Mm1q7yaisIUpOqxiXxrO6QIgAyymVFMciPcpPJg07ICCmTPyuLaqnTvt59zNjsRVotI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.0": {"name": "webpack-merge", "version": "0.7.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "51411d4752153a8308d42765eeda7467fe527bd2", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.7.0.tgz", "integrity": "sha512-gSsWPxcacSd+TV58hrhhTqtiutDbjpVZHqtwc14zNBAzG5IDfS2IFSb0q70z8y/y9+1UqjhaePTfVvgaFl9ghA==", "signatures": [{"sig": "MEUCIQD4npjiX5aeSA2NvQISyKhMlsBpCDGsk8u4q/U7/UuO6wIgH7sHbsxT5OF9FopPSJrkzOOB2W0ljSVowOf+b2+ol90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.1": {"name": "webpack-merge", "version": "0.7.1", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "a7375f2901667fbe88b55bde0c624abcecd79cb8", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.7.1.tgz", "integrity": "sha512-km+Kt8vpzaWPUSUjz8+W5vmRdt2tK5uoBBu6ayVmk0iPymV4S7SqU2ni2tgcW8f0QpwZ2u/bZHlmawOt2gZ6Xg==", "signatures": [{"sig": "MEUCIFdMLICVYBpXm/ez5RjC6YEEQhB1YzC4AmGxJpcFvjaNAiEAk/EqPxhjl1BHEi143ifOW8PNPMxuci7uWd6SBXMc5Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.2": {"name": "webpack-merge", "version": "0.7.2", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "e23bcc1cf8b984f24553a6459683d209916d4617", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.7.2.tgz", "integrity": "sha512-YYD5+PUlkhIHmNo4Z954pkyig8ry2nkjjwlqtlpaZDEIV15kwnK/IgM+MKYsCnxL6C2I4HuxmdGikJFHcY0shQ==", "signatures": [{"sig": "MEYCIQDw5wqlMnZ/F3+2h0Qt5TAC2MuVohf7no89YM5LRmd7hAIhAPw1OFJGLKDH9Rgylp/SrQEUc1Wnenvd6Xr66yHc/LIY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.3": {"name": "webpack-merge", "version": "0.7.3", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "f636e0a4370c7987345372292a79522e5c65a27c", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.7.3.tgz", "integrity": "sha512-d3pqDs6F3vV4W8USP9w3WgWYmFyEsBf+x5H5se2pTs747J7hDRKpl4/jQ1TCWBX7Z95YCw24vHwjF7Gf13U1cQ==", "signatures": [{"sig": "MEUCIDyjLaLqt0cb/vPFAh7I6Zss73mLzVI+CvXbbFC7LvNOAiEAw8udZKA7nOqpVlDl6+ZdDb22dgKaheBYwVKJtM2x3Jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.0": {"name": "webpack-merge", "version": "0.8.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "7f5bf940287945a04ccebfd5f1e6d2a3cd79bcae", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.8.0.tgz", "integrity": "sha512-oeh6VD70sBW3agM9+8wXohh0HIh4jXyQeJMm19ZS9jK6VaRDCfRqiMJhtkFK/p5X6MZ4RPYCBG14U5TxE2vgFA==", "signatures": [{"sig": "MEYCIQDf4zW0ueZKHW0cb9ajA/7C6kClsZNjeNTKZUhnJFqE1wIhALXj+/JQlX+r0buizN45/Ebn0XkNWzivlIP7vM+cxcQl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.1": {"name": "webpack-merge", "version": "0.8.1", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "4914020f846e86973a7f568f8c450ca86be636ab", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.8.1.tgz", "integrity": "sha512-os3DdyvnHNNKmX3Agfpk3SNfr2cU9Drshq4Quz4WufloD8TuQESCqus61ellZLIZR2lyVQWG3kvFAI6DWcjqUg==", "signatures": [{"sig": "MEUCIHwlD1QKDqgE+iEz/Fjuzanpx5WN05ZdNR0xSh8juSXEAiEAvz0UWDC1nhhrNgkBI2yWAgUb8fIeFENnMpxK2GWSf8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.2": {"name": "webpack-merge", "version": "0.8.2", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "df5ba43ccfe35900e5b97d13cf74a835a584abc0", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.8.2.tgz", "integrity": "sha512-KUGCcPPFlZR/dsPd+veO5vDO3X5W5ug5os1PZiRXeE1QI9bXx1NhsA4IG7JnI64+O7FidKUWUU003Wdf3pSVrw==", "signatures": [{"sig": "MEYCIQDuA5+04HQKrRbIIaMd/X1HS8/qwlf9NCQL5ZwC4av72AIhAJ31Uy33uWsP0Y2uI/xjEqzrWYEKAUtO0QZAd5VqzoxE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.3": {"name": "webpack-merge", "version": "0.8.3", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "cad41c66fdd52027eb721df225fa6fb7ee452711", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.8.3.tgz", "integrity": "sha512-h2SjMqf23RxTRPuhe37GP4B+fDmP1xXcJzufo7xcH4CxjdsfnrwQ8mkVIIBGBQYgHP+O5FQDZAoWrOrc5CdhjQ==", "signatures": [{"sig": "MEUCIQC2+yrS328sZajg+PVgRc5g26d2izrd3lha5NvYfySflgIgfv0MytbWT6WFrVtoK3X9GxkDjww9xFQn+sWyN1sbE7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.4": {"name": "webpack-merge", "version": "0.8.4", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "23fcdb0c2affd615bd2cc911ba51740a49687414", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.8.4.tgz", "integrity": "sha512-tx6faMw1+blNFA+viI/aU4AzTB4RzUwVyFxuwTnG8gp7tsCBYDx3ZxSrQsFS3lQ4s9VkaFCEeLBmoboul2A/bQ==", "signatures": [{"sig": "MEYCIQCYeb6YitxpTtcWLCkoT7FTDA9GekTfchR4Re6UJydIkwIhAJG8mgjrf00GHXuLL1YCEgHe2Ubx+5Ys0XVwVzPtGlLD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.0": {"name": "webpack-merge", "version": "0.9.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "baa172a681937fdd3449e83fe6f37cfe6740bca1", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.9.0.tgz", "integrity": "sha512-KP/C8CNbaUmhsgO1rEZZY+rA9BV0v5fpsLh7bIr9XlGG3vUUyyIJZujgBkm4cS6OFgJGl2UZZ4KlpRtbUH0AWw==", "signatures": [{"sig": "MEUCIBaSvkoI8KQL94Pbd9nPaQYSFBaOSbgtauKv0hgtb0RPAiEAkaTabA0N1d5acTtgHm7O5xyXLq31U6nWKBtsywkoLuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.0": {"name": "webpack-merge", "version": "0.10.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "52c59a871130f5bed361befe131f3ed6a9bee940", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.10.0.tgz", "integrity": "sha512-94bMOUNTAnLdXXWHLeRweJTl9Fq8ais4a6Y21gx7mfqOJr4dFVqa6hSQr5uQvtIOgsj1rrz4lZ5ki1QITRbg8A==", "signatures": [{"sig": "MEUCIQCr2VLspOAcMctBcIk0a/KO2nXOgX2IsIE2hpR+Q8BtFgIgAOCXFfPcV6+oJWv4rJf7aUwF9Bw8nz9xqAWYOXcJDF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.0": {"name": "webpack-merge", "version": "0.11.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "5c9914337c2d7c7b88c9f2660d3937c0b80ec890", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.11.0.tgz", "integrity": "sha512-KjCnx5QhY/V/XN/QTBosA2JD+S2eoeRxsUpz2dRjxKjUCboJOJtUAketZ+os4ngeQ9PdZCLmGcgv6/ADazjzyQ==", "signatures": [{"sig": "MEUCIFPWuGGJ9oX1Y4rYxKu5w1g69+Fm6Co9g0u0F3x32F4SAiEAyoFJT+q0Foq/XTqXr7aVqcMcSnv/jcSej1qM38y9Ppk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.12.0": {"name": "webpack-merge", "version": "0.12.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "04f225f5cefcb1926c7ce4fd23cc1a779a1acfb5", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.12.0.tgz", "integrity": "sha512-hT0QCuHTJ2q/GmBNjrC7l1sPXROGuqLW1unhVC1X7tvzPrvB+m+3tSphYTohRlMdCxyVuu5n1kKUNHGnJ335TA==", "signatures": [{"sig": "MEYCIQCtT9ESsSC6/MIeMuRTXz5r1813Eaojnqv2Vzkt99PCHAIhAP5TMFxKJSnug5eCMYUNeX3s2ezqKhYPCW8uMQFy8qNW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.0": {"name": "webpack-merge", "version": "0.13.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "1cacc95cec8a7581b99fa35d3fd9f40a152abafb", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.13.0.tgz", "integrity": "sha512-dDJfTOFpoXnVx9Kxe7pxo86NPg0FlVMgP4FVVRFiGGMII31ciPaI8A4rxSgivDhNhgd1ytIQDqNIcfSs/WL+gQ==", "signatures": [{"sig": "MEYCIQDqfT+udZEc+yhYNyg2u0bO1Eqbuav+SaEbpzEdkmMwngIhAPHGYYHZWhNDOI8ow8WgwJ+DfQQNlzzOIJ/J3sivqm7o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.0": {"name": "webpack-merge", "version": "0.14.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "ca25f372d72f4cf820cb38dc6b750753f8b42a29", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.14.0.tgz", "integrity": "sha512-6k+Yf9Mk+rPMyLNovF6pSvZLUJeHvumJHwt0aa9O/zSuk8uPTiYiD8o1XuYJ2vhLxRYMzJiw8O+WtF4CbgI/PA==", "signatures": [{"sig": "MEYCIQDYWgkIng2T8F/b85+PC4CzJqKFCI9iJ3BKdcxK2U/R/QIhAM8loyDVpoSKVcZEpAyMNdwR8HmlrpAliekt32JnDSpt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.1": {"name": "webpack-merge", "version": "0.14.1", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "d6bfe6d9360a024e1e7f8e6383ae735f1737cd23", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.14.1.tgz", "integrity": "sha512-fMxIKVmtFTFxnoVUfr8+4UvRDNdfrKl6opz6dd5ssSvVZPCfcn0HmRt1kiXjXIbyiYL3gfS3x7NXutqg9gxYHw==", "signatures": [{"sig": "MEUCIHvjtprBppHEP4K36OLRBpUjpmLGRyJwkH5g3tEoyajxAiEAiG2Dri95XaOQINjQS34t1xajulGt13Xhq8aArgpm8UE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.0": {"name": "webpack-merge", "version": "0.15.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "f6a930516d5eccba0baabeb30ea96b6a6737c193", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.15.0.tgz", "integrity": "sha512-u/N7Cy7hMz9aLoH15MBKVaatVyk333Q3Fbf86yRk87s+hpNHtVYhhckMvxSKFZpyvGzxvDzFH0rnCAai7sqBRw==", "signatures": [{"sig": "MEYCIQCRO34qLqcYtTZoLuE/NX5kTfYHxZJveKiCIuQt3JEJugIhAN6RFv+VMAl7bbYUdgtUS22K0TrjcLUXKSbk6UYJqfJQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.16.0": {"name": "webpack-merge", "version": "0.16.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "1b76bfd7d69f349cd805e2094a36dbfccf63bc99", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.16.0.tgz", "integrity": "sha512-PUsuS8y0SMWfEiqto1vegrVY+noJp/OiHsdGc2VybCfpO3mNRmIM2/CD6ktm8589A2z+ylzWyLYCBGOWkO/ufQ==", "signatures": [{"sig": "MEUCIQD1F+r8UbF/oax4+6+Ki85faLBKI7xSRgruwaq/T3TqTgIgX9EE+mrDjcAV2zmr/x3Cj5P4fscF2vs86LrPN9RuvUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.17.0": {"name": "webpack-merge", "version": "0.17.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "3548ef319e3a72676f831edfe7cba687aa781c84", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.17.0.tgz", "integrity": "sha512-mQwUYT+1VPxtoePl7eZdwHqCUdcEKHRAOHTDJmSf32OklNhk5W6HPv9dv75n5/Uk9tmZRyZyg+OL3BaRl50mnw==", "signatures": [{"sig": "MEYCIQCBjv+q09eHJyjVII9qM5zcZgAYn+3OYDbNM6ewXmk1MgIhAPAgBeA7jqXqOBdWp1ceQLQAfQpfYM6eFPnwazZet+jT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.18.0": {"name": "webpack-merge", "version": "0.18.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "287c737c18209d35ec5826e4ac69e9b2ed1e0564", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.18.0.tgz", "integrity": "sha512-LdfHkkp9QAB/Fxj69ej1MCk+rl/PbVCnuTdJhsmdKpHNh4q+q9nUwfEwzWJXzv7Xqu9vSs2yQ9xuTzT/pHnp+w==", "signatures": [{"sig": "MEQCIEHJde7v7+rRX8XcFncK58RtZYMyLEUODpmWdLpRxdoeAiALr84wCFnicxOGyGmgULW8rc9QQ7bDNDCbQYhb1o3mPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.19.0": {"name": "webpack-merge", "version": "0.19.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "a5bd927e0c069d3ffe8b99c7f176a388a96aa5dd", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.19.0.tgz", "integrity": "sha512-qxVEt2fdb5jsrpQRCTmG0+5LvIWIiWuBuGYnnYcX8dvhlUDjN/ZgJgibju9euc3qvVzUDDpyKwXJtEQGkkHrmw==", "signatures": [{"sig": "MEYCIQDvYi0TVHJ0WNThDnOYU8RlGMkXFNLPja96tciDflXcPgIhAKDoElD1vmuIjwEuskO8bx/hcondNdNIwzfWlu1TKQ81", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.20.0": {"name": "webpack-merge", "version": "0.20.0", "dependencies": {"lodash.find": "^3.2.1", "lodash.merge": "^3.3.2", "lodash.isequal": "^4.2.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^3.2.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "e4b73429517181a287c59c8cafef5fc9eb1d9705", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-0.20.0.tgz", "integrity": "sha512-BANuU/9IZJniOEaDsZLaQtLTwKNpPb9t+BL4jE1Ur3TOXzuqWVdxsvCobDmzUsGPsApvJZE1m6yYXJIrt4Iqdw==", "signatures": [{"sig": "MEUCIHvTVkGUxk7MBwjhpUjEEO+IhuVLF08q2/fO1bcEjYIRAiEAwy9cfghcvUqH6qUM+FJy+OMnZSMV4+oYLZl/kqb1B4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-beta1": {"name": "webpack-merge", "version": "1.0.0-beta1", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "63dd4c7bb6ca55c065684cb098651abe428e1e2e", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.0.0-beta1.tgz", "integrity": "sha512-A3Dk0r4DJf4lEwwMdEE1CoDhZ3fOFm9hIgJbXjwTFvF3vj9xwPdpYjkmuFOHpWweOMwCoWOnFGO+KBPYq7AwOg==", "signatures": [{"sig": "MEUCIQCL8d0IkZLBg8yNL4KNOrfPO/ym0yZ6aSgitl+mvyoWiAIgZmfqIDkRMbaAGDBEf79H7G9M+XJMoteOwIedIUL4xTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "webpack-merge", "version": "1.0.0", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "4623780e7f986e4fbd51a08b2f4d2165f7944dea", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.0.0.tgz", "integrity": "sha512-jFI7w7IIq+/8jGJq4F5fkzzgffnPk/SYPbpEc8PCDf/U+PYKNH5XS3bSLqw/wZYzi3RfGgn6TOHQBVHSQHDvgw==", "signatures": [{"sig": "MEUCID9MX9br2BZ/z9VuQXD9/dszuPqGTiyZj96eoeu5WhzPAiEA8V+OjDyVW/rrUdj+MMnreqsnwxyrEE74rua6oMXPDmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "webpack-merge", "version": "1.0.1", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "d8dd735edf1419b922d154ec9a192854e6ed4e0e", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.0.1.tgz", "integrity": "sha512-JoH5KoOfc49JIhRrTS60N6BY0rSaBd32BLM4rwDwO+iBOyyuIsRGmUHmZN+kuWNa1CL1w8qE3e3oIKIFpx1Sag==", "signatures": [{"sig": "MEUCIAU/zjn+P/O74IRg1c3pnkttciZikc0+36lqlijvktR2AiEAtagSNqf0P82i7hitR0gPC7CYq7SUj5NpAqPW2ZsTCbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "webpack-merge", "version": "1.0.2", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.clonedeep": "^4.5.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "b28f4c895361f1a985a96952760d7170679779b4", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.0.2.tgz", "integrity": "sha512-31lLIg+tWPcMqpBTL1LGalzq1aPjxOGGr6I45d7LinXkbMyxq+Knt/wRxij5E20rnl82EMh742r95wRgwUK/IA==", "signatures": [{"sig": "MEYCIQCepoOfHlWtUQ/IUYhavcJcmOABRrVN7JmSckhG3hMlGQIhAIehsK+/0qHHLlhNmesGcwAQyyMyDsKtKSw2939yKBEW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "webpack-merge", "version": "1.1.0", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.clonedeep": "^4.5.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "e51d941c75f180219e73cc514cbf709f109ea26f", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.1.0.tgz", "integrity": "sha512-Ig49ry1SSssCTqyamn7C2HUU3GIveo9cpAn0JITDo/0eztzjxc6ZikC7hX+iZWXQzmNde0k7QNdRlcQbbqinYQ==", "signatures": [{"sig": "MEQCICK3NNxhc6KkqMeP4uTswhJdGFL1xEAq3p6V3CpdaA17AiBqpY08lzE9F8z/RCtQ6BaD6uektfzleRs3ejdXWNolvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "webpack-merge", "version": "1.1.1", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.clonedeep": "^4.5.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^1.10.3", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-config-airbnb": "^2.1.1"}, "dist": {"shasum": "2816dce279f38fe05d62b6411144b7638553e61c", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.1.1.tgz", "integrity": "sha512-jW1+EMU1GsO0rMGsZhB4pmhsYjzz5ot+XOeWt8FAkKdJriZzzz1uvBpl8baUQJEiE5di4IkV6zyubcaPf+QdgA==", "signatures": [{"sig": "MEQCICLzqlMO/Kp23NlPxkLF700e+7gMg5V1ncN7v8Ve/9i/AiBGLKCu0JgK35RDBH7TPzoKeCEgqsy0HuvQfsVOAhiRGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "webpack-merge", "version": "1.1.2", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.clonedeep": "^4.5.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "49f2a68ba5fd34bb13c338c184c7028d93843432", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-1.1.2.tgz", "integrity": "sha512-q6wVGizxrJtw+5olUFwuKGcrKPECrbhuykVeouewqHFbZS5pf02ItvHJhKwFXq92RyVY8s5Yx6mDOk+B7fkPAw==", "signatures": [{"sig": "MEUCIEbOeFnF2CN1uIzg5Vp6OuluDqPuTGJh0YBonkDMJX8wAiEA2v98UE4gKW6l5YyeWDjUSQUmoTEwbMBMiH3WyyvA5YY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "webpack-merge", "version": "2.0.0", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.clonedeep": "^4.5.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "fd328707d26f6f9852be2767557cef51d942c1dc", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.0.0.tgz", "integrity": "sha512-sPj5oPbqfkVYhGLuyLohxYHYURZsQm3H98E2akPWl6eL5ngWbQTdsl0v2m45eBwI/H00dQrwRJEqD98yICCs2w==", "signatures": [{"sig": "MEUCIQCpl5P30j4BkFbNFfi+uWGoLDbxGO3WJMfdf7gJHwnqBwIgYwgvsKZ49hfO4/LfxCYYB2Qr6yWFkgKUjV4fdSHtJFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "webpack-merge", "version": "2.1.0", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.isnumber": "^3.0.3", "lodash.isobject": "^3.0.2", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.clonedeepwith": "^4.5.0", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "webpack": "^2.2.0-rc.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "cdf35a4d472bec492ace1e84ce4af9f5f52c7dc6", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.1.0.tgz", "integrity": "sha512-WaLEKHR3DV/N/mQtr8vzAstDCVEfPGfKZNfr8lYWs/MQ93WN639WsgM8zFucQ/HHKV+A1jUbuFn2RH9GTPbtrg==", "signatures": [{"sig": "MEYCIQCXgYxx0BzNG33kVd+bbEpsRu1hY1vx8KiLMY/r6NzVvQIhAOhrCvQRhiY2OCaZNjcA1Ft5lJBRfrGNS012aDHT4wX2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.1": {"name": "webpack-merge", "version": "2.1.1", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.isnumber": "^3.0.3", "lodash.isobject": "^3.0.2", "lodash.isstring": "^4.0.1", "lodash.clonedeep": "^4.5.0", "lodash.isboolean": "^3.0.3", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.clonedeepwith": "^4.5.0", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "webpack": "^2.2.0-rc.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "e41b279e01628d1232a10087069dea59dfbbf37a", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.1.1.tgz", "integrity": "sha512-wZbLpB10x1NRO71EGHzBmoq+VqdUQUNKZOkEY2cXqKQJGDREdN8FwYJ4+EqsZsZ0DRTaw/dDgWYJUutFs2nljA==", "signatures": [{"sig": "MEUCIHHMy0b8HzT4c6n2HgeeBptWEDfpfrI8pSMghiHC0esbAiEA55LcNNR5gfhaN0ZCequV4NWVIAoefzvMs3H6u1+Ftvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "webpack-merge", "version": "2.2.0", "dependencies": {"lodash.isequal": "^4.4.0", "lodash.clonedeep": "^4.5.0", "lodash.mergewith": "^4.6.0", "lodash.unionwith": "^4.6.0", "lodash.isfunction": "^3.0.8", "lodash.isplainobject": "^4.0.6", "lodash.differencewith": "^4.5.0"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-preset-es2015": "^6.3.13", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "8e26ee10d440be647f533ad3a93e1670fe5c5506", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.2.0.tgz", "integrity": "sha512-pW5jIYziVJH6/71p/kaFeJuIMsmFrMoAyIQEJHPOyKrK6IUe9aUBw5GvEl8H9voWWI+gzjyFd9VdtZYwk9EQsA==", "signatures": [{"sig": "MEQCIDh+gBn58fyvCLTiiBzBSfU/PhOSqgjzXPNvHEsI4T5rAiA3mWC5rrOuZ7I/sxq75HH9Gr3IiF7fF40JdoIeLzcUfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.3.0": {"name": "webpack-merge", "version": "2.3.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.3.13", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "2cdf2877f1979b8ea368873e628b4717ae9cc09d", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.3.0.tgz", "integrity": "sha512-XrM7eiK4MyA1haNzG6mb/rhqd70/91R1rKEtN/Q774Ph81NbDbC05RgTg4wdu2S6G1SIE5oJyY80StiQwg5AYg==", "signatures": [{"sig": "MEUCIGNF4hlAIf9FHBlfFKmYjNVAo1TOM3yP5Opl9lkSO5x7AiEAgCSMY8sql4vkVA4bUvmdFEH+/ZpajHIIwf6NCzL/mFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.3.1": {"name": "webpack-merge", "version": "2.3.1", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.12.0", "webpack": "^1.14.0", "babel-cli": "^6.3.17", "npm-watch": "^0.1.6", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.3.13", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.8.0", "eslint-config-airbnb": "^13.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^2.2.3"}, "dist": {"shasum": "152ff773c5fbc6e398015fdf75835de343c33777", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.3.1.tgz", "integrity": "sha512-g3IMc/kjU3siZRluWzGGCJFx1GRY1JBreGwwn0LlXj09HmHgXSfhN4zXaKNL7JamwOhkvVU1yboU0TuFuNv9gA==", "signatures": [{"sig": "MEYCIQD/TNmIrnyWEAi/3LzU93rYvx0CCaiWxYO8YxS0LOVijAIhAMoBiZQ0BQ4iFasv+J3ssmONQ/mmNHeF+jfa2sjrzWoa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.4.0": {"name": "webpack-merge", "version": "2.4.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "4c518d471632c29ae22e83687c2f42a9cd5f35ea", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.4.0.tgz", "integrity": "sha512-oUDkGPwcNKtopZlbtJ3OdlVTv7HqRjkRYyDp2U7prMX7lAbCCdtv0kmH/RQpHYv0rPEgLBamprkj6ZFsuZspMQ==", "signatures": [{"sig": "MEUCIQDIaZ8JxvttN8VhiBNiVBjaGztA3ZlLaRHYJs3izAyTWgIgfhJueWakf6ad35btb25ehAOnH6cddG8QrJBsub1+0xE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.5.0": {"name": "webpack-merge", "version": "2.5.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "1f4ba25b533f0a9a3be15716b5bd7bd6b6af94fb", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.5.0.tgz", "integrity": "sha512-TCkFd+fFXH2B1965cjPkeRXNUR5FXH+WmqsW87hheuxt19yuW4ea82i7WXSijThAhhOhmrnIsfVd3BWQUJ8dqQ==", "signatures": [{"sig": "MEYCIQDAMqbK0iYvJlLolNbObrk/7M+xrlEom22kBG/4yK2eaQIhAIVFSVe5tTUeo/kjpTLCnyExhcLgon88BMTeyBbgA2Cr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.6.0": {"name": "webpack-merge", "version": "2.6.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "52333b44d3a2dcf6143bafcd9f77e0f49e2a25be", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.6.0.tgz", "integrity": "sha512-rtTx3sMGgG2d047c9gIziGKvNqe0PcjtedgS1v7dpyMZsUsNuunfN2C2dVXmbndpfrn2xR93nyIfi+KH/Nb+Vg==", "signatures": [{"sig": "MEQCIFCvgrmJ8tqwUnupcy3hQ6EO7C2UvZKeF+S7Y63KM1L6AiA7v7HUoYbB/87+LJJavPBCu12RuCX7+zhv3fjlnuW6mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.6.1": {"name": "webpack-merge", "version": "2.6.1", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "f1d801d2c5d39f83ffec9f119240b3e3be994a1c", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-2.6.1.tgz", "integrity": "sha512-MoUJKKaHgNksVsO/1T2z7aDCojb2BPr90nkzzcyCgiZupWciuh49jFnZgip9zaZYcAvgytL/KUSEuy5D3Jmkow==", "signatures": [{"sig": "MEQCICZZ3otPj8LOrT3Vl8gefI8Yj41H9CUfGr1ho8y2A/6JAiBZBLZI/kldcKClYY1sxOnTqIV1HEHzxYPTDv/2BipOkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "webpack-merge", "version": "3.0.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "f2c9c28691bc44fdb124745cb84a8fab74125701", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-3.0.0.tgz", "integrity": "sha512-WysHk43b4UDaMNOe2/lgeSxOwMXJlJ4bLOakC3ziv+7U4QSGR2XoRmIk5/BokheovFzhjNAMg558Ifee3HGFfA==", "signatures": [{"sig": "MEQCIDr3O8eJRKi7X29xALWijwFN2fk+0IHYK6M1kDUUn+1EAiAHgt1G8wv97czAYwxoWjHIHqAFG/WcWKlTCiQu5foOmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "webpack-merge", "version": "4.0.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "4347ebeb9e71aaa413baef66be74d4eb2656b66a", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.0.0.tgz", "integrity": "sha512-POt51MzBj8NUGrFWZ3qqs2aU4krQcWxqjQrC/KKAfv1qZtYpUAb5sUEubxNVE0shYOGlee4QIlRu0IeQIDltmA==", "signatures": [{"sig": "MEUCIEtzlXq0COaa2jD3UG5gZU0BZIGC/P5z2EulUbb1fGhcAiEA9v+gvNLbFu8NMsWmpyOrgb44YibLRCzBynKVqKES69E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.0": {"name": "webpack-merge", "version": "4.1.0", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "6ad72223b3e0b837e531e4597c199f909361511e", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.0.tgz", "integrity": "sha512-YCqyu0Q+92XQMT32MakGk9Hg4ToATWjI1cN7i2b37LKjjEbpWV8Q6qs8aie98U/moBSNLBHSG3GhxsxBnv4T0Q==", "signatures": [{"sig": "MEUCIFhswUlBOLUO3o8PRf6Kzbclto81G/tIsHdOGUql25TeAiEA5GH5m3e+p83TpyHDkNrmTxZ+a5xIRSJornCyPvrV+DA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.1": {"name": "webpack-merge", "version": "4.1.1", "dependencies": {"lodash": "^4.17.4"}, "devDependencies": {"mocha": "^3.2.0", "eslint": "^3.13.1", "webpack": "^1.14.0", "istanbul": "^0.4.5", "babel-cli": "^6.18.0", "npm-watch": "^0.1.7", "git-prepush-hook": "^1.0.1", "babel-plugin-lodash": "^3.2.11", "babel-preset-es2015": "^6.18.0", "copy-webpack-plugin": "^4.0.1", "eslint-plugin-react": "^6.9.0", "eslint-config-airbnb": "^14.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "f1197a0a973e69c6fbeeb6d658219aa8c0c13555", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.1.tgz", "integrity": "sha512-geQsZ86YkXOVOjvPC5yv3JSNnL6/X3Kzh935AQ/gJNEYXEfJDQFu/sdFuktS9OW2JcH/SJec8TGfRdrpHshH7A==", "signatures": [{"sig": "MEQCIFGkX1SjrC13+gg1HdPIa+x5KB+2ik1MHRKZSvmvJ+VvAiBwW+N4etYC/Pv5REpuvGR0JKnEKBweE0nsXoR3IOrH0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.2": {"name": "webpack-merge", "version": "4.1.2", "dependencies": {"lodash": "^4.17.5"}, "devDependencies": {"mocha": "^3.5.3", "eslint": "^3.19.0", "webpack": "^1.15.0", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "npm-watch": "^0.1.9", "git-prepush-hook": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint-plugin-react": "^6.10.3", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "5d372dddd3e1e5f8874f5bf5a8e929db09feb216", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.2.tgz", "fileCount": 9, "integrity": "sha512-/0QYwW/H1N/CdXYA2PNPVbsxO3u2Fpz34vs72xm03SRfg6bMNGfMJIQEpQjKRvkG2JvT6oRJFpDtSrwbX8Jzvw==", "signatures": [{"sig": "MEUCIBcJP0f5J6Rxmuo0QeY4STePY2lnDfmo4lksFivwKl5iAiEA9W01xubQJGX3/oAXPP9XeUz0i/ssysZLkkD967ZOoGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39021}}, "4.1.3": {"name": "webpack-merge", "version": "4.1.3", "dependencies": {"lodash": "^4.17.5"}, "devDependencies": {"mocha": "^3.5.3", "eslint": "^3.19.0", "webpack": "^1.15.0", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "npm-watch": "^0.1.9", "git-prepush-hook": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint-plugin-react": "^6.10.3", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "8aaff2108a19c29849bc9ad2a7fd7fce68e87c4a", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.3.tgz", "fileCount": 9, "integrity": "sha512-zxwAIGK7nKdu5CIZL0BjTQoq3elV0t0MfB7rUC1zj668geid52abs6hN/ACwZdK6LeMS8dC9B6WmtF978zH5mg==", "signatures": [{"sig": "MEQCIDyNChfigCdGbUMCrnGDg4v1FRYGeUa+up5G6bxxE8zAAiA/C54VG2/sRx8rm5zli6GUe6hApHPh97tmKsXCMgBKdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIiPfCRA9TVsSAnZWagAAAP4P/0zP4EhwVZf0hhI7E/Nk\n/3GEfUZMW6mvMW3nzfUIhrKRAHLebmMqQoyf8tSE5B1aFq4UxidcxhqQ2X5x\nLGjo93u//0Y2euN4jU6YosvkG6w36xGv1W9x5UgbnDzscgzO98dwTcOK2jo7\nyadwBh489hAieT5vTDs/bQ5NIMHgrtmAysPPBkyoTuEZUVFop9n3EoOs8bAu\nRldT2xTdiz5ySBqqo6rrjeUV4ayvts9EE1MvomuxC3+eFIN/Y7pTGQFgmCBG\n/bhS2S5i0Ow+0NbzcDhYlQf4YN97Y5hnsV6c5e9E07GCKNJs0bsr/By1CJC3\n6arQYsUXDy5DA90sMQkuhpVJgk4UIA5tO5bZ+8Njk17DsmKFuDdyVxdan4+f\nt8KHrZwDXobLXQ6ahTERK18wl+coRtrcBX5EKs5dIZGOjRnerighbJ2cZ2HN\nX4lAznVBWtqIcVguRaRSoGzAj2obz5jemta0B/ZeOzdkT8LZVFUUYMMnQW+e\nIbNc+npFXOTEQO55QBCGaq/jFGZGk+aT96EJ74UuhK+JRqNR7N1HAuQDqheD\nxK2mmOM1FIgO0wkllqsztvghx1TXDAF2DG3SaISgkj9keVOnv07nSFeuMflU\n+qMyHNHa724ZfAnjvS9eq3hPVPYN2vh0gzCya6bvLC0m0/R4BxAx/7p2wFLg\nSohO\r\n=KJ9v\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.4": {"name": "webpack-merge", "version": "4.1.4", "dependencies": {"lodash": "^4.17.5"}, "devDependencies": {"mocha": "^3.5.3", "eslint": "^3.19.0", "webpack": "^1.15.0", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "npm-watch": "^0.1.9", "git-prepush-hook": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint-plugin-react": "^6.10.3", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "0fde38eabf2d5fd85251c24a5a8c48f8a3f4eb7b", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.4.tgz", "fileCount": 9, "integrity": "sha512-TmSe1HZKeOPey3oy1Ov2iS3guIZjWvMT2BBJDzzT5jScHTjVC3mpjJofgueEzaEd6ibhxRDD6MIblDr8tzh8iQ==", "signatures": [{"sig": "MEQCIBPLHwYiBui/SSgt/FKvWN6Q7e9hcWd4YOLWqqa27QOzAiBre6KJr8WbFtFhlcn9nOQ7HWB2xKH7KZC0VyXnC7FdfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYg+SCRA9TVsSAnZWagAAKT0QAI9kF0oE5+umgMkLFuze\nN2PkIAPPsY2rhQkLKVuUn2ZFvFjZY4jzIORLQAtV5zc262kwkEsM6O0GG5Hl\n5RdLvlDJqzGNhdX4QzfkW4roFMa3jH6JxKVjLroR/ubcJ+lNeByECiduyT3Z\nw+UuBDfseWUwjbkZfjJjkU+CE8V+KwLc627upSfPKh9ydA1gEq3pDPVdhHs8\n4TWu+nCHq9mdPlSV66Pd25GcmT7jNNIIFxPdYzWkEDpS7ALO0RxMjFirm/RS\nxGLq0qCrcqRvXqOP+8WzFkcCUuZhTiBddGmg2Dr3RRlfNJfjJgVWd1VXc/QM\nt+PYIk3rH3rpCDU+oKRsAHNHtyLRT/bkZd6yI7r0WtCIo52Z1OW+aqqqJm62\nPQW6rNMrSyb59PcqhN7mkFUdDEYep8e6+eIXezx54jWnEXr9Ei7U9SeFvp6e\nxJwmiuclRNso8Du3HhLZk2iNiZM9PzY3HHyCnk0bUv0T1Vn+NMQR+F8Hmtvw\nvnq+Ebvkaww5tazw3NU+XqcPjIPeUdYY1jQMCsUFnPQXQmB7vDCVn/U3KVID\ncaJnUIOb7q9Itkh1dzIQj6VNsE8FA6S95yfVCxHJii1uIsLG+FYl2RNq4svV\nrv9X8w3kySjbfhWjrgNrs2Eb1SvDWDvaaZaqVsEWXN8Kg9CCrVnRCzz2liMH\nH1vr\r\n=LWwo\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.5": {"name": "webpack-merge", "version": "4.1.5", "dependencies": {"lodash": "^4.17.5"}, "devDependencies": {"mocha": "^3.5.3", "eslint": "^3.19.0", "webpack": "^1.15.0", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "npm-watch": "^0.1.9", "git-prepush-hook": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint-plugin-react": "^6.10.3", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "2be31e846c20767d1bef56bdca64c328a681190a", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.1.5.tgz", "fileCount": 14, "integrity": "sha512-sVcM+MMJv6DO0C0GLLltx8mUlGMKXE0zBsuMqZ9jz2X9gsekALw6Rs0cAfTWc97VuWS6NpVUa78959zANnMMLQ==", "signatures": [{"sig": "MEYCIQDbivi7kTvj6aiMEnFNWyAPyh8ONAhDh1tYHYStwZg4BgIhAOujnRDA6xvtn+OU7AF6ggB3Js5WSUB8Z3nmDcMhvI1d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDX6YCRA9TVsSAnZWagAAdwoQAKN5y3YzGVrbxdlm6+Ga\n9VDfaAvhnLzhtRgT+yYgtkLF0/x5zipYALSkIlqeuARCGAgWWW4ZOBMi6/vX\nfCNZHz5PU05aRUYJhBX4Lj350UfHM06KvcP44GnHz695Gcv2PdZPdmrFMwTv\noHf882bVOhbI17R2JpQ4Kh37j29jMbcgdhje3uMrBpeqQ/qJDSCUhri5hXXh\nKmZXFFnbmFZ2syiYlN7KuDxLggbn6AEFxD6re56fsCvvE04ijVieO4ouGyA9\nIcpuJKZHipnfOQSStD52PSYw+fMzFDCOLC9kXqOSrw+J7SjGX05ZpuQV3cjT\nEo1ecX+47sO2bbd6M9zvesg3yVqlCZEVeq4+epyoRmbsp1MEi2UlqgGxUF9j\n3XqctYPVfUUbzptMXxMZWRy6VL6aTrvMPNyhYbh9HKlII9T1IZRP//Iy09P7\n9szgADqR7noRimFHFo2I3oobCxjnvbvY/PhD704Jr0z9tzG/o97Uahs2WPj+\nKaQ29fhPAgi7UzlKmgKWY89TQRzLr+m7etzaQWRImrqCvH9n5X2iSEz1EyQT\n42hH9MfplOnx0LE1GprRffk14CwwB0GyXOqLaoANrNiW9IVTQdPhOKhI84AO\neTu4fgQtLdVS0/RY7ilkGS6lyy35YeKPxHcu1BvOGFEkeQV29UrNNsbOnb/3\nJjNx\r\n=EkId\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.1": {"name": "webpack-merge", "version": "4.2.1", "dependencies": {"lodash": "^4.17.5"}, "devDependencies": {"mocha": "^3.5.3", "eslint": "^3.19.0", "webpack": "^1.15.0", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "npm-watch": "^0.1.9", "git-prepush-hook": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint-plugin-react": "^6.10.3", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "5e923cf802ea2ace4fd5af1d3247368a633489b4", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.1.tgz", "fileCount": 14, "integrity": "sha512-4p8WQyS98bUJcCvFMbdGZyZmsKuWjWVnVHnAS3FFg0HDaRVrPbkivx2RYCre8UiemD67RsiFFLfn4JhLAin8Vw==", "signatures": [{"sig": "MEQCIAEE9DBynM/8vsa7GL+1aAMAasuHnWfPE+ZXAnr2Az0bAiBCOhZVV+T6mXAB+KQkJTPp8VWDiiHOYD8E0uSBCmHP1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcL3wRCRA9TVsSAnZWagAAR7cP/3FSGM8fNXQdancdMvcS\nzGYMzVdSKDTnhtKiCpg7SeMCdId2VrUt7+pRs/q3kijqmC5TpIDnPwiZYDeF\nzSpijuKpT0DixMMwirbXMNNmRU+gDHA3yuBq8k+CUdvYIdGnCzUCXSbL2jtn\nR75yLgMC+XuGu5YYYDzAs6SIJhYnl0EEhOJFjroRrSGCP+jsNDFL8ky8CkAT\nrC6AqnAiu2JmvfdhvUd+g6N397M13gPiCTLTPOXOyub1WgFqMyQAydihI0jg\nkcXOTgY2q4sgFPaeWolO3CMlwidW4faZsZcnlHyyeE2eO3IQvjJSoWdOT5bI\nXl3szbVWhb3NWXmX7xk3zuLAWI2B2TvtfmR5qH/0CcJ056EkqboMf+2mXddU\nxPQU6sXHrZjingDI9YddzZlf2XmB6M1RNsfZZBSdkf+ctBO8ZZQ3PPoH70aY\ncHSD1w+eqPUMnfpPg9aVocJXC60mliDK3olmxYtE4VeOFq62160maRXCDg10\nJHKk6AC1/3p5HocZ526xtUofj+FOsuOh9O3xpiL/kl7SYuth5taXVsgv93i2\n35E99Tj/mL+SM5gd3MlbQkmD3IYw0/bRDBLRLgwfWkXC2RRwV6QY1Yy4LPkb\nwT22jorUDgk8iN/CnmplasUQTZ8En7QkBKxVOKl2uRNfladvAjKkKhP6v5mI\nuzP3\r\n=9p78\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.2": {"name": "webpack-merge", "version": "4.2.2", "dependencies": {"lodash": "^4.17.15"}, "devDependencies": {"mocha": "^3.5.3", "eslint": "^3.19.0", "webpack": "^1.15.0", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "npm-watch": "^0.1.9", "git-prepush-hook": "^1.0.2", "babel-plugin-lodash": "^3.3.2", "babel-preset-es2015": "^6.24.1", "copy-webpack-plugin": "^4.4.1", "eslint-plugin-react": "^6.10.3", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-jsx-a11y": "^3.0.2"}, "dist": {"shasum": "a27c52ea783d1398afd2087f547d7b9d2f43634d", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-4.2.2.tgz", "fileCount": 14, "integrity": "sha512-TUE1UGoTX2Cd42j3krGYqObZbOD+xF7u28WB7tfUordytSjbWTIjK/8V0amkBfTYN4/pB/GIDlJZZ657BGG19g==", "signatures": [{"sig": "MEYCIQCehRDBvnouNg/O7hxm1gAZgSLk9KMmXX1jJloHGjiqswIhALgabZ+LUTQkVOQGyLbxorLt0hUEHdECukJbFlSG04CY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZVNICRA9TVsSAnZWagAANEkP/0nRnh+GhriO3NYk7y9w\nOwqu+e3ny6iDKvWVZVnRM2BNpLqJGvUPZz/HAItaTAJHn58l0hq25Te6H/0N\nRkF9Hv0SJ/kt9Je9pjqXYfKGVrwWBZaUPb3izOjlMIZVLRx4Oaz1VggvUFEw\nuBaPcD6hO/sylF/tYXiyfeKiX3z1YNGP1byxhW3/866xPsc3TOiCRtGgY4zY\notFbc+Oj+N9aP4IMQC46MNJft0XF8npv2Adr2R2IMOQM+yGrrC6pUHEg3EnL\nFu9my+XXJATyiJyu3WYobIdrmXXuYV7VhTq8b7bvb0BmJhqjKtsXoafVRSI8\npzbeaBGsrgJlift96y+u4ci+t7lTdhX/+AGkZnz2tP43GISvyWD2mb3aVQir\nlUVo166EeynC2l/FETQJF4jaG9djHjoKo9RyPREDDUXgNGYLXxQYbHs0XSIZ\nO5eJ9j7qS4A+Lmu1K6Fw5y/SFHLLHhIm5DngoSJ6NYlFf2JcLUvx7KrI+APD\n4pW0z0xn46wMMu4X98zZgAehwcn80CQ0vx58KqvBMN6OylhTU1JwmFH9B5KO\nxQxVBei3fuKDMw6LeG6CVHI2M6vYomUUklW4SdsZrAmC9AuzfGCnXUoW8GGr\n0q3Xeo8gj0J5XGiFeFti5BMVB9Jez1kUvDY9bqDesazzpO1FXwG9Y5ICCtP+\nBHNE\r\n=8Hik\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0": {"name": "webpack-merge", "version": "5.0.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.5", "@types/webpack": "^4.41.18"}, "dist": {"shasum": "bde409c1597499c5f7bfa83ea77fe97c67b85a23", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.0.tgz", "fileCount": 16, "integrity": "sha512-ymeajxYJ9vpqFW/Ezysn0OOQ3OEk77O7V5SxKlIb9Rsw8p8gEdsoLtDlapDCYkpZ3IoB5EI5/uMaI3vjUE20Ow==", "signatures": [{"sig": "MEYCIQC6HQbnzBQ4uiqA54fGAcVYUyA3kIrOk2jDDpHqTllLnwIhALXtDDT7cHry8dO0Hqy0pDSQqTd4SPkhd7qCgg/v7TvQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/0pTCRA9TVsSAnZWagAAwaEQAJjK0Lv+SJxC/YwySAri\nj3EudA5OzbIIzeCyst26UBT/iBTKhwYliTnWqtwhDAMnxoudOBnW+DxvthU6\ncroPC5aujNe1N6Eh/Ub+Apvi1ZkbpUssQtHytWU6gEi8o9KvkNh+LEf3guSc\nJzVrLCnWXtmrrRL27T2Xp9hrlb2WJ78ZYh9QtdwFBD5nOy+oKajkgCMaUlan\nJ5kckQ4FcS/PyEBG+nX2LhYnJ/fSv3kn5Tng5aNjqWGXiV3QRxo3pmYhS4+I\n32MdZmpg5l84NrR/xh1B0PDd+8VAu9fbjPjk3+4au3dJzoUi0ZMczpP2s/8B\nQCBYaApj6VeP33dTx5VbYezG+PhH+uODE+tVG2P0QfoEpJDXPzsZ34CeWTZx\nXAXSfwFoUYqjBVthJk5/B5CcqirPmpSFAL6ZbVRtzmyBFO97Yzsv1bRGmGKS\nQFO1GNuEA8e0qHsPME8A9xHxY3QczdwE8sPncgn9zDfzHr8PbxMlWDMo+ejF\nJmKMTpKN+JSm6Jau2j+8qCHngsTRPJ6kwHh2TkSD81aqAe+tk3ZoD9DNhzfv\nNyaLoo/4FakEwaPmVi4jmdmPpHpcT3jx8A4v3fU2B2PPDw6TKoboUneT3Y86\n8YvKkPZNZsfOr+zM/grbKj/yZ1UXKSHl8QIHadpuLtBEzRs0xO6KCNAM67NG\n0BU9\r\n=5C/n\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.0-beta-1": {"name": "webpack-merge", "version": "5.0.0-beta-1", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.5", "@types/webpack": "^4.41.18"}, "dist": {"shasum": "a877215aa6e1d12835185ec84fe5c9a8f4123709", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.0-beta-1.tgz", "fileCount": 16, "integrity": "sha512-VlDWmwd+g7zI4Cp0hphVvMBTtgrsLzCg82IQ7vDtRmMwn/wJrxihViSZeUx/+3RIb5KgDLaFynP6kqY6MDeT/w==", "signatures": [{"sig": "MEQCIBsrfUEVzQgNW5BvLTI+I4VhS44U/FG5JBGvaEhMt3/KAiAgN3G9b9ZFs/VrdoN4SS8/LXiElJWSJsE6/KQP5crT6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/0t9CRA9TVsSAnZWagAAk5kP+gIvxehA9NEPqeq3znHs\nAcQa9vBkgv7Hv+y6+F9jtasVkUYWML5WF0AChDKaJw166yRrH2H8riIRmo+8\nNmtchG1cb4X9RRWaOgZmeU+/6MRWnTie3cT5j7onzvrsYUagoJ7N8dd22yCP\nYu0gRaAOevZdjeLhRgQvIM9TXOGLbtteXPGT1AF6aqpy2eKpfUFhf8Qha0bo\nIApimrNRHBYjGJ9BikiwliWwYMaABm6sc8zTVVJyqCEdkhy569hMijUF7FO0\n5THzVntw6cRulex0EzE8QmkRHuLWFKZovzewD89fmAFm17sUWK11QsH5U0oB\nco2QPDcdB6egxdstjCIL65AWKypjINzrMo7n2EohF1KMWpAuS3gd4nHaRwfy\nMSBO8aCeIMx26lekbRsL45pU8fhcICWm4AQ8aLxI+chGyN6jud36VEY5/KBE\nJmuyZkwvmFjvPSqHQCgY3mKDZyGu2G11fuDwUciTJyz5l0XwgQIdtLQ1UShr\nuuLYNhjZYNPFM6XwZ3hALw/ymCeSzUeXEePZ2h+qpiLyn71MRX2iBHCvymzr\nvbn4AcVgVUQkfblv7OlvxaTcR7IA1Nn8aoLOB2cFVSImAIm/8muJA/8/3Vyg\nK+t0YffSfLhXsaWbfZgMoUk46uMlQgxlNKRHko/EMc/qzvAT0dsC9edn56XV\nh0r0\r\n=dBru\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.3": {"name": "webpack-merge", "version": "5.0.3", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "d3bba03cc11bf43719bd058342842b16528f6779", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.3.tgz", "fileCount": 16, "integrity": "sha512-DDbqWyHYBUxoxjysYRnfP30puLciPTZ0BQ3v3/l2OrafinwKurCQhzKUa3L7u8w469tcVWDxAbqQM13KXeij8g==", "signatures": [{"sig": "MEYCIQCJOMPsDzp3wWuJ2M0Hfe6MLzwCzShQ3Ytsmshbs6DMuwIhAKZpJwW6ShjQygBsF8qJ0Ddx5b3Tr8UlJtltuMrn88xn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAyeWCRA9TVsSAnZWagAAl0cP/j+zEK3xKPv6n0XsyPWS\npX16wZoQQc0COrLZhqqlOdFw29ES2mwyaUt9ll92zouzsh93k0k7jVnJC4Qc\nIBiO2VMlWAb0blSxaaukaAIaRGxkPZwpotfBbDwyFgozNwExtAUV13gE63rz\n1jyl3kfO0Rvvs1dfBo3bT1HOwQKq+ETtqGqfpOWPHM5uD+PAtvVcJqWVqs7o\nE4Ol9hXkVv0rHkZ1BUWoFMqsTMRw61a3a5/vZH0WAxE1fuHPJuFx9ugideXi\n4Zj+qYaDzyIHF6ZZPxXI6oA06zEmC7QEcD5bd0L1xqtDfV10vM6KPbuyfmS5\nmfH76lHi6lFY4zV2+OE2TfM7dYZPJPWfBBZOIyfpFPXS2JAsga3Rhbq8qCqY\n6pajBqldhoST4StL1THozms7cLW1U95vEHKoWnHvF8TZZYEl+cZj7kIQxeXK\ntPX1mF1+4Imh0sERSxPDpGdb+h8lgBBPYgM2a5USe5xY47REk7OAwgDPoJNS\nl+Kzwwz+3UQbeLWwzZFst9+w18/5jRknR7ah6pu6EvbrJlVnjqfaMRJ7/Ztf\noqWKlARlh3npIlAjpDV/jhxDMLusQK8YDPQkKWEMVG22X9bTSfhor4P94lzs\ndh+azaUIawthrIdvqDXMAPLk8d4Htoqskl89Qw4+F1fq2tjCdTK7H0u2D6RG\nZKAK\r\n=KeKu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.4": {"name": "webpack-merge", "version": "5.0.4", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "70338267ec8a6e713d2c80ef04b5ed4e33ac948c", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.4.tgz", "fileCount": 16, "integrity": "sha512-lOb9MEWoPBZ611bRECti/LXwLN8oWwfjoKsPueiN8wbmFR+VQXlWd53mFRyUoXSLY9705eSyUtzlftV7tI0iiQ==", "signatures": [{"sig": "MEQCIG5YW5kmtfCd+BtEshx8DKVsFoQi/cqS8800TRMAem6VAiAfYVyOlEEbT7fa22L0j1toZETRhnD4ewBq8rHzbVrZGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA03JCRA9TVsSAnZWagAABdkQAIubW4B1u4Bu2g4DlXZZ\nGgRHXTQ7mLrue6QNn/wmRf5i7AM6ur/ENZS4QddgE0gFkZHQr6CEzMXKpreB\nwEJ9KwjZOGB+cfDNGiEUS76kyCrRBZynSVzXroE97V9zsKNgc8s+7HHAdg+R\nbraCXUotNghP2/uR/2X0hGCF1epgmFvZrJyZ3fFzRKB4Wox3ijgVY3MIU+xV\n83cfvatgsbkxKWgHRf+4kFWWAip6e/bRDQgqzg3eQWSJH91qoyuYnIBlLVKR\npMGkCPdtsCu48ocwCopYuTlScDrnDrpEnM8c5TbYYXzQhv74C+fM1TD7IQP/\nJ2UT/W4EppDWYyoQsabSD0QYn82fo0YfzczhrsdY9BBjYl3UV2Tt1W1fxAHj\n262nPcjGGhdXdH6q6/MGLRYSl/xcPr8MpzEUkW58byxjQNJmI59ni/E+ORjN\nuMkbDpBQlR5Hpw7mpkpofMDG2tzZ+gw+/KQI6LTUvsvpoS7FAH1jU0YInsyx\nF43dcERnPckCfD2AA0wIdv+DDitjs1a8WSls/ublZgg7Px7LqodsLzAvoZ+j\nLlnKvJKYvtmMB+fbX3oIT0pLAVHsba+++/k8PFIt4Sp39LG7jR2FSL+uEjpw\n/UpOF9PYnyXmQ/NtVa+++1Gm3FH1oAqSFrG08S5sQzydMp3Jx7N8FYVdU+cK\noj09\r\n=4NFu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.5": {"name": "webpack-merge", "version": "5.0.5", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "4f7a80f5cf6d2bd7f0002cdfd414b93f9dfc089a", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.5.tgz", "fileCount": 19, "integrity": "sha512-UEnSShCa2O4/bZ4bc0j0UdxKdvFgg59fOSEgknEowaW+QCafyyLTAfcrhTOBml54cgjhPykt/ONDrHPyyxPB7A==", "signatures": [{"sig": "MEUCIQDkP2SmQPV58E/P+O8zo3fBgOzPJ43nzgtVbm4mE82u+wIgRqWkdPnKfYoWjLJgAa+Wwx9voicefzXyF8umAQYcZ9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA1SrCRA9TVsSAnZWagAAn2MP/3LIILPvYEQnYmZWLZmj\nvMazo4nwxLKVgjWscZKR0PbQ/mW2BT9zR1P0QvsIPH7KtHonk+j3lWN6/5KZ\nvNkpnHFlQaDNLV6OOd2c7EieLWUOkgTFk3qbh1Cu8biMMaCyfIcEfGgLQmQj\nnizqwLiwKv+6ztourLyoV8IY3sW0hzPITPUEfTos8BJWIcpj99q9gq/+Yaf+\no0otPFEOD+rw8ooG/ok6zhS+Y1GhFlyWDiZRBcdkQi08FFh0MgrmTFYY9n0e\nDHkx9sOKEY5QZg+gLeFEvwxMoWiOmsQzSAcvrvC1w+bPAOuKmHWhvGyIz8ZO\nsORZfgQPvsJ071HRXC10N48tKoH+R1psXa3pvQ/rcFPOm2KbPP78/Y/m4n4x\nKe25jRqmkhgeHU+j2wR9aJoD/oLw5QSzH6+y9yIjssxxx0hNaJb5IZMn8Hj6\nCG5T/SFuHDnSVbXoE7tmeDf00oRrwOLAeTkn1CDj3yhx5LE16x+3L/RHDE5S\n4sisj71UIx1G5Xdi+hP+OqOeev7DEJCg4LYD1I63hSKhjeYfRfLuRr8klqSb\njaXUogjoOcQ5kW8ei7UzyXxzkD+GD151kVmD2nQfd4cIzuHD6E7KkAQqYfgN\nBPI8nxr0qEz2PhR+5X4f+vhB0epe6b9t6tze7aF7EI2MHl4ewveZx3SaKkhg\nRmCw\r\n=54S1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.6": {"name": "webpack-merge", "version": "5.0.6", "dependencies": {"tslib": "^2.0.0", "wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "f4843a8339059cf714983045d246c7e4fc83a0b2", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.6.tgz", "fileCount": 19, "integrity": "sha512-RkHtvANezZXAgFX2Qu616nakTW6Cv1aSk9A3LVG96k6pUa3j0ZXBvDRWMClTDa6OKqnxnhrM13Q+UZrBhAVaTw==", "signatures": [{"sig": "MEQCICPUEU3SR/+YHx/rEoMm7UuIOIks8/YR9/107Zewwbz8AiAsJeq3jierNAXW3Qqbcs3I721itZvPBbLXGMInh89+Vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA1WTCRA9TVsSAnZWagAA52kP/3ZpydrkuepP/4DIKVoP\nwpHt6Ycrs66gvEBL+fJQwg+fcPvkuM+CkpEhOVhdXetTlT3kDDNUJrJV4+sK\nhaW8Ed45r2JDAyp5xdF+Vb/OyHQolu1rW+clobpOb5fXWxbjg7Pnng3f1nSr\nOiN3qQGDB9+kv3QTAMKsCLFwHqQHz6onklhglmYn84pJgjoKil9IA8LTQuM8\nWw75PlcM4C+eIi6Fk9ueX36RBrZ68kQCfayOzp0pFajfHz+sY/+OMHIX+FgE\n8dgMSo69hqS8weTeCSyCKWYL6GW+aSxedFLsu2XW4qMu9uSvkvrt9AzZH2d/\nA1ov2ELWgMBe4t8STbnhpRcMRIQihNb5Gj9MPtIfR1xtHiz64Z2HoifmgPws\nIke7BEFL2x2gqVlondV+O9g6pHefZ7jEp/TTclytZfUfA2pAvFaTEq3QNCZ8\nXX10SuNetb2SwR8V7VbP4sKYtWStca5fo3+SwmUUBd5ocnac9iACqhSJXrFM\nO2Qf72iFjtiw/Ce2CcSQueExLOh91MRYBGamv2I0QmQ8tvGh0HWJj78AzxcW\n/f9EupxdAniow0U0mPaAl0QRomsr+pfeQZGlSnkXIzG7LiTjawpSNpeqeZ4N\nb0xhwle9vJZB7mb+kHTd9343Sl9lWYl1B4N+QAt0bSU6WLnRlsVgabYaUbUW\nx0p3\r\n=fHGf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.7": {"name": "webpack-merge", "version": "5.0.7", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "78be2f360ac1897f41a4b3288e1d83319e4f770e", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.7.tgz", "fileCount": 19, "integrity": "sha512-IxTERB8mNelEUN7YLSBhKMA0t/AySs0GGcUczNWWZTsNMKEFst4MM1t4aHX4ANAqVHO02A2zPPVXcdfv5Qpslw==", "signatures": [{"sig": "MEQCICYGbabp/qordxTDexijmUNFheGn9ZylOw9I/KBpJtPOAiAczIBq2cA5APfxq8Ziv82TapNXvilfz1A03ZJVO/YlVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA1ZQCRA9TVsSAnZWagAAlY0P/0exOwCr5Uefoj+K9Hs/\nsxfZdWdAHw6DlpUw4r633H1zXllE2B1t85kjV+8276A5JwVbWyt5yjdjvJw+\nYOkO4wvpczi/+sEzOOISHhmDUOyg3/1HuBL4lOo3D9Ak5OmQcKzHh5udzhCc\npPpyvryxZn/sLMK8uIU2bYh/Fq36JpBBkvwOknDVdZ88Pfw8r2fc/lVvSm06\n9aATAbkhMTBa5tcRHd0FbFCVuWcUQVNuqlXdn/skAiYgqdZO2IoYvOidMyCd\n3fWMlvNZH5hx8V7r2u36Gf2qBGa6GkoiQAIvT8IlwmjbZbaQ3I+y67mWoJqK\nGqeSzhRL3gD85urHL68WpYIhyCq6Z2hAGjTW3iQdNo2IO1tmXc+fp3Ivt/eC\nbURcDnlKBVOAe/czkYeDrM2MUQ8VqcxpMeBaUqiaomV5TMYMF+MlJMktrO/p\nKg4mGNqercMRRRAPDitPC49eNC5q2TdY5oMpKFmwme8FagfoppGOYpUSB5Uh\nhGnXSd5scxkQnX3JolmA7rNVsjx9XAw1j+JDXpkE4fNATjxADXtXKnj2z9Oo\n5jhonzQiU064t70g0P2wD2hs7BlhFqfi606Quobv10qTJsHUBimrN/QACPZo\n5gBMnERwNNn2NxTEB8hbqF5mHlQWSnaL8J7FUeC/Y65v8LJ2WH83B/7NZvgd\npAKw\r\n=Dx06\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "5.0.8": {"name": "webpack-merge", "version": "5.0.8", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "fe8590c58582e884bde52f87a1f5586a5bc65930", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.8.tgz", "fileCount": 19, "integrity": "sha512-jyPPojSgC42YnepNlK7oxpVfvfyW+xZQ3O6+K9BZDC67+9eDEAtQSv+bFAQn2Q49QSrfe42MdFs2ABGXJG6tTA==", "signatures": [{"sig": "MEUCIC2yXL61WJ1AKLMTmxwaDHxk8y4KLSgWb9H4b+meR6b2AiEAu13QuYMGu2mj9VPUTSgosugtzCmI+MQL1UbIGNeXaVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBCeOCRA9TVsSAnZWagAAd7QP/02bTAlgs1c/AXMSRpMt\nw9nUGm7sdcoXMTeS8WMaqEzg80R+5kYN+AKoAGORvurwm1FwMsdJn7mUzT58\n+f6tC3LvWZwbUVLWKnW8VhK30DW0piPfP+EZRv6eygKr6kiIw8ekcU3UwNIn\nq5aa8wEKQ3oFqXWmuhzEI0pkPRj/VwER+vTxbk4eLPTTKpGyMNTXoQQnjRzB\neckpHUgyMhNsg1ptCLKhXbrGlDZ4OiuEttXhmdeSY+R+X9cv0MnlIfjPtEGQ\nSCQv+P6unKW66j4d3z+9MpwPcIwGa2uKun7NWeABEWB9woUCMxJMPAPt0h9Q\nPXB0T6gSbFd6GpahFLCGHxC56yaVNuDQgRRVH1NKxWeoWT769e9DIyAuWrzq\nHqKLDqFxuHyNbO1uyj96wX11ZRXYKUhejsq5WL0A6O3GoWkNXW7cROUAvv3A\nU3UD2YmsCyZg+flGthTNR2Y47weh9jzNNz8UW3BPVCxG8JRL8ZQXxSGUPFYE\nXxL92w6YWqjqsZmKpn5bNkr59mIGAVTlM7GPw4iiVQbXj2RBR2SC2TpeuR7S\nMOnxWzjsN6bYbuAV0VheGqSI9KcK3puXaPRHFv79QEzk24k1KqCZpldQ5GLY\n0PZCUUW1tAz+LnE7jGvW+BjMAcOOo00AI5whFv3VyLA2KIFwLuJBExPd9P6z\n/V61\r\n=/s5S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.0.9": {"name": "webpack-merge", "version": "5.0.9", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "d5e0e0ae564ae704836d747893bdd2741544bf31", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.0.9.tgz", "fileCount": 19, "integrity": "sha512-P4teh6O26xIDPugOGX61wPxaeP918QOMjmzhu54zTVcLtOS28ffPWtnv+ilt3wscwBUCL2WNMnh97XkrKqt9Fw==", "signatures": [{"sig": "MEUCIQD340DAHSe2FvQ8RBZT6RlHUqJlBdUKs7/SGxvM3YeN/QIgGD1ij0iIVbLdXGHH1X5uMq9j77g+EdI6Auf+ZmzRxwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBwjxCRA9TVsSAnZWagAA/oEP/0YCrfpiaPZ0wmXkBGCM\nOFw1bALdM1B9kuMvL6EkNc+pxCzLSxsgGH4c4EavNPYi1prJNkDfgnokxr+D\nGkIX0WPEamtuuTrYN3XlIZ9zUVysepqqsaZPDGs9hZhLsOthghqegh/DZsh8\nYXBJPA44TvbMkpMYLfNHt5pXKkmABUeqc1wm09lKzDGXF34qmWMNIkWc9OO8\naJzmBbTGIIuUhuEhq0gZvGPkIq7W2Qv/QgmW5/Ypfz39PLgzY+VuiwuWjx37\n9PvSwDmfwy5M84/bzRiNcTcyNEpSLUZrI3bhMyQheJP6hmGfXNixJa57jZl7\nk0Ph0Qz8094YMvJwOG182jAHCG3GxDqgKpK/CoryDmHwuxV+ae2tBsB/nBdh\ni37OL24lLlCkrPNPE+HJfY8xgfUWEFAdyi4zLXTAvv3hqFcPA0p4sT1WSweI\nLdfxLFyoAUDky6oCVbMNVAs5hXtFPMvLYxbezCrGs/EE0ASH58QVobt4QiIW\nZnTwc0/skm8yIQLD38xNNPRzMcj3/1bHHe6Wc72yQulF/HmjBZ7X2e0bbqWL\nSRZ2COYi5y9XkAYbQcYvnRl3l2aW9Lu1G3xfdHNkkoAsJ++lW6CUMY2Ytnh0\nxgMKzqXd2RUL8DC7gNC9ldl71sJXqev2WhVIJtOxfAh0kpPN4yjEAr1d2g+n\nwBEA\r\n=f63g\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.1.0": {"name": "webpack-merge", "version": "5.1.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.43.0", "typescript": "^3.9.6", "@types/webpack": "^4.41.19"}, "dist": {"shasum": "5a24b2d5fa0431db96862116a7b7f18c4cd7bbd4", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.1.0.tgz", "fileCount": 19, "integrity": "sha512-NNEXYSV/ixLwICcAhlVQ8c8E8dHmOcVnubfhoFdL6Tpbq6N9ZUvotQBYPwTsyXepXmCm34KjXgdC4PCQwJBp/w==", "signatures": [{"sig": "MEYCIQC08wsyh9ggQuEcr9P9s4m2J+qoAlooB4YeQxLb47w6lAIhAPQ//FVG8LNfG931tS/fa1lzt0GELXOQHpCh6/dDpNAc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJ67HCRA9TVsSAnZWagAA96EQAI26C3y07N9sMCedM/nV\nO8r47ayUjdsA4EJqQyGZ1Ofgvrmbh1xPFJfBoPIq49NNAxqrJT4q0c2IkNbJ\n9wl+JK/ltbCPWu4cnQJ6cG1Rv7UHLRVwBvKW/UlmS+wLziSCI1hSzlslJV24\njeo6+vCu7g8unK8s+y3uZ4x3QmGSZuoQFXsGrfShN9GwPSwHlN5PU7a/1/M6\ndraD1muV0ndkg4M4fX0FKDfUSkHdnezT4gEG4VEUFJUoMGndT94N+pHqCwWP\nqMat/pwInOAWvrJa5MmVqiM37pvyp4nF3fq6LivG+tdibs5BewVm4zLsFvMO\nOviwZQIcauJQ2bGQCFrFY8nRXzHo4/XGXfh9JRKqgq1pRCXlKqluVM8b52Ch\nPxjlzzEBEOnPQiw/kYFpA4XP+yqZ2/znnqD0g39I14lkyoBXFgJpSftVfmYo\nEENzgZwT0mRQW1sSMzWAfDbLuy59wwkPAd3mutXbOL1Z9aNMzc5y1Ryt6qGK\nUe5Q4f9PAoHOPYqShQQux2MkbwrtC3waERer3wFaIcN1vDOiVaNRX4NkXjD5\na61eJ6rXTmGJoc3lJwxPrHMLAFXaALkVPGyY3NaoT1EobEI3Pb5y1pFASd3p\nBVEKg95ECYuZ/ulVJ4Jx5VtQmSlhK6XgkrhJCi6ALTiBSs4iRrBFiwcWGlDI\nIDAu\r\n=fN+d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.1.1": {"name": "webpack-merge", "version": "5.1.1", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.44.1", "typescript": "^3.9.7", "@types/webpack": "^4.41.21"}, "dist": {"shasum": "c79f36b4ad39a597c3bb780f809b514d65d85d01", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.1.1.tgz", "fileCount": 19, "integrity": "sha512-UhIkHAVqeG9EqFfYo7dRELrVfH6HYaOTYM7ssKCwfIIHYnWepGVOFp1E166GwgPGFqV6M68UgRiKOERjVOKIXA==", "signatures": [{"sig": "MEUCIQCAEC2sPGJlmH98pluR2RM/TglEh3StdkhO32H6u6jxEgIgOels0D5He45O12S1uZxaTSpZzHpMF+k7Nj4HtIE2+Mw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKQjoCRA9TVsSAnZWagAAgpgP/3PDosIXvlFFSHbNU+EQ\n81k146oAEARB+zdNuRwoa6xVUNchWe9O0VW1wHD3SyFkJOSeByKVwzVLDldh\n+LQf2WJSkh5eNBU045Y8TmPxztVu0dUH70lxRdazga1JVNHH5JLGwLTtMUFj\nfXnDiAA5akxwwHo+t8XO0dJ7hnhtOHc1UrXSl+8St/n7060XNsFS3oS2S2ie\naA46qCmmfsbQ9qTuzAIkD0g8W8BNo22eq9NBAh/9US+BBYKeAf3NZfHtMK7x\n8deLipwhHAi2ghr14tCVfCXlaZWLnmaEDe6tEOBX+ErxJcFHMGuALEnuhrRr\nOgdAAzYh8rN4v1oLbQdrW2xTqrROUl0Ld5Wf530ZoCnYO8KkdWKwAbcWFqVO\n7noSD0tA2M8iTAzV+p5GMXDEfwrOy9Dji1o/rrZLKxdMUgzbKJmOnG8HLpH/\nEQJw5Jyxpr3q9HdJKg2Nz1HnmppmcdjrlJr9BeZfgY4O391+vGxn/FcuvbZB\nLgCOVGaLQtFKgayXJD3OrDwWBs9344wKrZNuNGOGaGcx0R+bVCnj0a5A7d59\n+s3sFjNOz+c+auPce3ZLmi7EPsg6D/9YlGFU1tnSgd7JnqaPS8Gai0V4kSeP\nLpwQoy17TpIdmRofj1Pp+T1JhFU4XHB4IJc3NYRZ1egkOjyv3tiIAShh9OQC\nfEld\r\n=0Z5M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.1.2": {"name": "webpack-merge", "version": "5.1.2", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.44.1", "typescript": "^3.9.7", "@types/webpack": "^4.41.21"}, "dist": {"shasum": "21dc1ed0e860c305cf5a07a48750f53a99750855", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.1.2.tgz", "fileCount": 19, "integrity": "sha512-/slG0Kh0OKTf0zxdFJlhQHzv8bU9gUYVK5DkBjB3i/yoc1Xx4ADG0KITGO5S/6cqn2Ug43+8VR6Sz8daA/c+5g==", "signatures": [{"sig": "MEYCIQDRhR6l8STbRFAhvrKQoLBLAIspDMxevp0GIcI0kaKWhAIhAJoMZTc7LbS0LRX5Rr/WR6FRYeCEoncvtCDGZ2J1mJIw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPAP6CRA9TVsSAnZWagAA0soQAI9EnxM7r3xazt8q6zDt\n1rS2t8C5jmExINGJQT45LWlUf3LF6FFusliRmtZV/exvZ06yhplop6sgD0Gg\nVuvO8BoZb7EbPy7Ro0xhYe8+Q3agu43VNb5zBzXDX2AborDl7AVC/ByOHEht\nnavKSe9PRVVUCMR1WyOGJ25HMuCrVaGL3lsFQSDZgQYRDkhTq3OWzESz3CkX\nlyUI1Q8NB/NdhovUMKHy3hbsfY5pYZ5r2kAJ4TmQ5P67fGg7TzmLm5dGdmPC\n397GZSgDWULHQo/C7qYxzwGkB9Ws5e7fKtw9y0zk6MKGQcc53l8yQ5SWV16O\nvU1snr16jH3JNb6qPnRWyKQs40N4gl8KQD2VfhLsER+feQguc+/hxNE3KVS3\nJtcaiiPrEzKhx2Jzt22WwVbYvW8eNOFa4l08HRbZxQT2ANPaWFNcMcb3H6GR\nCdFwnJ6K7NfelGaB+YIpKwB3fRWaKE10OS/KmbzrJ7qqGA44uG9TLYxH8xoX\nTPgBsgE1cU5HXZKAvx9w1KnX2oJWk8hb7AeowZNwwitZScJ/aLtjfRe82mav\nI1rygamBuY/NmgG/mi3skCCC4DcZ+w95Lax5tHwWC7LXp9ntiGbaKvJV1F0w\ni08STbKkXCh11SeNH3YNhHeZHpqswaNeB5nLGE8V02LUq5FXsaoPyaw86tap\nwV6a\r\n=btyu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.1.3": {"name": "webpack-merge", "version": "5.1.3", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.44.1", "typescript": "^3.9.7", "@types/webpack": "^4.41.21"}, "dist": {"shasum": "e5570b0bfa654915340f1e348c93a6fd16d7a820", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.1.3.tgz", "fileCount": 19, "integrity": "sha512-fz/xHgfHyxq3uzGGrMryPnpPZ6x3vF1tHtws6vYwYX+8e6Dw+4U4r6rXuEPCqtSwmUIeD8hniWwFem+5FVLjzg==", "signatures": [{"sig": "MEUCIHyQrXcNpNjts9AfXuvfdMe3H6CKVY0iQ3SR3tyJ+5wjAiEAs9wt/Zf4LAFTdYYqtUkLmy3xBO6wJNO+eq0r7L8g3k4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfS0XvCRA9TVsSAnZWagAAeQgP/RExdEBspgHEpyXqWD1G\nDl4oycuCvmXqiW34R+DBrpsRjEY3H+ewy1qgsmiswvgJkJjsGsnjqOoJsxFF\nj7lPmbQM1Wp2teBnQLh1F4zMM7BNfgqACDisQ5Iw59cn0fB1Pocgzm9RmTM1\nUUYQgvRhyUext/cbVmLSe43ZpFnXXE9ib2D/OCN9GNhNmRgqkXi+sN657Qmj\nehl6Lt6DLz70OjwSTDO6ynQmXLtWjYcJEyCudo22T55mKtWbCB8l6ks2WuEO\nIQ7qNjBEFO7uh8UyPtG7K+D4nEEhKI9TLxTmAzWDMFzvhQ0pnrzGom7EARAb\nrm02sh83vJ5dzlHuF+aBTC01EltKO+EgGFDXmM1TpiEjzrkZtZAnNP6Nqnhh\nCp52SIQHy/ftndLOGSQzyuBBuXzSgmi7TzM/JEJZPqdHPUf4yeEuPM+Z3Tul\nuTbMAayPrxvd7I/G9fa/ZmBuFj2feeEaX7eE6WrT92ooIuFju8KaPxVPBw7k\ngT2StrMGxVmYlSt043pldfHJpalGr8tYwfkQQVH0HaDVzJCyATYOcj/3qYPe\nPP1wpb7wW3uVW47VUm8OTPGvD3ao2QfSyFwCytr5bDHYO31mSup8D++P3Ues\n1T+Iz0UBXsvDTjkmdD5n2e19LDW8L7Zskx78xTuZuXD8EMPBuPrRJVpotTgU\nuDP4\r\n=hXsC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.1.4": {"name": "webpack-merge", "version": "5.1.4", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.13.2", "husky": "^4.2.5", "tslib": "^2.0.0", "webpack": "^4.44.1", "typescript": "^3.9.7", "@types/webpack": "^4.41.21"}, "dist": {"shasum": "a2c3a0c38ac2c02055c47bb1d42de1f072f1aea4", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.1.4.tgz", "fileCount": 19, "integrity": "sha512-LSmRD59mxREGkCBm9PCW3AaV4doDqxykGlx1NvioEE0FgkT2GQI54Wyvg39ptkiq2T11eRVoV39udNPsQvK+QQ==", "signatures": [{"sig": "MEYCIQDQM5+AggeoFItucRw/ST1iK4klUODbOgRRAj8VoBayhAIhAMOV2DJq0RxIpmT8uWLA/zL7ESBIU3as4YR+pEA5f5i4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWIM8CRA9TVsSAnZWagAAgo4P/1+P+Nvw0ck98ZF82cl2\nKc5Q+rV8uIbwqCTeWZX/BAV5WGXiMzmS66xIgymfCgpamqWIWCdgUalkpuyo\no2EmPL3AK+KihhluT1/kAauBTkL3UYodtUcNZKFSLlubk03LIPW7C4GtLsVM\nF7rf7+Fap1NnZneOm+dB1VbH1X+kxq+mnImwuh9zfF1nJOYTDscqxSe1KBAN\nebYx2gu+KsL+1+H2WcPbVhTBlpq/3NIt7o216TyseOiWPfrLTKwbb325FPZi\neGafLQ/Mi24iPpsOzT28t+BIgL5A3umY7ubLPyCnRGNpbtBx6UdxfXVP08Gs\n/Xi05KjNriv/WrSjkkh9WJ/+PSqoL8mds9FE6x5yHuMsxW6UIr6B8RFWKJPI\n21QBb6fXPHB1okOCg42+xm3DjYDiX8AefbxQJZAj9wUZoO+LCeuEf7Nq21+r\nKn7Jm+VcrSS4GPylaVSCna1ZEzqJwzrb5X4b5RaN1x0ys+r1cDdTne2294HP\n6TxsXoljMaQKhda0kEBLl/W+jdslL05etve4DLQPiviASkzYLMPvYcsch0RV\nLOtm2Ip7fw3YwPsdzzqEp+TfLxuOIDC+wfU1h4+owvrLDM3kyxBdQhwFsoYL\nL2J5/vnOocaOUPidBdwUGEepch6KcHBOMteVpcMDWpi9sWzFaSTcDVLSZOVe\njgXb\r\n=3bgE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.2.0": {"name": "webpack-merge", "version": "5.2.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.0", "husky": "^4.3.0", "tslib": "^2.0.1", "webpack": "^4.44.2", "typescript": "^4.0.3", "@types/webpack": "^4.41.22"}, "dist": {"shasum": "31cbcc954f8f89cd4b06ca8d97a38549f7f3f0c9", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.2.0.tgz", "fileCount": 22, "integrity": "sha512-QBglJBg5+lItm3/Lopv8KDDK01+hjdg2azEwi/4vKJ8ZmGPdtJsTpjtNNOW3a4WiqzXdCATtTudOZJngE7RKkA==", "signatures": [{"sig": "MEYCIQCP0WJzFDjWVk4XKlvNCu2qy9rkqYn9rHaGt8tF9ptS+AIhAJIYUZTzMbFp0l+f3j6zAY0bglImBtMvITSwV2YmbJv2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffYqECRA9TVsSAnZWagAAM+YP/jh2NUUtr5AvApc+MN1k\nuiOPIf3tENhoo6ROUlSIltuIFG631hV5QNlGNXszQrqi5bs+56w2tJ9YW0YG\nd0rRpu5zmnRWYefLOy3/iKsAAYkfwCqWRwlJqSeq9hgEtLsgvTZVCNM88r4f\nl7QYpi1ybHU5I7fOT+24mnzyvp406F9KWocOu6G3W8XMboKw5qQtqvbroHit\n3MsGUhBPlkeCXoA9C2v8YuPivXG6vIo2D1jCoXzErWVrj31ECZu3IcvJFbJ3\nruuZB7ttr2O5YR4Jmw+6QGwfa+7xi8MhW0xGFWonxi5lEYgk1jq6HQrWtQom\nCldrrmVIaXThnwsZ6qnXIDDbAzxmTEMrIlppBEg2UIt4MsZNbcl0Q60oMccq\nRLFm/xHaCZIx6npubCTaB4ibELABfP7j7JCWv9f/+YlBHRKAu1IjzzCWz3BR\nsprU7oTGahGMgIkEUmIB8Kb4yBQ8QnQ5HCni4mAwuwknmTLGHINYSYQ6pcym\nw1hoYMhbx8vzflNqby/+6IroWmi/t6lHZ3wMGOSh8zxzFd/y102yFMHg7QBw\nZrXw5IhM4nle0BTfT1IHHpTHhbWgzescHkZymoOvNiahzBckppLJxY4Dag6F\nLgHCWfvZzB/0QgfPypxx3P1Ume90HuljcI6aou6pajFzo/Vem0T8+ORgV0qs\nK//p\r\n=1LB9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.3.0": {"name": "webpack-merge", "version": "5.3.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "a80df44d35fabace680bf430a19fda9ec49ed8eb", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.3.0.tgz", "fileCount": 22, "integrity": "sha512-4PtsBAWnmJULIJYviiPq4BxwAykbAgGMheyEVaemj2bJI54h+p/gnlbXZEH2EM0IYC3blOE1Qm6kzKlc06N1UQ==", "signatures": [{"sig": "MEUCIGMOKC58PYapfnT4QaQIeLtWbXeWphXA1oHsiNn3AHFpAiEA2wWj45g239OLBFbcz6dtP2OhzLTIhC1zaYgCRh8nG7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnE81CRA9TVsSAnZWagAAZEUP/27QCjuLFf4FIbK5hBaB\n/2eu2fUXf5VgpPk3SXLaQIO6VWd7UQYJyucdcR1XqAgHYHdlYoaVR+UoNUXC\nrOc+dEjLghjL/M/t4iw6baLgFDKQiyshWxTvJ94kfUkcDHfUcOns/AOKJvFA\nZNnbdq2HEKNmikSrvvSRqau8OtjWoKPGQ7rmGwQNmwT4w4nnQuwCbkQ6iw95\naArdYXMfZEFJWTlAvFj2tdl1PvetKc8ajvsYfeUMTPx+BcZOigNayNnMc7Q/\n5haMxsR7FlVJmCZXg3igWCFSKeTlVJMwm/RFlFgpkK0jTp2nI5Mxinp87xa6\ngDqzC/FcuGVfCwBHlvEQ5EGFK7qt76xPMbNy7EuvrVXVQ64lze216ryKpILU\nlqT1Pa6YKnoTn3PmOqVbql1JoGXgpcPeyh8bs7HnaPp0voN3K4Y4syTWVt/k\nF+OC+gxEutjYTYx5QawCi8i/X3grr3QdeLzm6C2hFKhYHZSTDY/T1jLY8zLJ\nnrfHIHCn900SP0wirrzaHD5gsiFwYBCTsZ5iCAd7eyKUdSr3B/+VRqORoqyR\niTZpGe6l6mnc7D6jVmWka0dBUqwgU1VYopJVbo7Hccz6XVgtztobKgKKpkpT\nf0UzZvyv/n1Is3Z3Wva9A7v9fC14tsLaizIfc3DDjU67mmXqOK7b+r1zViqD\nquaK\r\n=NC06\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.4.0": {"name": "webpack-merge", "version": "5.4.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "81bef0a7d23fc1e6c24b06ad8bf22ddeb533a3a3", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.4.0.tgz", "fileCount": 22, "integrity": "sha512-/scBgu8LVPlHDgqH95Aw1xS+L+PHrpHKOwYVGFaNOQl4Q4wwwWDarwB1WdZAbLQ24SKhY3Awe7VZGYAdp+N+gQ==", "signatures": [{"sig": "MEUCIB+KSO8lu9YUYPCmsBMg+ZlDOtu+/o342az1EhD1BTllAiEAjgTzhRfc5XBOVtUOErUHxXt6VF0cq9GR7Pzc2s3lZq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrQanCRA9TVsSAnZWagAAgNEQAKFQYz2uaWzhoQnIyiRM\nN7GIv9Sk0dbZ3f7LhE3vrd/gLJTbtr2Svf9V9WEdw9eFxbS3yqyKX9EUVpDk\nyJB53exSCMb8BO09QUJqZqNLJQ4N4tx5pwW2jMOQx7HtsRnrIUpFBCHPzbm+\nyG7DaQlUnq6PJ4Ll6x7YZk6hlACp33h3ahjQGKDjOVCloZMuvlfvwbGU1wjL\n1bgf4M1lw9GXrnxbtrkjxq9IuNtBNwjOm/9oDjGHCaCUCCcQFRHPPCXrduJe\nRx+Zk1tOE7RdXh2iIcJfMoNWYkEfcG+m2CdmyJ3s1aQPlGtyGKmOoNTHGzOs\nEU7KuKOR5mvMUKjKeiwdbIMpMd64pCsS15StMGUmzuQDld57iLPh3jTQmgz3\nj/N5EyWoqiDXNEH8iz/2ypdfVglyGNijo/SCcNi4VIMHAYfiMtsJLRiZNrsX\nulmqkQXKs/aCwWfn1gMWsHReNkAULix6WcM174DjUMPdjUIg19JWjmUr2UOS\nZhwpLZAs9ftYdSU4wXpiPSconxX+gDoyirCbtGb5RpiurQjyveoFbfJkrqOo\ntJL4/cCj8USAk4LikyhQfSpZ/mTvVW1bCNEtqfuAMUn81/xUSN0ZWZya1AMu\n0J3wn37VQepUs3rfFm1JanKSxER53AR8yJFupcZhqOsaBQYmaxgybZUDzA8Y\npI9S\r\n=QEvI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.4.1": {"name": "webpack-merge", "version": "5.4.1", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "1cfd7386b1876b69c17bbf72abc4325604decefc", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.4.1.tgz", "fileCount": 22, "integrity": "sha512-ubwNFcKJjm5BwPH1U8ZHTMaq2+XJqyvcfWXXU6yv4IIWWPWAFvgicok8VK1OiA7iYhl33aJxL5hvwTuZuNBIHw==", "signatures": [{"sig": "MEQCIFepFMM1X2ST9h4pQ5yqBrRbG4c8+KLYxEyPo+fGyyM3AiBQAW8tkAwiWXpx8c5mZMHaH36IZIznRAsQbqslx3omnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz5DtCRA9TVsSAnZWagAA7OwQAIvs7nwEfjQknw/6qYbR\nhXB2QuhUaaugfqG4uANW8C76Lgb7acFSU/038RsqY5+uvDfGr5eb1RYdcL1/\nAZd9cAIW9PjFYWh5olPwDlOXsVvtPuBm4zk8j8LKJqsGU811ZSDtwa380IT2\nfa+oIuPk/qM1Q6V1U00nVSzDRMVQic99uS02c6u1xELSo+yuUN933BEg5okw\nafc1brQjDtn7UzQCGPO9YewIvh8+4+eGmvEFFDVwRp7/hczFRycwoIYze+Ox\njWV4OSyA7FyKfHDnH1lacIO0rWRSfttRSNeprk0ZFqOlLTDEqHpzzixnvAqz\nvvaU5/OkkKNPgcr8deMAFA4jzzQpWH7MUZHFW1E/ClvHHeJWAB9jQDNwrxtR\nDf34mjQ2cPTDmzXQuFRM5r44KhXxLmC51Jfex1/qFbIHupccG3DjUoyC2u2Q\nrHwxgjubv9WbezUjkRRCfw1xvkPp87y9dGm8l8Gwo07ifwBSiSm4CFDHNA2S\nqhxtDfaw5svDiUtc2plx/SA6X2hJuL2/HKfUKt2Dpbzc9aF6j13J6Abbpmsc\nXRGfpq/WoaiZNuX0p2g6PxAY05eOYI+Y915rQIQRXN3v22t5surfypRqKaPV\nnNkqVJbgwh8K18+DY77i28HjMegX9H9T/vvQ3/TETPtnfErxHv7XcPFx8tnC\n/ms0\r\n=NHyS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.5.0": {"name": "webpack-merge", "version": "5.5.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "18b860249ee3f940ed1607c6b0cf1912b27dbf44", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.5.0.tgz", "fileCount": 22, "integrity": "sha512-EYKu2To70zpXh65y+ERG/8TbOy0YxPERP1hGvl5nnx7zY0HLZU57zNRlIowiPYQ8lI7kXsCHa5owKMgv/ImW/w==", "signatures": [{"sig": "MEUCIQDRXuR90ffd8VuuTzCB/IuXQZsyNk9H72WM8HQl1KD4cQIgCfKQUQTIVly8nlblid6Xde0jwiWpTxLYK0tI/S0OTE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0eAJCRA9TVsSAnZWagAAQ2cP/1o25WFhLdaE43hy9i2x\nqsYNH2iAmFpgTtVcvXDsLZv4iAPdcF4CJQMK6HhQWCsNPoWRDSmsUTtACEcw\n29eCsLsFUHu+j1urXosySwF6uRlbjMurXDXqhfZyk05bn0j7QkVKsXjnipiy\n3FAC+MgVvxEnxgVz0kPN+tBmujfyGcsh0qCGZUhFcmLVcEzkcXnK0FCBkxBu\njqTMcgtORtO7kkM6H2TozqUMHaWz4RsfPSDMz1Kg9SPBbMYM2rYtgrhitf/F\njoI6HD7OzzP9x79fO9bovSNysUFtkWGqYsYuvxMeq3MfNL17x5kukHYV99Yp\nDWTn3RHpJMyN8AcyAwMRIPz2JuJnZgMNLN12lAOICOhd9YNTy6yHusHjm2BM\n9Ves8MZcJrrq9Gc2V1/ECsisk2h3jyou5WN2yEz7ggiHGgG+saGL23dgNUMs\ndH+6ZuurDqHFfDnl2c1mrVPBMQoJ461Iq+LmmfCuuduRj6Yg5/Cmd4TJpvNp\nnSyTUObHdA//OE8sBButhdc1y3oy7pex0pfB3xJ3xu7x4X+omyinnHwcWkAe\nms3hRH1/iZSq/b3dtJ6WYYBaAAxNB8gRTzojEJ3c1MR5y3ylsrdoAgKIuK9v\n+3FC8+b6YUZ0YHncLYpcOp4v0IqMH8ejOPvQAD66Jmqmfngqvg/hRjNNNEy6\nMhcC\r\n=izkV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.6.0": {"name": "webpack-merge", "version": "5.6.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "21e0dbfb8399fda37b9bbccf1a83134dfd1715f7", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.6.0.tgz", "fileCount": 22, "integrity": "sha512-/2Dz4af6I3uG4QubAttbaV5WXv+Voc7Ud3VSIha744qPifRreMAB+o62376ZkMRt87JQSyvxxaCnkeEpl8pXpA==", "signatures": [{"sig": "MEUCIQD8MlXR/XmN0zcxtEn13U4Jp9MBdLemQ4DaonffUp0J3QIgAVLHOjkvyN4K6Hj2BdI65wRQE1wP7p1/ucW3f7zp7us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf033YCRA9TVsSAnZWagAA+tcP/0CNvSDR1/IlyJTb4fV/\nOUMM6JSiNJ1TLQGps1+vpN6vmraP5IifK29hkg4I3mDg/Gmv7dN+7T4o5jmz\nEIQRVexQpQ62AvbSwXHgMPP8G4z9ivaYjtB/NLeP9iQlewDPn58HGsI+L35S\njU8+/9ygZjqBezbNOb0w2Be1hqRXzpD/mABwG+AReDqt4p369bXKEneLL362\nN/nDe/AyB6dTbWQakhIy89VMhh8RKlitihybz9hO7Cj7lmBKVseYdCSsertB\nRfuVe+uzO36SEZy99DZiQUcxRmb5qUUUtF70TKr8+dyUxGvaqBlWBChFhz/0\nFXALPCdt/1+9zRT7mghrs6WxslOXG7D8ob74bKTZqjRjWWtIDOEHejnKd/kJ\nyraSworoLMaDxFqdDtA+QDfCDnzG5SNlrF58lIQtzM4/KuQU/DrPURMIuqqk\nHTpgtDQqQLBp/6mT4p+gOgLffruSeedt3N3aynf3f/aTfBha2kAR5Gjnegrc\nRjT5fKBW+QwLEh9nuOH7AqLQMUZmVEy6KHZMDasQPnaAGXFpJFT068ExO5Fx\nyyqzCH5T3ZUzbbPa9ZqLb9dQ838D097KJ8++eIgUHGbEOIf2PtwphriA1VON\niwWOeLLb+HIhOUJ3sno45jeQSc6gMmukxhiTzDeSl/VxONFxUW+El5bKnobj\neHlF\r\n=Y3U0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.6.1": {"name": "webpack-merge", "version": "5.6.1", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "93c886a6871486a799e5dd32d312008109694b5b", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.6.1.tgz", "fileCount": 22, "integrity": "sha512-5n19s1YcyQkBJkYtE+Tq3br/Ax6ofQjMA4U8PUfpcaRB2cvxJNoikUK4RQN3sEJt1Fl4Dym3qc2AnODgGK0vKg==", "signatures": [{"sig": "MEUCIQDO4lKWSAhamH3G00SGS+eUki2jT0K/j5qTfdGpSVDZngIgTzv60MZiTMfjS1XZX8KSM5HjMgh0HzahFP6bPu+o2FQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0352CRA9TVsSAnZWagAA7EAP/1WvY7BEmzc/A1ctpe0Q\n0i/TTqpS01TXy7q7zkVJuHC0kl/wOCpHh7ric2FS9Skz6dG3kwCQ0D7i23BB\n3kW3VUY4ahBWjLKVYlahzHia2S5ULaUT4JWqR2V4ARaJmcvBX6yCdWtHKTpk\nYOrTrORJGwVam1SalPWVHyIiTHrzph7J6wnqU1a/mMe5cPPx5X7p7gIA0kCO\nfRemexsb1KJnEXim4Ppi8ucWuNaMy1P/gSM0fmBLpgY0BB6zPcNI5iwSUtcw\nRqFYmmNlP0/j2Fv0OJ09DUuXL3Fc/ZmbVwOinWgI8Q9LHAbPFBydefutUgyf\niaXTmqBk20juM+stG35mBfLxRW9eennWDBPb/IIIBMR5W9Z9M4HHe9QT88Tn\nIlxgBvfIv/qz4edJt4n8WQimr+KtZPdVXMWpc7Xr/JjYKAmlBqdLq2T9tKhd\n2d66wZdjPiVXHPSzZVD06DQfnMiaA00qyfEXtRrjGMYTfY402a92JFPooKn8\nt83kb9/kfNLbBE5o8V9p75i+8uZ05RqEgjsN3QU5X1a64U8Dl9Ib1Z2l59g9\nZrMmWBNTji87x/zmJWxvzIkSgxHsoYOoj8P01EAjgX6fCBRYTyO2PDiyOhUK\nXPe54vKesqC6k79nuZcl0VZ7lxu9mrE/m0M/jV6YlMMhroFhKNZb8WAY6r9H\nwBie\r\n=O+Ek\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.7.0": {"name": "webpack-merge", "version": "5.7.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "004dec31f7a875e590c9731e5bdcd3efad0f2715", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.7.0.tgz", "fileCount": 22, "integrity": "sha512-UryoMJGRMwOOh/ie4NXZC1OtT0mkA7Ny2+C/MkWOwTRG+jVNEwChVV/+x8rd+ga2mVLeQ0m+QmzLAg7N36+oag==", "signatures": [{"sig": "MEUCIQCS/rPl5egWOZi/mQ1VrMz34b88d+UirLr+YAQO43ZqyQIgARoBSE0LamrGNvm7gpY7AcL7vW2aQ/S97W82a+h4V/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1KAoCRA9TVsSAnZWagAALYQP/26WJcO9QBOmQ2qsBKE8\nIqkxStGE1Ys+UpE7TXVybGS51y9lVCEwJss7y+RwSxYRYv0iLIJKydtDbVek\nnkkuLZGmfJG656t9mY3f1GKo5g5chp5JgjKkPWx7pdWqnFrG7pJ88AyPgf+a\ng2kz9ofV2WRlUrJLMGVBdGlDaOYuKL3OHGuHrmXMYF1zMui2J/hHE5zdD0Fo\n5AVjDZuL521SafNPcqjq4+fD0ZZIlFldloaoSnqLkPCO4I2l9zlVlwX8pN9A\nr0JOISGb6kPQGdCXG9VTpCvuFd8aYLOnL8yPJ1LWi8ltZlYU/HvlsbttBKYR\nD9PbAY2oNscNYhiw3qBbN0gqg/iePB6jBFSX/8tmzSM/F1vPSZJaPnCHjviN\nJg3ii5Cti/U/NsWfx6guaE+wAwR7GS/v/ZCwOPwuCshtjIwwP5BNHm4AsnMT\nOh6bM31b+XWvT8XIc8LpK9w9GYPyAMwYDGVWH0dg2vHwLxRfn4sUXV4+4uoF\nnosrIRhUEGw23BbZui29ziNbyiXHlkLeygYv2Na2mJtddU4X0r26ZmhssLHG\nDtbZvwSIiWbERnENarOzxDST0BDHwkKPE0kFNjZDbaLksvorO7yRtyEQjJ/B\n4WhLbAM0rgKjoLwHXu6b506247XY85TzCYnq/6o1rOf1DIBhraqjbQS0jJXd\nL2uj\r\n=lwwj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.7.1": {"name": "webpack-merge", "version": "5.7.1", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "61fbd4bd754e40d7cf5e77b6c72117a13ac119fc", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.7.1.tgz", "fileCount": 22, "integrity": "sha512-0oU4fWXTSRaZ+ePos9eMeCprmfDFOQgfbEa9fzeX2HAAUuILjj/eI1H+j62YdaCwTMIgE5DHctIhTVrKA6aDyw==", "signatures": [{"sig": "MEUCIQDTxcqj6LfJVz5cm5zW9/myiZ7U7hC63U55VnZTeWzfNgIge7XHwzApSEimIJjArGRKdaSf1y8ik8VApOBvHsb//vQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2dVLCRA9TVsSAnZWagAADCgP/jG7UfYRlK31hSf9rcPe\nQc8+woMME9QahlMaB8ASOWh4AtUVD+cw5QEpvUewrRcWjS0U0kfdoNO5hPdx\nUICbR90uTRt5xzsvu9qmgZiAIgw8SnKn+EYHsE8na6i2thFMsGyJtGs55sqk\nIrEOTTSKipUi767DGam63R75Is9+syJCLA1EF4u3oj475f1QM52sx9S5LBZ8\niNug5Y8sWmpJ3kBGihcK945tLt+A4AhHy2UIZ5oTKJxXXOk8WKwshCKy1v1R\nQHgSSDlCLAXhaVl9Mu1bVo0e1TDlOJXZ7KhhwPHjlojuX1LsiA3LzjoTF5U6\nQIuldb4ydH9B6Fqv81rcHgUhcGesyBjX5Wrk5c8Lon4TpNnpsuZq+uIfrGrT\nZJweRaQ3A8p7WRdGNnItAEQ66poa/TAvkPA9VMyaepzN5IR6r2dxCTYbUKPV\naDdJo1wmq2HXQXariEuiiUQY1MwkcktypEj8vJB+SUTUHjNqcx5qF1azIt1g\n17ZD2hepcTNMHHAEdFPIncNMqJedvKkfurq0JTFZqhOSnEUVDOwiZ1nOmQjF\noC9a6DQ93BEU1R3Ml/UDOnBpEQC/fMUb70srrvjKehw11t+RTPAuiGKYIOMr\nhmccv+J7M+BUXQNOeNWlaIpoMpFvlwgn6gBGUi8PjMZ2nWh8zzyO+8VDzdqn\nT0Ks\r\n=pK89\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.7.2": {"name": "webpack-merge", "version": "5.7.2", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "55320baf05d8be068ff8112be72c04d7c14a9abd", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.7.2.tgz", "fileCount": 22, "integrity": "sha512-7o7qjwcIB6lqHX0VZA2Vxcp8RHftW1LNcaB6t87PEpco/VPlG0Wn9DnvgmcJ0nZU578/vKQfhDSLTF0EZ+pFAg==", "signatures": [{"sig": "MEUCIQD6eITyasOjh+yr7MTTR2yVA9NcgrESFr8duu9Z2TDtxwIgNJHrHQH89c7jjLVZrQZyDnVvr2gaqWSJ08+UEOuAl04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2hRBCRA9TVsSAnZWagAAfz8QAILz9hZFG1mB/jqp4Qnx\nINqi+OPUvO1Xa5boXUcVMUbA50fkP0p6nXEuD4eIyQHHtFJDWQgPiFd0LR1/\nu2qA/zK3RKnUTr6ZyEuNvz2O2NlncQ8k2cpQSBuBmcduF89kx+eC0UdObUQV\n4SDSU3pmCgVO8XNK6tZFiuHsVk7Mz/nMGxtiaBwsHN6RA1AxGme8gFMHsR12\nWdd2kHNj55mijML5dbHQf2p8BTnM71gtrNFHLpjBnPHROz1AHsLaB2AXeYze\nFvvZwWBV330zMlFgXuQFP6E7JrPrB2JU7+FId19L/IAddfG/jTx/REEStSHr\nHlgwSeDwaFjOnCt0ECZjqJxEBuebjnm23313shq9Ay2y1imVDooZE6F56tk7\nw45mVBFuM+Utmq9bgdM12PF8W4RtvbVBXSxyEQztC+aAgn1y9QbcZxxmu3Lm\naklYxcJn/qwWraPjFt4s7pnET6QOVPmqkffwD9MMcDbrEVvsWRaKoNLko8tT\nyzQrRNQk9kMHX16H+qcJm/1G9CDFeN/lejbZYNysrHpUhokAMlCkpDYX96d0\nVcaRh0lYYZ+5WJ0jugwdso1EFIGQfEG1w5ZNi5dc1bTiI6STT3C3uufPvS4K\nsPvNJo/iIdcZ1yCyJcW5dtvJY7BaamkDWm22p3YjBKXDODnP2ZRg57+sToH6\n26m0\r\n=8IG8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.7.3": {"name": "webpack-merge", "version": "5.7.3", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^4.3.0", "tslib": "^2.0.3", "webpack": "^5.3.2", "prettier": "^2.1.2", "typescript": "^4.0.5", "@types/estree": "0.0.45"}, "dist": {"shasum": "2a0754e1877a25a8bbab3d2475ca70a052708213", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.7.3.tgz", "fileCount": 22, "integrity": "sha512-6/JUQv0ELQ1igjGDzHkXbVDRxkfA57Zw7PfiupdLFJYrgFqY5ZP8xxbpp2lU3EPwYx89ht5Z/aDkD40hFCm5AA==", "signatures": [{"sig": "MEUCID515B+8MYUt/gOdKarJBl0VzEaGOt5V/hCVsB6UomT9AiEA0WHjCiJFB3wU2aJ6GePHOWZ0QKYNLoPEHUyxdijf8iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4fMYCRA9TVsSAnZWagAAZz0QAIHaHeWEAbkRddnWPWkI\neMx8x1YgfJhApZGI0jklPb+2LfkkzEbCAPoJu6tVTTc46alsJp4NV21+ghqs\ngen8spXKyDpm6mNoA2t3HpSqF71mEqp2JksOP5a9dpU8Rlj/0TVNbcj4Cfme\ncSv0+Osp99Qwc88v0nUuiIEn30IcwK0BAAl4h6copUppnpMawjoD4T4DMygY\nYCXYDcfBOF/Kij5hGVE8VfTwWdYC4RoeZ95AQ2WOIcRLGWG94n8PVeN2Ws2x\nAfhqrVRk5WUY79BL2q46lvtX7soFOZqtPtoMDCmlV5544QctRhAA6tHdmhtm\n7iMtSFJZzygRAfeUefqTyxhZV9bsWD/meV5f01HrY7jXCI8RE//dsABqwi3D\npDjJhxAVgcOKq8/yQwFqKg6EKVlDAkwUnoELnBOJj/ex+B7d8uEJG5Fsh15e\nleqA4CiTlJNH/9/w4AzWBsR5vDeS/v+xutHgzKlABciZDCFanjWEa51Ku4bj\niBzV+d2oipfavIXKeFlkElaqJUpiPsn6UVV8pZV7rNVPqw73NlzUUOWIrZpt\n8OsBjLdEInxwciYh+b4HSSJgLF0ML1nRFXdATlCHZIRlKogDLeHd4ReeGm+4\nb3CyA1bjD+jquljREEq5xZwEPrCalKFgLjlvU4Aew/clLdzlghpJ1TcP7F9D\nOjMR\r\n=fSG+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.8.0": {"name": "webpack-merge", "version": "5.8.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^6.0.0", "tslib": "^2.2.0", "webpack": "^5.38.1", "prettier": "^2.3.1", "typescript": "^4.3.2", "@types/estree": "0.0.48"}, "dist": {"shasum": "2b39dbf22af87776ad744c390223731d30a68f61", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.8.0.tgz", "fileCount": 22, "integrity": "sha512-/SaI7xY0831XwP6kzuwhKWVKDP9t1QY1h65lAFLbZqMPIuYcD9QAW4u9STIbU9kaJbPBB/geU/gLr1wDjOhQ+Q==", "signatures": [{"sig": "MEQCIAaS0KYxZLn7BGYo5Z78EjHMAM0fq1N2m8hyt34u75OQAiBBGtt9JpaSUiQo5F8JjKSt1hSY580UVbqsStrvrpdO0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvdolCRA9TVsSAnZWagAA1/sP/0X6JQPPII7YUCo1KtQG\nPipJFHh3CSV2sD7Z/TCJFOEUtOmZcnNKmQHBCd2k3H3NfPXzBsBArpy2I2bv\nfU+MldsWmxVrTsu9IMJSZr8CQEh+SxZuzpeIqk44FEryLWrRdxB6nKyQuFHV\n3jiBwb+FkUAQHnf0KvsIpHRMIurLtGJ1Fk9i/TGFe4jpbdF0SBjI41njEile\nOJ3FNMNt+KF5Ac0N7FPPxKPm+jjY1uu8Z4BPr16xp0Nhh5Hzn7AUoX3ls/e+\n1gr0aSGSiV7ant00IFP/oA+Ai15JIJAW0EoS6d5rdnm9nVak1lPmGZsjuSIn\n7BoFwIh7fTGbn9s8zTzNOO6sh+J++kF8J6j30rkMnMFvp3fg053EcbpLZVJQ\nhBdcS1LnD31pmc7dO7dN9BAdZBkpoKcjnFBmH//QUq1PUz5S/5wvCOPGzMw6\noVAzb9v+exUNmufyCOp958f4KQ7sv4kMe90trq3uF+xBiw+RngQdJ7mcGaPn\nM7vyLJUVIGJKzW9xVesKPSVODTdNd5j8GlKmeBj643ZjkCuf1UG/JP+rFoqI\nzecTTx9LByd5BNyUpmj1WfoxzEglp6/H+Nv8kw+ONeYCJrvb0aU4mxZKe3Zd\neorZItkC/n2yIUe4ZsW+w73VlZaG+5SRuc4eJ0HtyP4zHanc15KUOcmwfTLN\nYhqv\r\n=fNjk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "5.9.0": {"name": "webpack-merge", "version": "5.9.0", "dependencies": {"wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^6.0.0", "tslib": "^2.2.0", "webpack": "^5.38.1", "prettier": "^2.3.1", "typescript": "^4.3.2", "@types/estree": "0.0.48"}, "dist": {"shasum": "dc160a1c4cf512ceca515cc231669e9ddb133826", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.9.0.tgz", "fileCount": 21, "integrity": "sha512-6NbRQw4+Sy50vYNTw7EyOn41OZItPiXB8GNv3INSoe3PSFaHJEz3SHTrYVaRm2LilNGnFUzh0FAwqPEmU/CwDg==", "signatures": [{"sig": "MEUCIQC+bYGv/SUepdvi43o8FCRJw98bauarazbSYi/UkCEOVQIgfJqZx2KISFndOuvDy+6/jz/NHf3bPwMrUjIkfBuxM3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43465}, "engines": {"node": ">=10.0.0"}}, "5.10.0": {"name": "webpack-merge", "version": "5.10.0", "dependencies": {"flat": "^5.0.2", "wildcard": "^2.0.0", "clone-deep": "^4.0.1"}, "devDependencies": {"tsdx": "^0.14.1", "husky": "^6.0.0", "tslib": "^2.2.0", "webpack": "^5.38.1", "prettier": "^2.3.1", "typescript": "^4.3.2", "@types/flat": "^5.0.3", "@types/estree": "0.0.48"}, "dist": {"shasum": "a3ad5d773241e9c682803abf628d4cd62b8a4177", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.10.0.tgz", "fileCount": 21, "integrity": "sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==", "signatures": [{"sig": "MEQCIERYRl5AUfnblu5BWEM7LmXFhaXRh4IFtQ6/xqaWI8g4AiBS/pgvbgCNHlA38aKP/I/XoQu1X5G40CGpAxUOG5XC3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47918}, "engines": {"node": ">=10.0.0"}}, "6.0.0": {"name": "webpack-merge", "version": "6.0.0", "dependencies": {"flat": "^5.0.2", "wildcard": "^2.0.1"}, "devDependencies": {"husky": "^6.0.0", "tslib": "^2.6.2", "dts-cli": "^2.0.3", "webpack": "^5.89.0", "prettier": "^3.0.3", "typescript": "^5.2.2", "@types/flat": "^5.0.3", "@types/estree": "1.0.2"}, "dist": {"shasum": "d26712b57b449af7e3dfe6ad7c9a61fd5309fb14", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-6.0.0.tgz", "fileCount": 21, "integrity": "sha512-SA5+gWq3++1uTBBLYf0hsGgh6mv8uCkdxzPM+pUOwI06PoBfMp39UW3m/HgXRJMWCORqz0sUU5go90bJoPaBXg==", "signatures": [{"sig": "MEYCIQDUteOhsUmIzID2Jr5nsVMJULuJDXCtp10E9xkI4zjXyQIhAMWT91Bhylqg7EdhWyXVmqCb8RgTbaHjmCGGmyj99eEQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49108}, "engines": {"node": ">=18.0.0"}}, "6.0.1": {"name": "webpack-merge", "version": "6.0.1", "dependencies": {"flat": "^5.0.2", "wildcard": "^2.0.1", "clone-deep": "^4.0.1"}, "devDependencies": {"husky": "^6.0.0", "tslib": "^2.6.2", "dts-cli": "^2.0.3", "webpack": "^5.89.0", "prettier": "^3.0.3", "typescript": "^5.2.2", "@types/flat": "^5.0.3", "@types/estree": "1.0.2"}, "dist": {"shasum": "50c776868e080574725abc5869bd6e4ef0a16c6a", "tarball": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-6.0.1.tgz", "fileCount": 21, "integrity": "sha512-hXXvrjtx2PLYx4qruKl+kyRSLc52V+cCvMxRjmKwoA+CBbbF5GfIBtR6kCvl0fYGqTUPKB+1ktVmTHqMOzgCBg==", "signatures": [{"sig": "MEUCIHjP0tbyGtxwxtOos4/0JxYISJhDS3FBMnQ13cOuQ8RmAiEAyFS2Zrjc0UOgITQpPL+duVoCs0LMwtIQ4GA1Uj46O7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49496}, "engines": {"node": ">=18.0.0"}}}, "modified": "2024-07-12T22:21:48.848Z"}