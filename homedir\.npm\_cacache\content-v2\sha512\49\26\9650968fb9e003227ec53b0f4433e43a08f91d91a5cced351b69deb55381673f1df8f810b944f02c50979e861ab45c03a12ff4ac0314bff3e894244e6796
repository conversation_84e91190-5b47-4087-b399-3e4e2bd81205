{"_id": "@xtuc/long", "_rev": "5-d47850e6218543093399074bc0e2f35f", "name": "@xtuc/long", "dist-tags": {"latest": "4.2.2"}, "versions": {"4.0.0": {"name": "@xtuc/long", "version": "4.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A Long class for representing a 64-bit two's-complement integer value.", "module": "src/long.js", "main": "src/long.js", "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "files": ["index.js", "LICENSE", "README.md", "src/long.js", "dist/long.js", "dist/long.js.map", "index.d.ts"], "types": "index.d.ts", "gitHead": "658c1d81c41ba11943e7fdcc7c9f21598ffdeeca", "homepage": "https://github.com/dcodeIO/long.js#readme", "_id": "@xtuc/long@4.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PTV+6MLfRM2GYR0z37fB9N4mX6WRHGBjVcDKco3jwH6hCuu4urJuJ9R5G3YNlD85sY7gduTjt+9tdL+FemHLmA==", "shasum": "a35b8bf08e6fb806cee8605175868e874da92405", "tarball": "https://registry.npmjs.org/@xtuc/long/-/long-4.0.0.tgz", "fileCount": 8, "unpackedSize": 185846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT4K5CRA9TVsSAnZWagAAfnAP/Avdijuh6i7K0J4ZXwnZ\njbmQARauneBicuogKXKS4rjyu1+gwNgfOvvrjfuTBTb5LdFP3nyOd8ui0FLO\nGoyf5qHo2G0sxHswDtBX202hjOWksumJBnAGkNw4ASV1/Hq4gkYkjNDm0ahc\nPDZmT8hgNpTx96dIYmCc5gNOZ7Fnn+pRRZv3UbI2aKyqQBWD7Yup9HbpJJyS\n89N3xfU8z6jfa+5jt+N2+seA30D+chKDPlgx6MErBiL+9aaOwafLT4YdUXac\nBbu5CBTRjBbAO9CGFCob8qh4IyiBfVW5HnBllWGBpTb3CscTBEl1Dt49T+tg\njIKeZz+8F+nWzsRYdZUkUetCHnmZkPTfSg/CWuNt0vR+v4kX8NsYSNbin1x6\nfCeq3SscanrBMlVVfkcAnEUS+PrZ9LJH0IU+CwnJ41rjSxZ1B+eNuI5MwHUq\nTLhGJY2G56d4/RAXQ/lHmwtx10Jf4l4vFd2SkmDMaoTFvTl9bmaNi4Ja4K8L\nbTQZOKFuIS/Gn/P0W9a5t5OPyekfN11w7MQa0xQsZwueat7Koe5CZDMr0Wfw\nkHSAqGiG4sT4TlwhP+doVSpqch62sva2bfKgvyNPucd+47KvLNy0Eqe9zm96\nbuXYN9KJkwI2k6DXbC6V7k+yFWSooTlHEKi7Gt+ibyKlvF6atve9iMNrqNHm\nDRwF\r\n=rXG5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHX7uz6PGxaj2gH3i5fBTJ5CKOBwZS+1XQk0/VAe/p8kAiEA8V2iYgsBUxKU6/y4NvgwhH8QPHsBhBT/Zr4a+wuYvT0="}]}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/long_4.0.0_1531937465233_0.733747711800611"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "@xtuc/long", "version": "4.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A Long class for representing a 64-bit two's-complement integer value.", "module": "src/long.js", "main": "src/long.js", "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "files": ["index.js", "LICENSE", "README.md", "src/long.js", "dist/long.js", "dist/long.js.map", "index.d.ts"], "types": "index.d.ts", "gitHead": "658c1d81c41ba11943e7fdcc7c9f21598ffdeeca", "homepage": "https://github.com/dcodeIO/long.js#readme", "_id": "@xtuc/long@4.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nJ0nIv1LysbQwWADUAjfbn9WDAS6/oA2M0Gsgwcaq616wWlf0OS2+S2NLy/TXDmlU3p6tJVzRl7ALw/tQZ5crw==", "shasum": "2762d76ed755059f5f0fc0355f843a39089733d7", "tarball": "https://registry.npmjs.org/@xtuc/long/-/long-4.1.0.tgz", "fileCount": 8, "unpackedSize": 185846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT4LECRA9TVsSAnZWagAA1z8P/Rjd7lGnUAGAY+XKegSY\n5z1U7w7p48YTPeLLbqYfzvWaLohWUBhrxJ/KmJ+hZqaZ/17jgwtpbT8vRCKB\ndpZps1P/3dOpKyl2gV43l+isTtSdWX6zB44hoK+o8sLMxXQKqswcw54pyzP9\ncect2Wxuc1A13XIp+MtyzQxrt93g4yhOCjGQdvgahbqeKTXcyZSbghGV2gkd\nx4YGzKHtW+8G3KJC6YsVvhs6fxUCb0Br9zPrcf8uJgZvqu23depDysPh+0X0\nl98zDQIJX5y3sl6I+ZBU8R5ytJAbS2bkbRAOWbK0f1yazID/txhQ4XWOelLg\natZ0fiMM1o6/Uq+lUOTPHfcrFMQ+tTRh5IhNGLb6a65yBoxmWiE8zBouvmVv\nNTt91gJDuWKKd00+prT9PPrSeX2Dgr10fA3sxx8vZPEd87cixxH0/PR40m1T\nZk5pQl7/5dVDyLulg0gGT0b300x7PQf4rXPs0DusezXpRcBkyhlHbqxSn9Yd\nLdYOuGgMqUeOpVIocsP3V4wI8qs+iwbrBUzh90nzghB4ge9kWfYVX8GfY7ZA\n4GlrtCUT9WJSL/CmUYKO3yGpbdO4PoahINqZU+xlxliDgq8aD3S3tTkIQ7VN\nh+ATjYjfrsNQO5pRXHWNOi6/L5+hVkDyhZ/W1sca0KPlEvpq2KL1v+9AmlSr\nmdR5\r\n=+PHn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICukI44W93EAG/BOvWn55CoUisUa0WtVZrUEJJKNacOaAiBVBISozttgyM2LfmQWKi4j2VKxbJeo8HAGZq72VZ8t/g=="}]}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/long_4.1.0_1531937476645_0.6543092140380986"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "@xtuc/long", "version": "4.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A Long class for representing a 64-bit two's-complement integer value.", "module": "src/long.js", "main": "src/long.js", "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "files": ["index.js", "LICENSE", "README.md", "src/long.js", "dist/long.js", "dist/long.js.map", "index.d.ts"], "types": "index.d.ts", "gitHead": "97fe570cef6ec2d4b0f39a00bb05615d9de6f8ce", "homepage": "https://github.com/dcodeIO/long.js#readme", "_id": "@xtuc/long@4.2.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-39Ny4avefOuyqWROefW/fbbkdWEA9wXPHIHAdAVDTbysjnX+8tcLFDyqzEGjErGoxGfHleJvV4l74tZEemjXbg==", "shasum": "04cfcdc1a9273649a4c9d43097f2937bc64f0cec", "tarball": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.0.tgz", "fileCount": 8, "unpackedSize": 185846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT4MaCRA9TVsSAnZWagAAzeoP/jntOeoTu1y/6S5iJnFS\ncf3QVGZk1C2AGww3yPTCk3tf2++9UioaJDoV9/MfDrYMDRDwkZxIgLF5A9oq\nKA3g2hjSsS5s1XGttAtcesKLIfCyLLoneQMTe7NRVsiuQA9hw0tXQf7Pxu2K\nISI12EIImq/J3XkF4aEJ+EgleAEUC/xP7vtqje1AQp4btrulq7Z0AJKc0t3r\n6Jtn5iaFjUVG5TwFw6qHZEpdMtE4b3gpvyGcM2YQrL6b6KuEiHuvxL97t+5v\nznWwee5bE0E3d6NyAZgINXi7UgonKa5JKqdiO9xKZ1z6XhkAoF+Ao/JEatHT\ng1/ptiWSwRZ8+B0AWQRkgCUs/L/Yf7yHSSZMnljS2J9RW4XlzzM3ehy2H3da\n2Cf91MqnLBkcCBkQu1gdAcowhMuUAEUC4n235KPjOEc8CjPZu7G8Cf1UYdM4\nSuybgVoyekVRz/6dJsCmTKruuN5Mi5IK4fZ17mNwAybHsR+NBsxc2/WQVyFO\nBZ/hzx9SOZP8+qcP2ltBqAfZe5mAA5oMNDd8y5PZfzOb6veHdDAVnT7WCotX\nXQ9TUi5TkF58+8brLyMiI95xPlJDBoDtjaQbxSJN1yviFCPk1mIpSAEdftOG\nZ31MwhOd7RGdNgXVmYEvDgKvpbg9eHagjopdwrD5LNb8PIC6Mlx0V2a4W/ws\nFPsK\r\n=NABC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXoIpbuEWh2NjZedqYyJt8b/dS4GPumgMGM7+aKQQxkwIgaW+Z8NZyBfcNwmawUzYC7OMTYqA/lsEQ9OGYlSYWIgI="}]}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/long_4.2.0_1531937561964_0.24650363376181272"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "@xtuc/long", "version": "4.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A Long class for representing a 64-bit two's-complement integer value.", "module": "src/long.js", "main": "dist/long.cjs.js", "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"@babel/cli": "^7.0.0-beta.54", "@babel/core": "^7.0.0-beta.54", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.54", "webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "files": ["index.js", "LICENSE", "README.md", "src/long.js", "dist/long.js", "dist/long.js.map", "index.d.ts"], "types": "index.d.ts", "prepublish": "babel --plugins @babel/plugin-transform-modules-commonjs src/long.js -o dist/long.cjs.js", "gitHead": "93cee21ef5453d5c2cef3e78cc1c854ded80b576", "homepage": "https://github.com/dcodeIO/long.js#readme", "_id": "@xtuc/long@4.2.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FZdkNBDqBRHKQ2MEbSC17xnPFOhZxeJ2YGSfr2BKf3sujG49Qe3bB+rGCwQfIaA7WHnGeGkSijX4FuBCdrzW/g==", "shasum": "5c85d662f76fa1d34575766c5dcd6615abcd30d8", "tarball": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.1.tgz", "fileCount": 9, "unpackedSize": 223325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT4o3CRA9TVsSAnZWagAAJHUP/Ayl8fNzfndyBUYqAwXM\nHWfO9IuwHT+T1EKep+MU2lfA/F0n8k3GVNrPUEOnAcmMD74Ll4XY+Aqwgg+x\n8YvjvyIYxdL4jnAMSF5IDZhAGwVyBMDnV5POYburzKF7aXCiMTbMSn9DROf1\n5F1QXPSV7Z2uJMRzECgP4hPdetnDTaaVAseiNOOmCki/9lSd5RyuFdnykWmM\nwnrbGKA80md3qr9CECetIn55HGNNkg8Vj1BD1dXNe+quDA09TA+2EhpmGR7U\ncCCiiD9ZrBEEQODtKB/K0qb5WIkWr1YDwL/1YAjf/lprnDQVf/3sOCW9cCuw\n+qlS3dLosfiiL5UjzAhalGwJzdeFbgNJ+7DoCMM36zC6+OVo+zZdQm7O1QGP\n65EXaseNc6OTlnHY2Q0Bgwa4+ULPduLin3aLtjhQGn7eGpww/SZOhzNkXS5G\nJcqLwFidDRZzt6+9UKPf1xNlNTQOK5afIM8MoXthTGuVwQ3X/2izhbxC8Hee\n8jCBz6GXvdgslpQ+9753p8jVY9+2W/9k6aLOafi9/l7vHJ5Cnsqj/HGnkp9p\nexTYw/daW4n9EA+/CO4byOuajvmqeI8NA+p4kcKLm0Evd9p9iW6j91DAh+RM\nMjwWrAlFFQawRhxlejkSrHAyVUMdgi+c5Pv0ZeUtkIWv7nnHQrKsoURyMwhR\n4qNr\r\n=TGLk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfIEdFROz1W+3w7wTPi1jw8tqXd0wlUpAF/G/YjKUxjwIgWJQY6Qc3PLjZlcg7yfh2Ad7hJolFb5QGfBnxYGrjpyg="}]}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/long_4.2.1_1531939382996_0.5140723365626272"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "@xtuc/long", "version": "4.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A Long class for representing a 64-bit two's-complement integer value.", "main": "src/long.js", "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"webpack": "^3.10.0"}, "license": "Apache-2.0", "scripts": {"build": "webpack", "test": "node tests"}, "types": "index.d.ts", "gitHead": "8181a6b50a2a230f0b2a1e4c4093f9b9d19c8b69", "homepage": "https://github.com/dcodeIO/long.js#readme", "_id": "@xtuc/long@4.2.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==", "shasum": "d291c6a4e97989b5c61d9acf396ae4fe133a718d", "tarball": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "fileCount": 8, "unpackedSize": 190308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcamrLCRA9TVsSAnZWagAAE/YQAJsFNB6O5qOUEsd6Xngr\nB9LYtiDz/Cj75xiX3k//+gXbaLO0fbKBPvGHIHHNB9ep+wQ6mJZSEH4Bfj44\nQu7APHuZtytQ41NY7FylKVzBMaloSbhoApAbhilWL4sHgzkzFWS4vYecboGt\nEiZLfVMf7fkPyY++TKCIOzzn54GwzuE1at1iLAuk3Jidh3xaG4qvc+Z7psgh\nFzB1SLPklscI1lRaAxwKel78YG4EJQFzk/c8L1tVaWm9vJNf1bKy+av3dPVk\nXYBHsWCFRJvJ8wrYPHdaSbI6MsED5eLQqOMtxThcfEL0Hjk0bV3I5OAC4ZYB\nANt1HKEoHaFOGoK/zaxAmT1G1R+7gP+pCIZwkgXXNZg41Vt8Xg+Ynx9rzpVa\nA3cIe2irjegrMT97qyrgUj5Wd6vBh54dfBK8846eW2cXm0HMcD/yVD8lvafW\n2K40Yet80Euwn0l8h/zn4Nm2ZqhH3OG5sxuNO+Zz5fIJ2KGZo3JW6OSqLp9b\ntYAU31G1HqGpgcIqJZGvFh8TdwEazZmiNATuZypOkQ8LamM47hkUM8l///jF\nFzUqk6VJ0VCnAouQRf+nazRKJiVI5sGFnnJTQAbZZmFeyygtTdX5qvLE8APy\npx43LDHfYw4YwKbwD4L6kjNoUBSFuAiyhwiJDqvgK61k1UWWicejefns1Dqb\non8O\r\n=Ptma\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAq7uWZhD120FKn73L1GLXQ77WrC3D/cx2p7z7xxfNggIhAM8KCCW5T4wL46DI3gF+RgY/LPbejFq4AcEiO4R2tm5d"}]}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/long_4.2.2_1550478026269_0.8472414364850549"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-07-18T18:11:05.009Z", "4.0.0": "2018-07-18T18:11:05.292Z", "modified": "2022-04-11T10:47:49.089Z", "4.1.0": "2018-07-18T18:11:16.783Z", "4.2.0": "2018-07-18T18:12:42.087Z", "4.2.1": "2018-07-18T18:43:03.066Z", "4.2.2": "2019-02-18T08:20:26.462Z"}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "description": "A Long class for representing a 64-bit two's-complement integer value.", "homepage": "https://github.com/dcodeIO/long.js#readme", "keywords": ["math"], "repository": {"type": "git", "url": "git+https://github.com/dcodeIO/long.js.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/dcodeIO/long.js/issues"}, "license": "Apache-2.0", "readme": "long.js\n=======\n\nA Long class for representing a 64 bit two's-complement integer value derived from the [Closure Library](https://github.com/google/closure-library)\nfor stand-alone use and extended with unsigned support.\n\n[![npm](https://img.shields.io/npm/v/long.svg)](https://www.npmjs.com/package/long) [![Build Status](https://travis-ci.org/dcodeIO/long.js.svg)](https://travis-ci.org/dcodeIO/long.js)\n\nBackground\n----------\n\nAs of [ECMA-262 5th Edition](http://ecma262-5.com/ELS5_HTML.htm#Section_8.5), \"all the positive and negative integers\nwhose magnitude is no greater than 2<sup>53</sup> are representable in the Number type\", which is \"representing the\ndoubleprecision 64-bit format IEEE 754 values as specified in the IEEE Standard for Binary Floating-Point Arithmetic\".\nThe [maximum safe integer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/MAX_SAFE_INTEGER)\nin JavaScript is 2<sup>53</sup>-1.\n\nExample: 2<sup>64</sup>-1 is 1844674407370955**1615** but in JavaScript it evaluates to 1844674407370955**2000**.\n\nFurthermore, bitwise operators in JavaScript \"deal only with integers in the range −2<sup>31</sup> through\n2<sup>31</sup>−1, inclusive, or in the range 0 through 2<sup>32</sup>−1, inclusive. These operators accept any value of\nthe Number type but first convert each such value to one of 2<sup>32</sup> integer values.\"\n\nIn some use cases, however, it is required to be able to reliably work with and perform bitwise operations on the full\n64 bits. This is where long.js comes into play.\n\nUsage\n-----\n\nThe class is compatible with CommonJS and AMD loaders and is exposed globally as `Long` if neither is available.\n\n```javascript\nvar Long = require(\"long\");\n\nvar longVal = new Long(0xFFFFFFFF, 0x7FFFFFFF);\n\nconsole.log(longVal.toString());\n...\n```\n\nAPI\n---\n\n### Constructor\n\n* new **Long**(low: `number`, high: `number`, unsigned?: `boolean`)<br />\n  Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers. See the from* functions below for more convenient ways of constructing Longs.\n\n### Fields\n\n* Long#**low**: `number`<br />\n  The low 32 bits as a signed value.\n\n* Long#**high**: `number`<br />\n  The high 32 bits as a signed value.\n\n* Long#**unsigned**: `boolean`<br />\n  Whether unsigned or not.\n\n### Constants\n\n* Long.**ZERO**: `Long`<br />\n  Signed zero.\n\n* Long.**ONE**: `Long`<br />\n  Signed one.\n\n* Long.**NEG_ONE**: `Long`<br />\n  Signed negative one.\n\n* Long.**UZERO**: `Long`<br />\n  Unsigned zero.\n\n* Long.**UONE**: `Long`<br />\n  Unsigned one.\n\n* Long.**MAX_VALUE**: `Long`<br />\n  Maximum signed value.\n\n* Long.**MIN_VALUE**: `Long`<br />\n  Minimum signed value.\n\n* Long.**MAX_UNSIGNED_VALUE**: `Long`<br />\n  Maximum unsigned value.\n\n### Utility\n\n* Long.**isLong**(obj: `*`): `boolean`<br />\n  Tests if the specified object is a Long.\n\n* Long.**fromBits**(lowBits: `number`, highBits: `number`, unsigned?: `boolean`): `Long`<br />\n  Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is assumed to use 32 bits.\n\n* Long.**fromBytes**(bytes: `number[]`, unsigned?: `boolean`, le?: `boolean`): `Long`<br />\n  Creates a Long from its byte representation.\n\n* Long.**fromBytesLE**(bytes: `number[]`, unsigned?: `boolean`): `Long`<br />\n  Creates a Long from its little endian byte representation.\n\n* Long.**fromBytesBE**(bytes: `number[]`, unsigned?: `boolean`): `Long`<br />\n  Creates a Long from its big endian byte representation.\n\n* Long.**fromInt**(value: `number`, unsigned?: `boolean`): `Long`<br />\n  Returns a Long representing the given 32 bit integer value.\n\n* Long.**fromNumber**(value: `number`, unsigned?: `boolean`): `Long`<br />\n  Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n\n* Long.**fromString**(str: `string`, unsigned?: `boolean`, radix?: `number`)<br />\n  Long.**fromString**(str: `string`, radix: `number`)<br />\n  Returns a Long representation of the given string, written using the specified radix.\n\n* Long.**fromValue**(val: `*`, unsigned?: `boolean`): `Long`<br />\n  Converts the specified value to a Long using the appropriate from* function for its type.\n\n### Methods\n\n* Long#**add**(addend: `Long | number | string`): `Long`<br />\n  Returns the sum of this and the specified Long.\n\n* Long#**and**(other: `Long | number | string`): `Long`<br />\n  Returns the bitwise AND of this Long and the specified.\n\n* Long#**compare**/**comp**(other: `Long | number | string`): `number`<br />\n  Compares this Long's value with the specified's. Returns `0` if they are the same, `1` if the this is greater and `-1` if the given one is greater.\n\n* Long#**divide**/**div**(divisor: `Long | number | string`): `Long`<br />\n  Returns this Long divided by the specified.\n\n* Long#**equals**/**eq**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value equals the specified's.\n\n* Long#**getHighBits**(): `number`<br />\n  Gets the high 32 bits as a signed integer.\n\n* Long#**getHighBitsUnsigned**(): `number`<br />\n  Gets the high 32 bits as an unsigned integer.\n\n* Long#**getLowBits**(): `number`<br />\n  Gets the low 32 bits as a signed integer.\n\n* Long#**getLowBitsUnsigned**(): `number`<br />\n  Gets the low 32 bits as an unsigned integer.\n\n* Long#**getNumBitsAbs**(): `number`<br />\n  Gets the number of bits needed to represent the absolute value of this Long.\n\n* Long#**greaterThan**/**gt**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is greater than the specified's.\n\n* Long#**greaterThanOrEqual**/**gte**/**ge**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is greater than or equal the specified's.\n\n* Long#**isEven**(): `boolean`<br />\n  Tests if this Long's value is even.\n\n* Long#**isNegative**(): `boolean`<br />\n  Tests if this Long's value is negative.\n\n* Long#**isOdd**(): `boolean`<br />\n  Tests if this Long's value is odd.\n\n* Long#**isPositive**(): `boolean`<br />\n  Tests if this Long's value is positive.\n\n* Long#**isZero**/**eqz**(): `boolean`<br />\n  Tests if this Long's value equals zero.\n\n* Long#**lessThan**/**lt**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is less than the specified's.\n\n* Long#**lessThanOrEqual**/**lte**/**le**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value is less than or equal the specified's.\n\n* Long#**modulo**/**mod**/**rem**(divisor: `Long | number | string`): `Long`<br />\n  Returns this Long modulo the specified.\n\n* Long#**multiply**/**mul**(multiplier: `Long | number | string`): `Long`<br />\n  Returns the product of this and the specified Long.\n\n* Long#**negate**/**neg**(): `Long`<br />\n  Negates this Long's value.\n\n* Long#**not**(): `Long`<br />\n  Returns the bitwise NOT of this Long.\n\n* Long#**notEquals**/**neq**/**ne**(other: `Long | number | string`): `boolean`<br />\n  Tests if this Long's value differs from the specified's.\n\n* Long#**or**(other: `Long | number | string`): `Long`<br />\n  Returns the bitwise OR of this Long and the specified.\n\n* Long#**shiftLeft**/**shl**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits shifted to the left by the given amount.\n\n* Long#**shiftRight**/**shr**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits arithmetically shifted to the right by the given amount.\n\n* Long#**shiftRightUnsigned**/**shru**/**shr_u**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits logically shifted to the right by the given amount.\n\n* Long#**rotateLeft**/**rotl**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits rotated to the left by the given amount.\n\n* Long#**rotateRight**/**rotr**(numBits: `Long | number | string`): `Long`<br />\n  Returns this Long with bits rotated to the right by the given amount.\n\n* Long#**subtract**/**sub**(subtrahend: `Long | number | string`): `Long`<br />\n  Returns the difference of this and the specified Long.\n\n* Long#**toBytes**(le?: `boolean`): `number[]`<br />\n  Converts this Long to its byte representation.\n\n* Long#**toBytesLE**(): `number[]`<br />\n  Converts this Long to its little endian byte representation.\n\n* Long#**toBytesBE**(): `number[]`<br />\n  Converts this Long to its big endian byte representation.\n\n* Long#**toInt**(): `number`<br />\n  Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n\n* Long#**toNumber**(): `number`<br />\n  Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n\n* Long#**toSigned**(): `Long`<br />\n  Converts this Long to signed.\n\n* Long#**toString**(radix?: `number`): `string`<br />\n  Converts the Long to a string written in the specified radix.\n\n* Long#**toUnsigned**(): `Long`<br />\n  Converts this Long to unsigned.\n\n* Long#**xor**(other: `Long | number | string`): `Long`<br />\n  Returns the bitwise XOR of this Long and the given one.\n\nWebAssembly support\n-------------------\n\n[WebAssembly](http://webassembly.org) supports 64-bit integer arithmetic out of the box, hence a [tiny WebAssembly module](./src/wasm.wat) is used to compute operations like multiplication, division and remainder more efficiently (slow operations like division are around twice as fast), falling back to floating point based computations in JavaScript where WebAssembly is not yet supported, e.g., in older versions of node.\n\nBuilding\n--------\n\nTo build an UMD bundle to `dist/long.js`, run:\n\n```\n$> npm install\n$> npm run build\n```\n\nRunning the [tests](./tests):\n\n```\n$> npm test\n```\n", "readmeFilename": "README.md"}