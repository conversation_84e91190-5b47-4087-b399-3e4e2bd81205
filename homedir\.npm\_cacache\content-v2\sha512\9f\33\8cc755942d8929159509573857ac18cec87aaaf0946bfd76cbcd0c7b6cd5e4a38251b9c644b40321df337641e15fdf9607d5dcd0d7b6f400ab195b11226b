{"_id": "aws-sign2", "_rev": "9-76f4ba12bbbe2b3bec7c3421ab5eb4ab", "name": "aws-sign2", "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "dist-tags": {"latest": "0.7.0"}, "versions": {"0.4.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "aws-sign2", "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "version": "0.4.0", "repository": {"url": "https://github.com/mikeal/aws-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "_id": "aws-sign2@0.4.0", "dist": {"shasum": "09629bb86d1734d45ab31f39699c3c580759ba44", "tarball": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.4.0.tgz", "integrity": "sha512-e2aeIYt6y/EAMXNvRkI17fo87YVLNYPSUsHd+aH5AUiff9+2JBzvFHGEMt0Ko1EeeCKAxOfwmPbXS4oe03IBYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzPgjjkXDJv91a05UPDjxpOZ3yQ/8IuHKqHAzdSNLO4QIhAIhkBS7W8zcU0SZzY5uQHvzwY4unGWQGn/bo/LOScTnu"}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}]}, "0.5.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "aws-sign2", "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "version": "0.5.0", "repository": {"url": "https://github.com/mikeal/aws-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "_id": "aws-sign2@0.5.0", "dist": {"shasum": "c57103f7a17fc037f02d7c2e64b602ea223f7d63", "tarball": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.5.0.tgz", "integrity": "sha512-oqUX0DM5j7aPWPCnpWebiyNIj2wiNI87ZxnOMoGv0aE4TGlBy2N+5iWc6dQ/NOKZaBD2W6PVz8jtOGkWzSC5EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwVv/AKrEfHcop+IOfvRof9k+xC+BkVDhC33F9liS4OQIhAJt1bpAeOklQ0Xl8mNu/KdiEv0SKdUrWng55FE6uJHst"}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}]}, "0.6.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "aws-sign2", "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "version": "0.6.0", "repository": {"url": "git+https://github.com/mikeal/aws-sign.git"}, "license": "Apache-2.0", "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "8554bdb41268fa295eb1ee300f4adaa9f7f07fec", "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "homepage": "https://github.com/mikeal/aws-sign#readme", "_id": "aws-sign2@0.6.0", "scripts": {}, "_shasum": "14342dd38dbcc94d0e5b87d763cd63612c0e794f", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "14342dd38dbcc94d0e5b87d763cd63612c0e794f", "tarball": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz", "integrity": "sha512-JnJpAS0p9RmixkOvW2XwDxxzs1bd4/VAGIl6Q0EC5YOo+p+hqIhtDhn/nmFnB/xUNXbLkpE2mOjgVIBRKD4xYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCP59EXIRNLeMyKGCO0bZGuWbNLQTlip1qtGeqEvRyTHAIgNwGZIH1REbo8LOnJSG2K2zTuvD3JPoFEsNxGVR2TJDk="}]}}, "0.7.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "aws-sign2", "description": "AWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.", "version": "0.7.0", "repository": {"url": "git+https://github.com/mikeal/aws-sign.git"}, "license": "Apache-2.0", "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "a0cdf4b61f80ca669cd1ed8482f978d908f0dd2b", "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "homepage": "https://github.com/mikeal/aws-sign#readme", "_id": "aws-sign2@0.7.0", "scripts": {}, "_shasum": "b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8", "tarball": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfZCVXqqAsEjPXjykAEG1HvzLm2tqC/mee2t/Aam2IrwIgOVL//oc3h2py2/sUJp0faiHBN/xWRQF9Y4jpX/9S7iA="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/aws-sign2-0.7.0.tgz_1492024128801_0.28444291022606194"}}}, "readme": "aws-sign\n========\n\nAWS signing. Originally pulled from LearnBoost/knox, maintained as vendor in request, now a standalone module.\n", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T03:55:27.328Z", "created": "2013-12-04T19:33:27.496Z", "0.4.0": "2013-12-04T19:33:31.089Z", "0.5.0": "2013-12-04T19:33:56.519Z", "0.6.0": "2015-10-11T17:24:04.379Z", "0.7.0": "2017-04-12T19:08:50.725Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "repository": {"url": "git+https://github.com/mikeal/aws-sign.git"}, "homepage": "https://github.com/mikeal/aws-sign#readme", "bugs": {"url": "https://github.com/mikeal/aws-sign/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md"}