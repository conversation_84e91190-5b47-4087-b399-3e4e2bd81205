{"name": "fresh", "dist-tags": {"latest": "0.5.2", "next": "2.0.0"}, "versions": {"0.0.1": {"name": "fresh", "version": "0.0.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "f98a0a1b9001b6e227fb9c65ff3927bdb7b404fa", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.0.1.tgz", "integrity": "sha512-fb5BxtuLck23DPZpj2H3Ck8YUEo5su5UBmGDbs171kkYQz/7VZ8pz5ZovJBZOhn/5vYkD6TnyMk/eR1HYUXXjQ==", "signatures": [{"sig": "MEYCIQD8Bm6Iy7YB1j/q5hJDqn8qmqrTfA2IvQV0UMdx5Lie7wIhAPudyII9GZTquB0h7A7Dw/TRA66ConF4yMBtlAUDwKMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.0": {"name": "fresh", "version": "0.1.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "03e4b0178424e4c2d5d19a54d8814cdc97934850", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.1.0.tgz", "integrity": "sha512-ROG9M8tikYOuOJsvRBggh10WiQ/JebnldAwuCaQyFoiAUIE9XrYVnpznIjOQGZfCMzxzEBYHQr/LHJp3tcndzQ==", "signatures": [{"sig": "MEUCIQCUD6V6REKUxUEkpsFap9Uf+bFAsnHok5r+w7OWUodTPQIgbAC8KWJucKfjkEgBprd0zRW3HzJUhq1NT0CWfR+wNMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.2.0": {"name": "fresh", "version": "0.2.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "bfd9402cf3df12c4a4c310c79f99a3dde13d34a7", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.0.tgz", "integrity": "sha512-ckGdAuSRr1wBmnq7CsW7eU37DBwQxHx3vW8foJUIrF56rkOy8Osm6Fe8KSwemwyKejivKki7jVBgpBpBJexmrw==", "signatures": [{"sig": "MEYCIQDs0BvdNtg4JLARwcoBoESSOZW2Z/M8+WwCjgF3RaHVQwIhAID0dDzJrruaijtkkYEP4hcuKgj8upQsDRbXg/ZUqQqn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.1": {"name": "fresh", "version": "0.2.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "13cc0b1f53fe0e6fa6a70c18d52ce3c5c56be066", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.1.tgz", "integrity": "sha512-6xM6o6wNg8wlfSvwohtdUBhCOA4edV3UsObLD/SrG+ffCk4RxnnHysLRD77v4pb+FEf9OE/5BaVmleEl1GdWnQ==", "signatures": [{"sig": "MEUCIQDXUVtLQOuTOkeAu6i4ABAkJk8D7RApbRFh0asG5owgFQIgDKnBsqGO43MqKZieXO93735uekQXHqOKsGJa2DTlaug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.2": {"name": "fresh", "version": "0.2.2", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "9731dcf5678c7faeb44fb903c4f72df55187fa77", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.2.tgz", "integrity": "sha512-ZGGi8GROK//ijm2gB33sUuN9TjN1tC/dvG4Bt4j6IWrVGpMmudUBCxx+Ir7qePsdREfkpQC4FL8W0jeSOsgv1w==", "signatures": [{"sig": "MEUCIH3CiJCS0nRuWmk2s6QcBhRP+9WU9Sr+bfcyDuyVK5rZAiEA4loMJlBbU+htN02j2aANWsiBXuEWm6GhFezsbuCmBDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.3": {"name": "fresh", "version": "0.2.3", "devDependencies": {"mocha": "1", "should": "3", "istanbul": "0"}, "dist": {"shasum": "2db40d43bc63830f418519380879d6bedde2e845", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.3.tgz", "integrity": "sha512-bbmrimlze7/XywucgiYX1k0CGai+9mutJ7IbAsxKeJpgeEA8nB83IYN+JvVcLCWZ2B9J2rDYvIYLo01znAmzAw==", "signatures": [{"sig": "MEUCIC8Zc0ZRERh12jwIhdqnX6EE4cWLqY6V1f7vi+yA7ibzAiEA9btGiLX6ZqyGK0AMkyZCR0i3Y44VD234zgyxVNiF460=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.4": {"name": "fresh", "version": "0.2.4", "devDependencies": {"mocha": "1", "should": "3", "istanbul": "0"}, "dist": {"shasum": "3582499206c9723714190edd74b4604feb4a614c", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.2.4.tgz", "integrity": "sha512-mnBGgIFRNu54GtbkXy6+QKPYW/b5joAURorA8ELeJc/5BBNph6Go1NmHa9dt08ghFnhGuLenrUmNO8Za1CwEUQ==", "signatures": [{"sig": "MEYCIQDi5I/0m4r0kpijz77GRa+tjai7EynIs4/qx+nWcZyoHwIhAPCgK/KWi2yuDcX5oKcmRmxte5wFUDd6343eHocp4gIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.3.0": {"name": "fresh", "version": "0.3.0", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.9"}, "dist": {"shasum": "651f838e22424e7566de161d8358caa199f83d4f", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.3.0.tgz", "integrity": "sha512-akx5WBKAwMSg36qoHTuMMVncHWctlaDGslJASDYAhoLrzDUDCjZlOngNa/iC6lPm9aA0qk8pN5KnpmbJHSIIQQ==", "signatures": [{"sig": "MEYCIQConFkUNRclV3Fb1+hbqcK0N9Rd9T9871WB7nrPBbbGtAIhAL981HcpsuGUiwOo5lCr5AuDoZEE98EisanNRlwTR3xY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.4.0": {"name": "fresh", "version": "0.4.0", "devDependencies": {"mocha": "1.21.5", "eslint": "3.15.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "475626a934a8d3480b2101a1d6ecef7dafd7c553", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.4.0.tgz", "integrity": "sha512-bvbLUkH8QOn/QM8BBDVvgej8pIaT/gZLIEi9jwmuz2gMAX3cmEPZKzX2pTN8qPEgHxhG7dWNqwk22mZ1baq/xQ==", "signatures": [{"sig": "MEUCIFmZ743EPYMjyXguq3lkfLwp2PUxwSsar7nZrPQ2dHV7AiEAvltdMWqgnnABT5yGv0idg6HARO2w5c9DOLxutLaoR1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.5.0": {"name": "fresh", "version": "0.5.0", "devDependencies": {"mocha": "1.21.5", "eslint": "3.16.0", "istanbul": "0.4.5", "eslint-plugin-promise": "3.4.2", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "f474ca5e6a9246d6fd8e0953cfa9b9c805afa78e", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.5.0.tgz", "integrity": "sha512-FveevZlqNSIBombLEB9L3WyWg74r6d0sk+unzqKmt33b5egY0YXDFWIQ/t2Fzr9mgBLL0BlL0/rdg8Oq1C0SVw==", "signatures": [{"sig": "MEYCIQDBjDXFwn0RiePXdjRTl1QRukYmyPHpPLWKCmDr+snNOwIhAISugPozjB/i+NU0Tkr11WUPwFBiXV06y1syrMDuhAht", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.5.1": {"name": "fresh", "version": "0.5.1", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "c3a08bcec0fcdcc223edf3b23eb327f1f9fcbf5c", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.5.1.tgz", "integrity": "sha512-itI/dEMy9O50obvdroLCBDD/83GzxqZLV673itn56Z4JdcgQd4T98Zb2XfbGVHFfNmKToPVP44wYzEA31BVr7w==", "signatures": [{"sig": "MEUCIQDRpwqWppMR3mtK4TNHpqNpy7dHYZJGhQfwutQ7Y1bj9gIgOrP9N0BGFMx0SjZu9dC3Sbul4owAD1U3ftKAVS7iVbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.5.2": {"name": "fresh", "version": "0.5.2", "devDependencies": {"mocha": "1.21.5", "eslint": "3.19.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "3d8cadd90d976569fa835ab1f8e4b23a105605a7", "tarball": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "signatures": [{"sig": "MEYCIQDCqawaTDV3DedLtJ76gUv97Ea9YgeHNYTmAFl9p+GbRQIhAK8JiMCdSkiq/Ld6Hr+0E8jDjv8T558uA98rBIG+Of/N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.0.0": {"name": "fresh", "version": "1.0.0", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "8.12.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "6.0.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "95336eef8f1419546bec657ebaa3f8cef48d3b8b", "tarball": "https://registry.npmjs.org/fresh/-/fresh-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-CXbd7Pq2ZU4uK2K8pIsduMqFH27c6DhruWq42tmJjQ5a3ouq1pZNjdSOaYuT2Vx2UpU6st5Cx9ye0GWnnMMnSg==", "signatures": [{"sig": "MEUCIFThuI+qvqDWJ+wwTWNilFQ/3nXQRmqBDmCn+YK9xYsCAiEA6+M0/xQ5zCNtskTwZMuLzKLzW2HMCylIffO1GZ3u7BA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10226}, "engines": {"node": ">= 0.8"}}, "2.0.0": {"name": "fresh", "version": "2.0.0", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "8.12.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "6.0.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "8dd7df6a1b3a1b3a5cf186c05a5dd267622635a4", "tarball": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "signatures": [{"sig": "MEYCIQCBFJPoFuukcOo1fzNYCDNO+SDzgPf0eKjqmYjOYyNzkwIhAPOR+Beygb9z6PiclNlPz2vyI3RnokWj9gPVk0RvZa0f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10289}, "engines": {"node": ">= 0.8"}}}, "modified": "2025-05-14T14:56:19.208Z"}