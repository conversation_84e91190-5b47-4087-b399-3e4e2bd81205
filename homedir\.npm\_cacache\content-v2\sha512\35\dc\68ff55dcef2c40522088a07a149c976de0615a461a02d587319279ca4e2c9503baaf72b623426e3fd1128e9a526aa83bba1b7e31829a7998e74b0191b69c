{"_id": "pkg-dir", "_rev": "31-813b42fc191f2a33ce61ab1e265301fe", "name": "pkg-dir", "dist-tags": {"latest": "9.0.0"}, "versions": {"1.0.0": {"name": "pkg-dir", "version": "1.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-1.0.0.tgz", "integrity": "sha512-c6pv3OE78mcZ92ckebVDqg0aWSoKhOTbwCV6qbCWMk546mAL9pZln0+QsN/yQ7fkucd4+yJPLrCBXNt8Ruk+Eg==", "signatures": [{"sig": "MEUCIFghpTap4AVwAQ1ifmHGcPk9ekAPvx/PfINWWmk+JncCAiEArw0APt7kHGYyhSbB44YI2lMUobsv1ny/S7j/hM+r11Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4", "engines": {"node": ">=0.10.0"}, "gitHead": "a06a60b1a2cddf2f84912221477311327398da01", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/pkg-dir", "type": "git"}, "_npmVersion": "2.11.3", "description": "Find the root directory of a npm package", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"find-up": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}}, "2.0.0": {"name": "pkg-dir", "version": "2.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "f6d5d1109e19d63edf428e0bd57e12777615334b", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-2.0.0.tgz", "integrity": "sha512-ojakdnUgL5pzJYWw2AIDEupaQCX5OPbM688ZevubICjdIX01PRSYKqm33fJoCOJBRseYCTUlQRnBNX+Pchaejw==", "signatures": [{"sig": "MEUCIQD07AqoWwO5ekdpK0KsHUG+kGsNdn+Whpeb75HPEdo8bwIgJw+MV6tba/xGC38Hp9UXLawtQYIQxikrUyvBei2v6/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "f6d5d1109e19d63edf428e0bd57e12777615334b", "engines": {"node": ">=4"}, "gitHead": "c18df2945cd928787f3290042a6c69352b4e13a5", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"find-up": "^2.1.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir-2.0.0.tgz_1493912348467_0.855934705818072", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "pkg-dir", "version": "3.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "2749020f239ed990881b1f71210d51eb6523bea3", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==", "signatures": [{"sig": "MEYCIQCZG7yrQpban0qWH+Mi+KJlfhF2JXg11ahx7xVhJ3KTMQIhANCV7W2Gn9Y1FqBqN96a9f1rnU46939JWaAWD82cJbfT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJ5rHCRA9TVsSAnZWagAAdbIQAKIgZ2BqfviuK3Pg7GWM\nv12xcztijs0OdD/Gaf5yAvI9ixCAMkSTmzuRB3VHBG5NbGf8/2rB2dX39SWM\nEPL01r0I8zxvZs/N+nUVGT1TA1O6Y2gYFT6LMDEdCBVunKy9Sbe6IpwJOAHz\nRuSDCz08e9VqwGhmmwJIEZPwW+UwymjqlOsq14DkP7nw96ZtVkJ9xWd38d4M\nEyDGnKzCpPnNuZs5kuPzt7tbLCOlqepe/HmH+zIuxt7UiRvtzZxYTc7Na90Y\niTJIneWW/TkMsgfAjpxbcs6sdbZhlvmpKKRZN3Q3YnaXjugwcJA8WlbJPe38\n911s8inJ57QhIbzI6CotAEHgJWuw6e532v4rSxdauU72f11ZGcNu4dna8ICg\nbteYEHLgNVZdLTBRbhn731/kPymmdwqhxAK3CDacj9slWMDGdArWmWh5SlLr\nduxllM82kRUr544f7Pl1WxHrCJZBVzXNCOLoOJF6b1uI0u081SGjnKWt+w7F\nMYRPkkP5O3rRQ4zxNZV/7q/Tsrzu8hplQA2nPDQ3i1Jny88g7DhKob5PZGyw\nor7E5SvJq6KR3wTxNq4E+hkmtm6IzLwsaluTWXC8NdNd+7JbIFBsL5uxm/Li\n6G1rkF+tk5cBFguEBE5rwyx5jDa9o9AdqA6x6CjvACh5+7c9vvcsSbL4OlOp\n0Ija\r\n=n5cf\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js"], "engines": {"node": ">=6"}, "gitHead": "bf7429aa96375d76455826a333f1f7f4da150805", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"find-up": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_3.0.0_1529322182903_0.9901723746601805", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "pkg-dir", "version": "4.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "1c505f82509ec0165a1fa530892585852e85ce9e", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-1S63UXE5ujsNUTFEgI001K06PkKqNceY07OBdRw24KYs3xvtoG3bkkunIG1p3glRKUjrZYc43opq2Y70R+/Rbw==", "signatures": [{"sig": "MEUCID55eFFo2UoMZGW0BU7B0NxgeED4s2RTa+TF61c0HkFbAiEAhWvI0R+anvejhA4eL3q/cWr+FAcayJsFPozOJwSytQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4221, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch3OVCRA9TVsSAnZWagAAsxAQAKMZbTJT5CuC6U7cdm85\nDTvd/ENSieYsuj8zPTd9O0wRh7Bc9AC18VquPVftu+R1plOHPUQJije07mTt\n3mURCLomZqt4K4kKg6vu9UFZEhDhPidY641pfQsOr5U8grsGWS3MMnICB3gu\nlI6IRKalgwUKvJ4xPcKU6WXYHg6+wnyrjA9qOLiv6jMqwY/n6sMy81E5rUkh\nuO4IKOEB+qPGX7QYsFYsvMGEzUYb2vNDTVduKerVOcgy6lb8T807vHF6lla4\nTb/gFxqugphgXqHe2dFgrDt0eVVQTi4xwVAiLD3JuS79oaP0ewrxGy+tPn/I\nkyPxhN80/eFryWQfQXRpSKUC+WqQI4apriu+wXLxk5lNsYoLSTD2sEJiqCs4\nadCHgzfWAIy8ORsWPh3mjMJuP7GlWbPo1JChQCmmGUgfjpGy0TZSHgmjOI5W\ncarEnYjTaBl/PccfT2pZj32FrzZkJWWG/ph2YqH63aNVzHaJGq7HRfb5Bdi9\n/RwaAAvq03KYOKkEH8ejEU0p93XhgAjtGMihOIW/Xu9GGb0yZu+HO5FHPA8b\nM6rUazvbGJaPF2BekvNM11IPOap6QoPP/4ArNG0sGZFnr2rtBO4taa7dvT85\n9QME8cqbL71DvLatbsKZcosOiw+ltU4s4J81c3hZV1+uQozXCDBoyNTDrPDL\nmgHa\r\n=029U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "2a72e4ece93b2443a2827aeae87f4995bb9e2977", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"find-up": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.3.1", "tsd-check": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_4.0.0_1552380821273_0.15593856451506727", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "pkg-dir", "version": "4.1.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "aaeb91c0d3b9c4f74a44ad849f4de34781ae01de", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-55k9QN4saZ8q518lE6EFgYiu95u3BWkSajCifhdQjvLvmr8IpnRbhI+UGpWJQfa0KzDguHeeWT1ccO1PmkOi3A==", "signatures": [{"sig": "MEUCIQDL+WgI1uq5xkdch+6YqXk9xCw1bpsMuTp9yLUvN3KfAgIgVmi0hEFCmzy44TjLuNLvj/WKhNaifBfQQgHktgH2Jfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpvbXCRA9TVsSAnZWagAA1pAQAI721cM6BaZ4wyuOJ0gE\ngKAPud/NCrGmyivJrcrP4YfMLse1DnIfN67iUv2E/GZ+ujN7X4HJnrrzGWBR\nqlYXkBnZT1RlXsuTPTs4R6hu+chdhZzqMbexlvE4nutQ4wjbqoLE40YaWRzD\nVF7/81B6ZMP3tbcEUQIWmFc+KrtJFZfyNq+A4OYeyVMW2J70r3HCOb1QMJSz\npuBR9ZzLr6cEKe0Mz2IqDeaGHi+CbU7XUZeeidP/iqEarti6KoLtW5l7UfUB\njBsu4a2Y2XqMxQ+JjgtuqhizBtezfbfmfL+adamiPt61rLgj5F1Jml54/bMr\nTpDcxB+cFF+IfGx4+vXw1J8ZqsMi56d2gA8TvwVwa6JTeYJs230t/C3vEI8Z\npd2f0TwDQEAhajQY0Z2LeTe1DQL80oTSlrWUq+6Wkr9XPkR0lL5adFXYZ8qB\ntoUCU5k5H98+tTsTvuXmHO6fZ8JS1nN1nNPdr0pWByaiRiRcyuQoY0nFcMKp\nWdGVyGDtPj9KCzC37QFzpmT4CaAcU8ugQTuKpzMNgNKm+cxzjfswy6UMpwmG\nx6tRXdqaVaW6WtZcIWgTWgequF9cXGrSWtm0TrH4hmHQpr6GgRunqRZ1nQra\niav/vrzAo1zfN4SaBrjClyYbGp5zIY6kjBXrMnSFT8k7L9ZyIoQrmThYXEpY\nZ2C7\r\n=Twve\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "abe3546fab603463012234bfed768243a074e8d1", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"find-up": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_4.1.0_1554446039149_0.43123501399168696", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "pkg-dir", "version": "4.2.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@4.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "f099133df7ede422e81d1d8448270eeb3e4261f3", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "fileCount": 5, "integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==", "signatures": [{"sig": "MEUCIQDbhlFYgOqRJM+1qw0sJki9cMOoVeSunCsbZV5ECACwBAIgFtEYYYJnLahB3aUcJLX3fcs42H7Npx6QOqCEFS4wDi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc48Z0CRA9TVsSAnZWagAA9d4QAIA8ki5bJw6PfCFYHATl\n1c7I/rJSZzR+iRY3lQjGcJJ8g5A1aQFcleviJi5dd+wghHbgvtD0ma9OhtTu\n9+bc3IH/46lUhHG5bjl4vWJTfMB1/p3WLe7CHvDJROhbcBp70E3AWE+iGrOR\ng7FpasTIPpbsveGma66ioF2K3aeybee0bklbDsWsuhS38taDCVOPM854yxk5\n2anz7zOaQALYuzfxvMmB7NFYIgPHO5WP8fDqcg1dpVeco97Un9c2E4OnWZfT\nLmpQC8NFvPFYbrypBa6l/jZMvO6+R1YpJd7x6kp02KUPYsd3KPWtvcrjiSE2\nibK6kTXq6ScSFW+uxhBspzK4kFewLc41h9of4bRBFh/dlb4AYFq8voMhcPhS\nQAewS3YpA8V6bwZZQJT+QSchRq4EkLZLGQCtCnXv27JgBBg2TZ3I/ay/rySE\n3Iw10ex72h79mJDyHohPw12l+mBxtNbo6pFx5XdBhjKl/32IzVXu6RWW2lsi\nYG0Ijt9WVD9r4z8FBnLP4zXtcht36YKNwHxj6WSZ9s6VVYy2yLlK+XHCw1Jw\nC/td4HlG/4oMem2GhMvwkcnvBzYKTrLlpqbK+fU2KVlka2PMemcoR51jJ36N\nMBZF2L6L+riT9n5hOtFGGSZao+N062mdCmR0Us3UINDvznq1kxx8D3U5XT8b\nSsUm\r\n=s+NZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "e25e5342c5ec425bb4a2c93787a880c29cbf0a49", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"find-up": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "tempy": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_4.2.0_1558431348399_0.7942128019820929", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "pkg-dir", "version": "5.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "a02d6aebe6ba133a928f74aec20bafdfe6b8e760", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==", "signatures": [{"sig": "MEUCICIRkDRSgWWRKPD62NnL3s1oCkhCHJCXpXjDr0EcIXuGAiEAkr3IYSY2coGlALvp+NsShbfSOBCqyCXEIR6usMbmS/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfibRdCRA9TVsSAnZWagAAbKUP/2VnhYPFPl4hP4i/5TVz\nrG4SbfGLrW/lntBdUktuzjRrFbdwOOb2sAQ3ThNeLQT/9mluWNm5dNAooxUE\nkfZ5RgMK/ypY4GVFBixJ88m8Ha14FJgQx/jUaVT76/L3BfbtojvOi1cF/mrG\nl7eW4pgris1GzU4MPPjqAYYGA8/fdOVOOsjAAy4C7u71qqhmPsQw+ghwwGNq\n963Xs856+xM+xO+q7caHToyFOTrIluz2O+o15i/GxwlJywBLyO1BIYfpiuci\nWbLtRmqlobxNBVKG9yECIHuup3HLS/Kmq5RaBOV2CmOnsbpaxI8CwhTHHUcO\nHh6+cn/hPQ49yllwuZhb1d3riOZwiPNHBF6iXT0vAVCuGRMad/JwqosHZynh\nYRUSWuDb0tVAhGv8Z2GZbyhs5k0MKrGPX6Ehtbkx5O5WfrAI8ITOXquRUn+D\nAN9ZqCebr3vDOXz+bDgL/zLY41pxXRZyrjmm2hJuZUIUswaG35T5a+RG/eT1\nJaLBBeWFypCwmIQ+Xdeg+qpoMi7qrgt433qoEk40Y+CKZ5uTltcoLvyfbEHK\nUID0k6bqBD3TZQH2BtsmxIUUvKm843Ee/injmitgtl4IbzF7fXdXdNcClH1m\nj6pBjB4PtiKUXqg3mWBhQ8J3d1VcVB62vZYE6VddtYNpiahxnWVQr7KwmGsU\n7yKq\r\n=T07Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "db14a380461b9dcce58bb3845df7d463ffd67628", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "14.11.0", "dependencies": {"find-up": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.33.1", "ava": "^2.4.0", "tsd": "^0.13.1", "tempy": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_5.0.0_1602860124497_0.1466003740553623", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "pkg-dir", "version": "6.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "f78318fe7bd9f1b2915402d6b9e0b90ab45daa8d", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-x5<PERSON><PERSON>ouPc1VcyhD6MI5OvxjQQ2UhKVFzs6tg0wWnEszRTLdoZwh3f8aVxogsG5HXJ9eloRUzmxSdxVtGHYTyW5A==", "signatures": [{"sig": "MEQCIDdxp6DMbMwiIF3goJ4Kvv6JwGzGhDE0PTBz5lUO4eqpAiAmzStGEmxN9R5Qyyja/dVNPYwH0Pa8yEte4gW5TUQcqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5326}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "a1006dbd2133b0b21a4fcd90cba12c73cc0c1c6a", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"find-up": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "tempy": "^2.0.0", "typescript": "^4.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_6.0.0_1633154152273_0.9866145156184172", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "pkg-dir", "version": "6.0.1", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@6.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "8ec964cecaef98a2bdb9c164733f90a5bcd2352d", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-C9R+PTCKGA32HG0n5I4JMYkdLL58ZpayVuncQHQrGeKa8o26A4o2x0u6BKekHG+Au0jv5ZW7Xfq1Cj6lm9Ag4w==", "signatures": [{"sig": "MEUCID6xUSTbeMF/JEM+TfE3OhHq1N6PAHq3N2V/AhyRxkNzAiEAkEsVBZ6QkLrCkx1k9zCHHkCoWF30dG034RVkB2xiV5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh22t0CRA9TVsSAnZWagAAsekP/RsYI2k/mGt4SaedaMD9\n/oI5oxxaY5gPeV3MYW+sO5h7WZqikLaEgl3h6spndB+afNhwA612H2GjwaLh\ngGFy0429YLLyORAhpCeUyO+4TQMVKIQyX9eD7gITlSY3IO392c+hgpqL3wKC\nvVptMOSSmredrtg/4hRinLehrwkEvPmfEOfKXcEcpeuMgv4KGzFcpaJV5JYp\n5rZtizaZGABNlfloglAimLqmf/PLdZfuYciKwUDRUIYV8p/rxWc3Z38L49k/\nGxDELUpmwzYPuuQBndZGbeouI6RnD+zgvAXySrUix9zyVjakHtzmszX6eKBl\nrm85nEXz0FmH6YmEjbezxkh1FF/kPX2NMe2TY3oNd4dW2acRoeaIOcy8FSwQ\n4SafP8/607kLB4Db0s+o1lt1yEvksWcqU5Svd2y04GartmaK3jhDteH0aX81\nEFu+vCcXDw3L0dclSRBJLr2zguoX/rkxDUQ4tDeB9VbeV3ZNhc7P03nJyuB7\nu7mrzR6gJOVv+gAJIBIl9+229jKShbVhSFA2De+Wj1ihv7WaU4orQZwE9L7M\nn8MLxVm3kueIkNutFTubS4X/8/1qSDvJE3xquo593NGUZVDbCk4/vVM2UV8v\nB1cvEPFUSuTYZvk8a3Ygs5mxtnQwnwZO0gQ1AM9ZspPKdPkdGmhVioTNBvpN\nsEQo\r\n=K4M9\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "7fac1fab1fd7eca9842f1bab8d7bf9ca1210f943", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"find-up": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "tempy": "^2.0.0", "typescript": "^4.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_6.0.1_1633498057211_0.02612335675989219", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "pkg-dir", "version": "7.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "8f0c08d6df4476756c5ff29b3282d0bab7517d11", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-Ie9z/WINcxxLp27BKOCHGde4ITq9UklYKDzVo1nhk5sqGEXU3FpkwP5GM2voTGJkGd9B3Otl+Q4uwSOeSUtOBA==", "signatures": [{"sig": "MEYCIQDpZFZLRe+knPXw2N2B5bX3OZs+mFBazHHHyDo8pho2YgIhALRKYG3JWD2EWp4fmSV5rAfJ69+ZaDteN4/wm/9QxgCW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+9lSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCvRAAmndoFeP7GGnZR2WF6prsGk1OjWvDn0NJlVm8uiz9pieBim8z\r\nuwQY3CCPqobO1Z49kVPW4P1DE9kse4EDu0cOT/vBuLEI4KHfWidz+Y1iPg2U\r\nCbyMM/07L9D7w70aW21dCTdKMQEg7VOaZOP1uAfHSTf1uoVbS1eJZ5FiQgHF\r\nm80q8CPWX7q1HBGF6ZubVYXQCa4OtodBM63cN8FNx5r6FUxodSY/qBvjZvhd\r\nmwWYYDU/1pSVzNX2Wemg5xR4k4hFxaN7p0gi2OkkQFfoBZRiiUIyvB+OpDSi\r\npS+q3rPOcNOoX0Vp7PZkZQf40Dng5y/NwxDHLAqQzv7X9G9LcJFNGx4gl3tW\r\n40Lou3AGNjPK8vJhWBg661pdYiSSeq9vy3vvwZ4ULrgPg/xbpTMWnHrgGSTO\r\nuaqwmYVcwV7DQUCvV/JGeRkhU0cZx49huNrWHVpFlZR6Z4NGaVJTrEDLGN7F\r\nPOFtua9EXLtQHhy/h6+3/nnxvArHixGiavv+3h0Jcjv8yP9MipMJjTiUdtPB\r\n7ytyiGfmmJ1WWWhUR567/B56++c4BPBUzP+fscKkqeWe7VTNCDZ357YCSaiE\r\nIFpnaokVfymBXwqL9VMIZSdSIMw92fstXCCvcJR6UjTGTaZSvEwr8zGJUlDF\r\nw00sYIM1k8jflG2Z5/E8OOM+3EOx7p2WTMg=\r\n=p1W3\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "7dfd36be6a92788c1b5ee901ff13335fa7523342", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"find-up": "^6.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.1", "tsd": "^0.22.0", "tempy": "^3.0.0", "typescript": "^4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_7.0.0_1660672338547_0.3448763788914968", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "pkg-dir", "version": "8.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@8.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "8f3de8ba83d46b72a05c80bfd4e579f060fa91e2", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-4peoBq4Wks0riS0z8741NVv+/8IiTvqnZAr8QGgtdifrtpdXbNw/FxRS1l6NFqm4EMzuS0EDqNNx4XGaz8cuyQ==", "signatures": [{"sig": "MEUCIQCVb1v0Z8StenXgcHezf9YXkJumDJ03y2ZSUbfEq8Xl4wIgZCG6uavtsbG8lcMQeKshRQQckw3qvd6rbxzRfigZssE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4930}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "fe0b0fbe45a2b3bd92961cbc586d8fde90e58e01", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"find-up-simple": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0", "tempy": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_8.0.0_1699041767277_0.41695641745763456", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "pkg-dir", "version": "9.0.0", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pkg-dir@9.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "dist": {"shasum": "79d0ca53023fa93f848ba960d75df4476e1fbb1f", "tarball": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-9.0.0.tgz", "fileCount": 5, "integrity": "sha512-BMmxOuokumBehLGYwCKMBJxpAGSBbvGTtDn1yUV9UpCAvC4F7UWHoPTGSDGPV8eQPkYq/G8b0g1W5vIc5K/EhA==", "signatures": [{"sig": "MEQCIAdOJVWBiF1ATB2EV23naBkaW38NPL/Ey6EQWvDJiNxaAiBr1tYoR3PSyIlGI+UQv+84HA+hk60B72N5xWIDA9zuXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4930}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "fe0b0fbe45a2b3bd92961cbc586d8fde90e58e01", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Find the root directory of a Node.js project or npm package", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"find-up-simple": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0", "tempy": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pkg-dir_9.0.0_1749148418202_0.4057696963948574", "host": "s3://npm-registry-packages-npm-production"}, "deprecated": "Renamed to `package-directory`."}}, "time": {"created": "2015-09-01T07:59:42.497Z", "modified": "2025-06-05T18:34:06.155Z", "1.0.0": "2015-09-01T07:59:42.497Z", "2.0.0": "2017-05-04T15:39:08.692Z", "3.0.0": "2018-06-18T11:43:02.957Z", "4.0.0": "2019-03-12T08:53:41.396Z", "4.1.0": "2019-04-05T06:33:59.286Z", "4.2.0": "2019-05-21T09:35:48.505Z", "5.0.0": "2020-10-16T14:55:24.632Z", "6.0.0": "2021-10-02T05:55:52.486Z", "6.0.1": "2021-10-06T05:27:37.375Z", "7.0.0": "2022-08-16T17:52:18.743Z", "8.0.0": "2023-11-03T20:02:47.479Z", "9.0.0": "2025-06-05T18:33:38.380Z"}, "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "repository": {"url": "git+https://github.com/sindresorhus/pkg-dir.git", "type": "git"}, "description": "Find the root directory of a Node.js project or npm package", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# pkg-dir\n\n> Find the root directory of a Node.js project or npm package\n\n## Install\n\n```sh\nnpm install pkg-dir\n```\n\n## Usage\n\n```\n/\n└── Users\n    └── sindresorhus\n        └── foo\n            ├── package.json\n            └── bar\n                ├── baz\n                └── example.js\n```\n\n```js\n// example.js\nimport {packageDirectory} from 'pkg-dir';\n\nconsole.log(await packageDirectory());\n//=> '/Users/<USER>/foo'\n```\n\n## API\n\n### packageDirectory(option?)\n\nReturns a `Promise` for either the project root path or `undefined` if it could not be found.\n\n### packageDirectorySync(options?)\n\nReturns the project root path or `undefined` if it could not be found.\n\n#### options\n\nType: `object`\n\n##### cwd\n\nType: `string`\\\nDefault: `process.cwd()`\n\nThe directory to start searching from.\n\n## Related\n\n- [pkg-dir-cli](https://github.com/sindresorhus/pkg-dir-cli) - CLI for this module\n- [pkg-up](https://github.com/sindresorhus/pkg-up) - Find the closest package.json file\n- [find-up](https://github.com/sindresorhus/find-up) - Find a file by walking up parent directories\n", "readmeFilename": "readme.md", "users": {"dwqs": true, "alexreg": true, "drewigg": true, "alexreg90": true, "rocket0191": true, "ahmed-dinar": true, "flumpus-dev": true, "hugojosefson": true, "recursion_excursion": true}}