{"_id": "nanoid", "_rev": "140-55199672a8d0d1089cbbf0377ac9cd6e", "name": "nanoid", "dist-tags": {"latest": "5.1.5"}, "versions": {"0.1.0": {"name": "nanoid", "version": "0.1.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@0.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "4365edaffed98a9858b5f0e0f8e884c93617c06c", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-0.1.0.tgz", "integrity": "sha512-chTeMaVZKcsxQ1el3SrJM9lf368QPkD7AWkCxdNkcE4lUxoO0b8NYuUQdgWu9ntN5JluI5jCvYvkrIHe2cIz/g==", "signatures": [{"sig": "MEQCIB9RjNt+PfjSUpWppNcwX+nqQ23ht9upx0KjhblAOBhQAiBv2Hkp0b1xSTWfWNyxyfLi2NGaoHUGjM/ANlq+bboH4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "007a4c4b0a65ca669ff961aed9710918765582eb", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && yarn spellcheck && size-limit", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "260 B"}], "_npmVersion": "5.3.0", "description": "Very small secure URL-friendly unique ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.2.1", "eslintConfig": {"extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^20.0.4", "jsdoc": "^3.5.4", "eslint": "^4.4.0", "docdash": "^0.4.0", "pre-commit": "^1.2.2", "size-limit": "^0.8.0", "lint-staged": "^4.0.2", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "webpack-dev-server": "^2.6.1", "eslint-config-logux": "^16.0.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-0.1.0.tgz_1501995839938_0.648109751753509", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "nanoid", "version": "0.1.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@0.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "90ef5cd03bc3ae70108ba477bec09f76e20a6304", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-0.1.1.tgz", "integrity": "sha512-y1H6Dt6aWoMQVKZfcFdGLv7UwmoL/yaQSNrfguNNgs4E1OU5+cfNtGhv0HsV1vmAFunwEzupOu4dwJ2c/tevmA==", "signatures": [{"sig": "MEUCIDsrbO2ui4DL+JjcywcPijDsM+W2UdrdghcWW1759A1hAiEAo61egcJst5ndLQu3e2UZyFkJ/Lv3YchS/6gnri6wmHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "6ad7facdc41fc3b9afa7b484fb85c0f42684ccac", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && yarn spellcheck && size-limit", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "246 B"}], "_npmVersion": "5.3.0", "description": "A tiny, secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.2.1", "eslintConfig": {"extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^20.0.4", "jsdoc": "^3.5.4", "eslint": "^4.4.1", "docdash": "^0.4.0", "pre-commit": "^1.2.2", "size-limit": "^0.8.1", "lint-staged": "^4.0.3", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "webpack-dev-server": "^2.7.1", "eslint-config-logux": "^16.0.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-0.1.1.tgz_1502254256002_0.3879985061939806", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "nanoid", "version": "0.2.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@0.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "ee6f261ad53b2f44e7820105df35a2a985bab405", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-0.2.0.tgz", "integrity": "sha512-sMV5Hnc7v2xsqA6uNgrcc52At9CGW0+VQv/BMNaln2OTe2Ka1DdGDD2i7hf1rSiKU6P6xDZEuoqlWpP102Jaeg==", "signatures": [{"sig": "MEUCIQCcpQISWtFwrzkY3q0SVJZOj4yETy65xCdExxaQoYAL1QIgf6CudOa3l/1myPEjFlFVZkRgXR8MHB94AhWMKzK6dfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "ef1d664b52fb0cd978371c9601658f09c84c88cc", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && yarn spellcheck && size-limit", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "181 B"}, {"path": "generate.js", "limit": "197 B"}], "_npmVersion": "5.3.0", "description": "A tiny, secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.2.1", "eslintConfig": {"extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^20.0.4", "uuid": "^3.1.0", "chalk": "^2.1.0", "jsdoc": "^3.5.4", "eslint": "^4.4.1", "docdash": "^0.4.0", "shortid": "^2.2.8", "benchmark": "^2.1.4", "microtime": "^2.1.6", "pre-commit": "^1.2.2", "size-limit": "^0.8.2", "lint-staged": "^4.0.3", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "webpack-dev-server": "^2.7.1", "eslint-config-logux": "^16.0.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-0.2.0.tgz_1502344762209_0.44672807259485126", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "nanoid", "version": "0.2.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@0.2.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "0bc1446ddf73011893cb09103e65783272028ff9", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-0.2.1.tgz", "integrity": "sha512-iHYEGJDkf7BGhlwkxP4v8Xt0lmXuyKJZoIyPFVUgBH7DwU0z9BHMhJrm75TVMssow4CouYBvYMfgQyq5qnmvqQ==", "signatures": [{"sig": "MEUCIEJ9+OrOJVFsCPNkUXxL4tt8ZDNU1eiIsIr4TqglppxgAiEA8OlFVL2Z/lHykThkGTibFHou2BP0EYZ8wKvNrYwy8Ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "c4e0831990c08abd57c09237502797ca6a0ea2f4", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "179 B"}, {"path": "generate.js", "limit": "196 B"}], "_npmVersion": "5.3.0", "description": "A tiny (179 bytes), secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.5.0", "eslintConfig": {"extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^21.1.0", "uuid": "^3.1.0", "chalk": "^2.1.0", "jsdoc": "^3.5.5", "eslint": "^4.7.0", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "benchmark": "^2.1.4", "microtime": "^2.1.6", "pre-commit": "^1.2.2", "size-limit": "^0.11.3", "lint-staged": "^4.2.1", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.1.0", "eslint-plugin-node": "^5.1.1", "webpack-dev-server": "^2.8.2", "eslint-config-logux": "^16.2.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-0.2.1.tgz_1505575670705_0.010530935367569327", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "nanoid", "version": "0.2.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@0.2.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "e2ebc6ad3db5e0454fd8124d30ca39b06555fe56", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-0.2.2.tgz", "integrity": "sha512-GHoRrvNEKiwdkwQ/enKL8AhQkkrBC/2KxMZkDvQzp8OtkpX8ZAmoYJWFVl7l8F2+HcEJUfdg21Ab2wXXfrvACQ==", "signatures": [{"sig": "MEYCIQCTNdPARewCF0OspV4TUd8uZEKqMJ+K6v6FqI/3jf1BBQIhAL1nlAIG1Zzz+bJ7F/n8uJqVK8femg5CcoAvcld7TAo1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "fbe56a27637068a64648290f48fc5aa1093ed525", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "179 B"}, {"path": "generate.js", "limit": "184 B"}], "_npmVersion": "5.3.0", "description": "A tiny (179 bytes), secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.6.0", "eslintConfig": {"extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^21.2.1", "uuid": "^3.1.0", "chalk": "^2.1.0", "jsdoc": "^3.5.5", "eslint": "^4.8.0", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "benchmark": "^2.1.4", "microtime": "^2.1.6", "pre-commit": "^1.2.2", "size-limit": "^0.11.6", "lint-staged": "^4.2.3", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.0", "webpack-dev-server": "^2.9.1", "eslint-config-logux": "^16.2.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-0.2.2.tgz_1507667153363_0.8696998821105808", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "nanoid", "version": "1.0.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "fd6c3d8c576ed6f7e6a45152c996ae3f5e7dfe36", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.0.tgz", "integrity": "sha512-eKG3dxtubAM8t+QgB7IAiBmSvE1AZ97JOUy35Xx1H66HzQY6EoWGgSMHckRal9uwsb4b5rzxmenAI7BLGCeB4g==", "signatures": [{"sig": "MEUCIGrwWK2pZrm0v0oanUPS0V/vb+4JsBX8ZebkIj9oukPSAiEAnnQN25HLdeQj0BXhQKcWjWGC0UrkoKMwFpZoZCz0UJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "ecf4bab4c8400545a8a3be6e430a6437e56194c3", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "179 B"}, {"path": "generate.js", "limit": "184 B"}], "_npmVersion": "5.4.2", "description": "A tiny (179 bytes), secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.7.0", "eslintConfig": {"extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^21.2.1", "uuid": "^3.1.0", "chalk": "^2.2.0", "jsdoc": "^3.5.5", "eslint": "^4.9.0", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "benchmark": "^2.1.4", "microtime": "^2.1.6", "pre-commit": "^1.2.2", "size-limit": "^0.11.6", "lint-staged": "^4.3.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.0", "webpack-dev-server": "^2.9.3", "eslint-config-logux": "^16.2.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-1.0.0.tgz_1508695899123_0.9389986684545875", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "nanoid", "version": "1.0.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "58a8e0577d9db07ae4f231a3afa59af94e4fc439", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.1.tgz", "integrity": "sha512-BXapDjcA0QjUPLBqcC5EcvwB8LMOY7zXf3UqDGp93R73PoMOfipmRVxpTogAhtqViE/pJzP9bS+jGK31rzkE2w==", "signatures": [{"sig": "MEUCICjsNC9sp+Cgiifi0jDmo5byWdock2W2KOp1G4s523ScAiEAhoQfbXLoVKe9rN7wDYEomJbOHPDW1QlXA58QCMGmkHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "80cf5452f427b7e791f31ee6b84818f764826d8e", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "176 B"}, {"path": "generate.js", "limit": "184 B"}], "_npmVersion": "5.4.2", "description": "A tiny (179 bytes), secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "8.8.1", "eslintConfig": {"rules": {"yoda": "off"}, "extends": "eslint-config-logux/browser"}, "devDependencies": {"jest": "^21.2.1", "uuid": "^3.1.0", "chalk": "^2.3.0", "jsdoc": "^3.5.5", "eslint": "^4.10.0", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "benchmark": "^2.1.4", "microtime": "^2.1.6", "pre-commit": "^1.2.2", "size-limit": "^0.12.0", "lint-staged": "^4.3.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.1", "webpack-dev-server": "^2.9.3", "eslint-config-logux": "^16.2.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid-1.0.1.tgz_1509376525439_0.9277203739620745", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "nanoid", "version": "1.0.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "15d2b5e76b6dbfb15cac1000cb457d4bfc845bdb", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-sCTwJt690lduNHyqknXJp8pRwzm80neOLGaiTHU2KUJZFVSErl778NNCIivEQCX5gNT0xR1Jy3HEMe/TABT6lw==", "signatures": [{"sig": "MEQCICmK1S0V0C5/C0/9VqSm86x+sDBn4JRmNRSjwkq5oV5iAiB25JQ1cJdDi6pvp/vP895vEXbCLejhRzOC2tTwNyl+Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11627}, "jest": {"testEnvironment": "node", "coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "6e8771bcd1b3348d254d5d966398f9e372c27777", "scripts": {"demo": "webpack-dev-server --config test/demo/webpack.config", "docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint-ci *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "size-limit": [{"path": "index.js", "limit": "172 B"}, {"path": "generate.js", "limit": "181 B"}], "_npmVersion": "5.5.1", "description": "A tiny (172 bytes), secure URL-friendly unique string ID generator", "directories": {}, "lint-staged": {"*.js": "eslint", "*.md": "yaspeller-ci"}, "_nodeVersion": "9.3.0", "eslintConfig": {"rules": {"yoda": "off"}, "extends": "eslint-config-logux/browser"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.3.0", "uuid": "^3.2.1", "chalk": "^2.3.1", "jsdoc": "^3.5.5", "eslint": "^4.17.0", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "benchmark": "^2.1.4", "eslint-ci": "^0.1.1", "microtime": "^2.1.7", "pre-commit": "^1.2.2", "size-limit": "^0.14.1", "lint-staged": "^6.1.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.12.1", "eslint-plugin-node": "^6.0.0", "webpack-dev-server": "^2.11.1", "eslint-config-logux": "^19.0.0", "html-webpack-plugin": "^2.30.1", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.0.2_1518550410350_0.8769834535806953", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "nanoid", "version": "1.0.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "3ff31779301f481baa52a6a31a67c8f6e91949d2", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-l5PWjXxZa6DTMzCO3uXNnnNxuqSUeTxNEMWsSr+5QUbC7g00Z4d6tSp8r0RQpY5h49KjwsMxmehLIfmuLv+6CQ==", "signatures": [{"sig": "MEUCICahXTVdh2/Q03Kp5evzpycpf6aNzL9lrWt4zz20a56gAiEAr46MabCZVg2nkUO5l0Qqh/+SnT2V21/P5LMvG4QfPLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFS5JCRA9TVsSAnZWagAAtQUP/1jZToEJEK3Ing9iABO3\nqKFf8gWm883fqEXjMTlwVAggYvhbpePaum5BNiK6HqkhKzI1yvVqfPn/VuaX\nYoSXAyYElhk3eoGP3mK+G1VnQECKE+ODxZKmgQ4X74uL0a8ubMU3c4wotLEP\nOgnEbMAVXZ+YRGAEe6vPK+sZ0JbJbz6pXIbHoCwjBSF7uShvhMHgr/f2xecR\nFss6Y5/NqJZJeAcs8hwpykl5DdGOMp7e5Po+ymRUoARAFSRkVy7moAU7n5UE\nmVtYuk7aJ5odpHuKayJBjW19d0bR3CqtVlSugzAVc4++N+mLu/hmgNY64fv8\ngiQE0uD8T2FO82alvrPue0vcEth0SPjd5LLyDMqyWP1AnK5jvRv3OSj0LZC8\n6nfkGBTLJc1UfQ1KjIKHpIIW6FD3eqGomPBbbOBCx5SoyGvipmeUjYzAC1XF\nEYotd0P2+QTF9sa481vAiYpp45q0iUjswvc0msO3wucGu7ikfDTv8rnlVVHF\nQktkc8+6x55jlcmrlxP/UPbDpAErGvbvbDbfyhfd/ZnxkutwYMMhLA01gBv5\nlJDq8Sst7FsdqPK8YB7x80H/4oCwYXC1/NDDQZBDFZLPj8jtUmsPxqIDSq3b\nqt6klWLISFWsveAnEwBePiB8CzUbZwsm2byq1SKaxl4xSIO9I2FH2RSyou+6\nK9LP\r\n=H/iY\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testEnvironment": "node", "coverageThreshold": {"global": {"statements": 100}}}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "de9d88bfd9b5181a892c6a04c6a0fbc54912486c", "scripts": {"docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint-ci *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "start": "webpack-dev-server --config test/demo/webpack.config", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A tiny (162 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.3.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.1.0", "uuid": "^3.2.1", "chalk": "^2.4.1", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "webpack": "^4.10.2", "benchmark": "^2.1.4", "eslint-ci": "^0.1.1", "microtime": "^2.1.8", "pre-commit": "^1.2.2", "size-limit": "^0.18.2", "lint-staged": "^7.1.3", "webpack-cli": "^3.0.1", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.17.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.1.4", "eslint-config-logux": "^23.0.0", "html-webpack-plugin": "^3.2.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-promise": "^3.8.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.0.3_1528114760006_0.6538326208256175", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "nanoid", "version": "1.0.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "29ffb9a4fcbb671fca98da5e14e5c5e21ba76cd4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-zWPAK2WibMbVda1DvhYbahj7DAXLMvSI4Ik7553nPyJ2p8I5qJr4/j/6kgHJalrOx4FRbbme2Oa6utdzr6I/tA==", "signatures": [{"sig": "MEUCIQCe7frNax7/Q8KuEkqHLOCXrhOzhUfmRA4xDXcCw1s+WgIgGB2S1tTRFhDtl8tulL69/iAyQuwwEG2ZX1aUhWiAw7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLRX1CRA9TVsSAnZWagAAfioP/3GsILt/N+QAo0aHyXWO\nxPGnqyaIxFiMHGadnDpV8ERLyL5gEJmZs+9W4+1LEfOk8K2rmWV7rEw1uKNB\nM3xBe+pacu47Bf8IVyfKoG1psJN17H+yMV6wStq8pp//BfnepIHXl9C/LFyv\nhqh8PJSeUwxcyaQA9VZn8HzxPgmwGWCbcCFwl3HRvykGJF9iK0GGgJXlOHLm\nuOSn6TN/isshM3V8dcS6tyaBexH7lqMiIuNz201H/Ono6z06fXn9gLCKxJuj\naAcWvNLkyyKYT55e5y5+bSO3oCURBXNEvE/QRcKoFi+MTRAezRP2SlGsQJl3\nztJmc28eMaQSugk8f0Emw8rNNEfWgkU+GvG6m7+wLzxeNnPWgkSgZkELFUvS\nsvmrwsIj43dLoFWvp8R+mcrMaBVthjFmuTL1wm2vixKXFxlM7/Ilya8q8MwE\nAEud37TL5oe8hf/ok6mw5WOSkYDbbb4w+SfEDMnCbBW3FVkT/JeCHmoKUSuL\nepfPkt0aZzqA6NKp7VXcleSeY4D9nEIKCmG8+uswfNPtZbvK3RhT9fAlW/5w\njZx3J3QQu2QBa8DcxhGC/zY7D8L4p7ify3KO0Dpl9DPn4w3nCrCigLw+loIx\nWihRqkYgyOgGa6Ypr1I7NMSG+eH2xcaGjcX4INP2i9jw2L4OFdDHhQcjgnoo\n6ZUV\r\n=HGLk\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./random.js": "./random-browser.js"}, "gitHead": "57cd0ffae47daed446ad9a3f15558ba81f23f127", "scripts": {"docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint-ci *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "start": "webpack-dev-server --config test/demo/webpack.config", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A tiny (162 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.5.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.1.0", "uuid": "^3.2.1", "chalk": "^2.4.1", "jsdoc": "^3.5.5", "eslint": "^4.19.1", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "webpack": "^4.12.0", "benchmark": "^2.1.4", "eslint-ci": "^0.1.1", "microtime": "^2.1.8", "pre-commit": "^1.2.2", "size-limit": "^0.18.3", "lint-staged": "^7.2.0", "webpack-cli": "^3.0.8", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.17.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.1.4", "eslint-config-logux": "^23.0.0", "html-webpack-plugin": "^3.2.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-promise": "^3.8.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.0.4_1529681397003_0.002517632260310876", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "nanoid", "version": "1.0.5", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "4a45db5a53c02ae29188d6b136fbf069eecbd000", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.5.tgz", "fileCount": 11, "integrity": "sha512-LqTGhYYse2oqJXuNCKEwudeZuDme/IkKuXNYcOG42XeB9neefy5MQXIzJWyeuSwQh+QOgozRobVac1TxJT3lfQ==", "signatures": [{"sig": "MEQCIC6tzN0D8uS7c3PeRy6h3dA+l1eCUbqnqLQXjBHtpXEHAiBNfRkt6zMPGv5Kjdw+kHjVaFh6aQ1y0MZ4Y1QXWEDd3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbP+HwCRA9TVsSAnZWagAAI4sP/j43BzL8YAgM0ZYSYp09\nps5ak5E8z/Yu+8yfvJ2pCUibeqYc0rfjqPzhCSUEWn/vaAGJKaO21ddvsw+m\npedIBkKhZoeoX4HlZxywq1Rq8L7ZEgwcIjHO+xadjZ4NRxgkUFwhGFPR694z\nzF+KwJpdTz5tXmDiZZg4nlHd9IL6WTDzUFt4VDKwCnuv16EAuUL2QTOGKs20\nR9T9XYjA4cTCBuxA2IAIPunDebLc7r/Uj0RhMFPLSUKzc8hLEynHGRIJwgvL\nIoXnjFuyEYLjIZf4V66O9/LZfc1ADcIOPy3OONj1mMVPx5HMSXLzTDZXBggG\nusGTO3gXmmk3CHg4tGNmzlkP8EAIusLC8m57iFaRUHBwxnHy9JiXUfc0uIJf\nwrTFwQRKcKoRbf2vxNGbnOrVSMBeUAzbMftphUQli6hze3tcVjN4gjaK30su\niBTGrhEL/uExGrn3BjpI5X5Xy+J5fRjTZ/fR/Ka0T6TN4Hw0uPqURicwGi6r\n1zyXSFO+nEudLEUQDA+4HlRzVR9Vlh30gqLDqeNG8SA00Lzo7XcZIVdcSE0v\nsEr33NrdazVMpeSKyWB0uD5jbWb447mZUneVid1jXQ8Wmem5427OrUH5GmNU\nK1KXfXFtsVRZSt1vfUF3V7z0JpbK2eHHo10XK1F3Bdkf+otinpnPWW8is6nQ\n9bRV\r\n=91id\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "gitHead": "3931b684cdaacfcc232e29d7169965035f33f4c2", "scripts": {"docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint-ci *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "start": "webpack-dev-server --config test/demo/webpack.config", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A tiny (146 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.3.0", "svgo": "^1.0.5", "uuid": "^3.3.2", "chalk": "^2.4.1", "jsdoc": "^3.5.5", "eslint": "^5.0.1", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.8", "webpack": "^4.15.1", "benchmark": "^2.1.4", "eslint-ci": "^0.1.1", "microtime": "^2.1.8", "pre-commit": "^1.2.2", "size-limit": "^0.18.3", "lint-staged": "^7.2.0", "webpack-cli": "^3.0.8", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.17.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.1.4", "eslint-config-logux": "^23.0.2", "html-webpack-plugin": "^3.2.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-promise": "^3.8.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.0.5_1530913264861_0.14705378037649974", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "nanoid", "version": "1.0.6", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "d6c227f0fd8d59735f7ef041de9ae824d224b948", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.6.tgz", "fileCount": 11, "integrity": "sha512-Jt7SU1SNKYrKFtvF2t8CPfNLxgfSNsFHvBaQGoyxjIbIPGMUw+sChtpiR1lnxMQ3OIr+7JrU8wsIAqWtAFAlMQ==", "signatures": [{"sig": "MEUCIDfCeovb5DB7xk+i+B+pIZy8G5EHQm0khG23HzbAhzUZAiEAsO3uON7JVRQPLzcukRISOba7TJnUFA6LkxY6OwDA8p0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQakXCRA9TVsSAnZWagAACd0P/1E2tfH76+mLaJ0nfsLm\nCQy4MwpKXVwiGJ+6XMSN779ah39f0XffzLXrwJHT0Cwr2+m9x6xoCi4ztrOI\nj6B/i8oqnU10w9E/N1rfoDZTtEQfux+tRbC3af4ACBvn4Zb4BLANc2EqqOX+\n9O620gXzSMpeKI5pzCJGU137CUOYrYvcMIexuuiNx1NADjuNpEIj7Tj53D+k\nmaPF3KKOSGuiHOksAsoKwiIofx9xFkSZMVIjG6QZB+IlJ14APEdBhZPZgnJZ\nc5jMKVEgMqBTBKRghu3X+Cd48Ylblo2Yvwn4XJc4DuTMHD/QlsUKxVN/ZMDK\nMgZcyE8DM8Gw7NzchG6WbIwgWpRoosfPrsrA7QcstEJ7Z8Pk8S7uOXI+hre5\nsY3hA2M5MbVuWlCCvga0Dt0aCkLUsJHwl+N7jULquG4lRnilqG9/F5NbdObm\nFIavba4Vs2Pp7SeL+bbgHhdDvA6GhNPOv4AWLTdHJB/lSo1nDlYMpP2EgEo/\n7KT9SHaYgAEakmbQErokcRnk9uNeEuEPPEoFIcTRLSSNeemb8Z4+qOFrZy0h\nxil8VslK+Bb7ON6c6JcVIBBeJ42B/7Hdm/lFjnLKkuebz+btstYevRG7ehZe\nJKvu/6TV39STzVFcUuYqTT/N30ZuW4S3inRvxPIvws3NyBYth0HIoRv9bmPS\n85qe\r\n=VFUd\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "gitHead": "34c9445dec73d8cd04dd351205817f6a8ba52cc3", "scripts": {"docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint-ci *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "start": "webpack-dev-server --config test/demo/webpack.config", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A tiny (146 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.3.0", "svgo": "^1.0.5", "uuid": "^3.3.2", "chalk": "^2.4.1", "jsdoc": "^3.5.5", "eslint": "^5.0.1", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.9", "webpack": "^4.15.1", "benchmark": "^2.1.4", "eslint-ci": "^0.1.1", "microtime": "^2.1.8", "pre-commit": "^1.2.2", "size-limit": "^0.18.3", "lint-staged": "^7.2.0", "webpack-cli": "^3.0.8", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.17.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.1.4", "eslint-config-logux": "^23.0.2", "html-webpack-plugin": "^3.2.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-promise": "^3.8.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.0.6_1531029783730_0.8539873147624912", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "nanoid", "version": "1.0.7", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.0.7", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "23857a9514ae8efb7374221b923a2b92be3f74f2", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.0.7.tgz", "fileCount": 11, "integrity": "sha512-DCTnU68QAckm8vgPbFhM1XcK3/zX8LWL4/kEXzPH5w2qy2ibBWHig+685aF8ff8rd5lUXkhVB3W60pb4mbRcDA==", "signatures": [{"sig": "MEUCIQCOFrk/fTxNQTMw7NwFelXm+8bw8ep+8TyGUXpCO5E0hQIgBqKyGHSFiQ+33d5vJ0wSHi7ev16A+bSlIrrQhQBT3SE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQcO0CRA9TVsSAnZWagAA4OEP+gIZVi3Z+JuDwPcbPjM+\nQEfh5ol32ELhj5jXVg5ieQ93K5d6ySFPji+OpO6SUqkjmPf+XrMqsIrVUie3\nxLwnH/1pzm3uIDmxkBKU6RqwPgD+zu56DCaX/blzfLk31obufWYTkyZl2W5k\nZKeAmJm0gjRywqEF6JqBDTM3XJz9IzB1N/l/Pg7p3n4zAweHlShe2oFt6M77\nGNJvAv7qYD30SxgwkCfIoH7KlsqT7OpIPWvvqNDgZHCrx7qp5PdMmmN1Hj5l\nGFyDWrUCb4e5mI5q3b8CNPOOlhRqbUyzcxiVdyNuaE4AqtJkNwPpAgaNNTIp\nD/2rDpiufWNGuoZMiVV57SVCl4+28e+Dc4mEbqOTFFioJuksZZD5FgfieazQ\nA9rS3FHRrGJvLGcsECfFcAZ3xbaWVZW35GCchGmrT/0qVe8if2r/CsastS0u\n58oNgzhaZvcHHhpfQ2OZD8qajh2FciDVM0HDRhzlKyftYXvK7o1JtX45m2iN\n1rkcosmJ2pFr9GBS/Oh2eS38NYfIUloSEdySa1REMLlRPbo3iBHueOZ/5f5A\n4l1f1sCFmGDIx0cDgn6UsaMOtSyT5b2Dk8cuK3oMPAfY3zO7LO3BA1faxzu4\n9WZSkAPrscIoWJNfvPirWIp1jRZg7dGsHpOUAB+rJrmo67p2SuLgRpi9c+hy\noPc3\r\n=BID+\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "gitHead": "d0963dd27bdf37857d5ecf55d8c5079f1aa7cd2e", "scripts": {"docs": "jsdoc --configure .jsdocrc *.js", "lint": "eslint-ci *.js test/*.js", "test": "jest --coverage && yarn lint && size-limit && yarn spellcheck", "clean": "rimraf docs/ coverage/", "start": "webpack-dev-server --config test/demo/webpack.config", "spellcheck": "yarn docs && yaspeller-ci *.md docs/*.html", "lint-staged": "lint-staged"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "pre-commit": ["lint-staged"], "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A tiny (146 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^23.3.0", "svgo": "^1.0.5", "uuid": "^3.3.2", "chalk": "^2.4.1", "jsdoc": "^3.5.5", "eslint": "^5.0.1", "rimraf": "^2.6.2", "docdash": "^0.4.0", "shortid": "^2.2.9", "webpack": "^4.15.1", "benchmark": "^2.1.4", "eslint-ci": "^0.1.1", "microtime": "^2.1.8", "pre-commit": "^1.2.2", "size-limit": "^0.18.3", "lint-staged": "^7.2.0", "webpack-cli": "^3.0.8", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.17.0", "eslint-plugin-node": "^6.0.1", "webpack-dev-server": "^3.1.4", "eslint-config-logux": "^23.0.2", "html-webpack-plugin": "^3.2.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-promise": "^3.8.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.0.7_1531036596072_0.3832453461399823", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "nanoid", "version": "1.1.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "b18e806e1cdbfdbe030374d5cf08a48cbc80b474", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-iOCqgXieGrk8/wDt1n9rZS2KB1dYVssemY0NTWjfzVr+1t1gAmdTp1u2+YHppKro3Bk5S+Gs+xmYCfpuXauYXQ==", "signatures": [{"sig": "MEUCIHXaNMB8XpqUBVJDRz8EuR7ZQ6kSHrXw7mFC1kaKPHlVAiEAg0u83VoCDlijm/nFiEBAapdFFHynFnOj0e5JE+qeOBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSciKCRA9TVsSAnZWagAAD5wQAJVMKhddvls/gYKL6Eb4\nEbRrb50Wpl2o/y4CkOC8rUxzpEEBMQkLkjSTFbYXsHoXs4TmXoifsXBhqb+B\n6qVjqcNRgsHF4FXVlGz2jW5TqAT7gPbMIc7vpGWb+bhp+yr1GJ/fZSp7+fV4\n5s73YW0i5BksuGwLDIbNJSJ61OHdTfIDFIP3v68boDi6twp1zSs+3cf1nLiE\nIC7nI5CtBBrJ3NpQu80pzmd9lkXeqAuZxQsTm2F1H4mS/IpzHiE2K4qHme+u\nlgo4Tn1tSgAMiJKdNJimzN5ChUFns6r9On7yUFzn5VLjQAkriS/nym8jkhrB\nBZVA0w4ysBywgGoSe/O1ssi8VYPw9Dei3R6/g29LfBqAV8H/3EhJp/KwtvTM\nxiZx9BCt43Vvy8DlVuh+uQNLbgaUg2rBUB+V7Xa9VKAyPnizZVKwAof66vg2\nffwVtvc0xFgh+4tQylMjdiJGPwx3fE5aqHUM9CldvYIdmYn/3zgm0jr8DdIW\nNBGkBadW50VPpEaXeZ7InzLqrF/KbZlQHL66SpKo7MX3xUHCCvo99hM0M3Nl\nkyOi/yMSH9X8LsnlYJNAwRz33rZ00Xo4NNnoFMsGlTvnx6AGcEMb9dmvBOJl\n8A+zxb0+Csr5dOP9o2SEDyiphWHgfPr2baUKtBgVS5RvLngUVqS5SmIHYWNu\nWrfu\r\n=FNMb\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "scripts": {}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.1.0_1531562122115_0.18947410018080424", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "nanoid", "version": "1.1.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "6159cc3c985acb7ebd70fb231b0b01f2947ed7a3", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.1.1.tgz", "fileCount": 12, "integrity": "sha512-tIMrzc7X0rCP4WK5+FB5jtZXWWazqQkvA1wxg4ZYo5OWDADR0QPFm+ITN2tv2RWnhXXsryinICcO2qQSG3SGGw==", "signatures": [{"sig": "MEUCIFfTzrB8uUesAsKlQWvqkeiE5+6EpNfQYuonin2h4zWLAiEA4Z/obuNCugdVvPb+Eg2omu/1ddlPmdbb/Lz7N0wJ9G0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbanXGCRA9TVsSAnZWagAAPIAQAIJngLwCPDKxSloNC1Nj\nbqpOcThJ545+FJzdzGPDOR3i+a33Bsh7lhAHnnMg4mWOXAr7R2JEpu8yBQyW\nNoNAexGKtBt4clzxycxLOO+qFAyDfCOy28Mk8SvNlJDge4wbO4lLCceJ7rgN\nAZhbaB4iH+i0R3TZ43flgfYoc+wR4uQC7EUvOnqGl6kSEtopKVWaOLhZd8+C\n3rB3GUgCaBD0GPyebpz1JQnu9PrgXAspre1kBA5ykozB/0fokgiusZ91cPp+\njkS3lNtirBHTJ1C1n0IauNLpk+rpoJQHKke/U0K4uBaPS8Xyw4EFiGlQTcTa\niQOZRmrBBdYFS23IxRsCvesn3pfSNgppUH/4XawV9c5TqLh72zpJRP9OpDsM\nB/x5ymBxkQiOufqngCwQ0tOulxgnkFSB0SAfCnH4+J1Zk68xg1N11II7uhc/\n0vm68ZgP7p5fMZGnr65DcJ4nrXEGV+8NjUa7NBsjo2ZMfGBSmfEbisENfI5x\nVoCahNNuHu5AV1TuuMbAnUhIMyXcR9uGug3ijvroHXVA9HirDirXH03FotXq\nCRfJ/P3qFN9TtnU5E+Y1g94lwFg2d1FMreli6tkjCnenWuQIXBPCxLCYF7wi\nR1n42Sw1G94eYVy3R3znJyAETOtsh/t/Q0AqcBCm8wWEJOp8HXNZQXY55o9B\nzJnO\r\n=2auQ\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.1.1_1533703622369_0.8639514789039988", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "nanoid", "version": "1.2.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "e2c4dfa45c2bed995d0214aae157ce5052cfcb1d", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.0.tgz", "fileCount": 14, "integrity": "sha512-rJvd0q5Bq375+jrMAJh8vZk+0Q4lnHyuqZL2fbrc9moYy4DCld5VSycYLXvwFHbbut1+UcjA+fm0bq4ADVBYQg==", "signatures": [{"sig": "MEQCICemTu8fbeaySZmukTuPBPNlYuTCnR4VP/5SgF7KzXDVAiAFyMbdGr6zfhVZ1Gs9A8TxT8aC0URG7UFEBhlTXGyHBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbOftCRA9TVsSAnZWagAAt5AP/18Y4ufDNearvBaz3fXZ\neZgDPBAJ8TLxo0UFEyDEXmAmWsSd7kdjhRx5lDFCPAvT5M6dz9TnLpTNbKQ4\nwG+cKdH/2dPHJ3wFC8QhSRUyJ0LpZPYfy6v/GDhDrOlDZ8L3ciA7xrDgSIEL\nBSN52r3o/MVIP1HPUlqryRcg5+VQfacjUZbiEnMz6tG5qWuhvoy4pq0vdUUL\nHGw2mBPc30m8TmU01wlW85IPoyVafjhrwn4B1+o+CtL5TFczmmZE6M8Uze3M\nTq6/LvjOvMZy+aYWfhEnJvkoWa61LHgJLzuVlTK28zJAwvqmBJZOBy1qfYkt\n+sKoM80/lgWtc2amo3Szl4jaXJ7ce+M6JH4dKizviy2S0YQG5jrIshSF5VZ9\nemc2/NT9j11OdpQvnUfQ8s2aBGNFVEG2zN+4kSy4GZtQCjpjoCjZ7IrHk3yz\njOTf9hSC19en4aUxDLxuOXFJ5ruSEl75Mp+6QoYAcwzJr14GEjIhPrCNBuqI\niwVOdbW7NmNPJJs/WF2ThKjfX0JW9Ihleq6ZOFMuVVDUJcMKe9WJ743gN1jL\nFfty5EJVDAfrlCuRIoekpoxHahERmbmartxUo+qVFbsOAR+LsbDfIuTRBSB3\nriyKkPnvOwe5fOFSJirxlZu+gFuDoDPnHM4erTVG9g2JTH190NBcxy2jX/+3\nFSfT\r\n=kixR\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.0_1533863917465_0.4575002057681956", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "nanoid", "version": "1.2.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "922bf6c10e35f7b208993768dad643577c907adf", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.1.tgz", "fileCount": 14, "integrity": "sha512-S1QSG+TQtsqr2/ujHZcNT0OxygffUaUT755qTc/SPKfQ0VJBlOO6qb1425UYoHXPvCZ3pWgMVCuy1t7+AoCxnQ==", "signatures": [{"sig": "MEUCIQC4SNNgsXTU3X2Ohj80msyFuhbnv2V8R56h1KBdfJRLhgIgHlw58QwbLl7xId9we8ENd5HNai8We8jLZkfrI2W6Bdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdR5uCRA9TVsSAnZWagAAKLcQAJc7wuPKdrE4Yl9aCQzD\nLpIcNuvx+OgQcpz0iY+6Cn/ZSbn1CF3ORDJ4eY5G+7en/S7S2vlip4M/yWfn\nK3zaxh0V+25v8TWskYMoqYjkM0vgX/x6+2fQ+gyZ4RZnqYOYwrsT6vb8LuTB\n519TcxvYw8cvPnUoTwAuyzBtB1aJ70aslzFPSQcxtiXd3KlqPaI0YhBNPC13\n0nPaTtr7XbV5QHr/XyUPdoGOWpGDjoBXSQtndd2Zc++/0rN1Rt8cUFvWcdI/\nQkXqqMdL36Pu9FEWV2oibVwpnxwFhoZMFO1StuOAKi0DaZkhQnS0NbatgSfT\njGeKeAlU5HThPXzM2KuXd5Viq6LAJkGne4wLPc5APkI0tR93QJJx5XTPgiTw\nPnN4VXacEqfjoqt4VF3mpO2lAYMlU7Yz0nvpwnt+WPeW3KO1qg4Y/3GJqcke\nnSX2/kYSgVQItsBUb4otC18eSAbcgV8kgJMjja7LAPr7ThfVOrpswbjliLUT\nwAcmdeoRk+3Wd9Cg2Sx7tNs3Q7wTB8EtcGiCrlUbVNKI31/l5TBgplEqIA3Z\nLVuSl76WkPTBNavZDvodHq6039refk+OKHwOlIR2UaWBmT0242FLrr5ygKuD\nR1+M5PB3WFm3b9ZAdLkZlKVV2dBHTmkQNWPm2hDZiXmDjBJ8oS3EkzeBg68D\nTRrN\r\n=jZyJ\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.1_1534402157733_0.11071688872189922", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "nanoid", "version": "1.2.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "9acfdf1c7441c5beb1c84013e2c832f5a7319536", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.2.tgz", "fileCount": 14, "integrity": "sha512-o4eK+NomkjYEn6cN9rImXMz1st/LdRP+tricKyoH834ikDwp/M/PJlYWTd7E7/OhvObzLJpuuVvwjg+jDpD4hA==", "signatures": [{"sig": "MEUCIQDWoeeE++hOWk9dvF7ldLndZR2lDizdHppgPKgO2ymlRAIgNSHZGRN0fj+sPSPiE/BRvXJ6qzAMMAKUTBkxLokFd9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbheGBCRA9TVsSAnZWagAAPpcP/RUg7Mj4uENE8NQZKpYp\nnxp1zRpw85U4OzyU3oh6wwXk1udFuVAXrisOcgFtGuxdfCmr9OSZ0Y3WVdCF\nIRHVeU0eIKGRNG5ZxDXMxNVE+PZ8hT+OKrbzDPMtqFdX3AaCKvZUDPoPX0yU\nYU1hElusog/DNIc7dsMeP3lcV8hFvwBhizTlM5KjeyhwHHg8+4f0Jlyneor4\nhEjGH0iLbY4bhF9krcI+Jz23t9aX1r/51UvoXzMCiIVYLlRYIM0ht8Fo2GoF\nOEQCuMwxH8JwfqobKL/lM/ZzHGF/MybQq6z9El3fOA58iO8rstSWYT0JGkha\nd2Ely6J2P4Mj57oEvFw0WQZkNbHAZiWQKG9vAZ19cIQ3qqqZhV/Qa8lD33GE\nlk1zuVj9hnpuaBPU+FKsK9Q1pTdj1JB1psbW1a9hVRdQduE2+PXbcm2MTv44\nfi6XvqlFQtwFDpjP2aV6YS1nqGQjPkna0sM9i0Qh0Wq7TzBLHXqM8jTZnibs\nopPCFC1vXnd9ecQvniFdXj7tjl2bHb4O57Y5hG3CooKFYMoRoHU2FWmadaig\nMzKgRcs8Vtoq9DgqSytUjbdzVCXWbQs7I3ZzFjO0mX39KNS4j2f7Cgd3uMx3\nCN36+HMKuuyFw5LO+s6hfsI3f68JCCCuryELDUxzGfgye7u7H5z/zRZhTXbn\nVXnx\r\n=SS/l\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.9.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.2_1535500672499_0.736853597204554", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "nanoid", "version": "1.2.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "b8f022193a5808f9e3112658a2c34e43b24c009a", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.3.tgz", "fileCount": 14, "integrity": "sha512-BAnxAdaihzMoszwhqRy8FPOX+dijs7esUEUYTIQ1KsOSKmCVNYnitAMmBDFxYzA6VQYvuUKw7o2K1AcMBTGzIg==", "signatures": [{"sig": "MEUCIQCps7MWT6/s9XcLb6Vn4PL1haLxwV0qBZOksbK0Qc/RWQIgQfZD576FgoT+dR8uTP+7h7kzs2AOTVFUDG/P4fNyQuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjqPpCRA9TVsSAnZWagAAXXIP/jZlSxiXQIlxZygNXjmG\nVGJtTMzVaZjfNnicpTm+dxteqYKiri56PeeAiwLGzBS2BWYaQUIVKuQPacF7\n5WXuZBPqD93+L8KnSJKcZZyz0JBTBUb9ESfWqG2PbKMJkd+ZkMR4brPLpMT4\nHkrVXDp9bzcKtt25+jcR557WZHs9enRa3c2vARCCOoEf+fBxzRSkIAjRHNv9\nrCnbq0sd/PX26go32yw6HXSqamrG8SAkw1IPv19X1u11G2UsUarUuMQloXZB\nHJk36qdKS4WuaX+ME55vrY/eP6EcdWbDtip0vkYkQHWzKV9myxfKUfTbYRnj\njASC7gZtgbjlSeDXDIsd3dmC5ROfkSJkobUX9pHH+Wi3Fpj5hMqbQWxCnO3f\nVIFSLCq9smS+c8WzZ2MlFVyPov4BoEao6MqNOUe6tjfmATfCUFnXTX88tUXV\no/xPQonnmDnQupV4eUHUUc2gFYpjS+a+q3nRMU4lBqteIpq7RvGWECeF/dwP\nFKryiYMzX/bq+xW/08A3ajWqnDbktp8uqfxITNbn6EE+lhyj2hzkqP8u/8cB\nEHsHfNF0MQilUcK5PUAOtk6HlW5MLCA078tMSsPt6chGtaiyqg3aFauY1/9a\n7lJJrzjOBXkKxERr8GrTyI6FADiBAawWezPLrJPuCf18u8+KatuQq9hJgpeS\nfSuB\r\n=lzld\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.9.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.3_1536074728905_0.4335031947506891", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "nanoid", "version": "1.2.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "4f0e19e7e63341e9fe9adaa8d96ba594f5dcc6db", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.4.tgz", "fileCount": 14, "integrity": "sha512-VbM1HbXvDxZhcONUEJ0olXwg5MgOWS89KPL0PlchtB9NJ2dOTva64RR3fypJuEMQffJ7b1rjcPyR39SsV2W5PA==", "signatures": [{"sig": "MEYCIQCWwRjHekVq3KOO4chgHyFoveXX904ghlA40FVRC/RPUgIhAPgJIAhkb0xBIgWV17Js/vPNyPwktuecJykvvMnre5CR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbq/x6CRA9TVsSAnZWagAAUR8P/iKVbB6l7wPvvnQCFixP\nbAHctxAFbjR81+TAfNx3UfcIwJH+kEfj3nAbXkW6m7EJIL6gp5ul0AJNphJW\nHVr7lJ7gxJ92pF/Aj9vWWL9WhHChujyoqeq/F4ZK+Wkl696aB3UgeqAb+I56\niSzsoxYaz5ho4kTwTua//RyTr56RD+nNIrxuydRoxs/q1gODNkO+m8sVXKa5\nZ9gUR9OtB4knuaLB7jDFwyfVHRuzSEPlaK5Z9fz0OySoyAMfBaR5kOdJFx4L\nptLSPo3NDCZAMXxSVSLNOWLbO8yIOrovlvxETZ1cMHa29+lLXp0Mh5zYLrX2\nIkCklho/zk3kOs/udZBHvJd8BGV+CRteW3VNOX6fOKnzZ513y06qmmC8ME4r\n4/XRFXo1znk23A/5hcPnxlRZ+Zuymri+goiat2z2MepvTgaT9OsUNHP9Fezq\nXK4FbQG/vuA0xER8DB3RayFycjx5JmWQ3b0w+iP1tGrl3clKS8wS1b+s2gjQ\nIcaTI9PLYzV6p7nZKZGfv0LTRxF6HRYfB0CBTdBhV+Z1BU9h5y2QjztWlIPl\nP7HWM7MPOEpYYhAwxM7q/+0jUhf/8ibfb3uKLSJmwaERQfgv+Uu9ZR3EQtV/\nxC3FufzJJnYembeb4foImvAiOjOgPWRz8Wkn/EADXM12IJV4iP+7TXYOG9Ba\nHXXE\r\n=XqED\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.4_1537997944930_0.8392238140567405", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "nanoid", "version": "1.2.5", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "122bcd6bf185651480369315f20c9ae1519efc35", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.5.tgz", "fileCount": 14, "integrity": "sha512-mOYdCQS3xdgu81Hg3EQEhORykMk53Pkzg15Y6I+O9T3db9qHPf9z6nXzQBCUaHXlQs57eRKotDTzYlYACL+5uA==", "signatures": [{"sig": "MEUCIGcKAlgsursD/uWDoMrIT7q4yj4Q7G5ZLvjbNxssXtpLAiEAh61x62suKmCXjyrf4tgDTAXzghww8ue86iQ7rSfifXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrt7+CRA9TVsSAnZWagAAT/IQAIwiY4Gmzp2xEBIAT1VN\nvdzE+zRMUWGlUtIO+KLxFS7NEWpU9wqZ34H0aE10v1aUTV9tB/zuQopz6OmH\nH7CAgaG33lJ/wzZAr5C7x5gY3BadizZp5cW3z9AAySLEXYvBAbYdAUYmBsgz\nhTkMVXy1KFx4zAG5Gs1MmSC+MqHAfmvg3izgBczzladYBxxx9hmxbovIorNb\nryMaPjj065V9Fce7/CoFetyD1Vbr5WjO7zSRxrdnJMxtGzbY5lgzSfNIqVpP\nXmIq+W7ZusEuYo8eRCdwjleuU26PRgrp6t29FIT5pZVQTpmS6MpEZN6vdgmb\nz3ofXjOzLChSASg6rdDqL5J93ZLuo4uk4YGTvTcXBSD0TnmxasxPm1xddbpr\nxFEV658EIQPFtEu7/hZYfDXRzi1es9dE8rIlEVljiJn+hch/8hgWCvorVIpA\nX6g4uydg2cNo7qe287To4MMU6SZQJ0mscHt+JZsAReQmK7+lbkDSQiaoPm/c\nCwGD/PBMkJXc34ZjXEV+GeQNWsJCWINCSlXPpmhEWjPmz5tvEl+ABolVecgy\nHTIxvOfTTaqemsRgGDs2+xTIq5VRARX2MMNWmIygPdGMg1kR76KRcm742DZy\nwtZF3KIOJgT2NJKPFjzWQaVioxVA58CAEOfMNyGq7iGa92ay5U6oTiF8+Cq5\n+1e9\r\n=lvFF\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (145 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.5_1538187005452_0.5597982302237139", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "nanoid", "version": "1.2.6", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.2.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "5b9427cabf41f5df1fc371b14c969ffcd3b095aa", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.2.6.tgz", "fileCount": 14, "integrity": "sha512-um9vXiM407BaRbBNa0aKPzFBSD2fDbVmmA9TzCWWlxZvEBzTbixM7ss6GDS4G/cNMYeZSNFx5SzAgWoG1uHU9g==", "signatures": [{"sig": "MEUCIDcSmYhlANYVQ7rOapPEJnS/jdHCRCqCTvYJxQoFrAq1AiEA8cA48bLlnFk3awS0d1udI1FQugdSYz8I2vXN28vHHqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs9+ICRA9TVsSAnZWagAAZQIQAJnEs7MOWu14bH2iCUKC\nZ/x64UUwiXJO9MxdEMqM1QDiLrnWH1BRIkNegfh0iaseEILyyeMfMD97/Rx2\ncO3vDWsQAA3H4KxfZnx2C/vcpZ8tjcKwFVqXRJMHKQozBdtP+XZg7KEfY6m+\nOwSvXdxI+zIHoFlVGg60npZgeL+ws97mdAuzt9GF+hOjZt6gnIeYV24LwAqh\nTiqao16k0FM8Kb1tZPZsuGi8jO1ia3gCrdsTv9CTCkWLKRO1vi2FvFy3nnPK\nQ8rijM0sAfCDKmivvTGkhmNdC3/AYkA1BjERZxL53XpBXAkUykTR/PMYNMz9\nbdjpujnEHCa47DXDdEDUCGwOJ7e+ZQcPF45HV0mnlQnn8+YszUXjKYz47kAc\n02D5vv41hn4cSQAErUgNX0cejOaibbXjToPjOYmXGa91iIDcGk/H4zlx/387\nkAqNzo/JvkRqEjchPm3Yyru7nTopcK2XHnTgHtKr113fOoPjZ1B04OKP6dgT\nzVUv17Wo3eYrgEkhLdaGcdcyTEiZ8Ng0e/fzubNaxl7bzNOHA8ByuCwVt0cm\nacKIdJFAMnajNXlYi8bBuqzIe1tbREe2JMBnfb5zELoSxI+5sln3hB6Cd428\ntQWSz957YHIvx2G4qDQNZNSZJKz+79/FVrD3WnUP43FB4hvDQ7yeINVoS/wT\nctJ1\r\n=4AgG\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./async.js": "./async.browser.js", "./index.js": "./index.browser.js", "./random.js": "./random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.11.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.2.6_1538514823590_0.7673237899332155", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "nanoid", "version": "1.3.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.3.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "0b8d089c81d3b6cf61fcc16051432342b06f3ad9", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.3.0.tgz", "fileCount": 18, "integrity": "sha512-OP8SoC91Kyjl1sdSTEnM1xYh4gUEOSkUl6wRBUklWOPyfPRbeJbhvdhQYXEjVtZ1LI9amVMkIWQI2nO8O7DL9A==", "signatures": [{"sig": "MEYCIQClDrmI0+GBUSwdJiOc62jDJCgzMMEi8KWcv5IcEO+dOAIhAPggstclpF3rwim83TJurzdLQaEh0wBo58XHrTaUiTsD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwNC/CRA9TVsSAnZWagAAVdQP/iTDw5rJaEg0XmSqqWY2\ngB2yDwgg1GxvP4QlzSIABXSIPrhYXWR1xxQO48eqqp3qkWSYZ6FDIuyoQQeg\nR/1ietbgpm6bCMCxHFANAf6BnsM8R/q57Vh4LforrfTK1d338CSYxvOatmyu\ncr05VL9Kpn3pFZqZm90IXFGlym5H9LZ1qUFWgzRjt4IN7xwoH0ePz13pRQj4\n+gy4o/R+EY2t50EycV9Np8MOAa2oiwPAg6T5RD9d+Xkel1kSJ86LG0qOcV1T\ncM8rQVa0wcJKPR1icQ4i1YndnXmyRGeTi2nUepGhDSsGgffNCOZtPxtQB/tJ\nGTrAxwOda26vZwUVVEG/mEm2VaSGc381t3VrQLRnlkfDe0h/0rIqUP6d/JEl\nrLgXUNffbAtpQEojfkBUxpqCpO3/2XFl1jEzlGUfDTchwOb9LlAjlXOrFemV\nVj/lm6WlM3m+GV9ZlUy35hM2wkMBEG44+9hbug/1iaIfaHcHtBFMm3Uy/wts\nXyIWvf2C6KLo5Of/G77BJlXhPxWVB3TgnTq3k2cUbCqaj6bPUr0jgLRgSXvZ\n/TVaMBhHG0/8x588fHwociU7rG66tzg7zuEeyEFer0b5cNtkNmf//jo/Am6u\n9iw8c1W9Ke6o8uWyE0eufC5KzvcQMIgSrJPGW+2iwrXeyOuyeEOAgcO55M5b\nR/T4\r\n=5av2\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.3.0_1539363005979_0.5489682671382383", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "nanoid", "version": "1.3.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.3.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "4538e1a02822b131da198d8eb17c9e3b3ac5167f", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.3.1.tgz", "fileCount": 18, "integrity": "sha512-wSBw7t+JVjQAY8q89BhrTaBTMdoPGbZP8qQqidQHL76oeaFJ9i+c6SKKHP2l/DmzLP43eeV6JkM3f5Mb6saH8Q==", "signatures": [{"sig": "MEUCIA6JYnLagO0nUSUQnSAsqbHPJ+qJHso9F/u+mqG/14WAAiEA5J4Li6NZpJHUG3LB03JfPBihUNsApFuUx+P+MRuBG64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyro+CRA9TVsSAnZWagAAKRYP/1zfL6Nuzm77gJc2fPOD\nWn+sw2onwLIpEg8RGesBbWJGCgow5cqj7cU3qvR7wFD/Dk9Iv3crdviIiDAL\niASAfgbZDz74CZHUV8EljV74p00q7ZGrXPI1ipMC74f78Orm60jZnzQbhMGY\nJOHYXClGRff6Copncw5ENbkf0QA+JddDCsGKcHYAk3whBl+i9wwP6hojaZjE\n0STlUV5YckEdK0DF5tlPCocMgsd4nwgspDDMY7t/ItpfL0dLq07pZuMisXSf\nu6xLzyNfIHiO2EAoY3v5Qx9gGpiNwUVmFNqMI/7XfRYwDo9YeJKVbE5N/6Qm\n/dAWcdxp6TTw+toZ3wh28bOebLKs+BXDM4/3oUIrkow0R+BnWCZAoj8EQfsN\nUQeM5lCHI8Uyrl6lgVfZuBqzEf3P4iQqSy9oIa0nMCwEIkGudh0LHlJuykN4\nARH4p2EIjfweAC0YPzcOJ8YxPqa6KRi/uUtCBiFKtvim4+XdAbaQelW5A9rj\nFUw+Kn88QmOCj2f7olLUUh/nm9qc0MRX0Po0oaVQzTnlFZGM8juS7ROVBjHi\n+THEwrsYkMd4okP49VlmKY1MQwDAsW9290/yzbbi4UHskIMzCMhehTAdM1qy\nhPYrXK/BZdRmVpENQJZ3tCkDVo8oKknxazWhDVON7+msMrkGdxkGFQAMIgQK\np/Tz\r\n=6PZH\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.3.1_1540012605966_0.5969030224942626", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "nanoid", "version": "1.3.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.3.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "403ca95fead0ab29350fa027de948dc3d3d65c90", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.3.2.tgz", "fileCount": 18, "integrity": "sha512-BWaJnKYm2wKjquj3qJZsTkOMvCg9RrDMfR8/wCg79X0RePCdNYhiPcz2g2uFdPDMkgZWPUuGewnxvSGM9UD31w==", "signatures": [{"sig": "MEUCIQDw/gBBABh8Ajwnetu4DtVypiXbRXsfQbycvwF+t//DjgIgNOwMiUiekU5VKioDpmCAXhrjZ+viDfw9pnzk5ENyWqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2RFECRA9TVsSAnZWagAAT4YP/R6s6LXOujMiJQtEsL4w\n7iTUhBYPFMJhT3WwTCW0Em4+4f+zwiOtsoUz1dqwtLcLrRXHHDheMV/729eI\nqgYHuzj1LEmrXG1sZigWF3DUDwSVMkLtQ+x+HZJ0PR9RD2VCs1SMsepxv1YG\nyqTOd0JZB6jgMVhiD0KenbCduy2qRdRgiGDH1gTycVJyAcoIGdQgy5y+vymc\nYsjmiQrPMQjU+Icfs4/ZJ1zjPdrLWqkzHghowdaxfK/5d6xODvm+t6VgYYWy\ni1Oyrb9wFwIbmCe0LuW1Byl5G24dANPMkXmEDs58T6BvKMQaqWlNMYYdLnZI\nRUDWp/mtAwdYZR2gqODD8D1SNcHC8oOxK+Rd/GnDTms0PoFfvoPfn4gi9JJr\nZpXWzpyCpDYst8Uw07iueIbwOs5HfkMGFt0ofSWMMtYP7oELtd5VlC4e/leU\nTGOEz59VSLXgBRKhKX0yDyG+LOrYd18d5BcmQ7scdAwAf0s9O00SBRH7OKCU\n7wkX4uf3y7izq88pRnU5EcRuz/V+FeArcjJw3RpbSkaeFEkamjEO/TfcA6Hn\naCDuLcqIHSwIm8x9yvlerlPWASWWnEMfgIGDjM9dfGnF47inj8u7m65Pj3Mp\nbxYLZGkMNvikeK2TkBy4+NuuenkOnoOKLqAdYAiSaONgkNTY+w5Ys98VdMeS\nuGuK\r\n=CYjW\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "11.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.3.2_1540952387977_0.4073365778433162", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "nanoid", "version": "1.3.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.3.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "23d4130cb3dcb455c742cbf281163d52f0cd51b0", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.3.3.tgz", "fileCount": 18, "integrity": "sha512-07OUEbP7fMX/tFLP3oIa3yTt+sUfDQf99JULSKc/ZNERIVG8T87S+Kt9iu6N4efVzmeMvlXjVUUQcEXKEm0OCQ==", "signatures": [{"sig": "MEYCIQDnIAdFuHajld0dHC45yrkF+qIci5fMartWIlSPbh1nmQIhAN626cresqEyLi3FLW3J8Kk0WhGxcv/yjAICr4Cjw/30", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2SZjCRA9TVsSAnZWagAAO70P/jY502njfzn8xTn9gXqX\nHDmDnluVZryZLSWVC1AqM8eVL2A/6xDkOKyLKRXFVgEXsqXq5TSTDRLP1sBt\nMLQ9N7NYUnjWbd0FGRTuv1cX3ZV03SLIJO+yLCy1As1cFHCvkRaojgq4DIM7\n3zHZr1IGVwO9eQS6cNe++ZHnOv6B+wKc2SwRcRB06TsdK0i+RUMFy/SXod1f\nj8b10Z+Tk0ZYUbl8/Po7x+xZcQM0P1XUhQHEiypKvH2hkCkYTXZYYRc7a96V\noJL3RJ0S4D9nrcayOR+ALCyhNmnzqrhe+JJ1LuS23lgNwM5skeL5ow4+1qHy\nU+zJ4m8FbQ19+G0c3oEGw37Adj23LkTsmGrOHPU9oNaMYorYwnKcdOaGxT9N\nFaX4I9O85ftn+x2SghrlvLEZ/U5QQ/CWyexqz8KBZGVmoCQ5eKZUTQGE98Nw\nd6OEGhmJ7PtV7nn1Lv3/HlzfZwnjan7FuARabef26OYyG4pjrT7b652Wkx/N\nJynWUCOKPemcJyfavvDD27Bp4muxXnvpIcMpN1znQCXBKPTasNwx+v0BDPe7\nQQfyv2kk7s+hStF9yETntKLCfHOO56J/4zhTWh7IpFylCl0Z7X1xubZ83g+T\nmSiLuyn+fZeTvtm1V028UCJ2MUjEf6AFBjtfZB5X07OC8b2Kd0bpuYwQiy+P\nBxvF\r\n=OuB/\r\n-----END PGP SIGNATURE-----\r\n"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "11.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.3.3_1540957794527_0.9847964188976102", "host": "s3://npm-registry-packages"}}, "1.3.4": {"name": "nanoid", "version": "1.3.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@1.3.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "ad89f62c9d1f4fd69710d4a90953d2893d2d31f4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-1.3.4.tgz", "fileCount": 18, "integrity": "sha512-4ug4BsuHxiVHoRUe1ud6rUFT3WUMmjXt1W0quL0CviZQANdan7D8kqN5/maw53hmAApY/jfzMRkC57BNNs60ZQ==", "signatures": [{"sig": "MEQCIHdfkkqMYWW5qzhqZ+H89e5JR07D4Qi81VWbhkqQsdJaAiB4yUASnvDru7Q8/Rr8JcKfEa0Hx4HneTs20WdDUqs8ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3kTCCRA9TVsSAnZWagAAs7oP+QF5jWUwNkn+m28K5CUP\nH6Ho30fAbHiCwhCrfYS4qmnIleQdBQTZlLZKCuqZmYfPN1HHrhcvwPbNAw/I\nyMAXYoYxdxo4Hh5ZjcgapGDmjenKtv9GS4qjJIm8MZkB+lP5ddijIZ9WifzP\npV8PDMIvWE6zXP1HlBWW1uXlGZSXqpyBjWHwObUKIevaZTY5G9pLfTiOUmvc\nP0u5hrIATLHgPBgr0xSLiWdZbhzgcc3adz9UZ8WfpSPa/x5pM1/BwvMR9ghf\nMNfSXL544H5Pnml0FjKVnCiHj5ZbLoeSS59xJnH+HR4znjeCHw8vFFz0uJ1N\nHCss+1KQlq3olZjf1zpvjVs2dVb6eZHthLSldfUx84Z2h/korU7PVo2GiAkJ\nFv5EBwk4S3axN0Y6iu9d5eKqPYbuozF9/XGReNeZiOEPyF5GeHiwljxzlZL9\nn1c3rUjxrK+JRrkILlcFTW0FrpIAhcevVQOcRVJ/FKWcNEH8wssaEXo/OkWq\ns7R120NNynx/J+YoMXcl0ZidRHD3Of7Y7gvFhn5z8+amjzLlGo+UvxNm52ZI\nZ5unKRFtZdDzRBF8zEb5gQfORbIrVXUI9rSUtvN0qG0rQ7p2HuC9CZUW0wpV\nXvt3+pjzXHNVtIuww4Krmy1odJhAmzZEKjlpw1fzQ2DghYhOLsqgSWaL74E0\nB0oc\r\n=6VG3\r\n-----END PGP SIGNATURE-----\r\n"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (143 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "11.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_1.3.4_1541293249522_0.2668378421212214", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "nanoid", "version": "2.0.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "e1ab4a4b024a38d15531ba34a712a201540de639", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.0.0.tgz", "fileCount": 19, "integrity": "sha512-SG2qscLE3iM4C0CNzGrsAojJHSVHMS1J8NnvJ31P1lH8P0hGHOiafmniNJz6w6q7vuoDlV7RdySlJgtqkFEVtQ==", "signatures": [{"sig": "MEQCIDkkIOp0c+bAOCuMIDzPfWSRLF//sOweupOXfVSOTg9VAiBXj+xVFu7Bb/jfcHSXeV1KvQV2OLCgAbHPhAngS7F1tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb31KUCRA9TVsSAnZWagAAkiIP/A9hxuau/6e/YDkH3cSR\nP6tldymu2tG1Zvl9ZfTvxllrFRzibnJi9jkZNlYGVm7OPxtlDdHoGHpdcPno\nKe2N5uWYzkWOCW7za4Hc0M8ITa8X6AbRb3jgWAkDQgF68BoMJQULLhcFlNCf\nhBIntyDsKB+B2/MerxbjK+QVzXBt4hT8PmXepq5LxElID3Cu65MywVtrhENJ\nFdOr7n5ykNp1XzyLFrsB0OzW1/bykOliP9DG1+rd4xWrWhsJM3g1gQRQY+0b\nqEHe4kcs+w5b36xZjfu4KHFA+oYbS5UMEglNA65ZzcL8vGhCzk9pRxot94Eb\nfB+115I1Fo2nNWdNO91sbjwIPUgHURqnHJbDJhkmjYEMEtA1MrFKhiSUg1cD\ni+KLviAarmHMBAW4HqcRU7T128+k0TbdOePT0WOAjPXGDf4UUO3VQ0wrRpBY\nfdnQBFvKhLvIQixDFOjNYoGPIufnpvkQRLXZnw76IKLUx52kdtZBqXhFqHHi\nwiRZJPk5/M9oh5twex3wp5L+PvItBIhje7MoI821xl9q5H71rYnQG/bsw5bb\nhRJbXXdmEXP2v51cExc+4kepJi1JVuBpnaENZcG2DaltsIwi+/CzJtKM5Wvj\nm6MRShJkCq2B/ABy7VfOgOMxof91HDZrDpGVeNRoxUvBYWflafq2F/ZDlhY4\noVz6\r\n=YE1E\r\n-----END PGP SIGNATURE-----\r\n"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "_nodeVersion": "11.0.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.0.0_1541362323554_0.8713292290602919", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "nanoid", "version": "2.0.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "deb55cac196e3f138071911dabbc3726eb048864", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.0.1.tgz", "fileCount": 19, "integrity": "sha512-k1u2uemjIGsn25zmujKnotgniC/gxQ9sdegdezeDiKdkDW56THUMqlz3urndKCXJxA6yPzSZbXx/QCMe/pxqsA==", "signatures": [{"sig": "MEYCIQCd2vIJV1ONQXU/6xxAufMDkOsqHNIbSMhytneR41/zKwIhAOwquzuhjVba5YZYOYeI5ZO2Z6I4upKKxO1dHRAze9sQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPN1XCRA9TVsSAnZWagAAVh4P/Ai6VSWZD6vKmbP28tCY\nzoTsLZdibxfu3caKQid5SfLxCnkY849hj9LvyASAr+9ubD9RFUwMis2LwuSx\nLhOR7Ow+4KkPiDQZ68NrXNTV7TzUFzEy6jqaK2GdtCULDxwH9y6lW4FJ+6i6\nvpBp6vwYhv30SBUj4QAZ0Z0b0GIRli1FFHkY4HlxYB6NzdlXNzfqymuQeh12\nmxJkytTrlr9izgU9OLJfVtx6UQTyc0HxGOSYNj2nijkkig46sd9Oc/GBnmjk\naWPRl3RWs48jpmgwnO12jwJqx7LElaecSQFqUPTopv18etXs7O1QUKLB9tpC\nC+n758pJjzAf4x8qmOuY3ho9ZDPNQf+nov3vBPYZECYI4Xr4uUoBaOipSYMW\nq6MLFBAlo6rxBkm6GqrYBNCoI4xyOU8sFHDy8NO5Q5arcStRPSO8M3r/TACB\nF1a9DpxxwP7DgUg5Bs+p23hpMxa4AHjvnE0SkSUadMOZTnX1xjCnwckq8lZi\naXSTYRE0m+0YEiQojYlgVviYvkAyXq27aU9xI6/J4rN9yzxPgr+yaPp4eJb9\nUT9rgarlzyLjC6Ouei2W04pwRaIqXeETyuA00SFpYTmn1oTiG2bJVjht68Qd\nU4cyhrTwPCbxUDaG+Lx3V93R48HLpsyxDXQ8rIio7o/rcZ8fEJoXlOc4l86k\nD5ZK\r\n=/7qx\r\n-----END PGP SIGNATURE-----\r\n"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.5.0-next.0", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "11.6.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.0.1_1547492694130_0.079394056383981", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "nanoid", "version": "2.0.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "2163edc84828cd42f9b8e4578979a4b5ffc1bb18", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.0.2.tgz", "fileCount": 19, "integrity": "sha512-X4yQ8VHoFvHcykGunT2Jxrsm1c4vH5UKtau7LLJYXO1istCRE3jD8JxDyGCzN+h7dpWBCvWaSYgloRuphKRqUQ==", "signatures": [{"sig": "MEUCIB9AiCpgwlDW7cIp0J95FI2IXVuBN8MTFlUKcuiSTOHgAiEA8gP8IFOHdO2A2RdE2AhExzpx1KeHoCNWhCgVxHvfwOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3XC8CRA9TVsSAnZWagAAHHcP/j0Q97eTctLXU/aQhF4m\n5z7tYsZRYcQ44kV1+F9yDsUktVuErIJRzCjktrLLcnrkNpPci4+HhJCnJiBc\nrrBmUWIXFrG3qO8NqX0Xc9KOleAok433X7OK5cToxrzXoPaRrmQ8Fn0VFH/V\nSFVQ1CnPKVcsucNagokuR14c+ov50QJoBAPVTpC3QmG9/7YF//c+8oioJaew\nKIucvPVCz6ZMuTmIh0omv2Y5TD1If0ZoFVdSDwdxT21NzdYFMS8rlBeZNZCm\nzY8rEFij+rW5m2/OxK6u9mVQ3TWw3V0TX0GfnznI0zZWdZ9XxhU2WksRo2/Q\ncAPLklkk2Y1d7/j7908gTnO9iwxP6yHhLCRDHE8IoCI+L45aXsTunVGiaJXs\n3DuQEddXo459PaoEjF2Qlc1KH8tE4Mn3aU71MnMA1Tnsmq3OKcNOrOchCF+v\n7n+g03pW26BMMd+CEl8HssfXKz78grxr7LAHdzuj/iDNx7oow0vwqq2e4cQO\nn3ySjMSiAc8gwQUn8nNiCy7Vui2yYGz3D3J/lzEGa8vMmdJYpSXQlHMtbTyf\n/EicUAwF2XWtkRP4hbRh2aligLAPGfllDjPVeTMFWWgi1Z+tsePsoh8wiKD/\nRDvnSosw91z07M0AyUcHU6bX1Yh+LyRsg1rEzN//Wk55ma6Yfu0u1MWAe1Mk\n9tGe\r\n=c3Fm\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.0.2_1558016188111_0.5971159974977973", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "nanoid", "version": "2.0.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "dde999e173bc9d7bd2ee2746b89909ade98e075e", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.0.3.tgz", "fileCount": 19, "integrity": "sha512-NbaoqdhIYmY6FXDRB4eYtDVC9Z9eCbn8TyaiC16LNKtpPv/aqa0tOPD8y6gNE4yUNnaZ7LLhYtXOev/6+cBtfw==", "signatures": [{"sig": "MEQCIFHFPRzGMSRAAdZx99utNqjbWEERejiqBSetwyqN1KuXAiBsnau9ny611/VVS1fZSSooMwo58TIZoioBwJBmQlYdoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5FdcCRA9TVsSAnZWagAA7OUP/3SyIzenzfhx/AIXS+hE\niWFHIKBZinBUVSrswcmFI8FoI3YuwqW/hODHC68FUv/8F5j11RgBEkcKQlvB\n4IjNAsWHIPgGz5nifz6JDe4Dkv9Yu8FHHwZc9Z7TZVZe8a8ehTGbrWDx75uQ\n1/NpzEHhsLDfdi+1eqeQJHGwNItWLi7zNeOvyTwCRlYq9TSKW09h9Hf60zKe\nSB0HqB4Q3l5cUoTT1OyALu9l8D2kHdBSko0K585Kd1cjEdQ+qvJVrBCh0N0G\naGDp720BemaAUKGP8oj5GxBwwcHxTpCszZEW11ftO83XgzP376twEW3lvx6a\nXePHj7SK/IvleUvl3D6UczVQu8apCRDmsYp6VUI5WfxIIF7xYvVg37IzxhcJ\n4V4whkysbuL3OVVuRZlWNa7MyAld+PoWXm6Vq/SOgEuhxYuxg88vpUuR6mOP\nvPfLr7Y05TqbGWnUlHAnS9KkbDd4h8EU9Xpvvg3jI3RtTSkCTSC30hH7ErRH\nRCFe+tgCB2HgcHcwqLR8vSUxUmBlD8Ik1KQag7tvJse1psETlPExBT9641s4\nO/l2YNyPCt6nqm3U/Z8bOAJsxx4V7vCoGNGrQW5bj9qghrLkd4lPHPBRavML\n0ODdWukWIZRUgDM90S1XlQ2T3oz092CY8BMkeW8CoDnKrfhnX7iRG2rcX/Bs\nC4mw\r\n=YUMD\r\n-----END PGP SIGNATURE-----\r\n"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.0.3_1558468443429_0.4607820836414116", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "nanoid", "version": "2.0.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.0.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "4889355c9ce8e24efad7c65945a4a2875ac3e8f4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.0.4.tgz", "fileCount": 19, "integrity": "sha512-sOJnBmY3TJQBVIBqKHoifuwygrocXg3NjS9rZSMnVl05XWSHK7Qxb177AIZQyMDjP86bz+yneozj/h9qsPLcCA==", "signatures": [{"sig": "MEUCIDLNRCf0cQkqo/VX5THBV22mDYan/irmz0+0tEbJHb8zAiEA6sE7cYbmWzcZjlCYxgY8Oh3AH9DJrRrHW6VWXvbcgWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYezBCRA9TVsSAnZWagAAEE0QAILcEi/dd9vOW1cxhmMl\nnHYshiOeOrY3LnTNaYQ0mMnSK6BYQPdqTbxGzZv0pelK2ekUNh5Q5e0Vd8of\nBIJHX+hYVpJEU/pp5rgRV5M5+LA3c9AhO32tdOmjqNfRE5kMT/gElVEH0S7z\nBLIGrmN6Rti7jfhxjWXLzSRjPH2mGOYb4NcBOGb+0flmMBuKJF79jujWFqtz\njvkePuX+OfGqL7PKKbfy+VQ3biX6OCWAlMH0BEIEviTvjgWiANhNHSfoALc+\n/tLvKV47It/mnqmwXGrzjIL1M2xRE0bv/8doNdsGWQge848rYnB4ZDG32mnW\nCwLyeJv5ghIfFPvABz1lQ4z+KgF+eSHMBdyBVGLfdGwWncmayNTfUit1NBBC\nauk9kXjqNC4oYavoyMFQtQCba9uuMaSWRHqsTE1sHSA6coa5v5Oook4QUZCn\n1+EqE09hGdA5hR/BpN8/N1BXzn+0EOwLiYFUWLc6cUptR3fTFdHTBq25EHdu\npXnwMliOYx1Gwwqvt+7LRaRIE6GgAZ0eRdmA2uTnUvDN0Wo3dCJcZaxC3S5a\nn7utVLcTSK3GVzewE1l/SXjRVuuoUT4NiFzeF/AweZ/Sh0abqdR/kIvSa3to\nAQbZ1t9kWz41pbsRPzhNWcXY9wXou9y5Mpmf5HuFROygJ3mWah9ruIJLf+HP\nxbRe\r\n=3KWq\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"version": "0.4.1"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.9.0", "eslintIgnore": ["node_modules", "test/demo/build"], "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.0.4_1566698688683_0.8693644828812168", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "nanoid", "version": "2.1.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "3de3dbd68cfb2f3bd52550e2bfd439cf75040eb2", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.0.tgz", "fileCount": 20, "integrity": "sha512-g5WwS+p6Cm+zQhO2YOpRbQThZVnNb7DDq74h8YDCLfAGynrEOrbx2E16dc8ciENiP1va5sqaAruqn2sN+xpkWg==", "signatures": [{"sig": "MEYCIQDN2g0Kg3mSTWNkP16ScSa2ozSylWiFwAcyzKaC5thjlgIhAKWYYjNvrIyQtvAPUJos7EH5tDA7ck8eC3Hr7NsxlMYT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24511, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaaSDCRA9TVsSAnZWagAADvQP/0cHR6XZCJHyHCbdKRbG\nPRGHYs337ZXPxjICqo6iZEii44sXO+fxdRKqVuB039N8+2Mnwlu3JY/IOxpl\nj8KVXWpewWtGNhBPHBL2FtK7Fg3C6sKPO/a8rG8If4LJ1OTOMTjJ8z43jO+x\n709dJH3kQM0FOqxVRtLI9yHLP1KWQOPvaLkgsrqD9ddmgtar/b/hpJ7A66fy\ntC6llstH7evuahHB1dLZBB/+5m0faf3k1VAB8I6V/7psOJ3HxUbtz+rLZkr8\nptjaTARvI6o6+MaGvgMgwLtVytikk8cvXP6ifVkaHhIyz17gp/C4jcJ8QGQG\nOCd4qnnyPJ2g3z8PmiEK0GgCxsElGxz5JTX+XRMc3GJ4tEXxmkgZJG/rQN+P\nl1af6XkjOy1+vNQMtaj2org6p40IIgQAuCzyu2knG3wKmzoY/FUcE+7MCff1\n0aNurQDeJoesjDLwC9G1HqSHs+INGqGPruqi32hyqtAGqdDDewPr/zGtdz2A\nfS535A1JUPt5yNKW9m8R42r1DvM1zwIE+QsEKkMdOZtLjO7zqj/YQoBTQ3Bg\nLwCjGj32NXvKliGFfdHFsw5JkgFmXZQ93Pxuzu8iYgB3ONi7u1sNpjb+i7Rb\nO2xsAyxtLhQZGVkEJWA+x1CiiVH9+mbhZYXWXIBo++UThpFT62hJ1KIYY1ut\n3pyy\r\n=tOJ0\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"version": "0.4.2"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.9.1", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.0_1567204483089_0.6753952131245473", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "nanoid", "version": "2.1.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "524fd4acd45c126e0c87cd43ab5ee8346e695df9", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.1.tgz", "fileCount": 20, "integrity": "sha512-0YbJdaL4JFoejIOoawgLcYValFGJ2iyUuVDIWL3g8Es87SSOWFbWdRUMV3VMSiyPs3SQ3QxCIxFX00q5DLkMCw==", "signatures": [{"sig": "MEYCIQCX23EbtlBtGGPZS/Um4GGZ0AYFhO/SqWJFrrRDmFks1gIhANtB+zUwIaFPKAdy4eEjbhnREF2TFHiWf6f3M0oNuq4Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdeJHECRA9TVsSAnZWagAAhHIP/j13S/Z3HsuTbUZBUicV\ngj0B/2CJ+65wt3PEVq6oX0MNJ2yzdlFMDXnCHPduE0zNsOjN4MtF5Lu9DJVL\nmvYITWp9830aK1HeZUzKDcAAY6x5Y0qy6rT8F6/jYqEk3EXNb730ZJst/SO0\nkqybc5myh/szOcyxHIHayeDWBCePpJ8ycmAWPq8OEKuqXWJLGAlm/9I2+o+i\n7R3RlcxrTsI6oyZSjSZ/8f/JtZdQn9UxzxcPTPuXzDY4aAdgWyiMXfaKGmUq\nudKAIBv+uwX1EpoxbC3gqQZARU0TjIdl5EVFcMVmAGcdyta/w9mvnUT1wmnN\nqokx0QTvyETEQJIpi9qhuxDDg0dO8vBT3SpNHc66+ibDUYQ2Sq6gmGxgq/7S\nTzeK+rg5DFMFuHVwLsslGTlYq5fs/6MStV6MypUJn0RqwtfsFxewUsKgiNHR\nzQnn+deUPUx1uoY4MXb/RmOQI1++aqyyyeEF46CBSYEo/WNtQQzSVvX0tDDs\n03cRZDd5cRJ15eog6quUr6MUycRrLh4g/dEL5TA+49O1K6yvm1NZSBhIEjrE\nRvA5jU0OczhqBfNtx0Wa4OuyR4xJgKN8jOcEDDyLGah/spH9+hzhxEPwV4Bf\nIEhM6oU+855xLSb1qeKaUyCEKeG0PPaWK9dx+/9FgRCUdXWEmF9Am4+UhDc9\nGV7R\r\n=0AkK\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"version": "0.4.4"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.10.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.1_1568182724013_0.9083134902270538", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "nanoid", "version": "2.1.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "5c63a913f4fbf4afff2004c7bb42dee8e627baf4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.2.tgz", "fileCount": 20, "integrity": "sha512-q0iKJHcLc9rZg/qtJ/ioG5s6/5357bqvkYCpqXJxpcyfK7L5us8+uJllZosqPWou7l6E1lY2Qqoq5ce+AMbFuQ==", "signatures": [{"sig": "MEUCIQDFmKX+KWvuLWr3V2vsAstZeLp367/l5qHlvlM9EpRfggIgALRm112dpajw8cGHFkI8YsAJKYMNNot3UMYT+pxnNJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlXZVCRA9TVsSAnZWagAABPkP/iRKsLXDOvy/rwPxkH7+\nsCNSHjTc8KpoV6ow2MCtE2RIN+TETJ0yijT+f4HGO7Rlp7bfwLj/qNZhdmIO\n8AEy4WuqgScGZO5Dhd1fm/kqtUtPbvqw+o/qLFvabCuTlbxrZd2vnw8wsoVc\nR9YPS/NmOQJ5VF/ee7kTaUrwmv5aJRAwM6VM6QRExQE5XlOELbH/nBEr6ILf\nHzjkhrHJ18xq4GlQO84MMJCTxj7aMh2miSSGosHu9KCz+oXV9ntsZq5Arj4G\nvpM8qKJom7qZy9L+Alm1dIuvBwY+0S3ZuL4QvTgZnfdNZ8Ur7jAla6g6jwBB\nax9j3HL72zHrTDbWSD4vXPf6f3ZdJDnSWXj2VvEEjbUnBGicCEknvTtOIXfD\nvrMBzafYAuYZklwl4uqbDzVw+K/HBrqW/ewTb3qhyS8MFTsRgnqlafBi2K7n\n8ny0oQClpj8+KSJQlf+WrVxg/god/yHvJoecGnJv4W6ZWU3vq1rKV40aUhqU\nQVmrDNSWAbaOGTqNZqKJq7zhQjzRDH1bJFciRJqW+0hNQa+xSWNB6QoyCfFh\noYG5tNHRRg1fjQ0mJV8LDROqR1ngwITiPiy+rV1lnYqaSBYrgfCAa6ledvq1\nWAzl7Bw7UpGyxFLxRH6Ni1Quy/F83I+WSYVaO+1kozDfzYBopmyBYerTRQkt\nHwm0\r\n=U6eb\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"version": "0.4.4"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A tiny (141 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.10.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.2_1570076244418_0.4074053319284674", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "nanoid", "version": "2.1.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "5130db537fca20d2676515fe7b8ecf8e22192914", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.3.tgz", "fileCount": 20, "integrity": "sha512-SbgVmGjEUAR/rYdAM0p0TCdKtJILZeYk3JavV2cmNVmIeR0SaKDudLRk58au6gpJqyFM9qz8ufEsS91D7RZyYA==", "signatures": [{"sig": "MEQCIBtaMN5l9LwEVzlePJCenp43b9SfyOpvqHBfhQiPSd6zAiBqWoOGCgLeHHxKvpWK9IyZR5zny/ojorKK7c6iH7ie/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoDXWCRA9TVsSAnZWagAACLwP/0CymSk5ngc8ccUci/Pu\ncuI0seSWJCoc8WTQWv5+FlfGzJGHQEAXhiUNBYKCROPpr87rkeQYsvZqybAq\nUraigweBdUOwmG9mTctiY3ApvcE4FS60q6QoakNFwwXW6tnJy0QCy7mV+gt1\niOL+B8yC8kiFprHSciFaSniEGZMi4HV9jYHTdfOpKR6kkkYczbGcjtzBcwgK\nVPRtD4taudpSOxYFQKGLul5h+b2lS6+okuKEXfSLB/ip4DC6puzWCKwklKQJ\ni41NotF7smqoEkicFFK02KKWYgFI4Il0ru08L0yVaJyp3CI4ZvpXWagEqpJo\newx6CF6600dAGXoepw7QByirfEZVBuGtVbw72RFZ1ky0p2HfYUBIgq7vZzET\nTHo6ZDwp11sTJzQDsDcvgK8g9pPqpjsb5hBX5AeKnSa2bgTkI3V7YwseGwoW\npAPDgDb9VAJJniMJkAmy/0VDd3kntW41zUyrhKi29zObZ4F1ACmvYUrXEj5G\nFPpFJoPmZcFZqxexzMIXtM8LavjKF5ga9WWue4jkZCnf4a7R2Gc6MSbofvHp\nVpKMkxfArmd32hbUC1xeIJ+XAkZisE7u+0iIOfWEldQsTBiZI4J3EMXKK2eX\nK8YjLa21GKQD/TYeq/LZuBEMUT8qxnez3xpnj7em2xuuG0GrrKwCYEFB4tdH\n//SI\r\n=PawI\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.3"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "A tiny (139 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.10.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.3_1570780630046_0.7882589561668236", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "nanoid", "version": "2.1.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "c38b2c1f7f4c60cde2291f40854420328d0d621e", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.4.tgz", "fileCount": 20, "integrity": "sha512-PijW88Ry+swMFfArOrm7uRAdVmJilLbej7WwVY6L5QwLDckqxSOinGGMV596yp5C8+MH3VvCXCSZ6AodGtKrYQ==", "signatures": [{"sig": "MEYCIQDs+znHT6oYQAey0YMYAuyk65yPJujZ2DXabTXgfHh9sQIhALiEHlUkaPmH/xTXTVAqrcS/aj62jF3dX8M5/Fo9xczK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqDMjCRA9TVsSAnZWagAAKJAP/1F3p/PzeIjqe9vQoux4\ngE7hqgMVu/+0ky1oWcYbsox0R80Uc801fh/pVKdBZW90ONKWxZHkNvjE9i3Y\n9M1fzhPXcXyTVVWoY0MleA/wcI9kUXq2xhGhVZYwKNS2eP0PNdDSqSW417Rp\nx8Tx99y9q27WDGXGNlKEPyjReQW8E0ZQaJZcnVdw3HBvFoS/N/K/Zj3HzAgV\nDGHXTkhWeEZWB8GPt6s/C6qPwOBXwY39UKFhgUZZhpVLj8GKxfZGK7WQY5U9\nBVrnuPhey+4m0HIi++azYcmSkTEJC+NUahDRBkX0RIf0wUfUUnJ2N/I6dHTx\nfTbHjw3Rt3XjMna+ZH3A+ibAGxYHKwyGBDFBlDSevtj9pv0OV0euCyFtdye4\nkGmZlBXz7E3jougfOmV/AajSIP8ucXTSZ/vJm5qjxhcb2wYg8k2skHmRT0SO\nRaAEZdn4irgccgLClh66O06WlusoEOxc9Ky9dDj2bJs9lOL+zllPJ/wzThlL\npy2S/w6O6YdtpFrgkiUkZ9gL2RTt3sZDO9OYLEMMMeLhIE5PkVVqeWREdhKs\n0uclspNeprRpfQ+7tiAWK8fMIKvXSLHFm1ihhLACn3x6L2cU1w11hEB8fnp7\ntnZON/1uhHltMobzDBQJnjS1AV4spxHzQoC0OQLJNn25N7thR3iKx1blWDew\ngTi+\r\n=P7b5\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.3"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A tiny (139 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.11.1", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.4_1571304226735_0.08374591227231121", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "nanoid", "version": "2.1.5", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "9fc2fc1b7ede8613c50b40ef78229087ed3a617f", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.5.tgz", "fileCount": 20, "integrity": "sha512-c3uWj6AnYHjbhhhzW9MEFApJX6Ts4XDShNgHPj2OAXgEAmtaxwFDQ61QUYW2exLHsJ1fsare5/3MkmaKIFvKmw==", "signatures": [{"sig": "MEUCIEDVq60+50cvetJWmAxRu/rau+CjFfLqbJ7BgbkBntx2AiEAwABp/mq0ikStQ0P1kandTJioVEkm9pK8zFTXBJgBIZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsHkcCRA9TVsSAnZWagAAjwIP/0RtQ89Le4TW+jodcpPe\nIFTCz4y/E6nWETPHJC8VrqKNRAqBa/u3ZIaNOM9+N3Y0pLMziiMlZttJH1dt\nnaNZ32CVNy0J/rwB8NYG9uc4EfgqeBEPJdLq5rcRAneoI5uJIVtuBrTyOVdS\nJpDRr8hcbp6VUFDM6h1GoyYBu/X7cL89kX3cYwWEyplmMN/fZlLfqP5xMkQx\n2rdTtgZcD0gj98W1DJPWejGHqdaeWjlA7sefP9CjKU/3hNZMJtHtrkTH76NF\nZfiXFNGAnP+B4CpKISvRLFixhVXP/gilv+FzPooRI157Rj/P1CQK7F9FXSQM\nEz0PPrlDaMTnaK9rpEleJEPYVUi61qzcIOVoZH8U8KF5g0grQFceAPmdD4qK\nE0TEEsAkjxNijrZhjaiJbMLUg0NWHRJZWHOR8a3tbNyirr8qiTZj+20yFcSQ\n2dv3caQCHCEx3d5g2/b/tbydGhA5BIlNv1xzuyIdf4saUlU6Uhg7eIhCe7M6\nbaWhsQfiSmqBib2Zy52Z2ButRnjc6gm66avt1afwS1zVRVUGKNjRPIo2/4Lj\n06r9fPXt9i69Y4NB6rma1lqjI9f7ikYIoufDTotcjllKJUTRqzVNIsNAsS9f\ngbgR52FQJshbhkGelkPszbbbPrTdayDsRTWvFXDs0aDfrftTuaH19U+KdM+y\nbV50\r\n=qKYh\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.3"}, "browser": {"./index.js": "./index.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "A tiny (139 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.13.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.5_1571846427946_0.24765974224891507", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "nanoid", "version": "2.1.6", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "0665418f692e54cf44f34d4010761f3240a03314", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.6.tgz", "fileCount": 22, "integrity": "sha512-2NDzpiuEy3+H0AVtdt8LoFi7PnqkOnIzYmJQp7xsEU6VexLluHQwKREuiz57XaQC5006seIadPrIZJhyS2n7aw==", "signatures": [{"sig": "MEQCIDHju81CMIr7y/QwdoBu9whvGiQiSkg2nR54LgpT2w3jAiBiaMB+Ye+16iuIPVoWLtxTjkWTPSaTipKXb6ro3vKWew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsUV5CRA9TVsSAnZWagAARnAP/26ITdqu+VxU08XKW7kI\n1gS5Yiqg9MG7rG6RJOuQUpy/tm+YtRuyvcwaXUl5IYwrAbDMh1A9RXuSARtP\n2KqxSZI6faQR9coX9k7wmNtmMm4VHUMfYWX5qZidOAswGfKoVpIaYNDnEcR5\nRDch0KSL2S5uWry4txU9JLiAuDScXZ1v5o72I+xW64maTlG/M1no6eP3lKtX\nXrd5SDzRb1UAidoUSQrN80T5xnF4G7Ip8HH1C7OGNme0nr6VDvU0U190UySs\nIf6Jv4Cpxy+S7MlBTmcFQUHL4GMP6NLbpNRddgi0nvrKd4ZpJqirrzedt05e\nSqrXggWIUL+3a9Hlom7pgnTMhVYE18i4qvNp3JR0q+QU7Wtd7xslYM/4OWaQ\nH2vA4yaEnzOEJX6hr2CVQYFoJBbwq/2vrtE5ky76VdB90MsJfG4hazwC8Lhq\nZmEUvMbTacyNOyz9esngA15aWHBy03+ylQuhRAFjohCeZtqPovwRYM/VdS6C\nQ7QK0PXnMt5TxCeWCG+FhvcXm9WRHT7TMY3gvHVfGOJfE6lOEUihj8z6q3o5\npuELENfk8g/P1WZ58B41eSyPtlZj/J6Eo059sogwI1wi5VvRJYRhCNZTGR3D\n7ufpEq36wrD+qj1EDqbxIw/TAuIj1ttnKCrmrigDpvagxNGb00E6ke2obejZ\nzwmZ\r\n=ufbu\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.3"}, "browser": {"./index.js": "./index.browser.js", "./format.js": "./format.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/format.js": "./async/format.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "A tiny (139 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.0.1", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.6_1571898745245_0.028036510651539892", "host": "s3://npm-registry-packages"}}, "2.1.7": {"name": "nanoid", "version": "2.1.7", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.7", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "d775e3e7c6470bbaaae3da9a647a80e228e0abf7", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.7.tgz", "fileCount": 22, "integrity": "sha512-fmS3qwDldm4bE01HCIRqNk+f255CNjnAoeV3Zzzv0KemObHKqYgirVaZA9DtKcjogicWjYcHkJs4D5A8CjnuVQ==", "signatures": [{"sig": "MEQCIBQy3vivljGRNsTdnLlgunHcJUiw0cHRDLoEqrj0r8XTAiBCtr8ytxnMVDKjPAoKyrTTDD+S5apBT6pVgNJqgppTQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdz1bACRA9TVsSAnZWagAADRAQAJepbuqRgvm/dUBotL4K\nFto4YRlUFEscrAhC6tb1P5YehS/HFVX1ic7oX57Tg/uztIDbZ/tBjUAN+jcY\n6SfyEF3/nCEpOuEXOChqk1tEihU/3eDMza54345+T0qrFxhi6jlhtnVHlSVI\ncMSLSQjje8YSWG4YusdVDSFaAUqDHKhU0/Gz3S46pQeZfX8ii9ChqnqEJdNv\n9X6mBzkdTbcbTy8/eFrBnas/EwsH0K8SlUgXMe0SnRdmmZuBJLYNasUFtYzK\nD+aRK+knvzhRSyL+MGGu1HWHWaIOeEntk/c+FYoO7+IuAGJaS0EXgnu7ycwY\nyuO4NDDiJzL6bTxlff3mzCjlSHSKxe+g4flGpdLz7OIND8QZj+Fewswd+nKp\n4n1Yj37FZ+dG6ARxZx3rkDS56AHbHnAheJRoXiPH0nMVylZ9KWFKHCgRAcEs\nC8PhNlVj25flk+eFOC8f/RumKfoss5HZX2qNE4N8lTI1gVJbXlsXvsklhrjg\nnsngMY5V3PbyQuCOx4RhYeqiqoGL+bd1h+p2990ffv8hZgqS0lD/dPdnoRtW\ntagMmwC7EsN8lRKy1bOpbG580LRgXenF7LrKCeLKXzmSGalk5DR+X2gUXG97\npSPhZB9hSMoh7fjZKomDAWN+jj5HKKhNgh1vnOn1r72GEBABkAWhPgRP4UNl\nG4Jw\r\n=QBYT\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.3"}, "browser": {"./index.js": "./index.browser.js", "./format.js": "./format.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/format.js": "./async/format.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "A tiny (137 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.1.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.7_1573869248082_0.14390192851838846", "host": "s3://npm-registry-packages"}}, "2.1.8": {"name": "nanoid", "version": "2.1.8", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.8", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "2dbb0224231b246e3b4c819de7bfea6384dabf08", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.8.tgz", "fileCount": 22, "integrity": "sha512-g1z+n5s26w0TGKh7gjn7HCqurNKMZWzH08elXzh/gM/csQHd/UqDV6uxMghQYg9IvqRPm1QpeMk50YMofHvEjQ==", "signatures": [{"sig": "MEUCIQDXy1A+4CbBw6rgS9kZLukX1Xj26L09CDVELvwiUDvZUAIgIQfb8dYqnFEH4b33jW4SoSoBQOAD7/OzJMS9XC9tK1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9S+hCRA9TVsSAnZWagAALXEP/RrOEtyt91OEb+Ip2wUC\n9L1AYzkuvAYRRUIgfEn1Pc6LeubliXy7OOSD56XJMlTGrI4f44lDUyxm6KFD\nCr18fJQaz5eHfY+RlLCwSQe5AptnR1dDjpAb30TP5ISOMW64y171phwfix8i\nyiAWVSQPeNCyzUTNfcewb9JoDKM3wmk05RePhISQvWehp+oxm+oA0i8zf60b\nMAgAsRWIOg1SCHnUXW6uEgVACfY9l41T785sfJAFK/oiHkOAmj2WndYPogNG\nu+FwN/FOLuyvJXAhgQPW9goqCHC9tiqEyyc0gwQ0sMjrMaOn7K+SCOSjsohB\n1eFxWl9iQ9YTCKCg4MKLd2eyFANMpBpGfp5ko3elzABDBjw1SJXVHh76JuoF\nK2CjlYCUuMGsTo2m1CX0OmYVVuBEC471RlozjnrACW6lpy9dzrSqBxwCUKDZ\nZETs5sHWYUctzS1modMCyLX/ZExm+PU8F+ABsvr9bZwJb4LrSUpLIzGQq8yA\nwYhVNjkZcN58bgEh/yOxQkHua3Qt9h5ybkC8elxtMbs1b+KZ/6QpAk3bXenr\nKPOgnqrcyKQImLrtWO9W1YNU79jfXeA6aYfNauGfaQqe/eZcoLgTLJjSjZ7I\n/r/AepkCgCZN1G4y4sMUzviwm+bc2KEcYqEkPmsnmMteZbNggalUMctLodXf\nB5fD\r\n=QUk1\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.6"}, "browser": {"./index.js": "./index.browser.js", "./format.js": "./format.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/format.js": "./async/format.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "A tiny (137 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.3.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.8_1576349601057_0.9914141622590085", "host": "s3://npm-registry-packages"}}, "2.1.9": {"name": "nanoid", "version": "2.1.9", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.9", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "edc71de7b16fc367bbb447c7a638ccebe07a17a1", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.9.tgz", "fileCount": 22, "integrity": "sha512-J2X7aUpdmTlkAuSe9WaQ5DsTZZPW1r/zmEWKsGhbADO6Gm9FMd2ZzJ8NhsmP4OtA9oFhXfxNqPlreHEDOGB4sg==", "signatures": [{"sig": "MEUCIQDr8x0GPP0SErmiwH2GsKurfk8ZfqksAesNaLyVMoy2wgIgYfHjZ7/jhm6u6rzYLW1yPei0BPat2FVNoyRMWwUGXzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFCw6CRA9TVsSAnZWagAA12sP/igZ0h4CyCp7ToSrHTZ0\n+ScGH+KbJ7gyeii9uzTUZ+4vu3CdJoBm9zxkgWriDGMIme9YhU4Spf6TXQgN\nwm7P/2nnnDd5QaklAc1fa6Lamyi1eCZ1MToJ/OdYjMvo8h5xxOYKQcFqbF0b\nbsVgxPDoemrIK2wot+DHfe+IMeCYM/AwhQO9iH98wgur+dNAt4Y/5QdtVLmc\ngoahW9vGyrVW0DbCmtorI9KnTnrNdIbByow3eXHIWgbBxEIg/PtZCKDKgWuW\ngDhE0cLLhe/ki8MaDm8eQsjDMi8ebNzPdqymtdn5//5uyUY8OdKiw7hGJhMu\nvZSYdIhA5FuSkmpNQnfKM/TZIy0El7AsVixDVJspmaGaK2yOj5ABUmuENCo6\nNoHHxVxI+rZ2feYHGGQk/pfFHjQ4Hbd36WcQ6//gFu3sN9IzBbUG8go6dlEB\nhBPO2AcPHD5gSFHzJc4hPvSOAbPxNyAydaG7uzAQgshw1/S+dH4TsUA8EsOC\nlKOSCDN1JhrNzpwqLzpCapen+EoBhKlNuZ8QVzhyhKy83w0vEe0ItDK94bAv\nhI8dBnpt+eS9nup1NdHIh8dlnPn9y1vN219jxzukjkYDpak5EJS52nVrFBnk\nWkn3AqkwrvtLfaoYwv6vEDSOAn4pMFIBbOv7bY+4n/68sLpDciQJXBTzwhdo\nTQEM\r\n=RWhf\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.6"}, "browser": {"./index.js": "./index.browser.js", "./format.js": "./format.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/format.js": "./async/format.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A tiny (137 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.5.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.9_1578380346101_0.1548237817513527", "host": "s3://npm-registry-packages"}}, "2.1.10": {"name": "nanoid", "version": "2.1.10", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.10", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "66fb5ac664ee2d3017f451b9f0d26cfec3c034b5", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.10.tgz", "fileCount": 22, "integrity": "sha512-ZPUHBAwrQ+BSwVV2Xh6hBOEStTzAf8LgohOY0kk22lDiDdI32582KjVPYCqgqj7834hTunGzwZOB4me9T6ZcnA==", "signatures": [{"sig": "MEYCIQC5CXaveLB13B823BoioGLRvXnzfSiFVoN1TxEXqiw1QQIhALmYEnxW950HuKGbqAVNCnLfsXyA1PBKN6b9H3FHg6HX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ8hzCRA9TVsSAnZWagAAh+8QAJ8mdgZpEBrj+g4ou5Xu\nUr4+GH9Cuf5q676fOLtf2yS0Xq8t1UJ8EeGVV0LdNNP5RiuSyUJeTkWk1Sfb\n5JAOCb/jOk7ywIcW6EPQ/mA9yy7nDw1RQlgbi9rktGlz2eFUlQyJ5AIv7u8l\neofHYdcnAlz/d6w98pKqStu8I9h6ARQNbX8KOvwjp9xCGLERQ03jqcLyRD4X\n7kQBwWGf+c3DowGGdOZJ/FWi48NEdQZ3fHAlxjUPE28Wqubx31Zpsnn9tc1v\nZWDcSbyAP7ypkknqK+Ud5Obb+/TicOlrLqwepNfIrKtytkIFqHPuIBQGW+FY\n7UDoBmZXsob/S7x8ABaqUK9mBLPZGUIZkZefHYSKKA5anNXAE6I6kjfMACBM\ndGWmh89X2g8HSJg9bj0IfYe5rLmqh4xP04mSSABiUM9dr3JfYteFg+V4bOBT\nDi962b6b5SPTI6J6f1g+eCJF/ircwHP6rqX/MPvEfJGhmPOu5keqeDu2Ge2Q\neGsf8v6V0nFoB3d+1faHIXvR33eM/ML9bENzzSkjNTSCoQXTyhn9RiElR74/\nI668dcahrlrq543TG2iKqvP0Om3esopn4CVuGX9x+r3WXc37iKJ0z6yGYHoq\nmsC/Rp2xL8zJgNLWXDvGjeKQNWD8+VMRHRLnX0dwq0iAb5ygFVHLZxojPclM\n2k9D\r\n=76kl\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.6"}, "browser": {"./index.js": "./index.browser.js", "./format.js": "./format.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/format.js": "./async/format.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "A tiny (127 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.7.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.10_1579665522725_0.1462596795459561", "host": "s3://npm-registry-packages"}}, "2.1.11": {"name": "nanoid", "version": "2.1.11", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@2.1.11", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "ec24b8a758d591561531b4176a01e3ab4f0f0280", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-2.1.11.tgz", "fileCount": 22, "integrity": "sha512-s/snB+WGm6uwi0WjsZdaVcuf3KJXlfGl2LcxgwkEwJF0D/BWzVWAZW/XY4bFaiR7s0Jk3FPvlnepg1H1b1UwlA==", "signatures": [{"sig": "MEUCIQCxHPzUrYGQqsjfgXdkxacCy+MWm+O01PzgY98Xfvn8JgIgER7eeeC/wlHL41B9icw3Az5vcTDO57MMvd4RG1MkbTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMzTdCRA9TVsSAnZWagAAZJ4QAKH4BKQ1sHgVhD3qhRc/\nf6oHc5Z9O0pBHbGTSQr9rO02tTMpRpJjn3LLJJQ7TN+kOvWXO+u4THSkbUI6\nQAElzMszOVbRLYvUjv8u7X8zSOr1x6bvbFux7xwCYKbYYEmKB2IyqmmxlnHp\nFdOHeI85XWqq/W/x74iZ5DP+scR0ATrXzRJW4R6kZv5q+Fnh60HDRXQqLf8J\nkmdXszgW+ELKJJ2hFmr01j1OWHueqsjbBeJq4oUwqWxZ759tYz8qpnjokGT7\neS+2n8e+Im06lntr3ZaXW9MSaLUHX6quGoHMxUGqF4kvdupC5D0nI18TnGbo\nQ+MY3yeB1oSjh+JEkMO71N/1d9PwdkvnSud/nY/8/kMwDg83qDfrDR53WRlV\nQVd/ze1JHUACgzO5GxXjZiKWZMaCSbQ0+hcqYCcFM7F0exlPCRdWI7n0Ac9q\ndo6H3GTtPDSOIKq65SFlcGCxo3lHy7pU6bpbJjtE95dB6jKGeGDDwQxujFlE\nviYYz23td2nsA/ZgH7GPpGK1bSjGAG25/0RIjRFtXkBdFxRDWD1pT7kSaAdL\nw4RfOa5GQseHOzxHu/C1SzMl2RyrZ19wsFk5kqdYcWPGh3QctwZYIWTifqTJ\nzTfCIR54ZiXTT154wkcJLRr5P0yLC2Co1iNeQt9aa+MVRXj0OfAXyy0QyZIr\naIm6\r\n=9HvQ\r\n-----END PGP SIGNATURE-----\r\n"}, "sharec": {"config": "@logux/sharec-config", "version": "0.5.6"}, "browser": {"./index.js": "./index.browser.js", "./format.js": "./format.browser.js", "./random.js": "./random.browser.js", "./async/index.js": "./async/index.browser.js", "./async/format.js": "./async/format.browser.js", "./async/random.js": "./async/random.browser.js"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "A tiny (119 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.7.0", "eslintIgnore": ["test/demo/build"], "react-native": {"./async/random.js": "./async/random.rn.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_2.1.11_1580414172656_0.010970519141471513", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "nanoid", "version": "3.0.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "760f6d7e076935d086aa3c790e0272cab41abbcb", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.0.0.tgz", "fileCount": 18, "integrity": "sha512-5lU+F2QpTW1JLwlXxnu2/jkpTipIuYZL3Za6TQzM16HYDzZQqlthbXYSW0H2KzZu1UQPGHDPbCl1TS3xOKfqEQ==", "signatures": [{"sig": "MEQCIEvOewOGrn0r6pMq/Lt2XXclJfxG/s9L0JP02ar3UH1mAiA0vdxRNEG9IfqLQoN/15ygerEVlC6tDeMg5u/Ee9D7nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefS4xCRA9TVsSAnZWagAAlYIQAIX2q3VZ4g2bvmBo1iqB\nn6A1tTY+W7I4afh2Cs29dO7ceqlojh3z5xEsPAAfZGULWljrrdwrYXrol3oX\nxTYDzWRsgnIBQrMuJf9hjL8xcwJlZioNjVLFK2D2r4gvCUOlsCpodgz4+X4w\nrFyYYHOmWIaWYzZohH+LY568/+YS1lljDBRW1jpb55+GEypM00AOWkxryV0c\nwjEO8KxwUdwF+AJuhSCeYkIu2yR2i4rvH0zaqI+nJyUk2MPkgoepVoH6o925\nz30LQptRgwu7AnMl57vcg3Xkp84b0YZ+FXEcPojnigeiXHLcdYl6J7ViGOgt\nW8eEKLryc/LXci2Ak/7FOk/PXK+H7Zow/S8gkq7zX4Vd8YYlP/ztKdst4CoF\nrVzDAiSGruZsujssKFSvFgGCvajMIC0ROPRZuDcP/VVXrgNUSc1/Z16y1iwY\nY37H60+FPELGMvDcSWunFbk63tPC/98rRGotajGTHRQ4lRkE9ovcJ4LIv1q/\noGkush68SgZYIUgOvFqBah4IUrQIzrsZofoIBkTfy+pOXyLf565sIJTaNAzR\n2PCvoZ/xWCC2oJhF0gHKtD25wnhcUpCb9sHwKk1TrN/7vyavmIFwLBL1Tw6a\nvGYX40tejlLve8SMI/WPDVCmQbECPBRZz1XnTtAzLrS2+orWobcnHibWzKqm\nMAVk\r\n=//NX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.11.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.0.0_1585262128146_0.03779097236492568", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "nanoid", "version": "3.0.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "8fb66b6910902327d4a47f623f9a3e502d563edf", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.0.1.tgz", "fileCount": 18, "integrity": "sha512-y5gyHbIZQUaim/PrgYLd27MPoDIbGk9Yv5WeWLzFcfDxdV0svMV6geY6G/z/gWrP+OiBrj4SJ+z3Tpyn6IaMOA==", "signatures": [{"sig": "MEUCIAtu1wRfiM4ihM+ivnwa5UNP9tMVVox4JLd948NFctveAiEAgkIaK/reRvn6Em68O5SmC0vr3kAi7k9kImX8jXVsuRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJef6+ECRA9TVsSAnZWagAAI9oP/0veNJOfDlNRnKMaqW4i\nykntis3gUbSurccXOBOl8qiSP5mJK15IZUogZAiJ4xHWqz/AEb4Mn24Sd4R5\nUqU+Yd43P7H2rKJFSg2u5vNXJepfZKALYoJt62+cvbDurUZaTxEKMSQqC8Q1\nAngvA3dJ+HvJCV2XTYpBVGMLV/yMkaLXdWx7SEG5coQfYAoriF+21JMlaRpi\n49xk7Y+m8ZAEu0trD+J1aHrX2TYqYPwLfN3Nnn3oygKLHauxZ3w2b9ldBCsu\nW7sDolV+AZIluTI3zgVwD9Bsd/89yaEO725Y4wH0dOBaBz8q8IxLGSJWYS0P\nb2uEANO4+KL8mlKdmtHHKno81UtDPw4w8aOpRu8RI/QRAc7ehqLuNLeOHIvL\npk4KiQHa/NDaFQos/frvrRWh19f5m5Ci67XNfmZo7baLYYod8g3l5aQuhiNH\n1AHDazIHL30bQuFC9k+lFZAFNhG8l5QYwx0HbUAAddSJPbN0Nv2ZnMmLqfVn\nPDNQspzdhwdRt58iPdn33iwraI5Q5KXx5lGdq114RjwIffc1ZY2Lqauabdty\nncMzCMFGiWZYY5WxRTEKoNVR/eZVALpIKQbE/ZRuNPNtFYFcFQ5CJch8nYzQ\nxserDTWKMkVVqdodIpZWyJnc7Bj1A/tZT0T7fkEqM24gQyY4TEm4UqY2vUdD\nicLv\r\n=zMEZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.12.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.0.1_1585426308274_0.8662236570702078", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "nanoid", "version": "3.0.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "dist": {"shasum": "37e91e6f5277ce22335be473e2a5db1bd96dd026", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.0.2.tgz", "fileCount": 18, "integrity": "sha512-WOjyy/xu3199NlQiQWlx7VbspSFlGtOxa1bRX9ebmXOnp1fje4bJfjPs1wLQ8jZbJUfD+yceJmw879ZSaVJkdQ==", "signatures": [{"sig": "MEUCIBm4gy9dixXNa5k5sDfi2ykGz/6eYGvPHYbaW0xgSfFPAiEAtFH6x020slu5ryeMwr0aywJ+8slYvoGEOKuvK055QNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegRGsCRA9TVsSAnZWagAA1lQQAJJIvaPkdFrLJ2xE8Gya\nHEjjwp7InwQHC0jjKlHMC8YzI9y/g+qBMfZysb/p76o9DzgvJbunijUm/rRH\nMGTeNhoFz++n+xGTtU837aVFuMAdMfUTGVofCPUt/5HaCtPWDPvG0MGF5JpS\nE83dl7/hQCfrgZFtpm+jCJOrciDZRDhvMUZDlQJnsmi78ut150tQGoQoLphT\nPCll4l4RDhsJtFpEKrLkceo28pUQMV65SxrbwYolmJJt7/lXHzmHiCRU6Pvm\nQny/zQJzeR2voqzsoOmxOEbvxZtIY909Hfg5m3Miruo1Lhp3Uwk2/KoP9X98\n0ZpfD2T/Ez9JJf+HR9TMaKyNT5Bacs9jAaP2E+3WUhbT+SxTakX97tXupSb5\nJrClus0GASzwojz6X1Nms21hMHtryWNYQ6AZfqwnf8rJrU17pgSRSFH62NgF\nHvNk7jEgb3oYhU68QTo4Uh/LukVaUL8USyOTwzRK/hw/Fs5fMJZxpjl9dHwo\nFliUykFZUochyBPpWE4S20Ojti/8JrCLjsL0el8zX/C1aQdIZrTLNTcm+L2V\nvG97Crmltp9sWkWaItB5Ci5pucGIX4kaavPaApa2J2wprXhcdjDpCIJPTZ2T\nj+2/8DCiyjbopjGAO6pwotrkjXFBAphIlz4/TRbag+pxkGiigIvMpXc2+EeX\nG3S6\r\n=Hdi0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.12.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.0.2_1585516972380_0.47816375086101104", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "nanoid", "version": "3.1.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/index.js"}, "dist": {"shasum": "4b818e31ff206b43d2edb712bccd3dfe48ac29cf", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.0.tgz", "fileCount": 21, "integrity": "sha512-efPYPGl9T/jOLv92u9SKvjFHSeYSy+8HyxC2cPNEcjDeZV88WnaczMJtz56ozbgfSon8OjiQLGnjze6bb2VQsA==", "signatures": [{"sig": "MEQCIHD/tltvRaFQu13HqKmIXC+OkDuq18vWTUC9cmFqn8nHAiAk9v2a3WZ/l0s+kymLrNBOymvl67aelXfV52xqoCgOJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekOarCRA9TVsSAnZWagAA5kUP/2JrmKVBnP/BARZhz8CK\nSnwv2rBuj/p7atwF3cbCd6skTy/XOfWGmqChsZ+YKsjrfkSRS/YYmLV//HjT\n6UuTCg9IRXkw4Wvv6L1nAN2SqacK9wmtOSnIN88fHHeLCBcVhuSBaW/tCc9Z\nXu185SbdGv6USBgznxyksQsqnNi38H2a8aLER3fu0WZnpOByD9fhFQhYtUmo\nDojuPlzl8DyWeIRzoGNkFP7ScpJhX+RJCNBUJOC6PuCACSrSt2G75GZlL3l8\nDfiu6MEy6EwFKk+1GfYI3bdeBHxCdCL9PCYyOysuzdDg94sOb0QjVJ/BIjsb\nBnKQqERqXDRtMOhyxAdJFx4iLFViz+/HKPhT+hrrwSJldFft4t/SVsdc4gBT\nN3joQhA0DDXDwt31mpOFB1Z8Q7EQs3GZiaybArV6GahIz4GNEPR8nICHopbF\nfAtXFZIFqcnmQkO9tEt9ca3foKyoAlIQcL+QcNQ9VNYC71qvKvmIaBzsTv+/\n+/FHM4JqmsXTcWZc5reZ0N/TAZhVndy5+dw1PWwXdHT8dVyY2721M7YA+CT3\nfN5t8NdR1elJ+2gd4vaJCvW5yfaWWhU7Db2ASSF1ws8O6E+6X9N5Z8fjKD3p\nNg/oZTSTKxUyH7G5EgdUBpa/yfWNcjQsW59Aj/WIq7RVM+2rMYcGZ3m9N5e3\ny1iR\r\n=2Jyx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./bin": {"import": "./bin/index.js", "require": "./bin/index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./bin/package.json": "./bin/package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.12.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.0_1586554538885_0.0005897132977163988", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "nanoid", "version": "3.1.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/index.js"}, "dist": {"shasum": "ada9aa4ce25b3c09b1dc7c76477deab4c59cc7a1", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.1.tgz", "fileCount": 19, "integrity": "sha512-PXqXpihaVr1vX4ygFXup7Upe89ZL1gaFXYwdbgfif+2N6kFDePNvd51Zj5NlkkJP90fkTVlpYgBhpJ9tFfzHdw==", "signatures": [{"sig": "MEUCIQDZnd/qJN4eSDBZTByr7T101wDTiPYRj3WiWJhjfB2FOgIgL9kfu0BLjJntxaS5W1WpKfE5lAdkftVJKdioXYyawFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekOlgCRA9TVsSAnZWagAAJykP/Rm8xwTHBzb7jkhB6J8K\ngGSUYR2uKei6kHr4Z0EfXJFaIcFWVSnOWYDR2JCGnZcgDN4y98Z6Xa44ctTY\n4D/9V2xAhgyeIUnpC/gwLYA7h/AS98f+h2lxicvvRSkkVc591thytiPWIWJR\nmMJcrDXaK4ANedMd1b9DXRe1mSkv1I8BkroAph8udKk6KuXJZ8U/hiLZAOht\nnA3wSiONtoSspa+y95gMcd7CE0CONplX5kPcE27XyoTtYnYixKXC8hAs5IoL\nlJnEetBuV2V98CrdVIopQ/ejuYTzRtvvwekkv/7Uf9Vk0IdoPtMd8ueFP0RS\n8hpzp74JUb+gg+1EEucnq7AxKsNE4V/Mbs9I7gyvd+7wC8wz4oLzDUqa33n8\nF9lQ4kZFMaEUtmNfklcooubWyUyhtSUsquDMlz5loxX/dI8e3zCrSUpTORly\nGKEoi3aHn5+LDKRZmugqkv3PYBf+fHvifN1rNjCmHX/BDdL4c0K0HP7n4+9i\n7pCx8jNKqqvDqk1B9mlEQJaRTZJxVLs7Fhzz2bEhUd/DpKBTzmQB+U4d19xO\nraCukvhFQEt1Da/SSCtykR6Fjo7GokEBSKCmtbdsSt5UpUpi0m1kYCGv70Cy\nkak9By79aqCEOpjUdWfe/5KvUBWmUgjZOlhBmkxUTWnMlB2mPhg6Q9SWyRFw\nvKdk\r\n=l8Fz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.12.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.1_1586555231532_0.9644875205601366", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "nanoid", "version": "3.1.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "6fb5b7a3aabbfbe852145fba4aa163f385d07089", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.2.tgz", "fileCount": 19, "integrity": "sha512-6wk2bzVF3H3D9CiNgEIpK2Z5pN6QzQBhUaYepQjgXwFusMFH6IVgXSxbvAjL7nQ56+3HSf3pNUTWBG4koD9A1Q==", "signatures": [{"sig": "MEUCIESnyjWW8r1TtDFkAqN/akeb7xKp9HQ31ZBlG/XRuWFJAiEA2o4sN8AMrttDF/5WBmPxGiV+dklsJu1/nS53HFMwJRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekOpbCRA9TVsSAnZWagAAeaAQAIHFuPI1YLKcK6E4aNnI\n5mAaWDdnlb6/DFAy5Dx7rsMsxDRNvsBmyxVjdOSP+AcuKz10qT65gDNKz2hr\nZyLJOJ4odjOHbcmfaNMN5+Af+MpO87qcDtM1gkzvfxnTxMHr/VjFhC3FGqTB\nlwZ9oSCqaUmzByZwC7GVlVsmObCSBUSGqwWTx1SWXp7nuPzw2scAd1vp8PHO\nqM367Y15os+hIvTTqzHz72g05Mrq6kQhTuEosOI2jMlwJ4WDJcXZTy8vDeh4\n56e2eqvZ6dmtkzNSnvJ+sOx8jJPFTozCQ43Rzim88PjKFWtytilfkbbGxTPJ\nwG63CJONgRz5HLGykyy/64dqZ++IIZddMnEQWUBfUwi1r1wri3cF/p0ANDyW\npxIARJG8WI4827R0EUGlHBG1JoFu+pXACK6KickHBTUBNVtktxmS1mWvSh9M\n0eKfQvVtk9eF9bxbyEMJTDIZjhyrAgOeCQyKRNV6ocHMLYG1Ui5LHMcIS0Tv\nT8I/TlbqZe/Ax5n77m8hmklp/HV48Ye8b1utIOZN+bKhgX4z88nrKLjKWLwV\nX9vmnp9WRrCTx8YdVuQCaBQPt3lXJywBq/U4amoJGPvlMbnrBJEIfcmP7o6Q\nkfu7ShP94oOh8BvX+bOT3+BAHsEhR1Ah6KlPHVcxnxeKXq5Aght7MEQ4cgf1\n2KZm\r\n=pUJt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.12.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.2_1586555482669_0.07668492250095427", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "nanoid", "version": "3.1.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "b2bcfcfda4b4d6838bc22a0c8dd3c0a17a204c20", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.3.tgz", "fileCount": 19, "integrity": "sha512-Zw8rTOUfh6FlKgkEbHiB1buOF2zOPOQyGirABUWn+9Z7m9PpyoLVkh6Ksc53vBjndINQ2+9LfRPaHxb/u45EGg==", "signatures": [{"sig": "MEUCIERAIeOL0MuXtgg3lPijzGmvJHPCoTC3+vo3fEEZlkBCAiEArxOGeRioJniDQWfvUONoQyQgZO2fN2GbpZMQEe1jmRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekPvYCRA9TVsSAnZWagAA3rcP/1gtZjKbnC5el90X0zT/\nbcWC4Cw8N3tU9UEnQ0K7PeCDLSPxW4GgOBhny0XwjZPc0uVvF8xvfE2thqiV\nCAeEvIxE7o4QD4r7bBJPS2KOTnHUxoGmjjh0q825nEv2Ei7C5dxXKydidtGl\n9Eb8xZ7M71z7EJ6njrKxbvsAzpTsXL99+4o6LEd/JDvN9TVQFwh5OtDNUO01\nmI/BEWZ9meX8BAinafWQ6yctb8jdJ+MbnHXbGyi4Cx7ac5X4PU6n/jlHlW64\nrV00XLpk+px1TtzuxLqzTWmeFEIYqJ7zVKFZx8MHulg5MmeRKPtWkN/Ra1Op\n9VkOsWHq7WpaknniJaZLjDGz+tPrMig5UEfg/myXuDfflNAluuoqg35ewxIz\nSK4OtcW/yp0H9hmiESAa8WOMwUytFoMc0XZQR89RdTOCdN82IBG6a32XFHV5\nA0mBpD672PMHjZ2D35u8JueWkZWpBQ5D5GQw1CgYasWbPpP/pHczYORXmOP+\ncuHvQ+UkH9zkR6+xvKnpGpjQtdBQQ7Hr4Y5AaydLm5SkNXQIHq5vrC1F+k++\nTHDp+ylARQrMOA7E8J4mAhVK9NagHkPRBUJ1jnTcN07kb0X06HrOzRy7SMVu\n/EiaTMqPxxSpj+BpSqLG0Fz5KKktsPl6jNMx7hFbVW4Ip+Dl8NF0P2R5cBlZ\nEPSJ\r\n=aat+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "13.12.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.3_1586559960439_0.15153309224638511", "host": "s3://npm-registry-packages"}}, "3.1.4": {"name": "nanoid", "version": "3.1.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "0c39d96aaf6317b580efb6c59903b95ada1b2ac9", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.4.tgz", "fileCount": 19, "integrity": "sha512-ycTkPSwmUmEdtvPPuszyOCm+Fzw+/AngWkaJ52oZ6l4Mdhh/yNNrWflAzrXbSn3v1mL+GBl6qegU6w6ml5JSWw==", "signatures": [{"sig": "MEUCIQCzvzStHjiEmovlNoyyBfJ01ISvNdSoV7Or1hIy6QBhhgIgZcDZ6mZM/KpetOUTfCJCROMZhehqsekWQ5YAZDW9PFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesCFeCRA9TVsSAnZWagAAZc4P/RshAbuTuva8cNZ4Xh3p\nYI1HmZL7Oo6bd5kUWlbNgZamP4pzM1VU3kmrSizuVG/7FBBqJgmUj++Pu/xU\nJse79X74hszbJKT6OqNGR3ngPrt10tj2r2YubG+l8TvshI1h2Iwc8E8KrN1+\nXp0TvEgo4u1xZZbwCIGC3nnZ/hvBks24V0f6nDQQzDOBRBvTLRDAVta1C+cj\nbrCjeFdsb1t8zIEXMIAkXGROG2q78YRAAylpL5y20xQhD6pVHAzAj4PxvBPd\nt5GtwRxkLmSwKWTYi3JTCWOXw2s8DfApHCfizS434leW4u/PEnE0x2i2g9yb\nxDKmwwmOswSQrqYbVo3KxmmPlGxwmtBPB1xI4PPH6SUfqAzUje3rKiJULAGx\nB0QZ+YKejSy/pdS8nS8xswNsEWBo5OQkLakuomixkp4D3EwolpGkpZ01cPV8\nTMeGhc5nDLS6D1rV3tEGGFVhZpIDhOKiDHIhGe9xhxZdpNpucM0Bi8aH5hWS\ncdRHjXz+Z/ZXI9AOIQaG0BdHMsn7c9/1SOT5T7NAuEbcAB8ZHrMxq6VC3OBK\nI/iyNesNwKmqESUwXBoI0x9LKNmKs6vdFnr4I/GMamhaq69xwoNjh8hHW/0t\nIXtn9Y7sadJfT3ofpZRw7G+ZWO3o1twniWP33WY04E1A1aCbELffxNijgCOn\nMJtZ\r\n=TP1d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.0.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.4_1588601181556_0.5639799549967712", "host": "s3://npm-registry-packages"}}, "3.1.5": {"name": "nanoid", "version": "3.1.5", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "56da1bb76b619391fc61625e8b4e4bff309b9942", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.5.tgz", "fileCount": 19, "integrity": "sha512-77yYm8wPy8igTpUQv9fA0VzEb5Ohxt5naC3zTK1oAb+u1MiyITtx0jpYrYRFfgJlefwJy2SkCaojZvxSYq6toA==", "signatures": [{"sig": "MEUCIQC4N37Cmt7l7nWHNThWjhwe+yMiH2MJA+rOv7pHopyqYQIgBdBAqQih/ccRyr3JyhBmbs1MU0XooZxCkwpZXFUdIZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesaP7CRA9TVsSAnZWagAA54kP/AsYBKb7/WUfCihrkSRn\nLLiC80jAa0kdZgMbvsvQxXjz0rmmYj8fdf37Eb0gq3RR4Afu0Zw+spRw6zWS\nzHa3vCC3r+c7o15EBx+ANFXeLGiGCHO2rk5vEPdo6GQ13Bn0p9MPH8K9Gg7z\nPBYFn8JkPyc2TsPfMmtxoYJlkNBluBMJhH41yhiJQNb0yTksgnhOM/hqqjZv\nhhnG1xfFoeJh+qkJedQyGFSrzs8b5s+Y8/vKZzIH4fFeHAlj7HyDBMyyz+Wt\nBlnEsW6EDKckgLfXhL5j2OMMj9InXhrTLt/vy2OJ+et095wk4mPphN2vN5cF\nlsXgE7eEe9fxoZAJs0DPh0AyQL6fQGWYk/vCej0yZMlvwyuWOLpwxjS+zuEB\nEGDm6aZepk+VG4zBAahvELE3pHZk8oKVRmznJGOdMEtpTwJ0Cz7mxXvqokYZ\n4h2VrJiRtAUfzzt7mwwWIwRsSTonPEc4jHX1uPhX60PeoXpj6ufrurOnBCBf\nsHjkzjRYmehZI17VS7vs1NhLik1AxcRouokHZbYCILekmRiTme/VtisciMPS\nzjdbGy4hvvfR37uWMY5CXVRWZMbdVMmqaRyLjXBOf6TBVcgTHNG9I5I+s4ew\nZX7VKbQ7zNVec1WLhYsoAJFK1LC3i6Xa83wSCADbnrzwpXJp0JkpTcYSQMCu\n/VuV\r\n=51Go\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.0.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.5_1588700153005_0.3175901756259798", "host": "s3://npm-registry-packages"}}, "3.1.6": {"name": "nanoid", "version": "3.1.6", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "f591cc886c14e5b2c4da753db0ac547fac1fee6a", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.6.tgz", "fileCount": 19, "integrity": "sha512-Kp/sUTkDjStVFvT4W0OO1Z1jJWhnxIa1TNJ1F+G27IPC+FOc3aqbX8OMptcZE4vRrzREjttOHut5PbC11F7KBQ==", "signatures": [{"sig": "MEUCID9xIw4NvfWzVPDjvwTkPEkYCwo4d5kD8g3UMvQs4dKAAiEAz3WY1xBC9cYRMEmX+uH9hAYv+zA70XozE8VntbQTnns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetW/4CRA9TVsSAnZWagAAJwcP/0A8Q6dHWzkFESz2DGd9\nakZc6X0lc276VV2NoyVjySzll7YHyfGEcALFk2MTV2KL7ni0vaU2vvmV7UKm\nNgUofN7BBtMy6YsQ2A76IawBeLu/3kxZZS49VVOH8oBjpmg7re/VpDzzKM1P\nC4FGxelcSwmLH5+KudYT+6ReGgdYNd8ePGi8whbKO8E4nfRbJWDUrjGxcBzg\nEyFdSQ8pqoLa14wJ17fYb3iqoPKTAya6XygroOVgnizUdXDlzfSg4Zh6Qzny\n3Tdov3YFA2bTDdwxgO/voU0n6we6l2sUX2JiWKBIfYY8jHYC5yna48pWUG4d\nIahCvYdjZvaTgG7xCGVUdtVnOPWh69jnD5mzMmlp7r9GqQui0yk5aadWk7PP\nW/AwfJTMWyF+NAIydKrYGEEAGGsiQPE6yjQyUc7A7Nryl1oetNldW8I/IWe5\njkmyEAHZZSW8dgQPywNTdXxAahxZYELZvh5ejXFRX6RnMoTt+bH8dQCHJY8K\nUTtB13zCU6tdYymEktkXsilYwHrS2qGrx/6OhtEjQGCRDO+0aXrt+Z23P1pe\nQPY8fn8HIbqI/88UE4iQe5vylldQXHvPTGZtfCmAhDYBZz7YCJ7nHOvJJ57D\nNIX7AsXl6RYSRAPUULXqhgb/ShU/hPJJtjVKo0UP3iiKu35nrTizj0jZ/sva\nNXlF\r\n=Rakm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.2.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.6_1588948983724_0.6007441992946465", "host": "s3://npm-registry-packages"}}, "3.1.7": {"name": "nanoid", "version": "3.1.7", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.7", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "3705ccf590b6a51fbd1794fcf204ce7b5dc46c01", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.7.tgz", "fileCount": 19, "integrity": "sha512-ru<PERSON>wuatdEf4BxQmZopZqhIMudQ9V83aKocr+q2Y7KasnDNvo2OgbgZBYago5hSD0tCmoSl4flIw9ytDLIVM2IQ==", "signatures": [{"sig": "MEUCIDE46S99Sr+fYe7q/Ztw8w+Vx7u4nYwBD5vBLxCHZH+0AiEAtrfjSS3994STtIUs0mnM3ZtNGXqYKnpw2XdAnmkxBGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuZSZCRA9TVsSAnZWagAAloIP/2huWPRpqKOrpq2KUbE9\nt3BhdfOUIz9t9/69wpI7Z49mmfclPGtmTli4XAiv32Bc8K6Fld0jq/vrE0I7\nNxq7d/O9ixqc14bQ1Tev3Tb45Q8yPzhQiyUcRFyqBGerig6URj6qMqodQCD/\nE0+uYX4QuNUmUmOpGpCZDc25aNckTgZNHKlEMSdUrg41hTzTjPL7WCj1avdt\nJW+tSAXYyM6HipNdcVi6z9Tzf3tm9g5+21g6H2+zrPp8qLC6pEb5kyoyTsAM\nylfe0qVwbFbOKVSfBa+TO+0J/SttmVyqEEXqvrigTuASA18b0rvUguph62wJ\n/jPuW1dIDqw/ucm+0jaUpV8u7lriYXDvydNaD8tDHGJwaDtSzIIPEYrelICO\nX2RqBDD9pB8QymBP6KZLN2a4vdE+tlQDKZvn9POt2yA2gzTNoGcJJ/ipQ0d3\n258Ck5wmDCzTCgaPl0Ltc6grBzuoUeXhQiX+/ZDLPW76wsHl5DewsHpKnnZy\nWHYYJ8RHsasI2vP/Pxr7uA/9sUgLqYXT1+kxBWMkkROQCCTCw72CwzmEebw4\nUrvgiVGo7vBh0YLcX04NNJFqFUrBQ8TzFiEUHIn6IJHonNUfuLcdt3EH0gz6\nmdkyN9heW1zjORUm33ApBWWMXP6bK6/1ckrunhK0JFV4p+qHfVik6JUi8GOf\nmu59\r\n=TIFx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.2.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.7_1589220505304_0.2585582294615454", "host": "s3://npm-registry-packages"}}, "3.1.8": {"name": "nanoid", "version": "3.1.8", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.8", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "de6460f6609e8031c161a12f7dfb09bcdf7449a7", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.8.tgz", "fileCount": 28, "integrity": "sha512-+kITo+bixOrnHjsPsrVa4gvWRxoSugEvCVygQ1q2NprxQcPHI+v9iID2zaGWFKBPkfuUiARIdWWIQonKdf6aQg==", "signatures": [{"sig": "MEUCIQCvRPS2VH2z8pPXcaqOiVQG01DoPyN0x/XMZXr7VJ39fwIgTr25BGtWZfEw9rzi0/+/aZs17FmW8M2Gp4dXr7RwhzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewDa3CRA9TVsSAnZWagAA9pkP+wbaadPRGkfmMXeiLtjv\nVkLjMRBKlRrN59dR0vSkcXi48QnSvtrRqiqh7MGQAWkBYwY+hj6GydKnL8Vl\nCz6HyOqv0htUfZrrcyGqa8YD4ppeh74+Vt2HosMTdH0gNxH+Apas0jKLZ1tC\nqpQW37teU+uh+lQlVt/192UnyFdg6jTCSGc93+WBDWPEK0IPGDdmUC+gus0K\n31UN74o+Q2MeEZGstM+jOcNYoZBO9J9oE0ZloZRV4C7T/NlED5scDhx67Jgo\nwnKfrZV0K6N8hfAdCrNgX2K9LU1zRv8IswKAcI2dKpCrdCZikjYvfdz/sFFK\nK28VEGWbtRteY9KHfj4L0c5Dku0o1pnXZwjX4BNSW3HcWUGkFKGjdwuOUrQ6\nuse4fQuc5jt6QRHOfDpxoPbkUL4TMYI33ROjqWd8h2mIflUhnjVZk2U+u7pF\nEijPuVmiA8CROpFdgbH2J4CfWcge7EXzl7DXKniXfaiwZfGYP1Y/TE8+kbpQ\nNVxryaRVFC2rxERaiscNTpPRJkdRJUwff7c5OxSyyRdC3G4WHTc/x6pLPWoW\nTOaox+V1mKzLEYTCBxqB0pT9qSHajc1HiaOppcT5NaLzMYOAfqS3sBvKWHzx\nT1H4VMglnVgOyjieNNdpj+X9eViAVP6hIid8FASHTuy2+WPFlAJrVUdSQT02\notZT\r\n=QUBv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./async/random": {"import": "./async/random/index.js", "browser": "./async/random/index.browser.js", "require": "./async/random/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./async/random/package.json": "./async/random/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.2.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.8_1589655223100_0.8304574294914713", "host": "s3://npm-registry-packages"}}, "3.1.9": {"name": "nanoid", "version": "3.1.9", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.9", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "1f148669c70bb2072dc5af0666e46edb6cd31fb2", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.9.tgz", "fileCount": 23, "integrity": "sha512-fFiXlFo4Wkuei3i6w9SQI6yuzGRTGi8Z2zZKZpUxv/bQlBi4jtbVPBSNFZHQA9PNjofWqtIa8p+pnsc0kgZrhQ==", "signatures": [{"sig": "MEUCIQDvc2DqD7l1xX75eVp2bKAmo1rjHQAlBH16tX3EAQ3EuAIgPavlpsPgCt4SpIbSlfkj9s+dTp4TTNQchFjFWKsBMr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewaVHCRA9TVsSAnZWagAAlNMQAJ7mWUZeMcr8KADUPYOg\nUrpIh/Ri43+C2Gwn2HP1uTYnmz7niHNl0L1AqKxFaEM93RoHQKKYWob6d50c\nfigM8XVTkKRncDwDZCDP3OTAEuDgaNcBX5OJkdNhmywhuwh41Lwc53VqGUVX\n+Y/i4IaSJOSXlGpp7E52waXoYKPPqssEig0K2kT9BBqZsfJrRWhk+4Zr0Efu\nBrQ0YjUuyphcIwGKtCY4UFGrIeA9CddIJ5bK2SRRYBXyJ2YDSKJXdDNVDJW2\nNbY3xfsmUwhoo943SEMFSDxYrFqf85ybd0KYsXNQGedD+LBCfmOKjE3yTZ/E\nHhNSZQks3brcJcTmnSMkZbKNjdOFN/sNEVMwB+4LL9tZeVOAMCP3FU0Zz0CS\nH21nlHBPocf1B31wNpW6VmO3T7DkRNP6yw7hcUDIVZFT9j9ZZSE0CWOVRORF\nrBV6M66N4uCe5MagMfzakBvt4EfQ1j5Dmi/LwYbVUWMBvGNP61yTqciDRG8f\nNo02RrTj03Q+X+UhMCdpG/FNJI+GaxxZ4kMUOhsUW2Fa0yERZzJbc0YC5I9y\niTYBwbO0rj3IYpptmN7eiL9THlmPPoHDDPyYeXz9+1+sqIEgpcmxhnL49uwS\n8LajTAMBBCwEbsfL8QclSoqWkBHJl5KHWCrfXeuMo4LArx8wkWltasUHQio/\n9Bl2\r\n=jqYN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.2.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.9_1589749062469_0.1790624617221399", "host": "s3://npm-registry-packages"}}, "3.1.10": {"name": "nanoid", "version": "3.1.10", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.10", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "69a8a52b77892de0d11cede96bc9762852145bc4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.10.tgz", "fileCount": 23, "integrity": "sha512-iZFMXKeXWkxzlfmMfM91gw7YhN2sdJtixY+eZh9V6QWJWTOiurhpKhBMgr82pfzgSqglQgqYSCowEYsz8D++6w==", "signatures": [{"sig": "MEYCIQDn7a1ZQ5iapcxU1W+EeCUgjNkVNyNTq7GB9OwEIh/61QIhAOE2NRlhrcTC9nEkoSI6dPkWXnjqdzUKGM5ZUOmdPykn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5EH3CRA9TVsSAnZWagAAGMsQAJR0nkjYSgkj/Xjjyz8T\n23rFDalDU11Jyxj6JjIdQxp7gKhJroDzKECUQ1wpDnAsnrZhyAwiWu7ls3Um\nu7eJdJrevLtDuVT0b+EO86FhDUbuIXWmuPUzTNkIjgEpBrNrRDAIGIpmxq6V\nr6xk1gp6080bZjpVVlYyadTIFnRYJFgsuLw1R7onjrKzvRXUArSS5ssSSxvd\n2FPPvljWp7Zm3Eozv37zIumW03JaYKyo/izJFmnv2wc9WW+K8xbQCrvKeINJ\nqh7YYUsHKV6ZPmfb5gjsExXNP5zefwcfSHubbYgkD4ukepxSvoay6tLKdnEP\nLMiXho6HZUpE+fGqKy0kLp4BEwlsCZx7ba7cP7GHYM84+CIxam92DKF4DhwZ\n57OCy8kHrjqdApk5w5CXPW/O313R1tphZTYMfk2KKeu2lH6YOGWTa8ZX1P8x\nvkR5OyrYHMjaZoREilERKESw/z1YaTl6UiWkUW1BSLdFLt9hzrrsVOc0PMGE\nheqZh1lUSlVidz8pHleCZnfj8r07nGsAIIvtF2kLY/qUxxZh0+PqOq6KHpx/\nJ/ag6UknkHChsVxDug+G3CL5g3YrCtCML6iYSI+wmtMn6Nl4NMkys6jRP4Rs\nV/5nc5U7rTWq3nWYGvXTJO7cL0l+5n0TAyhqqkkBJP2sCDwZx2E/5HKud/rF\ndoZ3\r\n=h6Iq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.4.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.10_1592017399051_0.3857493971372794", "host": "s3://npm-registry-packages"}}, "3.1.11": {"name": "nanoid", "version": "3.1.11", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.11", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "251516846e1b5c9599124fd9324c54ac1c9bea6f", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.11.tgz", "fileCount": 23, "integrity": "sha512-HyrIRpCB2uS1/sEoS7/UIAM/Jn72uXf2/yWgQRP0e16/UdMsfH/1JyLj2SqyQlkMkeeN1ad61tpT6FPRRimxnA==", "signatures": [{"sig": "MEUCIGs/zMQnkM8eTb2lPIoOkMEGlJVLQ2J5R/Lx6SxpWLTDAiEA6fEYQox0GYOH8djVPb+LclG0/RjdLH/7fE3fMqabhZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHxkuCRA9TVsSAnZWagAAd1oP/R/cEGslir6EPZACYgYC\nHVZ/Krz3COpvyZQIDDcu3OQxNe6Zkn0gZpD2+5LkchfRiWmQ02IXSO+3ORp7\nAhDcj3ZJGy4shM6Hl1+hmH0+wVsKyZ7vKHNbc2hCC7K2jlb/gO+H/UazVe9L\ny7RzJlMRn+Bq7bcB625B2JoEiGGfhX3dN38FksYfZFhR56DGp5mUrm7f+G/B\n3gl039nz8G3WF3VEfKn86DDohP0BNWfl3JmjMGHERMeUEnMm1KlwwbXUQJy0\nhhpfaG6is2meN72qaMhK/ZQcxCjGYU2Nnzk4EVLv+xUEWPIDfI5r/uiK7Pm6\nR5/ptzXXCYg2yd1c8waX3+4MsrL09uFg+66xBebkxbIUwzQLa2wVM8y9CwSm\nfFGH2accSwZ4SD5LJq30+pLj56idpZofJEiAJdoabMdK8uTZ8VtNeI9yY4Xk\nSh5lElM/8bsDx32DdLpJ4HCPsUakCuWMzZEWDqSBg/b3oZ7xwh0meaFV/0w2\nXLGASjCun0cQrmMfRY6KE2ryxCyTb/IZU9NFzKKNIultkZmgNq9GvEODqfNf\nLxKQou/MKFu9AdgK2JUTcQdsaS7TMQYe3dUIDL/CQQT0cf3NUt4XbKTF8wYI\nw/QwmHYbrO7vsOknsTUVQQMw4an4OIv58+MyTAbFwGtABqRwXX7GuducpNCS\n+DL6\r\n=wNS3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.5.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.11_1595873581835_0.04448193462339933", "host": "s3://npm-registry-packages"}}, "3.1.12": {"name": "nanoid", "version": "3.1.12", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.12", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "6f7736c62e8d39421601e4a0c77623a97ea69654", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.12.tgz", "fileCount": 23, "integrity": "sha512-1qstj9z5+x491jfiC4Nelk+f8XBad7LN20PmyWINJEMRSf3wcAjAWysw1qaA8z6NSKe2sjq1hRSDpBH5paCb6A==", "signatures": [{"sig": "MEUCIQCfgqqgp6JIG7kQyiC2Trq24mp9pSZmhVIqqtowvoTRNwIgUsEOK2V6+MoqeWyXBJT08A7oTrSpkdJIN8AR4ZxR6tY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIgBUCRA9TVsSAnZWagAAgl8P/RAvg7egXcLA1pk+C/ZP\neV1MjLKovdIDb1+kigW9Vrh80Zc5q1TQVYJoR+6rOjN+zGNJLr4wkuI3BUUS\noCyqm2JTLMAzHD0lgqAmW/qpzNrRodUeVXiqas+dwD+/Mz0ZWtzOJY7mWpIg\nCuKzY24LIdXrf/I/C9qIgUYYMljbN9JMPLXQoqSep2xDsJxNKQfswlN7vYfj\nKRZDvLoElfuRBwiYXLm8TGKySvEg4z5+CPbsp8WjQNW/SW/jbJB6ACIS3CWm\nZOQmgGbnssCpz/uKwO39dHZKRWjjieOw3MHjwhAIZcwZ1M/axYh9irI8kUin\nusebXozaoJZDjD356tX1xjbPuyfjYg4lOBsjchOueZF5R+zAztK+8jlT5wk0\nBso0g/7P4ABu/HCOGWfDTCXvuhHtQMqunJlb8H7bDTARG4jHtEFp8Z3z9DVh\nH6LGfIWXxZ3Uf3xs9Nf6mt3f3dks7wJR6jSNofCm/pUn3g6N9Kk3y+D64Mz/\nPLN7myTVXTQ5FcXIXsZXE9xylEWsJmxrCihl/+c5XqwiOHBDI1lMnDx+Hd7t\n6nmadmyLrBFcO5xp4787QpdQX+Qa7o6cpjYbIyGrA3thHcNFdkgJ9YS9WT6n\nc8uUN+jHDzn/r4bT/THpBBJXhWLx+1QvaAvCPYn3R9SjoZm7Ek1FSX6PYeqS\nSuiB\r\n=TVLK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || >=13.7"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.7.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.12_1596063828286_0.5950554700339199", "host": "s3://npm-registry-packages"}}, "3.1.13": {"name": "nanoid", "version": "3.1.13", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.13", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "7f4f0729ab8a824291deb72b0d10cf357bc7563c", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.13.tgz", "fileCount": 23, "integrity": "sha512-oYL7jWZUdScASxYOrcwE8EvISFGzO3/1g+t56vCyR0s2nrpmBcOc7hTAFJaVf6HMyEPJrnNelnjRnMN6KZnCPA==", "signatures": [{"sig": "MEYCIQDskqwpQ0ZVIJdO9Fh4HKnLi6p7qlLBLQXXYzVDBKB2OwIhAJz7eGiR5TE7E+ytvo4wesltdjy+QTsaVZaxzqZqv11D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkMS3CRA9TVsSAnZWagAAUnwP/0NkTJiG3xgyxmt7X+u/\nrumRwplUvzFE4YoS6Y6B5f0e5nKCmFGhoU12jRUj/XR23U8mqsD9MVlPK2u3\niQiS38MBW7DS4bOtRSAIJquY42uo6lN1C4R7Kr/Jj1kl351duoLhyzIfS2ZU\neyZlcUKnusBD1fFd5/M6xk2UEbXy0RsCwTEkPU2gntSrEo4Qo5o+pOh6Ogwe\nV9JmWdB41zxY2npaJmA1ltuTDr48cHHLw2VX1+TPSjbIyk6P891O8+K2Uwgh\nPBSWUq7s4PjjK5LSL9NBuCr9JzTwmyeYIdVjBBJtAqdwE8TMym505v2MVOb0\nb96hCwdI+WSmsKScHyhgSj7ERJbbak2uJ1apEXRSzrmLzxad1CASWdMUWcGQ\nKsevZGwe/3NjHCunx0dbJR5B6ZXN3n4mUA15MlmamTam3xf2vfKVOXMPFZUI\nSKJbtELeTrZR46Z0e0SNarg9UJh0A7LbqJoYHbP2a56d1N+mAIywfevfmXCj\n0H3kxiPXncp7tjETILcwl7PHWjSK7WNZdz1EEez9gBgc/5x5gbBx4hRSDG8k\nJvKiysSwzXKFLg2xYbGAUYf2BpJ2wSsfBPCGh1iZfvsVMazUyGzvoo76IQSh\n6laKPWVlHdwJRZJWyNYbYTcbl2Zobmd3dS56oeFVJpNt/W0Gl6b0lpf+NHl3\nBM9r\r\n=RXud\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.0.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.13_1603323062859_0.9966481788960779", "host": "s3://npm-registry-packages"}}, "3.1.14": {"name": "nanoid", "version": "3.1.14", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.14", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "ede9ee0b70e3f757f31ba3e60e8534fb3be8d4df", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.14.tgz", "fileCount": 23, "integrity": "sha512-eW4inyfnbKQ6tGwd52HXuMyjexTQ6UClX3tJidRa1HCgWTyWC+Zv6JM+r7csBnPObSLUBPptAhfyxEKMT8N2+Q==", "signatures": [{"sig": "MEQCIBl/sveNMjoHZ3BNdFpkHZK/Asi+ghP6/d0o4h2iC7adAiAo/1Vmn9BqmhhZalfg6pJGhHYuv32imdvD7udUN15XTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkjhrCRA9TVsSAnZWagAAjUwP/2wv1Hc6idf0PsYsT/i3\n1bdpaRCobThnIimF2pY3zTgGFad1feAUria/0feNz42slH6uGvGPTgm6hjhk\ny9jGZlnv64TGMBcxOK17YNQkRUd6aQB0pfJ45R1Q5DX2I71qPufz+HFDUwNS\nD3YLqk64l3Hh9Y3+gCKKxrWz8zUzCMcefgyPnMwh6L/7/csAEL0pMawk0nYB\nSdFTFPoJFGxl0XluMJLyobJoPvit+MyGGuIZKZ3/bsiiXgw4TR0tiTIIIT3f\nP3Jy2EIqvz30aibPOGD52KGaxyhg0j/lLYbC/ZY6qob3Wxrf8B4J4XtnTOhU\n+EbgNenDKhlhmkKSHycssZKSol6gC95mL0TYnxNxVpluoyzNQqB9iTbhH2UE\nxFoTy1AvR0zIbdIHN1PGPjKBDO9+zMrWgD1u+PJ3ag0A+VJTsiK+UCWEbmJx\nXA/3hcPN6I19dbuGb05x8XvMDhiDbW6xzm/rR2eK1bCHENle3a2wbxfOzIai\nU0GQHHktjZf7niMZkcY0a0ZKAcbKLvxXxVBOZq3HUTtOZ8Ei4rDpNIi9QbMU\nbp2uUlJp8wrlDQzL7UztW05apNe0zCF0vGIcjhp5B+ZTLUKImPjqsJplTEX2\npxZ9PBYogGNQQ87hgLzEtJ+L2XD4Tn1AghiB22DsyTem636qQR302HiHXGxn\nHo4H\r\n=aI+H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "index.d.ts": "index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.14.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.14_1603418219123_0.16752943561812117", "host": "s3://npm-registry-packages"}}, "3.1.15": {"name": "nanoid", "version": "3.1.15", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.15", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "28e7c4ce56aff2d0c2d37814c7aef9d6c5b3e6f3", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.15.tgz", "fileCount": 23, "integrity": "sha512-n8rXUZ8UU3lV6+43atPrSizqzh25n1/f00Wx1sCiE7R1sSHytZLTTiQl8DjC4IDLOnEZDlgJhy0yO4VsIpMxow==", "signatures": [{"sig": "MEYCIQDbZ57DxVCa2EN9atgThRN6lLoJi8DUEX6V7Sijgpa5EwIhANWLodAtd/6RwFPwBi0ALc/eVBgs3XDhdYvt5vnkgBEo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkjzwCRA9TVsSAnZWagAAzAYQAJJD2yJIhj7pCb3XknHB\nKYdEUttFzaLZwcZGoe5Ajl1w9SAei3EPoBYyeedY8rH3h0LDjImupyxOsDS0\npwWlZfU8tGuHu+rdfCqKsjdWY7hrq6d54nnM0H4PmNJfY31W5sGYLCp5qap+\neGdfKoN2piyY5Jlw1HGEnN89FKVkus8bhP1GxFqAkoMYJN7myU2+Oimd7KvP\nTTWxyC7iSFFkOTrMX7r8DHrkhdXlvDXfsNf3r364QfQZl6Y16wF1wt90Oa9j\nzXnb4hZKwJ/umCvC+67S+6ymizIR8Q0osBojeoNndYb/T+74UXzIIF4IBKDI\nyXWUZwJmyrFgdIlHjxwYPPJyB0fjcx+VQHtinDvk2g7XXWn6kJBrqbHGrhXj\n40PnLageEKjStsQ7tEtmMldA5MYc43cpzR8gREryDMG4tF35NuBJsCJZVEAV\nApn2l3iJcBahxluoytzbA3uvKuY+FG6rSDYuaRpg1ND4wl8/Iruyr/vf2PUy\nbAAhuGcZ2W8L2V9g3P5XpGX+JxUTzmjofeUYgF9IX1kGJ4TyU8DivScUwjcm\nGhsE3kTnbIitIVvrVQ+9tq99soEOcxNy1D1vjek5ndJ1H3qmcX0F3zyH95yG\nxJokWM+kzlTt/NMYSbruf/90LsyoRyLTNKWHUPP16zLPukTLF3oaWYsrS4kT\nfiZt\r\n=VEHb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "14.14.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.15_1603419376043_0.4687206329838698", "host": "s3://npm-registry-packages"}}, "3.1.16": {"name": "nanoid", "version": "3.1.16", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.16", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "b21f0a7d031196faf75314d7c65d36352beeef64", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.16.tgz", "fileCount": 23, "integrity": "sha512-+AK8MN0WHji40lj8AEuwLOvLSbWYApQpre/aFJZD71r43wVRLrOYS4FmJOPQYon1TqB462RzrrxlfA74XRES8w==", "signatures": [{"sig": "MEUCIQCJ8lyulbaKCVXzJE1qqhmnHUgrPmEYlQkQOO6IEpV4igIgTiy42aVKDBqvHhguOezuzrf95lnk5ymiS8hF1Mv4Y+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflik4CRA9TVsSAnZWagAA9W4P/jF8NJS6wi9veKXvJCwY\nYdwo+xW7+9+Mk/2EOBnItUj4P12T0/yuiDlEEJETu0NNqIf05nW/Nm/+l37Z\n1xG8j3Heh2YVcRRUVV0w8Vppr4ZGchVZM+H0uw4QulXbssUny4qZg3s+16Ma\neO0+aalVbHJht7UH5OyL9kAmJgnq/+0CKkXyFn3YnL0ZN2eUaD0u4v3i5jhc\nvFg2sQqu5Vha9F6wqp9ChTsndUDq1sLC/RlKk6ULKaWTuFmMXA1fUYcuhvTo\ns4F+LOiN+BbQbTzNf6STetRN+ppXQ89pa/gv9a6o8cuKNUuJw6t6KcFpUh0V\ntonus3DAP7iT5+0eXn0LpXT26STtcLFebql5D0oPcsPpGfbDYK3eDJYWmMsN\nuG0/oNk+CkCTQwfKUfOP3iRbzF1VMwcGli/fR9ePmDG/124mPVj5ktC0IOgm\nMGpsB3f2AIQSTT2fNKmXVQ37Ep8DsSSchdSr6gDPuUkWlj/jC1cg1JECv0TS\nwAkg4eyaTGIN0WsLu4XWXNb+D/LhTgbO2+mTtfchNLT0MZjqo4PM1KWWVddW\nDKDtwSmoxEuQFzuPkZ1Q29I6LNOgHTKGvRhIUlznePXsZn/uQV2JL5DYzTS+\nQIjw/Jnzw2DbVFx2wFpFvTSQ+1eMSP+J7Lhfl1ygFU4ykzHp97scaRekfwuv\nteDD\r\n=zAR0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.0.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.16_1603676471966_0.11836214296246816", "host": "s3://npm-registry-packages"}}, "3.1.17": {"name": "nanoid", "version": "3.1.17", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.17", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "948d606594e925dff72076201922f6e22d4cc543", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.17.tgz", "fileCount": 25, "integrity": "sha512-5mmlQz73ohlISpARejqTwgYzh92wwBccatETsLOI+VKkY6Lx/Dj3wvG7tCoKjX+eEaZWn0gB7Xkfl5JatHQTeA==", "signatures": [{"sig": "MEQCIDscxWjEQXGyq3pDaFNLj9fChh8aE4kra7EkCcNIUuB7AiB5Zm6hHAkHLQcb08qACLatHTQIS6fWzoR2allpOC9nEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftwenCRA9TVsSAnZWagAAknMP/22WY/tBv8hrmJMpXGe+\nfgj66gsyiMmf7SpaD6fPPiVNcbuMIe7qb5RoSqQBRPCUqdeVN1garZQDBbel\nutaAqSK3cSFwtPPboUv2MAJ8ceSuKU0DMfKalgKQWmMh3ZpA8leakyaObTfs\n2ppvCeJd7URNNpkUvGEbu7yqXpGIxqZB34KsvyxYxXw7DkvGZMYtWKpr81Gd\nez0Vk7AFQ5djVFUU1bMAtTmr4A4mt8CmMW+8cssq/j1KWHd+hVrrh69sSEYn\nzFnVJwlJwCJodHmsAqYzqJgRorpCsDWgHOSaPzeRYSTNUubKrP8ZXLB3eDgz\nda3bVGovxMZWlusb4t4K4yh/fjc+8kbzU5OB498HeQmgVW2ZDjW6z0VMs2MK\nbp0Msw/WkiDrfE51zKUQ6uwkHDe8tBORI1scflqVNN6d/saD1YN/WpSU+J3J\n6Cf5pFnWdunCFlF9urK5Vk5c/t2QiWYTfEYi8UfVm/3KAdMwJFQacs5XZHvo\nY/juLn3LgeY+bnLPKbe86jXqkhreKzEh+i4Ut/Hsv1MVQDZPjQFsfTS+Whim\nVkyW87Lz5dhFGUJ3uy/lWbjbMy6oZka5Vtm538TpNWwH1Wu19JbYGJ1bBM4W\nvzZ7S8DH+NvJHn+fcQvh1G3lN7hF8/cSCOyZ6He4VjQyT3UfzSPWHq7gpPAZ\nCCY6\r\n=lT6H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"production": "./index.prod.js", "development": "./index.dev.js"}, "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": {"production": "./async/index.prod.js", "development": "./async/index.dev.js"}, "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "browser": {"production": "./non-secure/index.prod.js", "development": "./non-secure/index.dev.js"}, "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "browser": {"production": "./url-alphabet/index.prod.js", "development": "./url-alphabet/index.dev.js"}, "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.0.8", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.2.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.17_1605830566739_0.8098974035969551", "host": "s3://npm-registry-packages"}}, "3.1.18": {"name": "nanoid", "version": "3.1.18", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.18", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "0680db22ab01c372e89209f5d18283d98de3e96d", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.18.tgz", "fileCount": 25, "integrity": "sha512-rndlDjbbHbcV3xi+R2fpJ+PbGMdfBxz5v1fATIQFq0DP64FsicQdwnKLy47K4kZHdRpmQXtz24eGsxQqamzYTA==", "signatures": [{"sig": "MEYCIQC5Wb3rFh3UtZJajF6ceBdirHQS0cNIgc70cG9iVmYaGAIhAMEWap4mzeNQuEWtr1cJsuHbIObSLQanEC8LGB53cQGF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuDxvCRA9TVsSAnZWagAAv1MQAJohxNLtt2HP4dmFvvY8\nf3FnwDj6UrGHnkvD7JTU3x4l5waC3c8rrctTEBN9jm/LgGyhGanRPEXBMxn8\njmoZLml0tdpj3/9ou7k5U0NHiQMoNWcMSzfVzEwsEc6Z+kb1S0Zcc3Mg4yhT\niniGJLyUvFDbItbEwOw0ivXwjuFVvxN3g3bsO43m66hlXb2A6KVriaQBNopE\nh6btuRuqDd0Z2sVgwKjBxBGBSZLutpvmjwsqELnZ9BcgNyyAws8/rnYtF87X\n8lia5VBWakT6fOWh63hc+Dg2IP3YaMNhSMlIIkojR94Wph5klYqllkRXytzO\nCWKk1cuL8CUjSJqBVCwMbSylAqs87fYPfJXKHbbPP2NYWoN8qEx07Gksb1iG\nUJs0xBUe2zI0gtUvN6+Y8S6taqKEB5MfK6aNA8OrlRgEhKfs4TuX4HsxvOme\ng82aFXckCymhJ+8Y4TTNB2MAIAn87BkM7lQc748nndXouxYJ3HspGfAQPr2I\nvjOwKWVBbit1TyBiV5DVewgNY6W0gUvcAugIDkABW2XcKzJgOMvTgyCAwc1+\n90z8F5/0h67a1h0klEuIelu7Ju2TTOt0dyTOysjC06uTbnSZWEbTMBU5QLh+\nS+jfViXqo+3cdmwjWdex8YeDlJUtXHfPvDC6u6014MyYH74DP/vgSj5Ww0st\npOej\r\n=fHbL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"production": "./index.prod.js", "development": "./index.dev.js"}, "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "12.19.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.18_1605909615022_0.5867661632459817", "host": "s3://npm-registry-packages"}}, "3.1.19": {"name": "nanoid", "version": "3.1.19", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.19", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "baf8331efb722320f7ea4ef367f056e8d965172d", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.19.tgz", "fileCount": 17, "integrity": "sha512-giCapaolEGFzITf00P2rSIjWoY3aWS3bb7AzjnfFJH4EOaAPLtyUrr3C5nQ//x4v1wTcU1JgtBpOKPOqqHrakg==", "signatures": [{"sig": "MEUCIEI+AaZcT5O3kEjmAuZ0APtLiS1kSUB1riyU3rGZZD71AiEA5Bn3XvdbJr8LZgGey35Fzf4H/6osSE+y1rC0Ew5p/jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxZKKCRA9TVsSAnZWagAAs2wP+QGm3WTTVhJmXviPcXrC\n1nKAxjOB945g2RAU8N8d1hLdU/tI2wv1/juZw3AuUFSNJE0Zscx2bNYO6kYY\nJg4f4QHbs+cIVxxDXenGZcRRDKNcEOnjAmD1igZCbJ+Jkg8dPeUsVr4ohRM0\nUxY5D5i9RFPQVpiRRggb9L0dPpsrfx2Jj1sSMApLvzKyNvYzN0N4gsmsqJ0W\nRUa5xAxhO07s0+zSLT4VzeTZ4BhzPkJP5dhywSrvjs9VdktocQZTAGzaTW6D\nXprtujLizCt37SSGZi5xDTVICwM/XFyFv1HQt4Z7YamQk7QzGReWrs2VST5+\nIhse6uX5HiE0Qi212rXq1ITOxisqTBBx4iopMurS/RGG40DKK156z+MGMJQD\ng5tczyBEfLyXYmg12UairXqYPr8+l1QpD0rLhxhJpyktphmZeH84RGSVFaHz\nQxUiSW6Tsj23XeSnTjfEHxLmzXT5WsAuF1dGkYDFK/rPCcQyMxcsTPzABOio\n/RbbsxvCEfU2sgYJBIQqYg/n21DMidhVNK/P5ZRF1DISdxuVNpe5Y6JFI3bH\nHD5F+QhiNBV6jiHsrnHawbIlnH3eN5V3NaDN3u2Y7ZUMNtPuZQipfRKMjHy1\nTYkcKkoMGp7+0QS0+sbwKnsZ8Nx+DU6BhvOXvPsEK6yM6IfbduEZQgbdfZee\nx/EK\r\n=OXGb\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.0.14", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.3.0", "react-native": {"./async/index.js": "./async/index.native.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.19_1606783625411_0.46068213219333165", "host": "s3://npm-registry-packages"}}, "3.1.20": {"name": "nanoid", "version": "3.1.20", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.20", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "badc263c6b1dcf14b71efaa85f6ab4c1d6cfc788", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.20.tgz", "fileCount": 25, "integrity": "sha512-a1cQNyczgKbLX9jwbS/+d7W8fX/RfgYR7lVWwWOGIPNgK2m0MWvrGF6/m4kk6U3QcFMnZf3RIhL0v2Jgh/0Uxw==", "signatures": [{"sig": "MEUCIQDKS+1zRSsqtgor91R2G7hfmhFw7ewYjNCO4ORNC6crvwIgKTct9Xk0JZ49pfwaJFNsT/TNAMC3+6iuxUwuU/WawBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxl4BCRA9TVsSAnZWagAAhOkP/RPClaquzyzEYGyUTTfb\nhG0mzBpOJrbcuQklbP5gafDET+LJYuR494P1SLmJtWpW2W6I1NdoaFs2jTfH\nZKGSGSbScyP7xbKJMJFkWw5JhfsuERWC8K++zSs45oQ1eub5IzbR/5btOk8e\nfO/VI5fYS2CbgJexanNBQmep67V7Iior/3gyAOQncUYqciNOPQn4xg7wUyvA\nbK/dYAHCFOqpfol38H0Ky0zJQWK0g9lnff0zE3OKw3bS0Vzif9OL0luhshTy\nIQ9PxSIuTXz3hLma9vg5SusqfTOw8q3r2Dp6YqEcgu5+hr0n+sRV+hTlQk/L\nf/I4GNPVhCClqkdk28Yk+hDZSufICuTjpFdEMO+Rbw+AXXQLJK8zM607J+ci\nRz7BiDxQWRSZx5e49TdK4eBi1iEVj6GNSe0HKVsd5IHr90l5fIZiGLNXViQu\najTZ1nf4pJBPnRc4fcxWHLylTRaBdrGzj/9BjN2l0qadmikOK57xeEX4NaOM\nUXLZ7WjIPDrdIEFBcLJZL3Kg64HdOCdW7YbJkvX02i3Di0xV2mt/n0lauozA\nVbnAJQDXjWsF1j847rSkN2UqJE70HDrnWCTiYovDSyjmOPEOgH8NDz+Mupjg\nij9mGuhYDZyf4DEKp12fOijEpeyOmaCkc0TvC7FpQ8lkalw+cn0Xxn7+9U02\n1FzI\r\n=WSN7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"production": "./index.prod.js", "development": "./index.dev.js"}, "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.0.14", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.3.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.20_1606835713001_0.8987888932700279", "host": "s3://npm-registry-packages"}}, "3.1.21": {"name": "nanoid", "version": "3.1.21", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.21", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "25bfee7340ac4185866fbfb2c9006d299da1be7f", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.21.tgz", "fileCount": 24, "integrity": "sha512-A6oZraK4DJkAOICstsGH98dvycPr/4GGDH7ZWKmMdd3vGcOurZ6JmWFUt0DA5bzrrn2FrUjmv6mFNWvv8jpppA==", "signatures": [{"sig": "MEUCIHO6bRIwKMIVKCql+3E4cq43di6Uv9tyrenBgUmF4erYAiEA/iNwvu1uQEDDxqhhmvxb/0LDwYHnAogD/d9Bawt35L4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSpwQCRA9TVsSAnZWagAAOiUP/1FTqc5qM5buTmLAYOhk\ntLCs745PCMSDa3oUzHZOyCOh4VrRfhxoMVZCq21EMys7FwOvQdoR02JkhYaR\nWItknD6eaytKP+eo9pIOFDVZA2Y5GGT8sx5z+h0hLCo+6vBwWprDY38U6sQ9\nE3TGvHgw5Wi7+barEzgQXEJyjnxT6jsntjOk6ArG4He1AqRHSpSnY87v6R6l\nFJM3pGi11T/eiKgLFy++uIAowlnREsoCJBMfbrT+wcWX3CrdaTB7wG6hyMuh\n+bxbTxnZbTUsM9HpNRkYCB/O5/nzg0OKmvHaL44W8EzOczyUITP+95Z8bjbm\ni/q//sYEgKixd02xhjXr39KUKD2Wx/2Kq06KENamFUkAhajJh8j8Bwh9Zx8e\nKb6dUZIQPxJ93PuLhOae1rKaEOPxfhNLU30Rm48nwQwggUrJGTzxAdMa0nX0\n2alqrbAJK8gCttXKs0MsHxykEEyr7I9DfWvwMJT+hybW78FsdqJpamjhqkf+\nlPjfiohGCWQkQdosSqJebAt3SROBnntM0h2747g45u07T4FCfUAjeaQW5Qex\n24eNRAw6AvyHWtmosrYCp1R5ggV9mLTPMNdCm5NLtrdZP/8GvgPPMzFT4b6U\n/xg1ptA7Uyd7gs+w2DwPza36Yrlqnt/mKipCJ2Rzi7CCvtkuwoFh3uQNM67S\nb0we\r\n=6+wr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"production": "./index.prod.js", "development": "./index.dev.js"}, "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.11.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.21_1615502351786_0.33209921318976154", "host": "s3://npm-registry-packages"}}, "3.1.22": {"name": "nanoid", "version": "3.1.22", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.22", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "b35f8fb7d151990a8aebd5aa5015c03cf726f844", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.22.tgz", "fileCount": 24, "integrity": "sha512-/2ZUaJX2ANuLtTvqTlgqBQNJoQO398KyJgZloL0PZkC0dpysjncRUPsFe3DUPzz/y3h+u7C46np8RMuvF3jsSQ==", "signatures": [{"sig": "MEUCIE0RIEFCH/t6bTuXb4Nd4khyVZ1yBG6tTWyxzyHmPFn1AiEA+gbkX4r1FR3XZl3CI79EXE/tOJ6gLNxKRLqhXC3shAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT3WsCRA9TVsSAnZWagAAhPEP/2sfXH3jyoHdQ+gRDiKT\ndZRU6vRbzm8VwZDQKyAe71V4DxDhJ5fRm1+QQVy1XqeQdnc31ioHk/24SVVg\nNm6GkVnIP6SLl1wk/ilif0As2c1NeMZNckgtM/eWnsYnS6xAgT5sRJ3KLRDs\nNWqGBAzHRFZ8yQpvcMdFUEesQ6pmliyDN1EkV+VGu6v3/OeUpwq3JyuASQW1\nLEyVW9V+o5qjEtaTgtn0l4h6WHaf4cPIedDMx/ylS5HQQYKFT/ElbuV+9A8a\nLxvENz9ds109TnNQ8vqYXbFJhQ+SZcsdIzHX7bZnXI/4F8FDTP2pK/2YYpKq\n3yvseJbdqe+vtALDxP+Tnk9qVKz+LBUxLhSOB5NiA1tiF8jNlSPxycak/TL8\nnxCsSEym70l8zE12ua2vQreCXVtCFP9PtCVvaFpFbTDhFIhU516biTd83KC3\nxJxEOFl4I4zHH63274JEx7jscZ4ZrLm41FT6VjKyxZZms6Dxoszwdz3O8+7p\nTJJEniY2kspeYD1K77hEj8dVJYX99IyRW9y/2ufWEw28WigHtHAH3B+lrOYN\noW4hlwXSfQUOFFD9fXQQss6i37eL4cpJhZJ1IQ9H9OfZMTOmm4wlD2DPUDep\nTfDnnFqY/03YCLoEyZgNCorqftusi44bXyMPbMvGBlJjFXORmYZaSQk1pld8\nmZJD\r\n=eq8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "15.11.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.22_1615820203459_0.31341124794693176", "host": "s3://npm-registry-packages"}}, "3.1.23": {"name": "nanoid", "version": "3.1.23", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.23", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "f744086ce7c2bc47ee0a8472574d5c78e4183a81", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.23.tgz", "fileCount": 24, "integrity": "sha512-FiB0kzdP0FFVGDKlRLEQ1BgDzU87dy5NnzjeW9YZNt+/c3+q82EQDUwniSAUxp/F0gFNI1ZhKU1FqYsMuqZVnw==", "signatures": [{"sig": "MEYCIQD050kuyjLXVFdyikW2mmAdTdbcnW6FxlRTkScyo0/IVAIhAIxX5uH12b9fDntgQQOAvbp5/pjVW6WV5WXmH0yNgMdn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmYSqCRA9TVsSAnZWagAAWagP+QA1QjTvEkHybFYn51UA\n9eppVCU/gdOv6E2xnTaUoqD4xAFyRdmjS0Jj7SSgZ2jE553UJv0dVDYTk9TW\n2hE6lShTK/GXZTu/sta7KLv7eC2oqXqzwFQfOBYGyU+736dW4ZMBnEc0393V\nTNsByboe0wN1Xgs3noJHGcRTnqmNebYnLFYq7Sez7xYhQpFiOereTWMrvPK5\ni8eFINs8m6hJerb+uCOZCxEfBz4+1NH/0x8otR3AmpM9Pb0OexyhBYClyuDt\nVWAL4fQHCxqldTGK/SXEu3IsotP56zrn9ghNf9OaE7HObBnKSaNmou65fsNR\n1Ih+1adGZeyIHqXzU91Jm8/ASI6/SZA0FV5W7HiJU9W4eurK2lOjeWV6laO1\nqGINBEe8qbOnalYItvPjoYsykd0mnoi6Q6trIY1M473t8fOTa2LM6z8A3Rd9\nz0Jav8wyVcrNQIDrS9CSBMqRxHu1Nca8NoV/je4tvCQyjHUC9HUHQDA2YGoz\n9iYjQaxBhDy7FEr6vLamEd3GWJp5MYWAKNZ1AmJrmW5ayk0+sy6NBYCzkCwq\nlCHfraN+ePK0oa23KAjkmKhxLTxAJvraPy85CDPb1a1xVETY9yUXuZ7OAO5F\nZJpRVjhZkT/GGGBG131B4DHg2jZX/JdYisMHs46GP8GEvK0eXm0gNfNMFrie\nrDvE\r\n=0pz8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.11.2", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.1.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.23_1620673706293_0.7538289082738905", "host": "s3://npm-registry-packages"}}, "3.1.24": {"name": "nanoid", "version": "3.1.24", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.24", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "d7ac20215f595c26d314ee5671169a27b609025f", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.24.tgz", "fileCount": 24, "integrity": "sha512-WNhqqgD4qH7TQdU9ujXfFa/hQI5rOGGnZq+JRmz4JwMZFCgSZVquTq3ORUSv6IC+Y41ACBYV8a8J1kPkqGIiQg==", "signatures": [{"sig": "MEQCICKspGSfIgm0AX/Subp4MnTeJIV7/dhpgjvBALpMLOSSAiAYB0vMBZVB4FkLMQrB4x6rZKHGcTF2AIp3lb7gaQNFfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFC3qCRA9TVsSAnZWagAAiB8P/j3vhltGa1fLSsY0auLV\nvzc/k4Ej9x16gb0KJ7JC34/c0GywrWy+KY/yJQvx/RF3qpdDDu4+/ieqeZWF\n5Cco1s+YB6iE/ncdAjqADqbA6TS5bDvmGSfwkiuoMB+mms1eY5auvkwp0AuF\nym2rba2Z1ivLM503LNYdbeMGTRso7VzziFNY08KM5vJ94jC9DCnGy7cBsKr2\nZ3z9XeBXIVg4M9AksomK6qha0J0vcmKlbQbmmwWdCPwZfWqKcYcjui6dz/Te\nTuZrG0OpPKTtOwVgiNcItJH/qPA2RIXJxDn9lKvy5Vktnt1Z8DLzdBfhs3C4\nYUbE/UT25Vqf/ILjuWyXuYqhdUmDNRVAJ4mrd3qhjK5q2p8tTd3Xwgog7oCn\nqmSKtY+AeXSpc/RMukYc7uwVUzsS8322LMnVjmWaYQeMYQT/owiUKECUD4/h\nlVYPGBA9BkaciNtKbhlLLQp7AcTlEJzJKkLUQELB/cO8fTBKIisRymzPvRbL\nUyZ6G7wcCZCcG03VFPP4aeRl7SPCqrZEoeAvSsp27M+xe9M94eYWs+NVa95M\ns+CzYK77wmSZ2CFDyb2GqLfVI+HSJdqMkcdAsvkepGMx0t2Qhs76LSI1mJxD\n5Vm++dpKx0E7LZ9LF1douA/hG/21IQ+bgno8W8G0EfhLN06m8TVYfPVIqGet\n24iq\r\n=wvrE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.js", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.js"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.6.2", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.24_1628712425874_0.2681418353802929", "host": "s3://npm-registry-packages"}}, "3.1.25": {"name": "nanoid", "version": "3.1.25", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.25", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "09ca32747c0e543f0e1814b7d3793477f9c8e152", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.25.tgz", "fileCount": 26, "integrity": "sha512-rdwtIXaXCLFAQbnfqDRnI6jaRHp9fTcYBjtFKE8eezcZ7LuLjhUaQGNeMXf1HmRoCH32CLz6XwX0TtxEOS/A3Q==", "signatures": [{"sig": "MEYCIQCPXaYxUJoq0Y6N6SBXSmF4FAKvK+X5ZLno4fF0vukh0wIhANinoaoxt0rjzVsXPnkXw/EgCP3MbwVvv4yyjqLLaDYv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFRYnCRA9TVsSAnZWagAAEtsP/2OwEkCZRGvUnjj0WoZX\nuqyGEe+QiZqZ66yLPl55sDL5+NWgWUwXTsL81F2ZPH+r4agKZkAhDYq1uA83\nlMu3k/cLlSlQef9bORDF0fgxKmz8+lzUUbv98TMDMc+SFTt5yxVz7U7QQK7U\n7rY/LPZCMVI85GQtl8V8B7ylZHuyzZwvgunLSq/P2a1b2cPi9ZZfTVKNBfId\nlziPUu2EyteWU0x5rmfJF8jamtyqjGbfnbUMuteMu12sdSKSpk96h8jSZLQ3\nMpuwJYGz1ZUwUq5QYENWr22WPNHThZprQGrIgA7i0jvhcoFdRUxUjZckllGG\nHZPp9m3OD6Wdi95P/tghb1UoAzs8wk7KS3BwONqFe4BKclRxYsEZVHcjY8Bd\nQyzDsqNniNT1KRxks9fdNFgr3iAHrbG+6jHpyOQwWP523hwYbtwO0VXkPQcj\n+KmQ5FgvbjSnDapIFttBaJ/CME1p6M5nfjrgagp0FeDy2SEdef5QoXYiaS6S\nVYDX3qzyFUpLDLoMi7RWvToGP5Grp/riLW9jyRHDABm0NMXMiGQa9XD/+s0P\n6rOrUypu8U6lO6QrnJlpzVseAR1sRNqsNAHnamvh3cR8IaNnHRd0q/+fRCcq\nYg+OvRnstCMegytIyvgy1O2CZrAaHei0acbT7+pWd1Sdvdt4BJzyoHNPdYxm\nLmoG\r\n=QQZQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.6.2", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.25_1628771879655_0.7406537240463158", "host": "s3://npm-registry-packages"}}, "3.1.26": {"name": "nanoid", "version": "3.1.26", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.26", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "c1a335a2a365ff4394f4f62137cd96297533f526", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.26.tgz", "fileCount": 26, "integrity": "sha512-f2KGmBOi1LcVIg7mz8tBR5Ma1cu+gnMk3dS13Ig5gAUbp4Ia5Zpci6KvNgy4nJMN9jR1ELw/YU7X3dRzE18aQQ==", "signatures": [{"sig": "MEUCIQCzltmm7znv5utpHvMojZhHSz441rWml+DhRukqbZL2pwIgM2RyHfiTKMbDl8vcGUAsOs0z28zTHiaS+h2slchrgkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46628}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.8.0", "dependencies": {"nanocolors": "^0.2.6"}, "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.26_1632641014276_0.15037472943892594", "host": "s3://npm-registry-packages"}}, "3.1.27": {"name": "nanoid", "version": "3.1.27", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.27", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "b5cb788c2023a41bf09897f2a02a7befec4e4307", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.27.tgz", "fileCount": 18, "integrity": "sha512-HTbojJgGU+7+huNWzWm+Td73y32U053u9erdGIhK+T9SxYoSV/+BYFfQ5dOsZGBnNyotzInULKkEMcF9ooNwlw==", "signatures": [{"sig": "MEUCIQDskgd3iI+dAvH73BGP62EOhpi1QVGWosLvEiDNdbyqtQIgbe1L64yB9SzaVKD7hqPBZLF+QvLQT/QxDYxdZUg6ulE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59655}, "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.8.0", "react-native": {"./async/index.js": "./async/index.native.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.27_1632644482198_0.5941010176892898", "host": "s3://npm-registry-packages"}}, "3.1.28": {"name": "nanoid", "version": "3.1.28", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.28", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "3c01bac14cb6c5680569014cc65a2f26424c6bd4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.28.tgz", "fileCount": 26, "integrity": "sha512-gSu9VZ2HtmoKYe/lmyPFES5nknFrHa+/DT9muUFWFMi6Jh9E1I7bkvlQ8xxf1Kos9pi9o8lBnIOkatMhKX/YUw==", "signatures": [{"sig": "MEQCIGbzDTEZIEFdrMgJ4crfflzCRS/DURKJUt7nL5J2kkkmAiAziaDRbs6AWRdCPBqn3fvS+Eh0qkpqHRXtguOZCBwQcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46576}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.8.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.28_1632644816279_0.9417742016304189", "host": "s3://npm-registry-packages"}}, "3.1.29": {"name": "nanoid", "version": "3.1.29", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.29", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "214fb2d7a33e1a5bef4757b779dfaeb6a4e5aeb4", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.29.tgz", "fileCount": 26, "integrity": "sha512-dW2pUSGZ8ZnCFIlBIA31SV8huOGCHb6OwzVCc7A69rb/a+SgPBwfmLvK5TKQ3INPbRkcI8a/Owo0XbiTNH19wg==", "signatures": [{"sig": "MEQCIEBX4xqoxCcbibN8RKC1tmtAt5/YDVg3Q4uxIVAIEDIOAiAu+SQBUxi2dolOatwhkbDy+70prtELG/2UMYQj2LYJEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25790}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "5870ed0e86d49f41799f16732e4220b21abe242d", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "A tiny (108 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.10.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.29_1633467865029_0.2377295158059456", "host": "s3://npm-registry-packages"}}, "3.1.30": {"name": "nanoid", "version": "3.1.30", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.30", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "63f93cc548d2a113dc5dfbc63bfa09e2b9b64362", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.30.tgz", "fileCount": 26, "integrity": "sha512-zJpuPDwOv8D2zq2WRoMe1HsfZthVewpel9CAvTfc/2mBD1uUT/agc5f7GHGWXlYkFvi1mVxe4IjvP2HNrop7nQ==", "signatures": [{"sig": "MEQCICJ+Wnh+Fw1A3NCk1LQGcINzQP9+/A3jTyiUa0Ye8aluAiB4zQozjgqVuTbrDZyXIYEFZxzDrvNvrlldvzYYpLOHQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh20JWCRA9TVsSAnZWagAA9uYP/RTTZ1lUKbemPK7leUgY\npif38BpxgM1t9lI5HhOzsaOgEZC8nFVkHZKNgLbYwxmhKMfSrVPC5lRhmquT\nWxD9Cp8vB9kk/JV9ggMU8byESGdyOL5JRE+VxnP+7G+FsNVpnjzqQooU/kHH\nFDS51lvcat3AtR1PILogbpcDnLR1AGjVEMgbpf9ySz0ZWHiMWHXoD9buM3IO\ncq0XWg8l/cRlCLONyy+fmLF+fU5M6gcyO+Be+ORDx06aw+Xm5my+5SDKDb4O\nX5/BNssEUiXYiuUJXkqeT/yKZqoaEXaI9ZLEV0iFk29jyozG44p6nCh5Ml9T\n6z2bDk9MVmYwwwRuhR7Zoz+WdtxzRnIgpJJvKYzRnnihEbFc8u+BCF3RSHt0\n9cQOUooeEChf9vNMFDoklOH9yh5+cIB8kuSojNPPF9IQuCiPbQ1cOkSr2bYO\nA4C/fUbe1OrC43TxW2+iXIEkG/FW1DJ6embIbWg1oiN5DCS2haY/JeheIjJo\ntyRhm7VA/Vn2hMmlOs1XPxVrvvqNU1iE1Vf/9Azds68apSBMctx6aslP9Zr5\nNru0aB0eMMyICCWj0lOWBT82xVBSBpV9FEaaqTjoFcERsHX4OHC7FpR4lB+V\nc5MEm+LXudkvS4yi6bL+bXvyKdLLFhKmLNPxNiqJ9UwEr/njcwEla5dCm+d3\nmL3c\r\n=Ez0m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "fd1b0dc1a0ebca7f29edbe8de839993f774992af", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "16.11.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.30_1634166050814_0.8320002215519029", "host": "s3://npm-registry-packages"}}, "3.1.31": {"name": "nanoid", "version": "3.1.31", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.31", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "f5b58a1ce1b7604da5f0605757840598d8974dc6", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.31.tgz", "fileCount": 26, "integrity": "sha512-ZivnJm0o9bb13p2Ot5CpgC2rQdzB9Uxm/mFZweqm5eMViqOJe3PV6LU2E30SiLgheesmcPrjquqraoolONSA0A==", "signatures": [{"sig": "MEYCIQCU52RDEIqQXSEjoMM0bzLSoPsGscXtWpiYyOu99HpEvgIhAIWRUDnsOS8bJYtNAdOoAtb9hu9gBnu1tWJZt75oSSqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3YjxCRA9TVsSAnZWagAAtr4QAJBRDV6Z0t2Rj19VAfpO\nu0/rdSWMh4sLWKYJoqdhST/IpRtaz71QevdvZ76RA/vdGLd8zBTpzVLeLBQF\nkpWGYuvR/cfrHUnQZntmBt5xZwI6c2fdhId6fkDaw0Ze9pnDPCaZpy0xz4IG\naIj2h23ohKB3ui7Y17TDHc5DTNw6mS+xNcAInT3Q/UyU0RHN7ygnL4ce76xN\n5INpj/9wjv2OfdLtacpPDL/fesLcUA3TPrNP8FxSz8o+6ckGM6NEhYD+qpT2\ntJ4bv09ZHFCliChJoq40ntnyOgXjcVf8o8p+6kcbthKo1XmaVsWTipoOtoyY\nD7oiPfpaN7Uc/FkPOYx1+sfUtT187jlMWxI9FlWATAwy2T5y539bXtLv6YWZ\nX1usiZspnUf9hy0loRypFWs33CVLRQcaged/jpAiJR+GKCr1TNdzwNUoAM6b\n2H7JKUwiRssDUe37u9rtZcE8VyJkbZbZyyHxuwlri8rmZrryf5kWO/uiFw4O\nrugDmYCeIPpTW08BBwNiaYx1+730w/ZCJswJw1nuWhjgl1hhM51cb0tqjZ41\n2cDH2I2Q8b9AxYhaH5gQATskAhkC4SpMpf37tvRBsF4RizRXYlSrb/VcnIoq\ntsxfMdZF5erhBWzlV1bcizGQsGlcOdBUWkC1Xz58c4T2ErpZiZPtnKxfoyGq\nReCz\r\n=6k4O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "07fdfa6618e2668ce51b2554ee3e1a3ad87b0492", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.3.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.31_1641908465355_0.11892096567674093", "host": "s3://npm-registry-packages"}}, "3.1.32": {"name": "nanoid", "version": "3.1.32", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.1.32", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "8f96069e6239cc0a9ae8c0d3b41a3b4933a88c0a", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.1.32.tgz", "fileCount": 26, "integrity": "sha512-F8mf7R3iT9bvThBoW4tGXhXFHCctyCiUUPrWF8WaTqa3h96d9QybkSeba43XVOOE3oiLfkVDe4bT8MeGmkrTxw==", "signatures": [{"sig": "MEYCIQDeGEQuApaJ4vePXNLBgMa6vE4r3IOpdmFkbw1J0XZndQIhAMeCaHL34B6fKmOxje7rfCkmYpn2fpi/b76Ksq0xV+bs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3qR7CRA9TVsSAnZWagAAB8oQAJdi4YvebAZ+jHLI2hY7\nRW6k5EOLVEGPM5l0f6IeDGkXzp+mTgIsHyYZ/4oi0aWuF4G/8BeHnnpj6nj9\nhbftgDqbkDPdToKnrooPOZqihhBA/sR+8prBllPu0OJCO/zPX/euBWuAcbfn\nOA2yG6Q4+u3pAbT75gejHiTFRI03FYvtAYHQazzcIMBF/9lJi+JXtu/l6Qi1\nF5a7/enTL1+zjPPcDKzVkSfFqVzI1AOVmrWlWu7KjMcuUzKyeloUtggAQjUt\ng6TFMNgRtw2vgMrQZEYowpbA7khmpU/6aEX5aCdKDPI51TyRwG46DCt1ZfoL\nSY8G4xO6xkzEF76mWaTOUuJSA8mhweV9sCbf2xj35nRSM+JFxhG3qop0KoZV\nljt2UHKS8mFfdjOcQTK9EcAkrwfMv3e6Y4WfqsFfugn+g00KEk5wyudTpKCk\nrQtfijdsfcTRg8rdOlnIUJFtPZeMm1fjaM3RjcumLZtfxL1rQ9l26bpUzcrA\nB+KNX/ycopXa9wtRnsuYGOE6zEqcJurqnm38eZaAkYAJGzhMO1nqFOCkDcLB\nqo/6S5WOK+TNXI5hzZ9sDLPEwO4wQpSmjFYbI2SzI1KztXkJ/CUDQ3+MWVK6\n9kn3z4dDevptgTBIWAxVN4hvEHToYNqYekV313P/Bt5Lj1v4JSNmRfwHNuvA\nAmLV\r\n=9gc4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "f4257780ece488734a65c176e80c2fd8ab6aab8e", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.3.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.1.32_1641981051738_0.03719328781949405", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "nanoid", "version": "3.2.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.2.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "62667522da6673971cca916a6d3eff3f415ff80c", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.2.0.tgz", "fileCount": 26, "integrity": "sha512-fmsZYa9lpn69Ad5eDn7FMcnnSR+8R34W9qJEijxYhTbfOWzr22n1QxCMzXLK+ODyW2973V3Fux959iQoUxzUIA==", "signatures": [{"sig": "MEQCIFaUZMOr4oJetcinjjki4nyfymxf6kJiGexZRniLWjpcAiAOOvzmniLfBfIAxCawczi8UoLw4PjtbsxUpSM1ZDMa5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5C3MCRA9TVsSAnZWagAAbKMP/iW54JjV+RQ+Um1XD6/H\nontoI2xkWPr5DO1KYgE6QYx3jl6w5qXv/FqmSqDIwq419UHIccAlRtz2vsay\nUCsQed/97a4UR3dOZsbTIMrXMs3sx9t1lyXSL1HqmUYF6BcVjRUqyK6RqTcY\nizbWK8lE70IAIcHXoCGyxJUgbBdnTb2KHpcBGriNg0mjD0KghcCWCEUTUPFL\nTiNvINWEH9oFoJmi2YpIJ/RtJKcnehYfhS4j1WeqAJRjAE7m0NtEog2VeuEw\ni/89ZVvk1vzJh2FbkE8bzB9s8EasFjQdn1NqMIH3sYqWQFL14KrVrrZV69eZ\nTsuICDIvcMrwBETbcsJJSLn8OiEdeIw51wx3IEMlehzweZVrlKB+uxBqLOAi\ndau+r+FlO+G5lZ5MpO8EZgRnNt5Dwlnwm1ebqSGwoOpTPtY8s2Hsfi6GL6WO\nPc8OtNKBiUpo6teDXHHa6EySPTaBoWKGM/K5PjPMPl0iobkNNZs/k9uuzIVB\nkh4YPC9qvdsN9X/0XsT+kqgp4vrheOYcBcfdk1B3dCbV+7qo9dVowRn5xJvv\nd8TT1PgSbj8r7YvVEeUms4oX4lZeXFK7vqKLBJZR/Uc1aQjlYXc/G/L+UNmr\nukx3r6s0y1PujFg8UpQitth2ZUZwgMRBjlNkHlxXTf1iXzoWeMEEXoxWPPE3\nwVTi\r\n=Fjp0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "23b136929a6d58f32e31b269534a3ce3f680a086", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.3.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.2.0_1642343884529_0.5594671975094112", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "nanoid", "version": "3.3.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "5906f776fd886c66c24f3653e0c46fcb1d4ad6b0", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.0.tgz", "fileCount": 26, "integrity": "sha512-JzxqqT5u/x+/KOFSd7JP15DOo9nOoHpx6DYatqIHUW2+flybkm+mdcraotSQR5WcnZr+qhGVh8Ted0KdfSMxlg==", "signatures": [{"sig": "MEUCIQD8IaHNopmM7oQut3eukV3cbRUbNrngbBj4FfY+52oTpAIgHWgpjw/wwDsWNGOr3q9vR+ysK0SoLyGSkbCHHlpnFUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCr1sCRA9TVsSAnZWagAAJMoP/0I/hzsO9H+Ous3nvm6L\nRVAVd+K73t2qVtq/5ZSAhEbI0SJKpK1qbAeOead3jTmXWXR2S5pT956JgedC\nruadZOGVsYgszSGKyRKuWOzPTx11OnlXooV+/P2Rtp+T9G7Ya1K6b4gQr1F2\naqeTxr2SnfL8fvcX96ZZY7Esvcj5mEOsG7RpQccgelxNkOUL9sfhBcQFdxcK\nWNCKbWLGS7PC4DvHrD/kgsKoiTUhBKe8SxtTZxyC1gOtnsMyh6RRpATbpYAZ\nfK0p8nP5IWh2k+V2EmjE+FGJQTNHIQXsh9RM4qj7xA4JUQQK1Pl3XONT6zRW\n35Gb1h3jWc332kOAYzO/7RB1EFIeylubuF9a3V71qVwp+QSSR9itGGOcDJT2\nDtaefUjeleYhWggpj3m4sItE0XngLOHZJw16S8BSuV84PjfTXIO/72aWyqeH\nDnD0k7IqvToAi7tSx6AFUVncy2Z5DpZGu3tFdPNDuhMXipjB1Ba3sSIrUtcB\nScP6gist27S6v7WU0rKn2SNu5eqUF0fmu+5jjXVsySORqVSnbWoO4hanBDHs\nfRkZrWfDtOrqlwcKcVOHRigfmgvR8uQyqsh6qtweIvIf5pSwk99TvdVPwtN+\nGEXIPGRTOHZrKLHsfpbmuhwxQNCDOOxs8hihLMREqlX/4lglv1cKiap4pFFu\n4Pes\r\n=15GY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": {"default": "./index.prod.js", "production": "./index.prod.js", "development": "./index.dev.js"}, "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "f74a41d3ef7d565d1b1c4247417144a3719c4bba", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.5.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.0_1644871020874_0.16979232903042663", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "nanoid", "version": "3.3.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "6347a18cac88af88f58af0b3594b723d5e99bb35", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.1.tgz", "fileCount": 24, "integrity": "sha512-n6Vs/3KGyxPQd6uO0eH4Bv0ojGSUvuLlIHtC3Y0kEO23YRge8H9x1GCzLn28YX0H66pMkxuaeESFq4tKISKwdw==", "signatures": [{"sig": "MEUCIGSz9LlULOAqq+hQzisqInAgYaR72NCtukabDGpDN/YNAiEAoKQXl4EtSB7WooIZk18mrrBexEKqAs4lmUWrYO8Fg3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDXkQCRA9TVsSAnZWagAA9d4P/ilOFbwq8uSKO7v9uBDB\nqn78byMAjrDT34Gho7EGqCMUaEjkqTmOWbXrFAlCvuHfneJJ4mDQVAk4DoVU\nAQ4CbiIludZEUDNJfM8FwrJJd2kbc67n3TONTMZaH5YaQUuCyklpN2p1eXcr\ny3B1thMmWu3D3q+R8MMjVs/Bz/T2KrHtdjqPNDCnkut6ZdV8CJcUNxtkNQvk\nXvFdRjrjxccV0w3hqerdB43VLH7KLw7cRpVazabjABU1MydieSvkF0iRlZl7\nl7HJlWudBwM0ZefTglIOUSgKcg5nwh9i5fkOrl982MStCEBAGqHVnkrEXoVa\nNkEJVDFfrtAdFrmIMaLIyvAoqQm1GXX7o169V9VnfXNisqyUuX7Tk/kcArx3\nvln5rIn3QZe7990IhEbCzMucJYaMuZ+fSulzMlcWB5BmYD+I+LxIrdJ70o1L\nYznboUbA8XWLOO6Z/5tdd6zJdZiLRXynOKVPp7gksHrXrv2LmM8b3gjOC859\noUQTMaeS5uoLD+92+XhgC7N9TGMACq31xrZxIQKyfRvJElvcq86HcpekwNua\nO/lv+rrNepgCAz6P7Yp+WQs4LlP6pmPu5pbv7X5I3JWSsmtCdbHt9PCGrfFc\nY12ZzgkMq8ck6314iJBxGp89hIsH28R+d2JJT7yOL/F7m99SgSSWOQtSYFFW\nOPvx\r\n=NmxS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "ec6f809b1210ad27568753a91a8312a164c62674", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.5.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.1_1645050128684_0.3888251869878223", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "nanoid", "version": "3.3.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "c89622fafb4381cd221421c69ec58547a1eec557", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.2.tgz", "fileCount": 24, "integrity": "sha512-CuHBogktKwpm5g2sRgv83jEy2ijFzBwMoYA60orPDR7ynsLijJDqgsi4RDGj3OJpy3Ieb+LYwiRmIOGyytgITA==", "signatures": [{"sig": "MEUCIFulWQT02JvrNG77fTgHrjDkArJszftqGXFSu/BhV2iDAiEA5Imu9bJQ87l1IpfjLZwcEn+BIDO6//Irr3KESmAqyP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQSriACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl3g//SsByrBnViTpf6IgIyzRlSXDy1Y/xD7vIo59MBahrSsGC3ISM\r\n+9XOTwzBrwXkSFlvdUZuvpXR5aDF7zoWUoD6P2f3bd/FBP8ZwRo3Qu83cnwO\r\n1df663lDlZULm/4bsqgXAR9QRMSPGmvLFQn8iLtrQB8zm1nFwyQAGbUl9qZM\r\nYY11pyF41sIVQ0UrMiE75uIF0nSZV+o9saonwUMebC2XiQoCDwxuUvZlOb+M\r\njQ2/byl5X8OF0RDHV1TLtKQRr9O+TuIW/vjuwGrtCsCcuA7XWDXYJw8ZZS/b\r\nW8z4yNBjyCZnL90PlDoz97P18lmKWIfi1b3zKGsNk9ZsYCGb409I6b/cwt80\r\nbkPrYYLkufs+CwpBU/4yX/D/wGGB19R5wZNbLig8PJDNakJC65rRYXgY9cJS\r\nFfz9ke0coJd98AnCbaGJaFhjuMczJzoVm92EKpg8z0GZIEMReUjOJDezUE1a\r\nyoymatnsKFXFLGzLmJWvQlyBxBCNbIw+I6p3d4jdf0BvdRniexwCmupCzjrO\r\nPHjyybvKIOhZI3I90ERyT5bbBQMqK2r7gM5KBfIjDk/QgnccbR9RqNqeZD2/\r\n2kiC80CWqkaBkcSeNltKiYzqM+HonKXwFYOWATPyWuIp1OHY/+vWfJdNkSqR\r\nzmQOMFRbABhH9ninQp9WUg9+mSlUkjrZmKk=\r\n=lMzp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "7f0df4721e3535ad6ed81971c65f423fb7892630", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "A tiny (130 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.8.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.2_1648437986855_0.19757302690028067", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "nanoid", "version": "3.3.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "fd8e8b7aa761fe807dba2d1b98fb7241bb724a25", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.3.tgz", "fileCount": 24, "integrity": "sha512-p1sjXuopFs0xg+fPASzQ28agW1oHD7xDsd9Xkf3T15H3c/cifrFHVwrh74PdoklAPi+i7MdRsE47vm2r6JoB+w==", "signatures": [{"sig": "MEQCIAz9iJXYtenslujlJrBFBu+eEHmly1mEVwCg8ScmS8L1AiAHZS84u6lDaIyI44r6Q+RJJIYk4sjxMAxW0EtRKpkL2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXT2XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmLQ/+Jwn0espkkduY1oP0DjBSFDwwv6hXI2HhwemifAmxSVB8uRKX\r\n3FFHmZ49VYot60AJpYU9I+HX7oGHayIZjmcl3DKIAQ73a4mbLLbf0AutHjpt\r\nBnG8B91ZwsdVebCQtLmL9Uy4JTgjdd0K7vmf2Qx25Wzgf7YlI2L9N1RiweOE\r\nf0PCoK9RSHa0tE71qkvZSB7+l+OWroP11gnKHy4ruh6QNXVyhR9eoDudBNPg\r\ngNK996jYt6BCkg2fgmd3x+eLkfh/TQnL+7R7hoe3uCxOIA+oD2ZQRl9/m34U\r\nd33Ibf5ghbufZkTsoqPDGpqMRbXoi/O4pB9x/N7QkgtIrTrY2GIBh4yqXQcr\r\nyvLYEiKbqNMt/lBXhYV0lGOzaP+ynTd8025NxN2oQknFMSkHNYCErAmSACDg\r\ncNCXtHjyrpBQuuF2ipMsWLllCOScoOpx6ojBWtKZ+apAlCGXIhU0aUpVc+Fc\r\nJfQOcz3vDWsjF7qOCzT4zQNKqJXFZs0zuwTathOgP9ZybnH0qOYje3YKUZdF\r\n0CuleKAnAub2IqNuSCzUDMMZryHf2LbSiL80Hmiv7TB/9JOijyGu5llrPJot\r\ndOgW78SKKRNylNWXEIbf720Ol4nj20nBvQwVDN16YPHWR0GPrUJixCXSUQMx\r\n318MSai4cf0oD1xmuNHPcOluHfOGuyoAyQY=\r\n=xqn+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "0454333dee4612d2c2e163d271af6cc3ce1e5aa4", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "17.9.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.3_1650277783617_0.13340117480997038", "host": "s3://npm-registry-packages"}}, "3.3.4": {"name": "nanoid", "version": "3.3.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "730b67e3cd09e2deacf03c027c81c9d9dbc5e8ab", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.4.tgz", "fileCount": 24, "integrity": "sha512-MqBkQh/OHTS2egovRtLk45wEyNXwF+cokD+1YPf9u5VfJiRdAiRwB2froX5Co9Rh20xs4siNPm8naNotSD6RBw==", "signatures": [{"sig": "MEQCIEXG2ta5bIaT6snvQFKV+m1KjuF4DaCpp186tcPo8vsRAiB2Eg9/6nKRi4lZOfwQC1fgq4EzrFjU8T+uqwGxWEQE8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicQqNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6rw/+IRvv2zOtwi8goF3h1VctIQVWtTtYrobDIVC2W++jyxdbgZoP\r\n2CDj1YWjrr+eM6O6sI1Bj+bF+yoqQ+z8ojtfW3vtRPpjzUf/7Sgs4F2ANshp\r\ne3rqdaQLjpHPriHf6HmPJy3YNJ+7n5TPPGoTEGXAe4eCZdko3XidCMWZdHlf\r\nYQU9CVYiG6mjjORkWw1sYctt8exdcGFMh0QoQq7BEp04QWm04JwvHjUiAgvf\r\nmEQLrNrf9nwzjpnubAJD+1z6fKOc9vUE44MOj2PkPoOr6a+iBBBgwBf45cnj\r\ng8R2G5xzxsRRB0a8XZdp67y3WA8rIaYaUuBFtEWYp7QFoA/tp6AGmHEAhjLa\r\nQKTquG7ejBu21ZsQaxpGc/3WWLEm+7F78GF8CXpQdtg0Kg1eugRotSNnU0SO\r\nPLiyYV4Mw6kXnbVchS5Y+HmcDVEcSBMTve/f1KpmIhJueJ20RCg4MGYZWgI9\r\nNJ1KgH2h4djX4XuoXpcsKnX3oVfinHEMke8sLWXHsMAtOxDipEWgW9cE9hk0\r\n71Y6LAAPBu34pmaj73B0qZiIY7wXxoGWQOCl2STS/VyDG/K9w1T+WiYROu+8\r\nE9Gd+f4qXmdi7Jw6May86DDfauCwBP3gnrB5aeOktCjWsgrrdClN3Hv2pIAN\r\noJcjS3IURf6oeV4+Yw1B5GoJu1Y/6U75fOU=\r\n=IMnM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "gitHead": "fc5bd0dbba830b1e6f3e572da8e2bc9ddc1b4b44", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "18.0.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.4_1651575437375_0.2288595018362154", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "nanoid", "version": "4.0.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@4.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "6e144dee117609232c3f415c34b0e550e64999a5", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-4.0.0.tgz", "fileCount": 15, "integrity": "sha512-IgBP8piMxe/gf73RTQx7hmnhwz0aaEXYakvqZyE302IXW3HyVNhdNGC+O2MwMAVhLEnvXlvKtGbtJf6wvHihCg==", "signatures": [{"sig": "MEYCIQCFE1+9tYoZAeTyrqiysmv4cgkFNpVdoDlRncDmLCdyMgIhAO6saODjPVldgUEBBzVF+drVTYKDFDgBx1UEaZB89BT7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioFmXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUFQ/+MVst8mu2Jb0vj9K4AQYWBK7FRwTHYdZB+ZQ8wBtRcDvtMgKr\r\na+aihVw8L9p254G7LQtK8YzOGHzSK2As3kG4ZoPAmOIYdkVqwNmuNRF2bz6a\r\n95K5OGYWgNdTzZ4ab+Fm5k5d3hGYDJfPNlAtzcQt3p9/1eqpI+dqAJvx2xh1\r\nfikSTn1Oc2wic9R1DW/C3tLNZlFa+SBDC+653+RXeLdyFQ1+y4fRQ7j70i19\r\nkUoniFAB9UOkHzwmT8X4wwmqPM5h8L3EX6GjeU9lRHBZQA6dauyOJe0mgfiM\r\nntPiKjp2ttTqv5CjKdXS4SYEe5O0Dib9BSLpbC8jzOLm/Z9dxgwUm3pTSm8K\r\ndForPhrIf+6cYUBDoxs79BgdgvYt0X5oWeNy1Cz5UJyiCQemQDqGD3Agruk2\r\nr78GqrrE4dd/OkZRg6O5zK0M58/i6RC7TtpBA/+gWiGG9/3jJk6pRFjuiX9P\r\nkMHCvYZgnicElpC1HCe5iWjtUpCpIdWXJXQsmSZUK6FqJklNOStJYYpCmua/\r\nqR1Fp3Hp9znQQ6xzCUTULEXxvyhp60H/3OS85bOE9DdJoeDjuK4dCz8Cs2QS\r\nrwVBfLJxOHVpoum77wfY27E6f81Fn96sseHtixMSYxfQ68Umk3yOm3H2ZYtN\r\nn2c2DvF1OCmcwA/WpA6y28POtzfBGChRN8c=\r\n=aTDr\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./async": {"browser": "./async/index.browser.js", "default": "./async/index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "gitHead": "fd80ce3f462d3309c31d07dc3b3ec2f3e0a0fc9f", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "18.3.0", "react-native": {"./async/index.js": "./async/index.native.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_4.0.0_1654675863703_0.9677050474659903", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "nanoid", "version": "4.0.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@4.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "398d7ccfdbf9faf2231b2ca7e8fff5dbca6a509b", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-4.0.1.tgz", "fileCount": 15, "integrity": "sha512-udKGtCCUafD3nQtJg9wBhRP3KMbPglUsgV5JVsXhvyBs/oefqb4sqMEhKBBgqZncYowu58p1prsZQBYvAj/Gww==", "signatures": [{"sig": "MEYCIQDuoxaE2Ok+aIxD0JqhgyyXE4UJbRMp8WdC5ncIg+we+gIhAJ3Yp8bFnTCx3MTq/6owLUNCkR3MIUQzimabUZYdZoKr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj35jJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFBw//b5++bXn5DvlGa5OA1iyi91D9bg3C/YlDB5CzbxlILJFczjkH\r\noCKLMW8YJbDr5OFA6Nhj5L/oPZ6QBlKG7MBaV/i/IqLMeHkD1z2BxQWzFLWj\r\nVXIWEIsl4KYdXuG7Rgakv7A7fDKkMFbdNLiNTgZ4B+uDTN/ZJPfhl9viT4yt\r\n8cUARslu6sUhI6Nwkfq4D4HSbA5fEaIlsOvAYOyoAjJ6pnHs+dOKbBzk0WUt\r\nf564kMCdsoJ/tDwrYru73HUyYutj7TVs4gn2JbbuPXfYTiusE3CuFh0PvH2A\r\n6PNrJ0ASiJEA3JITtBArfbYfVSV82IAXn4EQU98UNumXHiTZJwJ94WLDwxWm\r\nVe6imNpwiJdOTQmeTb7CRLzpi/f5yXEOlv6O9/Qouz/+JmLa23mtcqHcIk95\r\nkFbmFa1JFxK0hLTmF007iGiY7uJZPCNWN8rKnh3gB1IXmsZzCC0EFnVtOqyX\r\np5pi4M+ldiiQlHVQO71Iuki9uQ8nClSUwOCO1zSP4KtvcbL3O9t6qYCnEwVU\r\nyypri4Eiq6o6t8ruDE3DqMfVTjvVgLH41G+aqpmQjyd1Pm0Ismf5n+xYSrzZ\r\n8lYB80/aYh7bUMvwK2TApUfgqWAC8kLV4zaMdXBvVBe8a5Bv9IxmlEeRhk6a\r\nHLUt2JCT4e0RBCECrnsKGPIfnzJrZVXAvkI=\r\n=b5mt\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js"}, "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./async": {"browser": "./async/index.browser.js", "default": "./async/index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "gitHead": "ffa50ac24142688d7a666cff11f1ca1062435ffd", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "9.4.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "19.6.0", "react-native": {"./async/index.js": "./async/index.native.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_4.0.1_1675598025479_0.7412914355699223", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "nanoid", "version": "4.0.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@4.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "140b3c5003959adbebf521c170f282c5e7f9fb9e", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-4.0.2.tgz", "fileCount": 15, "integrity": "sha512-7ZtY5KTCNheRGfEFxnedV5zFiORN1+Y1N6zvPTnHQd8ENUvfaDBeuJDZb2bN/oXwXxu3qkTXDzy57W5vAmDTBw==", "signatures": [{"sig": "MEYCIQCmUMrB3YF6t6AionUN6h8Wutt921uciRuAWQWyTZeKuQIhAMpPUKM3RNOsXP8yjQdfWs/LtDdyKeA02SOFQJXwlZIo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkID5aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+cg//VpqR9ONX0idoz3QjMZEFpT/MOHPNCNuLNZvrmeVhBBmxVgkN\r\nCTjNUt2QB7S1krhIQHxGQctjIDgP+6mdMwIW5CBMULCmdEulCRXuf7CuBDDK\r\nGSihaT/Y78kwqomUdbTGuloAOZt+71Os7FrgKfEKYlvwUfjEDty4/t0q2Qrr\r\nsPK2B3istA6w4cXb9AQ4Ovv0NB0r/crZ8xr1QwfIw8RYxaF0Ft/GknrV1otP\r\nTnw5SkKnz7qczDmkKc2x5vtpkjwXm1TtThxNhZjktixLcRkVVpSWpLDP5vl3\r\nDmZdNrz4Pg5AXhKV+7FYZZx/oyuUmIjUlplXRH5jrs0vwzrsx2GWYFLG7PW5\r\nX+wom3334CHJdP+xset7See0WxCwgpsLmESTwLv08HGjmjM6j0JkuNPrvLCB\r\nWyFQ2VwLuZskvwES5rFlL5fuRNd3KFdxMAqVwFMkPHytDOhTI1mQgaJR9SX/\r\nQmZtof0oVOXVkswruytKRPslcrHa/KKcJ3VM4J6X+zs9D+HePQ8kThpL5pEU\r\nLeZEkiRTY8+ixheQZvmeRXZ2T25aXot4XMKuMZ3yowVz+tPkGMflqyHUfuXE\r\nmJ+samBe1saEXbYYUXCqqgTnoz+sqYarspBxbsof/lTmfQy8YLRLS0Soj1nB\r\nYcIE3JIExBM0DTG/kuqunG9sg+zJhDzlfCE=\r\n=IIRk\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js"}, "engines": {"node": "^14 || ^16 || >=18"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./async": {"browser": "./async/index.browser.js", "default": "./async/index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "50c4c6636d187bc72b7d0a51e386403d5f271319", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "19.8.1", "react-native": {"./async/index.js": "./async/index.native.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_4.0.2_1679834714572_0.9977285927381907", "host": "s3://npm-registry-packages"}}, "3.3.5": {"name": "nanoid", "version": "3.3.5", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "07ef5fb44ece2bc8574af317015e645d5f681422", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.5.tgz", "fileCount": 15, "integrity": "sha512-nvgaJGpIANf4+VWJAaDGORQyMzhFkze8aXVdrHq+BaSvzfpOuponEysaVFKV/0Bca5V+3SBiDvRabEPbpalEBg==", "signatures": [{"sig": "MEUCIQCTY/cxOB6fZwRm8wsDK79Hv/A4EaVhl/YDNRtrxiJjYgIgFz04Wl58e9vy57qPxtLKUDBZ/noeiC6NA3Lm7IFFYUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkID8QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo75w/+KrUucA4tZyAwpgZTVOaEcwdj+ZGqiNDsNDG1QBWRgThjM9xH\r\nQ+SqGfNOi26XdRIeWiHuj1KpEjfTr2530arotav9eFp5T6fDZgaLTOS2YFzQ\r\ndwTEEVIZLm8UsKs15Ht4daVOKpYx2B1P8PmcAcm+/4s196xwnw0SWFVmG3Hw\r\noqk9QvG57OPqAKHCSXhHtCrI8RiM4t8wW0JGyC9UdE1EDKQkw4LKD8PKv610\r\nprwjHHbSUHOlsKef28fNyT7JDzm3tMTzm3gmkQ37vPHceTxjzOE5XT6SECpm\r\naj9huvCUshAYb3VMMCYRijUisHDH1X3x4Eno1GTNik/ZBTfZ5hz8ubyn1zol\r\nVUncaX2OKSsYsPxAbkL7KwjOA+epqhf0q/JCZZ0IjoI5IK4uUYWqz8b19yln\r\nyWpziySJmD5Ckrae1WMKnO/uNmwisiAmPpL2wmx68SJCAER41KfhpB8FzhgQ\r\nwVxfY2Dq0HVp2vQNjoWr+34/UxpGXaJNiKe5jYiOoTmqKL2dLp5hqK7b+P9B\r\niFYYoJsUu7TgtBpY0c7N6iia4Xv1IRou/DQpWrnt6jlQdZF9ZOEjzXR3PS6U\r\nWvzys+M/ywaMQbwO0t5JiauRngFNkd7XlK5q51BRFqTBYBfwOUeHRj/8Fp/c\r\noXfCaKF9I1iFOuqjlCTD1DqfCrJb1r0UdoQ=\r\n=Juku\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "8210dfb9fc01efcf354c00a1b7e1948f1bebb32f", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "19.8.1", "react-native": {"./async/index.js": "./async/index.native.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.5_1679834896434_0.9835885917577389", "host": "s3://npm-registry-packages"}}, "3.3.6": {"name": "nanoid", "version": "3.3.6", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "443380c856d6e9f9824267d960b4236ad583ea4c", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.6.tgz", "fileCount": 24, "integrity": "sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==", "signatures": [{"sig": "MEQCIGX85PF8UD7lipFwc9xJ5ZafsSj8EouKT1EvIUqcoePhAiASyELO0yGZSxDsJRteMbrk7Ck8pvp95hJJ6McUfYc+sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkID9oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLPhAAl79+2cVre/Hw7iEysTmXSDhKGdL7VDrgHigSobxTPZRTU9Rv\r\nODRyqo7aRQ3ZI4UZykYT2u16KtXEX/kCS++LQJdNym/q9tBk6SghBCKyRnrM\r\ndrc0MgGlWeGY4NSKWLD4RG5FUkiXvfIv4hSngOyWgaJYCiBdJbOxCm6m8051\r\nIiGJBA18ibKEoQleFlWi69YkOSzpe0tFl6s4bRjL019iJxulqtzQQfa6p9Zz\r\nwj+o8v+mGT86oqyvWRuphyq80GVDxIsiEHQUKOg6/IuROMmWHAgNUKnip1vb\r\nPJiI1Xw50PxB5oKt43uXCpHhpQtSZt4aZ7/2i1WJTAwOavSSav62VmoP6j/c\r\n0dVk0u0ePn5JN7qXQ1SFNjbYBv2GHs3fOBsFDa1vO3xqIeDznoA4zEphqYOW\r\nfTF0NV4qbElPLckoZCGCeWipi/9UgT23KUcwCSIFmhMkUgXyTLodnE27L1hg\r\nubOa4kPbVbi8wu/SvmCrCQ4vlFDV7ibDec8rLHOG02pSk36N7YbqMyKSUPJp\r\nyL6ZaV+CYG1ftaBELeiPvtqM+drPcqKp5lv3OFFVeaNlWv/QRqXTVd02Tg6S\r\nKCLgiBRBHZPkz2WOt01LmxtuK5hJZuvH10x9t0IMiyktvbRRJBmpE0NiUCjj\r\nxKLB/8PkRa96LJ39LKOv96lOxJzk0IeWZWI=\r\n=9AiH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "browser": "./index.browser.js", "default": "./index.js", "require": "./index.cjs"}, "./async": {"import": "./async/index.js", "browser": "./async/index.browser.js", "default": "./async/index.js", "require": "./async/index.cjs"}, "./index.d.ts": "./index.d.ts", "./non-secure": {"import": "./non-secure/index.js", "default": "./non-secure/index.js", "require": "./non-secure/index.cjs"}, "./package.json": "./package.json", "./url-alphabet": {"import": "./url-alphabet/index.js", "default": "./url-alphabet/index.js", "require": "./url-alphabet/index.cjs"}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "d96f39222a800ea9a6a156db139992b64d4b0dca", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "19.8.1", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.6_1679834984725_0.40014775177131345", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "nanoid", "version": "5.0.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "9dda930d2f876f645cce018d9e0b1facf1f0874d", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-wVZsnt5OB0dYCCfehtku3pGSS7vDjUjGQcB96wS+67JA0fILOVx0o7CnMSHZdlzGpaUtegC41ltxUVEqn5a+BA==", "signatures": [{"sig": "MEYCIQDUZ+ei7cYJjz7Vv8sPnxvl+3c9zWS6AHH4hjgygoQzywIhAIPO/Mj/dAA4tfvxyBHllznZ1noLqkN1GbbK9VU2XkXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10717}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "cf151a7476c313ff68c53701c949c287043b9b82", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "20.6.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.0_1694547674034_0.6630570316415054", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "nanoid", "version": "5.0.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "3e95d775a8bc8a98afbf0a237e2bbc6a71b0662e", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-vWeVtV5Cw68aML/QaZvqN/3QQXc6fBfIieAlu05m7FZW2Dgb+3f0xc0TTxuJW+7u30t7iSDTV/j3kVI0oJqIfQ==", "signatures": [{"sig": "MEUCIEw+cmI/j51SpWW7Yl3KMwgqwlQkHe61WAVI9HtFFhDVAiEApJfyxImgZjW6v5Ayj2zFQrUCaDquRgZc452MiytTgkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10750}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "c6c8e333920349de950135bd65e43d45b1fd23e8", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "20.6.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.1_1694548469579_0.5422313549657423", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "nanoid", "version": "5.0.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "97588ebc70166d0feaf73ccd2799bb4ceaebf692", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.2.tgz", "fileCount": 11, "integrity": "sha512-2ustYUX1R2rL/Br5B/FMhi8d5/QzvkJ912rBYxskcpu0myTHzSZfTr1LAS2Sm7jxRUObRrSBFoyzwAhL49aVSg==", "signatures": [{"sig": "MEUCIA81mE+D8f900lkClagat7sYIBYbaBF1SX3KMqUP+H8yAiEAjM4rW2b07SPYFfEKAe4tLAnWLx24hLv2v8E8U8KqPYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10899}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "87de3c52fb1d6cd00ad3eb08a0ebade8b19d0386", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "20.8.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.2_1697371981961_0.5436071947675751", "host": "s3://npm-registry-packages"}}, "3.3.7": {"name": "nanoid", "version": "3.3.7", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.7", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "d0c301a691bc8d54efa0a2226ccf3fe2fd656bd8", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz", "fileCount": 25, "integrity": "sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==", "signatures": [{"sig": "MEYCIQDbpxGkPZhr0BrSe77rfTzvorYLce9hC7IqY7Wpk5xMHQIhAJXxto+WlObuCgKvYrhgiYBV2Uaa0mzqGfrerFji1VAp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24365}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "browser": "./index.browser.js", "default": "./index.js", "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./async": {"import": {"types": "./index.d.ts", "default": "./async/index.js"}, "browser": "./async/index.browser.js", "default": "./async/index.js", "require": {"types": "./index.d.cts", "default": "./async/index.cjs"}}, "./non-secure": {"import": {"types": "./index.d.ts", "default": "./non-secure/index.js"}, "default": "./non-secure/index.js", "require": {"types": "./index.d.cts", "default": "./non-secure/index.cjs"}}, "./package.json": "./package.json", "./url-alphabet": {"import": {"types": "./index.d.ts", "default": "./url-alphabet/index.js"}, "default": "./url-alphabet/index.js", "require": {"types": "./index.d.cts", "default": "./url-alphabet/index.cjs"}}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "89d82d2ce4b0411e73ac7ccfe57bc03e932416e2", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "21.1.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.7_1699247731360_0.8893387562336303", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "nanoid", "version": "5.0.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "6c97f53d793a7a1de6a38ebb46f50f95bf9793c7", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.3.tgz", "fileCount": 11, "integrity": "sha512-I7X2b22cxA4LIHXPSqbBCEQSL+1wv8TuoefejsX4HFWyC6jc5JG7CEaxOltiKjc1M+YCS2YkrZZcj4+dytw9GA==", "signatures": [{"sig": "MEYCIQC7t0cXjiBJ5kr5pP549qFp8h7pWenCTCCrsU9pd6LvbwIhAJiw2SKd3GXo8PDTRhZVaWIHV6MViXicsNM6GifPI4Ml", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10898}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "0452313b04445abf99a5271c9bd1438841284a17", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "21.1.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.3_1699248135310_0.10610108670692786", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "nanoid", "version": "5.0.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "d2b608d8169d7da669279127615535705aa52edf", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.4.tgz", "fileCount": 11, "integrity": "sha512-vAjmBf13gsmhXSgBrtIclinISzFFy22WwCYoyilZlsrRXNIHSwgFQ1bEdjRwMT3aoadeIF6HMuDRlOxzfXV8ig==", "signatures": [{"sig": "MEYCIQCnG6V3m9rF0UdgQbOeUwe7gMFRTKQVg2aMwIUU+yNMvwIhAKwML/T/xRt6yaOo0A1PWlGwQOn5ufE8ZtsLdq+mvU+y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10876}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "bad777f438cc0f5481b93157e68264388cb76aaf", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A tiny (109 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "21.3.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.4_1701486739935_0.19048074924061797", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "nanoid", "version": "5.0.5", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.5", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "5112efb5c0caf4fc80680d66d303c65233a79fdd", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.5.tgz", "fileCount": 11, "integrity": "sha512-/Veqm+QKsyMY3kqi4faWplnY1u+VuKO3dD2binyPIybP31DRO29bPF+1mszgLnrR2KqSLceFLBNw0zmvDzN1QQ==", "signatures": [{"sig": "MEUCIQCOYAcUGMDGdd2BK1hMnpUP7t8pojU4SfxyARDRZBqs8wIgNqLi9o6R8W0U1QtzlAQKS2Q568GkggXtb9ZhfpqPB9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10793}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "61a087715a21d971dc9154c5726c818db0e42b27", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.5_1706818045870_0.8793874016579251", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "nanoid", "version": "5.0.6", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.6", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "7f99a033aa843e4dcf9778bdaec5eb02f4dc44d5", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.6.tgz", "fileCount": 11, "integrity": "sha512-rRq0eMHoGZxlvaFOUdK1Ev83Bd1IgzzR+WJ3IbDJ7QOSdAxYjlurSPqFs9s4lJg29RT6nPwizFtJhQS6V5xgiA==", "signatures": [{"sig": "MEQCIGfSGGjO5wi9gDa3uHHPK17c88512gAdWxwnCFRMDRbaAiBruV0lLLnimM/0hkGy4vloWOdHF2hfKDaSWWNyum/TNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10857}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "5f5c5b0432b4c52970b2d0100a704ca99a66b7bf", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "21.6.2", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.6_1708437406086_0.4422516298355246", "host": "s3://npm-registry-packages"}}, "5.0.7": {"name": "nanoid", "version": "5.0.7", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.7", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "6452e8c5a816861fd9d2b898399f7e5fd6944cc6", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.7.tgz", "fileCount": 11, "integrity": "sha512-oLxFY2gd2IqnjcYyOXD8XGCftpGtZP2AbHbOkthDkvRywH5ayNtPVy9YlOPcHckXzbLTCHpkb7FB+yuxKV13pQ==", "signatures": [{"sig": "MEUCIFbDQr3HVm8tktTunWQxA0JG4Bh8qy66KqGx14tDEWE8AiEAkIvuEjzdi5uyIpXSDS9ICTC6mxbUwb+i25NLlxZuzUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10942}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "4d0036fb45febee7ba0256cc22ba6a2cd1dc1583", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "21.7.2", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.7_1712508244340_0.5663217967911156", "host": "s3://npm-registry-packages"}}, "5.0.8": {"name": "nanoid", "version": "5.0.8", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.8", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "7610003f6b3b761b5c244bb342c112c5312512bf", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.8.tgz", "fileCount": 11, "integrity": "sha512-TcJPw+9RV9dibz1hHUzlLVy8N4X9TnwirAjrU08Juo6BNKggzVfP2ZJ/3ZUSq15Xl5i85i+Z89XBO90pB2PghQ==", "signatures": [{"sig": "MEUCIQCsHCOmLPIlmncC51V8OFR84lmGqh6zLPzz9Gl/xit54QIgDnM5J+f16haZwu494QqQjhtzx7p+0X7Z5s++QI3uETE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10930}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "55cd90d7f90d4ffedbeff4734a5d0d9abfec0aca", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.4.1", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.8_1730114953273_0.5680609875847602", "host": "s3://npm-registry-packages"}}, "3.3.8": {"name": "nanoid", "version": "3.3.8", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.8", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "b1be3030bee36aaff18bacb375e5cce521684baf", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz", "fileCount": 26, "integrity": "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==", "signatures": [{"sig": "MEUCIQDCX22b8tPTFFfeVn/MBBfBrZ0shZDoRq2+Hf0q1mEZTwIgVJnZoBHJMCIIYCkzTyy9Hjblcv5P0uBC/x5+5JeD2CE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57113}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "browser": "./index.browser.js", "default": "./index.js", "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./async": {"import": {"types": "./index.d.ts", "default": "./async/index.js"}, "browser": "./async/index.browser.js", "default": "./async/index.js", "require": {"types": "./index.d.cts", "default": "./async/index.cjs"}}, "./non-secure": {"import": {"types": "./index.d.ts", "default": "./non-secure/index.js"}, "default": "./non-secure/index.js", "require": {"types": "./index.d.cts", "default": "./non-secure/index.cjs"}}, "./package.json": "./package.json", "./url-alphabet": {"import": {"types": "./index.d.ts", "default": "./url-alphabet/index.js"}, "default": "./url-alphabet/index.js", "require": {"types": "./index.d.cts", "default": "./url-alphabet/index.cjs"}}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "3044cd5e73f4cf31795f61f6e6b961c8c0a5c744", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.11.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.8_1732622105796_0.7625183651230416", "host": "s3://npm-registry-packages"}}, "5.0.9": {"name": "nanoid", "version": "5.0.9", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.0.9", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "977dcbaac055430ce7b1e19cf0130cea91a20e50", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.0.9.tgz", "fileCount": 11, "integrity": "sha512-<PERSON><PERSON>yr6MXU6HpvvWXKoVoXwKMs/KyVakWwg7xQfv5/S/RIgJMy0Ifa45H9qqYy7pTCszrHzP21Uk4PZq2HpEM8Q==", "signatures": [{"sig": "MEYCIQDF6Nyp8ClPi6RuG0y1xR+p9mXkG/yLRKOguSClCoFPDAIhAPpT2e1c0oiJFXbTE1CB4iba8mWXV4/nqSEV5W6CV4pK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10951}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "65a38ac63a0709c420b9de30b15511b84e87c423", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.11.0", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.0.9_1732622393022_0.5274708047807377", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "nanoid", "version": "5.1.0", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.1.0", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "d61a0cde4db69c39f9320625fc86764c072f221f", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.0.tgz", "fileCount": 11, "integrity": "sha512-zDAl/llz8Ue/EblwSYwdxGBYfj46IM1dhjVi8dyp9LQffoIGxJEAHj2oeZ4uNcgycSRcQ83CnfcZqEJzVDLcDw==", "signatures": [{"sig": "MEYCIQCMihlgXJJwbCNlgh/LTAA9KXXp5ukeGOZFFGcrFvmLGAIhAOMEboko79avRrdv7NvtEOTHDKi1RdIOeC0IOVyD4dPf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11155}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "e0f79c916ed7996f6a1547e3af4005dfe903ee65", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.11.0", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.1.0_1739566999615_0.16991174049387214", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.1": {"name": "nanoid", "version": "5.1.1", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.1.1", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "460b54040ee1b74198c656200a30d3d87d3c1f53", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.1.tgz", "fileCount": 11, "integrity": "sha512-GJabIFYt5DiMPnl+sJYk7nuBMtGRmuKTaqsX1YLODLAcKdJPm8Kvnt/Bl5r0t52ybiMHC1kOKxDpdQZYwnu9rg==", "signatures": [{"sig": "MEYCIQDubl7ZibGlNAlf7s5p10Dv+7ylVCWRpUn0v5k343AcZwIhAL1krR4NeOjF4YtOJTnqWH/5is8d+zl39BfQyHzC+6wx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11829}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "b66602ebc6e76f80c0916da2798351ed94b1ca9d", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.11.0", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.1.1_1740253639309_0.8338296136859111", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.2": {"name": "nanoid", "version": "5.1.2", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.1.2", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "b87c6cb6941d127a23b24dffc4659bba48b219d7", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.2.tgz", "fileCount": 11, "integrity": "sha512-b+CiXQCNMUGe0Ri64S9SXFcP9hogjAJ2Rd6GdVxhPLRm7mhGaM7VgOvCAJ1ZshfHbqVDI3uqTI5C8/GaKuLI7g==", "signatures": [{"sig": "MEUCICUuphgemMQYbrTmMiAFrGp3oC/902iPBxnLBN0JrqmAAiEAgRrqtHINfglJcSnW7OIEBdWBRBoazeTNWdXURFj7Oo8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12099}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "4c1e6b04ce1ed28f93b0c9644cfd4b4761761c38", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.11.0", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.1.2_1740253688236_0.6125821114535024", "host": "s3://npm-registry-packages-npm-production"}}, "3.3.9": {"name": "nanoid", "version": "3.3.9", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.9", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "e0097d8e026b3343ff053e9ccd407360a03f503a", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.9.tgz", "fileCount": 25, "integrity": "sha512-SppoicMGpZvbF1l3z4x7No3OlIjP7QJvC9XR7AhZr1kL133KHnKPztkKDc+Ir4aJ/1VhTySrtKhrsycmrMQfvg==", "signatures": [{"sig": "MEYCIQDVuqKoDCLr/fAMbgPnllNZM8coswYAHpMkuRlhE5TLJwIhAJCDf5MEXn8IOpbFKFemPgdyqWBWM8040vtAfyOKnRqy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56574}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "browser": "./index.browser.js", "default": "./index.js", "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./async": {"import": {"types": "./index.d.ts", "default": "./async/index.js"}, "browser": "./async/index.browser.js", "default": "./async/index.js", "require": {"types": "./index.d.cts", "default": "./async/index.cjs"}}, "./non-secure": {"import": {"types": "./index.d.ts", "default": "./non-secure/index.js"}, "default": "./non-secure/index.js", "require": {"types": "./index.d.cts", "default": "./non-secure/index.cjs"}}, "./package.json": "./package.json", "./url-alphabet": {"import": {"types": "./index.d.ts", "default": "./url-alphabet/index.js"}, "default": "./url-alphabet/index.js", "require": {"types": "./index.d.cts", "default": "./url-alphabet/index.cjs"}}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "adf9b0c05eeeebbbf391c16bbd93da2fc275e235", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.9_1741346778953_0.81966762493986", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.3": {"name": "nanoid", "version": "5.1.3", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.1.3", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "4ab91f7882148771526e3f0ffb87bb5af9fa71ac", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.3.tgz", "fileCount": 11, "integrity": "sha512-zAbEOEr7u2CbxwoMRlz/pNSpRP0FdAU4pRaYunCdEezWohXFs+a0Xw7RfkKaezMsmSM1vttcLthJtwRnVtOfHQ==", "signatures": [{"sig": "MEQCICn/00BV2TyS8AQkWxqnE+AMG/K6Tn3dfnRmBlD8QgrfAiAjL1DYyGJpKwcmTHgLEcwwZHLE3OMJaiJe3uL/RG46pw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12143}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js", "react-native": "./index.browser.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "32bc9bf2eece720d57538eba890e0d0729d4a45f", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.1.3_1741347185867_0.21824635413320514", "host": "s3://npm-registry-packages-npm-production"}}, "3.3.10": {"name": "nanoid", "version": "3.3.10", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.10", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "7bc882237698ef787d5cbba109e3b0168ba6e7b1", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.10.tgz", "fileCount": 25, "integrity": "sha512-vSJJTG+t/dIKAUhUDw/dLdZ9s//5OxcHqLaDWWrW4Cdq7o6tdLIczUkMXt2MBNmk6sJRZBZRXVixs7URY1CmIg==", "signatures": [{"sig": "MEYCIQDPUyuIoyTCJJM+wtgwBFjd2rAdfNWotSLsAByrFXy47gIhAKFOPuDyyzMjpqmt4rGgrFkjR4lvumk1FDujWMvwf08R", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56575}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "browser": "./index.browser.js", "default": "./index.js", "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./async": {"import": {"types": "./index.d.ts", "default": "./async/index.js"}, "browser": "./async/index.browser.js", "default": "./async/index.js", "require": {"types": "./index.d.cts", "default": "./async/index.cjs"}}, "./non-secure": {"import": {"types": "./index.d.ts", "default": "./non-secure/index.js"}, "default": "./non-secure/index.js", "require": {"types": "./index.d.cts", "default": "./non-secure/index.cjs"}}, "./package.json": "./package.json", "./url-alphabet": {"import": {"types": "./index.d.ts", "default": "./url-alphabet/index.js"}, "default": "./url-alphabet/index.js", "require": {"types": "./index.d.cts", "default": "./url-alphabet/index.cjs"}}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "bb12e8a6f9c37ebe0b5ff2c697b8f9dcf34c8948", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.10_1742074404174_0.7623574387992873", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.4": {"name": "nanoid", "version": "5.1.4", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@5.1.4", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.js"}, "dist": {"shasum": "10b15a91d2f727b1f200faf0ff73656fd96c497d", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.4.tgz", "fileCount": 11, "integrity": "sha512-GTFcMIDgR7tqji/LpSY8rtg464VnJl/j6ypoehYnuGb+Y8qZUdtKB8WVCXon0UEZgFDbuUxpIl//6FHLHgXSNA==", "signatures": [{"sig": "MEUCIQCqp2MWNPzHsP6XlLQB9vPzNUkgOPMZNSfKJndfgXInYQIgCV04lrgCAqXyaP0epyfJWctgezIAqcpQ6/YYsZ83gxs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12143}, "type": "module", "types": "./index.d.ts", "browser": {"./index.js": "./index.browser.js"}, "engines": {"node": "^18 || >=20"}, "exports": {".": {"browser": "./index.browser.js", "default": "./index.js", "react-native": "./index.browser.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "523a74ee7a90ac82697478d3eb7c776ee6ce095c", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "react-native": {"./index.js": "./index.browser.js"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_5.1.4_1742074610422_0.6991217391153586", "host": "s3://npm-registry-packages-npm-production"}}, "3.3.11": {"name": "nanoid", "version": "3.3.11", "keywords": ["uuid", "random", "id", "url"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "nanoid@3.3.11", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "homepage": "https://github.com/ai/nanoid#readme", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "bin": {"nanoid": "bin/nanoid.cjs"}, "dist": {"shasum": "4f4f112cefbe303202f2199838128936266d185b", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "fileCount": 25, "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "signatures": [{"sig": "MEUCIECDS1Tt13mITn9t6obiq3sWPewQ/9CORKNq1kRnHrXdAiEAw2eqZwxz/AX6kumfQhXi3V11mzCEgDND2DLjlY33Xws=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32583}, "main": "index.cjs", "type": "module", "types": "./index.d.ts", "module": "index.js", "browser": {"./index.js": "./index.browser.js", "./index.cjs": "./index.browser.cjs", "./async/index.js": "./async/index.browser.js", "./async/index.cjs": "./async/index.browser.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "browser": "./index.browser.js", "default": "./index.js", "require": {"types": "./index.d.cts", "default": "./index.cjs"}, "react-native": "./index.browser.js"}, "./async": {"import": {"types": "./index.d.ts", "default": "./async/index.js"}, "browser": "./async/index.browser.js", "default": "./async/index.js", "require": {"types": "./index.d.cts", "default": "./async/index.cjs"}}, "./non-secure": {"import": {"types": "./index.d.ts", "default": "./non-secure/index.js"}, "default": "./non-secure/index.js", "require": {"types": "./index.d.cts", "default": "./non-secure/index.cjs"}}, "./package.json": "./package.json", "./url-alphabet": {"import": {"types": "./index.d.ts", "default": "./url-alphabet/index.js"}, "default": "./url-alphabet/index.js", "require": {"types": "./index.d.cts", "default": "./url-alphabet/index.cjs"}}, "./async/package.json": "./async/package.json", "./non-secure/package.json": "./non-secure/package.json", "./url-alphabet/package.json": "./url-alphabet/package.json"}, "funding": [{"url": "https://github.com/sponsors/ai", "type": "github"}], "gitHead": "609646c6441c8fcabf01b65cd5d0dba806ccc22e", "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ai/nanoid.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A tiny (116 bytes), secure URL-friendly unique string ID generator", "directories": {}, "sideEffects": false, "_nodeVersion": "22.14.0", "react-native": "index.js", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/nanoid_3.3.11_1742339192738_0.12781989558550721", "host": "s3://npm-registry-packages-npm-production"}}, "5.1.5": {"name": "nanoid", "version": "5.1.5", "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "keywords": ["uuid", "random", "id", "url"], "type": "module", "engines": {"node": "^18 || >=20"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ai/nanoid.git"}, "exports": {".": {"browser": "./index.browser.js", "react-native": "./index.browser.js", "default": "./index.js"}, "./non-secure": "./non-secure/index.js", "./package.json": "./package.json"}, "browser": {"./index.js": "./index.browser.js"}, "react-native": {"./index.js": "./index.browser.js"}, "bin": {"nanoid": "bin/nanoid.js"}, "sideEffects": false, "types": "./index.d.ts", "_id": "nanoid@5.1.5", "gitHead": "5b1220d5c25386558350ab737626819f795f7b30", "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "homepage": "https://github.com/ai/nanoid#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Ir/+ZpE9fDsNH0hQ3C68uyThDXzYcim2EqcZ8zn8Chtt1iylPT9xXJB0kPCnqzgcEGikO9RxSrh63MsmVCU7Fw==", "shasum": "f7597f9d9054eb4da9548cdd53ca70f1790e87de", "tarball": "https://registry.npmjs.org/nanoid/-/nanoid-5.1.5.tgz", "fileCount": 11, "unpackedSize": 12143, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC5Ct7Xip5MmpHlOL/TLaHEji8v74XyALeCLns0ULUFUQIhAOTuJHOHj1MsY1hqxJAXujtesIrVKYVsczg6X7d+JInV"}]}, "_npmUser": {"name": "ai", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/nanoid_5.1.5_1742339363535_0.9826532746496743"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-08-06T05:04:00.915Z", "modified": "2025-03-18T23:09:23.891Z", "0.1.0": "2017-08-06T05:04:00.915Z", "0.1.1": "2017-08-09T04:50:56.744Z", "0.2.0": "2017-08-10T05:59:22.305Z", "0.2.1": "2017-09-16T15:27:50.793Z", "0.2.2": "2017-10-10T20:25:54.297Z", "1.0.0": "2017-10-22T18:11:40.124Z", "1.0.1": "2017-10-30T15:15:26.359Z", "1.0.2": "2018-02-13T19:33:31.040Z", "1.0.3": "2018-06-04T12:19:20.193Z", "1.0.4": "2018-06-22T15:29:57.080Z", "1.0.5": "2018-07-06T21:41:04.962Z", "1.0.6": "2018-07-08T06:03:03.819Z", "1.0.7": "2018-07-08T07:56:36.163Z", "1.1.0": "2018-07-14T09:55:22.187Z", "1.1.1": "2018-08-08T04:47:02.942Z", "1.2.0": "2018-08-10T01:18:37.550Z", "1.2.1": "2018-08-16T06:49:17.847Z", "1.2.2": "2018-08-28T23:57:52.629Z", "1.2.3": "2018-09-04T15:25:29.138Z", "1.2.4": "2018-09-26T21:39:05.121Z", "1.2.5": "2018-09-29T02:10:05.586Z", "1.2.6": "2018-10-02T21:13:43.745Z", "1.3.0": "2018-10-12T16:50:06.119Z", "1.3.1": "2018-10-20T05:16:46.159Z", "1.3.2": "2018-10-31T02:19:48.220Z", "1.3.3": "2018-10-31T03:49:54.801Z", "1.3.4": "2018-11-04T01:00:49.618Z", "2.0.0": "2018-11-04T20:12:03.717Z", "2.0.1": "2019-01-14T19:04:54.783Z", "2.0.2": "2019-05-16T14:16:28.265Z", "2.0.3": "2019-05-21T19:54:03.762Z", "2.0.4": "2019-08-25T02:04:48.861Z", "2.1.0": "2019-08-30T22:34:43.228Z", "2.1.1": "2019-09-11T06:18:44.300Z", "2.1.2": "2019-10-03T04:17:24.538Z", "2.1.3": "2019-10-11T07:57:10.203Z", "2.1.4": "2019-10-17T09:23:46.866Z", "2.1.5": "2019-10-23T16:00:28.099Z", "2.1.6": "2019-10-24T06:32:25.421Z", "2.1.7": "2019-11-16T01:54:08.276Z", "2.1.8": "2019-12-14T18:53:21.168Z", "2.1.9": "2020-01-07T06:59:06.255Z", "2.1.10": "2020-01-22T03:58:42.852Z", "2.1.11": "2020-01-30T19:56:12.816Z", "3.0.0": "2020-03-26T22:35:28.310Z", "3.0.1": "2020-03-28T20:11:48.404Z", "3.0.2": "2020-03-29T21:22:52.549Z", "3.1.0": "2020-04-10T21:35:39.027Z", "3.1.1": "2020-04-10T21:47:11.768Z", "3.1.2": "2020-04-10T21:51:22.763Z", "3.1.3": "2020-04-10T23:06:00.567Z", "3.1.4": "2020-05-04T14:06:21.652Z", "3.1.5": "2020-05-05T17:35:53.102Z", "3.1.6": "2020-05-08T14:43:03.853Z", "3.1.7": "2020-05-11T18:08:25.554Z", "3.1.8": "2020-05-16T18:53:43.198Z", "3.1.9": "2020-05-17T20:57:42.602Z", "3.1.10": "2020-06-13T03:03:19.266Z", "3.1.11": "2020-07-27T18:13:01.961Z", "3.1.12": "2020-07-29T23:03:48.397Z", "3.1.13": "2020-10-21T23:31:03.007Z", "3.1.14": "2020-10-23T01:56:59.296Z", "3.1.15": "2020-10-23T02:16:16.182Z", "3.1.16": "2020-10-26T01:41:12.135Z", "3.1.17": "2020-11-20T00:02:46.859Z", "3.1.18": "2020-11-20T22:00:15.188Z", "3.1.19": "2020-12-01T00:47:05.592Z", "3.1.20": "2020-12-01T15:15:13.191Z", "3.1.21": "2021-03-11T22:39:11.943Z", "3.1.22": "2021-03-15T14:56:43.666Z", "3.1.23": "2021-05-10T19:08:26.398Z", "3.1.24": "2021-08-11T20:07:06.035Z", "3.1.25": "2021-08-12T12:37:59.889Z", "3.1.26": "2021-09-26T07:23:34.453Z", "3.1.27": "2021-09-26T08:21:22.378Z", "3.1.28": "2021-09-26T08:26:56.420Z", "3.1.29": "2021-10-05T21:04:25.179Z", "3.1.30": "2021-10-13T23:00:51.003Z", "3.1.31": "2022-01-11T13:41:05.583Z", "3.1.32": "2022-01-12T09:50:51.916Z", "3.2.0": "2022-01-16T14:38:04.669Z", "3.3.0": "2022-02-14T20:37:00.990Z", "3.3.1": "2022-02-16T22:22:08.836Z", "3.3.2": "2022-03-28T03:26:26.989Z", "3.3.3": "2022-04-18T10:29:43.960Z", "3.3.4": "2022-05-03T10:57:17.542Z", "4.0.0": "2022-06-08T08:11:03.937Z", "4.0.1": "2023-02-05T11:53:45.668Z", "4.0.2": "2023-03-26T12:45:14.776Z", "3.3.5": "2023-03-26T12:48:16.594Z", "3.3.6": "2023-03-26T12:49:44.868Z", "5.0.0": "2023-09-12T19:41:14.218Z", "5.0.1": "2023-09-12T19:54:29.732Z", "5.0.2": "2023-10-15T12:13:02.180Z", "3.3.7": "2023-11-06T05:15:31.557Z", "5.0.3": "2023-11-06T05:22:15.512Z", "5.0.4": "2023-12-02T03:12:20.236Z", "5.0.5": "2024-02-01T20:07:26.003Z", "5.0.6": "2024-02-20T13:56:46.276Z", "5.0.7": "2024-04-07T16:44:04.485Z", "5.0.8": "2024-10-28T11:29:13.535Z", "3.3.8": "2024-11-26T11:55:05.967Z", "5.0.9": "2024-11-26T11:59:53.257Z", "5.1.0": "2025-02-14T21:03:19.902Z", "5.1.1": "2025-02-22T19:47:19.495Z", "5.1.2": "2025-02-22T19:48:08.428Z", "3.3.9": "2025-03-07T11:26:19.120Z", "5.1.3": "2025-03-07T11:33:06.062Z", "3.3.10": "2025-03-15T21:33:24.346Z", "5.1.4": "2025-03-15T21:36:50.628Z", "3.3.11": "2025-03-18T23:06:32.923Z", "5.1.5": "2025-03-18T23:09:23.726Z"}, "bugs": {"url": "https://github.com/ai/nanoid/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ai/nanoid#readme", "keywords": ["uuid", "random", "id", "url"], "repository": {"type": "git", "url": "git+https://github.com/ai/nanoid.git"}, "description": "A tiny (118 bytes), secure URL-friendly unique string ID generator", "maintainers": [{"name": "ai", "email": "<EMAIL>"}], "readme": "# Nano ID\n\n<img src=\"https://ai.github.io/nanoid/logo.svg\" align=\"right\"\n     alt=\"Nano ID logo by <PERSON>\" width=\"180\" height=\"94\">\n\n**English** | [Русский](./README.ru.md) | [简体中文](./README.zh-CN.md) | [Bahasa Indonesia](./README.id-ID.md)\n\nA tiny, secure, URL-friendly, unique string ID generator for JavaScript.\n\n> “An amazing level of senseless perfectionism,\n> which is simply impossible not to respect.”\n\n* **Small.** 118 bytes (minified and brotlied). No dependencies.\n  [Size Limit] controls the size.\n* **Safe.** It uses hardware random generator. Can be used in clusters.\n* **Short IDs.** It uses a larger alphabet than UUID (`A-Za-z0-9_-`).\n  So ID size was reduced from 36 to 21 symbols.\n* **Portable.** Nano ID was ported\n  to over [20 programming languages](./README.md#other-programming-languages).\n\n```js\nimport { nanoid } from 'nanoid'\nmodel.id = nanoid() //=> \"V1StGXR8_Z5jdHi6B-myT\"\n```\n\n---\n\n<img src=\"https://cdn.evilmartians.com/badges/logo-no-label.svg\" alt=\"\" width=\"22\" height=\"16\" />  Made at <b><a href=\"https://evilmartians.com/devtools?utm_source=nanoid&utm_campaign=devtools-button&utm_medium=github\">Evil Martians</a></b>, product consulting for <b>developer tools</b>.\n\n---\n\n[online tool]: https://gitpod.io/#https://github.com/ai/nanoid/\n[with Babel]:  https://developer.epages.com/blog/coding/how-to-transpile-node-modules-with-babel-and-webpack-in-a-monorepo/\n[Size Limit]:  https://github.com/ai/size-limit\n\n\n## Docs\nRead full docs **[here](https://github.com/ai/nanoid#readme)**.\n", "readmeFilename": "README.md", "users": {"dwqs": true, "miloc": true, "tttai": true, "embarq": true, "isayme": true, "merkjs": true, "scull7": true, "progmer": true, "restuta": true, "shivayl": true, "heartnett": true, "kgolinski": true, "nickeljew": true, "reyronald": true, "sn0wdr1am": true, "tjfwalker": true, "yanrivera": true, "zeroth007": true, "miniocean404": true, "robmcguinness": true}}