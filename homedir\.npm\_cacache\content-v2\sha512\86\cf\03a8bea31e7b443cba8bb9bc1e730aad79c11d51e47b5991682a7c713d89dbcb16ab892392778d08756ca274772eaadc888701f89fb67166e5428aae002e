{"_id": "busboy", "_rev": "168-bd67b9de2ec65a6f63c2705d88db3a59", "name": "busboy", "description": "A streaming parser for HTML form data for node.js", "dist-tags": {"latest": "1.6.0"}, "versions": {"0.0.1": {"name": "busboy", "version": "0.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A node.js module for parsing incoming HTML form data", "main": "./lib/main", "dependencies": {"dicer": "*", "readable-stream": "*", "iconv-lite": "0.2.10"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.1", "dist": {"shasum": "504c835b00040ebf992e072c47cd5dc3fa0c785d", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.1.tgz", "integrity": "sha512-Onmf9gXEk/yCArrjhFaTW0xNaS5sAQXrdK0i6D95pgASdBSQfCS6s+c+tclAufSecYhRQ7TWMnakVOP4GmiCew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCO9xYFd5Gv6OVwQhuwXrZ/YxcEyp0SkHW3D4oSHR4viwIgCAFKM/FGKdZq1aHgKx5R4Nrdoy2g/ksFppKiirheRdk="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.1", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.1", "_npmVersion": "1.2.23", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "busboy", "version": "0.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A node.js module for parsing incoming HTML form data", "main": "./lib/main", "dependencies": {"dicer": "*", "readable-stream": "*", "iconv-lite": "0.2.10"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.2", "dist": {"shasum": "41e46429e7b14d377244b640207057d31f8f6fc3", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.2.tgz", "integrity": "sha512-GBrE5/F4y50NzJYnKqTWxFRn6W1uQmqVMXU1C8yH3fxU0zU3VsMQET7Vs5fY24zOKlff0PM4HzfCIPmkqF43LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDlPTlKiFSe+RJxb9cjO0Zz8+UcuUqe1cCb7R6RVc44QIgVmL4wIjccAwbSlNbVPB29p47pGnyCYvgyCFX7qVhNo0="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.2", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.2", "_npmVersion": "1.2.23", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "busboy", "version": "0.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A node.js module for parsing incoming HTML form data", "main": "./lib/main", "dependencies": {"dicer": "0.1.2", "readable-stream": "*", "iconv-lite": "0.2.10"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.3", "dist": {"shasum": "ac34260e6f1794793203ff3102762f23fd52e1e4", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.3.tgz", "integrity": "sha512-Ocbt1bq4f9azU2BSezcowaHCIv0EXFm9ymgZ5FdU6W0u+WQEJB656a6Z9SJ2NxFAWolpatEDkFIfQXonFKvnwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeKOtKFu6Mrh1QfU//ndSAMd19xuXX3rZRWVG3E4xKMAiBtKSq3g/7MKfnAFT0JQ/j9iIgj2IrxAfFhTXUYdcmFfg=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.3", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.3", "_npmVersion": "1.2.23", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "busboy", "version": "0.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.3", "readable-stream": "1.1.7"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.4", "dist": {"shasum": "0085829400318ea87f6a78e638796ff0fae6f3cb", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.4.tgz", "integrity": "sha512-0oO5xnYiN7bpgqYwpdYJkOFkobwvd0jMeaSW9p+LcTsw4rFB3YxfGv1Qd16vLg6X1AZfUEd9tUM+fwcFd4V5Fg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFo3H883s7haznxDU3dbDTV/qItqwtfnC5YGkGJas0XSAiAZqVS30QaQ0v95K8OkD00uUUx3P6YUnLGipIIyw78Q5A=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.4", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.4", "_npmVersion": "1.3.8", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "busboy", "version": "0.0.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.3", "readable-stream": "1.1.7"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.5", "dist": {"shasum": "1b663033dc77c1eddb2927c6ecca2d4968fa5f89", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.5.tgz", "integrity": "sha512-KU4Vp2zMY+v3WR4npAju7Yip0PA/hofxLe9Da9UaagItDIrIxUuyS0IRtLzgrZcSwdyxL4jMgHc0nam8kxgZmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFGRWIEKRHfYrligeEW8I8eBY8HR256dbUKq590d0UaAiBaLYwSDsz2LiwNfUgUnBrBtexm/QhtXbJ4gFQLiD8kzQ=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.5", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.5", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "busboy", "version": "0.0.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.3", "readable-stream": "1.1.7"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.6", "dist": {"shasum": "55520a98b70bda978d4eaeab77279934d77aa359", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.6.tgz", "integrity": "sha512-8o8k8jNN9Tzx1y5jgQZpyy4dJazJSYcqov585gOV10Yws84IxyHlv8KQchtxA04lq/lKUafhw6qg+KcoDXDOKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHyfnpbtfAzFAPinSodMF5ItgVuXQzNlIw1r4QMJcobsAiEA6/H0cIbxiYVR6btk/MvSDN6krJesIG80UMIlZgObZfg="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.6", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.6", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"name": "busboy", "version": "0.0.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.3", "readable-stream": "1.1.7"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.7", "dist": {"shasum": "02b08edfc756a53e74ddda7802f6b10d2cdab0e9", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.7.tgz", "integrity": "sha512-d/LsThqw1PElwxeu2nY1uhIWKlU5+Q5Y8aOHqxWJDEiWpZEpjJZ+MLkJofnTHcB2pABkiV5/PXSfyZoaZ/OIIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB72pt2U69sTW/eiPYsPpeJgRSX8tfRWjpXqG2hcsENQAiEApSq/ecFrJm4X0SW8poAROxY7plHYqD6+1UEKIC+L6bs="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.7", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.7", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.8": {"name": "busboy", "version": "0.0.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.3", "readable-stream": "1.1.7"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.8", "dist": {"shasum": "ee1a10b56942882089f68767005e0ba0a9c7052e", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.8.tgz", "integrity": "sha512-q795BYDxqYiccfAzZJDRco3NBmswX4DOFsSxwEdza6kKstDhhW3jE0Uct1BNk9GsB5YZof8mkjGkb41NXTALPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFSz64PhSPl9RbxhK/1ZMX/Q7UQuxjIl9QkmFBrgWm8eAiBzxj3l8u7Tniv2CWK15afLVCfNvi6rcUyQUm7n17Mt2A=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.8", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.8", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.9": {"name": "busboy", "version": "0.0.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.4", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.9", "dist": {"shasum": "40a417350a8ef9f48774db34409165ede8c4c018", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.9.tgz", "integrity": "sha512-CbMTAuGzJHQl49mnLP7Ik7fxBNmc1kWY6598U7jIBIuluL70DhC1B8kp2RK2kO6SauKQbkrz01sEb2wA29leUQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGQ03S8wbdRvNfV4lGM1y1sOxylKKl02vegob+NMY65PAiEAm1pkaPKKR+p12DG0KrRoPQAtd6GqJ+DHRZbxSrjV/DY="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.9", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.9", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.10": {"name": "busboy", "version": "0.0.10", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.5", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.10", "dist": {"shasum": "1cfe7c696c4dac798c0f06cdd6a1d52ce0c89892", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.10.tgz", "integrity": "sha512-85XnCI4QMLpxIX0X3EmU+N2FITRoSwf+o+b56Bz8uKk9Z6hpv5IfOl9BzQU1d8zOIpdHgXr9b4EpMDqW21XFpQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc42I0LoeB3hJ+KHRLXMz5lWpsWgUpwpPzn38PnZdVMQIhAJi4EQ6Hj698P4zJp5vZDTf5ACp8LZQiXYfy/f981r4r"}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.10", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.10", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.11": {"name": "busboy", "version": "0.0.11", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.5", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.11", "dist": {"shasum": "010f401b84022ab4b220075f331d94c242831709", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.11.tgz", "integrity": "sha512-vN7v1XGtj4moqqCqfnYtrKFmTl5S6h3E4XpZckJXKdII15eYsLiCx6XfU3TD3fw7snJ7TMEpG7qUC9ckjwZS7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7kpE/FYCWvxpraNZnG6kxq74LjNpWjcV1b5+lJ4L1egIhAIigX4haJT65MydBsAJIniwTaJyyMiFSCbfupGh2FvNM"}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.11", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.11", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.12": {"name": "busboy", "version": "0.0.12", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.5", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "_id": "busboy@0.0.12", "dist": {"shasum": "22de65b30d0e6e3eca5947417cb70dc9cbf03df7", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.12.tgz", "integrity": "sha512-xM6WF005Dz9rozFSBoGzqV2KXBuSj3m9e4qKtO8qJoXht3BPGfgSMFPB22LlI/37nojDNp41yXQYEYZPfE/MuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtLgniCkwDa06C977GsxN2sj+cwNnLDfenDbgs7TyqnQIhAP7aO0ZUS2f7RoSAncnT7Wrxvsq9vpVzHxP4vfb2Xi3F"}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.12", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.12", "_npmVersion": "1.3.11", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.13": {"name": "busboy", "version": "0.0.13", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.6", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.0.13", "dist": {"shasum": "8c73ec80e6256ac4d22f232bf55f62dff87f22f1", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.13.tgz", "integrity": "sha512-GNP18WPp8qSdBqmSedvncspz1RPfkBU2Ddz/6RgdNhJJ1HmtyRARbCtvUGHaPJ6lvR9SGq+zlyC9UM7f3Aixrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZvR3ZvFEpKdiR1uOYColsEXm+/HopqglFZmazFoNy+gIhALa6vJCrYS+EJh+tlflQUGZIosY5YbWi5DE8QAW9rJfT"}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.13", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.13", "_npmVersion": "1.3.14", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.14": {"name": "busboy", "version": "0.0.14", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.6", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.0.14", "dist": {"shasum": "21583284c127aa70cb9bafe1521c8d78b071fc6d", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.14.tgz", "integrity": "sha512-/AiFcHjV/D35zNt8qQ3p/XgEINMkbOZhFFEK1LVVlPjO28RviR5Jphbtk3AtW8P0ULRep2MLvdcCcPpSRNEqJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBosqiTJrf6m6cob2e6B5WIrMx04mX/6cXzeN2NO5ftBAiAkyOl+tTIz1VMRYw51ST6zXFAT9bBghPfTvWXq2139Lg=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.14", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.14", "_npmVersion": "1.3.14", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.0.15": {"name": "busboy", "version": "0.0.15", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.6", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.0.15", "dist": {"shasum": "618b3b4a93f578b35a28e4f325aa4d3e4610a3cf", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.0.15.tgz", "integrity": "sha512-xk2jlDOC5IKeosarpAKKYaX9JakunuApKZds+k9zs8S/ccnsNGJfggqLU7Y/YS+i0Lmt1zYyjfGanM933xR5PQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH0axIL3VnQTxRiYS1s4cBigCsdaLbIEi09k9YdUgSk5AiB2lMvp5tQeiaqTGcMJQwfO17PipUtqTg2SL4stl+w3hA=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.0.15", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.0.15", "_npmVersion": "1.3.24", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "busboy", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.6", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.1.0", "dist": {"shasum": "8bbdaa4d069c848478c93bff339f7853851580f0", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.1.0.tgz", "integrity": "sha512-7BA1aiggsbB5EBgfOw/ddxCBKYKjtEgWjzWczKt89yzw2koWnY6NvrcuD8GZY5WttA/kK/543KSb0s90/E2atw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDvw+C77T1xNu/HZEm03vc+G1OIcb8Wl13L9B4Bne5RQwIgQQHNUfwxTzRwvHPihWm4U23S9lHo5YFgKPYyGkFEFCs="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.1.0", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.1.0", "_npmVersion": "1.3.24", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "busboy", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.1.6", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.1.1", "dist": {"shasum": "a466da847812a01c93a8b927aa0c7f8209bedb87", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.1.1.tgz", "integrity": "sha512-jWbshdaRUdwAXyzBDLR2oVsCBTUP+gsO4hfrFbkvhYtR2iD6Iqv6M4XP2AsnumunUaW0PCOUYwZAtlx7ffcL+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwZdZX4/SU1EV6eCU5MmhLO6eeaFkiDeftMqO3BTNygAiBVtgppPp3dvs5PU/erf1lo7orpYwOKu6Zc42Gf7dKJCw=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.1.1", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.1.1", "_npmVersion": "1.3.24", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "busboy", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.x", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.0", "dist": {"shasum": "8b32e9709e8fcd3668ea83a4de748cc0dec7c19a", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.0.tgz", "integrity": "sha512-i8dFdZHml8qs0bz14pYnFkH7j023bXhlSSnvkOTQcAUtk72GNIt1e32G7J9j4DfH2yUc90k+mCOMkAZhPIOGKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiVR/QA823AFNKtxWJ5lW7S9iX2a/7GImLXtfHJxOfsgIgewGj482d9CKC2ZINCNoLgDcthYESNJrL06K6dLDVbxc="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.0", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.0", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "busboy", "version": "0.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.2", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.1", "dist": {"shasum": "09df8db5105959784716d3277472d24f4c88350e", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.1.tgz", "integrity": "sha512-Aat7cI6RLfhtu2rgNcdcknBrcCK0fvDE53dLhDafBIfGx1MM+E8tF9HQX8ZSs1DHDnmrLiRiX+m4GbC7E90wkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAOzt9fyGDGwzNDBHKD++z6Pa/Y0mclKHXppNbz8GeJkAiAUN54XjIi6kDoZiXk+dzZf/zAg4Jvgg/YjK0Jrdjsx5g=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.1", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.1", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "busboy", "version": "0.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.2", "dist": {"shasum": "d482f2661e0abc9560a5056bdc65425567adf1e2", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.2.tgz", "integrity": "sha512-rm4jKddKAfmRVkl36jsLzUSQV/X1Kz2/jrm0A22eP/iI7kHsDmjhG3f3fQf+vQgwP170eY0OwHHN0/z7m/RTpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC+VLdGd+a5wijuy/M7wCJlZMPZXjlqyLZUw395sQUVYAiAflrqnXKE1j6gwAdsuWpGCWlzWfzNfyooh4xBwlOOulA=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.2", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.2", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "busboy", "version": "0.2.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.3", "dist": {"shasum": "e7da149f21fe27386ed977dbaf80b926a26ff4a6", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.3.tgz", "integrity": "sha512-7u9fzkXRq7IzbcktsLnwfpvZugWr0/GnueB3N+zk0vdmWNItsVMQRT0E7gwEaTa9+dxxEYVFvOHIPfYJ8T1brg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGk4OUIeLriICc1hveJEGxYwMLoHsj6V0IC6RO7EBb3/AiAtZO63w4zNocC+GyGr96Sifa8j5h2XIVUNLo67LZimqg=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.3", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.3", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "busboy", "version": "0.2.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.4", "dist": {"shasum": "1977e96e1ee884649651ebdf548ca900758ba7f3", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.4.tgz", "integrity": "sha512-hm0XN2dlecinlYSaB1TmSmuOaFlf6TKl0ITjqlszN+4O8z2eNwjUX3ak38dIXa7Pi7wme62QE7Wi2lC5L5Pu5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnL0ExO5rEV2XcZPtC1Mc8pRwKpNMlgA9hfOFmL7X3uwIgJmehxxDL27BLe6ddHlj6wWUTedxNeg4mnSLEQVGovZo="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.4", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.4", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "busboy", "version": "0.2.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.5", "dist": {"shasum": "245105f41dc1fa2b8039a6106bb87d0b60f4717f", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.5.tgz", "integrity": "sha512-KIGUcj7W1UkJ/gf+256m633jaPas8PAqzidnvdAmkzkam/MuALgMfIeelIFO7wC/HilmTycClk6WJGGCvgTQAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbqC8ENt7y96L4+DME2oi2/dQKWlHtacoIBS46OVEC4AiAqOft3R6zlYEE/NLst9VyUYcFk97csPl5xsn3e98kT2Q=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.5", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.5", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.6": {"name": "busboy", "version": "0.2.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.6", "dist": {"shasum": "ccf1ceb8a4b75461c40676a5be52a16c4252a777", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.6.tgz", "integrity": "sha512-maQJ5BFSnGq3PvUYX4BugsqOeb0kKIt3q9rb+NTy8SZlfcbv8wN5/8AHlpe1rUNQqIekS+joZCuslFTbqxAaxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHRj57l9kJVBB2C+ygINmDje7nInL4dpsGNVlf0PmOQAiAXfnl8g5ZyjosAe2ZXDUUjxakghopb7vYSnABV6mg37Q=="}]}, "_from": "https://github.com/mscdex/busboy/tarball/v0.2.6", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.6", "_npmVersion": "1.4.3", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}}, "0.2.7": {"name": "busboy", "version": "0.2.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.7", "_shasum": "e2a2f5f265f5639e16cf33d974dab846d8298246", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.7", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.7", "_npmVersion": "1.4.14", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "e2a2f5f265f5639e16cf33d974dab846d8298246", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.7.tgz", "integrity": "sha512-frFQGRwcQ/jSTnR0kl06GUpOyd5ec1+X2WINCt0jG5aJpZPbpaYPdOoZCGD8lmHij0iqe7VmxV6kRdkR60xFRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJgAOJgFg0UK13PCHCsED7zkv7TyyeRwUAniVdWijE4QIgHlwaO2c9n88EdAjjnKQuUseOkPZl2+0okwobW9PmGw8="}]}, "directories": {}}, "0.2.8": {"name": "busboy", "version": "0.2.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.8", "_shasum": "8e8357b05be7366532234975d735e32a5af57eb5", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.8", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.8", "_npmVersion": "1.4.14", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "8e8357b05be7366532234975d735e32a5af57eb5", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.8.tgz", "integrity": "sha512-oxlv7S5KCD+eDuwY8UagHo/6y5ZF6yoXD0GTuj+tYJpN1XdjxRIfAlZ7ZqEdX6aXmVzHVB7RE1ZGhGgiX1JkWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICOwXR3Ylno6K/jX5A46KVftxxFgUrQWXNrGy6DTQXG0AiEA4QlvGQHbO/+PlXgQ5XcbrlCjv/A6rkmLkYkh8sOyEEc="}]}, "directories": {}}, "0.2.9": {"name": "busboy", "version": "0.2.9", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "http://github.com/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy", "_id": "busboy@0.2.9", "_shasum": "a0a181e78b19dee76974560f55843b09eaea7376", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.9", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.9", "_npmVersion": "1.4.21", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "a0a181e78b19dee76974560f55843b09eaea7376", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.9.tgz", "integrity": "sha512-JbMS7BA5W2zGyhtjkyiCv53vK1CSPBsP/fZQBH+uc9/zNFBgb1/L/Ll9QsOsYwqEWK0+nyIMmp0oceZJ7uEiYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZvhZjmmTtzaW6WNFhb2RCLDghiQIgyf+HkvsFHGoqOAiEAo5C2tIb6GhBowCAoPLEUvY1bck8RnHBqBWT1uHj8hUY="}]}, "directories": {}}, "0.2.10": {"name": "busboy", "version": "0.2.10", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.2.10", "_shasum": "3b69ed38cc93ca245d333ed1a3c245b0abf29731", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.10", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.10", "_npmVersion": "2.14.1", "_nodeVersion": "0.10.40", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "3b69ed38cc93ca245d333ed1a3c245b0abf29731", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.10.tgz", "integrity": "sha512-pkgJJ9vMAJUXKU3iZ33vIXwHAZ35JZDWdva6frG9AuOsJEpe5YL05xq8bxsS/UvuF6qM6P0TuxupY6ZSQNa/fA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDae1jTfFSRGpmZQ+Kw/NJMygqoNcZPXQdh2L9QfUQtxwIgE/Owv/ha8j9nwpqUqHQs6wG6bQj6HeRNrbQ5FC9Ovek="}]}, "directories": {}}, "0.2.11": {"name": "busboy", "version": "0.2.11", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.3", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.2.11", "_shasum": "191347fe935e5afba665501a911c2f723d3b61f2", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.11", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.11", "_npmVersion": "2.14.1", "_nodeVersion": "0.10.40", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "191347fe935e5afba665501a911c2f723d3b61f2", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.11.tgz", "integrity": "sha512-wQuZjyniPiCb1Bidb7MhhgKyKQzVhy8Lb6NGlEGxWbxM3XltzhEkxqvKW+YxP32S2GZJmH4xyUpwCdDwO+rO0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAwUbClJooVPHndQxRbur1Zqx3Amj9I4rNpZEl5E03ZPAiEAqB3fUfTma9jZKPLDxjAGU1qvqpVyv9OO7O7WVulaZsY="}]}, "directories": {}}, "0.2.12": {"name": "busboy", "version": "0.2.12", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.2.12", "_shasum": "bf3f080dede87c72a028a3938081f3b1adf0b3ba", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.12", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.12", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "bf3f080dede87c72a028a3938081f3b1adf0b3ba", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.12.tgz", "integrity": "sha512-uxNduU28e2WoDFNmBHMimKDxZRKB+wvwnGENWb2GhEER9c09Xzmbd64BmWxdFfcDexjpqqwIEAEiTlaHAPNQkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEm/xj/TQSfaDgpBh6KUPdxmWY7wRCJqt6IP/GcM7zjgIhALQ8lFAREYK7F2922aKBqQUzGzGEPYtFyLbEhDxkInFx"}]}, "directories": {}}, "0.2.13": {"name": "busboy", "version": "0.2.13", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.2.13", "_shasum": "90fc4f6a3967d815616fc976bfa8e56aed0c58b6", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.13", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.13", "_npmVersion": "3.7.3", "_nodeVersion": "5.8.0", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "90fc4f6a3967d815616fc976bfa8e56aed0c58b6", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.13.tgz", "integrity": "sha512-FGE17z02xke/Ig4wpit6lDmI8vEbS6kDKtElLom8Xs0RYSgPinkQ+z5em+MFhWzyYH3/ddQseCq7nYmsVkmymw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFWsgL5Gm4Mhao7nMN/Fl/pzrFKliBH+UMfSvW/sz/e7AiAXnWTVw0GnfoUUZgphfVEdoZdZvxCbIw/gBXEHCXqgug=="}]}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/busboy-0.2.13.tgz_1458666955253_0.557709296233952"}, "directories": {}}, "0.2.14": {"name": "busboy", "version": "0.2.14", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=0.8.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.2.14", "_shasum": "6c2a622efcf47c57bbbe1e2a9c37ad36c7925453", "_resolved": "https://github.com/mscdex/busboy/tarball/v0.2.14", "_from": "https://github.com/mscdex/busboy/tarball/v0.2.14", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "dist": {"shasum": "6c2a622efcf47c57bbbe1e2a9c37ad36c7925453", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.2.14.tgz", "integrity": "sha512-InWFDomvlkEj+xWLBfU3AvnbVYqeTWmQopiW0tWWEy5yehYm2YkGEc59sUmw/4ty5Zj/b0WHGs1LgecuBSBGrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjmBtjPdQeP0ftPFZK65NMsI189GBpWf1XFJH3Xbw6iAiAWCmrj/o5KF3PZDKEJmNBZov1QgNRaD1kCqM64oxMpbw=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/busboy-0.2.14.tgz_1484764782815_0.4685383520554751"}, "directories": {}}, "0.3.0": {"name": "busboy", "version": "0.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.3.0"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=4.5.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v0.3.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-e+kzZRAbbvJPLjQz2z+zAyr78BSi9IFeBTyLwF76g78Q2zRt/RZ1NtS3MS17v2yLqYfLz69zHdC+1L4ja8PwqQ==", "shasum": "6ee3cb1c844fc1f691d8f9d824f70128b3b5e485", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.3.0.tgz", "fileCount": 22, "unpackedSize": 682033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNoJ3CRA9TVsSAnZWagAA5uwP/i33XeG/d4QhQwQADlNL\nBuZvbcJ7fIMbjIbFWRNCQ4OiG0jFK2zGjk2RCa9cdSrNFMq2IL8yr+vYICjE\nuQrAvr2XoY+YEqVTPWFH9eFIjwLhH+9EegwA467dQfFod/lxpe1c/laysKsq\nuimpMIdJzh0OzeUBZH2RePzpDtq58HR+Q1xredvFMBKr3O5uW5LE8hfCFlmL\nkxcafiQMK6ZZpi/eXX5a1PWTIyP1x65WYeDiYtrOTR5HFRM7pFf/63nLNbN+\naC2OljBa7pd8RZRjfRHfpBn/bb/6q4h3Asy3JhivnlGBFGpkkRZEoDr1O0Re\nrye8b62zHKtCatzLfVJEa5juW6jYBZaUe3oyfIrVUz+IfaqrEhsU68EywgkK\nR8YOEvOSfErZoOLgdg1KGK2DlrTJte1op/tG/vo2oy0zM3KifmgHtFK4BvVq\n2TQvJza22CJzp9UqfD6n4p93BW8Fuju/oPJ6WWt4SyoQF57I68zifnr+d8Mi\nPgApDoKAYYWLenzv+Bi8X66ucgh+yjtW9TeCjB3ZhEms/WF2RQnuNDuQTUgm\necdDDtZ2E6Us3uAshpnn+EV55EhvDvHNuL+1h3Fd5N7lxSnqIappgmzjm1hf\n3Kjq+Z/GEu+MYccwxJ0NMxsiWR2E9n2areQtqMlpQe2KFg/1vLj45GbLyyL3\n2zy1\r\n=SJ35\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkNHDUkv8HaDuKQRiJWDnUYjcMyUkGUskyuVqrR+5sQwIgFeObkPbE+0b6d4Z+XUYi9Lphy7dg6xlRjZKVcGsoCz8="}]}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_0.3.0_1547076215058_0.9247827324979925"}, "_hasShrinkwrap": false}, "0.3.1": {"name": "busboy", "version": "0.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/main", "dependencies": {"dicer": "0.3.0"}, "scripts": {"test": "node test/test.js"}, "engines": {"node": ">=4.5.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v0.3.1", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@0.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y7tTxhGKXcyBxRKAni+awqx8uqaJKrSFSNFSeRG5CsWNdmy2BIK+6VGWEW7TZnIO/533mtMEA4rOevQV815YJw==", "shasum": "170899274c5bf38aae27d5c62b71268cd585fd1b", "tarball": "https://registry.npmjs.org/busboy/-/busboy-0.3.1.tgz", "fileCount": 22, "unpackedSize": 681474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqSTRCRA9TVsSAnZWagAAOx4P/3pZfn42Io2XWBeCuUnq\n5XPCBuNJe7+xNAaBsWUys3uj+HpQL/o04nL8U/njsHVZXz9Vk7J0k7woeWhi\nrxUzVgApXGsqAH394NeDlnJVUz4Fv69KhGTzpf1A7w8a0VlF5Q/UmUEkn/12\n6IPxCa1NlUo2o6+4u9gT3BEmUWT1qzjefKFO8pJDn7prEPEQSx2Fom3TaB/m\nmhNkxkN8X3QSQSfLn005Wluirga5UsyLaAvqKP6feyCleeCHefe6l9bhQV3u\nK395V7BlJr1DEYnzU3kvr7eia3vxftp98fi4/cB644HmCGdZCUdXIq3uY5ym\n1vTFMlXhEI595ZGmqZDaoZYA2iFdEC929GjiWAZND3AcvZMOomVjv0WFBGwj\ng2gqft+RcPqCkmahTlWAB5uvdO9IWpHiCG7MMZ3/I0me3utPOKJO2bx9FHG4\nktp0+0x9Ma4C8/lkeN9T2HvQkZIvQzM6FMSVHVUyYZEDlvbS9p74HdllyVe+\nP4HydCJ5qCa5HC1eIZNCMdShrFrQXw2Z/k5iGEdd/4F8oPUlLqAmYwCaqgh+\nywZV5r/1SlgvjlSeTmhm1WaZSt0SmUOa+YfTXVZheeULGatCV40fPl6dvooE\nmVTeUP7+OACcUSjk8SmW3hTOv+QRsvisDK+7Q2biFi2u/BQ8Kmr8PQcKMC3/\nmKaz\r\n=+JUa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsB59WZiq2G1JvlTJA6dUWXgwlbJ9cBGrpZ/6NXRoiMAiEA1OonBjF8DrRmoMad3vcRHKlg9mcfc8+u3tWSRoD2qXQ="}]}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_0.3.1_1554588880409_0.8804512357173848"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "busboy", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v1.0.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.0.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-/rUt5teTUrAFv6Gq2mD3UIqZnD3YDWoFuLWmLCQvy3cQmk0q/8O/L8V7yJtDIBR34ZifQCKQcu4zhPmXvzYjqQ==", "shasum": "5c66fb349e8c22158c8e720b10734732919b40ce", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.0.0.tgz", "fileCount": 28, "unpackedSize": 119071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhv5hoCRA9TVsSAnZWagAAlk0P/R/Bmw74gUnb6BJ+PxHE\npXyKn4DsxtgmgxcoIt70XIVfgVlqGtEjiSC/qpQLP217Nf1YrjFLE+G3peWd\n4qTrYD1DUXWu1fVY896hazdWUkky7nXOKyWP/b4mLmoN7zzLk/DUN/XDWgTW\n+nAno0Rex2h+VTiOOfEPppiS8gbaU1+kbkkErWNwWHRRxsmkmjS/+wyPncWc\n9WNk/cWfM7A1HO6akO9F8fSm8Z5jvCfERJ+lAFinH5uKxr5q/d+gwLI+d6Er\nXzFOBDRhsMxIB+S72/zrw4jgfm8vedwDHUnPccmO7KK7Ye5ys557J0dtxnoh\noOor4zqYZ8B/zHYZoP4ChvCINKR3GFC01OBInrenIH+5yXk19MYNW3zWkvb/\n0S/dvn7ea+5UX+Rk9KX3CwvgjQ+LmYFe9mtCs6SfZfobMgvF4utE8TO4M8j3\n1hmCmfgHuKpXG85SDCX0vio+3oMJbdHbAmZtcRaE5pKmZDr/aGIZMk7ONZOe\n2s3C7lCPLqfP2CIDFYZ5/gBGALKAh/aEUh3yo8xb7MjR9XSML/frdy5J6NGV\nFAlkPrACuRbUkseHMa3WMTr3067m/8+H3GZN50zgJP0Ipg9AYO1F3w2CqFNh\nVXg8pBGLEWHjeVi0CnTGwdwwETLbJjbSfG/f8WYQn/Ek08oUtIJf+jEYh/jq\nzebb\r\n=imWG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDALFCGk5No/OtQheYvPDKDTtsXVVkZjq/iuCU+4fgNtgIhAJ3fsqH7asAJ0sbVUHlI//wbG1hd/NWho35hYBdmV7Hp"}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.0.0_1639946344718_0.2800405134578956"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "busboy", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v1.1.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.1.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-mPSE4F6YmIwUGuiXtkk4IfG6oseCAIrn/YcIW2778b07k9UvlMNfK4C2900KRAU+euDEcS0xx3c8iA92z+9Ljw==", "shasum": "b678112b976265d4b1e2709b5506d52f951380e8", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.1.0.tgz", "fileCount": 28, "unpackedSize": 120044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzA1bCRA9TVsSAnZWagAAqm0QAJ5SmtkMKlTWGwmSUH5w\n6lQzLeuwa+rBPeudA9Az9pzeCW/4EG743CMYlo2zhU1UI5FVz0RAgUbAO+c6\nPOtYVnwzUxZ6Y+eZgWY1aa62VKHpYTm+S+8dEk/PRb0Av9tJGW7OZ6pYisRF\nzJOCc9dDbIU4Gd5VPiskEZOCgIkZOHQwJaZpoKKuslj2tnc7n6lCbEsoLV2b\nu9re0h7J1zBO/FtQZJQWjofP0A+2zYvFHeibdihLxiGbRyqZJLzVFLmDZhU9\nx5Dc+FYlXPy3ZZMH4FuuDwyX5MQ0ggL5IbLECSoLgkcbD7dYiLaJgR1bBsf9\newYW1WG0D4c0XtgqTwkRYKV09j/TPl1kzmUjO0n3neSRq1mbLHJIYHooT5HO\nBFDRtDG4W5PrqRPlsy/Na8fLx9PoKvIMl1zqmtqsmNAUa4q6/+HPoJcab88n\nYBZ6Pfg/MtbBbzUAWjhCUbRy8HgDCsW+NP3hk8JiPpmAMhL5gJpl63TIQNvP\nb63f8cki0DMClRLlAX45yy7wVWTLbNG+Kmq1sXAmZSv3R78STAGdVtj860Jq\nuULNKCy9Q/EC2PH1uUTSKh5RAezemnTOpRVfBvQ5c1IpZ5K+hFqBxzX1NbO+\nMwiZlZmmOnq/DTzj93ktjc4a2xH5hrL+rVnkVCSnfxAp2bZ4Jq2K7RsYFwzD\nrxL3\r\n=sFwy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC3ZXRNb1xPwbd0LXCigEUFoSfcPeiupbh2oR2VaqsuMAiEAhDdh1rT9sAB0KYBOivGpc5uZgWpjR8QsWVMgMWeeYSU="}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.1.0_1640762715523_0.42381452483781756"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "busboy", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v1.2.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.2.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-/g4fobaR3KE5HD03+7Xy3sv5dXglRDdMyVxZ12nHv2VzFATHTCcONKyxfIW5NFHhTckRdgURvnwXFOryFufKvQ==", "shasum": "9dbb7d529f022ba37d7611566ec97685049a6747", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.2.0.tgz", "fileCount": 28, "unpackedSize": 119412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzVTWCRA9TVsSAnZWagAA28kP/2auwrBSR0b8Gne4smQF\niPvD2+XFBHcUgLjdLMsW0+fAv7G+42jW3S7M+59VYS6hI2k4I35jzxVXJo/E\nYWfVxf3MDXXPSzPsELWP28IA0wMyWgQiqPxpH3qEbThHWYnKC3EA5FWQjCnt\n8LmXwmfjph5XaPit06Wh7hLY8gIBVRHaGI/MuWke+LzU0uDmpNPqpWegSE/d\n2IzDcJvMh+rXkpsR5mjGpHiGnEjqNIwUKsw7teS7z0VEe2hi42FiFa8z5wK4\nUozgjHiF0kBwkyAphb4DNgDEIq6ECWBjcEFJchVLnYphcYgcsBG/BW6CJ2Y/\nIyMCthVjqfZp8/VM+rSQ/syi9PIJJyAW+/p22XVm0AV2kG69esngul7J9yaG\n7SFSJxMtmfk2JFx3wYDTB7jCiWlqZLBIkWI/8IG5HajuAycqsfNBvboAgu1k\nr4LH0GDAGz6TJUuXCoj1dbkCOXmkEGbJ+NmK1HLmhudRnWXy+8NQjcql+oU5\nJFVsWxN0nFIEtVzw5NrmNzuRya/tXtdfn2+m3xB0ZH89G588TapoFRuj+pl9\nXqXhdNkkh4ABG4q4UeTw7YB/2ZF1H/u+oTL/mwafbnyrgL9igOCXTPQHMau7\na6iXN7JWMe3TWmFafOlQ/1iwuQdDZbaN7SYXOqZvwHTqQkcd3SLMuXH+kHGw\nMPIF\r\n=gzM1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBseT8hlmjl0+gz48gtfy89CSHOJFSyy4n+Xe9kY2r5QIgbMTFIbfx5+a1FWAZUGBvDQryLh6qrM2pZ/vUpHM2K4Q="}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.2.0_1640846550191_0.6773825016761446"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "busboy", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v1.3.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.3.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-ytF8pdwEKCNwl0K9PSwmv+yPcicy+ef+YNAw+L0FTfyBLzCWhp5V3jEfau2kb5A0JD0TkOPrdtdCKLoAHlMu1A==", "shasum": "b391ba4d7a40b3cab8d4d6b8ffdcd443b06679b3", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.3.0.tgz", "fileCount": 28, "unpackedSize": 121073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzlC8CRA9TVsSAnZWagAAtNwP/3vZaLAStaFc6LqAb57q\nw6dN9pPo5AxoUfqULao5fmyxTdlq5CLoHsmsQVgC+Swzhai+UaheuM6/E68A\nRD87BmY9o1xc4zRcT9rjpD4xDdxh+NGhHLulzWvfH/hO7hl9BwB3tVzT7szV\nimfP8wnpNLYNMnjY6J4qmRXmULa30nlhdEi491kDfZxzImaQ+begJIg5jliS\nMug23jh/hUjR5IuUHnTg/2THrCPM/veT/IuJJqqe/9UWXDuSNMNmeOt78u8C\nmudsikfNITIStMwROIq3HdVBaOBMPDGsLcAIbBDTfdi0djnrv13g3Qipe8Fw\nLDeer0MJFUO9KmFYyhX/ZbbZ+Jfm2yMsI5DoOYC5J1G2JnF9J7jkjPwhOkqj\njF1KgNZmAr3ny6ZE0ym6JHeX8/g+q6OXPHmpY86RZQkAeiDMr2mJbVm6Tyws\nmY3II69qKopKTBjR3S9KDBbCROOtYPbm9eadJWYo8t1LxUfnzKtbrxNsXrvT\nQcqaBfGTh8tteQTNwmJyMLDUQ2rZ6IvTDU9NA4NUPsBixqvW/ZgQsUA6aFBC\nWdXI792GpS3H7e0DsPq9kNYTgbGZdBAuR+bb6nFqTPDziFYJ5x78Mn1aQe8j\nfmt7WUiBTSArMtuZZJFzq1xmFQ8hqLw49YYdLrwCnE7rbfG5lK5ZZPZTPYw3\ntd6G\r\n=L5LU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1ozGcmUz2zt1kaKLbI9qidgeDWKfbaXEapEHBXAVCFwIgGMpIM8lGwdpFlRdU/2sP7Qu+JXKTxQ28vb4KDdJMt0E="}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.3.0_1640911036678_0.13981902966191417"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "busboy", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v1.4.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.4.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-TytIELfX6IPn1OClqcBz0NFE6+JT9e3iW0ZpgnEl7ffsfDxvRZGHfPaSHGbrI443nSV3GutCDWuqLB6yHY92Ew==", "shasum": "7bb6ea83e672516ab62b7c3c418b5942f88b45bb", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.4.0.tgz", "fileCount": 28, "unpackedSize": 121051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4kepCRA9TVsSAnZWagAA7soP/R+zwL9quKPgRxB2z8x+\nVeTaCdp/Id2sxiMJ0AaJMWSFsl7T4064pKYBJXM4Pp5yHLJwK787Uwt16HHT\nSKsO412dzXQdB1oFrMD++GzFaJPxEqkSFb9DnqaVmf/huz4Hsj5l+zIfwr8h\nHx3f816B8Hy5w132c2gHVwzqc6iGnOY+HY5y7II6li5+dJyK3bkHXL2AiMnV\n3q6+aiXw08KmMhZ/lAUExY5BoOf7gV85MKN4+PjtuWj/89ZOrTas1NsN2Q3X\n4V29q06OVuCYusNBcelyfQmOICWQpwnR+61O2moG8Os1kMzEoJSu7/S5bKIG\n0q9v/AefRufr8FfsNd6YjXAWDylaQoswFkLxLPDc7UYCGAzo+0ye7sh1oiT5\n7Jx2z+96X1VdKcjZ676sWWz47xJSGBE4X9gdN0I8MKFldyyxIUDWCPXSVWvH\n5SK2pVdP4xFzd/tz8ZbLsqt//emD6ZCz/R4wN/xJ9U0ztIJVbmcm7jdiM5Ap\nWt5bz6O7DHDlznKxt5T8jhybuxh3V0qamBTFGf9c6I+GGSZ8Cz51MN5vE0ZL\nEfT3JghCt8T7qmSLXIlH2bhdkY0hqsd5gkXgvShnjQNeEaBRmceq8VF8THvA\nxIlOa4dtaaIo49cT7HYN+Yu29xg04lYkaMbnTxG9MlVb0a72sLPVvm/STwWb\n2th7\r\n=2C2v\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7WD4WJhmTmjVQ/mqB64uV/roNUxvgsMKE6waN/RjBeQIhAJWKaUI38ecBCCYmg/ZNSSRj1Lm4FNN87eZW+kqU0gvd"}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.4.0_1642219432909_0.1037441964633572"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "busboy", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/v1.5.0", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.5.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-bTXFtUV/DkvfwzHD7yjAkRK5/F3RuF2Ab/qWvf5plbHZMSCea8mxOdqMj8tG1PoSNj/Qi0cb3IoRPEj6qXAGgw==", "shasum": "018a0951f082b136f06f5d5886d75300d9ad8b6e", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.5.0.tgz", "fileCount": 29, "unpackedSize": 124547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLhjTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzOg//adL3XQsl+NfXMejLRF8FrkbY3cJZvx/G6CxcxQ20wcU9BmpF\r\nyMK5feubqICRAC6VmJa3+Rq5tuFilDVso9c/1EQQVFDoTtLUr6iFrpMuHV3X\r\nDuWraSDyRHMz3x9Yljia4FrDEqevrK0Ehg017Ne2Id84ApQFYna0Q7Ucqxpp\r\nSRxpOQaPGvZ0JVV9ZxQG1Xs+pF6VpAiLB90hW3TR8BzTjsufFjM92c4Z2gpd\r\nTOIAc7SIqtygthz9v2REt7djeM8HrCLyFQV68dCFTc6aZSN+tsJxAPmUrxDY\r\n4pVV1S1yaHK3jpskzOHMLZSZpQEEMQ8d1nTaB1Xf9XO+o9eetuW4PpBTQAMA\r\ncSYjBZFetz6rTBWOlTlXWkkdhmsW8QhppoTLOVgBS6TjKEplw7HhGxRANECR\r\nImj8+FfdQtwU/sLeELG4FZOofuR4mDzgj4/f0AtV7iHgVxnMkKKWGcNjMLPZ\r\njFaN95zgvluMzXJiAw4lf2o0Aqp32PGTE7FO/QH2r301Mle5Tv8C+vdRUbVh\r\n3ftY9cZ2ZLPzru3YsK54flLeCSqE/opinDUI6RJMbZE5v6ADlrJIio37iAQS\r\no5QbSI3eBwF1enlKhnkrNxT7dfsnl4c2diAYyABbhA1h3JJTIJe0hz1Q48kU\r\nnL2juFkgDNB+v4/Xh5jz1y4nGStV2M0NRC4=\r\n=r07T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBaLnL+2X8gjiftfVtZxrQQGXBE+ukMQb0WA+DG7rLa7AiAxiMt3QFsaX2I4FSP7jRxkRheyhwZYn2HMeZEVao3bsg=="}]}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.5.0_1647188179122_0.5895999436505523"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "busboy", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "A streaming parser for HTML form data for node.js", "main": "./lib/index.js", "dependencies": {"streamsearch": "^1.1.0"}, "devDependencies": {"@mscdex/eslint-config": "^1.1.0", "eslint": "^7.32.0"}, "scripts": {"test": "node test/test.js", "lint": "eslint --cache --report-unused-disable-directives --ext=.js .eslintrc.js lib test bench", "lint:fix": "npm run lint -- --fix"}, "engines": {"node": ">=10.16.0"}, "keywords": ["uploads", "forms", "multipart", "form-data"], "licenses": [{"type": "MIT", "url": "http://github.com/mscdex/busboy/raw/master/LICENSE"}], "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "_resolved": "", "_integrity": "", "_from": "https://github.com/mscdex/busboy/tarball/master", "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "homepage": "https://github.com/mscdex/busboy#readme", "_id": "busboy@1.6.0", "_nodeVersion": "10.22.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==", "shasum": "966ea36a9502e43cdb9146962523b92f531f6893", "tarball": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz", "fileCount": 29, "unpackedSize": 124397, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8zorLkbsUyPRpOM94kUy75kIyo6Yk9QqWu8yT7A3OrgIhAKVBJEtf6NdBZcUm8SsEtuwRAmgYUIgDxQvj/uSepbYL"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXrDLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSHg/+MnfdBHRwkNk1Vrtg1wY+N+TK8w46s9G11jAXkt9cCoonEMj4\r\njVgapoSS2PwRuvt6g2FqYmUmOrLhQcyYF1E9LqWyjveeefiCRBX6fpEUvYFn\r\nvsKbcs+Tvg6t7WH8GW9x7OxbHBLcxDGQ+0RQIAkrmLOzolJR/SAyYR+AxNFm\r\nYLcCEPd+saDXjefnDHP/Rf29ybsc1HcvLv1sTbJFUl8hY31dGwCfYYVMPX8K\r\ni22j4927LvQ+dio6YxC3x844Hi+Bl3TBDL1V/Up272mkmWlui1gRd/QAZwbs\r\n1bU6nWp46Sa1ysIvQV7mzYsX/voFUwsr+HVoZA15/qrwZRi/llgrAIAa8dZj\r\n+U+Jtnefo+wWdmNW1kpZx3z1bAwyz9j7eQi77v3m0Z1T3vxBdXNiR09gaI/3\r\nF6kzOAlaxPwef8vCNtsnIcPAIcypiEVaJa9qSol1xhBC4DtcyC/RLe4Egw17\r\nn0RIabq058M0Ggy3kPSof+l5HGS6DxaojRMLCRbdu4YPBz8Lghe0/TkKDhrc\r\nnUDCc25MBKsTh4w6t8Y4BkEbeqktU0Udn2rhs+z7amloz84Q7ZzVNAZ9gU4J\r\nDq7XaOY1sFT+USHDYvAKeADLqAk+U6Ckd/usEug6kXiHvi7xbGHsWu/0aNcb\r\nONWOl9FcAN0s9tMyrzazjQxJYQ1u9M7L5SE=\r\n=Spwk\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "mscdex", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/busboy_1.6.0_1650372810913_0.380036269457366"}, "_hasShrinkwrap": false}}, "readme": "# Description\n\nA node.js module for parsing incoming HTML form data.\n\nChanges (breaking or otherwise) in v1.0.0 can be found [here](https://github.com/mscdex/busboy/issues/266).\n\n# Requirements\n\n* [node.js](http://nodejs.org/) -- v10.16.0 or newer\n\n\n# Install\n\n    npm install busboy\n\n\n# Examples\n\n* Parsing (multipart) with default options:\n\n```js\nconst http = require('http');\n\nconst busboy = require('busboy');\n\nhttp.createServer((req, res) => {\n  if (req.method === 'POST') {\n    console.log('POST request');\n    const bb = busboy({ headers: req.headers });\n    bb.on('file', (name, file, info) => {\n      const { filename, encoding, mimeType } = info;\n      console.log(\n        `File [${name}]: filename: %j, encoding: %j, mimeType: %j`,\n        filename,\n        encoding,\n        mimeType\n      );\n      file.on('data', (data) => {\n        console.log(`File [${name}] got ${data.length} bytes`);\n      }).on('close', () => {\n        console.log(`File [${name}] done`);\n      });\n    });\n    bb.on('field', (name, val, info) => {\n      console.log(`Field [${name}]: value: %j`, val);\n    });\n    bb.on('close', () => {\n      console.log('Done parsing form!');\n      res.writeHead(303, { Connection: 'close', Location: '/' });\n      res.end();\n    });\n    req.pipe(bb);\n  } else if (req.method === 'GET') {\n    res.writeHead(200, { Connection: 'close' });\n    res.end(`\n      <html>\n        <head></head>\n        <body>\n          <form method=\"POST\" enctype=\"multipart/form-data\">\n            <input type=\"file\" name=\"filefield\"><br />\n            <input type=\"text\" name=\"textfield\"><br />\n            <input type=\"submit\">\n          </form>\n        </body>\n      </html>\n    `);\n  }\n}).listen(8000, () => {\n  console.log('Listening for requests');\n});\n\n// Example output:\n//\n// Listening for requests\n//   < ... form submitted ... >\n// POST request\n// File [filefield]: filename: \"logo.jpg\", encoding: \"binary\", mime: \"image/jpeg\"\n// File [filefield] got 11912 bytes\n// Field [textfield]: value: \"testing! :-)\"\n// File [filefield] done\n// Done parsing form!\n```\n\n* Save all incoming files to disk:\n\n```js\nconst { randomFillSync } = require('crypto');\nconst fs = require('fs');\nconst http = require('http');\nconst os = require('os');\nconst path = require('path');\n\nconst busboy = require('busboy');\n\nconst random = (() => {\n  const buf = Buffer.alloc(16);\n  return () => randomFillSync(buf).toString('hex');\n})();\n\nhttp.createServer((req, res) => {\n  if (req.method === 'POST') {\n    const bb = busboy({ headers: req.headers });\n    bb.on('file', (name, file, info) => {\n      const saveTo = path.join(os.tmpdir(), `busboy-upload-${random()}`);\n      file.pipe(fs.createWriteStream(saveTo));\n    });\n    bb.on('close', () => {\n      res.writeHead(200, { 'Connection': 'close' });\n      res.end(`That's all folks!`);\n    });\n    req.pipe(bb);\n    return;\n  }\n  res.writeHead(404);\n  res.end();\n}).listen(8000, () => {\n  console.log('Listening for requests');\n});\n```\n\n\n# API\n\n## Exports\n\n`busboy` exports a single function:\n\n**( _function_ )**(< _object_ >config) - Creates and returns a new _Writable_ form parser stream.\n\n* Valid `config` properties:\n\n    * **headers** - _object_ - These are the HTTP headers of the incoming request, which are used by individual parsers.\n\n    * **highWaterMark** - _integer_ - highWaterMark to use for the parser stream. **Default:** node's _stream.Writable_ default.\n\n    * **fileHwm** - _integer_ - highWaterMark to use for individual file streams. **Default:** node's _stream.Readable_ default.\n\n    * **defCharset** - _string_ - Default character set to use when one isn't defined. **Default:** `'utf8'`.\n\n    * **defParamCharset** - _string_ - For multipart forms, the default character set to use for values of part header parameters (e.g. filename) that are not extended parameters (that contain an explicit charset). **Default:** `'latin1'`.\n\n    * **preservePath** - _boolean_ - If paths in filenames from file parts in a `'multipart/form-data'` request shall be preserved. **Default:** `false`.\n\n    * **limits** - _object_ - Various limits on incoming data. Valid properties are:\n\n        * **fieldNameSize** - _integer_ - Max field name size (in bytes). **Default:** `100`.\n\n        * **fieldSize** - _integer_ - Max field value size (in bytes). **Default:** `1048576` (1MB).\n\n        * **fields** - _integer_ - Max number of non-file fields. **Default:** `Infinity`.\n\n        * **fileSize** - _integer_ - For multipart forms, the max file size (in bytes). **Default:** `Infinity`.\n\n        * **files** - _integer_ - For multipart forms, the max number of file fields. **Default:** `Infinity`.\n\n        * **parts** - _integer_ - For multipart forms, the max number of parts (fields + files). **Default:** `Infinity`.\n\n        * **headerPairs** - _integer_ - For multipart forms, the max number of header key-value pairs to parse. **Default:** `2000` (same as node's http module).\n\nThis function can throw exceptions if there is something wrong with the values in `config`. For example, if the Content-Type in `headers` is missing entirely, is not a supported type, or is missing the boundary for `'multipart/form-data'` requests.\n\n## (Special) Parser stream events\n\n* **file**(< _string_ >name, < _Readable_ >stream, < _object_ >info) - Emitted for each new file found. `name` contains the form field name. `stream` is a _Readable_ stream containing the file's data. No transformations/conversions (e.g. base64 to raw binary) are done on the file's data. `info` contains the following properties:\n\n    * `filename` - _string_ - If supplied, this contains the file's filename. **WARNING:** You should almost _never_ use this value as-is (especially if you are using `preservePath: true` in your `config`) as it could contain malicious input. You are better off generating your own (safe) filenames, or at the very least using a hash of the filename.\n\n    * `encoding` - _string_ - The file's `'Content-Transfer-Encoding'` value.\n\n    * `mimeType` - _string_ - The file's `'Content-Type'` value.\n\n    **Note:** If you listen for this event, you should always consume the `stream` whether you care about its contents or not (you can simply do `stream.resume();` if you want to discard/skip the contents), otherwise the `'finish'`/`'close'` event will never fire on the busboy parser stream.\n    However, if you aren't accepting files, you can either simply not listen for the `'file'` event at all or set `limits.files` to `0`, and any/all files will be automatically skipped (these skipped files will still count towards any configured `limits.files` and `limits.parts` limits though).\n\n    **Note:** If a configured `limits.fileSize` limit was reached for a file, `stream` will both have a boolean property `truncated` set to `true` (best checked at the end of the stream) and emit a `'limit'` event to notify you when this happens.\n\n* **field**(< _string_ >name, < _string_ >value, < _object_ >info) - Emitted for each new non-file field found. `name` contains the form field name. `value` contains the string value of the field. `info` contains the following properties:\n\n    * `nameTruncated` - _boolean_ - Whether `name` was truncated or not (due to a configured `limits.fieldNameSize` limit)\n\n    * `valueTruncated` - _boolean_ - Whether `value` was truncated or not (due to a configured `limits.fieldSize` limit)\n\n    * `encoding` - _string_ - The field's `'Content-Transfer-Encoding'` value.\n\n    * `mimeType` - _string_ - The field's `'Content-Type'` value.\n\n* **partsLimit**() - Emitted when the configured `limits.parts` limit has been reached. No more `'file'` or `'field'` events will be emitted.\n\n* **filesLimit**() - Emitted when the configured `limits.files` limit has been reached. No more `'file'` events will be emitted.\n\n* **fieldsLimit**() - Emitted when the configured `limits.fields` limit has been reached. No more `'field'` events will be emitted.\n", "maintainers": [{"name": "mscdex", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T02:39:47.323Z", "created": "2013-05-27T05:24:42.377Z", "0.0.1": "2013-05-27T05:24:45.032Z", "0.0.2": "2013-05-27T05:59:31.085Z", "0.0.3": "2013-05-29T03:32:31.769Z", "0.0.4": "2013-09-11T03:35:51.862Z", "0.0.5": "2013-10-08T15:55:42.058Z", "0.0.6": "2013-10-24T18:04:20.170Z", "0.0.7": "2013-10-24T18:17:08.673Z", "0.0.8": "2013-10-30T02:26:37.403Z", "0.0.9": "2013-11-05T13:50:00.036Z", "0.0.10": "2013-11-06T15:58:46.826Z", "0.0.11": "2013-11-07T23:02:31.829Z", "0.0.12": "2013-11-13T02:06:45.046Z", "0.0.13": "2013-12-12T16:53:40.030Z", "0.0.14": "2014-01-11T04:45:09.023Z", "0.0.15": "2014-02-03T06:24:46.736Z", "0.1.0": "2014-02-13T16:29:36.539Z", "0.1.1": "2014-02-18T20:48:10.037Z", "0.2.0": "2014-02-23T21:41:27.803Z", "0.2.1": "2014-02-23T21:51:01.230Z", "0.2.2": "2014-02-26T20:11:15.053Z", "0.2.3": "2014-03-01T20:36:32.655Z", "0.2.4": "2014-04-15T19:49:25.961Z", "0.2.5": "2014-04-22T04:25:59.620Z", "0.2.6": "2014-05-05T15:09:48.536Z", "0.2.7": "2014-06-13T12:38:14.503Z", "0.2.8": "2014-07-15T18:24:48.853Z", "0.2.9": "2014-09-21T19:02:49.492Z", "0.2.10": "2015-09-01T00:38:20.109Z", "0.2.11": "2015-09-02T20:01:05.139Z", "0.2.12": "2015-11-15T17:01:33.189Z", "0.2.13": "2016-03-22T17:15:57.809Z", "0.2.14": "2017-01-18T18:39:45.185Z", "0.3.0": "2019-01-09T23:23:35.167Z", "0.3.1": "2019-04-06T22:14:40.605Z", "1.0.0": "2021-12-19T20:39:04.899Z", "1.1.0": "2021-12-29T07:25:15.661Z", "1.2.0": "2021-12-30T06:42:30.358Z", "1.3.0": "2021-12-31T00:37:16.850Z", "1.4.0": "2022-01-15T04:03:53.073Z", "1.5.0": "2022-03-13T16:16:19.277Z", "1.6.0": "2022-04-19T12:53:31.121Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/mscdex/busboy.git"}, "readmeFilename": "README.md", "homepage": "https://github.com/mscdex/busboy#readme", "keywords": ["uploads", "forms", "multipart", "form-data"], "bugs": {"url": "https://github.com/mscdex/busboy/issues"}, "users": {"leesei": true, "andrepcg": true, "josephdavisco": true, "zemat": true, "normysan": true, "isaacvitor": true, "greelgorke": true, "henrytseng": true, "icognivator": true, "alexandermac": true, "hzapata": true, "detj": true, "markthethomas": true, "jamescostian": true, "akiva": true, "mp2526": true, "godion": true, "sessionbean": true, "luuhoangnam": true, "program247365": true, "samuelrn": true, "kunl": true, "koulmomo": true, "rbecheras": true, "karthickt": true, "nmccready": true, "karlbateman": true, "leonning": true, "siyb": true, "softwind": true, "nex": true, "flozz": true, "wisecolt": true, "santihbc": true, "junjiansyu": true, "spiros.politis": true, "shadowlong": true, "algonzo": true, "lgh06": true, "silva23": true, "kevinrwing": true, "complexcarb": true, "ghkddbguse": true, "cognivator": true, "kevteljeur": true, "syberag": true, "nickrobes": true, "wangnan0610": true, "floby": true, "ymk": true, "bigglesatlarge": true, "jmsherry": true, "kikna": true, "landy2014": true, "iuykza": true, "xngiser": true, "tomchao": true, "artmadiar": true, "kodekracker": true, "zhanghaili": true, "bigbird92": true, "jmkim9": true, "npmrud5g": true, "josemarjobs": true, "devinlin": true, "leonel-ai": true, "adriasb": true, "sak360": true, "maemichi-monosense": true, "nardhar": true, "shanewholloway": true, "schacker": true, "zuojiang": true, "grabantot": true, "mucahitnezir": true, "mattbodman": true, "losymear": true, "jhq": true, "jiuomhlom": true, "memoramirez": true, "netoperatorwibby": true, "egobrightan": true, "nilz3ro": true, "ashish.npm": true}}