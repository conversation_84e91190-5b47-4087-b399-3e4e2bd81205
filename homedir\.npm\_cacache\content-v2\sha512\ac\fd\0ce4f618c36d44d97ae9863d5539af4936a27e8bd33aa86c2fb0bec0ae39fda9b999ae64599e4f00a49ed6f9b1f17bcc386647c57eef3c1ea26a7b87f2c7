{"_id": "tapable", "_rev": "79-0dc365ec12702bfbfaab48f1c942a453", "name": "tapable", "dist-tags": {"beta": "2.0.0-beta.11", "latest": "2.2.2"}, "versions": {"0.1.0": {"name": "tapable", "version": "0.1.0", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "4b580abd8e4bfa8ff3af6da48a073d7b4f0b1cb8", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.0.tgz", "integrity": "sha512-8S76A7VSFdi/roqarj8CoYGXPRDK7JwxYtmLa0C5Z3pg0DuNMFMsdd6gncI3buDY4bsGjPYT+rIGnuWLy5o+2A==", "signatures": [{"sig": "MEYCIQC03r1KGzgffXpjLq6jAtTpVEPtUf29Pm+c/8dN3XFH/AIhAKw9zO9YcJlKdXOlCQkxOgNW2ThONOx3ztL2dUmLGKJ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.1.65", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.1": {"name": "tapable", "version": "0.1.1", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "b946d834c832ec85693f9f30bfdbd1dd1ddc9123", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.1.tgz", "integrity": "sha512-CzH0YnoYWmKhD87QjAa+BzI62eqESePM6fD57Qmq683aHtqO38FWROno/9Rsw8C5WmGoqbmPc3/0UagARungKQ==", "signatures": [{"sig": "MEUCIQDsIqc1yHRS0s8RAIAxLLzGLZr47bsxFGmnTcin2XqX/AIgLs2vdoPpswOFOTUJBvZwuhM8ds9s0siZdGau2yFGu0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.1.65", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.2": {"name": "tapable", "version": "0.1.2", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "61bf483a22fcb844783c0e69b22df59382f61ebd", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.2.tgz", "integrity": "sha512-Qe8CR00Er+4hzzvBqpPExeqPmPOL1C/m36EAmNdgh1orTWIKue/5X/clKF0G/AXxXdcsQoeZRiXZgRDRN/Y3aQ==", "signatures": [{"sig": "MEUCIG1yYe98tVeCqrXmVFlJSGx+t20yCq0mhck5tkWgfaiNAiEA1UpKp+hHx4/6UkqBWerGkGtH1r6TdECib1gU6AjhSxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.1.61", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.3": {"name": "tapable", "version": "0.1.3", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "ac6a31c4016e9a05824bfa156ee76e1b3c8da8f2", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.3.tgz", "integrity": "sha512-miixjUuXpWSywHb3RSTl9FodxYQStKxsTMekR5785pefApl1LHeEKlDCIQ+ZjjGT5mlAAI1amkaTK1+Sx27aqg==", "signatures": [{"sig": "MEYCIQDcuqN8/pUDNk/inDMq2jM2yXPYmDDxh4hM/NH3m362tgIhAOvhL7qKZMcytf9INwVLkvW6rfw0/cbn8BpFxw3HNcDx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.3.11", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.4": {"name": "tapable", "version": "0.1.4", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.4", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "2482b44b21136ba6464ead9828677da4d2c163f6", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.4.tgz", "integrity": "sha512-xvTic/hBasFHg6SmKNg7bjE175QcBS3D5jp2wOY8HJ7frVv9UnGIxR5fX9V9TuldRQIs69Foq97Z7e88CizB3Q==", "signatures": [{"sig": "MEYCIQC5f2bSWB/TxyjSrjCPkTjGs8fv/bRN4L/9wYOleb1SIAIhALpfZID2eu40Dv5A+IHhLYL3P1lP6x03pjpEQ8GUd9Ai", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.3.17", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.5": {"name": "tapable", "version": "0.1.5", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.5", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "e232665c6eb496c590e4f53b684d8b62ea79960d", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.5.tgz", "integrity": "sha512-QRlh5UYGJk1a6D1zixb3DnWTbj9I53fuHfw87nbfRoR4h/OcKJV0izBVV6o7onUw/IVKQoulscwwgkjr2H3QWw==", "signatures": [{"sig": "MEYCIQCObG+y6CTkjPBsnyufthrYZSWylRsctweHHrfVpmaXswIhAMxD8qf4V2MDOpygGECeocKHWRsaCJW7Dwq+3ASOZaxy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.3.21", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.6": {"name": "tapable", "version": "0.1.6", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.6", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "a9e959cb7e57f019cd8262f88bba32c7ac251b77", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.6.tgz", "integrity": "sha512-2cgP8BLy0F8MFHEnG6L6/Z+nJBXhw466ysyerXcpLX9bImo8CLhvattPbWzOQuQzlqtfTv4HVGBThH7Hkd1bSg==", "signatures": [{"sig": "MEQCIHdw99fL7M1IdkUf/88QrjHrukG1RvL3Z45GSsqKU2f6AiABmHrpgg1/37lLI00NOBKeiVLRLCQQGlvXsf4FAxMs1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.4.3", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.7": {"name": "tapable", "version": "0.1.7", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.7", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "4ae77c1a6402f58ae0bbc7b99d8000a0ffaf3ef8", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.7.tgz", "integrity": "sha512-Y4JYGQRgj9vdhFaXlphEPEMBbNIDuAFP1h71nopHp1iIFcj2kkV3pD3+CboVloG+/aeo2dpX1tWwsxXurKOs/w==", "signatures": [{"sig": "MEUCIBRGpiwu7jxPF98Bjv8k3sXVjuW8smt16xyawhELkvSaAiEA7B+z8VglJsxQrbZrSkQl3bznebcEJUlmklh59bV1jh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "engines": {"node": ">=0.6"}, "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.4.3", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.8": {"name": "tapable", "version": "0.1.8", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.8", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "a943314bc86ac87602c93fbc8ac609dcb19c199e", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.8.tgz", "integrity": "sha512-t4ALe3ihp44dE4w25Ma4h0cDQ5ga0yG/MJvrqnenOEuA6+KNZ3WIC6Uf48GNQBqXG9LZSD5BD+oRJ+90mbzcow==", "signatures": [{"sig": "MEUCIQDXvA9bG5koHLCKk5C7jae1Bi76r6T/+hCIhHhj3Xhv1QIgIv/A+R6ov1V+PvbzAuUd9RYXOBLOU4sILkUMWWXixTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "a943314bc86ac87602c93fbc8ac609dcb19c199e", "engines": {"node": ">=0.6"}, "gitHead": "18ce8ffafc024daa804e57afcac2e3b65202d0dd", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "1.4.16", "description": "Just a little module for plugins.", "directories": {}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}}, "0.1.9": {"name": "tapable", "version": "0.1.9", "author": {"name": "<PERSON> @sokra"}, "_id": "tapable@0.1.9", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "dist": {"shasum": "1003d9499c344ba238cb32329c8d2c8babc92e54", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.9.tgz", "integrity": "sha512-OduQlGlDou+WFwcYcx37n4a3Y/BnrjTJornHrt9ZHagSW4sYYN34JWyjnfmnpPDfT3so8ypJdAzeD6oG7wUS8w==", "signatures": [{"sig": "MEQCIHXtY2Ill8jjSfQs+6W4BeHk1Pw2/iYnuzH3gmRYkmfFAiAVds0ZkmHDiEAIBZIO+AOUDWRqbU4l8KIG/VHHB/h7bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "1003d9499c344ba238cb32329c8d2c8babc92e54", "engines": {"node": ">=0.6"}, "gitHead": "19fedb828e6195d10e2af9e133b54613ff413273", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "_npmVersion": "2.7.4", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}}, "0.1.10": {"name": "tapable", "version": "0.1.10", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.1.10", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "29c35707c2b70e50d07482b5d202e8ed446dafd4", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.1.10.tgz", "integrity": "sha512-jX8Et4hHg57mug1/079yitEKWGB3LCwoxByLsNim89LABq8NqgiX+6iYVOsq0vX8uJHkU+DZ5fnq95f800bEsQ==", "signatures": [{"sig": "MEQCIC4T2x4TOaGS5kDZ6nXHHLVYl36E2RGscY4b5iBNjcIkAiBGL6yOOf0txA8qO6vdYinggtMji+dVWS9e/UJ0uoA3+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "29c35707c2b70e50d07482b5d202e8ed446dafd4", "engines": {"node": ">=0.6"}, "gitHead": "32371f464d721c81f803f7b9440a4c3ddf1dd2bb", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}}, "0.2.1": {"name": "tapable", "version": "0.2.1", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "b5755d8c561b66975791a1cfa5774b91f4fc5fef", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.1.tgz", "integrity": "sha512-ZC5U6wPr5xGZnnI6jPZR1gxdfxvG0FQagYjth1adLctbb4/gtBmlw+3eahCblN9HRBzBXSwSGwQ1AudGBpIo8g==", "signatures": [{"sig": "MEUCIQCIxP1IXN7bZShYn5qXuH1VepWI/xj7jJhZta7FQQSdAAIgKvgvuEiNBBg0Ak/bpnWIcAo2YlrGwJ33CsHNiIcXe/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "b5755d8c561b66975791a1cfa5774b91f4fc5fef", "engines": {"node": ">=0.6"}, "gitHead": "4a87317ec1225c7b63ecc9f3343afd3d4b19d841", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}}, "0.2.2": {"name": "tapable", "version": "0.2.2", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "fd2f238db9b01ee026044433e274ef2f3dc42742", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.2.tgz", "integrity": "sha512-T7efIUkBGMuztdxv6LQ7TRPu3spiqdF/Nmzbkj6Yr1aVV5KrgmileKIr2q5Xk+3m+d9O+U8oXxHdvBu/Pamh2Q==", "signatures": [{"sig": "MEQCIH6ylwa4HZFR56Pe5+5pbEozC4rgKqCR5FqFZuy0zVvjAiBKd18os62Vx9rCo4aiCnVIBYPk9xQyqOCEw0k/yliXuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "fd2f238db9b01ee026044433e274ef2f3dc42742", "engines": {"node": ">=0.6"}, "gitHead": "06b8aae027e81d42dd94b652c0ae8d923d00ee1b", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}}, "0.2.3": {"name": "tapable", "version": "0.2.3", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "c84cc96699e2c177d6d928dc6476d93115bc0507", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.3.tgz", "integrity": "sha512-VGNE4SVhT9coDaDsUL1qu/CxZ+X1bjuqDU/bqXEtGYIn/FvRm4Ae3V6pCXRrXUFjGZ8ldZ+GK42EiK1jEXayJw==", "signatures": [{"sig": "MEQCIA3WLtXqj9CxcMEojsz6p4E2hSE1+9VHnOx4es9pHrX2AiAmuW6AN9EvyQjUB11rEirRQDZ1sY+qMooEMtSScMMeHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "c84cc96699e2c177d6d928dc6476d93115bc0507", "engines": {"node": ">=0.6"}, "gitHead": "e75cf5bb51c383158b0a377eff9284b94e0e5169", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "3.3.3", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}}, "0.2.4": {"name": "tapable", "version": "0.2.4", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.4", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "a7814605089d4ba896c33c7e3566e13dcd194aa5", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.4.tgz", "integrity": "sha512-C4ZWbMZw4ZTumxBBDmHpppGNL7ZFfwQ+wNDAa/AsmaWt19u+FnkFCjJ5bPv5irLaVKOvm4X1j9xKFjIwXshGUQ==", "signatures": [{"sig": "MEQCIAe7VorRACrXPgkh9rmkCF3WO0BB5PuDEQgIBb4KLxLUAiAo4DJ5D49M21LPCMJ6X/o9UbiQMFbdEUDr2mo/k/9IhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "a7814605089d4ba896c33c7e3566e13dcd194aa5", "engines": {"node": ">=0.6"}, "gitHead": "88ec6f4f23b2efb95488f4ad1cbd1df09b688fdf", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "3.3.3", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "5.4.1", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}}, "0.2.5": {"name": "tapable", "version": "0.2.5", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.5", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "1ff6ce7ade58e734ca9bfe36ba342304b377a4d0", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.5.tgz", "integrity": "sha512-3GpqtzLYoGdFD4QRzA+QzDP3zZBVqVQaJ85tIVIlHJbSu1XjSDG2Pb00RbZUPKiKzVTHRFJLZfxa9cDVSb0oGA==", "signatures": [{"sig": "MEUCIQDKSgZtJb0Yqth1A8X/88ev6W+P7NjoS0b/M0KqWgd1IgIgPwCPdESIVAMT76ldAca7CLRf8KEh/wzGQiZ1wYPn6gI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "_shasum": "1ff6ce7ade58e734ca9bfe36ba342304b377a4d0", "engines": {"node": ">=0.6"}, "gitHead": "b8a78d5b70e1c75bd2dcff355c7dda3cbfbf37c6", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-0.2.5.tgz_1480971694096_0.4875433600973338", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.6": {"name": "tapable", "version": "0.2.6", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.6", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "206be8e188860b514425375e6f1ae89bfb01fd8d", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.6.tgz", "integrity": "sha512-hRjOGCWR8mjc40XPPvG51OMy+myPWcK8c3GxkmjTMxmy7sN05uFFUvKZW+nUB69vAIrUb7tH9j09GMSqAr4VWQ==", "signatures": [{"sig": "MEUCIQCGeTST2UIUqDkSmtPkT3ftxZgB3TMaX2V+0HUSNyhr9QIgXJiRgSO2UPP0rHD4r+fKhYIk38/0Fav1CzyLS7Nl99M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "files": ["lib"], "_shasum": "206be8e188860b514425375e6f1ae89bfb01fd8d", "engines": {"node": ">=0.6"}, "gitHead": "5bcfb8bfb6e6bdf3a6ec3b891d7dd81f758ba7f6", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "6.9.1", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-0.2.6.tgz_1484129131595_0.5442721091676503", "host": "packages-18-east.internal.npmjs.com"}}, "0.2.7": {"name": "tapable", "version": "0.2.7", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.7", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "e46c0daacbb2b8a98b9b0cea0f4052105817ed5c", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.7.tgz", "integrity": "sha512-lbYtj3puAWZXt1QA4ytBdqW9HHF6MYzN2KK82uaMvcZvacA5tVaCgt7KSk9ntlcm7BaeGxhZWk7FvjdabJ0jUw==", "signatures": [{"sig": "MEYCIQChSw2dZKZ0TZ6CDbMKiQSi4LGTxP1YKqEAAOKL07MxzgIhALOPGHTB45sqlr/E67q0GhY7x+2YRszgOizlt915jtBz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "files": ["lib"], "_shasum": "e46c0daacbb2b8a98b9b0cea0f4052105817ed5c", "engines": {"node": ">=0.6"}, "gitHead": "48d7ef28de3ae77065b04065fdf08f8cf28eb8bf", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-0.2.7.tgz_1500529250387_0.9573970471974462", "host": "s3://npm-registry-packages"}}, "0.2.8": {"name": "tapable", "version": "0.2.8", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.8", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "99372a5c999bf2df160afc0d74bed4f47948cd22", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.8.tgz", "integrity": "sha512-e31IfP+udplfBC6xCNq6J4gR9mZLpPTx30O+1jU3L32qNwxskVbngvasWSnfRzZo1bLaHJURjl5f/kJPEVsCYg==", "signatures": [{"sig": "MEQCICvhqIkZ5D+o3ICZhKYylTH0TWpntGSlEf9dI4EqgOc4AiAHiiS7Syp2IJac+4F7Zbvy8SHD14/prPOQsFl0gj7+VQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/Tapable.js", "_from": ".", "files": ["lib"], "_shasum": "99372a5c999bf2df160afc0d74bed4f47948cd22", "engines": {"node": ">=0.6"}, "gitHead": "004df5643c1ac002a16de3ae8962330e1df3599d", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-0.2.8.tgz_1501661484636_0.9703884306363761", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.0": {"name": "tapable", "version": "1.0.0-beta.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0-beta.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "214faaa5c38faf34089da57206ff04d39a43302d", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0-beta.0.tgz", "integrity": "sha512-ZalL32yXslKrn1dnN2Wdmm2Zf8PiQSCSx2UOhnOQHXPCMULGqBMBxxWwH7KzXPacMMHEHzPzSY477yo7x6cMmg==", "signatures": [{"sig": "MEQCIAmpiqSnTkW/IyjBl5NHSqDEMrF5ReRfTQ5xHMCkUYS/AiA3We5NTvAQVyY7XYp5+vlfN+0yDXAgR6Z2F1junmr4Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/Tapable.js", "files": ["lib"], "engines": {"node": ">=4.3"}, "gitHead": "acb6b2b9d045d481786ef7b8fbd55929941c0da9", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.5.0", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-1.0.0-beta.0.tgz_1508164170462_0.8744393973611295", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.1": {"name": "tapable", "version": "1.0.0-beta.1", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0-beta.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "393ba614d35909b6ad486102a2f0d437f5f855bc", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0-beta.1.tgz", "integrity": "sha512-5elU9c22QtekR3F4D0kcYqBimZTllc3yZt6MKlHmKeswEl0MajqtF/v1M8YnJoDehTup6dDwMUAG4ZIclo6tjQ==", "signatures": [{"sig": "MEYCIQD/TeqijqR8tb+GC/ZV8FGk3z+HbmwaEMv6QwT78+a/FwIhAOCvnjoGsy5ukYEHhwe919FqrVZICHT+SKAZm6A8aIGl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/Tapable.js", "files": ["lib"], "engines": {"node": ">=4.3"}, "gitHead": "fadf027a001e8c888fc4a6cd5f23e25c9e0f568f", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.5.0", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-1.0.0-beta.1.tgz_1508250227439_0.4306126427836716", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.2": {"name": "tapable", "version": "1.0.0-beta.2", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0-beta.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "7510992f5bff47dfad97b58bfe14938391831c28", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0-beta.2.tgz", "integrity": "sha512-Y/M+rWtoXnquKhy+02hQtEuTxCHlFBv7ZkBcjApr9D/w8MNazO1D88cSvLgbzY5LoIsmwpMZAHiXkvgBi0hLGA==", "signatures": [{"sig": "MEYCIQCad/PVB3mBrNQethbRCqrnh0qJ6OKhKs08HjKIeJbk2QIhAI/7AMoTrQmXYM9VSA49jW2fW6XU5nmQzBDq/1m+TxUD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=4.3"}, "gitHead": "42b520760e138c23e7808881cb4322557e878307", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-1.0.0-beta.2.tgz_1511795881424_0.9473249402362853", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.3": {"name": "tapable", "version": "1.0.0-beta.3", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0-beta.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "1f56bb6fec924ac9570cd8f6ff788e5bcf4808f1", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0-beta.3.tgz", "integrity": "sha512-X1FQM64UScMElXJ4+x+F8vLfMKkVAfrQ6X6b7kMcSXLSW2q1wyn0i5unTsPZmIYX/379QlxqO+AF7TlUizSOYA==", "signatures": [{"sig": "MEYCIQDz/FMzYJ/cFXIF2zfeZ/3phQWAtLZ8NGGAHdnV51tlRAIhAJg0B1LNnIbZs4qukvvrP2O+OaUPLRZTX/QbmocyrqNj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=4.3"}, "gitHead": "a42da48cc61143f579764e79dc6d1cc1de49e9d0", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-1.0.0-beta.3.tgz_1511796842192_0.35019846027716994", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.4": {"name": "tapable", "version": "1.0.0-beta.4", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0-beta.4", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "0b32094cbb6eb217f5dd049fc27c3a7e99705b9e", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0-beta.4.tgz", "integrity": "sha512-3yD1lGFQgLeLgp2Hjue7afrviSiXDMBNw495ME25is+RJywZzG0Fl/IbIyD+RER+QUSfhrjZdhnpkRLmSLOYVw==", "signatures": [{"sig": "MEQCIBZmsNaAHj+7E9qy7kiHMUo1gkA6XfuGQgdeUrx8ACiSAiB2cU7CifIy+RhvvuDJQqsfh7YB9oh/3RS57gHJjzSDOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=6"}, "gitHead": "855dfcd3845a3351b0af85becc6aa9942c528180", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-1.0.0-beta.4.tgz_1511967565293_0.9066612883470953", "host": "s3://npm-registry-packages"}}, "1.0.0-beta.5": {"name": "tapable", "version": "1.0.0-beta.5", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0-beta.5", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "9bc844b856487e03345b7d3361288aefd97f8303", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0-beta.5.tgz", "integrity": "sha512-TRKt8j59hRVoYaoSB5IFCbfNEcLzDaVdKrOIPlC1AJAIZTmZZTbHDXGsqiUb9IjeRAYGeQtc617oEzlsnpUi3w==", "signatures": [{"sig": "MEUCIQCEWCbX/BATu8ioEl7DAN3iueBY3XsTxgpUlNgiPKKNiAIgPk+W96t3t6BjBLXtvoabaW49maCgXKVi/txdLZIQgIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=6"}, "gitHead": "6b91ca9e94cf032420a57a2d0b82b627462b5c1c", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable-1.0.0-beta.5.tgz_1513081769509_0.8237415389157832", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "tapable", "version": "1.0.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.0.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "cbb639d9002eed9c6b5975eb20598d7936f1f9f2", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.0.0.tgz", "fileCount": 33, "integrity": "sha512-dQRhbNQkRnaqauC7WqSJ21EEksgT0fYZX2lqXzGkpo8JNig9zGZTYoMGvyI2nWmXlE2VSVXVDu7wLVGu/mQEsg==", "signatures": [{"sig": "MEQCIC0sAI4XiOuYzV1OsgP7B+Gxyi3l7DbJU+s8fOOFPdoJAiBX4A7au1bfd5iIgRvFAkM6q/HeYh1ZBYBq3e1gXZVBxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241760}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=6"}, "gitHead": "f2718e63bfede29cbc81cda3ee79a6933ec5af8d", "scripts": {"test": "jest", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_1.0.0_1519286232237_0.7789554413074959", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "tapable", "version": "1.1.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.1.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "0d076a172e3d9ba088fd2272b2668fb8d194b78c", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.1.0.tgz", "fileCount": 33, "integrity": "sha512-IlqtmLVaZA2qab8epUXbVWRn3aB1imbDMJtjB3nu4X0NqPkcY/JH9ZtCBWKHWPxs8Svi9tyo8w2dBoi07qZbBA==", "signatures": [{"sig": "MEUCIB6NlynxFm6u0r0YfPm+jwyiciUGuv+bNNjsLKnY1B6tAiEAwt/sYZN84sW2AElQhwR9q/unm578tw3QAJOAvbgukaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 239470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmPATCRA9TVsSAnZWagAAklYP/39OgTP1JPCpxViln7vO\nygWRynzJ/pb3DrVnydNqlDXkmijM2LolO4yUpqgT6AaG48alkKBrOqh6qTqx\nhBqSW37umIBPpwoDWM/cVtkdyNc4lmdzjOFqpUp9P/wHPS5fGomYrfVWEGWI\nxwOD2geEDqTlnXpYQTvIpJOR8lzmOmJghnc8CAbl7au+AicWZ8OK9wPRpLkA\nfcddQ2qkcNWaMDvU3v/AkyfshTjJJ+5dss12houHhTxPIJqbuhYiBPb6hFUy\nE9dX+e6AbIuXPbh5+dWKo9xIGjXrWv6n61V7pUDBNpJ4BPMvC7AvvAFgt2UP\nMybX694Z4dnz4M06GV06PENVMkeM3B6KeBE6FtN6sO+nsDh2XB0ebTr61kXw\ni9Ih/hBF8p42MImS/DeDILi8OWj2R9W3Y+UX+J2GTd+6CWWQyhMHHsVztGa0\n90M3N5H3JhheW3Fon7IxjF1+bEwL+wAQperb+I2oze4FaH0unyfG+bkq0yX+\nTC5FIMSVTP7MDy/ErGeyt9tXxkqyNjmbXmrq1owvnHiuKsTUT8oY/pe3ql19\nn8uPUJkLHejeTv5euWgkHK2bIoweiaz3qFhmoWMM0hAEvji4dSUZDFfCs0DJ\nYrdg0m06mFAjbgkjoRyUixdIi8G3K3wvt9CWahJ+uJYdQLQd76invJ1GcrZj\nKo8I\r\n=8Jsr\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "4b736473267a7634696697c3eab26774ab018286", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.11.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_1.1.0_1536749586290_0.13956392119105243", "host": "s3://npm-registry-packages"}}, "0.2.9": {"name": "tapable", "version": "0.2.9", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@0.2.9", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "af2d8bbc9b04f74ee17af2b4d9048f807acd18a8", "tarball": "https://registry.npmjs.org/tapable/-/tapable-0.2.9.tgz", "fileCount": 4, "integrity": "sha512-2wsvQ+4GwBvLPLWsNfLCDYGsW6xb7aeC6utq2Qh0PFwgEy7K7dsma9Jsmb2zSQj7GvYAyUGSntLtsv++GmgL1A==", "signatures": [{"sig": "MEQCIFsPB2EiBJvnXh/b6FPhnWI/lKOELircz/gXHDuXOju7AiBDdWT3XhNcP8kFUb1GCPtFVubXXhPSZmNaiXIyOcRJBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/Qn6CRA9TVsSAnZWagAAQ4wP/1kWwq6T8vUy390ja3yU\nir/LO36OsgpS0v3+JiswVBgCAYItL94d/7FK3B9AJ8msG9IiRz0JGdo+Gphf\nnoPR0B+w3MfG2Qpd/bg8sGv4ShCFj6DNM0jzNFoSWGpi/Xul8Q1f+PKVuglZ\n/tO4U84Q80VBLHRw8xe2ZlwnSz6+fu+afX8xBp6P6nnQ+pLHoCVBoITt8TuO\nvAAdOcoIIozANb12kCLY/LB4i0DtwbPtsUg0vOAkfcA89Ly0BC8Kdqj7Sl+f\nd9LWRi/F405pMtk8gvRpj8I4Z7EzOJJug7lhyrbtXizcnLK6xIlSNiGPOW7k\nflZdZWt40hwZVzz924CUcE/T9NGP9cWp3Dvn5PDPqKMnrFaqgVZUtGtg3rrC\nqtyABs6rNGGbJEG/r3hbqo8WyeWCjOT35bUfIcNRalJrhRFG4i5VubPQWRin\nxojQsPrdCnX7eACqz/MewfgTbe5pgT8dk0nXWoMzvdBtGOIKQFtQhoKolgKN\nAUuU9JODQR+FhBW/gxeBaiIAiBgm4VVM1iB+IKg+xu/67cSUkxpihoa6Z3kR\neXMgAW6K5JHQEuZrJif9sndxe20SyEBFRYc5nVQYuWc02CdUgLoiMHdby6gY\nO6kTA/oee3kasen7cSKIxpEPisIvNyX3XEC9/tyeIPoq26AGIE7dIF1FwYpw\nFbBX\r\n=nz4V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/Tapable.js", "engines": {"node": ">=0.6"}, "gitHead": "b5eed4c2ce20d9394746cb9a74660e75df490d28", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_0.2.9_1543309817875_0.8941063194920646", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "tapable", "version": "1.1.1", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.1.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "4d297923c5a72a42360de2ab52dadfaaec00018e", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.1.1.tgz", "fileCount": 33, "integrity": "sha512-9I2ydhj8Z9veORCw5PRm4u9uebCn0mcCa6scWoNcbZ6dAtoo2618u9UUzxgmsCOreJpqDDuv61LvwofW7hLcBA==", "signatures": [{"sig": "MEUCIQC6/ZD6+/e2BsjXc1h9gIaei/fLSQMZ2ltDkpK7VTouBAIgcGTilPKj33TPqwGI6iV/TGQf5cVprzgPDG+Sg4Vce80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/QogCRA9TVsSAnZWagAA1ZIP/0O4KkoiS59Jg3+HPb/+\nHDDMifGCOuGoS7HbmVV2ZpN+Fla3lYtjU/GW2vr3nixImzhfh4fTp3oFG0TC\neiwAsWN8Ypcs3eFH9hB2r89cbC8t2d+u9oioiPbfm0p9gHjK9/8GMIZFSw0I\nmRmrd7uJF51iTfDYRUm957lFyy05P66uxVe0l7BkS/L/zx5l20cIegqb5kUo\nVhnK7re5BCvF8sYF7+RRs+m2f7n/vPTk23WM3RbtHJVN+liHR4kBvT14JE+Y\nw0yLkWOvcn6xYWlc+2ZEnHGkxM7uqg0pejp0cswkbdE31KAwkm14QCvOzHuX\n8vFb4xcBDl20LwUvXrJWT9zSYjx9BuMll/Zfc8D1HUmjBbDXD3O1LGwA0vYp\n3kZSuOumax+6fKeTuIOe9GvQtN28XcGrekva5Xj8wQH4vED6muVGe0HcpdJ5\nsvVs6zzWQW5SRappvCoyozlv7G2tNj7MmwV8/vDFuseJhJrblBkVpu8K7swb\n0V44jS9I4+t7l8NxUQUgOar1ZBpayRBEhvuyo/hXz7CeFRi/uqLtT7fDe60l\nk7wa3JQ//DG6XhRtDK44uXngxO1lo8gpCW+TnwTnKFdAVp+V3vssZEEiBK3L\nJs9NIE8WMreH6MMG1zgg7+jJPelkbv2WHWxOMfx6nB/+2EhPz4XRLEOUVSPL\nJdcG\r\n=kjLZ\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "de0704c4a068e726633313ec41078d68cf55264b", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_1.1.1_1543309855570_0.5950404723215823", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.0": {"name": "tapable", "version": "2.0.0-beta.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "46a59f80ca5bd8b3ba13a668afc87f160c32cc15", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.0.tgz", "fileCount": 33, "integrity": "sha512-Fz1l/44hJ5Qy/JlSxkKqmJNPG89zZ+UGZAqQiyMcEwy2Fi9BC+NZWGk/YkGPATbTLH0k7DZvNTTaGncDr8Z8Lw==", "signatures": [{"sig": "MEYCIQCgqKHrJINyORFLBdMhn5xK9BgBc75wM9mKrojmwAO9SAIhAJZonvrmyzk/coP9ngF3C6x68BFdIJP1WiBPOK0qmeI6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 237980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/QtKCRA9TVsSAnZWagAAEzYP/2AuMx+tD69tXu4wXSQh\n5GKZVjNFuZ2+vQr5PMoaNRJfoOzDfUhmlg9o1Cq7K2PZyLHvl4bX7jyhoJ8F\nj9hvpeitWSDeppd+Qo3MRi7EYV/ZfW5M3IRCFOWS7PW8xqIduhqkNQDoH/6s\nqPy9+i6NTDex3YNtROlcEOXwFPKYtJkncIwRBUZZHVwVdmR3yyKXrrQF2MB7\n7s43KWkiJ7MokblYtH5+L1sYhXRqa+s46yg0zBIqhTycD7Yh7QaF7HEu76M5\n16LtmRChv8UXFtrQAytEzlyVMSkDt3NCJz9tnrjTg/ke8KF5uhiNdVMz0f/6\nAbeGZNqYp/Gy9N+jKFu8aERYpAQkg59dIl6/tQg8m/w8Y+KU63s0VotC4zgE\noQ4E1IHHEhbE/rLjZbqKBxoEUzpqTT3dW0Pg4EWljDWtgOgtOLPAUF1/TXXZ\nhD8391o9cIUbvxbF9yx3ufNuKmqFxWmoF75eTFRVgPgFglnCwha15YR+4MlU\nXWPdGmspwpybBgiaY9cwhY47dtM0LqM9evQ6FjT1iXa/1FYiwqVmYl5c5lyM\nSgrRvwv7AjFFe8ss4L+6yKiOWpVeHkwRaNBG5pMZsYj7uo6spXekUHlOCtc9\nQR60cd54r7zFpRtPSvFLTrbhIxA95f5AS3Aigtp9S4ps+RPNUzhHuzbk0EA4\nEcUb\r\n=9AOg\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "c9d5458cd5199b0980d67e0596ef4e71c9e750c5", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.0_1543310153587_0.6394106684070264", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "tapable", "version": "2.0.0-beta.1", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "d8cc07eb4d215a33c957284ff79120e2f54d20aa", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.1.tgz", "fileCount": 32, "integrity": "sha512-uGJcxCyqVLqrpb+oq4JjiFVujQ4knh7r22Unte+pcK891j6ybyOnmMRur/r62a0CXPBQUnn5aM7REjHFMwdL4Q==", "signatures": [{"sig": "MEUCIQCKtI2KI8VFYV0fhviIS0G+1wRZbzqa4DUbz+TDH183kAIgEEDLUXWDVKx29DgZDBSeG7FB0iah8vNY1eRcEXNGkXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFP+MCRA9TVsSAnZWagAAse8P/2eZ6sMZzA92kGXDPeuX\n/kk+wRuV2rdZnx9q4NCpHLG6efAl31HkX1KBGoNZZJi/2ZvHeazVzEUTQgh2\nvAAbmFsl+L+t7Z8JGHQ/Oq4dsh2AfEhXv9uzod21gtzyY8t7G7Ye9gZ8HiFe\nQDR2pkxLzpw61Va/dy2iPYjtGPo0/N3/doA0Ocze6KsQwjnDA2pMfegZ9CRJ\n4Jd5oJUm4GKPj3oz38uMnOsmVGfkhcqtWWGP2O1R8pWsv57xQ1fVK4RxMdFX\n+OEt4jUbX7U0oPHRYycb1meTuXo1TjDHRnK4IxGQ4vYK8saguIhuD6udZcPR\ngYf814zVeTmEtP+gylGhhOtZOT1RGpTdPPPHOVQ5pWEprsMh7/6Zf44EWgyM\nPYUZrn++CPq2thM7mwyw982hmB7lir8SRv+5a4jaMmlBegzzXPYgrnn+BJIh\nRzpA2ZyGTwnxoOOlvNU3JW1DXyo7K54OXTNPpv1ti64Z6g0649I0WpbL/Gdu\nRx0iSQWi8yKTKw/TkfJYeH7uH5KfoxNNVysMRSAhfJjsH+z7kISQ5T4IDbkv\nldzT6konFfTy4xlFy1Oy916L+KubNbudIEslfDcg3YXMGqQNeI9MN+eF//aU\nGbPCKAXnEHkobqQ/4uUGvqUKTSSm3opvp77z/Vxpkmgcl3KnF1MlTN7i9SgX\nzSXO\r\n=utv9\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "4a133b8b80a3cd81ce7f5e9b131d9d867f24607e", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "8.11.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.1_1544880011706_0.27553903246175504", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "tapable", "version": "2.0.0-beta.2", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "a12a84c61770ab8536abf8ae3efee108390ab3a2", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.2.tgz", "fileCount": 32, "integrity": "sha512-O0OqFiXr9az2c+a0SY0DBH283vBftVCVR8aa6gmV9nBv47y1Qq/akJ0AlxfTWXt9R6ZNebo6Bt6eQKJ5WKu1rA==", "signatures": [{"sig": "MEUCIQCPdgB6qk3aG+jZXx0VJZB3Gz0GG9Jv03zOLDrA7wXL8wIgEwZ5lxQx+2nNqMj8GBQ7ksgDlvm4p+w1VE0YEEE21po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHKhaCRA9TVsSAnZWagAAhx4P/iqAlcG9Qb0oHt4t+unp\nTNizEPFS33mt9axnseJ1YroZWIIm3DKExt90ZrCeojWlL/Kp5vq2Gh7u6YQb\nTqSlAXUZxDrrrpiJrIbqxDLS/kDUvOt3sHRKIsJMmd3h2DnpoRC1gQt+3w0Z\n7cfxOMnKWh1v23RbFXMOvm3sYDM+72Q3XLMKauWup6ingbmYqFP+LHSnSQ4M\nd56YsLlNeuMQv8ndGknPLcvGTLV67tDvrsJJqpYDVeZ4G486WwaT5XQKTYY4\nzQJzq62jpmbemvNCl+6TgQ42Dq6am0A9f3sFGDI0Z0J98GO+H7Fcp92WbfPt\n87N5U5YZH8ZT/ISGbaqmzn088+t0MHRPKAiTD6sJHcjmsZzLmSgEn5xLOLlf\nQs/Xel7PPWjhvnjcKbflRe2Wd+v33n7k5vqYlVmQUhMyF0dtWPrAVYbxUM5d\npq5MFI6gLzrEh+oxBPp3cqVKLMem2yBB1CKF3pbuzwT0hc0utRJUipke5a30\nR9CqgbRdEVaH/qbFT1WIKaCR0zH8T+SNK5eBiCissbguvZzwmE9clXeBCVan\nE3lWddEUeky3AllShMIQ8HGZfvP3kDIZt3OIACYhwAbxWlcC+INIVF4bl0RJ\nNaxnF/oP2rPezynsTeE0E5HNH3BnCvR15TgrEeeep3Lm6ICmBJXM7QQf8Wlp\naVa0\r\n=Q9j4\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "0d8613c72b6082f4ea7ad690f660ec9a5dd7ec23", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.2_1545381977364_0.4381383095389666", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "tapable", "version": "2.0.0-beta.3", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "fda2711d315db1c242ac558032548d221dc66418", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.3.tgz", "fileCount": 32, "integrity": "sha512-0H5N+0jxsh1yLsIJERSOPY+3e8XjiUC+uVxuEgb3COJvUooDGwWAHE96j2MdEoSOwzXkQNq0PbqqHH407pKwHQ==", "signatures": [{"sig": "MEQCIHXELoaFjm51Z7Drs9goyRQpUnltXw5tvJkjG9s2PeIgAiAdAXSGkw3hbqbmtJIe9O7gOnLCm7IqBITV12P6/+/Hpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLyTLCRA9TVsSAnZWagAAbkUP/2r5RslKITcAIZvXKvak\ntsTMMxQDz+2ROpOQ6AGbdNt7nel33XMgApCUPliDyDn2zyf1ZSHjlwg2G68k\njyW0jfKNc+J6M8F7gtozR0msc2ESAgCLe+ERLQTVhmgGbelz2+icNwDRwcOh\nf7yBThtYEAih8S6YnrvRHMGzoXw4QoubCiKSdzBcnHCqTmQvU4fE9ogmhdHk\n4B+7hSl1QJRSfPgR/Y3szIm6ri9Te7VtzjRod1ol+7kIbfWW3bih2bn/7OPL\nw2+zPcGjb8D9CsVGx9nPnsFJmH3zPeN5RThKWyyudy4JXmFL8dX42dzM9FKg\nynSSQy6RJuVe+KoQGO3+kECyBdNRvYdYTblrWrVKi6XcHgfMzEAr7yaEzPtj\njUOlIyeT4E4ZkFPPwMzGNt1gOQMziOGREQmgghZyKeXsp4ICLudA39zeCurS\n6tCSP1AjZbwlRffdSXaNuLVESMCSMYty8jXZKHnNDPGraczYXMg7PfzxuSMZ\nEe8etqWPY0ArWyF94yYMQhFQuGAirCLWxwyposChlnFE5FCZneAefH7OrH8j\neqHwJq2mjDutgnqJBK3h8U2AE0Sae1tbkGOBhqiBdgEGBXVnDsPJGR8rQ33R\n4KSt6JfDM1mWxf/Nx56zzqpBhGOHJrs+FPpgYjFRDBKClO6kO5WXLFDfeytT\n+kQQ\r\n=V34s\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "b121017afad244fe603be7d2133f1a37c716ce3c", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.3_1546593482394_0.07035947454007663", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "tapable", "version": "1.1.2", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.1.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "fb6e59d0a436f29bd505388e2185d55ba350d0c6", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.1.2.tgz", "fileCount": 34, "integrity": "sha512-frRO0wN2NSRSMVWyTQ5ebz66AqBkXOf0b4ISp1EySkibJ+a/z7DlvfQMlpKebzj9y7XhNC6TFyvsBWjqHCjpng==", "signatures": [{"sig": "MEUCIQDHGvvFI6V9r/iwxOOkdqBBFEP8qJU76u4IbSNY0mxI7QIgMvGWnaL+6b7O0QaEiKaj85EWp0BcNizIC8ftB36Dnfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsD8GCRA9TVsSAnZWagAARigP/0rhsNyIo8pdoRM2ZHDB\nRmM1irrkZwAFA5Z0Zw6URejrAxOlUMz8MVDv0zZUqByRjzHCBs+sWl2abuoY\nsWmXbD1LhZ/Aoz+0fIDKOwUIfZe1K3Im8q9YyQkIguRGOyqMl4ltZQepd70G\nXMGtvNkpyMPWedV2vbij7vp3FVBnhJ48dbLEJOiypnR9My+lUEtPju9uIcy5\nn272e9oVFa1inIhnZT1tWNuTUIiScASLnWNAA3B6YcWZIplF33OX6OlSkLCe\nRFB18zNBy/PulKyNJB+jT3oDNG0ZlFMND+EhMFbBcaFeHCGDgS3p8Yv5JJJJ\nWhOI0EL7kgeigZsjy9LvWQdD8byIUAViOuemmoMf3XZx58qsZUsomPGYI8XG\njfTkgtBWSCxMMHUZNOPVDynqCAhVrsmxVwcwwTDryK+tN1yBS9/hCJokGsgY\n7KvdlNTtr/PPhyQa469u7MBEbw7N/J1OdI4rOsn4GQgP4XBJmvZ/IZY7VW38\nsgwXNporKWos22ZQKA2E1l8r8UmgeoJplyu40iuSq6UG6wI9amp1DzpDWcPr\nfsnsaTFqaTlhRsKoHQPj2z+nq0WxRPKHtIh8mG1xNI8ny05oSse/JcchIwtx\nx6rLhMsZIPn7kbLyj1H20GA5mW+tZ5duHyp7xQV2/vmhsUXM5vSOANUKrZfF\nz6Iq\r\n=6XbS\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "2210ed4e50cb67f2936921a0170ee73340c30921", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "10.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_1.1.2_1555054341790_0.5937222728326765", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "tapable", "version": "2.0.0-beta.4", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.4", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "26a7acfdaa064768020ee909fc5cb7fc8778fb02", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.4.tgz", "fileCount": 19, "integrity": "sha512-2SqW2/WlGd2mP0ukqm8KQCou36O77zdqo2e+pp5viqGRIcHuUGB+TLA8OnzOh/wUAF6467htfNsWmWV92vufNQ==", "signatures": [{"sig": "MEUCIDprYTSSSYJ3Wkmf3em+WJwnNH4zF2tSJWTGNxt+ovjnAiEAokEkQUVzbQueRu4td+phdq4K5MPEQfxf6aY9p2Lwx7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsEB+CRA9TVsSAnZWagAAAT0P/jvtq0tgSOia6HYY26fB\nVebah5SveGtsQ683bkN6+l3Pis+5toB0PyaZOiWl4QQxOibXuvZlwEqC2/YG\nGVvX98K9fnohyTbeOtvHRKneWWnQXxTlkcu98azC6c0dGpleRphLyKM1QW7s\nG3tLW0yZ1XDnwsL6p9s3j4FXmY6y8NHEnphKs+5/ok3Hf2h4hzzbm4Qw+lhf\nDO7GtxwFLtY3JN/xtqsLBJPZLwpDv03V+H+eW5M6PMjbkNHLrszXsfhwlJlT\neNKWA+nGnCNBhYyjRc1Q8BL5vxtSut7Jbpi6J7XGX7gltbwYZoepB/H3t7Kg\npbTRmrqUVdtFs1ispjneX/5Ap9G0N33Mmfyoa7WceOdzWMZf44KOOXt11lda\nm69Up50Q1ZGjeWDBjsEeQoaShRdP408KB4+hjQrOJ+R/SfexTRP53963iPO9\n1KJQzpQZyWbqSCgI6xDEn+MvGMQlpl99hOeP0Rd2j8O2Py/Re9U4yy/5Q0IL\nkIMMs0bMvzNKzbqW/2rcvxEYGS3Yc5cbYFHzl5/dT8atq43UHR4K+3OZ4qJv\n/0i10Fr178T7UoRHmgsO5Ty8SfHW8+qFlK20R06nbWqaRt6IAC5Q7XWpooTD\nQcIT+LMgipi7wvrDnA6E/eGwSAeEM43zbfrt6h1C5nKXhHOdanxID02/0zuq\noas7\r\n=hEzM\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "d784d4a49bbd3ba3b0a1ba2382d64665d6f61f99", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "10.15.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.4_1555054717580_0.40048541949403305", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "tapable", "version": "1.1.3", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@1.1.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2", "tarball": "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz", "fileCount": 19, "integrity": "sha512-4WK/bYZmj8xLr+HUCODHGF1ZFzsYffasLUgEiMBY4fgtltdO6B4WJtlSbPaDTLpYTcGVwM2qLnFTICEcNxs3kA==", "signatures": [{"sig": "MEUCIGV+mdECGZtKWlCjw2zViTVH/13/5XZ2QwX74IaJZrPXAiEAj2thpFUXi+OBLtVNTexfGJFVJtV+mZHM97+g0clgzbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsEEHCRA9TVsSAnZWagAAb7QP/iIMKjbXGJpnX5/y2uxF\ngdqSFoe8wVvclRt/0bTpWWMETf/FHVf40iJMzGA4tcvMEumC0VyW/CkDY8+F\n2CobDNleQANiZv7CobeCpZ3oUOdjeKcLwju3j6v+4pARaopImU+/Xv7eQib9\n7MWaSwMYQUcCx3yNnenR8Dov5IvaVf0BJUllQhxRB6JZ04KMqxnFaZN5J4fx\nYt383LvloqodLrUjrt4Ys6UnVbQffDZ8KBvTuHEOX1kXtfNIJsG52ij+1eqe\n6OOm7YZVsa/cYw0RsaUCCRDn5B8K6vZgimm69fmYEvq5e6k4ihs5cPgt5yPr\nYuLudWVjnoM2QaCXJeJwK7grE0niJyLaEfnlFxJs0hRm8hpac6IItgt1Nbf1\nOFbBTfWVKIB3+Drta94tGYUcQGawBfR86Z+qi2p64CKjtqmLB0Kq3vRyt2Zq\naPv7iUqD5nOtBrTLoT7PMouS6z9oY0X6bxy9skQn/L1P2z7Mqz5PAeZH5AO6\ngk0y75rBcxbqkTWeGI4iMG7HOQP42K76jpP5uk9ELNZ9yKIm6EEJ6FbRy8wz\nxtWoB/3sMGOt8eGJO/6aPpLyovTVswGEah4O30I52Qxzs+Hx1o2g2qRVJsRO\niZHOPHMcJa6dWpzp4JClc3/lsYLga6EC/HI8FCwKk6iVtqCK/iuFaOTtteY7\nqyhz\r\n=v568\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "6454fd11a4ffb1722c9933c922f5f460b5401b87", "scripts": {"test": "jest", "pretty": "prettier --write lib/*.js lib/__tests__/*.js", "travis": "jest --coverage && codecov"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "10.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^21.0.4", "codecov": "^2.3.0", "prettier": "^1.13.2", "babel-core": "^6.26.0", "babel-jest": "^21.0.2", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_1.1.3_1555054854469_0.9798717952534601", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "tapable", "version": "2.0.0-beta.5", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.5", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "51def4d94c58ad8fadf00cac7661216502fe9c70", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.5.tgz", "fileCount": 19, "integrity": "sha512-P4o84kC8CD66ZuCs6noh3QLU2ZuaBvMl0yCmp9rr0GXZyIfLd1sB2ZvJZjitQgfds8GDKRLmiiAcssr2bEHQ0A==", "signatures": [{"sig": "MEUCIQDnZ7IR2d0XCjhwLxE3XI3FYk37jwAkwMQNZdaNts/w4QIgRTdNudGf3qzNLLMpXat5nXsMRWaPkPuCk2CMJmWyDa0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdA5qkCRA9TVsSAnZWagAAYksP+gKpNkEWk+XNCzVcl87F\nKxjnCJ8tYhncHxjKxBeWPuLR/xVtpgnEkg1TEnkRQ0RnQcaRscCBkI33szOV\nQT/+yyh93LoEUZo7nQLfAd4zylfBHc9RDNbGVMAzeO+jGicluvEd3eaT5FFX\ntn66yH4sTITtHv4rLHUoZcewldFMOWvQDzS/ZNSlXL2x6LlOubk+vA2NdLls\n9f9PXBm1gEFhwSVJWADjx19bOBmzjJEt0Zr6Sns7q1ryIQHUrhFBTrPFle5G\nHCRi2rVUtLik88dZY7DGUQXQBTyZU2uuGln4BGc3c9I9CVS6UJSgApHTpe+U\nYeboApgXgoHnRaUJSU/76SnPJYJWT34NTIZ4I1WIDTmPyfAAMlZOh2eNI3dd\nbQOHmS7uZRQoCbTfDpXNcaDKvxDejhoJA/dG0iMuGYBOx72mkeHONOcBvN0e\n7N5hz0GpInt2l0W3VIk4HVKc513VJM5Nq0qV9x1kI9JdPGM4cu0wbNxQ4+OO\nDVTTQ+I35+vXPFxbgNxj3aqqRBfe7bStm2R0cJuMcGWoL6V4jnzkuL6AHKmc\ndAa7oIaxF+ZU7V3+4WdgXNOhkuX8jPNY0qX6xnCLaa9Yl/gUvZViXXPkZ3O4\nmeRRf02MSnorvKsZc7AmRdankbgfsdUNHUycZ3k2YUeHIQX5k6ydXy6runaN\nD95l\r\n=dEnK\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "30efca93a53143eb1f7708673377be6e6a068034", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.5_1560517283910_0.1812510084607073", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "tapable", "version": "2.0.0-beta.6", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.6", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "d7f122ec617747806294bc44386b959476ee90a9", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.6.tgz", "fileCount": 19, "integrity": "sha512-gmkt404ETYimQfzVxVaMxBzC2m/cvY3MIXP6ta8hnoshhJieuFJHDnnUPxBCfh6tN121ZyzIWQ9edBnIIXnsiQ==", "signatures": [{"sig": "MEUCIQCa4ExP1rYDhlsL8s98xw0xzGKLtM2cnxoTPshJd8yb0gIgEpo8ZgN5rMofuK8I3IZbIov/acwmMRyhZy+bp1DWwc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJIuMCRA9TVsSAnZWagAAG4oQAJ4e5O1CgGaCaz2s3MsA\nXh9QLlg80C70yBh132LcZVIoHYk35Er9UBADyzbo0jj8eYfOh88guEnxoiH/\nVguJaTQNiQa9gk3t2mFk3GOs70bIQqxFQR7Z26lJTKjInwE4kf6k5tOmIhoP\nnQKz3oZJl8rzXReROVzdq/FgesCejPPtBu+cYcxuexKFV8wYRy8xWdxKGcze\nwgvPvEY0BxAigBV8dv8pP8RJWQEFWIJZLYlpBeB4iZwgJp8rlxkEYB1ncvCW\n8IYhxLNYTnwYfMK4Q+8xEwS/cQTGGK5POAL0tF1L48HwZSvYygLt31orUCHZ\njR6Q0FMa6J+64YiXNx5uCSVrpfW9eB0pETA1fKvQWh6fNGbBf/I1hy6+Gsip\nso6HFqj+o4zM4u21E2d2AXFATgIOpMgellVheOfmoe2NodiKmOAc19tQsvr1\nl3ioteWAaAZKSBcZ73wyByc0frQQF8mFt9xgZ5kliyUFhlcGR7OV1LDqC2ko\nkakzPySYiWh1YjMEGpWv70i2uAm0UBaFFpC7R73SY/H59tRnCC77iVOzK/7k\nnVOaDE3xsh9AX60NPmmthQQbvjQIfJ2jrsIFcdzOckQHT9IymczX3/yvn4Te\nlFlwaXmUzi5uZAyv+vm+F/T4zMvBfPY3vW5tOAr/BJgt7waoXy4BofEdwTxe\n1USy\r\n=lodA\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "577543a01c331023db67cf8d2da4c7ca7dc19866", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.6_1562676107801_0.12615982687360372", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "tapable", "version": "2.0.0-beta.7", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.7", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "b290366cf8a4486b9c5db3ba3f16daa3daf34013", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.7.tgz", "fileCount": 19, "integrity": "sha512-05KcdSz8ubbcHqD+wRba6aUL6pmW1QZENopbm1mAh3d+YQnhN5+2m/gd5gbPmQvkWRy1Fo2vMK+WfoPYZPfFsg==", "signatures": [{"sig": "MEQCICFsrh61pxKq1OUCGrcTmwvbXEtQbYrLjiFi9FaiA0QtAiATsVUmW43hnTIionmx92b4xzMBQs8vb+Go4FZ0UpTQkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJI/ECRA9TVsSAnZWagAAU0IP+wYW6QbYIXH5GQzuHrzc\no+Y9hBDIcT33V5auZjtSS8PKfDQjGt1YDJIBNnFgRf4GNQNhDnPjZtShfyqo\nHAlO+p29aTnVPYfmBbhSNbzsoy8yHT5mNDe84Dp+EInS2vnTlTuLyy82ASZs\nLFS1WQuKVp6GbrXD8fIYiL0kQJtuXP19QGOjOT8pdyWwG/fUvX2UkGl/W+1m\niiezYaoMe17xzUCLNqruU4U8jv3bGMenWmEGUM7y7gMXzeSxroM+Pq5ics2I\nymMfPheCPEVNzJAF5ZcYIW0/O91VuYMauiY4mIWk2fRlwgIV6sphe6CLazsb\nSQRnpBDevHTnPXisIbT04D2CPc7HQyfx6HPQjtIteM5gJhPPUdbNraZDt0zd\n0O/uyo4AHNc2Tfde3Ny1wawYeVQ1RsFqV/Ybrs0buXNqaNNsKksHjsRtW1t7\nw8YeyS1UkEfSmSuFOtiVQ++Js/Fxir6VBKVT2GgdaCrT/Nbb/RsbJhGy+zIe\nB8o+TSQowRpU76qdyPIg5MdeU8axlgGFQ7YIgmOj26h80U2AGTMeImGVq3uh\nMWsDuYNzBq8BhbOnJQ8VpjipnLl+6ywuEOPDJHw/0QLJG2D5tgN0KkyoLuTT\njOf91syc906NkAuBStCPTVwTumadnFm4yuwzYETr9e8KGGIiVZ6QO4jDev+b\nwlRK\r\n=iTxI\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "e225fbaf8cbd1fea381290edc6384e3dae2b5cb0", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.7_1562677187582_0.704341253368407", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "tapable", "version": "2.0.0-beta.8", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.8", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "0a8d42f6895d43d5a895de15d9a9e3e425f72a0a", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.8.tgz", "fileCount": 19, "integrity": "sha512-7qMajFcHb2O+aWufLoAvKhEehIwikXUTH1s8RP4R5gSYMIB0Tmypp+J90EtSNVbIlI7oB0Oz8ZlpJUJlZ5owxQ==", "signatures": [{"sig": "MEYCIQDVijLnGrK2u8gFD4qL6lnhjotpVtB58c0+d3L1vfALPAIhAOsKyXWpkq7Uz+H9EgzemMk6abR4HIGsWZ3/21Mka3Ix", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJJp1CRA9TVsSAnZWagAAn6QP/0XL40JjMp51c3njz8oh\nlPnOcoiisOGdzNqzrCa9JZWZ2oJ47nklVGrVgpASqu5rwSegUDOBRHqWfpzT\neNTji6nqPMTZ0T+2sR7Azh62VxItsKhcuhj4s9PFsUgy3KDOQe9kN6LJQuaN\nPoHcm+Htzk7OG30fI4gvkKG35f1maNnT14NVokv9DyjqjnohbtzEdF6CT9lY\ngN6rR4Xxd1kzNerFtTrb1XrVZ2GdYwjSUWg36v5Iq0v+6iFiXMqCW9wCHF5f\nHUnEbUYhjS44k6xc5vGQXfoGREYvTNhQjPi/WqJdTSBMbBFSh8DpwE1bABhm\n/YMWrEpud904BJPWB7maA6vu3PPsBiZYUSN69S3T5Hzi6HVyPqv75LFdUlnw\n9vW+GJwdKiwuuRj8iRHyVC1ZeKVSW0p4LSe6lX4HpBctVDgNsFeWOZuX4qT8\nUeyr0Yg7ETjLJ8yRr0h+jcZC/OKniMJUR2SytN6kwHXZpMcHSVfZy6bbH4r8\naIrFNW0fVRd7CHHyFUlWNi0PitANambuHC+vnJ/vouRM7wXEb8G9U0BQ4zF8\nY/pXHCbmW+t8SPSZZFs0f8av8b73obZqaIExYf/bb2q9vFLK1H+jtJPxmZDd\nmZvzF4PmntkX5w5f9TMmjvwexzozQwTlUX0EGOfrezWzneiwTzRXRGdA7MX6\nQiIH\r\n=7Hh1\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "14fbef85bef0b6682ebf24f5b241ab19c617e158", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.8_1562679924778_0.2288356709884427", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "tapable", "version": "2.0.0-beta.9", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.9", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "638496fb27b53e69c21a0e6a4435afbe805845cb", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.9.tgz", "fileCount": 19, "integrity": "sha512-+VBUuZXh+WIHnKOeo+A27SB/1sHTVWozcKweDIAhB/XOGnr8cy6ULZjU+qpGxO/G4xEyWCCaWTX/HPEkGg3Xrg==", "signatures": [{"sig": "MEYCIQDgmaRcB0/p3jz27R5kofSdYYyHEY2LHTbAiJJ/u4bpnQIhAM0aWl50g2HShcNeWM6qP3tQ3hDLuxM4p+k9RuV9ksUV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/OkKCRA9TVsSAnZWagAAHbYP/0YMPyM6ihsDuF25JJYL\nw7svsrHh7tgcIcVY7p4ZpNv+h2GDp+WLD84vMcjA1RuJ5pUWD/QzqOgi6yUZ\ntO0YeBLYPRRG4s9rzvl7nVttxS+zQXTSwck+Gyy83LLn0RYbXO83iukw4qgY\nUrm79LlvnI+Xcyb0TaBHmFByQwzrvCELHiYqXcovwwXKwayUTgZRi0ngKDDZ\nS4p7ymLCZSTfkQGVTtCj8X3aIrdylQOL+sH66Io+h24JT7HOfM6JmJBNQzow\nyTbu9DjGpuMHGc2VUL2VIiHc0mKxYIQxebGT7dKcSSYLJ6GB5qlKv/NBTiYf\nEtkUgRZnuQV8LTaOChVb4o0JlbJgrBt0ro7w0RoRcR6zgNZyfF4hIJMpQ2cv\nK9g6O8nwTl7v/QxVcN1bGGlsWUWUEw4JjGq9VDg1+QaS9CmMoodwLQ0bCzTl\noopcQqg4gyTjoHwjcWzNbYhxNM6SBcLdz/Ui9Q1ZP4KlsXYDgjtNeTgnOWcc\nLYcBIVsUJkTqnUzJP/bHClPUnMdZnYr2Hdr2NE89VY75M0URRGL9axH9x+Tr\nIMnl/SmagiqvrWaQB3KMy4z/ywbWVc6evzRbAQChn0kUoxXXMIBltiGTY/sO\nhxh3okcIRWMJdp4Sq70udKSrZowDng/h4H9TZbY2601IJku5vUOp0gAAye86\nTiJL\r\n=XLO1\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "747a66766f8be92eead47f312f39dc84c00ff054", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "12.13.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.9_1576855818256_0.6189577538314641", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "tapable", "version": "2.0.0-beta.10", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.10", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "54a95a1f6be6c65d2d8aa4eda2562325ff6c2a1e", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.10.tgz", "fileCount": 19, "integrity": "sha512-yLpWrQT5qTw0SWvaJSrFpog+fvrbpfEfHApCKV+hrkE1937WifJf6UP1xonVXqre30pxmANsqm7B3XYQKtvlcw==", "signatures": [{"sig": "MEUCIFxk7xANC7bcyPHB5yYtTYFImcrGIc0626uC5orn0ti8AiEA6UVHuotcejpfFGqJYW/fVcJFAgCpecWP7cmeNy2qkKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ9F9CRA9TVsSAnZWagAA5PEP+gOW4/ZE12/nc14aZa+O\ncSsHt3eE27bqadqI/TfDtuWDFIW/sXoY1pl2vhgrI4JdrAcK+s8yBWD1YQWJ\nuX8eedNxIZLRtdtDFvBhO7m/AOXMVj43HxsW+63UcbPhBhCNul3ElxPOm2lp\nmocLwPVEGGMsuvuQItmV21+ksddR8PQW8vk73vai2p+/elH/nn7hlbA8zi35\nnYnGJzx+zAVID2cJ7kRJ9tdt+aZU5NbgMrAxlsS9h2jHk37EH/wH6zlns9em\n2qEj+4dP4JJpJtQ2YAaK51t2zKu5kxSrx8urCz58JFXvdsFtAFhThGj98IOQ\njLsYsAPaNWYTfjIHPEMAg0L4VcwsCfbmPvC4lsmkT4LdDsQFC93SmhsihbmQ\nSmJgwJ+0jwAtsJr+rLSS759UptVVu5Tyfmk7fYsgt6kZ2e/0/w64Tw14Rpwm\n8JUIPywcjYFkFpQAVVEw35OTuN3/2TkeZiJDslzUP2qGbAIo4deQIKKlMMZ8\n5zRZkyhzLxrltQZL8rUKLxNkv7hbv4hZ9cIyJkvNMWPxnBOzLvESXus3uxmk\n4/tSqkzAieQ9nKYS8jalE9brhvk4V5XCJCQOvD3G4z+o3/YVRly32p6bWlH3\n2Mhd1ueS28Ypkn5zXjwT2pmbEnEf08ijQ+KzbSNqf+b23f6HL3SmntSXMPyd\nSfig\r\n=uh9V\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "75fcb00e84922de8cb0ecb9840c59b68c5ced0d3", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "13.7.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.10_1583862141444_0.6298353164822188", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.11": {"name": "tapable", "version": "2.0.0-beta.11", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0-beta.11", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "5a6bd5e0353fad4da9e94942206bb596639e8cf7", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0-beta.11.tgz", "fileCount": 19, "integrity": "sha512-cAhRzCvMdyJsxmdrSXG8/SUlJG4WJUxD/csuYAybUFjKVt74Y6pTyZ/I1ZK+enmCkWZN0JWxh14G69temaGSiA==", "signatures": [{"sig": "MEUCIQCmUv5zXiZmbO1XTWApjxDAdIbtLMHDP3CEPWM4wABB8AIgOP+wmyJ3L3eyumE7DYZb2IfrTzAmiAPQEIkbTjm091Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevpmBCRA9TVsSAnZWagAAdBcP/1qTEopmSlj2GbyeRXVE\nCzt9/eDJOYDl7hXBaHPx4g89rbgGL6DtCy3PxwHn6TYcSgPRQpg9dc+AjLuu\n9oFkvu4lBuyjbxcDpAQ09s/lRsGdKEmR6IQ/tKMqwDoUQkdPgNVM3xmAK03Q\nOAGrm/RfPjv6SP5vi4c5uWPuhlkaODdgPRtyZ5mZU/X3NlaPJ75SPKrJXuwC\nT0lQ5ZUVDmyitbBn6RWtI8+xrkEMUCq4pfCdQV9Xv5KAcpveDAQrSpJUvD2c\n9x7MKSDhz3xg4LRURA4c9e0gOtsmpVum5bmoJoV1v5Z5lKX1NLCGORfPVgHL\nLQLDjtHN+yU6Mk1rqWgVcSD9jf5iD5yPP6wmJjkGAgaXsnMt5ZA7E2vB54iJ\nfwqvqk2vendyBVfRtmvJhyiJmw+S/7NP8WfY9po/Pz0uvvZKhKRSTCo2lRhn\n7cF4jgMbVGfPx9CB6kdEOqhg3Frwnx74s+T4A0Oci+3uQoQr3vQxnhFTWvXy\nMomw+AGRv7ISvoOyRSLENMpXaUHFXTAKU/FFs8g8hFsv2tv/7uf+rCpFgrdy\ncuj6kfB3eBpOyIaCVeMVRFgBWquKzrg5f9LuhS8bu78wYtEkp1HGMn5iCaFW\ngqWoTI5E2nE6ZVcCsGBPNtNqb8InWXSpYrcW6V5ex3NFisPYmcoEyxrPyQuS\n8PrV\r\n=rhtg\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "2e61d77a22cde4e8899a473ec7a625897287acde", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0-beta.11_1589549440739_0.8880069714665995", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "tapable", "version": "2.0.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.0.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "a49c3d6a8a2bb606e7db372b82904c970d537a08", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.0.0.tgz", "fileCount": 19, "integrity": "sha512-bjzn0C0RWoffnNdTzNi7rNDhs1Zlwk2tRXgk8EiHKAOX1Mag3d6T0Y5zNa7l9CJ+EoUne/0UHdwS8tMbkh9zDg==", "signatures": [{"sig": "MEYCIQD34fzQJ9HyoR0K5jXDy5KFOJdfzu12XUTli3tBl23OKAIhAJIsxxbP7ChAGvrCi4bthpqaY3Sp81SR/m+lhLIya+xQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY+eeCRA9TVsSAnZWagAA/1sP/1XK6WqvKDjEYhBH/u6K\nDdx5yXK1b7YCpiMwD3zF1cPCGVkGZl62BYPPxPXcOa6Va6Da5uW4oZgKEval\nKM7j4DTplySbF0rb+L+Qmm2k0LxG2gaIQVg5U/3odHRDqKv6vR6k7dHvPQ7b\nOKryrWKD72dQ8dVOSCgLoPtmcRZ7xSxX135NHK42+xPCHm/4zd7qMbfLiXt4\nin4YHcAacgarapcxAo2ZYyThhMR5XaSfAVCozMDebbCT+AonWWUQm3QNAXv6\nrjjRi56K6vgW5TQgYE3B37ekRQ+eXjTHq2y3O7oc0DLTOSYKXzptpny6HwYr\ngfraRYFHi7RReVGEsDeUXCmh3m///caDbD3cogYgyZ5QwlDKkoo/2COr69Nr\nm0t/r0OdW7ML/RU4V90V2YSG5YTwrBDxuxEBoPu8s1blHV2fRCMK2MUgbqzM\naV26296QrsjpjjYpjzSm9rttXXvwol0RndxgIc8yyYaXHC7pHkGLszJXRD2D\nWy58smJnV1H88SFnBYU96wjGlPOw93vpyYTE/Bj+iDVJIfcJUdcIzasb/qyz\nK6FPrtcToUfgZJ4TsVxchPhYuyf3vCX228TMoKdo+82kvD8hUe/CvAugfvDG\nvWmLLpnMjyBbDdepNpVWmz6dhJ//0F79KEBrbZs6PYOKw5IbTuAJnXGwrTmF\n8RAh\r\n=JIeV\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "fa54822199203163a13b5bf7e403abc118ce3a87", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "14.9.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.0.0_1600382878136_0.801814746317927", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tapable", "version": "2.1.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.1.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "5a9701fe2ce5602c9899f41f6bfbc944c8b2f209", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.1.0.tgz", "fileCount": 19, "integrity": "sha512-J89ZFmbrfc/qQ5xkAo5EOCaEQ+2vzPRqS/KjUThzcneD1ipIZ9M9z/nWRwUxK2elHr3REYANoivNDHcBDrjshQ==", "signatures": [{"sig": "MEUCIBsnTtG4JnFTDPGvL6JWCa2ZqnJujjy50UyN7CLCSxGEAiEAtyLrZDdr9uEWuVeW0MQhlavFCu5lC4jo+Bh5ZrYGfpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqQvzCRA9TVsSAnZWagAA+AQQAJQyAj4km9LoXqQ5L94A\nBm4b1lYDzj0cT3v8m/wLVD/LkzBRRnnFecBVpfQ0OPtQ2qk0MhKNJkclGC79\n8xqU882PcwRNUK4E/qbooeKfP8iaTL43+YCjdAOLui4xpHZqt8jZ/PVH4uAY\nEiNTafzmJ9ZP/BZanGa3u2VJSXksCPvJGZdMu6/IV+61rzgc2tT3Oet7zRxi\n+cRligXEkVf79sWrQDyE16XK3G5UEDkoSAZmN3xnvXg6Cv5DcZkTFQ0ag1to\nGNQ4OKSBdvMW7tDpdtKc4aN5rNx+sbzYcZ6Sq+fZ13rHiucxuz59/rWPArqK\n3XRYc0UxkDZWy98s2P7Fr4V5oOqvrh6WQNjFGkXufS7d1Dl3bTmgqqEi7Qm6\nE2HkiwUXmbPXZrUW5Ozo3MVH3FKwQxuqY+2M+u1MlHDvNxmmaLDfl78h8dQo\nZ98/TgVfMNB+tMF8NHXmXEaUy53x226L4uGcCAEaYenRYgx9402Vn1hG2Mrx\n8uSOIWtmcHrc1stuJLTQVlqkek9zdAjjWHFHdzdPMAd5VOVSNuC0aAolI9Cf\n14RwYTfkbIEoJPkMQjEeRLIJEecUePRoISxOXNKEhBLGmbt5lHOvnXOl9qUj\ncUi5YwjEbUNw4lseKd+z5KBMCoHMvEZvm+KuYLQikEhiT8M/40hYr1M3I9kF\n/Cve\r\n=lwii\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "0c4aacb0578d82e02434e8d6e390cc462174d2d6", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "14.9.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.1.0_1604914163114_0.36694484012036366", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "tapable", "version": "2.1.1", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.1.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "b01cc1902d42a7bb30514e320ce21c456f72fd3f", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.1.1.tgz", "fileCount": 19, "integrity": "sha512-Wib1S8m2wdpLbmQz0RBEVosIyvb/ykfKXf3ZIDqvWoMg/zTNm6G/tDSuUM61J1kNCDXWJrLHGSFeMhAG+gAGpQ==", "signatures": [{"sig": "MEUCIHhMlyD3U5uxQarnFpd69ktxX/VsA5AyX9kmbZe1I4lUAiEArYBCaxOVaRILCZm7w+IBCgzlJdUbNXKFgBn0FsueUlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqRn2CRA9TVsSAnZWagAAsJsP/Rl5vnbhYOqJhIoTwMHL\n986CzIAElz3rG9aPymaaZ0PaaDoldSvhNcWL3fE9oeskbz+vzvA/rhmyAhPK\nNPynib+D8fiOM3EQRAm9JHroWiOyUDM342syZxQ7AByI6DbjFOr9LzhLAf4E\nezdWSusszQ2gpJpi3bvHxezVxZ/Zx8o4KtY0Q1G5le+0dRidHlzUpXMN3b9O\ns8ZtTKo8sqFpwshwNhZvNEHvW8kwtYwWtupBunRua0IcDQFj8ETyw0qzkVg2\nxkeb7d6AaMfyvrzi4+4I5CQ+oSf3TYO4m3TYdZ8BP2MnyDVgeKotiK3KZHaF\nY4mV+vbX+UxEZvItZKCA94odae9ANEeao6sNiAjXkqVBE17tpqT9yUpfrfAJ\n8HhqU4K2wz7ErM/n7g0MVRiF0ehKUFGoMFt68CXStvp/DVcXj08jwpoXmB6+\nT0pabjJ3hULFN+245vy3glEi7owvzB+Gw0VlEv4WfBeob4NgZCHfl0hT/BBf\nQBxeob4K9cIY4OlM1d3AhMkaQDyhjIruY3JW7eevtI3vZE//vl+IQnvM3EJp\nOfqOc3eG6h8EgqGheNy9OVScX5r0XCGDBfrIXnmBE8FXw2lG6nW+UpYLmJll\nUcCBYF9NqfxzyN0LxsiJKmveyrijIJkwT7d7YAJhwfjrGjIswJGyeX1tChaY\ncXuc\r\n=Omro\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "engines": {"node": ">=6"}, "gitHead": "a3cb3ae77035012a470ca5b5ac8ec627550d4700", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "14.9.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.1.1_1604917749685_0.2597182598451029", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "tapable", "version": "2.2.0", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.2.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "5c373d281d9c672848213d0e037d1c4165ab426b", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.2.0.tgz", "fileCount": 20, "integrity": "sha512-FBk4IesMV1rBxX2tfiK8RAmogtWn53puLOQlvO8XuwlgxcYbP4mVPS9Ph4aeamSyyVjOl24aYWAuc8U5kCVwMw==", "signatures": [{"sig": "MEQCIHmPcvfD6FleZoERlPgBMiIJ76GjaxmBdUG+KUJslvDJAiAPpIc48OEEjQBNQh5Ew3la1YOVqCsI7q4IERdC7qL3jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfykAsCRA9TVsSAnZWagAA9JcQAJD0QGtio8EhwPBQs9Fh\nv0K4ZkoN1YCzW2iME0bsHSQE1PvJprer5LiaXlmIsMuG3/2ptH65+/V+gQZP\nrCe769utxLrT0x6Ra9FTdOmfRTT3FUgKOiMAk7tMOtsu/ICwOz97w7sQ2H6Y\nYFhX754n1Tx9hvcbLVaLVcpJaAqgG4FSAaya5OX9P8Z843eHTyNi4a2piFd6\nmRcwCl2Zdz2i6JOpyyNsvFExerqu1vfmdh742XYRYoEpuT5L2Mg2XX860ZBP\nfp05TjvLpJ6OQN8QRmvr4eVsJjkj9yiDrMYkQyphvDPSDtFJ9T1LcMsatjah\nW6mUv1X/ImY39WiFT7ECx2o0KCyh5gDeD8wV6rPyMjzEImT+Z+tQL1guzmAJ\nu7M1g40BLbR3mM9Z2NF5Gt8hKdELJgv20UfQdlBtf2BoU5fDbSTPzyqeQBmu\ncV0b8y+YihEoDvHQwQ2YUOilahvNSP4J87HXpty0rxBZSPx73+p1LCm3xIdJ\n/5m8MD3/AcNh3ogXzTevYzpqNZXGi5UFs/eowQssCAi5ODpZeHUt6VR6GJkt\ngyj54KMq/rba4dGomjYoaHmQNS8nCfOB9Gc2R4O2NDPZS9t/QIn4TpJjeFy5\nGUUHW9Cip51YlJbJ9xlmbYmM1hbFE9vfm0FxDpQvbREwboNuL0EN49t6KNIe\nWx0y\r\n=sMT2\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "browser": {"util": "./lib/util-browser.js"}, "engines": {"node": ">=6"}, "gitHead": "413fa785018406464963c29d172e06d2df42f22e", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.2.0_1607090220264_0.5042022096574259", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "tapable", "version": "2.2.1", "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "tapable@2.2.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/tapable", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "dist": {"shasum": "1967a73ef4060a82f12ab96af86d52fdb76eeca0", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "fileCount": 20, "integrity": "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==", "signatures": [{"sig": "MEUCIQDhz+86wq5+Ehy+pB3Xr7IUllSvLOZ8GsUumZnfsdI22gIgTffcSCNQ+4Jn0rr3yUJrO+kQC/oU9E3A4hoet7ZUVdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPyApCRA9TVsSAnZWagAAGSYP/jzk6UWqUEXyCDpO0YkN\nLd9mjVtrChODLtvd2ioAK/EQCaJyiXLjTJQAvXphP0Y1aby+2lzOWpp60X7T\nuLwP8gWui31/Z14ie5HQXFkiuYdFd6yfWr+W7FKkWri4FdA4Y1OlTfml7I//\nw41yGZZIyR9hJD+pjAag9kgZ0XhdW+gAtgU6mh8ak1pu4fOihfEUBrJNMkPH\nwJY1sJvC1zu3EiSzYXeh23YTLdFDrgh20CtKClOOJ6IqjR1p0Hl9zAt7cBbz\nc6HqeAK9bfEWateQv11fHGRADEOkvRGYsIyEl9cTQch2QO0UzlMtKprfP3Np\nKp1dOVS01P1bLYPDoaZHsxpGbWHde6LYW7PTX4Pn5n8pj/U7OBwjL4CYSKw8\nz9JEAsTUZkioYnC9UHZIKhSD5CMTrP+cNGyhRaZkYWxTBcBjbNOprneTmp5v\nDIjPPDO+9JXQxqsIycOQh8Z5SWOiPYltzO7K4sB83L95sQraD7U3WEyH+Dh1\nQ1dt6MAMgSclkstfkOLjnhjDkznhbGqi6H58wDI8DRYJGevpJrTN/paD3oK6\nYvwCZmgBRmAETnBAAGP6SMJRnHcW1XUUsJNfKpXTMSfkEtuEgqOLhUSYP5US\nRwLgMwBRwrc9baLWIwut93HlyqpuioCxzTTopHBbdKXQ7GexwvbDDrrzaJwQ\nCM4N\r\n=2KOf\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "main": "lib/index.js", "types": "./tapable.d.ts", "browser": {"util": "./lib/util-browser.js"}, "engines": {"node": ">=6"}, "gitHead": "a0a7b26224557bd8bb09b97e0126b7dbda9f8e6a", "scripts": {"test": "jest", "pretty": "prettier --loglevel warn --write lib/*.js lib/__tests__/*.js", "travis": "yarn pretty-lint && jest --coverage && codecov", "pretty-lint": "prettier --check lib/*.js lib/__tests__/*.js"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/webpack/tapable.git", "type": "git"}, "_npmVersion": "7.22.0", "description": "Just a little module for plugins.", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.8.0", "codecov": "^3.5.0", "prettier": "^1.17.1", "babel-jest": "^24.8.0", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/tapable_2.2.1_1631526953737_0.40136898445136016", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "tapable", "version": "2.2.2", "author": {"name": "<PERSON> @sokra"}, "description": "Just a little module for plugins.", "license": "MIT", "homepage": "https://github.com/webpack/tapable", "repository": {"type": "git", "url": "git+ssh://**************/webpack/tapable.git"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "babel-jest": "^24.8.0", "jest": "^24.8.0", "prettier": "^3.5.3", "prettier-1": "npm:prettier@^1"}, "engines": {"node": ">=6"}, "main": "lib/index.js", "types": "./tapable.d.ts", "browser": {"util": "./lib/util-browser.js"}, "scripts": {"lint": "yarn fmt:check", "fmt": "yarn fmt:base --log-level warn --write", "fmt:check": "yarn fmt:base --check", "fmt:base": "node node_modules/prettier/bin/prettier.cjs --cache --ignore-unknown .", "test": "jest"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}, "_id": "tapable@2.2.2", "gitHead": "7020413b8b58a9d7487938a218c0c0950376c94e", "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==", "shasum": "ab4984340d30cb9989a490032f086dbb8b56d872", "tarball": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "fileCount": 20, "unpackedSize": 47215, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDUOi9yT0yN2iKET556CKCKHdM20AIpT7JHJk1hoRiQ+wIhAPOQCWRYQAkz8ADB3ArZaD3k6vJVNi774yrgx5koePcV"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tapable_2.2.2_1747693100551_0.8058161833943804"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-01-21T11:27:02.964Z", "modified": "2025-05-19T22:18:20.928Z", "0.1.0": "2013-01-21T11:27:05.672Z", "0.1.1": "2013-01-21T13:12:58.031Z", "0.1.2": "2013-05-13T11:25:13.514Z", "0.1.3": "2013-10-14T11:48:35.999Z", "0.1.4": "2014-01-29T08:50:57.784Z", "0.1.5": "2014-01-31T10:57:23.664Z", "0.1.6": "2014-05-31T11:00:24.142Z", "0.1.7": "2014-06-01T09:29:00.228Z", "0.1.8": "2014-09-23T06:34:55.479Z", "0.1.9": "2015-04-12T19:50:26.186Z", "0.1.10": "2015-11-24T06:53:24.122Z", "0.2.1": "2015-11-24T06:54:18.214Z", "0.2.2": "2016-01-03T20:04:24.102Z", "0.2.3": "2016-01-19T23:30:25.841Z", "0.2.4": "2016-01-26T16:49:36.139Z", "0.2.5": "2016-12-05T21:01:36.219Z", "0.2.6": "2017-01-11T10:05:32.913Z", "0.2.7": "2017-07-20T05:40:52.260Z", "0.2.8": "2017-08-02T08:11:25.628Z", "1.0.0-beta.0": "2017-10-16T14:29:31.495Z", "1.0.0-beta.1": "2017-10-17T14:23:48.806Z", "1.0.0-beta.2": "2017-11-27T15:18:02.373Z", "1.0.0-beta.3": "2017-11-27T15:34:03.114Z", "1.0.0-beta.4": "2017-11-29T14:59:26.320Z", "1.0.0-beta.5": "2017-12-12T12:29:30.544Z", "1.0.0": "2018-02-22T07:57:12.283Z", "1.1.0": "2018-09-12T10:53:06.435Z", "0.2.9": "2018-11-27T09:10:18.004Z", "1.1.1": "2018-11-27T09:10:55.694Z", "2.0.0-beta.0": "2018-11-27T09:15:53.863Z", "2.0.0-beta.1": "2018-12-15T13:20:11.842Z", "2.0.0-beta.2": "2018-12-21T08:46:17.483Z", "2.0.0-beta.3": "2019-01-04T09:18:02.539Z", "1.1.2": "2019-04-12T07:32:21.959Z", "2.0.0-beta.4": "2019-04-12T07:38:37.789Z", "1.1.3": "2019-04-12T07:40:54.638Z", "2.0.0-beta.5": "2019-06-14T13:01:24.060Z", "2.0.0-beta.6": "2019-07-09T12:41:48.008Z", "2.0.0-beta.7": "2019-07-09T12:59:47.697Z", "2.0.0-beta.8": "2019-07-09T13:45:24.899Z", "2.0.0-beta.9": "2019-12-20T15:30:18.362Z", "2.0.0-beta.10": "2020-03-10T17:42:21.600Z", "2.0.0-beta.11": "2020-05-15T13:30:40.824Z", "2.0.0": "2020-09-17T22:47:58.254Z", "2.1.0": "2020-11-09T09:29:23.434Z", "2.1.1": "2020-11-09T10:29:09.872Z", "2.2.0": "2020-12-04T13:57:00.381Z", "2.2.1": "2021-09-13T09:55:53.936Z", "2.2.2": "2025-05-19T22:18:20.739Z"}, "bugs": {"url": "https://github.com/webpack/tapable/issues"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "homepage": "https://github.com/webpack/tapable", "repository": {"type": "git", "url": "git+ssh://**************/webpack/tapable.git"}, "description": "Just a little module for plugins.", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Tapable\n\nThe tapable package exposes many Hook classes, which can be used to create hooks for plugins.\n\n```javascript\nconst {\n\tSyncHook,\n\tSyncBailHook,\n\tSyncWaterfallHook,\n\tSyncLoopHook,\n\tAsyncParallelHook,\n\tAsyncParallelBailHook,\n\tAsyncS<PERSON>Hook,\n\tAsync<PERSON><PERSON>B<PERSON>Hook,\n\tAsyncSeriesWaterfallHook\n} = require(\"tapable\");\n```\n\n## Installation\n\n```shell\nnpm install --save tapable\n```\n\n## Usage\n\nAll Hook constructors take one optional argument, which is a list of argument names as strings.\n\n```js\nconst hook = new SyncHook([\"arg1\", \"arg2\", \"arg3\"]);\n```\n\nThe best practice is to expose all hooks of a class in a `hooks` property:\n\n```js\nclass Car {\n\tconstructor() {\n\t\tthis.hooks = {\n\t\t\taccelerate: new SyncHook([\"newSpeed\"]),\n\t\t\tbrake: new SyncHook(),\n\t\t\tcalculateRoutes: new AsyncParallelHook([\"source\", \"target\", \"routesList\"])\n\t\t};\n\t}\n\n\t/* ... */\n}\n```\n\nOther people can now use these hooks:\n\n```js\nconst myCar = new Car();\n\n// Use the tap method to add a consument\nmyCar.hooks.brake.tap(\"WarningLampPlugin\", () => warningLamp.on());\n```\n\nIt's required to pass a name to identify the plugin/reason.\n\nYou may receive arguments:\n\n```js\nmyCar.hooks.accelerate.tap(\"LoggerPlugin\", (newSpeed) =>\n\tconsole.log(`Accelerating to ${newSpeed}`)\n);\n```\n\nFor sync hooks, `tap` is the only valid method to add a plugin. Async hooks also support async plugins:\n\n```js\nmyCar.hooks.calculateRoutes.tapPromise(\n\t\"GoogleMapsPlugin\",\n\t(source, target, routesList) => {\n\t\t// return a promise\n\t\treturn google.maps.findRoute(source, target).then((route) => {\n\t\t\troutesList.add(route);\n\t\t});\n\t}\n);\nmyCar.hooks.calculateRoutes.tapAsync(\n\t\"BingMapsPlugin\",\n\t(source, target, routesList, callback) => {\n\t\tbing.findRoute(source, target, (err, route) => {\n\t\t\tif (err) return callback(err);\n\t\t\troutesList.add(route);\n\t\t\t// call the callback\n\t\t\tcallback();\n\t\t});\n\t}\n);\n\n// You can still use sync plugins\nmyCar.hooks.calculateRoutes.tap(\n\t\"CachedRoutesPlugin\",\n\t(source, target, routesList) => {\n\t\tconst cachedRoute = cache.get(source, target);\n\t\tif (cachedRoute) routesList.add(cachedRoute);\n\t}\n);\n```\n\nThe class declaring these hooks needs to call them:\n\n```js\nclass Car {\n\t/**\n\t * You won't get returned value from SyncHook or AsyncParallelHook,\n\t * to do that, use SyncWaterfallHook and AsyncSeriesWaterfallHook respectively\n\t **/\n\n\tsetSpeed(newSpeed) {\n\t\t// following call returns undefined even when you returned values\n\t\tthis.hooks.accelerate.call(newSpeed);\n\t}\n\n\tuseNavigationSystemPromise(source, target) {\n\t\tconst routesList = new List();\n\t\treturn this.hooks.calculateRoutes\n\t\t\t.promise(source, target, routesList)\n\t\t\t.then((res) => {\n\t\t\t\t// res is undefined for AsyncParallelHook\n\t\t\t\treturn routesList.getRoutes();\n\t\t\t});\n\t}\n\n\tuseNavigationSystemAsync(source, target, callback) {\n\t\tconst routesList = new List();\n\t\tthis.hooks.calculateRoutes.callAsync(source, target, routesList, (err) => {\n\t\t\tif (err) return callback(err);\n\t\t\tcallback(null, routesList.getRoutes());\n\t\t});\n\t}\n}\n```\n\nThe Hook will compile a method with the most efficient way of running your plugins. It generates code depending on:\n\n- The number of registered plugins (none, one, many)\n- The kind of registered plugins (sync, async, promise)\n- The used call method (sync, async, promise)\n- The number of arguments\n- Whether interception is used\n\nThis ensures fastest possible execution.\n\n## Hook types\n\nEach hook can be tapped with one or several functions. How they are executed depends on the hook type:\n\n- Basic hook (without “Waterfall”, “Bail” or “Loop” in its name). This hook simply calls every function it tapped in a row.\n\n- **Waterfall**. A waterfall hook also calls each tapped function in a row. Unlike the basic hook, it passes a return value from each function to the next function.\n\n- **Bail**. A bail hook allows exiting early. When any of the tapped function returns anything, the bail hook will stop executing the remaining ones.\n\n- **Loop**. When a plugin in a loop hook returns a non-undefined value the hook will restart from the first plugin. It will loop until all plugins return undefined.\n\nAdditionally, hooks can be synchronous or asynchronous. To reflect this, there’re “Sync”, “AsyncSeries”, and “AsyncParallel” hook classes:\n\n- **Sync**. A sync hook can only be tapped with synchronous functions (using `myHook.tap()`).\n\n- **AsyncSeries**. An async-series hook can be tapped with synchronous, callback-based and promise-based functions (using `myHook.tap()`, `myHook.tapAsync()` and `myHook.tapPromise()`). They call each async method in a row.\n\n- **AsyncParallel**. An async-parallel hook can also be tapped with synchronous, callback-based and promise-based functions (using `myHook.tap()`, `myHook.tapAsync()` and `myHook.tapPromise()`). However, they run each async method in parallel.\n\nThe hook type is reflected in its class name. E.g., `AsyncSeriesWaterfallHook` allows asynchronous functions and runs them in series, passing each function’s return value into the next function.\n\n## Interception\n\nAll Hooks offer an additional interception API:\n\n```js\nmyCar.hooks.calculateRoutes.intercept({\n\tcall: (source, target, routesList) => {\n\t\tconsole.log(\"Starting to calculate routes\");\n\t},\n\tregister: (tapInfo) => {\n\t\t// tapInfo = { type: \"promise\", name: \"GoogleMapsPlugin\", fn: ... }\n\t\tconsole.log(`${tapInfo.name} is doing its job`);\n\t\treturn tapInfo; // may return a new tapInfo object\n\t}\n});\n```\n\n**call**: `(...args) => void` Adding `call` to your interceptor will trigger when hooks are triggered. You have access to the hooks arguments.\n\n**tap**: `(tap: Tap) => void` Adding `tap` to your interceptor will trigger when a plugin taps into a hook. Provided is the `Tap` object. `Tap` object can't be changed.\n\n**loop**: `(...args) => void` Adding `loop` to your interceptor will trigger for each loop of a looping hook.\n\n**register**: `(tap: Tap) => Tap | undefined` Adding `register` to your interceptor will trigger for each added `Tap` and allows to modify it.\n\n## Context\n\nPlugins and interceptors can opt-in to access an optional `context` object, which can be used to pass arbitrary values to subsequent plugins and interceptors.\n\n```js\nmyCar.hooks.accelerate.intercept({\n\tcontext: true,\n\ttap: (context, tapInfo) => {\n\t\t// tapInfo = { type: \"sync\", name: \"NoisePlugin\", fn: ... }\n\t\tconsole.log(`${tapInfo.name} is doing it's job`);\n\n\t\t// `context` starts as an empty object if at least one plugin uses `context: true`.\n\t\t// If no plugins use `context: true`, then `context` is undefined.\n\t\tif (context) {\n\t\t\t// Arbitrary properties can be added to `context`, which plugins can then access.\n\t\t\tcontext.hasMuffler = true;\n\t\t}\n\t}\n});\n\nmyCar.hooks.accelerate.tap(\n\t{\n\t\tname: \"NoisePlugin\",\n\t\tcontext: true\n\t},\n\t(context, newSpeed) => {\n\t\tif (context && context.hasMuffler) {\n\t\t\tconsole.log(\"Silence...\");\n\t\t} else {\n\t\t\tconsole.log(\"Vroom!\");\n\t\t}\n\t}\n);\n```\n\n## HookMap\n\nA HookMap is a helper class for a Map with Hooks\n\n```js\nconst keyedHook = new HookMap((key) => new SyncHook([\"arg\"]));\n```\n\n```js\nkeyedHook.for(\"some-key\").tap(\"MyPlugin\", (arg) => {\n\t/* ... */\n});\nkeyedHook.for(\"some-key\").tapAsync(\"MyPlugin\", (arg, callback) => {\n\t/* ... */\n});\nkeyedHook.for(\"some-key\").tapPromise(\"MyPlugin\", (arg) => {\n\t/* ... */\n});\n```\n\n```js\nconst hook = keyedHook.get(\"some-key\");\nif (hook !== undefined) {\n\thook.callAsync(\"arg\", (err) => {\n\t\t/* ... */\n\t});\n}\n```\n\n## Hook/HookMap interface\n\nPublic:\n\n```ts\ninterface Hook {\n\ttap: (name: string | Tap, fn: (context?, ...args) => Result) => void;\n\ttapAsync: (\n\t\tname: string | Tap,\n\t\tfn: (context?, ...args, callback: (err, result: Result) => void) => void\n\t) => void;\n\ttapPromise: (\n\t\tname: string | Tap,\n\t\tfn: (context?, ...args) => Promise<Result>\n\t) => void;\n\tintercept: (interceptor: HookInterceptor) => void;\n}\n\ninterface HookInterceptor {\n\tcall: (context?, ...args) => void;\n\tloop: (context?, ...args) => void;\n\ttap: (context?, tap: Tap) => void;\n\tregister: (tap: Tap) => Tap;\n\tcontext: boolean;\n}\n\ninterface HookMap {\n\tfor: (key: any) => Hook;\n\tintercept: (interceptor: HookMapInterceptor) => void;\n}\n\ninterface HookMapInterceptor {\n\tfactory: (key: any, hook: Hook) => Hook;\n}\n\ninterface Tap {\n\tname: string;\n\ttype: string;\n\tfn: Function;\n\tstage: number;\n\tcontext: boolean;\n\tbefore?: string | Array;\n}\n```\n\nProtected (only for the class containing the hook):\n\n```ts\ninterface Hook {\n\tisUsed: () => boolean;\n\tcall: (...args) => Result;\n\tpromise: (...args) => Promise<Result>;\n\tcallAsync: (...args, callback: (err, result: Result) => void) => void;\n}\n\ninterface HookMap {\n\tget: (key: any) => Hook | undefined;\n\tfor: (key: any) => Hook;\n}\n```\n\n## MultiHook\n\nA helper Hook-like class to redirect taps to multiple other hooks:\n\n```js\nconst { MultiHook } = require(\"tapable\");\n\nthis.hooks.allHooks = new MultiHook([this.hooks.hookA, this.hooks.hookB]);\n```\n", "readmeFilename": "README.md", "users": {"pwn": true, "usex": true, "samar": true, "noyobo": true, "qddegtya": true, "xueboren": true, "myjustify": true, "princetoad": true, "shuoshubao": true, "flumpus-dev": true, "andreaspizsa": true, "stone_breaker": true}}