{"_id": "isobject", "_rev": "25-d43bfd45fb3168fefca26d1eede948bd", "name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "dist-tags": {"latest": "4.0.0"}, "versions": {"0.1.0": {"name": "isobject", "description": "Is the value an object, and not an array or null?", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/isobject/blob/master/LICENSE-MIT"}], "keywords": ["docs", "documentation", "generate", "generator", "markdown", "templates", "verb"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "_id": "isobject@0.1.0", "_shasum": "e7bb3876fd85f55e6f1bdcb0b7fc20e64e315f70", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e7bb3876fd85f55e6f1bdcb0b7fc20e64e315f70", "tarball": "https://registry.npmjs.org/isobject/-/isobject-0.1.0.tgz", "integrity": "sha512-cBU/3faqIocjPO/cME1fTxXj9xLRGkeirXgZI1eZhpdwI6IAOHd3KiyrQ6wSpU/UPWsztN4bfSo5ogMms0Z3QQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmc21R0uBvckX1XaIJhakZlQqXeUeHyRjRiwqvRhFwcgIhAMu8SaQVriz4uRr9KHQ8E1I4XuWWImuExrf10Jws6Juf"}]}, "directories": {}}, "0.1.1": {"name": "isobject", "description": "Is the value an object, and not an array or null?", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/isobject/blob/master/LICENSE-MIT"}], "keywords": ["docs", "documentation", "generate", "generator", "markdown", "templates", "verb"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "_id": "isobject@0.1.1", "_shasum": "2ebefb6ec50ed1f60d8bfce607ae9513f919d142", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2ebefb6ec50ed1f60d8bfce607ae9513f919d142", "tarball": "https://registry.npmjs.org/isobject/-/isobject-0.1.1.tgz", "integrity": "sha512-qNHYIccyT86kxkBYCpVKWXF7g7On+S+hpbJVN6+zv1MwizbqH3xbwaqkQp/ShZrhDExuV2sS0IW6BNKb5WTbkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7iIIZi4E0ncxA15wNcdxw3iPkEpmW27gqDhlxn69XrAiEA/o7CSpkbwnW1hwtU3tvS1QJHaAsCbDGxuMpaFi827r0="}]}, "directories": {}}, "0.2.0": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/isobject/blob/master/LICENSE-MIT"}], "keywords": ["is", "isobject", "is-object", "object", "type", "typeof", "kind", "kindof", "value", "javascript", "check"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "_id": "isobject@0.2.0", "_shasum": "a3432192f39b910b5f02cc989487836ec70aa85e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a3432192f39b910b5f02cc989487836ec70aa85e", "tarball": "https://registry.npmjs.org/isobject/-/isobject-0.2.0.tgz", "integrity": "sha512-VaWq6XYAsbvM0wf4dyBO7WH9D7GosB7ZZlqrawI9BBiTMINBeCyqSKBa35m870MY3O4aM31pYyZi9DfGrYMJrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCD69QSE6qiBG2Ix6FqjmXSeXhDnUMLaQpqwqdr2Fn0yQIgF3Zzf9BNi2yXwm+9q3Ag5r+QHU8x7VDenUAFQyfi1cw="}]}, "directories": {}}, "1.0.0": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/isobject/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "keywords": ["is", "isobject", "is-object", "object", "type", "typeof", "kind", "kindof", "value", "javascript", "check"], "gitHead": "3bd05aa67786c02c50e363595564ca921173351d", "_id": "isobject@1.0.0", "_shasum": "97fa6e8007c8e1251276be7565ce34ae69c90083", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "97fa6e8007c8e1251276be7565ce34ae69c90083", "tarball": "https://registry.npmjs.org/isobject/-/isobject-1.0.0.tgz", "integrity": "sha512-1eU4NnUEH6qcITGA6PjfZEqGzKKDmAUVk/OLTLen8ramfHLpzIn1M9TimzhqCSNI7Yk9HyxQxWygUEiBrBvZlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3HoK0SF4EM4BLULxAmRKeJIxN2521tMNhd6mN2g39HAiBwOarwwyF+GK58bYBIuVWkZK92kvwC/BK4Ah0vRnPlpg=="}]}, "directories": {}}, "1.0.1": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/isobject/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "keywords": ["check", "function", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "kindof", "native", "object", "of", "type", "typeof", "validate", "value"], "gitHead": "1f5c1ba12c2c56b421582415c31dc4e55367f32e", "_id": "isobject@1.0.1", "_shasum": "890af315b085f3ec265acd81fc60a1a123367129", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "890af315b085f3ec265acd81fc60a1a123367129", "tarball": "https://registry.npmjs.org/isobject/-/isobject-1.0.1.tgz", "integrity": "sha512-oqTWK0mVax5mMGI1/mjlZjg+AhQsd44PDXJzdtbmfaiausTXDdoLrNHJRK5KfyE4UnlvMomGHSqTVUKEYCGS7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaQWrMgucTxfwKfMq9MJLYh2oGkEy1brUr4445Xsin3AiAFI2NRf/zII7fdWGi//rLq7rmXpr5LGWfVK4c9+YEKRQ=="}]}, "directories": {}}, "1.0.2": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/isobject/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "keywords": ["check", "function", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "kindof", "native", "object", "of", "type", "typeof", "validate", "value"], "gitHead": "0d3070262eb950e2e19c5781da8f243b629c7731", "_id": "isobject@1.0.2", "_shasum": "f0f9b8ce92dd540fa0740882e3835a2e022ec78a", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f0f9b8ce92dd540fa0740882e3835a2e022ec78a", "tarball": "https://registry.npmjs.org/isobject/-/isobject-1.0.2.tgz", "integrity": "sha512-WQQgFoML/sLgmhu9zTekYHZUJaPoa/fpVMQ8oxIuOvppzs70DxxyHZdAIjwcuuNDOVtNYsahhqtBbUvKwhRcGw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQgJ7U76OO/8PDke+pNMR+e/cnmUbwDdNBrfC1oscWgwIhAMhfaBIeKIpLCgiJQVk1xDX65hJHEiUAB1Q8pP8MVt8Y"}]}, "directories": {}}, "2.0.0": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"isarray": "0.0.1"}, "devDependencies": {"mocha": "*"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "verb": {"related": {"list": ["is-plain-object", "kind-of", "is-extendable", "is-equal-shallow", "extend-shallow", "assign-deep"]}}, "gitHead": "563423a8cd174564f2cf758358c690b0d3a5e5c2", "_id": "isobject@2.0.0", "_shasum": "208de872bd7378c2a92af9428a3f56eb91a122c4", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "208de872bd7378c2a92af9428a3f56eb91a122c4", "tarball": "https://registry.npmjs.org/isobject/-/isobject-2.0.0.tgz", "integrity": "sha512-r4CAG0OFgyZDPfDEhVOHKjegEkzIO2kJP+hhI/07kkcg6Ap9AGVh19CfXGUzpMCP4hQ0liYJQogg505amBHG7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiq8gu6DP2aZOSSxbbDSn76qgOI0f7uN8QHpdqsEy6BQIhALnShCOUan/Qma8nzVAoBBL93uSCaSqhQxQ85piy1uhP"}]}, "directories": {}}, "2.1.0": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"isarray": "1.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "verb": {"related": {"list": ["merge-deep", "extend-shallow", "is-plain-object", "kind-of"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "d693ec8d31b02b42a19b2d806407a4ecb2f9fb73", "_id": "isobject@2.1.0", "_shasum": "f065561096a3f1da2ef46272f815c840d87e0c89", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.5.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "f065561096a3f1da2ef46272f815c840d87e0c89", "tarball": "https://registry.npmjs.org/isobject/-/isobject-2.1.0.tgz", "integrity": "sha512-+OUdGJlgjOBZDfxnDjYYG6zp487z0JGNQq3cYQYg5f5hKR+syHMsaztzGeml/4kGG55CSpKSpWTY+jYGgsHLgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCQLYBwkYVmUnHdfb06sSkq5N+ra/HkYkczRTnqOT47QIhAP1pwmHPY7xiT86vsiMf507y6DKG8mFCYh9ZqpZZYmjX"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/isobject-2.1.0.tgz_1461618425262_0.8524945541284978"}, "directories": {}}, "3.0.0": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "verb": {"related": {"list": ["extend-shallow", "is-plain-object", "kind-of", "merge-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}, {"url": "https://github.com/LeSuisse"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://macwright.org"}], "gitHead": "ab3ac4c0353758af662f1f5fe070d5cd5d98eb03", "_id": "isobject@3.0.0", "_shasum": "39565217f3661789e8a0a0c080d5f7e6bc46e1a0", "_from": ".", "_npmVersion": "3.7.5", "_nodeVersion": "5.1.1", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"shasum": "39565217f3661789e8a0a0c080d5f7e6bc46e1a0", "tarball": "https://registry.npmjs.org/isobject/-/isobject-3.0.0.tgz", "integrity": "sha512-BuneTXllap3BKS9/GCd+aGI+nAKc2kPAgOwOxWdQewUK5En5mrhZe0WfDkbXQBgB1tHAuzkYmuIgH2gn2vOsHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOszh2IUAipe2r15hQN9M5i1Jgr1423rLeAg+9cBenRAiBuwHGrPyyX7xxzyvFkaPo34zMxwTuJRTXQsJyYO5Ywlw=="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/isobject-3.0.0.tgz_1480628027302_0.14119082130491734"}, "directories": {}}, "3.0.1": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"url": "https://github.com/LeSuisse"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/magnudae"}, {"name": "<PERSON>", "url": "https://macwright.org"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "files": ["index.d.ts", "index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "types": "index.d.ts", "verb": {"related": {"list": ["extend-shallow", "is-plain-object", "kind-of", "merge-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "7ad1fc405d19f144a21e2bfe947fa82801baa7aa", "_id": "isobject@3.0.1", "_shasum": "4e431e92b11a9731636aa1f9c8d1ccbcfdab78df", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.10.1", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"shasum": "4e431e92b11a9731636aa1f9c8d1ccbcfdab78df", "tarball": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICYVaKGMsOg5wW2xEd+svaCbDUxKmU3U2SHRy6ub3ciiAiBhdrdak+y7HtNgs3Z9bO14lBuF75s+CCv9vwMinH8gmw=="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isobject-3.0.1.tgz_1498846769653_0.28330610087141395"}, "directories": {}}, "4.0.0": {"name": "isobject", "description": "Returns true if the value is an object and not an array or null.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/isobject", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"url": "https://github.com/LeSuisse"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/magnudae"}, {"name": "<PERSON>", "url": "https://macwright.org"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -i index.js -o index.cjs.js -f cjs", "test": "mocha -r esm", "prepublish": "npm run build"}, "dependencies": {}, "devDependencies": {"esm": "^3.2.22", "gulp-format-md": "^0.1.9", "mocha": "^2.4.5", "rollup": "^1.10.1"}, "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "types": "index.d.ts", "verb": {"related": {"list": ["extend-shallow", "is-plain-object", "kind-of", "merge-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "f34610b161b83d771fb519ea232097d5cad9bbac", "_id": "isobject@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-S/2fF5wH8SJA/kmwr6HYhK/RI/OkhD84k8ntalo0iJjZikgq1XFvR5M8NPT1x5F7fBwCG3qHfnzeP/Vh/ZxCUA==", "shasum": "3f1c9155e73b192022a80819bacd0343711697b0", "tarball": "https://registry.npmjs.org/isobject/-/isobject-4.0.0.tgz", "fileCount": 6, "unpackedSize": 7765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxXvKCRA9TVsSAnZWagAAtMAP/11qtoygwt6lsXMsQAeO\nF7qx/wsLH/GkR+5Q5VLBIVZVGKtuwiOjEUSeGXrrPUZdS2Y5P1jU4HKwD9UA\nMrI6foccOPtjBMwLtgLkED48dkzHdQC/RNb9FrR9gB5CTar3kN5nH8dscUEU\nwoxsUqVkgRfxlmN8eM/1LG2+G0leU67VMvEsMEC0MHVLQE1VHxh5NfQ8Gwdn\nlk3MB7YIeSsxFdEhykU472Qq7srtJzcHzmfQ7h/dtLu3J8QFXif/EawlXO+0\nQbMblOtlgo9YnKuSAVd14BdUvuqLemE/B+WnEZfGLQbIBFm6eMNjyM9Z0uIZ\nRlhcQn+/OnK31ypIVhYOuS6HQzcp6B4hyC0CvhAixMC08HMkVA2jOhPEX3+6\nCfVqsE6cqulAiNPCJvIi9Sxf2YLSmNh/Fwfz8cD0rxKjFfgpmUxfXtDS5v5k\nvlEjTkXBnE/YWEjbAonVkcLv240kLA0YdxZSg9oTDnUeQVThaVnXI2mbWN2f\nkzi9ReJau/BULMDULbI38ifeo534xsVjNfPhLOn1MsXykoNTMqWtVRHuczcy\nzaYcNMRBmU8Juk7BIIx91Lhss5tjYRHWBr2ZYYzk1H3bhQb2OxigFWWP6Yff\nAgsGUKIdUcRdCFMUgcsdFOism9UCCzpLarsqC5LEdkcpWTzmclj1Lq37EQTI\n2Ree\r\n=e1wi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVzz+79/8/qQQnOw3q4ylKjKHhBpH6tu/GCM1av2uV2AiEAyF7YRbS3BTMz1cDOQktlP5FIGD0OU+7b4Lm1SsYlois="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "trysound"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/isobject_4.0.0_1556446153321_0.6551427987025762"}, "_hasShrinkwrap": false}}, "readme": "# isobject [![NPM version](https://img.shields.io/npm/v/isobject.svg?style=flat)](https://www.npmjs.com/package/isobject) [![NPM monthly downloads](https://img.shields.io/npm/dm/isobject.svg?style=flat)](https://npmjs.org/package/isobject) [![NPM total downloads](https://img.shields.io/npm/dt/isobject.svg?style=flat)](https://npmjs.org/package/isobject) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/isobject.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/isobject)\n\n> Returns true if the value is an object and not an array or null.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save isobject\n```\n\nUse [is-plain-object](https://github.com/jonschlinkert/is-plain-object) if you want only objects that are created by the `Object` constructor.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install isobject\n```\n\n## Usage\n\n```js\nimport isObject from 'isobject';\n```\n\n**True**\n\nAll of the following return `true`:\n\n```js\nisObject({});\nisObject(Object.create({}));\nisObject(Object.create(Object.prototype));\nisObject(Object.create(null));\nisObject({});\nisObject(new Foo);\nisObject(/foo/);\n```\n\n**False**\n\nAll of the following return `false`:\n\n```js\nisObject();\nisObject(function () {});\nisObject(1);\nisObject([]);\nisObject(undefined);\nisObject(null);\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [extend-shallow](https://www.npmjs.com/package/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util. | [homepage](https://github.com/jonschlinkert/extend-shallow \"Extend an object with the properties of additional objects. node.js/javascript util.\")\n* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object \"Returns true if an object was created by the `Object` constructor.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n* [merge-deep](https://www.npmjs.com/package/merge-deep): Recursively merge values in a javascript object. | [homepage](https://github.com/jonschlinkert/merge-deep \"Recursively merge values in a javascript object.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 30 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 8  | [doowb](https://github.com/doowb) |  \n| 7  | [TrySound](https://github.com/TrySound) |  \n| 3  | [onokumus](https://github.com/onokumus) |  \n| 1  | [LeSuisse](https://github.com/LeSuisse) |  \n| 1  | [tmcw](https://github.com/tmcw) |  \n| 1  | [ZhouHansen](https://github.com/ZhouHansen) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 28, 2019._", "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "trysound"}], "time": {"modified": "2023-07-12T19:12:51.165Z", "created": "2014-06-30T07:22:55.649Z", "0.1.0": "2014-06-30T07:22:55.649Z", "0.1.1": "2014-06-30T07:25:49.454Z", "0.2.0": "2014-09-22T14:28:50.115Z", "1.0.0": "2015-02-25T07:31:11.279Z", "1.0.1": "2015-07-04T22:48:05.019Z", "1.0.2": "2015-07-13T21:02:50.215Z", "2.0.0": "2015-07-21T00:26:07.119Z", "2.1.0": "2016-04-25T21:07:07.504Z", "3.0.0": "2016-12-01T21:33:49.315Z", "3.0.1": "2017-06-30T18:19:30.705Z", "4.0.0": "2019-04-28T10:09:13.577Z"}, "homepage": "https://github.com/jonschlinkert/isobject", "keywords": ["check", "is", "is-object", "isobject", "kind", "kind-of", "kindof", "native", "object", "type", "typeof", "value"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/isobject.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/isobject/issues"}, "readmeFilename": "README.md", "users": {"jonschlinkert": true, "fgribreau": true, "rocket0191": true, "flumpus-dev": true}, "license": "MIT", "contributors": [{"url": "https://github.com/LeSuisse"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/magnudae"}, {"name": "<PERSON>", "url": "https://macwright.org"}]}