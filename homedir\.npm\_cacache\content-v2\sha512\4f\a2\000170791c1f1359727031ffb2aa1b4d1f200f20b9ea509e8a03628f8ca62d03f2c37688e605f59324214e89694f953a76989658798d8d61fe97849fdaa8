{"_id": "@webassemblyjs/ieee754", "_rev": "49-8dc999e285679c394f7261fbdfb68b5a", "name": "@webassemblyjs/ieee754", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.5.8": {"name": "@webassemblyjs/ieee754", "version": "1.5.8", "_id": "@webassemblyjs/ieee754@1.5.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "29383c7172e90121613d5614d532f22c19255c3b", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.5.8.tgz", "fileCount": 3, "integrity": "sha512-r0gly5YsSJj2vITYJnS/MYT0xw7nyANBzsUtfVZyo1OHyYql96SEYKFrSEaoXKexew00TCd7JufCK5PGoF55AQ==", "signatures": [{"sig": "MEUCIBmxLAot22zrb2nDvxhDUiSXiq/PghUBlKeuos7BrFLDAiEA7z1NxEW+awnDFeaDFjk7ykeYj/HeZS/ptXPyNKYxWsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/qqCRA9TVsSAnZWagAAUaYP/37P7KlNU6wx0S5m1D+8\n1rVjSC3gYVV+FKstRmwsfdi8i+hj9veHBRqgAOwz+U90QaYOpRyyHmeuRwfZ\nky19M4om5pVGv1uQzCvNyuxgxe2q5M3cpH7N1VdFk1hqip3M8tXhyCX5U3mw\nOHBedE6fBDv6ogN0QbGEPND6RplnY5OSGVA8Ay0+g/hf1L0sOD0bA9KEdxhr\n6UmLwVvHBXEvC/9w3mgPWJjDcWlhO1ade1YPQ8lUZR4K2WliC6iFEjlIq7tE\n/3EMbQR7pyzYmO18M2LvjLU0H5A1QOgb8seDlJ/gL/8bohpcadplWbyM+wiY\nfjic7cO8jx+MqHJpQTVQHL38pLqy9VuELi2ND+hutZQ+R/zlm3lCgpS8gu0S\nsQD8lYjMLtXuq0Om7Dww3j/fHwU/p5kdOUf6VrR4279rDXjU0NdygmFJH8gO\nLEWbbfPzAvYSTC9IEGDLKjNjH3dd7DxahUoPyOPfLZdT226qz1JC1GpihRsC\n5lezN1rjnA+lVbzxp9fWFarD+Zp56e0BplFMdZPBTB61J0tIipPA88IawT1d\nyp0sfYzQyMifq90jf+IOPn+bbelzA52C4A/mAPawHpelXNqZvOL8QtegH+5e\nw8Zr/U1kARZhlkrKKqjenzkuNsWzXfkys8OoZxLO6EEiRIbqp1eq8KEqA1fR\nzcaH\r\n=KkXL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.5.8_1527511722423_0.9984228551459373", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "@webassemblyjs/ieee754", "version": "1.5.9", "_id": "@webassemblyjs/ieee754@1.5.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "846856ece040c7debd5b5645b319c26523613bcf", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.5.9.tgz", "fileCount": 3, "integrity": "sha512-mhetZBDnpV3VYqZb5Aail9X01VyIqDDZrNYdYj8bfx/PsVPG2znX90wRyVNTeqC5ylqHCgGkJ63bPaPEyINfsw==", "signatures": [{"sig": "MEQCIDYxhtoBsPnrSO9SodV+oCXNRNVFj4pxz8oMbDE6rknOAiAGSGrD+yAh7AdRq1pOBSKZvZy3a/FUc5QLa/srPGaIWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVHeCRA9TVsSAnZWagAAoFgP+wQQgdsk5YyFyjCWZDEj\nwXYjiK3HWokmS6cvVFXOYKpy2Zi1+4mkv5iLX2hjzWUfcdWtwDA3Mha1RFGq\noF6VrsVD6n3XX9d3+fboyc+u90FxZ+4s30VqnAlWxDJ0UGQ4Aj3SRXCaLVnE\ncjGJH6wjobaNoFe8QitlmeBEnnNLNyOOb7YXkQrOQYwOjhWrAYs1bXwcG1Bg\nzMUjYeyly4kFEdj56WDVdP0rYKPxiklUDMrlaf9YHMULiTwsUBtfyMSYTepK\n1l7P7EyfBi1OzymxnwkNovNtk4CUIDSsDB6CHP1FgxRY1FyqoXATjDz1YMoU\nEZci4HlpORhwiSsDGqVLuQuYoOv/MhwbScRQKCo3LXmQu84gGOpcSAjh7MOS\nRyN/OnmYWArB3zlqBMJrnaRLi+OtNpDr7zhxan5HfIYajkXw7KAUo45Z95vC\nrAybgUwfUsjiJqb6U1tsLJu+ubjlQGg1n7PWlWBAlLrNlBiSMXsTRyVxmeJx\nHFCS2ZmJpWHsHwCDlXNCr2i2HfGEtd5vyNTleGZKTbQVzyJ6FBNvPNYD8Cn+\n9ofqaQbYQmgAgFSzwvi/9n/OmG0Dzpfv58vdST7+MtCHn0Scexcus9cqRG8o\nF25vyt1ebsg+rT6dj7+rVDn9aoHVKx0qf8kgV6FdhKd3UUJnegYvAdniWiG4\nv/Sg\r\n=6UGM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.5.9_1527599582493_0.13298773485469795", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "@webassemblyjs/ieee754", "version": "1.5.10", "_id": "@webassemblyjs/ieee754@1.5.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "257cad440dd6c8a339402d31e035ba2e38e9c245", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.5.10.tgz", "fileCount": 3, "integrity": "sha512-WWlO5quQd3qOUT4wJiuodh5E1A8BfXYkOueuZZjEPL3budH5snqdWsPDieTqkBJnfCZGwRkRSn14OH4OPY1hsw==", "signatures": [{"sig": "MEYCIQDtBh3/t4kX1KeCVBoi/n9u8NYM0IezRmhR8UYZelNBPwIhAIkePkf7RwUDIiNVdWWB0bIkKitPo2z4U7OfVlGSj5sg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUgpCRA9TVsSAnZWagAAs2YP/A0vQoi1mSGBB37Nwun7\ndz8ApeB1rIOx/DlVpU7BHfFuuT4aY7RhU7lCkPHrwda/H5vqj7rXfm7NHsOH\n+BHCGBGLCJyCVhwv+ro9zVtNbU7t+pjFQuq6GleIO87XBlROF5SdF6H030tf\nk/XRZfXEHdGYIw8Pfb38BfQpgTIWpq/WwQKgo+RCJof0eRkMmat3P/wKHQ0e\n1aJLt8qsjQHQryn/yNj/5F3OwiGxS998p/iCE9PTxlC5s1h66YN1s3Vq0efK\n5t4s5kj7pdXx3hFYOchk7ACUICh02h9O8KMb5cMKEzdp1c9JR8OS03trGysK\n02/BOAOlRxJhLIfyYNSUO3iJ47Czx2AtKew0oQox2cTatSaUVcgeA/rV1l5S\n1BdMckgt3krNkXE1VmjnCG68w4nkOSK1RaChqdsnFswwK+Y7qwTDpdulHmLq\n+rUZ6MDdb5iAHnR0l0KOsvmKhqet3q6IyQ0dqmLmg8/0u7mjesZgYED1NdMJ\nFQdES0wPBFGrMRP28hWz3iz6V8QvMKjHuiJE+wLQtdRg4JjvoN7Hdb7GUle3\nysfL/bkxt9Qp4DuI4eBObtaAxMIR4S1GVf+9kYxtxmRzRZel1jmEoradWVMX\nEuVG0i91aXR3Q44gHMbCbNR/wa2uoOsIjSX1i54hdM8mihodRI1e6cM4hczE\njNrF\r\n=A5j/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.5.10_1527859240836_0.1670388831923204", "host": "s3://npm-registry-packages"}}, "1.5.11": {"name": "@webassemblyjs/ieee754", "version": "1.5.11", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.5.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "520658507f4033645d9f12742a4156a31b0c2e6d", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.5.11.tgz", "fileCount": 3, "integrity": "sha512-0B8u3Fkyvwo/Y9hfgpAcP2P64kD2bIjPPYR9JTOtcQ1ZH405miXAp/BvqlJ9aw1rMyRPZe9V3QYhReyIhL3nGg==", "signatures": [{"sig": "MEUCIQD4TiJ4AaOfFgYKRrTAQXCPByeC2Vg2RX+KGZOzP6gYCAIgdfNsXySgcWR3BWvSKcT9rPeEwdCSe/Qu93hJxisH01M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6IbCRA9TVsSAnZWagAAYhgQAI9sjUkbh2rAdTjWmXSX\nIeDeo+MZVcJSSyuh8xdB1O+uy5HshzJbJdVzHmnbL0N52h7H1HN/FX0biQjV\nlarPgmpTXFL/OCCeOP+6+wLPGbck/z2OkFBuU8edFTOX37YCPVdcUFuhyS3r\nKG5ksnybApSQdszCo08O8eWZw+AtBqL0UV8Exe1cACXLZYLS+9b59jWEefB9\n8XIc4pvOxu2rcDZWVdKYua4KkgsoULXgeOyln92uCkNOBkW72ozFQHGFNMo2\nzLWllyh9MQcA0eqkaPEFC1B86a1sT25fujnzqbpsgy8bAzc7EbTECcWNqTHF\nI0BOJnIzFc4Y1KV9+ztChgaCDOA0mxj9Lnqxsw8SMgxA408hNb6cWFLgJxrr\nizxNrrPPdYixIbgXcq9do4GyCTX71IcK/rucyvbs2wAdnmb8cou/aCkzmQFd\nQ/xsPdH857NCczjAn+I2jOcGcF0BP805s4y47KHaDkRp5B1VBOf0GmHbqlB7\nni5+RwhMYAqIIIj/a1U7SNiy/Sbab84hflvKI5tf7cXhQAhpqv3BSdjBPaKZ\nUUxCKzgd3TPADlzwIaAJzkAljL+rrnT7TVeXWLddh7uEwkr3XQS45dUtJ2I3\n6Ll4VVp8MZ1BzvfUbNkT5GaH7rLM0/GGRIANV1hhXKmk17RB7wOkiLZ1XdoT\naI8i\r\n=VGVO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.5.11_1528275483016_0.9511900850726824", "host": "s3://npm-registry-packages"}}, "1.5.12": {"name": "@webassemblyjs/ieee754", "version": "1.5.12", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.5.12", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ee9574bc558888f13097ce3e7900dff234ea19a4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.5.12.tgz", "fileCount": 4, "integrity": "sha512-F+PEv9QBzPi1ThLBouUJbuxhEr+Sy/oua1ftXFKHiaYYS5Z9tKPvK/hgCxlSdq+RY4MSG15jU2JYb/K5pkoybg==", "signatures": [{"sig": "MEQCIExqmKGb0Pcxyot1+MiW+AE8DReWlLt5UtOAnA6h6wN7AiB3hIEKZ7KXle9rlkQ4uBjiECyQDsiC71BopTeAFY89Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPn1CRA9TVsSAnZWagAA+IQQAJwrsUmPQgtfqaFXuXri\na3ppRFNXxgQqbrOVNwkV+HgiWCYsLiq8WEt87pHOr2rdczPS2N4mOfowTn8q\nFs1N6V9b8i5O3Dldly4CrCYTfFJjHeNjSJ/G625vRNBFXnPUsQp/IM89zxzB\n3ukTWM5v8XcH0m/LIZtajlZxTco4l68wRGpBfvSzttu2Av8jMVPP8bxSNrMM\ndFFySIRov6I34x3Tvi0CuHSC+qRkZgi2/XOia62gMNEIT5Bkn+t34B14LSLu\nvcs+sr0KnxcSUQcDbB5TISmHiQv6EYoPKt4ZrktoeUxs+CmR7xOUmeWr55Q2\nGvP1o7lWUZHuDRD5yb07Jw42enAjWdn2X+DFExLvWFB3IeJkQ4nscaqp/tDR\nn9/OEKf8aNA7pEXG5WNu7lsFs/tuR7bo1tz4pfqc9Am4KkJflkFB7dYThHlI\nKm9xjuv8Ygn51fI4eznq6/mab4GIZggTfYlgXXTmMS7Fp5K6IfpaHJ5sxYZb\nzbCwD8OxVeVDtwvUny/C1KAHxaIfpuhi+Fy96zhH1GkrFUgyQHcnVTLi/YCV\npVbbMCTyV+gYMGZYf6TQSBn/U/XjhAWrbEY6Vd0c3wFsC9UCyK7EJT8xuxtK\natFGUb1cJlh7ELAIY7QB4RUrbl4QvnKRQtN942uFL1jfM+0dLHVvFzxtONQA\n6CNW\r\n=FCDi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.5.12_1528363508733_0.7008721252013832", "host": "s3://npm-registry-packages"}}, "1.5.13": {"name": "@webassemblyjs/ieee754", "version": "1.5.13", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.5.13", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "573e97c8c12e4eebb316ca5fde0203ddd90b0364", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.5.13.tgz", "fileCount": 3, "integrity": "sha512-TseswvXEPpG5TCBKoLx9tT7+/GMACjC1ruo09j46ULRZWYm8XHpDWaosOjTnI7kr4SRJFzA6MWoUkAB+YCGKKg==", "signatures": [{"sig": "MEQCIBbdlDuLWNjlG/WPivL0gEsh6VK4kcVzmTquglIwmVQfAiAx5L6h2xlHMC9XZ/laAXTt3XHQwBPGAYe9Cpqr87hJmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4k7CRA9TVsSAnZWagAAJXAP/13yZVh8AlaP9AUaJsyo\nM04ndtxQ6DXOmy6mTE3pt41KbLH63UOS3ZL9UKwDc3LSkNlO18XbQVJUeew4\n6Vn+5DW7Ht1t/St1/lEzPUmLSvLlKstKxc8Pd2ba9Tk8M7OzLuY0Z/hDq9Qj\nQLQ9NkjLeirEuLn9xElqlf5ojKmRoHP5dkLnbysDuYUAcnfHDKahebKhPN6S\nLb/+vgKOmpWnn2gsgAuU4mQSy8HuF8eoATNCQI5G45LluNMaDQzsMd5K0Dkv\nI4qXWt+ngrACzn3cDdgm4aRnaLCVkzDggUYILhiwxCy0BK+ROS1qbo8Hey4y\noZTsEVPxJyQALvZPst7t5aR3Ekl+ak8QMY/q8WAd4a54g5chQK51LiZKEMbE\n3Zz7Q2LPOpjwyqhZg2u56CcnLNwNbrKwzpBp0y+Vfkbdqikkz/rKOl23xe6I\nL7/Icuf7Cv5yF8MLEIQ8Z1yLqLPqDL8+w5Ix3rHk2AuP1lY9fc8p+6CUY599\nS1VPKdpQILMmkb/Nuv06EWTuzrHZW8k+4AzgyDsOrnn7V0AWVQDmtHJd70ED\nJ9YWwMs5PMNd9th1KqNmDebgeM9h7MAr3548EmJtaXkADi8kaw1tLoMuiuD/\nINnJ4UZ1+zclzsdVYWysFoOqDD5jyYHn9s8cs25spsSyOj/tu4FXbMZ0mNHY\nsBv7\r\n=XlZo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.5.13_1530366267727_0.16682191876070718", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webassemblyjs/ieee754", "version": "1.6.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.6.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e0669b6713e1f02529b5211c94ecc3e747408692", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.6.0.tgz", "fileCount": 3, "integrity": "sha512-aaUxEQphoNvdMBVVjezpm20aZlOhXgq6GN1+EDd4gRK0kz6/c/SKBEb827vjayHN64Brpb1ba6arfX1kRehdJw==", "signatures": [{"sig": "MEQCIFF+3d61dLVdYyE5+lMcpIdeKWmn4lWY7NDBGzZRJEejAiBgaNuQX967iZqI4JmPyMS7x2IBfeszXVqzBV/3vhZk3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF+/CRA9TVsSAnZWagAA1MwQAJ+m431U30aWndnLSb/E\n1WoW/4CwltEfh53ycyT+lTRie27wTR+bvNx9sFj8M4fN2Ag85nA9/kJS9E41\nmYIZvgGHYZcL3ONXYPHNRaXFVOtF7UblM0ah9EbpojOEBcMhUIm93aemq2RM\nwtLLmZSwMiLumjrrLAvfL4Go8GI+8yLKZXbhb/d+H9ybApatd+o46VzCKTrI\noGzVmWG9wpMtFL+1w9yzD3Zu7WBDBLNhbQLJ0k58kZOFfdJMBi6sJa5fHfZQ\npcsv9LEdR7N0yzoVTpMBp/C7B0NFqG+vJnf4B9ryaVXPUFg8q5nnKKlZQzMc\nG0jitWUHHCT1N14uTQqES4sa4cNhUH92HsolZ/K9MRDCUBnrCRvn4v7SkLp1\nM2y4PGmwo7s/3qBwV+AWOpjoTyDE/3AizWQpG+apOSMwAJ0UOK+Af75r81w2\n8IECHNMSwHImAJAUnEGtYr/t7QfgKmJo8othZ4fGWAn0EW67G83iZMxLspCf\n3f8B6in+QTJmN7DFE34aEoza6Hgc8HOqPZJS6CB1tToaEsVvAjlDbsb8qq7D\nGFxw+EWgxOXw+UG6OjPNsyg78XTW4zPw5ntidvd4xpBrnpWu6gf6Gx661x+5\naqj0qc/ovQkMruQRHnFo/1TLlVK2heuEBMrcDYE4NgIbB9XbHSZKhbofNXr6\nHH2r\r\n=iwaY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.6.0_1531731903680_0.43083925884170315", "host": "s3://npm-registry-packages"}}, "1.7.0-0": {"name": "@webassemblyjs/ieee754", "version": "1.7.0-0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.0-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "82f5e08df5c9cd8af1eee733cad8cd7247f26b21", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.0-0.tgz", "fileCount": 4, "integrity": "sha512-bxfEo0k8gRt2gzYGeyUOStiP4K9mINInovlgFVANvPpBDZ62VGH1xfCopDf0Indm1J7RgLhT6fGh7fQZa3F0FA==", "signatures": [{"sig": "MEQCIB1Fl9W4gm4289vf3694aQgG+K1aOa8Tzb2O6uTQG43RAiAPx6bQQcWkIqkBz+C/J1L3+8gFVWHQphCCfr+iHjxV2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzlUCRA9TVsSAnZWagAAVRAP/3YE2GdvZJb8IJB4/XmL\nUXMaTZ6KAi5QyIpvjWfrRgr8dH/fhd1myHdMS+jiKsMX2/ipZQqKGVk9bUyF\n7mbVypt6u8V28vG8UTmgQQzUQXY9yPrW66Lc+8ahDDjeeu1PjvJ4ocg2o76n\nN/1EJhH8pfRU9AGCtXVlKiqhTdfihVoOyxQoS0gaFftUpDhTEF7HOZM9FPUd\nvGflnveiujY5k3LM14U37Ff9H+DyOrEe4tF8R9Q65EeLvW4ETXQWLHRQ5wvg\nKk+P+WlButgMCRMztVC1pGAWaKmL0/ljIWk02hNUYmCepaoAhXfKC0wySErH\nyp7AVnjcZReVcsVcyvxbQmWM7131D4+pETBmJVg42ch82CJki5Yr1QD6vDqk\nSO/zh8WvYbIKRT3c7N86uV/4v1edN7bIbnDUTIdwTG5G8ATRxIBmAJ27lYnB\n3ICgikgvhtQMDTibiILLM51U4nVBFnRZB5SedvA+scwcJ8DhfbOo4YEvuha8\nvAaTIk4+meAcGlSOYAdyuE3/Beg94TRNAAA+058y7R+AYk1gC2lkHt6rADWD\ne98HZfVT/Ww6V0bFVVwqfkI09YzF7sUftbpB6fCzEcDJbWjN9sKW64L4G197\nP7ALwhh06b4A/Ldba1EmYg0K2sV+5/PRVdAhz0m9KTEelfti64yRjhnb+PNg\nUcLg\r\n=HySx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.0-0_1531918676023_0.780487537264533", "host": "s3://npm-registry-packages"}}, "1.7.1-0": {"name": "@webassemblyjs/ieee754", "version": "1.7.1-0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.1-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "19cd25b43012a6cfea65183e94e3763c405c6a3f", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.1-0.tgz", "fileCount": 4, "integrity": "sha512-jO4k28qmIYLEiZLGBqv9orjw+oCOa0DjlHrpoPO0UsT/dkZ9ZdDAabwTTVAba03tRQEMXveH+CWQTRlw23Kd+A==", "signatures": [{"sig": "MEYCIQC4RDkt55WwaDjLmqUCA6MbzidAkwpEYYyxkyYrz+arRQIhAIvYYaK25Qn2OQsE9rUjzl2K9W5Fs+KuJq1N2FBpKqNS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzv5CRA9TVsSAnZWagAAobEP/3KYv71mT/cF477OY1KD\nFvyxJaAK5SNczRTh8h7NU0FSEVLfdeTZgxMV2PNy/lm0bA3+VKALIteQcyYN\niniQtBfYQ343Ou0pGV/aijgAK9fbvf5Sm7f5lOqCtcEw78zcYR0WfOCVdFD4\n8WDkvm1OY3LFv/1hZDJrtw+7y0CRPYkpmFfe6rdmoEPB2eD3vtgbhmsF86Mz\nEFfilGPqSW279f66KZnDzJmwiuYcBA27zzFQ+29smRM8LfXHyQxa55XeMcPd\nbgQNbCmZs8T9ZvJVQjjIBBw4Xee6GTRedT8D6Vdgbn9wIwbets2uNnhL9l4l\n0ZnG8FOCI6Pf9cZ9TQ1T0KMjoMVRwqqZiu/B9ckOr+RtreChKB6LGonF2deM\nLcDkqmIk91U2Bo37m64W5FZcSAkoDAsqJPUcy9hzxXd+d0urpnb5x+nFpP1c\nM9PWScEXLxE0wRHa6KbW3bLUpV02rTM7wRWwNH2jLE4NVyoXWp/fnZRHVzb0\ncthCf7PeErV8rgMc6Ypr6E2zKmkNntzYtvH31dJsBIAmaoyl5gEK8jCys6L+\n36jYDQVMZdrsuIeggNRpVsT6tWoJubHzleVxpOv1DwMNYINz77bjj+BuPfE5\noqTY3JAXA9zDH7sN2WO6N1sUlOi6mXgJPEyG63nLKJsH+e3dgrGttW3ClT+P\n8GCX\r\n=zczN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.1-0_1531919353789_0.9962832318488026", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webassemblyjs/ieee754", "version": "1.6.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.6.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7788b45a9b8e764da51c5e00fac470ad6d5bc38a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.6.1.tgz", "fileCount": 3, "integrity": "sha512-613DQWGulGmDLCZlJuNL0dekDvEroEGJt5aoHEsBwmgodXoENb/XV8PzRJ3HWOQqiZ+SKUpMy/RMByRMy+Q9gg==", "signatures": [{"sig": "MEYCIQDWzm/Rv5ibg5KCu6hxFGZ62MizAwMGhP7q7xUPefl+pQIhAOF+BUjluLzrYRSywfDgXAYoNeZaIZBDY3M4mYVbA2/b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0WgCRA9TVsSAnZWagAAiCYP/ie8JQycL9fEcoMlZgpM\ntc13WEmwuGYcN0GKX8+rjj1T658bE2S5hYwm6gCRpio7rwGg6pqVBEzMn6cr\nIbvtRMjI6c6VOlRnRnWPKtuQCkEyG6PO1c1I4pg5DUVDpYsp2dUQeDXaLACJ\nONsHzS6QDCXymaNKd9jbtPW8zbR6wIj1LyPLM/fhvEGYe6Rcly9PaYqDiUBY\nGDHkBf80a5D0rX6fVITVPdlxaXp7NnIa9EjN4SRdcHOkj8RyiDQyJyXixYEK\n+QE5RKwDgACZco0FmIhjsL6ERBr68x+np4UnWklvdrv1NR9pHJYWogUiie2N\nNSog0UybRjbP4D+sjPdOtlCpwFgp8f33tw/x8ONPspEn+EXJUmN9BJlMlEee\nRBd9X62GMIFmJTbnUczkEtd7YWDAh/Hzo6t+FQ569XXy/KZidhjiTZEeQVPv\n4VflsDGhEI2OV17BWtLK6/JiW7QNxvCVwCKPSiec3zM9XrEqUGm57oXyUb2Y\nYYN/E8+l2W0ormOPNZ74T3V1zzHRkJdQFHL7WKn4MxXE2y3iwlKcMmNL6S08\njYEiOVSv+YIC1FYpfXWTiAesAWuPQkF3dS1c8bMoATLFIE2ZhEhE2hUhS18t\nXQ9EQssUqukqrC2hbZVTODD7He2hIvpDL7nbUN4+om1mFg96KmZNQo9OEXdt\n962B\r\n=Wln8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.6.1_1531921824202_0.22391650473481817", "host": "s3://npm-registry-packages"}}, "1.7.0-1": {"name": "@webassemblyjs/ieee754", "version": "1.7.0-1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.0-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4e1949df72c917b47536129f672389a36b0d010f", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.0-1.tgz", "fileCount": 4, "integrity": "sha512-SVkPQMfr5xiBWtSjUTknfe2gfl9O2y8DStfUKZ7G0KI9M1hRD5MXueYpMumFHT3/ex0qJ2Y0KXTSFwm52q2X5A==", "signatures": [{"sig": "MEQCIAnWudIwHe2+YsuYZrA3DesuT8ng5zJRAlXQxIcwCFWZAiAcJzEnlxlP8q2an7VkTmvdv/nXskyGocYKhbCAPFD+hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1XSCRA9TVsSAnZWagAASf4QAKAE47hy+acm+UnZBrMV\np6Uy0j+/M2T7A14WRx+CORuNY68hrEZX9yyYlzUNfyIUz2BlJgUpKcTSwHbl\n/MqkZTcwbCgRbDmPB/6xRQH7ze9fmbsObvVcUYTIjakbUl1nbat0uK8bsSu8\nPql8m66qbQjZ+l2nDnYZR62lEyY6vgmSym+wTelwi+qpKNpIsiO9jvCO2afl\nCC1PIR1i44Z744awdZpZR9E5dlFb4RBINj8g1X7ksNz3Xqqh/+EuDCnEEqkS\ndB0aQTbLuYPtVaU+7cJ<PERSON>gaRLL9XlDPL8BYmGR4xQEgk51X/n1b8igu1CGB2m\noc7twjD4+7AelidaFaOQi+rfp3efA0HihvCRHDp4IZ0fyPOb6vCdtTlvfn19\n+WQgbBrkO8w8DYSlrW7IQlNJ5QSqQYTT7K/mutbcmOMu40Yz1kLNoS+n4pp7\na2aVXczCSIQECceZpun4csccNNVrNbvJIAOwASlkmYfOBx2MUWFpJ+J/e2uW\n7VEwfiwYtmvlv4Jr6GClV36X1qpDGp8ao+mjooZgWYQoCDEbqIObglEO3MXC\nj0pTqeqCbOugk2XZFbqYXwUSLzsIbuj8dwTRwhL668FcAml8O5RcfYqxZFxy\nUBpfNoJlQGukaB9uWYv2llim2DP2/4R04KjK6KEWfVeAgdaPqMdMpukmhGbg\n4O7o\r\n=Vg4z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.0-1_1531925969879_0.027313118739127784", "host": "s3://npm-registry-packages"}}, "1.7.0-2": {"name": "@webassemblyjs/ieee754", "version": "1.7.0-2", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.0-2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6781a2defb03a38a49c745011d0e881f6401b24b", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.0-2.tgz", "fileCount": 4, "integrity": "sha512-3HlWw9vNAGW0AklRW25PeFrwGEv49vOrzDktHY/uvgBn0SoyXDzLKviZnXbRH1HRH2UelwM9utMa2OPoz2PvKQ==", "signatures": [{"sig": "MEUCIDNX3kFN3YA8NKC3mdpss2rogvs0MbRhAm/GvgqB5NtpAiEA29DE0LYXjPZydFLVVwEtt7xQpDIu//XO1Uz9nzqN4Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1l0CRA9TVsSAnZWagAAEyUP/RFCzQk3JrDN8OBnyVFh\n0y6oEt8NXWCbM394S0pHqa8SGjIysbjHO9NwoQqay4Bg5wpq4wAH1sZU8TwE\npjbsYaq2Mlz6f+7ltAWhJPti/j6VVyDvSFf90bdWQtGHy9WlsDu3s2Ash5g7\n1FO2MnRUYLNH0XfUKWULPT7pFiYiKUWFM2YFvJXemq7G+p70ryINLnygWsDh\n+5xdBQvkireMs0iFCA/HFQ/rmoKq7xLetlG7JK8EvzEnfJLfUL8Uiv7SMHuo\nwPXlF8nhye3tKTR4j5r7OY8pOGtyJ/E9euXHupTiHKxj2RU5Psd+JP8JAgDS\nkdfdU/j0TRE9cN4lSCoQrCr/9lxDkBfeQcCP+Krf+pMhqebLmzSCNIyqPFzv\n3tNlWSfaKucT+ujixvKPbjgAiRjq47oYG6dWpDpR+ybJSJnRaXXvXCH1wKRD\nmrbMk4/PBe6Pc5QXM/CWuimrabfn6kKzopN9qi5z82nWIQnvi78v4YUZbCZJ\neLjSYnglufui7RUqz5nG9tMnyKcCU28Nm6+yGgZoPysXZDtO/zXe0Rapp7Fo\np/bRhpayUAvNGBmLOk/YvnDtrJg0dsol3MM5XsIpiPO7fNky3TZchfJk56gH\nuJKCjzf4u9eJzjcada7FfQDS8dR9sDtR/1HFUJnS68VEQKTLu2GCPd0/gOIS\nKSfR\r\n=7ffO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.0-2_1531926900359_0.06128751673950705", "host": "s3://npm-registry-packages"}}, "1.7.0-3": {"name": "@webassemblyjs/ieee754", "version": "1.7.0-3", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.0-3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "2d5b5479727f85f8aa0454d175d27c39b411ced0", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.0-3.tgz", "fileCount": 4, "integrity": "sha512-XIUMhdBBcvkIvAXW3YXr6b2uiN7bpRku38UgVpPtOXOA67eUDc/bxQfOz+vngNmH/S4hz3ZTUNYMWOtbUywwfw==", "signatures": [{"sig": "MEYCIQCoAzgSZlUfsPdsD9ullHaXwN7dzvlF63dMz2OI1IUijwIhAI+lnbnuieBGbKplh4n4pr1btamXPM1CIZSAqlail6fQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT48hCRA9TVsSAnZWagAARsoP/RfrkxIwsLMFzIRf8nsi\nYXGsAgH3nYzhoT9B1Jimw25L1vJoIP2r1P23pPM75E0HVbLz/8Pu6BhPCbZE\nXUrd8g//vDJMjhIQhMExRjE/N2rhDfL8rE6slc/fRnJtTIbiu3WN8mpswWir\nMMAUzwcBDnBrlycFGUWhWeyZhBnNu0QqAAlvcDdshOMiM2DpAQWRDSP4IXn4\nphW/2Bc7c1+78vYvpUBV2uCKRRMLx6f0x0pZKTC6ac17EEsNXqJyuxnLXEHy\ng8GIU4NTppaOEwwSWb6DzDApbfVXa5vee5V6I2+FxuUjWfHQVo3VRhjBMvus\nxfIXENIpTvv4EEhzkv0imDQeH8tGR6sZsdY8GZ4GR5u2DYurv5x/t1rssCbB\nPt9uD0GuuMVrkV/MYL08xW6ukgcSb45ziQNOBwQJXuEvBF5WhdLU7TYJKtA4\nvUF26ffKrcrLi9/ogKzT9jnUf5LKdxVwORTdf5jvpfAuUR0KU1y2P07bip2n\nKeJI/15msA6g5y/y1eYnVYQMjpswqrzw+tc2QqcAehG6A50Z0HFqbbtlLjw4\nC1y8huoEENtIXVEDzLIeM30M+/rlL9azBGnNReUjN6RSccV5bYEf6I/7lAnV\n2TRyZ7lggrfe2XZhsggUawupOt7D9iPpMTFS37DWVFXWXf5i4Z/7D7pIGYYY\nfxFT\r\n=W03w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.0-3_1531940641406_0.5858310485506619", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webassemblyjs/ieee754", "version": "1.7.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "e1518d45f3b344a69bbcda8824c8305f4d6e046c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.0.tgz", "fileCount": 4, "integrity": "sha512-hgjJtEBcBr5af24d4mfbPEPlpzJ7Wqu5fgKjyQVlrVqM8XPxOsxoP+Pw7fhIljqjnCEIL5KzGV4Tankn3EsU/A==", "signatures": [{"sig": "MEUCIQDffYyG1CrCW54da+wWWV5QZMCQXXItrtgetT6PdirizwIgOQr1NsdKyUUKepMbFjTYJ6VU7W0U7gKXbTlPdRCmVO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULWvCRA9TVsSAnZWagAAmPcP/2uGBpYPA3VC3I2be3NS\nqjbhNHQkMFiGhGcE0W1+VgLkDO6eAKUAb8aPmsJT2/PoFOU+4XIF+jd+V9Ug\n+AQigQud9Hwl6E8ha4Qs9qTCaI5je9fMRhst4exaZ5TtDv16+Q9gPYpGW10N\nTfsZwOcsW2Hi7RWhdT0lKxfTW3cT1O165y3Kjg4Rkc1N5b/2fenRN2yXnWr9\nQCA6m2IHk1B7A2fJ4XSQL2lbchUK5CxIhT97V7l+uO/guEWB6/oGhjJjqB1o\n4xAzETEmSgRJW1135uUAjSMmgqMWTVwDC9Q4skgd1dcjhQi3IniXpV0qROYB\nON7p9BXs2wiY2K2un1PY78vfzYS6ttU1DS1PBR3HTBvpNAOI1VnmUbsiZLOc\nT7kWLqWIBpfFrHi4zDwgxEVx5B+soW0H4QuZWchFkFeSY1p0+jSYXftVztJz\nS9YBVx4nnQAICPhy/25zFhEQltDTIC34oiDzO/42liyvTRZJl7LN0qDezsZq\nMt7euB/PEbvz04nzZiwjZLFqi2JkeyQfxG2xvgUXKvbuezw3xZj9A1CIhjjZ\nFkzfIjiH9rEo9cZ8vgUMISgtP4MTj/DiuFjV8O8a7DVrziKmWIyuRKyZw8Hj\nSIsjQUkup8hGgong1vl0+XQHpWLk4xzCoebsnas4IfVARJKgrN9B6AYqaIVF\nerXB\r\n=tQ8R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ieee754": "^1.1.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.0_1532016047554_0.6919544448529968", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@webassemblyjs/ieee754", "version": "1.7.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "940264485d2666caf0909206c593c23009b3cc17", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.1.tgz", "fileCount": 4, "integrity": "sha512-en0D+rGKnOztYBOEi287GYofDvoSPXESmGuYi041t9Jm0oU/+xuswfWoGyK713ZxP/zsKLsLZ0BKBercvcdJ6A==", "signatures": [{"sig": "MEQCIFi9nBneg2Aj9+ZaJ495ISWuGN0W7ppv5BYLZbLYzks/AiAGGoQJD+eERVNGu78Wag5XgOEcKQxNNdxI3jcGeOWYTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULzqCRA9TVsSAnZWagAAehkQAJsZ9vBidJte5cVpb0Dp\nyAi+k1AIK+9KbSlO+ZaeRm2VSiUvp3MEmmY34llQRnlfeT+wvtXT3MRM/xxh\ngilbu9Jn89gP4Tru1+gLeGk5L3Mi3U1IIu0KLg+yU3wTrw6MX/qaA854LCFk\nIzNiLnSdALM2nYm2Yp4NqCDX28Ix4VJ6ijnUAYp9jTxnRkh7Z5pxh5040JtK\n5KP9+5Px2kDJNrVxu5YRKaYSu7XH9jYi1qZQPNdPc746YJi0V/A2k6V4XbQN\nJukgWUpqWRptL534gjU1RIB3eOvG7u6YjT9jdfVRHHwCHjczviRzo/LlkW5J\n88PXVBTLwXqAHFlOZ04F40ShIKj6qQQIjqf40chvyNVbiIpQqNjIe15acO3q\nCYlYXZ8aEUK4gRwqGwZviWOBytys07jjs6n2APH7eXGYaAUConhwfN6g3wzX\nsC0QYZ45nBtpLkY/iwz/+uJCwitdshp3omIEKktRtEw4UGlSrinXHiAniLa2\nnlE7ca6qXfdlt4i9p34UMJJD1dUi3MFj4Nku6l0wQ+n3/KAum3NSfOYg5Imv\nneHJJXEkHIsWk6hn1pSbDNaRL9h8Fug7WTbS5uGxwCunQwOuMsUPyYfx5xZE\nm0E0k/ggFi6lV5Cjv1VxmprXpcmIxMf9tn3F5B9T5N4VYqLCK57xn+Bp6UMw\nPPR3\r\n=+4bF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.1_1532017898626_0.2697456123506763", "host": "s3://npm-registry-packages"}}, "1.7.2-0": {"name": "@webassemblyjs/ieee754", "version": "1.7.2-0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.2-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5b874452e8588c6200de9b86cb06d5ab829cd68c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.2-0.tgz", "fileCount": 4, "integrity": "sha512-q9jH4ZdQm+i3trbAgYHJ1Jjj8B8V6XHzIiOCnX7vx2c/+BbOcIu3iH4ON8MwbDjCv1NgZk1VE3WrAY4/iGF/1A==", "signatures": [{"sig": "MEQCIAkImWjtxzSvYWQ+oHjXSbeUWBaHAtT1onyWToi4lmmAAiAqF14wcgG0Fkjyg1oaAXH3XVGRUzMY3QW7PY/YzFP4LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNPiCRA9TVsSAnZWagAARkwQAIwYVAAx4hpF5hEB7S5Z\nDzk1nTEvhRanCnVla7ks59p9B8PETAuIa9gnSRM8HhlF2SjNwU5bGbgXusZu\nZl9xiHu6lp6sMbX91VZ10HUpdg3p++4DPygoY0z1P+CYdpqREIMeiT6u+o6s\n/oHcqR0TmAJzcD93jlbtAo24eaBzVlrwezsY1SCXjVNv741B6VcH5dzdAx/E\nEYEIPgXoVdbkTRXzRFkrLokCIKtNz20KQwcssNpbOw4X/Hm9ax/QIGoufnZe\nYGEuJNlmhMCj6subk6/b0hdBiVsvLW2f6SM3kB2oCW3irBeqlgfJKrzLyLnn\nlj4/9WQ8GIex7J2t9BAQQc31KQ/kwOU0hcfJgVxGuF9ZvkpsrV4xV90EehFC\nbuVlS1eSmZd3HiSzx6KK2llUAK86FtvLbepT1wMAAbDsZ7cBRB1t+nQPvQEK\nCgazi/Iiyiyfk2PrVvIY08xqIzaLRGD9UDS2Z6sdP37t1jz96fBF2g9RZg2T\nJCbJbDAFBMzaNQwdhQ+ZTrQRaIK8xDT39kPpuV8B93xUp7YEsuS6C5a9EsK5\nkpNYfJsUObL6zbY/rf5J/qnaKkfvm8Q1oJk9rwpgY8LrKtlo0SLnJ5kIJWyS\nR2AZKwL3YpR6XwB1zBRDjpYhYkPA9W1MgdUaie7DsrVfP8jrgUz214v7Nret\nE5eA\r\n=2Sgb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/buffer": "^5.2.0", "@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.2-0_1532023778667_0.9914040493996636", "host": "s3://npm-registry-packages"}}, "1.7.2-1": {"name": "@webassemblyjs/ieee754", "version": "1.7.2-1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.2-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "5e4deac07ebe34640aabba019c4f366bf78eb62d", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.2-1.tgz", "fileCount": 4, "integrity": "sha512-J86jgCK1JsuOlsUwOLutCeGm2P54HILd2VXrnEy+Z19r7BCduHkPwJF/mTgP3SEqoHIMbbLTDyQ9T8ww2JIGyw==", "signatures": [{"sig": "MEUCIDNIAt/i2e0AWxyt3GgeHKSSPNiX8CKKY2kDBL2wY7XgAiEA90EKUG3/dUz4cjamdfpRfh35lfyOuwpd4kInSSckUIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNdZCRA9TVsSAnZWagAA1kMQAIxzjdPQs5RhuWGmel1Y\nt4mxCgFXi7KaHczS8AIsrPBLXRRcfu+N3WLwjqO/oqgjLa/sMuupWYeVxkQX\nGroJ5bC9pbaobyZEn1zZhh4K2MRASZrhAWf6mVQCnpw1z/sLJe9f62pg2DPI\n72ZRe2Qj9sCi3rcVVaZO6nhhj1/KOTgrR5txQX5eVQjkG9o4lAJm55AMe8vd\noHR2FWkuy6+YMN6T8QbJ1CfsJRSFHXeYDNEhlTVvEYTpWyV1+DFVkohau5Mg\nFEHvKZsF+CWC/ujIfOWUrlTPohcL3sMHI933CgIF4tPTqOnlTTLn5PKQZ19c\n6aIaHHrTGGeym4KiScFGHjkTFttx+rQbXl06K0tjGEIUGqwhCLOYE6AwJ8Wd\nHTAVJybXPq6derAf78GtjgO0Cr08KXiRwzaF93REEBkJ4z7V3AEPXmfd12D/\ngFVvMuDpoxtU9pwxvISVrUukb7ZuIBOacXsGUrhObO4OE20ufjRtH44t4hrZ\nxZhhIdehjvDxaiAdUKdhtr1Y9EyisF6G0eya2D5dYvgtZFxldg7RbrtSf+2Z\nSJG52uMB0G6ngE7EGo91PIvvi14D63vdt+jZ3OLUk4NrVr7wuJudUOneI0gB\nniRWdKFuNla9D4Cyg8Vgsj7B8Z8uKaGG+dUJueCGGJtQThht/paLSm7piePj\nrkq0\r\n=0rna\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/buffer": "^5.2.2", "@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.2-1_1532024665331_0.5713599538627241", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "@webassemblyjs/ieee754", "version": "1.7.2", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6c1b10454b61482ebc58631960fdec0ef73aa702", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.2.tgz", "fileCount": 4, "integrity": "sha512-0Y3R2eZEEbXLrBnoOpoxGOqSti22yNKHBK6SCo0i6VJv4Eg03xYzE9PT61hZ6ZjSyc5ze+JQquOhjvEQ/L2rAw==", "signatures": [{"sig": "MEUCIQDHmip6M8nPv6AK54moosKn1hGSRZM4eIlcG0qYXeCJlgIgLtcsmCy7Fbuk/PEqlnll+Ko4dkWhPptSesZMXrRFsWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNl4CRA9TVsSAnZWagAAe0UQAJBZ+7t6YolxJBZoUi+T\nO8RdAJR2gROPiahSP0JiW0W3x3MxDztn4sfFQtrs/ubCMiQ9XAnqKbkx0oyK\nXoIi9n8AlZp8GPYMlN1lapv/w3ELVg6UOXKZ9vGfFwjrBZCeYchfPSQyqe3B\naF+LiCbSlhCrhR4gfgX91sV0G1QDFquy3oYkGhzN64Zp+xW/k7p2UXvcNsIE\n6nIo71uhdnXWykcPIkDTOMNVS5YhruxXLXGLn1zfn45z2hXl3wJI3DszAOj3\npqSTfAIFB8AhHpZqjSiVzKsDmc0qHJRBJEvSkLKsA+pyefvvqe23GrZ+0deJ\nqpc7+Devpbc2MYY+UD/PipZv0HNEMdR+hSRZvcr5ja43wC4Yx74T3C7B44Zu\nfX2odbP3tFzD2yqdoebmLjBdf8tBOQ2ORktAOSIUFeDtxMBo6pKn/IkH6NTL\n8NEcsx8LWcTuRo8J5iAjbSu4CRKx4Ls2AYXqafhDGnYRFvNTdUIw84ri/P2f\nRhrhjAMJWi9meTbNQLwjfqvDtlQql3cP/WZ9h+X6qAWBbmDN+v11pOBOF1zt\n+BJchftV0NPWZdacBsvOX7kBAPRzsRxMEKoIL/2opR5Gu6EZXKISiOAgqmqw\nujea8uDRxmDKJto0Kwl3L4GlZsQdM8nrKsJ45WC3ZjSvNYr7XVB08wtTWuSM\n3nNb\r\n=85I3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/buffer": "^5.2.2", "@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.2_1532025208898_0.30341917778333594", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "@webassemblyjs/ieee754", "version": "1.7.3", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "21f00d64e108fac37ff72d40af3d63cde2b23cb2", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.3.tgz", "fileCount": 4, "integrity": "sha512-7b5rrV+TRt1DXur57glBbANeqk6OsYJWpGBqWADj39HzPrkEtADxySjXtR6wtFbudloIRMeDq9/brrE/bMLupw==", "signatures": [{"sig": "MEYCIQCjUfLZ3Miwyl3IwOnd8z9IX5WcBirKD9maTqDdZGFOigIhALFVHsbNDWm/OBjxKjwCSpm8g0PDsHPgC9NzJDm6SQde", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2CCRA9TVsSAnZWagAADr8P/i6myZ+UTxB0LwW3hdZy\nrZSQPLDofsShPO/WkKOFmHK1aiGEYlLsfZlG4H2llE9LU9YXecp8APIafMpz\nUegAkiTCJdxT2iS3F666kr/0feEOm0NYuhn2o9m3A4jMBji4MYCDWfYoTw9H\nrYMSeE8cNVjTbQ61ivrC8RawYkfRfUiCsNc2fqHxLFL3trsjfjAZG0LjBlHN\nJYJFgEX8sB3mEoOPZom/IElR8EYfPV/v67QEMfIrpwzbVeJ2L5nAP69MoYnT\nqXszAr7HfC3h6h60tIAMHXd1gEv1nrHiIFbQtX9psw8gqOG05t4tpOFDH0Em\nSAHTWIUhP9Ip+R6otwfYyY5TKwoh1kq0itWKByyAsXHgs+ZrsJKcrN6SnUro\niAJQ3GWze44iwR9vNrCvUWGLKgpFS/psyyBanwNxO1mBW4nd2NxDhSudjg4A\nnAxnGyd3fjLHByMS4FBL7B0e6VxMh7teE06UciDS1n2Fkcd7n5jUxpDXnH1z\nPOYulgBPRUjg3fGrZFo8lNMhUk6J7WX1ie+AcDd/BdMp7Bgj/bwdR0elYLfd\nEYZ6r7HrYQejoRpkPmgvBKYvSWN8lPA/u573eK80W69hbaeW5LT3dIi475oo\nj3Se5QXMA5gx8/nbIp/vQZyhiwH3B4KGQfXHeyvWkiRd8HkBAydM+7Zq7gBv\nZARr\r\n=geFi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/buffer": "^5.2.2", "@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.3_1532329346739_0.4051369598363097", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "@webassemblyjs/ieee754", "version": "1.7.4", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b58c92c364a954a027d7415adc1bf10dd74d7ac5", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.4.tgz", "fileCount": 4, "integrity": "sha512-SpD9i0y5gfcn3+6/UtGc2vVuQ7OWkHHqx/s61JS8ttvjVOCVLK280sU40HFAEm1CAlAvTvDxTVAZVwZ4U8G2nA==", "signatures": [{"sig": "MEUCIQCy/xYkF8N5LANUqTVepqQrbrRA2QBJ3iRCwnYJf1AeawIgKOLa/HymZ4pwrabFIpSh4nX41L8wFcqznzWkGRbFY64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs74CRA9TVsSAnZWagAAT30QAJL04GUxhiaioGEhy1ub\n4Bd35Dpj6ix+uM3m12wnCW/BxDVMU7+2y50o8cojGQszY4kKutYAdGUVNro+\nI+m24su1SoXsKYkHsTl69tTwxrXA1K+2G5Ib98eYt2+rmSt4j9KJhPFdA06W\nHVFipiUCbvjwibODSrphw66lzvcLD2yqIdGxSmGLgs0fjzCWYP97KqJC7I9m\nMtl2QitunkS386gIjl4nLunaGWdVxO3en5QMNDXzOAX/OyrNe8Tqrm7IIrOn\nUeB+rpzvSFi2KaR2M0wTlaStngiGFuyUQ6vBJQwwDvoxvWSvqXbNceeYPyXe\nUQR4QxIUGCzm0XlgvKZ7Eqy8kmJBZgotdgJstKbuFRscGvU6ZaNuAnQRHL7L\nRDai+QcsfPufOWdWM5B7zmFL5x9oHYxEcT9KTs2CdQZQdr+n+aoarO0KgDWE\nqRVypQWfrtx6beqGV105HJ11yHRLoWF08SI+eHoUynz1fa+MNEh2xPOIkh1o\npL+I9OQiCUlF+m3h7SdRtnGjlPTVG+gg7TWRLjT/3Vz1qQSDtBcfdTqZQjLl\n2gEzrxwZZ29XBlLxDfWuM9DT8JbZr+cgvZONtU4tby6fNfytCGRllpCsoCPY\nhlL6bWMjxbkavj398z+gcBhm5wIjT/1+IBmbRcV15ssEStlLuoUUzUKitxIe\nk39H\r\n=Svma\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/buffer": "^5.2.2", "@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.4_1532415736065_0.1956776890608498", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "@webassemblyjs/ieee754", "version": "1.7.5", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "a4f5bb301422500e4cc8109c47e62214ab78356c", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.5.tgz", "fileCount": 4, "integrity": "sha512-2QWnzNL575aNl+jjwYBg0q8EfGFnjzkBJAS4UvEdEd2httubk7Thduko18PnVtQT+oqJo2KwcqC+yHFGkQsVzw==", "signatures": [{"sig": "MEUCIQDqAhakmQjsCkH6Lki7iVAo7KraLKKlKQezxR9cYHdjaAIgGkfVkqE5N0AZ4AP65ajL7ruruyAiPYGhPXpNpOdaG40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdY/yCRA9TVsSAnZWagAAPFoP+QCHND2kZ/Jai0suD5e7\n9FVjOtIP+39Lw4hafPhh5WpjHkflmYA75TTfNrUSogW7qNHOp0LJ3f391rOE\nOE07jmym4uGKmSuGekJy0rdWttjIo8urgSzHSyJ2uUFK+YJ7gnkyFvpxiS1X\n93vQTWcJND27ieL/Rb1qPPbCWc2LrJxCj9bq84ILvmpv61eV4/yZ9bPK5qZt\neDr5ptA+CjSWb7T+vgwcm5tZcD6LxlY1mnsDFDC9jSKmbC+utub8BKE1Iytn\n0L0wQnqKZ+Tv/fAvo+IpjYm6pHKUCqD7/HZq3CQ5UpNrublrWqRgDik+LIH+\nm5fG7DqFxMoFnrgqX1806xdF52e4D2DEzUCUM6eoni9ZWpvSrnZsyYe1+PhG\nCtDU16oBkYmW2u7hCyFg2Ct2TxbPOoDI6yGyl/Dhr4HNyTFE58V5pZyXg2mX\ne1ZGWUXigVyzfwr78XhCWhwETAxz+bEyZ5olXAHui5F3ta4D4SPeyFmSrXu7\n/rW9flfF7bHtXPN6ywRFWQZo74gPsGlG9Q5mR+gI0MZAf15GwqkdBxeoWfXu\nh0v6ZsXKW/aYtvzEQa2zb4ecgM814qM3QQpFecKCHR8FwJhTG41Dk+hp64VA\ngsFUgVCRd+qJ+dcL5+ZDPfrT42azmbBdliEf+a16WZieFoqGB++tY4X5sZ26\nZkij\r\n=Njqy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.5_1534431217784_0.5920377741763785", "host": "s3://npm-registry-packages"}}, "1.7.6": {"name": "@webassemblyjs/ieee754", "version": "1.7.6", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c34fc058f2f831fae0632a8bb9803cf2d3462eb1", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.6.tgz", "fileCount": 4, "integrity": "sha512-V4cIp0ruyw+hawUHwQLn6o2mFEw4t50tk530oKsYXQhEzKR+xNGDxs/SFFuyTO7X3NzEu4usA3w5jzhl2RYyzQ==", "signatures": [{"sig": "MEUCIEel3wN6Z4P5NoyWFQ3P5d6EuK0YPAMgZi9vVEqbzt3VAiEAi0h4U7bk/NNDeqoz+bkDYNYH1FhdLNTRVTQYQ86tM5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblnzwCRA9TVsSAnZWagAA59gP/2kcodrC6XS5uxR7WpDI\nU1OPe3ObCt8nwsD1pRDyrOveAuqzjx1qTJuQdTMLO9LXxy4ZkvyDTNNl0Jpu\nVw8IpHBkunI7V1tKG3iWk7+Q+tRIP5QiJ4gJ2eWnqAOMoiFBOkZ1iS1vT2fp\nncqICTQETojH+4cZxYa3Do+w9WMMtCJKs84V4WWUGa938iqdX0iupvc3KLCW\nosIts+tz7GwqioF7g+MK1NsJZcLPnbj3SHVJx25Moi8BxRhE4RbHB2V26DIP\nrV2O8f9sQwlM5oKFbsix8D7v/80jySaMVVXTdz7ZJKFS6gnFhEnDnP/rG0lq\nIuZiSgjxY2pIwo7H5ud7VP3DsDokjOXn1dNL4h4Dp/BACxRVqTuGljviplqO\nrkTgvsdtYKTXtA9i5Gc6pSTgPjdbCfWlHZGPz4ASRJEDC6U7fh/u1OsuVBAt\n01MOeXkv6msftT1IuntGbbmAjFhDF6QUrpdzg9cP0Id18O9qIFMaptmBqZax\nbKJD9UmRYKJrwuGiS3NYcB7TjB25OIvgvBqteDNdcIbX8ip+O61uHTuXu+C5\nanimH8OxIkK/uO5LfH/wZUCkXLuEiDrQ+mcFvhWu7J8snOKrhx3BL4LfIpGy\n0UjRKtc/URLXBR7h/tnOR6sldFBCPTDDsLpaeyetd2ryX09yNCurGKQBAt4a\n2YKq\r\n=c/G8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.6_1536589037086_0.867595376400724", "host": "s3://npm-registry-packages"}}, "1.7.7": {"name": "@webassemblyjs/ieee754", "version": "1.7.7", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1d891f11ba6a70855b8d8713cc7e86359237afb5", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.7.tgz", "fileCount": 4, "integrity": "sha512-X8zuvnCet6Pa8n3SCdR9dMFSpatw2Wv4oqn1vMQrP7lsbIEp436wa6S+Twq20571MezEDeSlDYOY8TpWtetZpw==", "signatures": [{"sig": "MEYCIQCYMCn37JsqN+2xPX9aHnWptoIISgXt7xo5i1RPM51spAIhAL4SC/E6Nzixb0At2T4LxLydgGP4Ehs8OuriwDirwbdn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeEBCRA9TVsSAnZWagAAEogP/jxHR+ItuxEe2HCTYyw6\naLKO6VY9V7c/Q1WU/R1FskDogZEUkpd/aRQgze4TjizhEtyUKFemoVzXy/0G\nRPvSVyS9UP0R9ecTnXxKiTv8C+FlmroZOmqxzvppDCMLAtjERnGIL7lOB+RY\nlS1fwJuS/8jsNPrdr0qV1IZn2fBwVn9QYxjccOccHsIT3O1VD/GFBLQ55wOi\nBoCsKpYIvmBctBUrXrZeQ4w+KC4bzzYPElnMPrOMeAWCMG7NDJHBnGZCxo/H\nVxTxu4m6ekSNc6eB5Ent3bSW15ljoFXd7CGr7VO51HjUFqjCpgE0YoMhqhRG\nv8f4qJF7IOPvx0aGa8cDWEx1kd1hgz47WkdTgYH+on5NqlzTUzW29MGOIkh9\nskbkP/FagW/ZNTmqHcUg+NomHClBHnnaShKl7646zX4tVcIzTjCRc6GGlMsT\nwrAj8mbbIlaedEAh1QPvFNM2vDetLKVPbdQkbVDgA/Z8YASE3ZgMkMxemuKk\n5ZMrWoyQ/8ElNYD0tX5OlgzdCciaqry/EZ74gx7VKD90/8AR2lBlILogK51L\nS2+YpmR1Mike3IoSNFIms69RkyaCKE96ZdotPXJWsQS6fC/K/nfIAypstX8/\nwr6MCKd5aGSYkE1roJpzq7XbPE1JH0ldKqlq9bge4xYokm9PYYdFgvr/N6W3\nkn1A\r\n=nQd3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "5.5.1", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.7_1537335552833_0.2949782597510642", "host": "s3://npm-registry-packages"}}, "1.7.8": {"name": "@webassemblyjs/ieee754", "version": "1.7.8", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1f37974b13cb486a9237e73ce04cac7a2f1265ed", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.8.tgz", "fileCount": 5, "integrity": "sha512-tOarWChdG1a3y1yqCX0JMDKzrat5tQe4pV6K/TX19BcXsBLYxFQOL1DEDa5KG9syeyvCrvZ+i1+Mv1ExngvktQ==", "signatures": [{"sig": "MEUCIQC/zyX+tYe9T7uh2JWFfzMOf0GxsOLfPjraMO26e+2aGAIgck3CrrEn2lV3bft+3eUiSiTrCOPoX848eFMLVNEsJsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ+1CRA9TVsSAnZWagAAwaUP/35n/LgOgVtN3BjBQHyO\nmk8c/B3UP+cb2mgpNv2Inmy3pWuqAwU6yf3wg+jFat6fkJFLfp4KG+YeoQma\nW1MsWIUgxloFXAnQz+8wAUCo0kHEAQ5kdAkbdAMPgwEd9opeWfxCZ+gjluWc\njcM8iV/ARXM4GOj7/8d9hQZ0WolKmq92WhxrpOWcwqR/8Qq4FsMTcWmKaxpt\ncDuODGagLj67rPgP3mFR9qMepwql+ElWlpKa4LG1YoGsGSIcC04/4hgQ6mza\nTQXs04OLB+AVmladO7dsMNgeZ1lxxz0eV1wl2XLZ2EwD4hDRcJ+quAwAjNF3\n6q52GSfnb3ahFVd3rpAKcqQIBDqR6VS7BM4zsM3oWis9l5TUAE067TnmlYkJ\nfxrYk+Pf/0XZJ8RC5HVgx/dvX1W4c72upVBUizum9RTCZE53L+SVCtefdV6c\nkz6o0q8K1krEsvGWj413XUv4Qpw1berAgMLrD76Xmamow1VxrihhIK7YdbaB\nqnySQGGDJyi1IRCVphkRRUHKZlRRQCw88l443D7dFZnLiTVPa6weAJQN0ing\nop51OtFBahTsw9oo5FfEhp6HSCr0YKwjrYwDcA1zTgaWiIreCExZtMoYDI+b\nIblwkDfZhyWcEGztFbCwAZIbniNXtWi6MRLueFWQ8zUzhFNnKlZrp+efkkfn\nICf5\r\n=Hgot\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "43b83b600939b19c48c3c27a1733592c493c4386", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.8_1537515445283_0.9619314861676156", "host": "s3://npm-registry-packages"}}, "1.7.9": {"name": "@webassemblyjs/ieee754", "version": "1.7.9", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "faface0be17559a7f602162e1b54cbf6f4171234", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.9.tgz", "fileCount": 5, "integrity": "sha512-13xTw1x7+TqvMvwUOoQAtwuNCebcbs9Xo2QLNU9dMWeRXnVuRYxaSn9qcwetlj1lTTqQgGSJNka4VGsiEdh/2Q==", "signatures": [{"sig": "MEYCIQD4X7bocH0ZI3P7v708kHlCMQ/z9WrZtibKAbwDWo8nDgIhAKL88sCOisHb/mW8FuibdwnOdOg/ZhKKw2/Eun+9hp6v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgDCRA9TVsSAnZWagAASwYP/1lf2VCEW5fDggjOw6eE\n9XVa3IYCW7li79nefDkoWXPWyxmfB9IdLZiEV4EciVz5m6runtEO74vx+bsZ\nfF8Ob6vqyMt8WdFqFDgvf48gPvJi+ccqzFKrefUBjz8ZzohrgzxpUPmdqrg+\n0JSdzI7H7DQJPH97kO+WOMuc5wlz0oECKEAbRhQruGl+CL4gHGj/E+KNsoUr\noqfKd7FT0y4J4vfjKfHFjMWNAl90CFEiA4J+hWq/Txz3Np4gIiEHEuyLtWJN\nIJHbfkOGHpT97Hnim2R27Ilwu0TxmtrPd3OlI4Ih5xYp2MWg6GELYUQoipPz\nuRk3I0oiX2fSERByko4Xp/84Q8C5oxeH3PhTlXm99HL4D+dx1Tr48oiX1j8y\n9ze2Szd7ACU1uItdHzQBCNV31b59L1xBf0lubqwHv1Fr6Ees2+rHBE6VhQW9\nJXBbTTsaOaPTswFJbweoPATcMDxuzSfA3ENlxrf3uOcBf/AA8FqijWrdRb+P\njaf2QJvO2fmECQRuJ6gKaNCnaWa0UTETUy3L2ieestp3w9Gm/utiVbbhcvM1\nDIjq8kgK1vClzgRsEzx0NXNccop9rjYO9KxD9aOM+LnXHNpeU7zyJz0aqmGZ\nEjrSXaNy4E8oRyRylJ7U23Nhiqag/p5o0Kw28WXFkWBa9foAvTTcyyEyIG/t\nNo+2\r\n=Coxq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6c5bd6e21d734967e12bb7b7aaa38c80697b3b68", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.9_1539880962649_0.44178164620888594", "host": "s3://npm-registry-packages"}}, "1.7.10": {"name": "@webassemblyjs/ieee754", "version": "1.7.10", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "62c1728b7ef0f66ef8221e2966a0afd75db430df", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.10.tgz", "fileCount": 5, "integrity": "sha512-HRcWcY+YWt4+s/CvQn+vnSPfRaD4KkuzQFt5MNaELXXHSjelHlSEA8ZcqT69q0GTIuLWZ6JaoKar4yWHVpZHsQ==", "signatures": [{"sig": "MEUCIQCe1ILJB/ZEByY9RzC+v22vmHvLcCsZH/YXM1MD1yJI+QIgRmBFM0RBe4+BuMtV0aYzB3H/kJbZ6I2dzsfnUDHVIrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzs5DCRA9TVsSAnZWagAAnLsP/04GuDq+R/X0irtH5rl3\n/lDUOlyapc8ZunFgLCanyNsuFaVFmy/drN7HaHoyKVSlwegEQRk9ncWoR8Dg\nGVgoBaC7tExuaho9HbIW3RZoAZh2qS6fYuWLe5Pv+Jfzw/AvIkKpvjexbEQf\ntyWw+IWFPMbapsB2FkSHPj2HK1GLlt/dxA2A9Ts8K6WpF2s3c7eMh1CzFcr5\n9ViMfdgpn8Kz9T4G7DR4TnZB3mg3s01bblgxJaZuCNpt63d6hN7571gyQ/F5\n6xrEIIILy/GqxRvb0UAzix2Bf/fUtdNMuzvGbuPBD1znA7c/+UWNVNuMHhLL\ndA43pXiCqKbF1BqL6Wplirrsjd5hwp2MCNg5atx0wnKy173GoqRBMjjDY/cU\n2bRAWAV0nxJDSOIGzaGkji8W4jbK7CSmmT+5v1L7wFQw4cVos8UnmlifMQQu\n0iaGKfjv1M+jgwzBLWZuGK7yNrlRyuyoQ/NLaDnVJPkPqMIoBSUySgZbBly/\nvk/MEHEhBLtXnbjkboFo5s7K3nuczPdGUmdER6WtPFKZmvo9g3GqkH+i/uGJ\n6N4M1nwsUQRa7fJlPER7k3StYY+yDkQ0kGVrXxe0cER96b6cbFBrg/GDRSIp\nHmN6yLmQoRyPew3aQ/TaSb5JNT80SinotHwkIlCOqssnNlwSvUytaOKLpJWK\nWoCA\r\n=1LG6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f558c6c047187f24a2200ab04104f173de226794", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.10_1540279874503_0.19637246379478102", "host": "s3://npm-registry-packages"}}, "1.7.11": {"name": "@webassemblyjs/ieee754", "version": "1.7.11", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.7.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c95839eb63757a31880aaec7b6512d4191ac640b", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.7.11.tgz", "fileCount": 5, "integrity": "sha512-Mmqx/cS68K1tSrvRLtaV/Lp3NZWzXtOHUW2IvDvl2sihAwJh4ACE0eL6A8FvMyDG9abes3saB6dMimLOs+HMoQ==", "signatures": [{"sig": "MEYCIQCMJncIZXujGhZ8AY4q4WB7ZDe+moVRJejsnUyjgd0OGAIhAP2ZJlBTxgvzTx8nAzSoAc1tcRQB4MLm1o1gMe1E+kVu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxSCRA9TVsSAnZWagAAeucP/1TmlDZ7wneHlyAVqZQy\nJ6bz51SFMKyN5dj7MI0XZhJ89IprhQk5ISxWg6md8vYGSHLL28pU+w//2P3r\ntvTIW9F5iwvMyftrww3QpHI9DI918rAyRXaFO9JELq/jSKppQRiNu0izl68n\n+fgTWQBkkFxieVWwGYSa34IB8w0Bw6IBJHQUf5BbPO04Q+o4zM+Sm7JNTl6C\nK1ukyk1HvNhhq3uKujoKUUnsd60HRqIfoqUyBtWujyh3lchHLSG4EAYgYK67\nt6jQAk6lRIlKXF44PvFD8onJ+peCM+LIXeKsdNZXIQc36vdDiRHtuBtULwyO\n3kqhuqgaEfJO/EJvsGWQp7osrujIbg2GlBRucY1ufXmXRXW5eEJ08FzMWVhj\nAfolsTjKODS4NPz8RSHCUkPBJsAuWuc4u2ZQLN+eTM6q/9TdXbgrVecoc8Af\nLhpYU+eQouUNkE9r6GFIPPmt3pL1YDtNul6eriLB54NnFcW2EBXEAEZFHME7\nxdrmjLKp8CGchfbQOBUw8uz3ta7mg6700DnbQpZvu220NTiONhg4p3njhF4Q\naGV+O2+V3ZausPKZVV/jQgbQEPlNbWsoegcAZHOjEsFVEq3xhEv8aYMCO6aZ\nCa3CyGioEtOMB3iSsbCzKYmRgzU5TEhlffuL1J1NFeQj/moTo5vrN0pfAhLr\nxn5T\r\n=JMH6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.7.11_1540922449688_0.874123653534095", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@webassemblyjs/ieee754", "version": "1.8.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.8.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "29de0e46f7de193d9699308a9bb2a8ad00563d3e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.0.tgz", "fileCount": 5, "integrity": "sha512-gWwbgDFu23bT2oDRLh1cui1ZXWdP4IfR00iH8FNr6Yann6l9DDey5SjZvzK+z6hWhCvD+5LSyZO3wnDPu0V/8A==", "signatures": [{"sig": "MEUCIH8GtIu/g50oUxbbuvFRlrH4yOlnj4Fed8q/fhcO3c/gAiEAzp8VTYkKD5yr7gJVXuLT+qK7TFTlfDTvO4gxgojL3Xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2YeCRA9TVsSAnZWagAARkIP/1j2mh+s21tYY6hh4/Wy\nDHdIt7lsF8zNhzUfrHGw6L/BITHhferBj+TvvRe2QmHuimUXHcK9cXhPPUbl\n2niOaNTY5isSWMHeI/Jy9Tjr1LM502/S4NGxB50Q+GydsT5vW6DCqngkfjU2\nynuQEhRm7njTVPmgusR6HrUM3R4s8MRTV2KHiDBDE4vJXZ4sk5Tu13j6syOY\niJVIWoKpewBJQqDUiA75B5Zdt3WtXtEv+qiRM9xJBw/6BWY+ECb03eSwyip6\n+U5xzxvqgEaytmU5ZwaSh1To9M7A2/UHj7XV8Cq/nrnmmo1ImB+QhK225BkS\nakZhJZbR05cBkvE5eFr30Pxc8yY0a7H/bEXm59HYz9THQ24doDqqlBnzNRPE\ngDJqd0T85FWfa6PyEmRsvAjDgvT+O1LX3xjJJqzS4T0rpn1d1trV+A4aLx3f\nxWjphspMp7nkkQgT4jNlZSnSnSvRIIn1HWGcUlikVvZC7GnZ5l52+20cBAG+\nCVXPqhUKaQqq3se6Qh8hKrBELBN9weX6lmbQ6Gh9gnfKPK6lWNbt+3VrjxB1\nVbIqh+5ankb7NAFx3pguKptzCvqxuLBqnMoRx0JZYX+TbBHzoiOxziVP7or6\nAma1aZZUO7XVYPwaupEXhHto04QjN4RfhRZqz4Bb+dmzZIBrbWBDfHATm8PW\n1WSn\r\n=L1JE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "8b2d1afa793ea81f20ec63416134c201e39694eb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.8.0_1544513053310_0.0704828933858459", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "@webassemblyjs/ieee754", "version": "1.8.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.8.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1a4640739debc2266c9f6478ce6504647ac4cf7e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.1.tgz", "fileCount": 5, "integrity": "sha512-Pq3IQR3uay+rFC0qIgg6xvD+uu0a9QEWDCRihHuU9wmOBFW3Lda/ObnO0HjC7XUJ8A9h4xExaa1w5TsSk+DxIQ==", "signatures": [{"sig": "MEUCIH3C5RAzlxmFgTsj04QqCEOUNK9cc2f1DBAkdrTrWfCDAiEAxDnK1iHhEVrbalzXhhfO/5dsySx3KHse7Cd6lVMeZTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZf4CRA9TVsSAnZWagAA9IwQAJ5+vGnfRDNGmwkyO8pg\nco1XRHwnMb8zFBelrFnBOBdeSIzKK4VP324isZnNAg51oD0q8cEqcWP6H8Zn\ni46JIo91xOi52n2OsHPVsvEHT+DOBcIlgx68VcZQRimxhys/HcLzmDLqu+zq\np/8CJyX5z+lAHurtQZxZxE2GT/De+sMc6dTmSNZrG6zatPl+ZCdX23vaxIRe\ncvqqx6ucZvOAs3lb/4JA9WbbIPWT9iifbT/ihJrIjWXGsV0pb0S9DmMqjIcy\nNTvqF0wCotXTsLCroXDDocC75RCP5WcQJWh2W6TuKgEIjNCBFu/qtlNKXw4J\nruCHaUgz/XuAP1k8K3csrBZdF5mvyTEliybpa3sthzEdhhpvNuXRVd1DnjtV\nPQZOVBO8Se+dDlPijABf95QL+GkD3ISku+fa3JF8ScgfUvAub7YFqcbtYux6\nr6OPw+uf12BbkQZ/7VCDAp6TBSwJRUsnIN1YLiQXYzQt6CzKCX55zI3+Vr0Y\n9U2mMZC9pv0BY4x0gp9hZCdPQrt0HuP5sOlTq+U+OshmIJSzuRTpY64WWKq/\nBswDvuSsDfOwmirUFiva0PXhKz2sRFRwRdzsFVV6sAPC0bAX5W1AjcY79p3o\n8294oGwu4YJYa1b7HHzaGaLef0ciGoBETDFy2/df6RRr8w5AHrNfkQNf4qyt\nwKXe\r\n=h2kp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "a2f42245e9b597e3541e0f697253449d60fc4d79", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.8.1_1547540471633_0.3646453929635025", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "@webassemblyjs/ieee754", "version": "1.8.2", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.8.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "43f8969f90e08b70101f35c93df07afaca5c9d5a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.2.tgz", "fileCount": 5, "integrity": "sha512-v9RtqGJ+z8UweiRh47DheXVtV0d/o9sQfXzAX1/1n/nw5G85yEQJdHcmwiRdu+SXmqlZQeymsnmve2oianzW4g==", "signatures": [{"sig": "MEQCIDBQ1Ob60QIAlU2g601wNn99cJYt42AIAyrGSW4Ex21qAiBbe+UYSHn/YnLzSDyjZmBUELCgPgvYCxXJXevUCJSq4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWPCRA9TVsSAnZWagAAkqMP/AiMGegG9SDLKJgGHMy9\nbWb3/Q+GblHwqswMk4k0evMOW/MKjwXzFFTHD7GwwZwQuqyU7SkzskNU6HFu\noHPq6+RjnsIrjgX9favT0NLecWGMSiwjJlfcfVWPq6ULkcer4zbO9VX5KXIC\nVIF+r6ybEhaP/3d7TjGEKoBua1HQEelK/0L95HgKNLIlWMCtQdUtI9sJRjz6\nRXY1kZo+l2pQetcNn0TBZjlmzVt7gQygu6wDcF42J/KWd01/2opK+9nXtm2D\nynA2PwTHudhzimk4zayNDExyHjjWD0wWjIToN6e0/g2NMoVouruwi73TZEJz\ngG0f1DnZpQTUxRxGtMkkL2NhlHiHCJzn7VABjfZ3t2l3s5QvdIj+q5GNmpLM\nlC9BB32wt0AnZcrrKn+JtArXZ4EYo+K/AXsoGIqZCyVEeoF2N/PfFCdU/Tp3\n4tZ9X+O+CPINmmd3yynNf9FZk1+ZLdj+RKVZmHosAMJjr8d0pt/JzTxDuxgK\n0bwtdOtoSP4mXEDPr1Pctn5gfmyMclpuyd4fqMN8+UMqNXVhWgtMw+T+C0mP\nugoJYTC0ojiAB3yN31h0QwcAc/TP1iJ3mVmRje+w/4RF+wCAgbuSBAyIgETo\npTb3SyAJrfmE/vyDJYuxxJzOZCCFp/XUxNAsEU9Nug5px2pLQiaZha1IKYiP\nwS0L\r\n=mFZC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "02af462b507aa7a24f5d3201178434b181bcdabb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.8.2_1550161294557_0.6783562667450234", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "@webassemblyjs/ieee754", "version": "1.8.3", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.8.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0a89355b1f6c9d08d0605c2acbc2a6fe3141f5b4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.3.tgz", "fileCount": 5, "integrity": "sha512-UD4HuLU99hjIvWz1pD68b52qsepWQlYCxDYVFJQfHh3BHyeAyAlBJ+QzLR1nnS5J6hAzjki3I3AoJeobNNSZlg==", "signatures": [{"sig": "MEUCIBEkkmoUhEj4Ky27bH08pB4BLFuFVqRjGOvWNFXoyreLAiEA3Fq6JkShHGWAwFY9O7pUg8Pn63kI0HLeb6vMfvW+wBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam6wCRA9TVsSAnZWagAAQp8P/AsaaEz/EysGCMxcQKgJ\nzzO/CA9BJf5ItZU2LhjZfl6fHryJZj5CJprpC+jYGe+JVcfJsNILCt59Cenr\n7fr1QuQ0FRSt4O/2ahZs3eBiqSrp2D1WIX/8h4flh0tpWEVzCfbLdgthAYd1\ntX1S6jY9vU2zn27xOOl6nGB0dBPFO5jmecGNS/wgbItZRWLHrDnsGf0mzwGg\nGNZ++CghAJm9pEAFM03BkxbVenqwY9NKqXZgaJg4PGTi7VyBHuCXg1iq8BLX\ncTA69+D6ZjvInsVhFs63tA2v0h9Z575hyUx4WlkwBboERWJ5diaWzV5TC63J\nlGjBRKcxZ77FauMzruYL72nA/aAMHSz5kzcRpui8ImyjTfmvkgMsBGXMr4Mg\n/0/Xz2iF6oZWMY7LSrYKoB5Nzc76qU+L0/jTd9me+ISikZ1YM9Q2Er+jUbiD\nIk4IR+/ccvcFti746/nbhTrVy3CK68Sf80HUpPgzpADwKI9rIdfAGJVzOh5x\nR2H2NxZ2QzRf+OSCni+QkUei+m1Nw+HZTrCOqWDv1mmFbjnZ9zn1CvtZa7/w\nNETkCeuT6tYFai4sTpSoHPAvtKeCOilRH3hZfM8ccwVV39xkRhiBqHR8NX9/\nmDV88RzTQROhmyKWd0qO5cXTqqlap9ytk7tXVRzf/SCb8d50qPu0aUzqHx90\nY4i1\r\n=3ZwZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "e482c7ec291d61fc46e42c93d3b8ec7517b629e1", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.8.3_1550479023750_0.827529905236629", "host": "s3://npm-registry-packages"}}, "1.8.4": {"name": "@webassemblyjs/ieee754", "version": "1.8.4", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.8.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b5dbe4acc8cb4a3d2ab40388274a603ecdea0301", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.4.tgz", "fileCount": 5, "integrity": "sha512-jp90pcPqGp3Stda+otuUtRax4k6P3kd90D1mBSLqm+J59KnRCr/NQeaXMRfPIKWVFgfi7tQIMe0Oxm98Ou/RRg==", "signatures": [{"sig": "MEUCICC6kPPtgejDrgm7M/Xzw62Fme5Pplg5l368n1jjiGhtAiEA6yCp/eNZxPL9CvNAQ3NDjq160LgB/QcUGygkM9ZtEh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDxrCRA9TVsSAnZWagAA2IAQAJlcp8hkad0XcnXdX9tK\nd2pi5kQECnywwtlSgVv7cgMEut2QGP7YM3+4fKjF4Yk6lK5b8AHEKXyvtX1S\nOdNDb4tJRDcTyFHkw1hiiolLMMJs1fTmUJwKR1cfNJ0KWjj4aExQUiA+BKN2\nkV4bTdVpIpumEPmEdYiFKTaagArqf910fe+cE8BshxYJlGWh3Xws9I6fnzmm\nNFirPQGXgPcJb3Qwjt5A6IV+gDz4XiR6z3jPW4U1zXh78sLFx2G+ar2DQY6F\n/6ROlMyoG/z3efEfMf6MdB/a5attNU6wkqeAqKU6OtixyUYa8RTBWko/ILwU\nNO5PMT33S/cyuGwlcD55unc67dFimJ8wFbwDYUIr6DtiQqT7BoAsZnD//xlG\ng4og3WBRghNGju9Ty6bTH4R5VIkQpyzdIuyTklVJaqszuqMI/KNacoU/sDTo\nr5oTZAO2xnMiwDaOwXfFuLVjhB3HePM/ZlNXJI/JP6WTJ5sPEANspO4mRLh3\n5X9R4r4p+rwVuGhP30qtq43+dI2VlJuGnskwjAvo/r/5yOIEAzOsM12OxNAp\nYRyc+UCIBNVzBppJDl7/bpA4g6SbxPokr1IVpMlLm9aD1+UzY8UnMmzUKgBz\nxTu4pcJfuTGkAlazSKaiX0zFKhN0TexTebtLvGofAprsecJiu3yAFPXGxSJ3\nqRFR\r\n=mB7h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0154b989cc9b41c695724a361b3aa6fa19c5b032", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.8.4_1550597227349_0.12960220004435863", "host": "s3://npm-registry-packages"}}, "1.8.5": {"name": "@webassemblyjs/ieee754", "version": "1.8.5", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.8.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "712329dbef240f36bf57bd2f7b8fb9bf4154421e", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.8.5.tgz", "fileCount": 5, "integrity": "sha512-aaCvQYrvKbY/n6wKHb/ylAJr27GglahUO89CcGXMItrOBqRarUMxWLJgxm9PJNuKULwN5n1csT9bYoMeZOGF3g==", "signatures": [{"sig": "MEUCIQCYt2lMJ6UZxe/sgV3o7WHJFxQYrjCIndXNbs8fJ1x+BgIgEkXT05d2qwEzZ6uycTYB1s4n1qh0uFpp4vufgXMfMn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnUlCRA9TVsSAnZWagAA4FQP/RgU5My/KJvJrkU9RzR9\n/CCFqultIF1jIx2Is72nAS7/jgkx2LKovviuLbMyrOqdHIFWe+s96D9ZQ4fL\nMV+Jo9Dehon0glG6EGj1Q2a34DyE1eU6vkkyZ6Hx+l7MHh1mVVmD3upJ8iUJ\nBffxFOpNOlY0WO9eymlXv1/c9kN/0kOkKgM62KOywqsh38OYz4He5ie66TnU\nyu9P6wwxSUeP3uMKLNkm2CYj0L3DftNrrFue8f/0e5eLBD7O+rufZFQApSOv\nmZNbPr4MxKRCFpNpM0GnRinvLzq/3ZJ0ega1CIIdyurMvxmCEArWiZdKORGV\nOB/1eTDZSA2flwj4LKIdshF6WWP3MAczO7pPduKG4q+w82+MIBk9fNuaNEgD\nUf2OouwfA7mpKVJvPjhilWNvQLLWENJ4KUJsD0lEm8ykK4hy9L00Wi/BaSiz\nwTXw92eNXtq56tBqcCD9N3BIEv7PGay/ccoacL8n5ypnj/0vH40QxPqnhDrL\nQl8RXwGwvgRHJYaFNClymiAeo89Km6u2fHNRZgdPCZArc9EV1RcKr1J8oWJp\nnNZ8EnCTaD3nsPmflGacMLlRwsnMkzSnWMqV0NuS93d0HuggSh89xyBGS22Z\ncxSpy7FWl0wjUteyYhQ92cNt+rWdfmvv942cC9lVQ21BBPx20puwhq+i6fYq\nqh28\r\n=jee6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "description": "IEEE754 decoder and encoder", "directories": {}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.8.5_1551004964681_0.7991923071958331", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@webassemblyjs/ieee754", "version": "1.9.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.9.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "15c7a0fbaae83fb26143bbacf6d6df1702ad39e4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz", "fileCount": 5, "integrity": "sha512-dcX8JuYU/gvymzIHc9DgxTzUUTLexWwt8uCTWP3otys596io0L5aW02Gb1RjYpx2+0Jus1h4ZFqjla7umFniTg==", "signatures": [{"sig": "MEUCIQDH+3ZwfUxKnWkcRKdpSTRh/nQsL4VsuWn8hDOCDlzH4wIgQedy6bUOWOGjphUxIZdYbhmANL1OqiOAJozTvPEGCwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgeYCRA9TVsSAnZWagAAQQAP/2lUw3mupL6JxzqQyj8p\nVrdZwAWMImYV6H1jyiQH5QLzX0kOcPE5cSCEuaanU/hD5nHACbPcwUU5+xvY\n<PERSON>jmy+xa2HZSOYiXG5OWXpET93m1Na4UhHkH0937RW2SYozPNm9//BS7xNCnQ\n8mZ8J6ef+E8CaVtOCp03EfZm9lUUfOqLdnXjWrergtih88NMCz7CIgSCb2+g\nT7ufbQHfrO0sYo1F8280mYoVhyTW9TYkqoRBjfsYWDCoFseyPqez41v1Yf4k\ngw4K2AJI4WWCYRPk/wV+imi1SkwCNzSvmTkpKWeHz864E1yeJVoeMU6SwxoR\nKuMfadVSMKfI33EylDsOz4i8RMBXuAEpIgjWi21DZ+PEHRpOMwuB3U7PUxi/\nD/PltyZH4Dliwba1Led0e4yW2+Mh5FMzO9Lz5mM4beOB/9o/tjVAwP6WK4vY\nmxAgcAB88ZzvE9CtaW11hU4tBW0im/Vl4jn+f7CKk3lNNY1gKJ5fnX5TVViE\nEfboRovJ1dad8eBS3Zk27ZWtUymltmpHYej5rmH6g8MCXavX01rIQWoqq+op\nCVBptKOjavxERUiRIGkv4qYmgeEuV6T6BNSOEIirUaVGJpIpq4obfmeMsnjE\nEINMm8EjSTvrPdNHfUgRbleT00BRgxK8QeE2+BYKUoc6c26orz/TqdS4L3da\nL9xM\r\n=WNEQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.3.1+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.9.0_1580599191896_0.8861029474346858", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@webassemblyjs/ieee754", "version": "1.9.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.9.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3b715871ca7d75784717cf9ceca9d7b81374b8af", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.9.1.tgz", "fileCount": 5, "integrity": "sha512-EvTG9M78zP1MmkBpUjGQHZc26DzPGZSLIPxYHCjQsBMo60Qy2W34qf8z0exRDtxBbRIoiKa5dFyWer/7r1aaSQ==", "signatures": [{"sig": "MEUCIAzQC9S2bdexhqF3trLQ9lDT70elxRWj3N0uL9Asx0qwAiEAhmlIIoTYddzar8HNZ1JNVK6k55jKIs4P69pIRZAksCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNoXCRA9TVsSAnZWagAA3SIP/0I/h2bEWW9RDJpp3+Ti\ns8H4I3vj76ZKUzGGmg1d2eG4U0Ge38guVrcYO8f0/GCFiVTQ84IkM9d0MZF8\n/ryepk2hZOLR1Apefp1HN5H4uBpW4cMXNYG9kT89FTsUYUGTrstJwTfKcVyd\nFLWbctn9iNuw+BNCzlTc9NC8KELu+8uqwAolsLIjjBVm/igOaSxMguJoceOR\nW3WLv7XrhbuSnROMHYd3OCc/L4cMRVG8PkA4EmGB/TWpiSkz+Rwu5D9clut/\nmkGkKEN01WfIxbtcDbcjdOia4KFFW5OTs9/OpyKJzj8ZBS2puyAF5FiLcTMc\nOV24Ea44WeretYs3MWnGBvSSDILpkxZb8jBTdDVgqmoU298HR25ORL5VxIg7\nn9iVwfrU8+QrIjw03R4lUEeMA3C2i3N2p3iuyhbN3EAdG8+6ZFU4Cyl/UN/1\nAruxGaNuGnxQl41YhiAZrfY50F2r2yc7aa7lAaAdByqTFfBYMkP43ybPs7+L\nLORG7JEtZ19MMXZBmDUzKYTVc/KuXDvjCt6L3BiKUkPVxatqHciVtz+CZC99\npEfKzPEcU/T6XImRxn3YhUwUx/uB0vbHmpOaKn72/90I5Vj1uI1IqaKUjyDl\nKCglVMB5O54EoYnb/Ir59mwBIlKE9qsbOE89m2mIt6qhsqsumHgCvv02grOS\n81rR\r\n=96Ft\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "697a5f63048049e9ecb3205d789c1e80eaadf478", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.9.1_1601755670658_0.10199400781538892", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "@webassemblyjs/ieee754", "version": "1.10.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.10.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d7240508b4e3210533ef612513b848ba499edff0", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.10.0.tgz", "fileCount": 5, "integrity": "sha512-qfP4qsC8WvwxRV8Vpw0lXT2X7am0OBE+YfhkjYW2ymmqXOAZGzRo5xwNdOpjm5SeTBC7KWgZn7M0TmaKozguCw==", "signatures": [{"sig": "MEUCIQDKntHJoaOojQVqKGww5MsejfpWlgHRld8Qu4eqGzQqtwIgLZUCZ2Ugbrb+Yr8y78j23UIPg1hjrj3umkQWXspvazo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zQrCRA9TVsSAnZWagAAh+8QAJyH2c4nkKA5zLS1mOq+\nM/YL99eYuJMz5jIb8jj4Alla4PNJaTf5fn0XXVdQyrjwYGssahsveC2jVu/L\nbLSdbULEBczZj4K82oghADZPDnA/ssygwW4r/nkfdliyAcwvN2GenV4aC3P8\nFErLjzwRTjViZutdr/HquZEYMgWCCTS87DTBda9DRjAjLZTMJHpdpr7QFDtu\nu6pY4qCSXGkzr8fV4SQIg3z93/CFicU2PVIlbthbuk3l6sUxaWXpyTzs3E9Z\nB0+LjLDdEe3mNItTs+NWMpMIy2pQzrYVgvMdymPHAqzM/eD5iSbST4lvcPmn\nhmtRbEMp4NwJu4QzLTd16Igtsky+tUGaW3mZiqEZLkhnO23USZEik2HORtZo\n8THFsihZ+4sb8NC7yihdwYM/wH6ZdHdaUPlIHzCRfIxVo111Gg+SDBJhcDO2\n+3yoNwrTp79DHku5CIK2Uj5RyaU2wx+5zKPuvsp5d+2Lelbjx7D0pSWhyyzA\naTQXeGoGF24f3ZmvqlZPap1Ga9IQvPOfSAliwbp79pPnzuZsCYae9udTApYX\nY37oX8IXlfgJNlkxcuY1kliM4l+NGOkzQdYzur+bpLhNIucliLLTaVU65J13\nFkpD1O6aoHEHv9XdtKtm18m7VPbEeydpO+FSlzwsrpABHwC1JD9itf5LD76B\nYQ3w\r\n=l/a2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "d00b899ece0242275e2475e75232ae18ddd03aeb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.10.0_1610036267469_0.9359352433193511", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "@webassemblyjs/ieee754", "version": "1.10.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.10.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "6ecfed23763bd0b872f04ea1300322e054ebbed4", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.10.1.tgz", "fileCount": 5, "integrity": "sha512-5Wf/4Dhk9bVtiVIR2LLM2I/hcVwtEtuXBf7IZNMBIyHZv0s5DnnBgb3ckGduvd/oKBgKWiFKRNN89HEZc2llIg==", "signatures": [{"sig": "MEYCIQCvA6j+4ANRi1a0ePyQNhOnM360Udg0wSi8WyPiJ6vlwQIhAO0RBpBqB3b1CA8K8SyjRE20KlRKFWtqm2fcIlQmXTl6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqTCRA9TVsSAnZWagAAuF4P/22qZwxf7ZZhc5IO7yRK\ncUmZaEfTh/XdyuSAmJ6OFQpf3DjHd3GgRrYA0Z/rDrDBvQmuGVw83Nbc+sG4\nhIdr8+L1gd8OsX7i84tlbLIKvFjbmJclWOp9KUiqftyJVGZ/XwBVxLrxImsZ\neJhL1OOQovxbb+O6KqnyyLkJOFeFULPYNMjRD16BGuqWsNtp59UOkVlgeIpR\nf/DYzJi4Eokv9aiSyZKGkAzIM4BKUckam5xs5Vjt/AtbUMXWJuNYqeKVTSe5\nv0jQyr0o9TKbFfT3jL3nshEkxOHshfmWnHTJ997dAsHh9NATbI7lHMc0AHyb\np5postDePh5aiRcOjsX2KkO3i0bTSSnLS5jkPZyYDxdDhOMwBglykgeg3IOG\nF3I7basGoLvRUNzeKRyrjus7mgHMhX1A/fuCgTKUXoggY17SpYA4c9mDK6nK\n5XBVNh8QBtyaPFP+9RvZLqeck5UnOglardhEFPigdTp/G3Shlu0WRm7C/F/H\nF/G28dH5XtzU8Qlo9WN/g8ucE6JDBs19pIf+xC92YjZt0AVF7VZvJuYsU03K\nnJJI94VJALoZKMKNezJ3Z09NFBNtAQvAHuYWCqV37kaQCtlDWk2g5o+EO9hU\nc1Cl5DEnGvPLKbi62+ztTmot+5ul1LrIWnu4+H9mIdP5Qt9J6wf021sTn78G\nelQ1\r\n=3oK1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f723f2cdd9bfccb5e199962dd8c5c09bdb0faca4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.10.1_1610037906817_0.5287420113485306", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "@webassemblyjs/ieee754", "version": "1.11.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.11.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "46975d583f9828f5d094ac210e219441c4e6f5cf", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.0.tgz", "fileCount": 5, "integrity": "sha512-KXzOqpcYQwAfeQ6WbF6HXo+0udBNmw0iXDmEK5sFlmQdmND+tr773Ti8/5T/M6Tl/413ArSJErATd8In3B+WBA==", "signatures": [{"sig": "MEUCIQDaGcD1m1zWtiyQMLGtFYRyAUHsK/wEG4ViC1Cs1GhZmAIgNeT6pGI+EIJJk9LjPGYLulBQiYAaIWigf+/EOokIMmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907QCRA9TVsSAnZWagAABLMP/iCYctjzp+P+ZnJBPtJ1\ndhhBRzQkDp3rV00EIyBJh7GSt2So6cfBI305WVqzuPweTokDqGsLeUaiwlAO\nOw6m4eTRNsm+MnwNiTHltM2EPwAlSETT0YN/657CEH3C0pg5VwaiDDOT8rWJ\nqWRGjwOtTl2n61e5cZ/FjiHAmsWnqRpcZlw32KSF7sokEck8qNS/TSs2iRFC\nFMf0Obnvg1nycHxlIu3gD2gcnnajsTkR5GOFr3nCFelWbIPAGo5INX22r2F6\ngVQvnRRzVmECbXNwMnG5lJBPlUwRXZYruVVYoDfSH4dl0aM+RgrJhSab7JqT\nfOD71JXNHFAelQlkrJHCj/W819ApVNuJyre9dGFDyEqbUgrNxabrdhdEn0HR\njoSVToxZDYlSFYjyJ8tk1TrE248mv8usHRwtTdTw6NV5rMmHDGBEtjwQ8PTt\nD1lReONH8PN10Z67aeRNhWoPckCC/5RtVZFryVsiqN95bqFGO81BD+dgXNE7\nIUFHVWmsQu4q9QTIXgWvz64j4yZg0QOrRokZ/NCy5ZUIhO1bUrBXxSlH3III\nblOoaWA2Nz4DsdT+uxy+wDB8GI94vagStUQ09EvR2RFDhQESvsv0kQNNu7m/\nJshmwY6WhcGFoMAkYIL3CWXgKEn099ak2Sn/cVL06dAxJ8q6c/tNgd03Wz6r\nXZnN\r\n=zmMI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "2646d3b7d79bba66c4a5930c52ae99a30a9767db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.11.0_1610043087852_0.005976272770576108", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "@webassemblyjs/ieee754", "version": "1.11.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.11.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "963929e9bbd05709e7e12243a099180812992614", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.1.tgz", "fileCount": 5, "integrity": "sha512-hJ87QIPtAMKbFq6CGTkZYJivEwZDbQUgYd3qKSadTNOhVY7p+gfP6Sr0lLRVTaG1JjFj+r3YchoqRYxNH3M0GQ==", "signatures": [{"sig": "MEUCIQDyIbsY5Pmkxfr5ycvjwOkGPMT9qdfcY/QDCiq8z4ikLwIgU50KfvOBeIs+lT2Dxj5Wa4hKRDeqUFdFW93nhNIH8ZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sEqCRA9TVsSAnZWagAA92EP/0va7Co2hI+MN6n+8ABl\n55fg8l/VydEN6SjRQog4XHpFMm/D9QzZ34tp2U7OGEJbd1X2UiybpJhKehcK\nS32TAyJB/Y7uXnyCeRf/mqQLVpGbHXTUxcMi32z8LRviGuGXbW2lr1WT3Cw2\nxgk6Y8OTA/bmWEkJ3qBvFh2ETgB0056D/niFvrHrILQ8WqD3IqPYWSKic2ge\n0FIjIKTd9oxV1AsXtz3EGrPvccdo4lkDTuFk9MSce9oTpQ3UgyOcBgccTA7z\neNqj4RyBpO4TOSU11AnzqFVblw5u1L4P+qYQRMhX8nMioCPeI2tVbMRJc06x\n/pQvv9GdftD5XYbFqFvPoyB1cEJCGlsVjaCT/zgFXEAYsgJW/Fgn7+Q2zPox\n6fypN6a17/CZNDa6G68sEQ0qkoFd+COq94xSADtG0aL3BFC96cDHNBbJNc+U\nafJ/2tmdI5CEAzg+nWTHAsxdTTy42MleRiIvpi8cg4JZWx0zHLPDrV5O9mAr\nfKijRpHmYbvo8fbnF5k4IMeGRtLe14u8iCb0bCwcJs6+4FbBGQmwjvMKF5tj\n3T+gt9fz9EtKXRQ/GMQuYgdPQjEfoJvajCZqGjQbK2AJ5Pm3R2Knd5KTW2QV\nM3JnefFrbnYul3c8aNlUxLxaMvA6YYr5bTFro8mQ0wYhQ9Vkz7hsCx3oWF6s\nOIEh\r\n=vStS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.11.1_1625473322589_0.11858317177581212", "host": "s3://npm-registry-packages"}}, "1.11.4": {"name": "@webassemblyjs/ieee754", "version": "1.11.4", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.11.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "2a040cd62469090d3781d1c89b5fd2f993bdc5d9", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.4.tgz", "fileCount": 5, "integrity": "sha512-eld0ZbsBPU/HGJXtwvOWxEIm3fJmYj2Py098N0ukDF02Dp0f+4Sj7QZllTcWh9XtS4ysgAywhk2cbPId2VroLg==", "signatures": [{"sig": "MEUCIQCGmqxFzpu9jarQNGriiluQxBNiUlWjGY3RWUyg4U8SPwIgeNiFELkeGx7XXsXWzjbYNyHBXB44l4XJ2i5nUKiv39U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/Rg/9H2MyW4S3eFBvPmof7A3CI/Xxv4Kk6ZIb6E1xmmk94xK5pckV\r\nh8xxMUS9PMX9IiwoPS6XPnoydg3JB0l/7jllIBRdTUE2gsiTizn5eHXyLCBT\r\n+pH4UfqTxa2femX8iZo/zlNwHNuPClvo817Wzc6pyr+485eWa2m+DWnjvLLu\r\nPdtkcwq0zeA00RqBE+xTJLBnEie0pGFBpayX7rLsadGNhdePqsLySeB7CX+F\r\nJWE/E5rJdT3MegGrAW+/X0hnTGPx+zTOjv2Golqlvn9tw+NDIIJ9iSlWcYEq\r\n8xE4dtzcZ6Idzdxq+GLOpjGTGUD+MQhM0M+y89gS7wcgTVEuUaeUaCLwNR8K\r\nXWjl7JKJ/RJtKHVMBpOxeUBSbY1UUp9u0cgEHuOeFz5Q1vkhs6BQAyzbCPjB\r\nMO8snSOKM9+qXTcATVlM1jYDdaNOsSpoRUzDVvQ6WTHlo0acGXSDzfQdvrXu\r\nP3Wt5mD21Xpnt+PD7QWjQp3G2yFLXCNEhAzMWvVNlYVsONOQb6CwkAMXxnTT\r\nN2rc8ikMeGasMXy26XwxoEFebWCWl5UEUTOrpmfCOLlfA8R94/Xo/t2aXKY1\r\n8lG4H8quRuYY83aFiFBXnV6jTsf2cIumLKXfqa5Am4M4GI0+szWdmnihnqFu\r\n16I3Q4rWV1QIPkGCiXFa7lMYhp/HTI97pJ4=\r\n=0UgB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "5fd2425602b752576bbe8089c343d5d70ebc861c", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v16.13.0+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.11.4_1656762773629_0.9787893370906584", "host": "s3://npm-registry-packages"}}, "1.11.5": {"name": "@webassemblyjs/ieee754", "version": "1.11.5", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.11.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "b2db1b33ce9c91e34236194c2b5cba9b25ca9d60", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.5.tgz", "fileCount": 3, "integrity": "sha512-37aGq6qVL8A8oPbPrSGMBcp38YZFXcHfiROflJn9jxSdSMMM5dS5P/9e2/TpaJuhE+wFrbukN2WI6Hw9MH5acg==", "signatures": [{"sig": "MEUCIAgp0iHsuCEMIMiOTiofZRdBMulgK3I4P//BKedkpdAIAiEApV6cjrzEDDEoQ2nzbkBMVVkI4dtD1Z9MJKnvvwVt21o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO589ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHKQ//Vuwhe3YOVaVy8YWuIpni1kUSpVjRFk6HiEdhThG5uMKnrYEv\r\nPQeOqqSlmz5SMODACRrv16zMf8VP6Dxubf/jtKXjf/RSWHjYkAgEueCh4KIi\r\nttGD/Me5zAV5R8DQpCzRj+NhbTYWBsEpoMzjoGdc+vQr3l5y5eQforKipynS\r\nVlndtywQHdbuuMvlLU5TgI5TNtiOPHSf8ibrKxfrKdI4DHe/njwqhjnxV5NY\r\nJ/DK7EngpEJXUyl5Tk0uBUDoHL3h0xua/KL0t67lGpH2dSz7Tr6wlzhXOwG3\r\nGKMZseGRlqXpcBYwuqhIwLHdgVtCUE6P4fkxzjBJ0uMCOhYSHxy/0aPVVepm\r\ntnBAi9t+VzaJ2sZZKSGxAwIyQtZ3quHItqOEDDCIe7yaF02Xt/6HcLEDMI6c\r\nOx10xqCKCsKI1lBm/qE2/GkDp+XSDineG/5hS/0Yv2uMZHHnS6UudHT6w5zu\r\nNKV0+KHyBneNHCSK6pbPWn9S86SzA8sc8xe0UX1J6ShleyHnnW+Op/ZBZ1aO\r\ngoGo/+YUpy78OjStwOghFwrjuBQkacyNsHfZK++EI4Z/rJ3D4JdCEz02u3Pt\r\nxWnHnckR3a5X/j2iuqEVGuw/1ekwq4tj7VLOe1eGmEgpuXDFBKVs5jBLQT72\r\nn2Au4UPaanIGY+P/tqlgiM7aPN2WPrJXyCw=\r\n=xtSv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cc856f3cc847a69c31e92ceb2c6527e1d30a9511", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "8.19.3", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.11.5_1681628989208_0.42117091830614917", "host": "s3://npm-registry-packages"}}, "1.11.6": {"name": "@webassemblyjs/ieee754", "version": "1.11.6", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.11.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "bb665c91d0b14fffceb0e38298c329af043c6e3a", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.6.tgz", "fileCount": 3, "integrity": "sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==", "signatures": [{"sig": "MEUCIQC2ccYouZrTWS8zLR1dN8PKVOAuNCKgutjFLPOI1ublbgIgN8LTUMpWAnJvR8hsea769/shjabqFAI6NrbQ8Jqjhww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3179}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "58d40904ea7de2dd17f6f8d894ebe611b812a4db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "8.19.3", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.11.6_1683645056189_0.14736902556225484", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "@webassemblyjs/ieee754", "version": "1.12.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.12.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "f50e7304108a1bcc7e4e6cdbacc63dde32a09b44", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.12.0.tgz", "fileCount": 5, "integrity": "sha512-zQ1Q8vg09WD0j40e2q+g6Q70Lxid5Yjd8Enm94H0P4p4QJM+JYWnma3gtfS1qNrpCVzOmvp6/XEPfaVKgsLv4Q==", "signatures": [{"sig": "MEYCIQDmZKRxqwQk7HMVUI+KkXgAUbwIoIUQeEhlUF5mDdmlOQIhAP/Gxk53QWGFZeitoN39Ad2hwV5HRsOn6pOfLD8PklFT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5342}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6d1606bde5ab7ef21ea4b25715bd2fe58e8742cd", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.12.0_1710325116909_0.313920671493535", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "@webassemblyjs/ieee754", "version": "1.12.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.12.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "6c27377183eb6b0b9f6dacbd37bc143ba56e97ff", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.12.1.tgz", "fileCount": 5, "integrity": "sha512-fcrUCqE2dVldeVAHTWFiTiKMS9ivc5jOgB2c30zYOZnm3O54SWeMJWS/HXYK862we2AYHtf6GYuP9xG9J+5zyQ==", "signatures": [{"sig": "MEUCIQC1kNGDOPHidD2N0ODamVSqR3T/FA+CW4ozXCGcH2f4KgIgJ/TvWitQK24AdjMu5P1QRXZI7JfPyc8/rxrrYaOHuTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5342}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.12.1_1710325152839_0.8642855418520359", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "@webassemblyjs/ieee754", "version": "1.13.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.13.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "59e3edb74fab574c67f7cdf3f836e5067ff959a7", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.0.tgz", "fileCount": 5, "integrity": "sha512-pOf6l97QA+86q6zlvZbU+3nrZT19wE5u1Ip3MFclBaHRiufLeMMPwvuvODQ96WT6Mj6W4Cla6z9gAKzFYjhQJg==", "signatures": [{"sig": "MEUCIEnTsjjozwhQUDj7tuNAAKwdu0OJlIipbQkH8uN3dF1CAiEAi/5Y2+12JWbrrMZhAQZHHP6f6bOi8VIt8kAdk1ruxek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5342}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "9b0c0c1c1e035e3336edbda8c330827a1f267513", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.13.0_1730912081878_0.6007473718872853", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "@webassemblyjs/ieee754", "version": "1.13.1", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.13.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7cc2c522faad1da0c384594580cf378e1dcbbf27", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.1.tgz", "fileCount": 5, "integrity": "sha512-0ECu5BqyIA/0BA7jSvgAYNW0ZqMrn86khGcZexjb2A2Mvu9Er6bF0w9mZcI9ovqPwcVy4FZPdv2qNNXA4KTKoQ==", "signatures": [{"sig": "MEQCID5tHGvXgcngPgiBqN9ajlADheKAzLA66YqxkA4pxwzjAiBhWw09HIR+ADkCahiz4svQQ63F+hYfNZaXpbR8kK4D9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5342}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cfe35c57093d414839b9350398369b78d97815b4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.13.1_1730912128233_0.39027475888168195", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "@webassemblyjs/ieee754", "version": "1.13.2", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.13.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1c5eaace1d606ada2c7fd7045ea9356c59ee0dba", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz", "fileCount": 5, "integrity": "sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==", "signatures": [{"sig": "MEQCIFAdIsHdCFe+XTCVJEQpPvKPWPuqAHtWojXr2Yjz/gMuAiBHMxnzV8GsyOy4WV44dfrzuBrkGS+s6Lv9mbXUL9dE2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5342}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.13.2_1730929428998_0.9292076842817856", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "@webassemblyjs/ieee754", "version": "1.14.0", "license": "MIT", "_id": "@webassemblyjs/ieee754@1.14.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "12e03a58c941089919e68b81c3e19933dc442789", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.14.0.tgz", "fileCount": 5, "integrity": "sha512-fnUaIs5MauD1axlbSgLtX6TdZkUPRmG1pdFrVlX7DWaqK7JirSOgKNBlIqnAMaKNbFe/KnGFg2G/ncLE4890dw==", "signatures": [{"sig": "MEUCIQDqlYZzcSLNNgu3eUnrXE+yUpAhGPDAwt64YJciKKYw6gIgBDj1rMCTo/JqFEK8eBtJVppZKOkwrgXPhkki2ruzsdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5342}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "76babfc909478ebf173fc0269881878ca747dc01", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git", "directory": "packages/ieee754"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "IEEE754 decoder and encoder", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/ieee754_1.14.0_1730929987710_0.00475632237490986", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "@webassemblyjs/ieee754", "version": "1.14.1", "description": "IEEE754 decoder and encoder", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git", "directory": "packages/ieee754"}, "publishConfig": {"access": "public"}, "dependencies": {"@xtuc/ieee754": "^1.2.0"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "homepage": "https://github.com/xtuc/webassemblyjs#readme", "_id": "@webassemblyjs/ieee754@1.14.1", "_nodeVersion": "21.7.1", "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "dist": {"integrity": "sha512-hZDDfS2/NYPw+/0eFSqFZQ+WGNlFERxj1C6ObV7FxGUrNkoVuTRl8A6+Tg9prfFxD2O84Jxx7rbZAggO9EAQDg==", "shasum": "4d02a880f3ffae500cf019842b907b6f2c901e92", "tarball": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.14.1.tgz", "fileCount": 5, "unpackedSize": 5342, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBoqqfuDUybRSRNdQE+HI/xk1LL3pSaTsOjZM+mnkHcKAiBqSDk+UXKLkun4en/6l3eKpDRZWN9Ff9jLOcbiplxNHA=="}]}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.14.1_1730930015885_0.6738747666341605"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-28T12:48:42.337Z", "modified": "2024-11-06T21:53:36.378Z", "1.5.8": "2018-05-28T12:48:42.498Z", "1.5.9": "2018-05-29T13:13:02.545Z", "1.5.10": "2018-06-01T13:20:41.210Z", "1.5.11": "2018-06-06T08:58:03.169Z", "1.5.12": "2018-06-07T09:25:08.839Z", "1.5.13": "2018-06-30T13:44:27.768Z", "1.6.0": "2018-07-16T09:05:03.737Z", "1.7.0-0": "2018-07-18T12:57:56.124Z", "1.7.1-0": "2018-07-18T13:09:13.868Z", "1.6.1": "2018-07-18T13:50:24.274Z", "1.7.0-1": "2018-07-18T14:59:30.269Z", "1.7.0-2": "2018-07-18T15:15:00.449Z", "1.7.0-3": "2018-07-18T19:04:01.533Z", "1.7.0": "2018-07-19T16:00:47.615Z", "1.7.1": "2018-07-19T16:31:38.734Z", "1.7.2-0": "2018-07-19T18:09:38.740Z", "1.7.2-1": "2018-07-19T18:24:25.412Z", "1.7.2": "2018-07-19T18:33:28.959Z", "1.7.3": "2018-07-23T07:02:26.822Z", "1.7.4": "2018-07-24T07:02:16.144Z", "1.7.5": "2018-08-16T14:53:37.867Z", "1.7.6": "2018-09-10T14:17:17.202Z", "1.7.7": "2018-09-19T05:39:13.035Z", "1.7.8": "2018-09-21T07:37:25.397Z", "1.7.9": "2018-10-18T16:42:42.757Z", "1.7.10": "2018-10-23T07:31:14.659Z", "1.7.11": "2018-10-30T18:00:49.853Z", "1.8.0": "2018-12-11T07:24:13.534Z", "1.8.1": "2019-01-15T08:21:11.745Z", "1.8.2": "2019-02-14T16:21:34.696Z", "1.8.3": "2019-02-18T08:37:03.948Z", "1.8.4": "2019-02-19T17:27:07.442Z", "1.8.5": "2019-02-24T10:42:44.827Z", "1.9.0": "2020-02-01T23:19:52.013Z", "1.9.1": "2020-10-03T20:07:50.804Z", "1.10.0": "2021-01-07T16:17:47.579Z", "1.10.1": "2021-01-07T16:45:06.969Z", "1.11.0": "2021-01-07T18:11:27.993Z", "1.11.1": "2021-07-05T08:22:02.737Z", "1.11.4": "2022-07-02T11:52:53.799Z", "1.11.5": "2023-04-16T07:09:49.355Z", "1.11.6": "2023-05-09T15:10:56.379Z", "1.12.0": "2024-03-13T10:18:37.067Z", "1.12.1": "2024-03-13T10:19:13.018Z", "1.13.0": "2024-11-06T16:54:42.171Z", "1.13.1": "2024-11-06T16:55:28.431Z", "1.13.2": "2024-11-06T21:43:49.181Z", "1.14.0": "2024-11-06T21:53:07.958Z", "1.14.1": "2024-11-06T21:53:36.169Z"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "license": "MIT", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git", "directory": "packages/ieee754"}, "description": "IEEE754 decoder and encoder", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "readme": "ERROR: No README data found!", "readmeFilename": ""}