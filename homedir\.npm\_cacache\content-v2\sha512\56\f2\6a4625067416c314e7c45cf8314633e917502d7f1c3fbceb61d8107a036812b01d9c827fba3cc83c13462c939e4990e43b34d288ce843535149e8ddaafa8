{"_id": "cheerio-select", "_rev": "25-d6017194a9f6b8826df44f31f386c60a", "name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.0.1": {"name": "cheerio-select", "version": "0.0.1", "description": "Selector engine for cheerio", "keywords": [], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "dependencies": {"CSSselect": "0.x"}, "devDependencies": {"mocha": "*", "cheerio": "*", "expect.js": "*", "underscore": "*"}, "main": "index", "engines": {"node": "0.6.x"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio-select@0.0.1", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "5a8ac9bb60225587a6c27584a937e0197ccf89c5", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-0.0.1.tgz", "integrity": "sha512-r6xV71niR1wK+aYvuD595At9rp2GlbUIGZiRas5XYicZOl9Bi/YVNwVpJRpI1bC/1LI526bNxw9Miq7/0pguQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdDhoi6YZDBowSZ+GrMYzPyDaEveJQECVOABk0pIr/BwIgd5KxboyIjO9rAd5ALB3N6urKxvEPdn5zdO2KEBW4Rps="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "directories": {}}, "0.0.2": {"name": "cheerio-select", "version": "0.0.2", "description": "Selector engine for cheerio", "keywords": [], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "dependencies": {"CSSselect": "0.x"}, "devDependencies": {"mocha": "*", "cheerio": "*", "expect.js": "*", "underscore": "*"}, "main": "index", "engines": {"node": ">= 0.4.7"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio-select@0.0.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "a2b22748f7db9040e677ba5ac1c83849f371b0fd", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-0.0.2.tgz", "integrity": "sha512-kVtyaF+bv31Z8kccXCH3jOBWXxtIYH25K9yXqpEFtJeO2vR3Tk8ypcvFSC8+ZY/0z5KQWTHh2kyNn7L37w4gbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0qGQABDBR0SjUfQysx0tc2KS++Zt7oq8DxV+md5XzzgIhAPr/S92/zuc5tGDl/suAdDX/Kts/1BvD72zs32AxhyPz"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "directories": {}}, "0.0.3": {"name": "cheerio-select", "version": "0.0.3", "description": "Selector engine for cheerio", "keywords": [], "author": {"name": "<PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "dependencies": {"CSSselect": "0.x"}, "devDependencies": {"mocha": "*", "cheerio": "*", "expect.js": "*", "underscore": "*"}, "main": "index", "engines": {"node": ">= 0.4.7"}, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, "_id": "cheerio-select@0.0.3", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "3f2420114f3ccb0b1b075c245ccfaae5d617a388", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-0.0.3.tgz", "integrity": "sha512-T547B31tofMJg2Hm7sthSuP7X1hxckUHqOZYXEnaCJnEFL47g1VJCvwi67WIAsWbxcKFvMJYHPekjhBfTjSD5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+dR41V9Y6AqsuuzUeUFwrkkJgGMflwspX3SnxUj8z9AiEAwsiZEPQxoxTJWg40HtNhUhNjFpjv2ewD7rw5YdfVI2Q="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}], "directories": {}}, "1.0.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/fb55", "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^3.1.2", "css-what": "^4.0.0", "domelementtype": "^2.1.0", "domhandler": "^4.0.0", "domutils": "^2.4.4"}, "devDependencies": {"@types/jest": "^26.0.19", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "coveralls": "^3.0.2", "eslint": "^7.15.0", "eslint-config-prettier": "^7.0.0", "htmlparser2": "^6.0.0", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.4.4", "typescript": "^4.1.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "c13f3e5f2d0de1a2e1c754dac315496b412a9d84", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.0.0", "_nodeVersion": "15.4.0", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-ngCrm1rYldyYXgjkO+8fmjuoj7CtoVqg/xbi1kPmwBYMARHMOwMKp2qlBx6F7LXejB0kRvO3xnOXZvUcpEzseQ==", "shasum": "5c8edd46576e6bf30d69eda2c51ffbcc320da10d", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.0.0.tgz", "fileCount": 12, "unpackedSize": 19147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf5jOmCRA9TVsSAnZWagAAwrsP/2OPg3mNC9LNuY+qrYft\ncWVQH9W2L0DU9Y54/SqOabzc2AJS/WsHn02PL5WdUJy0bq8FK6N2n3NKHWDq\nl7vqBlQaohgXDv8VqrjaCo67+jgwevvWftK56EYlq9UhwgXiIBP5O3mR4JEQ\n5k2jXrtShbPvVBx9AeI4ZaRrg5S12X83xh2WwGXCIVUwEKuvaf9J1KdBRRbk\nMewqHD/Ro+QErNXiNjK/n8s4jDGlpP/RX5B9WNP2QXsiXqqGgdClRDGuQJnK\nyUMVdHRa+wdy+H7Zi1gA5rOGdO/DE1s9QTOHVhV6pTMoMpNanF91iW+SMzp1\ndbRyucRwRAUCVvcVMFGprVgw81XYDIAuD2fAenDoAQwnEPAupsCu8M4T7347\nprSfsZPkhEWV395j79tw9jzXyEocd0GdLMFAyzdQIC2jgi3HcmTPlg5fFGRA\nmB37B0ZDxKydnRoMP1MSK2SqE9NN1sVHv0fxi41pyyNrAD8NBxAGlbKBCCNR\nndvV5EkRhJh0ZwkRUoQ+kPAbWl4f12THgLCS0J0Ac1xNqYLAL91ByeyA1A+F\naxPfZxqqUK7ZRphgDMns313nujKf4YMJozRgXhkaI/D42toTB8O6HaXg6OqO\nr49ERUsWrF4AgMEUq+S5T1Qn8ypfp0o/xNcIG7h82uEhC6ARRP8wu/7mpZkQ\n4JHz\r\n=c5Fv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDuuzjsifbN6y++0y2VnN2hy8vqrBvFtthO7KJsnUX6AIhAPNIlzZqSC/Tou41jGL6giz7n9mbOFTgkcdlhCtABoYZ"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.0.0_1608922021665_0.9610599433364577"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/fb55", "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^3.1.2", "css-what": "^4.0.0", "domelementtype": "^2.1.0", "domhandler": "^4.0.0", "domutils": "^2.4.4"}, "devDependencies": {"@types/jest": "^26.0.19", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "coveralls": "^3.0.2", "eslint": "^7.15.0", "eslint-config-prettier": "^7.0.0", "htmlparser2": "^6.0.0", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.4.4", "typescript": "^4.1.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "cea6d8de8b8a977fb3b4364a50e46b5805cf6340", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.1.0", "_nodeVersion": "15.4.0", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-hOcy3Ps4lg6tMWM/q2NL5ItM+BTt1C8TNbtb9ZGscqDbGhKX0TzLRy9QJ2/KVlmmEbMjCNBublWdO4TPz51SdA==", "shasum": "d5a73b62915b30c58165d1ec6facb2a1f0a8a8dd", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.1.0.tgz", "fileCount": 12, "unpackedSize": 19434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9yByCRA9TVsSAnZWagAAF+QP/1HpixFyRSZ7d2GBqb0X\nlumP3uWtpX3rSDfQhExoY7wkrOv/TOa0QRVTM7ldAlU/RHL4+BeZcsUy1bh0\nWl7iQEF4IMwRFscpqjRs/qkIflB787uN/8lnzzoExYTU3ezVZkSvlnqJQkoq\n+o35C397DW7FR5OPlP41qiAl+SFVm3l6kHquR7vILcSuw6tfQFpeqG9r9FNx\nahU7b0ecCFv3jks9CcZtpKJYgA5xA3djjBHZqzpcrLDKlFeA1EXFz3nyi3cz\nJX4htPmwBaHi5A7g/FQ+WUnVn/aQ8qg2cI3861+g0sWfk2A6hqodOqotf1nf\nBl3zhEXejqqKCxWcA9U1SeZjSngeRPV6SUaoHv3mgAfocToVy34TIzoL+uFi\nmUG64N/xS8rgWeXQRGzo03dKNalcxdH16caXANWxG1P5XqM54G7I0fdXb1ox\nric5f3OJk9KtG4cjUSUkaGb15CGvHjO+dGDCc3qVlVbDU9SJi5wO9NePbXSq\nAhT7VZ7dr9TmCrIJQulmYA7q7G30t6ZZqXHeV9JSDquLrIDY1iQTRT7Oxctk\n2kcswGwWifAhionE61bSFBCogzdPUZCDe74INaJtUjHHOIwqKZ7DjUm4aVUi\nRYlE22efZE1oYqbnoSKh4EKypQ90nuBlh8hhYSC3xwLJtsxRvd6I0qiy4unC\nijwm\r\n=hd3P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICMtlWjhlpRru7oVfl+B5wkygXh64CUpBETV+11iBI4GAiAQg9YLMVvodmmkkQeRSms7oVIUngt1ym+wi4yjRBU/ig=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.1.0_1610031217737_0.9366431238002615"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^3.1.2", "css-what": "^4.0.0", "domelementtype": "^2.1.0", "domhandler": "^4.0.0", "domutils": "^2.4.4"}, "devDependencies": {"@types/jest": "^26.0.19", "@typescript-eslint/eslint-plugin": "^4.9.1", "@typescript-eslint/parser": "^4.9.1", "coveralls": "^3.0.2", "eslint": "^7.15.0", "eslint-config-prettier": "^7.0.0", "htmlparser2": "^6.0.0", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.4.4", "typescript": "^4.1.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "a8390e5a78f4854af3b248ac9bae3f645f6f0e0a", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.2.0", "_nodeVersion": "15.8.0", "_npmVersion": "7.5.0", "dist": {"integrity": "sha512-qK21OAtG1KvkwSgERrmGrATsZgtBC33whyFFMDZUULbKjLKuP0gGEppmPxWqkY7oyXJINhq/z8wVxKn+DZsw5g==", "shasum": "9667e6c6f2c4f1e3e520032ddaef5a219b8c2a37", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.2.0.tgz", "fileCount": 12, "unpackedSize": 20210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJKWdCRA9TVsSAnZWagAAZT4P/1sApVtPeI2KNxVT0Oj/\n6lJYIHJXcUNhGTHppiupQudLr0ngOTLkhQnNMv5EC6QKq8GgFL8EqzgS7/zf\njrZrQqGbB+gNUurBb5WozZPvWYQq335Thd8PAYrX4Ldmwy0UlxieLrZmo7oz\nT6OLDCRZfIcPOjFdIprOPOXIT1fZEiyJdxx/wINQOmzh1LFx/ebSHtAitCv1\nbR1mTXLrABGHAvM76tHYEkCaHInpdSTpt7hujJlPdOOulku6vqq6MVMrWrtN\niR9QcOsAx86ZljziLvKvk6dwZ8XzYkJa1cbCtgu20w4oBJ0RoKDlkJ//IIAr\nJRGWZlMPXg/E5JG5ksNWkzMcukGAGDLb6ME7mSSJZipmdOZb8a23TiotDNFS\nX+qh6ERGk0B2FsofpeEduOtkIyXZIYPsnSi50Ij0TJervw//XOFB1uO9+Jns\njbyh8MZicLrknzcCN6q/B6oDLMCCoX9i9PvMrrEa30PtTm7jGzdCJsLOadE0\ndAgZYVgmLOYhIAVOhMOsM3TKPvcgA5MfReOAZx37xPGW7uiGeXMPwDXeHfNb\n/aCeK8p3g8ESc5br3kwKGtusFXSLu/+/pEXIpjp/TM/w7ywpAJPyqOuxunaK\n++zYybShMkwGPU+4emUvaPt9cnwPZA+ZoqQh2gTVGZmk8e0ESPvycR2tvQUC\n9pIl\r\n=GZ0H\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMucuGXQRvoetgWXN9bKK1mRgvhxNIeeMFy1oHQV2HcQIhANjWVn/a5KWQmRqCbrZf78FD2GOzEjXKaGgVG12daD6L"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.2.0_1613014428830_0.8088403875616905"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^4.0.0", "css-what": "^5.0.0", "domelementtype": "^2.2.0", "domhandler": "^4.1.0", "domutils": "^2.5.2"}, "devDependencies": {"@types/jest": "^26.0.22", "@typescript-eslint/eslint-plugin": "^4.21.0", "@typescript-eslint/parser": "^4.21.0", "coveralls": "^3.1.0", "eslint": "^7.23.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.1.0", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.5.4", "typescript": "^4.2.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "fa76af5b2fa88b32774dcbfed33ce3c2eaf96eb8", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.3.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.9.0", "dist": {"integrity": "sha512-mLgqdHxVOQyhOIkG5QnRkDg7h817Dkf0dAvlCio2TJMmR72cJKH0bF28SHXvLkVrGcGOiub0/Bs/CMnPeQO7qw==", "shasum": "26a50968260b7e4281238c1e7da7ed2766652f3b", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.3.0.tgz", "fileCount": 12, "unpackedSize": 20108, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgb26PCRA9TVsSAnZWagAAR3oP/j4yMdZSc8oNkHs6sIRL\neDb4lh1tEUEZBbwfD/4cZ63w4piU388hLJfZu4I9VJjcQcombD7ngxum+Nv3\n1Z2+Gx8ByGqLh/CRBBIHWobzrJm1upFf0swEmLURvpn5uep6X4NkRpGT5QLn\nB/0n3zpKUHvBZk+rFalUD6S8zbhFKzFzHVxguhyU6ijpFg128k4dOAHqMveo\ndg7ttYCaqRig7FtoLMebsvIEtC2g/28EDnfaUklyFGCqSa6pVmx0jtywpCwT\n87BA4fvQL2DYeFmRchEd8r17cL/d1CBIXLZTaYfrbvNoUuvKsfTVKG0CFcfA\nrLRBIFBJqYBECIY0jCvAG5pvc1KeTDTthoaUuizNIvyrWhDzzW28FfPUulcp\nTn5TWq9eTlYhAet8oycEFfDLLhFLcQH5YdOVTtwKyiStP8WDt5WygnKIVsJJ\n7G90CFLpn41RVyzrXox6iAnI7A4x7L8ny9AEFyTPo4JCrnTNk/Q4hcSHNnfy\nr6Srxxs1ztJODL92N1NSR5Gq6Bj3LWPMXMMGo2kRBf10l+UDeHrXzdmVF7js\nzl/FM61Vs1K3dVEIydp8FCjL6ZDFbpxSu1DYPJM5j0XjCQdtL54CXRh1mPkS\n8JMNdjcD640OR9XogZAWrNcGGS4DOcAGkZokbfyQ97eIQLWge7I3xnNWCVhD\nKwu9\r\n=bpgP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAiT9FPUy22KlwK9IL3o4Ii8fseElncwYRK0eHDDrzH3AiAj+FGNGmcnD0yJl5ApOATP7/LbZ2bbJTgiPhFyna0KQQ=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.3.0_1617915535474_0.22588872413461347"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^4.1.2", "css-what": "^5.0.0", "domelementtype": "^2.2.0", "domhandler": "^4.2.0", "domutils": "^2.6.0"}, "devDependencies": {"@types/jest": "^26.0.22", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "coveralls": "^3.1.0", "eslint": "^7.24.0", "eslint-config-prettier": "^8.2.0", "htmlparser2": "^6.1.0", "jest": "^26.6.3", "prettier": "^2.2.1", "ts-jest": "^26.5.5", "typescript": "^4.2.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "7fb4ef64a65435c0430919f9883aca5b106b93fd", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.4.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.9.0", "dist": {"integrity": "sha512-sobR3Yqz27L553Qa7cK6rtJlMDbiKPdNywtR95Sj/YgfpLfy0u6CGJuaBKe5YE/vTc23SCRKxWSdlon/w6I/Ew==", "shasum": "3a16f21e37a2ef0f211d6d1aa4eff054bb22cdc9", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.4.0.tgz", "fileCount": 12, "unpackedSize": 22306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeXUkCRA9TVsSAnZWagAANYMP/iBjT9cBOsCIkCI9xPGU\nEw+53hQTO6pVXlH345VchRgLZ4hmiRge0orAJY50ujN3Ki2LHjyYuBeO8Ygb\n3uFXqiQN1TdGt0toLZcSfJhBvuovcT3FOObRjk/HdsBMjn+qjZBrOnNE8A1Y\nQD9fVCYtJSyxS2mmf8o/Gex3oMf1HttlZLzblynpejnBybD4cGEeagXG49x+\nd5GOjpQv4f0evTc6jDJMxhITotEnVK9Co+pxlaCddKlrAzl5kl2iddvXdrie\nxeWCq3tSOQvVGw+3Jf4o+gsW4spFkJ1x047p04cXqpk9cnSWCtIc7RPpGanr\n6bQRsWSYpgSGCVYTUG3zYJQoainXgIj5OEXGFj9IENEooLHSboDslIH4NFjI\nvBbG4QJatqBBD5Nain5OaRzZBU+wr31vodBbMLoAXslIcETdrealHPgm88/a\nQ0JE4QKXvs3/m55aINcdzEGsnjxL1YDvHi54dhfhRHxkTMlRCYgH1EIoMYlx\nwmKtgvWNrU6w9ts/tc1y+jiwDtYlltWW1Ej7V7ec64ZpuiW/5Bxfh4XWZoPa\n+5vj4NqUp54PY4EQTCfh0rkI+HS4EKhoiAU3FxeJg1gDRAU+LKvUkH6MbziS\nlHNrW9yhFR8x1lgZBBx+JRMW25g6UlAaK8fic6uo7JzKHc0UCkwg0uzP0xHf\n8UzB\r\n=r9Gr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpVWWIGe7FLzlCF1L1GC1PjptUf09YfqOX04FyItv7qwIhANBENj6tzE0MaJ352tBMBa2HJXBF5oAkIl5W4OIDoNnH"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.4.0_1618572580271_0.7508507413156438"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^4.1.3", "css-what": "^5.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0", "domutils": "^2.7.0"}, "devDependencies": {"@types/jest": "^26.0.23", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "coveralls": "^3.1.0", "eslint": "^7.28.0", "eslint-config-prettier": "^8.3.0", "htmlparser2": "^6.1.0", "jest": "^27.0.4", "prettier": "^2.3.1", "ts-jest": "^27.0.3", "typescript": "^4.3.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "694b89f208b171719a1d94628f447dda19a1dc1f", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.5.0", "_nodeVersion": "16.2.0", "_npmVersion": "7.13.0", "dist": {"integrity": "sha512-qocaHPv5ypefh6YNxvnbABM07KMxExbtbfuJoIie3iZXX1ERwYmJcIiRrr9H05ucQP1k28dav8rpdDgjQd8drg==", "shasum": "faf3daeb31b17c5e1a9dabcee288aaf8aafa5823", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.5.0.tgz", "fileCount": 12, "unpackedSize": 22316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvnwECRA9TVsSAnZWagAAkmQP/jazsyoS7pkOnxIcXrhF\nGTPablaM15U7EqQGRsSgq5m0qi4BDZXilKuOPjfq4FpSnPSJajqvMhsp7Jv0\n7VLj51Igw+zKn19/laICoGqXWmvFkMm33z5SeAJnSvTK0c913NGShyaWaBXI\nTwTIN5tIDk/OYk7X4f1v4eRwUua6hmnyWmqF1+w1elNA64FQclCtOKxmsPwo\nLUvL1wgemxin1q2MlMoMuzQTLsAtGwnUiRF7xD5mx2qKeX7BGx2cfC3xeZ26\nu3uMJ9KA8t+SXalEkzGb+oGt79wjdL8r0aS7OoHsVk73N+YQW+0/+TyISC6Z\nmiQ3OypbXmDJF5wYH79S6eu64gfySTf5JmkMOsEAH2c/tkV+VY0F92ZDfY61\n09ilKdNb+CSDJYYrvO9y+wRJ2yQHeMylI3bLCosBTuLWIaS4BqspfH2wXDG2\n/SFQqRPrMVwv7Lu3T8irpqgtLkcD1/GAcsle+c1NaG6/oI0tUB7VrSAM8saM\nH3297OD8SdmXTCPmXmp7/D4LLSsaN8ZRa8mDrNS194wHwzyd4LvBKLiCH+9m\nl1b/j0zfdwkX6okV4E88T7Huqnt7UH8janMcDL0oUjVfq4VmGmRbXrCk8DhA\nePm8ZHGcy2C+SnAZYHsGLKazEMeG3l0NmcXDXDLHq2apDSPVtOmYXJvNffh1\nil9/\r\n=87nX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZalN6s0SIQ3eZxEBNgLY04JfKV6JvLbMREZpzvYq/awIgEs+loykFelG54XO/LA3coO5O9tIBe6Uw1MA/sjT5VAQ="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.5.0_1623096324555_0.8502306974604092"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "tsc", "prepare": "npm run build"}, "dependencies": {"css-select": "^4.3.0", "css-what": "^6.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.3.1", "domutils": "^2.8.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.16.0", "@typescript-eslint/parser": "^5.16.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.1", "ts-jest": "^27.1.3", "typescript": "^4.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "28e98edc277c0428c3399c7f486abfb51b0ae63a", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@1.6.0", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-eq0GdBvxVFbqWgmCm7M3XGs1I8oLy/nExUnh6oLqmBditPO9AqQJrkslDpMun/hZ0yyTs8L0m85OHp4ho6Qm9g==", "shasum": "489f36604112c722afa147dedd0d4609c09e1696", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-1.6.0.tgz", "fileCount": 12, "unpackedSize": 22876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQjaLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqOQ/8CItHLu7I6gaGKbRRQCuGtkBi90NYwLQ7BhVphp3VXClZkVa1\r\ntfMw/k7N82811QIx9BUa38gMbVkO/moZPHiHDAbY0u6fpCP3hoD+hxKPosa8\r\nNF/Xmv/VRr/vVJfuHgWhAMtU6qgIzbE0YjwpdrD7CTyOuqpKDrWszAWw6Xw9\r\ngcGpdKAf+0cfEy7/yQcPJdMOmlGj452xnewvUpeCidU9SMvSzNUiSNcgchJG\r\nwYPA5DFdRRh/RWmyWv0VTpilnnC29JOQS7Jms0EO7zFRLarr7sndeTJ48Um+\r\ncDCFINGYhyw+oxmZqGBNAPh+JMJ0MmW9bAc/bvtoCJNPJbZfnPomzQTUwddu\r\nQhCYcigigp4OIZO9FpkrrbNMmym1mS0TItObuZZL8r1nUShQFTUMX9Vznlnb\r\n6WF45kMZGf7yNp1YITuArBZ/SNJ2GO6cGU+klU23i4GLGB47GJwuTFLC6xBw\r\n1cwF6rlJNu1cjtZjenHaJpWv0JK/tA1g6HzyKQEOVzv1DX3VB1obZQn5H4VU\r\nPbNa5u0TAAY0JpGPorsQe5kL4oSMXgv2SJ9lQIpYkmsfOTSfLPynN4LkAnYe\r\n0Bt9KWOukdnOX2nnMCMyklQIAhdkvX5y8PorZYl63Za31DLhlt8JHI3V2HEg\r\nDeE5wwMGTh4tyRQJzL588vi1JvOz8oTWxEU=\r\n=8LqF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3MYYbvMxDw2zh1bB7wHveNSf8wrcMbIHs+m0aRyzswQIgXOXJanxJVcUhGPKprgAU7KKeXCfA3KRKcqgr+8YqzIQ="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_1.6.0_1648506507376_0.6067135654802316"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio-select/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "dependencies": {"boolbase": "^1.0.0", "css-select": "^5.1.0", "css-what": "^6.1.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.2", "domutils": "^3.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.29", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "b9f778a17af5d10e9ca122e2113b1e437610f81e", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@2.0.0", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-NDwtZ9H1/2RGrDuovbYY3i593JYgLDaNnP4YxWE8SxdVK7qHwa2dT2WcvO1frX9HwF1GOtWS85OWPmkBeVCl9A==", "shasum": "889ae73d2216d9fde85ad814dbb07d13bb956401", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-2.0.0.tgz", "fileCount": 28, "unpackedSize": 60860, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOoTFL8tMZbUe5MKg9wX94vZiHFDJTc5uq7zFTV8yNEwIgItyQ5aBB0TO09tKW8vIp1x/pWPEmqX37E6sOZxFjleo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibQXvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyxxAAmgGsu2BIpyy2L2BoRNTlZKZ5o3JemoR+oLDq9nrSmH9Q1vL8\r\nki/4gRV7h12YVv/sttQPcs9s7LoS3jCMEY4rVp3l5O2lBYAduw8qpjYLWLry\r\nXzoLKpseliv+uw8VoBvVC6RmakpObkI54bHm8ZHuBjfwEmmsWT+tYARlxI3P\r\nwFlGixSsTLIdh/J2aHkUGVQpzAzqOiT7ymO82QeWHTK/1ApuIQERwk5QSShP\r\nLw9poi87qTh0zpfwaTfHRB7PqKn6vG5c6FgoCVUtm9sL6OqBtuQvA+jaTHR7\r\n+rCxf/5D0RWdRuFqBa1wIgAtyyWHmoJ3rSHc7wts7C/YLbsKdL0dC8nfDDH1\r\nxjIf4YicT9vmzQ4149SkgdGOBiGGEfLJlbNhWtLTApqSaZOemLYkFUjMzhja\r\nw8Un+iedunDG9nOJkyY5zSuUkIzck+I17vRuWqenWXYEHhxVvcEJv6s40lm+\r\nfNeMAVfaN2zOySuLyB4KxusML4Q4Am7gpcrBDavJsvTVk/hZ/DDtRMcTuwrr\r\nB6+q2Qm3BAnttTgeiZHmwKIpzUHsCVCsndAIEuU1kmldEblktUBjHs2qnJDK\r\n4sclyH++EvhZcw4Jhrd6hJX7CzGTco2Kzxw4lzbutuhIKtpae2kVXfYAie8R\r\no6b9SQPa2dyND0l0QlMOgA8sgtOjQRFZCeU=\r\n=SrfQ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_2.0.0_1651312110939_0.8561919606307618"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "cheerio-select", "description": "CSS selector engine supporting jQuery selectors", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/sponsors/fb55"}, "license": "BSD-2-<PERSON><PERSON>", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio-select/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "dependencies": {"boolbase": "^1.0.0", "css-select": "^5.1.0", "css-what": "^6.1.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1"}, "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.5.0", "@types/node": "^17.0.33", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.1", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "ef063a6ca4c3f0d02d2fc3505e750b6fb81c448d", "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "_id": "cheerio-select@2.1.0", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-9v9kG0LvzrlcungtnJtpGNxY+fzECQKhK4EGJX2vByejiMX84MFNQw4UxPJl3bFbTMw+Dfs37XaIkCwTZfLh4g==", "shasum": "4d8673286b8126ca2a8e42740d5e3c4884ae21b4", "tarball": "https://registry.npmjs.org/cheerio-select/-/cheerio-select-2.1.0.tgz", "fileCount": 28, "unpackedSize": 62620, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCByF1R1yNSnr4NdCjV69kN6ZjHmrkhCBO6Nkne6Byi9QIgCqCMhqRy1QRMKmhXJ8u+uCmNGZbl8Jbux7y4Jc8QRds="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigiyQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb4A//Vmm6xsAQgd2eHbDeHQ0b+7Kf6k10jJ4vXCtZ9dbJLnId56yT\r\nHf4ulSRZ9/Lx6g6h8K05u7SBmY7gl/GqmF+arTMPBY1/irDCR0TA49stYV0k\r\nQA/q09IVutJ0KWKxg4RCYc3aogMqKlKW3MQ2Sso9+Ilj5yE/mhKanjuB3Htu\r\nyjUdTh1RZqlkKJ9gSlIu7XSB7qovzvOSMiaEApGL1BqSYlMruErNINjDIvLM\r\nSolbiAETpUuHmS+pm8HtY1hSJb9FufyET+bIgXfCLyFQ3E3IgminmLA7pR8e\r\nrkk039QR8TDza/BWXK+0dB/vNTs4qHf39lI0jDTSe3rsWVZ57m5FxgMJwdQf\r\ngzxX1Ezn1xth41wuLKnGReEoZAC7GIncLd2UIOEzEgly7MG2BlEeKmW6RWFX\r\nuQICd0+ymqbAVr6M3qQRC8qM9rcF2eItIG7O8cCyyyWg4zI/O+rA7CAIyCJF\r\nMx4adnTsSIYvyFb6GV9tZ0K62U+MV1EExZo1ru7KrLL86Ig4FNugFQxZYaY0\r\n9PeRt+3aMobRxk84j7+xQ6MxlZF6zDpHlbCUBfl3o3/lqe6Wa8V5a0L9pjHg\r\nVbju8IkcUt9tcgK/nw8RvfH0Efu7QKJ361RkzI75lYsLsGStgAH0zM4YWJnC\r\nWIxkKjzPwwyION0jDYS/3Ly6IVQ6ZVxQGaM=\r\n=JKwy\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "matt<PERSON><PERSON>@gmail.com"}, {"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cheerio-select_2.1.0_1652698256678_0.6384029496136825"}, "_hasShrinkwrap": false}}, "readme": "# cheerio-select [![NPM version](http://img.shields.io/npm/v/cheerio-select.svg)](https://npmjs.org/package/cheerio-select) [![Build Status](https://travis-ci.org/cheeriojs/cheerio-select.svg?branch=master)](http://travis-ci.org/cheeriojs/cheerio-select) [![Downloads](https://img.shields.io/npm/dm/cheerio-select.svg)](https://npmjs.org/package/cheerio-select) [![Coverage](https://coveralls.io/repos/cheeriojs/cheerio-select/badge.svg?branch=master)](https://coveralls.io/r/cheeriojs/cheerio-select)\n\nCSS selector engine supporting jQuery selectors, based on [`css-select`](https://github.com/fb55/css-select).\n\nSupports all jQuery positional pseudo-selectors:\n\n-   `:first`\n-   `:last`\n-   `:eq`\n-   `:nth`\n-   `:gt`\n-   `:lt`\n-   `:even`\n-   `:odd`\n-   `:not(:positional)`, where `:positional` is any of the above.\n\nThis library is a thin wrapper around [`css-select`](https://github.com/fb55/css-select).\nOnly use this module if you will actually use jQuery positional selectors.\n", "maintainers": [{"email": "<EMAIL>", "name": "feedic"}], "time": {"modified": "2022-06-18T12:09:19.630Z", "created": "2012-05-24T06:21:28.143Z", "0.0.1": "2012-05-24T06:21:30.497Z", "0.0.2": "2012-05-27T22:55:45.156Z", "0.0.3": "2012-05-29T13:50:11.278Z", "1.0.0": "2020-12-25T18:47:01.840Z", "1.1.0": "2021-01-07T14:53:37.874Z", "1.2.0": "2021-02-11T03:33:49.024Z", "1.3.0": "2021-04-08T20:58:55.637Z", "1.4.0": "2021-04-16T11:29:40.388Z", "1.5.0": "2021-06-07T20:05:24.673Z", "1.6.0": "2022-03-28T22:28:27.531Z", "2.0.0": "2022-04-30T09:48:31.111Z", "2.1.0": "2022-05-16T10:50:56.847Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "users": {"sessionbean": true, "marc97": true, "coalesce": true}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md"}