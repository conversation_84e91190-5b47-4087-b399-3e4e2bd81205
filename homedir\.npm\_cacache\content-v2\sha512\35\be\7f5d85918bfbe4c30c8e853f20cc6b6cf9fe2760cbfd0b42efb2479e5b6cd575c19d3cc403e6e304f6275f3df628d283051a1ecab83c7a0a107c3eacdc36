{"name": "interpret", "dist-tags": {"latest": "3.1.1", "latest-1.x.x": "1.4.0"}, "versions": {"0.1.0": {"name": "interpret", "version": "0.1.0", "devDependencies": {"mocha": "~1.17.1", "chai": "~1.9.0", "coco": "^0.9.1", "coffee-script": "^1.7.1", "require-csv": "0.0.1", "iced-coffee-script": "^1.7.1-b", "require-ini": "0.0.1", "toml-require": "^1.0.1", "require-xml": "0.0.1", "require-yaml": "0.0.1", "LiveScript": "^1.2.0"}, "dist": {"shasum": "27fbb4d5184144399ffe3c63bf6613ae1b85e5c2", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.1.0.tgz", "integrity": "sha512-+4pp5bjorNgXfevjtAgi+Vydc4uI9kEdLv4tqaME3z3xxD+ivPfzHYFVfEZXIU3q1HEtrUXD70OGK9OL/MaU1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAnOOSVas+JG/W0B3m7Imve7asRmwbDwfJqEp2dWCAWwAiEAyv4r7ovcawzBU/OREoU4sdbK/okGXrplmmKDJ5+YZbE="}]}, "engines": {"node": ">= 0.10"}}, "0.2.0": {"name": "interpret", "version": "0.2.0", "dist": {"shasum": "1d485199b8a26162c9413dd3ad7a185e9ccfa167", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.2.0.tgz", "integrity": "sha512-BcLYkqjYGvYJ3LGqvqNh8+giWRJHO84Sum3QDDI5PTde+9hILDTuOT80ljslL4aXiTKoUqgd5h5uD56wjl6EMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtG+qopmThKFTSyCC5EYILe+WFf6kuEZzteBZYx3ELNQIgCyTIsvZVhCXgkbkpdp5nkVKsuBXQmOCxdiVDzMHKpjQ="}]}}, "0.3.0": {"name": "interpret", "version": "0.3.0", "dist": {"shasum": "380af3f0aa66d480464cf4194b7c5fef154ad998", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.0.tgz", "integrity": "sha512-p55Fu9SeoNdoRMF/LrtvlACztgYCYr8AVOL/vKgWvAaVRx5Ma329okoQXMGZbvAILEc73WmVJYUjpqmEm8OLIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB20OEtbqLMrtE9JAbHPKi/7yWEuPh7eSq4iEmaY3w3bAiEAnS9w9xpB4+b9uDlvJ/Wu/iOt31xIfayKQnT9MVWeDVk="}]}}, "0.3.1": {"name": "interpret", "version": "0.3.1", "dist": {"shasum": "24bad04138ae76ea1551793f4413c70f0ba8d67a", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.1.tgz", "integrity": "sha512-fLARDdhtPjbYa+KDrnvaAD/hDVJaPPrXUmMM549+19T4RhJnHlnFGGP0JqrOuCVkU9B+5aJC7WBTLk28+R0tMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAHJ16nbhPGS53pxMMIzvrcjx5KsUBahXMJcevKyWdMXAiAWAYk4VDcfHdfUK2t/8P0G7ObRUduELlo1xGWosqlHFQ=="}]}}, "0.3.2": {"name": "interpret", "version": "0.3.2", "dist": {"shasum": "134fa7a1af46ada77c3a70372820cb8018e84d58", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.2.tgz", "integrity": "sha512-TfUvK2ZBo5GQl+rr7+6gDzKzGzovlh7kQCk15g297yRXlYZaeoFw6/k5WhfvX8NpUZy6aeH6IPu6eF6eeDyDKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnTvmunBN2QjeylRr4HMVMjoBcXu3IknswcO0FLbS58wIhAOpPDNCTWXMN1ONaRZVkta2blwMNLJ568cCcGIgdYPx1"}]}}, "0.3.3": {"name": "interpret", "version": "0.3.3", "dist": {"shasum": "834e6f83dece228839d73af80cae11f05bd63eee", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.3.tgz", "integrity": "sha512-3cR2VRYlT/Zs9bApHsEFXEV6acZlntDSL4q85wHbMlQHDI08DedQUs65y0i+lbc1RWhEzjsFCvFeTZi0SJnphQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2NybyKDeNG9tUueD0EnPj54BDyL6YoKYue3/4vYluFAiAwskoUFO4MuwekueHWTDrdPowgqpaB0f3kdP2y20wdAg=="}]}}, "0.3.4": {"name": "interpret", "version": "0.3.4", "dist": {"shasum": "32cc8471f608a435d726ff27ecb79890cb20b9cb", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.4.tgz", "integrity": "sha512-pjoNi0qrPr1+wkspxn7CwfAGkvo6PkkmJ/1fBTEIHeVphX8tVExO4dXR+0Cqp6lm1OTEufgYJGCh/yvU7imQQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDRhSgU+T2D3z9D5vAs6ZUlcKd46HFLgb+cS35yHMoYQIhAOQ5IV2kaIcCzS89y4nll7jmKJA7W9oisQwmqQz5yB8q"}]}}, "0.3.5": {"name": "interpret", "version": "0.3.5", "dist": {"shasum": "0dc629e6f9d4dc05e429e18a4741880770f56c5d", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.5.tgz", "integrity": "sha512-zQlglPqkSTB965Pxp/w9TYm+Gq/dRAm8Ti5Wwq2rHZyr9JEpAHKXJn1tNSMZoWGYaaVYdRwsJcZs9RfEYHRDzw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICwXWPyB0KrZeMyyPnBM4EhI58u3z/p+UfrWipHKLY8kAiEA73oPWg4jlZGB1Q+gvBPJSMehEDDy/v03txqygdVzfp4="}]}}, "0.3.6": {"name": "interpret", "version": "0.3.6", "dist": {"shasum": "51b6927f372a92f1e4a2a5af0d14699de9618799", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.6.tgz", "integrity": "sha512-44Wq8lsPDxHTL3GFTcYQjvIbAaRZNN3c86/heaIq+QW35miLYDqXJYgIVEZFz40ACNVZlLGwARRXt4e0i1t3Cw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIECCXEdFUltC/UlH+CQyr/jfaKNa/tu9xcORugImr27xAiEAsZJRulZeDV+cfM1Nfs4RliChVEovrjxuRgaOAXBraus="}]}}, "0.3.7": {"name": "interpret", "version": "0.3.7", "dist": {"shasum": "18727eda04d50632ffa4b5eafb342b7ff398b36e", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.7.tgz", "integrity": "sha512-mMqp8GxK5QDNKWvxgTBNzlURO/aHesHtnil/G7zVyXohovtvtEDVDHakwTg6Bi6kwJpnmUdClAlTqGETc/zmRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBzSMF38/gwAyjxP5ZmI2uFjqPiEH9QnZ0Ab1Sp1oRhgIgAlD78mbPqpYOSHwJyhDy9099N/HV8B5wxm4k8RkxbUs="}]}}, "0.3.8": {"name": "interpret", "version": "0.3.8", "dist": {"shasum": "23c65e60ed18a3e21cddccf96e8a6658548cda8b", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.8.tgz", "integrity": "sha512-nM4BIrhhRKO9T2FmXENOarSqJ37Ny3qX2a/gtZbQynIPdYoNjVBjr++Bi1pTX8rMotMAIJODFoW7SC96C5tovQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1uaBwYLD3qE9hg8NpCcowFzfaRflmKf4rhEa4pr7KBgIgRIJrdCzG3/uZAGzuGYjxfJ9vyXlE3ccJj1TRk8g/oRg="}]}}, "0.3.9": {"name": "interpret", "version": "0.3.9", "dist": {"shasum": "009ae2e8fe5989656d63f4ac4253d6d0625e06af", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.9.tgz", "integrity": "sha512-b35ddLtB1tBFsLFVdLh5cwWFdBhXWLIW/+hfDl2X6qzG9kDBanNOgZK4K+d2dvVUafdCOqW3LF81TBHRg5tAyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZKbVt/BEzXOXqzZR3prtAVH6Z41bL3b8WBW651AMqAwIgSSodmNwpD+u4pkKxs6lyvdoQOr8V2RWtZK/2jTodXjk="}]}}, "0.3.10": {"name": "interpret", "version": "0.3.10", "dist": {"shasum": "088c25de731c6c5b112a90f0071cfaf459e5a7bb", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.3.10.tgz", "integrity": "sha512-HB83/0g7v0RcRptkokRR6baiePOMbj0CJxAsDlbtybiLPwMq8jlBSmvQTp+RORblS7h9UgXIUd3LL6aU7DwqxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkr8ndb7okZm0MHGX4Ny9ry8TjPSmfT2aXFZdGny0o1AIhANUuVZjveGRf+ack2/wrfrodQ4Aa3AXiLRqezx0eVswS"}]}}, "0.4.0": {"name": "interpret", "version": "0.4.0", "dist": {"shasum": "87b9fe964e8526f498e9d34fa590584c2c9c9c01", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.4.0.tgz", "integrity": "sha512-W9ERNO4A8+7BtWhM0God+4RqmxY9GS+vG6DxK9f8SYlOG4mQXZ7reusbLGcqpnpBM5IGViFMNh7c1hZK+l1JfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCODvyAth2XGpMnxdnikBfEqAOsxRdf8Owibnu6MJHCcgIgUOg2yV92O2/UHCdiAVtrIKi/y59+sX4GqiNhhrXYzM0="}]}}, "0.4.1": {"name": "interpret", "version": "0.4.1", "dist": {"shasum": "4c339b241fc3d581e1ad1c65b937c9e1a48011d4", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.4.1.tgz", "integrity": "sha512-J0hhXjnpSMgRqrupK42u/RJHJmfzXouaRuT5xW4h0r/iC2GTlDwIlrJ4NfkqfEOYBaZvHLLIHNq20LJElPAySg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHfS6dhVUs7j6W83bm0qIg/XdEYzIDS8Nn3cyKnpRScGAiAYsWG7apHpsIK1/vh3st5Oa5iAIIYjJUMedvxoYZhF0Q=="}]}}, "0.4.2": {"name": "interpret", "version": "0.4.2", "dist": {"shasum": "154e7a71e3efa0f86c8b6641b73cbece146c49ee", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.4.2.tgz", "integrity": "sha512-Ag5+bQUKFwNcsl3Za8zXFF2nKwTpdJ/aqu/u/YgCQGZ7vKOBjkIUr3UX7h5AemtoAZJkeMCkTxwVoykH6HxBBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+TDYkvfCODnLy2fdTDBWHGhQrC199TqwHJC2vubiuFwIhALYGcimitl//R8c6NuZf6UvGrY6gzgmh8DwnmsKbpOoG"}]}}, "0.4.3": {"name": "interpret", "version": "0.4.3", "dist": {"shasum": "0a1aa20e39884ae7a658388c955d810fa5f71f1c", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.4.3.tgz", "integrity": "sha512-lpFJQthx1jVkWEYf5tyOQhpPp6RGAyqIufp7L5VaLd+2C9SBPT7jcax4E4UkEXmFep/QsgsGzpAwY1qXEM9kyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAUVnnAEEfHvChC0reQDrBO3heSN3uHn0B7Ame/mzBXAAiAwF9mWqtBliq445kaHqOXmF19RnaTi3EEBrfD8RCvxSQ=="}]}}, "0.5.0": {"name": "interpret", "version": "0.5.0", "dist": {"shasum": "107182c2a06b197633b484f89af181526afdddc0", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.5.0.tgz", "integrity": "sha512-ZU9rMRsw3tQMru+5alfzljEBvleV6FS3M+0EPTHxXGKu12xIxVmRYj2qScRBmOkCqFemQmnmD4ahCFjZEx2q3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDx0BWk1DHSigm2VShkC6je83lJ3URLPV55/PGEasaf1AIhAIpVghXjWOOXqJv4XxbTYfQbomABMY/+FHUI8ujKeuLd"}]}}, "0.5.1": {"name": "interpret", "version": "0.5.1", "dist": {"shasum": "4a9ce1e8002b854ec88d2e233847e49ea0fd1fe8", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.5.1.tgz", "integrity": "sha512-UjrCnPpydK0J3G19P8qviOxIJPPhIkYPxWRcVrkbLw1/FM+smPbLgNQMLkmyiPl6bBcS+NAWoQ1UEa5eY4pnCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFAK1lgFfegOJn08/AzWQP1672m6Xq/6WhEkDjobcfoEAiEAnf6u6WYOvMC4Zjs89Nqbav7PySXaJngAQnvuUjuj0PU="}]}}, "0.5.2": {"name": "interpret", "version": "0.5.2", "dist": {"shasum": "377833b04b5ea693ff0d533380e3dda0c0381be0", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.5.2.tgz", "integrity": "sha512-xnOhtonBuNA5lcWD6yxNC6ezoF5zAOZAgVXXofDwdHKiBEuTP87PVcCq1epXXAdhTVWRy1slpkQvJydqAQjpsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEyW4jK2dPSYVcRGADGvkyaG/Uk2nSDKZx0PH+3wkNgWAiEAkfw0purnrUfMJyjbar8C0DyHDnhpfJdU1/dfnRCEvbk="}]}}, "0.6.0": {"name": "interpret", "version": "0.6.0", "dist": {"shasum": "323cacf5fbc3b5b383d3f28d69bad60d3cd494dc", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.0.tgz", "integrity": "sha512-qwF73uYBpz43LNbjK8arfwlJklbEGlN6Qw9q0jvyBEAfwiiFdla7NNeDipVqHw0xYfTHWfbmYnZ8PhkPlMh7hQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEN8DGeWY7EIubvJRJnVs7gHqjnkybNEM45zMxz3KSJAAiAjPvg5KyQeX0iplYqgMobj353Qf3HzNLBj+/ouQrS6GA=="}]}}, "0.6.1": {"name": "interpret", "version": "0.6.1", "dist": {"shasum": "cadd9dbf29d73b9c1ed05932dfac4a3f315f14e9", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.1.tgz", "integrity": "sha512-jWIfiAuhzuzFrzQn8v9FyWwFjZnlBHvvY0SscVFJCZ30MF1XiMxLjWfltsYW5QluwbjepmZuGYX97NHSEEKgVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4KWcFeRvFr/SOiuJ0O2p6qvKRtUg3gm5OjFrUeoH2egIgfEofTRGTjgQzd1ak7aU/hXoR53iHQnZKr6nNpsBkZIk="}]}}, "0.6.2": {"name": "interpret", "version": "0.6.2", "dist": {"shasum": "f76f87be2a465e00dae60ae59904fa385baa0c01", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.2.tgz", "integrity": "sha512-lfpxtbBw74dQMVhBt2LzbX+8enGy5ZSbvzaZMwsjoPW8gtICRG5W1x/dbc7Ykd58d9zp746G/UIViVv1ZDXCpw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDWrC2t6mzfvtb+aibZ6X7kUycTKaP8XDLgy7GcC23OIAiBG8IKkGEqc8hM1olfYr9B3pJgkNb1/jY5MBKu61hcu9Q=="}]}}, "0.6.3": {"name": "interpret", "version": "0.6.3", "dist": {"shasum": "4b9f40aa119bc56aa3432c04fc6e2d361254ea50", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.3.tgz", "integrity": "sha512-uzhQGVlwdOoxkV9Mgc5Ikxe0WO9sssl9wkkFGD7dRJMseZcritOqEsh26V4pfNgM7JR/u4T185K+w/IGwRJ/NA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH3tBqnMCd9lqswgPEozpT5bC11vYTI5wygnAz0ma+XXAiAyX0n1mMHvBzLBxaVu5Cf10ChF8gW3QCG+L3L69V7Icw=="}]}}, "0.6.4": {"name": "interpret", "version": "0.6.4", "dist": {"shasum": "c7625682ad0b33e1637af9d701e4f3ea187172ee", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.4.tgz", "integrity": "sha512-PfURDEVpoEAdQFYOAVoilmrkJhdeQXvzeME7b9eGiYHNDdwSe+2UX30/9rbS2Qf61H1Gq9k6fAAIrSjjAHY6KQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMKnsWQb8X9SI/6x5lAGMJfnYz78L0jiP7+SzizssGNQIhAOSLJ4bz0o9/MEkSjcYC8LY5/8G2UdB3j2tor74o5g46"}]}}, "0.6.5": {"name": "interpret", "version": "0.6.5", "dist": {"shasum": "14eead0fc076fb41142b0d3b599a4c8ff83f6525", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.5.tgz", "integrity": "sha512-iKUvWO+jnDRNowTTB9OFFcbqV+f/ecy8z8PhZ6ED5dDKj5+YbFIwzI9P1n7goDWOEuxAel7OeZTXAQoCJoRk7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLI4RSc7J4Mdxz6o0ifB2T2tA+qwA/tfBLuuqN5fV02gIgDoDw05WQvQRqBJkNdrdG+hcb7Y/nN2AFLG6Z6QbSYUg="}]}}, "0.6.6": {"name": "interpret", "version": "0.6.6", "dist": {"shasum": "fecd7a18e7ce5ca6abfb953e1f86213a49f1625b", "tarball": "https://registry.npmjs.org/interpret/-/interpret-0.6.6.tgz", "integrity": "sha512-Vg6X07U0AOZb4HF6CWHa+jnJU8j71buKQ9Pc0C75qBXgvCYbxWBkGo4jnTS3O0MIc9FZtt0mB7h+uclojqdw1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqmbN5PRHp1clGvo96tCEywaw96PRRrG9xzpm342iF1AiBudySfq+/yJLZcCQGyxKmj6WV+ZhajnIPFAoDN17uwHg=="}]}}, "1.0.0": {"name": "interpret", "version": "1.0.0", "dist": {"shasum": "2a3338fa1c2bdbe58cdbfffabcbd0eb52b05241f", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.0.0.tgz", "integrity": "sha512-gUtBc/F5EW4KblL7bE8QMsGxfY1Cp7Mmg2XxqENXsMakTOa8s7qA5ZEKBkcS32lBOUt/Yf0oYwyJbOJ+bIy8PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVLHozdUbrL+IrHVYRD8IPrJccAhBsDq1uSNj7X3oCvwIgFKks65AS3rS6pR0Csxq5FJi4O/fFZ3tpqzSNEP2dbQw="}]}}, "1.0.1": {"name": "interpret", "version": "1.0.1", "dist": {"shasum": "d579fb7f693b858004947af39fa0db49f795602c", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.0.1.tgz", "integrity": "sha512-jKzaEN9akjKsF+2T/3U/JWbSF/HqzupuTAID16eWC7sq/ZwaOd9u3M90D98wSQFig7pZ5gvTXOzTfrJH5ZetEA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEXoDpLEDbgIp47ZlRMPxRxM1p3zBKR00N/0NnkxLQOkAiEA6ToNVvoAm723hHOIW3kYrJlSmz6N1TDnnCczAN+KJOo="}]}}, "1.0.2": {"name": "interpret", "version": "1.0.2", "dist": {"shasum": "f4f623f0bb7122f15f5717c8e254b8161b5c5b2d", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.0.2.tgz", "integrity": "sha512-7OADfxT/DXVipZk94N2kHMugjokbmFXE+HxvPZUqs2sDgx/AyPrwnl/USFKBTbbEz2aHTPibBUKMz5RqCGpHqQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNrve6eKBRIcb7OZOUDkcH52jSQDwqZWtvQNpIn1HzaQIhAPIhZUno3HcpCdMvfIazDQN2mkHV3TL40kTAv8bjeLMY"}]}}, "1.0.3": {"name": "interpret", "version": "1.0.3", "dist": {"shasum": "cbc35c62eeee73f19ab7b10a801511401afc0f90", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.0.3.tgz", "integrity": "sha512-QZeLkTbMF2lgHs0JhQF8cCiJO8RSgBJ7b5ey6LIzAeiKWBZTD1LpsAXfqlONI3uw8VQS9YkQP647Fy0HRO54bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqgVkzzrASySdza2gh0fN7WuaoEnwIwRgAm5q0UGx/NQIgBBe/R+nIxZaS2yXrxWw45DaekLpGIRSRAcbO1H0Y1Hs="}]}}, "1.0.4": {"name": "interpret", "version": "1.0.4", "dist": {"shasum": "820cdd588b868ffb191a809506d6c9c8f212b1b0", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.0.4.tgz", "integrity": "sha512-SPb8MKeDGblIcyujqEdnLVwPeBpLzDKYl3g25tu7hEBOw354HsmDFb1co1QDPbVNDqZIp6m+oXbzENO3DugQ7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTj5UUSLRysbKD+ua2wdrfQ2G4Y19ahev3aBHrbYg/NgIhAOuLZTkrC/4lram92ea1myrn/FSEyN2dsinZkOcw1MPf"}]}}, "1.1.0": {"name": "interpret", "version": "1.1.0", "dist": {"shasum": "7ed1b1410c6a0e0f78cf95d3b8440c63f78b8614", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.1.0.tgz", "integrity": "sha512-CLM8SNMDu7C5psFCn6Wg/tgpj/bKAg7hc2gWqcuR9OD5Ft9PhBpIu8PLicPeis+xDd6YX2ncI8MCA64I9tftIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdfGojL9iQJkSV/7i913EbsZdT7xYMgwPpMqA3qJNcsQIhALclW3OhuwO5XpZtT8rX3tgXu9XUWkz+ebSabP5inoRi"}]}}, "1.2.0": {"name": "interpret", "version": "1.2.0", "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "parse-node-version": "^1.0.0", "rechoir": "^0.6.2", "shelljs": "0.7.5"}, "dist": {"integrity": "sha512-mT34yGKMNceBQUoVn7iCDKDntA7SC6gycMAWzGx1z/CMCTV7b2AAtXlo3nRyHZ1FelRkQbQjprHSYGwzLtkVbw==", "shasum": "d5061a6224be58e8083985f5014d844359576296", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.2.0.tgz", "fileCount": 5, "unpackedSize": 14509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJZDICRA9TVsSAnZWagAA7fEP/2QAHYR2kbr5jIidWDhX\nrKevjdULBYlTRcqcCG5vP2elm09A4hscGK3ajM7ZAYHuMpe6Xa16SDUUjxyC\neNx9C7yaFFVjDZym6CLbntltltXMNRQmnkoJjHAl9nFk43WhqL7wr1oeT26H\nrd8e7iAyypJI8qHt+IsQQVj9Wvoz557FABUi2pc8z1wRW3oJTYPaGob29MPq\ne0ZSnOnzLt5fBP1lMBcBeNyAqZg6q8rzOpebJ1pdWU0AaNuxNJJIAZKANALa\n93Ga5B3mkq+utXH2gnC/AnRBB7Xct66m8XVzLwdsYuKAXRp5TdKu4OF3Sdb2\njW24vK7/CK8IMkEGyHP+rkdax4SYeusk8WpdplasIALeYxgJuXFn9Gu6OR5B\nhhL9AqJM82YP+0+tZROaGSXuQcFBd2F6xyAF9J118EihnB1go5xySk1tWUqB\n9VA3EP/oE076Fj5V0Shb4zssk2f4HrRpxJ8olavStgoc2BK4TseXxsTkyI1q\n3PPeY9icQ03thH9Ow/blx27psxGZIBdGqaR/juG7V/t8+xfJj8RVdyf2gvUh\nHVASE0a1Aw45aHBcH5AHE9UnxtkSAGGwA1R60mcQOh2BGCS55Qml2YqyNykL\noB30lCluwjCsn3jL+wV3UbBXvkKcLaIvBbrhGY7Eq540zFPjbdg7mkfeGIYe\nOOrU\r\n=ztpd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiUL+t9DacgXogypmChe1/SATueHoT8NN27zsy6QfnMQIhALUAbL3DFSDTpKpkBeTYyhckKZ4UwHnLj4rxfVTuUzM2"}]}, "engines": {"node": ">= 0.10"}}, "2.0.0": {"name": "interpret", "version": "2.0.0", "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^3.5.3", "parse-node-version": "^1.0.0", "rechoir": "^0.7.0", "nyc": "^10.3.2", "shelljs": "0.7.5"}, "dist": {"integrity": "sha512-e0/LknJ8wpMMhTiWcjivB+ESwIuvHnBSlBbmP/pSb8CQJldoj1p2qv7xGZ/+BtbTziYRFSz8OsvdbiX45LtYQA==", "shasum": "b783ffac0b8371503e9ab39561df223286aa5433", "tarball": "https://registry.npmjs.org/interpret/-/interpret-2.0.0.tgz", "fileCount": 5, "unpackedSize": 16910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrl73CRA9TVsSAnZWagAAdT0QAIZjDC3TWAtaUbZE5dUB\nVKBD4u77xemTuhGpMUZJ25DBqlYATg/ixhBPbusYcf9JwxtlZr5taOj4F/wg\nMSZzPOHi6CAlPljNaW63iuGytsTVWBwbDUU6PDe4kHNMrkwnUORSL4ssRnYN\ngP5+hzKCxJx6BvSNVBzOneyD1SfbkvrRusXHB+Jri/6Ez+OqxCfJr8aJTcvj\nIymSHvv+2V6wTOutLWsOtRHh+EdtkWz8QBldTWn36YuXTpwfg2TDxAr4mbDx\nQSfWXWV65sBvtCyTJNyy6sV7G8/43INPaD1O7LlXmwvMghqhL07pt4w63rv2\naekopv87qDibBBhezfL7+GDG5MySXGnbbPMWHeloUOj60BlGZOIh4BgnNqBv\ngBbibj7bhUe/WWPKCK2g+oIOGj0b8Q01GSrDr9HdZhwhUBBxD64X1mGzXHvX\nvWH3QJJ1WKACkpxEWAeIts5PUa9ZJ6Pt8theMjBCqXO1L8R8jWgZ7ABTxQKM\nK3tEhIcdeiGoIZHCfG8V4lU6t48421ZL/Lxet1/EJWLUD36EggggzyksS4eR\na+ZA6/tuxeweVlfxFNeZb+jb6bKNTq8EgIG8Z9DhT2LUAKqPd3Dzf22JI4Yg\nRUWXqGGo+LqtMHCDpLr7y3c7pVgem8CWWtIL1VDhvDOaqA1b07Wx8133vRpc\n+v9a\r\n=ltSP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCB61AF3KJC4KsQPVJDHdEPevCR14rggsYF/sztAsQCZwIgYo6a8n+eDVIBqUy32J8VQiITtzpiO+ExsXE8cRszI7w="}]}, "engines": {"node": ">= 0.10"}}, "1.3.0": {"name": "interpret", "version": "1.3.0", "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "parse-node-version": "^1.0.0", "rechoir": "^0.6.2", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "dist": {"integrity": "sha512-RDVhhDkycLoSQtE9o0vpK/vOccVDsCbWVzRxArGYnlQLcihPl2loFbPyiH7CM0m2/ijOJU3+PZbnBPaB6NJ1MA==", "shasum": "6f637617cf307760be422ab9f4d13cc8a35eca1a", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.3.0.tgz", "fileCount": 5, "unpackedSize": 14638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1CrdCRA9TVsSAnZWagAArD4P/Ajw9diD0C9CLdCOR+3o\nhh1JT+GJINMrZm1IrVQwQ7p/gdqzB0VnUyUBEC6UH9iLdXGti3/Zg7lCGs4u\nO14PX061QfBvBlxVAOv1xQf1fNEsIIdwnJW5WxJ2e8ffgS7KL/kmrcxA8Pzt\ngFejfaVDApiL2J1T3CzNe/199+IzdRlIS+i1IihsjqVlPLj1epnN9TE60Mxa\nB4uaaPJFUpMW45ETq7aJMdPP4Jt8Pq68PhcJZF6FuZc4TSjD4XQrhGh0cVmw\nCaqZFuu1KXe6IcBcRqnQDdgt+ubbhojGoFC9ouJ0s1cuvbcvjLrlXQCg8SmS\nSw3QJ5d7NsT82jrWTGP+h3su3ramouFLOVaYqNmL9YW2X1IMLny/NLYW9zGe\nDTGt+oXWiW+dXLfwTw33t92EievB5XqmWD/KyyGxCuUinSQfbgzGYI/Wi53O\ns/BGr4LQNgWzmsit4hdaO7uSDrTzlNLnCeIOJ+rd6QCucIfSPS/bm8xRXNGb\noi9wmJR8C+Cs+YvuK6xLkWqs46uIcDowT3ihvra/0HI2pm3bWTVM5yiOLFcd\num58/qlwTFFk+NW8ts/bd1RMMxuvteyPZT9KK503CUT9N+j3g8n/0oeCTIDl\ncantO6RXOVImEWmKqTaNsgWvNhdO46rAW4AfubHBRJIkDn6I0IbMDXemSh9l\nvTpF\r\n=M7ze\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDopMMkeBtUPEOj+u3fuCCXg4msXxtzbMyfqEta12gT1AiEAvuvkcJ+7Ft9ycE1U0962xntNndpZmPeOZp2j2QhHGHU="}]}, "engines": {"node": ">= 0.10"}}, "2.1.0": {"name": "interpret", "version": "2.1.0", "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^3.5.3", "nyc": "^10.3.2", "parse-node-version": "^1.0.0", "rechoir": "^0.7.0", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "dist": {"integrity": "sha512-tNVZ/5HHkv5Bhp4zIF7YMXKx43/u74Nnpu9fmooNpf+4tc0ARZypBvHr6Mg6zvlltWuWPn2NeQJS5zpaNyD3RQ==", "shasum": "3440c40d660bb66e77a5953cf472f7d160ae45b2", "tarball": "https://registry.npmjs.org/interpret/-/interpret-2.1.0.tgz", "fileCount": 5, "unpackedSize": 17039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1Ct/CRA9TVsSAnZWagAAkiEP/jjV/Qhhzg5SlMA8Rl0P\nq144D0Pp3tZUUhujHdzUvLjqyfBVJepHMewN2/hcaZOuQ/FhuytQrTacxZ6o\n1/ahy6BRysYrVq/8MoHA9pydKvmpHNwMKPh5ilIf8T05dSTF7CS/fvTwhLcF\nsShKvDSyIlS3Rx/syb9c/4VVOqvs6UIHH4MPD7ZmTjosEiDksOMGb3aJd90m\nwWVFCs8ES7i9+HDMrVbbyPPPEO2pffC3/+72V37G3b/YUczD104OOETjDEs3\nW/4yt3W1ueZKhyxaE2SNFqFkFe+7XPI/g0QIapOfwvpmFqujqQOjbWJUkGxc\nJTi9+rpJ487cjRr5tlsODmSX6T3HO2hFuh78JZDgPQkK/8HRu8noiZY2XoTN\nJ2W/0RL4LpceTc/OxYKWTCe1We7k8oCBMO1VzO3ZqAkUvrx7fQ2zyGKrUtS9\nEKjF3qFiO3+Z8L2NRO11lKnAoGQ9FW/XWB3tg5N2kpaUpS8D6SFXUPIBwwDt\n2TdZui/VcSAyab0Mz9Zxd3LctfeTjr+wDIh0XjBRnCRsE1g+B411iWyzQkTB\niJ4GOCGcF0348AFKRNOIOwKqpsgV7aCf8OMxUPGalpONTnTqB6CkJ4xanPf6\ndUe63vl6i1KEqDbUVuLgNAkIg84JsWwV7zTdz1UZ0n0gYy/Q3RxY/nDSfJ0B\ndGsV\r\n=chdx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPjApNZCaHWc4HX993nSn/ikBn19Sw80oTYOYcgWm1dAIgIl9vGmbU97E0faS+Qtg7biidofUcJfgTE5LTTHjf0pI="}]}, "engines": {"node": ">= 0.10"}}, "1.4.0": {"name": "interpret", "version": "1.4.0", "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3", "parse-node-version": "^1.0.0", "rechoir": "^0.6.2", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "dist": {"integrity": "sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==", "shasum": "665ab8bc4da27a774a40584e812e3e0fa45b1a1e", "tarball": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "fileCount": 6, "unpackedSize": 14852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe10WXCRA9TVsSAnZWagAAef0QAIOaRpvxTi5XRQ77jHqq\nQ8fX5qH3W4CcWQvjuyNGMqccC1B1SbS4XEuUi8iOrk+xb0N1Yk/jJsZW55Aj\nbp8dMTP45j7Lmg5S2NXOiUhQCdLdCX/k8i7lnHSwyCzo8STql+6Hxu6mYtOU\nGJ1Ckd9pTMAlZtFQZf8GA8rrju+J9zLJQoiLvlhb7+NxPu0KHpIab5VwgPQF\nNnjjbRpG1aSnWh9jolLs9h05/t+KNPqgHs4v62bt9jjOq8d5l+DiGZd7B4gT\nrADiDXNrHtDGN0DbDo6toH0/CG6JUVYsvZG1ngTCBqBdI9LiggNE1YhqidUC\nR4+XzFiL/r4dfkeJSHOnAyEqdj1cg9413oRDXhq9l3HvA0Sl69UKYzs6IctS\nhY2UsJ6KZSx2Uwc419/p6B9XRxo/f7BanBi8XNo6sGdbQr04TGtWazW4penl\n1gFW8igbiqWK+CYwhTogRMUWObqXcB9qQSN8P97YWjM97iBSvyNPKV+9aQYr\nM8rU9L4OMf/CLiQxoS7mYg3ydHqrKH4rwbZMl4Mt4wAvfBcSrHTGleRkkNPU\n5JPW65UH3GG1/+hnnEEc22QyawTL1IUfKphToy10EB26ZuBUT8a5AI/+kXPX\nixGGevhMfM6wtUyX+R4NbGLvmP4dw1tJJueGaiy4CyRS7zWAG2XEjJRvsRxJ\nCjXE\r\n=ZlrL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICnZgxacxWs+ZQRfpu55BaOBcOlc6IhjYeeW7sdULxbpAiB8WvLgbWxMEzAKBqLiEWrZdJdrmo7ZNHtJqZxNL0yxjA=="}]}, "engines": {"node": ">= 0.10"}}, "2.2.0": {"name": "interpret", "version": "2.2.0", "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^3.5.3", "nyc": "^10.3.2", "parse-node-version": "^1.0.0", "rechoir": "^0.7.0", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "dist": {"integrity": "sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==", "shasum": "1a78a0b5965c40a5416d007ad6f50ad27c417df9", "tarball": "https://registry.npmjs.org/interpret/-/interpret-2.2.0.tgz", "fileCount": 6, "unpackedSize": 17224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2v5cCRA9TVsSAnZWagAAK7gP/24vmy23mtNNqxkFtMZJ\njJzjo+lrx9Y5zLGBc419OyC9ncrK87DhLMd5405nhNjdCsSshYszfzdH7RaK\nTniKXLhwDHqIKn8mXHFqvIvZu4sEC2hRWA6ZWCEgcOPBUfLnTGIhOfghSgs2\nFIZO9wLZJRdCpKZNVM955vCvDPeri/WIUcdn31IDRN9hWC2E+fRkexw4rUDy\ntmo58AsqJrWfruVc1e78HnjgdfQ3GqQvVaS8s34mLFxJudJCeOQlQnXeavWF\nGYMvSgCPvfNX2qEenWi3KAPNdATm3Jk4z64jdBR9/eZYDxk/RtHnoxqv1GX9\n6xFcp6ckkHQsBfNBHcUaKELynUwIgiXr2dhXCbWyJdIRWHVCXeSNAszkQGGl\n8sSubZLyPFw9xVwLZ4IZ/W3vZ8Cpepb5fvAQhRpnpk6wiI4ymUfS5a7vo13S\nH+6R0a9oMQSYGZSlyXjZNxdX+iO6xk0/lY74wnTX5sFPc6nxrbKM6gIypCJm\n+5Z40+aRcZe8GH8w7AHuTn45QwCXpZvds/mf1787+2fChwD6KLjD5wxsCkxd\nmI0SOjbhijKxiLZClVXIFuIH5Ro+yKmwWaopoQ2U6hm1/DI6xb9zvD/gXJWr\nNdbhmjj/qHgyHwpZpjE1RcwTdVcU5ZC3k7d56vAsC5s6rN4OuswgMNsBfGwG\n+FM1\r\n=3GaC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCASkWjDY7Wqr8AAeX5UgCJ6q2z27cX18NBo6xpqUvSlwIgdaX07v9KecL1hbd5em0TIay/IbxtdVpS4HAmGAOBsAI="}]}, "engines": {"node": ">= 0.10"}}, "3.0.0": {"name": "interpret", "version": "3.0.0", "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "eslint-plugin-node": "^11.1.0", "expect": "^27.0.0", "js-yaml": "^4.1.0", "mocha": "^8.0.0", "nyc": "^15.0.0", "parse-node-version": "^2.0.0", "rechoir": "^0.8.0", "remark-cli": "^10.0.1", "remark-code-import": "^1.1.0", "shelljs": "0.8.5"}, "dist": {"integrity": "sha512-UwJQtzlCPth4JBGxUrudwaV7UKDZehgzKaKaV5lYOHoUs4QcbBBqdZ/TCLqA8roNfwGvQZocDWdImNfbSIM7bg==", "shasum": "f6d59e2b047ca84d4f88b8b3e0d9da8158ac0966", "tarball": "https://registry.npmjs.org/interpret/-/interpret-3.0.0.tgz", "fileCount": 5, "unpackedSize": 21345, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH0ITBX+lx+w3A/j37cAPkn/uVNAeBLIgObKQivgsj33AiEAgy1uUqc5CzFRbiRht+h0tX2hwkkl/wB4Oo2+ozNb1jY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWMGlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9+Q//UlOwNaG8/1YywDdP0OP/qPo1prMfVP06axmQzHZpbXGlZi5Q\r\n3NknGTpYdQa6SIR9mImUbWmPP7EllTf4AkeMzsrbPJej6AmrBR8smNDvsDdv\r\npMhHs14YMGQqVGwSv4rkJsBPejKl//v8acYj6PjHTrCLoiURa5c4IbaNN0rj\r\nELWpIIgxGGmusZkIkDfaAv0i1x8ZCwc1+/YM+HOw0QgECoj87wnAJDW083zx\r\nPcMPYbCTNr3YsctaWR21stOES8Nli7ix2/xv3YnWPxz23VNqSEMHz/ck933Y\r\nhynklSp3FuCve13JyNaIV0V9HqWV628u30Vm57COKZFOY5KFF3A7mLl19E1k\r\nIrFqWV1RmOidQZFMynHm4K5rT9VEJcWvnrSZjnwyQbufuidpiumWm8q19TdC\r\nmdSPAkiBFQAn6bTSAfgIq6CWGRuUhZ0ydAnDHLWyyjoejHeW5XucF02lcdRt\r\naCA5y1L/+kaM3f+/oVSAaUtollek5UVuE3WQMlGNYw9qcAXhtAQ0ZcsqJhbY\r\nrSm/aoXjM8s6sU2PXDxmNMvWVZsWoKeacL/Vm01nw9u689bGuv5/ZMyIqIfj\r\ncsM/4fHkiL4Xl8Ia8I6jDa4sAh/EqYnwTaEBDoxVGT1TRHX4UnjYH/bmlePr\r\nEV7R8sbRdeZ+xyn9B0wKvqSPIgwt1fWlJLo=\r\n=qzx+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "3.1.0": {"name": "interpret", "version": "3.1.0", "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "eslint-plugin-node": "^11.1.0", "expect": "^27.0.0", "js-yaml": "^4.1.0", "mocha": "^8.0.0", "nyc": "^15.0.0", "parse-node-version": "^2.0.0", "rechoir": "^0.8.0", "remark-cli": "^10.0.1", "remark-code-import": "^1.1.0", "shelljs": "0.8.5"}, "dist": {"integrity": "sha512-opvgRqTb0qZLdjAM8pMKMteXO8O0hwI2GJBixPyx0EWJ2DWTN7sNt/UIJiloWd2Ww5OFOHsNbcEZzEiOHOjuOQ==", "shasum": "2d80316c1a6c9f52042bc02d3f9ef305a0ecf907", "tarball": "https://registry.npmjs.org/interpret/-/interpret-3.1.0.tgz", "fileCount": 5, "unpackedSize": 21404, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7CwnYjXO2gUw909wzC+NlufMaNQg5KhVtautWVBU5igIgeU6B9W2m8QTqtJucUQAMs01T3Rr//bMXerRN/y5OTyk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivKh2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRkg//XrwOmIe5wSeajWE/ajJT2SoVi1WmD/feW1OP/z1Hb8sKRfig\r\nbYXXWFUqJCu6+vNmtwbnizbavLXK3ZZgXMPoCN2tSStLqhLWXMNwlgwLW3IE\r\no5kQa0pn5/W3rhpvU4hsMRd6+Gk/2gvTDtQI3VzoWOp0eocVzZozSjl7381q\r\nMFKCFq3zRuj/xKvCpsSHwPZ/wL2o5VbIqxUD55g6ffC0wnpCSVJX+vhYGFyv\r\nWu9sqKDgI8nK8fjhutgol1AQrIgkPw9lG88znQDk/ZAEEMrlW7J3YjH/z6r6\r\nZ/pvJdFgZw5pICzLoFBGjWKPTzmESuk+rqbLHLN0Yp/I0xqRjFopMnfwBb50\r\n6TeQWZawDvtJnH/2Lyu7oJfm/ipgODYK6AdYONB1efUVY/kUXTohAnm5GW9c\r\n1s5pI+lHM6NHVPEac3AO3NBePA4OMGVzbUmyzHjbNPJ09ridYJqg4O4O9UCt\r\nv7mUyNl6YbsDO3fzW1Js7pBZefUqK4M5dz1Q5fyYKGBebDANCcKT2zZEooch\r\ncIG7pw05y/lJbHoOeLTJLNpU0G2/LtP3m8vgt2GMu4XZLkozE54aKTfnVhey\r\nVN1+FrWKzzxWoqFZVNOTiYYRy5twsip0cm3rVKW6SlWzVKiSI6XJDjuflLAW\r\nb7bzLsZypCDbVi44y2sbTWCpDqSqETCNdXc=\r\n=IWMT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "3.1.1": {"name": "interpret", "version": "3.1.1", "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "eslint-plugin-node": "^11.1.0", "expect": "^27.0.0", "js-yaml": "^4.1.0", "mocha": "^8.0.0", "nyc": "^15.0.0", "parse-node-version": "^2.0.0", "rechoir": "^0.8.0", "remark-cli": "^10.0.1", "remark-code-import": "^1.1.0", "shelljs": "0.8.5"}, "dist": {"integrity": "sha512-6xwYfHbajpoF0xLW+iwLkhwgvLoZDfjYfoFNu8ftMoXINzwuymNLd9u/KmwtdT2GbR+/Cz66otEGEVVUHX9QLQ==", "shasum": "5be0ceed67ca79c6c4bc5cf0d7ee843dcea110c4", "tarball": "https://registry.npmjs.org/interpret/-/interpret-3.1.1.tgz", "fileCount": 6, "unpackedSize": 21458, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmQa12FpO1G+GKHbNdDRGf46+ukNYtGRnP1UaMfEy8vAiEA8h1rGBUPk/B8M+EyctcXq8SHp5TB64x8LZQoRIbhYl4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivMJfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFcQ//ZCe4hjNL1QshHElD1KfLakCWjiGGzXSMkP9MYuqnXcjUZE35\r\nF9WRW90je9z+vd149leHF2rZipXHsgpDNY4WpDzywtdBMJCInWjuQk+ch15C\r\nPssFyvGkAAWBgikJF3Jqv+WOvlrTvkASA7R8n5iahfNmF1BgwnVx+nGatRHn\r\nFWA4qzgEndf5qnOqJJcw70/WfBAQW04pvNiHxQcIu6SIP5q7ew71J6CruHzm\r\n6haBmwhnnndjozcI+bienNXhQ3Q2dGtf2bBiZ3sLievLYz2nO7A2zsbY3Ti9\r\nWN5XFK5kYA1k0FE7dD6Y6botyx+/YOudVeJ4TVjGmdrzNHy0rgxG9eMnfbsr\r\n46Lwen5FGtMQZeo0tpx4s12PzPTsUNGbyZ7Wgc8PzQSf++/j0ysS9CED19Z4\r\nnAn/4AIbPJoTm72OXSO6Lq980U+G7/6ZGcqUA7jP1DdW7om0lIrpvcqrjJVW\r\nBLjgFH7syetpwzgWOaAsfHOaepHkFoBPQ98HyYHq6VtAWxlzTaXwUI/eYgL1\r\nw0td/GiqlNhw17mGhR0SWSC75jwZXy/ZxE6xoAMn2PRshgfUFCwDLij20JFb\r\na22UoZF7WYuc+XqnCtZ9JYGWtTlkpoa/v6T6tAZPUkJJBTlgs5PQj1SttLQw\r\nWSgEQw/AgvliUF0aAm3HHMzG6p2SzHKaBns=\r\n=CthZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}}, "modified": "2024-04-04T23:53:40.812Z"}