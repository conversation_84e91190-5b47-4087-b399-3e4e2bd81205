{"_id": "clone-deep", "_rev": "28-9e4fabd9afc64c92bd46a5e810c28199", "name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "dist-tags": {"latest": "4.0.1"}, "versions": {"0.1.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/clone-deep/blob/master/LICENSE-MIT"}], "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"chai": "~1.9.1", "mocha": "*", "should": "^4.0.4", "verb": "~0.2.6"}, "dependencies": {"for-own": "^0.1.1", "is-plain-object": "^0.1.0", "kind-of": "^0.1.0", "mixin-object": "^0.1.1"}, "_id": "clone-deep@0.1.0", "_shasum": "97c4903824f1af8db9dd455ee48662929d265cd7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "97c4903824f1af8db9dd455ee48662929d265cd7", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.1.0.tgz", "integrity": "sha512-iEur8mCzm1BTNOmQCEaX8HBBaqdFcYmzRe20hP1W9AHU7k8pWx3uzYoRgjhWGI+UI0Qs4aN5hwxSnz0mq2OMyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICe5D1Si7f2o/PI9/x/C/2wKSLOjb/TH4Div/eRAVW10AiEAoS5Y83P7SLZx41GZPp96FS0ZMwCFPa+sHTEMMDZHQ6Q="}]}, "directories": {}}, "0.1.1": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/clone-deep/blob/master/LICENSE-MIT"}], "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"chai": "~1.9.1", "mocha": "*", "should": "^4.0.4", "verb": "~0.2.6"}, "dependencies": {"for-own": "^0.1.1", "is-plain-object": "^0.1.0", "kind-of": "^0.1.0", "mixin-object": "^0.1.1"}, "_id": "clone-deep@0.1.1", "_shasum": "df1d5ff47582788e8b8629a4ca8a7fc4f34fa8cc", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "df1d5ff47582788e8b8629a4ca8a7fc4f34fa8cc", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.1.1.tgz", "integrity": "sha512-6O5sRiLdLD2ggz6IjwFxeKMj/wTF/w97gV64d0FzzT3TNesPqtcgW4R7NOZsp69ARy7IKgeN4M1ZqOT/hz7TYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChj7KpV0qMMoiNBdAmUJSOiMquXs6FfS6Z8k1vfD9leAIhALsi1ky/P2FGsld9P43hnV3W9WrbgwI/2mS3Qy6NzTbz"}]}, "directories": {}}, "0.2.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "mixin-object": "^1.0.0"}, "devDependencies": {"mocha": "*", "should": "^7.0.1"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "gitHead": "a6e407acafa803dacd85ea7d0f0ff4a548d58c68", "_id": "clone-deep@0.2.0", "_shasum": "9f8e3becd32ec332dcf722573d954a025615c995", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9f8e3becd32ec332dcf722573d954a025615c995", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.0.tgz", "integrity": "sha512-syAvhsRYJutufLoIzW8ZGgyNpqFMJhFHDjYq4nUGaM0v5gxsQJYPGfPFOq4RMRwt4akjYmSiLgIYeTdz9+6b1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVrELvaD7m3nVGeCTBaxRfmqRFfiJOMxc2vbGOYEm06wIgYMuH9dhxcfcafwkMucNArSPfQqpwET3+1hikTRQOZKQ="}]}, "directories": {}}, "0.2.1": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.2.1", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "shallow-clone": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "^7.0.1"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "gitHead": "8df93ba1e29607250c7d758d31a963c992f7ab32", "_id": "clone-deep@0.2.1", "_shasum": "791b49cfaacbc9da2a3b931199c677a8d5d9b236", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "791b49cfaacbc9da2a3b931199c677a8d5d9b236", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.1.tgz", "integrity": "sha512-W0RDPPM2q01hLyLnLbx2N0RnTK5iTIfWscTz3q/LdgkTEP/g3y6QlpX6kyhH1bXKIckAYdrLKQA+jCVUO4ZCUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCU3fJbQnM4dFStt+jzItvDGCFaKAv9/1v61FRUxS8AHwIhAIUjV0t/0of7WTCSGK4N0xZhYqwy0ZqqCuMr1CxEWEOg"}]}, "directories": {}}, "0.2.2": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.2.2", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.0", "lazy-cache": "^0.2.3", "shallow-clone": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "gitHead": "7016257d977f54043b428098dfff9b8ba44418f8", "_id": "clone-deep@0.2.2", "_shasum": "6b263e5c8e2bae6b793ed3e2dc750a2de69f98a5", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "6b263e5c8e2bae6b793ed3e2dc750a2de69f98a5", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.2.tgz", "integrity": "sha512-f1VmAXy6JJz2SQpoFcIhnaQRKzuFgXfgtMNd9Oi3XE6JeJFJwvrBWX8lR9G5TOpjsUyu+tU1fKo2wCZXYQuIgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIsJoQUqE+/Tgg01tglr+Ew/BY1Fv96ahP444HdfYyeAiEAh6nsf/cWlW10POiFh73ztR5gWv4XWVWEMPqnV84fEOc="}]}, "directories": {}}, "0.2.3": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.2.3", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js", "utils.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "shallow-clone": "^0.1.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "gitHead": "1fdd57f537ac7f44c8f864693ccd2d722149f3ce", "_id": "clone-deep@0.2.3", "_shasum": "42fc31f753e3e3d82c4a4e3ebc7514314c7d65ed", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "42fc31f753e3e3d82c4a4e3ebc7514314c7d65ed", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.3.tgz", "integrity": "sha512-tjCqSb0b2pGnCZT7pTaHRxyvXVQeAunsViCw81OBEU/n77Y+Ci4aGoOLI9oWR6JUWKuzFFlmjrMAnLggljuwcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFr28C8R5WDkb8NmUjuUBJSFzcD9HGISh4/SYWDym7WQIgU9Ky0Hvsvk2AEVbsTcsHEFr2OEGGJFZf0LTvMzmj0QA="}]}, "directories": {}}, "0.2.4": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.2.4", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js", "utils.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "shallow-clone": "^0.1.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "verb": {"related": {"list": []}, "plugins": ["gulp-format-md"]}, "gitHead": "8ab50fb93db4eb9288a6a9b6042dbe3e9a1ac7fb", "_id": "clone-deep@0.2.4", "_shasum": "4e73dd09e9fb971cc38670c5dced9c1896481cc6", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4e73dd09e9fb971cc38670c5dced9c1896481cc6", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.2.4.tgz", "integrity": "sha512-we+NuQo2DHhSl+DP6jlUiAhyAjBQrYnpOk15rN6c6JSPScjiCLh8IbSU+VTcph6YS3o7mASE8a0+gbZ7ChLpgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8LAx2hOkua6rXCH3PRHVdjzi4qWZbxvHSBseCBFx+DAIhANqN1TFTaJcZlosYpNdV64A8lXQlwMQhQPffGuKNm0e1"}]}, "directories": {}}, "0.3.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.3.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.1", "kind-of": "^3.2.2", "shallow-clone": "^0.1.2"}, "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.4.1"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "gitHead": "a5ca6b095426a4588a9da1b5e08d30494df82e6f", "_id": "clone-deep@0.3.0", "_shasum": "348c61ae9cdbe0edfe053d91ff4cc521d790ede8", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "348c61ae9cdbe0edfe053d91ff4cc521d790ede8", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-0.3.0.tgz", "integrity": "sha512-qp1do6NplYZZcLU+TQ93+A735g1kNcnH75QbC1olpKJphe36CqkjFEK0PyFSoZlI3/1jO5KnDg64xdwWiEH/zA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDR9eeW7sa3aKZxrSHRqfPQxzEyuCG5tohd2lHXQrMYzQIhAKHZQa27n0A2QXQLdz6BsVnGqYpXE12LdO5AXNK1faya"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-0.3.0.tgz_1495217030158_0.3240076475776732"}, "directories": {}}, "1.0.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^5.0.0", "shallow-clone": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["shallow-clone", "kind-of", "isobject", "is-plain-object"]}, "lint": {"reflinks": true}}, "gitHead": "b033c5671c6a807b8b32bc2a1f66287482d3aee4", "_id": "clone-deep@1.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-hmJRX8x1QOJVV+GUjOBzi6iauhPqc9hIF6xitWRBbiPZOBb6vGo/mDRIK9P74RTKSQK7AE8B0DDWY/vpRrPmQw==", "shasum": "b2f354444b5d4a0ce58faca337ef34da2b14a6c7", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhqRcyPH9zFjp8m2fytLFfDaa8x+OGSoH2qzV95rzjJwIhALgHsypGpBl1dXlsr+htyooNBwAecT4SK9HhyOdMm2mJ"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-1.0.0.tgz_1500207488086_0.801335611147806"}, "directories": {}}, "2.0.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "3.5.2"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "767ac5b573d04ca692e43e4b31ffd10908e5a722", "_id": "clone-deep@2.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EkjnhytZJ3BVXm0bPwiVO1CfhzE8+K+4u3SUT232FVNqQlHcTh4zrvffAMPWXDdEOwqP3fPYm31oZ3wqqRJvEg==", "shasum": "628211770a402b0f12641cbe46d185db9053ed1c", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5Sgov/4Xzxo/2i7SVJf05ZJYI7oHujdHxBSmVQXEYiwIgIsyuWEQ28AKcCbe+ORyivJ+bGTqNs529iVRd5wkvqQE="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-2.0.0.tgz_1510893594716_0.8107468553353101"}, "directories": {}}, "2.0.1": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^2.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "3.5.2"}, "keywords": ["array", "assign", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "date", "deep", "extend", "mixin", "mixin-object", "object", "shallow", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "f824399ee350cc4d3f245ea83a1677e0895ff310", "_id": "clone-deep@2.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-odS1ucsnBgQ15htQZQ++HaoO/T5lbOKTL1pRlwdUv03T8vhyNTBZjr7JIZfQhyYl0rBIT+gemHDMSPaXMK6/CA==", "shasum": "661de96d4c6c61b5e8a29c3334941a0eeb132cf6", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDg8hexv7INcNV5AJWubZ0bhbu2MVD3zgGmVEzwyruIPwIgbWRZqfTNKWLyktn0XMN0T/v2kGz4XV91zGe8ESwOWN0="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-2.0.1.tgz_1510895532407_0.4413618710823357"}, "directories": {}}, "3.0.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^2.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "assign", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "date", "deep", "extend", "mixin", "mixin-object", "object", "shallow", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "67da6ea9b7c332077f98b1b67637ca79ad6d73c6", "_id": "clone-deep@3.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GTgvzzSrUEt1vDfEHXeBiGr24dmuxnGHKYCUx9+FxTahF9EuOtkcw3HUVDqHIs86BJoca532OdRWCPTXbsgKRw==", "shasum": "c42a3c3489bd82d0726467bede3ccfd3756e1227", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIKJf4sClYyEnUPPwP1Ar9QWYWrqvh6XbtwsQdQrVBcAiEAzMcQCms0jqgwtUl02uvwG1f2KHTEqnHSYYPDXEK5h4E="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-3.0.0.tgz_1516338336337_0.07103158067911863"}, "directories": {}}, "3.0.1": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^2.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "assign", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "date", "deep", "extend", "mixin", "mixin-object", "object", "shallow", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "04d19f176662beafd9f1c1b81a63ac7696ef3f0a", "_id": "clone-deep@3.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kWn5hGUnIA4algk62xJIp9jxQZ8DxSPg9ktkkK1WxRGhU/0GKZBekYJHXAXaZKMpxoq/7R4eygeIl9Cf7si+bA==", "shasum": "7d1a4b88a3cf0bc2da84696ba712b349a6506a44", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-3.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvHd11sLEPuGNXmwvJEyDQTngQNDVll0BmsfulJ+Td4wIgYL4t5iE4kVy+uXRjqGaJRLSxXX22aCB++UtsRQuRsGs="}]}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-3.0.1.tgz_1516338524825_0.7630828393157572"}, "directories": {}}, "2.0.2": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^1.0.0", "is-plain-object": "^2.0.4", "kind-of": "^6.0.0", "shallow-clone": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "3.5.2"}, "keywords": ["array", "assign", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "date", "deep", "extend", "mixin", "mixin-object", "object", "shallow", "util", "utility"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "5256b34f4029a863a20662ac44c6f624b622dd25", "_id": "clone-deep@2.0.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SZegPTKjCgpQH63E+eN6mVEEPdQBOUzjyJm5Pora4lrwWRFS8I0QAxV/KD6vV/i0WuijHZWQC1fMsPEdxfdVCQ==", "shasum": "00db3a1e173656730d1188c3d6aced6d7ea97713", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtdgxupY4JuWvnwB4XXq+ymELvHX5RZOh7hHifTP0/LwIgSRP/igAAF3CY7qfiGM03sQcJKodmjc+9jJPBu3aL/hk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep-2.0.2.tgz_1516404843002_0.6002764257136732"}, "directories": {}}, "4.0.0": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "assign", "buffer", "clamped", "clone", "clone-array", "clone-array-deep", "clone-buffer", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "date", "deep", "extend", "mixin", "mixin-object", "object", "shallow", "symbol"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "e5735371900714e976a369a27cfeab7a1991c887", "_id": "clone-deep@4.0.0", "_npmVersion": "5.8.0", "_nodeVersion": "9.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aNJ5/7Bz2IYBb7nIj34TLGk78lBXpXUgV9qsLngtTvJ9+scsZNnlU0OX2S2N4ax/sUQt7sDBkXiGjGJEmNbXOQ==", "shasum": "a41ae54db9048b407d9c73e703297a12e1dfd932", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.0.tgz", "fileCount": 4, "unpackedSize": 7393, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBBwds4tkhuMPnlWEoEa29pipvMQhWJVu5T+C5LT/wD3AiAoMYGkxj0UWp/lcbeyegqtOHHWf6hJx2SkyEMTiJoLFw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep_4.0.0_1523343881408_0.8365443462498223"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "4.0.1", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"test": "mocha"}, "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^5.2.0"}, "keywords": ["array", "assign", "buffer", "clamped", "clone", "clone-array", "clone-array-deep", "clone-buffer", "clone-date", "clone-deep", "clone-map", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "clone-set", "date", "deep", "extend", "mixin", "mixin-object", "object", "regex", "regexp", "shallow", "symbol"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-plain-object", "isobject", "kind-of", "shallow-clone"]}, "lint": {"reflinks": true}}, "gitHead": "fb63468c27c087349f67929b90441f09d64bed2f", "_id": "clone-deep@4.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==", "shasum": "c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387", "tarball": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "fileCount": 4, "unpackedSize": 8000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9fGZCRA9TVsSAnZWagAAWR4P/Am6wEjUDVsgo/xIPKfk\nyqQ/612FFOi/0OlX/VL+lVfn39MSFiKcFEe9Ud4K1Yup7O7+DRQ4sjAtj30x\nSW8CRL2z7GkwCrNyQVGHvh0gc2nWJrg4dANzL8ytB/cO7Vx6hicyMpLxPesR\nuwsVTlGOk6vYYufcVJymuRRkgXb00P88CbVvpZXzAuwWnC9lxj+YuOBqgSe9\nAyYipfNB1L/QeFtvOaOnWAVFw8Yb/T90cxrzVJff71T9TC7wYmaBz4K5H7yl\nD5TPrqF1KmeGSGRfhE3Fbw85Rpb6a5mkAReQF9wKiNZ+ANhEhAvrUQHdoscD\niBKFqefnh82TMTTyrK6HldXHfOH395B3ryIB0mgZJz7WC2vSUFua0HnkCSwH\nEo62zDMiDfJBm5anm2wBQJKawPUJXEmWWU5hYV5wqTKxi7lwu03zE5PWNBcZ\n9Mo9iiOiqbjdex70t6sDMnSbpKYan/JMiJx1eZLIkai7FGuUOdzU6t/q7n5f\nftZtILHM85m6cLe6uRPYNPRZLXeg5jzWDo/I/3n1y71UvMfjgAyxjUYfeM6g\nb3e8IjA7EHZcImisOSm0BRdvSxl6e6V71MsshcitVbq7PMdk+iQcudJQPvdk\n2dtWVurEUEJvFXT3APmHS/JvyjQDUKyEMNydHw+MgiVa/qLbo5rpERE3CMdP\nJ8e+\r\n=HG9n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBNrYGDt3+h8Eb1qw48UZAfTHDMucy6JvHEled331lE8AiEAq2SVH8iwIyghCXecinEtzqbEVGzOeDXiRNtn3jie/Zk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/clone-deep_4.0.1_1542844824751_0.05567611359024882"}, "_hasShrinkwrap": false}}, "readme": "# clone-deep [![NPM version](https://img.shields.io/npm/v/clone-deep.svg?style=flat)](https://www.npmjs.com/package/clone-deep) [![NPM monthly downloads](https://img.shields.io/npm/dm/clone-deep.svg?style=flat)](https://npmjs.org/package/clone-deep) [![NPM total downloads](https://img.shields.io/npm/dt/clone-deep.svg?style=flat)](https://npmjs.org/package/clone-deep) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/clone-deep.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/clone-deep)\n\n> Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save clone-deep\n```\n\n## Usage\n\n```js\nconst cloneDeep = require('clone-deep');\n\nlet obj = { a: 'b' };\nlet arr = [obj];\nlet copy = cloneDeep(arr);\nobj.c = 'd';\n\nconsole.log(copy);\n//=> [{ a: 'b' }]\n\nconsole.log(arr);\n//=> [{ a: 'b', c: 'd' }]\n```\n\n## Heads up!\n\nThe last argument specifies whether or not to clone instances (objects that are from a custom class or are not created by the `Object` constructor. This value may be `true` or the function use for cloning instances.\n\nWhen an `instanceClone` function is provided, it will be invoked to clone objects that are not \"plain\" objects (as defined by [isPlainObject](#isPlainObject)`isPlainObject`). If `instanceClone` is not specified, this library will not attempt to clone non-plain objects, and will simply copy the object reference.\n\n## Attribution\n\nInitially based on [mout's](https://github.com/mout/mout) implementation of deepClone.\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object \"Returns true if an object was created by the `Object` constructor.\")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n* [shallow-clone](https://www.npmjs.com/package/shallow-clone): Creates a shallow clone of any JavaScript value. | [homepage](https://github.com/jonschlinkert/shallow-clone \"Creates a shallow clone of any JavaScript value.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 46 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 2  | [yujunlong2000](https://github.com/yujunlong2000) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on November 21, 2018._", "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "time": {"modified": "2022-06-13T06:11:32.512Z", "created": "2014-10-02T22:52:50.065Z", "0.1.0": "2014-10-02T22:52:50.065Z", "0.1.1": "2014-10-02T23:16:51.310Z", "0.2.0": "2015-06-29T08:24:27.825Z", "0.2.1": "2015-08-10T21:14:43.849Z", "0.2.2": "2015-08-21T08:20:50.659Z", "0.2.3": "2015-10-10T20:47:39.683Z", "0.2.4": "2015-12-23T18:53:51.976Z", "0.3.0": "2017-05-19T18:03:51.107Z", "1.0.0": "2017-07-16T12:18:08.982Z", "2.0.0": "2017-11-17T04:39:55.737Z", "2.0.1": "2017-11-17T05:12:13.372Z", "3.0.0": "2018-01-19T05:05:36.406Z", "3.0.1": "2018-01-19T05:08:44.903Z", "2.0.2": "2018-01-19T23:34:03.299Z", "4.0.0": "2018-04-10T07:04:41.490Z", "4.0.1": "2018-11-22T00:00:24.930Z"}, "homepage": "https://github.com/jonschlinkert/clone-deep", "keywords": ["array", "assign", "buffer", "clamped", "clone", "clone-array", "clone-array-deep", "clone-buffer", "clone-date", "clone-deep", "clone-map", "clone-object", "clone-object-deep", "clone-reg-exp", "clone-regex", "clone-regexp", "clone-set", "date", "deep", "extend", "mixin", "mixin-object", "object", "regex", "regexp", "shallow", "symbol"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/clone-deep.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"basilvalentine": true, "nisimjoseph": true, "yboris": true, "chaoliu": true, "ganesharulanantham": true}}