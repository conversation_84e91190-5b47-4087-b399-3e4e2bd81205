{"_id": "webpack-sources", "_rev": "70-0d44b8f77408c319429414bf8652bebe", "name": "webpack-sources", "dist-tags": {"beta": "2.0.0-beta.10", "latest": "3.3.3"}, "versions": {"0.1.0": {"name": "webpack-sources", "version": "0.1.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.1.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "c814e701c276b83dfd512a19c4fcc3e6052aefde", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.0.tgz", "integrity": "sha512-d7IYAVBGZt3goUEbLbwFp0akcOcWH6cYCQV+FE0T364zAO1ErOYnagToYoEonCL/93w8CMazcuMMDrIJkujRYg==", "signatures": [{"sig": "MEUCIEUrX1HyUpx/4ZEZ8lgvADXeCqIbwMrKi2X7c794XuQUAiEAq0o8i+dj7ioZy6bn8//baBvoPmc10EtbtQ4CZ7vAjGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "c814e701c276b83dfd512a19c4fcc3e6052aefde", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"source-map": "~0.5.3", "source-list-map": "~0.1.0"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}}, "0.1.1": {"name": "webpack-sources", "version": "0.1.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.1.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "a71dd7c0ca3e264af088804bc384d93fad0442b6", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.1.tgz", "integrity": "sha512-19dSXxC6TaiXY5v3sZ5KTcU437NbtjS/b0gLM/KIleUOMMiYPWTRrnHO7afgmQfuCUXQgYWEHTK2IsJIgXnKHw==", "signatures": [{"sig": "MEQCIDD+8XEwliL4gClJWBDx3FYbhdp2sxoPwZ/cjakzPAtkAiBAWyMSp5iXxq3gbgEsIWmj+9VUIQ4GGc9SrOU4ggH5bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "a71dd7c0ca3e264af088804bc384d93fad0442b6", "gitHead": "7e24cdd86d071ddff82c5750b83916e30e1934cf", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "3.3.3", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {"source-map": "~0.5.3", "source-list-map": "~0.1.0"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}}, "0.1.2": {"name": "webpack-sources", "version": "0.1.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.1.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "057a3f3255f8ba561b901d9150589aa103a57e65", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.2.tgz", "integrity": "sha512-gXC8sJPlpheDg/u35g+YlXaxA63dfGPrj2h9FayCCWBCkgAfAFQNY0dMLEmpHn3mLBUzVZYIldF1d9kjHDf8yQ==", "signatures": [{"sig": "MEUCICBKLNJ3p3A7nglxB/X6QNXFeuIQhM+x9pJDXUfXlRQcAiEA2kv3u7RAT1is52dP27bvvM6gI3AkmWujkpSM+oX55Js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "057a3f3255f8ba561b901d9150589aa103a57e65", "gitHead": "ab33c46cb6cc67505e0b8ccc99fed55f1cafc922", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "3.3.3", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "5.4.1", "dependencies": {"source-map": "~0.5.3", "source-list-map": "~0.1.0"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.1.2.tgz_1461278357602_0.4156553049106151", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.3": {"name": "webpack-sources", "version": "0.1.3", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.1.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "15ce2fb79d0a1da727444ba7c757bf164294f310", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.3.tgz", "integrity": "sha512-Vbl000TZ0unlen1du9H53+o1Hu+3UKb0BCsn6eI+W2AvY6/neZIHrUWMkF3mNQWiUCpoLAottc8c4jY0P1MDdw==", "signatures": [{"sig": "MEUCIDZjuHCyd6AZbDzrykNBTJVxQyi+ULhU74OVnxb/oam6AiEAotf2XUzqH1smFa8fqU5gT33ET7nwpxt8566AIfTVFWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "15ce2fb79d0a1da727444ba7c757bf164294f310", "gitHead": "fb0ed29f0e1bedbd87967011556faa7644f5a1e8", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "3.3.3", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"source-map": "~0.5.3", "source-list-map": "~0.1.0"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.1.3.tgz_1478966594151_0.9760280998889357", "host": "packages-12-west.internal.npmjs.com"}}, "0.1.4": {"name": "webpack-sources", "version": "0.1.4", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.1.4", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "ccc2c817e08e5fa393239412690bb481821393cd", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.4.tgz", "integrity": "sha512-bl4EhI0mm3a42mpQRAospjtHDbt1KQ33tVdFxVUDWFWeYPJKx6QJ8qwSVPq/xxNfJgYp5Bbeb47Mk1Prsn6ksA==", "signatures": [{"sig": "MEYCIQCiHuCZv6z3vPAzy/jI5uhZBmPtYBKLPVXqNsvaBysdggIhAOn55TojYxHl75bem3h8Ccc04ej8ZfH/zUceK+894GAx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib/"], "_shasum": "ccc2c817e08e5fa393239412690bb481821393cd", "gitHead": "9ebc91cf8d8b5eac523230cd31fd6789606598cd", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"source-map": "~0.5.3", "source-list-map": "~0.1.7"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.1.4.tgz_1484494496673_0.4281313957180828", "host": "packages-12-west.internal.npmjs.com"}}, "0.1.5": {"name": "webpack-sources", "version": "0.1.5", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.1.5", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "aa1f3abf0f0d74db7111c40e500b84f966640750", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.1.5.tgz", "integrity": "sha512-8CXYfPZkWvY0VWadHDQ3q2hUBqk2IJKTTdDPYb5hwnGVVma8bzqTJEerUDrpWwXnuY9vxZ0mGEnjYD0XLhRHeQ==", "signatures": [{"sig": "MEYCIQCQ4AdOXku+PYBZUFJgUrjRu7g8s9PuRLNhIKWmNm6hEAIhAMOZYJq8UeUZm1vVjlYsNahueU73GgZMkoSDBJ/cG2kW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib/"], "_shasum": "aa1f3abf0f0d74db7111c40e500b84f966640750", "gitHead": "d4cbf577bc12570c079c1aed4ad8c196b6c919d5", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "7.7.1", "dependencies": {"source-map": "~0.5.3", "source-list-map": "~0.1.7"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.1.5.tgz_1488917506799_0.5009965947829187", "host": "packages-18-east.internal.npmjs.com"}}, "0.2.0": {"name": "webpack-sources", "version": "0.2.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.2.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "fea93ba840f16cdd3f246f0ee95f88a9492c69fb", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.2.0.tgz", "integrity": "sha512-ZbWVJmpg5l+nOzBGIAtYVwPU1fo6HnLinwOliJBqt4bhgP1PnBquHBlg6o8iJPmKPlMR+PIcHiouKa75hbLdXQ==", "signatures": [{"sig": "MEYCIQCqWGJkcpIwDK99YQXTpjNy9Ax/4pFE0+tf1I7TvvZacAIhAPFu1X4QkQJLOt8Q2mVOw0cum94wXuOUmaJtTpWrErDJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib/"], "_shasum": "fea93ba840f16cdd3f246f0ee95f88a9492c69fb", "gitHead": "5154e49af10bcefc1e58505f3563da4a07763eda", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"source-map": "~0.5.3", "source-list-map": "^1.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.2.0.tgz_1488918763935_0.25536337518133223", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.1": {"name": "webpack-sources", "version": "0.2.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.2.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "2abccabc209afbf1d8cb62d533b54dbbc7fc6e27", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.2.1.tgz", "integrity": "sha512-nWtkJTewfvEjkWd98x3XfH4xPDE3rtpxWabKcBHrBrkwx57qXq+plqB1bm99GnC2Qusc0JiU1HoqaGUm2sLKOw==", "signatures": [{"sig": "MEUCICZN4mD/n6kitt+QpzI2qTm9APqrOLHfQa/JEQmh9Lb/AiEA7EGgSnyi3VqjHhU1Li3t9YUPthQ0cHKtxgubVBC59LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib/"], "_shasum": "2abccabc209afbf1d8cb62d533b54dbbc7fc6e27", "gitHead": "1692f4be47a74e8b50e124f797db99cd87b9186f", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"source-map": "~0.5.3", "source-list-map": "^1.1.0"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.2.1.tgz_1490389526371_0.09579946473240852", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.2": {"name": "webpack-sources", "version": "0.2.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.2.2", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "708714db9cc8152b9f8674e4acb119efecdb23fa", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.2.2.tgz", "integrity": "sha512-ny3XfRo1Tp4Uu7BcA2f47fuubz9jwTFa//Yi5Vc13kz0BoH5+urrnQn2WtpueEx6W/qDjaJH1UwVRcxEpKzdhQ==", "signatures": [{"sig": "MEYCIQD4oToEUhxF/VRUjbVp22a6PxmQoJsj+CYkwB4bupnIEAIhALwAXAJDU8gmnJRb60IIV3lKXn38qXzB1l9+AsZfolli", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib/"], "_shasum": "708714db9cc8152b9f8674e4acb119efecdb23fa", "gitHead": "2857561d723db6f8cc2f25cef9534d13dddde06a", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"source-map": "~0.5.3", "source-list-map": "^1.1.0"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.2.2.tgz_1490393113183_0.09044172172434628", "host": "packages-12-west.internal.npmjs.com"}}, "0.2.3": {"name": "webpack-sources", "version": "0.2.3", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@0.2.3", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "17c62bfaf13c707f9d02c479e0dcdde8380697fb", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-0.2.3.tgz", "integrity": "sha512-iqanNZjOHLdPn/R0e/nKVn90dm4IsUMxKam0MZD1btWhFub/Cdo1nWdMio6yEqBc0F8mEieOjc+jfBSXwna94Q==", "signatures": [{"sig": "MEYCIQD7yIN8M+2bk43JPzh0CBcqGFeg1YdfDRu7i/fqAdFU+gIhAJH31qT8TrVngPhT97u/jwfL//gI4zAv5Lsn3raFn6Dj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "files": ["lib/"], "_shasum": "17c62bfaf13c707f9d02c479e0dcdde8380697fb", "gitHead": "828a85acb96faf69c1ba0b78ba6d3a2acc9dcd92", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"source-map": "~0.5.3", "source-list-map": "^1.1.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-0.2.3.tgz_1490398694081_0.5428378875367343", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.0": {"name": "webpack-sources", "version": "1.0.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.0.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "fbbaa2cce01333412281a7e40cbe4b0c9c3344fa", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.0.0.tgz", "integrity": "sha512-yCPXB6Aqbn0w4rp7KqiF8m8bsL6gMRtRyGzd/ZMIS2VPAusZ92gsOCVTIGhEy7faQS1mw//6S5bD10B0zRNa0g==", "signatures": [{"sig": "MEUCIQCmHBEHh5VhchV1T4zq0rbTdrfDafgv+fq5NMYlf+uVlgIgOyuivYQ6TJMsEwNTWCqjbQdArZZQzsVL+t2YgCqW1Ow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib/"], "gitHead": "aa47eb9dc433ad236cf8328b4be92c62ff069904", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"source-map": "~0.5.3", "source-list-map": "^2.0.0"}, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-1.0.0.tgz_1496492594647_0.6798736609052867", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "webpack-sources", "version": "1.0.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.0.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "c7356436a4d13123be2e2426a05d1dad9cbe65cf", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.0.1.tgz", "integrity": "sha512-05tMxipUCwHqYaVS8xc7sYPTly8PzXayRCB4dTxLhWTqlKUiwH6ezmEe0OSreL1c30LAuA3Zqmc+uEBUGFJDjw==", "signatures": [{"sig": "MEUCIQD4CNpRw3S7USg+35Kcdn70UNjsEZBa6mzPTAfpqNuX/wIgE4eqsyWtCOf70ExnxSio6L5n53Ks9t0LGry5AJYBsB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib/"], "gitHead": "5cadaf4ab143a32cf994365db4b2ddacd0ab0eb9", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"source-map": "~0.5.3", "source-list-map": "^2.0.0"}, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-1.0.1.tgz_1496500323403_0.9926682878285646", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "webpack-sources", "version": "1.0.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.0.2", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "d0148ec083b3b5ccef1035a6b3ec16442983b27a", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.0.2.tgz", "integrity": "sha512-Y7UddMCv6dGjy81nBv6nuQeFFIt5aalHm7uyDsAsW86nZwfOVPGRr3XMjEQLaT+WKo8rlzhC9qtbJvYKLtAwaw==", "signatures": [{"sig": "MEUCIQCMWIwRa6rjYBxZaCflyA36hnAzqMAnRhhn4BVABQa1owIgB/GLX5YSEWfh8ixZRFMp0B83di9ousSywEVrFJSNV2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib/"], "gitHead": "e1880a046420dd2ebe8aa04f186c59e33c0d3578", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-1.0.2.tgz_1509780058239_0.18867489928379655", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "webpack-sources", "version": "1.1.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.1.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "a101ebae59d6507354d71d8013950a3a8b7a5a54", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.1.0.tgz", "integrity": "sha512-aqYp18kPphgoO5c/+NaUvEeACtZjMESmDChuD3NBciVpah3XpMEU9VAAtIaB1BsfJWWTSdv8Vv1m3T0aRk2dUw==", "signatures": [{"sig": "MEUCIQCyyHrFJmlo6TtifqwrMboFpHKvtIvQ7ndRNTS4HZlL+wIgKuqpnxXbiBxNZ8UYUZCfGF2rLZjCqOElq9AwyAc4sPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "files": ["lib/"], "gitHead": "b643bfba58c5820f9cf8f00a19979c1208e47692", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources-1.1.0.tgz_1511694386044_0.519901181338355", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "webpack-sources", "version": "1.2.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.2.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "18181e0d013fce096faf6f8e6d41eeffffdceac2", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.2.0.tgz", "fileCount": 14, "integrity": "sha512-9BZwxR85dNsjWz3blyxdOhTgtnQvv3OEs5xofI0wPYTwu5kaWxS08UuD1oI7WLBLpRO+ylf0ofnXLXWmGb2WMw==", "signatures": [{"sig": "MEQCIBEwU7RH4wpTaUNHDZbREcZwf2o+jTurrQ8OwVMh2soHAiAiMFmWxj7g0RKJiixFljxPF1/Iz/Ec2qVmjVdmTfN2SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhoz0CRA9TVsSAnZWagAA5CMQAID/Qatt3Rfz+cLxZLM4\nacb1ecA7wgV55iqAABKuo73vdjHTdLZiDHU7aoEURVLD8LQP3K0G59zoDVXo\nJcrP/44vp0bPabcwavJjADMKJ5i7Z79X5zrkrgzLsP32c+XoR9iU8/6ABV3y\nDtLVKE5rgZlU9+E8kerUuyc5hsedIXyEnn6KKGSJY2Y6OjGXHjVWEC/5J4/e\nRhsoW3x5958eavxc+tkheftiZu/sx/3Tkxz3devSqn8eHN7JI7vADUd4kUsg\n/tPrV9MwuDrm+rQ8kpc1F9QlclatUzg+eHvEWSIpvZyJiMq/2vEA4g2pIghk\n87uM/5EZWBSpNKRvHhpnOg4vKVdzBsjCYa7epQ0MzEhnUHfXerMVOogDzSO8\nD52h/13RSuVJqCY7OU2pO2x/4PQXes76/A7xrGwTHgF9aL25RiL4b2JGnUKO\nSPXcrzh6dJE5cMIvBoPcOa1A9YeXOOYLuGrnklG76rYq+C12EpqFMz+uoa+r\nBsTFFBcJoDI6Wzh1+rKPR3spe9g5+VtdBGIyM+jijNYzCiBtYl5xW503LIt7\nwdhr6HOfyr9tndJnfUO5s26ZUkqYviVm5DELY9g8HiXvGl9KuImjhD/Gh5bh\nkfZCOCByDQiW/C6B0InFxzbHhE/lKxN0QFAvQBA9mp5neGT1WDyU6V2AQBvq\nPMDt\r\n=zF3m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "30d2a800c36906a0f28e62dac8b8cb239d1403c5", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_1.2.0_1535544563641_0.6014773428826166", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "webpack-sources", "version": "1.3.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.3.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "2a28dcb9f1f45fe960d8f1493252b5ee6530fa85", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.3.0.tgz", "fileCount": 14, "integrity": "sha512-OiVgSrbGu7NEnEvQJJgdSFPl2qWKkWq5lHMhgiToIiN9w34EBnjYzSYs+VbL5KoYiLNtFFa7BZIKxRED3I32pA==", "signatures": [{"sig": "MEQCIDT6n4WO7OPAANuWTlYffBKgA4hBZ+I5grL6V5MDsDSnAiAh7oKG5ggJAC5+UQ/zhly2Sd4rVPY/Ho4zII0HfID1xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbo2LDCRA9TVsSAnZWagAATncP/3xrZTDrnHetMCtIxJ9H\ns2dIdpV+OSrw34t4jCkFCEEzZj9d3fyFKlstvkEboWsCzBqCwQbcBRInFIeW\nStdDFUsrSytHLIu/qcF1QnJ+NmGKnXfrrhuArtud+Is6oIuCPOM69fLDzbd1\nQCU7lO2mgWdVuRBY6V/lXsE+xElHd6xBe8kKND3Ba+0Hq6wb0k80Xl+dybz/\n67fYe4AsiYNDxqd/h+tXrHS9AtSbPVkyvkqky01qOTUwbHGBpZZ6dzDLw0Ux\nqnBg5MoRhO6PxiRElpo0zlZKSYL8evkAR/6P6CvvuqTYkmEBKO9DjeTMkSq0\nTHFfMDNVlaJBNn3KsLEcEuHfAxEWTc8ffiGfdnG0uS9zkiUIildS4c70/vzz\nGNqATzvgVOtatz2uTDxs4c+zfp1xpN8oqRmjlSjeWSvyPME7QzhpYmJQRYrn\nqAlVRLcm7z1siQm5b39lJxsJqau0vn4Q7hENtgsK4qvBC4/HaLGJRfPTXeQw\nHfSRvaPuGhgwEHUd+uYbJGutfsdm6obNkyDYLuY9Huj855LWwElxv7KQPlys\nrdsu3fAXwViDHRgxohqPR7a4YYODDiGnehA8QSkmlQeFFmQni7z0Ir3SmzWE\nPY8uE1lzxAe7koHPZjrYNcf/myeMLvinUtaWg447aoR78ooCWexy6x7qqtz3\nPecF\r\n=It7K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "0f9996427a2ee80c908237d917d60e62473a0e2c", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_1.3.0_1537434306785_0.2281706249888662", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.0": {"name": "webpack-sources", "version": "2.0.0-beta.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "f57b7a466f015132c70d8125d6d36993968854d5", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.0.tgz", "fileCount": 14, "integrity": "sha512-d7mJcjNdNa0hTOzGNdhIM/lVU+Xp1UxTVh42rEjH2/0U/deiaUXJtzj1npqo4TJzEjnXHaIvrMDgWwM/mpiQMw==", "signatures": [{"sig": "MEUCICSb51pAsoydLHLnWv+91y9XUNrhMTl0R4mOhnArwovgAiEArnFb9gTvZqL2jqe5tx6SgDl5sOvLgxfvvjE1LSxXI3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQixoCRA9TVsSAnZWagAAN6oQAIRMmn2d7v7TIVnRFaGN\nQUqCpysiN/yvtshHq/CavgqCrqT6g+2trPSANQKUDf/h/i4kLyxTHEMD3Ydz\n1lrGxqtYnGYyx6JZZGFDFMiFQNk8ysZuKTE18tp7Lolvxwix77DvDYW5kSWj\nZAysEfr5x90s0EqW/NvfTgR3JrGoSP1GepGC8StxMYUIDMceLjxD4uFwl4AG\nRKc6qtha3gWZeuXBpr+gBovAofpozJoQGH23BkQSykj+Tc1UPo+nmnoPjkhJ\nSxu6eg0lYfMYZo44LWIbGhrNYym6BH82rPusmSfAmj0H1c+vQ3+ujPkkE/UC\nlsvUVSRzobsyDxe4JHbp/FFtIuyXQkzqkCbgb0QVDMoYK+1FexI0Xvhv9U0w\nGTVyK512SMOK2fXg7C1P8NYKcAdaqDbYdj4KHDe69XGC0lWOY+99sF2FATMP\n+7CFS3V1NMoQ/6O4EZUtznepxKRiKiZI0ghZZAN24EnUuFPiMjf1NK/a8Irg\n2wiDva7tPd/N0CtCTKhtIX9HmBSb1VPsj8NiGCNZaktrL8roKqBM3+IG+x75\n29s+Ti6Fj48xqgtzU+EW0pcMyQ00mjF91iHanCwIZwLc2iF1MNh0gs5ji5Yr\nW/4FpH0xLFBHS7HZw9TxJb5midlMp54VlRW0Zr4TT5LFxYODMWf66faFcf9l\nD8NN\r\n=t/t1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.11.5"}, "gitHead": "6438e9022885426431f581c8437855d17b6e0e44", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.0_1547840615877_0.9774733786111278", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "webpack-sources", "version": "2.0.0-beta.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "7502135a42b18e5c6e88f85d706729fef1f9aee9", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.1.tgz", "fileCount": 14, "integrity": "sha512-crzDTpRrXVAc5E/PSs3CP4bieKaFUqcKh1fLkul5BGyh9zI/FRK0QCfD4ufmReeEgbWTP7lhmkuSASQeM/dnwQ==", "signatures": [{"sig": "MEUCIQCILMnQMQQBBXy0HMhiCfXg1PzJM2GK4u9JvUkFfNqBeAIgZPmKaTNSCFqldeP0PBnT/ydvQpR36zcVHVr6jtfBxfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQwziCRA9TVsSAnZWagAAM8IP/3z6qhTOS2BV431PtoaS\n2GvODqbHM0DhCqVz/B+wIGzQkGeN5OdkExhdWdL3bIXS2OPnyLUsb2xlslDc\nq+tJZ+Rc3qZG4z+UE3u4ms4LGSOx0NcL2J1kr3Ldvj5Aq06ckeD5CGn8PoOh\nYTdyYk61ImNgL0fNSDlrz358tS6U/XQEE709WIjuZE5eC3/6II8RalCRQWLi\n1w1GJ/aLVE1o2flBeZJzI9DFH0TZ2GIDc6WPTjkufekGebs74kmk0F3BM+vB\nniaKgklDLSJX7a8Yjn30ZtfvlgEdgMIx8cM1dx6zLseCFHAtNLlijtTEr5px\nHt+j0PM/u/vL5uE3r5FbG9X22BEhtXBCZZhR1gG03AG7kBpzEO3UjqxsE0em\n1hSaozn2GZaLrn9kUXb6XZeTdTjj5hWB5svQXNGkiD0gX2M3NV+IvqXZIQeI\nmLX3GoPfe4ONQUTK6h7m75L53kd29jm07Y1gdQjNcMB1eVdoMfg7E6isDF7P\nW5cmgGXDnuyBpTmw2yQVTMbPrkVbMC6U7tHv/ym1o+nb4rcRFE0OOXl/dFaf\nxjv66/ysWpK6wlK97m9g2EbUDgHN41gl5Q9Q96soUVK8UY9fcln7CH8sDFaI\nq5xJ1acOffWUhqD6uBxO+5mazlhIqIrf1OVSh2g2q8SZYQlS9f4g8EXLdSep\nzbD7\r\n=22NR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.11.5"}, "gitHead": "e3fb93fe5b8a7799cc6fcdcbdc271d11effc271a", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.1_1547898081677_0.25216097781514524", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "webpack-sources", "version": "1.4.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.4.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "be93d629601aa3ea48e8686ee4d78d9c4e706061", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.0.tgz", "fileCount": 15, "integrity": "sha512-1e4Cxgqfl8vnDhXMMpegX7JWWP7HwV8Kp8/Oefs6EG52bRtOR9vuOXM1Gl1vy6NwHfUxHeuR6ElD4HamuRPO0A==", "signatures": [{"sig": "MEQCICmGm007wlxf6+R6QES6xOmencLtK73/SdsSgjOi4v4PAiBOIHn/dSuczNS25STE3ttNfAgtI8tGa8ylq0JxIbNAqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQWPDCRA9TVsSAnZWagAAZL0QAJ0rT8kBK7Iclpdc3h2B\nhkbcPD4RKpLGCrtcITpE+CiwPBjZoHLhO6QJ1dZliq/vNw6/Xs/Ea6RreAkR\n6xNW0QM2BPBobgBrUTKYMBF7kzGworJ4s+4ed8DgoCnpOQdd5PCqojYNWh/D\nX7Dtfih+KDLe19Z+OUaHs7BXQMQ79uLTTZslh9GWjm7E96VdfEHV+QCzOzy3\nFOlCS94EKSQpVAvmZzOT6Kn5g4lZik1DRQrHOKHR7fkMwzDNu8QYX7bLIUwo\n9M42aLuGYG3p3eeC4roDhzxfVrlOV5xcmbpmEcy1VXaEde8CzKz3lCR15YrR\n1v7giLAcPYBttEftnHAKWisdEnJxYjQB0p+yiHZeEb8AS8LgQv2wKw0DIA8R\nBIAc0FCLNm6QZ5qHPRepQIUOYkoO2bwCI1h1YzHiH7JDQnhtW14GNLTJ51lN\nm0YTWyow6qZ0Gzje5BLxf7V8OMOYrQ0n8d3W3Zd6mv/lGV3/oxFXcPF6XUfP\ngexo7mBmVLxBkOtj1l4rggUtPM5DaVetMgwF6mNuu/+AGiafhMesNdMhDD1g\n43OL0exuzVY3GOrpaItrelZV7/W36LmN3X+8Vwet4Uh+H1nZRuMzWEcdpCOb\nMmrkOVsA1EO8APu3aC3NaPOLuDntrkrhwEhVHLM5FKxa6HCDd0JsLQ9rIWju\n9JAv\r\n=Ufz1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "dd11ce64012927058a87b805b2c0dffd9f6641b4", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_1.4.0_1564566466651_0.10388116617983933", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "webpack-sources", "version": "1.4.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.4.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "b91b2c5b1c4e890ff50d1d35b7fa3657040da1da", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.1.tgz", "fileCount": 15, "integrity": "sha512-XSz38193PTo/1csJabKaV4b53uRVotlMgqJXm3s3eje0Bu6gQTxYDqpD38CmQfDBA+gN+QqaGjasuC8I/7eW3Q==", "signatures": [{"sig": "MEUCIG6MsizmJ2tRw4KCOfCZh8fxACXwDTeZ2jiSevCob2WbAiEAscf9uNOs708G6C6JIx6nGsg5ohdbC+WT89bbncN42k8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQZVuCRA9TVsSAnZWagAAH6IP/2A+bn1C1RGKaTA/C3Rp\nemYxSzpdKSvT5kqGrkX0pSbdrp6zgR2prK1x42v+LSY95OBtjD59zSNq0t2U\n74QC1o8kVoDkXJXhw9BDRuftcUmkxo3ZOHIKUqOnsIuKGcrU6fPOOvtgnPHM\nNRtRjK6sLGJgsTNPaYVDbGzxePreceZK2K2AdxefP2+BehFac74NXlo5fNoI\nEUWfjw26mu1Pe/PSkVceOkHAjeu1xA4YOBdGtvQ23iaaMAxc64K4M5buwzYv\nEsycYfXotsz0JNZgqFXxlTXOtzJ6lW7WujfHuuoqHtT+JhOYv7DL35hRzAkq\n1olT4oQuCxkb4eKWuaScqfmRwsTTfANJM2jT0tj8xOXRn/HfWNaG3CsYj4q1\nyfffVu8pNMOMlFrHXCQAFdKPRuA4gILOcUFtE14dD39dhndmsKewtYZJjoAF\nMxRhfJyxWi/5GoBpN2vGrvQgx2M3YdUunc0ThZUZPAsYbNruCe8yWiPzquiq\nTeq4vx4Zj0MPE/pDTNIAmgNITUlqsn3z3LrLDDl1peF9Z+vTVO0G8tCIe22m\n+fHzqSPWFHwkKo2ryz4+N19JREsDZSnsnwiT2+JsQImw8CemnPs56Sofqqqk\n3qZm0CSHjtH9OQblIZnUy9dfLX2XanKA/Ay6CWZuS7ljObyBhV/zS2wrBaWn\n2guZ\r\n=NJuh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "47e5f6b7a2dcab49274611ec1f30e40c92b6cea8", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "eslint": "^3.19.0", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_1.4.1_1564579181915_0.2991884361012698", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "webpack-sources", "version": "1.4.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.4.2", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "a688692f5ce53f9db6edef498c0beae73610c9d6", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.2.tgz", "fileCount": 15, "integrity": "sha512-V/mHhu5RzLPYVY3Aa+ogcoLqdZU+XdOf0VCLuqSDLJ6oo5lk7Q1n9kG5cySidY/SzXfXwS+CASg4z00q7gIhRQ==", "signatures": [{"sig": "MEUCIQCtOZ71oeMFs/M4jjk8mtAdsGEsTooBetbrHnMFDR8XWwIgSLqzTdbxY6G3QmrH9gkXlrSgGET7Yk6grnLJLHMaHaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSBUWCRA9TVsSAnZWagAAVZIP/1h6Ecl4dEJ5kvY0CICn\nWn340MAS14lINP6s8uJxT4SB1o/BXqcnjLuD0V+ADWnhkN1P9pBE7YPfwnPl\n3UEnJ0yMJQEo803zclnXSVhkEfTgRn4NYV4pVl2visRtrt2GU0HMfdXoQ9Zs\ncgN0xaP5u/f/XigN4vRLnkBMgrkJ7LIbRNTMqU0ZLS90VE4Km3OUsDXTeyIs\n1zXfaVvrG3POI0ecg2XM4SkpNIXepc4FBlqV33SAvnv036VRpFRoQFCIOhpO\nYM6hDXx+Df831kKEO6HNYXHkJR8ZtcCjl7LgnWsBOBDb7jytMpdewfGhS9fC\n3fqTBi4DRGZUz3XI7tVWJPJPATfrYgOLsGelPoGuXgg/vPXXQriWMbsWJWSK\n+4+0m3gWNfUcGO4kG1tvWXb2a55KlPyjYj3lQ/JQCAVL6Kcmc2aC0/XmpVwb\n3OHDTFpJGz8KrHlMsgb6hWbYLHPKh1hIYENRc9+0+KU+9kuRt8A2/Zzp17j0\nyokEew4n+rC3C/il3XWEjiWCZyPqhQhWSjSGUuBwlKjAQc8fgKSYlY/ctzX7\n8kB+y9SOezrdGd1RFBSi+822/eaa92iLvGDPxG8e9BupSqq7BKybQJRixp7u\necYjMx7sxEFhIHgVJX6wLjzeLRRc9RmMtEP0roV6dMyq2voT+a71GJ1hvmUO\ndH52\r\n=WVLr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "741de57fc86867e78e905a4dbc82e4b34873a02d", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "eslint": "^4.18.2", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_1.4.2_1565005077597_0.7974915425378057", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "webpack-sources", "version": "2.0.0-beta.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.2", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "9e820680e6fa0d05b228754fe95e27fba63b8521", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.2.tgz", "fileCount": 15, "integrity": "sha512-1TtHKmyK/G6F61c78cwJueyexc2A2mHHC7L+oQ7g9j09cM40M4zUDij37IB9+gVvXBC5jGcJ6HQwmwV0skVvVA==", "signatures": [{"sig": "MEUCIDpthw6xNgdCR8T64dcHc5Bm3yG35skghQyiTjYUVw4fAiEAkjxmyYqlMfKWuOj8EJwNfhsd4++GjW4CH9RttMvep7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSBd8CRA9TVsSAnZWagAAvNEP/i6WsIqOhA4J16gnqRIO\nXOWO4try2mUOaKC/q+dvmgGhdo6ZRx3ipSvGHkkfwrn7ov7hfi60fLlkaLTf\nl/IrlmLW8iHmQbuqeXaBhbGyhRrt3efFgnEvRwX3KuHndfm5T4TcaZKvBoBU\nLydcahkgqOISUqXXuEFlHGeSOmLDVWJakAwuh86UKIe62kpJjumzn+51xqx5\n7ZsmtdzNVtOy7LSWJcoe2pZ5tpT2Xic2AS31OfQGys8TSBXxlWW8WCzcKliQ\nFwu9zf9XBTSYfUTqK3/rvjb2FWJQjx1DN1f4SmPlgOqlJnnn7etIorfimEH5\nYGfGTBx410PahN3PmNix5dyB/XKyJnKsrzstzBG+m4pt0yHjy4/gBZRqeQB+\nx0CQmydRFLiJkTlPuClOsVVDNH/8xMCh5zl6xQRmNiuhNVjgCgSXGRSVbhFE\njyV218cWD/yJzgwgVmq/4Wgm4wUSkofKdtiI8v3YrD8pMQP1GGwQC/l3OqGQ\ny4EO4kHPobtCIVZzdqt2yVQyGdqiZira4mr9TOA6zz/bb+AKIhWza0fEnGxe\nAhZna6U3I2oYO27e51RLCCn00zELulZ3vmkbuAzL+ecfcbKmBZdn+m01e/Hf\noDb48z8vBb+0L76kYNaBlFh6QugpLXCIiEaNC4BtMk3vRe08E7gpdfRCQ9wH\n7SjV\r\n=rGpj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.11.5"}, "gitHead": "95c52e67c16671e6426a9176d36d8fae3d561c0b", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "8.11.4", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.2_1565005691693_0.007510184343851867", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "webpack-sources", "version": "1.4.3", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@1.4.3", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "eedd8ec0b928fbf1cbfe994e22d2d890f330a933", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "fileCount": 15, "integrity": "sha512-lgTS3Xhv1lCOKo7SA5TjKXMjpSM4sBjNV5+q2bqesbSPs5FjGmU6jjtBSkX9b4qW87vDIsCIlUPOEhbZrMdjeQ==", "signatures": [{"sig": "MEYCIQDmb93o42lR96rb0obibmUlqueWpkEJ/fMUI1h6jkV6OQIhAOVccZsSC1HOcjHPezIqddh+9AfcyKDwNEypcN42gcTi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSEO0CRA9TVsSAnZWagAA+NgP+wSTDMmFTN5zPN3FdamR\nu6lKYG5j+24uIWkULFo/PBFuWFSCZ5jaso7dMWExiLy8ruXowlIHy6+UmuIq\nwdZgIQDogrI94hZ6NqwF2kWpIq8qNqz1nozuBOzTm7t4iXnqOpJiRVXsWKYA\ns92UIlkJZPOZFUkBzbEGw3VK1trzbaqk0ogzc2415ClCrG/6wAienxPpmi8X\nrtoSkLogV4UDljh1g5VxW07UWV0GiBBwr/WfKTE+dv2zgr7ShSyKk9kPcLt1\nUpB6F+2vVIW813T6PmaQyriqec8zbLrQffv5h8ty7GK5Y4rKbm9kRFwwn3Q4\ndRhb5RsLE3HshFrsg4/4KO92pJExxsoMQC6JOj9YQKtG9MDTxbPhSMTAt/D7\nuA159zr+iBWOqi7LtXRZEK102pFm1b/kg8RzlT6LCnvT9zGmEs7fzh3WdmNF\n2RME+L8bjG9GuUIAn7RJX5NYt/2NNf8h7JJ8uc+F/dRxZ4H2YIYawOJd+uJa\njB+0fi3/dF88aldDJWeGlRXV0mtLLHPFThaiyw/RdRw9RzBb3BUL+KMp+ucW\nUvHnsW382aEtrG8Yscgj0B1nBjyUTQCDP4wQLHE3tjCuqctHZLNXa+o6SlT4\nzZIgQIOsLQ9+KOrLr2CEfpkLmwrDCTAADelJr4BLd7pwC1vRC09x/9ndBDPL\nCBlo\r\n=DRZm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "gitHead": "d9117497dfbac940c02aa97a6fe48af633154edc", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "npm run cover -- --report lcovonly", "pretest": "npm run lint && npm run beautify-lint", "beautify": "beautify-rewrite lib/**.js test/**.js", "precover": "npm run lint && npm run beautify-lint", "beautify-lint": "beautify-lint lib/**.js test/**.js", "publish-patch": "npm test && npm version patch && git push && git push --tags && npm publish"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^3.4.2", "eslint": "^4.18.2", "should": "^11.2.1", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_1.4.3_1565017011941_0.3263161823803915", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.3": {"name": "webpack-sources", "version": "2.0.0-beta.3", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.3", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "4da69f67f34e3a3d1633a82b6db8a6995f4a7f13", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.3.tgz", "fileCount": 15, "integrity": "sha512-ncnvMMP5B/DDkqgPVG8eXTFD+Yk1ch6AKcyRRlvB9B/4DL1exaF1Xh78Y7xcTXwhpVsL2051KSEfl8MkP75yzg==", "signatures": [{"sig": "MEQCIG9rIi+frWUFYlDEnpQ1BhwpRB/fJeca2KpQrcpBhMtOAiAzUz2A1z9kZKefflEPjCT/vwno7GzcpCV1kHnQbUjcYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSETZCRA9TVsSAnZWagAAwCkP/05jvTBYCNY9Ep1eIPOe\nHZMUoc3krUY97f2DNoP1XtgIy76rc1ZVlJvTqy4mGsIlXBZ0kHMR0MYnXBEv\n4a/PY5/knuDkgBiZCNuy8Uiia5XdYuDsa4OwJHTHslMC/5esm0hzsqwmkXAE\nQcVyZzJ52fhmXmFOrVgE/veX7O+Nm8DEhGDtX1JLFQ4GL23PkZ4lghuqo2bo\n1ra3YBQzlwBaZveYLVffxmG7D8ef0DGl4nlNTUdw3I3pWxiKS2SNXzPnhuo6\nYRPTfLlbudjJFAKg61TE6VlRKHNqREivR3KbBELVBhNOJqv0DEtbCwlqwUiP\n73QsqYvJx8n1t29lQzNOFuwHdBxtLe4PmGEFjZGuXQCwnjZjoqgsIdWTcFV4\nVLuP6pvpExJt6d/33Qa4tBOCfWHVl5Mt8EzGg54dLp+EXI3FhrYktQWOS0d3\nqr1rDumoUnyd2cYKVwocRbBM5Q0qmRziiVKlQiGZ0VENdL259FOQD9R2+aMs\nPLVDb5b4G4U6cMtQzp/B9hI6gxsrOThMaY3Zin3LCg/gWwgIcEL2sY4RFFnS\neyOtQTPpahvj6DU0//nnjoVvR//tsY4fgXRvlBOyesmr0hqpYB2pM1xCOgq1\nQviRXtI/QWcJhoJ297DntaNdCDSs/UTidfaxn7ao7OZbhDFEmKRIanDw/9Qo\nruHD\r\n=wmpS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.11.5"}, "gitHead": "d240fb8092fd0a4e9c1b5ec72bbc3d54d41a338c", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.3_1565017305135_0.6160441220609603", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.4": {"name": "webpack-sources", "version": "2.0.0-beta.4", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.4", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "b92f912e16e5c49ebd3c268edf83b13972a3c8ef", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.4.tgz", "fileCount": 15, "integrity": "sha512-Xa2Ul5hAlMICQsKN1hjoSTSxJ0UmET4gZXR1e/Sfpos0XNSc2K3fhJHz79CxLyYh0KFEaj6NeoMra9Pp66hRBQ==", "signatures": [{"sig": "MEUCIQDljyuC4hGo1jqxfTJjsi/WJLfh7kAbqgCZU4+L2H0dIgIgXCbhh1E+CV+T7smaU9QmyPYsIqzSKQBDihLeSnnBF4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdembkCRA9TVsSAnZWagAABbAP/3qYYmMwHNs/JSjCEDYg\nIykwXOHi9z52NNYQ3SO4ciY/dsIrd007RIq2p87hqvbE72woTQdKEU8XqCgZ\n3sGK8+OeIxXSA4HjV5yBvGlcAKTwsanwWVIZ3dt6E1ReM4IV8lymJswM/XBJ\nx+ICVnR7TM42rintxHDFRrEFlNg3X8yMwMjc3Ao0nVBn3AYla3NCHkG6eJZU\nr+eTgUHTID8T7zEzdA+8+9eMVpbCfjBHrURh2s5xhKg43QJPy9xbNd3xO2Gh\n4ssaqeBDDBFGIZXELXe6je/xMt9S2aeVVnZioicuDzLrA20OL4OLXUGtlFlv\nDH9efP9doMXck7r0vavP9E9hRNjJHVZLnzXQH+HLvqS+BriwCalLak4fmJhj\n8fEQwKc5R76lg2FIRyj1wpEU6xx4XaZxfAe0zYD4hPvFe7g7QhhkXGSkOj9M\nZyP6k1UvPY/VaDKXBR5wyAUo1D185XjLw1OZwzUFcA5y341OrNpCDU3KPAzB\nFuo0f87CXb3mrn1Y1m1pY10WhMS6Jzp7/nFf4IATO3298C2Fc0rVmszc9eBV\n6x7O1tK/eQr1rjHofLBsIeel00jwnydGCaDjkTQDCeFgIEFn59k4JE4MGxxp\nrD5X1W5an1UkrMdKVo7a7UAIp7XESxc5srJvF3REnqzR8O9w5h4V7bd4/VuT\nl0Fc\r\n=jAW9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.11.5"}, "gitHead": "b18dd96ae5f52a6907dc93e678b20a3fee43d219", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "12.8.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.4_1568302819778_0.33750397726486225", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.5": {"name": "webpack-sources", "version": "2.0.0-beta.5", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.5", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "aac603d9fdf04ce96276cf845cd9bb9afd660c4a", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.5.tgz", "fileCount": 15, "integrity": "sha512-Y0NWr3nIeJbHzQciKPjWkERbRrmqZPhE4t8K5qOzl4dhJViIdBnvutCCAQ9PKj6MC8lo4Wytu+SZ9Ct8vhX/cQ==", "signatures": [{"sig": "MEYCIQCwcNDpSxgZhMrng6oWH8qDMfhnrd9BDYSWfG9d4FKwOAIhAKyfulmH1ApwGrOMN2GjgZWLhPwLoSldJfbvxCB8pfw4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrvdZCRA9TVsSAnZWagAAeRcP/idcdTNY+ns2lozmJfc8\nbHM2jVcT45kK/uoyE1cvgLmQExG/m97cgvwLC/n+4WlP6pTW+gJVECqcoqhh\nRMUsCNa5ZVaTTt5gUGymhdB+3b7I2B2VAATSEyxbsjNzUyUVr5r/zUDS3LAO\nA+UNIkzb9fZBdFT8jQdFxLcViq4+00XpEtLAkr6nnOae61vPffbeom9Bsotb\nQdvfMOCiYhHh8C0UknX8wKADIiTAaTiJqFjMpx0aUvevHNXunYgXY9mohpNo\nO0GodZyAQQpZgtc313091qIpMt/lflLIWD7621SccOfBeS1U1m6ZV6eho71i\nIUUPFpV0WCkno7xrvhVvaiFZeCWZ8iMKWSNnWhG3JFkWxd6FXEAwEVPUumEE\n2SC/up5QksrJ256Veab7j0+E6fyTj4YSQ8NcGAdjgye9u5M7BA+cvaQ9sbxf\nC5MrCyuaQODoYEv/bv3Y6JY6GV4y6TUlfFRmLBCcc+z3j0EBYEg85hlWwlDM\ndV+7kJK2kQGkSWu767DXGazJCtqCceowl5wshluoklk+J+JGXs/RiUD3hNhi\nFBXxcQN9+t8GKt6fZZza/cg73tyW+CEP0LD6zG5VZfnxiQrKE5XxEbj7QN5q\nQdC9fy6AN7qwSm7KZQSWSwav+U+DGSqxBmTG3uMCa3L/JCiiAt2R/0NLWvgC\nucaa\r\n=5s9k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "913329a70ef7f28482e0186faef6831a298a0db2", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.5_1571747671817_0.22840850453452033", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.6": {"name": "webpack-sources", "version": "2.0.0-beta.6", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.6", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "80707d4e417cf734ec634efcb1cdca4c57aa1be8", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.6.tgz", "fileCount": 15, "integrity": "sha512-zbYXdCVyNpHFs8vR0O3Tf7kfxmL76RDBGf1kBbeBrW1zlIKAp3msFKBVllwUAfyoMa/5OJ5iQRrqE7zn3eBjbw==", "signatures": [{"sig": "MEQCICY7ert68u3124lOle0mGqPMtILlM1r73qJgDxpxsfY8AiAaG9i/0DLY5/FF972SJHf+h0PJKYDm2eO58RlAhZUwBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxTozCRA9TVsSAnZWagAAQgQP/2PjCr8U/NYT+vy6p9RC\nQk5PUmXaRhrWFItINgYGph0uNU0lVJtnmzYnnDEWZMCFyX7Bh1Jfmc0OmVyh\nXK8HdIysE9iWq/LaEe7Pd6bV2msIrIirU42Bn2zigAcNjXKrSIpytl6BQzGx\nmxC0j6PlUlvJUxS7Eb0+N2UimSD5efT2Fle3zxgYoliHY62NgecpG1hsRt6t\nOHEVGgD14+QJ9Og72j6zH6oo6N+Zvl8v5I7MMG1+9SLSZ5mCv6/ecR3NwSUn\nhcpYoS2NzkCIGL6p+wrG4bmlVH4R5Wsbvhsnwm61+qFc2Rwy/C7T786WDvTD\nJhWa4xI6LtJIOLEOubya6deH8BPSOqSI/B6/J5w8fDsDGj2BOIU5AwpqT8+I\nIXDExjzDlaEcwjSWwpoqedh5+6lswmy2qcStKpZhunweQ3K3zgt2vkpAAR/I\nLl98vNnomxhmimXhF0AMTl950DC0CESMyg3QYeNi0t7jY07cvwtCK647xwdn\no8BnN/6UAC+s7YjtZj1+UABt070O0AYxt9IW+fK2hBvJhHpVJxd97eucgrh/\nHK4bBh9cia2VE6MnSJ50exXKX21YgOYlMVRFRVext7Ru8lF6GakHhn6PLrTk\npnTgP7JCU8z51pD2TArHZd9ONf/k/NQGXXeMuwDBhcmF5IkFPqfJM+MP9DVg\nZkhk\r\n=Jqdu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "6ca4706a4104025494eda4f6e49059842ec000b7", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.6_1573206578766_0.23776116757607757", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.7": {"name": "webpack-sources", "version": "2.0.0-beta.7", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.7", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "525b4caaef7371fd7f8bf7e5fffe692880038926", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.7.tgz", "fileCount": 15, "integrity": "sha512-jbUJbBcU/PHABhHDtUfXCSU6wD6dYOBMH4oIQnXxyapigyuOSHdkmqENayzV+3OEK8tm45qRQD/ggtyAMxqjAQ==", "signatures": [{"sig": "MEQCIHF0tp2S6cz4gkRWWimzNE1svWIpJiRQIKxEDraCeZxUAiAVJ/+6Wmdhy30MJKAmcPJmXBFSMd3856xN0JqE2NSYlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdy/3kCRA9TVsSAnZWagAAXO4P/icgv3hlsZW1smjQiZvj\ncjQImET9bq14n1IA3S1QvMQ78A4CV4dqcAnRnHzqw4L6whAlfR+QYdWmKpq8\nqbpFvkiv9x35zEvpKTHYjiHogv5Bij1TOZFzFCpDAwkfcir7OoD3P20S2G9y\nnzFKRzJPt6XK1luyEU+JVYgAAPnyLaYz2gulFETqcB2OXyp61Xd/HpvrECj2\n4iwBu3hxLboDvE0DJ4zMARX1r8aStC3dVQlLEl/9VDiPmWz4XofNuyuKiq/G\ngW/fkjrdZi7/i0WHSvN7guWakriMPmSpshDzYjtsYu2AYk78QCNvjf83/dr5\n9rS/DLMJd86u1lPkq9P8nMsywwl4hbPnXI4xYj070GftmWzZ49ukF0hHhRKj\nGP7Y/Mg27PO7uzOE+v+CKrSoJLz2/g0C1wgy9PzApy/rNThw0XvZZoUG0+yq\nCb69ABc/Ju+RaDnigBYsT34GEefVr/N6PY8x8BSQAVPJcWpm2K/ICUj9JB5s\nvktGUNHd+CELFQh6R30ogIU9dckXtvNySyE2vh8pRj3tHlP2ravOqTk6vIfK\nSRQthsuz2/KrhmbqFRww9XgRaFAu6DyrTbftv0gIMQgnA8mH7bArpRV4iHEr\nkiAUYYBazdDddb/mvm3IRXWAsK6ChTA4kqtG7MobONzfcHr0NXtBQvJvxsNH\nzlN9\r\n=NUJ3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "78af22b545500d6aab010eb7161ef343bc22e8de", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.7_1573649891687_0.6142788204135017", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.8": {"name": "webpack-sources", "version": "2.0.0-beta.8", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.8", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "0fc292239f6767cf4e9b47becfaa340ce6d7ea4f", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.8.tgz", "fileCount": 15, "integrity": "sha512-RUaCJu7HYNeuzlY4WYcArcnOzMIj7kHndQ4pBdgP3iiMpG3Ke0BWY5wvb/VEFgsIXp3ZzPGRECwX+4fgpcKFYw==", "signatures": [{"sig": "MEUCIF+bV6kn0qKy8J1TuLkaSA8WhVqMg5KYpxMlavcWlFBUAiEA04shF7XjDywipCHiPlGMhgzd+88VvfQ8TuAnzMCJIbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1V1qCRA9TVsSAnZWagAAsWAP/143mZwa21htO5pdj1kW\nRFUFmioxJyNy/CxcVHQIy+L2NWCo5SBCRC5CayzZGDgMAoQUtNFd2y6uC0YO\ntbN9Nlj++e2EjVhR2F7L+51YvAH0f7HdtGMIaf6PPLyQ+sNrVyRp/ubK5MHE\nRHfgAe47fG4rDnQlV0YNY3HiV5XZxmjuG9sYhDd9tQLTpOdqexOTMNwT9cKa\nMoshycQ6Y13HWc559bN30l8ho2LJ4mW37g8uf/4BekCnGbzywbnug/bQYBUv\nOWSfhKyXKEz9PuAqJ902mBC6dHz6uPf2/EyaCvQvBpcOnI0gMN1rD6QiSJ8k\nWABVAet+a2uP8xAKClG4M6x2Hg5NgtzldwIwD7cnR3xDI3XRcSXmw4wTzrlu\noPYsmE+POyJTm3ejL4ehpdAvm2kwmuR0A/5rESdZBV8pudetQZ9tt8nptjYP\nz69ED34Q6/E6dncytPOP92D5XBJfeXRsmxJ4UZkTbDegy2e+G3RP/YYEZyJl\n39Z/3E3Sv6KdqVbbWBCGjZ1nfdu/uWjhq4EMxR8dyUGICpjKOMaYcvpS+Hbz\nVMwnsuDXGLgbEtwP/cSRd2yseDrK3AcqT4HT9+9WX4xdGRTuIDscooVRHGnR\nUG7gX6NpzVY/jQ9s8jOpvVaS9daHD3NIt2soD747xYtCPJKgwo9TK42X4pts\nho1m\r\n=TFkB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "29ca74603f6332dd25337d1d9f2a41fc58cd8f14", "scripts": {"lint": "eslint lib test", "test": "mocha --full-trace --check-leaks", "cover": "istanbul cover node_modules/mocha/bin/_mocha", "travis": "yarn cover --report lcovonly", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"source-map": "~0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^5.2.0", "eslint": "^5.12.0", "should": "^13.2.3", "istanbul": "^0.4.1", "prettier": "^1.15.3", "coveralls": "^3.0.2", "eslint-plugin-node": "^8.0.1", "eslint-plugin-mocha": "^5.2.1", "sourcemap-validator": "^1.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^3.5.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.8_1574264170082_0.3259653075687241", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.9": {"name": "webpack-sources", "version": "2.0.0-beta.9", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.9", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "49a504a08c0a8b1c5418c1fdca4ced06b5cb9119", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.9.tgz", "fileCount": 16, "integrity": "sha512-q6O+gKGojLye0Fm05vgthtmx5LPbYpAU4FsdMsX8YBgkOZovDbQmBMhjY/CiWhxD9u/0AWeWdXFNbQz17mkdLw==", "signatures": [{"sig": "MEUCIQCP8zEpL4wZA+qhW70OWli6+1/Rm2Tw/cnDaHU3gGViTQIgMxCIvTNWCT0i52EaQcEhykAxVKJHBrZ8gbHo0yw0k3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPinHCRA9TVsSAnZWagAAxrgP/1gd/P+HHXZhe/sMtJyg\nwLQZ5ws7gX4X/elnLY0phjzcQdowjPu9I6xMJbRhI6PZKpykpQVimaokFHBE\nFqVaDYsvZ1EowbzcImnJljmuhFxQ+iUj3JWppQfvjC5meM8ORy+5AcCCBmnO\nsIUwLMAERflFUEvM7J6p52sbn29esaFeJWmzG6fQI982RK3RBvn9J0dEpVfS\nz3IzZBRpP7rwker77X/HRXGhFAW2gPJzG2RE2tFYerHs+Z5V5RJUghEAJ8gv\nlHU76TjQWX8AxaWxnKuSNQfI/oM13bgx9Ju/hijp/wjt8TPwDczv2DLn52Yq\nSZMYFnriuUNP5YqU3qhBK+MBevHpdly4K7U0LgOxkdAIO8WaWz9RHBZ3EdGH\n0ZOBSLrfMx54hHncZbKpc2LdgmPrhN8eaoNxJFbE0poIiHelS1Pf7yxcmr+R\n/v5SNDnx6Ag9f3U6HI08/Oe6kXqi39Xeb0hY7yA6Mdfzf9K/zd2DA1mvG4oJ\nUZekJCyhg7GoYd/+9yIawDqRq07IZs7FCVLaX+DxY47GXbKyBbh0j+qGaQ1x\nUZMOEzbTq5GRELCUDldtLzgh/hlm+LfHth4hCsSjCve9eGWICt7NV8We3xt4\nfmUTM5BF+JTxix4mBNR4Lji5Cgu+1kB9xrThr+4FuNJprDirC04YCXFXceDP\nEoiD\r\n=H+m7\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "6153bfcaefcd4d0597e50dca253e23dd23c99ec5", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.9_1597909446805_0.5586015707814336", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.10": {"name": "webpack-sources", "version": "2.0.0-beta.10", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0-beta.10", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "f603355c5518141976601bfd620a3a5a01ac7b5d", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0-beta.10.tgz", "fileCount": 16, "integrity": "sha512-HxeYa9Q6nMk3MtSbi5mKUUV+gOxYlGQwujKbeK0JQ+SmLSMgC4cQkZ+xpsWvsUtTvskDwpKvuVLpE9eW7vn0IQ==", "signatures": [{"sig": "MEYCIQC8wkZiQgPqNmSC+rpZNQlTLEolpw6JFHKnJ+l1dp7WDAIhAL1rcyEh7Jxhxq/W37Q3R+8XiXgFLrbNmcXKgICg6nJU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQ+I5CRA9TVsSAnZWagAAUrgP/19pSC5Z4GDDobGXffXm\nU4ud9Yt/uBeRj+oIGNdnBjRQDtACkLiH/HpW1tYXenLMg6JxBToLsYOy1AJu\nMvoIX0y3Jvo/KRjjY431AV+gbjsL+/iSljs9sUWIYTlf/YZXkKT62gaSE0SE\nF7GR9ZDh3QhAjnC0fPFtZwP9yFdgXdErgvAvnAUbezkRXjSZMGvHu8jhqDlm\nXMHX6G8nd8cysDGWFqqrEioT8FGHFU8bgSdLh3FY56fEZOfF2TwouItlDG2d\naU3nwGETBsovLpEpSrRy1RnVK6iygLQfswMgK3uRp1aQeLQylfsdRviZoSLc\nM+W49jP0WlX/bZj50MmoxwUd6gmM/Z69kKUvWC2erFU6R5+H/8TERI9AqrVy\n4M2bSRxl2eynmsI8SCTtwImufC9HBXPng95xRRHvSscATZx1r5yKZa24Ndo3\nudweWXiCORZFa2a8AxLRuLYTPPPOko8JEAxSybR4KIGbR3sa92qlGYqIsZ0v\nERicop9cRWNub11VkoLwGJqUAPgU18GST56gPF6ywQIUFK4p18NNhyfc6tc3\nYdz0CWhJTHd+KelgPIEPDDgKbL00Cl2+UnPEJg3/s91Y+pH+lDX683ftbamb\nT2wW8skkHZ+2iUK4i4YrF9aBn3I74rHdQ6PYaSh7dmj10EST+h+GOgGVTSx/\nJRl/\r\n=lplw\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "2649cb8227f448ed5103ab0a90b636f97b3fef3a", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0-beta.10_1598284345373_0.6551735511521408", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "webpack-sources", "version": "2.0.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "602d4bc7ff2e630ceb753a09ef49f260fa4ae7f0", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.0.tgz", "fileCount": 16, "integrity": "sha512-CpCkDjEKa5vYVRDFDRABBkBomz+82lz9bpXViN1LBc8L/WDXvSyELKcBvBnTeDEiRfMJCGAFG9+04406PLSsIA==", "signatures": [{"sig": "MEQCIFnegy76keoHksF4Z6+xnFK8uvuMiOE6crROuxzOnp27AiB6/PEeIz51IWHME3d4je0vl6g144UUbGGoQ+x2S/saxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY/JMCRA9TVsSAnZWagAAigkP/0L58AGESVIqcmngkxg7\nTz2jC1Irs0xtKfhslMfQV1Od1xNaGMi/rkcXxDSKZb1489w84HsnkCB2sjud\np+OkdUWMKvMHhJt6OjQInFyU+DeIyI5a+3KrUFVx2s/9cq+z/jtXzZCnbQas\nqm9wLvOt/bO4NiiI4ZL02JsfF5J9NYOic2Za7X5z4S4y8AgENVOHDNCQMImy\npz8TIjTkSlJpELxiBrIX9NU4WS4O1QG0uBjoQgY0vwtmyKjDfj7a1M8tV7gI\nrC20mx+HOkiEG0s+vnbDs+xRPs97VJjP3J5dUzTYO+2NzbqfMiNH3NySJkKO\n9p94UPauz92jzYQP53XO0d7xrKhSXLo+IPRQ36eNIpMyjKd9V6vJDH+6JFrq\nUmbkxUoGv5gHJinjoSF5m2FhSxFh7GKxvpaxctv1kFdylCTCalIrOs2oGWbB\nbz3IPJsCYRmGCOcrBSsDMZsOBzus9vjOncRKda69AtXQLabVe94wbxDRwNwl\nJfQEQA9yUrdM8tqQeYv70AtxSg810yEvjhWln7xbavv4Dj0WtfroiBKufyBL\nnQmHImGj5GEDSJwjQCsfpS3YD5FFsPUdi46eGvY0tVWpShRIqYUBZHj2NLgv\nX7g+PMGm/ed3amVr1gM2C8Z/Xk5u6wam2OApCGVaQqnA0fFnp3qXKKiPLBBn\nMP0y\r\n=br+3\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "935731bf2666efd787105d17b1f640f29dd72c75", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.0_1600385611454_0.6049086405021515", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "webpack-sources", "version": "2.0.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.0.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "1467f6e692ddce91e88b8044c44347b1087bbd4f", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.0.1.tgz", "fileCount": 16, "integrity": "sha512-A9oYz7ANQBK5EN19rUXbvNgfdfZf5U2gP0769OXsj9CvYkCR6OHOsd6OKyEy4H38GGxpsQPKIL83NC64QY6Xmw==", "signatures": [{"sig": "MEQCIBXrlI3EPU57Sz5jllVRegURlxkF7tp2JexIc4PA8I1YAiB6n+pWefi47gnAjxos7qmcB9O43EyyqqtD7BC9Hi7BiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcgY0CRA9TVsSAnZWagAASiYP/1nRnde+DVHNUwzCQr1M\nJFXEb3YQF28gI8AOTPvSZ8TdxyHZH+Q6VMZfopMqdusrUZktx5oFGzzIeGe4\n3sLwFBmLhcnQoQIDDLtFt/Qk1IWElT5gSw1xXldDltHvnlRPcl8zRnxAiu+T\nkuOyG/ItSt+K0SQRiu8GcItvQrkvPegoh9XvuygbFySTTzVU0r9oB9MU58GR\nIdTbdkc7OC6PIK/afCrG7PYuOo7ZQpGqqEQ6FpacPQW9OG+bu2otj7/1xw6W\nsnBG8ucsyv00asT+kxplKUrCNpPnVRwM1iEpxwX39/z1sVXDbTubVxIZreBK\nFtcMcrMv9iwKQDDBriEDFbYiaxwIfuohmd5g4iAgtlb+orKIgjvkhR16BxR4\nkcfc/jgLOU/+g+pe0cSsimgfP1fdY/I2cTxSOMlfPO6Cv6Wy5JaUnCtvbXqq\noJ1p2ceF07xqgbOq4/utoWiE+7G4lOb1Kv+iRXyeCKDNKFOCX158CJeZMwNJ\n5uQ8X6dLi9yGQrJJPM4micLlTAryNLRZxKS4LtkoYjN1PFRTy5DSZ2Fh2yAd\n8P1Mpl6kr4l1QYzr+U56XfEx9cQ9le2Z9DGS84ahWWmp69rM6/p6uH+idA4h\nmx5QrKmEIs51tL7RAu7Ja+MXGF6RCM6262aPEfucYIktwPquV9bMLwIggOpT\nQPWF\r\n=oGvP\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "511da963907e8771fa7dd92423e603c689efbc41", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.0.1_1601308211595_0.9999199735382227", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "webpack-sources", "version": "2.1.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.1.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "0d6d309f5e048e445d34e22c07073a238ca12730", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.1.0.tgz", "fileCount": 16, "integrity": "sha512-gOlNIlnBzDC4yIqRcguYXscNRfSL4KSd2EXMXxkTPCao56YW8CbjrY+EfW5fqJNOdWhlFMxGTy1ctwJgeprnXg==", "signatures": [{"sig": "MEUCIGCtbsPE8f38V9ZklRFkHZkioD+JeJvIR965huKaEy3FAiEA2KnRghfaBU7YC/kcn0XfyAmGAcGzuAC9nvCIRGiAdpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfloNjCRA9TVsSAnZWagAAW7kP/3gK79vCGL58JZEBYfUm\n+yXKYupbp9TGGYDg8X6ajzM6qC8cpUxY2Tk2v8flc/rHltlyqCwBKiLfooHp\ncSpZsi3fM3YcW9RwVKkGWKNvM7swYYVm76g56rM4A/6DLzf7Vl+eFAih5NtE\nBp/5unIXcmHURumClhjTyHuIcuF4N5PiXJ9JjhxhRH5YPgm02EINZjqUUBd8\nO0At5f5aPkxW4Y5clNoxx+V3dppBHCxEMCcwKe1c0Z8Ai2llV//YxHrQBJ5K\nXaD+PCZyjNP3vSJSuN/KSFvC8KJ4/NxYnSaZbCd5+B+wlra3IC6stMxE0b2P\nST5DG94JX5QgAuDopsxY0bnCVw15lohysYW7KF9FB7dveijWaKhFNlpuGLYG\n4PNsoBnMfN/wdAShq4WgTC8whmHqUhoTB9pKkaeFQA6Jnvx61UANQo/Xds0W\no3N0LF2HbIOMCikfJgg0Ap82KoDkXX2WX1xDyCQ0ZC95tk2FoG4gjA+512Cn\nXyxdrWLnQ4VBhkaJ30JvWaUuyh2QR7lMfH70eDPZqlYw0DicFtbBc/q7Qgzj\nJ7E8yYlF9M8FSKyuMvwVpbX/bCP3lSRyQcM+j4xWNf07Pm1AehSYpUXIWTG0\nPykm4I7nAMl3XK4eumuQb4DuWpjvyzIS8qnRn1BAtMSLSXXdOtLti/2HHCrn\n9lQ9\r\n=Z6HE\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "a1e17144ce56fd7222f1a82983700c3198e74ee9", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.1.0_1603699554917_0.5853037792660696", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "webpack-sources", "version": "2.1.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.1.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "6922dc1bf7f7f95aab56675535b6e20f87b9147f", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.1.1.tgz", "fileCount": 16, "integrity": "sha512-hraq9n99K564zuZUsE61iATO3jvzxOmGo20UlOe3zgdHOBp8inTJgv7EY4RgvCv7Ywx0/vpQTyYSjnFpv4gNtQ==", "signatures": [{"sig": "MEUCIC9zqSvY/1zUVZmhpH+qypcuJP0zXplzGcuDkAhYAi2oAiEAunGaI0pL8ksfeSsymO/SzPx0RcVg02P9LZ7XpxuAaOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmWaOCRA9TVsSAnZWagAAevYP/0oOcmetuvWqkXaZdic0\n+qMuVBLEoWQ47WX2aXaPPBnvW8MHcsAfAMjGKYSYRyi69UfdnSLctlNas6Mm\n0pRyjm54b3UsXpm0q+05c8GIt4xGzaVFIwf78r7tl9pb3uWFdFWnA1afJdEx\nQnqpw2ZoxjyxnS79NLkvn1jJKIl/+5jLq3EfQH6broMO3KBTw1lEFGOTxgcV\ngPtgb7rcwetWGrD6WKh7rHCw1INhDnjyrwYFPoYjGRc0XJL8py/2G2wMg8vY\n3NesU3BPfBCDfAgTZVxEjYGHkZ0zTNe3eOkAlFNuJ8sYaofYY/4JTmQs1eut\niSaAfVyxcqT11x4IpyYgbQZ5BISkVLxVLwn5I5JGobL7sn59RRVDY00zINvP\nKIprwd3EhwQRa5uGjQQW1KWCsS5UwX/0zuZ0Hxzc1n8rFBmm7EuLm6pJhq4j\nEcSRihbqDpzp/RCPhKV6tSLzm2bc+xTTTO4oo2bVb4yQPaNqfONYTf2WWQv1\nRND8shYGmUQUsRyjmgHCHTfCL6FAUBH+JDVS38iKqhxRHejCUJ4263GwJmdX\ntdCkiMtHQRwrR0odntvPjYSTGZyx0QPO21xBMNuYSWoopfC4v/E6BhSjbL2Q\nudTq5Y7469nE+4wJkgcTRxpFr9M/isLAen5wP3pwSYlGiRuiFpVhW/UDaAFR\nJmGz\r\n=2xF3\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "46ea8bc6b7dec030ce1acb6d15184864f3eff7dd", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.1.1_1603888781445_0.35872761914291096", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "webpack-sources", "version": "2.2.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.2.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "058926f39e3d443193b6c31547229806ffd02bac", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.2.0.tgz", "fileCount": 16, "integrity": "sha512-bQsA24JLwcnWGArOKUxYKhX3Mz/nK1Xf6hxullKERyktjNMC4x8koOeaDNTA2fEJ09BdWLbM/iTW0ithREUP0w==", "signatures": [{"sig": "MEYCIQClfUI3XqFDT72YdsQcUdgOy9qZ2zwFiW5pvLyjzvU7YgIhAO/HX7f27E6WcVCXNNiHSPc1ODN7VHRAAuCIOb1GUUDW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmpe1CRA9TVsSAnZWagAA4EIP/2kv1SeQcHyk506Dmt8U\nQ6QgOUU2fYsChf++7nFbrXPBbNbTXH3vO9/Ta80bTr7GetHubvxE5s7w9iwO\no3WBFEp3cF8RU64CgTqcJQxQ3JipufOmudGX6JLlKArBrNciU1SF3N72zyNj\n/00MlOH7nL54d+VFepG6GOG9HV1CQFKEZtTFuK/EM7+K0Y9Xd71n8Ppayyd5\nX98b11OvZXhPRjAaeidmXuP3SQ+/swOTodqGK9NVYWU3I0v2ZSOTBdRPQqs3\ny2WVQ7hqmhV1wBya2gJ3f2vxepDorrT6oE/pK3FiPPaDQ3A+UnSIUwr9kEgt\naIWx0PgNknqYawGJM6xNFNUqHQSF0bfJu4lw1jSQUIxmizfHukrFQNZaEuml\ngCF2sxeMOH4pHIGU9oItYco4bV8YRsXQzyL8u3OicV5Y8ZGzzLDXyJfXDU/3\nCcle6R9eYIsUauNBmeNRFmEezup25YqS6LXrbwGHZKrfLiPOKz7dUPOpZ6jT\nxf+QoAIRKsduOtlIIH4GtvPUGUu0AQtUgElQEhQkrZ8WYHWCgG1QL/q7kkh7\nBoW1VMW/d7HQMRF8K+XUysEvmxxYMLydMyAaVEZ5lEaA+BhN9m0UpCIhTyVr\n4FDvKRhdTiSkTEmRm/kOCZJKo1On5mGeKhSO21aZf5Bnhi/5P/zNwxZfAyCr\nWSW0\r\n=D0vC\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "9ec78f2ef3d9169293636c48b7741ebb7b457ac6", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.2.0_1603966900427_0.7132752111300411", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "webpack-sources", "version": "2.3.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.3.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "9ed2de69b25143a4c18847586ad9eccb19278cfa", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.0.tgz", "fileCount": 16, "integrity": "sha512-WyOdtwSvOML1kbgtXbTDnEW0jkJ7hZr/bDByIwszhWd/4XX1A3XMkrbFMsuH4+/MfLlZCUzlAdg4r7jaGKEIgQ==", "signatures": [{"sig": "MEUCIQCUiUZ1hQ2ZECilKUdRcFEz5oOU2YqRDkdp5AeqmHkf3AIgbb2/Q8mJMOWX/44C+Zi011Nxtv1H+DOsk74DaFvLs70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgr24OCRA9TVsSAnZWagAAJGIP/R1bug+6ILOUyUnMknAQ\nE0e66Cm/hvn8/z33Cbqf1x0ZqJ6IzL3BmjYioB8NYx/2Y0LVWhQfEg2e1wTX\nFBbO7X4Hr/hEewmEu4+FzF7h0XxKWA7raoUOnxZL4X7lNMNUlVaC7tMSR8f/\nmfjfKi2HI1mX9LaXvwDJSv2JnMQcounh/WOK/DVbJG348tPI6BgfW4qKIurS\nLtTawcG0PRin4/E84NmhaPtQL0HiLWNvNei/dKC1rCsE0nWr1+7cPOX+55dm\nb/7WxNvjseTabj07GrnQK8R1JBDSJk/7udTCRY5jCyodd1ISD2VpDdd/kfBP\nD0DmkEnYfdp/srzAnCQY8em+9t0GV6z3LUD9WiFtKTE6z7cOrAo7k7MkGA8d\nxP92pqoOl5Wi2X0y9L0cDEYr/mLIFy35BEELDew4F6a4qCTxQjPCNgQf+UW3\nMIIh4jHLq0HcpphsaSTHgiPITKBvMVoCPPeSfK4Gob4jlXYP9Yil8oRc5BDO\nuAq37P2PSLH+G9rEircDhl05BFzif9z1aTnruRSUCoO205+TxfDyrHZnAMH4\n1iAYdBrXN3udSyZuqgKbsfnzkbxKR1C6qc6KYnWYCmMnmDnZlS1alBrRwIKE\ncXZUwwSpFdMKdBQwlpSTiEeBFpfhT4waGsWaP2Ro+d3vxs1kFxOOSQnBMTcQ\nhpTO\r\n=VceV\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "f943746ba8cd15f263dfe82ad5dc4470752618ab", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.3.0_1622109709999_0.2033264264455399", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "webpack-sources", "version": "2.3.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@2.3.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "570de0af163949fe272233c2cefe1b56f74511fd", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.1.tgz", "fileCount": 16, "integrity": "sha512-y9EI9AO42JjEcrTJFOYmVywVZdKVUfOvDUPsJea5GIr1JOEGFVqwlY2K098fFoIjOkDzHn2AjRvM8dsBZu+gCA==", "signatures": [{"sig": "MEUCIQDJY9ffGQjh2Qtx1BWQXsQk7imS54AzKSlVtKAZAHAtSwIgNrGlWeD0x6NlGX7r12kZeC4Dhpj6kxr+d4qUJjy3G8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9pwaCRA9TVsSAnZWagAAkLIP/0HiLK2bQ/A+AgHHPhZY\nQ+w4mzFP8oakAGPGpgOPs2gsM88o4tg2glPx6v2Fwm5w80TEucPqM9CJRy8M\nAZQuoR9givlbH+h8HqCGchc3F+wnBIOjX+wBMQrwjT69C4nNEnGFHuktD42b\nxcw5qUJ0QxnKOM4i5kB8Y67Sh1bGiJ82USfSAcOHxxDR78ONVr5vzuUfzfgc\nWVOOuvTssG8thITbRhDs0LfTYXUhQhxc4OUVKAgFaSlYH2xCBozn9hHW/kvN\nVMtlPNqRj7G+ClcRO0NPpKIsJTSvaNPQ67QbNe6T46r+B3+uEx78UIB++dLN\nNJHRzqeFpOA4miaH+dOKiLgLPNTprHQRnOVBQyGFPTcYteft6SOR2vEjXo+c\nQhoHdzfJ5BykF+SWEGR+5lS9YKHAwwF9//VTGmSAF2Gy9g1psT+Gu8hqHxCh\nEwGN2xLuNoX+k7ezUZcDOPk+AyijY/wr/HQWccVbcXX3kyeoJREu4ERNSkji\nR2pC62q0eDhEPvXBVwagAROppSluvFgyeyUXpQpwJwJnFyNhNyjoiQSU/wbE\n8In91sHlMygkq3sLxPaEDLWffAlfv4aoLD/bqe8ZurqGA4n75irGao7I/QWK\ndkofhCLRTR5ABF+BuKdMWwFsTXKvXIHZmds+NYKHx4uYUKXITH4B5RlwVR6e\nuh52\r\n=lrXq\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "a23b258c91a7753452114027bd3a1578b6f6d7ad", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "travis": "yarn cover", "pretest": "yarn lint", "precover": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"source-map": "^0.6.1", "source-list-map": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_2.3.1_1626774553883_0.7945131801599374", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "webpack-sources", "version": "3.0.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.0.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "aa8fbdbc6beee44e4131c3c3fb90dcbccec01631", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.0.0.tgz", "fileCount": 25, "integrity": "sha512-RGfE5xRC0HIUTj8flXbBJWONiKRndwZGcMX6WgD1VwdT9FJqARhxx1sdZvMiox0e28jAb4jIKLQxFSCVz/mD5A==", "signatures": [{"sig": "MEQCIHKeI9oynHVMjH2EGP76TjpjpegEmIJWHNuyar32OSFuAiB4SaZNr9YCd8319clasqP4s3VSY6NQu5hk6/257ds/ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/0lGCRA9TVsSAnZWagAAHuIQAIv/daPazYdniMd0sJYe\nXvc0xmextVFZ+g4vqwYrd/gXhs06RQSo+bIZXfuMv7yHcc1lmqdR//+pJO7k\nYY0P76prwVhm+BViiHacpDniXnHPpSJ5U/vH4KCDTMTTaVI8sgeWLiebsjHb\nMJ0ckn9CuptvT3SM1jBimdZWwblLScRznsv3NswKplAvN7Qoq/HuNv7bX+JA\nJAXYrrIasVooScFTLJW/A7jA1a/xwkkUu9hfFCGhZxNrFDYSvSCFiK/grRz4\nY24GFaa1dvJNqtm0/4SeAV5VRAcS4GEpLB5ieJNzXVPKXTp8QmwuBTckuDAj\n4rLCk55bR4yI2ubwybaVPBbUJ/NtfzH2xF7F8DSjWhwcIqXIpluqq5IKY7el\nNxGKWnyzslHYCDhEKxY7QIN9JzW7YWO3M6fDhIGxv0nk790JEs7Hq2T42UC/\no27CBvdlytZN4v3HPH9+DyRuOGxE6q5HUtWdoqkdJq5B4JPSxzBTkGtB/rii\nf5fBCHvFi21pTe+ECBSn7EuyIa5sbrZJC6dS3+yQ3DVCVXZuhvsFF/rQ0YcG\nhK3zYF0e58bQ4kdqGyJiGokXM875wFNEEWYAQKL2VgoKwtKoX8aA4hJvZhcd\nIzfhypoA1IcqsKUrAlEXWDo9GHbI7fbNcbabqHBFrdWqoHzLyyj1l1X2YPbu\nrpKZ\r\n=svvi\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "657f0c07e5efefc667b3c55c9d9d8267cff19e7c", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.0.0_1627343174127_0.6508331026679952", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "webpack-sources", "version": "3.0.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.0.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "518cfabdbde3962f75bbecbacd11d88ab3205252", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.0.1.tgz", "fileCount": 25, "integrity": "sha512-LkBxiXJ3tTuhLaS5gz6D6l77Et8mPWlghAe7bbnmi2PyN1CtkiL/YitR+I0pn9PtBC88Irqgg6F9dBJh8+sJRQ==", "signatures": [{"sig": "MEYCIQC9fzLdfs8MM+Z8DwOrRn5bGqkFMu0hzmDx/pzt0Cd5FAIhAMytFBXOq1VCLhKHNoes+vwhvVnDeRIRIskFahGAqIfY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/+cHCRA9TVsSAnZWagAAs5sP/0iBmP3mKE/fylWHVf6Y\n8lUb0Q8m63OCyQD2HpqwuGUNEV3X3MnK2sembZBOqi+2TQ5rdR68eR1CRjtb\nai9DpczAITbDXthXfrf4QhLEYEi1WEgf1uORY858PBS9WLLCpztN+fQ94dmw\nqojqCGd8A2fOT4ALCjPj0+rBJZY+Tio+4XQeZLcDBoNVgFHI2OnZG/+QHJpc\n2KcehD3QTPS3NR73BPmFkMqx9LKMxyBCM6QPLJp76dFeCm2kik5PJnExcuQu\nwIlgQriplJau0sCsfURjlMYjT6Zy2U0iAGCxFAEMdgjthGDlpoBX5S4hfTf0\nhTe/vaJlkkJW7IZ3kBQKxTipnIILP9PPgTpLRRWNs6nw6SkEAJStks5AfTGL\n+SJJglZBdW4rLwF+WM8UowmXRJRLJ2ZkJD3j8jd6CEjfbwrfpkfbk54y/dCS\n2msf6VcdGUcNi10xdjC1ybyyFHPtM2CJVN6orXTTj8MkrEjFYY5TAy6V0Kla\nnpOeDmv+Cx6/X/XIHyirTUb1jOMxoVUTcyXY7YcH4aQBDoUDsILCZdAjx8hf\nC7o2EbUgej+MqrcBVGBLBiuJGpM+Jz98ihQNTiMYcvdZYAs2n35GkLbSzM23\nqPMQFu04rOSJn19ieIclcmckNU+OBfebHdfjUKGrKuY3tR507j015zF5l+Ju\n52J0\r\n=wr1d\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "8195523e686d2b4e39cfcc8169554daa793ef74b", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.0.1_1627383558836_0.782149032241402", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "webpack-sources", "version": "3.0.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.0.2", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "29942415daf201a06278f8e2b92e44e564a9288e", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.0.2.tgz", "fileCount": 25, "integrity": "sha512-XQ6aGLmqoxZtmpbgwySGhYLNFav1W6+qgMWPGgn6qScxfGrQgMdigkUqZXQ7oB0ydUrvfs9RRyHaSfV153K8Xg==", "signatures": [{"sig": "MEUCIFFpChlPE8vNpjkFUSHVMC1Rqhm/TbSKcN8i75c+H2VDAiEAlWuf6JuYlflRPBKFMoxPEVXaArRq7odJnOIHhH5i520=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAQVkCRA9TVsSAnZWagAA3OQP+wXi98E3wUpHskmuZNeZ\nVlX0T5kN6Ew7Azctufb6pkxvfDn/74M33NpOtHXsssTXAK2ttFNyvKcjRgN/\nqYHlAtN7XnRxbrIm+9WAklpefVdbcmfkIKp84JPZO8vL/uB6YQez04zVv7xn\nx2OSpddDk57RIsz6xp4sjbU8QaTFl5vJi+WNN4/CYyIWixPAwy5nQwEemNi2\nKbabYi/YnOoXaeVP9shFnTj/V+q/w3LFKYCMjfCfAc9uVHv9PguXfTJFxscU\njblgf3JFbnH2s4Q5TDUsz9BGPITqq1oNXkDfcVxQnI3niCpCnJMdGJnZbUzg\nuLHgHz+R3ooIVe74JZhnE1wYcT5ApCnRlrkZLb6eO2EBpF+HCQ5mYC2nXZCD\n6AmwI4PM1a6pgK73XHBH7NC9jsQO2YpHIW4t0G2N8Ya4CtUboyg9Fy+RpJrT\nkKTISxp9fmtOYI72qE8JLATKOOxpdVmvbRMQBm+sRyW58BqHr2XE/bhRLZkB\nLeXTVkRr4jRjow0mIhLyvjaleKgmUkre/pZcXThAXeqoq3BcE667rSkne090\nG0xu49T2cT11ujzqLIjFGwXAg5JvdjJ1enJPkbMwHrHpe6yNjtyoWqxYxnYh\nTfi2Q7UtLuTnGNPUhA5l4uFD+Ba2buS8GEWn79hw2ZYPL1q/WwjkVMIdsuUz\n3U4+\r\n=K795\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "5024ff8d42c97b24a32f60b4d0cffa2b5c3a49dd", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.0.2_1627456868338_0.6160292192587054", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "webpack-sources", "version": "3.0.3", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.0.3", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "33c478e1f67bf5577d3ec5ced4bded0a06ec88d0", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.0.3.tgz", "fileCount": 25, "integrity": "sha512-/Qgfp3i1FT2z/tpNj+d/ZeDTbdOWG5V6DdTjIvMLVhrhtpFxmMTZrGnEQEa0J7HF8Plls5kGa7TZ7IsvgnFdtA==", "signatures": [{"sig": "MEQCIBxECbhvwYaUjC81webHiWoXeEXzR5NB+K405G3ovUxgAiA0FYgUvf1BFjWGw85jDmt9WlgHmAF9uPY/xKa/8c3paQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAWjrCRA9TVsSAnZWagAA9psP/0l+ibpIOVgHGPAM5srx\nPo38Cyo7XUnqgoZfQkZO4TpTRTKlt5AuW0i/JnAWGZKswuafWpxpPtlZc7tq\nmVnUc8ftU5oWb+WtY8l1ToGXES27NN7orMyE276WiydWG2RLqOUV1OyYIPsQ\nuGAwYDgosAQ2976axYPlgeo/RQvCyU2B74T82GJClvJDbHRS4DHE9mfDzZAs\n6l+KvwhR8K6G3wC9yvjykSD0o+/Ic6Xjw+dkxVe+LDcpsLOJn0C1Wr5GI0YG\nEdt49Rq40ZnNygLOT9n7XNdffdxQx/iT/DtspggwMc+U7y2FjiHQuLUolrb7\nbvWFFFSgVsMcbQ9A2cSDbTKoHCJ7SVPepXPvFgfuZd8NrGcuJBHKTLHEmPid\nozNYzfEeYW4byZhLA/dZjBKOskZPxfxtTCeed32yi7hDSBFkkwZv5AbvNF88\n0UilU9WQWTern2z0DzddketyjQxeOvf4Y33tUV0+E8hGBi3j6z8kDHUmT6PU\n3JmXNulFR1oFllazaWToSyJKCm0HedWOZlHJhDrXjzikSgdKQNFfSLF5vKci\no4UJ2v1FTvxJnIGEeLiYMjf1MfyxuSR4GQ8b5vIHE1xx6Y6EbwIX03paO4Z9\nvaQ9q2aagIZC0kNTITkpEiOJjK7dyAeQAng34muRdti2dTH6f7r5I34xVq9v\n3Hce\r\n=GrLh\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "c4d3e28dfba7186d77cbdcb7e7b51086dd9584e9", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.0.3_1627482347033_0.3979189877276963", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "webpack-sources", "version": "3.0.4", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.0.4", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "1690e6e2beb9494c27eb3435ac8b57b729880183", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.0.4.tgz", "fileCount": 25, "integrity": "sha512-9bF13xKtYl13kHJTK8p63q4ktH9E2utn/sm+JHQGC5cYxo+9v5LRqjgStAsAdD4+Tzob+c4a0eK7LByhMPBrcA==", "signatures": [{"sig": "MEQCIBPmM6nL4BuieHQzs1Fsdqx9giGpBbsj196xJ73w2GDoAiAESVtG1JN6/K+TxIjoBuAVynQCcTV9djg38gFPRgOYlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAnMWCRA9TVsSAnZWagAA6msQAJxC5LH0Rn/782Bknb3A\nzmICOl1rp6Cwb/3TUvhUUc5zmnyIQOiHblcxnkIoCl0ZOONkJ42jaCpyj+u3\ngqRV+VJ1HD+zdqHo/ytEQbhqWgC20FZl54BnF4grr35KxOn44kAZrF3dl8S9\na4q3VpXLr3+Jsv+tUmbvhpdgxp3eBg82KjfszlwWwCFvzWPP+MhhoOwkCk7J\n4A1HS7dlD1M+2JUsVlZ3K7Ne5PPmgd+kbQ2SRrTzl+/7fGOYcKzcBbjJlENr\nis2Vq/SZTSXlVuPYfkrIOZ6L1TVLKBbWI83JSg74ToC2n4PYX8D2DJsNC9Pi\nvfy4+DCQVuuB08lWZVq+T+fv5KmeLTNllfwKhcEtyGXuBPpiMYrmtgtisFCW\nBOGH2PdL1kHdxQ0UT3hShNVRWqD7hyFf9AE6Ng4NiefCy12WIXDOUgv0mzfw\nw3P7jeEBKrwh25ovEuOBFW0fvxt9CO3h6oywByXPQvvEf/QgFlSd3rK3JQ3n\nB4o3kojtuVBIAIxfA/Y6S9xXM+isEKEkArpfagnqz/T7j4u+MFGlm0Q/glmn\n7Aa8SMtcQRiN48KbzYzXisri1wxSKan1bIZvKajCz3zSO2frGypTUkdoqG9y\nZmRj9/JjTBfXnmi54dk/7Vz3hG02kU8cu9jKl4WwzlCT4iOpP0LNeZk4EVyP\neteB\r\n=wbu1\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "0739bdf198a8ca3db59dd8fa64ccadbd94c1a5f7", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.0.4_1627550485940_0.3766519509745956", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "webpack-sources", "version": "3.1.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.1.0", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "e76f64fa033863dedb4870d0cc078587800326a0", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.1.0.tgz", "fileCount": 25, "integrity": "sha512-9bW1iYAk0XpzI7kdtz7u6NCWB5BhhNrNJ4NhD9Mc8uLs1suTRzfkZhTlElwucNIoNfHr3Un9ifbX0f4sblcUJw==", "signatures": [{"sig": "MEUCIGiTrQb+TRJ+GNH1o827D6ffjwxYt6S/8xxfH/1QAS8XAiEAhTXXkOq9WZCqr7dKL60U3zYdwih9RQGpd6ocuecN5Dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhApugCRA9TVsSAnZWagAAzcwQAKR+HyrGEz1tPsKdlyV4\noNOtzljoz/9t58cttWhkMC22JtTRaNw7eLJyqbVQobV7z+5pNUPP8fYFOoL8\nfuHOdwkQfEpp3LrIsEGXsd9ZtO2bSZg0BHI20+KDqHWeTgcrZPHVCxdbmoMT\nApuUfuSpNqE/joJulvrWmy50M5eMlUZeXjpo2RBkfhmGSnV3bpAKqCDSYLDm\ngwmW9UBokfI4KhoIw0eRR0bMZdOfJ4v9GVxlGd8fJm5ZrAVlw2uth5NphTRr\nuD6GPNqBOF6heGl4/WqvUVbmIT7U+hQup/amfxL8MedwiMeFWbYcu3Lxw86k\n2ODc5pIfLeMq8109nQ5lTSgPXN9Tf83Q28pgzOFh/qtOpuV9lzrFGA87HWyV\nKI2hX2nra2QyugC97N9n8QNo9vImQB1/cx+bSKRy9yv9cqnQyVn9N8XQBmjH\nI6DtJa21JEDQJwbqWE4RP5ItmM68nA+PeuP9qfLZq7EzXtByRntYrI9r9PS1\nQYvtBfjzzIMXzPHdtvPW0oR0RnlFkLNUEtvRFfCzG1adqK02ubzjXscJ+crO\ntz09x9ElMLos41QlA2kmZqby6Zc1XE1Yd9gFSTA7Nge/PgPy1nVEdjDgQOmR\nDUj/aPNxk7i/DUXFnU7p8QJEeOqrd9xMSw/MogpO9mU4Wyp11xPqtJHhSV6s\n6CAk\r\n=cXYi\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "dc042deb92d56cda934b02e2f1bb72d59c2ce2f5", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.1.0_1627560864806_0.9961857755070878", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "webpack-sources", "version": "3.1.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.1.1", "maintainers": [{"name": "bebraw", "email": "<EMAIL>"}, {"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "spacek33z", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "586d15bc9a9723765f6a735f672357d6402f9c57", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.1.1.tgz", "fileCount": 25, "integrity": "sha512-ztUmIWq0LWaw+1YyR3bXtUPjt8vQedtI9WxGn/q1V1ASHsombnaso7MN9S25lzKS/OuC9Q8lEg3GsZexjDbdlQ==", "signatures": [{"sig": "MEQCIH4oYsryDeiWE9+jJ3NNAGBHJjPQPQmNIlq1uo7HdGchAiBBQufD9p0ZscWoWWGLiOaRNTwznLuxWmSHDsa/3OSf/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAp5JCRA9TVsSAnZWagAAgYoQAIlUW8iUKSdDXMJTUx3g\npPt28mZfFmxAMJSbGhfLJinBfA+3lUuZOVqE87SNe3TN5MVPIsJ2YVx9ET63\nDi77emD+T+QKJo/D2CbKT7j6Nr9sNF/QIgsZx6M+ISqqFMAdRhrKeVHaG0TF\nzOQD5sxCBh4xGQH1VFcCc8T0KOJ+Ztrc/YBE50O3SsKJcQS9M+Du9cKgTkNQ\n7fDjqDjVr6tEikALyMCh+MbGSmEL2b6lqYrFo7pyaMOA02D0prxUi9IcxrDY\nPQGSThtNjUCtSwRN15YFCvvyVzF+mrl3yp+I+ptk9+Icusalqj9gKo2u+lM2\n6IvPHxXNFnR0wi7imHBb5Q1hFsHuC5BQLsof2LIxr0ZsgMHBr2kM1IgPCKCq\n1n+Y/7eYeWcDp5YMSotg727fG4R0dsqWeyO3oBaO1d6N3ROfaMLbPMuUY93H\nnFkA+2NHayeiCwbw55DaL1K74dDAeIjZGUjUMEbgqzoeQDlaatUSqY0ZSRu0\ntSK9D2VX6GlYZSLN7r0N1uoEbnucMIVNnD1KS2WuVseyUchFa+XEAi2aYg7C\nGp08qFm1F3fAzF6QhUJXSu6O68y1yuw7d9UxKOAJU8CbU3WBS2iYuFRPon5W\n/6cMKq1u640iyLG3LYGbWz0T6t1DF/+CAC/jVaaDdwGYIlqmpeP64WNn//iD\nHnil\r\n=vO1L\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "d103b21f883de762132cb665d8444904185ebae3", "scripts": {"lint": "eslint --cache lib test", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.1.1_1627561545584_0.6082941802941475", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "webpack-sources", "version": "3.1.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.1.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "614c962ac45dfac7f0f482052db8324a6e0dc274", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.1.2.tgz", "fileCount": 25, "integrity": "sha512-//DeuK5SzM6yFRXNOGK+4tX7QB8PghkL8kFBPyqSlN62oJOUkmby8ptV7+IBGH6BkIuIw5Rjd7OvvwZaoiF4ag==", "signatures": [{"sig": "MEYCIQCsFptOdVEpQvWdeKDjHSPBMi9G+S1+vEL44aISuBZPigIhANSKcYkddBLBzVVPrafDiaK6ktEmeowRlcDHufkaYxDZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhA9BaCRA9TVsSAnZWagAAyvMP/0vjpMWZrLNXP1fVwfUS\n1wJWQFnJPH1S6jiELqKfoz5Q9QgPlXey7iQ1ByCFS2g1jexzvaal0xEYtBh8\nQOU1tD5UM8UyqxNNxkbXhf4eO7SuCumvhVRgpv0Uvjg5PwToBsusMIkMGLg0\nfP8EkKx9CwZpv3/9Kbz7W+ZuOX5U1yi5dizUhhPQcbQvFSWszDO9JagdzaVy\ndxvzn4p2GNBP37M1LJtAQoxBP0Md4q7PtUhA+OxIz7lvRaRyn3vKDXPUsI4/\nf4e2SgAaMntOr5Y0YsEDCLNuBVXiYyXkNLYoO2PMeX0gF6Cfv4XBneEj9sVW\ndoUvaoI7t470EuwDwCABjicumQUjfF56wVlow6icrN8sBlG4YOoRVCgnH0xi\n6Sfmwyup3ABlD3mSEqmpyDbwhWYQAu3yQjExZEjg2GCBvnTAwFH8G8c3lzli\nKc48o/eSPTFhpuq5H2yRnj4snfsy//4hXGltquaXnsF716sRwHYP1ZicQ4hO\nd2DCR+nOxp+p0uClZU61aJxXiuLecRydo/dFVqmMGmAIF4rApoun+DSlx4xr\ntIyZBhB7OgUI4H7U9uGH0aO6Jlb27TkBOe/5/fyt8IaVk7o5mJiVYWDVnpmR\nnJ7uu/LE2CW23d4dA7jTKsaLLHOZths26eKCeqHijnlaCci5/u+Zj1HqXs8d\n5KYu\r\n=S4Ap\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "cfebf9527440c75ff45bf3ad99afa29a3fdff0e8", "scripts": {"lint": "eslint --cache lib test/*.js", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.1.2_1627639898730_0.46096201712670126", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "webpack-sources", "version": "3.2.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.2.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "b16973bcf844ebcdb3afde32eda1c04d0b90f89d", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.0.tgz", "fileCount": 25, "integrity": "sha512-fahN08Et7P9trej8xz/Z7eRu8ltyiygEo/hnRi9KqBUs80KeDcnf96ZJo++ewWd84fEf3xSX9bp4ZS9hbw0OBw==", "signatures": [{"sig": "MEUCIC6cbuAHuNQhE7A6sCFAYVCj9Rd5Sn006FeFZMAzPtdQAiEAonnyrqKwu3ul1+M0CwliCEKH+out9jluCsT+Q1Ih75g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhB8GoCRA9TVsSAnZWagAAxWMP/3mylGzbXbrzH6K4AWE/\nbZ8xxUTtBdUOivfbvaG6zpQ2ExWz6bxeQ3F+rdUQzrF4Ol2H2qGPeRzR40Hi\n+gCuMHC5UdV+SdGgLM1LaygVai9qi27NlUgfzNMteZP/z8KkEm0fimiN3u/K\nr+9b73z025xJ3j6Y1LuBgJE18JD0Nl1jHPiohETjaLP81T0ijeQBe2+yeGFe\nSY6t77dzpJR1yj8kPJDExPV7Hi71tjsoxmLXWiZYq09HQsTXHGgOv56v5ldk\nz0/hkEznbe59Ar83/sCnLafNxQdZNTw1zXUbvZiFJ3utA7xo8LytnuzSISxv\n5GUWkR6ScV29Gg9O/ebF+2HSblx/w36gyv5L4MDTuiAD4JnQxzw0LyQ7wCR+\nbclC2CrMOHwDswhEFqQm0e+vkpawdSnIU4tX1620nvF//tOTne27c0hzzVTV\nF0gaO/YT28ZaN/m79N0/W27ZCwLD2dpCohhfggAbtYuHddX+mgWszuj4qJde\nX+2KInovfvgx4Ufi+4eTZgtNOaqvKCJBMK20O44OVQMMYKwgXGbpKHsbcrQx\n42a0aqHTOy6scX/6gqxrRnhneRFBtXGOrRfEDCV2TkPpjmtT6fVuGQJ2SioT\nFBGE68BXvpGcZs4BkY2oORjH0iK1/438cTChP+dM4n6r81M3yLuQT6pE7ecS\ndGu/\r\n=L1w8\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "c15d9516fb11a60d2a0d5e3a21cd789f07568a3b", "scripts": {"lint": "eslint --cache lib test/*.js", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.2.0_1627898280546_0.1906411403048267", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "webpack-sources", "version": "3.2.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.2.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "251a7d9720d75ada1469ca07dbb62f3641a05b6d", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.1.tgz", "fileCount": 26, "integrity": "sha512-t6BMVLQ0AkjBOoRTZgqrWm7xbXMBzD+XDq2EZ96+vMfn3qKgsvdXZhbPZ4ElUOpdv4u+iiGe+w3+J75iy/bYGA==", "signatures": [{"sig": "MEQCIEl/x9wd7ELjpe+fSdQ5mVN1gud8vZMcdDtgrPIrw4vpAiBXmQBoPeBXYumgvFPiEphfuTt4Z9b2ls6ulYqXWYgpgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP2w4CRA9TVsSAnZWagAAXHIP/jJ7dqWAFdYNIo11xhMi\nkzLRiWuBUCKwhybeHSrePh/otHjgdVZ7cxXziJdxD7nz+psmlpdAHKF5U6Ih\nhAz1Ak5iilvWTSP54k7ZH9wEdlnkXOXvDDbYJHDnJ3NEUXIxD/PHdUOxjFfQ\nRyK3MAU4QRVQ0BK/JD+nQBIEM9/BEhvqHmmHg4Bo8yXTb+/X9YcRjAzd/Mqy\nfQpzHuI3vTGzYjaaczHHhHwC1DVNSOaOjHYipztb847cTpvsgEvJJdjXWuAA\nptdfHZRRL/UocPwmJTr2KXc6ImJMCx4HD8Ooy0btcgdO98nEL4nDxsp7Cn3n\nrrwsz2+aaIA486rgZvcIneLy0cQvdJapqQOwofS124aUYTqZIuzsKNvYxYV/\nkk4a+dw7EROA9kC9YFQnTQ058QcYyDlD0ZS1jGqdo2Oot6Tt/2b8b0wpzrst\n9jMcF22//7cNnzzSJTF13ALfaljCtsonmS8vbDhPhYRvOj3uyOWHjFPFXA7u\nemaeH58P3DiKbjZGLUCV2ZdpM5h435rmFp3VUAh7ar71neKiYpghYCaXHt5V\n4ByXHlg1NCAyDHrUtLkjQhfuByImWRrDE/WZ6FGAPFvPIKISJit1h3x6n6bh\nkHi3btnyTxH4sRZ25NGM+R4oUM+eo2YowUDBsNhRO7JF7zZ9VGbyH8tD3a8u\n3ggp\r\n=jjCr\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "dccca06b7d28d81f9c3b17cd471d1f03ccdf805a", "scripts": {"lint": "eslint --cache lib test/*.js", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.22.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.2.1_1631546424562_0.15652861680874386", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "webpack-sources", "version": "3.2.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.2.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "d88e3741833efec57c4c789b6010db9977545260", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.2.tgz", "fileCount": 27, "integrity": "sha512-cp5qdmHnu5T8wRg2G3vZZHoJPN14aqQ89SyQ11NpGH5zEMDCclt49rzo+MaRazk7/UeILhAI+/sEtcM+7Fr0nw==", "signatures": [{"sig": "MEQCIARkvwvlgHywg21llty5bCBm8zS+WCF3VIDd/ZXlof1CAiBmTNXnfgCnLDSVfAXWAOMUEJPnqKFrVX62hEY+rTt6yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91384, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BGrCRA9TVsSAnZWagAAoc0P/1mwaya38ziqx+JZGGD4\n6J1fMuKg4wCwXJ57KbmmEsC/9C/I7fNVqKvJB89cc2nJ4ThSdtNeRN7+6lCf\nq6lWFWsu/WlKKC/sDWRGy/gqfzIZCsyasdM+ep5GGhvlVC6/g8kKw5z6axCW\nbrcippAyHqDklAQDeZILtXjfxFPvHpHLTqD5aW+AZxywSyUHmI+R388dXX/s\nZqfa3Uj+k69nSw9SZ7eJXKs1fpFx4qbbcyauPJ7W05Zcno/6+WMsahS4rsij\nwA24xVk4NO4vcoRJgIZ+ACJmF+8ZiRxek3xXvpPYzpR32uj7ZLcpsL30I6iI\n2M2Pud4hKzNN8BqgPLodCL7f0hnbGDV725QAySipkAGjJNyXcAMDhA8wyYT8\nuz8tSlXLWPiYqwzII/+X4sEu+cgu5G2casDXXxzN7jcb68vHphznXO8U6ZHr\nrkamZI2YxOKPwF3vDGY5T0jtgfQK+Y9578KPsqzPusU6twV+Zj0w24C5GCDe\nhfyHVYflkYIvst0UDJ89HDSwRBhvacXprIGa1FmkF2CpIobfbkInD9XJNfLm\nN5RrtV6P4HGnt76Owf4N261XGSjMLK2VKcVFx1y3Ji3AnVNxrewMhK5nvYXv\n3q99CUkTpDKLMlbGCjvgXL+0d79hexSYIhCoYNB4k7ILJPkLIXPAsjUPAA4e\nn7M7\r\n=Yopz\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "56018cadc5dde19ed82169b20957732de85a6eef", "scripts": {"lint": "eslint --cache lib test/*.js", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.22.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.2.2_1636982022738_0.6220332525646282", "host": "s3://npm-registry-packages"}}, "3.2.3": {"name": "webpack-sources", "version": "3.2.3", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.2.3", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "2d4daab8451fd4b240cc27055ff6a0c2ccea0cde", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz", "fileCount": 27, "integrity": "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==", "signatures": [{"sig": "MEUCICtELje5tme2Sg5pZgOIR28kXvh4kvZGyRtaj7+ISqvRAiEAojZg3q2HVAEMb+irEQAxay61aOoRMGv/IFCazlIIWKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3bR3CRA9TVsSAnZWagAAtckP/0yM8j11TRznurw73j2U\ny5DttuNu4qLbVuyuuDTDmSsHvrXOH03ilnUrnRNqG79J3PE2z9WfO9kMjbFn\n8ZYdKfm4KWpinGodyL9EdeaJGDcZKKzK0F+3rlfRqd+c++ckWh5K8EVVPigq\nQqHaAvloykukpaYK5Tifl2CzYQqWloZ8IjK1CaF8TKNhcSIhf85aJx/evwyx\nDEaAn0VXyZ7D/phWe/Fgb4cCVPQqZDNRDYDcvl0d7yHd+9v+u2NIV0xdQd2d\nfD73RwvAJqmlCXqiIrIFTNaxL4SO3/fmzGVFibDcnbss2g0la1IAC+7Q6j59\nAQIPi0DwI/82QYS6xQqRcSxXxlvCdenCLGDnSMu8bOR7uo5HO/yBD/BA75Q5\nkkHyAM6xiCR64TLToMvu4ICb1mC2BGe3z3PDw67X+DMXVtZuE4+44KmWhUxt\nMogi5L9HGP3jIWWe32pKfKD4oPhjt+2qbwXfnNHpCj1xK3pzDaDCQ3rprZzH\nIb79idgXcKxKbY/LNEl/XGJ/HzWT5aWxRJLkLdh5lAFRozasARSDfxvGX7Uq\nSLK6KeljV7pz7rOqpeSQ6w8z/YVy+CS7dM3fSP6PNHVPgFS/jH+4mTOU3eiE\nNI2E12ZJSGYv0Z5OboKvU2zINAvbD740djbWmHE17NVLFZi8nksjQG2pjiHf\nbSMb\r\n=lvBR\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "./lib/index.js", "engines": {"node": ">=10.13.0"}, "gitHead": "9f98066311d53a153fdc7c633422a1d086528027", "scripts": {"lint": "eslint --cache lib test/*.js", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint"}, "_npmUser": {"name": "sokra", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "7.22.0", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^26.4.0", "eslint": "^7.7.0", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-mocha": "^8.0.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.2.3_1641919607417_0.38279536501534106", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "webpack-sources", "version": "3.3.0", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.3.0", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "8d3449f1ed3f254e722a529a0a344a37d2d17048", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.0.tgz", "fileCount": 29, "integrity": "sha512-77R0RDmJfj9dyv5p3bM5pOHa+X8/ZkO9c7kpDstigkC4nIDobadsfSGCwB4bKhMVxqAok8tajaoR8rirM7+VFQ==", "signatures": [{"sig": "MEUCIH0yowA3leTdePkVb+LpY2+XUU3tAlEkLNqzRr73XFc2AiEA4HgqIxtw6FmW0pzlwd3Aj6nfSxfJUanZGpmr9RpWPsc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 136590}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "lib/index.js", "types": "types.d.ts", "engines": {"node": ">=10.13.0"}, "gitHead": "016358f8ec92a8ee055325e133a71e39a6c63f6c", "scripts": {"fix": "yarn fix:code && yarn fix:special", "fmt": "yarn fmt:base --loglevel warn --write", "lint": "yarn lint:code && yarn lint:types && yarn lint:types-test && yarn lint:special", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint", "fix:code": "yarn lint:code --fix", "fmt:base": "prettier --cache --ignore-unknown .", "fmt:check": "yarn fmt:base --check", "lint:code": "eslint --cache lib/**/*.js test/*.js", "lint:types": "tsc", "fix:special": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/generate-types --write", "lint:special": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-file-header && node node_modules/tooling/generate-types", "lint:types-test": "tsc -p tsconfig.types.test.json"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "22.13.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "eslint": "^7.7.0", "tooling": "github:webpack/tooling#v1.23.9", "webpack": "^5.99.9", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "typescript": "^5.3.3", "@types/jest": "^27.5.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.3.0_1748024648539_0.22221428322802494", "host": "s3://npm-registry-packages-npm-production"}}, "3.3.1": {"name": "webpack-sources", "version": "3.3.1", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.3.1", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "ad67407c0bcc9a2bd963828ee324da6130945cc2", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.1.tgz", "fileCount": 29, "integrity": "sha512-EWzBqw2ZH/hIXIWIdOTvFHij6MuYdDHZVL12bZb921CrmP9UqYhK9+a3OC/onMGeBYrt2aOivHCLy5E+x5wYOA==", "signatures": [{"sig": "MEUCIQCgKv6gZZnLuBEH7x1x5PgWl3ZgDHhbgnqOWAgxaTvGwgIgT5m+4bsh75G18PrwfTmFD171C0SEAHFtFkdXdl4AQkE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 136102}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "lib/index.js", "types": "types.d.ts", "engines": {"node": ">=10.13.0"}, "gitHead": "60b8628971266198eb4dae5ce0baf0941cfd012a", "scripts": {"fix": "yarn fix:code && yarn fix:special", "fmt": "yarn fmt:base --loglevel warn --write", "lint": "yarn lint:code && yarn lint:types && yarn lint:types-test && yarn lint:special", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint", "fix:code": "yarn lint:code --fix", "fmt:base": "prettier --cache --ignore-unknown .", "fmt:check": "yarn fmt:base --check", "lint:code": "eslint --cache lib/**/*.js test/*.js", "lint:types": "tsc", "fix:special": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/generate-types --write", "lint:special": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-file-header && node node_modules/tooling/generate-types", "lint:types-test": "tsc -p tsconfig.types.test.json"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "22.13.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "eslint": "^7.7.0", "tooling": "github:webpack/tooling#v1.23.9", "webpack": "^5.99.9", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "typescript": "^5.3.3", "@types/jest": "^27.5.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.3.1_1748876285054_0.22615596513387004", "host": "s3://npm-registry-packages-npm-production"}}, "3.3.2": {"name": "webpack-sources", "version": "3.3.2", "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "_id": "webpack-sources@3.3.2", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/webpack/webpack-sources#readme", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "dist": {"shasum": "0ab55ab0b380ce53c45ca40cb7b33bab3149ea85", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.2.tgz", "fileCount": 29, "integrity": "sha512-ykKKus8lqlgXX/1WjudpIEjqsafjOTcOJqxnAbMLAu/KCsDCJ6GBtvscewvTkrn24HsnvFwrSCbenFrhtcCsAA==", "signatures": [{"sig": "MEYCIQCvv8XXU92fbQ+zYo2UL9Ahzsq8P2zSoioDHf5CyMLqaAIhAOj3Y8S8h7It4un55Aa5Fr468GMDQC4+oQoWUww6r26z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 136258}, "jest": {"forceExit": true, "testMatch": ["<rootDir>/test/*.js"], "testEnvironment": "node", "transformIgnorePatterns": ["<rootDir>"]}, "main": "lib/index.js", "types": "types.d.ts", "engines": {"node": ">=10.13.0"}, "gitHead": "9d63cbcdc1e5878d01876aa85c0fbcc51983f23b", "scripts": {"fix": "yarn fix:code && yarn fix:special", "fmt": "yarn fmt:base --loglevel warn --write", "lint": "yarn lint:code && yarn lint:types && yarn lint:types-test && yarn lint:special", "test": "jest", "cover": "jest --coverage", "pretest": "yarn lint", "fix:code": "yarn lint:code --fix", "fmt:base": "prettier --cache --ignore-unknown .", "fmt:check": "yarn fmt:base --check", "lint:code": "eslint --cache lib/**/*.js test/*.js", "lint:types": "tsc", "fix:special": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/generate-types --write", "lint:special": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-file-header && node node_modules/tooling/generate-types", "lint:types-test": "tsc -p tsconfig.types.test.json"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/webpack/webpack-sources.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Source code handling classes for webpack", "directories": {}, "_nodeVersion": "22.13.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.5.1", "eslint": "^7.7.0", "tooling": "github:webpack/tooling#v1.23.9", "webpack": "^5.99.9", "istanbul": "^0.4.1", "prettier": "^2.0.5", "coveralls": "^3.0.2", "source-map": "^0.7.3", "typescript": "^5.3.3", "@types/jest": "^27.5.2", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-node": "^11.1.0", "sourcemap-validator": "^2.1.0", "eslint-plugin-nodeca": "^1.0.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/webpack-sources_3.3.2_1748881110715_0.4164354966518846", "host": "s3://npm-registry-packages-npm-production"}}, "3.3.3": {"name": "webpack-sources", "version": "3.3.3", "description": "Source code handling classes for webpack", "main": "lib/index.js", "types": "types.d.ts", "scripts": {"lint": "yarn lint:code && yarn lint:types && yarn lint:types-test && yarn lint:special", "lint:code": "eslint --cache .", "lint:special": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-file-header && node node_modules/tooling/generate-types", "lint:types": "tsc", "lint:types-test": "tsc -p tsconfig.types.test.json", "fmt": "yarn fmt:base --loglevel warn --write", "fmt:check": "yarn fmt:base --check", "fmt:base": "prettier --cache --ignore-unknown .", "fix": "yarn fix:code && yarn fix:special", "fix:code": "yarn lint:code --fix", "fix:special": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/generate-types --write", "pretest": "yarn lint", "test": "jest", "cover": "jest --coverage"}, "devDependencies": {"@eslint/js": "^9.28.0", "@eslint/markdown": "^6.5.0", "@stylistic/eslint-plugin": "^4.4.1", "@types/jest": "^27.5.2", "coveralls": "^3.0.2", "globals": "^16.2.0", "eslint": "^9.28.0", "eslint-config-webpack": "^4.0.8", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.12.0", "eslint-plugin-jsdoc": "^50.7.1", "eslint-plugin-n": "^17.19.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-unicorn": "^59.0.1", "istanbul": "^0.4.1", "jest": "^27.5.1", "prettier": "^3.5.3", "prettier-2": "npm:prettier@^2", "source-map": "^0.7.3", "sourcemap-validator": "^2.1.0", "tooling": "github:webpack/tooling#v1.23.10", "typescript": "^5.3.3", "webpack": "^5.99.9"}, "engines": {"node": ">=10.13.0"}, "repository": {"type": "git", "url": "git+https://github.com/webpack/webpack-sources.git"}, "keywords": ["webpack", "source-map"], "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "homepage": "https://github.com/webpack/webpack-sources#readme", "_id": "webpack-sources@3.3.3", "gitHead": "1b98c0f26810925d66d56088a07dc9a1a43e63cd", "_nodeVersion": "22.13.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==", "shasum": "d4bf7f9909675d7a070ff14d0ef2a4f3c982c723", "tarball": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.3.tgz", "fileCount": 29, "unpackedSize": 137704, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIDEkChpC9k4yDHbgZ1jqD9uI2y/USwonwu4mMSlRABB1AiEAmzpKZCM/Ei/qSnmi62WXMSjWBCMjN5o1k8EpgkHsmgM="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/webpack-sources_3.3.3_1750433221311_0.5717062945807976"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-01-03T17:29:30.353Z", "modified": "2025-06-20T15:27:01.685Z", "0.1.0": "2016-01-03T17:29:30.353Z", "0.1.1": "2016-01-30T12:10:01.566Z", "0.1.2": "2016-04-21T22:39:19.360Z", "0.1.3": "2016-11-12T16:03:16.077Z", "0.1.4": "2017-01-15T15:34:58.888Z", "0.1.5": "2017-03-07T20:11:47.369Z", "0.2.0": "2017-03-07T20:32:45.800Z", "0.2.1": "2017-03-24T21:05:28.371Z", "0.2.2": "2017-03-24T22:05:15.254Z", "0.2.3": "2017-03-24T23:38:14.635Z", "1.0.0": "2017-06-03T12:23:15.625Z", "1.0.1": "2017-06-03T14:32:04.346Z", "1.0.2": "2017-11-04T07:20:58.402Z", "1.1.0": "2017-11-26T11:06:26.949Z", "1.2.0": "2018-08-29T12:09:23.738Z", "1.3.0": "2018-09-20T09:05:06.955Z", "2.0.0-beta.0": "2019-01-18T19:43:35.990Z", "2.0.0-beta.1": "2019-01-19T11:41:21.817Z", "1.4.0": "2019-07-31T09:47:46.815Z", "1.4.1": "2019-07-31T13:19:42.048Z", "1.4.2": "2019-08-05T11:37:57.718Z", "2.0.0-beta.2": "2019-08-05T11:48:11.786Z", "1.4.3": "2019-08-05T14:56:52.060Z", "2.0.0-beta.3": "2019-08-05T15:01:45.305Z", "2.0.0-beta.4": "2019-09-12T15:40:19.941Z", "2.0.0-beta.5": "2019-10-22T12:34:31.973Z", "2.0.0-beta.6": "2019-11-08T09:49:39.502Z", "2.0.0-beta.7": "2019-11-13T12:58:11.853Z", "2.0.0-beta.8": "2019-11-20T15:36:10.259Z", "2.0.0-beta.9": "2020-08-20T07:44:06.943Z", "2.0.0-beta.10": "2020-08-24T15:52:25.486Z", "2.0.0": "2020-09-17T23:33:31.570Z", "2.0.1": "2020-09-28T15:50:11.724Z", "2.1.0": "2020-10-26T08:05:55.075Z", "2.1.1": "2020-10-28T12:39:41.613Z", "2.2.0": "2020-10-29T10:21:40.646Z", "2.3.0": "2021-05-27T10:01:50.301Z", "2.3.1": "2021-07-20T09:49:14.115Z", "3.0.0": "2021-07-26T23:46:14.252Z", "3.0.1": "2021-07-27T10:59:19.017Z", "3.0.2": "2021-07-28T07:21:08.535Z", "3.0.3": "2021-07-28T14:25:47.176Z", "3.0.4": "2021-07-29T09:21:26.089Z", "3.1.0": "2021-07-29T12:14:24.971Z", "3.1.1": "2021-07-29T12:25:45.747Z", "3.1.2": "2021-07-30T10:11:38.853Z", "3.2.0": "2021-08-02T09:58:00.714Z", "3.2.1": "2021-09-13T15:20:24.748Z", "3.2.2": "2021-11-15T13:13:42.918Z", "3.2.3": "2022-01-11T16:46:47.603Z", "3.3.0": "2025-05-23T18:24:08.785Z", "3.3.1": "2025-06-02T14:58:05.223Z", "3.3.2": "2025-06-02T16:18:30.889Z", "3.3.3": "2025-06-20T15:27:01.499Z"}, "bugs": {"url": "https://github.com/webpack/webpack-sources/issues"}, "author": {"name": "<PERSON> @sokra"}, "license": "MIT", "homepage": "https://github.com/webpack/webpack-sources#readme", "keywords": ["webpack", "source-map"], "repository": {"type": "git", "url": "git+https://github.com/webpack/webpack-sources.git"}, "description": "Source code handling classes for webpack", "maintainers": [{"name": "sokra", "email": "<EMAIL>"}, {"name": "jhnns", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# webpack-sources\n\nContains multiple classes which represent a `Source`. A `Source` can be asked for source code, size, source map and hash.\n\n## `Source`\n\nBase class for all sources.\n\n### Public methods\n\nAll methods should be considered as expensive as they may need to do computations.\n\n#### `source`\n\n```typescript\nSource.prototype.source() -> String | Buffer\n```\n\nReturns the represented source code as string or Buffer (for binary Sources).\n\n#### `buffer`\n\n```typescript\nSource.prototype.buffer() -> <PERSON><PERSON><PERSON>\n```\n\nReturns the represented source code as <PERSON><PERSON><PERSON>. Strings are converted to utf-8.\n\n#### `size`\n\n```typescript\nSource.prototype.size() -> Number\n```\n\nReturns the size in bytes of the represented source code.\n\n#### `map`\n\n```typescript\nSource.prototype.map(options?: Object) -> Object | null\n```\n\nReturns the SourceMap of the represented source code as JSON. May return `null` if no SourceMap is available.\n\nThe `options` object can contain the following keys:\n\n- `columns: Boolean` (default `true`): If set to false the implementation may omit mappings for columns.\n\n#### `sourceAndMap`\n\n```typescript\nSource.prototype.sourceAndMap(options?: Object) -> {\n\tsource: String | Buffer,\n\tmap: Object | null\n}\n```\n\nReturns both, source code (like `Source.prototype.source()` and SourceMap (like `Source.prototype.map()`). This method could have better performance than calling `source()` and `map()` separately.\n\nSee `map()` for `options`.\n\n#### `updateHash`\n\n```typescript\nSource.prototype.updateHash(hash: Hash) -> void\n```\n\nUpdates the provided `Hash` object with the content of the represented source code. (`Hash` is an object with an `update` method, which is called with string values)\n\n## `RawSource`\n\nRepresents source code without SourceMap.\n\n```typescript\nnew RawSource(sourceCode: String | Buffer)\n```\n\n## `OriginalSource`\n\nRepresents source code, which is a copy of the original file.\n\n```typescript\nnew OriginalSource(\n\tsourceCode: String | Buffer,\n\tname: String\n)\n```\n\n- `sourceCode`: The source code.\n- `name`: The filename of the original source code.\n\nOriginalSource tries to create column mappings if requested, by splitting the source code at typical statement borders (`;`, `{`, `}`).\n\n## `SourceMapSource`\n\nRepresents source code with SourceMap, optionally having an additional SourceMap for the original source.\n\n```typescript\nnew SourceMapSource(\n\tsourceCode: String | Buffer,\n\tname: String,\n\tsourceMap: Object | String | Buffer,\n\toriginalSource?: String | Buffer,\n\tinnerSourceMap?: Object | String | Buffer,\n\tremoveOriginalSource?: boolean\n)\n```\n\n- `sourceCode`: The source code.\n- `name`: The filename of the original source code.\n- `sourceMap`: The SourceMap for the source code.\n- `originalSource`: The source code of the original file. Can be omitted if the `sourceMap` already contains the original source code.\n- `innerSourceMap`: The SourceMap for the `originalSource`/`name`.\n- `removeOriginalSource`: Removes the source code for `name` from the final map, keeping only the deeper mappings for that file.\n\nThe `SourceMapSource` supports \"identity\" mappings for the `innerSourceMap`.\nWhen original source matches generated source for a mapping it's assumed to be mapped char by char allowing to keep finer mappings from `sourceMap`.\n\n## `CachedSource`\n\nDecorates a `Source` and caches returned results of `map`, `source`, `buffer`, `size` and `sourceAndMap` in memory. `updateHash` is not cached.\nIt tries to reused cached results from other methods to avoid calculations, i. e. when `source` is already cached, calling `size` will get the size from the cached source, calling `sourceAndMap` will only call `map` on the wrapped Source.\n\n```typescript\nnew CachedSource(source: Source)\nnew CachedSource(source: Source | () => Source, cachedData?: CachedData)\n```\n\nInstead of passing a `Source` object directly one can pass an function that returns a `Source` object. The function is only called when needed and once.\n\n### Public methods\n\n#### `getCachedData()`\n\nReturns the cached data for passing to the constructor. All cached entries are converted to Buffers and strings are avoided.\n\n#### `original()`\n\nReturns the original `Source` object.\n\n#### `originalLazy()`\n\nReturns the original `Source` object or a function returning these.\n\n## `PrefixSource`\n\nPrefix every line of the decorated `Source` with a provided string.\n\n```typescript\nnew PrefixSource(\n\tprefix: String,\n\tsource: Source | String | Buffer\n)\n```\n\n## `ConcatSource`\n\nConcatenate multiple `Source`s or strings to a single source.\n\n```typescript\nnew ConcatSource(\n\t...items?: Source | String\n)\n```\n\n### Public methods\n\n#### `add`\n\n```typescript\nConcatSource.prototype.add(item: Source | String)\n```\n\nAdds an item to the source.\n\n## `ReplaceSource`\n\nDecorates a `Source` with replacements and insertions of source code.\n\nThe `ReplaceSource` supports \"identity\" mappings for child source.\nWhen original source matches generated source for a mapping it's assumed to be mapped char by char allowing to split mappings at replacements/insertions.\n\n### Public methods\n\n#### `replace`\n\n```typescript\nReplaceSource.prototype.replace(\n\tstart: Number,\n\tend: Number,\n\treplacement: String\n)\n```\n\nReplaces chars from `start` (0-indexed, inclusive) to `end` (0-indexed, inclusive) with `replacement`.\n\nLocations represents locations in the original source and are not influenced by other replacements or insertions.\n\n#### `insert`\n\n```typescript\nReplaceSource.prototype.insert(\n\tpos: Number,\n\tinsertion: String\n)\n```\n\nInserts the `insertion` before char `pos` (0-indexed).\n\nLocation represents location in the original source and is not influenced by other replacements or insertions.\n\n#### `original`\n\nGet decorated `Source`.\n\n## `CompatSource`\n\nConverts a Source-like object into a real Source object.\n\n### Public methods\n\n#### static `from`\n\n```typescript\nCompatSource.from(sourceLike: any | Source)\n```\n\nIf `sourceLike` is a real Source it returns it unmodified. Otherwise it returns it wrapped in a CompatSource.\n", "readmeFilename": "README.md", "users": {"ttionya": true, "dennisli87": true, "shuoshubao": true, "flumpus-dev": true}}