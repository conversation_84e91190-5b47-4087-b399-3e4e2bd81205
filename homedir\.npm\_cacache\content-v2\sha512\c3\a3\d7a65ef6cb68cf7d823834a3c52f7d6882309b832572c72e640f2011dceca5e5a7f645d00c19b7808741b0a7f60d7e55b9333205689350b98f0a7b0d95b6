{"_id": "backoff", "_rev": "68-83a0d0227794a8acf9d91150a174fe19", "name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "dist-tags": {"latest": "2.5.0"}, "versions": {"0.0.1": {"name": "backoff", "description": "Exponential backoff implementation.", "version": "0.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "exponential"], "repository": {"type": "git", "url": "git://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.3", "nodeunit": "0.7"}, "scripts": {"test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "main": "backoff.js", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "backoff@0.0.1", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.4", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"shasum": "a2be0d56d658e9297090c4095763c3c6138aff08", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.0.1.tgz", "integrity": "sha512-Zkh2gOWpwBemt9xp8fQwgXCHtCvab3HiEMvBOx8M7N9xzNZOZbEg7qU9s11BN4CUsA1jDzaguZHs8XOS6Q4kvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID/djtmltEaHhs/+CquY/O40NEYfI3hrcx4XRCkcVDSrAiEA4Yz5apELxC3HbYQMifsc6MmKqDPPWWnv0fLV8DAZLFw="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "backoff", "description": "Exponential backoff implementation.", "version": "0.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "exponential"], "repository": {"type": "git", "url": "git://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.3", "nodeunit": "0.7"}, "scripts": {"test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "main": "backoff.js", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "backoff@0.0.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.0-beta-4", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "08b4cb64931b0b069f3d5ca8f8e8504fb0058239", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.0.2.tgz", "integrity": "sha512-6BOgNYqmp+O818e1/o23cZf0LCNHFa+6R48j+gZAFm1HycGZptgT1dtXuQEy7cR4b7HWKt/4iblIock+eAdRXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCORq01Xqoy8tz2pU+55Yoj9NQuhYm6qpmWGPYNiGCZwIgLhU31zhoUPLJ/8LbAnn/1htqqmIKTbciHBcR/eRvu1Y="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "backoff", "description": "Exponential backoff implementation.", "version": "0.0.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "exponential"], "repository": {"type": "git", "url": "git://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.3", "nodeunit": "0.7", "jshint": "0.7"}, "scripts": {"pretest": "node_modules/jshint/bin/hint *.js tests/*.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "main": "backoff.js", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "backoff@0.0.3", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.17", "_defaultsLoaded": true, "dist": {"shasum": "8f66d0d5d4138a4a10b6db80e10d1265f93b97b6", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.0.3.tgz", "integrity": "sha512-C6EmEbQ3e2LbLdIgu3VXM35sm74mMqhIQxiNpu9e5ChGmI6edkTE4zYXIKHda1g+VtHQv1oB5Y04MIuv8CPMig==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAirYjQUM7519NSe4E985q/Ti9gokc+RWsRg4q8RAyKnAiEAn6l73qOIWpypqaFDfmEO4KMp07qj3U87zsd+bYM8cIo="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "backoff", "description": "Fibonnaci backoff implementation.", "version": "0.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.7"}, "scripts": {"pretest": "node_modules/jshint/bin/hint *.js tests/*.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "main": "backoff.js", "_id": "backoff@0.1.0", "dist": {"shasum": "84a21e7c1c305743705a49981aec6b0f0d8bd8f2", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.1.0.tgz", "integrity": "sha512-WORsHMgoLjV3e6L0CfBxMoJV+LSBAeYoF0Eissc178SUi4njB98KhqvHacTNXEL9sqbc9RdNUpp7wWBUpJOH2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqqopmif9+q3wx7dkmh+jdGNhZX5nJkuv8mWdsOiqQxAiEAiz7kbg0+KDJnkOyJ3Lgza0rp4Z86OGLF2PXnzrQslBs="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "backoff", "description": "Exponential and Fibonnaci backoffs.", "version": "0.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.7"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/*.js lib/strategy/*.js tests/*.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@0.2.0", "dist": {"shasum": "1abbe9393a28dd35d55c5403ec132472136b21ce", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.2.0.tgz", "integrity": "sha512-fqP1tsMAUW+TaAWeJ0zAKqSfBjEsF2yp9ZhNU+KSwe/ufg6a0Ku++zQLKHK2LG6GgkNnASrO9KNCCtJDtjToAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID2M10Qgh3N8bT5nrd9Hc7iVaT3J2Ts5j/l0N0OcgFe8AiB3lXNFsMN321TyZ0j0FOlrz0suQtS6MFz380wlvBMmrw=="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "backoff", "description": "Exponential and <PERSON><PERSON><PERSON><PERSON> backoffs.", "version": "0.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.7"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/*.js lib/strategy/*.js tests/*.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@0.2.1", "dist": {"shasum": "affea29debd29fe48680019b9967e84c636e799d", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.2.1.tgz", "integrity": "sha512-or7AWUk5Byg173IbJN+6fg+i4OIOkS7PmsaTDj+71ItvQzQ69MRRjMnw6jCvt50uB9VopQNw3g4518F7Ax1zWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBf6rd9jR2vLQIqvivb9GEnm8qXa1XnS/3gLcGDWplIUAiEA2M6HNl170ka5H92qOfL6cD9Hi49wRfVzU44uj30AxTM="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "0.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.7"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/*.js lib/strategy/*.js tests/*.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@0.3.0", "dist": {"shasum": "d63cbab90b0475d3ccf2fac929e11af963a96608", "tarball": "https://registry.npmjs.org/backoff/-/backoff-0.3.0.tgz", "integrity": "sha512-ShBh+dE87kMRUBVlDDOnFncVRzECWScaUngtcnwhMpNF6CXQ4WjyKpdMuhUyfPtDt96HS2h0IAMOGSQlSSAiOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbM4Ih4KTe+pA0UjCScFG3MjHwU8rq5nZFkt7MRNUDgAiBhlqNlqEWxQBhsUmdcJdtnLlHuUdekK9WjOmyYriUIIA=="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "1.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.7"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/*.js lib/strategy/*.js tests/*.js examples/*.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@1.0.0", "dist": {"shasum": "00982f78158ec6d03f99693ec0fb5f7b2ad1e28e", "tarball": "https://registry.npmjs.org/backoff/-/backoff-1.0.0.tgz", "integrity": "sha512-MVzvCWJB8O/9+ZwucWUAuK0hg0u1Moqwq9wULv1ax/jBdThZ5E9B9TaC0Ag5Oi6wsf/y7RYsYwDEQh1zi6vpxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICwIVTQSsAlOg987E4Oo0TWZtBUCpKUnj/ZWRrtOsPnnAiEAytNLJUvQSFepGKHSWJryr/L6MqXM+oVvwMNd9dtu+bM="}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "1.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.9"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/ lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@1.1.0", "dist": {"shasum": "7160ba2416caab0b9ce4f36981283a0ac25a44fe", "tarball": "https://registry.npmjs.org/backoff/-/backoff-1.1.0.tgz", "integrity": "sha512-FmdXNo9F29dFFc59sndIk01axHXX1EDYNwzVFQU1EF2JvMA31NDPkGHtHhoq71AqqA5bAN3UdsAZ97YuWmB+AA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH1vF3DvunM0AV/ZZ6sSTEMQ/1IWaueAPsPYMm2TEyllAiAMWcteC7TsGLNVOt/QmMneR8U7Noqt/PpqaSxTLFgIHA=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "1.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.9.0"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/ lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@1.2.0", "dist": {"shasum": "9e4935da1f42cb9d7c44184765c127f4602370d9", "tarball": "https://registry.npmjs.org/backoff/-/backoff-1.2.0.tgz", "integrity": "sha512-oB12/EcgvSa3ihH5kxgTFkDQOK0v4xvOQ9jvoPYRuBcfdwvKyXjLa9pe5NA1tsge683FvvQ7QlJ+MW/+iz53nQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrVM5ryFbSAZLnFizySaWsp9oaQJlW+XQd+bSIzxC7VAIgJw7MpJFldBlPlhjhx1PfJLr84n2vor85mXU2iJGO5ZA="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "1.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.4", "nodeunit": "0.7", "jshint": "0.9.0"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/ lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@1.2.1", "dist": {"shasum": "06baefd31bef5624bd4223cb7beb73f92a8e1ba2", "tarball": "https://registry.npmjs.org/backoff/-/backoff-1.2.1.tgz", "integrity": "sha512-j9Boz7j8ffyPUNeTxtEk37kLCt2EEj+9yVTbndfYYmXjTg/0eO3CQBRG5CMEpNSiQdf2mM0V0txC9PZakpntGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBGJDLaA0AnBf61E4nNDbYeMYk3uEgiD1brgQIJfuVBzAiEAo3BkHDz1qOazG2zVRQVKxmmXnATqSnXrHIMpeSHWBlo="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.5.2", "nodeunit": "0.7", "jshint": "0.9.0"}, "scripts": {"pretest": "node_modules/jshint/bin/hint lib/ lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@2.0.0", "dist": {"shasum": "b4394776f91c28a361049071dd891c00394fd805", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.0.0.tgz", "integrity": "sha512-hbNNsHWZybFpDYEMO33IBI7wbb/Oz5Zpgq8UWZ+J8ShEGA16/T97SgqgAOb2pKpnwq2ZFhOpDpJuEFt+wnY4SQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHezwHwScudzZMPXoWc/1DdwWtSAP7zKXQfAXnegkhQWAiAMU15dTXVHIIOajsdud5I/tyI5MIRsCJMD6OWG4E2MTQ=="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.5.2", "nodeunit": "0.7", "jshint": "1.1.0"}, "scripts": {"pretest": "node_modules/jshint/bin/jshint lib/ lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@2.1.0", "dist": {"shasum": "19b4e9f9fb75c122ad7bb1c6c376d6085d43ea09", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.1.0.tgz", "integrity": "sha512-wVnHxy+UC8jtV9FLF/W9Jk//YL4cOhWAdfK2s+4KCFp/0e9xmVtXorqgR74JI08H7viquc4rZU6tOsBYBFNvyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxAoLeWUJzOU6RMebj1bUyQQ26x4qykxBCawttZWni4AIhAOm+qp8aZLL9/PjRfXUBwhkC+0tYsEJRi76xKkeX+NcF"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.2.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.5.2", "nodeunit": "0.7", "jshint": "1.1.0"}, "scripts": {"pretest": "node_modules/jshint/bin/jshint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@2.2.0", "dist": {"shasum": "bae0026ef445580b582bffa7201068bf101691cb", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.2.0.tgz", "integrity": "sha512-FWn3LRbqw0ZXZT8kto/qS+9Hm59u1pzy7pcCiPntc38SxWJu/cHSGjH+FRe2xeSvASMw0j8x4w12kZkNmfoAcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+YD338o94jJbsXBTPU1NpghQNJYYlBP+2Kwiqq38zxgIgCiyDA9/DFCFKTiMz1Bp8YvC19f+VS8dU1G66tVIbOmA="}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.3.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.3.0", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "devDependencies": {"sinon": "1.7", "nodeunit": "0.8", "jshint": "2.0"}, "scripts": {"pretest": "node_modules/jshint/bin/jshint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "_id": "backoff@2.3.0", "dist": {"shasum": "ee7c7e38093f92e472859db635e7652454fc21ea", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.3.0.tgz", "integrity": "sha512-ljr33cUQ/vyXE/60QuRO+WKGW4PzQ5OTWNXPWQwOTx5gh43q0pZocaVyXoU2gvFtasMIdIohdm9s01qoT6IJBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICrpjwaXxwNmjUXwscLOphJj92AdQJkN6CdOD+FBwK/LAiEA/orNhb05ia0wBggBhljEnP6IklEThu7oEswP3Q72US4="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.4.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.4.0", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "dependencies": {"precond": "0.2"}, "devDependencies": {"sinon": "1.10", "nodeunit": "0.9"}, "scripts": {"docco": "docco lib/*.js lib/strategy/* index.js", "pretest": "jshint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "file": ["index.js", "lib"], "bugs": {"url": "https://github.com/MathieuTurcotte/node-backoff/issues"}, "_id": "backoff@2.4.0", "dist": {"shasum": "c5a2888e784a61e18d5cba310ef0f521fb107d4f", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.4.0.tgz", "integrity": "sha512-fg8NzmOv3jUGkAUr6FLdt/bIAmgwguKIEbDaez2+9kTrIDEvm2HHydOCw5FT8bjFgDGD0m8gxo7r3Be23sR7LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG0Igsd/dR0TtpmvQloEa7G2HvueG+fBsV0lBJeN5WCPAiAgABPPR5KAUt5JT437CC0WyzNsDBWRVHYyYBmEL9Nu3w=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.4.1": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.4.1", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "https://github.com/MathieuTurcotte/node-backoff.git"}, "dependencies": {"precond": "0.2"}, "devDependencies": {"sinon": "1.10", "nodeunit": "0.9"}, "scripts": {"docco": "docco lib/*.js lib/strategy/* index.js", "pretest": "jshint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "lib", "tests"], "bugs": {"url": "https://github.com/MathieuTurcotte/node-backoff/issues"}, "_id": "backoff@2.4.1", "dist": {"shasum": "2f68c50e0dd789dbefe24200a62efb04d2456d68", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.4.1.tgz", "integrity": "sha512-gd7froKGnmDsq2IczAXNLMQO6GXuqU6UUGlbo/R6MlaTmqFUozc7Ny3f5vRbcRwAK//lc0/hpaOKO7AP8zAv/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBS/KFF3wbL8vwBD3M+j3hPhbZqiVSWfMJzg2UjMNcBBAiBH92W+jvRx9VJM1FuFjtRXQVR5SG0qQPHUqPINFpqSKg=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.5.0": {"name": "backoff", "description": "Fi<PERSON><PERSON>ci and exponential backoffs.", "version": "2.5.0", "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "repository": {"type": "git", "url": "git+https://github.com/MathieuTurcotte/node-backoff.git"}, "dependencies": {"precond": "0.2"}, "devDependencies": {"sinon": "1.10", "nodeunit": "0.9"}, "scripts": {"docco": "docco lib/*.js lib/strategy/* index.js", "pretest": "jshint lib/ tests/ examples/ index.js", "test": "node_modules/nodeunit/bin/nodeunit tests/"}, "engines": {"node": ">= 0.6"}, "files": ["index.js", "lib", "tests"], "gitHead": "811118fd1f89e9ca4e6b67292b9ef5da6c4f60e9", "bugs": {"url": "https://github.com/MathieuTurcotte/node-backoff/issues"}, "homepage": "https://github.com/MathieuTurcotte/node-backoff#readme", "_id": "backoff@2.5.0", "_shasum": "f616eda9d3e4b66b8ca7fca79f695722c5f8e26f", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f616eda9d3e4b66b8ca7fca79f695722c5f8e26f", "tarball": "https://registry.npmjs.org/backoff/-/backoff-2.5.0.tgz", "integrity": "sha512-wC5ihrnUXmR2douXmXLCe5O3zg3GKIyvRi/hi58a/XyRxVI+3/yM0PYueQOZXPXQ9pxBislYkw+sF9b7C/RuMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqB4hvO43HAPRJPWVP+S1O3drU3tWwTi+oM5erjZlt9wIhAN0tU36WcVf2Tjg7YuWgXN7xLrgjkjyzF91WQZ3zqkJA"}]}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/backoff-2.5.0.tgz_1457796071422_0.5366517049260437"}, "directories": {}}}, "readme": "# Backoff for Node.js\n[![Build Status](https://secure.travis-ci.org/MathieuTurcotte/node-backoff.png?branch=master)](http://travis-ci.org/MathieuTurcotte/node-backoff)\n[![NPM version](https://badge.fury.io/js/backoff.png)](http://badge.fury.io/js/backoff)\n\nFibonacci and exponential backoffs for Node.js.\n\n## Installation\n\n```\nnpm install backoff\n```\n\n## Unit tests\n\n```\nnpm test\n```\n\n## Usage\n\n### Object Oriented\n\nThe usual way to instantiate a new `Backoff` object is to use one predefined\nfactory method: `backoff.fibonacci([options])`, `backoff.exponential([options])`.\n\n`Backoff` inherits from `EventEmitter`. When a backoff starts, a `backoff`\nevent is emitted and, when a backoff ends, a `ready` event is emitted.\nHandlers for these two events are called with the current backoff number and\ndelay.\n\n``` js\nvar backoff = require('backoff');\n\nvar fibonacciBackoff = backoff.fibonacci({\n    randomisationFactor: 0,\n    initialDelay: 10,\n    maxDelay: 300\n});\n\nfibonacciBackoff.failAfter(10);\n\nfibonacciBackoff.on('backoff', function(number, delay) {\n    // Do something when backoff starts, e.g. show to the\n    // user the delay before next reconnection attempt.\n    console.log(number + ' ' + delay + 'ms');\n});\n\nfibonacciBackoff.on('ready', function(number, delay) {\n    // Do something when backoff ends, e.g. retry a failed\n    // operation (DNS lookup, API call, etc.). If it fails\n    // again then backoff, otherwise reset the backoff\n    // instance.\n    fibonacciBackoff.backoff();\n});\n\nfibonacciBackoff.on('fail', function() {\n    // Do something when the maximum number of backoffs is\n    // reached, e.g. ask the user to check its connection.\n    console.log('fail');\n});\n\nfibonacciBackoff.backoff();\n```\n\nThe previous example would print the following.\n\n```\n0 10ms\n1 10ms\n2 20ms\n3 30ms\n4 50ms\n5 80ms\n6 130ms\n7 210ms\n8 300ms\n9 300ms\nfail\n```\n\nNote that `Backoff` objects are meant to be instantiated once and reused\nseveral times by calling `reset` after a successful \"retry\".\n\n### Functional\n\nIt's also possible to avoid some boilerplate code when invoking an asynchronous\nfunction in a backoff loop by using `backoff.call(fn, [args, ...], callback)`.\n\nTypical usage looks like the following.\n\n``` js\nvar call = backoff.call(get, 'https://duplika.ca/', function(err, res) {\n    console.log('Num retries: ' + call.getNumRetries());\n\n    if (err) {\n        console.log('Error: ' + err.message);\n    } else {\n        console.log('Status: ' + res.statusCode);\n    }\n});\n\ncall.retryIf(function(err) { return err.status == 503; });\ncall.setStrategy(new backoff.ExponentialStrategy());\ncall.failAfter(10);\ncall.start();\n```\n\n## API\n\n### backoff.fibonacci([options])\n\nConstructs a Fibonacci backoff (10, 10, 20, 30, 50, etc.).\n\nThe options are the following.\n\n- randomisationFactor: defaults to 0, must be between 0 and 1\n- initialDelay: defaults to 100 ms\n- maxDelay: defaults to 10000 ms\n\nWith these values, the backoff delay will increase from 100 ms to 10000 ms. The\nrandomisation factor controls the range of randomness and must be between 0\nand 1. By default, no randomisation is applied on the backoff delay.\n\n### backoff.exponential([options])\n\nConstructs an exponential backoff (10, 20, 40, 80, etc.).\n\nThe options are the following.\n\n- randomisationFactor: defaults to 0, must be between 0 and 1\n- initialDelay: defaults to 100 ms\n- maxDelay: defaults to 10000 ms\n- factor: defaults to 2, must be greater than 1\n\nWith these values, the backoff delay will increase from 100 ms to 10000 ms. The\nrandomisation factor controls the range of randomness and must be between 0\nand 1. By default, no randomisation is applied on the backoff delay.\n\n### backoff.call(fn, [args, ...], callback)\n\n- fn: function to call in a backoff handler, i.e. the wrapped function\n- args: function's arguments\n- callback: function's callback accepting an error as its first argument\n\nConstructs a `FunctionCall` instance for the given function. The wrapped\nfunction will get retried until it succeds or reaches the maximum number\nof backoffs. In both cases, the callback function will be invoked with the\nlast result returned by the wrapped function.\n\nIt is the caller's responsability to initiate the call by invoking the\n`start` method on the returned `FunctionCall` instance.\n\n### Class Backoff\n\n#### new Backoff(strategy)\n\n- strategy: the backoff strategy to use\n\nConstructs a new backoff object from a specific backoff strategy. The backoff\nstrategy must implement the `BackoffStrategy`interface defined bellow.\n\n#### backoff.failAfter(numberOfBackoffs)\n\n- numberOfBackoffs: maximum number of backoffs before the fail event gets\nemitted, must be greater than 0\n\nSets a limit on the maximum number of backoffs that can be performed before\na fail event gets emitted and the backoff instance is reset. By default, there\nis no limit on the number of backoffs that can be performed.\n\n#### backoff.backoff([err])\n\nStarts a backoff operation. If provided, the error parameter will be emitted\nas the last argument of the `backoff` and `fail` events to let the listeners\nknow why the backoff operation was attempted.\n\nAn error will be thrown if a backoff operation is already in progress.\n\nIn practice, this method should be called after a failed attempt to perform a\nsensitive operation (connecting to a database, downloading a resource over the\nnetwork, etc.).\n\n#### backoff.reset()\n\nResets the backoff delay to the initial backoff delay and stop any backoff\noperation in progress. After reset, a backoff instance can and should be\nreused.\n\nIn practice, this method should be called after having successfully completed\nthe sensitive operation guarded by the backoff instance or if the client code\nrequest to stop any reconnection attempt.\n\n#### Event: 'backoff'\n\n- number: number of backoffs since last reset, starting at 0\n- delay: backoff delay in milliseconds\n- err: optional error parameter passed to `backoff.backoff([err])`\n\nEmitted when a backoff operation is started. Signals to the client how long\nthe next backoff delay will be.\n\n#### Event: 'ready'\n\n- number: number of backoffs since last reset, starting at 0\n- delay: backoff delay in milliseconds\n\nEmitted when a backoff operation is done. Signals that the failing operation\nshould be retried.\n\n#### Event: 'fail'\n\n- err: optional error parameter passed to `backoff.backoff([err])`\n\nEmitted when the maximum number of backoffs is reached. This event will only\nbe emitted if the client has set a limit on the number of backoffs by calling\n`backoff.failAfter(numberOfBackoffs)`. The backoff instance is automatically\nreset after this event is emitted.\n\n### Interface BackoffStrategy\n\nA backoff strategy must provide the following methods.\n\n#### strategy.next()\n\nComputes and returns the next backoff delay.\n\n#### strategy.reset()\n\nResets the backoff delay to its initial value.\n\n### Class ExponentialStrategy\n\nExponential (10, 20, 40, 80, etc.) backoff strategy implementation.\n\n#### new ExponentialStrategy([options])\n\nThe options are the following.\n\n- randomisationFactor: defaults to 0, must be between 0 and 1\n- initialDelay: defaults to 100 ms\n- maxDelay: defaults to 10000 ms\n- factor: defaults to 2, must be greater than 1\n\n### Class FibonacciStrategy\n\nFibonnaci (10, 10, 20, 30, 50, etc.) backoff strategy implementation.\n\n#### new FibonacciStrategy([options])\n\nThe options are the following.\n\n- randomisationFactor: defaults to 0, must be between 0 and 1\n- initialDelay: defaults to 100 ms\n- maxDelay: defaults to 10000 ms\n\n### Class FunctionCall\n\nThis class manages the calling of an asynchronous function within a backoff\nloop.\n\nThis class should rarely be instantiated directly since the factory method\n`backoff.call(fn, [args, ...], callback)` offers a more convenient and safer\nway to create `FunctionCall` instances.\n\n#### new FunctionCall(fn, args, callback)\n\n- fn: asynchronous function to call\n- args: an array containing fn's args\n- callback: fn's callback\n\nConstructs a function handler for the given asynchronous function.\n\n#### call.isPending()\n\nReturns whether the call is pending, i.e. hasn't been started.\n\n#### call.isRunning()\n\nReturns whether the call is in progress.\n\n#### call.isCompleted()\n\nReturns whether the call is completed.\n\n#### call.isAborted()\n\nReturns whether the call is aborted.\n\n#### call.setStrategy(strategy)\n\n- strategy: strategy instance to use, defaults to `FibonacciStrategy`.\n\nSets the backoff strategy to use. This method should be called before\n`call.start()` otherwise an exception will be thrown.\n\n#### call.failAfter(maxNumberOfBackoffs)\n\n- maxNumberOfBackoffs: maximum number of backoffs before the call is aborted\n\nSets the maximum number of backoffs before the call is aborted. By default,\nthere is no limit on the number of backoffs that can be performed.\n\nThis method should be called before `call.start()` otherwise an exception will\nbe thrown..\n\n#### call.retryIf(predicate)\n\n- predicate: a function which takes in as its argument the error returned\nby the wrapped function and determines whether it is retriable.\n\nSets the predicate which will be invoked to determine whether a given error\nshould be retried or not, e.g. a network error would be retriable while a type\nerror would stop the function call. By default, all errors are considered to be\nretriable.\n\nThis method should be called before `call.start()` otherwise an exception will\nbe thrown.\n\n#### call.getLastResult()\n\nReturns an array containing the last arguments passed to the completion callback\nof the wrapped function. For example, to get the error code returned by the last\ncall, one would do the following.\n\n``` js\nvar results = call.getLastResult();\n// The error code is the first parameter of the callback.\nvar error = results[0];\n```\n\nNote that if the call was aborted, it will contain the abort error and not the\nlast error returned by the wrapped function.\n\n#### call.getNumRetries()\n\nReturns the number of times the wrapped function call was retried. For a\nwrapped function that succeeded immediately, this would return 0. This\nmethod can be called at any point in time during the call life cycle, i.e.\nbefore, during and after the wrapped function invocation.\n\n#### call.start()\n\nInitiates the call the wrapped function. This method should only be called\nonce otherwise an exception will be thrown.\n\n#### call.abort()\n\nAborts the call and causes the completion callback to be invoked with an abort\nerror if the call was pending or running; does nothing otherwise. This method\ncan safely be called mutliple times.\n\n#### Event: 'call'\n\n- args: wrapped function's arguments\n\nEmitted each time the wrapped function is called.\n\n#### Event: 'callback'\n\n- results: wrapped function's return values\n\nEmitted each time the wrapped function invokes its callback.\n\n#### Event: 'backoff'\n\n- number: backoff number, starts at 0\n- delay: backoff delay in milliseconds\n- err: the error that triggered the backoff operation\n\nEmitted each time a backoff operation is started.\n\n#### Event: 'abort'\n\nEmitted when a call is aborted.\n\n## Annotated source code\n\nThe annotated source code can be found at [mathieuturcotte.github.io/node-backoff/docs](http://mathieuturcotte.github.io/node-backoff/docs/).\n\n## License\n\nThis code is free to use under the terms of the [MIT license](http://mturcotte.mit-license.org/).\n", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T04:11:55.889Z", "created": "2012-04-20T01:04:30.744Z", "0.0.1": "2012-04-20T01:04:31.437Z", "0.0.2": "2012-04-24T19:14:30.591Z", "0.0.3": "2012-06-27T22:32:10.567Z", "0.1.0": "2012-07-25T12:33:33.583Z", "0.2.0": "2012-07-27T02:55:58.435Z", "0.2.1": "2012-08-07T22:27:47.797Z", "0.3.0": "2012-08-10T16:33:30.276Z", "1.0.0": "2012-08-16T15:16:46.110Z", "1.1.0": "2012-09-19T19:39:12.095Z", "1.2.0": "2012-10-12T14:49:09.297Z", "1.2.1": "2013-01-19T22:14:57.912Z", "2.0.0": "2013-01-26T19:04:14.712Z", "2.1.0": "2013-03-28T02:02:03.200Z", "2.2.0": "2013-04-13T20:11:43.082Z", "2.3.0": "2013-05-18T18:48:28.214Z", "2.4.0": "2014-06-21T15:11:53.139Z", "2.4.1": "2014-12-06T20:57:26.257Z", "2.5.0": "2016-03-12T15:21:13.929Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/MathieuTurcotte/node-backoff.git"}, "users": {"fgribreau": true, "dominictarr": true, "klyngbaek": true, "scull7": true, "silas": true, "ajohnstone": true, "joshperry": true, "animustechnology": true, "bojand": true, "guumaster": true, "hugojosefson": true, "moimikey": true, "jzhang300": true, "chriszs": true, "zewish": true, "santi8ago8": true, "shanewholloway": true, "manikantag": true, "gamersdelight": true, "michaelfro": true, "matthiasg": true, "pixelventures": true, "damonoverboe": true}, "keywords": ["backoff", "retry", "<PERSON><PERSON><PERSON><PERSON>", "exponential"], "license": "MIT", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/MathieuTurcotte/node-backoff/issues"}, "homepage": "https://github.com/MathieuTurcotte/node-backoff#readme"}