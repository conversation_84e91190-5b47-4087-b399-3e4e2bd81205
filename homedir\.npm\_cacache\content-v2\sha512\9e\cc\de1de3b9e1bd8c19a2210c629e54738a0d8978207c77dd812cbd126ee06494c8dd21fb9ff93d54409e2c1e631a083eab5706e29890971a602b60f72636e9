{"_id": "isstream", "_rev": "16-74103cb9a64157336a5baa0744493fde", "name": "isstream", "description": "Determine if an object is a Stream", "dist-tags": {"latest": "0.1.2"}, "versions": {"0.0.0": {"name": "isstream", "version": "0.0.0", "description": "Determine if an object is a Stream", "main": "isstream.js", "scripts": {"test": "tar --xform 's/^package/readable-stream-1.0/' -zxf readable-stream-1.0.*.tgz && tar --xform 's/^package/readable-stream-1.1/' -zxf readable-stream-1.1.*.tgz && node test.js; rm -rf readable-stream-1.?/"}, "repository": {"type": "git", "url": "https://github.com/rvagg/isstream.git"}, "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "devDependencies": {"tape": "~2.12.3", "core-util-is": "~1.0.0", "isarray": "0.0.1", "string_decoder": "~0.10.x", "inherits": "~2.0.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "homepage": "https://github.com/rvagg/isstream", "_id": "isstream@0.0.0", "dist": {"shasum": "566875667e0178ca298d5c869ff3ca6aba71e3f8", "tarball": "https://registry.npmjs.org/isstream/-/isstream-0.0.0.tgz", "integrity": "sha512-25AEfchYPGnJ60fpU21Zv3wu2JYJ+U7RWj5oLnWZYkLh6GrE6t3QzNYhoiOnDsfoXT+1W1haWv5T3QQmO44kiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCapxvwC1j16rSvkRso2vJSEaRvWr+NQOq8k0xx7v4B7AIgSjzCuchEAI6K+hkWQrrgphQOJe2IBxntFKq9BVECoBw="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}]}, "0.1.0": {"name": "isstream", "version": "0.1.0", "description": "Determine if an object is a Stream", "main": "isstream.js", "scripts": {"test": "tar --xform 's/^package/readable-stream-1.0/' -zxf readable-stream-1.0.*.tgz && tar --xform 's/^package/readable-stream-1.1/' -zxf readable-stream-1.1.*.tgz && node test.js; rm -rf readable-stream-1.?/"}, "repository": {"type": "git", "url": "https://github.com/rvagg/isstream.git"}, "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "devDependencies": {"tape": "~2.12.3", "core-util-is": "~1.0.0", "isarray": "0.0.1", "string_decoder": "~0.10.x", "inherits": "~2.0.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "homepage": "https://github.com/rvagg/isstream", "_id": "isstream@0.1.0", "dist": {"shasum": "1fc062509b8c8902ee78d0a8d015ad3251472285", "tarball": "https://registry.npmjs.org/isstream/-/isstream-0.1.0.tgz", "integrity": "sha512-31uLBqMsUVfWsfSi/8xTzzXVQMjkt6FCbkt8ZsDM6wb8T2b/tIOiUQRcBbSDlNmlaPxvqUzpNzr0cFk3XsYEWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFmFHTv6YdT/1pXqJoUIobWbEWvK6Ong+6aCmJDQqQiYAiBlB9KGadkRXAnoJZtxbdUe50N42W+aPNQJQaE0sV5Cug=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}]}, "0.1.1": {"name": "isstream", "version": "0.1.1", "description": "Determine if an object is a Stream", "main": "isstream.js", "scripts": {"test": "tar --xform 's/^package/readable-stream-1.0/' -zxf readable-stream-1.0.*.tgz && tar --xform 's/^package/readable-stream-1.1/' -zxf readable-stream-1.1.*.tgz && node test.js; rm -rf readable-stream-1.?/"}, "repository": {"type": "git", "url": "https://github.com/rvagg/isstream.git"}, "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "devDependencies": {"tape": "~2.12.3", "core-util-is": "~1.0.0", "isarray": "0.0.1", "string_decoder": "~0.10.x", "inherits": "~2.0.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "homepage": "https://github.com/rvagg/isstream", "gitHead": "0406cfe2677231b7b23a229a61b15999bf60ce67", "_id": "isstream@0.1.1", "_shasum": "48332c5999893996ba253c81c7bd6e7ae0905c4f", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "dist": {"shasum": "48332c5999893996ba253c81c7bd6e7ae0905c4f", "tarball": "https://registry.npmjs.org/isstream/-/isstream-0.1.1.tgz", "integrity": "sha512-pOzOiMsQZXRue+LQSo8hnA4zAxvzRYtv+U2dPa0MaXIL+Xxjg1gkJK/oUCcJ9DWyqys3n0d/SVTEthKq/2oCJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDScdI8hALoeF0sPWfwuTk9zc4azgbEy5P85cJ+wXx7FwIgHSTCYc0d+/3GqTdBV1t2+tEiSofMCgwh36aozL7//ZQ="}]}}, "0.1.2": {"name": "isstream", "version": "0.1.2", "description": "Determine if an object is a Stream", "main": "isstream.js", "scripts": {"test": "tar --xform 's/^package/readable-stream-1.0/' -zxf readable-stream-1.0.*.tgz && tar --xform 's/^package/readable-stream-1.1/' -zxf readable-stream-1.1.*.tgz && node test.js; rm -rf readable-stream-1.?/"}, "repository": {"type": "git", "url": "https://github.com/rvagg/isstream.git"}, "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "devDependencies": {"tape": "~2.12.3", "core-util-is": "~1.0.0", "isarray": "0.0.1", "string_decoder": "~0.10.x", "inherits": "~2.0.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "homepage": "https://github.com/rvagg/isstream", "gitHead": "cd39cba6da939b4fc9110825203adc506422c3dc", "_id": "isstream@0.1.2", "_shasum": "47e63f7af55afa6f92e1500e690eb8b8529c099a", "_from": ".", "_npmVersion": "2.6.1", "_nodeVersion": "1.4.3", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "dist": {"shasum": "47e63f7af55afa6f92e1500e690eb8b8529c099a", "tarball": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDq1ao1Sjnv9SBM977B6QDai3T/PsUzVKKM/4wQuiE04wIgDEJAffv0wBs3ddzjee8J0CavLUvtXcZ7CX7pwG5sEW4="}]}}}, "readme": "# isStream\n\n[![Build Status](https://secure.travis-ci.org/rvagg/isstream.png)](http://travis-ci.org/rvagg/isstream)\n\n**Test if an object is a `Stream`**\n\n[![NPM](https://nodei.co/npm/isstream.svg)](https://nodei.co/npm/isstream/)\n\nThe missing `Stream.isStream(obj)`: determine if an object is standard Node.js `Stream`. Works for Node-core `Stream` objects (for 0.8, 0.10, 0.11, and in theory, older and newer versions) and all versions of **[readable-stream](https://github.com/isaacs/readable-stream)**.\n\n## Usage:\n\n```js\nvar isStream = require('isstream')\nvar Stream = require('stream')\n\nisStream(new Stream()) // true\n\nisStream({}) // false\n\nisStream(new Stream.Readable())    // true\nisStream(new Stream.Writable())    // true\nisStream(new Stream.Duplex())      // true\nisStream(new Stream.Transform())   // true\nisStream(new Stream.PassThrough()) // true\n```\n\n## But wait! There's more!\n\nYou can also test for `isReadable(obj)`, `isWritable(obj)` and `isDuplex(obj)` to test for implementations of Streams2 (and Streams3) base classes.\n\n```js\nvar isReadable = require('isstream').isReadable\nvar isWritable = require('isstream').isWritable\nvar isDuplex = require('isstream').isDuplex\nvar Stream = require('stream')\n\nisReadable(new Stream()) // false\nisWritable(new Stream()) // false\nisDuplex(new Stream())   // false\n\nisReadable(new Stream.Readable())    // true\nisReadable(new Stream.Writable())    // false\nisReadable(new Stream.Duplex())      // true\nisReadable(new Stream.Transform())   // true\nisReadable(new Stream.PassThrough()) // true\n\nisWritable(new Stream.Readable())    // false\nisWritable(new Stream.Writable())    // true\nisWritable(new Stream.Duplex())      // true\nisWritable(new Stream.Transform())   // true\nisWritable(new Stream.PassThrough()) // true\n\nisDuplex(new Stream.Readable())    // false\nisDuplex(new Stream.Writable())    // false\nisDuplex(new Stream.Duplex())      // true\nisDuplex(new Stream.Transform())   // true\nisDuplex(new Stream.PassThrough()) // true\n```\n\n*Reminder: when implementing your own streams, please [use **readable-stream** rather than core streams](http://r.va.gg/2014/06/why-i-dont-use-nodes-core-stream-module.html).*\n\n\n## License\n\n**isStream** is Copyright (c) 2015 Rod Vagg [@rvagg](https://twitter.com/rvagg) and licenced under the MIT licence. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE.md file for more details.\n", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T03:07:39.418Z", "created": "2014-04-07T00:03:04.615Z", "0.0.0": "2014-04-07T00:03:04.615Z", "0.1.0": "2014-04-07T00:38:39.891Z", "0.1.1": "2014-12-08T23:11:38.322Z", "0.1.2": "2015-03-07T00:15:15.512Z"}, "homepage": "https://github.com/rvagg/isstream", "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "repository": {"type": "git", "url": "https://github.com/rvagg/isstream.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"wenbing": true, "antixrist": true, "leizongmin": true, "mojaray2k": true, "coolhanddev": true, "ganeshkbhat": true}}