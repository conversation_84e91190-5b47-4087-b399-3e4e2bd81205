{"name": "browserslist", "dist-tags": {"latest": "4.25.0"}, "versions": {"0.1.0": {"name": "browserslist", "version": "0.1.0", "dependencies": {"caniuse-db": "^1.0.30000032 "}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.0.1", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "dist": {"shasum": "b566254ca06832bd3326f1559a2361fed92bd691", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.1.0.tgz", "integrity": "sha512-ltSJvB6Lte6O/WltNlFWH5WG0jG4YDFq555/tp5SqtKpdVYc4osmMDHSNIn/YpISYaBFa8B2kOhUA76dgE4Idg==", "signatures": [{"sig": "MEQCIGmMNwGMDK6+NHvC+DN3iMcCWmY7mBwDprrVcWktyV/hAiAZbW/Hba+zDS3rgl2ZyYDFHwvgVrfjcuRhY7rgHwTdrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.1.1": {"name": "browserslist", "version": "0.1.1", "dependencies": {"caniuse-db": "^1.0.30000043"}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "dist": {"shasum": "279e955e6e00afcfc4dcc6160d79aea44db0645c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.1.1.tgz", "integrity": "sha512-xc/vrDm9doyy8wHpBd7TKEm4h3IZdjXxeAH7gZdXBI0/JfcnF5xac/DTuBOChbvFOzJly6A3DvrAj/pJn+O0Rw==", "signatures": [{"sig": "MEQCIGsY4BzwSH7i26h9Aip3QZyVyjYHYNwIykOE01QEES2nAiBgV6h+13F361DPgsOKrPicpnHkfSZ6gLoFd4anUp5ooA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.1.2": {"name": "browserslist", "version": "0.1.2", "dependencies": {"caniuse-db": "^1.0.30000048"}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "dist": {"shasum": "e27e20730dd426149bab8051073a41fc9d3cf238", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.1.2.tgz", "integrity": "sha512-qaMt6MQVpyAk5NyOloY5jeznNjnm1Na3igd1AYb579WFQIbQ4AlD0js/kQdUFYWxxHzdYIlExobZjEqDhzCgGA==", "signatures": [{"sig": "MEYCIQDZxiDt8LLBksTuiOHjQh7lQMmcZVma3EpB3B/oLEkUDwIhAJeAZCmc2UP3ngIVmz6h/VBj2akHFRbeXsy0kkN82Jsl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.1.3": {"name": "browserslist", "version": "0.1.3", "dependencies": {"caniuse-db": "^1.0.30000054"}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "dist": {"shasum": "2f67975c24bb357b6b48b4bc46e7c39a1f0b3b5b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.1.3.tgz", "integrity": "sha512-R<PERSON>uKKid9OQDFJeoIucQni6Fuu7da/QzGnIN3YScfSGtKwwyLTet9L9gUEsjElI2fG8n1oGnIO0R9UonbLwJ6BQ==", "signatures": [{"sig": "MEUCIQDO0K1ASYBGRwiV1fLiY18fI1NEe9aamx/XLvogdijPOAIgeSE1wDcxGSQvVar1ys76G2OW6PKxR/G1BbrwjqNBG6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.2.0": {"name": "browserslist", "version": "0.2.0", "dependencies": {"caniuse-db": "^1.0.30000054"}, "devDependencies": {"chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0"}, "dist": {"shasum": "e5b7cf311cccb70772cd22d4f61c7bb80523ecd2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.2.0.tgz", "integrity": "sha512-i6Sq3Sb/Li23kPHugpBK01cW+NTzifzfc2zjlhZ8NcSBoX64B513eXgcALNgEtDmfLzuVgLosLpaNUdUjkKscg==", "signatures": [{"sig": "MEQCIGcG+YRsS6zeITdff8nQN/eS109RjtQlHNM12gWdmhrJAiAJXrMz48XZanXGa/fzQRLbg5rDA30OoQGJDUGmmo6Scg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.3.0": {"name": "browserslist", "version": "0.3.0", "dependencies": {"caniuse-db": "^1.0.30000078"}, "devDependencies": {"chai": "2.0.0", "gulp": "3.8.11", "mocha": "2.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.2", "jshint-stylish": "1.0.0"}, "dist": {"shasum": "f4429e509b25e00c43d77bb60cc0be37292613f6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.3.0.tgz", "integrity": "sha512-ZKtKJIsBo251ENUlt5mdi5B8tgDyr9D+E/cUI0DphODaCsclZnVyvYW9ltylmKsFR97+xQmniE4ENbnbCnWR0A==", "signatures": [{"sig": "MEUCIHZ+ggRgpEOV7Ljel0fsX0a5F2KGoBDoIrujJfR9tS3eAiEAliW12b3XqUtjaWwaUQNYZaSDsuxgdRELvPw9HmW4Idc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.3.1": {"name": "browserslist", "version": "0.3.1", "dependencies": {"caniuse-db": "^1.0.30000081"}, "devDependencies": {"chai": "2.1.0", "gulp": "3.8.11", "mocha": "2.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.2", "jshint-stylish": "1.0.1"}, "dist": {"shasum": "d60dd16f4aa10087f18b4b19d6432f9f79bdc833", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.3.1.tgz", "integrity": "sha512-PQjLnXNWgwYgc8chpFdqTfZsSpXiJuaMD4DnqxrBm/7b4uRR3dlGlBsF+TJaCjIWJeXxT2dD/XbD9PXA++faWQ==", "signatures": [{"sig": "MEUCIQCkztK5EjyJRcn428Z3qEloZej7P2951d/qrsK6B0MZvAIga/aNBvaV0/UJAE0oaht/3miMMMBcbY8E16HeTWtkj48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.3.2": {"name": "browserslist", "version": "0.3.2", "dependencies": {"caniuse-db": "^1.0.30000113"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "mocha": "2.2.1", "gulp-mocha": "2.0.1", "gulp-eslint": "0.6.0"}, "dist": {"shasum": "4e516ed1882086ff2479bab80cd164b3ae463545", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.3.2.tgz", "integrity": "sha512-J2C1s94eRdfmItLoo3rKmdatFwPEh2QV/x6OBWC0TZfdmtDtaSfVfOfb0rOJJGNYMiC7fU28uBD8Im3BsUbMLg==", "signatures": [{"sig": "MEYCIQCkPmDus6TvqmeeATGiGuNcW1S38sG0R+MvwCJFDKSn2QIhAJHUVImcxyBqkQixUpgV05T9Xq660/b/qaD4O96WU0fc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.3.3": {"name": "browserslist", "version": "0.3.3", "dependencies": {"caniuse-db": "^1.0.30000127"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "mocha": "2.2.4", "gulp-mocha": "2.0.1", "gulp-eslint": "0.9.0"}, "dist": {"shasum": "c21beb6b3d3c2492404084781113f39fc133f2c0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.3.3.tgz", "integrity": "sha512-/jbys+EcVhS1h4hQmIzWdttTzyqhOnEH3dX1dTJO3iVseBueOQq3VF8HZOlwzSOQKyAA9ryvtPY5PV4Q/sBL1A==", "signatures": [{"sig": "MEUCIQDrFpdVqeQrTqjS+DvQ7/1j2zGE/ORT4Yq2Yp2UFLiEBQIgIcFhZPgOUStj7YA1S8SQO7LXIICQvrmiVXn9MiXA6is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.4.0": {"name": "browserslist", "version": "0.4.0", "dependencies": {"caniuse-db": "^1.0.30000153"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "gulp-mocha": "2.0.1", "gulp-eslint": "0.11.1"}, "dist": {"shasum": "3bd4ab9199dc1b9150d4d6dba4d9d3aabbc86dd4", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.4.0.tgz", "integrity": "sha512-/JVhaf9S6ru3THyiuwX5j86pT79r5UtgwV3s6w+KpGlmUzPxfMbI5OBxO88iFtqgdqPuNirprachS3m1611qKA==", "signatures": [{"sig": "MEQCIH3K+5T6u38RnCfx0YeHBpIrE/ioFUcZFaTi/RKvkSQuAiBs0efV8Mo7x6RxFMiUxX2VvkDtcpPyH8SyK4nNl4lgLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "0.5.0": {"name": "browserslist", "version": "0.5.0", "dependencies": {"caniuse-db": "^1.0.30000214"}, "devDependencies": {"chai": "3.0.0", "gulp": "3.9.0", "mocha": "2.2.5", "gulp-mocha": "2.1.2", "gulp-eslint": "0.14.0"}, "dist": {"shasum": "b82882493637c342b66ad3182c919e1dac6d1724", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-0.5.0.tgz", "integrity": "sha512-qrE6zBcJjdzvEwbeU2/UM1AKBeW16ngVE/zPnmLR679YAHocET7CkGtSF3oh/4ozhjcDZqSID9lCksxUpPLc7w==", "signatures": [{"sig": "MEUCIQCgqNhC7Gz9VWItRZ7y6yQxMu3FSCRCzkNiQis5MyBt6gIgO6pbMqr5BGCfSQXov2+L0BNo22/F+950YVfR+RfZdcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.0.0": {"name": "browserslist", "version": "1.0.0", "dependencies": {"caniuse-db": "^1.0.30000281"}, "devDependencies": {"chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.3.0", "gulp-mocha": "2.1.3", "gulp-eslint": "1.0.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "678336f890f2b5bd3cdf2093150533dcbab3e185", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.0.0.tgz", "integrity": "sha512-zAsoD942DaSKutUiJB9cNe8CKY0iq0VccRMC8N8OWGQZxqCvDmpWveMlF+I8xLJwBzBnif6VJtW4jOgj/2PKjg==", "signatures": [{"sig": "MEUCIQCEdLCvKETNhHhRdkubvto+jN9M8Q5n9a1cR0oW20+jFwIgHVkasxf02emgCfb64zyKOQacXw9vYvBsOD6WCr5vQOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.0.1": {"name": "browserslist", "version": "1.0.1", "dependencies": {"caniuse-db": "^1.0.30000335"}, "devDependencies": {"chai": "3.3.0", "gulp": "3.9.0", "mocha": "2.3.3", "gulp-mocha": "2.1.3", "gulp-eslint": "1.0.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ef0dd708318cdf74325faeea59efec84d9464717", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.0.1.tgz", "integrity": "sha512-RFDON6gOEm7hSSaEK5UkAbjAwfC7iVzrR5Gu4eHaaL5QsLoR2if5guuQXSgxezs3K5447DiGviNlZy/Uysu0kA==", "signatures": [{"sig": "MEUCIQD7lCbReU9OqDHrdojwCfUuQTBjOOIGnCqceJhjxI089QIgVZe3YLiQwIFewGELGMGzpwdOu4h7ANL3RbEfm3rc95s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.1.0": {"name": "browserslist", "version": "1.1.0", "dependencies": {"caniuse-db": "^1.0.30000384"}, "devDependencies": {"ava": "0.9.1", "eslint": "1.10.3"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "a9ef42c2a51b51becefb3ce525ebee908a578bc5", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.1.0.tgz", "integrity": "sha512-2PiGLsoJs4ACYHhKS83TvN+58EbX4JKzpUzeh9hSM8MY35DTSj440ZDa90A5YlO1iDJ8Xgfedn06Uar8B1K8/Q==", "signatures": [{"sig": "MEQCICZ/8rgZ7ULlr4+und18XFZ/KpLWchnxg2MlahokRv5SAiA75d5lkgnXOAo2133e50iUILOAzMifK4URFM1OT4n38w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.1.1": {"name": "browserslist", "version": "1.1.1", "dependencies": {"caniuse-db": "^1.0.30000387"}, "devDependencies": {"ava": "0.9.1", "eslint": "1.10.3"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "a333af8331160e1db14219ec1cc9b2da20cd4d37", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.1.1.tgz", "integrity": "sha512-d1SMvBvU5jIGqZRHSSzZ3b1P2pwchbeHQ+Sq0Nqo+TmBTOOsEQIgsjYmQfWaqf8gP8mfhWTJhKX3lnWhomQn1Q==", "signatures": [{"sig": "MEUCIGoQZZZPiiJ6qJFRQHO2mdNHIJcsdy86Z3NDnz3r4IzhAiEA9NbBi36d5vNkD/YouYSh2CsNUeY8KhuxaQAWyeSNa38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.1.2": {"name": "browserslist", "version": "1.1.2", "dependencies": {"caniuse-db": "^1.0.30000409"}, "devDependencies": {"ava": "0.11.0", "eslint": "1.10.3", "eslint-config-postcss": "^1.0.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ee71ae31fc87764da23816fc4f09d2346a31879b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.1.2.tgz", "integrity": "sha512-1niL8xpy/nhu5uChGoUnO1hHZG3QyDxs+ELg/1tDQoNkfwiRawnOilxb9SUsVbkXjWYYqjjLFr2RESiAwV3lbA==", "signatures": [{"sig": "MEUCIBru2K0wa7uTXOOdxhg2Vdbb6Va7RRxvAt7iVpTWo7FAAiEA0jLKvTc2Pm0hXZlOd24vp0N6jwT1dhbHUzncdKLR3lY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.1.3": {"name": "browserslist", "version": "1.1.3", "dependencies": {"caniuse-db": "^1.0.30000409"}, "devDependencies": {"ava": "0.11.0", "eslint": "1.10.3", "eslint-config-postcss": "1.0.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "63e0f1fd68545a56eccafca55b7b722918ebfff5", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.1.3.tgz", "integrity": "sha512-X8UFFBqP+v9Gedwd9tuiGYpHsUT971xHjVhSOBiV+LkOSa6qEhOZt1UVAP6LoARMM7huRZ3i/rvbb6lr2v4mrg==", "signatures": [{"sig": "MEUCIQCohhYfHwP0g5oQW80V7rvuGiZpaMzazRx8kbhB3g7XyQIgbBZxM/sC0yHxs7sScNHIfThtML9lc0FgCRpCj3GEvSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.2.0": {"name": "browserslist", "version": "1.2.0", "dependencies": {"caniuse-db": "^1.0.30000430"}, "devDependencies": {"ava": "0.13.0", "eslint": "2.4.0", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ef1b039b4cdd75f43382fa13a24f2a8d5da536d0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.2.0.tgz", "integrity": "sha512-4KG+F0xRqCJ4LW/YeeW5rvMrqg+sz0M7i1SmJJiAVdxci0j54DNdcrPlLxm8Rq4ZcHivzEUathUQLH3E4ktd+w==", "signatures": [{"sig": "MEUCIQD6QKD1ZMxh21ozBt3zF2ZFr/1iAsL6msQ2Y6LYcOKHtgIgCpePnpYMrFRs5ePrL8kuG6iYUmWzB3PJGEc7QoT9cLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.0": {"name": "browserslist", "version": "1.3.0", "dependencies": {"caniuse-db": "^1.0.30000431"}, "devDependencies": {"ava": "0.13.0", "eslint": "2.4.0", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "517bca7192a3b47fd1d71a92366bb2b30b50eed2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.0.tgz", "integrity": "sha512-Ndo+18Qsvaj5M8du2HljDj06js5h3m2gI2GKouYTCDaiw04uBTt5YLk0pMarbmBmn1W6t7hPjhPAr+OqEv5naA==", "signatures": [{"sig": "MEUCIQD4b5ojhmRT0vrdrrFoZB3EcytMSKTbG0cbGx8OZ5WTWgIgBNGItOVrLLNXbm4RU1SeGxSozNWGKlCF6aYWB3ihJuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.1": {"name": "browserslist", "version": "1.3.1", "dependencies": {"caniuse-db": "^1.0.30000444"}, "devDependencies": {"ava": "0.13.0", "eslint": "2.6.0", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "6198006485d3a350732408dca74165b69abfa718", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.1.tgz", "integrity": "sha512-n5TVMrDJodz0M9AoD4KIebWtDrbkwbRVOyIWTBIxTFtZ0SnlirNSzzPAHocqB0J6GIrJxZcX5TdukZ3gM8njLg==", "signatures": [{"sig": "MEUCIHU7bJmVnctwBRzIeSmlcF3KaScOvYFbId7FlH//enaCAiEAsJ7pdXlWj/DPKBkBzApETiEwPoDo20x8Z0Me/B/hpPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.2": {"name": "browserslist", "version": "1.3.2", "dependencies": {"caniuse-db": "^1.0.30000471"}, "devDependencies": {"ava": "0.15.1", "eslint": "2.11.1", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "afb4309174edae938d3042ab0f55dcc2fd88806a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.2.tgz", "integrity": "sha512-jeaIbJtmkqhyi5fycC8JX1lm6+TPRqEXoaMVkoImvFqRjIzR2RiTGUGgVgi1XUM7USmDzjZITr8+NHn0lGGHwA==", "signatures": [{"sig": "MEYCIQDEHil7Ev133HHCZ2eeahLJMPI7eT8rKpxoWucrdrTTSQIhAPDNjoGN1J4CWxKxWjNVlCmcEJIakDNZiE09YSNbilIt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.3": {"name": "browserslist", "version": "1.3.3", "dependencies": {"caniuse-db": "^1.0.30000484"}, "devDependencies": {"ava": "0.15.2", "eslint": "2.13.0", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "2fc1e896ed3636e2649651f74907b53254ddad6a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.3.tgz", "integrity": "sha512-1gWIzmKFCwixAH2BQpUlbCkAFt7aQwLPmE/55eLbx2QF7GEehqpY/FNcti8sUX79R83lGyh+Yi1XLBgMJfZrSg==", "signatures": [{"sig": "MEYCIQD9PafXGwsRcrpOb8MChBfEQgZZxel/YZEf5ZK7SaVP+wIhAO2TaQ0NIVrpg1jx4yUfzmNB6GTF8HfKfiy1q5dw8Li4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.4": {"name": "browserslist", "version": "1.3.4", "dependencies": {"caniuse-db": "^1.0.30000488"}, "devDependencies": {"ava": "0.15.2", "eslint": "2.13.1", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "97bba1a98f011f976333a053706d089ffc9b30fa", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.4.tgz", "integrity": "sha512-r0IPQvs3vi0Juqcg5y2I2SUuidw3RSW1bzgSIY2V/7Sd9xpSFUz457ksdNqCYbZyYU8TAPE8Vkg74Sk3c71giQ==", "signatures": [{"sig": "MEYCIQCHy5n5s8BCCWsV09oq9y3ZJaxtTmpFP+8WROD3bPBRVAIhAPkkOeuds4/L7hGi+1y4UpPojtApaok37AW7mHcsS0/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.5": {"name": "browserslist", "version": "1.3.5", "dependencies": {"caniuse-db": "^1.0.30000506"}, "devDependencies": {"ava": "0.15.2", "eslint": "3.0.1", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "2a1daf9b82b654186337ec13de4684b8f78450d7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.5.tgz", "integrity": "sha512-Xb2ZGTLX6WxTPwQfjmeqmQHq0eZPl7mInrrPP7EK+2H/9fylbnVFKN8hUB+42+zyiyqrJwvxHi7fkD8qkVEsig==", "signatures": [{"sig": "MEUCIQCbQxDhIeNhLLp8Z3C0p/dGFl1dimXmr6dsrHi+9f8PggIgDBu48F+pF3GlfVQbt+LMAHkC4yo9KmyLJzyA/+mNXR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.3.6": {"name": "browserslist", "version": "1.3.6", "dependencies": {"caniuse-db": "^1.0.30000525"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.3.1", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "952ff48d56463d3b538f85ef2f8eaddfd284b133", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.3.6.tgz", "integrity": "sha512-fKSWtyNQTclfi1A+s2KU91/r1mfANG1ZibxTdCwJGfV1J9UwcV22plFOm0wkaq4WzqW87zxiAkyp2Ho1Wn1NnA==", "signatures": [{"sig": "MEYCIQCEOtL74mlgPW3jy3bKrKpqeF1vjB5kTkNCMPhX58v60wIhANPDlRLNdioYJ1CV5zB3BpTz5l3rKED9p9R8KQr4oXG/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.4.0": {"name": "browserslist", "version": "1.4.0", "dependencies": {"caniuse-db": "^1.0.30000539"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.6.0", "eslint-config-postcss": "2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "9cfdcf5384d9158f5b70da2aa00b30e8ff019049", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.4.0.tgz", "integrity": "sha512-tXAQS/6YQiUsYWt9gtUTomlPSjxcUlmMoozZ4nk8YVoq/hk+3eJFRfawwrO/TQBGuTQ+5U98Y1bJ1FGp+Vlkgg==", "signatures": [{"sig": "MEUCIDcQd70zDAZgTHFH803VmOdaKN1BVCEOvufoioGDAxd5AiEAsB4pqU4mh5Z5fZ8gtV0b8Bxoj4Cfgq4Cdtc98kzyeYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.5.0": {"name": "browserslist", "version": "1.5.0", "dependencies": {"caniuse-db": "^1.0.30000601"}, "devDependencies": {"jest": "^18.0.0", "eslint": "^3.11.1", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.2.2", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "b9a1f3880dcc7afdf07baf48c8385200530eb126", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.5.0.tgz", "integrity": "sha512-1Vq1BmR6GT4KPh1dXqzGSNCT9L3tce9cqcrLE1MTS6E5RZMXW6M0SvEC6aL/k/rGa0TMILyhxk0Dl7mhkawTbw==", "signatures": [{"sig": "MEUCIHpK96zo9kICt/LHFEQPklweX80yDU28xt5eXSdC8DSBAiEA88rl72UB7YebudDf7Kqb0YLAvSwLdSMiG0ELTP6uk+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.5.1": {"name": "browserslist", "version": "1.5.1", "dependencies": {"caniuse-db": "^1.0.30000601"}, "devDependencies": {"jest": "^18.0.0", "eslint": "^3.11.1", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.2.2", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "67c3f2a1a6ad174cd01d25d2362e6e6083b26986", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.5.1.tgz", "integrity": "sha512-5TS4g4W9DRb5/bydnZ6j739uwdC9gozmSngWxhCSV1RZH4UtsFTCf1F7nIe7q34cCfnDscUfQn0jbUgUrudQ0Q==", "signatures": [{"sig": "MEQCIFlgUbJYOWBYsYwqazxl0/RVpziOHKzmJS1kjY3NM5/3AiAc2hCI4a+jmp72ZSLmaj+yruVHrkQ1Jc8z6BJ3pde2Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.5.2": {"name": "browserslist", "version": "1.5.2", "dependencies": {"caniuse-db": "^1.0.30000604"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.11.1", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.2.5", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "1c82fde0ee8693e6d15c49b7bff209dc06298c56", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.5.2.tgz", "integrity": "sha512-vCBhhZxZ5WQ7IASBt89jwUAKiLu+ctcWMIGW4lrrKmmH3SIrvEGzGC8FJH9j5NT1ujvFiLZoGK3lzf0UcYWd9g==", "signatures": [{"sig": "MEUCIQCCDI3nOCuF5eiswqJ/398HjRSvnvIfy6lI5frcYMOzJwIgRBCQBYME1+s4smexd+k7Ge50k/mYT2Nhec/GKwyujm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.6.0": {"name": "browserslist", "version": "1.6.0", "dependencies": {"caniuse-db": "^1.0.30000613", "electron-to-chromium": "^1.2.0"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.14.0", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.2.7", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "85fb7c993540d3fda31c282baf7f5aee698ac9ee", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.6.0.tgz", "integrity": "sha512-FVKH0lKXYhHOcvOKOD4ZjYYPFH/ZsSc/Hhwu0NAvV70ItYjAJx+JjQBRv0Vm+N/zIEjIZFjshGIJ6XVtpW1v4A==", "signatures": [{"sig": "MEUCID4LhQqAB+ot+gnx0IGBvI/iI5DmW0q18EczITD92BiBAiEAqqqLuXbK3hrU6PlnPGTsi+KUg20zkeRL8najaxF91to=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.0": {"name": "browserslist", "version": "1.7.0", "dependencies": {"caniuse-db": "^1.0.30000617", "electron-to-chromium": "^1.2.1"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.14.1", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.2.8", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "738df5b2971354d198b2fbd5a22c560d2d896084", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.0.tgz", "integrity": "sha512-ryKvtArY9nR03Ooup9D5eHN+RKy3pFzKJJPEJIJHOK2tnfyZFJIJznM3xLdaJ6+RWNPZr2uWCp+vslnXWPS8VA==", "signatures": [{"sig": "MEYCIQCMls3vaMRd3BWBUoFGUgrksaRJ7PGcgIoYH7Jsbh1cFwIhAIJ5USc2Bvj6/4WKH0LdRWmnxlRaO76vg+p1E5iN8aA/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.1": {"name": "browserslist", "version": "1.7.1", "dependencies": {"caniuse-db": "^1.0.30000617", "electron-to-chromium": "^1.2.1"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.14.1", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.2.9", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "cc9bd193979a2a4b09fdb3df6003fefe48ccefe1", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.1.tgz", "integrity": "sha512-ZbC9U61Du1/aJ28A74fxKEicP9CAz6+qqkDlyU5LKKEVrHntvpWhwe33YFZtZ+KuMA+ga+VCIykZceGGbdU40A==", "signatures": [{"sig": "MEYCIQCYXEbVZFKuQNL97oBt3gWt0JXveLDjsfj7Vt97nfVzMAIhAJ9dnDV6M7rixKTpcn3e2AlhUpQT5FdB+D+GE+gz2ztQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.2": {"name": "browserslist", "version": "1.7.2", "dependencies": {"caniuse-db": "^1.0.30000622", "electron-to-chromium": "^1.2.2"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.15.0", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.3.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "cf4977283c3e692d6dcc241192e9de91504ff331", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.2.tgz", "integrity": "sha512-W7aBHM1v4ixu6HlHZ0HsoTXbLeePosomqs1FUbxOEh2jSrqUnT1Cp85otBTnRgFkgFDP1EgdKSviCH7RZVZDRA==", "signatures": [{"sig": "MEUCIG/XzSXPobfNxerL4THFy72TOND5I2/3afSPujoOVhX5AiEA6GprgEdKvN1u+NgMM7YsSooebH+2IGt9s+w4ncslL+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.3": {"name": "browserslist", "version": "1.7.3", "dependencies": {"caniuse-db": "^1.0.30000623", "electron-to-chromium": "^1.2.2"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.15.0", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.3.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "25ead9c917b278ad668b83f39c8025697797b2ab", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.3.tgz", "integrity": "sha512-t9+m/xnzNvDNZ9HainrGTAaNIMGpH+lzNn3UQ90uxLhYymx5lskF/VpeWZpm3gg8UF6suBWAouIvljndgEG9FQ==", "signatures": [{"sig": "MEUCIGindNA8DPi4HUS5ijZBQv44MxFjVKZK+n7ZK2XfkZ1UAiEAhEAMd9AQR3X9WL87y2MhMuc/BrAxYLgdIujp4tlaDnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.4": {"name": "browserslist", "version": "1.7.4", "dependencies": {"caniuse-db": "^1.0.30000624", "electron-to-chromium": "^1.2.2"}, "devDependencies": {"jest": "^18.1.0", "eslint": "^3.15.0", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.3.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "56a12da876f787223743a866224ccd8f97014628", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.4.tgz", "integrity": "sha512-Wje0uO1izibOfvmusRJ+rG7rLHhlR2vEDJZ8VKsOaiHucZmMbWVPWIbgME8Zg8x+16xT53RPQU92zgTG47435A==", "signatures": [{"sig": "MEQCIDiLUvpSfJIdDOZDSzRnW4FDPx5UnmwnZnYJJPKnfLVnAiBGB6oPyEBBIkVqhnkXTQeX2uvD4GrCynGVpv70zFWnXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.5": {"name": "browserslist", "version": "1.7.5", "dependencies": {"caniuse-db": "^1.0.30000624", "electron-to-chromium": "^1.2.3"}, "devDependencies": {"jest": "^19.0.1", "eslint": "^3.16.0", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.3.1", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "eca4713897b51e444283241facf3985de49a9e2b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.5.tgz", "integrity": "sha512-LvH5lOpHIkHMzNXJ62mjmovjIFOJJ6XbfCnDPqvcU2+Ub3QrWyoisnpxABPYbclW2icj8WIkzdeg6oHTMfxjZw==", "signatures": [{"sig": "MEUCIQD5kvoeE6KH+yskp1danetpeAsIbm48efOV2lYCpWyCHAIgTSvdNBK/yPk1V4mJeVXoz8DK7CfM6K0vK5tPK0ng0OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.6": {"name": "browserslist", "version": "1.7.6", "dependencies": {"caniuse-db": "^1.0.30000631", "electron-to-chromium": "^1.2.5"}, "devDependencies": {"jest": "^19.0.2", "eslint": "^3.16.1", "yaspeller": "^3.0.0", "pre-commit": "^1.1.3", "lint-staged": "^3.3.1", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "af98589ce6e7ab09618d29451faacb81220bd3ba", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.6.tgz", "integrity": "sha512-hgVDSSNeJOYpuuBHPLm6rjgxLT5cNXBXjoNq2Nm5Z42EOyoFmTA1RWROiEtqOT2Wo8jPGjafAZMXTMoqrx2vIw==", "signatures": [{"sig": "MEUCIAHw/vZhooJWG72lHeCjLEPub9VpJQ/5eDo6ovyhJEWGAiEAxzgUvFNlDvyBBeCrb3MHj+ZZzvBmI94MW5XWXoBXauI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "1.7.7": {"name": "browserslist", "version": "1.7.7", "dependencies": {"caniuse-db": "^1.0.30000639", "electron-to-chromium": "^1.2.7"}, "devDependencies": {"jest": "^19.0.2", "eslint": "^3.18.0", "pre-commit": "^1.1.3", "lint-staged": "^3.4.0", "yaspeller-ci": "^0.3.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "0bd76704258be829b2398bb50e4b62d1a166b0b9", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-1.7.7.tgz", "integrity": "sha512-qHJblDE2bXVRYzuDetv/wAeHOJyO97+9wxC1cdCtyzgNuSozOyRCiiLaCR1f71AN66lQdVVBipWm63V+a7bPOw==", "signatures": [{"sig": "MEYCIQDNBSYcyge31FXVrUUGcE3Op0Qj8TRB7CC6/ViKqUQcbAIhANtyoHClZIlXiAfRohv2E8hLwNuZ165iN0yTxMEfGiKY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.0.0": {"name": "browserslist", "version": "2.0.0", "dependencies": {"caniuse-lite": "^1.0.30000657", "electron-to-chromium": "^1.3.6"}, "devDependencies": {"jest": "^19.0.2", "eslint": "^3.19.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.4.0", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "f9251e5c728eb7f18020b6743c2ef03feaff2a27", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.0.0.tgz", "integrity": "sha512-8WhS6PqdFH+8hoL/zxt6yGFy1CmxzSC0NqdRaLcE16Z4X6W0WBTYzvfn5+wx+ncAoasZwscKXURB4Bm2W0Iq4Q==", "signatures": [{"sig": "MEUCIQCS/1rQ+9OMAHAwXIszBJN77TGf0x8UQyWVC4I/7R/iqgIgXXUI9tfsLi6ayw7jqG+Je5rzrmYVBINraiR210BhhBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.1.0": {"name": "browserslist", "version": "2.1.0", "dependencies": {"caniuse-lite": "^1.0.30000659", "electron-to-chromium": "^1.3.8"}, "devDependencies": {"jest": "^19.0.2", "eslint": "^3.19.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.4.0", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "2590d3de07c7129a7bd05ce3c3cb2a3fc56e78fa", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.1.0.tgz", "integrity": "sha512-4QfJDAgTJDgwE9NrM4DKdtLGQ8m8LscN4yz94gwOqxu/OM+Nf3kVFmDgwA+6T2I1CLxOQ06qYSqaA7NCXHXn/Q==", "signatures": [{"sig": "MEUCICRSZ4aubtNu1qqku8q84zYOOup4nRVLf1/IT0QuNWG9AiEAnRqi7pKuKRAijjjvuBXIIz3jAjDFLAyXdYj8BAiPgC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.1.1": {"name": "browserslist", "version": "2.1.1", "dependencies": {"caniuse-lite": "^1.0.30000664", "electron-to-chromium": "^1.3.8"}, "devDependencies": {"jest": "^19.0.2", "eslint": "^3.19.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.4.1", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "d0da26318d86352be7693d407977b8523cc78b11", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.1.1.tgz", "integrity": "sha512-WM+z7aa8d0u4fbNX7/LKwrdsQxLThuaGzS1J+1EgIxWtilB3X/UAOmeixEl6FURbnWZ80AbSha+CCvrXqgdw7g==", "signatures": [{"sig": "MEQCIANStRG3HlKrc127oaHyPFhsDjxHbjpj4YGiVIfxcmwYAiBKIDm2ysTcUB9ukj12IL1ZtxBPNMpvIrEp3J9X55YgUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.1.2": {"name": "browserslist", "version": "2.1.2", "dependencies": {"caniuse-lite": "^1.0.30000665", "electron-to-chromium": "^1.3.9"}, "devDependencies": {"jest": "^19.0.2", "eslint": "^3.19.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.4.1", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "a9dd0791342dab019861c2dd1cd0fd5d83230d39", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.1.2.tgz", "integrity": "sha512-yWRRRIXSHU43c2d/MqKTMXxpUKQ4dkKDFFTAjE+HncLqFusWTeph7y+S1Y+KkgBuFwG6WgPMbvC2KZDUYBtdPg==", "signatures": [{"sig": "MEUCID5RCHrMiVNB0kzlx8zVuUqvDWoYr6QU3/FKnkJPDAC/AiEA6jcJcB9+3NdIs/JGKMQ0/zzEl5lhWI8YZ0/sEoroRcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.1.3": {"name": "browserslist", "version": "2.1.3", "dependencies": {"caniuse-lite": "^1.0.30000670", "electron-to-chromium": "^1.3.11"}, "devDependencies": {"jest": "^20.0.3", "eslint": "^3.19.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.4.2", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "302dc8e5e44f3d5937850868aab13e11cac3dbc7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.1.3.tgz", "integrity": "sha512-TJ+IxnYxr3t6j9Ernyhw6pq7z0vuPYNl9SjxdmEHXb28WGWVdGFIjwRZKe2CPdm1u0NUEGowOdBPmm/tQzWajQ==", "signatures": [{"sig": "MEYCIQDMcOmDtRJUTtatGE5JQtENLbSGaBjJ9VSiGtrgmez8jQIhAIhFrLVAfkdffpFCMiEAdAa8ZM8V8OBdGHXD0pwFtBjF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.1.4": {"name": "browserslist", "version": "2.1.4", "dependencies": {"caniuse-lite": "^1.0.30000670", "electron-to-chromium": "^1.3.11"}, "devDependencies": {"jest": "^20.0.3", "eslint": "^3.19.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.4.2", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "cc526af4a1312b7d2e05653e56d0c8ab70c0e053", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.1.4.tgz", "integrity": "sha512-W2q3Cey3MaYeWiMXiC/7YK527GQdA8BJBET1EFPzjxy2kLabgAh9+2NJzuFCj3Y2gWMu2O34sKtU2t6FC9OlCQ==", "signatures": [{"sig": "MEUCIFSfm/hqQFATYKf7UDb603ezoVtOG7niDxiBPoGf8HRHAiEAjVJPNV8EPshGYpv20IaKKIqHUyBIo2X/uxG8Y3YgroE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.1.5": {"name": "browserslist", "version": "2.1.5", "dependencies": {"caniuse-lite": "^1.0.30000684", "electron-to-chromium": "^1.3.14"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.0.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^3.6.1", "yaspeller-ci": "^0.4.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "e882550df3d1cd6d481c1a3e0038f2baf13a4711", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.1.5.tgz", "integrity": "sha512-dlcNyLXf7WsJDTqOmwD2AROEIwtgthhUwnUFt4G/AH1Nu4ukgiieeLCfvYrzs7QdMJ+PJVN0/l+EXM1MW4lQtg==", "signatures": [{"sig": "MEUCIQDEoGerPs6A4X4E3kWn3gx1wOs8yGDTtEW5CyUR5lxTegIgQ4pog2t5pUNNdQgkGGf7squEUkuEpEzRgglELzLLUCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.2.0": {"name": "browserslist", "version": "2.2.0", "dependencies": {"caniuse-lite": "^1.0.30000701", "electron-to-chromium": "^1.3.15"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.2.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.2", "yaspeller-ci": "^0.6.0", "eslint-config-postcss": "^2.0.2"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "5e35ec993e467c6464b8cb708447386891de9f50", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.2.0.tgz", "integrity": "sha512-t09UuOv/4/hrL1y4wZ871+kKE6E2bkdMD6duZtV7FZIHFpsmdQfW63BH3bAjkfpkbP/eFKyenUbDUKigV/VmRw==", "signatures": [{"sig": "MEYCIQCtvswkWxRrj2yNQR1+G+JqLj4P7m5ITCLUL7C97/Bq7AIhAPPSbvhl9oYuZOAm+wOJGczDoEvvnO8GFdtRtPWVH55H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.2.1": {"name": "browserslist", "version": "2.2.1", "dependencies": {"caniuse-lite": "^1.0.30000704", "electron-to-chromium": "^1.3.16"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.3.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.2", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "709048c57bf3bf9b382105c396a737ad525d948e", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.2.1.tgz", "integrity": "sha512-q9NH7wY49FzqqsYHBv2kA/A5XUaaEvWShX3OGLugthdeqnCEaSEmU/TffiIDlcrb5XISYGCNyLvxtnHgOu1qVA==", "signatures": [{"sig": "MEUCIQDnXObQvW5AM2bP20qfOrFn/zOGhsSClOpUb0UBLLGrpAIgUgfUSdfbBMKHtacbLyLVdevfrpf+fgMJ41t9to4Cttw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.2.2": {"name": "browserslist", "version": "2.2.2", "dependencies": {"caniuse-lite": "^1.0.30000704", "electron-to-chromium": "^1.3.16"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.3.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.2", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "e9b4618b8a01c193f9786beea09f6fd10dbe31c3", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.2.2.tgz", "integrity": "sha512-MejxGMNIeIqzgaMKVYfFTWHinrwZOnWMXteN9VlHinTd13/0aDmXY9uyRqNsCTnVxqRmrjQFcXI7cy0q9K1IYg==", "signatures": [{"sig": "MEUCIGeqmJxnxulr034+/6bvImeFdBwZ1iZgA/i0Oj3we8kyAiEAhuz7ll0/A7KQnSvw3WVEAwlbPVcsK+5W+nirtDcoBjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.3.0": {"name": "browserslist", "version": "2.3.0", "dependencies": {"caniuse-lite": "^1.0.30000710", "electron-to-chromium": "^1.3.17"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.3.0", "pre-commit": "^1.1.3", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.2", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "b2aa76415c71643fe2368f6243b43bbbb4211752", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.3.0.tgz", "integrity": "sha512-jDr9Mea+n+FwI+kR0ce7rXCFBoM7hbL80G/th7oPxuNSK4V5J3LPMHB5vykjeI2h7fgSihBbSdoJPmzUC0606Q==", "signatures": [{"sig": "MEUCIEBaYREtXtbIN0sjeaSRlRrl5Bn0HsRXTQZmj2/Z9eyZAiEAr0HiXDp1zfXGDYf4k3jACkrD3Npilwaewo8gn8/TIis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.3.1": {"name": "browserslist", "version": "2.3.1", "dependencies": {"caniuse-lite": "^1.0.30000712", "electron-to-chromium": "^1.3.17"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.4.1", "pre-commit": "^1.1.3", "size-limit": "^0.8.1", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.3", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "39500a2090330b2a090120ea6c7fc78b6e091c5e", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.3.1.tgz", "integrity": "sha512-YSRvpyaxDBBAPkwYO0THouUmA+cvYbyBHZrP/byJEUGDT9r/6TQcDASBtrAj4S45Le6JZbV8oeUAPR9wT8tisg==", "signatures": [{"sig": "MEYCIQClr1lr7Ar38KuTeHqzqNM8WfWw6z5TZvo7JessjMEVQQIhALx3YyA8ag8xse72K3dqRenpYjTWqiHzgMZuvwGoGCsP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.3.2": {"name": "browserslist", "version": "2.3.2", "dependencies": {"caniuse-lite": "^1.0.30000715", "electron-to-chromium": "^1.3.18"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.4.1", "pre-commit": "^1.1.3", "size-limit": "^0.8.4", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.3", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "343ff101cce799d5eaf0b742e17d0d21efc2d379", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.3.2.tgz", "integrity": "sha512-arvLUwBTsmpfmyMfoBQH8WWICiyaVkMxJsft73/rTRU80rAPSXsi3M0uYBcUH22w7MG475eET31F4M0+31w81g==", "signatures": [{"sig": "MEUCIQD7HUAQCbIwNRDZesb/GHNvOviikAdGSF+YD7fX+Qge0QIgTlGBroxSvIaKBYBg+VU6RidctwNDyv/sydHQJadjI8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.3.3": {"name": "browserslist", "version": "2.3.3", "dependencies": {"caniuse-lite": "^1.0.30000715", "electron-to-chromium": "^1.3.18"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.4.1", "pre-commit": "^1.1.3", "size-limit": "^0.9.0", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.3", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "2b0cabc4d28489f682598605858a0782f14b154c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.3.3.tgz", "integrity": "sha512-p9hz6FA2H1w1ZUAXKfK3MlIA4Z9fEd56hnZSOecBIITb5j0oZk/tZRwhdE0xG56RGx2x8cc1c5AWJKWVjMLOEQ==", "signatures": [{"sig": "MEUCIQDisHjFvqLNVHx713RrCZkHrMToGr/UneT9Zp6unY8XNAIgBZ/59hFPg7qVoMnsAranuGuVCtTKwOy8mqRIRj+8A9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.4.0": {"name": "browserslist", "version": "2.4.0", "dependencies": {"caniuse-lite": "^1.0.30000718", "electron-to-chromium": "^1.3.18"}, "devDependencies": {"jest": "^20.0.4", "eslint": "^4.5.0", "pre-commit": "^1.1.3", "size-limit": "^0.10.0", "cross-spawn": "^5.1.0", "lint-staged": "^4.0.4", "yaspeller-ci": "^0.6.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-node": "^5.1.1", "eslint-config-logux": "^16.0.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "693ee93d01e66468a6348da5498e011f578f87f8", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.4.0.tgz", "integrity": "sha512-aM2Gt4x9bVlCUteADBS6JP0F+2tMWKM1jQzUulVROtdFWFIcIVvY76AJbr7GDqy0eDhn+PcnpzzivGxY4qiaKQ==", "signatures": [{"sig": "MEYCIQCv1sIhNsY3XqyolL8Xm+TOwDWFz3nYqOI6kV4PEaUAhwIhAMsLSfrGFeep0iBsw4ys/PojOiQy8VtL8jwWVOXa7DpF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.5.0": {"name": "browserslist", "version": "2.5.0", "dependencies": {"caniuse-lite": "^1.0.30000744", "electron-to-chromium": "^1.3.24"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.8.0", "pre-commit": "^1.1.3", "size-limit": "^0.11.6", "cross-spawn": "^5.1.0", "lint-staged": "^4.2.3", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.0", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "0ea00d22813a4dfae5786485225a9c584b3ef37c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.5.0.tgz", "integrity": "sha512-6Vw1LIigGw8zCK0gxczksUMZlO+oPUwBazAztMmFL/F8D5wB0qCuxRJGYgYM3JzaO0v2ZMRIg+nnnOgNsPGHeA==", "signatures": [{"sig": "MEYCIQCFaZ+B9Tq9fSjWAOl3BTdCvAMD4RrYbc4QhcbK4RyEwgIhAO41HUkGxaArSnr6uMrfhqKnvQ8feVYdPq95W+X84puq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.5.1": {"name": "browserslist", "version": "2.5.1", "dependencies": {"caniuse-lite": "^1.0.30000744", "electron-to-chromium": "^1.3.24"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.8.0", "pre-commit": "^1.1.3", "size-limit": "^0.11.6", "cross-spawn": "^5.1.0", "lint-staged": "^4.2.3", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.0", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-promise": "^3.5.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "68e4bc536bbcc6086d62843a2ffccea8396821c6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.5.1.tgz", "integrity": "sha512-jAvM2ku7YDJ+leAq3bFH1DE0Ylw+F+EQDq4GkqZfgPEqpWYw9ofQH85uKSB9r3Tv7XDbfqVtE+sdvKJW7IlPJA==", "signatures": [{"sig": "MEUCIHGc6RQLWfaG4WAh8JiEeUzcY793tWC9pmkhG+i8bfBiAiEA9rixa1HOicT318sD6UHlcOlydm9tnKBZzBuEoTLGhW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.6.0": {"name": "browserslist", "version": "2.6.0", "dependencies": {"caniuse-lite": "^1.0.30000755", "electron-to-chromium": "^1.3.27"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.10.0", "pre-commit": "^1.1.3", "size-limit": "^0.12.0", "cross-spawn": "^5.1.0", "lint-staged": "^4.3.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "15b14ab7b7df4b108979585badff121eca9e1835", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.6.0.tgz", "integrity": "sha512-XgAVmla9QAC5VkNmJ+i5zbgh+sPumLEL83MDYR9J8LoCJ1NgkptAqIny0+2VyI83OAnVHLdCsUjM4vs8RLsppQ==", "signatures": [{"sig": "MEUCIQDfsPE3vXYWoKjLsRJZXIvD9/JOSEp2k93mNN8+M+I4/AIgYzzjLkNDl8/aExoqXspRUlAbSGYqo72X5W2uJsfJOm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.6.1": {"name": "browserslist", "version": "2.6.1", "dependencies": {"caniuse-lite": "^1.0.30000755", "electron-to-chromium": "^1.3.27"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.10.0", "pre-commit": "^1.1.3", "size-limit": "^0.12.0", "cross-spawn": "^5.1.0", "lint-staged": "^4.3.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "cc65a05ad6131ebda26f076f2822ba1bc826376b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.6.1.tgz", "integrity": "sha512-HBZwVT7ciQB9KlXM3AUMQbnQXtHWPsEUKQTiS0BEFfY5bOrMl94ORaqQD1GyuTGh69ZmYeue9QBqiw219e09eQ==", "signatures": [{"sig": "MEUCIBCSPw4BWM5zTv9hXGZVGGO+9UvUVQjM+PT/bOaMPtn7AiEAoNxLzWPoT9RvSe5Sem+AbaklaaY15Jggh596o2R2aUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.7.0": {"name": "browserslist", "version": "2.7.0", "dependencies": {"caniuse-lite": "^1.0.30000757", "electron-to-chromium": "^1.3.27"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.10.0", "pre-commit": "^1.1.3", "size-limit": "^0.12.1", "cross-spawn": "^5.1.0", "lint-staged": "^4.3.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "dc375dc70048fec3d989042a35022342902eff00", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.7.0.tgz", "integrity": "sha512-s34mrlczJsfbJu//mz/m9zlOy/S6tiP6El1u8iC1gTfEnzKXvxo8RAoCxS/MmojB7rd7bnfYzvKQNHykWaUWWw==", "signatures": [{"sig": "MEUCIQCHN0vZxn3AtH7ueTM15DoSJYYy0RCD/ZsDJ9g0ODbq8wIgQXu6oCMsvzYnMgx8G8x/+a1xo8S/5n8WS9VbiGFralA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.8.0": {"name": "browserslist", "version": "2.8.0", "dependencies": {"caniuse-lite": "^1.0.30000758", "electron-to-chromium": "^1.3.27"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.10.0", "pre-commit": "^1.1.3", "size-limit": "^0.13.1", "cross-spawn": "^5.1.0", "lint-staged": "^4.3.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.2.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "27d64028130a2e8585ca96f7c3b7730eff4de493", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.8.0.tgz", "integrity": "sha512-iiWHM1Et6Q4TQpB7Ar6pxuM3TNMXasVJY4Y/oh3q38EwR3Z+IdZ9MyVf7PI4MJFB4xpwMcZgs9bEUnPG2E3TCA==", "signatures": [{"sig": "MEYCIQDQKW/SA0fxjTGwSy6+cRBTpTyMROAfPULVEEgAYH3dugIhAIf41iZANfkunS5MCccSxg7T12sd3y4LcU80/vrlc5qw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.9.0": {"name": "browserslist", "version": "2.9.0", "dependencies": {"caniuse-lite": "^1.0.30000760", "electron-to-chromium": "^1.3.27"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.11.0", "pre-commit": "^1.1.3", "size-limit": "^0.13.1", "cross-spawn": "^5.1.0", "lint-staged": "^5.0.0", "yaspeller-ci": "^0.7.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.3.2", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^16.2.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "706aca15c53be15610f466e348cbfa0c00a6a379", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.9.0.tgz", "integrity": "sha512-vJEBcDTANoDhSHL46NeOEW5hvQw7It9uCqzeFPQhpawXfnOwnpvW5C97vn1eGJ7iCkSg8wWU0nYObE7d/N95Iw==", "signatures": [{"sig": "MEUCIQCsN3GUXoYJQqfrkvUCKzipeSiMUWKmOneMyjFWPCtQnQIgBgDph9sxJbn5aDn+MJbB0lM1RO31339jZMcWpcHNTeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.9.1": {"name": "browserslist", "version": "2.9.1", "dependencies": {"caniuse-lite": "^1.0.30000770", "electron-to-chromium": "^1.3.27"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.11.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.13.2", "cross-spawn": "^5.1.0", "lint-staged": "^5.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.3.2", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "b72d3982ab01b5cd24da62ff6d45573886aff275", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.9.1.tgz", "integrity": "sha512-3n3nPdbUqn3nWmsy4PeSQthz2ja1ndpoXta+dwFFNhveGjMg6FXpWYe12vsTpNoXJbzx3j7GZXdtoVIdvh3JbA==", "signatures": [{"sig": "MEYCIQCVljHjr4cT0ITWXsLpCCutBql9J6Ims7gk+UFTfW0CMQIhAOaBaJHuOEiTurr4+GaMX8bA9R75l+oV5TSS2Ll7AO6Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.10.0": {"name": "browserslist", "version": "2.10.0", "dependencies": {"caniuse-lite": "^1.0.30000780", "electron-to-chromium": "^1.3.28"}, "devDependencies": {"jest": "^21.2.1", "eslint": "^4.12.1", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.13.2", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.4.1", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "bac5ee1cc69ca9d96403ffb8a3abdc5b6aed6346", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.10.0.tgz", "integrity": "sha512-WyvzSLsuAVPOjbljXnyeWl14Ae+ukAT8MUuagKVzIDvwBxl4UAwD1xqtyQs2eWYPGUKMeC3Ol62goqYuKqTTcw==", "signatures": [{"sig": "MEUCIQCui1GlZtU6+n+JJ7uVGZ4efcZ4t3L221HCeNDj3Nf1xAIgB6xT/m0DiyRyfM10wa6n31ABWu3w1DvU3cUjNZUcDA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.10.1": {"name": "browserslist", "version": "2.10.1", "dependencies": {"caniuse-lite": "^1.0.30000784", "electron-to-chromium": "^1.3.30"}, "devDependencies": {"jest": "^22.0.4", "eslint": "^4.14.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.13.2", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.5.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "f9dc692b79004a78ec9ba0d012c54de44cc6d7e4", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.10.1.tgz", "integrity": "sha512-vUe1YhphiCb5lJ4YQwA5VbmAhZgv9cwgAQm/rZT6GA2X97ewDMOLPyDr08iGsqvPajvC/wEwWBZNtFFa8l4Hlw==", "signatures": [{"sig": "MEUCIEGGNHGjHbyEkcRXwL/RugQSJf6AT3Hvpt08kz0PLQ8kAiEAwkSHJAC1a+D12A5qPlm7Zg43evrVDcag/h64/OaX7LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.10.2": {"name": "browserslist", "version": "2.10.2", "dependencies": {"caniuse-lite": "^1.0.30000784", "electron-to-chromium": "^1.3.30"}, "devDependencies": {"jest": "^22.0.4", "eslint": "^4.14.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.13.2", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.1.0", "eslint-plugin-jest": "^21.5.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "0838eec4b3db2d860cee13bf6c0691e7e8133822", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.10.2.tgz", "integrity": "sha512-laXHudMWp4ZLgmObmAHZEzkP7Te4lzUyueSZabSuWTImaQznFhFm42SDRmwutfpAOHqDB4fJKGWSZdyrf5TsXg==", "signatures": [{"sig": "MEYCIQDNZ2ZLy4wt/XYVy7n5nMDtATVPsI6TeWb5n89+SMl/WwIhAK/neiHdy9zOW0VAgM6f7ET9+cL5XfgtkHQTyOoV4QXn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.11.0": {"name": "browserslist", "version": "2.11.0", "dependencies": {"caniuse-lite": "^1.0.30000784", "electron-to-chromium": "^1.3.30"}, "devDependencies": {"jest": "^22.0.4", "eslint": "^4.14.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.13.2", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.5.0", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "50350d6873a82ebe0f3ae5483658c571ae5f9d7d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.11.0.tgz", "integrity": "sha512-mNYp0RNeu1xueGuJFSXkU+K0nH+dBE/gcjtyhtNKfU8hwdrVIfoA7i5iFSjOmzkGdL2QaO7YX9ExiVPE7AY9JA==", "signatures": [{"sig": "MEUCIQCt2Gw2V4lOTsdWRrl86Og1Ipna1TFbzhBVZIx5RNuT+QIgYptpAQj2xwmtxRbYCEK/RTj1NOvJcVMgen4jO5W475w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.11.1": {"name": "browserslist", "version": "2.11.1", "dependencies": {"caniuse-lite": "^1.0.30000789", "electron-to-chromium": "^1.3.30"}, "devDependencies": {"jest": "^22.0.5", "eslint": "^4.15.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.13.2", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.6.1", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "02fda29d9a2164b879100126e7b0d0b57e43a7bb", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.11.1.tgz", "integrity": "sha512-Gp4oJOQOby5TpOJJuUtCrGE0KSJOUYVa/I+/3eD/TRWEK8jqZuJPAK1t+VuG6jp0keudrqtxlH4MbYbmylun9g==", "signatures": [{"sig": "MEQCIEA73BZUhpQ50TtztQdVm+TCFW257zOOGPbzbElNK6lCAiAQ4nGAsgbYypyzexK0t0QnQ44FzvLbmk7ECegSNr76sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.11.2": {"name": "browserslist", "version": "2.11.2", "dependencies": {"caniuse-lite": "^1.0.30000791", "electron-to-chromium": "^1.3.30"}, "devDependencies": {"jest": "^22.0.6", "eslint": "^4.15.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.14.0", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.6.2", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "76ad768b97a689512fcd9724a8b9d76cdffb18fd", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.11.2.tgz", "integrity": "sha512-BWT1zhRqq8BG/HrUQWB4pgkU6u27OyC1r5ErMn8zRaYTLtRS4cDtDCdZA3XYLsSmYWP2PPlBR8sggzrofImzgg==", "signatures": [{"sig": "MEUCIAP+PRp2QFzWfwjIJMqBbayzcKfnkOZtw7SvPXy7Y/uZAiEAiVbCbRYj/JONHxaGnyPiMqiUsisnzYlUBaxeKyuHQJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "2.11.3": {"name": "browserslist", "version": "2.11.3", "dependencies": {"caniuse-lite": "^1.0.30000792", "electron-to-chromium": "^1.3.30"}, "devDependencies": {"jest": "^22.0.6", "eslint": "^4.15.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.14.0", "cross-spawn": "^5.1.0", "lint-staged": "^6.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.6.2", "eslint-plugin-node": "^5.2.1", "eslint-config-logux": "^17.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "fe36167aed1bbcde4827ebfe71347a2cc70b99b2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-2.11.3.tgz", "integrity": "sha512-yWu5cXT7Av6mVwzWc8lMsJMHWn4xyjSuGYi4IozbVTLUOEYPSagUB8kiMDUHA1fS3zjr8nkxkn9jdvug4BBRmA==", "signatures": [{"sig": "MEQCIFFJ/8BiYMneUEvURaQ9CGZFi0p71DUveq4OixLdRHgAAiBl49UTA4wHR64A6SFRzc5LmclW1DCIpubIWHKXGxhGDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Browserslist 2 could fail on reading Browserslist >3.0 config used in other tools."}, "3.0.0": {"name": "browserslist", "version": "3.0.0", "dependencies": {"caniuse-lite": "^1.0.30000807", "electron-to-chromium": "^1.3.33"}, "devDependencies": {"jest": "^22.2.2", "eslint": "^4.17.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.14.0", "cross-spawn": "^6.0.4", "lint-staged": "^6.1.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.8.0", "eslint-plugin-node": "^6.0.0", "eslint-config-logux": "^19.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "5b41520c1a5ce6d0d2fe7c44bdf30e526b650403", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.0.0.tgz", "fileCount": 9, "integrity": "sha512-5ArwxNIJxmBTUUTe+F7P2AM8wNf6zFa9mb3/o0JCrhGdd042PuVgAZg4M+A29NUpqFEYl+H3kQQYoZUXfuRS9g==", "signatures": [{"sig": "MEUCIQDZtwKYOgHug4UZ6S2Gwjak04yIszrNxkxaiSy7zx0r9gIgA+nw09aFRa4PnYZ48Hn7dSwWo+xBvBRGXjsPrACkvZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51331}}, "3.1.0": {"name": "browserslist", "version": "3.1.0", "dependencies": {"caniuse-lite": "^1.0.30000808", "electron-to-chromium": "^1.3.33"}, "devDependencies": {"jest": "^22.3.0", "eslint": "^4.17.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.14.1", "cross-spawn": "^6.0.4", "lint-staged": "^6.1.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.12.1", "eslint-plugin-node": "^6.0.0", "eslint-config-logux": "^19.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "6a1ccc302ddf48e70480e2ee1a9acc293eceb306", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.1.0.tgz", "fileCount": 9, "integrity": "sha512-pyoJs5teqQWTdwOTG7F5IDKi7hMvifd9ri3EYLG2ElXlA2AwvqB1SZ6RIPMRHpmYb0RYN8N7GSERey5WgxSCUQ==", "signatures": [{"sig": "MEUCIGOQDCpA2JMdmZqg40OtNFYIqeUoMl0R6TNpjah7QuUPAiEAnQlKQ0D/+HU3gykDZnx03xIK46X/RbekVsEmzkn0Ung=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52284}}, "3.1.1": {"name": "browserslist", "version": "3.1.1", "dependencies": {"caniuse-lite": "^1.0.30000809", "electron-to-chromium": "^1.3.33"}, "devDependencies": {"jest": "^22.3.0", "eslint": "^4.18.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.14.1", "cross-spawn": "^6.0.4", "lint-staged": "^6.1.1", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.12.2", "eslint-plugin-node": "^6.0.0", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "d380fc048bc3a33e60fb87dc135110ebaaa6320a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.1.1.tgz", "fileCount": 9, "integrity": "sha512-zHGaPnTt70ywm+glR7uMJFZSl+ADGO67SgD2ae20L+Y3KJUeH4fVa89OkTqKCqAnXFE9mO4LTHBKBqKRlr7VNw==", "signatures": [{"sig": "MEQCIHnLeh4av4AQSv5ELbyP9BI3CvgYdgkqXUUoB3zqaomEAiAdjnkGlO9l/wQKFPsF6DTyReirIlGpSeEFn5eRJqXg9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52337}}, "3.1.2": {"name": "browserslist", "version": "3.1.2", "dependencies": {"caniuse-lite": "^1.0.30000813", "electron-to-chromium": "^1.3.36"}, "devDependencies": {"jest": "^22.4.2", "eslint": "^4.18.2", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.16.1", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.13.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-promise": "^3.6.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "893f29399d640ed35fe06bacd7eb1d78609a47e5", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.1.2.tgz", "fileCount": 9, "integrity": "sha512-iO5MiK7MZXejqfnCK8onktxxb+mcW+KMiL/5gGF/UCWvVgPzbgbkA5cyYfqj/IIHHo7X1z0znrSHPw9AIfpvrw==", "signatures": [{"sig": "MEUCIQCzwAxuPIp7VPDNuD3ujTm0BJaw0SGShMUFcQGG8VRS8AIgATfQOBaV+OgYXP8XqYJJxOBQuClTVphXktx+OmtK/GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53728}}, "3.2.0": {"name": "browserslist", "version": "3.2.0", "dependencies": {"caniuse-lite": "^1.0.30000815", "electron-to-chromium": "^1.3.39"}, "devDependencies": {"jest": "^22.4.2", "eslint": "^4.19.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.15.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "3d4a99710c12101e4567c9aeedade49c958cb883", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.0.tgz", "fileCount": 9, "integrity": "sha512-fXFmXYMUbD9E/I81MEknzD5TJmA1dQFy+2gYHHnO08CEWGTzWNlDyawBo9pfUIncaG840+RIuScjCT4IpQxTWA==", "signatures": [{"sig": "MEUCIHw/+WFLJwtWlbFtQgPNCRYqiwssdm7Kek5e0TzkAk5AAiEAszllJogJL64rwZ0ZH5bo4dhbLNQtEB8zWpRxi1Cgb/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55119}}, "3.2.1": {"name": "browserslist", "version": "3.2.1", "dependencies": {"caniuse-lite": "^1.0.30000819", "electron-to-chromium": "^1.3.40"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.15.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "4960a45fbfe39b2be36fe5ba07cce9ea32c8221c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.1.tgz", "fileCount": 9, "integrity": "sha512-Smb+H/evmlcOdUFLGBu45q0we1tQGFjNLAC1AsO1ZCQ8fBD6WtXLpPHgm6Hp/sh1lVS++WdY8gJwIxMN1jOgLQ==", "signatures": [{"sig": "MEQCIAgyhEORTcRzFLbv/1Ghou+X4kMryu8zFB3NX1zBAzoQAiB1HOmm+UHwwadw/2DcP4cbtrefRCOFCd5my1TclbR2pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55165}}, "3.2.2": {"name": "browserslist", "version": "3.2.2", "dependencies": {"caniuse-lite": "^1.0.30000819", "electron-to-chromium": "^1.3.40"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.15.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ad0559e3982786d80bfb24421ee2318daceffd2c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.2.tgz", "fileCount": 9, "integrity": "sha512-D<PERSON><PERSON>ZZceUfoOEVjaGBv5BAklxotFwOfBkAAV4E8VOhQpxY19rNlxvnOrd5ZjxSGcx9Zz2Y9GgQpvIlqp958cneA==", "signatures": [{"sig": "MEUCIH+2D0XgA18LerBhAYanOUTE2omVdPrbuiLmVF5leMo3AiEAjCDvgGq+3c9NFDyliQq0cFYdaTvCZoBZFpMF3smHuq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55669}}, "3.2.3": {"name": "browserslist", "version": "3.2.3", "dependencies": {"caniuse-lite": "^1.0.30000819", "electron-to-chromium": "^1.3.40"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.15.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ad36e56a43daeacf4d2b7bb16441b7ac30be4510", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.3.tgz", "fileCount": 9, "integrity": "sha512-cdpm0bFVz1KoRFuDTEk0IgxmZ2k2kTRKVlSQWf1RnxUe65GfjnMO30AxHpMkVv5wlPnpEYom7DhXwdMplKsKCw==", "signatures": [{"sig": "MEQCIAzrYCuav+HNKY/B+dasHVkOmU+ILl5nsnI+03bA/Y1aAiBgOK6k99zrpEEVlVZNdKoRaktHKCxYXvcsiThSvatjDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55855}}, "3.2.4": {"name": "browserslist", "version": "3.2.4", "dependencies": {"caniuse-lite": "^1.0.30000821", "electron-to-chromium": "^1.3.41"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "fs-extra": "^5.0.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.2.0", "eslint-plugin-jest": "^21.15.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.10.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "fb9ad70fd09875137ae943a31ab815ed76896031", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.4.tgz", "fileCount": 9, "integrity": "sha512-Dwe62y/fNAcMfknzGJnkh7feISrrN0SmRvMFozb+Y2+qg7rfTIH5MS8yHzaIXcEWl8fPeIcdhZNQi1Lux+7dlg==", "signatures": [{"sig": "MEQCIDyqoDy3kNcY3jUDLnTGqBJukTQ/FUrYEY2D89LoLCaVAiBUftcZfvvwTarPhT378sG75kJ2O+/HYNlhnCs8rVw5bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55986}}, "3.2.5": {"name": "browserslist", "version": "3.2.5", "dependencies": {"caniuse-lite": "^1.0.30000830", "electron-to-chromium": "^1.3.42"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "fs-extra": "^5.0.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.4", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.15.1", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.11.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.0.1"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "aa46a5ea33b5330178f3c91cfd85e148fcb57080", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.5.tgz", "fileCount": 9, "integrity": "sha512-svi6zUwlR6uiQRwShFfNMSEWzwttsVqSFEcqlLOWsgJ4tYk0goE2KLNGzctsLUU3E1AGpES0cgiyzkvuFNIRIg==", "signatures": [{"sig": "MEQCIFMcrXOIoqbjwT0WeEVX2M9zs6Jigky1/kqHcdQ5eyVDAiAWJEDd6+bowyZQeAfmdZfbu7PzlIUJp10ez645G9HCfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3U/gCRA9TVsSAnZWagAA4ywP/0SXbYZcbScOr+Pf0OOS\nK7bl1/lfsWX6TCBVGWm47NNuFkJjExp4x0O6Vp/Ti+GpgfgvPoPtW+GUgQf1\nF0/NxFElQCkirnfXWXJSAB4YxtIcePSTPezW2E+MeoEK6M4PWvMoygR3IoOX\nfENgmqWF5TUQQ5QMLv+3UmQgB/SMbEXTGXw+ihwVQhlT/NRD63sHM7929mAU\nolF03bok+GwcQ54EVlh+AzuXN5E0UDhDGe+OR5iEPNzRWO2szq5LyHYcoWCg\ndBl0Zy2e4ywDmZaq00InV+6h3Z+HCRE3r6+1qRt1VKrbXwev6/SdNqYFlqf9\nGGl+gHb4yHLpt2lGnJjoFZDkIZw6ihFqnRE1UCFQVtvv0SosetfkSWHGOyJq\nZFY+EI8Zhk9uICtTLegC2gVexS9maolR8PaVL9tLaHCJedv5Tmva8JPgZhfD\noaO/2hI2DNe0xqCfI5VcrYoOPKK+zSmFX8nIJ2oHoCTNYKJ5njdFUf3jRc6U\nEr2antTXT7fd3NFOZLVWpCpKLM6f16TRmtj+tJHwa2In0p9K0fjGKAVKrHSI\n6hXtui79+yjrz24PE0RbF7yojlt8OMhM001o1pNm5OhOQkoNOAucHzFmVWkO\nqV82w/ntcpHL0pZZUq0rhM2otHpTMUQTtHsopi4ovBBx6rrsHqxUkQkeq58Q\nWakA\r\n=sfqL\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.2.6": {"name": "browserslist", "version": "3.2.6", "dependencies": {"caniuse-lite": "^1.0.30000830", "electron-to-chromium": "^1.3.42"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "fs-extra": "^5.0.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.4", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.15.1", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.11.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "138a44d04a9af64443679191d041f28ce5b965d5", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.6.tgz", "fileCount": 9, "integrity": "sha512-XCsMSg9V4S1VRdcp265dJ+8kBRjfuFXcavbisY7G6T9QI0H1Z24PP53vvs0WDYWqm38Mco1ILDtafcS8ZR4xiw==", "signatures": [{"sig": "MEUCIFS0MLeRPMLGWorLtye1PejCVXLR+Saf44SEKMIy8EB9AiEAmkljx4TCiV0ZuMYcsN72Aad6pk71XhnhIP0DOqrZD5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4S46CRA9TVsSAnZWagAAytUP/2PfpyYCu2TmZcRVhDX+\nk8uKEvkKcuBQyNXOkzb3UJSGJkpTw6QC6QJsL+xBFTuh7/6UqIHfTGBJuGjV\ndNzdWfozehkk5rr3CR3WcE5UiIBis2wSyZ7+/eVmIUGHzEfUZ+Pvhq5oOPlv\neWlTZgJcyYPS3NAE6V1kt7CKkFaKIUXqBO/67VB63Vo+F3SWSFHjDYR5X3pL\nuwiBAnXtK6bzl7LJk0KacOV10znVK8GWFDdw33BsAwpzJh8BJ/UtSzzV9iIG\nbmOzuztJbHAoPgMbr1fUN4z24w8r7pOVd4NZ9tiXUgpbeU3m5j7CNlWGaZPA\n7rdDBLLWLtJ1Tk7345Y7DeKVr6xuBJ7Vom0r+ADXz7cEh5XFaLmPAPp5LQUZ\ndG+oIp0ql/x5yh/SyOdRJP9KcsdazmYeta6cfHFzJLXngZmAp+Cn8aIbI475\nmKzU3lwVVCSBD61ZM5ruOyIEF++SWLnwhZ7MwOdRZNrk3Retthg0RTDp6fm2\nav8KMITQSz541TymzuguXZjMT+vOeudMDKNj+RNIbTgyYb564dZstFD2SMfi\n4PSxC2/A7UaOvAmUrmCsohWlSTQg9jyy3XlQs1+bSFWoXot1iauw/hturUOR\nQt/O2S2C1ETtiOWhXmyCvFK0nvDGZOFRbeliMp8bwyJZDlQKZb1VGN5Hu7ij\nZThx\r\n=W99m\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.2.7": {"name": "browserslist", "version": "3.2.7", "dependencies": {"caniuse-lite": "^1.0.30000835", "electron-to-chromium": "^1.3.45"}, "devDependencies": {"jest": "^22.4.3", "eslint": "^4.19.1", "fs-extra": "^5.0.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.17.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.0.5", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.15.1", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^21.0.0", "eslint-plugin-import": "^2.11.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "aa488634d320b55e88bab0256184dbbcca1e6de9", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.7.tgz", "fileCount": 9, "integrity": "sha512-oYVLxFVqpX9uMhOIQBLtZL+CX4uY8ZpWcjNTaxyWl5rO8yA9SSNikFnAfvk8J3P/7z3BZwNmEqFKaJoYltj3MQ==", "signatures": [{"sig": "MEYCIQCydpucKLqJbW/aVoWsMStH1vX6kVncJTcyfc6MS0YlxgIhAK9HXhd512WqtNdzeBDCAJQ62N0cq+5nMX1TBIyZZKXj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa89mYCRA9TVsSAnZWagAAOEkP+QGHcMyv3EMoBx46CXa5\npgrug/XzCvWogEKsLk0SJ3wppCJ49H49OlimTjMJv3qGVeXUHWoM7VZcoTmA\nu+MZiWEJFtvtIQR8vXgBwDWZLw0lY30Ws5dvwejQofRDPE+/yIDxaw86PHF5\nPrHzxAHVEQAdK0KOopc6VtNUyGVaeIarWdT2Ax32PSJrriWtWUYLAbJ+kKlk\nof3Xrc6F0d5E7WCuRKontX4+blYkXLmXFLHMej/nwbhcEXV59uNX4uh7aNnv\nvahT0qaJXzGoRmTJx6QJI2RCedyybdR6KYMPBxuAzVAK5/lqd/cC5NcheIId\nyeucuvMxMp41nOvFjMh+Bwy/eEkhd7EEK9IhCKGwkYyzBwXgYG+efseXSLcA\nGYRRK94qPP4l8+33Mo52MEqOTCM8Y1Y/K5DUpzOx3E/CoPap3kERxPiKOb8Y\nA2rOBxrN9oAWsKv0C/T+Tp28WIg5ZsN1xzFJ0svJl8YeIayvpxTp5e3yXAL/\n5V8Pehc9UJQtqgoOXgJEhtrEMx0rxfjQsPXoSKKPLnpwVmi6bFNh9ddNoJQ2\n9mUGgFH4AjAL0Ro8XRr1fj77pZoVZdV1KDaXnRuTGd6+bWLTq7uZ9lbJ3ci0\n9s4rzudzBuZqt+lPVbk3hsD4WGtTzJTPbJg/u7FZP1CVegQu8RpIP3Izzh3/\ngfXf\r\n=zzW0\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.2.8": {"name": "browserslist", "version": "3.2.8", "dependencies": {"caniuse-lite": "^1.0.30000844", "electron-to-chromium": "^1.3.47"}, "devDependencies": {"jest": "^22.4.4", "eslint": "^4.19.1", "fs-extra": "^5.0.0", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.18.0", "cross-spawn": "^6.0.5", "lint-staged": "^7.1.2", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.15.1", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^22.1.0", "eslint-plugin-import": "^2.12.0", "eslint-plugin-promise": "^3.7.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "b0005361d6471f0f5952797a76fc985f1f978fc6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-3.2.8.tgz", "fileCount": 9, "integrity": "sha512-WHVocJYavUwVgVViC0ORikPHQquXwVh939TaelZ4WDqpWgTX/FsGhl/+P4qBUAGcRvtOgDgC+xftNWWp2RUTAQ==", "signatures": [{"sig": "MEUCIEEBlQwzNgzb4ZW6ifqFTu6EZAa33twKoCKF647F2EN+AiEA+oMFkZFIJA6yyJf2TWsV79knu1vXsUbF3XTjBsyAkQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAuJuCRA9TVsSAnZWagAA+K0P/1IgLg0D4Er3WTHQzOSF\njuPCMtpAOum8GPzqjf3gc2fZ4d0oACoS+hbbYc6GA7NenICLgwdBLs5ochWs\ntfk7fa2NYLzR0sFVUGvp9l/sLFW4D+WIaB/3+7Okyjy1mIgBIOr0lS0AVJJt\nTVeLVWZMT1aEh0hQsOeN1rCsUx1XakmPtjeBDrYJXIHXFa0nYVFp563DQIAx\nVQ4hnOrsUB3z5OkXr2ivEb7NFdetK8EfvJ7bpAQeuafbOxKaWmnAJKXXLBpM\ngg/+JC5yOuecelTgd1HsiR3THFsJ8lzOyuM8MHyDmf/8jmtv/J2DRasj94p2\npT90NI4QOdbng1ga2BszCxfB0nIBFVLek22GupLyg312jpiOx8dMc83bbGIh\nSd/V61kGg7lL/3xCyYaHrj8awyqUi5Dm08yoDJnOeJ1ANSkWlrCYIEjkDL5J\nrTRJ1zEoXgDdT0h2z0PVb5YFWtC/nF/q3YxY2lz3WhTaHpFRqk0Sc7mWHba4\n+vXvd/5jP/hdQ/mTWTo57thy9rfgqX8XT8EDAgk+66K8oJAQqkDZKNJ8jjI6\ngaXP2bVDYTuuqZLWwfVhPpN1KcqnZOam9ye9CCCiGoNbl4v+59641s0a31bz\n9YiIXGGI/3VoldPBMODSeOKhMYEnfQt/AX7fNbyYIZfe2YIp+9jM5ii2LnMk\nGRlq\r\n=wzf5\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0": {"name": "browserslist", "version": "4.0.0", "dependencies": {"caniuse-lite": "^1.0.30000859", "node-releases": "^1.0.0-alpha.10", "electron-to-chromium": "^1.3.50"}, "devDependencies": {"jest": "^23.2.0", "eslint": "^5.0.1", "fs-extra": "^6.0.1", "eslint-ci": "^0.1.1", "pre-commit": "^1.1.3", "size-limit": "^0.18.3", "cross-spawn": "^6.0.5", "lint-staged": "^7.2.0", "yaspeller-ci": "^1.0.0", "eslint-plugin-es5": "^1.3.1", "eslint-plugin-jest": "^21.17.0", "eslint-plugin-node": "^6.0.1", "eslint-config-logux": "^23.0.2", "eslint-plugin-import": "^2.13.0", "eslint-plugin-promise": "^3.8.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-standard": "^3.1.0"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "48703f1ed7ef981c6719e39e9444f20632b06571", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-fJq4izbUYHHNdQd/5Mco31HeL8U8dg5sSaj5boaDP17+aAe41CrxSZbQifIjaWw27iIilmy48z9PrVtelNJhbw==", "signatures": [{"sig": "MEUCIEZfLaMstF1lUvgT8SCIE0eu4YIWWpVU4kTGXZyEUkutAiEAhhKctwDNQy7FpmA4/jA4HrsvC6FRhmH05Boi5A0L+ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMqFJCRA9TVsSAnZWagAAiFYP/AiRZM3BqRwjBBQRmM9Y\nxqICl/f+1PdIQwmh26HxFban2TqBj+3DC2PjoLfWjbn/UePQj/8/bPoWIH9u\nJCVdeHV4N0ZJFTQBMtYUZ4OIFbDahtTSszRpDONWHaOSRn5sRr4MeJ3Rxga4\nSlCh0MFGVl123GyDpzWkZa8MANmBWy34M/Yga33hdMd8NK0wJ1VVWNTdYJOC\nKnjyxWQxYcsnHBj0Tp4ghuRWkTXaaY/T+l52s5ooV+UthevozcFIa7DsRqwv\nw2EVs38U9xbdRgBOT0j3wJKIIi9//rZiSL2iYAKmBcOiqXWnBGkJUnd44UIn\nq1QUjBb3wvBf1+7m99QuEn9tWLrjbYcmRBTQwgPqu9WHCPAJRNRW4MwvOHGQ\nJ68+zs5AsCIKwSKmB12vO/LJhLI1WtqlzHin7mab3rfJCeJRBZrEqSgGvN/+\niQQVC2gXooXkFzvBsCldFoD0X0ODB4aBD7scwJ9FeJ3JlifO8A7BGDc4pwCu\nc4MM+iNvrxFikchuOFYeX47zBvbQVXwTb6q4qZpdfJ+SHpDzluU69U5/MtWB\nqmnEznqhqhEyhcoJTbgZjpJo+aYTXO8UqP93rIQhMWmp5fFAjyl0cRDz6hCU\nhiFA4uxaMvKdBXnL+Jbfmpn9nfMFU5eFjr61ngIX3DNMMIigy5Wcu9rx6tMj\noG7p\r\n=o4QD\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1": {"name": "browserslist", "version": "4.0.1", "dependencies": {"caniuse-lite": "^1.0.30000865", "node-releases": "^1.0.0-alpha.10", "electron-to-chromium": "^1.3.52"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "61c05ce2a5843c7d96166408bc23d58b5416e818", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.0.1.tgz", "fileCount": 9, "integrity": "sha512-QqiiIWchEIkney3wY53/huI7ZErouNAdvOkjorUALAwRcu3tEwOV3Sh6He0DnP38mz1JjBpCBb50jQBmaYuHPw==", "signatures": [{"sig": "MEUCIQCKC+H/ULDvrNwdUeE1xBFfweD9HR/pE/MM7gRMrycvAgIgfD1Gg75Dr49PXellONag/Cmy4duMhLMskPoPFuMfa+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSeJCCRA9TVsSAnZWagAAKfYP/2G6T4Pq7ELTY7PtU8SX\nXr5lXpjVXkQRob8YfNnc+nMjLSNCyih6eqdX/H5v8p8FabBPAERz769vYNqi\n8HRgpVCE+AgFNMhKwo3ZSW5mVz30VuXnFvjkEbnA5OcOEBU1l+Ttx7L0ObWd\nUHaPc8Zcf1yf25isYvHoPKG08Kq4wkc7hQu39HcFCCgqxvdRobmHwA3Z2Nem\n8kPCIedcUJI7t59O4TJmUSkMYjFCVXtwPojxNcFkLRtrJa/aTi0exG/DThy1\nFM4Pmf+IPIouyLl5Ty5TdDBP1uMuNXgnPPmn5d24FAf1DwS0RLxckkkzwwbg\nxMB6OvDgapzjq5RJqKTD//opyA1ZxlxzANWunmxy6psjxXsfJ0rIdYntEd8j\nSlYvZ/DEZiBoDqllgeB+L206ginZdHE/r2KzmwkmhYWKvOgOfVvaL+qf9Kym\neb/qwq7Q1QTVxqhRSdAPx3kpH5o40DC1WvPulFwrp5yVPJjqcUZARB/2cGzI\nnzl5O/mOFychfmpZmhmd7+DSFwtNVFk/IiulmU+nzXcBpNQ+ofTW9Ff/WkjP\nkPaTX3tAS5/fiUlxOB1cgMEyYvBqc8wHS33sTL9+PMasM+lY1sjPu6Hhi9JK\n7Y6SIiIyRnvlcKE9HDkeGP3Dhd0GrJ8vR0IbaSiL9CBuWWVUa+2RwGaAMY9V\n9rkR\r\n=ULpe\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.2": {"name": "browserslist", "version": "4.0.2", "dependencies": {"caniuse-lite": "^1.0.30000876", "node-releases": "^1.0.0-alpha.11", "electron-to-chromium": "^1.3.57"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "294388f5844bb3ab15ef7394ca17f49bf7a4e6f1", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.0.2.tgz", "fileCount": 9, "integrity": "sha512-lpujC4zv1trcKUUwfD4pFVNga4YSpB3sLB+/I+A8gvGQxno1c0dMB2aCQy0FE5oUNIDjD9puFiFF0zeS6Ji48w==", "signatures": [{"sig": "MEUCIQDw4IgTvkKXx598q0gG6Ma+c3tIaagVcVcigvPgrFs5zgIgXEbC9YUmhsgooSBvjlam7ZldOVKhJzWHlcUexObezD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcOSXCRA9TVsSAnZWagAA/r0QAJy8eJTgERTA4CmPX4Vh\nDKxpMY6udeZemkEirKCBCOjyvu9swpW9+POsYBUN821jPWWajjrdM9RkIwD1\nGeeK6Mr2ABA+yo2iBcUG6pIL/4xYQqYqckWdcFkUO0hVzwWcgQMl2PBj5PJw\nO3TNMsUkaym0cDx1Izc2YZRmuiQGQHMho6IhJmpzTGT/OcaLWtf9hiKp4MfG\nzVWmriK/HAGX6rMSnltaPdsYKvwzrWQGNd5DcVniPCv77hKIOkKvSfMx28Mg\nG3h0kZd4JqeJGp+YXMRpGEHNHEug+viJKiHLKm4X4IED0waI95Vnn3Q1Kvny\nlftQvPy2tHse8xfwUbeGEo4j/HvF4YOwERPqc+wT3Ks7yZG2UTSJKbuj/eM1\ne0KYA+rO1UIGJGiEd6ie+5ElHL0kCrjf6LysAenBmy+eAb8kadwagVfSfs19\nd9fJaRg7xmuXPYIVU5fTzgPOKsLlnkp4+2mvu0LXUjJzkfl3favi1lkpaNAM\nLBzDaR3ETcOH8aJ7yo7MfvYIWBZb6ajWpoaZ/nFuOXz0oV8WNjz9lMJI7Q0J\ngSsPcLW/YuAxeqv4l116HufvVPZdDxKrQbXQoZ/I8mbaRxgAbW0RUGKllhT4\nsDculTWfsfY0OSHHYIDBK64TZe7YT996nmZGs2TRMBU0+mBts/7pU+urRX99\nAsyd\r\n=daix\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.0": {"name": "browserslist", "version": "4.1.0", "dependencies": {"caniuse-lite": "^1.0.30000878", "node-releases": "^1.0.0-alpha.11", "electron-to-chromium": "^1.3.61"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "81cbb8e52dfa09918f93c6e051d779cb7360785d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.1.0.tgz", "fileCount": 9, "integrity": "sha512-kQBKB8hnq1SRfSpwHDpM1JNHAyk9fydW8hIDvndR2ijTFKIlBPEvkJkCt8JznOugdm12/YCaRgyq/sqDGz9PwA==", "signatures": [{"sig": "MEYCIQCQ7Wcrp4K1eYDbF5g1XWif7boJ/IqUlOv2S9YQ1USdDgIhAKQxbuhtvWt+5tnuydBeYvtOdysSxQkHUn1uRVt1za7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfozFCRA9TVsSAnZWagAA4m8P/jdv4i4akkcTCaU7yant\n20YJivOvztPHbs8Sz1O9N3cbc3mmLm2byWD+4Olh3+a4m2DjLBW+a5ZQDIuq\nOFpR/63UtDAJ3rr3b+UpQHHOvG0NbI8pkjDLLoUFv5/p41rdTeRauf30pWc8\nIVAQGQR4HTG/fHN8vh5o7UcBhRlCPxyjnhNYbRNp/9mTSeX6qhURloZvOx/U\ne7EQcx6XMOORTNAubHZGJkXG7TaKjbxvqe/WctkmFODoyWdF5U3jESbdTbXH\nZnkWQbzdli0R8AtFIiOo355mWinw4zBuR0UNOwgMZZhXWFw5AdkAjkzhrck7\nLYqbxz9X0wtrcqhM0lRC5I2Zp2RYxmPvUQvxB4+VMn3RhVM3lTSpKGiVxfWT\no9iekIkBZ4gKMBlJHmWfJebY63Z5gsb8/BaGty1+BnTSr2/Jb0M0hR7kMryu\nZsn9Rb+SjbC25WBkXzw8rd1C4fAs2JDfHaWaOivuoslm7k8vIO4db7CCEHpR\nskpkPgiSP9dzq5Tm9t0hA7arXMX8eUmLEADS23ASdMe/lMvm2M6874UVlTUB\n5I3pspZsE4DTwn6t+g6VZwnjBXaEUUhVyTpaZInJ8CUujTdbQlMiUz2+MS4Q\nnSeXX/cx5jR0T9lr98HP9mLrGSVWw4QVZOZjc1cBUJYvNd7m8//CYYqvkW/r\nqRKX\r\n=Z0Ao\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.1": {"name": "browserslist", "version": "4.1.1", "dependencies": {"caniuse-lite": "^1.0.30000884", "node-releases": "^1.0.0-alpha.11", "electron-to-chromium": "^1.3.62"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "328eb4ff1215b12df6589e9ab82f8adaa4fc8cd6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.1.1.tgz", "fileCount": 9, "integrity": "sha512-VBorw+tgpOtZ1BYhrVSVTzTt/3+vSE3eFUh0N2GCFK1HffceOaf32YS/bs6WiFhjDAblAFrx85jMy3BG9fBK2Q==", "signatures": [{"sig": "MEUCICPZorp3cOj0Wz4dGUbuLIGLKKwwweU81wSDs+6DrrqiAiEAin5rqlEscHQr4JNhvjpnQN2R84Lcrbfg7dyRP1qrvTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbj4+5CRA9TVsSAnZWagAAEvMP/RMqHQAFmsuUQQ55ZYrU\nrCLtRKazA//LFzNLmunzAyfWWLGAdKg/VbdqRHQmXuRn/FhC8uoGm7u3nexd\n4fTetJ8VdjTHsyadd3P0AvnVHXQxwMfMS/jUJR/6Xr9bk0XD1QfSHKk9dMMV\nkNEfO6cxSretQY+4JZgQpsYms1f4ZPUpAVgC/1TiREnT8SnZI8yjVNCs6xtz\ndZ3YacwKR5vsnC0Qd5hJNgZ51fc/O2xfXvc0qxMEpzzeQD34vpMYEzGPfJJh\nzjeBvkuYYbcWJrAB+qDS3FZYirVkDPQR0HdmrAD9V+WT0d7UaQxCEwlGWW9t\n5f5GAPkgOUnuUgBu0D1Q4bo6WJK8iJWPe5bZH0qWTMBbV2Ju8N+zbEE+Vfmn\nqmsY3byXSJf0gSirPg749wIc/cVbBpI50cNrLh0biap4BodTjrstUAXP0a9V\nE2fhVZJKkewEXCjnqYRYXj+YR++H6bNvfdvjPrxCtdE8v4mEAgFwu9zscVTy\nf3I7hC8TYzT/DVHmcpdupMi/Y+7Ap2/6sclJRdSlzJIz6P2jDrrd7ibS1q0I\nthyEPGcoyLqBu7GXVSQm6Tybsg11p0cJ2N+BEm51ySmoZeVTEi9j+w51Itod\n3dIC+k5NVtyJuKLOHS5TvhKxuh55DgomuRZEf71E0ZiK2f92C2RwbEDrWR3Q\nkcEp\r\n=6Mvi\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.2": {"name": "browserslist", "version": "4.1.2", "dependencies": {"caniuse-lite": "^1.0.30000888", "node-releases": "^1.0.0-alpha.12", "electron-to-chromium": "^1.3.73"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "632feb46d1cbdd6bb1a6eb660eff68f2345ae7e7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.1.2.tgz", "fileCount": 9, "integrity": "sha512-docXmVcYth9AiW5183dEe2IxnbmpXF4jiM6efGBVRAli/iDSS894Svvjenrv5NPqAJ4dEJULmT4MSvmLG9qoYg==", "signatures": [{"sig": "MEYCIQC3VXJ+FS2crQ03wXbL8SMjN0fiZhXw2AdTK4c06qGt3AIhAIvc/1LWi7ba+9YQUYoyfa3yjRl8wsA7MKuzGqXg5j3C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsyzhCRA9TVsSAnZWagAAXb0QAJAAi52Wx4NFgaFHQCq4\nzPsunLv38G0deRqIca+kg+lD/UCxQpVF/RRlvVWwBWaoFJakqbLatrRiZGpr\nq/+lSMXz6VUcL4SHQWql7OzpfVYmo+qPO1wk27/eMrfbp/0t0ZKMy2u3XIXt\nkGZC7O2n8AKHPMHmO39MsGUh9pCOeEPawSCN+Yc0C6T0xy8LthSo+zHHjeGT\nntzi6lm895CIEGMypAFb8iQ2mMZcnCv5nybPiYgULipmuAAd6BqWZAXwVeb1\nUbUrU/hrZoM6Wb0G2Qbiqp3htM/bhl25L77GX007kh2MkhXSCaOmGbwC71Xy\nwNs3FqtzjFJs92j9btSbtk4RosnYBdCt9TNGim4QULGgW+A0Ch1j+odpprxj\nwZjlwrbGBN76TkWSwRwN1hXDoJlDwTuOwNXYg9Te+kr/pGNySLhLFkpnW8/F\n820D6uY60njQ5SIvzjFplCLlgaehApumrWWmIT1+EiDetHjLOColjvX3xHe4\nPkBzFAYo4/ciUqJ7m4TawoOntiXHWkUAfkNB8YoGALvnMog+vyljGQE8hDGD\nbL045F8MRNpNqQekG7nMFKTeF7/1NpIJB9uL20vfAPtH1NJE9pfnsrpU7vtM\nta9Yw6drnT1M+S4ZXnWOdcBq+aUkoINWfTQgS41rT7+hKnBJayzIqi2ItQMq\n/Gqb\r\n=W+iY\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.0": {"name": "browserslist", "version": "4.2.0", "dependencies": {"caniuse-lite": "^1.0.30000889", "node-releases": "^1.0.0-alpha.12", "electron-to-chromium": "^1.3.73"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "3e5e5edf7fa9758ded0885cf88c1e4be753a591c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.2.0.tgz", "fileCount": 9, "integrity": "sha512-<PERSON>rls1CHL7qfQz8Lct6QxYA5d2Tvt4doDWHcjvAISybpd+EKZVppNtXgXhaN6SdrPKo7YLTSZuYBs5cYrSWN8w==", "signatures": [{"sig": "MEQCIFE/MnBiNiL1ALbeqIBWh/d+h9BLk0s5fgjPU67h+soBAiAp8GgZzb8+LVJATXVRfclYDTxZpTctKqi+u6z4ixKC3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbt+DgCRA9TVsSAnZWagAAU7IP/ji6rWCg5z6Xr4LFF0nM\n6JpHNWXr1dgq/dz5vFNbfOw032p8zEUQYS9IW+OXOXR6EvFga7ClgBhsvURJ\n+e5VkU4PEXofhzUKnLy8osfQCCM2M1QCB6xf8Zf/BYzPQaeZksgfFPg2Xe/Y\nWaS6EglYlDuDI+9sj74DQ5KGOAN4f9Os9RkTp9a/nukh0R3175NBA7rB7a6R\nDsHw6oisVt+sd54xvyozMJUsUx//H2Lc72is41KICr/NBVIz208azaOa0u2C\n/S/e3l05pJPG1/ucmJALZnXVQWXF5tLcVMkO13qKkWEAPDR8J5QlOLxxA02P\n59UU5/sYDPvBDAScEj7jRbZtx8OL/eb3ApiqrpuI0Ij0HyrN42X2Z5fKynly\nIhvwFSL7cEIOGM8IHjCpdXrNrvhVc/bBx+A9GgCwckUAQwBwhH5EjkdcxG0e\n47zuApFLQblHPjXw5fsBi8BrNJTUM7Q/J1jrattTH+AohjYGdEMjpoYtH3zA\nklIfsL/XAwM6sV4gkLuFb2cmYISyL2q8N0+4ja9xGfY1NpV2XGDHrjI5I2MZ\nXr4hifsNxY6SG9Z11ntAN2U251ic4TskOmGvAjY4uQDcRzgBBoVuYnKXvkI9\nyw25sEuzwqy9BHxwq2fFLye+GQ4BhLKW/X/xpExW2rmFC7QYb6LRLS33b3kq\nD97R\r\n=olnM\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.1": {"name": "browserslist", "version": "4.2.1", "dependencies": {"caniuse-lite": "^1.0.30000890", "node-releases": "^1.0.0-alpha.14", "electron-to-chromium": "^1.3.79"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "257a24c879d1cd4016348eee5c25de683260b21d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.2.1.tgz", "fileCount": 9, "integrity": "sha512-1oO0c7Zhejwd+LXihS89WqtKionSbz298rJZKJgfrHIZhrV8AC15gw553VcB0lcEugja7IhWD7iAlrsamfYVPA==", "signatures": [{"sig": "MEUCIQDZ9Luxqhkwp5AR0usud1z4QAn8PXi/SfZGYknhaf49zwIgG2+kCECuf4SPq6Mnf3HUUpVxzIE9M+9qM5DyOovd8/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwn3YCRA9TVsSAnZWagAAKJAP/jr/qBW8a/hmXEHbvLkS\nRaefjR6Sa6qbfqgK1bhkh1zVePbAq2jrA+dexTqtqNfkfVdeFdqqvkXv4tiB\nNHX2PVNwFSx69FRaaqSx2g2RKt0LQnWPsls4KoMXvRzWW2tKoxigz4194ZeZ\nyY1neh/AwsYM2SNFvWOfMX2iWX4L/uuOYp4ltADn2ZuyWVlPi13qsPRHtbhZ\n/w/Ldn34sIIKYzX4O4mE8p/F2MjgL+gUccXhpUi3LRKLY2ve3GSegbtWezzL\nfcfUTvwpvgAETtsf3DwZ6NLuaTm1cnA2TIao//D7JaVBDEZ1qhfisQ9zOlhO\n6NkGP1hbgK9oJFf6sfKZ+xfLLkNqU3kkqdILV50MdDGzqr1/46do+PKYsS81\nMowH960ogWTYPEtuoOXo2gBVJJUft+wGTJyPfsRANQ9Dyc18d37t6CaHfBHW\nDZraz1tDVZEtcy7pLFRfJu0YwwHVVxtlnN4W8JPQBLUTUki4uqA/Yf/uxph7\n8cQkfuArzLb1KEH+icxiBBkU4q7pkpnTj0j6nZut3j2ZoDQnPYzeAsdk46rY\nunkVEBtDr64G5ycPupxImz5Sos4sCnlV7Wtf2PbaYOsiaZn7UFNM9RSdiSan\nJFsMhuomoK3wWD1SWz8UGCkzPfoRo2Bim7tOZc3Lgsh/qI6cwLCZtCYguB5v\n2Xk2\r\n=Il0B\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.0": {"name": "browserslist", "version": "4.3.0", "dependencies": {"caniuse-lite": "^1.0.30000893", "node-releases": "^1.0.0-alpha.14", "electron-to-chromium": "^1.3.80"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "779a235bd1ace0f3841a9b294df4cd47ac046c9d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.0.tgz", "fileCount": 9, "integrity": "sha512-j0jLqo+6ZhFWvTjEIcDyR8LIiN8pA3cUrT/SGAs0LPp/cKvkRpCnzuxtnAW+sOPLTic5wfb+TQvRX2RTN2wo4w==", "signatures": [{"sig": "MEUCIQD+YX1JExWNIrAaNegqVzt9KrrYzz3ZPbL8tvVruoxv8gIgLlJ94pgD39suZlPBwNdRGEN2ElPLmPHe29ptz0Gbhqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyrhPCRA9TVsSAnZWagAAkNUP/2GeZGd3Aebybt/JNgib\nP5oxz2aII20PcgnGptTiKQ64ItfQmw2vaaJapprWV7NosxwVZT3uTRbM29AN\nMsQFh7++ybPIt20wfUbZROv9ZIHRowj4bYMO2wdRO4B/XI7Jxtt5f7tqTd8b\nsZj8hvgx1eYfFd0PuYsNP9rXj0D3Xovl20fuWmlMJZFzZoHDwJ4qFy6MGB1m\nchKB76RG6gnMsBdPvmWihYvUm1YA5oiLH0uF5/QsR6ks0pBv0GV72I/ebsHH\nkN8USZpZc4PWQu7LyAAyhZn1G1qjvJLpSl5aeAi3BdIuwrUlyDdSsI+ceJYR\nTZeX9qDuzn/EYnIvLmM7LbFR2Sd0lkfr2Nx1GhL78bRxHFY8f0HojVlvdnrH\ncOR5xeyhQ2FHAOthT4n/+fWGh4zIYILQ86cw5NWUB3QN6ZI0LY62fuBwERWY\nJQuEUx9DUidEe5v5/H229P04uN6rJZe6cGA4r5uXsBa8XvZ178ESogKrLFZD\nj9IC0cgb7sh5kJEuZmXSEr7S5UPrsJVwRxMVnY0f0KD1AAS4cRMVWyvmvD0X\nb6NgN/NG1BcCxhyfYTv1cXZGwt/sQrdTxDxcBz9clpgQsPdHghCmJg8UcY5B\nawBxMzlKycHOmWRFmn4ahXnKesoEMfYYqrC9TBk7j989vpdzlGbMtm8WJw2S\n04aj\r\n=2Ra6\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.1": {"name": "browserslist", "version": "4.3.1", "dependencies": {"caniuse-lite": "^1.0.30000893", "node-releases": "^1.0.0-alpha.14", "electron-to-chromium": "^1.3.80"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "453e46e74b0663ec5d764cdac3e5ac52bd776038", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.1.tgz", "fileCount": 9, "integrity": "sha512-rlvbN4EERDFJcwI8qzmRz48a1zqvwE4L0G4d05EjH2nlswJqBxCXByafWhhCpVrHOsnOrDMtVjibPHdBElb8sg==", "signatures": [{"sig": "MEQCIDR7Y/2NQngoSYpQyD3CZ99rQrIRUZ7ApYrdG0UQGPG9AiAOzOeNkL0k1Il1MO6LZcqURGdsfnBytZOFlaXH2T7Qtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzUVeCRA9TVsSAnZWagAAAB0P/RKgCSNgZl6KOE2bnM2F\nK5r4T7S29VCR9l5HXXFnNaHAJRFQoDI7Dz3fMrRdtCDxca65HCz7ZjG31wiv\nyR2ZDGYvrbnh0hO1yy2bJ72Rm6+sMY3w003B1Ngql85nFTZKmsLC0k7MjRdq\nn+W+sdunEn9s3Nqd2mnMiWdDil/+KvfvnGBjNkV1uPbCoybAVsbM6TncGzyh\nsEBgfDTojvyHaeTuPvOTvuEXcUjnAJRBO0eyCmZzWkA3Sh5AnOEcvuCURMk+\nK5eAPt6gw9Jz0p7IuUl/U1rmQPM2ILzoOHOFqVrTFObE0qMq5mFu7VCr8mzH\noy5oGFDSKCSywrZrZO5NvZJo7X3mCrhnPfmExJSy+APap+dM3jOEU0UqYhTb\n3Hk6GuUDZO3l/1Epqsspt0OMpbAR4e5tBMz/EidDZDV0HRGRekN2HJvp/6nT\n6gnTEl63QPDIxBZNggtLMO8OUcls2vN/Eqr7U4Ezdj5sJdxRiT6/KJa9MlNf\nTBbSF4/JJLLgDog8bvipdClhWOAi5KI8AtaF/2LaK7O329aEVdLMLY9my0i0\nbdtoFS/1DO7vPZn85272pJ7hQSgaRM+E8XAqltbiFeKbUB6FT1fQZayWpV4v\n/6e+Z7vTrlH+ieiH3tXJjxC8R3WUN13eYcUF1QlBEXPPo9GN5Dywmp59L+hK\nIVCF\r\n=z/CB\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.2": {"name": "browserslist", "version": "4.3.2", "dependencies": {"caniuse-lite": "^1.0.30000898", "node-releases": "^1.0.0-alpha.14", "electron-to-chromium": "^1.3.80"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "a5479f20fd387357be2d01aefcbbdcaadffb4d75", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.2.tgz", "fileCount": 9, "integrity": "sha512-wg<PERSON>JWlYcDvsjRtf8socmAHf1nXq88KrQLB/gMYHGPUc2bzPWsgltSXwPWYHx4Sw0G9E/XGNW5wJDaWlpHRMpjA==", "signatures": [{"sig": "MEYCIQC+xUROL3D/maZBl8REuvmDrbxwQxsXxSxq034SklzfYAIhAMM+FrkdImg2SW2WeSyyjIzAs2eq4mnfbf4HariJC7Y8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuzcCRA9TVsSAnZWagAAM6gP/jxubgFc2ZDk1kFBNsMO\nj651ieLVTPxtgAokbfWePSvRbl9nWNqyZmGQPmbmjhz58U0hpdqiftfbXlrh\nF9fwVXPt4yu6Zwl6cEeN6uMFg7Ra8OQECZGW1WPC7VW/fLhUFjbKI65EyXeR\nTGVbiwxFjEbhvT4WPEIH0kHvQRQZZwYC+IJez73Qhg6j6dr2dZY77EyoeOBy\nevD4ZVqfA1cEWFic/GYh9SxT/sJZMyf9DARJtIOM1w+crDgsynDV1b3tc08L\nII5d2njOwzHGmFSEEXrBshvcQeBAt1/7UNSyCZxfYijqaCYhlTHa9lvdqNq3\nnNKMgHYeSVD8QZZxurItGNEz36cfK011oXm3f5Ar13pKTa+ZIoiOe9m9BKY2\nASLe5LrWSt+Aig4vX8Xn/JmTYJ7n4gpIwE2JkmPDymwZawu0un3/+b6uaO+b\nZsNkBOfbm1rl2mMo0FQu+66U9DoKeCiDE+BrZELTi9yTBDQTh8wqHm7BLRQ6\naUKMLXm3CpnFwY5oL36TybFYrL6pgW1ynrzK14roWWjnr09j8ffjW4ltLHzn\nDO/FDySkrsX3VLVUWfI8XHzmybklhGLTf6OqrXa/bzKYnX4DMJZWMTMveIPy\nbkUDAhAQtHDoLFjoPiyEoy4AkiIfphflAlGEWtO2hzLUzNzawuvg5Ls7W4sv\nAn0/\r\n=6eM/\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.3": {"name": "browserslist", "version": "4.3.3", "dependencies": {"caniuse-lite": "^1.0.30000898", "node-releases": "^1.0.0-alpha.15", "electron-to-chromium": "^1.3.81"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "88a7d9ce2e5db561e160ab660bc59cb406a0c41d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.3.tgz", "fileCount": 9, "integrity": "sha512-6h84UD1mmHeuQ9IucX6yzBc+KBYcBBTLYt2CXtY7GYCra6iE5kOm7oM+zuGw/0tjGtbJxjm58OvxSBmogEMCRQ==", "signatures": [{"sig": "MEYCIQCB1/QElLPJzRPAYbP5jRfqspXWzt9WyIM46njqFhChggIhAKbQ5euClWwOrzrw+ugIiNzL18chTojPBFIHoaC7oNLP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0FiHCRA9TVsSAnZWagAAm+AP/RULPWGbGq6jZ4MtMKoB\n9kKd93HApnex2qor+eFo+hIvkG9pUZKCjBlY9xABSk/FBye2uhHEXVNFIIVZ\nJi3NiuPeppnsiMcz5pnqrd1VVwbi68WSJ/dW6um1me8ZXUql0Bvhl5EHFRfW\nTqCbPUY8xhilwksrzwI7yE9cu2DswhAUsZS7n/jMDd3KkbFOtw5o50/iSV+m\nqPErcVA4daNFsMFZ6ThILEODK7367fiUYD7abMeJpuK1b7mAoVdWuq2ifg2E\nLxzWXiWJstFc/UfXTcxl1tiP/5IfRHK9WoIt13eeurD4hVkVg4KV5+oJ4Q1Q\nYG2LZ8rKkkktjDZyFFgQhUr8aBwY0GIyvSCPYjCv/JC65iIHP1JJuUnazAwf\nHXeLIcI6r+7KuDU/ikZvWnLVGmpDDmldFC0jD8eLUmBIx7Ga2OjozKgZJacB\nvPlu1elRrvK0nQknQ6RHdMQgGmp2HEbMy5PliiKrLRUmfosU4KPPVzClretw\nXegE/ZZGDTgsJQpuPMyxubjDvG87dUDgeD+9jmQdKoKX8SfsmFdOY4gpo9JS\nKGMJaOSmZc2y7sos3S+94SMHbAPFAl3f8v2hlYSLElA+OgHh3LmOR9PoXmTn\nU8+Mc49INBZ6fbczvENC7Oc7ukgJn7REX5qdzNmdkLdIVMpuWfAXbirM+Sro\n6xXc\r\n=QiKx\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.4": {"name": "browserslist", "version": "4.3.4", "dependencies": {"caniuse-lite": "^1.0.30000899", "node-releases": "^1.0.1", "electron-to-chromium": "^1.3.82"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "4477b737db6a1b07077275b24791e680d4300425", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.4.tgz", "fileCount": 9, "integrity": "sha512-u5iz+ijIMUlmV8blX82VGFrB9ecnUg5qEt55CMZ/YJEhha+d8qpBfOFuutJ6F/VKRXjZoD33b6uvarpPxcl3RA==", "signatures": [{"sig": "MEUCIAVXeGyyh8IvIcTkMMPYxmaG3kynvfqsPA4pAVds/e+KAiEA4wK5XhJMvAgyUfaBXyODPFeTB1oiqfK9NJF0DlMyuF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1PmBCRA9TVsSAnZWagAA518P/jiakX/hqUSFrdgPkFyY\n7bxMM4jOUetwRi4/NiGK4hzw58hWpjoVI0yz2ZHar1TextufOgXm9IqaV12T\nRayZgBPh4UBjY2ykBvf+z9E/ujj4makWVK/fessBNMbjZrHnlXDpYal2A5yO\nwLvF25egtZbs3NPhsnHTx/TLCKxgq8p1U4ybxdLJJckNLpMw079jKaQrG9xd\n+olImOQVBjA0bJhVLRqwt067gG5CRHWi8rvhB4SC7lQzqRGrNu4repIbHGWg\npXdx/9uRldiNNTayaXo5WAYVXhFOhjF5K2YPS+hLLv0z2Q9uCSToiGwYsG+x\nh/1jGtQPBV+YVj6WZwt36m46d7Mq1vs1qaa/CGiT6HfXYbqJ5JlilzviecNI\nJUk1wNKCLnYU4mGAUqe0lKFwOqb1bTJPCAYT+MA5LiT1jJecU2+2AD6esZrX\nDUclupJiye2GOjOmTketwZQoJ5JF0IN5ZoF6nNRQV7Y52W+ZNltfe8mr+mqF\nYlF+BoMdjC7lyfIQMSk05Y0Mjgzsk8VvMb5529dD97YhqwZ/KvBfHoJ/yRim\n/8gYEk0Yt6d3iA3/MqqlOsOsCC44atdhZZ8c4wplrt6knmrEPIyHCqDSa7RU\nGefcPS5qS8J2UpWN+5zZefXiKhJmzkPmROi1lYLBSPv3w1UWfJIeoffwwI1r\n9EJ7\r\n=RJJY\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.5": {"name": "browserslist", "version": "4.3.5", "dependencies": {"caniuse-lite": "^1.0.30000912", "node-releases": "^1.0.5", "electron-to-chromium": "^1.3.86"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "1a917678acc07b55606748ea1adf9846ea8920f7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.5.tgz", "fileCount": 9, "integrity": "sha512-z9ZhGc3d9e/sJ9dIx5NFXkKoaiQTnrvrMsN3R1fGb1tkWWNSz12UewJn9TNxGo1l7J23h0MRaPmk7jfeTZYs1w==", "signatures": [{"sig": "MEYCIQC0Vi5vjBpBnd6liS5xRJYuUEGHVavCgI+J+BxnQNkQUQIhAMIsIbF962+TwvDsJlIldAccyURzHgRLzRAeD70hns7K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcAiYSCRA9TVsSAnZWagAAO7IP/2dBjtOAUh3CNmT8aju2\nWuSzQEFBn7DpeYkpsq+tO1pWkIT0V/BLHp46dbVF6IN6x6t6zBqgMkj2x3+U\ntlPgnPpdQa7W84qK4arOQkh8uSo2AuzbEEa+2SOlzT98bMLwKIYmWZgwlc90\nhp4pQr7TFnXzjPxURqlp/31nbqiGou2N7SRR7fjVqKmu5U5uS9fqK2HRaosq\nNk3VJ0UakiOUUTRvw2A3qhAoJrb5SGx2pzytiH9bFF5vHLyyonipYjpOjk+b\nlYNHsnf6x0v396GppP9lqtbS/DXQmkWyXFV9xfs2oDqLxZOr8AIJli6KefsS\ncNRCRi6YC8JdTBcubbVgfJVjRR0WIE+EORk2JC+GFge4pbccYo7t93P1amRF\nXA0a04seH9H+qV7oHMSNp0+/HwcUdRfvfiZcU1ABLW/oiaYPzqpEFtEMn/6l\nzehg6z5TlKX24dFXR6PJQMvXhVT7P5cJZQftVkG84q6liReydGywe6FoQnNZ\nyITv+iBKb31AlZcSoPzMms4563/VzkZWkUwUeX8JQvHSXGywHMOl1WKFi4fD\n4lOt+yZPK36q36Cn4n78bTB4e4S6tNdRhyECptO1N/kW0QDXfL4AVhVmLnSF\nWZB6zkHxEb2Fpo9HFc2oPjOEdh4MCgR1Ef6U2uQJ2950Qy3p3tgB005eGn3o\n1Swc\r\n=ZhD6\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.6": {"name": "browserslist", "version": "4.3.6", "dependencies": {"caniuse-lite": "^1.0.30000921", "node-releases": "^1.1.1", "electron-to-chromium": "^1.3.92"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "0f9d9081afc66b36f477c6bdf3813f784f42396a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.6.tgz", "fileCount": 9, "integrity": "sha512-kMGKs4BTzRWviZ8yru18xBpx+CyHG9eqgRbj9XbE3IMgtczf4aiA0Y1YCpVdvUieKGZ03kolSPXqTcscBCb9qw==", "signatures": [{"sig": "MEQCICMLwXIhQfb+E4t0XF19z/07leKrSw0LoFhNfIQvvmWUAiBRCs2MTPZs7VAX9uc3rmAOOMXdThH9SlfprRp2NeWPcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFFjlCRA9TVsSAnZWagAAIZkP/AsU7rbeUBCKuvlpefX0\n9FOYJ/woXNNhmCWc8kaG8HTR8YU5DTgH9abLDnWznot8jT7dta3QMyU4MID0\nKP4fPrLxylVGTOcvIgLUK0BxM+KEP1leBAkGEtPm3ILbbmAUsPtLAaIBS/0c\naO4fmXATfKC6tncd9mum4giwfmBdLFHiLhdj9W6EDnOD9RapL02qx/xwhdB0\neQUftUL321WLXr4YXH1As3NSWsVKLV/aC0+zKGGaoKC4dt80zGFkl6WjYA+4\nUyn4Ez5dffTZaDiHbtoLpdx5VB4t4Z4yOAZzAuZp3y7+hbXUBxeGCpTLDQqm\nOCn2hmlQRfNYwQc/aRkrLwsAmcbx14TQeMPQhQ9Lt07/ZgDPespiFc+W8jyv\nFB/zpxigIDtzBAnxvZNBWRKZrxbIwo2DrVWkFZz8Q23UEi82I69bbgvOVKKF\neRBm+pggAREUaZe+7Ev1YkflfRaIwomxKCK/IioYtl4TDh8r7UhQNl3Uj+nk\nc/YSnGpCRUMz0dlcB1JkvsIfosNLJ2Y2nyLtJrdHHxCXIcDuezOZUktJMCgZ\nIyAWBkRdt7/V8HnnTwHC9LFn4DJFJ4/lJy4hpZW/39wZWmcJ4oi+YSoufo0N\nDIXyCnXh5D3CJMadYHZDbcFoL9D6aT3IY0zuEjPAbjXRTDFuNc5hjYU/Svj5\n8r5v\r\n=Xd38\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.7": {"name": "browserslist", "version": "4.3.7", "dependencies": {"caniuse-lite": "^1.0.30000925", "node-releases": "^1.1.3", "electron-to-chromium": "^1.3.96"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "f1de479a6466ea47a0a26dcc725e7504817e624a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.3.7.tgz", "fileCount": 9, "integrity": "sha512-pWQv51Ynb0MNk9JGMCZ8VkM785/4MQNXiFYtPqI7EEP0TJO+/d/NqRVn1uiAN0DNbnlUSpL2sh16Kspasv3pUQ==", "signatures": [{"sig": "MEYCIQDaUPWdmKBjinc4ijYrF7l1s2jcTfXT9jwPOcNpUtQBGgIhANa4ztrCI1VeUiDD0le3/hEPGrFaOe257PqmuqKESWWP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcK9pnCRA9TVsSAnZWagAAeI8P/R0w7zDybtcwEok+Pkxv\njQE1iCmVY6pzYiMRyc8au8AvKI8yW/6rqyclwur2q0yrP8pSAC8fVjyVAiuT\nZjTZeFRoC+jswvSNL7XVIzuYNHNqHZq4ok3spdG1u1NqcaVCvzBicT/fJCju\nw8Iz3RVEPpFvzv3hCYX52Ybuz5R78RKGBBURX5QXN0+Zs/vYZ/wrIOcAb8AG\nCeV+/jCj3zmVAmcfR01sqbsnJCQmyiRaH+0p5cjssBLr3PJTFssyQrCIQUx5\nyKjeM2R33N+MksxEkWkb/ugIaxwQ3Wo8nZy345la0aulAdzl/yxh9HLRRbx0\nANI5JTsZrzXYR9Ea6WF4Fqv/UrIIfQKlv69vcwfg717ueCJ+VYZrndiBmojw\nV/PxlhLT03vvIjjzuyK2yImUg93W9VorPSRUOda3EBEfR7ZOW8ImGtdzy+Ph\ntFT0pQKtgRLnC1QAHRGvYmTotRaa7EJAFFIyqGDyTKgPBb2ZvITDXuSIigw9\n01jh6A1ak7K36XYUtEGHjGJ5iqDA8InvkhyYtf4/6sNdBbGfsmLFTuMIB7AG\nKLytbl4WYjitQW/zINwDRqxOPxQVpRzY+kBr00pNm96knjnEMHy76Q6PC6cG\n9wREuYIE+VT5FkjgcfzaJYhzYbWuZu3Bc8dLPHKfDIRzXmfHXV5qPm6cmQuY\nAW1o\r\n=PFAg\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.4.0": {"name": "browserslist", "version": "4.4.0", "dependencies": {"caniuse-lite": "^1.0.30000928", "node-releases": "^1.1.3", "electron-to-chromium": "^1.3.100"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "7050d1412cbfc5274aba609ed5e50359ca1a5fdf", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.4.0.tgz", "fileCount": 9, "integrity": "sha512-tQkHS8VVxWbrjnNDXgt7/+SuPJ7qDvD0Y2e6bLtoQluR2SPvlmPUcfcU75L1KAalhqULlIFJlJ6BDfnYyJxJsw==", "signatures": [{"sig": "MEYCIQDNVhREa92c7LHJu+tNRfrCWYRXY+dslowsmErPS1DaeAIhANFMWij4565535+gy6kadkYinnqAsTXTkkD4CV4pDZqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOO9dCRA9TVsSAnZWagAAni8P/i407QVv/E9PDU2GFLwJ\nshbVZ7HawuOyJ5xzijzVQByyImIeL3SjdTy3Y3A9jU1aiDx0KwcmxxTcoj1T\nYRFJpQnQyK/B9ykXuMC19KpdFyj3AwaJyjMpbPmpNlIYuRgvBiNHGfe4qsIj\n6mfGGOrV9Kj1nRoIk8GLnzvNcuwRSy9h+S6XFLxSQHqPFG6biv3mF4NldQP8\nGcxfX+7AzKriAEqCdPkT9WjkGlRUqtYyDJSngtJeEKcDBZRUDke36cQ11fof\nIUINhtiyovlFxtGYig6F1Uw7gxQyy381g0dbBNfBjbncwAlkFp4dRllB2iiA\nMkJz5oSj+IuzYGdKuiXoET3XEct3TMHBtXUAyDqyq2/oVmrjcT3IdkgJ9k/s\na44vdOIqG6IDy5yPMTHG14Cv/kRWM6jJ21QpRJA/pxmZXKJYbKJ/75nwJrFI\nV+LWrbVTck7LC0x30T1/HhObnRJmmimUjYQEZippHyMa34ApAj/VucokMRpM\n7VMMXo0UhyyELGMmRUtKVZ28nNiY27caBlyXZNdMDaDA37swceMQUhz9ckNp\nJYrz3Z10DIxN1gWN8Otp0PIGoHGXtoBWtwxXmQqADeLCs1LtjQVj0P/EebkN\nHaNfvf+tMPLSNsWovnqVU3Yy/wBNBFz/Iqs/WMVOAPDhONSbiIUqElouuAXO\nzLgP\r\n=JH2D\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.4.1": {"name": "browserslist", "version": "4.4.1", "dependencies": {"caniuse-lite": "^1.0.30000929", "node-releases": "^1.1.3", "electron-to-chromium": "^1.3.103"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "42e828954b6b29a7a53e352277be429478a69062", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.4.1.tgz", "fileCount": 9, "integrity": "sha512-pEBxEXg7JwaakBXjATYw/D1YZh4QUSCX/Mnd/wnqSRPPSi1U39iDhDoKGoBUcraKdxDlrYqJxSI5nNvD+dWP2A==", "signatures": [{"sig": "MEUCIQDBv3JTzX7b89akIxSazps75YXO1Mj33BWTIkT/43qW8QIgI5BJAoyQONAHSIJHsqucKRhvnpL03WwVsitaPx2wIdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQOABCRA9TVsSAnZWagAA1/oP/RFaIxGLr4QuEcxX6puN\naifvd7yVZ4xju9TShKqSueTooRpXp1l5B0F0yeNLZnr5yUXqU4/Gy/ml6AbO\nl1K/1cqcg+zge+7NMf+Dvyu+JBfrK51aRHUjLa2mZe3pq5nUIhmS0mC1vcIk\nIilQhzNbsj8zMkTWTuyBHCNRKDO2ThZkszzXW2hDWC74JZP6nUBvMwlRWF/Q\ny23puGl1BA2jE44T+fWU6PakwmXUf5DJgoQVlYk89O1K2u7P7iTcISt234u/\nGv1mNYIOl7f6mi2K90kaDYdvKs/WSDsDreTvZwA0Bf3bUNSEsIm1wzjepQ+t\ntnmKc5vn3T0g29ynOe4ZFfNh9yQIpI08YRACUIJr5F/848GXns7qvl3mExmJ\nIerlHC1wy3jrU70kQjzRSsr1Kctljsp78f7q3nktWEEi5xV3U6F8ofaFFHgt\nTnWH06GUIqQmhE6TYgyPgg6WB+LnydDHNFMZvezx8T3LwhIZ0D6x1cRY4Ir7\naETP3b1RqikxQGdR1SP8XrnFnf/vm4slCLf2ULv+kVWKmjYKIKfSie0xFmqp\nTuI+nQODTJBxKt/G4D3ug6eohDuG8i5L1U/s2dteQUq1ye+0aIbqdReyh57L\nXpecKykSAAHPNSqSiDO7mRBEOAHbs60LgpnUfYigJONCz4WvRoWnZbRim64w\nnWvd\r\n=HRSf\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.4.2": {"name": "browserslist", "version": "4.4.2", "dependencies": {"caniuse-lite": "^1.0.30000939", "node-releases": "^1.1.8", "electron-to-chromium": "^1.3.113"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "6ea8a74d6464bb0bd549105f659b41197d8f0ba2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.4.2.tgz", "fileCount": 9, "integrity": "sha512-ISS/AIAiHERJ3d45Fz0AVYKkgcy+F/eJHzKEvv1j0wwKGKD9T3BrwKr/5g45L+Y4XIK5PlTqefHciRFcfE1Jxg==", "signatures": [{"sig": "MEYCIQCkJcB6PP/VuL+bbL51NpdzKdVnLKx6dbOg22DfjgYqmAIhAPztPb49n77dIQV0eu4U5D6w1oSuqiRej79iymBpbTUO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccYuTCRA9TVsSAnZWagAAprsP/A2WHvbvNbXuMkvnQr8N\nmrhewldvQWFcZmlRCBWS2RieWZup+iALlKF7FkAm8GeSrbQyFVaXa4tOeJuW\nCbK3HdQf/ox/Z8eWuoc2/1HfXkjLVibaHyHM6n4WgI4RLqwO353CH6DBOf5S\nDg9qOVKXV+KYB9wonNcqxzRo7b4GZM1Rgz++7OmpYxRGiIX/LnoTSvOa3Clr\nVEVG966TaG1GURZGHSxQcQWEpt2p3TWoC1vE4mB7vNVdyqIXHfVBZHlxzHwN\nBATjQxNPS2ntkTElJvF0Bl6uabIVl7rACGfRtQXVYRLyJjVwUnosiPKivIrW\nc4ScQLQQRTpPvnEMqwq51OVVJSUWrEuvua/EPf1Pf00TnOqoc1Q17AUORSvz\ncZA5zPoHzxDR928VrQOVvUUaA2wWTpgyacotgdc9xzVbnETIqtlfSCW5AT4e\nc9PqIq/yPkP00oWFGg+I/FB3pGPcX3cU6gychZ6JL1qoqy0q66vWXfZ/fNKW\nnb7jF5+tOveo8Shund8a4D0I2hMGCOFjMb8XnqEdImjfaI68eQNwDVrMbHID\n1/osO2LkphOALtLOJrxdVhBjclyWBjb6orKYo+vH/lq0z9PohQQxQy2RFMTD\nx/7QFayEgLbncO3x9VMTkTX5O08ZLYqQnxhjxIGXCruwK7Vkq04cA1E0NVMj\nOMab\r\n=OVSU\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.0": {"name": "browserslist", "version": "4.5.0", "dependencies": {"caniuse-lite": "^1.0.30000948", "node-releases": "^1.1.10", "electron-to-chromium": "^1.3.116"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "b26d80ac95b3b592b05ec9cdd8a142d924189ba7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.0.tgz", "fileCount": 9, "integrity": "sha512-92IS3knByY3wF7DDozdKwLxkvp23Ydn2O5L0qVwR1p/ioN7oIDl7d/eyPMc11rTVofT/6IbcdwP9eTId7orGZQ==", "signatures": [{"sig": "MEQCICRSfQuk88Y3GfzhfLh+IqPQsSIbB+mTjkFmECr5zJXuAiAT2KR19f6ZEAOXkrIPHOBmEwZBNqJonAmzM8iebTR+0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjA8yCRA9TVsSAnZWagAAlJsP/iO6zy5qfH3B9dxPY0UV\ni3ANc5+vAkOplK1OkXz+6wgygqqwakwh3kCAeSB9ctlJFQMoBqoO/wap74VL\n8kg9j1RqROnvMO2fuzJb67UMxicEUoQXeMG91OJ9qd3VWQg24Dx2moHBogiC\n4ZsFYPWcmWRtoac48NQNefZHX4D+Uv0c5FbgkjEAe9rLIIvaUfw0sfZJcxpO\nml415kl/N+4xFjfvf07q/j2jB7TGWggcll7NolhdM7atBUQhGXYfbspZCZDO\nzUoJacWHApjiOzNk+0u6ZZ38m1yGJQa9NKI179ixy/oL0HCZMczPaqvDM2Q1\n2+SV3n7X9m1jqfEMHqXcdocy1UH6CnBbU9a+O0TWaKmwkQIo03r5h9Qjpz1+\nFIz1Dy0Q5BoEiNXyKz9dAT+wo7c9Z2bI6cUhrEy67S6E0eCi4JIFB2m4dG5h\nDbl4qGO94r3KLv+zG0fHIi/+axdzCvTGMsu+dHFoS86jrijrs5of8PMg4l8U\nXtpooCjMQQ/fXXCUcK447pWIeUF+bZDKw/X0MRDx+F10okwizIZFw5dJyk75\n4yTIC4UINCkFMIUvJquVhY0u8O+8ZzdDGGGCUCwj6GVffG7ftvOxKYnBkxk0\naq0/KKFii6vWbqYbFMOpRn82NF7G/UBHVmuAkosZdNPKkVGsspLGBp9jNg/L\n8apO\r\n=iGsS\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.1": {"name": "browserslist", "version": "4.5.1", "dependencies": {"caniuse-lite": "^1.0.30000949", "node-releases": "^1.1.11", "electron-to-chromium": "^1.3.116"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "2226cada1947b33f4cfcf7b608dcb519b6128106", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.1.tgz", "fileCount": 9, "integrity": "sha512-/pPw5IAUyqaQXGuD5vS8tcbudyPZ241jk1W5pQBsGDfcjNQt7p8qxZhgMNuygDShte1PibLFexecWUPgmVLfrg==", "signatures": [{"sig": "MEUCIQD63s4ITM4/D6yBDApYGGmE4qis0EIh65ivilANVPMZ3QIgKyqAPPE9XuvD+6AlImUVjMt3xc8WPgKNSoNDcm2gNLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjauHCRA9TVsSAnZWagAAm2AP/25PYBrWAy89iOnyVt1U\nj+BbD+x5Yp81c36NOTdrMwQhscsIrrbImmGMwImmt3FzyXzrK206quD04d87\n/mS7l4qv5pqVEz3BTyDpuFzqsgROhP4uXiAFZ6MplifLpy3CsQlvWvWYwmxH\nUaF0UdiVXGncmnCSz3NaL4LHJQCvczF23t7W7UDdcmVWobYfxXbMYeXQrd+Q\nQJ0mj0AsZmpsYPKNjLaw4PoY0gm3leQGrYW6SetrmQjTw+wHfyX1L+wyt5PO\ng7lKaWUG0PaCTS1psTPyRmcSZVJxoBS1AHtze8Kr0B/CSC5yW2MdplhzNusA\neSS4P0BtpoTmXtqYdzdFos9CeeMwTSq0xnmbhHO7wHVfBDfeBrls4S8d5jr6\nIv2EKbd6l+Z4qSQRYy4xPmgREkHTNv7kB+MEDpEfxuauECv+HeZo8b/llfEx\nh8Du2m5pThxI6IjmMLDj2cWaZaFE7YILwFIq1x/9aqlMtlfn8lmtjMX/J3kv\nY2MQcs3F65uypm27n+AFdKYeIK751ABTDXPtn7L5lfHdUW62RMIwDA1ESsMW\nTRfZ/r0q0e1FulOjGFO7es2tLzPK2lbWDm3JQjyyOiFfjIZGBwwq+DyynQcx\nMaDivPzYDUV1gDpUA0dK44q9Yqp8O5EjpF4Cm1YWqLYH9FAtHDTuMJP6/ylS\nfsOZ\r\n=kQdo\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.2": {"name": "browserslist", "version": "4.5.2", "dependencies": {"caniuse-lite": "^1.0.30000951", "node-releases": "^1.1.11", "electron-to-chromium": "^1.3.116"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "36ad281f040af684555a23c780f5c2081c752df0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.2.tgz", "fileCount": 9, "integrity": "sha512-zmJVLiKLrzko0iszd/V4SsjTaomFeoVzQGYYOYgRgsbh7WNh95RgDB0CmBdFWYs/3MyFSt69NypjL/h3iaddKQ==", "signatures": [{"sig": "MEUCIGcgN8NjjKWlsOE7/H72+jnhvByGAZVqL7faMvnnTyESAiEAwMrHOBdh2XA+Qaz2iCchmd+gFecsYW88/v4CGM7eyWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckmahCRA9TVsSAnZWagAAsh4P/0l5ZMbd8Yy0KF7yWQXQ\nsNIaYwbiNUSmkEog9+15afGehg5Q0MWbRU5VkJOzLp4YfSLiRHOxOzMmzEeg\nLjmsGdvWUkoU/t+M0eqL+GmyD9JO/5DdsgnXXR1UxB/BDvtvO17cyCUguz9P\nHBJBqpi+lpALHuaGqmtWAJygDFdxDFxpyxIYrkhSdNjMgeNry7xHRaJRWbUR\nMifhfVyDMv3szwwrRBk4SPxWgWLSZ02CvpgCZvXqToH6LwYFLY5VEHQIAUko\nm0OPy4HfvOb3y19DI1oqK9jP3EPiesxCq8xxpFLGS5V0YVEuxNcswpLOLCZV\nXucxrSkZ+XxsD4GgQ+3I6H7zpCM8DLObRxsW8UzNT1r1fonkDWV9uc3Qj4IL\nvA8+NFCZZ2Ik1hN4NSuOJpDQFKukpa4qxG6zqQ8dO1oFIZTwv6Nh2tLc1+/F\nfqG7M5XBog1eOh0XOk23dAUpeTGyX5drUKoddtv1u3pRoHCFCVoSNrIeYbEF\nAL1DqeVIOT+msKeJRtZrS/5Ip5BBdsqyCUxQlL6pXdd0jbWL6RC+u1rJiaXz\n1hfbdDi0VOp3mdm3NFplRS5rhk3S6v4zFELHzuOJ9Q+qAyV68uPeXIOm2/Qn\nCI0uO+96FHlL4uTb8Z3aEsFnwaBtgKQja45BswTks51PVSEOXJXOPOO1J05p\nOSf8\r\n=67T0\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.3": {"name": "browserslist", "version": "4.5.3", "dependencies": {"caniuse-lite": "^1.0.30000955", "node-releases": "^1.1.12", "electron-to-chromium": "^1.3.122"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "969495c410314bc89f14e748505e58be968080f1", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.3.tgz", "fileCount": 9, "integrity": "sha512-Tx/Jtrmh6vFg24AelzLwCaCq1IUJiMDM1x/LPzqbmbktF8Zo7F9ONUpOWsFK6TtdON95mSMaQUWqi0ilc8xM6g==", "signatures": [{"sig": "MEUCIQD3Y9t6jBDPZJhcGk3iro2ysI09/otw1BTmwR6jaNtg2gIgObpHM5Tbx4/AkMqz7Gi8o7jDLoHcu/P+C/t9iCEYkXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnoXYCRA9TVsSAnZWagAAaFoP/3JJHSz/XgVkRm6bniYW\nBL62RGKMGSo8SXCYjAQSkkQhZQKGeJsn6+EYCkpbZx5XL/1x7Jp4NAMfdQcB\n2Fr+6N9T+ninNHugHDTGy7X+LtkdZm+mWdQQhr0HyYwcYVtkIrNJYFis44F9\nQfdirT4Cvz0XcG8bjWXDUaUMLECFGkhmBI9+uf6hxExY/adpysh+1RoM1VaB\nyY0Tc0kF3qFX4ywv9uVTUMynoIn2aHwzt/bAJ7wVX5O0W0q7F70u2phML8Bc\nz2+8SolCLwDC1jj5O9JBTU9Es41xtZMdoJUZxEDHFprx33ZukvGNG1I5YMwa\nv7i4ZCjOpCXgvMZ0k+cAAL2yPc1Oqjs4hgALlAWhPFWvzHIP/d1+zfoYH3ER\ngbBJDGk3QVphjvLeAxlSMIju4vvZLlGK2VVoQpUIghSKIBUN759yeTcodY2Y\n8gqCx5AVccsiBsU+MIoqMR/ckDApD+t691GXXb0PrTiIRzWiwIOSrbdIPkVK\n4/SK7Ur2a9JWtKPJBz/5vz4z2poxEzNejd8TcSsTcnrrv/6jmVBC5VixMB/s\nEtl0uRNNBrNneBu/UzLEHtTO8ZNRg/IoWkUkFhb14QoTRq9ndS7GvdosAUQc\nAP1PLIZT5xfJnaCKFJpz+cRRoslHV26q8sVe7lTlPOlurTtGHYXeB9AcBQEC\nJ92u\r\n=8s+j\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.4": {"name": "browserslist", "version": "4.5.4", "dependencies": {"caniuse-lite": "^1.0.30000955", "node-releases": "^1.1.13", "electron-to-chromium": "^1.3.122"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "166c4ecef3b51737a42436ea8002aeea466ea2c7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.4.tgz", "fileCount": 9, "integrity": "sha512-rAjx494LMjqKnMPhFkuLmLp8JWEX0o8ADTGeAbOqaF+XCvYLreZrG5uVjnPBlAQ8REZK4pzXGvp0bWgrFtKaag==", "signatures": [{"sig": "MEQCIEfxDNQ3HOSg2qA75RSKb99dTUcdnoJc1GVqqNVGjKOpAiBL4C4QBR8hLZIPcJt7++SAv5ZYYuhF7tLjsG7keOfBXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco+H/CRA9TVsSAnZWagAAmWkP/3pqndkilyNxF8+0V8Op\n+AZClXD7omlvyYkBWWV2YVMpLbIpEta2u3uN51oOQBA0ldCa1TS76WCtGzVx\ntiLBiiKPfnDtABe18hQZt+DmGQU0j2NQCDhSsIx29m5KiDYpIe2l3qKQTtga\naEOzGAdejqJuSU6KK0Ck4fcqQa7kuzE8HgoMylUy4eid8Lwba9AuBW1FD9kf\nc5ot8bHv6bQWzrKxODwutxuYTpbRfT+HkcoH3u/om5bM7mxJlu3I1I3gkTaS\n71EXaPSfyncaNaFzYPNlfealj/ILXN2OGOaTzTXx2hmPGx/n7pNi69nbI2Y/\n7pD3RBpDvufTrtzV20jHPbTxG5SS22nzmzF7DOpvn6mW0ymM64Bx9MXFppEM\nY64W10Rt08NV93SXPvnx13iYRxNkfMQrXv10kHLFo17C+Kwx6CT0vCp601Or\n9kEmDw370jOxbgQ95U9OdZ+2DPCqYEDNdcAACN1nrE7/6SiX1COS2/m2q3Ov\nAdRyMEaQpuKMUpgJO9pXr/nyxfqzIUdlcutHZ1DuVg8c6SnmFJdmsYqTF12x\ngNH1EHaZhlY6Ny1hzcsi+pUndlbzib8dRYqC+ZsKaJra3O7uPF8tJsmlMhwC\nRLGuuiGVKjdwvNHdsbwkAgyhnkkwO1S38Jm+P4ODiedVAX+k/BuUdrW30mC/\nmJbY\r\n=Pixi\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.5": {"name": "browserslist", "version": "4.5.5", "dependencies": {"caniuse-lite": "^1.0.30000960", "node-releases": "^1.1.14", "electron-to-chromium": "^1.3.124"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "fe1a352330d2490d5735574c149a85bc18ef9b82", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.5.tgz", "fileCount": 9, "integrity": "sha512-0QFO1r/2c792Ohkit5XI8Cm8pDtZxgNl2H6HU4mHrpYz7314pEYcsAVVatM0l/YmxPnEzh9VygXouj4gkFUTKA==", "signatures": [{"sig": "MEUCIQCOr589ctmtYlkOCZ+ESCRM4SNuGYdhNTeEFVl06j+W8gIgQzp86M96MYB4yCve8f/dx5yMfOuOCShaoU31EQSDm8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcthgZCRA9TVsSAnZWagAAPP4P/3BXXE3gkL/hRd5+PJmC\njrFK5qZdSig6fY3zd9fjT5EuAR0TqoGgwUHVnLioUIQLI2MTBh6hKP9QAVm1\nmOQDOELcIT9R4e6fpC+gaOnmgO3YC7/CGHioyBe467gbu1y+uH/t0jtCxpk0\nBhWUgNO3/4HHnBnM/NNHz97ybi/kRSAEA6WUfFz9PurP94IWCY8MQwbhOBo8\ntZMpSlZiMShTmS9SQyphE8TWtnCs4fzgE/OLk/TUDLyCB/F3cAoWNMSrjW3Q\nfGRmxhD7zZOuzAu8fYfYRzetJjPnxBPXYCJOdXzHTa/0JROBxWBRtqbtxIN/\n8XbNlqHUmqAlDIECflONT1u9JTK5w5YNA4sBneSLup8aajFeTtFJBF4NZZOQ\n+U1pEIXIwjuCP4O4Qky2pVDXU9B96yXmHdayT6/iNDm3ipHSZtwsLS/Qt1YV\n5BMCvO+2U/3Nl5me30IVGpgO9KSJKFMDiNGBdE2/Hq43NVCpH3iY4iL7DMkJ\nss8xhDWqvs7FxOen3HyuIRq+WUEGrUeaY37SD/nh2EMfDepkVWL3cedsDr8v\nfi97CbqL3eBFNfIqqLbCVYVBlzKsZ3EHdXVFPvZr9jLW97OSrlp8Z6ZtuczJ\nOJfi2ikQeDTGkC3N2BQcyZ/agAtyMjnHzzswswGBJjFk6UiW4uYBv+rT96JD\nuuHH\r\n=ztKO\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.6": {"name": "browserslist", "version": "4.5.6", "dependencies": {"caniuse-lite": "^1.0.30000963", "node-releases": "^1.1.17", "electron-to-chromium": "^1.3.127"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ea42e8581ca2513fa7f371d4dd66da763938163d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.5.6.tgz", "fileCount": 9, "integrity": "sha512-o/hPOtbU9oX507lIqon+UvPYqpx3mHc8cV3QemSBTXwkG8gSQSK6UKvXcE/DcleU3+A59XTUHyCvZ5qGy8xVAg==", "signatures": [{"sig": "MEUCIB3Sx9SCBD8T0DKM5rr4R5PvGZhvk03sK4PIY8lz91URAiEAoGTEs3IPVmjJ7O5nUJ5hzXgVPOQzmIbPGElPLXLvXBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyIoSCRA9TVsSAnZWagAAI4oP/RaoSRKXpcrVoAzxUKNq\nw+aQmZyft1wnT3WHZI8aU9ou3FO13V5TwjJe4JaB+yLnG8alp06h9dwkh1vI\njZeRDRFZl7NSFjJ9EjvcSxg3YBxaaFcUzPIVOXLCMPLEKEE4D4C9oJEqZ+BX\nHkpNU9EG+xqD+v6n0J+UQxwJZEo+tpmTTOrOue4VFRjl0o994MdU3tX29pow\nAg2aoAVjZe1sV785Vx6lXqdgCwTG53nH74R4YPFNgddtB/LpTuTQrnVnClFf\nvvGrbYXA7ngwV0LQV/8SUHU/36tvMBF3RpiCGrwdJnfhx47cHpghf649hTjl\nAypG4AoPIiFu6jGyWCEPPKSoUmQ5PIHJk9rqf+4UvFncnn4lZt05qjfjW9vN\nCg5bu6X81HdR5FjM8dRh6OJUzHO6Cqoneusq5JD6gYX+ADaFYXyqx/y/o0lH\nduOhJZFDImGoJ9g0LrLDTy3V2glkizf/lJpY85FdkbSih/xAaGbpEK+dUuQQ\nRqYjZ5oUf1lIG4fsS5/JKknEHDTDQPtJpao1olgwElgHcaHUUUq36kbXqwOv\n9THNszpAhs8I9YZyOLnm0/j2Bw8ATgMsoeJXMt0e/Ts+EBFAzocneDLHdden\nwrTmSHvU5Y0UoRb+9HasX5UNZRD8V9XZagdUVmT3FICSVrIDB0UQobZWeVey\nkzDL\r\n=BWrO\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.0": {"name": "browserslist", "version": "4.6.0", "dependencies": {"caniuse-lite": "^1.0.30000967", "node-releases": "^1.1.19", "electron-to-chromium": "^1.3.133"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "5274028c26f4d933d5b1323307c1d1da5084c9ff", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.0.tgz", "fileCount": 9, "integrity": "sha512-Jk0YFwXBuMOOol8n6FhgkDzn3mY9PYLYGk29zybF05SbRTsMgPqmTNeQQhOghCxq5oFqAXE3u4sYddr4C0uRhg==", "signatures": [{"sig": "MEUCIQCZk0AUtUVXeN0z1BuCsfogycCHtF2KBJ+TgrLd1K/o0gIgHsTYJ08wR3fWGClKFxjXgRbtyilscmX1c7N0xDegDgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2ogSCRA9TVsSAnZWagAAw0wP/1MAS2vGiDnNz3rG3wwE\nViDoB5XKFZBxDHx4pVVJrShaAGp1FFcKqyWFOJTBn7wtFEeqFDGH0ix4Fjji\n/VEdcxopjG2EVS1n2f5pqWJKIdHgAkSPGiEgQuTXyC+ZRWkad0R8YF9nvCQs\n/CPfthijhaZVmY5krgfO9Y0V1TUdyp5KK0kf1jujgQHU61uamoa8ac/3bVLG\nc31/THNU0FvqqPrOqEBWeGCWsD1j8pv/AOsj80SKQQoOVEgHnmj7v1cjg4D6\n1MKdoN46q57mF9OZmBZjYILxTP7sJ+S7Rezrj5dzLkhLzSKXAbJedqzcqYxy\nYvi2UU5U78gF4bScIqsLOFZchqXHYXMfwaqChq3iu7TYmMgJod31vUa3n9Vl\nAEZ5owo1rP9FDTagotu6nCdMD1g4r193U7GQ+/bnoEeI5x8R5LSKZJkDyAjx\neFQ86x2yYEvcJHKPjwkYFRoLG0JVZhb4/2Ry/opKv0M23co18kPGmZxH0OeD\ndhElBDoOQWD8uu/sp7NRBARxptgwNDd10ZgHpj9NA6bn/jTK8jM8NaJWYDCv\nov5QuUpzPSWhh9uoyRSPKjFecYv9W4p50Nttr/0fn5iFHPbWSbFnXqWk9G/p\nKfm0fgRWIYKC72gEa4G+f0rrdzYdxDT08FwV8tz1JXVHvH+WnTG+Kf8kJDu7\nIZWs\r\n=DZbg\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.1": {"name": "browserslist", "version": "4.6.1", "dependencies": {"caniuse-lite": "^1.0.30000971", "node-releases": "^1.1.21", "electron-to-chromium": "^1.3.137"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "ee5059b1aec18cbec9d055d6cb5e24ae50343a9b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.1.tgz", "fileCount": 9, "integrity": "sha512-1MC18ooMPRG2UuVFJTHFIAkk6mpByJfxCrnUyvSlu/hyQSFHMrlhM02SzNuCV+quTP4CKmqtOMAIjrifrpBJXQ==", "signatures": [{"sig": "MEUCICuivT4O4Zq5Ppsl+kVNnp+Xe1uyq2wV8PifiaFiL2AgAiEArBTd46zwoZm9XlUTveOkK+HKZL58XNBLmESrs9Q9fsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7ZaqCRA9TVsSAnZWagAAP08P+gLGwB2TVP4jpFdjSkty\n4KghMw6ozEWy4DyD66f4cWZsjtErNQJC0C/UxFGYPN0ZMnkn1yZ53/D0T0Td\naEUcwZZmSxiZw2K7+MIW57RXqI4gEzE9IQzhkma9CrOiFNpeUs/1seKKEytY\nLPE4uX4QNFAhoNQTUoRb0ev97WwocPIHjqkj20COuE4gX+8DbdITlNxLY7Uh\nL2HI0WEygICjDI5KEuf6D97j8MQvPXVBAquxsEHHLNUtlIUpRTVTLuebMb2O\nLqFVabm30wZTCZ9TWqGDLHTU50BhrnpllNXCN27zS41ZJRatGA9AEJMUshVL\nPz3bh/KCvCv+qGj5KEHMAToNApXsM6c1HpS8nyfVwofgPDSZ+u+rDSAs6QCQ\nqyDDfE0otyQW0bikd57le8pgK+VpIWehTRPXi7tl7HYdtcoPCfDYGXoja6vj\nFY505hl1rO34HprZMnNGb/I4XjDEUww+3+bFlu1jxww8GGaz3XLzpexn0Qwh\nQCHro1R50z89RpwvXlosFnJVF/CbbLrDKggu695fFJ5cNXg9yhiaHztg6uHq\nbZP2hv09Y9OI0UL+Pa1wsNMrnFGKWSsIa3u06F7XA4LprPVPg/MbsZ5bj66E\nQD0pOqW21onc8gVLft8kQ1MoEGo2XcpaSMvCESbPgGDZjxVJDNDw4QrMBOd8\n0Aco\r\n=scJF\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.2": {"name": "browserslist", "version": "4.6.2", "dependencies": {"caniuse-lite": "^1.0.30000974", "node-releases": "^1.1.23", "electron-to-chromium": "^1.3.150"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "574c665950915c2ac73a4594b8537a9eba26203f", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.2.tgz", "fileCount": 9, "integrity": "sha512-2neU/V0giQy9h3XMPwLhEY3+Ao0uHSwHvU8Q1Ea6AgLVL1sXbX3dzPrJ8NWe5Hi4PoTkCYXOtVR9rfRLI0J/8Q==", "signatures": [{"sig": "MEUCIQCsUSXV6BjIuexc+vDMmpMN4JX8p/6YayFkVn37cWdreAIgAyttvqLl9CcyLkiLFXMv4wkoRH4+UbSIjk5Fm3gByu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+rLzCRA9TVsSAnZWagAA1ggP/j6gDZtUU4rQcrlAiOI6\niTDIT2EJKJkgl008GdAumr2bWUU7+J7d+t+QIzD5mJ2/YSnKlB4q0QryhdSa\niYZouUCOxmxQdAcnuVKv8DAYNSmQCFNw2TYaOBn+QUhwi3GTQS/DSn9MJ3oS\ndyY6g4jjH6niv40fvlf6Zk/+tdhvzgoug1XOjj9VXg147cP954VFqL2fetpn\n87C5PB0RX9TtG6yurDa4/YsT7nLQk1Lkm8qZsy9G1Y9U7E0VXMJmfo1yc+Cr\nLiTMvatOegEHBKTHfDYZERkl43wif8hnKlYByJCjd1CvGp1Rmd+Mv/HqUBMs\nBIexBXhb2x5ICudapk7MtJglub93lvZDaYp7Ep0dy8rlMiLqhCXwjmAsh1zD\nKXAhI/9ExpDWpO47xl2TlTY+ge71NuFPFhjtclQ26YNKpml1Wbn7ZewwEWUI\nkjvCq39nK5wftAuRH3iWNj2m8VX1MRCDsHlKItFDrBcAsIrG8a8pVSzlN6cF\naXJy8i0wwMaXuk+ah610IOcJBIpauGDN7q36ZM23pDxF2ucS/y3DoEKV653K\nZwDas5eBFe6duGIddWDe2+aoUF081NYxIDNswiyRV2fxAL9uAMA1K4WNGPTb\nkpG/3LvDFEuLrcOeK/1YVxHab5GS0tFA5+y7uaXHMGTSzTTUvk1kdYQUywwa\nQtLw\r\n=l8QC\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.3": {"name": "browserslist", "version": "4.6.3", "dependencies": {"caniuse-lite": "^1.0.30000975", "node-releases": "^1.1.23", "electron-to-chromium": "^1.3.164"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "0530cbc6ab0c1f3fc8c819c72377ba55cf647f05", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.3.tgz", "fileCount": 9, "integrity": "sha512-CNBqTCq22RKM8wKJNowcqihHJ4SkI8CGeK7KOR9tPboXUuS5Zk5lQgzzTbs4oxD8x+6HUshZUa2OyNI9lR93bQ==", "signatures": [{"sig": "MEUCIQDk+SW+Z2BVhXRp3Sd3XcW0yFlKsfdEEEY2q+bKyTSaHQIgIwbP9hfIoGIMH7f9swKzlEJ1u8l0ZeSxq1EMLTeLD2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCKrLCRA9TVsSAnZWagAADV8P/R3S9nhCZzwKX83V6d6v\nH5knc8R+C7ElzXZEzVZCSeafOCyAdBbuzM/Rku1avtZL/Dd2FH/Qagq0Jlbp\naqEutSn4LQeAMt3pqoOMgBSc5RXOAtMNsefQnudR6acbYitoQ70tMVHHS3mW\nR52am5XhmJmJiDruMxxIFWZfNb/QzdRPhcrTpDXZnnkfJHXH/i6jfXhQyI2S\nhJn/dgHNEx8m59mzGLvZeNKVhfokChpT5VM/BtmTtwVRCltWuDSBYQIXj1Xh\nGfnib3RhgqzOqTV9raYI/AHP58Q1uD5rAbVTIeNjNILSprZNLxUTNwTqn7PV\nwX6fy5kTOViEV2zRp+SL4Ro488cb5ua/SOP+hy3ZrCewIV0EdeFGurfsyFz8\nciulca5neQjDAGBTPM440Iqs5U8C2ZWsKUF+8KYqy/IMHQb2QtktH1vZ4DGA\nz4H84l6IuvxX1jfqUd5UlNHcYjQcFgNS+/mv3y3EmbvILmED2GRcPHLCSnFa\n0vyV5XHVpYyXvnXfkqAIv3ioQzCd4IBDq2asq495N3lAiqqqHA1vM28mKhHL\nSP3mNY/fpD6nASM9TLpUwJeAIBStnFCx5SSqjySccb4gf3o3VkPeZDfledwr\nIylhS72CcokkUYlc5TZiJaKksawu6pVGIT6Sw2L2RtUAMeWbEGJoHK+VH4nx\nPNAg\r\n=l6RH\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.4": {"name": "browserslist", "version": "4.6.4", "dependencies": {"caniuse-lite": "^1.0.30000981", "node-releases": "^1.1.25", "electron-to-chromium": "^1.3.188"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "fd0638b3f8867fec2c604ed0ed9300379f8ec7c2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.4.tgz", "fileCount": 9, "integrity": "sha512-ErJT8qGfRt/VWHSr1HeqZzz50DvxHtr1fVL1m5wf20aGrG8e1ce8fpZ2EjZEfs09DDZYSvtRaDlMpWslBf8Low==", "signatures": [{"sig": "MEQCIBC3+e7b5WvywqJUCNVsOpZvWy34uEmGo2D8KquNzXL9AiB4uJvYUkPcoRPGFC+zAIlLFno/P/7bNgoEqZzAXgwU0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI2QuCRA9TVsSAnZWagAAVi4P/ArB+8E8K1fQMx1LLm65\ntRSoFhJSEPnVf3Z/43bAD7lFH2EfeLAxi/tYH3HgswelP3gUCv9WSXmDZ6R1\n/8gTVScK1YNohnkJnvGA4p1zkawP0kH1O8YvhJ2rhqmsCHxrkgp2u55DGz9o\nC+dyboYvrWQqexKbQcPQaiM7zODxPvpC/mhDW8fLhIhW4n+AgJ7jOrwtHwNE\nfhTcfcJPQoIsUDxf5Z2DtI6oHRvB7aBGwlr/t7kLbLeBDLC9HM38owJ7luoy\nLHSeaPRRFKo5+/hI81XGaejaE5FWXvuf7Kq6nL22BPk/1okcpSjqsMNEC1oV\nrFgwxvZ8lVEPs7Xe+kS3QoNq2Uq4gVEvxJA6CwhV0xlEtHFsnIwsnFU5fTkM\nFaWtq5AdyvD+hsQQupc2KN6hfYsimrYF3hBCJT9/yBpMy4KHmO8B+sd6Kj+r\nNQz7uGYppgq+remshUYwjEjl1FwrUsw5y6qHDVXxgFy/MmbGehas4X/ffhDi\ndKXlFLA503wYhyq/AkbPt0VRA8j8QzBVq38+BsGQ2QreBlXF2Q9mZzfLCo6R\nsx1xmL4u2uRSQ8cfzCV30f3z2g4MkDvTbROGdTcZ6mYeIAhKqQFfxe+4XTxs\nhnKpvse7ycqXkkCh0d6pXMVoUIW4u0Qnq3PG05aZ2XKQxPBvaDSlRsmCJyPB\nLPkR\r\n=KWNE\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.5": {"name": "browserslist", "version": "4.6.5", "dependencies": {"caniuse-lite": "^1.0.30000984", "node-releases": "^1.1.25", "electron-to-chromium": "^1.3.191"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "0af4d2142b7789f58c4c1fbcc05c0ee085f9a46d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.5.tgz", "fileCount": 9, "integrity": "sha512-6MJa56llNfIcHwNKvtiR0A7aCCIFECbMGq+egK8d4I2J4hX6CArxvx66tH27rlT9vKDpAd8+htI94Y9X9pJfEA==", "signatures": [{"sig": "MEQCIE9vCzgYII8D8DgOMe1+8jUDlYphGIzn3LLzQ7BX2zLKAiAGz7vUg8A9eOJvM6TmCJgjE0CijFEqL+9deF2O+6dtEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKnhWCRA9TVsSAnZWagAAc1YP/ikONYP1eSw5rsuv8mcF\nIxW79vtmxdR4dDH62bFBYmbCneHiy9pctd6LdD2qQ5g73iCjfSR8C1C9jBd5\nE3dWW4z5aKXmEaKeUDD46iVADr9pc+VvRMeV5ANZb1qxSGc0k4U2sHM5s/PW\nxc6uRFwQPeGld90gltbl+ho9GXuAUugo4muQz816FhTcD0ME/gCJpcJ5z8Tw\nQ60x5wvMUxdy9SN7H+Ry9wDGBEqsXqnxl8NxTmvUZKRKZ8OdS0jGHAzyZqvA\nNuUFKKhbAXHMJoIPWwHJKtWkxACUS3O8lkju1ZwDxV1Jv3G+uWr+GRZvvzyd\nSZlkyTe3wWDxD90UHBAuzPsGHWxk4I7s+MEV86vGX54f0auVgtJAtL1vz6vw\nIepW1uWQlb8QMwFAmVyh0Wxj/PBShaK1bHyXHYXJ2yJ1bqBN2LOEi/Jf2+MT\nlVdHgbRCL2GiHhuNf0fMfztmJXYHAqPwA8jpd1TmY4WZTwIAIBXjZtYgXcwW\nI4G5n4mRYXDHFJHtAy+zcgJCS9uc8UbS9xO4ue9g0264Pt0KiGVxW8mVD8fH\nBE/iIxeZ+2w6uDG08o6Y/qIWrWTp4U1rGiEOZ3xM7H9pfdeDLQET9jua6L0B\nnbLxPUGe1PYrhwOyUO8CMss2fQ2y2uTyh87Fp63oiCQdRmU2/vsykqzuk9Ko\ncBV0\r\n=GFtC\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.6.6": {"name": "browserslist", "version": "4.6.6", "dependencies": {"caniuse-lite": "^1.0.30000984", "node-releases": "^1.1.25", "electron-to-chromium": "^1.3.191"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "6e4bf467cde520bc9dbdf3747dafa03531cec453", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.6.6.tgz", "fileCount": 9, "integrity": "sha512-D2Nk3W9JL9Fp/gIcWei8LrERCS+eXu9AM5cfXA8WEZ84lFks+ARnZ0q/R69m2SV3Wjma83QDDPxsNKXUwdIsyA==", "signatures": [{"sig": "MEQCIGolf/7SEGGQq2BLmJ9Oai9ydgmjnBeH6Q4EvZhCZJoiAiBEK5qMsSOKBpA2B0WyW7AeDJDYlfhDjKieMZZrIHW0sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKnmZCRA9TVsSAnZWagAAXAcP/AtNHgp2x8y4pnUhfiYS\n4/nJ69cRsyjb9F/8tAablwlRmVQnlPVg1t3QU/c33CbRdtOw4ZzQlvo9YL3R\nb/U6kyCxPxXrV7gM47VGH+oqLmPwGBZam7eEpTS+VNnTA9yAcmdqiW8f6tfe\n8qrAHe9lPTvq7qztnQHpIVXpGoAq2rhyboVTUF8RJHxnv2AbSaBI+oajsZ23\nsv6p8b20sy3O59XkzFxUmfNIDITgkfjbACNJDs7DZBj4f//iQ+wHXnnik3ol\nMoKKya5HUDV4is6RQhYfLdxlmV8HED7KzHj3Zy8rJvKd28d4vFS0fOe0yMsq\nbEv0gzfmgxsKXfKmDF/fUsR5aSiH3M0YSkVzjbSsMYuyzYIZszxpNSHrom1f\n5blPn+vvMRWq/R/P3dcvIOS7pK/xc6X3owuY+PEjrtfk+uayd+PpWzrojxKp\nsz+pkMFsBCejtESKLktzPkTvGsOsesPh87AFoJjsaShhwBJeqcMQ5cy5IyhV\nT6Y3DCwh81ECRt0xKZlvr8Qrh70+lt86fA/VBKqqRKUCO705GCESYAYN5C6h\nDPZThGGFtZhojwrnV2cVuYSlp46Ovvcha5Bn8KZXgTaVnc6kZPT3f/DSCh2O\nLsEiLJHWNmFiCZNF7LZV6hpMLtZqaznPot1VE27keSo/qJ6kNDwf2iCOvfiz\n8DFp\r\n=utq5\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.7.0": {"name": "browserslist", "version": "4.7.0", "dependencies": {"caniuse-lite": "^1.0.30000989", "node-releases": "^1.1.29", "electron-to-chromium": "^1.3.247"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "9ee89225ffc07db03409f2fee524dc8227458a17", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.7.0.tgz", "fileCount": 9, "integrity": "sha512-9rGNDtnj+HaahxiVV38Gn8n8Lr8REKsel68v1sPFfIGEK6uSXTY3h9acgiT1dZVtOOUtifo/Dn8daDQ5dUgVsA==", "signatures": [{"sig": "MEYCIQCjiJJ3J89L195hcH3NSvNHZ15VqjdNZDbNnCZTYwdxlgIhALjgGU/1shkoRJm/PGP6TfW2TlQ759oroh2JkaFRtNFZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdadfWCRA9TVsSAnZWagAAOXUP/21No7EjXMeziLuWuvSr\nqzqNy2SXqQ8kY/RSRqFRANVhUihzpicXDCCHxsrO8Q4ilGqMlnh1/WUWzP8f\nQWPuo2K2d6WpGZ3lte3c1Y7Tyxho58QZ1f0VbRmlNmxGtLMmwlokHrr9RWtF\n0o+SJqOo+lxNusW1rwfeMnV1FXdtXmYIqS1xyy+7P8XNPQsmrOB0SkGann4L\nbhJmzAIHo4/259mgc4mtTAS3uXHjPy59uOT3cowMRBzmrJJVfYpFBs7RhK1a\n1OUN+lKC+aOQV/gdSu2VYS2gu2vvbs+xZDSVpE3uS27nRgqievlkDCcYRar5\nmTul32QaCTchixLbAxi/W7x+x4gQkz6lAnefXmxgyVVz81mPZZq60NZEURHt\nHdfurIciZgJQSw8LTqNCnAv0kPECPBkSL0Oxzurkr2nNzQcXOITcWWHzS5W8\n4PjFnO2fH9/MdiUPV7Y4WA6P8HbiI6dzNMjU/StIqTk0pzt01JY2OIOMEHRo\nyWi2vcO5S2J+PbwlNl2r6MKUuCgdia+aWi52qrM9ulgazecTxCn/xpiR50Jy\nQS+/RpYQJ0kpg7s0jous47jz4yR76oR22G8BcAHJOC+G7+U23Zpt3qWEAqSw\nlldg2dZpXsjbMv0zbj+qymTqg14RyDqdRPl/zGY/VmCqQ90w6qk3h0GgbJ97\nuPDK\r\n=X9KD\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.7.1": {"name": "browserslist", "version": "4.7.1", "dependencies": {"caniuse-lite": "^1.0.30000999", "node-releases": "^1.1.36", "electron-to-chromium": "^1.3.284"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "bd400d1aea56538580e8c4d5f1c54ac11b5ab468", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.7.1.tgz", "fileCount": 9, "integrity": "sha512-QtULFqKIAtiyNx7NhZ/p4rB8m3xDozVo/pi5VgTlADLF2tNigz/QH+v0m5qhn7XfHT7u+607NcCNOnC0HZAlMg==", "signatures": [{"sig": "MEUCIQCHxAgc9agN+MYw2z8rdImCdO6SLGD0DJhZqrOsSgatAwIgA47pQRRfUNE1AoECA5xJjfOKD6cBFGm7o9cc2n1iJIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqGcUCRA9TVsSAnZWagAALagP/0X2w/Jc52oWhO2wArkL\nZqAlD/IVjLoK0tKsL6gJfCadmHNX887152tk4oR13LbURE57BfeFtVEd7inO\ntpRfFs9AflWPzVt05zNU6PQ60MyUHApJDj7hy7iLBGsClMqNl7FgRhEGwJmp\n2rfVk/mx6/Ls0dzRH12gvRkBpCeSj8cHv1/Z7By3W5Nsko0LxcsqAyiZkQiA\n8nr/657gwluuIrn85D8+mbMwt1SwTLtepNhFhR9c8zuPbl8CyL95C5vtqXUa\nXRK5P0dtI7ljM2rKnk74VZ700wGqWhpRHbbbs9K+5oCXVfK0LfLrhPpPjSxR\n0sGdKonrc85UAMY0o+YWzSjbu02BXAi7LQOZ4zkQclAqIaEY1E5Cf2xIWyq4\nysRqHv6ZuXfcR3zrxxAGCkAXSaSNkHSVH8ON8jzt49ODq2EfWE+8BQWebnEo\nxcfB4Mbre9EN7VWoyTkLH0HCzL75Rl+MN8wRvRVyTcddgBAV6a/nF5w4CZPI\nEZUXMfxcRQgQej1Wx/sb9UtJN8exn8O19laBk7QSmQrFxRyFybnIkLQIgXkX\ntejgygdu6ubz21SZNrGusjtS4bz+LFYlfvtyYSy4qipH79qPOwdPqGpIjF8Z\n6AocqqC/yBzeHD+GuDMb92BV3/bEjgWIudrCkNmSGNQ037bKkUKHFFQRilYg\nJagO\r\n=xduj\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.7.2": {"name": "browserslist", "version": "4.7.2", "dependencies": {"caniuse-lite": "^1.0.30001004", "node-releases": "^1.1.38", "electron-to-chromium": "^1.3.295"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "1bb984531a476b5d389cedecb195b2cd69fb1348", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.7.2.tgz", "fileCount": 9, "integrity": "sha512-uZavT/gZXJd2UTi9Ov7/Z340WOSQ3+m1iBVRUknf+okKxonL9P83S3ctiBDtuRmRu8PiCHjqyueqQ9HYlJhxiw==", "signatures": [{"sig": "MEUCIHHEW5D9ufpxKWFEd4LF4ib1K4+kcM54aA/m6WZKoUslAiEAxIHdD+xWz/cP/PbVuCACrreE7kt88ae1Q1uVjkGknYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsUtFCRA9TVsSAnZWagAAmnMQAIPReGlVAjyEunJyA+Kh\nJmLVmLThoqH6vrcXhhzhRLLMmQLp70ZTWTC4pU57P0SnIvlQCRhq4pdYso6e\n4qgObGF5nWJoVzuJsp5lPCafMuo4NDv6USwdYa6nsV4jSudxM3FLdYz4WEqu\n2XhhQ2KxIaJUcO5mtY4wht5LlUZwCcjccvZ9eNwQwFDJJmyHa7vEbzwZNwFV\nCC+JM3hegQcRkZ4spVsHHJH5OlNkYOFNepf80PEPYUEYg4GwQQJGVQzsUQYU\nm+aGJgKd4zRuKSaN34V8Qhqzt6crprpAJfXxxk3qLtYKE3nuVZeGFNl2fPMV\n3T04lsWaZmkow6rkVuGokezuh2eGDbsPDJQLimINH5cZKALetl/vaGJKxQY1\niN2kKgZ5e2s5Sco7kDUAiOi6Kq/mKEdwMWu9gmMDpyCpSg3blk4DnJTg5Yp6\nvwA3Dt6wWhfdBdoq+RwlFOuT8aUfmPy+cheEEcNtdZGV+8iuPQg6egGN6ZvO\nCxw8X2U2Xy0pUiFZWb4Fi4GrdBdnObLQbg/mJA1tmpw22nJlNdY3qZMqH7lO\ndL8AeA0B9oKZUo8Z07FjyivRBOwcu8b/SVLD1Gyt6cXTJ4BimvxJzuGDhuks\nrS0DtxTZZJLw1MBnAVgdWJcn0weSjG+DzTwqx1KK9poXcHMiuFcvXGJAPgi9\nx6Yz\r\n=Jy4N\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.7.3": {"name": "browserslist", "version": "4.7.3", "dependencies": {"caniuse-lite": "^1.0.30001010", "node-releases": "^1.1.40", "electron-to-chromium": "^1.3.306"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "02341f162b6bcc1e1028e30624815d4924442dc3", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.7.3.tgz", "fileCount": 9, "integrity": "sha512-jWvmhqYpx+9EZm/FxcZSbUZyDEvDTLDi3nSAKbzEkyWvtI0mNSmUosey+5awDW1RUlrgXbQb5A6qY1xQH9U6MQ==", "signatures": [{"sig": "MEQCIFSzuBKp8J3Cur7Fv9vnlqFydDGfHR9UEpnNTUFPz7K0AiAUJJQsxewjjIWo/zjiuCyoYJhPJtq9ZSes3iG6MFF59A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0x0GCRA9TVsSAnZWagAAvdgP/jnswQXEEx/btRaNvQ3I\nzGoOcZbYNy1ta0a5mChJCqudSc8dKHr/LymOF7N/6GzLUNFMRvS9WwH0nt+j\n7U+4WKgJvI3H/4E2xHiSPgVWZ7Z1S5N2njxCeY/wIVsyxdGLrVBQ7/G6KJoJ\nJy5diMMAp4DQ6F5nKbhtC8h3+lH4iW/8YOp8xg14p+vdtMO1wi6Dxhxjk4yv\nihdaQIDDAKKJOpCGVFAKiDzt9Ka/zxRCr0prioktk46bLWRl9OSFeuN/J08E\nya+fAmIrtohiVLRqHA3g21qJRWsabZns5T9ggTA+sY3zi7hwMpeVd3+ZC3+0\nGdqWWaDwg9w8WWvg2AdUT4KC6T4wHRnOZchQRvJVXUbm+OLHHSGkCOmWAxP0\nGEZTKJz/vLHyRcy4s4rqG2ZPN1nP55C1SVj+lSNQf0PASeZfD4eC7ZlQzYsX\nko8O+J4aqKYd5QjDErDFncmcRj6y/0WP+rWfg+h6G3t3SVSopYkynz3jdM8x\n4zDrIImlxeMfGx10f0txHicxrFUwMUOUM4YUaHRQar1DuPvMgEtn2+0xVcA2\nyrsA3Pr3+ufUYZnGhfie9WVk+CIbmCnAbfAYPYF2hlYLWn8xvkiJnQ3bee8/\nT6VVLVi1ltQPFBRJQKMeyRv8OQEHK4wg0ithrP566lhtyRaBogTQV3v7MXom\n5Kn+\r\n=YeaN\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.0": {"name": "browserslist", "version": "4.8.0", "dependencies": {"caniuse-lite": "^1.0.30001012", "node-releases": "^1.1.41", "electron-to-chromium": "^1.3.317"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "6f06b0f974a7cc3a84babc2ccc56493668e3c789", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.0.tgz", "fileCount": 9, "integrity": "sha512-HYnxc/oLRWvJ3TsGegR0SRL/UDnknGq2s/a8dYYEO+kOQ9m9apKoS5oiathLKZdh/e9uE+/J3j92qPlGD/vTqA==", "signatures": [{"sig": "MEUCIHU++OUybSWulv1a90QYIV1r8/a+d3dbNtUP6+hHqRNvAiEA53ajhj9DvS/3YTxNcbFDz7fdBuzcVT4TZqWoIxBi9HA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4n2PCRA9TVsSAnZWagAAnKYP/A256VGF7snpBuk3k7UY\n8RaLQho3cA1cFP742SxwfyGU4Ad5BHtSiWIL4S2ACUckYP2JcS3iamurMu/Q\n6tweBY2OoP8uoA6I0G0nCh6O3Q7SPM1VZqh+2z1MonfC6Hwiiij/RShNjBKP\nbnwB/bIZyVXCy96cnBBQD4e++27pwlJd0HbocjIA+3mzM7nfi0dUPCpW4dxG\n4g5zVnE75Epr9r6+0m7/fR2dmM7Dx5mYPTDF4CcO4ciDqxIp3Pl2xrM9Xazd\n/lkqo7jrrh2jmA4FTEZkHoCRYF4SU1gHqlrAN6fTWk3jLiSgYUfokNW8tByw\n+SVeHoiXcZFru/EwhzyGvj9Kybec+PUD4E9IOFy9B0lnVMH3NfwpPbqrhSP5\nHZyiqFqPhtsrjdbPWxAHvHms3KofhJ0APUkQdsk3wzE4p8BmCmN/ATv5tKD2\nfQE1IgFwMUkqYBiCK5iHKr4/P2G7Dwj7vFQje1BfTRLXdrqjlw97Ha2kbSHJ\nozUYnMkq1lFkmGCGj27aPHwK35NDW1FL2o7Od5zvxh/RoNZ4yxY20WfXsw57\nvpEISf65qq0O1K5880a5XHjRBs/KJ6QrBFjMFSHQg2fexd+nL4gKDKmlEHtq\nyzaJu052UUAMdLpFcOpSNnSLmspEj6MJmP5ZQflgj/aRdcOC6oAaOxsklofa\n1/pa\r\n=l9I7\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.1": {"name": "browserslist", "version": "4.8.1", "dependencies": {"caniuse-lite": "^1.0.30001015", "node-releases": "^1.1.42", "electron-to-chromium": "^1.3.322"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "df0f50cc4b3255322fae60ae82a946baae69f8c6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.1.tgz", "fileCount": 9, "integrity": "sha512-X/lIDboA5bvFg9SOhHN7OBgHHlaZQWcwTXBLBGgrB8+6Iy1dL0wmUfHRe7OdETRTuD2d6f5JPa7iTEcbVyVf6Q==", "signatures": [{"sig": "MEUCIC0W54MOxDAnJfXbjXAX/ENOoC2dl5jssh+LMjMoowhmAiEA9ySlWtYgXbXXSBaKsZLkliLTiSIR3YzMedH5pDoIc8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6ONLCRA9TVsSAnZWagAAKVcP/25mD62vNqexNMcuzD+g\nhQ/5SSjTVyYNB7Cm3RcKB/PIsZaZGeGJEHRtkrXp8yKQiYZUlpb3Zmfu4zkh\nENfmkfu4suPSAymg1BOR2qGwfTryOfkNElgyGDv1KKOqfSNV6hW+tGEA5HKa\n6PMPzoWQuBkPeHmYGmpoVS0szVhw46c1EwZYU+KjEcmCjcHWif/ArTKQR1i6\nI8XeJr6Xp+ZuD4nFOKde5punBtGKF2nCfacLtRpUqTI/g42QuTVpM9c3pUT2\nGxVf3Ki+SUkREmtilkGm0coY07YxCpKQIZLN/tOwMjtFuI/H05QiaZMu2o22\nUZMUbQDoxA8rBxPPQC5khJomh5Jh/JV4Hw0AZgmmLCa0uMPYI7GBBgRa3IfG\nwFJcaFuHpmAPC5wjD8LjwpibgQtfdxg8rAFsk6JOnJenBThBmlQi+lS6+o8p\nQOvuI3yDKxnCmNwJUms2UfJzgHe7L/w5Uwjm9XUwxrnoHuYBIQCtm/87RCdI\nNUezaY+sydD8J9efPwj8JhnvscsV+1AW6T4mMQyrx15NzQ4IuUp8jxExHW2T\nksfqLnqEH5QiRGsNaxFFZyrlhSSFxHuRI3JCtpG3SrML6jAXan/wrcowZjWd\n2ZnygYa5lzlg9vCo84AQ0PfxIRwlADSLD4ARZcN+geOG0qWocZn+8KgDAZ7R\n9Gp+\r\n=7puv\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.2": {"name": "browserslist", "version": "4.8.2", "dependencies": {"caniuse-lite": "^1.0.30001015", "node-releases": "^1.1.42", "electron-to-chromium": "^1.3.322"}, "bin": {"browserslist": "./cli.js"}, "dist": {"shasum": "b45720ad5fbc8713b7253c20766f701c9a694289", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.2.tgz", "fileCount": 9, "integrity": "sha512-+M4oeaTplPm/f1pXDw84YohEv7B1i/2Aisei8s4s6k3QsoSHa7i5sz8u/cGQkkatCPxMASKxPualR4wwYgVboA==", "signatures": [{"sig": "MEUCIQDrPUKGO2P1c+wknFJLIFyqbDHNhRJ9XH27AWZInLiFcgIgFlGioJjKdeO6eYaAQyjTE8NyE5xpmYcGw93pV7n7KbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6R97CRA9TVsSAnZWagAAHoEP/1AkYLDaA0i7RqRDr0uj\nz33Y8AyY2BH63duh9ytPtael9ssMokBNKOKAYZWE6fHC5Yv+nWuBIqcMsmQn\n6eQx7zDq4l7tf5EGUAlr19zNv9mwX/rAHMZ+rxCMsqwSbt419ZkTk2kv3oeQ\nDt7reVWIcVGy5By85KnAU9bRTNT/teTmiQT6SWRZr1D2F5TUKaOGt2l5m6Cl\n8ASlMEAdGpkS+udskpBvTptal+Awf+URZnbeiK0DGwba6+b1sRXTdoTCCw5T\nCtVxObjeSQKRjE+Y+C40EgPp2COLS4ch1O7KEKAijAexmsHzc1XSo7NVU9ja\nfFFVPCSPLyvrZsCnMyzQaq6f9wRJqUw3HEieIiwUld2nh53ZtkK0W2Gg1Wij\nsN9IPA6k9egYvErVSiXICQDb4NJSr1cnwhCFfILuE4JvZVPsW/20GEAKZr4L\npDqDk7InTZtBhZUtd/E/fNWuaYnEbf9DUXM+M7WFZRc2VD4yQ2DkW8hZ/CEy\n/39QKwDKmE1GTyfDFHazlNonohYq7F5IbatB5mZx3I5TaM4/cV65tS54Od85\nmaKW/Ez7YFSvHGXZ8m/UGSiQd92T7AgYWPrIjxDxEIj2GCZoy0BPY6B7Bg4S\nlPWh6FoRBVjmO+OsVkCs1gRSzzaWRyHDZGQr29lMGkeHNvoOzHBEYSUfoaW5\nXutr\r\n=HBHX\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.3": {"name": "browserslist", "version": "4.8.3", "dependencies": {"caniuse-lite": "^1.0.30001017", "node-releases": "^1.1.44", "electron-to-chromium": "^1.3.322"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "65802fcd77177c878e015f0e3189f2c4f627ba44", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.3.tgz", "fileCount": 9, "integrity": "sha512-iU43cMMknxG1ClEZ2MDKeonKE1CCrFVkQK2AqO2YWFmvIrx4JWrvQ4w4hQez6EpVI8rHTtqh/ruHHDHSOKxvUg==", "signatures": [{"sig": "MEQCIGJyPMQpWolfYRUtnW+e6j9XqqYNxZ+gsZ+UXzaUHDsEAiAyH0PMzq48x9yOj7Z7BHWYcQkuRk8KpvlbeRUWTdkm6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDr/8CRA9TVsSAnZWagAAXK8P/2aHljgnHCV65nE3j9wV\n5cxuSnmDoaGlBauxUcfyl44fPPoioK522HgVFdPU0jNl9BHHDJ+aD2dyzHUf\nnB056JJDaGLT25YevbLeA8QNov1G9ScqZIF4d9ku9noyyilnUgqaTYc+MaGy\ns9IEXValm0/Y4lTN/D8IrQDRN23/FXQy6x6D/im/+KolRZM7KOa/mCLqTNWd\n6pSgD7PnfIyBtMzdmzrooCdbvPpH6br0OW6ADMikw35uHvtVV3l+0eBP/703\nfDdCkRnD0gb27M0Ef+do7ZjTLEQsx+qSpYOP/Fg40lwj6f0ObVo4HfMxQ7Cf\n9EOE8FFwELHJQ61N/F6fZAcp1vMYWEhqXwp8K4Kkvrt83YGaWaDrgSxm3Ahf\nQvh1+/xcqJULIXI//X0YkI20GIZDOLpjE159FDpHleUJW5+Y65d8C/5Pz5od\nOl/MBqG7nYeLmnIkc6Bd5eQARMsywx3DoLB1wwMcQ7GEvk3LbSPpwJzRmorC\nNDayTL1sZzOfXu4TCceSobeamDKwDW9lCFbZgYNMzrlByJfvmkEWLpiJXVAT\n9QTYiVSYtZ/Uwiu2nPzkqCiUSRnlvYKJFkV3XjZaf4SbnrAQCMzAbV0gKfEf\nndow4Jc8rE/4VhbbdJ0j9xUJ5s3LzmctZ8m0L/bI52VRZl5BDHDb7skSvede\nNFrx\r\n=3Bzq\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.4": {"name": "browserslist", "version": "4.8.4", "dependencies": {"caniuse-lite": "^1.0.30001021", "node-releases": "^1.1.46", "electron-to-chromium": "^1.3.338"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "b0cf2470ce928ce86b546217f70825577bb01c3a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.4.tgz", "fileCount": 9, "integrity": "sha512-3qv/Ar3nRnRTpwGD+LZc7F4YHDBb3NAEIn+DesNa8TcBhyxf8eDqYwTOa70kiWXwvFjQQz+abbykJcyOlfBfNg==", "signatures": [{"sig": "MEYCIQDsYoWiuTELHoqJmkgtxRMZyiNA1RJkWgCwCy9GXqyk2wIhAJQuYX4D2OqpdpKc1vm2yKzVsnn+78sb7n5dXo67MQvw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJjl6CRA9TVsSAnZWagAAEgoP/39XhDHlKYoK9vH3S3E2\ntX9D9lIwOayDt0PGl74/Xe2W/RR4FjKUvreIjkaS4GoGx9s5xeyjO9REOiht\nsir+lkK9RREU20RCJ1DV4sBAXZdkFuHvv9dqpBV6+tS5/UjUcliljg2LXX3l\nGQrbydleWdMvwA6qMAZRqlXKni2GEuL8zfipOboN7wf32DzzAr2KTznDsLo6\n+ffYG1XMBw1TWIKsR1GyCAQLltORCc9U2VmiNfLhQHAKe3NCHpu/eaSkb9ih\nk/Q16VGnBMiLpUDJPtf1wFtpw+90GSRk788HGcHSecl3JRsI29rzJtAIEfYK\ntipi1ncYWrHKc0wmr9yt9GzanubQMDPlU3BjrqDH9P7kO3xX0lhOVJGxhpTa\n50/m51fShKcymbWCQPQ+Pz/xaurRMGo87VBl2vrlkA5oP0pXSDPya4CfSa2f\nxJ6YlkPCF6ltmTrvPIAyBcN+xQ7CSYHbNHL+X14pD7cBfDkm06tmkLoxMz9n\nCb4tsrAo8KOdJ9VEgjHIByksmXOm3WltiVSTV13Fl4eoaNq6V/rZMwUJigJl\nG8qy4VyEW5/QYF53ZrqhB3wPwXWlXF4wz6zWmmgE1aw7aSZoVIXVeyxDUJQn\n4bnGi7q1xhTibhG68RlYd4CKjpHOBeWHqxAphgAOXDFpIghokIfKCeNZ9f5B\nGiz2\r\n=1mSj\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.5": {"name": "browserslist", "version": "4.8.5", "dependencies": {"caniuse-lite": "^1.0.30001022", "node-releases": "^1.1.46", "electron-to-chromium": "^1.3.338"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "691af4e327ac877b25e7a3f7ee869c4ef36cdea3", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.5.tgz", "fileCount": 9, "integrity": "sha512-4LMHuicxkabIB+n9874jZX/az1IaZ5a+EUuvD7KFOu9x/Bd5YHyO0DIz2ls/Kl8g0ItS4X/ilEgf4T1Br0lgSg==", "signatures": [{"sig": "MEQCIASqxmrLG2q78P1aPLugLVyQaWklaynd9he3Xy37O0OzAiBqRdjUf1Ww/hrPum/33CaLiFjoVGA1MxBeNKcAtuFCpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ2QgCRA9TVsSAnZWagAASuQQAJlLDpenIiH3K3cXHgFP\nshJ+eHs1neOUxtI88Ro79j+idUcM07Q1rygFtLKII1G486V1N2ivZ5Z8+PKH\nPJaSFPP2pl7rcSFm7cOOG5viOqWKhsTOlDA7dac/pArq7jUCiem4ZmbHgWvL\nMCNuLoSZOfOq7l0Noq37GM2R+DzvVWkpKJquuZrB2uSDuuAtnjCszjEICNxk\ninSLkrytcZtLKPzDO/C9iXZfp93qqRT4W8L2jwVvR4B06E8cmNuoSeRtmKQx\nGlv9GMow46ThmbjukNZa1p5VZ/6NFxaclG+9lruI2ac67+q2JywgfJtNVF++\nqcJYN59iDelRlP3ncBHdsH5axTbW5/FJ+En5JbOOC/JQO38iVchvXhm/k6MV\ntp54PlogrUY5YSrYdaCQU3Ro70w4I4Zuhh7EXyWfl1TjwW/vvntYISlojNF0\nG3bZEPW33Q9Zg034NeX6v+RoRlsLS91rnmAd4oQScyHihEp7dZDHekvmiId+\nOpKZExmm7z9I3j3+nO1Auo4i89AqvhzEi58P5UGx7AQc7ool74WiuZkFf+wA\n1wY329AlRmoIf4xgDmB8BdbxI1pJY02zoE5BOEqZH52SNZAMXTDMb1XVnh3d\nWy/knnb5U55kBHOAAdoHEnojNepthiRhwPMg88vg9k2kzn1DplXewvuZHa7S\nBAxC\r\n=puQa\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.6": {"name": "browserslist", "version": "4.8.6", "dependencies": {"caniuse-lite": "^1.0.30001023", "node-releases": "^1.1.47", "electron-to-chromium": "^1.3.341"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "96406f3f5f0755d272e27a66f4163ca821590a7e", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.6.tgz", "fileCount": 9, "integrity": "sha512-ZHao85gf0eZ0ESxLfCp73GG9O/VTytYDIkIiZDlURppLTI9wErSM/5yAKEq6rcUdxBLjMELmrYUJGg5sxGKMHg==", "signatures": [{"sig": "MEYCIQCojQc89YL/kgKGjummrQJTCz6FKi9uQyS4SHQB9zZsFgIhALr9P8hng5zs3se7+pD5+J6lWuK8N0c5tI34dszjYuJ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMiYtCRA9TVsSAnZWagAAUEMQAIliN0SFZKFpdvtUiMVF\npeG3KYl1FTMtB77TfBHXXbYmxCm9sCbIoyFcI2WqEjCZ94yVYgJi74fwA+8v\nYg9DlFZS80NpO8MHg8YztCpBQ+kCTezSKklaukJVpssZTkQ/MH4qka4G6VoY\nc4kCrLrIlaaPIld/E3cc9pKwbdK1awnrIkGmQhxQqzq9NWcOqqret6+X7wBN\nnigsiMupDBFBrY60ZlohYt4JG2/oPNNKWejUO97j5x7e9ZIxfnVEqcnZ0ACv\nRqsGmX3btqgKsAycIDMnVgcTO/i2/tgr/qjtDeyWax1u0xVAb707mopCFYAk\n9NjaLL+XCouwdMXfbllzJ3md3mS/VoDzPH1YqlwK9gT5qZDsufDQ7R1prdyt\nqBkL5h02cf8BkXTdfgQdyIFfXjkbCFuQ0gEkGgL3JSycM6M1dgSabQCYJ/QC\n5VEAhxtSpl4HhdBJi45ck7GwDFBnhCopjzKTqo/6oxW1KxjL/g43qE+saaEK\nhzId7NaqxwfnCoNcQ8HvQoT+GKVIJrBuHv9X01HheZSd2QqOZGN0fV43j29J\n9dWXksCswOs8vFw96102dJug5/FyN8iQYQ0FvWgn3yssYJJeMOM07ZvnY0k0\nYabZNqljgzRvbuE1PLQPaUp1AZbRy5O6hXZyNL07ze6BgJfrdxHNzBkawhG2\nKaSI\r\n=sN6/\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.8.7": {"name": "browserslist", "version": "4.8.7", "dependencies": {"caniuse-lite": "^1.0.30001027", "node-releases": "^1.1.49", "electron-to-chromium": "^1.3.349"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "ec8301ff415e6a42c949d0e66b405eb539c532d0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.8.7.tgz", "fileCount": 9, "integrity": "sha512-gFOnZNYBHrEyUML0xr5NJ6edFaaKbTFX9S9kQHlYfCP0Rit/boRIz4G+Avq6/4haEKJXdGGUnoolx+5MWW2BoA==", "signatures": [{"sig": "MEQCIAxiMM0G16Eo5SL8gs5Fgy5PwBvsfKtWeDO5Uj/PDBX0AiBEVJl101lwvuBV+PFORjhxdF0gkVWNVUE+pegHgLVGjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRXFbCRA9TVsSAnZWagAAWmQP/2c1oG4az/DPv4+psrxi\ntNxZJMKg/76jYcrnHOwmHua/L9g75pASk2MA1b1LM000wSH1wVgQn03TPeKg\nmJaq6KBffTwaXsDysxnhpJxd4bmlGQ0L2bgWLpunxA/t38ji9DoflXPlVd1x\nzDjU12RnTNC7tj59O4IxdOCd1lerqAK1jdTOTeNoE1AjGcHCp9ryi4tabbKF\n2iZTkOvHYnh+hBJ4ypSbcsD64CD7odRChrwdi3x6taeL9AateCWUlI5T7dgC\nZ3BustZDxNPGEm/AEmb0rRMETVpsC46HIg1vZJBk5LIM5KHN3pRIkz9dyVri\nghy8+OHOAoZxK46+GI/Lih1NoIXATW7NrDVt7MNLTRRXX0ur1B7tZ7an3Vrm\nEzVuZ/gA4veBQYQV1J2wxfjlyTIkQ9kZB2AvbX8wKSl93Qr8sRtZGTF1WZGK\nT8Rlkl7f7dan+eT97yxBGDwzxsXM2WS5xOugU1hPDTAPdbtCo8kToj4j51WD\nFhtvHWTmK68UGtYkuwKy6o/8LE2pV7vriDohP6d4Sd6JhPvfPdkGhkcfK4bJ\nwMNaYMU3woDrwD04vSW3qFYpenR7rOZeLpx67dCDeFOhM6vxvyTUqN4uajW4\nEJassZlXIgLBEpybEMSIVvV9DgtiJxC0XgOGUP8bVN9D6zpu0nFYgdvQocmW\naliw\r\n=rD/2\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.9.0": {"name": "browserslist", "version": "4.9.0", "dependencies": {"caniuse-lite": "^1.0.30001030", "node-releases": "^1.1.50", "electron-to-chromium": "^1.3.361"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "ff85c390889e0f754d7bd8ad13412575cdcf5dc7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.9.0.tgz", "fileCount": 9, "integrity": "sha512-seffIXhwgB84+OCeT/aMjpZnsAsYDiMSC+CEs3UkF8iU64BZGYcu+TZYs/IBpo4nRi0vJywUJWYdbTsOhFTweg==", "signatures": [{"sig": "MEUCIDkQ4ah2seTlT8cv3ZgpLzcl8qUFQsNJi1BIyrFzKLEXAiEA8+dHjOuYRQ0TlmAMSNfASUSRGg6H3ydjyTqzK/9km7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVvqmCRA9TVsSAnZWagAA2JYP/3LuIqScYdhniW+r/w37\nJrjrwu5K2SVn39HvS+gkNpkpuUROzs9C1SOMNqzPOSGwGasLsHg6FQQH6gfU\n6HOiAb8rWUQD97udPGpBIt4YGgHUX3T92vGXkiPCP1EK7V4BJd6KHYs5bxtP\n4RgLuNH9jrUgG6djqId0ROaPOt8DNxOEFzDMvcNjHXWxxgR4UTx3nM6pIUYZ\nbdhBFRoXQJgxLRnajO4IRI+6SQiipBJTSEQeyy2CMjiZBsEPr3Woh2kwJbnN\n5aep73+loNv2SsZ1sCEHUoRYUqfUNVng7YMkcxs2Ro9cVod61E18wK4bLZ72\nQWsHU7ibR8/bw811VI8qcsv9wX0WM4QdkgyhY4lkRIUmc+8lwKYCugZ5i1Ng\nuOwZrkQrEzJm3r3VPfvOaMsKEAxF8VGjQQaHBOylL/uMcTa9+UxxLiTo9/IV\nKtrEt9E6rbvg4T9QM3/hjsYBD/WkRMXSr/LXbacUHJsVD3ch/mwoOVLc3p6w\nwZAMeTRcpbFD7etWjZUknDwLu3WbnC1Wyt4I3482OHWb/QeYyc7MkNISNu2S\nf6B82Dxgfnd1x9nq4VxfzxpvQ4zt4ZN0Ri//z9oZC919tTVKt0TW97Ms1gkL\ndFn1eiXVjyU0k3fi0hfqGQjcljoqT1eLNzC/P7u15EEM3VwuMSNnpimYiE0Q\n7Jjr\r\n=Rmqk\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.9.1": {"name": "browserslist", "version": "4.9.1", "dependencies": {"caniuse-lite": "^1.0.30001030", "node-releases": "^1.1.50", "electron-to-chromium": "^1.3.363"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "01ffb9ca31a1aef7678128fc6a2253316aa7287c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.9.1.tgz", "fileCount": 9, "integrity": "sha512-Q0DnKq20End3raFulq6Vfp1ecB9fh8yUNV55s8sekaDDeqBaCtWlRHCUdaWyUeSSBJM7IbM6HcsyaeYqgeDhnw==", "signatures": [{"sig": "MEUCIQDFxi8RJzoIJUT45a8ubLlhO2dRMiv2utTSNKVJC2VPKQIgYxq1Y7DZVbawrKACFTGH7ew6CtPCHyDJAtb8MlYUl64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWbMwCRA9TVsSAnZWagAATOcP/iTtP+SXhUBnSm1oTSVn\n6pRBSGS+5gDMY/RwaXbQO50wYv73Z613ccCOd+mdOjDviGEd7pXSX/SMjeO4\nFG7mVS45cYWGhxbJ3mfjhTJ84Qc+K9snNjTsoRUbr2qg2PLhDImlUfpyAlO5\n+4tDSovJQH/EY2ZCYJgWNMmZKlF55Ddn1wofvDzCzmrjPDpJ2vSBNtKWAvUv\nDoJWpea5sZ0c6g26ct5zQ7lXu1DxHTQKeZd5fqMo48oPlpu4NclJq9erSdNM\nh7FrGT5sDbvup05mPELpjRxrqsJoO30CK/184G+sDM8UoSlOPZU7attuAuql\nghSrtHbSK2u1QgxJlLOONPU6+k2wn/Je74Q//gF7qm7cJA4bI29ikwplyFgd\nebvg8EMEPXGM1Adt9Q25AGisKwPTOA8QVzfA5Kzw2MZYSJkPvD1um2HheKEH\naNgA4Vpc+9ZzWgFO+YhIQwh5PW9tMXyDf3qJLjmexTaHr8DoaIKWa7iu/Jf/\nboBKsSGU8r5TGyajGNKeMPj2kmJrPzpEZJQMXa9az2X3PNEVt14hUxBJ/o2q\nRCqBXCrxGxhKGHLSkrgSFExyCYTAnzGdIcNLOnIX3sgONnjL228clV5tzXfU\nb3nY5TDXKDFaRIgODkTD4JpUAZkjpCAAs0lEwHUDY6DpG8CS5RlyTHgNlzhs\nAQfq\r\n=3Kxw\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.10.0": {"name": "browserslist", "version": "4.10.0", "dependencies": {"pkg-up": "^3.1.0", "caniuse-lite": "^1.0.30001035", "node-releases": "^1.1.52", "electron-to-chromium": "^1.3.378"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "f179737913eaf0d2b98e4926ac1ca6a15cbcc6a9", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.10.0.tgz", "fileCount": 10, "integrity": "sha512-TpfK0TDgv71dzuTsEAlQiHeWQ/tiPqgNZVdv046fvNtBZrjbv2O3TsWCDU0AWGJJKCF/KsjNdLzR9hXOsh/CfA==", "signatures": [{"sig": "MEQCID+8o0ecQ0Kt7kZtwEGzWh81tqLmR8WDGs3V4VKpqDxWAiBQV2XBeJRg9hJ+BAlt6n+wqhL/TMPcWIcV9alOnYFttA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecus/CRA9TVsSAnZWagAAlC4P/0IHxKThI+6zDFwsFaJT\nI/mBhKg573Lv1V+ZiKKHyM6ISUoNUUOFTC9OtGS0AupCg0T5KB1AtTdvSJYk\nt2GWzCIHOn6awuZipslRNPfVWauox6i8FIFv7r8S1OEO1vGhUE0IrcfMtPie\nMG/9WJyN+2/qMXDuqoDWHGGEqoN+F9PexrRvpynrC+K1SduiLCyzVL8c3xNX\nqKKUJgbVFbUXetukZilztIaMdxPAnGSZVVHHJsUyDd85NNfIa/z2xE3JLXUW\nYUkXn4HFzrbvRQII4bex+e/0Fv3lup4r1o4hGrvYF/HaFlrPKqvddZ5vljt8\nfQuXkbMEPtKuygOO6curmsUmSnvh0YkN7snQCsTRAEMAsaS4nK5sa0POBv0l\npxXPb5kTYT0eZ06is8Fe++3uIw1MCM4PRMHZkO/PmJT6QvlQSEVMSNmiLHRP\nqxv5CAVnb6j0S0zmX08SIRVHGdkRiN6+3VzDU/BTvfjhV49GMYRLh47IjQN/\nXR4ARg4q15oGjwyK/eH53XetjtlspVbbWzjZziZU5jwINcY3rV3QUICPcs01\ncoxdp3SWe4/Dsv1vyemMCLPvsGOrQDHDXARQcGWBHSl2i9/zLRUa3goZBgwW\nvBAyWiXJ9/Qw4rjCUe3HHH+oMK19KSKG5fiRmnkjO1vVn4iiFz3/timyiM/U\ntOYR\r\n=6dgv\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.11.0": {"name": "browserslist", "version": "4.11.0", "dependencies": {"pkg-up": "^3.1.0", "caniuse-lite": "^1.0.30001035", "node-releases": "^1.1.52", "electron-to-chromium": "^1.3.380"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "aef4357b10a8abda00f97aac7cd587b2082ba1ad", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.11.0.tgz", "fileCount": 10, "integrity": "sha512-WqEC7Yr5wUH5sg6ruR++v2SGOQYpyUdYYd4tZoAq1F7y+QXoLoYGXVbxhtaIqWmAJjtNTRjVD3HuJc1OXTel2A==", "signatures": [{"sig": "MEUCIQD3rCcumNjAiaEkTPdhnafKA3HDKB03Ge89dM7qvx9mYQIgSCUl/voTK6Kj9FYklWBMI/hvWq/uflhTIE7dPh/wzGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed/f3CRA9TVsSAnZWagAA8d0P/iXPYA9s0MIH4n12yf9B\nJgOGv/wAxq7SXV5w0CZDbyKwXk+hInKG9igPbR/SkNwVF9P9yJl6v+rDuj8d\ncr7Ut/F4Qc89noLj11QIkx7wWG+8qb/YVD2SE17k6wHcfY2UYZV4xv9L++7W\nz8ktyUb2rmDNIq8UyHnzgLxd2OqDoRfEjIiL/INC5UOXrvFE1OQ8PUaiyH8U\n2XLWaJxtXQ5YP5G0m0CE+bT6KBCTfAVuse3T/PaN8Tgk1YgoceQA9hKMZvdy\nVZ4rkmLMaMLz+ZjC0ZlivpeN/G9TdEPdVDMzaTBlIMTQg4n3EJQwOSj1+uKA\n8HveSJIKEfqbt9pz5joLwQnIGSKtwFSAVseD18dRVQ7QBmFY9KFCASLbbmH7\nt6xAXg1h2o0XSQtp1+PqPvIzoFFnctJXwP9PHy8ma5KWpe1VBlJ4pTrj/K19\n/+llKF3gdRgsNoOZoc7MkdzPQiNe5RsfUoobLWoDdJxMPpcEQJZxtidfgaJw\nhNPo0xa9dCPxPvaqCUDcQWhH09zc9YhHhP3KDqnqAi351JTebLcUezjCYpyn\nXwJNsx6zMRppc6uIS0MDQ5hzUdoRyV8CUGvjb7Eq3DhSokraFCxAgUBofIBe\nvmMBOS9a07Fh/Mit5A5jidNITCHViClsqZg58bMELlmtfIxNkoFvw/26Ufjd\nZgFl\r\n=VKtM\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.11.1": {"name": "browserslist", "version": "4.11.1", "dependencies": {"pkg-up": "^2.0.0", "caniuse-lite": "^1.0.30001038", "node-releases": "^1.1.53", "electron-to-chromium": "^1.3.390"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "92f855ee88d6e050e7e7311d987992014f1a1f1b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.11.1.tgz", "fileCount": 10, "integrity": "sha512-DCTr3kDrKEYNw6Jb9HFxVLQNaue8z+0ZfRBRjmCunKDEXEBajKDj2Y+Uelg+Pi29OnvaSGwjOsnRyNEkXzHg5g==", "signatures": [{"sig": "MEQCIAxXz2AYrK584wNq9Ug+dunaUYpsJQSYqhsJz9ZAvjEDAiA8EeromL3Wts3G2p/dO7v2ekZXDnXdklyJMvIe2xradw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeglZcCRA9TVsSAnZWagAATYsQAJW6idlTIbxBx/et7Ph/\nCntgJcZuSlC6K80j/foh5j1awoE7JQ5xluNPRRjytsib9aSzFD9Q3VDQVJaL\nvDBo/eUtWBuziw4gcK4XQfTVnY/j/bWCY77aFL8hwxwWsNGwdB81Vql8odcH\n9E8FF/C1gNlvwUem+fxkL+yWymcsfEuJ1Ny/J07Hz5In3ocyRHQbuBu4U+2N\n8paSC6bgiNXu7Tc43jgE2Zz5VBVs3AHmxBQECx1rjjr4KjEi2lkzPODiqnu0\nuCkp7XBcxjcACEg6bDsCuc0MCQBmieACVukye8+fYczl6WVM69UpDuBXW8Be\nh5m7lPdXOQ4toGQUKNKgCMLULVnSdzUO7WeSGWwuYxQYxXYg1Blo2B+oFSUj\n6Uyy63ku8tilo75D8VkpMqmhlGqVwGD3NAUIDXmsq5W0W9Eh1Hk6hdhfZnhU\nGEf0vpKCiWv1Ks0SqWQ7xNkh/aoM1EHuXbSPvLMG3P6qFtFaaX3ob2HDFrh0\neLmNCks3X9dRSWP3tAxUGe0yrELbC3zS4oehHVLULCYhfcCGQf+CbNa05BTK\nw9xj2u3spQp9aanEn/+1Eox18R6Qi5kRoCgEhLcPb4DGdJJyEyfnLJ8TWRw+\nDYpySXpxqOnZrO0y9hr6cXG54FkbiTLPjOynOI69IWRakGMUeno8Zz9B02xe\nQNg+\r\n=XTe3\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.12.0": {"name": "browserslist", "version": "4.12.0", "dependencies": {"pkg-up": "^2.0.0", "caniuse-lite": "^1.0.30001043", "node-releases": "^1.1.53", "electron-to-chromium": "^1.3.413"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "06c6d5715a1ede6c51fc39ff67fd647f740b656d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.12.0.tgz", "fileCount": 10, "integrity": "sha512-UH2GkcEDSI0k/lRkuDSzFl9ZZ87skSy9w2XAn1MsZnL+4c4rqbBd3e82UWHbYDpztABrPBhZsTEeuxVfHppqDg==", "signatures": [{"sig": "MEQCIDFkWegl58bvln5HYO/caf3K20IO8pqPjZ6q+QuPyDsOAiB0XgHAlQgEGA9eDOpumeY6DfpAXUbMntLlvYxfa8ubTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenhfoCRA9TVsSAnZWagAA2bQP/2npnJsAjj9Hn/yR+34L\nAy5AU6PtHcj1M6NpR9W7O8tcUBQKExhMx+TyRxn1Az7WA4XiledovhZVRo6h\n+rRu3fnW+tU98QLQOE6OIvn/E63coIX3pWC/f0E88OnTgDlcR/ggstlv+9A1\nC2PKdhwqdwLPzsuPO7u/dlCtgrF3dgAkoTYnA/Ktw58dvNsujSPB3r6CIriy\n8jzax+om+xyBK+m022ENBObEaDYm6Q0k3TdHJPoYxlsA9knVVd19PuoJNZut\nv//lVucS6MU+qoMpXDgmc+GEV90U6Up9AMYc1ZYSZd5GOI7jMRLfgALQBxAu\nbu5BZ7Q4LVWaXz2Stt+XlE0TlW+TrjrlwOh2ZJsjKrBH9uB+PaQD4Rg3j4NG\nV62pze77j1IfYpoTX4SMBcKYm51yCQtw4NEP7NGwo4sqpTpSROLXaq2h6VWz\nSgS1nLunr/tN7hguu8f0FdmJwRUbRy5Hlw6buaBpmcQYKDh8nROrQS8CqQbw\nHbdt5IyRV458ULUBHBJO7YEpMQkBzxUCzMD/z4FBHpqRHQCmAQ3Us0dTYsa2\nu4FeEq4fCw6qXuKJuyF7rgz5Qr1CWmkqEEmG/fUtcXtsu1V1pvbzosmPJnVQ\n2iHF/NRcbENxxDXcFuo7jXuSTk3wPVJf3lPdJu1JKDbOYCaA8mpU72qugf7S\nkjPw\r\n=EG1+\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.12.1": {"name": "browserslist", "version": "4.12.1", "dependencies": {"escalade": "^3.0.1", "caniuse-lite": "^1.0.30001088", "node-releases": "^1.1.58", "electron-to-chromium": "^1.3.481"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "6d08bef149b70d153930780ba762644e0f329122", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.12.1.tgz", "fileCount": 10, "integrity": "sha512-WMjXwFtPskSW1pQUDJRxvRKRkeCr7usN0O/Za76N+F4oadaTdQHotSGcX9jT/Hs7mSKPkyMFNvqawB/1HzYDKQ==", "signatures": [{"sig": "MEQCICe5uUA4bpJ17osXlZzx2gWo3HebSScbI/ZfhDeNicliAiAUMlXOPxFcP3g+EVJ4VkrR+t9EyJdlOxNXE1pYoUymXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe84vQCRA9TVsSAnZWagAAeGEP/1ZznBXkGvBmXLQ2hDek\nbChA0Zd8nP37cg0qgAE1rj+yzEoKKsJ3qfS6PhYV05lN1u3gv/jr8as7MBqz\nBsHdklmApSZBxn5aUJnZxurHAa4sgFV1Ys+9svl4zJof3//7qNCtudcHrSZe\ncA0C63rWSO/PR/9Kst2xDf/+5xeE1CIEdOnT89oGjc2J50BskVxlGSJdF/P5\nCLBZcZRf6f/Nxtm2RO7iLCS9moA7kyQhXNx2WGkF/KyBQvNQkc++ca6aMZ3/\nd8V3vdjJFABHUMovbq5NgYfXfKuElbftsz4LsM+EI3CuLm8r3UqgFXa9sWDe\nlZTcW/c/cZMGLphiYuUH715F1UROn2a1CYsHymZ1u8PcGGpB9ZWOJMN8U4Yh\nC33ISCgUDvGLLOTg1H004bsosxTEAO3ZYn0R+Ex0Ol/TfruNKo+N3laPCLUE\nEOntIDWctvYPGOiTmbiwuY37DdOlyR81og8sULpnFPdeVMYTIA1uvAWFrbg3\nrTh2P7LwxljKpd+h0NiYJlABcoZpzg9aAsYwJWISBzp849Xllt5khV9wXDXm\ny9HZh/qKmAQdDHgMghtnZY9REHqErw/2D3pMkjL7Yea7ZjXo+Lr3NKKfak8f\nNP2JXCQ+F4Ifj+tTpVmuegBcHLOr1VyRMNFudxy0U+erFmDLKr2velPMmZhR\nHDq9\r\n=oG1L\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.12.2": {"name": "browserslist", "version": "4.12.2", "dependencies": {"escalade": "^3.0.1", "caniuse-lite": "^1.0.30001088", "node-releases": "^1.1.58", "electron-to-chromium": "^1.3.483"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "76653d7e4c57caa8a1a28513e2f4e197dc11a711", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.12.2.tgz", "fileCount": 10, "integrity": "sha512-MfZaeYqR8StRZdstAK9hCKDd2StvePCYp5rHzQCPicUjfFliDgmuaBNPHYUTpAywBN8+Wc/d7NYVFkO0aqaBUw==", "signatures": [{"sig": "MEYCIQCXn0JwlbLjcJWl6w6XiphoQOTiLkS3dzgAjuJ1HJTEagIhAPRiugpAbg9HGN1pFtYHk8nk1ySNWu4KucZTyoBsgHY4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9i58CRA9TVsSAnZWagAAifcQAIgrXtDwJoO+6k9GPU2N\nHKuXXrDk/LTPitSQL0eZBomUiYiUrYhCjfUhFECqBN/R6/kAd15sR8ElTZbm\nITLrUgz2sH3tXkv4T0zQ59ISgkuO7O/MpGbZfNaPX2qNYIJaSUUoyl2v6Lpo\nNZRK8Ab9XYavqfWbia5i02YtDqvXoTuWZVbHjWJTKcqnlqYX/Es6phzvDyFx\n8K7RtKipZjSRogKW5OhA/MEI2cEGSj0ZiPGQcNxX+bsMGpkIFxQPcGPs6cu9\nRdnsFBxTJ6Cz+TidYItFkJq/e9sfZPJuPC7nMX47mjIvA6EaQERfW+dNugsN\n8NwCXJiKQaTHRU0IOL5jzgs+t1jjLUNnZsqWXUH3q7x0gZ7Hhutv0Wf4mDZL\nUqFrGQ8kuq0WZWe/HH3fS8EfZAzsT3l1ev5Cp2LSj/5jeA16lwIAjFd34FxL\njBTmnjt2eB91MYlcP5IvwHa2UJzje4BFlkNLKAPkSxqKGKFcTU9W3Cejbt36\n5NfnASjY/q+HNugmFkK/ZiixtRY2Te65iLZmilUNkO5Wx6mYM2xNKMmgKdLL\nmbgNajzNL54Mc62VaBEeMZ1uR+F1pVC02BpPg02BNw9xoU5/yn9MEHMi9FAt\nrzhXk3Es05/9JG8v+G5lsRYsyoJS9lt5PzyJdwV+B1MLtl0GHQ/utt04avdY\nY/qb\r\n=IGZQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.13.0": {"name": "browserslist", "version": "4.13.0", "dependencies": {"escalade": "^3.0.1", "caniuse-lite": "^1.0.30001093", "node-releases": "^1.1.58", "electron-to-chromium": "^1.3.488"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "42556cba011e1b0a2775b611cba6a8eca18e940d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.13.0.tgz", "fileCount": 10, "integrity": "sha512-MINatJ5ZNrLnQ6blGvePd/QOz9Xtu+Ne+x29iQSCHfkU5BugKVJwZKn/iiL8UbpIpa3JhviKjz+XxMo0m2caFQ==", "signatures": [{"sig": "MEUCIGAMqdD28I5DkFiao0oPpo88A5dZd6iApGHnxqiXPa0GAiEAy2LO6/jHTu4IbVsAc7BBXJKThpLGR4uvhAqm+dbDGHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfASX6CRA9TVsSAnZWagAAenUP/2r9EQKsO0BvGhO5DyeE\nAjOVaea4tPOIBzTsfYoauCiC4QKmWScC7Ga0zpYTrlvdn2XDPHHEQlv6oM71\nqdnh1m6Tcchy9Mq/V7VwPupcQHthRd24CKP1ChA0pVDIiNJ2TD67VP+28WNn\nZHIgsR+HuogDVrytuJqh1qQvIM7ApZnnz5E08Ka1Rvwu7dOtQswosBJ0oa6O\n1Yie/aI0JrmBGwSvbLr87rsZKnNvASQOtDpE511J/Jnq0L6nlOlhX9zAq0+P\nLaoJey1QoD0lnSWSo39Rxm4xZL/LzU9D7sxVKJh4kCpMoZ5b2LI8ZsVOy2jc\nEfBFv7mp0rts9+toCEvEgAvtSFZIMleQb0vbNocpuCNR1J2bmAiZ43iOTIIv\nCpF08tfsujGy69R1wQu5VQeGowcyHf7pK2cxpPsIAvbIgQHGcUNgIoWLJ6XJ\ny90y5yTcbYpHlwHEwSo9FPwfNebyEULjtTqHRTRTaTS4la1gMnT5z6Zm73jz\nfG/x+lDfcFwXcdsRvDTsnB7OSTDeDluFlrZOquKV1qNQy48+TmaIWaEfE0/e\nDvL7gzxhfycvXqxmU2umZZFE8WdYiA4cQV3p59yUF8QlfeBkIo0tsnmCNpbk\nUHJeJimFlzP3GF025Ge07C43DZu8VIG26DbPFiT4y14mft2kcYhN5F1gNcns\ngM1v\r\n=9fws\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.0": {"name": "browserslist", "version": "4.14.0", "dependencies": {"escalade": "^3.0.2", "caniuse-lite": "^1.0.30001111", "node-releases": "^1.1.60", "electron-to-chromium": "^1.3.523"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "2908951abfe4ec98737b72f34c3bcedc8d43b000", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.0.tgz", "fileCount": 10, "integrity": "sha512-pUsXKAF2lVwhmtpeA3LJrZ76jXuusrNyhduuQs7CDFf9foT4Y38aQOserd2lMe5DSSrjf3fx34oHwryuvxAUgQ==", "signatures": [{"sig": "MEYCIQCOh7x6SpcipHLFwjCBnijrYkVCO9FMV/S/+6/i0DwhDQIhALoD+ebpSS8LB/r9fCX5d6jhl72uVpuqTT+H3pVjATYc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLJBRCRA9TVsSAnZWagAAf1QP/R/73zVZRMdvpm8/FImF\nrGP1HdrWsvKklpZBeZHjFxWwITZxuXYUdQhCpGR4HDAm7ECe/tPpku54kKeD\nNEFpfcSmOiP/lUhtVauXM0ZmTYMtwVyokTbwvCqTf7L25L8X/sQ0WHt4Okc6\nLjP86g72XNEIG6uqsrKNNua/Dl7/D47t36Bi9o3Ja0DyxLMrkBUj7ItgNXxM\nEn8tR/kbHUIGcWHzQunOH8yQWoWPTgfXvhoPNfrcrrQYHfxRUgYkalH1e2xw\n/wUetDEz3aEEmAtTjy5Lu3TtLIKCfopjQA2DOJQPIof2G19pG2VqzbYPtrkn\nx2cLp+Hxtvi7Bb0u/UI2yLpTbkOXJ1AOTBDexEZ3CIszGdq4dGBgb1GKZNBU\nn050dm0O8JIFxruGOvNvlhnmpKvtrurcQ3Yhfe2QB4DFR6rATVunEH7OgfOo\nZs1BFNX9ys8QhO9y2QJ5esfx7NOVdmbrkCDmJjgFN0q8MP88e9DkawodW+pt\nD3bmIra1uSi/gTp/vQuVobre6mKnzZzg5d6ekW6bS+/yo3zJktLZfE5AL1GZ\nlXFc9ShfE/ehXKz6IOZT10wWtyFr873HoZx0XVQkFnHUTrl49iFwL9Xo3J47\noHlYZcBMxdsPf5OMNt6mx6SlqZvOPP2V9Bqa9tGCkO2kkCfCq2i3iWjKqkDQ\nq2++\r\n=A7Xy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.1": {"name": "browserslist", "version": "4.14.1", "dependencies": {"escalade": "^3.0.2", "caniuse-lite": "^1.0.30001124", "node-releases": "^1.1.60", "electron-to-chromium": "^1.3.562"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "cb2b490ba881d45dc3039078c7ed04411eaf3fa3", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.1.tgz", "fileCount": 10, "integrity": "sha512-zyBTIHydW37pnb63c7fHFXUG6EcqWOqoMdDx6cdyaDFriZ20EoVxcE95S54N+heRqY8m8IUgB5zYta/gCwSaaA==", "signatures": [{"sig": "MEQCID2dhB79WMFnM0XJGXrkjytAm5jNFwAnrPhftRYFB/afAiAwOYRm9PmTNrilbvq6W6DUbmx5Eke8AJIOln/ZJzxCXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUqHyCRA9TVsSAnZWagAABOkP/0Sta31oPVNJ341fyRmd\npGPhoAD2iHQNH4IPxHWI+2/sk3OAJnYKC2XhHEF2j/xenp6eR66q2x6FpTZK\na8ZeUc3z3IZNEF5mm4EijDlIjUpzzfyhqxkWvcE5NPvXQPFpxbxYa5w8+3W2\nG+m4f1kbVJ4HXH+orEzFs5JBVdA6PtZFf3UtXXG1pkCiYnxgNY+xupx53Q7K\nuGpG4Ac5uTqxhaY5X+CDOGxpehnd9fFtBdD0e//gz5Dy+y9JOCrMd1XofLYR\nVLDqLGbz6rGh2lqhp3EyRUkVU1h4atY7CqnnpxGDyUr21ocUhXXX8hRdVK8o\nn7aHWPnSC7b5XSOXuhmuFpRc1dFclpvqrstyvPc1Pg50QMmeE+5aBzqNaGaO\nTdbwcjNMlveFgKEWDvaEOl0xY7iZxqHMkZvefi0ZpZ/zFhWqp+hhvw1s786J\nnKbLqwHaifdps1tqH1UhicHf/2D8D2qisGpKD0mTEveL97qXc6k7pjplbXJh\nOepJ0BKtyA3MiFjWIa7ZvBCO4fCZFnGuLSWHc+ljxRCDMWQmC1vSMspoVR+E\nPeX3/ZuI4wgcCukfDHqCu6M4iFYu5IIBWF50tc0W6hd4X7TiQwEBQrsQsZPe\njxbYcPCRZIF1XmN1e9BNU7miKcEeHN8Rgbpd8F9J4Y3osVCPI+RNLabvBDUh\nWY9O\r\n=2rEH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.2": {"name": "browserslist", "version": "4.14.2", "dependencies": {"escalade": "^3.0.2", "caniuse-lite": "^1.0.30001125", "node-releases": "^1.1.61", "electron-to-chromium": "^1.3.564"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "1b3cec458a1ba87588cc5e9be62f19b6d48813ce", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.2.tgz", "fileCount": 10, "integrity": "sha512-HI4lPveGKUR0x2StIz+2FXfDk9SfVMrxn6PLh1JeGUwcuoDkdKZebWiyLRJ68iIPDpMI4JLVDf7S7XzslgWOhw==", "signatures": [{"sig": "MEUCIQDHTJJlm0TMtBpayBIsjjoGqJm5Mo2TnJYZxGigZM/BKAIgUpd8LgnMPvliGBGSLAsoqllPdB+gkA63mWJ63lu2aao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWSNsCRA9TVsSAnZWagAAtqcP/i+lCvp3uvmrhV8CeMl9\nRGhPoQWdaOD9hlI1SZEOkaiDNIiGXdtFu+ol3Ex1UPYbu/KGDi/ZJ5a6cRbz\nl+Khp0OZyd8EPrX3jvSWxdS946D50793nIWGTqzeXZdZoHp1JsevAKx0/uPX\nLACjwrwSUB1eeZbdPPTumixka1weN8DLgTo0frzKFHMpV9F+lCl5NjxIFprR\n0S/TbVYfyVA/HkeyQfUX+Lx/iFfT64bPJbg3bzXnYztsYneGwEmcufMVR20z\nfimqYcl3CT6agXZmkf6B0Nc8UwElpDKuuiiWLqnhu5RPW1rTH0ZaorhBVZ+2\n/ZdrJzUmIyIRyproyb/6qxkRNfWJlCa0rzZuSuh34b6jw6X+H6O1JdXU0lJb\nIUQHP5B1urZSkmEk8eRgmvV90GWPej8hCdBcRb2mDd6o9EDdJ13LtM7TBuYd\nlRQQB0FnandcRWmbF8Br4DRurW7g8lJ84dAjVqBlyurkHrE7iFe+bDwlv+sL\nwPgA9QmAOgSHbBU54iV4h1UZ6LlD9fikM+spQ7FeQMvZP+EBPikRFYVOK52M\nV87DvGIpEZB0lQeS4P8DAErhRQTsUfNZAH4wdBPdkMZp5BsTc2zFISANVbLs\n6RV8slytDOCL5Ly4X24RT8YsgE4NjREfhO7gdlYZvNSsxgg8xANzINz1tvmb\nVQBz\r\n=Vni1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.3": {"name": "browserslist", "version": "4.14.3", "dependencies": {"escalade": "^3.1.0", "caniuse-lite": "^1.0.30001131", "node-releases": "^1.1.61", "electron-to-chromium": "^1.3.570"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "381f9e7f13794b2eb17e1761b4f118e8ae665a53", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.3.tgz", "fileCount": 10, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON><PERSON>+YqyPO4SFnz48/B0YaCwS47Q9iPChRGi6t7HhflKBcINzFrJvRfC+jp30sRMKxF+d4EHGs27Z0XP1NaQ==", "signatures": [{"sig": "MEUCIQCbw5h0vMpEeh9QL+J90bOxMfYLt3SpjBso7/1HI3m8zAIgWTC9sYUvMaxjajEIf2f2OlRnH8d5ZKOm9Kd9vIS8ptk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY4JVCRA9TVsSAnZWagAAnn8P/2eBOtjrsl9nPVlHuSde\nBrLCGKheYJHwxEwCHVIwbN+lYWjwAN9ILCJUQPu3aHmGq+dijAGxi188SF2n\nXbG1Kf62R8YY+9BSMjGGBEyX249d/epeT5DgKpbPeA4hK/3ASe1QqFRFpqdh\n6ZaR4+4QWmhmNFPGce2vN3Dg37cgI9vup1zdUrLtysY/ECWSu7hTQx2lB7xg\nhezeTAde2nhK4+H1goO4D+lXLwH/SBondtNO9Hu5Zergk9L2amTjRR9t0tqO\nIeNj9D3u35go8KEJuD6s6MYORfQcjZtcluf0bpTxfYQwbwTxyl69fk5KwFVM\nxZZmW5AZ3neGpdnv8g5Hi4OAgoxGzFVbykyuYh8t43MVDFPc3DYdEuFN3Trj\nb51/l1aDsYlUQba2J4DcYS8dBNQO1tTmeUYliFiBQIRvYdLOQllnBO8dsXQ2\nd1cVJbqHriKrjc1aHCE65FZV+hS/a0yi5yuYYeUQcLrYs59tar2f9K341zwd\n8AFuHcKA58MiebfWTAvliGYgC0lSa6aTJpdZs+nQRwMLytpuBVlJiajuYcO7\niOSJAZDdqbgtkc3dge2W7k9fWGzz4USO5s5WZvea8i30aJvWHGsQd3JvMG65\n2Kc7DrZMFNCvS+YWg6Wk86JIEDGIBEpvFG2Uld5d9X5Pk+wn/d8KvJHyFBP/\nvc5M\r\n=LLls\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.4": {"name": "browserslist", "version": "4.14.4", "dependencies": {"escalade": "^3.1.0", "caniuse-lite": "^1.0.30001135", "node-releases": "^1.1.61", "electron-to-chromium": "^1.3.570"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "66a18131439f9e16c3da7f352518dfa12f60b0e3", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.4.tgz", "fileCount": 10, "integrity": "sha512-7FOuawafVdEwa5Jv4nzeik/PepAjVte6HmVGHsjt2bC237jeL9QlcTBDF3PnHEvcC6uHwLGYPwZHNZMB7wWAnw==", "signatures": [{"sig": "MEQCIFfWcTI7UhhBQDn1G2SpBSTRiRKvbBSrsyWlErnq2ym/AiB/wzJylJ1RwhMMaU5duZH4Gsa11IhN9NR7+S7Qv+e8ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfak0ECRA9TVsSAnZWagAAAMIP/Rg7eKcyvwpnI+JIScjE\ngE7FPEWeBKiGENuOm6zjfiG1+W6qxsXPyu7Kbr5nFCLPjuaDOZPCIATZdlHp\nFacyGvBDbK4KxqibKE239Dh5bDaG97NDK37xGGsp++8ftyWmTgZg0GUjyDCl\nw91YGpRK8bc1tv47QTzNObs1rvuF7KucoNvbvuNBLbxqr1GpTxLeCh+HKrnZ\nEtcrTNmt+m0xodeVOUPXScqedWnoVEPZqxy3x/5SMbY8y91Gow37lW8AccfP\nFqjT7sVCkvNOHrAFJBUJc6KxXd3up10BM6vgJOsAEGTyEqcvpGC5xoSz4Nk5\n5Pkmjuzmf4yRuDcnXNKeil8oFzLcGPwnZQrxDhPcYKDDJ6KikvEEFHdT8ede\niR40mbRFJB+EtDV14l5foKROGi4toIx4UUFwewG41iQSJtQ3mU7I7yp1IkaQ\nA92U7/10bJFDOYEBCf1j1FK5jmMLNgHC2aWCy5izztmFMVEzCscRJV3ioTWb\nhuwCdv1tpOb/jJF1HT448m3vLyxtuiYA+d0KD+kf5VU1mnhGlgFJNLe293A7\n5PRcgBIoRo8xszpi7nCUCBFITXeXAPzFsNQ7DNXAdx8oaOlpeVGnzIj2mnCY\nxZqwJaU0God2YTm88HQJyvaqsGIS8SC8dlNBgMPUXx1VRsYrbr05MwTO40wr\npvXP\r\n=kB/M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.5": {"name": "browserslist", "version": "4.14.5", "dependencies": {"escalade": "^3.1.0", "caniuse-lite": "^1.0.30001135", "node-releases": "^1.1.61", "electron-to-chromium": "^1.3.571"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "1c751461a102ddc60e40993639b709be7f2c4015", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.5.tgz", "fileCount": 10, "integrity": "sha512-Z+vsCZIvCBvqLoYkBFTwEYH3v5MCQbsAjp50ERycpOjnPmolg1Gjy4+KaWWpm8QOJt9GHkhdqAl14NpCX73CWA==", "signatures": [{"sig": "MEUCIDmtCll5ixFnL750Ip1beGmi6vWEQo9A08hqhdzvwSeYAiEA4gXQol0LXHy5Cfqpp+f+xGypXQZqqCZjbsdj+eNa9l8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbTc8CRA9TVsSAnZWagAA0KwQAJa3xlIrkh7OMGnLrrIC\nOO8SDbp86+Enh46iZgO/mObu4i//kdt2F9Clln1fpuYk46/lfZsPzKmgH7XI\nPutcTwBtxOLPr3Yld1/B/28Hmv0PdNMfnoptLZ5z73KyfGILuTj7B0o1lnRi\nLUyAysdoq7/gDkxcBCsdb0v2sx0DLDh2aGWmJMLQ9R+Fk98QtYZvIegLr+8o\nH0+1SWsWVRexln6U3ABfTI5+YTum702RotHABZzcB/5WlPU2fWZFsxx4uzTD\n8kwX8OPG/nHEu5hYJMmFEk9SIl5mvwo5yNVw8w1wE/SIy9yGCtCgA0vJV8PO\nMDuzppAjBQRlilDPWDJikHEej/uknjsaOwxfCgkkv+RCIXz+rj2a2z4Ktvbq\n0gC4AkPTy51rSjmZ6U1OjcRWh2K7qcs646BJNWB1RKWdweY1x490at3lhB1u\nVGHMecmr1MpsyRCpnHI9nQ0I8F476BgCnYqabqO3K5enTSMOfVYiaY4Qozgs\nm1kPoY+r4foKIVGhLlcyhYI9jESZdaHYIxKpgLd89onoXMJAKtwP309+5FNW\nfWS47A2RCqy9TIBvWEDAECFOwivwEpxp4E6hzGE6dmD0yM9pTvliPeM7Q2Xa\n+eBrdMTzLlTBN0GRYEMQbYyZKBbkOXt9ZEC7ZGIeEdw34hKdm19t3M1vm3tc\nuqQV\r\n=895k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.6": {"name": "browserslist", "version": "4.14.6", "dependencies": {"escalade": "^3.1.1", "caniuse-lite": "^1.0.30001154", "node-releases": "^1.1.65", "electron-to-chromium": "^1.3.585"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "97702a9c212e0c6b6afefad913d3a1538e348457", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.6.tgz", "fileCount": 10, "integrity": "sha512-zeFYcUo85ENhc/zxHbiIp0LGzzTrE2Pv2JhxvS7kpUb9Q9D38kUX6Bie7pGutJ/5iF5rOxE7CepAuWD56xJ33A==", "signatures": [{"sig": "MEUCID7MKQqdewEzQevWRVGl6JrmoJcdGkVaXWuGjmQHsN7AAiEA5J2HQLZv3az4gly/mu4fx8HUwpwQGFcSpD779XBShuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfntBTCRA9TVsSAnZWagAAlxUP/3Wc4gWqrbN5kTbWW089\nBI8jXiVK/Qj5iSE0Xm5pRaSwM54fubXerEGLbkn8H7MMdcRPOztU1G9+zR8P\nD9uZGl7+6ZuMjeY5iSP7AGFLjmzMPh+gma6Xazj+c80PMjsbNCVm4ToR/LVW\nbYwi9mbWUvc09AoYyPaRAhJBPB5ZZQiyIaQiL0yVOA4XbM3EQvXn2us5eW6D\n/GUZPUa6YCe8YWb+kqIwh406bRKBDROJvTQFkbmHWwCcPVU7Hf0RKpaTticr\nvRWigeI5RtxHwe66NYRg1IBT5agMNzWhPcS5JBYo88Bo5U6iGW3dyslNM9hg\n2ZPz3n+MvEJ0T/NzeRheHk0n6yFsXI2KCwXEgaez/uVh/nq0W0ftOdYcoTmA\n0RpIEa88LpZfN7vtHFjtUhXdid+lXPfZgZcwCT/6/puKMm5l5aOhl+MSSn3R\nX6WK8qXi7VPo+KaAq98Q2/Bc0SHwJoKTTTvAAqYnt82hrypi6tzocZ1aXueY\ncDt45p+4tdBfuNBlcnLvqtYM8zP1WynfN7MFIz3Q0m9cdyYVNK4+mo5mZ5XT\nGuD4OSt9KTaz9m1B1QPMqLxs7J9CJTcxPF2V2hQDrvH/9qGawj+2QCdQ00JZ\nfjN9M4LFJ+1yW591eJinLSAk6pj+U/ZU/i9wZTHIiQCuu8Afd3GMAPvilDfR\nsog9\r\n=0vTW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}}, "4.14.7": {"name": "browserslist", "version": "4.14.7", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.1", "caniuse-lite": "^1.0.30001157", "node-releases": "^1.1.66", "electron-to-chromium": "^1.3.591"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "c071c1b3622c1c2e790799a37bb09473a4351cb6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.14.7.tgz", "fileCount": 10, "integrity": "sha512-BSVRLCeG3Xt/j/1cCGj1019Wbty0H+Yvu2AOuZSuoaUWn3RatbL33Cxk+Q4jRMRAbOm0p7SLravLjpnT6s0vzQ==", "signatures": [{"sig": "MEYCIQCI5t43H8xTGlfzG8ZEI4ehzhq6qjjOxQ8XuwjXDWEJygIhALYzUQqCse4/gTBP0Vd2c6lIBQlx6bo6TN1MUsN46CVA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqYROCRA9TVsSAnZWagAAQm8P/j4iUq89CzcxW5uXN5rk\nknBPvAAGBQa6U96zKyg7zAPsiGxvgERtXDjSSEQl8VW4s49w+H35GqzCBMmT\nULNeqQ87xsvUI0/tupwhnanLED+61liPwI/gkZsR/q0C1HIRnXznJP3QkcXW\naHZNPqtJs+Mbj7sc2yG/miCflQt2oPmmKzPfZfuikOti72XUYuV7e6so+U2f\n24sjAAicixcirGT6gd5NQ14ccay2Olj1rDEWjDLZfRAxF9qz1nDfjoUhqDMR\nX081BDgkTQ8oa0Yvo0F8Af6/o1BD8IJ3x6FZ3VHkZpYbrug7zFljR5EWIWr3\n6/P4phYEIpisTqc45ExiqG21bvRWS2lcW3vn9qzB6lKNYiVOsN6gtCuM96X4\nDO4cA2Q99E5gnG8cCuT2SyGycZscWayWxE6Yu05UouzFLr4TQyJRyO4NXmUJ\nvtAyRsHL5dM/aD/p2GZ1W1NZ4uziAxDimNrK2E87d5KlU29lkovf97qgy7LO\ngFHS997ldqcabbLqZ7vaaDUJwaSqGoeGxgJyJghWREj137gk8G7J2p1To8rU\nynzRD/nCtvIWnSi/0uQJgQVi6Kc78nFo6Df7MPk2ALjr/4RWnuHmyTkuyHJV\nkC82ip7J5SRd15w+IqsEc7mzPoYWQa5SnbJP3GDBJftBT1fVm7A9yL5o7TAc\nrnRB\r\n=xphD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.15.0": {"name": "browserslist", "version": "4.15.0", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.1", "caniuse-lite": "^1.0.30001164", "node-releases": "^1.1.67", "electron-to-chromium": "^1.3.612"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "3d48bbca6a3f378e86102ffd017d9a03f122bdb0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.15.0.tgz", "fileCount": 12, "integrity": "sha512-IJ1iysdMkGmjjYeRlDU8PQejVwxvVO5QOfXH7ylW31GO6LwNRSmm/SgRXtNsEXqMLl2e+2H5eEJ7sfynF8TCaQ==", "signatures": [{"sig": "MEUCIAT6y3TdzvYtvA4o022KrViVnb6FrEl9JnnNObVUTabwAiEA3sDk09aej8sN4RnUHoFlfMe64rO1uCFctDwVHHNqbb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxtTkCRA9TVsSAnZWagAAwDcP/As5mzAyImXjGs0WJ/k/\n6UWFNofi4Dzw/YQ2z0+uIEcEuMA3jcgO5cw/hXQDbcnotN2hwD37yg2HfszZ\nCY+o7HtgN2R+z1NOjq0uxGGgVmB4gvb3z5L8j1KmjVvJFgOqbfzo6VMeLhg5\n2l7/2gPwy41mWXqDDfA7+xH1taiWughdA6IF1vjAZ/TjKgaA2sWCVzW9MU4z\nNHNIHfx7GSZCr2N7P2OhtV1vc7QnBhSeExgtmNlH40CP3TarbtPjBfWLDLIM\n8aaGEpX9HscTNWyCZ2hu2C//t1HR9HN5nGBDwGKNo4379EcKZiWpvW7KeOtq\nKaTM9YOhyyu5j0G5zOUkPCoyPgP5bMINtNfQPnAuNixQThzuvhUqdoD6ZZ2E\npj5hF+BtArav/sMjW3yNXeUlH1nzGofwmn0H5/CkBujCTlPs8LqiuTydd/Mj\nnfVDc8K+68Vt30G9vveZ+BQFjs6XsZMAnUHobi970hOniwAjvMiIm/4sKtrR\nGYimmQ1MOC/VmIRXEzCBgBFsa8o6k6cFDibYVA1nbnsn07zdai6FEue9WTTn\nz5/UuiHq28cm1520gaW/5fdWKsd3xGGzqbw1cZOjtC9i4FBaoFRO2hGjW8gJ\ngcMXN2yTfwaYZaMxZ6sYOs+S2dkD67FdSb4cexXYXJjH+or8K9VkAN49MYzc\ngCz4\r\n=DeMc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.0": {"name": "browserslist", "version": "4.16.0", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.1", "caniuse-lite": "^1.0.30001165", "node-releases": "^1.1.67", "electron-to-chromium": "^1.3.621"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "410277627500be3cb28a1bfe037586fbedf9488b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.0.tgz", "fileCount": 12, "integrity": "sha512-/j6k8R0p3nxOC6kx5JGAxsnhc9ixaWJfYc+TNTzxg6+ARaESAvQGV7h0uNOB4t+pLQJZWzcrMxXOxjgsCj3dqQ==", "signatures": [{"sig": "MEUCIQCl+oKFlDAJoxjm5obWx+98X934kNe/EQ7pLV9aIuQHxgIgO3g50aK+UQhaP2id1ofMigYv+nb9u8UPsEJedfav07k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0e5bCRA9TVsSAnZWagAAMokP/jtYLQA7uJai+MGzgMC1\nq/PBet2FOKJ1zEKrxzc3u6UJ9eof9maziIVKBcqUixP38w4nZpQODECz0Zve\n8+lzd5S11n6AmBagCYuS4pN43yC1ehSjzM1oqGLPlWSuXyPo0caes4+WS4SP\nO7Ia1JWm+088KbscBoPbcl68LmxfQ9BHx4Oqp3N4LbslbqkkOtV1JoiSQhny\nwJ1DsfCNVFTWLCfGyoXFKqvwlUHTEUN4uH87ly9ay4a/RPoWSgCAiuaGDLN7\nY9XDNusu9maZAVj9SdsKlN4MBxkMLtK5eENsEC/P7XVzmP16WMTTIYDCAFkr\nUjzTBwab1MvjTNTnpaS58YCFBOlLYERC1aoO0sZeJhk21qAPq8UpFHh8WGeW\n9XCX0I0G5i35mOzqISouthJMrAzEBK2v2sNsQzFjj3tl0VwSwlYCZSLqrpC+\nrXjMTqfKe85NFVcF0i2Cy8fLV0eQ/do316X+3Pj3yowCKZZ8tVFf88Q7mPfq\ncMtMeLRuM19+JjQFPffq0o6zwIRg9Atq/cyibB8i2mov5zV5ynMqovsPN7bQ\nM8xcyhIcSAlH27vPblCnjThjpwfSJBqS3KIrNMzC6KBqbeaVVb+SmcnCWcIL\nvOD378HRuD2FIN5n57Mh722wUj1zLz4+Joch0W+oWIeO7YWVundlGjwJAES6\n3YA1\r\n=xInT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.1": {"name": "browserslist", "version": "4.16.1", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.1", "caniuse-lite": "^1.0.30001173", "node-releases": "^1.1.69", "electron-to-chromium": "^1.3.634"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "bf757a2da376b3447b800a16f0f1c96358138766", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.1.tgz", "fileCount": 12, "integrity": "sha512-UXhDrwqsNcpTYJBTZsbGATDxZbiVDsx6UjpmRUmtnP10pr8wAYr5LgFoEFw9ixriQH2mv/NX2SfGzE/o8GndLA==", "signatures": [{"sig": "MEUCIQDqFjhu1GpZ5t6Fiu0+0+hyy8bKIFTFb+7ITrMzmeMzQQIgFCHw6oxBN16uMJ7eJsemh2M5hjfcglBeiDYq2OUCaSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9RZSCRA9TVsSAnZWagAA1b8QAIRdpRl5Ag6tYfSYFfMR\nchv1hpBXJQdWgh0YhrByUfLXRJChvxGGXF2wEZZd9+8aCbqpnr3RXX8ZXqn1\n4/lqve9UUGgChA53/DCs2FFbEBf/6pgvpTSPVdVn22KzcwRAYIGuYfmityrf\nWwyDV8NPTzu4cx7H6GvDcrJKv9vQqn4OJ/N1p4Gn1mu+VQpZLUnMVV3/o8Mv\nrSbWVzd3WhwNPV5utTGiWXJLg4BrjBB+c2Jreqg/z/W4iMdmkg4LdkGdK//D\nrh1lCy+TiwOo9VIgujTz0Hks669VtMiy0PMc0JMK8lay2G8YQrD2v/pVn301\nsxAk9Wy/Ci6Uk47tykSKUO1WXrG6n8LhehC8lHT/SKcaVuFGt7YnWCU8CGz0\n9TGZUyE1RQRsNGURGZxc2S+8NAiTCk91kbF7P3O81p0AazKFBOuYe+FGu1l6\ndhMDwRnZq0f03XI/bSedLYHFPchLIwYfT2wTJeyo045Dw3q4BovZr17yMYlM\nhWj3yNYmO8acamXmYMLwD3EqFKxS4y0mCJNhPK7cdtsu/sE6XAYCfIr0TZtR\nILejwvAU0ThS9JnmXa3mLaqLG0Mz8s1F01hw/v7LzSo3P3qy7PU6mc+JWjnZ\n0osYAIqQYMXspkmYyvOlT5j+PZAzEkYfrt4tqvf4WHZjRknOeB04A8PMB5Jk\nap1k\r\n=XdeF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.2": {"name": "browserslist", "version": "4.16.2", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.1", "caniuse-lite": "^1.0.30001181", "node-releases": "^1.1.70", "electron-to-chromium": "^1.3.649"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "f79d67cd37e8d80ff0835fe7bc456e406fb1582c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.2.tgz", "fileCount": 12, "integrity": "sha512-oi5WJ1XukqFwgGsMxja1dySAzyWaXZqWSEWDedulO5M63JDw1rgGQbegfVZvxQyXLwkHm44xUbLsgP8C1iHeNg==", "signatures": [{"sig": "MEQCIEwg8aVxGI0XnPj9yQGohOPtWBX65K0FzGZFn/zGj1nuAiBgbikRKNRJNmlgs1wSP0sthqG1YQSLC0KdMeSRLTI/Dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFwzRCRA9TVsSAnZWagAA5OMQAJiztov3ZCgZf7sBx2Ex\ncYvJ6dsuCOERTJaFio4y0WPfJkpnfcYP+63U03+6sVb4F6JCYCzUtQ5A4oi9\nSwtySBgssVv/WGVMZkcS/4jCi2nhFrZL3BT/oh0RisJucaUcwP1gttY8qyDL\nBqJsgCTo8ClsMTYHbdFqnQUu3RtdmKc2bGQLxGtHkJOgT1O6I3U21jrsTHJx\n8lckXqLJHn8UM71CsEtWlV3pSSHTlQnv8qKBXDEa1l/P13eDPNNPX2LKDGVI\n11yZS0RGLZRh2M3AK3YwAk6rSbeuNFUYyEXOSW8jKb0n9Uz28JAsHp3teAm3\nsaa2Ewbm2y8wtLTwJdiPt3aMWh/sxTnId2H+BawAmC4mqkvdhJGWBQPjUKxr\ngQ1W3FeMdXqb6qeY2AhFVe+61fx8fvGaTZ4/BwMsFMXWkc3lkyJRBd/OZ48l\nooLgPVNvp+DYPZT82EO9E5x3BerDAjIWs7A7cSwvrkoFHp0erCKkGUF3+Yyi\na0/gdVzhtXvZU/JBlGKfn96pPtJhe5mET2KHTaPXRPFp4TI7Gf6rSFI3rvV7\nzI4T/Ky6FDLtpFrpj0kux5TmEO86QGhgXKX0VH1qmw/jG60BnB21bOT2WQWE\nFKlYMnnR3D0HNQk7lLcbKyoAtMaZi+t+PXQW31/OesiIQsOrCX66uOEKN0XR\nrnep\r\n=fz2A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.3": {"name": "browserslist", "version": "4.16.3", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.1", "caniuse-lite": "^1.0.30001181", "node-releases": "^1.1.70", "electron-to-chromium": "^1.3.649"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "340aa46940d7db878748567c5dea24a48ddf3717", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.3.tgz", "fileCount": 12, "integrity": "sha512-vIyhWmIkULaq04Gt93txdh+j02yX/JzlyhLYbV3YQCn/zvES3JnY7TifHHvvr1w5hTDluNKMkV05cs4vy8Q7sw==", "signatures": [{"sig": "MEQCIFUI/6eGR8TqScIWi5bLdeU3radqbF4OUu36kNTJuyNhAiAYbmOurxk2LqZWNi37HLsX+2Cghsj3Jge/0PWRkyaifg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFw7XCRA9TVsSAnZWagAAYpgP/iA5bbU2oRsOJuLTX3f7\nrvMY7oqSvRZtsOYVEPjJ4D9Yko6oAL0wCH3SBCy023+JqeURlTijaQZSsizu\nmFjrboMOlFJrasE+EC14gFMnET79bY9u7Y2otUZ6bQqF78xLQWPyXJfmOpLK\n3gYviKl4LgvhFSdN8IyDnEWVcqmnaWiJ9JI1hID7JmrDd3dz7rhu+Xfc1p5F\nHB6Y4kfa6zik5EL+PRKUPJmrBTbpMs8WShIeCPxg84ti9o7KboDIbF1tBNGv\nfbUXc1Tb5B6v4+UM2icA0OWpYpuwljJ3ZySlH7DU7fP6vpVGFc+FadINfZzm\nZBBzE5f4zIayHy7yUuVTqEbsu61AxxzKlu8GC411LhFeXkZI1atRfRUq2coH\njPk1GcRom1BxaR+iL2dXBW0fEU9ZW/8QKBR9qsUGpna6ZX+NO1AIDn8j0u+W\nZE019VZLIa8QSklBrbh2SPRVotI1n9ZyFarRHHnUXfCzxnL0SJRQwXrBqo7f\nCHeYcxiOEjVOPtAKc2APrK8FpUgTUED1dyPTSGYBn0E0VTod9jfRFEKo60Xa\nFxspHoGDH9b2/WrqEibKUaclfCLoS0fmBi+a0W2XoynLxA4elL7fjdJieHLq\n+lusAwLZZ3QGXD3GBrna0VxU+euRZmkqnG78B9EIty1LktCqJlLPqI2cEGVE\njSQD\r\n=PdCB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.4": {"name": "browserslist", "version": "4.16.4", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.2", "caniuse-lite": "^1.0.30001208", "node-releases": "^1.1.71", "electron-to-chromium": "^1.3.712"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "7ebf913487f40caf4637b892b268069951c35d58", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.4.tgz", "fileCount": 11, "integrity": "sha512-d7rCxYV8I9kj41RH8UKYnvDYCRENUlHRgyXy/Rhr/1BaeLGfiCptEdFE8MIrvGfWbBFNjVYx76SQWvNX1j+/cQ==", "signatures": [{"sig": "MEUCIGM4TW7Cw5cgEiQ9wAE215/Uc3TwyIdU/aoDYvTCBdTVAiEA2XSmvKy9yGiJRKql2gYKOwiaJs8AJPB6Ex+nyGS39ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdCbWCRA9TVsSAnZWagAAo6oP/jN8K5WpIulTyr6eFruW\njedjkdxnxCMTlVm0KE/3sgS0U5D92fc/TT/NZXDZz7IHk2/VZkWn7d1ncUMc\n6SRItfNXfcIRb1TtePItS4ZwwsnQxmJ+LqWAt013a8tWonXzmmAcDgEiVyNk\nZlzxt4PrxWcuxXWqTC3m2ed5e/Oc7cYtdM5I1gGWON4XWy/eV6z0Zf1qBHew\nIsRr35r0YCu4YETn7ccNc/oxScfuQQyGCTjW3zjDU7eDmDHGOpQhDisD2WlO\nn5UIn7+X8qBLZpfQGIw44RHWTdAZoa/fMKPd5XcLFbXlVxWIOgyDMgoFi8Ar\nbHLptcGbvHWEZBm+36KpCFfCX4gfejbzWUcYOe29LvPZ4c7A1iMAoil2WGMt\n5YqMZVodvhADMMEWXUbrpYvXhkrALFrmKGL2WBp8awrxaVZCvYtAPr45xkqT\nR/xoHQhkMkEVpagbHOv5lCI7PYTaEafqVWIOjsJR0RiqogiMFmhZTN3g2Z+F\n3MIoWCpTfCWGBA+irIhJCuYOU36MLVwFKYC8sYGXdfirwgK1qzOymYCBgEwd\n6T0TboO/WZ+Ld4MTFdf/SZFa+RRtN4/KYzPPXRP7S7ug9fPl6Lw5BmmQFPsO\nnRi3T0LntzcToM+UhymtgpmdlJ5VXnnZhe4uL9dr0Qj221shMG92sBau7OUh\nAk8m\r\n=d28W\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.5": {"name": "browserslist", "version": "4.16.5", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.2", "caniuse-lite": "^1.0.30001214", "node-releases": "^1.1.71", "electron-to-chromium": "^1.3.719"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "952825440bca8913c62d0021334cbe928ef062ae", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.5.tgz", "fileCount": 11, "integrity": "sha512-C2HAjrM1AI/djrpAUU/tr4pml1DqLIzJKSLDBXBrNErl9ZCCTXdhwxdJjYc16953+mBWf7Lw+uUJgpgb8cN71A==", "signatures": [{"sig": "MEUCIDHDgDTOtZ2SObrDT3Ov0qVo3bqD7wMfi+l2TJOuT8tTAiEAuNx/VjIB8Ou3vxI1etaJnvnIA4PUiLVD1dyI0ruOQ+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggW9SCRA9TVsSAnZWagAArmwP/1jFeitZQ8/iWd9gB/KH\npuBgujALctXj+PeM2gF+O8PToRxCLu0SmbKBFvKHJtrOYqIo2WzRXZCwVMT7\nF6jCZW3XeglYayZUPvZSD9RuYSMLkeOeavtrnzfUUDWyQHAfqDhsyHnVhNBY\njGrhTHuoyO0JnA/iL/biJdhaFSkY+juvjTh5nQmqJUN2IoAfsW7IzXpx+eoI\nJvRR0aLy3IjIIK2q//Ffg0iV8Q0gOY+7G987AOtFrOujgrynzDyPKKkPuhmL\nhlHZeoWUj+Ydn0b1yfN8Azlh5GN8GynH54fXCeyC4YpacUgKLqZcfsWfnDcr\n38R9g8/KiPuutI2p0UnBaxUEAtZI4HRzPEV1+JQ5/jm4GfshlBU42P1BHdXb\nW5HzcJEdQhr1VHO5Gy8XXAjpJAKKbsyXITpFzySizv4c+AewC+hkvKsHxs1H\nkRZWm8geaCBeHdhGauDxt0v5utilmEeN4HvJlxYe3HhBO40aIem+dYX1mdTh\nt0j+iFlEYbfHuJirqq7VdZIwAutlaJUzuB2DWrQ9zsZcHNecSslfSy6MF+Zj\nTmbaVw9m5BwajEpecnBlVL992jMrA+uRtrs+7XegYAtbfpNMijZMXDNwQrhG\nLUrAM9v7gIWodKIa2hhYigq3yR2i5KUkiBkd32Pr2/iPRGtculc69XQtHSqE\nop5E\r\n=8E6h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.6": {"name": "browserslist", "version": "4.16.6", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.2", "caniuse-lite": "^1.0.30001219", "node-releases": "^1.1.71", "electron-to-chromium": "^1.3.723"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "d7901277a5a88e554ed305b183ec9b0c08f66fa2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.6.tgz", "fileCount": 11, "integrity": "sha512-Wspk/PqO+4W9qp5iUTJsa1B/QrYn1keNCcEP5OvP7WBwT4KaDly0uONYmC6Xa3Z5IqnUgS0KcgLYu1l74x0ZXQ==", "signatures": [{"sig": "MEQCIBgIZh/eG3Dm5S2M5KmuWGoRVh2C6Nq6Ys9AID4a722UAiBLqzi+MxnQi25L2gMougRRZtN2OvCLu+v2AfpiDi8WNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgjAT8CRA9TVsSAnZWagAAtXgP/0fde3ruFSmgTKwtVFgV\nR3TuACE6u+csZSRkMsX89xjUBnxdSYeO7oJgfXowBc158VKzUBBTMpoGfWQ4\npb0Qq1A43AHJapXqsc+2ecXw+7Rj1Gd/ik09nypiKVs+XHv5bHhMpnwjKcH1\njrUvKw134Mq26GQ39zEg6KQ6ixOBZjexWUkEFPbvc0jU7ncta3OqL9j69qYI\nMgUCtjmi8RgJnkebd/RvLbMwLNYU9SZZNxs+D5kORswx2w/R/XOh8aF6Cr30\nFhc/XJ/rS+O+gQ6/KjUMdxwEoKt8eZgzcXUx/yEFEUlTO1V2MYTSiC6Sxrv9\nPJwVD97bMsDo/f7t7/e5VoufWzmP5ckDbkVxnd9dvXdLm+odECisU677ZpMc\nHvmNy4z3LUYz79z2mM2x1OZ9MP963DS2Hem1oDSOkpz+bJMQpRIdRbzidrb2\nDeFsFWxUAfXDc8E8Fk1VU4A4P/hCDDqGEnYneuVKVOL4pDvuOA0DMzQr709A\nSS+bY0nlokxcn4+w2IGqp3ei5NOPvQHTXm+8g4bEIX77M0giLmrQmpfSE4Kd\nJZio0j0toojwFRO3xFuP+xPbFC1IezIt37JuhHWCpo0tztq3DHyGBh78nXc0\nCdSrttd//stvUTreunQ6MoA1Owc8Ejih1RFutUrgIqodGJGmYnnDkSTMnCYO\nD56V\r\n=Y8LQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.7": {"name": "browserslist", "version": "4.16.7", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.2.2", "caniuse-lite": "^1.0.30001248", "node-releases": "^1.1.73", "electron-to-chromium": "^1.3.793"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "108b0d1ef33c4af1b587c54f390e7041178e4335", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.7.tgz", "fileCount": 11, "integrity": "sha512-7I4qVwqZltJ7j37wObBe3SoTz+nS8APaNcrBOlgoirb6/HbEU2XxW/LpUDTCngM6iauwFqmRTuOMfyKnFGY5JA==", "signatures": [{"sig": "MEYCIQD4Z4BIGpUdCf/J5z6OQQszvJT+aijNwV/wXi9Jc/s7PwIhAMexst98cPeSXttRHd8y7juZLzi01oAbZcxv9aJBl21v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCQoVCRA9TVsSAnZWagAAu8wQAILKEQQwabaAYsBSumLn\nLhLsXResZm+u+ATDF5F9MWrtGOg8CS+W8FP1XdrU6ez9AjCjMTvasVsxZPAo\nPuc0HlpJ72SGIvGeTtnqIAXIqa9g6AzYCMq7UO4TcaCmwBu4iLkd4KOMft16\n9r7Oj/gU0ysx+40UGYW/CLKAuaqDvm9RqFSReZWmNcVsHGhL+QhKm3sR3WyR\naUDV4VtlV6t8yytuwxS11tx1F+wEcCT8eRFjwe4ttAOsCc0QSPKAPp2tgFyS\n5xA+bnfL6fPCtbh8Dr5qZWL/cIbTB6j7sMr/IauhK12zmCiP+EXqfQJBjEj+\n7mkT3RtjvfuTCrVE8BhRZ94ohOyqVoXzuSiVB6IBBWJTUZYJ7a5MVX/MB1pk\nEeNLKuQkn/CpZX113b25EYxD3hxgFdSsyG8rWdXd0Wd3fVtJdR+bjSN2TVZ+\n6r1jRx680PwpdkYIAsOP0LFX39236Gln1ziczIbsU302kt/VC/wqU14YRDRz\nPbL7WLZE7g/Z7TVnFHFKlTnc1b+RwSl6wTjWmZZ0F9KfoFgihuY55h+yArfP\nfEWGZNZHmgKeMCirETRPPCGZFwP7A5K6K0j0Ia3iPkTXElgt7O2NgFtJyakS\nim0KHEPtp686ZkqUdPoG8vl4mofdWaX5KarhrmygMczm6pagqge/7cluN6ID\n98gB\r\n=lg0Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.16.8": {"name": "browserslist", "version": "4.16.8", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.3.0", "caniuse-lite": "^1.0.30001251", "node-releases": "^1.1.75", "electron-to-chromium": "^1.3.811"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "cb868b0b554f137ba6e33de0ecff2eda403c4fb0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.16.8.tgz", "fileCount": 11, "integrity": "sha512-sc2m9ohR/49sWEbPj14ZSSZqp+kbi16aLao42Hmn3Z8FpjuMaq2xCA2l4zl9ITfyzvnvyE0hcg62YkIGKxgaNQ==", "signatures": [{"sig": "MEUCIQDwQCrX+/IryKPQC36err8pEd5JHz3Qi3FlhtAmmqZvUgIgPluPugj045PA81iiKWa8SwCtCjLS31g3WVJwbTcinow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHS8MCRA9TVsSAnZWagAA3iQP+wZRi7He+1kb2+OMxTVU\nVijXx/sNBGusWaWh/hQMdHJXOQ2uEghaKXeS2HSD01CPJi/kGT9bhPKmymm9\nS9AcsPYECYH8ne2kWD533FvTRzj2cMFXrmeMJdqg8nTUzN3jB+Yp9drqVGyO\n/trkA7Z8q1uuSBnFs2QzbFX/cEITcG6+AKGUL0EPSJOYMdvMVS8Zt9i738+R\nAg3hkKFv2KZLJOMSc3ZkohSelamSj8vHmNvqtOHcCnCU1ioadvoSEghkg9Yf\nGVELAErbuA/JFvTx/5MBm93Xbv2hoKATu+YMjkJn/lbgpWUW0hl+hpRWXMkW\ndOCltJhQe24DUEux0XJ74deAbtkjKqGivjH+1C/v2CbWoZS23TCFgOiC56DS\n+yNz4MpKHtWVmAU2bYlDBuQhq/piqFCvQ0vmfliNB5kG12ooO7ShmH2PE+wv\niDBlTNOVIEVhK2EaJlOBgPYXGHyM5jfSU0OR+p48JVeHRJgcayliEl5yWzmK\nVJr2/5/r7uTZ2DAwsTVg3/Cdi40MnkDCYwZJW1dUwEMjDaPnqYTT0Xx2z+3u\nnje+WO1daG5M0gNUh5/CmA4XPzcBcLZ+0PsElegJczDaAm1Rqdq/DVFFfqjk\nNFlwlcF7MZmlGtWJUa/L195ah9oR8SqGXDGTJKJ6jBDW8EAamEFtg/SnqHfB\nX80q\r\n=ly24\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.0": {"name": "browserslist", "version": "4.17.0", "dependencies": {"escalade": "^3.1.1", "colorette": "^1.3.0", "caniuse-lite": "^1.0.30001254", "node-releases": "^1.1.75", "electron-to-chromium": "^1.3.830"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "1fcd81ec75b41d6d4994fb0831b92ac18c01649c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.0.tgz", "fileCount": 11, "integrity": "sha512-g2BJ2a0nEYvEFQC208q8mVAhfNwpZ5Mu8BwgtCdZKO3qx98HChmeg448fPdUzld8aFmfLgVh7yymqV+q1lJZ5g==", "signatures": [{"sig": "MEUCIG5IWDfpvviIWb2kn/DHuPkNcbSAKxyn5AT32/HeaLzlAiEA2ytBdBxk6177+00CWnbbig7qHIrGLz4yBTI4HipoCS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNJbECRA9TVsSAnZWagAAlWwP/1BmSOltDDMW/dwCTxGy\nqJpxIOxKhyjsw5XYbmRiWShv/oI9l4RlRVVX/fR7YuyPXFV2z/V3IL9GhXtZ\nyFRSHhT/WKH0xE4TTRFStV37SkRV1SH45n48uY0SYm08Lkwry5O0ZWyfinjv\nyEb1ao4sSCvgLiBuNTCOI7nBv3FMMM8S08e7u0lzHEA9P9y048IqEjMEHXqg\nrvlKPzLh3rpT9Wjy1ow6B+IxFvbAfyGS/aVuxGLqaiJSzPXoMTahzEzABmOz\nZDQq4XrnO2p6k28lGfVn/HeYaqvnsmbn9mk9Xhs3+kNmA1St2ymX6Uk2xXhs\n2uhQIyIv+LmCjL64WXhd5nbfBylHvN8IFGw3mynSeNzJtfjKb1QITCAc9uIo\nJ2ar9EMpG71kquUz628PGic18NdxcUN2XI6rNYEJ305s9IC5QpUtd8OuwFHI\nAlM+ciBJtt9SohnCAg0iEDkukzBKa0ObG6/wZkYSAC87lXZtGquG/deYAVBF\nY9S1XqLFTeyXYMp+AgRNcYAPZtoqmB4RKPElnBj2/qwc9/FrH16/2bsreMdQ\nOgr+/SNR8FqeMQj4Gl8GuhmaecyDwaXK5hSKPzKRnvt028vZ1dT3yVPan+nE\nYTx7MhmUwWpuzHm75ioE1N8wttLAVlg0spnu1LRL6T8pzS7Si/brJjDyDrKR\n7h1C\r\n=kAq4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.1": {"name": "browserslist", "version": "4.17.1", "dependencies": {"escalade": "^3.1.1", "nanocolors": "^0.1.5", "caniuse-lite": "^1.0.30001259", "node-releases": "^1.1.76", "electron-to-chromium": "^1.3.846"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "a98d104f54af441290b7d592626dd541fa642eb9", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.1.tgz", "fileCount": 11, "integrity": "sha512-aLD0ZMDSnF4lUt4ZDNgqi5BUn9BZ7YdQdI/cYlILrhdSSZJLU9aNZoD5/NBmM4SK34APB2e83MOsRt1EnkuyaQ==", "signatures": [{"sig": "MEUCIQCDY/+O9u0BRX/sV30huk+l4jQyfCGUF4DfIhNe9VWo+gIgEIpAR2X5PjVfasUq5GJO8oQH0xSHAkGwRgP4aqgPQ+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88940}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.2": {"name": "browserslist", "version": "4.17.2", "dependencies": {"escalade": "^3.1.1", "nanocolors": "^0.2.12", "caniuse-lite": "^1.0.30001261", "node-releases": "^1.1.76", "electron-to-chromium": "^1.3.854"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "aa15dbd2fab399a399fe4df601bb09363c5458a6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.2.tgz", "fileCount": 11, "integrity": "sha512-jSDZyqJmkKMEMi7SZAgX5UltFdR5NAO43vY0AwTpu4X3sGH7GLLQ83KiUomgrnvZRCeW0yPPnKqnxPqQOER9zQ==", "signatures": [{"sig": "MEQCIFx91zIYRkIqLVhH3hcUDz044v79+PNeWGSyLbt66K0qAiAFuoXAc7ZHgVcVshws5lT1268Bmgqckl5WSti5IYCaiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68417}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.3": {"name": "browserslist", "version": "4.17.3", "dependencies": {"escalade": "^3.1.1", "picocolors": "^0.2.1", "caniuse-lite": "^1.0.30001264", "node-releases": "^1.1.77", "electron-to-chromium": "^1.3.857"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "2844cd6eebe14d12384b0122d217550160d2d624", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.3.tgz", "fileCount": 11, "integrity": "sha512-59IqHJV5VGdcJZ+GZ2hU5n4Kv3YiASzW6Xk5g9tf5a/MAzGeFwgGWU39fVzNIOVcgB3+Gp+kiQu0HEfTVU/3VQ==", "signatures": [{"sig": "MEUCIBj2qEsQ0CNPjsq+TVBNfdGYq35bUGtU6G3y/fbKkLcOAiEAs/DINZ2dekGLJwGG15f4aR0SIuP9aBsgmhB/apEGO4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68382}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.4": {"name": "browserslist", "version": "4.17.4", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001265", "node-releases": "^2.0.0", "electron-to-chromium": "^1.3.867"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "72e2508af2a403aec0a49847ef31bd823c57ead4", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.4.tgz", "fileCount": 11, "integrity": "sha512-Zg7RpbZpIJRW3am9Lyckue7PLytvVxxhJj1CaJVlCWENsGEAOlnlt8X0ZxGRPp7Bt9o8tIRM5SEXy4BCPMJjLQ==", "signatures": [{"sig": "MEQCIFZXqwObgOSh04zEqJMRQSW1t7lX7yyhS/T7jO7qST5ZAiAQoByPTTD7efYEpRTUya6wGa6bt9P/abOM3zzuY+FUlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68381}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.5": {"name": "browserslist", "version": "4.17.5", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001271", "node-releases": "^2.0.1", "electron-to-chromium": "^1.3.878"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "c827bbe172a4c22b123f5e337533ceebadfdd559", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.5.tgz", "fileCount": 11, "integrity": "sha512-I3ekeB92mmpctWBoLXe0d5wPS2cBuRvvW0JyyJHMrk9/HmP2ZjrTboNAZ8iuGqaEIlKguljbQY32OkOJIRrgoA==", "signatures": [{"sig": "MEQCIBVbzZxQgjL177FyTaxgE9/WmVdeV5EQwvl570hq7Go1AiB+pqCtFiZ4qa+GdMLP2+HfqYjTJt0g3XF47XbO5+k0+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68957}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.17.6": {"name": "browserslist", "version": "4.17.6", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001274", "node-releases": "^2.0.1", "electron-to-chromium": "^1.3.886"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "c76be33e7786b497f66cad25a73756c8b938985d", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.17.6.tgz", "fileCount": 11, "integrity": "sha512-uPgz3vyRTlEiCv4ee9KlsKgo2V6qPk7Jsn0KAn2OBqbqKo3iNcPEC1Ti6J4dwnz+aIRfEEEuOzC9IBk8tXUomw==", "signatures": [{"sig": "MEUCIBKX1tMHzvwlsC+J3F8yRRiUBdVMbfmQH7JS7TKMYCtFAiEAxxMDUJFIwbgP/SjKFgJqCVmslLArTATEEDH8kB/Us5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68665}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.18.0": {"name": "browserslist", "version": "4.18.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001280", "node-releases": "^2.0.1", "electron-to-chromium": "^1.3.896"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "849944d9bbbbe5ff6f418a8b558e3effca433cae", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.18.0.tgz", "fileCount": 11, "integrity": "sha512-ER2M0g5iAR84fS/zjBDqEgU6iO5fS9JI2EkHr5zxDxYEFk3LjhU9Vpp/INb6RMQphxko7PDV1FH38H/qVP5yCA==", "signatures": [{"sig": "MEQCIH1/6YvAvh27JfBpCH5uyVegaVCj25tg1onazGDApuC4AiBJOPMwI0mXjanrnK/b4uk6VlwOHzbfgAfeZfopMLGsKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68811}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.18.1": {"name": "browserslist", "version": "4.18.1", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001280", "node-releases": "^2.0.1", "electron-to-chromium": "^1.3.896"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "60d3920f25b6860eb917c6c7b185576f4d8b017f", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.18.1.tgz", "fileCount": 11, "integrity": "sha512-8ScCzdpPwR2wQh8IT82CA2VgDwjHyqMovPBZSNH54+tm4Jk2pCuv90gmAdH6J84OCRWi0b4gMe6O6XPXuJnjgQ==", "signatures": [{"sig": "MEUCIA2/EZPynz0JBju1IWAcBWBwCoqJzn4287nmLWN34l4uAiEAxyg4Nkef6Jnydq6qTIlZoZqs+iH7XiMsR6Y88bblYUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhko1ACRA9TVsSAnZWagAAWIIP/1p5N7fuEDKmk4DfoI5f\n4zWNZ/LwpubxhzdGBz20Fw2fIVdxm4BaRwqoEzUf3Df/2797NSbetCYd74gJ\n5xaYJTUfS7LiGI7jFpVBsjMpCyRVMLGKcpNM9zr9cgIFQLUkpbZhmNVf5RyA\nuZV+u/8UyNluX/+D+uhr5oB+p6ZeCW2flHDOBjYX10+qEH8g00QT+yDm1NKW\nF9Fm+EUF1FsVB86Lxko6xWiZe8GshmwGYt3/sX5/zeoMXIbw0NEPLklCGj9J\n8JJu/8JWwZGtWcyvcml3rvkrbJupNFdbJSsunY4ca5mQb21mmWj5Zr98KuLR\nMFNme1G45rm1E3XRZDy9qYO3AN3f6dVJ1h/6TZVBN0n3gNujYzLC0YxhRl3b\ndE3pJvAXB8CnvcN6HAUzKtwih2tmkClzTETt3ie4tRJHfcanJG91VMYqyPuG\n4udObutkbleOayyIPv+XkClvoNbEl3B9oeB/NoJrFQstDTok8G/nw7E1QgFP\nJKkpoeVonPIwDH03gzdIxFLCbZhzp5zrV5MravSwhRz4b931gymxJbVlgL9e\nXbAugHu7lDvVeCChr1z+AymfyCCCzruasMvm7I6PqJCmlo+jE2EL/2qMAeTo\nRuRURbKi4Hr4MBKo0KSO2z87JGaVeCht4cNljMVO0SsohVeET7r/BzX5hfRr\n8zQf\r\n=GAgz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.19.0": {"name": "browserslist", "version": "4.19.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001286", "node-releases": "^2.0.1", "electron-to-chromium": "^1.4.17"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "5f02742ac2b86dde56ae4cef7be2b003e47b1ee0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.19.0.tgz", "fileCount": 11, "integrity": "sha512-JGHzm73ei2OnAcobcQ61GXNnN6vDCg5Oz5MayudL+FyzjoLnCzUWnuLtDLMIYw8aXgQzzdCZMVky+fftD5jbtA==", "signatures": [{"sig": "MEQCIFOOwA3NVPP6sfmqKPjDQ+0G8b97BuPlXF7PjoHK4NMDAiA9PvBgnoW8bbkFf936r1a7VWSAJ4Xxk/8RFJplUelO4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht/RKCRA9TVsSAnZWagAAkHkQAJG59mbs1QvCFy90eEGK\nMz6wbOQ5CbfuqvkUwtYxzg0wTUFU9uTxO4qgqd1EUoYSqjuJx0KPnpaZPHfS\n0Nzhgl1txUn6nfSPSuRM7Vu9PcWzQJNS9S+L8YHDKsRV60ti25Lfed9l6roO\nI9Iq6ZpiPEXawBh22wYPdsKTFlVr7+afpUbqjp5H1bANU55AndUdCks9caqH\nLWye7Erd4Z/4otDFMBwV/j0VT+PxwwouMgTk6TjMfU8nRzmsfh9GlFHq2D3L\n5BeGIhG0XHmmli4JLw24TMxRPm20PmnlyozDK0sGVPZ9MynBbgcX29Wyw19r\nuvkc9UYUhs9Cx92ERD1lvU1iTisYgm4e3/lKh3ez0BxqvhYCJCiKnQtK7GTv\n20txWTVTpcjyQ8egtgXEKtihecqLaMWkEAA3tqTsLDgreAg+f7wz0mJsLtO/\niJFA8DwuDKsvhOafYzX731gKwtgow8ngXDIN2O4+s1sNOwODX+QfOxwFu38u\nynFKB9nJxnopCAngwICKVgCaF+iG6PWyWQaOXlpGAcJpQX/OV82+Bff64GT+\nsIm5hW48f1hCRHq2oA+sR05TVVr5ptxdtQn9r0R+/wiqkmhOj/td0GxVK+3y\npWTqpMDAy/b/GiJl1wBtN0gLohNOg7vLyGGY7g4Y1Q/9XduZlhp8vmUvGqq0\n8qwF\r\n=Kxaa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.19.1": {"name": "browserslist", "version": "4.19.1", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001286", "node-releases": "^2.0.1", "electron-to-chromium": "^1.4.17"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "4ac0435b35ab655896c31d53018b6dd5e9e4c9a3", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.19.1.tgz", "fileCount": 11, "integrity": "sha512-u2tbbG5PdKRTUoctO3NBD8FQ5HdPh1ZXPHzp1rwaa5jTc+RV9/+RlWiAIKmjRPQF+xbGM9Kklj5bZQFa2s/38A==", "signatures": [{"sig": "MEQCIGaJ/dmbZrP66FQSvndJE/2LYlRAUU2TeUdOGQuxbsHmAiBI4zJ8dxdDBgjDZe3dTjz9LRvBg7b9BUNBu0P7iQcaeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuK7XCRA9TVsSAnZWagAAEFsQAJxx9PE81WaRmnXLuIgN\nV4cqagVe2ymdh22oPDyaCJqeZUAh1X9GZvrx/LIdG5FJ5zUYMaJf1T6tT0LB\nvHDng2UyJ6aBLbYZBVmc5ckVbtdGm+6NIj/0rR20JTNMf4NrdqhobxspT2kV\nvMEUhFY75yDb+LJS4UQb6Nfc5nC7ZCeqC5ZhetQyetXaqjHG+VKmRJHrlOt4\nsKE0OgVYR7V2+FpqkPodS9XwIrYslG6cFeHSMidFi72gTIEs+NVfikoZqopT\nw4BFOv4/GVvzMvi3IshnfO1KpAO+lfALTtM3NNOg6vCIT3gkbGY1g1FQFN0R\nlxs2/q43i0UnFSRQqgpYA3Cd3RMHqJL2l4rdwdcjkGDuE+riTQn6ToD/Grnw\ni5Saa9VqgG9stVMoTehFO9TAmrO3A42yy6HekXizaxgibtMm4Ftp0Ksxa+BF\nGY/ap/E6t9xMP4oXa8r8J2CacTCJn2xFOW8DD1N1B8Cw4mac7CPmbHMmbzi6\nflEcEfpC3MbA1c/1xaPhzyWe9zem8IuKGfjtc3ajQFsB6E+/G4toRanI+Hau\nNVXUiptkyj8jRshbKBIwjbeq3dEuwu1hlBWC/zw8SqMepAiwZDNLL/gIMcnN\nvmYX6MM2/xneaPXPwDn3ISEAvk8qYiVAlhXBXNmOdrYvXG20MvZNwrQgtqb9\nI6HB\r\n=0mBH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.19.2": {"name": "browserslist", "version": "4.19.2", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001312", "node-releases": "^2.0.2", "electron-to-chromium": "^1.4.71"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "9ba98791192a39e1242f0670bb265ceee1baf0a4", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.19.2.tgz", "fileCount": 11, "integrity": "sha512-97XU1CTZ5TwU9Qy/Taj+RtiI6SQM1WIhZ9osT7EY0oO2aWXGABZT2OZeRL+6PfaQsiiMIjjwIoYFPq4APgspgQ==", "signatures": [{"sig": "MEQCIGwX2AwwyDsvw31wk5SmKgDMCmsDYQCobNxwRxV/gZdNAiBk/AowTWhdCKvjRaeoh8o+vQj+WL7gjc6Xgxnu6pK2IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiD5LzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRWhAAh6ssPOt5PuBaO25mu9YMZV0+HgHH9ihht0NHEOvRLeSey57g\r\no6IXHXtiaiBZ9TRaqzmB+GV913fAZgKmAzfQ5NI25AxXFfdI69EEo8EDrwPQ\r\n1tyu3rwMxBDL9gSWaRHhOQJSuaI/SMRpWYSZUY89SimODV9TFccqyshd1Mij\r\nkSPFVP53mIydfpbK9UEkrLVbgYJbvKqYEIDaWFvaEXStFfzB366nKLZWYUlc\r\njpMjUlEyYCayGSfswh+pwm2Fg5xhpLGigwdSXNir3dg/jWGZ3kWrltYI+Ezn\r\n6nVI7Hx8ljFZBLVUto5c4EEiFOggEjf+ubWln5JxfoDmu37PDsd61g8uxRlV\r\nRP3iAPJntp3+3udm5UwuHObMHZE5VBPCMg/XUAtrOPaxKv7Ds1dAjoxjdFlH\r\ntH+1cQvJQ07XBN3pK42XqQpNkVjqhGbde/iFKwyTh+8RJmkwsFH2P9UKwyFN\r\nOHiBsT9Q6l1ynpjjpSJ7o6ZrXVeOuy96Ytkij8rUx/GvVMC66b4kOP7akK2p\r\ntd0MNmNzmuK5C2ByNQZok4fcqGfWPZsbL3tFNoVwImG10+xFlPkzbfDVQ1XV\r\nAtw98AYO9psjxW2WW8/VAMaOBftdbwpTGnOa6bV+O3Psjyd6PtAe0sVMA7rO\r\nUWBYj4g7ZqOxZSLXCfj3tvxVFqFcmFUKq8o=\r\n=jer7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.19.3": {"name": "browserslist", "version": "4.19.3", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001312", "node-releases": "^2.0.2", "electron-to-chromium": "^1.4.71"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "29b7caad327ecf2859485f696f9604214bedd383", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.19.3.tgz", "fileCount": 11, "integrity": "sha512-XK3X4xtKJ+Txj8G5c30B4gsm71s69lqXlkYui4s6EkKxuv49qjYlY6oVd+IFJ73d4YymtM3+djvvt/R/iJwwDg==", "signatures": [{"sig": "MEYCIQCe4/nre65LdGIzRv9S8fdls8i6ebDD9u+SZunGOM+udAIhAJYob6HHJTeaIHI8PLghmt80I6UY6Bsoz3PZ3PRK93D/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiEDdQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnoA//bDZYgKIEkuIbz4byewZeQBvX7cXOHM2geT1ck6Lb5DtA6i2g\r\n1nePJeC3pQB91wU1pBOd9fHL9XuoAlLoV/V88SZgHgrcHCpnyeJlaCexTQKV\r\nWbKbGxVw60wQKPT3xRTdKNH/FlJAJhGFL7HmUQ/+Sl/X4kyJu0YZwsjP83F6\r\nLqyE8S446ZmfwbG1zwasgd7btl6vsn79mDfJhHOFNmGc11uEn6RzvxG/GbY3\r\nBi0K1/2RkS5NA94/w1kyTLh1YrJPTTHK2lrsmX/jj0AttEKpm3qKgMhm3zrn\r\nV3wH9EtjLVmZ6wvBQHRDS8KJl38I8t3Vn65DxkvLm7FfFyi4TvLpT6xA2wr2\r\niWbOXhcygpEt8k+l2fxm3ZguJ9LH+SWC+cjwM1frCNyBHhqaUBkgJeZ6HH0j\r\nbPznWCTERwOjciyZlI0gefUvMfxoDgkcl8Tn69LFGw3GZv9Rs1g7hKBanmzK\r\nfHBQS8QfcUdYtr/4tfYCtvtf1eoax+r8+EgFWbSWeXvwielKKdrBSGexmwEk\r\n+JBWgU8H40wcxMnBrMUcHF/JTAI8AbhLCaRmQHeM2YcYHZs5WxeEOppwKBSQ\r\nO6fY7vZu/MJUbuy3C34beMDb88VhR/fPl0azkPOiUQ19rPxEsaLH9Q3d65u0\r\n9lai+OTzUYV9UHTp3R4Jzioi/Iyi8F6/fqA=\r\n=nykY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.20.0": {"name": "browserslist", "version": "4.20.0", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001313", "node-releases": "^2.0.2", "electron-to-chromium": "^1.4.76"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "35951e3541078c125d36df76056e94738a52ebe9", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.0.tgz", "fileCount": 11, "integrity": "sha512-bnpOoa+DownbciXj0jVGENf8VYQnE2LNWomhYuCsMmmx9Jd9lwq0WXODuwpSsp8AVdKM2/HorrzxAfbKvWTByQ==", "signatures": [{"sig": "MEUCIBeHoyB94aT80hBh1Ry8UKv0cUhCyE7Ax+T97x1dWCFBAiEAivx+ivrudqWQmmu/XHJabv9I6S6gWe5TOVbbSco+Mcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJTrGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSfBAAmYBX7cLmqPL9+Jrp2n4rem5GQSP5oodal+2r0nyaE4fl+7Qo\r\nPwMlRmvi3X9XJZDbAatSommBi02smbtLer7yWSES5j0dJIQ3Ce3Ry7VnywfA\r\noYpAda6URzKk0XCbMaF+mzzvRtzqhEuvyfzZGuk/Y0RIEaXsM9+c90a8zr2Z\r\nMwLy+ZVn1J/+x/eUFcz/SYIwYxXQA+QbFJcTv60QYnyd9RI5wHpVBuu1mYHZ\r\nsY2eMZyZhZgELt5g/dLKVznDLRiLGrjMoRYU8b1YUp9RDd+oOegSXtda8JR4\r\ndKcRtXPQRLRXDyRHskWo1zt0llSaxWn3O+0Z4N4MXvO+ErRQubStMOocG742\r\njgpHVhHxdZKLO0jhEXuYajtJUs087Npy+lTjjq6G8IoFOtYIW4kdxGOLRSe0\r\ntUHQYHewi8z+rjrVK2oo8BAJ17U+/ied5jqlEK2f3+n2KcbZEPLiWInq2mnM\r\nxr/WlljqNrUAL/ZsV8BKffgDh2qpZf65EWkL6XpPxEQcpsSN4ywZJzcyulSC\r\nmmSVL23EBz9TApEu07TpYQS0zUbqQ50omfPWiZaFaFxAcr/uHlVtT3HaR8Is\r\n/0qoD1zCtBWPr+cAywI/8gzdeZhop08GcbVnG24R1W8JRzGSa1BKcQWwdVsP\r\nBjp+XsZFxPrCAVNiOJxES3BWctktpizZjmM=\r\n=Lgqw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://opencollective.com/browserslist", "type": "opencollective"}}, "4.20.1": {"name": "browserslist", "version": "4.20.1", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001317", "node-releases": "^2.0.2", "electron-to-chromium": "^1.4.84"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "01ad36fdf01a129dc7fb4528d9cc784bea8cd9d5", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.1.tgz", "fileCount": 11, "integrity": "sha512-NkbK9ojxpVgEB9BaIZhrtuk16OnAcztHn6D64hxkSvIeTuXGSovZMR+gXJlh9mzjuLtif2Nl9TTKuTDWa+ax3g==", "signatures": [{"sig": "MEQCIBUt8UlQDqWgXhR49AulY8ii58NU6O7egcDYbghQ92/kAiA2Pj2Tn3q4tvaexve4Pdwd2uvvZ+kZ5K+CGWycOZnwTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNe7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4ow//VbZvQMQMBcXrYV4ICJCpTYjE1EaiOdEmpns9a4Q7+txC7I7i\r\n3SAhVpdNL2Nbnihnz8/PWBB89aZ9adCySjM3nJD3OZY4i6DprP65WQkK1cKM\r\ntDeYLsD+MMzgGCDp0ZBEk2hhBhh80Oay9j6LtP2mloWM1kSp1zjulFJqAdc8\r\nrc+PqtauaGEZXD5d3d+NfLSJMdi3yPEhQtKK+xFwu5XqODyR+mXlp7twXCbR\r\nRqtQvQBSghkXqxkbuLsZnPN3McxMMOT4ztqzhOzVyNHvx8BSUtzoBQDuwFRA\r\nmmdHYHbeYKfhvFojB381g5uft7uW1z9s4gW0Fd6X0ZT4YEqsk7qc5K03QnKS\r\nFSdlBGsXj9YrIZzaEgCMIuaQ6K9H7JRQclRmI7raBcL5SiAgB3TTZ9Z0xX6N\r\nTYzw47NOw1GKdt3UPqaPUz2/HWDVZ86TvJQ/dstyMkwJT5qGk+S9+aesYu5I\r\nl9rw3EFveppGrDhc36C7CEHm5Ev6dkQv0Hz/PyRo5iqpCxZitYx75Iw8SGQU\r\nXzSfSIhsUQrSx2Y1R6ji9HDxpYmkDxhwjH9Qdb7Ap6WEkzGv3MtKPYF0biua\r\ndqj4iRctSUOfAb58aDzxG1JqzNg1XfMBAzWQcKNK0aL149QT8aDVQ5An0z6I\r\nL/mMOBu8M7srgIIvxmlcnazccJ7psq0YE4o=\r\n=AI41\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.20.2": {"name": "browserslist", "version": "4.20.2", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001317", "node-releases": "^2.0.2", "electron-to-chromium": "^1.4.84"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "567b41508757ecd904dab4d1c646c612cd3d4f88", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.2.tgz", "fileCount": 11, "integrity": "sha512-CQOBCqp/9pDvDbx3xfMi+86pr4KXIf2FDkTTdeuYw8OxS9t898LA1Khq57gtufFILXpfgsSx5woNgsBgvGjpsA==", "signatures": [{"sig": "MEUCIDHv4C6Lgrr2SFoHRxVbKupPCegqezcQZPHrsKu8YYLeAiEA48c1CifHhkDbuHGparCUGiAmB6S4MXKoj2Q5ayYf1sE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNk7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokAw/9Hf8FRFDQ37g/58vJI10OHLfWGQ0KrbyScylylPNfqr+dBYKh\r\noHCbW4hBgno+tMq2dYFr75u/7lJrSEg6kz6rY/fIZwjbxfNqRSQKI/Sj7hSL\r\naoBV8V1cNvRKVINr9YzVT/OmzsFMDl9ThY8hubg7/GCHFGo66k8pegboIGTu\r\ne6JdQjQmVfRxC83gH3QirkwEsKP0/yCHrIShGhzerVlxqOtztYjH0SaF8uxc\r\nfTcmCJ4UaekzGqpHhC2pCXs4BM7P7UyaFEGj19QnStaxMSxavkzxEd/AUEiz\r\nAcMUJX4H2CUSmk3dQFmp1FJreHve1gY3g9JOk/HkDFN5cTl7t/2HIQnm/tRV\r\nUeED1KTApi6a1ajTBi3utWTjncTzG9gHmu0wvLRHmhKuWfMWXV6JQ0jbn1Et\r\nkUbAIB7oynqMlnvAotwSs6bC871w8yjlig+xHCDxoqLGNS3Ql0y7pWz2RcxT\r\nVSS5IpluIjof6R+oqLQepmAEZxUiTrBNa/DxvamX50NV+IzyHnsWmzi6mN62\r\nKfgIDYE4vsKkpYtp23xtjCC3dgfjlfTEAakEKiZuHzdocrdXOZ5yulOHTASN\r\n5+mn0mNvu/gNUcJv4Wc6I2UynsPo4lJL0y5e0d/ka2/f2HNh0OCK2tlGKsYd\r\nYShMwdwzXHIIj8NQoLSWc2QUUqQlp3GQirA=\r\n=mBzm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.20.3": {"name": "browserslist", "version": "4.20.3", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001332", "node-releases": "^2.0.3", "electron-to-chromium": "^1.4.118"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "eb7572f49ec430e054f56d52ff0ebe9be915f8bf", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.3.tgz", "fileCount": 11, "integrity": "sha512-NBhymBQl1zM0Y5dQT/O+xiLP9/rzOIQdKM/eMJBAq7yBgaB6krIYLGejrwVYnSHZdqjscB1SPuAjHwxjvN6Wdg==", "signatures": [{"sig": "MEUCIG985yPk+SvcScZ5bcBrPTEFBQbT8zYvJaNU5gFsOVF4AiEA6BxSG1tKRrNJH7g3xyFGpyUfcDUzVkmNaJVOBms9UUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZXWUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruYxAAm7/MqtQ+hsRl/IArQVTAXOa0jZpCoANtZntBh1rYd8FgS+Zp\r\nSMButPvpV20Z9Wv9HbsabasjnvuORtCcSlK49uzRh8aWpVTqc30xjk+yViHr\r\n1zOGAxsnqmGA1wRtPcDFKHDQbaUGl+lckQ1/1ETXN/iwEWjlkewuIwI5gsxB\r\ndoQbswR83ONBaRUkYTL7RRGzYtog/HiA1xG9I/2HEBA6K5tBhkgLh7i8Qd7b\r\nLbnlxf3pDFv5v2TrHa5SqYHMvM9ZojM/4oie1uTpH9RwGSb/QyoLgTT5sbhX\r\nw30imEimINqMQViyoMGyzbh3EfCVj0KAS5iobbTaEQXUTyhwgJToRBwn0Mo2\r\nchBYxmcrhy1VeSyGwLVNPUEO/vvnKUpQ2c9F8FkbUmInhHLfobtfSCKbRbEW\r\nAaEpkpGeHkDCdrUZKhh1727LoAK4/liTpd/aSeaAU8NSOz7Hj6hBUMYV72fK\r\ncKxIHgWZaJ9MDeV+4B8Hm0t7K6jA5QwR96e4RU5DIAeuIn3HlvLiI5I0Fgqs\r\nZG/vBqt32lt8m/36P8vzpcdagx4rNsKZJd++9FX3rHnHQLAxkaxy4XYisvEl\r\nRzxn0FVzC99BjTaWQ5QdfGtjzriIUXZoVoz6bxujf7jgOkvVIrTfULe2TCln\r\nXtyz6DjVqgqFU4YzrXzJPGsftUEoBQufNzg=\r\n=vP1j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.20.4": {"name": "browserslist", "version": "4.20.4", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0", "caniuse-lite": "^1.0.30001349", "node-releases": "^2.0.5", "electron-to-chromium": "^1.4.147"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "98096c9042af689ee1e0271333dbc564b8ce4477", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.20.4.tgz", "fileCount": 11, "integrity": "sha512-ok1d+1WpnU24XYN7oC3QWgTyMhY/avPJ/r9T00xxvUOIparA/gc+UPUMaod3i+G6s+nI2nUb9xZ5k794uIwShw==", "signatures": [{"sig": "MEUCIQCB2ZTRaFrpyKZo4lInXwAyZBR86mbDdlWMCaOZCE2mzwIgHafX4K0D+NCnR5bPmuheexgYPNcwVdBA2THM1U/RxKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuZ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYIw//TUZnv1BRVcE25KETG1OyeIw381Uk+Bs7+ORpterQIU3/tu1E\r\n+s8TityAknrQzLX+FR3LDBOF04PpEypDIOYTGPuZzovl24h+Vpjol+fbbJXe\r\n5YhyO2OB4rB+yEz4qwJwjNDwWQjPztR1qwnzCQ31okARKB00M3LTPKycrg9D\r\nnx4OUJXWkoMiwhK6Mva2V4EMP4ACNrO//70XDCKjxUOBZxWoXFrOTdlRJZ+w\r\n6IBI0h5WG2toEGioeeFUWr3l0ayi6L/7vTHZPMux7Wn+8oXt+le3M0L2xSgh\r\nROtfKYB+CfO6QyCJe4WZdnDjDkQwzELYs+jxC41v4L2oNNuPC25lZRiepDqL\r\nhPUt6f7qobxJkmvswO69whq/VZ9vMyMbkCFMRuOj1O2WnGcrkxS4JP+TlJOd\r\ny/m6P8IDCfa8Gkjv+gg6xUOR6lvs01sOGBfeZMk37HDYTdppMdZ4p39mi8j6\r\n8Xhxs9traIarBXGncCAJ6MIpJoEv2DjjA4Z3T571StM5bABRXWCK3uvrMSZU\r\nvg7s9Wc4U1bxG13QowRdKpgzGiAgKnFjPDYoGTZWyqjrWPY6VCqWYY0L4hFM\r\nAqZ9bFmgAK4791jUcKVFJfdLjw5I7YfFG4GqTYZkZxigYvrR53N6A8HtnX3O\r\n2NRmteQZ2dswrgHtpIQTd9z3eXDrLQaufo4=\r\n=6bAk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.0": {"name": "browserslist", "version": "4.21.0", "dependencies": {"caniuse-lite": "^1.0.30001358", "node-releases": "^2.0.5", "electron-to-chromium": "^1.4.164", "update-browserslist-db": "^1.0.0"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "7ab19572361a140ecd1e023e2c1ed95edda0cefe", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.0.tgz", "fileCount": 11, "integrity": "sha512-UQxE0DIhRB5z/zDz9iA03BOfxaN2+GQdBYH/2WrSIWEUrnpzTPJbhqt+umq6r3acaPRTW1FNTkrcp0PXgtFkvA==", "signatures": [{"sig": "MEYCIQDuhE0A9FLP9otAS5Md8aL8/+fmPSDlThP9owst8LTOPQIhAJubGk+eDq8K/3vioplHJmLq2hKSSVggZl580hnwk4bM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisfRYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi2g/+MRAk678SHLIKu+ONjtkBuwMNXpZhxuI7xFGgx5LhkmHSaNU8\r\ngM4NV86etqq67lMYWesFWIoTRqekZwd5OELRvNcuHsKrD67Ow1pBk9puwhlD\r\nRuIEa1TfEVUZpgsqMfHtlC6y86A+mrPZjgnMOU9eWkVfwwxg+8Dq2Gw1wKC+\r\nT2Ce961Q3RBGaiMzkHsnTZMMDvGCKzmCSQKMdlQnO0SgvX5Gt+abQ+UIlths\r\nIJHM5eve5zdOE8vYFl5QITTSw+uxizhKJSekKfJWDZXULiD2dV1wN4/yPYrN\r\nF3NIUWRyvIjtal5LX0Hs5reJ909Wi9pW9xmdg20aqzEwlzmy2E1AGlbiF6bt\r\nnC5MBel96NSJo8i+m+hHBOdsuRnVvBdZyis3v/nUqpJBgCbJWE/HHgOr2gI2\r\nCJITGLRaq9/F0onnTket1uQUxG+a9WjqgAEfm2mhwFEGi/JaS51u2skyZVCZ\r\n0geQsfjhnYq027towozInFO+Hs8f3U3iYaNWhrUkY+KBSktyYws2kZMhnA1r\r\nv1quqoADlENMeYL/uOQLZyUV1uRea62PbFmYKQKDVOIGsZo/IwXUXjrkBF3T\r\ny3mwn5/20H390E0mpefXit4Z5fC9PLAmTiz8BYXmyvnYfBm+vezxq7ARmO9S\r\nPyGljUmy9AQ0Cjveuq+93GPLFdxENQAIDUA=\r\n=vFCv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.1": {"name": "browserslist", "version": "4.21.1", "dependencies": {"caniuse-lite": "^1.0.30001359", "node-releases": "^2.0.5", "electron-to-chromium": "^1.4.172", "update-browserslist-db": "^1.0.4"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "c9b9b0a54c7607e8dc3e01a0d311727188011a00", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.1.tgz", "fileCount": 11, "integrity": "sha512-Nq8MFCSrnJXSc88yliwlzQe3qNe3VntIjhsArW9IJOEPSHNx23FalwApUVbzAWABLhYJJ7y8AynWI/XM8OdfjQ==", "signatures": [{"sig": "MEQCIGoki2HvRDsu0W38MUBQ/xcHq2DY7OwImcZC4agOqEjfAiBR9tfKnhtAXmqiaefR3MMLEdEdy4htvJRhI/Bo+B7i0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuyxXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogVA//VjkZ4SE87sDWtVmumfmNwx+wGocljrNF8m0InoVKsG2Ndq9D\r\nRKm6I7p/LL7DovzfN8d80igBjPA3UVaGzabZHqF6IGnF/jOy4FYROll3AXyV\r\nzQm3vTeZghsta9hq0uk3WZ7Ywfw8WQYoktdlA6LYK4XEEyYRlXx82nJtVY00\r\nM+CFR7Yo3qoERRGE1QggqZMO8NKHhBHMvNrzDSJCh5g6rHruJLxZU+jlvG1x\r\n+OlMhe855Gto28aohVJnPJRhnAnujUccMCL1p3dhaGm2Y8D1xwMmVfDcpeCY\r\nMw+wHRVNl82OlClqQ6jwRpYqBA+FJBi3aSS8hBdhUpWwScbDlAzuQv0MqQSq\r\nah3XfF0ZaWhEEGyqQbrsM3IrNT9MI8MW62VEs2zEKSZfawUZIhRr/3brkhyi\r\ng7wab1kufSfttgn6UQAhEun2RjgQOMeNZ+bkColbdMYYZ5wNfx+kuJZzGNw9\r\neChefmTC4ROk01857aTvxTNvpysNBtPXR00/K8z/ILUqVvI4bsqM2LFPtcPl\r\nCrYqkf1AfdmfQma8MQbUdgqdqfNiL6lcE8uh1RevpBgvxG8JNzGepSifcQPf\r\nAUqqHrn/IP1Spnjwa92PTdeUofP4dnkzEbgxAi9rpDcvMsKmWq7WxUoT6vbc\r\nFS4YiLWBhNOyO42d3cRC7/RSHwGFxC48Fdk=\r\n=o0nm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.2": {"name": "browserslist", "version": "4.21.2", "dependencies": {"caniuse-lite": "^1.0.30001366", "node-releases": "^2.0.6", "electron-to-chromium": "^1.4.188", "update-browserslist-db": "^1.0.4"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "59a400757465535954946a400b841ed37e2b4ecf", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.2.tgz", "fileCount": 11, "integrity": "sha512-MonuOgAtUB46uP5CezYbRaYKBNt2LxP0yX+Pmj4LkcDFGkn9Cbpi83d9sCjwQDErXsIJSzY5oKGDbgOlF/LPAA==", "signatures": [{"sig": "MEYCIQCorI2pT+00K2Ej/NJJSO1E6mQQoCUNB6Ufa+5l/jzQwgIhAJCX+WiY1yNbu7LB454hOTLEgV+77p6Vax6DhncpUQMK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizvjdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPOA/+J2YXmNXxJNR31wwp4c78F4Vhz8RZjQAyhp3sY6bUf4pbCcrT\r\nTgIj3olzAYRYzQ9nG2tS/2Cd7224SpYD8OTm+U8c+qEoXoqWiewKwH4h1Tzq\r\n2GYPmA37BxKg68jR0hptLDmud4S3FPTw1KHSDaK/S/LOji0QiOJzESiRBu/z\r\njUoQbdSVDkXETTShAffD+T8LCfG7hqHOLqEy8F/scSlP6vNqOk9dxZZJoPW3\r\nOrl/2MYwSU371DPFaEn2xlmMFWQz56dwYSZgURoAUHavToF4zUnUMcYb8qdt\r\ny8LPBcGC8pfiqkE/djPMIa2/IjgiCBRGZBCGSukhZjzX2eB9MKcZON/W6rBr\r\n8WN5ZIo7aR+PnjDeDkDXvrqKl0+CEwVSSPaA46/v9d94kuSSCOY53aIwPA5Z\r\nT8WHS9L5zvxYupwaZm9uUdMnU2Y4XnqzGnLhvMVzIKb7XXAfMxQJFRdFM8a2\r\nVikCMeZvv/YTSXm6XTFmS+OPWpg6UDVberJzShO2nkrbZYs36Qxoeg7XnPRU\r\ndCoK9+2irEB8dazcBZWp7BuO294bl5YNv9NhM0QGoMaErFU9tV0jE14aRdlQ\r\nVP9oH1J8d/4ppq6fYQS+5d1A0bp+uB2W75qqw6Gq6TZPJIfkzaOPYXLhV/1H\r\nHC6WDx46rOavXWLgTEkQoghtV3IoSAlvNY8=\r\n=Tm5x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.3": {"name": "browserslist", "version": "4.21.3", "dependencies": {"caniuse-lite": "^1.0.30001370", "node-releases": "^2.0.6", "electron-to-chromium": "^1.4.202", "update-browserslist-db": "^1.0.5"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "5df277694eb3c48bc5c4b05af3e8b7e09c5a6d1a", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.3.tgz", "fileCount": 11, "integrity": "sha512-898rgRXLAyRkM1GryrrBHGkqA5hlpkV5MhtZwg9QXeiyLUYs2k00Un05aX5l2/yJIOObYKOpS2JNo8nJDE7fWQ==", "signatures": [{"sig": "MEUCIDDfaAaarz0AmnYzcb2WYgk9h+Tg6ozWli1beUwLoz7PAiEAlft9lsCAjc3Z8WokxkkDMWN5buEYwIyHYNYDAcAcxak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4QPjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOKhAAiTPcEarWfoLwjlCVDdSwvv/CJeohSoOcPTG8onLdrKQuYytC\r\nZr//YCdXz0czooD66MsACGYxSTs+J4vothE0mWMwuNvasLjatG34QpNZSZiu\r\nPrIhlQffXfak4bIjrY0Y8RlW+FSrOllTgFPkl7d5MDptpHIL7wMRWm4+Mb4d\r\nIsZHHpgLkwRqibZNnHqCzizNsmWsS5K1hsFOpgq5vgC0FEQuAuBdQP1n0VrQ\r\nJgz701j2WoEpn264v34Df3xc801d1Ix32diC4Ck/Q1KQn8+sxAsd7vb5mrUQ\r\ngamLAN+IA9jErDAo7Eq/Xf3i/iq2jUmFB5CLN/oVVNmfpCQ5hiLMHPbI2SsP\r\n3LIOznEAG3L2ELGHrIujd+eAeRiHr928l9OuyjOFez/Qy+PgvtXeOlAJI9WN\r\nUNOev3I7hv/idKsPrQF70WYcUDWs0l+vCbZ6ASK6J8mo+yPCtG3/deq4SBJd\r\nqCKlFNvDxb4T3Zv0pkPmN8rtbpyCSR3w5cRJCuUqfiFPv+RjofJTq2YrC2y9\r\nNyh5kksQ/CZu1LthKcA6JlIJmx1AJg/MF6vBg3jbL34Yv2auBDBACKj/eGjT\r\ntH1eTYaZaH7DPsK4P4k9/ZtHDs6M1l9Vvg1PXTfabEmhZt7LW/TXI5RBDHa0\r\n5d6LOfXNmlsTRVLkFiJyi0FHr7xmpIAY5KA=\r\n=HeGm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.4": {"name": "browserslist", "version": "4.21.4", "dependencies": {"caniuse-lite": "^1.0.30001400", "node-releases": "^2.0.6", "electron-to-chromium": "^1.4.251", "update-browserslist-db": "^1.0.9"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "e7496bbc67b9e39dd0f98565feccdcb0d4ff6987", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.4.tgz", "fileCount": 11, "integrity": "sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==", "signatures": [{"sig": "MEYCIQCLlgeqRt90GIl8ntoEd6jEzc3ng/jbX+LbOr1nyqD1UwIhAOPLrVWBRGOkIWsjbH2eJB2xIr/5EeFXCbIBAwJKek9n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIwC2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSjg/7Bn1wNnm1PYQMrjeHHczDb6lgwkxkqwiev7IFOEfASlCLjLgR\r\nvegvBTlOljHguOIbhtCfANu1xVAXARKrAq/bjaHjp9MbR8UMiZe/EfHkixYQ\r\nHLsxcqMs8PvURernPsebHXoL8BoRsb0Pdft9a2hRRc36pgAerGsU59Gfm2Xv\r\nV5iMb+AZ0J74a5T/HI51S+d+aONdcBtZDU27c/ovO+bNVNFJSY+VrcxA2jxS\r\nEReH2fU6zRoTIdvRduR86HQacyEfi6hd7vTN5p5fwo4IjirurWMi8BpzPLzg\r\nxEAuj01o8aUAPgDSpSJ0YAvMS4FEebZrdmNOzNUjIwmCcUhaYE5iXD672D7b\r\nAhgSinXCSzEiOUi9fjNcAtiSgPoPP27HAkYgv4T8XyhvMAJZJ/RhLNg6jh+e\r\nlPDhGtBIVI7zu6HooItiIcu40WN/XudQSRKlO8Q6YuzsGM9+KOYKc4V9he3i\r\nVAQmgfXYdYQo20O7wfyTpW8wgiE8miY97ES1Mwr9D78bnxh+pinBSXDwH/cy\r\ne4lkxvtJ2PJ43GT5hDhl9KeujvZMLSUWFQ4Yl9nMrsveESoC7SW6xoMjz6JK\r\n2pAYu1A8wm/dbkcY/pA0vvKGlb5hCiLtbvvurphmEj5uq1fDTe0/61voryki\r\nnEDRaeA5whqRG/dN7Jyve5dw26517abtrKM=\r\n=+ewH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.5": {"name": "browserslist", "version": "4.21.5", "dependencies": {"caniuse-lite": "^1.0.30001449", "node-releases": "^2.0.8", "electron-to-chromium": "^1.4.284", "update-browserslist-db": "^1.0.10"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "75c5dae60063ee641f977e00edd3cfb2fb7af6a7", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.5.tgz", "fileCount": 11, "integrity": "sha512-tUkiguQGW7S3IhB7N+c2MV/HZPSCPAAiYBZXLsBhFB/PCy6ZKKsZrmBayHV9fdGV/ARIfJ14NkxKzRDjvp7L6w==", "signatures": [{"sig": "MEUCIQD0XHSVAgL/NPe/i20/v1wK0wtg070T8GIZS18EFOvTLQIgRoQX/or0wH43PUIMYs+x1HJLIjcQQmuA2Zd1JKaZSCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj17HQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQlg/+IfJutijt+olNrnUqAH5LOGvmMH/c8uXsdFv62GnDXlTdVT2b\r\nIhTtt61aokbgwFhoOdIVQKXqw3k/uGy4pVzT5O4CigvZivbQYPZpYQXYnlxS\r\n/Yf9yL/kH9ekvVjv4XO9fYPPq5JA5UsTJzx1EuxNM1cnYCBl+Ltmu1+PAYJI\r\nW1JacUm+rx4KENjPuYCPzhpq+MqdXcurG/RxxTcIWwNejZhC3JhqVOpBPgF7\r\nZowQ3ooPIFUFStyOoZKUqnC428pLyr4IkBeME2H6jySew/9PL9ooyTsc7zxP\r\nan+iY1OC/gP4DqTtwK6x51pCHkM++sXYr+F645b1JYcAN6cIqkKQh/JgCUnN\r\nX9AifsOMhTgDnp1Ek7kg8tsZI6PO9ZL9gtEmX7ZQLmj8fD79LMj0klwgYc4g\r\n8xnuSjR4GYtt86vLdQTtvGlaLKg3Nqisorh5HhLO6G1ArwA6bS+mdxo+TRpb\r\nfz8DvKncHXQht4oPZ2l/pGv0go20dJwN3MDH+T0r6VDdYAkUQeB14rZeAy6x\r\njc6xkzFQEJ5IXqx+IduopetEClA9O705zKy7gZmr3xR7sxHJhRzfsLeIxj29\r\ny0uu6pJ7QopCVG5Hg8KpKeP1DE0jhPD7masNhtkdSSvao+ELrXATX4Dk5Wga\r\np98gzaP8iuPyJt1O0rPV/vbhBFmgKE8sY3w=\r\n=fvYE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}]}, "4.21.6": {"name": "browserslist", "version": "4.21.6", "dependencies": {"caniuse-lite": "^1.0.30001489", "node-releases": "^2.0.12", "electron-to-chromium": "^1.4.411", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "18ab9830a5a61806a909a4717f85665792e7f267", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.6.tgz", "fileCount": 11, "integrity": "sha512-PF07dKGXKR+/bljJzCB6rAYtHEu21TthLxmJagtQizx+rwiqdRDBO5971Xu1N7MgcMLi4+mr4Cnl76x7O3DHtA==", "signatures": [{"sig": "MEUCIFhKpOwyaG9HSdWKE7TYsXYRihVGq9jOGMkZ8h+HXlcJAiEAveZOp+PiSqqXqRTnZsXpSyn2ASffgivPnCZC0dR8Tr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61493}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.21.7": {"name": "browserslist", "version": "4.21.7", "dependencies": {"caniuse-lite": "^1.0.30001489", "node-releases": "^2.0.12", "electron-to-chromium": "^1.4.411", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "e2b420947e5fb0a58e8f4668ae6e23488127e551", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.7.tgz", "fileCount": 11, "integrity": "sha512-BauCXrQ7I2ftSqd2mvKHGo85XR0u7Ru3C/Hxsy/0TkfCtjrmAbPdzLGasmoiBxplpDXlPvdjX9u7srIMfgasNA==", "signatures": [{"sig": "MEQCIAu9/2wb/2YfHcBANY8QwrHP2PpV/bECLKHwmq4F8VLyAiBk4TX/L1orqELOKCABHy14VBljJW4kzCmlvpziIA87TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61453}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.21.8": {"name": "browserslist", "version": "4.21.8", "dependencies": {"caniuse-lite": "^1.0.30001502", "node-releases": "^2.0.12", "electron-to-chromium": "^1.4.428", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "db2498e1f4b80ed199c076248a094935860b6017", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.8.tgz", "fileCount": 11, "integrity": "sha512-j+7xYe+v+q2Id9qbBeCI8WX5NmZSRe8es1+0xntD/+gaWXznP8tFEkv5IgSaHf5dS1YwVMbX/4W6m937mj+wQw==", "signatures": [{"sig": "MEQCICIQJo8B7PnzUbR1aDpO8OU5L7iih4BEtG6ejzvXfu06AiBbHTIUspqfHW2dH4WOwNw7PtCSAo6HUaIaU4MH8dKG2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62107}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.21.9": {"name": "browserslist", "version": "4.21.9", "dependencies": {"caniuse-lite": "^1.0.30001503", "node-releases": "^2.0.12", "electron-to-chromium": "^1.4.431", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "e11bdd3c313d7e2a9e87e8b4b0c7872b13897635", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.9.tgz", "fileCount": 11, "integrity": "sha512-M0MFoZzbUrRU4KNfCrDLnvyE7gub+peetoTid3TBIqtunaDJyXlwhakT+/VkvSXcfIzFfK/nkCs4nmyTmxdNSg==", "signatures": [{"sig": "MEYCIQDlmM4tVcwU7eYOCxU6qvN8vcYyIa6z/M8GatSssV+x4wIhAJEgbCamACQaEDwbuymsF/ffW3lGVuXqYtMP48NKnoSo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61921}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.21.10": {"name": "browserslist", "version": "4.21.10", "dependencies": {"caniuse-lite": "^1.0.30001517", "node-releases": "^2.0.13", "electron-to-chromium": "^1.4.477", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "dbbac576628c13d3b2231332cb2ec5a46e015bb0", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.10.tgz", "fileCount": 11, "integrity": "sha512-bipEBdZfVH5/pwrvqc+Ub0kUPVfGUhlKxbvfD+z1BDnPEO/X98ruXGA1WP5ASpAFKan7Qr6j736IacbZQuAlKQ==", "signatures": [{"sig": "MEQCIEgEOm1c8cWUyhiQiRHbEhIQe4nlxozGJcyKWtGLmK/KAiBRlpDvEXWyNs0uVRHNo/fsn/sPjcB17KfmFoj8GAAfbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61937}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.21.11": {"name": "browserslist", "version": "4.21.11", "dependencies": {"caniuse-lite": "^1.0.30001538", "node-releases": "^2.0.13", "electron-to-chromium": "^1.4.526", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "35f74a3e51adc4d193dcd76ea13858de7b8fecb8", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.11.tgz", "fileCount": 11, "integrity": "sha512-xn1UXOKUz7DjdGlg9RrUr0GGiWzI97UQJnugHtH0OLDfJB7jMgoIkYvRIEO1l9EeEERVqeqLYOcFBW9ldjypbQ==", "signatures": [{"sig": "MEYCIQCJVHK/OTOBZmGxFPX5FabZH1En0bDT063oUXfkSYkRIgIhAK/rtRJfMk978/YT+szj3smsbn0YRc4Old3NVIfTxqJQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62149}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.22.0": {"name": "browserslist", "version": "4.22.0", "dependencies": {"caniuse-lite": "^1.0.30001539", "node-releases": "^2.0.13", "electron-to-chromium": "^1.4.530", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "6adc8116589ccea8a99d0df79c5de2436199abdb", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.22.0.tgz", "fileCount": 11, "integrity": "sha512-v+Jcv64L2LbfTC6OnRcaxtqJNJuQAVhZKSJfR/6hn7lhnChUXl4amwVviqN1k411BB+3rRoKMitELRn1CojeRA==", "signatures": [{"sig": "MEYCIQD/priejha5OnN4EZu1AEOH7eN8wWVfz1JOivxHHbqXdgIhAPuYLzqqABYzwD68lKY0LAAVIi5SI05iKZ/zLJEBf85J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62307}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.22.1": {"name": "browserslist", "version": "4.22.1", "dependencies": {"caniuse-lite": "^1.0.30001541", "node-releases": "^2.0.13", "electron-to-chromium": "^1.4.535", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "ba91958d1a59b87dab6fed8dfbcb3da5e2e9c619", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.22.1.tgz", "fileCount": 11, "integrity": "sha512-FEVc202+2iuClEhZhrWy6ZiAcRLvNMyYcxZ8raemul1DYVOVdFsbqckWLdsixQZCpJlwe77Z3UTalE7jsjnKfQ==", "signatures": [{"sig": "MEQCIE4RTMMIC2xBbfNs7EZE5xticHBgqwZhqgmkJrUEw+uSAiB1rD4GwbGWgCe0VSecJQuuobWVPfOblC0PJ7jaUHT5Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62292}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.22.2": {"name": "browserslist", "version": "4.22.2", "dependencies": {"caniuse-lite": "^1.0.30001565", "node-releases": "^2.0.14", "electron-to-chromium": "^1.4.601", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "704c4943072bd81ea18997f3bd2180e89c77874b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.22.2.tgz", "fileCount": 11, "integrity": "sha512-0UgcrvQmBDvZHFGdYUehrCNIazki7/lUP3kkoi/r3YB2amZbFM9J43ZRkJTXBUZK4gmx56+Sqk9+Vs9mwZx9+A==", "signatures": [{"sig": "MEQCIHrgVYo95NKQBqc0EWcNBDMq2nX4kE3DU1m4HpefJU0qAiBwWKMcrYexnRjdK0QPt76hzPt2bN26O5xtafpjxtsJqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62462}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.22.3": {"name": "browserslist", "version": "4.22.3", "dependencies": {"caniuse-lite": "^1.0.30001580", "node-releases": "^2.0.14", "electron-to-chromium": "^1.4.648", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "299d11b7e947a6b843981392721169e27d60c5a6", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.22.3.tgz", "fileCount": 11, "integrity": "sha512-UAp55yfwNv0klWNapjs/ktHoguxuQNGnOzxYmfnXIS+8AsRDZkSDxg7R1AX3GKzn078SBI5dzwzj/Yx0Or0e3A==", "signatures": [{"sig": "MEQCIBN6b8DDCJAp9WlpMjsSk7phZLfXQaUMmXABtF8QYPu5AiAELWrsFol3SzlebvPoHnlAdyxd1z2BZBk7sEDqZ8WVpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62446}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.23.0": {"name": "browserslist", "version": "4.23.0", "dependencies": {"caniuse-lite": "^1.0.30001587", "node-releases": "^2.0.14", "electron-to-chromium": "^1.4.668", "update-browserslist-db": "^1.0.13"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "8f3acc2bbe73af7213399430890f86c63a5674ab", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.23.0.tgz", "fileCount": 11, "integrity": "sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==", "signatures": [{"sig": "MEQCIAkac3mqu9InVfvxjwaON9U7NpEfd2BN13DA4DzrdPSEAiBAXADeFNW/D3lv3SHK1CRY6pPtnteabR9/Twls3Kx23Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62763}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.23.1": {"name": "browserslist", "version": "4.23.1", "dependencies": {"caniuse-lite": "^1.0.30001629", "node-releases": "^2.0.14", "electron-to-chromium": "^1.4.796", "update-browserslist-db": "^1.0.16"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "ce4af0534b3d37db5c1a4ca98b9080f985041e96", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.23.1.tgz", "fileCount": 11, "integrity": "sha512-TUfofFo/KsK/bWZ9TWQ5O26tsWW4Uhmt8IYklbnUa70udB6P2wA7w7o4PY4muaEPBQaAX+CEnmmIA41NVHtPVw==", "signatures": [{"sig": "MEYCIQCDcmNKQvuP10Gp03dLYx6sF+/VaVZtTyroF/0857EosAIhAMZfrds6pplIYh38y6DPsP7Z0dneYy5BM+voG6zUXO+x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62872}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.23.2": {"name": "browserslist", "version": "4.23.2", "dependencies": {"caniuse-lite": "^1.0.30001640", "node-releases": "^2.0.14", "electron-to-chromium": "^1.4.820", "update-browserslist-db": "^1.1.0"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "244fe803641f1c19c28c48c4b6ec9736eb3d32ed", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.23.2.tgz", "fileCount": 11, "integrity": "sha512-qkqSyistMYdxAcw+CzbZwlBy8AGmS/eEWs+sEV5TnLRGDOL+C5M2EnH6tlZyg0YoAxGJAFKh61En9BR941GnHA==", "signatures": [{"sig": "MEUCIQCfiUwzSuOwjbwhR58SyOUs47kIB7o3AWNosP4TgKSUPQIgb8CzDCqf2dQDFas+glocZTZrKbnakaczIyMztv3vx6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62886}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.23.3": {"name": "browserslist", "version": "4.23.3", "dependencies": {"caniuse-lite": "^1.0.30001646", "node-releases": "^2.0.18", "electron-to-chromium": "^1.5.4", "update-browserslist-db": "^1.1.0"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "debb029d3c93ebc97ffbc8d9cbb03403e227c800", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.23.3.tgz", "fileCount": 11, "integrity": "sha512-btwCFJVjI4YWDNfau8RhZ+B1Q/VLoUITrm3RlP6y1tYGWIOa+InuYiRGXUBXo8nA1qKmHMyLB/iVQg5TT4eFoA==", "signatures": [{"sig": "MEUCIQCjdm+pU8pBJkn2aSdL9Gu+w96K5qCiHvrlFw7fl0nnHgIgdkauhnADZyyi+EzNQym0oz0KH4Mzv399fjfmSN1wUdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62978}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.24.0": {"name": "browserslist", "version": "4.24.0", "dependencies": {"caniuse-lite": "^1.0.30001663", "node-releases": "^2.0.18", "electron-to-chromium": "^1.5.28", "update-browserslist-db": "^1.1.0"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "a1325fe4bc80b64fda169629fc01b3d6cecd38d4", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.0.tgz", "fileCount": 11, "integrity": "sha512-Rmb62sR1Zpjql25eSanFGEhAxcFwfA1K0GuQcLoaJBAcENegrQut3hYdhXFF1obQfiDyqIW/cLM5HSJ/9k884A==", "signatures": [{"sig": "MEQCICMOJgYx/HSBu6mYt092ZSEfy1Y1JF/026A3JNC5GE5PAiBvJSo+zFSTBg4gnVHg1xRj4xRGEokeec8BWUGugiztJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63505}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.24.1": {"name": "browserslist", "version": "4.24.1", "dependencies": {"caniuse-lite": "^1.0.30001669", "node-releases": "^2.0.18", "electron-to-chromium": "^1.5.41", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "cf554ba3d4142cce8119d970bbe332feab6d6ddc", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.1.tgz", "fileCount": 11, "integrity": "sha512-1p6HVUoq1POlayfJXpDBZ0YbvAbcRpMz/QgX0wzbCcPRMwvmiWDaRGgWqELv6ugiloCT3lSiWf9fJblXGB90Gw==", "signatures": [{"sig": "MEQCIFStsGe7kaEUuZnLzPHx8n3uk/UyPOdXeoqrBuAYSBdEAiAhO0/kyLa/UPJwhKNvb2gWQ1hPCvksLgLw1uyngfMpSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64289}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.24.2": {"name": "browserslist", "version": "4.24.2", "dependencies": {"caniuse-lite": "^1.0.30001669", "node-releases": "^2.0.18", "electron-to-chromium": "^1.5.41", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "f5845bc91069dbd55ee89faf9822e1d885d16580", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.2.tgz", "fileCount": 11, "integrity": "sha512-ZIc+Q62revdMcqC6aChtW4jz3My3klmCO1fEmINZY/8J3EpBg5/A/D0AKmBveUh6pgoeycoMkVMko84tuYS+Gg==", "signatures": [{"sig": "MEUCIQC8v61bCHxWparJY7aPQhKFiDQCgXmESam43jkdFFrTuwIgDt5qA+G3P5Z3nzvDyF5oDQstveB0pi7WW3g1pifrC70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64180}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.24.3": {"name": "browserslist", "version": "4.24.3", "dependencies": {"caniuse-lite": "^1.0.30001688", "node-releases": "^2.0.19", "electron-to-chromium": "^1.5.73", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "5fc2725ca8fb3c1432e13dac278c7cc103e026d2", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.3.tgz", "fileCount": 11, "integrity": "sha512-1CPmv8iobE2fyRMV97dAcMVegvvWKxmq94hkLiAkUGwKVTyDLw33K+ZxiFrREKmmps4rIw6grcCFCnTMSZ/YiA==", "signatures": [{"sig": "MEUCIQCVdj8lxLrxvJe8DB7ZmGOtqb5mgdZSXtIbrtRjwKvYcwIgfF5qrUkWWNAygHhskIcdFOyHVAXeML64fAGVVBxKTBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64165}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.24.4": {"name": "browserslist", "version": "4.24.4", "dependencies": {"caniuse-lite": "^1.0.30001688", "node-releases": "^2.0.19", "electron-to-chromium": "^1.5.73", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "c6b2865a3f08bcb860a0e827389003b9fe686e4b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "fileCount": 11, "integrity": "sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==", "signatures": [{"sig": "MEMCHzm3wiCD5vj+qodsJa0dxznMHN2t5EE1SqRHZXTMg8UCICW2QwanNNVQ3zxsuW4gidLlxG+41/BEbhpX4UNBBEKr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64926}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.24.5": {"name": "browserslist", "version": "4.24.5", "dependencies": {"caniuse-lite": "^1.0.30001716", "node-releases": "^2.0.19", "electron-to-chromium": "^1.5.149", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "dist": {"shasum": "aa0f5b8560fe81fde84c6dcb38f759bafba0e11b", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.5.tgz", "fileCount": 11, "integrity": "sha512-FDToo4Wo82hIdgc1CQ+NQD0hEhmpPjrZ3hiUgwgOG6IuTdlpr8jdjyG24P6cNP1yJpTLzS5OcGgSw0xmDU1/Tw==", "signatures": [{"sig": "MEQCIFAerKTpbaWJqjimlcZuV8o9naShE/+TBuM221VtY0dVAiAamfd17BnQh01wtems3ruViVul48TMKfPjnCxkiPjuJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 65174}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"url": "https://opencollective.com/browserslist", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/browserslist", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "4.25.0": {"name": "browserslist", "version": "4.25.0", "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "dist": {"integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "shasum": "986aa9c6d87916885da2b50d8eb577ac8d133b2c", "tarball": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "fileCount": 11, "unpackedSize": 65447, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCxb+GdT+2svDQJx+8f4dtawTh3r8Us0PHExq2eLutiogIhAPtX78OR8UKfSkM1PlewpNzwe7tuYFGkFoN+aV32E2fy"}]}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}]}}, "modified": "2025-05-29T01:49:40.211Z"}