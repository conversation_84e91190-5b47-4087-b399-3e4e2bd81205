{"_id": "oauth-sign", "_rev": "26-30730e3f7e611630bdb32e617bab8c25", "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "dist-tags": {"latest": "0.9.0"}, "versions": {"0.2.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.2.0", "repository": {"url": "https://github.com/mikeal/oauth-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "_id": "oauth-sign@0.2.0", "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "a0e6a1715daed062f322b622b7fe5afd1035b6e2", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.2.0.tgz", "integrity": "sha512-4DtiD64CwPJ5vZ636j/KtM7DxWbX1KlkqwbqbEAxI3BCpBrQdrKOv8vC/36U6gfm1CVapy6QmcVxPnXPPQApTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE+3CsJ58Kdso+Aya5b3G3I+lPI8yd3HuHEqag385E5TAiEAlFFUiMLFkEOVnavd91+20re2Ms1OCh6MI3Mc/3zcrh4="}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.3.0", "repository": {"url": "https://github.com/mikeal/oauth-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "_id": "oauth-sign@0.3.0", "dist": {"shasum": "cb540f93bb2b22a7d5941691a288d60e8ea9386e", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.3.0.tgz", "integrity": "sha512-Tr31Sh5FnK9YKm7xTUPyDMsNOvMqkVDND0zvK/Wgj7/H9q8mpye0qG2nVzrnsvLhcsX5DtqXD0la0ks6rkPCGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBqdHBhqW0bqTEBp0UnNEwWTgVV3maVWFnRYwMZJ8IKvAiAaUmwUAeWHUMhznBVI0pz0ZhlfGZ3fPWKvhQPlYjnxuQ=="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.4.0", "repository": {"url": "https://github.com/mikeal/oauth-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "_id": "oauth-sign@0.4.0", "dist": {"shasum": "f22956f31ea7151a821e5f2fb32c113cad8b9f69", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.4.0.tgz", "integrity": "sha512-vF36cbrUyfy7Yr6kTIzrj3RsuaPYeJKU3IUOC6MglfNTyiGT6leGvEVOa3UsSsgwBzfVfRnvMiMVyUnpXNqN8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDjwJVmjoHr8HF+XRcVn14Vi5paERpFFvR3LIiXk+tfQIhAPPZ+eteW9NU5seiolIalK1Zfp6D706g9i8eoCliLRyC"}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.5.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.5.0", "repository": {"url": "https://github.com/mikeal/oauth-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "6fea86c2d4a38e1b3780ad0cc56f00196e5213c1", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign", "_id": "oauth-sign@0.5.0", "_shasum": "d767f5169325620eab2e087ef0c472e773db6461", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "dist": {"shasum": "d767f5169325620eab2e087ef0c472e773db6461", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.5.0.tgz", "integrity": "sha512-jXeZq5EriUSGdNIePO45lhemfuCBKi5DARdE30v173MPCLymq2DxR477J/RuCXLphNx7OVAqXVyj3JoUaiHpNw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDfO1Ka8yvPueWxd8Ebgh8R/p742fu37c7w4L3QW4hkQAiEAtynL2JGmB3mvbEs+QRVH81gwv/UwFM/yeFDkRYtRb0k="}]}, "directories": {}}, "0.6.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.6.0", "repository": {"url": "https://github.com/mikeal/oauth-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "f1b5d7714712ab7eec485cca9d18ae95db58aa6b", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign", "_id": "oauth-sign@0.6.0", "_shasum": "7dbeae44f6ca454e1f168451d630746735813ce3", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "dist": {"shasum": "7dbeae44f6ca454e1f168451d630746735813ce3", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.6.0.tgz", "integrity": "sha512-E65G/AGfoCE6FILW9X+4cfJu27PNIi40brTmDmnrWIjOdPaaJSNti1XZ/+WzFkyIdMxYk0/WtwGNiQr6puZGWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICR5CJ32kAu5Z4t7UTCxrTkOVU9WiGvlXTVMHYMP9x9lAiEAyxPBdxVPrP/lWwdqMNKDwENGoMkNC39jFOpWmiePaf8="}]}, "directories": {}}, "0.7.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.7.0", "repository": {"url": "https://github.com/mikeal/oauth-sign"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "494c8991dbe1c672141445e0df0266a322ef8aac", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign", "_id": "oauth-sign@0.7.0", "_shasum": "1fd7eb1a357d27dc83c167dadae5d7e3bb42824c", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "1fd7eb1a357d27dc83c167dadae5d7e3bb42824c", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.7.0.tgz", "integrity": "sha512-6D0JqRII8EVzJ/otCga0s8irPXwiEtkEsyze5k40ZAxSBE9t3PzeFB5bndmkU6S9qL8/mCaZfmAhvDXJjIwcMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC7/OHNOFg9VsqoPchDSFeyDd400s3xru2MQVJpn13YBAiEA2UdrshCNKTQy79oXGKZQ9yKJy5K7BDA6INjWmY1ejsw="}]}, "directories": {}}, "0.8.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.8.0", "license": "Apache-2.0", "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "e1f2b42ff039901ce977f8e81918767d97d496b5", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign#readme", "_id": "oauth-sign@0.8.0", "_shasum": "938fdc875765ba527137d8aec9d178e24debc553", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "938fdc875765ba527137d8aec9d178e24debc553", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.0.tgz", "integrity": "sha512-cZAErO07dyAFyYKlrbm/tmxiqGxWZdsPAD0645q5OSvbpJbIZXjYspbBs0AaVznJCDneZlIZ2lgy3cyA3iKuiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVADXJsqa5FGi0DluLcg4j6ImJpNvB2DB3MsGWmJ6YRwIhAMGT7EAdyZgtYp1Kj7ZPiE8FMoG8/t9MSiDfD/fqt1BY"}]}, "directories": {}}, "0.8.1": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.8.1", "license": "Apache-2.0", "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "9c7229a336c9face98b83f93b72cb7c80dbba08d", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign#readme", "_id": "oauth-sign@0.8.1", "_shasum": "182439bdb91378bf7460e75c64ea43e6448def06", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.5.0", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "182439bdb91378bf7460e75c64ea43e6448def06", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.1.tgz", "integrity": "sha512-3SZ43ApiBSPPXu9SW+YnWuXEUL6zv98vpfYEsvi+qBa7ZGW6YH2ODLnfYxR4OMp//C1zQc+P09FQxz9sTb596A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEujG25ZxGgK9djRbmR2W+K0rd90a2LVVwGnFrcH+uxQIgGmI7wc9tjQTBEIz3NZW7IN133St4G37kB1GmPPDDwCs="}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "0.8.2": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.8.2", "license": "Apache-2.0", "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "0b034206316132f57e26970152c2fb18e71bddd5", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign#readme", "_id": "oauth-sign@0.8.2", "_shasum": "46a6ab7f0aead8deae9ec0565780b7d4efeb9d43", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "46a6ab7f0aead8deae9ec0565780b7d4efeb9d43", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha512-VlF07iu3VV3+BTXj43Nmp6Irt/G7j/NgEctUS6IweH1RGhURjjCc2NWtzXFPXXWWfc7hgbXQdtiQu2LGp6MxUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsGDMpWVw5ht9ctwZdNWh1NhnqRkiUWUQira5SOdDJ2QIhAIIl3waE0bwT06kDDxjJ4l4tKzF2FIV8hg1Txv2C8xuN"}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/oauth-sign-0.8.2.tgz_1462396399020_0.8175400267355144"}, "directories": {}}, "0.9.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "oauth-sign", "description": "OAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module.", "version": "0.9.0", "license": "Apache-2.0", "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "node test.js"}, "gitHead": "18a2513da6ba7a2c0cd8179170d7c296c7625137", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "homepage": "https://github.com/mikeal/oauth-sign#readme", "_id": "oauth-sign@0.9.0", "_npmVersion": "6.2.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "shasum": "47a7b016baa68b5fa0ecf3dee08a85c679ac6455", "tarball": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz", "fileCount": 4, "unpackedSize": 13805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbY0eHCRA9TVsSAnZWagAAjUEQAIzrYuPpA5aQBQzEmivM\nmcseCarSrww/LRygkxzZjdoqlGOJtbWhIrZbwDhkRwe8jZ9tNxlikODsEpCN\nSHgNNlP4+vLRQTtzBbLRs9gNn+xFqKD5gLR6+6onSjjImykdqXmetAZqILFI\ncH66JIOBUkRFJcd31vMox78v/RJjeikg3rv/DLguuD5/7dvszzSfLEkA3aQr\nXq3PQUZoPFHzEhrv2FD2C4+9AsIL2BkSI57Gq19Q0HHGMvQ5qUIvRqlhDiDX\nVC1Ru74solr0yV2LKP/NSyCtL1AqBVq9Buzb85PRdZMcZ3Ir1ExflgfRon2h\n5Oxlo+ZfWe8cnCiMOOTxN6RBRDte8sK+somrg+9ZzTpK4/tGTHrNsm3dLze6\nQ3WVSizWpZJlgfmjD5RYrRvgNfKi+idAqgGL39W+1YNzQCvF7w9KFbhAMnn0\n4JEH/IrJlWHYoNP196ZQeonRLnJE4pl2IaBPEB7PGMCSm3HX0x291YnxI3ma\nyUxY12l09S9Xl54P8sFwa5UZsbvNxOICeEhzw7X8bGLCHqtXowlWCTo8qIUw\nAtRz84Iz8+6kXwxwf+SY+A365buiNHHJVD/AST6lmUQPbEQHGZUSERrZiHJj\n/xqtQEmQ9YjuWIDA34Py3Kl96wcAh8um8OISlZyZnBjx9ijiloBVucVM2pxX\n45tC\r\n=aokP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnl0Hncx2XEq4jPohEbq/+CD5hwUi3l1+iEpCMTzuJmAIgWYhFxBFwAZt/GXR16B0msJBJPBTii5SnYtQkWWn6aWY="}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/oauth-sign_0.9.0_1533233031528_0.1034004371004853"}, "_hasShrinkwrap": false}}, "readme": "oauth-sign\n==========\n\nOAuth 1 signing. Formerly a vendor lib in mikeal/request, now a standalone module. \n\n## Supported Method Signatures\n\n- HMAC-SHA1\n- HMAC-SHA256\n- RSA-SHA1\n- PLAINTEXT", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2022-06-22T15:38:41.309Z", "created": "2013-03-01T20:23:20.098Z", "0.2.0": "2013-03-01T20:23:20.779Z", "0.3.0": "2013-04-22T05:12:37.269Z", "0.4.0": "2013-09-23T22:58:47.722Z", "0.5.0": "2014-10-13T15:50:26.714Z", "0.6.0": "2015-01-04T01:30:55.552Z", "0.7.0": "2015-03-21T20:51:35.145Z", "0.8.0": "2015-05-28T10:43:18.008Z", "0.8.1": "2016-01-31T07:18:27.473Z", "0.8.2": "2016-05-04T21:13:21.752Z", "0.9.0": "2018-08-02T18:03:51.602Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "repository": {"url": "git+https://github.com/mikeal/oauth-sign.git"}, "homepage": "https://github.com/mikeal/oauth-sign#readme", "bugs": {"url": "https://github.com/mikeal/oauth-sign/issues"}, "readmeFilename": "README.md", "license": "Apache-2.0", "users": {"mojaray2k": true, "wheelo": true, "yorusi": true, "willwolffmyren": true}}