{"_id": "<PERSON><PERSON><PERSON><PERSON>", "_rev": "61-b8cb4552d96155b76526701da5cf350d", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Handler for htmlparser2 that turns pages into a dom", "dist-tags": {"latest": "5.0.3"}, "versions": {"1.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhand<PERSON>@1.0.0", "dist": {"shasum": "a7d75c9eee3b87814305770c787ae9b626c57842", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.0.0.tgz", "integrity": "sha512-qAjwJSx5ARksmja6s5WxsJHJmORWKckZ5V5oKWviotSX9gaOkYaBONi1jqeHYaGZvQVfJBVqJreWI7xT7UdSBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZ+4d74RRWufBOPVCGcIBSN8WrAp8tRk9hHyMw2xxj5QIgXJn8g5p+Tmit9/V/RTCVGkIY6vecDBmuE+W2crGXihM="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "1.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.1", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhandler@1.0.1", "dist": {"shasum": "0815fde05d5767a0c774ce8d37e4444381d5e499", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.0.1.tgz", "integrity": "sha512-1HybvHEY+yaZB9sWwGJ70oZTSAO9J0ytE+ft4WEq+NtEN/rIYipOXUtjhcuN6nzJPssTadqFe5Lw6gbogL60cQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDWPjIAEjCH+HGDPFGs13l4Wp8SXAzVbHfpep+GJYL07AiEA8kExm1Bc8vBMfqZRZiCHi7oJyC6Lb+HiV3hR19IjkNE="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "1.1.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.1.0", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhand<PERSON>@1.1.0", "dist": {"shasum": "f60d691dfed25ca26aef3538f47d2cc081cdf95d", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.1.0.tgz", "integrity": "sha512-mkqBjhjIXgG6bd4XZP42BiWKtW3fy50KVhrfT4T6On5aK1ocLGNhAZ2NMEnObFsv7+M98k28294W3j8lmPTVHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6cK3TBHtfiNlpwjJF0rAIIl60Fl3FRoVeaDWbspiWdwIhANn/pXZ7oETr9yROGOU4K+eWiLP2M1m7HWiB/a9OWA8o"}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "1.2.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.2.0", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "dom<PERSON><PERSON>@1.2.0", "dist": {"shasum": "a21967803f677971c629db13c4b883424096a3d5", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.2.0.tgz", "integrity": "sha512-39e8aoPlmsoXFC+Mcov4t7NOplgsJGPAeYEL3akr0TA8A2hH57awS9SH7uH7ZIYXkwbZKYhNTkr/leDy7iS9ng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCU+PwFjyOkqVr7ndNyxfc5QoApAWn+98q1KC3xdXayzwIgSwA6W0xySa9sfYkVGInD6fJMiR+6Railztos6+rpRzg="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "1.3.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.3.0", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "do<PERSON><PERSON><PERSON>@1.3.0", "dist": {"shasum": "78b2b8d433039ad0fdd1923213a5d1776a089cca", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.3.0.tgz", "integrity": "sha512-81n2zC06HodTQ8ylli0vWUIB5jhrp+tOnF4n8cPhxq6yrlO0or/5eiq9IbEn3uR0GE3HENRpETGIXmJH/iOPzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAawKhSN20shAVGce4rZVnm4EG3xDvgGPw4x0s499+HTAiATUiPo4FS0/GFQS98p5JOjNyrzG3L7MQbQf392PY0LzA=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "1.3.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.3.1", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "do<PERSON><PERSON><PERSON>@1.3.1", "dist": {"shasum": "9421ba3ea870bfeebe117cfa02d967bc346b02af", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.3.1.tgz", "integrity": "sha512-Z5TW+opZ750zlb+MIpqKw3i60o87OpDjiub+SM9HsRcj7UTwKhrllwfuxPzbPd2Lc58Nvg3nTULY4MjkR+uexg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDh3IgqpTJ+xTUzkC0J3XiI8tPy/7dfLnYu8qJtA5wEHAIgLVHoLNBRKiWLShu1lhVTDzqFbWuNhu4Ud+blxhuHFkU="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "1.3.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.3.2", "description": "htmlparser2's dom as a separate module", "main": "lib/index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "do<PERSON><PERSON><PERSON>@1.3.2", "dist": {"shasum": "b7627a0de883fa83db9e5217d2cfb11d58f4746e", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-1.3.2.tgz", "integrity": "sha512-aqgvw+M1S4QV7gGaikqjs30MnBuip2HZSlUv80ECsIZGk1tuHMy/1UXSaE/Vs4P7Jiuectj8tWasyuhDyMtePQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE/Js3Cw2WiUAXdCJJqc+aBh7dkp4A3sdtuo2NoICYOwAiEA8InJaHhBO1o1gP2TuLo2JOQJ7xnRK3YVrT8WI/phWzA="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.0.0", "description": "htmlparser2's dom as a separate module", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhandler@2.0.0", "dist": {"shasum": "231dd23a1f5b4d1c6a04b19a90e93ef40ff55681", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.0.0.tgz", "integrity": "sha512-o6NtnIRd++2oOmDHH2tLLKxhP4nnhqLnNbX6t0cwD1eD9J+xnzU4rUQ7cCbfiMENs0SDwc1wCcTsl8L6caO39g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDReGX73TfWcSrW3jvEI0l4Qalp3YyJNugj96TApDcHgIgDDNoDIOajNuCJ2ICqNNMK7QseKc+CvL1sJTKQPb7EFI="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.0.1", "description": "htmlparser2's dom as a separate module", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "node tests/00-runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhandler@2.0.1", "dist": {"shasum": "c968e41b1bc7bc15d55fbb6811a84ef5ca15e5de", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.0.1.tgz", "integrity": "sha512-R4XVaJykxBoVCAwB33JdqxH5hiqbFzDb5EyukG6TJn4eGfyMKBzoNce7kl51YwZx9J77EQvACAA43CFIbSi9KA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMycI/rj/dMTUwL1GF1QmJmQ1rnPdbRZiANFJQqA049AiADJZ80PeQwnZNID6LdTE+B/bySbcJFEn2JiORPEfE7qQ=="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.0.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.0.2", "description": "htmlparser2's dom as a separate module", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "node runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhandler@2.0.2", "dist": {"shasum": "ca597f42c440173c64d80d838cca738164f1b5ac", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.0.2.tgz", "integrity": "sha512-ooxhlh1uURRVFfHr5MWp0vIeksbtGjqaQbs3kU8V7CwVnr9LtW4+/8SWRlfb2shu+ogU0YXjL/Fl2vhfakqy1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAyOx2DbGoRAzcfAbv3gZycQMzb57KD4AZq6pAnu2/ITAiEAwQ41PohVqSWJX/OSmaKubY+lTWxu+SsFo9ZM5vaKpDQ="}]}, "_from": ".", "_npmVersion": "1.2.10", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.0.3": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.0.3", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "node runtests.js"}, "repository": {"type": "git", "url": "git://github.com/FB55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "2.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "domhand<PERSON>@2.0.3", "dist": {"shasum": "889f8df626403af0788e29d66d5d5c6f7ebf0fd6", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.0.3.tgz", "integrity": "sha512-D8+qeCUo6LpYvBZsmXWunDJ9zOD8mVg6EwZIdgxmnT+xGWRsReF/TwsZ5EzlIJDipxlE6qJh1dXt9oeplGN3Bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXCzor98NkAuvA1akyi9POfTOmPgCEhTJF48Zq2Nmz1AiAuHrt7YSG8K+z5VC8DFnzojfiesegca/o5ALsDmpPfcw=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.1.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.1.0", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "2.3", "mocha": "1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "_id": "domhand<PERSON>@2.1.0", "dist": {"shasum": "d2646f5e57f6c3bab11cf6cb05d3c0acf7412594", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.1.0.tgz", "integrity": "sha512-4Qr6GTzdsnKVGdYferZT3na5zkswztvfsoyprP/j2bLf1l3pUTamwYvNVldkEYFG6Ll+3eV5mVk0zgRr6iI+SA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDagTtGQaHyQ4k3MhszhQZgVuguuq36hbTt8QPHm6tj2gIgduPsgrcviuqUqrt5IHPlZsyHM+eCVzzNAYuKi/SJGpk="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.2.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.2.0", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "3.2", "mocha": "1", "jshint": "~2.3.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler", "_id": "domhand<PERSON>@2.2.0", "dist": {"shasum": "ac9febfa988034b43f78ba056ebf7bd373416476", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.2.0.tgz", "integrity": "sha512-gs9p0U32nbUW4zYkNnBSBONXuYuOIhb5UMH581DU7PZhg5gXXvJupnSLz//xfGjWNX5R2VGRLH5LpS+BMtQNdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEMg9RHZXWTBBuxPUM4dBHqR3ABhPdQVMKdyrylGm13CAiEA6M0VTUoqc+SR7yCoimCb8e24ji7yQSidIq7CGA1oz98="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}, "2.2.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.2.1", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "3.2", "mocha": "1", "jshint": "~2.3.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "gitHead": "3b4885326dbbc85b71094fc988b93efb9c926b08", "bugs": {"url": "https://github.com/fb55/DomHandler/issues"}, "homepage": "https://github.com/fb55/DomHandler", "_id": "domhand<PERSON>@2.2.1", "_shasum": "59df9dcd227e808b365ae73e1f6684ac3d946fc2", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.32", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "59df9dcd227e808b365ae73e1f6684ac3d946fc2", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.2.1.tgz", "integrity": "sha512-MFFBQFGkyTuNe3vL9WEw9JdlCwIoBYpOGESLeZAvc/jClYNsOl6P1KzevJbWg76GovdEycfR7/2/Ra7NnqtMKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIENtzxU2Go1y1YvvhI7hr+b16QNBfZxkGNxqdVHwT3HNAiEAwRLoHtDY62SknG+XuZow4oF8XSMERYecW4dzvvwO5Zw="}]}}, "2.3.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.3.0", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "3.8", "mocha": "1", "jshint": "~2.3.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "gitHead": "9c224be43a43bc54ebfc2d2e47ab3b9f97836cb2", "bugs": {"url": "https://github.com/fb55/DomHandler/issues"}, "homepage": "https://github.com/fb55/DomHandler", "_id": "dom<PERSON><PERSON>@2.3.0", "_shasum": "2de59a0822d5027fabff6f032c2b25a2a8abe738", "_from": ".", "_npmVersion": "2.1.5", "_nodeVersion": "0.10.32", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "dist": {"shasum": "2de59a0822d5027fabff6f032c2b25a2a8abe738", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.3.0.tgz", "integrity": "sha512-q9bUwjfp7Eif8jWxxxPSykdRZAb6GkguBGSgvvCrhI9wB71W2K/Kvv4E61CF/mcCfnVJDeDWx/Vb/uAqbDj6UQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFOjT4MKEQ6cvycUT6LlUfM1ECeAc5EW+R80Wsgkoh5RAiB0k1PwDdwYY8AbXr2GwQzyxVfScx5xGwh0ZUnvNd+klw=="}]}}, "2.4.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.4.0", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "^3.9.0", "mocha": "^3.0.2", "jshint": "^2.9.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "gitHead": "bed1062f836e545d6a1666b907b2574dbf6369fb", "bugs": {"url": "https://github.com/fb55/DomHandler/issues"}, "homepage": "https://github.com/fb55/DomHandler#readme", "_id": "domhand<PERSON>@2.4.0", "_shasum": "d37bd99a1367a4000b0d9cd9548c9d6aaabfd35c", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.9.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"shasum": "d37bd99a1367a4000b0d9cd9548c9d6aaabfd35c", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.4.0.tgz", "integrity": "sha512-ZdmkmUSw4gmiPElPTaH0ZsA0hdyI7GAjuM5E6P0c1IdbwN+xvdfkTg81slgQNgrJ3sQLIp+4pcfhmo7exEGqzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC63Or+m1+2ocCjq8rwhiXoz8nhT7+mQ+J7Y+YpZNwi/gIgaQ8QPE9R3uZZPvEmivlikeNxDpMjgxLk9jwYWRYSoGI="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/domhandler-2.4.0.tgz_1494125310939_0.8457358016166836"}}, "2.4.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.4.1", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "^3.9.0", "mocha": "^3.0.2", "jshint": "^2.9.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "gitHead": "9b17cc6cc9387a87dde6eb8ddbae11c753e7d23d", "bugs": {"url": "https://github.com/fb55/DomHandler/issues"}, "homepage": "https://github.com/fb55/DomHandler#readme", "_id": "domhandler@2.4.1", "_shasum": "892e47000a99be55bbf3774ffea0561d8879c259", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.9.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"shasum": "892e47000a99be55bbf3774ffea0561d8879c259", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.4.1.tgz", "integrity": "sha512-j/nPtjIvPTcloeLUJ3FXpck1Ey6jZEyXx2Xni9GiHrBl56cYnSqOGMNzmzspo+U7+m4zncrzs3a42IYSvOig0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICVR6UJQ847uXt25G8SayIBoD0iZDI2MBMGnPLfjp8w0AiArYsml4IrWG+l9PVUKrIVduGMfx3VdVnUMfymI8PD5Uw=="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/domhandler-2.4.1.tgz_1494125946418_0.981079070828855"}}, "2.4.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "2.4.2", "description": "handler for htmlparser2 that turns pages into a dom", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha -R list && jshint index.js test/"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "dependencies": {"domelementtype": "1"}, "devDependencies": {"htmlparser2": "^3.9.0", "mocha": "^3.0.2", "jshint": "^2.9.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "jshintConfig": {"quotmark": "double", "trailing": true, "unused": true, "undef": true, "node": true, "proto": true, "globals": {"it": true}}, "gitHead": "045bd8d634dca30192fbd23fee0c530adc9c6f52", "bugs": {"url": "https://github.com/fb55/DomHandler/issues"}, "homepage": "https://github.com/fb55/DomHandler#readme", "_id": "domhand<PERSON>@2.4.2", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JiK04h0Ht5u/80fdLMCEmV4zkNh2BcoMFBmZ/91WtYZ8qVXSKjiw7fXMgFPnHcSZgOo3XdinHvmnDUeMf5R4wA==", "shasum": "8805097e933d65e85546f726d60f5eb88b44f803", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-2.4.2.tgz", "fileCount": 33, "unpackedSize": 29945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa80btCRA9TVsSAnZWagAAhQ4P/A4+DwDYOfgcue03Fsa3\nI+WBrBPQev1+huy0IUPiY64Dt26kQu5tUYckGCg8aEn2aA2hZf9EmnIdwrqD\n0h75PwmD4hkhE8GfRG4ehPff3vv86Gx2lLfk6HtYCGtUrrxWqY/XYgaYgJM+\nZmWzGheOqgXrphgOf8JgbWL6uBN7zT1eOLafvskMuKIGkfs4YWwNiLypy8Ue\nzYhxf+gimANQ84EhUzlQevDMXktqWuakH2aEbuDV2DaelnOFibO15xqoRl2P\ng0npC5v37NB1hxOvch8zBUtJ4NUfA2eR7VQx5bX0s6SCTTTFUBrh/qRdVDcy\nvLPlRO5koNgi4y2rwrQxqPG/duI1qvPSftRVzowmpS51hUwDSbbVxcZCwWp4\nNtj9lGC2qL3GIl+RtM6l4dHG0Rh34DUXEj74XjRODNP1ttew0/lk2bRFqMfO\nQrqAJnHj9WPa24ps91JssrA31bRQex6qvwWwR11rJ2IAZVIyu9lzPV503zM3\nPv7edFmyBQ5nwiI6gDiZGg5J2JM1E44DZPtTR+RWf0VBcmdoh8z22BtpoWTE\np8eKewHJn7h4UfbwA+YEzOi9NfJhk3FKLmTZF65ltKwyOtorPSoPmbHQSl/P\nNhmY/6TLMvn+XJx6Hc9oPSOWH8QPSkKltdVaSViDpAJqmZwxfa5sPC1Tar3l\nc8a8\r\n=kEog\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQLh8w0lHiVrgCd8THP7/DhK/Q8NkDeUvp1Yo+cgsuOwIgdyoF+eqWSyey6JTNXaWqj8WP1WNIHyGCX3MVrDmJsIA="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_2.4.2_1525892844640_0.057355339211383916"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.0.0", "description": "Handler for htmlparser2 that turns pages into a dom", "main": "lib/index.js", "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src/**/*.ts", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/DomHandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.0.1"}, "devDependencies": {"@types/htmlparser2": "^3.10.1", "@types/jest": "^24.0.16", "@types/node": "^12.6.8", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^1.13.0", "coveralls": "^3.0.5", "eslint": "^6.1.0", "eslint-config-prettier": "^6.0.0", "eslint-config-xo-typescript": "^0.15.0", "htmlparser2": "^3.9.0", "jest": "^24.8.0", "prettier": "^1.18.2", "ts-jest": "^24.0.2", "typescript": "^3.5.3"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-2-<PERSON><PERSON>", "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "e1adbd8c4b9d9180e37e60e53ed449a64c5bf591", "bugs": {"url": "https://github.com/fb55/DomHandler/issues"}, "homepage": "https://github.com/fb55/DomHandler#readme", "_id": "dom<PERSON><PERSON>@3.0.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.10.1", "dist": {"integrity": "sha512-eKLdI5v9m67kbXQbJSNn1zjh0SDzvzWVWtX+qEI3eMjZw8daH9k8rlj1FZY9memPwjiskQFbe7vHVVJIAqoEhw==", "shasum": "51cd13efca31da95bbb0c5bee3a48300e333b3e9", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-3.0.0.tgz", "fileCount": 53, "unpackedSize": 200138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQ4/ACRA9TVsSAnZWagAAcK8P+wY70eu8IQVoCLUg0jGp\nJ3MLwEVouXB4aR7PglY5O+khL4D6PQZR2yLXJi3pR/FAUMvIk+qzPksQH0Z6\namUhadchdlYNHuJVJrMkdMalzB+GE7br0ggIuayZKaU/1y5asYI5KOyxx8CD\npOAcBEmXZ1347qUqoBRDOrFHpad1OPcDsPQ3vgMqSBG19UD8hRF6/CR6YAt6\nU7uNHcUbSuxyiCH8ZaQ/RsaDkVIwaULwbXFXgUhDf5oMw0GPLVJoS9mxJjtw\ns/6K0cExkeupRLq3/M2wyImAOHKRvaVvHjyhphhwGDWayg0JrwIEibNrAT1c\nCpQ0sT5xO1OaLxeIVYZy+Vy1S7auFu9HXyCd3+7x46111CPhs4cRn/pihlrc\nWm10dChzfbnuRr8oxDK5vFqt56PInBeST6CDkmYhHKoNVNIoFo3b3MpMINf2\nyzGfV4her1+xZ00hbZ2i7eLZy249KdFPDNYPfB1Lc6Jd8s3U7FHgZzIfYk1j\nmDxXFSk9pSr7P57ce1b+uuVMIBUKnp9Bvot8RWdgJvfvlWQCUVYXILZC76hx\n7LUMzzaWWghyegN2/Ph6QshgOfTqwJRVp1sc0XbVrvFt2AwlHh87QB1eIb+a\nlB9sAWlRslRwLRw6byIOvOiNlPKwuS0rNwz87luOzneZfKvsg6T07862e+q1\nYjlW\r\n=VOSK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSIKlQBaNplr6zpZtrh6l8u9FMBWM5iCe/wTKmOXZRTwIgI7Un+NzbUgG1B3HD4wpMUH2RRbXjlbtDwqskymun3mY="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_3.0.0_1564708799395_0.1478239559175607"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.1.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/fb55/domhandler?sponsor=1", "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.0.1"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.9", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "1edb2abd2d22d1391893c531949ab9ec9de9849c", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "do<PERSON><PERSON><PERSON>@3.1.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-kZFobjdGafS1wknD9t0Ft+NazYYDxXNbcjqQ5z43xTq2FC1NT0U2QjgAmfc0C1urfeGDOAN2xzcKjifSbBpywg==", "shasum": "d931d698a36079ff3612841b7e99eec15c678e42", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-3.1.0.tgz", "fileCount": 9, "unpackedSize": 28199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa7EICRA9TVsSAnZWagAAX8YP/0HG9INuwrvWTE8wHWML\nRdzWQ9LsXEde33zsv4xedT/Q9XsZ8VC2UZH8pKyJsXk57jTzFB+3WYZ4++Xr\nIg4fJ0x0v/YJGRw6HKdxJZ6238MYMet1mv6I7r2qnjpmRzLDIgUUj6N2cFIm\nRZPSa3frV2uHEMCzVzEV1rj8ko33ESX1srCVFBpORcYrH2mWWkbjGygMusGo\nZ6VN9gw3mkTbbft8N2ZA1y+NtdEPAeH9RiVc1UBWoXjPQVUjGffBEUafINu9\nObBOPFXcQwaX5Ge/shAqkmpmq9oJEsRXpobMw/MKmODfIX5wdczBCM+VqFOZ\nCFFrsWZi6hL+RsA+BNgjl/MXevKE/TLktCnfQ+RJ1oDl8G++/rjayvQs7keu\nwTKj+2/vq2YX4h6rMzzH5we+AT6LHIYeHrAHjb0HIZSyWVrjWZ9bLP3nbhdy\n8Crnb3F4JwzrR/IlflPbhn67c8a2tH7Rrbi44RlgemgOUK/e1unygHc8OjzV\nPHKH/lHdvNIuSwG6hM7aiKAOIof3Tflq0939UWuKG9+TpIddQVpVWldZuHsz\nxyd6n1EanItpnTQfTwjmPEMPCNL3nUiPNHUlcHxw7FW7bI+SeLkvGNCWdUf3\nVQjulxMgHpObEqoZnJE0hrgcVZMX0Am8CTSbIkfiinkgsJxgJOOB3N5DCtIO\nN6nd\r\n=XvkC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCh6h90+XOZc5GT3yOfT4wWdCDNm2DV71ZM8qXiFBSXfgIhANkQ9YTiSJeqm6X6M4Xo/unzKYL3wJQRsDTjZJRscyIi"}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_3.1.0_1600893191981_0.3440780720841008"}, "_hasShrinkwrap": false}, "3.2.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.2.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/fb55/domhandler?sponsor=1", "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.0.1"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.9", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "4125c1c3e3acde39d94fddae660d2426a33fe0c4", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "do<PERSON><PERSON><PERSON>@3.2.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-FnT5pxGpykNI10uuwyqae65Ysw7XBQJKDjDjlHgE/rsNtjr1FyGNVNQCVlM5hwcq9wkyWSqB+L5Z+Qa4khwLuA==", "shasum": "41711ab2f48f42b692537bcf279bc7f1167c83cd", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-3.2.0.tgz", "fileCount": 9, "unpackedSize": 29450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbj7ACRA9TVsSAnZWagAAZFgP/iUAa4E3aLKPrb8XsghH\nmaXiS/TK8ctx5XWOovGDIJFbNgiVGu9GZiZQQkBDLqxV2thEBW4ELPbxUg1I\n4o0Z4cdXfpwWbQDRiGod9zAdfxNdLPW2dVoSEb+xE8bEPP6zTcFlYhkhDJzh\nL3gtISm2hP0EOBnm7pA5RfyoUUv419hiWUblgcitCPWfj90jfDdzRCVOM7Fr\nJveMPqBrdETm9aDcU+PZlp+LD8mXufQOH1QJFzkdNSEfKsBHJVvSy8vOMmmQ\n6UEVkIJxZjdALt7JujC60JhXibZjiE01SlAwYX5Fx7/C3xaLKkgLLpF438Mz\nxz2PfncflLknqDFuLeasvr6OvszXFv9PMm8jGaMp80M39Lcv7rmPZavOy3HK\nz06tCkeOEXEOitQBdq4TCwrr43EWVDA2vIabp6Cchh90B+qDwKtcFzcXbAbG\nMY9f+jJzmapfchqpBzpl84qM1cCaPhHz6ucOj+97HCtxwgQAYloTxQq2gUJp\nesHemXdOev1R0BeHxHk44PvnQ7MVTNRWLFiSmyzo51c7e4DViZZqTrnPWJpN\nbLb5MR1nITarmvD+OGBpmhddMEmNs7/QVSnJN2NlhfHudOBAi9qdPyZYli31\nu8tLOVFKRHUYvNtmCRv7Ttyp4sIWWhrtu5EHpstXbKUwVKoJDJQF5ahFC49b\nBLRB\r\n=T/c5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXCWtS8ZUXtni0iWI8X5e7tSteCzaUIZuRu1yZIoEevwIgHZw0NTFDD1OW7mrOElScSztV54YEjXgWWEjpxSF+xLQ="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_3.2.0_1601060544541_0.3623852478039329"}, "_hasShrinkwrap": false}, "3.3.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.3.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/fb55/domhandler?sponsor=1", "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.0.1"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.9", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^4.1.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "a079eb64c711bd39abb875188edcf666e50c8261", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "do<PERSON><PERSON><PERSON>@3.3.0", "_nodeVersion": "14.11.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-J1C5rIANUbuYK+FuFL98650rihynUOEzRLxW+90bKZRWB6A1X1Tf82GxR1qAWLyfNPRvjqfip3Q5tdYlmAa9lA==", "shasum": "6db7ea46e4617eb15cf875df68b2b8524ce0037a", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-3.3.0.tgz", "fileCount": 9, "unpackedSize": 32610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeO3jCRA9TVsSAnZWagAAKQUP/3DnhM6jdE2qW/aLEE19\nEt8YdDGNZUdDTj8FCIR4gvbHVcyMyZFs1tzucXGuQEXlaKyqsTyp3/b8v/HC\n/rNk7OoRMlxP96NaYuvBD/unOMUvDw9bc7Rz68t0i6lis/zG1cvRaiJSQNKN\n85WEbBwGtm3SkD8YgWca3bGDcBo8LijSG60O+j9ymA4fVAZC7Mk1LIzsazH8\nXLVj3EuiUtFvITG5KCniNipLcHs1sMntmCkPiUGYWmLIQQ4x0W6/bnaVFQrS\nOo7jO/XLkpDV82tU0wrQmljGA4jStjbf4ZKS0+fpYq3tZ8Q6is5hZjcdCYhu\nLtPi/djciR2mlF4nEawxji9bzoeGZMTZ1fsx0oBnDx7Mm8ETDnWHB95DWkQN\nK3HJzWcOdWuoVjAxlkgVDhMZJEkdrdtzirvStFW9h+o+a0WgtUyBFj4yTzHz\nr8Z9xAYAx0TsvwMcktI1+n+oqvTG0dHqYmsALHSFzv2GTztHQSwej28y170S\nkv5y3LJgKMHveGuLQ8u0vJ5JS8lUpMXxZnAWoIhxSGwQ5xE7DbRA+Zftwkae\nvMi3oTcKz4EGA6vwSeEUEN5mp0UVvXS2Z1hRm8DtXIWBpNUA/EjcOAhdosKQ\n/KrYD3jz4NwiUZRpBQYPz1DdRW8FYQLtRC47BlO2uvhCl45sPKS+rNpLO9Em\n6Vx/\r\n=abWJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDs4fUcSCp52/eKu6zs15HCpPGLDmQcfCNvswIHUOUJVwIgYKsNReF9FtWyXxxe/3Nnjbw1mzSWxJhXBAycr8beDlk="}]}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_3.3.0_1601760738953_0.7410084223970252"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.0.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.1.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.9", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.9.0", "eslint-config-prettier": "^6.0.0", "htmlparser2": "^5.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "e94b44bf2e77130c49fc7839a3fe2485455bcdc3", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhandler@4.0.0", "_nodeVersion": "15.3.0", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-KPTbnGQ1JeEMQyO1iYXoagsI6so/C96HZiFyByU3T6iAzpXn8EGEvct6unm1ZGoed8ByO2oirxgwxBmqKF9haA==", "shasum": "01ea7821de996d85f69029e81fa873c21833098e", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.0.0.tgz", "fileCount": 9, "unpackedSize": 35477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxvPGCRA9TVsSAnZWagAAWjUP/1z7RYMaQwf16yDrYeRw\nNcgKFinlSQVmPlj+e4zP9RlCdf2NyfTlLzm9XV9YF22YGCa1Ft1B2KWP/Wke\nZSK7nUChh8kJ6h7/4OxFy/jK465FlkzDt1vpM1nHyUvVNVsVv9Ia2GyYyna+\nM/Qd5CjFfiSmU3G2F9aNpqjn2Xhkjrfq4A/eMh0uD0gVQNvShtHc+UgKji+K\nyU7saEeTOv8jHdd+sSbA4wY3zONpTWAoenM96g+iKpXM0IhmwcnbwrrEUJBz\nP2JXVZzlPYvsVsx80vGdNdlbSHppU+YvOjicg6gQUG/Qm41tH2vnG+oHhc46\n+t6SWXPFpY9dYnXcY1KzP1fKjZIpFUqea2VBlnUK9aVNJFSgQ3QRCJS5IRoN\n2CfXLMSNY+x7rSQ41keSyLvSDzGmimpZ6md+QprxGtMAEfnO30BrnT5yxUxG\noKbzWmMyKCXhEuGPwRnE6YKE4XFy8lTmhTo+KnHMVSpiQ3Uxt2WiVcoP52IL\nfdnmN3LRZyiPvdvFbf3MDU4xLQBe+gQ+0WC4LHJB5CTHstMnrX1GhzGYt7Hg\n4OVaoVBeT9QHIxf/u1I0eH5rivjEof9jbUQCGEmYJ4ovo8sLrpRZz841MuiK\nT084LLVa2GoWHxVsKBHsr47+/RqjLpKHxwr7H3fikQXcrLKc/xmQfPoS9wa/\nf07C\r\n=kU8d\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDawDvqC1fppBcHOS6SeTGUqDXi3iFu44hAK6AStYzv5AiBMYVRIDiRNsJ1rOgIRq2eBBpr5QS6NM8Y8cojGotl8AA=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.0.0_1606874054145_0.22134086349454174"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.1.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.9", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "b4a8960abd54408bd335e6004f9d2d28a811f845", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhandler@4.1.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-/6/kmsGlMY4Tup/nGVutdrK9yQi4YjWVcVeoQmixpzjOUK1U7pQkvAPHBJeUxOgxF0J8f8lwCJSlCfD0V4CMGQ==", "shasum": "c1d8d494d5ec6db22de99e46a149c2a4d23ddd43", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.1.0.tgz", "fileCount": 9, "unpackedSize": 36307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZgdFCRA9TVsSAnZWagAASSUP/Rhsv6J1H47M1bg7Dv3q\nucNlUZnTdbKydt9FHiZ3/F2q7GRLQ37NgR/ceBS6Cpbr6XJuF8EjIcuCYE1t\nC2Z063af+g+ZZMBnv2SlSIezsTpSjYbeSTdq5fE11TYoFxXFcBkJyz1vap3d\nK8DwpZeuRHZORcJXV8K0h+AFdilwHOX/bxxg6K8VeZOSiE8gtNuiZdt8Fcoi\nB1VzkrIBlkm3rytJkHdtghfA/YFJy39bt+6XHn1XG0Xn6Gi2IZDokDc7L2VJ\nR4pp6yDwMCodOe5i0fCR6+mqHcCUwhVsbk8bvcN2nlW/KJ1vZ45necHBQEgx\nymNxfQmi0y45SuucFoam2HzrexSOgNx0pqcXmHQ7e/iYIwd1w2lxaOhbdBhJ\nQZpd//nPpqlrjedcVQH7d44U/ZhWWOjxvZV1mZhuFUscIhDc8HOm5N1VtTbZ\nQw3xtpQ8B5R75VRsHe1Tcr5a6lDSJqmIjRXB5Lc/gPP8GFEplbLWZ/BAUWc5\nnqAiufoVhQGQNYRmHs1JRz3vXKAzbcmFK/hD1OOZc2nT6horXwpWnYmXlMgk\niuIjMzmlxbUACdCAwSguIxY/lxGjks8FIqksotrEi9nn1uOE01XEZgwH47jA\nCiYhd8HApOlNvZ1o1NYy6H6fQmeWO1BMFau2625bIdVijS6Mj5qtqIX2LS57\nyteo\r\n=tRMW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAubNDQ0xKjFYMq7sl3iBACa2ud9F1th/P19IpcMylAJAiAYOrO7fr0N+U1GHiLW6rJDAsA5XuxiUefFUVp8v/jmpw=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.1.0_1617299269073_0.5976963236303667"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.2.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "jest --coverage && npm run lint", "coverage": "cat coverage/lcov.info | coveralls", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^26.0.0", "@types/node": "^14.0.9", "@typescript-eslint/eslint-plugin": "^4.1.0", "@typescript-eslint/parser": "^4.1.0", "coveralls": "^3.0.5", "eslint": "^7.9.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^6.0.0", "jest": "^26.0.1", "prettier": "^2.0.5", "ts-jest": "^26.1.0", "typescript": "^4.0.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "8dc6824760791c3096b99ed6183304e8e689b5d3", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhand<PERSON>@4.2.0", "_nodeVersion": "15.11.0", "_npmVersion": "7.9.0", "dist": {"integrity": "sha512-zk7sgt970kzPks2Bf+dwT/PLzghLnsivb9CcxkvR8Mzr66Olr0Ofd8neSbglHJHaHa2MadfoSdNlKYAaafmWfA==", "shasum": "f9768a5f034be60a89a27c2e4d0f74eba0d8b059", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.2.0.tgz", "fileCount": 9, "unpackedSize": 39360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeVNHCRA9TVsSAnZWagAAaXYP/iKt5OSH8C5DtxN7zFZw\nClAtqo5Rgx4gYnUrK6GhoWDdMSs8YMwM5cg4zc3dhi5AnyCMnHduN58s1uh2\neQNG1nBEvWnl2/D1hrrHYVv+NPq8t0Oth4saRaaJX5ZgGI4CvrJDu/aSmuXx\nyTgpdTgYXXwqi6+w4teyWBjIkNLCd+UA8wW1VxZLkGlFsLisxs12tYke9EOj\n4MfR+KultM/2PAY1CTabU3djbAE5dcskURWOu8roRVvt3F8BuKoqxhZRwDgY\nJXXkEUPPpJMMY+DarosZDZCHo+UVKEbO8O1gIuUzAKE7tQRKyzp3YJg98dAt\nfeCIyufgm0bPQq/QB+VwlNmcUsuPqZsAJWSZ942gDTIfYoy7JkSq70bQORZX\nAwFs39AaveU+uIOZeN+xwRmc3kaC5p8qgRSmlK84wRYhXi3HXk9FzT+pc5Ma\nnkfKds0ELGOk3ZWfBAEwCsmdzPbD9nQDaBcnSp0sUqE5grfXqKbInv4YFirF\nTTAVTf/0zcO9Sf7JxzrdaVK1YbZu5Y5U3QJoxGTdlktKk/pebYgWZafIkjZu\n2wyz09uEYU6GE0fxjNwmOzUKZRmpGvIItXXot9xCUeowqJ9G/g3kdb7Qy/zq\n6AyCkey1B0VlCoqg4srbL5ycYeFN2u2+bN7oiTq+G5uJ/dpeEVo1q2E+YW7I\nZUmE\r\n=XRtV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSwzoOEqxh0kwiv3kwg+Hd1KB+ITfCKhpYjKD4VmF6XQIhAISSZgJNq2RhFJ3SIp/5sc4EevTfeClxminm4EWUjI32"}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.2.0_1618563910983_0.8815395008389071"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.2.1", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^27.0.1", "@types/node": "^16.7.2", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^7.1.1", "jest": "^27.1.0", "prettier": "^2.0.5", "ts-jest": "^27.0.5", "typescript": "^4.4.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "bd435d932858638124affffe18cf2987361a30f3", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhandler@4.2.1", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-Z3lL82wPfQYi7PcOePzqAlXPkFI9ICw8cAH41B/MOqwO20W4aQaCRN2PLeIC1UmYFPlszNQtphORFPfyit+H5g==", "shasum": "73ad893a0048535996ea9fddd1071ccc86378563", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.2.1.tgz", "fileCount": 9, "unpackedSize": 38029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhK5TkCRA9TVsSAnZWagAAtMwP/RztzC36W7L4bCDjfVfJ\niY0kxCvJsAuBT4F71Fg8KIxq+dHbH+Sy8lFlW9Q4frtNeXxww8h+YmmUajbU\njJhThsTEc/UPQBFvHcYUm/+xVLA6FUDU9nW4WViM/TVqH2NRx1xHrQBtkxnu\ndtT9mfs5jvC8DXEnZSo4A4mXsdHWcGL5cl8fp6iizZPam9wamr8wmjTo1cTI\nY/7+zS5QWb8prbqB/xxFfYtYsoAzuGV8TGN5Km9iGRssxSCNWFbyJNgV3Ko4\nIprK2fYeg2OAPPjvA7dyQxZTe5vNjsdyitecfXxSpJLaVz19WVb/Ag/O7kyd\ngikuZ/pP8VgdKiyV3IbGuB8j9YB13Zb0Q2MWKlTJpB0te8XtQ579OrweaCO+\nNcMHTI4r2ji9E0LqPQNAQtTPoslAicnSh5zQmu6kKfAie53fNNxZ2dbcNpLq\nYaDFoHoHxQiK11gzsp/Sil1dh8Z1P5ta1ZsWGTuI3sKd1SDzLz+oo1SaHbXD\n3L3rjPfzI+7zGBeJnbMG1oca4J2dbIujeI8+Cos9JQ0wl/vCeO9DN2gxoXli\nPj2J2o8IpGoK19M9jexdsTIBYLuXy5aaC9Fy3hTxfBDm3PblQ/tFyhQJ2cCL\nUMLXvGow7S1GPTTsr4bOrjRR1XOd/bGRZ94yiIAOZUd6av+IJlzNR+XsPEFl\nbB8b\r\n=R+4r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE5CpoBsbGXNnAThcB+nx2g+Zhk+jy/sa1+iX9DSnTuPAiAOmRJeUtm7S858KYHDtUsE5b2kSFO3jFOiVz/hp6MV3g=="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.2.1_1630246116054_0.224784369591863"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.2.2", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^27.0.1", "@types/node": "^16.7.2", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^7.1.1", "jest": "^27.1.0", "prettier": "^2.0.5", "ts-jest": "^27.0.5", "typescript": "^4.4.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "210faf303ad20fec2b04963774424b7806b9f2ca", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhand<PERSON>@4.2.2", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.1", "dist": {"integrity": "sha512-PzE9aBMsdZO8TK4BnuJwH0QT41wgMbRzuZrHUcpYncEjmQazq8QEaBWgLG7ZyC/DAZKEgglpIA6j4Qn/HmxS3w==", "shasum": "e825d721d19a86b8c201a35264e226c678ee755f", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.2.2.tgz", "fileCount": 9, "unpackedSize": 40025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhK5dQCRA9TVsSAnZWagAA3BgP/1+Gl8YjrsICUE4AwQ+y\nApruybnfQOaMW3k7Qou9tuOVCt8+hGNNLv3lKSZoMG0IxWVEPIc7DFAkEesh\nHpU50YeDBjZs7gU1Ospe7YC6Rk3bDYiKQwmqu+TjQlxeKmlHDIHa/+eFftF0\niU3EN3Qib3KyCpIT0cACHJuCZxmjVM7hB+qu0/m+80TpbUXuLWizQZkPAuKD\n327COYj8paMgJmavgDd8PEopolvEGeazKf2ckgYNNQyEDMAOF7vJLjnc54Hk\nQDRkQDSsvsfJc0bqrR6AGcY1d63+iYlzEifaIK27OdGvqjKeLrxBOQpyZk+A\nxY+I2Sa7UqApA6/2GgMlOmdmvLfUm+tCoBYDAQ1gXdRjM4ayyfpISIuKXuF1\nLZzdRLDY1eCZbuV0TBJd8gOhnXxLsuWkLt3CWOpqdiFuGTMeDUypYZBmJDMX\nefe2y8KM57Oo7kfZKvhJKnA6c8e4vsxnHP/BV9M5bDRmLWmUVlkOTIMbi5aj\nRN2IqtM4Gq77aLF0DvfHwnEUi/WGJXAzutLX90upUK9yJu/QOuzxEUVvAJya\nGSqpTc8/78E8i55X8VhUvYG6JJSbHsRFhhQZSJJRXTOcZxd5rrEbpx06s4Er\n6Ob52mxSq6obA4ZAspZCKzKFPe02YhGecY7chg975fXscfvl96gEys1hM6Lo\nG/24\r\n=ppjx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIOyec6HU69cq7SQ14zmm+OMfjRnpNfCOkH9AR1atCXQIgB0lfLljstEXD+qg3gJYsAg3IY23ODuVv/I2tW354ruo="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.2.2_1630246736525_0.0062498074585981556"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^27.0.3", "@types/node": "^16.11.11", "@typescript-eslint/eslint-plugin": "^5.5.0", "@typescript-eslint/parser": "^5.5.0", "eslint": "^8.3.0", "eslint-config-prettier": "^8.1.0", "htmlparser2": "^7.2.0", "jest": "^27.4.2", "prettier": "^2.5.0", "ts-jest": "^27.0.7", "typescript": "^4.5.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "d28917ecf11ffefdcdf2eb4a474f7cd08b3f1e76", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "dom<PERSON><PERSON>@4.3.0", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.4", "dist": {"integrity": "sha512-fC0aXNQXqKSFTr2wDNZDhsEYjCiYsDWl3D01kwt25hm1YIPyDGHvvi3rw+PLqHAl/m71MaiF7d5zvBr0p5UB2g==", "shasum": "16c658c626cf966967e306f966b431f77d4a5626", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.0.tgz", "fileCount": 9, "unpackedSize": 43373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpkDgCRA9TVsSAnZWagAAZjcQAKDvJWF3pzWD/RoygYhc\n7lkMtI/PYkcLZcimQYuyUKAX1d6D145bGDqDomncXB94+NSZq56kt302WNxh\ngz0BW2nxFDJQW6bZ/5XoHec9yidTFgXiY0I97KloXY5p5cRMcufbCf1KCZ50\nquv4OZQalFqaWtg1tlumM7/gIEvnpohEHz5OklOtXIoq3lxaiYo94XWJSBdh\nWf6Y2x6SgBP4ezoUHB98AuWsU1c7rZbJT0lxATe2GJ3CwARyRuqvZ6RcOqNr\nA0Z7Br/5k4h4euoXpMoxSwjGzY7VnB2VVbznFvLFh4Sw/hQ7YYIC9S0l1cJ4\nuBdTc4rkMUsSxVGL/o7gHZOuSacgXkoQN0KiEA0p6fv14+vtbQrBYBlmXdrx\nju9teoQBrvUAYVuVI9SuEfq5Olnx7ZwWH5WKeCZKUNRvF6+Pyy7ut5pEqUiH\nIxkN2BfU4BjYmNVzsrYgF0moZrc69w1XHEsXBpp+XJ41mt8ui0nebxvszRb8\nR86Z7jffcsZMZOW/rUGRY5+zlGiu2Cw/ZwCkhdx6jD/U504Dc+7uyUTSwwWk\nQx6ByxqGGJuQ2fPQII9lGn4fhQUOQbAxVS/Puhh7pP6MuweLPwsj+Z5fJ06F\nRfNLFZw3Cnm9owPB2C/uyhRi3O+rgeeCGZAJRa+QsEuD+BvQ01CbkC/6rCGH\n1r2W\r\n=bTdk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUriuP0Vf2MGC6ZTTv/mmZ/553aLNk0Pgx0CyCconTKwIgI87HK40gJm5AwpZDkOYD5tuAFtVZzv4v/BxvIdez3U0="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.3.0_1638285536154_0.42613270862113684"}, "_hasShrinkwrap": false}, "4.3.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "4.3.1", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.2.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.21", "@typescript-eslint/eslint-plugin": "^5.15.0", "@typescript-eslint/parser": "^5.15.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.0", "ts-jest": "^27.1.3", "typescript": "^4.6.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "prettier": {"tabWidth": 4}, "gitHead": "f5eef4a7c2dc2c474f1a2f9b02d26bb590c35331", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhand<PERSON>@4.3.1", "_nodeVersion": "17.7.1", "_npmVersion": "8.5.2", "dist": {"integrity": "sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==", "shasum": "8d792033416f59d68bc03a5aa7b018c1ca89279c", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "fileCount": 9, "unpackedSize": 44583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNcsIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgKg//fr53az6eCg5ogxjHlYbTc3R/vWMsYi6i6ZfZ37Dtabes0sqL\r\nCuUHNkH3vGSUjm5h/C9eBe1fejTAreYUyUnhw9dSE9jiGExQ4hYhuJSn7aMT\r\n1jbGISRkzCVGVSPTXWUFR2Dd/LGIgfedqYzN4Kfu8yw94himER1NG5WlAjKP\r\npO5sXBWhACQXQFvXvWDcRjvoPS07h+9j7CH6WJinfLW+eEcEANH9oLk159dd\r\nLbhKBdlEFoESZP9gTytJKNeRRFWxkiDtMYoTfSVR4OKoVD/ce205sm5WLL9q\r\n7d6QTrhpit9dP/MG0erjm3FTQCCkCM2V+/sKu1dtEtkb0SQ6ujcKLxP7emz/\r\nYeDP+Nz4QJ7JOIL3aC0EhQO5dhNssFQaTX/kvs4OzlhaKjugh6E4UYI2IlB3\r\nuW9AClUYoL1JnBVtty3Mye63xn1TWsQ2bC9zHGYl1BwtM13jyFb/eXU4qlA2\r\ny4n8sPiB1BPogUzodyGyFRFq6fuuMp+vyukHoG9IEtM6nSQCkWQ1jcSAMIv4\r\nkC4v0kRiuHDjh4gsedDxEpDu5wF4wkIy+IMxNZWh2ovVfyp8dBR7fmIsfh27\r\nAxvH4WJ7p+nfKnvtHa/ZsB/1eDpr5yn+Q1xKlPZTLyYWEqV4jbRzpnHhwKlc\r\nFE93IDVWAEGjTiZr0jnG+sWqPdS0oi96BhU=\r\n=SVIT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDeTbAkh4vFvUJpdgSDvnSUghpmMpS2tDqHN2OHm9bF2gIgJIObJ9j9S3dJwlf8YTyYnSku1+Cn8BoSFFa3MpRl/SA="}]}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_4.3.1_1647692552807_0.045921160066817235"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "5.0.0", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.3.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.18.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "76e1e2a58a8740cd02fd34201bb345935e29a70d", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhandler@5.0.0", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-uqGSXUEiaG7yfHc7qTixMLPvrJJPs+0N3qMYhqHVnwTFWrdI1wCil5N6V9Qp/fmjN/9NSn3UhkKMW7Ixl6RIig==", "shasum": "24e3e44883e18bbb0a1a99ab48c03ae582aac834", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-5.0.0.tgz", "fileCount": 16, "unpackedSize": 74855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE2Kdr88CYIQIpOAB7GdVsT3b5UVKXuQiAmAEjGQ5lRUAiAPqdPLFCUFrjXZ4dgI/E1woYdIGETCVKGFW22DjjGSyQ=="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUGHuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmox9Q//ZkqiJ9krtBydPcnHUJKChtEkq2QePknJ/JrzKd4WNwQsrBCc\r\nFCdkvc5mCagmNUnHr/xapUJA93Dz5v+X2ayGp2TMm3bdC5qW6+z1xTLzJoy6\r\nIhmZt5vJ1SIuNG6QklgIo+ezbT/I41n+MgOoqu2aBbO4LNF9htFUNnsYDquI\r\nEuIs13CY+NYHFJ47uAKmITMfguSOc3Kc8sJBW99BbiiAoPvlbOidQZOpnEkX\r\nThxDRGDFwjfvuTWa/Nc67PH0TIF/YKFwgGe/IdhHEB1nwJ0LKfSmKwyPURg+\r\n2bK1vGQyhiD4b8R1o2i0yCJgR915mI/2cGP4EHUjctNPyjg+sJ45rT6WaTsn\r\nx8jblsZsxolRBverolUiclSKGMq5/sYW5lD1NamvcOpPFb0yD5IzxEranPeR\r\nnSYPvVoTWrtfVIjTcqVPWPjfgDS8ekb6OgyCRbsTO6HJ6i8iYSgky1Ru5K/5\r\nuX+HS5nnLCrLHizqe6Svs7DUD0rC+w6xI8RRiU5tXxeaYrk7wqGVJeIzN8Xq\r\nDdrzrggvheBw/ZmTBjHOc+TQ6z9VRQwEHNqO5nRnxtCJ79G5GP9IXcVRdfas\r\nHCakQ7ep14PWX83Qm+DDCTX/7xpeFJrAbMnueJq/TWiQSGQOB6JGb+hT9uMd\r\nxEy/pYctFwRBQ9n+kZWYlJ7nq/gaRRN3GkE=\r\n=FDiE\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_5.0.0_1649435117910_0.4548316414937119"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "5.0.1", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.3.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.18.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "56935147227e1131161c373df70a64ea26f87208", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhandler@5.0.1", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-3Wtz/hWaKI1FhyOKtDzzaBdZirwVg27uEeeEMPHE2x/Anyazfmi9HsVDuKVX26fL9T9ThPm5l6rvkY5OczVfuw==", "shasum": "f3095ce839d60082dd9bc1600a60765f6f32cd1a", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-5.0.1.tgz", "fileCount": 16, "unpackedSize": 74853, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjUkuBHLWd/vwTugpchjrAiCQSTPr7aaOlwLmGLgSgsQIgKl+xSLCfssxs1NfGEN3oS0e6v7WApwv2UWJyKkRYBjU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUGSjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnEA//cI3Yh0oT53G694CWEB9AfTsrFpTuAmz45CkOElUMMXbMi/OP\r\nUhFdwnx8lY33JUJ2PF3wGDkqw32OleGMdX+oeTCqmgeng0EboKh3bW/DDAcP\r\n06Tv5dqeCVNMDjRo35O7c5I6R+xi2CUvZr3P86lOvcy8biydMD0Xv02A3fKH\r\nOhJgrXqCYKcy81qvuiO0sEGRvxtEG1zRFSYP+9WQsGMMPc2k0hrzv7MW/yCd\r\nA9Wt68Llb/3lqt0utCK+rsCS4tkW5uTYR0QBK4f4H+P/1xwY5jxkgMnNoctW\r\nsL4VZn+Y1tgO6DrO1GDXSJJpxyp7k7zKBWRKH5QInzmrrRVGjDT07NQiKa3p\r\n2vDDhBeEUN9V0B5grNt1b71yaR/Qf81UJT32QvDyzsp5qJokkmCMd4uam49V\r\nF2EQshNuMhcmHZLJR+A0DM2B7ShKjA2++HJXPrRJoTPoTX6VpobL63MJMsY7\r\ncX70BriCAi5209SMh0vOtuGW9gJdVxv2DWwxYMGj5IfKxt3U2pyNv9NdNdls\r\nubEByiGp8NFhOD1+Hx4RtoSAFPs19NfgUuReI3C/dS9CrkmISpb/EaAQMmYb\r\n0A7riwyWFCc9p1eMzWOcq/r7OCAEHBpt+NiLwEZfJQupYSTibyzM++5uZV+c\r\nwcHsv7C5O6RrdxxuHkrM1hoFSxw7RCdIFh4=\r\n=iS/0\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_5.0.1_1649435811560_0.12424746703047584"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "5.0.2", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "eslint src", "format": "prettier --write '**/*.{ts,md,json}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.3.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.18.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "4725cfe350e9ea5f603975659565f83a61e25122", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhandler@5.0.2", "_nodeVersion": "17.8.0", "_npmVersion": "8.5.5", "dist": {"integrity": "sha512-pr8ToPIuwBonzUy42STpc5Cf0m69zsQ7gtCLLvKrTbhVRnRohT2pLiJmGp3PAh16nDVWpYpcRpdjuk1vFmnQUg==", "shasum": "fdac2564e054ae05d4ef73605a43fcba17de2d1f", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-5.0.2.tgz", "fileCount": 16, "unpackedSize": 74957, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEgxm8T9tsLkpKq97imsltiEFu4DFxhJcORdN44aRvLgIhAMlgFkeD687qoYpAuCCAr8fpQaeGp+5Oq98CBRLWDE/W"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUVP8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRDRAAo9K7c9bzb7iLzGJboUjiKh7nmHhitY8MNIRJVFDKLD8owuml\r\nxO3/lJU0TJHLeGkXAEMuRGLNUcr44JacfMAkhqj4Z6rZFF1TyPM/GOh/ACk1\r\nSwBFKt5o/Q1J2lqgiyLNpWQbbReu6Hrl7bKIhmJie6HC6xHwwKGt+k8HE2hZ\r\nwC8wTfuqWao0+kDNzUT/UywYlsTsXko8J/Qz+XYGaWr4u/N6YwonsRRVvlly\r\nwHBG8HdclbQh2LzzgTsCPBGHoxlCtmx8rLmPuqY6wVjdDgm9zhqXevzRvQCM\r\njI2X7KRYsu5HXKYmjXc8SIkY5BssjawWnIU8qUxJGMbm9nMfuxAxYyR96+BN\r\nG3luxbhTG4hB/mEaiqHZozJL3T/nwJEHi8LWpTYUlC1uvIJs3YE+8g37Ef3b\r\njVCDDwGiOBXk7W1GN9Y5XSTnaoI1oHCe5W37I8wPrnVmk3mg14AacTBEWlOT\r\n/5yeEpC3iYnIi33s1/HycqV9winN29IvFXz9fi2v4PkrrNG/Yq0xIQMe+Na8\r\n+82fUKIKXtlztnjfK6CxAnXIbkYgy12H12LtMe88p2mbdqmg8Vd9kyrbRBYH\r\nLR2E7CJCg9PXrnt+KDLbf5BkgXFAeMkH8mDQXt6z+HoDdz/8sXuubBmNnfZ+\r\nNH8BgChZMDEUFY9qqys6lXoSR9OQV9jdSfc=\r\n=R1NJ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_5.0.2_1649497084403_0.47153984192776033"}, "_hasShrinkwrap": false}, "5.0.3": {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "5.0.3", "description": "Handler for htmlparser2 that turns pages into a dom", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "sideEffects": false, "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "keywords": ["dom", "htmlparser2"], "engines": {"node": ">= 4"}, "dependencies": {"domelementtype": "^2.3.0"}, "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.30", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "prettier": {"tabWidth": 4}, "gitHead": "767c031b7db3ed79cde865dc368b2de930ae1934", "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "homepage": "https://github.com/fb55/domhandler#readme", "_id": "domhand<PERSON>@5.0.3", "_nodeVersion": "18.0.0", "_npmVersion": "8.6.0", "dist": {"integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "shasum": "cc385f7f751f1d1fc650c21374804254538c7d31", "tarball": "https://registry.npmjs.org/domhandler/-/domhandler-5.0.3.tgz", "fileCount": 16, "unpackedSize": 75339, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7KylcE5EuEVUc4XxW4/oucmbpKilop06rD+Xj+R8FcAiAQjG/5af4jQ9WDdcsRWYipl1A599kulOmsBAR847XAZA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibRa/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYeBAAoGE4oMFraoz+ZTdrqKzOp8La1Kra+HlkFOEJ9o7C48BZqAlC\r\nEuUNP+OJAiS2uanMz8AXLoM6Om5dUBG8EiDGr9NbOfs4jmolNHSPJzMVIULJ\r\nNShvZjhu2lKKcVi/GawcTWKnavDNmXqZdbRPcuFcGiZrhl8YYZdh0SccuUSj\r\noFCnXgPvrFRcEtbyYMZu2lXQ4VRhuQbXADyIidxG2eofLVTctERNfhO+NcaZ\r\n3x3wWEFQ65JuycTyBCm+FClfVw5Ev7BXS/279X+9DtCSi6mlRSEpl1yEGRzT\r\nDBnXYvTyxwjOjAa5CgkaB/pqKtGX0KqaZ3pqwPAxCR5bZnd8/borgfW7nOS9\r\nmAr82+4j2KjEM35hhkXB2RKdyE4jYWKEdJm8wpNNwzIPMBP1twAkz5DXqkBk\r\n5wro+o9jx/nlmiYkOKQpPEag0CkIr3U9t/dqOz7CtqcLrwkKP8w6pT1IyyG1\r\nMZbeeqW7qaP+Jo6wTis4AIeCcifEgJoGnbAKJGHgalLCQEEVdLpl6BOZN4ou\r\n6B89HUG1nBQXilo93K3HaDRULL1y+7y2sMVq4Q1krVz4z/zjSUz2js12NBSh\r\nzPXVKVx59vLrWFK00BW9PcA18MkHfPMWwr70DiED6it/mw8OomA7Z4fX8J+2\r\nGt8w1BgIk3Y6FWcoyIgcRjF9P/4Pw3vXkbI=\r\n=Juum\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/domhandler_5.0.3_1651316414782_0.9441399041939285"}, "_hasShrinkwrap": false}}, "readme": "# domhandler [![Build Status](https://travis-ci.com/fb55/domhandler.svg?branch=master)](https://travis-ci.com/fb55/domhandler)\n\nThe DOM handler creates a tree containing all nodes of a page.\nThe tree can be manipulated using the [domutils](https://github.com/fb55/domutils)\nor [cheerio](https://github.com/cheeriojs/cheerio) libraries and\nrendered using [dom-serializer](https://github.com/cheeriojs/dom-serializer) .\n\n## Usage\n\n```javascript\nconst handler = new DomHandler([ <func> callback(err, dom), ] [ <obj> options ]);\n// const parser = new Parser(handler[, options]);\n```\n\nAvailable options are described below.\n\n## Example\n\n```javascript\nconst { Parser } = require(\"htmlparser2\");\nconst { DomHandler } = require(\"domhandler\");\nconst rawHtml =\n    \"Xyz <script language= javascript>var foo = '<<bar>>';</script><!--<!-- Waah! -- -->\";\nconst handler = new DomHandler((error, dom) => {\n    if (error) {\n        // Handle error\n    } else {\n        // Parsing completed, do something\n        console.log(dom);\n    }\n});\nconst parser = new Parser(handler);\nparser.write(rawHtml);\nparser.end();\n```\n\nOutput:\n\n```javascript\n[\n    {\n        data: \"Xyz \",\n        type: \"text\",\n    },\n    {\n        type: \"script\",\n        name: \"script\",\n        attribs: {\n            language: \"javascript\",\n        },\n        children: [\n            {\n                data: \"var foo = '<bar>';<\",\n                type: \"text\",\n            },\n        ],\n    },\n    {\n        data: \"<!-- Waah! -- \",\n        type: \"comment\",\n    },\n];\n```\n\n## Option: `withStartIndices`\n\nAdd a `startIndex` property to nodes.\nWhen the parser is used in a non-streaming fashion, `startIndex` is an integer\nindicating the position of the start of the node in the document.\nThe default value is `false`.\n\n## Option: `withEndIndices`\n\nAdd an `endIndex` property to nodes.\nWhen the parser is used in a non-streaming fashion, `endIndex` is an integer\nindicating the position of the end of the node in the document.\nThe default value is `false`.\n\n---\n\nLicense: BSD-2-Clause\n\n## Security contact information\n\nTo report a security vulnerability, please use the [Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## `domhandler` for enterprise\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of `domhandler` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-domhandler?utm_source=npm-domhandler&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "time": {"modified": "2022-06-15T21:38:32.935Z", "created": "2012-08-14T14:57:01.877Z", "1.0.0": "2012-08-14T14:57:14.272Z", "1.0.1": "2012-08-15T09:27:06.055Z", "1.1.0": "2012-08-23T18:39:01.597Z", "1.2.0": "2012-09-09T22:27:47.293Z", "1.3.0": "2012-09-22T12:06:18.201Z", "1.3.1": "2012-09-22T12:17:04.700Z", "1.3.2": "2012-09-23T11:24:54.321Z", "2.0.0": "2012-11-10T13:53:25.346Z", "2.0.1": "2012-11-10T14:03:04.229Z", "2.0.2": "2013-02-17T21:34:38.441Z", "2.0.3": "2013-04-15T14:06:53.530Z", "2.1.0": "2013-09-04T13:53:30.048Z", "2.2.0": "2013-12-12T14:14:39.358Z", "2.2.1": "2014-10-30T11:05:12.890Z", "2.3.0": "2014-11-04T19:06:15.413Z", "2.4.0": "2017-05-07T02:48:31.220Z", "2.4.1": "2017-05-07T02:59:06.685Z", "2.4.2": "2018-05-09T19:07:24.683Z", "3.0.0": "2019-08-02T01:19:59.568Z", "3.1.0": "2020-09-23T20:33:12.125Z", "3.2.0": "2020-09-25T19:02:24.669Z", "3.3.0": "2020-10-03T21:32:19.135Z", "4.0.0": "2020-12-02T01:54:14.340Z", "4.1.0": "2021-04-01T17:47:49.262Z", "4.2.0": "2021-04-16T09:05:11.149Z", "4.2.1": "2021-08-29T14:08:36.172Z", "4.2.2": "2021-08-29T14:18:56.906Z", "4.3.0": "2021-11-30T15:18:56.338Z", "4.3.1": "2022-03-19T12:22:32.959Z", "5.0.0": "2022-04-08T16:25:18.056Z", "5.0.1": "2022-04-08T16:36:51.950Z", "5.0.2": "2022-04-09T09:38:04.565Z", "5.0.3": "2022-04-30T11:00:14.978Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "users": {"gonprazeres": true, "mojaray2k": true, "tsxuehu": true, "scott.m.sarsfield": true, "shuoshubao": true}, "homepage": "https://github.com/fb55/domhandler#readme", "keywords": ["dom", "htmlparser2"], "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "readmeFilename": "readme.md", "license": "BSD-2-<PERSON><PERSON>"}