{"_id": "ecc-jsbn", "_rev": "12-839c7f44abb9a3fd259247f4e7f20fa9", "name": "ecc-jsbn", "description": "ECC JS code based on JSBN", "dist-tags": {"latest": "0.2.0"}, "versions": {"0.0.1": {"name": "ecc-jsbn", "version": "0.0.1", "description": "ECC JS code based on JSBN", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/quartzjer/ecc-jsbn.git"}, "keywords": ["jsbn", "ecc", "browserify"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "maintainers": [{"name": "quartzjer", "email": "<EMAIL>"}], "dependencies": {"jsbn": "git+https://github.com/rynomad/jsbn.git"}, "license": "MIT", "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "homepage": "https://github.com/quartzjer/ecc-jsbn", "_id": "ecc-jsbn@0.0.1", "dist": {"shasum": "970577ba31b4976fb1889a298cb7451d896c466d", "tarball": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.0.1.tgz", "integrity": "sha512-LCzo+Li/nY+cdLCCkk9bgkfVZIa3emvYQQEtWwjajupmIZzXp1dVUJpxhiFHtKZwMCGJFFIXWARZwzK1fHj61Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiaVVcwKpG1fliJH3ivSSDXNOI0VytjMSfNv1/3NpmWQIgfgUiLRhN/Mwb3DlkZTOPOuu4P3rZpSZDpcEH6GVUPak="}]}, "_from": ".", "_npmVersion": "1.3.18", "_npmUser": {"name": "quartzjer", "email": "<EMAIL>"}, "directories": {}}, "0.1.1": {"name": "ecc-jsbn", "version": "0.1.1", "description": "ECC JS code based on JSBN", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/quartzjer/ecc-jsbn.git"}, "keywords": ["jsbn", "ecc", "browserify"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "maintainers": [{"name": "quartzjer", "email": "<EMAIL>"}, {"name": "ryn<PERSON>d", "email": "<EMAIL>"}], "dependencies": {"jsbn": "~0.1.0"}, "license": "MIT", "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "homepage": "https://github.com/quartzjer/ecc-jsbn", "gitHead": "d35a360352496721030da645e8054f07efc22487", "_id": "ecc-jsbn@0.1.1", "scripts": {}, "_shasum": "0fc73a9ed5f0d53c38193398523ef7e543777505", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "quartzjer", "email": "<EMAIL>"}, "dist": {"shasum": "0fc73a9ed5f0d53c38193398523ef7e543777505", "tarball": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz", "integrity": "sha512-8Pvg9QY16SYajEL9W1Lk+9yM7XCK/MOq2wibslLZYAAEEkbAIO6mLkW+GFYbvvw8qTuDFzFMg40rS9IxkNCWPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBUhh9iIYy9TjtURssYe43q1YaMA1x08uf63p574sD65AiEAkFEDn58yUSaiFAMlXnYRmyL4uL+SUkaOpHKdliobzJQ="}]}, "directories": {}}, "0.1.2": {"name": "ecc-jsbn", "version": "0.1.2", "description": "ECC JS code based on JSBN", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/quartzjer/ecc-jsbn.git"}, "keywords": ["jsbn", "ecc", "browserify"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "maintainers": [{"email": "duhame<PERSON><PERSON><EMAIL>", "name": "aduh95"}, {"email": "<EMAIL>", "name": "quartzjer"}, {"email": "<EMAIL>", "name": "ryn<PERSON>d"}], "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}, "license": "MIT", "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "homepage": "https://github.com/quartzjer/ecc-jsbn", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.", "_id": "ecc-jsbn@0.1.2", "dist": {"shasum": "3a83a904e54353287874c564b7549386849a98c9", "tarball": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "fileCount": 10, "unpackedSize": 27799, "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGJtQVyEnWqwXhOfe2Z5AABmKbqgenR+dsyKvFbCBWBxAiEAjY2ba3WgbPxnIcHQsOQbOxlS81sTJbHis5ThcGgzCQk="}]}, "_npmUser": {"name": "aduh95", "email": "duhame<PERSON><PERSON><EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ecc-jsbn_0.1.2_1532879660765_0.1527427174348932"}, "_hasShrinkwrap": false}, "0.2.0": {"name": "ecc-jsbn", "version": "0.2.0", "description": "ECC JS code based on JSBN", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/quartzjer/ecc-jsbn.git"}, "keywords": ["jsbn", "ecc", "browserify"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "maintainers": [{"email": "duhame<PERSON><PERSON><EMAIL>", "name": "aduh95"}, {"email": "<EMAIL>", "name": "quartzjer"}, {"email": "<EMAIL>", "name": "ryn<PERSON>d"}], "scripts": {"preversion": "node ./test.js"}, "dependencies": {"jsbn": "~0.1.0"}, "license": "MIT", "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "engines": {"node": ">=6.0.0"}, "homepage": "https://github.com/quartzjer/ecc-jsbn", "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014 <PERSON><PERSON><PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.", "_id": "ecc-jsbn@0.2.0", "dist": {"shasum": "7c98afab245f6df32290473c0abee2f2d39334c7", "tarball": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.2.0.tgz", "fileCount": 10, "unpackedSize": 26962, "integrity": "sha512-UWXb2tKr7ou/9RbgTd1iPw3Yf9p1ZJFPS/8CwsA0hS2OQrXcpTi7vjRUnFdRG0q/tqzNHgPWMYsOwgzgKaClOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTvRxVpcopkXluJWV5FGITFG4eL8LESQAinLXx0dlnhQIhAN+Cl1BfFwHXZQXoDOm77981zqFYwk2mfnOUlrtvPCgA"}]}, "_npmUser": {"name": "aduh95", "email": "duhame<PERSON><PERSON><EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ecc-jsbn_0.2.0_1532886280183_0.328701639638149"}, "_hasShrinkwrap": false}}, "readme": "ecc-jsbn\n========\n\nECC package based on [jsbn](https://github.com/andyperlitch/jsbn) from [<PERSON>](http://www-cs-students.stanford.edu/~tjw/).\n\nThis is a subset of the same interface as the [node compiled module](https://github.com/quartzjer/ecc), but works in the browser too.\n\nAlso uses point compression now from [https://github.com/kaielvin](https://github.com/kaielvin/jsbn-ec-point-compression).\n", "maintainers": [{"email": "duhame<PERSON><PERSON><EMAIL>", "name": "aduh95"}, {"email": "<EMAIL>", "name": "quartzjer"}, {"email": "<EMAIL>", "name": "ryn<PERSON>d"}], "time": {"modified": "2022-06-16T04:34:32.552Z", "created": "2014-03-06T05:48:33.314Z", "0.0.1": "2014-03-06T05:48:34.662Z", "0.1.1": "2015-11-23T14:19:09.579Z", "0.1.2": "2018-07-29T15:54:20.855Z", "0.2.0": "2018-07-29T17:44:40.282Z"}, "readmeFilename": "README.md", "homepage": "https://github.com/quartzjer/ecc-jsbn", "keywords": ["jsbn", "ecc", "browserify"], "repository": {"type": "git", "url": "https://github.com/quartzjer/ecc-jsbn.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeremie.com/"}, "bugs": {"url": "https://github.com/quartzjer/ecc-jsbn/issues"}, "license": "MIT", "users": {"mojaray2k": true}}