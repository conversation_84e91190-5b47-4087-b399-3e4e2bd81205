{"name": "nodemailer", "dist-tags": {"beta": "2.4.0-beta.0", "latest": "7.0.3"}, "versions": {"0.2.3": {"name": "nodemailer", "version": "0.2.3", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "12c093b5ad877514a9c402f5899cb6f5dbafb885", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.2.3.tgz", "integrity": "sha512-J4StaSRpwjmSlZWGzKavcRV4796DinaeDvVDB0FAlsvwOF5D1YJeNGBbQlsDD9qZytM9FaprKr6qvo2GFtPeNA==", "signatures": [{"sig": "MEYCIQC3UpqYOipIHXs+eag8NCb9nF0ohLTO+rzg3qRcf8dryQIhAM8pdkrBL/epyC9kFUf14uFbhRbc9eCRCgIaIRaQvFjc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.11": {"name": "nodemailer", "version": "0.1.11", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "420eba41d28400e30337f908d4e4ce3075d44a02", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.11.tgz", "integrity": "sha512-kKfLuqiNBGKVMnZrjZqGUdft8pfzQVZi6xuCNX7hrf8rsQuAQNpywq03JG+pLwja3o+n3KzJ7QP61r6Lm4SWVg==", "signatures": [{"sig": "MEYCIQDhdnXFRpASL6tSp4bdInZ8AE7wFGF4h2Ma60kh2Ia3uwIhAKnzCiEx6eczvR7ERSQj9sn0QdoCJiZidU8+HGWOekFn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.8": {"name": "nodemailer", "version": "0.1.8", "directories": {"lib": "./lib"}, "dist": {"shasum": "04e95a20fbaae7dceffd0a467c14eaf061fb05fc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.8.tgz", "integrity": "sha512-urGABTBJ9+89fxzrfnc5EgsVX0r5eeLWJRxCEFE6A3rAU7TWzXqfnDPejrU3d3cXQQsgoscRV5pWUk51vO5dsA==", "signatures": [{"sig": "MEUCICxG7EgNIYjbsmXLbj1Vy0qotxJUbZJJXd0FK8gVJM7+AiEAjgZADDjd14jVro2db3X58AiLbaJU2hVetLrrp80Y49E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.9": {"name": "nodemailer", "version": "0.1.9", "directories": {"lib": "./lib"}, "dist": {"shasum": "e23026cbfce95dee05623358d2eab450a79b533a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.9.tgz", "integrity": "sha512-rgoTaQyxphV8pNqm9Mt9IcmjDKI84GJeQYeRhY1UKQoGoKNBU6XAeDpFEyRPzvOtJk8zf4hDvpqG5VOWU8XhoA==", "signatures": [{"sig": "MEYCIQCP/G7QPFwVWnItplS+LLHVjwj+QIIwupGSekwnWHHSxQIhAMEnuXb/4DmKcjQZqHbaB3DFybDi82GQ7tpm1ymppGN9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.10": {"name": "nodemailer", "version": "0.1.10", "directories": {"lib": "./lib"}, "dist": {"shasum": "79822261753b14643f2918814f5365767e0c84ab", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.10.tgz", "integrity": "sha512-ehEeFpLnCxP6AalbI6Ou0pSmDQg8hXdKK6UXLNknMK0xgYSTCDSxCDYP+U9PCA7NP6onqZfRVXEtzXS/AwqRqA==", "signatures": [{"sig": "MEUCIDotrmkPHxuNevH2De0pwrNUcFSlcMMyInT8yfn5LGAoAiEAmgPgrHB8FKPdTupTC4866qpXP8gm3wB9Q+mj3w5i06M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.19": {"name": "nodemailer", "version": "0.1.19", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "dbaf7d62c04fafdfe48d0242417a392052be867e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.19.tgz", "integrity": "sha512-eOPLxWW8WpjkfsZCW3O/7rmyEc13XhQmdBH1+AXqi5M/yLz2V49JRASY4UAGRSd3WWzok9+QPh7tWkkgCyUSSQ==", "signatures": [{"sig": "MEYCIQDl6VcrclwSAOX5itzdXOhJLJFPj/8lOMRAqwTwohWelAIhAOdhIkirpgisNwIAAKv2EXFABKJcoOVROdlVhvbTzgdc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.13": {"name": "nodemailer", "version": "0.1.13", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "e38caecd50831ce1041182aa95caf6b2759893c5", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.13.tgz", "integrity": "sha512-UqaR/xffCX0ccFZuWaFEJxWVfOJLogAKDg+R0HeocGrh1oVhDggHaSuR8T3leDfR8AWoT+jmlmOBjc7rPKr/TA==", "signatures": [{"sig": "MEUCIQDP2F0CL05O6B6odto44nRBuQEZzw/TF3GPT8Qs56lM6gIgB0nkVkDb8oQ1G18xQTVjsZ+pr+BRyHZWuk8Nea33HIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.14": {"name": "nodemailer", "version": "0.1.14", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "2dbf96801a0230ee69b52f7c528155dc32260f6b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.14.tgz", "integrity": "sha512-pzmIU23x/oLCv/5IBu3ViHiIz9drMVjsKMrYxv/UFo3FZrqKo+UMjp0y2uCo1WkUSYx+vMfItLxHPqq7TpN8GA==", "signatures": [{"sig": "MEUCIQDL/OMIbqlrUA9bolr7fj/GUWir7OQtZOhdCsrWAu21PAIgPL0GBDQtJ47jLAes2jdsjQDk6Ugc1SlUqc1KwJjtZ9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.15": {"name": "nodemailer", "version": "0.1.15", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "ad85d9f973a56dbc8a87aec3e423f36004a58415", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.15.tgz", "integrity": "sha512-p+efT3V4mzxHtPO90eDtPOvtaKS6zP+BmsMDUsBKZgDSsLxFtA6gEDwWtEPSFgVbvz2KEIgDM2XCipfHYvxx1A==", "signatures": [{"sig": "MEQCIBtGlq50Y0OLvBoZawKr9j7peIviyrLF1xNgXY004CGJAiA3JJ+8nzMRDs+pf7WSi9NjhbbGhKHt2Cxi16Tr0cjfpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.16": {"name": "nodemailer", "version": "0.1.16", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "94a75172ba902e3679d3191a6fa060455ccef20b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.16.tgz", "integrity": "sha512-4saHdMv2ST4mC7Xmmh56J9d3nPmPs6A9W0aLgZeaI62MVCwiepBJpgMwcCWBzTJxxvtMulXmJPj5dHPVkzrekw==", "signatures": [{"sig": "MEUCIQDLQUjF8Jik53KaQR/sXzm45lNjbP64ZGvAF37R8W61IwIgTkJ8wbrgAkigEePCGR6K/l2feQ17Hy84Slyn1ek3Vw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.17": {"name": "nodemailer", "version": "0.1.17", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "a7cf77598ac1ad08b87acfabd0450de91877f215", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.17.tgz", "integrity": "sha512-utIY8DrJ95WRlIlYixEzUhmEQp68bGpNVV7j9+Qke1Ixc3OykzbB9g7GNTILYhScO3T3nAthZSW8dARPKBp8mA==", "signatures": [{"sig": "MEQCICqPt2r9xCqv4xRDkXF3abVLYB/XI8gfRn7NoUc61gUcAiB24grK9GKnX/fwNLB+5eqJBGy787hTik2NZrxn/H0wPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.18": {"name": "nodemailer", "version": "0.1.18", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "a6b9d067aef3bee0760796d07550cab5c83f9bd0", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.18.tgz", "integrity": "sha512-smyOMADZgmCSPw6xEOzgaeAHQVMqutpnzfEfdDy8TZktSrFn9Vje9SoIJhXwLLOpcMJAIWh0kmOp9xlbn9nhAw==", "signatures": [{"sig": "MEUCIHV+x6Bv+r0XDqXK9Jmc9T3LfwF5FGwJUL4hro+SfzpTAiEAvr9I+/HviVQr3hheGEgKCgd/sgjr64SpCzhH/HoAKi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.2.4": {"name": "nodemailer", "version": "0.2.4", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "5e2ed789d903f699aa6fc5e4634478814140c20a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.2.4.tgz", "integrity": "sha512-/04AH8jbuGWDvr27aw46gMCiYl1l06ZUUjMoTOXxMzMqecmEjOehju1knkXBK9Gz8TwTRrA9ezdbwpf0n61BMw==", "signatures": [{"sig": "MEUCIQCxiDxtGnAKyHRMPK+/PDDRW/Q+ViBshKk+PNjZqeX8ugIgSKCsG3a/FFu38Sm3hRjanwM2Wm7cu7ZmuiHwN2nBSr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.2.1": {"name": "nodemailer", "version": "0.2.1", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "c80e543598bb575fa591ccce10b535e4320dd16a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.2.1.tgz", "integrity": "sha512-z1Wh7ydA/6CIP44nk81EB8UYG9wZTN0TEeGFl16E2wN4rcZWI/AZK8Sp0EJ0pojja7Id0Kzkj3EehU1QlhhPCw==", "signatures": [{"sig": "MEQCIGVWvbOyXOsHdQqk5Z8G821vEX6DQZ/dObwYBn+2om8CAiArGyKXQt/B+V6FN6dC7Q6TSCZChnq1fkj4NAXLgYBUBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.1": {"name": "nodemailer", "version": "0.1.1", "dist": {"shasum": "0ee29a6bce95463855d5cd27eb886e0709b750a4", "tarball": "https://registry.npmjs.org/nodemailer/-/<EMAIL>", "integrity": "sha512-NqLZ6oGbcDJNS5C/rkxCub86N+tNhUCeCEJ3xxoydCQOHO7JcfBqadoZ69k1q8Dh4AXDc00+CuF6qtks141nMg==", "signatures": [{"sig": "MEUCIFOSz0ttTN8w6TWzmNXHpHn51GxLsLXS9jSmyXF7ngHYAiEA8v2lFnzfwIVS4AFnJm8mmXB38YhVlqBBdVlaDVzbkes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.12": {"name": "nodemailer", "version": "0.1.12", "dependencies": {"mimelib": "*"}, "dist": {"shasum": "c4ca5cc44bc588e75c524e11b60729ad5f928384", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.12.tgz", "integrity": "sha512-FRrVn3SmiotwhUjvGFEypVGsal1NyDDYUD0f7LloETKOhQlH02c0pxs9UK+1CMFzH6Ii7jHfpualSnFk7ZODmg==", "signatures": [{"sig": "MEQCIGNsfyUPlAlSHnOvwEa4B2oXq6JWYA6K3P+wexX3OcaWAiBmNMJy2StMsL8iVgjwUz0aE3uWJBzubjbOowtZuz1JuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.20": {"name": "nodemailer", "version": "0.1.20", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "a91748a448eddf35fe24d7272c8187ddcc38e253", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.20.tgz", "integrity": "sha512-0p+nadmJiD2SpYsxsAdj26QYMZKTxAHC6h4zyl9sKD5QHsyby9AdqxYvP5F++rUkc4x1goAyeOi6Xl05k6eekw==", "signatures": [{"sig": "MEUCIQDDYUlw+xj7ZW+KeDyn0o+6ue1J3Y3tY2jru42Uo4GCoAIgEn9ga9TyyVjmxmVspS2dWV2UHTnip0xW/0arOq8bbYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.21": {"name": "nodemailer", "version": "0.1.21", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "d1dfeb05201f0c4e8f47e4ad1762db9f1abc7d42", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.21.tgz", "integrity": "sha512-u74ZBSQbdNtfQQ5DBdUfnn1IomoTBvlau31YoqcKx5LuvEiylLU5ZEODgI7ucjgTpxJG+FAvBrA2CcSZfyoCfA==", "signatures": [{"sig": "MEYCIQDxakBxTCp0pHgxNH2Jbol/CiMdgEQJcvcHAfSMrdyVXgIhAPrKa3VJaRRsfqbtfDvCBpGCTyYElP3rlsdq4ia47TKJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.2.0": {"name": "nodemailer", "version": "0.2.0", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "0147aca24b280423f9c499abf07a2f2355949e82", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.2.0.tgz", "integrity": "sha512-JBJQOBpgk4e5vgWrEeBQBLpZJdkhJIPk7ssXqz5QBpoc8I/6nA2ALYya4blcEN12AM4i9ggTZUTE682bf2ZgXw==", "signatures": [{"sig": "MEYCIQDFPn476D9fPVu0U00CtkCA9aNP4zyZuIXCLlL7s3IvWQIhAKItU5yIH8NakyLhEl+y1WJ8reEOGk51ocTuQs63rOIo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.3": {"name": "nodemailer", "version": "0.1.3", "directories": {"lib": "./lib"}, "dist": {"shasum": "c4fe01ea2204166b6414cd5efad04aaff09881fb", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.3.tgz", "integrity": "sha512-x2mUzEcFHGRX/uZTKEKBRpAxRAbCyqCrKI3/DvACdVGpcVWjnGq/LeLxxbtzYAw+kLS/9P8JlNbZ5bDKmgKzuQ==", "signatures": [{"sig": "MEUCIAu6PL3BB1FPZcyMCaAIY7Gug/2Mp5uDu+qrGf5ggEXrAiEAgLCM5T1hAQ6RBeMRZtYJEJ+M6zsQqWZto3HuCpoTZRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.2.2": {"name": "nodemailer", "version": "0.2.2", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "4e283bed91bc4a7ed051162907954f4d00230320", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.2.2.tgz", "integrity": "sha512-iqaPxBUi8LDy8Uv4JkO0CuHJarhMbk2ZhPK0rrNA5hqdcTNq/2xvmcgeCa/2cvmTVedJkbd7Py9mMEI2srl2dg==", "signatures": [{"sig": "MEQCIFGTSAk0fXUndE+/TxyMEFfywwgx3vL/Eu9+NTBwy4ZCAiA2rOfMXXs25aW9K8xzB1zk0tw7AdxfGkuAtSookqgKRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.24": {"name": "nodemailer", "version": "0.1.24", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "c766e5992ed6090c02dd977cf12e8d3d26f7b09c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.24.tgz", "integrity": "sha512-jVDV+LWlMC7054nL5gSQrd1qKaihT2Y5P05IwogCd53ZsIs6bp2VKJ/K8v3ypxatvmbmppab5GJd9H2OO1doDg==", "signatures": [{"sig": "MEQCIHGukxM67LX0OYVNd+0a6dU3cMZ/eb5MullBoXhjENgtAiA6YG78/HGIxZA8tz5z9BZQ9TaDxEJ4AMdZBeGwecjdmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.23": {"name": "nodemailer", "version": "0.1.23", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "1e9d73fd12b62cb3be1e4e72f25f62b8e24a7913", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.23.tgz", "integrity": "sha512-1JADZa6CkgiQPEpvex3Ds42DXsHSB3ng8WJwKNONT7PPEKxFAz4NiAT9KzqE1JRtcT3wBjUhqvNVyG8tt9b3tQ==", "signatures": [{"sig": "MEYCIQC5i+9IbMkzpceRqrKYIQts8Fn3+pL+elwoKA/iZGwWHgIhAOjG5gIuww4T6MB0tAQOCIN2gyKUdHwgsNP3iS0mOBpM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.22": {"name": "nodemailer", "version": "0.1.22", "dependencies": {"mimelib-noiconv": "*"}, "dist": {"shasum": "d2abdce753d4e2fa9e5654ff771a70f5382b5c11", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.22.tgz", "integrity": "sha512-J8pN7h75UWBtEQJL77dZgUPFwDQ+eSo99I4ARQJJ19c2zWd9sKUE5t37rgoZ2pGdt7LtsSuU1ZhLW0zGpMaa9A==", "signatures": [{"sig": "MEUCIQCSgfdtwQv3zqgw34g0gtEwwS8vp3+rpMyTMY1zdomlOwIgXI3+kRgLxsLoOgLd6xpxxGB866nuFnvW1Xc3fbz7Ep8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.2": {"name": "nodemailer", "version": "0.1.2", "dist": {"shasum": "28a212a5cc87ea6127e1dfe32a29ac92ebfbe222", "tarball": "https://registry.npmjs.org/nodemailer/-/<EMAIL>", "integrity": "sha512-fIzZq0G2/uhYWX66DBQmHiotIwDvTppuVtz4L1edkSLP3YMWVJt+Y9+ERVpy+nlt3PrH01A17ifA02vmdRlK2Q==", "signatures": [{"sig": "MEUCIG08/7vp3gk+bultmii46B6GjlA6pYvQ4oOqdqkd6BLRAiEA+XbOaQq7ZmzIwGveHDt/h/LeQGgiKHVszUujDvnjHLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.4": {"name": "nodemailer", "version": "0.1.4", "directories": {"lib": "./lib"}, "dist": {"shasum": "df5ed19e2db7afa75f76c3dc55719873ce6edfb9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.4.tgz", "integrity": "sha512-69VkOeJOiBAncy0NtI8yU87JSA+M92Kad4oB/TlTqTYqS6WTaMbqEkds2ZlGWIXrKHQbYmN7Skh4Ru4cB+MS5A==", "signatures": [{"sig": "MEQCIBdn5uv1v5wEE35W3pFvydWHoF4d4OKSIAPH2aoC1oHXAiBY70mDJGyj8DWzhRTT+xAjbX6o/IzxeRVRqWfdtLbLiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.5": {"name": "nodemailer", "version": "0.1.5", "directories": {"lib": "./lib"}, "dist": {"shasum": "28e06ba22cee7fb9c34f732da5eff36ebd3ddc37", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.5.tgz", "integrity": "sha512-nr7ithRoScHl/QGmu2ws1N6HNGKAWj64Bxu3CF5kw5dPZrfci927/11gODggm9TACxhaZmDUtYYU1NrIsVx08w==", "signatures": [{"sig": "MEUCIQCHKj3cLGloLAmJRxGRx00LPddWT2MUAafe+FZR4SWL3wIgcrAtEN/fCkbU3E+HYb3wMOvE8Bnl+YZImpIeoYB6h4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.6": {"name": "nodemailer", "version": "0.1.6", "directories": {"lib": "./lib"}, "dist": {"shasum": "7b060435c3f44659edbd8c987a1abd244ddaa540", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.6.tgz", "integrity": "sha512-X8DTJb3FCKRNNXHqM4xUNTFJwW/15cM9lrudwSMWJqKMHxt1vj5PJMmeBFD8+ynfCMSvKqpclACWbLsLhZjRSQ==", "signatures": [{"sig": "MEQCIDjMJQkKHRsPhAe50L2fgD5/n95ZrpbjVCJXJaC2lrOqAiBJhpc48nHvYJskVTgSupdzAHRzB5/pXmdjx8NZVigU/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.1.7": {"name": "nodemailer", "version": "0.1.7", "directories": {"lib": "./lib"}, "dist": {"shasum": "4d6fc7e63a5e9d4be1ee472f924cdfb430a6b109", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.1.7.tgz", "integrity": "sha512-iJRHOZ4XKl9+rjoaZO17E5Hu9KQ0caa7Lyy/hs6h+NpB9gp5Lze+2fbTwDQbs28zEQvzBQpq5MMLszILDN8q5w==", "signatures": [{"sig": "MEYCIQCOf1mYNvZw99x3e5k8abfX42pkHBbRVIB9oLrtl0gGQgIhANBkDSpO1X9Ujr2l8pBs5yq9nyoiRE/Xi9Wg5QLRNQ/O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.0": {"name": "nodemailer", "version": "0.3.0", "dependencies": {"simplesmtp": "*", "mailcomposer": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "f48956b25bffdd1be63c8f4ad9a76193b79b6831", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.0.tgz", "integrity": "sha512-J<PERSON><PERSON>bRcDojpe4WckS8/YIOesd8gGMCHWOoPvRXUmqVCO9DBmu015V4FmjTHWxy5al06GeaTbDZNo/9GbsdsCN4A==", "signatures": [{"sig": "MEQCIGxV+DOXw4nc9lzquD9tEwbrZeisP6rmsYaN7HC+ZGEgAiAS4nRP7yMJJRbOdOXLI+gtenFrcF/ElgyTcyPWK255ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.1": {"name": "nodemailer", "version": "0.3.1", "dependencies": {"simplesmtp": "*", "mailcomposer": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "19285785114dad8fcc549550203d975e77bdc3a8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.1.tgz", "integrity": "sha512-I3Pl0UGxInmKnZuwNnFZpa1RNi2Ir4VJDIGINnUIX2HVHDZomQwZmF8XGl9Ic2X6yk9qTE5akqFwUeK2fLFubQ==", "signatures": [{"sig": "MEQCID+hu/U/fu/EvGZaSxoH/HK+ZsFg9q95PuQFo38B1/stAiAKBL8R7rHdH3HSrk5ai4CZ6lw4qje/wvev/NGm06vTuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.2": {"name": "nodemailer", "version": "0.3.2", "dependencies": {"simplesmtp": "*", "mailcomposer": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "9b32259054b153bf2fd8efda6eaceabd2cde6fee", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.2.tgz", "integrity": "sha512-MLxehHrk+XsvfktGKuEbreMu6+iIV1JAOusA1YVgazV5c54XjyMn+/hLBpV9be8f5xHpAVvpb/GaHtBiNWFoZg==", "signatures": [{"sig": "MEYCIQDMHFRkw4VxdQYCQ9vPtC8AuwCY3T189UnPNoEqitLtvQIhAMbh6ina9F8tHjSJU2uYYtOnH4EUIs+5TgJVn4hI3mtw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.3": {"name": "nodemailer", "version": "0.3.3", "dependencies": {"simplesmtp": "*", "mailcomposer": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "50af25afe7bd1b991103e9908e5e58c97bc4e7ae", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.3.tgz", "integrity": "sha512-FdEkmk1ND5phlZvjoONceXajZoy3VKWt76FdRvLde3bd3qi4K2qA6v+T0PScLyfzcTeu332p/NxLKWy9TRBsKw==", "signatures": [{"sig": "MEUCIQDfQMdxbg/2B04SSaxWBCVFOfvx4wHMuR0wn5kF1eG0SwIgMEZPcHbXyzQc7ntYbV9Lk4VX4so2PBXVMQseXf2U0ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.4": {"name": "nodemailer", "version": "0.3.4", "dependencies": {"simplesmtp": "*", "mailcomposer": ">= 0.1.5"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "08872e8a76fb7faf9192a605bf8114099bdfa073", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.4.tgz", "integrity": "sha512-AunGqYsUpcm3HH5tARkdKqLWESlHZFw7q4MSRJBb5p8OxAyVbXj0mZiSmBlDUy0+xQ1ZsYMSCL7arfS47CPjIA==", "signatures": [{"sig": "MEUCIQDk3HqUvaxL6UnIycz4I8C12MXpuD5PEhUa3z+iq5vU1AIgS4py/Jd0MX/Ox/7Mzh4FmzSOIas8MRPXwzi6t8uwfoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.5": {"name": "nodemailer", "version": "0.3.5", "dependencies": {"simplesmtp": "*", "mailcomposer": ">= 0.1.7"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "6c748f15705172a7c7d72b680bed61c5ab566aa4", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.5.tgz", "integrity": "sha512-LUY8ibyOXFmaWxGOYILY7LGLbE0uewIRKUPPXC4zkH8lDUpIbEyjCjLpFdRCtfxcfh13KZof1v2KZsbhYOfgjQ==", "signatures": [{"sig": "MEQCIACv71hIqvvq1NIlWwSMiozOeP/KJ3Jb6MVfFfoGo+oQAiA9UgsGBRH98mhxOfeSCDn0U09kedsOw2zVlIl8f1mNzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.6": {"name": "nodemailer", "version": "0.3.6", "dependencies": {"simplesmtp": "*", "mailcomposer": ">= 0.1.7"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "e9215e60bed6e88bfec32b833f1037c572813fe4", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.6.tgz", "integrity": "sha512-aIPzyWbyhtymVKO7AjLFLRPOYwgzzNDCSnznFAHSeHyZtI7ojsCXPq5/qAfFg0virWLDhvkqMlcJ3O8HAnw22A==", "signatures": [{"sig": "MEUCIQDfOy+s/Niytpe+NywG7BtigxTFvJnddmF9y41QxrtcNQIgN9CvXsRqwuclOBOtxABYPh4UKeq9WOXUCnJ4bmckpag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.7": {"name": "nodemailer", "version": "0.3.7", "dependencies": {"simplesmtp": "*", "mailcomposer": ">= 0.1.7"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "a90f24d40ddf80c3c9607a6a306c0995c4ce124a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.7.tgz", "integrity": "sha512-93+n65vN40rbQJH7Exnbrlc3u5ytvpMxFpjApxPTjxRNP9KNvuCCSkwELgqYOheXDW5YIMIcvttXnxITJib1aQ==", "signatures": [{"sig": "MEYCIQDMEUdTRmQQVBqwGeSXIUj07Z5L827UhgkJt/EYJyc0tAIhAJjs5YVpzXIzZCw6w00LvDxYcB5tDunHBQqDip98fj0B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.8": {"name": "nodemailer", "version": "0.3.8", "dependencies": {"simplesmtp": "*", "mailcomposer": ">= 0.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "62aac360a64b013e1c46bfede79afe70cc4ad66f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.8.tgz", "integrity": "sha512-ssFwvwpbJG3hX1RQGGFVbOwxnuoPy+idqeucHZAyFrZD03PgrmOIaOmT8iNGtdwa94O1SYM8tEwbUuF31cWOPw==", "signatures": [{"sig": "MEUCIQDq2nvwxW6xflZC/OhX1RJ8s1pkUredi1el2Q6yW7ds7QIgWGj3oYrrL4yf2L5T3WhB/wRoOLLLTCuWaf+CPYBEylA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.9": {"name": "nodemailer", "version": "0.3.9", "dependencies": {"simplesmtp": ">= 0.1.14", "mailcomposer": ">= 0.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "acc83044548f90a1f8ea67166fdd6052dc7409d8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.9.tgz", "integrity": "sha512-IqavAv7ZDMputBk4hNzR3f0PltUpbCe+gM2dqtsb6cx2aBoaiQeRcfK2mk8mZW5i8LyfUGBnD6G2+lL65LsqfA==", "signatures": [{"sig": "MEUCIGFM+n3vEkg5npDIIWRYNmCjPw3HSUXNQ8HiJxt+LMtaAiEA+1LKgAgqxfoGxNGbHRlegKKtVhLGl+UbKyTRhOYFwmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.10": {"name": "nodemailer", "version": "0.3.10", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "4e74ba69fcf121e6e49682fb0b982264c866f10e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.10.tgz", "integrity": "sha512-7hXHEyXXcNPI3FaKaEh8CJhMyf/uymnBu8Sk2GYwFA0UA8GY483KP+TY0y6lFtJaGPPM37JJcqBigElxkY9KLg==", "signatures": [{"sig": "MEUCIQCiajbeSo/2fFzj4WDdbBd+b6cudvne9Sf3Y+qhs2wAOgIgNJPINyOg61lUl4HDbcQbc/ai5K1n1NFC6APNyvfYnOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.11": {"name": "nodemailer", "version": "0.3.11", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.10"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "a20416149fffef9adc6f38dff8877bf9fa29d7ac", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.11.tgz", "integrity": "sha512-RIAU8vkt7A0UaSloTItccDuSY7/+WIRVj4Iw1dnDi59CyV9kKE3aTOASIC3rWUFWH5zfZ7ILVWwIffSKGs4cHg==", "signatures": [{"sig": "MEUCIFh6jPFd3G0tRO2g7DYEe+QTfJOVFtP0qbqacRLe3rB/AiEAtS3YXOp0RNpimbUP0rDpYYeErLZH9Ju822E8iADcQIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.12": {"name": "nodemailer", "version": "0.3.12", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.10"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "cb62d63a2463ef93c9bf91f3f966ddf53cf0455e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.12.tgz", "integrity": "sha512-5MPaVh6cpvEmrFmyOyQ0sq8t5cXW3kBSB3kbAUJuADip6a7uPWqo0FZ4u5JYDnGvlXd5BiHiqTIck6gGxJcIgQ==", "signatures": [{"sig": "MEQCIDs7yQjsOsO+B74r43LMv4APc4Pl4X93Po123cUl1VTqAiBFFCa/eAqxe7nyRjPA0txDJQ8p8Y0OL/vuQFEQp6vpDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.13": {"name": "nodemailer", "version": "0.3.13", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.11"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "ca64ef9065fd7f17759870b68041627fab3b3595", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.13.tgz", "integrity": "sha512-<PERSON><PERSON>eaBG2hVoZbHwD7d826dpQKK3yZv0kByjv1cBZBcoBK4Nolhj2C6zSKZVKlLcQYtI864cpQkKA4CuOud5B6kA==", "signatures": [{"sig": "MEUCIHpYGoODrUL+L/6659/nbhzZ4txSkk/0VCrmF2EIWuaaAiEA1NRBVTvvDr9MTQTEuFLaiQIuNcmBR7hNEdhhrERJGt8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.14": {"name": "nodemailer", "version": "0.3.14", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.11"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "db89555db03693ed0d6fdff8a3f8ab791df6729d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.14.tgz", "integrity": "sha512-Pa8G52/JzwfzlTMZST4pa1viIly5GO56eL9j9JPr68an5qHrKYQHVSqg8ZSbD0rShG1aebbYEjBb1rsvkBGyCw==", "signatures": [{"sig": "MEYCIQCRNCAOpPHIjCdkItdJvTV3ugqNSwsxCEIqlMXQIOndogIhAPAxeo5p3ZlLh7TGKGl6mi3QX9VgEE8EbePpR66TWjuT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.15": {"name": "nodemailer", "version": "0.3.15", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.11"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "60cd17725352d194fe00d31a0d0c6b1635bf7151", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.15.tgz", "integrity": "sha512-MvFyIjBV94RGSQzD0kRr2k6/64Mgc2Onz3B25N6fyJ+AYz7eSC7lH8o9hReHXNt2Mih4+tsDZulzZN4uSPKBow==", "signatures": [{"sig": "MEQCIG+ssOnNqp+vWNxoZ1MbfgbYnmN8EqXyJI9oMhhhFdPVAiAm6rfpvEPJehaXWq/mBn+Mi8I08U1BZROTgtp30Zfrcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.16": {"name": "nodemailer", "version": "0.3.16", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.11"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "e578f77167f38e1a37ffadaf21bd06871cb68781", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.16.tgz", "integrity": "sha512-7V+IFy0uu9OEVYzV4tqSz95jSAHL8KR4kPgQbVxzH+hoVUoaMMdB9sGl8kKu8DCUONjHrRH2W5K2m7+VtwJm9A==", "signatures": [{"sig": "MEYCIQDwO2gTJjnCvzXlXfIOLmP4uYndh2EJVwXqCkqRyEfdjwIhAMChC+UeAsWdksQF3uI6DzlkHVwO822dq4mqfCAfBLRD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.17": {"name": "nodemailer", "version": "0.3.17", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.11"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "975851d4f2dbd84ee4e66d499990abd52d86d7a8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.17.tgz", "integrity": "sha512-n1+oNwB8Mt5/+cKbsj92AFZOA/SV5CZVSO5DPNOh/JaV3EmfEM4A7md9N+A/oIeblbnZGnyjIyAeC/nfg4B9VA==", "signatures": [{"sig": "MEUCIQCISLJ2v53CbnG27oP+TBBp+cq7MGJ80QiBWiAfhYaH2wIgcLkPmSSAdt5lRHNPsThpYI3VpS3jp1+Aybn3S6GEhCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.18": {"name": "nodemailer", "version": "0.3.18", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.14"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "e2e1e7a1760fcb4c6747a483196afbc2b00800cf", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.18.tgz", "integrity": "sha512-kOzKDjAMndROW0PwiKVL4YTAuCzuLzFxSfq2aNJfBAz+/qUVMNOhLZ3YH9PbjRlDKSq8duQqhV3yu5duZyommQ==", "signatures": [{"sig": "MEUCIEjxZZ/BDoPDUYE+g4fQuVb4CoDLmr4Qg3MjYHWo+N6SAiEAr6QMo+lQR6esKX3qYoi3Zkg7cTX0dF3N6auSycYZecw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.19": {"name": "nodemailer", "version": "0.3.19", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.14"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "235c032096d4cf4b6f437d376c7f5329c5cea5fb", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.19.tgz", "integrity": "sha512-+HWKsF7Xi17EBw3k8wLBfot+7cmvl+XbDOOoovT03S9sa753YG6DG0uC38jZAdhHIvkZ43GJvCdQGt0DhrJYiw==", "signatures": [{"sig": "MEYCIQCb4UgZbLA0/u2Afy5c/l9ZJowtsQQg8Rk81kUZeN5LQgIhALaQYfx5hnwNJcuitc04768EpcKbcx/mh74ZIrlI1CUH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.20": {"name": "nodemailer", "version": "0.3.20", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.14"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "9960c82c5d85bd21b29d496bf8ac7ac0801796a9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.20.tgz", "integrity": "sha512-C154zLwjlSHBW5pI9jUJYO/X42EPKrTHTQud5amylqBO9H3InSozPMcdC0GLY//AvN/1CabxfoEI+ir/GQUqhA==", "signatures": [{"sig": "MEUCIGAKYGIDRCcJ4GOvepvO/V/IGhQT+kWeDaVk3KfqRNHaAiEA/uCx/pzLKlHpnnyQnAV5IJRgy1Kq1q0MLJUDr317QGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.21": {"name": "nodemailer", "version": "0.3.21", "dependencies": {"simplesmtp": ">= 0.1.15", "mailcomposer": ">= 0.1.15"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "d9637a1d585f36fd30362c3036ce0692656fe771", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.21.tgz", "integrity": "sha512-vI6X6JoAJCTEXclJEF6hLawol/T2ai8MwPBGV1gppLBNfqDDzQNO+JByv4rj3IXmI/kg/vIKM1wR8x1DOIcEPQ==", "signatures": [{"sig": "MEYCIQCZwobW3mIhOCT38DZUMDs2AOWFdaXOGX3MAVBzpnv6VQIhAJqd5hfVBQwmoRgZC18l4Q6C9+Ep9wHePk38KNpbSm7m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.22": {"name": "nodemailer", "version": "0.3.22", "dependencies": {"simplesmtp": ">= 0.1.19", "mailcomposer": ">= 0.1.15"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "4dbf313d1e6ad9a0627b032b600ba248dc9a4bc0", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.22.tgz", "integrity": "sha512-1bmVdy6TrvA9O1XcXZbeMO0Tbn0hzbkdQ25IBmedEzwoCP9bfbs6m2N1E+PpcIIN6/INTuCP4RuQm0XBaCZ7Tw==", "signatures": [{"sig": "MEQCH3/azNqy2SQpGqTsNew3eSG4VrtOJwPJBcB73Mw8kDwCIQDH0o4PKgf4HNB3lMiAKR2dRzeJKrf8HQ+7DfZ7TuvD+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.23": {"name": "nodemailer", "version": "0.3.23", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.19", "mailcomposer": ">= 0.1.15"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "3cde46fdbb5e9c3bdb2fbf0a58fe470b1130fdf3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.23.tgz", "integrity": "sha512-/wnzpZoIEJmfUDBjx3cZyOx+S60SXlCcW6S9siY4xLcWrMcGnePbrZzoot1TRP2gFTEPV41V7IpshNRyUuXY/Q==", "signatures": [{"sig": "MEUCIQDbaNN/FkvTcs+0MjLZA5JUMIpUlG0hEWyD1Gn9grU3KQIgfcUjeFvSmbiARxiT7J6QsGy6fI9K+GKhOVT2IKqpl+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.24": {"name": "nodemailer", "version": "0.3.24", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.19", "mailcomposer": ">= 0.1.15"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "7f3180c31b22378176192aaab465ca672c89fedc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.24.tgz", "integrity": "sha512-NbcbbzCW/ounAAYPf47vKEaQVXQw+0uG0V1VyWKtWuQQVY/tYoACvHzeSWU0AgFb8YRiFXXRXyo9aC5vuS/m5Q==", "signatures": [{"sig": "MEQCIHsR67vssorLDUoYt0pOAg434yUlKSIYIsK6Yo9e7HlMAiA7nbE5M0KjXdrht0FqEkgZZWnNYaaAK9zE8dz9euB5jQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.25": {"name": "nodemailer", "version": "0.3.25", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.19", "mailcomposer": ">= 0.1.15"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "bc1ec1ea0043e5f6e7bed5606db0e936f894b29c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.25.tgz", "integrity": "sha512-iNBwkCwlQXERadJ6kYbBLCaPjbu2DZHL2rMvWvM7bAN0LNCRbG1PSTQ71tD7u6ccxiHGZd5H8kqv4X64XaROSA==", "signatures": [{"sig": "MEUCIBjbQrz71D6Ihz8jM+p9SdIEgl5tf9g3unHVyMJ9QAK3AiEAqXQYM4x9nfbl0idTLd/atP77vm0lsw+E5Q+a15bRe7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.26": {"name": "nodemailer", "version": "0.3.26", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.19", "mailcomposer": ">= 0.1.15"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "a937d8de6be5dcf384d840a7494b73248b193dbe", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.26.tgz", "integrity": "sha512-FLI+bHnNY4UalQUP2CIzRHmuDX8sDj4L73X+Pr1qVTTSDPpo4BYTSppEiT+seAmQPQLFnw+RtEsr3cYRu4zeCA==", "signatures": [{"sig": "MEUCIA7Tjhab2pvBhbRfGXudsPEau4km80416yIUuKKMlONxAiEArnZYae7esjDTmeUphh11xUEZpfTNrD6DteTkG2FiN60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.27": {"name": "nodemailer", "version": "0.3.27", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.19"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "834cb5d29407a863b2e9a95142dddcf92a19da20", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.27.tgz", "integrity": "sha512-bKJVtjTnHRpqKYDH2AEz52C4VEyNhDKXJl7xzq/PzCE++lZS2vA6WrWpIQakcv4phN1TqVcmhGQkHargvcXQRA==", "signatures": [{"sig": "MEQCIBgRhloqQYgSGFodeXRoZXBRksxtfpRMiuzaEkD/i8mhAiA9dUvz7bt6el+VOdd6kebZmSVHk098g0dqKvrupHfY8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.28": {"name": "nodemailer", "version": "0.3.28", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.19"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "f1a59137d260d882602fcf1cb854f83e92f65dee", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.28.tgz", "integrity": "sha512-evStVpO3MbwwZYWjDZVYnKzlbIDKfjY4f6/aWQ/q6CvS2Hn4fxWqm5PwrjyalfgS1B38wPoOXxrWUKjZb7fw/g==", "signatures": [{"sig": "MEUCIQCTa5wk+Q12SP9KADFqI9/qap9dACo66EGHmofHTv4+VgIgT8gsOfTlG6kXy15j59ptoL7Uu5Xb3NOQdb9VOmF9reo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.29": {"name": "nodemailer", "version": "0.3.29", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.19"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "64138bc21cad63e2d1111503aca8f06015507dfc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.29.tgz", "integrity": "sha512-TjJPlY7kP6TtnfzTB1WeuCmdx5sQNWlCbwV5KKzcYiMoVTy3d+iBCmp0wgr7iPdayRrwiBddPLvRsZgHdxvSpw==", "signatures": [{"sig": "MEUCICIxc48X2eOI4PlDi7ZtMKax4xn97mTBQnYvulO7cjnuAiEA+HRcBP52T+kAPK1AUz32Pt+/1YeHylx6tbUroUYAyw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.30": {"name": "nodemailer", "version": "0.3.30", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.19"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "7c54bb8116a89a8cda189d7e7ecb9a4c3afd8b4e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.30.tgz", "integrity": "sha512-6AlkcTeNEcq+PvVe8wWN0vNYHZd/kdDighTMXBNehS/R4m/CBQlLB2VC9zZ2ZTEOfKthPn5/unXqicV1LH0IyQ==", "signatures": [{"sig": "MEYCIQCjtC3Svdn8yK94xr0PFSjMXVUFuaXXgLpHyFZekvr/dwIhALeJa9dq6EwVURXT/u0QIgSkcyZwBiB3QbXQ061n/8Ja", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.31": {"name": "nodemailer", "version": "0.3.31", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.19"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "d29f7c294aaec251ed9709a34031e85f6a0e303b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.31.tgz", "integrity": "sha512-pLDtU6Ev2R9R2AGcT+IrJ6t0qPiUsWJ8QNbf9G7zD8exCqjWcNQpcHwOAFNDS5oAAtjzrGN5QoMuubR+8VTp8w==", "signatures": [{"sig": "MEUCIQCFJU5Y47bfjtJAAhD8D1JncoHBFWUDDK6ar+CKsGChtAIgBsY+qFxft3sBF+RV8bAwL/x85vzy/7AyxJgSB26Pp9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.32": {"name": "nodemailer", "version": "0.3.32", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.19"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "278d71229c803b0179e12a4e5b24c67459f85efe", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.32.tgz", "integrity": "sha512-WvI1wMiYdb+ZEEnzdH/TwNFBgKp8tIj+fqFmNBrtsUQh6i1ScNx/MHXMK8Avoaz2w51co80SS2acFJbMNwW7pg==", "signatures": [{"sig": "MEYCIQDoID/jM1t7S/7iiDcwkbr6Ge4Fox544DyvkGG12LDzmgIhAPfTWMo0tYzQKIuTtoN0pKzY/1DVViw1Xv/eI1NpaMvk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.33": {"name": "nodemailer", "version": "0.3.33", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.23"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "49dcd6465fe0285597e3d5d21950fcaead6061ac", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.33.tgz", "integrity": "sha512-ArgQ1bXNDSTTIdbPEhLOGuyzr2geCnYWCTYH3kFBUzeLzpKvtyxmHLGelgckNeLioI2bvYOG/t9ivTb4yI7xQg==", "signatures": [{"sig": "MEQCIHUlWjjpZFzFENHdapRZlqD2INSE6dN1Out814S2nNf2AiB/8CywUts1oqJjTa8g0J67iG1xlv8EeXP7Rma3higmcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.34": {"name": "nodemailer", "version": "0.3.34", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.25"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "e4b9f9eb85877e159d38f24bcc41b15061c1f944", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.34.tgz", "integrity": "sha512-FaFl7HorPcjTHDuSUi3ja9k1a1QpM4sH+9cKfP70VflsZROtvbflDazLsSyiGkpFNtW3JHGmIul/FOsRtimsng==", "signatures": [{"sig": "MEQCIG+qB4Yoo72GqOfx2l3n0B27EKVoVh/EvN+6KWZ7J3/MAiBQCGaxC5w2WVgr7pm9loh/ycwF7yDkZB2m3PGsDzn5WQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.35": {"name": "nodemailer", "version": "0.3.35", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.22", "mailcomposer": ">= 0.1.27"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "4d38cdc0ad230bdf88cc27d1256ef49fcb422e19", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.35.tgz", "integrity": "sha512-QZ5nHgIShc4kTBVhtAwB0CscuSl9M+NgzXrayfp4uQ1Wstw/2O1R4t6wq+dCesY6WHjQPdI9tIfQ2mor/v8T2w==", "signatures": [{"sig": "MEYCIQDsVug15nctfU1SeHbHeW6L+MaDNt7YEwFKXSUZTsn7AQIhALEBkjcldHQ0ITlmgweSchmFQ+IJ/7yMSD+mnnnZvRFR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.37": {"name": "nodemailer", "version": "0.3.37", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.27"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "d28034198d648ce005ae5b747c61e89c4cf7cd77", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.37.tgz", "integrity": "sha512-sE16sv0Vs7EgV7hMDd1kUGX0I34mozR7dddWmZIwtVDWgw8wOtFWwSJjTLoJUwG37JBdXkab37g0LGueAGzT7g==", "signatures": [{"sig": "MEUCIQCJ4FZJf36MWzV5pYOcwF3M5HOpltRjjOd/IaAcbJJrYwIgHTWBPdqkpDM5y4941o4M4Zf0l0OOvkEr2TVNfNgCqrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.38": {"name": "nodemailer", "version": "0.3.38", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "517331048d1941bc5b706557de15d754aba135e3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.38.tgz", "integrity": "sha512-6s8woWqQRXwv8UlPWgPHp24NkdWadnw+EmMr3BlLmWlrHQm+9wirhY02PRbaiZU86q9YwRo2HonY5BpsspA/6g==", "signatures": [{"sig": "MEUCIQDSYUZEdIpesdY3buvDas8xLf1G74ST3gsEsWJFhKfIvAIgEr+6cgk+j/kOOVPAspJsYk82ZPGVIAcIVPyyKb0QheY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.39": {"name": "nodemailer", "version": "0.3.39", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "ff2967e0e97336e6c77ea833e762ddc105cbc48a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.39.tgz", "integrity": "sha512-qn3UnXiQdAIhKMP3r9fwYihQ4at7yN365D8WV3vpgi4j15nFMoFuAE8z3Onj9V4wEepGJJvqC93hVJgpaMlFhQ==", "signatures": [{"sig": "MEQCIHLWK27rLs1NLXHbDaHGLANEJdeLEWcOMr15kI1KPHkFAiBrht8m1rJdW+a0BDKAv01rU/riwENz14cIKrGoex7m8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.40": {"name": "nodemailer", "version": "0.3.40", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "20e7fc05f60839d88b4c0a88a50da4fb29957489", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.40.tgz", "integrity": "sha512-VXUedUQK0LhyURcmSK7RqT8KrQap9+fm6Z2gTgODMMFEYXzmqBvvnlM+ptCdaA+4L6+kkYZ4FYRgfcnYHQ1frw==", "signatures": [{"sig": "MEUCIGUOhLoStv61ayNY6aXoCyejohyda/Xy3gSKSiD+1haqAiEA8EeOjAjtSbKseCpycwJK/upE3l1jJw5jSg0NzFjQCjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.41": {"name": "nodemailer", "version": "0.3.41", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "c67e2104ef0b519849409c9c2a5a8e3fb3190f45", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.41.tgz", "integrity": "sha512-dMepcu6hsUR6OHev6zHbuhg7e/dTWDTOdInzPuAXqLwPv0aQENkpkx2okQDWTj1gge49bWZ3DRAl6NrGrkOqbQ==", "signatures": [{"sig": "MEUCIQCiZEPZ1WxrK+1eLTGlRub0AgVH7FNawokiU4I4J1GTUwIgSruEE+NnfGjjk3CQH1acihP9xOmKKG59NO9IbFTg/Rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.42": {"name": "nodemailer", "version": "0.3.42", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "1f928331062f8e890f8a0341648c92563381280b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.42.tgz", "integrity": "sha512-MZGvlDvZHuN8N2qR6tsglxXfBWNgskEI12AEIJcGeBPiuRKLu6yYSAHW8lnsucpQt2OCW/h0MjYTkCq4bmTYNg==", "signatures": [{"sig": "MEUCIQCELb5LTQTO1QsjCh/tXc1Wq8wyb+27xK2gOnHCF4LhbgIgKmqOZFZPrIJ3xXvH2Jpw8rw2Sdn7S53kB5TSXBBqVtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.43": {"name": "nodemailer", "version": "0.3.43", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "d8c60db9aba92bbd9d366005a8059dc90b966cdc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.43.tgz", "integrity": "sha512-NDOGRJvsroBvHB8FjYwd0Vs4ohS2pvzMI3S8UFx6VO0I31ZZJtFR9lCJ+nxSrOjIxjGTvIQSWe5zUGSIIzUZrw==", "signatures": [{"sig": "MEQCIHzpzkfiYZBDUxbjEF5pK5KJlenfdsZx1JG3axaLplTOAiAQm33dyjXCMZWt45Up7XHIOm1CfJIY+64WsZThQBo8fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.3.44": {"name": "nodemailer", "version": "0.3.44", "dependencies": {"optimist": "*", "simplesmtp": ">= 0.1.28", "mailcomposer": ">= 0.1.29"}, "devDependencies": {"nodeunit": "*"}, "bin": {"nodemailer": "./bin/nodemailer"}, "dist": {"shasum": "a051dc38cdf3d080ed795896c7d7424a54d990d9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.3.44.tgz", "integrity": "sha512-EuaAP//0D/qLvFWRYaPrfTiwVM2RWxiiHKRHCsZ+jsyBfl9YBqlgLV3whNcUacybB28GFXH5gyw5k+vYAVqQpQ==", "signatures": [{"sig": "MEUCIAHL21PYL11cV1ts9jKpFjRh6AI5Z9hTRW/TsaFPu0peAiEAkgK37rePVmg4CwFFlrr4jo4n9Ko0Hl3DwxOt4lpxmSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.4.0": {"name": "nodemailer", "version": "0.4.0", "dependencies": {"simplesmtp": "~0.2", "mailcomposer": "~0.1"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "79c01bb1b814f9743440eea27f873160003fb462", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.4.0.tgz", "integrity": "sha512-nuuLG9upXH4mUZoYqodFMmu/4na2m/uLQxAj/JS0FSsQ/nD6GoB84NZPOsj7Ss1WGp4yVxG3ZDi07VzpABl7vQ==", "signatures": [{"sig": "MEQCIGKNDEm1DVhB4UxpWMsS1+QyhIYNzF3SldITOI3USlIwAiAwIuoLg1J3acWL+My+riZMF8aUHPIfVMTENp0TNQDIag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.4.1": {"name": "nodemailer", "version": "0.4.1", "dependencies": {"simplesmtp": "~0.2", "mailcomposer": "~0.1"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "e9d62e8a71fc64c0aff556757f9e8a3408b6dde8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.4.1.tgz", "integrity": "sha512-N/t348DjgREWzpj8FBeFq4w/OOZke+rF3LCbgqqpV/48nKZq3u4o+RdHydEFzJkXhzlnP+36/GTo359n66BeZQ==", "signatures": [{"sig": "MEQCICUjTDk3Z0Hzxk4jbH9zjtMctKhPslcNZ7swceuA3/WlAiAxM181xx4P1hAVFgOi/mz7C9HSW2WfT5gW90sxuSn1EQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.4.2": {"name": "nodemailer", "version": "0.4.2", "dependencies": {"simplesmtp": "~0.2", "mailcomposer": "~0.1"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "81161bcbe97439c22417efe8c3b1333518f9a305", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.4.2.tgz", "integrity": "sha512-M+QASYuWe+EfFOWpBSCT7EQTcssrQ1pIB6K72YoY9CLG+NwicDVhoB8HGREdOLplr1/0LG9UzO3r2RNb/tNGpA==", "signatures": [{"sig": "MEYCIQCunzdujSk9Ld+vmi+oPPTEy/gBOxTyHHwoKr6yz96KHgIhALtXOxm1Chr+********************************", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.4.3": {"name": "nodemailer", "version": "0.4.3", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.1"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "4be1be2e15b16cc828e49d10d5283a887847886d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.4.3.tgz", "integrity": "sha512-liJZ0sx0jAN54BaC2B6+bGs+WL8tS5G8hwii5sizh3SeP7FEO5qwkypdzlHlLX7MFusV8pM1rAQI7V/clIjTWw==", "signatures": [{"sig": "MEUCIQDCHaON296vFs3c1ttW5KWTonk/sEwzocvkNv8k4Slo2gIgILVIR4QqXxfr1+INmRndKhgED03NMdUSBC1ak5kY3UQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.4.4": {"name": "nodemailer", "version": "0.4.4", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.1"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "cfcb0c144f74dcf4ab33785020d82076ffb90572", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.4.4.tgz", "integrity": "sha512-/A/shQ7UpaWcJm97r5z9C2Ab31EuCOlCfPMmod8f0+raq96bhZE86p2l3+Figjw4rliM457/vNw/+tNiu1VxXQ==", "signatures": [{"sig": "MEQCICBNBXtNCzTH5pnhEOob1q6MELKlqmCw5BEyOuyR7NC2AiAixjCSKnj4AFqUd32hBr7AxU+eq7uhTeNcsI8v0QXxlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.0": {"name": "nodemailer", "version": "0.5.0", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "873d41fce60b4281cdd31d9e92d24677b92ac547", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.0.tgz", "integrity": "sha512-gqB7baoWPd9LvEq9EnMC9axJ6de4TJ2C920EVuDZU+cko6SrvQ8qG8M3l6e4bZkn+rgjubXvxjLHmEmU/aTsRQ==", "signatures": [{"sig": "MEQCICny92eSCP/Nis5eAwCVWGdxVnwSQsKdyCqtFNzSayrAAiBGbwWjlk6WOKaV2xPEMvwyR2KaPrrW2bKIq37KuKmALQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.1": {"name": "nodemailer", "version": "0.5.1", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "6ce6d9b4dd4594d0d4cf2f8b43c544c4c177af0b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.1.tgz", "integrity": "sha512-NADl3LDdK5pZyLi2wfkSDZfT2cq+wevAtnxNG3N/PP3M0v9XiZEtRvS80yVDwLBVWG3nVR45HeQX2mZtmjj4jw==", "signatures": [{"sig": "MEYCIQCTl9OQbD8OB5/lBRLsuCThtEcK2s4rEdQiD4/6l6MviAIhAIm7YCcQRjP5FxCMXMseTE13Vngps5cAPdPXYpcw9jSE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.2": {"name": "nodemailer", "version": "0.5.2", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "1c2fb72e5744469016495d296fb9da3d3adfe2ae", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.2.tgz", "integrity": "sha512-bcLFxM8NlaImBvEGdsyXAtNI/Ykxs2EnURuR/vva1k6WzGxR1f8dqnyTPyyo+zGwllsxz1HBhC4udyAeeX6XEA==", "signatures": [{"sig": "MEUCIAr4UpizbYAaaOSMs/9mACm2fLLSXBH8wcbl70E+HQpXAiEAlR9VPNSj4TUWeiYaIEWaeakMjjenxxCc3x26DpCuEzo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.3": {"name": "nodemailer", "version": "0.5.3", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "f1de09bc4fc91e92e59318370d80714f479eeb97", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.3.tgz", "integrity": "sha512-GPfEY2Kt3gfprLPJ/UZRLv5SEomNxN0rq+XyhrbN3pfY9M66gICRz/O0L2joSt3t9Xdw6rKEVpUS8SIwJWKptQ==", "signatures": [{"sig": "MEUCIHuPhBwRnbsmStH6LIF9DvCE/OZ3qc/Hei/iL82EI0z+AiEAyKrBK6jJNZruPX4u9k5WtzgzSfL45Fjs/M92D+zHTc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.4": {"name": "nodemailer", "version": "0.5.4", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "4ed49212c3a16801187db2bc603a5bd3e17b99a2", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.4.tgz", "integrity": "sha512-NKC5H3wHQgmaDhEd+RmQ0Sq+sujCJkFf5rowqgpnGysIG+EeQ2AxK1QJ/1ipSbL/nIeZZlrwwhy6VMN617HURQ==", "signatures": [{"sig": "MEUCIDJa16GIejRBMN/Wod7H4xBhTK7YSfMp8Af+jUjadXTBAiEAwSHbybVmT7iYO/JZ5tponklBTeroWtQo32cJBwaJOUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.5": {"name": "nodemailer", "version": "0.5.5", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.3"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "541cb98cd2c650c79c3f9126db19e3eaee67cf52", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.5.tgz", "integrity": "sha512-S<PERSON>rhuCavXI93M65TaE9FFECDcc2c/iHjHymL4Jh4YUwJsk70Vrbq5YjwVANOweKw+ajZGw36GCMOWs7rh3znhA==", "signatures": [{"sig": "MEQCICCR8KuP912xx1eiM75fAGzHUcIrkQHOi3J6M3NtWgWEAiBVNzpwpzYWHreFXJio7BKlUq5BfTiK/psdN4VsNc4qOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.6": {"name": "nodemailer", "version": "0.5.6", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.4"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "350ebf981b262ebd04004573933cd5a2fbdad066", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.6.tgz", "integrity": "sha512-4NipaWIaFoOK94wZ/4BDS4CXEh7FNAqFwmBquZ17lgeZBOEnh5rnbpHKIuvDktZ+/Ag0OEzfYLJYzwen6s1Qpw==", "signatures": [{"sig": "MEQCIFRW39nI72oupfibcluI2oNdUIkJZ2xXcCuYqYQewgpVAiBiR3CpyoyCEkeRXMVOwh0vYEQgT6g3k5HXcYkZQtO1yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.7": {"name": "nodemailer", "version": "0.5.7", "dependencies": {"simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.4", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "9d87caa0b2b2dc97700b0cd8673b60f7abd61108", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.7.tgz", "integrity": "sha512-J2MKKCAVmlexNEPpDm8yj7T4EGB3roCI6sPyb+3gwxDcpAQJDwS98P0lgjiK1yOvMIai3G+CWYqoSAEJk+sAAg==", "signatures": [{"sig": "MEYCIQDea9yRTXJgaBs6DXK00P1IXzLJFxQ4qnwVEOPg6cfZEwIhAJ/G9pZZVK/vQp6LvjgyshDhjkw6sntXsWBnIuBY18dq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.8": {"name": "nodemailer", "version": "0.5.8", "dependencies": {"directmail": "~0.1.0", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.4", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "bca32023bcb33d90c837314e112f56ac17908556", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.8.tgz", "integrity": "sha512-2BG5htu/bVHE2u7OK9p6y9SeK7PKxIUeKulzZIMnNmfZymNu/C6O1acmQtLRdf34eIGUCRpGniFX46xANt+4MQ==", "signatures": [{"sig": "MEYCIQDPX1sSbRLCZ+4AGj7tTZTaTsY+jgivfo/yDWYjW4P2RAIhAJArsZEdhaPRO8yWv+YhFNqFT1lwFn5HoOt2XOebm69O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.9": {"name": "nodemailer", "version": "0.5.9", "dependencies": {"directmail": "~0.1.1", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.4", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "85d2ae0baf78e96c0ed90d1437b386ee3768b873", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.9.tgz", "integrity": "sha512-1pUXz/IbdZPCXDDtr9HYcHnZYYDEn1g1AJ6pu1M6KChrsi59YoaOgUka4m7q2uCg4DKfotBHZAh0ts+S+U7+aw==", "signatures": [{"sig": "MEQCIC5fCudjUDuuvbO6oeh/qCLlBMGsDMNe7NDhGU9CkeuZAiB1/XPRFN4+ce/vGFo4Z/8CWIXlQuCTqrBfysvWF75hgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.10": {"name": "nodemailer", "version": "0.5.10", "dependencies": {"directmail": "~0.1.1", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.4", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "ad0dbb4b872c8e0307114dd0c518beb313ad9baf", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.10.tgz", "integrity": "sha512-8vZ4FuNKZp6xJ+tuDfFgmh+y0Z4NXIxC7ZxZ/FVIuEe1nlCygrD4de4l3tfrfhQoplyu924GI6h0Df9FPsQtxQ==", "signatures": [{"sig": "MEUCIBFJ4uj0p5rYVwAaT5Re2YqNkUXOnV89hwlhHDclEKCcAiEAjYXpvaqb5kbv4fJZk+qmnRKe6eouv2A46ekQJ+2C6nM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.11": {"name": "nodemailer", "version": "0.5.11", "dependencies": {"directmail": "~0.1.1", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.5", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "4f43876d6f80c69098884562a381e30006d1f392", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.11.tgz", "integrity": "sha512-oRb94LXvyyI9RPQxKPhUfQvSDLeqvHinMufQYvCBLePd83bs2hTWI0O1HQK7AW0CFCDH9RT051Q9nYwK3BVEeg==", "signatures": [{"sig": "MEQCIEaZUVp7By+us42rl+38Zf5f/2BpUlFC9zBtN6dWDby4AiBJOHRiL9Esz1el8HcJp6IVySmB5my8kcZFxsxIoyTLjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.12": {"name": "nodemailer", "version": "0.5.12", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.1", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.5", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "b23592e20bfd4c26c2dfb993886c36872fe33347", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.12.tgz", "integrity": "sha512-USvRvghrxEu1BMVczk5RSzO6vLRd/hle94JjJPzFgorkq464l8GjfQYUihXVVGGzJBjCTaZCog3vahFX2Sko8A==", "signatures": [{"sig": "MEUCIQDPBkP/8bZ6drkp+6oLbR8QmlOlbzTP0W4dKkvnszT4ZgIgUv8cw4qnn8n5bBxLTuIPjJdTd5yZUF/uGuvCYaxCM4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.13": {"name": "nodemailer", "version": "0.5.13", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.1", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.5", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "a498c9fc875f72b400cf7c193b0c234f81467b3b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.13.tgz", "integrity": "sha512-MQ6al8LqFcaB0LQH3XjDevymJCvRmrqrlVMWs6JfGEHJSvmlDmU7QSbA3zCVprjn/iS8aswdc0U/Rjo2uuFjMg==", "signatures": [{"sig": "MEQCIB+gkqzjbe3IJY2cuqp0aczrr/Q6c9YYJwkK52zmAu9vAiAnYt6ajF2ZTH49nDniADcac6/JP9spp5jglv+dnxzq1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.14": {"name": "nodemailer", "version": "0.5.14", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.6", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.5", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "f1a162b5b7b769672e65e5f5f2a87d9e998055ab", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.14.tgz", "integrity": "sha512-ILrNwqsRpgXGCp67rs4SOKhjUjd3VFkJK8vpaARCSIHmR2lyBL7l5CnO6pkYHdhqkyUdQ5tic/p5+htmWHoXsg==", "signatures": [{"sig": "MEUCIQDcm8Noh0grSPQqdl3sDaAVwe0zM+XWRn6LFrUS8B69FgIgBFjhdK4SMMN0cEtsP7WyBRjtdt5xQJfHNJ0WMW7b0zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.5.15": {"name": "nodemailer", "version": "0.5.15", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.6", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.5", "public-address": "~0.1.0", "readable-stream": "*"}, "optionalDependencies": {"readable-stream": "*"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "2136c016ec3b099bbc4117f2401276bb1718c02a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.5.15.tgz", "integrity": "sha512-sXajUKGDp3Yv+5jG/neoFlYJ4fuwgzJuBaCv6S0di3Iup+UzAQL7Uj0KlHEsjOm/Oa1PBkDr6K2q/iouwTkF4Q==", "signatures": [{"sig": "MEUCIQCnsRK+tnzYeEr9tmBLyeNUjQ+1svFbbW24cVzGLaP1PQIgWt+K0bqPPwKyo2B0Gg3dAR9YdUpbr2O9RjH3Ri4nCZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.6.0": {"name": "nodemailer", "version": "0.6.0", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.6", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.7", "public-address": "~0.1.0", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "230620f83b0186305ce1970db656838e04ec30df", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.6.0.tgz", "integrity": "sha512-uCQDs8JLByTi5IysoR4p19bEM+DklpRXIgkcfarRRDmORWtyMMgaG5rMZ/DyTDY2JMv0wpN/yFVuitJx2xeC9Q==", "signatures": [{"sig": "MEYCIQCAsqDTJ3h3a6zn+IUCaHYG2B+u3KT3dheqRNiS6XJ8XAIhAKz3MOufnvnWnwsPDo2K8r7nIP530ZQCA/dSR3ZBNe4d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.6.1": {"name": "nodemailer", "version": "0.6.1", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.6", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.7", "public-address": "~0.1.0", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "3be514ec9ecce3ff38455b4de7d81be5a59de857", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.6.1.tgz", "integrity": "sha512-kyVWG41QPX3jDg+P+5db2BKTWr7/YSq3rCTmoBga9ZHoS7+kncuEePWlVdQZ6QWmuE+klBgcUpneq12UV01Dzg==", "signatures": [{"sig": "MEUCIDCQZu/aTsWEJ3gIg0I4HTYXTxV2pROV/qG2hSdMgPebAiEAzDz9GSeSgen9Jfvp6rdzKN4A3ABkw3/1Y79RlwMowtE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.6.2": {"name": "nodemailer", "version": "0.6.2", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.6", "simplesmtp": "~0.2 || ~0.3", "mailcomposer": "~0.2.7", "public-address": "~0.1.0", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "1c1477d307199916ef6725c6470ca8061617e74b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.6.2.tgz", "integrity": "sha512-VZfbTHRCxur0Ug/PupDeBEGtQNsLIE2GNLnhsfsvatA8gX7O7w5ebWpTMFhlIvoip3QPG7bZ3KgwD4u9/QtNRA==", "signatures": [{"sig": "MEQCIDmQxztdhYkVvnHWjhFhRGbg9hbB6hLni4P3eMIO/H6zAiBCQAqIH23V/5QuuLAjO7KsZbgac0OdhwcvwdxYSDHM/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.6.3": {"name": "nodemailer", "version": "0.6.3", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.6", "simplesmtp": "~0.2 || ^0.3.25", "mailcomposer": "~0.2.7", "public-address": "~0.1.0", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "0b7aabd1df9b94ed03f2366ec588edd0cc6ee365", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.6.3.tgz", "integrity": "sha512-gJmE9g6db+9Gd0h7oIKYGz+xeFBV1QnAyhQXPAi+WCiaflCDPCsr8RvTpgiEhXnA6v7RAdLqx46nXQifcnB+eA==", "signatures": [{"sig": "MEUCIQDe4y4Udbnnr4MttJAhegDhoG/bxuz1lxXvI94BBHO15wIgSMxjOoe0N5cm+LslehNadbXzbpuDjL9DevlmHEIFgxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.6.4": {"name": "nodemailer", "version": "0.6.4", "dependencies": {"he": "^0.3.6", "directmail": "^0.1.7", "simplesmtp": "~0.2 || ^0.3.30", "mailcomposer": "^0.2.10", "public-address": "^0.1.1", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "9bf537009afee8cad85b2a2bd55e6aa8212403f7", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.6.4.tgz", "integrity": "sha512-Ala7iJimKOu2NqSGLUfpcISy55BRFduSh2DcPyYILp/jHfV63XD0wPZ8QBr86K+/1tRPtnnFfI8oWSGKpo3Lzw==", "signatures": [{"sig": "MEYCIQCxn7QhnMfg6lgiCx4X/vjNOJbeDteWBIG+K8zjkxjMtwIhAL19J1NyD4DQ48ndE9YR8BNJXZEdBhSZpmEEx65xOge4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.6.5": {"name": "nodemailer", "version": "0.6.5", "dependencies": {"he": "~0.3.6", "directmail": "~0.1.7", "simplesmtp": "~0.2 || ~0.3.30", "mailcomposer": "~0.2.10", "public-address": "~0.1.1", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "d7ccae72459a944cb97dd6b75ee0376270d5f57c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.6.5.tgz", "integrity": "sha512-3vBPHgwDMCh5okVB5CEqYh8AAL32UxfVnwRRb63PY8iyNSjvnxbIjKNeDmE5o6LE3LxgxYPwDbpxYUYGZAJemw==", "signatures": [{"sig": "MEUCIC25JZpssg3Ac6qzsDXwN0KYlrnBIH/s1zzSIjrsASX3AiEAzuLOEJmm5IEixgcgRo/iz2aBFMyemgF09BfJV7ccTE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.7.0": {"name": "nodemailer", "version": "0.7.0", "dependencies": {"he": "~0.3.6", "aws-sdk": "2.0.0-rc.20", "directmail": "~0.1.7", "simplesmtp": "~0.2 || ~0.3.30", "mailcomposer": "~0.2.10", "public-address": "~0.1.1", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "6f058ac5330e8caba1e0d8626615bf206149503d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.7.0.tgz", "integrity": "sha512-mMTF55tgMfdzSC247EtRUtUFWfYD9gfpCfKlviefYF1myGeWQgLKifXVV91LFh5llJaAvs1JHevVTV0AfUb/Dg==", "signatures": [{"sig": "MEUCIAg+hQsli2prImuoOr7KHZJTrGt2aBG7UPnmLe7Ig2H5AiEAln+GcSI4ce//K6W3ZVOD4m980O0dwAufBQMIy0oqgSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "0.7.1": {"name": "nodemailer", "version": "0.7.1", "dependencies": {"he": "~0.3.6", "aws-sdk": "2.0.5", "directmail": "~0.1.7", "simplesmtp": "~0.2 || ~0.3.30", "mailcomposer": "~0.2.10", "public-address": "~0.1.1", "readable-stream": "~1.1.9"}, "optionalDependencies": {"readable-stream": "~1.1.9"}, "devDependencies": {"nodeunit": "*"}, "dist": {"shasum": "1ec819e243622300a00abe746cb5d3389c0f316c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-0.7.1.tgz", "integrity": "sha512-LQJ5KHEcuqQugKghk+jOKleihUa8Mf2ovpJR2oJX5ZSkhbjkhVWQd3KGPTQkZnBpaPPis5F8pN5DVVKtzeN+2Q==", "signatures": [{"sig": "MEUCIQD92eg4a7UmsQNJXxVFObisEctFGrqZFK+ngcEA+6rGcQIgVyEJle/u6ZS2O6IK0Y+vReeQwAGiAYyXiUSaaDYskAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.0.0-rc.1": {"name": "nodemailer", "version": "1.0.0-rc.1", "dependencies": {"libmime": "^0.1.2", "buildmail": "^0.1.11", "nodemailer-smtp-transport": "^0.1.8", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "f9b6582337b8ef3358de958f0ed26ec65aca6c96", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.0.0-rc.1.tgz", "integrity": "sha512-fhEKOBXgKiaXnDv9EYtwLT79EPta4+JJi6k0KRxWW7wpOdWjeiURRwEeubKZ6JJYCRrxiQIrEZfhbqdSOMjvhg==", "signatures": [{"sig": "MEQCIEv/4cmaJlXJraoP3/YllqP+ZCFwVUXd3XIFImRgysWQAiB1zyQ9tm7qD2oFrEuhjiha5STnJi6Pnvk3m8A2od4/wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "nodemailer", "version": "1.0.0", "dependencies": {"libmime": "^0.1.2", "buildmail": "^0.1.11", "nodemailer-smtp-transport": "^0.1.8", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "a14aa7fac3eff71a0a750717091c942d4c881def", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.0.0.tgz", "integrity": "sha512-JvAjeF6/MFrDgJoP5qv7RlZtXiSHZaE0OJaiFjINKaGFBFNGlK7nutVt4n6h51jjDWQvemVnKyfEyWerJq+k9A==", "signatures": [{"sig": "MEYCIQDDCK4duv54Pqz2wlpxHO5jHhJC6Jv2S1IL9/JPI+xDKgIhALvbJT0Cn4yxWKvyp/9v/16B/ACK9FaXmF+aUQdO6jB0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.0.1": {"name": "nodemailer", "version": "1.0.1", "dependencies": {"libmime": "^0.1.2", "buildmail": "^0.1.11", "nodemailer-smtp-transport": "^0.1.8", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "70a11a1b05bc9e848438ba701191295ade542073", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.0.1.tgz", "integrity": "sha512-GVNy/Y1n2iZTohljxdcmNELsK1Rcboy+O6Z46Er1dqHwweklbsVF7adf1c0fHFIHUkUQAj3cpzxPjTN8p+qiwA==", "signatures": [{"sig": "MEQCIC2XLCO8pUkwOkVScnZWbS9azWQaihkLXSjT5/wiftmtAiAaKaESucgFNA5TOlUmehxkvEM/eP5AuDXpR9dWG+bqFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.0.2": {"name": "nodemailer", "version": "1.0.2", "dependencies": {"libmime": "^0.1.2", "buildmail": "^0.1.11", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "423b01ca0c563a15dae57bc33463c37edc1bf5f7", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.0.2.tgz", "integrity": "sha512-pdEOKh92wIfiJj5NYVtyohyGqP9+skRwd6lyxrPfjSOkNyhaPVoBhYEYFBxKotMg3uq9xqfZbMNa5iJ0wWlNbg==", "signatures": [{"sig": "MEUCIEMB1jUBuOkR3BFRb6c5UBIzPB68nzXwuroY8n9LpZgTAiEAs7fxEuIf9Ggw/yMymNTqkZvcdAItM23RYeWTGjrsOO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.0.3": {"name": "nodemailer", "version": "1.0.3", "dependencies": {"libmime": "^0.1.2", "buildmail": "^0.1.11", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "ff8c5706510b3e307aaad47a629881a60c56406c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.0.3.tgz", "integrity": "sha512-s0buBg+Bnxx3eCE1GSeLJCRaNsN3ceNHY1tNabjF0tHoE2c9kA/ACdy0IkHpGVvhO1mnHJJ6V5KFWfSzrr9kyg==", "signatures": [{"sig": "MEUCIFUbEAMAfnFIvRU4zYqO2ftyuHo0H+HUtnSXALyaLGMTAiEAsu/XX6vJ8FFDahB5HYqsScc1IKc9xjS9pDDemAKSDHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.0.4": {"name": "nodemailer", "version": "1.0.4", "dependencies": {"libmime": "^0.1.2", "buildmail": "^0.1.11", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "a4e6c092f96364aa1e2f8c70005dad9c98097cef", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.0.4.tgz", "integrity": "sha512-VvC+zhfv5lGRWPus09ft8lsp+SDPFBT5UmBiPEslRRAL9a1tfNlCmVdz0L763qtgDiabJW0gUpr8QdBAsOXGWQ==", "signatures": [{"sig": "MEYCIQDZkbYFp7ArTVou5u3OtaEO4KOYAwDxfG4WfxgEaJpqowIhAOT8behNaTcImWzqPFrkA4mMkZKw7f6baON0VTn1UCxE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.1.0": {"name": "nodemailer", "version": "1.1.0", "dependencies": {"libmime": "^0.1.2", "buildmail": "^1.1.0", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^0.1.1"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "a56e62b470072a0cbb528a7033693cb0c493ae3a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.1.0.tgz", "integrity": "sha512-3Ve+qNs7Sxk1RGavqOramCOV3YFhD+U8VfR8KJ1vj+PsTBsTBkTqEaXaSj3XvYc3mJ6ICqI0a90520xKSxioJw==", "signatures": [{"sig": "MEUCIQCHxO7pH8FZwG4aDA5v/Kmtx0WkNt4zWMZaibBghI+qJAIgS2XYt8jW6HypsPp4s6sAQkLlwrnklAwo/xXWEUQ3ysQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.1.1": {"name": "nodemailer", "version": "1.1.1", "dependencies": {"libmime": "^0.1.2", "buildmail": "^1.1.0", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^1.0.0"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "73a7b45a4b92f40c1a304baf92d3b78df146fa1f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.1.1.tgz", "integrity": "sha512-C<PERSON>eynwanAQdu/gsU77L7kL9ON3f9xcQZb/35fryjQq0hlG5DG8nJGGGNZAbv8JrMSCU8awpDDtn+sREV+I1Ujw==", "signatures": [{"sig": "MEQCIFM1sScfLCKO/vAzILmZ6L0sRCZGAWTMFulxOYfB2JlKAiANSVrqay3zLeYmG559Byh8eEbwmDBd4LlXZFC5u/5EaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.1.2": {"name": "nodemailer", "version": "1.1.2", "dependencies": {"libmime": "^0.1.2", "buildmail": "^1.1.0", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^1.0.0"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "54eb58035f2af13ca099f13edfd0621ffd7009c4", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.1.2.tgz", "integrity": "sha512-UBRCeHBbsXogRp3gcWqi1nwYqPBD3ZiapRbesJog0XpfKKGfzIENloAjxy9ZGV1BAOsEjMPdhKozZhJbA6q8sw==", "signatures": [{"sig": "MEYCIQCxKCnmRxXEFMakjF4GtpunlYS7Hqci127jOrY/+FYBSAIhAPcSJcOOzMRsr1GUHed/aLB8Vg8/nHYmM+jPfv8u3k47", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.2.0": {"name": "nodemailer", "version": "1.2.0", "dependencies": {"libmime": "^0.1.2", "buildmail": "^1.1.0", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^1.0.0"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "be026387a898663c4c18b73db6b718670390853a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.2.0.tgz", "integrity": "sha512-Q8ijt7pBAv+WnHuOdSU5gmAIV3mhbZ/LV+DmwX7x4yXZQpGiUCa1YeTLzIklnHY5W/DJaLVCNAEmR5Y+ixN0Tg==", "signatures": [{"sig": "MEQCIGwfWHZEVUtZsiO/V9V9TOaQW1UnAw7Euohq7923bUBXAiB0+ZJovmCG72JO1Wz8/vEh/KI7CUynIP0yq/97s/2DzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.2.1": {"name": "nodemailer", "version": "1.2.1", "dependencies": {"libmime": "^0.1.3", "buildmail": "^1.1.1", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^1.0.0"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "6ee77d1f07515454901177bb3584323041239f7b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.2.1.tgz", "integrity": "sha512-Ply4Ts9oKYrmLZg6YBp/2XwODZD3eAY6SVGGrFiSyuTUDY2hmaEqOqGkdmsnJnSp10D5RPB5v/rsp2JU5BPEgA==", "signatures": [{"sig": "MEYCIQCHI+WZWsFijftMFdkN2jqsI1Uy2nNLWaU6hUnxXtHKDwIhAOiaLjz/trEuHBUmUcUbt2Qm4Dj9KkEDDvj/zMdk7UR+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.2.2": {"name": "nodemailer", "version": "1.2.2", "dependencies": {"libmime": "^0.1.3", "buildmail": "^1.1.1", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.11", "nodemailer-direct-transport": "^1.0.0"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "0109b4d5d7c93f2bab3542f0c40dafbad95331ad", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.2.2.tgz", "integrity": "sha512-NpbzYJCEhC97i+XkRG3JJax8naJ/EsRcYikC8RIqlk/YY9fO6ud9Do/XSsfq2YGcOq+uMXH7XOE1Q37OQjqwMg==", "signatures": [{"sig": "MEUCIQDjLeFhF1f5uDUlKQKxQnjKA8mPRB+pZsPPZKxPquz79QIgN81MjOP7ibK9XqqsP9+Hztt1Sei3N5+PPdGYIQmPU54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.3.0": {"name": "nodemailer", "version": "1.3.0", "dependencies": {"libmime": "^0.1.5", "buildmail": "^1.2.0", "hyperquest": "^0.3.0", "nodemailer-smtp-transport": "^0.1.12", "nodemailer-direct-transport": "^1.0.0"}, "devDependencies": {"chai": "~1.8.1", "grunt": "~0.4.1", "sinon": "^1.9.0", "simplesmtp": "^0.3.32", "nodemailer-dkim": "^0.1.3", "grunt-mocha-test": "~0.10.0", "nodemailer-markdown": "^0.1.1", "grunt-contrib-jshint": "~0.8.0", "nodemailer-stub-transport": "^0.1.3"}, "dist": {"shasum": "a6e4c507b2f776a03d46e9be8d40b5be916f290a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.3.0.tgz", "integrity": "sha512-pwkEFfvRh0r39j/Pe851uq49437Hc9F3Ycaarjyx4XPiqG85R4QS+pAp4VbSQ/T3YujKstio9nJzaxCIGOpcQw==", "signatures": [{"sig": "MEYCIQDiBYmS74dmrFqtSGxiSuCPVVQF2X2kbEOuzK6OZ9ui0wIhANehrAn2S7PqLh9BE5ubblnTtyPBV2UY7/ANuqycUwXV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.3.1": {"name": "nodemailer", "version": "1.3.1", "dependencies": {"libmime": "^0.1.7", "buildmail": "^1.2.0", "hyperquest": "^1.0.1", "nodemailer-smtp-transport": "^1.0.0", "nodemailer-direct-transport": "^1.0.1"}, "devDependencies": {"chai": "~2.0.0", "grunt": "~0.4.5", "sinon": "^1.12.2", "simplesmtp": "^0.3.35", "nodemailer-dkim": "^1.0.2", "grunt-mocha-test": "~0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "~0.11.0", "nodemailer-stub-transport": "^0.1.5"}, "dist": {"shasum": "511c1ddfae5bac67e63a443a5d4e3bcaed32130f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.3.1.tgz", "integrity": "sha512-SSi/m4J8oXi2Eqyq3dJW3PSRGtAXu2hh0JfFj51NuBT3iMpD4DvAIGD+JElYmU0Sm37BeoUBLgn9HKKr0Trc5A==", "signatures": [{"sig": "MEUCIDILRJOs1nO2/mfsDAsRqwKE7XmdozNTUzAzsEZR3a+cAiEA3aXZw3ldZ+lbT5skfKy6PbcKJwHHGK6ZVNmFbo/XtU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.3.2": {"name": "nodemailer", "version": "1.3.2", "dependencies": {"libmime": "^0.1.7", "buildmail": "^1.2.1", "hyperquest": "^1.0.1", "nodemailer-smtp-transport": "^1.0.2", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^2.1.1", "grunt": "^0.4.5", "sinon": "^1.13.0", "smtp-server": "^1.1.0", "nodemailer-dkim": "^1.0.2", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.0", "nodemailer-stub-transport": "^0.1.5"}, "dist": {"shasum": "07ef85cb219362b920acf712c73093ff2b29e15c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.3.2.tgz", "integrity": "sha512-uHztmRShrmb/fuqqS7+/JoppX0VU78OWXbPONpyG8d7lWzOBq7bNosD69az9M5KVv4tUzw3zlvwkCEr3y6s9OQ==", "signatures": [{"sig": "MEYCIQCrF34xL7ku4r9tywH9hKyDnNG/9/jcrVaNie2izA2x7gIhAOSWqpYNZdE6Rr/IdzY4LqWiBrhnpLR5KWYT+AlN3daN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.3.3": {"name": "nodemailer", "version": "1.3.3", "dependencies": {"libmime": "^1.0.0", "buildmail": "^1.2.3", "hyperquest": "^1.2.0", "nodemailer-smtp-transport": "^1.0.2", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^2.2.0", "grunt": "^0.4.5", "sinon": "^1.14.1", "smtp-server": "^1.2.0", "nodemailer-dkim": "^1.0.2", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.1", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "ea9908757621fa90f0d2335acdd00bb8df0cb26a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.3.3.tgz", "integrity": "sha512-6b5bCku40fod4SQw674t2pP8TbScRgcgylj+p5mlA6XzTaECC462FPKOc1RSP48tRDyfaXfkA01rKbGnNlj5rw==", "signatures": [{"sig": "MEQCIGWg9/fTcLswwXS8/uc15haNm0kQTSLjfEjradK2DFwoAiA81RHBS51gQXo6rgCSPPg+X4QtmQJ4GkDsYLAufadPqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.3.4": {"name": "nodemailer", "version": "1.3.4", "dependencies": {"libmime": "^1.0.0", "buildmail": "^1.2.4", "hyperquest": "^1.2.0", "nodemailer-smtp-transport": "^1.0.2", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^2.2.0", "grunt": "^0.4.5", "sinon": "^1.14.1", "smtp-server": "^1.2.0", "nodemailer-dkim": "^1.0.2", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.1", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "9e5ca29ddc9fd78584e5ed46ddc486385be43ae3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.3.4.tgz", "integrity": "sha512-PBIeRUDC0x+meGaS+IDKMD1/vYunTbgboOzEceYji8ONg6mfxL2JIZb/ru6GWZPdxK7HfuzXbxtQV/mFHhTJpw==", "signatures": [{"sig": "MEQCIADpaaeSvULxLBcD0rU0khym2FoNwPn73rCwqFflSk8qAiBG3LKmnwhkxZCCbLECBV/CqcMCzrDRTUGyARjqMy9NfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.4.0": {"name": "nodemailer", "version": "1.4.0", "dependencies": {"libmime": "^1.0.0", "buildmail": "^1.2.4", "hyperquest": "^1.2.0", "nodemailer-smtp-transport": "^1.0.3", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^3.0.0", "grunt": "^0.4.5", "sinon": "^1.15.4", "smtp-server": "^1.4.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.2", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "ef9d0e1611ec45039e64e4ddb9db1512642a8d42", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.4.0.tgz", "integrity": "sha512-1HIFN84gGHevmIrsF6Qxrojo6tjHe4O9a6q/vOL8MjWh2ebgfCFSm6TQrt/IvLEjp616R/7vWDSAhBVnTmBiOQ==", "signatures": [{"sig": "MEUCIQDYjXCK8FM5uIRwfsw2mLJ5Qfg1g2TPRaCJXv9g+A+VxAIgF1vL58jTI5uVvgRhxoqLUkc4zxOkwn0i77edkDGUsGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.5.0": {"name": "nodemailer", "version": "1.5.0", "dependencies": {"libmime": "^1.1.0", "hyperquest": "^1.2.0", "mailcomposer": "^1.1.0", "nodemailer-smtp-transport": "^1.0.3", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^3.3.0", "grunt": "^0.4.5", "mocha": "^2.3.3", "sinon": "^1.17.0", "smtp-server": "^1.5.2", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "9617c078c850ce8461c583a55e5386033532f0f3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.5.0.tgz", "integrity": "sha512-REkWtS1iVL5OJqFt8CzJl2f+Eqj+nlaHJzqG+4yo1f7zdQ6zbvkS8jqjTkUlrYd62vYBnadUTcV7iufe6uR6qw==", "signatures": [{"sig": "MEQCIFy9v2WjCw05FcELFsglQTR2irlreauc2lUskrm5rNKhAiArSDe3glZHVTEzMYWT8JyAR6XpUMk+DZxKW82l8FzB4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.6.0": {"name": "nodemailer", "version": "1.6.0", "dependencies": {"libmime": "^1.2.0", "hyperquest": "^1.2.0", "mailcomposer": "^1.2.0", "nodemailer-smtp-transport": "^1.0.3", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^3.3.0", "grunt": "^0.4.5", "mocha": "^2.3.3", "sinon": "^1.17.1", "smtp-server": "^1.6.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "0106a6a18d45e463981fecc8b61b11b58ff93c0b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.6.0.tgz", "integrity": "sha512-L7GGdOptRWXzRdpvPmizUfZYpWLhqW+9O3cNM2OabMhQShz6Sd26YjcG2Ws+xFfy2n3HEyNtZ12OEyuGqIt+Nw==", "signatures": [{"sig": "MEUCIHLwPitUo2530kh0EQDPZzbaOO4IdHAL/2sAeggYS/OwAiEAjuBgCdYKr4mh2WDJhgYY0wMHUQnp6E8VMVU9ZvnSgVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.7.0": {"name": "nodemailer", "version": "1.7.0", "dependencies": {"needle": "^0.10.0", "libmime": "^1.2.0", "mailcomposer": "^2.0.0", "nodemailer-smtp-transport": "^1.0.3", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^3.3.0", "grunt": "^0.4.5", "mocha": "^2.3.3", "sinon": "^1.17.1", "smtp-server": "^1.6.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "4ad13559299dd8d3c70b94c1238cfda94038820f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.7.0.tgz", "integrity": "sha512-V16s1kRRUibs8P74JPA4Codd7/FKc3O+dnVWwI4jTx1vIUG1RvEA7p9in5KXRqyop0MfDapE11I0gp91SOI82Q==", "signatures": [{"sig": "MEUCICu8TYZEcCG6QoT9vgyovgsjnD5p19Hwb0mHzst4I/kiAiEA+O2Y8zEpC3cArUOK+2grCYe+TnKOkMmSvrB3mtCfVBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.8.0": {"name": "nodemailer", "version": "1.8.0", "dependencies": {"needle": "^0.10.0", "libmime": "^1.2.0", "mailcomposer": "^2.0.0", "nodemailer-smtp-transport": "^1.0.3", "nodemailer-direct-transport": "^1.0.2"}, "devDependencies": {"chai": "^3.3.0", "grunt": "^0.4.5", "mocha": "^2.3.3", "sinon": "^1.17.1", "smtp-server": "^1.6.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "d9c84b0714afe2378a8d7504bac11e946f87251a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.8.0.tgz", "integrity": "sha512-S4DmazSLeiO2CdfMx8uE9PPmyP9QcEYKU5LHu1xnUL4jbT2xDrZ3rZdlUXNFhJVx/BnjJzgAwWF9PWJVVFmIyA==", "signatures": [{"sig": "MEQCIHkm28kqH8fi8KCIA1KHq0q89/KnjZumuJ4z6m/m8dMoAiASent/REKL7B5S5O+BxvrbvAEBlCaYGSGN3LGm2s6LDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.9.0": {"name": "nodemailer", "version": "1.9.0", "dependencies": {"needle": "^0.11.0", "libmime": "^1.2.0", "mailcomposer": "^2.1.0", "nodemailer-smtp-transport": "^1.0.4", "nodemailer-direct-transport": "^1.1.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.3", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "1c3b8bfc6c921846f0562bef00788f5308553a54", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.9.0.tgz", "integrity": "sha512-t4CPAJh8I1a9TwOONoneglbmkQQkJwb1IgZ53Fl53yglOvecb1468Uq3cy5KjBjVYzGn78aHN1BZPXd6WtCxhg==", "signatures": [{"sig": "MEUCIC6vZqK0x6OEPYbb4MmS1eikSlCRmeojQbER0nc8IKHFAiEAk5BcBrSci4UJaJbmPUPyvn2tutIdGfAxmxYpyUCV0kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.10.0": {"name": "nodemailer", "version": "1.10.0", "dependencies": {"needle": "^0.11.0", "libmime": "^1.2.0", "mailcomposer": "^2.1.0", "nodemailer-smtp-transport": "^1.0.4", "nodemailer-direct-transport": "^1.1.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.3", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "57e6f02ce2cd54910c23d4c4001e2c474021b541", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.10.0.tgz", "integrity": "sha512-Tl8nrUPeIvkJTBIqFTmxXKmI9R8oUO5CWPgF7eOBGi2WF+bxkZM8sp1iS7Af2M2pHeqJpcbHlkL6SJsKJ9Zg9w==", "signatures": [{"sig": "MEUCIGivTEG+hp010goZ3rpJARhQBY+ctGHOfAdTCIkab8pvAiEAlK5/5PVR3f9Q0SeT3KiE9ay+vL5lxjaVoLyLBJnL0H8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "1.11.0": {"name": "nodemailer", "version": "1.11.0", "dependencies": {"needle": "^0.11.0", "libmime": "^1.2.0", "mailcomposer": "^2.1.0", "nodemailer-smtp-transport": "^1.1.0", "nodemailer-direct-transport": "^1.1.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "4e69cb39b03015b1d1ef0c78a815412b9e976f79", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-1.11.0.tgz", "integrity": "sha512-CA+mq08eL85Gg5S/Z4VdkS9f0jZUbiHr4XgUlifo7KGRem16tJuzwHOygW9CxfDzT48WgV8ffbNNJWX0BiT3Ag==", "signatures": [{"sig": "MEUCIQDGFa9Soa/xiNVfwvgyMTKLkb+yed50ULAvLCIOBpRv8AIgStSBjVKBmb2GRR+cJtrODjXlPtuiOATptdmh6r2doR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.0.0-beta.0": {"name": "nodemailer", "version": "2.0.0-beta.0", "dependencies": {"libmime": "^1.2.0", "mailcomposer": "beta", "nodemailer-fetch": "^1.0.0", "nodemailer-smtp-transport": "^1.1.0", "nodemailer-direct-transport": "^1.1.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "83d3fb720830dc52394c1ebaa07a355d2ac9495e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.0.0-beta.0.tgz", "integrity": "sha512-Jv+ijSxKvTUDCdzWsY2A6svXQTDCLPC9EwGCM7oaUYLqyyC4nt2goNRHG4FGE7cxNq1HCXW2012LGWn0dCGNDg==", "signatures": [{"sig": "MEQCIByRb40A9/uQCQEnWbiU+8hW35Z8eHAO20Pdi2Yda9iJAiB/A05oFiv+8mjQP+2Tnr84ZqSQq/fKzCdEWF93I8a9Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0-beta.1": {"name": "nodemailer", "version": "2.0.0-beta.1", "dependencies": {"libmime": "^1.2.0", "mailcomposer": "beta", "nodemailer-shared": "^1.0.0", "nodemailer-smtp-pool": "^1.2.0", "nodemailer-smtp-transport": "beta", "nodemailer-direct-transport": "^1.1.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "b1843b7db42ad397bcacd5cfd6ea4f4708c29771", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.0.0-beta.1.tgz", "integrity": "sha512-j79DmjssuIPxohNNWGxVyRpRe+KC/SGXUVcV15tJOm1aBvqqau8+81wh+RP5DgoLaIapPfsvVbdKVW7bGDzfWQ==", "signatures": [{"sig": "MEYCIQDEcTF3Iq/zwlVoDOOIbJDQJldy2isrUW9Q3s6VVo8OvQIhALHmowAw8tsbORXr/1FYsFCQTArG2hfxFpWLt4REfQIN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0-beta.2": {"name": "nodemailer", "version": "2.0.0-beta.2", "dependencies": {"libmime": "^1.2.0", "mailcomposer": "beta", "nodemailer-shared": "^1.0.1", "nodemailer-smtp-pool": "beta", "nodemailer-smtp-transport": "beta", "nodemailer-direct-transport": "beta"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "6b518e48b0f614356a5b63f4fc07a042b5686925", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.0.0-beta.2.tgz", "integrity": "sha512-H9qcrC3r9orHhKx0/gpOA/2VYvbOlCP1LUtzf7kXLcs3LNtii+ZqatQfhWR0cIIhCdmepsxqwo3n2ZdDjf8TJw==", "signatures": [{"sig": "MEYCIQDf2JfV4fhgX7+hymWXcp+MA4OQacdX7HUxwIAesdOwgwIhAI2/3L7xXQZ0yJ7xGHQAyruL3MUgAeecXKLF56nlkBRV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0-rc.1": {"name": "nodemailer", "version": "2.0.0-rc.1", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.0.0", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "a42caf44006d2b1f297b198a5e28a6d777e8762f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.0.0-rc.1.tgz", "integrity": "sha512-9gE13/Vq6Od9cj8DY92dPsIpsGeTMz27VwJ72AhdcW7EnDFyg+tpmKq1tWNgG4MXLCfvz7n8lX9Yo/COQvi27w==", "signatures": [{"sig": "MEQCIDPYljzF2edW1DsU1w5zcNlHTR16/IJIwCRihzD6saTUAiA2elIh6Nu+MkSQBMhiwIvGlHIUd6i/DUSoCFRD1fsdBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0-rc.2": {"name": "nodemailer", "version": "2.0.0-rc.2", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.0.1", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "a442117ba3968363ae797e2917ce5c1a787d770a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.0.0-rc.2.tgz", "integrity": "sha512-K74fm+X1MfaOflQjDlauesAp8/3TsZHH5Z70I7Gh3nmrm9FvyAnweWR/cnsqnwc7wEQhXnhHaL3mIxnmNN4IXQ==", "signatures": [{"sig": "MEYCIQCDw+X0wLHDSYIXRq92jyi5tCsfpFEQcsT9h27xyWWnhQIhANLGkb7xuaoNnW42zIX44xDfKkv+7+AyUVpBtlwfQifZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "nodemailer", "version": "2.0.0", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.0.1", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "fd2fafaf463414b146d502f4b898d4c8a976d709", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.0.0.tgz", "integrity": "sha512-Fw2IdiMSHV8iEFhoLN7SSHldMQQlU71fvKwyMh5xPCMgY/gk4mYlfrQ934T7Nfrg1lmrn9qu8fAZvj5wK3rPew==", "signatures": [{"sig": "MEUCIAWhzlJ58+S8VzmxVyscworvCWcSh/CKbynAR+giiXajAiEA4PTQd/VSJ8LHMTnMG7tLwFRZu5lZNk+6g+QD0LT18lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.1.0-beta.0": {"name": "nodemailer", "version": "2.1.0-beta.0", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.2.1", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "d1468ab2c2d2c530b214786b3a80f87a454ff1d2", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0-beta.0.tgz", "integrity": "sha512-SwwXe1LIjTROg/J+LtG/1oHwvOvXPADh5u2CO8aPWzZoqDTWZqlCdb6XP6LbjUesgjtgyew8wkKBfb4h5bTyHw==", "signatures": [{"sig": "MEQCIFEzjCaayA/0OyHRZW+NqNg+4+HLfJiLDyTQ/7xYG9McAiBWQ+J74cihHiowupa0C1Mj0keYUiD4ZYxneSXd3feSJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0-beta.1": {"name": "nodemailer", "version": "2.1.0-beta.1", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.3.0", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "c4c8a19b4b09818bd6bf7081ab4c121cf0225adb", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0-beta.1.tgz", "integrity": "sha512-Y/E6fxHkmjh1UoXX36cz+9PD62q5R2R8V9c8zID6JERryfoxIsR2JgAdeURYqtiuEbf9CChjAo4zLEL3VKk/SQ==", "signatures": [{"sig": "MEYCIQD3BLsJPnjb7zKtZmdpo+ncD4f/3laCYYYYQVOqJOjYiAIhAKFeSBDWvK9uOT21HQjOy6Na04iWKPFPMHqKva+CMzLg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0-beta.2": {"name": "nodemailer", "version": "2.1.0-beta.2", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.3.1", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "9943d127f83e2521a2cd2be516e4e997f1f4dd16", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0-beta.2.tgz", "integrity": "sha512-oUMgaqUWX8TVC/aY14F/l01QzELWcwZvR0oaVbwJ/EMgseCQumNSmBZ2PDautNYNGfsB7F/fP9+J25b3eWNL+w==", "signatures": [{"sig": "MEYCIQD7HGtvwIQQTkYIBKYmjVGyGMWh18sVFk3YYNAphvGoRgIhAOHGfJ8hDQO2y66Icz71TZAsz/6FoZ4rD1p4FIZ1Kp2H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0-beta.3": {"name": "nodemailer", "version": "2.1.0-beta.3", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.3.1", "nodemailer-shared": "1.0.2", "nodemailer-smtp-pool": "2.0.0", "nodemailer-smtp-transport": "2.0.0", "nodemailer-direct-transport": "2.0.0"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "handlebars": "^4.0.5", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "email-templates": "^2.0.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "818304c53bfab08fefb62866432586c0ab551384", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0-beta.3.tgz", "integrity": "sha512-vtkAWqGtsFH37x2zCR1umnORbnng3YXGawKj9Qs+Id+kwOq+UxFAarsLUuPzf0IvETmNCeEXuwYmD3Mkj3VGZA==", "signatures": [{"sig": "MEUCIEfvrG5YTmq3kmH59hrdlG0NVhZ3TCn8A8IriwdRujOyAiEAwQWa2OsCaO/NAMRp7w6wcwSWu2CKTdMa1gQoaP5r3ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0-rc.0": {"name": "nodemailer", "version": "2.1.0-rc.0", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.3.2", "nodemailer-shared": "1.0.3", "nodemailer-smtp-pool": "2.0.1", "nodemailer-smtp-transport": "2.0.1", "nodemailer-direct-transport": "2.0.1"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "handlebars": "^4.0.5", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "email-templates": "^2.0.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "61693f1f10807fe531cd997a212ec341a494fc64", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0-rc.0.tgz", "integrity": "sha512-E3j03GDWunyPtoD3bA4yaXqxsgD39N2rgPofCTQZYQmGciYe841hbVkgp+THjor+OG/k/gI/zjIGMhbJN8IB3g==", "signatures": [{"sig": "MEYCIQDY8oc9WPXT4JHnP/CqXTiHxZ3bllPwGy4hCgfxVp2FNwIhAKWJ7JnKMNhGi0YsZ8b8mobzhHg4VgS/TOwfDJ09KRQJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0-rc.1": {"name": "nodemailer", "version": "2.1.0-rc.1", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.3.2", "nodemailer-shared": "1.0.3", "nodemailer-smtp-pool": "2.1.0", "nodemailer-smtp-transport": "2.0.1", "nodemailer-direct-transport": "2.0.1"}, "devDependencies": {"chai": "^3.4.1", "grunt": "^0.4.5", "mocha": "^2.3.4", "sinon": "^1.17.2", "handlebars": "^4.0.5", "smtp-server": "^1.7.1", "grunt-eslint": "^17.3.1", "email-templates": "^2.0.1", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "09b996cf62f973a4614cde964fde5e2a9d9d5fcb", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0-rc.1.tgz", "integrity": "sha512-3d08JhmmVBWvXRQR0I92eh/410lrLzAL/tn9xZ/A3L/V7Hj6OIBO7Jxd8zEViSdTENB/M7mSQTLIFHR5xrk+MQ==", "signatures": [{"sig": "MEYCIQCiEAhg5b+g5LrYOvVWMJn/Pz3xo7jdYe5QgRCzCK1DQwIhAP7N5Y9PVG+rXbdtU3+jbHvGR/sLSXxb5jftHjf0oPEF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0": {"name": "nodemailer", "version": "2.1.0", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.3.2", "nodemailer-shared": "1.0.3", "nodemailer-smtp-pool": "2.1.0", "nodemailer-smtp-transport": "2.0.1", "nodemailer-direct-transport": "2.0.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "cf10adfde6e5c1f10bea970071abd9e83a09d3f4", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.1.0.tgz", "integrity": "sha512-hj7/X2U9kebbDlZnWMO3qPC7wtw+pOBsdAteIsNXU8KDHFx2q1YYL1yYBtobD3R1j1i49zaaw72KlJ2/kwx9Cw==", "signatures": [{"sig": "MEUCIQDykA7tcTTc18CyTiSW74uP6dxWVr3shwSSII3pGN0xOAIgBwTInoPD8+4qpky5xY0AXuwEuix7UjRvk6w/nqqZZc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.2.0-beta.0": {"name": "nodemailer", "version": "2.2.0-beta.0", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.4.0", "nodemailer-shared": "1.0.3", "nodemailer-smtp-pool": "2.1.0", "nodemailer-smtp-transport": "2.0.1", "nodemailer-direct-transport": "2.0.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "cc4280e21e1f591fa076fae8f9c99730b0147780", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.0.tgz", "integrity": "sha512-ZdHTUgqBcLD+kVcYJNdIREGj+2d+ah/sKS3mWMsiRG4VgJB+svRiD/zuOhLNY2U4u8ZwdP53XrAxHGbtFPWSgA==", "signatures": [{"sig": "MEQCICSIttG/UFvSxiY52nscOfu3gsLBYISZpBUJS93L3ekHAiA5cXk+5UhX2N/RBTOQZ+teGCce2J7/Xzxx/NPhfIoslg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-beta.1": {"name": "nodemailer", "version": "2.2.0-beta.1", "dependencies": {"libmime": "2.0.0", "mailcomposer": "3.5.0", "nodemailer-shared": "1.0.3", "nodemailer-smtp-pool": "2.1.0", "nodemailer-smtp-transport": "2.0.1", "nodemailer-direct-transport": "2.0.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "5dabb52c5901ffa472faf373767794da9843c2be", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.1.tgz", "integrity": "sha512-oUV5BvmVXBALhwBoFclLq73tnLRUDT/n+hdhDNWwxQ8dwVZfOzUG3OlSy/xZNJ6e0ta5kGE2IrcpB4V8alQnSA==", "signatures": [{"sig": "MEQCIDmU0hpZ3iVbP0Od6QL/mDLM9i9kBD0G3wAjT7WMEcKpAiBW2bQ5+VPHD6ZqGcFreGxHKHIKH/9wWtMgb2wLW9QL2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-beta.2": {"name": "nodemailer", "version": "2.2.0-beta.2", "dependencies": {"libmime": "2.0.1", "mailcomposer": "3.6.0", "nodemailer-shared": "1.0.3", "nodemailer-smtp-pool": "2.2.0", "nodemailer-smtp-transport": "2.1.0", "nodemailer-direct-transport": "2.1.0"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "87c3ee97157766c1edfef8e65779a370417d394d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.2.tgz", "integrity": "sha512-K2MOaZPbBS7KtPX3Vh1LJF04VY+rs721MA6b+EBwauJlRcSKTubbuiTRKKbepOH+JBU9IgQ+5m3RO2i36z+ntw==", "signatures": [{"sig": "MEQCICJhqzSgBpdNOj4GhxkisiAONEv9Jdjo6SmkWwzyh030AiBYCHh35+FV1EXWbkfQAFgnCUqoI9WUGVVdP81L5/dNww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-beta.3": {"name": "nodemailer", "version": "2.2.0-beta.3", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.1", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.2.1", "nodemailer-smtp-transport": "2.1.1", "nodemailer-direct-transport": "2.1.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "3168968d16a3713634c48b04555ee4a1d1486255", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.3.tgz", "integrity": "sha512-Cwf21yvD6gOzbBScfIMSOJyrApWGLiqvx4NyN0S2TNPG8XzAeVMsUu2Lu/belrJ504wpGOS+oiru1JhY+7zrWA==", "signatures": [{"sig": "MEUCIQDWJr/lusmPb4EuOBl8sRKhc2KUPw43vDAnbXfLsmzpDQIgW1CoL+4pndY/KWITvuZh4GpGEt+hQ6dtnqFYrEzFfGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-beta.4": {"name": "nodemailer", "version": "2.2.0-beta.4", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.2.1", "nodemailer-smtp-transport": "2.1.1", "nodemailer-direct-transport": "2.1.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "035f7c128160e18caf4ff72939809cad565ca08e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.4.tgz", "integrity": "sha512-izK7bcPAsahTWWcor7S+0uKtBnuNL/VpN/KvAlBPFJh7Eyl1om0SUeEdI0OoThr6F8MrzIUw+DwVUgkQoECkfg==", "signatures": [{"sig": "MEUCIQDprtYjXKg8waeSpR0DxJgVywTj2+U6Ctl/1zdOlp882wIgGvavM5fAfE9gpZPwfkYkNKBwkTxgG27RDcI6SXrtU+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-beta.5": {"name": "nodemailer", "version": "2.2.0-beta.5", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.2.1", "nodemailer-smtp-transport": "2.1.1", "nodemailer-direct-transport": "2.1.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.1", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "14d53f5c5fd4dd713993978edae0a11f84ae3b16", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.5.tgz", "integrity": "sha512-KvI3zjUxdw8wVX4SVBD8C4swFDP2z+B3ohKXYlf1kBBwQ3C/nYJ1l/z/y4BkXFeWJ87zYoA5vj9aHFSa8gWQzw==", "signatures": [{"sig": "MEQCIGtzBLc+T1OKMluwl/2Tx5iCN1VPVFN2R+JFAZ26RTxHAiAtf4ildK7BV4HmcTr78AE/ZPu8HSUDhsJUt5bQCcJpkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-beta.6": {"name": "nodemailer", "version": "2.2.0-beta.6", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.3.0", "nodemailer-smtp-transport": "2.2.0", "nodemailer-direct-transport": "3.0.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.2", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "9d66475ce9de333b7dce21ce2d727db0ce96cd4f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-beta.6.tgz", "integrity": "sha512-X33+9dKIGhUPxcwQ+HkTU/Oj/4SLNRP7b9Y82BmanoP65PRo3xV1mjavUoNUYJonFmDYgt+dwmOqDBLIM8kGvQ==", "signatures": [{"sig": "MEUCIQClLRSjeiVKWPePtMwPmU/4CKRg1245iabYDw4ZoKzbNgIgEZCmOOA1MEpVrUIshl+/9Rq16z0Mr0sdDe6hPpSzud8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-rc.7": {"name": "nodemailer", "version": "2.2.0-rc.7", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.3.0", "nodemailer-smtp-transport": "2.2.0", "nodemailer-direct-transport": "3.0.1"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^17.3.2", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "1df44e3bc47cb816f39e10bb0c53f9d6fd8fba9b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-rc.7.tgz", "integrity": "sha512-+pW8b7fokimHYjfISHHhMcaR4Ktli3D+Rx4XqPqrU7cn5WJfcEI79Mfs8RCoktXZgvD1gQ92H+huoylS3tDh2g==", "signatures": [{"sig": "MEUCIQDhWA/prQziCCoYNDvDw8SI0sqCSg1EQGn5oka/ruuEJQIgfZL+MIgcq1EgZvf6ZDiJx0q6MVugdXpNImLCALIEMoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-rc.8": {"name": "nodemailer", "version": "2.2.0-rc.8", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.3.0", "nodemailer-smtp-transport": "2.2.0", "nodemailer-direct-transport": "3.0.2"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "82f11d3d915340f7abb0de959ae2cda8c605bb3c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-rc.8.tgz", "integrity": "sha512-DYS8C/moRkACAYWNXJ1GKAdpnVMMrjFE6ChJEv63qGfN3EqMPyvBuwzP7jjQ8Hrfp1AnBv7H+RclYa9iJBuqyA==", "signatures": [{"sig": "MEQCICBpcZ/cHw83nIn0JZQqU/s1gKg7XWtLy94lcxG90ZbhAiAgHqRfNsnSzYdWWzk5fKltDeChL5ZQ/b9Oaq5ImbWgDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-rc.9": {"name": "nodemailer", "version": "2.2.0-rc.9", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.3.1", "nodemailer-smtp-transport": "2.2.1", "nodemailer-direct-transport": "3.0.3"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "e6dea957cfe06be03c22db235dce947ed874b8a4", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-rc.9.tgz", "integrity": "sha512-6y0vSUSXD77Y2VMAjHYPJC8wKKftr7P43oQ8buhYMB4s+HaxfF/S8Z7+GYEg5eF8CwLuEEQwGpJqE53Hs0KSfw==", "signatures": [{"sig": "MEUCICbifbbHJp+UuQAk0VU/CX0LGiNvdNJgKVwY6AgDYgyKAiEA1t00uDgvCuFsH7T5evPxb8KAPo5NAsA6nzVBLugJN+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-rc.10": {"name": "nodemailer", "version": "2.2.0-rc.10", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.3.2", "nodemailer-smtp-transport": "2.2.2", "nodemailer-direct-transport": "3.0.4"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "4b64b928c3c61bb9083bedfa2ad318d4d8b833b1", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-rc.10.tgz", "integrity": "sha512-VttwZ0wwLgN04q6iup7LFgrwhzUrqbGd/YtSVjLtPnpBQA2njiENTQS43dtaRrtD6xumchdFYMhZAtvKB7Nfww==", "signatures": [{"sig": "MEUCIQDZfRtasx+pzJ8vVKjHCWsdT8X74TrCVXugBTqTh/wDSAIgHqHpjLXyZqGTa/6mScBekQ/iuLAALADZ+c+RsYkiSqg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0-rc.11": {"name": "nodemailer", "version": "2.2.0-rc.11", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.0", "nodemailer-smtp-transport": "2.4.0", "nodemailer-direct-transport": "3.0.5"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "d3fee68204c6748556211251f6c80c061483c456", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0-rc.11.tgz", "integrity": "sha512-AjGFP2/x+vTC7OS7xsVFcziLLsJaGf8Br8QIDHy0TnbHAbu89J0/ZnOeX7tTm2c35Nr7VqlcZlQAOT3WIg+ebA==", "signatures": [{"sig": "MEUCIQDXq+MdCp1fldYiw54p054DWfcWOK3XxQLdDQZnt0Mk2gIgXOQqXmiLSOc2ZpPSZmQInqINdSMhYV2naLeOTSwb+vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0": {"name": "nodemailer", "version": "2.2.0", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.0", "nodemailer-smtp-transport": "2.4.0", "nodemailer-direct-transport": "3.0.5"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "fb596698d5b11f5e843d0e2491bc02510468476b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.0.tgz", "integrity": "sha512-22jRYbwTc6oztZ4uZuN5KhV5OzIhh6pNdbtBgTK3qgR2FXyDvztWf9MGh3F8o//6MKjcGHPeGSI7SEvOvXaHMA==", "signatures": [{"sig": "MEYCIQCmdmM+LCzCu3gbZr5YkJATtvD04zV44YQOGbrHzzfW8AIhAM10eJ2xKaikNnZIUkXn/Z0YobYpd437LKMpON4k3+M2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.2.1": {"name": "nodemailer", "version": "2.2.1", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "e62585081bcf1a0141b6ad449a5dd4aa85e726d5", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.2.1.tgz", "integrity": "sha512-StmtJz5251ML5Utp6EJGa5TPqPq90Ig/5+4wC7Qar8kbYNI4K6JkvLjdJJAj2DYyGTqUrOEfX+cZim0JmCD5kQ==", "signatures": [{"sig": "MEUCIQDv8Sk5SYn7LLwoDAhtyqiAGSzzZSjHBePgj8tf1xvuQwIgAwySP6Wc0SOuE0caCy+JdVEMx5pP2A4HDWNqlXw4o7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.3.0-beta.0": {"name": "nodemailer", "version": "2.3.0-beta.0", "dependencies": {"libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "socks": "^1.1.8", "grunt-cli": "^0.1.13", "handlebars": "^4.0.5", "smtp-server": "^1.8.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "f3430b130b5d67a524f63e6e3758c850aa0fbf4b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.0-beta.0.tgz", "integrity": "sha512-4T/M8yFfxPHKGXcKHOzrMI3K952d5jfyWrYaYrYJO5MDGdnc7IIJyduEVA3itQ+olj1Gqp8Oi21i+fJZXrKhVQ==", "signatures": [{"sig": "MEYCIQCBNWhYpmLwole6SEFDVzZFIsvchZtnbBXkv93lvU8BWgIhALCjWpf55tll8ct1rxoNFe6kPPpizVonWcmMSB8v4Eeo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.0-beta.1": {"name": "nodemailer", "version": "2.3.0-beta.1", "dependencies": {"socks": "1.1.8", "libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^0.1.13", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "73997f6080f9c60c52685c447a42ebd1a8201857", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.0-beta.1.tgz", "integrity": "sha512-pMmkg6u2sPBriGN/Dey9pkvGCQHFXGXf7asqKcjyd4OGmTUGAcMuSADBsUw5dAuZ9xl09xCF/42283iMkIwZLg==", "signatures": [{"sig": "MEQCIFK1jq+QfReJ0UO9uTZZkb9iWhknDqNfYzEFg6pnwfPOAiAeHKb48vh0k/zqxE5X7lUMCDd5ZmV+ZnVZ4yWE2lw/wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.0-rc.2": {"name": "nodemailer", "version": "2.3.0-rc.2", "dependencies": {"socks": "1.1.8", "libmime": "2.0.2", "mailcomposer": "3.6.2", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^0.1.13", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "fc9c87f2b564880132289c801f8c1a82e5b797bb", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.0-rc.2.tgz", "integrity": "sha512-FgNNUfijK7XT5ow4IQs1Y7Rls8dHqoqMph6jX2+XndhAlDxV7BDbtyXsKmwW7YdlUAfjC23C2n28jyK9G5mvFQ==", "signatures": [{"sig": "MEUCIFy6NBRv97vYQUQhdyFWn/hIEL69aOb5fC1KHW/djS+yAiEA3JzhoyNTFKJ1ywjqoFkvNlTecvBdr3jfcoBA/yNBIs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.0-rc.3": {"name": "nodemailer", "version": "2.3.0-rc.3", "dependencies": {"socks": "1.1.8", "libmime": "2.0.3", "mailcomposer": "3.6.3", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^0.1.13", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "5310b7175212940c4d7f7333d5acef994e600de6", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.0-rc.3.tgz", "integrity": "sha512-DwL3r8fF33OOhSXjNjRhzC1UtSioA6e4IeQHSJC/TlBQtLbCIm1iXo1wCf1YutIeoZpG1ZRfhiKQVkXhYZ7wCg==", "signatures": [{"sig": "MEQCIGJZeHfY2r6IMhQKgXQyqTdcYfflVI5cMzW0B8Um+BhYAiBybjLqtHyhFDwAOv6v+XufeFb4aEV+ERCpoY8tO4G3HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.0": {"name": "nodemailer", "version": "2.3.0", "dependencies": {"socks": "1.1.8", "libmime": "2.0.3", "mailcomposer": "3.6.3", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^0.1.13", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.1.0", "nodemailer-dkim": "^1.0.3", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "7038dc6952c7e60a95e9f5186d1b7c9570cc06bc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.0.tgz", "integrity": "sha512-Pp3ZWkK1YcPtj2ATjJvgPdKNcS00LAa2wmolq6Mc+PUlQBrBFqGV/6wLRIZJgoi+gMCw9oPe1lzKQU8xZixtSQ==", "signatures": [{"sig": "MEYCIQC8z0/xPZCpE3sP1MXQxqJ40WWRNXWEt9efnlSnj+Es/QIhAMzgaXxEH5xZi9wQJHr9sCe0kpMkyl5ozKdl4lqGK77w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.3.1-rc.0": {"name": "nodemailer", "version": "2.3.1-rc.0", "dependencies": {"socks": "1.1.8", "libmime": "2.0.3", "mailcomposer": "3.7.0", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^1.1.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.2.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "039d385d35d0c050d41ebd34db9e0d69ac4a1eb8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.1-rc.0.tgz", "integrity": "sha512-jkDxN6DV0iGJH4C26/t56zteuwil+azvkEz7vtND6iUXBcLLz1SICP6Sni/UNANV2SjUFmdDC1B7YK2we8MUWA==", "signatures": [{"sig": "MEUCIQDZLE/Q2UNjwrDWPm+XEkw6QXtg+8ofNkyOywhqv8yKfgIgcDwoKZZIv8Bs429cZ5AUKywqgOWSCwsyEqI52PUca3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.1": {"name": "nodemailer", "version": "2.3.1", "dependencies": {"socks": "1.1.8", "libmime": "2.0.3", "mailcomposer": "3.7.0", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.1", "nodemailer-smtp-transport": "2.4.1", "nodemailer-direct-transport": "3.0.6"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^0.4.5", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^1.1.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.2.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "e1dc760f2e90cda3371677aca7cde808cc73e6cd", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.1.tgz", "integrity": "sha512-WXYAAb6zkZncWghiRoFnSNupNetUxFdy0DIcqX0nup7/FRJ+bZfJv6QvXvVoYIpPQi7M1+XnHHbDA9xKa+8+oQ==", "signatures": [{"sig": "MEUCIHTQcbb0alhHtbBeEMXA4hmECSOWeSE534mVv/LKsletAiEAvPHP3/c7c9yn/ZkvbYyBzsAs0pooPyXjtbeBzD+FPrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.3.2": {"name": "nodemailer", "version": "2.3.2", "dependencies": {"socks": "1.1.9", "libmime": "2.0.3", "mailcomposer": "3.7.0", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.2", "nodemailer-smtp-transport": "2.4.2", "nodemailer-direct-transport": "3.0.7"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^2.4.5", "sinon": "^1.17.3", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.0", "grunt-eslint": "^18.0.0", "email-templates": "^2.3.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "d8ad5de4f43a23369569370da306e6d1d6b40196", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.3.2.tgz", "integrity": "sha512-ERMqVz7nroCjUS29yFMc2WPTSdgqLLVKkO624/CiZNhbNdsvu9SOJ+6S8NsGtimfEQRKJy6a7/jPfV0AmAKImw==", "signatures": [{"sig": "MEQCIFC/s+oOS55NoyxI99rP7YUBnj7zPGekeuBmSPyMbzwiAiAQmxO0mWl16zH+jsWvB4ycGHjAT12woaTfcTRAFNhlXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.4.0-beta.0": {"name": "nodemailer", "version": "2.4.0-beta.0", "dependencies": {"socks": "1.1.9", "libmime": "2.0.3", "mailcomposer": "3.8.0", "nodemailer-shared": "1.0.4", "nodemailer-smtp-pool": "2.5.2", "nodemailer-smtp-transport": "2.4.2", "nodemailer-direct-transport": "3.0.7"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^2.4.5", "sinon": "^1.17.4", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.1", "grunt-eslint": "^18.1.0", "email-templates": "^2.3.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.1.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "739b2b63506f40b1d534b9d952dd1ba2c72938a8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.4.0-beta.0.tgz", "integrity": "sha512-w5QsZRS9mPdU8Mp59rgKdXprhCoVGQ3YYk28rb0GP50WyPL/LNhgjYu7+GoxjPnZcuGT7PaG9fbfayRYv6kRrw==", "signatures": [{"sig": "MEUCIQCgpCAmSkSG05pG/iMc5W5aN43pj5yuWq1UDv9IHARbTwIgfJ9MDr1qTunTVxX2QpgEcksvBKS65pEaY+z4jKdLnS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.4.0": {"name": "nodemailer", "version": "2.4.0", "dependencies": {"socks": "1.1.9", "libmime": "2.0.3", "mailcomposer": "3.9.0", "nodemailer-shared": "1.0.5", "nodemailer-smtp-pool": "2.6.0", "nodemailer-smtp-transport": "2.5.0", "nodemailer-direct-transport": "3.1.0"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^2.4.5", "sinon": "^1.17.4", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.1", "grunt-eslint": "^18.1.0", "email-templates": "^2.3.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.1.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "9727f1408416e7ae09841a2d17437dc74d60f5b7", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.4.0.tgz", "integrity": "sha512-pUReIvCMukFmN9DS4DUGuRzXdFGteNYqKUbU6N77p4kx/tLeooRkJvKRfdLeOkUafQ3bYiHCvvnyxqUIQ7EKpA==", "signatures": [{"sig": "MEUCIQD04j+ARTIFhu8xnaXFuSYEXxI3hJ+8j4vth9OslRl07QIgJC4fz9Fmj+8M+oJptz9wu+XcZGT9ZNJ8iq1wMlcxSmI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.4.1": {"name": "nodemailer", "version": "2.4.1", "dependencies": {"socks": "1.1.9", "libmime": "2.0.3", "mailcomposer": "3.9.0", "nodemailer-shared": "1.0.5", "nodemailer-smtp-pool": "2.6.0", "nodemailer-smtp-transport": "2.5.0", "nodemailer-direct-transport": "3.1.0"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^2.4.5", "sinon": "^1.17.4", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.1", "grunt-eslint": "^18.1.0", "email-templates": "^2.3.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.1.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "d5e375976f8772dcaa5c43f0669011d62aeab9e0", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.4.1.tgz", "integrity": "sha512-Q7TRH1nW/DvxVLosN9ZopBNvPqMZctfzj16YIGJXZ1ZyMP4NFo7K5Ynw+CzHtYHBnzQ5X4/CcRSKItHl/1JVwA==", "signatures": [{"sig": "MEQCIEJ50qxErw69UUKckBVOnCnchlqRZ60FQNweJYeMJbBCAiBHZQv2QDQRqTU2SY9gvZrNCUiU46aKceKZEogHsBPjpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.4.2": {"name": "nodemailer", "version": "2.4.2", "dependencies": {"socks": "1.1.9", "libmime": "2.0.3", "mailcomposer": "3.9.0", "nodemailer-shared": "1.0.5", "nodemailer-smtp-pool": "2.6.0", "nodemailer-smtp-transport": "2.5.0", "nodemailer-direct-transport": "3.1.0"}, "devDependencies": {"amqp": "^0.2.4", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^2.5.3", "sinon": "^1.17.4", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.9.1", "grunt-eslint": "^18.1.0", "email-templates": "^2.3.0", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^2.1.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "497b59deb506efda36b69fc041228e42699059d0", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.4.2.tgz", "integrity": "sha512-NeelMWLZiJy3HcD1qF9OtwvYI8bhECCD3ED0fvbrI+k3USoRu4geB/oYu98stUWFh4leDA2iQtD1gODFF2GFUQ==", "signatures": [{"sig": "MEYCIQDpJx5d4iWtnhqKHVnriHfB9C2oPGlLrFEb0IEkx0o7tgIhAPMg0GCf5UvlCSrBsj52YB4QCnLPQwh03RMp3zTO1lDo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.5.0": {"name": "nodemailer", "version": "2.5.0", "dependencies": {"socks": "1.1.9", "libmime": "2.0.3", "mailcomposer": "3.10.0", "nodemailer-shared": "1.0.5", "nodemailer-smtp-pool": "2.7.0", "nodemailer-smtp-transport": "2.6.0", "nodemailer-direct-transport": "3.2.0"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^2.5.3", "sinon": "^1.17.4", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.11.0", "grunt-eslint": "^19.0.0", "email-templates": "^2.3.2", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^3.0.0", "nodemailer-stub-transport": "^1.0.0"}, "dist": {"shasum": "9cb782b049f4d62e17022dcbe743c3b07a2de55b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.5.0.tgz", "integrity": "sha512-Wg425VYYhRz3Z3jXeSmge/wHg/AcVOu/U+8BrfblZHLCFGQXeCViJMScuhw0lDHddO8ZvvsiTH42nInc3O82Nw==", "signatures": [{"sig": "MEUCIQC37iXwtnKuvrU8sA0m80lXiB0oj2yZitaGxiWDe3Vv2QIgSAic4dTnElXRHXl/g5LUmBASWhDuMD88x7ujeYhlpf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.6.0": {"name": "nodemailer", "version": "2.6.0", "dependencies": {"socks": "1.1.9", "libmime": "2.1.0", "mailcomposer": "3.12.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.0.2", "sinon": "^1.17.5", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.14.2", "grunt-eslint": "^19.0.0", "email-templates": "^2.4.1", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^3.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "d37f6251d7ac846d0f297ae14906f930155fecd8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.6.0.tgz", "integrity": "sha512-AF/gOK7uzPU4QJ8dhtcE4XWAYks82HaxbyhjYlS7tH+Nuerc6joxMaVUcVzKzwIzTBjnFMVZdnWX+GcDJepllg==", "signatures": [{"sig": "MEQCIDtIE1pyIIdp0BUB6+T3iSTO05Ce7LAgJToL1CG47squAiB4GHLZZvkPBInVKkejaHoCRQvt9fS9lTwHJOqNUdX+ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.6.1": {"name": "nodemailer", "version": "2.6.1", "dependencies": {"socks": "1.1.9", "libmime": "2.1.0", "mailcomposer": "3.12.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.0.2", "sinon": "^1.17.5", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.14.2", "grunt-eslint": "^19.0.0", "email-templates": "^2.4.1", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^3.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "3caa29db5a3e7d67faf4522c25dfa9dcfbe0a90e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.6.1.tgz", "integrity": "sha512-yEMkbA51gWyPi/nAhsHzGkKkIQtBaQ4Es6KEcugsJREQI57Wa2eL+h4O86CgiKBixHqoQg1XhCBWFuk3vPAm+Q==", "signatures": [{"sig": "MEYCIQCR6EjDLn9lz8CwXa9wURUv1lU3pHUHs1+LkaEy3dZiugIhAIE7rVTmfQAku+ApqgQ6zNlL7JMTblIT4iodWugKf4KH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.6.2": {"name": "nodemailer", "version": "2.6.2", "dependencies": {"socks": "1.1.9", "libmime": "2.1.0", "mailcomposer": "3.12.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.0.2", "sinon": "^1.17.5", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.14.2", "grunt-eslint": "^19.0.0", "email-templates": "^2.4.1", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^3.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "d56e474ca91c963a68d78e6415885ff45c88f696", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.6.2.tgz", "integrity": "sha512-G8bsfreqULJI6ruoe3CjElLmCQNS1t30JQujy2aOnu4QY6d/DwNwoNT/wHeJ2kfsztBVAu/7S7cXHgp2LX2B+A==", "signatures": [{"sig": "MEUCIGfW+2VoEsCuXNfXyJMGvmTj+M+H+K4jSGccT88BNo7PAiEAqvdBXUtYqAeSzX/t632pvcbzFJIoDv9jJ17L0ieIRuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.6.3": {"name": "nodemailer", "version": "2.6.3", "dependencies": {"socks": "1.1.9", "libmime": "2.1.0", "mailcomposer": "3.12.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.0.2", "sinon": "^1.17.5", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.14.2", "grunt-eslint": "^19.0.0", "email-templates": "^2.4.1", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^3.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "d5a57109e879bb082179a34ab107b9eff58cc731", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.6.3.tgz", "integrity": "sha512-U+yrkAkh98/zUB2pUdmawFtPIiOnanN+5mAKfb8l53zem7lFn/zxxFp/s6UkOuVra+pKFxtbfBAbvLFgzDHImg==", "signatures": [{"sig": "MEQCIChOxR97DaBhCQa8vJaKEKIIjIUOD/RcZ3rFpmDoVrrjAiBECR0aCag+JaC2BirhA6QWXR0Zes7fXCEe7rNheu7PZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.6.4": {"name": "nodemailer", "version": "2.6.4", "dependencies": {"socks": "1.1.9", "libmime": "2.1.0", "mailcomposer": "3.12.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.0.2", "sinon": "^1.17.5", "grunt-cli": "^1.2.0", "handlebars": "^4.0.5", "smtp-server": "^1.14.2", "grunt-eslint": "^19.0.0", "email-templates": "^2.4.1", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.12.7", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.0", "swig-email-templates": "^3.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "5620707c55fac674b6e2e5e425708b9ad5c6b6d6", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.6.4.tgz", "integrity": "sha512-TDC83tHL38bL4+fmxoqoQ0XZNkfuZEestemUqYxUBaGZu/AKmjDuUZjAgAnM6Sm54FbMVZdjwdQJ/K+tYnBQaQ==", "signatures": [{"sig": "MEYCIQD/uqaoQxpGa2T10q0Qv+YlvJvC1rrV176LVRyJrKP02QIhAMtJkYkNlgZF/YQCCNsF0HbTt4l1GhrhtE79WN+WRtit", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.7.0": {"name": "nodemailer", "version": "2.7.0", "dependencies": {"socks": "1.1.9", "libmime": "3.0.0", "mailcomposer": "4.0.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.2.0", "sinon": "^1.17.6", "grunt-cli": "^1.2.0", "handlebars": "^4.0.6", "smtp-server": "^1.16.1", "grunt-eslint": "^19.0.0", "email-templates": "^2.5.4", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.1", "swig-email-templates": "^4.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "d68f1d6c0c7a65fc7abbc3c0f02016183012a36b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.7.0.tgz", "integrity": "sha512-04zHtoI7yBZ+hsIa8m0qIebMj1v4SD2EB4W60ZK25Umw5T4/ZFarhsr2ZrP2Yrj1WYIeNjbEr975XROnoGpxQg==", "signatures": [{"sig": "MEUCIFWe3Z6OAgM79ZwwEu+whl1W4KDXYVEv+nl2dW7KpSZjAiEA0XoE5RjxfRnl81xhUkgcYdl8T1icYvcQFRC1aPraLa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.7.1": {"name": "nodemailer", "version": "2.7.1", "dependencies": {"socks": "1.1.9", "libmime": "3.0.0", "mailcomposer": "4.0.0", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.2.0", "sinon": "^1.17.6", "grunt-cli": "^1.2.0", "handlebars": "^4.0.6", "smtp-server": "^1.16.1", "grunt-eslint": "^19.0.0", "email-templates": "^2.5.4", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.1", "swig-email-templates": "^4.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "d87f2e4a47b10009caa67a523816d6f878dc45bc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.7.1.tgz", "integrity": "sha512-X7GGAs1oBXpa/SFBKtdPFfx/iG5Rv3bP5vjk82v2M2ElgfbrnAbWdtjFES2bDj2ps0SYcAm0pfxWk7XFo9lRqg==", "signatures": [{"sig": "MEYCIQCSu680sjsEhNXkPiSAwuGa6S0sMS1JtxLf76wcYqb5hAIhAIHmJu9P+nPKyyQoXRrsMEuAya4EfiBt9CYVQYEhvCzp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "2.7.2": {"name": "nodemailer", "version": "2.7.2", "dependencies": {"socks": "1.1.9", "libmime": "3.0.0", "mailcomposer": "4.0.1", "nodemailer-shared": "1.1.0", "nodemailer-smtp-pool": "2.8.2", "nodemailer-smtp-transport": "2.7.2", "nodemailer-direct-transport": "3.3.2"}, "devDependencies": {"amqp": "^0.2.6", "chai": "^3.5.0", "grunt": "^1.0.1", "mocha": "^3.2.0", "sinon": "^1.17.6", "grunt-cli": "^1.2.0", "handlebars": "^4.0.6", "smtp-server": "^1.16.1", "grunt-eslint": "^19.0.0", "email-templates": "^2.5.4", "nodemailer-dkim": "^1.0.4", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "nodemailer-markdown": "^1.0.1", "swig-email-templates": "^4.0.0", "nodemailer-stub-transport": "^1.1.0"}, "dist": {"shasum": "f242e649aeeae39b6c7ed740ef7b061c404d30f9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-2.7.2.tgz", "integrity": "sha512-Jb4iapCeJ9nXmDurMyzg262u/wIVGRVkwr36oU0o8hL7U4w9n9FibMZGtPU2NN8GeBEAk0BvJCD/vJaCXF6+7A==", "signatures": [{"sig": "MEUCIChoBQAU/YXXRKy6RQXdfE9jbcXTic5ZXELzlRwtFJgUAiEA6q7INyl8inKUArFbzjBcp9vdMxVSvT6ItCfWjC5hij8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.0.0": {"name": "nodemailer", "version": "3.0.0", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^1.17.0", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "31b65911c0655b71dc10c9d5152cb6ab6ad88c8e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.0.0.tgz", "integrity": "sha512-nbHYeuuf/yXwM4dHQM684f2llQyO6o2qV5BDwmstifVaSbzsmtaFzHiHGik36ieMRfzxmC5nGA4iWrZTxvui8A==", "signatures": [{"sig": "MEQCIF6LsmapbgA2sGzAR/+N5a/YkNmEekPRvY4zfaMM68a+AiA1NSDrneDQyj5p8gMhSBojQsVapDWAPCJm0v3iJYPLrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.0.1": {"name": "nodemailer", "version": "3.0.1", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^1.17.0", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "e6cbf049fee860d3aa9f16e605631b09270753ce", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.0.1.tgz", "integrity": "sha512-rwMz2q2t4FCYqgm21bv1zFWW2HJOiKSFX1YaK1AO0504bsdu4yzu48LQ2iMUUnNSGY3JQ8miJnXAvJBnlha05g==", "signatures": [{"sig": "MEUCIQDkP9goz4qALOlwSnCTimP0hgT1lW7JblizL42tduf9DgIgekmyhKMtL6rYAKS6u4HxX/H2Q2Vmc2rNm2dCjvmchRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.0.2": {"name": "nodemailer", "version": "3.0.2", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^1.17.0", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "6f5ffc608590b1980a9c24820597e60ef8182305", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.0.2.tgz", "integrity": "sha512-YWT24DUgLWp1E4ll0+6CUaZ1rBo8S5IIdBJQ1NL31Mux10LTcX7EUB63JWdLJEA4obJkcg1Sz68d8uciJCUtNw==", "signatures": [{"sig": "MEQCIFjs43xQXWqCRjI7lJnMjKfsRGCTX48QChAtGUq+TmGUAiALq2x9eqgIKe/R9lZenJETt515lxrRH1AxXGUsLHgiOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.0": {"name": "nodemailer", "version": "3.1.0", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.1", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "751e032ac9ccf2c5b02f49a0d5a1af78130ec93e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.0.tgz", "integrity": "sha512-4GQ4cyAwAEqAmPFm0Xms5nM9eH+JeCl98UkxMrhh26zV8kRWa2QFS2DO5vbwQc4hsknZbUgMOMj317GDavh+Rg==", "signatures": [{"sig": "MEQCIEVBuIOoEBtQQz6tjv8a1U9ctqbXf/3pfb1nWScEyXkoAiA+gkFDdsnLDI8jcaDJIu2KvYkF21HU3xjknHGlBV/LXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.1": {"name": "nodemailer", "version": "3.1.1", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.1", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "cdddb64d86387f902542307b4d92ea00140445a1", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.1.tgz", "integrity": "sha512-b6GR+2uZBifAvb5f5mnPfFWCW4MHscx0WOi7jhwfg9Qv6s3y2kWA1PQa75NnrTMJ7UtXqbH1RdKP4JxDlvrQRw==", "signatures": [{"sig": "MEQCIHrNIeKdEOuLH0HUkePw97wr59xuNO2NIefec0cShJiKAiAyQISMGrnTLddMOv+Pyt6x6gZgZfF8abfSe1WUKFC/Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.2": {"name": "nodemailer", "version": "3.1.2", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.2", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "4306b8a9af86c1ec8b1ee40dceef1cbb089ea56c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.2.tgz", "integrity": "sha512-/9lDTFkhlGqEY42xJw0QAA0yHwPI4A/eNLEhaQBjfCYR6ukpXrDkwsfBXJtQIONmus1xqpQfR+QXffzucGIXHg==", "signatures": [{"sig": "MEQCIB7ZD2K91yNIHbSaWSUb9K0Bcb+oW0w5WyHiOC0yRbgEAiBxKBRhuSybAodtK6sosHrXkXSxvtSejQajaABMhxVeYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.3": {"name": "nodemailer", "version": "3.1.3", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.2", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "331b3265522f52b128bfc2dcaba9fe317b82f018", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.3.tgz", "integrity": "sha512-26w+wBPEmJiJHUBYBex2NhQwvRt/+9iZILy229FXx8g3BJ2ILwtALW+WC50imzU1puncA+9JYcq7bE7BTW6yUw==", "signatures": [{"sig": "MEUCIBa18dPx+DtCywId8qCD3c//+a5U5sBdoKKPu3HOuK1WAiEA7SQFTDA8FM+UZ+wb4Ibf/Ms/17qSVnrNAWILHSx+Ie0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.4": {"name": "nodemailer", "version": "3.1.4", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.5", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.2", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "0c969a7fb7990a1b1c92a9d9f5b0b84c41e4c1f8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.4.tgz", "integrity": "sha512-+pyPyIFZt3ZsUJvwY8dbBtfnaiNqk3Bga0+z159jGD3g3XdeO/tTaXWOjtDYhsIYjXfubpMIpgJvyI35lSq9xw==", "signatures": [{"sig": "MEUCIQCNdP0LgbWr31qLSqexSO9n1nkK5vsOUh44xK63y9bJUgIgCuo24FA+RBEHqvxhtMC8RfgOok7yyosb6d5p/j8Ah+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.5": {"name": "nodemailer", "version": "3.1.5", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.8", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.2", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "b87a7bdb6651fe72fd8099ee41e2e4ab60534569", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.5.tgz", "integrity": "sha512-pvxS3CtcQ79imLgD7ngmaiFU2iYcRvwUqw0amtYE/rMrTTvnYFwEvAAZe97wSmVc4ezF5iE1msO60MD7eV5ibQ==", "signatures": [{"sig": "MEYCIQCc/eLy2cLtq+Y4Qlcscu1o/jbazTOVTXZhreiBcXylqwIhAMJLDn4mx13p5BBJEx8p9hnCUY9GSxYLYZmpJ+jbeONc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.6": {"name": "nodemailer", "version": "3.1.6", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.8", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.2", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "5fa7eab70bbc19ba975cbd8997ae5392a74e4be9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.6.tgz", "integrity": "sha512-ex61W4tw6DRDvL7isS+kNxnIZtsNX/pLmXciXSlk7cwND6hqFGouaX7TLFvDl7gkjQTtW+tq3ABYlgPemsojYA==", "signatures": [{"sig": "MEQCIDoQjzLgEPsq3U+JAtPogv/+RHP2E3waJHNf/Bs9yMP8AiA/GNrKmT8edzdDxy3cp3MciuKY4qrD/Q7DrJ73tcdWtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.7": {"name": "nodemailer", "version": "3.1.7", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^1.17.7", "bunyan": "^1.8.8", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.2", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "1f03b649ef060f660b5413e47f77c24189a7dca3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.7.tgz", "integrity": "sha512-lFfDURl2pB+RUeY0R58ZFhjjRogFtX4FpjckStD9zn0AYUyo/JVFPh+Nk6SRJefywRh+wAtajnJOL1qBywhNMQ==", "signatures": [{"sig": "MEYCIQCJ7C0s/kW9opdNzZrqxt1cA2CvAP2AVcrblDJYDBYuDQIhAP2kFXy3lCYJi8kNhjuKFzXWGBJQGIvpY6zgCuED3IdP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "3.1.8": {"name": "nodemailer", "version": "3.1.8", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^2.1.0", "bunyan": "^1.8.9", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.3", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "febfaccb4bd273678473a309c6cb4b4a2f3c48e3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-3.1.8.tgz", "integrity": "sha512-F8lol0IlDLVXvXRHiqdA1oW0U4M3VHo4F/ONGdD0KjOt2/gZDbw1XA/Zf79NkK6H9bsSY51MFCZBaENTKqUZ2g==", "signatures": [{"sig": "MEYCIQDq1GyMZoBEeUeEoaP1JTUO8Nfsxl2G7NZVKVmvkhtUYgIhALyFlrc7gW3sVQQW/VBj/4Xp0q30bRW1o3gfbjw7QuxO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "4.0.0": {"name": "nodemailer", "version": "4.0.0", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^2.1.0", "bunyan": "^1.8.9", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^2.0.3", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "517a8f9a9972c6d1888c576bd5629bed41188d39", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.0.0.tgz", "integrity": "sha512-eniKG1dPUYZsnb0jpxKT3YinWuTTOeq8f5m21olzVcRK97YC4DI4HPTAtuk4Yfj5GPpnV/aShSjvp5BaiQyaPQ==", "signatures": [{"sig": "MEUCIQDKo/tqoPBMRsMlh6+zhtQYB9tOAqENfWG165YmB87X+QIgel4mW2GecyOpTgGLjayoP5Ab6oxbMYn5j82vV68/jkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}, "deprecated": "All versions below 4.0.1 of <PERSON>demail<PERSON> are deprecated. See https://nodemailer.com/status/"}, "4.0.1": {"name": "nodemailer", "version": "4.0.1", "devDependencies": {"chai": "^3.5.0", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.2.0", "proxy": "^0.2.4", "sinon": "^2.1.0", "bunyan": "^1.8.10", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.1.0", "smtp-server": "^3.0.1", "grunt-eslint": "^19.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.0.0"}, "dist": {"shasum": "b95864b07facee8287e8232effd6f1d56ec75ab2", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.0.1.tgz", "integrity": "sha512-CVY7O+5/AEJ9NCWDUWAp1MlQMT/8xRtZmK+H/7O0G70+6oKkfc7pt8mWN3+Ju/9lS/9itL27gUmD5J45rvxktw==", "signatures": [{"sig": "MEQCIB64ofmqI+LmFwts6ZjxggeTp1C0a4hruEVNT1yM2liRAiB70nLvIgFnh4hrXmV5I9BrA6qcX49AvVML23UlKn0YPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.1.0": {"name": "nodemailer", "version": "4.1.0", "devDependencies": {"chai": "^4.1.1", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.5.0", "proxy": "^0.2.4", "sinon": "^3.2.1", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.1.0", "grunt-eslint": "^20.0.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "e3f76bcad7376bae44714552571f5b0674fe469f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.1.0.tgz", "integrity": "sha512-pZg74CNQgnC0gZTfH0btXCxjKj7/2v5pea6hmMJ/iKyT48Z81TXZua7c65clwqKIlWfMfYBQG3OkrKxycIdXTw==", "signatures": [{"sig": "MEYCIQDb01uZq/eooNYQcN5Oi6WAJPyC7/GlE3ryIc3VROFNjAIhAOsQHQYnQATTbDyz2FfgWqIyThE57cRifMOXMcot1LWV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.1.1": {"name": "nodemailer", "version": "4.1.1", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.5.3", "proxy": "^0.2.4", "sinon": "^3.3.0", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.1.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "e84a65b423e5ca2b65b81ff39bb9aee71c3dc718", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.1.1.tgz", "integrity": "sha512-yLKoPqqcUiRc+Yrnc5B+BGnuU+LdkkT5ZZF0moO9l2nhvLUL+W3oxfwEaPK1+Aspf4Yx2tM//IZh422px9dxAA==", "signatures": [{"sig": "MEUCIQDx2P+97TmG2MCkXBeCFr6NLtiqBHVjlK7sVJ7hsxQLnwIgAXYmCx6Casp++HxiMwrsjprohbLyR8C3nn2QG7/CqZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.1.2": {"name": "nodemailer", "version": "4.1.2", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.5.3", "proxy": "^0.2.4", "sinon": "^3.3.0", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.1.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "82e1fb61ddc7272fe4f34c5ba6adaa99faa8b635", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.1.2.tgz", "integrity": "sha512-qDKhvCaGXjSL/cbIZerBY3Ftjobe3R005/uV92K5DqHlnO6tlt3ZS+WxuCXtrGVZJk/JzzA5CnNM9+7tm7klWg==", "signatures": [{"sig": "MEQCIHzhnj/ezS36r+yuDKu9+V2F2PZEgg7YZ541bJi7tpSrAiA4tCxzPI1qFG482z9G5U09B3TXWxJva9vPfxmMjzPXag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.1.3": {"name": "nodemailer", "version": "4.1.3", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^3.5.3", "proxy": "^0.2.4", "sinon": "^3.3.0", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.1.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.2", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "4125a6ef79ecfb68357a65c34e4810f210ae120c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.1.3.tgz", "integrity": "sha512-MSljy3ZNtch/QBVOwUNfsMLe7PD5Pdda2FRIDGhWp3mfYGAXEyWN5nuT3UguScxEQP3T7a42ySQKEqNSX3Yazw==", "signatures": [{"sig": "MEYCIQCLF2rhH/HLbvl9ZBkJ9+AroIQeAd1bw6neWSuKJtRJEAIhAKFZBC3Rr6Bqpse8NKrdym4w8HhgAa9oQ9j+oQnyiOuf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.2.0": {"name": "nodemailer", "version": "4.2.0", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^4.0.1", "proxy": "^0.2.4", "sinon": "^4.0.1", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.3.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "310781d30130bc5b7bf756bb626ec27564c5079b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.2.0.tgz", "integrity": "sha512-4ZEbC9Y4JLsCkU11tiiZnnGIcq2bvs/27c+ZAaSOebnMruh81dEhzViWoMvNj9dZAKHtNZ+qToo/UgRVDxpQyA==", "signatures": [{"sig": "MEUCIHZVKbh7ym3KBwEkyie8Oj4/Z7vtCyFbDDDvz9I0mOUjAiEAw4tFLd2V9onWaLNOCmdJK8AH3T0A2w9/nS0ZsA8/kq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.3.0": {"name": "nodemailer", "version": "4.3.0", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^4.0.1", "proxy": "^0.2.4", "sinon": "^4.0.1", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.3.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "59cbfcb7aa39dbc469b31439d11e2a3f924208d8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.3.0.tgz", "integrity": "sha512-+/fZUhto12S2xsRGTUQuzCVjV94f4knH+gbbVc3+zxrSWZUIKKBok+JEIcPPQB8ePD0eej2ZU/pT7N+t0DM2vA==", "signatures": [{"sig": "MEQCIESzgs9OrH62L3aRxuC5q0Gv9JZaNTL4ITaM3wIa/MpmAiALAXZCCriANjuefYUOTyGhk6K5ufcBLvhVMxsZ94XBIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.3.1": {"name": "nodemailer", "version": "4.3.1", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^4.0.1", "proxy": "^0.2.4", "sinon": "^4.0.1", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.3.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "40e162f54193ba2fecca200d544e8f6c1106d694", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.3.1.tgz", "integrity": "sha512-ngyDzou/Rbppn9WUOpWNoe25mRrW5wMwRokWanBNLt+3YaxOLmUtrc0ZtHMOgHGFPAYNgKA9H70ELlV3qSHL7Q==", "signatures": [{"sig": "MEQCIBGBv4CwRFVAcUBWuPqjUw4sFlcS7+Qgpvb9lOAl2HVmAiAOw6w/3qWVClEhyYVIRgKjg9DvgebXVr3aa6Uk/lhEAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.4.0": {"name": "nodemailer", "version": "4.4.0", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^4.0.1", "proxy": "^0.2.4", "sinon": "^4.1.2", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^0.2.0", "smtp-server": "^3.3.0", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "571989e524a906fb83b1518f3e4c0d3140db24af", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.4.0.tgz", "integrity": "sha512-d6E/wK/0Ijoh5v9si5nnkDTIPfz/1sCvwpIdqwoLscJtlNEp0ggysQxffu7tS/Bpv1bMNxl6cjgD3sL4UcYRrQ==", "signatures": [{"sig": "MEUCIQCe6DBLm0O+6bBpbfoYhULSZSFp+KAhi0WJ+Z0U+jJvCgIgMWCebG3RJUpWTMY2eaZxB5azvKOdWCpK/pg2WU/As6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.4.1": {"name": "nodemailer", "version": "4.4.1", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^4.0.1", "proxy": "^0.2.4", "sinon": "^4.1.3", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.1", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "ce480eb3db7b949b3366e301b8f0af1c1248025e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.4.1.tgz", "integrity": "sha512-1bnszJJXatcHJhLpxQ1XMkLDjCjPKvGKMtRQ73FOsoNln3UQjddEQmz6fAwM3aj0GtQ3dQX9qtMHPelz63GU7A==", "signatures": [{"sig": "MEUCIFhTLCD54Kj/PN1DASe2CIHfN/Qf3aGfOdzN4LzYir3kAiEAzOKpkoirnu+SYQKyfgB3fCx5ihQErz9BgtLCn7D0QpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.4.2": {"name": "nodemailer", "version": "4.4.2", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.1", "libqp": "^1.1.0", "mocha": "^5.0.0", "proxy": "^0.2.4", "sinon": "^4.1.6", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "f215fb88e8a1052f9f93083909e116d2b79fc8de", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.4.2.tgz", "integrity": "sha512-sstDxbCSPHDXrWRQhH++khr3yVU0CGvH2dCtAHOPQQ0lRR7xwq2txItEXckMpX481B/cN+0akER2bfExh7Gu/Q==", "signatures": [{"sig": "MEQCIAItST8eB+ovXfKihd/EPfZLacFnW/BT7v2sWxSRaFXOAiAy3/yGCKtaodolxqppFDMlcYvExFiX5PbMFfq/pg15Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6.0.0"}}, "4.5.0": {"name": "nodemailer", "version": "4.5.0", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.0.1", "proxy": "^0.2.4", "sinon": "^4.3.0", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "f041b5d545671f97a12e973a8fc48a3113122a5f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.5.0.tgz", "fileCount": 39, "integrity": "sha512-bznbREPZoRt3g400RdmVYXs2mbZft9Y1DfEqE2GS0VcBwU8DiEMDQ2q4gauSltbsnXtIVHtKH+VmE80I35TxhA==", "signatures": [{"sig": "MEYCIQDWIq9cC2SNmbQxHn+Je3zo7vjKEQ1fgIFycPVMb+V5agIhAIZkx/0+zZ0SdfHyyzcLdMMtCKLw7ergF99SBB5JGs7F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 515478}, "engines": {"node": ">=6.0.0"}}, "4.6.0": {"name": "nodemailer", "version": "4.6.0", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.0.1", "proxy": "^0.2.4", "sinon": "^4.3.0", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "b99391435aabbd34b74a1283112fbdfd34f7eb8f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.0.tgz", "fileCount": 39, "integrity": "sha512-yYTtNAmukmu0qI/zAYGggOvOdMgwacp4jmvCyXrGao/84Elz+2lDGOhVg6bkvOhkRK/hnun3XoJ+AV7w8x4wbQ==", "signatures": [{"sig": "MEQCIFhC8OlmV9Nm4m/Nc1yp2fAWUOR8iBOefU5uAUBc7XXDAiApqKAR78nig4Y3Dei0RUMbjXWNyL9gHYoJnLPypogOlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 516625}, "engines": {"node": ">=6.0.0"}}, "4.6.1": {"name": "nodemailer", "version": "4.6.1", "dependencies": {"request": "^2.83.0"}, "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.0.2", "proxy": "^0.2.4", "sinon": "^4.4.2", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "304e0857b15bbb0a13554d50a69536d7ccd41c2f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.1.tgz", "fileCount": 39, "integrity": "sha512-CFThG6WHP/Jy9hbUFkjAFTcs/QVxK1xO+t7S9gvA1Uv01R/PiJeUiizS2LnnR63xhVFQva7iE4mtVhTiVucuWQ==", "signatures": [{"sig": "MEUCICEC0nSWPgCpkdJyXH5MjC99zPpc1qBE0wcsfIc7zK2HAiEAuJ7c+6VxeeyaOwzaOWSx8MOETQ+QP6tdlHYFll6wH3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518094}, "engines": {"node": ">=6.0.0"}}, "4.6.2": {"name": "nodemailer", "version": "4.6.2", "dependencies": {"request": "^2.83.0"}, "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.0.2", "proxy": "^0.2.4", "sinon": "^4.4.2", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "1d0b34691d9f4b7ac5e6c240bccc1c9d025e3f67", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.2.tgz", "fileCount": 39, "integrity": "sha512-T4A1m3YS2d+4RFPhbZ3pVWCpU6atMrxrV6Ql7n3NosDYmIdmxknSc715u752RzzLnVHSk/6zwtVD8IJVAGCACA==", "signatures": [{"sig": "MEYCIQCM4r6c/9rDV/Tur9yyEtuKxVGKYZLqHj1UWWooWRYTvwIhAKPH3jGoEsudGwra5cC/IcyOM+RT3Y8VGOmJwUrab7hG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518308}, "engines": {"node": ">=6.0.0"}}, "4.6.3": {"name": "nodemailer", "version": "4.6.3", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.0.2", "proxy": "^0.2.4", "sinon": "^4.4.2", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.1", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "c3b7e97fb72f46d4a475c406a15ed23a45dbcddc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.3.tgz", "fileCount": 38, "integrity": "sha512-1AmOpDZJtyPAO+gfUBfT+MWHbYwQ+DZvb1gvYaTxBZV/lUeysZIt4kDq8Dlwt6ViUZGp3cMGR+D1MNQYyYiVUg==", "signatures": [{"sig": "MEYCIQCjtOwES3+BkRw9P6ehznA++wgB5F6RQwbMMlCxU5fwpAIhANjLK8Gp0Fi8uNzyxKezHKRkHKD2TlPGVKAq4nas4R38", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442124}, "engines": {"node": ">=6.0.0"}}, "4.6.4": {"name": "nodemailer", "version": "4.6.4", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.0.5", "proxy": "^0.2.4", "sinon": "^4.5.0", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.2", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "f0d72d0c6a6ec5f4369fa8f4bf5127a31baa2014", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.4.tgz", "fileCount": 38, "integrity": "sha512-SD4uuX7NMzZ5f5m1XHDd13J4UC3SmdJk8DsmU1g6Nrs5h3x9LcXr6EBPZIqXRJ3LrF7RdklzGhZRF/TuylTcLg==", "signatures": [{"sig": "MEUCIQC9Lb4D2LSuvydB460Sh/To2NclRaYM1QtLQRn186pqfQIgbL8Y0gLdhGoYCkCeN1YmcdrouEhB5SY00EjxFS2VEXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442441}, "engines": {"node": ">=6.0.0"}}, "4.6.5": {"name": "nodemailer", "version": "4.6.5", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.2", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^5.0.7", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.4", "grunt-eslint": "^20.1.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "306ce18c4543f3f06e5f76665d3cb6277f872f53", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.5.tgz", "fileCount": 38, "integrity": "sha512-+bt+BgmnOXDz1uIaWXfXuTESth8UHkhtu7+X8+X2W+CHAn0AuuCyCk854qnathYQLWEC2jkpx7/pkVHcfmLKDw==", "signatures": [{"sig": "MEQCIFPui3CXMxD7bQ23PnTak87zjrH0dNU075Vl65G3iOYaAiBUhtCkaEOklAXHc7w+BuixZRQ7FYZ2EbRxcxlZL+MslQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBSaICRA9TVsSAnZWagAAnjIQAI3DdUcnzbWGb5avDZJn\nLVQZAdKhMADlX72G3IMYQqiPH+1RiIAD2wQgiK09lOToj06zOMTp4XGTTpF9\nTBHLetp8CRPG8eTWAuqv4FpMLgPnV+r6z0KR8ARXWmYs8rlm+uUqceYMlQtF\nzXYb75uMZ1Z6ceFS2SrIBI1BXvAwXe6pUNxfp8VFvs7pTr039MP2Kpgsimg1\nWk99rRTd8zAATR0hNsXtdoq53HrRaCM9eFX9LTJOa5umqjIfScezuBT7xYaF\n3L+NC8665jDyoIbmgHsD2ynYhr7wRyo8GlpSZwfCQkTONoONnlTlK1aMvFU3\n93B9UuVSigm6WZoTX1g2MJYcnxIkPl2s9LARev0LMUaMtLmi9z+T+goSs2kk\njF95B+wcAUQH5Ie+v47f6HWctDIL8nJlESPaw3DYPK+rhcFt2L0iMGiKOFLQ\n98FBiJo/1VdfhAbRCd/Z18zXWfzCjrjNI/FdWa+mDDNd8ljYSieRsOGQsyc8\nntImOqW8Obsb5PWpoJjOn2nRX632ck7dB9r+fybP8Lewke0Q2681kuAuz/nV\nIPQB+m1vN6DK+DeHqrSrq+pB9BtPTCkgDpC18ioZ3pSg/QjgwKwfa5v57b1K\nnLNXAaBCkWUU3vQ+vCzkSFTF0bRl8bjFg2x58kTLiJNIXcSOlXCeReY/tpKW\nQ7f6\r\n=S9xO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.6.6": {"name": "nodemailer", "version": "4.6.6", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^5.1.1", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.5", "grunt-eslint": "^20.2.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "9ba4be51ffa5da2b2dbc392b87e30bff7166e139", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.6.tgz", "fileCount": 38, "integrity": "sha512-sIcM/Do0XBJmu7ruENLoR+4TCk0B4C1ftqRjWrVeoez9Dt23SmL9bXKqswVqyxuT/RdK8TKWciZvxHykerXCRw==", "signatures": [{"sig": "MEYCIQDmSrRud2dmG+kJL95+UsheeCybh+Bmnmxmk/LNlHk0PwIhANQU8C5tyKpTbUpBYlCwmHJj64JzpwRcRYimdTB+Zt/p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHQYwCRA9TVsSAnZWagAAt2QP/i6esit+3cv+6jCc0CDE\nrcivKhFPpHnrciwc0RcEdGeFmd1/PMlvqW/CPKtC7KBVz4Z5h/gySI1OWVIs\nmcEJ0jatB7UMrcETbPwdwViPj8NTZVHsGtfwtFubCDij8K1WD5H0y3z3i+J1\nMZIyO9Nc26mET/5HGDwLqpcB4rMAbuigcpR/XuJeIr7GO+Zf0fjQUF6ihFiz\nKc1Ryz2163kcr5QcbXOHfSUQ7+ramq81v8Pb/B71CzJcggwgiqSVglQjH0LY\n7VTSZLLGAjRc/S0L7Fw3qK071X2R2hDif6T1V2vxMNZwOSWClkDCuV+TC6+x\nlo+qAXX5px5kuhsQh4xJVG5SE4eZ4aAPapULbLHj9oGyHAGYVROXP39mw632\nUBQD/lB5piHs1xTve2Qhiz8xYCrvItW7pjJRQ08LYQw9mLFb3zvBjyN2rS6Q\nzwWEd44tfdiPVpWnLJGw9Wbg9s9PIEW1O7pl1VJZNojQQL4hptKs+195IeQo\n0SMxl7ma+gQ6ea0phYtJj/X/cciCKJlK/Rz9jg5nM+Q8c70he5Q3AeL2xArR\nm8i2nk8QwkyxBofSoTL+/sspK7q/24JJaPSjkl9KshVs4rMfkeJ1UKfGITeY\ntOpPfvk+f73+804qulT2sIIeD9+dOYPRIhjRL0BmLWOH4ljz/fdBgEMDacSJ\nQFL8\r\n=oV/s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.6.7": {"name": "nodemailer", "version": "4.6.7", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^5.1.1", "bunyan": "^1.8.12", "libmime": "^3.1.0", "grunt-cli": "^1.2.0", "libbase64": "^1.0.2", "smtp-server": "^3.4.5", "grunt-eslint": "^20.2.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "b68de7f36d65618bdeeeb76234e3547d51266373", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.7.tgz", "fileCount": 38, "integrity": "sha512-GIAAYvs9XIP1fBa8wR89ukUh3yjL44pom5LKY5nTZcL+Zp9sRkqL8wgskyBQECQg9CPsDX/fjTZx8MNz20t0jA==", "signatures": [{"sig": "MEUCIF2drFHT//Xii1a2GEMH9JoF5QsepeZZLQ9bGbKwmk3+AiEAzmAIZV4hgqyju09jPziHbvkac/UXkE/T5L4jE3SJm+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbI5sgCRA9TVsSAnZWagAANigP/ivynWN7GuwZSYQMAbxd\n9MlwAcr17XcHr7g5YLRN4+G4BjSXG5m92OuZDjEHSpdX3NX9RnxFZwYCMf5C\ny1txbD27p8zVSWfyM5uix0XbMvvzh/OdwsYemUEYz1mRdItlvxaLdYF+YWnF\nMG2REkgpssmeNIydeUKu5KTbH9vTUdDauwbMCm1DSPvNfGH6tbKNMjTkWa34\ngU6n8uvhXn2LG7YB8w/+rFEuLE0NMSSSE7hnMlaS6bbcDx2YMZAejQzOfJd2\nrjhPcFHhQc1we4SbOZFuYUnpq+ZSslNqw/jmEPlY7WW/EGJS+7QfFofzoB3I\n4Kgj2qR/vKo4kx+5bBPAq/BMchAgcNdjM58Vsd5bu3D4KVYJnkzUm1bun+ZB\nN5THcfebKa6F9gGqrkpGA04+EYXswTYMd1aqddlbpZIBv96bD1IjaVaA0xju\n6GWCExnBNJJ3Hwx5nii9eDM6czkhDDVltBpazHjlBJPlX3gzW7stLLZoGfAu\nflNUCuDbk8f7VP2xkbpk0F4MykBoqEPcN/W45vazbeT6GiWB7ElmAF86eimZ\n/cIFDiJ2qzs8g+1RBm0xIjUmGEG4hk1Bwlw9NaCu1r2l1Zt3PhbaxkQeuuGW\n+P5+fvGpRuCMW25Z/5dNCk3Izbi5p9gCo4d06Y6vHYUp8uWpHjxztiH+CC8i\no1TL\r\n=rmIq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.6.8": {"name": "nodemailer", "version": "4.6.8", "devDependencies": {"chai": "^4.1.2", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^6.1.5", "bunyan": "^1.8.12", "libmime": "^4.0.1", "grunt-cli": "^1.2.0", "libbase64": "^1.0.3", "smtp-server": "^3.4.6", "grunt-eslint": "^21.0.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "f82fb407828bf2e76d92acc34b823d83e774f89c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.6.8.tgz", "fileCount": 38, "integrity": "sha512-A3s7EM/426OBIZbLHXq2KkgvmKbn2Xga4m4G+ZUA4IaZvG8PcZXrFh+2E4VaS2o+emhuUVRnzKN2YmpkXQ9qwA==", "signatures": [{"sig": "MEQCIDlFTD3EiiF83OKygp9jhBJ/EAXErSb1whTvAs1GjLCSAiAN7rIhJ8C9Pt5HzipjzOaeN9gQR8bVw9c24OC0YpYo/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdFjyCRA9TVsSAnZWagAAkckQAJJt6B/JeTv3Uobqihi5\nCqTIUGGqwObgQjfmlkt/qBMBsjjhVyMuej3gNCErOthNROwqzUEZUYuKO1m6\n2gfTndkvu8Ofb9WHaTJAkdFMDsu+AR+IHRGYu9Za40WTMt8bnbG3rKIdYTsE\ni8XSd+qgjM/nTvYRlKC8ayR+fk2iRY0wXvWJiTnxNEEUSF5oduU9GZuYUb0O\nN5Qvk8uPtji6rxgKNRCd0U5/moFb+y6wAMKxAMnnIrbKzvZS5Mdx7tZC0/Pi\nyn6wHW4kNvPpcc7E0cilBtWLgltFtIEdJLt59ERanBklntxLDNaxkrj/YqrP\nqOf2/PF1OLLXKLP9pjBssiyU9lylrGm0pz1agExPLt0qsIUPkAClewcA/ich\nfLC9ZHL8Ljrki+DEW6+431WjRKXmaaR7gCP7+rLF2Fb0VyQmHawpoILrvtSB\nDmh6AV6Fbrvyqm329z1Yp3YVUne00bnmeORyhu1WiHYTIA4kMS9v8h6KtqJh\nWHuCI3YvwtuG9P6El+5gqE/OaEcR8oO3BZNqFHxSNAxaMxXGx3WVM0FLzWsx\nzAFNH6ijDoKSxafhlqzf8ud6Jyelcps6UsXrJzsgoVSoNETDKoLQbe2XNWuz\nmQuHW/KKxCUA9whXT14mo92J5XecDCqYRaHmE97H9DV2xByhemdpGga7wl6I\n3B5P\r\n=yvh8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.7.0": {"name": "nodemailer", "version": "4.7.0", "devDependencies": {"chai": "^4.2.0", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^7.1.1", "bunyan": "^1.8.12", "libmime": "^4.0.1", "grunt-cli": "^1.3.2", "libbase64": "^1.0.3", "smtp-server": "^3.4.7", "grunt-eslint": "^21.0.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-prettier": "^3.3.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "4420e06abfffd77d0618f184ea49047db84f4ad8", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-4.7.0.tgz", "fileCount": 39, "integrity": "sha512-IludxDypFpYw4xpzKdMAozBSkzKHmNBvGanUREjJItgJ2NYcK/s8+PggVhj7c2yGFQykKsnnmv1+Aqo0ZfjHmw==", "signatures": [{"sig": "MEUCIQCFh/wJSkVEwiCmedoTja0l25Og8MYpP4dG9YlKvri63gIgbgk+VI3XFm7xX3h+YNqWr2XM+y7cHbK32n2IRYhlY0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 446716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8qwZCRA9TVsSAnZWagAAPrwP/j3owBHRRC/pMAnpsoUj\nNHHS6Yf8r86LUanpVHrrpcsanne0tHu6Se+GA/Jst8DusfNAczaOc4io8kHE\nyNiAgM+s7yQXZxznQ1prpvEKheWmprgROBWnLJMHQWoVDH2Upn/Gmyyodg/E\nGgVklUhKnA2pZy8Qfxnf6nQa9W/qMosYyQjAZvV8oYUQZtm2OvvNmjrXvZxT\nZaj4MJ6caACfZiQAniAEc6jAhncTI8gEW3On6xl6cZ34eofUDDNherN6VWVS\nYfJ0o499WzXt9K/CKlxkAUaaeO0g3iWjh+ki3LINCBGWc4b7OftRdS2H50TF\nwFgvdds8wbK0MdW8/jPuTDSVA36Pdc/8nmP5z1DnTrfT9AUD5hmoed5xHXM1\ndgkcOed5wunQqADGa4qwVwLdTyOE16es+3nKxjB1MR+AZLEPem+BBIFuJubg\n8qR2Zk4hPLlZXkVuehDnRKYOeRZvc2o1yArC7Ra/uKKdVa7YcZCoLdMx36RC\nKpH99Vbg2bA84k0HxqVYnEBjGj2239/OU6KXzxr6VvLyifXHR+Ef+qWE6609\nxKo26N+jHfpz48jk+9l2FolYls1YlcF1v/Tr90U2uN/OVnBA6HW3rhG/MEFp\nOwSX8EO7y1jsXWD+bwY4IkeiKR+NpdVNN+HTZv+6pF7+lB/gFMVgYSpBxLji\nUG42\r\n=qBpK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.0": {"name": "nodemailer", "version": "5.0.0", "devDependencies": {"chai": "^4.2.0", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^7.1.1", "bunyan": "^1.8.12", "libmime": "^4.0.1", "grunt-cli": "^1.3.2", "libbase64": "^1.0.3", "smtp-server": "^3.4.7", "grunt-eslint": "^21.0.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-prettier": "^3.3.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "bcb409eca613114e85de42646d0ce7f1fa70b716", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-5.0.0.tgz", "fileCount": 39, "integrity": "sha512-XI4PI5L7GYcJyHkPcHlvPyRrYohNYBNRNbt1tU8PXNU3E1ADJC84a13V0vbL9AM431OP+ETacaGXAF8fGn1JvA==", "signatures": [{"sig": "MEYCIQCl23poJ1U+xtSqMg+MRK6WNqMQweQrgKEKLf1G3L5VFwIhAPyoZEdDuROYUMMJdsSTb7jCFAshhYWE+PiFcxnlqqha", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 456090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJf7XCRA9TVsSAnZWagAAgMsP/2M11mKok7r/Cbpg0n9C\n5Ml4O4sHfg/1wEzbn5NCLJl61YWKzqZN5Ft3orHptZ2n6yx/5+XG31fMsROA\nnDyLJRx9QQWhIoNxNbpiDhQpjGwdoJkaJIvZ/hctXm9Q/9NyylZI+nvMlcw/\nwVbye/6xXio/DhH2mVD7aCzT/6HLnRoeJ3EGOheVtOonAyAw5l6QuHHA8pvJ\ncMo14aX0DIr7ysAVQ+GkapwFUUXtaiwFXWquktOYs/nlgaKYaq7c+lCxLUC9\n0eEo4mkDz8Yi+pfzpFsPPFLU+OooNqPNcOHsxtbDyiOmAfN1I87Qyt0ZC2yG\nTHHWufOI/MdfkC9+qLFZZQk+yS4VycbeAySFEBCNQ6H9Uw1zCmwqGpXITOni\ncWcV5nCDfD3BkU5Jy7y76LUZJY2oEyPjQpyTZfAaDVdd3baSsahm3D+0t/aP\n6KYwpKvZFKYTBje3GAGNMJUHn+ojvp72YFnzN7Cxum9Xb8mVVApWtR1a0Urj\nHgiRvQAZMRBREQHpg/jFX6k2N1oVCgGoHs9nyaCWvl7E3g5ZYhcTSB0xzylR\nhwO/XnXIBGnKoyR0D/+UsyQvb2/foCVEheTWFALDq1u40t1cMB29K0v6zS3o\nR85iFhPwVoNAn0EeD07rS/ft012i5QIvv94G3nQT07I7bKwDFraAn5Osj1op\neUbw\r\n=0pBT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.1": {"name": "nodemailer", "version": "5.0.1", "devDependencies": {"chai": "^4.2.0", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^7.2.2", "bunyan": "^1.8.12", "libmime": "^4.0.1", "grunt-cli": "^1.3.2", "libbase64": "^1.0.3", "smtp-server": "^3.5.0", "grunt-eslint": "^21.0.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-prettier": "^3.3.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "39590e1d324512f641de3756f44f87f051266520", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-5.0.1.tgz", "fileCount": 39, "integrity": "sha512-S1jpwJ5XL7KIqibINuyYNJwlaWolWDfeljLU6UIhmuX+rY5xT1/z33ZUQLNbIrTlQYMo4F8GZOzKLp4Tlhf1CQ==", "signatures": [{"sig": "MEYCIQDpMl3Vyv3GfdoyyKIfuUB8vkMn2J+GQ9LUtR1kpDnewAIhANHb2WuLKehH8NPTEWlDxpEYNNHF/yZhDbZXqYiNrm0m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 458423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNR+LCRA9TVsSAnZWagAA9OYP/3YDgwnAUM71bX4G+dy5\nGtPRKFb8saTtxDIw7HSdaIoQzpaQd3YPX5n5taXpGpqIZPZQOZU5nQH07ZR7\nQPUdvvB5CIkh9TWTdWAOj03OoZXfC06yY8VpbN5q/2jr01eoKAPYPjfOQ6VU\n4V7cIb2Yq7bI+23DcEYRgODDA3pmMix68njqRZnKkmyuecWvdY5ieb8bhWNQ\nbTU6rnC+GXd57L0q1zhZ1DDbOWiz+bo/J0/7UbwXiegxFYPfDQRvJAYkcI0b\nXeOH/H4TQL3XAxiFdP9X0CGmYnlq5FuEnxKVVqQFp7j3cPyO3SyLobRyX7Zz\nUWo8LQGtkHuRlB1mLvCEMqY8cNhjRd3Tt6yOedZM4rV5rZJw3LSARC3YAB07\nQ+Yge+phEKOQQK8Eq5ZZ11ur3agn/HjmZDtcBSbgv8UcdvvFtvfAbzU+CzKi\nQot/Ozke8FNwovZM5eWXQbFNnRZOPrPajqFrBM25x4ScB45oqRim4E7O13dN\nwM0AU7DluFaW7jJiITSmCSk20Ep7ud4GvvMm5RKs2bgYhozx/kkzwm3DzjMm\nHlFYzkhuouOmE8z9lP54gezxa06Boqfm29Zv5+pYdn84FqkVQ1A47hUlUI00\ntlYeEOckL/OwajWaBC/1U3Ig1VZ3Y7S/hmspQTN2HHOQmtVRRV22cdHpY4TS\n21a6\r\n=9GzK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.1.0": {"name": "nodemailer", "version": "5.1.0", "devDependencies": {"chai": "^4.2.0", "grunt": "^1.0.3", "libqp": "^1.1.0", "mocha": "^5.2.0", "proxy": "^0.2.4", "sinon": "^7.2.2", "bunyan": "^1.8.12", "libmime": "^4.0.1", "grunt-cli": "^1.3.2", "libbase64": "^1.0.3", "smtp-server": "^3.5.0", "grunt-eslint": "^21.0.0", "grunt-mocha-test": "^0.13.3", "proxy-test-server": "^1.0.0", "eslint-config-prettier": "^3.3.0", "eslint-config-nodemailer": "^1.2.0"}, "dist": {"shasum": "e1d3056222fa8b10c6cceafa10a967a13113023d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-5.1.0.tgz", "fileCount": 39, "integrity": "sha512-g72Tr4NZXK0PC5szgXZfph2tpVsF14pxF+QthwK2RdVbMU8RleioODANvpze0WvgNqB/M7kKy69PpgbuUHTAxw==", "signatures": [{"sig": "MEUCIQDubnKGoIx5eBUzPRV+FzxIiagjAjyqjeyJJVv2PzlZJQIgYmr8TiZaLl1xP81AgXUE3GWj7JP2raUGgpuzPEFpYQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNboiCRA9TVsSAnZWagAAuLoP/jc1m0pwkK1n4txCERe8\ncT0FAJVp1XicsCJzLUjW3lqM5Jvp9/cVvj9NhCEugbPOsBD7eWqJDuO0OQ+B\nbswM7M9n1l05RKp9T879BiGLe2Ax7f7+rDHjnns/X4/vXn6Kdstcasa9bHck\nxW5yBLFN3hP+EfE5HWvJrn1dADQ+RPuD1IoCIigV8d5NBIgoImlGbA/BM4yf\nPkoXprJNSLDkF+jPSO53ZgLLGbfkv4stns7ZePpCK1gg0ydgXxKEg45H9l3R\nfJ9NCXOtJ6d1uyh2W+IJ8PuKVLNN6cuM7/FYZNA2gVv5xJSQdVCDWBhpsiCb\nm65rbEQXo7/YTp9P+95IcmIMShtPAGBskDLdmoDg/6EnpKvZzVTfIB/9wVGI\n0l0UlB6eFz70CeBIM8swIoAI/kOWgnpCFG6zXWxNSGoRsP4/lYqFxWsVdbIe\n0PGg7ZYgfiVwQzxqvuWNv9O5s3OHJwnH6qMfoNFnel1pZ2si4CpBaiX9Vy7f\nOFRYi0rm4xSIIY9b5tOHJPBjdCNSUGw2C0Gdg3Na4+rgcPlEHPfVI3PEJWjG\niKrZ5bOmZEMVZVB67Q4y5FPA54te8pSF11DjVSpbUvoisEvctu9/892Ln7VA\nEILwu+arKaLf3OHHkWLDZprqHsPpoLpdkzvZGKFjAPYtongtr5CdDBM59ABR\nvcC3\r\n=yPJs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.1.1": {"name": "nodemailer", "version": "5.1.1", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.3", "libqp": "1.1.0", "mocha": "5.2.0", "proxy": "0.2.4", "sinon": "7.2.2", "bunyan": "1.8.12", "libmime": "4.0.1", "grunt-cli": "1.3.2", "libbase64": "1.0.3", "smtp-server": "3.5.0", "grunt-eslint": "21.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "3.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "0c48d1ecab02e86d9ff6c620ee75ed944b763505", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-5.1.1.tgz", "fileCount": 39, "integrity": "sha512-hKGCoeNdFL2W7S76J/Oucbw0/qRlfG815tENdhzcqTpSjKgAN91mFOqU2lQUflRRxFM7iZvCyaFcAR9noc/CqQ==", "signatures": [{"sig": "MEUCIFikYP3JvFoevGQ3paWD95hVvywTwJPxvKssTlGxNXGJAiEAudK9ymE3dJFjrzL4HMdxqPK/jVsLcMwiTNwJ7DdNv7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNmHJCRA9TVsSAnZWagAA3r0P+wa1qWoMA6gNCbt1Yiix\n+5hUsLnW+IbCG0DT5waZaTQ8+QUQsxH7ule7k3CnQ8zBV6peYxGLFc5WZPHc\nbkm1+Ui1QIM63X5i7/z5QXBeydiOLj42SPdgDrm96707PaWNrPaYGLYfYipG\nB4mPY0sFkVPygb17X3vos80+BRzGjtGriyseoDOhjKneFYFHPnaOylRQsdPH\nH+E5ZK6kezSs9NQMZGY/Pbo7Wyp+2fYeFaN3uO0S0ZEGihas2RBt2ejTsTh2\n0RE0t9a7koGjjh+JiHIoM7qT7J3MA4x1rj9LObbyGbILZWm8n8gcTWsacZTp\nK4CcCPJJL6ZcpykuAeOBeIfFhkSFFaY0A8dt12xie/XdzeCKqfDdexXvSYec\nCLO44Ogu98qrpWdPN1cQJA0f7gkSAGb/xeUOKKwW6WSHSpbsy4+5zxFYpVlu\nWhJmJM8+iTzU1TAbXJkH6H+Nj7UZ0DhB86vdnr9tScsUj3LmjlR+maENP8wo\nBjRFnY6zbj0grJxbzZ+46DaXl1/Gs4U6z9iATYqeWbgc+EAiUIo7+tVuIc+8\nrXfWwrryxgTIK76kFVsxdOdIxI5v7a+Kx32l7rz/CQgSgEC/CgAcqxL/viH4\nLWnLtFqDKay9T3sJg0KQ1jyVbCbaCqFn8taYMqAGKA/duuqViGimqK34JLfM\n/XtC\r\n=j3WI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.0.0": {"name": "nodemailer", "version": "6.0.0", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "5.2.0", "proxy": "0.2.4", "sinon": "7.3.0", "bunyan": "1.8.12", "libmime": "4.0.1", "grunt-cli": "1.3.2", "libbase64": "1.0.3", "smtp-server": "3.5.0", "grunt-eslint": "21.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "4.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "d9761128771739dc87c1fdd747f569b7f135cb02", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.0.0.tgz", "fileCount": 39, "integrity": "sha512-PMQJyLhoNAMoBU1hEh5aaUkpa/tcDNwzS7s7zow/myKfoEoZewMxUuWZqQ5yjYsAnvE484KSkYH5s6iPvcjhCg==", "signatures": [{"sig": "MEUCIQDGcI+UkxEJ+rpUgZP8Hn2fS5VPQkqcaJCKIxu/A77CTAIgSclChfRPrvYglcszUFxSWJWfnJNt686XGIN7GHb7f/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmKpdCRA9TVsSAnZWagAAGUgP/j7am7qfK/HhWOMJFbyv\nfAR6OuOe1jmGkhbsvwcuTiSNkb9vHzVb0hQ0l9ZMwMPXZWAqnwalFKvOwrEc\nwt3aWdWa5P71CVU8GK1evANfqrVh2DPGOyU/uj63gF82AuklZ6hHPiAqGlcq\nCJYTD4wcXw+mZiD2zh7h7IOoPLwNbpULICb/q9oLXPhw9WHyi+5E1sJzv7Ms\nnScQwMDYxvHTSD3/gVaMWWlgbmmmGxepU0lpCr/vZqNxwhgeUaeyCOsdl42T\nAsPeA4lyjc+DvwUgyz7jRsOpxT848S/zfdHP7QeHq7dTKVnf8SG596I8HorL\np9e+CuhUjpmfplcwlJLtV0QI/Zy0IHgz58qYuJY2MkMaw8gRTVrerRP+HsMi\ntfA4WuCxNBM6x/qdNL/wov4PTCdd1SzHQSacZUB/8qtfI9ch4//MzmzhXe6J\npdjzB1JWCdeMLCqqPYAzI6Lv3EBdtr6rn+XSfUJW0gaJkGLDwZQOhm4wqgYh\nkUg0Wk0GWRVmXlVdfzLgs1OBZbzkYuaI4NOIJ01emcZoqmVNp7l6D8mRsLhK\nfOsLRdJrFhC4uiIzC1v3swSGLtq/ROIXcZ8rFM0ZlBER/N6OW2YudNJqwTJU\nP4p9692PZga0+c+UA8PAOxHTZ7ccfsBM7JqR9XvQxMwxam7dQxiX5qi1BGDE\nR4nD\r\n=0BJ9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.1.0": {"name": "nodemailer", "version": "6.1.0", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "5.2.0", "proxy": "0.2.4", "sinon": "7.3.1", "bunyan": "1.8.12", "libmime": "4.0.1", "grunt-cli": "1.3.2", "libbase64": "1.0.3", "smtp-server": "3.5.0", "grunt-eslint": "21.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "4.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "81a918857f2f157b66bdaca528b83682505ba98f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.1.0.tgz", "fileCount": 39, "integrity": "sha512-mzKGT5Q1PY84v6oRVjy88ymMDLUbPqvIr26n9Uy3j2nXzdhKWx1z4GLSHOyX8655zMkQng1MFR3lK+cE1egS6Q==", "signatures": [{"sig": "MEUCIAYKMP2y0UakxcMGXIhrewrlixXfk4w8KLlogqYhEbUJAiEA4ePMmFYoFX5O35TExW8fkzGEM3XQKBfZXKjrXsN54Dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqL4GCRA9TVsSAnZWagAAHy0P/3NU2LUR8+YqCpIp0AGH\nbMtwtgaLq6vVdK/xV1gxEYKZTU2cEZyEjXpP3U5WX1E4Nc5BkriHIdqW+7XW\nNNT3rFvuLzRmpN8OWQZ2xf4+LZWFeFLRypVaQwzSlYKwFsMluxgGP4P9AVZc\n4N4xJjwEhlPpEo/qJXNT6MlqI1iq2Ev8F7sBmyCv4BBI8ZNYlz5OTq8DPTqL\nqCsMvgvS40MsyR269qGQ7zBY4rbXsLqaeZNjIWuMYPrNLmlGIhmR22i0QWAH\nFSlMYzKoeyp0whDS/tGfqpDhSQjy/1RrzOMEmlep6kKoj6zDs5+7RGj866ro\nDUafGHHo9MfLFoD9xi5QAbVyBmFc8suP58MyRNCKlmJb1sXBMeDhdf12ovUS\n9ee9I5L5vzsrOMjsTM87iussfo+aYSN53gpN/l1DtrCHxNUSoX/0PBZAfGuC\nOTP3eyWilxA4jbheic4HBFHNv1TqWCmPcTAFv+jKdEQGSfRhz1MFGLbGWykm\nUAS23qG1usqkw98UTBihrpZDN3UXI1ziXVHw+B9pGxvNmxfLAdxlk7gNkSUe\nPGEWv7a5N9HGfNGtQe0oQ4qiBw9/WrJrTVQKBnBGQHLqpdl/vEk4rtvZvCuv\nUdv2hBhwQNc92L4SnFr4rVbmw890X0YNbPmWD6K8thyQe79DEjZYZHzZJjun\nDgPE\r\n=QaAq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.1.1": {"name": "nodemailer", "version": "6.1.1", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "5.2.0", "proxy": "0.2.4", "sinon": "7.3.1", "bunyan": "1.8.12", "libmime": "4.0.1", "grunt-cli": "1.3.2", "libbase64": "1.0.3", "smtp-server": "3.5.0", "grunt-eslint": "21.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "4.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "09e88ef4b3646f01089c5d84d007b872141fb575", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.1.1.tgz", "fileCount": 39, "integrity": "sha512-/x5MRIh56VyuuhLfcz+DL2SlBARpZpgQIf2A4Ao4hMb69MHSgDIMPwYmFwesGT1lkRDZ0eBSoym5+JoIZ3N+cQ==", "signatures": [{"sig": "MEUCIQDMWvCvjSNFb/+KTP8WQfAmNOu71Oe19zmJ5EeBnYrdqwIgBNvS3YVT0G6gcgKOr3cyZvChpthJcmtrCOOpmyq1HRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcusmsCRA9TVsSAnZWagAAfSwP/R9WeRSHaLY6VquwSUZ/\nImI8O9/Tyxwv9qB+fFn5JNi4BUMA1aazUytVR5Td1YlfWJA6EfxHwhtYsy4X\nlEnk5h7cr6i5MRgQF6lngD43JMjOFYJee2UHELJzETEloHR1fsiLnIhOu0l8\nDz1XwKZ4R1+eD8N6YnbzsTZ7elenTAgwatGF8cBFbrRjtxTJvhHGJ0hwbTXH\nDIsj9WereJIp8RUnqQ4ct8Vq0m6VGybxkYJuqLwNN7VtpHsDU5EgJ2d/qof9\n0qWoOM84b+A5BnQYZPMjHx/LEwdV8H3UErjuuSU5vBwqZ1MrPC8Q0FC9gx3l\nWz5jwtKXJm6t10AQjlNsn+r+G0qa0gJT6aonAij6IbHQbEfgBdUUE0SSmom0\nfweGeRkd8jKV0l6zRaxfr8cTLHA3ZoJIhCjONvaDHpFWSLxmQkUzVTPeGaDV\n7IgM4kDnMuw3ec7fO7pGPmpntZH1+2Fkzd6zCo5ymSuC6XgeI9+wh3BrUifU\nDlGTREUcaUu2AAkFkVqqayg40gHaUQRpi+ddOWygbEao1oGfUPo5wnB+TEaU\nL0+V1Jwznlncp4Q57ngOs1z9jq9SXwjRv0Eh+A13EgvCW0kAm4imWemCK/G2\narJdA9btuf/rfWs/1Y4CAKbuTpHp+aSqvZsitzEFrhB/EaeSZ1lFDBHl3Snq\noX5x\r\n=8hgl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.2.1": {"name": "nodemailer", "version": "6.2.1", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.1.4", "proxy": "0.2.4", "sinon": "7.3.2", "bunyan": "1.8.12", "libmime": "4.1.3", "grunt-cli": "1.3.2", "libbase64": "1.0.3", "smtp-server": "3.5.0", "grunt-eslint": "21.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "4.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "20d773925eb8f7a06166a0b62c751dc8290429f3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.2.1.tgz", "fileCount": 41, "integrity": "sha512-TagB7iuIi9uyNgHExo8lUDq3VK5/B0BpbkcjIgNvxbtVrjNqq0DwAOTuzALPVkK76kMhTSzIgHqg8X1uklVs6g==", "signatures": [{"sig": "MEUCIQDUh2Yzra5Zp2OXs0SoF78UqAk3X+jO+iiL0rl/uvyRnAIgLZ6OFmIfyTApKgZxVhdBtfDsD6LvaMBnuwDeQ2vYdL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc56Z2CRA9TVsSAnZWagAAFPQP/jDn3y0HkXd4Bus0fHj7\nK46jxzLSqLpJkJDV5NGPN9mqGzFPkvNxeIsmo2uZo7csayJ+4+9FYqhq5nwW\nmzClya/bdSFuXJ+MXm1uY5CB39E5xPRFoMeS6L8ugc/vhRzn88uR95HcJ7TK\nZCMwF9hEO0+T2BuUGwEU5RcJJEo6Xdlh9HeSYoHxNMeAIJP9pVt5z8QCCIql\nqI3X9SX7ToyDZZ57RYRkEggadhY3tzlCEkVG4r5BMetqqHMOB1CNCrwuIdiR\nxbaBSKcUyFJZk2ibpbClRZmpHZ6hytio3VCujwyvYc1m3MwTQpg6TUyb7Ss0\nNK1c+c4VBGZzqJDSBGHOCuUQ2Oqmim/aXD3uY+LLdeaEmb5jsGfa8cCDDNiy\nJO/JGzmolL01JNF4cVA2w5uTo8NZuWT5B3sqPt0exFH1BmA1e37PNfnRxVYD\n4T3tL5Wg5LspQhXQyWImQmkzVDdxrojWavdRau93SNUw6b96Nur3TJbNK6se\n38aEzM3tEmyNu3JZ85TTS6DvVtSr/9uTTSTldl8o7RMwp9T3th1ooqCkhXH/\n1TLmNwhUnliqQbm/ByWC4cy086qr8UV9tdf1k3mwbvEDatZdU/BZ454UVVQT\neNRzX9tLVUcD3EmbJi6jz4LI11IpEUCwhA6WbHdDjc11PPkba3U1VzzRjEiE\nqDl0\r\n=2f1l\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.3.0": {"name": "nodemailer", "version": "6.3.0", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.1.4", "proxy": "0.2.4", "sinon": "7.3.2", "bunyan": "1.8.12", "libmime": "4.1.3", "grunt-cli": "1.3.2", "libbase64": "1.0.3", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.0.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "a89b0c62d3937bdcdeecbf55687bd7911b627e12", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.3.0.tgz", "fileCount": 41, "integrity": "sha512-TEHBNBPHv7Ie/0o3HXnb7xrPSSQmH1dXwQKRaMKDBGt/ZN54lvDVujP6hKkO/vjkIYL9rK8kHSG11+G42Nhxuw==", "signatures": [{"sig": "MEUCIQCqefWltgQgFAMZy6s/RJcuhH4RmqqzNMVvg+o4oCN2hwIgfN+v007+OsvtwX6JIJ1aK4C0iB2O/2ClXDp3Dn7gW0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKxCdCRA9TVsSAnZWagAA+igP/3C373laFB0PfUZ0glSC\neIvfGWcFPGRTXDkhcmnM3PWjrGmOMJ0fOH2zNpSZWQqhSibSiv1CZ2m55u+O\np1XfT3yI17LJ4zhQIk3VuFlGTSPaKRf63G5jT4rqH77PMGe7bR7KY41+vsg+\nA34Hznrf+hcQCrqqr9eQ0pO27ymkq2yVUSKxtR/OwF7taLb1eFRqE7e7VCWi\nmJ+4HooO4/S77Keh2vZfiDWeOHnGuWAbzIp0A/a0Agq31E6ukt+7IUKXefPr\nBL20ldwNVJCHXyL9A<PERSON>+crXqIBGmfpFh2dS1rXViTzHpiLf7Kbqv7saVJm52Q\n2XwSTY4Zxqf3RTJ5V8MCQYCfK0wIR1cJ5y0KPMFgdYi6XskVyRYn1EvTtzLF\nz1IsYTYKFBJlkGzd7UVKLdxS/f+HxODqegmY7p0Y13fKqfDrXthqyg2WR7g3\nlrgi2Ma4xRcvZ7Ka9Sc5vaehpO/MNZzRZCunMxkHgp44mKucYCpKZKg6h8Dh\nfMJEPm3GY9KagXLpVT41LISzklZADihXy5USifIPCThKoo+Dh6mBiBprNAUw\nL9OKO/fKEkecSn3uR4y1HqQn7y3T2ndfA295pwLsh7nImekHUGBVGpEumro2\no2AApN0c2Wf5y0qPDevDj9rY3KACo4zLi/0eSHX+WYGPkr4EdPEc07VUxC6G\nneSf\r\n=bQ9+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.3.1": {"name": "nodemailer", "version": "6.3.1", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.2.1", "proxy": "1.0.1", "sinon": "7.5.0", "bunyan": "1.8.12", "libmime": "4.1.3", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.4.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "2784beebac6b9f014c424c54dbdcc5c4d1221346", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.3.1.tgz", "fileCount": 41, "integrity": "sha512-j0BsSyaMlyadEDEypK/F+xlne2K5m6wzPYMXS/yxKI0s7jmT1kBx6GEKRVbZmyYfKOsjkeC/TiMVDJBI/w5gMQ==", "signatures": [{"sig": "MEUCIAFt9w/VBzs1a20SIgTB4dxdaeEV5Js7vTP6OeKlsARnAiEA+7fYHf2/d/GB8M5xsbbxGRs+aX1BYyJ6OMWgZdn6oi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnXhICRA9TVsSAnZWagAAQ6cQAJlrjQFCW0DUoOzgQdNS\nWI+DzS+jNH3Ai9XgCF5+OdWYPR9HdIZDmOq60xtcRMKBQxn7h0KoctjBmw4N\n2QTnT2inczcccaqhwFG36PCeKwxGwGLHIdksmx8NE3GdQVmCX6P10RmnDjos\nZdHJ3vd474he9UtgsBIwzDL5vPwocViDSwYaUcRTWksSJe21KzskWiLpsNbX\nJ4C0T6LnCGyphKLANg27X68uGsHC3ZpdBkBmn3AgN5/2BUbFhnxz8mglDKH4\nxvu6irw+j4KakeN9NCj6NTzH7fsk3BVkuXoGQYOCFiTf1OCzADzT/TY5y+ue\nK0/z+X4GTR8ZExDC42uN/yH51Dzdzrp5AVLJ9MbU7qaA4t/d/ldCNST6sa3h\n+bNVMq3AYVe3911bJyEvqK0pRxqlR7gOBDSDoe7Z3N9JIXMxFnIRDPTguQJ2\n14DrhaDHEgKZdf9hve8EgTiGjL0JsuJoxpYJBuo25XmVCXrB0k1aIpocAH3j\nKD05g6h3WmTbWJpP5JWH3qJqiEqxFp5Mv/WnZ3M2m205eTz+ZvsPUOROES0D\nAjSRQP5R+TK/0gfm0R8MD9IHIQ2GA+ZgO5fYOUwh5dqGbtOm9poL686zPrYc\nTEkQiaus5NyFTBKW9ujWeTTcJxZm8YMweKfJmK7g8nUfshICerAyJkarOu5w\n3feJ\r\n=b6D/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.0": {"name": "nodemailer", "version": "6.4.0", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.2.2", "proxy": "1.0.1", "sinon": "7.5.0", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.7.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "91482ebc09d39156d933eb9e6159642cd27bf02c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.0.tgz", "fileCount": 39, "integrity": "sha512-UBqPOfQGD1cM3HnjhuQe+0u3DWx47WWK7lBjG5UtPnGOysr7oDK5lNCzcjK6zzeBSdTk4m1tGx1xNbWFZQmMNA==", "signatures": [{"sig": "MEYCIQCdRQXdn3ZR/tjUYFMlcrgOcoHOdGcPA4SIfwaBRKJGKAIhAPWU/fOs9mTpJ22w8VSsY843tRH9KT5VZeRpwkrViQeH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd562oCRA9TVsSAnZWagAAOzYP/jHhg+ae8ZgZSN9neqSc\naHdE4AQWXDYXrZkOPAcTNT6WNASy4pdFYVESrGfljlFDchtT6aQk6rK4QYwT\nbZowddY6NvfmtpAtQLEBIg2VRDPA7g0uZ1+e2VU4S58xaRSmYn1e6GjybN4Q\nUDiCT0C/TXqun3d9GSG2fBO0kdA16I3c0wAMiilepm1dnQ5qkbrOEyKvSgIS\nwEFRglWjqYmViqpFmbHihw5F0snGhWJBV/udKTFF7qTouEIvO6U+3XBc00pJ\n26o10htyxiZlNOJQSCZWnzxYkAOSdRMBeuUlpizn06dArJJNkVuUx9UINUN0\ny68zUYqzYhD2P1bebE0YFHC8UHjVq5BNQbZ7Umn6Sc1l/cyMmJd819YHtdRc\nTg8Zqv6Iopn9QUY+g8XkHvtKJPqHKF5V93wZO5qNHj9WnMneNvaQ6oi0mZwC\njrv9XCWnb7l+0e9YTFKbSpaWr0ia7A7qo2NG5qWf011pdaKfojkQI9AurBGa\nN2VNVOx9y+D/dTJCfOTa2HD9lTdZQQbf1QBo19WZLXWyAYnlSE6gZAKsg7FS\nuueqDQUAWVaJ/tWKyQiUr42jx9reSMltvzrOvuGd9QqyjAv4TQU/Vt1ny0fJ\nXoverg0+XnAHaJTt7l/KGB5rek1WoAx23gjlxHj+D2p8p07asQNy56PhQhB8\nai6a\r\n=/iS/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.1": {"name": "nodemailer", "version": "6.4.1", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.2.2", "proxy": "1.0.1", "sinon": "7.5.0", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.7.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "f70b40355b7b08f1f80344b353970a4f8f664370", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.1.tgz", "fileCount": 39, "integrity": "sha512-mSQAzMim8XIC1DemK9TifDTIgASfoJEllG5aC1mEtZeZ+FQyrSOdGBRth6JRA1ERzHQCET3QHVSd9Kc6mh356g==", "signatures": [{"sig": "MEYCIQDLCDnHLcStvWIy3ZecmQNrTwuaH9P4vc+NmfZthR5HuwIhAPE0Ea+X+cIDTTLt9izcAlY0AhfOtHQCuPXLrGF6spMu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd69w9CRA9TVsSAnZWagAAeL8P/1aG9QCpXFt4VuYEGqpv\nZowtKSYFjbsNi6kbwZFK+FP1V3fhtb3cZ0Z7udqoQoWoEnh2fnrBm7gL27Hl\nR4ZOkAWiYimEhK5hPuPCtHrM+bAkBsCsDHJ6OUuN4VdghUuZDxdoa/pcSFvx\nvgIwmlg1Eg+fE/Aqi8HXp/hZWz+d4rQtdhIiqzXy479F7O50D/PUOF2a3kMU\nc5uLhF3FXZJSiS1rTg0IkyPI+0fC3lG1FWPJzgR3JNyvAbj35X+3TBdwxS69\n9Xw2CWrvXepYCrPiimBuBEDxhQ1cr10bKmui5rqZakZhqell6V8pNjKSlJo8\nF0SstDMg10gaU4Vsku5BfvGFo4AHg1YZHwrWx+M4UyUpBCLjc4hgZ1kfm/JE\nVChikWw3b7XLldCIyyvtZTa8x44zArgM+KQ6z7C4tfFHEw8VKQEFXOCJj+MM\nm0ibB2OAebkZ/sgRCUkcKfOIQZlsdNaYEndr1G1pNdTZYR3goQgTYqxBQvNy\noi9cc1KFk903JSwWuB9BV40hCM0CinmNWCwIgSs8dFLjUBOJdTKhEIwzP3z8\nmy2jCX7rUc5tZ+sSkz2c1GjuotRdtnvl99s7tNUe0RWOJhLkDVbJ3r6oSj5b\ndeVQeqEvo9lQUXVuASGAvxAlJwOyCS/aaGYFE0CxylEMJNtVGL8r1QInvTbU\n/TAL\r\n=haw9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.2": {"name": "nodemailer", "version": "6.4.2", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.2.2", "proxy": "1.0.1", "sinon": "7.5.0", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.7.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "7147550e32cdc37453380ab78d2074533966090a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.2.tgz", "fileCount": 39, "integrity": "sha512-g0n4nH1ONGvqYo1v72uSWvF/MRNnnq1LzmSzXb/6EPF3LFb51akOhgG3K2+aETAsJx90/Q5eFNTntu4vBCwyQQ==", "signatures": [{"sig": "MEYCIQCjjMmCivJcWHCzab2cyEnUGTlNG3vLElH6Z3mL3CHFnQIhANFcKabgkiiLGb/+FOPr7BIoWQ9rxdT0yjdXUhl3IrC6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8WS7CRA9TVsSAnZWagAAEPMQAJqZdDPXK9oOuIaMC9Hu\npSAVHD44BcH3+hKkuP9ZQTniZGvamVBzO9P5uVD2znwELF2o8MFxDOlq436X\ncsZ4+lsfR+9uSuCp3LRF1+76y6K/zXFsVgj+Pdgi141LwM/OOBoeKoyryrYa\ntPFUfK7Hmkcsku02SbC56wnm0vnxIksQw19vlJtebicNOevWHOvRTHFl3LED\ncXEQ+Se4bchBUvw+7bWq1dEJAPC56/Rl/02HSzpVksa3xqHBrc+fC8EM0uoF\nSfrOGv88Ac7nCFsSd1Im0mb3ULhL/jQy/MTY+0P0KHU0jhrrL5eQAt1YG6EC\nwKpKnC9Km0ECLv1OU6Qu2PasJ7hJE4O/CES4zS7jJG3nxbRQfB9DJGez4yB7\nwe1creqtyGaXPiZzKCTG84VtAqug0Vo6FDV/aF1MqRRjcXviVmtiFD8QprAD\nY7WjwyT6Wni32fkeiz0wSpzp1rqqIvTVq5hOJeWMeiyg7p6Ks+Pgn/0fX8Yz\nWMkLfEy+IhSWuE7mJCWTmoXjS7OXGgYKQinGaRjCeACXTIfMr5JwnqxSzXqt\niMzutwxBon3RSzZ3JEjqlftWjrW3Rp5pf8jdeeGv/etz4bL7q6Kv36y6a22Q\nO/3gzrWtFj/ntpHIilvfPf9x55ozAmt7lGSO+XR2xL6BIM03YKNFenm4af8k\naHBH\r\n=ncfI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.3": {"name": "nodemailer", "version": "6.4.3", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "6.2.2", "proxy": "1.0.1", "sinon": "7.5.0", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.7.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "10d29195fb020e3297495bcba39d3519b23483f3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.3.tgz", "fileCount": 39, "integrity": "sha512-zmx4MTzYWQo2abFexvFYVURaiBGLekoA/yjP2Ctigd82hYtC4n38pcphQBi805t7AB2sY6DwmxnP/tb77f3KHA==", "signatures": [{"sig": "MEUCIG5cTK/LtFJxaUxvsnb2S//7iQpBp/4DCi+qnZqKTbL6AiEAs0n4WlFFySqGZD21W2yyhkkyS6n/3gEvNMM6hfUiOMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUWMECRA9TVsSAnZWagAAvNIP/Aj8gYHJ4PP9xjoeILjs\nWSWI1fFK6i0zO1CpoJizqawbeTFfP2JFkFU9zrXfsqTlU4hqLN5B0luLsOq4\n7S9WJuxdIlWPCA29QEACsf4Qg4tEMFnWhrfZrd0FyH9On47Yb+3NXjaYbfmz\nm4lBMvz8JkE6XCI1ZR+OLu9K2GoG91ivx/CN86UG47NkcmzrACNcThR0k8zp\ni0v4P88unUg4AQBiTYwfBzoXkrClZpnywjGMATEG1DlE+lb9YBdaOb19aoQB\nZogHA+PybCk+ApQSrY2nnUej9jNBskq7GFOITZ+JP56h9MyU4huCKH3O7Nmz\noN4OQzDuur3P1Go1FYi9KRck4+2XNfYmsEGkz39LQppmxdtbX0/BzhQPJT7Q\nv32T7TAKmbBxZEQJKwSca8P9dUVC29d1k0lcbC+P2GbyLwS1/cw1eS7XiSpr\niPM5GZZDxkxbwz36KLH0OHKNtQsimX9SFzMQB+w4GqdndFPLw2IK32TFpFu4\nTL9DG586BtmZMryewCySmgByk2fWY+MdTpO0teBgJH+DUHFefzHRbLTb3x6h\nHpPRPcsQ1NPl7AqQy+UNv4+JUCr0VubhonhtIONNqBHIzzYSdeYhE40Tgt4W\nxI412F0eRTdvUr/l/iPnQvnTxynat+ECK1F4swzItuL1MD4LEQDa8Xg+nIPh\nyuGp\r\n=4X6d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.4": {"name": "nodemailer", "version": "6.4.4", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "7.1.0", "proxy": "1.0.1", "sinon": "9.0.0", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.10.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "f4bb26a833786e8908b3ac8afbf2d0382ac24feb", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.4.tgz", "fileCount": 39, "integrity": "sha512-2GqGu5o3FBmDibczU3+LZh9lCEiKmNx7LvHl512p8Kj+Kn5FQVOICZv85MDFz/erK0BDd5EJp3nqQLpWCZD1Gg==", "signatures": [{"sig": "MEUCIQCZTZtdua3iYFLu6CeQj2MQmwAmD7QR6iZTRafONrMh+gIgR/NhKX8FOQFs8eUORWWbywPIK6wJzQqEMC5NuZBERAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 462068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXBvuCRA9TVsSAnZWagAAwacP/iG5m3+H0f73w/4d14Gn\nCNA+DbLvlD7JnRp6hGE3J4G8lJKGTt9OYoV3Ve7UbAq6J7ikIQjdEhLhZpx3\njKtehiO9rIKqw8Uz8mXfVGKPzyVsITURKQY4in9CYrsL6/ja6/ZZyXZuInfW\nZ2GK2NSOMKHIYL/mKwnF5cA++DdnO31bUJwCWslEayT7FDXWlq094dW2AUaW\nWUrOfKY05OKf1wNHy8lr9gJW5KokGdUGSidfR7KNVYfabdMwp5t/tSrjegje\n<PERSON>xl/qN8nPgI6OQe8+WNv0EzadEl29sVSupQbqSpU3Ka0tAOnY+FpCPuOud9+\n23dfKRIHXgC310kOE6a1prunU2jFlqei6uwEPuYaiSQ/Ykir5suJYlpf8dT2\nFIjazGsD2u3HVWM2dlK8IXBSZSYZ5LcLiQIo/Qcb3BjgH5EB7PODJ4jTezIi\nZyYALlPDJMiI31YxZF3HYLhArZeQepK9U2DZ8ocPWCvwVAQAiWsiuK6gzb5c\nzdZlNzEqFy9mvlGbxOTtvo5FiD+uH6pbUgCTFCHxFh6rb0egpUtx67+gTA6F\nu9iPQnH2UrNgbOldRzTi6F60AtIKPnqQaHUHpFa4XbXYuHiobdgTURvZn1gj\njsS6eoeEkI242ROVjgi4UcghKyZdD76gNvZySy8eQfNL3CNjBlfoLKEWcbZX\ndXxw\r\n=UmiC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.5": {"name": "nodemailer", "version": "6.4.5", "devDependencies": {"chai": "4.2.0", "grunt": "1.0.4", "libqp": "1.1.0", "mocha": "7.1.0", "proxy": "1.0.1", "sinon": "9.0.1", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.5.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.10.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "45614c6454d1a947242105eeddae03df87e29916", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.5.tgz", "fileCount": 40, "integrity": "sha512-NH7aNVQyZLAvGr2+EOto7znvz+qJ02Cb/xpou98ApUt5tEAUSVUxhvHvgV/8I5dhjKTYqUw0nasoKzLNBJKrDQ==", "signatures": [{"sig": "MEQCIBn2Tp6Fy6r/orIyhVaU1Hm2tiVn7VrEmvOLnBzW3LA8AiAwHYVGTFeozPrG+aozEl2qPgVd3OKyVtRL8bMtpMzlaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ+ZoCRA9TVsSAnZWagAABB8P/3pyb6DyUemA8gMjzFSn\nOFMKF8I3qubPbfLLniwPqWo6RaD+4k2IETzF9ycM8W1+wlmI+o+yH8DwcNah\nuUjNzFT9zYu7bdybYqShHk8rbT9oZzepFCANBGgb/qlwkopJraVX54NOrHHZ\nTM1zURYeZzjycNX6Sus7jQqvrh5YFT3bfpNglNv9cXXtqu75FwFAqVdamol8\ngwmf03h6SyU7ubRBpcNslYs4KNZwTcC+pEnWtpYj+ILULJTCMNIdTkrIbK3m\nQ1yF2E2IAvgn6i9a65OfvTe1yFh6zO9LfV0ZdrpljPoqQmsRSEnDrVwaYV1H\n0RcgdIyhnA0BK7CfEzoU9IgAXdaBfZ3kafVFdSDqrjWl8jQ80wvfsGt435Bi\nHeNQA47G7ceZmCIquYPGyzjmv8pX4bdZcEmL1J4P0U8ifC6ir/NuGXhBSsAS\n4C+OKNpQcqEOmzzI9pL1b95E3iQjGhLwe9ScquHOMDEHxdANnAkzsIv3k7rB\nFTsZU3UX0fZXE8Y8hBEKr7SvuV86YHH51+5NiqVU+KLvjwCkoqKP58a+3f2k\nCFc8hYWc7Yz1OWgYLI9DLDyBg6OMp68SxvjytonzUAeDW8F9GiAaqfyT89i+\nWVUbS7twB3H+MwXLrrFC85/N9Xgnb50QgXTN5hL0XFP53DqRwEZE7xBN2v1y\nOtne\r\n=dn9r\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.6": {"name": "nodemailer", "version": "6.4.6", "devDependencies": {"chai": "4.2.0", "grunt": "1.1.0", "libqp": "1.1.0", "mocha": "7.1.1", "proxy": "1.0.1", "sinon": "9.0.1", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.6.0", "grunt-eslint": "22.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.10.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "d37f504f6560b36616f646a606894fe18819107f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.6.tgz", "fileCount": 40, "integrity": "sha512-/kJ+FYVEm2HuUlw87hjSqTss+GU35D4giOpdSfGp7DO+5h6RlJj7R94YaYHOkoxu1CSaM0d3WRBtCzwXrY6MKA==", "signatures": [{"sig": "MEUCIG+jJ1CoFQbBCLTEu2CfiM+WQB/oxUZmWqghQFWDDilAAiEAv3pi0c2r7Gs1b8Y+udU5Lamjh5GFa7NeQQojurZk96c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedJiUCRA9TVsSAnZWagAAiEsP/0tAqXSEoLCbwhX+3jec\n/Neu7LbaN+mymtaXQ6pLdQnt1t5MDbuOGUgWK7n6hdTJ1OwoxCK7yu9jUYBi\nwg4fuzf/4bR+ttGBs3MTZvEbu3Z8l6M1FpzlqG2BtDVRtdDKdAJfgbM7HVYY\nkIILsMHFHyVMqj5lQzZNiXUL7RU/LMpK5hLpF8Laq4YcpXdpqGaf3E3JwEKn\nZALbe1cIT/p1tYjM/e/xE/JkrfeDgyPiLwPw0yiYqlZk+XgWqQkteTmrSfpp\nvcZZwQ1PijEo5kPsF1yHqGlNq2m3AwHgHy34DhRbL3N3TRt478XuiLLbMhNH\nx0iBPC697MHWFM8CjstNd2hmOo5HaAEs6Xqg2HTlXe5HuaGV+AMFYpPOreej\nKIUehivQu9NitXhvamviuyRiSvbv+M3Hb8idsWbACvYokVvzmiIJN/IhO7DG\nBXzmDq6DOcL81M5gODOLShHCpiMmiOHCM/jBZogQbRphghzraDjZEdTtr9p6\no1iPaW+apkzD3JV6mww8hqux+/siRd/9aMHvlzolty8Se1ZVcL/kBlehwpOF\ndztDG3kFKwkK0sSvpB1e1UPoONe/Jrvu2Jak24aQV3VezPRuNthV65Ad6GSb\n8J827Vj+63JdqSf/HdpQAOHqoUCJENo8//To7f9Z5NDg2ozXohIi9PFxgDx/\nUqq9\r\n=rd0G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.7": {"name": "nodemailer", "version": "6.4.7", "devDependencies": {"chai": "4.2.0", "grunt": "1.1.0", "libqp": "1.1.0", "mocha": "7.2.0", "proxy": "1.0.1", "sinon": "9.0.2", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.6.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.11.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "b905f45d5b755d4f8b45ce017d7227d843cba081", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.7.tgz", "fileCount": 40, "integrity": "sha512-lreSSov6kugFFmFWrs2fcNHqUe705wy3VTuHKNevTHWTrnQNZCzWEFHXf2j+Y65dbB5cpPenX0n3ZCUaPlTTGQ==", "signatures": [{"sig": "MEUCIC7HY8LNktpt3mi+WQkA2WMr6aoYxOa/lQot9bUwYJIfAiEAjAjO8a1yONgMYTAmIHzmpwfFYSFr47I3karrnlyKmFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJez3YECRA9TVsSAnZWagAAnssP/RwGYCkZGZMKIwW0psNP\nxq82O/Ds7VA8TZpLeYc2OmWb8JdxlMQEHHiYTQ4xTujqGWXWs25a8BtQOxoP\nRAvIN/X74gp+wV3Ii3W9XWjFKybvEWtyv/UspcsGjNel1y8BQkUOjWRekGJn\nc41d69/cNd0S6fTvWrretHanZN4MWE/0BsmQa70EFVN5yy9R9tzRZ1qsd+pR\nETTUy54gj0rSa9Tki7MkG0/U0+iOTh6uwxIejl+gOa2srptU05H/n8/4MWbA\nHCZKwvWLOxzQjy33GSmxURRM/PdyqAlnqW6y1NRU1mq6qh1c5zEa/PMKgtab\n2mi9VHn+RqH3T6H6fLF4mIAs+7rAMCX/OGM3Zmrmyt6cky/zZeISGihWmOZE\nwCgahWaRa0XSk/PWB1PXgTPuJlvrJB4bdcE1IFI9cTRKN8/C9oB4gfAn96ZY\nB07arS6JdoAZE0OYvvWavAzRxtp5lwC8PbgQoJqX7i9GeEpjJA1U4uHLOhHJ\nc+EUtdoLf4X6IcBvSKE4GX6FoxT8KI+hnyRlO9UKb2oH2GzPwEol6jEIfsXe\n6v0hb4jojpoAYKj6EPfM9nYc4gfJMrmghhnrmhf5J9WEAsGLTL2vMRJvq1fU\nLT9pxZuD5fX1qa8VFeSE1axSLRnhd3DjqATfbfb/AL10GLyxGUX9spzb3uOc\nY8Kw\r\n=puY0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.8": {"name": "nodemailer", "version": "6.4.8", "devDependencies": {"chai": "4.2.0", "grunt": "1.1.0", "libqp": "1.1.0", "mocha": "7.2.0", "proxy": "1.0.1", "sinon": "9.0.2", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.6.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.11.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "aca52886e4e56f71f6b8a65f5ca6b767ca751fc7", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.8.tgz", "fileCount": 40, "integrity": "sha512-UbJD0+g5e2H20bWv7Rpj3B+N3TMMJ0MLoLwaGVJ0k3Vo8upq0UltwHJ5BJfrpST1vFa91JQ8cf7cICK5DSIo1Q==", "signatures": [{"sig": "MEYCIQDY5xx9eHfiVCkTG+ko/E2GDrWLppTp+f7CM4lObszregIhAJj4TXmanMwGsX/sWAR2D1pPmeujOUm3317vvo2rDVzQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJez60MCRA9TVsSAnZWagAAJTYP/25qYLDB9Q82/QTosKFK\n6WD/65fqb+AKDkf+/p1FuInPjXB1Zk48PCRncdqmhOa45gr6w0vJ9eWkdySf\nx8OWuGyjsY2IC0GOtZHDhM/lLqxOOFTdxyvREIiYSNIzXB7aIqnU0jlQNtz4\nH/wfqLBLwvawbzNPt1ZA6Q7tih/4hBNFEAz/o662tPObTg9L+v9WKg/W2eJu\nXUQc14Dpc1QtUXBdtPdSCXkNbc5qu4ErftZiX4aWTPDxe2rFJv10OKg8nIGo\nYbC9w/1f18YEu3z0xPJfWLcPFygis+3uxITtrlGqgLo0BUSfWnL0LW/uqPvJ\nstuKGYiarmJtP0cH87vAuAFL7WqU+Y3WOPgqmIuUtShM6+bs7ZMr2Re64W1/\nr5ylgDVSzf3EEynYOMY0s+PjWuq9/dAkBZgG5TBSGM3BSmWCmduzERDjfBiV\noff2KvtOxx+F0/M9Pby1nb7ra0KwN8uSUiojuC+8L4bFDnjsUov5UzvQmWDN\nL0c3puj8gghv3PYlBcWXUgFpVvnA7HJBYNcfscDbXIl6Z0ZkbQzdrcPBLhK9\nPXzOQAHPWknkbH2oVOfbz9b5S+EyGl2k/yUq34Bb9a/n6uHRBzjdLjPYYU1k\nBZEVv9//0MNhvpU4psFHzvLFhf5xvpp7zcCw3lo6lV2mxi/RodKlmH8KGkw2\nyds6\r\n=n8f3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.10": {"name": "nodemailer", "version": "6.4.10", "devDependencies": {"chai": "4.2.0", "grunt": "1.1.0", "libqp": "1.1.0", "mocha": "8.0.1", "proxy": "1.0.1", "sinon": "9.0.2", "bunyan": "1.8.12", "libmime": "4.2.1", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.6.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.11.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "f4c8dc7991c57f41fd081bef224ef01f7065143d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.10.tgz", "fileCount": 40, "integrity": "sha512-j+pS9CURhPgk6r0ENr7dji+As2xZiHSvZeVnzKniLOw1eRAyM/7flP0u65tCnsapV8JFu+t0l/5VeHsCZEeh9g==", "signatures": [{"sig": "MEUCIQD2q+tBvcBM7K5gsrMe1KFfXIgEB3vaP6mn0cWfE41c7wIgZa8tkEyPZYEQ7r10aJgDoWHS03W+XTTBeloez36dglk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464854, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6duNCRA9TVsSAnZWagAAWEEP/1TBosPhrclVNxJPyMFD\n/+4qnXM7uiI+3TZBYCetd4a+xaQCYZeedG9L5RkEFFRSUC/eIDS0OiAG3qAo\n8n6oSkEQBcX++7FRXtZRta0zDIGMSsBBKTUXXANF+y+2tKDI7BWkOH/QaQ8G\nRbitGx6ZTlWTPtRMUg/4q8yBg/MZcA+m5dQ1qlL/mjKI5D2WHVdvghosvMVO\nVSELt3m35NC+9j7cii+ktEJxXeKXQUbSuGrXwrXeskjTjeo2mAVUfo8+NRzg\nHY1PFyeYuqqkwQE2KVP58T+aMmmGUuHE4tqCPRxA9mO9FAp62wDE+/pX2pMx\n6trCb3QxFqmeFiaBJA50NVRprVwFUn6096hIkTYIGMLG31QSBCVb6rlu41we\nsYBGkYOCZMGgG63E5B9LmsOoSaDpJoqR0dkqkuN8A/YQ/purXxIfq9I7POw9\nUu65A47lytGPR9AJ3d/Zn5N7oElDFCAks3g8R/iEDYSHcHEmA4s2XRpfalXS\nTjQAinPszaTNoVeFp/bJZIWJEZ9fQ7aPCaI2Ar/KyIAONW6N+tv+GD7IlT9m\n1NuSRy5jj58E1bCOQe/ZCERuSm6P3JhYCA7ytexI0FfQY+YMo/UpqLp2EYKx\n6xDnTvPS5f0Jch0/yqX1Qo04iNq8biwGGsQBgS9fjIKcUDrZSkyyqkpKrrGh\n4BvM\r\n=+NaI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.11": {"name": "nodemailer", "version": "6.4.11", "devDependencies": {"chai": "4.2.0", "grunt": "1.2.1", "libqp": "1.1.0", "mocha": "8.0.1", "proxy": "1.0.2", "sinon": "9.0.2", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.7.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.11.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "1f00b4ffd106403f17c03f3d43d5945b2677046c", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.11.tgz", "fileCount": 41, "integrity": "sha512-BVZBDi+aJV4O38rxsUh164Dk1NCqgh6Cm0rQSb9SK/DHGll/DrCMnycVDD7msJgZCnmVa8ASo8EZzR7jsgTukQ==", "signatures": [{"sig": "MEYCIQCwufkflN9o+2N6z8h8vj2DXLULVRyfd+4CJxQx5RjbQgIhANJye9NbnHtMcgzRxI/FYc8C89h/LFSLWS1aQ3+5v6OV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 468867, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfITAyCRA9TVsSAnZWagAAThQP+gLvFgF0c0DrGkeP04rg\nXIAJ3VDBCKtRM46A9Nu5ay3oaI0QFGntFhg3TbhOd5PWyqufYLm3BC7zDPg5\nTeoB0LFbsBD7hMNh6VHGFsHXCYGopa+v1MRgn3v6ZLAKKgK1mQV/D7PTy2VH\nCwgfxqTPECP5SYo1nquc+/0eSpDgG+RCCiyVpvi6GychhVxlp7OL5IGhZGuG\n/mPghaxmmB4vKSUtyZtXUsqW9GH9DcDkI/wP4/cH1xU3FKzGkyEwyb9Qfbob\nX5jGDui2EIIwPygrxFMFZE6/4itHYS0jNdaj43R/cY/WcodKaiZOkq53EtN3\nxXw4DqJweBUuPaEB9l/TsvJKQBfpDa7ZVOGI7qtt6cj9P0Sj5UlqFvoo6hA0\nHawk6BoTjXgOM5Q4gOdKJy9Q4cdntWbKjBGFSouU2zGbq6YKD0fD2gEZtkyP\nUfSGy3tT6h4VIuUwGbzVAHjb/QsBMSAw6Gv+uk4WwFktrdEIbd2DH2MZ1I8j\nUQfRShPkdRBBzKdRP1ppMsOC4tyxwWji3sCZSDaMHhV2A7wZV+65BvBvkPeL\nG9ZiA1l9oddBy5nJTEHRlSquj+uMV/nAiiZfi7sXEs9187THps3jwvLszaVo\nxFM/BxpFj/2myWqbEot3EWSnQBGqVk/mcwJ8hN4At9YYDU1mIRcM/n17KpfM\nzC6b\r\n=+VzW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.12": {"name": "nodemailer", "version": "6.4.12", "devDependencies": {"chai": "4.2.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.1.3", "proxy": "1.0.2", "sinon": "9.1.0", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.7.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.12.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "efb3946f3e5c5afad50af8090e070957cea1c43d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.12.tgz", "fileCount": 41, "integrity": "sha512-c/WplZp24Lxc+hn0w/kweNxcYGpaqtH1iecfGKTbXVmp5qx+ILApKsmCAucWWIIQiYKKH4ZA/ffSNOsai6xJGA==", "signatures": [{"sig": "MEYCIQCyQZGxZEVBFkis3pjH6CAiBiANQpBe64dU18weu+s7dQIhAPIrmY46yws+1m2KsRyQrNTxK8Avc7UkUcpmeIl05/iI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdOeaCRA9TVsSAnZWagAA1HwP/26R412AUKe6hSs68mEX\ncJwn+gEpjk4zrDvTDgOPUThfMh0jwqmcUaN7Of1g0fH7t1WhOqT3svaaECLn\ni5nsg7ZQBnkldPpBfg39dZt/3SNmNHenUWMdCZvGu2jJREEHmz2+FEQA4oO8\nSTLLT7YMCS4s1Z0jJ6ceieAZ9t4z/Emj3P9XB++2siPF1AyOWb3nLGWqDpgw\npTVFzvGtRKxrb8rcdnlNgf5BrtA5AKY1RP9l9T5oazDykVibYFcB6io4Qxq0\nXPjPNZWRZvkGmneYpjcWzARohc+DUbW0jn7YHL1tLWZ6MrfcXwkzPTkT2vVA\n/CY93uywK916QuYJ+lG4jy30sVYnDO5gMX2VU0ZvlgbzHAWapBljyLiHqRPP\n70DHGK/1ojcVRJ9wsfuJsaiI2zMrNueT6fREBhDYzxx6kNXiz5n15kQ9prHM\n4voyPTsSvruGIH2IdGh1pgWG7Li8SGuNtmbRKIenUzYgGutlljSFZe3om91X\n81NDA2NVUPmOMrz08GFE0UENKDG5ZHAwpOyB5IXMf3LRcujhSThCmLDrB908\n4/Km+RLrhUhy4q/4TabA10Qqbzxn6+A8wbhDCNOSTS9isatOjG7AZmmyWGPR\nxo//t8TLotLqDGKaYwZoy9NBbtFo17wL7eqznyk3G1JsEJSCE3PFxzWC8hqt\nq7Pr\r\n=Z2bM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.13": {"name": "nodemailer", "version": "6.4.13", "devDependencies": {"chai": "4.2.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.1.3", "proxy": "1.0.2", "sinon": "9.1.0", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.7.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.12.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "40e67cf5a32f2052258822a9394e190ea9714801", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.13.tgz", "fileCount": 41, "integrity": "sha512-XmtiiKza2Cqtr+ZRMchMZn9s2nmwQDeakbf+yL0ODsIXOv58UZgk/MKPOkDKqY+mvxHs87PrJK7Nf/tcpKHqYQ==", "signatures": [{"sig": "MEYCIQCNkg1ZeVqODbbX0Tf4QOWM0l8MB+ssycbhsIEz1PbXvAIhANiQFuOSPPSPjRsWw2nanrE45FT4rDjil+vSJX/QQDxH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdtoBCRA9TVsSAnZWagAAPlQP+wdITnxhkCPGWB+dHDWl\n89rZOufuUZMGHhBKzhBliNQOh+jKg89V4sk6MpDbdCYN1h0S6C1ZyHOto9vF\nsPgnSEEOjBamP5xZy2dI3ndWE7zvUWov/78hKS1Pj1R1ttOEJfs05F0UTlE+\nYfp1LY0Z+4Ga9JdAQ6HS44b93ZY6k27MpV0rr54PxT5gh2ivH/VNmi3WB8ht\nyAq59sAIqVckZ+NlamahJO3mAuUZcbO4VIJ6+aCXrblB1jd/u6Q3nVa4KmDw\nOlbXz2UXuPWwVLkRJJMti38aBbYJn1aB50Z0jHspKo4s8YbQXi9JzTRMHb7/\n3jzkS/oYtEkvuU+UlbwwDZVvizcjYxDDSjBsTulypbrsH/WELGA7rawOxIZp\nR1lwbxjvEOWl44ODCRzgGHcPUqRWMlsV16YyxNN847+8vrQAEmIFqlBQ0rSv\nDMpBh86IsfPORLaqnNU9Q1LXuHV8oDs9KEjpqS+0E6d30vSwaygpJD4VMjqA\nwYhruI6KsL8/28PHgg9LtkFLufP2ajzu/4wPnwGdluJar5MThbepjCoV9vS3\n3hZ3hIWrSgYQBrHGOr3/KaYKwupygLRC6m9kkF0a/7Od/unwn4cqBm26ViiJ\neoztEDz6NeKBqZy/vJhta6T3Pn4/dqhpoMbEMQTIzGFM1AFFmnBkxkX9ToUo\nbF8E\r\n=jNDG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "hasInstallScript": true}, "6.4.14": {"name": "nodemailer", "version": "6.4.14", "devDependencies": {"chai": "4.2.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.1.3", "proxy": "1.0.2", "sinon": "9.2.0", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.7.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.12.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "2ffb160b63ff0c15a979da75e1f82af85433d2a3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.14.tgz", "fileCount": 41, "integrity": "sha512-0AQHOOT+nRAOK6QnksNaK7+5vjviVvEBzmZytKU7XSA+Vze2NLykTx/05ti1uJgXFTWrMq08u3j3x4r4OE6PAA==", "signatures": [{"sig": "MEUCIQCdsuBFjg1l+zHpCm2wyomSG3c/zz1VutOwKAM4jRk6OgIgTZlBPcIpPK/OXT7IoWQM9tv2Lz9nRGaCeCv6fZSBRDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhpU4CRA9TVsSAnZWagAAU9IP/idn9x3noWdjTGwivcXj\n91xDbxfvWMnBXByqohn3IRY0eR+fH8xxauTEKnLMyFj7lDjHbv2vdaQkIqXI\nWJhsHgiuWC+tOmV73v7MoGiX3bGHqNiJj1HJBKQHS7MXVNQ4lRn1y3s97Nqq\nGq4sSWGv+YvlFkR1muNI2ije/21kM7v2+doOS4NzXjjkVCT7ioMQye6+IWAx\nVlmikF/J1ZBT5L2c0OdG3kXorxpOvfLneNCr2P6qP3i+D/K2J8KANL17TnQK\n0B42o90/PoQp7L9g4Fpp15hrT4B5kchM1rzuKWqdtjLj8AnETQo0zsjVIEa8\n3RCtwMDEyw156WLvXr0mYpL0vZHm46vCBNtPc/a6SAwkVRwj5aHcTEjUsJSf\nf9bzLMmqUDi5P3Rsyw5k3uTte2XFVPkAYFIiekLEi9MUVeDcKP1YBXudyVLi\nueEUpLxPvsaPwdHu4w72OsKhM+zYXN5sgEpGKMHHkhCjNXQvr95/5XxIOGOw\nzygAVKCPUIh40Hpp2lRsDEa/odIVFm+XSFE1fYSiCT+pHxYR67vjGhR2B69l\n/QrGBiwyKR+YLOb/Z73NFWZ6+Rel43/UIxvnSABWdKy0WZ+LfM72m3hX++P2\nKWaW7xmsAzX7Gji8bSgEpLihdJwKIA8pw6TV0RN5yEdtIStqyvxAV9btA3Tx\nJbxb\r\n=Q7mi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.15": {"name": "nodemailer", "version": "6.4.15", "devDependencies": {"chai": "4.2.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.2.1", "proxy": "1.0.2", "sinon": "9.2.1", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.7.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.15.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "da5dbb78b8376e34c232c6aa3d2d65cfe17440e9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.15.tgz", "fileCount": 41, "integrity": "sha512-2/z13dBTWdgTRlxVMAK6C13dCI22GEShET4+jFLlQsxpblxYhojnucfcTZO1QBu5CsHvABsBj2JCGO3vl0HSQA==", "signatures": [{"sig": "MEQCIGQ1eAbyox7R/K/qt34VwpFmRBLU4pRhTF3NiJ7l+W6gAiBX80O6qSnRKgBJKoOBueff2Y+8i8uKzpfneA0RRopINw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpUdZCRA9TVsSAnZWagAALPUP/AmWSXH8r/hajBbZDyG0\niwfHhb0OFYsO7vuDAUdu4CxlyK268yVheBCIHKJFnfYSlOys8DOmw7MLHZ1h\nYLITPR0VQ/EHypfAgQE2i1Vl45vTtjPuG+HDzy/3wQD0g7o7J/2sah/8ExTT\n2mvfJRSNi4qCWrj1gfgR5zPNNrYibk+bR9YW19Zk7q/BqMMX0FEU8PbSpV/M\n0n2kHgv52mSRFzsX+xwSL2kUOjid1wUwGbTi5cMXxKGNmM86qWsvGvMj2vzf\n9g3bOMsCZ8wTAyOoZdUE65BtnZn9ToTOBTr+aFPWZ02pEBR3AGJcrw6uoOAV\nubVq7ctP6bm072EWfP1mDZH6vb4fUbZSwUCvWguU1FQQ2ZUy2RS6JqU2iWCZ\nRGNeNQkQBDVp7afaxzT8QrVjRZdfAQUqXx6iw0rDI+OWqLEW0v0aal6zjs/L\nzu8EiyrONUB+S5YL9IYBPWJVP/jObU/U0GgOOl/3NOzSQGetAT8I/sdKCpfC\n9fMC8oLUKpGKMdu/uETcMsRSY39dG3HTJQC1XAFJsHZVXb03KU41edWCP/ZY\nVKeU+9mNyZaivylDvIqF6zkf3ncYBG6RB3lzBFhD/0uWl2i3Z8xXVOYFUB9p\n1N8Xrdj5wtkk5sR6kRYtu5eWG3iuIlvVne4FAV+1h738p5+JhHZEMSdexEtn\n5cRE\r\n=Sa1Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.16": {"name": "nodemailer", "version": "6.4.16", "devDependencies": {"chai": "4.2.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.2.1", "proxy": "1.0.2", "sinon": "9.2.1", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.7.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "6.15.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "5cb6391b1d79ab7eff32d6f9f48366b5a7117293", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.16.tgz", "fileCount": 41, "integrity": "sha512-68K0LgZ6hmZ7PVmwL78gzNdjpj5viqBdFqKrTtr9bZbJYj6BRj5W6WGkxXrEnUl3Co3CBXi3CZBUlpV/foGnOQ==", "signatures": [{"sig": "MEQCIArSYLzIP39GDI9bSPbpg2mT3tAroTO3o/GNUILvGJ00AiBYlUP4Mq7afLpzRflAcTxEGk5z708fxmpknUdaj/Gu1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrOxvCRA9TVsSAnZWagAApikP/1qFlibrlM+n/2xsemvO\nr3+GlNqtEZLtZhhLNl76J1WFtGMt4oe6en0xra/vGVEu3AMUelYStlaWLzO9\nXWhpcuWLfrQGbCjmFeQP0OzjDA54Gow6nsjh0vdj9ge5CvtJCm9MOaRB6HuX\n4olvozR5XXnWnx4i8R0wWaxe+Cj8YCUMJF1foSQRl3nIP87kaYHwJZ3OGuqR\nFY6IZ3k9S9SCjWAJEAPqpbmthahs8LNva0HjxY7uKOrtZ5Uuq3LdPqvyf540\njPcIBsianJDtQBnuecFKhzV6lUvwJ4X+ecQdc9WjT4kNlOKK2Zz5oOO6TEqW\nEJutg7KWK3NK4oDycABLDRsit5oLN4lE+oGGqnJTIRuXUAXWyDqLTy6VrtI3\nuQkvPbztz8ErN5pgK+q2OKJm7mRmKo0w0EbEdWVD2lUl/ntQRvSCLSKva6Qg\nfE0oHTLvkraE0SkoQYYZCXxhTK2QvjRuqOepMdc185I/pOxpN25dNHLJFQvW\nqM9iE2IvA42DWntShY9dVhp3FwY3IF+pepfJIX/VLiFcNm3UUlcURP+imWSJ\nf/r47CzfsvvM/4Kvew34c6hpiw3H2aGu8hNHQZH2BJ8l8/lZ4RsEI3+XV/zQ\nqKeo/ogZsRQn+4qPjUO7hF/+DTDxkuBnJ1YMMXZtqVcBaIzSFe/JEJRYLjr5\n4qce\r\n=RbS6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.17": {"name": "nodemailer", "version": "6.4.17", "devDependencies": {"chai": "4.2.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.2.1", "proxy": "1.0.2", "sinon": "9.2.1", "bunyan": "1.8.14", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.8.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "7.0.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "8de98618028953b80680775770f937243a7d7877", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.17.tgz", "fileCount": 41, "integrity": "sha512-89ps+SBGpo0D4Bi5ZrxcrCiRFaMmkCt+gItMXQGzEtZVR3uAD3QAQIDoxTWnx3ky0Dwwy/dhFrQ+6NNGXpw/qQ==", "signatures": [{"sig": "MEQCID0Lu50bYi5ysEgVCIRegq8gmAE0O3XGVhd6vKRQuhjoAiAzW+vbWVRsRHPfo261HHDACw961jMuYFuaRyMv+KlvTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf00tsCRA9TVsSAnZWagAAXp8QAJNFdFE1ZObTXiyxaMkK\nX8Rjhf1NVMu/uH4V3yfFkn49HUV+cNid+StAIrZoLabWxBHXMXaFwV84sHK0\nV2lQTgAHMnDm+paBgtRZzvgnd7xOHNL63bJGYODhFZJsCiLgYQfXhT5H7wRu\nZ17tFqXV3h1Qps/NqneCAoyweENLWIqAxwuuX440eyxOGx5+fvlu+7OW/58W\n83KZPsSd9hDcD06qvkJXG7b7FwwGuOhaFB8c1UWrmNnOIEhpWWFhbBv3Jpio\nqpa4TP9SLx6M1WWzvra4A0ltK2aGS9Ntv3Wr9dANt+7xJG5pe6/O0mnXziWe\nEVK+YgXrK23F4t2WexeOkssupTzBZabevncD4BHq+Uxe6C86CXu3hclMWWaT\nteO9lEA0YozmcIYXAAYg0wAkWhnTuiaAskkXbSE2nfVPoJ1HZeIF1+bv8Sxc\nD1QJ0r0VqTGSZXY1cyh1RjbepNOV7pV7+yJCvU7nal2BVzmiVQM/Y1i9gWNr\ncgQJr3nUB37Qv05cW4KoYxblOdbrXSiA4QxnMd0bDeylhuLTEY+A3iWYoVL5\ncutVhJ7EBTh6HW3lrSBLWoqvH1hvNzTT6uUbiiCoyTHRmhox/MXE4lUZyuRQ\nZE+D/YN21Z9ci8Zrj8YAL3S33oN2f382s9bl2jnFssDWCRWvQdfiYx4FCplC\nrJT+\r\n=nSa1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.4.18": {"name": "nodemailer", "version": "6.4.18", "devDependencies": {"chai": "4.3.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.2.1", "proxy": "1.0.2", "sinon": "9.2.4", "bunyan": "1.8.15", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.8.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "7.2.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "2788c85792844fc17befda019031609017f4b9a1", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.4.18.tgz", "fileCount": 41, "integrity": "sha512-ht9cXxQ+lTC+t00vkSIpKHIyM4aXIsQ1tcbQCn5IOnxYHi81W2XOaU66EQBFFpbtzLEBTC94gmkbD4mGZQzVpA==", "signatures": [{"sig": "MEQCIGiKNsfiDxzp3JiDtWkqrw6EbjBjgsr7wTx8g0iiLBkjAiBP/iP+wA20FYswpsjA0/OB4rfKD2qjQshO02bggbjV2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 469840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJTFxCRA9TVsSAnZWagAA2IYP/2AUZ7WHjOLNgOmXil/3\nRUNegPkoXyrB4RyNamwEf8wUpT0QOzqtQYQgNxYAnemfvvliXHqJp8dGd6dX\nC8/sKpkbKp1QAi0utFPbteyEwQeTl2Ph4iraIyAbRObcbG7qIFIyoAlhflPU\nwMRwNaqno1NDItgL8Gsn3XWgurmwezIPrqZa3VsBQxhnpj25dlGivFLsOO6x\nNE0/By1nNg1vHiQ+YBApVCc85q3nJtDn/BhkjXx3OrWItv1a6TKqfb0mNegx\nDtYbrYpROJQVzfxUAnbdpT5WOswA+ardIM7TmYHVPiw3fJeTxV5aOnCxsRFg\nlVx41dV+1pl+8odAulMHhs1DK+1EVLL7P18fgOvEM85lC4j0mF7fqjwKZwKw\ntDzSGV82iczUVsH+MeqoTybhzsCQOq4nO6zP84W8d7j8SVcvNvvsdc2PiOTX\nuqclXLIjmXwoJ61+u03iJfYU7gDdaK4CfXcmyufEI+XC/Gc8emOYkR+lZMcl\n16Pewo62P3U0a3+TBki0cjJtuSuHYPLLUfF1T0iXRD66NKx1JDCZzgwzf3Lb\n0KHaV3VYxIIkJDRWKTxdZy/Pa6MJTs3EPaiDlvWi+hDOUpNOXmFHsnar+Zpo\nPujXP3mRIv8SfEXkukdxA1yuGlJMudvQJXdBYQ9ivSdhNZjAVLOycn2TJX9V\nT95Z\r\n=Y6pB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.5.0": {"name": "nodemailer", "version": "6.5.0", "devDependencies": {"chai": "4.3.0", "grunt": "1.3.0", "libqp": "1.1.0", "mocha": "8.3.0", "proxy": "1.0.2", "sinon": "9.2.4", "bunyan": "1.8.15", "libmime": "5.0.0", "grunt-cli": "1.3.2", "libbase64": "1.2.1", "smtp-server": "3.8.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "d12c28d8d48778918e25f1999d97910231b175d9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.5.0.tgz", "fileCount": 41, "integrity": "sha512-Tm4RPrrIZbnqDKAvX+/4M+zovEReiKlEXWDzG4iwtpL9X34MJY+D5LnQPH/+eghe8DLlAVshHAJZAZWBGhkguw==", "signatures": [{"sig": "MEUCIDwQpJfhya0k/Ug0YepkukF/f3Z4nfrn1+31On/WXNSpAiEA5POdsfbhr4oWEZEVhE9+qjYINlnj9FX0Yh2X8YAfLjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 471241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOLUnCRA9TVsSAnZWagAAY/YP/i+ICJAYfrO4nkyZHgqy\nlvQ8v3taQVuYo47CsAHM7Do8L/HFKkR8kaH7gvod0PZHdPbww5J8RsUe77dX\no7vcKCtjXTliwSarKhmE6rIO4DEuO89mKn9X62F8D3aTyhXuPGhVsc4X3L3l\nD2ZZ3wn9+tHEHxrlFd4r7T2XMu1D0yWWzqstnzeUDe8+u+YlK3XdKq1iElKW\nsst1prRN255mnwMRFO8yHgetSUlfTbzCnnwZknSzmKUGYBf1DuCg1F3Vqj+s\nCO8H4F3gG2lQYWFI7iXP3G3zUJWU0puEUoAI2a5UrmV/F3/AmyKeH2JSt5zO\nQyqThp8U1Kv5DJfZ5vR439+gaicxZnx4dZGfCcu6MgRNjA+GIOnCziRLIoBD\ndTbfzkdBvwhWhHmKsMFRdUYx6XBjncqMLaRGpJGOU4FbhkyXBojknnyGbqA6\nAjXFYtgDmdZrpS9zZEx5ltjd8b0hmKbqF8y8JaZw6UQO03BmagNDxE/V0k6D\nFqg+Um0RBy9YKbdvlhYvQ/SvljHFEYJzzwkzUMWOAbZfq70AIJqn2yg/VvBJ\n8RC1Es7veUmsNFQNP9OVlaN18OhS8jZ43Chs8ct2sZZwe3lhQuHcrBW1ZKx5\ngX748HmvfG1wN7tz4lAAU4BqscPQC6yh55/lmMZTFS+wMSXSww+k2cKHUvnO\nr3Dk\r\n=L/7D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.6.0": {"name": "nodemailer", "version": "6.6.0", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.0", "libqp": "1.1.0", "mocha": "8.3.2", "proxy": "1.0.2", "sinon": "10.0.0", "bunyan": "1.8.15", "libmime": "5.0.0", "grunt-cli": "1.4.2", "libbase64": "1.2.1", "smtp-server": "3.8.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "ed47bb572b48d9d0dca3913fdc156203f438f427", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.6.0.tgz", "fileCount": 41, "integrity": "sha512-ikSMDU1nZqpo2WUPE0wTTw/NGGImTkwpJKDIFPZT+YvvR9Sj+ze5wzu95JHkBMglQLoG2ITxU21WukCC/XsFkg==", "signatures": [{"sig": "MEUCIQCzXCmXIEp0Fu6bc0TIee8LRgC3m0Ttu2wwMY2dgSA7owIgd9FPSQ30rYrB61CrVB/1uUeR8PhlZaEbz+bykBCJx7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 472207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiSl7CRA9TVsSAnZWagAA2kEP/jxWNwpKb5ZrOemsw0SW\n++n/WQvO2jsFCsQVEBiFhJLIEZpqjfoarFJHH2JN5LeIFUDKfPTIbyo5GD9W\nfpL46vifMjkxeDFDpWUPMzGA/pNybREFRqYoUMa8MHGNanHg5lYIo0n2F5O3\nCpPKvRKaucfeeQZP71dSLv2SvhBZG1PJ9p6YE3FkZEzpr08Y1H5WqfHbuzDi\nXOYLJulAteM+glTDTSkFAadTuiIf4zyo2aQfX8BybQo1CA14QEzhBVZAUlJC\ndkzYreP+AqkeSrfOmK0vV7177iv2lfiomSsP8qSlObccEp9G9LU7lt9Luzwg\nDDyzx1t0qm73ed4z+2yq1chpmIj64A1NQPUKiJ+rf1PTx+8kARNZ5dL//zGX\nhgNMKIx3k/BC4Zv3pjAcz2Kcgp3pAKQEC0ZGaI1eVeJeNTds6xqclXPMWQu1\ntpjP2Al1u4Sn61PIBb0ImoDIr/SSzAihRsELIleH/6Hv2cp21fBYrRYZOm1+\nCt3i2duz1c8AEa6NBJ1yASJ5AgGZI5y7suKpUs8wWDw7navwZOmDL7HNVcTk\naDHv8a6uCpDMfs4sOzZzacJphS3yzcwN68tuALJfqwrD94Sj7XrvuLz9xKXa\nD6UGBQJnIzOQqE547u0zkS9aKM4DJZUdUMurYCxscdDPLl0hfibRpSR15DTO\n101n\r\n=mQ+I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.6.1": {"name": "nodemailer", "version": "6.6.1", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.0", "libqp": "1.1.0", "mocha": "8.4.0", "proxy": "1.0.2", "sinon": "10.0.0", "bunyan": "1.8.15", "libmime": "5.0.0", "grunt-cli": "1.4.2", "libbase64": "1.2.1", "smtp-server": "3.8.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "2a05fbf205b897d71bf43884167b5d4d3bd01b99", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.6.1.tgz", "fileCount": 41, "integrity": "sha512-1xzFN3gqv+/qJ6YRyxBxfTYstLNt0FCtZaFRvf4Sg9wxNGWbwFmGXVpfSi6ThGK6aRxAo+KjHtYSW8NvCsNSAg==", "signatures": [{"sig": "MEYCIQC4ZFuVhI9ueMjGGGfgh/glo+4IIZQ9BZzHQXZiRuAQbwIhALXC3yqZdl8k/0gnzmZvmVt6Mw5QAkmQ6WAAQfNoprxH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 472949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqiwnCRA9TVsSAnZWagAAS7EQAJMlPp454DfSeYJifS/Y\ni5P/v6iLJVDBkVGW2dvW3pE4ciJyajwJd+UyZ/nN/AiSKeLDqLAGzvUMLlEK\nOVaIyU97/sx3ydQnkp1RQXBPw8V1WNb+3TzELfHpNoKENXMIzoDRG5QuAWnJ\nTyIJj5lG30WL84BnhElOQSt1EcS3hIfwckcz/X77gFmL3WMUIVR76VFycPMS\nPRgccKpf6r+9RZ3kj2KkKv/WUGy+VhrD/Mab1UqccJey5qqhTmkKWJCtIB9o\ncaCQVPIflD7cLW9t/RMu/gjk0PhO7mYGbKOh43Gxuoy+QtdV294nTT4GEFMb\nK04isHBEQkNJuYSWk7wbF4fSStk3QC70tm1CDU14Nr8BljGnGAtBzSaRNOU4\nNwRKKtbkD9BQz2Sr7NOgaUKX2nJNvJ8LTHK+XMYw0VIrDWFsjDVW7meF/c+u\n7KK/vTXNOPwGeyNGBBMukiQUCKmtEuEWi/ZhX9oygCObygmiDg4uXTqQBrYl\nu7ACulu1m944uGDHBETVJ9vBr/7Olj36oJfwSAaaX3E8h9Ca+nrkl0b2LB6Z\nZ23coxK9zKIQ7jeSprAUZbxIP0pF3q15YH+B1iNRG6LXT993dQmpjnI9jHVm\ng1nf6xzJjlywxiIyYmXMRc425CNbsRebAH3hsLfdl8fu1lHU0zwuX0t1GmST\nx6mg\r\n=8GqA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.6.2": {"name": "nodemailer", "version": "6.6.2", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.0.0", "proxy": "1.0.2", "sinon": "11.1.1", "bunyan": "1.8.15", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "e184c9ed5bee245a3e0bcabc7255866385757114", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.6.2.tgz", "fileCount": 41, "integrity": "sha512-YSzu7TLbI+bsjCis/TZlAXBoM4y93HhlIgo0P5oiA2ua9Z4k+E2Fod//ybIzdJxOlXGRcHIh/WaeCBehvxZb/Q==", "signatures": [{"sig": "MEQCIDCZhJUVoNDYqJ7JhKEATfQJvoaMRdhglWdES8cJKlztAiAwV5rtxiouLhwNRCuyTtmWgntzcAsHp5ACmJeQE0ErcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 473057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzDqJCRA9TVsSAnZWagAA5vAP/168AScrMcn0CdvUvhET\nupaaU1yi+RcmQTU1K5vPV1VJUEJRT3yrROE6XkoJlrTc3tAKzroUwJk2QpgN\n0rXGrQ5cGC3UMc0hAvzBDn5QbFHYkRzxSWI81tPPXoIm8uV7mygAU9el9pm8\nKknAa4eSN4ND4MvJVsm0TWCDp8DIrPztXpVkcTaDFOZ4AX3HFN5BYwsIcq8A\nHDMo23No52ZRvOQXCYbEmyGhux7YXJKlrFouza1z3LjgC7cvXN+jGwoB+SNn\nPybGDct4nOVFdazu3y3alsYyDX97wnk2VaEvoOMb3HEEqf5eMneKd9MM17+b\nlfLixY7uzuF3k5x/W0Wi1yjjzWfkfR5qn4karwqh4Iw4WZDyO6cqflFNAG70\nzCCnQ6KF6b8hp0GbQHEaPyZe08orsoXFCcdpqTnzaDiMbgmrDMHWAxhy5/aa\nfwFkn2AQu0HRs54zyvq8oWRW/kT43SuVtsLFyl0pbkC0fbkLJrgFQ49bp9Dy\nwqegovPOT/LXDaDBHemfkSYVqdx7M+LWeff7VnXwekP7V5UZVenSLRAHnCwq\np0xG5QnxjC//K9l02a/HwGFUwxRGF9sB4kK3/934ZCiiHI+pnABsKXVSHk5g\n7x4xTSM0ZQ4HeqgTF/WrSSAUfqJv2yM6K4igA6a9NEmEjPBgqwRLTYmzxfic\nR5pA\r\n=SYo+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.6.3": {"name": "nodemailer", "version": "6.6.3", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.0.2", "proxy": "1.0.2", "sinon": "11.1.1", "bunyan": "1.8.15", "aws-sdk": "2.945.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.21.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "31fb53dd4d8ae16fc088a65cb9ffa8d928a69b48", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.6.3.tgz", "fileCount": 41, "integrity": "sha512-faZFufgTMrphYoDjvyVpbpJcYzwyFnbAMmQtj1lVBYAUSm3SOy2fIdd9+Mr4UxPosBa0JRw9bJoIwQn+nswiew==", "signatures": [{"sig": "MEUCIQD74zRNhRZsjWlx6+UfV1PdLppw2r3qIpyLIL3+9/xi2QIgYu73c9EebaKhAtYmtxDW30SAWEA1Ydk3xa0Rm2cqFFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 474313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7rtRCRA9TVsSAnZWagAApaEP/jbH0Ei70mwAhbXCLuIj\nNMcYs+1waw+KbfAN75gdnLoei+KyzcuUG0w6tZUh0fWQUg5xSb+ErQtRIm2z\nvh3Rap47c6qNTAuhg3DdQAprn21BIHVnifOZY5a9H12SOvGdgCsLlP5SqOcO\n8GgCAy4tZdcKIGcTvhaHJEbr8+nEv7ojCeXmleDWBKpOH3DJbYAX56RqAuAb\nQtOetW5puR6g6Kh2qu8I2wKXEe4GwZBGoXI1AfcSQ0GZkO8qjFWpWdAMYDH9\n4WFxgUJxuVb2WSeAgQW04GhgXrFWTTmNd7EaWBViXDmwpVYhywsJkJhAG9IH\nf0VH8fdXaw1SkxUNPeuvkAysObgdEw0jH7yQg4p4k3khZJPJfrX4I4168K4J\nN4IM7T90z1+zZJDUz/9RHZ/HOSTXNkDYHButkEosf/MqaQqzaTu31vQf3F+K\nRTVOG/VbZlLHrmydXEYrF9iDre67s5GOUNCEo3JijppLSFPYCBeweKsQXSZT\nyTH6OTkv5zTwI2351moyfLDUbYk1aUi6vMfjuikVODH+n8P8GUZmApEQvh0J\nnqLsoVWk0xKEAKKWoZOqVBKfFPfpJOHgIGOkoK/ipvvWI1u3lJOXXB3tND55\ns2n12Pk6laKRca7YR6HuLyI6i3IvsZaz3eZ7gncU6nQd/OEhY5DE3fd0NlpB\nPek3\r\n=7W6r\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.6.4": {"name": "nodemailer", "version": "6.6.4", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.1.1", "proxy": "1.0.2", "sinon": "11.1.2", "bunyan": "1.8.15", "aws-sdk": "2.993.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.33.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "7888b9817cbd59ac842b2b62fb99000cd873f117", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.6.4.tgz", "fileCount": 42, "integrity": "sha512-7ZXmys0IIaX531apjP+m/w1YYX3LGToDeRGTrxaPPdWLPernM8ZM+2w6pYp1eNF6LJxbUbAN8KHmlGaFz/HqqQ==", "signatures": [{"sig": "MEUCIHsUVnAxqJcZEvauaZrPhTE2RApbetBlkUnDevSE7OFHAiEAj7/xsTAQDUNwUpf5uVtTRvMVdz7zrpzzcFTt8tqH5sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 475904}, "engines": {"node": ">=6.0.0"}}, "6.6.5": {"name": "nodemailer", "version": "6.6.5", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.1.1", "proxy": "1.0.2", "sinon": "11.1.2", "bunyan": "1.8.15", "aws-sdk": "2.993.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.33.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "f9f6953cee5cfe82cbea152eeddacf7a0442049a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.6.5.tgz", "fileCount": 42, "integrity": "sha512-C/v856DBijUzHcHIgGpQoTrfsH3suKIRAGliIzCstatM2cAa+MYX3LuyCrABiO/cdJTxgBBHXxV1ztiqUwst5A==", "signatures": [{"sig": "MEUCIBYfl9zBvbtQexMDdJRtXXKOdnMWC8LkOrK4RPZU+zyMAiEApUg0JjSMs3ODQ2faSys71gS7gpGC04Gdn970IM8Azok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 476381}, "engines": {"node": ">=6.0.0"}}, "6.7.0": {"name": "nodemailer", "version": "6.7.0", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.1.2", "proxy": "1.0.2", "sinon": "11.1.2", "bunyan": "1.8.15", "aws-sdk": "2.1004.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "23.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.36.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "86614722c4e0c33d1b5b02aecb90d6d629932b0d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.0.tgz", "fileCount": 42, "integrity": "sha512-AtiTVUFHLiiDnMQ43zi0YgkzHOEWUkhDgPlBXrsDzJiJvB29Alo4OKxHQ0ugF3gRqRQIneCLtZU3yiUo7pItZw==", "signatures": [{"sig": "MEUCIHOo5EIbqP1n0yvKhYAv/7OEtvPYMR3YP+rARL5E837cAiEAiKaFaNOYKkNOvKdm5dc15LQzY3v5DNs6XphwhgY1aj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478910}, "engines": {"node": ">=6.0.0"}}, "6.7.1": {"name": "nodemailer", "version": "6.7.1", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.1.3", "proxy": "1.0.2", "sinon": "12.0.1", "bunyan": "1.8.15", "aws-sdk": "2.1028.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.41.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "09f72f8b375f7b259291757007bcd902c0174c6e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.1.tgz", "fileCount": 42, "integrity": "sha512-E1C8G3rnXrGjznwGP1k+OrW5k4rl0XtqTEB19f7vtJAMYwfxZVSsAu2iY5xJkrZsbVYr6PwwAwRmFlakPoFC0A==", "signatures": [{"sig": "MEUCIQDHUtq0aeFiDy7/A50rFBSjEc/77TiUbLhWL7R7XFB96gIgHRlpurL0K1lYiHbCKdOhauikfsGeZTnGJtCy3LIjR80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478966}, "engines": {"node": ">=6.0.0"}}, "6.7.2": {"name": "nodemailer", "version": "6.7.2", "devDependencies": {"chai": "4.3.4", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.1.3", "proxy": "1.0.2", "sinon": "12.0.1", "bunyan": "1.8.15", "aws-sdk": "2.1028.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.9.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.41.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.3.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "44b2ad5f7ed71b7067f7a21c4fedabaec62b85e0", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.2.tgz", "fileCount": 42, "integrity": "sha512-Dz7zVwlef4k5R71fdmxwR8Q39fiboGbu3xgswkzGwczUfjp873rVxt1O46+Fh0j1ORnAC6L9+heI8uUpO6DT7Q==", "signatures": [{"sig": "MEQCICpbxEOrdhOAvFBDYiEhXaGs2XUzC0sm9LguhpZOnmX7AiAcg7l4wkhS6Uxf9PQFmK7B/PWeYvWMLNWdyWzn2yFYtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 479255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoLF8CRA9TVsSAnZWagAAnPEP/12mIF4K1buh43BFdV8Z\nJXQkOLZ+ja+i/0njli2f9qabP0Acq00mpIKfF3rUWKs6d+iIbRQqjcybuxP8\nqvDrDlESNdKFUAl4EovVnPPdY851QS22ZGu2TdthGU44JCbBOiWT4Pe/0V8j\newg7pBAJSJovczsIAGwJZsDNLoXkvg7R9bGqyzt61ptJ7Di6ikgXawsnMHAS\np+8wYkcu79t/9ert8SQaqsS+TYYrNUeADFNLtUMucX+LKSr0OAmA9usjFbim\nvC2YeoPf6sJQG5zJcTeAdlVESz+uPmLw29RYZ4GIYAajM3jkhoJQ5PJ38eq2\nYBbI2djVRbZEqpIBodUAAX1giVELsX3pXaMIZpn1Vr6u67hNcOT5iStUfEvm\nMupKGntVvfT3lLaPlk7nloOG3W/hoLdhQP79cD8OMjRs/Pmbb40nltqf377C\nvuHZbKowTJ3eXolaHiAea0k4tzcefapFQ+5CM+sb3UYPuGXXteewQ2t0M1CY\no085PEVg4o5sabgzq1yfVu4PCdwJNu0n01PMquyiXRg90FqadA5z9QvcSEHW\nm7J36kCA500f9cFZiyaDWBisOH5U37s6h2Z4KJzR6jmdV4ga83cNbG4rWGmP\nP87IGK6pgVMLuPWxkW9yEw7mS+U0BztwV8w9S720aJ63MUHlP4VSc/zStl/j\n3CVo\r\n=M0zg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.7.3": {"name": "nodemailer", "version": "6.7.3", "devDependencies": {"chai": "4.3.6", "grunt": "1.4.1", "libqp": "1.1.0", "mocha": "9.2.2", "proxy": "1.0.2", "sinon": "13.0.1", "bunyan": "1.8.15", "aws-sdk": "2.1096.0", "libmime": "5.0.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.10.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.54.1", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "b73f9a81b9c8fa8acb4ea14b608f5e725ea8e018", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.3.tgz", "fileCount": 42, "integrity": "sha512-KUdDsspqx89sD4UUyUKzdlUOper3hRkDVkrKh/89G+d9WKsU5ox51NWS4tB1XR5dPUdR4SP0E3molyEfOvSa3g==", "signatures": [{"sig": "MEUCIQDYoiJan0QlgrQcJ5PNmHWbmxa5DvSsTB+09LmRMUGePQIge6d2HnyYEc2ZaiDFEy8+d1/inZyzpZ3Dg07btH3uLz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOEHMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXIRAAiBEVNRfTWQlb7qV02S1CcjtuWcDIAIdHwIDkiJOqJ48pEGHy\r\nMb/L1B98ul8aMxcnp+QJq9QUpOzbJv4zmSXUGjJ8KGQRvJlUlIzW2gyD2JPJ\r\nZSoVIPuaDOp6A0ACEVdoUMxqeIAQMx0Taa1AceOfgeStn0NMhiLBqMcXJnjg\r\nEqEUUtZZLjHD3JdlVMJZHa7ukajLKZDMc4lxAYusjKrbo3PpVJ7iaO2P85/i\r\nw0v/b+XffTulmiCIYjDWJ8fjfWl+RUHcynyZmOcsRNqyXdCmxb4cSPnOqRk9\r\nvRFUP+a7sZGaf3bSQqBEk4MeSb4fcPQGv/qWqFHPqrB7tI1YBnlZwXhXvFFn\r\nJ3CGq7qOgODQL7Dlyt+9rBpoB8zhx3uduwY4m0i/bMtxFkhu81WSjGIpENKn\r\nvd8pMr+z5dY+o6h5JNUY5ygPdsfkpSRvSMjiwnJjgavDYjVoyQJMul2dGmQi\r\nAEXgdYXgYfN+LjmDuGij7Vk+NugsewLY4aa7lcrovqaDAyPEUXKAwvR9DU5C\r\nipv87CheniXrj6d7xJk4fuqiwwnp9AsAh2paPoa1MImnzaPatj1X+43j0u0E\r\nV5EQWS5jV9mal5cMV1gqjloHq4Xa9n7RtLW/Tysm9jkO9ilDP/v7WuNJ8fN5\r\nLlMfm9quHF9Qyldwso6RWvDb6u8NGYQF2BI=\r\n=dEUO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.7.4": {"name": "nodemailer", "version": "6.7.4", "devDependencies": {"chai": "4.3.6", "grunt": "1.5.2", "libqp": "1.1.0", "mocha": "9.2.2", "proxy": "1.0.2", "sinon": "13.0.2", "bunyan": "1.8.15", "aws-sdk": "2.1124.0", "libmime": "5.1.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.79.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "28771bda3dda8f2dad1912aca0f8727ce7f09d89", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.4.tgz", "fileCount": 43, "integrity": "sha512-TBSS3qS8WG45ycUwEvEA/3UM1o3sLz9jUl4TPUKPz4ImWWM6UgRCb5pLO+HOouDKEj57yNLOrzQlO8+9IjWZoA==", "signatures": [{"sig": "MEUCIQC8MCUDvoa0ZjIXp3tu6eOjcGZmpTJqUDPk+fgQUFvCYQIgTweuU4lLc0mXOU80T5SjGwYKkx4HVF20SYQxPAv3TLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 483177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiawx6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxqxAAnuYEQyRxJOa/XvgazDfscOgImCPbACyeZZ/LsRnCupPqCzMs\r\np8mt9uG22ezpvbJBddBchWADoh6E8W9weYom4mEnteitb9ch0Yn4nqFlht2W\r\nkqc8eJ9N7eMOEhGD4s/nXfQ0ftASx4E3lSt1qL4/KEd+XTZtVEnQYF9xIHhg\r\nf0frb/Ubnkdf3wLajuBeRDUQY3S2cfTqLS4qSn2I2hEj1jAq134pu8jLroyK\r\nBylyDhH2Ag6iDGUdZyCwk0oC4EYdwvIMP42VSk8n3uyZkp5ZZ5r/VpGV6yQK\r\n84Ox1YwFdb36qvA3dI8Rtj8cqYq1mMSR2rvCi9YdmddnQMf4bkRJV8YW0wqc\r\nnwzrvF9a9KObTjZL3yT8vPIHffv4zDlpiKxgSD0aq+xMZY5M6xApxSAGW4lI\r\n6pHDRCas74GnExoYB8/TxRFEJh4p92KIyptNHqR920h8hjG2mOEw2K9PeToV\r\nTl5V/RJKx/RHpO5e49vpBUyI1zowi9TnyEMyioL2IkYXK0NiIbEy3uqtQOcA\r\nt6Nub2+ziq22Ama0FkIDWYdl3/Z+aLfSUL9cCL8h/fwHKALDiFxnZ/cSBYBR\r\nAnSYOZR6uvOrepu7LuoOdvKG6a8/xeEDdQjSI9vof6+rMBL+jKnS8C6vJZ9v\r\nvE6VKgvMKOSgO2jcHo5+tlxxCwLg7K8HuKs=\r\n=BqD9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.7.5": {"name": "nodemailer", "version": "6.7.5", "devDependencies": {"chai": "4.3.6", "grunt": "1.5.2", "libqp": "1.1.0", "mocha": "9.2.2", "proxy": "1.0.2", "sinon": "13.0.2", "bunyan": "1.8.15", "aws-sdk": "2.1124.0", "libmime": "5.1.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.79.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "b30b1566f5fa2249f7bd49ced4c58bec6b25915e", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.5.tgz", "fileCount": 43, "integrity": "sha512-6VtMpwhsrixq1HDYSBBHvW0GwiWawE75dS3oal48VqRhUvKJNnKnJo2RI/bCVQubj1vgrgscMNW4DHaD6xtMCg==", "signatures": [{"sig": "MEYCIQDOt74CkukmGjKrpcYR+Zefc9SUePAbOMol+VyMno+6PwIhAI+o8iHCzEeksPbsT2tq6Xoya/Xz/bUqWU6V/Ab00EPM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 483783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJich0LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmod0RAAkmZiOWIbsE6B69PGghZpNckgdNReEnqamRGTPBOLne1RUca6\r\nyxrEmHbAKHsJheg57AhC5D4Lemm7mFlFeAAt5Nz05jHnK861VLMeOuQYf1BW\r\ncGeLLqfjxWagrt2/NSK3gjPZvzT0wsfiSrolezOH3HnUFdEVrp2jtNI6pnqt\r\nLi2odxVTuS8D/K5t0+D4yAxTtFhF3EaozUSuZVxojF+RoEIREYA1dnoF86ee\r\nScEpSTAlAxd3Cr5UxcezBWGmyoJCkU0e8oR2bGNj7iKx2+NePwJKKjEDtLVs\r\nvLeIETm12N54TftGZ+8EKbGTZCpdHV5aOmhj+Blrghr7Lt723k8ao9Q2Pty6\r\ni1j2A2Mh0XJrgbOQDf1nLhMREV1tTIoEXvnJLgleGfLdHh7gf/FDebjPA/3n\r\niduHD1cpQivpp2TrONbKF2LmVy6sblyYnMHprnauS6uF1ZumUw5/UHjQj/Bs\r\ntx4iIz1vKoac+CHSN3rvOe9CToYmWer67p7rm6xW5bTdH3HJAzCm3HvaGIHt\r\nqpqr9qwoholdpVTA18IPtr67pVJajzi57OY+YMt05x/HfeSaMRRYB+uhBOu+\r\nkAzTvq9Ee2A+skxKtT0NL7yM2BsFIvDpCengqmZXhD2Je0VJNSPcmKZZMqBg\r\nBJZsRQXWr1nwFSBxw6UKxnL+6mFsaJjBxBk=\r\n=0HI/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.7.6": {"name": "nodemailer", "version": "6.7.6", "devDependencies": {"chai": "4.3.6", "grunt": "1.5.3", "libqp": "1.1.0", "mocha": "10.0.0", "proxy": "1.0.2", "sinon": "14.0.0", "bunyan": "1.8.15", "aws-sdk": "2.1165.0", "libmime": "5.1.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.118.1", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "d3de8f644eaa0dad784d1be1375c596de492f3fc", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.6.tgz", "fileCount": 43, "integrity": "sha512-/6KF/umU7r7X21Y648/yiRLrgkfz0dmpyuo4BfgYWIpnT/jCbkPTvegMfxCsDAu+O810p2L1BGXieMTPp3nJVA==", "signatures": [{"sig": "MEUCIBET1Kd6XTuN74PsK9L0VXcnkEbNdrtqag4X3Cj/r1AEAiEA2n/hCecvZ2FSD/cjgUBIFwm7o6CfIvPkq6UgJQL/MPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 484521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivZP3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdsQ/7BVxN2vhcOISQAoTThHGZj43Aju1N2jyxgBeAbfoNLYxSVCQ+\r\ngw0vE4/xRpO3hkUZbbcMmz37T0ZG78x8zL7SiXE9nIhF/pgo7lyzrYutwUIX\r\n035JTnVtbpTuf9By6S0D7ut5p5RJim4Gy0LQUyz4HXl+amSxZQ44C2RLJZD4\r\nMwuM6gHTK45kcky8xa1VKiai7+WliD+PBuhaCMgSW8bPqbDaAW4LYnUUApuh\r\nNMSwftwrPqlBLGHoR6o+ILOPCaRxhJhq0MYrDWBSx5cY5KGMK5pvlCTfaV05\r\nnqZ9nQqfTuH5dtLJgW8ZXOBjcTKdrKifEGeCrdLtmlv2wWaZKpcqoeVjN/0o\r\nYMllyNXlGEd3y3IeS74thAZw8y0aK6t8eZ3acthDsL3M5+5h8h4fsMhem+qc\r\nhD22HKy4CCNHe/kxee/wJYI0u8PS59qECIR8XfZabTkrOtPxw8nTTMg/X2HQ\r\npHoeuqHt01uu1xbxGltb+TPzoqmzvChzi70IsWmeRZeqIDdHC1fpW4kxDKjv\r\ndrJks+p8Jo03thWYKMJEKTi1ZGlXRR+zQtmiHVbAUiyfqohBUQBAfnYlsfTU\r\nO6vliAYE0a2fPJmABs1UAOEaPp6y+bPJbzFtXpsayWXHHnzn0IbWeZQVl3If\r\nYlE9eVv92NwVMUhhbwxnKqm7N2BwLJtMprE=\r\n=uzu6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.7.7": {"name": "nodemailer", "version": "6.7.7", "devDependencies": {"chai": "4.3.6", "grunt": "1.5.3", "libqp": "1.1.0", "mocha": "10.0.0", "proxy": "1.0.2", "sinon": "14.0.0", "bunyan": "1.8.15", "aws-sdk": "2.1168.0", "libmime": "5.1.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.121.0", "nodemailer-ntlm-auth": "1.0.1", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "e522fbd7507b81c51446d3f79c4603bf00083ddd", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.7.tgz", "fileCount": 43, "integrity": "sha512-pOLC/s+2I1EXuSqO5Wa34i3kXZG3gugDssH+ZNCevHad65tc8vQlCQpOLaUjopvkRQKm2Cki2aME7fEOPRy3bA==", "signatures": [{"sig": "MEQCIGnHcEX6SfNHwoWRAmfu/0sDlP+0Q3ejEqvvpbZMBO1aAiBJVQL0yhZ/PawHqVCLryxLlp2NORzY56iNI8lT4eQH5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixXtNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZSA/+I0yn0QLfr3lrXCJ3txUUSuLiPF0YrOdWwdFehHifb96ytKX5\r\nHi6jG1mvrFuSMCyJ/Io5gwnYBOosNYvP3Sye4cXFRuA5u8uIAuGwXBB5g/CZ\r\nDbo9mkTavUNSkwROAWWugmwoUybWtPVgIkxNF5/GhauEljls915lYuIcCjLj\r\nx0P6553icDscadC/XzE4i4aWb+Jrzk1g7n7Tw+tcRbwhTfmZxTZUA9foMkHb\r\n0NrDQfRMuVPlJSVzac6y61njtSJoRz84D/t91TGEl7+YaUUAPRkAayjWP7y4\r\nP3zJ9smbmsvmQ++48xpF9SSPGHhHR6m6KXVRaCO3sjZ9pt9bxIsHOzGIfcWm\r\nhiLEWz57m/GXK1GCM7wIezSPvai+lS/Zcc7kK5QK1zsh6b43FJXEgL5EbEWP\r\nqhkHberZ78hFQ1tu2kE2af9X4IBYO+Sw7/lZwZvA/rUL6/aFIj37FDbbwhBX\r\nKRXkgmMh9bgsdTSNZ1S73qePgRdfOXHeGlA1fR8wG2jl8SmKPeW7sOxvUvtd\r\nRY8K///fhLYY73+jUh5HMbAEqaLy2XfT/v1oevXg6MLgZo4W2PLutg9RV1n4\r\nLJJS4hFkPNBfo4NXV2BGtR9JDzcO+CgFUmVCvBK9HCDHJrRxkUHm+m5TpdOy\r\n4/0fX2G5tEctoNBnvDGioYrIUvMaGuCro+M=\r\n=cz0+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.7.8": {"name": "nodemailer", "version": "6.7.8", "devDependencies": {"chai": "4.3.6", "grunt": "1.5.3", "libqp": "1.1.0", "mocha": "10.0.0", "proxy": "1.0.2", "sinon": "14.0.0", "bunyan": "1.8.15", "aws-sdk": "2.1193.0", "libmime": "5.1.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.145.0", "nodemailer-ntlm-auth": "1.0.3", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "9f1af9911314960c0b889079e1754e8d9e3f740a", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.8.tgz", "fileCount": 43, "integrity": "sha512-2zaTFGqZixVmTxpJRCFC+Vk5eGRd/fYtvIR+dl5u9QXLTQWGIf48x/JXvo58g9sa0bU6To04XUv554Paykum3g==", "signatures": [{"sig": "MEUCIEgsuccbZYwMVLsyzP5bMY/6cT92Aquqe7ccwNVqgjJ6AiEAmbHFFDNB1ZzlqcK6B19S77U2o0t8/0HXCRsjJYpjcfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9V0pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOvQ/+NrgZQ1OoGefQaNXRHwGuo4QaMnSPQjcXhVuqVbnA3ySrJVgP\r\nRHXjBi6vMopwump4M3LIOiywWL+HA2DAF2uyyIGOMHhq1xes5Z4KP40BC89S\r\nPQTglmiMfrpXdYkaumsh79NHCgm0jC8IVNxj2+gj3H1vhPhDGJxw4TELeBqQ\r\nhQbHS79vdiODJh7yjJ75Wz+mzy/rB7sti05tbNDJp9gUNeodzQyQxnj42sE2\r\nCIAjfCbKcUXr8eXWTu7NigqDYWtcvmvkUi7iL29MiFbN0QuKr+I0S7c4ZJvQ\r\nyqPsTsQT6GQpykZMCEf5AGZLUalnveIulL2nJ1M3wyGPAkfowzyIii3Ta0N3\r\nCME8njxGwoNBainC+3plRDanfoGVc8+O7UAE1s++wXq6X01hYEO07OGFEmD7\r\n6+LEUgTg5Fd+3h4mFsmVRd7lO0fTjiSlclUx+nZadxkHUmdEsePYEk/chv/7\r\nqxIyCCdF6+roX2y8/AKFMiJ9KfhdR1XPOa0uvoeYdvjMMKV2MXBc8g5OwQxg\r\n3zZvRVuEqceuEhNAY/9OCIpu1YRbJFGNdJb/594x53Vax/hOkPJl48QcWTlD\r\nOG59Aps7NRAPk+5qSaHEnw5N3HNn+EwkFbGtyx9eSMsoUwZukHr4mbD0bjCg\r\nTf7ClXtwFcPYrW13ybIOk5oQpwjH5w0enOc=\r\n=AUeG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.8.0": {"name": "nodemailer", "version": "6.8.0", "devDependencies": {"chai": "4.3.6", "grunt": "1.5.3", "libqp": "1.1.0", "mocha": "10.0.0", "proxy": "1.0.2", "sinon": "14.0.0", "bunyan": "1.8.15", "aws-sdk": "2.1225.0", "libmime": "5.1.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.180.0", "nodemailer-ntlm-auth": "1.0.3", "eslint-config-prettier": "8.5.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "804bcc5256ee5523bc914506ee59f8de8f0b1cd5", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.8.0.tgz", "fileCount": 43, "integrity": "sha512-EjYvSmHzekz6VNkNd12aUqAco+bOkRe3Of5jVhltqKhEsjw/y0PYPJfp83+s9Wzh1dspYAkUW/YNQ350NATbSQ==", "signatures": [{"sig": "MEUCIQDNBsbngHERcx4qovOV19n9e7C0IkwmUP8z+Bleg/iMbQIgDPJ+53eN3NdsE143HZBatMMjOmZ/+9sE791B53EIVxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNH0xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrrA/8CdHG3+yr5JHafb5DtrOy+ZmjSwTSDxB3rClD3y1Cg0BO3WY9\r\n8x2/ON8BLa2gZ8p/DsBPtRo1owRuFOFybpO+mVegyjBU1ZHj87sZAxw5lKTb\r\ns2xiA6zPuKvJdFWfus2MdfhLSoGGYccH2/gnpaVlbhCWmvpbHNLC2TPse4nE\r\nP4X2F5lnj2C+d21luWgKUjeDmtkOwvrDIwK8jCJ4S2hgO0I3/00mv9rNPBEu\r\nx8NLtkTT8Ch7XbUGIrRRsQc/E8JI8a8jPrdS4Z3mSYHshpfytUsE7ZbDHfRv\r\neAtCEX19MNIc0UZTkLyvyyaM2mefwnwwND/72OJHXKIBXyouB2i6T0uQEJH0\r\nvlAyF6OBxHdGtDrSgxQfK8z++Q3kkF6CsaHgUZI23E5tmeDRcF1ARYKFg8aM\r\nYJEXrppsf+o+KN2yJB7HP4YKza3hQsNnpV/2eLIxVHKs15oCwX6FuCCVtkhe\r\nKl9qfkKTWrhzBgc5n7tYMADhJoYXMCl78cEjdxr0dZtElxzSr9MLPaZKgM3t\r\ngD0iP0IKhWrqXQJG6Nv6pRUanRE6DyU4zrSt6c718Tg/EqwPWDvJoyL7Yy9v\r\nnbo3Dc1kRKm1qg4y4D6bpoadMhYN1dQ9/at9Qa7K/OK8AqeFtoxFMHDW0lTP\r\nGPjjS6ZmgXATXooF9IdUZ+7hEdqPNuFVyn4=\r\n=i73s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.9.0": {"name": "nodemailer", "version": "6.9.0", "devDependencies": {"chai": "4.3.7", "grunt": "1.5.3", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "15.0.1", "bunyan": "1.8.15", "aws-sdk": "2.1293.0", "libmime": "5.2.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.1", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.245.0", "nodemailer-ntlm-auth": "1.0.3", "eslint-config-prettier": "8.6.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "a17488ff470ff9edf1bb31d9ec23079bc94f7dd3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.0.tgz", "fileCount": 43, "integrity": "sha512-jFaCEGTeT3E/m/5R2MHWiyQH3pSARECRUDM+1hokOYc3lQAAG7ASuy+2jIsYVf+RVa9zePopSQwKNVFH8DKUpA==", "signatures": [{"sig": "MEUCIDWyARH2uYOYirNe8fZURyEvGzpg9/57zzUbxTFXXtJyAiEAigxCOo/R6MPbwy0rPk1Eai4Fx4hDCjSFKstKpTVczIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjv798ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodXQ//R2COhZ4DRG/cRuyM5H/lbtzxvTJk4OZGOs5AkYh/heCmHhjH\r\nQ916CV0d+xT9zc01zdiMuCLuVetn3D9zaCybItohK1O3RzwCOF4Bv+MjqO0+\r\nLVve+Ga/9thj/0NBFtwO72CRoow4eNLRiYIPsSK1ei74oBZxRIFz3dSahGB7\r\nBUfp9OjzFt2/8Vx/m+3n1oM4ec81oTr8CpeEPsaZlq3OSiOOCbIYuAmO/KR/\r\ns78kNUxDJ4UHWQD0NSsPK9lEkjIJIxmA3Z4v2hRAjuOTQt/fp/CTvxkVHd3S\r\ns6CzYNUcZpKNFwZ1vGIG5q/ISCGwi0lkPtgvgC0Vu3MQNrpN8hsCo/yXfj/p\r\nnzxP8CK0Cv6CGQ8pE8Y8UecH+H1x1zoAtV9bU9PE3vf0AJnDrS+nGZjgidQK\r\npJX35oQ2X112cU1v37msteyp722zQg7F1EvA1djkmN2WxMZPGCg7TAOq9KKc\r\nnBjStAeZxqIyVKQdqrxI6EvTYMiGrW2KqfhxEpnIebvlnlHMD2dSt6YsXOEU\r\nQ4PECq2yz4TtRv1MoIuux/I+yjxJEdqOd8I+n2ZvXGIrd1fpeZyKoiU0EMBE\r\n4rfQithx6aWZ5jpT/+eG28dfHkpenmDoKRXkVS2ZU+1VKZSlKg9OGOX0UcU3\r\n7NC5ilVyqIcPDGP7Eylsun5NwXyUaSLC0dQ=\r\n=cWTt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.9.1": {"name": "nodemailer", "version": "6.9.1", "devDependencies": {"chai": "4.3.7", "grunt": "1.5.3", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "15.0.1", "bunyan": "1.8.15", "aws-sdk": "2.1303.0", "libmime": "5.2.0", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.0.1", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.259.0", "nodemailer-ntlm-auth": "1.0.3", "eslint-config-prettier": "8.6.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "8249d928a43ed85fec17b13d2870c8f758a126ed", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.1.tgz", "fileCount": 43, "integrity": "sha512-qHw7dOiU5UKNnQpXktdgQ1d3OFgRAekuvbJLcdG5dnEo/GtcTHRYM7+UfJARdOFU9WUQO8OiIamgWPmiSFHYAA==", "signatures": [{"sig": "MEYCIQC4Y0upHJAEAsSpM68ZECACVWZ6Fzkb2PfvE2PeadFBZAIhAIRJHDjXdtHz4HGu+DFFpWT1sw/0lGrwIO0kEUOvvT3F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj05x/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/mhAAmSLkkDiIm/XMs1HFjlKLOjohWSC9j14xBywX60l8k2uu9J5d\r\nFXaGIF22fDJbk0MdkGGTfDJI97FvcxVsmVdV5edjaJhuQ7sIc2ShIeKqXwNh\r\nov5LrQ3JejO0SnaUd4pkOz98I0h6PK79b4Mwf3KADp0tc9sEYh/0w8+k+JHl\r\ngrWVr0bJiyhgWkfj1NMd3AEkxT3SAf+ojERiSA363zqNeIoj6lB9Wsl5y5uh\r\nxUoNcqlN4kmUfzWHc1IbxADgcoBLjfOaSS040RAmwrJPpv87QrR/GpX2S8Yq\r\nRTD3ZNP3CQboJDZ/wbkCeKaPem1543BZ8Gu9nkLMfYyerPIPsbNA3nMdzAEF\r\nHql0cydM2X0+4RHQsh+CKzGGdCrrg/Ar7lz3JwnLgoMjLZEAnl4FaBXGQFn/\r\nkjiB84oXGIAapwrgIa/+vw3QTtzhcn+j0hfMH6al2nMJ1Ee8FtCtbeLWFsVD\r\nZwgsf8FcvdIAc5C8/RuQmIR2lA9waNmA9/rfLZzxEVW3zuZKv2/6ClWHmGRv\r\nndC1Bex2IJ0xdaiEOG7ry84iDRv5UzK3TiDZxgLDRt6EID95p7mM1fQZ7gno\r\nnry0GSHSscLbmftGur1T4bFmn04hw5fJwQtIxJlp6cYJwzOn8QUpju+uoF9d\r\nGbdZBcL5eM1KLB9x+fJI57YrfrvrRJqF9bM=\r\n=IN/v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.9.2": {"name": "nodemailer", "version": "6.9.2", "devDependencies": {"chai": "4.3.7", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "15.0.4", "bunyan": "1.8.15", "aws-sdk": "2.1376.0", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.11.0", "grunt-eslint": "24.1.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.329.0", "nodemailer-ntlm-auth": "1.0.3", "eslint-config-prettier": "8.8.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "b79051811edd52c2436ad1c6aed2dc45b9c9cf1f", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.2.tgz", "fileCount": 43, "integrity": "sha512-4+TYaa/e1nIxQfyw/WzNPYTEZ5OvHIDEnmjs4LPmIfccPQN+2CYKmGHjWixn/chzD3bmUTu5FMfpltizMxqzdg==", "signatures": [{"sig": "MEUCIQDOH9wvRrfjL/gPYjIBdhStvFXEXh73NVASrgLt9aYJsgIgKNDFtCpMxzNQN/hG1S45+ZK2k2CEfjPzpJ72dGBtNn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487816}, "engines": {"node": ">=6.0.0"}}, "6.9.3": {"name": "nodemailer", "version": "6.9.3", "devDependencies": {"chai": "4.3.7", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "15.1.0", "bunyan": "1.8.15", "aws-sdk": "2.1386.0", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.12.0", "grunt-eslint": "24.1.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.341.0", "nodemailer-ntlm-auth": "1.0.3", "eslint-config-prettier": "8.8.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "e4425b85f05d83c43c5cd81bf84ab968f8ef5cbe", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.3.tgz", "fileCount": 44, "integrity": "sha512-fy9v3NgTzBngrMFkDsKEj0r02U7jm6XfC3b52eoNV+GCrGj+s8pt5OqhiJdWKuw51zCTdiNR/IUD1z33LIIGpg==", "signatures": [{"sig": "MEUCIQDPcSHNToonSzw01lVZPcoRCyojk+vVqyUIzz0SoRm6OQIgey8nGTBFVyx+apsklrnMv+39DihN2PGMBCozrX8NwvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488998}, "engines": {"node": ">=6.0.0"}}, "6.9.4": {"name": "nodemailer", "version": "6.9.4", "devDependencies": {"chai": "4.3.7", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "15.2.0", "bunyan": "1.8.15", "aws-sdk": "2.1417.0", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.12.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.370.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "8.8.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "93bd4a60eb0be6fa088a0483340551ebabfd2abf", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.4.tgz", "fileCount": 44, "integrity": "sha512-CXjQvrQZV4+6X5wP6ZIgdehJamI63MFoYFGGPtHudWym9qaEHDNdPzaj5bfMCvxG1vhAileSWW90q7nL0N36mA==", "signatures": [{"sig": "MEYCIQDPkc2Uau9D+H9pkzvtyziBnlToD1W46KdK8Iy02V2HGgIhAJA1lNmeK4EI6JqSPrASQs9uoP80ZMOmG/aa4m/5OObP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 489073}, "engines": {"node": ">=6.0.0"}}, "6.9.5": {"name": "nodemailer", "version": "6.9.5", "devDependencies": {"chai": "4.3.8", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "15.2.0", "bunyan": "1.8.15", "aws-sdk": "2.1450.0", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.13.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.405.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.0.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "eaeae949c62ec84ef1e9128df89fc146a1017aca", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.5.tgz", "fileCount": 42, "integrity": "sha512-/dmdWo62XjumuLc5+AYQZeiRj+PRR8y8qKtFCOyuOl1k/hckZd8durUUHs/ucKx6/8kN+wFxqKJlQ/LK/qR5FA==", "signatures": [{"sig": "MEQCIC43UYlaNGT7KC39zlIQw3s2nO/G52ax8M5Yj0cmCuxuAiAmXkgWMgwVBzB4TWIB5H7fNEa0EzoyFNjTWsw9R47Rfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 481747}, "engines": {"node": ">=6.0.0"}}, "6.9.6": {"name": "nodemailer", "version": "6.9.6", "devDependencies": {"chai": "4.3.10", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "16.1.0", "bunyan": "1.8.15", "aws-sdk": "2.1472.0", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.13.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.427.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.0.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "5a38a2a4d9fb1e349542a138f9e2d5c760a663ed", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.6.tgz", "fileCount": 42, "integrity": "sha512-s7pDtWwe5fLMkQUhw8TkWB/wnZ7SRdd9HRZslq/s24hlZvBP3j32N/ETLmnqTpmj4xoBZL9fOWyCIZ7r2HORHg==", "signatures": [{"sig": "MEUCIQDg5XSkLrC1mQSoHrnuoZWh90t+cC5RJ89hbm6Qx4OmEQIgDyqQhKgXJMwcFwIBawj7DzRRfSXd7gtCOOnnEv12CT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 482493}, "engines": {"node": ">=6.0.0"}}, "6.9.7": {"name": "nodemailer", "version": "6.9.7", "devDependencies": {"chai": "4.3.10", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "17.0.0", "bunyan": "1.8.15", "aws-sdk": "2.1478.0", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.13.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.433.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.0.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "ec2f488f62ba1558e7b19239b62778df4a5c4397", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.7.tgz", "fileCount": 42, "integrity": "sha512-rUtR77ksqex/eZRLmQ21LKVH5nAAsVicAtAYudK7JgwenEDZ0UIQ1adUGqErz7sMkWYxWTTU1aeP2Jga6WQyJw==", "signatures": [{"sig": "MEUCIEwMvQ7kLmqb9+sZyrqeUcdLUVl1rrLOwKJR9Sd6uXblAiEAmVNjfVsob+uCZc4y/U2OGFPZukDxdx+o/BAEvuAIC8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 483818}, "engines": {"node": ">=6.0.0"}}, "6.9.8": {"name": "nodemailer", "version": "6.9.8", "devDependencies": {"chai": "4.3.10", "grunt": "1.6.1", "libqp": "2.0.1", "mocha": "10.2.0", "proxy": "1.0.2", "sinon": "17.0.1", "bunyan": "1.8.15", "libmime": "5.2.1", "grunt-cli": "1.4.3", "libbase64": "1.2.1", "smtp-server": "3.13.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.484.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "29601e80440f2af7aa62b32758fdac7c6b784143", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.8.tgz", "fileCount": 43, "integrity": "sha512-cfrYUk16e67Ks051i4CntM9kshRYei1/o/Gi8K1d+R34OIs21xdFnW7Pt7EucmVKA0LKtqUGNcjMZ7ehjl49mQ==", "signatures": [{"sig": "MEUCIQCQ7wY/+GpDiH4++88/XTySHgX3ENT2YqDY7aWl1es/yAIgSe8GbHCrMqE1XxLD3rv0SWnxKDLQzBlHhyBq36r/Q9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 499193}, "engines": {"node": ">=6.0.0"}}, "6.9.9": {"name": "nodemailer", "version": "6.9.9", "devDependencies": {"c8": "8.0.1", "libqp": "2.0.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.56.0", "libmime": "5.2.1", "libbase64": "1.2.1", "smtp-server": "3.13.0", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.484.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "4549bfbf710cc6addec5064dd0f19874d24248d9", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.9.tgz", "fileCount": 43, "integrity": "sha512-dexTll8zqQoVJEZPwQAKzxxtFn0qTnjdQTchoU6Re9BUUGBJiOy3YMn/0ShTW6J5M0dfQ1NeDeRTTl4oIWgQMA==", "signatures": [{"sig": "MEUCIQCJl2YxxeLmJDhvS+5kihsuMnz1/GOj14aaKVftzR/FuAIgbM1dJycl8nlfi094agiykWRmab16AlhW94jNuXhfdjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 499888}, "engines": {"node": ">=6.0.0"}}, "6.9.10": {"name": "nodemailer", "version": "6.9.10", "devDependencies": {"c8": "8.0.1", "libqp": "2.0.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.56.0", "libmime": "5.2.1", "libbase64": "1.2.1", "smtp-server": "3.13.0", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.484.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "54cc1e062f20726606d3289fcf38ee731aff0d9d", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.10.tgz", "fileCount": 43, "integrity": "sha512-qtoKfGFhvIFW5kLfrkw2R6Nm6Ur4LNUMykyqu6n9BRKJuyQrqEGwdXXUAbwWEKt33dlWUGXb7rzmJP/p4+O+CA==", "signatures": [{"sig": "MEUCIEeZwBrZ7DZ8va+apPjewrTDKdKcTNumTWayp+obc8PUAiEA+UwONNnPYSK/Ers7UQQeinalDDk0Gs5ym7Ll9i+NApg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 501590}, "engines": {"node": ">=6.0.0"}}, "6.9.11": {"name": "nodemailer", "version": "6.9.11", "devDependencies": {"c8": "9.1.0", "libqp": "2.1.0", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.4", "libbase64": "1.3.0", "smtp-server": "3.13.2", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.523.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "900703bd6dc62855d9cea962acf261926841f825", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.11.tgz", "fileCount": 43, "integrity": "sha512-UiAkgiERuG94kl/3bKfE8o10epvDnl0vokNEtZDPTq9BWzIl6EFT9336SbIT4oaTBD8NmmUTLsQyXHV82eXSWg==", "signatures": [{"sig": "MEYCIQDqqHIbHv3B/HE29JRY3KqScSuLQAfR5j4hECD5heF90AIhAOi8RUAwe3iK2royhp1aogpj7WLSv6J+bPnc6OXazeOj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 502236}, "engines": {"node": ">=6.0.0"}}, "6.9.12": {"name": "nodemailer", "version": "6.9.12", "devDependencies": {"c8": "9.1.0", "libqp": "2.1.0", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.4", "libbase64": "1.3.0", "smtp-server": "3.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.529.1", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "48560eaf3575f8b0215eb38a45ca9025ffad2314", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.12.tgz", "fileCount": 43, "integrity": "sha512-pnLo7g37Br3jXbF0bl5DekBJihm2q+3bB3l2o/B060sWmb5l+VqeScAQCBqaQ+5ezRZFzW5SciZNGdRDEbq89w==", "signatures": [{"sig": "MEQCIBMYCtmJ2UxH/kTfcLZhe0uOTsUkJKNjHGat0GNBpLOXAiBSi+m4g51OugobhsZ45Yuf+tONvgI7mu3CEHrmp7QELA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 502511}, "engines": {"node": ">=6.0.0"}}, "6.9.13": {"name": "nodemailer", "version": "6.9.13", "devDependencies": {"c8": "9.1.0", "libqp": "2.1.0", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.4", "libbase64": "1.3.0", "smtp-server": "3.13.3", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.529.1", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "5b292bf1e92645f4852ca872c56a6ba6c4a3d3d6", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.13.tgz", "fileCount": 43, "integrity": "sha512-7o38Yogx6krdoBf3jCAqnIN4oSQFx+fMa0I7dK1D+me9kBxx12D+/33wSb+fhOCtIxvYJ+4x4IMEhmhCKfAiOA==", "signatures": [{"sig": "MEUCIC8kq+G5La/N5Nljy8B1GaWdiG5QAGKRHwDPinamU3xkAiEA6LTaTF7sMZgqZ4VXFEr9I7Ht5Zh4vW4W8yV8ThkexyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 503192}, "engines": {"node": ">=6.0.0"}}, "6.9.14": {"name": "nodemailer", "version": "6.9.14", "devDependencies": {"c8": "10.1.2", "libqp": "2.1.0", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.5", "libbase64": "1.3.0", "smtp-server": "3.13.4", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.600.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "845fda981f9fd5ac264f4446af908a7c78027f75", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.14.tgz", "fileCount": 43, "integrity": "sha512-Dobp/ebDKBvz91sbtRKhcznLThrKxKt97GI2FAlAyy+fk19j73Uz3sBXolVtmcXjaorivqsbbbjDY+Jkt4/bQA==", "signatures": [{"sig": "MEYCIQCUVoA1QbkF50Iz2fMJZgIAY9agii+loOEBMwdIm1dwQgIhAJzcCIitsJFlTrsjzwXw1Crp7BJh5iamPcuIE6heDaTP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.14", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 504715}, "engines": {"node": ">=6.0.0"}}, "6.9.15": {"name": "nodemailer", "version": "6.9.15", "devDependencies": {"c8": "10.1.2", "libqp": "2.1.0", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.5", "libbase64": "1.3.0", "smtp-server": "3.13.4", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.600.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "57b79dc522be27e0e47ac16cc860aa0673e62e04", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.15.tgz", "fileCount": 43, "integrity": "sha512-AHf04ySLC6CIfuRtRiEYtGEXgRfa6INgWGluDhnxTZhHSKvrBu7lc1VVchQ0d8nPc4cFaZoPq8vkyNoZr0TpGQ==", "signatures": [{"sig": "MEYCIQCHY38nbkaGn43lMtVSd+Z4U7jOHHgnt8k2DHMkXTfx1wIhAJHYdPkAkNalskjJ2iagnLmLzpDBUrOkYRcLLE+x/ru7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.15", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 505709}, "engines": {"node": ">=6.0.0"}}, "6.9.16": {"name": "nodemailer", "version": "6.9.16", "devDependencies": {"c8": "10.1.2", "libqp": "2.1.0", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.5", "libbase64": "1.3.0", "smtp-server": "3.13.6", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.679.0", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "3ebdf6c6f477c571c0facb0727b33892635e0b8b", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.16.tgz", "fileCount": 43, "integrity": "sha512-psAuZdTIRN08HKVd/E8ObdV6NO7NTBY3KsC30F7M4H1OnmLCUNaS56FpYxyb26zWLSyYF9Ozch9KYHhHegsiOQ==", "signatures": [{"sig": "MEUCIBv/WJFqPM5BIPPCl7jTRy/daS5p9JVzILyvmDhKi1PVAiEA0AhoYC15hv6d1GTHsqb80K3I0aCkvVUsUcKEVbe643M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.9.16", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 506502}, "engines": {"node": ">=6.0.0"}}, "6.10.0": {"name": "nodemailer", "version": "6.10.0", "devDependencies": {"c8": "10.1.3", "libqp": "2.1.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.6", "libbase64": "1.3.0", "smtp-server": "3.13.6", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.731.1", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "1f24c9de94ad79c6206f66d132776b6503003912", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.10.0.tgz", "fileCount": 43, "integrity": "sha512-SQ3wZCExjeSatLE/HBaXS5vqUOQk6GtBdIIKxiFdmm01mOQZX/POJkO3SUX1wDiYcwUOJwT23scFSC9fY2H8IA==", "signatures": [{"sig": "MEQCIBTh1Np0E+2EEfbMsOV12N+svQaiveKvW3iUsdJC4vEHAiBwZH/r4faLPcsUBNov6Iev3xCBvTDe8GFsHwIsx7TJqw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.10.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 507555}, "engines": {"node": ">=6.0.0"}}, "6.10.1": {"name": "nodemailer", "version": "6.10.1", "devDependencies": {"c8": "10.1.3", "libqp": "2.1.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.6", "libbase64": "1.3.0", "smtp-server": "3.13.6", "proxy-test-server": "1.0.0", "@aws-sdk/client-ses": "3.731.1", "nodemailer-ntlm-auth": "1.0.4", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "cbc434c54238f83a51c07eabd04e2b3e832da623", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-6.10.1.tgz", "fileCount": 43, "integrity": "sha512-Z+iLaBGVaSjbIzQ4pX6XV41HrooLsQ10ZWPUehGmuantvzWoDVBnmsdUcOIDM1t+yPor5pDhVlDESgOMEGxhHA==", "signatures": [{"sig": "MEQCIECxew0xg1OHOYSx+wSFxXbXoRJ9ahP/Y3WtLWRw3r8qAiBBJS9Tu6E4GPaO/jhjTh20z6Pa4qm52KXGCu+uqJkIcA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@6.10.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 507785}, "engines": {"node": ">=6.0.0"}}, "7.0.0": {"name": "nodemailer", "version": "7.0.0", "devDependencies": {"c8": "10.1.3", "libqp": "2.1.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.6", "libbase64": "1.3.0", "smtp-server": "3.13.6", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.4", "@aws-sdk/client-sesv2": "3.800.0", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "3dda775f202d279e1abaa9422428ff3ff35a8830", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-7.0.0.tgz", "fileCount": 43, "integrity": "sha512-9QyEp969UmUWVDB2gVQvxHm1bqkyuC5YZIyX/ySJUpIVyXpuEFyBAuoFFs+G5/C2ZaUp9F5A65DPfxedfVHXXQ==", "signatures": [{"sig": "MEUCICkPHlzrkpeskJVYshTgigykieWF+ERzQWfzvWpbqYPTAiEAnXCZNr9blEs7b2ejP5Ex/N79QuOjJQuQKGXvN8BmFb0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 504824}, "engines": {"node": ">=6.0.0"}}, "7.0.1": {"name": "nodemailer", "version": "7.0.1", "devDependencies": {"c8": "10.1.3", "libqp": "2.1.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.6", "libbase64": "1.3.0", "smtp-server": "3.13.6", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.4", "@aws-sdk/client-sesv2": "3.800.0", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "dc0322fe1c14e7d48951a9f04da1afb7e3a2aa00", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-7.0.1.tgz", "fileCount": 43, "integrity": "sha512-olisl5fd1ri30mtoIgB4Tm+/28CamTCTRa+CET1zqoc14AWJsveogyRkyc66hqxJkXXE6VNi4Aot5mZRWFYkGw==", "signatures": [{"sig": "MEYCIQCvNMV42gg7HfEYG5hMBtbVyZrKcWrgopLyQeVK3nyehAIhAMHBsGP0KJlFS3jAYikuj9XQhERHC1eB3PcwNTYT+/2C", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@7.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 505215}, "engines": {"node": ">=6.0.0"}}, "7.0.2": {"name": "nodemailer", "version": "7.0.2", "devDependencies": {"c8": "10.1.3", "libqp": "2.1.1", "proxy": "1.0.2", "bunyan": "1.8.15", "eslint": "8.57.0", "libmime": "5.3.6", "libbase64": "1.3.0", "smtp-server": "3.13.6", "proxy-test-server": "1.0.0", "nodemailer-ntlm-auth": "1.0.4", "@aws-sdk/client-sesv2": "3.800.0", "eslint-config-prettier": "9.1.0", "eslint-config-nodemailer": "1.2.0"}, "dist": {"shasum": "29d2bf5a54898f1967174d8a050466e2ae8aa6dd", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-7.0.2.tgz", "fileCount": 43, "integrity": "sha512-SYsisPeLFYli5Q+BCGSyHT5CVvezPmQjHgINV9KVvVLV1aktuoD4E0Np9Q3ND9I481qIHzUQzVT+Tl/Tw7Ivdg==", "signatures": [{"sig": "MEUCIBt8nhh+UIEYyYjg6Y30tuZAAGNfEVCAjCwe6cGIePjzAiEAsVjh8ggF9tZUq+dypAlnaAdnf+3vbZfUmEsqFvlAQJk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@7.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 505689}, "engines": {"node": ">=6.0.0"}}, "7.0.3": {"name": "nodemailer", "version": "7.0.3", "devDependencies": {"@aws-sdk/client-sesv2": "3.804.0", "bunyan": "1.8.15", "c8": "10.1.3", "eslint": "8.57.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "9.1.0", "libbase64": "1.3.0", "libmime": "5.3.6", "libqp": "2.1.1", "nodemailer-ntlm-auth": "1.0.4", "proxy": "1.0.2", "proxy-test-server": "1.0.0", "smtp-server": "3.13.6"}, "dist": {"integrity": "sha512-Ajq6Sz1x7cIK3pN6KesGTah+1gnwMnx5gKl3piQlQQE/PwyJ4Mbc8is2psWYxK3RJTVeqsDaCv8ZzXLCDHMTZw==", "shasum": "c098ce79ac60c7c111a86949eed2f99bd00cdac3", "tarball": "https://registry.npmjs.org/nodemailer/-/nodemailer-7.0.3.tgz", "fileCount": 43, "unpackedSize": 506320, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/nodemailer@7.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFvPT0ENyzq0LHS+YXjR8S6bcV+xdQJ5K0IGEUxKPpSVAiEAvzlXjt3tszhm/GaTf53BCZrbrWxqz6rNs7+AThPiTtk="}]}, "engines": {"node": ">=6.0.0"}}}, "modified": "2025-05-08T07:42:29.243Z"}