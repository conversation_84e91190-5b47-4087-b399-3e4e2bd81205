{"_id": "tunnel-agent", "_rev": "25-4a813a8fae1713ef983b5a3f35df15b4", "name": "tunnel-agent", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "dist-tags": {"latest": "0.6.0"}, "versions": {"0.2.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.2.0", "repository": {"url": "https://github.com/mikeal/tunnel-agent"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "_id": "tunnel-agent@0.2.0", "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "6853c2afb1b2109e45629e492bde35f459ea69e8", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.2.0.tgz", "integrity": "sha512-PXy4q1PH88BK0pcGOEMXFAslyBuRWz1wxLfPXTlYFd41eyUgjOALaVGbWJN1ymjbnBzjWunVSKmrrMMh8oLaZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDSoShF0A8TKHtjqi8HJB38zpS2Yhip/7J5eBkd8akLAIgNsMGqj9ipWOebjvYjpcxKNOdz1/2ITKMzQ3XuVvAEUY="}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.3.0", "repository": {"url": "https://github.com/mikeal/tunnel-agent"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_id": "tunnel-agent@0.3.0", "dist": {"shasum": "ad681b68f5321ad2827c4cfb1b7d5df2cfe942ee", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.3.0.tgz", "integrity": "sha512-jlGqHGoKzyyjhwv/c9omAgohntThMcGtw8RV/RDLlkbbc08kni/akVxO62N8HaXMVbVsK1NCnpSK3N2xCt22ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDznbkxqBQdBUXheb114+KcPtDBVCpUWuqabVpne7EoxQIgTmuGKS45xGEVb5iM+EhO7Ix8JRRzR+p5CYWWFuZi6PQ="}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.4.0", "repository": {"url": "https://github.com/mikeal/tunnel-agent"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "homepage": "https://github.com/mikeal/tunnel-agent", "_id": "tunnel-agent@0.4.0", "dist": {"shasum": "b1184e312ffbcf70b3b4c78e8c219de7ebb1c550", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.0.tgz", "integrity": "sha512-BLqMqH/aN87Zq3ff8fPQfG9akaurt5eztqAUfVVhmGognmCc2V7sJHZpJg5ekX96HgDCkXAq4Bgr5SycljS/IA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErOhAfqH6ZJ0sEHjBFJyeWJ+VovF4EUJaiGdb8vlYf2AiBNb80jJNnXYrfoBvn0pLDIhmT09rt8kCcQHGD77dmn9w=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "directories": {}}, "0.4.1": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.4.1", "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "912a7a6d00e10ec76baf9c9369de280fa5badef3", "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "homepage": "https://github.com/mikeal/tunnel-agent#readme", "_id": "tunnel-agent@0.4.1", "scripts": {}, "_shasum": "bbeecff4d679ce753db9462761a88dfcec3c5ab3", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "bbeecff4d679ce753db9462761a88dfcec3c5ab3", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.1.tgz", "integrity": "sha512-vDxn4rpUNJyLx2ur4U/PEjbgms6VkBTFyPG4aIE6xPMSJ1yXXEjF1JIqBrh+1GfewVqQRUatY0f/ow+9GcLPJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkx0jx8UxzcN/3um2jE1h/sHmi9I5hUGEO63akhm1HYwIgTK+oZdn0Qhy42MPw7AYh25l3uSb7GWM1z7LTnJHoGkM="}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "0.4.2": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "license": "Apache-2.0", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.4.2", "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "main": "index.js", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "449634d1054949f9f145f4925985a8dea0f46b0f", "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "homepage": "https://github.com/mikeal/tunnel-agent#readme", "_id": "tunnel-agent@0.4.2", "scripts": {}, "_shasum": "1104e3f36ac87125c287270067d582d18133bfee", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.0.0", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "1104e3f36ac87125c287270067d582d18133bfee", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.2.tgz", "integrity": "sha512-uAcPtVev8tZ0JJ0ubNLMM2E2GRjHHhjqkaM6SDWJi/RaetrNwhe+UyprwnweWZnr83gVa9reqIi1xKLQSEfhAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6BkdJtJUgkGzf3AMY5nx8spPbRfniukpFTrefYxm/ggIhAK1rHM8V2gFzHwn67W9XQOkoEsCUYJeBM4f1Clz9H4u7"}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "directories": {}}, "0.4.3": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "license": "Apache-2.0", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.4.3", "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "main": "index.js", "files": ["index.js"], "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "e72d830f5ed388a2a71d37ce062c38e3fb34bdde", "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "homepage": "https://github.com/mikeal/tunnel-agent#readme", "_id": "tunnel-agent@0.4.3", "scripts": {}, "_shasum": "6373db76909fe570e08d73583365ed828a74eeeb", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "dist": {"shasum": "6373db76909fe570e08d73583365ed828a74eeeb", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz", "integrity": "sha512-e0IoVDWx8SDHc/hwFTqJDQ7CCDTEeGhmcT9jkWJjoGQSpgBz20nAMr80E3Tpk7PatJ1b37DQDgJR3CNSzcMOZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbuXKxCmv0n2RZOvNR4mkFCOM3kR8Abi8JtlHBgebWJAIgC1V4xvYAZygZBSCjjnXodzu+TOQa0Q+cVgEHdb6pevk="}]}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/tunnel-agent-0.4.3.tgz_1462396470295_0.23639482469297945"}, "directories": {}}, "0.5.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "license": "Apache-2.0", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.5.0", "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "main": "index.js", "files": ["index.js"], "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "5fe622150e36621cf5986b2f9b3289aab93da1d7", "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "homepage": "https://github.com/mikeal/tunnel-agent#readme", "_id": "tunnel-agent@0.5.0", "scripts": {}, "_shasum": "7ae1bd34ae766309556b7ef34fd52cf874b14c7d", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "7ae1bd34ae766309556b7ef34fd52cf874b14c7d", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.5.0.tgz", "integrity": "sha512-uV5rOpCsCkqGN9S9Ro31ZkN9iIejB+cyrV2IXlhc/jNHz5i6n4F80m0s4r8y7Yq3V78Mg3Tv6azjlOuNvvi2EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPlbfKXs5Q7QO88bK+pTuiC+QMWkxLxOPTrBOiJ9PqWAIhAOvXRIRHaaaj132qEL0EoiPWNzBIdmg+dKXakNPUZ7hb"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tunnel-agent-0.5.0.tgz_1488655969566_0.1062539997510612"}, "directories": {}}, "0.6.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "name": "tunnel-agent", "license": "Apache-2.0", "description": "HTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.", "version": "0.6.0", "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "main": "index.js", "files": ["index.js"], "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "67df643033258e7cb1388f648ee5f141cd66101b", "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "homepage": "https://github.com/mikeal/tunnel-agent#readme", "_id": "tunnel-agent@0.6.0", "scripts": {}, "_shasum": "27a5dea06b36b04a0a9966774b290868f0fc40fd", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "27a5dea06b36b04a0a9966774b290868f0fc40fd", "tarball": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9ATlzuhu2hwlePNPqrWGLoZT46jzdYng7Z1ngalhIjwIgOLGKgCu6hpmsm3mWAufGKePcGSm1jMSs8OdHsgLV/d4="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/tunnel-agent-0.6.0.tgz_1488673799706_0.16846991260536015"}, "directories": {}}}, "readme": "tunnel-agent\n============\n\nHTTP proxy tunneling agent. Formerly part of mikeal/request, now a standalone module.\n", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "time": {"modified": "2022-06-27T23:20:40.165Z", "created": "2013-03-01T20:43:54.074Z", "0.2.0": "2013-03-01T20:43:54.791Z", "0.3.0": "2013-04-22T05:11:34.229Z", "0.4.0": "2014-02-04T19:33:08.224Z", "0.4.1": "2015-07-02T16:27:22.528Z", "0.4.2": "2015-12-08T14:31:54.613Z", "0.4.3": "2016-05-04T21:14:31.757Z", "0.5.0": "2017-03-04T19:32:51.786Z", "0.6.0": "2017-03-05T00:30:01.500Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com"}, "repository": {"url": "git+https://github.com/mikeal/tunnel-agent.git"}, "readmeFilename": "README.md", "users": {"fgribreau": true, "program247365": true, "wfcookie": true, "mojaray2k": true, "manikantag": true, "ganeshkbhat": true, "voidlesity": true}, "homepage": "https://github.com/mikeal/tunnel-agent#readme", "bugs": {"url": "https://github.com/mikeal/tunnel-agent/issues"}, "license": "Apache-2.0"}