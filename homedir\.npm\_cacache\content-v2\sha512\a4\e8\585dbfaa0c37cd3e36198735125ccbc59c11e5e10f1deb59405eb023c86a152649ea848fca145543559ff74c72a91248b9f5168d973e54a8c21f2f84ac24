{"name": "ajv-formats", "dist-tags": {"latest": "3.0.1", "beta": "3.0.0-rc.0"}, "versions": {"1.0.0": {"name": "ajv-formats", "version": "1.0.0", "dependencies": {"isemail": "^3.2.0", "schemes": "^1.1.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-aUO0+DSgPeeqaoJw7P6hFB4+53qAq8pz7re71CHlmyzS/HB3STNeCwPaQlzL5VslMuogSYRzGE0lxJleMFSPug==", "shasum": "b2fd022d6ef248c0f5a7bc64ddcb03d06ee9fd66", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.0.0.tgz", "fileCount": 9, "unpackedSize": 7204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHgEQCRA9TVsSAnZWagAAsrIP+wcZpMFXk3CyKAxp3Vaa\nbpVhNevU/+DbJfgnI8FH0F7ZFRHzyWv1GWnvSnno6vGBTyClxUAmFrXtJtZt\nkcl+Uhyp+E/O4J9lxXXM7HWIsoRUERYzOmln5dYF1YVFsFDxxDr5+mm0jYw2\nQZE+FbSBgxna86PBuqFb1CGuu1/IW7U3Z3E9EzBJIqPbamE4WeUxyYoadvL6\nhRGCu2nVXwN1Jo6LUhw/ZnWGEeXdPqFE/02YefnVpLkDKrU4vdL2OsZ7Kick\nwG7gqIq/iLjwosLE9kZtJh0NWsjQzbsXV149RreaR037V+VlJ2tZWjM3QykZ\n/ya0iM8O4I5ZqYzUwDPoMDaY2Slysts9mik8GOAicrrZw4JNSQ6+zfOFZ8sA\nCltagLe56sCJIS/RiU07yFEK4vOdk0TmiyrpA4oGaSSC3RjzdJ0VFqY6fp/L\n2av9pTras8RrIq+9WJkg/QryuORB7bR7kblUr9Y1lE1bbcnbmQrgqLbCRdkZ\n/BGr0RIR9G1bWtR9jlwUD4uRbGvDKP9m287LFsSIAxbG+FcvbRLC+1glQieV\nofGZYFn+KAonzHCyTsofnBvHBL+TkFgathbgaVyiHtEChN1Az4HwP9Xg45vR\nLdjuR8tULaeheAI7v3xLhZSfZUmTY//MMNB14NNhgtBK0wIlkbVmoUtRFXXD\nLn+P\r\n=TMdV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBvAOMW63eAKHEz8EGL0XEXIYDyW024urLTUE9INWz/kAiEAkqnMlSo1fexNdtrg0KNLChQWhA5ZmAJkAsWJsIcUXCE="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.0.1": {"name": "ajv-formats", "version": "1.0.1", "dependencies": {"isemail": "^3.2.0", "schemes": "^1.1.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-anO5SPnSWiOpbifd/92bY7wK27Oqq9bmtR+jon3kDA8HcR1YIhor3SYsT4Hnag39vYRPYUXApB4VHLVmBM2+Fg==", "shasum": "467423ee815d78397c8868eb3c0341426f97f27c", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.0.1.tgz", "fileCount": 9, "unpackedSize": 7174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHgRDCRA9TVsSAnZWagAAXrQQAJcuYlgtTfxB9y2mAgfk\nIT2Wrd2pdU0Df8Pm/R1GzIgtBLfbVNiJvrq417h+0RM2um5cFRXbjl6y8ef6\nq+y9o1vz0R0vGvFv2NcCPkyfBb9eAkh2VWcpVn/I9s5u8lYcxlXrKKNMzolN\n+cj8clewsr4FYX0Twpj8uDzABlGAjfc5UzYyI5DipF/l1BCqZV85yv17kTxp\nQeztw3EFiV1KjvEw0DKDEYLKTkyV2GdEJMgR7xRC/BxgWJuK37DjHVZB7y+a\nd4sYdpOr6c7yjj/K9yulNwb3eTO/mtF12ZEVv0wka4YkAWFmPUHoLcTwLSde\ne2zFRSaaBfeNp8gt0k2EUH7vN74Fhbq1Np+LnLvzZe9d3c9obx513gr4/5e4\nIIM1MajMyCl3ixz/8ceP2eg3fqcGnGXBVKkN3+rEKdpBxb7M2YabssGus0r9\nbMzuBfsKKGLzAHROKJNofiJrLYeqWzjJnxFWbqYf1edUnxLvhmDZQmouILwZ\neLkzGjgBQaUMUtThQsHP+/Y1vmL8rQaWhYdJWQQvb+vELzNRtS6OmFN+31gu\nvHAUwaxmWcDwemycPjnwVR2Wwo+yWDS96F7k1gQQIzlCwEQjxMsSxz2TFCJJ\nbSIUGe0Uq5RQdpglZCoznLzR8xTrLY5F2NXj5JEIhBEdj5xidz+aVAVNrRBN\nSvzY\r\n=xj9P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzDlMCyyQhyaKoHItCLeV2K1lWdc7fsjTwaf9hox4PdAIhANhUuAAolqSRtlAOVT8Lnq4iw5WV5pqfMqKLNHdvt7hs"}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.2.0": {"name": "ajv-formats", "version": "1.2.0", "dependencies": {"isemail": "^3.2.0", "punycode": "^2.1.1", "schemes": "^1.1.1", "tldjs": "^2.3.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-LPGWNVSU9Kzgbeq6J9m9DuiACdq4Y5k3IRmNI17Dss2VO7YJwaWvL5aF9ibs6JV4phFV8hvREgQzsvLboNlnJA==", "shasum": "a66f57e22d56348a54504d062f15db8ad8afaea9", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.2.0.tgz", "fileCount": 12, "unpackedSize": 14051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKJuFCRA9TVsSAnZWagAAipgP/ifgBQ5K2DnubD5R+FEG\ntHPscNYQe5JNvBWstLago+yQztqMAkIh3kCvJZrXbto8hsLEZKXMN46GUQCC\nyW6yShUkgBRC7H1qSQjVHkoPi8D7P3uuDF7kyNkTx239qZJ5+3qaNPyc32CM\n7mMlsDi0IbPw8D2+w6vMSBx8bm45cTALQg/RsgI/Avng1hKyk2WZ7rOWob7O\nCD2CG3ue7dvBg0UiPzl8gDetFPBjOLqJ1zbWkNYUxL+WzmPcJIfRzA6J4g4W\n6J5YRXhU8ujfkhNWxzajxDNRuNU7+VDvXPAddWlrG2tkfFipuFl6NvrikjUr\nIauGDGOQTecSvyAp158MCcDtNcQ5FuOazdov8s1ioXE512I8AyzR46mNX3cT\n/NvG1npx09RnWp2GpV9/YTmRXE3PCwrD+PCd0MEDeZeTRcA4P99dVUPxJdyF\nUaHe3e1jncEm/IYMZux3e6XzoSzlVzX7YX1is8gas6gUACs0/dizPtrFCNkO\neoJdeBMbHV+7DKAbJID/J8vaSZzuTAeRYQYKkWnS+NBdJ4Mxw4iJEj/89SPT\ncmU7gxv9rCTKgPFpsuVTgV2WwQtYGY16FWUzbQOZhQgy9EjNop5N+VU4wW3m\nMjSYdZDmGln49PhOebaDJ2qO9BWis2XTs+B3UmO7Vex0l1ub/m3RCPSTxSUs\nZVkI\r\n=oOmA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7W4EeSrQspJQm7XW7K8SodGxv0sh7kO1c/3KV6wWMxAiAEd8+24lQ+dot+wB9lI0gJf20lKGUoF4kbY/KiJDj7XQ=="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.3.0": {"name": "ajv-formats", "version": "1.3.0", "dependencies": {"isemail": "^3.2.0", "punycode": "^2.1.1", "schemes": "^1.1.1", "tldjs": "^2.3.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0", "prettier": "^1.19.1"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-1ODaj+8w1xy9fWKhUxj9vXbQmQ9vv3U0H9wJYVnCabiVDsoFTDBq3DRDWBMYXYj689B2UxUMetwKeKjS4mEaaw==", "shasum": "43ff1587979ccd235a3f569bef6db2a1682281c2", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.3.0.tgz", "fileCount": 13, "unpackedSize": 13890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKKLWCRA9TVsSAnZWagAAk7gQAIU+w8Nl5Sfyy7zUV+oI\nAlhdmsrFmwAnYBZ2snv0iLh+An6l9kp18kzAHShFRsZ9+lE0l9QxwnZXTjbD\nDHX1IrzcthkqPnamQNreOEX20q0haBHUcH+frBzMVFWXfBVn2bYpXVYsEH3v\ny0Ssbp7r8um4xmDwgT4JQrfpIrb3OS/uI16tDH5i8zZ8mmfrQ6xHkFeBFCWh\n7E83ZGa6mNocgKrJ9M/rQ7ZYu2NhPfe37AIXUhYBBVI7obDbp1N8DB5sjDDz\nrxbrk1K6mbJkZDvqIRYRB3z1WoIqq0tozIiVU+p4ZLP3JP6Rgzv5e8ast9vB\neAMcOqIPmR54J0Yfqw+qlv5wLY75pEl4r6A2DUxsMzOmIYT/e5XPu1G4H2UH\nwFA1fK0FW9FuTros0Qfh5gUgYaEti3ELnEN1ixJ0QMTgpj5fgjRQwHm/Jq11\nfMwgr17c5ZDRdcIojRoSvNXXm0Lqovy7zNg2xDc2To6Fl+3beX9mR/Mrx4se\nrmCVGwHG/vRfSCiMuG6WDf0PEjMeCpQpdfgPIyqV2h+ofBGhP8gRbIWVOz2b\nLuDyKUAjpHlhYo+9X3Rsmld5W+kP7hRYkSk5MrYy8CYlUcREQ6o0DCt2Esmt\npUOhTNncx4bDgIZbVWjfSGaG5BfLV9EtnVLsZyxcT7AF9DnFVKNmNzVODAn8\nSlbk\r\n=/ZS3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBYWKMa8erycedJxrYgiE62scZchxiN8QL9geND2bQ+5AiAgBE8OinoFyMl8PYK/0g4/Bo/m3dZgLoK/mgzwWeV9KA=="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.3.1": {"name": "ajv-formats", "version": "1.3.1", "dependencies": {"isemail": "^3.2.0", "punycode": "^2.1.1", "schemes": "^1.1.1", "tldjs": "^2.3.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0", "prettier": "^1.19.1"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-7YsjGQym+Z6fSxprhCjn4dL7ca8ZWAypAkxtLX3NzcE281xnOeN+HlGdoS8yn9iOIcG3D5SI31eKGX3HaB+UrQ==", "shasum": "00c36a51ae6d0d81944e95216ac8d7ef700bb6e1", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.3.1.tgz", "fileCount": 13, "unpackedSize": 16040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKKkxCRA9TVsSAnZWagAAI2MP/2ut6JaBXzkpcAiz7f21\nRoYkfQm6+tILqBFqKEgkE7p8PqWKHZpogzzfy4Ks3MFcsY84hmVOnv9DL86U\n+FnDe4HSVKRK0d6dGod7sJA2baebzlz7dEPPQh/Qi+IG3jOgCrGflZXZsQko\nPNMbk+2T7NmYh5drgynftPtNqRMaQA6WwjnEnfB4ywrQDvnSY7yBKR3XNO+q\nQ7AYCWAlBlRxRsYOw1ot2ugRscgibHgFd0t9HbVUU7tRSCCJC+9mA/DANnoc\n4b5+lS9nOIoNiUH+V/LmgGNHv2g7pKlWZQ3HDk0dAakF/U3CBvU6Zte6h8os\nnQp8YHE837tg67Q+1e1U6oRoZIp31phwSbTkeTgDRJeXlYAKDc3fUlJx9qG3\nzd8vpnAsMy05T8YxfLZ2ejNhC0COa7PV6RHNVScoANA/v7p5WyQyAZeACHzs\nSPPckye3WpWUVdiZHzMUQTwQGgDGq1pr05vVVMD9FVuy3zsuQx6TaDvq4YOV\n1Vt16HtAUb1EKGkKtq029bKHPNh7KYhycBBqGinVGhrBCHO2XR5e1GQG55sk\n99DBnJ44qatFIFKTrkvcByWaqrC9wIt1E+FolQWIQ6SLyQkH3y5ftbzlICYb\nt3Wd0yGrlswfrZjkeycolk13DkKLZ2iaPAvvjM57SZV2cJkDIxBr+6VzcGka\nFYxI\r\n=L0ET\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICcYftT89m68ge2KyqOM8V7yIY057szg1stFgXSiUHrXAiEA/u+SkOTgbWTT98W8HMIxyvc9e2lrlSDzC0CBW8mlVHE="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.3.2": {"name": "ajv-formats", "version": "1.3.2", "dependencies": {"isemail": "^3.2.0", "punycode": "^2.1.1", "schemes": "^1.1.1", "tldjs": "^2.3.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0", "prettier": "^1.19.1"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-pO+/5q3GNaU1p64F11bK4tmb7Y3tsnCOWciZBxCaXafdute4qG941vd3fqpZrg2DoPDoB/j5sQSrsPVVZsKOIA==", "shasum": "89c1fcad45a94a47e3a27eb2e4cbabffd8e03a07", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.3.2.tgz", "fileCount": 13, "unpackedSize": 15975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKKohCRA9TVsSAnZWagAAikQP/0GtbgLCdGqSX7bQi6qA\n049pl7H1NYI5Omg4i5Q57e4510Rrd+3eFtg/RSHrluTW7tOk/yyVztpVjaG+\ntfGMC/J1quOc8g1OfcbluTOwciYJmqWRae53YsEGqQMo9kJqeEgbXEvCgxRI\nuRrJtI+XaA/AZ/0QWUl0Hq5eVwZwIl6d4Prp374u1D8RW0oMZ4aq3XgJ5SNx\neNUeQdZPgkRBtU60ib9qdFpSR0OLAJ3f5v/lkk42/7rU1P5tFXrvzFdlQxl8\nGmi3SaKLkbuEcNfVkxPcpDODserMthGSuiS+NhpndK3RDwH4SrtYgxOmLRo7\nluFWmakhjJ0Gai2lVpoMx1Z3Lk/QSTuhtcUK/ODDmcnnSTbCTuOXshS+JBVx\nWYLQ5T3F5Giwm3SJcEKWTqzNqKAVit2l1YB26rlVK0Db+D2UdWZy+OnDuZNH\n7G+fXxXNywsZOclMYmhoefIK8sKQ2oS/sIEOSXHz92uHQuh7pY6Xqqr+Datt\n5Pp3XSoPB303FbrN30GPlZWa1au8UjrCbpoRXIlsGluULvRApazoUiMLaQFJ\nTOkDe25/PkgJhInOt4ctUSekDA89WLTVNoSDk6pOGPCzl2kZvRkJVmxdo9Uy\nAuA7DsVoUiBSoDDvmn6EPjjNXU4+zQonCrBm1zUwmItVHXBK4JkqUQ+03Zpg\n+mBZ\r\n=Iu9t\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjxa2pEenr/6DwO/enCT+icjoG2v9qiOMtaHAO/XfRRAiAD63XlzniHW9dv2Ctdc6tO0429ooTWPWhbl9vXuvwNvQ=="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.4.0": {"name": "ajv-formats", "version": "1.4.0", "dependencies": {"isemail": "^3.2.0", "punycode": "^2.1.1", "schemes": "^1.1.1", "tldjs": "^2.3.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0", "prettier": "^1.19.1"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-L7qeHMpU5X9INXrOdve9Y2+wpQb0SeEC5Uqn+oZcUd12dvwgnHmGLKnqYrLsI593hu+aBCkt6+iRXUcdE8OStA==", "shasum": "52bbcbc196973c063fe8cc5a31f2bc5e21baed5e", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.4.0.tgz", "fileCount": 13, "unpackedSize": 17140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKjbgCRA9TVsSAnZWagAA320P/jfGkRsVRl+S9Vq6Nu92\nyVea0mMwCIRWfyhWpjaOD9X5hpuEBRofxgTaf1SzEJgfpb2bt7zp208jt2w/\nZHgRlU/Tr8/pz6POdSFmEXTXnBG4t1vEBumhE/uw2M7Ok1h8cO5hFyETOME0\nA2V+jgSzMP002jtN6ES/TPuQRlwYh7/y1oTmbnWkOJlXEjL3y1jOZrz2R5MF\n6sU/h4QQLO/YGMZqprcoehh1Xd7ote31UjVXOJCMA7LOu/1MEovUEOhbdTZr\nLU7qrV22vcPy57XCEdpuxgZo+/2yRLEqi3vbksHpqU4g3b/UqYeuOPhqyAqP\notub85o1baEbmTRLObp1O3yfiSXVgN5374thLy+dwAxaAK5ckYFQx2pRelNf\n4PfaaoCCq/fiZ2KikcLmdTcLWV4bZ2xUEHUJ99Khd4YR4YfqAYHLGpj0kmqG\nBbY/olnkUyrHMqr3c0HMjMr07C+vku4SVA0k822o5snLcc1x5r8XbSc3REoU\nwfB5GxvmRBuYppJorQbswfXusr41PBo+2Rrw37vDIbrGtMOhk8U/21qIE+8r\n2M2nDlPP4u9NFUby9XsFzYDL95r56HkmTw3jEUq8eKybFNn0CbJZION/zWJp\nF7UJvkLRGxvg1sEGAlyTVBmSDQWueDp2RtvuDhZd2EIuKsPcr2dlObd74C2m\nKoHu\r\n=ApFa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBzVM7XRmYcx3xu5c2kODb7V3YhkrGe1M1xMoBkHooWsAiEA2cWWPeSCoun+6hZ9fatSCfFrSDLpWjMehZHkMk0Xyjw="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "1.4.1": {"name": "ajv-formats", "version": "1.4.1", "dependencies": {"isemail": "^3.2.0", "punycode": "^2.1.1", "schemes": "^1.1.1", "uri-js": "^4.2.2"}, "devDependencies": {"ajv": "^6.10.2", "mocha": "^7.0.0", "prettier": "^1.19.1"}, "peerDependencies": {"ajv": "*"}, "dist": {"integrity": "sha512-Piccuow5A/MfAfyp1vxLxRNyQHpKtkpMYIO9UNgnhu2ZhxC21n1g56VDUwo6CVpQp66/1WNmKpSYwt1WOKsAng==", "shasum": "b6ff5906903a98f6ecc3910e209904a4f9f4830f", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.4.1.tgz", "fileCount": 13, "unpackedSize": 18535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKkxuCRA9TVsSAnZWagAAMRsP/AmJRKLeM5drf/Ty9pbX\n+RZkdGZmsSfKJH11iFheAJEoJAXbdaPFGp7lYLwfGOA4F7c93T3GUEg+kQnK\ngA8KjG6IrCWaPm6g8M64aDGw1NYfTh7v539tYxcwZRmWrX9NFbWpnR+MuJwA\nHXOeTqhCe51e4BerHcoyBEY5uHftBk25Enuu0Kga1MNnC1zmOoRk79goKXK+\ncZTEMCgEeECqn/aiwrvyXR+3WkE8mSbf+7q7cZOA88+lWeFRmcJhOzheHJgX\nSLWohZ8ceI53HjU1GNaDfHzI8xi+8JG3rCscWqyIYaNwWOnWVoY76fcr0Ei0\neDgavS3zcw59TolZ/PT6iiXA0CXHNyay75UdC4cwECK6aAmOnvKM8BFYxDfA\n6pBM7ql6nvCE0Ia0x/SgHhiXulP+ZbMwe2w6A79ChPddxGLqTOfdFGro34/3\nY7KwLnz0xbXf6N17OyozfXqxXjSB11IkK9nP3m+hoDeIkJ4DjszNv+ejikdk\ns0/655W3SZz9vY3wkeR6uoAPc3K1H2mkX7YSjyjDqRY5M0iR+dJBcVzXXe1n\nVVlYkeChSv8qMMkhCXE0MEASdBi0LPcpluaGAdY0qAzVQ188RpdrAUJwcL6F\nGCWZ+CgkAe7OG8b9UHt0iLic2I7ag/fsl2qMi5XuOvtRTmzdpoXT2syFfc0N\nduR+\r\n=VZR7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBM5h6sJtWSUcz7ryPRPLE7VgvBtvoq4y7bIoMLikdHfAiEA9FuXNAt6n1fF9lpRhCxsQ762oXStPLBqbzRFrJgvKkE="}]}, "deprecated": "Package has been renamed 'ajv-formats-draft2019'. Please update your package.json accordingly."}, "0.0.1": {"name": "ajv-formats", "version": "0.0.1", "dist": {"integrity": "sha512-vy58F+e3wAZHrtb02+TWBokpc3jZeucn5xWxOGaD7FXbNktlT7Fk+L4Q7lHR312u/P0HWLRxO4ETVR0mn7AHuQ==", "shasum": "2702584c8e84d9db6ea69ff669910e99088e87f0", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.0.1.tgz", "fileCount": 3, "unpackedSize": 1734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFXCrCRA9TVsSAnZWagAALrEP/03L91y55GEI671BBqs8\nutpHGSIQrZWCdiLXu42/zVn4+avxBbnR61GXFLiv8Z3e7fM0ONvT4g3VEMVS\n7e7+LUMR+yX1rbbKE9OwzEo9UGzvIcO7ldMB4o84rODNfPGg902p52pXw9yx\n48dpnjTFCwAA5fSJuFphXv7efhzzEt6QRYfkIAoGrPk1ovVZw4AKZkeJJXv3\nOQ5D9R+S2/UzAQge4hqSbd52uWRD2hQWTXH/N7jIoQbmFAxfXJTD2EDcFRmu\nFiwHfFrQ48PxbEVbAFe+HqHlZVjwQEQdeqieBowOgvEJ8vY3tVHCREWSLciV\nfLls+6L5TGrgLW5REouP5/txvlDQ3GptHQwOm+/XvYPXDDOgvXq2f24SxxjO\ntFs2/mlfHLfkIm9CVHY5gfV2CRDZ/EuVu8Z1w0HhcHSO4kgh2U3KhSSFr1bb\nIdzV8Ed/67XaG6d27ChUBTDQV4DlbMpaKiPuUe3UEgpv3H6No5djyfn2l5TY\nQADJ6C1nCj7x9zH9maBIICAX386aYHuQu3e5KVaDYSToXNVYyls7bSR7UwR6\nhGNnqtNwFJ7NHLUQs79voQ6kFbXTOXBwC7G41FRDJ20PzSnTcuhvsUMptlik\nnh1ewNuO/yvfHkv+SHKUHReQbGnFsY6putVD1IGRIKppYWRRaUAHij8Q837K\nGnaO\r\n=jdQg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHDGKBRs8Gc3LPhXXrEnbkTVd/4z7STYR+47Cq9aZoedAiAgcqnFXfmpyI//qcR6zr/NA71Xz3dwill03xRCqGzwdQ=="}]}}, "0.1.0": {"name": "ajv-formats", "version": "0.1.0", "devDependencies": {"@ajv-validator/config": "^0.1.0", "@types/jest": "^26.0.5", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^6.12.3", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-v7soSyYgY3YJ6ddA06BSk6kMYFo34f52YikeFt0ErX+m2lwckVDpkq4i8OeZdmcduRKENaxAiACjwzH0VFM73w==", "shasum": "7c58fca1e6ab8d3c5e070d49543ff5cd7f5eb790", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.1.0.tgz", "fileCount": 289, "unpackedSize": 1023857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfF06eCRA9TVsSAnZWagAAjpMP/RNtOLdgB07Nqr4gY0ei\nnJ9NjfiZjcwBoQE8L3xgvX91a3u1hoeU/55MeK6qYz8yyuxO83j6bFWPgZe7\niuAGaoz58ir5LyEuPnXgYEmFbB0xkddVW02ec6nWqRg+nV5odYTK2aruG7mm\nZbF0JR/cwAe7Y5EbA+1WWdgtPDO5NImYE/yvuzIRXCJWg57rZNb2Hawqg6kz\nngTtzPw4iTQkiiOXe41nFZ+tvCnSxPf0/Z0LtC7MGXTx3inQ9e3ZPj7lK4sn\n9aXO+4LQwirWnLHDL3TurwG+nbEhZvzSxaUD1X5s2Llcz0xzTlGb/yASjyH3\nuAKyIWIHQOo73d8d5onOZcbycknMrwhrPwQcc9dfYFbZtfbRFv24fFC28cta\nj8Mf8/Opcull2oYINPjwway0eE3YZHFzbDVcVf8jyI7hHfo8KlqUAYSWZf/h\nIwHw8ga8PDAm2LqkKte+yVGeDP5a/jhcMsj9sYp7qf5pnKGV7J5SbQo+dOQ0\nr6r1TdKOe5aZUSw9bE/CLwiTaAfbpRzX2w8CQGzb63++rqV9jEkHaiX36CBj\n6PiBLIg56s5ZdWaK4BQI0GKxgxdx1tCDYASe3FrliXkBv4lp2A1hpDtWZR7b\nfOk6yKtYkP8lNHljdGUfE5H8C/n7VEVMISSBUMVJZwnEsN2rKhVP9Zs1UEy7\n8Yb3\r\n=6Eb6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmGyK8Mqfz7nhN2nnt7e3n2MHff5LdptzVdq249zVMQAiA6F/kwZSs8K4RLsduP8zTq0UaDMHfXX/hiz69UjPpX2Q=="}]}}, "0.2.0": {"name": "ajv-formats", "version": "0.2.0", "devDependencies": {"@ajv-validator/config": "^0.1.0", "@types/jest": "^26.0.5", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^6.12.3", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-B9tuHMvVNaVwZKOvg3Ww93GJs6aX79gVTpKMsWV7I9Xg7ZwjwC2ohZIhR02U4eGzKQYFRQQBUPHMLvpj6W5nzQ==", "shasum": "fc559f9e50d4f837610bccd5dbb1f1e90c02ddb7", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.2.0.tgz", "fileCount": 290, "unpackedSize": 1024257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGCFPCRA9TVsSAnZWagAA69kP/0gN/rjddSypMbcbi07F\njGcA2kimgbLxi/VGdc+5Sobaoo3xH9HCghpznMw5Iw4KwC8GfnE+T++UIe8d\nF58Lma12bHlrqyJ1ZRWCxK8uEaainIvyvlfVekItEZl3pR3wEyc5ejjhp8SO\nO9Oj3571X4PtUonbmM3MRSl23VjacrtQ5+B4daFnBZoU+J/j1R0A4Gbfs7Xr\n+JPFqqUrQuxhtmn1Bkw8UXf45I5cx8LSIBOFuM3kyl+j+QhvTyKW4QFWk9/B\naDowFeNiUO+tN6umZhvTSJWhZLbTfKexaRfDH1XKdPlmQw/GkdKdZ3W6+bHw\npfuxfQ6C8fGYMBiECM5ACnDnBd+U07sXdhJx+R8DjFdEs7/B+HXvhUyyQafQ\nXLo0iClPJbbGyloTM9LANt1aLznhEjmiKqO9zX1udQGv0cwFQT/qiiAwRypY\nuy2LGuZisShiOPXOYykwxNEQaFy3jik/JT8AkTawOrHRlHLtSCgIdvsQZrdg\noqq2KGYdlZeePTSlmqZE0YOLuWVshu9S8cN0X5KuS1o3O4e4oBOupVkmV4G6\nW1rDa803crbib47nL9aV3N3FED2LoKN5q5ZgYqFVIReNVUSpTson4T0ceA8u\no6T2J6LQ0yJZ+yX5N30FsbIiIDghqKCCIWXXC9eGj8ZOlLyu2eAHa1rbFR5y\na8ws\r\n=Zgqf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6IFXoBRB44sBi55vmipVUp8jVPZC3A0KpbswwE3PZ9AiATq5JHKcZqh/dP8Pd5hXJpK6FLXjI+knu9VUOfx9X6rA=="}]}}, "0.3.0": {"name": "ajv-formats", "version": "0.3.0", "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-alpha.1", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "peerDependencies": {"ajv": "^7.0.0-alpha.1"}, "dist": {"integrity": "sha512-u1DU7FUC3QgtyEqmb5A3VHn0Zsif5NPFiQvMGa7XsaWVWYUYLs/IUwG+piTl5I0E8GWCi0Q4NjlF1E4bXVGjig==", "shasum": "9b17966ed19c2f4bb16e16b570d93802f8c1ba38", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.3.0.tgz", "fileCount": 292, "unpackedSize": 1041366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYgoaCRA9TVsSAnZWagAAcnUQAJVcjMdEvTRJBaIh18ud\nm0N806MYICuf2fcl3D8ASg946kXlgO666a3/KaqGqeOEYy+8NLYqNRBm1lGX\nnuDyoc0iKpEN+32obQACz3yltBYCQQiElHdfKBH3x71cUEoki+Ft2LM0Krh9\nDwHegs7Sfnt7jir7GBlNizb1u9Q5BHUSLn+loIpSiKQuAH+6BEbiVlgjqD6N\n0TnCnugIe/Jt8FtTWS7anRCy6Ac4WXJ84o4OFMHi2+BWujj3le3aA5VHwI96\nWg8W3Pj/Pl5VJmM9dNpOkjT8J6t4jLHLWg5aQ6SgNkEJXkkHRyInLBcyKoYN\n5k2cYhLc5iyoWKXeI5pl0bHPKY5CmkXfehBmrCJUJAxzFYgp+4O3nAvnQCz3\naTWd/qlm8ICMUWPcL4w3axtvGlackaNae2MSvbnEb6BizKE4B7dmvqbwpyUG\nnmWfmtOqLb3T2l9Q7fLiw6g4RAimcOQSJmiP9d8csARgeCV3dWxmde03U/dT\nfwDIp8rbkFs2fpXd250RIIHi+y0dJxUe/Kg5AhcECPiellXdT/78PBpaamef\notpneKrioBZpTA3VJu8V2m+jnsuZ/UXhwVYdpf8tKKBeDP33H8fXtxy6ojwT\nw4wxXKjLFqusK6rfGN5HqMgHoLLV9Lo0m3PBxERGjJ3H2Vv3uI1/YEPkhzeZ\nup3M\r\n=QDnp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5icWG4oSSglW214g4ENsxdI1ke5NIDArnMt6iqFDRLAiAvxEHKtptlErFVjQUcx0E7c+NQDjQ/wxdC1BFDfIMh7A=="}]}}, "0.3.1": {"name": "ajv-formats", "version": "0.3.1", "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-alpha.1", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "peerDependencies": {"ajv": "^7.0.0-alpha.1"}, "dist": {"integrity": "sha512-1QIkvjw+5kwuinsrlh0I3qKLkwNijaer5e/jytgj1CPzZdESUg6k0sTy8ccxDzroSfdTrW4FglhCttq8WLO+cg==", "shasum": "3f640acf52aae675541a6b1f7d47a09346d18370", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.3.1.tgz", "fileCount": 292, "unpackedSize": 1041366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYhmjCRA9TVsSAnZWagAAPUYP/10mk+/8fLU3DJFzVXrA\nj6F28SX03eKltuFoOdaEzpHZwU8Syn4NZyeTTslHmNtqMhxIb+9ZloQiFyxj\nKxh6CZq1KCYoooalFQXUT+ygxB8v4uDIbOeFrOLAShW6Qkm1HztBA+BFbQa6\nh1Y9ELRwBkfvlnRug+bPfkb0Mtvhwl+p9kJ6bTvx1s3nqBWX+qjhM7eqO8VQ\ncz9EMDg2D0myN2/rj9K7devX0u5+KKit4/IaLmZ3cXyapI8evCKaRELiZM6e\nQcRiiZwqazh4fyAGePcgGjvXc2+53YPE+eGV+f/PgfcnF8zbyOosM/vKQfF9\nbiJWbeWjlJYPzPsgkkfWs/b01w1QEiMBvuZJ0H+nwUEpeKaJVQ2d0QFcl8G9\nMfd2NcTTXN57Qzpmngzc02HpwsXhpK6khdLjnBLGqmLat46QMYgC4Fo17WbE\nLa98FEkbclQUh8+X1nJjaKoeutM1cH3gtv+W7gefevno1xyRMHN4gt1M6Bzr\n/IOOrydF2jdaBUQ1W59w4mfRz9j7VdTdg6iU+KnaJJa3fd7bxeBmbo8nRvo0\nkfww6IKGIw8HcLBKYWjCB9fjeUxZ5aH7X42qwq79Bve476IEnHqccLfo9CP9\nvjDF1oGwovf/isG0a2o8+rF9TFWIjQec5z7pymw+gh38tWJMIkhP+ehGU/++\ni/N0\r\n=BdU2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDoqHFVYxhT1B8FWpg8AGf83qpeJcucIPIGNbdJEJ/GKQIgGPZJceMgv38UY5pi6F9vSJqWxUWp6CHG9FnHf2eWXuM="}]}}, "0.3.2": {"name": "ajv-formats", "version": "0.3.2", "dependencies": {"ajv": "^7.0.0-alpha.1"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-alpha.1", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-k7ki1CboXZdaBLzoEunIcwjYt+IldJL/g3pt+8p/IcvhPiE5CFYVSle/ixyXWcFmhOQ4ItnmS7Im+4uDRqYcNQ==", "shasum": "c581118f2ec8e5f943411e7b6234343cc64c81a7", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.3.2.tgz", "fileCount": 292, "unpackedSize": 1041362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYh4pCRA9TVsSAnZWagAAMRoQAKHSHuNJ//DHH50xrWNr\nISMzXRIqEfHvMUayZ5uwiniGa8CEh030pfaGS2FtvcM3TwgnQcPDZZZDhpdz\nbSU2imudxcvBbqDGtSZdIKlF1LHq0Xjs+RxmzQu43z31FEq2Y0HKSQbXpDuA\nSshfgDm/lte5WbaaI0rekWlrWmUP7rCAsWjYsZALHnyxHlIqQ6YzZMylsqqG\nvCD1gd+1U2klZJaIoAm3hJfWuFtbGwLIWqQAIQLVRbz9HhMApJ4vZohB4w8W\nNoAViruWKpaK7Nbqt5Pn+nxC6vkFiTnoDbRy/J4vN3WnSCZR586uaJg9+h3u\n+Do8ZBnJrK6x+YhpMwqm/41SN/pvplO3Gumvgeoc8PhYCn6VW36/OYE7VGlW\neIe7QR2yKGM+9xDtpiYi1AY8yGdQzJdTifkxyEE2Ir0ebnU5ITMCiJpBPVHq\ntnTvJktmMSUNOYh8Q4lNw4RASh1X2tPSzGvZ5fR92BH9y0lQp3ZGG9qMWhbU\nG96p/SegW+SMRxSD9E35KlsICZj0bJLfr2fZ4kfS04hu2LHehjRgHm9B82z+\nN8NHh9X1ZhO0ce28cqO/Q60k8wJZqHfiGUG1ItEbMA0iQJbQ31O8wHnw3n/f\nUtRCvPCVapHUpy63yqVD3GXAfIVGHkqG2PDWHkokAl2C7yzkrPFUlIC4xUFi\nzzU3\r\n=/ZOI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtwvNCTTtQYwqudh0PRdbEwgDwZfIr0cUlo6BNGGGFdAIgDcbY8uUd/zpeu9DK2iE6gxsozXlafaofZoKbHVHmORo="}]}}, "0.3.3": {"name": "ajv-formats", "version": "0.3.3", "dependencies": {"ajv": "^7.0.0-alpha.1"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-alpha.1", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-w0Gj2Vie+NCy6ZNoIYMP++fwv3kZ19p2Q5bKStzfGM6wyrWXwoktETsvaWtyoz3sterpT8ys15+8WOJTfs+0+w==", "shasum": "377b72a855fec512a9767422950b17d97bcd7d5a", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.3.3.tgz", "fileCount": 14, "unpackedSize": 36249, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYiWxCRA9TVsSAnZWagAA1yIP/0UQCq9oICQsp9ufF/Aw\n6e5S9/CPFUk6uRxd7du6JOD856WYIIVv574+vSYVx0jFKAAh93TsHMOVzFBw\nYFsIPFLqNlBA2tJHIe4LI8EY3zMtxYPOumwaQJaSgQYsoFx2rneNrFasKzeT\nyTPeci4BHGx5bIKYkpVgFKUkCBWXfUATpt3bFe2kPAbiPNQLcN8qQWTKi2Wi\nbJDBDPffcjh5JEe6nP8Qp8q1v/HTEPfNmmEgkOh5ND3wWy3bOu4jyXaLmeFU\nJe+RSSLejXTPA1T8AuRQTjfYe4YOVAtf0b8DPvlBHDPfnBxA3mU5N0dX91h9\nPCie/Ch7xNmcQ/fW5u3FxY+DpSjYG0VzXorINanENeh2id86JEcQpN+R7SfJ\nvbOgjH+zjknOaFx0r3GSSzNsf/a9tgE6nAU+/S31Cz6PEOlcQqMwaTL31Tin\nz8VrikH5X1J8CsWEOh+LbuaPlsd61y4+P8+VFWvJygSF3pKWl2il34v4iw0z\nb4gOlISyE+K1wuCuWbDFwqms7WdXtdCVfzbHaU+byQNgDc3fWs3ZfO1sTCSG\nKEn3XEIywMWsiOnZl8LgDICy8jZmWYvhiqGAjOpAOwnPPWeKiBPKghoFVIeN\ng5a74Fsp2fcjA1PcKq9f2idUGbDgIaZPXYRy3TZ7c+P+eHt1HzvL/1aEOPXk\nL9Wa\r\n=aUBw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB7gKYJYEq26dpJ1YPFGrR0HGM3dYTVX8zHyIeajXYq4AiEA3oL6vf03iJv/MQhRIjLAhQ9MXFSKCKpTiWXjrEoLsDE="}]}}, "0.3.4": {"name": "ajv-formats", "version": "0.3.4", "dependencies": {"ajv": "^7.0.0-beta.0"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-beta.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-iLcquk0mWlX4Xs4FeV8ipDx2jrk9YtuaAnl/bu7ZmKbv3K9uOFJ8LddhQSr+WMV35OdjOi6G/jVszNnb03089g==", "shasum": "6ccb2c60f7da7cffaf73bb2d04f1a6c6e9d2b156", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.3.4.tgz", "fileCount": 11, "unpackedSize": 31317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgefnCRA9TVsSAnZWagAAyP8P/i2ItlZ3pXhwxz+fUaen\nrNIamQ7XQCXCnHY3PGVmTspDa9lzNKsWIrAqpGt5TW9bsoQL6xUOu6LdzbRc\nxTGxDkpqNc1tY27HzPEIsEefoNSxpNcR6U5A01aIkCH2jmS0XqVdD3EsDb9l\nBstqznacv5KFnxxGrgRyRu50yHq9Cl/uLXC9DJpt9q7rdPJydQ4Q2bziBGlk\ne3rTUuY+nLy3BSNQPOZY3rN50gfDfoKQ3PsqqFHEQhRznuxr9Gt7BMDgQL5Q\nB2z54HLG8Wlk37wIDC9G397cIG7X5hI14NkQ5tar3LTsCiuf6/6B3HENiUmC\nZ1bB4+015CamuWYu+Ncb3BaRP/zxBlop3Sdd+in2XspGmPQcbi34qUVwqW8d\nTQO2LyMFB63281st8OhSCG+/CvoHNaEkZiiY4Gmx+8g8Q6x90OY2UfjnKfV+\nIsfkEL0M1CIYcHoEbe7gPM8o+a8zX2KeRuDqKROCIgIuTGThsfSPbwZr1pTf\n2fGG+1d7TdhJ6gg3oYkDF3+Aa9+iiyhkfjrGRRjj9yvrSTiMkCfYEJ+aMPJt\nw7kf+CejNQThnXJxhhBJjNiLeK3dOxLY4zgAcHGED/9UVIDzJvCvxl5Q39Lw\nka8ecC0N1mCTDfNJkdV0iRnmd9sNZsnhN/iGGeYfuAEJ0/VlrI2HjwFBl//e\nggzu\r\n=QOxK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBiQOEjPoRrAoDzw1/2tXxwuWv1ubRUh2HNxD6tjoDQ/AiAUFq/KyBkWDGOBuGZifGBeaBqrVr9b82bY3RjS5I7HVQ=="}]}}, "0.4.0": {"name": "ajv-formats", "version": "0.4.0", "dependencies": {"ajv": "^7.0.0-beta.2"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-beta.2", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-jpnvtETr71E0RhFHk0qQ9XzFShVYq3MH9/mOIUPFMluBSzQz6/dNN/3EnKQ1awzz2DAtoIMBD7tKMKGtUIx/UQ==", "shasum": "f2e992b4b755117f33f2cdce2e67ce4549c93bca", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.4.0.tgz", "fileCount": 15, "unpackedSize": 47944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflIJYCRA9TVsSAnZWagAAzwYP/0GAS5FBCHp3UrNY8ywp\nlMXJ6ixW3sBWgvgDv7FjlOgPDxGtRGQbxJmNJWuXVVye88EfRY0JYUZSnn3s\n+ZxSAahQptIMWExmLuHtfqQILo0/v3gdgcsz0WzGQFx3XSfNgH7Tc5RjaXrV\nmO0CmdM3sjxRFtx/FALg4CDwU8B57Lzz8fhfOj3YdFEEoKnjrRrXM5A62ziA\naFaXRLkBhIbCpblAAIcPB/H0GJuI55Hxl9nqMhryeq/PIbnXM9KjL+RJC/SW\n2RZs0SWOuyKrby7yrH9dG3TUQIU3FLaE3ip1zEoYdvlm0yD31LyV9V03CwML\nWEDOZun3TMKbY5XMiMKsz7BMUiuPdVBYt5ypgsmUV10SaHQKhiTOhOZkwkpn\nGlNqSPhN9OdL+F2IE1eC6h704LdOVHHZ/qAWZ8i+pCCReucguVthOUZdIGe7\nZb4d5QibhRFNWo2Mb4QTmpDvK233jh75U7L3C21mr5CujB2p198T02UuVirx\nw9Ly3qcTJA2Ex8FkDyphxb2LELJBvzFEJeJq0wdAloOz3mfVMeppmYnFPNZ3\nddDwoGHkhb/zg1SzRN48QBi+4Qnzy70cdqbFH+q2ET6SMTwfPk8W9EH4V1vW\nwwDWb5Se9vclFjKYu5kMe0EcVGAjCNpFhE7V+9fIjxNuLDOtdQnrIxiH4c5z\npd3G\r\n=h1AL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGX4jItxkfvUyTK2ALkmLHoEHLU+dnYTmPKcGwjCTfKCAiA8efbShRlolCLNkKH7yiCkm0MDva13i79Ha5E+JcPBdg=="}]}}, "0.5.0": {"name": "ajv-formats", "version": "0.5.0", "dependencies": {"ajv": "^7.0.0-beta.2"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0-beta.2", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-B2OYp14ov/K0NaTAp7NOp8KUeual57as7KBA4RNH620jIxr9KoQGljnFyVpzDGBqPC4nRw6A5NjQfmlmUHCh2Q==", "shasum": "88b49ca65e5a1c675eaa13b557914902e725b23f", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.5.0.tgz", "fileCount": 15, "unpackedSize": 48394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqE55CRA9TVsSAnZWagAA3R8P/38SgSyS/jn28ABepxUm\n6BCAFKo2rU0/GaJA8VqT1nbtywvnueSu7knbx5SxFrUTNTCYIM9D9k9F85b4\nBkw0TYFtPWxV2Kon5qEY7p0iRaC+Z6emrgML6VD/4AKXLbeqVADyEWGFSw8e\n4nx10tafkmKNd8eqr55OTobcsGL3zgOYdWTpwTXX3tn9Le6RtgNQ0d6y8zKx\nw9eFF3/EAqNe/7nJay8zIjfeUEExQttNbidHe3khP67QLXXjsa1W2IG4nUY5\nqqT/UazpCI8gxo5B7o/Wsfzh+8n3/t43kXJ2sQS8p6JoWrrNIg9vVemBZwZi\nogluwWInfkRIVJ3HW5GR4Z+VUT6Z5hqeFu9V+5AnTa7DaHNc77V3UGWsLm/y\n81JERA4EQCO2qo5iXWy6FiKAj22RQrtjtMyqHrxTUbkdHvPx3KOYwmLSvC5g\n5CZmypXcSXJLpAYPGizpjiGEeZkgg0QglneMPl0ZsqvMiEjo2nZrJdHZ9drf\nmwUklrR6P4hDhV3BEFBZK0J1K35Vy6OBJ0HrEJSb9ETzyEUgRtNkSwhEZjP0\n2/i+rJ9WAkAZy+yOfZrlI9Is6DfkUWJPrSz1k4HVHyrqZZzbt2O4Lt/xxx6U\nH0JBB9H2Ae13CzKn75+A/1gVX0HQrldwLR9yehEjAO/ziFiymSO9aInNifzU\nOMaU\r\n=pXWT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEKMRwqoZeYv2tvP0IFFiCRGAmQOZDl0new/h5AfXM6AIhAMIBEgT/FFnHsDnMncATRoZRKs1iz0l40qpSSpf4nXaR"}]}}, "0.6.0": {"name": "ajv-formats", "version": "0.6.0", "dependencies": {"ajv": "^7.0.0-beta.7"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-3Fc<PERSON>uuk1oxauPG7tgYYEGbVwJdVx2xhAiM1TePDOmSDXC26cllIoklQE7cwvsSIfg4Y3C30DAyXVlGz9sUjl3Q==", "shasum": "e9b3106bf85ad213efbc7194618c22f427eb700f", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.6.0.tgz", "fileCount": 15, "unpackedSize": 46280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwh7fCRA9TVsSAnZWagAANggP/1A5Y8wI0/l/QvYZ4fed\nHMA5uA+x/GrTLph0+oSqKYgW8n6Bnl+JPNfqt3W7AlL0Tv3nO3tV3jWuWyFQ\n3T5Mrhx5rWnuRfQ9uwMeU4OWDElrJ9D1stldd1RyajBojLcmVYCVuSxtA/p/\ntE6p/p05BowNAmwRXUBreZMs7yhtQ8kLXK/wL40LIlL2qP5oegALTP8MU21I\nAoiQ8amfskrRPmvRxy2Bc2tSOdZNYdjEuWep21hp1v33wOwValv9qVVzDrZu\nZX6vZi4jclPTkRMCykxZvJ8VTf+8xIBj3tcIiOvY7FiyHeHt8foIlUkM57jZ\ne69we2PadgS49cP7j+BNcx3y3/K702Ld6d4q/Q+C0+87P1LVVaHmv8uBOGSJ\nWifXViMApGE2caISA9BIbj5q1NaNazIjkGudagddqVMONczCCzszl6OyKamK\nUmFT1iORQ5XpO9khcxvOWOfsqW3TnRWtHyd+swWF27HBCE4k8pk0U0Q+DpXB\nmnSowt3k/QMfVVW44Lc224advOP+Uds5PJrnc3mk5gX3ieTjZG3Fym+Rq9C9\nsTdz6/mnLE7pFgTmGtzhluIZ+gGyVNubOmNQL96fKKRzHMoYViCWs/9Cln1N\nbsIPpCi2faHjScozKDh05LvMVsWZjrsxx6+r9t7cxNtX2rLkxZtfCdSHL3L1\nvIPe\r\n=IahE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrtK3dDbnp6/iIV7Yfe2ceTI+3WXkVJto7tINNR6VilAiEAppd0lqxCB2iyNPMEA0pUE8jfUCrDmWYx5NDC7Vfok4Q="}]}}, "0.6.1": {"name": "ajv-formats", "version": "0.6.1", "dependencies": {"ajv": "^7.0.0-beta.7"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-Zze3O7jGabuY4ospj2s8Jvjf5aGNaiwrFRqEdWdHs9oJd6IFXuWuS3ZHbJjyJvkjlDGncGQEBYnPRY9DoyaXgA==", "shasum": "a0bb4bdebb4a40c062b609bc34eff4e2fedbca9e", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-0.6.1.tgz", "fileCount": 15, "unpackedSize": 47285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw5xjCRA9TVsSAnZWagAAncgP+wUgBtVnVPx4ktlw4AeZ\nowy4/B848FN8FuQpnA6REm02e6weoYIKweHG3S4ayKkn7qgS52reggal7Ug4\nkaWo4xvma4iChac81GFb0wpshyLOpO0s9BxFGFZoagoSXNwU17SYqh1PVGVJ\n8mqjdmC3QumV1BQ/9Q1L0+/rUiUL3d+UWhC/7Bd0xki2JITO9p/5RBORqQ6p\nSv9k38qB3Jl5SQGWS1ggxnjR3HZJf1++FL+QSx26morEV6bd16PXir6o9n3m\nS3FgaUSICovaef2+vJXtsr9SsT+mL76idYxTs9iZtKxZyO40R2Z7QIyrvKRI\na97L/UOBDhhI9EWt10yhAVzCTBCA+TcasHBIATkemVxvXaykv/0njoriym80\nbkGmIbPywvoNBqFQKqmLpqcVU8wK6GE9aqxYHHmcdmiRIXfbuczWopyE6p6s\n9NFq+kmLSewL4IFC5hkFrtNwrRwUOMvuCV4EopXM+g+hVhyity9s3DWdlEtj\nUtANPCS44fd42PdDhyJQsj8xh8eOM3o4uRr5/Kn4PSgFCiGaR83jewGWJLR0\nGIaw++KApqGSXQmJa6J1f6QIGoPGmjkyo/l1ZklYtuJwzQYBexDA0uig0l63\nKQFOET1pPiv0ih6ylBEV26g18mjPUrc8SLpQOIZQgtVfDsi9vHvEB/NU0rno\nxNqR\r\n=w3y3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+i2013DvNDgx9neu1e6cn4Q1KypfL+hQfHDvBPWOXzwIgQKOmgQCSXRDEhXLgazI3JlgQm7AoFS7Guc0ZNSOW83Y="}]}}, "1.5.0": {"name": "ajv-formats", "version": "1.5.0", "dependencies": {"ajv": "^7.0.0-beta.7"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-qfWBJjdMIm+M6efUCXv2u8Jh1SRlSiPp7dUn+d4HCdoE8+6Cu3rUDEr6g5ZkGWwa5tvCmqicySQsx6Msvfl0lQ==", "shasum": "e34d1f569d9da29b0511a02e1caad4f846eaa2bd", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.5.0.tgz", "fileCount": 15, "unpackedSize": 47770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1gz3CRA9TVsSAnZWagAA2xYP/2JJHwsn5NMwaDTxv87f\nAVScUYv7YtwHjiJA844JjK/9BwQsWGhmpTJLtAQop1J55yackMiqggyADcUT\neN4UH9I5RHmtsNBx6E+zoypGcwPWhIM0wPz/W5dnMF2s3I9918ugnYmYgzD4\noTgoRHKD9rk7KyoyFQp8Gutwz5a9M394Aqx4Y9fj6CJL9WURB17s8u7THwjc\nB5RxDoSEVG9Lc+VwxTRc911j5Pqn/jiwr5YIG+20x6KUrbk7g0DNtiLFqH9S\nxv3DNwXXAfBPwrRZ0ZaRuf+XTFKVBo0N9qCTVFZXeyQc+DSA9rwMj/cNMCSK\nQH+MBHUcD8JMN3zmZNB+Icif0A0OTsPTCvqimVQsNZr9ewUCo5r3a/UczleB\nBr8W14QIXZsE3WSfCQLzVWe+csolXxXpx3KqYBXCSRjBbrVHtcpBY7GWPy7W\npcv64shQmpjlotgSfj3vKJrOGQ8S12CF7PzcljAC76xBKJ6LeJQocqwCttqQ\nzAKorwQQ9x/L6lXEa3MLFDfQ6G7tsnr2q8ENo+ztYEeufXf2mCS3Y2l97AKo\nw17o+WljqC10YRlfFrnHmxet2GX9hS5j/gYgk6Vu2YRcnVCKDU99iv54NLlW\nJOF3FAzmTBLR/N6VYsEaOH5VBuZh1roi4t8PV6A03l9hgwI4f1rgbtbPgEXu\ndtX+\r\n=n3Vx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBKWAAgD9w9pRgR32V2bj2Hkj/8f3r58UYJfRpM0wZfwIgdYrRL7WHPkKnqU8gyuTA8lP5hbtsasqOmAs6Wfb5utY="}]}}, "1.5.1": {"name": "ajv-formats", "version": "1.5.1", "dependencies": {"ajv": "^7.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-s1RBVF4HZd2UjGkb6t6uWoXjf6o7j7dXPQIL7vprcIT/67bTD6+5ocsU0UKShS2qWxueGDWuGfKHfOxHWrlTQg==", "shasum": "0f301b1b3846182f224cc563fc0a032daafb7dab", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.5.1.tgz", "fileCount": 15, "unpackedSize": 47768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2QxmCRA9TVsSAnZWagAA8yMP/3xE2X3esTUE45+OgXPi\nENT2BF9PaiU1/zzK3DW+woG4T6X7CopSGE9uXJQ+T+1HecssH2gKvMtf1oGb\n1mjeBVh+hTjce02MCXWRM64mUpdv9fviow2j2QTdsxgkAxhgj0nx869YRs3c\nwnPDZdEGt9iH14Sq4pafQZUQLtsPPC3n7Jky/QGhJazRUj8DAqfHaXtaTCw0\n2dcmpeDv4IBmwzmHs4aj4akiPR+Jlb90avTrFbG5HCGK8Yzkd3K+YeLPjHC2\nqBn/2LAK7tOGUkFbKU4kuIaMWByRbK6HTV98hpJ/h1IZAKfa3kdnklnZrkuX\nyi8c+q8Zvi2Frx/mUO3YriZRWjKFl5l//ERJMmYTa8et82z9VumqdOyMl84j\n79p7hELPUorL4JPh/WLFh/SXyGh1Do50g6I+NS8qEbdOhh3p3xb8piztrGzG\nCSAmuch6ANtUuGX82mpdT4Hb2yRDDxIt+HsyG71Pf2aKRQ5mKwP/SBwroKGh\n8v3p+iPxYutN09IWMsxmbg4OvEJQ+nQCmpCadqqmX0PYa2r0HH8iN6y222MM\nE9ZzPfvLTrqDsLGOT4k1k6c1Ycomg0FWAnhStuYUNSGgTEXY06ccyqQh/qXV\nVPRk+wSNqjzL+qoubqKIBtnbd8wIblfBoKeVBiKPUFaatHvN1vkkDVOrdyMM\nzNXJ\r\n=W7wq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCagTco5JP+C5eagIYlv+rsxT4OQ8igzud63xTbUn2w0wIhAMhkiv9URMpjSkZBH16pAPqGhYGjyEvy7pz7bntjsbsU"}]}}, "2.0.0-beta.0": {"name": "ajv-formats", "version": "2.0.0-beta.0", "dependencies": {"ajv": "^8.0.0-beta.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-BOHT3TgTSC84Bk2sdUBuklmHLNTFXwpVjWgdO1W4aJPhIEMZyE36uP2oh/sQIbMDTEiZij9AVBCwU4vf75+gzA==", "shasum": "53dea3d51f5f43dc6139c30bfd9f22d375fe3897", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.0-beta.0.tgz", "fileCount": 15, "unpackedSize": 47782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTM8nCRA9TVsSAnZWagAAjgMQAJP6ibK/49Inlp3U6UXX\nyUqb9QvrRdrktybauUDJOvqODUBIXESSN06An0/eiuEZTGyiQTlZuxdGk0lf\nvbI587bEbKgwRrh3R5EFyMxZhWc8+mvdCyL64HxFNEzcjdqZ/428vY7CuSKN\nyuXFwQsNQ/s5FiJbQLzT+pyqV7bh+XtqWphLA0U3QIxqbxYbknxqpjOb+fh4\n8mMh6PmNE4aJX7pXtJ39C6XStaQrsTejvbg++IqOtPkDdQejQGakzlq1/u5Y\nWUWXt65sHUAHoM2EIGmMI494+38cwax50hi4NUqe8zwYnR/NeCF34LXHTa7q\nTNGwTyQLdv+/JaTVRbSYn6nCeeN0BqDWkI3rGM+yrIMm1eo2OOkWLXXh3Ne5\nqw+Cvl5XuCnIbC8sg4NefwfpFxBseGgFTFS1N2x4H+DDjQ3nmHWYNwGBB+ox\nFpnBdOJkaAWIf2II4q82jzDg+dC6pNP2xi8iGqD9Kcv5L2hdnrBrZJwwLfi+\n4pdc7ymxm/VEs7InbZwsFqaWT1ePcIrd4js0XWCiMUWIN1Vku6V95ZIlZlPU\nTvHFggksGW2VH0l/DKuriTbre1+EveDx4sy/jsIudyTu/IC7LOhJAX0gWVkv\nihTQMXJKwsJNAO1Sl+4Mtjp+0JZrj2busNNx4vxAsLn8ywKC08DrRY6f7kIi\nwrYr\r\n=6k5N\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFkjrukZSwHpfk/dTTAcbZblKhdURyVLk5mshUJVHJGQAiEA5qtmcHsluuY/L885tG4fteigpaG8tnyM0NW5pxa/WaI="}]}}, "2.0.0-beta.1": {"name": "ajv-formats", "version": "2.0.0-beta.1", "dependencies": {"ajv": "8.0.0-beta.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-AZSYK+N7B9ZPtuHjUodxyYJ/G5fYYWakZ+NIvHpUqFmkqNzA0OoJLMctK04Oy5ePCuJ+Zcosa7+f17PIESIKuw==", "shasum": "830a1f2076573061e2ff5345acbbd4487da4fc75", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.0-beta.1.tgz", "fileCount": 15, "unpackedSize": 47736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWQOGCRA9TVsSAnZWagAAX/cP/1XXZHgEEYnJJXzQ6Uo7\nTGVGOav8aKNOp8+fYJBORUNW2ACW/c7J7Cfgw/wwkIzyfNKGi+Xjc2iln12M\nOi+zgzRH0aFFeabadJ8yFmGIxYqYsvBtC1rZ/78BEt8S1hsBhMnPk3JX/trh\nVAR6YVhvr9UX9xX8aaWhILw0CtefngV/UXJVc97t/ZgaG0cuVNBlsY3H82gM\nKeZ8h1C5Bcz3NIHwzOrc0JC4ncuKwUaWCt8eaA357K2PbiJgb1IAbViNybXO\njFtPiGkFGKuytOF8EwkQfzr3qh/tiiUpyg8vFpve1yEDZWjJV34ntBySMXMD\na5k8ij0Sjya0rfnm8eXCbGp0pZnPzWYOwDZ2ntf0eq7Yrat7IrwuYksaGSmL\nHt7DDhbf4EdmmU3MKcSQ/DdaG6Yra5dl8gM/EAJqW5YvnNLTqCRcQfnP3Ua6\n1p3pxBj9UP6NcEAt9ujpflPD/Lmn/vE0H2HK00dAiCS04qebZFxeQqEG4QnF\npE+J15h13KfaSZtOtIXcABxBJFC93+FeqsPDGnWYI1KRJ2NvXhXr7BW1Pkgv\nYHLV0POHqOh3qCa/RbyyFKgzEFfd2BCseLF88/nKww1A3ollOoZXyYb+FBI1\n8aLrjO8ZkSQRAitYqnhgWcf5gvu2a84a6BOhkkURmYgVpyIreEmYO6EhIV9K\nPXoW\r\n=sik1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxBOgnWA1X7Ni9k5hVHMdfq7i06w3K882Px6u+tPbh/AiBN2MV6ycf1qduw4oaJ/yiOzVtwZPvN0CzLBMafXnfaiA=="}]}}, "2.0.0-beta.2": {"name": "ajv-formats", "version": "2.0.0-beta.2", "dependencies": {"ajv": "^8.0.0-beta.4"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-TFxyA2PS5E3hK27ile/VBzkSnDfVhTDyROskxV1A6mGT/gUYf6gZ3RAlIRgaTc+dDiBiZOOIEemuj+7lpIjQCw==", "shasum": "a5638a5577036d69006263a7294de1b273d7eadc", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.0-beta.2.tgz", "fileCount": 15, "unpackedSize": 47737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWZSwCRA9TVsSAnZWagAAuPgP+QFh++yh6b8wLYpiswQU\nOXOL/SwexOLNR1WXA84Lh6WY3OA8K1cRpUg3ahxXO8A5kNzbySZIjwL5vWnP\nrGooVlYR3j5shjzQXc5+9qoecEDCuJnjuEqt6Zo32kDJMm1c08wbd5uCzng+\niJaxAr1h2fZU8DqbQVofzrdK3eyYQHH/SIgWCSvUgFDKbr1SdE4KOUncY78r\nS1VcuuL46n77sfzi1Gu//8niu8X0W14zfK4J9geiys+xGTiv5ZxqfbSIfrLD\nD9TVPrTn4dGrmjINGOfkIXe2gbqk4kG7n2GVYyF8Z7GlsL8bMeDsCyHccRDM\n2ltd4uFjPbBtFNJrIhuj69XOrwvBKcxn6BQgxXBFkYnEtdyTBJ3rxk878lGw\ng9aN0tSle9FgCCgUgczHFE5+OZWcMXNH9dtxl7OkjJTplplfiugdvuYl6emF\neFY91MlDz1aElBI9MQpM4EVV/vVlAH22oVoKOb134ONYk1mIF3MgaiCeaHz+\ncEAkuLPhkRUJEqnyaoABxuBIIHe7nWRtX2IRXsRtwYOPFFlAeLpPlrjmT0YI\n9OQ5fLn5lcGBNiMC8Y4AObRg49c8gj0ZAChyrtGcOBrrXjkGEirUqVPH48Za\nsD3DTM5n26B9gXSf7vcMofFY6EiTxIF7iP8bKSBGch65juOQLTFgYnt4ibgs\nVB/W\r\n=sCKn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCr9S9Cae25Fi481owJ7v1y8CI6tMq0/Ck3CCRRAjvoWAIhAPnfx5SARPfo71pejWuMaQ0wmR+7Fpa9VJJTZyud0uZ6"}]}, "deprecated": "published beta to latest tag"}, "2.0.0-beta.3": {"name": "ajv-formats", "version": "2.0.0-beta.3", "dependencies": {"ajv": "^8.0.0-beta.4"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "dist": {"integrity": "sha512-YsSpXoLpwFHsq8l+UImIgEh6vnjA3bb8MV2/l/tbrFdCN5fNSH2SA0i70bOsEBYB1heBmcK8/I1ueK4az8D4ow==", "shasum": "abe42a10861a3635e7153c9d5088396645041541", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.0-beta.3.tgz", "fileCount": 15, "unpackedSize": 47737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWilXCRA9TVsSAnZWagAAYAkQAJczqHcdl5omli1/0rzm\nl8asp2TvWrz56H4F4QJVb3rbOplN/GZpZ9zx0LHrjcnlD02V0jj1LS9p5Ldb\nOlicyqhASo9JUGgyoEframi4uCTS+jL0QcVOE3aJjn/iUchbD2OwcMc+ZCkd\nIPE2Osgk/weRnqwsMuWDnyWZ7KgvxmHFy4m9mZHIRArXiY/AFRY71Cpx0+/H\ndnyOAtGICalilWGlgkfhnXRyJyI/1yxMiR1w9p9kFJgl4U4cPJUcCyhU73c+\nEz7MYlanhQuMCSamoRa+foIWmdDpL7F2dSnM9IgC/QEjHjxBNm14Saps6yIU\nJ8cfIlqo29Jtk6a9ycAMF08IyyQ+o1M369xTX3OobWWg/Iv88vVQilY+5rs1\n3iQ2XzwQBRZdNiTUM8P0H97X+tRCrPd888KvUyZWiUKHJoc0xE3EEZXmpvfN\n1d2N0GWbJepGBOYEys1Jgtbmn/E8TC245VbM5xst9zC8EZlBa+HI0nymdfcS\nAyXHjAnXhpvyagrBJexBiagckglN6tLXib4//HFrGepaiBEl3ANZv4utceY8\ns/DIxpBxgi6xovKhv3+JSweI0k2O0DrF7+97jxtVFFaTuP5ICQ4bClASAuJG\n2+Ih7S//nBYfufMoGv72polvK+4NR1l+6O187xVWqQ0V6aHW+dCGK09PAXJj\n8SFA\r\n=Ulc+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2Xkxi+apK/hQVPKyIpGg70Zp7KFQff1leqfTovlQO5AiEAv8VtCZc5Ibr+eiUtre0/rzXA+bQC6YhG+0d2RT3UoGw="}]}}, "1.6.0": {"name": "ajv-formats", "version": "1.6.0", "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^7.0.0"}, "dist": {"integrity": "sha512-iio2lsjYkuTq49avg+/coyM5D3qdjyW1dkiy+I79XG3DAQFAOcGltC6eXsw6dX10OtH2S9Kyez7OkFtY0bJBBA==", "shasum": "605304a6b4c10613904b55ab2cda0669d233d127", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.6.0.tgz", "fileCount": 15, "unpackedSize": 47791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXvdQCRA9TVsSAnZWagAAqVIQAKQLxVUO9FW3MtSuxPjz\nVDjXsimMoq3uowrvtBTm6Xss+TjsR86RqX0PqBBwZfz59U1jJigZ+S3+B8yi\nTwl1I208zNbFq+cW3Ce4M3To7+M1nkA0FFVU5urASxjfAcoWTs0q22IPd3g2\naw9WSEsTMX8nf8PgEbFFwsQFMkcDF/DYHyCyIfvxPx3gAqysvAwq9Sb+o3Vv\nOXfcgYQNuGTtCO1FjNTswFBLzwiEoq8FZ1gSfgJMEGfmKMJYHhz/wJ8zZZJL\nAOC4BODCVhuW4blUUc3I7oRyhKC+sIq0yUSjL8jtaoMm+QOW97Bo351DBUH1\nlCxDSzBOz7cdT8EpnqtpR0UDJP/2FGRFnT4Yz1ROtpj7krYjq7qxphZiyRhl\nlaZJ20DQOHPMTVh41liaAkt4YDRUq4YiSwHFtrMWbHFU+SXyNnv+1Uloiwqq\n5MzbzkY1XmswMqmuRrZHhc5Dx/X4/b5Fh+yICmCDBQWRnVSqDd+xrp1EFvJW\nfdp0eLq0DmCHQOUqGsRkThs9kMaK+kp5ZLl46i/BoHeoIaAH/qD8Pt3duv5h\nYBR/tY8btxkGhbgudavzP2gdzHKBtjHBw70fjknRimQrlaw96+xzwhnKUbTK\nE5WgEIXcTRNOIAOsmdAWjctt34bhSF+vaet1nMv4ji+/3G00WgwaVMx4YFWp\n10GO\r\n=+BfM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJAfn3sHQCMfP+z7te38yGyYrDtB2aP/zgJ3rKm6czjAIhAIppo20UIw7tOzEsLn4cxp8WBC4YTt/H3/NYfEk5dfUU"}]}}, "2.0.0": {"name": "ajv-formats", "version": "2.0.0", "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0-beta.4", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0-beta.4"}, "dist": {"integrity": "sha512-+Qmnz4K1Z7HajOd8hPX7w+9WlCRfW4rvcMr4XLW4CghOAlS3qWKX7pUCJHaj6HAWPGaWRy63evu0v8CxOClw9A==", "shasum": "7505a091ee5c85757a4c1509b2ea7a81e72f39f2", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.0.tgz", "fileCount": 15, "unpackedSize": 47829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXwCyCRA9TVsSAnZWagAA34EP/2ZjqPoqirJO5IAOds97\nh66jDRao5cBWvn5AOmUoGb7mfnjSYOPpeMp1yV7uFzeg50BX6XFfKNgKxaDQ\nfmYNEb9U+d0lBEhFgQyJ2+jVqJOAvNpRF4131REaq9Y0s7VHFDim5nWZ14PC\nxdrgtxfPwuV82YIHUzB3Hqu0FvWaZ+/53eBwkYEAad1qzQ8QB6bgb/I686dQ\ncOHuk+wfz2LGDVQF2zwpFokti6w+xMUj9mYOCRMtsgqoahHle56qQRVyDg3p\noWeWM7WJEuPqYHxtrTLd0YKKhT0gmtFLBRoi8EbOs7G3vNNEzRduXxMyeeFR\ndNa7+Cq3PzpGAO4hDR34rbQePQMlbgbQJM6lYw19FZLNuXx7wwZazravvNns\nO1Wbh/g3JBXx7xd6T0EvBUbXAFDo42KVtXwITwtirCpnvQyQmIU8dP01MS54\nIkPCnywTLtYCCsvRfKsrnh9oBwveWy443CeBQnCkXKgAxjsEv5pXmcP/UbP6\ndop5pHuQOX2JOOAGYwOvvJoYCR2/umFbh+LCD5ubWQl/zGuhHnu9tw+GrTYT\nD0ZlsYezcmk/iRX/Kxa0UB/jlDhL0/V8Qea9xXtxxqGtAk+qAhfRgfJY6HXN\nunwoCZQb7+1taMKhY0c3DOF0S3UrkWaCK9xmZraXv47Q1o6YFF47uMzRb2f2\nYsNc\r\n=mVoW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcHDmNGAne9WTAOOtEuQVjAR9oFZSeQ+Zxi1n4Nk5KCgIgFgKJkjb8ICIibonrGERP9VSr40ZnfLZg/g4NV23mqkE="}]}}, "2.0.1": {"name": "ajv-formats", "version": "2.0.1", "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-WOCTxWY8Q+JLBJxJ8fjizSuk72qaAlL3v+XtJakEger28WcevS5tBEH2eND9e7U42thp2Uc/DNDYw9riD+kTAA==", "shasum": "16ae6250c8c51bd53a4fa4885ff29ac9a63d04cb", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.1.tgz", "fileCount": 15, "unpackedSize": 47815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYFrnCRA9TVsSAnZWagAA5/YP+wW2qY+m3vmzYqnLNiNc\niaabBWtV94kYtGHcGFqPDet35Ov4mWV4RsAHwLPQm2tiUN0d5Hrp6JgWWQjV\nwzwRPNxpZpI6hw8g2azCyt6Ew7eTvYO7imogeqAmrwaH51wi67dp+aKAQflM\ndY2JTYq+ssj3eDoXmcQ9m8o1eCjGLi3cPju0lgc9ODc30x+RtNBTQvFS98YX\nEQywFhLhNMOJtywjrBz0C4AJ4FoFq53J5KMbosETq56ncmE/dMAGtXZ9iVC+\nmDjwNC+UsEcgrtmMIwwH6Lz1d2CCbWr/3LVWGijvBuk82liRXIl0hG1O4Aom\nGm8mKcfe9tMZ+IlJPXnXTp0+0FhKpl/KX+I/sCxsRYTi93CXMKyVsow3oQ0d\ng1AYRAxIRoizrCS+1kmzI30hYryeIzrrEtc5+QALrC2HxyaQ+yi0QO/v5auG\nbvVSGSY/hj+X+znHfaf2Ei1bIGsg5tRO0gnzclagchBnjRIPD8Dv29/E4yvz\n1wC1aHZSDZfUducGt00/NixYzHFaDYEjG0Va6RBHHnlnOp5EZ1wnKa3VAnc4\nt6i7r+heEsqE8IS4fHFn/BIRmucTMCD90iBNr6UGFyR3f2NJ6u/MUqnJJ/vv\nSpNvVU0SkrPcWDFdBIGKWHmAbT77ksjM6VXLLf5crGVQgdJZJSTCIXb+EZry\nA3Ia\r\n=ihlX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVdd851rzXOQhRjIp7Y0bb/n7PnKeeaCsRp3PxtwHHmAIgG061y0xeRDs2xwbffcAIvYzxAZlffk448a1EiA3DLvI="}]}}, "1.6.1": {"name": "ajv-formats", "version": "1.6.1", "dependencies": {"ajv": "^7.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^7.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^7.0.0"}, "dist": {"integrity": "sha512-4CjkH20If1lhR5CGtqkrVg3bbOtFEG80X9v6jDOIUhbzzbB+UzPBGy8GQhUNVZ0yvMHdMpawCOcy5ydGMsagGQ==", "shasum": "35c7cdcd2a12d509171c37bac32f2e8eb010a536", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-1.6.1.tgz", "fileCount": 15, "unpackedSize": 47911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZwOKCRA9TVsSAnZWagAAxJwQAKQBI17IgJSivnfbc8HX\nthsxjrV7c8SQObO6Fj1YFQUvQXljQkkfRk665jKSRZJj+ZPHC0oqDysLeNw6\n0aZNp575pO3vy1XuBFZ0qu87e7Jo4xLUXo83sEaAKrH07LYn2lkRu3koFSi9\npBryc2sNNAk32pL36Dt6P9XGsacfHg7T3Oc3HZw0rDjrYAN50MEkU3gCtQZa\n9w86lCK4UlvD6yhCljWgeCsxTdunewuhTUOBFv1xX+lzzpoGRfGvxVoSj46h\ng2K42agPmfa+3B5Ha2FzxKtH5DTNEWQSWWj34yG8MRjf2oX8K9r9cQbvTq5k\nEMmQYlU0xNvuafNd0CWHhqaKJbJPxm5v0Uh7L3/QxgOk2gv58b/cJlsoV39N\n5iEBJSq//4zMsWorOZgB1MMHKylgOoTRLX5qL+lBUyN1MCbtP7ikxXlBI0cJ\n+ZNBn/eBjuVtzN4ogoThtHL7T2/GrSlBEKaECreI4FMA//BF2gpz2dp6Ei9/\n9bH2THhLf8JIc0mSNyupIB96n4fivxzG75bh4cScec2gah+3y7I+Mz+08hfn\nmStNh9UlPXb9QO/0Tmkk1TduEOHZjKWF8adru63D3wf011dkzBoLl6edT2zx\nWsF/3MNS245pOLF8wvsa55jqxc3mW38wAVkK8DoSZ0Kmwvn/SU27ErHzF67z\nMr7y\r\n=pKr+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBk9KXSYB4NZP1VF6r29RI0yx+mHWfugr8PzwK+mgIZSAiBtR/7oDLml3VOfLvjpmLj0jTrlhhi+xLJxmkL3s/zIgQ=="}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "2.0.2": {"name": "ajv-formats", "version": "2.0.2", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-Brah4Uo5/U8v76c6euTwtjVFFaVishwnJrQBYpev1JRh4vjA1F4HY3UzQez41YUCszUCXKagG8v6eVRBHV1gkw==", "shasum": "69875cb99d76c74be46e9c7a4444bc232354eba0", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.0.2.tgz", "fileCount": 15, "unpackedSize": 47935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZwWKCRA9TVsSAnZWagAA92sP/3/x3g1L+CBlTQzlBCfI\nanl2r4RqnuPabLjE90f3HW/VgigvMqc5OTILbFEGnEcDE5KJVo4i26aJm0NZ\npDCRfddhR6jBiscVpS3Hy/G4kdjdWMlgas3Wjwe/2u5k7u7aQXJCa5rC1Gfa\n6uEM46p/xPF98E9CXQ4mPTiV3OhntjAK+qml0E+LO4W2Q9IBJMuCjWZgz8cm\nKqOz4/iCBpYBw4+K8mS5I7iE0uaCXHZ0OCvY/86Q2N80SgNqvnadG48sNry+\nqlfn6IgTLx5cgyNVRry0pgaJC342t3bj7wUBLAfWLOOSzyaR70ywyDcO0MEo\nn5FsCw4o8mu9bURvP58fDtMHEUM2nozt9VGE/NptG/84gBm38OkYgrErTCPc\nBiPtAv0ImU3dxjBIistQsGv9FsU0LeM3GyKfAJdpDNdfcjj0kHBBvRwq8fiq\nmLm3lD3oYs+Z0GdLm21DTWp8KL0bcjTaKSWo2nkNSIRyA/PhdeAw6FPUDHN0\nIoCFa0gjrfZvumx+1KXj0XTEhdeAH+oRQTXcL788TozOlHuTRFq4JUHkJYLp\n42CIIH/wIh0FZUue7kiiammz/tYHOhwnz3CUNfcMncRG5V12TIgeazlSD8R7\nRBc/Zs7+A3jfD6mG/4NJbHeCstiEW6nczOE1JLlY/1MOeJphOVmLazwKoxwU\nvDMQ\r\n=W5/G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBL7z01TIdSrP6xqHxfqN4lCVi5FXq9cBrDZu0+9D25PAiBOXH8jQSPOK/********************************=="}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "2.1.0": {"name": "ajv-formats", "version": "2.1.0", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-USH2jBb+C/hIpwD2iRjp0pe0k+MvzG0mlSn/FIdCgQhUb9ALPRjt2KIQdfZDS9r0ZIeUAg7gOu9KL0PFqGqr5Q==", "shasum": "96eaf83e38d32108b66d82a9cb0cfa24886cdfeb", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.0.tgz", "fileCount": 15, "unpackedSize": 51881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkbZhCRA9TVsSAnZWagAAdNUP/jgjBXV0KOExgoN41IlQ\nvHUr4EF/TLVTtlI83H7K8OFJnR5Qnkh03eB2LduVcFmBGbIHjd8LTJVPsO/N\neIu/6DNB8LgTcQs0wtCBFAJ6+7WUsSMP8mHbjIaZOEj2fDHQE1Lf205OHrCI\nwOVE75CQvStmQOe0yi0++JK3/4TRKaemnHFx3zhs9PoyBF/4g0yCazRwGAUe\nXFp4iuneVo8NRmyu6VVFJLQGV4xgl3yj0HMRnQEt1SonnpBNomnfSF460RjH\njZ3XcIkAuwMQyEz4BxXIa1bb8j63wb7zEjyjAGTzas9emtjh7TQB0YgRPoCn\nkvxPelhzLBsUSL7z/HM8ywt8LouIXhqK1Njyj634z262eXeBymoq7RfChhkJ\nQuUorx22zQrkro1EtuLXaOANFxn7hvY180kZKnGGWsC/SH+cODn0cuVcdfs3\npXA9ub2soKI3HComiGYt9a5V6YgOwMzJz3QroT0jfFKDabh8aATU8FCrLwUb\nlKcoJFN9uOmSpBqfLwFwrM6aimQ81qmf+O7pDDBv+q/+ZWI1KH6aMXIqB4Cz\ncKtbW7Jqu6NCfzw7VThnVIljozEdciKDDO5Ws2clzfDomEkP0furKJF2NjqJ\njhhS37XLSlDd52AFVYVjB4xVjX/E+L85Z3tEaU9ZTPrLsaZGicg8vX8S0qx1\n/pa9\r\n=6tXZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGFmTXwDqMR1IhhZiePt5MKLf3Qb1CNmsP/L+DT5HS7pAiEAwNSKo+BYWnaTKye6D1thKmlC92de2mPlXSgoecMLTZA="}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "2.1.1": {"name": "ajv-formats", "version": "2.1.1", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.3.2", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==", "shasum": "6e669400659eb74973bbf2e33327180a0996b520", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz", "fileCount": 15, "unpackedSize": 52227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2jKWCRA9TVsSAnZWagAAyu4P/AxuqlnEShCyP+ukQBKX\nL0cHq5/18T4izpHsZHE/vu2sf3JdeZzeFLfoL5JjZmlyumJsbXgwdepTH7HX\nNGHfRsUwHr+D48BiXd/Y6wZlC2kTpa/+UJfPZullkRVtGop/WlMkPc3dnRU3\novZ4qAz7w04mHdUSqkYwGT3ajLNHndUjivZ9ZAh8mCh+vjQFuA7zAvpFDz0K\nZLAKumx/nvf2SpoDNdofzjvL7pjdpaJC+yQDvggY/TII/XkrpDWNqfh6I8Xd\nx4v6ho/dnn4ihlI1SNRbT6dZMK3wgyrWbR19z+Qj2ruP3Vvck4usSdUgMIzb\nKPA6T61x9tgP5vxNfStRlUm24xjZhkfOpe/OaioN/XEnv1DpAyuU28gk2mPt\nHMD7T3p31JLTMZg1BN+TToau4vYsfjWW6UXk7IstQf5attcLp2VL6ZAUwYC7\nWSFWCpavok2mO3cOvkSyNJKGripNquL8N9W1BcQ/aXr3mK9OA0Vc8MrFtdbs\nq7tr7UCOZobDu8tt5KGBPVHiDdjpb/X6r7GTIirdaikktx5DW/dfMvzGqqVq\nZPm4Pe69QdsR7kq62/h1OnmKFqBC3AKXhix6QTCqbmv0iI42DE0BSsjqs6L9\nuTx3tNo4BrMsimcBekRx1Vm+0YP7Jx7RzHPK0JYBYuANdvwUOSzNOUFqKS2N\nNm8/\r\n=nmgl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAiQ+wpxq7SGVGoZOo0Nq8utdTDArin0rYRIkc5QOzfGAiBwW/bjoF2mAg1rps4zt9jh3F66eaMSaMP62JTw0OOZqg=="}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "3.0.0-rc.0": {"name": "ajv-formats", "version": "3.0.0-rc.0", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.3.2", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-eyqaGv4OE7RMgW2GNujqwJZWFjOT4z0tQZTVnYiWtSDh9TFwD8CIsJ6ta065IblpZXcV3wFuy8y2gKFb1d0uPw==", "shasum": "d345d205242072ee8877e3a19f100f41a2106e5f", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-3.0.0-rc.0.tgz", "fileCount": 15, "unpackedSize": 56824, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS2T8HRQsnMVTa/diYdWyEo213VFJDnutZ/szuQiqBxQIhAJ5Mk3nRHFlq9xB7dDXjG9WC1TATZGlOV562mGqbCI+v"}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "3.0.0": {"name": "ajv-formats", "version": "3.0.0", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.3.2", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-Wn61FZ85ioKFYeODEt7tm/D3ojcNc7CC15heA1REpIzmgpVZGq1M/0VkEe5Ig8bRFbINPCDg7mlUNFQqi/homQ==", "shasum": "b72b6853cbf91e78c7f5b4c8ccd69aad6fd3f1bd", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-3.0.0.tgz", "fileCount": 6, "unpackedSize": 26930, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbb+60NvzvKj3mEZVpCdGsynG1iUbAwXFFuQ5YLr9wAwIgQ5s7yaYs+wMbQXkbGjQT6SsenQTt+RAPGTy5qWBUt48="}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "3.0.1": {"name": "ajv-formats", "version": "3.0.1", "dependencies": {"ajv": "^8.0.0"}, "devDependencies": {"@ajv-validator/config": "^0.3.0", "@types/jest": "^26.0.5", "@types/node": "^14.10.1", "@typescript-eslint/eslint-plugin": "^3.7.0", "@typescript-eslint/parser": "^3.7.0", "ajv": "^8.0.0", "eslint": "^7.5.0", "eslint-config-prettier": "^6.11.0", "husky": "^4.2.5", "jest": "^26.1.0", "json-schema-test": "^2.0.0", "lint-staged": "^10.2.11", "prettier": "^2.3.2", "ts-jest": "^26.1.3", "typescript": "^4.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==", "shasum": "3d5dc762bca17679c3c2ea7e90ad6b7532309578", "tarball": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-3.0.1.tgz", "fileCount": 15, "unpackedSize": 56763, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNjmZ9iM4dJkMswBBQvly7urSTFzJJBcAEdkDXyHjTYQIgIy06JeUFo1/lGNnbGrOf2WiAlyZEYe3K+SBvP3QUq/s="}]}, "peerDependenciesMeta": {"ajv": {"optional": true}}}}, "modified": "2024-03-30T11:30:26.893Z"}