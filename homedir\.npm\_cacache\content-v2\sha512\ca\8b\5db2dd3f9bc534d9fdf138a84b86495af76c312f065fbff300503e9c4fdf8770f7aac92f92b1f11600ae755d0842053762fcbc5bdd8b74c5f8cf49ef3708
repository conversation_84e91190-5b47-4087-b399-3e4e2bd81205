{"_id": "call-bind-apply-helpers", "_rev": "2-56963b9eaf7a0f6cfb8657f38a8b4b46", "name": "call-bind-apply-helpers", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "call-bind-apply-helpers", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "call-bind-apply-helpers@1.0.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/call-bind-apply-helpers#readme", "bugs": {"url": "https://github.com/ljharb/call-bind-apply-helpers/issues"}, "dist": {"shasum": "33127b42608972f76812a501d69db5d8ce404979", "tarball": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.0.tgz", "fileCount": 21, "integrity": "sha512-CCKAP2tkPau7D3GE8+V8R6sQubA9R5foIzGp+85EXCVSCivuxBNAWqcpn72PKYiIcqoViv/kcUDpaEIMBVi1lQ==", "signatures": [{"sig": "MEQCIHW7XfuHryTs2kNK+/GrAusDSttnxXzy+no1dG9KBiKAAiB8tBUgklhwfVMKrAPde2dlg/AhkIqh+K386M5Ic17Wvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13840}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./applyBind": "./applyBind.js", "./actualApply": "./actualApply.js", "./functionCall": "./functionCall.js", "./package.json": "./package.json", "./reflectApply": "./reflectApply.js", "./functionApply": "./functionApply.js"}, "gitHead": "d90414f6d1e0e6b95153a7d564f8d793c447d207", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/ljharb/call-bind-apply-helpers.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Helper functions around Function call/apply/bind, for use in `call-bind`", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/function-bind": "^1.1.10", "@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/call-bind-apply-helpers_1.0.0_1733445252434_0.****************", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "call-bind-apply-helpers", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "call-bind-apply-helpers@1.0.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/ljharb/call-bind-apply-helpers#readme", "bugs": {"url": "https://github.com/ljharb/call-bind-apply-helpers/issues"}, "dist": {"shasum": "32e5892e6361b29b0b545ba6f7763378daca2840", "tarball": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.1.tgz", "fileCount": 21, "integrity": "sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==", "signatures": [{"sig": "MEUCIHwPEWbTRvp20B4Sr47T95aJCDYbhRkpr46kXbWVYCVHAiEAsKrisdtgDsJoWqsHHKVWnVqpGu9V2Yv1yhQZSA920tM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14495}, "main": "index.js", "types": "./index.d.ts", "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./applyBind": "./applyBind.js", "./actualApply": "./actualApply.js", "./functionCall": "./functionCall.js", "./package.json": "./package.json", "./reflectApply": "./reflectApply.js", "./functionApply": "./functionApply.js"}, "gitHead": "53cbae48ce58b5d8523d8582711d1be0f12b64c0", "scripts": {"lint": "eslint --ext=.js,.mjs .", "test": "npm run tests-only", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "pretest": "npm run lint", "version": "auto-changelog && git add CHANGELOG.md", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "nyc tape 'test/**/*.js'", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublishOnly": "safe-publish-latest"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "testling": {"files": "test/index.js"}, "repository": {"url": "git+https://github.com/ljharb/call-bind-apply-helpers.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Helper functions around Function call/apply/bind, for use in `call-bind`", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "publishConfig": {"ignore": [".github/workflows"]}, "_hasShrinkwrap": false, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "hideCredit": true, "unreleased": false, "commitLimit": false, "backfillLimit": false}, "devDependencies": {"nyc": "^10.3.2", "tape": "^5.9.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "encoding": "^0.1.13", "for-each": "^0.3.3", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "typescript": "next", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "object-inspect": "^1.13.3", "@types/for-each": "^0.3.3", "has-strict-mode": "^1.0.1", "@ljharb/tsconfig": "^0.2.2", "es-value-fixtures": "^1.5.0", "safe-publish-latest": "^2.0.0", "@types/function-bind": "^1.1.10", "@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@types/object-inspect": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/call-bind-apply-helpers_1.0.1_1733726673175_0.2140833140350913", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.2": {"name": "call-bind-apply-helpers", "version": "1.0.2", "description": "Helper functions around Function call/apply/bind, for use in `call-bind`", "main": "index.js", "exports": {".": "./index.js", "./actualApply": "./actualApply.js", "./applyBind": "./applyBind.js", "./functionApply": "./functionApply.js", "./functionCall": "./functionCall.js", "./reflectApply": "./reflectApply.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>=10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bind-apply-helpers.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/call-bind-apply-helpers/issues"}, "homepage": "https://github.com/ljharb/call-bind-apply-helpers#readme", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/for-each": "^0.3.3", "@types/function-bind": "^1.1.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}, "_id": "call-bind-apply-helpers@1.0.2", "gitHead": "7068296a67b98424d5299606599d2f8117ded880", "types": "./index.d.ts", "_nodeVersion": "23.7.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "shasum": "4b5428c222be985d79c3d82657479dbe0b59b2d6", "tarball": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "fileCount": 21, "unpackedSize": 15952, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG6fAxtBxkVLqeMj4hMX04Vep0JNjOVl6JP2xrnsL50CAiEA9XubTWvgXSyVHKqfH6C/PHeZ06C6S+wNdxbVqOFCR6I="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/call-bind-apply-helpers_1.0.2_1739388296756_0.5004641025912346"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-12-06T00:34:12.433Z", "modified": "2025-02-12T19:24:57.167Z", "1.0.0": "2024-12-06T00:34:12.669Z", "1.0.1": "2024-12-09T06:44:33.335Z", "1.0.2": "2025-02-12T19:24:56.950Z"}, "bugs": {"url": "https://github.com/ljharb/call-bind-apply-helpers/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/ljharb/call-bind-apply-helpers#readme", "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bind-apply-helpers.git"}, "description": "Helper functions around Function call/apply/bind, for use in `call-bind`", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# call-bind-apply-helpers <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][deps-svg]][deps-url]\n[![dev dependency status][dev-deps-svg]][dev-deps-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nHelper functions around Function call/apply/bind, for use in `call-bind`.\n\nThe only packages that should likely ever use this package directly are `call-bind` and `get-intrinsic`.\nPlease use `call-bind` unless you have a very good reason not to.\n\n## Getting started\n\n```sh\nnpm install --save call-bind-apply-helpers\n```\n\n## Usage/Examples\n\n```js\nconst assert = require('assert');\nconst callBindBasic = require('call-bind-apply-helpers');\n\nfunction f(a, b) {\n\tassert.equal(this, 1);\n\tassert.equal(a, 2);\n\tassert.equal(b, 3);\n\tassert.equal(arguments.length, 2);\n}\n\nconst fBound = callBindBasic([f, 1]);\n\ndelete Function.prototype.call;\ndelete Function.prototype.bind;\n\nfBound(2, 3);\n```\n\n## Tests\n\nClone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/call-bind-apply-helpers\n[npm-version-svg]: https://versionbadg.es/ljharb/call-bind-apply-helpers.svg\n[deps-svg]: https://david-dm.org/ljharb/call-bind-apply-helpers.svg\n[deps-url]: https://david-dm.org/ljharb/call-bind-apply-helpers\n[dev-deps-svg]: https://david-dm.org/ljharb/call-bind-apply-helpers/dev-status.svg\n[dev-deps-url]: https://david-dm.org/ljharb/call-bind-apply-helpers#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/call-bind-apply-helpers.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/call-bind-apply-helpers.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/call-bind-apply-helpers.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=call-bind-apply-helpers\n[codecov-image]: https://codecov.io/gh/ljharb/call-bind-apply-helpers/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/ljharb/call-bind-apply-helpers/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/call-bind-apply-helpers\n[actions-url]: https://github.com/ljharb/call-bind-apply-helpers/actions\n", "readmeFilename": "README.md"}