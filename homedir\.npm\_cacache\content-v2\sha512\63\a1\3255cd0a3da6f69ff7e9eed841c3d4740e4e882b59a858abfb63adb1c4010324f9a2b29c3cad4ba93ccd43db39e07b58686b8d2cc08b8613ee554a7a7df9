{"_id": "esrecurse", "_rev": "29-4f68daa051080f4b9478a6d9efe71454", "name": "esrecurse", "description": "ECMAScript AST recursive visitor", "dist-tags": {"latest": "4.3.0"}, "versions": {"1.0.0": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "1.0.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": ">=1.8.0"}, "devDependencies": {"chai": "~1.10.0", "coffee-script": "~1.8.0", "esprima": "~1.2.2", "gulp": "~3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "~2.0.0", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "4d1783c0a5231197c358874560ed3efb013e90a6", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@1.0.0", "_shasum": "ca7a8b02a5d7ac5ed9ef668cc77527a4b644118f", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "ca7a8b02a5d7ac5ed9ef668cc77527a4b644118f", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-1.0.0.tgz", "integrity": "sha512-ejwV7/otReex+5ktMmdMXtVIifEsmmjQgYWhE6rqeqcP9H9GunNTXqHhgRYxG7HQ8ZDJoJ4Iw38MT93SA/JB3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGNPVo4hfe5zmCKpWqr6ZNzk+X39iBKaWbUwmTvuWnbFAiEAnTe/zbmgYssqbZJBIEadS0QAdSfHiAjIGPyymYIPjgQ="}]}, "directories": {}}, "1.0.1": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "1.0.1", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": ">=1.8.0"}, "devDependencies": {"chai": "~1.10.0", "coffee-script": "~1.8.0", "esprima": "~1.2.2", "gulp": "~3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "~2.0.0", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "8e7f6465802ab9a7537df7271fef0643ca29246d", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@1.0.1", "_shasum": "9de96fd671db5bc0b336a3a56ae7fc319c17a393", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "9de96fd671db5bc0b336a3a56ae7fc319c17a393", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-1.0.1.tgz", "integrity": "sha512-jUXETA+Q1239apku2bd97zHIOftxQxF7a6Rk2mjGi6OUmbYWApUMM12QIythwaDC2dgJY1r2qzdOLuRYOR6PYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFzUanvTWByUju+0X4tqrUqhzNi0j2t32Z5N+IgprKE9AiEA/IM2JSEc1pce+UQF9qGZEozzNI7ASnC2T1CR7P3c6Mc="}]}, "directories": {}}, "1.1.0": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "1.1.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": ">=1.8.0"}, "devDependencies": {"chai": "~1.10.0", "coffee-script": "~1.8.0", "esprima": "~1.2.2", "gulp": "~3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "~2.0.0", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "19f8e36903abf54d20293f9a9df4109f9ff2b6d3", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@1.1.0", "_shasum": "98c42656826d540837c2e7ba9f42f929a2490d05", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "98c42656826d540837c2e7ba9f42f929a2490d05", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-1.1.0.tgz", "integrity": "sha512-YtzrpifNbRVv+6JDPCcxEGe0ztVf83uqnLUeKF17KhWARtPZsqUkU7Ri4brGv5FlvBQKJcX4KFEh3WZTZ1JAcA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHM7twOZD0cOeC3k/XOswgCrTj4tacoGBeJ+y2SIej1AIhAP+j0mz/gkfEtoHmfW/J+Q2xRRWMIUnFLb9bGxNS3HPZ"}]}, "directories": {}}, "1.2.0": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "1.2.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": ">=1.9.0"}, "devDependencies": {"chai": "~1.10.0", "coffee-script": "~1.8.0", "esprima": "~1.2.2", "gulp": "~3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "~2.0.0", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "9b363cee6d6b6b29b5236f4b98066ba0735fe9e4", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@1.2.0", "_shasum": "25e3b3ab76ad8a1da2d38e9393fd76b8456a706f", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "25e3b3ab76ad8a1da2d38e9393fd76b8456a706f", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-1.2.0.tgz", "integrity": "sha512-a9refhSey+zsU1vwVOlqojtVQl2yJN9+Bguqz8pKIf8iRf4AvmlqkWJhUZNYLNLikUHeHINz1Prf7yNeG3Bfvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICk4EsvB7Sz4L87zzs0cIJhj/Khqy3yCiMFj88hXdXGsAiBoAuOZff29rrsZMqClRYFccyrt+bmA21n3AFAE9Sl/vw=="}]}, "directories": {}}, "2.0.0": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "2.0.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "~2.0.0"}, "devDependencies": {"chai": "^2.1.1", "coffee-script": "^1.9.1", "esprima": "^2.1.0", "gulp": "~3.8.10", "gulp-bump": "^0.2.2", "gulp-eslint": "^0.6.0", "gulp-filter": "^2.0.2", "gulp-git": "^1.1.0", "gulp-mocha": "~2.0.0", "gulp-tag-version": "^1.2.1", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "75a56079e92c5d53596f69b71da855845444bc4c", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@2.0.0", "_shasum": "a85c6b3d537284f4a3c81ad7dd82263065f07450", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "a85c6b3d537284f4a3c81ad7dd82263065f07450", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-2.0.0.tgz", "integrity": "sha512-WztQuwEbPDy3PgadZFQol6BLM75fRNWgZJZjo2dKAcQW9fiejzm1LTeYP5PE+tDS0exWOlDnGGyE5Tk31uGsmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNTn+IJJiozzr7a4b3+dnrKO/4z1DRkrJO6I0Es8TSXQIgEMsbUEyf1KSdXcC5Xapn4V6WAe2iNI78Bim4tn8ms/Q="}]}, "directories": {}}, "3.0.0": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "3.0.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "~3.0.0"}, "devDependencies": {"chai": "^2.1.1", "coffee-script": "^1.9.1", "esprima": "^2.1.0", "gulp": "~3.8.10", "gulp-bump": "^0.2.2", "gulp-eslint": "^0.6.0", "gulp-filter": "^2.0.2", "gulp-git": "^1.1.0", "gulp-mocha": "~2.0.0", "gulp-tag-version": "^1.2.1", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "c2313a58354ee0b9707b33ab621fe8c84ae20bab", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@3.0.0", "_shasum": "47b2691da6d9db521beb4285130a5dc41c9e6795", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "47b2691da6d9db521beb4285130a5dc41c9e6795", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-3.0.0.tgz", "integrity": "sha512-6AhqIf3OjvqF7tAF3tkMvzH5Q9vdxSu96LXmnNdxF5nEyrC7XtjngocPXnPBgb2lPw+GY2Kc7yTd4PyKH3X3Ug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPudMXf8WtcjsVu6m9kNsHAdDOo49h0nQ21KAI7W336AiBFGvlySLZ+dI0SvSx3JihfQVCM2SKZaQ1190P8hcc3KQ=="}]}, "directories": {}}, "3.1.0": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "3.1.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "~3.1.0"}, "devDependencies": {"chai": "^2.1.1", "coffee-script": "^1.9.1", "esprima": "^2.1.0", "gulp": "~3.8.10", "gulp-bump": "^0.2.2", "gulp-eslint": "^0.6.0", "gulp-filter": "^2.0.2", "gulp-git": "^1.1.0", "gulp-mocha": "~2.0.0", "gulp-tag-version": "^1.2.1", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "34dfff23a1ac8c3306041a549fe9b24a32231f24", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@3.1.0", "_shasum": "9dcaed14d541d4c2b7ed43d6d71c640930a11fcd", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "9dcaed14d541d4c2b7ed43d6d71c640930a11fcd", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-3.1.0.tgz", "integrity": "sha512-a2MZFQ6ZIamxBJ11LpYz3kutlmM1CqEjTIHftAJWVMfGmfpDjBsMdqSSX6BSAqCAuyvEW0OyOHzjbnaurnHoAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIElyY5aFtykTuWQfajD8Sa+oSeZgNBiKQzymI+q7fWqXAiEA0+ZDDZKJ6Hka/BsBfHeq4ffDF99aFrqAhfZEqPD1m0k="}]}, "directories": {}}, "3.1.1": {"name": "esrecurse", "description": "ECMAScript scope analyzer", "homepage": "http://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "3.1.1", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "~3.1.0"}, "devDependencies": {"chai": "^2.1.1", "coffee-script": "^1.9.1", "esprima": "^2.1.0", "gulp": "~3.8.10", "gulp-bump": "^0.2.2", "gulp-eslint": "^0.6.0", "gulp-filter": "^2.0.2", "gulp-git": "^1.1.0", "gulp-mocha": "~2.0.0", "gulp-tag-version": "^1.2.1", "jsdoc": "~3.3.0-alpha10", "minimist": "^1.1.0"}, "licenses": [{"type": "BSD", "url": "http://github.com/estools/esrecurse/raw/master/LICENSE.BSD"}], "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "600a8aac5e7b313875a873134fd110b47a76fc77", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@3.1.1", "_shasum": "8feb963699d4d1b2d65a576cd4b1296672a0f0e9", "_from": ".", "_npmVersion": "2.0.0-alpha-5", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "8feb963699d4d1b2d65a576cd4b1296672a0f0e9", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-3.1.1.tgz", "integrity": "sha512-AEB4F0qUq5ae0S+bz4QcfewjLJ2CfWTQBIrrsE1iB6Fxv3oeSKEItiJYLCTcaChty/ddBTY/Lg5Y9YoP+hTdnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF39K0CfGHFtygCNNnl7Ql4LaDiINGvBiunIVro140L1AiEAipKHiT2Mykwbxhh/tBj7NDLKvKty4tLvtT3JISJP2KM="}]}, "directories": {}}, "4.0.0": {"name": "esrecurse", "description": "ECMAScript AST recursive visitor", "homepage": "https://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "4.0.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "~4.1.0", "object-assign": "^4.0.1"}, "devDependencies": {"chai": "^3.3.0", "coffee-script": "^1.9.1", "esprima": "^2.1.0", "gulp": "^3.9.0", "gulp-bump": "^1.0.0", "gulp-eslint": "^1.0.0", "gulp-filter": "^3.0.1", "gulp-git": "^1.1.0", "gulp-mocha": "^2.1.3", "gulp-tag-version": "^1.2.1", "jsdoc": "^3.3.0-alpha10", "minimist": "^1.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "fc4b0d9b1e9ce493761e97af9a1afb5afd489f87", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@4.0.0", "_shasum": "c7b50295f9af5ff9a0073d139eb011476128e9e6", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "c7b50295f9af5ff9a0073d139eb011476128e9e6", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.0.0.tgz", "integrity": "sha512-SuMC57mdfGkPeC4IL6QEj0K9HeFnLwLMfCUMM4AQK+YJFySnYo8iQEZGBRXUXm4p0NYoWHKAkYGZwBmfleRdzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHD1a09jDfudBNM0xFrDJ/Qpp0SXSGPV/XDOJwjy7y55AiEArG54VqqvCKsRdr/ud1La5d4i0H2Qm/W9UrOPzgx/ogY="}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/esrecurse-4.0.0.tgz_1454512903651_0.6128629888407886"}, "directories": {}}, "4.1.0": {"name": "esrecurse", "description": "ECMAScript AST recursive visitor", "homepage": "https://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "4.1.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nzakas", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "~4.1.0", "object-assign": "^4.0.1"}, "devDependencies": {"chai": "^3.3.0", "coffee-script": "^1.9.1", "esprima": "^2.1.0", "gulp": "^3.9.0", "gulp-bump": "^1.0.0", "gulp-eslint": "^1.0.0", "gulp-filter": "^3.0.1", "gulp-git": "^1.1.0", "gulp-mocha": "^2.1.3", "gulp-tag-version": "^1.2.1", "jsdoc": "^3.3.0-alpha10", "minimist": "^1.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "gitHead": "63a34714834bd7ad2063054bd4abb24fb82ca667", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@4.1.0", "_shasum": "4713b6536adf7f2ac4f327d559e7756bff648220", "_from": ".", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.9", "_npmUser": {"name": "nzakas", "email": "<EMAIL>"}, "dist": {"shasum": "4713b6536adf7f2ac4f327d559e7756bff648220", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.1.0.tgz", "integrity": "sha512-4pWjwT+t5yO1v2/nV29A6IUpV7I78jR6mmZhhM/65pPV3ZZZQ5f1j354Mt5XzhDH0bqB3oDfF0BA2RPOY/NxBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPVzFGO6Lfl1/9HbUXjmdCmzkJzbsulLwU+Iljlwlq0gIhANEcIbYmTc0wDldPejBIJWgmGWxP/cil1wuyM/Wf+4xm"}]}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/esrecurse-4.1.0.tgz_1457712782215_0.15950557170435786"}, "directories": {}}, "4.2.0": {"name": "esrecurse", "description": "ECMAScript AST recursive visitor", "homepage": "https://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "4.2.0", "engines": {"node": ">=0.10.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nzakas", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "^4.1.0", "object-assign": "^4.0.1"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-eslint": "^7.2.3", "babel-preset-es2015": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.2", "esprima": "^4.0.0", "gulp": "^3.9.0", "gulp-bump": "^2.7.0", "gulp-eslint": "^4.0.0", "gulp-filter": "^5.0.0", "gulp-git": "^2.4.1", "gulp-mocha": "^4.3.1", "gulp-tag-version": "^1.2.1", "jsdoc": "^3.3.0-alpha10", "minimist": "^1.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "babel": {"presets": ["es2015"]}, "gitHead": "dabe93e7bb8a92f52b8284b358cd30e230920c96", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@4.2.0", "_shasum": "fa9568d98d3823f9a41d91e902dcab9ea6e5b163", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "constellation", "email": "<EMAIL>"}, "dist": {"shasum": "fa9568d98d3823f9a41d91e902dcab9ea6e5b163", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.0.tgz", "integrity": "sha512-TLXkx8hhh1f3PBJQAV24x0JJpOAWvGW/n2KyIRuGOpt5dcl9fuRLY8Lv3zB2psFfqJBT2ZN0Ss4aNSTf9lLqwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxbJ3Sn9t3aNDFKlug39YxRaDuc/SOHikPZacyLH3HlwIgeqsDXh98q4SDfRn48tRSK7Jif5yHxFlUGgNhWKNA9Dc="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esrecurse-4.2.0.tgz_1497923921530_0.31889034481719136"}, "directories": {}}, "4.2.1": {"name": "esrecurse", "description": "ECMAScript AST recursive visitor", "homepage": "https://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "4.2.1", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nzakas", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "^4.1.0"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-eslint": "^7.2.3", "babel-preset-es2015": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.2", "esprima": "^4.0.0", "gulp": "^3.9.0", "gulp-bump": "^2.7.0", "gulp-eslint": "^4.0.0", "gulp-filter": "^5.0.0", "gulp-git": "^2.4.1", "gulp-mocha": "^4.3.1", "gulp-tag-version": "^1.2.1", "jsdoc": "^3.3.0-alpha10", "minimist": "^1.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "babel": {"presets": ["es2015"]}, "gitHead": "c6e650de18ed2bb313025bfba97622af10064478", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@4.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.7.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-64RBB++fIOAXPw3P9cy89qfMlvZEXZkqqJkjqqXIvzP5ezRZjW+lPWjw35UX/3EhUPFYbg5ER4JYgDw4007/DQ==", "shasum": "007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.2.1.tgz", "fileCount": 5, "unpackedSize": 13527, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDghkRckN+8cL8C5jfpNeD/uw6lesxcMObh4lLHzWUYRAIhAODqLC0KTxVJx6+9wi8l89s1tkXN9e9WMqVc9u84FLob"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esrecurse_4.2.1_1519660054685_0.5873947529967192"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "esrecurse", "description": "ECMAScript AST recursive visitor", "homepage": "https://github.com/estools/esrecurse", "main": "esrecurse.js", "version": "4.3.0", "engines": {"node": ">=4.0"}, "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nzakas", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "dependencies": {"estraverse": "^5.2.0"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-eslint": "^7.2.3", "babel-preset-es2015": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.2", "esprima": "^4.0.0", "gulp": "^3.9.0", "gulp-bump": "^2.7.0", "gulp-eslint": "^4.0.0", "gulp-filter": "^5.0.0", "gulp-git": "^2.4.1", "gulp-mocha": "^4.3.1", "gulp-tag-version": "^1.2.1", "jsdoc": "^3.3.0-alpha10", "minimist": "^1.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}, "babel": {"presets": ["es2015"]}, "gitHead": "8ceb63797813d9ac69572a671b95cf58ffdf35b6", "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "_id": "esrecurse@4.3.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "shasum": "7ad7964d679abb28bee72cec63758b1c5d2c9921", "tarball": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "fileCount": 5, "unpackedSize": 13527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTT+5CRA9TVsSAnZWagAAqZkP/24w7D3ZHMmD+dW7rCVS\n8i7YBm2+jg6PfRb67h3kwcgRo9y26hc8BBhcNc5eueK8hoKSjy6bMbqo9rnY\nMLObuQWscdq+/1AWq6HCETp0vxqLglwyyWTWD8e8FBqiNx54XEYGm7S1lmOZ\nYswD4E4dW99PRmer39hKUeg1N5a1xNhT3TyxUrRrml7/4Zt1rZHg3cDbkvfI\n4WU8S340Uor9C2erDOpqBmcSKxjzbqbHsVZImMGhPyzHghxldCQQQ0TpDrZS\nCZL5F7TZysCE43YISxIM108f5BbTLAVzPiOaUNxzVynimqHPXWUHVGAINBF2\nAmWL1aNAhqJxxWwiPrwfWMsB6lC+DxewR0wlOH7PdsLDJORPygWDGFuNf78U\ngkBlNAWPslkhJ5XjS8zNMR5DaheENmJiMzZjvR+xSoKERlDzl7jFkiG0acJ1\nlGvHz3RtegI1X1Cyd81rejOO5mgrAUKHkbFScrf7dt1d3qmgd7cWlnmreLvf\ngZEHzcCoCD+dn9EH3VGHgU07Pi4HpC97I0m3MzNwK/bYOJvJqQ2hwkni6mrm\nqTXpAyv6Kdb8wOdXErPo8JmPj/jlH4/doCvteLilDF4ZCak1R21vw3+YHW8Y\nfpI4F+hEWnxtNSgQS8q8LDCWoO6Er98h/CqEmPQWqgz6rMj5uK25lFvdmuqh\nNiew\r\n=cq0F\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFhsVudxfufLwMI80sLozcVw5zKZf4ij0r95LyxjxilrAiBMN0PNPCNnzdzJtDZ0AmikWWw/ICqrK8oul9YKj1Cujg=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esrecurse_4.3.0_1598898104693_0.7592543911776826"}, "_hasShrinkwrap": false}}, "readme": "### Esrecurse [![Build Status](https://travis-ci.org/estools/esrecurse.svg?branch=master)](https://travis-ci.org/estools/esrecurse)\n\nEsrecurse ([esrecurse](https://github.com/estools/esrecurse)) is\n[ECMAScript](https://www.ecma-international.org/publications/standards/Ecma-262.htm)\nrecursive traversing functionality.\n\n### Example Usage\n\nThe following code will output all variables declared at the root of a file.\n\n```javascript\nesrecurse.visit(ast, {\n    XXXStatement: function (node) {\n        this.visit(node.left);\n        // do something...\n        this.visit(node.right);\n    }\n});\n```\n\nWe can use `Visitor` instance.\n\n```javascript\nvar visitor = new esrecurse.Visitor({\n    XXXStatement: function (node) {\n        this.visit(node.left);\n        // do something...\n        this.visit(node.right);\n    }\n});\n\nvisitor.visit(ast);\n```\n\nWe can inherit `Visitor` instance easily.\n\n```javascript\nclass Derived extends esrecurse.Visitor {\n    constructor()\n    {\n        super(null);\n    }\n\n    XXXStatement(node) {\n    }\n}\n```\n\n```javascript\nfunction DerivedVisitor() {\n    esrecurse.Visitor.call(/* this for constructor */  this  /* visitor object automatically becomes this. */);\n}\nutil.inherits(DerivedVisitor, esrecurse.Visitor);\nDerivedVisitor.prototype.XXXStatement = function (node) {\n    this.visit(node.left);\n    // do something...\n    this.visit(node.right);\n};\n```\n\nAnd you can invoke default visiting operation inside custom visit operation.\n\n```javascript\nfunction DerivedVisitor() {\n    esrecurse.Visitor.call(/* this for constructor */  this  /* visitor object automatically becomes this. */);\n}\nutil.inherits(DerivedVisitor, esrecurse.Visitor);\nDerivedVisitor.prototype.XXXStatement = function (node) {\n    // do something...\n    this.visitChildren(node);\n};\n```\n\nThe `childVisitorKeys` option does customize the behaviour of `this.visitChildren(node)`.\nWe can use user-defined node types.\n\n```javascript\n// This tree contains a user-defined `TestExpression` node.\nvar tree = {\n    type: 'TestExpression',\n\n    // This 'argument' is the property containing the other **node**.\n    argument: {\n        type: 'Literal',\n        value: 20\n    },\n\n    // This 'extended' is the property not containing the other **node**.\n    extended: true\n};\nesrecurse.visit(\n    ast,\n    {\n        Literal: function (node) {\n            // do something...\n        }\n    },\n    {\n        // Extending the existing traversing rules.\n        childVisitorKeys: {\n            // TargetNodeName: [ 'keys', 'containing', 'the', 'other', '**node**' ]\n            TestExpression: ['argument']\n        }\n    }\n);\n```\n\nWe can use the `fallback` option as well.\nIf the `fallback` option is `\"iteration\"`, `esrecurse` would visit all enumerable properties of unknown nodes.\nPlease note circular references cause the stack overflow. AST might have circular references in additional properties for some purpose (e.g. `node.parent`).\n\n```javascript\nesrecurse.visit(\n    ast,\n    {\n        Literal: function (node) {\n            // do something...\n        }\n    },\n    {\n        fallback: 'iteration'\n    }\n);\n```\n\nIf the `fallback` option is a function, `esrecurse` calls this function to determine the enumerable properties of unknown nodes.\nPlease note circular references cause the stack overflow. AST might have circular references in additional properties for some purpose (e.g. `node.parent`).\n\n```javascript\nesrecurse.visit(\n    ast,\n    {\n        Literal: function (node) {\n            // do something...\n        }\n    },\n    {\n        fallback: function (node) {\n            return Object.keys(node).filter(function(key) {\n                return key !== 'argument'\n            });\n        }\n    }\n);\n```\n\n### License\n\nCopyright (C) 2014 [Yusuke Suzuki](https://github.com/Constellation)\n (twitter: [@Constellation](https://twitter.com/Constellation)) and other contributors.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n  * Redistributions of source code must retain the above copyright\n    notice, this list of conditions and the following disclaimer.\n\n  * Redistributions in binary form must reproduce the above copyright\n    notice, this list of conditions and the following disclaimer in the\n    documentation and/or other materials provided with the distribution.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\nAND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\nIMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\nARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\nDIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\nLOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\nON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\nTHIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "maintainers": [{"name": "constellation", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nzakas", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:31:47.428Z", "created": "2014-12-02T11:53:32.918Z", "1.0.0": "2014-12-02T11:53:32.918Z", "1.0.1": "2014-12-02T11:56:26.359Z", "1.1.0": "2014-12-02T15:19:25.669Z", "1.2.0": "2014-12-08T03:32:22.850Z", "2.0.0": "2015-03-09T19:29:08.259Z", "3.0.0": "2015-03-14T18:02:20.304Z", "3.1.0": "2015-03-14T19:50:16.981Z", "3.1.1": "2015-03-14T19:55:45.483Z", "4.0.0": "2016-02-03T15:21:46.466Z", "4.1.0": "2016-03-11T16:13:02.631Z", "4.2.0": "2017-06-20T01:58:41.693Z", "4.2.1": "2018-02-26T15:47:34.732Z", "4.3.0": "2020-08-31T18:21:44.794Z"}, "homepage": "https://github.com/estools/esrecurse", "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "readmeFilename": "README.md", "license": "BSD-2-<PERSON><PERSON>", "users": {"kaizendad": true, "larrychen": true, "flumpus-dev": true}}