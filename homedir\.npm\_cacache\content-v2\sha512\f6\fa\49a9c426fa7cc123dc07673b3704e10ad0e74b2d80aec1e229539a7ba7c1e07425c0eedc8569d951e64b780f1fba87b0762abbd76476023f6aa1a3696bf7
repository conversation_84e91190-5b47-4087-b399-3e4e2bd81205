{"_id": "@rollup/rollup-android-arm-eabi", "_rev": "152-6544298a893b176daaf6983a3587dd67", "name": "@rollup/rollup-android-arm-eabi", "dist-tags": {"beta": "4.33.0-0", "latest": "4.44.0"}, "versions": {"4.0.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-0", "keywords": ["modules", "bundler", "bundling", "es6", "optimizer"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ff13bcfacdc53d3b1cce6a0787dc35e095794071", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-0.tgz", "fileCount": 2, "integrity": "sha512-PX0lyfZIqr8cQxVze6KN/77gaZmeWpLfECJRqlKv98hQnNnXUvmBeFxt9XGwTdy7ehSVguSP9VTl5KRoBWktsw==", "signatures": [{"sig": "MEUCIAeVTN1jtQn1xQVCCVmEwGADaNO8VWOB+Bgq1315uZkpAiEA2qKKoNJeKBsHSJ4uY/bA3Gth0jn6RfjXdDHqskx446g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 735}, "main": "native/rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "580d17223962a0a359da88420001bbbc738e633a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Next-generation ES module bundler", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-0_1690831077592_0.7048309735644593", "host": "s3://npm-registry-packages"}}, "4.0.0-1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "877a6c6b59eda2aaece842336690242057156e39", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-1.tgz", "fileCount": 3, "integrity": "sha512-Cka+iAdredOrVVGif5NBKCNTKg9zC5pqRU2kmH7pW4aHRN7QX11RAsZJ/oY/FYpDCv+oFmz6MENVF6D+O1ussQ==", "signatures": [{"sig": "MEUCIQCeNqlTKwA+1uiWzbt7dC6F3g5NLnhuptirJaEoPqq+IAIgNMEP6uE/3Ll5DUWDaZipBdI6fS2KO6kvElM96sPtIzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1479276}, "main": "rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d5b6ec3f77c860c048e2830353f5af4593ffaf20", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-1_1690865342024_0.9154011787671796", "host": "s3://npm-registry-packages"}}, "4.0.0-2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "25fe8cf867da2a15fb59f23e015120485f359a0d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-2.tgz", "fileCount": 3, "integrity": "sha512-665RLsLbc0j0Xza35FN8EuNVR9AuI6ZFwX4i3RaAyWB5o2wDdm3RmghmgYTdFlzO9S4+vH5DODfpvBkpDdAH8w==", "signatures": [{"sig": "MEUCIQCNKka45dyASF4CKqCkIcEeMA/mEWF74jm5s8JN4mNRsgIgRySQWUpOaTvmAdKs4gEkhaRFHUOBCo7/FFZyq9eaSko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1479276}, "main": "rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d62558dbc45912c9c4478dc761bb290738c3b968", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-2_1690888595730_0.059514295014508", "host": "s3://npm-registry-packages"}}, "4.0.0-3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "a7adeab3f194a2b9c22113dae7c90a4f8f908e6d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-3.tgz", "fileCount": 3, "integrity": "sha512-Hy7zP/ZOHDmp+zhjTPgDIzyA2N6vcqCSUXj719JRzRNn9b1Vz4Y+CbVTKUYt3MVoJKx58JxsKUgRL0A3tZOEjg==", "signatures": [{"sig": "MEYCIQDu7j3yz0NYxuvTsE2iXENIzKxTB7/kTSJpjJNS5227CgIhANCv1ZMGUR/K1yBPMAfGUx6+DwkpvmNB6dc6h1AmOZ24", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1475404}, "main": "rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "d9deb724f026a6f3e429509fce2d920e75d6a1ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-3_1691137021434_0.31490269167333373", "host": "s3://npm-registry-packages"}}, "4.0.0-4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7ef0763b6f770a29b262968f7c07f172cceab0aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-4.tgz", "fileCount": 3, "integrity": "sha512-vtbZHfve2uL4vdj7+mIiZQ+yxCAmuJWfRnYPjRTFVlJoQzhSrZbcDBznd2vLAeELPJcLKKUyAlO+YTNwFp6o7A==", "signatures": [{"sig": "MEYCIQCMvHFIsl0YP4iQyY043DZqbBLPRbX6gcQnM5V/DWkrIAIhAKbmfxw/BDXVpEWpSoZmWM8V2OsnYAPGr37Wjlw5EYCA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1475316}, "main": "rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "gitHead": "c416e3eb3d2d6055d6567cac6e8747b992eec1de", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-4_1691149005474_0.9114209651532601", "host": "s3://npm-registry-packages"}}, "4.0.0-5": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f85e9856465dda67442e9c0fe893fa140d47e570", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-5.tgz", "fileCount": 3, "integrity": "sha512-ncBW/ZBtEpHTw/f3PQjD5hCLMoPBxhF0HthKnKoXy+RAsp7fMX1pz427bxPR/rBYnJ98pv2jMrQ2YN+lJzAvIA==", "signatures": [{"sig": "MEYCIQDBXjg8SDCOd5Ph1V2R1xwSVrimkuawzAy6G2a4uYfaSgIhAMgrVvehZ1JgNyd3axcWoFHUvpNruBT9yE7vHTql9AU8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1693487}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm-eabi.node"}}, "gitHead": "6284e58c1be160b656b9f2b44e8e2b1e5a93f9df", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-5_1692514612800_0.045422020166494326", "host": "s3://npm-registry-packages"}}, "4.0.0-6": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e628bbc8302cffe2f51c6198c4f5bd82adf439e6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-6.tgz", "fileCount": 3, "integrity": "sha512-6D5lK5rbcadiUHdSH+XxtxAYkEJug8x2VM/yWALqVF9l6jb0bPn8gR0x7Lg6S3EOj242RDJUZPqMdOo5djLo7w==", "signatures": [{"sig": "MEUCIQCk04LPa9ZuIONM1DIEx//5vA1c3hs/p6EmFuUyJQw6DQIgYE0dXY9m/XB91vZA45dskJOGorzJlZdMaceY7FkcCZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1693487}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm-eabi.node"}}, "gitHead": "39e7492a12eca9107c929d533c16608c9a0054be", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-6_1692517901401_0.22333783930444207", "host": "s3://npm-registry-packages"}}, "4.0.0-7": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "285bfd3978eaa56b3e51aad21d3b10b83190da0a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-7.tgz", "fileCount": 7, "integrity": "sha512-aObJ33EnuUfyW6FUDhJXjS486H5DMjwOyvAhmk/vbwfK04c65qAWCrn7EbUkHZS3PYkgJJ4wRKxKYiZ2fuoQ2g==", "signatures": [{"sig": "MEYCIQDDprprcGKScC5usIppsaPmXtrJIFqNmCsGJPQ1mOXlCwIhAOwxwI4YKnFadr01O6HY+BJYSWEWdP/FSjEcKf07aijG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7148409}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm-eabi.node"}}, "gitHead": "afaa754955a083970b389711127e368d6f4d235b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-7_1692527613171_0.8304188176667084", "host": "s3://npm-registry-packages"}}, "4.0.0-8": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f0f7a041a9f99b9e9d9c1e5981dcd29d5f50951c", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-8.tgz", "fileCount": 7, "integrity": "sha512-iYozuK2QocQTiUXZvcTceZGQf3SslcrIAp+glfx/3mMPJAVvLaop7r8EAaxz3rn4am4X1+MspaQee2ZEanJUmw==", "signatures": [{"sig": "MEUCIQD4rNxkXOkJhdzWQQish6ntpwvCMYOGbAL42ZRDuIpTlwIgB1DqPR8KcXvL6umeZ8dXuxTAPtDU3Osf36zXNCGWgqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7148409}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm-eabi.node"}}, "gitHead": "5bfa022de96252b5eaf0bdab90be6bcfefcccb57", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-8_1692530545081_0.975998040557762", "host": "s3://npm-registry-packages"}}, "4.0.0-9": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "dba6ace3734a67f9d69bd80d1f036244eb75457b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-9.tgz", "fileCount": 7, "integrity": "sha512-Ew0lLUuPZasbTfaVyoQ51y5K76jt9bIKeHI1kut29NeKQ4vVk1U1zd2IW4v8nKOR12O7h3z3UXiYzUdpinNBcw==", "signatures": [{"sig": "MEQCIBsI3U6T+YLIvacDvk72KXUpNB+CsxpF9XIEAIXUuzebAiBwdivWHJmNnYeBR6cg/ydfg7lWnmF4nzxzFUQpbI+m1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7148409}, "engines": {"npm": ">=8.0.0", "node": ">=18.0.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm-eabi.node"}}, "gitHead": "e4d55671a81334ddc59fdbcd81ceabdb77d96974", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-9_1692541751213_0.7539359623769712", "host": "s3://npm-registry-packages"}}, "4.0.0-10": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-10", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-10", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c15020b47c80a7679b13e889d22ade4bc565b96d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-10.tgz", "fileCount": 7, "integrity": "sha512-ZfmWV4wNF2rRpGO0q6oN44y124rM2FufA+0nfJLpQqfLPfSnff2OtWu0M/QPx92o84x2+//yfXCzFzROh2pUsw==", "signatures": [{"sig": "MEYCIQCGESkG7RmGkKldHAtdaZ86ZpDlZ3osxQQm1+cC0aqfdAIhAOC4e+3k3CLyAUMKLQmC7N9zQaHM1PnlH6zVk98ZdGbs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7147733}, "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "exports": {".": {"default": "./wasm-node/bindings_wasm.js", "node-addons": "./rollup.android-arm-eabi.node"}}, "gitHead": "2c7e3e32f5d56c60d92907a9ceacd338aa99ca82", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-10_1692631809580_0.33226000995730587", "host": "s3://npm-registry-packages"}}, "4.0.0-11": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-11", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-11", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0008f0c123d038e718926383bbc039df0fc33a5d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-11.tgz", "fileCount": 3, "integrity": "sha512-eU68UO/rqV06Qh1t77mRkMFuZbwYzMpeCCdqhEb2IBx8vJSglfEeoxdGyf/N3xB2qXjiv7PepD2kRM9nlo6bJA==", "signatures": [{"sig": "MEUCIB3Nuwi2nu7Bz0MKEtPL6lkvaynm5NjhukZIWyABI3OwAiEA490AsGSMM5MiyI2oXzi+NlRi3b8gevcMwCc5vK1LRRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688992}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "3fc8b18da06fc76c386527cebadec4d8936b0f7a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-11_1692785759585_0.33805903023231254", "host": "s3://npm-registry-packages"}}, "4.0.0-12": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-12", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-12", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4b35cf2093db27c29da0f340e85992bd0220fd14", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-12.tgz", "fileCount": 3, "integrity": "sha512-QTqy1tNxZkc9O/Qka7HQDlmGf9ONUEvvLNVb/75dHRI/n8Fp0d5dMt4dPWPZRtsHN9RSgdFqw/gRq4dtqNstiQ==", "signatures": [{"sig": "MEYCIQDurmlk9NeUBNiRPqYTMFkcb54jC1qk6mvoPol/7BHVTQIhAKEHmkpIC1ZacjEtjOCieHO+jc8sUb2LAgTiVOyyz7zq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688896}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "b6eec18d711348e3b177ef58dc2836cdf75e0432", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-12_1692801632780_0.3347490234666628", "host": "s3://npm-registry-packages"}}, "4.0.0-13": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-13", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-13", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "258e0542ba4d58f5dcc07a1572de2fa171bf1ccf", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-13.tgz", "fileCount": 3, "integrity": "sha512-Fyqmgx0SZ0q24LOOxLZ77OgGqCfrAC8cttzYpXjea4gGFElHjkPPiKXJpvx9IBbOlnJDufpvQqO3dV+oNEHjrg==", "signatures": [{"sig": "MEUCIQDBYP8JZC08FoGKOLTEcVkKC5bIEoSSeZUQ1n3/FnAsWwIgPTO1T9QSYJiJeEQ8vghCrwREIeB4thHQt2SZYljvUkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688840}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "07d3baeb218f6d1084e9d1b17a429ca84cb92561", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-13_1692892118968_0.3809173144197542", "host": "s3://npm-registry-packages"}}, "4.0.0-14": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-14", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-14", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4eb43a7412c4d610d969cf0c4bbaaf69c4cd2d60", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-14.tgz", "fileCount": 3, "integrity": "sha512-ZPk0RRd4aMBhFgJY9gBzGvEiZGRjCAKh6Hu43dOX8lpWWi74IRqY87KhaPuTiFVq/dWIxOw2G7vVkInyk/sYcg==", "signatures": [{"sig": "MEQCIH53d/xw6uqesayyd/5+qzfiqc6FaIeLjiJhXIo4Tdk/AiB0xN3szSNVVJMjF5kxANFnOgudPumvSiJ3zbGVhBq/1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664024}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "ec2f8ec863d8d896aef0dd0097f2d73f59e8213a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-14_1694781269455_0.09958142327570174", "host": "s3://npm-registry-packages"}}, "4.0.0-15": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-15", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-15", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "395f34c9d6f588862a39d66964bcc9ba4e7cec4e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-15.tgz", "fileCount": 3, "integrity": "sha512-6XDlYtr0ezDMLoVp/8+mhpo3lMNYMJH9kp8SGNIJIJiAdJbXlsEC9Ob3fVsjHpw8WYu+iG9Va99lqf7zBXrffQ==", "signatures": [{"sig": "MEYCIQDru6eu11HCnyIapVQaKEiI4bkKJIjgFp067d2ajrcJeQIhANKXecuMicM8faWjwYk+QOzFJzSYJ2ruzKkVZS4jmsgG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664024}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "6e6186636ebb169611373a0e430853eb3b6ce8e0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-15_1694783216816_0.07733641714774042", "host": "s3://npm-registry-packages"}}, "4.0.0-16": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-16", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-16", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d7673eb1251b62c5c325ec7079c7d62f22c1352b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-16.tgz", "fileCount": 3, "integrity": "sha512-Z1zDjTwc2dgRR8xIOWO9fSiA69SV2SrZXATID17VoOFAOK/M35bXWvQPtt+2mQMGGikehlcHe7vVMzoAjdYFkw==", "signatures": [{"sig": "MEUCIQDwu4UOkY61NEbUDcTXfKESmwaJIEwDK8bZpBSPe5jE5AIgWz4Cse4p0WWOE28byYQeWeR/Yvg6KDen7YTFi2SYq78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664024}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "fd025bcfab85bdecba183367d11c13a1f99c4f10", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-16_1694787440886_0.9449442758361519", "host": "s3://npm-registry-packages"}}, "4.0.0-17": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-17", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-17", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "548bb63c4c69a3c6d6fbf25b93687cb503bc15ef", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-17.tgz", "fileCount": 3, "integrity": "sha512-MQVHWAr6RjKxdI9ICUWMtOjEWyeToykegeYMvc+29kpBDgiOnUlj4wU2kIQj38X0iIIw3GM1ffHgp+VhcLXYEA==", "signatures": [{"sig": "MEUCIQDP1NKmN0uWH/hALGveNFA1b0OvFk3PJN+7Rv18ARN5xAIgLvUmQZp2M1vnHVLfOe0XLzBr0Th01x4nx8ruDJqiX2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664024}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "f7eb39f003eaa325451091faec04dd51d774ae3b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-17_1694789950988_0.26553775976370586", "host": "s3://npm-registry-packages"}}, "4.0.0-18": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-18", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-18", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f4441eee5b6ce9589128caadbb4ec82e636312a6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-18.tgz", "fileCount": 3, "integrity": "sha512-0a9AJ3ntb2WzvQk7SRWEzYUHvaMt8t3nmq1oIB6xL5OEeRrJG9M1nLfV5tNynreGHOugO6UFuTpMqESxR8Jvxg==", "signatures": [{"sig": "MEUCIQDCdRsSTFVr6BjL8Iba1xe+AVWq0IeVHbFipKrOh/An0AIgbtuAKtiOZNpCmc0ZahNqJby2TJsGnbs+yCjjfEx9fe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664024}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "646171ff58e4f31127714ff8c78868c79b77d596", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-18_1694794216366_0.047211246174094335", "host": "s3://npm-registry-packages"}}, "4.0.0-19": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-19", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-19", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1673c8eb44d912d17759dd53e87291c22ce41f9e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-19.tgz", "fileCount": 3, "integrity": "sha512-CJbXVUxm64EYt7HJYiDC64YqcMPl+L9Y2Z78zRLsDE9QgLXV3g0h0O//8037uuqAjMf4MbV0Kkuz9RZCDhN9/A==", "signatures": [{"sig": "MEUCIQDtvoCu6NyqU2nd61WjmBU7AVivfIGBHFZEHOl3rPm7vAIgBse7e9Kisnl70Ai4CMTVir/AVEJxlAvC5dObLtGPgHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1664024}, "main": "./rollup.android-arm-eabi.node", "engines": {"npm": ">=8.0.0", "node": ">=14.18.0"}, "gitHead": "25753ad04d73429f0d7b4d5dc85df09aeae78485", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-19_1694803861269_0.9875915179511137", "host": "s3://npm-registry-packages"}}, "4.0.0-20": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-20", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-20", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "75a04c9f51f24dcd80f22dc9a59f086be836e9ea", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-20.tgz", "fileCount": 3, "integrity": "sha512-zVWIoSSm3s3TK7qt0nzEF4i7tdHCtbT+wXwaV0BYe17cvSI4kOQNKxAWPSvR71dWSEf4LQ7kVYx33YhEi3J3ag==", "signatures": [{"sig": "MEUCIQDn1xLbTYbnlD0r/nc30yoXwZi0ABKn8FVRVpewxKeWiAIgQR96AZu9OQ0UBpIrJqb3hD463bmVLGhi9+uxzG5yzgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666510}, "main": "./rollup.android-arm-eabi.node", "gitHead": "9d6dc574c6dca3d85e9eda512b09797a6d15462f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-20_1695535841568_0.473546866988805", "host": "s3://npm-registry-packages"}}, "4.0.0-21": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-21", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-21", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0f10d4777927e532a4442f73e614a5d7e2adb6f0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-21.tgz", "fileCount": 3, "integrity": "sha512-FTAtbwj1FIP5DRrda/pH1vFamNhN46Rlv8tjZbXAJpnysTnye9XFYOHSEhCxACljMbndX6i0f94blyb9Xe+TyA==", "signatures": [{"sig": "MEUCIHBmZ0GhaOXwPA/KS26GkBU9lYwl7LKCsWE38m9sZxSvAiEAijZTCaSO0tpaJoJGauW01tiymahwg7s84n2/PFVmX7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666510}, "main": "./rollup.android-arm-eabi.node", "gitHead": "fa868ad975b9ae6007ddc64b1a9e82766de6fa9e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-21_1695576148202_0.9942450589385392", "host": "s3://npm-registry-packages"}}, "4.0.0-22": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-22", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-22", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "57dac912e1192fc9cb49cf310411f4b98f894d45", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-22.tgz", "fileCount": 3, "integrity": "sha512-HFyknvycaAIA4Y0OuE70Iuqp5TK1I7i5btZiPrzeIaZ6P7KjToD7I7JbLD0vfRX/qHoX3x+nN/ZaVva2zY46gQ==", "signatures": [{"sig": "MEUCIEMmJGyr6so6s7/84n3+xiXM3ONwwDlvP6P1bPcz+02qAiEA5DhU0vQqAcNt+WXDARvuSiGxl0twjnTTeb+oxGzMXY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666322}, "main": "./rollup.android-arm-eabi.node", "gitHead": "38be49cf19099321f935c1ad5968e76fb30e0957", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-22_1695745051895_0.7062754116533156", "host": "s3://npm-registry-packages"}}, "4.0.0-23": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-23", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-23", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "72b2ff8ebd2b95507e427d3e9cdd6aef66faf8bc", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-23.tgz", "fileCount": 3, "integrity": "sha512-bFVjyz+f4ICADXH6NmEthwLa6LeP0oHSlh5UGDZnGptPzAjMH/gQ3Bo1kFzG0p9xUNW22/uwHb9gnrLGQjgSBA==", "signatures": [{"sig": "MEYCIQC85ndFexIwIj/NzB/kf1UqxQmjxm9n30ePdYmmun7stAIhAJTTMw5d4s2+Jq3JANSa5KhQtJDjbVj5M1jJzsSBHrbF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666338}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f1d93caae901c556ffb1e2f553428754038d65c1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-23_1695759266865_0.36979049622341287", "host": "s3://npm-registry-packages"}}, "4.0.0-24": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-24", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-24", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "686cea1af32751654f49c0134f686430c44aa6d4", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-24.tgz", "fileCount": 3, "integrity": "sha512-19cF3V1fHfzPzwu0cgZEdWLMdNkqSmKOhidqQv1CkUqAMcb7etA7WLx8YrX5ob31ruI0BYYrUDBunlIuMHHUrg==", "signatures": [{"sig": "MEUCICKBmPOuQK/YqNBI8FfFqts2zCDO5JWSihEcmasGOcKfAiEAsWlxr6WPykcnIjpOxCFcfiv21y5F6eiSEv6GvuDrfRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1680754}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ced077f2920c473c4c2ca31a8d72b259bec91f67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-24_1696309970304_0.37676805893689225", "host": "s3://npm-registry-packages"}}, "4.0.0-25": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0-25", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0-25", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d056502d7e4fb2a72a06c4c1953ec4bbdb4cf135", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0-25.tgz", "fileCount": 3, "integrity": "sha512-u9p/d+WakO0U73IZmHvtalMG7LPLoDgkIImVTbV+kK5QRX4WQF8h7Y96T1MNDwM/7+Ci8QOso5L2FDWbk+rTuw==", "signatures": [{"sig": "MEYCIQCPZkeltxQZ68sCmrdWCF24EerXQwvJu9fygj7g0uvhdQIhAMp8TZON0pStPUtrie5EDnPfcXlCXanDxLQB73FS8DLq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1694214}, "main": "./rollup.android-arm-eabi.node", "gitHead": "1ac6bbc437c7ed0de3ad23e4e0904f00783e703d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0-25_1696515171310_0.17157695001704676", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3a0258d22b5ee30235d04b4b4cd49c28b590ad8e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.0.tgz", "fileCount": 3, "integrity": "sha512-rN3qt1JzOx0v7JWyK68zkb3yf1k1f1OhhHR0i7vLlGlediTtM3FKsOkestQN6HwJ9nEaP3KxPHxH5Xv7yr6f4w==", "signatures": [{"sig": "MEQCIHpym81FBtGFWj+TV+O4bLTLcHmKVOVWsfEQp50uHJrvAiBDObBSClwxtTFkH6HjF3m1O2QOmDrPh2tHlr5P+Mkk1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1694211}, "main": "./rollup.android-arm-eabi.node", "gitHead": "2f261358c62b4f9e62cb86bf99de8d4ff3668994", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.0_1696518877607_0.2920535102566455", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ad1e1330e7a36b5bae033830a8c0c88c939c8f99", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.1.tgz", "fileCount": 3, "integrity": "sha512-u6xSfKQXP6NlQYr1+3b+rYEG2YsczVC46ZQjdkBtP+IKqRNT6w6ivTv4ywoxN/rMpBirrru73nvPF7F/gyTkJw==", "signatures": [{"sig": "MEUCIHdlAN40ovimmfToodU53ZnNc5+pCQvgmI9oX06DJoNEAiEA49orVnPucO0HE0u4WI1I0iQnbN2tBkl84JOl32vLIn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1685659}, "main": "./rollup.android-arm-eabi.node", "gitHead": "fcab1f610fefb24621ce001dfb0831dd30e59ab3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.1_1696595809075_0.5614372226048907", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.0.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.0.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c990ff2c4317b477121272fce8ee4075bbc36b67", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.0.2.tgz", "fileCount": 3, "integrity": "sha512-xDvk1pT4vaPU2BOLy0MqHMdYZyntqpaBf8RhBiezlqG9OjY8F50TyctHo8znigYKd+QCFhCmlmXHOL/LoaOl3w==", "signatures": [{"sig": "MEUCIQDkDUllcTqp+hsFI4/S0UQ94T2kPN31miaRZMhJzI6YgwIgKNasummUqn4S6k2aRf33pY7l2bIWbTE9FWt3cilmW04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1685723}, "main": "./rollup.android-arm-eabi.node", "gitHead": "3d9c833c4fcb666301967554bac7ab0a0a698efe", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.0.2_1696601928449_0.694062323208319", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.1.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0fe32a4289d7e7c8dfbdbc8658f6b62fd5907e36", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.1.0.tgz", "fileCount": 3, "integrity": "sha512-ALx3P+gRnVSzWPsPq7F3pNCay4zN1NJVRTjpSSUNrZj1+DqBuwwt830JLyEATmGaN1VJ15UkqudSx8Mu3BF3BA==", "signatures": [{"sig": "MEQCICx5Ej7F9d4ghK5+6SNk9v4/OKedR8b95GO9V+C2rl6bAiBMKc6yNKjTCMfNSLQlAhCAWjMldfUoNyGhLBKyZobxYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1710215}, "main": "./rollup.android-arm-eabi.node", "gitHead": "cb144b2be4262b3743b31983b26f7fa985be3ceb", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.1.0_1697262740469_0.09593316160075882", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.1.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.1.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3940c0f8ee7c859a117e3b5301dbe96af0c6b0d3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.1.1.tgz", "fileCount": 3, "integrity": "sha512-unO1UYnQ1j9OEAmfjTF2kffkLBX1lFUlfAe0zWA8lKFr+PpXOL/xrkyAXmH3HF+DicabKgAgU1xm8n+W2+8y3A==", "signatures": [{"sig": "MEUCICnUxUjhXoQal33BXzMcWrD8HTVFT71/8kEVCArV2E5OAiEA8EdUX1CkzemMW4qKA4sg5951XD8O/K7skptpuP2Q6vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1814919}, "main": "./rollup.android-arm-eabi.node", "gitHead": "d8b31a202a246758b8d67eefe77361a894d37005", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.1.1_1697351514482_0.7558112097567011", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.1.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.1.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "06bfca0a2a2a9189580fab7e5d03b859103fcfb2", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.1.3.tgz", "fileCount": 3, "integrity": "sha512-pugxxuAygWTDj3WcyXGspor433gR35riZz66L0EdToX+shXGfh9qKgEFdTPoW9KwIrrJnxDGv9ncfl+a9bFg1w==", "signatures": [{"sig": "MEUCIENJgx99dg2jhK5MwQQhhdqJaMM/jY5sni3USnOkmW/3AiEAtF5XJ89VL5wMe8tOeEOHDjROlf+xPQ4j05cbUM2SlwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1814919}, "main": "./rollup.android-arm-eabi.node", "gitHead": "c61a1507a88fc71be431550642b040da4b9422b0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.1.3_1697392113362_0.884843373857573", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.1.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.1.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e9bc2540174972b559ded126e6f9bf12f36c1bb1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.1.4.tgz", "fileCount": 3, "integrity": "sha512-WlzkuFvpKl6CLFdc3V6ESPt7gq5Vrimd2Yv9IzKXdOpgbH4cdDSS1JLiACX8toygihtH5OlxyQzhXOph7Ovlpw==", "signatures": [{"sig": "MEYCIQCKbVTpDH2Ax/uBcwiVIMCG33o1lbgCSdi6kqCxWjY94QIhAK6M3ssrih36JRk4SaxDnlqYH0bvMjCpFaWxzkWYE3vz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1811911}, "main": "./rollup.android-arm-eabi.node", "gitHead": "061a0387c8654222620f602471d66afd3c582048", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.1.4_1697430855738_0.8984427644178232", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.1.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.1.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "aa13d291631fe72552ca2adba50f8d58ef2cde6f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.1.5.tgz", "fileCount": 3, "integrity": "sha512-/fwx6GS8cIbM2rTNyLMxjSCOegHywOdXO+kN9yFy018iCULcKZCyA3xvzw4bxyKbYfdSxQgdhbsl0egNcxerQw==", "signatures": [{"sig": "MEUCIAXzQQC6E8z1E5SzSh4IM/E29XlYDC8hHqQ/+0TC6O7VAiEAwuFh//LKPvmkJ377U9ca8aAKHQQ1cKFW/YlOrvkV/Ak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1814215}, "main": "./rollup.android-arm-eabi.node", "gitHead": "1cbb382b0dd3ab70541671c105f96eff283904ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.1.5_1698485018616_0.5665226422649883", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.1.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.1.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7ecf454cb7cad93981356bf49edd04779a2eb657", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.1.6.tgz", "fileCount": 3, "integrity": "sha512-zJv8c56LMdPMpaQJjc1OV28O5G53Y9ZWZM2g8WzUo6XfyHPXPsXw3esrA4n0+lIaxmsE3pWwrIYUvp5Jw9Zefg==", "signatures": [{"sig": "MEYCIQDFdzXKToHt9LYnjfiVHm87WeHqeCjfXaTAY0dVdCcY8AIhAJvdLdepBSyKltIjZaF8hja0XiN639PhO60lBx+wPwA8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1814215}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5901e545697b36326110d89ed02964fdaffd9f6f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.1.6_1698731121852_0.6260111756000737", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.2.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b945a7044afce5de03d03a55aef3503a50d92586", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.2.0.tgz", "fileCount": 3, "integrity": "sha512-8PlggAxGxavr+pkCNeV1TM2wTb2o+cUWDg9M1cm9nR27Dsn287uZtSLYXoQqQcmq+sYfF7lHfd3sWJJinH9GmA==", "signatures": [{"sig": "MEUCIEqT+ARpuhzKhvB4/1mzkJe3NVuNeQNiW1tx/gFhHDBCAiEAm4fQAoPGi77Yxw08hmjx9z+wOabzKxbXP6vJ0HMzrgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1825783}, "main": "./rollup.android-arm-eabi.node", "gitHead": "fbf806aceffd822d43e4603b664c54165c72cf36", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.2.0_1698739847053_0.48791605495650625", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.3.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8ad8a660b18f1a24ad4a272738a65ac4788a8811", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.3.0.tgz", "fileCount": 3, "integrity": "sha512-/4pns6BYi8MXdwnXM44yoGAcFYVHL/BYlB2q1HXZ6AzH++LaiEVWFpBWQ/glXhbMbv3E3o09igrHFbP/snhAvA==", "signatures": [{"sig": "MEUCIDh2ZOyn2EGhhSnVXDkCftps/Uj8icIggLJlSJy0UP3wAiEA/UYBV4t6lYEKFrAvLXVv2R6Jml8FnN+/6vA7swWu0Sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1825463}, "main": "./rollup.android-arm-eabi.node", "gitHead": "937d9911376574c42f893e1cd14b55418c4f7b68", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.3.0_1699042389793_0.6276667297634344", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.3.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "feac5d45da2d98dce58905b3e36f286c9771fef7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.3.1.tgz", "fileCount": 3, "integrity": "sha512-D+opNc1CnFmN6EcpG2BXUo9dI/vgoqo6xijv/nUPE1t7Y0Iz9IaXkSjaqw5MJq7B1DUawXfEaIdVCod27IsAOQ==", "signatures": [{"sig": "MEUCIQDwhhOIoMqv8KE2h4xtmYPPCkPbXbzkfe3f0vwZRu7/vQIgVA9OlTodXrclx2V6SNGgfgfItZeOHC9R0GMrhGgzk5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1819567}, "main": "./rollup.android-arm-eabi.node", "gitHead": "52c55bb1e17154ae6d01fb40e0e4a3589bc20a8f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.3.1_1699689480316_0.47968582569636675", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.4.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "07f0bd33af84bfadb1a93fd86c3d0ea290ec2558", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.4.0.tgz", "fileCount": 3, "integrity": "sha512-AD30wtT58hZZsXIeiksytR6Gm2gofUxn5KqrDBdyzekgxXB9bXN9dqWIEcPfYo9lA9MVRm0lC42LuYGsscRxiA==", "signatures": [{"sig": "MEUCIDMaPuxO8syNxaK6ADgV4pelxgGz0u/XUK7ExI55eKezAiEAvbe6G26Vj3jsT4nqJFUu5v9EPacmenjNKSyiMfmTpJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1589791}, "main": "./rollup.android-arm-eabi.node", "gitHead": "53d636051ac60da9b302c4bd6b7eaaccb4871f4b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.4.0_1699775399112_0.41822218060111105", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.4.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.4.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f276b0fa322270aa42d1f56c982db6ef8d6a4393", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.4.1.tgz", "fileCount": 3, "integrity": "sha512-Ss4suS/sd+6xLRu+MLCkED2mUrAyqHmmvZB+zpzZ9Znn9S8wCkTQCJaQ8P8aHofnvG5L16u9MVnJjCqioPErwQ==", "signatures": [{"sig": "MEQCIAMiI0AGc2C8+bg/c+VwjDQzCs3Y6aM+PJBNUQF6zTNeAiAdR23OW0vElcrT2tuTopOrIUc4WjfShgMJ+W4i9LrgWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1589791}, "main": "./rollup.android-arm-eabi.node", "gitHead": "01d8c9d1b68919c2c429427ae7e60f503a8bb5f4", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.4.1_1699939525452_0.6718778999386261", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.5.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5984f98288150a2c34928de023bbd122d61ce754", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.5.0.tgz", "fileCount": 3, "integrity": "sha512-OINaBGY+Wc++U0rdr7BLuFClxcoWaVW3vQYqmQq6B3bqQ/2olkaoz+K8+af/Mmka/C2yN5j+L9scBkv4BtKsDA==", "signatures": [{"sig": "MEQCICqqylbkE+PNokUP4nw7TFl8CCd41PiOXnYilc6zsamNAiApWqbmhAKkkb2VrIxecwzgJ1L4iXUrzMBOpkD7luZIng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1586911}, "main": "./rollup.android-arm-eabi.node", "gitHead": "86efc769f693516a29047c8d160c6d7287fb965d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.5.0_1700286737500_0.1818824792228595", "host": "s3://npm-registry-packages"}}, "4.5.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.5.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "11bea66c013e5a88a0f53f315b2d49cfd663584e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.5.1.tgz", "fileCount": 3, "integrity": "sha512-YaN43wTyEBaMqLDYeze+gQ4ZrW5RbTEGtT5o1GVDkhpdNcsLTnLRcLccvwy3E9wiDKWg9RIhuoy3JQKDRBfaZA==", "signatures": [{"sig": "MEUCIQC8S89Rb+CqyYJ2C+MzQEajmSb2HZ5hWh6LkJm9f3olYgIgHbG/N/LgaVWbuQr4fxCjWZA/2bVeizqaX+2srmvjzMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1586911}, "main": "./rollup.android-arm-eabi.node", "gitHead": "a083019c7f0c18a1c17260ab1239b12400984a88", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.5.1_1700597594975_0.039504777783258005", "host": "s3://npm-registry-packages"}}, "4.5.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.5.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.5.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "fa3e9ba323449f098b8d144ecaa4f044d2ff2ee3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.5.2.tgz", "fileCount": 3, "integrity": "sha512-ee7BudTwwrglFYSc3UnqInDDjCLWHKrFmGNi4aK7jlEyg4CyPa1DCMrZfsN1O13YT76UFEqXz2CoN7BCGpUlJw==", "signatures": [{"sig": "MEQCIE6QKZUAAofQNDChjcYxZovA5oF9z/7XDgfXVeeRnfcyAiBlz6k9oGjsYk5i4qQe1Fd1e0QmALSbnP7FFiGrckE2Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1587551}, "main": "./rollup.android-arm-eabi.node", "gitHead": "2e94641971195c1a4eb9e1a3fe6d73b9d04ffae0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.5.2_1700807392437_0.2889906608375876", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.6.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c08a454d70605aacad17530a953791ea385e37d5", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.6.0.tgz", "fileCount": 3, "integrity": "sha512-keHkkWAe7OtdALGoutLY3utvthkGF+Y17ws9LYT8pxMBYXaCoH/8dXS2uzo6e8+sEhY7y/zi5RFo22Dy2lFpDw==", "signatures": [{"sig": "MEQCIGVGM0pYTG3ppsdAHFOKfv+djR40lmuqviedfRrjjc5oAiAN63H2H84MLYabveTRZHA7jC+MrQb/uNB+ofN1dcd4Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1587551}, "main": "./rollup.android-arm-eabi.node", "gitHead": "020774d0c7b1371865b20878e59dd3a6a45d3b31", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.6.0_1701005958785_0.21595739710093098", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.6.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.6.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0ea289f68ff248b50fea5716ca9f65f7d4dba3ae", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.6.1.tgz", "fileCount": 3, "integrity": "sha512-0WQ0ouLejaUCRsL93GD4uft3rOmB8qoQMU05Kb8CmMtMBe7XUDLAltxVZI1q6byNqEtU7N1ZX1Vw5lIpgulLQA==", "signatures": [{"sig": "MEUCIHsYUsHoKWYPQArsnGAFpkwMqXjdLETAZaql0xtNUrmlAiEA7mvFhOTX5d9SRN6QLHh0TvZjulZrDWYXSkRlW6zn3yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1587551}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ded37aa8f95d5ba9786fa8903ef3424fd0549c73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.6.1_1701321794670_0.9289051684437042", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.7.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c144935afdf83e3da0ddea4d903360f99f69c79a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.7.0.tgz", "fileCount": 3, "integrity": "sha512-rGku10pL1StFlFvXX5pEv88KdGW6DHUghsxyP/aRYb9eH+74jTGJ3U0S/rtlsQ4yYq1Hcc7AMkoJOb1xu29Fxw==", "signatures": [{"sig": "MEUCIEF9e2wOlbqdDxSzbHemeQi+ct9CbolakdcafCGH0NzmAiEApcZ+bR3i8KquAH239RyR13bf0PIzqynB25gKGWRZDH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1588895}, "main": "./rollup.android-arm-eabi.node", "gitHead": "098e29ca3e0643006870f9ed94710fd3004a9043", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.7.0_1702022286455_0.698219044404387", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.8.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0e42b155630adaaec0f659f979ece4b7d3391329", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.8.0.tgz", "fileCount": 3, "integrity": "sha512-zdTObFRoNENrdPpnTNnhOljYIcOX7aI7+7wyrSpPFFIOf/nRdedE6IYsjaBE7tjukphh1tMTojgJ7p3lKY8x6Q==", "signatures": [{"sig": "MEUCIQDB2l/zgEyqrH6z7KZL7P0QvftvR9PEOrorYlTWCdptnwIgLDMgH5kGuLyvMQqUgOX6FMc0I0bN5JnGvhE2SxtY+ZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1588895}, "main": "./rollup.android-arm-eabi.node", "gitHead": "62b648e1cc6a1f00260bb85aa2050097bb4afd2b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.8.0_1702275898935_0.1714744851194303", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0437b27edd7095d0b6d5db99d13af8157d7c58b0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.0.tgz", "fileCount": 3, "integrity": "sha512-+1ge/xmaJpm1KVBuIH38Z94zj9fBD+hp+/5WLaHgyY8XLq1ibxk/zj6dTXaqM2cAbYKq8jYlhHd6k05If1W5xA==", "signatures": [{"sig": "MEUCIE+xrQfKsU49SE60T16GKA06U80nf8VDXUFrtapk34ShAiEA94BwMqoNE2tqeVyCJP/sl6fa+dI0HRQ1yLBcrzf7agM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1588895}, "main": "./rollup.android-arm-eabi.node", "gitHead": "c5337ef28a71c796e768a9f0edb3d7259a93f1aa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.0_1702459462239_0.14694860780784258", "host": "s3://npm-registry-packages"}}, "4.9.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "beaf518ee45a196448e294ad3f823d2d4576cf35", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.1.tgz", "fileCount": 3, "integrity": "sha512-6vMdBZqtq1dVQ4CWdhFwhKZL6E4L1dV6jUjuBvsavvNJSppzi6dLBbuV+3+IyUREaj9ZFvQefnQm28v4OCXlig==", "signatures": [{"sig": "MEUCIGyW2gmhGH1/8NrCsakMe89Ioa+64Y/He88+SJcxvzkgAiEAjNv4ClpLjhJFwxBEmM3Rfmqnj2mdoIsGrKb3YI1YNj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1599327}, "main": "./rollup.android-arm-eabi.node", "gitHead": "d56ac63dc0452820272a0d7536340277f7db68bf", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.1_1702794376485_0.6386814801622178", "host": "s3://npm-registry-packages"}}, "4.9.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ccb02257556bacbc1e756ab9b0b973cea2c7a664", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.2.tgz", "fileCount": 3, "integrity": "sha512-RKzxFxBHq9ysZ83fn8Iduv3A283K7zPPYuhL/z9CQuyFrjwpErJx0h4aeb/bnJ+q29GRLgJpY66ceQ/Wcsn3wA==", "signatures": [{"sig": "MEUCIA1LDm1n8xaiLugm3k7CPiQpIFVWkck02XQ1nELRy4vUAiEAlyh/M7e7ygyx+DiWGOfHkv629SYkwb/nPzrtUSjpl1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1596959}, "main": "./rollup.android-arm-eabi.node", "gitHead": "347a34745b2679c1192535db3c0f60889861d3ad", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.2_1703917412597_0.7362921635000217", "host": "s3://npm-registry-packages"}}, "4.9.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4ef03e5769e9052d5fb89d0dd9c7cc8930635fa5", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.3.tgz", "fileCount": 3, "integrity": "sha512-nvh9bB41vXEoKKvlWCGptpGt8EhrEwPQFDCY0VAto+R+qpSbaErPS3OjMZuXR8i/2UVw952Dtlnl2JFxH31Qvg==", "signatures": [{"sig": "MEQCIE3vcNT6KRs09Ia0WzRKce20gJ9ztem2BeGp6O3gReU3AiB1D/bZKkr7DqNM/uLY26oQcE0uYY4TA7DyEXx5opxXYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1594207}, "main": "./rollup.android-arm-eabi.node", "gitHead": "4ab3ad360457cd79f4ea852447d3ddca22da95d6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.3_1704435651808_0.7963807408836681", "host": "s3://npm-registry-packages"}}, "4.9.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b1094962742c1a0349587040bc06185e2a667c9b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.4.tgz", "fileCount": 3, "integrity": "sha512-ub/SN3yWqIv5CWiAZPHVS1DloyZsJbtXmX4HxUTIpS0BHm9pW5iYBo2mIZi+hE3AeiTzHz33blwSnhdUo+9NpA==", "signatures": [{"sig": "MEUCIQCjVX5HyVhEI1XwtAU6NUU+Apa0MFZvRbC/ExKN8UK0owIgDaLqxVEI1HkFBDKmStrsLHw81pZCtjvE5f5Ct34zWkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1594271}, "main": "./rollup.android-arm-eabi.node", "gitHead": "18372035f167ec104280e1e91ef795e4f7033f1e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.4_1704523145434_0.11524687328040284", "host": "s3://npm-registry-packages"}}, "4.9.5": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b752b6c88a14ccfcbdf3f48c577ccc3a7f0e66b9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.5.tgz", "fileCount": 3, "integrity": "sha512-idWaG8xeSRCfRq9KpRysDHJ/rEHBEXcHuJ82XY0yYFIWnLMjZv9vF/7DOq8djQ2n3Lk6+3qfSH8AqlmHlmi1MA==", "signatures": [{"sig": "MEYCIQCxc6FtWDDt9QUUYZcXV02Zwwf5sWTse6UwfpVf08EQegIhAINo93aNHs8VN84a0RZhGPK4qKU4kWkfPOQYMYLVtOQf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1589791}, "main": "./rollup.android-arm-eabi.node", "gitHead": "7fa474cc5ed91c96a4ff80e286aa8534bc15834f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.5_1705040178718_0.2780224812230587", "host": "s3://npm-registry-packages"}}, "4.9.6": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.9.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.9.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "66b8d9cb2b3a474d115500f9ebaf43e2126fe496", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.9.6.tgz", "fileCount": 3, "integrity": "sha512-MVNXSSYN6QXOulbHpLMKYi60ppyO13W9my1qogeiAqtjb2yR4LSmfU2+POvDkLzhjYLXz9Rf9+9a3zFHW1Lecg==", "signatures": [{"sig": "MEUCIQCX4cZJPzU649yHWvyMJgBlZUjmbsusvN8O17eMAvAclQIgHFwY+0EJK7WctesAN71R1jWqwTtVhN1kcNc6KwLGU3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1593119}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ecb6b0a430098052781aa6ee04ec92ee70960321", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.9.6_1705816344100_0.008808530921597724", "host": "s3://npm-registry-packages"}}, "4.10.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.10.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.10.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "786eaf6372be2fc209cc957c14aa9d3ff8fefe6a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.10.0.tgz", "fileCount": 3, "integrity": "sha512-/MeDQmcD96nVoRumKUljsYOLqfv1YFJps+0pTrb2Z9Nl/w5qNUysMaWQsrd1mvAlNT4yza1iVyIu4Q4AgF6V3A==", "signatures": [{"sig": "MEUCIC49NbUJEpiHaDzsrbM7VhKGM5uA+n9ZFvIOOskR5g51AiEA+3hDxweswnoY7viFAFm8e6vLSvv9OTG0pF58cGtR7pE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638312}, "main": "./rollup.android-arm-eabi.node", "gitHead": "762420860765e8e46e24d48b38f5b98ca31735fa", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.10.0_1707544726547_0.042567256568284195", "host": "s3://npm-registry-packages"}}, "4.11.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.11.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.11.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "78693f843483a511bce6ce1d8a153a49f4cbab87", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.11.0.tgz", "fileCount": 3, "integrity": "sha512-BV+u2QSfK3i1o6FucqJh5IK9cjAU6icjFFhvknzFgu472jzl0bBojfDAkJLBEsHFMo+YZg6rthBvBBt8z12IBQ==", "signatures": [{"sig": "MEUCIEvXaipGL9hVS3R6ARZucVMMR9f4bpz2oSISSwUYmzZEAiEA5ihn+XUhusjHNOQnnnTz0vIGPA5XodRLGAdag/kXGCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638312}, "main": "./rollup.android-arm-eabi.node", "gitHead": "90ad652b745c5fe7167d92b4ad671cc387577a99", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.11.0_1707977386535_0.45850453934777513", "host": "s3://npm-registry-packages"}}, "4.12.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.12.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.12.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "38c3abd1955a3c21d492af6b1a1dca4bb1d894d6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.12.0.tgz", "fileCount": 3, "integrity": "sha512-+ac02NL/2TCKRrJu2wffk1kZ+RyqxVUlbjSagNgPm94frxtr+XDL12E5Ll1enWskLrtrZ2r8L3wED1orIibV/w==", "signatures": [{"sig": "MEYCIQCHty8rmzyTylS6uk+V4mRDh4XU1SV/4gurhpxA4XhBFgIhAPkfB9bAFfj4XuZ/LzVc/09ps7mV/7jleVGI/hudD8TW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1629992}, "main": "./rollup.android-arm-eabi.node", "gitHead": "0146b84be33a8416b4df4b9382549a7ca19dd64a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.12.0_1708090345689_0.21472074879201664", "host": "s3://npm-registry-packages"}}, "4.12.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.12.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.12.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "11aaa02a933864b87f0b31cf2b755734e1f22787", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.12.1.tgz", "fileCount": 3, "integrity": "sha512-iU2Sya8hNn1LhsYyf0N+L4Gf9Qc+9eBTJJJsaOGUp+7x4n2M9dxTt8UvhJl3oeftSjblSlpCfvjA/IfP3g5VjQ==", "signatures": [{"sig": "MEUCIDUIEtzjyHyf5PgY+mkPzz8OHGtTqS5EXDUNgpGESKuhAiEAxW4bUvHWhzttyk67eif6S3oh+ahFIhFcnS47dpArn98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1634152}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f44dac3170a671b0978afa3af43818617904f544", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.12.1_1709705021805_0.013569176082342649", "host": "s3://npm-registry-packages"}}, "4.13.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.13.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.13.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b98786c1304b4ff8db3a873180b778649b5dff2b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.13.0.tgz", "fileCount": 3, "integrity": "sha512-5ZYPOuaAqEH/W3gYsRkxQATBW3Ii1MfaT4EQstTnLKViLi2gLSQmlmtTpGucNP3sXEpOiI5tdGhjdE111ekyEg==", "signatures": [{"sig": "MEYCIQC+7tyeNC8iZ+C4+QIQHnYm0fRaPpjWK65j0dH+nuqrmwIhANyEP656fXVs5fWVZlc0m1gshx0se7sOzvCX4EYp0szw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1634152}, "main": "./rollup.android-arm-eabi.node", "gitHead": "1c8afed74bd81cd38ad0b373ea6b6ec382975013", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.13.0_1710221324247_0.3584325786436957", "host": "s3://npm-registry-packages"}}, "4.13.1-1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.13.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.13.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9b8de7e89b33741993898e2752171bd9a1fa6c12", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.13.1-1.tgz", "fileCount": 3, "integrity": "sha512-PpPhlWSagPko4cILCvfR1CH5sHQicILVkvFmJoLFHB99HI46CnnK6xB5TrlzVyp/vDAglCBCi1Y/Or0BLzzKyw==", "signatures": [{"sig": "MEUCIQDi000+GL2YMjjA3NFIl2p23+ocIyAMg/AU2WRbNyc56gIgOCNv7bzmA++646rz+0lLZiqVTaXLNtWAiQfiBDmMqPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638698}, "main": "./rollup.android-arm-eabi.node", "gitHead": "84797d177bee161df233644292bc8f128b989cea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.13.1-1_1711265968428_0.8435821844492601", "host": "s3://npm-registry-packages"}}, "4.13.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.13.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.13.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "88ba199f996e0000689130ed69e47df8b0dfbc70", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.13.1.tgz", "fileCount": 3, "integrity": "sha512-4C4UERETjXpC4WpBXDbkgNVgHyWfG3B/NKY46e7w5H134UDOFqUJKpsLm0UYmuupW+aJmRgeScrDNfvZ5WV80A==", "signatures": [{"sig": "MEUCIQC/kfOREgUt7TuXL26hD+G91CUqKI7HclazU0ySPhxAQgIgBbgnKQc+0vfk8tOB0nq0ELf48jQIE8zbTu+gtCSb5Lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638696}, "main": "./rollup.android-arm-eabi.node", "gitHead": "fffaedeaa1cf9c8f6efc93d53bb8a81738e0ce87", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.13.1_1711535267360_0.7361568035273183", "host": "s3://npm-registry-packages"}}, "4.13.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.13.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.13.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "fbf098f49d96a8cac9056f22f5fd80906ef3af85", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.13.2.tgz", "fileCount": 3, "integrity": "sha512-3XFIDKWMFZrMnao1mJhnOT1h2g0169Os848NhhmGweEcfJ4rCi+3yMCOLG4zA61rbJdkcrM/DjVZm9Hg5p5w7g==", "signatures": [{"sig": "MEUCIDl2hsLZjwhnDhPkVQfGF9ggAyy6ecsaIovalynO85v4AiEA4h7pcvWyWFkfJJ2Aq59CMAE3P0HucTMB/KvzIJs9Q+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638696}, "main": "./rollup.android-arm-eabi.node", "gitHead": "b379a592234416a2084918b0eea4c81865a1579f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.13.2_1711635220758_0.9418542310819225", "host": "s3://npm-registry-packages"}}, "4.14.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.14.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.14.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "57936f50d0335e2e7bfac496d209606fa516add4", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.14.0.tgz", "fileCount": 3, "integrity": "sha512-jwXtxYbRt1V+CdQSy6Z+uZti7JF5irRKF8hlKfEnF/xJpcNGuuiZMBvuoYM+x9sr9iWGnzrlM0+9hvQ1kgkf1w==", "signatures": [{"sig": "MEQCIF8eqt+ii+ldyHBzosO5J4L8bIHL45pYk6K4D03UFZ7AAiBCwN71I2/r8YXQ0zDoeHkQ8ay63jKwrkOIOMx7S6CicQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1646056}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5abe71bd5bae3423b4e2ee80207c871efde20253", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.14.0_1712121780637_0.5055494611260833", "host": "s3://npm-registry-packages"}}, "4.14.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.14.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.14.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ca0501dd836894216cb9572848c5dde4bfca3bec", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.14.1.tgz", "fileCount": 3, "integrity": "sha512-fH8/o8nSUek8ceQnT7K4EQbSiV7jgkHq81m9lWZFIXjJ7lJzpWXbQFpT/Zh6OZYnpFykvzC3fbEvEAFZu03dPA==", "signatures": [{"sig": "MEQCIEb52tzLf/5Pm1ddzSzjmP2nJ1cJfpZQFE9ihNaGCgirAiB4dtfyd1/qCSn+RXNzukTL6DSWfscBuuqg69pV2gFtkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1639848}, "main": "./rollup.android-arm-eabi.node", "gitHead": "0b665c31833525c923c0fc20f43ebfca748c6670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.14.1_1712475344847_0.915874235379277", "host": "s3://npm-registry-packages"}}, "4.14.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.14.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.14.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9047b5b1ec19f58c0fdf3a072bd977bcec056576", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.14.2.tgz", "fileCount": 3, "integrity": "sha512-ahxSgCkAEk+P/AVO0vYr7DxOD3CwAQrT0Go9BJyGQ9Ef0QxVOfjDZMiF4Y2s3mLyPrjonchIMH/tbWHucJMykQ==", "signatures": [{"sig": "MEYCIQDp6zITQuBNtHSJg8dk1YzkKknTKe6WxJhSrnzwOt44TQIhAKSyjddB+0k6AaoVkSsShwsUWZVx6lD0yg89z5UIWFtY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1642856}, "main": "./rollup.android-arm-eabi.node", "gitHead": "7275328b41b29605142bfdf55d68cb54e895a20c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.14.2_1712903025268_0.5361156895226835", "host": "s3://npm-registry-packages"}}, "4.14.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.14.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.14.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "bddf05c3387d02fac04b6b86b3a779337edfed75", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.14.3.tgz", "fileCount": 3, "integrity": "sha512-X9alQ3XM6I9IlSlmC8ddAvMSyG1WuHk5oUnXGw+yUBs3BFoTizmG1La/Gr8fVJvDWAq+zlYTZ9DBgrlKRVY06g==", "signatures": [{"sig": "MEUCIQCptJcsx1egvS0YxOp/M7K42ymrHLxeaR1bNk4KpmhR0AIgHPDzzV31A6SCIcSKzgUCshHlBFjNWmfs2dFn8QD1uRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1639356}, "main": "./rollup.android-arm-eabi.node", "gitHead": "e64f3d8d0cdc561f00d3efe503e3081f81889679", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.14.3_1713165516840_0.25326952254831436", "host": "s3://npm-registry-packages"}}, "4.15.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.15.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.15.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "28c9c79c5baccb59a96afcf60e428ea6965a5579", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.15.0.tgz", "fileCount": 3, "integrity": "sha512-O63bJ7p909pRRQfOJ0k/Jp8gNFMud+ZzLLG5EBWquylHxmRT2k18M2ifg8WyjCgFVdpA7+rI0YZ8EkAtg6dSUw==", "signatures": [{"sig": "MEYCIQCu8RhjGrm2guNGPbo8xOQrsFjnqxW//uxHaBQTuwY69wIhAOnMTkX4nb2wXI2nPkDY82/19T899ixNRgpMhpPZkI9t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687612}, "main": "./rollup.android-arm-eabi.node", "gitHead": "e6e05cde31fc144228bb825c9d4ebba2f377075c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.15.0_1713591439780_0.22050766966849178", "host": "s3://npm-registry-packages"}}, "4.16.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.16.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.16.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "51e697b1fe08d3bc39d9f0341f22b1b6975a44df", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.16.0.tgz", "fileCount": 3, "integrity": "sha512-4fDVBAfWYlw2CtYgHEWarAYSozTx5OYLsSM/cdGW7H51FwI10DaGnjKgdqWyWXY/VjugelzriCiKf1UdM20Bxg==", "signatures": [{"sig": "MEQCICVlsSJnSDgLfnigWtFa+1NDXBMH8OIZ2x6ATdZVDMUAAiAYgOxBXX+W5Z3KdlUW13Q7qVTnHZcENKgavtRLMiE6CQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687612}, "main": "./rollup.android-arm-eabi.node", "gitHead": "38fe70780cb7e374b47da99e3a3dca6b2a2170d2", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.16.0_1713674525836_0.665749255393892", "host": "s3://npm-registry-packages"}}, "4.16.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.16.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.16.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ad76cc870b1e2bc4476dfc02b82e20cea272a09d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.16.1.tgz", "fileCount": 3, "integrity": "sha512-92/y0TqNLRYOTXpm6Z7mnpvKAG9P7qmK7yJeRJSdzElNCUnsgbpAsGqerUboYRIQKzgfq4pWu9xVkgpWLfmNsw==", "signatures": [{"sig": "MEUCIQCtfXy3WrHkQgWel8+Tk+9CtSQ6ymExhuYK5JG2ChIlVgIgK0Ie/dl+/lf2gT6VEEzh/MAVdGw1u6kDuF5f+ezXuRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687612}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5d8019b901e98cc8895751a23e5edfc9135b1a35", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.16.1_1713724205841_0.26931064064124466", "host": "s3://npm-registry-packages"}}, "4.16.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.16.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.16.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "29b7b3c70ddf532fe6dcf859cbfc3e4714c34842", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.16.2.tgz", "fileCount": 3, "integrity": "sha512-VGodkwtEuZ+ENPz/CpDSl091koMv8ao5jHVMbG1vNK+sbx/48/wVzP84M5xSfDAC69mAKKoEkSo+ym9bXYRK9w==", "signatures": [{"sig": "MEUCIQD3bVRPIyrtePjnIRGkGiMVu8lTPLJXSoHkYnqS3cmL0wIgZxh0iAgXj66btzxZ4h83Hnv8ioWJc9oNFD5l02sYvF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687612}, "main": "./rollup.android-arm-eabi.node", "gitHead": "18839eb234f79adc44a591e355fd7b3243a4cd21", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.16.2_1713799163383_0.20145196924083075", "host": "s3://npm-registry-packages"}}, "4.16.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.16.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.16.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "823a1af37014adb462156e3296c5f3595a82a5b8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.16.3.tgz", "fileCount": 3, "integrity": "sha512-1ACInKIT0pXmTYuPoJAL8sOT0lV3PEACFSVxnD03hGIojJ1CmbzZmLJyk2xew+yxqTlmx7xydkiJcBzdp0V+AQ==", "signatures": [{"sig": "MEUCIQDRbXIekmnBetq8hFDeQSK5sdOMvH1p2CTc9yUWRI9c5gIgWSnpJ0do5p0LPFX6vvnVbhtSIHxIK9u1d/NcNdhhWKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687612}, "main": "./rollup.android-arm-eabi.node", "gitHead": "b9a62fd4cf28538d7c3b268eb25e709b45d44cce", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.16.3_1713849159825_0.5302757315771103", "host": "s3://npm-registry-packages"}}, "4.16.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.16.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.16.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5e8930291f1e5ead7fb1171d53ba5c87718de062", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.16.4.tgz", "fileCount": 3, "integrity": "sha512-GkhjAaQ8oUTOKE4g4gsZ0u8K/IHU1+2WQSgS1TwTcYvL+sjbaQjNHFXbOJ6kgqGHIO1DfUhI/Sphi9GkRT9K+Q==", "signatures": [{"sig": "MEUCICYjwOOP+ng09jtgNyhNmdhS4w6BoF6qMXcKGYyOUDDlAiEAzSHWUZlLfOZreDlGT+c0ipX+a0NA7mWuzHdF3y8KKsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687612}, "main": "./rollup.android-arm-eabi.node", "gitHead": "1c404fa352b70007066e94ff4c1981f8046f8cef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.16.4_1713878112600_0.044438037707043465", "host": "s3://npm-registry-packages"}}, "4.17.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.17.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.17.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "bc8d14ba7673d901a0d5b4b5061dd4f843ab1797", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.17.0.tgz", "fileCount": 3, "integrity": "sha512-nNvLvC2fjC+3+bHYN9uaGF3gcyy7RHGZhtl8TB/kINj9hiOQza8kWJGZh47GRPMrqeseO8U+Z8ElDMCZlWBdHA==", "signatures": [{"sig": "MEUCIFd8NK5eBT+5yJ2e1XG2dQb4QbCVkdDaLpUyJzWJDzHrAiEAuGasIDP37s6kdToRuCy72HfwfPG+hwOC82fA0UyPDCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687804}, "main": "./rollup.android-arm-eabi.node", "gitHead": "91352494fc722bcd5e8e922cd1497b34aec57a67", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.17.0_1714217397140_0.3056014185361069", "host": "s3://npm-registry-packages"}}, "4.17.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.17.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.17.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4a5135e88d522dbf85c4ca43ad14af9dab27bd07", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.17.1.tgz", "fileCount": 3, "integrity": "sha512-P6Wg856Ou/DLpR+O0ZLneNmrv7QpqBg+hK4wE05ijbC/t349BRfMfx+UFj5Ha3fCFopIa6iSZlpdaB4agkWp2Q==", "signatures": [{"sig": "MEUCIDCUyI/OJpJ/cyC5E7pq6YgGkcNu6DwbByXMY3oO+CzbAiEAy2JwTz7TQznoWFw19RlTcXuvQ/7Q2Zae0U+YWpf9loQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687804}, "main": "./rollup.android-arm-eabi.node", "gitHead": "dbf0a2e5d3c3eae09ac4d502646d0ecab63f40fd", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.17.1_1714366679913_0.36084419518516464", "host": "s3://npm-registry-packages"}}, "4.17.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.17.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.17.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1a32112822660ee104c5dd3a7c595e26100d4c2d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.17.2.tgz", "fileCount": 3, "integrity": "sha512-NM0jFxY8bB8QLkoKxIQeObCaDlJKewVlIEkuyYKm5An1tdVZ966w2+MPQ2l8LBZLjR+SgyV+nRkTIunzOYBMLQ==", "signatures": [{"sig": "MEQCIAH4dFM+VznAy1wb7fVnhuiTwS3Tbyc7cSuLCEaSexxkAiBMnGuVpZcvIVqmwgSudQ56lJxIokiHssEpb+Ds/rA01w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687804}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5e955a1c2c5e080f80f20f650da9b44909d65d56", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.17.2_1714453252943_0.599591384219432", "host": "s3://npm-registry-packages"}}, "4.18.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.18.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.18.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "bbd0e616b2078cd2d68afc9824d1fadb2f2ffd27", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.18.0.tgz", "fileCount": 3, "integrity": "sha512-Tya6xypR10giZV1XzxmH5wr25VcZSncG0pZIjfePT0OVBvqNEurzValetGNarVrGiq66EBVAFn15iYX4w6FKgQ==", "signatures": [{"sig": "MEQCIDgCpbPWQMCuPimrAGHT696bjZyu5EvGzqc0XsWeKxNOAiBoJ6EZ1yuLRTICU8PIvW9JmfQXZy7/dW/xvpqzOf+MpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1696700}, "main": "./rollup.android-arm-eabi.node", "gitHead": "bb6f069ea3623b0297ef3895f2dcb98a2ca5ef58", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.18.0_1716354230384_0.7329607390790089", "host": "s3://npm-registry-packages"}}, "4.18.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.18.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.18.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f0da481244b7d9ea15296b35f7fe39cd81157396", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.18.1.tgz", "fileCount": 3, "integrity": "sha512-lncuC4aHicncmbORnx+dUaAgzee9cm/PbIqgWz1PpXuwc+sa1Ct83tnqUDy/GFKleLiN7ZIeytM6KJ4cAn1SxA==", "signatures": [{"sig": "MEUCIQCHpDPpAIv3xNF83cr61esB/lnxf3L4tFTM097Hklpv0AIgJDHDBHNUaUdmbEN4NoPwenNwL+A8rfpjd+WxwQZSlmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1585076}, "main": "./rollup.android-arm-eabi.node", "gitHead": "21f9a4949358b60801c948cd4777d7a39d9e6de0", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.18.1_1720452318147_0.9028020090293705", "host": "s3://npm-registry-packages"}}, "4.19.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.19.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.19.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3d9fd50164b94964f5de68c3c4ce61933b3a338d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.19.0.tgz", "fileCount": 3, "integrity": "sha512-JlPfZ/C7yn5S5p0yKk7uhHTTnFlvTgLetl2VxqE518QgyM7C9bSfFTYvB/Q/ftkq0RIPY4ySxTz+/wKJ/dXC0w==", "signatures": [{"sig": "MEUCIQDXQykphGoOQ9itU4zdpt+nxUTLZd1Jy+/PYn9EHnx2bQIgYgZrmC+n3LfS+B6ju56rUED6+b2a0/Lllvwmqjq4Ihw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1558324}, "main": "./rollup.android-arm-eabi.node", "gitHead": "28546b5821efcb72c2eb05f422d986524647a0e3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.19.0_1721454380789_0.6150882971933458", "host": "s3://npm-registry-packages"}}, "4.19.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.19.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.19.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7746deb85e4a8fb54fbfda8ac5c102692f102476", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.19.1.tgz", "fileCount": 3, "integrity": "sha512-XzqSg714++M+FXhHfXpS1tDnNZNpgxxuGZWlRG/jSj+VEPmZ0yg6jV4E0AL3uyBKxO8mO3xtOsP5mQ+XLfrlww==", "signatures": [{"sig": "MEYCIQDNimWAnPb+d3XYY4B1OP3Oky4i8f9u7Isfn74ckUArlAIhAL0jaFapvira6tIM7whjeQotmZi9pRO0zTKfS6gT+xtt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1524980}, "main": "./rollup.android-arm-eabi.node", "gitHead": "8b967917c2923dc6a02ca1238261387aefa2cb2f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.19.1_1722056047067_0.12457631912183809", "host": "s3://npm-registry-packages"}}, "4.19.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.19.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.19.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6b991cb44bf69e50163528ea85bed545330ba821", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.19.2.tgz", "fileCount": 3, "integrity": "sha512-OHflWINKtoCFSpm/WmuQaWW4jeX+3Qt3XQDepkkiFTsoxFc5BpF3Z5aDxFZgBqRjO6ATP5+b1iilp4kGIZVWlA==", "signatures": [{"sig": "MEUCIHOj9ccNsZDB7+r/f5klIx0jMWmlDCLaMNoPq1X9f8XeAiEA8G7a0w0ArJSUDi2+cT8QcYGLUNz0Sb221ZzOWv9TRZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1524980}, "main": "./rollup.android-arm-eabi.node", "gitHead": "39955e55dbc12ec379a21efcf8fc21e55ec6ce3a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.19.2_1722501179846_0.21169883766886288", "host": "s3://npm-registry-packages"}}, "4.20.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.20.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.20.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c3f5660f67030c493a981ac1d34ee9dfe1d8ec0f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.20.0.tgz", "fileCount": 3, "integrity": "sha512-TSpWzflCc4VGAUJZlPpgAJE1+V60MePDQnBd7PPkpuEmOy8i87aL6tinFGKBFKuEDikYpig72QzdT3QPYIi+oA==", "signatures": [{"sig": "MEUCIQC7h6eV5EVcrqP5jde2JBNXVYlZ4MieDtuNUvxTXkmJNgIgEyXZphRh1eys/ST1DlMRrCjSdekSMgcVAnD0JHch1Kg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1530088}, "main": "./rollup.android-arm-eabi.node", "gitHead": "df12edfea6e9c1a71bda1a01bed1ab787b7514d5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.20.0_1722660536855_0.38979090897946933", "host": "s3://npm-registry-packages"}}, "4.21.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.21.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.21.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d941173f82f9b041c61b0dc1a2a91dcd06e4b31e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.21.0.tgz", "fileCount": 3, "integrity": "sha512-WTWD8PfoSAJ+qL87lE7votj3syLavxunWhzCnx3XFxFiI/BA/r3X7MUM8dVrH8rb2r4AiO8jJsr3ZjdaftmnfA==", "signatures": [{"sig": "MEUCIA6wIIEk68cobAncIMfkYsyanAs6GvempB7Vv5d/IhC5AiEAipT5y3qpy0aP/MoDri/r1FuSqlec8/soABWXqu6M1x4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1485032}, "main": "./rollup.android-arm-eabi.node", "gitHead": "c4bb050938778bcbe7b3b3ea3419f7fa70d60f5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.21.0_1723960540332_0.46047319371965556", "host": "s3://npm-registry-packages"}}, "4.21.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.21.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.21.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c3a7938551273a2b72820cf5d22e54cf41dc206e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.21.1.tgz", "fileCount": 3, "integrity": "sha512-2thheikVEuU7ZxFXubPDOtspKn1x0yqaYQwvALVtEcvFhMifPADBrgRPyHV0TF3b+9BgvgjgagVyvA/UqPZHmg==", "signatures": [{"sig": "MEUCIQCR4tmdu437BX+6pNg4OLjc6bj77ZzlTp3WZN9vrNpEnQIgRpUC4MZwFeBkg3/On89Z9jFMN/6Z8rkXGjjxMkTKa6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1480360}, "main": "./rollup.android-arm-eabi.node", "gitHead": "c33c6ceb7da712c3d14b67b81febf9303fbbd96c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.21.1_1724687660251_0.4316488116014392", "host": "s3://npm-registry-packages"}}, "4.21.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.21.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.21.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0412834dc423d1ff7be4cb1fc13a86a0cd262c11", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.21.2.tgz", "fileCount": 3, "integrity": "sha512-fSuPrt0ZO8uXeS+xP3b+yYTCBUd05MoSp2N/MFOgjhhUhMmchXlpTQrTpI8T+YAwAQuK7MafsCOxW7VrPMrJcg==", "signatures": [{"sig": "MEQCIH1o+bRrq3uAtVmLnc6bTMHVhqgnkHkUgR8Y9LgSaEdoAiBO0LtbreUJSJaAyY6HCn8gs9d9Q7mH7X4FApgwP0ctRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1484712}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f83b3151e93253a45f5b8ccb9ccb2e04214bc490", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.21.2_1725001472106_0.867607247060693", "host": "s3://npm-registry-packages"}}, "4.21.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.21.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.21.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "155c7d82c1b36c3ad84d9adf9b3cd520cba81a0f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.21.3.tgz", "fileCount": 3, "integrity": "sha512-MmKSfaB9GX+zXl6E8z4koOr/xU63AMVleLEa64v7R0QF/ZloMs5vcD1sHgM64GXXS1csaJutG+ddtzcueI/BLg==", "signatures": [{"sig": "MEYCIQD3WjHvxe5/qUEZZQEb28W4kkHw2+TeVpVRm1w5R6qBgQIhAJF/aQe7Q07w1oo38scfiDsmpKApkPD6iLiz8e/CezUv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1463160}, "main": "./rollup.android-arm-eabi.node", "gitHead": "9f5a735524a5c56ba61a8dc6989374917f5aceb1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.21.3_1726124756063_0.3159630746328499", "host": "s3://npm-registry-packages"}}, "4.22.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e8c16c336f060b4cb592f62eb4f0e543d79d51fe", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.0.tgz", "fileCount": 3, "integrity": "sha512-/IZQvg6ZR0tAkEi4tdXOraQoWeJy9gbQ/cx4I7k9dJaCk9qrXEcdouxRVz5kZXt5C2bQ9pILoAA+KB4C/d3pfw==", "signatures": [{"sig": "MEUCICWtRZ71vD2FPjLo7DUxAffs82+4EB/wR1rFkfaHjVolAiEAo6Fg+LIpx6SPzGGxhOTQ6S/XUnvPKvZuIPZnZ046SNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1459136}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5e7a3631a28a863ddb97a64189c3b76eec9983ca", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.0_1726721737812_0.9106263658766554", "host": "s3://npm-registry-packages"}}, "4.22.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8ee3eeb39485be9ca36e4b398051c5969d0eda46", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.1.tgz", "fileCount": 3, "integrity": "sha512-GrXxNVBes13Q3wSBjdZlmu4VulFhfNs1eP2/pX5dmx6cE1XgfV2/BfqdGt4d2Z7Zqp+qnYSf7zvIB4buc+2DwA==", "signatures": [{"sig": "MEQCIEYon/i3dKaH/Cl7O+g+/dQ2mU3XFRvhkf42AK+sdYY/AiBciTdRUas0PmeZErge/+Tt0vTIgszxQFwvX/Ob+6XG9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1465664}, "main": "./rollup.android-arm-eabi.node", "gitHead": "76e962daca5b7352bf199c28fa0a10ad4745c5e7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.1_1726820519754_0.8262395440814558", "host": "s3://npm-registry-packages"}}, "4.22.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4e0c4c462692ecb7ae2b008f25af4cced05ac4f9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.2.tgz", "fileCount": 3, "integrity": "sha512-8Ao+EDmTPjZ1ZBABc1ohN7Ylx7UIYcjReZinigedTOnGFhIctyGPxY2II+hJ6gD2/vkDKZTyQ0e7++kwv6wDrw==", "signatures": [{"sig": "MEYCIQD84gzPZ2peWWY/IWSKt4ZUT2Rrjs7b4Q0ygvWOysDzBgIhAPWYheteZ+zyUniTLf6yIdiVWDiyx0Z61nupTJiUOH7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1465664}, "main": "./rollup.android-arm-eabi.node", "gitHead": "b86ffd776cfa906573d36c3f019316d02445d9ef", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.2_1726824831647_0.12262979539970487", "host": "s3://npm-registry-packages"}}, "4.22.3-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.3-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.3-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0656f4d9019d973453a35aaddc5bdb1eebefc701", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.3-0.tgz", "fileCount": 3, "integrity": "sha512-QLuEdhwlTa55IfwF5P7fsH45qMY1yuRkxuAYcC/1isvivVurEdqbG3OLpDnqF8DymwZ9s/seUVVasAjDd3wczw==", "signatures": [{"sig": "MEYCIQDGfI9vMkNBqlJKew2Pe48EURdcKUP39aK42aJj+oPWFwIhAIBwwhbpoEl2LIccXu6WIHkjOMUNMLcFlX2TYMSC51OY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1465666}, "main": "./rollup.android-arm-eabi.node", "gitHead": "9e04b4849db9134473b84e4b94aa353ae4fd8754", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.3-0_1726843685180_0.04974554443173029", "host": "s3://npm-registry-packages"}}, "4.22.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "01f8c622accc278f4ffb3358f306120cdafb7be3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.3.tgz", "fileCount": 3, "integrity": "sha512-Po+UujLih7BSLvAWcPWTKgtmaFdzkzgPXoJ2w4vNPbIgUJEjhzdNgei/2X6RyWExXuhr1c68kHPySdXH8F+EWA==", "signatures": [{"sig": "MEYCIQDErnaGPvBTIpTQMOkSIYfYtYFDFZ6N9Q3JqCaTUuC4DAIhAJ6Vw5KSYSefalVdAwbUilnhF8Mo3r4se9UZcWZ1OHbp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1465664}, "main": "./rollup.android-arm-eabi.node", "gitHead": "e1cba8e84a0c01dd16580ba7a2536a988dfb4e18", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.3_1726894995886_0.5411297748168382", "host": "s3://npm-registry-packages"}}, "4.22.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8b613b9725e8f9479d142970b106b6ae878610d5", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.4.tgz", "fileCount": 3, "integrity": "sha512-Fxamp4aEZnfPOcGA8KSNEohV8hX7zVHOemC8jVBoBUHu5zpJK/Eu3uJwt6BMgy9fkvzxDaurgj96F/NiLukF2w==", "signatures": [{"sig": "MEUCIAWqJgf4/9s0Uq9RJcW/IX6XSQ7EAAe5WuoDw/UU2m5NAiEA5/sSMhjfcIzttOyiV5bPB9WR/ABXcyeWl16jxNO+rTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1465664}, "main": "./rollup.android-arm-eabi.node", "gitHead": "79c0aba353ca84c0e22c3cfe9eee433ba83f3670", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.4_1726899088023_0.697287762722947", "host": "s3://npm-registry-packages"}}, "4.22.5": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.22.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.22.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e0f5350845090ca09690fe4a472717f3b8aae225", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.22.5.tgz", "fileCount": 3, "integrity": "sha512-SU5cvamg0Eyu/F+kLeMXS7GoahL+OoizlclVFX3l5Ql6yNlywJJ0OuqTzUx0v+aHhPHEB/56CT06GQrRrGNYww==", "signatures": [{"sig": "MEUCICmm3CtdOueGKMkvY7URmsCNH0XtLWi+/cLNgXVikr6AAiEA1120h6QivWg3827vApEOaCCP4yoAvUxkT+3uOrBM06s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1458176}, "main": "./rollup.android-arm-eabi.node", "gitHead": "bc7780c322e134492f40a76bf64afe561670425c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.22.5_1727437702627_0.13552084919575003", "host": "s3://npm-registry-packages"}}, "4.23.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.23.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.23.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "17c381804b84fecee9dd8588e93d9b2a4544ea42", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.23.0.tgz", "fileCount": 3, "integrity": "sha512-8OR+Ok3SGEMsAZispLx8jruuXw0HVF16k+ub2eNXKHDmdxL4cf9NlNpAzhlOhNyXzKDEJuFeq0nZm+XlNb1IFw==", "signatures": [{"sig": "MEQCIC+NbuP77ZyhF2FacwlNr9gWzClIyyfgMLBdxdM70md9AiB30FtnScx130q5Hx0xxvmFaN0wk+ECLQ3BGtQem23Baw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1458176}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ed98e0821e6ad064839f0af46ceca061adbe3f14", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.23.0_1727766613778_0.5810929140188132", "host": "s3://npm-registry-packages"}}, "4.24.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.24.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.24.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1661ff5ea9beb362795304cb916049aba7ac9c54", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.24.0.tgz", "fileCount": 3, "integrity": "sha512-Q6HJd7Y6xdB48x8ZNVDOqsbh2uByBhgK8PiQgPhwkIw/HC/YX5Ghq2mQY5sRMZWHb3VsFkWooUVOZHKr7DmDIA==", "signatures": [{"sig": "MEUCIFwdH4ausZy80SXFN1F9FlKLf0xUQe82vBnKmjeoUTIlAiEAsZKqe+ypZJJr81EP9d7w4Uom2XFNdYe/d3A9EsD5jns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1463488}, "main": "./rollup.android-arm-eabi.node", "gitHead": "d3c000f4fd453e39a354299f0cfaa6831f56d7d8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.24.0_1727861847556_0.1405291741604946", "host": "s3://npm-registry-packages"}}, "4.24.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.24.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.24.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d82149b2e4f1538528b22ba2733eb526a8510c44", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.24.1.tgz", "fileCount": 3, "integrity": "sha512-j2llrtCTwNu68yp1wybgkTUW8CrR8AZvGZzIO/qwNAetVP3FHidylyz1s0dU2zXG9uqqpoUIhWKmMypGMcdM2Q==", "signatures": [{"sig": "MEUCIHpBkRMww8zK4MYeEcHcCmvWevHP5/KEoTVnWOlJ+1hZAiEArOK8m0+JfnuDKfk565On9x0vgoJMAYStDXYhlOO9Yak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617788}, "main": "./rollup.android-arm-eabi.node", "gitHead": "88a54d892dacbb0efdbcade263a32d9df1a77b37", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.24.1_1730011391072_0.4846117970234807", "host": "s3://npm-registry-packages"}}, "4.24.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.24.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.24.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "07db37fcd9d401aae165f662c0069efd61d4ffcc", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.24.2.tgz", "fileCount": 3, "integrity": "sha512-ufoveNTKDg9t/b7nqI3lwbCG/9IJMhADBNjjz/Jn6LxIZxD7T5L8l2uO/wD99945F1Oo8FvgbbZJRguyk/BdzA==", "signatures": [{"sig": "MEYCIQDB7g2irMlFlXMiR22MU2rjsINWVlWF6sag1slY5o8l8AIhAPzF53boHA6n3Illvj2dQbmwTPKttKqeToNqCBymolHo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617788}, "main": "./rollup.android-arm-eabi.node", "gitHead": "32d0e7dae85121ac0850ec28576a10a6302f84a9", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.24.2_1730043618088_0.33002935620935103", "host": "s3://npm-registry-packages"}}, "4.25.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.25.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.25.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8b5c0ab5877b76adfa140bc15c11b30677761f30", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.25.0-0.tgz", "fileCount": 3, "integrity": "sha512-MAg+96DmKUyjhNH5XOJInG13rqDNk9S6FsCBEeicsWfk70jY2GymNeLSuSZrxM1EyeHM6tcH6IbznW+3npDPZg==", "signatures": [{"sig": "MEUCIGKfSdKkrq9e46n0KjVGRmrsQsgRWvokjjAh5wUEvsvuAiEAhOjQpUsYwKQivKYHw2qx/mWLjIJiOMSJW0g9J55F0sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617790}, "main": "./rollup.android-arm-eabi.node", "gitHead": "b7fcaba12e863db516f39de74c1eacfe5329a5c3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.25.0-0_1730182521169_0.17577257640454125", "host": "s3://npm-registry-packages"}}, "4.24.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.24.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.24.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "49a2a9808074f2683667992aa94b288e0b54fc82", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.24.3.tgz", "fileCount": 3, "integrity": "sha512-ufb2CH2KfBWPJok95frEZZ82LtDl0A6QKTa8MoM+cWwDZvVGl5/jNb79pIhRvAalUu+7LD91VYR0nwRD799HkQ==", "signatures": [{"sig": "MEUCIQDxi9qZsxmSkFU8p2U/Xm/OuEQ3Z3hgfE3MoNHppfMqqgIgVl8mVCjDfdVXeAbMZy6fwBQz/2TmFTdSOTxbigx8pY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617788}, "main": "./rollup.android-arm-eabi.node", "gitHead": "69353a84d70294ecfcd5e1ab8e372e21e94c9f8e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.24.3_1730211258508_0.03503928942649548", "host": "s3://npm-registry-packages"}}, "4.24.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.24.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.24.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c460b54c50d42f27f8254c435a4f3b3e01910bc8", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.24.4.tgz", "fileCount": 3, "integrity": "sha512-jfUJrFct/hTA0XDM5p/htWKoNNTbDLY0KRwEt6pyOA6k2fmk0WVwl65PdUdJZgzGEHWx+49LilkcSaumQRyNQw==", "signatures": [{"sig": "MEYCIQCxmFlHjyOIZZ2bSgfxTjF8nt+6OER9YR4+6nZpuUrdGQIhAIf1n5MAEpkxMJLxTF5ZhLOaAH8EqsXTpG5+XnII+pM7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1620284}, "main": "./rollup.android-arm-eabi.node", "gitHead": "cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.24.4_1730710039142_0.9047887450375247", "host": "s3://npm-registry-packages"}}, "4.25.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.25.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.25.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3e7eda4c0c1de6d2415343002d742ff95e38dca7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.25.0.tgz", "fileCount": 3, "integrity": "sha512-CC/ZqFZwlAIbU1wUPisHyV/XRc5RydFrNLtgl3dGYskdwPZdt4HERtKm50a/+DtTlKeCq9IXFEWR+P6blwjqBA==", "signatures": [{"sig": "MEUCIE5IUywQL/BqwNrDPpdTIjqdXlX5eYIC9+j/mZRes08+AiEAsUhSYJbVu+CTjQIXvTdI6c/4NofSbmL9BufzPLdK3Wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1615356}, "main": "./rollup.android-arm-eabi.node", "gitHead": "42e587e0e37bc0661aa39fe7ad6f1d7fd33f825c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.25.0_1731141452220_0.9140574658009548", "host": "s3://npm-registry-packages"}}, "4.26.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.26.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.26.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f221c519a6efb5d3652bff32351522e0fb98e392", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.26.0.tgz", "fileCount": 3, "integrity": "sha512-gJNwtPDGEaOEgejbaseY6xMFu+CPltsc8/T+diUTTbOQLqD+bnrJq9ulH6WD69TqwqWmrfRAtUv30cCFZlbGTQ==", "signatures": [{"sig": "MEYCIQCVrDnXNgAZk+y33+KnkfVnlYqRfjekLAh0+xmsTYviDAIhAKcoQHpQovqSkVehK3Nh+UmGoH4NFTdhcitw0VwNG6yb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1615356}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ae1d14b7855ff6568a6697d37271a5eb4d8e2d3e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.26.0_1731480309621_0.8709250369574839", "host": "s3://npm-registry-packages"}}, "4.27.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d9c9cb4b60cbedce7acac12dfa6c82ff9cf4fbc6", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.0-0.tgz", "fileCount": 3, "integrity": "sha512-2LBxbOds8K3dfU6l123qQpwGNHZYpRAzLb42ejM6Zf3e6Ok8lid/NkmF7QarlTuTQGY1i2UrBIWtPsRh97qJdw==", "signatures": [{"sig": "MEQCIC2HrNlMnUhvoJ6GyO7wnGLsLUlvhkTuK1IMqQgLlPyHAiB+i3NNVMkpyeV7k5gVI8/jN0AJaEY/CEgALraEqQDroA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1615358}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5e6074f07843bcbcf26b916c557fdfd81d2adece", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.0-0_1731481403461_0.19628104835396654", "host": "s3://npm-registry-packages"}}, "4.27.0-1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c1792b77ef2ce22b85d9581507b37526ce90a73a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.0-1.tgz", "fileCount": 3, "integrity": "sha512-EMy6pWUouQChzuJtIR7GGM+ukhaIsFcAut7gxIt0KpUnKGOAC0ujZDN5nxSlVu/zv12ovY2vAfZTKko1TqhbtQ==", "signatures": [{"sig": "MEUCIGXGAMuss2oJEU7R430TZM8pWs6yR6KCBW5hRGc+oUu4AiEAgyN985UZZOh9HVuUroJSTQ1qGJgWvs4vImf42hEHGqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1615358}, "main": "./rollup.android-arm-eabi.node", "gitHead": "81f5021d7d7e2a488639dc036f2334995b3761fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.0-1_1731566001176_0.5312752649823971", "host": "s3://npm-registry-packages"}}, "4.27.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "fa721887182c2ae77cce47e2b5ad7bf56003f371", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.0.tgz", "fileCount": 3, "integrity": "sha512-e312hTjuM89YLqlcqEs7mSvwhxN5pgXqRobUob7Jsz1wDQlpAb2WTX4jzvrx5NrL1h2SE4fGdHSNyPxbLfzyeA==", "signatures": [{"sig": "MEYCIQD/4l7ZVpCrCzPiRe2kVeTgMTJZf5q/nJ6CdETNdCaJVQIhAPxOkG4EQHAXm8pQSjc88coiSW0kAEYoSUiMy19RU1J6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618236}, "main": "./rollup.android-arm-eabi.node", "gitHead": "c035068dfebeb959a35a8acf3ff008a249e2af73", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.0_1731667245775_0.5532279974809882", "host": "s3://npm-registry-packages"}}, "4.27.1-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.1-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.1-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c37ba31e2f00b741ad8b8f8b48b14c463c766a1e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.1-0.tgz", "fileCount": 3, "integrity": "sha512-9COW+x2fpnTpnFXYwwKVjpu6Qtu5BtmiHfniUrRwIPlSbh/XGfwsNqCtfqAqVXOuHFscucVMpq7GqOgYzr2xQQ==", "signatures": [{"sig": "MEUCIQCbSi8QN4XxRJVGc55iH0Wjake1vn356OSugVBSRG648AIgA+uhKnRqoc6evjr2xp5WMLfdn2eWjtlSeYlCKcm+hBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618238}, "main": "./rollup.android-arm-eabi.node", "gitHead": "a80f6a94d720224a44331d5a50745e9887619703", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.1-0_1731677298302_0.8778447242808589", "host": "s3://npm-registry-packages"}}, "4.27.1-1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.1-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.1-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "a1d4d34c18d180703382bb4921807328403459e1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.1-1.tgz", "fileCount": 3, "integrity": "sha512-1cz5JkXzPwFK0JkfRJyGzY40kEG/8bdeFCeotLDS3iAfQ2iU2LmDtqpXVDYvbjfIfpp0mRTGpLObZ4SlqVu2ig==", "signatures": [{"sig": "MEQCIH2rO/sWox/gbxLZSuLlHXNi4MZMnUI3QvMvjEmQJSWWAiA/dcfbGLy85ZSTxTCFAPTsAHZ0moF9dkGxEdKfkgqt0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618238}, "main": "./rollup.android-arm-eabi.node", "gitHead": "892ce0206dbf4fbf656b2f0563ef803c5e5a0016", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.1-1_1731685093863_0.23707250483188247", "host": "s3://npm-registry-packages"}}, "4.27.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c8d7ce3836b9e5f5edcd6e56a21b1ee62c009970", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.1.tgz", "fileCount": 3, "integrity": "sha512-Y/i1fVMnP6PEllrv2yMFWIxq5axF3cIzeLHqKwKYd9FgIq0Py1qKWoHoosbxHmsokbLJtfjyH7/ebY6KTAIARQ==", "signatures": [{"sig": "MEUCIHTqBpHQ6V0sDiVLQZsdpYJ4FmbDTlmLmqAn5ie4wt24AiEAu1vtkVaILHwCGb2cZqRoG0PHIDTG9WDLNhR6wVzXyJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618236}, "main": "./rollup.android-arm-eabi.node", "gitHead": "aaf38b725dd142b1da4190a91de8b04c006fead5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.1_1731686871637_0.03885062127379957", "host": "s3://npm-registry-packages"}}, "4.27.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9fa3640a1f435bc3800b8d703d3f18823962b045", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.2.tgz", "fileCount": 3, "integrity": "sha512-Tj+j7Pyzd15wAdSJswvs5CJzJNV+qqSUcr/aCD+jpQSBtXvGnV0pnrjoc8zFTe9fcKCatkpFpOO7yAzpO998HA==", "signatures": [{"sig": "MEUCIHudclgNpOO5TJFYHUpg/D+MP6VgsjAmHBSwinFInCEtAiEAqBzuEisshHA9kr3X9/mo8tb8+odALjMbk3ob2g2ssdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618236}, "main": "./rollup.android-arm-eabi.node", "gitHead": "a503a4dd9982bf20fd38aeb171882a27828906ae", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.2_1731691214383_0.1460509544614279", "host": "s3://npm-registry-packages"}}, "4.27.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ab2c78c43e4397fba9a80ea93907de7a144f3149", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.3.tgz", "fileCount": 3, "integrity": "sha512-EzxVSkIvCFxUd4Mgm4xR9YXrcp976qVaHnqom/Tgm+vU79k4vV4eYTjmRvGfeoW8m9LVcsAy/lGjcgVegKEhLQ==", "signatures": [{"sig": "MEUCIQD2RoBfCKTl3nfNIIxftDlT9PkFpvngT4FtPpmMtf1PbQIgL6H9F3QZ2G/qJ2yFJzSpiCCBnnWbbMoeJ0WpQ00sULw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618236}, "main": "./rollup.android-arm-eabi.node", "gitHead": "7c0b1f8810013b5a351a976df30a6a5da4fa164b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.3_1731947986573_0.9355461098960014", "host": "s3://npm-registry-packages"}}, "4.27.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.27.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.27.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e3c9cc13f144ba033df4d2c3130a214dc8e3473e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.27.4.tgz", "fileCount": 3, "integrity": "sha512-2Y3JT6f5MrQkICUyRVCw4oa0sutfAsgaSsb0Lmmy1Wi2y7X5vT9Euqw4gOsCyy0YfKURBg35nhUKZS4mDcfULw==", "signatures": [{"sig": "MEUCIQCjd5Uh4NbWUfaEdnkZ6SLl0epb0QZhvpC6mE252+2z5AIgINGp83r/kpPRufP5X8XBn3PpK7HtwpY2YpbHswCxNJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617724}, "main": "./rollup.android-arm-eabi.node", "gitHead": "e805b546405a4e6cfccd3fe73e9f4df770023824", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.27.4_1732345230291_0.46303994169837837", "host": "s3://npm-registry-packages"}}, "4.28.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.28.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.28.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "462e7ecdd60968bc9eb95a20d185e74f8243ec1b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.28.0.tgz", "fileCount": 3, "integrity": "sha512-wLJuPLT6grGZsy34g4N1yRfYeouklTgPhH1gWXCYspenKYD0s3cR99ZevOGw5BexMNywkbV3UkjADisozBmpPQ==", "signatures": [{"sig": "MEQCIAJftvg1REkCqxdjZyFS7Tdu9eM+P66FKC4zf9P54LdaAiA3WuHMTlTjyh31B8+mO5dxEkMdvn+CxG5PF0+z9rVk6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1618604}, "main": "./rollup.android-arm-eabi.node", "gitHead": "0595e433edec3608bfc0331d8f02912374e7f7f7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.28.0_1732972557988_0.7609922313642872", "host": "s3://npm-registry-packages"}}, "4.28.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.28.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.28.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7f4c4d8cd5ccab6e95d6750dbe00321c1f30791e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.28.1.tgz", "fileCount": 3, "integrity": "sha512-2aZp8AES04KI2dy3Ss6/MDjXbwBzj+i0GqKtWXgw2/Ma6E4jJvujryO6gJAghIRVz7Vwr9Gtl/8na3nDUKpraQ==", "signatures": [{"sig": "MEQCICfkg6dgXnFH39t8/mSn6ohrMAlZKuWN1M5PJWTRYJ64AiBHloP32erYPnnmXaIkoGyY9v1F3OG8mwnZLLYMOM77Sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1610028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "e60fb1c5d4e54ed5257495215eeda1bb43cf54ba", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.28.1_1733485506575_0.990951747164893", "host": "s3://npm-registry-packages"}}, "4.29.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.29.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.29.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "67ac01a9d46a5883e3f6e3938222b84eb892296b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.29.0-0.tgz", "fileCount": 3, "integrity": "sha512-jBMJrZqEt39DflBKj17MP2qk3Adjcnb9nBZU7xGTOA+7Uk4a+NNhNlbbLsfR6fNY5s05gwGC82zGvVYWoz5fCw==", "signatures": [{"sig": "MEUCIGfE/zg1MQXt+wt8jBupVwt6xbUTl5FICMCL8Hj/UD91AiEAsn4wtBVGSYDYVaE1A3vVtNXq6yRUVp5nhasY7+fiqHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1612206}, "main": "./rollup.android-arm-eabi.node", "gitHead": "879d03d68890f365f880e30c69b58377b8743407", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.29.0-0_1734331204453_0.48122237778469645", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.29.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.29.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8c5a985bd1b8ee4e835b942574049dceb764af92", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.29.0-1.tgz", "fileCount": 3, "integrity": "sha512-6kLnetNDq1p15zdGMrF65aKApE0Om5+2GGqV9RlwaIKYVNaSYsoy6CfJ+CDdAVDsL9EGilMPaL2sU51aCMrlpA==", "signatures": [{"sig": "MEUCIBW+Di4v4Qe/YvVlE+5ZYYyqvFo/NAS/ySwWyFC5gT5jAiEAiP13+hcfMxEpiz0iWPuRLeRRfAKrzW/tmCEPevBA6aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1612206}, "main": "./rollup.android-arm-eabi.node", "gitHead": "fa5064084196636acd98263f95ffea59f8362e32", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.29.0-1_1734590261204_0.0791999475985834", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0-2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.29.0-2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.29.0-2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "62d8e1fcf121b860757c367800c8971bf00380a7", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.29.0-2.tgz", "fileCount": 3, "integrity": "sha512-oXxyC7+PLoW+MO5mHJqVsssDUEpDuzKwScwbJ6+wEKpjEMwecJntspWPoO5dnbhohPfTNoTYcZJkKHRDFHUwtA==", "signatures": [{"sig": "MEYCIQCh3+QYMzC8tMnszLJ55aCFso1DWVw4HoufNBsUHsm+lAIhAM3y4+EwCGfbouKkI+pL0IFKI+um0ELamdDpsUcpeikK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1612206}, "main": "./rollup.android-arm-eabi.node", "gitHead": "bbb7e7b1d4e208a923b0f18ceb8dd886838e1a01", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.29.0-2_1734677773811_0.28400253091046634", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.29.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.29.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "465773ea545b642fa786358a83fb6a26affb0396", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.29.0.tgz", "fileCount": 3, "integrity": "sha512-TnF0md3qWSRDlU96y9+0dd5RNrlXiQUp1K2pK1UpNmjeND+o9ts9Jxv3G6ntagkt8jVh0KAT1VYgU0nCz5gt2w==", "signatures": [{"sig": "MEQCICnS47750/0sktzIv/+bBOoBvB9QINorbKrqxN2+MG4pAiA7UUjAyTVLkjCYZ0SOXrSNxgMlVem5mKkS4c/Qiyg7nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1611884}, "main": "./rollup.android-arm-eabi.node", "gitHead": "dadd4882c4984d7875af799ad56e506784d50e1c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.29.0_1734719855614_0.8016081562325126", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.29.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.29.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9bd38df6a29afb7f0336d988bc8112af0c8816c0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.29.1.tgz", "fileCount": 3, "integrity": "sha512-ssKhA8RNltTZLpG6/QNkCSge+7mBQGUqJRisZ2MDQcEGaK93QESEgWK2iOpIDZ7k9zPVkG5AS3ksvD5ZWxmItw==", "signatures": [{"sig": "MEYCIQCRksa7uCFGRA5fFriWPKf2fScLHWAF3oYj4b4lhcFUCgIhAL3XdB3F1KXWgO8zIdC58GEx0SYCmJrIXPoTd3XPl7sv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1611884}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5d3777803404c67ce14c62b8b05d6e26e46856f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.29.1_1734765372934_0.319705836817499", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.30.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.30.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4af994c01a90e0d3ff947387b01a2755df75734a", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.30.0-0.tgz", "fileCount": 3, "integrity": "sha512-QyFJnO/bKfr+hEGzL1RmBzE18PTJdnmxRiqFxlmYxDckXgIubNN5MQtKPiQdj66HCMa1YFjbjyrLxuZQ3feXqg==", "signatures": [{"sig": "MEUCIEXb1zeoy876/vWsFllk048Z39cGaC+kTMF0bMd1TraAAiEA96SoCn6PW0orbgVqS/lR6cpfEQR46z/B+rnDutERKgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1611886}, "main": "./rollup.android-arm-eabi.node", "gitHead": "2339f1d8384a8999645823f83f9042a9fc7b3bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.30.0-0_1734765445197_0.9111908558849222", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0-1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.30.0-1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.30.0-1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "cf477c05803405209e9894b30cdff4d116acd24d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.30.0-1.tgz", "fileCount": 3, "integrity": "sha512-rST28DWLeg6w+LGpHTY8YQvuU6x0A+LIf0snodZt4GYey0H7EQ3CCDf7FdrXXmsH8t/eL4+NvL1OlgwihMZmFA==", "signatures": [{"sig": "MEQCICuNF5IQpS/JXaJ7Jx4uKQOgPeUwGf8F1+WrDRX+on1uAiA0WaG+ETzn8fbtsitNZqhhJR6iGVcdmcMHYNUE75qa5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1614958}, "main": "./rollup.android-arm-eabi.node", "gitHead": "41ab39a6e4a5181e9be21e816dd6f11c57e1c52a", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.30.0-1_1735541548089_0.27757403543647596", "host": "s3://npm-registry-packages-npm-production"}}, "4.29.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.29.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.29.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d4c3965f18ebf567a99154f93bcf283fd527e2a9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.29.2.tgz", "fileCount": 3, "integrity": "sha512-s/8RiF4bdmGnc/J0N7lHAr5ZFJj+NdJqJ/Hj29K+c4lEdoVlukzvWXB9XpWZCdakVT0YAw8iyIqUP2iFRz5/jA==", "signatures": [{"sig": "MEYCIQDb8GZLmaDa+OIFGaiWJaQ72Jsc0zT7InqwPupPTy5BUgIhAIW66soOnQfR0qY2twjoMZ4p3ZjaOQx/NHSGFSzOic8m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1614956}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f5c349e5bb4cb40b0cc1a1b2a3fb5de415946406", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.29.2_1736078873117_0.2541331932037021", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.30.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.30.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f2552f6984cfae52784b2fbf0e47633f38955d66", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.30.0.tgz", "fileCount": 3, "integrity": "sha512-qFcFto9figFLz2g25DxJ1WWL9+c91fTxnGuwhToCl8BaqDsDYMl/kOnBXAyAqkkzAWimYMSWNPWEjt+ADAHuoQ==", "signatures": [{"sig": "MEQCIEnMXwu0faMUecvGDPaI1p3Q41UeU144N5APOgdubOieAiBc5iNfiqk5UgaOHIhreZ0o3Q0rAk9pV/Eg2Gq0GINe2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1614956}, "main": "./rollup.android-arm-eabi.node", "gitHead": "958d5ebabd49297e9a4b78ad34ac0a0132305dea", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.30.0_1736145410279_0.5050085579014414", "host": "s3://npm-registry-packages-npm-production"}}, "4.30.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.30.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.30.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "14c737dc19603a096568044eadaa60395eefb809", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.30.1.tgz", "fileCount": 3, "integrity": "sha512-pSWY+EVt3rJ9fQ3IqlrEUtXh3cGqGtPDH1FQlNZehO2yYxCHEX1SPsz1M//NXwYfbTlcKr9WObLnJX9FsS9K1Q==", "signatures": [{"sig": "MEUCIEftOF98iS6sPpKcQS8EoqYvOkcZOtfsiKutMS1ZdF2TAiEAxTDtSsZFuNpGb1HsHXLvte0qsPyxVeUZ8rkUEBR6ZLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1614956}, "main": "./rollup.android-arm-eabi.node", "gitHead": "94917087deb9103fbf605c68670ceb3e71a67bf7", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.30.1_1736246163177_0.9086165680917369", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.31.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.31.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "dd93e993e0ad713e9266406645244a613ea0baf0", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.31.0-0.tgz", "fileCount": 3, "integrity": "sha512-Sxm1qlwym2ziZZ/ELuFjEB/60CCUtoptkCV8kvMvqZneDAxRl/4wOc8p3SwevYUxbtp9PVacNLhH7IFnzErD1w==", "signatures": [{"sig": "MEUCIQDjRRKawleigoOQ9vA8HFohuXPgqg+rAZuuxa2BQ6/SjQIgf27Q/yOcqlf5OGo56Rl2SXLkTreNXTzPyJFGq04NIPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1613102}, "main": "./rollup.android-arm-eabi.node", "gitHead": "8c80d5f657f0777d14bd75d446fee3fa4b7639fc", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.31.0-0_1736834272387_0.07192267571208899", "host": "s3://npm-registry-packages-npm-production"}}, "4.31.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.31.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.31.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d4dd60da0075a6ce9a6c76d71b8204f3e1822285", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.31.0.tgz", "fileCount": 3, "integrity": "sha512-9NrR4033uCbUBRgvLcBrJofa2KY9DzxL2UKZ1/4xA/mnTNyhZCWBuD8X3tPm1n4KxcgaraOYgrFKSgwjASfmlA==", "signatures": [{"sig": "MEUCIHWGk2eziLG3ehDYH2zBEu43fGt27U4qFb3V0V2pGkLSAiEA/iwmdNQB1PmNUD7bx3zrdl7DbE6sE8AcG8mD2Uk69Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1628012}, "main": "./rollup.android-arm-eabi.node", "gitHead": "15c264d59e0768b7d283a7bb8ded0519d1b5199e", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.31.0_1737291417519_0.02946904201510936", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.32.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.32.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "42a8e897c7b656adb4edebda3a8b83a57526452f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.32.0.tgz", "fileCount": 3, "integrity": "sha512-G2fUQQANtBPsNwiVFg4zKiPQyjVKZCUdQUol53R8E71J7AsheRMV/Yv/nB8giOcOVqP7//eB5xPqieBYZe9bGg==", "signatures": [{"sig": "MEUCIBaQgwQkncaEIlXtqntf5KFKr3206qR31KKB/db2Qu7fAiEAkxuYse1oj/tng6iRk11ZR+AQgyfEXd+az4y6RhiGLho=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1625836}, "main": "./rollup.android-arm-eabi.node", "gitHead": "2538304efdc05ecb7c52e6376d5777565139f075", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.32.0_1737707265049_0.904780934684329", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0-0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.33.0-0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.33.0-0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c1a8cf544a0753e564be4f6da4b296cdeb7db256", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.33.0-0.tgz", "fileCount": 3, "integrity": "sha512-woTzzCINjXtH3FtxiQvevBBAZqcfZaruJcSZsLxPfYy4dtE4WCuQti2QtVnl/YoBe9Lmz0wPTNmcxA9XsveGHw==", "signatures": [{"sig": "MEYCIQDvHYn/8DDH3Y9MacKhxjzw+TVB+HevdXE3ssp7sSYUjwIhAIHpD+XZa6rBZQJBs03py69bQVICYu6xhlLnyaMrO3nA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1625838}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f854e1988542d09f9691923eddd80888e92240d3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.33.0-0_1738053019265_0.49758743851984244", "host": "s3://npm-registry-packages-npm-production"}}, "4.32.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.32.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.32.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c18bad635ba24220a6c8cc427ab2cab12e1531a3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.32.1.tgz", "fileCount": 3, "integrity": "sha512-/pqA4DmqyCm8u5YIDzIdlLcEmuvxb0v8fZdFhVMszSpDTgbQKdw3/mB3eMUHIbubtJ6F9j+LtmyCnHTEqIHyzA==", "signatures": [{"sig": "MEUCIQDqnGDNsinsLS0FSzguCNo6GSY8sR0daKyJXOhQUPiVNAIgROtX1sAinNaQsDEXCwi3BngyPBX4TMD2tqB92JHYcz4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1625836}, "main": "./rollup.android-arm-eabi.node", "gitHead": "abcf4febe11f3d313fae41ddca35fc60670b9ff8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.32.1_1738053207386_0.24142030773234957", "host": "s3://npm-registry-packages-npm-production"}}, "4.33.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.33.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.33.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "655375dce0e19a7251d988a101b04f98abc87b13", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.33.0.tgz", "fileCount": 3, "integrity": "sha512-eBVEV1xXlpRFlaW8IhM/kXHY7vnfWIfQG8ZQV1k5q+3gDQI5D39H9fZk5XysGv/b9kj/pSGAUKJ6+rWoWSNdEQ==", "signatures": [{"sig": "MEYCIQD8dSayD0xALQ1uZlIvF5vdJ37ND8dUVEkpYf1+gA/VxAIhAMx0EtxEaWO0m3xcoBFHhw/bl7BGDw4jFFeZhA1jVNIA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1612028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "494483e8df7b5d04796b30e37f54d7e96fa91a97", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.33.0_1738393930829_0.8983555217110826", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b3eb2caca0d67d89f1797fe732938cfa79c690be", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.0.tgz", "fileCount": 3, "integrity": "sha512-Eeao7ewDq79jVEsrtWIj5RNqB8p2knlm9fhR6uJ2gqP7UfbLrTrxevudVrEPDM7Wkpn/HpRC2QfazH7MXLz3vQ==", "signatures": [{"sig": "MEYCIQC5LGWq1ejrbsUoGWV41VbmQa6/XSoo+8n+enNgvmz/qgIhAIA4CIXXPUua+WaRUzlb3fgGBneW6XtIXAdibG6TcR9x", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1612028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "979d62888dbe75f92e50fdd64246c737c52f5f1f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.0_1738399234009_0.25550221215177116", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "a09cb0718297a2f0cc7f9b4dfca4d08753ea5c9b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.1.tgz", "fileCount": 3, "integrity": "sha512-kwctwVlswSEsr4ljpmxKrRKp1eG1v2NAhlzFzDf1x1OdYaMjBYjDCbHkzWm57ZXzTwqn8stMXgROrnMw8dJK3w==", "signatures": [{"sig": "MEUCIDX8PODgVZCdE2tkShSbr6IoblVk6T3s7pjJ+b0XWq+GAiEA+7Bh1OV4CNH2czP0ir2gnJR5yHWSV626HFG4TgwUutA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1612028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "0f20524ad9ecd166a900d43af93f05a3405d2a45", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.1_1738565903611_0.23212397782232297", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "a1b26b73f873172f70f45d02849be085d6809479", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.2.tgz", "fileCount": 3, "integrity": "sha512-6Fyg9yQbwJR+ykVdT9sid1oc2ewejS6h4wzQltmJfSW53N60G/ah9pngXGANdy9/aaE/TcUFpWosdm7JXS1WTQ==", "signatures": [{"sig": "MEYCIQDORaWCI4RQnXh/xjg0ZT7l8IcII4LGtRlr1HAKQV1MiwIhAOhpX3cXappgeJhAvmP+dbWcOL4dFFUVynxZzGQoRaqe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1612028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "615efa045779fae70c4fd5fe64fdb08a039c0442", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.2_1738656612688_0.4773701481280208", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.3": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.3", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.3", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "eb1b0a1d75c5f048b8d41eb30188c22292676c02", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.3.tgz", "fileCount": 3, "integrity": "sha512-8kq/NjMKkMTGKMPldWihncOl62kgnLYk7cW+/4NCUWfS70/wz4+gQ7rMxMMpZ3dIOP/xw7wKNzIuUnN/H2GfUg==", "signatures": [{"sig": "MEUCIE+xR98Dm0CSk8c0tXPuZ9uVrhyWbLTY5uSdj8MZs7nwAiEA7rCmFyIJMTwd6I0tr1sBbF4tnm/Zuz+mhKpaE0RFA14=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1612028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ac8b06a2b5406f694c38c416912cc2b18ba13355", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.3_1738747335669_0.45326493926356837", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.4": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.4", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.4", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "186addf2d9da1df57c69394f8eb74d40cc833686", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.4.tgz", "fileCount": 3, "integrity": "sha512-gGi5adZWvjtJU7Axs//CWaQbQd/vGy8KGcnEaCWiyCqxWYDxwIlAHFuSe6Guoxtd0SRvSfVTDMPd5H+4KE2kKA==", "signatures": [{"sig": "MEYCIQCBYz7lVp1Te4rpRTU/qINn/L6wD24TovpkTLSQo2Dc6AIhANBmlqXyI5wG3BtL3YH4Ti+NANAH1QPWJfU7X2/pPWkg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1612028}, "main": "./rollup.android-arm-eabi.node", "gitHead": "19312a762c3cda56a0f6dc80a0887a4499db2257", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.4_1738791081870_0.708433021548966", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.5": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.5", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.5", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "35f3e4bbd9e7ccf72e0beaa91052f3eb4274ad27", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.5.tgz", "fileCount": 3, "integrity": "sha512-JXmmQcKQtpf3Z6lvA8akkrHDZ5AEfgc2hLMix1/X5BhQbezBQ0AP5GYLdU8jsQRme8qr2sscCe3wizp7UT0L9g==", "signatures": [{"sig": "MEUCIQDCSrnLrQlksDNW0zcrIL7O4NNCkUVXk9e/+N7pEJV5kQIgMMT18h02REz+yc9Zmxc1aPLaJUC9mnn3fHz6BVrdi9c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1610364}, "main": "./rollup.android-arm-eabi.node", "gitHead": "3426b026e95319048dd5b703f2a0330c1c924e52", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.5_1738918392587_0.2378157722073102", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.6": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.6", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.6", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9b726b4dcafb9332991e9ca49d54bafc71d9d87f", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.6.tgz", "fileCount": 3, "integrity": "sha512-+GcCXtOQoWuC7hhX1P00LqjjIiS/iOouHXhMdiDSnq/1DGTox4SpUvO52Xm+div6+106r+TcvOeo/cxvyEyTgg==", "signatures": [{"sig": "MEUCIDVlNub7QyH7Jx4+KHJwFo8amexrJ5kUg3LKcOss5l7LAiEA+eYwPmkMgvGrvcIlqwQNGCXsQLFkitOLkFXovlpsZUk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1604460}, "main": "./rollup.android-arm-eabi.node", "gitHead": "4b8745922d37d8325197d5a6613ffbf231163c7d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.6_1738945937975_0.****************", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.7": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.7", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.7", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e554185b1afa5509a7a4040d15ec0c3b4435ded1", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.7.tgz", "fileCount": 3, "integrity": "sha512-l6CtzHYo8D2TQ3J7qJNpp3Q1Iye56ssIAtqbM2H8axxCEEwvN7o8Ze9PuIapbxFL3OHrJU2JBX6FIIVnP/rYyw==", "signatures": [{"sig": "MEQCIAHGrYchVHC0v2+lRh+prHZwey7lBQkVifn7pLpvyrZ/AiBpsNuKOl7KqHlHr5M9SUDJYMQlgJmI1qrg/spmz6TUMQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1602540}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f9c52f80074e33f5b0799e8ca215e3bfac7d2755", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.7_1739526850409_0.9788362036675289", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.8": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.8", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.8", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "731df27dfdb77189547bcef96ada7bf166bbb2fb", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.8.tgz", "fileCount": 3, "integrity": "sha512-q217OSE8DTp8AFHuNHXo0Y86e1wtlfVrXiAlwkIvGRQv9zbc6mE3sjIVfwI8sYUyNxwOg0j/Vm1RKM04JcWLJw==", "signatures": [{"sig": "MEQCIBH5W8sDp3jVli9HbNJGCT9Ehqk2XROHlPRPTOQbBbtHAiB0350n3aBQ422ufZqjuzV2L8e7FtbuvO4hrTAf6dAgCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1602540}, "main": "./rollup.android-arm-eabi.node", "gitHead": "8f667b7c15b176728449a4917cb29fe5ee3a1c0c", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.8_1739773595416_0.3912193518340421", "host": "s3://npm-registry-packages-npm-production"}}, "4.34.9": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.34.9", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.34.9", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "661a45a4709c70e59e596ec78daa9cb8b8d27604", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.34.9.tgz", "fileCount": 3, "integrity": "sha512-qZdlImWXur0CFakn2BJ2znJOdqYZKiedEPEVNTBrpfPjc/YuTGcaYZcdmNFTkUj3DU0ZM/AElcM8Ybww3xVLzA==", "signatures": [{"sig": "MEUCIBG4W+UBdAD4rdc1rb79mDg/MjRmjCRnYHP++P7YQgV0AiEAkdQvMz6PFwNzmyjzHtvZc0UkvWhV8ILi5XHn6SIsBn0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1681004}, "main": "./rollup.android-arm-eabi.node", "gitHead": "0ab9b9772e24dfe9ef08bfce3132e99a15b793f6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.34.9_1740814367032_0.14476188085412045", "host": "s3://npm-registry-packages-npm-production"}}, "4.35.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.35.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.35.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e1d7700735f7e8de561ef7d1fa0362082a180c43", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.35.0.tgz", "fileCount": 3, "integrity": "sha512-uYQ2WfPaqz5QtVgMxfN6NpLD+no0MYHDBywl7itPYd3K5TjjSghNKmX8ic9S8NU8w81NVhJv/XojcHptRly7qQ==", "signatures": [{"sig": "MEYCIQCOhENgnUhVnhuFT3+N8apArYGAvVLePz0LPMrT1Fsh2AIhAPpMKRUS6/ua32xDrK00k/M+LDv7KNtp/li6Xzh1TJtC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1685164}, "main": "./rollup.android-arm-eabi.node", "gitHead": "70ef1cce7c740030cc2935b563d13950cc1511f5", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.35.0_1741415096457_0.31907059534835147", "host": "s3://npm-registry-packages-npm-production"}}, "4.36.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.36.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.36.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6229c36cddc172c468f53107f2b7aebe2585609b", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.36.0.tgz", "fileCount": 3, "integrity": "sha512-jgrXjjcEwN6XpZXL0HUeOVGfjXhPyxAbbhD0BlXUB+abTOpbPiN5Wb3kOT7yb+uEtATNYF5x5gIfwutmuBA26w==", "signatures": [{"sig": "MEQCIHke0fiDdUnFSRiFtfodjgKc/a3HgAHKwwvlEhIdb+2HAiBrBsi92fmRDQt6EvSqkhM5jsZX8qwrVnNPnfi784wYXw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1686636}, "main": "./rollup.android-arm-eabi.node", "gitHead": "ab7bfa8fe9c25e41cc62058fa2dcde6b321fd51d", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.36.0_1742200554413_0.32033807280676974", "host": "s3://npm-registry-packages-npm-production"}}, "4.37.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.37.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.37.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9bedc746a97fe707154086365f269ced92ff4aa9", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.37.0.tgz", "fileCount": 3, "integrity": "sha512-l7StVw6WAa8l3vA1ov80jyetOAEo1FtHvZDbzXDO/02Sq/QVvqlHkYoFwDJPIMj0GKiistsBudfx5tGFnwYWDQ==", "signatures": [{"sig": "MEUCIFhkGd9+xNVkpldzcqYem7+JzuP4jhSmSznPJKvNfgKWAiEA2F6L27KyqRk1T2GTXpCh029AeBEWR2H4uzZ/krz4Zao=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1688236}, "main": "./rollup.android-arm-eabi.node", "gitHead": "8b1c634d945dda9294cf579de68c4b223c618e7f", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.37.0_1742741836860_0.28676703753862354", "host": "s3://npm-registry-packages-npm-production"}}, "4.38.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.38.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.38.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6ba67cc0f3a2d7e3a208256a349c2cb2798f57be", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.38.0.tgz", "fileCount": 3, "integrity": "sha512-ldomqc4/jDZu/xpYU+aRxo3V4mGCV9HeTgUBANI3oIQMOL+SsxB+S2lxMpkFp5UamSS3XuTMQVbsS24R4J4Qjg==", "signatures": [{"sig": "MEUCIF6n2Oy3JLZwtBy0MsvbyMD+PptjkNnhvYZiwZQ4L5g6AiEA87FukFkxEDnc/0T/+RHa2uD7Kbkd5fvh5XVDmdt0KZM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1687852}, "main": "./rollup.android-arm-eabi.node", "gitHead": "22b64bcc511dfc40ce463e3f662a928915908713", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.38.0_1743229758019_0.3228004299631251", "host": "s3://npm-registry-packages-npm-production"}}, "4.39.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.39.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.39.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1d8cc5dd3d8ffe569d8f7f67a45c7909828a0f66", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.39.0.tgz", "fileCount": 3, "integrity": "sha512-lGVys55Qb00Wvh8DMAocp5kIcaNzEFTmGhfFd88LfaogYTRKrdxgtlO5H6S49v2Nd8R2C6wLOal0qv6/kCkOwA==", "signatures": [{"sig": "MEYCIQDhf/Vm0T6fbUElBv+ssennfeg7epXhfphWY78tJeeJBgIhAOzuiRlEdMc6UxjuI1hd3rxkupeM/9mDomQ99mJeY53j", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1687852}, "main": "./rollup.android-arm-eabi.node", "gitHead": "5c001245779063abac3899aa9d25294ab003581b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.39.0_1743569382620_0.5078604460677631", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.40.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.40.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "d964ee8ce4d18acf9358f96adc408689b6e27fe3", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.40.0.tgz", "fileCount": 3, "integrity": "sha512-+Fbls/diZ0RDerhE8kyC6hjADCXA1K4yVNlH0EYfd2XjyH0UGgzaQ8MlT0pCXAThfxv3QUAczHaL+qSv1E4/Cg==", "signatures": [{"sig": "MEYCIQDhwz1git3q0CUH2HQPgoBaTYQI7Qys3KC0G3NwTpbZYAIhAPrlhDpQigEZHwsmZ4O0rQeBj13icRrq4tPyvI74ETEb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1712564}, "main": "./rollup.android-arm-eabi.node", "gitHead": "1f2d579ccd4b39f223fed14ac7d031a6c848cd80", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.40.0_1744447186002_0.14302595416783137", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.40.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.40.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e1562d360bca73c7bef6feef86098de3a2f1d442", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.40.1.tgz", "fileCount": 3, "integrity": "sha512-kxz0YeeCrRUHz3zyqvd7n+TVRlNyTifBsmnmNPtk3hQURUyG9eAB+usz6DAwagMusjx/zb3AjvDUvhFGDAexGw==", "signatures": [{"sig": "MEQCIE3u59bZl3pFD5Ak2Zv5qJgFdJ1+f1dlNBiCz+Oa3cN1AiBRpK5m0RXllS7ikYQxjGced8TwyhSyVVd1TXKz/g16UQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1721356}, "main": "./rollup.android-arm-eabi.node", "gitHead": "1e6c40f49c428b7657fe3b9a2026f705acd39da1", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.40.1_1745814933886_0.6513232162516116", "host": "s3://npm-registry-packages-npm-production"}}, "4.40.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.40.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.40.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c228d00a41f0dbd6fb8b7ea819bbfbf1c1157a10", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.40.2.tgz", "fileCount": 3, "integrity": "sha512-JkdNEq+DFxZfUwxvB58tHMHBHVgX23ew41g1OQinthJ+ryhdRk67O31S7sYw8u2lTjHUPFxwar07BBt1KHp/hg==", "signatures": [{"sig": "MEQCIBi8fiKxnsmAGz9rUKxRhbGCvRM/ijGKrGBZkYAMDaOfAiBQXTsr0zKSIUE8mfaPeMP4ipeUUNSPPn1lCrQeRTTrpw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1723468}, "main": "./rollup.android-arm-eabi.node", "gitHead": "02da7efedcf373f0f819b78e3acbe50de05d9a5b", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.40.2_1746516423356_0.5957999715336719", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.41.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.41.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9145b38faf3fbfe3ec557130110e772f797335aa", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.0.tgz", "fileCount": 3, "integrity": "sha512-KxN+zCjOYHGwCl4UCtSfZ6jrq/qi88JDUtiEFk8LELEHq2Egfc/FgW+jItZiOLRuQfb/3xJSgFuNPC9jzggX+A==", "signatures": [{"sig": "MEQCICh2GNrNE5inOMt7IrQ1Av8w8CoBppwmjTuipwKcwKb9AiBegvt3hCbA1TuMg/dbqK9cdaRbEU58uff/V2a9AGhB6A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1823036}, "main": "./rollup.android-arm-eabi.node", "gitHead": "0928185cd544907dab472754634ddf988452aae6", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.41.0_1747546421655_0.5942339460870834", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.1": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.41.1", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.41.1", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f39f09f60d4a562de727c960d7b202a2cf797424", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.1.tgz", "fileCount": 3, "integrity": "sha512-NELNvyEWZ6R9QMkiytB4/L4zSEaBC03KIXEghptLGLZWJ6VPrL63ooZQCOnlx36aQPGhzuOMwDerC1Eb2VmrLw==", "signatures": [{"sig": "MEQCIEMqlR+c+wfpD3lfSg4vM68Hyg57LhFfrlRbS8fOUl5HAiABJDSCNTxPXf4Tg4shw1AQG8npsinEw7DgvhESOTeidw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1908564}, "main": "./rollup.android-arm-eabi.node", "gitHead": "7c469dc4eb8e1cb6def9fdc04581fdfce9975da3", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.41.1_1748067284095_0.04195789405409589", "host": "s3://npm-registry-packages-npm-production"}}, "4.41.2": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.41.2", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.41.2", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "abdf7dcc6a277b94f62e99fa71f7fa89eac6a22e", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.41.2.tgz", "fileCount": 3, "integrity": "sha512-YvIQXGGDzbOpkLuFcjGs+aiAi38D8FCyJanIdlcV2m9DWMJpHTSY8L9piO93VHBLRoe8O9C/FiycjnJ8+aP1tg==", "signatures": [{"sig": "MEQCICewsoOPsNPkPVuyhFgezP6j6hWznE91XP5Zs+QArcyPAiBhaGqFA0bK6r5TOuBJQznqkvxZoVTedaDxlUuX9lYR+w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1896148}, "main": "./rollup.android-arm-eabi.node", "gitHead": "13b4669dbc21cb738551cd725d2a18c77b3cea11", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.41.2_1749210042992_0.7782844837903409", "host": "s3://npm-registry-packages-npm-production"}}, "4.42.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.42.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.42.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8baae15a6a27f18b7c5be420e00ab08c7d3dd6f4", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.42.0.tgz", "fileCount": 3, "integrity": "sha512-gldmAyS9hpj+H6LpRNlcjQWbuKUtb94lodB9uCz71Jm+7BxK1VIOo7y62tZZwxhA7j1ylv/yQz080L5WkS+LoQ==", "signatures": [{"sig": "MEQCIAgevZdtcr+DV0xJ10dnf7fn7JBJWteZcW3hAyUdidFsAiAWXWYvHsakOlerA+ScinCDfYzjzFH8+SboH9hd8I6wCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1896148}, "main": "./rollup.android-arm-eabi.node", "gitHead": "f76339428586620ff3e4c32fce48f923e7be7b05", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.42.0_1749221303326_0.7157356791059892", "host": "s3://npm-registry-packages-npm-production"}}, "4.43.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.43.0", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "_id": "@rollup/rollup-android-arm-eabi@4.43.0", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "homepage": "https://rollupjs.org/", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9241b59af721beb7e3587a56c6c245d6c465753d", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.43.0.tgz", "fileCount": 3, "integrity": "sha512-<PERSON>rjy9awJl6rKbruhQDgivNbD1WuLb8xAclM4IR4cN5pHGAs2oIMMQJEiC3IC/9TZJ+QZkmZhlMO/6MBGxPidpw==", "signatures": [{"sig": "MEUCIGvfRd0rnixgzCGByQnR+NewNAZeA9ugYsyKVyLtPANLAiEA8C21/ITNXjyUYDCXMrJkYanTBVQWzLIIbo3c+yh0XYE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1896148}, "main": "./rollup.android-arm-eabi.node", "gitHead": "72858cb1474b81c91902794ab7d28c79f34b8ca8", "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rollup/rollup.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native bindings for Rollup", "directories": {}, "_nodeVersion": "20.19.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/rollup-android-arm-eabi_4.43.0_1749619370784_0.9008346065764119", "host": "s3://npm-registry-packages-npm-production"}}, "4.44.0": {"name": "@rollup/rollup-android-arm-eabi", "version": "4.44.0", "os": ["android"], "cpu": ["arm"], "description": "Native bindings for Rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "homepage": "https://rollupjs.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "main": "./rollup.android-arm-eabi.node", "_id": "@rollup/rollup-android-arm-eabi@4.44.0", "gitHead": "fa4b2842c823f6a61f6b994a28b7fcb54419b6c6", "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-xEiEE5oDW6tK4jXCAyliuntGR+amEMO7HLtdSshVuhFnKTYoeYMyXQK7pLouAJJj5KHdwdn87bfHAR2nSdNAUA==", "shasum": "a3e4e4b2baf0bade6918cf5135c3ef7eee653196", "tarball": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.44.0.tgz", "fileCount": 3, "unpackedSize": 1901332, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCubeDPg23oXb2+QOSY3RlDzsQ/7U++1Wp16vDbQLwXBgIhANGcvNB2AlQzB9Njy3+fuUy+i61OWzHO/PFqkZbc9L5t"}]}, "_npmUser": {"name": "lukastaegert", "email": "<EMAIL>", "actor": {"name": "lukastaegert", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/rollup-android-arm-eabi_4.44.0_1750314189196_0.07746920971876214"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-07-31T19:17:57.532Z", "modified": "2025-06-19T06:23:09.709Z", "4.0.0-0": "2023-07-31T19:17:57.709Z", "4.0.0-1": "2023-08-01T04:49:02.284Z", "4.0.0-2": "2023-08-01T11:16:35.990Z", "4.0.0-3": "2023-08-04T08:17:01.631Z", "4.0.0-4": "2023-08-04T11:36:45.780Z", "4.0.0-5": "2023-08-20T06:56:53.094Z", "4.0.0-6": "2023-08-20T07:51:41.690Z", "4.0.0-7": "2023-08-20T10:33:33.420Z", "4.0.0-8": "2023-08-20T11:22:25.276Z", "4.0.0-9": "2023-08-20T14:29:11.388Z", "4.0.0-10": "2023-08-21T15:30:09.819Z", "4.0.0-11": "2023-08-23T10:15:59.792Z", "4.0.0-12": "2023-08-23T14:40:33.042Z", "4.0.0-13": "2023-08-24T15:48:39.201Z", "4.0.0-14": "2023-09-15T12:34:29.771Z", "4.0.0-15": "2023-09-15T13:06:57.053Z", "4.0.0-16": "2023-09-15T14:17:21.140Z", "4.0.0-17": "2023-09-15T14:59:11.241Z", "4.0.0-18": "2023-09-15T16:10:16.668Z", "4.0.0-19": "2023-09-15T18:51:01.588Z", "4.0.0-20": "2023-09-24T06:10:41.815Z", "4.0.0-21": "2023-09-24T17:22:28.470Z", "4.0.0-22": "2023-09-26T16:17:32.162Z", "4.0.0-23": "2023-09-26T20:14:27.236Z", "4.0.0-24": "2023-10-03T05:12:50.586Z", "4.0.0-25": "2023-10-05T14:12:51.569Z", "4.0.0": "2023-10-05T15:14:37.813Z", "4.0.1": "2023-10-06T12:36:49.338Z", "4.0.2": "2023-10-06T14:18:48.760Z", "4.1.0": "2023-10-14T05:52:20.670Z", "4.1.1": "2023-10-15T06:31:54.808Z", "4.1.3": "2023-10-15T17:48:33.645Z", "4.1.4": "2023-10-16T04:34:16.088Z", "4.1.5": "2023-10-28T09:23:38.885Z", "4.1.6": "2023-10-31T05:45:22.115Z", "4.2.0": "2023-10-31T08:10:47.327Z", "4.3.0": "2023-11-03T20:13:10.360Z", "4.3.1": "2023-11-11T07:58:00.703Z", "4.4.0": "2023-11-12T07:49:59.427Z", "4.4.1": "2023-11-14T05:25:25.708Z", "4.5.0": "2023-11-18T05:52:17.757Z", "4.5.1": "2023-11-21T20:13:15.220Z", "4.5.2": "2023-11-24T06:29:52.728Z", "4.6.0": "2023-11-26T13:39:19.003Z", "4.6.1": "2023-11-30T05:23:14.913Z", "4.7.0": "2023-12-08T07:58:06.626Z", "4.8.0": "2023-12-11T06:24:59.156Z", "4.9.0": "2023-12-13T09:24:22.505Z", "4.9.1": "2023-12-17T06:26:16.733Z", "4.9.2": "2023-12-30T06:23:32.841Z", "4.9.3": "2024-01-05T06:20:52.081Z", "4.9.4": "2024-01-06T06:39:05.658Z", "4.9.5": "2024-01-12T06:16:18.981Z", "4.9.6": "2024-01-21T05:52:24.308Z", "4.10.0": "2024-02-10T05:58:46.696Z", "4.11.0": "2024-02-15T06:09:46.816Z", "4.12.0": "2024-02-16T13:32:26.031Z", "4.12.1": "2024-03-06T06:03:41.976Z", "4.13.0": "2024-03-12T05:28:44.524Z", "4.13.1-1": "2024-03-24T07:39:28.656Z", "4.13.1": "2024-03-27T10:27:47.575Z", "4.13.2": "2024-03-28T14:13:40.967Z", "4.14.0": "2024-04-03T05:23:00.888Z", "4.14.1": "2024-04-07T07:35:45.129Z", "4.14.2": "2024-04-12T06:23:45.495Z", "4.14.3": "2024-04-15T07:18:37.039Z", "4.15.0": "2024-04-20T05:37:19.926Z", "4.16.0": "2024-04-21T04:42:06.093Z", "4.16.1": "2024-04-21T18:30:06.097Z", "4.16.2": "2024-04-22T15:19:23.574Z", "4.16.3": "2024-04-23T05:12:40.078Z", "4.16.4": "2024-04-23T13:15:12.775Z", "4.17.0": "2024-04-27T11:29:57.339Z", "4.17.1": "2024-04-29T04:58:00.187Z", "4.17.2": "2024-04-30T05:00:53.182Z", "4.18.0": "2024-05-22T05:03:50.535Z", "4.18.1": "2024-07-08T15:25:18.469Z", "4.19.0": "2024-07-20T05:46:20.991Z", "4.19.1": "2024-07-27T04:54:07.350Z", "4.19.2": "2024-08-01T08:33:00.072Z", "4.20.0": "2024-08-03T04:48:57.064Z", "4.21.0": "2024-08-18T05:55:40.517Z", "4.21.1": "2024-08-26T15:54:20.467Z", "4.21.2": "2024-08-30T07:04:32.330Z", "4.21.3": "2024-09-12T07:05:56.277Z", "4.22.0": "2024-09-19T04:55:38.043Z", "4.22.1": "2024-09-20T08:21:59.987Z", "4.22.2": "2024-09-20T09:33:51.895Z", "4.22.3-0": "2024-09-20T14:48:05.494Z", "4.22.3": "2024-09-21T05:03:16.173Z", "4.22.4": "2024-09-21T06:11:28.250Z", "4.22.5": "2024-09-27T11:48:22.860Z", "4.23.0": "2024-10-01T07:10:14.033Z", "4.24.0": "2024-10-02T09:37:27.793Z", "4.24.1": "2024-10-27T06:43:11.324Z", "4.24.2": "2024-10-27T15:40:18.341Z", "4.25.0-0": "2024-10-29T06:15:21.417Z", "4.24.3": "2024-10-29T14:14:18.764Z", "4.24.4": "2024-11-04T08:47:19.378Z", "4.25.0": "2024-11-09T08:37:32.485Z", "4.26.0": "2024-11-13T06:45:09.873Z", "4.27.0-0": "2024-11-13T07:03:23.729Z", "4.27.0-1": "2024-11-14T06:33:21.360Z", "4.27.0": "2024-11-15T10:40:45.952Z", "4.27.1-0": "2024-11-15T13:28:18.545Z", "4.27.1-1": "2024-11-15T15:38:14.112Z", "4.27.1": "2024-11-15T16:07:51.896Z", "4.27.2": "2024-11-15T17:20:14.656Z", "4.27.3": "2024-11-18T16:39:46.849Z", "4.27.4": "2024-11-23T07:00:30.523Z", "4.28.0": "2024-11-30T13:15:58.214Z", "4.28.1": "2024-12-06T11:45:06.910Z", "4.29.0-0": "2024-12-16T06:40:04.654Z", "4.29.0-1": "2024-12-19T06:37:41.426Z", "4.29.0-2": "2024-12-20T06:56:14.054Z", "4.29.0": "2024-12-20T18:37:35.844Z", "4.29.1": "2024-12-21T07:16:13.176Z", "4.30.0-0": "2024-12-21T07:17:25.453Z", "4.30.0-1": "2024-12-30T06:52:28.304Z", "4.29.2": "2025-01-05T12:07:53.387Z", "4.30.0": "2025-01-06T06:36:50.499Z", "4.30.1": "2025-01-07T10:36:03.428Z", "4.31.0-0": "2025-01-14T05:57:52.696Z", "4.31.0": "2025-01-19T12:56:57.811Z", "4.32.0": "2025-01-24T08:27:45.257Z", "4.33.0-0": "2025-01-28T08:30:19.434Z", "4.32.1": "2025-01-28T08:33:27.565Z", "4.33.0": "2025-02-01T07:12:11.066Z", "4.34.0": "2025-02-01T08:40:34.234Z", "4.34.1": "2025-02-03T06:58:23.836Z", "4.34.2": "2025-02-04T08:10:12.968Z", "4.34.3": "2025-02-05T09:22:15.943Z", "4.34.4": "2025-02-05T21:31:22.137Z", "4.34.5": "2025-02-07T08:53:12.861Z", "4.34.6": "2025-02-07T16:32:18.210Z", "4.34.7": "2025-02-14T09:54:10.646Z", "4.34.8": "2025-02-17T06:26:35.717Z", "4.34.9": "2025-03-01T07:32:47.293Z", "4.35.0": "2025-03-08T06:24:56.728Z", "4.36.0": "2025-03-17T08:35:54.569Z", "4.37.0": "2025-03-23T14:57:17.067Z", "4.38.0": "2025-03-29T06:29:18.272Z", "4.39.0": "2025-04-02T04:49:42.896Z", "4.40.0": "2025-04-12T08:39:46.190Z", "4.40.1": "2025-04-28T04:35:34.157Z", "4.40.2": "2025-05-06T07:27:03.571Z", "4.41.0": "2025-05-18T05:33:41.850Z", "4.41.1": "2025-05-24T06:14:44.412Z", "4.41.2": "2025-06-06T11:40:43.238Z", "4.42.0": "2025-06-06T14:48:23.554Z", "4.43.0": "2025-06-11T05:22:51.037Z", "4.44.0": "2025-06-19T06:23:09.439Z"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "license": "MIT", "homepage": "https://rollupjs.org/", "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "description": "Native bindings for Rollup", "maintainers": [{"name": "shellscape", "email": "<EMAIL>"}, {"name": "rich_harris", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "lukastaegert", "email": "<EMAIL>"}], "readme": "# `@rollup/rollup-android-arm-eabi`\n\nThis is the **armv7-linux-androideabi** binary for `rollup`\n", "readmeFilename": "README.md"}