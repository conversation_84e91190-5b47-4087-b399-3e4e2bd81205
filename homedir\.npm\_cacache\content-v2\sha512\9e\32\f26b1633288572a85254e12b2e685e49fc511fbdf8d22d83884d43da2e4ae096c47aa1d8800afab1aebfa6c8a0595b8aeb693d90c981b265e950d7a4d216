{"_id": "aws4", "_rev": "135-9d38be3487b2b5b49c2b1d9c6e22d8f4", "name": "aws4", "dist-tags": {"latest": "1.13.2", "v05": "0.5.3", "v04": "0.4.4", "v03": "0.3.2", "v02": "0.2.5", "v01": "0.1.13"}, "versions": {"0.0.1": {"name": "aws4", "version": "0.0.1", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "244de4ce76d1fa82eefd7264b6511ca97b1b41cd", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.0.1.tgz", "integrity": "sha512-I+WX2C0h0ak8+HdSrLuTkCl6bnoHeEaLr7ArkVzk6g8zT997VG5Umc/1J4b0Ou8FrS3PUIZRZ4BM/hBzpdKS4A==", "signatures": [{"sig": "MEUCIFIOOxkYPUORN7Sk/oSswOy7EslsxdoSkQv45IbR3SugAiEA1Q7D89b9mn6ByMYvUEK+n7ZLWUq6hIClEjLr+xCEayw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.0": {"name": "aws4", "version": "0.1.0", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a9fd70a58d2ea6bd2e4ad1e943f4e7bb5b9f0810", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.0.tgz", "integrity": "sha512-Rm7owDiFsJN3E86NoTYUnb8XcZ/aoc4UZV+8TbJn/bniNG1I/wIousQdBMcQsU1RIjn8x/tbyvhjBAKE0p1KvQ==", "signatures": [{"sig": "MEUCIQDJU9R6huKXT4Fl/ophEU/XmR3TbEBFd05X3nWyaFzDkQIgVT4PVjHKgC626wrRIf/Bm3K7v9vL1JyTI7jE2IRRUK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.1": {"name": "aws4", "version": "0.1.1", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "73b9513a96277df45c3d24a2f93d21f0c1d0c51f", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.1.tgz", "integrity": "sha512-SWkQ15kZwYj0+l/EKNxJHGQl8f+SXXC/FUCwOtjwdRvr4TuXABvnTO6777gfCBEoruzy6qujq5I2Kme3HkxK9A==", "signatures": [{"sig": "MEQCIBe25kN9quCKZNfj5VHdmSyyVAGxgMEx3jUR6jBlbhhQAiBxbuqdkEiyVch+saSVbNls7Sl0eijVfVIjWkFTUDOzAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.2": {"name": "aws4", "version": "0.1.2", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a5dc963a397ee7fa0d1b709acda73d990a40cf28", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.2.tgz", "integrity": "sha512-SnlmBTN4P/WfdT5O1orVd2DgZTJYYYBXNoBQHjIMD4D8yW19FneakHjCnAKjw02IwSnw9LzS++7iIRk1cAPh7A==", "signatures": [{"sig": "MEYCIQDpTRI2uxtcpjNb6yvbFaPDyOa+o7cYyTSgrL1vM32gbQIhAKba4DBrPrz86A4X2+w8HnPUlQGobxvM2ESPrVwe4XB5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.3": {"name": "aws4", "version": "0.1.3", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a791589a127f4f21097a73e345ad2ba800d289cb", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.3.tgz", "integrity": "sha512-3mGMdpWrFtKCym/+iMlA+41T04o7KJZhRpZw269/CoJrKrUOouBrbsWoi76b0QGGUZElaRdziBghY2MQE33yXQ==", "signatures": [{"sig": "MEQCIEsPpkyTV8FZvjrLxQ8R7PfPbGWQGWF6GVRXKSNHVLOXAiBrr0oEIu6r865LOd6L+wJw12Ceq3csGZgioU6pdzBfFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.4": {"name": "aws4", "version": "0.1.4", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e1effd726de95cef76f172f670a962a2b9ebc0c0", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.4.tgz", "integrity": "sha512-OikUbrOtCBwJeMYXxh9wvBzUAt3qEeXZUF2tlupOf3pF0T7dpWmvGPr9wT1nkXVVkT4AgfM3VviUKM/m0D5TXQ==", "signatures": [{"sig": "MEUCIQCbId5IVPUnYscrd/luFGdGBRKEBEs79fackLD1rO3fpAIgP04+GEl7vB503cjAz2PsL3QtPQwW0e3Hgr8P1Xtbj68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.5": {"name": "aws4", "version": "0.1.5", "keywords": ["amazon", "aws", "signature", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1bd035bf7d9fe7e04a7cb35c51383676715200d3", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.5.tgz", "integrity": "sha512-WmfNNcER9iHGr7srlu+MEmOMkBPF9jYX7vzl2IYFOT7K6OPakDuz8UTXyh5VhmAeKNDlSLKyAddWBGbp+PlrAw==", "signatures": [{"sig": "MEQCIHIgKRl6OJs+BW6udOh7wqjJF+yk0yLQsME7kP6UdintAiBcV9jEyvwYqbKqMSTUTdbYgyZN/e1xCA0a4Exp2bXpRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.6": {"name": "aws4", "version": "0.1.6", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1df3c5d1b8de9084fbc445646e031d3f3697c547", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.6.tgz", "integrity": "sha512-LXFVbaJ+nIuYV89ka/d+VbdxY0nDg9QmVp8KK1j0HEPcMlYZ9US7Md3/FpVV3yjDg1FlBIN+g82q5Ye281AgbQ==", "signatures": [{"sig": "MEQCICwS6YeD67HoTfdww/D7rdVdfJM66mDl44Fg4YjoFlqJAiBfie3ILu/vXzu6z9AHbWYAcwirqrY/OeNbCz3LfAQuXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.7": {"name": "aws4", "version": "0.1.7", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e5600baa7f3756742cf3171964db73b2b583704d", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.7.tgz", "integrity": "sha512-a/kLbci0eaE9ggVLiCzN1KuWftkl97TW6rRApx+DQXFZHnCCzOrVhQU8pIiC+rMwdTm6MDkKZb5HHwmvAK2lnw==", "signatures": [{"sig": "MEQCIDX+pbuzuPc8FSGjPgiuZvntVL+kGDcgpsHc3tqK6VgRAiBCj0CXHk+qgQWW6MJb202iGDEPOVql9Wu9VbmPz/y5gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.8": {"name": "aws4", "version": "0.1.8", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e069d19410247712a2b3783dd566c6b09d740769", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.8.tgz", "integrity": "sha512-w5x+Lww7CjTdYX4u9Kja1iYfCOaIGWd6HhLiaXFP85Mf+40DfyZBQda1J8Y1tftI0wonGa5u82QdWZE4YV7Q1g==", "signatures": [{"sig": "MEYCIQCII4syPL0g0ysP6ouVZW4QpL3657wH9Zr/tEe32AEoRQIhAPnBVyH6khR9LV1ofozf34d4twqJW4+5d23K1V+cUUHQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.1.69", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.9": {"name": "aws4", "version": "0.1.9", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1cd92abc232f83b1f29ccf5fef8f2f9f872421f6", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.9.tgz", "integrity": "sha512-CtONMUDByfjmOgi9srVzabYMdzaFBvevBuSehkx/cWOxuq8vDgAmYJY8OjJzVF8F5RTFI7OrxAXvk64tV3hSjw==", "signatures": [{"sig": "MEYCIQD9XM01R8L5Z42CXehYulrQurs7LOcIglfmRjziAhhcsAIhAMvMdh4g6EEczILGjPCk3u+1LzlO25ghLOH2nwxwMB+P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.10": {"name": "aws4", "version": "0.1.10", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5aec181b78dd5230bc22604fa87a38a12d6f8c0f", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.10.tgz", "integrity": "sha512-S7Imak9QA3OQBs60gzHPJ/XGsAhxmBa9zHknVB5I8cYEmXZb532xS3Im6y8250BSBSkcvmD4iitCu/b+Eai3MQ==", "signatures": [{"sig": "MEYCIQDc0I8b2d37PkytcLftwx0HWgLCjEnMXxudZll1aO0DDwIhAMxxepEHNMNgP24ZG9Fa8E2S0wFZGHiD58F7ePQ17gwW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.2.11", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.1.12": {"name": "aws4", "version": "0.1.12", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.1.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "e9c9223084a41b62eed44842700b0b564ab0763c", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.12.tgz", "integrity": "sha512-x9/CzxlC4ntvWmzPbxqqA8V3wB5r/px36YLvy92SohvW+wEYenAm+Gl1cTcQ121QEnO2LhKLzwpg4cXUVhCWlg==", "signatures": [{"sig": "MEQCIDzWG2qQCCF/4Nddl0IyJZOeQmx2YRi0yJQe4fq4xbpnAiBsh48zaVe1FKjWY8QhF97yrB3GMn+kAQiANVBGzDfRyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.2.0": {"name": "aws4", "version": "0.2.0", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "1679617aa2c2b05801bbd7affb81d8006f3b6be3", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.2.0.tgz", "integrity": "sha512-rGnKnX6cGUmjutvZ0kAMeTQK9iaWG+RrmpOSkAq8eig9it8m1b/xPR9zCGnKWvOSpTpvYgdskvzan1521q7IoQ==", "signatures": [{"sig": "MEUCIQDk9Um3idElac6NvR3I65a93IreTbqaTVCH/1Dfjy5H1gIgHWmDEdZUAk9PvE/c/i/UnGYTUd1i/RCM/dJt5vvTbug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.2.1": {"name": "aws4", "version": "0.2.1", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "5980b645c950df357208fa6ef5f05de661e93c9f", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.2.1.tgz", "integrity": "sha512-KCKe1FuoR7GL7bdKml90HD0AYCNX/d3Bb1joLQUEOMNF5IfK6CLDGbzjNRZmE92YXBiDmHreWRXspGdcMWRlkg==", "signatures": [{"sig": "MEUCIQDN+hRzmXknh/H2tWJ9Wf48AQsAV1fnCqj5OabuvzyCLAIgOktOJZ9NrDzASAak3tBu56RTd16xBDGPC4FGtBJj/UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.2.2": {"name": "aws4", "version": "0.2.2", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "5b39b87a51fbfa92127469ee35739f3a35cc1499", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.2.2.tgz", "integrity": "sha512-9StjaDNNwTuch9a0ToocCQDlL8aX6AqEbW+IYImZVtvkNYFbfQPUIiaVhorSWee+Twc1oxGQVG5XyhJr6eOI0w==", "signatures": [{"sig": "MEUCIQDxM/tccQm8W5o27EOqB+dJ2PM1ZpdxBAu2ukCguUE/hwIgYtOf3UauaazeJf/+h7hg7PPqOEu3sJvxR7Ru8LwON5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "5b39b87a51fbfa92127469ee35739f3a35cc1499", "gitHead": "41a4b43e1ad62ca6947eae49fbe11fd67c5ccf48", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.0.0-beta.3", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.2.3": {"name": "aws4", "version": "0.2.3", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "db0921516606c7704ac556f5f9b026a155a23960", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.2.3.tgz", "integrity": "sha512-U9fu2ZSvMfEmo8wErg+kqILx94m9U08TZ8iFCBwgejY/jtpxq+hA6KP+mKhDaAzxzRtv9NjTIHRBZR6DTc9tlg==", "signatures": [{"sig": "MEYCIQC0HHcI0OJS/LlSwsJDLYJrk2K4E8KCdJVsbgkiRAq2kwIhANTWrcGypQg0/SFN+pI4WL4ISq7zWvEdq1PmcNca0QSi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "db0921516606c7704ac556f5f9b026a155a23960", "gitHead": "9e0fd36f002e0267f18a65809315a36e1f5157d8", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.0.0-beta.3", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.3.0": {"name": "aws4", "version": "0.3.0", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "acb28678de00516779342ebf1876f31aaafc7fd4", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.3.0.tgz", "integrity": "sha512-tWPewmUFGrKU+kxaUu+YHc7TX16I1lTJqR00JyxRVCuHhDdl1SRqT+fxh1mcHJU1i2ozgwm0BSacmEoo9qGXCA==", "signatures": [{"sig": "MEUCIQDG60MoT4uBKFc/ICWXw1mVaeYd+i0BkneGoNSgaGG1eQIgEjRUNkDw060EzXYMQM9gMwmcnHz/VjTbWyvZq+FQ4DM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "acb28678de00516779342ebf1876f31aaafc7fd4", "gitHead": "ba832c846ba287bcf19f4297bdbc78c657775f88", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.4.0": {"name": "aws4", "version": "0.4.0", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "88fcf400ed9f184045b73b041f7c84577fd06d57", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.4.0.tgz", "integrity": "sha512-qEs7O4bv6bkQDeX+elZoXCazov+tRlduWmskCjLFj0LO4EzYGqgO+o2rkgmv/yKeUtblm3g92egKMZz+6XoZAg==", "signatures": [{"sig": "MEUCIQD5IpZRQkjqtfcLx7fHEimV2CN0JKAcUQa5yKQPwFNP3QIgExQb4sOZ2K/lKIuzeKvKWbKilyu+z800f0TyB9WcSsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "88fcf400ed9f184045b73b041f7c84577fd06d57", "gitHead": "4ee9dd44292fb4d1276cf5bf334dd5e032e688a4", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.1.16", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.4.1": {"name": "aws4", "version": "0.4.1", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "c7846bd6a8b94153b064d7a98b8f3970386834ac", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.4.1.tgz", "integrity": "sha512-CHBlgI7Yf0MuLc2Kpy/slji2l88tJMTOLy+SE7yekbjcnC8gToOrQjB2ROBLHKCAqqUV0Y20k5zRYp3v2ecnvg==", "signatures": [{"sig": "MEYCIQCPzruKUoun5nxEROgTHGVVENoYB2wH6VQUV+Lpsd7xCAIhAMULrIIbiM3+8C3cGXGrwEt/XE+W7EY4VtgmfqWAUySi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "c7846bd6a8b94153b064d7a98b8f3970386834ac", "gitHead": "f3ecac42d229e2f265e7d1ca170db68b72c8d69a", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.1.16", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.4.2": {"name": "aws4", "version": "0.4.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "1fd42c37c38afbe6211a1823836c6d535a4f83d7", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.4.2.tgz", "integrity": "sha512-JPFCWlIGRoz8DfqXouokG22erCIp58z/Xx6sTEarcxqBfQDrQ3z0j1AE0P+sy0B0PJH/4B9tsTiTik0oqi1uzw==", "signatures": [{"sig": "MEYCIQC3aWXpWc43HeweEEfIPe3LIBhTnp0bMTr64ehkAgv/gQIhAKW7ZCqRB9YhfTPTHaWMTUYMQxwgEuB0eVyV/sBmfzRr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "1fd42c37c38afbe6211a1823836c6d535a4f83d7", "gitHead": "0afee5f04f234c660c997795e6e6dc3441b2cc5c", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.1.16", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"lru-cache": "~2.3.1"}, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}}, "0.5.0": {"name": "aws4", "version": "0.5.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "848a0dc6dd2fe7dd486ed1b7a981df60ab8ad94f", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.5.0.tgz", "integrity": "sha512-7vS6f95+XFyM5WiOmMkADAg7+l2mLFH8oQmf5QmvMK1HXSqQyPr7pMUhPOnnkKge+ZotpOcKAseCZtJMKDT3ug==", "signatures": [{"sig": "MEUCIQDIieotIiW1TY5s0/fBvC8jXowsxsG2NRxnxBGdusITnQIgRE01nq0sRKkfK98JLasbr7bNvVEKjiBT0iaEMXvTNbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "848a0dc6dd2fe7dd486ed1b7a981df60ab8ad94f", "gitHead": "f326a8aed70bd260f66ade4bd215264d5fc34481", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}}, "0.5.1": {"name": "aws4", "version": "0.5.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "923737a1705fd01cd2fd55e1208067e11527cd3a", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.5.1.tgz", "integrity": "sha512-O7wtCdh7xuDHuycTAFEJkIDwy56zZlypygj+pEN6039UyxcN3fGZC6wDArz6JvPFzCh+iOy0l2/CJp5gNda+DQ==", "signatures": [{"sig": "MEYCIQDJke0fa/WmhLZqypooaZAz/Pf5qu699bTkpl7dQJutVgIhANhyeF+3ul6vO7S/ZjiJiYMUhcR6W93sXc5pYhxNtWKt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "923737a1705fd01cd2fd55e1208067e11527cd3a", "gitHead": "02e24d44c9f9ce32796b94bf7db9c60e04716d55", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}}, "1.0.0": {"name": "aws4", "version": "1.0.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "2df807f7ae5652497bcf7e9438bd3a2f0c47f96b", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.0.tgz", "integrity": "sha512-f0xhaqBqBv6ey7/y/Uanuwv8K2Ix7rL/tGMqXykgO2oZgX4OiR2yC2hhmRTneQcgljcGKTEXJRiUOv9W98PZLA==", "signatures": [{"sig": "MEUCIBcOosxEAqXsVgWa5c0bwOrts4aGQ84ZXd+QzYeO6Du9AiEAiWQbk6blX76DY2IDloQNWVoAY6gWte1KqAQ52ZxRBTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "2df807f7ae5652497bcf7e9438bd3a2f0c47f96b", "gitHead": "5bfe09565359f3e9064cc57035ccceedcca270e6", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.37", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}}, "1.0.1": {"name": "aws4", "version": "1.0.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "c033efaee8d6a45ca1302db7c2bdc3f48c79e2ca", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.1.tgz", "integrity": "sha512-Oq8Jo8KjLdkT8LNzGbWmdreagUQX6YpSExz9/5M/Cch9TFSOSu8qjMsEV+/o+LymgW2iYgOKmFK7oIsfdhTY0Q==", "signatures": [{"sig": "MEQCIFpGLMOVAWS06Br0HDqrlxslx+HeNmEXqGM4WPhzAgoFAiAl07ApycPEqBNjY8xdvukKFIHeIE6rzVtMMZeMCP8OyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "c033efaee8d6a45ca1302db7c2bdc3f48c79e2ca", "gitHead": "9012819433e901378f9aa34657da0f981e0fc031", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.38", "dependencies": {"lru-cache": "^2.5.0"}, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}}, "1.0.2": {"name": "aws4", "version": "1.0.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "888e5637625ec07bf2f0ebdc5f916e04d58470c9", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.2.tgz", "integrity": "sha512-Uh6DNPW/6pLgR0s5O8GC0/XQtqAjHFwScYLMtuyQHq+Se/5nCy7hiDEVSz2uQ80UnRNMZnH0MZ9zdxdq0PfDLw==", "signatures": [{"sig": "MEUCIQC/wYkdO5uepHQ4DPD3KijBGv4dEFsDjqEa4Mf8TjE1ewIgK8990w1J+xy70yjAzh+BSCHiPppRqyU70Na7OY3fYy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "888e5637625ec07bf2f0ebdc5f916e04d58470c9", "gitHead": "b48cfc6d1baf56c2a39131b7783ae5ea509d1bf9", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.39", "dependencies": {"lru-cache": "^2.6.5"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}}, "1.0.3": {"name": "aws4", "version": "1.0.3", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "ca8880d61a2c7ce40f0542c8f7fea3ca4405386b", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.3.tgz", "integrity": "sha512-PRESmZHMStTJOYI2XyZshaSPDm1FP7NMJcvz3v8eqe/v3ADL1mmxWQKgD55CnMruTqEb74pp0T6i0QyUtlkE1Q==", "signatures": [{"sig": "MEUCIFKC97t9BUfpsuY9HHivVCtvjS7TOWXj/SF6Zf8A+PpjAiEA85MUh76ahVwLpue5f/rpmH2G6hwmVopEeZ4m75hpXmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "ca8880d61a2c7ce40f0542c8f7fea3ca4405386b", "gitHead": "52f46074897fb74fa3eabfca184a72ca69ac5db1", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.39", "dependencies": {"lru-cache": "^2.6.5"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}}, "1.0.4": {"name": "aws4", "version": "1.0.4", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "2ae0134fe17af91d556e951b1ecc343a32169788", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.4.tgz", "integrity": "sha512-nrbpGJcFBXeF3LLYWtf+vKHKGFfNvpq8UOpolRNX33S+A7RHFF+axqkLjIGpNCiEELJ/GfIeO2rYvmdbQT1f+w==", "signatures": [{"sig": "MEQCIDlL9GNRSmOzdSzJSNC588McJ4tzvW859yJdJdpdpK41AiAci6N5sIFzTyceGQmk+GyjcE80FtY0R5fT1oTacrJfFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "2ae0134fe17af91d556e951b1ecc343a32169788", "gitHead": "2f73f9f05100c52e8d175e327d86f1fa3ec76bde", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "0.10.39", "dependencies": {"lru-cache": "^2.6.5"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}}, "1.1.0": {"name": "aws4", "version": "1.1.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "82291432db9aad5a7c9113b7bcdcb2bce856315a", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.1.0.tgz", "integrity": "sha512-18yG+RpmF0YPViFnMqLi+Hn1HtGXID10mmybnQb80QScIInOpq3EXGbE+V5C67d7a9adFJm+8ac8ojLXHcpOeg==", "signatures": [{"sig": "MEUCICuDpBAWzJ7pxAEb5c2yb/DM4/iMtfcY9Gv7Yt0FhkM9AiEAnhcR4IG34or35YbRFyKHFH8T3ej7TyNS1J8Sq28PpnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "82291432db9aad5a7c9113b7bcdcb2bce856315a", "gitHead": "165f451fc25368684385c8ffdd11e77f9a657732", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.14.8", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.2.0", "dependencies": {"lru-cache": "^2.6.5"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}}, "1.2.0": {"name": "aws4", "version": "1.2.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "1d78ffb8b93b6b62ade7aa526993a8295aa8b454", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.2.0.tgz", "integrity": "sha512-MyDpazO7GdLlAn8uIh93l3OLQ47d17W7Y78yYrQasDiz/4j2sUVSLgjg039PaTzYn8UIhGGImWNIPYSd2LvDQA==", "signatures": [{"sig": "MEYCIQCaxXQS3deFCPvgRnsfPMtovSEMFKnW7+0OgBNgHOm2rQIhAOhEPgz4uVsA1HF9p0qJ7b/CBiuh27xKmXjv3YNuGI4Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "1d78ffb8b93b6b62ade7aa526993a8295aa8b454", "gitHead": "151b93e6147b1854c8531a9b11ad4fda10067f62", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"lru-cache": "^2.6.5"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}}, "1.2.1": {"name": "aws4", "version": "1.2.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "52b5659a4d32583d405f65e1124ac436d07fe5ac", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.2.1.tgz", "integrity": "sha512-g1zhUP657WbMJxj9545AdaN1wUtkfrq5B+mAQdsVS7ra9EJAjQKMd5JSQdIu6o5BN1i6Je3gA0zlfggp/IyqUg==", "signatures": [{"sig": "MEQCIAwe4lqxc0Ji5VO/vA3x+jU2PwiUtkj7k6Onwi97yphjAiAdcMZKEe5URKCJ0ns2Zf0r2CpDWidpqqDxe0XkYp6GJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "52b5659a4d32583d405f65e1124ac436d07fe5ac", "gitHead": "3d8a3a06a8415bd5255b4f60eb91576952e97f5c", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"lru-cache": "^2.6.5"}, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}}, "1.3.1": {"name": "aws4", "version": "1.3.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "746e5456c0b8f731245581419c99e885321007b2", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.3.1.tgz", "integrity": "sha512-LEPLVaynb85UBg7W/eQsipXzf+ApFhYuUnZjDWqA6GuPRbQiSLfAI1VSM1CGWdMiRiYzSLStnK1VGUyWjdBKKg==", "signatures": [{"sig": "MEQCICkyAy8fii6+KHlHqJZOduA3AfwYHAdVZ1VPqRzlzkG0AiBda9x1TXnx8aaAdNdjQe62DX1TszVUvn9ZIHoxi3Azpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "746e5456c0b8f731245581419c99e885321007b2", "gitHead": "736a90cedd14721101a94f91c7aa066bc5bfcfc0", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.14.21", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.3.1", "dependencies": {"lru-cache": "^4.0.0"}, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4-1.3.1.tgz_1456774939650_0.23266926012001932", "host": "packages-6-west.internal.npmjs.com"}}, "1.3.2": {"name": "aws4", "version": "1.3.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d39e0bee412ced0e8ed94a23e314f313a95b9fd1", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.3.2.tgz", "integrity": "sha512-W0YgnbzGPOQEuwSZxCN3fOJ5ZKQTG5ylVVvT2F1LLlQItKVuIF8vFlVM3Ys0gvS2cj3KsrSJP4kPOmUxD86DSQ==", "signatures": [{"sig": "MEUCIHAAEbM7iEBfWpmSnd2Wkjtxh1Ykd7bDo+egWCS60h7HAiEA7O4i1UItipOKd4IIC8CKaf5v01KJ6gIRHuPjenXQELA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "d39e0bee412ced0e8ed94a23e314f313a95b9fd1", "gitHead": "e899db3aacbf2a034398f7dd483345dc793e8d72", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.14.21", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.3.1", "dependencies": {"lru-cache": "^4.0.0"}, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4-1.3.2.tgz_1456871543899_0.13955276948399842", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0": {"name": "aws4", "version": "1.4.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "116711e87093c04fd756d49cdd7a66e244508752", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.4.0.tgz", "integrity": "sha512-HmpaeG0ym8CUV7zQJ+pEIuNNSkFZdtcU7Toe7omEYYfy2NBiisucrE4oJNoEffh8HClAinuCeZE5MBoWgbUm0g==", "signatures": [{"sig": "MEUCIQC/KG1dmXpKzNHBFG/eC9WzT20TqeWxpi7b8Ny9C3qY+gIgSV4OY6UHq0VBEUeX5XWNdctBvVzvmEgGQQMaBCAKV14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "116711e87093c04fd756d49cdd7a66e244508752", "gitHead": "905ee9189619d05c574cafa65fad9b9bef179f77", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.15.4", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4-1.4.0.tgz_1462572395340_0.5036796217318624", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.1": {"name": "aws4", "version": "1.4.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "fde7d5292466d230e5ee0f4e038d9dfaab08fc61", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.4.1.tgz", "integrity": "sha512-jL/nxDBjzkUgR8dkBWS+IyHyt3eVwG7NzAbiMDG/phpR8mK02WoF3TXBmB4FTc/FbSvoXoSgOyvmh6QRiDUXvg==", "signatures": [{"sig": "MEUCIDGtQowIRJ9B0OVqKgRhYIc+xRF61MRsBbz+EZgnYr5xAiEAhA0juTaWRpikvX9O0W2zmyvnZyDlzISbzohaMTzn3dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "fde7d5292466d230e5ee0f4e038d9dfaab08fc61", "gitHead": "f126d3ff80be1ddde0fc6b50bb51a7f199547e81", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.15.4", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4-1.4.1.tgz_1462643218465_0.6527479749638587", "host": "packages-12-west.internal.npmjs.com"}}, "1.5.0": {"name": "aws4", "version": "1.5.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "0a29ffb79c31c9e712eeb087e8e7a64b4a56d755", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.5.0.tgz", "integrity": "sha512-msbruB31mspZNtdkAMGIpHwGe3salMf4Bu53YJXsO/AmoxrgaVi3DLyP46XKYFKvKJ7Eh/BrLZGS0TMtyzbHlA==", "signatures": [{"sig": "MEUCIQDWCOdMCcXcugWUHkYZdyOxPBrIqNRiavEarEvqX3SZBgIgML6X7rT7dzMtcDEIEx690eivLnLg5lyz8mVaHm+fwiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "0a29ffb79c31c9e712eeb087e8e7a64b4a56d755", "gitHead": "ba136334ee08884c6042c8578a22e376233eef34", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.5.0", "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4-1.5.0.tgz_1476226259635_0.2796843808609992", "host": "packages-16-east.internal.npmjs.com"}}, "1.6.0": {"name": "aws4", "version": "1.6.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "83ef5ca860b2b32e4a0deedee8c771b9db57471e", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.6.0.tgz", "integrity": "sha512-tkleq4Df8UWu/7xf/tfbo7t2vDa07bcONGnKhl0QXKQsh3fJ0yJ1M5wzpy8BtBSENQw/9VTsthMhLG+yXHfStQ==", "signatures": [{"sig": "MEUCIExAAcbn43TPjN6HnkTACYOlCvvz0WD/wJdbBs0l+qNmAiEA1YsGO/6mIGHb/8Sa5fRdHxEHHq0FCoFf2RbMXws9dZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "aws4.js", "_from": ".", "_shasum": "83ef5ca860b2b32e4a0deedee8c771b9db57471e", "gitHead": "74bf0b64d1e8cbcd184964999c7ef53f52d7ad32", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "4.5.0", "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4-1.6.0.tgz_1486481933920_0.6127187723759562", "host": "packages-18-east.internal.npmjs.com"}}, "1.7.0": {"name": "aws4", "version": "1.7.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d4d0e9b9dbfca77bf08eeb0a8a471550fe39e289", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.7.0.tgz", "fileCount": 6, "integrity": "sha512-32NDda82rhwD9/JBCCkB+MRYDp0oSvlo2IL6rQWA10PQi7tDUM3eqMSltXmY+Oyl/7N3P3qNtAlv7X0d9bI28w==", "signatures": [{"sig": "MEQCIH3qsa78j4ZTia57lxl8y21/R/9IpqV95DhDegBxhG1gAiBYp6hmWp8tlCP2u6uFu3r1/fCOH1Ea8ub8LOuU8atElA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31916}, "main": "aws4.js", "gitHead": "58a4cfddcea0d31c2ea60f967b7ec072b7e42ee5", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "8.11.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.7.0_1523139854595_0.22793470969680474", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "aws4", "version": "1.8.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "f0e003d9ca9e7f59c7a508945d7b2ef9a04a542f", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.8.0.tgz", "fileCount": 6, "integrity": "sha512-ReZxvNHIOv88FlT7rxcXIIC0fPt4KZqZbOlivyWtXLt8ESx84zd3kMC6iK5jVeS2qt+g7ftS7ye4fi06X5rtRQ==", "signatures": [{"sig": "MEUCIQDG4ltcJS/Q4GmcyjTjOgD+hokaK3oaAjGClOdHBEVEugIga0ZkaiE8oot5UOvHwrxNvOK1xkAXvGfu/+mQcJtm0f8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaFc9CRA9TVsSAnZWagAAb7kQAICqhE3wrSTWhpcdKyW3\nTKZwkSrT/ifP8bQlO954Z+JthiQPDo82FdUmJfmIy8GPtHRi+UGNZLiB8Pmt\nfdkGlL7qqFV6VBvPVmKr0ncrYCHjFzEyAmXNJqISoVVb/lHbZtwbGJMMDOaP\n9Ye1Hl7Jn1LErzeU1J7lVzL5ikJUwcPSSPpWqqTrM0GMNOXLX5ShPYpEiWgS\nmW2l2pvJUInJiqApRC6rnN6NOibqugQQlBV7fKg9FkqWZxIO++PzojbFl7tY\nFwh68Mq4t2i9uf9I+tGUwGIJWoyho7AE+xqNjm6QumfYTWUFvWKIjiBRfA2w\nOovOe2cUfbSDfgqFlZQjvf2ZEJDO1Ttfq43FShdgak9Z9G4UMuhVw4Pv7ZEc\nkA6MoZ129weunpfZG/lEVJgsPBBlvhjUKYpvWvCb52yzkDpaARgtwyNI9wq8\nKGlVEr9Aa7qEFLJKOgdu3qv/fGqoJiM3P3l0LyshHhDs9bWLt77JJQqjayOF\nBahtsc2AVNIfKuXVb4QTNYYgWzvCUbBo2x8lh/wIDwcrOSRYmdFGu2HK3IS+\nTi+KohN/H3Ix2o/1QYFlCkEtsRLmFjRSwxcS1IvDTAooCMbI2FN3sdp9UxkI\nwQnk1ibJrAloJCFoKGeqX5m0idlx/Ab9SJO3W7mW50qtecXnmlFSym7Bjuqn\njjHR\r\n=/J61\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "1f450081cdc783c878e0b54df5653eef5a7b6fec", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "10.8.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.8.0_1533564732614_0.9619256235299285", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "aws4", "version": "1.9.0", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "24390e6ad61386b0a747265754d2a17219de862c", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.9.0.tgz", "fileCount": 6, "integrity": "sha512-Uvq6hVe90D0B2WEnUqtdgY1bATGz3mw33nH9Y+dmA+w5DHvUmBgkr5rM/KCHpCsiFNRUfokW/szpPPgMK2hm4A==", "signatures": [{"sig": "MEUCIAiE5YDQsspW10dn9FmfGcYdyeWO0PAizX4bfmuggbvbAiEA1Inn00VPRLOSEODpj7CcTTzV809uGTRGZfXHuXvdBwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32351, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3adrCRA9TVsSAnZWagAA7Y8P/1yA/GMzqcyrIybcemFf\nh3lBfdt3UcZDO9hbCQjCigwpuN8fUwo8/9MGyDzu2woibqBrxBSN+yzVvfVb\niocaholwSI8lnvHD6oEc/cFQ275C8TzcPMa+/pZR+7dC6uLdRE2hhFYugZGh\n9YgSy6wHV9QAOnqEG26Ow45+jciLqvYgxNj8G+Ghf6m9sXuSw/C8leLiGwvr\njgStnRKyNS+1aLoDIMw7goiO7Yfo9lD2HvmUaD/cx1laBkQgqP0UleBw2qN1\nnV8+HhGN8wmc9/CqBKo2UXVRbJutG+/H6XGFvXfHzT9lRY3D2wsuGhCNPZJu\nf0KyUmMFjY5AKzsqJRsyr5flflvHLPQ2VbTx4r3V8ZJaLrbJ/MzZgQlDqJCo\n3Vdf9Iqs9qm9kKvg5zl7B4fDKKZZBz5EQ/pioHspU75wJjA0PUgDnYMiLv0g\n0vwedi8dtAOtvWHIvJuJQi2/qAAt+HwHo/IBuiQXzfd1W7DSVjxghVOwmDc4\nNTPQT2+oDsFZzHWs3M74BUxQNyUc4QGmBF4ByP94G0cCMnQA7IFnPlp80Y/4\no1T6tlLDl7pWAAS/RfzAAyVhB00+kxzHKMtin68KAhsxhqG1IzaD3WURNZ0K\nvlASAIvFCt7q8nZSgOdfxnSBSeI0/hvsgzwIh+P3jhjVoGi+PRUh7fEDV3fi\nNN4x\r\n=nk6x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "ff76d90d4e47ff4d123ceca2e9af8b1e5e1861c4", "scripts": {"test": "mocha ./test/fast.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "12.13.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.9.0_1574807402671_0.9800245248929691", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "aws4", "version": "1.9.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "7e33d8f7d449b3f673cd72deb9abdc552dbe528e", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.9.1.tgz", "fileCount": 6, "integrity": "sha512-wMHVg2EOHaMRxbzgFJ9gtjOOCrI80OHLG14rxi28XwOW8ux6IiEbRCGGGqCtdAIg4FQCbW20k9RsT4y3gJlFug==", "signatures": [{"sig": "MEQCIEEYNapZODusiiA+0k+XZti81MmPJlIN/GtAO8BCZk2vAiARd10/Fg3INyOkLZdthe5dXoJMvlWW6kKrGqeqPo2X5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHPx8CRA9TVsSAnZWagAAvMEP/jnJRHWums0M5NSFH9Aa\njCvVZ0pfWUslj46gxLSuyN3VEDVJcCCgNmSb0/T/aODbREc/fSpZKZ2FbQnK\n12wunrU2iPZQyqV6F15y4peWR6EZUtbkU6bl9CYRVDtlSvxLJWbLghh1yU20\np3O39X+l9doFQMRKLFVXJc0cDedHp81qS/yWeK4n6a3crFntoPCJwQvHplQ1\nbwApwofEmXLu579ajtjluqZFCCwxESNJpV/S5PuOz1696phudt8zDYolcL6d\nP97J8Z8Xura7l41P2B0UUUoil7SNxrmaZAGeUHKsus6UQE46GjkaX3cnlYr2\nzZZMa8QPJqCYha+ZWVlInVPOJvzxnCNzy8On3VpuHn5kW6eR7OF6HDEduVbF\nLfjh8EX6cOO1t+/A313SRwxh6zAain78qbWnjQ+Wq8Qwl3r/OlbFJuHEzC12\nDrO1a+6+pQ1wi4dJh9UbyLmuImHAZO0GWdHOLpxoDY92cnBlbUnkwnI+Md2N\noFmIelPxKX3Em0mnQEEjVIYW/aRt1o3Fpdrn/1Y5FBRW75T1sLj+Sy1mxSc+\nX8Ses/st/3gKw7vGs6ez1HMcNk3ktFvit+FC13vK84scZWh9q/nqCEB0SzS7\nryi57MCb5X9fsU85xOBuo87JMVKPRlEHNPkVtl0aCGwLsAjsIBNkf9iyUY9i\nKN1L\r\n=mdFt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "c45e001b09071094ee6dfa5f52429004b459f8cb", "scripts": {"test": "mocha ./test/fast.js -b -t 100s -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "12.14.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.9.1_1578957947737_0.4549390524295822", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "aws4", "version": "1.10.0", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "a17b3a8ea811060e74d47d306122400ad4497ae2", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.10.0.tgz", "fileCount": 6, "integrity": "sha512-3YDiu347mtVtjpyV3u5kVqQLP242c06zwDOgpeRnybmXlYYsLbtTrUBUm8i8srONt+FWobl5aibnU1030PeeuA==", "signatures": [{"sig": "MEUCIHJucmhiIGSz+JFoqunN4wLou/4+sQQdRAQmPYVVJcdVAiEAmNRvHYxhwRyOrx+v/6aNa/d7zNCCgEWX1Ohd89Dj/pY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeyEJeCRA9TVsSAnZWagAAS9YP/0M/tlgJFdmsgCIekHyU\nRh+roU6P6vIGZQ0rpYgV6yGxRikMJjJUwRi9mi7kQWASaWlvk/LiCM3c3/PN\n/4NeIH9qX3a3/mraEZZFWu3Ot1UAZqRW2+Aa6ipN8+rGmU1+1Gh+/6ztTRTd\nagAev8J9XNL+ZMrxjNUPENhDzFMdRQpZwiqvy52dajrHMq3C2qMfoVU/+Nm0\n2LiSYcjBLpLLOIS75Ammn6kw3XzThBKMMZDBvhyimXRs1pET+Ej/6rFJKAzG\ncIueaiFLSH6ekcgLBpXf8vExlhydDRRNXcqZKEMgA8EFwzrYPRzcIgslEPvM\nLL1/shDkgLaSqyp54fhZvojcdbJiBXsmTC8jeKoIcmLXGNA1eDitESRp2QgM\nLsKkIUYwosApGBGULwjYxJCbDECq/B0o7MRIoQDuFz84ZYHEj7GBhMOvKu/a\nsTM5ocRHm+qwqhAlIlrx7hjqNE4cTMYTExynFwye+FKWrTLl6+xwyqpQpkHr\nG99SgNT1q7QUQVUF0/LM4heju/T5Qab3WGHLs+hB2ylbHwVC1ftXzvxqkQ9U\ni4t4v5fa27H4gjXCxdq2kxwtQDCup87Ta8ZLP5Prog70mLID2fT0SUTWkuDX\nThaLQO15bVyR/mQzteggv/rUujS3tdK8/jYyKR4nBIRQ+AieUZpZ3glmXF5S\ndobo\r\n=x59z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "09c55bf6d424a4b5547150f0809f854bd14f650a", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.1.2", "should": "^13.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.10.0_1590182493870_0.10780918062212641", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "aws4", "version": "1.10.1", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "e1e82e4f3e999e2cfd61b161280d16a111f86428", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.10.1.tgz", "fileCount": 7, "integrity": "sha512-zg7Hz2k5lI8kb7U32998pRRFin7zJlkfezGJjUc2heaD4Pw2wObakCDVzkKztTm/Ln7eiVvYsjqak0Ed4LkMDA==", "signatures": [{"sig": "MEUCIQDt40E64GyHEl43skPAjNnGWBRfX/I8AM0H95zw2HtUWQIgSNNgb+7ZQ7mANB39Rs95SB9dhD9POGM3GbDvxiBk2Ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM+1vCRA9TVsSAnZWagAAJh0P/j7ZkqiJlGbi3tpIm5RZ\ntqkUOY3kIOa/j+1i8U4v3P1DW0tL1clJy+Yk4veVAO7gJMEwhm3OkJtz0Xh6\nzzY7QmDiKqEPHtee4Kil6p3bq/n8UiA3LHbrfKvNO8EkSFbUgFSZb2Hu/ESq\ncJP9ngERkQaEkG3ekdwNRl20ZGx/J4ymYj07ZC3fHuGitTTBWkDlXGQ7YVH7\nEZAgjSDucKjIq6nJScCIa/ehzOYVkMeGiePdv1HgXD8EyYw7wMOhX6XolYc/\nxFwLQ4vN0sjAu+EKAqXQS5ea6ZxO3WUBZs24zcN2Iiz4A0/21G7aKW4iu69D\ni6LhWRx32YwW+Rc5kd5HHpQfOYfJxjKbMli6ioxuVEA0m04EpDRQRKEng7Ms\nLfoKYymBwWG2K/x3HgeOS4PKdxYpUYLvcSlAwKv7ZOLUXG9244A4/6PRFiwr\nep3hd27/Sd/azsEzEYFt9zElIDSc3yxU3aHRkNWLikj6phnt6WnUiu8jWoQ+\na4VRWTWplHbVVvH2UHrSQdFwgGBaxxLKf8arph6mMTgZMWF4V4IgSWCgMZ9j\ns9UXAX8i+5VUhQCTK/F/Z9v671/c1qqY2lvYG+RhVoanQJM+JtFJqaf85eQq\n1N8CzMIzvweffSxjsCujt7kdhB8eipwWAZwnhRDj/TUu2jJLxZ2ljTqbXSx/\n35hI\r\n=5Dcs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "cfbf3e38012ab82be48518048d1ed87fbca5bf5a", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.5.3", "should": "^8.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.10.1_1597238638752_0.4615872487755939", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "aws4", "version": "1.11.0", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d61f46d83b2519250e2784daf5b09479a8b41c59", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.11.0.tgz", "fileCount": 7, "integrity": "sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA==", "signatures": [{"sig": "MEYCIQCbrslZsUbWvSncxALWkVXRZ94QRxzRYF7smzcVF5Fx2wIhAMmMkxSYy0zPeSeX6FgRhHKYcNBaPS8Xyb08Y53f/0im", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnKsaCRA9TVsSAnZWagAAJ4kQAIJu1zjLbIqCm5znu9tQ\nx/xYn5IQdN2+UTSGz/vbc/yRaS6H/ErfuBSIWOcySorAb6u7TnIEr4jmVddX\nfrh0uOwE9AeBBoULGQ9I3oy2YhG9Ari2XpYt47r8SgZl2vrIuH9mepSe+0wn\nnjdV1KowH5rSR1xNvhyDX8jkoiI5nSghmoaeZUQlYWiUpie7Yb9FxhBWbK2W\n3ANFjsW7mNmlSfOUd3ZHnbijpTfYVWbolNX+0B6lctxj2O2t3NkTS8YPM3Mv\ng60YW8kPPuwZFqxgMwTeLkJJ/EgwY/6KaajfGh2JbSXzVkwwIpU600kGF+NJ\nPzlV75fmS0rToaRltTU2SElEtVuCt8jMpBLHpLLbUoZGgERdevAYhE5ERgqI\nE7nSwN7YtVbkoTJDskhP7Am4MigSsMqaxE36awN7gzMRBIUDvT/JOKHfPfcY\ngoNF4Q0MQAStnZ3BujnNlNDH4Wau7j75UWaFXCCgeyHUZBpJ5rCk1pVucu/P\n2+Ia/sk//+4Yrr5eqn1jWyJ+3xZZAhopp7f08kLmtrL3zdUP9YOJV1ndcZ/n\nBHxtuGC8d+YKlPomiZzMVGOWA3WEad7ojJWoB6X0uZuojxL5WBOlYh/wlgij\npVcX7z20ezL4F/DS/NZ8B/VRYJhj9WR4znlkavIzyi4GKUAf890CM5mGA/1Y\n6y1r\r\n=ZwJs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "771266fba5a7917375e97d4e8af23d6be3894e5b", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.5.3", "should": "^8.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.11.0_1604102938108_0.8570953004911339", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "aws4", "version": "1.12.0", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "ce1c9d143389679e253b314241ea9aa5cec980d3", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.12.0.tgz", "fileCount": 7, "integrity": "sha512-NmWvPnx0F1SfrQbYwOi7OeaNGokp9XhzNioJ/CSBs8Qa4vxug81mhJEAVZwxXuBmYB5KDRfMq/F3RR0BIU7sWg==", "signatures": [{"sig": "MEUCIQC7TJIahcwnIj6A0x0t+UkkXyOpxLgEwfnmoSCycRpr9wIgb9l0QtuKXG+ChkPMdVqj/dXC94qeuk04TetWgWsVJKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjue8vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfvA//dHKJ5Ihkw4FTEPAhowuDzKz723dn4BykP7u4gM/qCt2Z+Aue\r\nQyFJkh8f2KuRX2SP9OiG2Zuj3p63qli0zmYp8V1+pjThK9U46wHSTz+cTj+m\r\n42yfd6iJSYCgD52mx6iihAoJLZpgerFUJjkIlskLuQpVATnalfQs/ItylxoF\r\nh9vWjzHHsHnBMz3fUkBrbUpMRFAn1Y31ImJ8u0Vj6lBst5jZYB7B9FgbXZ/+\r\nKCJK3Ya1CU3v13wD54LZUJIZ86VW+130rtCuMR/gywZbzy/Wi5DdmOpq2+76\r\nAV2x6BTpTw3j13XBlkQR5GkZaHH04xUA85meyP1FRljlF+U5q2okA67dWTaj\r\nw2D+TEtkqoD2JWuJIQiJfTecB/Uq55jhdJm4IVIWwR8IqO3CJ99BnQXdJId0\r\nevSqFOoSVWe8JmkG1ux2YwVYGxdmV8ZQk5w+O2e2J+HA7i0+Voq6u1jpLbPG\r\ns/3ie5MYFhVl/XNIb+9jOSPh3zSGP6R55Zm4lMGkEwH3BGLW1ZgI2fHdufiD\r\nnIRzXYVWRapBawr11g3/H26KJha7GcrttKeR+6bbq2/u/2q6mBge9jK50UXQ\r\nI66IGt1bUWGi9TApJHy5W/vshbduz3ksUTIYMvNfcXpjBBU82WM1P6+mtmHN\r\nDdNVyz+ayGkUIdp8NqoUMXFz68QYjk0+PcQ=\r\n=e5Mb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "aws4.js", "gitHead": "317c243e311f06b0722ffb3124508e6f55388fe8", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "8.14.0", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.5.3", "should": "^8.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.12.0_1673129775407_0.9108890325740726", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "aws4", "version": "1.13.0", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d9b802e9bb9c248d7be5f7f5ef178dc3684e9dcc", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.13.0.tgz", "fileCount": 5, "integrity": "sha512-3AungXC4I8kKsS9PuS4JH2nc+0bVY/mjgrephHTIi8fpEeGsTHBUJeosp0Wc1myYMElmD0B3Oc4XL/HVJ4PV2g==", "signatures": [{"sig": "MEUCIQDEmkEtk3uTanbVC/vsoGilS5XZroYHZcVCvnp3LfJe2wIgTYjgwiaOlkLeCUE2KsiR5uKlH43rjvdAAznhTxphf0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23431}, "main": "aws4.js", "gitHead": "ff2884c83838658b2b358fa52bc286d85a33e22d", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "20.11.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "should": "^13.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.13.0_1716269631582_0.8269839226903966", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "aws4", "version": "1.13.1", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.13.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "bb5f8b8a20739f6ae1caeaf7eea2c7913df8048e", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.13.1.tgz", "fileCount": 5, "integrity": "sha512-u5w79Rd7SU4JaIlA/zFqG+gOiuq25q5VLyZ8E+ijJeILuTxVzZgp2CaGw/UTw6pXYN9XMO9yiqj/nEHmhTG5CA==", "signatures": [{"sig": "MEQCIGcAvmgl6HfQIFT3/i84Mm+UpAHvJt0HS0h0z1UBWzcCAiAy0S4CSTXO5n/bofCd976BPMLgn0VPnEFFoptD7l8veg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23389}, "main": "aws4.js", "gitHead": "0056f2170c9cf16ab1c5f533f1e1d707b87dcf01", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.7.0", "should": "^13.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.13.1_1722907630011_0.007705574125395964", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "aws4", "version": "1.13.2", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.13.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "0aa167216965ac9474ccfa83892cfb6b3e1e52ef", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.13.2.tgz", "fileCount": 5, "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==", "signatures": [{"sig": "MEUCIQCxpXtpAm4SCHPtBJoCDUXh8zHfIWZY9lrBdi7n1epBzAIgdXvSxI9zIT9xWNp2TYJMgyKxSwdYipqiXIJ7bYRi1s8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23399}, "main": "aws4.js", "gitHead": "2bc7517338d86620709554845a437d174f48093a", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.7.3", "should": "^13.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.13.2_1724831030666_0.649751079429131", "host": "s3://npm-registry-packages"}}, "0.6.1": {"name": "aws4", "version": "0.6.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "999a3ec007f82fd496600df43456783308b6f799", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.6.1.tgz", "fileCount": 1382, "integrity": "sha512-2O9aF43zDB5Asnsr09YvqoBTVE9+i3aslfRsN+4rPo5jrBP1TOysOOSzS82BZfqLErOyooFDc7aqHuRfW/rLcQ==", "signatures": [{"sig": "MEUCIQDyY+spWYVG++9jBpNqaD2KY2uSazsufrxd0zBnLmSEnQIgeKWyeODqggQapB/MeFFmqkHZuaWfTVXN5YvdDg24NUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6709953}, "main": "aws4.js", "gitHead": "f1d52fa8bf0c0054b47c3952ff0934e9a21b5514", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.6.1_1724831291461_0.27915486437535564", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "aws4", "version": "0.5.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "e82a29c5d9cf6e5ef54b125fefdf0de4dfa4ae78", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.5.2.tgz", "fileCount": 1382, "integrity": "sha512-QIHpQcbZCd7byeKbLopa9Q5VbkdPESefWB2lGy3LBLgYh/8UkK+EReOhxXRee9l6RNGmzS8zthaUGsYREVff6g==", "signatures": [{"sig": "MEYCIQDP/NNuMPPjfZ6Vv1FdwbWcsRCeUvc7VgilWGxXf2FAMwIhAIe1p/B3JxqajUKluUONTQAUg1RB4NTKZXvAaGv4lqUx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6708759}, "main": "aws4.js", "gitHead": "e77409e38496065e533ac3de9eb22c4b08308cbb", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.5.2_1724831785245_0.544472140109586", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "aws4", "version": "1.12.1", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "bc8a76805391a2ec213d2da72f47d42bf3ff71ad", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.12.1.tgz", "fileCount": 7, "integrity": "sha512-XbkGJdb+XQs0ZTnRc3f2gasGgBhXWZKVs16wmbNKYAjIfGfi7X3obtFPG55u+KZwDD522EigaYCKKeSw2Sk/bw==", "signatures": [{"sig": "MEYCIQClMvDq7oeoUN+KIdnIfg7Km6gQfzEiHS8RK3OB5aIXlQIhAJs9lK08+KmAqMa7iOlUfM9O7Opl+PifyKbart3mgC71", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23526}, "main": "aws4.js", "gitHead": "0cd9fc079c894dd96d082eaf90fd655617101c95", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.5.3", "should": "^8.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.12.1_1724832364923_0.2910276542930603", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "aws4", "version": "1.11.1", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "e91675bae88460d4ba057e826e85002fa095b770", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.11.1.tgz", "fileCount": 7, "integrity": "sha512-knrCHPExfV1Hju0IIfzqmoN3A4SgebHytAY0IHVmS1J8Dx5b37Yy4WVR0tR5swWClCQBJdOI4w/CUbJ0vhWwfQ==", "signatures": [{"sig": "MEQCIHYmYT9J8diSj0rQqHE9zMA6XCNUO9Mcx9+cskg5mi5PAiAc+NZMZ5FQu05zv4ZyZFm/zAdTzc/q75mTxI/ONlj+/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22289}, "main": "aws4.js", "gitHead": "95897cb6bf6145a3a53380efa8054a9fd0718ffc", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.5.3", "should": "^8.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.11.1_1724832454810_0.739041836811533", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "aws4", "version": "1.8.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "f1e117d8b0d54765616e02fd632fa9317aa605de", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.8.1.tgz", "fileCount": 6, "integrity": "sha512-jjjbSWcLMFZqtvluDc5DFab/fdcpSrj39H7bFdNjDoB/UD0UyD4RlNnD4WUY1LNiuA+9e5RdsYSCdE+Vl8XQ6Q==", "signatures": [{"sig": "MEUCIQCRuFsaDyURU328fNFDFKk9Qn1S5PzLzgvElal1wi6d3wIgUSQPT4elb9qolnRBlxGoRbxX77xMeAuDMKt5svAX1XI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31933}, "main": "aws4.js", "gitHead": "dcc0760015fff98d92f16cf580c55360efc3b99d", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.8.1_1724832569038_0.39652646747295006", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "aws4", "version": "1.9.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "9969ce43813caae88bb5c7e9eb5edc50a80e3264", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.9.2.tgz", "fileCount": 6, "integrity": "sha512-iyK02XM2uP3lW86v9OEoNzF2iubkvThsuBquqKmNWZHFRaMA6VqDM9FtDCQEn4ztdszkSsjAtdnIq9CSrvqsGA==", "signatures": [{"sig": "MEQCIGFnzEZthTXbdbGTFxttoa0WnqHSq1bWJNUglDiSR3VNAiAZ0g2nGzOyz3LLLvzBxy539iy1P8A3hZD5DgCenLxsIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32655}, "main": "aws4.js", "gitHead": "bcbd34a7294508fff546f1962c1e0126f6fae9d8", "scripts": {"test": "mocha ./test/fast.js -b -t 100s -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.9.2_1724832686360_0.08999235492627", "host": "s3://npm-registry-packages"}}, "1.10.2": {"name": "aws4", "version": "1.10.2", "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.10.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "96274c02f33b6f72177b92259a03487144adddd1", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.10.2.tgz", "fileCount": 7, "integrity": "sha512-+5Gh87PVOfsBEIrZKhYm58/gloaOYvkkePUtd5J9kz8TamoDkoEnYE/HIcJaXWfYqX5PbvYwtddxO+ogL3k3SQ==", "signatures": [{"sig": "MEYCIQDmkozH9HL0fl9o3+7+3BlpDEPvk56y5N6Ixmz/0yIR+wIhANvvtxJ+pOFsK0FS9jCR30f7rX96dvpmtOLDANDcBYcw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21519}, "main": "aws4.js", "gitHead": "10bcfe36b6c041584b1bfb76b5b37c2138b24a25", "scripts": {"test": "mocha ./test/fast.js -R list", "integration": "node ./test/slow.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.5.3", "should": "^8.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.10.2_1724832784886_0.7914640097693018", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "aws4", "version": "1.7.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "086f16ee1483dc4d78af1ea98ed4cf4adf2e9846", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.7.1.tgz", "fileCount": 6, "integrity": "sha512-3g+3j+YZPWLhccveu83ftGQVk3XkcnlKngqEqEW02Yc825OzNSdTDeQtNDUYhEGyOXQZ37hRmP6yTPQt7TO7Lg==", "signatures": [{"sig": "MEUCIQDtclur1crwzik86SYQkoelEJ2xaV/dLbYWq6h8NA7iVgIgQPsY+jrfbsoz4283RUO/RGKuODliWLxnTfhHSG2xCNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31926}, "main": "aws4.js", "gitHead": "0683589f0491e962d7a7dd9de432d3ecc19c3cc0", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.7.1_1724837966094_0.6814770881060026", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "aws4", "version": "1.6.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "5cc00b88f29786c7538c719114140aaf817b2424", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.6.1.tgz", "fileCount": 6, "integrity": "sha512-4y2zorPP2l/nrx8Oc6okjcPWuOVN0AMjUS1FWTbVJD8XbViwKKDpjxELv1eiy/+pu5F5hGKSQmbj3Do+PpJAXw==", "signatures": [{"sig": "MEYCIQCNlXCn63OCDQs1ElzdHlIbH22lGNh3aJ2EOEIvMqyKcAIhAIw20rA1cFY2M7AVCKWotNC6n8K5iwR2rBB/oNqv5J42", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31930}, "main": "aws4.js", "gitHead": "d32b2ce9347ca30939629fb47b235c085cccfb19", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.6.1_1724838046745_0.4997043189857442", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "aws4", "version": "1.5.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "bb1294878de6d6b73a0397b3cd71b4cd1a852e69", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.5.1.tgz", "fileCount": 6, "integrity": "sha512-55rvuqEHBD2kdJSJ+nh8V3v3sLaLyDB8/Nk6REKOsIrppjhHeAD9/3vdGsylMOvdb33BK9FZgsniCwE/yurJ6A==", "signatures": [{"sig": "MEQCIDQIH0vt75bRahzx6ZJgiFhfUbJi7j6cNIDqfvUmOn72AiB7QCwnZ0FcHnD15YiqdpwXxi6ZcZNK2WJpt34QLWoboA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31556}, "main": "aws4.js", "gitHead": "a2d6c7a1d7448270d277386da3038c68ad2d958f", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.5.1_1724838111592_0.09837751811867568", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "aws4", "version": "1.4.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "3b0ecf5e362a9b8b33b0cb2056cc85c1a1fbc648", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.4.2.tgz", "fileCount": 6, "integrity": "sha512-K8LA0HioG1sQ6OeirNYFd44EK9j4LT2MLQKc6DTQi/peVoiLXTfabPWgaK3T22+SkwpHunrDzvl8RTROfbvfjg==", "signatures": [{"sig": "MEYCIQCGzfDp8ZnWjoHix7wA42oUJOs9GGQ/SqoZW4TwSHBeMQIhALqs+r6DlvXrh2sShCfeGJjnyG8i6B8gSTM9PVyEZqJ/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30987}, "main": "aws4.js", "gitHead": "6f9d9b0a3d7216a761159be5d4feb9b67aa0a3cd", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.4.2_1724838175887_0.08455331185655468", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "aws4", "version": "1.3.3", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d4e7ca6ef2854361990d1de880aa002ec2f9c8d5", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.3.3.tgz", "fileCount": 5, "integrity": "sha512-1PpWsOTkiRfGQO/ynk3+T4kV3oaq6gPKBOlnUkTVdcT32JXr4I6YvZGBNTsRk8vUrfT9oMg7OtZ1I+IWvVXhOA==", "signatures": [{"sig": "MEUCIQCHfv3vHtMCcHiMmkLsUA7PYQtggm/AFwg4bMCSuqndvwIgHRnRXM/v6wITvTqNa+cp0Xz9939ds3zSJt0sx7JFs/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29128}, "main": "aws4.js", "gitHead": "5d4b204e3304e6e8c24be281ae6283f0a72653e7", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.4.5", "should": "^8.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.3.3_1724838243036_0.820938416681873", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "aws4", "version": "1.2.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d66cbf88d0c8df4a8a2392bd1fefaa549f8babef", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.2.2.tgz", "fileCount": 1382, "integrity": "sha512-kvp+EqxytO0xJ1Mwcw1cEYgHpzdxonksJKcZqZ+p+15TknHjuGdjAU9WElzbKSay6XPjTSopkQl5OlEcHZSHPQ==", "signatures": [{"sig": "MEQCIFGqbHGGXcwPmRHV0CIqOmekryGyoFFTPcZLL8UCFtpFAiAXyB70ZbC135AVBlySgCrWRcuAtYyix9knpBLFEPJJuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6712532}, "main": "aws4.js", "gitHead": "1f9a8c5e374bd680970ae6cd9ce93ea7c0175361", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.6.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.2.2_1724838432639_0.4046351448284464", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "aws4", "version": "1.1.1", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "08342810399e335132263cdc512afbbfd4bc9cce", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.1.1.tgz", "fileCount": 1382, "integrity": "sha512-erj2yp44DTFkS+x86IFgzNtM2vRBv5j45i6WlXhU0jIczDU9f74HfKE2LGt9kjBzNxTVTm9yqtBYnQSmCW8igA==", "signatures": [{"sig": "MEUCIQCO51fry/DOlwMtPJpviLfI2iH42JBvoNLvT4dbU2/wuAIgJA3j2iI0R/F98Q60qhwE9lkUrrTYrwoYTnEmeM3JlhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6710433}, "main": "aws4.js", "gitHead": "88c0b2ed2f1e5207e1362422d95c8b633d7c036a", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.6.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.1.1_1724838493960_0.723867393221358", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "aws4", "version": "1.0.5", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "2589c4ab0ec2ab7638d64f3a43f1b0f3ec4ceb27", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.5.tgz", "fileCount": 1382, "integrity": "sha512-IWZg2oU8u6oStYOMXQlzapxuwfeXVCIrpH6ffypQft5ooncYbDlXH1ZrTnF8DaYfVwia9Y4jV59V8PV+xm7Vrg==", "signatures": [{"sig": "MEUCIQCnh/xIc04aAdkRUnKXJZlugmuyxFoo4eK8HOiD0xKnOAIgKQEqk5e5VsKkuLCk5nMTaEWjaTkaP0iO3PcLPBeiF8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6710122}, "main": "aws4.js", "gitHead": "f66670b68e2448d0ae11c354562fd5721b891af0", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.6.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.0.5_1724838563898_0.2787709524892146", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "aws4", "version": "0.4.3", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "1d1126a645e55e12124c2d025c703a581767a016", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.4.3.tgz", "fileCount": 1382, "integrity": "sha512-jkznMm4p2OxtgwVWy7CJ4qelYE3Zoivh0QrhzGX3pVr0nfta79a8BjFmo0BFV51BRQBvT9Jhg9GW25daezGjTA==", "signatures": [{"sig": "MEUCIQCXMkutimzzmje1wc5lE0uCXx8fQ0YKsGFDG9t+xj/3uwIgUO/oB0EbBjNhY8bpYE7xffQvHwWvbW+1Lccq1Btzgu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6708022}, "main": "aws4.js", "gitHead": "5ca1301f27fe3e997912eb01acd1d2621948e88a", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "~2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.4.3_1724838640593_0.19924469913693366", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "aws4", "version": "0.3.1", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "a2eb511eae6e7618ec504496d9a2bb64ba7d8655", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.3.1.tgz", "fileCount": 1382, "integrity": "sha512-X6skbOAdEF0acr5oK75KhPFrCxb1+/SPBZ22lGwo/eU4QvIun8ceSSmdYagOq7Zsws/CgdLbUkClbE47QPXvjA==", "signatures": [{"sig": "MEQCIDxuXkgI2ukfpokO2TdlunkCJVJF3KcUWv1mCyHGB4+nAiB87q3PggW8ZOBy6BI2Y+4WtMr7iVZzASaXcHWkGwN12w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702357}, "main": "aws4.js", "gitHead": "52372211e5a4966c39e6fd395368a61835114329", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "~2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.3.1_1724838741303_0.47472793117763334", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "aws4", "version": "0.2.4", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "34e2ceca0575a51c68b5bc062facdfc736b134cb", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.2.4.tgz", "fileCount": 1382, "integrity": "sha512-sclM/bzEmQkNoj8lhI763pNwW2HeMUeRa/66G9Ox5kYm6ZHjpombUv9AJI/9i1mlq4sTDBnCAZkf9VYyFYEJTg==", "signatures": [{"sig": "MEYCIQDLztGLz6GR7TItZEeEmDRjJ6Epz2RBWwrR7dwhJNRW1wIhALIpHaRzwXqs/JycajD0mRh1H7GFNCyZwDPKeGZZq7Zg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6702018}, "main": "aws4.js", "gitHead": "b1c4f9302df9fe341812cdc232e4c6df876ee679", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "~2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.2.4_1724838813500_0.1821360619924366", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "aws4", "version": "1.2.3", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "16f4e775d89dc4158b74ee99793a150447c1fb7b", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.2.3.tgz", "fileCount": 6, "integrity": "sha512-coRaQ/d2bSwxyRx7f7zLYseP4NRICoorDq5Ddp5104Y7GkLNSW93GSEyPLOqybHxLr7b3dTIJHyAvaEKv/oB/g==", "signatures": [{"sig": "MEYCIQDC05Mmqf+/vD1THPEkZFImLYuYXOLZ/dlVHu5YIy91DwIhAJVtPsZMIAaXyOv6CIIBHQigrO4PLhXGPzqpnTyR23lZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38848}, "main": "aws4.js", "gitHead": "a328fe473418b17b7a87beb9bb4d258d5b1bac04", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.6.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.2.3_1724839021989_0.44626503528629824", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "aws4", "version": "1.1.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "0f9cd0140f0991cce7f4173561a2bd41248bda3d", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.1.2.tgz", "fileCount": 6, "integrity": "sha512-LtBcA/Q6FJyV9jBG0nTvr3XgZQfTMqY8OJcF4hj9pCReTqDazeZRijIqNFYXNQwIXuol8V75MUP+DyYApbgc9Q==", "signatures": [{"sig": "MEQCIGClUUe5DFdfJtGDCZAcFlzvtl7AM1NWvSBincVaTiBXAiAA0cFt97lmUWRa8lyvOEVv/kK9FsFgzbjeHp2x4tiyYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36749}, "main": "aws4.js", "gitHead": "e6883c4ade628d5cd4e129606f08f6b85429ddae", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.6.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.1.2_1724839136806_0.9675475738714272", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "aws4", "version": "1.0.6", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "ecf32d3b63ac64bc307ce01d6ea34002d1deddb3", "tarball": "https://registry.npmjs.org/aws4/-/aws4-1.0.6.tgz", "fileCount": 6, "integrity": "sha512-SiG9Dt9ybD5WaUVNyLVpdmmjGy+G6zQ1mnc5CBiY8E0xS5/eqDvojzpK3Z71gigOUcUn/b2nJjyPRPSyf5P9xQ==", "signatures": [{"sig": "MEUCIQDcY4/UFXBJVB87kAts09HVHC+g+RVUVHuE5lyqyonB/gIgeLsAbivVfYkbsVxcn6XhEtHcZxsyW4RFTLRJ+UZsOaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36438}, "main": "aws4.js", "gitHead": "74c1309be548b1254eefde7908ec029de996e978", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.6.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.5", "should": "^7.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_1.0.6_1724839177702_0.6609630002552693", "host": "s3://npm-registry-packages"}}, "0.6.2": {"name": "aws4", "version": "0.6.2", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "cb4dc425924ca3cca59eb34c3e4ec00d44883e41", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.6.2.tgz", "fileCount": 6, "integrity": "sha512-ZokJFKgYeKC8DOjsAPHtubKGaklG6B3esh4kbbxNToT7Xn8Ha3Mm93B1rQwfaN7vPcf3Mmz3i9YcDjypL8zkTA==", "signatures": [{"sig": "MEUCIQCj5tj80g1t9xyMf5ZAssEfmmnFOrdDn0dRy2NQwIA6/wIgS41Y6FQVxBWDeLJE8rgqIu0LnpLjXWPM6Z6THqsHCPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36269}, "main": "aws4.js", "gitHead": "eb2179a2ecce4e205ef8b80aad0516d977689538", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.6.2_1724839213370_0.5356898622245632", "host": "s3://npm-registry-packages"}}, "0.5.3": {"name": "aws4", "version": "0.5.3", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "f9f6f235994ca298c75071f359aee4424e7100ca", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.5.3.tgz", "fileCount": 6, "integrity": "sha512-eQnFhWCCvyB0/QH2lKZ/73Q8tLK2H/OJ03vzkf/ueHZXjQyi0TKLH2768Td77QhjAQ+fhvwNeoNn6xyUiHXy6g==", "signatures": [{"sig": "MEYCIQDvUAOa5q/q0k9H22qNFPIpFW/8V6YhxIAGzk/siNLuQAIhAKGOHJGK400JsXMaQsgvdEI2IBAK41ZabZ1hIMJJvBaO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35075}, "main": "aws4.js", "gitHead": "cc4e69125ea317fdf1fddf1e8a46c74402d9df72", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "^2.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.5.3_1724839268561_0.914605148337525", "host": "s3://npm-registry-packages"}}, "0.4.4": {"name": "aws4", "version": "0.4.4", "keywords": ["amazon", "aws", "signature", "s3", "ec2", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "kinesis", "lambda", "glacier", "sqs", "sns", "iam", "sts", "ses", "swf", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudtrail", "cloudfront", "codedeploy", "elasticache", "elasticmapreduce", "elastictranscoder", "emr", "cloudwatch", "mobileanalytics", "cognitoidentity", "cognitosync", "cognito", "containerservice", "ecs", "appstream", "keymanagementservice", "kms", "config", "cloudhsm", "route53", "route53domains", "logs"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "d690ad7015c94041b4bc32025b194658234f2a08", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.4.4.tgz", "fileCount": 6, "integrity": "sha512-RO0/iRzcSp/OOCi9fgGih29U3TXXaLzQ/pIxXLuJWrhIggMXYOEa5dkNf2F3EpgPMTr0iCKUvhLZ5+xBGxaQIw==", "signatures": [{"sig": "MEQCIDt26TesbECcMh537IgxMV1Ak4gN8us+/DGRKzLwT6tUAiANkXhiyAzDPOCtpH2scpjaUnq+8uRNrds9D6K3TXS0SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34338}, "main": "aws4.js", "gitHead": "bb67ef7365d88c651daf51b08d8c53ea04ea6fba", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "~2.3.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.4.4_1724839293322_0.8890356633178924", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "aws4", "version": "0.3.2", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "44eb62038bf4d17fac7436507ad6432d3d77ab0b", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.3.2.tgz", "fileCount": 6, "integrity": "sha512-XOKBokE4bAgQdEC03Z/iCcL4QpPWg5oWw3W9UZ6Wt/T+j2U100gM7b7rvFe6nMB67OdyhAVSIncq9Y95LClXGA==", "signatures": [{"sig": "MEQCIEWCSh7PSOw0lEUojIFdvoM+1dgSSgNFY0aUVOkZlCYMAiAkEarO0i9z5jYebcQbIsXO/hVCtmRBDGJtT/K9gsV+jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28673}, "main": "aws4.js", "gitHead": "6ec2499b90284f60c4da1b45d0e92176ab0e1e0c", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "~2.3.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.3.2_1724839318225_0.6779792072240423", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "aws4", "version": "0.2.5", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "author": {"url": "http://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "aws4@0.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mhart/aws4#readme", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "dist": {"shasum": "4788db8271bf798eb9646a78131541356af5d0a9", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.2.5.tgz", "fileCount": 6, "integrity": "sha512-rez50Ij3LhLFa6QOx4R0BFM1nHK+pOhITHr7AYbigf+kYdhNO7YB0GWO+ithlq80Bdw++JG3DfTv6zju0QEntQ==", "signatures": [{"sig": "MEUCIDO2EMLji2Sazxuh2X3xTBr1a9zDFyGPgi80bxql3avYAiEAotFMzznSBARYdT5JRfJTn22Vj2mgxm8/gWnOfUIh25A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28334}, "main": "aws4.js", "gitHead": "4f85289df2e23ea70655fa0c25b94d7b4a2102b0", "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Signs and prepares requests using AWS Signature Version 4", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"lru-cache": "~2.3.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "~1.7.4", "should": "~1.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/aws4_0.2.5_1724839347491_0.05939814530142651", "host": "s3://npm-registry-packages"}}, "0.1.13": {"name": "aws4", "version": "0.1.13", "description": "Signs and prepares requests using AWS Signature Version 4", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/mhart"}, "main": "aws4.js", "keywords": ["amazon", "aws", "signature", "autoscaling", "cloudformation", "elasticloadbalancing", "elb", "elasticbeanstalk", "cloudsearch", "dynamodb", "glacier", "sqs", "sns", "iam", "sts", "ses", "storagegateway", "datapipeline", "directconnect", "redshift", "opsworks", "rds", "monitoring", "cloudwatch"], "repository": {"type": "git", "url": "git+https://github.com/mhart/aws4.git"}, "license": "MIT", "devDependencies": {"should": "~1.2.1", "mocha": "~1.7.4"}, "scripts": {"test": "mocha ./test/fast.js ./test/slow.js -b -t 100s -R list"}, "_id": "aws4@0.1.13", "readmeFilename": "README.md", "gitHead": "eef2ba9645f6e48182ef60aba95fda95632f09d6", "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "homepage": "https://github.com/mhart/aws4#readme", "_nodeVersion": "22.5.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-/mx7ZpKfqzQrQ861Hu+RgEzhuuITgpkGcIm0ux4D9/hg4vBt0n+uZOE/XMv0rWmYoiIXyiB8TU6gl75igla8Yw==", "shasum": "03ae47ddc39440040eb3e9c7cb0c8111876f7e99", "tarball": "https://registry.npmjs.org/aws4/-/aws4-0.1.13.tgz", "fileCount": 8, "unpackedSize": 26653, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJreUT/WHQ1ZLDpBmCfc4sP3x3Oa11jgE1KytBjt24xAIhAMGLZKw7p8FOlTAg+2qwHtm8/LnDjtYZ45k+wFMLzNiz"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/aws4_0.1.13_1724839418136_0.6797901795299115"}, "_hasShrinkwrap": false}}, "time": {"created": "2012-12-26T04:43:37.518Z", "modified": "2024-08-28T10:03:38.460Z", "0.0.1": "2012-12-26T04:43:41.495Z", "0.1.0": "2012-12-26T05:36:58.627Z", "0.1.1": "2012-12-26T06:53:28.885Z", "0.1.2": "2012-12-26T08:28:22.376Z", "0.1.3": "2012-12-26T08:46:07.150Z", "0.1.4": "2013-01-11T05:14:48.712Z", "0.1.5": "2013-01-11T11:22:48.897Z", "0.1.6": "2013-01-12T06:42:59.595Z", "0.1.7": "2013-01-12T07:13:53.650Z", "0.1.8": "2013-01-13T11:35:43.284Z", "0.1.9": "2013-02-20T04:31:18.685Z", "0.1.10": "2013-02-20T05:21:29.413Z", "0.1.12": "2013-06-19T02:09:54.455Z", "0.2.0": "2013-10-06T04:14:54.000Z", "0.2.1": "2013-11-12T09:50:11.173Z", "0.2.2": "2014-09-09T20:33:37.707Z", "0.2.3": "2014-09-09T20:34:55.791Z", "0.3.0": "2014-10-02T03:45:24.482Z", "0.4.0": "2014-12-31T02:48:22.493Z", "0.4.1": "2014-12-31T03:23:46.527Z", "0.4.2": "2015-01-08T23:27:59.859Z", "0.5.0": "2015-03-16T16:38:42.925Z", "0.5.1": "2015-03-16T16:56:23.217Z", "1.0.0": "2015-03-17T00:12:50.344Z", "1.0.1": "2015-04-03T14:08:02.613Z", "1.0.2": "2015-06-30T19:13:29.751Z", "1.0.3": "2015-06-30T20:18:08.420Z", "1.0.4": "2015-07-01T15:22:43.662Z", "1.1.0": "2015-10-12T22:24:06.868Z", "1.2.0": "2015-12-29T21:59:42.644Z", "1.2.1": "2015-12-29T22:10:42.693Z", "1.3.1": "2016-02-29T19:42:23.040Z", "1.3.2": "2016-03-01T22:32:26.246Z", "1.4.0": "2016-05-06T22:06:38.080Z", "1.4.1": "2016-05-07T17:47:00.756Z", "1.5.0": "2016-10-11T22:51:00.450Z", "1.6.0": "2017-02-07T15:38:54.676Z", "1.7.0": "2018-04-07T22:24:14.670Z", "1.8.0": "2018-08-06T14:12:12.685Z", "1.9.0": "2019-11-26T22:30:02.810Z", "1.9.1": "2020-01-13T23:25:47.909Z", "1.10.0": "2020-05-22T21:21:33.965Z", "1.10.1": "2020-08-12T13:23:58.846Z", "1.11.0": "2020-10-31T00:08:58.241Z", "1.12.0": "2023-01-07T22:16:15.596Z", "1.13.0": "2024-05-21T05:33:51.716Z", "1.13.1": "2024-08-06T01:27:10.176Z", "1.13.2": "2024-08-28T07:43:50.865Z", "0.6.1": "2024-08-28T07:48:11.657Z", "0.5.2": "2024-08-28T07:56:25.494Z", "1.12.1": "2024-08-28T08:06:05.185Z", "1.11.1": "2024-08-28T08:07:34.998Z", "1.8.1": "2024-08-28T08:09:29.322Z", "1.9.2": "2024-08-28T08:11:26.560Z", "1.10.2": "2024-08-28T08:13:05.046Z", "1.7.1": "2024-08-28T09:39:26.252Z", "1.6.1": "2024-08-28T09:40:46.898Z", "1.5.1": "2024-08-28T09:41:51.712Z", "1.4.2": "2024-08-28T09:42:56.045Z", "1.3.3": "2024-08-28T09:44:03.186Z", "1.2.2": "2024-08-28T09:47:12.882Z", "1.1.1": "2024-08-28T09:48:14.208Z", "1.0.5": "2024-08-28T09:49:24.121Z", "0.4.3": "2024-08-28T09:50:40.892Z", "0.3.1": "2024-08-28T09:52:21.531Z", "0.2.4": "2024-08-28T09:53:33.654Z", "1.2.3": "2024-08-28T09:57:02.145Z", "1.1.2": "2024-08-28T09:58:56.979Z", "1.0.6": "2024-08-28T09:59:37.919Z", "0.6.2": "2024-08-28T10:00:13.721Z", "0.5.3": "2024-08-28T10:01:08.705Z", "0.4.4": "2024-08-28T10:01:33.456Z", "0.3.2": "2024-08-28T10:01:58.356Z", "0.2.5": "2024-08-28T10:02:27.695Z", "0.1.13": "2024-08-28T10:03:38.282Z"}, "bugs": {"url": "https://github.com/mhart/aws4/issues"}, "author": {"url": "https://github.com/mhart", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/mhart/aws4#readme", "repository": {"url": "git+https://github.com/mhart/aws4.git", "type": "git"}, "description": "Signs and prepares requests using AWS Signature Version 4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"kkuehl": true, "gjwiley": true, "goliatone": true, "necanicum": true, "anacierdem": true, "basicsharp": true, "quocnguyen": true, "esperluette": true, "jonniespratley": true, "bigmountainideas": true, "maemichi-monosense": true}}