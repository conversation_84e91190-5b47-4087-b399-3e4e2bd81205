{"_id": "performance-now", "_rev": "43-1a03e61c481320ab5cf6607c329b14e9", "name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.1.0": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "0.1.0", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/meryn/performance-now", "bugs": "https://github.com/meryn/performance-now/issues", "repository": {"type": "git", "url": "git://github.com/meryn/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6.2", "mocha": "~1.9.0"}, "optionalDependencies": {}, "engines": {"node": "0.10.x", "npm": "1.2.x"}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "_id": "performance-now@0.1.0", "dist": {"shasum": "cb3b75f580bb49ee533ebf08651ce7a11d0d9afc", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-0.1.0.tgz", "integrity": "sha512-yJYyVkrFcpbZZuq7XLcqbI7Eg5gxCF5vMYOLdjXXPDoZOON2fIX3+5tyTH9BpY1blZf62nXKh4bPc/8SA7Sg0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD23gb7If0KG6Dy4UV9ZEP0u60MmibgLlwgRU2C+Zjr/gIhANkJEZzeP4zx+R1jkgkp3jNqH0wnP+CfmD70M8uiGjS1"}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "0.1.1", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/meryn/performance-now", "bugs": "https://github.com/meryn/performance-now/issues", "repository": {"type": "git", "url": "git://github.com/meryn/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6.2", "mocha": "~1.9.0"}, "optionalDependencies": {}, "engines": {"node": "0.10.x", "npm": "1.2.x"}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "_id": "performance-now@0.1.1", "dist": {"shasum": "739f6c2ae4b4d2fac0ada962b5b810dd832d1438", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-0.1.1.tgz", "integrity": "sha512-vxXgknKUiPN4IFhP66+fpymmTAHHW0Y+icAVjT/10LsvYgvMPtO8w30AKCkamlqD13dXq8hQfR+TcLY9bvGedQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0kN7utPjVz3nwwNQgRzJbhbdVP9LSLrpx0/wxNly+sAIga2qjvpW8SVwv2ya/Qmh+CMpcGIg/wH7CgVyplf6iOg8="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "0.1.2", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/meryn/performance-now", "bugs": "https://github.com/meryn/performance-now/issues", "repository": {"type": "git", "url": "git://github.com/meryn/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6.2", "mocha": "~1.9.0"}, "optionalDependencies": {}, "engines": {"node": "0.10.x", "npm": "1.2.x"}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "_id": "performance-now@0.1.2", "dist": {"shasum": "8e2e61d2ee554a817f14a229d5c6c26677cec929", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-0.1.2.tgz", "integrity": "sha512-DhPmic+MyCNzIPUnaNL0iNKsWnEmXiHoHoB2LrGFRzf6SkDlhchgEC1wbzED8+CXfF1HV2sGAE2d7pyrVtsjnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICi7Kl8w8w6hPknQTwCPPlG7luIXz6/Btlgl9JzMxMr5AiEAsozpwcDoOGQYN0zKUkiNhMBtUnv0Yi0zwn4ObXZl9x4="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "0.1.3", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/meryn/performance-now", "bugs": {"url": "https://github.com/meryn/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/meryn/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6.2", "mocha": "~1.9.0"}, "optionalDependencies": {}, "engines": {"node": "0.10.x", "npm": "1.2.x"}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "_id": "performance-now@0.1.3", "dist": {"shasum": "70ec3232e9e053a1b16c2a007f3aff2867b2dc04", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-0.1.3.tgz", "integrity": "sha512-o+w4zv+UM+eDd+QindV737cW2m/rN0kg2JBsYCdY4yXbwguerynUO+ZkH1nT/1rmWamZZD5dczvS062/PvorVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICx0zX8dRMkiT3vqEBQtGnosiJpRhG+3EnzJEYQUtU17AiA46bsjzmrxC1SNmByUBpLHgdJGCsgwktLxYzXeUU1g4w=="}]}, "_from": ".", "_npmVersion": "1.2.25", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "0.1.4", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/meryn/performance-now", "bugs": {"url": "https://github.com/meryn/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/meryn/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.6.2", "mocha": "~1.9.0"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "_id": "performance-now@0.1.4", "dist": {"shasum": "360b642f073ff8c2a693b3c38d7ccbc17b53183b", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-0.1.4.tgz", "integrity": "sha512-iaeVTVGLcGTsN9WVFTV9YZH6gYD+SurpzHdDxeqRxqkK3IgfQiexetIjlkNSUIeV9HljjwT07jv0fAxsDuGSHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGpbisGeanu8l8JTit2mZ0f8Rikjq1K3bS5w2pOteuXHAiBmY4Mg+Sbzmms2a0bkh35GW6Gcf3ITsa3180wprzAIjw=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "0.2.0", "author": {"name": "Meryn Stol", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/meryn/performance-now", "bugs": {"url": "https://github.com/meryn/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/meryn/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.7.1", "mocha": "~1.21.0"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "_id": "performance-now@0.2.0", "dist": {"shasum": "33ef30c5c77d4ea21c5a53869d91b56d8f2555e5", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-0.2.0.tgz", "integrity": "sha512-YHk5ez1hmMR5LOkb9iJkLKqoBlL7WD5M8ljC75ZfzXriuBIVNuecaXuU7e+hOwyqf24Wxhh7Vxgt7Hnw9288Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICQPNcn5snqHAbLBiYDFPYX4L6xyf3bDo7AoM4YoH+OvAiA7rwIGGbPelKL1idCu5VkA5tMUnlzTrjKAbVHJPaLZdQ=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "1.0.0", "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/braveg1rl/performance-now", "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.2", "mocha": "~3.2.0"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "gitHead": "4f0fcf5cf61fe5d1f36a60317f55ec72717175fa", "_id": "performance-now@1.0.0", "_shasum": "f5daeff439d15fa8ad36af829327f9b7211f2c64", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "6.9.2", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "dist": {"shasum": "f5daeff439d15fa8ad36af829327f9b7211f2c64", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-1.0.0.tgz", "integrity": "sha512-wp9B+QvYcxX3yCKuEkF/UXJVitJxV4YQ3rD7bryw5caewUVNHjeqbIagMTq73nBNfUYbTkbZ+4gyh4VXGzeqQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAgOWYTM0lFKiLTJ80o46RXgi/g7GvHbm1uxGxxlTJvjAiEAreEqMkIqc06iEpuhhHmwDd28o6uqw1vH4SwRcwY3ZFI="}]}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/performance-now-1.0.0.tgz_1483474765653_0.9213698275852948"}, "directories": {}}, "1.0.1": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "1.0.1", "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/braveg1rl/performance-now", "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.2", "mocha": "~3.2.0"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"prepublish": "npm test", "pretest": "make build", "test": "make test"}, "gitHead": "02b80954bb869d297e3fe733309acb90b1bbe200", "_id": "performance-now@1.0.1", "_shasum": "0b56f6129e56a6adccabb9177db0de84dd3beb60", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "6.9.2", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "dist": {"shasum": "0b56f6129e56a6adccabb9177db0de84dd3beb60", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-1.0.1.tgz", "integrity": "sha512-Rr3uw3mC0+ML9kmq6Mnicbwek2V0JEiy0R647Yh56ql+ghPFL56EFwmCWoXh8q5GihreUWsELBMRftXIO4S5pQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErzaiN8JqhdDBJBPyDqZM9uFyxynt323YZ9cH3ch1BZAiBGSjxaSijrNfHyL4Kq9fHu+PxiGvtlvFWGSjMEMZuqWQ=="}]}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/performance-now-1.0.1.tgz_1483476745376_0.8059437498450279"}, "directories": {}}, "1.0.2": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "1.0.2", "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/braveg1rl/performance-now", "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"bluebird": "^3.4.7", "call-delayed": "^1.0.0", "chai": "^3.5.0", "coffee-script": "~1.12.2", "mocha": "~3.2.0"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"build": "mkdir -p lib && rm -rf lib/* && node_modules/.bin/coffee --compile -m --output lib/ src/", "prepublish": "npm test", "pretest": "npm run build", "test": "mocha", "watch": "coffee --watch --compile --output lib/ src/"}, "gitHead": "b9669e9ed4dcc0316ebd58ce352f42b0fc81e52d", "_id": "performance-now@1.0.2", "_shasum": "e288cab9e33afe7815c06006422ba2baabcb2c92", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.3.0", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "dist": {"shasum": "e288cab9e33afe7815c06006422ba2baabcb2c92", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-1.0.2.tgz", "integrity": "sha512-QZ2ZikBKwoaJacFxMBxpZC8ZuND3y/873IA0yHyFU8zgoOd+Y5ts9Gknfuton+8zSkJ7FENBFQZ2xz0gexeDfg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRN1iHejPJLAy5/rdnehthxSljFQUXE5omx4QodijywAIgdapl7NXpJT8Hn1QOtNJVRF/OFUWMGgsNxJHBtF2bhKs="}]}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/performance-now-1.0.2.tgz_1483849955689_0.006345483940094709"}, "directories": {}}, "2.0.0": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "2.0.0", "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/braveg1rl/performance-now", "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"bluebird": "^3.4.7", "call-delayed": "^1.0.0", "chai": "^3.5.0", "coffee-script": "~1.12.2", "mocha": "~3.2.0"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"build": "mkdir -p lib && rm -rf lib/* && node_modules/.bin/coffee --compile -m --output lib/ src/", "prepublish": "npm test", "pretest": "npm run build", "test": "mocha", "watch": "coffee --watch --compile --output lib/ src/"}, "gitHead": "c4a9b72b1bb0b40fc8ad91b32bcd30325c1384a1", "_id": "performance-now@2.0.0", "_shasum": "c449ceeeff2850bd09d9112b7c0330111d164880", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "7.3.0", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "dist": {"shasum": "c449ceeeff2850bd09d9112b7c0330111d164880", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-2.0.0.tgz", "integrity": "sha512-o2INJELs/4OnLXbcKT8d5Kl42x2j+wKei+4VtwpNptHydwfkoWaMnY2m0K63T5upH054xBgfzUTifBsYQKlfoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFWp/rlMr5iCn1DBXX3sujwMAiPdCChOnKDX9MwtS1btAiEAoAH+7l0qYWZx49B3mPCi7VH0d0k9zDNhnoRSAZaC1wc="}]}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/performance-now-2.0.0.tgz_1483850893174_0.6089684108737856"}, "directories": {}}, "2.1.0": {"name": "performance-now", "description": "Implements performance.now (based on process.hrtime).", "keywords": [], "version": "2.1.0", "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/braveg1rl/performance-now", "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "private": false, "dependencies": {}, "devDependencies": {"bluebird": "^3.4.7", "call-delayed": "^1.0.0", "chai": "^3.5.0", "chai-increasing": "^1.2.0", "coffee-script": "~1.12.2", "mocha": "~3.2.0", "pre-commit": "^1.2.2"}, "optionalDependencies": {}, "main": "lib/performance-now.js", "scripts": {"build": "mkdir -p lib && rm -rf lib/* && node_modules/.bin/coffee --compile -m --output lib/ src/", "prepublish": "npm test", "pretest": "npm run build", "test": "mocha", "watch": "coffee --watch --compile --output lib/ src/"}, "typings": "src/index.d.ts", "gitHead": "107bb703494cc5a8071cdf45a87e53f248a5e0f3", "_id": "performance-now@2.1.0", "_shasum": "6309f4e0e5fa913ec1c69307ae364b4b377c9e7b", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.3.0", "_npmUser": {"name": "meryn", "email": "<EMAIL>"}, "dist": {"shasum": "6309f4e0e5fa913ec1c69307ae364b4b377c9e7b", "tarball": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJ2i2ybxM2vFBv9wXm52uK07eTrgwtT+R4j2uLQF5P6AiAlnnGanTZeeaCupdkebLKrLCXEiHj2V8Bz5r+0fV35+A=="}]}, "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/performance-now-2.1.0.tgz_1487514529361_0.3097308638971299"}, "directories": {}}}, "readme": "# performance-now [![Build Status](https://travis-ci.org/braveg1rl/performance-now.png?branch=master)](https://travis-ci.org/braveg1rl/performance-now) [![Dependency Status](https://david-dm.org/braveg1rl/performance-now.png)](https://david-dm.org/braveg1rl/performance-now)\n\nImplements a function similar to `performance.now` (based on `process.hrtime`).\n\nModern browsers have a `window.performance` object with - among others - a `now` method which gives time in milliseconds, but with sub-millisecond precision. This module offers the same function based on the Node.js native `process.hrtime` function.\n\nUsing `process.hrtime` means that the reported time will be monotonically increasing, and not subject to clock-drift.\n\nAccording to the [High Resolution Time specification](http://www.w3.org/TR/hr-time/), the number of milliseconds reported by `performance.now` should be relative to the value of `performance.timing.navigationStart`.\n\nIn the current version of the module (2.0) the reported time is relative to the time the current Node process has started (inferred from `process.uptime()`).\n\nVersion 1.0 reported a different time. The reported time was relative to the time the module was loaded (i.e. the time it was first `require`d). If you need this functionality, version 1.0 is still available on NPM.\n\n## Example usage\n\n```javascript\nvar now = require(\"performance-now\")\nvar start = now()\nvar end = now()\nconsole.log(start.toFixed(3)) // the number of milliseconds the current node process is running\nconsole.log((start-end).toFixed(3)) // ~ 0.002 on my system\n```\n\nRunning the now function two times right after each other yields a time difference of a few microseconds. Given this overhead, I think it's best to assume that the precision of intervals computed with this method is not higher than 10 microseconds, if you don't know the exact overhead on your own system.\n\n## License\n\nperformance-now is released under the [MIT License](http://opensource.org/licenses/MIT).\nCopyright (c) 2017 Braveg1rl\n", "maintainers": [{"name": "meryn", "email": "<EMAIL>"}], "time": {"modified": "2022-06-23T18:23:26.866Z", "created": "2013-05-22T13:36:44.948Z", "0.1.0": "2013-05-22T13:36:48.409Z", "0.1.1": "2013-05-22T13:39:17.729Z", "0.1.2": "2013-05-23T22:26:56.371Z", "0.1.3": "2013-08-21T21:25:25.350Z", "0.1.4": "2014-08-25T13:24:06.629Z", "0.2.0": "2014-09-17T10:25:54.525Z", "1.0.0": "2017-01-03T20:19:27.695Z", "1.0.1": "2017-01-03T20:52:27.654Z", "1.0.2": "2017-01-08T04:32:37.563Z", "2.0.0": "2017-01-08T04:48:15.350Z", "2.1.0": "2017-02-19T14:28:49.939Z"}, "author": {"name": "Braveg1rl", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/braveg1rl/performance-now.git"}, "homepage": "https://github.com/braveg1rl/performance-now", "keywords": [], "bugs": {"url": "https://github.com/braveg1rl/performance-now/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"kenjisan4u": true, "stpettersens": true, "jasonray": true, "parkerproject": true, "poosanth": true, "monsterkodi": true, "preco21": true, "menoncello": true, "rocket0191": true, "heineiuo": true, "jhillacre": true, "cr8tiv": true, "daniel-lewis-bsc-hons": true, "honka": true, "danday74": true, "philiiiiiipp": true, "ganeshkbhat": true, "mheere": true}}