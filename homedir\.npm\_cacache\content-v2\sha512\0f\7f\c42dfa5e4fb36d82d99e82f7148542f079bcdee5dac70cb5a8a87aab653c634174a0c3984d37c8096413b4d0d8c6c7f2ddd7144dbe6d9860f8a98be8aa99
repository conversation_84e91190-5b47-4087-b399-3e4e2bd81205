{"name": "media-typer", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.0": {"name": "media-typer", "version": "0.0.0", "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10"}, "dist": {"shasum": "4ee71136eb8612cc771ecfa2bf25ddc153084f5c", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.0.0.tgz", "integrity": "sha512-xThAi0oNneSuMjpVgV+Ml97ZzaBWgzyiDPv3Ol9V2OvgNAoEXXbnR+vBXvCMDJ41hUh9xzw1q7hxQS0p3WKPsg==", "signatures": [{"sig": "MEYCIQCPDPE4GNUfNUBVwxAVI5ZgCpka7a0/Q0gqLi4r9hyMtAIhAOREorwzdOKDKvYwnEcpEoE37XSqPMxcROhrxZhahHTq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.1.0": {"name": "media-typer", "version": "0.1.0", "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10"}, "dist": {"shasum": "00607eec8005776e49ea593202f5469f703e8060", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.1.0.tgz", "integrity": "sha512-rQzgJCOLIC5eiHyyKatRNkYw6DLxA/0eRiDtDIWMs2jbBy78RlrzhE4FXl4XtjrbVpjsUTIeD6xGfyDrW4gBtQ==", "signatures": [{"sig": "MEQCIBSOaursCkt4fd6ZXQ0remqSON0E/kwl+qxAARvX1/pJAiAwc3by0fvb5vs1qnvAJAjjyKeK7K8DUlUwbqKWWIa89A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.2.0": {"name": "media-typer", "version": "0.2.0", "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10"}, "dist": {"shasum": "d8a065213adfeaa2e76321a2b6dda36ff6335984", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.2.0.tgz", "integrity": "sha512-TSggxYk75oP4tae7JkT8InpcFGUP4340zg1dOWjcu9qcphaDKtXEuNUv3OD4vJ+gVTvIDK797W0uYeNm8qqsDg==", "signatures": [{"sig": "MEUCIQDhRz3yZcvoaVLNm4mBSHnXaLEL8O1SNvRRejIrrwlNUAIgAJaz1NACvTEodzCMB0acPAzLTZyNUWYKgMSj1KJ75pQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.3.0": {"name": "media-typer", "version": "0.3.0", "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2"}, "dist": {"shasum": "8710d7af0aa626f8fffa1ce00168545263255748", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "signatures": [{"sig": "MEUCIEzGAZiWrq2CnMK6wYcXnCpDHR1URZRMrKJ7AefzH7gVAiEA+nI2pPIszFxnWvefnYD8yTaK/BG5Jw77Jt5RVNxWnBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "1.0.0": {"name": "media-typer", "version": "1.0.0", "devDependencies": {"nyc": "13.1.0", "mocha": "5.2.0", "eslint": "5.7.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "ca89611f8e31b936efcb3aa90826772f135e3341", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-MKXtlq1YKUow2wpJlJ7ep1MU6jQR0BaKpkDyrE294cmNSbhoOU1yziJ4UZOU1guFxcEaFt5t4LHsL/t891D6BQ==", "signatures": [{"sig": "MEUCICaSUkB/C4F+bQXkUVi049XPD885dVMyqLEKd270vYAuAiEAoZYsIqoCjFD98VCTM/htSY/jpMOGwqay9FreEZ2XLYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJby98nCRA9TVsSAnZWagAA4d8P/A+KHvncQuvDCGeEBhDT\nNS32nytG/grOSzaMcv/L3KA9zAHBWyYa0EmBQ5Iyvd2A1jk12yKW5FsonQNO\n2Q+FMWW8fzo2pJtV6KB7FGgTJ6oWfViT86c4gjZTnJ6sZBe5z30Ai/5hSk6g\nAv749aM7qgX88RysFGRsbq38rKwmV4LIvLqYFT+knDo+R1A72rFjZLtWSe2a\nfrjdhfz3+f5S0Mj6GWdtgmn6zARFlIi6SyJrrNjoOumQXDRHQt3rgicEgBCR\nztwlnBjFXPCBj6+xK5mIqNP4XJMv1W0DRplP1WWfChTUy+8SU3PMTmE3n0gV\nvx1nBWWrFaZFRsBSp6l6qD5Am4JO/RlFV/GS09OxL93+R/gL+BnGY6uEZhcr\n5d2SDrLAzzznBz6q5KCI3wV8Fx0jNQyibw1XaJ75BL72HsCV1TWU9ZVeGCau\n32/J7ZuRsPkn+adujX6ffIi5+oeFBHkl+NuCvtEZT8nknPw/CsoPYEZ7SA3F\nX+yBPf8fPXiNIk0FrdVtMJvMT2ueaoEw3+hzzmEfwC6Lq25VYTOqdpsbyTiw\n0+Uor6Oe9sYwIZy05pc25PJdJvwHn3LaDLA8Y8PAjntDWNMd8nW2ZnXGVtLE\nia74S0Pqi55M/ARn9DiGe4H/4n77GEQyYuSWIiJheo71mY2/Y8M3OgHGLY4r\nNlif\r\n=3F90\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.0.1": {"name": "media-typer", "version": "1.0.1", "devDependencies": {"nyc": "13.1.0", "mocha": "5.2.0", "eslint": "5.7.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "e39d677e19a011c52d2681f430d1adafb299dd41", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-v42gdPIuqYCoDVH5OiaKsVrv6aJqdMWJzl8KCyDs/KeDyBveYp3Wxq4UWJfsWjkSZUNC0xlLfDlLCPa1h/oo+g==", "signatures": [{"sig": "MEYCIQCG+1J4L/K42JRYsTrcbZ5Ig+MyRMSOVr5Z+YfOnftu8gIhANK/LK3TvOGXGAPTdQs5WObiLFQLawoYzBgiw2B73YYa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJby+HDCRA9TVsSAnZWagAALRcP/jsONa6K4aEKiHKqqmU+\nNryRP9TBIgMxtSRaXP2lU+2BqeQRAPFtxa7H4SaFyyM6QLI/I3G3C1LDltXx\nKcmaWPzGHNJZ6aFVqiNgn7nia+/vGiciP/ZeEg1AKy6vFbkKVMuk3AOQVYO0\nMhByAAVLTKBRqMtuMqv2AH7tHQLSMNJCvHQMelQAOruHz8wbE9Pg189uWaud\npm0sD0eqVw6L1VD7A8XtA0TcyidcnytIIgq/uSr6sES6sFIBTtCafbFaz/X3\nrdm4Fiv1jynEN5Q5PWO9sTCIBeJ3gdlTm43EkXS98ggR6pPqYTujTdI15SCJ\nnM1qaMUR3k+2BAZttjD1v+gvtI5U3nl/jgdTWCt0oQlQ3GgO8TjO43RHKYmR\nP4LylGuve7oih6Sf6/MDOi/QPReLiQW4FBdAxCB8jkKjiVV5YEZ4XmlaO2sd\ny/YMcoxBrROebWcjmlFXROfAs2ianwxTD7lhe7YnjghGIf2m+BxFWJF0xLga\n+5op2MR/GCbf93BiV9RguUnfyaRohgmo3acIc4Fm/+M52nMrMS3dyiOxvCRD\n+cjiLJxHUsf192Y9vgbXMxlBqUzr6lGzSQAPpDScImHZzxC6AU3LVNOVCCDU\noMd/a96idMBHmCUC0dXn4WhYGDTjNRAcaj8PTE8vENp66fEbp4/wWFfWVM4h\nuvVC\r\n=0v4D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.0.2": {"name": "media-typer", "version": "1.0.2", "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "e64b0a709d52c158ddedaf38ebebfa00d8e02c14", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-/ky7iFD18Y2mN5BdOS4zotSSgu11BsIR2l3L7eK2bTaRWQidoSBmSxGgMFd/XOSGyivlhtQUdDLoUzlr1PWb1g==", "signatures": [{"sig": "MEYCIQDQxQHKR5v8iKrSSGbb7BWn7JgmERGaWfaExkhnAtoDugIhANpq8hmEacUuHeHRgo+WgmC/xKYRSS86iqEEFvuJKRKf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcukLZCRA9TVsSAnZWagAADM8P/RDVhBWP4o9R/32p6fdI\nVr8PGyNgx0K6s0A6KVOis0x2YwXRHCZKYIP/DXjsstTd4qQ/sT2QQVDTkS8s\ndeTYEQ2W/nvjNymWxPtbkfgZ4fdGFa+QFNoZhQCjg3fbrze2Ym1MPehlAi9G\nw9Li0tIxKQgy9LcXyBC/lPiQdm8Ip5wOvihfVOd4Jm7dyT4NgKem41oKNF8L\nLQIbguAHGE9ET/s8nvLe6WI9IK3CUD7BN4fTLs6ypOJKm3leLDCmzEkwd1Yq\ndvI4WKg/sZ1jYi436OJBaYfzP/kJZDYWT4Hghqh1p4y/WQF3GumRhoPRVSfR\nmBARnlBniHYPoCchPXmSLr4NbKO8ceITFeelxgi42AJzAY4UQU1QP2jjP8LS\nCBo6jmMiePIPLuIN/oOKejtIX8EDsWZelAXDH1jE038PqXp22U2j0KmqPc0m\nmkXBLDOAECvnsqvMqawEJzjJi639EBuRf3tFBnQZGqLoNSDPEtJNJxxMy6zA\nJjjR5zsrlvfX+07NeJHjNB4vutas8sOb/T3aZxc/7VWFgJoDApP+vsoxuQy9\nlvhLPoc+Ew/uXqOt3GwZM27g1caqdVmJolrTzpjKk7GLUR9GoedWcsTCTJZy\nimhjRmbJT24q23z99eeJO1GaraRtxOe3KaLwYpjhTFC1aKJcuH3Yp5CDF3Sp\nYhBE\r\n=e9ga\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.1.0": {"name": "media-typer", "version": "1.1.0", "devDependencies": {"nyc": "14.0.0", "mocha": "6.1.4", "eslint": "5.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "6ab74b8f2d3320f2064b2a87a38e7931ff3a5561", "tarball": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "signatures": [{"sig": "MEUCIQC9UI2Z8NvAhUvnplJNMlz8uTaMjzgungJS7hhHBFNc6QIgFQaPxCjC5otSzLU0zp6KOyOdQ82VoCi0sttAS568jQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwSZ1CRA9TVsSAnZWagAAq2YP/iVhvTj/MN3o7Aq8zi5d\ncqBXtKn6MQPCQFT6MlyUSEyXZkR76fZJtpqJLuqMtaICoUcFbt2eAJDqLDY2\nFB0E/q9Fcz/3sz5Smi6q3TBeJhWiMn9VhxkE2WzLBXDl6o9LD0iWAsDgaVse\nSTKTP2wO0xxwUxJKNGWDr9KLCweldS0/5SNMUgGkk7nYfQRaBRiW9t3QI/SW\nYqEllnjxH1r4UZOGSGfX07W5oNhPmBzutKkky+VBQXo51U46qcRV3f6NAObA\nshoOzlCCreCkF3mZuLzG8gfwFULM5b6Xud3/m6Hr1JxH7kVjaa/w2BhUhj3N\nsbgHcBMPHxOqzv1TdA8LAv0J/+sF5brRTLZW8Tp5WKoPvJDp6nG46rmrPm1H\nL7xzDwXKUnL3bZ+KtrSwgXY0viF6uMQlejAjkpXVQki5pc1pOEpnnQ+nfF22\n4myltUfRq5TGBmqqAZV0+Q+vIHf3lMBi/PMVm1ZI1QspY50B08fAr1iH81bI\nCredpoZbf5Avd2eNZnDFQUbzXVh7FW3Q4+q5667AlAvb9b3rZ3wEFgSDW2bS\nnUm/VQFkvRXQl2gM0lQJWd8uOzRFInCH4S4TsPQezBBILBQtmH44qnw/Kqn2\nsDw6GU8MifAKlOfPTl7+WPlALvhmkjrZNK0HukXlowlhisxR1UMLJRGIaWDO\nzTFe\r\n=OUQb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}}, "modified": "2025-05-14T14:55:59.100Z"}