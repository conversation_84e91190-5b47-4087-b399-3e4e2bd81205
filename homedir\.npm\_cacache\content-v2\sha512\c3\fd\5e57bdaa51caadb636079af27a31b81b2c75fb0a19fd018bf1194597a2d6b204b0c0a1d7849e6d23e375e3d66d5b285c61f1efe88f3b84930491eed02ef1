{"name": "@jridgewell/source-map", "dist-tags": {"latest": "0.3.6"}, "versions": {"0.3.0": {"name": "@jridgewell/source-map", "version": "0.3.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-TZsD/uQOd/XZi0cA8iSgxh8WGFnM16kLBry34pHnM3rvD4FRwK2qHWevddtBLoub7Vro1RS64XXOQLXIMqmE4w==", "shasum": "3970ddc111bd9fcd91731df4fcdd35353b91a590", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.0.tgz", "fileCount": 8, "unpackedSize": 229006, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC4wVxxKnkSc0nmAS5zgM1wlucRK7g/yNgBCurRCwH7rAiABK9GMcy1Oj97jSRQbBozm9Z4w0RVzaRWhHBY4GO0amw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibu15ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqLg//WYaWkqXBFUrYuPpPd+43EBlddkty7aaB7tr34K5BQHYCsSbA\r\nSpJbYqi2b3Jgq90fSZRvtwfAtdIRw99FbPgGD4g58BF9M6CCPuIrg064ta3K\r\n8CuY9/jwz7r6HvHIXqy5NGYaUyfBafTEXuOWz59mJXvafMMkvSTCNSLFYa1h\r\nIguV8bjBwqdl0RATwhYGEWQc3g6OvW2fLhLUZuXEwsSZUpBo0y8fX8wcrvkt\r\nGjBXJ9u4jK59kyc2pCr4dEk3SRCMraGrKUy1JcWfSpCFkndUrlwbdgPSjbDc\r\nRoQF27nb91r3ihVp5mQYPtEVvkyvhIusHDNDDNA9dgi8cIqubemNwcL0MZ8I\r\nRQYbgeAW81C0Of5Xgu/7uTq5tCrO9DF1BrxvhfAVkS8Lp1a2rzygYpYt5IR0\r\nNZ0X55JVl4YZ2v3ocvJm2Yo4/DajgMg5N25HrATS231fBdBoUhRaZe7WZGHM\r\nWnQGq0MtiNjaFCSVzyRFAtUh4XfbFNJJpN5Z45uI94q5PvBYD/f6N7exLdqP\r\nhIjCpquEccXO+/Sc9H2+Z7bdDYwaiRAThniJoPbLgsHjia6xZswoda2h6Ot3\r\nAlQEDRoRt9RnYMT1dE8QCZFqEmPsQyYbLWChpZBxDBQXK+S1NxLjRqWpA+fQ\r\nRwxXhxFHdbomXjKWQFy975q6tjlEAWGasMk=\r\n=soFH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "@jridgewell/source-map", "version": "0.3.1", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-gqt+z2d0kIiUtI4boNGTINaBNDljT95R6MBiUoBRA62b+SfZg/MSFeblZBwul9PnzBB4Fks2aVq5l9WpM9WCAA==", "shasum": "a7a54e1f9adc78ba3029d1500185fbcb1e213bd2", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.1.tgz", "fileCount": 8, "unpackedSize": 231010, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGFJZr2TIQ8Ce+9N4F/17hz2PYYhrLrT6oXF4kkW8xUAIgSnctrir+IUB3hmR7lAEeDImS4Z2ZlyQU+xRfBunYNbI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibvKBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSWRAAldvMoGPmj1kIP5TSednGj1+KIzXRoHiHHcO0EHKGA2e2bm4m\r\nq96yPXe3Jq8ezTZWZsGVya9qKYD4g+md5K4DVIIdyiDxpwDKh7ruXgmoiBdk\r\nob6KYPDLf/BtqwxDLgAx6BPM8Zcbpdgnk8ZfJmEEzkteRA7fR+OLUEizo62w\r\nnUrs/PI+nCAXZl7iql05ipuEf+AyLrj20GRf67w6dadiVJIEh8PVBHrECkoz\r\nF7RzmSUzqFnYQ9s05lhq2Z3clBnN8KDnFoOZt+pdG/+OQwWVpMtEoR8VGbqm\r\nTv0cT0UXsiwco3Z7CnnbXH6zZ/LbLu5U9D3ar9UCYxE/e7sdehGAmfKjHYAS\r\nzhDF40XmHaXZTJSq3b919uT1p4jAtQwHoX0a7e7F2kX4RH2hsLaRaAelwD04\r\niO5RwXC2eJ3YFTo3KrlY2ryKcaEuCa8WvAPo+S95chCnhvQ5JAE/WfZ4ziaI\r\nTc2R6MCw/jprE7EC1BtAv4ppWY3J41rPPZn68GrqVfW4gmxrMKcsouB6o+f2\r\nqpkKv8atwzJ/o3OMWj6by//LfJwpEPIodSNcJpZ9riukOHDmQKZ21e/CxM1B\r\nB+Cv/M9DpvggPXNDk5L6KRGgkrae85mFaZrB+OtOpEKNxcbZiRDzCKvbbiRX\r\nzS4r90xVzmQh6HlNcM38xw5k8TqoIlSEwbg=\r\n=2+ys\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "@jridgewell/source-map", "version": "0.3.2", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-m7O9o2uR8k2ObDysZYzdfhb08VuEml5oWGiosa1VdaPZ/A6QyPkAJuwN0Q1lhULOf6B7MtQmHENS743hWtCrgw==", "shasum": "f45351aaed4527a298512ec72f81040c998580fb", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.2.tgz", "fileCount": 8, "unpackedSize": 231477, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSXlb59xbPQiHVolPAoEfp+wj6NFgffWFn+tZQhhBs5wIgGJatu1bQxYHSbpW5WjQjWmloPDpBzwCJZ4RxJ/ggmzk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibvXEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn/A/6AiulmrxB4S8a/VYOrTpz4yglx3f2lr68XV2rnOLf6EMo7658\r\ns487vue+2k9okglvPeGYCTVH+s3++yUKeD3AoHlV2vGLZywoHTrRWvxmNPkU\r\n3JGYrB1Gh5jDK8sKXGPOYBck7BBbfdZCevAVMi2Sp1MpRjGsIRbiFo+5gmx6\r\nRbFXfja2zILGuFowJ+tVMYXIO5Sxdvfoo8jV+5PMDKQp3SRvSDLBYLCzitU/\r\n3oNOvbbmLLE+FNCchNnLjE8esQ/vw8WwukccL42CkFEhdAuJpPNzthmIEgZO\r\nT4P7jy5kLrhxHXMRSx9HNUZUQqGwRVQJcbOQ0QGHmHUR8HWlV0AYjr+gf29L\r\nLehhpbq2PG7x3WIi3NL/mCQqRhGkWRbzisv/IXFaknXsCe0Da1sz2TgKWU7l\r\n8tlo7+wZMVg804WGna80smITkn6Da/S0XUi+KhfuT8QAwDTwDridTyxhSamr\r\nMD416TxuMSEhl5lhsN9BGqspWd0Lfu42VUgCFKwO7JABsxmd13xF4oUFCAQb\r\nN13F/xfltgAMZTCEVDzGe5z1UKDY+kH1obkczif6js7cqB3CM9hVlzLnv6Z1\r\n5Lq5MKbKOYJeWzT+D+Mu2LdJZ3HOJBDceS1wUbICy8kfwzKXv88vPDbKHDL4\r\nrYKtgMXfGqFDIUDp1MjCKLIAwUSXLUXX93o=\r\n=i8We\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "@jridgewell/source-map", "version": "0.3.3", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-b+fsZXeLYi9fEULmfBrhxn4IrPlINf8fiNarzTof004v3lFdntdwa9PF7vFJqm3mg7s+ScJMxXaE3Acp1irZcg==", "shasum": "8108265659d4c33e72ffe14e33d6cc5eb59f2fda", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.3.tgz", "fileCount": 8, "unpackedSize": 231580, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBsfbf+oKpafJBmfkrT1zhSu7NWi/3ATbDjNUNtcVcJHAiApUjtg+rY64fNICcLhcHm7xrUv7YHWcvXIUMPk3aRkJg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4M4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraqQ/+P1VlS46FS9oVZuoybfEp/lko/8wtte1R6a0G5mEwW+a0ahIk\r\n5bZauQnP0Hh5CHqp59E0B993Xc30UTP7rMJVg4WDlQt6eeI+9u0X+JtW2iXu\r\njFgOelRvlg+aX3ydKStuVyfzOxRGfEOUew8o3uqrxexbyc2ksG62xvv2bIil\r\nWD82GWCaW2abXQah6j9MPPKKNGcIXkWa/m7ZXMWt3Yh7OCrhU+lNPT9LCOls\r\nh6qDHoM7mQQ5BwvprLN7wDZIH8C8Ft7jwO2oXcXSGMXvAUYBaIrwtv4xsXSA\r\n1M7I41IgndZZxCSfuKuwCuP99p0fdOpyy1F5L2cxPg0iVWOWbchy4RyENEf5\r\nacq5SXKIAUWNnSRL6SKoy1c73/M3/jSfpaKgSIRXXw9K9utvzB51uCqO9+dg\r\nAptO3KqM6PRHRrb2uI6YL33PvsOM28iKiXtKDUjs4uN+I2XGFewEpqL8nbF6\r\nR2IZrwdXerzEFArv3iTb+BN4SHdHD61NCCEsgFZaX+F/qRd1sVCSMUthVvQf\r\n18GJdhKsJec4md3O4g4k9wYu2mdDNYKFvxjjpKEwO18fUCilnvRYdO6MDHPa\r\nYBIDZCvu8WRwzmmMVTXp3XQIidnbIXsJqKOfr2VL7apX10pJUVSF0NHECRor\r\nZ5Rsj2fQKJ+LQiHwgROr/GlPVk+5O9O2COI=\r\n=rHBQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.4": {"name": "@jridgewell/source-map", "version": "0.3.4", "devDependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9", "@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-KE/SxsDqNs3rrWwFHcRh15ZLVFrI0YoZtgAdIyIq9k5hUNmiWRXXThPomIxHuL20sLdgzbDFyvkUMna14bvtrw==", "shasum": "856a142864530d4059dda415659b48d37db2d556", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.4.tgz", "fileCount": 8, "unpackedSize": 231556, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPMHrs9OU7LDJeZs3bD6l1JHV1LRlQAoyXWcE2iUH7pAiEAqaDtFeSXOhJGa66RBTN7RpSkMs+W4+mzjBlHj7GpRs8="}]}, "deprecated": "the version has a bug with typescript type resolution"}, "0.3.5": {"name": "@jridgewell/source-map", "version": "0.3.5", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==", "shasum": "a3bb4d5c6825aab0d281268f47f6ad5853431e91", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.5.tgz", "fileCount": 8, "unpackedSize": 231580, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2hfSKdAbIOjCA+2a+6mYElmueDH6nRdT/G68ogun5nwIgQMAMxflOyh0eSjjbd4abeG4yUmnJD8EGttXnWAeyOlc="}]}}, "0.3.6": {"name": "@jridgewell/source-map", "version": "0.3.6", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.2.1", "@rollup/plugin-typescript": "8.3.0", "@types/mocha": "9.1.1", "@types/node": "17.0.30", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "10.0.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "ts-mocha": "10.0.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==", "shasum": "9d71ca886e32502eb9362c9a74a46787c36df81a", "tarball": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz", "fileCount": 10, "unpackedSize": 177363, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCt+Bm/zwQHZtcTnk7/z4vn8thGttNmDW47I3ejhptleAIgJxyMQ5XNqnUQW949WHTdOOkCNLzTt66rULQtatUBLOc="}]}}}, "modified": "2024-03-12T03:24:11.937Z"}