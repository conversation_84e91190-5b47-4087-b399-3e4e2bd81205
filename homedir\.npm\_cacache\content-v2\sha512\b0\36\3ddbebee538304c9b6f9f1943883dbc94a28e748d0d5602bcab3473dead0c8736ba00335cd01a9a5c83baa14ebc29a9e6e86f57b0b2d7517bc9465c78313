{"name": "enhanced-resolve", "dist-tags": {"beta": "5.0.0-beta.12", "enhanced-resolve-4": "4.5.0", "latest": "5.18.1"}, "versions": {"0.2.0": {"name": "enhanced-resolve", "version": "0.2.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "dbc461a2bbc5f42d76331e83307504f88565822a", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.0.tgz", "integrity": "sha512-DtEXwNWKIBkla93hE5UWO3sLAexA0NAl9oPC10Fq1K/HG+YJ2epmAKyzI7Nwlnlw7Vnhvlld3ltmKhp2ZWRoRg==", "signatures": [{"sig": "MEUCIAbjI0OubKS/WMRpqllWIXTO2PN/T2boMRsTk/H6qLUNAiEAuPghze3uKU9QaoKCVnVN3Bg+JkPC3+pGE/Z8enjftoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.2.1": {"name": "enhanced-resolve", "version": "0.2.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "c9c6765fac3255b2f767967d0d2f248fd3117a55", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.1.tgz", "integrity": "sha512-LtKxP9RhLarQs2EiAhvbjIce+WrJhhEd229jKFOZd7zEYFft44ovlRRa5sAmd3sYpagAe/8d68w/wk0Gr/+yYw==", "signatures": [{"sig": "MEYCIQD/fFrvFpJl6yoLOlR2aLodyr7XPCljQ+w4775+bc1jsQIhANE95fYHwMviylB9pkQCPIXwPKiRHXkYjhxNeqUHApHZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.2.2": {"name": "enhanced-resolve", "version": "0.2.2", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "23f6cdbb04c0e8b255e4615c0bdf9d117815a1ca", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.2.tgz", "integrity": "sha512-KF18DfLr0ELgKB8Jf2vcfX4PEn6vp9gKFemTDQBEvUeTyPpkLAxNQuMZm7MNLqgMz8/yHznUNqyc/DxUsXFPbA==", "signatures": [{"sig": "MEYCIQCeAaCkOSa/6PuLsdXfnosQi7mPGE384t9Jo1BlgNmZhgIhALJp6W+E1tHbr0ZRl/dXZIkKRegvI8JssjDhFkj34GKB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.3": {"name": "enhanced-resolve", "version": "0.2.3", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "cacc8455e1c41d8920b261480ee10ecd6ff2298b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.3.tgz", "integrity": "sha512-w7yWKCzpIodRt5iQ5mM8o+eaXnC/70E7fZ73kFZSamw2SWWXNZlC0Np1ycYmiCaPpaAyRdwjcSQMqhOYcT12kw==", "signatures": [{"sig": "MEUCIETflt8FjcLRxqzehMIeTJDjWHOY66tIY27XJe6r16cOAiEAiHrtEiZOcQ10Hk3R6pG7rDMHLcXcGg5kmKxNgYtHHrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.4": {"name": "enhanced-resolve", "version": "0.2.4", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "6f5b8f4066021255f99b25be9cd34b0529ae4b94", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.4.tgz", "integrity": "sha512-W/1L+s2NxdEbTFOW2ZsEFIKBhuPLyhUvYIbF+N1TFdeXsMlacA/L4QSLHt3jNsax/p63fOkM/kHEinQLD+0mWg==", "signatures": [{"sig": "MEUCIQCHBUWC/eKOEaVCX8gYekkurGX2Z0lqKiqQGzpX6mUdYgIgC4J02+8ou7BlVQk8P6G4SfqulrL9uohV9XId3LkVb8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.5": {"name": "enhanced-resolve", "version": "0.2.5", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "a2cc54e58cb0ff1b7981f88ee9b4fa58326e71c8", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.5.tgz", "integrity": "sha512-415G0nhVHPttOi4rw7y8CtTfc+jbloraVTZ0u5IKtUZiYmE5v5i5ltgi+7EAHYKS3XZs7Du3i8zAQLA4FU3L7w==", "signatures": [{"sig": "MEQCIDSWsLN9jc0LwrdwylLlC9Z49spN8xvi9PbTLQRw2r+rAiArjbb6/CtIsF8K99rw0omCunzOGBRaJfZxUnEZNrCqRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.6": {"name": "enhanced-resolve", "version": "0.2.6", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "22a7fe92ee77c377ea1859aeb739c3f90652e19b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.6.tgz", "integrity": "sha512-TJUOFtKYnZ1uTxvt5IWfJafur0AMC0Fe/NprPfbYYhi8csrFnVJWNuljBIswqL6BsK6GAicYhHzmkv2i+NgXjQ==", "signatures": [{"sig": "MEYCIQCdzBWbdgfF70L1wnOH0EkaEjujSAmZzbEHhrz7d3FVZgIhANb1hF9oonIBRWXoKFleKCmtu0GFcs+BoAL9SWTEMV+2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.7": {"name": "enhanced-resolve", "version": "0.2.7", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "31fbb95ecd774ecf2ac40074155b99fbe6774630", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.7.tgz", "integrity": "sha512-YML9a6Munz0cH5LPYtM/UdrG9waB1nYZgLq1ITtwaDiVF3DlxXa1OvtqnEwqrhTf8s9UbqcsbVXAkQwNS1nytQ==", "signatures": [{"sig": "MEQCIBWGC5pjW1JvMMVLkFT1k1hw+T4YRo6lh29ALpA8clhzAiA9B7nhdPb3aOohQHYkGSkQHaagUh/Szm7TD3xKPuju+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.8": {"name": "enhanced-resolve", "version": "0.2.8", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "0fe4018ce7b4a9cdd7a6b5e78c941dc62791f64b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.8.tgz", "integrity": "sha512-IqG6kXQjdYJ3m+8Sc5VhOSXNrMyzFnxBwfgV646Qgx0IJskXK8x1QnvESu3keMRW8yF3YGizYDNJ3crWm4KMJw==", "signatures": [{"sig": "MEUCIAbilqjlHAaO0xJh/XJgg/FbLp7VayWp/efoJo1gRFPYAiEAzcLotwEw9IJuJ3hGtjCtCJgtNYTc3vQrcrerZojEfZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.2.9": {"name": "enhanced-resolve", "version": "0.2.9", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "23d0659f79963877932e265a415ea60d2f8e8e35", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.2.9.tgz", "integrity": "sha512-I3nwsjCsWJtlS1JrmDvmKU9dNwjOgUYKZXYhYOgtB/tNTG6TRRy6vFI9wNE9qNiQEFBtPPmTmAAykgvzSBJxyQ==", "signatures": [{"sig": "MEUCIQC8Tl9H30izzSysHk+o93K3x6mSzMPUEOWkrkTutF4nUQIgdeqzdLlNr84czWjt+Ztsl8fRIeCHKJquxxOIqUSS6Hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.3.0": {"name": "enhanced-resolve", "version": "0.3.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "395d3fc2fd43fd0565b160de718573f49533ee03", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.3.0.tgz", "integrity": "sha512-yHZBx0prekkWEQUemqgnw1ymD/4CZdhsgFOYSsNu5o3acYY86BQ1GsThuRLXDqcSbRIqXYNeEooJ4WQjrL4xBA==", "signatures": [{"sig": "MEYCIQDaIzxdPpk8nB+sWPMGUbqwtGmuF1rxST+GJbLx+lOE9QIhALfTtOWbLkW5I4JQKNPo89/tu2PKXRsWJGv+afqFBx0m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.3.1": {"name": "enhanced-resolve", "version": "0.3.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "56a15de2b7b05079c4067aae1a9987c5635b96b1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.3.1.tgz", "integrity": "sha512-hDT+09S0nUjvL6y02lO6MfhVoKtJq5IOboj5h1FBUAJ/Ui4W3UPz9XmzKKyZVY+BRfD10lULG+i6WUpS6kx94w==", "signatures": [{"sig": "MEQCIF0DHSCfm9tplj93ge5mDZ6gvE0TBJS1nR8iPhLiRbhFAiBFJI/Nxliav6wnS8BQ88mj81FoDjrzkiuZfdgm80fQLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.0": {"name": "enhanced-resolve", "version": "0.4.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "60c4300876bca576d2f62b6d96a9e28b67b95be5", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.0.tgz", "integrity": "sha512-zrfTaFVwiPHXhgw5rkkaWIHglylAqAFpxkD3XKltvixq6VS/UyQayW4E9RfT6CuBV4W2a3Lumk0I1oIGH+Y6tg==", "signatures": [{"sig": "MEYCIQDu6Nr/hzR4DfBUbJRMw1/4krQ2HsUXzYEJqNVFZK8N1AIhANs54EoemitYJ/C5xpvOryPF0WtTJhmkqTo+zFiaRpiE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.1": {"name": "enhanced-resolve", "version": "0.4.1", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "c92f3572fd6c154fc70f1c18eef3c76fd0b4b6a1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.1.tgz", "integrity": "sha512-umxHhHuP99R9i8PLClPH+yzi8QelnFZ5sCUl436WxaypQA9ZDCWdpw3xeeCHMxAurts17xJGhczxj3GXH5RzyQ==", "signatures": [{"sig": "MEQCIAJXmBAxCzP0wGTlmYdAnkBpy8Duw8PmMIUA5RNJ8v3tAiA7a2Ry9A7YRBjidJYWAT6FXUzGd2vd8NTaFEBhORz+sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.2": {"name": "enhanced-resolve", "version": "0.4.2", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "dc84ec4c6561428140b15bcaa78203d93589175b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.2.tgz", "integrity": "sha512-q9ieBQDkAF93xrA+Tu+eCmGkFwNkku2P+g3a7v5e3wvph7NvtQJfc6KdDPr5UdTqpqEIAGAt7pbBivwj1GSFNQ==", "signatures": [{"sig": "MEYCIQCnPxCdNNs+FprRO99y4d/OCqwvqITSIkeghta9BL7D0gIhAL66zmxVL5o9Ivzf8cogdZbm65zQ9ZkqhT26G92i/HHe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.4": {"name": "enhanced-resolve", "version": "0.4.4", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "5437898d3f9f8db366d3993ae1520e0f958d8902", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.4.tgz", "integrity": "sha512-BHXrqX9tRO2xtol8XMk1hzZMjKD25yvXqFD0XWHcMAjYKurR9lcIBp7UrAfeIikqUU4POg+FNSM+Nmzah+b6ig==", "signatures": [{"sig": "MEUCIQDdcNou1XComRLITpHA0KBW1OU7kDpC6Fh4dBYge7ZIsgIgay5cZ+SEaFbk2/U0DTfwB9Svk+Rh+uMrJAzkT3zsFRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.5": {"name": "enhanced-resolve", "version": "0.4.5", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "5d38d78e6aa36e371cfefc17eec2c4c57064b6cf", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.5.tgz", "integrity": "sha512-cVBedLVi9CwxEfc71ZcNbKnpsp7yMuvPC7NEltFACosz5DobFh5pSYuVmqmDNS5Rs7qk9YqHcA4xqx+p5UFGBA==", "signatures": [{"sig": "MEUCIQCSBj5PNxKV5Cl8tGk0wnJw91M/zSpr59qHBjL1sKbcmgIgU54CwEW8UXACz0eOOOl5wgySUWMmS89+oo4Txjz1aAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.6": {"name": "enhanced-resolve", "version": "0.4.6", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "9930f3329613bdbfeaca678d60f0eacea86b40ed", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.6.tgz", "integrity": "sha512-tBFOhw332beQdNibXE/84TwVRJ38KAks6WES1Ax3J4HTgWb0VS/dAMk+2SB/9Z181PPTY2nyV7uFbs+qpA98mg==", "signatures": [{"sig": "MEQCIDzsRr1YtSyuEgwm/PCanNdf5HnpYjcKaYpjjdt2JRDrAiBPoeZ+huWA13LhCiprjgdLJ0/AC9gu9pEhdjhhCCz1rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.7": {"name": "enhanced-resolve", "version": "0.4.7", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "3b9c5ca73bb2635116e80c06e0a32bcd9ab0892e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.7.tgz", "integrity": "sha512-rKC5s6jodg5YqWzIFKlBK6JvHovuvwauXIDJY4Qh9030KAYznJHt8PGdKiffLTeQjXT3THAPJ++W5zZrb37CtA==", "signatures": [{"sig": "MEYCIQCBqGEjP5xAv0xpd341Hvdl9kv1lVJ86qHSkt7AfD6fngIhAIu6iulM0E4lYo0zxB5T4BL/D8i7Dp9NU38K7Ov837N3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.8": {"name": "enhanced-resolve", "version": "0.4.8", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "9f84891004d770f9a88523aea6c87f8d9e3bf164", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.8.tgz", "integrity": "sha512-uSLkzq/YYbmy71s6QTFyvXq84ONdunkAt2RzA9NTi+9rf7JUDnl5avp1dd/jYtrtsviBCj/bwFRgGUTgLaPiIQ==", "signatures": [{"sig": "MEYCIQCNsqL32HXe9vrsanFkxGObYyaWbYcMoACGFg1MjNKx6gIhAPkHlvmlVfdqUTir3Dfso267R5QJF2PONabIspURC4H8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.9": {"name": "enhanced-resolve", "version": "0.4.9", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "45c21996d049f5e7252bb0a453adce93d7cfcb36", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.9.tgz", "integrity": "sha512-QlQzOGbmf+sKX92/e1gq7zVR1QBuukKkhpSoB1+PwnIkieyMuhher6vscqTbqV4EvKyG7VlOhNKMUL5AVJbPkg==", "signatures": [{"sig": "MEUCIGdE2ekeSXAQtpRBMMatyU0Rg10cRuzCeGjMDskgXLWkAiEAy26f7X8az/Xc/mYScEjZQJAAgQLub9g+Cv5jsB4yMHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.10": {"name": "enhanced-resolve", "version": "0.4.10", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "7d3eca0e4c3832350e0ee7f864414a9f32a8a083", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.10.tgz", "integrity": "sha512-0n4W05RvIgqsjZdyogkyl3XSI6uMpnempTl/J98l0PyyRbSfYXn+aFtGHDZ9EYSWBHWwDlswod6B+ses3l1cnw==", "signatures": [{"sig": "MEUCIA3OeizLNyLAGkdKqQ00nTmSlYign3+pKLOfiw6Y+PMDAiEAtUzLH93kUdqj6DFb58ya9qdt5Tb1eonbPREc5ot88nQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.11": {"name": "enhanced-resolve", "version": "0.4.11", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "258094db1dc354754238f9029bde702e5b4ac329", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.11.tgz", "integrity": "sha512-a7JZKB2wC+v01b9krTFdU99WU/fX0AesEUaym7es/W4h0yYHcLSSDMPkk8hIOXZhNhoy885E9wTdpUmJbnECmQ==", "signatures": [{"sig": "MEUCIBwz0mKl+7Kx8mOqcOd9gdnYweF0ny4kmvZtkFvHXv0DAiEA+iA0oZgTWSaj5+uEUEI4baXfAsf3d8X243jE3FsS8MM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.4.12": {"name": "enhanced-resolve", "version": "0.4.12", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "8d6747bf3a6dbbc595457cb21f9b99633d15c5a9", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.4.12.tgz", "integrity": "sha512-6u9cjETGfcl9Y6hXpGBNZ3K3Qxs6zWagukICU7brPoPRn8MFQ31IZccoC8NbOfBP+ab1r44BDriFp6JpoPoMKg==", "signatures": [{"sig": "MEUCIC2JoD4eQZNCVbXySLfmUwigkOnM8DEXPrQNx0U8ieYKAiEAm1Wo7mH3ivX2DYTt8S75NsZ639RrkUHiuAkbi0GIC+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.0": {"name": "enhanced-resolve", "version": "0.5.0", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}, "dist": {"shasum": "1f70c2d2fe50a410464b778afea27578475ee383", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.0.tgz", "integrity": "sha512-uj/SzvFwGX+WAnlwlwA2LyzoENfdqaPB9a4M8OL2DKM9TUP+Es9YgWg7reeXYVHUNPKNZqnZ+ObytFgf7rwfsg==", "signatures": [{"sig": "MEQCIGu6Zh26mSoQmNK/VKk5kapuY6S+MUGBWQh9lfEHDl6JAiArv0R+xzULl+CKutLf7i+DSHXWZcasuYWI3TO/BvfiZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.1": {"name": "enhanced-resolve", "version": "0.5.1", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}, "dist": {"shasum": "ffff62dad092d537693b57de333fa9f546f2d89f", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.1.tgz", "integrity": "sha512-56s9TmTEOSJaCm2htOrJNanhdQxSir6uMHYGHQ1FE/XytZnjjL6TnkZGu5Cv+UDnu5EnGEYhs+akfd1A1SU7dA==", "signatures": [{"sig": "MEYCIQDgARbQ0xkgUpdlfvXEvtOlZVri+Sxz8Ix5PkDPwIdUSwIhAK9qXuiWR6f8fpFlqg1vRiBsLnVWRvpvbssfJ/4tlu8A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.2": {"name": "enhanced-resolve", "version": "0.5.2", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}, "dist": {"shasum": "5aaa12b74bce55602a62d11bc46d7779333d9d08", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.2.tgz", "integrity": "sha512-n59956mt+8o9zdud0ZHw4SxUuYq4IqlCmsiamxoKENtatJqeracMYS5ml3wn3/JeyE7+YpeAW0+5pdzWpGjrqA==", "signatures": [{"sig": "MEUCIDFFYErg5qrJrn4YYFs/0zBKgKWXaaiP+P8d0IUozIOWAiEAlE7EXCmYXRSQVeHTMb+hnsK/9cxda3Vo1qf7KUD4tQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.3": {"name": "enhanced-resolve", "version": "0.5.3", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}, "dist": {"shasum": "aee50a789809d9e6ee2dd48264f521148405e4cb", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.3.tgz", "integrity": "sha512-8Lv0UkF8Q4fri1gqAmmD3sQbiCHsJlj2Ens6OiaFPfjGbY8YR84wgcoPgLR0svaSblR+rxynxk3emBChCUy7PQ==", "signatures": [{"sig": "MEUCIQCYDYzkGlOkvW/kCL+j5ZUu5dz3Fe7QTMSsa3942zzVDQIgaCHR4IdX91vrMjbajFMs3ylmWkZLntqtiOsNk1G5HPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.4": {"name": "enhanced-resolve", "version": "0.5.4", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}, "dist": {"shasum": "a34226713f44474bc3b9a9cccfe1f6358e3ef088", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.4.tgz", "integrity": "sha512-hd9fDUKugDSnks2q6+IUhTolLiMbgwDDJ/WjGyCFA14XWFxc+U/IIlw1DCqmdzMGqgu4utuYb56LwU5v4aET9g==", "signatures": [{"sig": "MEUCIF9yYJqxvtUZMaTAlxsn01gyyMKZE4o0/Q/9O61qPPnjAiEAovJneMIPzyeNVdGhOVQnLmKCW52ZtsRkK7+RmLCq51c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.5": {"name": "enhanced-resolve", "version": "0.5.5", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.3.x", "should": "1.1.x"}, "dist": {"shasum": "0f54fdffa4927bf6300973961c34d8acd7649099", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.5.tgz", "integrity": "sha512-XIl2QzLO8hdBZF07dPK8VibzqjLpKMu3LQZr1eEFotMjxvAVqsF9q5CzvwU0JIOs2IBXMmkGz8Xwhd/XxZwdyA==", "signatures": [{"sig": "MEYCIQCxoVEukvIDlOMyp3K1sEdtfTs2hd78FsvjFa00sSI6OQIhAIxmem0aMhbW7qtTAs4nPH9DBxta6R1IIvK8nw3wN/ul", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.6": {"name": "enhanced-resolve", "version": "0.5.6", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "c4731a1b8029ab43d2fb13106e412f3bc7f26569", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.6.tgz", "integrity": "sha512-9bbXjiIE7NnbwckWdXnViDNCqgwbrp7DhgJuuWsd2bSnzjTbgqbkUVIQDE0kI3oN/LEz0/AwNS1YGkyChpRkKA==", "signatures": [{"sig": "MEYCIQCuD/bgrBKBtpcv7uV7RZkG0l5jI5P6E7kUojC6OhV5jgIhAOXAtbt4keUuXgTH4Xel6LsCkrfP1gQBWUs4J5fOo7rW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.7": {"name": "enhanced-resolve", "version": "0.5.7", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "4e5549931c02bf1f57b8864d18cda65294504fed", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.7.tgz", "integrity": "sha512-IQ2zRgl6EX5T3n4uyvodBDer6nZcizW6oNXrGMyRsFUfDsevPYWTq2Y5aK/GBptB15g35oWwt35lt1ado2i/5A==", "signatures": [{"sig": "MEYCIQDUGfMYsnhyAi1rGvdLcUDwnWt3DzuQU50cYoZiXIZAOwIhAJIW4ajrPlvKmaKsEGPlKb7Wil3qK2875gTGnXNcvVEE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.8": {"name": "enhanced-resolve", "version": "0.5.8", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "07b00738f629fa0ebe784b394f9840dc6eb25169", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.8.tgz", "integrity": "sha512-xWWp7EdCDFzGWvwbTRk849LD5mTHbs0Uak8zWWLA5SZM8JOHMunp9KjyigRFL68eWfOiE/C9RlEmV8acikoSyg==", "signatures": [{"sig": "MEUCIQDfUaO/bRV4vY5gOvZ7ZPMbHDL/cyN5AneG4E3USan8SgIgfa83ld55przBPVI701qrLLj3vTDkdhYJeZ5EQk+QoGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.9": {"name": "enhanced-resolve", "version": "0.5.9", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "9977fd6ee9e4edc0b91705503a7ba87d7c83d799", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.9.tgz", "integrity": "sha512-bsUB8ei4+ZawxZNDnmnSiKNOqpOv9c0qbc4pxngPnJPefz8g1Or20xbo9lvdrK0iM+RthOyVAAcaeyeNEfNnFw==", "signatures": [{"sig": "MEYCIQCIvSvka7nIyf64OSNK1ibbhI2Q/hM8p/PSOGDsHnZyOQIhAPeHPm1anJm2yvpeJG6jgAo5s+RbbP0i47EcblUeMAUs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.10": {"name": "enhanced-resolve", "version": "0.5.10", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "7ec1ace1b77515bdc5365c94a09ce1898670afac", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.10.tgz", "integrity": "sha512-vgJ3NUMs0g/oA6TLbxPQxAVTFaKADbBFb32jLZPj0uonXte4e9gXflApMErz5vI68U/UrNyh8a2QryWuJFAvCQ==", "signatures": [{"sig": "MEUCIGde10shpb43rZfX1OxU6gxG/OJ5O6e9opypB5ib8JW3AiEA2gy1V6Hc96xO/otnTDVINmKjVqR2fRA6LJsuc8EJBkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.11": {"name": "enhanced-resolve", "version": "0.5.11", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "9014f331727ebf83de4ee2d0fac2952eaf60fd04", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.11.tgz", "integrity": "sha512-lhwXbmHE6sQQwnMsW4KHxEfYB9hpbO2yTnpSwdLm6qLaZV0zScTvJnUqXvnxsAaei6wFRTywLaMqFVCZtLMLEQ==", "signatures": [{"sig": "MEYCIQDzVHCFXCRzsfc5JuH86GhrGVqX/BsO4xyLdS9Is6gdqQIhANJQ3Z31blk8kuBICxotU6zZMefRgqrn95SpR+eqc4J3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.12": {"name": "enhanced-resolve", "version": "0.5.12", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "9ffd5a2abb85acbf1e6f38944900d8830e290ea7", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.12.tgz", "integrity": "sha512-W2jhQQB9ZbRCZPZKNgXZcIJUVQkBtV16+i3kE6Z+bEBkBZw/qcpCJVTlvqj/Ko26KQ9Hn99plr4sRTu49hlIeg==", "signatures": [{"sig": "MEUCIBkJBgFP8IMLDCrmpx9rG9MV/VQsQa6cOCmo7efoR3v/AiEAh4PI2CodB+U9r3PQugF/lcx01gjioP5tPVy7LT87z8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.13": {"name": "enhanced-resolve", "version": "0.5.13", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "3a4cefa931a3ea37f29279dbfb231fbe5331c731", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.13.tgz", "integrity": "sha512-zitSxrrv5TWfnCPYlgbb+wYZjMH3Lie8hVnJEaVjQL6if/ewDz1DnntVVM73O7btgQi3a7/TB9lI/3ARs/Kcyw==", "signatures": [{"sig": "MEQCIFNw41aRl/0ovytXxPCzOqViqIbZNSPYfue2R5khi8IQAiBlAPjpfg5EqbbiM5oLzO+QyNz2T0vz6puXtVBIZdef8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.14": {"name": "enhanced-resolve", "version": "0.5.14", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "1.1.x"}, "dist": {"shasum": "2528d28a45af1845d1afc8fa9686395f43de259a", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.14.tgz", "integrity": "sha512-XTNBBnN3yNO4NlPB0MTPoHrMq8/esBgJN4j2WPeofTblkVwqQVgZ7yo+s3iPkCGzjTiq3liI6WCNa5P+b7jy3A==", "signatures": [{"sig": "MEQCIH/3SxXpGOMq3RSJb/RcJlw7Jz9U56cJ/SJQDED1GF8rAiBmLYIwjOC5PFTGPjsCfweYIafsQcvW2L9d25lRjyDaUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.5.15": {"name": "enhanced-resolve", "version": "0.5.15", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "48ae41ebd9157e3e81fc51726955db8045fef702", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.5.15.tgz", "integrity": "sha512-3rSkbNkVBEz2d9JQfVYk0rzuNROy5uyp1GWwlXyBv+0u97e4A30XD+ipfIR1F0P+jbSxdq1bBvy43w2rS04jgA==", "signatures": [{"sig": "MEQCIG8KWxsBmze9QhzHkrURZhA/A5/tRZejcv9BugY/F1zuAiBx4TzhvKenSNBawghdpWYDK4zREq8vVthZYdpYzoht/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.6.0": {"name": "enhanced-resolve", "version": "0.6.0", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "b01230c84e4b7c4124fc52b2a2206d7fe1fca9a8", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.6.0.tgz", "integrity": "sha512-VIk3i60pDcYjycyD0HAsepUnRg1uFuDTlazGZRDFWeQtrCSulouMnvjX4YPEOVnBKFEczbr5ODxgAcbvSz/OGA==", "signatures": [{"sig": "MEQCIAnhaIPJHAujCibA67lJbSj7tlUqG8jcp8Bn7BObyUWRAiAqA/KhGKCX5ECHd++Zz3PD6kVZoPWLp+Zxm54qaY1rzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.6.1": {"name": "enhanced-resolve", "version": "0.6.1", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "3bdbb1e7e084d12b766502ee258bfea8ba05ff67", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.6.1.tgz", "integrity": "sha512-H+stx84M3TtjhTPqy2JmhfHKXOzn4G+6XM9qYF4qhHYtZV8elEwvoKehuyDSsqD/2rzXAai7wE9wce4zmaA5ug==", "signatures": [{"sig": "MEUCIQCc5HaYA2i9wbGN+ACeDBvoBKncHjykwP1Hwgx+nyP+JQIgP6f09e66ChDitPtOuufnYg+/wt1J4zvIQnWzOU+7Fy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.0": {"name": "enhanced-resolve", "version": "0.7.0", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "972ca2945da357947137abcde1d01750dcb94de4", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.0.tgz", "integrity": "sha512-XEYnGJC1FQHlqDmcO5CXwTa2ViWw1C4lJIqdSDRQ1ci5vTZmBGeorXRLR77S/2tjSVt3w4uXVyRfGTfNJHatvQ==", "signatures": [{"sig": "MEUCIQCy9YepE5VQ9qVBuV9dh5sCuoYr+iIgDnNnoD1388SJ0gIgc+fJc6iLUC2Aky2y79wWz7rws4Bb64tcLE47CRXzSkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.1": {"name": "enhanced-resolve", "version": "0.7.1", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "d13bd9bb002cdab64b18225ebe1289c901be41fd", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.1.tgz", "integrity": "sha512-scuzbDkG7y0MTS9vjYgHKVJ5Is/zhyxp58e89M1pmTfEXVhZYXYn1ZbGJ68CmmjHm3QAmVBn1KsevP7cmk/zHw==", "signatures": [{"sig": "MEYCIQC/BFGEW76CewI2WM252bYzr1DyKBOH6NaodcRr3ROL2gIhAPjAoMc/GSPvZKP/37c5DczTn41h7VgwbxIdG6CgTk01", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.2": {"name": "enhanced-resolve", "version": "0.7.2", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "226ed39892520fd987f25cf0af7707211efacfb1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.2.tgz", "integrity": "sha512-16m98ijAoX05kzDOirNg91e5/P+HUaGHumC4MQFPJnN7bzBj4XV3eRAwo6v0iGuczRkxD7dY8yPg2bVCCZgk7Q==", "signatures": [{"sig": "MEUCIQCqM2xu4NAb6the/Cs9L1q3bflSNTDEU5LkDsCOIpUEbgIgav2amkcJ8avs6xJoWv+NP8T9LR9qbwqgBZOiJ3qqHBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.3": {"name": "enhanced-resolve", "version": "0.7.3", "dependencies": {"tapable": "0.1.x"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "f283ff1607e36d96b7ca8433b5421d062b41c99b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.3.tgz", "integrity": "sha512-K6/FaBJdVGHTOM93E/IbJ8dVe7HZxipuWXHyetC2Qdmz5Pbu/QHrxVC1Hg9pQlUwD+73+HVxrHlkud3OZ/gCrQ==", "signatures": [{"sig": "MEYCIQDqcGq6ihccoxHPc5D1FnCPTw1c4MP0AWF8tf7XGJGpjgIhAIbVB1noRA9ApEfXuhYUnPxEM/ydhPvKDhDIGJEuE/lT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.4": {"name": "enhanced-resolve", "version": "0.7.4", "dependencies": {"tapable": "0.1.x", "graceful-fs": "~2.0.3"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "830c240a464cd37da1c2d4c6ca4198cebe984698", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.4.tgz", "integrity": "sha512-cYnHivWKsqskOD0t/SGZxohlj+wE4SI3/LmDBtHJ3PvbJbG/8bTuk4gVV2hpX6oghJTMC3eLtWB8o6P5vN4DQA==", "signatures": [{"sig": "MEQCIGvGX6B9XOmggUVHSCLlQtzEQX1/QJa9sdQWX+D2V5tqAiBRwd26MbF0TgMHlCJLPAAnhYP7SkuzgyOoSlB0RgTakw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.5": {"name": "enhanced-resolve", "version": "0.7.5", "dependencies": {"tapable": "0.1.x", "graceful-fs": "~2.0.3"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "f6be9e189314ab308b023bb45e24eff8276aa628", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.5.tgz", "integrity": "sha512-EcluESCN7sCYjaMKDWz7aHjf8h9DK1qi9Nl+mx4tR5lOLeCXnGMFtHP8zrR0tcmmbQbe98kgoHG/EPHL1EU1yA==", "signatures": [{"sig": "MEQCIBKzo72ICF0Kw0NXak/bJvl6TE/VD5iknQVimy2kmFvgAiBn1bzV/6zhL4MJHbTmmUKbtjQGD/mPQdWLy7gTA2QS9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.7.6": {"name": "enhanced-resolve", "version": "0.7.6", "dependencies": {"tapable": "0.1.x", "memory-fs": "~0.1.0", "graceful-fs": "~2.0.3"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x"}, "dist": {"shasum": "4a429f76133da8266098e54be820e165d37e5ccf", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.7.6.tgz", "integrity": "sha512-inB9T0vetkUEkNpjCnFjqIbhtqHfDmqnIGk+xrmfCrSVk3uuX8xvsTlUXdhxVwTEQ1keuR0njWeJnBo9D2HRdQ==", "signatures": [{"sig": "MEUCIA9PCKylXkcQT+oEEv4JzpxBbYEgB1TTkTB5ZJ/XF9DWAiEAn/gnWpHNWckIuYKlW6qyrBlHTx9HdtCUbQgTjti9b4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.0": {"name": "enhanced-resolve", "version": "0.8.0", "dependencies": {"tapable": "0.1.x", "memory-fs": "~0.2.0", "graceful-fs": "~2.0.3"}, "devDependencies": {"mocha": "1.8.x", "should": "2.0.x", "istanbul": "^0.3.5"}, "dist": {"shasum": "94e8357912828997caf24be48d074a6a8310cbe9", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.0.tgz", "integrity": "sha512-cY+DjMRcROAgTLECfeh08JYFVk9bqpgxVn+Y4EE7u5SezQgLDAB4XLqfLLN08vPD9CsfZem49F1+zu3JSHV2dg==", "signatures": [{"sig": "MEQCICPzpl1luC/cMGDBd3oKuP4EnIwo9UEMZbC8FTix4JVZAiAd9elIyNmdkDIqu6xGW3p6qSXjai6mV63EpHcyvgs7Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.1": {"name": "enhanced-resolve", "version": "0.8.1", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "2ba0ce61764c2e5bf7d40f7cc376bd8f14e3ac68", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.1.tgz", "integrity": "sha512-cTfKqDChGvhsBQIjIUD6D6maQrfJsO1pmkZ4e3ptOCEGzABBMSv+aaYPQaXWkSzYF/b4VL/rLYTCrmHHG7vSCw==", "signatures": [{"sig": "MEUCIQD7U0wRa13VShTwL7zTZuIM5X3Pq+w+6aVnkSpstn3mRAIgFaHQqOkub8zNoIxSVYt2QaQ+6jQY9XC3xm2L7y3tN0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.2": {"name": "enhanced-resolve", "version": "0.8.2", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "57d007c18f0a97c369e81bc28a5992419466be5e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.2.tgz", "integrity": "sha512-Vl7E4JfDI2L0PwcjjCQDrQVA79ft1ePeg5Uj2X7bHG/hyfQsOrhqjFIybkKmKBJ7HgqKvwC0fBsnSaS5CJo9CQ==", "signatures": [{"sig": "MEYCIQDKb5bblQgF5QwTeQkgC55xjxO7fvkxy9aYmoejkNRZ1QIhALMLTLM6P4c3i4XaDqr57c3zleD+8O1IS4Iu+OXBMwLf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.3": {"name": "enhanced-resolve", "version": "0.8.3", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "00e1a46a499379b2022c29516131d7246b8f60f1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.3.tgz", "integrity": "sha512-dgvanESBskXNZfJqBq0NtRCNl6uK3Ddjq8uQK0sISqKVU/wyKUeUflaO9WQyZvp0DzqELp2ukWxo4gU5cW7j0A==", "signatures": [{"sig": "MEQCIEb43jcg570N5Lup+MixHxqXCzo1Du7Q4vyWUVfRhBPAAiA/iMEuLdGpql2c8HXyUs2EFUR9p2Vftw9gAZ1ume2WNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.4": {"name": "enhanced-resolve", "version": "0.8.4", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "4dae8ed556e5312256a9dd317919478e904b5387", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.4.tgz", "integrity": "sha512-tQbeokpqCcC+u/yxXLp6nesXFS+zfcU2uicC2C45dmUROaTKVW9mdWGLN7bz6KpLN5i0GkJinrXdcRDhBaOO0A==", "signatures": [{"sig": "MEQCIAjJ/58N0fFATKQoW+Pk0lirJ5CvHVpI/OJRu3IMPuYRAiALzijIpi6aGMcrS4KW5EBG8c68K/q87pUbmCTq2JKDyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.5": {"name": "enhanced-resolve", "version": "0.8.5", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "cf0a05c2d186969e1681963e59593ffdd8a4362c", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.5.tgz", "integrity": "sha512-Efklqt4vthyweRKYx2d5C5LldgzyXAFdgowuSGgcHVKs2pYPMIdKqfKfRiYRGhNt9JZ0s9OtLuF2uYjQ4Rb4rg==", "signatures": [{"sig": "MEUCIEr3bRBBcNICdJGha985jM3trdYz/33j7+ogps+ZjJ/1AiEA7J7OcuOdRhkdxS0rgejiJ0BcXXgTmTN4g2Wn5+qs4Is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.8.6": {"name": "enhanced-resolve", "version": "0.8.6", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "0806a6b643949aa79f879e5adc6db8bb2b77f35f", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.8.6.tgz", "integrity": "sha512-cwA7rKMq0BJnMbUk7YCFqbvbi0Gq3rzDospHb7K67VZB8xEmsoT1vR1debInNKcfIjZM4uNqZRuvrjeSOdKKGA==", "signatures": [{"sig": "MEUCID/eB9SKqpUrjxUtHPDbZDWR0HFrzrU2g8Rp1tgDZlVDAiEAlv0aDTzZ2CYQC+wRFbDUXpy904Y3ozSIyjAQ4V84zuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.9.0": {"name": "enhanced-resolve", "version": "0.9.0", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^3.0.5"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "2354591f240649669f830e33ec5c3f2e5d80d400", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.9.0.tgz", "integrity": "sha512-aI81WRflL6pRleyujfK2uUjNKNvopeE74QwyvN5yeEoQDg4kK61FwuM13V3f7IF3RDtkwe1lCFdnadt5JbTvLw==", "signatures": [{"sig": "MEUCIBzoLrjg5sd1OAcqUpZEycTTQvxYrJfWFQF18Um9dZZlAiEA+BbU8xMm36is4pC5a6GLmpyt61mP8lKOhAX/FJElqhc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.9.1": {"name": "enhanced-resolve", "version": "0.9.1", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.1.0", "should": "^4.6.0", "istanbul": "^0.3.5"}, "dist": {"shasum": "4d6e689b3725f86090927ccc86cd9f1635b89e2e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-0.9.1.tgz", "integrity": "sha512-kxpoMgrdtkXZ5h0SeraBS1iRntpTpQ3R8ussdb38+UAFnMGX5DDyJXePm+OCHOcoXvHDw7mc2erbJBpDnl7TPw==", "signatures": [{"sig": "MEUCIHoyHqSHRabPQZP8Mj+mloZIKjFYUFPUyii5jZ1d2VonAiEAyfN1S3QaGmjMmDLVHbP54Su7QbvupttgWN8YqOBj/zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.0.0": {"name": "enhanced-resolve", "version": "2.0.0", "dependencies": {"tapable": "^0.1.8", "memory-fs": "^0.2.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.1.0", "eslint": "^1.1.0", "should": "^4.6.0", "istanbul": "^0.3.5", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "5984076290a36df1f9d5f6dabd4fc425816c8436", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.0.0.tgz", "integrity": "sha512-YocsMod/UqpqIZSVyHPi06mZgM3+sOYISCaRQSzMRukv+a/8IEKH2z3RZHGpXPx4zEt/uBAUeaPrhTGEXLP8ig==", "signatures": [{"sig": "MEQCIDji/j0z39+TfTCfEqUiwlDvs1aJxd8IuwrJvghqm95HAiBwjFfBk4aqiMImWB482CaOg1En3cSqXBCtEY0ywIZ4pA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.0.1": {"name": "enhanced-resolve", "version": "2.0.1", "dependencies": {"tapable": "^0.2.2", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "b554140e14531448469b58260c518e7ec87b1c23", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.0.1.tgz", "integrity": "sha512-NSt+5Sw5eMgq1OB4gUJfXDkDuCPATNVAuMtlm8j5nYDhUTK3H5qUXT/ZPG2Gllhhdqdck2fmOnaQbWASf5APfw==", "signatures": [{"sig": "MEUCIARPYgEDp5sHYEtBp3Tis45AUqEu0lNLlP2lz+tPILhYAiEA8SaXLMHdvhL+7q5nCAEISz6bXV6NbUlnksypj/KrkYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.0.2": {"name": "enhanced-resolve", "version": "2.0.2", "dependencies": {"tapable": "^0.2.2", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "dd6f715770123d55ea372eba5d4cd0af69757859", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.0.2.tgz", "integrity": "sha512-oawVY+SusQzpc1w9JXpEjTtDWY46ZhwjbFzwdgKoHLtFQScduSqmZBG/lcSyjXe4RwWPs/akEnlcAXZoq4xgmg==", "signatures": [{"sig": "MEUCIHZfDvCAGRkVk14/e9uJA11L7o6YiqkrQB4c+D18lsAnAiEAul+aipOMJHqBf1qdto6Fe2ejeNK8cHVXBxGH+lk2Vq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.1.0": {"name": "enhanced-resolve", "version": "2.1.0", "dependencies": {"tapable": "^0.2.3", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "e630d78d969106f7a41b2c1f601ddf1550b1a5de", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.1.0.tgz", "integrity": "sha512-KPlHJ8HJg9weHiLJxR9M3N72dwnGuVNCcVhRLpoxIiIgVaJ6NcqjbBkKOxvrFhvEB7dr6X7bnWisff9R9Ayzyg==", "signatures": [{"sig": "MEQCIAU0RBJLKBsLgXACBL3yDolEaUZHiGtw0Bg+16OIAY04AiA95t2XEmwTahh0Mnc//XBtuX06of9l0vhF043bEYwRDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.2.0": {"name": "enhanced-resolve", "version": "2.2.0", "dependencies": {"tapable": "^0.2.3", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "569f117a0e28b1cd1273874d7d5f4b9a20357432", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.2.0.tgz", "integrity": "sha512-3Rf0odxVaTgiXBV1TXIVJtP4e+uolppAy6ScBtGMowik78VVzVyV2jh8wA5DoHUk34JzgFnqaPD7CtBHlYGVFg==", "signatures": [{"sig": "MEYCIQCcP6NdmqPAWxIeL9IG/3LbWURNz1cabsiHNtxZSHcOLQIhAL5Xa4fhT0B+GZJ5vTX9fb9FqQHVUfc+aBy2dg1G/FG5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.2.1": {"name": "enhanced-resolve", "version": "2.2.1", "dependencies": {"tapable": "^0.2.3", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "74fe93fe62b21a10d696886b1c3f0689e2db2277", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.2.1.tgz", "integrity": "sha512-Wqm/X/teqlLyMBPJLCdU56bw4ZI7VC5Qbi18zP/6ERhcBcwqDvTWfTl6PCnwHBvE+dAgUMTqUHD73MdpVqxQcQ==", "signatures": [{"sig": "MEUCIGQV4sWXJsRA9P2K3IONQNBOJaFp7PGPNhMyfvs5zcWjAiEAkYbZMopWlNkyVabBXbEMnN65Wt9k3j9r616KO1aSHF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.2.2": {"name": "enhanced-resolve", "version": "2.2.2", "dependencies": {"tapable": "^0.2.3", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "4da494d591183308bd46a95f5b40e255dc9d4a16", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.2.2.tgz", "integrity": "sha512-ZIMb0OgKi1C3249Vh2EYna0Zcsa2YEMm0No2eQimhq8kI3QI0otY/u/TSWwbqlZ6jhoLrspPzHhUZ0aM4GmFFw==", "signatures": [{"sig": "MEYCIQCokirhfgXXYlZ3uIfphoW8HJkYkjR6L5LYNOiar4p11QIhAOglWE4uwONWQ1YbHWqrkjMM6D1u6dHfmCFhIZh+Z801", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "2.3.0": {"name": "enhanced-resolve", "version": "2.3.0", "dependencies": {"tapable": "^0.2.3", "memory-fs": "^0.3.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "a115c32504b6302e85a76269d7a57ccdd962e359", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-2.3.0.tgz", "integrity": "sha512-n6e4bsCpzsP0OB76X+vEWhySUQI8GHPVFVK+3QkX35tbryy2WoeGeK5kQ+oxzgDVHjIZyz5fyS60Mi3EpQLc0Q==", "signatures": [{"sig": "MEUCID4+Dgvy8Y9BDOeScFawUCfIhpHFw3++3NxARKrfFHUWAiEAyZ0d5RLbRZ//xotPNPegFzaLOhLOfR6I05dCW7G2Wgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "3.0.0": {"name": "enhanced-resolve", "version": "3.0.0", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "cf30a6600bc236a4fcef627bb8e5adf072511a8e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.0.0.tgz", "integrity": "sha512-jcpAXUe/9swM9QfgajBXZwiyHRGMcQuqzf0/rlHl7qWycq7KD4+PYlrWbEf4e5qC4Q1yQHx2cr8FUNq0It7nlQ==", "signatures": [{"sig": "MEQCIHp08YL+7LFw0hq3i5NcDmvdPdCP3ZXZGME1kVu4R76LAiBy1jJgtcojZxIpUBF7VW1gOdKQzCMPmzVFTF0Suloj2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "3.0.1": {"name": "enhanced-resolve", "version": "3.0.1", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "4df07e42d444fd698876f0feae55f7b992012ea3", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.0.1.tgz", "integrity": "sha512-s5MHaON8i3d8kXeC66/YOZBMbLpNSKhP90/RjO6OXp6gglua9aJZv5F6ShD/FhX2oW4WECXIpceGAKdjFCEYwg==", "signatures": [{"sig": "MEQCIGmwzO3vjM0/kfPY5A1PdHy8Hda5PaA6+5EQz94WfZTHAiA9yff4YABAf7ckRL7YUfvZXng9nXUo0qSm7u2tvTgs+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "3.0.2": {"name": "enhanced-resolve", "version": "3.0.2", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "0fa709f29e59ee23e6bbcb070c85f992d6247cd1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.0.2.tgz", "integrity": "sha512-E6letZjQrUwOcENItcr/8w2ApkWCB5NP08WJCtfTij3nK+C24EIgVb6VrQYjIfxVBeHFPi5iKsO2HrnxWLiVow==", "signatures": [{"sig": "MEYCIQDvNc51NF5/ELSiLSdaBIgoJAcpr4IMIra959HOsJRuCwIhAJzzIWzly4phl2EBLFryS0iDcNELgB+aOCFMgwMGHveZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "3.0.3": {"name": "enhanced-resolve", "version": "3.0.3", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^1.1.0", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "df14c06b5fc5eecade1094c9c5a12b4b3edc0b62", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.0.3.tgz", "integrity": "sha512-Whh0HTS00afHvYnjXwr17A5whW/zWVBs28YErmTXeJJX6MxtSkULW525ZApxU3Y/BdpSjRVzVjmzdsnrVxTn6A==", "signatures": [{"sig": "MEYCIQDql+vmAcamjlh8smS1T1Hk4tCdtZyT0V7LKkgdW+3UqQIhAIx8ed0R8qFcr0N5VZnzQLqVPM0k6CbQCBTkTtcouVsw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "3.1.0": {"name": "enhanced-resolve", "version": "3.1.0", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "9f4b626f577245edcf4b2ad83d86e17f4f421dec", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.1.0.tgz", "integrity": "sha512-Qkl4UBkjqcE44Y5hfHBecow6X4sH1Va5LnReabyMCS7otozX6Zpl/23n5+Ea9KqBsdFUpeL1kLUaJV3tg3Jhyw==", "signatures": [{"sig": "MEQCIDgfuRWqD3kFWvrO/0daD421Z96LCssaj3EjfW5le0U5AiBg17Kh+fMp+eJ2or7deLdjKU4jJ/jfYmmKmW/67WAWqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "3.2.0": {"name": "enhanced-resolve", "version": "3.2.0", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "7b60300c98e155a9caa06bf4550ec010bf74f6f8", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.2.0.tgz", "integrity": "sha512-Vlr4UydY821fadrNdW00y4FwebPe6TnD9dK0HhwhsqXEVpVCZnlZiGMcysrLT0+zENzA5Q/k9NCTXW0qbNRsxg==", "signatures": [{"sig": "MEYCIQC4BMNQ5vtvuP/PG2i6vxlZ5ooghjwIgfCUuCcyuPNv6wIhAPzbQQEQ6LKySbgVHhpVDZHtyKcKiDBB6E6nAYuUS032", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "3.3.0": {"name": "enhanced-resolve", "version": "3.3.0", "dependencies": {"tapable": "^0.2.5", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "950964ecc7f0332a42321b673b38dc8ff15535b3", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.3.0.tgz", "integrity": "sha512-2qbxE7ek3YxPJ1ML6V+satHkzHpJQKWkRHmRx6mfAoW59yP8YH8BFplbegSP+u2hBd6B6KCOpvJQ3dZAP+hkpg==", "signatures": [{"sig": "MEYCIQC5RR1YCEaQ06Fqw4SNdKrDd7Qat8kjdMmnYlZI5XohhgIhAKsN2hzyJdHRQOLi4ipEqo5hxYIA8ogSAkl5cKZ+vr8V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "3.4.0": {"name": "enhanced-resolve", "version": "3.4.0", "dependencies": {"tapable": "^0.2.7", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "039c09818871c86efcff7b4b69dbf6c9b18300a6", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.4.0.tgz", "integrity": "sha512-8bM8nfAd4BFwLLlaiFHQy2fHPtcCpRqJcfaqMH1UREEKrje+PcqvW1Nz52BpjW77GgSPW1LrR1YwQyUalLoTgw==", "signatures": [{"sig": "MEYCIQCTcke4UULEQpS34q0ULwWuEdxJo/f5O0hvhQ/QLlHczAIhALEu8uu/F4ZHmRNiDoPAet40jv04X5TCoYo2baRA3VHv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "3.4.1": {"name": "enhanced-resolve", "version": "3.4.1", "dependencies": {"tapable": "^0.2.7", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2", "object-assign": "^4.0.1"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "0421e339fd71419b3da13d129b3979040230476e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-3.4.1.tgz", "integrity": "sha512-ZaAux1rigq1e2nQrztHn4h2ugvpzZxs64qneNah+8Mh/K0CRqJFJc+UoXnUsq+1yX+DmQFPPdVqboKAJ89e0Iw==", "signatures": [{"sig": "MEYCIQCLKJRsnftWT2weL0E6Ut79HIRerV8dyPwiznxbkx1YqQIhALJFUmx5jHojOOfAt0Y7hxArEQgx3rkHHwrWLykz6AnJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "4.0.0-beta.1": {"name": "enhanced-resolve", "version": "4.0.0-beta.1", "dependencies": {"tapable": "^1.0.0-beta.4", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "f8257df604491e83eed7a3d20568fcd4c849c251", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.0.0-beta.1.tgz", "integrity": "sha512-vJ01fSrObB1c5tZO4SroRgG+oPkExZ7dUYpEtojEscH2K7BJcg8rWWA8TW7CAQSRb4euJ9LFU8NAbpG5QxOCeg==", "signatures": [{"sig": "MEUCIBS0lwSF1B8FCI/0KctoN5Jlx7SRe8elhcrcddpWlm1bAiEAh4mBNdPHYQB0hJq1Lcwy6VEn774jJ6Nn7BxKKz3whXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "4.0.0-beta.2": {"name": "enhanced-resolve", "version": "4.0.0-beta.2", "dependencies": {"tapable": "^1.0.0-beta.4", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "a13a9da2340712c4f3dacf27e26717e2eae69c24", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.0.0-beta.2.tgz", "integrity": "sha512-OQfvXu+nF8vR+CiNYjUk1565pROxNhlQeUmjxHdfPTsj1jM0E0k0q8Ez4zTjoV07mt9GB1rtVGUctwhUwfP6uw==", "signatures": [{"sig": "MEYCIQDCO95daRMXYgsQgPUd/pI+QAzFTWi3eSXYCcYohNd+hAIhAIa892DA3spPbEGJrlyGZkne/BrNvX2hygZr5/dxmHTo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "4.0.0-beta.3": {"name": "enhanced-resolve", "version": "4.0.0-beta.3", "dependencies": {"tapable": "^1.0.0-beta.4", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "e06b5964fc10e067370a7d7b2b52db64ccad607f", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.0.0-beta.3.tgz", "integrity": "sha512-4gvfQYOhCu0q9QoPPAuXHQYchYR4/Fq0iXSsfQRrGk19B7ktBAx/r6kLTXNoBEJ4CKm2tSSpe3kMwWVCkU06Sw==", "signatures": [{"sig": "MEUCIQD5UUhLINUJjVsvtLVik0N6ltbfw4RyHiPLLq6Qepdy6QIgF5fdWZfsuBKGKPWQk0JCESbXY832a0KPar4Mnut2tzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "4.0.0-beta.4": {"name": "enhanced-resolve", "version": "4.0.0-beta.4", "dependencies": {"tapable": "^1.0.0-beta.4", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "17e14aea8ded4d6daa4a6bff67f2d38fcca76452", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.0.0-beta.4.tgz", "integrity": "sha512-9d8MpQy2g8+DRPh3vhU3DnxVTDJ7R5nD3i4N/mhRuBzuHAtFnLawXKEsjsi88S0Jfr+5i6sUJu05B9VrjxIYEg==", "signatures": [{"sig": "MEYCIQD2KFI2TIeL2bjZIwoHzTVPD8/sKRjB1rTzDz20yek9KQIhAMqC2tAQR46DffsSkvZqKUTDaoeFF4Ka21v1I5Omel57", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "4.0.0": {"name": "enhanced-resolve", "version": "4.0.0", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "e34a6eaa790f62fccd71d93959f56b2b432db10a", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.0.0.tgz", "fileCount": 42, "integrity": "sha512-jox/62b2GofV1qTUQTMPEJSDIGycS43evqYzD/KVtEb9OCoki9cnacUPxCrZa7JfPzZSYOCZhu9O9luaMxAX8g==", "signatures": [{"sig": "MEQCIBGCd1FOId8BSdGjShT3PBcXdcJY+b4C/RWD08zmpgBFAiA2PXRK5wA4Bzug1ujIQYeZup+aqBEJje4JsVHRXDLLxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87441}, "engines": {"node": ">=6.11.5"}}, "4.1.0": {"name": "enhanced-resolve", "version": "4.1.0", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.4.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"mocha": "^2.3.4", "eslint": "^3.14.1", "should": "^8.0.2", "istanbul": "^0.4.1", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "js-beautify": "^1.5.10", "beautify-lint": "^1.0.3", "eslint-plugin-node": "^3.0.5", "eslint-plugin-nodeca": "^1.0.3"}, "dist": {"shasum": "41c7e0bfdfe74ac1ffe1e57ad6a5c6c9f3742a7f", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.1.0.tgz", "fileCount": 42, "integrity": "sha512-F/7vkyTtyc/llOIn8oWclcB25KdRaiPBpZYDgJHgh/UHtpgT2p2eldQgtQnLtUvfMKPKxbRaQM/hHkvLHt1Vng==", "signatures": [{"sig": "MEQCIDKiHhHr2hdJ7JgGzq9Eo4l+SQfJAwX3Hq/hEQRguVKhAiBa3m3y2KhmXvOT964U0dejZgxCo2fQXXzZX+XMA2agGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNNlBCRA9TVsSAnZWagAAelQP/RN6qjoBYuiboI0Hn6DH\npe0HdLWarB1fc4MDfK19k8bCsZJqN36ISHgfQ1NTXfWGbdN+r/S8coNc8zxk\nMWkkMXSyo8+/KgMm4fYJd8MrDfKAqRwd345tiLipVYihfXqO5K28aBJhOiDo\ntS0GJFnRhkiApQDeKHHzZLGFvjBXZGQ6hjHaQHY3pvp6x6q8s0xzS5CRO9qy\nKDmXDTKAtY/k28gA52nhSbMezQTRE94sX9ymlb4hn8nPZw20QtvPz6KYC9kT\nXkDJonOpGOHOyGutoTRXsu7VkNNRl2lMPCx9C15m5rDaYkXvdX53wf+vyG4E\nwW6InYDr0u0aVq627wZ3rvgURwEqTxODDsmrJ8ZAct3dQGZ/yVi+hdgwyTr6\nfIXstMG3Z3WDdXZll73oQ2hT/MSBXPSaeu8w2jmMgprjVg4dpgm/DyXeYE8T\n+Vx/KKUBPyOdlVtOekkVSw4hV3xdeuU7ZvTW71r18y0V0xpu4iPiz+jmEDaF\nsQpvDakoDe48X/IsmJA53BQQMiplJIyuXYdfcNrCA7PrwsFiUzUc1z1FRaWt\n73lZOmJ2VUawrBfzbPTNj9byU1hwyA4+rMCbKsLzHet3AhZfAmRVTJ01NFJg\n/OeYIanIWLBGBqkPLsq9eV4b7+QmnbveWqepoNsCf0yCZVTLjZ3legCyrEJs\n6djg\r\n=L4ha\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "5.0.0-beta.0": {"name": "enhanced-resolve", "version": "5.0.0-beta.0", "dependencies": {"tapable": "^2.0.0-beta", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^6.1.4", "eslint": "^5.9.0", "should": "^8.0.2", "prettier": "^1.15.2", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "7635594f4f38a515ba3a5a93498556828964d3ac", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.0.tgz", "fileCount": 37, "integrity": "sha512-s0jFC1SvF64ZDMdJfkSATGvQPaXjOq49PJHEvodqUiY7pCMRzHjfQgTkTM+Nxpg29ym9ASjEPSiHqo1trqvVEA==", "signatures": [{"sig": "MEQCICb4QvfFVqpT5/f+FZmrgFBK9xLKr4gXGpuKiQnVRX7vAiAVuVjq1MuDHbyvJ/wt5AQaF9StBcP2+l4RDyPBxn+9vQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHmYMCRA9TVsSAnZWagAAZX4P/0EYFYQZdBnh6Oc2cJIV\nuZQPY3fsHWm8zyullzmpu/Z0mj2CBwEyX6Hmxdy8jPGLTlmoestCUnkTKXKt\nve1kyPKM1XpqlmEIZ2Rbu5r4REia6SJunVla8Zs7pRhqvyTLirGRXagd3sYt\n6yh+7KlTt0YajSL3anbAXkrbnPfzzyfBg5E+GIaCYGU8fh2QX6wrv8XpfMpA\nBtWMo0K+F2tETesHVFSjNyqHWvIRfavgn/rhfcW3kELyqU8cJvzrApVeMZA9\nUa4CsbDVIv8gfPWqjm3LnI8pobYVI6WeCHoMlFNNBpPZbGanwDCjW15Uzmp8\ngeCesjL1IQq+9N7WP7amcpidyuE5MG0AHD7Zolj4htHOkxlpZh/2XiZ0dXy+\nDFHJs3q97PwncSonzbF3H1UfNtKy6lE3Gbsm9C8wmouuZhzR0P2+vBYOdJ6L\nOudLONfADyCJXMkbvCjMazG/KEOG5etxWHBy3jYJcKm9khjEj/UBcJIgPJwd\nTxeg7E35J/m0qQEkETVQhd0+7T8zqJ30Y1Vp+CHaiyko1QO4yDybu0zjphFD\now2AaGfbWrL4lZ0z5OSGuxM7Q4+gwGkVf7meV+1m2eV2Hr298AJvmwly2CXf\n129LiRdyBsXlRciU2ND3rH40EBFtrdrWGn3bmtYwB8mnZupQSiKq8+xJToEw\n4si3\r\n=DJWF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}}, "5.0.0-beta.1": {"name": "enhanced-resolve", "version": "5.0.0-beta.1", "dependencies": {"tapable": "^2.0.0-beta", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^6.1.4", "eslint": "^5.9.0", "should": "^8.0.2", "prettier": "^1.15.2", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "600c242ec84a31f7198e165ea08143eef2cc3a04", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.1.tgz", "fileCount": 37, "integrity": "sha512-dlCyQrjJDL/RJzVWzZkrO5aL8EYCGpMvqGv5IPwaVCO8yLMs+rQuuMczeOxYNj0hD2DAtOGAfvocgDkzbNCmDg==", "signatures": [{"sig": "MEQCIAjhQLebHa7ajz6euqbRmTCt2cNvbBM1OeaSxheMk71/AiBc2B1ElbxbOnwV9mvs5ke9AM7F8d8LMuivvTS/xoUt8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI1DkCRA9TVsSAnZWagAAUEMP/1u+WyS7suOYN1nWPvnP\nB5MtRMUxD6WZoV4qOaQ1jGl0b1yOgF+G4ie0sTZtbnN5HhkH5ZeUD825BOLv\nrVwUVtVtaaKEQ0pN+Vhr7SRR0DVLhEuMM/OKd/RqtYdjwMsN3NJrkWDQEpwP\nNQI+HU80Lg5BzUVUeUv3fqJ6b0X2goQierhusXscweZdMYxa1Ax42PTx3HwS\nUc9BaXPxB8wg6buR9iqDhBmpXAbOamoPEwu28pT8x4dV0818iteTjTCcVC+I\np6PB0fc1mlVaWluIK3uFh5pTQ/EI9sU/IovZgUngaupdLIJsCerQeycxTimM\nC2+oj2VmbHk7Zyorulsez9NtggKXzTWt6jSbKgJv8fL0Qte3R+iCM3rt2020\nxIcwXYqKZul1HZEEgjRWL0MzTq5TCHHvNomD48jPP54olZwvwgd2hMsJSvQu\nEJGB5uVPiyzVVSa+3puSf56MF5IuBC6VqcNiKF4Y70Qul/KqpWmwPBodkDxr\nrKALYCNEXAdopNTgduH/eyFmxI3TDf6CIyTmaaqRb4OK8+ueC669OHkBWbmp\n50SKS4MsSSF8HOIQxwU/wxwNX10/NhzcVBYzgsVUuEy4Dcj90hAXZdxpNsEI\n1/w7Lh5XhFddRUsVtz9cfE+YiVX9mBiDeTd8l982rXY9k4gRywdpDEi00Ahe\nrFaT\r\n=wrtK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}}, "5.0.0-beta.2": {"name": "enhanced-resolve", "version": "5.0.0-beta.2", "dependencies": {"tapable": "^2.0.0-beta.8", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^6.1.4", "eslint": "^5.9.0", "should": "^13.2.3", "prettier": "^1.15.2", "typescript": "^3.5.2", "@types/node": "^10.12.21", "lint-staged": "^8.1.0", "@types/mocha": "^5.2.7", "eslint-plugin-node": "^8.0.0", "eslint-plugin-jsdoc": "^15.2.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "9dfd00b7d73e7d6e0acab9e1f54241a557edbe8d", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.2.tgz", "fileCount": 37, "integrity": "sha512-qo97XNV7JrYZR9eaFRPfKtOtiF1RBTGIx9v+j5DttaZyAhwgXnhs69YYTzEKgQpiOW8snnAiVdp5/eu+d3/55g==", "signatures": [{"sig": "MEUCIQCap02bOwv6Zb0hYV56/qylgQg9qtDUGcL1P48r/9CPGgIgYP6FhEoea9XkgjRn1TV7e+AzaJ6L6cRqq7YEVc5S2mY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJMJzCRA9TVsSAnZWagAAGfoQAII07q6bTQ/P1D97BmqM\nKiH/ok2rj14IgYOJIEKo8mYp3mjNjnfyv1Dav8kN0GRjTfI9g6Dy1ttXevxR\nkRuvaSRniEc0n1JvEKfOVdHOvCgM0CtAkTSJvSxMm/sX2X0wivLLaaTB05b2\nIL0kXz9EvxCF+fq/yRyJlfy/Y4nSSWEvl+hKMwwektAkitIB1kaM8hh9pP3j\n9raAzegpWQmKEt06qtOU4C4p/b08qC7BYPZA6O3g+wGQCoz3/4pqi+3NJKzD\nE4FrgZ56DK6Tms6XnDQ2IvGF34uft0pU5nD018HJh6cglLY5CS+8M2XLdtmG\nYkiwFU9/y1Xpgvok+oKbGHFyPSF2kJ0R1eC5GlTTMmQBB8BjqGqCzx7EFbJi\n3GygjBeHU6yfT2m3dofLDx0K1bJrhJAuAjc7/PR9/pAfqAMw3OoK4S66AxKV\nbCBdksW0q/bhvQ+KIdQX0rQ7Sw7erWPrqL3Xd+dQgA3orgpkYHWcgQPmLVpV\nVkESc+OSY2V47xWs0ooStX0TZavPPOKry9ztw2NMLkjFdY1vxLTmeV5objla\nxpZ5PJT63GrZ4oExy/+glcRKPAfAk8d5kkA/9B1g8ngt0eXt06/5XoZwhToD\ndxrpnrTeyLSRNuSWRYpbK8Sbe71FL/sO8Aw/D/I1N2dxSXkGy7Q5+pqC5dRO\nu3Cf\r\n=cm4E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}}, "5.0.0-beta.3": {"name": "enhanced-resolve", "version": "5.0.0-beta.3", "dependencies": {"tapable": "^2.0.0-beta.8", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^6.1.4", "eslint": "^5.9.0", "should": "^13.2.3", "prettier": "^1.15.2", "typescript": "^3.5.2", "@types/node": "^10.12.21", "lint-staged": "^8.1.0", "@types/mocha": "^5.2.7", "eslint-plugin-node": "^8.0.0", "eslint-plugin-jsdoc": "^15.2.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "1f5b24d223db90a2e86235c365e337fcbf28a68b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.3.tgz", "fileCount": 37, "integrity": "sha512-SMjrP/h0qau9mj6L+dY0I350vTnHUDNm3hjV2QRzqhfaa6UYAkDoLfuRWPqAErKDejveFPyGLiQx1A8a5uR5hQ==", "signatures": [{"sig": "MEQCIGXz/r3Okg+qnvuvRIZco5T9uvQwd4cJBRheaf0Daq2ZAiBRRcbAFrpK7uqT28p/RgXlKA4KwtNdsIpmSeJch3z5CQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVnqVCRA9TVsSAnZWagAALg0QAJQab7+9f8Yh6Ql2n2hu\n98gD74j4TS4MAhbkCW0Gylw67y4NE4ImGUtlrfLrOWda58ppzpIKBYGAQntW\nkO0K+ci6PEWLI7KMtmbXVRnpjM3za2huwEXqIZKhSNDxLBuwuLsimDIjkZK/\nL//jVIYPlMdESJ0hzq1FJYjCli4ormyjEXuVKL466bKz5Wjw/Qsyqkjc4L5k\nqIroDXJA6JOAhaNiNiZWhb62h6ujPlmgWv/BBrMZt30ihT5X6r/TCjkElj9U\nS5L5ZFiq/SYqLE2kyHl+IMpfzXZade9caIGAmGsr/XAfv2McRvU2DstVOrhC\nblq245ILEYNn/GUbF8QISbUc7hD2rUqSILRCuLMYd5oOEPombYpn+k+e/pf5\n43Vb5GpOpHQ4kQUfCjp6nKhBLweD1+ly7evT0b1lQJu3+Q0HlTTHeWg9bri2\naCIHeOuho4aWWqQEg0dwn5uNoXIRQtKsRgEAlDMP4MXAiK0/M0v2zSLUlNFc\nMBnzOceXaGMOVXQvKc2gfDqJP/A7Pt/iFU3UcTq17zb3Posp/0/gQ77AGu50\na17PSI3BVEKcKVuLjEw8d/07KhX6TB+GPTlECgYh/En4uSJutA6m/zHm9iYs\ntwKRbHss7kIuiXCPEBL1QxMsnfcot7GJlK2MjL6YoEhd8y4tAONkJCDywauZ\nUM10\r\n=7+2I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}}, "4.1.1": {"name": "enhanced-resolve", "version": "4.1.1", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.5.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"husky": "^1.2.0", "mocha": "^2.3.4", "eslint": "^5.9.0", "should": "^8.0.2", "istanbul": "^0.4.1", "prettier": "^1.15.2", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "2937e2b8066cd0fe7ce0990a98f0d71a35189f66", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.1.1.tgz", "fileCount": 42, "integrity": "sha512-98p2zE+rL7/g/DzMHMTF4zZlCgeVdJ7yr6xzEpJRYwFYrGi9ANdn5DnJURg6RpBkyk60XYDnWIv51VfIhfNGuA==", "signatures": [{"sig": "MEUCIE+f79X9S8X52ZL4X7QJ4Tfk5N5zrUvoVBsrpVL7XmW+AiEAqIi4hPOJgOX8tufVeEcwUnC3hNe0Qz9CzcwYhmggy6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnIJvCRA9TVsSAnZWagAAFXcP/j6cheZ8K/2cwL5IDAm7\nus/Gf6lCOUXEF2T/ytZ33PqAi8ZwuEEImmuPDpT9d839X6qpT2rAVNzsE6B8\nvfhYqVbBR9gV8YDsf1DCSU+iWndxnxwXigCnDgYMwZO1GgrP8mU2jzhlciTr\nUkNU673m86FZ7pIZNSqRdqpjtOwHgptL/rtCNT8EJz0gNYR2oDorweFLmYaC\nGOjBsp3IOpcrWCd4YLbILX89Cid9LTHQk7p7fqKsEy4qFs+R6p287DHJVmn4\nbnf282FFDDexRb6CufOSJYfVE2+4pQNk59b0plEVWJk1Cl1tQJ2wG4fG3Qqy\nJXCHEp5kGKKdTkCVJnbtzyltP1sNk59hDtXr+oYLOvSPR6cnYjA1VppiBpW5\nLJsKK7aYfcwTw+2QjGfV4pejEp3pF4AQKtsU51TFDXt9f1JJyPYGVhK9cq8g\nChhMgdgX18BOSr172e2EsVG10vNkOOpSDWw3oXYyJMcv0APOWwPRHfGA4nJR\n6E4eyYzM7QAeOwffW2Nu7O1D7PhDErui1CsGv9eE5/UamSmE1hGwOyQ/379j\nozuBi6M4Yj3A+HF0+UiQwUnMihF3H+9lSUSkqsRZwASayMayKkSbTs4lpEhW\nxWS0YNOr+dJLc6GFLkvL14vBYqOntJU5AK4szCgklGsTsiLC2xQDzO+PHh5V\nRfwp\r\n=+KJo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "5.0.0-beta.4": {"name": "enhanced-resolve", "version": "5.0.0-beta.4", "dependencies": {"tapable": "^2.0.0-beta.8", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^6.1.4", "eslint": "^5.9.0", "should": "^13.2.3", "prettier": "^1.15.2", "typescript": "^3.5.2", "@types/node": "^10.12.21", "lint-staged": "^8.1.0", "@types/mocha": "^5.2.7", "eslint-plugin-node": "^8.0.0", "eslint-plugin-jsdoc": "^15.2.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "a14a799c098c2c43ec2cd0b718b14a2eadec7301", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.4.tgz", "fileCount": 37, "integrity": "sha512-5/C1ZLbPbiFKM9WnF2LKvwresdLoKb0Py6r9XAt9gojZ6wnJb1ay2OzLY+T0DX5KSrimvTufAGISysArUlRpdQ==", "signatures": [{"sig": "MEUCIAXCy7gS55pGmia+rws3c139Cg7g/OEVIb1WIsXqAXhZAiEA/93OaDNLZbXl1HQtvkuLzE3tpQoPlDDE7pgQgQwvjiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwfDmCRA9TVsSAnZWagAAFj4P/2W9QLx2yMUVSMEZwxII\nSg+ttliE7m7ILJTIee46UX1Qi/r7FfYoGO7P0VZA+HZCABNa/qy6ZDOKCMUh\nTTleFF2MK/BzI3piD7AGW9lb5kxEHVPxFtXP+nlnAv/Yt9yup4GG9h+enD5l\npBcLG1jUhmOcKkJWPuEoGXGCbJZAfVNU3hJd+LWvWCCAB1b0cChNvt1151oU\nZdHpx6RXsNtMPKgZOPsVEP59hLAMp4m5ktcn2yGr3SdaYU5CK6KJ860kgyD1\nVHa19kF8um4MwwmDpnm9Wg3X8Z8TM1+m1C1ZmGi/kGjx70b6BT0JVZrsDxEf\n7dfGeVx11XFXWxbBuxpH3hx/0fUsQPKziHvL+sFO/b0m7XZhHXowjU897beu\nJ79Ut3PnwJ8eq6FtD5cI4k/B3HehddycJvxO2IrSTaUDwLgBPC14fcehZ+FY\n5vt/RFi29da/UIDEupe964RkssM+Qfb5hYMwzXk3wJtOLeAkxYKLvsOQT7iJ\nsaW7y3v1DMis0Iij4gjNYGHrwHft8f+eyC22HwVhrDmqR+wXcgTO9kW4NCo8\nI5CDSmTwaFOoYfyt0eUHUYTSDowVPGzSC582osz9cmsbjc5ctBkQAR0dD6i1\njUe7uKEDeYsGNDEBKE4xru3sJnURnKmj15zCcYm+c5uzTCUeep9STS5C8cTz\nHGqc\r\n=Ov8k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.9.0"}}, "5.0.0-beta.5": {"name": "enhanced-resolve", "version": "5.0.0-beta.5", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "f0747982711bc2d823e768d68f00e421ea5f86ae", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.5.tgz", "fileCount": 41, "integrity": "sha512-v57Kr9URxhxWNhyF6+Inbezz/7jld4m950YrKBTzy1wzRAmNg+40HAMQFijCMw9tWIlXONfaoJGYcDOIpAREEw==", "signatures": [{"sig": "MEYCIQCta5/g86T/qg5XRQVxDklTDudqRGO7Y1Qgga7FafSnQQIhAPDBmG37VUuhbMy9zfn9NI1Tn6lbkomXO4FcEaJlh+wS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeyC9VCRA9TVsSAnZWagAA3nIQAII6WmAx/LsIAI/o8MIP\nDseiRAbSS2Y4N1c2ozSTYjjQkl0gAFqwlgA2gsOagG0VnVYP9leXeYreC89O\npl/yKlYeSKonkz2wb6UEz4zvKVI/tAngAkWldJK66g4kDlFmwrgtuf/rmJLJ\n5Pn+nm4qy2Vz8cASKdYj4Eob41F6S4Q/2aPABQlK2YokLUCfBf9dYvJeFtoM\n6c7CJNdtJ0gz0OAk7qX6AKtoqcZShb+2S7LZglNI+7SjRl/9OrHfluyFGdc1\nbes4VIInNSZuIp2AqDoj3epGuK8VXmSlAILqKZcaLGBVFDd1CPMbZdJdrk5A\nDgD9hmEDKwTJ6OmjWeaCo8uxhkHKGDqcsbOS10UUV0leMYNvFPeYojOZTEzS\nwnQ4e4fuJDDaSiGLRnILwAmod4ebICVTwWhMojPN4KiqCNQ5astuum1ca4MV\nwkN7CPdCzMCM8Xv+46ni+rkz+TxLjCAK5PfH9/jP3O4nQeic8S/eU55lD8Pr\nnVcEl43pWWTe8mHALRq8t72TV+br0CGvt7XbZSvACYVXpI9i8MBhLkahc+fz\nT8NmrxfFLcSt5SlR3iJ6TpyYTVplqwYHJJ3MPlKrgHQZXUyMrxazQ9MlZsg/\n5zGITD+fCALU942cYP58G+fQQSxL6rPV/gvTlVggkbFjfw5iKt8r7g99zSfL\n3X5i\r\n=TOSq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.0.0-beta.6": {"name": "enhanced-resolve", "version": "5.0.0-beta.6", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "1606b86002f34e4eb82e5299df06722efafa6fb4", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.6.tgz", "fileCount": 42, "integrity": "sha512-4LMyFuW56kf7DCOGDVbphVU1JDEaKaiLR/Wlw/SMzCqjS+Lib+3tgRbzuRBr+6QFM8OLQFD1S3N2sIL5kQk8Tg==", "signatures": [{"sig": "MEUCIQDPVbpcrelOjaTpehYtMiOWTScuClM9ukJ/YDM7uWv7nwIgAiZn5q8CaccOEnpBzCl7Z5M11LrcZQnopRo2bWf2UxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe12CXCRA9TVsSAnZWagAAiIMP/i88PbUn+RzDlci3fvsY\nWamZjt0dKYg+TtjDEuj8bNc1EZ+98B3ak/fh5TbBJ6Hpyh2f8/kBFzcxs6ph\nEg+HODbV7YFDXoL+NevrqMIfgY60uXksNQuHVw2gPIWuNYDc1vp76Vxfcc+e\nE4qY80SAYsw6Se8X5iO0QH75Yi8tVCLONUQdTXW8HRT1tvdfh8Xdczc+pBZd\nn7sJ2qhdSOtBW+UGDKyyzRmx2zZoxRqWYVCORLpAb2r7zcHyvWBh5uxDn7+D\npScCUt+yyVKj6ZSSBNSXOxDZmOGkCUqTJN/kVIdaJ5k8F7QxF0SvfqWugnsb\nm7j93VJJERbtF/TYBT4lB9bGpLruCyeVqqi5ncQCysUSr8XLzIWTIfJ670eW\ng5pu2jLlAXrB19/oy2ftNBI2BgLJf/Ixg4HjdHCDWlalDAuXq8skNSZisPq8\nMuPxp6FB2RNd+e++TAMYmTnKrLf6pB9xIdOTbyu7M4OyYYUDrNSfO0/2PJdx\nGqFA1l2vThGEc6PLAfzGQ4rqnUrmyAFB/H9FrBEXnDfH29B6xbjwzltItYwF\nFO8I5zafmgj/Vg3hPDNtgbIhIFVxvfbLS8iyDhLyuCZLF9hBgQaoZwpWfwr8\nHlEbpuoAUPTXPZ4+J3xPJF0UCGw3abhi8L8wwSzpKqatSnsoYuelfTaxFFSL\nLON3\r\n=hOvZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.2.0": {"name": "enhanced-resolve", "version": "4.2.0", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.5.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"husky": "^1.2.0", "mocha": "^2.3.4", "eslint": "^5.9.0", "should": "^8.0.2", "istanbul": "^0.4.1", "prettier": "^1.15.2", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "5d43bda4a0fd447cb0ebbe71bef8deff8805ad0d", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.2.0.tgz", "fileCount": 43, "integrity": "sha512-S7eiFb/erugyd1rLb6mQ3Vuq+EXHv5cpCkNqqIkYkBgN2QdFnyCZzFBleqwGEx4lgNGYij81BWnCrFNK7vxvjQ==", "signatures": [{"sig": "MEUCICHhEMCsISkAcjOuXkNO2BKBCNJen3rwgWjp3laBQml1AiEAz1dKno+gu5DQecvZxIcw20wkjEzmkkY6H5L7HHqmajc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5/OoCRA9TVsSAnZWagAAdgsP/177HvjkAGfhHFSXmuPp\nJW8m509JEdPw/QV6RHJ2T+E4a5bd+1oRATwtQ3kj4057UNXL270OJNz6XttV\nMo4rzeBoMHJ5rw7pIlcEWZp/lFzgQTyeHyeT0KqYUALAmgbmG9xlRcajsS4F\nDlfUUFuYrCCvlqMwQUsFwRxMSCPlnqjNv3p3NM3yVZkt4UwcR4lFLOuUn5cB\nyabjqkxNW8EWCFVJYwyMY3d6HNimJk2Bwl2N7EKR2IlcDNmA7/pIbHjW8ICt\nTTU8ObKBOjNbQ3yawn2gAgXp0fhyxl+JtKlok6qNOtNbAcQzmlGOB6xwt04+\nLvhk0+G5ZTZxy9g6thz9Ez8wRfB/7sUSEmrRVBcpCjc6SlCYMmJuQRznEOFT\nI/hWfm0XCxK/MMzEFPK2yaaeJGA+LzyugwnjMawf4R37GJgIK9yJ3NHrRfvm\n7DDKqmpX7dG6d7HsilItH386nxD00YD/kKSyEufXOL+hdT4iK9Wp38WykX3C\nNU0QU1nF7+/hJDEiX1YpHr92OMkLBpjP3/PqkordPUdZ/yxovA/nCJhUCQOQ\nICmrwtysN661MzaOF3ulhMbxPDphpKcACKFy77MlDhgtroqeZpJ7RL0d6uC9\nG2VAAsHqnRri2QJ+FcmA1+xx6iCbM3UJWC9Hd2E+bB7dbW10U+tfFUzsU8ka\nr25+\r\n=4Tc/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "5.0.0-beta.7": {"name": "enhanced-resolve", "version": "5.0.0-beta.7", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "174f085396ac8edc734253f5b6aa9a05cb0d73b2", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.7.tgz", "fileCount": 42, "integrity": "sha512-4r9mhIEedx7IsNgutSPyFtD0hKukbknr8Fuee36IXg9dYcAeDLb7l6LzBAeiDBgUKeFv+OgMSkCyp/SGCZ5Xag==", "signatures": [{"sig": "MEUCIQDPg0PNYFH7JlzEEukD9opGn52iV7w3ykF4XY7UoCj37AIgW2Neb0Mq74O7+ZWcRGuntZfBYfNeNjI+LRvdx/krkBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6e0/CRA9TVsSAnZWagAAVQAP/A487FmCMLeelZEqZ84h\nDt//mko6RjkxqmkA3E/M1c0BRwcSdpO4jWJW7moajCxsiBYUZ74xItNFfwvt\nQyies6sOwDPZ0vbhC4BD3S/1NEjLKrfjm+iyjj8JND0aOSWWYOqo1GxDKUU8\na9LBhYjaSfTFg8ylHCnybYDdCb8M7bRR4MPBJJQF6p/WrULRj2OKdr8GyaAR\nX8NiURqhBOb5duP2bcO9qvbJNiywBirI9xlBnksA2J29iRpuiHXmlwfjoCPf\n/HjGibS8KRNLkXb1f3YgyD9qmW+W8MVenSEESRc5qSyoD9bwu6iRSBMu7LDh\nao4xnMl41dG/pNcCFTpEK1TQ65jbYKp7lImlu9lY3aA1guA8AHD2HmrWhjl0\nx4MFdy6G+o/Q08X5dA7MHxxWInHyHO4uBIahonTYXLqsU+OGkIKr1Atv4150\nG04AxKbkVHKur0F/akNkqO2RzHOsCcsLuOEOMaCj2UOHuegSIoK7/dyoktJh\np4szF+3prxhx9KUmAtpzew5rn5hfGGVYJcMV4WyIvmsHutfMGowtkaeclm/u\n9xpu9dZdbcLZlEUbjpkKI6HjncJcaNG44LVrCiXwnqrEOtnhnCV8UJJ2TPnP\noEH486T7ayIwIj02bxbwNSfw/caPc8VC5CNWjpaYr47hqQbRITIYPvJcZXvC\nQhnu\r\n=B9as\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.0.0-beta.8": {"name": "enhanced-resolve", "version": "5.0.0-beta.8", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "f399920cb9f73350e61ab10bf2c7f36c37dbf032", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.8.tgz", "fileCount": 43, "integrity": "sha512-6MteIR5h29V8UAsBVXkW7P2cAf+5p/c+Gu79xNCpBPt+hgKcJ0vujcX4vAiMGJjyq3SCHaY5N64C8HXwwRS3gQ==", "signatures": [{"sig": "MEUCICSq2XtinVgC4Te4hyJxyHyPt7ibl8jy5LnT4seiPKPAAiEAp961F+O+icSPJTKaRCWL32JX8yf3THX7nndzCWyR/zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA5EDCRA9TVsSAnZWagAAXkIQAIRis8szB4JBwhasLf47\nay0WI3yvQ5pSUPxcdYE0V0jhhT7rmW3VczMucSrMq5Ik1ThIv+Vnc/yhk+x2\n1qiXCHLDhzsmSstd0JhDvgih8ZvQl9/q/wZc53XVQnuybpXDxEULdbRSglAy\njdCZttfN7IQ2ZvN4/chVsB5arA4CvYhNJ1AJPhBTM1p1XFSFwO+mXkh27vTr\nNJ1ewbLghBo7rxxd/tbsOCUekF6OtSYdHnRPBVH1ghMEIJOl4GVjIY2Ev51O\nv6AgP3xDogyCHPH/fABsbW6CiMn0ItGI6ws8/2N+jV14xPrlm5RT0G7odyWp\nD7/FZ7wO/bKm3yy42flY7Oe2QPAc+1I4cigALeSpxs0Pgc+1d/KzNveyA6hX\nQ48VIiP0fZavDYKELROX6wrObMyfWgMtCJ8vCcvrNUU+rzZcYmN99RBqoaFI\n0jXlvft9XH1euBtsjowl3XN0psv2QC5bAbyD+Y++iZevfnAwiv01bw4R6Ahc\nUcyfViJHu6avSAFnwjHOImWrJAjvvRuba+Uqk6ipLbOitaSy/0s7qEZy1ys6\nzWWGqr/cnq+/XUQ7jskGV8D2j4cdfNx7e+RWWsOAR95mdIFbbS6Q2ZOZ1XKt\nasdQ8LbP+peIQoT/s0BR/3TEEfi5VFr42iOWnSQyjdIxqkCLduK9PfUPDF8L\nPiwO\r\n=86ck\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.3.0": {"name": "enhanced-resolve", "version": "4.3.0", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.5.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"husky": "^1.2.0", "mocha": "^2.3.4", "eslint": "^5.9.0", "should": "^8.0.2", "istanbul": "^0.4.1", "prettier": "^1.15.2", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "3b806f3bfafc1ec7de69551ef93cca46c1704126", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.3.0.tgz", "fileCount": 44, "integrity": "sha512-3e87LvavsdxyoCfGusJnrZ5G8SLPOFeHSNpZI/ATL9a5leXo2k0w6MKnbqhdBad9qTobSfB20Ld7UmgoNbAZkQ==", "signatures": [{"sig": "MEYCIQDFvgJwRKVYQX/+VBEcX73ZfGpM7ydPrWg7seVS7fzWQwIhAOYqgOjbh32SLtq7YflRKjy8ccmNcRQO+xrY5hujs88J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDszuCRA9TVsSAnZWagAA5AoP/0FMLtK1UCcqNODNVpZY\nBrNxKxg69Z2esxPfCucoj5at8YKpWmJ0+geNUU8kbuEiU+4bPvkmACHJHOU0\nXuHvuEYceSmOKKw7lGqefbvvgZ7ex6YlqApD5bR5LhpOEZwJ3PQVqKaasGwl\nQI7il5mH3aZJcl3PWPMDUWql14n2PBMNKjXbfAjHlG7dXUe+ywto0EdkRV7V\ndv8KXFDSzUdlaa7ctTu2lGtDdQnFjk8nTr4K3bIs2T5tb/+h7J8KuBQqpuq+\nzixqudHEmkZNYFz0VNqhhsru/WtqIb/WEKGtcmzSdkRURIDOYFcHMbS9d7xD\nLN6i73GierC/Rg8RvPfQLEwN7jMCMxGsSr9aZoqMTbweDXGFYny08+RnpcRp\nliWOc4rk3hIwOx9jTSjrOQkbHepgRekHfmFLaM1gvanyoCGh94LCl+qbm8mn\nRcon7o88Ed6nfFRpMwY76eGESUbm2hYCjj11vXJoK3kQBrBSeEh/Z1DbuckJ\nfrw8ERpTKafRXD7ZhOr65kBdzU9vsMz42v1A1QndQx0ubDZSUemLZfd2ByGk\nr34iz3OKt3OWqXJuPoU07Bd1GOy6SjtJxOlQnlMoosGaXkm2xagMPDyYMIr5\nnOoFXngja9xPKkFELGfQuorMHAXsW2RCMD6PtqzWHMNSqON9VH//fFgsdXV8\nVG2V\r\n=Ak8O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "5.0.0-beta.9": {"name": "enhanced-resolve", "version": "5.0.0-beta.9", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "a44e9ad9a0cf47591cc996daf0a30d83aaf54c2f", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.9.tgz", "fileCount": 42, "integrity": "sha512-qySAWRlTNigaSuuwqqxwXH9krUSxxCWqcxKalBCaPg7ciZgiiEGc+y6kDsHDg2FvivmV7AuIBdQeX04Jk6ugaA==", "signatures": [{"sig": "MEQCICuoid86qSmjbRgnqsqQLYSag/R1mFReLJTG62eW47kaAiBDX0IHO2hzCtzMfo52nusyCiYdtKts6FdaCGdgtSMQwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEUjLCRA9TVsSAnZWagAAMEkP/1pTVDz3SSGp/gSuKt42\nF3TC9BfHuDB+uMrURJm/AoYJ6cKohOL8toYMqrGMvXiACY/yj9OR6Pv06mio\ngN66XoeKS8pdM4iaTIQLzLWj8hYkwu/1IwO29QhpokMlukZDXIBZkwbszFXE\nFlFR8v0IQbO4TwxKPG3LT7rdV8CngQoz9aPetWf9N8LpI8FOugJLcguzNO4+\nCXsPAxq7iA6hBWTu7fBhgkNc1eoDpSdvmnxmygFzQVHT7spUUjcO2Jgfan3f\nX1N6u+gy0s/1BXqTnI0hh5i+3wz0sfk/3sPRh5KO1fJY9OwEKaKQmSFMnu/A\nLHLK8vemFJzKO7VGGuWeOg6J9IN9yio3290oCV5QgMiSiLUiGGYo89nWU8Be\n0EUU5CJmCx4vL4WpZl72YNxlAAqAviwo0oS2d7JI22MbA8o5Id/w8vLNikaM\nJahoEUs5Zk015erKq8bhw0f+yVRLiDoSEMj6a0LYGTD/OqpEXkJYpv3XOTgA\n5kOkb/W73GaBoaSNESYLf3GPRhCJM06DOE2Li7X6Q3+ja3NDRco0HlVRK3J6\n3J34O1i1y2hxrVq6KcHS5iJFaxx6oTy5P+8kG0q92eLH7zycxdmHpHqW6crL\nJp4ODbYLCX96lBP6PMivw12gDVTVqe3vxZTIMQQEpOXWmshirU8qzgNgJTfj\nY/ws\r\n=sOAj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.0.0-beta.10": {"name": "enhanced-resolve", "version": "5.0.0-beta.10", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "3907c034f8e59446dfa5a89a1fd73db29aa0f246", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.10.tgz", "fileCount": 42, "integrity": "sha512-vEyxvHv3f8xl7i7QmTQ6BqKY32acSPQ4dTZo8WRMtcqTDYH9YyXnDxqXsQqBLvdRHUiwl9nVivESiM1RcrxbKQ==", "signatures": [{"sig": "MEUCIQCx1KmHWV+0pZHUh1auk9SowR1cz5L/1kwY1ahTfMyfDgIgUBYyL+JN4gPdz+hYjI8SVOTNGedCR3XMSkBI8qngnlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEVgKCRA9TVsSAnZWagAAnQUP/iSgdtKQzzFKkTRuRC8k\nDbiGOSL1ndxOjH5Q2F8WwUVAUwBUtFDTSqny0dscOslD627Banfpy28VNtjB\nqgyscOJyjrMr76FP74ax8tWo5v61/oEBGViK5AFR9Kq4jBiR/qM0IBCwgDSI\n/9YTAr1+BuWViJfkBLxdtqrir9h0c5Hz/D0VA2RyjuwcueYo9IwAWKeJbv1M\n2LcAHo9k0xTmAzkmPoA+ttzdhLCY0zlOz6+4IB9X1AKb9WHBh4ZMXsFFu/cc\naFLEjUxt3iIKGuN3WogWA/9IKDvoAeUqssno1laCjI6E43jI753797H4K5+d\nWRveJ9JTuyZo+f0gNUgAZU3K0zPLzilFDFc/UyTLx/oxrX4BebaLGtMpf3gO\nMypyXHKjPAgBeREPVcu1dxyN6FKfb/DjMFeXqlUXcIcLLSmQrvQeNkkX9urr\nTEJAlAqYc/CXKGf4Qg3XxzH95JbNHOtG5XtyJNljZBD6O78XDGJp83NZ4WjZ\n9uqncojLIR6yF/2prDyMJ5E4h1Jdl+JHW5uE3STHLI0IkSVJKFGm4WAJunrz\nYdeHSOwnrSqjskfCSrRGozy44z2sh1NZqcputOqJuFnOORnM+DZCBgMNaO7J\n25jl2i9Rvg33W3UwWt+1A2B1rv1LJ0CdFCwgM8jpbQ9NrzPFGy7F+D0XfuTq\nmkIs\r\n=jXwe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.0.0-beta.11": {"name": "enhanced-resolve", "version": "5.0.0-beta.11", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "f5885b17bca8e04feb42e7f22e8706cae3827e54", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.11.tgz", "fileCount": 44, "integrity": "sha512-kIINkJKz5csnSKnASQh4zdtY3OxVdiZkQZYPteIg6qCHC6L3sy9wcLMXZBUHdQQ2Vv0920DclXqs2jyAI+FQQQ==", "signatures": [{"sig": "MEUCIHWbQ5aGEJnq3bs/7Q5hS1USWTB6016wv3L7x5OaHIgCAiEAp+Ln+MkK9F5sHaqJdV/AJkIXGXWWXa8NqIg6Rc7/b2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149972, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYOC5CRA9TVsSAnZWagAAsx0P/2DGLGEbGLZ6+XUj37FE\nbeX+9/LXGGo8Gvsyw43UvrB/tJvtLwEnXBNJ3+7c6sqgQVGdHTQ5mwG6hEdk\npQjOqElBMTTJoscRVR5+GIyNnk6zYmcaRvZGpltoAzVus8opyNC3dEaGFmJr\nOtBHOL/BmWHyLr+HF3dyNEbn6rzEQy7HCuSsoN7SPDf+GNXQaJciDk67J6MZ\naZKdNtKKkiZgUh/WuzhvN8kZk4gNviAUgumNjcnzav6D+j+snwoi3g+CnYRe\nBgQkNWN//18JP/2r7dtPGtfuvMddW+fnkV7l2EygUHDEXNr2bNCIRsRIkphX\nLYc8ZcmxVJlyf4q81Z5x9TSdQ/W7ImnmGlBNwud1N3SFq1ofiuKH0gL8iV2h\nz4vHbiCP15KpfO93Bg6XvKwSrU07dNi9nNq0xEz8sqzyhNGbWzKvxnBytjjq\npeHtyBGtuVHnvvASzTfPeWFGg2Bw5MicJ49jkIkjErxexH/TLKm4cL3prIiU\n1E7EisMQbDIlh4jz3cEzOe7de+fcBTENfeOFXrC4blP2wywlCi/6lwjBkKiX\nr1KebOgbYvVWKChHzOSTrea9rsorg2DSlGo8cHHQJw4R/FK+Az2Gy/Vqwq8J\ntVI12s810uD10y0yPcH3qaxHIPbr9H/B+ZkAvemHnYJIAM5Wn7w+d3TfxHk2\nnTCA\r\n=F4EN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.0.0-beta.12": {"name": "enhanced-resolve", "version": "5.0.0-beta.12", "dependencies": {"tapable": "^2.0.0-beta.10", "graceful-fs": "^4.2.0"}, "devDependencies": {"nyc": "^14.1.1", "husky": "^1.2.0", "memfs": "^2.15.4", "mocha": "^7.1.0", "eslint": "^6.8.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.6.0", "prettier": "^1.15.2", "typescript": "^3.8.3", "@types/node": "^13.7.7", "lint-staged": "^8.1.0", "@types/mocha": "^7.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-jsdoc": "^22.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2"}, "dist": {"shasum": "ddc2eab710a5f973e1350fd3c0f721df550ec59b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0-beta.12.tgz", "fileCount": 44, "integrity": "sha512-lEjGWKw3CAvoCZcXP2QBf74D7azAqSop2uOSN2nqOiqmy9h4F3Ci2OaGkxfifrnltv7MAtQApWycZOmzGwDovg==", "signatures": [{"sig": "MEQCIDrhagW/Ho1Bsr68qsK7/U2YNRV3FpSJDIp58T4AassDAiAcG1n4vPi5YdLXpKE4SAst9dgjIDOuxZ2B+nd5RTGXCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYaEdCRA9TVsSAnZWagAAHaAQAIZ6yJ+hN2WI0JnCEDT/\nI7khEShiRqrdgxgKB43srTMorCs82zABLhqY19Tv71FlIdnTrK1swqxVtFAS\nKyqoRcsDoPCny+s+vwyUVS4jYcPd2l3iOyvSZfyAoUN8pcquMvIdVLbTEWgw\nmxCy84YsBXrPVYDDXrIr4lxG+Y9KycUenqLP+7ZuOI9PBGWTTfB2Dlt032kW\nzonM5BAXC9l1hMBc44MzdgjEABI2nr8bpnY1c91JlqvAOWWGWmuEwi7bBYp3\nzGAq31gsiWT8Lz2SQ5JdR1tG2410c+j8Dx4nWT+MD9JjAyKKzm60XQrn8g1m\n/lKL87nQZxdjwxUpi96dbjSAm9WC/1o/dnTI0KS+bMCwGnN9zeHrCIJ6ov2S\nxRSnxmT3wowWgW2qRmAcW77Nk2OxbHgWECLvRKb8P4yTyf4ysb+HpOxWu8xA\nGBVnRca+nYIrDjLivJmQgaVACDxse5aStVQDIlwkTVt5U9ywNaaMwYbeB9U3\nTKpWdbteooiJnjxehziPnSjH0Ae8WnJa5o0UlTMmviLn2WbPbkbFVGyHYaGo\nuw4/ANIIm4hldNBcZznAu3ClP6zB8PIY3Op3OkF7YIvNPjjzLNoapeTypRDn\n5qtfJB9UBI3Rl7TBlclXTeKbDwqPxIk2GUmJAnvH32ie7AmVR5K8gydTVtXm\n+Oef\r\n=fguP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.0.0": {"name": "enhanced-resolve", "version": "5.0.0", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "4737e6ebd4f2fd13fe23f4cec9d02146afc2c527", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.0.0.tgz", "fileCount": 44, "integrity": "sha512-6F037vvK16tgLlRgUx6ZEZISMysNvnnk09SILFrx3bNa1UsSLpIXFzWOmtiDxf1ISPAG6/wHBI61PEkeuTLVNA==", "signatures": [{"sig": "MEUCICuBK2a7xjqGcHJIoOwpikSTXZrVs8ZhHpZbTI062QnjAiEAt97tGSfaszp9BHPl5GHCFxm/9+PYd9rTeQk22iGvAVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY/Z+CRA9TVsSAnZWagAALRcP/0QBQECw2BLrvAybAFap\n7EdKGwgC2tScvgm5wTcwfyAxYxgRuh4pACEjU/w8I8LjUtnUPNQP81qhCRwm\nPw4R7qJ672/+MfXWhVCPGSti7b8QmAVUNicNYcmRbY6IUQNeq7sMF11CU2qZ\nXUnzT0ga1UoTDgXe+rOU7b/H+XVpuv7eCWoe4uLHLiB0LBUzgrrkw6pPqI0e\np7tCwJ+TqUX/9XfW3m/9Z4HpagG5HfybdrT22e9v9yIXNNm/Dv+e7Cdb335l\nDvmx4dWcz8EwCoJVxOIVvdYBGKzV/hJbOGZpX8Pjj6yG0xZBsaQX+lzyh1z+\n/2/1A70WN100eQCq8Z+R3zF843gw3mi5Y1FEiarSr5KEOijy6upfA6zu0ODa\nOaiighyLvLWHfPwkjdbps/XEmv8Y+ls5vgsbPTZrUynET+2faNX9hSAReRlT\nHy8v08BeBOBHNhw8iKpe9Bgv8hUHQ0Mex2K3LI3HS3HdHSn2WyCOuoENzOP4\nP4/E/ORBUmQCMrRsoujdiNGmiSLSCaFzbHY4cNsiMuPE0gH90w8lkRFenQk3\nDgE3uwNO3yR3mwstcmT1CZXTfcF3NoHbwCZMs9qWZlfw8PAuZ2/DPJwZYsIh\nDNv9+zFhkWDSCwaSNPb9pTl10JsXg+OlMR+wT2XesZHwmcVPNlhTR0+Ajhjs\nos9z\r\n=vYAG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.1.0": {"name": "enhanced-resolve", "version": "5.1.0", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "44cbf2242876ae9044dc32f7207c23c0dd3acdc1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.1.0.tgz", "fileCount": 44, "integrity": "sha512-EM3ZMRrprkvO44dVdDRGI9pNPY1Vkw15lT/cQk1IwlbcI7Tpc3la8y1FQCuilWQ8qvlq+n19abwPBjVLnld21A==", "signatures": [{"sig": "MEUCIQDWEzAvHAYHm1/Hb9zG99LcSaQ179hv6jIuUshBlMxR8QIgSKzg99KjbN14zY8QxzWT53fyennoV/Tl9FW3ZdMNTw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcikPCRA9TVsSAnZWagAACAsP/3Q1D374m3KM3qkA587O\nscVGTUtGGQPsDLF7/ARFiH/vropHXDzBPUVygre01A7jXo+1VFLRSPkSjH6F\nzh97Nt3+CtjlNB6cB1CCO04nBhnhF0aFXpH8sj8ExpJmD3rpoQZGyTMHoSpa\n6OMIei6sGTcXof4C5hN8IeTBQUzNgL6pws3W4ZVQkWCAHU7gDwGoIftelpSE\nSOXtprjC+ChwOZiEWypeEznnqpbIdTRsDjkQi+Mta0XrHOGlwd4rcYcr6Tly\nNCQtZn43zBmneFaGBP+HANoIvhv9VhpIdqxiRMiIfjORzgyfINEwQ9tS9j/q\nLOo16U0XHRenNTUNsDeB3oH+E88YDVXtxCTss4faWUOjK3ECtbwBMscKtiuu\n29aIcTd31RDziHE9LjN7B56XospdDrdJZRnENlKHIEBfPgoSRhpCyQ9cKVwl\nD6ruKDUik/0RUcPBKvX3KARws7QTq92hkLB1voILwuXs44wBqa+6xQZ27UJd\n0BK4Jo8/LNamhILYfEtitgZgJZWq67ABcWMA6weLK3WYXbtLBaoxpj6Rflnh\nRTfzqAG9Qx2yVpRMVzjKnOPiEBSoI6aon8VqcUC4gU+T0OomRBWIUo7r59R8\npvvsEB5BSvuhM8axoxlNHMvPloZnQqtYvlaV9SPbWfKDzKfjHpSFCLC7gRj4\nxv6N\r\n=DY9F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.2.0": {"name": "enhanced-resolve", "version": "5.2.0", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "3db3307a608f236f33aeea79303d32915792cbab", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.2.0.tgz", "fileCount": 44, "integrity": "sha512-NZlGLl8DxmZoq0uqPPtJfsCAir68uR047+Udsh1FH4+5ydGQdMurn/A430A1BtxASVmMEuS7/XiJ5OxJ9apAzQ==", "signatures": [{"sig": "MEQCIGSPnZ9/NXiEMN5Xr81TA3WwPS7unvcaHx32ao+CfaqJAiBcqUyKRn5TXzTzI1vKxJm81+WF5lxXbr4VnT7EJmAG2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154061, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdDZlCRA9TVsSAnZWagAAIIsP/inIeropmHRnK3qeLVX3\nzW+bG+JxI0L1rSbxrGnKlsk7CIpvRT62YUrUX/rv/sKILRN4OZEjjZm6SRdq\nyAIBFFbIpjf+YFi0WyMR0k0K/1o1L1C9T+t+uHiSHsUs5fooBMU6CDyH1Ss4\n+6y/vrE9/ytufW6U4BdSZs+qe2UP8013ErjRakiAlJIS9AFkcfz0x323vRTU\nVhooCBAYpUOlduFf2RWxbEmiMMGRftgTeAAh4QhZhN1Su7z/GD5bzlcuDX+U\nZJwy7YSIilXEQAnOMCF0aMn+CIq0Pb4VciCWNHsdoIuiIXlsXLx6thinbkN9\nuR4WVugdeWDes6Mwn1IkTVNFj53khzCBrsnF9eFRY0rEVo37dGVT7NSwij1z\nhB+sAZvSrPM8HZZP3Q6q/rBM6HnAQJbFFd1J1l/yLA3bpZxCFiWOaeAn9AIc\n0ZIqAedr+XmDj1+0l3qSCAakoxyGqoyB1lkKR8bpKnxRy5E2RD+FzKDN+rvI\naeGPdYpZ74gX5Wxb50gEZJJj/ZuxZIxTANc3dTFhCZreU2bbW7cnqMQijTQ9\n1WD7fwK6RXs//UVIZy3omRa8SemkoVlTOfODpyZgp4PuWMx78i3vMHH72MmH\nzdZQ7h+bklrqbWXvI6cM5PyyuUQ+3X8Xcj452ECDCmur3PLXfnx0nSTv2pYH\nw/em\r\n=tHRa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.3.0": {"name": "enhanced-resolve", "version": "5.3.0", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "14be504e14ad58e9821a311ea6a9037c716786d9", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.3.0.tgz", "fileCount": 44, "integrity": "sha512-EENz3E701+77g0wfbOITeI8WLPNso2kQNMBIBEi/TH/BEa9YXtS01X7sIEk5XXsfFq1jNkhIpu08hBPH1TRLIQ==", "signatures": [{"sig": "MEUCIHN4s2IW/Cr5YhWuLzlliR6oCgkeNrxltTGIpGJIgMiCAiEA+8uqJVLNq/SCj4sgJvIhTeOG6WWmjgMzUfQ0XVqbGoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjs9qCRA9TVsSAnZWagAA1YEP/jTM9CaCeuNHdX3hcYzK\n8LFKXADtwt0H266kdiwJIj6DzAs+V0Oyw0R/4rZPOC158Kb/dhkWdiuUmVbW\nElacRc453591JNrlxhfKsWXaXwzZtt+WkF0OZuWL37q9Wz9kSLOt5mp7KoAA\ntgvZnEH2K7yr8aHFrWpzFmyz4R68NSibIeoWgWCnRKteFqYFrU6+BQSaZMig\nTWktXEd4L92aYCwv2ukgHig/ZJjyojAJbeCkUjVhCgRfoAjjdwIA7B/cSd+Q\n1rdtwyEtCwFU72Q793Rgo6BjufFidwp7wU6id9CymSoE7lr8neAdR2T9CNXT\nrks8+iWcLqqygMhN7AweENwggQlwPZylXXAAyLlytVaCfALcOTP7BLV41NFe\nU3LZsR93YT8sLNbFDBfhTY2E+kWj51ETMyeL24HPNZ6owjtbKhR49aioThWK\ng2nQC//KKJcKnkcBdcC/lJn3GMi2N1oeD33R6+HKI+OFXONDNfv9lDcQcrdD\nHmJ5Gez/R3hcFMtplFyRBUm6YRCfMWjBWJiVMP4fztisUsLuuNRDD0ymKWKx\nMInKNExJjwkXtFg+UOFTTHF0Aq0yKgKn9vh8f88eXepiUdUXELoeZyD88mRM\nn6Yi1FKMY7+BbwTIZCg3xAHhORCyqEkkUAVlLBrz0UmLLc7deqjVdJ/3cmH2\npssB\r\n=wqvI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.3.1": {"name": "enhanced-resolve", "version": "5.3.1", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "3f988d0d7775bdc2d96ede321dc81f8249492f57", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.3.1.tgz", "fileCount": 44, "integrity": "sha512-G1XD3MRGrGfNcf6Hg0LVZG7GIKcYkbfHa5QMxt1HDUTdYoXH0JR1xXyg+MaKLF73E9A27uWNVxvFivNRYeUB6w==", "signatures": [{"sig": "MEUCIF656Lkg61KglwvS/2p9qg/zmztAR2LRfR/rj9Y8m3WnAiEA2cyXRxHNi8V7iSepkmmk5XoNaoStNzxyXhjZeUUo+a0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfktD5CRA9TVsSAnZWagAABToP/2NxXHHtXlDBrSF8Vhjf\nFEwRNuojyHPeh3HewqY1X/qd0U7Pw9QYwTx0lqqNMtHD82nDFhyn+VY52HeB\n9QiSuGc+g2MNCpDOFKiaW+5dwPD1ZkubSE/4fu9clIVHRcF0PpNRkzGD+qtz\nJgZ32yhbE+Dlpp3M3H8kJqw9Qh+RQdnXWjEiIm87WQ1cbRvaeO9o+EUr9but\nZJkhDDz3kdjbUGCN7nZcj0eje+vwUhB6O6CPtIgqhkGBrGr54jJl7HV0UQ7h\ncG96YlxQjSCti/oIFTwAYzafkndj+Y/Ma8EkyBoueBrKyOA5xvE61KFm/iLb\n+BVpAud1PZY7yh/y9zgb1x48v/MhcMQfK2vaFYLJ1DDOSPV5HlaqX0SZ20b3\nMXMVa5ISgAG/GeJU+U8XoWDYdTFBYFrDX54jOYdxYHC1ZYNQq+pxXA88IQe4\nMs/+MWr9kk+JJaCcQzx0amyjXMqy0P0Iav8RlfZtD/4O0sl+MfwRmzybBFxA\nMyE29umUpXqDvF8KTqLpg/xFyfPMVXhTsXq9mqBUbidRxLQACkqUn/jbyMVK\nqDovST4IW0yNgZqP0WPHJk0kyP5GPP77ey7ypDXVhJ2o2Kco/1DBOp+29tur\nYJbprZK0WlpgHs19tMMLSS/J3vJ3Pr/OcWBbQy2Y4jbJyjXL+jrV9vjttGn2\nWDA0\r\n=bsYl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.3.2": {"name": "enhanced-resolve", "version": "5.3.2", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "142295dda51aaaff049cf256459dc9a82a0b67f3", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.3.2.tgz", "fileCount": 44, "integrity": "sha512-G28GCrglCAH6+EqMN2D+Q2wCUS1O1vVQJBn8ME2I/Api41YBe4vLWWRBOUbwDH7vwzSZdljxwTRVqnf+sm6XqQ==", "signatures": [{"sig": "MEUCIQCyVYqDmAACPBEAWGPi5yjIZqlU9aFniIthaakIytpXPAIgevzioietUuQm15KKIk2ptWpgHfCqNKdNxHMDlRllp80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJft+sTCRA9TVsSAnZWagAAMBUP+wdS9dulosYw9OoiHF7V\nf7QNIwEc1eegyX19E8ZiZd08KJgMdaVc1YW0ng5b+FYr7yirtQHFzu1MdUWX\njhUMfchzUk9MA7rFHwEtn/AN6RtaJ76afP5cHshIPX8X7v0w5k32s1aJj/7M\nRo3lLVn5nBP95JWelCZL2LdArZ0fu7IJEKpRvV5ESe6nBypnKCSn0aHI5h7z\ndOTPKe3eDdBaFrrMvSFJJeHN5kyF6GtzCDhcozoLNmhNAb0CN5Ibx7HHMYN/\nj/XpuFskZlmcGBpZyJ+iyWLfFC8fbwzIEZKvH4Zn2P4Zpl7ewm2nT4YkPqjk\nbiHBVfjZVc5j8f9Byo+iR3EHfMs3kr4TSbfF7vdPVeDJoN06qzSDaTKDq1CT\n03MOGn8hvHqDE9Leurf/4ShwFCUv0Jmo4x+PYCnkSOfwZ6g/gIGBdAZEnBNQ\nmBD6GcyR1qCQt3KwJ/Rk/8QxvMgiKS6cKOQ/uL3oVY7X3J2qCEJy+F0USqlF\nMFawKqbNPoDxsvGeHBv6L18KOE8vzfWrYfg3DZO7kuHkVsXnugVDH8+Ld/uJ\nGRsXwBEXb0es6Rpp6WUr9U7eyVcbeXozKIH4F/D4fcfn85J6XixOcJ21IZvB\n3zMBw9euO6wAOeBEaLiWNYU+MfADJqzhw+EAqjtSh203SVdPl8U6udDYIIEH\ndiYP\r\n=09Fw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.4.0": {"name": "enhanced-resolve", "version": "5.4.0", "dependencies": {"tapable": "^2.0.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.8.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "a8bcf23b00affac9455cf71efd80844f4054f4dc", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.4.0.tgz", "fileCount": 44, "integrity": "sha512-ZmqfWURB2lConOBM1JdCVfPyMRv5RdKWktLXO6123p97ovVm2CLBgw9t5MBj3jJWA6eHyOeIws9iJQoGFR4euQ==", "signatures": [{"sig": "MEUCIQCEeRysgqXUi6yWC+Cq3VyZBEe4i3Bfnv83HlNa3y8mOAIgYS0AB6dy302QoYnmZeEgj/F9fUwwRHYGJ712JfExr9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyBLRCRA9TVsSAnZWagAADsEP/3W0BUyvpdAUL0WGXyPu\nVE9lCd1y7Esdja0h3GjCvZA/4IBUGl749xAlLboLoZ8q8sln9Be6KdO6QXQ/\nigvJwGPDQTs46y6ZmoBTCE6SUANq00FkvDykqPjP5FujuHJ5JpRqBzoFUBon\n6rScTe4YhUOZ0E5wiSuGVSzQPkj3xYQSnzxCcshtNUqIohroDvFTF0puM5hH\ne4910WtscxA6YroUuA4vRActgzaKemYv58qjBfoFWFknyl/vyDBXUO342E7O\n7zTzIG1YVFJRc5RER+U1d9ah1PRhAc+dWKRkcDc2T+0Y/6hJif7Zn7txtsTw\nQO+xILWChqau4nf2p4D2BCazTJRHmozWxBBGyQ9egv8tH9UBdvEu5VbH76et\nIAaQREe3bhA4LxbHF312aYIfEkUrUL520c54ILbsvrVore+UgHVZDoha95yR\nr8g+vY0i0gIE3zE7Go/9oK3YYv6zwRw344AC/yIHv5IT2tfcu7l/+DN+vFhD\neI8kIZfYkYQ8qtDkxcCpOdpm82RwovWnE6gzukk09oCCa+8Z7IHIY6++qLMS\npPLZAv9Xg6a+KOrr5ReQ2tV/BafJs4vvc7QfuSNDfQrF2OSwj07RiIHRph3+\njbXx3aM+YTg4du9VhcnzEH3ywMMRusW0FZzbNViTBPXFM2wjiOYQstw2MIV6\nJ3q8\r\n=tbky\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.4.1": {"name": "enhanced-resolve", "version": "5.4.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.11.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "c89b0c34f17f931902ef2913a125d4b825b49b6f", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.4.1.tgz", "fileCount": 45, "integrity": "sha512-4GbyIMzYktTFoRSmkbgZ1LU+RXwf4AQ8Z+rSuuh1dC8plp0PPeaWvx6+G4hh4KnUJ48VoxKbNyA1QQQIUpXjYA==", "signatures": [{"sig": "MEQCIEOhmMLpHs2+HEhmSNmALcQWlrj8O82HzuuH+An9y5qTAiBpEk7WsBAyYK0N02MsjGSqjBw1HYMZgoBT1AVqYDrY8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4KqxCRA9TVsSAnZWagAA2tkP/jP7BGdytbwsPQp2gAY4\nOSvQ8ONnYNXkEccn6OU+QbgNZTi1sCWB7iWRm7e8faB5orQr3df1SkGnKqcr\n7ZNqe9UGc0TN2LBbYpYCOriTVfVuNySTlbaCAd2cf+AxiVDHoEQkKvtL/jbx\ngMtdUn5FAzjDaHpQ0dlRKVerkCrsPinKuWxIK6Rk/QM1O0jBIJoAy6GXLd5N\nq4IiNU7kB31bTOFucm/L3zF4xR0H0vWN7pY1obRAxh+KgazcDvihw/GFFbtF\ne4lYhu0tux/wcpReBlfb9qHJpUJVkJ4N1cYh/jT+fCf3esYBvr1cJvTsEa4s\n1MFjIxR6aIZrCO1C9TGRY97hE7sbqcmCXuuFlw1MVHvU/wGtm+y790KX/+1U\nmiLzSBivmTkZwLAP8Xne8zcLzesITCtTQfK7/eENPEqh8r441ZgRtxp50s6I\n0815EYdTTZ+LNv5XhmnziBPPUqTZrF7IDKpqDuyin+DGcgVuWzcrFXfbyNTI\nUH9Bn0ylJMVcbJC3mBn+bZPmzyBcg/Kda7SLUlJBZupxJ5QikTd3r7NAtyIN\nyUVyKuD0VjJ6calvFflgkmYO+gFd+wPuDumG5Nsg68sUifM75a3japLlnZ01\nhA/e6Bmb9JqcYwJOCBmqr/3b7dtvGGfVFKQCZU3Lml95N+5WOBYfqB4iNY6s\noHhn\r\n=I/VF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "4.4.0": {"name": "enhanced-resolve", "version": "4.4.0", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.5.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"husky": "^1.2.0", "mocha": "^2.3.4", "eslint": "^5.9.0", "should": "^8.0.2", "istanbul": "^0.4.1", "prettier": "^1.15.2", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "0bcd254f2042d18ab61e937f8ae849b76961c5ec", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.4.0.tgz", "fileCount": 44, "integrity": "sha512-4XqFcmxO11Dxm8yxVr219ifOvHGzbMRZMs1WmZtwYfUWEUh4SN8nJ04l0qHZ/iWwI9ZXxHzjx9IXd+5j/QPqKg==", "signatures": [{"sig": "MEMCH28d2U1WXE65lHTFb2A/WEE2AROSXBDkD1+BZpK8bDoCIAp0gJyO6vtLK1+NxfK49TQBCm1ik0dnuyYfk6mhMjrb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/DqsCRA9TVsSAnZWagAAmrAQAJn2Y7DaybK190QRFN4g\nV794zxEuGzywVyEvG8G3cqOIGLnu1gI8wyD3hEPVWdZUPCE3fmGTmBn9Rj3H\nyYoI/h4QwsVRqcfdmnz/MBbLbGI5NQgxXpHTazAHB3OiSiVpuhR67uHrJ3lY\n8ZLPsORfyRgB02wD5r1i6a1aFhUazKIqeUWssxjETwWpvuOEKnMScVwe7kB6\nQUuefLhotrHSBpKlLwHy9k6axlGw/rlvxPgMXN3wGaIo6BR2EqMT7/hN/doZ\nNLUgwgyCIRcKQeNNG+49X/KfSFGV/TLgUqIwnKXmlg17zqMIO8b9CuFMMYQ3\nRnxaVTTmRcd8j27SftrxJ2F9KVo/UTs3kYs0idi5I3fnEt77cIlyhh26KPcZ\nrl+EDT0dUsbNy0mjgiXWRKfJmEfrqZFYFtkwuQAnRG2d83qSf1hblvIMtljC\nK5TcuWe+Dwrd0zH5cc0RKaaTuiXzmJX0cu/4iuZYrxjLuX2BP5nIXnBFt+if\nj4Mz2aH0Pg31sECcv/VIcXnXvN1iI4ql1MNjKFHGl+6c2N/vYHW+e1FDpoxz\nWivrGlAJp/XgrHFufE2yl432aLBcsrNLkhyLXdu/MNKuvwIAYYFMoxpBSFXO\nnEnY74vwK2yPi0rvLp8S3eqX706kZoLX3pgNUos2Qb957nu72/dSMDU64C9j\ntOjW\r\n=uDhn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "4.5.0": {"name": "enhanced-resolve", "version": "4.5.0", "dependencies": {"tapable": "^1.0.0", "memory-fs": "^0.5.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"husky": "^1.2.0", "mocha": "^2.3.4", "eslint": "^5.9.0", "should": "^8.0.2", "istanbul": "^0.4.1", "prettier": "^1.15.2", "coveralls": "^2.11.6", "codecov.io": "^0.1.6", "lint-staged": "^8.1.0", "eslint-plugin-node": "^8.0.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-prettier": "^3.0.0"}, "dist": {"shasum": "2f3cfd84dbe3b487f18f2db2ef1e064a571ca5ec", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "fileCount": 44, "integrity": "sha512-Nv9m36S/vxpsI+Hc4/ZGRs0n9mXqSWGGq49zxb/cJfPAQMbUtttJAlNPS4AQzaBdw/pKskw5bMbekT/Y7W/Wlg==", "signatures": [{"sig": "MEUCIEC4x2vHG/XU1x7sc8VRww88RQ9P/r1YYnT/8u58yUdLAiEAwHNPGcyBOhlUx1FnsyJirsrgMYBP63Hd3Q/00LzFp6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/Ew7CRA9TVsSAnZWagAAN4kP/RIPlc5XZIy5hmG1/UEM\nU3PDzjp5F8Qorbjlbuy/8rt5uBwFn8Ni5Fat8uBSVO1Zvbekw5Y8InlybHok\n2YV5YLlIxVTNKeF6hmt0HgANKGIyjhb59NASKvKBsTBurYRUaMbY1ik289OQ\nEpcf2XWoJ1fAWnGeyhsfyt79Qiw8nT9gOjJl6XVc98PTg5ApsQZbABmgAa+Q\ny4euNYDTNflmlZcyVziop39DRpQJib2fEIljYB96wISBsjg22HN2sNCuBx40\nuAzJlDE9aQQjPpsSSM4hViEdcU2yLFSsFm6NiZw2lwdfNVY+/XVn/wMX2Bue\nOLJQJdbgtwi24bjmWvvwaM6ebtmat2RQq4f6GeXHXAtCvHXWmKbgjS54fHuL\nkNlXQxCu4n6wCXg9RsMtMtPD1E+jinOejbTLqhNbEPJ8QKxkMRuIquvIveUM\nOevSlIjCiF/MEjSZGfbBF4v8Y2G17pGCWCRvq8wMbeD3M59AMOvlnQz+thSm\nE60T+kq/jISMpNwOS4cB6PB6GHTXgoRZ54xeWecqm0ia8lYWJ5JTsKTvUi/b\n/UOpH846+tyvskKUfGAXdmsh8a2/T3wCusbdpyHOW6mvNuR+Miid3TuDRgs8\n0DWoXpzRguGj243z+nDUB/laEjOwVXmbe48COG1J73pzIUdE/2wnDjMDGbgL\nzNqi\r\n=WQv/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "5.5.0": {"name": "enhanced-resolve", "version": "5.5.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.11.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "5f2ca7d511076541e2a30dc364a40c4f6c027bcd", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.5.0.tgz", "fileCount": 45, "integrity": "sha512-b4a6BasBCoLzri4MdaeOlDMpls2oioI28CF17csMiav9dq46yvQaKPFNUrCHB6VqQokBDG2VIEEL81jMiQ6Wtw==", "signatures": [{"sig": "MEQCIBCNOrIIL81YGqD6zTPvTXT5vt+pV4Dz9p1DOYcSuITvAiAuFTKqbTj5Cha9W7n2ifm4PNb8ygXI5oHQpL5fKI6QxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/FBmCRA9TVsSAnZWagAAzKsP/Rq5vbzqnu1sb6Ku6FCG\nlVpcca1qKlthZQwsoZaYzgkH7KUzxPtH8qiZVyvEppT0pogNMdBUF1DeZVD3\nmENuLCLSdzJi3vJvIgJEOQE1pnJfpZwIAeAfzoMav6+QKGYLQQVF1Y3BagHK\noOE8cRJogF9U+5M9AJD8EJHNyuQfUsXB2dQ6Vw1yddraf1rzwxKR/CybXomV\nAQF+X8qq/qoilcxyRtcUpO0mYjr5WLj1SfX1cezMI8+IrLyJ6Czuns3Xpmgr\nbsHdWOtZSTYdCL24CjObg857skvqpKQqTWWeLvDj2PCAUMgfEkOrYXcOueKv\nP5axNVYvBVm8TwAFtZdaZe4ENQZELQuxwN9l7Ilf3YeaaBfd22jria/rAF9m\nq4RwwxyE+ait7xRiv8aOhDGSY/651aR9aXW7fETrqCdOH+cRBabrPVjclf3d\nlIEL6ALTqeCc1TApxjMS45GYVYt2iW70v9ujMzEs6cbQ3hh0AlGdPBGhtaWi\nCK3OsSjzcrhL37ei1llkbFimmiBhKe1uEw5bgktCuKsCUuQYkupk2YCwJF+a\nrvKQ+G155q2IqCuDxwgUCyxOho30GFJAV9diD5U4SJYGjyoxsCZoNykKBzOq\nKj4QebFz4fYbOMO/vAJTtVdNsiGkw+MOPqWyCw5S1dTLU14rM0UwhxKKN8WE\nlPX0\r\n=2vLQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.6.0": {"name": "enhanced-resolve", "version": "5.6.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.11.0", "prettier": "^2.1.2", "typescript": "^4.0.2", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "ad19a1665f230a6e384724a30acf3f7332b2b3f0", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.6.0.tgz", "fileCount": 45, "integrity": "sha512-C3GGDfFZmqUa21o10YRKbZN60DPl0HyXKXxoEnQMWso9u7KMU23L7CBHfr/rVxORddY/8YQZaU2MZ1ewTS8Pcw==", "signatures": [{"sig": "MEUCIQDAW1gPQ+r5mbeM20oSJRXvfAN5tbuPv4gAV2R/fIh3LAIgQmu3v0w4hatAyQq4gBDhrXyDY0A5yY8xlhEVRSIFFyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/Lr3CRA9TVsSAnZWagAA3aAP/1A94EHvhw4rD7b/NM5X\ned4EBqQUVBz1c6sH+J0/VuciPysMIwYCKTIJ+EEb7wXS2a1Ap5TfjGWJw/4s\nOwS7M3hc7Q3WGXiTr4RMgGYv+0djii+TepQF7KR7PnMw5PBwyakgsKjNFXSa\n7xnl72/lx971+5hUspgNAzNMnyCbtTtTyBPHZ2hIkS4EJrU21pBEfiwQhmxs\nH8do8SRtD3/7rxIoHvt9qODN8uI9AcQjqVqc4hV/Hz4/D5VRbNUmscDFTKYO\n5G+40GyFibeo3wz0davJOKKVLuA0p2WGod1xA37KD1qE2xk2A3Ax0Gvj56SB\n1jwl24PqeLrqlQbUD7ZOM++67pKbV27k7tbZx65JAHenwPDojHCRPsIi9YQn\n744Zq+VtMf3osLNnq65azKC0tzTqDeO+Um8i5XxnbT5tzEEwRWK5r50lPPF9\nif+n6X97N7ITG/dvLQia5bJYcISfb0lGXXMQpAP4nUpjzzXIHrahlVPjMi6v\nUw+xqqMYK0/PU2zpI5nlnrf8g9dn21Hd14fHmdP/9Ep9F6p/3sLKTiVhqSbX\nGRjvx/cmzFeAQoL0BrgwnDzewWKCAF/OXay2uBgkgelfKvCqCYU6Nqj1SGqE\n5ue4BKXjUMT/YtMv4bGFpGGqkHcY4YKA0uZoQ4EuwHtTLnaoXZVpWC9KHnqw\nAkrp\r\n=2cUG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.7.0": {"name": "enhanced-resolve", "version": "5.7.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "525c5d856680fbd5052de453ac83e32049958b5c", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.7.0.tgz", "fileCount": 45, "integrity": "sha512-6njwt/NsZFUKhM6j9U8hzVyD4E4r0x7NQzhTCbcWOJ0IQjNSAoalWmb0AE51Wn+fwan5qVESWi7t2ToBxs9vrw==", "signatures": [{"sig": "MEQCICSgVA8mtW8bB71m2QCzvGmB37YNUfrU00/jyzOph6eNAiAk5EPgtHvEGkRVkQHd2RJFacaB/HK4iIIHHcmV8VRZhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/1pGCRA9TVsSAnZWagAAsP0QAJJcxVL/ia0nKRM48NCr\n+kKNLMNea9OSS4ECMLVD9TnWl/X1y9Kghp0ZK5UM8WjFqOXbbpr2XFwmWXkC\nCP7HoPew+FoCjDwkqt2KV0sOI5O3aIf3R8Ai/x3skXPhldVIG8Who+0vtUlS\nHjhYQieN/eMsmvD/dDT5vYGXcNkYjE6DrtYCmUTITVImyu7ow6dpZ6KF260X\nq4W91jHcn+ReanM/ZN5yhilohuj8pvMW6jEmsKWnjjijAznJnjRF1l6ZqByk\niCSIreob10YUdxDGEAEqrLbSBUPaNXbiJ6nnxynSfk8LLjib0CiVUwq3idN0\neqDIi3cPrKT3y8fjGuQ4Z+S1NT9bqkOS4yR2KTjZ/rUe0Y8jZbakFB1GUXv7\nkuCeVHkjAjhGKnSJLl317C+EEl/WOPMZpGhmzaZAEBi6Z17sNrnuU/wFi4AA\n3HHFiSTVnqbnBR1Hhjv1S8j+i9zqRcl2rDZBnEXdoXQFvxUxyRM91q7sYT45\nFkQHtUpLDnvsc/Wgs64FIIgvhZADnFnNRLUs+yRCvet7W9OH5O8tcXuZRwHo\n4vfN/QskOEdGRoQ+atEBgzx9Ff0RZyu4NwMBbg5Ok96RVBcJfFU3QNWCyMPW\n8uhUQ4/ns3CWzoQJkD3HxxdnQ8yD0v0nVIsuTAwme3v5iBvuuy8OuxHcDolL\nWJ4L\r\n=huX/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.8.0": {"name": "enhanced-resolve", "version": "5.8.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "d9deae58f9d3773b6a111a5a46831da5be5c9ac0", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.0.tgz", "fileCount": 45, "integrity": "sha512-Sl3KRpJA8OpprrtaIswVki3cWPiPKxXuFxJXBp+zNb6s6VwNWwFRUdtmzd2ReUut8n+sCPx7QCtQ7w5wfJhSgQ==", "signatures": [{"sig": "MEUCIBooTWqzHA/DUguqSd7tRwRDh3bqzMOQeSiAwzLxyPXOAiEAwCv4PoVn7LuxK/B53FgJf820Y8QGA18cBQ+Ow5Agj54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfVDXCRA9TVsSAnZWagAAXTAP/i6T46zIt6Uv3JOrxL94\neOcikachzTiKqM6PVMseN2NTVcvc1yBGnGj0lrTISNgNl4b62238C5MN0232\nnDPdX+jG99ZhAgX30D9cBGop1ex6jTLDo9drUTBwTWH2AHXyp9vQhbQgTG31\n3g0F/giJ3qkR6DR2arF/05vVBWcjrBWsL8x9M90kkCmpL3WC9C6JJeINpeQY\nOZ2LiZ7sR1SRaNuOuNjCbzLqyvCZnTaf+RAPUw0f59l653+tUOSDdbJNuqHW\nznogijO/xEN+1jnbefiNJXeronWX5Dv3UA5EQ0QPHfVIN2igXCgM3gdhHiQ3\nZzqBDd4J9iZrPD4/A8ibOQdRUEclEZpkmwOpQWSiNt3HkGHea4NPH+dkUngi\nQ0j7QERGEGGP6rV+NkM4B4BMTAaQgc9Sop9kostcdg3YNB4rfFH9ub7QClCj\n5J/Vdg+o/xXVwIWGwV9Q0/sxUv7wsV+nd9rCfzyX+M2j5bau2OIQQNezupyE\nlK8j8GudR2XfycQhcJYeEztrnpyOerWiBpWGsyMkfGXW98XiYnn5cSybtZL8\nzDOJs23K9l/hy/FSKe5rg50A3CLRc1x/SVYRtY52VjYqf+w8NOd583t0NMl1\no9que5l978dgMJ4m3iCH7PXgD7hn9sDTqwDAQl4EbHanrwon9JVGJ+DxphqO\nx2fe\r\n=L55d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.8.1": {"name": "enhanced-resolve", "version": "5.8.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "3b375599be390e48573138385b9a89a3e316328e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.1.tgz", "fileCount": 45, "integrity": "sha512-TeHbKJI8Yy5M0SNz/QrMtZ0Mx5WYpN+OwglnTOJZkM8oy+K7xoicYRGFOb2ZTj5EYLPY5JMRUTVsiKVLL9vgOw==", "signatures": [{"sig": "MEYCIQCup/ThCwYve8Wih/7BkZBOc/6FjSAQ4mkp+R3nLSHJ+QIhAKlDtKOrLEMoyeTl5ckwExPT5iohw/6/IT8Uj1zt0G/f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmUI9CRA9TVsSAnZWagAAZf8P/A4Kt3ZZ98T4SK0dGbgQ\nlOJj6Ii+3UobdFuQtoVG6L/XIKqxYxYqdDaexYpgXsnsk7NliN61lCOjGTu0\nikhelJperu0vFoochbgXvPoWNwgb6NAQz7boX3Tclrkv+LvQlv9boV6wBZjr\nd1u5VBqMm48uTijDfIpz8vrD7xxaqzmP7t+dGKpmGZ9YOUUq+yySgwdQBPZH\nAEnkjGgGW5vydB6SB0H+6UfOhZG76ebWXrbfkmke+T7xBsDpz5qQ73wqb0uL\nThGEn6LuXN+dP+RINOxHHnWDG7DZeHYy4XgQDrOcs3Me+8OFK3P/VoiIWzUN\nF2HSz1Hxp7+tGhnf83sCMIRw7fkAoD+juYmfSbYpSvGowatgMJWGm0PALYSD\nenxMA9VRcmyEcreNJojdvC7R4EezIu87oZXR1CNaEbBNV+SD90nW0tmok2aL\nUAKlWQiVrBfblrY+Z4mqzlPnEtkWPMPNw58Ti+5qz33ciHwMO9duT3pQZpjb\n/pVe/GrRngH3X0NN7+pligJMmGaIpNByb0BZDLUW8sVGrsMVo9hVBgMQELj8\n/AtALjzIejOAA4G1MydtTbBTt4f5RjQ+voh/kdCHKdZpF6kcFntGLbcJ87TY\ntnQsP9VzQjQNP5IF76zWRIsWgPcTvZeWGb8HNy/BJr4u3+rw0vsBCs2WLcDK\n7h6f\r\n=kKbc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.8.2": {"name": "enhanced-resolve", "version": "5.8.2", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "15ddc779345cbb73e97c611cd00c01c1e7bf4d8b", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.2.tgz", "fileCount": 45, "integrity": "sha512-F27oB3WuHDzvR2DOGNTaYy0D5o0cnrv8TeI482VM4kYgQd/FT9lUQwuNsJ0oOHtBUq7eiW5ytqzp7nBFknL+GA==", "signatures": [{"sig": "MEQCIHh+lVNJ0oEbndITRjllkAO+1GdS08nGezlrDs4gqfeiAiBwQt9/Btw4uZZDjV96ejpUHyV3++gRRxE66SsFprGzsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmVsyCRA9TVsSAnZWagAAVcgP/1pyFTxNZqaadt/wyDdu\nIIiiPPfvPyK+5ATrexV4FbOHPO4C3d/CdUCQV1WBXQzpM0b5jT5TGsWjOBhI\noAHSxJjdkyInQlpC2Ld/e4aO0B8k9Enzycz/t+kM67BpAtn0TNhiJw4ujmGo\nSAZ8JI1QCvh0aB2ONj3SfDh+4w0/auNmWfLh2TBOHVC6RxDIDaEKYkVBOZrn\nZTEddo1zUVP1VXliIKrdWxKuoUO6XewD1clwiAjjI3SaR0bjCEuPFIZjPUCq\nlXdAHXPqaDKyQq9s4fMaG8gWkcoRpEEg5FBgi1kBgo/Q+6BtoFeqr1G2mz2K\ndIphED1tEDd4G8WJCGYpY4xOQB/6dG5D/2PG6dDH3Qv7qLrnmQqS+V0rkGh7\n0aB0Jfb9tNk2chmykOAW/9K2vwFyTRxxV0rl97dgPOY1EM3VSATEvgXCFuUL\nSr4Wdk/mGm4SzMJdb+rbyqEUB/EoVLBMPXdh7FBbpd5BV/UpaAExcZjZ1Jnw\nHSzXPn/47sndoC4DxZlvAPUA7wcKbI+sIBHMJcecd0pcP9RPp8hcn4KolaTQ\nkKYLyKOgb1tGl3NVaqqLj3ghaic2ZAIqOCN53DdIsDF5jqRp22P5DhxvfhCB\nN62x3iLigrpyKFYPr0TrdeOdFD4hIWabXWc3lSUF3O2hKA+8zlYQIPwxoGnc\n3cjd\r\n=TMu2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.8.3": {"name": "enhanced-resolve", "version": "5.8.3", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^4.3.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "6d552d465cce0423f5b3d718511ea53826a7b2f0", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.8.3.tgz", "fileCount": 45, "integrity": "sha512-EGAbGvH7j7Xt2nc0E7D99La1OiEs8LnyimkRgwExpUMScN6O+3x9tIWs7PLQZVNx4YD+00skHXPXi1yQHpAmZA==", "signatures": [{"sig": "MEUCIQCb5tLwT8KEXbC88uG9WhJq2tz8priJFJkB54CzVW8BVwIgOnDVjXcNSl0RP7S2drmP2WR0lUPrx2Px8U9g1j/FFmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2ptMCRA9TVsSAnZWagAAYegP+QHZG/VXZXdtSYIIYYsW\nELeh/SJ2sYnnXPHmciYJJPPqrdTgWimXNOSHH8WpcZm5s7mSchaT9W4u++aG\nDcQwgwxiJsmoRD+xLB7fAOhebutoMzAr1JZcQ/+6igTMuqesWP/neSwxe+jb\nOZAhWXApYdYYwP/Hk/PubEfts1iSePdaQ6SNgMmh04lELasZawB3hzU4WGS4\nHHvi2lgdFiMk/hNdhNfz4EyXx2z1e1SNlIo6jKHwn80QUD+3VpQy0If7aCua\nrYZdIipAx2PtimgNfF3JeZZCrH9P/skA+6qYqmSlxBIJQ2t72cIGnXljCNW/\nD55hb+Sr0NRApvvByZEsF6Wn+IIIEKK4lYzZfFtEDwm+g7PIIhKjjWWaqW/J\nnVh9a1UiSRjBbslUG7ge8MIZQMNvo4X0v1ISqRwWkkdbFC5loTbjtFRQ+kBT\nJjFTjfxIooGgRGWXxCU5r/GVyEQGI1qpgKMOWs8Cx6xkBzLVVpRq8mC3PTeH\nrEsa3mbqPHSgGtRdnxdoPV7z6hu5sv9Ffu5yi6QdxMy0oFMHusZDo5RRFPyY\nNNI7mDbII3NrRj4VfONkVizgmyBzuW7+TrFahAKR2K86GlpXdEHW21Z5qjwb\ngGLvIkIdlpchEvZe7MRWJTcs3ssvbc1CN88PbKWUjNtihcGWzDkwWVvnrTlp\nuLqd\r\n=a5bh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.9.0": {"name": "enhanced-resolve", "version": "5.9.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "49ac24953ac8452ed8fed2ef1340fc8e043667ee", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.9.0.tgz", "fileCount": 46, "integrity": "sha512-weDYmzbBygL7HzGGS26M3hGQx68vehdEg6VUmqSOaFzXExFqlnKuSvsEJCVGQHScS8CQMbrAqftT+AzzHNt/YA==", "signatures": [{"sig": "MEUCIE15qAN2qrm1G9ta2XrJuwlE6GAhlDdHkU09G7CPTFp/AiEA8kV/du2z0qYdfSk6olOdHtkcEPvKIuPx6qiQHUZsNuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAiBFCRA9TVsSAnZWagAAmdAP/2tyxtnm3BE7tNwYujam\nRke/Ax+U8HONMMEaHPjun8a4umBPiK5ZLHEgiXRbGFV/KvQICUwrPPnxgTlD\nBk1hVScaFkhw6Zkq1Hcg+Ym3AMpGTTHjYJVfGtVbHprGsZiNkMKGOSqGCXXc\n3LN9e1Em0dAQ1GDIiDk6hgJuYD/P8KltEamdB12TzxCp1UHEImYqNehH3y9x\nF4zKeWcfqLFE032/44DbkRwE62Sk0QGaxkLG84AWSenyHz3QQsPBkga6115T\nfWjhvCwHClE4BBbkOVZnS6y+EjLM6tfRmLeFN7Nr1FHPgDTK7aMSd0491uOv\nO8OvOVZI7HZAC3JBCA5vRHfvfaO4nPIdUqGZQZBHtAx3Qh3fxXBraTZmQ5Vd\nHxjRmezuj0ydR4IQoUCU9DtYXQZmKiFbmBce32QxUDfWBWsdr9K9KcGWXv0Y\nOb4Eum0IL6KeYumtzH6BQW9Pc1bG8I54cuZ6F/WcVuTT2OAUbmW7l9Lydgdj\n+9MenI7wIEwKVFEBGN9X9aGaeX64t4Jog+dzu6rIJVM5G/Bf2TmmntOEPpmU\nqvVG+ofWn8zz3OYbl6B2S4pOT4xmWUPVIRcXYxa+0NwWnurKTf8AmpU7yZIH\npollFhLYgz02SHW/eDwdbzpybW/MnLBZ4VRgw588tAz6367JfTnXu/4Dxv+o\n6lil\r\n=QqXs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.9.1": {"name": "enhanced-resolve", "version": "5.9.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "e898cea44d9199fd92137496cff5691b910fb43e", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.9.1.tgz", "fileCount": 46, "integrity": "sha512-jdyZMwCQ5Oj4c5+BTnkxPgDZO/BJzh/ADDmKebayyzNwjVX1AFCeGkOfxNx0mHi2+8BKC5VxUYiw3TIvoT7vhw==", "signatures": [{"sig": "MEUCIQCAV+X64/5w7fbe4tFGsUHLtu7x/Oibw6KM8mfVz2Ey+gIgR1RSYI31ANd/xIF2XieRY0k+G9r2JEcnQNgT0L4U/dE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGJlrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIvQ//U7NMxfgP4WM1G+TGcXlFOOsKzPAXehfqTxzDZmTlouX8hR90\r\nwoQ+PSMYZOKDKvJUX0XqBg68XT7N6ZaIGqxlCEDc09xYBDThz4gSKYt/7A0b\r\nFpgRz8Z6PWvAES094B8PDqoDrv80eEfvYGSYnD0YFl6N/zirfjZw3CIWFBp2\r\nDc0rPUrxTzfhNTpPjnivCS7KcoKVqYdRv9xsmTnCV+i4tByVktJ78m3xvfZ5\r\nd7KjJfE7LrDG9bqMbLh1smMjptjeb43XUZQmTAsq26JQbXY45xlessh7B68q\r\n5fd67wRFOhHoNNugDpZBZZhlBrnKSpAqUdqrpu8vrnW2cxtOQAd7pQasum8B\r\ncxAafdjCn1ys6YuhN+RV9saFKoEl8QZUr7sZrVFO/CNRr70cALpCqLzTm2l5\r\nKOVH/yt6kZ9+9e3Xi69EPxmf8WcFVQkjEQ+dqDPqk2B6t9QmFVOKoDEy3rou\r\nI4oi24yDfld+6pZE55NmaMR3DLZdFF1jFfL1Yw0uROH0gjubceTK/R2KrAZS\r\n6eCVr8RvrgcULg8EOe5PmhUM78km+IL7FTdcObn+PXfspuumde25TZKp6rKW\r\ndwdN4lt3uE8HFAugwLvzsPzNKgv0mwudySsFMOnBD5e9XZTrGYXy2i/g2sZf\r\nd14Omz3t1R4ZIdrsorive9CzUlq71QP8b9M=\r\n=BWmt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.9.2": {"name": "enhanced-resolve", "version": "5.9.2", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "0224dcd6a43389ebfb2d55efee517e5466772dd9", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.9.2.tgz", "fileCount": 46, "integrity": "sha512-GIm3fQfwLJ8YZx2smuHpBKkXC1yOk+OBEmKckVyL0i/ea8mqDEykK3ld5dgH1QYPNyT/lIllxV2LULnxCHaHkA==", "signatures": [{"sig": "MEUCIAxmgEco/x1NbcEDGM6kPIcpAOfit+TAGFCjvlWOKTHpAiEAwXFBzkSFADi43FlLvHTg+pIRpl4JcyBWFTlETbRZbEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIHSIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkyxAAoGyB9PVYmT/zDIVhUH/dor7OO95kOMh0uMhu0SRDpRwJNfIs\r\ncq9oIP8zubbL+Gy4ZHy0DWUG7Hp9vtXYmhBLLFVC+0NxfminlQOSQGGGL3tz\r\n5KVU1TL1Renb91SnOt1iwLRiArgKsmGEWvHm1E68h+3XNdEYk5COmOQpjBNq\r\newuEHS3Vyc8Mh4FZ2MizlMbex8iJYiP3IIY/FGpFtxhzxJWiGzG0jRE7dQk2\r\n9VYoA99jVy19bPRuDkFSihjYlxZZ6yOaGkynWQ+PuFMzXCMwFqRGZHvx5Bmy\r\nc+/Zd7OHpxTLyYXNYqcVv6dl2eNkva3fOvsn1Eq6bdRwXmE2T+xaBVx8HiFW\r\nKFjj3gEgoJXv4uogq6GGdo6gidldgVdIahCTwef0unDIlfRmnmzrx2yRP6jP\r\nmBIBNmbzLe4Zftg9SjL7ayQM/2XtAcWOzraAOAnvr47ZJblOlQ/SSu5dBkii\r\n31olEsHCXpgf6o4H+Qj5QsXcPQ1UpS8Y4/Mr6Zq3d5ORjOOIPiXe9lLnD1iF\r\nocwOM6lLyo7U0LRUnjdAWrFUl77PN/HguP0lDZl3fYPfWVqhW9B0KMjjLHZ+\r\nXVR6E6gt65Nc8SHy5uB+srFgDUh0SWII9fMpZpH/pjzs0SWIoXBQ0mfv7hYF\r\nMJgn7BQ4sV4nv6H/xpwqsQwDnrrGIhhmKTI=\r\n=sS1D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.9.3": {"name": "enhanced-resolve", "version": "5.9.3", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "44a342c012cbc473254af5cc6ae20ebd0aae5d88", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.9.3.tgz", "fileCount": 46, "integrity": "sha512-Bq9VSor+kjvW3f9/MiiR4eE3XYgOl7/rS8lnSxbRbF3kS0B2r+Y9w5krBWxZgDxASVZbdYrn5wT4j/Wb0J9qow==", "signatures": [{"sig": "MEYCIQCzhc2n/Xg2tviuKizAWzK1MkJA2olXEDtYTBDY/JauHQIhAMokh2ek8JRo9LxHAhfCFOYC2i98PeQBEDOk/HjS0l1M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVoLHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCrQ//Z2B7uBlkjTAow0cG263xF/ujmsTKdc/F9Nn02nPiplNiRX1O\r\nUWO65HOdNgpMn7P5iDphdIekIeo90eTVm2ImDnIZ+dYLi+bR75KmgLUEYaHq\r\nJbXc7WV3aED7d+u3brIPo/SkBzyhi+hrTmXc1PpLWhGuuMSAjlRkdIk4VCJJ\r\nI+Z4a0TiDvy1RkqRlEN0YUpC55Kg+niJ90/D3qcvAlSVlDv7c11dJ0MGuo9D\r\nvWA5rwCU7z37V8e3r/H/N5UiFxFJYVneC0BLmqeAeN6E7vrvXTYPEEff0kxX\r\nPu9y20pXItZyjOtlNWrEL974TLlyfANuZD5OKb38dgD4Yc6zGIA2wEwOZZO6\r\nmuyrKVUMmYxtXQsvYt7zhm27SPvzZHNljBwrgscJosd7/IZ1xszjAT3+bmfd\r\ntQiWKKVwAg9qBZUoaVrw5zAU1LaKoPbR3ewR01QTolNjNRP9MEwt09oDjj2U\r\nPuGMHkXI2Ma6f0XVIOk5pAY7STw1vhtsUfDr8aOIEMwnjjoRQ//ob6ZbRMD8\r\n8qyg365Xikq3oDldHJ+iZHJglsnHPf1q3lADLoMFbb1u2q+JZy7HAsG/k0lw\r\nmoaDBOJb8R6q4MSWq5bBSZ6E4Q6V+m9j27QPHAqLwTSY3U3BE1/gDvH9kHcU\r\nB0+69RXJxOMdaLgnGxI0V6sINHqkkcflsk0=\r\n=tRZj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.10.0": {"name": "enhanced-resolve", "version": "5.10.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "0dc579c3bb2a1032e357ac45b8f3a6f3ad4fb1e6", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.10.0.tgz", "fileCount": 47, "integrity": "sha512-T0yTFjdpldGY8PmuXXR0PyQ1ufZpEGiHVrp7zHKB7jdR4qlmZHhONVM5AQOAWXuF/w3dnHbEQVrNptJgt7F+cQ==", "signatures": [{"sig": "MEYCIQDBcMuWcqgq0NSvfN9UjcKppfAZxgLR00nDkK3fjwTzcwIhANO5H8h0Lq1nQBTcBTldmW6pET2Lcy4D8qc5yjXQQL4P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiutcoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVyg/+L8KXwYl64N4JTpjcymNdx/ZaespUagGrUVbAi+/DcjC56jS9\r\nsMBZUPJQ03AOPIEPCfmd5/SAnF91haiQCgA8uuHcSw7qeG5R//Jv3jZL+zDe\r\n35q256PKIcTtEKpmp88INO7Q1XN3ju2+Nsp6T7MmkRr3a72r+tl4k7yZYoW8\r\nRDCjMOHzJ11nKSxy/o+EzoCRwm/+t43gaH+qkCjX+k8BsLq0Q1JS1a5f2Jay\r\nQOYUh+pTFwpH/q1pdej/YGA/DT9wxeHNmubtINMIXUJCUCVPDDptUZYGHxDK\r\nF43MuQzCAEqgqi469yOkO6S4l57A1nKab97B7K/Tw8xZ7yHf/Nbck2uGruBe\r\nHDTeO1w0DmMt8sru6vcw0ur/dHWp3/V3/8izXdOeGm2crhMx4lpD8yutFXri\r\np2RIbQAnqnLdlI4+D+GUGL9jJCOyEibkJaI1QrHZvhSJfr9WSLvAdNH2SNxq\r\nSYP3F2NG/f17jVkIaSmVmGwANI8+sXQQIjIy6OBI6Z177AHlZ99voeV/GydX\r\nug34CdLtO8tZV4glGepwRu06UzpMU8bRS1spB8eSwF5tpoXteOxHlNWYsKN9\r\nBTmXxUVCTIn8iIQFka7Ipxb8JIVmnGRsfYxLNyeeRTDc3YRTB40kgSkiG8gh\r\nheL6GclGKnj3EGA3OXbnoFftBEOdJqnGzms=\r\n=TlWu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.11.0": {"name": "enhanced-resolve", "version": "5.11.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.9"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "543cf6c847a85adba0c4a5e2170bded4d493919a", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.11.0.tgz", "fileCount": 47, "integrity": "sha512-0Gcraf7gAJSQoPg+bTSXNhuzAYtXqLc4C011vb8S3B8XUSEkGYNBk20c68X9291VF4vvsCD8SPkr6Mza+DwU+g==", "signatures": [{"sig": "MEQCICuj2DyzoEPkq6A+z3Y0eFz9vbPN/IRqcAoxGU8dyo8lAiA2T2dGluKZbD9TqaD6Fto9m6EZEFmtFBXtu1qQZCbuHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfOTvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmQg//aDXF9BswN0icT3wtN/WRc2aZiQ9TNzMcTP+7tanD+cFzpQPW\r\nzT5uAhV5pcYBZUtxVPwJdBczaQReDSzcnJj4Y4n+pZWyTGvAM2OIy8OAL3Iq\r\nv6bsS9B+3gY2yRpkdo6ePATOKklCjZGTMZjuyyFohXcAFYjS4aDvFANvZ8Bv\r\nVaAufghalo8NwS123YD2CfLsLxWxnFq7Ust8BXTrNEVrjDeKK3k2ZcNTxdEG\r\ncO04iR1M8ZGXJ8gwIMfBP44GY7mB0ydGlyNw14UcpaqAV2I40LARLDP8JV81\r\nfb0AymcVPK8cAooqrP5g9hWKVM49Oz7O313VTupfsdZSWujxQtHK8+Wjkr95\r\nx2nZ5oiYKi/QnClFTP0tty20WrCocfaoOESjNbIYuhUa8lh1YV8TFPuqsK0i\r\n/pjMy2+8evwwNm701CJw+tLGhap9nSYVN9k2jUenLyl/0jBz2udyHvymW7TT\r\nukO8dNaU2dqBLODo2YADQzoI/DoTsZJGMCn1qUs/S9rDtoNcdBh/oQEI7Pj/\r\nk9pdoI0jqqk1NZRcZV/vufRqpqybv/HBVurvxmG2qZyZd3YiSozXpvTG+H4i\r\nkFyJLiGV5nRr3pq6swwOFDiatPE5zFaF7xs4b3e/E4RYH/zOcsPvHR/zCgdw\r\n5WD4q2QYyZynsCA/vNVBheXHYNQ6nM5yOUc=\r\n=gazj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.12.0": {"name": "enhanced-resolve", "version": "5.12.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "300e1c90228f5b570c4d35babf263f6da7155634", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.12.0.tgz", "fileCount": 47, "integrity": "sha512-QHTXI/sZQmko1cbDoNAa3mJ5qhWUUNAq3vR0/YiD379fWQrcfuoX1+HW2S0MTt7XmoPLapdaDKUtelUSPic7hQ==", "signatures": [{"sig": "MEYCIQCWi2LXd3q8bmmpSoVH6YPnAQ5CE0KHDTYybAwX3E8fSAIhAKVH4bRBwFywPPSYcFeSEKdV5Xi0TJkk12PaY3YH5OyA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfmT4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1kRAAoS6211ODsUXj6Naicxb0AjFomfJHCWvFQ2LObiqnKn/Yupf0\r\n7eFzUpj0QN7h+X9DwO8fiT8h0YcP9xg7a8z/91Hne55IyLAASQrapl6+rgUF\r\ntcH58XjkyTEImDA7MjpEKSS3WPlwIWc7KcYBCPtWInIVmd538DGmnOI/A3n3\r\nYJv7+CM2qvxF7DiWI2l/LkfP9Facvvfc5uLk+++O5kdvsoG3SfmuqKHd7Us3\r\nQsaJAj2zIxLF2UiGo2jiNK1+g9fy0HO1/ozfNYMaJmT8Z+FE/zcrAYyLHAzt\r\nH79B3hjGoc5ndfF9Zpt2LzQ5Y6WPuR88+bN1LXnNi5yuGsrkQkRI9BRMTHkF\r\nU9J413euUtB7EfFhOnxwlk4TdNzm4w5uk9ShQsN+Cb2iErANlCTTvHvt/4/4\r\nSRM5fjvfXwazsbsNAaX9f1IysKoYx1niTXF17dRiouyc2PZ4vbUVC75wR8rK\r\nkBDVAAxrdg8JotyflWS/uVsOnTDS//jK2GtbrH9pe2qWGg95hFXTyBeo8E1h\r\nhKwbXZqNiY9SIYRhl3fw4mRCFs+TN/baC0uliFOLZ1iiBeE0iOPQtnP+qqye\r\nPhma/w+9NZOxze9rIh0UnpH3ES1zZbLTZ5n3WACzwDMHXqkmf8P5j2iWigm6\r\n0OCBS9gv4Cyf3a4L4fdwqPcwSmTiLjBqXgc=\r\n=l9bn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.13.0": {"name": "enhanced-resolve", "version": "5.13.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "26d1ecc448c02de997133217b5c1053f34a0a275", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.13.0.tgz", "fileCount": 47, "integrity": "sha512-eyV8f0y1+bzyfh8xAwW/WTSZpLbjhqc4ne9eGSH4Zo2ejdyiNG9pU6mf9DG8a7+Auk6MFTlNOT4Y2y/9k8GKVg==", "signatures": [{"sig": "MEUCIQCDLkScfCgTNF6s4xsurE+3h2Lq6KQxIlDiaOs4kfye3QIgKIPirED4cmZ2iBR26ZO+TjG9v0P26Fz55I/9und2xI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQAJoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTmg//W731wiZ6hUkt9ltNqRqg53boFX0H8U11zEvLkNe2QpyfXPtp\r\nAyLrdWNrCOKlmYH0oTyrX5MUJ6hrNZfY/3KYlkcmKgxRcqKKjF49Cm9banZK\r\nEdL9gX0NkO2YUndE7Qlj+GUrc2cpVfwA9qy9fLHPrOwBHiHT1xm+rUVs6Z7w\r\nN9EiPGcy62It/IYlYYNfeRNrJngWV/TOhwadHn8/uYJfO2md9bjYwrl/bFnl\r\n5suf0WzkNPaGiPmKei7OP4NZNQ8WYTavYx8ZnV+Siw9mzKW1Ub550srV9IgP\r\nZS5SMhXFuK34gcs29wQ8d3whuxvt8B2pVLdHZXA/ta/7tCdaiX74km/lAa3O\r\n/55sc2EdqBI2m6VKE/tbAlA5BRqQi4AGU3JMGjpGoo+6NFlZ99V4D+ROUqOS\r\n71UrFNF6V05MUHWKfvdp/xb2w6EtF1TSAjBb6TGzanoDX3Nt9mxIREJXWsmb\r\nVw5x8V42mcRujdJDu7ghCdmu6Dc1TrENTmhOwU6zCdKhmHvpUZq7R+O8xGbS\r\nm3W+VZyj7S5k/Xlxa3+gL0IQt4r+BjCDyYg3KTHAAygW7/M6LNEjQq7LePqR\r\nuC4kk3RA4R08fy3EYSWV0oThMvhZfJs4Z17YMHqJkmCy21ZLujJ4Zhj6IuQP\r\nVlyh5W/nyhQ+kHmbk0DZHpkAwYu/rOE/Vdg=\r\n=D+y+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13.0"}}, "5.14.0": {"name": "enhanced-resolve", "version": "5.14.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"nyc": "^15.1.0", "husky": "^6.0.0", "memfs": "^3.2.0", "mocha": "^8.1.3", "cspell": "4.2.8", "eslint": "^7.9.0", "should": "^13.2.3", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^4.2.0-beta", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/mocha": "^8.0.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "0b6c676c8a3266c99fa281e4433a706f5c0c61c4", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.14.0.tgz", "fileCount": 47, "integrity": "sha512-+DCows0XNwLDcUhbFJPdlQEVnT2zXlCv7hPxemTz86/O+B/hCQ+mb7ydkPKiflpVraqLPCAfu7lDy+hBXueojw==", "signatures": [{"sig": "MEYCIQCNeh0tJUjr5Vbl+wTfsvmYRt6ylnpiWq6Fw+X+j8IxFAIhAMpiu5DXC+NR/p8cs8nX7raKA05fyB9Ae+o8ObZZQPCe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168642}, "engines": {"node": ">=10.13.0"}}, "5.14.1": {"name": "enhanced-resolve", "version": "5.14.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^5.0.4", "@types/jest": "^27.5.1", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "de684b6803724477a4af5d74ccae5de52c25f6b3", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.14.1.tgz", "fileCount": 47, "integrity": "sha512-Vklwq2vDKtl0y/vtwjSesgJ5MYS7Etuk5txS8VdKL4AOS1aUlD96zqIfsOSLQsdv3xgMRbtkWM8eG9XDfKUPow==", "signatures": [{"sig": "MEMCIBJ2OcpBOvvB+1RhiKqmqgIbnyg+XTQWovhUMcgb4RNRAh9TAQKhPNo4zqZDjmWGGAFUGD4eYMbFeHjXdZkmf85h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187075}, "engines": {"node": ">=10.13.0"}}, "5.15.0": {"name": "enhanced-resolve", "version": "5.15.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^5.0.4", "@types/jest": "^27.5.1", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "1af946c7d93603eb88e9896cee4904dc012e9c35", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.15.0.tgz", "fileCount": 47, "integrity": "sha512-LXYT42KJ7lpIKECr2mAXIaMldcNCh/7E0KBKOu4KSfkHmP+mZmSs+8V5gBAqisWBy0OO4W5Oyys0GO1Y8KtdKg==", "signatures": [{"sig": "MEUCICiYd2kP2R1ZeUurogAl2OE3eNc9/rdI5YwrZPU0KmBDAiEAl82eyLDqjD8v8gW/2gISXDcYMoyrT462AYq6MA68v4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187232}, "engines": {"node": ">=10.13.0"}}, "5.15.1": {"name": "enhanced-resolve", "version": "5.15.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.14.0", "prettier": "^2.1.2", "typescript": "^5.0.4", "@types/jest": "^27.5.1", "@types/node": "^14.11.1", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "384391e025f099e67b4b00bfd7f0906a408214e1", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.15.1.tgz", "fileCount": 48, "integrity": "sha512-3d3JRbwsCLJsYgvb6NuWEG44jjPSOMuS73L/6+7BZuoKm3W+qXnSoIYVHi8dG7Qcg4inAY4jbzkZ7MnskePeDg==", "signatures": [{"sig": "MEUCIQDZqo+2JiI659bFXc/Zl7hjKxDpGHFibx07Br+9fi5AXAIgRevDT22erVa0my5WxvA5Egzrn9n3nzNBA2wQtmjWuUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188726}, "engines": {"node": ">=10.13.0"}}, "5.16.0": {"name": "enhanced-resolve", "version": "5.16.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.23.1", "prettier": "^2.1.2", "typescript": "^5.3.3", "@types/jest": "^27.5.1", "@types/node": "20.9.5", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "65ec88778083056cb32487faa9aef82ed0864787", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.16.0.tgz", "fileCount": 48, "integrity": "sha512-O+QWCviPNSSLAD9Ucn8Awv+poAkqn3T1XY5/N7kR7rQO9yfSGWkYZDwpJ+iKF7B8rxaQKWngSqACpgzeapSyoA==", "signatures": [{"sig": "MEUCIQCx3GGXIMgeSw1DF2nbax5FwIOzM9UuVHkcHhDxlEtL+wIgSpi/fmBPxLSzxixCMNgXc4WbaIZFsYQWzcR+JuYOZTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210424}, "engines": {"node": ">=10.13.0"}}, "5.16.1": {"name": "enhanced-resolve", "version": "5.16.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.23.1", "prettier": "^2.1.2", "typescript": "^5.3.3", "@types/jest": "^27.5.1", "@types/node": "20.9.5", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "e8bc63d51b826d6f1cbc0a150ecb5a8b0c62e567", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.16.1.tgz", "fileCount": 48, "integrity": "sha512-4U5pNsuDl0EhuZpq46M5xPslstkviJuhrdobaRDBk2Jy2KO37FDAJl4lb2KlNabxT0m4MTK2UHNrsAcphE8nyw==", "signatures": [{"sig": "MEUCIQCYwqacXIlHxtttdN2/4ZkCwRLhFrAFl4PZmkqE0O1u4gIgelvuDX54RUzPFNmQEVkIK0AIFKzPOcsW94IeV+T+gNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210372}, "engines": {"node": ">=10.13.0"}}, "5.17.0": {"name": "enhanced-resolve", "version": "5.17.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.23.1", "prettier": "^2.1.2", "typescript": "^5.3.3", "@types/jest": "^27.5.1", "@types/node": "20.9.5", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "d037603789dd9555b89aaec7eb78845c49089bc5", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.17.0.tgz", "fileCount": 48, "integrity": "sha512-dwDPwZL0dmye8Txp2gzFmA6sxALaSvdRDjPH0viLcKrtlOL3tw62nWWweVD1SdILDTJrbrL6tdWVN58Wo6U3eA==", "signatures": [{"sig": "MEUCIEuHJxxvXV9NM2qaRL+HktxXdh72Vo0Gf0j6wVNHa40yAiEAhU/FhrT9VS8i8hq0YvBE3eOALQHy7TqigkQ3nrkEm+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210162}, "engines": {"node": ">=10.13.0"}}, "5.17.1": {"name": "enhanced-resolve", "version": "5.17.1", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.23.1", "prettier": "^2.1.2", "typescript": "^5.3.3", "@types/jest": "^27.5.1", "@types/node": "20.9.5", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "67bfbbcc2f81d511be77d686a90267ef7f898a15", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.17.1.tgz", "fileCount": 48, "integrity": "sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==", "signatures": [{"sig": "MEUCIQDdCUB8z7mJS+r2HUTsl6VS8giiwXs2mn0yKh5LhikbMgIgT60TipgmNltOh+89iP8G3MDUDvc0oa4mbM+LPJ3qjeY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 211733}, "engines": {"node": ">=10.13.0"}}, "5.18.0": {"name": "enhanced-resolve", "version": "5.18.0", "dependencies": {"tapable": "^2.2.0", "graceful-fs": "^4.2.4"}, "devDependencies": {"jest": "^27.5.1", "husky": "^6.0.0", "memfs": "^3.2.0", "cspell": "4.2.8", "eslint": "^7.9.0", "tooling": "github:webpack/tooling#v1.23.1", "prettier": "^2.1.2", "typescript": "^5.3.3", "@types/jest": "^27.5.1", "@types/node": "20.9.5", "lint-staged": "^10.4.0", "@types/graceful-fs": "^4.1.6", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4"}, "dist": {"shasum": "91eb1db193896b9801251eeff1c6980278b1e404", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.0.tgz", "fileCount": 48, "integrity": "sha512-0/r0MySGYG8YqlayBZ6MuCfECmHFdJ5qyPh8s8wa5Hnm6SaFLSK1VYCbj+NKp090Nm1caZhD+QTnmxO7esYGyQ==", "signatures": [{"sig": "MEUCIQDlxlOazzCf6kD/Qs91n/srZtgjDkaezE8zpCeV7KpheQIgHx6E21G8g87DhV49VuYtALAsDICIfwAMeQk3A6aWoXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212397}, "engines": {"node": ">=10.13.0"}}, "5.18.1": {"name": "enhanced-resolve", "version": "5.18.1", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "devDependencies": {"@types/graceful-fs": "^4.1.6", "@types/jest": "^27.5.1", "@types/node": "20.9.5", "cspell": "4.2.8", "eslint": "^7.9.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-jsdoc": "^30.5.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^6.0.0", "jest": "^27.5.1", "lint-staged": "^10.4.0", "memfs": "^3.2.0", "prettier": "^2.1.2", "tooling": "github:webpack/tooling#v1.23.1", "typescript": "^5.3.3"}, "dist": {"integrity": "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==", "shasum": "728ab082f8b7b6836de51f1637aab5d3b9568faf", "tarball": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz", "fileCount": 48, "unpackedSize": 213354, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBJ6z7czSnbGnos1ze1C5fEhn7wRmpkflF7jFe+2lriRAiAuv1OZuzahpGZOhrTTBuSt2k0HEHOkQlydfC7cuJOs/w=="}]}, "engines": {"node": ">=10.13.0"}}}, "modified": "2025-02-04T16:29:43.246Z"}