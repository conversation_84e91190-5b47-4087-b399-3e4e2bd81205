{"name": "is-binary-path", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "is-binary-path", "version": "1.0.0", "dependencies": {"binary-extensions": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "dist": {"shasum": "51a9ab34cc239e8e97d1cb1c874faf25d79d54e5", "tarball": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.0.tgz", "integrity": "sha512-Z18<PERSON>eilayhcNsdCZ5zy2JdOpejbN+OxrAFwtygptAA/2PncqMUrSAsINuufsNDzeWqFTXsnT9nvfIAeJic06kA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICgam1fAP2+oOnq4LemqBZMXJp45cCCxM4BmWYHtDjFWAiEAuzwQTgCCfrC/+15IYHExNwglsGflDzoJJn1C1f9R/FQ="}]}, "engines": {"node": ">=0.10.0"}}, "1.0.1": {"name": "is-binary-path", "version": "1.0.1", "dependencies": {"binary-extensions": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "dist": {"shasum": "75f16642b480f187a711c814161fd3a4a7655898", "tarball": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrp5HpeZRvtnDFsU9fIGr9XUlXrhYkJXwsHjypiDbztAIgc8gdFUWo0mak/oMCeTKNFr3aaBanJKO3zfRu3r59Gps="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "is-binary-path", "version": "2.0.0", "dependencies": {"binary-extensions": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "0e61cea6974b24dda8bcc8366ce58a69265d1a36", "tarball": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.0.0.tgz", "integrity": "sha512-hTfiSaEa9KeCrfbh33630CuItVn5lK4UmCQ70lSN7xKTcGxln2WBQ8UU81z/hNAUyl/EgnTaxZ6/U1Ld6R20PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFLfeTTqs5aFUA25q/Z4Pz8aZykayBP9Au/qZAqjiE1SAiAEGgbXSDQLmm59OkkbPmoxsB3cBjUcf1ZYVIylgAKvyQ=="}]}, "engines": {"node": ">=4"}}, "2.0.1": {"name": "is-binary-path", "version": "2.0.1", "dependencies": {"binary-extensions": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"integrity": "sha512-nqiz8guiosvBdlbkIdKI7io7/1LWXqTz+EKwkEvArNjcQ754E1wKdBbtWtJt/kD1yqp/jr9qIHQpf6BqLzJ7RQ==", "shasum": "a3846f848b804f3adf914eef3e4e4f16045bd778", "tarball": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.0.1.tgz", "fileCount": 4, "unpackedSize": 2825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcm2kZCRA9TVsSAnZWagAAJF4P/0Ny/4chHzPwr81sWOMH\nOjiqXSYdnmI/sXprW9m9kv8+k1CSdR3WXfOW9xEZZAuhXB7lo4jqD0qzXF0H\nTVs+IOyHxynpymlaQnLdn7stFMw4bONknszBgX0MsowrUO7kVvhFl8pr8ovl\nqVCDOw6gp0h9N+WPYu03tN6r/ZpD8IDifM9xzY9dLXW3XgThKEeoIN1pGlnI\nv2Nll2Zan9q3+7m5k3Ombe++upCX0B45WjbYOyEU8JnjDeeeXpJoYu3y9Pia\n72otvw/Ug4Mp/V3kC7vPoPYGmCavy7+9D5gWRju7y9bPGm82EXuJ+17f4eyb\nWESglodLTFF2sWoY3rbpFnmiHdnEdg1myDkUGTbhG+F2t4ScPGkHaV1rooZ6\nhqiOqAWTC5K+qPaOaxjoHQ9Wsb+xezNdqEalX1RXlLN/RzwF/D7ATm0iF3Xn\n4B+hI6JqLfvX7HHe/DGM9E2cy6d6G+i9V/yZuIlWB9zQ4gfHUHfZu0V3XiYC\nvcbTXFsDhTjMtBsrENVH8ba2lDAynm+j+bgMkYDn9CChAvEmjA9++NBA0hxq\nhFBGdB2mzBklCusLI+QdVt41QCWj67TBRQxkaIcnro8u5GaZ+ikydUoGE+qc\n9TbKHhGgHhz2pOEtkiCHnH5vOLHl8eS1YhxGb7qxlTVMd84dkfD6UYrk6034\nXRno\r\n=LsbV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvZC6zj/EZpJjzKCa2XxJkpAvyJ2Z8z2vl9YCfzelirAiEAtIbeLadDIevEy9goxVg/3vHuI5Y6wHFGzSgezXaBsEU="}]}, "engines": {"node": ">=4"}}, "2.1.0": {"name": "is-binary-path", "version": "2.1.0", "dependencies": {"binary-extensions": "^2.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "shasum": "ea1f7f3b80f064236e83470f86c09c254fb45b09", "tarball": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "fileCount": 5, "unpackedSize": 3078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuVHGCRA9TVsSAnZWagAAHJYP/irfvd3+BqqznmABffxg\ndlp7QJa0mBx4VkvLKisN/YYsDCy6favbB4vekifA36+krW/euGG9pDgBox2w\nmihOsa0/WkQ342d1+CUi//entVtm5EsiovpUhpLBKQVF7RTUTR8zjd2PPRAQ\nhx0nYjudHFm/80m3IF7gUXrzponFPr+chhsv+lSJBLs1Rb6nRof8K65yXGa5\nvVUWuNo2EekgvFBn6+IvjFfb6e0nTtt9j165e+Vs9YToaqDBw3bc9RbUNOen\n0mInZbBz51ck9SbsB1eHbG/YesuxOhoazRXwE9rjZbMvX0G+riilAjtsPgrt\nRdHB7wyN7kAdXdGuQ/ATIRgSMH7fCLD5KCT0CVR/0I8epNRTj4/Vf4oDNYyX\nayPm81PG82Jlyz58SwaGSTot5gRr3EVKiUpW+55HhWkOvFbA4K0512dStYst\n8b/U66G6Hd0cwkg7SOZixkdzd2z0ngesoNNzQUvUds8ED3M02WeQeAnBD+WO\nWRlFVRWKd7cdC41ouEjOWW4SZ5KFtzUmO8+UCrR/y5huBtlsyimVixu8dOdW\nGWJzKGBqO+Y6wxg3mNVjnkl1iQSHbJaqlBR7xpnZKg9IIGDYprT/GE3LaM0K\nMTOBla6R8PZp/VYhY+BZg8nJyYniO2E0Qx/WOZMiUBmKiJXllsiqkac1AAdP\ncfPH\r\n=sZyR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxUOyXbcviJbp2Q9ycmzPlknGDfXUlW7BLKyL2pYK38QIhAO+5V3l0Whb+d3WwkLkxwG/raj6GcaAYTb97J2lPtHzo"}]}, "engines": {"node": ">=8"}}, "3.0.0": {"name": "is-binary-path", "version": "3.0.0", "dependencies": {"binary-extensions": "^3.0.0"}, "devDependencies": {"ava": "^6.1.2", "xo": "^0.58.0"}, "dist": {"integrity": "sha512-eSkpSYbqKip82Uw4z0iBK/5KmVzL2pf36kNKRtu6+mKvrow9sqF4w5hocQ9yV5v+9+wzHt620x3B7Wws/8lsGg==", "shasum": "5d1918d62169c2a12d07bb71062c5f3070411779", "tarball": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-3.0.0.tgz", "fileCount": 5, "unpackedSize": 2982, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICSph8KDb3vIe45Plx/ysPniPNjzD6NGrmf7CKX25AIZAiEApSyV+ja6Khv23s3JSgwpb3X24IE+/g+Jfr4ifH69X+M="}]}, "engines": {"node": ">=18.20"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2024-04-30T16:50:26.044Z"}