{"_id": "shallow-clone", "_rev": "13-eb8e3f31d6f67ad1e77d55ee13b8c371", "name": "shallow-clone", "description": "Creates a shallow clone of any JavaScript value.", "dist-tags": {"latest": "3.0.1"}, "versions": {"0.1.0": {"name": "shallow-clone", "description": "Make a shallow copy of an object, array or primitive.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.0", "lazy-cache": "^0.1.0", "mixin-object": "^2.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"related": {"list": ["clone-deep", "is-plain-object", "mixin-object", "mixin-deep", "extend-shallow", "assign-deep"]}}, "_id": "shallow-clone@0.1.0", "_shasum": "4e03c6111a885d8611ddffa635c1ef8052527234", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4e03c6111a885d8611ddffa635c1ef8052527234", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.0.tgz", "integrity": "sha512-RY6LNQvFdswI2wZ5tc9tu3KI+1N0YwrfdIDXqzbcOjSjUz07ik7+YUHDE4S7EZY1/9C1EED+UTEiW5Z8nJt5vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEGWwvqxdHNt5UEYOnDjpZAxGDbes/2NSKTNGzlstIJrAiA/eVlhBdtyLqASEhb5eCNXvq1bf0Kqqyk4Tt2RYTTwyQ=="}]}, "directories": {}}, "0.1.1": {"name": "shallow-clone", "description": "Make a shallow clone of an object, array or primitive.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.0", "lazy-cache": "^0.1.0", "mixin-object": "^2.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"related": {"list": ["clone-deep", "is-plain-object", "mixin-object", "mixin-deep", "extend-shallow", "assign-deep"]}}, "_id": "shallow-clone@0.1.1", "_shasum": "d114e09ff7c3b198e92f764277c28444a219ea1a", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d114e09ff7c3b198e92f764277c28444a219ea1a", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.1.tgz", "integrity": "sha512-edGqnvFjNmrpRpzvGImm7zX3mDLjPn65SEb94O6mMG7vEdAVMTVZ/0qvLae/YYnHfPxdQ3oobnPRJRVa+wC+HA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGSufbBIVJesXbH60jE39c5RRHkPTlUc0bCeZVbps06KAiBCVyR8TdykXPUmFA80xm3Tksa2R7A6DQqQ7eCBi1D0vA=="}]}, "directories": {}}, "0.1.2": {"name": "shallow-clone", "description": "Make a shallow clone of an object, array or primitive.", "version": "0.1.2", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js", "utils.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "mixin-object": "^2.0.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"related": {"list": ["clone-deep", "is-plain-object", "mixin-object", "mixin-deep", "extend-shallow", "assign-deep"]}}, "gitHead": "66fc602ba09fd936241c1c1ffd964274adcd98e5", "_id": "shallow-clone@0.1.2", "_shasum": "5909e874ba77106d73ac414cfec1ffca87d97060", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "0.12.0", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"shasum": "5909e874ba77106d73ac414cfec1ffca87d97060", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.2.tgz", "integrity": "sha512-J1zdXCky5GmNnuauESROVu31MQSnLoYvlyEn6j2Ztk6Q5EHFIhxkMhYcv6vuDzl2XEzoRr856QwzMgWM/TmZgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGqzsyyZhDZTZNQjwdodDILy5MbbQKJSnD7I2vnU+qXzAiAlYuj0AqH3dXiO42lU8FkUPMfa3cu/2B8nkLBagBDn5g=="}]}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "shallow-clone", "description": "Make a shallow clone of an object, array or primitive.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^5.0.0", "mixin-object": "^2.0.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "should": "^11.2.1"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "clone-deep", "extend-shallow", "is-plain-object", "isobject", "kind-of", "mixin-deep", "mixin-object"]}, "lint": {"reflinks": true}}, "gitHead": "20023f3d8b672f57a59d3af9875998ddb4ebef5f", "_id": "shallow-clone@1.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-oeXreoKR/SyNJtRJMAKPDSvd28OqEwG4eR/xc856cRGBII7gX9lvAqDxusPm0846z/w/hWYjI1NpKwJ00NHzRA==", "shasum": "4480cd06e882ef68b2ad88a3ea54832e2c48b571", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNZk3ixT/mza0eGjyuP5ugeGJFzF9X1Zl6YQjgeJGhYwIhAJ22WrLg6VFUj1KE2Mp415mwieg5gkPm12scUJtC74Gw"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shallow-clone-1.0.0.tgz_1500207093497_0.3621308500878513"}, "directories": {}}, "2.0.0": {"name": "shallow-clone", "description": "Make a shallow clone of an object, array or primitive.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^1.0.1", "kind-of": "^6.0.0", "mixin-object": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.2"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "clone-deep", "extend-shallow", "is-plain-object", "isobject", "kind-of", "mixin-deep", "mixin-object"]}, "lint": {"reflinks": true}}, "gitHead": "3fa8cecbf11780495ad04a253963e0a2397396f8", "_id": "shallow-clone@2.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-M0ikAzIEgiQqFG8gkkuM+sOiVVNSza7VjcvP+WfB8HvYccIFDKt0YIwlx0bbLsD4eiya8ARd0rznCbES9fXMqA==", "shasum": "5d0f491e313686b2f53660b8859ca33684f18d20", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBuI0TgAXEk9cmLtzFSRQLRNwHEDBHgytTiZ9KXnv0nwAiBBecRGqhlnyt/LL3QNk/G/pfRxrBO87B5ozWGrcym34Q=="}]}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shallow-clone-2.0.0.tgz_1510895365522_0.4853158735204488"}, "directories": {}}, "2.0.1": {"name": "shallow-clone", "description": "Make a shallow clone of an object, array or primitive.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^1.0.1", "kind-of": "^6.0.2", "mixin-object": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "clone-deep", "extend-shallow", "is-plain-object", "isobject", "kind-of", "mixin-deep", "mixin-object"]}, "lint": {"reflinks": true}}, "gitHead": "bfe3ee5b1cc6cb6fc367ea963a1a483e65559e6e", "_id": "shallow-clone@2.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PII/xIinoENOGpZmTPspQTqSZxfbOMdvGKwGpskewVlFK5YKhe4bHIIGhdsGONZm+N9zMga65YHcn1ZZUyK+/A==", "shasum": "8f33c2b957eb5eada9346b91883184ae1a2395cc", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4tGAv4RVVxs9Og84fOrxxdQVY/Q/m46ctrWcnSg8JNAiEAnZktA60M3DVWqI3NEgRd1ZAhXxuVBrBnoGCRkmyk6jw="}]}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shallow-clone-2.0.1.tgz_1514500323385_0.97433264227584"}, "directories": {}}, "2.0.2": {"name": "shallow-clone", "description": "Make a shallow clone of an object, array or primitive.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"is-extendable": "^1.0.1", "kind-of": "^6.0.2", "mixin-object": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "clone-deep", "extend-shallow", "is-plain-object", "isobject", "kind-of", "mixin-deep", "mixin-object"]}, "lint": {"reflinks": true}}, "gitHead": "2daa71bc08629104af94707af5effabdf5b8e215", "_id": "shallow-clone@2.0.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.1.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2o81AG/RpLTAG/ZXQekPtH/6yTffzKlJ+i6UhtVTtnP6zWQaNo9vt6LI28bhZLSesB12VQSfJYtXopTogVBveg==", "shasum": "7f6e9cf3b64e37d5f4afb0f648a0204da556b872", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwFW9GiqDCXaek5vo7F4e22umsPQ4WR2Ai1xLiykBsNAIgBKm2/NQ1NSrJdQ+V8h2mh8My4FeWtW710vcjk4gAQAw="}]}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shallow-clone-2.0.2.tgz_1514500386393_0.5015493901446462"}, "directories": {}}, "3.0.0": {"name": "shallow-clone", "description": "Creates a shallow clone of any JavaScript value.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^6.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "clone-deep", "is-plain-object", "kind-of"]}, "lint": {"reflinks": true}}, "gitHead": "fa62f9dfa131ba79bab0fe2b09d1e9a53121c48e", "_id": "shallow-clone@3.0.0", "_npmVersion": "5.8.0", "_nodeVersion": "9.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Drg+nOI+ofeuslBf0nulyWLZhK1BZprqNvPJaiB4VvES+9gC6GG+qOVAfuO12zVSgxq9SKevcme7S3uDT6Be8w==", "shasum": "317b701facce5e742d4c04c64e1d52f957e22b28", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.0.tgz", "fileCount": 4, "unpackedSize": 9090, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIClMf11aDHdFdr16USSvmeksTRBy/fdF7pDJYnYFlPpfAiBx18H+BfmFGnWtd+4sMBgTgrvV+8WxAjbh9LWcyfh3MQ=="}]}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shallow-clone_3.0.0_1523342926123_0.141181021651261"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "shallow-clone", "description": "Creates a shallow clone of any JavaScript value.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/shallow-clone", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=8"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^6.0.2"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^6.1.3"}, "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["assign-deep", "clone-deep", "is-plain-object", "kind-of"]}}, "gitHead": "be39af9c1224ba65d57c3da95ab2d396bad3f2b9", "_id": "shallow-clone@3.0.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==", "shasum": "8f2981ad92531f55035b01fb230769a40e02efa3", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz", "fileCount": 4, "unpackedSize": 9446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctRyTCRA9TVsSAnZWagAAK0IP/3bAwWqnsdhLgtowdxJm\n6cycI7dxZ8Lsjd5I3e7mQ77uQv92o4yOX6Lqs822o91qAqUW8+xOhDUuCkhr\n+M8QhZs3/Eo3owZTwej7a6mRsqNEaMA1K2oFp5fDE+B3h4vwJRQ7EiSyv8Be\nCKUz5UZ397J43DAW9LDXfP7L5XeTLHbF9PmDAC5zmJPPeyJERWkqqJLc2iOr\nYrJ9npW0DMXEDNxZ16+SqzcBwtPPkcCDwunJN4bxAgNtv5WdlHEhjAfrFXxe\nYE5liz/qQGP/+gmfIlqTb0kjkSkg5nwhJlXX926Pte39/fzVMU5A/nPzbc27\nlfkJP31YzZHn5G1yH2EcByyqyYCbD4L1FbCKbehUA5cyJW90+u0f3qsWmOl2\n6GrKuN0ezPYW/6rkMCgrGBcfiFnIdcFNhS8+TTZFNqq09grdTV6AS6U38ITH\nYlNSIcIaixh6V2GGWDvh9iw/HWyj2mk0K1WZ1vbgFYC12gij5Hm+8YqZtt10\negMQzWxorftG3EOGN1B0Lv89zN3gXPBDVD/Re/kKRDQ4Fe+mQJpuiwT642Lv\nY+Dkti8B9dUVwJt8MV9dl2o6J3lkHGXzo5qDAQ0l5BG5o/6KKCLKQ9kfKnL+\nk1Qtv3ajjpFwi1ZfIQGbw8KfUCDQK7INse2J4UvoD/ver+Vny2tN+eTcqV3r\n/3+t\r\n=f5v4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqJxRJiC5DKo0M2l1o0PK5vcsVqzxV/UfSwOqXI5vKUAiBHo0Q6wP5/VUp4iiwyBr/pIYMxgdMuQ3u2skcijXqhuQ=="}]}, "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/shallow-clone_3.0.1_1555373202359_0.4682979114888508"}, "_hasShrinkwrap": false}}, "readme": "# shallow-clone [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=W8YFZ425KND68) [![NPM version](https://img.shields.io/npm/v/shallow-clone.svg?style=flat)](https://www.npmjs.com/package/shallow-clone) [![NPM monthly downloads](https://img.shields.io/npm/dm/shallow-clone.svg?style=flat)](https://npmjs.org/package/shallow-clone) [![NPM total downloads](https://img.shields.io/npm/dt/shallow-clone.svg?style=flat)](https://npmjs.org/package/shallow-clone) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/shallow-clone.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/shallow-clone)\n\n> Creates a shallow clone of any JavaScript value.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlink<PERSON>), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save shallow-clone\n```\n\n## Usage\n\n```js\nconst clone = require('shallow-clone');\n```\n\n**Supports**\n\n* array buffers\n* arrays\n* buffers\n* dates\n* errors\n* float32 arrays\n* float64 arrays\n* int16 arrays\n* int32 arrays\n* int8 arrays\n* maps\n* objects\n* primitives\n* regular expressions\n* sets\n* symbols\n* uint16 arrays\n* uint32 arrays\n* uint8 arrays\n* uint8clamped arrays\n\n## Arrays\n\nBy default, only the array itself is cloned (shallow), use [clone-deep](https://github.com/jonschlinkert/clone-deep) if you also need the elements in the array to be cloned.\n\n```js\nconst arr = [{ a: 0 }, { b: 1 }];\nconst foo = clone(arr);\n// foo =>  [{ 'a': 0 }, { 'b': 1 }]\n\n// array is cloned\nassert(actual === expected); // false\n\n// array elements are not\nassert.deepEqual(actual[0], expected[0]); // true\n```\n\n## Objects\n\nOnly the object is shallow cloned, use [clone-deep](https://github.com/jonschlinkert/clone-deep) if you also need the values in the object to be cloned.\n\n```js\nconsole.log(clone({ a: 1, b: 2, c: 3 }));\n//=> {a: 1, b: 2, c: 3 }\n```\n\n## RegExp\n\nClones regular expressions and flags, and preserves the `.lastIndex`.\n\n```js\nconst regex = clone(/foo/g); //=> /foo/g\n// you can manually reset lastIndex if necessary\nregex.lastIndex = 0;\n```\n\n## Primitives\n\nSimply returns primitives unchanged.\n\n```js\nclone(0); //=> 0\nclone('foo'); //=> 'foo'\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [assign-deep](https://www.npmjs.com/package/assign-deep): Deeply assign the values of all enumerable-own-properties and symbols from one or more source objects… [more](https://github.com/jonschlinkert/assign-deep) | [homepage](https://github.com/jonschlinkert/assign-deep \"Deeply assign the values of all enumerable-own-properties and symbols from one or more source objects to a target object. Returns the target object.\")\n* [clone-deep](https://www.npmjs.com/package/clone-deep): Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives. | [homepage](https://github.com/jonschlinkert/clone-deep \"Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.\")\n* [is-plain-object](https://www.npmjs.com/package/is-plain-object): Returns true if an object was created by the `Object` constructor. | [homepage](https://github.com/jonschlinkert/is-plain-object \"Returns true if an object was created by the `Object` constructor.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 20 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 2  | [doowb](https://github.com/doowb) |  \n| 1  | [jakub-g](https://github.com/jakub-g) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 15, 2019._", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T18:34:58.906Z", "created": "2015-08-10T21:01:31.777Z", "0.1.0": "2015-08-10T21:01:31.777Z", "0.1.1": "2015-08-10T21:01:54.541Z", "0.1.2": "2015-10-17T18:04:20.761Z", "1.0.0": "2017-07-16T12:11:34.571Z", "2.0.0": "2017-11-17T05:09:26.500Z", "2.0.1": "2017-12-28T22:32:04.259Z", "2.0.2": "2017-12-28T22:33:07.393Z", "3.0.0": "2018-04-10T06:48:46.179Z", "3.0.1": "2019-04-16T00:06:42.461Z"}, "homepage": "https://github.com/jonschlinkert/shallow-clone", "keywords": ["array", "clone", "copy", "extend", "mixin", "object", "primitive", "shallow"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/shallow-clone.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/shallow-clone/issues"}, "license": "MIT", "readmeFilename": "README.md", "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}]}