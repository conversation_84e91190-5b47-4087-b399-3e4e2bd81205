{"name": "punycode", "dist-tags": {"latest": "2.3.1"}, "versions": {"0.0.1": {"name": "punycode", "version": "0.0.1", "dist": {"shasum": "ae0f52d48d5efcde4a8e02fbdfdc9d679bec0d01", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.0.1.tgz", "integrity": "sha512-4p131gbmlARDXOuRPEa4M6HsMaZy5W8VrNfQ7zI+1A/oZiXvW9hMVfowcLkI952S1lMzbIn+a2OnnsIrnjbO7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICisBtS6j6cVMKBNCdiU/OXbt1zfGuCyPK5y3Hmb9OBDAiEA1OkyQUnseG8M2iucAIcnJfxSbuPQCFR8xHvFTYNaxEw="}]}, "engines": {"npm": "1.0.6", "node": "v0.4.7"}}, "0.0.2": {"name": "punycode", "version": "0.0.2", "dist": {"shasum": "a16bd1e46e5b59276e2e21b6f15086c484f3a6c7", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.0.2.tgz", "integrity": "sha512-v2IWXYnc3Fsorh1AYF+OBrczEK8IrC2AuIJmm+AgQkA1gHtl9r4MD3d0eiHC7cP8Qn5GpyLme3VEx744al2hFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5zwgPpMy8YBkzR9nKj0GOto4XyIiYkIyl/5JopXz1IAiAOnXJkUAxsRaXSjMy6ySIr5JMgtIVRsCdlPaT2jy/ueA=="}]}, "engines": {"npm": "1.0.x", "node": "0.4.x"}}, "0.0.1337": {"name": "punycode", "version": "0.0.1337", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "73a5fcc5e6c89681a9cd9800bfe75cb245fcc15d", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.0.1337.tgz", "integrity": "sha512-KOc4nF4oGehYglj8XlE6dIk0Z4D3dhAFbHDskHgpuegyghr73dp4YOmmt22AkY1ljshPi742LGZZDMS7BIIU6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICtOV6ZqbuuqwthJWvRLTYiw95a4TnBwMOPG1+9QclzqAiEAzmiAbjO1ScZ6LKGJz1lxTDVEE0KzkP58KUtu55mWikI="}]}, "engines": ["node", "rhino"]}, "0.1.0": {"name": "punycode", "version": "0.1.0", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "8f7655c949a9d0adb0e0b61039f8c9d9ac3e12ac", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.1.0.tgz", "integrity": "sha512-roeg8KPiCXpHfvfweMwv1HhP7W6Mv+y+zTZEmDUHCPWO9dtMPTZ0pQ+tNPDW5t07XRmJmEdR793pc4PAnbFvBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5EXj0Rplkhj/SV0u5HYMTAjKYfIr4CFNR0yiEwRMVEAIhALzMYmYL/UDO52fLQrTCO+b1vB0N3dJn/bGgFaDSb/ku"}]}, "engines": ["node", "rhino"]}, "0.1.1": {"name": "punycode", "version": "0.1.1", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "35be18fe2e00e034c6815ad763534959595ae5e7", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.1.1.tgz", "integrity": "sha512-Hrm32Ojxzuwrhs9A5X+wiyljnotRxlGjqaEs4HOQYmXn2IeRbPVv6ZaYYKSXhh12BDhvXKgdwqSGEQrT9pXtdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEW0hN7ozDM8soJFTnGhl7gzq550LG+iWX/bozmwAP6jAiAG4CVW+A8jYEqwsza69jWLKaRhCZPjTe4qFsqeu1+rHw=="}]}, "engines": ["node", "rhino"]}, "0.1.2": {"name": "punycode", "version": "0.1.2", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "bce2172b2307c51d58b95bf02e9b033e8a12982f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.1.2.tgz", "integrity": "sha512-o0ply/kNxdtIwyDbMgi2HeBGAWdzYmX3rz74A1zN2+ua0B37dsZkNMkY+cWJXQgdfQMLfGMor9eyLNZ/Urftdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcAWzBtobqyE2CdEui4TiHVoOI0a9N8r/Hb9oAzaRLmAIhAJIarbMs2mIzbEdjHHVQe18rkBluesko9U/0NSvEWdmP"}]}, "engines": ["node", "rhino"]}, "0.2.0": {"name": "punycode", "version": "0.2.0", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "e7cc94740eb902bc9f791a8a598078a481ca677a", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.2.0.tgz", "integrity": "sha512-hhiwybWtWoUSSa0rAto22Q66b8FMibm42i/aJy0dcKePnVz6raqYcfF1o0EK1Pk9qADQiU9UyJ5tUdZhjO4xIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFRlV//2l/4LU9UiYFWRyuIEAVusUMbjkyIL1dzAGK8AIgBch79p0rR5Tl/9O1q0n7VkmDSMhX1QEmNWr1SIvBUUw="}]}, "engines": ["node", "rhino"]}, "0.2.1": {"name": "punycode", "version": "0.2.1", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "c52e2d332170d0fb45f853f3b4e718b00245e167", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.2.1.tgz", "integrity": "sha512-BmYVxr5C22UZVBB/1Kjt8EHKY1XUP2HjdIj/SUlDcCTCrKyMls0hQy5K7iQzsJml3JEVfhkgv65z3tgVS8DGxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFPOl8c9br+/nUwbbM2UorTMHxUaa7TerGYOvYtXiKCAiA0/HF62hLr4lqhHSJ0SMhEqLWrL83/SAY7uhFe829pgA=="}]}, "engines": ["node", "rhino"]}, "0.2.2": {"name": "punycode", "version": "0.2.2", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "b7bf8403ef5a61a5860a5755dbda0a176d6a3be1", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.2.2.tgz", "integrity": "sha512-Pp/Vkhq+bbdUonJ20LSb2RT++T5zeo2kkVc8rP+S0RNYlP1YqZYOkKuoie6JzUazLD1BRk/WFJCJV2iJ4l4gQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFk1fPaX4ZhHuDJ2zIAlz4lxnVMrx5DC4UZ2zn8iWgUVAiBr45FB6g6lLVSsIx4W6sa5Abd3Ko2e1gVfOLAM+BUdDA=="}]}, "engines": ["node", "rhino"]}, "0.3.0": {"name": "punycode", "version": "0.3.0", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "3e6d6f2e2ca3dce4364c66ec971823b228302d6f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.3.0.tgz", "integrity": "sha512-gdxRpQMsEVATGrfqYplbrHrKLdVBcuW1JRl+6RO3gy0t461ZN9JpHoLMMqfWwwnXry4p42UJDm3H48+agHNn+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRkGt6TUz3H9u7FNGEwgsfaCHBBQhEBVdDk45cGgxK3gIgEtLCZyl5jcCvgVUYmorVjrX474js9BNfameMolxSN6Y="}]}, "engines": ["node", "rhino"]}, "1.0.0": {"name": "punycode", "version": "1.0.0", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "ce9e6c6e9c1db5827174fceb12ff4938700a1bd3", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.0.0.tgz", "integrity": "sha512-H/lCVQ35bWuDIVJyWvEsoBokpjJX0OF1eeQzfc/JoNj8seA0tPU599UOGtlCJmP2oukCejCzOBUlvIVXePk6pA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICHgfd8IlZcVrozplC6GyT9MkE9jDcUd6KmZFKrXumd4AiAeJjcBU6BluOOWzF2NiVsqbFSpax49E3v93pzc2C9DdQ=="}]}, "engines": ["node", "rhino"]}, "1.1.0": {"name": "punycode", "version": "1.1.0", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "6b091fb61ee64128ef9fa18780e74e1e7ba0217f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.1.0.tgz", "integrity": "sha512-AKZuQh5doUm8PLa/g2TG+eCo6S96KJqXR+uAmohMNJnGHvvlzSH+HJRlaUjbfmm58KnkhzH9pKULyPOhJ28tsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHfT9ncjIKI0ImXIM8NWwj8zb0cwyxDnT2bvuHS/ZKOQIhAJBzH8qMZbX0NEiC78wCYrTAL0lNTWYiGwEbUvty6b66"}]}, "engines": ["node", "rhino"]}, "1.1.1": {"name": "punycode", "version": "1.1.1", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "42b3ceab84d3c9c1da4d5a43f59eddbf54c60678", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.1.1.tgz", "integrity": "sha512-RRkhKrDNJO9rXHJkMGdDfEWCCWQL/228CI4HRDkuvWk+QUr+cXPV53hBUOl9iLXPTWpRUfrfh8W8kmIhal8qoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3vBqAdwnKp/yDhycer22sSMiL4tvsELAMTHR8DM8NCAiEAwGrWW/k/qzWbGGeNgUs+RD6xlZh3K394fSdiBTuCEqQ="}]}, "engines": ["node", "rhino"]}, "1.2.0": {"name": "punycode", "version": "1.2.0", "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "aee66ec448ebc5c45849af628485ac05c324b9c2", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.0.tgz", "integrity": "sha512-GaFXpycxJqrtLzJNWJaRkqH+NtV5PDB9pb2kjwzI2vbxqugbsgEXFJ7GQcBSkhSigaLR+x04Pwhz0t/o69TGWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCHyLLVToqZ6/20yreyn1KNGxPEN/S6M4pwKofe+4ok68CIG9vgeYmDK/34yu55JEBF+5LQuOCvT8ayzwM6wUum6Bi"}]}, "engines": ["node", "rhino"]}, "1.2.1": {"name": "punycode", "version": "1.2.1", "devDependencies": {"istanbul": "~0.1.33", "grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.0", "grunt-shell": "~0.2.1", "qunitjs": "~1.11.0", "qunit-clib": "~1.3.0", "requirejs": "~2.1.5"}, "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "90047bc2e6dbbf757d281b25af69b0a773df9cef", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.1.tgz", "integrity": "sha512-DGJc5D3affPS+Akvn2HvndCJ9qykDE7UbNdW5/JZQDY0YrzR4Hfd0APClGX6q1rHFqi40/QVK5Ppd/2ZBH5bnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHwRkbVYwIWP505CRIrbtQe08YQx70Wau0J906ARnjGwIhAO5bpjbAFZTfY2vF25VeTYc0DiqbqfB61oZKasnHAnpv"}]}, "engines": ["node", "rhino"]}, "1.2.2": {"name": "punycode", "version": "1.2.2", "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "a7727afa42fc80a4bc19f7fbecde6ecec7e5a2c4", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.2.tgz", "integrity": "sha512-7fnS1RnGR2BNSDPQBvphDpFLPJcjHMluTm+KJ24Bz0eGH1CUppZ0sfWYaA9nwISNMbVdCtT/HTlarY3RUkTU+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjwCgc1fTCL3DeTkOfpqEuLsaqEP8cW/akR9AiyWGxNwIhAK1akTlfKs9B1i2Bhm+QAVdznglp6TTL3HUG9MFsQdxP"}]}, "engines": ["node", "rhino"]}, "1.2.3": {"name": "punycode", "version": "1.2.3", "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.2.2", "istanbul": "~0.1.37", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "b4e304471082d783c73b3bafabf2fd9b6a486266", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.3.tgz", "integrity": "sha512-fw7/4wZQN3YCxJlsaVgVSjm0EP15jeO+YwlO76qt+qJu9qhtyyqUYQHWIVen6xfG8F9D+yS497xGo6mwB3UtkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRDf/Kh9arxdRySCkAds1F/Rn2yvMcZ7qE37mvcF4peAIhAPc8zL9xCuwDNRj/OpQ+bGxBrcPC0xa/1k+naVAnTnvc"}]}, "engines": ["node", "rhino"]}, "1.2.4": {"name": "punycode", "version": "1.2.4", "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.6.4", "istanbul": "~0.2.4", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "directories": {"doc": "docs", "test": "tests"}, "dist": {"shasum": "54008ac972aec74175def9cba6df7fa9d3918740", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.4.tgz", "integrity": "sha512-h/vscxLPvI2l7k/0dFUKZ5I5TgMCJ/Pl+J6rw77PDuQM6UApf/GaRVkjv/YSm2k+fbp7Yw8dxsoe29DolT7h7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXN3txqow0bNcMJ5LTVCxc4NxnBV9cmSlAMZvxQfXYvwIhALq3vAhVwESJ48RZ0UGMbTr/ac+dvDe2ju5QV9NnlJ/i"}]}, "engines": ["node", "rhino"]}, "1.3.0": {"name": "punycode", "version": "1.3.0", "devDependencies": {"coveralls": "^2.10.1", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.5.0", "grunt-shell": "^0.7.0", "istanbul": "^0.2.13", "qunit-extras": "^1.2.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.14"}, "directories": {"test": "tests"}, "dist": {"shasum": "7f5009ef539b9444be5c7a19abd2c3ca49e1731c", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.3.0.tgz", "integrity": "sha512-dQN3nR/bArA9w2a4MdkxuRtnIL5nmKh5YDnD2FAN9tc5OSTMZImoS42dI4CneIJmKECLpAriSojg5nZlqbygoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIt9KD65POi/aLnIT5qNLnyUGLpCE6bUqzGX04QIucPgIhAPtiWnqM79GmefTPKLUYykHYI5cNHpxLTMcZ0ckBp9aA"}]}}, "1.3.1": {"name": "punycode", "version": "1.3.1", "devDependencies": {"coveralls": "^2.10.1", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.5.0", "grunt-shell": "^0.7.0", "istanbul": "^0.2.13", "qunit-extras": "^1.2.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.14"}, "directories": {"test": "tests"}, "dist": {"shasum": "710afe5123c20a1530b712e3e682b9118fe8058e", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.3.1.tgz", "integrity": "sha512-08OSO5WGhNXz0UMydTAJdMkJ57T0gpq9Y8smtppxDKCCc6ozrN+hWmWKfgZtQYE8JtUZa7HWyQ3kk8xUhH7w5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9zOff14gwVK8/0SUwXax3cNDktGmszk2TFBnCxnF9PgIgAe8YEwd8239TdDIFtg+NqnCXDK9cSNrqJc+a5+lNcIA="}]}}, "1.3.2": {"name": "punycode", "version": "1.3.2", "devDependencies": {"coveralls": "^2.10.1", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.5.0", "grunt-shell": "^0.7.0", "istanbul": "^0.2.13", "qunit-extras": "^1.2.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.14"}, "dist": {"shasum": "9653a036fb7c1ee42342f2325cceefea3926c48d", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUtVYz5j518Awum8gq6SMxCoBRy1/pZYSQpgqwT3imLwIgPKWQIgwXSHtJwUOYqUN+zXQ0V4G42H6k9/1UJsQdEyg="}]}}, "1.4.0": {"name": "punycode", "version": "1.4.0", "devDependencies": {"coveralls": "^2.11.4", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.11.0", "grunt-shell": "^1.1.2", "istanbul": "^0.4.1", "qunit-extras": "^1.4.4", "qunitjs": "~1.11.0", "requirejs": "^2.1.22"}, "dist": {"shasum": "3f879ea03f24c718d4d4b7e47de1fb51cf6c3e33", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.4.0.tgz", "integrity": "sha512-2f5mYw3Iqt8BVEvQV1c9RaaSQ9VqEjTPQpens4G9/+VNWjRFu/7ahnB3/qjiXfcbQ8PVXE4DVJXkgyhHqH9aeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5tI439C0W5PU9YqY1yW1hAxcAVlzbQCAS0kys/CLNyAIgZ+RBukk1CcY6xupAx4WHNQwep6ISvLUG+5YEt1jPDfs="}]}}, "1.4.1": {"name": "punycode", "version": "1.4.1", "devDependencies": {"coveralls": "^2.11.4", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.11.0", "grunt-shell": "^1.1.2", "istanbul": "^0.4.1", "qunit-extras": "^1.4.4", "qunitjs": "~1.11.0", "requirejs": "^2.1.22"}, "dist": {"shasum": "c0d5a63b2718800ad8e1eb0fa5269c84dd41845e", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvBwsAIkRTowoxRYNpn856NGuWz6ev3f4CLaUqAjgxkQIhAL2qaZ7H532zvmWlQOGIBgQqEAsim27EBf6tMv0jlFIx"}]}}, "2.0.0": {"name": "punycode", "version": "2.0.0", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "dist": {"shasum": "9145b207b5228410ca17a10fe1cf4ba2c015f6d7", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.0.0.tgz", "integrity": "sha512-oPNQZ+HNC8wo+rGM6ojbqVaONYfqwphn4QrZJyqZCrPw1lxcKhafaMTYz9dGgY27tfsp/I2+s+zcsuWG8LP/WQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAg9V79f1lvg6HbksVSEiEqfbqKIVUsfZ7DyxLoQ1NtSAiAprBBL6TyQf5p+x6UBNGETrv85KuKJrTFul8v9EQPy5A=="}]}, "engines": {"node": ">=6"}}, "2.0.1": {"name": "punycode", "version": "2.0.1", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "dist": {"shasum": "3f142fd8e6ef4e9ce24acbf7ba869ff9b00d2c2b", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.0.1.tgz", "integrity": "sha512-xFK4f2VCQ/FaO3X3Xq5V+d/Cw+kiZAUqoMkp1khOkmrky+sFhgBJn5Pn3Jp57st0qyAmUaWGDxVxiDBnHoj93w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjNQ9PhA4VlDNj9O9Q8D2kK++jLMMzou7I2wt/AxBtEgIgAqib2wcMtEM99dvpwoBxgY86uwHMpJ2OHCziWHEzs84="}]}, "engines": {"node": ">=6"}}, "2.1.0": {"name": "punycode", "version": "2.1.0", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "dist": {"shasum": "5f863edc89b96db09074bad7947bf09056ca4e7d", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.1.0.tgz", "integrity": "sha512-Yxz2kRwT90aPiWEMHVYnEf4+rhwF1tBmmZ4KepCP+Wkium9JxtWnUm1nqGwpiAHr/tnTSeHqr3wb++jgSkXjhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICpb1wgiLZ9yOoMRTMCFCn1L5N49ZXdl5FuF8KAIBGSGAiEAmuwJdXQ8M3nGgjA5zIv8ZzoRF1EEumL8V9aJea0eZIE="}]}, "engines": {"node": ">=6"}}, "2.1.1": {"name": "punycode", "version": "2.1.1", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "dist": {"integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==", "shasum": "b58b010ac40c22c5657616c8d2c2c02c7bf479ec", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "fileCount": 5, "unpackedSize": 32434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbA4SZCRA9TVsSAnZWagAA5ZYP/0UOrnGue5Uhy1In5lOe\nh8pglPP9+qBCRx+nfe+YgmPV45IdYadH6InqCibkxAAeZ+j+Sc0g4I1Vv9SF\nPDXiA34tNt12kmLgFSEmdAhbxa87bGtvTnPCzme1iZfmqYmc7GE/q2iazWxd\n9R2im5Xr1oUwZCe0jiY2Le7HOhd+Mnkd76pdknseybWYJxf1RbNSDCtOxndO\nMsZU2CzhG38CFyPVozm+5+XEf5QD3cjKpwpZKZOxVTJ8dwB9FK5SLAvCQSGW\nO15Bili5YCxi2053KDvP/4VGMGdVxPLIp5E+uJyeZSwqVAiqmtk52iLJq4iO\nTs0B3tkKxi3Rxkk5vr0OvvOk6iIW+jkcgaQTFj8jZaGmvvGXztR5F2RDsrDr\nfrXKkahtw+W7u3eoAahCh4FgvJ3sclI23Ik2+ahQQR7B3+AsJ/hVP365RpZo\nSnR/YC+QtfzFJYwbepvg2rhaOqkiGk3dcWS53r2hUm6Ugd0AIR8Cmjuqbsrd\nUaeS+WfpvoceuU45rC9nSCPYms9bBNYBN0leYPb+okOWFbZI5v/gp86CfRs3\n8gwadUA99Z8IFcjzpiIEkmPkk3Z8y5tGXIw66FGuxVDE/tPBORGmEOCPEVpg\nJ/zsiqblJ9pTTGDKTdKxFF4fNq3koen1N4ZAIAoZqJUq7pGEzwjWy9Zw/+JW\nRaNl\r\n=ncBz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBcK2xxRA8qZpahqjEJb2iYYQ3oB5CZL1nabOwakIbRUAiEAzxUMeTgnO/0/ZBwQCOBSmpV8FR0PGra5FS51nJmaeTw="}]}, "engines": {"node": ">=6"}}, "2.2.0": {"name": "punycode", "version": "2.2.0", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "dist": {"integrity": "sha512-LN6QV1IJ9ZhxWTNdktaPClrNfp8xdSAYS0Zk2ddX7XsXZAxckMHPCBcHRo0cTcEIgYPRiGEkmji3Idkh2yFtYw==", "shasum": "2092cc57cd2582c38e4e7e8bb869dc8d3148bc74", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.2.0.tgz", "fileCount": 5, "unpackedSize": 32776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIP6Xm9mVsKhWwuUkNOwtc5pc/Av7ddxyExcEtsmu6agIhAK3KDFLU/cDgSMbTHdBIc3UORTZ9ZouNqt8+X+ZYp/aS"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvWqlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocZA/+O/UPiSnw5F69q/vUnwACizMjqUr4Pf3JGsicH6GYzJlozg3Z\r\ned6DOjbc7HGyQBWWqq5dCXRZ18SPdwMV9378ASAA2XBZtfh2UbmdhRTNd82P\r\n/VKcIQjoamlulp8nJ8pV7LSFlpRVaE8auDTXIj0l/8PReZz0q7ysgpFMyTel\r\n/6NGAMnZ2v6e7vDJLgQY1xQRUxzBlPWnbRJue+O1DpPrySm/46tfmr0z98Bf\r\nH+XjXYbkdOb3KuL0IWjz2iugo72+KzHr7z/eTzLlix5h1aK08eJ3VA+mG+zA\r\n47O4/IDIkZSGh6FKlSCVVhpbFdwKv7+C1PTFWHnDo6OpFrfyYvBBLLiuIHyI\r\nlMwjXxfusSeaxXz3zew7kNKOgkkRIWYeypk8R+FtceEsqdDbk8xaSE/TVWCK\r\nf5Earx4ow0bvLlo+kKj3PGTcJfV6jHUJxP+r4rudNq0FBnkWsDibIeAbUcYm\r\nqp1x5f5ImZI8ubL/c9ikVU5uba222r0Kg8/dGU8C29U0bHF5/9StWAMBPrTH\r\ns5gMSG3P+sy+1otCLTQeS+Rks1jthE2FxoGTpNhvGy5Aab0kMQ5sN+VxMqzy\r\nrFShr/BRCX3Wfr48vZVGI7CB+j7lSxyRY/UzaoEWyjxckupFlDlugnPvNHkM\r\n4tGrDTolKKNtcKnbED63FB+g9J5EcgSkSHc=\r\n=O3hv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "2.2.2": {"name": "punycode", "version": "2.2.2", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^10.2.0"}, "dist": {"integrity": "sha512-vKAcKbHEGA3cKbkNWwsA9MjHgIg4/X/xO/PnVQ/Pxt5eNC8/F726fGNlUlwwPej7NkmTg9OBWP4ovYvtdPtmXw==", "shasum": "082313fe5efe0aa2d931d7aed15b99315c24e548", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.2.2.tgz", "fileCount": 5, "unpackedSize": 32931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrpuARtCeUj7cfSn7bcQmWDWQpntWkH+jr/fntODSZvgIgUyzmGnLrY1vO0sdmr+SBJ3SV57L3BMwH3pLKgyBBMfg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyW+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa7g//cjk7hTRa0D9LEMyavWAdRTBg6MIoLQwWbXWSVzkzlS8oWqkz\r\nu0l8/tBoaEL7nkI+AQMJnYAUgjFIGfbfCUrXg4WsNjgbnE7hrv/tBlx247xt\r\nLBQDsdmHnL3dbVBBS/6vq0nzkC0Dk2jri698GAAguNdRUkheMrw1L2dgc5kd\r\nmdIQHnScmW2xulflbf0xkE6GVYTfuUCDBVE3JERvYyGT/LVMMbtpmcFrU1nn\r\ndLnZcCzcwkgN/tcqr/TC4P3hu08lbGteGt+q04+YO4IGB4PaVlDQua4tOiwT\r\nIWmjMMJoNjKtddl7Cxr9wmJSOnJWRszXy0crRjG96WXxArsiszuzfhjwki3I\r\nlYu0LtEPMFhlt8uN8FHERg6TJLvLXv/q1i4VV/aJ8pIJmxWqKByE4iJDBM+Y\r\nt+w7AUDVWlAuQmyxKo70qV5NJi64MX3LUIBJUceIihGNgVfoBnMkLjP9v4ga\r\nIyxwQ2gPMIHtV9Id6+mChWFX/8QipI1sIffPW98U42B/rsEtGtu/pW/HzUPS\r\n0fiQQnQvGlPlV1vUDcu8avz8YbVRVL6S+jsyAUThKXa21a5D9zxF4rQzclNa\r\nYXT2gOinsoUYRNLX/uGxknsQ3/p4n4TosQKp6xr1lhGUdbpTmujrdhaT6Mbi\r\nPg1sArfJaoRFXA6o3t+uu9Ryj6lneKEx7lo=\r\n=kxZR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "2.3.0": {"name": "punycode", "version": "2.3.0", "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^10.2.0"}, "dist": {"integrity": "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==", "shasum": "f67fa67c94da8f4d0cfff981aee4118064199b8f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz", "fileCount": 5, "unpackedSize": 32931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7etdrakClReF2eotN7CploeiUvyNk3JG2EwoHyp8INAiAXV2SXP/IKaluu8vQ0Y58UA4zHNTJXqSTAaf1tN3LCvA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyYSnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgOA/+NTfKcCQhRUufecHdwepK2L1WkMZ/I3oTQMTT+1pjBURKI352\r\n0BC2iC7CK5V60JI0KXxDvql+zD/HvAg06gKs9jbUstDshIrbjoESPy3+Fd3c\r\n058ZMX8+YCtA6LiGpsFZWX7gcCArpzj+l1LQidy3Mvep99M3xpOoXDcb/Rjl\r\nnY7hZQjN9Pd2WvMQIWWEeirGkXzAQ+wM5ASuTmqnNr8qxNlJ0uNQhFK1l0+U\r\n4P+1tSCDKUpVv1OTQFLzD39rd7lIOu1zfzth68i/ustADPrWHwU48EgGfQsQ\r\n97gKduBk3jN7ZRfj5AN1v59Y6XXtmLF4K4qsabbdzEz5hsw7z2Hr01udxkjL\r\nlw6wfBn7C0Y89PL3qH0ALhSS/4IL1DXUMfbRJcbaF3/MkcnglcH31EElmrld\r\n1THcl4dTrqe/GuDePY5LSV4oO/zrvdA3eNOUPq46K2RuDloXFNyp6iafsqUK\r\n2VLKbohIhTyu5ooRp7rC22JSi0JKRrC3GI/C7BJGlS40fcfgy/ogFpZ49trq\r\n2WxCL3iXn+gJU97I2yyNw3kFtQ183p5goXcXpgbDtkP/3taAP5pPz35PqnwK\r\n1vueCRcH0lJAypULbQj0+0EsLJlEV9z4nZ7+TIBwyR3cBQAwD37nGrwhBdto\r\nEV7m5YgRguo6aQYCSQLsDS+JQAUyxxaZi2c=\r\n=41yH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "2.3.1": {"name": "punycode", "version": "2.3.1", "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "mocha": "^10.2.0"}, "dist": {"integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "shasum": "027422e2faec0b25e1549c3e1bd8309b9133b6e5", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "fileCount": 5, "unpackedSize": 33514, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZvXa/+EopO0I7a00fXXIlm21laKpF9NAascJaEmE6gAiEAueild+KrVfZMTffBSXj1LfptDjmXF437Aet8pmmV9m8="}]}, "engines": {"node": ">=6"}}}, "modified": "2023-10-30T18:28:32.689Z"}