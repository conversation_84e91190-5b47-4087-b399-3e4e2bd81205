{"name": "finalhandler", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.0.0": {"name": "finalhandler", "version": "0.0.0", "dependencies": {"debug": "1.0.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "1dcd03de37b283d0593b47a327535c9490d5b246", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.0.tgz", "integrity": "sha512-fruIHIE9W5akKLIs9P2XnFtdnhUFyn9gw1G2DvkSq5q4tjLjk/hp5w38sziMjSCR93sEnXNECEnkm5HDT0YACQ==", "signatures": [{"sig": "MEQCIC/LJHjSu8CbdU/0wRSbHH/k5NhjjoeLhEWXpsC/LBGOAiBQmPSQLxknjQRo/Mjc39d4Jbw3KokOsYzcHrDDEVVz1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.0.1": {"name": "finalhandler", "version": "0.0.1", "dependencies": {"debug": "1.0.2", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "624429d98b41ab1538b21b97086a74f23b07fcd6", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.1.tgz", "integrity": "sha512-LYcUgLBvsQTI0X/WHy9BshCekc8HTluyXtb90E8dNLhpPllqBOO6x+s+aBI1pFRltF7KEdTCXX0MpJqHGk1x1g==", "signatures": [{"sig": "MEYCIQCXL1jeJmEkXi4VyVLhDGxMx0G3P7KrIn6emHlkdNpOsQIhANA1GrrgDAqIIPSn1Z9AZuvg6Hb63WlwaBJaU2R3npO2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.0.2": {"name": "finalhandler", "version": "0.0.2", "dependencies": {"debug": "1.0.2", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "0603d875ee87d567a266692815cc8ad44fcceeda", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.2.tgz", "integrity": "sha512-SbpQfvWVwWEBlPTQyaM9gs0D5404ENTC0x2jzbb7t+P+EOD/cBlWjAAvfozIQYtOepUuNkxoLNLCK9/kS29f4w==", "signatures": [{"sig": "MEQCIBM0Eu6fUgk0LT83TGxlUTW1PxKSwZbTZVw4U8dACKA2AiBq5cDwV7RR4zJG4ulzCr23xm7msNg3gxalrQijz/ofAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.0.3": {"name": "finalhandler", "version": "0.0.3", "dependencies": {"debug": "1.0.3", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "5a86b7bc4dca3d1275eb0532c81ee81d747504df", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.0.3.tgz", "integrity": "sha512-/fqgssseNfnD8Y77HWyJKQ+1xbKu7bZl2LXfhFjkgeGg91WRMMO9GN1KKL53NnIG9g1H2Xq3iKrZkuIcAmjd0A==", "signatures": [{"sig": "MEQCIGjbMFMgHo2d3oq1RWcZLhfs3JYaqjId7y7B09ejRVZVAiANCxkhQcTc9NQ/73RK3SKVCj+mthHMbbRhZ0GT4/pnkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.1.0": {"name": "finalhandler", "version": "0.1.0", "dependencies": {"debug": "1.0.4", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.1", "istanbul": "0.3.0", "supertest": "~0.13.0", "readable-stream": "~1.0.27"}, "dist": {"shasum": "da05bbc4f5f4a30c84ce1d91f3c154007c4e9daa", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.1.0.tgz", "integrity": "sha512-VxIedmyMyIZh8ol/AeWrgVwzDqYVO5wqOcXNuQC0olCvWDgvN7+QyVKHWoZyplbZ82j5p7BynpekYybNSmTjww==", "signatures": [{"sig": "MEQCIEK01t/9/t6sh5vn7Icj70niRCfv9LnAYDST0DES+/LNAiA5mRfS0B31gzy6WFW0drBJoxCkXjfoSleyX4xPn4DDDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.2.0": {"name": "finalhandler", "version": "0.2.0", "dependencies": {"debug": "~2.0.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.1", "istanbul": "0.3.0", "supertest": "~0.13.0", "readable-stream": "~1.0.27"}, "dist": {"shasum": "794082424b17f6a4b2a0eda39f9db6948ee4be8d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.2.0.tgz", "integrity": "sha512-+8V22UTsucJNTi5IcGzTzdYwtESxkyEJ/ipGSVzbGvbqSmmjnNGvrzm/8Uu1FqJT6d6DgXkuzgfefkE2Fl7Hnw==", "signatures": [{"sig": "MEUCIQDuE9i/mBtAftJRW5/1zOgnkiYeSWOSIDhsS0SEV+9kkwIgGzfQkaU3Fj0Q1gdyRpFvQMqq1xeJ5MouJgHTA+6H8NE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.3.0": {"name": "finalhandler", "version": "0.3.0", "dependencies": {"debug": "~2.0.0", "escape-html": "1.0.1", "on-finished": "~2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.1", "istanbul": "0.3.2", "supertest": "~0.13.0", "readable-stream": "~1.0.27"}, "dist": {"shasum": "581afe4d28da13491487f1c0ef9c29ef883e6e59", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.0.tgz", "integrity": "sha512-fBTSSrzaAUVdlsJWPOpwSZL2GLPWsPzbhro+ujUTPQJIJ8suaOnPp8Bg7/3b4Ofs7/NuLSgcPNueazgqxQqeuw==", "signatures": [{"sig": "MEUCIQD/YRahjmSX5pfjrfTTZTSLfx0xhEg7/30o8nXXI/eTbAIgLcFa9wXwELhzk+VtxiwMQ06TwDOgl0E4uDXI5hmSGY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.3.1": {"name": "finalhandler", "version": "0.3.1", "dependencies": {"debug": "~2.1.0", "escape-html": "1.0.1", "on-finished": "~2.1.0"}, "devDependencies": {"mocha": "~1.21.5", "should": "~4.0.1", "istanbul": "0.3.2", "supertest": "~0.14.0", "readable-stream": "~1.0.33"}, "dist": {"shasum": "ffda7643228678c6b088c89421a8381663961808", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.1.tgz", "integrity": "sha512-2wQ95Ql93ncJ8QmACP5gtSdP2nN7GlaOiLUhmhoxrRootI05zP/ux5fd5CeYDx6WJg6pdOZQNv6wO1G2UjKMTw==", "signatures": [{"sig": "MEUCIQCLq9Es4tPGHFVXYEmJZecPf+aTmct+TNwyfCoea5srvwIgXUihzBU5ifC0hnIXJIbl/sYxHLQU0RHxOhGDWjYsKj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.3.2": {"name": "finalhandler", "version": "0.3.2", "dependencies": {"debug": "~2.1.0", "escape-html": "1.0.1", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0", "readable-stream": "~1.0.33"}, "dist": {"shasum": "7b389b0fd3647a6f90bd564e22624bf8a4a77fb5", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.2.tgz", "integrity": "sha512-+V+86srh2q2ebx2iD1qG4J5GULZtZLujK+g6g9Rtwe1sKVekOIRyJaWOKyWoiUMLpI30JPlp1C8QH3ucLGmKoA==", "signatures": [{"sig": "MEQCIDHQworiGvokroGAl54lVv+nL/xjHHO1lozTj9pSdcV/AiBoExJQA5iXoXmpb/s/e6OcDZ5DSYnKBhJM6SsYT9+q/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.3.3": {"name": "finalhandler", "version": "0.3.3", "dependencies": {"debug": "~2.1.1", "escape-html": "1.0.1", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}, "dist": {"shasum": "b1a09aa1e6a607b3541669b09bcb727f460cd426", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.3.tgz", "integrity": "sha512-2Os1f3WIa2papf/sQ10h3NKjI+iadgTUIb2MfIUFcKFZgRSmQtBbYdhKct9wkWjoJA6+zIgIMDFtk4Fd0qKhdg==", "signatures": [{"sig": "MEYCIQDaYPLoOwZWwXgzTt4f+2S8EmpO2JC27B3ZVJDwQc9QkwIhAKRjd/FcNDheA4c4UQ6mr5hZmul2NIDXUpIuNc59N43X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.3.4": {"name": "finalhandler", "version": "0.3.4", "dependencies": {"debug": "~2.1.3", "escape-html": "1.0.1", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.2.1", "istanbul": "0.3.8", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}, "dist": {"shasum": "4787d3573d079ae8b07536f26b0b911ebaf2a2ac", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.4.tgz", "integrity": "sha512-fg2FhK0sQAeb3EcYej2OPhczPIqmMGDHi8C+Ri3UPgslMJnmvwVkw6hFW3xUDt4aQowDwZd+BXGBTHDzMWYDyw==", "signatures": [{"sig": "MEYCIQD5HLdWTKji06D3CNebBFo41cp+jDpkmKq6BtYuVgbC+QIhAKytt//Mmm2Ab9+SVrtxq1+vRBO+/zMJZwYwKuOSf0Pb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.3.5": {"name": "finalhandler", "version": "0.3.5", "dependencies": {"debug": "~2.1.3", "escape-html": "1.0.1", "on-finished": "~2.2.1"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}, "dist": {"shasum": "0d1bf5dfcb5f77d073a402aabe5f7a8a90413721", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.5.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>htjEciScXeJYEc5Exx26KGrL1T1Bvs3mITNk2mLoKaKspC6j1WSl9neb8R0hnzRs42Kp8YEGTOT3cuHA==", "signatures": [{"sig": "MEQCIH4EMl6xQun3XhfcEv0LvVQrI75AW+/p10I4fMjLsN7oAiB2qH3WXyDO8SbeiNua8aZDSKLbfidVCuKGd07olEPR4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.3.6": {"name": "finalhandler", "version": "0.3.6", "dependencies": {"debug": "~2.2.0", "escape-html": "1.0.1", "on-finished": "~2.2.1"}, "devDependencies": {"mocha": "~2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0", "readable-stream": "~1.0.33"}, "dist": {"shasum": "daf9c4161b1b06e001466b1411dfdb6973be138b", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.3.6.tgz", "integrity": "sha512-yVJsDXswFVohBY1qO3p8rhTNMcsZav+s30+2PlrFAeBzzbIgVg1214pHymmSP++KSrr6FXH5+RQItsGEeLK6+A==", "signatures": [{"sig": "MEYCIQDUudYeb0vfjlx6xEr9i6SIkrZMxC0ttK2U8b26U/oAJQIhAP+j5+1XnTot48nkXhC/f3hubDMcRI8n6dpJK+z2QBcl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.4.0": {"name": "finalhandler", "version": "0.4.0", "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "escape-html": "1.0.2", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.15", "supertest": "1.0.1", "readable-stream": "2.0.0"}, "dist": {"shasum": "965a52d9e8d05d2b857548541fb89b53a2497d9b", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.4.0.tgz", "integrity": "sha512-jJU2WE88OqUvwAIf/1K2G2fTdKKZ8LvSwYQyFFekDcmBnBmht38enbcmErnA7iNZktcEo/o2JAHYbe1QDOAgaA==", "signatures": [{"sig": "MEQCID9hPvVABpRZCy8NdCKapEa/nnSxv0U+VmVenRczfQdoAiBWumsooRcOZlciYDUnPIT8Au1dUERQ6GBvimDyj012kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.4.1": {"name": "finalhandler", "version": "0.4.1", "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.3.4", "istanbul": "0.4.1", "supertest": "1.1.0", "readable-stream": "2.0.4"}, "dist": {"shasum": "85a17c6c59a94717d262d61230d4b0ebe3d4a14d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.4.1.tgz", "integrity": "sha512-+AkanbaabSCYrDcrU+TcA/8SEyMDAN7mjE6GC71GAlvYDXM4wzUsRqLLS2qPtWecIlkX5+MMZGd2RyxO3yBOfg==", "signatures": [{"sig": "MEQCICzgjIGNPPu1wBsH+Ms1cEsuAGxfHt9HpFy2sbRzMYLMAiBjDepogcXPtxyYk3tv0YqLJKUyq7Bte0WB4skFsTAXEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.5.0": {"name": "finalhandler", "version": "0.5.0", "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "statuses": "~1.3.0", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.12.0", "istanbul": "0.4.3", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "e9508abece9b6dba871a6942a1d7911b91911ac7", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.5.0.tgz", "integrity": "sha512-KCwi04Tss2Qa+3NQkU3/4lBYXfHYunl3YM0rDJPxhdZ1qjlGvf/BilX2g7vm/qkHUMs5MncaD9f/VTdYN95iig==", "signatures": [{"sig": "MEQCIHyemDv5lbOv8O46B2C790RTUI2BtsCLiMKcp2+s1t4QAiBKtY5j6VVHx8cR+FzVlZliZJRnzVPlzyAdWibMrWKM5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "0.5.1": {"name": "finalhandler", "version": "0.5.1", "dependencies": {"debug": "~2.2.0", "unpipe": "~1.0.0", "statuses": "~1.3.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.10.0", "istanbul": "0.4.5", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "2c400d8d4530935bc232549c5fa385ec07de6fcd", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.5.1.tgz", "integrity": "sha512-PYuh1UzGGCOoRvrbGYCq6memvx41rxMCr+0XT9NtiIWqGG7hbCBcPMBRQoi5sMZDzTOxwiuv7/gwPtrDOz76CQ==", "signatures": [{"sig": "MEUCIAZ3I+2exzCHGhlP9rfqnKaKxvO8kBuRFg8zGN+Qq3PDAiEA4VUpyEpcKHTfgFhnFMUZIZuzhDzKj5yRsCwEC/YFZbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.0": {"name": "finalhandler", "version": "1.0.0", "dependencies": {"debug": "2.6.1", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.15.0", "istanbul": "0.4.5", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.3.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "b5691c2c0912092f18ac23e9416bde5cd7dc6755", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.0.tgz", "integrity": "sha512-vqD6y4OKWX0ColC1GxaH6HiIYIZouvOdlvBjY/P4t7GreZHRuK1HkzW26zx3OtZ0PV5j4yNYQgrqBdrY+suCIw==", "signatures": [{"sig": "MEUCIGg7KYJTkCBLZjEe7OaH/yTsTdPn0aILjbPzKqqH23otAiEAtL6sm7exlHgouUGJdL1c6ml+IZb4vDUM5V1GbV7UB9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.1": {"name": "finalhandler", "version": "1.0.1", "dependencies": {"debug": "2.6.3", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.18.0", "istanbul": "0.4.5", "supertest": "1.1.0", "readable-stream": "2.1.2", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "dist": {"shasum": "bcd15d1689c0e5ed729b6f7f541a6df984117db8", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.1.tgz", "integrity": "sha512-Pr9a4afq6d5JBCdDGMwuGMM0HSt1ZRfl7+X2PAltwiz9oGA+wfi+TPgkcW8YwTbVT1OBESQ6i0XlPbNep58jwA==", "signatures": [{"sig": "MEUCIQDn8hwgnXnuLagj1/0B+0KEkla8qtl33G1P5VFnIP0vSAIgL+0pL1GtmXGCjjWC+2tkPxhXi8nCpAw8yR5+L3hwkXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.2": {"name": "finalhandler", "version": "1.0.2", "dependencies": {"debug": "2.6.4", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.0.1", "readable-stream": "2.2.9", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "d0e36f9dbc557f2de14423df6261889e9d60c93a", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.2.tgz", "integrity": "sha512-pJN03HWitZ0/S24+UcMTLamRkQEauALQSR7e35HQVcRsnYR5qoDaFG5v96jZazOof4B1MKYtV8Pgn34UI0hP2w==", "signatures": [{"sig": "MEYCIQDklSM8DUrS0a+4wrIwxRikI4QSJeUhRvItgNuAuyXffAIhAPkcN1W5h1HNQ1J+thJccNuuxMITQeMhIr4hguFDfGKK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.3": {"name": "finalhandler", "version": "1.0.3", "dependencies": {"debug": "2.6.7", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.0.1", "readable-stream": "2.2.9", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "ef47e77950e999780e86022a560e3217e0d0cc89", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.3.tgz", "integrity": "sha512-Crb5GWw1cfFnZlZ3LJYAQCMbrfsKoV7+DNSvCQpwbybd+8tkrjHad5JuCJhunq8gJ80AqqsvbWxQSDBYxkr5tw==", "signatures": [{"sig": "MEUCIDjnKCTf6RDQzQpFhftdrjmj4yrKzmRjteCc2xA1R3uEAiEAhJ9JsZTJBFP1O9qRc0OAI9LxURHQjgDN5/cor6hRmqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.4": {"name": "finalhandler", "version": "1.0.4", "dependencies": {"debug": "2.6.8", "unpipe": "~1.0.0", "parseurl": "~1.3.1", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "18574f2e7c4b98b8ae3b230c21f201f31bdb3fb7", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.4.tgz", "integrity": "sha512-16l/r8RgzlXKmFOhZpHBztvye+lAhC5SU7hXavnerC9UfZqZxxXl3BzL8MhffPT3kF61lj9Oav2LKEzh0ei7tg==", "signatures": [{"sig": "MEUCIQCsEFmttWP8fX8zo46iooBt7LKKCytIcZZXcJt9tkmFlQIgMUrJ9S7FIhRRMVKn6420qTg9rhhueNQfRNQcuYPWEc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.5": {"name": "finalhandler", "version": "1.0.5", "dependencies": {"debug": "2.6.8", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "a701303d257a1bc82fea547a33e5ae89531723df", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.5.tgz", "integrity": "sha512-Zy7Wo3xyTfMqIbJIqSgyqozMAY8GySij0h9c4sIghfBgPeBfQPqcTY4zhAAfHC9zCaU2lwWybWKmoJb+sQNwmQ==", "signatures": [{"sig": "MEQCIHGHk0Z6WxtHUwCuilzLMykpGiV3EZvBr/hshQwaxpopAiAaceCZOxvaWuwMerjsOXq9x5FgE+PCEwKh3Gd8Eeh06A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.0.6": {"name": "finalhandler", "version": "1.0.6", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "007aea33d1a4d3e42017f624848ad58d212f814f", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.0.6.tgz", "integrity": "sha512-immlyyYCPWG2tajlYBhZ6cjLAv1QAclU8tKS0d27ZtPqm/+iddy16GT3xLExg+V4lIETLpPwaYQAlZHNE//dPA==", "signatures": [{"sig": "MEUCIQDK80WReNkc1w9GXp1jcJbcOP/t/3N5gzG1fwVmF1/YkgIgX+42fUOw//hoR2LP2w3CUnkusf5U34is7wnptQoxQx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.1.0": {"name": "finalhandler", "version": "1.1.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.3", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "ce0b6855b45853e791b2fcc680046d88253dd7f5", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.0.tgz", "integrity": "sha512-ejnvM9ZXYzp6PUPUyQBMBf0Co5VX2gr5H2VQe2Ui2jWXNlxv+PYZo8wpAymJNJdLsG1R4p+M4aynF8KuoUEwRw==", "signatures": [{"sig": "MEQCIG9NYuP2lJ+pMrQcOLGIOIg/ojLDY6+e43iNghsXTZ6TAiBVwoC1qa/lwLb+Rzz6svD5RLzoLkZizBgmYEe6FpqkgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.1.1": {"name": "finalhandler", "version": "1.1.1", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.2", "statuses": "~1.4.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "2.5.3", "eslint": "4.18.1", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "readable-stream": "2.3.4", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.9.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "eebf4ed840079c83f4249038c9d703008301b105", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-Y1GUDo39ez4aHAw7MysnUD5JzYX+WaIj8I57kO3aEPT1fFRL4sr7mjei97FgnwhAyyzRYmQZaTHb2+9uZ1dPtg==", "signatures": [{"sig": "MEUCIQD9yaevjcuUnb2pQtBngoijq7qzxl87p/EW02hq4PaTGAIgLb3qeypiLlVDgexkIiVizRKMzaxJA5ePbZDOdZMpaW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16905}, "engines": {"node": ">= 0.8"}}, "1.1.2": {"name": "finalhandler", "version": "1.1.2", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0"}, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "readable-stream": "2.3.6", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "b7e7d000ffd11938d0fdb053506f6ebabe9f587d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz", "fileCount": 5, "integrity": "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==", "signatures": [{"sig": "MEUCIHQ0EqIEi2ZujhmHkPulCwWGYcTAKAHr/GnmM1q8zG2KAiEAsIs1p36gdXV0VYeiszruvI24Ay7qQB5p/BRxWmJ3QBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1NxFCRA9TVsSAnZWagAA5lsP/ikRb6COPldBamNBvpJ9\nRhs/yK6Xs0xWScOozeN+eYycCG1ybDJaCZ7Pw1dkjpfSVB6gIuV+o1DxycnA\n3Fp+IPyJ4W3hHgWUL+1KuA5ajYbrH22ezf+DnmU3G04Xj3qpEZeeYI3hQ0IQ\nBXWjiCwmn2CEB8HYyAuvgEgsH8crqkzRlaLMb4I57TAKk0JKwZd+4r4t2WlM\n8sI83C7in0rOWkjUpJWBuR7hwSGqygKf8i0cIrOdA3ilf8EjmjELrA5mIga/\norpYoarZRenzzyyRRfG3H3Sryblavh6lajTwqwh+CxmeQJLv+gJTERLvrUc2\n2Rjmj2Iu3Z2h+D6f9qWdsPRXqE91KU0X0fDtBNjibOMncX8GtpG332SmN0Fd\nXs1E/Fcu2GlOgoJfD5mKA2OJ3GN9YlFfc7uvXuaB491/i9J/Zde548Tdn3im\ncxN8ULRL3GLHHbxONXX9Q49mdQxmHsJDPe6xN7RQwxirLZKlZEXJjl82x1kJ\nJQ0wHcxsuZiJmUTje64W5ETFBI1qN/rJiFN8HQTEkUeXpnI/Kl4ktzefhwPF\njlgPQ8BLiCeHAM4cCweDGUTUVelo6RQaN/ljpPivvd1l8DU+1R4guvjjDWcr\nb2eE7W0pup5R/PAJe61LTA38pRuurq4kR0lLfPWvOQvhPuSZ8WbDP0JNBf/U\noAB7\r\n=2zSq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.2.0": {"name": "finalhandler", "version": "1.2.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "7d23fe5731b207b4640e4fcd00aec1f9207a7b32", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "fileCount": 6, "integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "signatures": [{"sig": "MEUCIFPj6xPah9aAusx764/n8Uzbjg6HDuLmdds0gdjM2t/sAiEAiA7wlztmwdbDRrC2ImXm47JxpC7burCNug8YKiCxj60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOnBcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7QA//eTRG65RFRRWecfz6OMAXeYJBjrqGRLlCwjTpcMvlB2txpGML\r\nDt5RXXQspjfNjkJoA3Fx7W8+/v/CZz3YoJzkNdrOda9ia7KfTWxf2XICDUvF\r\n2y1lbwcW/M3FiKNTRnKC8CZtF5+TlfDhzWpfnMi1yOGQ2AftiE1QUN9KfpA7\r\nNBr0KJZeURaiFJ7oFIIw9QZaIhxqMp5X/Fmk+zoJ/paeWs22UvU4wxJA4YeZ\r\nSLSTz98aHUUoAl3MfDH8oYWi/Xwv5x3kHQXt6kNtfct1QUEEeDL3fynjdZK2\r\n100pDboUQBq0A/5Qp6Zy7x2aVass9faYJmx1Hog7N/TcOQKHHWiccWcqQcZw\r\nq66+642gUKOaVIs49Fk38qYkLoW3bGNRcHmhc67PrUBvHv3UFQhut9U9OlbO\r\neIymoxlobcjTCXE1YKJJp9rrXQobs6nF8WNzUpX9IiKw+Vy30ASggYQZj3m6\r\nIlLxmjGhOb1irF21crbjhoKr4hGIheC6MJ6d8ClCWik1eX5UJ/TlpkMWf3mX\r\nkiFkdaXlF+QJHQvqvK79kzMpDQ373wJzVCDTMTrh3zwaph9EXz/AHP8Hptai\r\nr0YJ5X6AzR6oNaVzvt4C5bvf0+ULeIgAbdGiRSEDVIitJVauHONHM539UdCY\r\n5UWYqxWu7MtsB2ijEtn8YY2+juTEJSHtwRM=\r\n=jvxr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.2.1": {"name": "finalhandler", "version": "1.2.1", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "57c1ca3c6b41d6c20206bf5c76450a1b9e027ec1", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.1.tgz", "fileCount": 6, "integrity": "sha512-NpHDfiu6jURpO56pYkM6DEvnBEA9jNrwj4v05Vjs5hmdqEB2/kRA3wugct7BMyqYydjN+kWunMhtTn+itVmxpA==", "signatures": [{"sig": "MEUCIQDH+1tIlQvt/jZLq5lp1kouZnENxHH/xhQQFRm3G04x2AIgHE2XYr2/TmhBYMqjpeJ2jfUCXYHRqMqCxJdZ44rnjIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18837}, "engines": {"node": ">= 0.8"}}, "2.0.0": {"name": "finalhandler", "version": "2.0.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "9d3c79156dfa798069db7de7dd53bc37546f564b", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-MX6Zo2adDViYh+GcxxB1dpO43eypOGUOL12rLCOTMQv/DfIbpSJUy4oQIIZhVZkH9e+bZWKMon0XHFEju16tkQ==", "signatures": [{"sig": "MEUCIQDzcDSnxF3JRk+uG9HRZM3MYomorBkLdeV0JXz6SIkVKgIgeSMdI52GlIJG62KLJ45FGewBDD3w7q/Gvif1fqFMZko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18997}, "engines": {"node": ">= 0.8"}}, "1.3.0": {"name": "finalhandler", "version": "1.3.0", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "f378d7aeae27cda454081088f5075edf662b215d", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.0.tgz", "fileCount": 6, "integrity": "sha512-bmwQPHFq/qiWp9CbNbCQU73klT+i5qwP/0tah3MGHp26vUt2YV4WkdtXRqOZo+H+4m38k8epFHOvO4BRuAuohw==", "signatures": [{"sig": "MEQCIF2BiHgQgF4/KgW6sszADLbBOocAp5nIkPz8IZJ7T1bkAiAC1cbNZR3lwzMKTzPg2noy5GhAJYGnpHckn7ed4Y301g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18966}, "engines": {"node": ">= 0.8"}}, "1.3.1": {"name": "finalhandler", "version": "1.3.1", "dependencies": {"debug": "2.6.9", "unpipe": "~1.0.0", "parseurl": "~1.3.3", "statuses": "2.0.1", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "7.32.0", "supertest": "6.2.4", "safe-buffer": "5.2.1", "readable-stream": "2.3.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "0c575f1d1d324ddd1da35ad7ece3df7d19088019", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "fileCount": 6, "integrity": "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==", "signatures": [{"sig": "MEQCIE+EO/mk2fLe3N7K3OOcWPOFd6nwxaSD8Of20x9DmgJTAiAiaTR92i7LmC4vR59PUGWFDc7zfYEG//Cx25uyMLsuFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19034}, "engines": {"node": ">= 0.8"}}, "2.1.0": {"name": "finalhandler", "version": "2.1.0", "dependencies": {"debug": "^4.4.0", "parseurl": "^1.3.3", "statuses": "^2.0.1", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1"}, "devDependencies": {"nyc": "^17.1.0", "mocha": "^11.0.1", "eslint": "7.32.0", "supertest": "^7.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "72306373aa89d05a8242ed569ed86a1bff7c561f", "tarball": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "signatures": [{"sig": "MEQCIH8/+8yXa5lkOzPItu2ckGzsiv2m9TfoY7mBHAQfIGrXAiAFvw3jnh8xpTSmOhcXEX3i7oxnhLmx9KdqgHFOhP1pzw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17525}, "engines": {"node": ">= 0.8"}}}, "modified": "2025-03-05T14:51:07.880Z"}