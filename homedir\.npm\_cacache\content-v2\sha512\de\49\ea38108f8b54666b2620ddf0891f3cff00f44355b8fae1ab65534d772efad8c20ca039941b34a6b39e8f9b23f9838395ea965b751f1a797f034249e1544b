{"name": "eslint-scope", "dist-tags": {"next": "4.0.0-rc.0", "v3-latest": "3.7.3", "latest": "8.4.0"}, "versions": {"3.7.0": {"name": "eslint-scope", "version": "3.7.0", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^0.10.1", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "d95536f4081dee922e5fd60ed6795748ca54bb76", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.0.tgz", "integrity": "sha512-QR746DDAcFmtSB3oll6foK26mKD4wLk2KkqoEK+zdRgUvUJmBtcM21XPm0Tbxw+A14yMWSU7nbAOdhuMQEAYtg==", "signatures": [{"sig": "MEQCIBSjLGTz5oNrBGgO7thUZcV5MvEsOFJISGbMK+hAKUp4AiAYRTQWqt8dolW/j6KPkRTM8ak3raS4i9pZz8oEl7YE1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "3.7.1": {"name": "eslint-scope", "version": "3.7.1", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^0.10.1", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "3d63c3edfda02e06e01a452ad88caacc7cdcb6e8", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.1.tgz", "integrity": "sha512-ivpbtpUgg9SJS4TLjK7KdcDhqc/E3CGItsvQbBNLkNGUeMhd5qnJcryba/brESS+dg3vrLqPuc/UcS7jRJdN5A==", "signatures": [{"sig": "MEUCIQDu7QP49NtSzhi3f5Z8L7u4xm54l6xgHY1Ep6fYKBeB6AIgAfh/e8lddooDA4QLyC41dMcyUNfIn3JSRRSRGgr4gHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "4.0.0-alpha.0": {"name": "eslint-scope", "version": "4.0.0-alpha.0", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^0.11.1", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "56d64aa6db13023373e3ad70b5c6cf40c8fe40b9", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.0-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-lagZ90I5IxJvoGmCbhCviN69Ycx1DBiI6h8SKz1DXHYMq5XnIxoCl3gMS9PwfVcZBNREbSv2jDh0/H1LTmJXWw==", "signatures": [{"sig": "MEUCIEZgwGZIlmen5GDp71PP3VjLRCiRJG8m8DBX2noe4gDVAiEAw4A4yX21CNxncn603TNzaplRPWCa5Zc+PiJ1RE0ytR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75766, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa49KmCRA9TVsSAnZWagAA8eEP+wSqHdR+uXC92vPq+lZF\nhEiiv1DFn6dPKUBAKquiUJWhPx1JgMaEVojhvGtR7cuWOv53FNQg+sehO8Mb\nhaoS2sHjjfZeeBOt32fE0XTtbctg7hkmWCrz0SMEzasQms42QjCdSM8So6fT\nHDK9ulc8Ymh+XgXAXh2vH3t+H67S603fKn0SIvoyRhITxDVwxwi45kTHwQfv\n/kmge7+z76jRCRR9FIYcqst43WqJu56upew3x6HYOrv/HWk36AEv3E14XEae\nuEuJC9ByhpTSDFSk8zDpAl8ZWz9m4JkqUQ7Rl+wnDtNTUtxOjSlT+og6C8Dw\nDg7lGokIKUOaii5VJV+ppL08g9U+J2bxFQS0sTwKoCiq2+73q0NdeunxSwop\nOCbj/XPzfC16UL/CeCGhqAAlgTLIkPCUYeQrRDxaG9crHkSUetA11Fo6+8ZK\nVhJfJXAozSvFZZ7zG/HmGaeQe7T3o98WHnX7aLaAM7DIcjZm4pBbtJkEyEkk\nLLLi2yz+ZbJuEBWszNSaPotwH/1axNUs1oHEUIwsfNLeI4dDxuxsOfx0CyZ4\n+i7p7l13kCr+u/TvyiicJcH6Na6vTOug5mzPn4NHttdIh+HuMMfylz+Z4HxU\ndHrkp59KHtZPRnDIH0tTRWHaluXlN4TPdTBJn1KphInBqfbz7BTFboQjOZY1\nw5UE\r\n=pv8+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "4.0.0-rc.0": {"name": "eslint-scope", "version": "4.0.0-rc.0", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^0.11.1", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "90b7e7ed231c13956c3cb9cc018e96156e8cc6c6", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.0-rc.0.tgz", "fileCount": 12, "integrity": "sha512-w9crN0noCk/F9WcS+EcwnHBzMNDSF2b/WEXVGEXCXtZoW+QCK59MmAwBs7mbvUWqqTXG1C0T1OQlly6Nxta7og==", "signatures": [{"sig": "MEYCIQCJCZRX15qHc3mOQ/YNMYbLMqGPEVWVPFo+lwRbg3GItwIhAMkFB5ktGlb8wTl3n+bCiiXqepP6S1d/c3UV+/LjA4Gx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbG/lVCRA9TVsSAnZWagAAlb4P/iIXVQ4b4hKJAmvLOt1O\n6A9WBLetMn7zy3cDGjrSIoBshJJ0gEVf5RoWBb6vB0GKl21Oxq54ROG4Vd4B\nOCUhY6lo2wiJlPeCaH0IbjRBWi34XeTkNyZrLDLMbnIXDK2DkBFlQFsseQNX\nbu7rfzD+twmGnGO9JoHoOVd8wa5UqbtBhw+HPqw3j1XedKsa7B/z+l0opRDf\naEOs17V7gB00RGI6igOpggHjRHUttO3ytIrwkqpT8ywJBfcXwT85Cm+4s48T\nEwenS9ywTIiAU1QOVa2Glq6nahi3Olp8GUnXyitSWKixSx2EHlLvnTl/FuGL\nVbyMj7OvAzExFepSxQu81mgMoFopoCTRl1ZSp7IN6L6k4tCgXOzqwyjzEHgG\nMlRDTwKIXW50Y3p6pYEAfFznG7ILsj4h3auREmjggNaxVKwuIWgOXgCiDk3w\nOBBPNDThUXwZun6NY16Tn8v3nK5WTOPrS7ggk4NYbEz9MarAzXkI/Jm+r20f\nTdLVZaxygCcVIcyGYA/k6CcJsD5Qa+AnuE22uarfdsAdktSf3FVTyAnUFoXw\ny9oYMe1ADd1tao94coDGEDem9N+2vhte7W0bteFBPw0Oob1cAbFDBzGrI7mt\nfL0hTovu5OoeRgu9IPZj+9mo1aY3yv2Qc1Py6Xjtk7KW4YT7g0hnEraxLD03\nMraW\r\n=r91n\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "4.0.0": {"name": "eslint-scope", "version": "4.0.0", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^0.11.1", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "50bf3071e9338bcdc43331794a0cb533f0136172", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.0.tgz", "fileCount": 12, "integrity": "sha512-1G6UTDi7Jc1ELFwnR58HV4fK9OQK4S6N985f166xqXxpjU6plxFISJa2Ba9KCQuFa8RCnj/lSFJbHo7UFDBnUA==", "signatures": [{"sig": "MEYCIQDy9v9wcFFeJULgtLIubDVty7bNmSOVC+3+pNtqZEQlPAIhAL9e9TXQzFa8E5ly/IcKtEjoVcVC/Emd5A0L6UEyLVpE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLA2FCRA9TVsSAnZWagAAGsoQAIuIqvOsnsXeglc97Nfp\nUvK2NkNtr48ScUsmW4QUAfahKCAhiIB0h8lsqi24sg7S+5Bq6n1tfvP9PW8a\nSUNMfPxzYxLyxeURNM+v4lDu5l3MrA0YJ+/XkW8FGmUrmalSGDfY1uvc9d/u\n7L3NP4Pkttx7CaYvYrOQWs+bEpq0jb+cvZX4aPHtpAcq5f0Ow8F3Znfin6nE\nC8SXrPUoM0YEU02fc1tduC3uyoxl0I3IWUdq8BQ8fDDoZWEQl0Qax+iDMliU\nnltV4SQ+AjgmxcqxUsLgee22ZVIEX9Ke+0nbV5QEsYJKH8KchBE3lznguoPj\noqzNO5mmOxkdf7ptenaTEI6/uwJAQxXiOV5HBHEratpaVUjBsJ3ULCf/YkrF\nTYfv4+cZBdUrPo6dVBRWDg7eP/FKZTNAMhTtbjRwKeizBJMFQvlX28Ry5Hl7\nz4Hmlii5g82uoWwM3cEeVbfALWJiEpHBXUkmvcJKsAacKg5Nr6yIgfwkuJ6u\nTXsSRUuDZoaz4qTpd9VSY5o9l2AmeMSRML1SYck55czzkCGYFYTiozr8nB1X\nckuD1jVUQPqT0XNEsgQuWadkUVL1TFdhp3qivuMRse66bA6TRdvjyjYoOGjL\nRhTwQVqR5JA4MUe/hqowBgpz0OCZCraRe93c3OjobzGCRI8akoPRQK6MK2T9\neGcE\r\n=HTP/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "3.7.3": {"name": "eslint-scope", "version": "3.7.3", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^0.10.1", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "bb507200d3d17f60247636160b4826284b108535", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-3.7.3.tgz", "fileCount": 12, "integrity": "sha512-W+B0SvF4gamyCTmUc+uITPY0989iXVfKvhwtmJocTaYoc/3khEHmEmvfY/Gn9HA9VV75jrQECsHizkNw1b68FA==", "signatures": [{"sig": "MEUCICiiFMbBKevRmfo6gcSNkecbSMoYHP9dMdd5dDefx4IaAiEAvEtCorIkhrj6jN0rM3dTK1qK6bOaA7W5OzidqlQ9u84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbR5LCCRA9TVsSAnZWagAARioP/0WkEws2Mcuqds2pXijJ\n1a7yByCtQcx5vuk/Ez8WBJnaWsE8PuAaV3j0QTmuNjRZ9OYOyvPv77007L5z\nzCLOcJdfe34yoj2naz3bCnOPFhPLrTjekFIJz6TBJ4vxfVgShqpOIRDB8y7D\nJA2EDy4YMQ2anIZXwfzsLbD1GERhyKllUcDjYh+kpkPpz4y02yUpttqO3pbH\njIi5ASnPFYsWCpOIknW86D/3UPZZq8X7mhCrGRoxwwHZKRk4DAYHCTqElGce\nJYWN+erT8rybmt387AWNsAK2c+Hru63LB08qjp7J7wvJSONSzBaUGEMrEoO9\n44vFngopzHdzSZHBewuRISDS/MoulC1r1l4Gdb/I+c+lMml1MoeiAOIYP2dZ\nDhyujnRscIw9ShsiMJaXbE2P9/9Y7Yx8MuYCdhlkPlQSlqcGYi5X9S5bbMz/\nysPFFz5w78SyP08XBn0U6tKfvh5oBgRbvc5fmooUBLenuhgVak9og1eInZA1\nNkpEf8QCOdXpMPFuRniFuEl18v8UPAO4B/W68Mjc0eaLtFec4b4SdxF7XMDf\nge78ArEsy0a/C+SqK3grNCsYiI9ZJGSIyxe7QyKBJFGxCddAmR6+hMv5lMBm\n/8Etle8gqEgQwkRTYAlCvaPELMpxiTEtOa/Ge/eHSCO8ZRaUQUw/LOqsMofK\n665T\r\n=OiPz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "4.0.1": {"name": "eslint-scope", "version": "4.0.1", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "dc3e21b1129fe4f61509a025db075fe7ba3abdba", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.1.tgz", "fileCount": 12, "integrity": "sha512-PeQeU4s1Pr0LXLZ+YF7aYaZirFDw23ssCKeahBqZqa8aM9Si9jSx7MoSKhAl3m+VM9oqnGSMdLYZ2QzB09aQnw==", "signatures": [{"sig": "MEQCIDvhEhA2jpOGAkW38/NwrVwSsee2WSJfzm+wxrHH5KcfAiARL3g/tGJG1g5q59g120FVdTHOTZVLQ1ctuqsZTIIE+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcedivCRA9TVsSAnZWagAAAMIQAI3qlR2giYyxMmBOlVM6\nMoRegPxwx2jX+Ws95uUza1S6zhMnhZZ6o4Cby5lGYS0vwDPu4n5VSmm/28Uf\nVaacgjHKfM+HdhsYFBRbo68tlWtSUAGTYclyC04MMqR1tLSwRnye0rl2UqCa\n3sZ3rOXdgL9ciy6tx74MG35Kn8NEdrdlVDMZszyPTfEgKc/VQOzNPxNl0tf4\nA7htOkF3pR1FT0lfuoamBbv4nNrcOY7F8cS1QhZm2lScvqHRhlGa4rhREsBd\ng2nk0LEhK4SEcjFv8MC1SNRgdnz4dFGfIQn7mhu9NIz0llfRQM6eIve3ZmFG\nOtYMqIUCQmttScVUdDCUpLx+Lgj3CjEhGW/gmq6nDPnnvfAHq5fDN2TzliPR\n/K8o+JtK0z/QFK6npnXiDU1BkMp02Eu/j5Zp55KXkf8r9uNXN8HEF8EIrV3W\nce7wL0/qfIIxJhGJsFhdZ752nF5EXIDRELM/dZFPdy712uaTTgKH4uhOMeUL\nDzikAYuSDt8XwEV92RIsdTmAnZOQSfb9U+gb7I7FN/31xnTR+13Zq1LvNw7S\nGs7/ROFZFjWk1JHiCTMsXB6pRgwFY4MkqJxbGrMYA3lh98VGz9Is+LWaj/qw\nfey3WfFXZ+WEGaS4MDPSI6Jkn87LnCl3B2fkYMpPGvV5nKhD/VLIE5So3A1c\nv+r7\r\n=s0j9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "4.0.2": {"name": "eslint-scope", "version": "4.0.2", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "5f10cd6cabb1965bf479fa65745673439e21cb0e", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.2.tgz", "fileCount": 12, "integrity": "sha512-5q1+B/ogmHl8+paxtOKx38Z8LtWkVGuNt3+GQNErqwLl6ViNp/gdJGMCjZNxZ8j/VYjDNZ2Fo+eQc1TAVPIzbg==", "signatures": [{"sig": "MEYCIQCOZVWUPnLPJbvkjNHIvTgSj6N33OFOj+3vyvOHx7nrTAIhAJ2/7uMEqGQpqicd8TF5ZXQjInF8vA2mhtvV5AdniBgC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJced0TCRA9TVsSAnZWagAAcm0P+QCgBu9yCCO5EwsWTT+T\nKxuYbwD/kVB2Azm0TRNzwJrUnDneEaARKlri8TBZd/nvmJd0BwGzEOi8MPiT\ndcS5QeTtqLw++RfQ4yKLmWN/CuW4REezZyePBXkAzN8yq54s02GK/BgDn3y4\ntLXayJYNu3+pJFYHOnm2tubZPB4r4ZufCms4Ag/iv67WLzMPsvvtzvFUSsxx\njDilis/I0YyrXhtBjlNHZnJT6vafg34HFWg/Jbnm4JRshAAl6k63PxRrgIPZ\nbyl4mjx10EgK8/8KNy9mMHKZqFg4AMJZLJLeeEOaBkTCiKvEWB2RnurY9Blm\nTlM/8CEe4rxGgJsrmJky8Xek0hw/mniELuNdggc+MFBT8MIY/3auGYINnzXX\nruP5yXhcA4B+pCw6I3w5z78AkOui4WZf+YO/+ppCZSBRdGuIRRLAHJDFTpEG\nRa8HoJGhM351vcVoOD4MW1ih8fdriV5Xm/I9IRB33oHghU/Wt0+ZyFnjUejl\n1zin9ItzCa69lTEGuNOTiTfD+rKmyDT6gjspLtfO1eejAOvr25bU0UE6KLtK\nIPlHYuTaA/Od5OaCtPPhmCr52hqq5TUyKmyH/DZMrDOUXgiT4F7RDNF+/Hf+\n4NDq2yMKycXwQFgUh2cIz932dSeP2pvPmxoBjmuHE4ippm6Y/HCYKwdVGekn\nevh/\r\n=X3FY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "4.0.3": {"name": "eslint-scope", "version": "4.0.3", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "eslint": "^3.15.0", "espree": "^3.1.1", "shelljs": "^0.7.6", "istanbul": "^0.4.5", "typescript": "~2.0.10", "npm-license": "^0.3.3", "eslint-release": "^1.0.0", "eslint-config-eslint": "^4.0.0", "typescript-eslint-parser": "^1.0.0"}, "dist": {"shasum": "ca03833310f6889a3264781aa82e63eb9cfe7848", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-4.0.3.tgz", "fileCount": 12, "integrity": "sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg==", "signatures": [{"sig": "MEQCIHSeTdewpXAA8J4iqEp+lyp0T6Y+UE7kC628cAhRyhdYAiBmRu1Fix3vEBi3x2Pc3i3O22knn0TWS9Jn0u2Np0/DeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJci9zbCRA9TVsSAnZWagAAs0AP/iaE17Q937T/f4CLRoMm\n1GJL5eIYcMIudpA1b1WrxmrpRCLqc38bEqYo4kWvFGNmT4ZCcnrIMod3jY26\nVD9rXrtiW2QPqHvkr1CLKS5HkUA+LgesZDl9I6BNb8SfyrHav1dlx136QTCb\nlSWLnrohTfdEWbjhh+RtijV3B60XPQcrh3TQlOTOIJb/SSocoXfFP/kH37uA\ntsNFtizrOpjpoNINnhzVzj4jntnysJZtswK9hoSCIJ1/Jbt1Bn5RIO5fWY3C\ngZX3nC4x2EVyLLbQJrRGmuUQCdvCE9a0cvqzVI85u//nD1hZIodhEfBHK1wq\naa0fwG4KH7mfLF53f2xQl+3hBsSpjuotcmRQq46h4Hr59cY5pe/5lZx893UO\nQyOvUwPMPmqFzA3arkFfDLCceJxg5chrVlsvpxgUoItALHNaZ6aGzwMXc4i/\nWyGvzTVgCHmvrKbqgsBDhlAx5SmasM1NhPRPcX2OWhZ6tK6y8ASD2JlraSSg\nlIrfs70KzR2+g7lk3PAFbmwScs0XHbiMydn+Du4SmndFiZtFj7yRQl5QvMIV\ntTx8TQJwP2S9rYnBNtgdnuaAnMTQwa93aTiz9IlrzPctRZ08iEoYIllkqnmz\nIsklWUkEhtwucRCg6w/1+B9T+sUQfi6I4k4djHYoPycFtg/T1Bx7pkkAOL4U\nBgrX\r\n=MJPG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "5.0.0": {"name": "eslint-scope", "version": "5.0.0", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "espree": "^6.0.0", "shelljs": "^0.8.3", "istanbul": "^0.4.5", "typescript": "^3.5.2", "npm-license": "^0.3.3", "eslint-release": "^1.0.0", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1", "@typescript-eslint/parser": "^1.11.0"}, "dist": {"shasum": "e87c8887c73e8d1ec84f1ca591645c358bfc8fb9", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.0.0.tgz", "fileCount": 12, "integrity": "sha512-oYrhJW7S0bxAFDvWqzvMPRm6pcgcnWc4QnofCAqRTRfQC0JcwenzGglTtsLyIuuWFfkqDG9vz67cnttSd53djw==", "signatures": [{"sig": "MEQCIDPqEAYI2NIE70VnVIDciLWKCy7G09wByWlySk4v/XWWAiA6dmWXT6cTq4flKNI5JHPHMeqbM3I/Nh4SVZmwKonuvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdM9orCRA9TVsSAnZWagAABO8P/RrxXV5AghxUbjnWGmWR\nEZMr/wCVWahoKdu9FughLNrCIFDqXA5KWlflBhRKo/s87YcWjMEwvn+5hrDj\nwTZyf1qHUEfbWo8zJiqY7j6O/iDYrHAkd+mWS77D9mZxwbAMt07/snXUX8ui\npPdZnir76f88h5AGgt/VLZyB+6pEO+v5BJYyhWlCBt9YCUfULGQy8LzRMYfO\nXVIGTJFQle8P3jscmUmRq+ycfEySq2qW19rv4hbVbG4Y7Kvmh/iiXJ8ky0u6\nkS03eK3vCHImudAfAeD+H5GU60bMEFhD+PBnsR/wuEVUIcWk+0u93Jjs8xTb\n+CU6HDxtkcQ/hw7yZmz0WskY8o8Aa5G6iXxvLz6x4RJOPBDofXBJWHguZ7io\n5iCaivucuGwN0u2HypXDRsDKKZo+32UsfX6YDpuzMi+beEGm41ly8pIFRbiR\nJvD1Tx7218TPHhDMHLQtfaIsgylaUjec03KTuDaZpjA+chrIE59ERqCho1Iy\nvi8rSgDJxnzYk9C+WgZM/2Nvo8NIGpF52TqJI1TdqauByyzVldp6pdi+AR8m\nWlYztW5orsriZzGJEm2ZVloQoBBs0jBe8idlD0XjvWTl7DJGYYx7UimtorAK\nWK2eh3vpgTrqWLUrVsiG0emKShD/MR9kl8hPoe2megm6k4Y6YrOP4JVdxwh9\nckif\r\n=Aj1p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.0.0"}}, "5.1.0": {"name": "eslint-scope", "version": "5.1.0", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "espree": "^7.1.0", "shelljs": "^0.8.3", "istanbul": "^0.4.5", "typescript": "^3.5.2", "npm-license": "^0.3.3", "eslint-release": "^1.0.0", "eslint-plugin-node": "^9.1.0", "eslint-visitor-keys": "^1.2.0", "eslint-config-eslint": "^5.0.1", "@typescript-eslint/parser": "^1.11.0"}, "dist": {"shasum": "d0f971dfe59c69e0cada684b23d49dbf82600ce5", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.0.tgz", "fileCount": 12, "integrity": "sha512-iiGRvtxWqgtx5m8EyQUJihBloE4EnYeGE/bz1wSPwJE6tZuJUtHlhqDM4Xj2ukE8Dyy1+HCZ4hE0fzIVMzb58w==", "signatures": [{"sig": "MEUCIEJ6U9h/MS7Cq3w0OXulTmpcXOhNQra/+TB/xuRv4Y9vAiEAlSvRxWyGpCIA8wh/mwXZVsyUVsJSK2GzCIqmgyssc0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2NoACRA9TVsSAnZWagAAamEP/1uqeiu9fenJVmBJT6We\nHj7tx2OWOXPwtZvx19xZQaDiVDroZ7m4sSxxy+LsqPg+pfQNVn6LLDDz6VfL\n6vrXvyUrB+JVodgewumlhms9Aua2GsPnd7O/vXebylktpgFargNZJs2mAXjk\nbnfosV83nhtsQQWBD4Qp2wvxR/d41AY+KA4Gbqig3QgUCBpFy1hGm0cO6DDB\njwg1+baemgsbJ/MGUs5l/RYc4r2n/T+bz9R7B8PgqL+G3O6n/zhT4gskPpiq\nu7v0Mc0O/S9RaEQCPOHLRbCLuL2aNHqn1fevR2T+KEt8IEZkZodDc9K9ueYk\nA3/YeGM1ewnnQavKSjl0ZKcEbGFmfBRfk7bpcCyogOK857Sp1WZfxX/njHrT\nJwyfw2FxOhzObULJllmZTU7IdAGqWyUs4eyI+CNH9fyVdcSS7Db56RRstI9n\n409CN5jDXH+3/wrI2wu2lDJYoS/xHuIgAbLOc4yzoOf6eAmSKtFtUUn7TDM1\n6OVYadipip/w5H5ySAyBEMrcrSp16vkzlTufapWuZrmDDOYAYleRLaGtNHO3\npcmRPkCC/q8iNeFGMwmgu7TQE+cKK0bpOPkr1CstV+ti+JXy9gEolCGbatwl\nFQ/hH+c+v4qUpYedjgtbs7u9TbzzM+/KkeEiU93eEOqm+cz08JhOvDIDNkzn\ntwhy\r\n=lfPZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.0.0"}}, "5.1.1": {"name": "eslint-scope", "version": "5.1.1", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "espree": "^7.1.0", "shelljs": "^0.8.3", "istanbul": "^0.4.5", "typescript": "^3.5.2", "npm-license": "^0.3.3", "eslint-release": "^1.0.0", "eslint-plugin-node": "^9.1.0", "eslint-visitor-keys": "^1.2.0", "eslint-config-eslint": "^5.0.1", "@typescript-eslint/parser": "^1.11.0"}, "dist": {"shasum": "e786e59a66cb92b3f6c1fb0d508aab174848f48c", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "fileCount": 12, "integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "signatures": [{"sig": "MEUCIDlmvprbqSY/tVPWeX/xkR+iKbcLTBElXSAN7/eI00cPAiEA8nJkzVF4RMxYNbOUdKIJzRhAJHoEiqpeR3rDNmZZkTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXRNtCRA9TVsSAnZWagAAv5oP/3+Z783XScQ9vzSbequn\nIQKq8LiHf27lKQFR2ptwm43xrjEOM7d8DZNB0kbvB55ivqT5kwTW9mIzX4z7\nwb4kcFbQPytSZDzgq6tWTIVWm4SdFVmUF4o+sRDIP+lbTAC+mH+vURVsC5QE\nBxxm9l7c8KsDnqJhnVNjUGp2FGlmbjDxPRCCef/d+HDduzz0tkW7UJpIAwEy\nmUVJKM4K8/w5GjqjAyU6pmgL/iBK0pDcLtknsMyLapsJBxx2RE5WZ0uzssH1\nHSmungKj3+C2qKa6zLb7DVDKf8j3kO1uCRz83SaL0ngt5AB1Ee+mEZmHo3CX\nDHtEVMvsAEeM2qtJDGqx+cUY745MZdeaji7qyKMq2WebmbxgzhU9CoQxN9eG\nYZ+GRYPPKAV4X2xLEUytg9oFbaqv1WjGfAWG09hHeXaiVzBbhN+lfWGlX0G1\nOdhuGq4xlQx6QhhWdYrqNkezjXiwnx0YeNsrHkqO7WdwNMBG/IjZv4rLl2Z5\n0ISFeSZ4rUCsUPLKdSDXYZRaXijp/rR3iK7GYnxVXvUCyT77pcMcRLSqcLZs\nmAEkt96q3KhgsLz9HNM5j/MLEhUFuBWpTrzq0CS4g5TzxulCSmSQh2Izd4sj\nX4O8mXlGJCiHDoQv/6qj7+b/hYAew+DdTu97ptp0VPeYSXGleDyp3jbwsSaJ\nlB2b\r\n=Dmv1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.0.0"}}, "6.0.0": {"name": "eslint-scope", "version": "6.0.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^8.0.0", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.0.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "9cf45b13c5ac8f3d4c50f46a5121f61b3e318978", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-6.0.0.tgz", "fileCount": 14, "integrity": "sha512-uRDL9MWmQCkaFus8RF5K9/L/2fn+80yoW3jkD53l4shjCh26fCtvJGasxjUqP5OT87SYTxCVA3BwTUzuELx9kA==", "signatures": [{"sig": "MEQCIC8vUtAGYmOO6wjB0Qo5SWx/HWuQ4etUNOpNZ41z3qPiAiBiqT9Qd5JLSmXGT1/1JY1Z5lggiy3dhUdh6ISFRU3x2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+v3OCRA9TVsSAnZWagAAjNEP/j8aFD84/fcnbU0lK4Oy\nYYhth4u3SsyEENjEvhd+JpUjbRgoEFBMiDh7ZMKePCeq6Lx+uVr9AsvycD7x\n6168+vXGhYQuBXlYFx31CB7HW9H9cqlYuvogWerjGfEKs/kX7MhW/K5dm0dv\n/vmVk61GwmESjYz1NT5k6NXnwtILgfLD+2oZFTHhUXGGcCeK7LhvWboRCZtz\nH77r3OvpWAzDvAcGdxtCTtE+5fl8AiSsY+giaTqk8Cs2whO8Up4Jma8SqZ3p\nD6U1qXC8wqAz4r0ChSU5043LVhGcATpxlxmlXoSBP9BqGmaa1XazcylbyM+d\n19mQyHTdTcQLpVmSLTX+A7akSVVNSmKxpYtrpx/x7ys5LgI5PecqliIeZkQp\nA1lmDFQSmWkqylO1V0JHoL0oWwkKV8CmY4EL6cZcgd2MOs3qRpcRYHBqB3Ya\nCx1khwHvchsvlPHabKve5EA3KN5gp3PWGQYJS1EdAKx+RLa6awrk0LfwrYo9\ne8qG1w5FhsiWGj9/eMpK7X+0GZcndyDNYvf24etVv7lxChWGUTavvex10WwO\nSGEooD1lDu3l7w592zYzOIAHp+d0H6UcPomtH7BM+6jZ4HUTU4Anxi0XJyCh\nmlSpwXx2J2dT6gxFEm+Hu5C7aEEeWuNC3VUUCI/1RDLaOVKCFmo1iV2rhPB6\njsSa\r\n=TEuj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "7.0.0": {"name": "eslint-scope", "version": "7.0.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.0.0", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.1.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "7028b1bccb4471d7310d8ae86305cb7c8b30903b", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.0.0.tgz", "fileCount": 13, "integrity": "sha512-fquex2G7PGu/aqTwSa5dYYxDIRx9kWZ9hhx8ybhW1sdkieKuKLi8VRyS1LUmFvCMVHjnlSCopMGnPHRmwFOonQ==", "signatures": [{"sig": "MEQCIB05sIiaBgQlCG/h+tpMhPPjAmrbtxYFGKrUa0CmTot1AiAuPMuwaeO5QQSNWmnQDWmNYBEv7P+pywSFt3fD6KKIOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk05SCRA9TVsSAnZWagAAqBIQAJOiHT1fqQmvsh01k3w7\nZrCUf0rlFng6QW5nF4WCwkt9Qdo0Cbbtyc2UlRMCndD3NWr+BEajzdRtaqoT\nvcn5Hog0UDysqH9yIiOZoZvHjBMxEReN0THY6Pi6GGWrAqlByUPUHRMgZ17/\nhfLi7ru0GlJtkLStJ+MPVBQpkj/BP2sb5CD590XMpngr6ZuoDNS/cZlxV7m6\nmAfzJGK/fSV6bEQbKY8voNQjxOhFVuxctK7F7xetfnm/PxHIvFN+acZLAh99\nbJz3ANJPWOjYBQY6rLg11W5emLYRrQoXiPxCdV//zbUc/J1aLCwVYbm+3Y7w\nQNsZfRsqgdzt2yiW2dfJ+Oke+qiPa+XL0M5vhGyezHr0T1MfU7VuTM1+CWLy\ncq4zXr2Kiq1/N9XXyd5GqSage3hWeXxqlMZ23im7uC7CCA8tXtgMDHqff83r\nMo+pSZXZ2c6ukyFr8JGt/J8Qr7P7aFeWn+R4sQ7iPEzpZHBq660v/+LkOf5y\n8YvG0ZETFXy7y83hPcit8YGQTdRIzT/SH+6JY7PJTnZxTXnYHdMDkUt8jwYv\nZlXOzJHrzkmfNXGiScDLIGRjsGe0ESc2T8/5ujEToImuQwDdFp1h76Xj/BcF\nqFAhPynbmeFXkCvqkF76wTdgYHGkHqJLML38AIszXiImjEm2xYk/nrYSjSjc\n2GNX\r\n=Vbgv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "7.1.0": {"name": "eslint-scope", "version": "7.1.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.0.0", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.1.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "c1f6ea30ac583031f203d65c73e723b01298f153", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.1.0.tgz", "fileCount": 13, "integrity": "sha512-aWwkhnS0qAXqNOgKOK0dJ2nvzEbhEvpy8OlJ9kZ0FeZnA6zpjv1/Vei+puGFFX7zkPCkHHXb7IDX3A+7yPrRWg==", "signatures": [{"sig": "MEYCIQDSn3xdrAOik3lIUAhlX/4DWnqKI0oD23fjlqTwpz5G9AIhAOFIFYZvDQO1rGAv+VEc6bIZRW8SYuzT5AZzepyyzIyo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmcBkCRA9TVsSAnZWagAAFG8P/09+ClXrHuncM8I/tuJ0\nu1YJzGOWsZUZKRxsH+rH6i0by0GpboTe+qa28HGdC7dWXjs0UCUddl8pOmFK\n9mhvAZ7J6y6VYtqh1f93pLlJ8MMTgH014S9DplxQGp/Fuup+hkEmWZ0ZHOip\nHL6aqlgcVCtbX3IRfLDKUwKzWd9yyASOXEwtnurtjgyL5nFV8mvf6rb6uoJf\nwJYcfCDNZP+JHlvKdeJ2o5vZeY04ZrdeAGjLiKnIAdyMapcFyEBQPVBZ3E/r\nJH+ajq9IrXU2BmvRf5yVq2kMlEBqG3W7cC+MqTeMScoDheN3BvKWByiMtqbg\nE5e2EjHg6AYsqnOXK+W+WjKCr4yDp7BjjSriDv4tmvxY6Jj0j8d7WXCIWSIT\nRmlYcQM/SgeMLcVnPnyw/gvBMlSXEN/8+EMhPSd85SV+FJrw9XPVpDFP9sMN\n2V+8RGtuw/mrd7/FvThr4HITqVtGox4HUFuVsnSUpELaCeZLYsl7P8Ugj6Wf\nMTTSEw9sLR2EhJ1lBYxx045nc4tJl+vv18/ywamaMR1X93WMbP3k4tSBoJJU\ntJzEv8JbCzP5Ga+KA5/K/CoGk1R0zJLPjo5+K53nJxSst5NBHxHH3mevxaTg\nH2IulqstJTHaz69IKlktw9WAle67kW/m0/4l2JSj2Ly205wh4ESbueIKw77b\nOltK\r\n=iQ2j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "7.1.1": {"name": "eslint-scope", "version": "7.1.1", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.3.1", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.3.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "fff34894c2f65e5226d3041ac480b4513a163642", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.1.1.tgz", "fileCount": 13, "integrity": "sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==", "signatures": [{"sig": "MEUCICLo8mlCmZyTU1VthiRWjQ2qfKUTGqB9iNKwKOxVm65XAiEA7L07fNDeeY7jhEGD0hj7enVRi4tvednMV3ykgUtViA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBvl4CRA9TVsSAnZWagAAwFMP/ig+PMw18T7SdZSgb3sM\nrIqB9Ysfxb/DHJjQKGTNEhtaePmh8p60j7UCu/mGlfjw7WdXRlb+gwb934dN\ncvx0TF/uWXCWrNLUnggSvnTQtgDCUs8sBr8h6fwXXW1SjQvhJow/lQsY9KzU\nrilhWsf88yoAlh/22wPPs5a09CNHfJGf6U7AMFJf/SVpN1aQ7Vb2lNZ3Xulx\n1OOvM8aXd+5qwlepjagYt0z4d8NLMLLMMkjr2eK95gLjQgo+ASbjMfc2rjPS\ncfxYiWAhvZvPaRHXtY1N1d+SLxFt6LxASqoCOc/lIO96kI4Q3WHBzAd+B812\n4b2CIUfxQ9mJSGfXzsChByw9a68lH8Kj/ceOmiADA/hLUir5XgWZZX3xjNwh\n8zxg/oUiBAzRu+p1s3/J9QVdHUMIZkIx/B2Qk8kbtdFPnINSTTDCWouAuP25\njluYIv1vwV276riJcxfP7ij0Cbon5itRguJl+lMeikcKCBiV89akU32TmuyV\n5IPCQ6RlXeQ21zmQOchiKJf7WGJSxqljJGQkjeyJQpHbktigwmAKYl6wCXNT\nO9dn4TmD/PZKUZHIMS54poQ031fFdJ9lhFp/fnaflLDEVekhL4NBxUvs+I5R\nag/6pTocjWcMKcbcOp1K05RRr9Tzc3WidPFaEcs07f0G5Zh7+zuU3p3uNsVq\nfhNY\r\n=1zrv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "7.2.0": {"name": "eslint-scope", "version": "7.2.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.3.1", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.3.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "f21ebdafda02352f103634b96dd47d9f81ca117b", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.0.tgz", "fileCount": 13, "integrity": "sha512-DYj5deGlHBfMt15J7rdtyKNq/Nqlv5KfU4iodrQ019XESsRnwXH9KAE0y3cwtUHDo2ob7CypAnCqefh6vioWRw==", "signatures": [{"sig": "MEUCIQCKGiYNisG/B5p7hoaaGmb0ZXEmOVy1dz/EthMgLiFREQIgbVx1Shc+DDHjCvg+QRg8F5uW8VhQqI6pwWmKhHgawfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOEitACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqeA//aq3djsJTz6PHpttQdwhXl19PFYA04P6I4GIkU33pVWKmsnIh\r\n72YCRtljN8w0ga8swBHtpHsYRvsIVcOfNbfigGi7ErCiPnnRo7BoCeXYBgf6\r\nHJg7cdfeVzDfcFzy7Pwpq5u06ZbEMBvf9wIm5UezZhcLhuCbEu4YFnFQnUK1\r\nhxD3MtEgWn3WibUJpS/Tv/X5g6NgmMsGFdoDC3ILRx+K+6fdNFCQS958njf0\r\noWIiJGmAr9EVGEQInTdGx5Td+B200M9sqYXw2dKNXWkERG+oJuUV+eencTi7\r\nqr0+BOGqvTBC/kfxmv77vCadCLfZ9HZkBqKViW9ZVvt8g/82hVSpOhkcMoVu\r\nZjAMXRtSt/xl8e2gD/hele4XVd2XdefaHxLb9tml3xLOwlVns0tFLD8YkLMk\r\nk/U3xbfFDZTsNhoL4HjR7+kv/JbURZ7OFwCcgwUxnu4jc9z09AU3+Jd11vJP\r\nncI2YHr3oBb42Yj3TMzQfiHRhQEY2pob6tYsxPKqXbCZURkqHIvw9NnLDIS+\r\nSvW0pziTV0Dy8zhbvUJKgKNqXu8tmzko16Jwc/oMEJG9xMWwhmuTEgM0KI36\r\n0B4N40wjL0Ki4Ee450JkOx4U46KTwmQSEk/uVM8C71Wlre6s9XwoXypTwC2y\r\npGErLhqwripTBRsF9Ci0mJdCDR/5WCgZl9E=\r\n=AlQU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "7.2.1": {"name": "eslint-scope", "version": "7.2.1", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.3.1", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.3.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "936821d3462675f25a18ac5fd88a67cc15b393bd", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.1.tgz", "fileCount": 13, "integrity": "sha512-CvefSOsDdaYYvxChovdrPo/ZGt8d5lrJWleAc1diXRKhHGiTYEI26cvo8Kle/wGnsizoCJjK73FMg1/IkIwiNA==", "signatures": [{"sig": "MEYCIQD54S65cw26fTfdlU5Yp/0Vx8nL6ku6TIyYjPI3JyeKPAIhAIUZEZhu6ekjtbSqTOIYbZTPgFAMA6iZ9oYj99bQgwz7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146333}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "7.2.2": {"name": "eslint-scope", "version": "7.2.2", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.3.1", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.3.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "deb4f92563390f32006894af62a22dba1c46423f", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz", "fileCount": 13, "integrity": "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==", "signatures": [{"sig": "MEYCIQC5y8NyrCCYRscdrzSA95IZlmBySPcXL1GtjULEsnkwogIhAK6PVOPckSWS2FcyLq7y426LcQxFmTnIys2A3QKVDmpl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@7.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 146333}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "8.0.0": {"name": "eslint-scope", "version": "8.0.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^7.29.0", "espree": "^9.3.1", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-visitor-keys": "^3.3.0", "eslint-config-eslint": "^7.0.0", "@typescript-eslint/parser": "^4.28.1"}, "dist": {"shasum": "7b6b067599c436404ce856cd2c47331464603a4a", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.0.0.tgz", "fileCount": 13, "integrity": "sha512-zj3Byw6jX4TcFCJmxOzLt6iol5FAr9xQyZZSQjEzW2UiCJXLwXdRIKCYVFftnpZckaC9Ps9xlC7jB8tSeWWOaw==", "signatures": [{"sig": "MEYCIQD8sPfDzEw7JppUoSdSox86WNiRCkH/8c4Ryt5tskmVTQIhANTT7AewDWpfobiRA9VFpTBKhueyD2R1xGkvUwB19QxK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 146508}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "8.0.1": {"name": "eslint-scope", "version": "8.0.1", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^8.57.0", "espree": "^10.0.1", "rollup": "^2.52.7", "globals": "^14.0.0", "shelljs": "^0.8.4", "typescript": "^5.4.2", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^4.0.0", "eslint-config-eslint": "^9.0.0", "@typescript-eslint/parser": "^7.1.1", "eslint-plugin-chai-friendly": "^0.7.4"}, "dist": {"shasum": "a9601e4b81a0b9171657c343fb13111688963cfc", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.0.1.tgz", "fileCount": 13, "integrity": "sha512-pL8XjgP4ZOmmwfFE8mEhSxA7ZY4C+LWyqjQ3o4yWkkmD0qcMT9kkW3zWHOczhWcjTSgqycYAgwSlXvZltv65og==", "signatures": [{"sig": "MEQCICd3Domjt9i6pIYovy94nCj6AbsVu9gvpj1NK1ADatRFAiAQdc2hRfkdNAlZ8r9x6u+FGiY0Hu6gI4GkfS/GqaepqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 147977}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "8.0.2": {"name": "eslint-scope", "version": "8.0.2", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "eslint": "^8.57.0", "espree": "^10.0.1", "rollup": "^2.52.7", "globals": "^14.0.0", "shelljs": "^0.8.5", "typescript": "^5.4.2", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^4.0.0", "eslint-config-eslint": "^9.0.0", "@typescript-eslint/parser": "^7.1.1", "eslint-plugin-chai-friendly": "^0.7.4"}, "dist": {"shasum": "5cbb33d4384c9136083a71190d548158fe128f94", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.0.2.tgz", "fileCount": 13, "integrity": "sha512-6E4xmrTw5wtxnLA5wYL3WDfhZ/1bUBGOXV0zQvVRDOtrR8D0p6W7fs3JweNYhwRYeGvd/1CKX2se0/2s7Q/nJA==", "signatures": [{"sig": "MEUCIQCYRIVgSBhfCk3NYmbhJwsNGJelM2Fc0SIfMq1/bHAK2QIgKo9Oim8AOqQs5ojLQbG93be0FEMIk3iJGRZJBr82R5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 147971}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "8.1.0": {"name": "eslint-scope", "version": "8.1.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "espree": "^10.2.0", "rollup": "^2.52.7", "shelljs": "^0.8.5", "typescript": "^5.4.2", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^4.1.0", "@typescript-eslint/parser": "^8.7.0"}, "dist": {"shasum": "70214a174d4cbffbc3e8a26911d8bf51b9ae9d30", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.1.0.tgz", "fileCount": 14, "integrity": "sha512-14dSvlhaVhKKsa9Fx1l8A17s7ah7Ef7wCakJ10LYk6+GYmP9yDti2oq2SEwcyndt6knfcZyhyxwY3i9yL78EQw==", "signatures": [{"sig": "MEQCICdXpv3y240grChENsuwHABggC8WzxTiLeg10beVzX6LAiAZsVUKyoBIbqaxKfSH6HyrEwEp2mgfV4o11xQBdFychw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 151445}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "8.2.0": {"name": "eslint-scope", "version": "8.2.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "espree": "^10.3.0", "rollup": "^2.52.7", "shelljs": "^0.8.5", "typescript": "^5.4.2", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^4.2.0", "@typescript-eslint/parser": "^8.7.0"}, "dist": {"shasum": "377aa6f1cb5dc7592cfd0b7f892fd0cf352ce442", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.2.0.tgz", "fileCount": 14, "integrity": "sha512-PHlWUfG6lvPc3yvP5A4PNyBL1W8fkDUccmI21JUu/+GKZBoH/W5u6usENXUrWFRsyoW5ACUjFGgAFQp5gUlb/A==", "signatures": [{"sig": "MEUCIBd3OscBZCGnXoIbX/4uEVO3qqmPV2IOFJugvayxnASmAiEArFF0NcuDWUu1y6XFFpjHgmqsC+7lVlBW71FDT+T3E2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 151568}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "8.3.0": {"name": "eslint-scope", "version": "8.3.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.1", "espree": "^10.3.0", "rollup": "^2.52.7", "shelljs": "^0.8.5", "typescript": "^5.4.2", "npm-license": "^0.3.3", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^4.2.0", "@typescript-eslint/parser": "^8.7.0"}, "dist": {"shasum": "10cd3a918ffdd722f5f3f7b5b83db9b23c87340d", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.3.0.tgz", "fileCount": 14, "integrity": "sha512-pUNxi75F8MJ/GdeKtVLSbYg4ZI34J6C0C7sbL4YOp2exGwen7ZsuBqKzUhXd0qMQ362yET3z+uPwKeg/0C2XCQ==", "signatures": [{"sig": "MEQCIGhH/V+tayebtN88RS+3gBSUgOXCGmUN7+urF7CB0D4wAiA1HKC06Wjnu0oLE4v6o7QOuz5vS3d5kMirpY3gXKCYXg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 155422}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "8.4.0": {"name": "eslint-scope", "version": "8.4.0", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "devDependencies": {"@typescript-eslint/parser": "^8.7.0", "chai": "^4.3.4", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "npm-license": "^0.3.3", "rollup": "^2.52.7", "shelljs": "^0.8.5", "typescript": "^5.4.2"}, "dist": {"integrity": "sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==", "shasum": "88e646a207fad61436ffa39eb505147200655c82", "tarball": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.4.0.tgz", "fileCount": 14, "unpackedSize": 159607, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/eslint-scope@8.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIA6b1T9Xdcze1HMNd9++0mEdlixNpCf8Fq/n/M/SKPb/AiEAmb5JqarA85Y51hrChwa/6BThTAnNSLtlkqFCFxSXvMU="}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}}, "modified": "2025-06-09T15:46:11.606Z"}