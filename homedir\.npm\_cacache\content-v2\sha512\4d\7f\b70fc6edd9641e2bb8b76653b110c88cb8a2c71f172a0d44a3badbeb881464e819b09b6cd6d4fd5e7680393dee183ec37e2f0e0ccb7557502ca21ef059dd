{"_id": "ycs<PERSON>in", "_rev": "9-3c683e4dfd75d5632feb5c370e16aece", "name": "ycs<PERSON>in", "description": "CSS Minification from YUICompressor", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "ycs<PERSON>in", "version": "1.0.0", "description": "CSS Minification from YUICompressor", "main": "./cssmin", "bin": {"ycssmin": "./bin/cssmin"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "istanbul": "https://github.com/gotwarlost/istanbul/tarball/textreport", "vows": "*"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "url": "http://blog.johanbleuzen.fr"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "keywords": ["minify", "cssmin", "compressor", "yuicompressor"], "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json cssmin.js package.json", "test": "istanbul cover --console both -- vows --spec ./tests/*.js"}, "repository": {"type": "git", "url": "http://github.com/yui/ycssmin.git"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/ycssmin/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/ycssmin/blob/master/LICENSE"}], "readme": "CSSMin\n======\n\nThis project is a fork of [jbleuzen/node-cssmin](https://github.com/jbleuzen/node-cssmin).\n\nIt was originally based on the javascript for of the css minification tool used inside of \n[YUICompressor](https://github.com/yui/yuicompressor) based on code from <PERSON><PERSON><PERSON> and <PERSON>.\n\nWe forked this project in order to maintain and up keep it on a regular basis.\n\n\nInstallation\n------------\n\nYou can either download the plugin and unzip it into to your project folder or you can use npm to install the `ycssmin` package.\n\n`npm -g i ycssmin`\n\nBuild Status\n------------\n\n[![Build Status](https://secure.travis-ci.org/yui/ycssmin.png)](http://travis-ci.org/yui/ycssmin)\n\nUsage\n-----\n\nThe module exports the cssmin function, so you can use it with: \n\n`var cssmin = require('ycssmin').cssmin;`\n\nThe function cssmin takes two arguments:\n* `input` : the CSS content you want to minimize.\n* `linebreakpos` : the number of characters before the end of the line. If empty, the output will have only one line.\n\t\nExample :\n\n```javascript\nvar fs = require('fs'),\n    cssmin = require('ycssmin').cssmin,\n    css = fs.readFileSync(\"/Any/Random/CSS/File.css\", encoding='utf8'),\n    min = cssmin(css);\n\nconsole.log(min);\n```\n\nLicense\n-------\n\n    Copyright 2012 Yahoo! Inc.\n    All rights reserved.\n\n    Redistribution and use in source and binary forms, with or without\n    modification, are permitted provided that the following conditions are met:\n        * Redistributions of source code must retain the above copyright\n          notice, this list of conditions and the following disclaimer.\n        * Redistributions in binary form must reproduce the above copyright\n          notice, this list of conditions and the following disclaimer in the\n          documentation and/or other materials provided with the distribution.\n        * Neither the name of the Yahoo! Inc. nor the\n          names of its contributors may be used to endorse or promote products\n          derived from this software without specific prior written permission.\n\n    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n    WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n    DISCLAIMED. IN NO EVENT SHALL YAHOO! INC. BE LIABLE FOR ANY\n    DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n    (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n    LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n    ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nThanks\n------\n\nThanks to Johan BLEUZEN for originally porting this to node.js\n", "_id": "ycssmin@1.0.0", "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/ycssmin/-/ycssmin-1.0.0.tgz", "integrity": "sha512-zGTdbVccNMnmumzoMzu25DPaUH2Dm8bHvYDn7w4X7QJ8QjyORrRdqWGCI1HTY/8q8sWgQHSXXkanQEQtm7Me+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5+53oQVcPWCndIKgHpLGvr8XtA+6eGFXeT3EmmIr4BAIhAN/kvKgt1pB1RbCv7jswM6xcLa+lUkNe5liyiBpNC6Bc"}]}, "_npmVersion": "1.1.62", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}, "1.0.1": {"name": "ycs<PERSON>in", "version": "1.0.1", "description": "CSS Minification from YUICompressor", "main": "./cssmin", "bin": {"ycssmin": "./bin/cssmin"}, "devDependencies": {"yui-lint": "~0.1.1", "jshint": "~0.9.0", "istanbul": "~0.1.8", "vows": "*"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "url": "http://blog.johanbleuzen.fr"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}], "keywords": ["minify", "cssmin", "compressor", "yuicompressor"], "scripts": {"pretest": "jshint --config ./node_modules/yui-lint/jshint.json cssmin.js package.json", "test": "istanbul cover --print both -- vows --spec ./tests/*.js"}, "repository": {"type": "git", "url": "http://github.com/yui/ycssmin.git"}, "preferGlobal": "true", "bugs": {"url": "http://github.com/yui/ycssmin/issues"}, "licenses": [{"type": "BSD", "url": "https://github.com/yui/ycssmin/blob/master/LICENSE"}], "readme": "CSSMin\n======\n\nThis project is a fork of [jbleuzen/node-cssmin](https://github.com/jbleuzen/node-cssmin).\n\nIt was originally based on the javascript for of the css minification tool used inside of \n[YUICompressor](https://github.com/yui/yuicompressor) based on code from <PERSON><PERSON><PERSON> and <PERSON>.\n\nWe forked this project in order to maintain and up keep it on a regular basis.\n\n\nInstallation\n------------\n\nYou can either download the plugin and unzip it into to your project folder or you can use npm to install the `ycssmin` package.\n\n`npm -g i ycssmin`\n\nBuild Status\n------------\n\n[![Build Status](https://secure.travis-ci.org/yui/ycssmin.png)](http://travis-ci.org/yui/ycssmin)\n\nTesting\n-------\n\nClone this repo:\n\n`npm test`\n\nCode Coverage\n-------------\n\nWe are using [istanbul](https://github.com/gotwarlost/istanbul) to provide code coverage, to view the report:\n\n`npm test`\n\nThen open `./coverage/lcov-report`\n\nWe also publish the [latest here](http://yui.github.com/ycssmin/).\n\nWe ask that all patches have a test attached and full coverage.\n\nUsage\n-----\n\nThe module exports the cssmin function, so you can use it with: \n\n`var cssmin = require('ycssmin').cssmin;`\n\nThe function cssmin takes two arguments:\n* `input` : the CSS content you want to minimize.\n* `linebreakpos` : the number of characters before the end of the line. If empty, the output will have only one line.\n\t\nExample :\n\n```javascript\nvar fs = require('fs'),\n    cssmin = require('ycssmin').cssmin,\n    css = fs.readFileSync(\"/Any/Random/CSS/File.css\", encoding='utf8'),\n    min = cssmin(css);\n\nconsole.log(min);\n```\n\nLicense\n-------\n\n    Copyright 2012 Yahoo! Inc.\n    All rights reserved.\n\n    Redistribution and use in source and binary forms, with or without\n    modification, are permitted provided that the following conditions are met:\n        * Redistributions of source code must retain the above copyright\n          notice, this list of conditions and the following disclaimer.\n        * Redistributions in binary form must reproduce the above copyright\n          notice, this list of conditions and the following disclaimer in the\n          documentation and/or other materials provided with the distribution.\n        * Neither the name of the Yahoo! Inc. nor the\n          names of its contributors may be used to endorse or promote products\n          derived from this software without specific prior written permission.\n\n    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n    WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n    DISCLAIMED. IN NO EVENT SHALL YAHOO! INC. BE LIABLE FOR ANY\n    DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n    (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n    LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n    ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nThanks\n------\n\nThanks to Johan BLEUZEN for originally porting this to node.js\n", "_id": "ycssmin@1.0.1", "dist": {"shasum": "7cdde8db78cfab00d2901c3b2301e304faf4df16", "tarball": "https://registry.npmjs.org/ycssmin/-/ycssmin-1.0.1.tgz", "integrity": "sha512-nSBxAfGA/RlALXyqijYUnIjMXNXWxYHrQJSYwNqypeULl44J8Z/eN5larw7ZEdYLeUHBgPyilve6hqQtWVVs9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyTOUclRZS+WaBkjdebBhNaMpuvboXK6hQ9txXwOQFWQIhAItm+bltjs1nW8EjJGBkN3c7S7tdnJaBAj+YfvNuEL66"}]}, "_npmVersion": "1.1.63", "_npmUser": {"name": "davglass", "email": "<EMAIL>"}, "maintainers": [{"name": "davglass", "email": "<EMAIL>"}]}}, "readme": "CSSMin\n======\n\nThis project is a fork of [jbleuzen/node-cssmin](https://github.com/jbleuzen/node-cssmin).\n\nIt was originally based on the javascript for of the css minification tool used inside of \n[YUICompressor](https://github.com/yui/yuicompressor) based on code from <PERSON><PERSON><PERSON> and <PERSON>.\n\nWe forked this project in order to maintain and up keep it on a regular basis.\n\n\nInstallation\n------------\n\nYou can either download the plugin and unzip it into to your project folder or you can use npm to install the `ycssmin` package.\n\n`npm -g i ycssmin`\n\nBuild Status\n------------\n\n[![Build Status](https://secure.travis-ci.org/yui/ycssmin.png)](http://travis-ci.org/yui/ycssmin)\n\nUsage\n-----\n\nThe module exports the cssmin function, so you can use it with: \n\n`var cssmin = require('ycssmin').cssmin;`\n\nThe function cssmin takes two arguments:\n* `input` : the CSS content you want to minimize.\n* `linebreakpos` : the number of characters before the end of the line. If empty, the output will have only one line.\n\t\nExample :\n\n```javascript\nvar fs = require('fs'),\n    cssmin = require('ycssmin').cssmin,\n    css = fs.readFileSync(\"/Any/Random/CSS/File.css\", encoding='utf8'),\n    min = cssmin(css);\n\nconsole.log(min);\n```\n\nLicense\n-------\n\n    Copyright 2012 Yahoo! Inc.\n    All rights reserved.\n\n    Redistribution and use in source and binary forms, with or without\n    modification, are permitted provided that the following conditions are met:\n        * Redistributions of source code must retain the above copyright\n          notice, this list of conditions and the following disclaimer.\n        * Redistributions in binary form must reproduce the above copyright\n          notice, this list of conditions and the following disclaimer in the\n          documentation and/or other materials provided with the distribution.\n        * Neither the name of the Yahoo! Inc. nor the\n          names of its contributors may be used to endorse or promote products\n          derived from this software without specific prior written permission.\n\n    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n    WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n    DISCLAIMED. IN NO EVENT SHALL YAHOO! INC. BE LIABLE FOR ANY\n    DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n    (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n    LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n    ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n    SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nThanks\n------\n\nThanks to Johan BLEUZEN for originally porting this to node.js\n", "maintainers": [{"name": "davglass", "email": "<EMAIL>"}], "time": {"modified": "2022-06-29T07:14:48.014Z", "created": "2012-10-15T15:52:54.432Z", "1.0.0": "2012-10-15T15:52:55.485Z", "1.0.1": "2012-10-24T23:10:20.707Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://github.com/yui/ycssmin.git"}, "users": {"zhangyaochun": true, "acs1899": true}}