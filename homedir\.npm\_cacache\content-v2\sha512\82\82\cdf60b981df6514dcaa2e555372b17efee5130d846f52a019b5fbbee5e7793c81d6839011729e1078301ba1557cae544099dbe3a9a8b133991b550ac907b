{"name": "multer", "dist-tags": {"latest": "2.0.1", "next": "3.0.0-alpha.1"}, "versions": {"0.0.1": {"name": "multer", "version": "0.0.1", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}, "dist": {"shasum": "9ae33a85b96ad03399f3283ad54a7713bf92e5df", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.1.tgz", "integrity": "sha512-zwUog43W34sGLmRX6zy2aVixGY5mDbT/GR27ZFplOmS6IuvwoD4rjJAJqtCTdBMplT1rAAQIfhyjoCOGi65QYA==", "signatures": [{"sig": "MEUCIQDOPyduTL/GgIcL2O5KzECQ6feZCy8T4UsabW8lqBHyKwIgF15p2YjYb5xpkOQbpJXmvoMBrybPeV7ub6/Wt/7Io/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.0.2": {"name": "multer", "version": "0.0.2", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "1cc8e34515db9972caaebe6d450f5670781b6d93", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.2.tgz", "integrity": "sha512-LyOxLDAGq3c1H4sfVVG97Nht5AsDWLDTMMLGnNZsXjTSbfX4xmMEYmWknSMwnrpwRes9zlvAxhii/Q5+uZcvKQ==", "signatures": [{"sig": "MEUCIC6S7UCqMEQgJK3VQmojG20dzAiJTXFJhwCburwQyMTaAiEAwJS0x9m/+LADF3V2ONTTpr7+2z45qykmLwFW5a7vmdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.0.3": {"name": "multer", "version": "0.0.3", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "ca7172d214677b82cf3a2e47759cde3acc68f124", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.3.tgz", "integrity": "sha512-EJ/rwgghBHg558EzerEkxjCjcS+aKVg1GKZsUTJKj9ZzksoIZ44cEVZ19iVOKDKG0Wm+9D4H0gsm5MZFojhOcg==", "signatures": [{"sig": "MEYCIQDUo+Y76jOAEBwICtoycr895jLU1NSvPRxfO4eF3zEXKQIhAKYn8IGaWYRF9fyx5gGk91WC+Fqam1ClngBRYxuF5cuy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.0.4": {"name": "multer", "version": "0.0.4", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "1557cd3c930fa26e0f1a5b4fe81f6d7d1d22ffe4", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.4.tgz", "integrity": "sha512-TkvfGKcggxVzQwxfMt39kFhEC+PrYDEgMQKHBQWusOhMiN5QIXI/utkEAyokGaubsR0xk+rrcd6HWXzdLWDouw==", "signatures": [{"sig": "MEUCIQDFXJEGkd/G9x+IFx7wFMiSys838ljFx+gg4eFYb9rVlQIgExoHuO6PjF1X2Ek3+L3h/TBNg6ag9jLQxjgft+FShJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.0.5": {"name": "multer", "version": "0.0.5", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "68266980b8ed59e3f02e6f927b85dea1f9b5091f", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.5.tgz", "integrity": "sha512-pR8IfWXmEs72sdqXhFau9AggIxUB6uHPQjkOB51M7VYpXsGAUc7Uwg1ivzOGU42BV9nPERkZBO/eJWKGIziC5A==", "signatures": [{"sig": "MEYCIQDi4c4aFBYVwh68k/EYTkQ5HmYAyTSX0H4pmpGGLXzhVQIhAIVB4aWkB4oRbG6AlbLnD2uVpBFgHKwCrDnCxkX0lZfo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.0.6": {"name": "multer", "version": "0.0.6", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "b803f0bc36b34bc38136eec2dc852005aff057eb", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.6.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>/uN/bwizGCaXsgEBQ5/qGk0y9N+/9SnFUCCDuqJHh6Pr95smJSyviGLyCOAfRJjB9GA3oAIFA6LyPlWCg==", "signatures": [{"sig": "MEQCIGBE8/qsQIGQK6h04D/ajuB+gSI16c2yzGn1a9vfMx9ZAiARM1qb5I45Mpoo3TTJ4gXxyzA0TzDvxIpTx8UIkakswQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.0.7": {"name": "multer", "version": "0.0.7", "dependencies": {"busboy": "0.0.14", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "a345730b62875ddd51de02244441fcfe06feaa05", "tarball": "https://registry.npmjs.org/multer/-/multer-0.0.7.tgz", "integrity": "sha512-5Z22MwUFct32KmLyypVPXXL7zyMe20Le/7OC2h04IdEx8B8jPB5xDhb2ANODr3+g6UlBQGubWLH/yeWYa2aKHw==", "signatures": [{"sig": "MEUCIFNHM4ASuwTUk13L6INqhaoXuPLSd/xjcP4e8PZboBUVAiEA/sm6OvnQygBh+wGzlRc1vUJWe9usHZEcOgh1X7qfmbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.0": {"name": "multer", "version": "0.1.0", "dependencies": {"busboy": "^0.2.6", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "e60dab05f5b0a26e84b549e8a5586eb8ae178268", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.0.tgz", "integrity": "sha512-2HPqcsdBiwTmwWMrVSAWDxTwE/HZbuhcwZ/rZK6rdgAJT+X1GnfHJz9thaa+UmtY1gd+wnBx+tH5U3URlYh6pQ==", "signatures": [{"sig": "MEUCIQCE8P3TH9Rn474MVylddhQlYmXW0ZclN+7E2KCLvT4CPAIgdIti444fF5/xPIkvScWFrW+BBh/JPwF/T9TL3LvQLqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.1": {"name": "multer", "version": "0.1.1", "dependencies": {"qs": "^0.6.6", "busboy": "^0.2.7", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "b3099dcea773b816ca3f50dbfbc282b09d1ffa00", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.1.tgz", "integrity": "sha512-FMI3I8ZgBp5c3Ba3isd352CcXemki3BAJ+gTu/cGWC8V8mauZPsKulq+DWwzmHpvfzerqkVfFtXIecXJNlrc3w==", "signatures": [{"sig": "MEYCIQD14V50ZNQ5h/6tjnmTORnrBIbTqo1rNoD5tTjryjhzcQIhAOJMCjaGQvv1hfPV8MnQeXGbZIn7v26p9uGghR/xEaZu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.2": {"name": "multer", "version": "0.1.2", "dependencies": {"qs": "^0.6.6", "busboy": "^0.2.7", "mkdirp": "~0.3.5"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "express": "*", "supertest": "*"}, "dist": {"shasum": "ee180bd70641b47c29115acc74ac8c77a10596ae", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.2.tgz", "integrity": "sha512-/DoE+rWV4VMnyFL9laZRoukNjTQr0VNPZLC9/bEdeU9HORn8YKZ7WSS5PKR/BAViGU4no8G3s6p+utqVRJnOHQ==", "signatures": [{"sig": "MEYCIQC8Q9oGYF+nPiNkPxrgIZDSsNPoDhm2F0dgw8uUSFyaEAIhAIOi/Ule8+j6GRITbBC2mbQ5tlNDsxasTWQ7iMLkS13m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.3": {"name": "multer", "version": "0.1.3", "dependencies": {"qs": "^0.6.6", "busboy": "^0.2.7", "mkdirp": "~0.3.5"}, "devDependencies": {"co": "^3.0.6", "chai": "^1.9.1", "mocha": "*", "rimraf": "^2.2.8", "express": "*", "supertest": "^0.13.0"}, "dist": {"shasum": "2eecf57bc8b1028081eaa766922e4f7fd7cbb4c4", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.3.tgz", "integrity": "sha512-L1hK5TCQLEjsHcqsURJw+OZ/9pplyHs8s2DtHlhixTEwj0fIadPB1Xsy+oKTreoawUrC8ucHyxi7QgCkX4TL2A==", "signatures": [{"sig": "MEUCIQCkn2zuWTb6kN+uV9GqnzQ18p9zm9Jj6tXVXjIWpV4fBAIgNXr8TDzA/AWFP2RCFj7zflNYz3+kGn7mTauoMV8ew5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.4": {"name": "multer", "version": "0.1.4", "dependencies": {"qs": "^1.2.1", "busboy": "^0.2.7", "mkdirp": "~0.3.5"}, "devDependencies": {"co": "^3.0.6", "chai": "^1.9.1", "mocha": "*", "rimraf": "^2.2.8", "express": "*", "supertest": "^0.13.0"}, "dist": {"shasum": "ef1597bfdeae8df32674fd206ad5c00ad17d1954", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.4.tgz", "integrity": "sha512-QBW2uVxPIn/1xdamuY5tbkDLlASRlAQ+ZNSrK73mlrP+toGHHdoNyPTVmvbUCXY1q23Uamx4jzQrFNakg5Pk8A==", "signatures": [{"sig": "MEYCIQD9d59wAE5mPk4CBoL21XxBJmG8QvFXNOe+w4c6cS9kOwIhALh0PSd5Ww6OHOuzq3ZGoWYOAF9KniLUFOFExVtjvMUM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.6": {"name": "multer", "version": "0.1.6", "dependencies": {"qs": "~1.2.2", "busboy": "~0.2.9", "mkdirp": "~0.3.5"}, "devDependencies": {"co": "^3.0.6", "qs": "^2.2.4", "chai": "^1.9.1", "mocha": "^1.21.4", "rimraf": "^2.2.8", "express": "^4.9.5", "supertest": "^0.13.0"}, "dist": {"shasum": "06e22a8434262976fd27f3b475b43e83990a0490", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.6.tgz", "integrity": "sha512-i6+b1IoInC7lBgVUoY+L0ILECWuah9fBOU2uUXT/ExxcpOYplOHZBECs86DctvkXreZXCR+2ArgC97rYuP24Ug==", "signatures": [{"sig": "MEQCICssiZ1v91CMRw6PjmP0iiySa7QvTUiawCZM95j1C0+uAiBa1kaCrM8iXPLu75qaB8TFTcmPl7sTqvEnIYiH0bkjmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.7": {"name": "multer", "version": "0.1.7", "dependencies": {"qs": "~1.2.2", "busboy": "~0.2.9", "mkdirp": "~0.3.5", "type-is": "~1.5.2"}, "devDependencies": {"co": "^3.0.6", "chai": "^1.9.1", "mocha": "*", "rimraf": "^2.2.8", "express": "*", "supertest": "^0.13.0"}, "dist": {"shasum": "bc2b7f72a32ad08496e209881ee348f38c495b22", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.7.tgz", "integrity": "sha512-sa7wwNmM3SvoJcd3bGt+99p9T/gJsY0HQ04uRL5TrLfRA3iezG1UNrPuv1JutsUImvQZn/9tIRjwFr4xltQCzA==", "signatures": [{"sig": "MEUCIQD39ZbWXGSTXvGTh2wQel6wvLi5P47vUkb7S1IzlIimsQIgeCTp5LtLcK59UnTIa09td5iLZKXtDVOp9tGZDRXEwpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "0.1.8": {"name": "multer", "version": "0.1.8", "dependencies": {"qs": "~1.2.2", "busboy": "~0.2.9", "mkdirp": "~0.3.5", "type-is": "~1.5.2"}, "devDependencies": {"co": "^3.0.6", "chai": "^1.9.1", "mocha": "*", "rimraf": "^2.2.8", "express": "*", "supertest": "^0.13.0"}, "dist": {"shasum": "551b8a6015093701bcacc964916b1ae06578f37b", "tarball": "https://registry.npmjs.org/multer/-/multer-0.1.8.tgz", "integrity": "sha512-Lk4Md4QpOSUswGJ8WuMaKly5DtpLGe2f8+zXfAiiT4XU/4Fy/qR2C1fIRQoJo93Ld4Wfd9OhSaYIOxe7DWJysA==", "signatures": [{"sig": "MEUCIQDKT4t0WBH2L/T6zMf9JvZckxDNSJJhSsKXKwIHtyiPxQIgByrz3JvZT+p2uLXfHRQh7kEH3UZoMZvGaPHQIEW/uTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.0": {"name": "multer", "version": "1.0.0", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.9", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "append-field": "^0.1.0", "concat-stream": "^1.5.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "df283c19d0d7ff7427e5473591edc891fb8e1d9e", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.0.tgz", "integrity": "sha512-vv7RD0InZATnLlBluh0gSGI2pw99uRQvaBerw4Z7eVDry6y/OB+JlLiKVGquwhGjZreskyn+oniLLHYFIHl7Ww==", "signatures": [{"sig": "MEUCIQC8u89ESQqx6WTisoMmFwVZK6OS3aQOnLGc4EMMs5/fPwIgJV5XC16osZIAOqGMixbxWo7NpAAt6Gb/NJYhcH/D7uI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.1": {"name": "multer", "version": "1.0.1", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.9", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "append-field": "^0.1.0", "concat-stream": "^1.5.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "f0172390b7ed27923ec6a3897fb074a4a894eb94", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.1.tgz", "integrity": "sha512-iDj2vbkMCl5y8mno6tLEf8E2GxBiHYXBslriPBS5qDngTmxsxMof2ngq1p4jsUoh4dDo/vGBsqOaBx0NlqcIpA==", "signatures": [{"sig": "MEUCIEWxDu8cqGKKeSsjMkNII1Kni7RknSeCkD6JQxhJEFocAiEA2215pnCLyg9gSScxqiUazd8Wp/vrZjqG75S9TOEXEHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.2": {"name": "multer", "version": "1.0.2", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.9", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "append-field": "^0.1.0", "concat-stream": "^1.5.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "62707d3f819128c73c2cf24cbc516f5aae85a283", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.2.tgz", "integrity": "sha512-QlVh47KjnoiCd99dPSHvoLBbnbhSJ4AYyWhT/HVUZeudwVr9SQEG40IXxl+4tFW+VAE/NNxeQK14dHSN0K0/9g==", "signatures": [{"sig": "MEQCIBOmO3hGB1eUu5qm1ZoKTnxzyrCB1Ar+hySOS0ZJUqZQAiAnJwVewSt11RVU1qLb9dRi3b2N44hmt1a9gYI4UUrOfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.3": {"name": "multer", "version": "1.0.3", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.9", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "711fef5f94f1f2f0cbfe00224c584989cd776468", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.3.tgz", "integrity": "sha512-WD2EpEb3D2l2ok1aXP9T8Y9vGVDxj7APcK4dlCrgJSyfrWesJ8fsdHatlc5UJ3gbvFBBN4+E0HdzExvWcdlxFA==", "signatures": [{"sig": "MEQCICqhwvTlRElPDuVTKazvuGn8UJgK4TGu3eCQ1Z7yTgtbAiB/H3Mh46AJsc1xg70L8c+t0WR4179w8INIzAAnnJM67Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.4": {"name": "multer", "version": "1.0.4", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "c64822ea1c7f9852a125b2f17eb6d7fd9ba500a7", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.4.tgz", "integrity": "sha512-qpP9CTnAGDG2Y7YAvlpr53px6SSaX8atTj2apli3OVWobkNaD1Icjr+LRwHDFT/4rzlBfkkzV+Prcvm4ysU4mg==", "signatures": [{"sig": "MEYCIQDAEM17fFQ5+uaBZIWnKkZgQ224iP/6QCYpSKqLnmpVhQIhAJ4ZTTc7tHg2iLpXgoUHH5qHP16VQpS/vJmYMch2aEGk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.5": {"name": "multer", "version": "1.0.5", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "adf72db4e97586ce858e73e36918cf4db1cfc522", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.5.tgz", "integrity": "sha512-jECa7hWMoiIwTrRjA2v2gY7SlKov2kfakRoDUXrnoKh0LKOejrz3M9bW06pVM8rVxhLum1Gg7S/5RW1JSYTJkA==", "signatures": [{"sig": "MEUCIQD+S4oiYgnwY/FLc1kalTf0EWelzqVq2BiXQWbC6+z/GAIgMWc+5RqlaeVIijyikjGvL3asQuWlt98ciq33s85YVEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.0.6": {"name": "multer", "version": "1.0.6", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "526524f6bf59a38a54e0621208b5d0a54f507a7d", "tarball": "https://registry.npmjs.org/multer/-/multer-1.0.6.tgz", "integrity": "sha512-dAnI+6QugOgPGlUn9+epVHFcY0l0KFCo6BOfe7S+3T12vEcdKO6h+2fpVXZ/M9zQEm6fanA3i/Cg2/hn0bKMYg==", "signatures": [{"sig": "MEQCIGeg5iVPJDsqXTB56CIfoDUBWhBAiSQ7cnNw0GnUw/3lAiA8MJEqf8V0dvKUpT6jatZ3yGdhxXf+8rltlSEmrteZkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.1.0": {"name": "multer", "version": "1.1.0", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "b32d536343950bf62c6eda7817e71f7376516fed", "tarball": "https://registry.npmjs.org/multer/-/multer-1.1.0.tgz", "integrity": "sha512-J0UXcyIPRtoYh2YDxWO1hFVGSZMl8dwMCik5ZmtdMlNSPd2M6qgnEs+WydAOdw54uJtgrIu8qvRSaK6etpHj6Q==", "signatures": [{"sig": "MEUCIE/5QffYtlCe1ZGOPe8fcF+z1IkiSAgCAzgIQ33OQ3QJAiEA63JaK69O7JKXfyVGCAGkdqnEA8WiSMDRO5ancbi/HVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.2.0": {"name": "multer", "version": "1.2.0", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^4.5.4", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "5be1a45259fb04d2753d33c7c2a1caf5224705a2", "tarball": "https://registry.npmjs.org/multer/-/multer-1.2.0.tgz", "integrity": "sha512-RfOEdAPYxujhOaIaeGmkA7+O6qhus2+dAJEOOi9aFAE6Rh0hRZ6uRdNoZK22mYtIMKuNILpas/p8Q2e4Tt1u1Q==", "signatures": [{"sig": "MEYCIQDSXAhB/v4ufIEPajSeYpNEggQZgYHgWofDBlYat1yNZgIhAKp7s+avx1EtaBJme4zZDY0mwDrwRr3rWqGc1FcFTX1R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "2.0.0-alpha.1": {"name": "multer", "version": "2.0.0-alpha.1", "dependencies": {"pify": "^2.3.0", "pump": "^1.0.1", "busboy": "^0.2.13", "fs-temp": "^1.1.1", "type-is": "^1.6.13", "on-finished": "^2.3.0", "append-field": "^1.0.0"}, "devDependencies": {"hasha": "^2.2.0", "mocha": "^3.1.0", "express": "^4.14.0", "standard": "^8.3.0", "form-data": "^2.1.0", "get-stream": "^2.3.1", "assert-rejects": "^0.1.0", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "f2548d5057a7e939230bf5172c9021345bb66c53", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.1.tgz", "integrity": "sha512-mwxIH4gOW188b7RAyra1PnWmKS9NXFD9pmake2NAM+dCykFvHGFF4g1qP9OFIdMaKnUXu1dWFgX8LTGLnsB3bg==", "signatures": [{"sig": "MEYCIQC+OjPMvbbD2V/Ak4cTy6DffRN1TFx9cVzHu8ens3tRwwIhAOEFVgfpyGGVC63FrAXFWmkgCnPiRx+PJsFlbnLdDmpc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.12.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "2.0.0-alpha.2": {"name": "multer", "version": "2.0.0-alpha.2", "dependencies": {"pify": "^2.3.0", "pump": "^1.0.1", "busboy": "^0.2.13", "fs-temp": "^1.1.1", "type-is": "^1.6.13", "on-finished": "^2.3.0", "append-field": "^1.0.0"}, "devDependencies": {"hasha": "^2.2.0", "mocha": "^3.1.0", "express": "^4.14.0", "standard": "^8.3.0", "form-data": "^2.1.0", "get-stream": "^2.3.1", "assert-rejects": "^0.1.0", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "78ff42c7363acaf5fcedade24616e822c19f20b0", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.2.tgz", "integrity": "sha512-Swz0y2tj9FuvTDi+bOOVQHK5tKMiDNppovBdqlyfhOEOTaVXxV5Vu9karIlSq3tKqT0ITm5Vgp3gdtDqRi7kyg==", "signatures": [{"sig": "MEUCIQCsd7CFeL1ctiaHQY6gooUKHOu+vB8WRgvhDKyLJsPfrQIgRbeC2N6ghpQMhlEt4T2dt9gZsgVGkhgw+317171MA78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.12.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "1.2.1": {"name": "multer", "version": "1.2.1", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^8.2.0", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "759959c6c19f7fb03d0239f1df0b1ecec88473a3", "tarball": "https://registry.npmjs.org/multer/-/multer-1.2.1.tgz", "integrity": "sha512-REvVIbZg2EZqympqwmniqHBcY59UePllh0RMNWk+/rPjfQKkf3elM2zjq2J47cEGCxcUrXCEBs81vkOkUQ3ehg==", "signatures": [{"sig": "MEUCIEFjFrLLgKeVC+KucD2E/AaUy07l4AHRI+VPrSds5wVgAiEA9WXETYDK/aBJT/o6Og6pa2PbgQhI/rrMAvarJz1UbKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "2.0.0-alpha.3": {"name": "multer", "version": "2.0.0-alpha.3", "dependencies": {"pify": "^2.3.0", "pump": "^1.0.1", "busboy": "^0.2.13", "fs-temp": "^1.1.1", "type-is": "^1.6.13", "on-finished": "^2.3.0", "append-field": "^1.0.0"}, "devDependencies": {"hasha": "^2.2.0", "mocha": "^3.1.0", "express": "^4.14.0", "standard": "^8.3.0", "form-data": "^2.1.0", "get-stream": "^2.3.1", "assert-rejects": "^0.1.0", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "893b5f1f5afc81932fae33e8889386c3e3c55942", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.3.tgz", "integrity": "sha512-GgsJT9GUbWKp69B1+w8VcIccBOiQS0m96tDAexmGbBPsmICkFVZKiSBuwds+9fxiiy/rqTTkV1ChDU4ScjuS3Q==", "signatures": [{"sig": "MEUCIQCY6+YuL555Vi8aEMf/rHOQRS9GNHy9rxhh+bjEM5RQFAIgGOZ9hODga6/HzlMEfRxUR10XApIiXGMYVfkGezBOx60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.12.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "1.3.0": {"name": "multer", "version": "1.3.0", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.0", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^8.2.0", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "092b2670f6846fa4914965efc8cf94c20fec6cd2", "tarball": "https://registry.npmjs.org/multer/-/multer-1.3.0.tgz", "integrity": "sha512-wbAkTsh0QXkvqvHCU2qSLEXLuRN7IKMEe80+JrXfJzANniPNgrNcDOMKfGgR1EhL7y7MHIbODVwT7uaVY20ggw==", "signatures": [{"sig": "MEUCIQDtAydmMveqzro0/8++k1M/D944g6YwIs8/6+FXsWxOxAIgJKOig8Fq1feuDZXpKr9eQC2df6fisQp9vfTNbCQ2Mxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "2.0.0-alpha.4": {"name": "multer", "version": "2.0.0-alpha.4", "dependencies": {"pify": "^2.3.0", "pump": "^1.0.1", "busboy": "^0.2.13", "fs-temp": "^1.1.1", "type-is": "^1.6.13", "on-finished": "^2.3.0", "append-field": "^1.0.0", "stream-file-type": "^0.1.1"}, "devDependencies": {"hasha": "^2.2.0", "mocha": "^3.1.0", "express": "^4.14.0", "standard": "^8.3.0", "form-data": "^2.1.0", "get-stream": "^2.3.1", "assert-rejects": "^0.1.0", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "192917f68c3af479c8f8b831600a63bf2c14d65b", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.4.tgz", "integrity": "sha512-nsSkundS0zGpyXvfhTzPpWCJ7gyDBro8sr3jl4Y63PoDy5et+FKLnSVcvT1qoMZPlK24N32mcRWsfPpCeju3iA==", "signatures": [{"sig": "MEUCIQCGMSFb8KNoeK+qpOtOGcC05ELuCDgaPaqlDp7EhdH2dgIgSrsWdBawNwt/pjAUUe9+E3+DhIF4fZLDDjzxQmW7n5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4.0.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "2.0.0-alpha.5": {"name": "multer", "version": "2.0.0-alpha.5", "dependencies": {"pify": "^2.3.0", "pump": "^1.0.1", "busboy": "^0.2.13", "fs-temp": "^1.1.1", "type-is": "^1.6.13", "on-finished": "^2.3.0", "append-field": "^1.0.0", "stream-file-type": "^0.1.1"}, "devDependencies": {"hasha": "^2.2.0", "mocha": "^3.1.0", "express": "^4.14.0", "standard": "^8.3.0", "form-data": "^2.1.0", "get-stream": "^2.3.1", "assert-rejects": "^0.1.0", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "67c7eb6ee49342ac1dd94aa02eaa29cc1721e3b2", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.5.tgz", "integrity": "sha512-tuq3upubBX2K0vnwpoOhuXWUFKfShWJz/Cb3SeYcU7BkWUqmyCY7KKGij1tXPEWZSJ4qoimhDrYaRrHhnGcqzQ==", "signatures": [{"sig": "MEUCIQCL6TDMLHpgGYxi7J3V9OtnTtLh7jsDf1fpDHZD3KfnJQIgetEuECfElgSWxXQWiaklhXrGXWdKRmF6ZICclQJgEEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4.0.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "2.0.0-alpha.6": {"name": "multer", "version": "2.0.0-alpha.6", "dependencies": {"pify": "^2.3.0", "pump": "^1.0.1", "busboy": "^0.2.13", "fs-temp": "^1.1.1", "type-is": "^1.6.13", "on-finished": "^2.3.0", "append-field": "^1.0.0", "stream-file-type": "^0.1.1"}, "devDependencies": {"hasha": "^2.2.0", "mocha": "^3.1.0", "express": "^4.14.0", "standard": "^8.3.0", "form-data": "^2.1.0", "get-stream": "^2.3.1", "assert-rejects": "^0.1.0", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "ef159740ee4fa42cf16b874c7fae90106ff68911", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.6.tgz", "integrity": "sha512-tTAIGlgbcPyIJzbm0DZvyK8tkKGQxL8WYqJ1pd39sDY+SpxO1jU8mRvFj50E91dMAjjXr2lUK8HQ8/eoyEVudg==", "signatures": [{"sig": "MEQCID8dbvovrCGVi1C9SU9cgPNaMvT1UBA0AgleWLfLybzeAiBdQ/hD+eTgF3GnLwcu7NaRBM4VSLmNjOYNVPGPKOqM1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4.0.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "1.3.1": {"name": "multer", "version": "1.3.1", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^0.1.0", "concat-stream": "^1.5.2", "object-assign": "^3.0.0"}, "devDependencies": {"mocha": "^2.2.5", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^0.1.2", "standard": "^8.2.0", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "c3fb3b35f50c7eefe873532f90d3dde02ce6e040", "tarball": "https://registry.npmjs.org/multer/-/multer-1.3.1.tgz", "fileCount": 12, "integrity": "sha512-JHdEoxkA/5NgZRo91RNn4UT+HdcJV9XUo01DTkKC7vo1erNIngtuaw9Y0WI8RdTlyi+wMIbunflhghzVLuGJyw==", "signatures": [{"sig": "MEUCIQClg7kQvWx/43D7k/SLdCb7Yx2DRYFtNKCtOF2w8UYp3QIgAPCSLQla1xkN+LhE7/TYa+26aWZGE5rTrUuUrfz87y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNISvCRA9TVsSAnZWagAAWZoP/3h5psgSUKwGV4c8DyuH\n1cQoy50ytnJaNbvI6N3heSFNCsrf7R3BePKT2EwhjwladqnGdLgD4BDDQDA5\nUhlfZpPM/oESoDsMD4Uu++nIURAsAsq3hz7RWv4+y6Hf70ysj+GU+JvjMJIb\nkUPgm/vVxptzN9rcd2KKY6Es063TR3DDOtBWZAMVIwfGw0fgDvGd9TMsgWVY\nZbozH3aJO1pGHKJ43epmZuZ5T4EIvbAd7QQvjTZKjXYOQUPUkUOM1q7HscgP\nKR0PmHB2J+nclpbck17clFZiyoSTp2uMlC9wwyZP2wwlxc6qkyxHjNuzzsIF\nYuNZRgKtWvN5StJQr5QqglGarqByKn7QlmZ708scsQnmo+0lgn1SkAF3n+yV\nnEUrD+leKaexqm5j73zoVYnOicM2mjLfjgRUT119khskX8sDt4rz8XyrpNNI\ncLChMHmivBaa5InFRMevSM0Qs9kOm0+lSOqjyepNrB3KcuHwIYSm/2iYsmKO\nCPdUrppsmCGDPZYTmL+zpOhpcMUdNEDqupItcVRZrM5P86iioQQ7TwQoEGr0\npX7lzutZjA3hEUHWg/CbsQq88OgLPTmi14A2fDGy742lI0y6++Ef6vufTUSI\n72+N3ZecbA9oKMraAgPc/nUOQDDO2Gm9+Q+O7nSrc/M0MpMms8kxKroJiJ/x\nZAyO\r\n=4tlE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.4.0": {"name": "multer", "version": "1.4.0", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^11.0.1", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "c951616c3f97a709b6294acec3a9952a10d33159", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.0.tgz", "fileCount": 12, "integrity": "sha512-/JATgsDPdb/D9u8A2dFD8MhZYqgDKrWibpA9etSUt8CVzSHtsA13Y8LH9pR2/crsaYFua9g29UBgZB/g6nmOEg==", "signatures": [{"sig": "MEYCIQDJAb/9UedhvKkH2GMa4kiBa9gg+liq4s3utHMZCEAzhQIhAMdlt6gYxmYNUHjOsVPcLmX6KOiTqdUYGwUda12vNzN5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbq34uCRA9TVsSAnZWagAA4SsP/Awyt482LNDmQq8AdtLG\nm+nQ0YJ12XJrxcVzvDCB91JPoPrrmFJo7q5Rf0Gc3RWVVAmu5ZLW5VM7LiFr\nayk4VxebzzJgamn4uqx/Z5IflLNU1ICjxl8WYfVUL34rMxNqfRSqGp8R7sxn\nYReDW2TEVt0gUfl172R8zyR9mGeqaJCXdCGgnLq2L4yLuuQnKC4M3/bDywjm\nO2lV5Y8DdgNv369VDMAnXNsOihIE+YXHj9V2a2ycMqO9k1Dvexeu3mPz0paY\nitlkrVJQ3VI2+aQ5cQMTtRRGNTGE73w323ESTMRYZFm29c/s5H0lWuZpopb8\noa1LmEJhdUKze7AP5ToTQ8zfZbx39hMORpxFtxX5d8oSZ0k0C3D089yOgFTp\nyLxpDWNqhU+cbsiBbZmNFEi9eVFxza0+YfsXleSz3pTkEM5cP7VU0etspR+H\nKid+q/9xBC+nQGNHvbmUBZg00OEVjtnqZvqWnp5sI8Myy8SmnOUt6IAZg3KN\nAz71V8/Et/hbEh9tL4jAhZN40YTfFNYFAdXCqlxL3DndfMBoBgj3oqQ7SXwZ\nODbblivCEBu5Yzi+gFJz3+xAfu7a+8L1G28uK6PMTG2BYBa7C16PTnfhsIJl\nzD1wmVeXVBomsWs8cElQdiYJHobDjraZWUswyDEwLNiSsRu+G1mzWurs6cX8\nFib3\r\n=edgF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.4.1": {"name": "multer", "version": "1.4.1", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^11.0.1", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^0.2.0"}, "dist": {"shasum": "24b12a416a22fec2ade810539184bf138720159e", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.1.tgz", "fileCount": 12, "integrity": "sha512-zzOLNRxzszwd+61JFuAo0fxdQfvku12aNJgnla0AQ+hHxFmfc/B7jBVuPr5Rmvu46Jze/iJrFpSOsD7afO8SDw==", "signatures": [{"sig": "MEUCIQChroQvAjiuOPgm6xbL89H4FStF21C2qNyS6Xt2+v6fLQIgcZN35E92K2wTNayuxvXZBEiBamQaSg4Jvc9XLXGS5A8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv4nnCRA9TVsSAnZWagAALFUP/RZ6j2OBEGqGJ0j7L+Bn\nvP6v3ZziW45wgX44E07N8Qwbva/hcmj7kc3bgZ8QHs/QyRP+JcYsAIL4LeDV\n3EJVBUwwcZ0oQ+KuuQ3HA0O2DReaACpV4Udy7mhXJxSUQhrnM5E05BZYeHU8\njkKsxd7pXvsfMnQGyVuK4yOUQP9JG6AzlEcfZjhHto9Jz2c9FXpk/vYQEJVX\nq/09uUAOfaTrPRgD43HwMRJRnMk+C7SYUlTQDc9LmsktCmYo3Lay2sNgevuf\nkXWko+9T64tjl7t9YgBwgsPO435A/6SvZabIqrh8ZdleeyS01jnBwiW0s+00\nqmsva5aHa1sBu6M2cD848fFnOCS6fXLU0lr174Zl4R+VRrkfW5brPpnchgS8\nERDzGkBMB3Gu3nv6c3kHySa6HYXO4WH5MhIoGaUqZzhrA5lU1yq4qfek4oHY\nYne0g+4lU3JNt/5fJgekW2NIhNCGcRKLWGtohPubzu+8I6mHpHqKVcOYQDNo\n4JrgdPaVaIevES9MFcNaHg0eFI97RKiGLOhQ4njBd1Gvdg2UmXGKwhsbGZ7M\n47ETKLa3h+NGtb7DvD9HhmzjN+hwsvEcZJDiZkzPguxIFmunpT3u6dDkkliE\n1rcmKoYmoEfSy81EUbgLRmJOy/LZarjrmSazvJd+Bjtt02wBaQac4L2ROkZC\nH6Ft\r\n=mCZl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "2.0.0-alpha.7": {"name": "multer", "version": "2.0.0-alpha.7", "dependencies": {"pify": "^4.0.1", "pump": "^3.0.0", "busboy": "^0.3.1", "fs-temp": "^1.1.1", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^1.0.0", "stream-file-type": "^0.3.2"}, "devDependencies": {"hasha": "^4.0.1", "mocha": "^6.1.4", "express": "^4.16.4", "standard": "^12.0.1", "form-data": "^2.1.0", "get-stream": "^4.1.0", "assert-rejects": "^1.0.0", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "78b5a8243d4732bd2803dcad1815b3523fed18b1", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-alpha.7.tgz", "fileCount": 10, "integrity": "sha512-WNrfhCLH2IHn3ErqSS53pCU9Q4CSFbvyMMEC/dm9C1Tz/T+LkAcKXL9Y+7nHnw6e+vWDjcz+gZasBDpeMZBTYA==", "signatures": [{"sig": "MEUCIDTSJ5jyVjiCImS+uUatac9x4FrPtenVMmBI5DNlMKroAiEAwNUI9RWdRIcmzpHsBCQWaj2TLxgXYcFYGNg01IRt744=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczGwJCRA9TVsSAnZWagAADOUP/AsVNqNppeXtwUj0ogCt\njtnvpYxmb8WSr0JIMQi2/wBAawEGenlGGQMWp8suLEPGoHyorSpUGst/7LEe\nusZSCB9esPspBZj3h+L8rVrFWzUqmhzHdrHSjeEU9fdcnFRga1y4uzWC4vBn\n4vZLIPcOFVuqiN3vPAN9gMtP3YdSp5shu/WxekijX4aceYs8nNWr+mIWPXEt\nc+OaT57IDAE3vG0r2p8cYlrdzQPCy/0Z0ymwkgSDa193sVkKDCcKhwrVZuE6\naoq/RKF2MRSEVn3k9B0sktlItWiAu1vCq6n+UfwBnqEQnmcv2cmqkRYbEo6e\nyC9l4k5tixeQfC3w2ri5PbvG7QWlRm1ioZAyd9MVZ5zpv0Mi8TWCVbxQQxiY\nf68oGN1Je+RRUZ1l4D0lGblmiTGKD74e7JVSvMfyu3PnA1OdoSZv+rp96oXv\nPSWdYr7wbtW+2tR1AM1jDYoCf0czS9gVkuLzRwDTLJTkk3mzpE45S76mWhBy\nejSEMOl4LbbYV7nxGWHJOQko/HUHZYz3IQElo+2YZoxan62pPGv1Fu8mTlsD\nvodpEv4reDaxOLT7gPNeS62kaNKx75WdCNp+A9wVusCcUKmyR+XQ9s3fzXUu\nfo9GENQoh7dp0l3k6PTQFPkv1gAAOnWZHODgj0Nw8IPMRGvY98PCIzib6vZx\nSoth\r\n=PxpI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.0.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "1.4.2": {"name": "multer", "version": "1.4.2", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.1", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^11.0.1", "form-data": "^1.0.0-rc1", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "2f1f4d12dbaeeba74cb37e623f234bf4d3d2057a", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.2.tgz", "fileCount": 12, "integrity": "sha512-xY8pX7V+ybyUpbYMxtjM9KAiD9ixtg5/JkeKUTD6xilfDv0vzzOFcCp4Ljb1UU3tSOM3VTZtKo63OmzOrGi3Cg==", "signatures": [{"sig": "MEUCIEaiq+jF138WuOWwvmLvmGpWFzoDIVNQU25YcWDSBPwVAiEA7xFAHwhDu1cFK3QfRyqaGovt5j6Qjzzq/DxM7+095DI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLaMgCRA9TVsSAnZWagAAElcP/2xvo5Nd8AegdY0Nyrg6\nnbXmVVqoMtPFxRQxCKVfNCuJ0vjzKW84xJGF3KpxNbn+vKIEZyAtvT14lX7m\ndqy8hs4Z/b2+jY0NwU47OQvVQpvONqhaiHF/IqdJD/Kc93WFtPd1KX4Qx2Tm\n0NGwa6I33DltceOrv5PosTWhCQOWejveEH2aFXMV4uUiXc6M2xQTwQmTgXdu\nW8zBXSTeqXvylKOurptMjxp1h02RsdEq3KjcnQ14B/SDKNDGJMQVtOIVAt3K\n5zoRjxOc4C4dnlArDUdzCTe5MxkmQrID6dVwtKv3BhG3zhjvWiaSFMe1EVjN\n4rFWDhwvC7V+mQl9LJ8Y+WmM/k390j+g+hALAbUgMiE6M52ZY+vtfzgr1D4d\nK6InyXN3ddNOTBqErpnIccgD1/VLazjhjOBuvlrqGlQmuBpP38oOGTifTg1d\nNFlrjXZOEK81zWKErMmTNz6UAjUx+vWlZ57bLSgv5IxeWY6XoaqG7MZpGvdg\nlhZXxZ/FXMvEBMKQtCwNkEvWjaBOYOkhkD8v+uj7dkrxkFfK8yW9xpcGB+QC\nS1bd58TEjjhuRL3sOyJygvAfdq5JvLJrbaLo0HA+44Cr0kHsVCpH9n3nkA2X\n9TAyp+UJcGiziCX93kdJVmaqfSQkbGD8n/MgSYVfz9/J/+SQS01KuACImOH5\nZonw\r\n=PJqq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "2.0.0-beta.1": {"name": "multer", "version": "2.0.0-beta.1", "dependencies": {"pify": "^4.0.1", "pump": "^3.0.0", "busboy": "^0.3.1", "fs-temp": "^1.1.1", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^1.0.0", "stream-file-type": "^0.4.0"}, "devDependencies": {"hasha": "^5.1.0", "mocha": "^6.1.4", "express": "^4.16.4", "standard": "^12.0.1", "form-data": "^3.0.0", "get-stream": "^5.1.0", "assert-rejects": "^1.0.0", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "d9eaf52888b5180ad075ffb6867e417f465e8f75", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-beta.1.tgz", "fileCount": 10, "integrity": "sha512-Rs+nvJwKuPtbLDjOe9AidjfipIvWf7xw8kmts6Ri3h9EC66AzHV1zV1+Ch0avPdPbjzDriHFeo4G7UAQaqPyPA==", "signatures": [{"sig": "MEYCIQDeCwrNwuTSaaZFFkbLtOH4zI6TcTbs0cxKQ2xdydjUZwIhAO+ZgEoO24+tRcib14f8FWyZOXEDWcpDtOfKJfInXUkB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2U9JCRA9TVsSAnZWagAApfcP/i2meNBBjk5CIiyFGhGe\nFvbiLzVU0B6jHMzmhhdrKJXvkbDPT1Sw8491gLVl6aTBjVAhhvbSUmFQ0XlA\ntJFiyVGReB3VlSKSjwvKzEKg7SGUz5Xii8xkD+54i9BMuGS/9HsL/XF6JY0e\nuOdoNYxro3vZnPNbd7UBJgBcFhvv/BX7+TM3DdFjFVqXIjFaghur6KUEDP29\ntuBDQNW9Zw+289fICTZf7/6/e4aalq7vPeZOy5qwPgzA2QHyeSXqxeJcSdhk\nKJ/rh4S/WOhTLmOngwf1WdX/MqgVrWwVMGMhpskoWbh9UBBrXi9uz/GqFNbC\niChwWz6rG5/0UB59x2EQejXiyy4XabVbB7RjYx0pN72SSozPOhLcTYsuLsYx\nW2jVK1VBUAehfFAJT41YaIoqONcsP1QKiBUAB/Y6jlyd360ZOtsL6/bp8O8q\nAfDRemSH1vhklbAWjFtehJpSTraKW2iee/kJEK+cNkNj3FnsBaWroNXv/6JA\npjWu0KwT69rSqSV+zO5bjEq9jgAYEME/+vgyS9mEAuLHFtO848GJifX2OE5K\nwYor5sYwBtNIEIEVK6dQMtPYdZIuQW5+uL/4pxtVk4o2IBaYk9TK54J/M70Z\nu3kZtU/t6PYy3p+zOtyqdWDb7W3Kuw2XaA8YW0ie8SZoAzcSvhOP6WCTnJhV\nlMIR\r\n=O02X\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.3"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "2.0.0-rc.1": {"name": "multer", "version": "2.0.0-rc.1", "dependencies": {"pify": "^5.0.0", "pump": "^3.0.0", "busboy": "^0.3.1", "fs-temp": "^1.1.1", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^1.0.0", "has-own-property": "^1.0.0", "stream-file-type": "^0.4.0"}, "devDependencies": {"nyc": "^15.0.0", "hasha": "^5.2.0", "mocha": "^7.1.0", "express": "^4.16.4", "standard": "^14.3.1", "form-data": "^3.0.0", "get-stream": "^5.1.0", "assert-rejects": "^1.0.0", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "328df63d3746ac9b4a706c0336c157aed4b7681b", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-rc.1.tgz", "fileCount": 10, "integrity": "sha512-nXGowAUhO2wlHoxqFSJwvX/TbGUkrYP6UIZ4D1gtR/DuKU0k1qiVNEgoaIvDnS17hB05V/AyaRqa+57lHNm2Tg==", "signatures": [{"sig": "MEYCIQCvQ5Rm81IgBhUMNw8iGiysHUvqrt18TpeW/+YsjbjSXgIhAMvbAtS0ykq23nwlOtD31LKeND8Jq2KpIlykTqSnEuyg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVu3MCRA9TVsSAnZWagAAqIYP/iycv3x8YCzSnhcE0s/u\ntqm0aNdwUqwSrthIiFr5nyp4XVN5+7B6VlST2mrSd8tfhdf4TWEc0azQavT0\nDvzoXNI45aG4iGX18PWIgGCT8t11tCFzh97jnmuuq9rAtDTZWRX9I2KF6d5c\n9Z53C71bsD3JK5e3xQc2Lrg/lcRLASRCfmAtFjJ+NWMCFgMlL6sgDU54XlUE\nlhHRhrMrs9w03WHDbyF3fQnAMItQd+FwcBkhCKtANOt7w6X6b4V68xc8oUXv\n7RoIdb77o5KPV7YrQsqNtH0ZoSwD/KuD2bymV8uDTjgRfjmobWZfCUYDyCrX\nbWvjCluxG5+xMvwwoKGGWBH9nncgmp2+aY6kowGdRIUdrNjsoo3QiymNhTfh\nhRvotMr5P5iFNkAlgxrRBeRqK0NTApQLtDaMdPo41Y6bXmPfMweYSz2P9sDl\n9vhtFDjIU+OQvbylySFSv2UhI6tuU+rdnepQazlZBxpDsAZGpzFUaLlxwLh0\nS+xIdqrLcx1vxY183bIHJQuEGMUMPnsCwuwZXUAc35Vvkvh3IgD7xXfn8aUy\n4jEi+CmDEDUJNMLMpHzSvIsBvmknD+Azp16QQBNUOovfS2k3m4NMoEBNfXQ4\nxLH82/lxSC03sF/Xzld5+fU8EAKvgNkKbSsS7FIl2datB+x2P6BFcLLh1GhM\nrVaq\r\n=oL+I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "2.0.0-rc.2": {"name": "multer", "version": "2.0.0-rc.2", "dependencies": {"pify": "^5.0.0", "pump": "^3.0.0", "bytes": "^3.1.0", "busboy": "^0.3.1", "fs-temp": "^1.1.1", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^1.0.0", "has-own-property": "^1.0.0", "stream-file-type": "^0.4.0"}, "devDependencies": {"nyc": "^15.0.0", "hasha": "^5.2.0", "mocha": "^7.1.0", "express": "^4.16.4", "standard": "^14.3.3", "form-data": "^3.0.0", "get-stream": "^5.1.0", "assert-rejects": "^1.0.0", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "52cdf94b67e69636c30bc2a67bb95676f17ad445", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-rc.2.tgz", "fileCount": 10, "integrity": "sha512-IjQe1wZoANXrZ0A7cED1dxUny3BFezp6jajZ2FDjP6Rjfxib20WPbWYIPe2mjq0enipuIqz7XLdAkbqO6+oqzQ==", "signatures": [{"sig": "MEQCIC6CRHJTK2pyhKmrBReCsVTS+Zov/O57NUrFg+9OJGZvAiAmDZIah64u2wOsvotRh3E8CZLph8cYniKbBrCW4wRhPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeblfBCRA9TVsSAnZWagAAbaAP/2I0J6aCMSWg6lnuGdaB\nu30zlkOd1ylZSnXnZ7cSMFo7liA06z+7Ykx8DpEZChTN+VKAHXdE9c1Qzxal\nNK4CkKchZW4KlCcNlGEgLikW1R9g5yVRcEZbdogK634Hw7SFQRTBG5ZgehBq\nW8AzsTU5MQBaVYm3i+Z8/+YH+qhbZ0TF9/5Fd6W211fFgq5PUuy74110h4z3\nKXT/NORNFCG0LpViz3gmphGVvxAgy7NBEox+tw41AFY275JAcOpkCUOBppRJ\niCdyp1rWvojORE7h+1Ngjrrrm6Uelp5jknsU4wMTGlH9xbDKnbuoUXpfixsN\nvy/iEprqoCnTRc5eWsNszghnlwDHlpouWCAM8QxLFElacNDuW+Q+/JS5kqoc\nypK9vwmfn86jYMIdzC4+IN7z/L/nNhEsECfdn6rYZyIKqCgtCVvrnqvNmT6D\n5CdT65MTy/xdeO//Nfzchq8TuibFVFiu1ZBs7++sB6xVg7aLXJptbldXZiiM\nj+ADtpJYAIv4obtJZVjfBEphu8ct0lr4u0dNa7hjzhGlUcUoWkGFXj5Y49wR\nH3UjOIuJHQcDx7+3yhqDbgQlZE1zITHFZEtppP9ZSI7OImgiwc/Ve3WXbhrT\n1xpfIYYVz73c5cVLBmrSn9aLNtOGKCkweZNS2eARMz9ClfE5aAyCjazIU3VR\nnIhD\r\n=W4lr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.13"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "2.0.0-rc.3": {"name": "multer", "version": "2.0.0-rc.3", "dependencies": {"bytes": "^3.1.0", "busboy": "^0.3.1", "fs-temp": "^2.0.0", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^2.0.0", "has-own-property": "^2.0.0", "stream-file-type": "^0.6.1"}, "devDependencies": {"c8": "^7.7.3", "hasha": "^5.2.0", "mocha": "^9.0.3", "express": "^4.16.4", "standard": "^16.0.3", "form-data": "^4.0.0", "get-stream": "^6.0.1", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "4f4a8c62ebf6c679411cae22d770db309c2b38e3", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-rc.3.tgz", "fileCount": 9, "integrity": "sha512-wg2wN2xaS8Xrowj9y25jCVvhUQtIXNzVgWd1VIb7kdwilzpnA/teiZbh0KcOfd3y+RR1DiCXIzmfnWeky1i0Ag==", "signatures": [{"sig": "MEUCIBCamDQSZnKomx+UpQy6BhF4zWFfWoH7u3d4eqGdi3wEAiEApvgCIpfW5w0Yb6NP+QPltfNGOqmiiG2ZU0pgTbvQ/OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAuTPCRA9TVsSAnZWagAAgxUP/1uuIXZkhnokXdE5OT7t\nahfbdCXj44Eza+EruOuBdUElOBnblnlGCX/CXrCNyvC+zNCu4JRr/9SR+RKJ\nvlBPCt4Ug5mc7XRqjeXUcNVvbum4o3N+7fkZjYrRTTA/9xRw9sKMRNaUZyku\nQKjUF9z7hONKsk0WiMafBTjGLKoaWTFtCeGCf6ZN0+djt5T/jvyjrhmAXRC0\n5QvN0brYLrZPQ2jEseKiaMEnJtUvjgU4CwEPY2bQcMGp+9wfyf2KfmQJeLSn\nqDgHUTRlQupv2stFojoVn0KHcv+Oi8iZJz9GkmEI89SDsFdtIQH/is0Qkj0f\nzPj77wCYtmrVMHbf61iwsJcAdLdpCXX40mBIhY5Qx8RnPf40+/1f2E6rMaVM\n1msqQls6qjaHlXaJOCsDxqEXjRDoUZeVKWUvLto7/9YvVLctDWAGHflgEZgp\nFMwxQi2dbdwrXSYGbeQxCE8LmrLPLpkTilZM5C0QjydSZcmliEXbeaiFfMHn\nqPl4W60YqMdYHGYH7MC6CVq96y47AbrUOsHs6keqR4VZ26dwQqZcTTjPh+LP\niKvzKRXysFHTUi6urubLFlcDIsDv9el/51FySMQBhzNByp7/Rsa5Toc3J4UA\nAfxMVZrftcYni+ivfwceZTxbz7CfnrngDrjRoL8A924KCrDQdn2FDPmkUbLs\nt7/Q\r\n=t35H\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "1.4.3": {"name": "multer", "version": "1.4.3", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.4", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^14.3.3", "form-data": "^1.0.0-rc1", "deep-equal": "^2.0.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "4db352d6992e028ac0eacf7be45c6efd0264297b", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.3.tgz", "fileCount": 11, "integrity": "sha512-np0YLKncuZoTzufbkM6wEKp68EhWJXcU6fq6QqrSwkckd2LlMgd1UqhUJLj6NS/5sZ8dE8LYDWslsltJznnXlg==", "signatures": [{"sig": "MEUCICaIUDYeX4Wl2rwYX2i035TB3rJRb75Pz1rZMHjWyDMiAiEAxaQ3i308JlY/BCi7pBUJ2qmSglaLP6vEprCEhktx2ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEOtoCRA9TVsSAnZWagAABfQP/jqBQsakCASZex6/4si4\n1ti5m3gLxYNPAXjqqgMUkOyrgdrUMINMmzALyi5F1qdvXklGdVAjw8TszNVg\noYvX1PLliRXQwJQqaWvXStNEXNeRWy73ABUrPw+QSInWkqsAZkHp9lcNsImr\nBxQ7SGbZ+fmM3hMUCjepuSZa5Kzc57qNRq7rTN56YGBaOAOwi94b7D8t7u0I\nkMXNGfu9Kf5L0nGelNHr81RmBZpho6QOkz9KeDyNQGZeIhOZvAEoWZmu9YCV\nsB5OqLY1fSbVk+ofAcIXtT+izkhx0KFnPfUYiQiKEMB7tU1W24L97elZlZ73\n4DnawL6MqczNk3G78yML7ha9GT+rrVySl1Q1paqxgLjCehnLWeDkGFhmd0by\nQ0b4PaDC3xWy51X9eKuxlJjeeEK7MHHscGO/QC1iyY1VzejC03odQcyqyLlc\nVeEYHG7rjQRnAVUHuv0BZ2pGeMP3ND3nLIkHJ1XToJrPmWrDDg4b/d40qawD\nabAsMg6un83MKg8hG38dZNK1Y3T+IhrttYj3LUuXo4QNObx8DTr3wDxaE5GP\niOSFiwJSwIPj+Ppfca5JFFR05uhf4VpYW3kCLi/NsrfRL+DrtMqDaKY2jhMs\nP57lOOiR2lP4OZQWL+WImyZX6yypgf5SNGKmr+YZalRqa1EAKVDC5viJwHje\nVzfM\r\n=Tjvo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "1.4.4": {"name": "multer", "version": "1.4.4", "dependencies": {"xtend": "^4.0.0", "busboy": "^0.2.11", "mkdirp": "^0.5.4", "type-is": "^1.6.4", "on-finished": "^2.3.0", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^14.3.3", "form-data": "^1.0.0-rc1", "deep-equal": "^2.0.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "e2bc6cac0df57a8832b858d7418ccaa8ebaf7d8c", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.4.tgz", "fileCount": 11, "integrity": "sha512-2wY2+xD4udX612aMqMcB8Ws2Voq6NIUPEtD1be6m411T4uDH/VtL9i//xvcyFlTVfRdaBsk7hV5tgrGQqhuBiw==", "signatures": [{"sig": "MEUCIQDCOCCjPTzYg1t/Ud699T/rMnXoAXTYiEKspHjDXb5VXQIgIPt64K3e/f+ktdf0I1UZcBQ+/0PrSnXbElWYZQ3QfpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr0ftCRA9TVsSAnZWagAAYpgQAJ8DiCqRL7+/2OnHngAM\nmomu67//tvcY5OMFZc4aCpQ6M2RbCwbspUheidxc5JtmMe7d+yGiNbZ7CLTG\ndL20Gu8J0k9g4hSDgfFFKXGEFwofYxGd68MpQrzQFpqWRXwsuQ+qUJruGHuX\nOeGS2WizGQr5w8pDJAmd7fkCHE6n5D6mAhvYvWSVx2MYMCQeG+RW8UDr7UmO\nILZpd4nTdIuDHaNG9CtupY1M4OvRf+2FKpeQ1pOS0Im/Q1C8rp7YfoLackYd\n+jJGuKmKX2BEMAwrcdd1lmZt3rVANvtwLjc5tr2PESCyXEscFPSdNgGOw085\npi97hVhHQCOMgBsT0cWXgx2VKwuGA0Xp/NLd61PS8nV4E1ZaUda1R6W76tEC\n6tZI7ysxE0g459MCeABx0LGbCvMthr92WsCR9TX7FfYa4yxpBAqurYJbMFlu\nYkrCMbES8yJoECI6dscyyCWy7bk8rXjaZ/Vtm8xQ7RunDAx+ifNelmxStM08\nSMBeyPPMyLucAOIyRG7+zH2Af4BjOxeLwPashz6p4UqQ2OUX3dD2ddmh60T6\nhEnkOqwjS21JhuhbrjODw/4wPWvY6qlQI1MFawmSXTVr3a64iFChZUhAuC2u\neb4GAMwo9Wzc4UKX5mfMv/hn04ZmC/S/OlzwTRLa92xgS7kRpzEE0Jwk5BTf\nibYf\r\n=vvDs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10.0"}, "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10."}, "2.0.0-rc.4": {"name": "multer", "version": "2.0.0-rc.4", "dependencies": {"bytes": "^3.1.0", "fs-temp": "^2.0.1", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^2.0.0", "@fastify/busboy": "^1.0.0", "has-own-property": "^2.0.0", "stream-file-type": "^0.6.1"}, "devDependencies": {"c8": "^7.7.3", "hasha": "^5.2.0", "mocha": "^9.0.3", "express": "^4.16.4", "standard": "^16.0.3", "form-data": "^4.0.0", "get-stream": "^6.0.1", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "ae3b4e5e7c2c89bd57193247c9e63b71093cbbe5", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0-rc.4.tgz", "fileCount": 9, "integrity": "sha512-YkjV/rOzngEBxtZV8s6BcBcB/z2v/dfDcIpWagmMsKBq0QnQCu/NpCUDd2ElExfnxRXMVXKrFh94/UKe4NdxBQ==", "signatures": [{"sig": "MEUCIG/Sm7EGqBjQuG3FpRKvG1vwco2vOXb6sCq0LBq3PyIiAiEAqvZB8q59zvPDKa7Ddb4e8nX8PPSwQ9HCy8wJWtA1afg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJWeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNHRAAlT55Q/EOhn60sQzUGac4IovfSpW+waoiN8vL3Jy7t6RrG2bT\r\n+04ngB5FyoTn+KxdO2VOvi1eAD6hZCDzZwTKfAZ5y0aUwrLsX2TjU68RQjy5\r\ngjZCbCbP7GqDBoBp4mkZkB7a1RizxVd6zvJdvcFYI5DeRgDDYeB21LuxKfmc\r\noReqWeI3j0HekLIKtzxwzPZia1d8l/lhlFM8rSO+tZGlSDEg32SfE06iKQGC\r\nTVtyXsF5P+wAvQ4VXwoqQSd7WvPSC3CPGE7qOfBFhGRV+2b69e5GO8RuWXQY\r\nAW7WJ7/z39FbVxLqUXYooJSlCBdptj9nn0fK6QgX7NWPtovfq/Tn2nX6hg4i\r\nV0j4qw9hr6pXtgta/mXEJ6UltoywG8/8yi0AH+ghE7wYdXoNCG5quSwH4rCQ\r\nP9PIG1DXx6Lpgz+mAKuChOvuCEtF3mcfVxIhM/YI1W/G/JpBuF7zHRcCCEhH\r\nqs0pZnlc8KA9B4KUEVLVJ3FTElDGXZ6ciPat6CfZbqtcZX+vX39u0ca4V3nO\r\nB+PtfxcYOKX7tG85TheHRyynAvrdidD9AYZS9BHB8fafSNqqt/9hUKIY5vWP\r\n4ybJW6jSKdFieffSjUXmP8F/THWXrfsRPtSe+DeKwQx3lanuFYgyPGKmyDZC\r\nz+4rjjuuF4e13n2nvwvwT2l+kCkyBWoxBk8=\r\n=0GiV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "deprecated": "You should upgrade to 3.0.0-alpha.1 version"}, "1.4.4-lts.1": {"name": "multer", "version": "1.4.4-lts.1", "dependencies": {"xtend": "^4.0.0", "busboy": "^1.0.0", "mkdirp": "^0.5.4", "type-is": "^1.6.4", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^14.3.3", "form-data": "^1.0.0-rc1", "deep-equal": "^2.0.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "24100f701a4611211cfae94ae16ea39bb314e04d", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.4-lts.1.tgz", "fileCount": 11, "integrity": "sha512-WeSGziVj6+Z2/MwQo3GvqzgR+9Uc+qt8SwHKh3gvNPiISKfsMfG4SvCOFYlxxgkXt7yIV2i1yczehm0EOKIxIg==", "signatures": [{"sig": "MEQCIDV+K1F5fEPYOsfuoiG5G+m9+y7wZb3E6zygB2sbxe8tAiBoJlfukHtWm8m86wGn9XKoNz96PE40WckCaPo6DRNrhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikx++ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsSxAAi6C5XjBOwSdjhOKOL7r0ASn9k0hi+Syg292KbawQQfgHIJyc\r\nXB1nwomtN8e+j0WEp4HElXQVgbs60OE5CfZXz9RWUTgQZoE4IPj37uDNqsiV\r\nah+PISMgUvkX6iaqICbVaVHl09/18YsIUQn1UVV0QcCuF6uyu2ZD+iXzgY1z\r\nnWCCHdBgnL0ayDKbF4RNsph7a/iP3DoAGjCFIvO1Se1fL6FLME/ROTgR+l3m\r\nSn/9UYGnqDkqzmlRZgDnOyYKTkV2KqBpxoQaai9D47iiGGw3KNIxvJrSZSid\r\nLKrOFgzmayZSaEhIj2udCvttYDFyjqyFtpfSNsCjnPfRQo4GEjGGup8Wjs9b\r\nTVu4AQUfb/DLG6zEGDJSzrVgdk+DxJzq3rqqVGaqx0He0V+tbpWMe8ju0kvq\r\nmfg67Hn7gLMRlMnaLCa0lmaai9A9HSiVJzzmGM3Np8j803tvM/ta8QbADcAs\r\ncP03VPvgCqbVo/QgX5wTbCy6zxPDCCWCy94QhxtNLKUZ+u1NoHiBWd3WzczQ\r\nzgHCMw9dRw8TNpIJr1KwD/ZvxA5GGPj92D3Rpi4dhSH19OBTbutRb4sYqVrs\r\nafycDNif19o1IsMuQvWTyyLRxDNgMotvWjO50J84R+c8sLtlzVf6EkI2X7Zw\r\nb6fsfejTFBjwGctj5uyEn3DcSF92Fl3Srus=\r\n=Gm1J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.0.0"}, "deprecated": "Multer 1.x is impacted by a number of vulnerabilities, which have been patched in 2.x. You should upgrade to the latest 2.x version."}, "1.4.5-lts.1": {"name": "multer", "version": "1.4.5-lts.1", "dependencies": {"xtend": "^4.0.0", "busboy": "^1.0.0", "mkdirp": "^0.5.4", "type-is": "^1.6.4", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^14.3.3", "form-data": "^1.0.0-rc1", "deep-equal": "^2.0.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "803e24ad1984f58edffbc79f56e305aec5cfd1ac", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.5-lts.1.tgz", "fileCount": 11, "integrity": "sha512-ywPWvcDMeH+z9gQq5qYHCCy+ethsk4goepZ45GLD63fOu0YcNecQxi64nDs3qluZB+murG3/D4dJ7+dGctcCQQ==", "signatures": [{"sig": "MEYCIQCd5d0vXv/TNUlEQ1WsMAbVQelKjLt0RulYSLktFopJpwIhAOdMB/INI+hbrh4eFot6cpEeVaZnnUW+8to9h36Y60vV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilOOzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofSw//btrzBw8FzYEt3hVuxvfuRq+vr7st4q7Zphzmn0XBWpVe6Yv6\r\nwerxluIVFUxyWTzysJKTQFMmPvh9APvM4mw5Xxlkcsw/yWZqNAl4u4aHz1wa\r\nVxhh+s7Z6GpXpalADM+ve9vOFH8QMZB4ri+C8m8XX6lHuKG7lKkW40pum7kC\r\nZxJDhg4oOinp0M3qDDmvegqibCHq9rjw86lAH/Vg5wL4TyLDTY5/eeci9Pth\r\nCyDeFFwXm2ev7GVf4FAFdXoVorE2OmMzt2DkRV+Zo1njge06WZ489Hj3F6qo\r\ndY+t5OznUeQ5GWqby2/ARJtufKHrf8ZdNwfOybW6qfbQzwOyolQI9StcRybT\r\nkiVLvnA6YtzmysZ8yxvBtIxGhQOZZ+5FFkHYPMFTGggm2NIkh6DD4yvkpe0m\r\nAKcY3ClMS12EJYpWV4tDSRy3CnTeLPxxx2r4ncqlaRghSIYDJEGDc4tZdCyw\r\nWcZzwv+fLAoRe2zLeZkGmQnEbw4o5S+hdpCiG4oEbLlmOIch6dCZS6hKXtKr\r\nBzeiSP783hyrUN79cp/W76KwwInUDGQODuu6NynNjn1ilBJY/Ml63m17b2rT\r\niaK/i4v6xZf/2OdqnJkkEcp7DOOfMtLYJbB/X5YxYz6RIWmZqCtardUSIEUi\r\nAWidDYJbUOYI4isAqhSyYRKi8UxwkVN0KJY=\r\n=a/5N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.0.0"}, "deprecated": "Multer 1.x is impacted by a number of vulnerabilities, which have been patched in 2.x. You should upgrade to the latest 2.x version."}, "1.4.5-lts.2": {"name": "multer", "version": "1.4.5-lts.2", "dependencies": {"xtend": "^4.0.0", "busboy": "^1.0.0", "mkdirp": "^0.5.4", "type-is": "^1.6.4", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^3.5.3", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^14.3.3", "form-data": "^1.0.0-rc1", "deep-equal": "^2.0.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "340af065d8685dda846ec9e3d7655fcd50afba2d", "tarball": "https://registry.npmjs.org/multer/-/multer-1.4.5-lts.2.tgz", "fileCount": 11, "integrity": "sha512-VzGiVigcG9zUAoCNU+xShztrlr1auZOlurXynNvO9GiWD1/mTBbUljOKY+qMeazBqXgRnjzeEgJI/wyjJUHg9A==", "signatures": [{"sig": "MEUCIQDO9RquW26ygGcsz9lS0mld5aHFXCuSuyVE9SvmDFkD9QIgeEzHzPk0u8Y+PtcwWtO/oX9ItiToseny/j2bb/G88cM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27680}, "engines": {"node": ">= 6.0.0"}, "deprecated": "Multer 1.x is impacted by a number of vulnerabilities, which have been patched in 2.x. You should upgrade to the latest 2.x version."}, "2.0.0": {"name": "multer", "version": "2.0.0", "dependencies": {"xtend": "^4.0.0", "busboy": "^1.0.0", "mkdirp": "^0.5.4", "type-is": "^1.6.4", "append-field": "^1.0.0", "concat-stream": "^1.5.2", "object-assign": "^4.1.1"}, "devDependencies": {"mocha": "^11.3.0", "rimraf": "^2.4.1", "express": "^4.13.1", "fs-temp": "^1.1.2", "standard": "^14.3.3", "form-data": "^1.0.0-rc1", "deep-equal": "^2.0.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "47076aa0f7c2c2fd273715e767c6962bf7f94326", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.0.tgz", "fileCount": 11, "integrity": "sha512-bS8r<PERSON><PERSON>urbAuHGAnApbM9d4h1wSoYqrOqkE+6a64KLMK9yWU7gJXBDDVklKQ3TPi9DRb85cRs6yXaC0+cjxRtRg==", "signatures": [{"sig": "MEQCIFuIVEc6HQK+U1fo0kpypp2ETcIB4QPnTgM5g2xopOrHAiAAw5u8BSwANBbnrRxL+wAjTony6aHIQzwuqLyNG5Ka+A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28280}, "engines": {"node": ">= 10.16.0"}}, "3.0.0-alpha.1": {"name": "multer", "version": "3.0.0-alpha.1", "dependencies": {"bytes": "^3.1.0", "fs-temp": "^2.0.1", "type-is": "^1.6.18", "on-finished": "^2.3.0", "append-field": "^2.0.0", "@fastify/busboy": "^1.0.0", "has-own-property": "^2.0.0", "stream-file-type": "^0.6.1"}, "devDependencies": {"c8": "^7.7.3", "hasha": "^5.2.0", "mocha": "^9.0.3", "express": "^4.16.4", "standard": "^16.0.3", "form-data": "^4.0.0", "get-stream": "^6.0.1", "recursive-nullify": "^1.0.0", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"shasum": "388652b571f1e7739d5da76bfcdfb7ad09c12fb0", "tarball": "https://registry.npmjs.org/multer/-/multer-3.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-U9ilVxu1BSOEjaT0pMPE4ouoKkInCgyXkLLSl1dJnAuUrO9Iu58t6wT0x3e12/HnM44CvyjVLGmX1LbN1XqiWQ==", "signatures": [{"sig": "MEQCIAdLPaV3TgkofvdMkcEk+yQDUCBp1pmS/OmhpjWqnSuDAiBhaU2s42t3DYVbgE8EItvVG1WIdc9m4srMkeeIfpZPIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17751}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "2.0.1": {"name": "multer", "version": "2.0.1", "dependencies": {"append-field": "^1.0.0", "busboy": "^1.6.0", "concat-stream": "^2.0.0", "mkdirp": "^0.5.6", "object-assign": "^4.1.1", "type-is": "^1.6.18", "xtend": "^4.0.2"}, "devDependencies": {"deep-equal": "^2.0.3", "express": "^4.21.2", "form-data": "^4.0.2", "fs-temp": "^1.2.1", "mocha": "^11.5.0", "nyc": "^15.1.0", "rimraf": "^2.4.1", "standard": "^14.3.3", "testdata-w3c-json-form": "^1.0.0"}, "dist": {"integrity": "sha512-Ug8bXeTIUlxurg8xLTEskKShvcKDZALo1THEX5E41pYCD2sCVub5/kIRIGqWNoqV6szyLyQKV6mD4QUrWE5GCQ==", "shasum": "3ed335ed2b96240e3df9e23780c91cfcf5d29202", "tarball": "https://registry.npmjs.org/multer/-/multer-2.0.1.tgz", "fileCount": 11, "unpackedSize": 29362, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAZvfxtwRS9bhb2fuBRMfwWRgtCoesUxc2zatRn2JWqKAiA9O/SqNUU8bxgeskArv7QwL0OnvWv4ymIEERVFg7mTjg=="}]}, "engines": {"node": ">= 10.16.0"}}}, "modified": "2025-06-03T16:30:50.059Z"}