{"_id": "jsprim", "_rev": "78-eac181f720fe833edcb62400b8e45c64", "name": "jsprim", "dist-tags": {"latest": "2.0.2"}, "versions": {"0.0.1": {"name": "jsprim", "version": "0.0.1", "_id": "jsprim@0.0.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "94236129559e23c3843e2801696e9299613e6356", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.0.1.tgz", "integrity": "sha512-7uIZf7IwrqFxTaPzmoQufuP82VmWGUdSJBGn/fT8z7lka9KWVr4sqWrIkj/lNlOjU2L2A6BfXIZ8U5tU/rlAvQ==", "signatures": [{"sig": "MEUCICpAKqAAMg2Ttt/Ai77hg56/UKB63HYf5/NGp8JqJrD9AiEArkYSFeDmStuYxXVW6jc6G9CIJyjv6fE+oBBgEAEA6I0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {"JSV": "4.0.0", "verror": "1.0.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.2": {"name": "jsprim", "version": "0.0.2", "_id": "jsprim@0.0.2", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "a8cef84b98ef8d7908365e139b47d70b69a8f11b", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.0.2.tgz", "integrity": "sha512-n4yhW4bn4wnzpTbylJQ97P9WpyaLaUKOqbVqXHdLVGWf/aKRrwRHdmVZ9ph03Vioodrzgzm08JcXnulFV75vjw==", "signatures": [{"sig": "MEQCID+1OxYCzRUIv3zejFQTjU4cKNkJmia9bg+RJ+qVlFtEAiBJJ1KFPr8nlVXhMrdKIgoktws7bsIoOyPsPvK3mYd/ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.0.94", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "v0.4.2", "dependencies": {"JSV": "4.0.0", "verror": "1.0.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": false}, "0.0.3": {"name": "jsprim", "version": "0.0.3", "_id": "jsprim@0.0.3", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "849b0f7c9a60b39dade9fd4afda49e95695bfca8", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.0.3.tgz", "integrity": "sha512-U/Hb6gEP1Pu8675NmbDYi0xOWc0V933SqFFtLuZDDLRVNFE/vzm4a5aqetA6itzAnzKQ47d1oDrTbHX+Lf/fMg==", "signatures": [{"sig": "MEYCIQCXV+vH4LkjcLBdfM/bYbLpbMz0HVZu7aYM71ll2kFwJwIhAIhbC4+7ph8WmrQkqzvQy4Lvsg6WgBd4zHjUoYfD4+W6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "v0.6.17", "dependencies": {"JSV": "4.0.0", "verror": "1.0.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.4": {"name": "jsprim", "version": "0.0.4", "_id": "jsprim@0.0.4", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "2bc05d781e0f2b5dcf69a991a8be554b43aac556", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.0.4.tgz", "integrity": "sha512-LyGYbJV0RTiE+J/HZ6Lg9aO3uyd1JbBB8Io4IYcaKh3VrlkDwGhppKed0k/sGMbw80A0RPcoDGWN9SRn0ADSLQ==", "signatures": [{"sig": "MEQCIE/TL78P4Zo3Szf1EWx5Fh/LcJirl2GR8VdthVu+v2X0AiBIAWlXGIQ6osNCulYdUK7MJ0fj8wEptPFRN3+V1qGBNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "v0.6.17", "dependencies": {"JSV": "4.0.0", "verror": "1.0.1"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.0.5": {"name": "jsprim", "version": "0.0.5", "_id": "jsprim@0.0.5", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "796a40c5571f9acb4bb701e632a05ba72d993d13", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.0.5.tgz", "integrity": "sha512-4cGOWTgeO6mJX5xnw84s70ePgm3QTXyslYoVtVn1POwpVcLqaUgIU1MpNVCFnR7pjB4xBnu9ymxTTV/dMTZNQw==", "signatures": [{"sig": "MEQCIEJu29IrXysu7XO2wR/PclKOckZEmHGtCD2KisTtNS9aAiAceyyXvbZGEUS0uBLbzigTFbshTiDRxpweV5YhRRkb3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "v0.6.17", "dependencies": {"JSV": "4.0.0", "verror": "1.0.1", "extsprintf": "1.0.0"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.0": {"name": "jsprim", "version": "0.1.0", "_id": "jsprim@0.1.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "d733febfcf88678091606574b683b4a8e58eda04", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.1.0.tgz", "integrity": "sha512-DGW5We671KfDGxTuTizanPYPbCDVkwjfJ44dstbot1YNDj3BlQrFXvjN+QeYqe9ot7OO2GjaXHmcJ9D8e5H46g==", "signatures": [{"sig": "MEUCIQCVvybIE4XffdZ7nKYtYIftNh5ingE8VxlFHKvY+BgEKwIgGsHY1tEfRtepT+iXg0EPuknB8dEUs1mhuONWgfnpbF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"JSV": "4.0.0", "verror": "1.0.1", "extsprintf": "1.0.0"}}, "0.2.0": {"name": "jsprim", "version": "0.2.0", "_id": "jsprim@0.2.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "9b84351e80182cc6b1b7b732e04a64943e92febc", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.2.0.tgz", "integrity": "sha512-69pDMiYwKFT3iYti9C/qfEyIyRMLZCnYZtJUMtlOgQ5WN6vdigr7UZtxsU5flaoKtxgnRoIMr3IPRYmQN/oyhw==", "signatures": [{"sig": "MEYCIQDSKK1aJn0SMHbbMfVxzPUmfAbGy4JPERzE9E0OVhoq9AIhAKwqN40Ys6yczRaM23GEBjncmjyaH0coY9vwLgWzkeFR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"JSV": "4.0.0", "verror": "1.0.1", "extsprintf": "1.0.0"}}, "0.3.0": {"name": "jsprim", "version": "0.3.0", "_id": "jsprim@0.3.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "cd13466ea2480dbd8396a570d47d31dda476f8b1", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.3.0.tgz", "integrity": "sha512-lPLTWfTELEgTAY3mphlaIjYa5j6n1XFB3ju+nqFGNXTvX1ZXArW8fwEzq+i+ggILHaBRQkhzeMiQ+M56FbWU6A==", "signatures": [{"sig": "MEUCIQCidzhyBsUCx0m7ZjS/ess0dB71Mv0ZRQwzog9Tie0YQQIgHj5N2HajOtcHneQxog+tCMduUg+NzZvk8MD/xk9t350=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.3", "extsprintf": "1.0.0", "json-schema": "0.2.2"}}, "0.3.1": {"name": "jsprim", "version": "0.3.1", "_id": "jsprim@0.3.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "7d0475ff9e3a4a10f4572c21bdd91004890ffa42", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.3.1.tgz", "integrity": "sha512-Oe+GHv2BsV+Up2USkbTi6TQopQWJ+VxcArNqiipqd5kT7x1iWn2hPe71gRmnZfrRVw70PasO2UDBBoboY5kpyQ==", "signatures": [{"sig": "MEUCIQCMyGLSmzlljMJ1a4QAyImWSopZpUa3jfAdX+ihFsMDfAIgc6n4vYnmMmC0hCD/iWLLiRBLkEjzQXwzSoknYPEi7+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.3", "extsprintf": "1.0.0", "json-schema": "0.2.2"}}, "0.4.0": {"name": "jsprim", "version": "0.4.0", "_id": "jsprim@0.4.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "a6a94839507641edaa5b29ba939435d33c4d1a16", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.4.0.tgz", "integrity": "sha512-pIWQaAgijCu6I2cZ/zkxcyATc24LLI09c6boZj0VQzjhSODr1hTnBIZA+3ogjfyaZJX+LmGz8dsIhy5uh4+TqQ==", "signatures": [{"sig": "MEUCIQDvj05aLLIYeF7FiljcrvMoV1TbdV4DLtYzSsHsmfqPQwIgTGHx0AmUhIxG6KsmweXX193rffd6uQ1BbYDnK2OTeQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.3", "extsprintf": "1.0.0", "json-schema": "0.2.2"}}, "0.5.0": {"name": "jsprim", "version": "0.5.0", "_id": "jsprim@0.5.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "dist": {"shasum": "f6522e9c6b3cc9b542eb1c111defbc8e53f8dab9", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.5.0.tgz", "integrity": "sha512-h24Q4gFYS9ZHvTDF/FrLMWFhkAMPaRgjjRP+4QO+ZV9EB5OCWlNEDPJcad10KjpcfXBqbqXMZHYSJSwIxUgUOA==", "signatures": [{"sig": "MEYCIQD6oUVzpUgRkHanPmdor1Y3NHK5Ku8IdSsX0A9MsppcPAIhALcZFshjwPsvRKBGKBnuBDJ5d55J59cCrl5q6euIUwOA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "0.5.1": {"name": "jsprim", "version": "0.5.1", "license": "MIT", "_id": "jsprim@0.5.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "ee44301326e3ed89403b5e14da059f6421ee1d43", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.5.1.tgz", "integrity": "sha512-NC8/NMMXIQOn4BczQMPZBrp66oIcUNNf5ajhhe4s7rEfugRzyxpwFddiMUHRER119Q6JyRoYO+pvAyTaS+OBwA==", "signatures": [{"sig": "MEQCIEm7/rWEuj8+hOK6pY5FICnAS6+DhbRq+w3vaHbjI0ZkAiAw6BnXS7uYhH1uJqzjOX9Q8+16Pqw1uTiD+UTC7hpMoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "0.6.0": {"name": "jsprim", "version": "0.6.0", "license": "MIT", "_id": "jsprim@0.6.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "9b4d95876f4c5dedf65b646ae0191614c0177939", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.6.0.tgz", "integrity": "sha512-Uy0P7yoc5MyPLYhTkZmmVqDrKLRNzm5QqHXKTSEfD7LSh+rH7tSwcur32SepOUsCPGKL10Ew9dPwfqoaH0s9KA==", "signatures": [{"sig": "MEUCIQDx3kTCE/seskQNsl/qhQRKkfGH7GjpqcGvYgXDdvUcvwIge0/qsKjOVPkcMpJisH3VyMMIVR/HKlXY+ae8FEbmidk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "9b4d95876f4c5dedf65b646ae0191614c0177939", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "0.6.1": {"name": "jsprim", "version": "0.6.1", "license": "MIT", "_id": "jsprim@0.6.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "124c5b5aee1d03845206140b6594a2df63df4477", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.6.1.tgz", "integrity": "sha512-nn2IHXllpBoRgnmvK0aI3M3G+FXkJ8WL5RWKIyKoVzQYONd8G4nMtwauvGLDH9As3pc9BL7nBkqLz+rIQq0fVA==", "signatures": [{"sig": "MEUCIQC0d6Ua8ueTe86xr2MAazkJW2E8jDI0fDuR2gm9PG0jnQIgbg0A70ep+0tzysl7vfn3t/FZK0gSbQ5eTVygEuTK/iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "124c5b5aee1d03845206140b6594a2df63df4477", "engines": ["node >=0.6.0"], "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "0.7.0": {"name": "jsprim", "version": "0.7.0", "license": "MIT", "_id": "jsprim@0.7.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim#readme", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "a869f8e013b875ef74af982b5eb1a4682ee32194", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.7.0.tgz", "integrity": "sha512-XeJ5RcpcU94Va1hwl2UgFvfu0VSTSPF4pLmwWKvYgXTsbKA1Arelmsgrw6fA7RPUATz4SLT+mK2PV9tlmq4S7A==", "signatures": [{"sig": "MEQCIHOB3yiTzqs1aP5K+yzrDTKnPmOvWeO1wR9nn62vq+GDAiA+uiSpgkVMcEYauA8iuGVvLBmSziXhniTsnxrUo/zarg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "a869f8e013b875ef74af982b5eb1a4682ee32194", "engines": ["node >=0.6.0"], "gitHead": "1ffa1800495c9b8648223602dca83e2e00ecf8af", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "0.8.0": {"name": "jsprim", "version": "0.8.0", "license": "MIT", "_id": "jsprim@0.8.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "0f3829737bb127f8f9800fcb2ba9fae300cf41fb", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-0.8.0.tgz", "integrity": "sha512-S/wEGjq+CEqA71ovVvK7KRUCYLor/LW71xj77L8CCjFp7zNieHwYZSG0AF79vVijWnqPF9PDwckO2lTaqBjWnQ==", "signatures": [{"sig": "MEUCIBKlwE+qMKb9lPzJbrpX8jouwrHIuYNsL38nTe6/7iNFAiEA/iF2BqjOiDSBkA4GHgk1qq+qWJ5Lg4tDkTY4uXjX49Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "0f3829737bb127f8f9800fcb2ba9fae300cf41fb", "engines": ["node >=0.6.0"], "gitHead": "6c9a700c6997bf2aff37d367ea3ec99bbdbfb6c8", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "1.0.0": {"name": "jsprim", "version": "1.0.0", "license": "MIT", "_id": "jsprim@1.0.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "ef7be7eeba5e1de3156f2ea7e4529b0c0585d9a6", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.0.0.tgz", "integrity": "sha512-XMW+OGDAMSSy5/sBvsZBX9OMx6rTqYErYfHSnbFHS0xmnua6sntWI5UhLiakwJ+E87Qsnc+p5FIBEjFImOloMA==", "signatures": [{"sig": "MEQCIELm42L+fjzKW6mZhMro+F4texwlTz+OOyx268u3px/xAiAIKRzSbmq28rlzCZSwH5/Ghtkk8mVVGCcU01Y77aYS7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "ef7be7eeba5e1de3156f2ea7e4529b0c0585d9a6", "engines": ["node >=0.6.0"], "gitHead": "fe44229b41354dc8256c38d7518b07ff9f93643a", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "1.1.0": {"name": "jsprim", "version": "1.1.0", "license": "MIT", "_id": "jsprim@1.1.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "e04ab36d82bc1b5d809b553cd4b5e2ed8b42c28c", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.1.0.tgz", "integrity": "sha512-wNWlFhAZErbt7DOA/il2uQDECeymYm+ZOF+qyCwu1m8OM6Cr98HGFcPKKQsDGWm0DR+8HbZGhCvg0CrEKiCWxw==", "signatures": [{"sig": "MEUCIEbtrzPFr8opP4XSFozQdO7NlsInZvID914vQrGjQBXdAiEA10TDnJ3kKDa4t5x7UGOhhXeWnfFozO3GStB2w77y7t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "e04ab36d82bc1b5d809b553cd4b5e2ed8b42c28c", "engines": ["node >=0.6.0"], "gitHead": "417fb65eb791db6fbc8ecdc896b7d478c8a450bb", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "1.2.0": {"name": "jsprim", "version": "1.2.0", "license": "MIT", "_id": "jsprim@1.2.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "74d6fcf239a50ac27307fcfc2682b4d3450c2cf2", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.2.0.tgz", "integrity": "sha512-FdSC8qb89fE1QxN3wT8Ub7iyDKj16LLYbX5a+etPAHkKnC4UMP+15qYTnKinvb//ey8NtEQYnvGkTQMcVa3U1Q==", "signatures": [{"sig": "MEUCIBrHhhzkIRU6vvIpJCzvfqR2D3Mzt6dM73XYMEvcIglQAiEAqneHuh04XGEvq3mO8JQvqDiaOx0rq9J1FyKnPG+FQ9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "74d6fcf239a50ac27307fcfc2682b4d3450c2cf2", "engines": ["node >=0.6.0"], "gitHead": "4e53702e79c58adc6e73deb016b299de12d2dfd1", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "utilities for primitive JavaScript types", "directories": {}, "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "1.2.1": {"name": "jsprim", "version": "1.2.1", "license": "MIT", "_id": "jsprim@1.2.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "555716c8d3b2dbe072f86e657d9ffa83d1ef9978", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.2.1.tgz", "integrity": "sha512-4vU5XKYT+iUh83pkfAhufP9+k87SRK+U+TZwyCdMtYFbZ3C2cqlwnrwMcxYuQqzAIyfCjaBD1rUbpqRY8K+SbA==", "signatures": [{"sig": "MEUCIHqua7X1VOCmYUsCwS0YoVikYiyr3zapzZWWCMq9YNYwAiEAzSBkZAuVkhlPZ9By1yrwtDS9ZuFAWZ5K6aYHemrqhZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "555716c8d3b2dbe072f86e657d9ffa83d1ef9978", "engines": ["node >=0.6.0"], "gitHead": "16a06921d504b268e7deba7a8dde8c79e48e4ce6", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "1.2.2": {"name": "jsprim", "version": "1.2.2", "license": "MIT", "_id": "jsprim@1.2.2", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "f20c906ac92abd58e3b79ac8bc70a48832512da1", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.2.2.tgz", "integrity": "sha512-hTAms4cBEbKR9iSUi+v7AyTvfyPUWssZqEUBsoDE96bM/sdackVTlDFQWznBxvJwsgdJZqwUBH/IO12HHt0y6w==", "signatures": [{"sig": "MEQCID5DleSDthCNuOyGapLFa0Tn5c/xz9MGEyCjYs0Y49mnAiA6JXZmW/MHIY43oAWQqShPGd0K99KBxtD+f2/V2RuiXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "f20c906ac92abd58e3b79ac8bc70a48832512da1", "engines": ["node >=0.6.0"], "gitHead": "0e709876c35eddfe4c1f95104ac3c4abfc9dab84", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}}, "1.3.0": {"name": "jsprim", "version": "1.3.0", "license": "MIT", "_id": "jsprim@1.3.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim#readme", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "ce2e1bef835204b4f3099928c602f8b6ae615650", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.3.0.tgz", "integrity": "sha512-VEQ5HZwLwk6NS2QC0ZewTgrDxd7RuglkGnc7mhlJJ8aK90K1FBxtGsujXkNVkIspeWcAKZhozIQAWwu8CxW3fg==", "signatures": [{"sig": "MEYCIQDYsMWOYuUGplXaIPxec/YwiGcF2Kwaq3MJju5wfXG/KQIhAPUAoVCOBkX8zOrNq/wqntg6hxQc9BGoX1TYRdIMWO2K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "ce2e1bef835204b4f3099928c602f8b6ae615650", "engines": ["node >=0.6.0"], "gitHead": "694edcb22e2291c21f6c2a23907bf02e1edbfdf4", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/jsprim-1.3.0.tgz_1466708163640_0.5282344303559512", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "jsprim", "version": "1.3.1", "license": "MIT", "_id": "jsprim@1.3.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/davepacheco/node-jsprim#readme", "bugs": {"url": "https://github.com/davepacheco/node-jsprim/issues"}, "dist": {"shasum": "2a7256f70412a29ee3670aaca625994c4dcff252", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.3.1.tgz", "integrity": "sha512-bxYB8xe0/KIpYlNbZcA9e+lNquVmdPUGmZFNIQiAHPxkUDK2THIq3R+7VVgnv3UkZ63BrJ10+IeNZuew4Pso9Q==", "signatures": [{"sig": "MEQCICiHRTOris2yg+5uSl+Y4Gw5L3YjL0nmH7nbxknZTcT/AiBL/ikopTL1Plg3bD04DLpsl3atlVbBDn8dGR2kUObWwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "2a7256f70412a29ee3670aaca625994c4dcff252", "engines": ["node >=0.6.0"], "gitHead": "825aba45c6cff4340c18cdae363ccb5bdf840bd7", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/davepacheco/node-jsprim.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "json-schema": "0.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/jsprim-1.3.1.tgz_1473725209917_0.5387293708045036", "host": "packages-16-east.internal.npmjs.com"}}, "1.4.0": {"name": "jsprim", "version": "1.4.0", "license": "MIT", "_id": "jsprim@1.4.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-jsprim#readme", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "dist": {"shasum": "a3b87e40298d8c380552d8cc7628a0bb95a22918", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.0.tgz", "integrity": "sha512-OyKQuabgqUi2RUPauBrfZNoCb0KNoulf1DqQ07rUW2vzauzXAq/uUe7oDstV/2RavaxGn7NfcI/F2hrBk38Fbg==", "signatures": [{"sig": "MEUCIQDVTyOiFqjrHNNXrCL7txB5bL0xNpme9CtHacOWVOgIggIgErw8rcewmO/Hoi3+x02salbKFFx5679BmoJXG7WWwF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "a3b87e40298d8c380552d8cc7628a0bb95a22918", "engines": ["node >=0.6.0"], "gitHead": "6ea6cb4f3d2d889d87c336487f4940eb4aa11980", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/joyent/node-jsprim.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.10.43", "dependencies": {"verror": "1.3.6", "extsprintf": "1.0.2", "assert-plus": "1.0.0", "json-schema": "0.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/jsprim-1.4.0.tgz_1489453554649_0.9609751487150788", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.1": {"name": "jsprim", "version": "1.4.1", "license": "MIT", "_id": "jsprim@1.4.1", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-jsprim#readme", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "dist": {"shasum": "313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha512-4Dj8Rf+fQ+/Pn7C5qeEX02op1WfOss3PKTE9Nsop3Dx+6UPxlm1dr/og7o2cRa5hNN07CACr4NFzRLtj/rjWog==", "signatures": [{"sig": "MEYCIQCHLAg/EUfT+AnbWzSixevHgihbUOF7MaEZcvcjC51OPAIhAOBfBZlo1rQxblfoY6sJdfdU8jvcDPH1A2K0QivES3L/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2", "engines": ["node >=0.6.0"], "gitHead": "f7d80a9e8e3f79c0b76448ad9ceab252fb309b32", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/joyent/node-jsprim.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.10.45", "dependencies": {"verror": "1.10.0", "extsprintf": "1.3.0", "assert-plus": "1.0.0", "json-schema": "0.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/jsprim-1.4.1.tgz_1501691396911_0.08959000837057829", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "jsprim", "version": "2.0.0", "license": "MIT", "_id": "jsprim@2.0.0", "maintainers": [{"name": "dap", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-jsprim#readme", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "dist": {"shasum": "5a5d9a35770978600a1caa095c78c7831ecf4f7c", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-2.0.0.tgz", "integrity": "sha512-rxXGXYGevQlcHOzUpgfpGpD1W/3bBMwQeSA6s/h+KWtDWoCsW9TTqdtnMggeaQ88kLhX/eO4JWCf2ns9GODlBw==", "signatures": [{"sig": "MEUCIBQs87/hMhnYoySjgm3cLFZT/Pw+fSc8DHBFv6Ehbmj1AiEAhLqcd1SYkacHiJF/UceGQ1/4iFtrHrPIOhlGek3z5ZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/jsprim.js", "_from": ".", "_shasum": "5a5d9a35770978600a1caa095c78c7831ecf4f7c", "engines": ["node >=0.6.0"], "gitHead": "e65bdabbc1abdf83ba1e8d121e5a877ec29e6fe1", "scripts": {}, "_npmUser": {"name": "dap", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/joyent/node-jsprim.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "0.10.46", "dependencies": {"verror": "1.10.0", "extsprintf": "1.3.0", "assert-plus": "1.0.0", "json-schema": "0.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/jsprim-2.0.0.tgz_1508978212851_0.908593182452023", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "jsprim", "version": "2.0.1", "license": "MIT", "_id": "jsprim@2.0.1", "maintainers": [{"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chudley", "email": "<EMAIL>"}, {"name": "tcha<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d<PERSON>ell", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-jsprim#readme", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "dist": {"shasum": "b7e4a153064871b496bec6d12a7c26874391db79", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-ykT6/hSq5BOLi+8v/a8Sq/FiS9s+N/Y5hjFY0Gv134uAk+GXEoMTVoq9Wgju8oG7laWpT198myOYeZVn8LUhtA==", "signatures": [{"sig": "MEUCIQDLZ9gR7UoEO7Wyc3TuXngc0xJlB1m7re7/vj/pLHl75QIgWGsruM6bsYvE0POJI3a7vnG7GFpKOi0uVEbGERBRJAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31146}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "gitHead": "f544b6d5f87e76881fe06dfe5a93338019c6e9aa", "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-jsprim.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "12.22.7", "dependencies": {"verror": "1.10.0", "extsprintf": "1.3.0", "assert-plus": "1.0.0", "json-schema": "0.2.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jsprim_2.0.1_1635970809813_0.9428596511905991", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "jsprim", "version": "2.0.2", "license": "MIT", "_id": "jsprim@2.0.2", "maintainers": [{"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chudley", "email": "<EMAIL>"}, {"name": "tcha<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d<PERSON>ell", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-jsprim#readme", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "dist": {"shasum": "77ca23dbcd4135cd364800d22ff82c2185803d4d", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-2.0.2.tgz", "fileCount": 6, "integrity": "sha512-gqXddjPqQ6G40VdnI6T6yObEC+pDNvyP95wdQhkWkg7crHH3km5qP1FsOXEkzEQwnz6gz5qGTn1c2Y52wP3OyQ==", "signatures": [{"sig": "MEQCIHKIW+QCqC2kFunbnVDhdXQuZkEtMGtj8uH+M7fcpMfqAiAvOxU2wkz949nZuwfLtEHzBK9CjK5P7vOqa3xyZnXbiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlFEoCRA9TVsSAnZWagAASpsP/AuQDSdk5RYleVZpa+7U\njyulI30GHAb+DYpKnIwQG2NN72vFTePvqwA1kjsKL2H5f5Ce/8Y3mQSyLhNw\ntbkjK/qJKU23hNsyuC+i/6x91RuUOIoMjSDeD1DFeGT7M8XObq2V5s9Ifpna\njy/21XuTrWzWQEy95tO5+a7PA8ofhrebx7Inuy22FvW1ZVxW/3WhOauJqm44\nJ7Lgz8MMyaqxDn4XEJgm5vSs9h+7FZMYiVPWdSTrim7MSqEG+cSYG+p5ao1d\nd3ZTC9Hfx8C6fXLBon5RGT642xubSZj2ssejxo9pdn7oB+I6Lt2lFx/Wcy0h\n3IwfQu4BRTdiZI2RxNKOpxW5KpTKIlq1pbd/3+9ht6oWiE7Gn7GojYH59yDs\n4HKEK0ItQiipEaRa7QgdNV+0K3lDnxLwTJte2h8/jXeHVFMLT322EDkN7eDR\n3O7eGRt2n/DVS8k4rhiVwIY/kXL/44SzZ1hERg2Ut9CmTVZKVCmIW/v6n26Y\nBj7MmYuLzf5XejpaFzNmyXE9AU9WgzIoCk+FlG/gVECjtmCWccJCLfoZRd0U\n87iG0SCOylFMn3TsT3RHsrSFcgGzQYHJDcKoCsDO4yvzAUVZ7YYfkjW5KfgT\nOTOEIvXg9n32qARTMgY/e+Gcu94PFgM8lwMqDYf/iSACPJl6QA5UGg7jQCFc\n43RQ\r\n=bI6r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/jsprim.js", "engines": ["node >=0.6.0"], "gitHead": "017f74473e55872cf71113bec35ce46de74fbe17", "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/joyent/node-jsprim.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "12.22.7", "dependencies": {"verror": "1.10.0", "extsprintf": "1.3.0", "assert-plus": "1.0.0", "json-schema": "0.4.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jsprim_2.0.2_1637110056049_0.5400856381221282", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "jsprim", "version": "1.4.2", "license": "MIT", "_id": "jsprim@1.4.2", "maintainers": [{"name": "todd.whiteman", "email": "<EMAIL>"}, {"name": "kusor", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "micha<PERSON>.hicks", "email": "<EMAIL>"}, {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "chudley", "email": "<EMAIL>"}, {"name": "tcha<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d<PERSON>ell", "email": "<EMAIL>"}, {"name": "trentm", "email": "<EMAIL>"}, {"name": "dap", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "melloc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/joyent/node-jsprim#readme", "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "dist": {"shasum": "712c65533a15c878ba59e9ed5f0e26d5b77c5feb", "tarball": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz", "fileCount": 6, "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "signatures": [{"sig": "MEUCIBOwRynuF+OBYBgILmD9nqup9WlpRBiZP13Y/QTWI9Z/AiEA4HJy/jNQ0SrVJYwEsc1tI6IpdJYcCEpFKQbPWqvBr48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpPCpCRA9TVsSAnZWagAAqhEP/0t1AH15Z4Luq23NfTaf\nwFwDRcRg1krxwtpNektg5+4C3otbg0BWtutlVYYmpGOVWKPumjA6KnfnTrcx\npsxG2hFeoDBv7DU8XrcFRG1JDzjETNK1oanLnP1E3uU8ugptnsX+U3GwPmgF\nuz9lqlqFzRn41g8lgJRHSrlIkkcRL2Ht7myiKgBiocGFUBYeGQgE8ZjSsbg7\n6ek0XOwSuH8PdCh6fnUO+4q/xIpiS1OqCCmZWw6fQILNEQrYii4nBzWwX4Qz\ny0MWk9krIVWHbnV2RuRbb3+o7wDBjpMdI96rYmLE2IF3V4Zxr2LSC6GctG2J\nX8qxcPrWbTZnnC/XqzAgYQFgunzpCHAkiZ29mHqfRhXh1JWYmsbXal/XvCBE\nlIwdh+Yr3+fUDwAjuSupzJMUvSszrkXK75z3aFVCEWz/EZYO74S4S4x4Hdu3\nfTszQhl285zH9ILeSsnhNamJqIZgBnoaLyrrYX+MVNLJKP7xluZ8q7tt6Az/\nb8Xy3oZ2CUfv8lBfmfApr413sLG057IetfR1kQKcBIpS1xjePdoU7LCZsSp9\n2aTRvGemZ6BN7cMHRn6Qf0KEdJ2YN2V4eeyC8lTHJp8MvhgtVdRRjPFbYDtf\n4bwo3TBiK7vbr1tCmWK7CBYcttCXuqwMqNqF4PvWJb2jHtQE4fopidWTNSh+\nXN9d\r\n=xeHG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/jsprim.js", "engines": {"node": ">=0.6.0"}, "gitHead": "5c8475fd44567e459b1b73b82f2669c39a0642b8", "_npmUser": {"name": "bah<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/joyent/node-jsprim.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "utilities for primitive JavaScript types", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {"verror": "1.10.0", "extsprintf": "1.3.0", "assert-plus": "1.0.0", "json-schema": "0.4.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jsprim_1.4.2_1638199465092_0.8446735929720175", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-04-18T17:12:25.768Z", "modified": "2025-02-07T15:28:35.950Z", "0.0.1": "2012-04-18T17:12:26.971Z", "0.0.2": "2012-04-19T19:03:15.543Z", "0.0.3": "2012-06-05T22:04:58.580Z", "0.0.4": "2012-06-06T22:17:55.604Z", "0.0.5": "2012-06-08T22:24:36.586Z", "0.1.0": "2012-08-20T18:33:42.230Z", "0.2.0": "2012-08-23T17:55:39.825Z", "0.3.0": "2012-09-06T18:42:14.711Z", "0.3.1": "2012-12-04T01:40:51.900Z", "0.4.0": "2013-01-18T01:06:00.766Z", "0.5.0": "2013-04-03T00:07:50.875Z", "0.5.1": "2013-11-05T00:04:38.490Z", "0.6.0": "2014-08-04T04:49:56.552Z", "0.6.1": "2014-08-07T16:27:58.826Z", "0.7.0": "2015-08-13T20:28:49.036Z", "0.8.0": "2015-08-19T23:09:58.286Z", "1.0.0": "2015-09-02T01:07:04.114Z", "1.1.0": "2015-09-02T19:24:53.077Z", "1.2.0": "2015-10-13T23:07:09.292Z", "1.2.1": "2015-10-14T21:18:02.739Z", "1.2.2": "2015-10-16T00:03:27.758Z", "1.3.0": "2016-06-23T18:56:04.140Z", "1.3.1": "2016-09-13T00:06:53.113Z", "1.4.0": "2017-03-14T01:05:54.870Z", "1.4.1": "2017-08-02T16:29:57.065Z", "2.0.0": "2017-10-26T00:36:52.930Z", "2.0.1": "2021-11-03T20:20:10.275Z", "2.0.2": "2021-11-17T00:47:36.240Z", "1.4.2": "2021-11-29T15:24:25.448Z"}, "bugs": {"url": "https://github.com/joyent/node-jsprim/issues"}, "license": "MIT", "homepage": "https://github.com/joyent/node-jsprim#readme", "repository": {"url": "git+https://github.com/joyent/node-jsprim.git", "type": "git"}, "description": "utilities for primitive JavaScript types", "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "trentm"}, {"email": "<EMAIL>", "name": "dap"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "melloc"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "readme": "# jsprim: utilities for primitive JavaScript types\n\nThis module provides miscellaneous facilities for working with strings,\nnumbers, dates, and objects and arrays of these basic types.\n\n\n### deepCopy(obj)\n\nCreates a deep copy of a primitive type, object, or array of primitive types.\n\n\n### deepEqual(obj1, obj2)\n\nReturns whether two objects are equal.\n\n\n### isEmpty(obj)\n\nReturns true if the given object has no properties and false otherwise.  This\nis O(1) (unlike `Object.keys(obj).length === 0`, which is O(N)).\n\n### hasKey(obj, key)\n\nReturns true if the given object has an enumerable, non-inherited property\ncalled `key`.  [For information on enumerability and ownership of properties, see\nthe MDN\ndocumentation.](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Enumerability_and_ownership_of_properties)\n\n### forEachKey(obj, callback)\n\nLike Array.forEach, but iterates enumerable, owned properties of an object\nrather than elements of an array.  Equivalent to:\n\n    for (var key in obj) {\n            if (Object.prototype.hasOwnProperty.call(obj, key)) {\n                    callback(key, obj[key]);\n            }\n    }\n\n\n### flattenObject(obj, depth)\n\nFlattens an object up to a given level of nesting, returning an array of arrays\nof length \"depth + 1\", where the first \"depth\" elements correspond to flattened\ncolumns and the last element contains the remaining object .  For example:\n\n    flattenObject({\n        'I': {\n            'A': {\n                'i': {\n                    'datum1': [ 1, 2 ],\n                    'datum2': [ 3, 4 ]\n                },\n                'ii': {\n                    'datum1': [ 3, 4 ]\n                }\n            },\n            'B': {\n                'i': {\n                    'datum1': [ 5, 6 ]\n                },\n                'ii': {\n                    'datum1': [ 7, 8 ],\n                    'datum2': [ 3, 4 ],\n                },\n                'iii': {\n                }\n            }\n        },\n        'II': {\n            'A': {\n                'i': {\n                    'datum1': [ 1, 2 ],\n                    'datum2': [ 3, 4 ]\n                }\n            }\n        }\n    }, 3)\n\nbecomes:\n\n    [\n        [ 'I',  'A', 'i',   { 'datum1': [ 1, 2 ], 'datum2': [ 3, 4 ] } ],\n        [ 'I',  'A', 'ii',  { 'datum1': [ 3, 4 ] } ],\n        [ 'I',  'B', 'i',   { 'datum1': [ 5, 6 ] } ],\n        [ 'I',  'B', 'ii',  { 'datum1': [ 7, 8 ], 'datum2': [ 3, 4 ] } ],\n        [ 'I',  'B', 'iii', {} ],\n        [ 'II', 'A', 'i',   { 'datum1': [ 1, 2 ], 'datum2': [ 3, 4 ] } ]\n    ]\n\nThis function is strict: \"depth\" must be a non-negative integer and \"obj\" must\nbe a non-null object with at least \"depth\" levels of nesting under all keys.\n\n\n### flattenIter(obj, depth, func)\n\nThis is similar to `flattenObject` except that instead of returning an array,\nthis function invokes `func(entry)` for each `entry` in the array that\n`flattenObject` would return.  `flattenIter(obj, depth, func)` is logically\nequivalent to `flattenObject(obj, depth).forEach(func)`.  Importantly, this\nversion never constructs the full array.  Its memory usage is O(depth) rather\nthan O(n) (where `n` is the number of flattened elements).\n\nThere's another difference between `flattenObject` and `flattenIter` that's\nrelated to the special case where `depth === 0`.  In this case, `flattenObject`\nomits the array wrapping `obj` (which is regrettable).\n\n\n### pluck(obj, key)\n\nFetch nested property \"key\" from object \"obj\", traversing objects as needed.\nFor example, `pluck(obj, \"foo.bar.baz\")` is roughly equivalent to\n`obj.foo.bar.baz`, except that:\n\n1. If traversal fails, the resulting value is undefined, and no error is\n   thrown.  For example, `pluck({}, \"foo.bar\")` is just undefined.\n2. If \"obj\" has property \"key\" directly (without traversing), the\n   corresponding property is returned.  For example,\n   `pluck({ 'foo.bar': 1 }, 'foo.bar')` is 1, not undefined.  This is also\n   true recursively, so `pluck({ 'a': { 'foo.bar': 1 } }, 'a.foo.bar')` is\n   also 1, not undefined.\n\n\n### randElt(array)\n\nReturns an element from \"array\" selected uniformly at random.  If \"array\" is\nempty, throws an Error.\n\n\n### startsWith(str, prefix)\n\nReturns true if the given string starts with the given prefix and false\notherwise.\n\n\n### endsWith(str, suffix)\n\nReturns true if the given string ends with the given suffix and false\notherwise.\n\n\n### parseInteger(str, options)\n\nParses the contents of `str` (a string) as an integer. On success, the integer\nvalue is returned (as a number). On failure, an error is **returned** describing\nwhy parsing failed.\n\nBy default, leading and trailing whitespace characters are not allowed, nor are\ntrailing characters that are not part of the numeric representation. This\nbehaviour can be toggled by using the options below. The empty string (`''`) is\nnot considered valid input. If the return value cannot be precisely represented\nas a number (i.e., is smaller than `Number.MIN_SAFE_INTEGER` or larger than\n`Number.MAX_SAFE_INTEGER`), an error is returned. Additionally, the string\n`'-0'` will be parsed as the integer `0`, instead of as the IEEE floating point\nvalue `-0`.\n\nThis function accepts both upper and lowercase characters for digits, similar to\n`parseInt()`, `Number()`, and [strtol(3C)](https://illumos.org/man/3C/strtol).\n\nThe following may be specified in `options`:\n\nOption             | Type    | Default | Meaning\n------------------ | ------- | ------- | ---------------------------\nbase               | number  | 10      | numeric base (radix) to use, in the range 2 to 36\nallowSign          | boolean | true    | whether to interpret any leading `+` (positive) and `-` (negative) characters\nallowImprecise     | boolean | false   | whether to accept values that may have lost precision (past `MAX_SAFE_INTEGER` or below `MIN_SAFE_INTEGER`)\nallowPrefix        | boolean | false   | whether to interpret the prefixes `0b` (base 2), `0o` (base 8), `0t` (base 10), or `0x` (base 16)\nallowTrailing      | boolean | false   | whether to ignore trailing characters\ntrimWhitespace     | boolean | false   | whether to trim any leading or trailing whitespace/line terminators\nleadingZeroIsOctal | boolean | false   | whether a leading zero indicates octal\n\nNote that if `base` is unspecified, and `allowPrefix` or `leadingZeroIsOctal`\nare, then the leading characters can change the default base from 10. If `base`\nis explicitly specified and `allowPrefix` is true, then the prefix will only be\naccepted if it matches the specified base. `base` and `leadingZeroIsOctal`\ncannot be used together.\n\n**Context:** It's tricky to parse integers with JavaScript's built-in facilities\nfor several reasons:\n\n- `parseInt()` and `Number()` by default allow the base to be specified in the\n  input string by a prefix (e.g., `0x` for hex).\n- `parseInt()` allows trailing nonnumeric characters.\n- `Number(str)` returns 0 when `str` is the empty string (`''`).\n- Both functions return incorrect values when the input string represents a\n  valid integer outside the range of integers that can be represented precisely.\n  Specifically, `parseInt('9007199254740993')` returns 9007199254740992.\n- Both functions always accept `-` and `+` signs before the digit.\n- Some older JavaScript engines always interpret a leading 0 as indicating\n  octal, which can be surprising when parsing input from users who expect a\n  leading zero to be insignificant.\n\nWhile each of these may be desirable in some contexts, there are also times when\nnone of them are wanted. `parseInteger()` grants greater control over what\ninput's permissible.\n\n### iso8601(date)\n\nConverts a Date object to an ISO8601 date string of the form\n\"YYYY-MM-DDTHH:MM:SS.sssZ\".  This format is not customizable.\n\n\n### parseDateTime(str)\n\nParses a date expressed as a string, as either a number of milliseconds since\nthe epoch or any string format that Date accepts, giving preference to the\nformer where these two sets overlap (e.g., strings containing small numbers).\n\n\n### hrtimeDiff(timeA, timeB)\n\nGiven two hrtime readings (as from Node's `process.hrtime()`), where timeA is\nlater than timeB, compute the difference and return that as an hrtime.  It is\nillegal to invoke this for a pair of times where timeB is newer than timeA.\n\n### hrtimeAdd(timeA, timeB)\n\nAdd two hrtime intervals (as from Node's `process.hrtime()`), returning a new\nhrtime interval array.  This function does not modify either input argument.\n\n\n### hrtimeAccum(timeA, timeB)\n\nAdd two hrtime intervals (as from Node's `process.hrtime()`), storing the\nresult in `timeA`.  This function overwrites (and returns) the first argument\npassed in.\n\n\n### hrtimeNanosec(timeA), hrtimeMicrosec(timeA), hrtimeMillisec(timeA)\n\nThis suite of functions converts a hrtime interval (as from Node's\n`process.hrtime()`) into a scalar number of nanoseconds, microseconds or\nmilliseconds.  Results are truncated, as with `Math.floor()`.\n\n\n### validateJsonObject(schema, object)\n\nUses JSON validation (via JSV) to validate the given object against the given\nschema.  On success, returns null.  On failure, *returns* (does not throw) a\nuseful Error object.\n\n\n### extraProperties(object, allowed)\n\nCheck an object for unexpected properties.  Accepts the object to check, and an\narray of allowed property name strings.  If extra properties are detected, an\narray of extra property names is returned.  If no properties other than those\nin the allowed list are present on the object, the returned array will be of\nzero length.\n\n### mergeObjects(provided, overrides, defaults)\n\nMerge properties from objects \"provided\", \"overrides\", and \"defaults\".  The\nintended use case is for functions that accept named arguments in an \"args\"\nobject, but want to provide some default values and override other values.  In\nthat case, \"provided\" is what the caller specified, \"overrides\" are what the\nfunction wants to override, and \"defaults\" contains default values.\n\nThe function starts with the values in \"defaults\", overrides them with the\nvalues in \"provided\", and then overrides those with the values in \"overrides\".\nFor convenience, any of these objects may be falsey, in which case they will be\nignored.  The input objects are never modified, but properties in the returned\nobject are not deep-copied.\n\nFor example:\n\n    mergeObjects(undefined, { 'objectMode': true }, { 'highWaterMark': 0 })\n\nreturns:\n\n    { 'objectMode': true, 'highWaterMark': 0 }\n\nFor another example:\n\n    mergeObjects(\n        { 'highWaterMark': 16, 'objectMode': 7 }, /* from caller */\n        { 'objectMode': true },                   /* overrides */\n        { 'highWaterMark': 0 });                  /* default */\n\nreturns:\n\n    { 'objectMode': true, 'highWaterMark': 16 }\n\n\n# Contributing\n\nSee separate [contribution guidelines](CONTRIBUTING.md).\n", "readmeFilename": "README.md", "users": {"slurm": true, "moinism": true, "mojaray2k": true, "shuoshubao": true}}