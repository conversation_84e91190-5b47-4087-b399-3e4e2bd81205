{"_id": "resolve", "_rev": "201-cb431f0a23770d6c249ceb286fb935aa", "name": "resolve", "dist-tags": {"next": "2.0.0-next.5", "latest": "1.22.10"}, "versions": {"0.0.0": {"name": "resolve", "version": "0.0.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.0.0", "dist": {"shasum": "9a74e26be2ea4fb18960236f1448b0e38bcc93e5", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.0.0.tgz", "integrity": "sha512-cbxF5Lxdn/q3pxxiGz4wo4Ch7RE+Ns1jDJLF37AKvxVwHk2/STAYh4mwHF/9SKZucqcP7L+NJafqd1zje19xCQ==", "signatures": [{"sig": "MEQCICi9F3Ze9v2fPDtYREsN03Vd9nJOD/Xkf58nh26xxB7sAiBPlAuLXQYDiBCMiA64TS298F0JQcVgMuZ7/z2UjZRLOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.0.1": {"name": "resolve", "version": "0.0.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.0.1", "dist": {"shasum": "d7188e3ae59196f3556cd4cfcedf7a9b12fb55e9", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.0.1.tgz", "integrity": "sha512-PPQlvsp2VXCNfmzs7QD8471rsowKLFm/8X0Y5rUcDG83flqJZbIo3Vz5BsGMGekl7H9kgmi+jxNmK6ZQm5beGQ==", "signatures": [{"sig": "MEYCIQDQQP35LCk4UzfnZ2R10iyuxpOib31x6jkhFDh8i5KrDAIhAIrT9HMaVrHd4PYhICRy0SFJ5Jt1H45v4rai0ISRbqjE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.0.2": {"name": "resolve", "version": "0.0.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.0.2", "dist": {"shasum": "9c6835475096251f8b2e292ddd45df2974c64162", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.0.2.tgz", "integrity": "sha512-nikl0GNN1xPFOpNH1NEzfzAtSmVeRnZxHoIZRDWSvECGnbKvz8PSi6NvJ7qlq4oGjJcCdYIEey9BVqOj3nGwTA==", "signatures": [{"sig": "MEQCIEqDb/f8xsa6U1NaJSUXt0e7muLI97tgs63rSxEfPKAFAiA6m3DbUp0qvRoKKs0qe2fd2gB428AxXrskWvWOfVLNqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.0.3": {"name": "resolve", "version": "0.0.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.0.3", "dist": {"shasum": "8212502b729a63fe8dea0af1920a58538b161742", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.0.3.tgz", "integrity": "sha512-eHPer0RdSO7M4sAWu23eFXUCgiUKarGCkkGwQVAYy+I1PEimmjFEz27ySAx3qBL/TS9rt2eDzo1BQqq3DOc2/Q==", "signatures": [{"sig": "MEUCIHL0tf/nk9jcpHprYvRIzm4QVUJNOGZ2yKaOgqLOGuxSAiEA1MQJ1ZK8r+qUicVi8HgJLPyPy6GQV6cUvrJVVTRSjIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.0.4": {"name": "resolve", "version": "0.0.4", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.0.4", "dist": {"shasum": "b3f7d9c3b46a0f512984940a4b23f30176dda95d", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.0.4.tgz", "integrity": "sha512-nwD/0lTptroPNrKlYam9yt22KmTxqKMsiV7nE9NbDcyTpcpGODxvHtoOuV0kXgDSrFrdT/VuyKnRf9Rmp06THA==", "signatures": [{"sig": "MEUCIDLSQPloPLo5wo3aM6Y0Qaom+OSPybRGOvGqpdz8Pyr8AiEAthjjBr/tkcqJ4gPuvuMbOIEABGFSND+J9bl+CiBPCfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.1.0": {"name": "resolve", "version": "0.1.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.1.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "de35cfc7e7048e566f99056ad0b06d7cce8d49cb", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.1.0.tgz", "integrity": "sha512-VjSqVe+ix2ldf3jS5n1WcWxXw1VoofyvGVhgXjcVZBYHBWORUJPXDodGQ3b6Cl0AiIh/2zBWprdSZnhGqWsncQ==", "signatures": [{"sig": "MEQCICjwTDItUEY8JGamuTciypU6uceheV17U5Ha/U7uCfHPAiB+vkqrlWzrkuYzfdVhofZ2QhuHKT4jY9hnWLAE92jyhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.30", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.4.12", "_npmJsonOpts": {"file": "/home/<USER>/.npm/resolve/0.1.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.1.2": {"name": "resolve", "version": "0.1.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.1.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "37eaa50a0b586adac455b9fa6dc45217e6b002e7", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.1.2.tgz", "integrity": "sha512-+VEVKiP5yPboILu0eCVjk8xa9xl59R2rwmZHee5MdZiGpQKp+CgRtf6AKEtMcjzHHFhAkAkkAHbSVlmLPY1YQw==", "signatures": [{"sig": "MEUCICdgAeu4FbVLbWTsBMwPZ7lh5ShBlad2iYODuFsgxHI9AiEAs6yAzvLD4yL9NZ3D8HZoYrOa5SjbBU30yWnzSImcAdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.101", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.1.3": {"name": "resolve", "version": "0.1.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.1.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "204c18364bc529a0b376e4a714b7ba44f0d390d1", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.1.3.tgz", "integrity": "sha512-6yFJ3dyxXFfIt2oA+1an/T6UTUn3iaia5416l2dF+6NOFpD/ndCJnRZSP44Oyr9RiC+k19nHdXuApWxTF7QJjA==", "signatures": [{"sig": "MEUCIQDrmywrydrxPP5SLy5lcaCHkIIyq/vlXEaCnNjv8LxC4QIgE0y1Gumc0S9KwU0tMJeQCtNw4qvOhTKic8THeMv/PRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "=0.7.x"}, "_engineSupported": true}, "0.2.0": {"name": "resolve", "version": "0.2.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "136daea49be374950974cd30858c66a6d1c9bbe4", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.0.tgz", "integrity": "sha512-9jSCXLsxC+PIXGzKJtp1nIVvL34hvVRxNM6qGOBF5k16heGMtoP3zPuZcheOUS7no8l2dlBt9Oitoa/jvCVCtA==", "signatures": [{"sig": "MEUCIFW8BsIZ7bjnYLYzYr/CNRsE98pDBOkePZimvYYDGp2YAiEAtgreFO+OgyELeitjz/WxSeeZi+H2imcjfmc74IcCYik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "expresso"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"expresso": "0.7.x"}, "_engineSupported": true}, "0.2.1": {"name": "resolve", "version": "0.2.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "39689c97d867b40d865adec160358bee91da7f5a", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.1.tgz", "integrity": "sha512-ZJTLPuR0iciAzPGeZZ2Kl81BcC0MqGvscaqXfWse5hIDNkpj9B8UMu31Bu2S3PqXWW1Kteicll+kBAacevkIbg==", "signatures": [{"sig": "MEUCIQCjT4aTjLoSqqcOL9TCCRlwy7MAnmvL1SAoOzlh9XxWMAIgMdX1NA7rhQDp5TfYlfMBfiW6JrP+HuxjOV/GeL30QyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.6.11", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.2.4"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.2": {"name": "resolve", "version": "0.2.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "69116e81b47e0e4aa9a9231b271605a73dbf8b8d", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.2.tgz", "integrity": "sha512-7dZrWAgAozHLTqZTTy/TA0/yAXP9re9SNT2jHkyt0ClqBK3VEo0b1BNglTokNAhBSvMj7I6evhEtvK9ds1hKfg==", "signatures": [{"sig": "MEUCIAI4pObr4zN7x8SLrMEASP/OtpdKZfbsCFe1KyxUT11wAiEAm47JkdbmyueDTYVrlfzUxvISmnW4vz0Cjmxlhv+LSR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "_nodeVersion": "v0.6.11", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "~0.2.4"}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.3": {"name": "resolve", "version": "0.2.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "f1eb7fb76436f91d87fd19c5f973fe7d506f6571", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.3.tgz", "integrity": "sha512-I/hvtV2tRDV2NCPRICDW037Z3zorUqb8chGZad8jO0fhqha1bVV1eZm8fsSRUohOJFETPHLw8f8om2p62r3oSA==", "signatures": [{"sig": "MEYCIQDe/B41pH+DcrFR1ld18wo2EB49qYXHdCcr5RmdfYGNkgIhAJFMub0Iq6QjjQqxS45Gs8rTTbATRPLFm7M35A+LRYOc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}}, "0.2.4": {"name": "resolve", "version": "0.2.4", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.4", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "47d5e3e845108807480ffed66f40e533d330704d", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.4.tgz", "integrity": "sha512-EFRN5eh6BEdbr++HaEEn68pFJQxH+ol9xw1e5YkulmLnmqsv6fEM7wMDOmfv0+tFoIhK5gNYGGfQb0gCivgvcQ==", "signatures": [{"sig": "MEUCIFrVQNsFo3tVeFPaNADs5R/9g1XPqp4hVCIz8vpqy3HkAiEAuTQVPYVxVs4vuxhq4cvZVZ++x8pLSWFF0tcoFk97TZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}}, "0.2.5": {"name": "resolve", "version": "0.2.5", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.5", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "2049e344ec140ac5c41024d5bdb4d9546336bf79", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.5.tgz", "integrity": "sha512-4EkwzkxTavEa4MJ4U51h0Jni3zT0V6cnhmLvlj87AVwi3pZx6a66+Gp0lVDP6fSVcp3H7fX0nk49DvDWFNsClg==", "signatures": [{"sig": "MEUCIQC1K/M5o6g4xx68nlR7DKo/lgDm+ZMUuFUe2k88wcS78gIgAsq90TYOEFGpDvDXoG8ZkjnUr0k6dOdgKLot1+eBLR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}}, "0.2.6": {"name": "resolve", "version": "0.2.6", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.6", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "fd43d238fb9feffe677da88acfdd0da0e02ec6e0", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.6.tgz", "integrity": "sha512-FzhJOTvPXXNWNIkgablj20Fr4tqEUYfDdcokDVQDuEa4U4NNgW30TylcgtIcOq918PQB3mKbhr4er10C76XrgQ==", "signatures": [{"sig": "MEUCIBoMLFnkJrybilcJdcXVgym1384eYD2lRmrNftXvI7goAiEAldmn2FgLH03OdmDU8lL2ehuxqr+YMAb6zn/+gH2z/v8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}}, "0.2.7": {"name": "resolve", "version": "0.2.7", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.7", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "133fb57a56182b4c542c271491c57026d3b3bd06", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.7.tgz", "integrity": "sha512-Aly9zc8mEhU169V1CCtluftrwANgtB1VZWZvrx0gU+/2KGy0byMn6dYrph2Iz2VWEznINbnvvUoa7l6Uk0+3OA==", "signatures": [{"sig": "MEUCIQDZDhvbJl8vg0BQqY6OoG4hIYvQD6Yp+Ivgu4OMI4booAIgDyRKkrXobJCUjcKqnQ/3Kdi9avzOrWil+XMA8q092Ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}}, "0.2.8": {"name": "resolve", "version": "0.2.8", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.2.8", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "fdb17d4abb0ecaf6f80d67ac03cf290088f6c0d0", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.2.8.tgz", "integrity": "sha512-/u93d172yGtNlqM9641bKSdPmgaJ/X9TiD/7Mrb3DtrZ/CAvfNEXAdRABvayxLst7GGWMKkD0oGFQvwn0exevg==", "signatures": [{"sig": "MEUCIB0uAqOkE/OFXDCgSXOHKi93Pv3vk3Jq0/YiZn123ANTAiEAgKqtZRjrQIuhjabu6Hgg6f0KCVhI130WVKsH10nq4bQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "A more hookable require.resolve() implementation", "directories": {"lib": ".", "test": "test", "example": "example"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.4"}}, "0.3.0": {"name": "resolve", "version": "0.3.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.3.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "c9ca553334490ac68f75494aee2083e600994dce", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.3.0.tgz", "integrity": "sha512-Jr85PgHJjrSR74PqIHnIB7TVmGXoOIWPxLyP+7LXzeac0sApOgX0LS7Pdizqg8bNDCSNR9QCKUpPHKK17EKhqA==", "signatures": [{"sig": "MEUCIAj1R4CQj5UXonoDXwC47BHT4fD8qfM0ZrT9Cv2UJBIAAiEApksmNsZVpbA3Tls0Tw3iIyXy7O/evktGpG2ldDAnHs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.3.1": {"name": "resolve", "version": "0.3.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.3.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "34c63447c664c70598d1c9b126fc43b2a24310a4", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.3.1.tgz", "integrity": "sha512-mxx/I/wLjxtryDBtrrb0ZNzaYERVWaHpJ0W0Arm8N4l8b+jiX/U5yKcsj0zQpF9UuKN1uz80EUTOudON6OPuaQ==", "signatures": [{"sig": "MEQCIHxi562XZR99Fda6rOEaU24JINQ0/weJLsBBoYECPKu7AiApmvAgtROXIfPiEi5umL2oLFhRnOX3huaD0EudFneLOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.4.0": {"name": "resolve", "version": "0.4.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.4.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "531d572fab054e12e89fd545ad65b2e49555c34c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.4.0.tgz", "integrity": "sha512-AmlTWiCzf85NohbdfvqmAXxXfbEVEiKmiXfAMIBQXKfQlSmk+amMAnEfY9USoK672ev35jIDdyQHA+36RtfTDA==", "signatures": [{"sig": "MEUCID+ljG+Q824hqJWLLtFLX9c+FFjFdiUCix4KQTjpdmNBAiEA2aiIEovtbo8E1UuLQJ/wuyzlAkt/Q3b1JvYbCEY6h3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.1.71", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.4.1": {"name": "resolve", "version": "0.4.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.4.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "f4ac6addf19c665b4e7b2c9df5cd477cca8be370", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.4.1.tgz", "integrity": "sha512-nc6XGbv88otwAiD4VFX8c7bvw6iLOwhABIDsmyc4Gk/NZrqqPtb8li27CrrHikJSuDaTkJF632FytZCttlQpBg==", "signatures": [{"sig": "MEYCIQCr7MiRlArq5uoVSGsvCIHtu9tiBf9JAOdUpUk/Qe1JdgIhAJlfg4q78ynPIKTvXYW8GpFDJ9UwsvqOD8JCw1Gzw6N6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.4.2": {"name": "resolve", "version": "0.4.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.4.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "e6bb8e14a4e5d589e2e085e9201512f8e6b2203c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.4.2.tgz", "integrity": "sha512-woK9kVVwwfZxsIhn/zMX1wjOqnwtjH6jiakKXmwPZikApSrEA+zAbd08Exne5dfvuSy+e+bDedrlphivlqamvw==", "signatures": [{"sig": "MEUCIQCS7Ok4PEHyeGriZg7KnsVFv/9sMC2ur3PVmqQFeLJvrgIgXCIpkzViB1wOI2t+pY36m74cbq3VIqfTAJ6EbCqzSBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.4.3": {"name": "resolve", "version": "0.4.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.4.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "dcadad202e7cacc2467e3a38800211f42f9c13df", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.4.3.tgz", "integrity": "sha512-VRdITglwtstb0Hqag3iAVSYWgS/HfeuQIfUcq5Iv9Dmt+kzevtN7i5IZTJrZA+KcDHq8lSVOH8UewJxD9EMa7Q==", "signatures": [{"sig": "MEUCIDC4paiyFr0kMiZTbyohetssBFguiqE5Z/ERSlF3yOmTAiEAsTFyDbMCDvSUG2W/tthqt8rYY4PS2vRhSlYBPhKgAbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.7", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.5.0": {"name": "resolve", "version": "0.5.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.5.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "e797504cd5a33ef1dbb9bdad252b6cbffa95b0b4", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.5.0.tgz", "integrity": "sha512-JL/+cd3RKDQ/fXrCQ2H9AHbJ1QdDLJ7ui+gnbsyIjLkebV1KowQf+6Iwwu+8HNlIiN6XHoVGbXk3HqO//4R2ww==", "signatures": [{"sig": "MEUCIQCqFnsxBf2403mnwo4wSLQpee+gxNsHKjzIBtaLDEzVKgIgd9MKoQCsuDZkqlMFBRqbn4Yj7iuJyLkxhj/7EpPZLLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.7", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.5.1": {"name": "resolve", "version": "0.5.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.5.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "15e4a222c4236bcd4cf85454412c2d0fb6524576", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.5.1.tgz", "integrity": "sha512-PgoPtxVz3j45jqtNbMbxcBG+5FhjLLa425zzNBf50//c4XJDx/FC0fbAWJiVPsXOV/MLhbQslSYuEv6RFf7p3A==", "signatures": [{"sig": "MEYCIQCYnZ+/N01a3bS8SZVHqeHTvNsOpfnQx7BYF6Eh+yYFBQIhAPO9aM5IDRr55OTkVKDr4b7Kj+7/+gXJbnCdPuchyKuo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.7", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.6.0": {"name": "resolve", "version": "0.6.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.6.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "1e2b3401bd384a03494fda53be278155bb57aeb0", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.6.0.tgz", "integrity": "sha512-M6mdgQmkrw8e+VbP2JVTn1qgj+aq2Ytl1ZSzn4uVLoa8/1vPXxbm9+m9C/WtPLnlVVL5reV7wlH9qJsIgHO46A==", "signatures": [{"sig": "MEUCID5ATbhtjCOajWTy//H7xO7FKJOTwyouBxyNyAqn5Vi+AiEAypcv2PsvRFqDokv6A2DpyqIWi0Gl9/EV7fgBJXEuVDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.6.1": {"name": "resolve", "version": "0.6.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.6.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "b7fc874a8fe240b44de2c92b7f885a2e8598b652", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.6.1.tgz", "integrity": "sha512-vIlHXFIibxZ7yIzE4pAdOoKxhIFcuI9LM70z5oxGutAP6W+J9tEsNYKCFKzKBtphRuIl2QE4c7WR5AXsVjVPkA==", "signatures": [{"sig": "MEYCIQCcVATdUQLe4kBl6FMXLznlzfRTqxbZ/ACn0Al8kIXTpwIhAPd2rOSjLkgthFVFys59BZkZJc5ISt2wd1libbnm0zsU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.6.2": {"name": "resolve", "version": "0.6.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.6.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "7404e59e3c02980aa172272186521db3cf0a15f5", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.6.2.tgz", "integrity": "sha512-hmH9DydiPsOxHV4v3x43WSka37SvWcMvAu0j7LINyIQLR3ZDFETk4JbBbKVjNyo7XQjhfGuopSP0Tbt6TZkVjA==", "signatures": [{"sig": "MEQCIHV+yTM50Uv0FAhB4R5vSEfoIGi0/HxZQFlvWF+d8aN6AiADFCFlECrtmzIrQistOFoMNGz31VVtpHRO1R24qhA/fQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.6.3": {"name": "resolve", "version": "0.6.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.6.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "dd957982e7e736debdf53b58a4dd91754575dd46", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.6.3.tgz", "integrity": "sha512-UHBY3viPlJKf85YijDUcikKX6tmF4SokIDp518ZDVT92JNDcG5uKIthaT/owt3Sar0lwtOafsQuwrg22/v2Dwg==", "signatures": [{"sig": "MEQCIB2UwxQ25/3rdc+ccO+T78Pj+/ccxyQUwYuxy/T99FObAiBtWBIcDZc/YZQAZD4Cf28KhQj0M1N9t1VchL9Uacq17w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.6", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.7.0": {"name": "resolve", "version": "0.7.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.7.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "dded14da73b145673e941d71b96a2a30c0f3b6fe", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.7.0.tgz", "integrity": "sha512-o9RUUSVQPoWBiEwjp9PGHzmC73f1y0tu/AlaUxYOuHkTSXK69keW59K4Aef9TsdQrSCIjcds+HAS9gerPL1Zsw==", "signatures": [{"sig": "MEUCIQDIWMCQi4Oow32P4Ucb04zQ4NtyVZXuybE3kjgusBwmigIgceF2Y3m+HeuVUAFJqmcnNKsxJ2KdFjyu9n7lZ/v9Qaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.7.1": {"name": "resolve", "version": "0.7.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.7.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "74c73ad05bb62da19391a79c3de63b5cf7aeba51", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.7.1.tgz", "integrity": "sha512-UwPxf6ye7YEy4c29myQ2FESNuVooJcufgdU+Rh7yTA+PDR7xX/q3wLeI/vbhBXOS7zDZWEKSblPBGMaAi1D6tw==", "signatures": [{"sig": "MEQCIAj5dsoW+PTHJ83LBJD03UfCyGQ2V/dIKhFWLhPx35TOAiAbG4NU+O3sY9MpKKSizuvv5+1RWX/RlHenTzjVnLNySA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.7.2": {"name": "resolve", "version": "0.7.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.7.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "1dba8ed610e5c709e916be4bdb0f5ca400233439", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.7.2.tgz", "integrity": "sha512-kfGy7Nexe0+Yy6la0lZiKoRg0VzYjyVnelE2boMBu7VJkLLLwftAejrWOkuxhdmP3PqKlKx5on7NmWlcWP88wQ==", "signatures": [{"sig": "MEYCIQCdBmSqELzDcPPKyMK63rW5vOEwolP+nlkQfoq5KOcFmwIhAP5aHCgNChcmuHgh3qxYSdTutEol5kcNXaFsYFEUK/2l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1dba8ed610e5c709e916be4bdb0f5ca400233439", "gitHead": "eae92dd55fa92543c32c4eaff72dce2d78dd3b99", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.7.3": {"name": "resolve", "version": "0.7.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.7.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "50f30669c9fac7b240368cec4dc06dd7a296fd02", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.7.3.tgz", "integrity": "sha512-s8uZPschw9D2OWosDw977/+6KPeZrLyXG3aZaFNCGgz6wm6ffd1CfxvCb1Zmsi9dHPB5KqCF7Dk3jk8N2WsSQA==", "signatures": [{"sig": "MEYCIQDBm+ThgBPFQqFWhhY1onU2LVZ0Fr3iRxI/6/YyaDqWbwIhAIob+AqoYMsDRZoSTJ6V8d3EF6s7jXkuS4fn0IQ+90M4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "50f30669c9fac7b240368cec4dc06dd7a296fd02", "gitHead": "f6200998628490aec1c3008bb4e8bf099ab4920f", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "0.7.4": {"name": "resolve", "version": "0.7.4", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@0.7.4", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "395a9ef9e873fbfe12bd14408bd91bb936003d69", "tarball": "https://registry.npmjs.org/resolve/-/resolve-0.7.4.tgz", "integrity": "sha512-zxmAcifDjKxmUbk7chQdKhDSn8ml08g+MYyU37xhEXBp+N81cfbYsm4e0Gn9jtLbAvbR8w8Ox09xqUZtPuCoeA==", "signatures": [{"sig": "MEYCIQCjSgsIkk/kHZ1vlK0+eDG9GMETltejzKO+0K6zNmRf9gIhAPtG7cXONejA0HnIFSlAKsCsFUShIEkUNVCCvBTrBE4a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "395a9ef9e873fbfe12bd14408bd91bb936003d69", "gitHead": "4ad661931ebdd07c3df34bc897c24255705adbff", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.0.0": {"name": "resolve", "version": "1.0.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.0.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "2a6e3b314dcd57c6519e8e2282af8687e8de61c6", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.0.0.tgz", "integrity": "sha512-6mGRCiHIQAu6w9JQ582WjlRpGdl4HAbQmMGub+NlBJnP78DnS2k+mONQyhJkGvDe6xqFNVOnrwoXHonl2J0HLw==", "signatures": [{"sig": "MEQCIFSz3d4eXmO6YZAL+Gh/iw/nQSC1kR4Np8TO8hFIqHrgAiAAutLkBL0wbs6lnZxOm1N3X7CWNnGeEpFxTcChTQAnEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2a6e3b314dcd57c6519e8e2282af8687e8de61c6", "gitHead": "d0c465c88e85f05113a7fbef7b976c77ecdce965", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "devDependencies": {"tap": "~0.4.0"}}, "1.1.0": {"name": "resolve", "version": "1.1.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "f9ad602751ed06a13e58cf1eaa1565bbe38d6d93", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.0.tgz", "integrity": "sha512-e2EpdnbuJRYHuhFNkPy+bHm8rpAFz+4Es3n5ewywvhdDTtEl5qrKR5mcQj+iGf3HoWgAO0DrcmZP2xJYtsz4Yg==", "signatures": [{"sig": "MEQCIAOikrEoeYfILUxkrtpyQJ5r0CTus8InL4P+uSckY9m3AiA5IfhV/YohPjTM9a3tGkLojUdviO1cgYheHP6qoQS2dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f9ad602751ed06a13e58cf1eaa1565bbe38d6d93", "gitHead": "65e1789c3612c1b04ad5002d1131d82e8b6262e5", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "0.10.35", "devDependencies": {"tap": "~0.4.0"}}, "1.1.2": {"name": "resolve", "version": "1.1.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "9bb3df6d6c7b97e96149add8770ccfe3f649a45d", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.2.tgz", "integrity": "sha512-X3ZQIvaMjsUyKOnzhyWgPiVwSDy7IR7/LklPpq6EjMw5Ah0cPayc10R7cCYngJMmF6v+LKR2C5CP/8ODDul9+g==", "signatures": [{"sig": "MEUCIQCOH3HYzAHW0DojzqY4jz+6q+w0gyEgc9ORWEZ1y4doCQIgSpaXn+IzYCcHYLuKhL0ibKIf0kpEQs/gAVLAs2gj34I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9bb3df6d6c7b97e96149add8770ccfe3f649a45d", "gitHead": "d5f6ad02eae9b504e5edfdfaf2857600847c9bcf", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "~0.4.0"}}, "1.1.3": {"name": "resolve", "version": "1.1.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.3", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "0c23ca8cac81c192ac30399489c3185f2b42da9c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.3.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>icznOoEieU/O9oqp018UB/WSwdWv9aSbEfhmYPNXEcaonyQoXxkOonBDsy6Bx+L0+H4fMkF7M9kWV5txFw==", "signatures": [{"sig": "MEUCIFaLE6AmoRCcoew+/Pkk3LdY+o25L89jurZKbvG3ZgnrAiEA7gojPn5TZzCU73WC+aa1S5Hj/OFu8y3kTySGx1Eaf2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0c23ca8cac81c192ac30399489c3185f2b42da9c", "gitHead": "70146a5ebc4d96438383ada02785d4e722c6f5d9", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "0.4.13", "tape": "^3.5.0"}}, "1.1.4": {"name": "resolve", "version": "1.1.4", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.4", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "c8e58b8c57616e84298e053b39e417676a55ce09", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.4.tgz", "integrity": "sha512-yNCvpZBMDiDjJv6dSaPZMMJ1ZyfnnO9EkkogIRvQqjTPoz9mn9qpXWzx4r95nUSfOd/Io1TPxIIc/k5mDCOErg==", "signatures": [{"sig": "MEYCIQCp6aEHR1Wvw+0xWgH4wKupoxS4gy41olW9/TliL0B3VQIhANidSBJSnsSblWU13xyZb25vGBBlFek9YiQDO8mp3loB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c8e58b8c57616e84298e053b39e417676a55ce09", "gitHead": "7496374878a8482f6bc26bca474595cfb81ecdd2", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "0.4.13", "tape": "^3.5.0"}}, "1.1.5": {"name": "resolve", "version": "1.1.5", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.5", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "3b74c0c44cdf5eee32322b2cda0a4acbf6970fa7", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.5.tgz", "integrity": "sha512-YOpZc7V9L8UePByDdFoBmHLoubnjsZTbJl7gP3Cqo5FveRGZz8WwLaK9nHjpz1cnouqOpFdgeYnTpvDYncecjg==", "signatures": [{"sig": "MEYCIQDWkXkPh9r4JuvCZQHOJIqLWslaunNEt3cD9wOB9f4GeAIhAKniIt7HFe1PM3KwYo55Y7P0oKsvbKFkN4E50ZTdyYjN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3b74c0c44cdf5eee32322b2cda0a4acbf6970fa7", "gitHead": "a225602be4ca80f75292a6a17c78ff3b27eb0bf3", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "0.4.13", "tape": "^3.5.0"}}, "1.1.6": {"name": "resolve", "version": "1.1.6", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.6", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "d3492ad054ca800f5befa612e61beac1eec98f8f", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.6.tgz", "integrity": "sha512-SMcLzi1wJbhxmLyENfy1431vlUnIjKT3sY2BDEC0Z68e1ZpjRmXCdXzYL+P5HwixED2GIITUeM1DlHBM8q1+og==", "signatures": [{"sig": "MEYCIQC6ybDf9QUHe19SBuaOa7uQy/kbmUwSKXSiBQyfvRoQvAIhAM9A6brgQCc5VY0EB3PO2JsJAxBcwLdNpx15kyR5/sH2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d3492ad054ca800f5befa612e61beac1eec98f8f", "gitHead": "38d451c0ecd9267277a7683970432d37f001441e", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "0.4.13", "tape": "^3.5.0"}}, "1.1.7": {"name": "resolve", "version": "1.1.7", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.1.7", "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "203114d82ad2c5ed9e8e0411b3932875e889e97b", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz", "integrity": "sha512-9znBF0vBcaSN3W2j7wKvdERPwqTxSpCq+if5C0WoTCyV9n24rua28jeuQ2pL/HOf+yUe/Mef+H/5p60K0Id3bg==", "signatures": [{"sig": "MEUCIHp7LnqqGndPjKcq9vH+N9nRHFcKP9Kv+2/id50HfYtyAiEA7HasI8G2bhv5u8EHnZwnXGdfFlXbGuqoNVQDH1/9MGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "203114d82ad2c5ed9e8e0411b3932875e889e97b", "gitHead": "bb37f0d4400e4d7835375be4bd3ad1264bac3689", "scripts": {"test": "tape test/*.js"}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "3.4.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"tap": "0.4.13", "tape": "^3.5.0"}}, "1.2.0": {"name": "resolve", "version": "1.2.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.2.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "9589c3f2f6149d1417a40becc1663db6ec6bc26c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.2.0.tgz", "integrity": "sha512-+jJIwCqx3jFzPJUizKYOnCxS8Rh8n1LP7VxgAe1/vZxEH2FyfQL0NNmsXJPJ2TxUpeOdDSFCQlAmImpBG/Uc/g==", "signatures": [{"sig": "MEQCIBqKJdB8KYPZY+57Pu/TicH+VylITbAiDZB+2TsTFqfwAiAJXEpcfZUfvoDbTWslmu+2lwYp+4m5ftw79oi9CDYRCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9589c3f2f6149d1417a40becc1663db6ec6bc26c", "gitHead": "8e4a4659f4120c145e2f12bb01cf4ddad61730b3", "scripts": {"test": "npm run --silent tests-only", "prepublish": "! type safe-publish-latest >/dev/null 2>&1 || safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"tap": "0.4.13", "tape": "^4.6.3", "safe-publish-latest": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.2.0.tgz_1481676943045_0.8319015400484204", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "resolve", "version": "1.3.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.3.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "2af115a2e7f54a322dc879914311fc826b4ba83f", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.3.0.tgz", "integrity": "sha512-H+kHTfNL+G9qsMbKDUsyCGop5qRzCQ7Y9arjED2tmc8w3Q6fs2Ao/TBryspiFlIW9ZydgL55C/xr9dxK5kH+YQ==", "signatures": [{"sig": "MEUCIQCPmwvQPnXbwaemWiCzUb17hkKrcXOt7/fGlgTZs9yFZAIgHDOK5xACkrbnHeyoLZLX/ZAfwItRTsWjIEr6L8nK57E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2af115a2e7f54a322dc879914311fc826b4ba83f", "gitHead": "c6966fd37d985aca1191711f9993bffb7ba43e96", "scripts": {"test": "npm run --silent tests-only", "prepublish": "! type safe-publish-latest >/dev/null 2>&1 || safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"path-parse": "^1.0.5"}, "devDependencies": {"tap": "0.4.13", "tape": "^4.6.3", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.3.0.tgz_1487923387821_0.8103991178795695", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.1": {"name": "resolve", "version": "1.3.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.3.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "5d0a1632609b6b00a22284293db1d5d973676314", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.3.1.tgz", "integrity": "sha512-4j4CmBQOor7L616RdY6RI7HLtvk4MXSUjd8T3N7qDksQ1A0nM3FMOnLgfEYPA4e4X3u9LVbZzkAfjx0QLiar5w==", "signatures": [{"sig": "MEYCIQCNf9ec7UgEIfxEmjYHmVxNgEitQDrZkgDhJ7LYiIjnRQIhAPeiMZwSCqHy4BDbDcKuZj/ABXHoOTmYBRfhyZSeFzoR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5d0a1632609b6b00a22284293db1d5d973676314", "gitHead": "05a5ab961b5720bdcf1a641c093a4789af700506", "scripts": {"test": "npm run --silent tests-only", "prepublish": "! type safe-publish-latest >/dev/null 2>&1 || safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"path-parse": "^1.0.5"}, "devDependencies": {"tap": "0.4.13", "tape": "^4.6.3", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.3.1.tgz_1487930636460_0.8978362330235541", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.1": {"name": "resolve", "version": "1.2.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.2.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "0fb2989c0a86a1c545ce918aa36a8809ff7356c5", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.2.1.tgz", "integrity": "sha512-RowGQ8jT9cg9pYLz8v8LHHQCiy9ll8bK7KAHhX0IgVyyf9kVkK1agBjTB4qtt209YrX9iUr73fyddThZOvDUVQ==", "signatures": [{"sig": "MEUCIBy4ErBl9lesRpVuYDkrNEVCud49c5QLqkgJOvBJuzQ0AiEA+d/Rp8TWhzno3SbaCLTn+zkX9QJ6/cr1vo558Cs5arE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0fb2989c0a86a1c545ce918aa36a8809ff7356c5", "gitHead": "a73e1114ddfe9d29cc8f1874d6b704d9ae8bb220", "scripts": {"test": "npm run --silent tests-only", "prepublish": "! type safe-publish-latest >/dev/null 2>&1 || safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "7.6.0", "devDependencies": {"tap": "0.4.13", "tape": "^4.6.3", "safe-publish-latest": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.2.1.tgz_1488149708063_0.26836016145534813", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.2": {"name": "resolve", "version": "1.3.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.3.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "1f0442c9e0cbb8136e87b9305f932f46c7f28235", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.3.2.tgz", "integrity": "sha512-eKTFjt7CDhKxWDXyXmvmFzsJ8NnsHAji1XK+pvMMxek4LJN4a3LQwFynIq0297xNRPC5JyQXz8HBOtRk4+8AbA==", "signatures": [{"sig": "MEUCIQCvaFQGwjRDnJ4jNM00zGoYWArleulvX7Lx/82oy7Wx+gIgfTCIjgQm7St5C6QXy2KEV0Sb1vrCW4o8qB/SZ1QNZUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1f0442c9e0cbb8136e87b9305f932f46c7f28235", "gitHead": "781da847169b8ba43f65ed3d9dbc1283d5bde74c", "scripts": {"test": "npm run --silent tests-only", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"path-parse": "^1.0.5"}, "devDependencies": {"tap": "0.4.13", "tape": "^4.6.3", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.3.2.tgz_1488150101096_0.05632958956994116", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.3": {"name": "resolve", "version": "1.3.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.3.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "655907c3469a8680dc2de3a275a8fdd69691f0e5", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.3.3.tgz", "integrity": "sha512-1p/C+O7k1Gt16zZRRp8wWxNr8N/7hBP25g3OcUxgYB18hUx0k1vHaIvI9wtVfCNYogxKAYLdpLF8MMB5eh4IGA==", "signatures": [{"sig": "MEQCIGGnU2fCPRRx7cUHlBxqIbtRhW9nco319iJ3Qtgx6vIzAiB+9RfwqUMec/aEQu8jsnnOC4kz6cYcM/B15hvvC2uwJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "655907c3469a8680dc2de3a275a8fdd69691f0e5", "gitHead": "f0098226a4fd0dedc85b5f1e8ca8aac6a7ca7a60", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"path-parse": "^1.0.5"}, "devDependencies": {"tap": "0.4.13", "tape": "^4.6.3", "eslint": "^3.19.0", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.3.3.tgz_1492668562208_0.1827435742598027", "host": "packages-18-east.internal.npmjs.com"}}, "1.4.0": {"name": "resolve", "version": "1.4.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.4.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/substack/node-resolve#readme", "bugs": {"url": "https://github.com/substack/node-resolve/issues"}, "dist": {"shasum": "a75be01c53da25d934a98ebd0e4c4a7312f92a86", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.4.0.tgz", "integrity": "sha512-aW7sVKPufyHqOmyyLzg/J+8606v5nevBgaliIlV7nUpVMsDnoBGV/cbSLNjZAg9q0Cfd/+easKVKQ8vOu8fn1Q==", "signatures": [{"sig": "MEUCIQCokXkO+dtw8BDoD1QeyUxPGq97wRsnhRnSI1OXOPWLNgIgLUwuRLdHfPFXKvmQuLEmiwymGLUUZwUQASqu9NshYyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "744f8162464a104aca60777e5f615418dab3b774", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/substack/node-resolve.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"path-parse": "^1.0.5"}, "devDependencies": {"tap": "0.4.13", "tape": "^4.7.0", "eslint": "^4.3.0", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.4.0.tgz_1501111654686_0.44756864523515105", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "resolve", "version": "1.5.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.5.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/node-resolve#readme", "bugs": {"url": "https://github.com/browserify/node-resolve/issues"}, "dist": {"shasum": "1f09acce796c9a762579f31b2c1cc4c3cddf9f36", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.5.0.tgz", "integrity": "sha512-hgoSGrc3pjzAPHNBg+KnFcK2HwlHTs/YrAGUr6qgTVUZmXv1UEXXl0bZNBKMA9fud6lRYFdPGz0xXxycPzmmiw==", "signatures": [{"sig": "MEYCIQDVjlrqbyCh938XgejLMOsgtC7AImxyCraf7Z1c9vN72AIhAIay1hmkNWo1zoCxFD3pJkAEM7vyB1DwT0R3xQ6ZSTRJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "19cbd5e0b67c78176e771a1a6fa3a6ffd478e3ac", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/node-resolve.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "8.8.0", "dependencies": {"path-parse": "^1.0.5"}, "devDependencies": {"tap": "0.4.13", "tape": "^4.8.0", "eslint": "^4.9.0", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve-1.5.0.tgz_1508892739498_0.5415699891746044", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "resolve", "version": "1.6.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.6.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/node-resolve#readme", "bugs": {"url": "https://github.com/browserify/node-resolve/issues"}, "dist": {"shasum": "0fbd21278b27b4004481c395349e7aba60a9ff5c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.6.0.tgz", "fileCount": 71, "integrity": "sha512-mw7JQNu5ExIkcw4LPih0owX/TZXjD/ZUF/ZQ/pDnkw3ZKhDcZZw5klmBlj6gVMwjQ3Pz5Jgu7F3d0jcDVuEWdw==", "signatures": [{"sig": "MEYCIQDJQuDyVDDJTCj5xZM+xGYcZvmTZDT9ff0u+JVWXXejxgIhALdTLlP5keWoTt0bzzxxHonb577aune/6cqdiJEqq99Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70507}, "main": "index.js", "gitHead": "1de53b227990cfcb07e59fdf49d34b2a8373ba4c", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/node-resolve.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"path-parse": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.0", "eslint": "^4.19.0", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.6.0_1521546395321_0.02452950987026914", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "resolve", "version": "1.7.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.7.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "2bdf5374811207285df0df652b78f118ab8f3c5e", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.7.0.tgz", "fileCount": 75, "integrity": "sha512-QdgZ5bjR1WAlpLaO5yHepFvC+o3rCr6wpfE2tpJNMkXdulf2jKomQBdNRQITF3ZKHNlT71syG98yQP03gasgnA==", "signatures": [{"sig": "MEYCIQDsLYQ8YNnYyGL/4DRuCOBxhd/1ne4WMng4MgDBs2CfUgIhALgR6wB+Os/5AeWFUkgfVQUZYAXqra508Cl07Rc5PC3R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75402}, "main": "index.js", "gitHead": "bdf1210d96f3e06c00fad051917950eb5238ab05", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "9.11.0", "dependencies": {"path-parse": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.0", "eslint": "^4.19.1", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.7.0_1523137085326_0.2818222877391463", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "resolve", "version": "1.7.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.7.1", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "aadd656374fd298aee895bc026b8297418677fd3", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.7.1.tgz", "fileCount": 75, "integrity": "sha512-c7rwLofp8g1U+h1KNyHL/jicrKg1Ek4q+Lr33AL65uZTinUZHe30D5HlyN5V9NW0JX1D5dXQ4jqW5l7Sy/kGfw==", "signatures": [{"sig": "MEQCIGaohCdq+gl26uw+M+w5JB4hHBZxMEijgSX9DW6PF19uAiAyo82lYqnm5BaZHE+fZgCgpoivmxVjD+GWrcTF9ppwGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76446}, "main": "index.js", "gitHead": "579e2b161a7cbb79284b335cc3b605287da27610", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"path-parse": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.0", "eslint": "^4.19.1", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.7.1_1523518976988_0.3249811404950176", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "resolve", "version": "1.8.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.8.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "a7f2ac27b78480ecc09c83782741d9f26e4f0c3e", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.8.0.tgz", "fileCount": 74, "integrity": "sha512-MNcwJ8/K9iJqFDBDyhcxZuDWvf/ai0GcAJWetx2Cvvcz4HLfA8j0KasWR5Z6ChcbjYZ+FaczcXjN2jrCXCjQ4w==", "signatures": [{"sig": "MEUCIAmFTcRif3kDhP6pUyGmSd70MODr6Ks3T/rvmaDf/c6JAiEAyI0yAFjwMGZNcZlKBv8Sn1ISATfk+430xOxTyTj/qZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJCN5CRA9TVsSAnZWagAAzakP/0bd5rOjuOg0iqBs9oAP\nuF5O4h1ysWKqueFMyLW772upb+B1bhOcYweB7/dyEwUg73Mee32kkXjJ+WaN\nI1tcnaIOPYp6V7rtQVw/keElvV7KbEqmT0lgw5l39mwLWA01W0aG93219HbY\nEi3EYPojf4D/HuWclKy1zWXO2H1HLuEDNgCLRdo7zNux2V6CM5EyADSJe8wn\nZ2GSdo9B6s76htAAUUd9bkXismCcnGpXsJ7s4GL0WZX0DOjDua/HGJ7Z5ee0\n/jJO62KT9acnYZyZs1L+eCsTRKN+ASbU7RXRCXoXfer4IfBk6wCVdcnfje/4\nmu3bDsHAW5GDF95wbjhYGtRHw2nqoxY4wQyBNYNZbhNWOm3O6j6UjNNF1Jyg\nQpBb7dcly5PAdC3GCyyzzKc0RNYJbE4DTltgkq0b55NFjbdW1YFSmV6tjBLH\nztNhTBYqhBtKXmAbm/rkvFKOkr5MEqEdvZxaDwxBuAYfsgfY6zhp73GbGf7Z\n7Myr9fCID9OAktfCV8vaMq+pshZYoLwWh21cmIlTfwiZKOp6WCbIwh7zw15X\n3tfK5WrdAkXFdmiIu/FyLooWUqxLb4NwdN5MtuBe8m419I3mTC0SvLCM19BZ\n/X6dUW4kiwPgaxOW0hJ64pBz4HAJq6BV8lWdB3WL2DTJB8uLTMU6pDELUJhR\n0xIs\r\n=4U7W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "dcba6d077708ef93085270526bb8f982b66fc16d", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "10.4.1", "dependencies": {"path-parse": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.0", "eslint": "^4.19.1", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.8.0_1529095031897_0.11662929601217797", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "resolve", "version": "1.8.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.8.1", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dcousens", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "82f1ec19a423ac1fbd080b0bab06ba36e84a7a26", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.8.1.tgz", "fileCount": 75, "integrity": "sha512-AicPrAC7Qu1JxPCZ9ZgCZlY35QgFnNqc+0LtbRNxnVw4TXvjQ72wnuL9JQcEBgXkI9JM8MsT9kaQoHcpCRJOYA==", "signatures": [{"sig": "MEQCIG2PeVKBIpb80NASd1B53ZbUO1rvVXRYJkKFZlChydQvAiB4Uo7gGrDem8gKENk79qS49KKO6ugcCTx9B+Z7EqrdCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJqgcCRA9TVsSAnZWagAASdEP/0buJQqIMth2dWI2KEtp\nS7MimpOijMWwCmrTmwyCMiS9d6wVktFJmLLWHYmDwKQS6PoglJ7DqFEDrj4c\ncd2yuK3pZebQ0EQ/Y+qSJ9Z+hjkKviEINgZAp/KuaWuBmJyv+jXnrazrfN3a\nEa5EDv8j98vYwM0jSpW/FBIbxg7/vLRHEZCRtKxBCDeT0P0Wi9xtHBGJXSqg\nV5ivkTnOW0f59BD8iAuMAHgQmnfNKl3x9QGx8IjwYv9o96dmK54PdKrYKcKT\ncjXgV2wWc8VPmJdPTaIJDp0NI2/RF9OMlVMGLQE41XX+56cGuIABQTrMSaXb\npHWYJVpN0n/SLR1ApIB0i+boCKMQ/89QEPxUykcjiQCwoWZfv1+OGnOvL1Fd\njPFf+oSfSfOvr+tivcGfl6QFTaV7A+svLijhSIgtTlrWVcQ933KHH14wEHo5\nb3JUpsCa2E04R2l1Jc17jC3HUTqX8UvIYPv8goayEPa42A8/skZlTSo73AGh\nY7PLfvT3VC9AI7XBoCWBTMSGrMVkmsOdu33WVDUkULHf9vFvwqJ5D9yAMgbo\nWvCYCjerYufkYvuFfyMqRMo5tRcjLPX93ZHcnhNLZ9bvSUo4x+mG3o87On4Q\nw4nliih1/pk1iN32s6pelyG/xVleZGnJtPaKKgFOXdVAFyH7LJMMvqelK8Pu\ntjce\r\n=D30K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "b5fc91bf59e6da3aafedc8a8ae4ce53907c06069", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "10.4.1", "dependencies": {"path-parse": "^1.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.0", "eslint": "^4.19.1", "object-keys": "^1.0.11", "safe-publish-latest": "^1.1.1", "@ljharb/eslint-config": "^12.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.8.1_1529260059580_0.8035259400498278", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "resolve", "version": "1.9.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.9.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "a14c6fdfa8f92a7df1d996cb7105fa744658ea06", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.9.0.tgz", "fileCount": 85, "integrity": "sha512-T<PERSON><PERSON>ye00tI67lwYvzxCxHGjwTNlUV70io54/Ed4j6PscB8xVfuBJpRenI/o6dVk0cY0PYTY27AgCoGGxRnYuItQ==", "signatures": [{"sig": "MEUCIQCMJRaN1btObAP1XQG1mr7L9wxlxDUGGgsEAl93Cf76OgIgM+OWPts8yA+lgUPf67tE/fFlr1yhi5Wvy1RHfeQK61w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcF1ttCRA9TVsSAnZWagAAQEYP/236JOC06weZ/2MfdHCS\newewF6EL+Jp8faKj9MoMCa26Thc92ZrSIgTTWoKQghOxeH8s3487QW4UtnU4\nj3foDqeUAWyPEqJjNK13XoljsDNReB+7lQawuWtwLdVVNdU1BbTzCJjBRe9M\n3MlRSOA9E+rlGZz4EgCehfqKX/3LxEt1lOV5aaM9QzjMyEXdEpOkd+0hV+rI\nV23SWK2a678k9cXvQjyJ9s4WJk9RQ0rNdf0NrckDGDKRQFnMcpzboVe398hk\n/I8T5+9mQogJ1j9Wo7lPyKIsEidM5eU7gpPUK9Y2ocWdlHTu4044j2S9fo+3\nic0TCiZALZVEztfVfQ8VEVEMZEfEO/DmjCJcL2FvDXl3lFHjOBzoaf+JUsc3\nEGyP+11QWF+EyeHPAoknZevWaG1QLbTFsYDt5qkKrkJMwpLHMFmpjU+GT424\nTyLxyN4gu+wVnvH0636kvDcXlZF/p2UrZtBJ3rhsoYVmMaQjMXltqUWwq+ZD\nEmPEUUGvyzZUhd52VMPlbeK/W3VvgiICJdBm0CRN1DBDXszcwG2Q4eLbdqCN\ncEzhtUQMIqggi9KNEmzqGj7/DwKfzIDw4Rd/CrODGtVBiV9HnMSDQ4KUYLh0\n08n7EPVkT6CjqGs6hS6ggS+FX/zqgAhSh/3VgxO5CPkyqJsNg2HpGkCZt6oU\ndbVl\r\n=VxWM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "254bb4029df2f8d20a33043dfabd8e5cabfa37df", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "11.4.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.1", "eslint": "^5.10.0", "object-keys": "^1.0.12", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.9.0_1545034604491_0.19497105893164424", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "resolve", "version": "1.10.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.10.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "3bdaaeaf45cc07f375656dfd2e54ed0810b101ba", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.10.0.tgz", "fileCount": 85, "integrity": "sha512-3sUr9aq5OfSg2S9pNtPA9hL1FVEAjvfOC4leW0SNf/mpnaakz2a9femSd6LqAww2RaFctwyf1lCqnTHuF1rxDg==", "signatures": [{"sig": "MEUCIQCrIN6CZQd0cMP4Ju+4QO5auRTNIuGXL44BU3c9U8/XiAIgFT8TWuAkx/kKkg7u8nmFosHWbNJsYXTnQLVWMV9vcqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjPTCRA9TVsSAnZWagAA6MsP/2QdkTOviKrHYvE3+qLO\nWi9QoL8GwLqO2YxrInAgBi/AYihINTa9qkRefFRsB0UPVf7q3UIjN7Mjt/uM\n7bqFc8BOXPLBRxOvs/SxlY/WrHCLpJli6tvWDMwPa0Vsx2PufdSoxMTnYRua\nf6l0u0mQISLtK0fN5kLpvUNTDzEoZPe1ft6YlWLsRgbZK6VMdJet/c3LAwQx\nGpGchIOdNvjRAp4tcpixg1HJXa/pYdjK+sINqCjF9HG8QzDeIqznD7/dQ0ck\nZed5m62jSicFQSDqg0t+oSQ2OSXOj+UMuxn6qJ9Lg6cC8ZQZyzR6TmbDsERm\n0AEE2X1toWDQcot+pHC8Y1vKiFC5EnuHuGHbH/d4FzNB6iZ6YjfMxFmRU3s2\nc4BaZW/X4Sq5wFIvWeN3wXzPh2Sa5bt6q/RZrd6niShf6P/zH6rVJ5/9iikZ\nG0UM/ivXC7Z1zGp22RKUDDHYUYGfkVKj+ZRijSzxLC+Qg8H571azo/4jux6s\nRRubyBGddRy3ErlNQCEMUeIqe7ofpSGhliNNXqhPUGs5BBq4+DMYlU4li1W8\ns1Wcr5OIiBHdKorMN4jkheaP03dwLvMflnCy+l3QyFgg2Esm1JzDcCEPwt8g\nve6H60Q5zY3/gRzlcAIBLAEdw3ELas0VxNyqI5NzCfq4Y/M4KFjY3VA+FvWT\nl4Am\r\n=Fagu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d098e92a7c5d7919d18ccd4d7a284ea97d11e586", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.9.2", "eslint": "^5.12.0", "object-keys": "^1.0.12", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.10.0_1548104658529_0.2309574974395905", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "resolve", "version": "1.10.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.10.1", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "664842ac960795bbe758221cdccda61fb64b5f18", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.10.1.tgz", "fileCount": 85, "integrity": "sha512-KuIe4mf++td/eFb6wkaPbMDnP6kObCaEtIDuHOUED6MNUo4K670KZUHuuvYPZDxNF0WVLw49n06M2m2dXphEzA==", "signatures": [{"sig": "MEQCIBPuYwTZsf4zfaZ5ocn5vOP8AXYBuIDdSCN1nD31dwItAiAn3rfkbHiDIyGpbHvlh6/Q2QHtDd1aeimb75HR8r+NXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwARQCRA9TVsSAnZWagAA7YYP/1mAf/vIUVUF3+8CxUDD\n6XUe53f1BKsMwfv1+S+OW9yXQhKsBf8feqfgmeuJ/ejmKqBvDDfVqp4veUEx\njmQ7I1NRsUFkZwoFvhaUeAyr+kqLEVjqBkWGOFRv+8oPeLr19a78X/FkrsLz\naEq5xzyznqtU0GZWvEK2ecVazl38vEJTM4FoVvnFk2mS5e/z/h/7oSjA4o47\nuEsZtKakykBwmKspbyJ8wCt/oq96LfFctHZ56Q21nHyUgsSMPvFaA5xXVlBY\nJ/xLUwG5iM2FDq4lNycL90TSQVLbf2k98umZ9NwZWGZEwfk6dJ057jSQfVYm\nkmxok/IYQcnnKychr1yevB4ToQbC/4BsJmR2GUyuI3qzG+UkCQRIwljTEFpN\na3afe+GuKPOdsKlX2Xyd0QG6WFOYPXNJ5JHnmfK18Khia1bcQIPgaAXfySRo\n/RWF5kQbZJToza/3nXKwHYyX/QP2liH/0yOrG+V/9PmaAbZQwg8YSjUJcjnz\nSYn/HNQb5pSKYw7hSjzpJ9FhF1H/Wu1dZg2t1nklxGpjKIh1AUolKD7nb5+8\nlY4Zyiqbg/nA0pk6CwcJFmWqltqoeK9OtDGvuCDefv3LqULNDj9w6msZaPKZ\ndANukgpTH19o3MSuWCGDuXI1YBY4ktzn1XmfLmjDVKjMTbSHPmnaHdeu9NSn\nKS+o\r\n=J7x9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "99a2ef57758255e10269f12de6c97b832a5f5fa0", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.10.1", "eslint": "^5.16.0", "object-keys": "^1.1.1", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.10.1_1556087887537_0.9559557636826803", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "resolve", "version": "1.11.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.11.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "4014870ba296176b86343d50b60f3b50609ce232", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.11.0.tgz", "fileCount": 85, "integrity": "sha512-WL2pBDjqT6pGUNSUzMw00o4T7If+z4H2x3Gz893WoUQ5KW8Vr9txp00ykiP16VBaZF5+j/OcXJHZ9+PCvdiDKw==", "signatures": [{"sig": "MEUCIQCNrFI4H6hNcSdhEqOrXOd7ybQHN5pjlsxiskbmc+fD1AIgfZkk1gAk4MJdcF0tcfISM6P+mnMtxmrLUsgEu5XZw4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3GG7CRA9TVsSAnZWagAAHfUP/14z/m0dy+F21lIsBxHb\nVecF7kLUXXzSuYuIUIE/bh4WGsY3sy6VZPTgPKdZv5BZf4EWWQIGSGwu1x11\nAkjx9P0+lMS8VOEO5bWgX36hMMZXp+cL4uFMgqWKyyNOS2uNGIoaQVgBKWQH\nTfNST4DGMZtc5VhJNg8mCTJP6puwCNNGm3jVRPmVQgOUZ+38JEmZY+BmcUK4\nxWOTSTRKFjc3w5QozCTvHMTHaG9TDKDDYL82Fahr7j8/p5NQ9zFTxVv1C2RL\nqz5sThGQKHKL9Jbhosbu/hpByfxDaYcYstAmFOQI9CsL+w5D327Q6bQ1IK5F\nDa4PO0tFQM2dhzSI/hwqJb8kzDcxIk11ToHcI0M5xsYy3tOgGjUrQcjhrqxi\nKaNXkEcBy2r4Pl/A3JXJi6jGUb4xt7evADsAypW0gCS8vktY7Ty9Ru6mcGWU\n+NFRYrz0xWgIfV70G1FNxQ/FXniJsiy6tPZy/eMducmGMUV39enpCmloREgh\ntOrxrU1wY60x5SCp6cT+B6IK7tmSsPsfbLLres9T1vWemDkKmWgSr5rh6LVA\n1VUrDI8qjtIhMfiQLNJSizlk+pBdrk2I5NWjiBQgDAi+YDOvQesIitTQ1veW\n4CGJ165gQ7IYvWKiRC9BSdcQ+GbHsbJOC/R8K/tThcrnfeNSEHCL1u/covlX\nsezS\r\n=s5dL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "da345f39588998003a53a7d9959c4df40e04fb23", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "12.1.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.10.1", "eslint": "^5.16.0", "object-keys": "^1.1.1", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.11.0_1557946811120_0.4088999308496508", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "resolve", "version": "1.11.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.11.1", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "ea10d8110376982fef578df8fc30b9ac30a07a3e", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.11.1.tgz", "fileCount": 88, "integrity": "sha512-vIpgF6wfuJOZI7KKKSP+HmiKggadPQAdsp5HiC1mvqnfp0gF1vdwgBWZIdrVft9pgqoMFQN+R7BSWZiBxx+BBw==", "signatures": [{"sig": "MEUCIHrnOb+3DSHfwRxv6ETdCNst+JTkajO8hP7d0/Ru0xX/AiEAu6sQqoFjKUH6ksaBXQYpPF0tWkWvMbPvApnTqdxNCls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9W+yCRA9TVsSAnZWagAAAnoP/3ljhkyB1pqgkVpT+3z8\nDjM5zmU/Iy3IXo28jJGas8WSL5cVrlIsdAw+uWxbj3LzBhz3v8VwAPLHcMfo\nuaW6Q6hRdKRhhzRkFl1Ip2nG9x5JQQDBOVKUF4RJ9CRh/DxxTjPQYsexO0jm\nxTJveQCHUG7ZtPq69hR/gTXQsUWTxxwOgaODCnoUKHGa5hKXoeVQgqETxM0r\nPzr0qbMe32zNxpjbhzlmDN+dCWaWD9th+IpdjQHevRZK+K7MpLO7KyXegRoW\n3pqdYtxbJZaDSk8m7EmYzzNecdcBmATsJXRqjip2fQvpd5tOiUAN39F3D7I5\nOaXipbR6x8oLu0yYQzQmtEeAH+l58Wig3JF0j00/9PTkBDEWpASiJ9jWMqbC\n+eESL7W9GhCjGCmG3sfdyPCJRKdOalLwrks8vNuuHh0ER594uqxmm5aJONeo\ndHa0leuuFd777oU1mUptcRY8T5U2oSCHvezeIbDAJ4L3DBCziPTB6t09Devs\nmo2GHQTspSHbfeGQBi2Awo1ZaDRisvOpL6ZQYB7xAx1EfdmsNjUWu3fGR99l\nuR8KELYRRQk16ZZxmkxoJ4Am5ocaOh18q/oBxo1p02mUUy7mW3kH86KC8uNy\n+V8Yr6PXW/81JUz0uV3bKrHqC7UiL6dByOGzLRNDzfBkblIgJere8mg2VAgY\nVSBr\r\n=Nz4z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "b234492f27a2437b1b281edd432b4d97163db95c", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.10.1", "eslint": "^5.16.0", "object-keys": "^1.1.1", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.11.1_1559588786109_0.9881098789520049", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "resolve", "version": "1.12.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.12.0", "maintainers": [{"name": "ahdin<PERSON>ur", "email": "micha<PERSON>.<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ashaffer88", "email": "<EMAIL>"}, {"name": "bal<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bret", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "dominictarr", "email": "<EMAIL>"}, {"name": "el<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fpereira1", "email": "<EMAIL>"}, {"name": "garann", "email": "<EMAIL>"}, {"name": "gkatsev", "email": "<EMAIL>"}, {"name": "goto-bus-stop", "email": "<EMAIL>"}, {"name": "hughsk", "email": "<EMAIL>"}, {"name": "indutny", "email": "<EMAIL>"}, {"name": "jmm", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jp<PERSON><PERSON><EMAIL>"}, {"name": "jryans", "email": "<EMAIL>"}, {"name": "leichtgewicht", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "lukechilds", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "matt<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}, {"name": "mellowmelon", "email": "<EMAIL>"}, {"name": "parshap", "email": "<EMAIL>"}, {"name": "pk<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>", "email": "maoch<PERSON><PERSON>@gmail.com"}, {"name": "substack", "email": "<EMAIL>"}, {"name": "tehshrike", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "thlorenz", "email": "<EMAIL>"}, {"name": "ungoldman", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zertosh", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "3fc644a35c84a48554609ff26ec52b66fa577df6", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.12.0.tgz", "fileCount": 90, "integrity": "sha512-B/dOmuoAik5bKcD6s6nXDCjzUKnaDvdkRyAk6rsmsKLipWj4797iothd7jmmUhWTfinVMU+wc56rYKsit2Qy4w==", "signatures": [{"sig": "MEQCIHuZshd360SlaJvUMS0FTLb47bWCaHE5jW8et97bBOWOAiAGgCZhgqPegO1S2kG5DveFJB4Kq2t4ZX9LRVv+4+dUAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQoiJCRA9TVsSAnZWagAA0W0P/RI6csd4N1j8nVSjjZ0r\n2gWQ2cnMv6qJBHoTb3gzfb+XIDwcFeQ9RStQfnmJuL91eYXJVxT36DC8iwdY\nH26bWA0apNvVMv8q+Ni73n2kijOMLatov+wXhyIE3wmIxY/BruM8k43CL6IP\nbBfnrkWXnq+/8yZS35H1elflee9WBnDgnmK0UsULI2ClbnreAHISbZ6c8Qpt\nSsaqIX2MJUAAFm0X4GH32wRbKdkZGMDYXYJCuynbVcRm/s0cbLL+CrhkgfXX\nGxQfjSaUGzwEoyjnfXwH6d14FvI6vo5NYJdV1JOEf8LOV4r+RLsGRe3A3Xzm\nVfldkUr2CSdBzPW+7tuaGwQ8/kN9AydfCuCg8ZQqeS1NTaGuG261XJqsDfqj\nsQ4fwqcm5ORBODVTTBRDyBRm6WSy/g+CvyQxE/AVfQeh260nkFULaUy3RMGX\nV91OxnBz950T7zJd4nxfVdqHPOlIpqE8UrebhGfJM2hS9QGgGx7F5NQAjF6l\nPhmkr8rGJvauJ2vXKs/R7YiTddAFjGtaectgtEQladvYUTlwoANeAfJMkVX7\nNe2dqL6+bcCIQ3GtAxeQ8vO53U4EQZiANm1LRE9mW4rcedrVQg44w287fkoN\ncPdGkLUALJO9iQsbRFZ58Kl88jUiRnLwnpLWSn2FXJvrH/j+eH7tNOaGjRm1\nZSoy\r\n=+qCR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2c61679ed9d10524ba1bcec46cf5488755c39cf8", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "12.7.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.11.0", "eslint": "^5.16.0", "object-keys": "^1.1.1", "safe-publish-latest": "^1.1.2", "@ljharb/eslint-config": "^13.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.12.0_1564641416976_0.7240442719092743", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "resolve", "version": "1.12.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.12.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "a17d6368ffafb9dd14b584cac7940f9219ac55bb", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.12.1.tgz", "fileCount": 89, "integrity": "sha512-+j3UVImywlkdEOzjNCk379mEqqUEmHgCmUDQkmVVwDkBVopfk/TqrBrr8ZfKZSnCqW/sgyuACFVjBwoB5Mw56A==", "signatures": [{"sig": "MEQCIELQr7MM5GutHJed4TAmSAwSMS3ioyXxNaIBaaGZGfcBAiAkHJ4OAm+Ye2LntD2x6s3JRJneV53xgtf2clauWu2WAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd13ayCRA9TVsSAnZWagAAKwAP/izhT+RRZdOZLh4qBpz8\n4jod/fqvLcn2ge9how/9IzdGPUJ9Ig9Kv2yXhyQaqGTREPOor8+vBoWnSxXA\ncWqQPTvKtL6bQknyAmI5W/zIS/oPYD+X8tyBi/UH1Bn4y5bx6DY+9aXD+ijs\nAbfUMZylLc1Klq51I6qPmUu6O0h/t9jLbccbkeNswBlxz5iKn6gpAFg5ZkM+\nA2G+lunV6fh44a+DM1ShzoY5XcQaPcV1tjtZ49w0YygTFj9NwJuSxvhwB/wD\nHANsMrz0sDup0owSWoq7NBh39gm2qk1cFAa8FDUk1OAiFESPhU3h2GEcmKpi\n+jyaSa9jlr6aLv2SV4BKEkHOj/k+zUlq2zIzWs38M2MjtyDZN6nFjWtO+ys9\na4UoPi/DS83xdOvjwFzoUQ62IuIHpS3UXzHCk3kFz9/yUf0p1qeuHg/IFOtV\nwV2Olmk+Pux+FszMeFamS+/a5hZjxKWZatShrNVrI5tySjqmJkWpVHiBPfxm\nqGc1Gj0e3sWwFlF46wVvGXf9naO7gwdnmXmvXNbphLxPS9sF3zre87shcftS\ngkO3xEutkAy0eQNdH2IIJGHrWyfLtoGTXzzKp8srrTh+JI5unME5rHTVDCMw\nBWQB0KTfOw7KZcp+06QOzmWDC0hbko1Mhry5jA6FFhpV7iDObu7qJtb4aEUS\nNuR0\r\n=UBs8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ecd0672a465e601e732829093f15d814a5dcbcb3", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.11.0", "eslint": "^6.6.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.12.1_1574401714101_0.5348823459884744", "host": "s3://npm-registry-packages"}}, "1.12.2": {"name": "resolve", "version": "1.12.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.12.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "08b12496d9aa8659c75f534a8f05f0d892fff594", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.12.2.tgz", "fileCount": 89, "integrity": "sha512-cAVTI2VLHWYsGOirfeYVVQ7ZDejtQ9fp4YhYckWDEkFfqbVjaT11iM8k6xSAfGFMM+gDpZjMnFssPu8we+mqFw==", "signatures": [{"sig": "MEUCIQDnxbk2m1lpQ1mGlZnJT0e98xEfNSub0piCIwfUeWK5GwIgI6cYofQpyepdkZPuqnnqunB6xOmarT5Ked8gxr3B4+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd15jCCRA9TVsSAnZWagAAbooQAIz1etZ//kMKBqE0KvZv\nAbOh0C9J3XFcuauBShrm3OYAm9l3xhg+5RmLi//aMZTZtNCY2mXJR3FQhfFf\nKW98VXo8pH6v9V3S+ZuApOoZEf2X7//1FUPTrMYTyajUxnecToHqTNaCmlqT\n9ZJOqCpdknOGhFHeWNX/QSAGyCaGT0AEeJx7vu/dw+jKpiHIJ8pFkODxSLs8\nQ/XS4+pXvneY7btaJCMSKLEp3UaxumChhk269VkSGAedsw8rlDNVuahr0cBp\nQvpZJXCy67CYxYsyHU6Ch268blhKXyoXhQMm0mvA/K9K/OOf2GM/lXrGOjOt\ndswvw96jHyMZQxsUvG7N+W0EaaoSOQquqnTqKdSGOB6ttF0k7/4NuGP9H0l+\nq5EwANL8efcC5xN2B22xZ/kA4O9WTgsgm//uUaziuuvdpwx32bAOoTiwjd/n\n/+IsPt30QLxfDGSl87FoIXppibkwo2I7Bx/ygLpK0evIOVkLoZ6hbM19I0Z5\nTPGOHwbar/co/LjKZSyPjdKHIQDWmfY2r7epqd+bj8GMX1nw9HN+XwFLG8uN\nTrlMChBrXpgtTJR+1L3NzClEqJ+U9fsaHl21+7PyekygcQO0kOPZXXOuxqXi\nDRUa8LyMgdqTFFOUoXYeSLvgXCVNXM57jxReICd3HC3JYybRqjMIIix/y9j+\nm2yu\r\n=nRbt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "d930f86989df09a59976ea690a2fabf5223883ca", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.11.0", "eslint": "^6.6.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.12.2_1574410434483_0.3597815684754764", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "resolve", "version": "1.13.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.13.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "e879eb397efb40511056ede7300b6ac28c51290b", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.13.0.tgz", "fileCount": 90, "integrity": "sha512-HHZ3hmOrk5SvybTb18xq4Ek2uLqLO5/goFCYUyvn26nWox4hdlKlfC/+dChIZ6qc4ZeYcN9ekTz0yyHsFgumMw==", "signatures": [{"sig": "MEUCIBUw9pdQBLGA2vGnl9lBTErNWWUoBDvF/mfmUzUnmvDTAiEAwMCF2ojyx5TmZoSlAuHEv/DlPithBWslYHxe9DygUu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3GltCRA9TVsSAnZWagAA9RIQAJDq3eVllQPE9Ka/xga0\nJKs4LKKVOFvW9in4g+TCyPEzbO227oe5nrL9EuQwswIeevNvLs3/w2iNPKVC\ntfWUZU2XGXzs6x7MRGn+d/7OvZ9J2fLBRkdYfzm0th2C2Z0HApdAcbxt3iwS\npR50IQYQfh0/a6yU9eNT/x/XQmx+Dv0CJHzEdvxIBdP7F153NBQzY2UoIuOK\nbBZH89zuxzWTZLrxmm6bhQptAixlBNi1N5+eeVvddeF4GVN5A3I0Hla7Wg1F\noiAMP5jtf10wbQUtHIR/W13F0vNkSqh2j0ijI2/vmRN7moA8V9wcAgZAdh7t\n0Rk26U9CYeWjqHLmLuIpNMzOj9jtjwlCKzgHKWKL4QesJB8LBu+vMwB2HzZT\nW6JXxN0vbqbss86+6b9z7hz2fnAsV+JbdYbLcsMbJXmmoAb6lYFelVRnRkX0\nIcxRlGRAcl/PUdrNqknUFzVSDA5oGmsXAYTeqSmKNTE0GAhncq0vOt1ML/XX\n5aeOAwTZ02nglVo3HdB9Y7DhxfDsAO8bPkDhs1a8zENVGChsWkmh0LyFA824\nj5nmMFRiLsGS3cD4k7PVkshh5jJmMTucUFGsXpMi67F1PpbxaXXs3oEK9gLi\n2/s1XhTO5XN46+hJbDEyogrVna5IRnztQWGJXGnRRAT450KxxALV4UqqQLnk\noZ/i\r\n=+j87\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "a86405a24ddbae33f80d5255e851ca7a886d1e8f", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.11.0", "eslint": "^6.7.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.13.0_1574725997253_0.3159939301813701", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "resolve", "version": "1.13.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.13.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "be0aa4c06acd53083505abb35f4d66932ab35d16", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.13.1.tgz", "fileCount": 90, "integrity": "sha512-CxqObCX8K8YtAhOBRg+lrcdn+LK+WYOS8tSjqSFbjtrI5PnS63QPhZl4+yKfrU9tdsbMu9Anr/amegT87M9Z6w==", "signatures": [{"sig": "MEYCIQCVSKgJFJ1UNvVAW9Gu1YpWAe+ddKN82cn1wHJkawuwDAIhAMxUS9NBVRRAJzvWymWcSoT1t220qEcXZLPC37ctFvLJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3X72CRA9TVsSAnZWagAAB0cP/RbNSN7PLoUcQynlw9Uy\n3mPxgKSucNP0A6uKvhRGN0FcYbYVwAatY6cwHK1c1vnhQKDWRgwmhyNh6qOw\nZ+N4JEMGJt5f/04ORwfYEeP06MgnonmOAbsPCSKshJiHZsLKG4UOH/GaEAoW\n/ccpfSUZ8pQuImAlEUzNLrPlB6olXOdsyDVcVyN1zH3s+oaC/qfxg4FLmo2/\nGxkmfd7cjOHZykTC1cDDtqXHOWtKyce5Ue2vowbCdZshlBDRf1XgqFr/Xak5\nnyj+cNkedtWLuA8Y62e7gB4deBv5rQz+CA4FgQ1xOXMicDSmOdAn5+M+Vonm\n89T80l2/W601PIv0q8kVOXOrKop5TJBw3gaLpXvUbPiLqSbcQdLvqYIWg+JU\nJTMU2/6n3XpHOlXmrRIjQyGOgntettzM65BL6bqxYAm2XFrWQvUexc/iWj6F\n4xAWMUbevd+/SphqYx0VpGSwaIIjItTCf57YlKf6CNDMY/5+EkyjDy+HIoMd\nuZmsmv3A900n63DKqyo1uRC8CVgp+jqCRr2k9/l0JDwPHTSBRm8DJ0xnmLgd\nbesan+0fdOTrkvHrzhGQIeJOqBkliPHCD6jeKwCwhlYJhtFwJwQJZIFdSwHq\ncLdZ8aGsjI59IdfiZnRxzfHJpQJdLxHBV3vMw6FwvY106aVUB4JGs1HDWUsL\nZzpd\r\n=smsL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9591fb70e34b26b2c8f500d67c91cdea2ef68fc4", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.11.0", "eslint": "^6.7.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.13.1_1574797045803_0.4582700288615975", "host": "s3://npm-registry-packages"}}, "1.12.3": {"name": "resolve", "version": "1.12.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.12.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "96d5253df8005ce19795c14338f2a013c38a8c15", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.12.3.tgz", "fileCount": 89, "integrity": "sha512-hF6+hAPlxjqHWrw4p1rF3Wztbgxd4AjA5VlUzY5zcTb4J8D3JK4/1RjU48pHz2PJWzGVsLB1VWZkvJzhK2CCOA==", "signatures": [{"sig": "MEQCIGP8QnCCFCwDZ+p6jWwN10kPTusWomaod4SGGKJkTMV1AiAXGXZglAJutDJHyT9zSlf6mgfKHW9h75DKLztZLU3MOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3a1wCRA9TVsSAnZWagAA1y8P/ir4C14cmpf9qZ82Qe8G\nnoUeU3VVfjvfUsNyWCnC9GYrl/a+Nkn8KFgj5K4m8JzRQeqo2FjmeaVtM6BW\nmsQhI21Y8TLqFs92AliUQaDR4H1K7CN0h12FfcaNJWtBqmeQfH5oZA2eDsin\nsQLCV/VAzcRLLJ77qqaGbqEEc8M/e/6IPqpcahMr/+9k8wUXwNheyEsuoH2S\nXmZHIA/t+QKKlZb0d8nWV3zilZ31PUNtG6d7aAv+ByhFUWIwg/cfEctBz4K9\nDCzZpdxUFbFzEKYTydLZ0lG1QxAYPxlwF1vZhsYzLI5PNY+DdaV1dbZnuY2J\noPzC3Ek8HVHkEZLy2p5nyxJrODiG7Dem/1kw3dCsUVWv/O3QtNFR5qI2lp2D\nmbbPMzLKnVaYvjqiAO6cc3JJaIWmYzxsaEdHw7xbVntfBD1tAhEdo3IHw+7e\nc7R4iLAiNDmuXjJ/Z6+7NGQOw57kc//TWZ7oovzluDS8P3mtG1Xc92gNP/iC\nJN8guUDdJyUikjSRje45HZ7Z8AIVvY5bGs6FQNiveg2wN7yQqPvrmzazMREQ\nZgL8ICguwHa705yixBpAzNyx5Qu4P6I9FAl5nNXUMeLZq6mEdxi3ZhLoghQO\nOZow4oRLxwJqvAPWddwbD09BpVw+YdJQAznSqgXn/r7dBNYxe82pfH9AE7+g\nugpc\r\n=9szi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7967adc031d7298e5707ef6569dacc9c7a9955ba", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"tap": "0.4.13", "tape": "^4.11.0", "eslint": "^6.7.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.1", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.12.3_1574808943746_0.8624531554690444", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "resolve", "version": "1.14.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.14.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "6d14c6f9db9f8002071332b600039abf82053f64", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.14.0.tgz", "fileCount": 90, "integrity": "sha512-uviWSi5N67j3t3UKFxej1loCH0VZn5XuqdNxoLShPcYPw6cUZn74K1VRj+9myynRX03bxIBEkwlkob/ujLsJVw==", "signatures": [{"sig": "MEUCIHmcDLsmxczqj4WiYVYrxjak/Gcdd1qUqNpgP7d3cAI4AiEA0aNRPI6IoyNbLbvZqwN0W0C4w9VpP4dewuEeUnuNBWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+XJqCRA9TVsSAnZWagAAjqQP/2fGHn90rvZLEzT5Z8dE\nbpOTaNo+rIohgtvYZzVFhjAVTJri1Nyj5ODQGmwRxlbXycZpk/kkG/maCPJh\nBNxVUxh/ZL0oXWwe3wZDTY2niuAOcc9bpwGejyurczoPvA9s0Jwp9Cq3cYkK\nUma+1MqrEDZ9xmtER2g/Lt4OyzUWYJ8OQKmGrrp6cxc/DoZ7hwMr85zjIOef\n7/x2dZ7DgzkAasRGWUsYq+LNPPP4AV6pbOn9sxk69O+lQKzai7ZEPViKn2kF\nQ18ptJ+V1Xp61VOsAQvzNOP/oU7o+KbCaK5IkaOYP7wA6PCehk4SWzlSU2o9\n2ZeViA/hmiMcdSphXPy0HNpgzVsMUfMYK7MeTZwdRpbgw9OlZU2obu5Wknln\nLOZeQShemSNHL2XO9kxgu16lo21iB7y5X7utk3px14UYLhPIs8fDii0vlTxg\n73nq2lMuQzyFiEmXLzUdKj87Rrm5Cix/YgMkzMrJsbn0zf0RdJgs/CG5s7kJ\n/l6qlur9MLhVhUpqBS5/Ytn+gy2SJfAtJY0hKlzUb0kb+U5hc/Vpcj2BiLjB\n4tgAV7fd9yoLRQu2m2u1BUMACYiIZmunqkfoV5bY3J+wgwHn2VyawBcGotq1\nfMWsZ7v9qID0hvf8Q5HjKkuTYI6BReA4QlJYGiwV9/NZ/IXEpQzDRLWTmrBF\nK6YT\r\n=aabR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c5f819b56cb2f785c9466d339f33550936d283ba", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.12.0", "eslint": "^6.7.2", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.14.0_1576628841623_0.5116367303327636", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "resolve", "version": "1.14.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.14.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "9e018c540fcf0c427d678b9931cbf45e984bcaff", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.14.1.tgz", "fileCount": 90, "integrity": "sha512-fn5Wobh4cxbLzuHaE+nphztHy43/b++4M6SsGFC2gB8uYwf0C8LcarfCz1un7UTW8OFQg9iNjZ4xpcFVGebDPg==", "signatures": [{"sig": "MEYCIQDBsrg4T8f1iGjauSNK7EBZ/I+9xbSl9rzuUFh/NhFeIgIhANg6QklDiPYjAaczSmjlECBQVrTKmfzXqCzZYLwdy1c5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+vgMCRA9TVsSAnZWagAATaAQAJYQbLmaOPyV86QFkBZl\nfSy5oMHCRE0nAm3jIW6Bqs5M/pnCBhsm5SfoFss6HbcNFLNcEqFjj7XzypnY\n/C8WLswToIW7XMRirlFEYuo+9AmlzMbJ9AbLJ6pQ9idU9PjiENGjc5Qd4VxG\nCd7SlIAsSCXOa8ElpiaK8xtiUMtOa/8YfXLE9tg5skqKQjg+omJDj/mQZ2Um\nieBCekLCc14U2AA4yMzFfP2IpqZeIScatv/2yVWEuhyYQQJjdVXk3lQk/m3t\nrV048WEUeJzYNrW6lpf7km1UAcl4Ga0mWNliNiXNbrEtMP5NZpv+fK63k3tC\nHp815u4YJOr7blY9A/9YonV/yGY7qBvlRawIUM+Es0nEW1C3OQtwyx8SSL53\n8cj+Nr4QXYNQFM/ahxGRkjEB8v4+bQM2LeSO4HrbzLZbdcLQaErm9k2WitGk\nXNhfm3eobF4JuS3xmO+Anqo23ETzcpK3FXOuyLou7MXxenQBM+L/llj/zohH\n2/5BkRxmHAxPUckfAL2dBMwivQIxMvK2YnGwQFVJiw0GnucK3Ne2JNwdwQIS\nYSs48KXJAwFJ42n20wzIgoky/2/kGu/u3CRPBkQcIcYQHmAEXhTtRA4ft+kN\nFH8b3IiBYwN7qkDoHs5+6TqOE4r6CZ3Z1M415lZbCVCmstrTTjShsg6bir/v\n3udF\r\n=bYf5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7eb98ea4058c9ea3c6f81234fcf47ed2406a4e79", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.12.0", "eslint": "^6.7.2", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.14.1_1576728587358_0.6143897898508723", "host": "s3://npm-registry-packages"}}, "2.0.0-next.0": {"name": "resolve", "version": "2.0.0-next.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@2.0.0-next.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "c9b5b404ef2a9fc9c5164e9478407d6f71c4a2a6", "tarball": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.0.tgz", "fileCount": 91, "integrity": "sha512-eCuBGbyVAXWyf/OzCAtQ9rWu9Fsff+r/FsQgERHApf2e1Bmg/ge9U16S17PmI03N4jHOfNN2huVUeGW91BwFRw==", "signatures": [{"sig": "MEUCIDRFahDS2dZpK0Pnchie1qU1HsYxte/jOM1K/M8kTxgCAiEAo7MqVzc2vDFW5hSC/SbCm1eJv7ZsKi9JSGvblsJ3mAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeATjHCRA9TVsSAnZWagAApz8P/1r1kYWUaME0ldWYCeZw\nk8JGXz33HowSZDz3V6592e4gpKDQ02v/YxgQKVJejuomDoocO9mz8lv00Z7j\nHfcgnWyqHfYFsgx7s90p5iFeY4FZRw/I26G/js9ZDJuomda7c+Ynj9GElVqg\n27AQlFAaGv+WmS5tJM5M+WRXmyeGu0DQnGQ8xAEWf9vtRROdMKj0FKiY+MDE\nZz9u3jtJ3Sp1wjqzoucUhgoF2mOwjSu35gmJxDiPF8MdIemib/AHsNndd5h6\nlZ8zkkTkYPmt15c98rpwgCI7qyWEes9x0QUHCoJbWlQgodyzW+sK6yKGz4Rs\nf06ukN33DC7dd1+RnIfjPMqz0olti9j83ylNPiTqKqR8qwIs3I00c7IPCSI+\nUAopQPgCIBdIhBNz8mUby8a+IT6iOr6zSyPhVHgEeR0H24/E0J9vnsf/EuZY\neYtbijVtNz5MmcgDAcynAvDWxTf2q8M6VmO90GjYVDf5YDU5IqEAnq9wK4+Y\nVMzeTC2DA4Iy+CFLRZCvw6p7nwHpQKaElJUb2U0C3gLROkuEQBqg+6nbkWCi\nz5mdkkx+qAzr3fyLHIDcGOxa4vGjIFN0BxkPjZiBu/LQOmhoPnkCsd85JuZt\nOFVrfueQwdGcsk4jBvzdIvGEMLeB1Hi6ub/Pf8lBCO1IS4hKSiFlEuO6T7Wk\nQUpr\r\n=lsm1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "6f57476ac1b1a3a23bb359a725961b5e040c55d9", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"tap": "0.4.13", "tape": "^4.12.0", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_2.0.0-next.0_1577138375476_0.02973384236750687", "host": "s3://npm-registry-packages"}}, "1.14.2": {"name": "resolve", "version": "1.14.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.14.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "dbf31d0fa98b1f29aa5169783b9c290cb865fea2", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.14.2.tgz", "fileCount": 90, "integrity": "sha512-EjlOBLBO1kxsUxsKjLt7TAECyKW6fOh1VRkykQkKGzcBbjjPIxBqGh0jf7GJ3k/f5mxMqW3htMD3WdTUVtW8HQ==", "signatures": [{"sig": "MEYCIQDIV/Bma8wUKs1CkxyD55eKVVmas9f8nKXZtkXqmGO65AIhAOGfIs1ByAvGBf/MNtfiobxPRKzbu1aAwyNsVjsgSDaZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeE+VSCRA9TVsSAnZWagAAqW8QAKB4nGqr7jiv8ZzTLhDp\ngF5ruRin+rIT6Nl30IDUB1klfXs/INzoUnsKVE2pDgCZLqd1xDagOsJRXKGd\ndH+H/0vveJ1cZcu1eDsbTKnDtmbF+QgzNU+K7DqRkT10Nwl0Vgly1ew+Cn12\nfjkLWM6RPza6MMRe3jF7YE4KzuuvJCmrOSnoHtGl+/P3x74qDxDvj9RsdvrR\n3VuZXJ96N1qM5wxXSKBE2Ot0aY4yBYhDpKQHTYthOoedS3cdmw4TkwxJdfm1\nhXyY8rjWkrt6aHrZav5mP8c14GtwTwWfQWBNm2dN0SMMtuddwmqSNGGGuDSs\ncWaDTfCssCpNjH1s9tXwwbgdIqxRDpNl6zgUhYxdpz7MeIV62/cKhkMC3zga\nMENPDtivE16Dw0h9gEVBqZ5mvd3RguO0/LA7OPrIiG0iiwkG/1NjftDuizUq\nxpTYp0HZjuSM9wPPIZea9NOtGzXXq35KcvH6FDvPL7XHdidTJPr5fOLlzVa1\nRzrdQTiLUiSoD4spHAoHPc9OAZf7ObancUoWu0mqpr8wiJ6ad3VO+0X0d3W3\nrLgQ5D4oQSV15Xn9CSvExH5pVLaJDacjoL6M5pWF8VOqlIyr3c36O9EUOkc7\nXSq8aB4BSwMs+itHtXf7TL8A3NX4OKVWaoWGUbq6KnL4zK8W1bgtdiW7+M2O\nLtmb\r\n=5Yfo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9fea81aa462683ddb6f9de53ac4fd258399754ea", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.5.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^4.12.0", "eslint": "^6.7.2", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.14.2_1578362193576_0.46297596925560036", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "resolve", "version": "1.15.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.15.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "1b7ca96073ebb52e741ffd799f6b39ea462c67f5", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.15.0.tgz", "fileCount": 90, "integrity": "sha512-+hTmAldEGE80U2wJJDC1lebb5jWqvTYAfm3YZ1ckk1gBr0MnCqUKlwK1e+anaFljIl+F5tR5IoZcm4ZDA1zMQw==", "signatures": [{"sig": "MEQCIDVgNu7oZcJrJE+JY3cki2NSaxrukog4PNI38QPhItyYAiAJ8iXimsVjg2052WDuQaI/RZysxZzGBNKximSf0pA6Fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKAZ0CRA9TVsSAnZWagAAZvsP/R4W8Eb8xizqv5XxCU02\nvozITQaASwLhuBlsazW4jhvQoXQZImLcH/V/A/om7O83vdm6LKLWOtMNHCDS\n6t++91Jky00pj4rrr90tBHlgpXJQQF60Sa/UCZUlKgG4XGbEQEeycF0l4/yD\nPYR9slSKp5yk3sv+T4bkGUWugbejdWORwmQdLU80AQtYdWzDomzxq7uJl12N\n9ARlJMbXRgRVyFoT7PBnl4Zy0pxZcHG8PeZds1WyKRES/KdOF2fR4Bu5h86k\n0tIxbwQN/xdl17k2NQQsgNzv1X27yQ9HAkgcOR3cnjjlCGZw0EnV5doVuSoY\nsdEArTB/D+f8aiRXiZSJlq/Y91KL+ADrQH5XtKEhmZnyIwmvnHaNZiXTKa7h\nJhh7Mujh3vzwLU9MjJapaNciL6dqXu1X056655bxHGz9VuJ5Lh3VMc0fIAmm\nfoJFVw4HsiK1SrhZ7dkZjTUYhVD3Fb94CsrwkEclCkXHmbxtlghLqpKfpLoK\nUJsw8SYOY/GZTboF5TZHOsZS1ezFXjuZ/99aZICGtVRbw1ZlxX/BYL6CA1PM\nOmDyEpHnWyZjDD1ACHpENl9utkTVhtkBlFxOqJcvS2j5GQ5650uxNQQl/r+W\na4kzRD+XUl39UFafc0+42YhV265WxfCjtNLBzYdM0bz5cDYlAvrp6UZSsNIE\n3did\r\n=Mbpj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9529db447c98ccb763fd360e55fd411710dc3232", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^5.0.0-next.4", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.15.0_1579681395452_0.8270026486660373", "host": "s3://npm-registry-packages"}}, "2.0.0-next.1": {"name": "resolve", "version": "2.0.0-next.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@2.0.0-next.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "4d96ccb89bf82d54ab037241ae053db4e92bb5f1", "tarball": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.1.tgz", "fileCount": 92, "integrity": "sha512-ZGTmuLZAW++TDjgslfUMRZcv7kXHv8z0zwxvuRWOPjnqc56HVsn1lVaqsWOZeQ8MwiilPVJLrcPVKG909QsAfA==", "signatures": [{"sig": "MEUCIHSd+80FqdrRfNeGnNQCnvlhUxUb0OxaCV2pAojp/uy4AiEAgftaB1NkmY/mP6hdeinCXx0Jx/Ohf9VStE2r312MElM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKHXYCRA9TVsSAnZWagAAZcMQAJr9xrgORCz9QCdNR4Hn\nn9Y9oFhePh3Jg1rQBRp8WfCJC074HyGqos/i7OAEsGTEZ+YvnP+heHdewnPU\nxy7ZJiZnvw1SAS3EfSa3ALVxp/UL3MRYtsUXtkTEV3Cd7pOM2ZYU4rQaC9nB\ntSVnuqHlB0/wK7CUKMYF7sofydVq7ymeDRsw2CKSlxfHtWbYgl4AAPfXEFGx\nApidS79G5gU1/Ntat4aYIZNOwWSVCNekDJpRpNmFdB3gRc81fZRPNoyrJVyJ\nkTPbMzZ4TuxS0sI3P5KeumrijjcJdGdeNP1OEPDm34TTQ9X8JCy2hXMK4H6e\n0kviJUVRODWHcW2EugKFGqmXMbwCAbEyCo1IO6O6PTEoMRwzJwLcmT26ibjC\nScJ2WIbf7xg5PZSjzx6j81ulYdw+hReh0fG4cmnzqkbg3htrIDPQs1SOIQB9\nxx3SjShv8WcOC9nutVjviNRGZZt1g5/LyQVFWWmQlAJqHVwkjt767tnBYy4/\nNezxFXtWeCXEf0vnxsYupcc0kGd1R9DA/4qIpmmhgwaDTSM9XWEt/2lYhwjO\n68i1Be32k6fNr/5dbcYs/WBKe3aZfBaNFNpfGX1lLzJZSjXEbr489Lyw11gF\nOAdjoamMoj5M76MA4BUQD1eVE8IjtD//amRzrMGvHS2Fs/qas8zkN7zJFDTD\nxO4m\r\n=oefJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"import": "./index.mjs", "default": "./index.js", "require": "./index.js"}, "./index.js"], "./core": [{"default": "./lib/core.js"}, "./lib/core.js"], "./sync": [{"default": "./lib/sync.js"}, "./lib/sync.js"], "./async": [{"default": "./lib/async.js"}, "./lib/async.js"], "./isCore": [{"default": "./lib/is-core.js"}, "./lib/is-core.js"], "./core.json": [{"default": "./lib/core.json"}, "./lib/core.json"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c32cf25cdda5ec3fc64e21696f908f59dd44775b", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"tap": "0.4.13", "tape": "^5.0.0-next.4", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^15.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_2.0.0-next.1_1579709911586_0.2616463854998077", "host": "s3://npm-registry-packages"}}, "1.15.1": {"name": "resolve", "version": "1.15.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.15.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "27bdcdeffeaf2d6244b95bb0f9f4b4653451f3e8", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.15.1.tgz", "fileCount": 90, "integrity": "sha512-84oo6ZTtoTUpjgNEr5SJyzQhzL72gaRodsSfyxC/AXRvwu0Yse9H8eF9IpGo7b8YetZhlI6v7ZQ6bKBFV/6S7w==", "signatures": [{"sig": "MEUCIFShuv/KVbfoKlRDRoikPSVYkOg38bt/jb/jv6KHyMUOAiEA5PcL7uY2tXmojcswUXJFi7JTWL7ynMdUm2bn/tjhAiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOzQSCRA9TVsSAnZWagAAHE8P+gLb1W5mtrC6rr3VxjjY\nFMioXWme6aLyr+T3ICEAkBz/c3NK49tmqmr+AhTz5d7psbul9YAAJnnrJOey\nW8Q+e+SnwirNaIuPPupJWoUScyWyS4r0iqPT9Kmdyc7XsBBhr0npUPL8YU76\nref36X2XTNblInVDZB6dU/mUH2euVN2DiJlAZf0dbw41pteO0fAEn40EQjdW\nfrgE1aYOM7c9xWdrWGiGVVZRgC0t+IoA/rPQMzaezfeMQjzrN4td48Yhc6oD\nalFHySyCoAtxSPb/Q2azpTqSJm9akjOgiOAFYp50X5H7xSRMPHg7m2KV9ISB\n98hf2Vo1x3BS49IHlYoQgGiHxq9D6IeGdAL3Xwa2S3H3oAmc5Nrj6ThFseFV\nGvUWKF5NN8fN1j1xTC07Q/jWXrK1dS17ALD85DbgTWtmMFoSKA8gGPXUjOZU\nmjRilO/qMAsGgO319pTqG8QXUwFes8MM7RDzCKnQw2O061r+5HjOyGtBUDTO\nlgI0RoCRgEhTUqaWqG56PJ1EsRguDY5TJS9JDrzPGWgvrotZ7HWA+SG6+nji\nyHNMoNsMVQ8/OV4kXBAFntDqgRkowt76IWc7WHFBZJkzjX+CDMJjmTmjOH5e\nRxU4p89B5vY+Khlf+nmYbNqwUFgSbxrZ4HicD8H61HbayvlckVC9hc4bk/s4\ncu+C\r\n=GZXK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c1750d6f17dbc607c96e73000812b2becf7ea6e5", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^5.0.0-next.4", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.15.1_1580938257367_0.15287318519972892", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "resolve", "version": "1.16.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.16.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "063dc704fa3413e13ac1d0d1756a7cbfe95dd1a7", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.16.0.tgz", "fileCount": 90, "integrity": "sha512-LarL/PIKJvc09k1jaeT4kQb/8/7P+qV4qSnN2K80AES+OHdfZELAKVOBjxsvtToT/uLOfFbvYvKfZmV8cee7nA==", "signatures": [{"sig": "MEUCICD7ODSfXNiCKekARDM0YkO2KTZFoPvUwSRbT0B1BDRWAiEAkAbIU3mNo16H9rH76SlCkxpuj1VV2JPzg217O2+UM8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJel5yOCRA9TVsSAnZWagAA4igQAISgTm+QClRAlllA2fLE\nrJaP6LbUpGGm4KKobM2KF1BBWgB3sz1M8ynqMPx/IDNb4vnq4dYmvToUsrFN\nRlZFuGacGX4HPOJ+VdcRWFPFUc5L6k3mIwmSuNJFI0WnMCW+NQ28kStno1Qv\nEyASRA6FReZ9n9EQpCJSENTA2KYmEhTJPt9BigGX7BLCOCs9Jcw1YHMyKeiL\nacjt8JId+hRqZcq9g2oOlk8LXIYDhWlcCsp6RHHgPUJrU+x6EKOgQwkMEq0R\nBLlzbNO3R2K6CwRxC3JHrLzilpN9oIaVZy5WnnXjvR3Ui9AYN74C6HPjw5cY\nfa2eU7d0DSF5j2tcfhwYMVvsprJLqq1ViuOji476Pd2FImZOZqTb5I4a5hXJ\nNPrOkaVT58ZoZ+KvKoI0zc4JDd8W/o+bY9UTkjaZDBCW9wHiAYz0+T3yiA+P\ngoOmYUQzdt1ZTAbpkYt7Uvxh1x6t6uyVEX6aRMRc/CTG8bqhfxjA7taqVe+F\nDRcT2SLax/QkzCyPEjoBzmhL/CFG3/6ecBNcmRWZOFAmbYd6LTCU2KZy6uBf\nt4gtWbE6nCSBC8tCNaD5bBtwUYuojDXzuq5oenCv3QXzzU6BbA1rmt3NVqLW\nnhptPvT3z7ARMZWRKBf51+f4LhpPcn5UGh4fSJjow11UNopeGXhMUwxpI//A\nItSs\r\n=vDXQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "301c07d90c826bca8878ded121cec2cd20d6983c", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.13.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^5.0.0-next.4", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.16.0_1586994318046_0.22378266879338504", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "resolve", "version": "1.16.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.16.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "49fac5d8bacf1fd53f200fa51247ae736175832c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.16.1.tgz", "fileCount": 91, "integrity": "sha512-rmAglCSqWWMrrBv/XM6sW0NuRFiKViw/W4d9EbC4pt+49H8JwHy+mcGmALTEg504AUDcLTvb1T2q3E9AnmY+ig==", "signatures": [{"sig": "MEQCID3w6eAbPr7UO6KLApSUDF6+bAZ2VXjUmQvd0cZnhgdJAiATMY8qITdHyWaVCUl2vaNxLk5jXCIj+XwSENEsmbtjMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemhyHCRA9TVsSAnZWagAAmQsQAIKWsRAHmEURDYZcfOg+\nIHJ3oxmQ57+nkRrWEVsZ84JDnGQ5Z8fT4fqQ+bcr8Rzahpb2D67AROmFF4dK\nMmw5AZpZPslrs9qqxghUw3gk+xFEH/jrve9A1WaksvhO1ei1fqtpt0KWlXAx\n47GoShpoRjvARkSiU0lTBFqwjaN6dRNGR6I0tpgT2ko8I9uIES4umacR3Zqn\n8KzBAecG65UY+yEoDgjmKSWASJa8dmKWMeYWrW3NklJ/Uks1Qk3d1g0WBjA3\ncwcqo1IzHunP1L0ft3PRjqAkx0ragzvlZ+2uo2JOMcEgMN57ORkxiKNnnf2m\nYzny5CFI9xuMmIlr1hOM/M8dDQ6rxb37f6Qph9hinO5SEs1tazUYySOB26nu\nuKD8HHnLbpNrhm7Qlekb7XG0yn3H2/W3Vgdi2j/ZxZKXX/+4MxKAbNXSxple\n0NFbPIc1rvy5LZhAKUgXlbrKkofdwoFRRn4qpveUCabAmhASKgiifkYPHec/\nZeCZa6/lr+SXOABaDVsdNbedHnxfPf2yQcFQzrYtoZ5D05kB0/0KAeD4/N4v\nzqMt9xs+tgLfD5PKkAtP8F21Gcb+367SQRvNy0V5PXhTr64YD+fgcCOA1D0b\nygNNty0E9eWSeullLdczs8Nq+dEcW023Q/h7rxX/BYoXXLem5OgWHHpdiSyC\nMtUH\r\n=FOqY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ac3eb2f47deb7b28ec2d5f6faecd97360d7c0954", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "13.13.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^5.0.0-next.4", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.16.1_1587158150432_0.12734693311264156", "host": "s3://npm-registry-packages"}}, "1.17.0": {"name": "resolve", "version": "1.17.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.17.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "b25941b54968231cc2d1bb76a79cb7f2c0bf8444", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.17.0.tgz", "fileCount": 91, "integrity": "sha512-ic+7JYiV8Vi2yzQGFWOkiZD5Z9z7O2Zhm9XMaTxdJExKasieFCr+yXZ/WmXsckHiKl12ar0y6XiXDx3m4RHn1w==", "signatures": [{"sig": "MEUCIQCg9DUryKEEcT4Oy5RhI/GHx1vh3sVvvTt487GbC5vJngIgMU4SCvSWcVk61S1ylKWjGfun+H2CIjs1hw1dJySk8sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoMvfCRA9TVsSAnZWagAAVTcP/3L81m69shZ81WRX3Us6\nJLa0WuKYNCcvCg6Hz8KU1/T/WZtN/hGjaJbzbgYE4LIKnmcOxDo9Mi5oOB91\n5R+UJNlT/gkxABplflMlwSX+p1JpEtp5FyB+iIutz4zOxqumMpF5bivHgiju\nuOQcPg27yJznSjzmo1j0/5Z/RBVrZ0YoU8d9eT8Lc8Y4MGPzhWAW29uvOhwK\nwjOW2qe//g/cJ4VspZ53Jl2nf0pu9Bo5JmufogK6Pg6BRz5q6tmq9Sl+L5yh\n4HpRzsScfnwS1eTyA2rTvpxgx48SOUmOvoE5QFd5WbGUcUo7AYicWblAKYJ1\nnk73bSGlNuAgsidNUd7CIX8Ge2KMRZpM7+0iv/XHXstakDQg4xY58ekVPMqC\nBZODDFVeNniCSW7hmvWweFyVebznmQRCTeqq8m4O8mSlYjWlLaWY8UHD1AfZ\npz/xkR0XIokZzD/AtXxYgOTtImfs9u3BX3Oeo+SUH4/gzbTgncSlTZQraIuZ\nSv5bvE2+MK+uROlptrP4XxZ73MsMF7rfqoI1EfLOMIgvBNB9vtBUoDl9sbyH\nTiiy5KJjO6/WFlNyhsXTXKWpm+oiAejXIMIHN1Pg6xLy97M+RNU7Gukg6Nxf\nHljtW9sV80sFzYksPPljzfj5iau2TO7lYqlTNmI9DG2Y/hAl/+5OuaenyQah\nKzdI\r\n=oJtT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "3a76ef8d2cc232fc4d4246e0748506258a104484", "scripts": {"lint": "eslint .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"path-parse": "^1.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^5.0.0-next.5", "eslint": "^6.8.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.17.0_1587596254649_0.8630091359000285", "host": "s3://npm-registry-packages"}}, "1.18.0": {"name": "resolve", "version": "1.18.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.18.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "3bc228555e1fbdcac8aa6d37fe002b0199f83c0b", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.18.0.tgz", "fileCount": 1243, "integrity": "sha512-qhdohlSALEa6vW2S28IbVilioXLddMz6eA85B7WMfG9lqI+ubAX5hQUbbnk5oqcxRlZXcr+HqCXOE3/R4yg0cQ==", "signatures": [{"sig": "MEUCIDAe0F2PkNylNE9YEab7G8hwp84dEuBDXtA+k58AWO0uAiEA9xMlLZTa1DL07TdojyT8pnZ3nMTXnUuxhfzXO6xGmi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 703141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjd9TCRA9TVsSAnZWagAAhvAP/A00Ci2wkUNF1RNuzoV1\nfoLGhzA0kxXFNy821U3LFcVcYCv4c7E8r/PNr4ohH4G5iN9TXL+HoR+7dBn+\ndQq2n0nWapDCKIzaaFRIGxs6d97TrQFkTNiSI1sHOgXqua7TI2d2itNwTPZ5\n7Kfduc164uvXQfjLF06+9PKOm2s98hvDFpUmEZ/D4Uc54lyI76+g0nYGIYeX\nvFgFuJYhtB4yiEcq3H5nCZtYPxXmhnUNlw/Ieaqn4/0je/2YBoU47aPtpt0X\nprhVeF8nLKbMYI2tZNLufyO2Z+r4iISZCNDf6ZEdgbfaPD5vsanIW1/wI2UV\n7EtJD0XARBLa9ttxQ3oUGv3GRwBTxREd4ZtZUtjgKU07zBdkX7JWg2Ez25LH\nTvxBh/H8L/VIG6hrkr3UkliFbX+psh0ngIW5bNIceWexBRx/LxFSoOOvnrQN\nJs5BDzZIR8MpMjDD68HgQrbYeg1K3v9Ne8Nai/9c4B7vU4XdLZKNuM4eNqmm\n0T5HxS+ueY6IeLO1xUHDK25nh6fc9B4oXxwxTP7rHCqBYPUz8zu3tubZ7UVJ\nL9WgwRn655oqhfaJcg9lwpQFvnLdsvPY5+uzBORv+lBMF1N5/PiQ6ekRYzOY\n1G66ZepshRtJvoY0i6N6DXSBZpicPB2aCQnQTvnbEFf59qpXmVI3ppWInNSq\nYy4G\r\n=D+gP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1cb0b9c2362c0af7c59d94abfaddd995b954a82b", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "pretest": "npm run lint", "posttest": "npm run test:multirepo", "prepublish": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "14.13.0", "dependencies": {"path-parse": "^1.0.6", "is-core-module": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "0.4.13", "tape": "^5.0.1", "eslint": "^7.10.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.18.0_1603133267337_0.3603715032511825", "host": "s3://npm-registry-packages"}}, "1.18.1": {"name": "resolve", "version": "1.18.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.18.1", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "018fcb2c5b207d2a6424aee361c5a266da8f4130", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.18.1.tgz", "fileCount": 91, "integrity": "sha512-lDfCPaMKfOJXjy0dPayzPdF1phampNWr3qFCjAu+rw/qbQmr5jWH5xN2hwh9QKfw9E5v4hwV7A+jrCmL8yjjqA==", "signatures": [{"sig": "MEYCIQDvXh3h/bTjSW/3M3tPDcgfMPXE7PcWPdZ8oMwCkMXoWgIhAKhI5RojvTuGnihK+nl+UVflASB11nsO1unuoVkl0xDS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105670, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjfqlCRA9TVsSAnZWagAAkcQP/iUX1LPswK4ZGLg+F0JQ\n2XPmqTtR8XX1Imy+bYjt2gZ/U6n8fZh4LKDPbSvrnrLECW7fjW24ybXcZyDZ\nGk6eczU+D7/mDRA+W0NXcFS1Xr1QUpvfUiBLB20clTq3zMhrY9nHSpNgSqhG\nJJ3TqNggvAGHlhBfezX38e1b+JtaIEUE7erRRmexKuTzLG/PVhhNHw3j543S\ni1S4SPYs7s17VTpmHN4iOdZZaYghtOkfo9hcFbKCxp50NerPJWhFIntCaQYH\n79/huhOo2+rm4RRrEW7FxjPzuCzfGK44xF1FEjkzrZYjK9XNM1AuZVmJ3x1Z\nYU2xSYcgphm31htq11uqn83oYnJ/eUreHXvoLhlJ9qJe99WDlVK/y4YQ5q0Y\nQskKlt6v4DcG9QFL4SerXKQaqBsauoK0U2+lVRe30f7GREsNQPfIUS8FQDJc\n2Czdi+J/Wem98jg1BAr1QW10DlqHx94taNinsuIgK4Jai3xoYGfvOxzfyMTH\nv9wZqzPeNoqbHzhG5Kz23DmV/n4vACBw9yDOsjkWlYYBPh+j/arK2j8No6sd\nMBGlYHKt/OLHTWZrPUBNNeeQSWAKY14XjQidOcXZeA/Ft4I6t/OpPXMFjq6B\nYtqcW6fVLb3pke9YVi/kYT2o+8mAsBRnTKBbWzR48UKgvSF0CMc3lnLulNLQ\n45Dh\r\n=z9cu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "014d98ddf6c11d95e4a6c9f65216c0daedd52f05", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "prelint": "eclint check '**/*'", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "14.13.0", "dependencies": {"path-parse": "^1.0.6", "is-core-module": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.2", "tap": "0.4.13", "tape": "^5.0.1", "eclint": "^2.8.1", "eslint": "^7.11.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.18.1_1603140261389_0.03297515984033961", "host": "s3://npm-registry-packages"}}, "2.0.0-next.2": {"name": "resolve", "version": "2.0.0-next.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@2.0.0-next.2", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "47b7d854315ac518d07ebbd1079ff77ca1fae64e", "tarball": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.2.tgz", "fileCount": 89, "integrity": "sha512-oHC2H45OCkhIeS45uW5zCsSinW+hgWwRtfobOhmkXiO4Q6e6fpZpBuBkZxAqTfoC1O6VIclqK6RjyeGVaxEYtA==", "signatures": [{"sig": "MEQCIAHN/uEeIPudZXZ+svzcbke0EfnZ2PqiY6TGDs1JrxOuAiAXJ7ciF+eiVBnRJTiASeNoH4T4pdV+Ool/brymB3O5fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkKaLCRA9TVsSAnZWagAA3X4P/idYQitCzbmJ74N/YH5w\n5pqbzSDO5r37rmRt9UsttpOsKNgWtakY/RSGjq+4dCGHckfW6W9ISax0Ulr0\n6Q6mPrhiGJ5a7h2NpXOB05979wTKv+E+FuhgfZ4p8vVmmHQbcB8Vw2nCqVBA\nsmmzgGHFZDikZ9FoMAJFXhyxc9CvqlrrmyWwjSkZmHohJ/VbE1oUBf/jdvfo\nKRTlR2y9tbNRo3R/QfbzQbM2O3KFIK7uv2i2NaY6tBvyxsu+BDSf0AggKPh0\nGEOgw0vISV+m3O<PERSON>Osoi+u8OQ6GnOpizBtovgX/9KFfT+yoTOSwDg5Ar34Op\nv8uAcpdHrcz+7J5KIoDHS4iO2U981sHqfBpQM467m/zznbYbcrQpkXYL5o/w\nXBJGnAbrmYWHIjbLdWDXAj/S8llW7BkcTJ26TFXoqur7MijFDxHjNXZDyJet\na82KU+/r7VYTjtq9T68ZFlbjswkvv097AewMM/PFQWSzPwmz9l2e124lo76h\nAkB3U7Smz0Ezh3DW7r8kABCvmpiqUhmbngekOEKYMqYgjbnJFyIguanYjKBY\nmej8Uul1m2LwRMLVjpMbhwA2HbciqtFbBgw775CITsNmEsdGQo0iMkP7B+Nx\nRHGotgxhMJazwdDHPa98v+Cdd4JjV7nUPxZeTZ4I8OZxHDyIDzOuy2hz6t6F\nbSqs\r\n=vRum\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./sync": [{"default": "./lib/sync.js"}, "./lib/sync.js"], "./async": [{"default": "./lib/async.js"}, "./lib/async.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "9a318c311102f1628c490199fb6edd20e513c4d2", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "prelint": "eclint check '**/*'", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "15.0.1", "dependencies": {"path-parse": "^1.0.6", "is-core-module": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"aud": "^1.1.2", "tap": "0.4.13", "tape": "^5.0.1", "eclint": "^2.8.1", "eslint": "^7.11.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_2.0.0-next.2_1603315338579_0.8995998305286601", "host": "s3://npm-registry-packages"}}, "1.19.0": {"name": "resolve", "version": "1.19.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.19.0", "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "1af5bf630409734a067cae29318aac7fa29a267c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.19.0.tgz", "fileCount": 96, "integrity": "sha512-rArEXAgsBG4UgRGcynxWIWKFvh/XZCcS8UJdHhwy91zwAvCZIbcs+vAbflgBnNjYMs/i/i+/Ux6IZhML1yPvxg==", "signatures": [{"sig": "MEYCIQCvJ8u45GWJ+VQvSUHUxf7lLQKX7LJFNSCQV5SbH15xYgIhANgCrgLoVM92CzKl9ZKEGcHqwQVbXgEYrRry6pF5o231", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqyXDCRA9TVsSAnZWagAAtakP/0IWzZatJ435dNuFjLW/\nRH7gpW/AaTlOMX9aHvpeEdmxLapFptDnmloc+Q6p7trUezSv7aNUMPlCQpDF\nxyy20BPbrUIQrXduwX86O3qg/fnH6DcLaX4dftsnk4HPvF/nMC2fGffyCcaT\n1fWrtzfhGUcj1LFys4vAGGW3l6k1+NR02pdLgrNOeUxdnhQcv4DE0HrAT+uk\nM32M6TtNHCFJH9lig/GH6aiksn4tkEbLn9G/s4JnAKeYngwz0K03bGDV4q1l\nIujCvtCxgtWWhyPhT1ktqHpf3IpiK3NGM/vQVvv2pCvATJc4JsPHd0ncgbw8\nLnk632MCOShzWf1GWmi06WHTZdqCb8rg6mw2dALUkE6oZ3VuF6alQvchlWln\n5evG3Ujeh0wmcku8Nthfptzs1ApbHYerZDS9QgPTlfPbXzpFHzSriFVKxWNu\n8dJBva+aucNtDRlgZ4LcfhGX/652qybdM/IjFQH+scJJRetlaXa3YyfY5iEI\nW+hUXSvz5wK4se+UK+C/y9sRJyJFq3RbEnaLBDkwMkLmMSJUYmayzicyKl0b\nBA3TB//S4mA2AdImkZMh72W3ymVqznM/Aj1gNm9m74st8/xyg7S/qu3QjbzX\ngfNJe+zxF1gA+Ctt/CgRw5lLFUUfhcgL+SVb5DEarEHQmg32rJq+VthFUbGU\nZOTT\r\n=YJrF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "ae1aa4f11309ddd742cec2ddd4ab66d182917a45", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "prelint": "eclint check '**/*'", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"path-parse": "^1.0.6", "is-core-module": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.3", "tap": "0.4.13", "tape": "^5.0.1", "eclint": "^2.8.1", "eslint": "^7.13.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.2", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.19.0_1605051843350_0.0026252986028363345", "host": "s3://npm-registry-packages"}}, "2.0.0-next.3": {"name": "resolve", "version": "2.0.0-next.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@2.0.0-next.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "d41016293d4a8586a39ca5d9b5f15cbea1f55e46", "tarball": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.3.tgz", "fileCount": 96, "integrity": "sha512-W8LucSynKUIDu9ylraa7ueVZ7hc0uAgJBxVsQSKOXOyle8a93qXhcz+XAXZ8bIq2d6i4Ehddn6Evt+0/UwKk6Q==", "signatures": [{"sig": "MEQCIFLVAZ/uhBS8JtRgVM4/2wMVjHSyIFIMgOtHrJY+DqZTAiB/vvJtJbScti5t3eQWJfUctLaQdsqw9CmoeQ2cWQ6Ilg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJK6aCRA9TVsSAnZWagAA10cP+gJgNurTRvy+SA1grXNa\ntrC1wgzs+2ZE9ycRsb7rOaG28IGF31FcLYSFcgTOJV18ii3uYCOqNTtYmGcP\nWfpkGONa9qqg652ouLVSbgSsiqshlmnuHDrcmrY1GqI7a3+V9h9ju1Yyg3GN\nCsh17RlVrDPaVs2cMqOAD8c3vV1hQ3nvZrh7/LEdRdxGQ+wVJjcj46coofPO\nMsgEkW1zt+zWy8rnAhd6hvk+24e4zmO6wx3y2Q1uqGBzrEPlSNnEx3n5j5Fn\nyu6emOh1Ezo0l8Ejs9uOY86OWFyaOftMTcwTs4UYn2vjufD8dDbqY14Fh+m4\nAvwBsiq8exlACln7e4GqYxzkyDaUFTHRyNm0Gm+nqXiufMrNEwYxQvNNMLYW\nPEPwix3KdJ2qOvVAXJe60TyRXuVj600SUjp9nJzkiUGVFYKEn/oDAcH2rWea\nahycErjEGJP4A1jEe4TZg+hdhfqr/0vZ4MFxEWw8V314+GbPEnGJjQ6ezzMM\nC15h0D7S1D8gww0VTjTbBaEV4n+meIjqQsYcnQbZYqrlEjzsyzBCCLSCfcKC\nKBvT6MwL15KB1VT780pq8cHe7RQYQCnAgn4+pS+B23m75ICruLfXbBye/h0Y\nvhjCBz7s7xDptrYQGhnZc4CuFZW2mJNAgIxUdt8/sFuX1KQBFI96LUOum/tb\noiEQ\r\n=aSNT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./sync": [{"default": "./lib/sync.js"}, "./lib/sync.js"], "./async": [{"default": "./lib/async.js"}, "./lib/async.js"]}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c6bf2a5a23f7bbbd523fcdf39897f8d3908b9442", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "prelint": "eclint check '**/*'", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "safe-publish-latest", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"path-parse": "^1.0.6", "is-core-module": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"aud": "^1.1.4", "tap": "0.4.13", "tape": "^5.1.1", "eclint": "^2.8.1", "eslint": "^7.19.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.3", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_2.0.0-next.3_1613016729967_0.25728475441177423", "host": "s3://npm-registry-packages"}}, "1.20.0": {"name": "resolve", "version": "1.20.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.20.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "dist": {"shasum": "629a013fb3f70755d6f0b7935cc1c2c5378b1975", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.20.0.tgz", "fileCount": 91, "integrity": "sha512-wENBPt4ySzg4ybFQW2TT1zMQucPK95HSh/nq2CFTZVOGut2+pQvSsgtda4d26YrYcr067wjbmzOG8byDPBX63A==", "signatures": [{"sig": "MEUCIFuGyVvgkGWcvwjMIF5cww1shpM45HX2H24RARg0zOmJAiEA6BgbTR2nBoMEr5UXX0HA9VFxdm2+9fefAbbNImD3lUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJUMECRA9TVsSAnZWagAAVC0P/1Li2cGL/ePYi8/BsV/m\nBieeJ9F6sLVPIgYzQ8nwcFuwPv+eRzKPGSAoHVMH6oVbnPBfZAZAGl9KoGTe\nutlMGAcR+WQm2E+bThEJuJs6nkwNIKfbJ114ZzjrKQr0YhFgoajFpFw/to3Y\nzuor0ufzgQ9Sd3sxSUv6VP2YBID9mK/GQGaMDNXl5w3BDyhsxTSe8siPUeFE\nRc3mwmdX7u8k1nW/WVWldwdg+64IbF4C8fRum8ZsIft3aSljGmcN6e62jo6c\nCxCfnwA/OwXx2N86WqX6s7igpA72Hps9EHY5EKq7F4+y7VM2DDNFtJR0oPrc\nE9hoqwDJ9HLxpJT8md9WwoJlJM7B4/U8KA8YUl/72nP/dSV+2Gf6wa7UaK7J\njzfCBKHVIkb4aS0+pZfCP1J/kBE+yil6IuUKU2q/JjhWrewkLA6E8pAyjLAQ\n00pY3NDP9jMCcaCiyE9FItpBDNWK3oqoRdTOzp65YFCodamZ/u7dRB0CIBMa\npco0vxFzbX+mlN1+2KYs0MtUuZmUouT2ImlpefbAnlpeVr/Qe+MazmCWn/eO\nyziGnTgKzU2DAA3LA8eECJhFXz740gM34Ses3xeNC+/v3Pj1ULPXC+OsQB9l\nxyLMn1q5a/Vkhl7SeLj/Zo2CXuZiJmWg3a/hLf5d0BJNFkZLcRP1riae1yB8\no26Z\r\n=Cl0v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "26e54e89e38b603ae7a6397d7de00dbc80aa5413", "scripts": {"lint": "eslint --ext=js,mjs .", "test": "npm run --silent tests-only", "prelint": "eclint check '**/*'", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/ ||:", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"path-parse": "^1.0.6", "is-core-module": "^2.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.4", "tap": "0.4.13", "tape": "^5.1.1", "eclint": "^2.8.1", "eslint": "^7.19.0", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.3", "safe-publish-latest": "^1.1.4", "@ljharb/eslint-config": "^17.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.20.0_1613054723763_0.8071854002091299", "host": "s3://npm-registry-packages"}}, "1.21.0": {"name": "resolve", "version": "1.21.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.21.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "b51adc97f3472e6a5cf4444d34bc9d6b9037591f", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.21.0.tgz", "fileCount": 95, "integrity": "sha512-3wCbTpk5WJlyE4mSOtDLhqQmGFi0/TD9VPwmiolnk8U0wRgMEktqCXd3vy5buTO3tljvalNvKrjHEfrd2WpEKA==", "signatures": [{"sig": "MEUCIQCLue4VsBLJK2HnJh2La3TxTdsQvtaBaohT97sFgSJc9wIgerEJgqTjBoqImv9s8xs82gBGgqifesa28L1JmQdax+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh02XqCRA9TVsSAnZWagAAMQEP/3whfllAg6k8Kz9RE+aV\n/qwH/KA4L6x+6a8ptRtGGbuATcIGlAjdCd8FsilY37aCr2WctFjCQ+F4iMlQ\nua6Og6aBuYwOqm8AbpzKCrhiwiYjSvwiC4Jrik1PiKytHUzviQ7cmDaVbKgH\nlFu78SmbPh5ov+qYxRpUPuS026XeFG6yZ3to1Bqi3n8ClTjw3zmL2fVHmd48\nFm5QfysrLaydS6x7SPZuvUzRHYnWx/1Ow72MRj2vp1wS3Ku+eioESq0hUOW5\nBB7n/Y0UCvVPMJwEP0pJklvtA3KW3A3byiBhw4j2Qs6wNLheI86X31WoroZ7\nBfmH7DmW+dgu3ugqA+vrd0/JqrPmyN8ISzFMB1S/fONTBSkws8r3MKWFU1+v\nfxazMtr7X0yBHE8FjcbkRQocaorxvl1wXdVTYbBJlgjOjFxfSP0GJY19fQW3\nn2wUOIaYLVqmtDps2BAWbe7gerBZqUCqr4pn8/58K85YLPjYiZ6p7QbECcR8\n5bls0k55ypX1MLjO1YozNJwyIrDfU1DQ0o3W10mDEQI9xACvq/WOMI20T90z\ncPOmMcVMTCV6+i2f6l06JzJy8S2LMi7SRS7HqHWB3QHh+2c6nEETg6N1jmfb\ndFG2B7M1YdXG8Nh+EaKB+zsJ7P/dyY8eOxv5EsY1IRF0+hm4kHLsEMwMIA9r\n7R16\r\n=zUTb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "da9e8af55d89ac3564466e0adc9cc784bbc93141", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/ ||:", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.8.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^1.1.5", "tap": "0.4.13", "tape": "^5.4.0", "eclint": "^2.8.1", "eslint": "^8.6.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.4", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.21.0_1641244138725_0.8074808252073382", "host": "s3://npm-registry-packages"}}, "1.21.1": {"name": "resolve", "version": "1.21.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.21.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "1a88c73f5ca8ab0aabc8b888c4170de26c92c4cc", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.21.1.tgz", "fileCount": 95, "integrity": "sha512-lfEImVbnolPuaSZuLQ52cAxPBHeI77sPwCOWRdy12UG/CNa8an7oBHH1R+Fp1/mUqSJi4c8TIP6FOIPSZAUrEQ==", "signatures": [{"sig": "MEUCIQCC7+lHn/xhjxdoX3qlfDPkJVfF7tzbwpAQFDGfyEIQGQIgIFaQ/1a8wpabDJAHvgNWDPvjzUx5uNccPj908EdySf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6ltYCRA9TVsSAnZWagAAok4P/2UGnXT1Y6hpFbDqC+HZ\nZyENQ6KMgfC9KZkXurvpYBxcGr5w5xf2dxLS2hxOpwVXndn646Fx3tXNA+gk\ngAcmMdACkD+LCUBjVeUsNO4xJSaEQ02MOGe/a3McGLdpl1wj9y5FrPit7uBx\njI6vhSrpSv7x2BB83qKE8VXSlCzeT/QTq6q4xo9Z8p0fCH3bzFYtcyOz9v4F\nVvttPAJl1+/vbn5ghX275Wjc9HtoRhGoBKAFlL6ryMIK7/4zn+n4QX823AQL\nHT8ydtq8IfcT5itFNkbr4s9uycsLvRNACPIFdvAbmEth3gkHQ9h/kd2+Db+M\nhSNWMMUy+7iJ7WIqfpwiG2i6+XjZsXo6LTgzs+gL3IZDiU7wclORixFt5NYz\nk1tWER3EnP8HyOVbguU8h7m4wX5FSiXWd7rk3UC7jIokfP8yC9soFJTvPBos\nQcJTldAFK0HOo2Kn8vCZ4u7868eU/gYfIKmphi7fk+IZ2hAc4bTWcbeH8EFV\nlYkCKEFtJ6HdXEcHqFm5e902J+y++ZEieqG7ywaJdh5mSIDISFNHJ+JwN8ks\nKXgsnXJkO9zZLXh/jB85XEkbKYbK3ScWJA3347TnV1/mGbCsSItyOwXfHTWP\n95f+iHwUVY3LcUcEFyueSNJ6362MoSLnJaz9d/OwbvkDyeaKu6ZBdhGhIbFt\nplRt\r\n=uFMP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "1f3dec557247fb034c04b9b857a6fa94d7b493bb", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/ ||:", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.8.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"aud": "^2.0.0", "tap": "0.4.13", "tape": "^5.4.1", "eclint": "^2.8.1", "eslint": "^8.7.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.4", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.21.1_1642748759874_0.18086397659798914", "host": "s3://npm-registry-packages"}}, "1.22.0": {"name": "resolve", "version": "1.22.0", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.0", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "5e0b8c67c15df57a89bdbabe603a002f21731198", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.0.tgz", "fileCount": 98, "integrity": "sha512-Hhtrw0nLeSrFQ7phPp4OOcVjLPIeMnRlr5mcnVuMe7M/7eBn98A3hmFRLoFo3DLZkivSYwhRUJTyPyWAk56WLw==", "signatures": [{"sig": "MEYCIQDopBp4W+Q9RUzJcaOxyI1rnZQWO6KblXyu9cI5Z8RrWAIhAPW/drGC6FHhwElkIMLOsclskoA8W2jQcseZmcwG6Usu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7FioCRA9TVsSAnZWagAAlVAP/irHtmbY5Qv8g89b6f+U\nNXxDGqNU+WqmPbSrh+nbvOwmgCj45ztS1XJpNrWLxJF6WmBySzw3J+d82xLp\nOMxOj95SM/c+1QpxhjCplxydgWEgIY8jOqGCnVqTfgbqd+/710IE3E0jVNqf\nfYLGIRyzS+Dlg5dtF27T0yK6YuVbaMVtw+N5EJMk0yPLT9TpkC1RY0juuySl\n86bPOuPNKxsv3ScSYnQuhzQCsuCC90LH6fek5s1yAUT6eX5IHOxJtUX//BdD\nZhb/NQs/wIxZpeVX2uNmmyyjynAePR+VqmQDZsvGLeVhF3zmz7qryKSDEHZa\n6LvnDzwIq1yOQTyTCGuzCp0G31m8kdK9slnrmrE8ItKXk+wB+iQNlxneUKpK\nXhgi0PSwgli/D4f3AQO/4iPoImg/vVjdLSeNIm09ktE1gS+yJQu5otdiENVZ\nFFfrZgHwK6PgXSs6AE58r9pThnHdxkz1Whb+4yl10U9La9zFG4Gauv0kwW1O\n4+B2uNN8mthdT97ScP/GvLkEQwPbJiX2jndrM6kvzwYSQCuwP6lK68plKRsz\nCKhOvxiLOk/3NNVH38OiZk4p5uMKBq43W9R/3Ohx76BFz/5lEsHUE0KS+Gs+\nLCqGdABm3jnsu9Voe99xnsaRncWNbmqnpWWJsuBUSOfq8Z4b0p6TLoUxTRzU\nFJ77\r\n=Cm/r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "cd52b64c50bdcb749f95e007914a469863554c4b", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/ ||:", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.8.1", "supports-preserve-symlinks-flag": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.0", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.4.1", "eclint": "^2.8.1", "eslint": "^8.7.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "copy-dir": "^1.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.4", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^20.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.0_1642879144275_0.8522007626844326", "host": "s3://npm-registry-packages"}}, "1.22.1": {"name": "resolve", "version": "1.22.1", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.1", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "27cb2ebb53f91abb49470a928bba7558066ac177", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz", "fileCount": 99, "integrity": "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==", "signatures": [{"sig": "MEYCIQDe3lW72AzQxIkBglfhaho3ritMbGFMyb4avp22aLla0gIhAKioMo5Y1Gi99TY4YmxoOg5K8CPFckd+9i3hZhcOWDM1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirOo/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpD+hAAgELTZAGvClpCsxKobsdaG5LDwdcC4nR3HyO/BBlEN35GvtTm\r\nscK2fSDc7RDXL3j393Um6LBjb8jb1dUst7AhK/HSXr6WcmtwXP20jh5dyq38\r\n7D7VwO7cXreDSKbg/Aoj0mkYI9KldgEm2wvj7wM4JxLYk06T+CxsWXsnjF9M\r\nbUpbAqCr57RNzUd7XxuLIG31DPmdL7T5KuKpKiE23WvVLiQre7o6DFdPkjmG\r\nDE9VcGgp06WkxCBdyYulZ0IWeuFAOHbtt85/aO7shl5/GvFXTocj5FeuaFYj\r\n8RWmx1MXs6gqSCUh2JRgxWbrjWiPtvR7g/g8+MNS+ymyjSFZgbrcgP/FPOJB\r\nA4/Q6FYyyQcGHgjy3S6N8cHDUGjOkDyUdajRz5Sw/dBWmdtsCQQCBQgYQR/k\r\nQm2U7hnP34NhEbSRM3KaK1oHqaGEnOg7Dg3EpU1Ta7Lskph/QTXfi5q60HJ+\r\n9YONloSq7phq74ukEKt5q7pcnU7z5Blp+FyFPJS0Z1+3yDoGGGg/ki0LoIfz\r\nwZPIhW2bSkpGw9TGNtQOhzV3ydd6l7G6mYAl/fWbuNWLPZIu+q0iP/aWyfxL\r\n2WXka5L2SkQVwGgy7zUyIglmTYvJZfQ3/s7Boi9v5kz5CDftEtgyHxt0FVtB\r\nz70HX6bONmo5o+k1CMUMK8iLnKvfGPRyvEw=\r\n=jDoy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "8eea601093612229da100e9dfbeb4b2b47693aa6", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest && cp node_modules/is-core-module/core.json ./lib/ ||:", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.9.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.0", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.5.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.0", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.4", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.1_1655499327393_0.6059282883803792", "host": "s3://npm-registry-packages"}}, "2.0.0-next.4": {"name": "resolve", "version": "2.0.0-next.4", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@2.0.0-next.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "3d37a113d6429f496ec4752d2a2e58efb1fd4660", "tarball": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.4.tgz", "fileCount": 104, "integrity": "sha512-iMDbmAWtfU+MHpxt/I5iWI7cY6YVEZUQ3MBgPQ++XD1PELuJHIl82xBmObyP2KyQmkNB2dsqF7seoQQiAn5yDQ==", "signatures": [{"sig": "MEYCIQC0/Kx9mMhSzvkv3/xoAMsrpn2B+0SQJxrdQHs1Fkp3KwIhAK1j/4w6wUSuOpBDNSEjk3AgBYI0kHaHkHTbBWzFbGFe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJire5MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqF5w//RuuCbjLC+/4ICaeYCQJ5k4HHQLbURcwXBflz5Ont34fugMbr\r\nTOWc52NKqiAjsKqkENtnFK0Q71O6rDmMstc4V9LrbgAQUy1TTRdcmerJ+6Nl\r\ncrx0coGODGIZ04REtu0NVzpboOhNm6q8t1be6ey/GJAdSItxRZnD/tWkrikY\r\nUR+pWOoC+Bo9CiXT+RMKisrBUnlbgDK78/pfgUlF7SeErVeUg+03iMfTYakO\r\nQOi+ewMswXst7LysMyoMRRNHYwKhJNasmo4KYvqwjEYqQE760bz8F8gUjWSH\r\nnWdJdxBjv/R+0JLmPM8ajVoVqSLE/GXmV0F6o75d0QVWvHmMQswJ8v8NIZwq\r\nVSfC9n6UDysRmuCTmOcU8UNmlJzWM46o+A3q9+CPrI3KPAx5jhSrOCBjVqKs\r\nLEf9xVUKnmvKcNH9Ap504T7fpu3oOHe3afGrAtvae7xEWs4hsCcEOOcNzFFe\r\nvqmoxkYDgFRgt3dD+3du8kTt856N/3qcB0j2745H0zan1axPJJsQ4AbkEh23\r\nRZtJeferv5sGR/O7uwQswHsZQVY0GNEMrMvnfGOALQp7UbjnQgZ+D0Bv1ZZ1\r\n35tZmf10oHhr2RXk8XEBLefzed8sWTvkIZzh2odgD/EL1bG7xU5GK4qq6k6a\r\npInm2aIaKEfQ969twoONMvLyh41SXFC74l8=\r\n=5JC2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "exports": {".": [{"import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./sync": "./lib/sync.js", "./async": "./lib/async.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "74097d0962e15eac7470b72f21ed7b6939bc58ca", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "8.12.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "18.4.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.9.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.0", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.5.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.4", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_2.0.0-next.4_1655565899879_0.4631601391397775", "host": "s3://npm-registry-packages"}}, "1.22.2": {"name": "resolve", "version": "1.22.2", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.2", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "0ed0943d4e301867955766c9f3e1ae6d01c6845f", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.2.tgz", "fileCount": 99, "integrity": "sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==", "signatures": [{"sig": "MEYCIQCrdepiJqbrMHiDP/0iQ8lan9UG3RbEx8JP4EE1cyceOgIhALbfSowolRLKvf/vAR6/j+lXKSPQXq927v9nr2uXTh93", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLa7gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquDhAAkxZITDLegRq6zlr7eVeLLG/7Fn8sPhTtiwDK6CJqYiOAVAQ8\r\nzxNbTk5Cl7EIAgQ1ZSpFYuYFpM9O2nxBdFuZ5sE0EgIapj/gRc89Ii1q5GOj\r\nc3EgPGCi36snDip9YURNZRPxU43TsYXszo+fqznS+8SRO/ng60w1v3PvFx7W\r\nRVpGMWewXr1g/ld6KN3rizLFKYnM5marowu5VGfGsIZcYwOw+t/yMOAwpuNk\r\nQniGZcQooiFPmUHZHv+vMWzumR/jHoOneW9bLDj7rieyV2PDh3L8QDTxhNBc\r\nuwWJFEXbMD6ezssL8wEAZJjTqF3ieHesPkI/adxUmZFpQU6OUbBvC8RhAode\r\noxPjP3FnYedYxZros8GvYn0v0nSDCBHRzQcIK8te1hJqhhuPwRml7ZITzCqq\r\ntDD0kB3iRPTBLa1RHdaxuUacCb791gsLZZ2UvwCjeTqZq5T6sLlLpnT54dPp\r\n4ygLMWCgNeVrhE/RcT55BS+/enKC8LLz5GUX83/MbWjD8q8qoiIhhKaj7pMp\r\nKFJ1wKCCBov3yhEE9t6qbu82mSFg53wHeSOduwHbwwUaYzdiWAuf+SCd2aS+\r\nE0ATMzaeWuZ9mijKMet0zpCRjixGZq1GoJNNd2hxkg8pRXWGfk/qmfdzqaRX\r\ntA+/zowss1SdAAkqn58oMFLA+YQTQvA9qGE=\r\n=D2oe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "c2f9ce254f0157b5e2e53e9aee0403c510909f7d", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "19.8.1", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.11.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.2", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.6.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.0", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.5", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.2_1680715487990_0.9080196087646395", "host": "s3://npm-registry-packages"}}, "1.22.3": {"name": "resolve", "version": "1.22.3", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.3", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "4b4055349ffb962600972da1fdc33c46a4eb3283", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.3.tgz", "fileCount": 99, "integrity": "sha512-P8ur/gp/AmbEzjr729bZnLjXK5Z+4P0zhIJgBgzqRih7hL7BOukHGtSTA3ACMY467GRFz3duQsi0bDZdR7DKdw==", "signatures": [{"sig": "MEYCIQDCQH30jhoQGxiOd5FntG9StiOwM0zXGZfelvPE8vRLTwIhALzv+hU1m4outYvgEWifUi8arfsvjJXFlTpOXUZpu9Y6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOXy6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI/w/+IR09Ct64+xkI09BG0T8CjdpIJ5vfYMRnd3KmltfbSU4A+aZL\r\nT/ivYhbXujQ6IMpYLfjSu2SFcvoLwIu8D+kjdd1MptWsoO58VGAgtabFibcX\r\nwutWy4P+ALmSRSrmAt8ETIDKli1gO0FN77CqfaIVbx4jkjJJ2dO47XirbWks\r\nvvw3TBzj8hCNIT0fEGQiUFbvTTICnyJPG0a4zv/uwfHwsnKZLSFXYGuS9fzb\r\np/ISneBt9NKKYDd5RPZ+mv9oNhaYwN0a22PgJXItD76RIv4aC3t43DtwNlDo\r\nOxxFh56X68ef5Gbj9anvU5xpeyifAX5PUzPJFzV1eAG1ar91krwfuwKJ01yE\r\nBPLAE7yOSX8xoFD2tFsknL2Nun7NyxLm2UnZy2lKwfzFgz3ru6FiImdAgPCz\r\na7nzU4SIaIc2b5CPp2oJJ38Gl0a8KLF0uFKoJUXMD1Q0ZWk+sdHKt6pGxiKR\r\nj7mlq5AeJ1IeNxTviPs1C+JLspguRTP178Ofumb1qa+ndvjfjBauLuhffMEX\r\nYmOEA8f5sUQ12JWf1kudvu6AdiIVkbQTBUcF5aK1taoHHm8HZbSTUl5SKttg\r\n3OwWMw3bdiDVT+kFuOowDiTFoXilUBE9aOcQeYNftVbB9KberTn3/1/o6rfD\r\nwGq9PhXXf6SQw+9C41joCGMbBs34FqsIdkg=\r\n=LULo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "d3332a20d1c06e4056fdb976c2bbbaf9a5dba7e2", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "9.6.3", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "19.9.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.12.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.2", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.6.3", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.0", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.5", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.3_1681489082223_0.21967505715884217", "host": "s3://npm-registry-packages"}}, "1.22.4": {"name": "resolve", "version": "1.22.4", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.4", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "1dc40df46554cdaf8948a486a10f6ba1e2026c34", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.4.tgz", "fileCount": 99, "integrity": "sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg==", "signatures": [{"sig": "MEQCIC0TRuyMZLur7Rq8m9FCbCG4X3MfXzTvCggmge/30jkXAiAriSYHHL5igodfcGzs2JK5wjhGWELbK3N1ORm5nir4WQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144813}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "37504d61c2fcf31ab070b7a67e18e64d558fb227", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.13.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.3", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.6.6", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.5", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.4_1691191851882_0.2999288680667882", "host": "s3://npm-registry-packages"}}, "1.22.5": {"name": "resolve", "version": "1.22.5", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "a83c145cf04ffcd19b1f3f5f9e0ae8b9053f0615", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.5.tgz", "fileCount": 99, "integrity": "sha512-qWhv7PF1V95QPvRoUGHxOtnAlEvlXBylMZcjUR9pAumMmveFtcHJRXGIr+TkjfNJVQypqv2qcDiiars2y1PsSg==", "signatures": [{"sig": "MEUCIQC9kRpDJM1Wlu1EV5BsfUvhFNTrvsEfbr8kDoC/AtTNcgIgK3sO2FtAM+xfrgKuGxGTYh+38QbPfWk/XGaPImtcxWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144830}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "7f3ebd0f31c91f8f07fc59434d26c3b71af489af", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "20.6.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.13.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.3", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.6.6", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.6", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.5_1694725324433_0.10683432014144967", "host": "s3://npm-registry-packages"}}, "1.22.6": {"name": "resolve", "version": "1.22.6", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.6", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "dd209739eca3aef739c626fea1b4f3c506195362", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.6.tgz", "fileCount": 99, "integrity": "sha512-njhxM7mV12JfufShqGy3Rz8j11RPdLy4xi15UurGJeoHLfJpVXKdh3ueuOqbYUcDZnffr6X739JBo5LzyahEsw==", "signatures": [{"sig": "MEYCIQCS4QCQfwrEZp/HUYIhR5NQkNZnEna55kfF7lcgsWlIPQIhAKFri3WVhx9C29Zdt6moMjYmlbwJFXQ5E5DdMFuRiBIr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144875}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "2ae67c11e6009b9c94932fc552f688204168edd7", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "20.6.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.13.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.3", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.6.6", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.6", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.6_1694813579897_0.29305691853387383", "host": "s3://npm-registry-packages"}}, "1.22.7": {"name": "resolve", "version": "1.22.7", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.7", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "dcbb776d65d7771f2ee4666e4b89c564b15f20dd", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.7.tgz", "fileCount": 97, "integrity": "sha512-YiXAr29s3pviTexp8YEMKtdQDeFxk74jt/w5Viiw7SAVC9McgYD/GVaVyJ18pNCF/VIvzMtsk+zHwJp+YQWCKA==", "signatures": [{"sig": "MEUCIC8rPNy/lJr0nZaecgWkuwEJWenCCgpDaBZV1WHlRDbVAiEAjUI3dncmfELT83nNQrK9Q/xwKkdeb4cR4ZGdgCTbtdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145086}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "75d3bb9e247f7a75f970c95391fc0408b0e34ac2", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.13.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.3", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.7.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.6", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.7_1696971035491_0.7727110745752364", "host": "s3://npm-registry-packages"}}, "1.22.8": {"name": "resolve", "version": "1.22.8", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.8", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "b6c87a9f2aa06dfab52e3d70ac8cde321fa5a48d", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz", "fileCount": 97, "integrity": "sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==", "signatures": [{"sig": "MEQCIHtNi8KWj6UJ0unSTZcXijr5+j8MpTbXYCEGMWty70PHAiBuESUbSta0k4V0gljo7AFp2b94qhorvxVzMbzGHDKsJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145394}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "b8298720c6ece9d3b7231ea90bd920f266a449a8", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.13.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.3", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.7.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.6", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.8_1696973834478_0.9766265164147392", "host": "s3://npm-registry-packages"}}, "2.0.0-next.5": {"name": "resolve", "version": "2.0.0-next.5", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@2.0.0-next.5", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "6b0ec3107e671e52b68cd068ef327173b90dc03c", "tarball": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz", "fileCount": 102, "integrity": "sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==", "signatures": [{"sig": "MEUCIQDaZYKM8W0wFdraPPT5T+ZGD7wf2ncLnCUMek8E9Ww8XQIgU1NsB2lITXFSV3+9+5HbTvhNRb3YZ37/L8MSxrZkNKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138100}, "main": "index.js", "exports": {".": [{"import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./sync": "./lib/sync.js", "./async": "./lib/async.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "284daba733327c803dbc85168fd3c72ebef52fc0", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "posttest": "npm run test:multirepo && aud --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "20.7.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.13.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json"]}, "_hasShrinkwrap": false, "readmeFilename": "readme.markdown", "devDependencies": {"mv": "^2.1.1", "aud": "^2.0.3", "tap": "^0.4.13", "tmp": "^0.0.31", "tape": "^5.7.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.0", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.6", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_2.0.0-next.5_1696979442667_0.5404564388733637", "host": "s3://npm-registry-packages"}}, "1.22.9": {"name": "resolve", "version": "1.22.9", "keywords": ["resolve", "require", "node", "module"], "author": {"url": "http://substack.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve@1.22.9", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "homepage": "https://github.com/browserify/resolve#readme", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bin": {"resolve": "bin/resolve"}, "dist": {"shasum": "6da76e4cdc57181fa4471231400e8851d0a924f3", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.9.tgz", "fileCount": 1472, "integrity": "sha512-QxrmX1DzraFIi9PxdG5VkRfRwIgjwyud+z/iBwfRRrVmHc+P9Q7u2lSSpQ6bjr2gy5lrqIiU9vb6iAeGf2400A==", "signatures": [{"sig": "MEYCIQC1eS6nhqMEYSpMpkVdfZQRPfZRDlJt0ggUOxKjYChA6wIhALQXCjE/nog0qaAQv+4SAuITP0LL3TpOpA3MzYUvkipk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3087705}, "main": "index.js", "funding": {"url": "https://github.com/sponsors/ljharb"}, "gitHead": "a7c30751ce70ef316cf7b141a992d24a4374832a", "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "test": "npm run --silent tests-only", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "pretest": "npm run lint", "posttest": "npm run test:multirepo && npx npm@'>= 10.2' audit --production", "prepublish": "not-in-publish || npm run prepublishOnly", "tests-only": "tape test/*.js", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "prepublishOnly": "safe-publish-latest", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/browserify/resolve.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "directories": {}, "_nodeVersion": "23.4.0", "dependencies": {"path-parse": "^1.0.7", "is-core-module": "^2.16.0", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json"]}, "_hasShrinkwrap": false, "devDependencies": {"mv": "^2.1.1", "tap": "0.4.13", "tmp": "^0.0.31", "tape": "^5.9.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "mkdirp": "^0.5.5", "rimraf": "^2.7.1", "semver": "^6.3.1", "copy-dir": "^1.3.0", "npmignore": "^0.3.1", "in-publish": "^2.0.1", "object-keys": "^1.1.1", "array.prototype.map": "^1.0.7", "safe-publish-latest": "^2.0.0", "@ljharb/eslint-config": "^21.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/resolve_1.22.9_1734124383054_0.024450477568345397", "host": "s3://npm-registry-packages-npm-production"}}, "1.22.10": {"name": "resolve", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "version": "1.22.10", "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "bin": {"resolve": "bin/resolve"}, "main": "index.js", "keywords": ["resolve", "require", "node", "module"], "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "tests-only": "tape test/*.js", "pretest": "npm run lint", "test": "npm run --silent tests-only", "posttest": "npm run test:multirepo && npx npm@'>= 10.2' audit --production", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "array.prototype.map": "^1.0.7", "copy-dir": "^1.3.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "in-publish": "^2.0.1", "mkdirp": "^0.5.5", "mv": "^2.1.1", "npmignore": "^0.3.1", "object-keys": "^1.1.1", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tap": "0.4.13", "tape": "^5.9.0", "tmp": "^0.0.31"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json", "test/list-exports"]}, "engines": {"node": ">= 0.4"}, "_id": "resolve@1.22.10", "gitHead": "d1e73275da515a3bc5d0064f15ebf09b3947199b", "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "homepage": "https://github.com/browserify/resolve#readme", "_nodeVersion": "23.4.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "shasum": "b663e83ffb09bbf2386944736baae803029b8b39", "tarball": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "fileCount": 97, "unpackedSize": 145619, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCge/1OHiL3LBDDCQbVvzAIkbc8+9JZ0PRFuIf9pjuwowIhAJHU3s08faIa1dNhol3QWWPcbke0HpWLN9imkLrUgDK3"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/resolve_1.22.10_1734629351034_0.4330978986649696"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-06-18T10:12:44.109Z", "modified": "2024-12-19T17:29:11.417Z", "0.0.0": "2011-06-18T10:12:44.853Z", "0.0.1": "2011-06-18T21:31:40.192Z", "0.0.2": "2011-06-19T02:09:52.813Z", "0.0.3": "2011-06-20T11:21:37.073Z", "0.0.4": "2011-06-21T01:53:52.588Z", "0.1.0": "2011-10-03T10:54:49.523Z", "0.1.2": "2011-10-31T03:52:06.706Z", "0.1.3": "2011-12-14T14:50:00.624Z", "0.2.0": "2012-02-25T09:12:33.038Z", "0.2.1": "2012-04-12T22:34:07.555Z", "0.2.2": "2012-04-30T08:21:44.301Z", "0.2.3": "2012-08-12T19:19:25.411Z", "0.2.4": "2013-02-18T07:33:11.628Z", "0.2.5": "2013-02-18T07:35:31.068Z", "0.2.6": "2013-02-18T07:37:01.940Z", "0.2.7": "2013-02-18T07:39:27.988Z", "0.2.8": "2013-02-18T07:43:06.007Z", "0.3.0": "2013-02-19T03:08:24.356Z", "0.3.1": "2013-03-29T19:58:32.914Z", "0.4.0": "2013-06-09T00:53:00.320Z", "0.4.1": "2013-07-30T03:02:45.931Z", "0.4.2": "2013-08-03T17:19:53.826Z", "0.4.3": "2013-08-07T23:19:08.390Z", "0.5.0": "2013-09-02T00:26:30.489Z", "0.5.1": "2013-09-22T21:09:27.891Z", "0.6.0": "2013-11-26T20:35:26.199Z", "0.6.1": "2013-11-27T13:06:31.064Z", "0.6.2": "2014-03-21T06:34:39.864Z", "0.6.3": "2014-04-16T23:57:01.187Z", "0.7.0": "2014-05-17T03:07:28.674Z", "0.7.1": "2014-06-09T23:36:57.259Z", "0.7.2": "2014-07-25T00:20:21.008Z", "0.7.3": "2014-07-25T01:05:05.891Z", "0.7.4": "2014-07-25T08:56:46.679Z", "1.0.0": "2014-08-11T02:18:16.735Z", "1.1.0": "2015-01-27T20:54:19.747Z", "1.1.2": "2015-02-16T19:52:08.334Z", "1.1.3": "2015-02-17T19:40:53.158Z", "1.1.4": "2015-02-20T23:59:13.906Z", "1.1.5": "2015-02-21T19:53:01.630Z", "1.1.6": "2015-03-15T19:56:41.925Z", "1.1.7": "2016-01-24T01:21:19.636Z", "1.2.0": "2016-12-14T00:55:43.275Z", "1.3.0": "2017-02-24T08:03:09.538Z", "1.3.1": "2017-02-24T10:03:58.229Z", "1.2.1": "2017-02-26T22:55:09.864Z", "1.3.2": "2017-02-26T23:01:41.351Z", "1.3.3": "2017-04-20T06:09:22.786Z", "1.4.0": "2017-07-26T23:27:35.619Z", "1.5.0": "2017-10-25T00:52:19.611Z", "1.6.0": "2018-03-20T11:46:35.365Z", "1.7.0": "2018-04-07T21:38:05.389Z", "1.7.1": "2018-04-12T07:42:57.082Z", "1.8.0": "2018-06-15T20:37:11.949Z", "1.8.1": "2018-06-17T18:27:39.698Z", "1.9.0": "2018-12-17T08:16:44.617Z", "1.10.0": "2019-01-21T21:04:18.650Z", "1.10.1": "2019-04-24T06:38:07.685Z", "1.11.0": "2019-05-15T19:00:11.276Z", "1.11.1": "2019-06-03T19:06:26.312Z", "1.12.0": "2019-08-01T06:36:57.123Z", "1.12.1": "2019-11-22T05:48:34.250Z", "1.12.2": "2019-11-22T08:13:54.630Z", "1.13.0": "2019-11-25T23:53:17.426Z", "1.13.1": "2019-11-26T19:37:25.958Z", "1.12.3": "2019-11-26T22:55:43.885Z", "1.14.0": "2019-12-18T00:27:21.745Z", "1.14.1": "2019-12-19T04:09:47.566Z", "2.0.0-next.0": "2019-12-23T21:59:35.582Z", "1.14.2": "2020-01-07T01:56:33.705Z", "1.15.0": "2020-01-22T08:23:15.632Z", "2.0.0-next.1": "2020-01-22T16:18:31.746Z", "1.15.1": "2020-02-05T21:30:57.685Z", "1.16.0": "2020-04-15T23:45:18.213Z", "1.16.1": "2020-04-17T21:15:50.634Z", "1.17.0": "2020-04-22T22:57:34.855Z", "1.18.0": "2020-10-19T18:47:47.488Z", "1.18.1": "2020-10-19T20:44:21.594Z", "2.0.0-next.2": "2020-10-21T21:22:18.731Z", "1.19.0": "2020-11-10T23:44:03.504Z", "2.0.0-next.3": "2021-02-11T04:12:10.079Z", "1.20.0": "2021-02-11T14:45:23.911Z", "1.21.0": "2022-01-03T21:08:58.850Z", "1.21.1": "2022-01-21T07:06:00.047Z", "1.22.0": "2022-01-22T19:19:04.418Z", "1.22.1": "2022-06-17T20:55:27.618Z", "2.0.0-next.4": "2022-06-18T15:25:00.057Z", "1.22.2": "2023-04-05T17:24:48.153Z", "1.22.3": "2023-04-14T16:18:02.408Z", "1.22.4": "2023-08-04T23:30:52.078Z", "1.22.5": "2023-09-14T21:02:04.863Z", "1.22.6": "2023-09-15T21:33:00.237Z", "1.22.7": "2023-10-10T20:50:35.711Z", "1.22.8": "2023-10-10T21:37:14.696Z", "2.0.0-next.5": "2023-10-10T23:10:42.975Z", "1.22.9": "2024-12-13T21:13:03.506Z", "1.22.10": "2024-12-19T17:29:11.220Z"}, "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "homepage": "https://github.com/browserify/resolve#readme", "keywords": ["resolve", "require", "node", "module"], "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}], "readme": "# resolve <sup>[![Version Badge][2]][1]</sup>\n\nimplements the [node `require.resolve()` algorithm](https://nodejs.org/api/modules.html#modules_all_together) such that you can `require.resolve()` on behalf of a file asynchronously and synchronously\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![dependency status][5]][6]\n[![dev dependency status][7]][8]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][11]][1]\n\n# example\n\nasynchronously resolve:\n\n```js\nvar resolve = require('resolve/async'); // or, require('resolve')\nresolve('tap', { basedir: __dirname }, function (err, res) {\n    if (err) console.error(err);\n    else console.log(res);\n});\n```\n\n```\n$ node example/async.js\n/home/<USER>/projects/node-resolve/node_modules/tap/lib/main.js\n```\n\nsynchronously resolve:\n\n```js\nvar resolve = require('resolve/sync'); // or, `require('resolve').sync\nvar res = resolve('tap', { basedir: __dirname });\nconsole.log(res);\n```\n\n```\n$ node example/sync.js\n/home/<USER>/projects/node-resolve/node_modules/tap/lib/main.js\n```\n\n# methods\n\n```js\nvar resolve = require('resolve');\nvar async = require('resolve/async');\nvar sync = require('resolve/sync');\n```\n\nFor both the synchronous and asynchronous methods, errors may have any of the following `err.code` values:\n\n- `MODULE_NOT_FOUND`: the given path string (`id`) could not be resolved to a module\n- `INVALID_BASEDIR`: the specified `opts.basedir` doesn't exist, or is not a directory\n- `INVALID_PACKAGE_MAIN`: a `package.json` was encountered with an invalid `main` property (eg. not a string)\n\n## resolve(id, opts={}, cb)\n\nAsynchronously resolve the module path string `id` into `cb(err, res [, pkg])`, where `pkg` (if defined) is the data from `package.json`.\n\noptions are:\n\n* opts.basedir - directory to begin resolving from\n\n* opts.package - `package.json` data applicable to the module being loaded\n\n* opts.extensions - array of file extensions to search in order\n\n* opts.includeCoreModules - set to `false` to exclude node core modules (e.g. `fs`) from the search\n\n* opts.readFile - how to read files asynchronously\n\n* opts.isFile - function to asynchronously test whether a file exists\n\n* opts.isDirectory - function to asynchronously test whether a file exists and is a directory\n\n* opts.realpath - function to asynchronously resolve a potential symlink to its real path\n\n* `opts.readPackage(readFile, pkgfile, cb)` - function to asynchronously read and parse a package.json file\n  * readFile - the passed `opts.readFile` or `fs.readFile` if not specified\n  * pkgfile - path to package.json\n  * cb - callback\n\n* `opts.packageFilter(pkg, pkgfile, dir)` - transform the parsed package.json contents before looking at the \"main\" field\n  * pkg - package data\n  * pkgfile - path to package.json\n  * dir - directory that contains package.json\n\n* `opts.pathFilter(pkg, path, relativePath)` - transform a path within a package\n  * pkg - package data\n  * path - the path being resolved\n  * relativePath - the path relative from the package.json location\n  * returns - a relative path that will be joined from the package.json location\n\n* opts.paths - require.paths array to use if nothing is found on the normal `node_modules` recursive walk (probably don't use this)\n\n  For advanced users, `paths` can also be a `opts.paths(request, start, opts)` function\n    * request - the import specifier being resolved\n    * start - lookup path\n    * getNodeModulesDirs - a thunk (no-argument function) that returns the paths using standard `node_modules` resolution\n    * opts - the resolution options\n\n* `opts.packageIterator(request, start, opts)` - return the list of candidate paths where the packages sources may be found (probably don't use this)\n    * request - the import specifier being resolved\n    * start - lookup path\n    * getPackageCandidates - a thunk (no-argument function) that returns the paths using standard `node_modules` resolution\n    * opts - the resolution options\n\n* opts.moduleDirectory - directory (or directories) in which to recursively look for modules. default: `\"node_modules\"`\n\n* opts.preserveSymlinks - if true, doesn't resolve `basedir` to real path before resolving.\nThis is the way Node resolves dependencies when executed with the [--preserve-symlinks](https://nodejs.org/api/all.html#cli_preserve_symlinks) flag.\n**Note:** this property is currently `true` by default but it will be changed to\n`false` in the next major version because *Node's resolution algorithm does not preserve symlinks by default*.\n\ndefault `opts` values:\n\n```js\n{\n    paths: [],\n    basedir: __dirname,\n    extensions: ['.js'],\n    includeCoreModules: true,\n    readFile: fs.readFile,\n    isFile: function isFile(file, cb) {\n        fs.stat(file, function (err, stat) {\n            if (!err) {\n                return cb(null, stat.isFile() || stat.isFIFO());\n            }\n            if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n            return cb(err);\n        });\n    },\n    isDirectory: function isDirectory(dir, cb) {\n        fs.stat(dir, function (err, stat) {\n            if (!err) {\n                return cb(null, stat.isDirectory());\n            }\n            if (err.code === 'ENOENT' || err.code === 'ENOTDIR') return cb(null, false);\n            return cb(err);\n        });\n    },\n    realpath: function realpath(file, cb) {\n        var realpath = typeof fs.realpath.native === 'function' ? fs.realpath.native : fs.realpath;\n        realpath(file, function (realPathErr, realPath) {\n            if (realPathErr && realPathErr.code !== 'ENOENT') cb(realPathErr);\n            else cb(null, realPathErr ? file : realPath);\n        });\n    },\n    readPackage: function defaultReadPackage(readFile, pkgfile, cb) {\n        readFile(pkgfile, function (readFileErr, body) {\n            if (readFileErr) cb(readFileErr);\n            else {\n                try {\n                    var pkg = JSON.parse(body);\n                    cb(null, pkg);\n                } catch (jsonErr) {\n                    cb(null);\n                }\n            }\n        });\n    },\n    moduleDirectory: 'node_modules',\n    preserveSymlinks: true\n}\n```\n\n## resolve.sync(id, opts)\n\nSynchronously resolve the module path string `id`, returning the result and\nthrowing an error when `id` can't be resolved.\n\noptions are:\n\n* opts.basedir - directory to begin resolving from\n\n* opts.extensions - array of file extensions to search in order\n\n* opts.includeCoreModules - set to `false` to exclude node core modules (e.g. `fs`) from the search\n\n* opts.readFileSync - how to read files synchronously\n\n* opts.isFile - function to synchronously test whether a file exists\n\n* opts.isDirectory - function to synchronously test whether a file exists and is a directory\n\n* opts.realpathSync - function to synchronously resolve a potential symlink to its real path\n\n* `opts.readPackageSync(readFileSync, pkgfile)` - function to synchronously read and parse a package.json file\n  * readFileSync - the passed `opts.readFileSync` or `fs.readFileSync` if not specified\n  * pkgfile - path to package.json\n\n* `opts.packageFilter(pkg, dir)` - transform the parsed package.json contents before looking at the \"main\" field\n  * pkg - package data\n  * dir - directory that contains package.json (Note: the second argument will change to \"pkgfile\" in v2)\n\n* `opts.pathFilter(pkg, path, relativePath)` - transform a path within a package\n  * pkg - package data\n  * path - the path being resolved\n  * relativePath - the path relative from the package.json location\n  * returns - a relative path that will be joined from the package.json location\n\n* opts.paths - require.paths array to use if nothing is found on the normal `node_modules` recursive walk (probably don't use this)\n\n  For advanced users, `paths` can also be a `opts.paths(request, start, opts)` function\n    * request - the import specifier being resolved\n    * start - lookup path\n    * getNodeModulesDirs - a thunk (no-argument function) that returns the paths using standard `node_modules` resolution\n    * opts - the resolution options\n\n* `opts.packageIterator(request, start, opts)` - return the list of candidate paths where the packages sources may be found (probably don't use this)\n    * request - the import specifier being resolved\n    * start - lookup path\n    * getPackageCandidates - a thunk (no-argument function) that returns the paths using standard `node_modules` resolution\n    * opts - the resolution options\n\n* opts.moduleDirectory - directory (or directories) in which to recursively look for modules. default: `\"node_modules\"`\n\n* opts.preserveSymlinks - if true, doesn't resolve `basedir` to real path before resolving.\nThis is the way Node resolves dependencies when executed with the [--preserve-symlinks](https://nodejs.org/api/all.html#cli_preserve_symlinks) flag.\n**Note:** this property is currently `true` by default but it will be changed to\n`false` in the next major version because *Node's resolution algorithm does not preserve symlinks by default*.\n\ndefault `opts` values:\n\n```js\n{\n    paths: [],\n    basedir: __dirname,\n    extensions: ['.js'],\n    includeCoreModules: true,\n    readFileSync: fs.readFileSync,\n    isFile: function isFile(file) {\n        try {\n            var stat = fs.statSync(file);\n        } catch (e) {\n            if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n            throw e;\n        }\n        return stat.isFile() || stat.isFIFO();\n    },\n    isDirectory: function isDirectory(dir) {\n        try {\n            var stat = fs.statSync(dir);\n        } catch (e) {\n            if (e && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) return false;\n            throw e;\n        }\n        return stat.isDirectory();\n    },\n    realpathSync: function realpathSync(file) {\n        try {\n            var realpath = typeof fs.realpathSync.native === 'function' ? fs.realpathSync.native : fs.realpathSync;\n            return realpath(file);\n        } catch (realPathErr) {\n            if (realPathErr.code !== 'ENOENT') {\n                throw realPathErr;\n            }\n        }\n        return file;\n    },\n    readPackageSync: function defaultReadPackageSync(readFileSync, pkgfile) {\n        var body = readFileSync(pkgfile);\n        try {\n            var pkg = JSON.parse(body);\n            return pkg;\n        } catch (jsonErr) {}\n    },\n    moduleDirectory: 'node_modules',\n    preserveSymlinks: true\n}\n```\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```sh\nnpm install resolve\n```\n\n# license\n\nMIT\n\n[1]: https://npmjs.org/package/resolve\n[2]: https://versionbadg.es/browserify/resolve.svg\n[5]: https://david-dm.org/browserify/resolve.svg\n[6]: https://david-dm.org/browserify/resolve\n[7]: https://david-dm.org/browserify/resolve/dev-status.svg\n[8]: https://david-dm.org/browserify/resolve#info=devDependencies\n[11]: https://nodei.co/npm/resolve.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/resolve.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/resolve.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=resolve\n[codecov-image]: https://codecov.io/gh/browserify/resolve/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/browserify/resolve/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/browserify/resolve\n[actions-url]: https://github.com/browserify/resolve/actions\n", "readmeFilename": "readme.markdown", "users": {"dmxl": true, "j.su": true, "jrop": true, "dralc": true, "pftom": true, "daizch": true, "ierceg": true, "dyakovk": true, "wenbing": true, "gurunate": true, "jprempeh": true, "kehanshi": true, "nraibaud": true, "pddivine": true, "qddegtya": true, "seldszar": true, "yuqinweb": true, "fgribreau": true, "jstrimpel": true, "wukaidong": true, "cognivator": true, "leizongmin": true, "simplyianm": true, "ahmed-dinar": true, "flumpus-dev": true, "silentcloud": true, "windhamdavid": true, "chrisdickinson": true, "shanewholloway": true, "willwolffmyren": true}}