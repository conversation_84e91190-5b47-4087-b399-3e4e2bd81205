{"name": "ajv-keywords", "dist-tags": {"latest": "5.1.0", "beta": "5.0.0-beta.1"}, "versions": {"0.1.0": {"name": "ajv-keywords", "version": "0.1.0", "devDependencies": {"ajv": "^4.1.2", "chai": "^3.5.0", "istanbul": "^0.4.3", "mocha": "^2.5.3"}, "dist": {"shasum": "3270be5a388fc73f3427e88c5c552f0bf161cdab", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-0.1.0.tgz", "integrity": "sha512-U6u+XsUZ8vzo7je3O7SbugkpWWbEANGNRAOlsHAgO/O3zHJK9lsrDkOEL84PRp2gFIz5htDsAHCeymF27F/KmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+Hs+djdoXAlrnzxBSOZm9w0BP5xYziwEEtWQcb5E+YwIgDvpn9ZuGJZMrIFqZd9rTEzA+9rAAI3MXRrVcDJQ9Baw="}]}}, "0.1.1": {"name": "ajv-keywords", "version": "0.1.1", "devDependencies": {"ajv": "^4.1.2", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3"}, "dist": {"shasum": "d90d08fd88b48e3d81c8e7a29fa6761545796d7f", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-0.1.1.tgz", "integrity": "sha512-K7WRAtr0Ux6eDzFpQO7iDAjHhuEZTdukR697NSbUhFhONtHhqQPUkOgs9nbJSUQ2nQkTj80G0h/ht2rt/YzjiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIIOwc1TGj42Bu37FNMPowJ3MPrVOAPcV/O9aucAoaRQIhAIyPXwSMhwYnsvi0J08tJc1d5ynDEqcZ2SEimXLnxXej"}]}}, "0.2.0": {"name": "ajv-keywords", "version": "0.2.0", "devDependencies": {"ajv": "^4.1.2", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "dist": {"shasum": "0f2660200504451e90c5a8d9d8fbff45844a7ec9", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-0.2.0.tgz", "integrity": "sha512-9JA5nZLp2oWgSFbxBGqistzjXYb67oGqWbEApbboHulAWsn0snY5hk4D/gM/OmInk8enPhJY1QNF1GcUtbl9dg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcHSeKkTQ7cPVZBMTieJJt7VRRvpKX4UJtyqZzAUyVswIhAPAtz3aiKCWuY0XfhXBBnL2iwcAM3zUtQXQ0kMyuGvIX"}]}}, "1.0.0": {"name": "ajv-keywords", "version": "1.0.0", "devDependencies": {"ajv": "^4.6.1", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "peerDependencies": {"ajv": ">=4.2.0"}, "dist": {"shasum": "3dc461b3ef08f87242657419b062369bd758dd8d", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.0.0.tgz", "integrity": "sha512-mUBcLUJOErLcFwdxDHEVOtsgfPAqIzhzzZQBATm7ueRjlktmCWEAIKiacGOYCTVho9OVwFQCJQ0gW721RQCPzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo3HRbqQLz1y8TWdnZwDvW87191LkZuV2wpdPMVPLs0gIgZyRr8Vo3NF3j+ZSNFmZKOeAiV9ahw0lShv352Vcl9jw="}]}}, "1.1.0": {"name": "ajv-keywords", "version": "1.1.0", "devDependencies": {"ajv": "^4.6.1", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "peerDependencies": {"ajv": ">=4.2.0"}, "dist": {"shasum": "9ec1600c61080ab32140fceacb09163c2ca41e5b", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.1.0.tgz", "integrity": "sha512-IB0NB4NqiGmoGmERVdiyV3scWTbJ54Vh92yJryKW9J9BZ/J4YAlvujqcWiL88k2QeLfiuZVmCypnFMMTjZo5qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB1HFSXbYoTTvWHkr7vt0Gm7GjQ1xu6PjoD0NTdxSuTIAiAuoVeQSGHSUorD8D53tZGR3luoDXArxNwG0ZAAVjutag=="}]}}, "1.1.1": {"name": "ajv-keywords", "version": "1.1.1", "devDependencies": {"ajv": "^4.7.4", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^2.11.1", "istanbul": "^0.4.3", "mocha": "^2.5.3", "pre-commit": "^1.1.3"}, "peerDependencies": {"ajv": ">=4.2.0"}, "dist": {"shasum": "02550bc605a3e576041565628af972e06c549d50", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.1.1.tgz", "integrity": "sha512-tE/AzlA++Yh5NVn8FXoyZO/HUBfqUbMTpDSAanIYMQy54bQenQCYiE4d+a9CIEUsS2xTphyeSswkaodj0hekLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYPNF1UoEsQmwsj/SJvqfoVP4jJWtynmK5oFUU48bkUwIhAMpE51rvlryq6N+wsMBwQtqFIAU2uSOU6muaUWh3XYD0"}]}}, "1.2.0": {"name": "ajv-keywords", "version": "1.2.0", "devDependencies": {"ajv": "^4.9.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "eslint": "^3.6.0", "istanbul": "^0.4.3", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.9.0"}, "dist": {"shasum": "676c4f087bfe1e8b12dca6fda2f3c74f417b099c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.2.0.tgz", "integrity": "sha512-3L44bBuV9JpvY7Tildrc6mdwD1LtAoFz7cgFEoUiMNk7/xEXKT7JgjW/u2bkgBb1JVIGM04n53a2gRaV2r6E4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAvh3Y0myaHvi0O/WlVnV7XEdS60n9aSrnDKbRVBXTUIAiEA75pWq0MWziNkLJI21b1Zclw9wVYQVfK+UlxjZz7gwdg="}]}}, "1.2.1": {"name": "ajv-keywords", "version": "1.2.1", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.10.0"}, "dist": {"shasum": "2e1cbda5f82b2466f9fbcc8c6d6986d8b44df27e", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.2.1.tgz", "integrity": "sha512-fzIW24GBrnWKE1WzuHiZ16e1o6DY5d/VgdBAmwIxAyJB9kOUKjIJ6XowIBz8v6ETvwO36UYtcTUC60JQch6JHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWKe87BBGucd50dSz9BUv2o9w2kUA4apgKPLB61Z71nwIgCiehf6QcQTHEO+nfzfib3wE31vHfPTZNJG+2CZ5PPfo="}]}}, "1.3.0": {"name": "ajv-keywords", "version": "1.3.0", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.10.0"}, "dist": {"shasum": "b2dbcdb32ce40b7a64ce5bc6e4ec9b0a918b455a", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.3.0.tgz", "integrity": "sha512-dZig+w5scKjitVeBDFJGDrE93itaivBQYhjvAxRowm9Z+ZxamaNHZNtxwoqenYhrBXwL878KFUmhbGShR4jAkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxBN9K5xDUff71lBR+DfbiU1q9xZnFPM5dp8ljwuYmvQIhAMUzq6gO8uhgCQZnHevtn4Y+YXCrCeTR1o5etn1Q/mEq"}]}}, "1.4.0": {"name": "ajv-keywords", "version": "1.4.0", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.10.0"}, "dist": {"shasum": "87db6a428bac4a5057a772fa83c6c22b6ec2768e", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.4.0.tgz", "integrity": "sha512-vpx09L9MuK3+NyCJq6HiR08I6K38bOvJYRhI+3Q7+83UyH9b3ZPBwensfeSefZZuhcwi56geMthfo9I+tIK5FQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDISX95IL5inVfuTAScpGxeXC68wt92SwGyU1rZRF0atwIgCoVkg4Y1LKu2m2BoB7qTQMqGAvvXtkKvi6YiQmcM7J0="}]}}, "1.4.1": {"name": "ajv-keywords", "version": "1.4.1", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.10.0"}, "dist": {"shasum": "f080e635e230baae26537ce727f260ae62b43802", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.4.1.tgz", "integrity": "sha512-Nzs7RomP93A2i93P0Nlp109zGgrPdlG4icdGfplMYMu9nxrBvaygy6A5Og8+zx5tU7xFkXv7NqGzYUVE+yo6lg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCv1PLbos/pT9Nu4JMNdIf65fR8LDFTa5kSjtNzYwH8xwIgElBl7pLefQo9+hNQ6dtZVr4JRKpXjCF0xSqSF7Qcm88="}]}}, "1.5.0": {"name": "ajv-keywords", "version": "1.5.0", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.10.0"}, "dist": {"shasum": "c11e6859eafff83e0dafc416929472eca946aa2c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.0.tgz", "integrity": "sha512-VpV3NY65ZMIwYNX59aIygyx4z89VVMGCxPqAYbvAEZKMivVbZFpWwbeomRJNw8pRkQk66Mg8o0/d8N82bs8QKA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBx8/hrzKOWDBoAhW//Hv1oJVGutINSBWw6hJ4h7ZMVfAiEAqSo2BRRRz6OYOcAMSkeAq4jVfoWJHkYUI3mWezRb+Ls="}]}}, "2.0.0-beta.0": {"name": "ajv-keywords", "version": "2.0.0-beta.0", "devDependencies": {"ajv": "^5.0.1-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.1-beta.0"}, "dist": {"shasum": "913711c5efea0ce53af27bea199873a3e4cf17bd", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0-beta.0.tgz", "integrity": "sha512-Y+i0ADj178I1Rs0op3tXKe+CC1kJGHcft1Pb7OYNTRwKLLgUSn8wJ9gjLfEc9stVsIoWijM39O5yG0Odw7DBLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5H8mIT1e53qwWtT4FLQT5meHAQ/zKjovgX//6bxEjhQIhANUdZjA9oImrrDhwyG97YALLSXM0jc1jsHLJKFPRLDRP"}]}}, "2.0.0-beta.1": {"name": "ajv-keywords", "version": "2.0.0-beta.1", "devDependencies": {"ajv": "^5.0.1-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.1-beta.0"}, "dist": {"shasum": "30c7a6720aca04af02012afd06ed626339c79de1", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0-beta.1.tgz", "integrity": "sha512-sKl7nJx8blRcRcPfHz/4n1v2QhCMDYAFrkh+OX5kwXR1tdHxHMwNtSErAh7mA8qKLZbt6yZYPnoHPOdLfUNS1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDlfU9g8YbicysvY0a9naDTlH0ZBvyRNLhM+PaKpuKKCAiEAsFehbRx7wkGYgRc7hVgbpRCKABFt3NjYW+rjqH+d3KY="}]}}, "1.5.1": {"name": "ajv-keywords", "version": "1.5.1", "devDependencies": {"ajv": "^4.10.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.2.1", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=4.10.0"}, "dist": {"shasum": "314dd0a4b3368fad3dfcdc54ede6171b886daf3c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-1.5.1.tgz", "integrity": "sha512-vuBv+fm2s6cqUyey2A7qYcvsik+GMDJsw8BARP2sDE76cqmaZVarsvHf7Vx6VJ0Xk8gLl+u3MoAPf6gKzJefeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfYecWZCvVTwhYy7pMYI5WiPtCnTMxGZtq+zCnWRfJSwIgZJXH+9GqIH+CHHXcAOVy647QbK6S4fdgz8gufa+vAJ4="}]}}, "2.0.0-beta.2": {"name": "ajv-keywords", "version": "2.0.0-beta.2", "devDependencies": {"ajv": "^5.0.1-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.1-beta.0"}, "dist": {"shasum": "42ee1ed41c251b5aa4a66f5bf5a2b6a130054ae9", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0-beta.2.tgz", "integrity": "sha512-PtUCcaVUFeGVs5I6xEUPl8oS9quz2yNzoB3ATTn3ix7GYKwR2w3UWbkirMdsKljex7V1H9PTQoQ9bYnNkfRMmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB9R3ehJLHW4lvGhhHF1a70IjoikSXvBOw9CKPzYgcuIAiEA3vQnTYAnDNYnfBBJsLDg0URCNpYK5elnfyvtIX4tMyY="}]}}, "2.0.1-beta.0": {"name": "ajv-keywords", "version": "2.0.1-beta.0", "devDependencies": {"ajv": "^5.0.2-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.2-beta.0"}, "dist": {"shasum": "aa242a131ae362b8bf72ff9ad07b24d0918cdfab", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.1-beta.0.tgz", "integrity": "sha512-vjFnZgUC0klJeUJ/FvIB9eA0RYlh18+AH+2mUHH/ZMF5mzxGdZpS/93JVvlVr447IicaQZPADVRv60UdofUXyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICepThugSItbspELtrTRHjkOv91cTcGntdxg83fOMSqRAiEAgpD4i3PjsT1sPrxYxRb+xwlO3grOJ+pBu+353/kw7J8="}]}}, "2.0.1-beta.1": {"name": "ajv-keywords", "version": "2.0.1-beta.1", "devDependencies": {"ajv": "^5.0.3-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.3-beta.0"}, "dist": {"shasum": "f724f5495a1c71b09d1772560df8a4c5a4d018a5", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.1-beta.1.tgz", "integrity": "sha512-C7IqPzkKAce5JmYTfh2gGXktbigAMJZXc5qm7/yGg8FYkOPGd2uuxtcf/nRneIWx8IPb85qS4Sz+71tltzJ9LA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGxrGje5NMAlch5z1jF0Yw8vr5VYwLMtmQA590rBI/LhAiA2PqfC3wjiWHq/Ja8NP3vRXCHFxolH5MMMLVTMmD/qIA=="}]}}, "2.0.1-beta.2": {"name": "ajv-keywords", "version": "2.0.1-beta.2", "devDependencies": {"ajv": "^5.0.4-beta.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.4-beta.0"}, "dist": {"shasum": "b48f36d63e9334c5045bafde090db006328a0972", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.1-beta.2.tgz", "integrity": "sha512-ROrphaG6XepvpZDqHy0zwZVXBR+QZ7hsFaa9Vlg6fYHmp8kKsaD2iLENu72jDbWkNNYkZ8g5c+gqoZ1j7hYaCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQxstHfFkq/P8SrkASg52dC1xKe8vVs5XmupD8EIEmVAIhAL4Uz3Rjfy/7KcXJq77xuTMAtX+sgExL9qZCiZ/ki081"}]}}, "2.0.0": {"name": "ajv-keywords", "version": "2.0.0", "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.2.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.0"}, "dist": {"shasum": "a37d02f845b6f52569804164270b24cb6c6cee61", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.0.0.tgz", "integrity": "sha512-oRMPh0lQFsBYDa5/srclbFUxEUtflMIz1Evpx/gSEACwBs+bDCRcEtrgTOKYU5PGSk6qbpJFcxkqilDPVM1lcA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFE/6hOzA2JaQynPXUKzMChe3azgDyT+rjSK9yoNxMEzAiEAhYw0WAIkoiVys76zYE1kQGl3PbhHFyHd3l9jCfKSc7Y="}]}}, "2.1.0": {"name": "ajv-keywords", "version": "2.1.0", "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.3.0", "chai": "^3.5.0", "coveralls": "^2.11.9", "dot": "^1.1.1", "eslint": "^3.6.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.6.4", "json-schema-test": "^1.3.0", "mocha": "^3.0.2", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.0"}, "dist": {"shasum": "a296e17f7bfae7c1ce4f7e0de53d29cb32162df0", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.0.tgz", "integrity": "sha512-UVqQ2fa3ELt80oJ/QctN+25n/ccCQf+YXZxYyj2nO1wiIZ58poVAuWpuIkQ//RUqNr/nWK4Byqflyn1omwXlyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8TGMoGEMK7RpubL9UN+4GDzgvlPmQr8OcuWteuo4I0gIhALmrG9HE3XWPB94adJbyAeL32SOfuLM4ip7ycT+i++K8"}]}}, "2.1.1": {"name": "ajv-keywords", "version": "2.1.1", "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^1.3.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": "^5.0.0"}, "dist": {"shasum": "617997fc5f60576894c435f940d819e135b80762", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-2.1.1.tgz", "integrity": "sha512-ZFztHzVRdGLAzJmpUT9LNFLe1YiVOEylcaNpEutM26PVTCtOD919IMfD01CgbRouB42Dd9atjx1HseC15DgOZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEAt1qnFoY4JjsYrbwwEKPfNuixYZQGKI1Kcuz4Pzc2wIgFY0lyHdPDGQmzjk6ycMMYx1i3/uBNiK6QiWu0/DALjk="}]}}, "3.0.0-beta.0": {"name": "ajv-keywords", "version": "3.0.0-beta.0", "devDependencies": {"ajv": "^5.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^1.3.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": ">=5.0.0"}, "dist": {"shasum": "0acee9de32190b79a2fea9a0f2345838d4752525", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.0.0-beta.0.tgz", "integrity": "sha512-9FKPiAOvGv9fbMgdF6A35rFgyn9b7bXBkgNk0vyKpk8Y7apGo2uC9wOymF2joYsafw9QK7OpHUjabR+Q/35f/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0uDveEF1bMUmysml8M0qJeH37kd33Py4W2diG6k99CwIhANDbKzMVzE0pO7oXypfgxtkEeKa649XJ1CAVQyMWtuFG"}]}}, "3.0.0": {"name": "ajv-keywords", "version": "3.0.0", "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": "^6.0.0"}, "dist": {"integrity": "sha512-uMRktPTgqPL+jx4aRg29iv9+jNPDH3NGw4tn/dKONgwd53XPdmZe433n4eyjsj92v0Koe0qqHwAodZCzIriwrQ==", "shasum": "d1c2d845e2664dd3b95551d0cedc7675d6d1cec3", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfP7j46tqBazJ6qSfdjMpFK05K9nsw0SII1k086z7NLAiEAkKfq+pSp9lFfTuGPA0OXoSk/ztRSSTiUUiHBL6f+Xoc="}]}}, "3.1.0": {"name": "ajv-keywords", "version": "3.1.0", "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^4.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": "^6.0.0"}, "dist": {"shasum": "ac2b27939c543e95d2c06e7f7f5c27be4aa543be", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.1.0.tgz", "integrity": "sha512-CRpBPN8yJY/ySBYc93INtFdQNctYjxlCvOmB8zRKEaIGk4fvawQiNAGiPOv3eWJ6NBmotfTJi/06TXfEKw0hVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6bqvZmdg9OPGz2BMF4ijaFvKOYdKPxDnjrbUxCtus+AiA/WW7xxn8GDTyZ85VnHu6yBzEEPAMT9/MfW+qdZv+3DQ=="}]}}, "3.2.0": {"name": "ajv-keywords", "version": "3.2.0", "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^4.9.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^5.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": "^6.0.0"}, "dist": {"shasum": "e86b819c602cf8821ad637413698f1dec021847a", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.2.0.tgz", "fileCount": 29, "unpackedSize": 65030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5D8gCRA9TVsSAnZWagAA0dgP/3wEqJkIgz8c7N8eQSX+\nV7gTjqtoeHLh8Ne7MJyAlJ7eKabvEVwoOjeqYnVEoh8htsE6IZyHg9TNFFJv\nS3IZCqf77RCNpl/rWDDyHbfbjPv7Cnytiz0RdejwORV6mQkYvzW4Wst0V7it\nIwOwpUg/AiOmU55z9kEKkufSFb7CdTm07pTyG6X+ieHn2Tf94b4wa8jjW7Sp\nxj4x2R7GHlDzCxTrm2LGjJ1mJRky63BLtli6/lUI/wxW4ZYfnxssZFtjz5X8\n9WZAdhnWIcvTTRvRH50MuhtPxvrZw3LxkPVckqPZUYBUmc8VkywJWAJWOSuI\nfPuRrxZ6MTIp0VIrIwsy4+Mg2bTBWHJ26JT6hh7lqPQYNTLGUzzIuYPixvxC\n6CGEzeC0NiojSpXbj+WMHXdSbSe7Wpnx4ghXbc/XE5huJgZK2jmeJHZqTtjq\n/HX68q5zQy+TUbK8eYJJ9bEkQy6tQvCkrYIep837QFcmEUnGutCwenRr9ocf\n6ro37wc+MLuB7hzrEQ09GvbLzC1P48HNi/xMt1pGdyRLg4m+PLt3vwoTJONd\nfBGwxN1a+qOTL7TXCsHL3PLmlYMvxTGzSDW0f0IQVP8XHuqdrjEtcrt4O0ZY\neuS6z4L0rmA/LgZAmuS81t4usbEgwgJE2K30iiFfQhDn2NbGT4jNPXujMDBE\ny6U9\r\n=GXSy\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-UPjC/WMTkkh8SoNBayj3ZGsPLYOelXyEDThWIRymcvSowMhXORI5bBgm/3u2mz5mi50CFUhGsMy88USWUl4txw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgRqY5nYcDZaQaQ4l/0Dvusdh07GYglYdtEN1paGAwqAiEA9FpR8va9MD5RXODB+BmpKisJlHGOy3F+ZbDZHXnABw4="}]}}, "3.3.0": {"name": "ajv-keywords", "version": "3.3.0", "devDependencies": {"ajv": "^6.0.0", "ajv-pack": "^0.3.0", "chai": "^4.0.2", "coveralls": "^3.0.0", "dot": "^1.1.1", "eslint": "^5.0.0", "glob": "^7.1.1", "istanbul": "^0.4.3", "js-beautify": "^1.7.4", "json-schema-test": "^2.0.0", "mocha": "^5.0.0", "pre-commit": "^1.1.3", "uuid": "^3.0.1"}, "peerDependencies": {"ajv": "^6.0.0"}, "dist": {"integrity": "sha512-CMzN9S62ZOO4sA/mJZIO4S++ZM7KFWzH3PPWkveLhy4OZ9i1/VatgwWMD46w/XbGCBy7Ye0gCk+Za6mmyfKK7g==", "shasum": "cb6499da9b83177af8bc1732b2f0a1a1a3aacf8c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.3.0.tgz", "fileCount": 29, "unpackedSize": 66623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTYA3CRA9TVsSAnZWagAA5FwQAID/Xlz4cQ6g73jBf5GR\nL5mp9aBGULDA9k/ArzFrDmAxzNG9kM/TprIFtewGYQf0hOy1FMJxLTN3s9a0\nLX8lzZzl4pKSHC9Vfwhiu/bYFXhzmHTI/3rqqdbBxVmAeBXLNg39Yhffitn7\n0dNrow0cLxd4a+TXj3Hmqq8XfzasSbrvErm98wlsEBgWEj8aMsyfl0LzDjj/\nM2EwuP1Evb/wBJpuTufT24W8uCuHR/SY0EBzku0RucZhjRw4WH+Vi9pgzA19\nCQKwq0R14OlELQ8la7R5f1x4SsAVaKnmnzLAcAG1DXSAJSfgic6PERTJHeP0\nwyRg+oSD6/5lnmNU3Ca+VOB6w6uWx5418QZ5Ph06IdpKJWY2kKcutldHZclv\nCiOB+FTPo4tMhL8gFq8wWLSOo0MeSCLkax9szNAPZMGTzobUiOmCxCLLo1tF\n/hCoxuFfXyy8YpspLrHEFiakpUZ8PO5uZQhUC49le92tK/kiU6DQwTXsSMNp\nwA6D0r/7WSraTdXu1nlaQvoRkkKy8rQVI9a40b6YwntZgI7x1+wxlqdPe5Uf\nKlLBALhy+9sLrfJ6CmsDPt7zWRygece8OvusWYGPZAlIaHouKXo9XzDTQqtj\ni+yovlqVip0kvdojLFleUUKps7Mnz351WD/oOqjnxFlanTr3t+HBQg/3/d0E\n+hRK\r\n=QBp9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICa5l5S/0Q4/ChYBmOHvGHOxu/XjKfwUZ+h+TAZcAzaxAiEAkiWP0a6WeuZlhKVBHbfxYWAYVUY1DzRSSJp837ZIOMo="}]}}, "3.4.0": {"name": "ajv-keywords", "version": "3.4.0", "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^5.0.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^5.2.0", "pre-commit": "^1.1.3", "uuid": "^3.3.2"}, "peerDependencies": {"ajv": "^6.9.1"}, "dist": {"integrity": "sha512-aUjdRFISbuFOl0EIZc+9e4FfZp0bDZgAdOOf30bJmw8VM9v84SHyVyxDfbWxpGYbdZD/9XoKxfHVNmxPkhwyGw==", "shasum": "4b831e7b531415a7cc518cd404e73f6193c6349d", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.4.0.tgz", "fileCount": 31, "unpackedSize": 69839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYEXQCRA9TVsSAnZWagAAQeAP/ja42ryFTjcfufvCZU6y\nviBfm5K7aGpfl1LbpWEwKHs3q8XwE0oqF1cs8Uummz15No2nuP/ZjzNJ9sn0\nAoH8m1JBlplPL8Md34FJJU22sch6mMSTeiVi24vr17jcc8ZE2ADshygMy3bQ\njXhXzw8JV4CoYwZFkuZtbJoOMpy8dB3IChu5QKNiTCu9vJx5sHoG1mvJeuOT\nDFH+beHbr9kmtet7afgB73IByfYsY/yvDkzwTGygVK/OUvun+GglrpieqD1k\n5FAhfEtnRCIcMsM1T8+IPv+Kt4org3zSppgIfAvr36sN1/sDyPTe5bfx5HlN\nTu4+oBMi4fqP4qiJfh0nnR4jRbNR9QG/8fcxI8GbIxHyauq50s/UuG+w8xSz\nJqrLXJjtJgyJf8bfc6k1jkH8TT6/c2c1vKYTdGYqs0Q2lIsjDw6W+zLhQf9W\nCBBu8AWpeAzvBOnWC9W8lE7DXKLAu/cncfBZygjL7EYmGCGJR8wdarYA22+I\n6N35T2CL0xZNrJ1joxSnTLZpy9YO6zA6WCrfo+zdZavsZKWHCXskElOaVLEf\n6w/gL9khv+o1WZGjCs2gDI/m9wl85ZOTm3JyHTwhVPzlvI8Lt6BXMAxokFkm\nTqcasLdYrHqaMYhJLHqGpixUGVm/nvVcjBkFbCg5t/rGArUHbxul9wC9us91\n/aZV\r\n=NIo5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3GiCMpaXApMgqBML3xyXuZUizl2DQuIDZHNDdpK6o5AIhAKwdBPpfv0rC9CsJpCKURO9L0J+7YP7oFxBr3vKn7ZLR"}]}}, "3.4.1": {"name": "ajv-keywords", "version": "3.4.1", "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^6.0.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^6.0.0", "pre-commit": "^1.1.3", "uuid": "^3.3.2"}, "peerDependencies": {"ajv": "^6.9.1"}, "dist": {"integrity": "sha512-RO1ibKvd27e6FEShVFfPALuHI3WjSVNeK5FIsmme/LYRNxjKuNj+Dt7bucLa6NdSv3JcVTyMlm9kGR84z1XpaQ==", "shasum": "ef916e271c64ac12171fd8384eaae6b2345854da", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.4.1.tgz", "fileCount": 32, "unpackedSize": 71883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIPsXCRA9TVsSAnZWagAAaBcQAIJ5ZqQTcNxoI0cVtkbT\n2Zj1e66mkfZ2Bm9k5fCwHcHMQNKqpl/aElfETYkzVZAwOlwQfI1XrxncNWqF\nxwRpYHZDOCLPnanfPOc7jF6wJkspQrN/iE4ghI8BxpGKqjVbYBOtSxd85WEL\nNbR8xXTepBK4A9Wd7SYot3M0rcxrUuMN920r4aAvYQ9WhCSMFtvhFlpFp4Sj\nknc06OKrmSyacSFgijUSYOfzuzumsw1h3GDFChDfKSbOw2lFw0pIEAaK+Pbt\nlK98HEQHrFGByATWSn4l4kmPcMU7gH3zAARJbye1BaIGSGB3ec6a6TMw2t++\nPkuvVK7813GDT1owgMOrsQvwLSAEBHTywLL3IKHFJHz+6Q4Y0x0etdgpXy9H\nETCGQHGyrkb3oEgNCFPNTeU9a0+vDWr8fmXfJI7Rhb3rv43Hu5UyO84gYNtA\n08LnqIG64XqDGen0M64lL11j5Dy1Fkg+H3OxGdUiWhKtj0mFzVi+W+XkVJdX\nv2HTtq543TOaoMrzoPP0H/OgMAiTOCuBzAPAcvRn3dtbDyOsh/LFgWdeeEdL\ny7xSnHlTqxEwHESSp2vDouBV0Xpx8pZGyADF9cbVWs4iE5qIjfyHyXoJTIvN\nGMrdlDrmH56MAiqlhK96Wv6hw3bZsaOVz/tg51fn0Kzy8nTEyDjavXJxrad+\noZ40\r\n=eDCH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXcq3NsMOrqNet+xiqqzjbnN5znJkrcCmMLqJSd+NyuAiAwT/ogRLL1pMlEbwjyLbY9lQlrkhC3EROxUp6SZhVL0g=="}]}}, "3.5.0": {"name": "ajv-keywords", "version": "3.5.0", "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^6.9.1"}, "dist": {"integrity": "sha512-eyoaac3btgU8eJlvh01En8OCKzRqlLe2G5jDsCr3RiE2uLGMEEB1aaGwVVpwR8M95956tGH6R+9edC++OvzaVw==", "shasum": "5c894537098785926d71e696114a53ce768ed773", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.0.tgz", "fileCount": 32, "unpackedSize": 72698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7M8eCRA9TVsSAnZWagAAhEEP/2vHbksny97ge7XgHe+O\naESf3r5hD6GZcfhrCSvjnKbwbiav2Nyjw2SFuEiRkcXSZlX6/HsW6kDlEdG0\nVtCxEzPqYqst/IBGbmpJbRlOaK+SCvtLKgnYgGk8OUbeCi6UNf3Ce8i6OHF1\nnmPlOp1i/L+c6m7tRyKx7o7t+q53XlGkEuG0xXlPZ/WEkpS7Su8CYg8vJd5J\nq02QJOSNW4MK5OQqm15JNf5iMxZEIFfgglfep8FoLFRcd9RZN4fVyXXGUvoP\nI4clIp/5/JKjycse/kuKwQPkMGjGPqANKDVGlUmbnzmLc/mxBs/GHMlDTvsH\nl+AepETZgD8MncMn0DRE3EvbMrd6UnS8YtYOYj2qjD9JIQkjzDCbe9QmYXdt\nLHUoLZ+9o5c5/nFfIqGHESDp7Fud1edxtYGPv1hy8cpsEvFeHQAqcVQ6kwHl\nxEcHaI6ZJ1m8PtqfDMAgwk/OVUqdoPRRXMbV3y2sG5cPxakVVKR6NrPylFfx\nsse11jNBfqPxZGMvwKnCqtq+Llhv0ck/2CD5hW0h2VmcVbh9NNt2MSMQKcmU\n9spZr4Pah5sH+WzSGPhjK0xrP0WIfZu5DoFVm5WfEfsTIqwiXcDT84m/DYJX\nMXHmjLFXuH+oj2PV3PzNEYlcoT+3EwbAoHZvxEZPlI4dvVjJyGwOd7luVe7C\nUTWn\r\n=k9xg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDl45aXxtpVkUK4St+DPiDj7eFdx68Nfb3PUtyOwwuWCAiARiGGdL2kIQfFCtlEcU9cKbNfOnQOLFaM/cOfKIKKytA=="}]}}, "3.5.1": {"name": "ajv-keywords", "version": "3.5.1", "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^6.9.1"}, "dist": {"integrity": "sha512-KWcq3xN8fDjSB+IMoh2VaXVhRI0BBGxoYp3rx7Pkb6z0cFjYR9Q9l4yZqqals0/zsioCmocC5H6UvsGD4MoIBA==", "shasum": "b83ca89c5d42d69031f424cad49aada0236c6957", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.1.tgz", "fileCount": 32, "unpackedSize": 72711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBN+nCRA9TVsSAnZWagAAlo0P/3DPrCEJ0oqskSkuWfQw\nfhVI1qwC4mtiM8/LkpJqIVjnTiFSU53qRq+DAXfw4XkaIPXEYoDvjaxbbdpZ\nROo4bmlqy31X8KCVQ3pV19Hg3+GtMUGDPHoYX6ptvxI8Q1wVx6K8AgRYcdHU\niY9+KcMu8exQs8NMhGLOUMICDgmDquIPDcLQCP55IEhElmkKejN0DL8yJ97m\nxD1Go9TjxMuH5uj5R0f/oq5mO7ZoEEg/bIY4vvnbg6rARENlMsh9hbwmSle2\n/voUCeuR+JyXhWsZO+naIa7Ej/F+OVCWTAu5+w2rby38UQhgrGyPPu7aPuVX\nrX4XVXn76qpMZmJ6EG4d86J34oEmFrcSNGcQrojRN4XY+45KPAkc/JZHzu2H\n4Gz2Y1EgP13f5rdQw2eDLQBoMKSaS26HCfGXD3eNE3C+CXPI3dEzOZmOVkng\ncaFCLpA2MM3rSAnGZCH6cq9eYSTlLPfdATYsUI0VehOHO+a1mULETOOb6+Bg\nWuktkoHTwsN6Sqb8658ngRWzAN9nhyjkvpU7MgN0i3NdgqxmF0x0yj46shxb\nVOKk9ZqIHYtunvTInPVTaOic5/FKqjFmMWBEnlGTFv2YkfFyXQYvi0i7OT13\nuyViWH0ZGNbUcNlDvhXEWUVeUid+t0mHbFnT7Svr2xckkP3E49aRSpH5zUBG\nuVHL\r\n=5yto\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVULGyMDfFVDSad2NaZIX2N9qPe/8o4SWx5rp/u7pgLwIhAOTZEJlIjnC/ymG/zhqpd6v2N8laIwMkxbqHCl3cr13X"}]}}, "3.5.2": {"name": "ajv-keywords", "version": "3.5.2", "devDependencies": {"ajv": "^6.9.1", "ajv-pack": "^0.3.0", "chai": "^4.2.0", "coveralls": "^3.0.2", "dot": "^1.1.1", "eslint": "^7.2.0", "glob": "^7.1.3", "istanbul": "^0.4.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "mocha": "^8.0.1", "pre-commit": "^1.1.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^6.9.1"}, "dist": {"integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "shasum": "31f29da5ab6e00d1c2d329acf7b5929614d5014d", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "fileCount": 33, "unpackedSize": 72887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHuErCRA9TVsSAnZWagAA8ScP/ju99xLCkS/HZ22XR+9e\nOdif6HER8Hv1nt8EoN0Qxw2y68xMw2tJJv8zVHd9mBnSxuV5L4zg4yghB6da\nPjCHhKJK4omTOjEpX/rqqSu3C6c1BZhQoHUHYI9s4AgoUpe9SXng3fcLmvwU\nl0geOryzjQszhfwhAaYFaNwb1c/VeST7usuXdwYz51x+2qDvkbw0fOUyIPSO\nRtYdYnuL1VxFJj2352YY2GiMaqGs2M/4zX+iEgP5L7lZLQqS1G00mH27QzvT\n1+l8B8mY0gFVPkk9VdPxU17zUlwzIx1bri4QjurxOqNarYeThTRUh4U4o41L\ngGzB6D1//PBT+cnIAIRvt5uEmuiMTyGBQdWoy1vaTQ0PTHssko2JZMV0TOu+\ne8n5DY5CygN31cpkKjJ20ByItKjavkRTPHEdexY+KyGkUjxIgnX2Ug+mb4G3\nrNw9TC1IjrMsF2nJv8pQvf4mXSOaf06hq86o93UukhsKq6t4/3XLFRGLFmay\n8TKdPVEA7dd2DF4o1okea92BH6j4IFpv8ZWt8wTV4pIhoOT/sBNOBU+aWMDb\nMU1dkMeFet2cGSa81tLu89BOXSmZq1pwlT0dY/X81Y1u6zfhbw1Oywm9XGhR\nRYPqMm5M4RZ4d2QPoXYZ7uTmX1lwYP09QjvrtK/yLXYCPWJ+B0TLdxbDtTnM\nqvQg\r\n=EI5j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEiuzyQT9Pmj4Y1jO8PIpWyqTLg2PiYZOjyfDgxQDRR3AiBRvMiuAfsK0bxz6NH+qB0oE0cBCpjJ4+VBnGpEuJXeKQ=="}]}}, "4.0.0-beta.0": {"name": "ajv-keywords", "version": "4.0.0-beta.0", "dependencies": {"@types/chai": "^4.2.14"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.2", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0-beta.2"}, "dist": {"integrity": "sha512-+FDLq/nSDmtqixRZ2FQqB431EAxvxBmVoClfwDeSqT4/1IjzoEm4sJSE1KWi2W1C2AQwkfTTPUJuS5NdtFVhlQ==", "shasum": "3080dc66f89b75248afce0a7047c853e96d07e35", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.0.tgz", "fileCount": 160, "unpackedSize": 124885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflIAFCRA9TVsSAnZWagAA5ZsP+gO4MC2fUba/bUCs+J03\n0wV3wxQVkPaV41MO7/60b9UlTrPVRnPQrLF2bGwbzDErAJTrGVislL0rv70x\nur5GfDLwbFZPj1shhfyJfGINfD2PgMzSjhcDBwQ6vGyvk7jKbNY8Ja1QuJOG\nQYtHuquobzD03tw3l/mtNVgm0aYBgEbJMpnTzfAnCqsud4Jk+SFe7bXTo4qw\nrJb/sQEr63aps5YNn/eI2z7JpDjL9QEXDVEACo5auCR/Nn4rjTi5oH9i37Yi\nGGfsuFkcls/mrQC8QOmucwIh7lXL1bkKZ8hkmKTX88Yn/MDD0aiSrcHjXtgs\nrbybKj6XaT4GM0wdyOThsPwlsn9BsbKSXyrHiXlpElo2op/g00kQWyqRbwj4\nJkLL2gez6Stf4vk7o0gI0vEORkfbyC2/40HnFJ4yFGmBDDyK1poT//xVicSi\naP0NM8Dj3Dj6RD6p4xos2FTm1NTM3aPmh2ASTIc75TD7QGKYrKnGgymOOv9d\nrDyIJDQ5tqhtxKFSUSjrEKEmgrr8J7QNwPkHv0XRhl/Kw8+uOPsV/AE3TjA6\ndF7g/V7zTszXZrquaPFKU8QZ0cME3kUJA7RJKcMlN1jkZZuOux4kp8y5zn+/\nOjaHMSSGsR8x8ZipKwC4GMl/MILQRXqcwIbDSjKrJQHSp8zolWr0jJW8BkjU\nbWFF\r\n=MYic\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMLZrWTz2JpABdTaQwcO/W9qKfMfr5Tlug0ofYrtGzaAIhAOZyWchLKbiOw4bf/m8wt+5JOYVqfudrSj0BNINMPPQG"}]}}, "4.0.0-beta.1": {"name": "ajv-keywords", "version": "4.0.0-beta.1", "dependencies": {"@types/chai": "^4.2.14"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.3", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0-beta.3"}, "dist": {"integrity": "sha512-L69r+w17gfiv2OeaJzutSvscG182a2w4vNHSZMgFAxdgG9+jAGPCHTEd6Z9dAMEF1brhYWP3QeNyKUNwMcwoJw==", "shasum": "f6d5e721ec2567221d9489208aa6c99ac319add8", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.1.tgz", "fileCount": 160, "unpackedSize": 125735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfo79PCRA9TVsSAnZWagAA6N4P/i3GoiyMU8/Kxe3oMFNl\nNceRm9fXaBx+ZUIBiHXq17Jp6S5MsniNtj2pPCfVEXlu896gGM7+E7STxzmW\n5ZhyL5ISCJCLV0P6GSRI3jwMyulrbOc7RUzGeJq2COgtlQuY3+dXGEBRfkcK\nED5QM3xYcGLNJiXrK5lI2d56+dvcr0OPDTKi07xDNFSlJZk+bLqHFaD2yXIk\nB+lfsxOo/AxV4S2809V51+tGMsAgXDem0tgPcD8cSkbs3QkF2wZb4Bx3Bop7\nIX3xdaTjaSR3Jp87GXP/7i1Bf6icu2koNT5xkXfNk8SP6wsprtz5G/lRRlmJ\nsNq2n8BJ6PAEIMD/6I4LuX/5LuDd+uM/LVo4Ay5Bw22D1bNOX6DxleDtcPVM\n5AzvhnrD1SHt8jkGtolhh+SXpW6D+v6b4HPuc51/I3sEe/JzYd6Ps7GCfbRi\nFGtDUgkcn/emxVFo0WY3HD+MYg9mKThF/oA6znyqqN0EI6DSd0hiLdesZ3EL\nXzqYKYm0SmHkoa5EpTSTV62752VMacJ8qwCyYEy+TLZexps+ta3NyqHsPsDY\nfMd1nPIArmCRcBZGVn4n5EJoqi7Ou5nwbFVMmTruAvjbspEfNTUf6YzSQFCy\nWeKzUUv7PPfZ9Rf6XeIwQInHFKdzr1rGyRdT4ycGO5D3rAQRotaZHx48LvQx\nVpFY\r\n=JIuY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIgpIAtblK3WKaS6neWNWy6k8Isg/fsfezthY3eCq9MgIhAMvNQoIGeSw2rNI+aSzttdDWpa6lUJAXYkZw1Q7v07iR"}]}}, "4.0.0-beta.2": {"name": "ajv-keywords", "version": "4.0.0-beta.2", "dependencies": {"@types/chai": "^4.2.14"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.6", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0-beta.6"}, "dist": {"integrity": "sha512-XNxBBpjezomZlXKOO4KVu/rrgEHsjEdj+CQNA0ENsLxBbthcoQDQ4o4Rf8xdudCU6iUBsuE8n4izwN5Og7C3xw==", "shasum": "cf60b4fa8e64c39e51e5529f9ce014265554c3ae", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.2.tgz", "fileCount": 159, "unpackedSize": 127056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfs406CRA9TVsSAnZWagAAa8wP/1vkfq+CUaWfiWNzN3TM\nNhL4bjpX0mT+6TQdL1cGZbRF47lKBnYWxA2ZwKoyuIRNu6piFDKnbcWQq9DY\n0AXjAKPo0rCQzAdw7jviE+lUAHLdFvOVfPudKR5XTvi09YdBK3OOoFyFp42D\nwP20joRnFoUdSD5SXSq245h+M4jOnz23ezwl5h/q4uFl+F3zpWrwJWk+DDu/\n/ImKu+aWBnyV45MI/I29xCImErXw4ZiApskFXl8FsMW1cQrmRHyRrJJqpW7Y\nLlLmXQtNBrbS1mywoDaQUSHl9HjB2pJ2uaPQziCLHyt6v/xPLmVDapeiZClI\nm8q31LWSB//uicVunaIkPQwjUZ86nAtwk1d0k+u9PUE00CkfOrObIgShqIbB\nGGwy6AzmV3RX0GRLxuZ/OUdRuMxKV1k5q4Wlxc3a+DFZb/GN8WR5pHeuUgUi\nh+grFyjsazrbsf4YCnm4kgk1NiAOKdv/W7IiQaagS3ygd5CgL/IqJ4/q1INN\nFXhFUfmmkytu0wyVH7uheJspWZApZlCV9YIGCDtvKBem06BQQojbi4P4a4KJ\nKSyRf9R5e152bEepkIRjHRrfwrSRnnK9VvXsh/zivSqUVoBzfvhahUQB7VNS\nNXUHk3TOFfr+rzVZShnC2ZsA9+f91MmxVJDLRZUkuSu2gLveze4ZJYdKiW/5\nTclT\r\n=1Pee\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuI9RCl32QlXXjWcoOvUs0rHdu43qUsWOVjL6V8BqthgIgX/T9SKPjfizBKf2ZoVbFbvx8HP632wF8bfzJTIsKetg="}]}}, "4.0.0-beta.3": {"name": "ajv-keywords", "version": "4.0.0-beta.3", "dependencies": {"@types/chai": "^4.2.14"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-beta.8", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "dot": "^1.1.1", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "glob": "^7.1.3", "husky": "^4.3.0", "jest": "^26.5.3", "js-beautify": "^1.8.9", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0-beta.8"}, "dist": {"integrity": "sha512-aEKdUuq8Ww5l7Ialc74xA5Sc5zAT7FOyIaMh/7fd0mKmfc9mV4f/B1T4DNxpROFVdbVixjB73l4M61RkS8Jm0w==", "shasum": "3ee3e3005e081bc2b38d31f52d600c456e234d57", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-beta.3.tgz", "fileCount": 159, "unpackedSize": 129091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfw/5UCRA9TVsSAnZWagAAlSAP/3blkO9k/Rlq2vmZIjWS\n6s/MkzuDM8u6z1/WsbRr/us0hCJKlJXzRw5r5b6GKed9ewe2v28ycqpgoYSL\natgwh+AYNf0Om0g3b9sdOyhLrFyQ+kbS5FyrBYsAS7YsXkCp8d20x6svVFwH\nzT8AQVLWow6vHl/FRqM6zccn9xKoBs691/cCfOamcXyHOU3tAcNcmizaN7Ol\n9X7yh4Qr7KCDvx5rose88FlxngO3Z3DuP+yAB8kmag8jF9iP40pvUjTlxfBa\nSgB2SqWsvK3lESr3CQB69yA/zeLSnNDVa0KXyJiEqNbuC0v/PfQl/xK8xN5n\nAAZv1EIU8UHt1wPhoXTOrZBUrWNL7/Y4qQ+gKpxrj7Br/N2qYmV6B4xYQnK6\ntUB2iqwq9SVLTPjAHyWycR5F3/nZ6gjphgGA51fIh4Yw+fsQl3PvWi3C04Cx\nV7ePLaTghAWKwTy+z7HrJYC5Zb14VIp+Veecsr9x8hx2rDzZmPCB8EkfWkNF\nmcq0rVl6uYe7KHYch8JRLtZ2jSEH8a3qrFfN5iZCpweY9eqhZz7a59eJvgSN\nbrZoFf/uS0TBb6i/zrp+CQACNWQPVYSdaXpke/sr+TH8PMyHAvqvmEfuXnK1\nBRK0yDwhNu27m15Xo1fUPB5BTvqGqykc+Zxah0DXzkfNPbmoQFODD3pajGEm\ngyS0\r\n=k0wu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0l361/aGK8dvK1T+DLRPSSuUL8hj665W9Gb5MPUQ2qgIgKY0taoVtAFxVH8tzuwWEThQwWFi9P3GK2McgBHd+cSc="}]}}, "4.0.0-rc.0": {"name": "ajv-keywords", "version": "4.0.0-rc.0", "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0-rc.1", "ajv-formats": "^0.3.4", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^6.13.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0-rc.1"}, "dist": {"integrity": "sha512-MTXCXzH1J/Iy2Fo0c/OoAIiScuaLak7vzm70Ky1Sfz9G2V2DwlwnoJv0qHQZu0ArtTu7kUD/CDMY0yBgJrFEMw==", "shasum": "b69f11ba027457811857088592653d3a1745ab1e", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0-rc.0.tgz", "fileCount": 159, "unpackedSize": 130581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0zRxCRA9TVsSAnZWagAAE9oP/0GrQ9r3SImR8Ea0+d5C\nWWr/4BIDk6w3vOPXv78YjIzLjQStKUVrjx5p1xGQGborVSyl6BWTldjrSqOg\n35Cfs4cGXydFJ+vB2ZywiSGWENUA56vyxOZQlEtmu8YOMbQe36Hpna8hQUAU\nuuo6yw8h3emJHB6FyNXrr0jcPNUDKMCf20MLsIh+EQ4C+aUZg1z6LzleSMPL\nN3FQDMCqbGkLCp7ivGLLiZo1nj/+pjrPq+4pxeo9bMfth1l3uYZcCUHo7HnX\nWJCkUneRHNUsMl5gCgFMHTLI7U8uLjYbtG2YrwMY7SLiD1OYSCMrJ2nekf5G\nk02/dlyp6dsiV4zcRCY7B2/zfwXNybrjp8koUG3F4HBlz48Wn2i0mSFP8EsS\nT5qZPECPqZH++HUsgBH8+KGGBJAHy5cuxN1k7dG4qHiuD8epbfAKdbuYC/4f\nNScKUlaQV4J+ivbMjZSCKAZ3YOUOnorK4ffyWT3Mb5FG8dU0T0GLA0u4BKVM\n7G2dbVHpQ5ldFmu6+E/YP1iDV7CSUrtYVEDsSl3KNGHJNDYvwvDBwvwVWyx6\nnn5xUzBc+xUkO22qLgdYD0dsKM8zaiTiFKsp6poZUjHsa3brhoqMkwtyhJug\nQscBfSlboDmP5mPs9BNnfIColvLsgn2tzrrZiSLJ5+J5kmPSDJL9+Fhmp7eL\ndUWg\r\n=TFj1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqEi6Tkr8Q8/0A3I4sJwWbwXXD5aW8JcJGocKFLtt17wIgGz12YZUnbnF+yU8E1XO5l/owQhXPPqkCzig1gArDG4E="}]}}, "4.0.0": {"name": "ajv-keywords", "version": "4.0.0", "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0", "ajv-formats": "^1.5.1", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0"}, "dist": {"integrity": "sha512-baL4pEYniCF5E/5Cj28f1DmPXGGASQIeCFfntY94vJPtrq0fei3iNt/TP5f2IwEH4opCzcOOvL6hKsi2IHaecg==", "shasum": "d0ffb23189d5002b234ad54c1a1b620a5398db58", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.0.tgz", "fileCount": 159, "unpackedSize": 130594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2RLgCRA9TVsSAnZWagAAtd8P/Rjge43ik1zeswMK/kIv\np+eDA0XAnYma4zpBpVEK9MJG8X9vLcRVZKRXVH8oBoGFOfJrwcKlrBp58D7q\nARsI0zHSDv9eG083n3sohEsgW8cY6VpDsOMf2E7uS6U46DKFH51PcUaX+ol+\nhRCrPg4yXJnlJ11BOq5O20Mzu9C7/K1RyU2gzVrsa6evO2/zOTG8s9vu+bD6\nemDMwxEOcC0Hti2eZ9yfBI1G4UEJkqqlNYhbFsYI0sCR9WjiaOp1X4Dur6Rl\ny+c6ZiWLJ/mliA2GOKc96/h52xcTj0Sl2//PPTmzCPlDIBssQuNdsO2HS9y8\nPDtf8dVpXqvss1J1FJelqoABDtR0X2M3qcCUhTWAO3EEi28EIEv1x6Lzpc/m\ngh4qZeWbKlTGXZLrWTJ++cXdKr+jrqqGfeYIV/NNAorTY0sv6JyO7gP5Fy7t\nh2NnBtEm0NsDUyQHPocubhOVWxTIgU9TIVvVPlqSrJFtS/SXc1SV8mu6rOow\n9lqPPm9UXjH3qavkUyBJIgRK+bJ8khhJRx6CaZ1PzcdSD35aI6uNKr9mdMW3\nsUezIeb08IKHh0B/qhj5Gn2baTNqhUpEOCYt7I7SZixLxW8zHK5r0w8VbvWv\nHMvXT9PnjkMLBsp6VspAOcdCzgcnE9lM7fYPly7/cIM90e8ckJspZrbT7Mog\nW25Z\r\n=V4RA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzNZ2/NXVk0pJ32Sk8lDcV08R/Jo96/KJuGIXKflYMBwIgXhq8AlFsGftifaGuxsJHyspyvjMM8TL3L/rq1/VvlGk="}]}}, "5.0.0-beta.0": {"name": "ajv-keywords", "version": "5.0.0-beta.0", "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.0.0-beta.0", "ajv-formats": "^1.5.1", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^8.0.0-beta.0"}, "dist": {"integrity": "sha512-RK9YuOTgWiINFzKqayKBJpOOxeK3nTjFiJTaqZvselMKgs6jRE1Q9zMYdm6dXS1pwXzVy0x6hV1G877wbbpOhA==", "shasum": "f98eac76a08cbafb9300594ddd7e813a16def629", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.0.0-beta.0.tgz", "fileCount": 159, "unpackedSize": 130926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTNIICRA9TVsSAnZWagAAMCEP/ifBiGt3GGEI7lMrXBKc\nQH6qt4GMm9SijHHvVc4EsnBOfSA/VgEa5TJTYNkXEUXoxFPvNPumNGYs+a31\nXRQXl/pzMkYLUAB1aBVEUE//Hz/W+3ITPut7Vnh7zIHCxIVdeSKPTDKvn0p1\n8nF8C/j0s/BoBV2hk9kUEVTGEX8a/5OrOJPmSRcuc9upymI/JPkLSgPUboar\n0ktVFI+PTxaRzsgw5BDNTwOWUuFseL539W1KVLEds+c1n2MFnrqS/46+sAov\nYRkBrhyHJuWSof0sFHBwhesqu8XfGv/qnAc+H3vO1mHJACxD/HBcpUrcAhru\nldPEh0CM1LepjiBUlVfHU/JwxuGXd01l+/CaS/Xf0M0AFN+ERtHQ5b9Qs05J\nqVrd2C7FdbI8LrOK/Z7aJa1llBSU06eddV1sI7UTFtBd/1fTv6RRQgVn+o/9\nX7NeRjZ/eG6IsJSZEL6BwNff6r6Hg/CvG0eAmhmZ+zTWwADmaTyGGMA1i5lO\nBH7nd8wbEOPfOqDopNMOb9Q0kkecFkiL06KQb1XbDVRhdbxw+ykL1XjmQM5R\nMZG3ePCeBD7wJv6Vwo58SjOuU+F0XeaDskni0I8HjM4POQbKQKH69dn9A9AC\nq9hE6jXfkblHopli5WGjmk5fbvA51t+NHlwxnRKLJpKEy4pKPwRRhrLvSfjD\nDYw3\r\n=Bn8C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE/K01mRXXtX2Ps97IqT13daqhWkbfn4EcRrT9iypYQlAiBMuvWDb2gdBISCMVLUWK6M3iXl92a0qlDFd+Fa0r0HMA=="}]}}, "5.0.0-beta.1": {"name": "ajv-keywords", "version": "5.0.0-beta.1", "dependencies": {"fast-deep-equal": "^3.1.3"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.0.0-beta.4", "ajv-formats": "^2.0.0-beta.2", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^8.0.0-beta.4"}, "dist": {"integrity": "sha512-qFMpDpknrsJ1SUF8AV5ucmS8ZmoCz0YXuxIPWUBMhhl3JBAcUpgZuS79LMn6BmHcurLE4z2siHDnoNRTMNTWPA==", "shasum": "6d6505d88fa9a33f1a434e423c7597a13daa18ed", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.0.0-beta.1.tgz", "fileCount": 159, "unpackedSize": 130811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWkAwCRA9TVsSAnZWagAAvHYQAI05MFVjAqRmE1CxF5No\ndyJcrciXp2qG4Mqg7sD6HICh6qowkddrp8MGvov5swyRn2+/SWWiqmZw0+tw\ndxGJisQJ0FCdwjx5B9Sp1X74wqwsSFsew7Az/okwpEMHAwC7XDeXXWmiYP36\nEnF8pWSWvZX4Ig9r10Xavf5O6vB4zBVowY+D2n8WFMBm8rBtGGRqVCbjGBBr\nSPlH1hJw6n+0iR5dQL8OVV3BnlZqlDFummEZ+9XMTgeB/pDqz5vO7O1A3PAE\nXgXYpTNv+M0SU5PSphATGgskZ36IEpDQZZhHBMnkzj0TbWr7CztdvhSrNGrf\nGmeyzLx3usb86i3DLq9C/UzLuNLLiw+Z17W4/eXyZ2Xupm4qVdm9vMtsBNTB\narcHhK8lOn6bxmb/9VTvemoRgNxUAGahlamR+mksRrzO+qyDj9kulshLW0sY\nS9eMI+6/KOFNaMB+Mdnxs+mcUc7kRoVM7QstABAt3ESowm2GBU/ECpSW6VrW\nEhVfdgyiG0puZft9jIK0kJqDlntEYHk5My9lGLg9D0l6f4J2iq1kQMj/SFTg\nVuqvhztyjosg1Dwtt/umrAxRGSKeb0OrhZCI9+tlXq23Ae/E8sxrZtDQSC+W\nBPKBenQqPCLgRaZ2ulnjr/3xRN7jVmn0v3Oc6UaZSwHOp655XYQtB6jHKfca\nuyxp\r\n=67JG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExzP8FOGUjv9PuxEsuSl5eC/PiHDTfZt8l7o2bjOV4dAiADI2tjKlXRDAJXy+Hj/YsH4ZlzmB+xEt0dvzxPXzFlrw=="}]}}, "4.0.1": {"name": "ajv-keywords", "version": "4.0.1", "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^7.0.0", "ajv-formats": "^1.5.1", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.0.3", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^7.0.0"}, "dist": {"integrity": "sha512-vPmwZT6AL4R7kKrjKOMoKgnompJJS8MUgTB7dPBEknSSv4ahKgu046H+bGOH2eMRbAcz5pYz3fwaSwrCGNvuxA==", "shasum": "3fcd1988846b741ec958b2555c96ccecce2b3a59", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-4.0.1.tgz", "fileCount": 159, "unpackedSize": 130915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXDW5CRA9TVsSAnZWagAA9YYP/00wFj752UIBkHePRRAd\nMpNYGtq9qj6dJElU0F2XCVo4iu8slKj3vEC4/mfzmgqRPFdErEpMasEQz4xF\n1MXKD863T0EutqhPddk+23+B6ElW2UmyG50yLuPgF75xFHK7oWQJje8A7b0c\nTWhNL8e0IwDazfa4n8o043e31OHAog5NzezjlkrSGOg1g9RBqtwlQXkl1ky8\nja58g8u6zkNVaw4SoW75VpT2opxt/IVkfgey6Hy8tNmOR6qwINP2FwfvIJh5\nLtRF0+RSomhgt0JDp2MeBBBbl1bBwrPvqobhix9remCvPa+YCE6cTnbLKAKN\nrd3AZxzdCJY7h+XXn1X9xYZ05zDm2QUMLD0E6x5pBbckNxpqCyLKP3TDPVH0\nBEkwrF5aAD4avV/mOvA4iAKw10+y4hCwO/fb91jajJ4FkDkjwfEKNs7egK0B\ndsqsi+Bu8Bg08CTqL7ookH+JJh3re/4134Fjc8Mz2GVgM8vkEBcDztDFKC+S\nEpCbWqfhhSpqdnbSSc5IMpznIFxfoM+Zscm7rjICKiyTDi33VosCvEaRHjyO\nr0rHJKeTn/QKtAMgEXQbLCqNUvWvEkIooI27wETcTYbZPBl69Zb3o2cvLx5U\nsNaLNJ0dSAvsi5vyDv6uMamHawHbSr1BoA4sxHq/gVD+tX6wu5PBiOL5p9CC\nLsMo\r\n=PFBf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJ4iW9YdOXK7vUzhcrc8jl+NYTb3SVPvxdNsDkE4E2nAiEAhwin4AvcXfG/2DzTlC+djt1+v216R2pTmkeTPftNExM="}]}}, "5.0.0": {"name": "ajv-keywords", "version": "5.0.0", "dependencies": {"fast-deep-equal": "^3.1.3"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^14.11.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.0.0", "ajv-formats": "^2.0.0", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^4.3.0", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^10.4.2", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "dist": {"integrity": "sha512-ULd1QMjRoH6JDNUQIfDLrlE+OgZlFaxyYCjzt58uNuUQtKXt8/U+vK/8Ql0gyn/C5mqZzUWtKMqr/4YquvTrWA==", "shasum": "d01b3b21715b2f63d02aa511b82fc6eb3b30083c", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.0.0.tgz", "fileCount": 159, "unpackedSize": 131000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgX6qhCRA9TVsSAnZWagAAWcwP/3doxt6kuGipC+osK0GL\nGhA3tLCqfn6G4ItkWr+/NK5AWqDPQ/vGkhYgQX4BNNb3FU/rFX0a1Txfixkt\n91+WJKM0I4Iffk0B0MkBU6oVB2LZ/JKE1rEHAFtoQrVL8HtuqEw3cKLk8P46\nRUlwJTL4IauQ+vuOVnnhAGigukr08ZpZCFX4N7Sht2I1Xr40VlT/AgeMDYJU\nGJjiFv8tX1ZlmjW39QPA+3zulZpkdRkSYfujJMrSxzlnPZV8OZr+T6po5vxZ\nbcbE+eJR8gu0AAev0fQRRSNz1gwJZwYi6REUummE8vhBI5vCd+zXrAbw4BMq\n6phVUplN0OCgMiUPU1kFejXYy2YWMbEFptQF2QwJxFc6O2lGmLsJl41F8l6R\nO7BUdOfR2gvCNI8asQKSLQ+qfMmjLZ3V8I+VESHD4gvOYUnXL0KD/zKMlwnI\nzwCAqsAyDJ690hgvy8ksrYbrnZr9rvj6jVcZRPzk7Xsaw/GdUo5pnuUhHD1v\nQ/sCz+ZdGQedOVWfyqhr/YV6yzp2TXmE9vzGS/J8+VAKCpJq5KWOo67SRriC\n9En0ji2SDzQ8sj0t9UXBYG5Yor8yw/80xdwJdNsPENhdnyfQoTnWJjN+BPA7\ncBxTyUM+BUuMO1ng4tcq3a53URlpcJck9RUA0IJ0Zbu2Tfaf4R5KqVImvIe6\nqL00\r\n=H9SU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBuPn7vP1NeTh8bw28wqlzfagVjQKSoylqblcjk3dbFsAiEAjk/T3oKII3/14ZU9o0V0ebEpYH7qBT0WON3gJMLv8bc="}]}}, "5.1.0": {"name": "ajv-keywords", "version": "5.1.0", "dependencies": {"fast-deep-equal": "^3.1.3"}, "devDependencies": {"@ajv-validator/config": "^0.2.3", "@types/chai": "^4.2.14", "@types/jest": "^26.0.14", "@types/node": "^16.4.10", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^4.4.1", "@typescript-eslint/parser": "^4.4.1", "ajv": "^8.8.2", "ajv-formats": "^2.0.0", "chai": "^4.2.0", "eslint": "^7.2.0", "eslint-config-prettier": "^7.0.0", "husky": "^7.0.1", "jest": "^26.5.3", "json-schema-test": "^2.0.0", "lint-staged": "^11.1.1", "prettier": "^2.1.2", "ts-jest": "^26.4.1", "typescript": "^4.2.0", "uuid": "^8.1.0"}, "peerDependencies": {"ajv": "^8.8.2"}, "dist": {"integrity": "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==", "shasum": "69d4d385a4733cdbeab44964a1170a88f87f0e16", "tarball": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "fileCount": 159, "unpackedSize": 131806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmqEdCRA9TVsSAnZWagAAeHgQAJcRTbLU28z+qJjSAqt2\nj0KyFJUEu2/LC95IyLZx1GUvx5HrCmOdGvyyqIkM79iS8puffaEwjkZ03OmS\nRDpglaHF/aCf2Rh8Wa8y92+d2js8SPL7KnGEzKXR/V+hoCzjWobo3rtDRkYd\naBTQVFO8uOx9iHtqJY82IkF9xbzOTzG0MG4oRsUA95Ke3btn9B6OTXvZkV1H\nx/bowdTslZ6lYQQViH4N2VDhSgJguRYRRy83BBRQPH4Y+x6h1kaJ6SMLlKlR\nGRbE78aTqZRE8boOSUi72nGYR1dMMfD/bSeOIFmenKRuDSadR+as60enoa53\nBXBLwcJ9vEv2qHPhSZhM9i/OOtw83kkwjyiMZ15HmdspdUZrsNJMjvX3JoPj\nXyFkVi8//9TuxhgxfImitidsAHzoSLUXG8MjtJxoRKSvkuQbRZJ4rREjVtHH\n8BPn/mEzPK1RFGJq3px2+Ifiij/YXkU5HE3wqpeCWwlOqLz6Skg8VImEUFFN\nZOO8yva+jc6JlTaB5Jlay3jYixJTbsKWT//7sxf065bBHNk40tcZv7fuzS8j\n6l1qkhEuqi4+wO0OtVWVT6blZF2oKf6xX8s6HzaNt6rmrkdLX4b47Qrlkprb\naeVbvCbBaUUMrx6020JEpXZXsp65gE5YlEbHKigFOXWiPeRMMzooPISMrWZ7\nypdU\r\n=rGFp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGXa9A2eVjIcA38NFZcv3gJvfFP4As4/M9Lx2EmJxobQIgIKxEh6JHT86f2iX0PPZWWShpMPZ305MroBAqJ8/X55Q="}]}}}, "modified": "2022-06-13T02:34:05.200Z"}