{"_id": "bcryptjs", "_rev": "263-3cc66352b755ef360936970cbaea366d", "name": "bcryptjs", "dist-tags": {"latest": "3.0.2"}, "versions": {"0.7.5": {"name": "bcryptjs", "version": "0.7.5", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@0.7.5", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "6c7a53daa21ff137c9a213588a22b509182bdc90", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.7.5.tgz", "integrity": "sha512-i3vwrq1QTYYX2OjWW/nSF/+8f+x8S8Op7GRjNSPOUepwwe9qciyMmNwaTfQpN0EIPR6sOE+PbmqJ0No+snhhhw==", "signatures": [{"sig": "MEQCIF6TJVjAtwWbuN9X6w3z0Zg6EGEWX7QQ90eA2/szQjJNAiBaFJ/xXbjo/mKo0FJLD74j5cprwyJjTNVfxWQ57cKEow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./bcrypt.min.js", "_from": ".", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "al<PERSON><PERSON>", "build": "preprocess ./src/bcrypt.js ./src > ./bcrypt.js", "compile": "ccjs bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.2.18", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"dojo-test": "0.1.x", "preprocessor": "latest", "closurecompiler": "latest"}}, "0.7.6": {"name": "bcryptjs", "version": "0.7.6", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@0.7.6", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "ba372e36f67f8e1116a599aec8252ebe0a943f97", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.7.6.tgz", "integrity": "sha512-48QfhKxX9LJjEv3ln1V2qozDLhWVRIBdLr0GGpClEpCvDMKOjslxJN6QUKUraJZ0BGkKgRUQqkiP9EhNaDDCzw==", "signatures": [{"sig": "MEUCIDLuVtOoP5gAdBjzzQWjStIW8PGjV5f/fn8EHl9AnZRLAiEA31JG+dK9Oge3/vNVQY2g0aJ7aVXINyzCTphVN4/eL7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./bcrypt.min.js", "_from": ".", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "al<PERSON><PERSON>", "build": "preprocess ./src/bcrypt.js ./src > ./bcrypt.js", "compile": "ccjs bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.2.18", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"dojo-test": "0.1.x", "preprocessor": "latest", "closurecompiler": "latest"}}, "0.7.7": {"name": "bcryptjs", "version": "0.7.7", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@0.7.7", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "685515075d6459ae5d90b468df8afff78e5e1332", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.7.7.tgz", "integrity": "sha512-/SR/8uLOwqFV56RRtn1HBv2zZSUEWZkkXqJ+k/BXNC1urbGIm77gO3MPEejGmszKa9Ov0RyIt3WDtKlDvFHt0w==", "signatures": [{"sig": "MEQCIHoYDmH4mf/N08vjnP8rGSbFG0gw5WbsoDy+ynhUhGEmAiBJBXMmUoxNAlM2vxE9nDgp7LmMCXGFC7x7eB50wTtM4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./bcrypt.min.js", "_from": ".", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "testjs", "build": "preprocess ./src/bcrypt.js ./src > ./bcrypt.js", "compile": "ccjs bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.2.18", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "0.7.8": {"name": "bcryptjs", "version": "0.7.8", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@0.7.8", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "24c74ef09c720810e78ad7d3d12b9978568581d4", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.7.8.tgz", "integrity": "sha512-eORMMEBBpThMy7HagX7e7VnnABOrSZl/7lENme2+IZewlc8xmMg9uoOcUdsIxjkEQBSHuMn+Zfm6iIY5MQ5cuw==", "signatures": [{"sig": "MEYCIQDldPpDzZVnwpb3ackM3BNz4ZnAcvcV3RK5ZEeUL+hRQgIhAMNedGydisML5NH/dY2+oBDKhwTowshwABMt0+oIVNI2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./bcrypt.min.js", "_from": ".", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./bcrypt.js", "compile": "ccjs bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.2.32", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "0.7.10": {"name": "bcryptjs", "version": "0.7.10", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@0.7.10", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "5267e18589fe0bf6aa239ab75f34986ed78d0ee7", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.7.10.tgz", "integrity": "sha512-EY8m02UHzrEb36k54jFOakUqXKj/DNsi0RSHy/CTI6bKEpuN1qYJ7hiagPad3TkvGQQFU6Cx0y4SgtESvxseIQ==", "signatures": [{"sig": "MEQCIGlYJ/MiEEwXYhVJLz/sEJ9r7kgt08CUgGb48B+bfJidAiBZd4JU8K6QBc1qcF3ODK2jo219BGbhT4i5em9DdDxzcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./bcrypt.min.js", "_from": ".", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./bcrypt.js", "compile": "ccjs bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.2.32", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "0.7.12": {"name": "bcryptjs", "version": "0.7.12", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@0.7.12", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "ccd86057c0af099848fe214b7df0d2b6726ccceb", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.7.12.tgz", "integrity": "sha512-ZKy0NMr7j3SxDvukB48TjSTL9Qm3UbwX8tBPajX5rPS9/9y3FgZPturWEgPF/V32vgAYIMrKFib2HfJZIQng5w==", "signatures": [{"sig": "MEUCIFGGEmzoD614kji3gYFfhpA8wA4pBZQVwYhPN5S0SF1MAiEA7dXnbvUKe79mAXfkNhOMOmctkqcJgIs9+iSuR/9vswI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./bcrypt.min.js", "_from": ".", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./bcrypt.js", "compile": "ccjs bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.2.32", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.0.0": {"name": "bcryptjs", "version": "1.0.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.0.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "b8134874adbce11cf54aa9dbbd95a34b6e8b93af", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.0.0.tgz", "integrity": "sha512-svXzQEUUY9OtcTUd0prAXl+Y1RPtfyyUSAJSsN1/BiSVymxXRE+0itICjqYMgVm2wWMzq6SCCGqBvpdLqMnEpw==", "signatures": [{"sig": "MEUCIQDrjteMyXK/aw9kbvvqnnn9a9/xgn2mr9S151jQd5M/rgIgbE+EjVwjD/GvWKsIQltQZWZjXbizyzltiR6MnwZ55tI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b8134874adbce11cf54aa9dbbd95a34b6e8b93af", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"bcrypt": "latest", "testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.0.1": {"name": "bcryptjs", "version": "1.0.1", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.0.1", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "c53418115d488d4de1400aab544c8c094ea5f088", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.0.1.tgz", "integrity": "sha512-6SCa2RvqxJB2H6VlVvV8jLwjPuy6TlZ3KOsGSGv3iE7sSpyPoXWm6+sINt5EzP/TVWnsw8QiENMP2bhE1GpcVg==", "signatures": [{"sig": "MEQCIHqFW3gZUqqAEYLu1NqWeMNudWGhQ6b9IJro6IWsmpXIAiAyRRDbOqNfcXhrVarzA+AcnnSKFUb6nPNu9exnzKa66w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c53418115d488d4de1400aab544c8c094ea5f088", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --warning_level=VERBOSE --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. 100% typed code. Fully compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"bcrypt": "latest", "testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.0.2": {"name": "bcryptjs", "version": "1.0.2", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.0.2", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "34d814aa982da78fa65f5daab5a806137ae02adb", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.0.2.tgz", "integrity": "sha512-5XbcUr1OQv6To+J+RKmjUWk9pZxVeDm0rPgk1U4gELALaFDRwQynzL5gI8R66kRi+XaVIpNPdAhhHtMjbHRc5A==", "signatures": [{"sig": "MEUCIQDe429NDfMNfmaWkZPI+zterHAulpTpDAy20v22VlaLOwIgWY6DO4400Gn4d8xfY+CfDJ9oaoYhX4br9o+VnehHlxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "34d814aa982da78fa65f5daab5a806137ae02adb", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"bcrypt": "latest", "testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.0.3": {"name": "bcryptjs", "version": "1.0.3", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.0.3", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "eefc8503e49221ea3241d7fafb869feeaf4a4c45", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.0.3.tgz", "integrity": "sha512-llKb8yW83ZKOsRFOlTGjWl979fFoK2D5kPlONd1oe5vOPRGDbTZX1qVUGt55266QHutQHM4LOBwN8VFXgwn/6Q==", "signatures": [{"sig": "MEYCIQCGHTlQ2pLMJGdioPOq2U7mqBUETbltweGoptb736AUkAIhAIpQC43wL840ENoeageGVdHCIJ6iv+XGT4cctlojU08/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "eefc8503e49221ea3241d7fafb869feeaf4a4c45", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"bcrypt": "latest", "testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.0.4": {"name": "bcryptjs", "version": "1.0.4", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.0.4", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "a4e72a62b384ee3baa71a832c51b9be9df9ea2ba", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.0.4.tgz", "integrity": "sha512-DiVKwMEh+7/0RxGMSP/2F9tVlorlW3DKmI3M1K0oeIcTQa3RlufKxXi7J3f4LbsOoHrmzFiOo3jucTV/ZMmINA==", "signatures": [{"sig": "MEYCIQDtMV9jWkEqIeJQ2+1Ji1X+HAfdMxPPm/df+nYJ7CafjAIhALvUwFEHBpfVvVwQVbCcdEWC7z56eOblpZG22bki4Gi7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a4e72a62b384ee3baa71a832c51b9be9df9ea2ba", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"bcrypt": "latest", "testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.0.5": {"name": "bcryptjs", "version": "1.0.5", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.0.5", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "c17a4bec50926ef40f583db221ccc42132c2211c", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.0.5.tgz", "integrity": "sha512-nIVeXYiKYULzP4VMsI0RyplZCIdge6OyYNwns4FTWcL83z+h61VVZYTFgSHQUhk/TH9E7RovEFC49ZLvmkYldg==", "signatures": [{"sig": "MEQCIFv5mw7WBofkQI8Mpl/ffo3LMnJf+17xvUILMOwtLAw9AiB4yUy3+QFXUfm5evA2JqmnbiLEQweAdrv0CyV9jKEjLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c17a4bec50926ef40f583db221ccc42132c2211c", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "preprocess ./src/bcrypt.js ./src > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"bcrypt": "latest", "testjs": "latest", "preprocessor": "latest", "closurecompiler": "latest"}}, "1.1.0": {"name": "bcryptjs", "version": "1.1.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "bcryptjs@1.1.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "5b09d6ad419bea83cf11956873f926a5c5cb4f49", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-1.1.0.tgz", "integrity": "sha512-xuHnVhbZz/fndYpc5iGjX2C5sJ81VSPfdlTmhRbUr20XrNHbvYbnBvdEWPyq4KS6yw+TlEOLjR/ruhg1ol6hOw==", "signatures": [{"sig": "MEUCIEylqlI2HMKmCcB8Kq9stguKGXi1/YJHsY0rw/BH+5QFAiEA8MxqgdOUWkMAxnxqpfhyogIn3vz9pawPbxIIPyR76/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5b09d6ad419bea83cf11956873f926a5c5cb4f49", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node node_modules/metascript/bin/metascript ./src/bcrypt.js > ./dist/bcrypt.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.0.0": {"name": "bcryptjs", "version": "2.0.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "bcryptjs@2.0.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "e5c8a775a5d871f93241f6ad90150443758327a7", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.0.0.tgz", "integrity": "sha512-6CVO35YZD8UW2Cz7PrGtaoxezVtpaH6P9N7pGFIpuWFfUtUOcRplqnK3nNA/iChY5OCGcb0j7Zd7FgP+7QExpQ==", "signatures": [{"sig": "MEYCIQDTDAdcuGNhiy8gkla8SAGUGACUq3d7lyL3Pgww9C+zhQIhAMeuRsHhkAYeAez9BwyIK5+OK0VA11faWQZPGZ+9OwsV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e5c8a775a5d871f93241f6ad90150443758327a7", "browser": "dist/bcrypt-isaac.js", "scripts": {"make": "npm run-script build && npm run-script compile && npm run-script compile-isaac && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compile-isaac": "ccjs dist/bcrypt-isaac.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt-isaac.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/dcodeIO/bcrypt.js/master/LICENSE", "type": "New-BSD, MIT"}], "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.0.1": {"name": "bcryptjs", "version": "2.0.1", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "bcryptjs@2.0.1", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "7ddd96ea778f93bf3691e9da5907896a9a2ef0d8", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.0.1.tgz", "integrity": "sha512-vwnsYVT075eSF1X8xN16atjIl3xtCHBj1bohTAv2OyuXZUqMXF8pBoCoXu4UnodnUGwigFT568MB2/nbjz6NLQ==", "signatures": [{"sig": "MEUCIQCRbd56P4t71ZiRrTcCr7apKH6adrwdB0jYwASwOj5pJQIgds7OzWPDUjtuvCbcdpY6Uv68w54FOHvlHO/ohIrMsug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7ddd96ea778f93bf3691e9da5907896a9a2ef0d8", "browser": "dist/bcrypt-isaac.js", "scripts": {"make": "npm run-script build && npm run-script compile && npm run-script compile-isaac && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compile-isaac": "ccjs dist/bcrypt-isaac.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt-isaac.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/dcodeIO/bcrypt.js/master/LICENSE", "type": "New-BSD, MIT"}], "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.0.2": {"name": "bcryptjs", "version": "2.0.2", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "bcryptjs@2.0.2", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "1cef0376a70190d3bb584df1c0740dbde2bca747", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.0.2.tgz", "integrity": "sha512-JCvHmRTFTq5B818ATuSDR1zgLImPNMOApRrtX2La/NKJrybA4AQj2wttv9VTETlWv8ITLKVI62D7tdShPz1T0A==", "signatures": [{"sig": "MEYCIQDyTJyW5yso7abQ6ZGZ/RDWIeUR4f5r8X+SNLk/xCicrAIhAMs4t8EfOm1+wkdJsyHkQUy09fPpGu2icZwdcdaz3Lnt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1cef0376a70190d3bb584df1c0740dbde2bca747", "browser": "dist/bcrypt-isaac.js", "scripts": {"make": "npm run-script build && npm run-script compile && npm run-script compile-isaac && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compile-isaac": "ccjs dist/bcrypt-isaac.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt-isaac.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/dcodeIO/bcrypt.js/master/LICENSE", "type": "New-BSD, MIT"}], "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.9", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.1.0": {"name": "bcryptjs", "version": "2.1.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "bcryptjs@2.1.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "5e6076196f6e3e957820aceb004410a7cf367042", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.1.0.tgz", "integrity": "sha512-6QrBwXTjIXjMvo61TjUwPYpD1Hpiqu/CJURzh/FHYdv5VWuISFbObZe+i/****************************==", "signatures": [{"sig": "MEQCID4fMghnImX+p3eF0jPxIjYm+KVk2fgTw8SeV7Y+Hxl6AiBZXj/65YjMnaU0HMpSVdxJ0qCaEWhyx8MKUy6jTVIqIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5e6076196f6e3e957820aceb004410a7cf367042", "browser": "dist/bcrypt.js", "gitHead": "2c4068de300d98ec050f49169e9c99859e170900", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/dcodeIO/bcrypt.js/master/LICENSE", "type": "New-BSD, MIT"}], "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.21", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.2.0": {"name": "bcryptjs", "version": "2.2.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "bcryptjs@2.2.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "188d6507ca4ac54ecd1ca38ee4d1a1a24ca685b8", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.2.0.tgz", "integrity": "sha512-k35klR1zdLqgnaLgr50sH/2SBgPwCdDx+AT+DQatbSh5fWMhBx7OWWssW8MqTzfxBEMIhwK4W/ECaAfyo04J/w==", "signatures": [{"sig": "MEUCIQCJsel4RZTPv1YLGJSDmPjRjCMOVJFfOLPsc/ho0Oe1EAIgaNBLQ4JrNfMLipHXQDtMIPygV9tXQ28g4MuyLwkANRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "188d6507ca4ac54ecd1ca38ee4d1a1a24ca685b8", "browser": "dist/bcrypt.js", "gitHead": "5014ec5d8e06a615522a10843605ea3d5a5e8299", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "licenses": [{"url": "https://raw.githubusercontent.com/dcodeIO/bcrypt.js/master/LICENSE", "type": "New-BSD, MIT"}], "repository": {"url": "https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "1.4.28", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.2.1": {"name": "bcryptjs", "version": "2.2.1", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.2.1", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "102a2e99c19069340c3d3e6d11abaa0ef7cdb988", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.2.1.tgz", "integrity": "sha512-btiwuIWGpq0kCkbcNt4LyvoL2m2OhXPt+5Hf9WCtof+hKq57lT+ATpLRo5vuVWTABFUUazpSLxSlB1RwsUSDNQ==", "signatures": [{"sig": "MEUCIQDN8RL9/9iIRhRRxvF32JRoKl537P4fwjvorOwnQ8TQewIgMJLDaaHPq2mpr3BGjOJaYegoqptnYSqHyAraWDFwfbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "102a2e99c19069340c3d3e6d11abaa0ef7cdb988", "browser": "dist/bcrypt.js", "gitHead": "dd6eaf724afc72618126782de8dfb061c1a5ae90", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "2.11.3", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.2.2": {"name": "bcryptjs", "version": "2.2.2", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.2.2", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "a2f8b6e802626684e2d172892519572bddbaefd9", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.2.2.tgz", "integrity": "sha512-hf81DxSbL8IC+EjJvF/z5Mz0moTHhAO0ZLDcLtsRHg04PdEvQnZiYdhuaHeYUe6/+ijf7+wJQZsM7rhUuVBdlQ==", "signatures": [{"sig": "MEQCIFVS+hPMRToewptHO6JH/5gI7KHXsM1TJXEAQ/EHS+jeAiAUm/CsJwJ6Df1NU5p7zTRJKfYT0m8ubbiZxSMMUkB8Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a2f8b6e802626684e2d172892519572bddbaefd9", "browser": "dist/bcrypt.js", "gitHead": "594428af8a36c110f88ae248debcede44d0d11a3", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "2.11.3", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.3.0": {"name": "bcryptjs", "version": "2.3.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.3.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "5826900cfef7abaf3425c72e4d464de509b8c2ec", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.3.0.tgz", "integrity": "sha512-cEPr8jwWSB7xk73mbJYuWxyM9EMKomNlv51da7j+xa9Go2pyRU/Hml8v/WX9doW87b7a2ph18G+xsp7bQcliwg==", "signatures": [{"sig": "MEUCIDdb4J+XALAZ9D2myKeCe4LWf9kESI5XejK3T7AlpLr3AiEAvS5+lPvri28RZ+LN7M1O301Aec+bPnK+fkz/5vGIs28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5826900cfef7abaf3425c72e4d464de509b8c2ec", "browser": "dist/bcrypt.js", "gitHead": "89c19400dcc9ca77b9f768bd79a6700eb764a1da", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "2.11.3", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}}, "2.4.0": {"name": "bcryptjs", "version": "2.4.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.4.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "fb7f4a0b133854503fe1b2da3f25db834cf0e678", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.0.tgz", "integrity": "sha512-B6DCERXiRT4wcd8HFptyRO6HebnshpJE3X8+MtCScC7gXetYYOvloeCSpK967yxb6kYQL73BpicySlwnojUONg==", "signatures": [{"sig": "MEUCIHmR5Kr+Ht0dqBweSUEwE9VxffOgJtaWrJzojKy84WDnAiEAialwK3khOn7juERi1AbbCaLvxhmLshleoRdA9y0Nlu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "fb7f4a0b133854503fe1b2da3f25db834cf0e678", "browser": "dist/bcrypt.js", "gitHead": "21b79128929f3392e7f43cf7251c8d9eef78f0a6", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "3.8.6", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs-2.4.0.tgz_1481374594306_0.36583748064003885", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.1": {"name": "bcryptjs", "version": "2.4.1", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.4.1", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "eff0cfaf747a9e6553520c75a34bd5ebb53cfa09", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.1.tgz", "integrity": "sha512-ALX481awnprAJX/ZrIXcArZw8FZhCg+ABzgDRDhGtf3yZOULroBCa7MpU1jdVF65R4Xe4ILPxYmTfPTpvmUewg==", "signatures": [{"sig": "MEQCIFrQ5PGX0raKPPYJ598qN/aPex3R9C04EPUoyxJBwmOfAiABeChc/5SqyVet2Qsyp47sR7s0eRdx153E7V1bP0MERA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "eff0cfaf747a9e6553520c75a34bd5ebb53cfa09", "browser": "dist/bcrypt.js", "gitHead": "048c58a7ebb71acfb748e1c579d940f863bc9470", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map --externs=externs/minimal-env.js --output_wrapper=\"(function(){%output%})();\" > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "4.0.5", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs-2.4.1.tgz_1486453337001_0.301876129116863", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.2": {"name": "bcryptjs", "version": "2.4.2", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.4.2", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "6faee62ed9cb636fbbb3f4b5a4164bbed5a9ef7e", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.2.tgz", "integrity": "sha512-4m7EJZjGs+W+1aIdc0zzdUqN7vV22PRSN1JN2hMQRdym3ula76QGCofg5iAPpdVRxJfSXlYAEDrVzOpS7kUh+A==", "signatures": [{"sig": "MEUCIQC6RTa1psD3eLxF4OaGUXvvsoMul92pJcUrrtcyw1pobAIgBPDfyQZaIiFG94fEbNLviCAvqB1K72CS+oEDd/jXVGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6faee62ed9cb636fbbb3f4b5a4164bbed5a9ef7e", "browser": "dist/bcrypt.js", "gitHead": "2fa2b7cadf8a5e2bbe8436439b219b0d5596dbe7", "scripts": {"make": "npm run-script build && npm run-script compile && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=SIMPLE_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "4.0.5", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs-2.4.2.tgz_1486454599210_0.048139352118596435", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.3": {"name": "bcryptjs", "version": "2.4.3", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "bcryptjs@2.4.3", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "dist": {"shasum": "9ab5627b93e60621ff7cdac5da9733027df1d0cb", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz", "integrity": "sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ==", "signatures": [{"sig": "MEUCIQDoEAtTW5x957TSZiGJWGaF3AovV9O8lxxy8NBoxuET+AIgdKn3r1S6rK8XS7acehkUdRJHEkcuHif3j4KAUFtwvV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9ab5627b93e60621ff7cdac5da9733027df1d0cb", "browser": "dist/bcrypt.js", "gitHead": "f7dd725a0b77036696042b5c1cb5e13cf0f7291e", "scripts": {"make": "npm run build && npm run compile && npm run compress && npm test", "test": "node node_modules/testjs/bin/testjs", "build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=SIMPLE_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "4.0.5", "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {}, "devDependencies": {"utfx": "~1", "bcrypt": "latest", "testjs": "~1", "metascript": "~0.18", "closurecompiler": "~1"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs-2.4.3.tgz_1486460625673_0.9308078193571419", "host": "packages-18-east.internal.npmjs.com"}}, "0.1.0": {"name": "bcryptjs", "version": "0.1.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "bcryptjs@0.1.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "bin": {"bcrypt": "bin/bcrypt"}, "dist": {"shasum": "93e26d1d2bcfa97506fa536a218316ad48120e68", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-0.1.0.tgz", "fileCount": 10, "integrity": "sha512-6IwnBq61NJ33dnhpkqV+aG+Fa++wfE5yaBsUuLgzfqfeinWX/622nEY6V0UxOmls3JgdIQbWFYiy5XMrSJNfIg==", "signatures": [{"sig": "MEUCIQDjo/WxZ0hiqCjn7byvm3U/s7aTu1mmel1iakkLMMpXbgIgaiee5TiZ5NI9lG5YnWOcxk2PIS4g2YxVNkZRsaiDfpI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 102259}, "main": "umd/index.js", "type": "module", "types": "umd/index.d.ts", "browser": {"crypto": false}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "gitHead": "d5656b39e2e368c87724a312e4e454456a4e5d1b", "scripts": {"lint": "prettier --check .", "test": "npm run test:unit && npm run test:typescript", "build": "esm2umd bcrypt index.js > umd/index.js && prettier --write umd/index.js", "format": "prettier --write .", "test:unit": "node tests", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "10.9.2", "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "23.8.0", "_hasShrinkwrap": false, "devDependencies": {"bcrypt": "^5.1.1", "esm2umd": "^0.3.0", "prettier": "^3.5.0", "typescript": "^5.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs_0.1.0_1739470803952_0.949116371687337", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.0": {"name": "bcryptjs", "version": "3.0.0", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "bcryptjs@3.0.0", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "bin": {"bcrypt": "bin/bcrypt"}, "dist": {"shasum": "90b3ddba08c5dbe1a9e15ac900afdc8fb4772965", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-Q2vVGpGC7B7m9wggpcA5lq4OYR5OS1nrXoUpnH9MmogXU8HpxzKg63uxtCrLebY5v/y3o0r7JcGCpR/vTGGn7A==", "signatures": [{"sig": "MEUCIHX0gcNdi8Ni2QAoViqNWnLcDuDZS1p3ofkedKLPeRHcAiEAvW/u8V73pUYVTtYrGQofIuvS+/FZUHQTSvtCX/99BnA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 102259}, "main": "umd/index.js", "type": "module", "types": "umd/index.d.ts", "browser": {"crypto": false}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "gitHead": "2a9bea9e276e6be04dbd403f9695937788b3b10a", "scripts": {"lint": "prettier --check .", "test": "npm run test:unit && npm run test:typescript", "build": "esm2umd bcrypt index.js > umd/index.js && prettier --write umd/index.js", "format": "prettier --write .", "test:unit": "node tests", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "10.9.2", "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "23.8.0", "_hasShrinkwrap": false, "devDependencies": {"bcrypt": "^5.1.1", "esm2umd": "^0.3.0", "prettier": "^3.5.0", "typescript": "^5.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs_3.0.0_1739471043853_0.7389334074397533", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.1": {"name": "bcryptjs", "version": "3.0.1", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "bcryptjs@3.0.1", "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/shaneGirish", "name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>"}, {"url": "https://github.com/alexmurray", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON>", "name": "<PERSON>"}, {"url": "https://github.com/geekymole", "name": "<PERSON>"}, {"url": "https://github.com/nisa<PERSON>son", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "bin": {"bcrypt": "bin/bcrypt"}, "dist": {"shasum": "870705ef5c5148de29395a0695ca2e7766cf6c2e", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-3.0.1.tgz", "fileCount": 11, "integrity": "sha512-iY+rRoR0bToRe8LTJkVX2gawo8gmgDQbiFsaKy8fVuG3WXekEQrjasfmWIbJxJHttEKPa+xPxbw3GROnJSmu0A==", "signatures": [{"sig": "MEYCIQDlpqIndzjTtO6Q+FrgX44wPrsnL3cGMTkMrUP3itDxagIhAMqeFJ+tWDj3b/tmrvXge0KnQqvYifsIjfOrObzlLMAP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 112235}, "main": "umd/index.js", "type": "module", "types": "umd/index.d.ts", "browser": {"crypto": false}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "gitHead": "e7055caf0c723cbcf8bc3f0784b8c30ee332380f", "scripts": {"lint": "prettier --check .", "test": "npm run test:unit && npm run test:typescript", "build": "node scripts/build.js", "format": "prettier --write .", "test:unit": "node tests", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json"}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dcodeIO/bcrypt.js.git", "type": "url"}, "_npmVersion": "10.9.2", "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "directories": {}, "_nodeVersion": "23.8.0", "_hasShrinkwrap": false, "devDependencies": {"bcrypt": "^5.1.1", "esm2umd": "^0.3.0", "prettier": "^3.5.0", "typescript": "^5.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/bcryptjs_3.0.1_1739808118405_0.5246753990134037", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.2": {"name": "bcryptjs", "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "version": "3.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>", "url": "https://github.com/shaneGirish"}, {"name": "<PERSON>", "url": "https://github.com/alexmurray"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/geekymole"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nisa<PERSON>son"}], "repository": {"type": "url", "url": "git+https://github.com/dcodeIO/bcrypt.js.git"}, "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "type": "module", "main": "umd/index.js", "types": "umd/index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.js"}, "require": {"types": "./umd/index.d.ts", "default": "./umd/index.js"}}}, "bin": {"bcrypt": "bin/bcrypt"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"build": "node scripts/build.js", "lint": "prettier --check .", "format": "prettier --write .", "test": "npm run test:unit && npm run test:typescript", "test:unit": "node tests", "test:typescript": "tsc --project tests/typescript/tsconfig.esnext.json && tsc --project tests/typescript/tsconfig.nodenext.json && tsc --project tests/typescript/tsconfig.commonjs.json && tsc --project tests/typescript/tsconfig.global.json"}, "browser": {"crypto": false}, "devDependencies": {"bcrypt": "^5.1.1", "esm2umd": "^0.3.1", "prettier": "^3.5.0", "typescript": "^5.7.3"}, "_id": "bcryptjs@3.0.2", "gitHead": "28e510389374f5736c447395443d4a6687325048", "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "_nodeVersion": "23.8.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-k38b3XOZKv60C4E2hVsXTolJWfkGRMbILBIe2IBITXciy5bOsTKot5kDrf3ZfufQtQOUN5mXceUEpU1rTl9Uog==", "shasum": "caadcca1afefe372ed6e20f86db8e8546361c1ca", "tarball": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-3.0.2.tgz", "fileCount": 11, "unpackedSize": 112248, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDXujtkNnG2EXy+8J+8+cdkodKXF25GinmQOjXVSDTvmgIhAM3SqcCEfR1gaF74xgSpXPXd5ZOQWjxQ5YgA1CXP0e+B"}]}, "_npmUser": {"name": "dcode", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/bcryptjs_3.0.2_1739908368823_0.38089360644819226"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-05-01T03:38:38.047Z", "modified": "2025-02-18T19:52:49.160Z", "0.7.5": "2013-05-01T03:38:42.060Z", "0.7.6": "2013-05-02T18:10:39.056Z", "0.7.7": "2013-05-03T21:35:40.629Z", "0.7.8": "2013-07-17T00:48:17.974Z", "0.7.10": "2013-08-10T00:12:47.464Z", "0.7.11": "2014-02-20T04:09:23.342Z", "0.7.12": "2014-02-20T16:25:16.999Z", "1.0.0": "2014-06-08T20:46:34.032Z", "1.0.1": "2014-06-09T21:17:22.752Z", "1.0.2": "2014-06-22T15:16:01.292Z", "1.0.3": "2014-06-24T21:21:49.343Z", "1.0.4": "2014-06-26T13:46:42.122Z", "1.0.5": "2014-07-04T23:45:19.956Z", "1.1.0": "2014-07-12T16:42:50.654Z", "2.0.0": "2014-07-15T01:11:42.093Z", "2.0.1": "2014-07-15T02:05:06.954Z", "2.0.2": "2014-07-23T16:08:16.052Z", "2.1.0": "2014-10-21T00:00:55.892Z", "2.2.0": "2015-07-07T20:01:47.292Z", "2.2.1": "2015-08-02T21:15:50.620Z", "2.2.2": "2015-09-19T00:42:53.367Z", "2.3.0": "2015-09-24T13:44:58.890Z", "2.4.0": "2016-12-10T12:56:35.033Z", "2.4.1": "2017-02-07T07:42:17.664Z", "2.4.2": "2017-02-07T08:03:21.341Z", "2.4.3": "2017-02-07T09:43:46.316Z", "0.1.0": "2025-02-13T18:20:04.201Z", "3.0.0": "2025-02-13T18:24:04.156Z", "3.0.1": "2025-02-17T16:01:58.561Z", "3.0.2": "2025-02-18T19:52:48.985Z"}, "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "repository": {"type": "url", "url": "git+https://github.com/dcodeIO/bcrypt.js.git"}, "description": "Optimized bcrypt in plain JavaScript with zero dependencies, with TypeScript support. Compatible to 'bcrypt'.", "contributors": [{"name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>", "url": "https://github.com/shaneGirish"}, {"name": "<PERSON>", "url": "https://github.com/alexmurray"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/geekymole"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nisa<PERSON>son"}], "maintainers": [{"name": "dcode", "email": "<EMAIL>"}], "readme": "# bcrypt.js\n\nOptimized bcrypt in JavaScript with zero dependencies, with TypeScript support. Compatible to the C++\n[bcrypt](https://npmjs.org/package/bcrypt) binding on Node.js and also working in the browser.\n\n[![Build Status](https://img.shields.io/github/actions/workflow/status/dcodeIO/bcrypt.js/test.yml?branch=main&label=test&logo=github)](https://github.com/dcodeIO/bcrypt.js/actions/workflows/test.yml) [![Publish Status](https://img.shields.io/github/actions/workflow/status/dcodeIO/bcrypt.js/publish.yml?branch=main&label=publish&logo=github)](https://github.com/dcodeIO/bcrypt.js/actions/workflows/publish.yml) [![npm](https://img.shields.io/npm/v/bcryptjs.svg?label=npm&color=007acc&logo=npm)](https://www.npmjs.com/package/bcryptjs)\n\n## Security considerations\n\nBesides incorporating a salt to protect against rainbow table attacks, bcrypt is an adaptive function: over time, the\niteration count can be increased to make it slower, so it remains resistant to brute-force search attacks even with\nincreasing computation power. ([see](http://en.wikipedia.org/wiki/Bcrypt))\n\nWhile bcrypt.js is compatible to the C++ bcrypt binding, it is written in pure JavaScript and thus slower ([about 30%](https://github.com/dcodeIO/bcrypt.js/wiki/Benchmark)), effectively reducing the number of iterations that can be\nprocessed in an equal time span.\n\nThe maximum input length is 72 bytes (note that UTF-8 encoded characters use up to 4 bytes) and the length of generated\nhashes is 60 characters. Note that maximum input length is not implicitly checked by the library for compatibility with\nthe C++ binding on Node.js, but should be checked with `bcrypt.truncates(password)` where necessary.\n\n## Usage\n\nThe package exports an ECMAScript module with an UMD fallback.\n\n```\n$> npm install bcryptjs\n```\n\n```ts\nimport bcrypt from \"bcryptjs\";\n```\n\n### Usage with a CDN\n\n- From GitHub via [jsDelivr](https://www.jsdelivr.com):<br />\n  `https://cdn.jsdelivr.net/gh/dcodeIO/bcrypt.js@TAG/index.js` (ESM)\n- From npm via [jsDelivr](https://www.jsdelivr.com):<br />\n  `https://cdn.jsdelivr.net/npm/bcryptjs@VERSION/index.js` (ESM)<br />\n  `https://cdn.jsdelivr.net/npm/bcryptjs@VERSION/umd/index.js` (UMD)\n- From npm via [unpkg](https://unpkg.com):<br />\n  `https://unpkg.com/bcryptjs@VERSION/index.js` (ESM)<br />\n  `https://unpkg.com/bcryptjs@VERSION/umd/index.js` (UMD)\n\nReplace `TAG` respectively `VERSION` with a [specific version](https://github.com/dcodeIO/bcrypt.js/releases) or omit it (not recommended in production) to use latest.\n\nWhen using the ESM variant in a browser, the `crypto` import needs to be stubbed out, for example using an [import map](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/script/type/importmap). Bundlers should omit it automatically.\n\n### Usage - Sync\n\nTo hash a password:\n\n```ts\nconst salt = bcrypt.genSaltSync(10);\nconst hash = bcrypt.hashSync(\"B4c0/\\/\", salt);\n// Store hash in your password DB\n```\n\nTo check a password:\n\n```ts\n// Load hash from your password DB\nbcrypt.compareSync(\"B4c0/\\/\", hash); // true\nbcrypt.compareSync(\"not_bacon\", hash); // false\n```\n\nAuto-gen a salt and hash:\n\n```ts\nconst hash = bcrypt.hashSync(\"bacon\", 10);\n```\n\n### Usage - Async\n\nTo hash a password:\n\n```ts\nconst salt = await bcrypt.genSalt(10);\nconst hash = await bcrypt.hash(\"B4c0/\\/\", salt);\n// Store hash in your password DB\n```\n\n```ts\nbcrypt.genSalt(10, (err, salt) => {\n  bcrypt.hash(\"B4c0/\\/\", salt, function (err, hash) {\n    // Store hash in your password DB\n  });\n});\n```\n\nTo check a password:\n\n```ts\n// Load hash from your password DB\nawait bcrypt.compare(\"B4c0/\\/\", hash); // true\nawait bcrypt.compare(\"not_bacon\", hash); // false\n```\n\n```ts\n// Load hash from your password DB\nbcrypt.compare(\"B4c0/\\/\", hash, (err, res) => {\n  // res === true\n});\nbcrypt.compare(\"not_bacon\", hash, (err, res) => {\n  // res === false\n});\n```\n\nAuto-gen a salt and hash:\n\n```ts\nawait bcrypt.hash(\"B4c0/\\/\", 10);\n// Store hash in your password DB\n```\n\n```ts\nbcrypt.hash(\"B4c0/\\/\", 10, (err, hash) => {\n  // Store hash in your password DB\n});\n```\n\n**Note:** Under the hood, asynchronous APIs split an operation into small chunks. After the completion of a chunk, the execution of the next chunk is placed on the back of the [JS event queue](https://developer.mozilla.org/en/docs/Web/JavaScript/EventLoop), efficiently yielding for other computation to execute.\n\n### Usage - Command Line\n\n```\nUsage: bcrypt <input> [rounds|salt]\n```\n\n## API\n\n### Callback types\n\n- **Callback<`T`>**: `(err: Error | null, result?: T) => void`<br />\n  Called with an error on failure or a value of type `T` upon success.\n\n- **ProgressCallback**: `(percentage: number) => void`<br />\n  Called with the percentage of rounds completed (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n\n- **RandomFallback**: `(length: number) => number[]`<br />\n  Called to obtain random bytes when both [Web Crypto API](http://www.w3.org/TR/WebCryptoAPI/) and Node.js\n  [crypto](http://nodejs.org/api/crypto.html) are not available.\n\n### Functions\n\n- bcrypt.**genSaltSync**(rounds?: `number`): `string`<br />\n  Synchronously generates a salt. Number of rounds defaults to 10 when omitted.\n\n- bcrypt.**genSalt**(rounds?: `number`): `Promise<string>`<br />\n  Asynchronously generates a salt. Number of rounds defaults to 10 when omitted.\n\n- bcrypt.**genSalt**([rounds: `number`, ]callback: `Callback<string>`): `void`<br />\n  Asynchronously generates a salt. Number of rounds defaults to 10 when omitted.\n\n- bcrypt.**truncates**(password: `string`): `boolean`<br />\n  Tests if a password will be truncated when hashed, that is its length is greater than 72 bytes when converted to UTF-8.\n\n- bcrypt.**hashSync**(password: `string`, salt?: `number | string`): `string`\n  Synchronously generates a hash for the given password. Number of rounds defaults to 10 when omitted.\n\n- bcrypt.**hash**(password: `string`, salt: `number | string`): `Promise<string>`<br />\n  Asynchronously generates a hash for the given password.\n\n- bcrypt.**hash**(password: `string`, salt: `number | string`, callback: `Callback<string>`, progressCallback?: `ProgressCallback`): `void`<br />\n  Asynchronously generates a hash for the given password.\n\n- bcrypt.**compareSync**(password: `string`, hash: `string`): `boolean`<br />\n  Synchronously tests a password against a hash.\n\n- bcrypt.**compare**(password: `string`, hash: `string`): `Promise<boolean>`<br />\n  Asynchronously compares a password against a hash.\n\n- bcrypt.**compare**(password: `string`, hash: `string`, callback: `Callback<boolean>`, progressCallback?: `ProgressCallback`)<br />\n  Asynchronously compares a password against a hash.\n\n- bcrypt.**getRounds**(hash: `string`): `number`<br />\n  Gets the number of rounds used to encrypt the specified hash.\n\n- bcrypt.**getSalt**(hash: `string`): `string`<br />\n  Gets the salt portion from a hash. Does not validate the hash.\n\n- bcrypt.**setRandomFallback**(random: `RandomFallback`): `void`<br />\n  Sets the pseudo random number generator to use as a fallback if neither [Web Crypto API](http://www.w3.org/TR/WebCryptoAPI/) nor Node.js [crypto](http://nodejs.org/api/crypto.html) are available. Please note: It is highly important that the PRNG used is cryptographically secure and that it is seeded properly!\n\n## Building\n\nBuilding the UMD fallback:\n\n```\n$> npm run build\n```\n\nRunning the [tests](./tests):\n\n```\n$> npm test\n```\n\n## Credits\n\nBased on work started by Shane Girish at [bcrypt-nodejs](https://github.com/shaneGirish/bcrypt-nodejs), which is itself\nbased on [javascript-bcrypt](http://code.google.com/p/javascript-bcrypt/) (New BSD-licensed).\n", "readmeFilename": "README.md", "users": {"awzm": true, "d3ck": true, "enna": true, "hema": true, "j.su": true, "leor": true, "n370": true, "nazy": true, "wayn": true, "dnero": true, "fm-96": true, "holly": true, "leota": true, "lqweb": true, "makay": true, "ablbol": true, "ahme-t": true, "akarem": true, "beytek": true, "bpatel": true, "ethan_": true, "isayme": true, "iuykza": true, "jrthib": true, "lonjoy": true, "manten": true, "nauhil": true, "olonam": true, "phajej": true, "post72": true, "quafoo": true, "rajiff": true, "rezozo": true, "ryaned": true, "sgiant": true, "vcboom": true, "ywemay": true, "4rlekin": true, "antanst": true, "atomgao": true, "cbeulke": true, "dahdoul": true, "demoive": true, "dlv_201": true, "gollojs": true, "infrasp": true, "itonyyo": true, "jalcine": true, "jkabore": true, "joneshf": true, "juanf03": true, "kkho595": true, "limintu": true, "mamalat": true, "nichoth": true, "nogirev": true, "robsoer": true, "ryanlee": true, "simonja": true, "tomchao": true, "vinchik": true, "1cr18ni9": true, "blindcat": true, "bpolonia": true, "cslasher": true, "dtiziani": true, "esundahl": true, "fabioper": true, "gedarufi": true, "geooogle": true, "iamninad": true, "jonathas": true, "kaasdude": true, "krabello": true, "leonning": true, "lobodpav": true, "maxblock": true, "mhadaily": true, "mhaidarh": true, "nicknaso": true, "pr-anoop": true, "qbylucky": true, "rfortune": true, "szymex73": true, "tenpenny": true, "tosbodes": true, "vinbhatt": true, "allendale": true, "blitzprog": true, "cspotcode": true, "heartnett": true, "itnovicer": true, "jerkovicl": true, "largepuma": true, "mastayoda": true, "mjurincic": true, "mr-smiley": true, "nmccready": true, "npmmurali": true, "retorillo": true, "rlafferty": true, "rosterloh": true, "ruyadorno": true, "sasquatch": true, "snowdream": true, "stretchgz": true, "tomgao365": true, "vitordeng": true, "ashiknesin": true, "cfleschhut": true, "gerst20051": true, "hinneslung": true, "jessaustin": true, "jussipekka": true, "karlbright": true, "manikantag": true, "morogasper": true, "nickdugger": true, "rocket0191": true, "sammok2003": true, "wizardzloy": true, "zhangyuhan": true, "aidalegrand": true, "campallison": true, "cuiyongjian": true, "debearloper": true, "django_wong": true, "eserozvataf": true, "existenzial": true, "fabioricali": true, "ilia.ivanov": true, "jamesbedont": true, "jonatasnona": true, "kodekracker": true, "lucifier129": true, "luuhoangnam": true, "mrwanashraf": true, "octetstream": true, "sadmansamee": true, "sidhanthsur": true, "stonecypher": true, "writeosahon": true, "andrewyang96": true, "ghostcode521": true, "mohsinnadeem": true, "processbrain": true, "windhamdavid": true, "lucasmciruzzi": true, "markthethomas": true, "matiasherranz": true, "mdedirudianto": true, "pablo.tavarez": true, "ral.amgstromg": true, "serge-nikitin": true, "imaginegenesis": true, "julianomontini": true, "karl.alnebratt": true, "karzanosman984": true, "oliversalzburg": true, "shanewholloway": true, "thevikingcoder": true, "andrew.medvedev": true, "andygreenegrass": true, "hyokosdeveloper": true, "shriharishastry": true, "lherediawoodward": true, "maciej.litwiniec": true, "netoperatorwibby": true, "ognjen.jevremovic": true, "totallyinformation": true, "obsessiveprogrammer": true}}