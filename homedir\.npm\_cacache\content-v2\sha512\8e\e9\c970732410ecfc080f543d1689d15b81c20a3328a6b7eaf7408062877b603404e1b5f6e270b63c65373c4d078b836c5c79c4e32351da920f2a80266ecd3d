{"_id": "@hapi/topo", "_rev": "27-4e1ea0afb2259dde58d3586e74ea2223", "name": "@hapi/topo", "dist-tags": {"latest": "6.0.2", "lts": "3.1.6"}, "versions": {"3.1.0": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "3.1.0", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "6.x.x"}, "devDependencies": {"code": "5.x.x", "lab": "18.x.x"}, "scripts": {"test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "d0e1f5173f1ce4eca12cb9c601eeeacc1841aeb5", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@3.1.0", "_nodeVersion": "10.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gZDI/eXOIk8kP2PkUKjWu9RW8GGVd2Hkgjxyr/S7Z+JF+0mr7bAlbw+DkTRxnD580o8Kqxlnba9wvqp5aOHBww==", "shasum": "5c47cd9637c2953db185aa957a27bcb2a8b7a6f8", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.0.tgz", "fileCount": 5, "unpackedSize": 9535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnnkSCRA9TVsSAnZWagAAknMP/3RoqUDYuXKHNP97w7fI\nZmoFZ0icHQxYUj0oFkjiLSNJ5nnLwsRs6q1vmv+N+qImhcKMVXB+pgfappQu\nULHlRH/WdoQsiUcIgwyKH9pgk7Tg2MTu5Hj5m0DKgvoW9lZLpLIox/3xlLzu\nYetTan9bOGqrZx1EBAUL/0CyLSzahxH4dPGqLNquUwKkfSdOYQ9tdsfYJEq1\nkUywwzJJtTToPynM76qvkLYsu3JtTb1FqZupx2tirWoSKeU6CoaTXKCpJ0z+\nrgj7dkE6Le3YUf8ty4Z0sj+GGf5TolZ6/gPPV9ozSZo6kPWbrFeFJh+aOpUd\nTCmEQxWiCIzbFLIiFLhgK368QtsDFfUagtuS95pjk1/rrwTDKtPVK5qukEC8\npXXjlrXqbsfjLjRZlMLpZrXQDqSa8zPxyY/P5iKQyA44/98FH7kPNi4m5CP4\nWHlPl+seD0g0TURvCKGe2IK6yYvob/1+AVRjyL3CvQRAoLX/BcYXvur8YlSI\ntqc6j9FnVq0tcrXrC/4giyqFeV8pBD8gPbUx9ePdwYCzXIKmceRlGbC35wDk\nu9DjUlSrrZlQCsLoItSiO+XH+Nsxfu+KT9ThWL2S0tf57QaI0lCnf1ymHqBr\nEPnRrISwOQvU1L52tSXzLeKoqA4q21qMIiknd+5yxNsiNQjzrDViacWwzca0\nGt3o\r\n=O+TB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDojNwGLlGUrt/c83o5gv+mgghBxn+jojEE63GSpawQaAiEAmpX7qSTrkvmMrwttuJOAqO2odwr/ygFCtbrmihM3IPc="}]}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_3.1.0_1553889553832_0.3949727001622776"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "3.1.2": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "3.1.2", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "8.x.x"}, "devDependencies": {"@hapi/code": "5.x.x", "@hapi/lab": "19.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "f6e944f7eefed301fe9ac3548d2968222e00b2ea", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@3.1.2", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-r+aumOqJ5QbD6aLPJWqVjMAPsx5pZKz+F5yPqXZ/WWG9JTtHbQqlzrJoknJ0iJxLj9vlXtmpSdjlkszseeG8OA==", "shasum": "57cc1317be1a8c5f47c124f9b0e3c49cd78424d2", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.2.tgz", "fileCount": 5, "unpackedSize": 9609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE76hCRA9TVsSAnZWagAAiJUP/0PtPX9Bu5QzxAz2gYwB\nw9jML4+rYgkkmi57NFjcwxiR/IUjYXLGTCb3D59CHuCV5GkD6yBSoRbqRDuE\nQj72UvJlJ97iB4To/H1k8Cm31BjITNazWICUdcTlnGm6jlp4tZdp09vpPW1e\nrYv5FyvwE36lEyEkwVxLq7v5WCOmjjBl9SKYZbtioFtbePzzV3uPiLnGlLEn\n4UVWJGqk2GohWWqM2N7J24JiTnGQuLBYE1Y8KIYeL+g8q/TbnoC20Rpbcdbp\nq6AilMY9iWRBf97eOyC2o6yRj8X3Co7bDFkbtOQ8Tcbv1HjrNLIJR+9YDS9v\nXIbm6JkmeUSDY+g70hB2g76Y3A7RAcQTHavFwX2lIo97Q1ntOp4m/Ihax3uR\npaXgVuhVsfD6Xcj36x4lFKc+cfGSIrRh8tCyJrqyMbq0YkCqzaFKIrJCl5O8\nprYxwTAkmFDyNpm99c9b8gxqvGnhAg9W4QHF0V2BWm2/pdeu6T4kY2jk/2V0\nCTHXin+prmoR2e2X8G1Thz81utn3OVdYlyWvhGFFNkonpLY7aynb2II6lRLz\naRPplkcUSloVMSynnpufaICD0vacUCFsEA+QbwP3idIb+L/VaKXnC+qlcYhu\nAgRau8mMjUpnR6swMql21QWVhiJOYXnBc1doSedx2tcg8yIp25DlF6/nIaVv\n0YSa\r\n=oikV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0tmhQ76y/jqsNqAXFbjh2YIRY11EdoLBaymzsigGKigIhAJoGnBn0khBdrGn7n7uUPlglRdrlGs8e7Os50+GI7VFh"}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}, {"email": "<EMAIL>", "name": "wyatt"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_3.1.2_1561575072752_0.7324355121918265"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "3.1.3": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "3.1.3", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "8.x.x"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "e5f017c85953074c7ccca14ee5c32ffc458fd7b0", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@3.1.3", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JmS9/vQK6dcUYn7wc2YZTqzIKubAQcJKu2KCKAru6es482U5RT5fP1EXCPtlXpiK7PR0On/kpQKI4fRKkzpZBQ==", "shasum": "c7a02e0d936596d29f184e6d7fdc07e8b5efce11", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.3.tgz", "fileCount": 5, "unpackedSize": 9592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdS0iwCRA9TVsSAnZWagAAJaQP/0iBw2/bhpSP5gwdxES8\ngd084z2MysQ8uXhkdoe2cKfuc1umxnXgNuhFcox6KUfmC0MCpiN6CWd+mm6F\n0aD/Kubz5pp53cboR3Ud+ZyL+Sr+KaZI4Qc2IV3m0bnGnyDa5EXLAm7ohJRt\ne8hCdNX0dMbW1TNwSD/Kft7r+WBYQbcAS2Tp+WxdIYN+j40L5v6P4JVQHWks\ne/V74dKwDexOYYWs+uzVHjJODh2tovySsbRVVDEdRQt00v9QJ9vithEA5KNQ\npM8CY1zshEkNyMeY4Qs3LgAxD4PY/kzN9o/lAJnqltBqo032LQbH/sw8JFaR\nLhvgOs+IJ/Prryz9NkGvYEv3YPF6wpa12QCU/qyAZrzZpuUxJtx7RfxwFlo7\nwJkRMCKtHM5hWBICtAfcTLBi23mBwxQdsTNReFlKQ1Oc9WLn8B9N2aUXT6y+\nOd89s8RgM0X3MALMmr3vsT9rqQEs7dN8IH+6FwaAibGARDrirb8XyQpgwRAv\nNzoj3nbMPKuIoZotJ+tPGS50q3s30kUofDuIKD5rTluef/ZSnglYpnnM3dXJ\nOw9B9TOTSLLcz8eWhyanPpAQ8tHqG1hE/vu3ZE/2xl5o1aTclaHN1jeLiYtB\nm3UtyCEEE/EtW0fp90Eb+9DTawAkXp7wf2Mk79KmLhv6SaYRBz9BxvlB2snZ\nnQTI\r\n=kux3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEC4ElcqjHbYcNetj/nzm9v6rDHTV776PSwPFG81RbJHAiEAx9bvPSQSvHvrQDc2o3UDDDuWjFx3WajMTTVql4YRKNc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_3.1.3_1565214895316_0.86417596080884"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "3.1.4": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "3.1.4", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "8.x.x"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "80de44a1fcd30f75cb553db86f131489cc7c231e", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@3.1.4", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aVWQTOI9wBD6zawmOr6f+tdEIxQC8JXfQVLTjgGe8YEStAWGn/GNNVTobKJhbWKveQj2RyYF3oYbO9SC8/eOCA==", "shasum": "42e2fe36f593d90ad258a08b582be128c141c45d", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.4.tgz", "fileCount": 6, "unpackedSize": 10215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdg/6XCRA9TVsSAnZWagAAuwgP/inIMLnYKm1Xul57R8Ut\ndX2DrotaVOUh9iVDnVEggVCpIXDKvOYWCJ0RZ6nlQJ9O8WCb+K357cczqgIf\nHGtj/oQ4zivBwMjRclQoOXENwDkuKQh3OpmpycFxrt4RVwLJBzdoqY7+7+Vk\n6r21FWdlQex6g5ui4erz8QeBUHHA0CaHGNwEEdabis21vQQmfc0Jzm9Hw3Ep\nd2oL85/b+HDcauFfD1C8HaK09/NjSXW3hmYm+w6y3w+aKwK1karDxla4+DcR\neQKHs2z66iHbRMEf7VkRtsmLr+bSSck/XANC0OYS27ZweGyBy7X2VjzFlZYs\n+P2qnX8DVHjpF5J8yGcuaAoQCQzoEEHpFJvdqqkxACKUwlUYbUvOSb/Cw+oT\n3MrmyQAuMNV5kHUXdvP4CMPArC7iGzLrMuhZC4Nr7SHHo6l6OOt3lCuT/JaR\n5zrpDnZWLvw8iQkKYhPHVCesXnkkBfA+I11YbeujVH4HdXI8tHrHfH3yUxKJ\nxrj102hZxigMOuJDTe7g6q4zish8CAeafKZ2VZMSuzujhNfUTVqCB9Cwczp0\n43fg2uyXOvLEhJhnR3RH0mqS7oyu8uJBwoQumB7ZR1nLshvm0EmrNbARQ/9E\neNc7XUGzP7yYFnnKkfXeT2zstD/CJ6WKHmsXsV/M+IPnqgKF6DH8zqxEuMiL\n4qqU\r\n=jrf/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIERWlns0k6cd3XpeUz0utIcX/3NNi9Rso8i9zGtPorkoAiEAy7TjVtiJApwykDD/9yJAjNtKz42qfroYV61SqwM86os="}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_3.1.4_1568931479355_0.3396484855148858"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "3.1.5": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "3.1.5", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "8.x.x"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "2a524b6ad29b9f9f8a7c0f20dff4f4382e8bb5ef", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@3.1.5", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bi9m1jrui9LlvtVdLaHv0DqeOoe+I8dep+nEcTgW6XxJHL3xArQcilYz3tIp0cRC4gWlsVtABK7vNKg4jzEmAA==", "shasum": "3baea17e456530edad69a75c3fc7cde97dd6d331", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.5.tgz", "fileCount": 5, "unpackedSize": 8917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmWmnCRA9TVsSAnZWagAABHIP/3sPtr/PoJtADXcLCRwP\npyJ1uQQYJCfl4I4JuXisMp4Vt9da7qE8gg4+5m9LiZkw3fdju6r37XNgx9oY\nFFwns8W+QQRv2/2mM0koPb7806K/cXJirS3LasRSHP2yvpnCL+2NslmNCe/V\n6x/mLaqxkvQk2Ufi2B91ss7SbwfzsLwqFTByHiXkM6bGkyjkfJt61a6Yacl1\nppR5wv/+OVauBmlmAV7CoT/tic9kYst4YDRfWQNGwBdbuw+NglQouYqE8rfA\nwg/bWjBToG1PluaExi5CIwCvj0buDuqCClNkz84sYjdPsqfAU0iWoyfh1w8A\nVcQDDAtLP3wVviXzYeCnl7lZSae8mP5Kh2iXKVSxOOkiUQp4JyvjhHO64szK\nd54UGg8tEb0y3pbq1vx8aGEXRnA9gu2vvhUxwSbybBqr71A2pl6CUk+mksCJ\npE7Ut0H3d7KxC+YSpkqERnUgPkBzhyCBfSOSRqln2iBhx3CoLPTWrZQYAZbs\nl7Ve2xcangQZu2yNS6n5DJIpdpSyNoxFfkTvYRDEXpLP6SuUHOLowYdIPCCr\nDftakK+HEyquWeQpNik+HVYvavScgv0x8jPReodT+JDPcGn5Bb1dxuJ/uPUh\n/6Y6RtwzVeQhAbfw7c7LLgyyiHH+fzi8/dK2KnJNcDOgiZw5VMMBHNxrSVHW\n7CF8\r\n=szEm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFGfULwH2ZNYjMbQfhpo5kAgTld4s5PlpG7+/xqeJywIAiEAiW18KYVdo3GHr+4vcRRZGlGDeq10b8nx9PZGyY8QTwY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_3.1.5_1570335142505_0.06694420189880379"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "4.0.0": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "4.0.0", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "8.x.x"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "d485597533cd8698fde6db67bfdb29b9abd760a6", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@4.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XZGwokYlWTy86YQN0Iun8tWxOA8Zow640wPm+jfkN1wUzspVN305g+fhqoVlVxyH4bqeh6LFxWAUFDsmtR5jOA==", "shasum": "b123af1cede125b08fb5241bbc6e23f4d04b0230", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-4.0.0.tgz", "fileCount": 6, "unpackedSize": 11095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmWoGCRA9TVsSAnZWagAASk0P/RKRKa4LCCPRR17dLOG5\nxTldguJzq8yGGOpd211RhdMZHBS3FiKID8u3KiADfY38oY4KK/NWg/V1oyY+\nBdJHz4y79h58yrmX0XUjjKF/DRHXWIf4zQx/tvqIv7FaZAuMpLsWHuESAAn9\nsR+SZx/SyjB9wXv196m83MIRd2oS+lIWd5AYEfy61JTePW2LUz3RO8xxONt6\ngLdFECoXak6VRT4teybCyfQC3DHWr7bmVqPoxs7NWSevzkk7s/y7/dHFZbPp\nKKbqGalsguckEYKFf/jLwD2VZCvvjYGFHU3yagfuwxbujff7whlunOpN2W8T\ndCVshyaS3NG3lH3e9T1tWOUNk7LJhYxk4bFZOe7JpKOI9DGs7P0pHCJlxFZE\nkfr2f/nB5YuS9ssfwfCUHwkhnC74bqusxsXEIUdtRglEUPnnkoHHN10QXfTM\nWiyTGe2w1zI1tALIDWw5LS2VfelWcn7D69BKv1Wk+CTrO4JW/pPFzHKgBrcD\nZcZjW3RrS3btZ4HpoygfBywSFGVw+6OhzGTa3bnFeJf3uto3+JSm522aSL/U\nlmGjkcebOywcBl52c/+r8Uc4f97Gm3FqFkOBgHgUVc6W7rN40p1ncs6BATdG\nhyy4P3J0aeKU1TCe3RkLvpbVSjpfY+ArdleXkSKEWzgnqdZ3Bm4IqAzy2Tdk\nyDY4\r\n=r06U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJb3sRvACPNGrOrGBX+MUEHkkWWPn4/2ufzLggt6zAWgIgb8q7Az3WJfU7oMXyFbwkEYiqO+pIg6BfMI1vFJ7b/ec="}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_4.0.0_1570335237557_0.24658225016907243"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "4.0.1": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "4.0.1", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "^8.3.0"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "1eea19c0e9b02902dde49380821b72d9b41376ed", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@4.0.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-f0G5nx6AUrqoq/+yhZXdYaLJ4senRBgamxeUIzwgoz2c3CKofY9I0KdDuV3adfHaYBztXXVw1wPawUcIiZyDAg==", "shasum": "8e8f97ebd311593540f28222b3fa3543284baf20", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-4.0.1.tgz", "fileCount": 6, "unpackedSize": 10546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoOz9CRA9TVsSAnZWagAAqywQAISx82BA79aiyPYANL2Z\nnLZr3C3GTOjyCKx67PIUmi3gGsLzF2eYUgFVgwct8QjrihQBXP0qbH4iWbg+\nmoCEFuRjr9ZU0P1QKl4U8eBSX+vGw5CRoDwV82/VpDPrNWTfKuFUp5nZkhUD\n9mXLQRqE0CgjmH+nHgrPCrpDSTCRQ0ZPr/gk4GG6uBHRxFqc1sFXadilGSA1\ncGxn1imrmcBwG2K89yBPBL/4geefGCm1rJ8/JlsSJ4NeOs8XNV+dAxjDWR4R\nKisQeg2+lnsIxZd+/239grDKLxNenqCzZHWtUr9U76qHyOv0NYZNxwJu7yaq\ncG9k6o2RqjjmforfMPbKLB7yBzoo48T69U0CWRzADsaXLSL3JJfhXktWqqSg\noNbc4iSLFTriRYTqLkUl7bHPwSMCJD+NxhVBIfx5qb2ShUWsQ6jOhwcxW6K1\nGsjWII/LRtYYq+kfiln5q70EyNK9PQAp2VlbYGfaZZvqgAI5uPMvopAk48hx\nNtaW7CIwjyQYprMIAIDhRVGQmvm3v0huJjJtYwPqfiu35UUF5yCkyjSOprK9\nWmIMjnefMrtU26QjkaVPFv2vmZEtvl8DKcQwi0IixxInbJD1qmS0cCYSRZBp\nGpHDTVbsTbTyIyMy/To2v8D9AAXgbLntkt30oEeqoH+um0lbpZgtXaZZh7CJ\nVnUk\r\n=MBhT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAtYk1pDzt2eL8PnL20TsO10b6itj0GLh349Y+AXXDhxAiBCDaFcEKvQdnsSawWXwpE7zZ6hjJwD+9IDoGm0mjGt2A=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_4.0.1_1570827516269_0.18375587533571425"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "3.1.6": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "3.1.6", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "^8.3.0"}, "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "readme": "<a href=\"http://hapijs.com\"><img src=\"https://raw.githubusercontent.com/hapijs/assets/master/images/family.png\" width=\"180px\" align=\"right\" /></a>\r\n\r\n# @hapi/topo\r\n\r\nTopological sorting with grouping support.\r\n\r\n[![Build Status](https://secure.travis-ci.org/hapijs/topo.svg?branch=master)](http://travis-ci.org/hapijs/topo)\r\n\r\n## Usage\r\n\r\nSee the [API Reference](API.md)\r\n\r\n**Example**\r\n```js\r\nconst Topo = require('topo');\r\n\r\nconst morning = new Topo();\r\n\r\nmorning.add('Nap', { after: ['breakfast', 'prep'] });\r\n\r\nmorning.add([\r\n    'Make toast',\r\n    'Pour juice'\r\n], { before: 'breakfast', group: 'prep' });\r\n\r\nmorning.add('Eat breakfast', { group: 'breakfast' });\r\n\r\nmorning.nodes;        // ['Make toast', 'Pour juice', 'Eat breakfast', 'Nap']\r\n```\r\n", "readmeFilename": "README.md", "gitHead": "745941e1ac85502cefe25aa3c9ea4d1ca522f3cb", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@3.1.6", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tAag0jEcjwH+P2quUfipd7liWCNX2F8NvYjQp2wtInsZxnMlypdw0FtAOLxtvvkO+GSRRbmNi8m/5y42PQJYCQ==", "shasum": "68d935fa3eae7fdd5ab0d7f953f3205d8b2bfc29", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-3.1.6.tgz", "fileCount": 5, "unpackedSize": 9190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoO2WCRA9TVsSAnZWagAA2OkQAIT2aGyFP7k6JVmKMuRw\nHIduQD3jDPwe4x9dPMnoYYbRpiFk1SLMSp8yaGsQj3iV7cof/9MJSrN39tWQ\njZqnRlZlIP6uH0lhs/p3cWT+mCMwjBRb92ygr98ZC54inlfsgiVDo6W1hTJY\nEtHG+943u4N8P7uWr1waM2GdNgVSUaZpB7TcEJXPdQRDigH5Gl6yRgGboAbO\nIjYLiLD9UbtK73/gJVlx27E20b/+n2y4sJRySgEhyWjoGOrBS/+csal+N6ZA\nGQKkXvxsr1aovvkfb0WC1Sidw3o6KPYLiAY7pRR4kDEgPefGVpHtqFKknYlF\nt1XmdiGWGP6Cmu2q8SO10+pBPOtQRFRwlNyT9Zjt7/xPnVGzDMy9jSErnAMC\nPagDNM+vNbbS0H8RH9/1m9kw8zNZWjvFCAaG0tj0xsmqNxw7dN3ryn2bBlg+\nqOGMUxDsQPEF/bERK+6oFyIycq6Bd5+aZ0C3Ge3aJUM+D+YmJyVDAyfUaf1F\njW8uO+vu2doo1lGGFCCOmsrHl3x2+JhBkELirVs+KVz7uFTZWDl6SfkdXVbf\n7Q554PnvEi0NhBv+VI+C86jtAgedkcGuMzJDzT62R8muT7zII9XL/SItCEVi\nGQhCoyllXuB6W7bMHiIvBxvW8AfV6oDX9Sdf29Q5T3K+P7Qt6vIFGTmDVEPK\nCKxN\r\n=7Fj4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPpsI77vS7tl0I9RVk8GnUFi8ky1vYHR/vYO1GcU0UNAIhAJDdp6qyyS1tPLVtWFAf3WjRRLdQpBuSdjxIVNb8fvIr"}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_3.1.6_1570827669601_0.5161743314071856"}, "_hasShrinkwrap": false, "deprecated": "This version has been deprecated and is no longer supported or maintained"}, "5.0.0": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "5.0.0", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "22.x.x"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "abfc5ca5d16835e7591ab16b84bc36c55c13f6ee", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@5.0.0", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tFJlT47db0kMqVm3H4nQYgn6Pwg10GTZHb1pwmSiv1K4ks6drQOtfEF5ZnPjkvC+y4/bUPHK+bc87QvLcL+WMw==", "shasum": "c19af8577fa393a06e9c77b60995af959be721e7", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-5.0.0.tgz", "fileCount": 5, "unpackedSize": 10247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEFTpCRA9TVsSAnZWagAA26UP/Ah+mFniMWDUlKu0Fe+M\nTiynHAZe+uwRTUStEhjC/D0f35XZ32F205SBt7bNqWAprUj90jccfvsFn3U4\nYS4Q6ynjmkcm6KCkyQZQCIPN7wXWj46Z4NLiuskujp0w+w6L36ill5RuNw89\ni4w+ShypLCvRTbfGPTTHcMn7YGxcoIufHquhcFki76b8ijBxVh/WZPDCFB6l\nSQPBKlO9/gGHyWrVHeJq2POa8DAdSaVfKZ53ggDe4vWoYKRoXLoGwANstqoQ\nOtl42xF8OnmCQj/0JmQq7AO0WLOXElpb8xNsbhWSAlBjmOVqneI+g1okWiAa\nUQdUBnwLfsrZFNrvTJ8NvI/+GgjSxoiK6WsQitUV4ye9W5BSi8C59khpZu98\n8UoGMpJ5G/R6N6tuvYM/sSj+K45wnUi71D/UVtj2nifatb6GJZSB5dIqyt/6\nLhuXf63Dvgkpzx9/exYVLQFqh69EgbrNo2TLlhD78o8zGv8dSZcefjuhN83u\n4EKT+YXAnB75mspnwb2vDRJP3zXfmwZlnEI4eUubnUYhsPA60coZJGL5srY3\nBT+xkkIyNGjErMUem0Jek2gczp5bkjGQu80S5p9AglICat8JMFuvmvlZH0jI\n9t3Ex2HK7ynOpfyAGUjOJjPNWIzhrcVX/ZdfQZ9rdH9Cs43kCABwYMgDTVi9\nRRLX\r\n=sDGK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDRKNZgsJH+xnvsi4K1YF7enLXeTJ5P5R+SPQ8VvqWvqAiEAnLRD6VtTo82PvE7E8PXRKuOtdog+6iJKtX8mPWAhAls="}]}, "maintainers": [{"email": "<EMAIL>", "name": "hueniverse"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_5.0.0_1578128616473_0.6489766303951836"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "5.1.0", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "dependencies": {"@hapi/hoek": "^9.0.0"}, "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "~4.0.2"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "0e8fe262b06f9d2b258a0c796beb104b54b206f3", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@5.1.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.16.0", "dist": {"integrity": "sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==", "shasum": "dc448e332c6c6e37a4dc02fd84ba8d44b9afb012", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz", "fileCount": 5, "unpackedSize": 10720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2kTXCRA9TVsSAnZWagAAFGQP/RBBONb7XYunof94Xy7z\nHKAMWBuZQsw82QzjOSCW0+w6AV3OcULl1xCTSRCyOtBd4jDbd30vofZgBSXq\nLD4e9XOraiRaJWRtB0pvja1m42WE4ENK/DnHfddVP8jJNTHY14Rl6TdnJXpZ\nSdcHaa/k1uV1S+RsNu8szED1yCmqpmslTjj7Oa6nvjGwv6PTNigMcvrymO/h\nDcTEU2DS4dTvBfbM0b3h+6YJZLwMiUXjkd3YbF5QyBepK+SUJwF2o6vG6nAh\n8/615jmMzx7v45WXTWLUjPSn57FgDn7jPUVddu6e/79vPjH/MZmFhKekCgXq\n0cM8gfj3lY+2m3p1Prx4gHOmoxUYH10yU3ThCZRMf6NhvIO+Faej8K8MEFk+\nS2HtzrePkzb1nUR8rnfQpFSm7D7MYrn1dg6wUMWb91blmYlMX9GCMw60AlEA\n2IWvwUU81FYmVrVV2c4eJBKReck5kC6HT1eqQKJXOvLSA86hiaWxRbXB+dZz\n3vNdZWu8tCZKygH6zE/LfXfJDiIdiCP3nVQlk7kP77VMdQAZ4c6PtvDQ32mc\nuQKkMcgcp8XEm+7IIgXs7ZBZGaTG0OOP875A12gtrdHwakoBfz8PRJxS30dp\nw7tzY9eLHONTiIX7QtHUpOpyg5t26gIyDRzuDDp5ZZi7XhGKXE7AXJ4C/wZ2\n1ZaO\r\n=gGA1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZh9MjQqWsCdzcjkKU8m1d3KoSgiiiLOaDd/s1sQCl1AIhAMb004j88gW4c4Mvh1VVf5wT/sXwVNjziXzIrD5NMr6U"}]}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_5.1.0_1624917206824_0.3582494275095882"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "6.0.0", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^10.0.0"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "*", "@hapi/lab": "25.0.0-beta.1", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "2ccc930946f3879f17384eedcad8f26d13411677", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@6.0.0", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-aorJvN1Q1n5xrZuA50Z4X6adI6VAM2NalIVm46ALL9LUvdoqhof3JPY69jdJH8asM3PsWr2SUVYzp57EqUP41A==", "shasum": "6548e23e0a3d3b117eb0671dba49f654c9224c21", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-6.0.0.tgz", "fileCount": 5, "unpackedSize": 10884, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHNJ7usAk6CkmZ7IqHbf1k9lnCflrQYDHJmOg/BUGdYQIgZ5bLPtO/uINCMMHOZK35P2wDj35uxDe92hSrVAbadow="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicHkCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9AQ//R7GJGNbtUPCQkhCDF1yZHiy3slRWjc4gsuyJeJ9RuW4yixE0\r\n5FqPLXBp71dUfJy5sA0ujaZKku5UwhfPDQChUh69+To1W2oXVPnLQE4VSOGx\r\nmSuKbVJmGPxgMeUXeJ/c+it7myNV/wQszdC/psuqD3peDxSh7Z/Lw1QKINEV\r\n93+/hTUCmXocrIgHQc7q4xCZimUZgdlCEiub/VT7Ms+XCWKi3pCRK8RBSgfi\r\nzZPYgIRbNxBj414wFybjS6kwowv9tsnGM0ywdM5sjYLPGXF8i6TM3TzxnfOL\r\nJ4TEi/4sdkeRxeNRuMV51SsSL54BEBruu5/k1ytIIrztnGfhErXmAgb+8/N4\r\nllfjid3N+FsNlxaaWsHv02FcHikNOvvtkBiGEJW7AwatOQKTE/atVgmVejeb\r\nmRdicjwF/IE000aqlwzo8OMOiDv2n/n29gOYNXmmKlQXF7mJBdH6E0HAqT6D\r\nDL4153yBKgabUSFMNjMayO7VGraI6oTHHCbohTFtg5J+z+ifIPpSeJawKBC/\r\naHTHMvq1f8kJP/WOs9eIb3AAHaxbUFY/XE/erLR2PsMttJIdscxJuig8LiF9\r\ntPMeitPJOnQ7BS4WNjIGxYHqXo+3/k/7Bo5iDkkI6GTmLa4CrFiwRAtSu9BT\r\nCeLx8uy0yV11mEYLv1dXdw7svJNxXNrs+Ik=\r\n=zezG\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "devin<PERSON>y", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_6.0.0_1651538178630_0.6954276990773658"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "6.0.1", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "bafcc8d8362155575de79a4d2bfad6cab6f9b5e3", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@6.0.1", "_nodeVersion": "14.20.1", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-JioWUZL1Bm7r8bnCDx2AUggiPwpV7djFfDTWT1aZSyHjN++fVz7XPdW8YVCxvyv9bSWcbbOLV/h4U1zGdwrN3w==", "shasum": "c0f9bbbc3f98bd60c5ed825bb70f3a92b3a6ea3f", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-6.0.1.tgz", "fileCount": 5, "unpackedSize": 10874, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6boWZ/EtAwJkCIByFAK1ke9JGcUUyg4p9UxfH4104BQIhAJJYtVTrfDPzXR8cLzcKf69dTT/W4jF4VPwqUTA7EuJT"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5+DqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEcQ/9GnpYLOLboxEYuGn2hvC+FhsFIVXPcKeOUIFmCFMKHaBbnOPu\r\n+LAUajpfajcUaJ4lubcS+/h1dvUgjITw+NEt6h8+ru048QeJpdMvY2wDx1sd\r\nIa6s/eFL3lyzLny+/ziRI3E/kkzzKYbciHtxV/rjRWktAlezSqG7SOL2DuTJ\r\nqlw0JJhEh+Hx2AY+1/QJ51B3h6/ATV/8ZbOb4tSkSFRd7sHu5BOP0P4jz+6n\r\nX+tWtxoKPdvYY6yOiZKOJEf7+PkrfFGCkAuXpBAWyVtNWXD3QRf7qLy03wxj\r\nCqEGaw6cdayi+jIfTul+TD7ez27yQLKypecCznobo3Eqz8x+8apHsg+Nx/ef\r\nd04ZoHTW9SpFF9bg4MY8AaLJ/l4ZCBOsWKDJGrCSaLqzfcM1EuempPYjm49N\r\n2vvCAcFtGGdK1VGWRq+vYrFUsgoBCT9k0ZrWFK8VWveVbHaeR1ZWXUt2gfIP\r\nXiqGCgs3A81wuQCOfJAi5RGQ7i50YPBvwN7IO6w2sXL5zJjLVdP6XLknCKJT\r\nRvq00KmSCzTYMGpyXU7rN61wWqhxuvbTSwIx0+3Q/5gdd44oFhfKH4/4Qe5S\r\nRaBWs0L1R2M3NqPVmhz7ciGTKWLq9V7SE9Y3xVp6Jd5NY6i0vti/+jYEu653\r\nUn9MDhmEGqqW0Sl/qgukbi2g4r48QOyxn6M=\r\n=RyYt\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_6.0.1_1676140778187_0.19432511189846968"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "@hapi/topo", "description": "Topological sorting with grouping support", "version": "6.0.2", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["topological", "sort", "toposort", "topsort"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "3c4ee008ec9f719b0992ba06feb35c24b57787d7", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "homepage": "https://github.com/hapijs/topo#readme", "_id": "@hapi/topo@6.0.2", "_nodeVersion": "16.19.0", "_npmVersion": "8.19.3", "dist": {"integrity": "sha512-KR3rD5inZbGMrHmgPxsJ9dbi6zEK+C3ZwUwTa+eMwWLz7oijWUTWD2pMSNNYJAU6Qq+65NkxXjqHr/7LM2Xkqg==", "shasum": "f219c1c60da8430228af4c1f2e40c32a0d84bbb4", "tarball": "https://registry.npmjs.org/@hapi/topo/-/topo-6.0.2.tgz", "fileCount": 5, "unpackedSize": 10871, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCT3CPH90d4d7lgDyc6dWQC51d+eGsP5y6yqEdhwoIezQIgWMbc9Do/KwSglLYhh0A9xTsVJpDdyO1+K4GbrpaMyZ0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDGDoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqk6hAAn2OoobymSpPQKYxv7TsbSpioOzjSQVAz/Zb/cFB6r+SiUEWg\r\nIPoIZzAYNUY5kdumOLuKsqvJsKXpTzRy7UMM3tZqTHkd1u2t3WSv5SoHyfRs\r\nY76t5e+WHfy8ji+qgmUHI6rCVJytnDczIfe7zEjVg7LXdyK1vBz1fv1u5GAW\r\nJnn/JtmHMtETi2L5z+aRP4w0jxJtDOKxYZYuaUm+K6q3r9w2TnkY7vpgSUQH\r\nLwaAWlxy42JFy4vOBfK7EbwVdZw1+s+8kTSHY2DiSl8ruGWbDW6vWooROd6w\r\nPyjE5jpxuDdjDm1o+TT40PKjauk0mi/il6EVoAjZ5beMZnArqkvRFqPSx1l1\r\nwUi3dPS8TQ7chH9hZuU2sepwZjb+63iTsetEqI8DtaxebfDHsK5ehUOzGB4t\r\nlZzgsft3CDaueqvmX+34sTeaFATLAuVwEgNawHf87QoXxuS7gi5ADHY1InOL\r\nn+E3ad7X7DsaS6r5pQ7eHDEKFayXt01VB3/OXnJ2j1rH/ELmlfheK3ACy6iD\r\nAuV1mgBnUHdOxrjmBAWK1C+dbaWZq9JYl8yzhYImnBcn4bfhZYRKKUe+gZrl\r\n+f74S0VZIKVKTn8LJi6EdcZOgrM3txfklbtoLBJkSrZdsvPMv6Q4OprxyCcx\r\nqd3oeTVse9xX8+xKXVE0Qq0QAmqMcpCpVIM=\r\n=Spyg\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "marsup", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/topo_6.0.2_1678532840562_0.6442720896004142"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-29T19:59:13.655Z", "3.1.0": "2019-03-29T19:59:13.950Z", "modified": "2023-03-11T11:07:20.874Z", "3.1.2": "2019-06-26T18:51:12.881Z", "3.1.3": "2019-08-07T21:54:55.634Z", "3.1.4": "2019-09-19T22:17:59.485Z", "3.1.5": "2019-10-06T04:12:22.635Z", "4.0.0": "2019-10-06T04:13:57.709Z", "4.0.1": "2019-10-11T20:58:36.374Z", "3.1.6": "2019-10-11T21:01:09.723Z", "5.0.0": "2020-01-04T09:03:36.791Z", "5.1.0": "2021-06-28T21:53:26.965Z", "6.0.0": "2022-05-03T00:36:18.808Z", "6.0.1": "2023-02-11T18:39:38.352Z", "6.0.2": "2023-03-11T11:07:20.741Z"}, "maintainers": [{"name": "c<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marsup", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "devin<PERSON>y", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Topological sorting with grouping support", "homepage": "https://github.com/hapijs/topo#readme", "keywords": ["topological", "sort", "toposort", "topsort"], "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readme": "<a href=\"https://hapi.dev\"><img src=\"https://raw.githubusercontent.com/hapijs/assets/master/images/family.png\" width=\"180px\" align=\"right\" /></a>\n\n# @hapi/topo\n\n#### Topological sorting with grouping support.\n\n**topo** is part of the **hapi** ecosystem and was designed to work seamlessly with the [hapi web framework](https://hapi.dev) and its other components (but works great on its own or with other frameworks). If you are using a different web framework and find this module useful, check out [hapi](https://hapi.dev) – they work even better together.\n\n### Visit the [hapi.dev](https://hapi.dev) Developer Portal for tutorials, documentation, and support\n\n## Useful resources\n\n- [Documentation and API](https://hapi.dev/family/topo/)\n- [Version status](https://hapi.dev/resources/status/#topo) (builds, dependencies, node versions, licenses, eol)\n- [Changelog](https://hapi.dev/family/topo/changelog/)\n- [Project policies](https://hapi.dev/policies/)\n- [Free and commercial support options](https://hapi.dev/support/)\n", "readmeFilename": "README.md"}