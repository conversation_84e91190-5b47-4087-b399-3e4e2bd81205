{"_id": "path", "_rev": "137-e862ef6fbb5c05f0091337d4d10a66d1", "name": "path", "description": "Node.JS path module", "dist-tags": {"latest": "0.12.7"}, "versions": {"0.4.9": {"author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "name": "path", "description": "Node.JS path module", "keywords": ["ender", "path"], "version": "0.4.9", "homepage": "http://nodejs.org/docs/v0.4.9/api/path.html", "repository": {"type": "git", "url": "git://github.com/coolaj86/nodejs-libs-4-browser.git"}, "main": "./path.js", "directories": {"lib": "."}, "engines": {"node": ">= 0.2.0", "ender": ">= 0.5.0"}, "dependencies": {}, "devDependencies": {}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/path/0.4.9/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "path@0.4.9", "_engineSupported": true, "_npmVersion": "1.0.15", "_nodeVersion": "v0.4.8", "_defaultsLoaded": true, "dist": {"shasum": "380c68d01273e43f9368d7ad50fee5e3e8d477f0", "tarball": "https://registry.npmjs.org/path/-/path-0.4.9.tgz", "integrity": "sha512-BNeSpoSH+KgIySxVr7QlMhZBDudpRguEJKi/+Iv1iU1Eb2Ub2JV1ncHjb4bwp+3kamVglSLIk9Pu+kmbA45bkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHM9qqdDpQqA60qPIt6x5lsoR+Gu3YbuxGTRa7CoZ3ACAiEA2z2IBSI5DSgU7ovqBqm9qq9+IEoBLUcy1r6fR/H4YwE="}]}, "scripts": {}}, "0.4.10": {"author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "name": "path", "description": "Node.JS path module", "keywords": ["ender", "path"], "license": "MIT", "version": "0.4.10", "homepage": "http://nodejs.org/docs/latest/api/path.html", "repository": {"type": "git", "url": "git://github.com/jinder/path.git"}, "main": "./path.js", "gitHead": "c88a5778a572deb548fdb32fd141687fc1726b9a", "bugs": {"url": "https://github.com/jinder/path/issues"}, "_id": "path@0.4.10", "scripts": {}, "_shasum": "22fef27b7cd6eaf30fb13fc027801e956e518ef1", "_from": "./", "_npmVersion": "1.4.28", "_npmUser": {"name": "jinder", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jinder", "email": "<EMAIL>"}], "dist": {"shasum": "22fef27b7cd6eaf30fb13fc027801e956e518ef1", "tarball": "https://registry.npmjs.org/path/-/path-0.4.10.tgz", "integrity": "sha512-OXGpluyRVdUQP5fOGYMsPQo9eba09SmxaFMpM+h61gp1Mz55vhBVJt/Q3cIxAUQ17sIJn7KSwZ2cbmwRRqS1Rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHm8LwlhRxsGzL+x7ZCg2fK0xHkmw5v3DooNIkTt+RDqAiEAu0mL9wF7OFGPBh60A7Gh+zHEZsB4L7ei0/NkXO5ENuc="}]}, "directories": {}}, "0.11.14": {"author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "name": "path", "description": "Node.JS path module", "keywords": ["ender", "path"], "license": "MIT", "version": "0.11.14", "homepage": "http://nodejs.org/docs/latest/api/path.html", "repository": {"type": "git", "url": "git://github.com/jinder/path.git"}, "main": "./path.js", "gitHead": "a723663db8388017634a2d854aca09532b12befa", "bugs": {"url": "https://github.com/jinder/path/issues"}, "_id": "path@0.11.14", "scripts": {}, "_shasum": "cbc7569355cb3c83afeb4ace43ecff95231e5a7d", "_from": "./", "_npmVersion": "1.4.28", "_npmUser": {"name": "jinder", "email": "<EMAIL>"}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jinder", "email": "<EMAIL>"}], "dist": {"shasum": "cbc7569355cb3c83afeb4ace43ecff95231e5a7d", "tarball": "https://registry.npmjs.org/path/-/path-0.11.14.tgz", "integrity": "sha512-CzEXTDgcEfa0yqMe+DJCSbEB5YCv4JZoic5xulBNFF2ifIMjNrTWbNSPNhgKfSo0MjneGIx9RLy4pCFuZPaMSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGWu8WQeTWD4XMPQGts13HCM/fRGAnBxuAxlpKylus73AiEArE3MiOWS53xYShVtnqe4KYpXlosrZChRQVh+vHZGADk="}]}, "directories": {}}, "0.12.7": {"author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "name": "path", "description": "Node.JS path module", "keywords": ["ender", "path"], "license": "MIT", "version": "0.12.7", "homepage": "http://nodejs.org/docs/latest/api/path.html", "repository": {"type": "git", "url": "git://github.com/jinder/path.git"}, "main": "./path.js", "dependencies": {"process": "^0.11.1", "util": "^0.10.3"}, "gitHead": "7fbaede3ca9d224494cbdd47d7ca803ee96d2055", "bugs": {"url": "https://github.com/jinder/path/issues"}, "_id": "path@0.12.7", "scripts": {}, "_shasum": "d4dc2a506c4ce2197eb481ebfcd5b36c0140b10f", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "jinder", "email": "<EMAIL>"}, "dist": {"shasum": "d4dc2a506c4ce2197eb481ebfcd5b36c0140b10f", "tarball": "https://registry.npmjs.org/path/-/path-0.12.7.tgz", "integrity": "sha512-aXXC6s+1w7otVF9UletFkFcDsJeO7lSZBPUQhtb5O0xJe8LtYhj/GxldoL09bBj9+ZmE2hNoHqQSFMN5fikh4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIARO0X3kZ42htSUwZXhGG105L5JYg7Byz79zlRExvkvpAiEAptemUHPAjMTKrpb7gHqVM1sTLxu3FY72onbwl7T/c/k="}]}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "jinder", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "directories": {}}}, "maintainers": [{"name": "coolaj86", "email": "<EMAIL>"}, {"name": "jinder", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "time": {"modified": "2023-05-20T13:42:09.760Z", "created": "2011-06-29T23:52:16.174Z", "0.4.9": "2011-06-29T23:52:16.548Z", "0.4.10": "2015-01-07T21:17:05.282Z", "0.11.14": "2015-01-07T21:18:43.564Z", "0.12.7": "2015-09-13T15:17:56.689Z"}, "author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "repository": {"type": "git", "url": "git://github.com/jinder/path.git"}, "users": {"326060588": true, "m42am": true, "adilapapaya": true, "ubi": true, "gregbradley": true, "unclesowise": true, "kingcron": true, "yourhoneysky": true, "tophsic": true, "amirmehmood": true, "asmolyakov": true, "jerkovicl": true, "mmercan": true, "goodseller": true, "arahnoid": true, "kubens": true, "wkaifang": true, "mofies": true, "koulmomo": true, "ifeature": true, "kontrax": true, "arifulhb": true, "choi4450": true, "nukisman": true, "edloidas": true, "arthurvasconcelos": true, "vwal": true, "jclo": true, "yuxin": true, "pdedkov": true, "evanyeung": true, "monjer": true, "muzhen": true, "luzhenxing": true, "sternelee": true, "igsys": true, "iori20091101": true, "zithan": true, "juangotama": true, "mark24code": true, "hongz1125": true, "liu946": true, "manux": true, "scotchulous": true, "christianjames": true, "tmurngon": true, "jacks": true, "rsaa": true, "xiaochao": true, "sparkrico": true, "tdmalone": true, "abdul": true, "dzhou777": true, "jmsmrgn": true, "maxwang": true, "ffeng": true, "krabello": true, "jjonathan": true, "pavelusov": true, "4rlekin": true, "latinosoft": true, "chinawolf_wyp": true, "toledano": true, "u.turkoz": true, "arcanedev": true, "bphanikumar": true, "sibawite": true, "binfahad.afa": true, "abt10": true, "metaa": true, "karzanosman984": true, "hyungdookil": true, "_~": true, "sayansaha": true, "jakedemonaco": true, "svoss24": true, "nazy": true, "austinfunraise": true, "kevin-wynn": true, "leor": true, "ik_make": true, "razor164": true, "nguyenvanhoang26041994": true, "rubiadias": true, "edmondnow": true, "juananto11": true, "philosec": true, "anatolie_sernii": true}, "homepage": "http://nodejs.org/docs/latest/api/path.html", "keywords": ["ender", "path"], "readme": "# path\r\n\r\nThis is an exact copy of the NodeJS ’path’ module published to the NPM registry. \r\n\r\n[Documentation](http://nodejs.org/docs/latest/api/path.html)\r\n\r\n## Install\r\n\r\n```sh\r\n$ npm install --save path\r\n```\r\n\r\n## License\r\n\r\nMIT\r\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/jinder/path/issues"}, "license": "MIT"}