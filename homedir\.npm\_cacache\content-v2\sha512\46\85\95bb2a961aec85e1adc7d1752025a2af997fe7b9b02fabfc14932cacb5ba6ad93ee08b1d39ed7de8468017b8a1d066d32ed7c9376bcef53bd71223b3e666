{"name": "en<PERSON><PERSON>", "dist-tags": {"next": "5.1.1", "latest": "7.14.0"}, "versions": {"1.0.0": {"name": "en<PERSON><PERSON>", "version": "1.0.0", "dist": {"shasum": "853f6bc9ad272c00f1a630018244856d377f735e", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-1.0.0.tgz", "integrity": "sha512-4nwamM+ezFjBYBp0aVOzZRpjLGS2eKV2iucxbunyb6Xb/QwuOfHBSIetRgN1jDFYIiIJDqPvG8/8/kLTrku8UQ==", "signatures": [{"sig": "MEUCIQD4y6Lq9g0htenfj2p/hk7KhONu1zjFZ51kkOzw5MQ/WgIgKU3eL19wx0fMgE+glqy5wzn5ztLVKx9U8UexIAF1dio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "en<PERSON><PERSON>", "version": "2.0.0", "dependencies": {"chalk": "^1.1.3", "os-name": "^2.0.1"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "node index.js"}, "dist": {"shasum": "c0ba534b89e843b442481a89cb92f6b65f441b1e", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-2.0.0.tgz", "integrity": "sha512-4sXCVAmPZg8LlsIMa07mUkptOiRFdD9/gF0GDqPP7mEbqcUnzoX96H/vFrRYoo7SdEI+idsXfxN5rmcn6erjdA==", "signatures": [{"sig": "MEUCIADzzd8rC4Sxn1gd2wZDydv8IDXXBEg7hlwRpyVUgTF8AiEAz0U7OOp1nMIAy+vFGNSp9VnFDKb2rJ5LI+jABULvS4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "en<PERSON><PERSON>", "version": "2.0.1", "dependencies": {"chalk": "^1.1.3", "os-name": "^2.0.1"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "node index.js"}, "dist": {"shasum": "35839e69d7d790fef34179fc97a2cb6004b46a63", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-2.0.1.tgz", "integrity": "sha512-RuVuCWAhgUY9GDA08HNRGw/+jfAbve9JnVCt60LlfNFkKl2qO8T6zQIAbRu625Lo+g6jDbt8mgCrsA2BUtaNnw==", "signatures": [{"sig": "MEUCIQCvIoYDDJmKSjfrH/OcI/qlOOJhf2yneVtxsththsub0gIgLNeF0WgYq+qfpHscLevTDokECWjhlCYPNawOYQlPu2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.2": {"name": "en<PERSON><PERSON>", "version": "2.0.2", "dependencies": {"chalk": "^1.1.3", "os-name": "^2.0.1"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "index.js"}, "dist": {"shasum": "064b866d8bd3c2c46dd7201ee635fcaadb5574e4", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-2.0.2.tgz", "integrity": "sha512-IXHaF+scEmT+nmDxWN4ar9WDdaaXJKcBRm2g9sCqTrZLvVyjdvr4q89faYN2fbuxxSw4UkWkGcurBWGyQAAO/w==", "signatures": [{"sig": "MEQCIFE5mm3Ul3VMX7HVFjD0rCLbwIgr/ehM5r4gibIY+jkHAiAH6Pv0Wj+ECFTJ7+BPma+J9kM6JoE4IneRgO9cDCTBhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "en<PERSON><PERSON>", "version": "2.1.0", "dependencies": {"chalk": "^1.1.3", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "index.js"}, "dist": {"shasum": "d107f52187a27e50b2fd277cbc38997fcf349cb6", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-2.1.0.tgz", "integrity": "sha512-DB3P/8fyWNeNwkEUwuHvla20Hb28OVOGnegKyo0+iwCWNfHpDi+COBQy0rp7udAxrRZtQNMSsWnVofMfFKNPzg==", "signatures": [{"sig": "MEUCIQCEv57AM6rdME9sl9MCqJtUgrejI3+TfU27suzSfAk/KgIgYflWr3u+cBs1sxO9JZFsZV0eApKjymlgHDUvWAzP+/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "en<PERSON><PERSON>", "version": "2.2.0", "dependencies": {"chalk": "^1.1.3", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "index.js"}, "dist": {"shasum": "e14a01702da2864ac0da7c6377356434948f06e3", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-2.2.0.tgz", "integrity": "sha512-V+WZrkVbH5qYPSFYFYtz5cphoDFbRddzphsFZWLALEh+59FV0HV9ok+VR7wdoW7CDIZjFQ4pBrbliiBqHhOAiw==", "signatures": [{"sig": "MEUCIDtmBWDRlUIfn2JtsB4cwF636vszfbsN9cVD/EPdKTC2AiEAmmM5pPU5dZIKaIpdlZzaxaayT34jfULoEWGl3QHabik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "en<PERSON><PERSON>", "version": "3.0.0", "dependencies": {"os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "5d745343d654112220c54a6fe7a9777c1da3821d", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.0.0.tgz", "integrity": "sha512-CjRKDme+WazFo0atVRpjobrl5Kgl1mHiMQNIlKddiEeQimYfV+rsCBBoWZEDjpfFeLWbFpmF6V/2zKsD2J+wgA==", "signatures": [{"sig": "MEUCIQC2G3gXKuDCKs09pnznqXyfMB/tSSSpCIKmihf8GMO3MgIgASjKa45NSn8XJiXB1/gGBI3Tjq8/dV/G5CW+6SHmDkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.0": {"name": "en<PERSON><PERSON>", "version": "3.1.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "cbca5523cc47f2ce37df7b3ddf1c583ab7bf6d8b", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.1.0.tgz", "integrity": "sha512-lT1FwTs53MD8XDFU7sWlf1h6hf4hZDBBow0xM1PHXn+wXAXUMXAvs/GDREdKPfElbPLaT5MJUzjZsEtL+0+hoA==", "signatures": [{"sig": "MEQCIF/yzaPv9ehLjCVGO5XMrkRcajowHuAM/DrV3q2+4toyAiAYhWmNYzxx5+OZL3Zf10iFgXbJeNDCDma7YAjUBzHTsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.0": {"name": "en<PERSON><PERSON>", "version": "3.2.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "fc743f5d47add83748c95958bcbc7e9ea01723ea", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.2.0.tgz", "integrity": "sha512-vse48AIgsd+rL1DqCbJpD1ddQTB5TZ7AVhd5xQNCrXeGoc/2PumKu27n72D47PSyktP27PaTzPCR5mi8BvE5lA==", "signatures": [{"sig": "MEQCIBxoKbQg5wZNTyl/GaMC/oBwp1PUbwzG9Y12jnySSSpaAiB5NwBfpVK6DfN8UAr5cPtGf9eSd53C8hWmzUoqTD+kFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.1": {"name": "en<PERSON><PERSON>", "version": "3.2.1", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "8b8bbfc67ed644ff2ddafd368f2245f1c784a29e", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.2.1.tgz", "integrity": "sha512-sIKiZAAGR9FmCXmOhkG1T1S4JnxJDvcxngsmG2MULRJ6Ds0riRb2U6hB72kcj5+ngHl4KDk+Yqz6L+mEuPoKcw==", "signatures": [{"sig": "MEUCIHJlWtFh54QVHvHKzTBd7C420HLqrKjiKHq+zEriFEPsAiEA1JV/ESoGK1Pu+Z2XA7dn0h/yMvsX1abm6k6Wwrh6nUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.0": {"name": "en<PERSON><PERSON>", "version": "3.3.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "f54048ead2ac82724e28117911956c933d0b2869", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.3.0.tgz", "integrity": "sha512-MKZOZBP8RfTIiLuNiURIjvGrpFa1/j+oSYAuVSQT0BZM5DS9tJ9fjeEFbA72VLaj7Wk3v2t37yd1KN2UE7iqkQ==", "signatures": [{"sig": "MEUCIQDl8f/3Rd3ikE20Y6sq388a6cMjFmQ0oAmOI3Ad1zfi3QIgeSSmxh0bJY0V23zU7SgBjMlUKUITlQ31rCLUs39HSfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.4.0": {"name": "en<PERSON><PERSON>", "version": "3.4.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "28bca896ff654efaa34b42f4f62d6baa1a00ff03", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.4.0.tgz", "integrity": "sha512-EWVVSIJ/hlnGSu42Kup+Ml5ujNRF3ElgxQBZtjgyOvQ0W1jFoO4h1XF/67NQxz3tl9NTJbbJqAFgldaZsumX0Q==", "signatures": [{"sig": "MEUCIASX11W1Cj+TYbXtCcpPPzgqVqMlv2W4D2mzD/3SJ7wuAiEA+dYkW5FE6bVBW9XWjPl3ehtrmSlABxV6L0Eb/pej6EM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.4.1": {"name": "en<PERSON><PERSON>", "version": "3.4.1", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "8c80e9f2eec2cd4e2adb2c5d0127ce07a2aaa2ae", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.4.1.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>up64JfkHdfaARGQMu9cZxMvu7ZwCXvqwJeBv/6jAxytYQWSDmzoIK+r1du7bZ9uNS5YJfQbHL2fnlUtrw==", "signatures": [{"sig": "MEYCIQDhzZqsujyY9AsK3czOrnGjIrFSZ5oGlrsm4wCwYLE1tQIhALvfLpw2+9jCpIqdAj4qCnj5gRXVPc3w6NMmAKeyGbX4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.4.2": {"name": "en<PERSON><PERSON>", "version": "3.4.2", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0"}, "devDependencies": {"eslint": "^4.0.0", "prettier-eslint-cli": "^4.1.1"}, "bin": {"envinfo": "cli.js"}, "dist": {"shasum": "f06648836155b81e1d7b4a1c3fca3f6b5f38789b", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.4.2.tgz", "integrity": "sha512-yqKl+qfQ849zLua/aRGIs4TzNah6ypvdX6KPmK9LPP54Ea+Hqx2gFzSBmGhka8HvWcmCmffGIshG4INSh0ku6g==", "signatures": [{"sig": "MEUCIQD3gh4G+PaED8S+x2Xk1JK+LXsrgCbgJHcLuzssSb7frQIgIcMPncLhEiCBd4KmqByES8hsF+b1sCm4wJt4Nph1lVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.5.0": {"name": "en<PERSON><PERSON>", "version": "3.5.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "2c88fb33a0223c19f42ced23c4cf223393d236e7", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.5.0.tgz", "integrity": "sha512-iZvtXhsycSwLkdU6p3gVJHsiJkHK+xfJ9Sr4LNc88xHfTT2SoZjAC7NN04MIR3FhOsyXlOUw2/4PqlEmVPdLlg==", "signatures": [{"sig": "MEUCIQDyuek2OA8KJwsHzm9eIFNMK0ZbV4oVzE2z9uxIYch37QIgMRwDwa+yeOffJSp2SD7pzkztbA8ENNKTwtXc4Zxmub0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.6.0": {"name": "en<PERSON><PERSON>", "version": "3.6.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "510168e3143e3d4fdae22e9951ba6e627227276b", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.6.0.tgz", "integrity": "sha512-<PERSON>f<PERSON><PERSON>uiaWCbd4eR9/uYqcVjz4SstzJcBfG1vf68mSV5q/TXLy7qHFDyv+ATiu1m+Ot7u9MyjWyQPMAg3f9V7tcQ==", "signatures": [{"sig": "MEUCIGi0i7fD+J3uEF57VyBApw/VcAX4MAZlS5hqlRYRgMUgAiEAngVNzpj/nxNc0n2bjd7p/yUoYUzbOzDrmbrtBx+O6ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.7.0": {"name": "en<PERSON><PERSON>", "version": "3.7.0", "dependencies": {"which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "9b0e39f5b6e04b8e14aab32fe0056c362d3daa62", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.7.0.tgz", "integrity": "sha512-UIcudhq0ra3YrNrYDqKUrXn7pg9PCfdAsBqdMFXc4sikkOPUwLB4baRY0JP6Mhh2cgskrUNMVdg32/tzknmWEg==", "signatures": [{"sig": "MEYCIQC6EC3EAldviQmzcwcyjN+UXympA1MFnLs+z5QqNuV+rQIhALQChKLIqp/l8p0b9tDc1j0tIqLl5HE3/2nyCYGynnHp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.8.0": {"name": "en<PERSON><PERSON>", "version": "3.8.0", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "f877f21ca6f0ed59adc1d0f90fdd6a66cb310172", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.8.0.tgz", "integrity": "sha512-SU3rfLI0XuUOD1O2QKIi3FE67rFQJMR0zkg/mRqwkrD5o35j8Xoa8QnRgeECY5mQWtCcOfQjAHn0qZ1qtPYpwA==", "signatures": [{"sig": "MEYCIQCSkPlQgrvXCgMWr4nfE6XODxJByQCvfxjZds1t72c7sAIhAPwIzcTU+a34k4HT/u9UqolhGLGwmUg5rm+F4q1dKzUL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.9.0": {"name": "en<PERSON><PERSON>", "version": "3.9.0", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "eea5c3d5bce44ee9fc9a345ab9f44a616f45552a", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.9.0.tgz", "integrity": "sha512-1X9ih+PmP5GiiKkiBmd9IcZj4BVHP0VMCiO6Q8jgoCuu+HntG5PImBUIc7rxO5O779Mg8Hl2MfgKMNFtQ0mR5w==", "signatures": [{"sig": "MEYCIQCKGH+Que8e+QU9k2sRcDcnzClVnHBGz3OUNeHHSaFAlwIhAKO6tzDvYI/Z1Rl7HjR3CCSlzF6F9CyOmrkOpcFwKV29", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.9.1": {"name": "en<PERSON><PERSON>", "version": "3.9.1", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "78d5ccbea17ca2daefd8541561d1273813b96c4c", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.9.1.tgz", "integrity": "sha512-WDDzKLBbQT3PEhKzKRDABFyOxhoiQyHxGvwLtL3ONV68GWsPqeZ5fQKDEutRZ0mAZ5q40+gHOwwZYf1sh57m5g==", "signatures": [{"sig": "MEUCIFZCJ53kISVySqXxpBIGqlvB65GQWi95yMxIT8MgrwKNAiEAj0Ch8Up4f2T3Qii0hDoh61Rrj0AuOIameL9fJtd62ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.10.0": {"name": "en<PERSON><PERSON>", "version": "3.10.0", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "copy-paste": "^1.3.0"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "24b52a5c19af379dc32465d1909e37344dc41c20", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.10.0.tgz", "integrity": "sha512-7m6zSyFfEb3lAjZI217G1XVSAkYeFJHk2EqAVeoncrt+WtHddW4nnft2qPg82Xu1aB/T8nC/DPvkGgUUahli4g==", "signatures": [{"sig": "MEUCIGuuZqv1F2tgdhRnQMLuL+UX/zttQC+8KCOn5slYtkwXAiEA9heha7L7uZZnyigDEvU3svcL+D9FUAdmgdwEjaOyqmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.11.0": {"name": "en<PERSON><PERSON>", "version": "3.11.0", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "7bde377e478bf14835412f7b05e7c6b473cb734c", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.11.0.tgz", "integrity": "sha512-jhE0+XzBaKIktPEfvAyMpWZK8bq9tMYKVYKFo7x+kRbDZtyifD6EtOfzFkdb0qpQTTlDczDHlDgptiLM4/azFg==", "signatures": [{"sig": "MEQCIGt2Ky5A9WZaSKL0ge9Pwr1j2C+cbg3nnnS/nz0UiAWeAiBD5TlPILSsd1N9QdC5Sy2d+azNcp1NC01fwbJ2F+dN8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.11.1": {"name": "en<PERSON><PERSON>", "version": "3.11.1", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "45968faf5079aa797b7dcdc3b123f340d4529e1c", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-3.11.1.tgz", "integrity": "sha512-hKkh7aKtont6Zuv4RmE4VkOc96TkBj9NXj7Ghsd/qCA9LuJI0Dh+ImwA1N5iORB9Vg+sz5bq9CHJzs51BILNCQ==", "signatures": [{"sig": "MEUCIBBgWTVX5/9229KWmegaG1bTBINC90g+SBy24zMBrktsAiEAp2/0GN2jUob0IT1Ar9bLkfNcM+aYOk2dVbPTAVVyrEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0-beta.1": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.1", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "835043ee8814bb3fd98e50b2a5795c32a70796a9", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.1.tgz", "integrity": "sha512-E3eniX+mqL+3KvNfSrkAoeoRf5V9KuyIsBfxQDzgJW4GPatG1YP/BcMPb1uJdI2h6nFeB3bQR2+Tx32okh2QnQ==", "signatures": [{"sig": "MEQCIAD1txIGp66OqVIv9x3v9hC0FoSrY0upxgyF+cDwNir6AiBwM445a4T4KsBq/vjkeSLUyR62N/2GZqcrJkqJnZ8/Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "4.0.0-beta.2": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.2", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "55fb0913cddacc6e2747e5f69eb6deb3200789ab", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.2.tgz", "integrity": "sha512-Pt4BOrGuK9S0Xrj/y4fsKSgVEJGmZHf2LvOVUyRYm/p6k7RiAczrIvNn1RQglYIir95uxZya8zkuVM2ob2N4nQ==", "signatures": [{"sig": "MEUCIQD305Paw5lm7lgat7R+pDz9z0sqSo3tNZrCKctbv70EIAIgd1vj10A33C+Iwa/ieynKmtLoHagB2GRdKnQbOZWJmf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "4.0.0-beta.3": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.3", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "5361d3eff51bf8ed04bc36878ac05bf795bce60c", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.3.tgz", "fileCount": 15, "integrity": "sha512-c0K+n36PwqD4SIRghX2foALUrRvmCqybeusa+X33YuXfmVJDEzGW8ggVNrHKanDjUVoOzxTV5ufKsyfD64gLqA==", "signatures": [{"sig": "MEQCIDP2hOpdyftbzjX65ajpukD1igUG4Llf/eRUtQANNF2tAiBOsaWw1EuX9L2D6JNw5HcDBmK6tmflHTNrOuF7OnOqmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85023}, "engines": {"node": ">=4"}}, "4.0.0-beta.4": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.4", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "f0d38a699479d3392823739e0d8b3c96c49642f2", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.4.tgz", "fileCount": 15, "integrity": "sha512-JFFCG9wgs+28rwggZ3j8pOhO5m7boBoIahCDGU6eVJ4lKl+exF76p+TP0lw32QftumWOEsahimg0yQgcwPFHHQ==", "signatures": [{"sig": "MEYCIQDNNSkLfdFOsRbgAvTCCGQzJ3FgmaQnaYsVAKGuHWGZ1QIhANk6MgsgwD3/8fx0BOtHpRCZjpp1gsRMuOK0h/0txNcR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87179}, "engines": {"node": ">=4"}}, "4.0.0-beta.5": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.5", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "aaf1be9463c8575d14955329ec272b34ce3e4ce6", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.5.tgz", "fileCount": 15, "integrity": "sha512-32/YlVSpSloQzB2XEu6VNuFTonbortO7h+pQ+O0ocdrFf6VX/pUT+5809Vf2AGhBLuKj866o8fpP/xZQrGAYrA==", "signatures": [{"sig": "MEUCIF4/OrLxiRX5IJvisfQ6w4s4Hha8/36ENA9PY+tHePJEAiEAwZAWlfI1shJvMwQM1qTOnyuYa11iecRHbNOOlPWbpPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89295}, "engines": {"node": ">=4"}}, "4.0.0-beta.6": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.6", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "cde70dd5383c40f7506d8e748bf3fdc0a26a2d3a", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.6.tgz", "fileCount": 15, "integrity": "sha512-2jWqO4ueOP7JseP4X0diwvcmDEaDLx9f+wx8buX4rVxyIn/Nfivo1ootNIyiZLAVbOnIcdlJFhjOEW3aEsoBEQ==", "signatures": [{"sig": "MEQCIAajYM9jgjEU75CZ7OtgWe7MTjlHJ/CfHLN1YFFEzKmYAiBqQ33tOER9iNt44o5/V4DY19nvllaWcn9f9GJEiLjEvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89310}, "engines": {"node": ">=4"}}, "4.0.0-beta.7": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.7", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "b663c6e7cf6c23fbfb26d77a9aa42794bf839960", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.7.tgz", "fileCount": 15, "integrity": "sha512-IFiFgioJfD16a2Ij18kc3p2TS+FBMynFErU8Xyj1LjRPl2t71VjzU2ZDymfNOJV3mp0csM9flakUb6Ovdzniyw==", "signatures": [{"sig": "MEYCIQDIJv6XnCZmv4aF0Kyz1Mxg9OiuOvOSmVAQeAm3WIx2qQIhAI7Y4q4hHkciXgTdyrXstOv4oMUvC1vewUJMG0LbJn5H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89965}, "engines": {"node": ">=4"}}, "4.0.0-beta.8": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.8", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5"}, "devDependencies": {"eslint": "^4.10.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "src/cli.js"}, "dist": {"shasum": "f36abba7df5b9a43234d6a79013b158cef6597b2", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.8.tgz", "fileCount": 15, "integrity": "sha512-0oJcA8+vPVFdn4xMVR5MSV6zuxTK3i4RoD9NQ7AkktDsKkRzdT5a96IXBKUNXUEYWBn8ACFyUL5pm1tyC9y6kA==", "signatures": [{"sig": "MEQCIENIMWtkvPtpuy+Vo62+HLsvQTvkax3eFTKbOB/udXKOAiBU4X26atzNhMEL8DDT/TZApY4JicPLmY0OSBTBRpDbjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88485}, "engines": {"node": ">=4"}}, "4.0.0-beta.9": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.9", "dependencies": {"glob": "^7.1.2", "which": "^1.2.14", "os-name": "^2.0.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5"}, "devDependencies": {"eslint": "^4.10.0", "webpack": "^3.11.0", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/bundle.js"}, "dist": {"shasum": "eb293a7d6539ac42b366b3a2a034f3ebebea475d", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.9.tgz", "fileCount": 17, "integrity": "sha512-vKZF6mdhZEgYbASEuvHTHFO9LYmJY+Dzy2naOjyMDhi7q13cIkAABt2KIwYXKEZwj7kpbbJdY3Bhxjuvjxlm1Q==", "signatures": [{"sig": "MEYCIQDmystoc1L4A1oXzDmGF6qUXoF60yvLXVbad6GrWCmz9AIhAPbgPu/Y+Uj5iRY5mb1R3lPA872dPAkO5mMtDW84BpJj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269094}, "engines": {"node": ">=4"}}, "4.0.0-beta.10": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.10", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/bundle.js"}, "dist": {"shasum": "1df07819316bd308c57173eae99f95c17e11afe4", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.10.tgz", "fileCount": 17, "integrity": "sha512-yoDYdF2Krx9RB2gnfijQp7xphJcCMQpJRVSNlfbxCzP4IyrwqcnNjMdidjlvIlWL/qhNb9XEWhHoa5foNYNb3Q==", "signatures": [{"sig": "MEYCIQC+Scz+Ms2sDg2Nv/tziWlRX7XYRMMFwTzyWnJ3iLUT2gIhAJ9frpfToY8azZwt0NxrCxRD6orGmM4k+xessMtjKVdS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282097}, "engines": {"node": ">=4"}}, "4.0.0-beta.11": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.11", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/bundle.js"}, "dist": {"shasum": "e2a95d4fc65aae506bdbd2f708b3ff6ef7a280c7", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.11.tgz", "fileCount": 17, "integrity": "sha512-qIt0k1t74h8Pn1t6CWJ/VBexMdKQCP0yEU8+mltp8f7r2+JZpeQT8Yz9Grc8/cyAGGA+xQtII4wlBLv7xPfzNA==", "signatures": [{"sig": "MEYCIQCfu6OhxA4yNOouZj2FFWl6OeDMbGOcpkWjR2IwG61N3AIhAJMz34adlZFKC+ydcv7TWNLRPkbP/PCC8NIy6hltf+mu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279457}, "engines": {"node": ">=4"}}, "4.0.0-beta.12": {"name": "en<PERSON><PERSON>", "version": "4.0.0-beta.12", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/bundle.js"}, "dist": {"shasum": "397e4f73a6e20599f775b44666683f13d8dc6145", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0-beta.12.tgz", "fileCount": 17, "integrity": "sha512-LgLJ8SoOIhQno7OEkdoD6WJjZ83Ll38+lhXvJ6MOl11tuHewgrzH2gu2Cap3/nLR0fBs6bfgbTwsmujbf2pY3w==", "signatures": [{"sig": "MEUCIQDbgvLvde3wwwf/kH9ICh9rErPvKDjWcFxzhl3XUXky5wIgRW4h0Dtjoe0tzlBssbjesuSDWgLWCreAX2QvJthg6eU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279935}, "engines": {"node": ">=4"}}, "4.0.0": {"name": "en<PERSON><PERSON>", "version": "4.0.0", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/bundle.js"}, "dist": {"shasum": "af7ea10f011cfce883936bf07c7758be0e185d91", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.0.tgz", "fileCount": 17, "integrity": "sha512-cjPe8KjtIdakaUp0Nb3eEKQlZut0OCtCdzK2nPiZbJoly+R/k42cvGDQnqsNkiVhh78la4Hxh7tJs5FOK5EEFQ==", "signatures": [{"sig": "MEQCIGBVeYjXVZtR+v6s4gUa9ZnXF+Nkd9CMx0VXTmOBj79SAiAJeG/5h11i4FNzRiBZrJmyyO38W0cYp7ctBIlkUNIP9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279927}, "engines": {"node": ">=4"}}, "4.0.1": {"name": "en<PERSON><PERSON>", "version": "4.0.1", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "09ed5b7adce9b9e6956ef8bc9689e311d9a40991", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.1.tgz", "fileCount": 18, "integrity": "sha512-p58/2vqK4kr81ZQCCFhoxJigRbgkaqnzu4qnNjJiS/K/PwstOWEi9JaUNayuI5tkJ4Eov7JhBis/dgbYEqZW/Q==", "signatures": [{"sig": "MEUCIQCToh8vyWIRjJcYfIaRA4UrKvsIj9LBBksLjdIW2MIRJwIgHgD8LdzrKX3bot0fE1I9/E4LbI84Xj3El//NfrjaZRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 394066}, "engines": {"node": ">=4"}}, "4.0.2": {"name": "en<PERSON><PERSON>", "version": "4.0.2", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "f08072c8dc0417c60ec252ce97c37e80409e9b32", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.0.2.tgz", "fileCount": 18, "integrity": "sha512-rvDGAf4slDQww/gFY7Xsjjd1axFP4R6UsAeYFzlvts9XPmMoLO5YDf7bK3S58oQk/6MwqMsQ3NTy3SwlX626iQ==", "signatures": [{"sig": "MEUCIQDIsFcmYnZhL/uNWZ76FMGjIGcY0MD0FGjzFoBH4TpWOQIgGj3yR7lTjShbxLIsGE2eW5F4K3x40V5donJxOAR9pqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395910}, "engines": {"node": ">=4"}}, "4.1.0": {"name": "en<PERSON><PERSON>", "version": "4.1.0", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "c36d6548074df2e98bf634bc543f1a2dde1ec7c7", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.1.0.tgz", "fileCount": 19, "integrity": "sha512-dPsH/1cGfjTwYicrRji0t8is7vOj3Z4QQG7V32RohCMOYs1SMvN6fImyt02PfahnTPi5F2bp4nONjv5PoQR6zA==", "signatures": [{"sig": "MEUCIQDC50BT4c0MVDPfFKvpehH9QBEqwwLuT/Hjkwm86bEHfAIgIm+kCw4rEQ0C6WKUU9pi4WFX1nHxr03AcURBzR1jJG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 412880}, "engines": {"node": ">=4"}}, "4.2.0": {"name": "en<PERSON><PERSON>", "version": "4.2.0", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "4bd0e513ae452496aa575a480bd81c7e1a8fbf3f", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.2.0.tgz", "fileCount": 20, "integrity": "sha512-<PERSON><PERSON>jalboistr2HTgUxEoEr/crWt6CwP/6+g0oWJWVrX2Ex2pCO0vpiHQ2rimzIQZ9maTMd6dWnFIfx3SvQvLW/g==", "signatures": [{"sig": "MEYCIQDzq+UITFn7jBkVtuAUotGgdljRWRjLaONzE0xZ5sbpuAIhAO7q3DQxJONZ/qBK+sR+QEnz5h32AF8FCo7y4mn6uZFP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 413767}, "engines": {"node": ">=4"}}, "4.2.1": {"name": "en<PERSON><PERSON>", "version": "4.2.1", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "6a4eda08d10d48ed74a7eb6ae821973ca7e8d529", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.2.1.tgz", "fileCount": 20, "integrity": "sha512-bkw5tkhBlacnb6tJjM7Dzc54sBciBaDNEAefxwvJH/KPq9C0qRKqV4mzbSioG5po2VgOYrD9o4lDPw5Df6hflw==", "signatures": [{"sig": "MEYCIQC/cC6KaSl5Gr2wxCOXqVG1Wh/J9D2qj2lPyDrw03QFWwIhANEJd1ZG7KmZuN0XHUAiUjGHCdDSeWXhb7J807kPB68x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 415282}, "engines": {"node": ">=4"}}, "4.3.0": {"name": "en<PERSON><PERSON>", "version": "4.3.0", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "8c315dd9b9558dcfcd3f12b6d3f957043a7c12e0", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.3.0.tgz", "fileCount": 20, "integrity": "sha512-kF6BQHfS3lLJwYIwdm35z9BRScHRDrPG0m0gVuMURbWNj0QR2Xi2JYqLIwbM8I0KOAyYdjYH8yo5IPUvl3FqvQ==", "signatures": [{"sig": "MEYCIQC8U6uCcJb8086qR3VtjkCEe9V3AtfvHXO5cS0NLLO6qgIhAJ5KVL+c9Zf7zDb5/arvYaY9KlwNJkkUOV4T0oaiDat4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416256}, "engines": {"node": ">=4"}}, "4.3.1": {"name": "en<PERSON><PERSON>", "version": "4.3.1", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "bcfa9341fa1e64dd416e07877c182a037c403eeb", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.3.1.tgz", "fileCount": 20, "integrity": "sha512-HdX4+D8bzsp5C5yTt+IUcSwl7YzRKIhB1pO3SPzMyLgnMBcFMg+fFyLZb5yBvZqN/ErxiibIyV5m1kse2eTbVQ==", "signatures": [{"sig": "MEUCIQDk2KQ3HuJCAG9dIwxHBKGWubXNQnJC85Hh/tghxnefuQIgSEx/uFjwj3DHXN4nXGrg/cXFhRxgJZRMiBp50oc46Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416822}, "engines": {"node": ">=4"}}, "4.4.0": {"name": "en<PERSON><PERSON>", "version": "4.4.0", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "2f4df2844bc5646815c63e47d08c0ee5fa1b3ff0", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.4.0.tgz", "fileCount": 20, "integrity": "sha512-WJueI1tdi25a/8oz3BSJcWwD1drRBxk3NNH0lCknZQq7I5u+C9vREOElZjx/egnfFdj9lJRvsa15/RmMf5oq/A==", "signatures": [{"sig": "MEYCIQCAegd6xzFIrF7E5Tx1keK1N3xel4BI3uAWsmo8H9ZlmQIhALq5XkLCKkrum1mdwYRJ9mDeXxmlmeJuynR/tYgRzSXP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419635}, "engines": {"node": ">=4"}}, "4.4.1": {"name": "en<PERSON><PERSON>", "version": "4.4.1", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "08d778eb93e439b145df8b149c9a2349494805c3", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.4.1.tgz", "fileCount": 20, "integrity": "sha512-BJuWPp7Nvj9PEDLEb/PD7EqQMvoKrmTskjCLowRvdHDfHasKWHTshiKWKdZFH6VzQPqvFv04/vgHxa8zHBAs4w==", "signatures": [{"sig": "MEQCIHrIoAFj2olDfJm3mfNCb3WbqL0u4JVqyGmNtv0cKXc9AiA4KbUaSx4+d3E7VYFolFazeKGBMif3TIBPpjMIoTNzVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420139}, "engines": {"node": ">=4"}}, "4.4.2": {"name": "en<PERSON><PERSON>", "version": "4.4.2", "devDependencies": {"glob": "^7.1.2", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^3.11.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "472c49f3a8b9bca73962641ce7cb692bf623cd1c", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-4.4.2.tgz", "fileCount": 20, "integrity": "sha512-5rfRs+m+6pwoKRCFqpsA5+qsLngFms1aWPrxfKbrObCzQaPc3M3yPloZx+BL9UE3dK58cxw36XVQbFRSCCfGSQ==", "signatures": [{"sig": "MEUCIQD9ox5DCAmC7Y+TnnkWM1GOOH6a1uJbGk/3M4ayM0tiyQIgI96x87nB790+n2qGvj0oidkfzAJsnmrrvscZfVcGQ0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420651}, "engines": {"node": ">=4"}}, "5.0.0": {"name": "en<PERSON><PERSON>", "version": "5.0.0", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "e024776ddac6e571e0d80b07db8b7bc8422050c1", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.0.0.tgz", "fileCount": 20, "integrity": "sha512-m12klItWST5Lf/U2fwHpTy7UPQt6KfR0GiO4btY7c3fGlkPuXOC4DEbxwTYONtlJmWHgOY9l17kmFzS45Rszjg==", "signatures": [{"sig": "MEQCIAWom33V6V5DXdT+FxfUrMoiTHILCHATTbU++6YHqM+7AiA4I0GL724Hw+zZZmKea9V7RInIH1weafxR5HZ8vNgANA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 531571}, "engines": {"node": ">=4"}}, "5.0.1": {"name": "en<PERSON><PERSON>", "version": "5.0.1", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "0819dadea8f5b7b497b8c2a492c622ba6b91b3eb", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.0.1.tgz", "fileCount": 20, "integrity": "sha512-sbJk428cw2511hnO6ik0tMXbkLIT9cOF9wdRlx+PKHG2D8mVoJKRQ+1Kf6uWQH8Sq388UepeAFbqiIV7pyc1UA==", "signatures": [{"sig": "MEQCIAvW4AHFLlA5UbfnJNXyFDwuvEy+Z87k3S0vzpQlN8k+AiBx3ptYt3BS/Tbfls2ye5h6WFONfVp4VgDmr5rjHqqRVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 531595}, "engines": {"node": ">=4"}}, "5.1.0": {"name": "en<PERSON><PERSON>", "version": "5.1.0", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "7b49950c934a089a3992422e7f2eb4a9aecbcf82", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.1.0.tgz", "fileCount": 20, "integrity": "sha512-7H+ae51q7xv8TZzaLL+CpQSOYE+ozIbSDXCblQG0758hlI9YSXQJ80WmIdQlDEWtbEmnwbExnCbouqc4cgWRHw==", "signatures": [{"sig": "MEQCIAqUw5m3y77bMB3VNN0XfYB5OShHoBYetr/zYOlR1GRcAiBrDlSlmEx+QPfh4HNCLDw8DN2PXMiNv8LtR2qRiFZYSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 539328}, "engines": {"node": ">=4"}}, "5.1.1": {"name": "en<PERSON><PERSON>", "version": "5.1.1", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "9d65f8c14ce6c0d63c0ba36f9528c1f2c15147b9", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.1.1.tgz", "fileCount": 20, "integrity": "sha512-CNr/Iq0+nuz899mKglV/5okyJP7QyTVoOk32AhYNLwxsVBq34SsFR7VCLnph3u6dhW2O7pkeDUydXfrxDJWV7w==", "signatures": [{"sig": "MEQCIHfr5XSTp6jPcuCM5T1dtDAif30tnNIFzMyYWaEkLLExAiAgj7qF2dqVgOLAersMJ1OwBlS9/qLyUMJLHhWJAgydEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 539328}, "engines": {"node": ">=4"}}, "5.1.2": {"name": "en<PERSON><PERSON>", "version": "5.1.2", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "aa51f0100df48cfdeaee4b601f30162a08009a17", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.1.2.tgz", "fileCount": 21, "integrity": "sha512-owBzvK527umyotacdLxNxanaFTDnvPK/q41BJ/O1jpb0tL5hDMto8RElnDetKnNx/ANnrZnnBhsEyxmYQtd1eQ==", "signatures": [{"sig": "MEUCIQClUcb9WqkSB0aLODuWqxZjRfV9NjmhL4WLeP/10RerIwIgPbWqWObETZ+4CEqBA7l4SdtzuFf8YMzJkdT/nVTBCs0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 782011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa02sKCRA9TVsSAnZWagAAO8MP/ikSbpbTbmxqjasSr9xQ\nH4Uy3Y1FbiN+Z+ERoxv+UG/KHhCAGWlv41/nr7N6OUHsNhghlt8ARQVg/e4R\nZBs+6gfrTSV3PF1iX1dCffTsOPN0zlss0y9PpbLMKSHw1SEJnbNhGMfKb5/f\nYCQviY9GTSjU9HCrV8S3fBoAEG1wtVTcy07vyckdzBt0W67rI19uIX/KBfpV\n0UUYqf+iTn0iEF7POs7tkcO+mvcHU1OxhapBdfbAkxgOWOWbNKeB+5dZDG5b\npu7kl9Zdy0gMRvJLc+nPr9UuYLbeOCvPjvxMc63q9vk1DMRDA+Sfi/cS8qHf\nqviNjxYgVUntepz8wIVnbss0U0MWijlQzP19m4oYIWH/YR2Bigme/VNeZwuv\nuBhetYJSMpRsIZN9K+FmJM/b798ihYy5pPsnbX64GvW8TPnNwk/PlnGeWPUX\nCMOAEyxGZaXMcHUxBEe7QmyLwhCPUbhL+pbXhKXzCP6osqygR5VXUyfduLDD\nbA65iegz5n0ao+5buyt7rAT/N0R6OqOZ2bzZ9WqDYSThQK2Nc6WbXCFdtC+3\n+IDnbZYuQjkbHL1Phopd4NfjPuDLgRgqVIowlf6ZdCAqBkNek4AOktmguh4X\nFhluGcWJFNktsyCxUt+af2DFZ3GtdC05ATF4SAbM0Rr2gO81OfxWo4Oxx14S\n5AvJ\r\n=BW5t\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.2.0": {"name": "en<PERSON><PERSON>", "version": "5.2.0", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "d7794ab4a80267b173b27979ee6d52213b2da721", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.2.0.tgz", "fileCount": 20, "integrity": "sha512-HbvntMb/aLra+YXK03FdeTCN9KARggSmtxC/PX/Ktu7UbL3kilQkH20v293kf31CkERLylz5Q6EXBqC+F9msbA==", "signatures": [{"sig": "MEQCIGajkFnpkZt6P72uCVFa34QHUOK5C3NgKOjnvnoU9UVZAiAXDb1HcBrHchkIgfJTUI0KH+IB012kEIVxjOxkHTy3Jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 541472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa42gaCRA9TVsSAnZWagAAgWoP/0RtMFNOHKcf4i8G0aqq\nUN5iy7K1ym8sOqbHgXF+5T3lpyP0/H5+bp9L/Qmfea/8x3fITUi5r+0KWvM2\nuGFnInKz1TrwAEDVWEnXHv0hPCDd7S8NVzG3h+Pcj1DNJVhHsE5hXbqcIrL7\nV2lhWG+YxMjWcYy10eLdhcvtMRvtQvPG2mJoKsFlaoC4UNt9WVyaw21uKIsI\nrHkB18UNqBmDUjxev8WaHPvI0zVBQIKg2Us/XLiXA75kfHHoP+isbevukut2\nyFqT93dxSTgAY60+FhdqwJLSc8P9Kkgz3GA5RlbV5gMJObWYQJwvlTLCGYK+\nq/+V9rmwgVNJ0bf/8Nf9kyaDARt7iMN5dQ+BVSZjNEGJO82X2vWbkWQNE6RP\n1/5HRHC8HoICR24Td5NULL0CHdBP/3wscKgvvzQyHn+PB9k4Of88yXnqzNrg\nurl65IpmDznSBR/hBT+oYpA2tLcf1SoK/3V/UR4XgBsCxYNzYLTrRNyb4wEr\nPXDvjoeKb7iKvuiGL6Ot5bfDvGoCET/qq4KlZnGcApYdpGEpCFYwqXBorKK5\n985bHKUlXIJ2IO6jjnmv2pAl3LhdGAt/SECwwCTPapMPj11kejj1Xcqsp8gL\nXlpAx437YPtjeuxARe/4DdcW9s8uMWQxOazaPTG9osJ3dCVIMjqtbzDMfGgL\njlcW\r\n=6lGq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.3.0": {"name": "en<PERSON><PERSON>", "version": "5.3.0", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "37c3f3b3281683556ee46464f81fcca6aaebb951", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.3.0.tgz", "fileCount": 21, "integrity": "sha512-9wAcf7Ngx6wAghGti2DZn/GG/ucKPIuq0KkXYxe8VccsT07yxwuS7YWCY9jWDbJv0OsM+DsQLo/xtL6EcKiVVg==", "signatures": [{"sig": "MEUCIF8NAZe5whMIpmy+otmb3Oz+Jo48sMkFzavR0Fo39CLHAiEA0f9Q8wAA9zo+ToLrFMYGCT6zCqQGk+GRWU6EerHVahc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 546485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5JH3CRA9TVsSAnZWagAA3NQQAJoIi65jxjqpkaZ84A+I\nzjofle9gqxe7M53pT6P/4lw/lVZYApS2C28qQepzoADAtuLuyUlL89TDMxV+\n9TQo/3xZ1YfITAGMccjTaFjHPeCiBuN4KcKYhqUw8/g0okU7gfebTxLRLTf0\nJ55Upe2U+IdUluiZH28GT7DDlePgbhlYIvRiUDgxCv5WPRVxtVaTc/QxPNvl\nlFRB9OnlG4HO2qXaRhv43tOTj2oqA/dtIwQOSHIYCVjnGe+7VCwYATmKA1HL\nPEEWmwMwUth2i3ap14FlgGnEbiv46oaPQ81iFcr6qsnMzpf/7BaSq9DpLZWi\nhLPqEb2usb8+/zNV5h997OOmoNX2B+YqMb9Hlyv3HONy5ICmqzZsHX1tpdpE\nLUizrMWAZHPIucKg2eCQ8lW3dqCIeWz74UKgQQvQvIoLrTV4S/DvmVvX7KJ6\n7dz3D9QMDJdasGSEwgu8YDUIv/S4oSmJH42UE8a+1dBzHqIHxETnVs6Xw7eU\nm9YMrJx4ZyjqJk2rW8D0zQIjtiUq8w9KhtxX+IUbgqXvJAt0Rg8LjLmhQ65L\nscD63dxKa1BAzsKIpAuC1ScR6zjWo9iWTOQl6n5MJ1AerPmdPgwpu4EiDyv+\nOTkdhsdEyL69u9yg4M25S9NF4ChzDWe6zmEJo/p6l1kNdG9jQOuWJC+CFPJk\nff7U\r\n=CXsX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.4.0": {"name": "en<PERSON><PERSON>", "version": "5.4.0", "devDependencies": {"glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "prettier-eslint-cli": "^4.1.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "5b2eeaf43966d7f52449cf3652165eb0075073b3", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.4.0.tgz", "fileCount": 22, "integrity": "sha512-q+e0Wy83k0tPJQ8PO8r10sbIHptBPL+tV+BauM97RJYrFO20mYNqnrzylAJdJZNl6sT1woAv/0BnjyBLP+RAww==", "signatures": [{"sig": "MEYCIQDg1+afpEwNCHOGXBuCIb+dvIumCtvUz1Y3UxR5FpV2KQIhAMP/2uQ2WCq/yLQKbf4dVO0N5U99gfNcaJAVwkrfE0hr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 549095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5T58CRA9TVsSAnZWagAArV8P+gPwyDLPJKyu42bJ78Th\nVdHE8pcAwJ0hH529Pf8h2srIa8eZ20+gxCh3eWrYjR7XxSJiKy+H2iV5yBl8\nQBKDjmrAMF+YlV2EdgZ1ZKYEk8EMNeENt32/wDHhFu/C0wyMJwXnCdKLj/RR\nk95tuNz34NH9l3ThSRuErzHqjjkaXzPH5XmDVfTdQ2azszZygNu6QCy0TG2+\n+od2O5zwxa2iCiyK5xgnf6mcAPr5ILX8CEac/htSuyxienXXHaYoQ4/efrKt\nYhIE8TJnKkJp843LoUpdIYObMWiTvNjasf0+CVhYQ6GPURHTLRhbsnfuIzu4\nGWyt/SnCUhMOfqFt7MW3RaqFLPpggKbtvuXqS1ihezas5Y2AvEss3SRK3uzb\nI/eZN5J268NyMGiaZ3ZdPSRPuXz8r5AHmOyUIiaxCxP4BO9g1HYdRUaRhWUY\nD6rx2b0TyF/oX5DoHZbUBR2/llRhIMHhXmBLu1ukxZbT+rQpiM/ge6fTDHyy\nvSEo8kFZ/Ea9f28rddIPgwKr7nYSc17751gOP1qBlvOZ/QayDDVUyzD7yqVz\n4RM2YE/gI1q49vu9zJymzFz2WKZ5FpladwCirD2z5xTAk5TI/s6wa1jAef1Y\nWe+1umLiGptZlFQWi4wGCzFZDDt/DENcZdp0RZfb+0lku2++eFdd4Nvm3ZIo\ney2m\r\n=GWdd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.6.0": {"name": "en<PERSON><PERSON>", "version": "5.6.0", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "62cc847568a2701da92372829fea67134e13c284", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.6.0.tgz", "fileCount": 23, "integrity": "sha512-6jxIPpBuE84DOSJPaxLY+ZFdDwzZHysUH/kZ9m45CiC0HT/jiyjZOjDph+PIw2kbs3bua1zhJ+avV+Uwd/hKXQ==", "signatures": [{"sig": "MEYCIQDWoUiNziFA2SC5flIaEV71SIiC35VK9pp7R6+lpBW3CQIhAOSBXzupQ/DrhWRA97d/pIgG/m3qHihywA88ds0ew7rm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 575946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b8rCRA9TVsSAnZWagAAkXsP/Rx13TnGcJYQ+r4xZ/Rd\nhtVAY45S88cQYjCZP/el17WgJS2EDJi6aiUShcfpZo84Ot2vChNt36ysLSce\nMatzEi00w5cL3A9PWbrHjBpDGRJmpBpHhZvmbXXb+nyHaTsqETbiJZ+FKeoZ\nOIFacwm5YHBFCzzQ59eqkKjrVOkXM+XKWZoAUQawCjdVOs55nPpBkNkyZMa/\n8J/y4PE8GujcKu4FnC8tHYjT34rtOoGqLgbDNV9T3Ghc/LNFuRIVfNV+KYDH\n4ixZBmhyHN+0LUn2e/2wk0OuvIPquGRcrQq+ou4o78Kcl/BRAVUwAflfeGMn\n7lWSgMt76f+1Dg54HXmszBWOqTe/UeLPHPVIb8GGJt2ShU29thkfgkxwPA/U\nSopSVKFBVfQuEH6IdSHWs3sAr9Zzu7qL5HQ7RNLQ2mvXqRm4e/lmmI3KMM/y\naFexji9mMIoFr3BRVeOigIhvwacGHXpb6DB1MIu6jQBIz/iMWwc5HwqhqnK3\nUVty7aJrjz4sIsXUH4omR3zpbTYlHZLtMEnqBI8ZcQ8qXeu/2JMRqPWilE7I\nAc7xQ4rnXaQg8sx3983D3wKPXwo2GxlXQlA9kjvfCRDRHk77AmIfsKXoEDSg\neEtOcViG37V79iQz0gWndJOYzzgmxWc4tw+GZaoneKZO8re30eo7OPskauS0\nGSSg\r\n=8/4s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.7.0": {"name": "en<PERSON><PERSON>", "version": "5.7.0", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "690e5fdf30aa75dda19c0c5338d0a48a48f1e998", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.7.0.tgz", "fileCount": 23, "integrity": "sha512-+YEENfz2TAXPo/6kG4wvANO88c4aGFy73TI+etozWehqxyg0k8uOL3e1Cxqa2TppImicOeQHJUz8jWDpuZ3wkg==", "signatures": [{"sig": "MEUCIH75Ls0IGjOKiwTptXPFDvVRx7CaY72khb5+W91K/V7zAiEAh9mMEm6gwmKIhbQo15W5iVaUj2Us276JdoK4xziz3CM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 577287, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/dgyCRA9TVsSAnZWagAAWb4P/3DA3awoQK5uDFM6Yu12\nL6VonsPSfFuPZzvmEnXwaAEsfzqJcAHqweO7rpCUUQuFmvLibbkh6rykbd5D\n/pxa3YFAoeQacp7O9oEYTp7uQYkWAnAlR27X4Cizre2g3tSXeQgvhRC6QH4Q\nIl0RR2iBQzvy91Za6AiKGzjm8xELXkz4ZgdR8oCn6IGYd+gCzw9JNvahKXpi\n4nBYqJKmzUBjJpG2t729dK5A9Hds4+2j/UBBtEkKPGU0xVKm2R/kv/HYwQWA\nbMSy41VTMpGh6j6aj4EvMiJ4/gdOsjmvj+Lp+jBhtOqtXhOmtLNTqEgOlsji\niP88PHMZgpTVdn5zIQyeHKcYUNyrr3NP45BCkuyMNSsUAOxkdCY3rf3pjl85\n1gj91SSMtkDcOCo7bCbUVwjNess4S9QGIKf8YLrr32CiWSePUyqHLULhj5XA\nxnpIN2yX3K0kgATMHt+J/3Hj/X14QT5oaePtAQQbybdtc4O/WgcQjed8WcQU\nFOq2MH8yQZxYzhW9vh7v/xtlcCWZW4jzVoqQKL92vgvFJa2IeZyT5ayVjAxq\nI0n93cViyrdP5AbHA5WVpwMROnq1khTNzd1Wr3d5lglfqAzhDonruG0stciC\n1bN2Fl8RB8b54M0Qg2PBdu4Qd+N1bnr5NsODW5U+KzUUxAih2oWZxa1kYjF4\nwbZG\r\n=mAh2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.8.0": {"name": "en<PERSON><PERSON>", "version": "5.8.0", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "93a66d03be4b25432974fa7728a8f9cd8a417a0f", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.8.0.tgz", "fileCount": 5, "integrity": "sha512-TKdv8cdCweX9Gc3VkFo8AWHGmSRoq8w9zlWUvQ1de1F6Kbs/oTlTgvFG24M5tyMFQLolL02qwQZp8jdtzqTUjA==", "signatures": [{"sig": "MEQCIDDzWQ2DvSardByBt/fg9LSBOsN6agwD9TXUVm5vgxWxAiBPM79t81knWShhlUWQZ1GmnCBMYPmY8IvEkc8TBWaBDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCCrECRA9TVsSAnZWagAAJFcP/1+qvFiF6UiZBPZGQoNZ\nK5z4NA1QxrjzPuI3OkDwywyZ2dVb4PiIXYowp58UG1H321hG3lp1ITcAvLpg\n4T9lCclLF18Xwu3z/aAF9wYW4rq5YO1vEYs8eT/Bd8Ye1/NQSsI1HlB0+Wae\nrLAlYvm+Ci0H1vTZ5oQWozuxW37hI2vm96Rk5KxhI0Hb5ILMaTzoZXtwpFWG\ncSVnNMAmhvEiKzby+M/sxw+i8mUvoCF4kdE5ddQPnd5itAtmoNP2ByCUUizb\n5jj9ob4EapeKXLBnQ1PL0Vd5ecQ1KuXhNWGuBuSveOLBJiZ6wu3xwoHoy6ww\n0A5z70zDy0g85XaarxAWosLCCteghHOCw4G9DB0WybuKv1OXQkbVbkjitIXg\nvZS3c7djMm522TfOEGA3Z/20YULO3t6WDm3DadIrOsr1SufhoTlvAKgnvOL9\nsmJftcevIZpE0Wz8YVDtP9coti5hNzpNnkDdeBzFYK5bPO7pITW4S5dh/m64\nUaQF/5O1kQjfN/vCKIPBSnvtA9b5OqLYDe0QRIlFbtqb4ZmXJW5OxcTRqueO\nulnZSwP71YCPshEk9ajnJTRXCrCilHK6pr89Wu4a6fNnbH9VuqdyuivB0MEb\noUdWFPEuo8K6CiSwWmOPskaPjLRGZgr/CwmezqrWIMI0khQfd6ttVu/sotQC\nRG16\r\n=PR4b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.8.1": {"name": "en<PERSON><PERSON>", "version": "5.8.1", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "1910c0d1dbd74ff1c6b8ca0974841a5dcf7900ca", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.8.1.tgz", "fileCount": 5, "integrity": "sha512-6DUjb7ozlC09FNADu5WZfLr4NLsk44loPi8mDp+NjyVtCmcLDS4eYNFsU3gSYjsg1Gxp29ZXD06nsO5J0cUhFg==", "signatures": [{"sig": "MEUCIQDS6SBgGzQ9zLAjVe4mMsaY36j9wRsBSBZSBzEQ540ECAIgAIGyZtThMTk8hJ3kSPWEcoL5kifUAEQvyFYaAjgDaCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCC/rCRA9TVsSAnZWagAA7FUP/3jxfV8bwdzOqXpCJlkv\ng4GJrVhjAY0msGOxlNwWRkKCx0OKg/BXZJTTtJQzNzUCmKPq0hZP6dfelOdS\nXMn+OpfSNMg7xYYRVe2k34bI8i61HwOFH8W0DljjGtMjtRnnnhbHqwqyau8g\nr3LW+yfqf89URw0McctSfxag6tAd5csROwvdgrAU6YSLSiHfJFXYR+HD6ljT\n9xj6m+Y/zhl9oa/7ZbOcLAvnwoMiMYcg+HY1tdfNEaLHfQVVDuJBw9cvA/Te\ncL+2wf1sAizhgDauNpQmXMKNsmuJhWKyfRSsY4Inoy7NORX4B5M6Do4FfZey\nB7ZAQU6PuSz/UNqBzSbuVgNFxwdMYTSDsqPHI1Ou9ZSem/f+IEP+v/TLam7g\nyOc1bqmbD+Fz125lZ+EpWzbvJ7adQ3ydLeLrjwBxt9EQ1QImb4+4VclG1CuF\nz8T9Vps0neGfUNqBR7YQ+SH3K/rqKsaPRqhHzSX6rwdwjq4nryT+MYrVqJub\nWKYYr59xb0iF6Y/0d9mk2lxfbDDJinxl8S6F7pFefYnbJngj6dhiXVWNQtI7\nCDQMXRpwtyvaOMNHZqwTNP4vEkkP556YzyspVxnOHbW5Fwn6mwCYWH927yEh\nXvd1DisOzQvjTNAUKIapKuCCq5k6YEle05P7or//FMGNr8iwwHxkfOVtD59f\nnb+v\r\n=tKQP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.9.0": {"name": "en<PERSON><PERSON>", "version": "5.9.0", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "1e681b7400c384c679ffc886b6e3f811d7e687b5", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.9.0.tgz", "fileCount": 5, "integrity": "sha512-JeaiKfpCYQwIgSupqfApjt+AMMc1iffwHDr4YqnO8JBlbirgeAE3nZui8aSVsEUij25oG0Pi20MQZwins5shPQ==", "signatures": [{"sig": "MEUCIQCMFyFDRPL37a6d1G0gE4cPyU1pFm1VrK4sLAi4jR3F5AIgR4t45YrZBl4v0TAU3Lu3h5jmqqu0rJZMm/R1XEaUgaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDrz1CRA9TVsSAnZWagAAJ1oP+wau/jSnejZrhaZwutW7\nWN0QhsOu+rSGJnaXk0U/PxaredtLAbFE+4edUSxlBKBSJscTgr7u6U8cchiR\noCAVP1RKgFP2ABTt47JryjY5Ipjo19nFagOisZoc+A0E+2KDNzA2R3ot2icV\ntv4oxxZ6I2nDUjF0LlupqhQJgXqlP36AlI4EUwjVbYV5sv4vVtJi8kygukpL\nxK6ylvwjLHM66o8yBXpHrnhjKnpuZtWe7Cy8v9wmsOx17oRLEfIhSbBHZozO\nCfQ0h2pVvEZQ045mjogu4CRTCTOFiG0sTgHHF6bwfX8xZ/ognfKELRxNR/KR\nH+PFCFbBZXVZSoR6wpc1ZO3bGejTj6uOE8H/7lj1GYxqaDv472EeJy90mmhM\nJxfs8kYeUJ5OqltIqwNfYgsQPr6wwQh4F5lJHsqRpLy7siYhOJkdEXRJmeb8\nDTzweZGO6D9WXR5EkFUTGysN8ZwWzBcpRvyIFBBYxdHV4pJlGGB9eguOaLV2\nGmyZj+sul2UmP1JFDeebkGPtwQRguIx9yGIkTtCGnhhb//jhl4Zp/FbY22wm\nDsj71h4pfsBj/8XPDhOBmoBQXrSWDuZYyoiu2PZPZ/B8KE3er6nVYYQqAZOJ\nzqF3iHBEJMm+niAlDLPTQ4fzqFSZobRK6o2hPUube08tVnDpl5orLtTH/VC+\n14Nh\r\n=Fjs/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.10.0": {"name": "en<PERSON><PERSON>", "version": "5.10.0", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "503a9774ae15b93ea68bdfae2ccd6306624ea6df", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.10.0.tgz", "fileCount": 5, "integrity": "sha512-rXbzXWvnQxy+TcqZlARbWVQwgGVVouVJgFZhLVN5htjLxl1thstrP2ZGi0pXC309AbK7gVOPU+ulz/tmpCI7iw==", "signatures": [{"sig": "MEUCIQD/TDv+OPF3jWHbHlZ6qFLk01rmF31INghu+b+NtsdZCQIgGV2FLrphRWFzQmVPOADlzcLveJkFUOPmChNNf5PgUYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 270309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEWDmCRA9TVsSAnZWagAA4nYP/3Wnc6bCCPAToF2XFewo\nIn+o0UCiOa6pnYi7I2WHxbXv7I+lTeDxNc5gYxlGgpnVay4EgyuWngJtLc6f\nHcFmtJ2hw6Maau7OxuBQseUI3YpTl/petRPh6nQbKiAAPEFP2KtIHszqKcXq\nKDkKNimUP5SQ1DX56wYS3BeERTen7BVDf38hCw26luJSV8TdyKzx3pERGhSz\neYHGUJDJWgnmLMB16Jje7MEwnHoH/dMCv7Y2gmQKdVsCfmU2/9Y7x0eY/R+j\nDMmdqZBoZ0c8aHB3jyd2VMoKUP+9qM9PW3H1IJuk3r9GgiYt10GLbvbdmClW\nJkudshFZCTwRCuWgvyljk9cr1FNRZjF6R2bADK74yW7U4Y2cU4CEc+9D2KqQ\nkJCwd2f4oAGt84PmzeH51VODsECB2GBFn3ZNzGXssX6pS0FvXPEj5T59ngdt\nQKSgZLE7bknXYGrV6lA0Ubv2fOjpg5i9hRFiV766w97E+8uincdsUnC77gCs\nO7q3MjuTYL6eLMSD4syUWKfMCToPoGh2gsGfGfYTJqQP1fnl6NNuGNVyz1VC\nr31UdW1t6ln3m0D+fYyc/RJgdIQ1pyMeUW5Ndza5GHNJgtBD2c13lrWQqASN\nMbpSJxdMJEV0gZ3Hd19CFJ6athhUCRE0ZCGqQ0jpLHpvAT0euGywjFbYV6nu\np+xX\r\n=hg/U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.11.0": {"name": "en<PERSON><PERSON>", "version": "5.11.0", "devDependencies": {"pkg": "^4.3.1", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.3.0", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "0eb97683e4225c93ce029e4be3c90acd0c4b09d4", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.11.0.tgz", "fileCount": 5, "integrity": "sha512-NnWUQbKHZSG5rJSUn18+K6l1gh8jG67xQR1gt9v1rcHf2MEOPKAMzk37Ii/Va9MHWJ/RlLCzPFU/TXvlQydp6Q==", "signatures": [{"sig": "MEQCIE8A25XuAst7v2l/uQFmBCGUTgZ++7rIQUgMqead92M1AiAiZKe8wOJspGIHfGkHlP7oob9w+b1LSNbfiJ61VLMhCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 290420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2cRdCRA9TVsSAnZWagAAEtoP/RVGcS7Hcu+mjO1NA22S\na+REZYG9mZR+YKLrsMHtWFv78MAAkeo40OtjugZowSRpXcICA15OMObBSPGV\n+fLyT4P/SCyH6/iFY9BJGp/RXjL4oY6rgyKMCZr/6gehOHFYTdJdUtwfskWB\neSb7Nf1wW8DK86B0ICQ7K2VdhU2LfVvU6hlyGtogMy4SePzzWJDue38E0tv2\n+VE+oLzflRldMonuvYoQmAFIaqjq7n/Lo5Gy3CksY8qHJ3BhPZuye9sXioU0\nfx8v5SmTvM967cAA3EnOoJLNlR6Sr7TDRuDrOYXVLZrai1saoZegXsIKTGGd\nsQU4WIT6XkA7Jc9jx0Y+4U4m8nJslaCCYeosxWfqigFMyquYrVMntRh0Wj6h\n3kCps4UfUlOW7UfmKniLIGsp3h659Ctx1xON07S2If7ZwQ/7zfs4o6odN/TC\nd1MKilgxuXlyBRTWhOERY0OKHQ9L6KJ1jplKXDVMDRC1easVRlH01WlZ9Ivy\nWvaGV1DIe3PZVtLBDfvayXCwTgPd6daTodS//tlcNLKxzzNeOYBYMpNg6lmv\nCp/huWKD6eysMgj5nUiKrJfRwruByXAs5+11cVtdhSYAsuGGnOJBR+8O1FbP\n3aAODVY0dAEGSTgsmVdkT02TG+GeShDBuNnJ1DAXEaxWTIf9w758k+rGrvTp\nCXT1\r\n=OTnr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.11.1": {"name": "en<PERSON><PERSON>", "version": "5.11.1", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.5.0", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "uglifyjs-webpack-plugin": "^1.2.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "a1c2cb55196931b2ac6d4110fa7f0003697ad9df", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.11.1.tgz", "fileCount": 5, "integrity": "sha512-rmEr5fZLYYSRCj3kDhriz6ju/oMgEzC92MwF3mggFba2EMjK+CUE13MQo17Ua2CDT+KFFPAGFosodUoL/wxjug==", "signatures": [{"sig": "MEQCIAIYP4F9TTnir9TOPnBle8q3JCmZSgnyFD76C0xzAeIUAiBQv4RG8riV/d255taelGoJpUK3ujZHhAnkVGT7VVikQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 290392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2w4iCRA9TVsSAnZWagAAL34P/j4wdyc6cHe3Js1P6Pej\n+h64Wo8EQBdYA8GwDHSEkaZ2e4stEVB6H0YOYfyiCEbrurKHU/9Z6S0LZq7a\n48TRusvRwHu0Mn8o9CIS1++aaOxAqjckRBXMbm3qA2ehQmQiHBPEEXOKa57/\nefkWhkhE/MC3U5y1g/Um7AGTsYrtd0pr5dzrEgVS2+naEjvKecZ4kg35JwH0\nosoFDLxh5vm9kO2GNSSJa66A/KWb0ICjMMvl6XTphUyGmPXIjfN9iX+vIVmc\n8s9NpMdD7Vf3FrY7jsqxONt8eIq5faLBEOjR45rS1+04d0P4iucvYWfObRxg\nmDiNtq/psaWYLQfVDyeYjRbLraSmnPQmiWs1TH0NnnA3n3Zd1QIrTZ1+2aPB\nU9vF/WH3+YrfkMh9p4b3LWWAQf2JT26+HP8je/rfZiPaPJh/x7OOrW+stJ0+\nmEyB4QqfLAXXLjk4Q3iKt95uxGmK5jT4pSo5FmyLsrAvFAq8M4FQdBJCL47k\n0P65FzrVD1v71QVdJ3YV3xheNO1brU3w/iqLCJffxucWMDl4B9s3h6WJGFQ5\npXy7cwenAx3YHmgcUvfyULXsUHQAZo75euTBP3CgmMXlk1LgNYLyZe910BaJ\ni3wAFcrlNn6X/wyJqiz6J15UxG39xfZeqajLLIzQi+ezBi0VFWVz6wCZx4Z7\ntpaP\r\n=Tgcx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.12.0": {"name": "en<PERSON><PERSON>", "version": "5.12.0", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "a605b551be0864ce3f2f18e8da4f1101c04c5cf3", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.12.0.tgz", "fileCount": 5, "integrity": "sha512-PXUsJoikcXHLQZibUogobv4iUggKv+uMvlawePCYq3jbs57mGRCVwtppw+rmIlM//doVsAW78bapA15vLGXf+w==", "signatures": [{"sig": "MEUCIEl87H8w8CDw9jtqOuHoYIuaj5XYk0UH9fbzBp7wnjhNAiEAtMq6u7YAK+LyEHDhTrT5d9fca/itFCvD7VhyraYhv8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4dRtCRA9TVsSAnZWagAALDkP/j1elUo4owbJlX87ur5P\nZ590Z3ZEj+jt2iyYzXn7m2I3ltiO8+gW4yURhbtf4vSbEcXvtzOxTkD1KtNy\noh+Y28hdnVfE5BJWxNg4uFol9w+3KrtY3K35lSF/CyS/4tJUML5zRzko9R2Z\nQexFGPgv+1Yvz6DUVBDHxGGyPhP6REVWn8c/C2/goadhv1M4ggwVZeKMvzsk\nNSlu9jpyBSiLeRjnNamHNgM8DUXzHxNBMVbpaZEJRfWZWsKRekWgW54uGgkM\nXPGdK6PvOuCjOQWjsXBfvpvzBji5J+/QqFaC6h9lD1OK5lRc9jRrxk0W0zuv\n0oglr4OPxrdkXXObVN7s9Q2wAHY4wOeNzewz34sZ5Jp0kW94VlGq40Kr0M6b\n8mSHBL26RzSEqzgowPO++lA9jr/K3/kXaHOLJWzif9ENuM8ZmKAr9Z+mM0G/\nNTSyXMPwciruqFFLbNgy85YDGApBl3dZdSz8vkIRTJyBmPHeiVKMUw+Yuhkd\nPZ4oQtmrpB6hW4x93WJinCZHOqGyPNh2h86tf9Te2ORzLUPFNKmsbA+rgsQ+\n3hYyj7VKufGev0YiYE0myGhqpILZen9AuEXDWE0HG3Sa/rpdC3Yyxz6C8KjR\noD6qqlEfCd3kRTks/vBB+KPgwzfwEsYnghiyrFPzzmSM2ojlDYajLdyd4e7T\nUcqt\r\n=d5Lp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.12.1": {"name": "en<PERSON><PERSON>", "version": "5.12.1", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^2.0.14", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "83068c33e0972eb657d6bc69a6df30badefb46ef", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-5.12.1.tgz", "fileCount": 5, "integrity": "sha512-pwdo0/G3CIkQ0y6PCXq4RdkvId2elvtPCJMG0konqlrfkWQbf1DWeH9K2b/cvu2YgGvPPTOnonZxXM1gikFu1w==", "signatures": [{"sig": "MEUCIEalpcqtV4pYYAj9/Y7NQ+T2Yfo7XSbq5/KeNtE14HKAAiEA67arF8RkesvxEiphZaP4aHBm63B2wC2yx717EwFWggg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 273317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4w3lCRA9TVsSAnZWagAA+ZcP/RocUVxZCOQOk/Wqvdzh\n4W9m+QFoBLEf5D7Wy8qA0NjKg8OKSFEpJUFQqNMVUyTgpUC6x/bQ519naUXf\nbDU3rrYOXt9PzS13ZfV5J4dgX4F63VMuI5NXkZN6zKLX96PUuzR9ODTIwcPP\ns4kTMsD104r6+me0ret27ct8AphaIS6W3VxHxeb6ZqqirLcbo1Ud/WrjAka/\nWevw1BWFBAlSVhRxIAXvq1qXX4txGqHgpaTpHy0x5nOC7qIQg+9Fye3pTQlI\nc9u2tktzjLRsaBff/DuuGz7ZSf/A9ZRWAEAVf0Xo8116o1fzp5EsYPM2Q9nl\n9l6+AGVExCE8IwsnIRv2ZRO1OieMq6i7SOVGxhTkhfav11kn+vR/ByU7gjCJ\nVPSXLavADULCy9llTML8gpN2HrirbibGTxzrvzFze0OamLE3rKdYvt2/y5Mg\njbKRUbH+73BCrMH9ox7UXA4zrbR7e2iJkoGPvzZoGrg4AKKO+IdTSHm3YOnV\nxGVJmBrG0a8kTmtEb3cqc+ECn50qZQWM6PhtXZ4zHU2u69eNTzAq9iqCVbey\nii8gYM4gwhBqFqGBmmVveawZzx2rsdxC6GJ7gDTzrF7JPwVq9xNW/xfPiBom\n/iTNaQM2S2rMF/7JjwGqxZ1Ww9NW4oaMzzKE+lFUXxdrDd30zyW8lMg0EZzl\nHujp\r\n=TRbr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.0": {"name": "en<PERSON><PERSON>", "version": "6.0.0", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^3.1.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "e83970ccc0127f40392f6b46bbc111f7449b87ba", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-DG/O4tFIIBehBWGRtN0Ps6GvXjLos0jjfLlkZ2BgYSkF0Wtv7glwpyBJD8HkVzyLLCMofmv7zSSKN+itA51spg==", "signatures": [{"sig": "MEQCIFlunqUsZu3L1hPcY4bZLZtG0X+Kg0gZJbnn/lKrBCqcAiB69O8v7V/U50gdPGtLMsR/aUHBk05fvvE9K5CBfCcRxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 239151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/rpXCRA9TVsSAnZWagAAutIP+wSfPNxwcY7Vk7O1OaKU\nwgk9i4f/JyF0k51Np+dvdXn9lpYqd5BfmcCkm/XKODuygyZfZQAbp1jETBN3\n64M6LWNs5ij0vC4q9+6UnMHVfg3A2XO/XnHwoAMr0sGrC7qvYP+KTtrKbrh1\nN6UKDr+SZtS8wkhkhBYOnp/1xCmOahTQM+GOVHEAZ0Xj+oTwsZpAuXJtwi9o\nC75XASMKKkznF2fXQC5+w2UAhy/QCl7yQNAmrRrWoyWC204c599vGwZuGtIt\nkPL5NMxq6EPnPN2QKKFcM2Bmwqj/l00u8BVkQWBdi+lJgx5oWY/Zt/6rr4lD\nuhsiVvGNGltLDu2hmJyVWSJFkLFyDU9aULYZsH8L00TNetNnkGXBqy5wiB8g\nxe9yIZxlMhNCYaVcoq6GZJ6l4L8f334rRMTo/afcnyweaTIxuokOhvAAV63P\nnHZybjqmY605fkB5iLL8C5MvTj3cIIB8yW/lmmXUoJrERPAK5SqL8nrh10P2\n6E9nmjKetroKctG5r/Lyc0GR6uKJCiDKtSDXfwZwprbsehk40EqGdk9IEsFQ\n6fd7Nfp4knkUYNy+W0X6kt27glvoJO0MQXNsNP5nlLyHKje5rvaGJYufLKs+\nVU++VaMOm0sYeYCFTr6h7TDcmwwkbsUh5ySNDCkPOO0rjYiIn85iDc1pzBq1\nuWsQ\r\n=MbrK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.1": {"name": "en<PERSON><PERSON>", "version": "6.0.1", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^4.10.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "clipboardy": "^1.2.2", "webpack-cli": "^3.1.2", "object.values": "^1.0.4", "array-includes": "^3.0.3", "object.entries": "^1.0.4", "yamlify-object": "^0.4.5", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "dec51f2dd38fb4a1fb5bf568488c06ad1e7e08a7", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-IbMWvMQulMm1hiky1Zt5YTcSDEdZs0r9bt77mcLa4RUAKRYTGZvrb3MtAt47FuldPxwL+u2LtQex1FajIW1/Cw==", "signatures": [{"sig": "MEUCICZUIuuaPRyDaJ42PPsJu7FG/ULWlOs7/VXXod4kQngpAiEAxELK9NMhvYNvz8w/zLNn/jiu6dC6BcLI9U8yOUdkm8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 239133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/wgDCRA9TVsSAnZWagAAM0AP/jByYNDnaYQqNv+V6mEL\ne0OCtIvbxiXZiDy4Z6FbJpWWUCxRrS0T7b7/86VOG+A3u8o0pyJmlMev3JCs\ns3wQ4fzdnlkydLN4KKoCt3buq/C/H5aAQepG/8ONXXfQtJEMywyOlYJFHi3A\nPsF4ipm2hA/Sh9zQOA12JR3SEytRftSb9AJ43kXas1IGCZ3LCFpypelqx6Pe\njD6ww/QVZNUXIvCi8/vj3B4Mu9f6rmJXOldJA4RTFaIbrc5ascEcEBi9kpJO\nK+jMI7T8DqJ8ObbbNTWucdWdp8ZzDKL94EOZuw7oA8Y5Ev741qDJcFYZ4wYu\nYiIoIF/Zns/+b4TdNAxx5uhDBwT+Q9tixV3sR42Tac3GE/CHzkLeEVIh/XFO\npiXEQnVMiUmvWOyFnJg5US7fAYNkSXc9XZ/pnuMS/OvLFJn90jL4XvOk0L8X\nzVbSql4FyzHZBjBGZV3o+SDvEmdGhgPlXZdq5MrpH/OkMev1s/t1Z/V8/n4x\nLvcL7obp+UByh0ge2Pqo95Nuo4+PmGErWQu1Ph4gdDMbkwCl1kU9ThXYUiSC\n1poBaEfuTb+VYHvcraj2Fxvzb1ZapcaiDPcMdGxJu0CDWsvRLTf7mT2x7KHM\nXVw8DHDrn6YYYiR0lnOwMvFHftKvoALPz1T7yzUfp3Fc/iYlEXhMmhc+R8yz\nP/SA\r\n=jcjc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.0.0": {"name": "en<PERSON><PERSON>", "version": "7.0.0", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "d1f924eecfc44753b6e93f30b925ecc8819d8d03", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-6P4tU6U6zT8SwrgYIU8LV4oTgzBiBNON6DvKjyaz6zmVUro88hvGyOe82blZt16j7vSEFhdZ0a1y7F18DUKoSw==", "signatures": [{"sig": "MEUCIQCUXo4rwHa4rFjZIyowHzH+N/8E8kp8ivSG5tA7WjKmPQIgQ1UlLzLjZBQGv2YaxPLzLzxOA2czvVPeHZwkri40Ob8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZYAPCRA9TVsSAnZWagAAZOUP+wa7wLqx/ln7QxU3+StJ\nviAROLti8SFRCWKycrN1/KQRl4WQvKw2aBJ1TOK9lUEGv5gKgc0Yt3pRLtc2\ndqnLmP0a/jP2H6T7wqZlfO0tAsxwsfopU6DpuOatC7I0OsrTW6VkY6LfcgHx\nXX0WX0t1zcBsLgI7oFoXPPl7ekRiNROjLsbyop+G6mTYuYu/JVGx9nB8c37c\n4FP+1/V71/AoipvBz43AyCDbPwy6Lnlox6LDbr+bxUKDwaEowOBdOFqGWX1M\n8CkedFX6ueRsCZRw9zaVzLyBJG1lEeQx9/7UHFkDWTyio1sOPLnbgssZPh41\nSWcdgdKNYM+wE0S4LEIfGXgEHzHbO6Pt3oqSMh9aD7ZmLf1nORIxb2kWca81\nsm4hIgR5yz0zOTL/1KFfNo61N01QqCytQinMiWNRPLfxo+uut7gByHHg9G4X\nhnTdDhb2MIA+p9gOziJp8wLIQ4n0b91QPrPa9yNgWR5ItblOzVZIlhWbtm+A\nQ3aehya4vrCMNcbnZ9KMYjzFoE7eEMuE+GDdY+ek2VsmywrJkGn6qQW95pvE\n8NY8o1musMnZxp0Xn9gFaDTstYB0KzRHCAR/0lEz8W0MLa2sygmByH/agf2d\nIWQXxND/WUYKQn/cvDmO8nz+A516g30pOz/047WgJ4SYID7s1vCoEHA53t8S\nhoDn\r\n=/PYA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.1.0": {"name": "en<PERSON><PERSON>", "version": "7.1.0", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "c64f80bbf5faaafc962eef76c0d871c6c672eec0", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.1.0.tgz", "fileCount": 5, "integrity": "sha512-38LJhrmyQafVInoYlaEDxomIfjtK+HUtp1JsInWdKtpxk0MlTU60fqYHg0LrKgxxJuq6H89ddw4IkxfQejZ77g==", "signatures": [{"sig": "MEUCIQCacHnjSQp7NZOVgh2j2fc1SKdSSyEPP8yypsbOFAsxIQIgTqIJCJERhx/G7713NPKh8qbu91Ur4F8SnSrnTY7BoCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 239047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceWRGCRA9TVsSAnZWagAA+54P/2lOmA94rCBdx4igL4aH\nulnY1b1pkLcfYhk1BuIN8wdPsFvVpn2GjG70GUsXaUCilE0xthaGHO3mkUtI\nKsKk8hCoeomzWQgsxpTlcHFFuNNrcQDhavV2EBGyy/5lPiQEis3NY9VnC0GD\nYICObrjO5BUagxaPYWS87gmDlIvDsuKTuMbq5pbdK30N7mwX1fqDmw4/3Ki7\ndwJT0o9g0tVLNgqEy8dS/MpXRhCnrD3W75M8haI9TsCTviweM/L235LQaiEA\nxkF+qRAuD4lfjQFGlU0a2grnz7UacQhR8Cg02h/iYygVCpRLLLWY7dAF93Vr\nE4lO/f0BKlwjhQWGUpweR4puFVWRQ1IzQt3IPDzfCtiHM21x77rx8xgcyWG1\n77h37sfCcWU7dQMWzb7KKOpb4w3UleA4tPi9vRTIwjjwPSeI+PC8mzXVx+CM\nVyPjoH33RKBZimEN3wQ2XqTLYT86/H5ENMNIu3maOu5PA404COpjsA3xJNyi\nsxOyrNheB2kkpiCc/CRE2oF1iwtczr4sjgvuMjYwYTkw0tdg3Ps2MzyVgbnd\n2HFI6+iNtkcPGkp5p1CHVJvjLQolwcwGY0/KFd0eeW90ZGY9Y8eTIQgAyZH4\nyH++vlCdxWXzEObMApRZU7uW21P72DpuLydHdeTqC94SHqUC2PDN1rbFA8xZ\nG0lX\r\n=qmJH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.2.0": {"name": "en<PERSON><PERSON>", "version": "7.2.0", "devDependencies": {"pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^2.0.1", "webpack": "^4.25.1", "minimist": "^1.2.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "7e6d7638740d2d43806431403be6dd4bddf15af9", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.2.0.tgz", "fileCount": 5, "integrity": "sha512-hHDGx34o8V0IULC6MnMb+jIMEJ5LBuK/EIbFi8/aw81r6q+wXCvu9QCA9RPbpOaBxtlH/16cYB2HkBnRFTy4SQ==", "signatures": [{"sig": "MEUCIQCi1l6GClEx51nTgY8N40or2FeJppgyItLQl/qwpNT+hAIgUN5Ryb1wYiSHGeazEsBm079Ffh3KZTaZf5O2uqa1tAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctJNSCRA9TVsSAnZWagAA33sQAINiTqDR9s5tsSK/Kh0N\nEehQUiOYpGPGMZ1xeAaUrgkGPag1HLSa6NCpacDwQSZYj7WnlgzRBlRNctRH\nrnVkn/QJcOIHelxvq7LNxEqAgLT+qpuYuGnagJAlOaDH6YQU/6v+xflD+6HY\nYmvuZBZVMuSbjUw9W6QAFw2GbD3Kpjz1FFQN+K3HFNfWixr/Oyj8odkhsUcw\nW78Hwgzxsk0Be1HVF0hjmbpV46WiWFGczZcjwRTlJLIuYFFu2ErHb4Wn/gsH\ngzoigIcRBvyVBgegzNKIyyiB+3aFYDwMScFBGxGbN2hcPYGPUQDqkrHIO0sR\n/Pg2OgvLrJjViLMCz5bUOkinllfG8wM91k/xRhgYLJegsT2lek5NBuYEWm4m\n2RuA8+wcdboQv8XO9BveDFG4Sw5wBAGz6ST2pRVJvqx95wgb90d8JC2JIzJH\nSrDnYZ4wXVduwkEotLqBzdr4m0MvvoSgx1wzzeE/RWYRZyH409yhGN6C5fTo\nqHqwYagelnv37pfdcBLV5TqMATmZNFxcyK3h6elL6nnJeMIIIRUNWcyig33Z\n5KwC3fbT/YE4utdxVWZcApBu7Hahh4OHBJ+mxXjlue2/87f6B3Xft9Y89vcC\nEmCODZR38Q7GHQOnR02cMajPy9UfelHjj292uBWZLyZn8LL2jCxQQBj9HtJZ\nGs/w\r\n=kdIK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.3.0": {"name": "en<PERSON><PERSON>", "version": "7.3.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "b09aadc63f558ad53124248e865d84eeadf1ce84", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.3.0.tgz", "fileCount": 5, "integrity": "sha512-CyFTT0VNXWNkudz4Izb/YwBNL4Ev+ZIlRxn1uTZa8LdDHrRZqtaJn7LpFCI3aAtDjSU1hkh53uHGIFrmDQtb/Q==", "signatures": [{"sig": "MEUCIA6fUwQQojoyfCfa3YPC/GBZvZMlsFGOlXwAsvHSPzchAiEAp5uQ5EQbTxlxulo0pHvgsCV4c5cjXSb+rm3Hodkma/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc0bynCRA9TVsSAnZWagAApFwP/3Nc0xrdo5a+i1S/CTeq\neio6fb1BkhnAcdy/GF1IKwLvGoN1Sj5IAeg/HZ8GL/rcYuBWXB08+WAyYPQH\nPbHd/0jOM2r4ENq8Tk6kAWo24YfCq+xvJOn15DnAhw0894Fixs27to1sRgbu\nDcKfjLmvZxrY27ndOQ/hCRV71z78WHeUx0IFy4lX61Xh4q+Maxz/DSuLK9AS\nMpWWMxx9bDdmlEI7tBozgfnb2GYDq2bp5OqBTFD9kPp1myzsV5zlHgdg3pFw\nB8vkMozaxs45z9YnAd+DYeihgJVmJ7Imn8GXnth4Qu8xKxtNhVe3Lr3NR2lE\n/RC2GARzqogASm4BKkislqPqvE70f578k2s2hDvJ7zXvrqynV1UIFNcUd7m1\n+p/FiNg0px9wndHqr6J/6sqeFN8I5BgtVn6rqU0ixzKTqP0Bb/VadgDtt3Zk\nrO7M6xCt9qJ2obDM6mXfkUAT27u3HHxW8ynjNiKzkIofQc9VWrP9E7zTKcG0\ncKxIviu/V/4KxHCAwLiAvnR3TW7t9IIhobHlGbKALYs4X2YvZ31Yd0A4MoUf\nEstSTemhweUhQ9z2kIyKzIEpGsIaquapW2a/1Q8r/LuNFLh4x2E/dkYLXEQS\nUgFB34KAqc9mqVdUMsri/QBGSSZiJMBVBeo0ulea9PylA5LVCQ981Hy/BzhE\nJ/+u\r\n=EWDc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.3.1": {"name": "en<PERSON><PERSON>", "version": "7.3.1", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "892e42f7bf858b3446d9414ad240dbaf8da52f09", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.3.1.tgz", "fileCount": 5, "integrity": "sha512-GvXiDTqLYrORVSCuJCsWHPXF5BFvoWMQA9xX4YVjPT1jyS3aZEHUBwjzxU/6LTPF9ReHgVEbX7IEN5UvSXHw/A==", "signatures": [{"sig": "MEQCIAyNXLBAZ1f/his5ZQDapKaGHMBdW0AEhjNjVo+LTt52AiBRa7pAE4Vn+blfKldZNrxETqsoEZm4a7iInyhYBj3NwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1KBHCRA9TVsSAnZWagAAX4sP/i4/F6Ri3kz6AplAwCer\n7MF2vXH05bwCn/rynwS2fOd4HSkiV9nmmlcLXm75RogkVL7DJkswmmhknstH\ndM0hbaJjlBA44erJyEYg5wEmK92YiPrPNfMDYkpqaAeWmsElsw7hIvdM07yH\nbhxVKLcMvO2CYAa28OAFdDvu7Sbry2OtQUQHfhMpuV4iV2BhP/hnYjrd6u+S\nY743vDdvcwU9pEElq2DRTxziqpJ3dny2B2Ea1ygqm85FpUfpA2AwNbh++yKf\n1kDk0CNlc1M+TAUmStkuzIgnDLIsBzP8IrfDx8Svs8FMI8UgXxFQVC+Fbcu7\nJ1DvMwTlk4O3NwdxPNcoP0JCA95pdPraYh6enPNDMo0A7hMAwSssbQycBix7\nJvoGf8lwx8AoU0XiENaYJxbyxqIUd9TFsciSPAxQKemG1xUR8aa6Pbo8EB2O\nur/hxzXnCnUUTyWcs4zYeUj0TjhK4BnLz4c+6F6xa5SEYmiwgllwHbtkp2Qo\nhMT8CzPBIm1Dm6SMSs8iNiu3mbS0ZEHlxhy1qBPWMpn8J5/aiSgJ69YJRwrt\nr0+EGmp4IZpQveoeikwoFrakPlWRXHozzZxGVaZLjgPaHCJaBG5vgwNRucUb\n4GxhskIQ6Hkq1oeEnjTXhbxhuzEH6raAyChIKDNmhmAUlB7O5lMO3bGglySU\n4Lmt\r\n=GzpK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.4.0": {"name": "en<PERSON><PERSON>", "version": "7.4.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "bef4ece9e717423aaf0c3584651430b735ad6630", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.4.0.tgz", "fileCount": 5, "integrity": "sha512-FdDfnWnCVjxTTpWE3d6Jgh5JDIA3Cw7LCgpM/pI7kK1ORkjaqI2r6NqQ+ln2j0dfpgxY00AWieSvtkiZQKIItA==", "signatures": [{"sig": "MEUCIA5ooMgsIxg33DkbyJL7zbNpj7O2BeHlvIJwTjF/CDRdAiEAxkqn++jcuuD4rC7uXEPYynYiDcL2fd+6Votbi463ywI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiVbYCRA9TVsSAnZWagAALw4P/0F9DUeM7MWHrp9Fcv3n\nkb6vn4K6/kgf5cTyTppzZGxTzLTNL9XHUxjlj3wW8+5unvsbzH8Up52hbx3s\nA3gHT7xj/iuiGiAdq7kv7VGclQty4D0qT/DgNZDUdIq0DsQNCoGoXIDYXKuk\nJHyQL9zD+ZfrCQ/YIlh8ZxKQnzLV52tyLs1E7AJHhJTOtyxjdd6xwM19s2v0\nVTHK9yUN4XCkPBhAZUNwHBZE7ytJkB47ApGzQgsIKn0q7DKv1qKXh7OppATD\nuu+Te2YXOq5B0WDMhAPEqZL1VP0uztBtzoJP7rmJl27hZf8h3gG0k4PyCgI+\nlsDqzrXfcUWqTk1OC1ciZdwgqT/wW7/es+a4ptJ3MoT8ozVegJnNRZ6T5rKU\nN+1uD7lTXXDJg7kikGsQNQeaFjt9f4MFcbSd8Bk/MFW1F3bCXc58QBzotqae\nhl/RdvAA4uY+Vob6sdmd2AbSmDXOuonap2JxuF8xgK1kp3Sm0L1DsYGoYQJy\nycxkwhMjA5a5BypzPBeMDFOnqtTxEYpIl4dqa1NYnmGsxzrAm1M7VVhKT2yR\nNBMbMOZHiMYa65baNeRyIy+EC+GIJtz1zfKaKayT3Ptqg4SzWwPEkPxqg0ur\nTywpqISRPQlx0jbkYHWtklvmpVuLii4vfrkdfcaZaDGDvVzF88POLgbuJ4LF\nMU8I\r\n=CQnJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.5.0": {"name": "en<PERSON><PERSON>", "version": "7.5.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.0", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "91410bb6db262fb4f1409bd506e9ff57e91023f4", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.5.0.tgz", "fileCount": 5, "integrity": "sha512-jDgnJaF/Btomk+m3PZDTTCb5XIIIX3zYItnCRfF73zVgvinLoRomuhi75Y4su0PtQxWz4v66XnLLckyvyJTOIQ==", "signatures": [{"sig": "MEQCIAK2Hn3gx0FC2mL3BrO0q85njfacaEjhjqL5ZbPnir9UAiBedGfDCBVKKNnuMAkINMdhyu8zgisqiZxK9ulPEhbWoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155228, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1YlzCRA9TVsSAnZWagAAVp0P/RMROhBChTlVyDSBpEiV\nxvRRvEafIBm5tE9CE+Umc1UTc+BZrfVbUG+OeusYK/5jSNmfhopCshfOxgP2\nsuLDQoVfsdFoVsHurrUf+Nlutvi8doGM4b8C8rHPrR7eSfiqoUkw22553sle\nLKCVKMGcPRku9ipJXc3mLV+gocHnuVCJDpjbLzhl6dGyuOExv/5aKZvwANix\nJgZBg4HVRnERJRZ7yLVHPoM2TBhyKaJkpfJHMD6E1FRLoeFD72MB5cV7mxrf\nNnNlZthvpvWAWRvyD/0if2mMy5pzYaOnabPklxhrNqedAGR7JCZMGfa3xwNQ\n//dv0ws4daKe05c3q2RZO81oChFV6Me9yED3cORSao9Rm85oxGZ0+MhSDU+z\nTff8VNeVN8GrwOgCdtO4f/J7ghj8zmFjuhOtXUTCXxryzfXB7+cL28QJUrd6\nMduFJpJMd1+syidpZ/AJLBsFCJT8pIzfHKNGjJAQwy8oxt8eLsDJDyZ1VvdN\nkZ5Pl0riSupVFjEcGzUML4Z3Ve4D1uKRdp49BYbLWYqC0jWkEa7qKahpNfGv\nurW5PJExu4FCw4c43erdrAt5/EbPU66r+0yxBMLZ0qF5UpDSRsHugHo+dO/u\nhj+XF5qLXn/Pi5nJ3I651lvMCOsO5D2vU5nKqwWiyi3sIAKrrQLNP7FbxWtz\nJwp2\r\n=lLgU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.5.1": {"name": "en<PERSON><PERSON>", "version": "7.5.1", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.2", "jest": "^22.4.3", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.0", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "clipboardy": "^1.2.2", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.4.5", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "93c26897225a00457c75e734d354ea9106a72236", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.5.1.tgz", "fileCount": 5, "integrity": "sha512-hQBkDf2iO4Nv0CNHpCuSBeaSrveU6nThVxFGTrq/eDlV716UQk09zChaJae4mZRsos1x4YLY2TaH3LHUae3ZmQ==", "signatures": [{"sig": "MEQCIC5DOpY2GUnEfbEXBuarI7Nks6PsbsCyHVP4JTZoVVJjAiBgnx6T4wbqGIRKuJwqLpkOVZ74qCCJS3zsDtxbWva54Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepcooCRA9TVsSAnZWagAASdUP/Ao5nXlAoomQoslGnmVV\n6rxPsLWbOUOIybg7+58E3YiSnNkTdbzd2c5FG9fYRQLbElyFFd3ujlBei3rY\nc5hDSEMmhAR/XwvkAlRoM7i+YG9iI8jGalFgOVEmazmyqn+TbQaJQ4b2BGT7\nN2ZlRX3T6m9YAVWUAQgknDj+IXZkZ1mH4Uh+8b74exWrVyViLCk5kgfC6ACo\nQ1hweha2LGeSJ0QGK/3evD4wCFprquqzzQR/W/NadT8R7hXO9/fUILmftY+d\n3N4W8H6pECo/TLWYuEadmAhmzHgHrpGN6jMoL73YEtjso+wmZv3v/OeMcwan\ntvcwm62lEmlwK2BB9rRPpgCTqOJIjPxN2FrA1i82FICt3B6kjw8iOzFXTzFy\nhgq0OE6e+QjJq2TgAIU1sx5LQDUHSsl4HW11DP00zVvVnHoFqw3ZvNdqs63J\noICJ4W9Q8vKe3cVTqIsFz9tJJ/ceoqWP5k4RleNJ9tdGZJW1Rg+eNaVu/hhs\nSJF/7ppKwfKFhOOWK55Nn5+PqLauVwOiMULbRMMUp5Q90wNAO/ikDB+Ao9K/\nDLq1aafm8K9UGiFu4n7694ccYnZ/eMPA6d1hq1exl3ayj8H/smMuoh19agJS\nlfNx3x6zWJrc4MTC9Hj8I5O1dKj06Bmm4UowXCoyxNmEXZPeNciXEWUMcWLZ\nPhcz\r\n=i1P2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.6.0": {"name": "en<PERSON><PERSON>", "version": "7.6.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "8201bf6162b3afc372278992d0851d47d0567d33", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.6.0.tgz", "fileCount": 5, "integrity": "sha512-Q28imaho8jwBach1iGj7qqQeStDV9z7ApPAg7WQKLEyvqHa0HcES++QOL7by+efgAnkXzK51MQPYgj/3I7s/dw==", "signatures": [{"sig": "MEUCIA0BshGRxNQD87nqqRsQ+5bqTC6rMtaPuoJFBE8yTYVDAiEAt1ZwVydG/GzcrQEHqd3zc4+xWWuFgUYWCkZAG6eGzpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFwqzCRA9TVsSAnZWagAAfQsP/3lG+SfXbbx3JZTb82zN\ne4WGXjoYNlv9DvH96kJlZOKz0xcncfJ5krd5w/G+/43jT5mB+ceyXlDZpPWR\nEVhBru66I8ZKx27bT8CpxntsOhRhz+mdnufYgRhLPeZb7owSrUpRhZEeqfmq\n3QvI0hI0U83BJ5axTVmaF4/2hXgGudkersR8oCvHoYGbnOLtoZcfmvRJjZ45\nX/NJaP1Jks324PHY80D0KIKsb0NlTVmqIREwdzddC2faHbFUD7wEI+c+ye6i\nyjYR4ARfhbdWflIxtpD4tlHJZoylLWcJm/z1zaozd4GfUpJKVTrzAqTTIn/Q\nAyvBrmm4Fxoow0VNaDWCseeN1I5XFuDHnLs5gLArGcNUHg1mOEgo6DYC8UQt\nIbHAPdmdL0VXzwIIvfhaPrX1FLV0rxoUXN74GeAeR859F/5xRhcrjG9cJ/BB\nxMha3etTTi4X2iq94hHgsOogZ1khbIRHl9eHQ5dkXghMOJ2CSjj0u+COqZVD\n/ZdjPBZq+VyLjYUQiICZRE/JOL0fblLdoAKRurq0goRpYYPYHT+Okn2S6ove\n4bfwG9QI6yGT25OhYR9GMIsq0DApU9i7UiSgTY/5QrWj+i9tCaEmB25ffLpz\nmufmiupTtJ8xjrHzuVUbhJ/b1e/fz3rZl2vyWE3CKkEk8ILnZJJysTh3KkfF\nCHd/\r\n=SPxi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.7.0": {"name": "en<PERSON><PERSON>", "version": "7.7.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "fbfa46d739dec0554ef40220cd91fb20f64c9698", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.7.0.tgz", "fileCount": 5, "integrity": "sha512-XX0+kACx7HcIFhar/JjsDtDIVcC8hnzQO1Asehq+abs+v9MtzpUuujFb6eBTT4lF9j2Bh6d2XFngbFRryjUAeQ==", "signatures": [{"sig": "MEYCIQCOT2czHrwzUlib02dvtZ55AyMygaxccI5oZ+1m7rMTGAIhAOrnGwDbY2XV0Oq0GmjXIFUpV4uofWOOAy/wt1w27s8P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFzlxCRA9TVsSAnZWagAAneoP/19SSUlW8bgugTSMiH5/\nAwJuQMi6d9JQGgWqN7sk7ykPAmnX0oozkRVBQPEIdNVPArlsiy+LRiesE9BV\n84tB+K9ydKjoI+Mxz3d4+C3doH190y4XHCdyITmpleEzGSr862FhGU8vXCi4\ngfrfOIcqCcAftXOBoUYXpSgks4u8ONchRmE4Be1jgLWsHb7aj8cmoVJcMCG2\ns/Tn91NG4Z+A+xyoUlorNhCXhvQ0ktRDIZMZCCzCNiNMEJXo4a28sfPtIXcQ\nlFWHylFnlMcJxwLgdWipXceYvlzQkulWNbxlALb7hv2y0j7Bt4BowTx4eAKR\nfa+7XOEDWcwPP8lOUjqYrPo3Xr9FJzRasGIXXcNCRBqPMp2NQhFcnSUE3PHb\nMuPqXCR9IY/jn0DYQCha+PMQ1zSyPb+8RGD3dfBYbYpzPSTYsJgxPPUNnXVF\nek1iwJ4M9ACLbaqIyr7Qb70i4wgGDuPkpxnOeT+I3glJpqiDuaiwoTqY6qLj\ngTuGicyTlloid8ukNOMY8eKFppU0OuMtk7bEvAhgGQYnzS+jb/rSp1ygaOt3\n/W6rOLzwFtx0BBaqYbG7NtIVdaSCvX3gTAMubfP9KBQ2l+QmUyTRNtRUJLlz\n0oG3vZ9isgv4KGRWmnZ6DeEtD6Pjfi1H5ColcntAkfAi1U/gHgGaxm//g/nm\n5fRc\r\n=s43e\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.7.1": {"name": "en<PERSON><PERSON>", "version": "7.7.1", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "f275707c204768103a3e96fdad9bbf649671bee3", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.7.1.tgz", "fileCount": 5, "integrity": "sha512-KzGxhG0kxl5egWBM3ev/YEgsDPUt+gN4LKKunk4J74xxeLpIfZF7fULnU+kEbYIcbUO/rviCkOXOXVSRbDhR7Q==", "signatures": [{"sig": "MEYCIQDhZ7nqwhRWonrpIbaVO04N7O3VGeBQdzNRUqpFRlaKnAIhAIyX7An3gPmwr9ARIT2aocfsEhIhTFF5K7/doIElIvDa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGPqICRA9TVsSAnZWagAA2vwP+QEkTQq+y6no/7+oDTZk\ngwd5ntHxbgHN4MamgV/1cKjCRo6tZ6NQhUfM1tUXgcviYlF+h/Ff3Rd2tU+h\nQDL4Sd2IoGVrp7PshRmJBorYM8lhMelEjboS9Ie8Jb3dgxJeqaPnwSj6knYB\nOm6R8rRh+c1f8PksVI2l9ugAC5sSpCHGiRhsreQNgN5pNGlzjfuMwr4RP4pa\nsdKf4ONfAC6oyCS1qYFZZwtlSI3Llty6xuDGuksaJsDklXw8G5/Wkzm9FvMW\nUHeYbTyGBjrIMldH7g1WrGNJa+2x7UuGHziuRJ0Rra9eg0zJ6DAs2W9TAkj8\n4DdnQZ31Da5cUts+77A7NJ/WD2UqUAehS5Inq+4NDxIpQKskjErUqIvdl5KY\noA4lVvvBCGgwBeUk8zQQtt6pSJAX4/0flVTGV2eTJ/Th7306xXfoQ3h88DSz\nPbcAqACFp5s9eFP3zc9j/eJHCs/L2aZOWTGVTbiL47bSBkOoT401kg7rE56x\n5ZiDQ1gRnp77Lizcj1QDhV2BWO7pE1PrGC6DOp8GH5jXX+vwqLf3jEA+IByV\nvPWmCRjiap0nD3toibypdKjoocT7OzeB0C1sDytJcs5WXVQACX0JL9112g2K\n5Tb+erzZ9B+TW3mirxydEtzUMf4BT/mVXke0X4OqVf7d0a3LsjOo4oUoTkAB\nWHXb\r\n=j3R0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.7.2": {"name": "en<PERSON><PERSON>", "version": "7.7.2", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "098f97a0e902f8141f9150553c92dbb282c4cabe", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.7.2.tgz", "fileCount": 5, "integrity": "sha512-k3Eh5bKuQnZjm49/L7H4cHzs2FlL5QjbTB3JrPxoTI8aJG7hVMe4uKyJxSYH4ahseby2waUwk5OaKX/nAsaYgg==", "signatures": [{"sig": "MEQCIAXc3nUz36SxEa5gX4IyONLMcUy4edV36p91ojHZeV7jAiBS5+CEP3B/yU35haDV9oDVdNJvhaATUNqjl9os7WlCTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGP1eCRA9TVsSAnZWagAAbvcP/13A2VZYOzdgN1G3ozi4\ncZV0CLLUEDm91KX8d6RbaYThCx3tWv0pvKOKKHD/TgRA09PpLfT4xT4fSUrk\nRWuFGUlun/4dl/VMmVZHoZRDU9BhQyxqov35PPcIp59y9BOrcBuBp3kDARkE\nmLBwVJRoIkXxRuBY5uUHCPU9riLtOZAAkzHtmTqBsx8A6bzi+7CnHMjyqcVY\nM6MLor3SaA4frFVcZurODyge6+pOgRUURBCi39mZhRS6Nj+PcsPjMKnTB+Sl\nvTPltS4M2NEl2QVjs8k99X/MfXGyKqZBsayhKO+HaZ5RK3Ov5Hfey1ZGmGUl\nsHdHfZi4fa0rzc9pTSLXPwO4/Mrlna88NJrOHyYO6czR7VfWznc2QsqQASGl\ns/r3IAv6JmN6b/mz2LvrBD4xs12yg8UgpDskLr3PvgH3z/+4EEUX7EBjuoDq\n1kDhm4Lm/SoUbZ97rwK6iLOXfrJdTEbvSQ//EFm72FTi+Bk88dUU7Bbmr7mA\nruzPxNEMDu+c2luWgpwrGe6Yk2oWS8v1nYZZEkjvPTLpTX77ufrdkJJAhChx\n0BuWa8qzAhYvscFLxrGYD3zKtMmEv5ZaFD74OQ6rdACmIl/jSQ8xnWHDrO9z\ngB39Awnj+DsMyty9k9BnastOB6cDSvhI2eXLpdJAfXo1Imn/5bcGoEUbdj8c\nO3G0\r\n=aEKh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.7.3": {"name": "en<PERSON><PERSON>", "version": "7.7.3", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "4b2d8622e3e7366afb8091b23ed95569ea0208cc", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.7.3.tgz", "fileCount": 5, "integrity": "sha512-46+j5QxbPWza0PB1i15nZx0xQ4I/EfQxg9J8Had3b408SV63nEtor2e+oiY63amTo9KTuh2a3XLObNwduxYwwA==", "signatures": [{"sig": "MEYCIQDuo7bLwQQ+MIpcbuG0/w/zGqukRJvWuBuJanSUPPX3oQIhANpkM4hjEeGvaE5ghPz79IUq4kuUTIWiKqxObtFbTx55", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRGYpCRA9TVsSAnZWagAAgrIP/2kdd6e8CqogtAkaJ3pl\ni2M3NydHMWgSVnK/oHzYLfeN833hMQ1LQjDI73E9Nu/US14NrZqztu3Q49e2\nKsh2hx52EhRrcs9oF4WC7tsge9NdNP0V0oicsdTrbSo9jP40e9SChCsqweFn\nHaCMFp0sFvqY2o1PqNEq0Pj+fNRhgAVknPcGtzzSJ9lkcaygTb0Z5sB46FEY\nMoqhWSj9l9O55O1JcS6ceLZISbBPWGAO9MNaeT8JLUcg7bBb4urTtvDbEQzx\n8d148dgReKIQrq+oKC/tK2j60ubm6Ol+sHVGDdPAu6ofYaz5VVdQ6Ueapq0q\nlBN29/VOVLlKy7lYER0IDNjK88blUvoyDKs3DhgUezzB8jpIlRbSdmQ6okUs\nx59H1Pnuo4a1FUtGwQzd9qyLf623F1TYw5u9RUq/Vt1KYQdjQhuozlcLTizR\n96K63QL24Lxx9M0x6sJiH9q2M4LnX8f5hrGn9jXdlN6gNTMD62vT8RR+hBaX\nxBOZbgB0gBj1Ym84W2L9a2LG9KAwmwrHP5K6WpqrPKCgHFyUkXYhOZ/60imj\nGmsDJFcSr+SISah18T4Ye8jDnisuSEXMbbP61uEMAp8WUT6MJAVvf/GEe7Q1\nDq65b7G0nLFtU49aOf7ECa9DKZU9yreTSBJYbXc5A41bQAyq68MkNxgZPWH1\nX2RD\r\n=zq3H\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.7.4": {"name": "en<PERSON><PERSON>", "version": "7.7.4", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.3.4", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "c6311cdd38a0e86808c1c9343f667e4267c4a320", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.7.4.tgz", "fileCount": 5, "integrity": "sha512-TQXTYFVVwwluWSFis6K2XKxgrD22jEv0FTuLCQI+OjH7rn93+iY0fSSFM5lrSxFY+H1+B0/cvvlamr3UsBivdQ==", "signatures": [{"sig": "MEUCIQCuX6jR8CSmkTuPVEKyJpW+I0OWCuazs4CmEZBPHN0APAIgO48beKrNwDoanYeIg1kgxDHZ8I43WB+wqHIOHXMUEQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGCkpCRA9TVsSAnZWagAAPtYP/A5j3ACjI27i/NDYrFx3\nOIHKKLt3hEj83kiG1HKugPSM5DvFVd/nMaA4uuQI4OYolV9HONTZvxBORbhX\nuGijJQ4mjH6Sjhdu+WYCScdt6mLbW2M9x2OMaPzETnGr/3lRJbrJKXfTDpwU\n4FQperGDxYWg7/4OINDlu7kz/tXu1DNFvYag4QA6+g1Zu1tMO0kQ+rHJfYDI\n0Q/lFbtHcVly3g1MYKBXI48MQar7fjR0+XcEx9XZ4JfCbNJ6oLI5OWhnxZy+\nAakVfBVnaNNLN7Dao6RrG/wFr9n7Zmu1bnhvvfV7AyZ+JPloHSHs11mDlTwM\nzFsOBqgF39ZqLyOb0bWxW/KUYynQhzs1H6FYNpmGEbK73kHdsZIJm0yNTTlp\n1tWtCMYR1YwYJiAP0igJLlsTvkaHBNm0GLtqMpqTWP+x2KuxlTCpqUC5Sf4X\nIQjSmKHh4cM6KGVXEr+gMQEd3ZD6s7abu7yZgXiFn3f9nMDfzYPNE55R9QIL\n4OR2hMkzjvgvWJegMbMp2PDxElLUZTVQ+IefW98rzJc84ZdTUZleBlWnEzlw\n7rI5XF83Fh7JPgi3aRMgEjMEXPchc9Zc5BqXm68pB+lZwYmc/LmWIdsIHtOb\ngJGPzm2UtdAuGgUNKRQzbIm5PVYNtFKxW25SQZbHYvp7ckshMVtPZHZGoZBb\nwZrm\r\n=Z1Rs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.8.0": {"name": "en<PERSON><PERSON>", "version": "7.8.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "e74031b916c6e6e16fbc3a2c4b5744c0bcdbad57", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.8.0.tgz", "fileCount": 5, "integrity": "sha512-OFCVhTgHckhAy/EP5IV6hvXflfZrHfxfYLlzeFzzWj5+Zla5KmTqeSi938LPyRCtKDe+lutr1tCmzHYTIe3zCA==", "signatures": [{"sig": "MEYCIQDdhpehlo6RPsi9B7sItRSaA7sz6PS2yRVoUE4XGa8CYAIhAOh/xuLWrwgUehk8e6Hhk6dQIfMkdQGQeCDbVJ8ve1ze", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga7nRCRA9TVsSAnZWagAAmkQQAJb8as1aLezZzp6s/msc\nFM+Xme4gAOB6Ji5re3+yA2DkCxASWIQfplx4ZR3gIJ99behkJGFBWcem/FEp\niGRtLyvJO06smGz3SOIrFGafVEw9OPN8kaPsWNfRmFQwj6DAfXrcR+afc922\nn95KUsgcttkmv8NS+veayfIKx3ljD8pPiPtVP9W4F64Z+d0s5Ik/w7y07UnR\neXktoA70H+e0ZL22aKsGGFRt//T40Wu+uFnpA7VcSaOQop7/75uTQXDsbRyx\nHQTP2RxuqGRlpQQJk7Cd6A<PERSON>EfZMNONUXn1n3cDAT6xWyzkJ2MsOeCAOdPfg2\nTcs3NOHQN1GwjiGdxen6iDuc0U4npFVOrbbOfAKpHUHAc5ypTpYXCHvVM/Rl\niYFf5RFZljH0kSCplPX8DeFYeMisqKyRIaOS1mMVTEzyPfkRlh6j1RDS75a0\ns7Xe2UkQhwwCQ7aOFLy7WWmaImFVr37Ra7laEZspOsmDjmzBW3kJMx3rApXd\nL2k+LpXgpuPa5jjG0VffWaLFwNahkRqRDKjpTVnG3BaEHc9PkPZMazHwi3gj\n0mCnIQCB3pSD+f92Rel2GblbHOfwN2Nv5DB7iYiAv48fhS6k/K+LuY4GzrNr\nvntvSI7B1fd3kbFpOrXfxeLYiPE7kKSTY+pjy1GW1NDCfZ76SMpeqqrtInXN\nHn4z\r\n=3XWG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.8.1": {"name": "en<PERSON><PERSON>", "version": "7.8.1", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "06377e3e5f4d379fea7ac592d5ad8927e0c4d475", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.8.1.tgz", "fileCount": 5, "integrity": "sha512-/o+BXHmB7ocbHEAs6F2EnG0ogybVVUdkRunTT2glZU9XAaGmhqskrvKwqXuDfNjEO0LZKWdejEEpnq8aM0tOaw==", "signatures": [{"sig": "MEUCIQDunx/XzTSC1xvnkO4m1jxpIz1ogJA3SDPfZ8sF0WXqfwIgJHjHyTykfiypQD7eCpcJS5M2kKkBB1G8QH3aaufWFhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga7rgCRA9TVsSAnZWagAAjn0P/2Sqd1+Or67IeUZzpFN/\nJ3H/BrXld1zf+WzmxprYNhrxISH7dFcMq0udYdJOgQ2arzG0HnbUYP2NBAqm\nMjLX1YvMAudHowBKzrjGzUq13UkbVr8WAknZVziO8Nwd4LYzRkOjJKHhrf42\nP33Oxk9uUlJ0wf8lRkSP9TS2xIlzou4TBNbajHuaaly8Mb0E2gcEzxppx5vZ\nG/RZa9Ek2AJf0s/CtOw8YHNshu4Cbio8NnKacKALzqPjCp5L8id4ne0XozyM\neiNG/qlbONaHU7IPfcVvwEU289GFBsoj6G1+3iBxh7zT7gCI6kHqnv9co1h0\nE462abnEsfgCTJ/E4HuFEwfoYDa8u/C8J+Iv9D59ZqAHT7F1PvBQ1ZQZS1JP\nWIY6W5YDUxG3g90bE9WC2HdjeXrDmAs713NN4pE1lj5NyB4YXiktBmOfW1HB\nMfqvjtO1MeUN72ibvKXjSzcwCZ1jm91gCbts+IF6kTR2wR9HKKpdi1HiCXfj\nqmZdpeY8MwIhScL6DT9FTIOG5nMdiUtmUFKD+qgg3fFhIZOoskQMsi/2qUjs\nR3uxCNwwur+e/lpeuXUrP1BRlJ2Wid5VQWCjL24oTi4sI3Pd0wqBhXnbgfm+\nW51K50xvlxMKRJR1N1BCkMWXyhd9ec/NYQtNiieVJabAJbc5NZZNP2iQPOUL\ngaEX\r\n=AHAX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "7.9.0": {"name": "en<PERSON><PERSON>", "version": "7.9.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "47594a13081be0d9be6e513534e8c58dbb26c7a1", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.9.0.tgz", "fileCount": 5, "integrity": "sha512-RODB4txU+xImYDemN5DqaKC0CHk05XSVkOX4pq0hK26Qx+1LChkuOyUDlGEjYb3ACr0n9qBhFjg37hQuJvpkRQ==", "signatures": [{"sig": "MEQCIErg4T6jRuxxIHyVdQSLMBGvIp9f7WNVpFe1YbxR7tp+AiA3S92DSy+PTNJkD5dvj9O8x1n6uGrHQHReoZCX355i8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160530}, "engines": {"node": ">=4"}}, "7.10.0": {"name": "en<PERSON><PERSON>", "version": "7.10.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "55146e3909cc5fe63c22da63fb15b05aeac35b13", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.10.0.tgz", "fileCount": 5, "integrity": "sha512-ZtUjZO6l5mwTHvc1L9+1q5p/R3wTopcfqMW8r5t8SJSKqeVI/LtajORwRFEKpEFuekjD0VBjwu1HMxL4UalIRw==", "signatures": [{"sig": "MEQCIAEzFWvErlc+DG2st3EFzT2eRtYxwPItfMxxhUjv5e12AiAsBs+RO3vw0plpyhS2UbG3X4Nmb4gA+I/wzOyOhdxKvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161375}, "engines": {"node": ">=4"}}, "7.11.0": {"name": "en<PERSON><PERSON>", "version": "7.11.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "c3793f44284a55ff8c82faf1ffd91bc6478ea01f", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.11.0.tgz", "fileCount": 5, "integrity": "sha512-G9/6xF1FPbIw0TtalAMaVPpiq2aDEuKLXM314jPVAO9r2fo2a4BLqMNkmRS7O/xPPZ+COAhGIz3ETvHEV3eUcg==", "signatures": [{"sig": "MEMCHyOf1qrl4V9e9EKVENJZjQcH8BKQ53qBKmiMb7J8dX4CIHSG9puwN7+Lw/jLtdjqQYSDVVWRaZloNo38UopphTuV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161687}, "engines": {"node": ">=4"}}, "7.11.1": {"name": "en<PERSON><PERSON>", "version": "7.11.1", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^4.29.6", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "2ffef77591057081b0129a8fd8cf6118da1b94e1", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.11.1.tgz", "fileCount": 5, "integrity": "sha512-8PiZgZNIB4q/Lw4AhOvAfB/ityHAd2bli3lESSWmWSzSsl5dKpy5N1d1Rfkd2teq/g9xN90lc6o98DOjMeYHpg==", "signatures": [{"sig": "MEYCIQD6pwQPOH0HvkWLX05Ok8Reta4lDTqcx4g2cWFk9bZZ5AIhAIeIOWAzNz5IgpjoqKf4pgmyMVag1c1IyvOwM9m6kgOp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161725}, "engines": {"node": ">=4"}}, "7.12.0": {"name": "en<PERSON><PERSON>", "version": "7.12.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^5.90.1", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^5.1.4", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "b56723b39c2053d67ea5714f026d05d4f5cc7acd", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.12.0.tgz", "fileCount": 5, "integrity": "sha512-Iw9rQJBGpJRd3rwXm9ft/JiGoAZmLxxJZELYDQoPRZ4USVhkKtIcNBPw6U+/K2mBpaqM25JSV6Yl4Az9vO2wJg==", "signatures": [{"sig": "MEUCIQDfPnXzyQmqlqdA096eZuo3nSb62+pT3nLY+N0VdPo40AIgeMG7avx8FofEPO1/sCYJzNWZNmFJlh3aWLJNO4hgMG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160969}, "engines": {"node": ">=4"}}, "7.13.0": {"name": "en<PERSON><PERSON>", "version": "7.13.0", "devDependencies": {"esm": "^3.2.22", "pkg": "^4.5.1", "glob": "^7.1.6", "jest": "^22.4.3", "husky": "^4.2.5", "which": "^1.2.14", "eslint": "^5.13.0", "os-name": "^3.1.0", "webpack": "^5.90.1", "minimist": "^1.2.5", "prettier": "^1.19.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "^7.2.2", "webpack-cli": "^5.1.4", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "yamlify-object": "^0.5.1", "@babel/polyfill": "^7.2.5", "@commitlint/cli": "^8.3.5", "@babel/preset-env": "^7.3.1", "github-release-cli": "^0.4.1", "prettier-eslint-cli": "^4.1.1", "all-contributors-cli": "^4.11.1", "eslint-plugin-import": "^2.8.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-prettier": "^2.3.1", "eslint-config-airbnb-base": "^12.1.0", "@commitlint/config-conventional": "^8.3.4", "@babel/plugin-proposal-optional-chaining": "^7.2.0"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"shasum": "81fbb81e5da35d74e814941aeab7c325a606fb31", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.13.0.tgz", "fileCount": 5, "integrity": "sha512-cvcaMr7KqXVh4nyzGTVqTum+gAiL265x5jUWQIDLq//zOGbW+gSW/C+OWLleY/rs9Qole6AZLMXPbtIFQbqu+Q==", "signatures": [{"sig": "MEQCIENu0wRZlzWF8+Oi5IB+SMckr4zgUrxjcRsu+Wxyug0cAiAb2iEbJdW6WtnwDuZa5vLeVHdehkhM9e64jVbwQq9QbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161712}, "engines": {"node": ">=4"}}, "7.14.0": {"name": "en<PERSON><PERSON>", "version": "7.14.0", "devDependencies": {"@babel/core": "^7.2.2", "@babel/plugin-proposal-optional-chaining": "^7.2.0", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "all-contributors-cli": "^4.11.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.1", "babel-jest": "23.6.0", "babel-loader": "^8.0.5", "eslint": "^5.13.0", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.3.1", "esm": "^3.2.22", "github-release-cli": "^0.4.1", "glob": "^7.1.6", "husky": "^4.2.5", "jest": "^22.4.3", "minimist": "^1.2.5", "os-name": "^3.1.0", "pkg": "^4.5.1", "prettier": "^1.19.1", "prettier-eslint-cli": "^4.1.1", "webpack": "^5.90.1", "webpack-cli": "^5.1.4", "which": "^1.2.14", "yamlify-object": "^0.5.1"}, "bin": {"envinfo": "dist/cli.js"}, "dist": {"integrity": "sha512-CO40UI41xDQzhLB1hWyqUKgFhs250pNcGbyGKe1l/e4FSaI/+YE4IMG76GDt0In67WLPACIITC+sOi08x4wIvg==", "shasum": "26dac5db54418f2a4c1159153a0b2ae980838aae", "tarball": "https://registry.npmjs.org/envinfo/-/envinfo-7.14.0.tgz", "fileCount": 5, "unpackedSize": 161856, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtzWSoxNDrpjTkbIS+b9REM6nfo7rCkAfwN5n8/ePaCwIhAOqaDsr7E0gAnTUy89rOKmHJZ580PAAWthD9PQEgAQwL"}]}, "engines": {"node": ">=4"}}}, "modified": "2024-09-13T06:00:14.133Z"}