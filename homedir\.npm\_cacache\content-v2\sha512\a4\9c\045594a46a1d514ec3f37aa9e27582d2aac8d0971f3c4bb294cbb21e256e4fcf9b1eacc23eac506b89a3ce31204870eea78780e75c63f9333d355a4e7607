{"_id": "cors", "_rev": "382-75b1de79ad33d0ed10c15ab137e8cf18", "name": "cors", "dist-tags": {"latest": "2.8.5"}, "versions": {"0.0.1": {"name": "cors", "version": "0.0.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.0.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "4354f78c02e659e6199d6b9c6f2199763e387f0d", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.1.tgz", "integrity": "sha512-ieXsXHf1hcDNmmPmLt+j5eKZ3ldJ3BxpVVFWFt/eIBo0N7dSd5XZ/3VNWN5OpqXmc/+U0ho2qPqLuvq6zAkAUw==", "signatures": [{"sig": "MEUCIQDt0oP0d2biqs8KmQwPNaOIb+Q8m94Tn5lmSQZASbvnFgIgPYCoSkpNxqxRKnynUF9qwWvTbtvEbEEVJcqr5HrpoT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "latest", "should": "latest"}}, "0.0.2": {"name": "cors", "version": "0.0.2", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.0.2", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "1eb6033bf2939081e3868be6b71e43641b0218ff", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.2.tgz", "integrity": "sha512-iyrZSKt29ezaedRf5ED6eMlo7TnKxqfI0YVtffDVQLNlTHKsQAbTFYMrcUeiPxkSg2CEN2FovW/E8txNuWU8ow==", "signatures": [{"sig": "MEUCIQCgUXVsemwtopSNAuDFKyu72UZHK5IY+KZYxCne6tk1xQIgS5PTax66gkowtLbRFw4TuaXacgDTLOiex8xm3UdDe2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "latest", "should": "latest"}}, "0.0.3": {"name": "cors", "version": "0.0.3", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.0.3", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "93fe11c0749e942e7d5347eb52ea10c8dfec44e7", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.3.tgz", "integrity": "sha512-NIcy+BSGDJyy/vVluBaXIXW8e44HH5Ov0+Xwibv57sgm3vaaTh5ih/EsElnB558H9qb7F78oX+ft3slvLQByhw==", "signatures": [{"sig": "MEYCIQCByK/tVyUk887VLNgtkPMVFhgyRZQKDnsSR4oZuWNPvQIhAKDGCiPj+ntT3q+WGsLaYyOprYCdulIoq3lU0YNKGUgE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "latest", "should": "latest"}}, "0.0.4": {"name": "cors", "version": "0.0.4", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.0.4", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "5131fc707f51dc9d4afae8eececd7e6cc32cd96b", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.4.tgz", "integrity": "sha512-wzh67PmTTGpFToaeMDkEs8HDdyGG0Ir8ObigHwIlVn+CixYRaHChRrqjZRJ4lKaD66hA3BfwovCofyRLwsMQ6A==", "signatures": [{"sig": "MEYCIQDc9Bu+C8mtGoCoAHTuO+VrKDjWempLhln7Y1ANNfbBqQIhAJahy2h2mNKN9El4LAJZVG8fiMKEtANhWTNNqWMTlv7/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "node-lint lib test", "test": "mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "latest", "should": "latest"}}, "0.0.5": {"name": "cors", "version": "0.0.5", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.0.5", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "7f7130d17191d635dc437ad2b0688ae53865c524", "tarball": "https://registry.npmjs.org/cors/-/cors-0.0.5.tgz", "integrity": "sha512-aVmn/vDG85ehM2HC5IDqMi5yAjf5/UFJaxUIibMgiAg2aeZd1G47a2fxb1/2Y8jxZPglag7jvlj4IBU6F7VJOA==", "signatures": [{"sig": "MEQCIDlLRzhsb3OlBkNhcAVN5p6hcOmL2s151XQGMW+MkgPsAiA3T6L7xMdXMbQja7wD1SoZPdZPlcIlMzsBeSsRFbyVXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "node-lint lib test", "test": "mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "latest", "should": "latest"}}, "0.1.0": {"name": "cors", "version": "0.1.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.1.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "20bb4acc3021029bda8a21c8349c48bb29d2d9f2", "tarball": "https://registry.npmjs.org/cors/-/cors-0.1.0.tgz", "integrity": "sha512-yaaZEPubze7fIwpAS+Qx73F8p7Ij0OpEvRSvFd5EEyVKP6yFdtus9C49g0O36A8zBUChIz4uY6GWjTLVZB3Pfw==", "signatures": [{"sig": "MEQCIEye/T9a1hFzdfRwLIZEaRylTdf/TiIt/JoGb8mp+vlHAiAFgjQnQmi2UPjTU+gjVTKc+TC1KbZI1a/KJrTLym7UOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "0.1.1": {"name": "cors", "version": "0.1.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@0.1.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "917cf65c8cff90971f2e41443d808253d454f7ea", "tarball": "https://registry.npmjs.org/cors/-/cors-0.1.1.tgz", "integrity": "sha512-MQRv3vhcgnlmEBwfb/6VOI+cor/d3IcXeSL80kIdobWQlqIIz+/rRVZVo41+5LeRU35s45aQQrsUlxyD8CA4Fg==", "signatures": [{"sig": "MEUCIFXtqgzR2U37MaYeIyiImWPJ48CJw0JhhvRIaNnxFl3eAiEA1eVOlaaiPlr7sT/kITAjBhQtVMxV9X/NxTUSSUJ3tas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "1.0.0": {"name": "cors", "version": "1.0.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@1.0.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "a1e478967477209d204340ab1991aa4e1ac72a99", "tarball": "https://registry.npmjs.org/cors/-/cors-1.0.0.tgz", "integrity": "sha512-ziL2NKl6A2Dz6Q94Nmm0Wt/xfLepQ+sriiILzORG002nHbRluDrkfnhLIY0YBwRkXobX5nC1cdmshzgPcvfU5Q==", "signatures": [{"sig": "MEYCIQD1BlEsxuLBPOG6CmHlu75acgqGPLTBAbqXjiqjxYjLgQIhALd8RBTh65K+xY/jwaQUdbROXvDlHWrlIVegAMGVK8cJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "1.0.1": {"name": "cors", "version": "1.0.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@1.0.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "d8b18bac9460aefd5bfbb06f5d8684f7206f6d9d", "tarball": "https://registry.npmjs.org/cors/-/cors-1.0.1.tgz", "integrity": "sha512-ZeuCU/xztHR2OfehqFP8B+l/1fgVu+qtBeXg5T8iQSUKHObmCpjcAXw4Z7U8lW+pv8cHwT8xHDIYJRoaFaK2Hw==", "signatures": [{"sig": "MEQCIBDZ1c4ZVkw7ymhtfp4A0gMsGkHu10lQ2Md8MNDjPs/7AiAJW6LbXCw+kG9QgdoC/A9GE6ipQ4cgOrOOggWOOJPcnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "2.0.0": {"name": "cors", "version": "2.0.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.0.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/troygoode/node-cors/issues"}, "dist": {"shasum": "426a7060f35347ae3235b594a42bc09d0f766d54", "tarball": "https://registry.npmjs.org/cors/-/cors-2.0.0.tgz", "integrity": "sha512-8CpE/Dw+A4pZuOb3awDKnrmyj8ZBVYVd2Y111YIxGG0bbPBkNtRlvDRvjt5CmnqRVJ1N/bj6/ZZtXU3RBN2Rpg==", "signatures": [{"sig": "MEUCIQDDCFYBLw+qok3dwf8b+BF7OSoxLDExwQG2sgLQBL3hMwIgCj5tJlWem2f2AkinU8A5BNE1dNhhbmqFlBaJncBmVgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "2.1.0": {"name": "cors", "version": "2.1.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.1.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "7f8404a5762456021884e99da31d39b7e90bb994", "tarball": "https://registry.npmjs.org/cors/-/cors-2.1.0.tgz", "integrity": "sha512-a3sHBnRcr9bVVLFxxXiggJUFrIRXnC/RqyH8l3F/ix6LW3AqZEzcaTyCKmxGG41QO1QTYu42bWYiTaI0Jg73Dg==", "signatures": [{"sig": "MEQCIGLMP0EMlXAKhvEcIWppMIlcQLTNF33EKZ3Fvxa4WRroAiBOih+CrYxIImEnWDXb5JGGvgSDJR6nCiT8qDYHcUPN2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "2.1.1": {"name": "cors", "version": "2.1.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.1.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "affc232dd9f87c27fa8b03cc1aeac85284441786", "tarball": "https://registry.npmjs.org/cors/-/cors-2.1.1.tgz", "integrity": "sha512-tmLYKZEsfe+Z5ps0CKNZfrwyYYRrA7A4QC4gDSYBz1oX6z5hX7jeM1oaRGzkpYq0Bmde7oHTYodmaIqGrAwPOQ==", "signatures": [{"sig": "MEUCIQCQSiykG0vtW0i4SoJlRQ2zwxDn+0mnmDdn8dq2FmsstQIgNA9+wSVADI+XVNNZ0UyXAlWELsZGcGrMLhcNKVoorgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "2.2.0": {"name": "cors", "version": "2.2.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.2.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "d36a4c32917a7831cb652191f702929bbd61b6ad", "tarball": "https://registry.npmjs.org/cors/-/cors-2.2.0.tgz", "integrity": "sha512-w4DCLUil2p9eDVhWVrDoEVJFQCNBm9I8GRp3xMYTdLV/EgZOqduxSSTeyR8MSJ2OLXapwYYr1khCmuDNsemj5A==", "signatures": [{"sig": "MEYCIQDuuzT7DYa9Kba6aYiRs3ld4+x8RrtaclqTahAZSl1O2QIhALGUdBCkBw7RAyG2fYmiCtCE5CCjMVhZ2nIK3ucXYr2V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "*", "mocha": "*", "should": "*", "express": "*", "supertest": "*"}}, "2.3.0": {"name": "cors", "version": "2.3.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.3.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "d7bfe9598bf427d54983cd7fab59629099bb0f17", "tarball": "https://registry.npmjs.org/cors/-/cors-2.3.0.tgz", "integrity": "sha512-jUtYSMYGFmiiXQrLuNCD/9a2sjEH0VTq2paUDgut0Nl7ocLx6RBwsTlV9fjt2c7YHxKV+78QqrhvdMMMF7Ti2A==", "signatures": [{"sig": "MEQCIB4j6ZMuu9nzASFwF7xTCgS7fQCK2yu5Az0LkHdleCk/AiAIWeUavdyu/7rfuHGRUoa2CiOa9dmqaOsggTmfGZql6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^3", "supertest": "^0.12.0"}}, "2.3.1": {"name": "cors", "version": "2.3.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.3.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "d223fb3763eb9475a4d05406d30e7e0344f5d00d", "tarball": "https://registry.npmjs.org/cors/-/cors-2.3.1.tgz", "integrity": "sha512-WnBAYYiTTyT+9mkmwtdIBfx/+UNrVM/PmMG8l0k7TyR9+FHu7RvPuvwbtKizaiaDlWTavazATHjPDUXhQhm+wA==", "signatures": [{"sig": "MEQCIGB3ZcywNB8AlRm9UFU+r5CyRGuaDcxA4HXHuQrPeGrTAiATZ0/bBnGKX6aiWC/4JxNOQULozGkXnoQrnhLKulW8LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "engines": {"npm": "^1.3", "node": ">=0.8.0"}, "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^3", "supertest": "^0.12.0"}}, "2.3.2": {"name": "cors", "version": "2.3.2", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.3.2", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "d8e4cbf8dabbc04e4b9956fd0f708c63ccf20cc8", "tarball": "https://registry.npmjs.org/cors/-/cors-2.3.2.tgz", "integrity": "sha512-odlEDqFm+0KrIY9aycBQKJ5DtVjHri/gadwRT9NNW3CU/qOAjIL/T/rHJ+qsGZjfOxfGLqfdCPOx5PjjztYqcw==", "signatures": [{"sig": "MEUCIAW8Wk+VZEULuESAnHwWu+CSWdAHxvPIMGlmDrHyRZLyAiEA6XsUP+jfLPS2ZwiunY4GBPOig6bQxSTeKEYUcKmz2Z0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "d8e4cbf8dabbc04e4b9956fd0f708c63ccf20cc8", "engines": {"npm": "^1.4", "node": ">=0.10.0"}, "gitHead": "c78ed4285d59cf7a7835e33326eb63707bc1705a", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0"}}, "2.4.0": {"name": "cors", "version": "2.4.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.4.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "4190112c6a79c5d128c193557481b51593bb77a7", "tarball": "https://registry.npmjs.org/cors/-/cors-2.4.0.tgz", "integrity": "sha512-5P21nCRJ4HM59MwUqvmalnG5XyPO7rLPATJrZ8vE32hxOEkR9OioU0tv2X8IRFPQCuM+ZGamyV9/TQ2O1n3plA==", "signatures": [{"sig": "MEQCIEb3/wPOiJ2HB+6RguF49LJ0Bq3kNq45jfUeUNFT4sL/AiBh/siO1ulKegL5IX8A5vJ4Rkr1V3geAanum0z/PJCdBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "4190112c6a79c5d128c193557481b51593bb77a7", "engines": {"npm": "^1.4", "node": ">=0.10.0"}, "gitHead": "9af79275f43e88994be47e8d7f5c965b11e87edb", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3"}}, "2.4.1": {"name": "cors", "version": "2.4.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.4.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "b09c049a7ac8d5a8f757d9a4162b6dab51621581", "tarball": "https://registry.npmjs.org/cors/-/cors-2.4.1.tgz", "integrity": "sha512-FHxWgCy+mCYk+qYD+Ka30iIzC8XaaonFunZAiDpMI8UtzI0/sob81LW19Y4JwY1LS7dl5fNH84RZgzruk/3GHA==", "signatures": [{"sig": "MEQCIC1IjFN46kUxucAwi67y5w6WRUPblK1es80sZekj8AtDAiBJtV0/G2qyjjpKIJhqzfnSHdowKZPQ+XV/yXJt0ba8bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "b09c049a7ac8d5a8f757d9a4162b6dab51621581", "engines": {"npm": "^1.4", "node": ">=0.10.0"}, "gitHead": "893cf2cf93a0a6825944109552a35df827ea2106", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3"}}, "2.4.2": {"name": "cors", "version": "2.4.2", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.4.2", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "2d0fbd504855a0a76a469b3baf5bff5bb80fa331", "tarball": "https://registry.npmjs.org/cors/-/cors-2.4.2.tgz", "integrity": "sha512-1uM9X29E6xDIJnBrmQPbzq00BhifHnjMKR+eRfS0ZHJcRk03aAG4j5zYvQq51nyy0wNxBnOoEfKspgwO58lFaw==", "signatures": [{"sig": "MEQCIBpIgnZcb/MzMJsls185PHGy7wMpOm8SSFRyTCmu8xgLAiBFwpA4y+6pSIhet5bZ/aDyB4oh4ukfe8ulZOcSWHUhbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "2d0fbd504855a0a76a469b3baf5bff5bb80fa331", "engines": {"node": ">=0.10.0"}, "gitHead": "210446e8882dd599e2f9526d048b82018f77b48b", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.5.0": {"name": "cors", "version": "2.5.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.5.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "78dfe532496fec6c3e37d08dbe7cf1d3f7ad2586", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.0.tgz", "integrity": "sha512-qnWQAFBLnHKmMuVVM4h+eaAFEUHWI0OsvzTb+2TRh8MHUeerTyxkRogCll2WyFN88ETsTL0wfMVHORye/iARxg==", "signatures": [{"sig": "MEYCIQDCLJzdU+qgQ2rKz4yxbcmkvOAgk4uXFC9UwoE+mnd6cQIhANj3W7U5Du4aY4kSGslCo1qoWb+9TmQ2Flt72R73EdaZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "78dfe532496fec6c3e37d08dbe7cf1d3f7ad2586", "engines": {"node": ">=0.10.0"}, "gitHead": "0aca42ec76993c612abf38cae27d096172c66fe5", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12.0", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.5.1": {"name": "cors", "version": "2.5.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.5.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "0fe1985195ce355c775bbd36e1cd397d5e1ce069", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.1.tgz", "integrity": "sha512-yXXpIYjplnGcVz/5gzvJm6PzDeTxomt57ckW6doR78nNQ/5TYsB8EUQs4knOQrvFqe2W6P+8tkUiIjPEvN8ZOQ==", "signatures": [{"sig": "MEQCICjIuSZ91KgDjKK2qaFZAzU1f/e1jFIFgiOjQOnMKD1rAiBT9dTAB2ckURa4iKpH4iPrW4jy0zIEQzUGhzGaKJsCaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "0fe1985195ce355c775bbd36e1cd397d5e1ce069", "engines": {"node": ">=0.10.0"}, "gitHead": "165f35e8a289e36caefc92131e8f874560bd8ce6", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.5.2": {"name": "cors", "version": "2.5.2", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.5.2", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "00d5c0d1ced95001c998fa66b52c4ef931e6f8b7", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.2.tgz", "integrity": "sha512-IuoQaP+q8zND//sld7XRGbCl31Qo3/a1X9NbOFHMnEONH3jHfDe8WmrIKOuQkS4oqFx+hURLwmYxYEA3OuvY6A==", "signatures": [{"sig": "MEUCIQD7bd4pqBxAN9KnIqi8GdbKXUeXXy4GIQJgvGGTEmx5fAIgGkR6Up+07xwkXq5KlNMP8cobRjavf70ipgOdxaggMsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "00d5c0d1ced95001c998fa66b52c4ef931e6f8b7", "engines": {"node": ">=0.10.0"}, "gitHead": "fee800f0dc13a1df5b696035f09c43b8c714371b", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.0.2", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.5.3": {"name": "cors", "version": "2.5.3", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.5.3", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "0d70a211ec3b6cc9824e6cdc299c0630ef69c392", "tarball": "https://registry.npmjs.org/cors/-/cors-2.5.3.tgz", "integrity": "sha512-QxDg4+bMIpDffByfCNUFlVmIj8anyoaPnvH54axXKVKKIj1DnJS9FgDurDZKSqzCNRC+QbkxPu1Ysmnhy7VbIg==", "signatures": [{"sig": "MEQCIGVCtrV9R39+KwrXxKNYG6LVnSBo1MHOeTlmNpwDlWb8AiADcT7noVrZz3cJdEu3nxxhGgMBMjFPvWWmPP3MAal9dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "0d70a211ec3b6cc9824e6cdc299c0630ef69c392", "engines": {"node": ">=0.10.0"}, "gitHead": "9959d2e4301bfb76e150c1c65e5ecd28924269fb", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.1.10", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.6.0": {"name": "cors", "version": "2.6.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "cors@2.6.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "ed0ef328fca50e13e902f1bb7063fc61d89f2974", "tarball": "https://registry.npmjs.org/cors/-/cors-2.6.0.tgz", "integrity": "sha512-Ma<PERSON>r6gtz7l92KrLw475emM6/7evSu/nZoec7ZSVCJjEese6WxpNU6KAFjqYvR7VqKoMwVkE+CDqZLLMuvfWvhQ==", "signatures": [{"sig": "MEUCIDXFtBEzwZy2yJ4Q8tbjYoUNr2bJIyP8a/5kyfhz/yUaAiEA9ENpMpYvcSgQsDiJ/d9ZndwOomCNWxpn1nOtyXop7Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "ed0ef328fca50e13e902f1bb7063fc61d89f2974", "engines": {"node": ">=0.10.0"}, "gitHead": "e56aad29cbe9a4e06ef1c5c2be17e6a4f497a90a", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "licenses": [{"url": "http://www.opensource.org/licenses/mit-license.php", "type": "MIT"}], "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.6.1": {"name": "cors", "version": "2.6.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.6.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "5d27270a5fb8ae28fa2a7aa38613f7ec980792e9", "tarball": "https://registry.npmjs.org/cors/-/cors-2.6.1.tgz", "integrity": "sha512-ydxE9Jmm/J24MVH+qnlVbDBPLlM1BInOFV1OOpXzT+7MuKuE734ANAsaEO4KeKMd28wjhN28nrxs3Vd0O1l0kw==", "signatures": [{"sig": "MEYCIQC7ADDxnlMliTp4PfDk0UW94bfSVu6H/c7pVMEgPJBZNAIhAOp63MJVw3iT8iqTj9nOdBxuJuL+uQ87XD9xQm4LYKrD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "5d27270a5fb8ae28fa2a7aa38613f7ec980792e9", "engines": {"node": ">=0.10.0"}, "gitHead": "42cae8e6ddcf0640d74483eb756f5b59333c2a50", "scripts": {"lint": "./node_modules/lint/bin/node-lint lib test", "test": "./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"vary": "^1"}, "devDependencies": {"lint": "^1.1.2", "mocha": "^1.18.2", "should": "^3.3.1", "express": "^4", "supertest": "^0.12", "body-parser": "^1.4.3", "basic-auth-connect": "^1"}}, "2.7.0": {"name": "cors", "version": "2.7.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.7.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/troygoode/node-cors/", "bugs": {"url": "https://github.com/troygoode/node-cors/issues"}, "dist": {"shasum": "8e21190caccab56d0651de4a89208ea977c6a377", "tarball": "https://registry.npmjs.org/cors/-/cors-2.7.0.tgz", "integrity": "sha512-BhZ1eFCRBm+DAvIV7//EGFAr7uL//khLcb/0Ff8F3jfxHXJTEr2TJ0aOI6/+hWA9gGTZx0s7pzfr2v7Kdh0YUw==", "signatures": [{"sig": "MEUCIQDxYgs+m4s1eKgY+wtP92Rav7seq2dCXo1S71aDhcGkYgIgVHSyB336ewItmaq5KEdwcbrPPl7VJKWeNhFxv+sxVKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "8e21190caccab56d0651de4a89208ea977c6a377", "engines": {"node": ">=0.10.0"}, "gitHead": "8ec22a9b86f71389264cd961f567e72cf7db83ca", "scripts": {"lint": "./node_modules/eslint/bin/eslint.js lib test", "test": "npm run lint && ./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/troygoode/node-cors.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}}, "2.7.1": {"name": "cors", "version": "2.7.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.7.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors/", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "3c2e50a58af9ef8c89bee21226b099be1f02739b", "tarball": "https://registry.npmjs.org/cors/-/cors-2.7.1.tgz", "integrity": "sha512-s8O0W7pnSUNauMz02MUIFjRCfM9Cq1q2Dgcz6Lg+zMMPK79RrWj6VggI3E1eKpKrcickW2MbItQF+Vg1ApNnOA==", "signatures": [{"sig": "MEQCIEOHPMRLLDQggRegPTVIq6EClKzwHDOuAoZRHvnZ20aDAiA4hTW2C7oosc32FAzBk6a3wt+5gE6JYxwMIhlzmPMA6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "3c2e50a58af9ef8c89bee21226b099be1f02739b", "engines": {"node": ">=0.10.0"}, "gitHead": "bef1a97c22a3f15cb23ab2d9cf8e7e3f7134e107", "scripts": {"lint": "./node_modules/eslint/bin/eslint.js lib test", "test": "npm run lint && ./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}}, "2.7.2": {"name": "cors", "version": "2.7.2", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.7.2", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors/", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "21385debfff24c223a10605b82311452999a1cbb", "tarball": "https://registry.npmjs.org/cors/-/cors-2.7.2.tgz", "integrity": "sha512-Y2arOFKVr8sp8CznsXEi/VaCNgCsXI5VbUNx574rmRw5BTzvt1v5znyTP/+RR33ENcSFgx5LIhW5UV35F1uSMA==", "signatures": [{"sig": "MEUCICLAHrwZfk0pxc9BTo2p9+DNEnLLrwj2psz4BO8GJ7g4AiEA+sWMDB2tYvXf0nkkPHVClNbETiqRenMfZVHxn8GZY2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "21385debfff24c223a10605b82311452999a1cbb", "engines": {"node": ">=0.10.0"}, "gitHead": "6568976a0c2948ba602d0f20205a5b33c6f2faea", "scripts": {"lint": "./node_modules/eslint/bin/eslint.js lib test", "test": "npm run lint && ./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors-2.7.2.tgz_1471975079663_0.5144260262604803", "host": "packages-12-west.internal.npmjs.com"}}, "2.8.0": {"name": "cors", "version": "2.8.0", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.8.0", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors/", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "6262888a49f9ce4c5d189d29e1d5710ab73e6a85", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.0.tgz", "integrity": "sha512-hBj2Hnsmtcy3buHGWETxMUuBLvSvoeSNoG54jxpVpiXGm08MwBIiydQTv4VQ3033vBz5U55so+o206K7YanXSw==", "signatures": [{"sig": "MEUCIDk9Ng/NHE0OHFwPcKSiaZOcvqdyt7+LI1O7Jqkr0gcsAiEA4M/Ezvf66ASllJ9W3axwcUg/epSN3DAKDC5CDWQSQwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "6262888a49f9ce4c5d189d29e1d5710ab73e6a85", "engines": {"node": ">=0.10.0"}, "gitHead": "5dae6d8caf405c8f7fb1a094f964c712f62d214e", "scripts": {"lint": "./node_modules/eslint/bin/eslint.js lib test", "test": "npm run lint && ./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.0.tgz_1471975881465_0.3300019665621221", "host": "packages-12-west.internal.npmjs.com"}}, "2.8.1": {"name": "cors", "version": "2.8.1", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.8.1", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors/", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "6181aa56abb45a2825be3304703747ae4e9d2383", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.1.tgz", "integrity": "sha512-s7rWgFmYGmV3YJZ2hBl6VqvIYgos1NFaoOyVKlx/mKYUGD0MU11Bw2KWKCt49O75mfFD/oUHHhKIXDS1CYqw8g==", "signatures": [{"sig": "MEUCIDxINA2JZ+W/uLJ5ojZOUmKEwsajpBckifyYNABW112+AiEA7PLxbKdzNd0x6k7Ls4g//+oPaYlZ5bXmv0LWOSNCN3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "6181aa56abb45a2825be3304703747ae4e9d2383", "engines": {"node": ">=0.10.0"}, "gitHead": "458804a9ebd71a205a22d41da60f4cc5502a7776", "scripts": {"lint": "./node_modules/eslint/bin/eslint.js lib test", "test": "npm run lint && ./node_modules/mocha/bin/mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "middleware for dynamically or statically enabling CORS in express/connect applications", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"vary": "^1"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.1.tgz_1473366971988_0.42439922760240734", "host": "packages-16-east.internal.npmjs.com"}}, "2.8.2": {"name": "cors", "version": "2.8.2", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.8.2", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors#readme", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "3a6c31f5a398c87394e31555b627ea3523839080", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.2.tgz", "integrity": "sha512-3YP/ZBGAAUKg5DKzd4sFlfcbhO3ynFANTWLBJ+UJHke4KmJTuGUcWhNIYpA84F2VeqcLzj3kwviUD2rzWz7lJw==", "signatures": [{"sig": "MEYCIQCeYrGdg0mxuJhG37sV74uipiGPku0MWyoLB2BRbd3nBwIhAIslsOkOx46IvsW9aq95UcIyuU6uaT7Mt10/vNzBQFhu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "3a6c31f5a398c87394e31555b627ea3523839080", "engines": {"node": ">=0.10.0"}, "gitHead": "2c4e64718f08f5e2e7a9462c02b70863a42b9a1b", "scripts": {"lint": "eslint lib test", "test": "npm run lint && istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js CORS middleware", "directories": {}, "_nodeVersion": "6.9.5", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "istanbul": "^0.4.5", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.2.tgz_1490749034978_0.576983995269984", "host": "packages-12-west.internal.npmjs.com"}}, "2.8.3": {"name": "cors", "version": "2.8.3", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.8.3", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors#readme", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "4cf78e1d23329a7496b2fc2225b77ca5bb5eb802", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.3.tgz", "integrity": "sha512-anT5RmfPJjvXdnFfu7Ft+V+5qOoCHaFSVDIRUAaj8NNQFv8gP0Ew0hxltS03M33A6OH0UxhLTv4s55Zco/R8qQ==", "signatures": [{"sig": "MEQCIC5LzRvaZ8hVki45r/oxQbJ62R1yyT3rN/jcosrK5bRHAiAzkUma9cg3EBw+Muy2SHkgzgfUidOQ3K+sjjTELuYbXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "4cf78e1d23329a7496b2fc2225b77ca5bb5eb802", "engines": {"node": ">=0.10.0"}, "gitHead": "90a7881d8bf9c273ca9183e3ca95f74ce9f5aee2", "scripts": {"lint": "eslint lib test", "test": "npm run lint && istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Node.js CORS middleware", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"mocha": "^2.2.5", "eslint": "^0.21.2", "should": "^6.0.3", "express": "^4.12.4", "istanbul": "^0.4.5", "supertest": "^1.0.1", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.3.tgz_1490842777775_0.44825206557288766", "host": "packages-12-west.internal.npmjs.com"}}, "2.8.4": {"name": "cors", "version": "2.8.4", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.8.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "troygoode", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors#readme", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "2bd381f2eb201020105cd50ea59da63090694686", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.4.tgz", "integrity": "sha512-ZEDmjGp8X+Slw7pFoWntR1aGcULLoVeKzcyTdR8dfP4LcVooO6oQ2n/vElN7L2RmoJ7mpJGGiseaDr3m3g85eg==", "signatures": [{"sig": "MEUCIBBYYZQwsUf+OvBYMSoESd/ElxMI5Mf9Ap8dLSG7u/cAAiEAh33uf3TvNL2eGk63sc7Ws05gII4bNTu3t0KsTTBtvw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/index.js", "_from": ".", "_shasum": "2bd381f2eb201020105cd50ea59da63090694686", "engines": {"node": ">=0.10.0"}, "gitHead": "c6ed038edc4a483096ded79ad9a0629e4ff79000", "scripts": {"lint": "eslint lib test", "test": "npm run lint && istanbul cover node_modules/mocha/bin/_mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Node.js CORS middleware", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"vary": "^1", "object-assign": "^4"}, "devDependencies": {"mocha": "3.4.2", "eslint": "^0.21.2", "should": "11.2.1", "express": "^4.12.4", "istanbul": "^0.4.5", "supertest": "3.0.0", "body-parser": "^1.12.4", "basic-auth-connect": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.4.tgz_1499914217936_0.6064511660952121", "host": "s3://npm-registry-packages"}}, "2.8.5": {"name": "cors", "version": "2.8.5", "keywords": ["cors", "express", "connect", "middleware"], "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "cors@2.8.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "troygoode", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/cors#readme", "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "dist": {"shasum": "eac11da51592dd86b9f06f6e7ac293b3df875d29", "tarball": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "fileCount": 6, "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "signatures": [{"sig": "MEUCIHzJBLZQzZrohsa0JrSIjzuf1IQnRkM4nZCLWNPVsjOOAiEAzBozqnbTfeHT7nvI/pTbAPTSu8OkOMDBcFGVUeab0A8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb313dCRA9TVsSAnZWagAApVgP/30ky26N6zFR3CDubvgd\nvrQRFNnbrB3n80hIoyBOVnGARmeO2LX+Cx2BvuQc10omCjLWDO8pS8FQCasg\nV0rGsbMId632LvJ598RnfyWw2qwwP2jBKWqtiAa+sg2nizqvvVnXlp/OapbH\nvSFncO9EFR8f5f0j61/Ue3SfFlak5d1RNOrCYprNHL6gzNS54IPzQkYix9H9\n9yih7kL82nijOPAe0wSRQGF++lW3Cs4oM3h+U/cT+dPEU+eB6JWKc92uXtkt\n7bd68ZWeUDyUA86SvxZfsOqiTakojR248wUPmC91X9wXFRRbcGP6U7HfCxSh\n7YihwCW06LnFDsaZzMZHNUfnrilsehT80rfQBGCG7cGll3c0O53ZT7otJT9g\nQJlHyNGhqzgwku1/WPMPL7UciFGy9HJsleXAYt+iUzJiMWBdprAmxVP/yJkl\nFvlsiqvz/R247da8ONXJqg5xis7+nIlB31ccPqmchbSIRhfDYVEpFUnn0des\nmtoBpqW+xuRxR7Atko65CyizTc/8ZM0mbkYeQdrSnbt34K/BY/bbMwy/bJhX\n7wb8xWmRPZEi7LTRHDqJkBj2+kv1FzCkZZk1srhy1uFIX4rnf5KJq0aj7BK7\nLfLk/eekTQETfvi9ZefYuCgBcE7YVFZ9faYAXtx6J08OShR17NsPDnYxVPpc\nxCzl\r\n=ec0y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">= 0.10"}, "gitHead": "9158a8686d64bf567440d030873378c429ad60b0", "scripts": {"lint": "eslint lib test", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --require test/support/env"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/cors.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Node.js CORS middleware", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"vary": "^1", "object-assign": "^4"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "13.1.0", "after": "0.8.2", "mocha": "5.2.0", "eslint": "2.13.1", "express": "4.16.3", "supertest": "3.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.5_1541365213146_0.114361639846984", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-01-31T04:13:40.565Z", "modified": "2025-04-09T02:30:00.440Z", "0.0.1": "2013-01-31T04:13:41.715Z", "0.0.2": "2013-01-31T04:46:07.407Z", "0.0.3": "2013-02-01T02:36:58.903Z", "0.0.4": "2013-02-08T04:37:24.660Z", "0.0.5": "2013-03-12T18:08:00.657Z", "0.1.0": "2013-04-28T16:24:21.826Z", "0.1.1": "2013-04-28T16:28:06.170Z", "1.0.0": "2013-04-28T18:35:01.910Z", "1.0.1": "2013-04-29T14:26:35.799Z", "2.0.0": "2013-10-10T01:45:20.429Z", "2.1.0": "2013-10-14T18:18:05.282Z", "2.1.1": "2013-12-11T06:03:03.701Z", "2.2.0": "2014-03-04T21:49:38.672Z", "2.3.0": "2014-05-06T22:32:41.785Z", "2.3.1": "2014-05-12T23:16:52.511Z", "2.3.2": "2014-07-05T18:26:22.355Z", "2.4.0": "2014-07-05T18:48:29.359Z", "2.4.1": "2014-07-06T00:24:55.054Z", "2.4.2": "2014-09-13T22:04:32.516Z", "2.5.0": "2014-10-23T21:37:40.967Z", "2.5.1": "2014-11-07T19:05:52.211Z", "2.5.2": "2014-11-10T05:50:27.768Z", "2.5.3": "2015-01-20T21:52:22.113Z", "2.6.0": "2015-04-27T18:49:54.382Z", "2.6.1": "2015-05-28T08:30:06.995Z", "2.7.0": "2015-05-28T23:10:48.484Z", "2.7.1": "2015-05-28T23:17:09.344Z", "2.7.2": "2016-08-23T17:58:01.780Z", "2.8.0": "2016-08-23T18:11:23.068Z", "2.8.1": "2016-09-08T20:36:14.237Z", "2.8.2": "2017-03-29T00:57:15.205Z", "2.8.3": "2017-03-30T02:59:39.822Z", "2.8.4": "2017-07-13T02:50:19.018Z", "2.8.5": "2018-11-04T21:00:13.277Z"}, "bugs": {"url": "https://github.com/expressjs/cors/issues"}, "author": {"url": "https://github.com/troygoode/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/expressjs/cors#readme", "keywords": ["cors", "express", "connect", "middleware"], "repository": {"url": "git+https://github.com/expressjs/cors.git", "type": "git"}, "description": "Node.js CORS middleware", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "troygoode", "email": "<EMAIL>"}], "readme": "# cors\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Build Status][travis-image]][travis-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nCORS is a node.js package for providing a [Connect](http://www.senchalabs.org/connect/)/[Express](http://expressjs.com/) middleware that can be used to enable [CORS](http://en.wikipedia.org/wiki/Cross-origin_resource_sharing) with various options.\n\n**[Follow me (@troygoode) on Twitter!](https://twitter.com/intent/user?screen_name=troygoode)**\n\n* [Installation](#installation)\n* [Usage](#usage)\n  * [Simple Usage](#simple-usage-enable-all-cors-requests)\n  * [Enable CORS for a Single Route](#enable-cors-for-a-single-route)\n  * [Configuring CORS](#configuring-cors)\n  * [Configuring CORS Asynchronously](#configuring-cors-asynchronously)\n  * [Enabling CORS Pre-Flight](#enabling-cors-pre-flight)\n* [Configuration Options](#configuration-options)\n* [Demo](#demo)\n* [License](#license)\n* [Author](#author)\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install cors\n```\n\n## Usage\n\n### Simple Usage (Enable *All* CORS Requests)\n\n```javascript\nvar express = require('express')\nvar cors = require('cors')\nvar app = express()\n\napp.use(cors())\n\napp.get('/products/:id', function (req, res, next) {\n  res.json({msg: 'This is CORS-enabled for all origins!'})\n})\n\napp.listen(80, function () {\n  console.log('CORS-enabled web server listening on port 80')\n})\n```\n\n### Enable CORS for a Single Route\n\n```javascript\nvar express = require('express')\nvar cors = require('cors')\nvar app = express()\n\napp.get('/products/:id', cors(), function (req, res, next) {\n  res.json({msg: 'This is CORS-enabled for a Single Route'})\n})\n\napp.listen(80, function () {\n  console.log('CORS-enabled web server listening on port 80')\n})\n```\n\n### Configuring CORS\n\n```javascript\nvar express = require('express')\nvar cors = require('cors')\nvar app = express()\n\nvar corsOptions = {\n  origin: 'http://example.com',\n  optionsSuccessStatus: 200 // some legacy browsers (IE11, various SmartTVs) choke on 204\n}\n\napp.get('/products/:id', cors(corsOptions), function (req, res, next) {\n  res.json({msg: 'This is CORS-enabled for only example.com.'})\n})\n\napp.listen(80, function () {\n  console.log('CORS-enabled web server listening on port 80')\n})\n```\n\n### Configuring CORS w/ Dynamic Origin\n\n```javascript\nvar express = require('express')\nvar cors = require('cors')\nvar app = express()\n\nvar whitelist = ['http://example1.com', 'http://example2.com']\nvar corsOptions = {\n  origin: function (origin, callback) {\n    if (whitelist.indexOf(origin) !== -1) {\n      callback(null, true)\n    } else {\n      callback(new Error('Not allowed by CORS'))\n    }\n  }\n}\n\napp.get('/products/:id', cors(corsOptions), function (req, res, next) {\n  res.json({msg: 'This is CORS-enabled for a whitelisted domain.'})\n})\n\napp.listen(80, function () {\n  console.log('CORS-enabled web server listening on port 80')\n})\n```\n\nIf you do not want to block REST tools or server-to-server requests,\nadd a `!origin` check in the origin function like so:\n\n```javascript\nvar corsOptions = {\n  origin: function (origin, callback) {\n    if (whitelist.indexOf(origin) !== -1 || !origin) {\n      callback(null, true)\n    } else {\n      callback(new Error('Not allowed by CORS'))\n    }\n  }\n}\n```\n\n### Enabling CORS Pre-Flight\n\nCertain CORS requests are considered 'complex' and require an initial\n`OPTIONS` request (called the \"pre-flight request\"). An example of a\n'complex' CORS request is one that uses an HTTP verb other than\nGET/HEAD/POST (such as DELETE) or that uses custom headers. To enable\npre-flighting, you must add a new OPTIONS handler for the route you want\nto support:\n\n```javascript\nvar express = require('express')\nvar cors = require('cors')\nvar app = express()\n\napp.options('/products/:id', cors()) // enable pre-flight request for DELETE request\napp.del('/products/:id', cors(), function (req, res, next) {\n  res.json({msg: 'This is CORS-enabled for all origins!'})\n})\n\napp.listen(80, function () {\n  console.log('CORS-enabled web server listening on port 80')\n})\n```\n\nYou can also enable pre-flight across-the-board like so:\n\n```javascript\napp.options('*', cors()) // include before other routes\n```\n\n### Configuring CORS Asynchronously\n\n```javascript\nvar express = require('express')\nvar cors = require('cors')\nvar app = express()\n\nvar whitelist = ['http://example1.com', 'http://example2.com']\nvar corsOptionsDelegate = function (req, callback) {\n  var corsOptions;\n  if (whitelist.indexOf(req.header('Origin')) !== -1) {\n    corsOptions = { origin: true } // reflect (enable) the requested origin in the CORS response\n  } else {\n    corsOptions = { origin: false } // disable CORS for this request\n  }\n  callback(null, corsOptions) // callback expects two parameters: error and options\n}\n\napp.get('/products/:id', cors(corsOptionsDelegate), function (req, res, next) {\n  res.json({msg: 'This is CORS-enabled for a whitelisted domain.'})\n})\n\napp.listen(80, function () {\n  console.log('CORS-enabled web server listening on port 80')\n})\n```\n\n## Configuration Options\n\n* `origin`: Configures the **Access-Control-Allow-Origin** CORS header. Possible values:\n  - `Boolean` - set `origin` to `true` to reflect the [request origin](http://tools.ietf.org/html/draft-abarth-origin-09), as defined by `req.header('Origin')`, or set it to `false` to disable CORS.\n  - `String` - set `origin` to a specific origin. For example if you set it to `\"http://example.com\"` only requests from \"http://example.com\" will be allowed.\n  - `RegExp` - set `origin` to a regular expression pattern which will be used to test the request origin. If it's a match, the request origin will be reflected. For example the pattern `/example\\.com$/` will reflect any request that is coming from an origin ending with \"example.com\".\n  - `Array` - set `origin` to an array of valid origins. Each origin can be a `String` or a `RegExp`. For example `[\"http://example1.com\", /\\.example2\\.com$/]` will accept any request from \"http://example1.com\" or from a subdomain of \"example2.com\".\n  - `Function` - set `origin` to a function implementing some custom logic. The function takes the request origin as the first parameter and a callback (which expects the signature `err [object], allow [bool]`) as the second.\n* `methods`: Configures the **Access-Control-Allow-Methods** CORS header. Expects a comma-delimited string (ex: 'GET,PUT,POST') or an array (ex: `['GET', 'PUT', 'POST']`).\n* `allowedHeaders`: Configures the **Access-Control-Allow-Headers** CORS header. Expects a comma-delimited string (ex: 'Content-Type,Authorization') or an array (ex: `['Content-Type', 'Authorization']`). If not specified, defaults to reflecting the headers specified in the request's **Access-Control-Request-Headers** header.\n* `exposedHeaders`: Configures the **Access-Control-Expose-Headers** CORS header. Expects a comma-delimited string (ex: 'Content-Range,X-Content-Range') or an array (ex: `['Content-Range', 'X-Content-Range']`). If not specified, no custom headers are exposed.\n* `credentials`: Configures the **Access-Control-Allow-Credentials** CORS header. Set to `true` to pass the header, otherwise it is omitted.\n* `maxAge`: Configures the **Access-Control-Max-Age** CORS header. Set to an integer to pass the header, otherwise it is omitted.\n* `preflightContinue`: Pass the CORS preflight response to the next handler.\n* `optionsSuccessStatus`: Provides a status code to use for successful `OPTIONS` requests, since some legacy browsers (IE11, various SmartTVs) choke on `204`.\n\nThe default configuration is the equivalent of:\n\n```json\n{\n  \"origin\": \"*\",\n  \"methods\": \"GET,HEAD,PUT,PATCH,POST,DELETE\",\n  \"preflightContinue\": false,\n  \"optionsSuccessStatus\": 204\n}\n```\n\nFor details on the effect of each CORS header, read [this](http://www.html5rocks.com/en/tutorials/cors/) article on HTML5 Rocks.\n\n## Demo\n\nA demo that illustrates CORS working (and not working) using jQuery is available here: [http://node-cors-client.herokuapp.com/](http://node-cors-client.herokuapp.com/)\n\nCode for that demo can be found here:\n\n* Client: [https://github.com/TroyGoode/node-cors-client](https://github.com/TroyGoode/node-cors-client)\n* Server: [https://github.com/TroyGoode/node-cors-server](https://github.com/TroyGoode/node-cors-server)\n\n## License\n\n[MIT License](http://www.opensource.org/licenses/mit-license.php)\n\n## Author\n\n[Troy Goode](https://github.com/TroyGoode) ([<EMAIL>](mailto:<EMAIL>))\n\n[coveralls-image]: https://img.shields.io/coveralls/expressjs/cors/master.svg\n[coveralls-url]: https://coveralls.io/r/expressjs/cors?branch=master\n[downloads-image]: https://img.shields.io/npm/dm/cors.svg\n[downloads-url]: https://npmjs.org/package/cors\n[npm-image]: https://img.shields.io/npm/v/cors.svg\n[npm-url]: https://npmjs.org/package/cors\n[travis-image]: https://img.shields.io/travis/expressjs/cors/master.svg\n[travis-url]: https://travis-ci.org/expressjs/cors\n", "readmeFilename": "README.md", "users": {"akey": true, "dbck": true, "j3kz": true, "jket": true, "leny": true, "nazy": true, "usex": true, "vwal": true, "ysk8": true, "zvit": true, "abdul": true, "andyd": true, "benel": true, "bengi": true, "brend": true, "dabin": true, "dtoki": true, "mmork": true, "muroc": true, "panlw": true, "samar": true, "sidkb": true, "tdevm": true, "travm": true, "xyyjk": true, "yvesm": true, "456wyc": true, "ackhub": true, "alvajc": true, "andysw": true, "axiang": true, "bpatel": true, "bvaccc": true, "craigw": true, "freech": true, "hvo986": true, "igasho": true, "iliyat": true, "isayme": true, "jokecr": true, "maddas": true, "merkjs": true, "mikend": true, "mrbgit": true, "nanook": true, "nauhil": true, "nuwaio": true, "owillo": true, "quafoo": true, "rahman": true, "satoru": true, "snarky": true, "takez0": true, "webbot": true, "wheelo": true, "yb1997": true, "yong_a": true, "zhqgit": true, "ab.moon": true, "abhutch": true, "asahiro": true, "asaupup": true, "ash0080": true, "astesio": true, "ayoungh": true, "dabielf": true, "delacap": true, "ezeikel": true, "flyslow": true, "geekwen": true, "ghe1219": true, "gokulnk": true, "hckhanh": true, "itonyyo": true, "jaguarj": true, "jkabore": true, "jyounce": true, "mactive": true, "nunogee": true, "ostgals": true, "sejoker": true, "shivayl": true, "vboctor": true, "vivekrp": true, "xfloops": true, "yanghcc": true, "ahvonenj": true, "akinhwan": true, "alicebox": true, "alincode": true, "chaseshu": true, "dburdese": true, "deerflow": true, "elrolito": true, "elussich": true, "enanchen": true, "fabioper": true, "faeliaso": true, "faraoman": true, "fredcorn": true, "hektve87": true, "imappbox": true, "jmsherry": true, "klombomb": true, "kogakure": true, "kulyk404": true, "leonzhao": true, "lyaotian": true, "meggesje": true, "pddivine": true, "pdilyard": true, "pr-anoop": true, "psmorrow": true, "robba.jt": true, "robermac": true, "rochejul": true, "shaddyhm": true, "slowmove": true, "sofrente": true, "softwind": true, "stephn_r": true, "szarapka": true, "tcarlsen": true, "tmurngon": true, "voxpelli": true, "wandyezj": true, "wkaifang": true, "zalithka": true, "abuelwafa": true, "akatechis": true, "alexxnica": true, "antixrist": true, "bigbird92": true, "chunxchun": true, "derycktse": true, "devalphac": true, "devonning": true, "dylanh724": true, "eduarte78": true, "edwardxyt": true, "firerishi": true, "fleischer": true, "flockonus": true, "gabestevy": true, "gauthierm": true, "gochomugo": true, "heartnett": true, "hewenxuan": true, "hoverbaum": true, "igorissen": true, "jamiechoi": true, "joseph320": true, "jpshankle": true, "keanodejs": true, "kkk123321": true, "largepuma": true, "larrychen": true, "marcosc90": true, "mauricedb": true, "maxwelldu": true, "milfromoz": true, "morphesus": true, "myjustify": true, "npmmurali": true, "seachange": true, "sqrtthree": true, "sternelee": true, "troygoode": true, "undertuga": true, "acjohnso25": true, "alanerzhao": true, "alin.alexa": true, "andriecool": true, "antoine129": true, "antoniordo": true, "ashish.npm": true, "avivharuzi": true, "blind__man": true, "cfleschhut": true, "chirag8642": true, "coderaiser": true, "dccunni171": true, "dh19911021": true, "evdokimovm": true, "f124275809": true, "guzhongren": true, "jussipekka": true, "justinshea": true, "krostyslav": true, "kuzmicheff": true, "langri-sha": true, "maniktyagi": true, "midascreed": true, "morogasper": true, "neomorphic": true, "nickleefly": true, "qqqppp9998": true, "rocket0191": true, "rohitchris": true, "sammok2003": true, "sanketss84": true, "sayrilamar": true, "shadowlong": true, "tarkeshwar": true, "windupdurb": true, "zhanghaili": true, "adityathebe": true, "ahmed-dinar": true, "albertofdzm": true, "ambition101": true, "amirmehmood": true, "cbetancourt": true, "coolhanddev": true, "devdebonair": true, "he313572052": true, "hyungdookil": true, "jamesbedont": true, "karlbateman": true, "karnavpargi": true, "kobleistvan": true, "malloryerik": true, "marlongrape": true, "mtdalpizzol": true, "orange29233": true, "sammyteahan": true, "thangakumar": true, "themadjoker": true, "tobyforever": true, "totolicious": true, "vparaskevas": true, "wangnan0610": true, "xiangpaopao": true, "abhijitkalta": true, "adrian110288": true, "annesrinivas": true, "derickdsouza": true, "dpjayasekara": true, "grantcarthew": true, "johnny.young": true, "martinspinks": true, "mobeicaoyuan": true, "natterstefan": true, "rajivmehtajs": true, "rethinkflash": true, "shaomingquan": true, "superchenney": true, "tobitobitobi": true, "withinthefog": true, "chrisjordanme": true, "everywhere.js": true, "gzg1500521074": true, "hibrahimsafak": true, "jasonwang1888": true, "jeremygaither": true, "josephdavisco": true, "markthethomas": true, "matiasherranz": true, "serge-nikitin": true, "suprememoocow": true, "armanghazaryan": true, "gggauravgandhi": true, "jakub.knejzlik": true, "julianomontini": true, "karzanosman984": true, "maycon_ribeiro": true, "oliversalzburg": true, "shanewholloway": true, "spiros.politis": true, "docksteaderluke": true, "pensierinmusica": true, "animustechnology": true, "horrorandtropics": true, "christopheredrian": true, "ognjen.jevremovic": true, "avanthikameenakshi": true, "nguyenxuantruong.dev": true, "vanshwebdev": true}}