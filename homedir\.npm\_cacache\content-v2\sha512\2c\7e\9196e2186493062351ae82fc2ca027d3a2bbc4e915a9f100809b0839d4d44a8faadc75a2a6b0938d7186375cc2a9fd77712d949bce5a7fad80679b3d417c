{"name": "destroy", "dist-tags": {"latest": "1.2.0"}, "versions": {"1.0.3": {"name": "destroy", "version": "1.0.3", "devDependencies": {"istanbul": "0", "mocha": "1"}, "dist": {"shasum": "b433b4724e71fd8551d9885174851c5fc377e2c9", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.0.3.tgz", "integrity": "sha512-KB/AVLKRwZPOEo6/lxkDJ+Bv3jFRRrhmnRMPvpWwmIfUggpzGkQBqolyo8FRf833b/F5rzmy1uVN3fHBkjTxgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeit2JREMnGo+lyI6yoifcGwz03uckJGwbMOYfvRnHzAiALOmJCbvzQggmzmPybp1dsmmBlKgK+mVGpSEQMu2Wojw=="}]}}, "1.0.4": {"name": "destroy", "version": "1.0.4", "devDependencies": {"istanbul": "0.4.2", "mocha": "2.3.4"}, "dist": {"shasum": "978857442c44749e4206613e37946205826abd80", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz", "integrity": "sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpKO/Fv1wYVlXGPq1EyWJQ2ypd+HpOairW5TRyVAi+2AiBf1Jz1H3kLevLMb51WDLm7K7F/gFlOd3LA00/uEgn/Sg=="}]}}, "1.1.0": {"name": "destroy", "version": "1.1.0", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.0", "nyc": "15.1.0"}, "dist": {"integrity": "sha512-R5QZrOXxSs0JDUIU/VANvRJlQVMts9C0L76HToQdPdlftfZCE7W6dyH0G4GZ5UW9fRqUOhAoCE2aGekuu+3HjQ==", "shasum": "b77ae22e472d85437141319d32ae40b344dff38a", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.1.0.tgz", "fileCount": 4, "unpackedSize": 7604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8EgXCRA9TVsSAnZWagAAN0AP/099LQLye0ezRKzDBFO1\n9sTqui6s8gk3Bve4NE4hRLHiASPiR4sQ9ny2PvHkilR8q0ybTFNODcj/3BPC\nLcvB8XpT1aTs04tIOw2WUNHA738PhG/TQDy8nDZanA2ZKOAESBZRAJyx8iHw\n2pTJC0brDshttzulZWfQG13lpAMH3hQsId+yKHShTu6gpbSfE2FiJicxpzHP\nFkBb30yLGA8TOhd4wKnsso32ppgMa3otipkySg2W64E8KvGFPWtFmTpmJEyE\nyKX1vg0A5Q2+6Jhq82UaTiwrjSrEVLgHrIUp/00Wr5lfzfLOIe2Xqng8oE1z\naVa0aXAzxKSIuyL6bE8LEwLgz7zbVil9bI8xUFo1XEgrwc6/YQds/5xHbIkU\n7Ecyxeb7LBtpIVdOdXd04Ahs+SvOnRaO4sjKLAgmfmHD9Vf/+RFBpwHT1Ces\nhlGnHQMXvBoaExc2PZw22JKZ0ZKsHHycE3C8lefRxa2zA6ZoqGoYFX6unwij\n1zaXqFkOEOEW9Jw/hyEyFTsDMbhye84XdA/eQs0PV+de3kIevsglZYSkmUbw\njS71+tRDVePB41i4I/ovYseC+7wmC5InmebK0ROGJ3G0Ba1+fc9S9haANbxq\nMQjZHBiEODSfR1jMFnSZbxhcGk0NWYwfkzkq9l+SjH1iLM3BleNmYZ5veUzC\nOG+F\r\n=VXVf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFqMP66wjv4rJ89sHGF7xzFRWFZFvzwiAYPjhb5KKCutAiEAkiTx/rOxl3cJSoAYiW8zIcYgHMatmeMy1Os+wJI4/ZI="}]}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "1.1.1": {"name": "destroy", "version": "1.1.1", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.1", "nyc": "15.1.0"}, "dist": {"integrity": "sha512-jxwFW+yrVOLdwqIWvowFOM8UPdhZnvOF6mhXQQLXMxBDLtv2JVJlVJPEwkDv9prqscEtGtmnxuuI6pQKStK1vA==", "shasum": "38a65ed2f2615ad12bf59c6b5e885512c0cf13dd", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.1.1.tgz", "fileCount": 4, "unpackedSize": 8176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHSxUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeOw/+MojKAAoQ23Kz7o5dWOfB68l3dZvX+3m82sDgV30x6K4kAiMW\r\nDBQEA44Fl1+Vs8jhoNklFwz2RgkOZ3DQuMgN1I8sPc5ShzJ3Jvybjmhb0vXu\r\nODquOv8TchRuvxFAz6GmUziWuEFSYex0jkX2qm9eylhNxVxA+LAAmOSNJI56\r\nVnihLzGTEabvj09cja4F1JmgzoAKTf7Me3z0ac+lUNVRMLZ9rgKC03670xJr\r\n+YzbLKUwYuiNU9yrUVK4S/8qxcHorWMfzPe6Y1itMksq/Tfno2lFSoyBAZjq\r\nMbeXPVRTUVUpXkMIgPEkpink/WWGtDBASiiWslej7O8qvGTD5IccItvLl8hH\r\ndAOvXIWZuA1GTNhI5iQKFc9T5FZTl8qwKuyJgRJRZtq7dcZ7f8OG3OyCtvzg\r\nm+WS1myI7L2srKhN0l0Ujki9YJZjNiKaYuszooKPetCqjyHi8XPhnf2gawGJ\r\n2JeiFC9lYGGNDX0V8vUSaoSua7dC5Wrn1zBaKodbhSro9O5dPFa8zxS7NT0R\r\nyM/afWmetaL2eFYjHC+BhYsAXabyCIrvpjsHM/XNEBWaUaND73uwQ57m4w5V\r\n2vvjUhV/GfepntH3jrS8VKHy4knuxwS8P6ygXm+GJEVfZd9o2T4qUBnyR7qb\r\nTASRLNiaZEbBLL8weXJogOE8SiHZ5WjQXZ0=\r\n=tpo+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFeiIZ6BCzTB2Ibcc35m9sYhye0gIs5Gpctt8p2bi53SAiEAg7GmCElyD1czRCdNZcGUkqxsZkl/bjRHcxl3FEApvmw="}]}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "1.2.0": {"name": "destroy", "version": "1.2.0", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "dist": {"integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "shasum": "4803735509ad8be552934c67df614f94e66fa015", "tarball": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "fileCount": 4, "unpackedSize": 9018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiN3pwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2ZRAAjcRX9pSm0kfICnOCjFla9HKKvzVVXwMNJQ0cPWAN2Q4/sCr/\r\nsnQY0aTN7TxYd2FdsTUS6V1WVfVMQZCCJbSuIAEVuSZaIQx+GPVnPpOTQyG5\r\n+gksV1GS2TC5gg/CqHeXJCZ+I9SVpLK+PiUiGojODYqdLWOrtGSIJepYAkLL\r\nZPJ/YBFeVisOFETdmjOYj43l6+cru0WR21AUJEjBFhiiXxi2ex4B71l4f0rm\r\nNATSmlPKX7m/Wq/1A85wR1V8RHUU8dFS9SQ9ZpqaHUvTzfQ89KdEzweEsQg3\r\nLbUwuf3tVUPv1IWKT3rZLeh+2XF4ycDRwIA/u8FtiQ2fNTeIjEj8iAbi+Gwq\r\nAx+tQuLh28XH0maLrWYbgTo6Jph+s32AdHLfgdAYnz9xlNOiyKmvfI9QCBs+\r\no3SGVOW20u5YvGNL7+kh2UgACexrOG7aZmAJQ6SOegxlKK7ZzbHEFVqLAjTz\r\n8sDm+WAgbbm5jvDKoM+dW232t/zlvhyuilGbfNgNIpCe+JQ8voXaNEK3L8Ek\r\no8SYW+iaD54tNMSivIChGSpH+L8LWk5Q5zzZ3XIRiEt3tqhNUfWzFoXvlroU\r\ndhPVXDfKMjMCkFzpNfB0NYV7RpRTGa47XAMG7t8J2Ag6wAjd1kWdehZlxH5y\r\nZ0v+ieCqjGkKESD1U51GvxC7WYlGzxk3lMc=\r\n=uElj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAvgBzNFFtjdCvxlY7mn7G0Iibc5t2EE7fAlK+zlw/jfAiAmhIErvReywAyY5drbpZj4xVSVfDeVfAcU8T1j2KJRzA=="}]}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}}, "modified": "2022-06-15T01:49:38.799Z"}