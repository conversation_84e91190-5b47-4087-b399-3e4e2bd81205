{"_id": "core-util-is", "_rev": "15-f43db8b21eed7517e44fd4383abc1bf1", "name": "core-util-is", "description": "The `util.is*` functions introduced in Node v0.12.", "dist-tags": {"latest": "1.0.3"}, "versions": {"1.0.0": {"name": "core-util-is", "version": "1.0.0", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "_id": "core-util-is@1.0.0", "dist": {"shasum": "740c74c400e72707b95cc75d509543f8ad7f83de", "tarball": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.0.tgz", "integrity": "sha512-0Str2onNHGPWBkONuKNLtH55ay6DNkUe7w6BiIv3boqbWSk9rva7uzI6R13BcI4LKCiy2vWjt7AzGp1nw7alyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCJlhwncDGWRQueAsY4RitqNozWOyqudYM6d6xeU6EFQIhAOudkmPRQupNAv4cqPiO/g4hxc6ZnOEQhPYcM0JEfYCs"}]}, "_from": ".", "_npmVersion": "1.3.9", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "core-util-is", "version": "1.0.1", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "homepage": "https://github.com/isaacs/core-util-is", "_id": "core-util-is@1.0.1", "dist": {"shasum": "6b07085aef9a3ccac6ee53bf9d3df0c1521a5538", "tarball": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.1.tgz", "integrity": "sha512-fyj6blBb339ReZvm1g6NHGVI/q7THT6UJb1GVDMVxRvGb2v9J1xazqnfQ10wrChRH6tpxJLY/eUmFH0+gANIRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA+2NxjoYr8SUT9yMvkTrlnw9fFrbnSiMi2F+JCAoDVWAiASRMSA+bxFZMsIfUi3rGrA53OukeX9KEbJt3VPLVssuA=="}]}, "_from": ".", "_npmVersion": "1.3.23", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "core-util-is", "version": "1.0.2", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is.git"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "scripts": {"test": "tap test.js"}, "devDependencies": {"tap": "^2.3.0"}, "gitHead": "a177da234df5638b363ddc15fa324619a38577c8", "homepage": "https://github.com/isaacs/core-util-is#readme", "_id": "core-util-is@1.0.2", "_shasum": "b5fd54220aa2bc5ab57aab7140c940754503c1a7", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b5fd54220aa2bc5ab57aab7140c940754503c1a7", "tarball": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICCvkz+hdynpjnPx1hLuAX9RQccJgakxeoLWB18fZNsdAiBNv7LVvsJGpglqSsdD9CPLCyrCn/GSk5fcGK4vaSiQNQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "core-util-is", "version": "1.0.3", "description": "The `util.is*` functions introduced in Node v0.12.", "main": "lib/util.js", "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is.git"}, "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "MIT", "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "scripts": {"test": "tap test.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "devDependencies": {"tap": "^15.0.9"}, "gitHead": "85f4620829d1b6079fd7b351f040b6ea7e184970", "homepage": "https://github.com/isaacs/core-util-is#readme", "_id": "core-util-is@1.0.3", "_nodeVersion": "16.5.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==", "shasum": "a6042d3634c2b27e9328f837b965fac83808db85", "tarball": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "fileCount": 4, "unpackedSize": 4982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLj44CRA9TVsSAnZWagAA0AEQAI2rvxf1UM5FkovQLnTM\notN8pVW7FNS65fxu1FLu2d9NX74wN/ASuQwy0UIa+lGkHxq5tdt68JhPwvLQ\nTFDDzWt3Pc0Oym6axkIGM/g0Xa/CS8dzbOy04J+4eys5H2GFEVaSoRqgZwL/\nIM1JkE0utIRPLL2b/+9t6Wbe34ERurckzu7JIuJV3Aa6gkYyHhyrprqDh9Ft\nJG9C38gde82J/KRk6cT5E+LUF9oxCZS/OvaR/JZt9qtxJGTVKah4dVNOu/2W\nIA07E418ydf5iNxZy5EjT/M9UJexUygmzWd+KwFrghjoRTxXsQ5Xkl1qtiay\noLwiS1/1eqokkgLsIki/7v7cMKyJ74Dxgf2TOLaIQsLgClqlOvB+lU/FmaqR\nDjwLblnDynxQbAyG9wlv+8zuRCN9GnPnYdCkTrz9T3cYcr55xNqo5xabzhbs\neHmIh6Wa5PcDwWCrKer9BO/V/h6WouZ94xBljL8artMI2xZJhx3NleSNkSHm\nRIi8qRB3f6o/AP/Db00Qw6EZ/aubezwKLTdIfbv/fptzZoDFCkmttFkpm2HM\n8/xaOWFC4Vfv6nCe5boFym/Sjv1xaX7738gfekKNyz9Ukim2JY2LAHYcfLum\nVu7rxTCYJCiXYAxN9MBfP67Xea3gH60bS7nFZA/Rt+wc7CCItjfkUMXwmYmy\nuGG1\r\n=Ftuh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDM3OvU/pAA0cvNI8WHXIZgxFM0qRr2Lj7WWJp2+uxHTAiB+mDe6BTAT7as04NQ6lD36C9bI/dz8Nu4DQIuGxL6T7w=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/core-util-is_1.0.3_1630420536772_0.9472501484769849"}, "_hasShrinkwrap": false}}, "readme": "# core-util-is\n\nThe `util.is*` functions introduced in Node v0.12.\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2022-06-14T01:14:43.878Z", "created": "2013-08-26T22:51:58.467Z", "1.0.0": "2013-08-26T22:51:59.881Z", "1.0.1": "2014-01-08T05:50:45.831Z", "1.0.2": "2015-11-20T00:37:33.081Z", "1.0.3": "2021-08-31T14:35:36.995Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "repository": {"type": "git", "url": "git://github.com/isaacs/core-util-is.git"}, "users": {"tunnckocore": true, "wenbing": true, "cparker15": true, "monjer": true, "shuoshubao": true, "kael": true}, "homepage": "https://github.com/isaacs/core-util-is#readme", "keywords": ["util", "<PERSON><PERSON><PERSON><PERSON>", "isArray", "isNumber", "isString", "isRegExp", "isThis", "isThat", "polyfill"], "bugs": {"url": "https://github.com/isaacs/core-util-is/issues"}, "license": "MIT", "readmeFilename": "README.md"}