<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
</head>
<body>
    <h2>Test Login</h2>
    
    <h3>Super Admin Login</h3>
    <button onclick="testSuperAdminLogin()">Test Super Admin Login</button>
    <div id="superAdminResult"></div>
    
    <h3>Vendor Login</h3>
    <button onclick="testVendorLogin()">Test Vendor Login</button>
    <div id="vendorResult"></div>

    <script>
        async function testSuperAdminLogin() {
            try {
                const response = await fetch('http://localhost:50002/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const result = await response.text();
                document.getElementById('superAdminResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('superAdminResult').innerHTML = `
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        async function testVendorLogin() {
            try {
                const response = await fetch('http://localhost:50002/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const result = await response.text();
                document.getElementById('vendorResult').innerHTML = `
                    <p>Status: ${response.status}</p>
                    <p>Response: ${result}</p>
                `;
            } catch (error) {
                document.getElementById('vendorResult').innerHTML = `
                    <p>Error: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
