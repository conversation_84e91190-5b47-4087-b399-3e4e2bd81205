{"name": "yallist", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "yallist", "version": "1.0.0", "devDependencies": {"tap": "^2.3.2"}, "directories": {"test": "test"}, "dist": {"shasum": "478c1272847a661519938e3a43b98d00faa12f8a", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.0.0.tgz", "integrity": "sha512-KNi+WzRCAoXcBT3z+UFoYSubPzBDvm/Jh5GlLaaTdvo/NcJn/0DokB8Oci5xc6N5bVnZoWjS9hsfxJ5Wn35fXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRDYHf9TOtha1ACYUtC0CLT2CeNvVyFB58icbW2SppUgIhAMHzGjvYvzK8S3b7H4OVqkZkOoQh7gWaVTPGZymPqvsb"}]}}, "1.0.1": {"name": "yallist", "version": "1.0.1", "devDependencies": {"tap": "^2.3.2"}, "directories": {"test": "test"}, "dist": {"shasum": "daa8df3166c72bc78c3d307ea8195c1c14a1430c", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.0.1.tgz", "integrity": "sha512-34n0AanmymrvNSUcdwQAQ1RmvRoPkSKHmXR70ln4+2L3JZaKQBXyhxXUY7bRIqPKqb5jgncxFRbdCtTJqCYWDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAr1D4CJQyK+gfuNAN3UHF1BHk6c7ZxmpnXkDCorUDBUAiEA9035dEzDkHpmMF8IHmB7tyHYOws1x5U6hCef5SrVV4o="}]}}, "1.0.2": {"name": "yallist", "version": "1.0.2", "devDependencies": {"tap": "^2.3.2"}, "directories": {"test": "test"}, "dist": {"shasum": "2805e1ba9e78bac506a19c4e028d7da6d031adef", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.0.2.tgz", "integrity": "sha512-m81cHiLhQWWHr9ewXLJ+zvFnk41LRiDC2+raYPdYRW7ohwC1tWz6xmfdFOxz3AaXQrIjk1aFSsSygfn7V0nCXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/oRG9jyuZnCke/gJy2TiHom6IJ/mfvrRDSnIK10T8mAiBRk2QvcedotPbSM26b5tc30Fwvxi9v2rALGSoSTWCFxg=="}]}}, "1.1.0": {"name": "yallist", "version": "1.1.0", "devDependencies": {"tap": "^2.3.2"}, "directories": {"test": "test"}, "dist": {"shasum": "f1878f30e2955a6da83ac6f179782344cd6ea4fe", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.1.0.tgz", "integrity": "sha512-2pmuDf+Bn8FjaPPavD52OKuG+8rsSYkgDTHNXI6+YwRIUGNz7//deuhhy/3ON1jKvc7KExp0dQ8iNlHc6DhKog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICO1sZkl5xCtUy30s4v7Pll42g0YXguu6oQitnx8oj9lAiEA+Skfkse9DYq/IJmuNjVMWhRb+WgHwp1v4Eb96tv3JrY="}]}}, "2.0.0": {"name": "yallist", "version": "2.0.0", "devDependencies": {"tap": "^2.3.2"}, "directories": {"test": "test"}, "dist": {"shasum": "306c543835f09ee1a4cb23b7bce9ab341c91cdd4", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.0.0.tgz", "integrity": "sha512-l446SSVnGyIspyBekF2U4g2cUMLwBJLu3IvXvqDSwmJFWtAgQ9B8CtHArNluFgI7nPm5+EEbH12fwPIwSXPQyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXyw1IJYcIEi0lBOPW+uRSkwhtma4RqrGUnLdTtYgHMwIhAM29+pFrDOKyk4Ghzz/+FvC+qA/8QlaBX6yXH+CBlEGr"}]}}, "2.0.1": {"name": "yallist", "version": "2.0.1", "devDependencies": {"standard": "^5.4.1", "tap": "^2.3.2"}, "directories": {"test": "test"}, "dist": {"shasum": "6c11279640abd6ed0a86dd32ed56b03caa9df4f1", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.0.1.tgz", "integrity": "sha512-nvYoTNlZ+T0MBdHpdrJMyI1AY/s2NeSqukeXuwiaTPZjbkzsg2M9f0OmmPwyIr1DFu88Xd7jzUj2+SEotaEuog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUwkhQe6Di3z+78ztRX6Nr2LnprmCB088SoTKxLuMvrwIhAMjYpv9QmSd34QF7H+IZ1O9phy6UpgDcBDbBgomeOWZV"}]}}, "2.1.0": {"name": "yallist", "version": "2.1.0", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "3a0f3b45f42cb60f822c92f69ade2bb88beb1ae0", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.0.tgz", "integrity": "sha512-bw4zKj62wAeQuEpEdtr5a8EWAhVdmCqzogKTo/3ZtdPCeu1yKOM+/DzzSdcs4BqOcovXxff8W/CAaNzp5qSzXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGpqpbjIg4BLOmc3khatdAkheLMG+X3Z068fOrvV5JyXAiEA5uXR4anxdGjDT0jAdlap5EyN9aNb8B+KpFi0ewCP3V0="}]}}, "2.1.1": {"name": "yallist", "version": "2.1.1", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "08309c7044b1761d5e1591dc12c67629271b6ac3", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.1.tgz", "integrity": "sha512-wJXW2iOHgovkGFfG0dBkFufZ+QbrPRe4H7f2p4CSIOgmGB+32iuMV+5mjQUi+KOYEvukoQTxz+wYvraUdCNgfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBCrW5PF20efeh01J5khErAd/4dCJJCIuxcRD8hwYabFAiEA1zaMlKFMUO7YGkBXBwSCu2MJB5hGeistgxByplE4vyE="}]}}, "3.0.0": {"name": "yallist", "version": "3.0.0", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "717369f9df727e5a03cc69c9ca94d7d94579f58c", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.0.tgz", "integrity": "sha512-e8<PERSON>sK5rgly0muU1LeKbPMpiJoLWDEfe5+hanR9wljH7NJqmQdtKIYFQkxdLENYpl59mqg5g42bYS2tX59Gu4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAsp+fD/c+kidvGrfB+BAVu47PHdsXlGmP3Rb3fdomFMAiAajJpSDsmduiON+nuK10DKgLMs0bgL1qJjwnhpv9jxiw=="}]}}, "3.0.1": {"name": "yallist", "version": "3.0.1", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "4affa89763ea5c2aeb9e2ed98387ceada34f4c4d", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.1.tgz", "integrity": "sha512-XZPrO9OnL1B6IOPVEgcH6Ex3pOd6KMqtGlXjUSr/VR5nSWOYujm5z5y1iVxoVt9hrusLrKiWzGyqpQFILhtGIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+cWLJdcGXmhbWhS5XQ0NpLE6Uxmjl86GRV76lq9ufpgIhAJ+F2PQzm2mUH14OIBcRuhizOo3LqRWqBWoWTkoLZNck"}]}}, "2.1.2": {"name": "yallist", "version": "2.1.2", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+TNJuIqGHzo8Bvfepcs0WQiDozvoOqZnQRPZlvhcbvAiEAv+4EA2UZc8xlaE//S6Kb2OOZhP7gffW+kKojB/TbVKo="}]}}, "3.0.2": {"name": "yallist", "version": "3.0.2", "devDependencies": {"tap": "^10.3.0"}, "directories": {"test": "test"}, "dist": {"shasum": "8452b4bb7e83c7c188d8041c1a837c773d6d8bb9", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.2.tgz", "integrity": "sha512-U+iKQ8rDYMRmvEpvDUIWZ3CtM9/imlAc+c1yJ7YV0vu+HNtP82sAkXzuDXPLkIPoLZohnXFSs9wf2E17xk5yZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNGjxMhXXtzoa07tErhxxrUkrYuIq0l4omD8xmOT7OIAiEA2OtStH/5XJXBacd6Rx9xPByqKGJKeDHnIqyVNFWmJAI="}]}}, "3.0.3": {"name": "yallist", "version": "3.0.3", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A==", "shasum": "b4b049e314be545e3ce802236d6cd22cd91c3de9", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.3.tgz", "fileCount": 5, "unpackedSize": 13744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9ei9CRA9TVsSAnZWagAA9zIP/ijnxiQ7otRl2x3s1hy3\nzi7QbMsTJzIafuxtZNPxIctL9ePQkyl5ccfvAGamlNDJKaWZZocHfCghCaCw\n//TLvHHISid3zVbs6iYiy/xGP45UFbRf+4/qEmBE5JVV9ZHyjwaNDwf19HQ5\n3Lcfofdwo3kkR/xIWsYA2S2vmkdwviPYPWsPMwyQIGmCnEV61WUuuvedXA5m\nBd5W6l6ofe8qGQPQVaniEFaqViUZlNxSyiwSiKcUNve0sWvT0BVr9QmvpDFi\nmcGrYbcUhMNtxexdUDdJ4QB0BNLEPymm2iCB2iaq+WZiZcCShDFl1M9PJKdB\nNQsKddC1xTfWfdtIbtw0rQDwBOYJpw5sFFnurCaAz2/JNEaYZSqxwzwgud0s\nh7/AaTV/1Knpk0qEXskogYMej8kO4AIcel4iFINxlIy+5yDWHcThEm2aMDNZ\n63wRojSH/crwKdFYAwVxCUTRQGcLre/0NqNIL9F6cPZI/JmzzG+nfp6UEf7K\n6E8zeY7xPf6fJ2KmKyP8nq6rNY50JexlLwY30LZY9g9shaPBQ9lAUSTaIBvN\n3oDin3U7V/+Reqlw8tSOtjOUP1sqSP66Wv8Z0NfWUgv1jStv0cduD1rAvMHE\nCD2VxLsFsl1rv8uGuW7NfNkcrBtqp7oFGsoBDAeHVUCjRh5ZDrZdmbY7zabD\n2S3K\r\n=UzPp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEOR/DV23WzacyszfQhg09nw3Ny9djU1dzEAvs3ZULNTAiBQclKbl9EXuu9+CRH+TKZBQlaGZDHW/1FyzfJ/5qLnHw=="}]}}, "3.1.0": {"name": "yallist", "version": "3.1.0", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-6gpP93MR+VOOehKbCPchro3wFZNSNmek8A2kbkOAZLIZAYx1KP/zAqwO0sOHi3xJEb+UBz8NaYt/17UNit1Q9w==", "shasum": "906cc2100972dc2625ae78f566a2577230a1d6f7", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.1.0.tgz", "fileCount": 5, "unpackedSize": 14752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjPHZCRA9TVsSAnZWagAAuDEP/1O6n2B3Kc7y61cvdPyD\n4VMIJMEXwhGEBX4tGY1dMDlpgzzB36WYeGNrTLwDFAG3ylB4GnUuBqzULcBw\nSqPFwWaBIQr42no90xufaH8eYOaFa7CGZocQr/5DLNkWPCRJFbz5bxGzQw8A\nWE2/5JrWKFRmAP+0T4zHLix109VGcAtsVZR4hYlyZI57uQTrmH4MMXapRdeM\n3THrHhUaCb9muAFET1wPpJ2ElBYVj9yxJhLWArjtX70/+4RFCNhtT3sIQvcB\nm6l+ntmxYGEL5ctRMuO0Zp6HJmNFe9LUDzORb+yIcYMBElHTw9x8dm1Mf7bA\nwy4S/ltoYb1JQc7R/WdRzAQ/7ydQxPi58+qitwg3dRVC2g7QLRlQ/qnGo3ua\nhbhp18s4pyc3fCpVfBcrm9wRprF6+3a+jkxoCCD520+Dk39wikPywe5erXeh\nXXtmjJuelQ5NTncwPoKgSwUEUTxxu5SM4U8ZCrmLm11NeoSuFZAlGhzZ2RY7\n7Hbuxgeebem1iXINKs+2wKqz8lDqynl6B73dr+RKvo1a7n//wnryv7RdQt7I\nUnXuai2w1nyF0ujnB5eY1hWHOI98S5Wfhd4ixSO+NJXfDmSSkYEy8nt8zZ1q\nWRPb+OCU764+aaIMnou3+HeFgFabfOxQQZjyV5IOklaJr4P9Qc3ScLeW2ejF\n/s0O\r\n=uzdV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEPx4WzSbryVu0CMhLYbdkmKSsfap8EuN96AhwLjBs7QAiEArhd19awNw5mqGUcFtiUK9C9T8WuLZv6BcQJR5GtTn1E="}]}}, "3.1.1": {"name": "yallist", "version": "3.1.1", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "shasum": "dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "fileCount": 5, "unpackedSize": 14766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmB2CRA9TVsSAnZWagAAWgoP/0j+iDVcsa3byazx3CgE\nm/aBzB3GflrXZAtMZ11Q/VfAa+gNDWDSJ2NOFmX/GxhKTm1zeyhl14WgDAZN\nE/DIF6bZkAbr01gz0k9Xa7/sMprHei90zQ6oNxmAONiiR0N1E7HRwN7Qu32C\nKMYGky23RxnXOmcX3Ij+dITQPNT/3525ehO0F7otC1b6AUwHybawJYSv7ILq\nR2x6w7X9RE9cizxLADzFM7PwXHic/20b8lgCnaA7P7qqSauVrlmdiaejmvAW\n+F6/S2QCfWGsXfLMkvJbBAjcX4+mWE90/gz/qC4+DXlDjRAJ2LCVKCcXsszk\nsz1mzU7FDfN3tp60kd0XtEQ6xuagICLxy+lit2maTcwVQSYiKko4T7aFAFr+\nYKwEsFl1+PcnSDqnuc9+6K9K7dYYqAL2/4L509joWusG6cGWdWGd0j5IGosG\naXKFW0zB54WRRbZTsNrfpy7f8wSlXZSMZyeC/n84rUKAx/HtPycBkoJTvFdD\nkIaCnm5orykEaT8JSAqqG9o5ndF4Cwba79DUzHPWZscSR43A34iWJGKb83QU\nRlNLkxo8mgAFMNdA808wr0tyh/bbm2d5q6ZCKnTmrlktPCBcsbi854ol9WAH\nir/yNg3bKNVGgvMLKm/qngACxszUnIOy0Fsi0I5eUGg392CNbgAf7myATWlY\nWXcr\r\n=gXWc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4S0jeQXl5F21QIAwOE5yjXumczmgvEYzxql0ZH3+R2wIgNuJW5JkGMAXm598C9Bi+MvGe319f3bz2D1eR6wgDDpg="}]}}, "4.0.0": {"name": "yallist", "version": "4.0.0", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "dist": {"integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "shasum": "9bb92790d9c0effec63be73519e11a35019a3a72", "tarball": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "fileCount": 5, "unpackedSize": 14752, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmCpCRA9TVsSAnZWagAAkVgP/3zxsW6EFohtKYJOFO0J\nlkdsbK2ee8965Oy4UNyD9F+d13p47zFmrNztw+zVS+gEt9AkSpjwMomHdQAV\nJ9NBsGaG7ppjaeNNfp8CwGO2El0rILWeEr4YdhoL6wfnxa/yuQ6HeuJty097\nOZ/FP3CXPCxj8Jlos0NwjLig/Yemtt1PNNlufcDhcKX+wzftExCMir+lBBna\nq5NpnUnOQnr3+QUFqeRHXWgPh4nJLGd9NuAmu/HcrlCnu1FEd3nWrmKtEvVg\n3GZn8lK8FLteiAxS4rHaQb8QX0W9r/RGLxuvla/KXq9LfxucTStI5pLhTj3A\nZlOxZDI/v+0wXAfr3+Bcsq5sJefk0+N5h0sWc9+hl+L2yvbMiZr2e8Z67msS\n9Hl8uwvjMwtEOCTMIyBdvBVy0OCzvEHjm+2tYwd/e3VTTWzEc9zIdUirWVU/\nqcCgSMnN+8MjV+8KLdLHVjDuxQSHFlm0LcmaU5ILlKX57rKFVOVWt8ZJ3wep\nuAzfUOkmrwWR+lN58w/MIbhdwqHcMpLmgXBJEHEtbfh7Qk6yK+A/Q237YBNq\nYIIB24k7z0vcAPN82s3LxxfLxbQBzFB1mwT8NcKQCG5XJbef8V3LLJeD78iZ\n1NvS7tl8VpSUg5Vc3C5OJvmbxbFiVPZXio/nFnwwR2jNe6ukFJ/YKlSo5MrA\ny6Ty\r\n=3Dpm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYUnufgWErBDMXe8KslrNmcyrPV1kGU2qlaK4ZeXoazwIhAKBpS5djrxLEclmS9SbzcNwhJJW8F7NXnArMOSGiGSCv"}]}}, "5.0.0": {"name": "yallist", "version": "5.0.0", "devDependencies": {"prettier": "^3.2.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "dist": {"integrity": "sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==", "shasum": "00e2de443639ed0d78fd87de0d27469fbcffb533", "tarball": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "fileCount": 13, "unpackedSize": 80297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6e4G4ytvhoms+0w0K9/h69xTaFaNhKrrQtsadvn51egIgFDoo7A3fA8NgnwexxWK9dLURqTrJhW0gluarpuWEJhI="}]}, "engines": {"node": ">=18"}}}, "modified": "2024-04-09T19:21:45.008Z"}