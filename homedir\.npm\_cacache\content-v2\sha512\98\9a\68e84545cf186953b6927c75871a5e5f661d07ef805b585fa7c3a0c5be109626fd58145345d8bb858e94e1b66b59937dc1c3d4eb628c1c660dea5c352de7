{"_id": "bytes", "_rev": "69-3378ae4662dff77463ea8aaba1891eae", "name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "dist-tags": {"latest": "3.1.2"}, "versions": {"0.0.1": {"name": "bytes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "byte string parser (5mb etc)", "version": "0.0.1", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "bytes@0.0.1", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.24", "_nodeVersion": "v0.6.19", "_defaultsLoaded": true, "dist": {"shasum": "2a76c866ba90e6fd2641ab5c9fdb6c4e8b4015f7", "tarball": "https://registry.npmjs.org/bytes/-/bytes-0.0.1.tgz", "integrity": "sha512-2ZhP53YDniyaeXmgESc8twPWeTRY0fY6jqVEHJRvG16w8ZgE1kK+/eaiSAnTJmz5m2408nrgOXNIdaoHjE/Vww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGOcpRJisVFVt8+VH0JUsYLSpxbuM6HDSb+xetMQ56R9AiAiyHHhquwcMLwDBcTVNVRKQVFa/gNQRPKyQ4guBnV3pw=="}]}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "bytes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "byte size string parser / serializer", "version": "0.1.0", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"bytes": "index.js"}}, "_id": "bytes@0.1.0", "dist": {"shasum": "c574812228126d6369d1576925a8579db3f8e5a2", "tarball": "https://registry.npmjs.org/bytes/-/bytes-0.1.0.tgz", "integrity": "sha512-zTSmfpu7b+Mll4T9ZjTYUO3Q6+m+F3ZEQ515ZECaAFhmmHiRl/UcdcAsuFyVklbMRo9GWyRyqTsB6C6ahjGnVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsXk2cE06Mt78hwrxxUDellfIKiOBo017K0X0eF6ncKAIgdk7Ruh81XeLkgC2jMRfbWi13u+yMy6UkaU17YyE2Uf8="}]}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "bytes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "byte size string parser / serializer", "version": "0.2.0", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "_id": "bytes@0.2.0", "dist": {"shasum": "aad33ec14e3dc2ca74e8e7d451f9ba053ad4f7a0", "tarball": "https://registry.npmjs.org/bytes/-/bytes-0.2.0.tgz", "integrity": "sha512-qH6XVfDizpXcxZisRfVo6rtnGQC2EoF88+p29KDyGN/0VQXFJ+ot8pkYiD673sUgeTirO42UVBitFOFzjVOIrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUOaMCFiySMNT70A1ElyLJ67jlXMYkPW9q372xm5KEOAIhAKty4m3kcSXhLaqk49P9wlmoR6exRvfC524kSTrVLzgl"}]}, "_npmVersion": "1.1.64", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "bytes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "byte size string parser / serializer", "version": "0.2.1", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "_id": "bytes@0.2.1", "dist": {"shasum": "555b08abcb063f8975905302523e4cd4ffdfdf31", "tarball": "https://registry.npmjs.org/bytes/-/bytes-0.2.1.tgz", "integrity": "sha512-odbk8/wGazOuC1v8v4phoV285/yx8UN5kfQhhuxaVcceig4OUiCZQBtaEtmA1Q78QSTN9iXOQ7X2EViybrEvtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCy3Lfo9HWeMRx89Env55OARilhr/035Gy5DBr+0kZH3gIhAMtZ5H4MCF4QdWpnUDzyjAPR2SoXbEui1P8NdgKnLZ+z"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "bytes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "byte size string parser / serializer", "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js.git"}, "version": "0.3.0", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@0.3.0", "dist": {"shasum": "78e2e0e28c7f9c7b988ea8aee0db4d5fa9941935", "tarball": "https://registry.npmjs.org/bytes/-/bytes-0.3.0.tgz", "integrity": "sha512-koasz05sePZ8FLtyBSyGGlrvr1DYqr+D/MFXkz9afRugTCGKuqw6fjWMMmaCWEKtmWpgOnaGI4qlw/hPyyYX6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEZud+4pBNtClKgW0u9lrPJ7msR05PaNGBIdWBVYxv7qAiB79mc9om2yZtAChqnQg99HrHHkkVRWAm9yZolz0HneBw=="}]}, "_from": ".", "_npmVersion": "1.3.15", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "bytes", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "description": "byte size string parser / serializer", "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js.git"}, "version": "1.0.0", "main": "index.js", "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@1.0.0", "dist": {"shasum": "3569ede8ba34315fab99c3e92cb04c7220de1fa8", "tarball": "https://registry.npmjs.org/bytes/-/bytes-1.0.0.tgz", "integrity": "sha512-/x68VkHLeTl3/Ll8IvxdwzhrT+IyKc52e/oyHhA2RwqPqswSnjVbSddfPRwAsJtbilMAPSRWwAlpxdYsSWOTKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzaAu2kfu04Asalxc2OspM5ekHfq4NTHa4qFcjf4ROzgIgQwUiWuUcMIwfIAnUuFwbD/d9spYq6RLoDLqduYtoWjo="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "bytes", "version": "2.0.0", "description": "Utility to parse a string bytes (ex: `1TB`) to bytes (`1099511627776`) and vice-versa.", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js.git"}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "main": "index.js", "component": {"scripts": {"bytes/index.js": "index.js"}}, "scripts": {"test": "node_modules/mocha/bin/mocha --check-leaks"}, "dependencies": {"node.extend": "*"}, "devDependencies": {"chai": "*", "mocha": "*"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://medium.com/@msanford"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://vf.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://justindeguzman.net"}, {"name": "mixu", "url": "http://mixu.net"}, {"name": "theofidry", "email": "<EMAIL>", "url": "https://github.com/theofidry"}], "license": "MIT", "gitHead": "6b0e590f1aa83a43b20b4678441cba883f06d6fd", "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@2.0.0", "_shasum": "37feb25b3478674e7b78a16720826b033459a6ff", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.11.16", "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "37feb25b3478674e7b78a16720826b033459a6ff", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.0.0.tgz", "integrity": "sha512-jDZ1sp79Es5p93171z37OwKZ/GeE9Vf62ElWBlvbRLfDo9oP5FMizQh3eNuABN+QxCcgGQcGvQhyFXXub8+llw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkVGEcOpFYm3m1WQMCQiFAkdmnF2zMD1hc9ZyQCgYY4AIgUhaFPGHMfW3q0UiVVZkDNUnCVvEVTK87zsxl8+ipzKA="}]}, "directories": {}}, "2.0.1": {"name": "bytes", "version": "2.0.1", "description": "Utility to parse a string bytes (ex: `1TB`) to bytes (`1099511627776`) and vice-versa.", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js.git"}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "main": "index.js", "component": {"scripts": {"bytes/index.js": "index.js"}}, "scripts": {"test": "mocha --check-leaks --reporter spec"}, "devDependencies": {"chai": "*", "mocha": "*"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://medium.com/@msanford"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://vf.io"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "http://justindeguzman.net"}, {"name": "mixu", "url": "http://mixu.net"}, {"name": "theofidry", "email": "<EMAIL>", "url": "https://github.com/theofidry"}], "files": ["lib/", "History.md", "LICENSE", "Readme.md", "index.js"], "license": "MIT", "gitHead": "8c00b7361081cbd9bbefdeaac387cfa3483ee584", "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@2.0.1", "_shasum": "673743059be43d929f9c225dd7363ee0f8b15d97", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "673743059be43d929f9c225dd7363ee0f8b15d97", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.0.1.tgz", "integrity": "sha512-pUGc1znbnPovo6iycD1Ster5q3/ZP9YOqzXYLWKqgok0eCVMzZ84mVH3wjyRi6HPuHZ+Y8zKgzXdRWi9XIJUjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGSXu26NBGM11GKjB1Q6vTmaIAFVCsviQRIORXKXMd0DAiEA+HYCQStnjXX6GkKcc3HP7V3lSSf4C4kPpLo+9yNTtFg="}]}, "directories": {}}, "2.0.2": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "2.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "devDependencies": {"mocha": "*"}, "files": ["lib/", "History.md", "LICENSE", "Readme.md", "index.js"], "scripts": {"test": "mocha --check-leaks --reporter spec"}, "gitHead": "613ebfa44c8e42ae107833e1a587f6a3349f6a64", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@2.0.2", "_shasum": "580fea1111c2df039f2644ff917ce4010501184e", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "580fea1111c2df039f2644ff917ce4010501184e", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.0.2.tgz", "integrity": "sha512-OfudwDk9qPBwtZAamVy9DlwEM1onqoccAwVEGejp+tvIywbO4SIHEiDX54Ut9DUQavunXU7OSWvsdwfLV7YDjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPfgN+eGGpDsoji2gfvWLF80WCBwUD+In4m2MXVGRkeQIhALs9VQczTVYSPV9GZszf/RQ24crkO9OM92atvpZCS/u5"}]}, "directories": {}}, "2.1.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "2.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "devDependencies": {"mocha": "*"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "scripts": {"test": "mocha --check-leaks --reporter spec"}, "gitHead": "86e4520cc369b34866154a53344ca50b2bb5ddcd", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@2.1.0", "_shasum": "ac93c410e2ffc9cc7cf4b464b38289067f5e47b4", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ac93c410e2ffc9cc7cf4b464b38289067f5e47b4", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.1.0.tgz", "integrity": "sha512-k9VSlRfRi5JYyQWMylSOgjld96ta1qaQUIvmn+na0BzViclH04PBumewv4z5aeXNkn6Z/gAN5FtPeBLvV20F9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC51PmpO7BlP0nmh3A+IXgfilzW5Wkam1MG4oSJhosW8wIhAJitJVH1FLyfnw7xBhhwuqvzvoqZIq9vVPeyFPbf4fMZ"}]}, "directories": {}}, "2.2.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "2.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "devDependencies": {"mocha": "1.21.5"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "scripts": {"test": "mocha --check-leaks --reporter spec"}, "gitHead": "509a01a5472b9163ae5a7db41e2d6bd986fdf595", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@2.2.0", "_shasum": "fd35464a403f6f9117c2de3609ecff9cae000588", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fd35464a403f6f9117c2de3609ecff9cae000588", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.2.0.tgz", "integrity": "sha512-zGRpnr2l5w/s8PxkrquUJoVeR06KvqPelrYqiSyQV7QEBqCYivpb6UzXYWC6JDBVtNFOT0rzJRFhkfJgxzmILA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDzzZmUmAYJXrb39ZaE39RS6IJ8Z6SRo5ZLeXhH9dZvCAiEA0mPbdv1rO5Qaq6LBVxH8UVii7JH21RGOi6ZUNyLEqz4="}]}, "directories": {}}, "2.3.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "2.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "devDependencies": {"mocha": "1.21.5"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "scripts": {"test": "mocha --check-leaks --reporter spec"}, "gitHead": "c8be41b24b04e04992d5918356d5a4dd35fbf805", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js#readme", "_id": "bytes@2.3.0", "_shasum": "d5b680a165b6201739acb611542aabc2d8ceb070", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d5b680a165b6201739acb611542aabc2d8ceb070", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.3.0.tgz", "integrity": "sha512-G2l2Thxusl/3ntUPv6kkri/pTRN4iBelhHJRWldzTFxaCASnl5gB2+IWLDHIGfqu99qC9M+BE7cMOC+z99B4pA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD2GMowkzP8LsODxYl4/ojkr8SCn+dg2Dp0CNBslwRuiAIgKikjj14NtAbrSv7peblv/+mw51rfJ9exmzihkx9j504="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/bytes-2.3.0.tgz_1455595208428_0.5990735022351146"}, "directories": {}}, "2.4.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "2.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "https://github.com/visionmedia/bytes.js"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "devDependencies": {"mocha": "1.21.5"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "scripts": {"test": "mocha --check-leaks --reporter spec"}, "gitHead": "2a598442bdfa796df8d01a96cc54495cda550e70", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js", "_id": "bytes@2.4.0", "_shasum": "7d97196f9d5baf7f6935e25985549edd2a6c2339", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7d97196f9d5baf7f6935e25985549edd2a6c2339", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.4.0.tgz", "integrity": "sha512-SvUX8+c/Ga454a4fprIdIUzUN9xfd1YTvYh7ub5ZPJ+ZJ/+K2Bp6IpWGmnw8r3caLTsmhvJAKZz3qjIo9+XuCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRSI+Fm6POiCGBXkYKhLusRtkimKtLNSRI+fRdBjq41wIgScRZrPImrT8y5AuWXiJQ1cxj3PwH5KYWcZBPdmJm/3E="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/bytes-2.4.0.tgz_1464812473023_0.6271433881483972"}, "directories": {}}, "2.5.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "2.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "component": {"scripts": {"bytes/index.js": "index.js"}}, "devDependencies": {"mocha": "1.21.5", "nyc": "10.1.2"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "a4b9af2bf289175f12b3538eb172f2489844b1ec", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js#readme", "_id": "bytes@2.5.0", "_shasum": "4c9423ea2d252c270c41b2bdefeff9bb6b62c06a", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4c9423ea2d252c270c41b2bdefeff9bb6b62c06a", "tarball": "https://registry.npmjs.org/bytes/-/bytes-2.5.0.tgz", "integrity": "sha512-hkQtlCqf2f67v+GDlR9DImH1Bu/DxA/yNR7EmnbxCgxYgm4u7rLTJw8LYJdttHOl+H+++Fv0SQF7PgXAtqkfVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHEzcRFv1++m/9I4Pu8MnXt9uJB5AKWmWEgQH8wEtR8IAiA46uG4AKCn3XynnnWduftb6/8m3Z1OD1oqYdowdNRbWw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/bytes-2.5.0.tgz_1490416399283_0.2922299497295171"}, "directories": {}}, "3.0.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "devDependencies": {"mocha": "2.5.3", "nyc": "10.3.2"}, "files": ["History.md", "LICENSE", "Readme.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "25d4cb488aea3b637448a85fa297d9e65b4b4e04", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js#readme", "_id": "bytes@3.0.0", "_shasum": "d32815404d689699f85a4ea4fa8755dd13a96048", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d32815404d689699f85a4ea4fa8755dd13a96048", "tarball": "https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz", "integrity": "sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/Fi3tDm79ZgJWwOcOfn9AeW98b+KHElf0WkF0U0brNgIhALxt7v9i9V9BGKe9TJBIuUwHGFqme6dCt8l2L9vcb14c"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bytes-3.0.0.tgz_1504216364188_0.5158762519713491"}, "directories": {}}, "3.1.0": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "devDependencies": {"eslint": "5.12.1", "mocha": "5.2.0", "nyc": "13.1.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "49ac709cb210af60e35957c069bb2cd07f335cfd", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js#readme", "_id": "bytes@3.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zauLjrfCG+xvoyaqLoV8bLVXXNGC4JqlxFCutSDWA6fJrTo2ZuvLYTqZ7aHBLZSMOopbzwv8f+wZcVzfVTI2Dg==", "shasum": "f6cf7933a360e0588fa9fde85651cdc7f805d1f6", "tarball": "https://registry.npmjs.org/bytes/-/bytes-3.1.0.tgz", "fileCount": 5, "unpackedSize": 10997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR/D3CRA9TVsSAnZWagAAKxQP/jm6fmIJFjJzEpnOmOQu\n4tOWELz5luZxpItaWETqZxSAqm0cU4PiqWdkzVV7KJPgKROz6IgtqW4gvya1\nUcI1LlVSQ8zNlu0UiDLOL8yz/MKjeOEdDppglxHN7Dim+tvUVu0hF/4uhuOc\nhAG1ybdaijfeGN6uBM9P6TiqQUpT2AFuS4BUfZoAW4Gfq8fYUG5RW0KsicZB\nJ0IVRreG3KXu9BOoFa+PiGXX+LIG45yE7vMNXbWULyE5vnkBdJJK8L45lFPJ\nSHGKqYK/WIyFEnDBEQgXK9pDsbz9UvPRJFqRfrHyAkfifpTekhpKTNvaj+bC\nvuokTgppKHo8h3l3wFpXKO/Zb/UQFYH1N0dKFO+NSv4gR47Bez6O2Q0Y1ZdE\nHJmZRrFCRzr/m1VT3fZmDwDPJxjZ5kyATliI1lttyOInGlVJg+VR0XFrn0d4\nYOSKxgqTS5jIOPBNZgvt7lYGdBt9TnGk7VYMEdwHm9jfx4Hdj2aEhj3x5mss\nxlVX6q1+5Qg0Am5EWgRMYnODh9Q0oLH4Zaxi4lJr+hSp/6Ln+0IXPNcgT0lj\n+77CxeAxOQG6Aj5G6H5e45x2CGCavny5rsR133vCGBnHGuRcNOSvgZ3BmUvH\n2JB4YSQCAxX/Ku6phJepP0Ktv8gKMWohF+AM2b4oHlUnW4Ny6B7w7PMJoscM\nlFdb\r\n=oAEj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICBEKYkl8yYrf0e+6QxRU+y3f5R5Uy70zGrfY3QIWjXJAiEAkMc0g2s5Gd7wB6vD3To9fOIZqgQtcl5FjTr+uyouaIo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bytes_3.1.0_1548218614714_0.7781245590502732"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.1.3", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "a66d1b578f3e6fceb518f7e6a83827f7b2f17ac1", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js#readme", "_id": "bytes@3.1.1", "_nodeVersion": "16.7.0", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-dWe4nWO/ruEOY7HkUJ5gFt1DCFV9zPRoJr8pV0/ASQermOZjtq8jMjOprC0Kd10GLN+l7xaUPvxzJFWtxGu8Fg==", "shasum": "3f018291cb4cbad9accb6e6970bca9c8889e879a", "tarball": "https://registry.npmjs.org/bytes/-/bytes-3.1.1.tgz", "fileCount": 5, "unpackedSize": 12136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2lUOCRA9TVsSAnZWagAAHw8P/Rca6dTuXi60E+sJkqXw\nrqtLyoL0IDRqTU7Qj1jnHHdA79D8vZQJbR8B4056F0OlUlRo/Am4hfbQdfWR\nReNj6ajXv0+cUK6ttOg0vg/5TjaRKfWOh5l55aockoZcTVYOkG9fygXV2mJz\nCioEu1mw+V8hkF4sh5HQl8zp/SLfEPQhEJ/KXEJxTyGryyGC50OO/xilanBM\n1hHyZBXNCtXPYVLpOhjoIeQ/dPwmVZfAKxN4RWGbD5M4kRnHUpwFu3PJ5jpa\noW56S4mBfRoNQoiLwTB+szZzRtWzytMeP6PV/hiYOzGMjNpw7OrC1EqMFeZk\nWQ0x+eDD7px2W1trJQl6mhJNYDL+uzkBEpNAHdweyNWV1W2VNvEFuAfd8lFw\nUeEp+QKpxNY8eMSDJh0cjzbsFCtEy4ZrboYjkXJ+Dvzvp9ZSbAZL1TmOhqgR\nf35E9taJW/DiJ0g5LVlva2oWFpfiIFi1uycp3t7KmZPCs4RJcfOSAYvTDqU3\nU6AEvEa5Q/1SVmkyuz8QvWiyy9AVdm5ThDtqO5A3PbZFpWA8eHXFckqUhbAw\nC0GViq3QrcP4U8YNHLc1w8XBcFsspKzHVHuUWo2uPuZFPIctXsuQE+T5MjIM\nHfYqfNIRWo2VcGWnxVP8eCHiSw6UVOU+Y9N22lTbjqHTILQ9WNPNkscohdju\njz0S\r\n=BeFr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsUVFAHknQTScXP943jHi05/pY6N+VHbCx7ivNZHGm6AiEA3+c/nmd8wXI39YO2XRUy9HLpxPAgeyHixwOJKNEO2M4="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bytes_3.1.1_1637015004035_0.39905084333451346"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "bytes", "description": "Utility to parse a string bytes to bytes and vice-versa", "version": "3.1.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT", "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "gitHead": "9ddc13b6c66e0cb293616fba246e05db4b6cef4d", "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "homepage": "https://github.com/visionmedia/bytes.js#readme", "_id": "bytes@3.1.2", "_nodeVersion": "16.13.1", "_npmVersion": "8.1.2", "dist": {"integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "shasum": "8b0beeb98605adf1b128fa4386403c009e0221a5", "tarball": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "fileCount": 5, "unpackedSize": 12270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh83jtCRA9TVsSAnZWagAAe+wP/jWIZ72XE6ZWFU6ZFAAF\nTkIIEfiGukvMydg/O8gyR/5+9Z8tHnooZKM37TcPLTCHpAOOR6iDpUy2bawn\nvmNqqFVzihzDCRTGWpsg8tG48FFDylbKA0znBT8RM6P94vE0kshsqUnRtCiN\nb0s3OU4UW29TKShrp4y542Kbkoc3xQkdp47LqgWGghESWQiuxylYc4+xy6Yq\nw5O2MZ6THbG6dv25HuXS7vwHi2tcV6BRe39r35Fi0WHnvCOS9uEGvapP2TE6\nS+sgnr58h+bjfUY4SJ/kCmafvhVHVpTuPMGEhBO87nn031XGjqskrSD1pRxc\nqgcWE+VV1WJ//ADgQH4lUR+z4NHnFCLPVrfZsuZ06ul3Ig1lM6vBR8bWOzEQ\nPIwfmwQ/VsXFo/q5I3FZwJ+OH2AUYQ7vHo9Jf79mbJsoIju2Yf3GthU0aQra\nseIjwQSm+z+LN03Gu0fScMMxCg7hl9trZYTNdMnOH9QhXhrC9gugbqPpuqWq\n4Pw3Fh/ppCIWM5bpxcelA+jEAoxOUEKBMf0QQdYFEy9mpJNo0Y9TqwGAtIWh\n94Yl7Qpj9i69NfWZZt5hD+SdnrAtgXDQAG+FtSiB33MTXVvOZV6NoeOomJHO\n+X0YscYu+mYaq+qbS8jp3C++mP8as9k120osRye/EfOfwJ3Z/vAUn6UJI37L\n22G5\r\n=SIO7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICIXGWWKT58oV53CG9pCQQkHrfBUvxGaCO7z41FJPcNHAiBu4c8QL6bJm86CKCn4L0LI1gAjJEygBuFXBLxjnvcJkw=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bytes_3.1.2_1643346157520_0.6545144953034934"}, "_hasShrinkwrap": false}}, "readme": "# Bytes utility\n\n[![NPM Version][npm-image]][npm-url]\n[![NPM Downloads][downloads-image]][downloads-url]\n[![Build Status][ci-image]][ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\nUtility to parse a string bytes (ex: `1TB`) to bytes (`1099511627776`) and vice-versa.\n\n## Installation\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```bash\n$ npm install bytes\n```\n\n## Usage\n\n```js\nvar bytes = require('bytes');\n```\n\n#### bytes(number｜string value, [options]): number｜string｜null\n\nDefault export function. Delegates to either `bytes.format` or `bytes.parse` based on the type of `value`.\n\n**Arguments**\n\n| Name    | Type     | Description        |\n|---------|----------|--------------------|\n| value   | `number`｜`string` | Number value to format or string value to parse |\n| options | `Object` | Conversion options for `format` |\n\n**Returns**\n\n| Name    | Type             | Description                                     |\n|---------|------------------|-------------------------------------------------|\n| results | `string`｜`number`｜`null` | Return null upon error. Numeric value in bytes, or string value otherwise. |\n\n**Example**\n\n```js\nbytes(1024);\n// output: '1KB'\n\nbytes('1KB');\n// output: 1024\n```\n\n#### bytes.format(number value, [options]): string｜null\n\nFormat the given value in bytes into a string. If the value is negative, it is kept as such. If it is a float, it is\n rounded.\n\n**Arguments**\n\n| Name    | Type     | Description        |\n|---------|----------|--------------------|\n| value   | `number` | Value in bytes     |\n| options | `Object` | Conversion options |\n\n**Options**\n\n| Property          | Type   | Description                                                                             |\n|-------------------|--------|-----------------------------------------------------------------------------------------|\n| decimalPlaces | `number`｜`null` | Maximum number of decimal places to include in output. Default value to `2`. |\n| fixedDecimals | `boolean`｜`null` | Whether to always display the maximum number of decimal places. Default value to `false` |\n| thousandsSeparator | `string`｜`null` | Example of values: `' '`, `','` and `'.'`... Default value to `''`. |\n| unit | `string`｜`null` | The unit in which the result will be returned (B/KB/MB/GB/TB). Default value to `''` (which means auto detect). |\n| unitSeparator | `string`｜`null` | Separator to use between number and unit. Default value to `''`. |\n\n**Returns**\n\n| Name    | Type             | Description                                     |\n|---------|------------------|-------------------------------------------------|\n| results | `string`｜`null` | Return null upon error. String value otherwise. |\n\n**Example**\n\n```js\nbytes.format(1024);\n// output: '1KB'\n\nbytes.format(1000);\n// output: '1000B'\n\nbytes.format(1000, {thousandsSeparator: ' '});\n// output: '1 000B'\n\nbytes.format(1024 * 1.7, {decimalPlaces: 0});\n// output: '2KB'\n\nbytes.format(1024, {unitSeparator: ' '});\n// output: '1 KB'\n```\n\n#### bytes.parse(string｜number value): number｜null\n\nParse the string value into an integer in bytes. If no unit is given, or `value`\nis a number, it is assumed the value is in bytes.\n\nSupported units and abbreviations are as follows and are case-insensitive:\n\n  * `b` for bytes\n  * `kb` for kilobytes\n  * `mb` for megabytes\n  * `gb` for gigabytes\n  * `tb` for terabytes\n  * `pb` for petabytes\n\nThe units are in powers of two, not ten. This means 1kb = 1024b according to this parser.\n\n**Arguments**\n\n| Name          | Type   | Description        |\n|---------------|--------|--------------------|\n| value   | `string`｜`number` | String to parse, or number in bytes.   |\n\n**Returns**\n\n| Name    | Type        | Description             |\n|---------|-------------|-------------------------|\n| results | `number`｜`null` | Return null upon error. Value in bytes otherwise. |\n\n**Example**\n\n```js\nbytes.parse('1KB');\n// output: 1024\n\nbytes.parse('1024');\n// output: 1024\n\nbytes.parse(1024);\n// output: 1024\n```\n\n## License\n\n[MIT](LICENSE)\n\n[ci-image]: https://badgen.net/github/checks/visionmedia/bytes.js/master?label=ci\n[ci-url]: https://github.com/visionmedia/bytes.js/actions?query=workflow%3Aci\n[coveralls-image]: https://badgen.net/coveralls/c/github/visionmedia/bytes.js/master\n[coveralls-url]: https://coveralls.io/r/visionmedia/bytes.js?branch=master\n[downloads-image]: https://badgen.net/npm/dm/bytes\n[downloads-url]: https://npmjs.org/package/bytes\n[npm-image]: https://badgen.net/npm/v/bytes\n[npm-url]: https://npmjs.org/package/bytes\n", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:06:34.291Z", "created": "2012-06-11T15:44:32.849Z", "0.0.1": "2012-06-11T15:44:34.095Z", "0.1.0": "2012-08-17T19:26:03.899Z", "0.2.0": "2012-10-28T07:40:18.125Z", "0.2.1": "2013-04-01T18:48:09.764Z", "0.3.0": "2014-03-20T01:08:49.952Z", "1.0.0": "2014-05-05T23:52:20.318Z", "2.0.0": "2015-04-12T21:04:54.307Z", "2.0.1": "2015-05-08T04:43:03.189Z", "2.0.2": "2015-05-21T03:19:26.235Z", "2.1.0": "2015-05-22T02:55:58.116Z", "2.2.0": "2015-11-14T01:44:07.135Z", "2.3.0": "2016-02-16T04:00:12.239Z", "2.4.0": "2016-06-01T20:21:14.269Z", "2.5.0": "2017-03-25T04:33:21.150Z", "3.0.0": "2017-08-31T21:52:45.175Z", "3.1.0": "2019-01-23T04:43:34.815Z", "3.1.1": "2021-11-15T22:23:24.197Z", "3.1.2": "2022-01-28T05:02:37.661Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com"}, "users": {"m42am": true, "purywp": true, "cshao": true, "qqqppp9998": true, "honzajde": true, "fotooo": true, "moimikey": true, "dpanthula": true, "x4devs": true, "snowdream": true, "puranjayjain": true, "ta2edchimp": true, "wgerven": true, "seangenabe": true, "shaomingquan": true, "domjtalbot": true, "heartnett": true, "ahmed-dinar": true, "mlm": true, "shuoshubao": true, "yinfxs": true, "edwardxyt": true, "ccastelli": true, "luiscauro": true, "zhenguo.zhao": true, "frenchbread": true, "flumpus-dev": true}, "readmeFilename": "Readme.md", "homepage": "https://github.com/visionmedia/bytes.js#readme", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/bytes.js.git"}, "bugs": {"url": "https://github.com/visionmedia/bytes.js/issues"}, "keywords": ["byte", "bytes", "utility", "parse", "parser", "convert", "converter"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Théo FIDRY", "email": "<EMAIL>"}], "license": "MIT"}