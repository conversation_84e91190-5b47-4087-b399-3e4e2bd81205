{"_id": "assert-plus", "_rev": "66-e77c30193c9be6c586a0ca9c637ed691", "name": "assert-plus", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.1.0": {"name": "assert-plus", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "assert-plus@0.1.0", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}], "dist": {"shasum": "386c2c8452ebdfcf8f1f722fdea548c7bf874f9c", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.0.tgz", "integrity": "sha512-dpSjrqWF0+UoVeT8RUg7XgRjPJKp/A3Ki+sh1oRPaZgiB/wDsX48m5rtoy7m6j8a1lZ26uRbVz9Bxo6Jo9uOKw==", "signatures": [{"sig": "MEYCIQCxfJSD8PHbo3iVKHOYQR/Ez/mdh/uNq9m8C4S8rzJj4gIhAO0GMMJw7vS5uY4dPZ2NmNKgp6gO29wNgdZv9V0o9ZBy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "engines": {"node": ">=0.6"}, "_npmUser": {"name": "mcavage", "email": "<EMAIL>"}, "_npmVersion": "1.1.21", "description": "Extra assertions on top of node's assert module", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.1.1": {"name": "assert-plus", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "assert-plus@0.1.1", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}], "dist": {"shasum": "b070bfb80440b5f0812195e1b3395c8744c063a3", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.1.tgz", "integrity": "sha512-Pbe3jXYMjgzV/Jq+WTRGemDgm1fYIffHNdli2k9JSE6XjSgtdNbp+Ab5nM65UHZ7FuyrLZe3ApR1dnIDUo/ppw==", "signatures": [{"sig": "MEQCIBrg8f3BvMEjKeavuNNP1TAKE0BegzGsqdb6K2VuLRTsAiA2Nww8ktD4C9tJ2Z22BYBfvdK90d9kpEA/2aVp+xm5MA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "engines": {"node": ">=0.6"}, "description": "Extra assertions on top of node's assert module", "directories": {}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "0.1.2": {"name": "assert-plus", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "assert-plus@0.1.2", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}], "dist": {"shasum": "d93ffdbb67ac5507779be316a7d65146417beef8", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.2.tgz", "integrity": "sha512-BbJV8Hq6grYTokkHi/qKS34kfYIFYpu4wKd/H0dARsa6qOqEFH1wboxMwrccAmFjyRjkemjElaVC/sZSUMxHnA==", "signatures": [{"sig": "MEQCIAb+yNSYdoTV5z5TN+xFNEVEtaCasZmybMxjFFmMGIQ1AiAL4mT0qQQV6syY390SCX12RAX0r4ahGCB+BYLF4GsJmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "engines": {"node": ">=0.6"}, "_npmUser": {"name": "mcavage", "email": "<EMAIL>"}, "_npmVersion": "1.1.59", "description": "Extra assertions on top of node's assert module", "directories": {}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "0.1.3": {"name": "assert-plus", "version": "0.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "assert-plus@0.1.3", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}], "dist": {"shasum": "32eba8ac83e50ae4f4b5babab1ae9aa0edec9fef", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.3.tgz", "integrity": "sha512-1ZRZiZWBBgkI54m2YnNnNIeoynYpPN84XHpHAnPoEzZkrqwNROMQIhOwuEZ1ESsjAc3eXQ/OAmrFDGcVvZbqNA==", "signatures": [{"sig": "MEQCIFlCtjbFMfvVRdA4YaEILwYM7cmuPFkqwu9a+rc3SionAiBLbbQeSmi5uV0mWlqylipBA3SUoAu5XfeNQtYKr33s4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "_from": ".", "engines": {"node": ">=0.8"}, "_npmUser": {"name": "mcavage", "email": "<EMAIL>"}, "_npmVersion": "1.2.18", "description": "Extra assertions on top of node's assert module", "directories": {}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "0.1.4": {"name": "assert-plus", "version": "0.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "assert-plus@0.1.4", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}], "dist": {"shasum": "283eff8b140ecd768529fbf3730a4c09ebec61f7", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.4.tgz", "integrity": "sha512-hS8i9Ke+BHuDcAumCtW4wZrFqaVdjv7kgPqXSbP2B7dntTmsTB+AJ4TLQhSkqDm5CfEeWKq/h3n3GDffu+sipg==", "signatures": [{"sig": "MEUCICoglX4NX3MTzsY3kZ0sNGezMEjwT0FzvkvOK/9xN5q9AiEA8Qrox7BrXj0QX9MjwH4+028MtYVXmpZ2+bYrGyOQBN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "_from": ".", "engines": {"node": ">=0.8"}, "_npmUser": {"name": "mcavage", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mcavage/node-assert-plus.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Extra assertions on top of node's assert module", "directories": {}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "0.1.5": {"name": "assert-plus", "version": "0.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "_id": "assert-plus@0.1.5", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "dist": {"shasum": "ee74009413002d84cec7219c6ac811812e723160", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.5.tgz", "integrity": "sha512-brU24g7ryhRwGCI2y+1dGQmQXiZF7TtIj583S96y0jjdajIe6wn8BuXyELYhvD22dtIxDQVFk04YTJwwdwOYJw==", "signatures": [{"sig": "MEYCIQCLdo/z8k0+S43aAGBmcBs9Ms01QfKSXJ/Bgp4E8XKA2QIhAJKaFwRdJR6xafVCtnEwR23+sKWhKz3oiXtEOAu/csNa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "_from": ".", "engines": {"node": ">=0.8"}, "_npmUser": {"name": "mcavage", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mcavage/node-assert-plus.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Extra assertions on top of node's assert module", "directories": {}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "0.2.0": {"name": "assert-plus", "version": "0.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "assert-plus@0.2.0", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}, {"name": "pfmooney", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mcavage/node-assert-plus#readme", "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "dist": {"shasum": "d74e1b87e7affc0db8aadb7021f3fe48101ab234", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "integrity": "sha512-u1L0ZLywRziOVjUhRxI0Qg9G+4RnFB9H/Rq40YWn0dieDgO7vAYeJz6jKAO6t/aruzlDFLAPkQTT87e+f8Imaw==", "signatures": [{"sig": "MEYCIQCwKUBmv9LO9YbpD6fgIGg0s3pZscqI6Ak3Be6o1NMOsQIhAL8oLWRmoCCEp1Yd9MF3Ws+j1JAeWMOrQVdfeffnv9rf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "_from": "assert-plus-0.2.0.tgz", "_shasum": "d74e1b87e7affc0db8aadb7021f3fe48101ab234", "engines": {"node": ">=0.8"}, "scripts": {"test": "tape tests/*.js | ./node_modules/.bin/faucet"}, "_npmUser": {"name": "pfmooney", "email": "<EMAIL>"}, "_resolved": "file:assert-plus-0.2.0.tgz", "repository": {"url": "git+https://github.com/mcavage/node-assert-plus.git", "type": "git"}, "_npmVersion": "3.3.8", "description": "Extra assertions on top of node's assert module", "directories": {}, "_nodeVersion": "0.10.36", "dependencies": {}, "devDependencies": {"tape": "4.2.2", "faucet": "0.0.1"}, "optionalDependencies": {}}, "1.0.0": {"name": "assert-plus", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "assert-plus@1.0.0", "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}, {"name": "pfmooney", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mcavage/node-assert-plus#readme", "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "dist": {"shasum": "f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "signatures": [{"sig": "MEQCID/TqhiA2b4H7rkc6tCDusmu4VSS3xZ0CYxf35pQKJ+sAiA205F5ly8ZJRIyCQmXkt1XDn/2XKo8Wj4uLLJHiBHRfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./assert.js", "_from": "assert-plus-1.0.0.tgz", "_shasum": "f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525", "engines": {"node": ">=0.8"}, "scripts": {"test": "tape tests/*.js | ./node_modules/.bin/faucet"}, "_npmUser": {"name": "pfmooney", "email": "<EMAIL>"}, "_resolved": "file:assert-plus-1.0.0.tgz", "repository": {"url": "git+https://github.com/mcavage/node-assert-plus.git", "type": "git"}, "_npmVersion": "3.3.9", "description": "Extra assertions on top of node's assert module", "directories": {}, "_nodeVersion": "0.10.40", "dependencies": {}, "devDependencies": {"tape": "4.2.2", "faucet": "0.0.1"}, "optionalDependencies": {}}}, "time": {"created": "2012-06-23T23:15:09.466Z", "modified": "2025-02-07T15:28:21.789Z", "0.1.0": "2012-06-23T23:15:10.890Z", "0.1.1": "2012-09-11T17:23:02.814Z", "0.1.2": "2012-09-18T21:45:30.157Z", "0.1.3": "2013-05-21T21:40:02.279Z", "0.1.4": "2013-06-27T17:20:20.703Z", "0.1.5": "2013-11-25T17:23:47.053Z", "0.2.0": "2015-11-10T07:51:02.234Z", "1.0.0": "2016-01-27T19:03:38.091Z"}, "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/mcavage/node-assert-plus#readme", "repository": {"url": "git+https://github.com/mcavage/node-assert-plus.git", "type": "git"}, "description": "Extra assertions on top of node's assert module", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "pfmooney"}, {"email": "<EMAIL>", "name": "mcavage"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "readme": "# assert-plus\n\nThis library is a super small wrapper over node's assert module that has two\nthings: (1) the ability to disable assertions with the environment variable\nNODE\\_NDEBUG, and (2) some API wrappers for argument testing.  Like\n`assert.string(myArg, 'myArg')`.  As a simple example, most of my code looks\nlike this:\n\n```javascript\n    var assert = require('assert-plus');\n\n    function fooAccount(options, callback) {\n        assert.object(options, 'options');\n        assert.number(options.id, 'options.id');\n        assert.bool(options.isManager, 'options.isManager');\n        assert.string(options.name, 'options.name');\n        assert.arrayOfString(options.email, 'options.email');\n        assert.func(callback, 'callback');\n\n        // Do stuff\n        callback(null, {});\n    }\n```\n\n# API\n\nAll methods that *aren't* part of node's core assert API are simply assumed to\ntake an argument, and then a string 'name' that's not a message; `AssertionError`\nwill be thrown if the assertion fails with a message like:\n\n    AssertionError: foo (string) is required\n    at test (/home/<USER>/work/foo/foo.js:3:9)\n    at Object.<anonymous> (/home/<USER>/work/foo/foo.js:15:1)\n    at Module._compile (module.js:446:26)\n    at Object..js (module.js:464:10)\n    at Module.load (module.js:353:31)\n    at Function._load (module.js:311:12)\n    at Array.0 (module.js:484:10)\n    at EventEmitter._tickCallback (node.js:190:38)\n\nfrom:\n\n```javascript\n    function test(foo) {\n        assert.string(foo, 'foo');\n    }\n```\n\nThere you go.  You can check that arrays are of a homogeneous type with `Arrayof$Type`:\n\n```javascript\n    function test(foo) {\n        assert.arrayOfString(foo, 'foo');\n    }\n```\n\nYou can assert IFF an argument is not `undefined` (i.e., an optional arg):\n\n```javascript\n    assert.optionalString(foo, 'foo');\n```\n\nLastly, you can opt-out of assertion checking altogether by setting the\nenvironment variable `NODE_NDEBUG=1`.  This is pseudo-useful if you have\nlots of assertions, and don't want to pay `typeof ()` taxes to v8 in\nproduction.  Be advised:  The standard functions re-exported from `assert` are\nalso disabled in assert-plus if NDEBUG is specified.  Using them directly from\nthe `assert` module avoids this behavior.\n\nThe complete list of APIs is:\n\n* assert.array\n* assert.bool\n* assert.buffer\n* assert.func\n* assert.number\n* assert.finite\n* assert.object\n* assert.string\n* assert.stream\n* assert.date\n* assert.regexp\n* assert.uuid\n* assert.arrayOfArray\n* assert.arrayOfBool\n* assert.arrayOfBuffer\n* assert.arrayOfFunc\n* assert.arrayOfNumber\n* assert.arrayOfFinite\n* assert.arrayOfObject\n* assert.arrayOfString\n* assert.arrayOfStream\n* assert.arrayOfDate\n* assert.arrayOfRegexp\n* assert.arrayOfUuid\n* assert.optionalArray\n* assert.optionalBool\n* assert.optionalBuffer\n* assert.optionalFunc\n* assert.optionalNumber\n* assert.optionalFinite\n* assert.optionalObject\n* assert.optionalString\n* assert.optionalStream\n* assert.optionalDate\n* assert.optionalRegexp\n* assert.optionalUuid\n* assert.optionalArrayOfArray\n* assert.optionalArrayOfBool\n* assert.optionalArrayOfBuffer\n* assert.optionalArrayOfFunc\n* assert.optionalArrayOfNumber\n* assert.optionalArrayOfFinite\n* assert.optionalArrayOfObject\n* assert.optionalArrayOfString\n* assert.optionalArrayOfStream\n* assert.optionalArrayOfDate\n* assert.optionalArrayOfRegexp\n* assert.optionalArrayOfUuid\n* assert.AssertionError\n* assert.fail\n* assert.ok\n* assert.equal\n* assert.notEqual\n* assert.deepEqual\n* assert.notDeepEqual\n* assert.strictEqual\n* assert.notStrictEqual\n* assert.throws\n* assert.doesNotThrow\n* assert.ifError\n\n# Installation\n\n    npm install assert-plus\n\n## License\n\nThe MIT License (MIT)\nCopyright (c) 2012 Mark Cavage\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n## Bugs\n\nSee <https://github.com/mcavage/node-assert-plus/issues>.\n", "readmeFilename": "README.md", "users": {"dnero": true, "irnnr": true, "jeltok": true, "kkuehl": true, "barenko": true, "chmanie": true, "kontrax": true, "hellboy81": true, "xiechao06": true, "blackwhite": true, "qqqppp9998": true, "benhutchins": true, "vparaskevas": true, "xinwangwang": true, "zhenguo.zhao": true, "caiorcferreira": true}}