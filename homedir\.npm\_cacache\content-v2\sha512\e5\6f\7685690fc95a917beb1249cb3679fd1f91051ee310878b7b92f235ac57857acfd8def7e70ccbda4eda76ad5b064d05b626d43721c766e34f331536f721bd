{"name": "getpass", "dist-tags": {"latest": "0.1.7"}, "versions": {"0.1.0": {"name": "getpass", "version": "0.1.0", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "2896de9fc14d5b2cc6833c6d3a601326a37eed9b", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.0.tgz", "integrity": "sha512-jAd/+NE/zUMDjEYh+k2r5I3zMc8s8VIzPAEsMTqMiEaD3L4TQrMGNWuQ7LbxnsrQMawehXLX5xKKZs4HrlM5xw==", "signatures": [{"sig": "MEYCIQCgprTg91TBpOitZE1BrHpdwhnTMDzWrZR0IS8tjaOXhgIhAMWHPBHFvFyjr14c+h3ILJs61EQg3NrhrhdFmavkwMkN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "getpass", "version": "0.1.1", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "fe4c1462c7eb72a0074d882769bc915f2051bc47", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.1.tgz", "integrity": "sha512-gY9OjCLSjaLu0Yq++uCvtTlyUoaVLlyz7eTpmJTxTRMDaVDupyIqhb1gduaNwEsJv56OtZkm0Ecp+f05jU4T1w==", "signatures": [{"sig": "MEUCIQCO1v2fj9HJmVoP16pvpPbrZBYre+EdIy0WDC5xuMEGiAIgFrRF7icEfo4hI1BwDEfG2xN/KxVebgoxmxjvLCykm7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.2": {"name": "getpass", "version": "0.1.2", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "dbcc91a1143267bbcd18d061bf889a51182778aa", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.2.tgz", "integrity": "sha512-HkPiKsTNNsboUQNP92qtrYBWBPaaTOxeIkChqNcxjEGY3kD3Htonm6eGWIXzCWWJJPS2IjSTq/VgwB1g8ow81g==", "signatures": [{"sig": "MEYCIQCUUoi4/PCcdFIrPbq5tX3GQfmJDsJExYCKLbpSQzt3HQIhAK3zaqzBaCqQsUn6s/Z94Lbo+YJ2YXPlVKv90ddkTG8+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.3": {"name": "getpass", "version": "0.1.3", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "49c085a288f8b1be109d4f4a5ebac839c579fafb", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.3.tgz", "integrity": "sha512-MyTteTHYXLsoHCSo8x8xav73MN/Xs+qCF52J+DSkpA8nFyFrUsl/wrFA3d5/Jsa/3iVDG/467UcBoIoNT7gs+g==", "signatures": [{"sig": "MEUCIF+f085UAKZzb6Ivq5OWXTZ4hUVKLzBaRVYGjyicod+lAiEAwUwIsCL82fN2HfqnbjnsWTbNFkrKD8tpbwT3MS/QPSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.4": {"name": "getpass", "version": "0.1.4", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "c627e40b359a1db8d0a4ff0a7c218870e011a04d", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.4.tgz", "integrity": "sha512-EFHNzMP/K6eV8HZSMzUGwOAd8l/jUC+5SG/2UTmOfAWldSDSWrdyccddlnv7hydNFLg2NgL1yePcckUDtYspWw==", "signatures": [{"sig": "MEUCIQDXLyPp/XmsrI+6RICMu5l1WryLze8ZWJFAw8P64hyp+gIgRTSWidfmeTleWx4/n809Y+3b8oCiGP6g6Sa/GpkXn0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.5": {"name": "getpass", "version": "0.1.5", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "a20d5f2a8fc83f11db0d591d0487469407e6be14", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.5.tgz", "integrity": "sha512-LyhF74N4wi6zY+/VPSGAXWSOJ8E8P7oXQhNoLMPTbhhP+iR4erBhiar0cdVcL/PyBTOuly+h6lerlFsCPaDYwA==", "signatures": [{"sig": "MEUCIF6jqu1c1kO1rfJzrb22rjLo2NGNrdhackl86xlAffZkAiEAswMoI6/7W/tytNZndt6nsFx3OpPvqGuAw6F9yNcIg2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.6": {"name": "getpass", "version": "0.1.6", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "dist": {"shasum": "283ffd9fc1256840875311c1b60e8c40187110e6", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.6.tgz", "integrity": "sha512-Uj295v1VGRPhKEty7IiEzGYf2rAIEbcGQ8dxK5QrQuwP7tCW8ftD5o8FUsGW4MLdws4P3eKRBzo+mFySYYcimA==", "signatures": [{"sig": "MEYCIQCtfNYB8RxlW3FzpfzmoitRrM43R7XAybuRPh4cdIlWpgIhALMrT1Fmv+4mLgX8DX+65/u4n7QbjnyVKzr50fPXeCIQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.7": {"name": "getpass", "version": "0.1.7", "dependencies": {"assert-plus": "^1.0.0"}, "dist": {"shasum": "5eff8e3e684d569ae4cb2b1282604e8ba62149fa", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "signatures": [{"sig": "MEYCIQDnhcTUutrTk3UyzzW3QuLFaKQBPE5MVEd6/puNqrHx6wIhAOtCAmw/aE5ENXCoCYN+jM1Mj/apoxT2SuzpQSHDkUnr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}}, "modified": "2025-02-07T15:28:03.000Z"}