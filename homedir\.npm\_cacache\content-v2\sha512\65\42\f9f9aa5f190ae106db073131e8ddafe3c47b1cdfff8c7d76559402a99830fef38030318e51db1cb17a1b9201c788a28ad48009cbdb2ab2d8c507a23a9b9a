{"name": "asynckit", "dist-tags": {"latest": "0.4.0"}, "versions": {"0.1.0": {"name": "asynckit", "version": "0.1.0", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dist": {"shasum": "328b62aa8bb99bff494e25379a19861e01572a4e", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.1.0.tgz", "integrity": "sha512-H2szEj/NE/S1CHUTalFXa0rROTQ75dcLSxt4fg1zP8hzvumVdU1myGjwwonhhYxNlm0IKGDX1DC1rbZDOTgyzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCp6DthqVastRAajzoCK+Ym4i+3teC3ounrbNN4YUKvRgIhAPPRWUoB5NvmCYlVYzEyI8qhp1rK6GdpF52OTsjphKdP"}]}}, "0.2.0": {"name": "asynckit", "version": "0.2.0", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dist": {"shasum": "449693b00700b64babfb12f81be4c9919e4deb4e", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.2.0.tgz", "integrity": "sha512-0M7mjypF9Y4iT1EE+Z2Ysk+yT2HaU6e5lH4Ii72gQYarafrQW7oV5DFAB61rxVmkjTv0Rc85lZiYDFCjBBOY8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE8IiW1uJP61sWZZVn8CizEvJXBeAFT7n8l2VUTs98rvAiEAzOJs3uwelYUMBXl+FUD+Z8iYH2fVSb1OGHXAXh64t7U="}]}}, "0.3.0": {"name": "asynckit", "version": "0.3.0", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dist": {"shasum": "bde42eec27d7888fef99f7427e63c2eb624c2903", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.3.0.tgz", "integrity": "sha512-qCxikyv0Ny1dG4uriCSpP0fev60kCazWJQyM7UyZ1njF5XuvE92konkM8YA+/y5ONBy1Di4VD3MX0uG8UQvKhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICaanWgtDwvKuU+qCKTR3Ail+/YRI/xW+VztiVKplyh2AiEA/9429K304tv3FY+ZtH7ALX7nN+cskSla24AMZMS0WTk="}]}}, "0.4.0": {"name": "asynckit", "version": "0.4.0", "devDependencies": {"browserify": "^13.0.0", "browserify-istanbul": "^2.0.0", "coveralls": "^2.11.9", "eslint": "^2.9.0", "istanbul": "^0.4.3", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.7", "pre-commit": "^1.1.3", "reamde": "^1.1.0", "rimraf": "^2.5.2", "size-table": "^0.2.0", "tap-spec": "^4.1.1", "tape": "^4.5.1"}, "dist": {"shasum": "c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79", "tarball": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbge6e7VDP+ilAARDSShG7w73q75+pQRdohpUXBDv2CAiB+LKgQl5A0S/jAElUxESEQ0eOsIiqsMLQ+vLPlsFj23A=="}]}}}, "modified": "2022-06-13T03:39:04.302Z"}