{"_id": "encodeurl", "_rev": "16-a03f8395b510433df186daee7797120d", "name": "encodeurl", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "encodeurl", "version": "1.0.0", "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "_id": "encodeurl@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/encodeurl", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "dist": {"shasum": "7cfb78e36a241593379e2ebad3926783dc18e058", "tarball": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.0.tgz", "integrity": "sha512-t7dshuwL0l+kfCkwsQufpTKS2QpamJxUAxvdo+icEyV76RY/BhaD57Cla6WJC1Mz9IYOH3mhen6fkKLsKR3i8g==", "signatures": [{"sig": "MEQCICyABqpPt4hfx3fn3M7tnJkT73DR2s0t7dmJu6ZnysyKAiABuh+UpHoyWFCg1MxnB8WyTcGRWs2fPMoR5+t9sK3mjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "7cfb78e36a241593379e2ebad3926783dc18e058", "engines": {"node": ">= 0.8"}, "gitHead": "b3f2a3a74af20c4b27f0466782d80fb0685feb47", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/pillarjs/encodeurl", "type": "git"}, "_npmVersion": "1.4.28", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "directories": {}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/encodeurl-1.0.0.tgz_1465412322161_0.9512871925253421", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.1": {"name": "encodeurl", "version": "1.0.1", "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "_id": "encodeurl@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/encodeurl#readme", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "dist": {"shasum": "79e3d58655346909fe6f0f45a5de68103b294d20", "tarball": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.1.tgz", "integrity": "sha512-Emsft8lNRSZ7+fFm2KgTM8OZPcfHip/hNMSkje83n+LqPx5tI4xkCxyunJIG3EZsWHz9sqzohiPR6monRXWD8g==", "signatures": [{"sig": "MEUCIC+1JQNva4PG/Yd7Hun0ETOL3Bs2U9ozuiFqQsCo5UeTAiEAtMd8khtEYg95K7dXUYK7TeUEL9gdbJJBtOlh0/vH8Jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "79e3d58655346909fe6f0f45a5de68103b294d20", "engines": {"node": ">= 0.8"}, "gitHead": "39ed0c235fed4cea7d012038fd6bb0480561d226", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/encodeurl.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "directories": {}, "_nodeVersion": "4.4.3", "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/encodeurl-1.0.1.tgz_1465519736251_0.09314409433864057", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "encodeurl", "version": "1.0.2", "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "_id": "encodeurl@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/encodeurl#readme", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "dist": {"shasum": "ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59", "tarball": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==", "signatures": [{"sig": "MEQCIEZEF05/h8gAqiJeucYt7VIMp4ayOLGGiaSmLeIMoTJaAiBsN6tnzH2TNdHb1t534W4ruVnBCH4JYlrPwgiZ6g+doA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "_shasum": "ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59", "engines": {"node": ">= 0.8"}, "gitHead": "1a7301e330bf20fd7c8c173102315e45cd1f5d1e", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/encodeurl.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "directories": {}, "_nodeVersion": "6.11.1", "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/encodeurl-1.0.2.tgz_1516591169672_0.5424360500182956", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "encodeurl", "version": "2.0.0", "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "_id": "encodeurl@2.0.0", "maintainers": [{"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/pillarjs/encodeurl#readme", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "dist": {"shasum": "7b8ea898077d7e409d3ac45474ea38eaf0857a58", "tarball": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "signatures": [{"sig": "MEUCIBOFos/fjObZakhslwkYNCRgy3lxta+k4AJdNDf+lDfaAiEArFwkiXZR7H08jnrMnVmch2fkcCs8hSToYkixEEgpsVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6980}, "engines": {"node": ">= 0.8"}, "gitHead": "5f2205efe622fc7972c4e45ed7cbe7f8d652c299", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "b<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/pillarjs/encodeurl.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "directories": {}, "_nodeVersion": "21.0.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "2.5.3", "eslint": "5.11.1", "istanbul": "0.4.5", "eslint-plugin-node": "7.0.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-promise": "4.0.1", "eslint-config-standard": "12.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/encodeurl_2.0.0_1711670621995_0.1569270866269692", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2016-06-08T18:58:44.662Z", "modified": "2024-05-30T18:11:31.473Z", "1.0.0": "2016-06-08T18:58:44.662Z", "1.0.1": "2016-06-10T00:48:58.829Z", "1.0.2": "2018-01-22T03:19:29.733Z", "2.0.0": "2024-03-29T00:03:42.135Z"}, "maintainers": [{"email": "<EMAIL>", "name": "ulisesgascon"}, {"email": "<EMAIL>", "name": "b<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "repository": {"url": "git+https://github.com/pillarjs/encodeurl.git", "type": "git"}, "keywords": ["encode", "encodeurl", "url"], "license": "MIT", "homepage": "https://github.com/pillarjs/encodeurl#readme", "bugs": {"url": "https://github.com/pillarjs/encodeurl/issues"}, "readme": "# Encode URL\n\nEncode a URL to a percent-encoded form, excluding already-encoded sequences.\n\n## Installation\n\n```sh\nnpm install encodeurl\n```\n\n## API\n\n```js\nvar encodeUrl = require('encodeurl')\n```\n\n### encodeUrl(url)\n\nEncode a URL to a percent-encoded form, excluding already-encoded sequences.\n\nThis function accepts a URL and encodes all the non-URL code points (as UTF-8 byte sequences). It will not encode the \"%\" character unless it is not part of a valid sequence (`%20` will be left as-is, but `%foo` will be encoded as `%25foo`).\n\nThis encode is meant to be \"safe\" and does not throw errors. It will try as hard as it can to properly encode the given URL, including replacing any raw, unpaired surrogate pairs with the Unicode replacement character prior to encoding.\n\n## Examples\n\n### Encode a URL containing user-controlled data\n\n```js\nvar encodeUrl = require('encodeurl')\nvar escapeHtml = require('escape-html')\n\nhttp.createServer(function onRequest (req, res) {\n  // get encoded form of inbound url\n  var url = encodeUrl(req.url)\n\n  // create html message\n  var body = '<p>Location ' + escapeHtml(url) + ' not found</p>'\n\n  // send a 404\n  res.statusCode = 404\n  res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n  res.setHeader('Content-Length', String(Buffer.byteLength(body, 'utf-8')))\n  res.end(body, 'utf-8')\n})\n```\n\n### Encode a URL for use in a header field\n\n```js\nvar encodeUrl = require('encodeurl')\nvar escapeHtml = require('escape-html')\nvar url = require('url')\n\nhttp.createServer(function onRequest (req, res) {\n  // parse inbound url\n  var href = url.parse(req)\n\n  // set new host for redirect\n  href.host = 'localhost'\n  href.protocol = 'https:'\n  href.slashes = true\n\n  // create location header\n  var location = encodeUrl(url.format(href))\n\n  // create html message\n  var body = '<p>Redirecting to new site: ' + escapeHtml(location) + '</p>'\n\n  // send a 301\n  res.statusCode = 301\n  res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n  res.setHeader('Content-Length', String(Buffer.byteLength(body, 'utf-8')))\n  res.setHeader('Location', location)\n  res.end(body, 'utf-8')\n})\n```\n\n## Similarities\n\nThis function is _similar_ to the intrinsic function `encodeURI`. However, it will not encode:\n\n* The `\\`, `^`, or `|` characters\n* The `%` character when it's part of a valid sequence\n* `[` and `]` (for IPv6 hostnames)\n* Replaces raw, unpaired surrogate pairs with the Unicode replacement character\n\nAs a result, the encoding aligns closely with the behavior in the [WHATWG URL specification][whatwg-url]. However, this package only encodes strings and does not do any URL parsing or formatting.\n\nIt is expected that any output from `new URL(url)` will not change when used with this package, as the output has already been encoded. Additionally, if we were to encode before `new URL(url)`, we do not expect the before and after encoded formats to be parsed any differently.\n\n## Testing\n\n```sh\n$ npm test\n$ npm run lint\n```\n\n## References\n\n- [RFC 3986: Uniform Resource Identifier (URI): Generic Syntax][rfc-3986]\n- [WHATWG URL Living Standard][whatwg-url]\n\n[rfc-3986]: https://tools.ietf.org/html/rfc3986\n[whatwg-url]: https://url.spec.whatwg.org/\n\n## License\n\n[MIT](LICENSE)\n", "readmeFilename": "README.md", "users": {"antixrist": true, "mojaray2k": true, "mobeicaoyuan": true, "icodeforcookies": true}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}]}