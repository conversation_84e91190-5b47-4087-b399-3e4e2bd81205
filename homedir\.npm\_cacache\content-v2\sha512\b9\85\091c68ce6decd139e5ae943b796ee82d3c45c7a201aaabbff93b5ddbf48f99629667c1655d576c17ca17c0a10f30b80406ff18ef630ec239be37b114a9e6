{"_id": "to-regex-range", "_rev": "25-fe0cfa69c3914321080c1f8f4d7b5052", "name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "dist-tags": {"latest": "5.0.1"}, "versions": {"0.1.0": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max. Useful for creating regular expressions to validate numbers, ranges, years, etc.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^2.0.2", "repeat-string": "^1.5.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "sh"], "verb": {"related": {"list": ["fill-range", "expand-range", "micromatch", "repeat-element", "repeat-string"]}}, "gitHead": "e9033fa99268989821413d7301b70c2f62da9b99", "_id": "to-regex-range@0.1.0", "_shasum": "1332e39727b43e2767c6ec26c7df880db3db11aa", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1332e39727b43e2767c6ec26c7df880db3db11aa", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-0.1.0.tgz", "integrity": "sha512-2E0Man7t9dOh4fe8x5cPqonPBDNlhnnpeHlu+U5vWZ68j6fa6BewBv/l2MbrfL5FT2oTdIpf63TOwUVkkqxP5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFnFRfP9NSlRefqxQ7qnheF2pFqDEBtaozy2P1fKfmTYAiEAukpcuEh3fiV5k0qrE+4+UAfWBbJJyCIdsSOt6Qh6BSY="}]}, "directories": {}}, "0.1.1": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max. Useful for creating regular expressions to validate numbers, ranges, years, etc. Returns a string, allowing the returned value to be used in regular expressions generated by other libraries.", "version": "0.1.1", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^2.0.2", "repeat-string": "^1.5.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "sh"], "verb": {"related": {"list": ["fill-range", "expand-range", "micromatch", "repeat-element", "repeat-string"]}}, "gitHead": "353d4020a22a91ac079615d46909adfaa2d058f1", "_id": "to-regex-range@0.1.1", "_shasum": "c899f37ca02d1aa33611835000c45b5e46e5cfb0", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c899f37ca02d1aa33611835000c45b5e46e5cfb0", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-0.1.1.tgz", "integrity": "sha512-E+P16hr5OR/RWLmViRYgv2Vccn0Cr6jtZsmCB/pW4bUKUpJNKAklDaIAxJ5vEFj/RdAlI0st2Z/iOABMD1kpyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICDl+zlBDxZKAjrLX3rB2MD4cxkT8koJPW6j3b/J5KZUAiEAuqIey2kExlKDKgXat4v7TOa2AdIG+2rRgohqW0Oxmrc="}]}, "directories": {}}, "0.1.2": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max. Useful for creating regular expressions to validate numbers, ranges, years, etc. Returns a string, allowing the returned value to be used in regular expressions generated by other libraries.", "version": "0.1.2", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^2.1.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5", "should": "^8.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "sh"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "353d4020a22a91ac079615d46909adfaa2d058f1", "_id": "to-regex-range@0.1.2", "_shasum": "258539fa4984ba79847b1fb4294a53487e8d15e9", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.5.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "258539fa4984ba79847b1fb4294a53487e8d15e9", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-0.1.2.tgz", "integrity": "sha512-80jFwVvXAwvUWIuqTwJ6EUl76t4rYXecAh2nr6t2S/sS8bUpUx4w530T9kU2SCjJlg+m964kXaO3FmcCmGKYrw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAkUpdPmWV/p00SrYF755Sd8aZgJ01bWgGmrxfE3iZkMAiBuvGdOyzC2NVEsgOhugcpALSxINy22XXu4V8EfoCK4xQ=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/to-regex-range-0.1.2.tgz_1461741100056_0.09246146096847951"}, "directories": {}}, "0.1.3": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max, with 855,412 generated unit tests to validate it's accuracy! Useful for creating regular expressions to validate numbers, ranges, years, etc. Returns a string, allowing the returned value to ", "version": "0.1.3", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^2.1.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5", "should": "^8.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "7dc1bb25f8bf0b94cec120de917a8d67c9c6213d", "_id": "to-regex-range@0.1.3", "_shasum": "2dfef8fc4c46586a98739774c2e66e1fd5a24a58", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "2dfef8fc4c46586a98739774c2e66e1fd5a24a58", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-0.1.3.tgz", "integrity": "sha512-sExxxnGd/rnFfNtLzJJS1O64gLSoptQlZnDL582lfNpsJmnMgTsfMhlMPhZyA3Ce/McvDi90PrQUkS+NyXFKXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5tK0X5pTVdnDKzZoRb4c+dIAC3fTWPU4rr468I9mCggIgUnfhqJ8HHMW1vqN4Fn2Soaieqj6foBcItZ+M/N/6GzI="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/to-regex-range-0.1.3.tgz_1473860813552_0.9383346708491445"}, "directories": {}}, "0.2.0": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max, with 855,412 generated unit tests to validate it's accuracy! Useful for creating regular expressions to validate numbers, ranges, years, etc. Returns a string, allowing the returned value to ", "version": "0.2.0", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^2.1.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "7fb685679705470ec3701dbff48ca05a5fd461f9", "_id": "to-regex-range@0.2.0", "_shasum": "12b35ace6ec656ea32d9303c2abeb07efa61c1c2", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "12b35ace6ec656ea32d9303c2abeb07efa61c1c2", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-0.2.0.tgz", "integrity": "sha512-niV/fY2U9bSm818O6ZfA44BcNXdL0fr7DhmYQb3BRpN7khvIbBIbyPzL8FuzHRBFZzGzYQwMw2tQ7NZGXvvMbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAItHACFOxTBjSfdGnCt9PTGg/y7ojeP+z0FK8f0HzClAiAS1uIAAfoyariZlMRftmLEJlsNnGi6qvuc+tM49sD7cQ=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/to-regex-range-0.2.0.tgz_1473873994194_0.8441547460388392"}, "directories": {}}, "1.0.0": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max. Validated against more than 1.1 million generated unit tests that run in less than 400ms! Useful for creating regular expressions to validate numbers, ranges, years, etc.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "77612b7a51ce6ce86e2931f91c08376ab886844b", "_id": "to-regex-range@1.0.0", "_shasum": "0f411a456c89a592465f54ed79d3c376ad285906", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "0f411a456c89a592465f54ed79d3c376ad285906", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-1.0.0.tgz", "integrity": "sha512-zoWe4g+11aX4DAP0FkjqlY3/Dys0rwuwYgPANSN1I2vZW4leuUzDwD9vutm2tz5G9VvbXCaPazDLAqdABI6wYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEF8qdWOVsYXcrTyflB4snYVvDCXVyoJM/Z5XX2lJ7+DAiEAtMZ4LayLwF1D5AaglX64gvZT81Sk0DaX0iHllYjvHDk="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/to-regex-range-1.0.0.tgz_1476886365049_0.586534371599555"}, "directories": {}}, "1.0.1": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max. Validated against more than 1.1 million generated unit tests that run in less than 400ms! Useful for creating regular expressions to validate numbers, ranges, years, etc.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "23c0a9584ce9b269b53bcd33efa6e1a2908d8cfe", "_id": "to-regex-range@1.0.1", "_shasum": "415a8c95a7bb3704aadff3f1efff0089590dd43a", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "415a8c95a7bb3704aadff3f1efff0089590dd43a", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-1.0.1.tgz", "integrity": "sha512-srZowjq9YqJNhXjKP9GPLpPuRWr93jVPZ687Pht4jAv00zWgk1ABh8w2fTrQdfRoxp7zA/d6moSnF5r4MnQt/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChW/cAwucSr7rI991BMwjm9mbCFENZqYjmHo+yZ+JTlgIhAL0Q1a/ROBbLDDZNKlKJuFkjhe8ANUHQchwEw3GYwgkb"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/to-regex-range-1.0.1.tgz_1476886634926_0.06069781770929694"}, "directories": {}}, "1.0.2": {"name": "to-regex-range", "description": "Returns a regex-compatible range from two numbers, min and max. Validated against more than 1.1 million generated unit tests that run in less than 400ms! Useful for creating regular expressions to validate numbers, ranges, years, etc.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.5.4"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.1.2"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["verb"]}, "gitHead": "70f53c926ac14ba42c317a9084d048e1d342b69f", "_id": "to-regex-range@1.0.2", "_shasum": "48d5a984e7f701ba99f1e67b3bde1d1b11ecf74c", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "48d5a984e7f701ba99f1e67b3bde1d1b11ecf74c", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-1.0.2.tgz", "integrity": "sha512-KmuhH/HSx56UPweUyym97tlcqPemszSnU7hwQ8sSEEOnFitN8jGLZEQJY5mVPKHwst5XCOfQ90flxER+DM6a4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2sh7fIQ3V+RmqMxkKJNHJZyN480T/2sb/lr1stUNd/gIhAOWlXAcXTxQo3sP7K3u4YtvmtK6BcVRs0yMFbcZ8s8PD"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/to-regex-range-1.0.2.tgz_1476886731697_0.7599127704743296"}, "directories": {}}, "2.0.0": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.87 million test assertions.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["0-5", "0-9", "1-5", "1-9", "expand-range", "fill-range", "micromatch", "npm", "range-regex", "repeat-element", "repeat-string", "verb", "verb-generate-readme", "yarn"]}, "gitHead": "7dd74b20031f934143b627d2ef2826cdcb1aa4bc", "_id": "to-regex-range@2.0.0", "_shasum": "53bea64df461ac8f024f2b07d8e9ff27daec5985", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "53bea64df461ac8f024f2b07d8e9ff27daec5985", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.0.0.tgz", "integrity": "sha512-GPUromf0uvYuaPhTKe830QODQMJLBMnnjQRN0GbN82Z0S3DIVHCLGJw2rMOfeLV3qtmxnuWP30Ju3CEoLLx9Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE3hXDY+BfKfzM3gnwoRr+xWTci5rIkffb8ZhMJDatrEAiEAkHDGm4WpNDlW4pV9aFQSHur9VxOySMJKHD8zrerBeNE="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/to-regex-range-2.0.0.tgz_1492845290425_0.21176357800140977"}, "directories": {}}, "2.1.0": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.87 million test assertions.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/to-regex-range.git"}, "bugs": {"url": "https://github.com/jonschlinkert/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"]}, "gitHead": "c122d720c8f89889261c15ffe428570b6b5bb9ee", "_id": "to-regex-range@2.1.0", "_shasum": "ab2989a43c53bc367dd604adf22e330984840d74", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "ab2989a43c53bc367dd604adf22e330984840d74", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.0.tgz", "integrity": "sha512-7P19jh/jrkD8/D5Yx2OL9JNYBmb81HocpDFtUpVc4vwTAiZosCMihjcRYcULwpTkq9zuyCN+mBny7RTIdDRMIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH/3AGfxvasOE7mMKb/ryUfyae0yhyARlOhhyLXm3UTIAiBfaRzPhkRywMqZw3J9R07p8qodmgxYBYcy57rC/AA3uQ=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/to-regex-range-2.1.0.tgz_1492912458461_0.6350747356191278"}, "directories": {}}, "2.1.1": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "2.1.1", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^3.1.1", "gulp-format-md": "^0.1.12", "mocha": "^3.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["0-5", "0-9", "1-5", "1-9"]}, "gitHead": "de34e5079dbf33f8ac992d87914fa5e3507fa07d", "_id": "to-regex-range@2.1.1", "_shasum": "7c80c17b9dfebe599e27367e0d4dd5590141db38", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "dist": {"shasum": "7c80c17b9dfebe599e27367e0d4dd5590141db38", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha512-ZZWNfCjUokXXDGXFpZehJIkZqq91BcULFq/Pi7M5i4JnxXdhMKAK682z8bCW3o8Hj1wuuzoKcW3DfVzaP6VuNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGLV74q7l7BJTDG7zeXQ4AS57fKlDuFWho+ctI9VWQkeAiEAsQ5GW4QriTIwRbmA/RfYHG8c6WNrq7KlYr60b/tJ7WY="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/to-regex-range-2.1.1.tgz_1493300068448_0.391837228089571"}, "directories": {}}, "3.0.0": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "3.0.0", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^4.0.0", "repeat-string": "^1.6.1"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^3.5.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["micromatch"]}, "gitHead": "4fd5f5368873c971857765b5e7a15e1cc0a80feb", "_id": "to-regex-range@3.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6b3QvX2mf8yGBjYOWEDp3ZJBDn2RYx5QSoIKNho0zFxHcOEV5S9Ww9QdL6E3o2juJt9ouHu36yQo21ImlpXgKg==", "shasum": "ef50e217a2fc2b97da74ad62ca9a578d929fb462", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGyt0Uwu48worpLDeTovVCBsV+UwwQ9IpI/PHUoATPauAiBSlkP3KcGkVJPYWWonLWA0jPJHOZR5FAPStHy95CEHXg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range-3.0.0.tgz_1509513941688_0.93934185965918"}, "directories": {}}, "4.0.0": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "4.0.0", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=4.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^6.0.0"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["micromatch", "0-5", "0-9", "1-5", "1-9"]}, "gitHead": "33c39901652fa443424d9c911861d25c8ca9928b", "_id": "to-regex-range@4.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EG/hemm1cqaGMaqEfwBVLdFILRh0NAtpg1TA59oNb7IlAFMhKtbeiDliT68poGxcIu+mF9NrL/MzYUqOK1Uu0Q==", "shasum": "130abc838f977433e27e013e150d8b5b4427f009", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-4.0.0.tgz", "fileCount": 4, "unpackedSize": 21525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPOFBCRA9TVsSAnZWagAAdj0P/R9WviB7HksBB/Aoipcd\n430eXg3IyOyh7ER2lC3j/NZnW2AppPR7Fqh6hARmM3A5hJwQmH2ItlnZ3vZn\nlFgzzyixOZ8XUGthf3vHPt0o0yf2UUaWhKvBpX7VPgl0P/4kllmtEzO+Wz+d\njDGa8cauHj3+/W5pA0B1c2BCX40Sk2TtGNfIE8CIoGA9QnY6gmfoyWzUbIkM\nG4IZ1bi/QEgzbDo1zqvD/e7NJH6gqTduj/gOYevyJnodbhzkbOGvZGwK/rYl\nuaLnrbxgswZXT8x9FOaerpq7W367wIbLVrJU2cKGx4+AThB5IBlfMHTUqtw0\nS/dFvzsJBeTpPQ6O2qSLqrbmzAbdYUtAxZAICQWeG/kxvotKhkOElsLyv7Cz\n81tyhGGOwGQq6sk/d2e+RUvIf9ELQgTRP+/GAhyooY5NJWjuw76f2yXjYa2d\ndLuXRGN6RiANvP/uvLg/bG7SJuQEGNDuGlhrnKTFQTDiIxNzxxVFYdx5aCBV\n59y0Q9KV7GT8lSQc9vKcYA11K95S+pWQB1e2rHODwDJehT1tKlSRd8DHTapf\n38Q6tRvc7ByIsDc0G0MXFSlEnfyJznj52WZBu2VxgOaQ1tIie+EfynEewkm6\n7d4iUmsbpnlgR9XuQ8yAuLXuJGBbupKhTsNL1JrZQ2SNRiF2NrhG3VYBlR/8\nl4cN\r\n=rtek\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1NGW7HjB+5813xtJXUo/0Umh/43NX1OT92lfWClvQugIhAO1603cNMb48+CAQAo2lvA6xTHKZlIfucfBLtz9mMHZP"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range_4.0.0_1530716481443_0.3251037654723883"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "4.0.1", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=4.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["micromatch", "0-5", "0-9", "1-5", "1-9"]}, "gitHead": "33c39901652fa443424d9c911861d25c8ca9928b", "_id": "to-regex-range@4.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Zz53svp5+xqvAZSIkLDdxu15Sv/qgem4HUbFWYPmSvRWWrfhOnXJKiEM36Zv9yibgvam8c+bNdtpbEKYI/wLnQ==", "shasum": "e0ffa0f6b924dab8ed8d6c87768c60f0fc056330", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-4.0.1.tgz", "fileCount": 4, "unpackedSize": 21525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPOM+CRA9TVsSAnZWagAAnxEQAJkcjwKeIGYFdEvCyEfV\ni2ce/On3G3j7FA6jAkeLdRaJQ3DmCobYRdUASAxzKfDelfgv7iWok51gIp4/\n0zrbDMRVmK8vZg+pLuRYUyVUuRoODpfkVm+u31tZNV1rNFKrHas84cSPE+BG\nnZBt+1vPLetge8Gs7lINbt5g2yRBY2WzyKPbw4wFnB/YTcwSGpiY1DBhFEz8\n5Ex89yCmsdNMW2vKYdewncvcBBzVhwjPdiVK7++yEcqOdgi4JDg86y8vcLtt\nQy5CpPCvaxZZp8F/Ujn1Jrh9mTd1C9etm4+ViaG2uXL3m/5OiPkb1J2bigTe\nLYfsuLsUL4oYaMH9r2bF1PRWWiJxfFZo1OwuW1Kl0KY8/xtdcPokqYA/rELd\nOz161F72ypv38dOkkIZkDSstmKlMgbS94JBjVJqwJZsbmZTiE8LIzC5iD3BU\n6S1uPAATGn2zHCIe/iml/75YGKSnc88HpaOavQFSNSoPVd7cuubjBj9D3bE5\nM/spqWZA9FR4856xlKfpMX//6BaXft7dFwhAA3ExfFU+Df/ka+w40P1nPpoH\nPB8oGQF4Hk/RccaAwJPHCbXWOq5HUcd7UW+OOeoSAMFE8iP8d6ZmTMGfiqDe\nkZ96RpHsaItcchML3vZRTvlSzQ4m4p+G9THs5VAKvK8y4EkvE6molYUvA08b\n2Bn4\r\n=1bwt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWJqVRLHLZx+mUaOrxGvRYSDww3lQeLFuzjFE2X9hfdAIhAN5Xdtk8qLdxHmWN0dOdsXjyOxMSE0Epr8rYuP0lMJpr"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range_4.0.1_1530716990885_0.9290698909228572"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "4.0.2", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=4.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^5.0.0", "gulp-format-md": "^1.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": ["./examples.js"], "reflinks": ["micromatch", "0-5", "0-9", "1-5", "1-9"]}, "gitHead": "cd04d2cce5691e77b0754bfe801e5c23b5a92ce8", "_id": "to-regex-range@4.0.2", "_npmVersion": "6.1.0", "_nodeVersion": "10.3.0", "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sxk1o2k6PE/1pglSD23fhfgoTKNz3LT8FxfcBEqVDUjNzQV1VdAhipuiylH9kOm2qWsRTFO0mzRDXpLtm01XjQ==", "shasum": "f4556608ab0e94adf697ec8aabc1a777cee80126", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-4.0.2.tgz", "fileCount": 4, "unpackedSize": 21836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWKcJCRA9TVsSAnZWagAAhVsP/iTXpnX5i/XJuCvId/rT\nIBOJnoR3ZGlpLMBmavghk0QMQAMbC6Lqyj/rcr+4gG2uFRM8pK0O5oWI65CG\nwTNvnT1dub/BC6yYuEfxqVXkr2OCnHZJm6aHy0qaak/EP1a911ju5bP3pRfX\nzKXXZHdzL13ozaUhd1nhP3jP9CTEavg2IncLtoHMcQL1XdNVv2mX5f/9z/j9\nEmxXmWlJO8k0FUwnvW1RGKNkbyNI+VIqr42col1+JBz4sriE6qsIh0wFbkMA\nXpB54YiQ9SokEPl7YakGfR+5QaQqpt0k4cnZUDYGO+OUJZZod6a7qqYG6ykt\n6r++kkuIrvqC4pDHqrznLftr2SY6UqY6KWlRPHOpgXpjtrNy4pODqoqeUHlr\nxoVXnm3RIIa+eN8IfYVQF5aJiN8QInPMInkcAR9SueZ1f+L0sNdIcKT8iq1Z\nw58/8j+HuxZhM1UBRY811fySlJJ86T3XXA9jZAhAM+vCSvW4wos8RzViFu7F\ntoRxPmSSbqOsFtAWk5AdHKk8V1pgqKHVzcTsCqH7wkAb8Egdb+yed4+peApR\n3Eh/82Uq/h9Klr5TasqD2vPBUJGk/MdLDz33Gso5eadJziygLoBpKdaadXFo\n5BN7JAJHunqd9mfbwAQ+HIfwXDY7nkCWgb/8MwdcZamMDOEzBKoNWCkiW211\nIgNk\r\n=e+gr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo1p9D9j7ieswM0AOl57ZCXFm7nF+345pn218d7yK/OQIgINXc6fIfxEu1diH3Kq70jq2JSSAVm3EbqKNMqjHkoDI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range_4.0.2_1532536585285_0.6234204874902807"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "4.0.3", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=4.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^5.2.0", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["alpha", "alphabetical", "bash", "brace", "date", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sequence", "sh", "to", "year"], "verb": {"layout": "default", "toc": false, "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": {"examples": "./examples.js"}, "related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}}, "gitHead": "f7ce5ace1f41b53c7f81bd76802decffc8a2dcec", "_id": "to-regex-range@4.0.3", "_npmVersion": "6.5.0", "_nodeVersion": "11.4.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-AtJgwCeygrdcfleD1nolmv8TSJcsPvSsvnqQRzc1AkEa//+RRTseKZpaXOfZk2/U1B+bz0sRpkaF1oHX5YmHKg==", "shasum": "64dcfb33638ec86160eb1a8fe6937d7160fcd49a", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-4.0.3.tgz", "fileCount": 4, "unpackedSize": 21857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQOtRCRA9TVsSAnZWagAAKYsP/3pAY2otqajDO06nEg6A\nF6vVmQ/gQPjnDCJvynn8VAEz4ywQv3zi5/pAFxdvfjofhulJIsLRzs/8/S4j\na4pB471biKw68XGSsMuvPRM2FKA4nuOnn4WXFb9FdakzeLw6XmWJH+EQSh1N\no45f4ZsuDQoYgVjq3N8ef8XPoBA+w/JQ4glkK0P5IqTnJ6y1IJWd85yhm6Py\noSnHVdhqrBFoQc+u9TShsuTgF87yCFVyteh4R+7CtDUGsuUxtot7CEGRe5Jt\nBba7IE2JC8g2DDpyVV93Nd6+vSE0zcIcVsdYy1UGLnQuVC+/23uRx2bqeYZe\nsDlc5j5Xd1ZhmdsAUGSxhTsupScpN01yJeU3wIUCHephWCJaiEwlu3h4XjNL\n4CWntC2A79nmjygjFp6jVJf6ZrOB/x2yupDrEBTfdsz4VDKGYSQ9JaX4PEQe\nepsDJQigUjEscLqGqCtQ0kvQ0sKuRAj/H1D0DxFyqsuFrkJufpm21M38does\nxsUUtrImHwrHyJRE8aNxEhlw7qnTqysYIiIn7Ai5R8LXe2lEKcVd+yTtLifP\nR5Ee36X7mYNQHBAxsrouOKTANNW1Pwm6eMsZMyxV0eT+NpHRA5Twym9z+2HC\npIpbw8o3QJNE1bPuIb0wuCaZtWclNNahJSSZuMBwjJ298iwr9bh0JxY6HskT\n41BO\r\n=u5mr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJzkxF12HgeBvqWKg/XkeJmlPcXlAjx8BBLjULqVHOWwIhANsvRfdrdaP7buy4KzZ3LTZXnfvDuAgrukmoSTug3Irh"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range_4.0.3_1547758416308_0.761733796901277"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "5.0.0", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=8.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["bash", "date", "expand", "expansion", "expression", "glob", "match", "match date", "match number", "match numbers", "match year", "matches", "matching", "number", "numbers", "numerical", "range", "ranges", "regex", "regexp", "regular", "regular expression", "sequence"], "verb": {"layout": "default", "toc": false, "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": {"examples": {"displayName": "examples"}}, "related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}}, "gitHead": "ae7e43d97827e87650b975719de5faf0434e34e6", "_id": "to-regex-range@5.0.0", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LIBaWcdxFv9GhuMrXVdDJ0zLJK+3GjxCvJq/q68ZKEeHWGmSLl6m5ZRdflM96lO1QelulcqxU1SJ/IzucVmjWA==", "shasum": "d53e58b704377f6cb2abe26e0fd63cb1ee348dde", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.0.tgz", "fileCount": 4, "unpackedSize": 22939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqZJWCRA9TVsSAnZWagAAzygQAI7GI8P6u3vwFI0YHDvZ\nV3vcv8kuoyPqWSYkPE4OiS7hFyvZfUB2Lkira9NNXUcorjufmCpuCBOcTRIJ\ngItTI12vTdjYr0sMeXd2dn58EKgLw66P1TFLVJ47mz0z8HJ3Ufv/XHyyAVA1\nxPgNLUvTSBiRm/g+IZRJ7Bj4QEngaiKFXG5wlK/KtzwPbb0tQ8Kn23gtvHfc\nRnsfCjOMTghBTDwH4jF5XSW6r8BsJ7lQwcEkMCahc4NiN1QvPIvun+PNMPN6\n9bE6HFmsPnUlZtRhch6lP2bPwF1KMzr7wqi70X7YMf05zEkYAe7dZSfCiXRi\nMtz9HTs/rw7/SLB8hIovCa2b36J3sKheFydnPE5Ah94QZY4naw6cNIVOBqZX\nPtSIw0fVxdCkbclz9xPlSoiENaHfc1phB4MRhFjhvfi1/0MN3JjpJty/h1mg\ntVL3UqDnM6QWGfzybm+5eaFYZ8GSNf13GA9jzEfof3OslMTRLqgHhhMHKzmh\njG+Wmt9CfGFX1+BOCbhskAEKUpyg62Lqwe9ti2zL+ZmF/6GGrKCen/sHkcKy\naPMzm6ARY2haHanxFxq0Rgzf+3KorTQYiNycGzNySN+h3KEpluGUFqN8o9RQ\nT/1657fyIp+6xWho2X+EMn2pXtb8fzQRpoLw+LtpYWx4EWss4lak6Ie0V61y\nUTLg\r\n=YzD+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCx24Q3ufi1ZJXuu0zjYEfUfW2SAw1mYYI5mUOV7JAKJQIhALBFPoCK2BZtX44nEb9aQk8r9T8D0N+KKqrY8IZdrlZB"}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range_5.0.0_1554616917590_0.23901851149275455"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "to-regex-range", "description": "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.", "version": "5.0.1", "homepage": "https://github.com/micromatch/to-regex-range", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "main": "index.js", "engines": {"node": ">=8.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-number": "^7.0.0"}, "devDependencies": {"fill-range": "^6.0.0", "gulp-format-md": "^2.0.0", "mocha": "^6.0.2", "text-table": "^0.2.0", "time-diff": "^0.3.1"}, "keywords": ["bash", "date", "expand", "expansion", "expression", "glob", "match", "match date", "match number", "match numbers", "match year", "matches", "matching", "number", "numbers", "numerical", "range", "ranges", "regex", "regexp", "regular", "regular expression", "sequence"], "verb": {"layout": "default", "toc": false, "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "helpers": {"examples": {"displayName": "examples"}}, "related": {"list": ["expand-range", "fill-range", "micromatch", "repeat-element", "repeat-string"]}}, "gitHead": "c05ef9ec07e7703d146467934098ecbde9d0bd95", "_id": "to-regex-range@5.0.1", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "shasum": "1648c44aae7c8d988a326018ed72f5b4dd0392e4", "tarball": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "fileCount": 4, "unpackedSize": 22939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqZL1CRA9TVsSAnZWagAAJuAP+wfZA2jBqNp9atPJlpCY\nu6if9owyyJmytCo1FdveRaWBnzTIVQNnQFtUiBBcn4yO85OIY4QYi+A7h61y\nJ5mJiWhE8Fd/2iHq3y7DUoKvC+JMhj7Yx1Ua5FGKymiL8YM2JkA/ZLssc3sh\nMTcnyOcj0t+FRoc/qDVFUIuAF97KEiOpCPlLfbVkBqfD/6OwcqM1630Q/Am2\n+MPl+X/WYwFUPe8DvHUM4tUs0e8PxAhtm3uWpkk9HYPTYFiznb4CLlmk0ag+\nG1i6W7YrDhWxhFlwRfHYrKzlxkNtktpg7/JqaDysJ8vhlgp+KOig4ypKMf8a\nUfXCiSsSzqGJW3ZlKYWJg/zW7eTvs9/CjqhZylEPILJyp7aTZoZ/mP1RrZWT\nHzmGIs3H+vE0nGp7kPQYNXcSSLk/wA/fWSaSSro9TgkuNB8y/uc8lBTnsLls\nzPvRmd5T2HHItfpZtiBrjLBc1KNB37mNJoQQGAjxhoDiRLvjAlSQdH/g9vvM\npgUUELBWk5IQEggivPmg2IGVb/wE+/KqJsieVFMcIVZ9K09nYZWmbio5eYPZ\n+qVo2yagj/LLHZFpRvE/KLt8OfB1zfOGPAFUFtxtJDgVvzvMPp6JsVyVKgiW\nZ/J0ykRkeCTHTSqNtuumael9bmgkZmiWnUtUidxbeMsYR4PUKYWbnW5LQJU+\nnaxh\r\n=ZLF2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAuG3AgRDWv1sM6JpSfmACOwKnfS2VUO8mm07wxg1FilAiAVT1haxG6oxpandLKEjhmepC4dn4y22NhYJTeW5Yfbeg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/to-regex-range_5.0.1_1554617076904_0.4972203553852246"}, "_hasShrinkwrap": false}}, "readme": "# to-regex-range [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=W8YFZ425KND68) [![NPM version](https://img.shields.io/npm/v/to-regex-range.svg?style=flat)](https://www.npmjs.com/package/to-regex-range) [![NPM monthly downloads](https://img.shields.io/npm/dm/to-regex-range.svg?style=flat)](https://npmjs.org/package/to-regex-range) [![NPM total downloads](https://img.shields.io/npm/dt/to-regex-range.svg?style=flat)](https://npmjs.org/package/to-regex-range) [![Linux Build Status](https://img.shields.io/travis/micromatch/to-regex-range.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/to-regex-range)\n\n> Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.\n\nPlease consider following this project's author, [Jon Schlinkert](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save to-regex-range\n```\n\n<details>\n<summary><strong>What does this do?</strong></summary>\n\n<br>\n\nThis libary generates the `source` string to be passed to `new RegExp()` for matching a range of numbers.\n\n**Example**\n\n```js\nconst toRegexRange = require('to-regex-range');\nconst regex = new RegExp(toRegexRange('15', '95'));\n```\n\nA string is returned so that you can do whatever you need with it before passing it to `new RegExp()` (like adding `^` or `$` boundaries, defining flags, or combining it another string).\n\n<br>\n\n</details>\n\n<details>\n<summary><strong>Why use this library?</strong></summary>\n\n<br>\n\n### Convenience\n\nCreating regular expressions for matching numbers gets deceptively complicated pretty fast.\n\nFor example, let's say you need a validation regex for matching part of a user-id, postal code, social security number, tax id, etc:\n\n* regex for matching `1` => `/1/` (easy enough)\n* regex for matching `1` through `5` => `/[1-5]/` (not bad...)\n* regex for matching `1` or `5` => `/(1|5)/` (still easy...)\n* regex for matching `1` through `50` => `/([1-9]|[1-4][0-9]|50)/` (uh-oh...)\n* regex for matching `1` through `55` => `/([1-9]|[1-4][0-9]|5[0-5])/` (no prob, I can do this...)\n* regex for matching `1` through `555` => `/([1-9]|[1-9][0-9]|[1-4][0-9]{2}|5[0-4][0-9]|55[0-5])/` (maybe not...)\n* regex for matching `0001` through `5555` => `/(0{3}[1-9]|0{2}[1-9][0-9]|0[1-9][0-9]{2}|[1-4][0-9]{3}|5[0-4][0-9]{2}|55[0-4][0-9]|555[0-5])/` (okay, I get the point!)\n\nThe numbers are contrived, but they're also really basic. In the real world you might need to generate a regex on-the-fly for validation.\n\n**Learn more**\n\nIf you're interested in learning more about [character classes](http://www.regular-expressions.info/charclass.html) and other regex features, I personally have always found [regular-expressions.info](http://www.regular-expressions.info/charclass.html) to be pretty useful.\n\n### Heavily tested\n\nAs of April 07, 2019, this library runs [>1m test assertions](./test/test.js) against generated regex-ranges to provide brute-force verification that results are correct.\n\nTests run in ~280ms on my MacBook Pro, 2.5 GHz Intel Core i7.\n\n### Optimized\n\nGenerated regular expressions are optimized:\n\n* duplicate sequences and character classes are reduced using quantifiers\n* smart enough to use `?` conditionals when number(s) or range(s) can be positive or negative\n* uses fragment caching to avoid processing the same exact string more than once\n\n<br>\n\n</details>\n\n## Usage\n\nAdd this library to your javascript application with the following line of code\n\n```js\nconst toRegexRange = require('to-regex-range');\n```\n\nThe main export is a function that takes two integers: the `min` value and `max` value (formatted as strings or numbers).\n\n```js\nconst source = toRegexRange('15', '95');\n//=> 1[5-9]|[2-8][0-9]|9[0-5]\n\nconst regex = new RegExp(`^${source}$`);\nconsole.log(regex.test('14')); //=> false\nconsole.log(regex.test('50')); //=> true\nconsole.log(regex.test('94')); //=> true\nconsole.log(regex.test('96')); //=> false\n```\n\n## Options\n\n### options.capture\n\n**Type**: `boolean`\n\n**Deafault**: `undefined`\n\nWrap the returned value in parentheses when there is more than one regex condition. Useful when you're dynamically generating ranges.\n\n```js\nconsole.log(toRegexRange('-10', '10'));\n//=> -[1-9]|-?10|[0-9]\n\nconsole.log(toRegexRange('-10', '10', { capture: true }));\n//=> (-[1-9]|-?10|[0-9])\n```\n\n### options.shorthand\n\n**Type**: `boolean`\n\n**Deafault**: `undefined`\n\nUse the regex shorthand for `[0-9]`:\n\n```js\nconsole.log(toRegexRange('0', '999999'));\n//=> [0-9]|[1-9][0-9]{1,5}\n\nconsole.log(toRegexRange('0', '999999', { shorthand: true }));\n//=> \\d|[1-9]\\d{1,5}\n```\n\n### options.relaxZeros\n\n**Type**: `boolean`\n\n**Default**: `true`\n\nThis option relaxes matching for leading zeros when when ranges are zero-padded.\n\n```js\nconst source = toRegexRange('-0010', '0010');\nconst regex = new RegExp(`^${source}$`);\nconsole.log(regex.test('-10')); //=> true\nconsole.log(regex.test('-010')); //=> true\nconsole.log(regex.test('-0010')); //=> true\nconsole.log(regex.test('10')); //=> true\nconsole.log(regex.test('010')); //=> true\nconsole.log(regex.test('0010')); //=> true\n```\n\nWhen `relaxZeros` is false, matching is strict:\n\n```js\nconst source = toRegexRange('-0010', '0010', { relaxZeros: false });\nconst regex = new RegExp(`^${source}$`);\nconsole.log(regex.test('-10')); //=> false\nconsole.log(regex.test('-010')); //=> false\nconsole.log(regex.test('-0010')); //=> true\nconsole.log(regex.test('10')); //=> false\nconsole.log(regex.test('010')); //=> false\nconsole.log(regex.test('0010')); //=> true\n```\n\n## Examples\n\n| **Range**                   | **Result**                                                                      | **Compile time** |\n| ---                         | ---                                                                             | ---              |\n| `toRegexRange(-10, 10)`     | `-[1-9]\\|-?10\\|[0-9]`                                                           | _132μs_          |\n| `toRegexRange(-100, -10)`   | `-1[0-9]\\|-[2-9][0-9]\\|-100`                                                    | _50μs_           |\n| `toRegexRange(-100, 100)`   | `-[1-9]\\|-?[1-9][0-9]\\|-?100\\|[0-9]`                                            | _42μs_           |\n| `toRegexRange(001, 100)`    | `0{0,2}[1-9]\\|0?[1-9][0-9]\\|100`                                                | _109μs_          |\n| `toRegexRange(001, 555)`    | `0{0,2}[1-9]\\|0?[1-9][0-9]\\|[1-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]`                | _51μs_           |\n| `toRegexRange(0010, 1000)`  | `0{0,2}1[0-9]\\|0{0,2}[2-9][0-9]\\|0?[1-9][0-9]{2}\\|1000`                         | _31μs_           |\n| `toRegexRange(1, 50)`       | `[1-9]\\|[1-4][0-9]\\|50`                                                         | _24μs_           |\n| `toRegexRange(1, 55)`       | `[1-9]\\|[1-4][0-9]\\|5[0-5]`                                                     | _23μs_           |\n| `toRegexRange(1, 555)`      | `[1-9]\\|[1-9][0-9]\\|[1-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]`                        | _30μs_           |\n| `toRegexRange(1, 5555)`     | `[1-9]\\|[1-9][0-9]{1,2}\\|[1-4][0-9]{3}\\|5[0-4][0-9]{2}\\|55[0-4][0-9]\\|555[0-5]` | _43μs_           |\n| `toRegexRange(111, 555)`    | `11[1-9]\\|1[2-9][0-9]\\|[2-4][0-9]{2}\\|5[0-4][0-9]\\|55[0-5]`                     | _38μs_           |\n| `toRegexRange(29, 51)`      | `29\\|[34][0-9]\\|5[01]`                                                          | _24μs_           |\n| `toRegexRange(31, 877)`     | `3[1-9]\\|[4-9][0-9]\\|[1-7][0-9]{2}\\|8[0-6][0-9]\\|87[0-7]`                       | _32μs_           |\n| `toRegexRange(5, 5)`        | `5`                                                                             | _8μs_            |\n| `toRegexRange(5, 6)`        | `5\\|6`                                                                          | _11μs_           |\n| `toRegexRange(1, 2)`        | `1\\|2`                                                                          | _6μs_            |\n| `toRegexRange(1, 5)`        | `[1-5]`                                                                         | _15μs_           |\n| `toRegexRange(1, 10)`       | `[1-9]\\|10`                                                                     | _22μs_           |\n| `toRegexRange(1, 100)`      | `[1-9]\\|[1-9][0-9]\\|100`                                                        | _25μs_           |\n| `toRegexRange(1, 1000)`     | `[1-9]\\|[1-9][0-9]{1,2}\\|1000`                                                  | _31μs_           |\n| `toRegexRange(1, 10000)`    | `[1-9]\\|[1-9][0-9]{1,3}\\|10000`                                                 | _34μs_           |\n| `toRegexRange(1, 100000)`   | `[1-9]\\|[1-9][0-9]{1,4}\\|100000`                                                | _36μs_           |\n| `toRegexRange(1, 1000000)`  | `[1-9]\\|[1-9][0-9]{1,5}\\|1000000`                                               | _42μs_           |\n| `toRegexRange(1, 10000000)` | `[1-9]\\|[1-9][0-9]{1,6}\\|10000000`                                              | _42μs_           |\n\n## Heads up!\n\n**Order of arguments**\n\nWhen the `min` is larger than the `max`, values will be flipped to create a valid range:\n\n```js\ntoRegexRange('51', '29');\n```\n\nIs effectively flipped to:\n\n```js\ntoRegexRange('29', '51');\n//=> 29|[3-4][0-9]|5[0-1]\n```\n\n**Steps / increments**\n\nThis library does not support steps (increments). A pr to add support would be welcome.\n\n## History\n\n### v2.0.0 - 2017-04-21\n\n**New features**\n\nAdds support for zero-padding!\n\n### v1.0.0\n\n**Optimizations**\n\nRepeating ranges are now grouped using quantifiers. rocessing time is roughly the same, but the generated regex is much smaller, which should result in faster matching.\n\n## Attribution\n\nInspired by the python library [range-regex](https://github.com/dimka665/range-regex).\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [expand-range](https://www.npmjs.com/package/expand-range): Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. Used… [more](https://github.com/jonschlinkert/expand-range) | [homepage](https://github.com/jonschlinkert/expand-range \"Fast, bash-like range expansion. Expand a range of numbers or letters, uppercase or lowercase. Used by micromatch.\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/micromatch/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n* [repeat-element](https://www.npmjs.com/package/repeat-element): Create an array by repeating the given value n times. | [homepage](https://github.com/jonschlinkert/repeat-element \"Create an array by repeating the given value n times.\")\n* [repeat-string](https://www.npmjs.com/package/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string. | [homepage](https://github.com/jonschlinkert/repeat-string \"Repeat the given string n times. Fastest implementation for repeating a string.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 63 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 3  | [doowb](https://github.com/doowb) |  \n| 2  | [realityking](https://github.com/realityking) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\nPlease consider supporting me on Patreon, or [start your own Patreon page](https://patreon.com/invite/bxpbvm)!\n\n<a href=\"https://www.patreon.com/jonschlinkert\">\n<img src=\"https://c5.patreon.com/external/logo/<EMAIL>\" height=\"50\">\n</a>\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 07, 2019._", "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "time": {"modified": "2022-06-27T07:24:37.330Z", "created": "2015-08-07T10:48:06.053Z", "0.1.0": "2015-08-07T10:48:06.053Z", "0.1.1": "2015-08-07T13:00:07.697Z", "0.1.2": "2016-04-27T07:11:41.093Z", "0.1.3": "2016-09-14T13:46:55.235Z", "0.2.0": "2016-09-14T17:26:36.135Z", "1.0.0": "2016-10-19T14:12:45.685Z", "1.0.1": "2016-10-19T14:17:15.634Z", "1.0.2": "2016-10-19T14:18:52.341Z", "2.0.0": "2017-04-22T07:14:51.512Z", "2.1.0": "2017-04-23T01:54:20.425Z", "2.1.1": "2017-04-27T13:34:30.544Z", "3.0.0": "2017-11-01T05:25:43.109Z", "4.0.0": "2018-07-04T15:01:21.509Z", "4.0.1": "2018-07-04T15:09:50.948Z", "4.0.2": "2018-07-25T16:36:25.376Z", "4.0.3": "2019-01-17T20:53:36.504Z", "5.0.0": "2019-04-07T06:01:57.735Z", "5.0.1": "2019-04-07T06:04:37.030Z"}, "homepage": "https://github.com/micromatch/to-regex-range", "keywords": ["bash", "date", "expand", "expansion", "expression", "glob", "match", "match date", "match number", "match numbers", "match year", "matches", "matching", "number", "numbers", "numerical", "range", "ranges", "regex", "regexp", "regular", "regular expression", "sequence"], "repository": {"type": "git", "url": "git+https://github.com/micromatch/to-regex-range.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/to-regex-range/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"jonschlinkert": true, "borax": true}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}]}