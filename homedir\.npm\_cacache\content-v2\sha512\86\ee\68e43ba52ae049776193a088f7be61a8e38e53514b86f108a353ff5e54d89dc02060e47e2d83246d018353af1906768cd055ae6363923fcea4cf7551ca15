{"name": "@discoveryjs/json-ext", "dist-tags": {"latest": "0.6.3"}, "versions": {"0.1.0": {"name": "@discoveryjs/json-ext", "version": "0.1.0", "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "coveralls": "^3.1.0"}, "dist": {"shasum": "58212ca5478669a5a50b5fa7a80ef9af86bd6092", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-eAioVEGXRlMAXDI+yk9FmVE6+HsLzlafrx6gEk1MlYAMsUscx+2Lva/IP5wQ82K1Hmt15E0Uq0+/7ytD5SQpEw==", "signatures": [{"sig": "MEUCIGzru3Yb1t7QBSZp2PJqp2lH+7LGyVqzdQakcASXA63aAiEAjd0RV3LH/FaHiLQIoXvz0rO1/cWzccI2g4J3udZuLFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV1y5CRA9TVsSAnZWagAAJlQQAJpFmCurNYZsSHG8e7a4\nz0vpJEkNjsnoA7+2wVHEdnvUsxaIsHwrnG6teblvwDqS8QbSrm0AITPsGtCF\n5zFBWPDZqhYU3A8JMjvJKmjZJj7lFRQNoHirUWljfsVak7OpqRZBO2yzSRL1\nsiu9gnFAYPOBnhJsnvJMBV1VZ/VFqGPmkTKbgXzI/LXfxg0SkWNLjq5BLUr4\nte4ToJyMKeiBtUIzYyb1zZRnjd796ac/Up0r/po70gvZ1L2aEp/DgVFtsSK5\nBAIB94dJuaXUJU7f4/tFtlECsgT59XeW0t4WDSrDDk/geEHFtIzRbYQ106rg\nrXrE2K687QPGRZB4bHsuGBRyGkBmOKa0FRSfAwoNNdBPYtuCdpyzp1ckjBWT\nw/dL2P5rA1QBMchzjaey/6MOUEWV28SOUnRwvAlJFm0EQynS4TosRWFyn8td\nxJNtgTlc4PmkbLg+iDOUtH1HhQ8Lu5q3npSDtfoH7nVvWp4kXm+FMKEvOIwx\nsZ7Djmyv/tPYGpYbXeZLa4JtWcuw5OqpRQz8YSHIklt6ILNGFyAtY6O6uNdc\nkf71ucPnqoQb/XIAwBdNdkI6bFDr4f/6TorXmtUZLgw/6g2E4yEwyYv/Ncjw\nRQKPEXIsbbq83b/q8na6p5qq8RrrGRHL/mhOQ202E6k8KBsfBlOXG6MOM2/j\nVEMY\r\n=n/Bd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.1.1": {"name": "@discoveryjs/json-ext", "version": "0.1.1", "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "coveralls": "^3.1.0"}, "dist": {"shasum": "7d36c499032406e3799f1c3f54f2f9b3f4272026", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-T5TdVRPth7Vew3oF649Hf1OkLFTvAvRpCq5vRJehKWOHSWqeOQyAUyUzvDEmql/IgfKyafOi6eB1sg6qXGkj/w==", "signatures": [{"sig": "MEYCIQCMuqSJRSH0HA3udCMT/yaoR5u9eQPUUSHOHdLipYB9egIhAKY0kQG+3LWIs/AvDWVlya3RPAdmxF341yxFKgAaGcDP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV6zlCRA9TVsSAnZWagAAqdUP/2yJ0JfgdmgBvbsv3/IX\n8jSYq3H7UlJ5WdEmj7/ERkMbi2UzIrvyvb9UZsEPjQBQtCYaq9egPrUTizmj\nAa7HT+TE89r8F9b3oxWE2mvVtXnQQiv2vSSRC8kt7p9PvMuvodhmL24MQLg2\nG9GOYI090tfZ/9woTX5GGUS9izlNkFTEYxTRxat+w7eDlW6GnXKX8eznf4fe\nwq2g9WcVFTJ99cZZ2lrnxZYoYBNceKrOdqy9XKUg7Ac/+5q1nsTDOZikO+4I\nlcEhsJnylSSaNM/1GrvPFu/F4y1jObv5QSVw26iH27iY0EQ45Vo/l2aKN2vQ\nUJ0H+7U2vDebsXwiELkz1dhaOvFQFWBEVgVZcxgp1f1QuomWAYiGX4o4ER8C\nBEtf5YzG/PvJ31t9BvicgYmTTCWnDZEfEkMmp5LE0bA9bQuDhQwErUHaxr/D\n4jjC8TP1alkJgFkD8uRs70KwWYgE1Ra8v1dw4q+osbbSnE3c1YlY7757MXi2\nlU3I47BURTAqFFVOZejZyiVk5i6bEatHq5nop2qRVVCfDUkOA46QiaezNelg\nCWCav6YR7tGhmu4oQNH1alQfv6muc3SaXs7+yi8kUhuNqw0Ijm+pkUQVd9aT\n4HiZ1x03rDECue6kWbIdX2e1S/ySjH4Dnr8GYiFDy3nl1oyvvkTG4MPyUpOH\nwIhF\r\n=D8xg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.2.0": {"name": "@discoveryjs/json-ext", "version": "0.2.0", "devDependencies": {"nyc": "^15.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "8596902f4339526cfd94b509b0735e5dc0edace3", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.2.0.tgz", "fileCount": 8, "integrity": "sha512-nAH9XTVYpEPXmrO1QnX59LNLVyx+q8VCZ9+rnM1gKop7zAEq92VMCEknvpIv0EdUdVswo1h9uB2B1xhvbbZjgg==", "signatures": [{"sig": "MEYCIQC9pH4fxeX20AlSQA1xw/s21spbZLq6Jvpv9a1d7uMQyAIhAIuzPims0n25VpyW4i/ItMdaeFscJuG+ZXsmfQzG1juL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcdhmCRA9TVsSAnZWagAAes0P/2pEoZygPA+VsxiIIgCe\nvFZMxSkwmB5rAILM3oAsTBAgplTkGhY94DN6KWwiFQAoEB/2Bp54U6li96Lb\nomRXtCclYAsdTtS6UM2VrDc6Vb4OFD6MmfH1y2M5o5fDaaf14/K7BrojMaKM\n6LUCP11BE7kShdZ/p+va6XDr/sp2q8tB8Vti5mZCUmuYUTORG9KLmoDG7LDZ\nYVMtvLeZmOHYFq+8G9BA+BpttrsAu117r0LNQzQ7sY2u2aVid4F67fp6t3f6\nvEx0w3uX0ele9E5I05gnjeOT/YWclxsv6+VVmTsqht/ejA9NY3JopaVXtdXH\nSEG8UxEsLajQmPFD0TskhamcXEDsKsHEr3qCAJKbPJ9PSYt5TPe31Jgp5tgm\nnlqn1T8KiVrVMaEBQmgg+JQqkaqC3vhVGd1bTMClc3OwDVz3WvCg6b/IV38p\nQ4PWcLxhOZJnVylgOFAEytZtNmWeo1YbAHnkod7zKI/fFsffpAQCtmjsEMMS\n3NZkRNirtooW/8cPEI55JjSs1kJUrzlO8dZILJpIbwBOr24qvSWr+ffcoJ7v\nEuAwmLjK4KSDFVfy4QYi1cS9A0OQG9iLi36YtE10i6sGC9kWfH03Blbw/YVY\nWxmZwjalH6IyX3OdNjJ4uRzeQc7avQTsgwQ2xpvVaNBfAOWzx5vOdRpBEYJj\nZXJf\r\n=swzT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.3.0": {"name": "@discoveryjs/json-ext", "version": "0.3.0", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "d0943856e54e5859268e67b2d7f3755077e8a948", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.3.0.tgz", "fileCount": 10, "integrity": "sha512-hT6NV1B7CzT2s/XujkifKIFkcXzkT++p6jiJup5xaJol53yU34q16Ovpva0FeRIfHs60KnU+M3GecNZUW+ed6Q==", "signatures": [{"sig": "MEQCIHI6wLrbsyaNHLF98ynEDrAsXi3CbuYQ22+LoPobCXhjAiAp8gNiIQy1WT0xTxCqWJXrbZZ6EqV/p0tje/nULymOTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfceKOCRA9TVsSAnZWagAAxxkP/jWO+PXbWmaCDEuDDKYR\n8yLq9H64s0QqtBq3sBhY8CvL6Kq6c3b3Ox0vHXbshvIlT0UOVich3bDLE49E\neoukplilIPQjQuxkU9IrbZnHpHVVPhrEZPmjePYszZx+rfW3HsdB3hGlQaJa\nTmINVpWxDDyFhvYTV6i+c2cI9VECATpSqlm2h2Sm9ELrKuBPk25lB2B1Rjug\nDa3Lwuo3nX6YErwhy1zXRR/wXUD1waRnDhIQTN4mD3Bysfo9zDB19LGZx/2i\ngu9rxRMKc4D6BJLnNyxY/XsJkcNjRHUL3jchAZCKNdIKtKJY881XvLywVq+X\nAayKLbu7CktXjnox0DIp3HU3AzIIT0q6GF8oKTCuXb8jji1SRTI/wu6fZnma\n/sCdclV/VrkQjYOLAAgBARvrNbcRw2eNpWO92uddbZkiU/JLKINu28TMoDx/\nxTCaV+GUFqo2jKfL38tVgSWT9nT0+3Hf60CJqYekuD0mu7XixWfFnph68OWR\nq5fl/0HD+gOdDuYghX7z4J8vZkIOuJR9rQkQgl8tZWPcL6hMOtlAt9lMDsVO\nuWc7CW4Jvc0iKQSqQGJrfT+u919MFfjUQBEhsWnuKNj/oa/1pRLCmNyEDm8j\ncH2+SCLQOqse8beaf0RXIHm1Q7IsjtEJIt3oZsCwOSirXK9UXstHZiN8Qht1\nXnsl\r\n=4ik/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.3.1": {"name": "@discoveryjs/json-ext", "version": "0.3.1", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "7b33ccbef07506fe60d2ed7c9d70c8901ef01083", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.3.1.tgz", "fileCount": 10, "integrity": "sha512-Mm1lAQBGmvEc6FmFI+Jqg3ox4c6qlKJEWfvr89hwzytLOLJEp0ehnZVqZRKTggmQUhvvlQUI/1odM5viEW9TAQ==", "signatures": [{"sig": "MEUCIQDzJE/vT4RF52DLxDdlDBfKA47kKxAbwE9aUsSoUbmLSwIgMOBJr2uO5TCou0czwyioINU4CoiR/2pkOpaKOD1NUnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflsu/CRA9TVsSAnZWagAAtUUP/i53qKw1BfNhZg2cU5wa\n/fenY9HNUGTgIqAEjp5Tx9OdKSpLaTDCbgplhpcsBtCEuHzulMTZyhLnF92z\nR+iaD+z+nTNpUE3ddhTp3JoDOCxjODelFghVplx7N8ZSkUAYQ4KrNV6e8syl\nD7/JYrLc6zQcCrGaYk2EDMrx5kF7uDxAboHFpDx1xDPvDMH80uyxKClcpUo4\nTe/U/wbtVPmIC65/wKVt+rQP3OZZ1WeE10ZbrwAmJPJUmI/IFMxFacEFB8UW\ngTTW5triAaC4tVq1JZNrm6c5ia9gVB0GjcUKh+f8urIErQQ3fx5zZEfgnGzB\nE0PUwBGNkbMr0vkIa7Z/iVYZtVPwUbsUtDPcxrzu2Gbfj4CcVAb3CBx8GDg8\n1TsYn1DAh4391cfj+Q+kcQfZg0ScztEjrDx148BH9158eS6gm6T0wkliIKhY\n0YtB1s8NyyvK1WlIjcWGMHaVEMFpbbBsd7vIiSuTywQYyOOcwcnZURH8CqYd\ndyG2ShS0T56oErtz+xk/HLxvbUaZvo7SGDHN/hYELrIwB37kxgflQPJ3wRWE\nvQgguuEysnJP418+h+h/X9bn4h5pN5KQAf8jlr5swGCnAfWDEsAlHmjqJ3WV\nQt83D2vprkrS4RrgyBPqNsCXbWaI4tGFxMK3NnldWVOKnAEGH5mBqz8rUxdN\np9WU\r\n=M2wa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.3.2": {"name": "@discoveryjs/json-ext", "version": "0.3.2", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "80ff80f3b221890fd125f185da1d2642e73abfa1", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.3.2.tgz", "fileCount": 11, "integrity": "sha512-C7G6IE8/8tVrLhXxNIh7Jwt2qIDXGUlwMNU4lSRzcwfcxM4Vb5yOnfR+EtoC1ci+UBSJE1NfbY4O0VcsnIhlhQ==", "signatures": [{"sig": "MEUCIQCvemX38h4zaaVcQiffKs0Zrxc/FB3TvvcXTLt8u/fZQgIgZ9/9nyAIFpNVuDlhNV6twVdXI7PoK45/QEhmXmopMT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfltJSCRA9TVsSAnZWagAAqbEP/Rx89UfFrIyuRwmWdphg\nZOdrOG2oyAb2l/QHNulpwqKcp5NFxCQ7Mf5UvqWqZfTpAXuXPbjZSxuvLXMv\naXALMHUpe6W1rQAmgfwM5/JfEgDkyHjAXXCdnbvwD4I3MChmzxQPY2v9sHXI\n0ovOc1MXnfhnwBSbdbGFnbLrRIJ5juXpw2Md8c3dqKuslIzEZq1Jeze/j6G0\naVbjrmUuJTJAHKdCTOjshHc/w0Dr9dwYhkQCbKYOBi2F7HzMV2OikqCDSU+N\nU4VoLjWS4OfAlqO4L+ey2Smh/h6UA2oBzmztvhZOmnaJ+bc90xCzdp7UzQew\npIWOXNObUzpbtDEEvEzXgD4BluGBWMoSKa2VZ98bkEAMdRSb3ZbQgwJ5w9MU\nyLdNZoJ0tg7SeFV/gOu5cj+MesQFRm/q3+VrfDQVgZGmO1ZB++m7DSpLV+t2\nksa6UjdjiaEp4miJ3mIxE+FyfQZDaV/dDeGTViK8a3r6LkOR6LnizxtYGGA+\n/UGtDqnHntEUmtGgB8l++97S6AeQtvYhBNTtUrLdkodGaPaUbgCPOTCsYR06\nPe1DXY/YOUHP7teNZHYJZCmbjZtjLEHiVti1yompENPj5+LU8ZgYaXPndKqi\nmcoHJpSwbo+9R3WrIdVNMwKNpLzeAQoOtQrjNybKUTIeksp6aemlF2JBOc0t\nOj/T\r\n=GWoi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.4.0": {"name": "@discoveryjs/json-ext", "version": "0.4.0", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "f88244572b887d8379b7015f88e58f68e13806c2", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.4.0.tgz", "fileCount": 12, "integrity": "sha512-duX5JTqLWZYpcAxWmDGXX4nm3LDaVCgl/yMSKNXtpzbM5KAm5yQxvtFByt1+SJQDies/lJBK0sIFtqAyja0cuQ==", "signatures": [{"sig": "MEYCIQCFPugkaABppmpnubRGjiL2jvdznfFLdTrnnIOVjOShiQIhAO95OCvbK0pmHO0fld6AIGQIcxFZTqhRjH7O8OSMzvuO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyr2BCRA9TVsSAnZWagAAnu4P/jzM5jI1X/oZHOkYobKE\ncHjA5tYQ/hSim44oau6vVaWroAhAyN8U/jFUHvPjNlaVXHjdnqw/yQN14gzB\nAeeymIFMOuEf30nmSfcejTGTB/pePp3Dcv0dmw8ozU13KC0SFz1o8UGExXDp\ngZUIfLeYngs6zcpX5mzZ4bYFDM56nnnsP0Oq9RR7OBRc5GIW25/kCFZ9WNYM\nsthEpZtvms3lqJEwoGmLfnpe/xBRNaGcnL8fs/lEkHdZvgRDyEOgHM3TZSmr\npYI7D0fuqIJjR0xBrq8XdTHFa8pxAtZuiI5qk+Pc47vTWYngX3NPMfrONkSl\nqKWcNww3Q7YpCvNnW4j5aNhtS6BP1wVaUmfOaozBXJIplW+l7Y/Dr93DjvSO\n0Y43+ihyg84z11Ab3PCq6UE9v8jfrpF9roG5JHQw0P18exte/b0Me6lileN7\ntnBgfESZovxj0+bQG4QIcHoEYfW2GUeeaH1C9Y1uz0cSEb0SUhBTGNIJxAdV\nDDExFQ8hT0ciJSv6zMrW8cbOhcQmjT/lJeIuzA0mIjg7KhhPcudAeH3pwKXE\n4Uwc87+GqE8CPjX9iisp+RzoR1fav1GNdheyuIAi6g/RGW8sXb52ZSdOFnEX\nc/fT+KEvnDbr2luWn0O9JB6eO1GbVztALvz4QePA5Bo95VzI8MhTaDXIy7QZ\ncW5O\r\n=SyMd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.0.0"}}, "0.5.0": {"name": "@discoveryjs/json-ext", "version": "0.5.0", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "d6cf8951ceb673db41861d544cef2f2e07ebcb4d", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.0.tgz", "fileCount": 14, "integrity": "sha512-gX9Hx+2BMP5+yXfr4Agb+iBd9YiI729x38wbhfvRSkxQqfXnJUNy1nnyJWetYCvxnL1caWd1HqJnbQwVrgvgeA==", "signatures": [{"sig": "MEUCIQChsG6ZEDoKm5jCufNNIaLCqz9mho8Tted7+goP4Fn3HgIgP3yjCzELf9OtX9GovjiTOfjQ1e4Pxp9HdvC/GTe37Dc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzA60CRA9TVsSAnZWagAA5vsQAIljXo2d9mi/p82SMtIE\n3ZgomVS4f5S9pvxIwqQSKhEBGFZFrvYmMi/ju3EFBYqRBtcvIog8Ov5fsovs\n7PPG2McvJcTf5NwpJyaOIPr0ELPpZOfjdx9fEqjAwrg65g/nRzIA7oxG1FL6\ndjcLAs0x16DtrIiVfPtt0EVrtZjdABGLhPAGuBFqxioKsGX2VDeEaSsKxJxj\nhlPNCjHLBtJkWKYIWw5vUoNrrrnd3mYBaC7Lh01J3ARtebxM3b0gUO/hLj28\nliHjyBgNooAOdKGWAruvzgE5agpU5kZ8Vqje4PT1jM5m3I5f/N2JZdpy3X8V\nezyq8/PpJNt88p1QOikSGcPLQV/iuv2elTj04ALe09XjwmwKxQTwSPcLckBN\nkhymUj4hLp5GNE8OduwtRG5m6HG4QMWsdOhBzwfvMzWmPo98ltHSI342bVUe\nEmLINhgV/765/S8UW/v1leGn/uNW6ha/IFBp5/6e+L0HzNZBqgyXBrowYa+7\nma8Qx6mZL0sAy76EHH/pCIHJhQMtVyBACaHGN5LrtwDgfGi/ugX4Wo6WqC7Q\nUpiHt08A7wg7KLpwk7FBFLxx3QiN6EfcJpqL5qXJ/jgbB4Hvet+nH1g3KOwN\nHVKSILcf8AwKFhQ74EFmQItjx8D/tAygRLhAsD0YJRXmyuvX5S0UyX5051Cc\neJMg\r\n=dYqG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.1": {"name": "@discoveryjs/json-ext", "version": "0.5.1", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "cf87081ac9b0f3eb3b5740415b50b7966bac8fc5", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.1.tgz", "fileCount": 14, "integrity": "sha512-Oee4NT60Lxe90m7VTYBU4UbABNaz0N4Q3G62CPB+6mGE4KuLMsTACmH8q3PH5u9pSZCuOdE9JClJ9vBqsp6DQg==", "signatures": [{"sig": "MEUCIQCMMeeoeSttbh6ZH2lhxfDiXHucGwJ2BFo3uNRBiLzotQIgVG5rJoTItLCUGwTmTud51LM8eQ8DYX8rnsMBRiRCrMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3R9fCRA9TVsSAnZWagAAww0P/i+CvkWyLfHPg6bv1Axx\n5F+W0iMW1SpzNholHgmtQrrYprbrNW3FOqvtuFs3wWxzn4W3ObVJlvx/6w0b\nCSxN0bmw9a4+Jxu3SBTr0Ie9KS80gL//M3/5gd/CHRI+HKuZ2rWbBGgaQxCR\nKRJS7Boz2f8cwx4kvw+bX2ueu7E6Ed3U75Ph4H04v7zbWILNaTthJuPZUKd8\n+ydrQnf9yApjIffXVDdzjGZcOP4a3mbC3QkqAEDBtm79UY0WiIs3OX7A0b5l\nLbry1wnUKbLAD6Kxp3jUDi2aCPofMI5t8CxB+I4ZhrUu18dGe3gjY7MRIYk/\n70EKI3DUrQeJ+hHOqzzyrc7B1cULYSTgBColFKUU0B6LyNCntpBP8Vk0gYbG\nk0w0l2NGE/xIHYiYQbpf+uMHPEYd/S4t0ZfSzlyIHpBRmAyQ4gv0LMEiI9HX\nLpxo8jVNqxHhi5VAkwg9AwDcT+SEAiYpilQ09EZToG9ZvezR1mRZ4fF1dMIu\n0zI9CTFuSSi3FhEjkYzzenXIDfMr7PBr1PadhDzCcmPquehZY7QFE3mflhmU\nXHfrSFeYXPPEnspUwmGtDzaI9ownrWSr8A0uCQbqHdrFyl2jivLaA+H7bn7g\n4LvZkajKA9F99g/1LsjTzJq9JHbmSYc487ts7qxRge3rLm1fdj5UU5BwQ+g0\nmgTX\r\n=7hoQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.2": {"name": "@discoveryjs/json-ext", "version": "0.5.2", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "8f03a22a04de437254e8ce8cc84ba39689288752", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.2.tgz", "fileCount": 14, "integrity": "sha512-HyYEUDeIj5rRQU2Hk5HTB2uHsbRQpF70nvMhVzi+VJR0X+xNEhjPui4/kBf3VeH/wqD28PT4sVOm8qqLjBrSZg==", "signatures": [{"sig": "MEQCIDG+Vb6kc5EJZUuBDvjyAR493GdoiqPUBeKNsDDUjqQ1AiAbTfRH6Y+hXDxRdzN7AliuKgBw/8+5aztXIhfpB2LXWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf50tqCRA9TVsSAnZWagAAXi8P/ibeFAQdGjiYWcqqkBs+\n2dQHlAsDN63fDkc5zus5J8zF3YvRgQ/FhREzjwcMsDeETwuN4rmK1oV8rtUY\nf1g6gCgzdTWYGU34n5QjxUC3+p6NRlfN9PVAkk5qiIxYbOHQ6sP4joaPXE2C\nOEAb5IeHMAQ8hUsgmwHiANzvu1jVt9s32dmSQZ76g02g8M23FUTU6QbWdt0T\nCXlK8ANqwjnPpgfrbu3SxPQmrwp4iZv9I6xrvVbmEWGRxLFcvDx8uo/369k8\n2H0ec7l0my5MxAHRJR+2BzI8jP1fesqlVIxjioN9vkOHYqVv1x0eQ4aaPO+y\nsFAHLyWSVsXwdZI1an2g6l54zYwnuz7NEBQR52V4fFNn0DxM/Bxo0nGQo5Cm\nGNiaj/ppY1FCNlHJ2XMVVEcuKlZljr47IxpAQ3/tNBnMwcXLuH5hzR3scR7D\nryf3nj507r0NxgLkmtplamc3NaUXyFfGldbmlwC++DyJWZc6zKpXMxBHjFLt\nTEghKCxuqjk9VXKsICkQ0p6BcUF8VIHCxqHpfdslgwXPRCy8msuyq79lDu50\nsdTB3gV5h6SPCOTAqeah0Gp0TcSI3xJnHHPkJO7Zupgz/CnpJ9/Oa9JRTlkJ\n1jxhYzeIRQnsEFt/jynRHS7LnT0Rzkwe9BfcfQxswqRNEV3WmjA4cs5XRqdL\nBzUS\r\n=jmMa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.3": {"name": "@discoveryjs/json-ext", "version": "0.5.3", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "90420f9f9c6d3987f176a19a7d8e764271a2f55d", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.3.tgz", "fileCount": 14, "integrity": "sha512-Fxt+AfXgjMoin2maPIYzFZnQjAXjAL0PHscM5pRTtatFqB+vZxAM9tLp2Optnuw3QOQC40jTNeGYFOMvyf7v9g==", "signatures": [{"sig": "MEYCIQCQM5idK4xqsRJrJj2cU/IBocPRe5wgjt5sw1z+YM2+SgIhANUr2+AY2tQSqqH408G41AMaBaZZfUHA5G6vE739euUG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnZgfCRA9TVsSAnZWagAAcv8P/0shVx889KYcO9apy14q\nbAtBuYEuxcQ8xXqKXqp7KPiwBDdb/TyemElKzMGsXoLO3lTeR0TJqZSfFHUd\nib3aSzzzgLAfPxViMprnbVSzMo2UEkHP1CSwR5NHje8Fb1d3abaXGfXQPBYk\nHA/aV1DLv/yGS5mytAV3JCE1+ehlLyKFOweLNO+Z9fwtL1njwI6D/vt/9zP6\nIBbdtQrHHkVOIJ0EYf+NPjNf/5pdtu7tnIo9kWp9gkw5vkgcQ0TnptXZ88dx\nRocTy23Q414tqxlFxyGesU91dmGYc3Fyyfy+VPVRYUBCbBnkgU6xJZU6+oIS\nImif+tKIlv/eHoBxFX5NvHQdgfP49NHxOYDygn83m0NUbTUmyMhKYJdW4uYF\nMiv2rtXXQjXI5Eo1svtNFSBdbQAGSO/7x1vuhv9got0yoq5RtwuEmWYueGe7\nNI2+y6+hlud5Y40nyFpzSJ5/fn+RywYSqISpxInvANmHOqjdu0u+TX/nkzWK\nMTG2a154EvmlvGS9z6h3AQQnd95lU2Hl5MGLoXf3x2g4ju90/jXVbWWDEJUu\nk4PT4fYKlEYCwgPNzfgfemn4Mkuuv1KZcaaqy/a41kyAvufqKztkGTESX+47\n4T9cqjuP6QxWIGqDjVgQ8jY+xaPoe7caaSIYAQPG5x8oz7DhNHfVoXvXR7ni\nQ9BW\r\n=66lR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.4": {"name": "@discoveryjs/json-ext", "version": "0.5.4", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "d8f0f394dec7be40b5113cd78088339c42ee811f", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.4.tgz", "fileCount": 14, "integrity": "sha512-8szzeplTi6qw+dbzBB/I3T5TzU9GM7AxzQkiiWdakKsCnaMlMLUMElfEhqT1S3PfdSjHBwzPUTcfWyFHdf9FaQ==", "signatures": [{"sig": "MEUCIQDCYUSSM3XZpJzpgZP+KSF2yDLzGBX3B3HHWSRX4nulLQIgBdc0cYos/zoBy6cciHPOCxmDKzd6X4c//62Q5799lVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQLPwCRA9TVsSAnZWagAAN/sQAI8rpdZ2Dol+wSnLD1ZT\n7VmBqELatqZ7nozDCbE+zLQ1As0Ru/AkggYUXP0DRAgdXdc8koKOlXL0PvRq\nhKxj4Ir4764WwfQq2QwQtM9B/Dl3uXIKIm+gzrKllj+5zaBBo1uZW33vLqmG\nQrdsGElfSKWOVEIixxfSmceMjucZE7/KT+iR+0jg9OgK5dUcJ4KOuckQMnGo\nV/CVslTTGNc003C8GXuhDDVG8TY7GkXwO2LISatVKWzw0LydmVgYw8TWJ4HP\n4MhmaBxn6wHk9QRDrCnagMW7Rr8BAEA0UBQx3wQxizxyEHCfXKOFzhs5qZT9\nbSXvQpBPZ3LlI1xXnlC1FtpRRSXrvcGsdEtkQ/1vZnDOcCczsGRI2P9oVh8H\naY8bc2cuz/ETir5NTC7JXIniLFIG69IHiQZkjYpmbQvMaHiVBk9/r3VXHqWS\n28WR5B/U3qkEzZVXqnoymSbkuPnuIDbupX4rHQuvIgU9Z+axte2rOVhjOv32\ngQM4jpRWaZd9eTaei6Wvv1w9U4L783ueUbh3XObLMvg7U56Ki4yzeu2MkINZ\n7dpDM9TSHwWq31m55/NRRhnqRY2KZ7NdwHHz94Sd4JL/edgM/e/AF6ckUPn8\n+b2IVQ2a7iOb1Bxe3ofIKc2gIZG6A+C3aMnmKaYKtrqr0UUga68INT6sdUF5\nxg8E\r\n=/gKT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.5": {"name": "@discoveryjs/json-ext", "version": "0.5.5", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "9283c9ce5b289a3c4f61c12757469e59377f81f3", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.5.tgz", "fileCount": 15, "integrity": "sha512-6nFkfkmSeV/rqSaS4oWHgmpnYw194f6hmWF5is6b0J1naJZoiD0NTc9AiUwPHvWsowkjuHErCZT1wa0jg+BLIA==", "signatures": [{"sig": "MEUCIEpw2p05KxO7Tg2ot4LStfNrJEgn8Hj5n3pZtaxBiDMNAiEAgle2HopcxbeIAfOlq12oLrO23HACkIcC/aX/dvlziNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQS+sCRA9TVsSAnZWagAAoeMP/j+tNUKXy20XWW4Hg0oU\nTAmdRL5vCU4A0jShbIV45j7GLt3tO8JKPgS1jMzIZEVL2W0gyoE8CVITrOqG\nC0IdQSFGH49QZl4xEi7gfC8xY4QwaHGN0ySrnYJ+BwT3eI+BvQBeaMceL0NA\nqVKT6to+4gT3vH+tDuIU+H9KLIG5aMpN1VfTj+J/SICOeNOw+wszHPRwKpmH\nzmfxGCYLyXX5lyOyTm6M4M2Iz6oXx0UAoeJSy9tPILbacfaFkLSr3qGHoRDh\nAQxbcfITA1luqiBiFbcLeblcsoOFEF7toWJ2lP0BXODTuIv9uVYulHLbRi0L\nGY0810C/5muc/uh4jAa4EDQptZYQuZyWgsTKgP7NzI662/K1IRdqCJpq6sI0\nkyVDTwmjVSoyoZruyqQTmqSrUSPC9QMIx1edsSTGHd0TYdW3LOALpeTpBPdA\nMj0Fi04UbwN8qKH716MFdIxmuwJubcGpNRs92baME91ZGCgheOqFwgvNhDSf\nCWzk0V7omZXsEOoMKm2WpDdGOLIWcnPijV4AqJTthfpBA3LflOHRF4b6Znz4\nFwxs2gNtVze8tl3AZXc7EHySWjKhQop5sLvvlmQ/DexJZYjp4cgJKethCaIu\nfiUpAUkIfV7xJ/R9oAcOHZfuG6Sw5GW3/O782NAS07Ku52RdzCRDExKohKZM\n3VyR\r\n=82DZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.6": {"name": "@discoveryjs/json-ext", "version": "0.5.6", "devDependencies": {"nyc": "^15.1.0", "chalk": "^4.1.0", "mocha": "^8.1.1", "eslint": "^7.6.0", "rollup": "^2.28.2", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "d5e0706cf8c6acd8c6032f8d54070af261bbbb2f", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.6.tgz", "fileCount": 14, "integrity": "sha512-ws57AidsDvREKrZKYffXddNkyaF14iHNHm8VQnZH6t99E8gczjNN0GpvcGny0imC80yQ0tHz1xVUKk/KFQSUyA==", "signatures": [{"sig": "MEUCIQD2XuT09XthvJPHi9nJJbYKTyXf5Q6yYH8MtSnMEpLfdwIgMxCkZB7QQwCfDgbHJ/neClOkZcqz1SxHKr5xemopOGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpp+8CRA9TVsSAnZWagAAtuIP/3tnkcjXUk3JlQamZa8H\n9wmm3UWBCyBydbMZNAZqv6rfTmXF/DIqz1b3Hs1Ll0Laf8X+NmBqAeZmlmzs\n5udSilWBUkSl9t7XaDeaFRdpSyKj1cl4vbkWcOqawqJ8J4t9x8/VxgAEUwH7\nBbMY7pkzRfHmY4c3A0I+pjwvhOgN1LK4I7yebx6G2PuWMm0i/e+C2k5jFmCq\nQrgtS955h0cwdEpG17EIC0QsXbfn1mPPozUKmOqmniXQFRY+UfoXtBBY3GeQ\nScorPtDkjM+TCwFPa+37fUIT3Ml5NVHUgu2OtOMqBpOO17oLd9r05jlu5hhI\nQhgnwcjZ4P3Bi5i6WPXqm6KdTJ1HdADw57+SpN301nnBu2+hFPROwguFNkQi\nag0UGOYpVioVZotLH4NdTYXxmrUzUKuo8t0aA5N4dhmG8b1v45zhDr1qYHXU\nymrmn4o0CFTrQOHxzuspJDnrvVwgQ+J3tFnAFHrClNYARCzyGnWdopqCxNPY\nJkj4EDo6B9kYFOnq5atb6UwV/NqLgSzZDr/rR3QjSVChYenQbTC245bsksqL\nrAGIqmaT2c72WtavCbz3QNetaxpgGsZKpEuNjrUpgeRzeQsMvg6CY4fy1PMw\n+30IjhyOSxjmPyZjndlYVGrWEBzTzNpNdaZWEcMmclERYFuljDYLpbsQR3Mq\nqFmk\r\n=lnm8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.5.7": {"name": "@discoveryjs/json-ext", "version": "0.5.7", "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^8.4.0", "eslint": "^8.10.0", "rollup": "^2.28.2", "cross-env": "^7.0.3", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-node-resolve": "^9.0.0"}, "dist": {"shasum": "1d572bfbbe14b7704e0ba0f39b74815b84870d70", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz", "fileCount": 16, "integrity": "sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==", "signatures": [{"sig": "MEYCIQCMRh+QfcpLEvtci2j1eZXmUATBfCPbBCeZ1QdruQafIAIhAPAAJmIblhWs/z6xwqlinepC+8c+F8tIndCTJL7taJCq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKOPIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8tQ//chYdflfnb6VX6LE0vffZe+yWqCtDGVUuEx3qSej4DQgK8deR\r\n0k+6U+8sIvLBkv9MWZ4wuloD/zSi9ZtwKhV/WMQyMxHQiR0K9RC6ni7MidB1\r\nW+Dxy9qXkkCLU8PS/UY7MZ9QhP/pdkPdVPsVzW++AfDYpE+W4GR/JyYUZujY\r\nz5UQTfptOvUZ9SKq5E+bMWlQQXrPsUKkbDnt9PckFb+FKCMklPE/9BW/6u19\r\nzFMtpIdmQVZAG6X8QCoWlzEKLxcVhgtMytfoWp/DTLDtQh3A81W/GJi5izvC\r\nKtDdilQJm+4E/+oqlLrNPm14BluCs3uRk22Cn/0C2R6a0Y32rYPOIU/WMV89\r\n2t0OPgMUuIyQtOL4uJ34yqUNxBekiEJSGmtDkJU8xOyCGDPDpMNNYIGjudXZ\r\nKKTikWoFbzCkpmD4iZFQiBqnOehZJOehOj8MSK4Td9pRl5I5FDiX/cIoT97N\r\n/ndkbvskuAvGPYCH9/D9caqaBiWIWgvTNfUe73NeskJYERdiaT4/1ZxcuPZK\r\nhgJ5dkZOYC+6LLYSKhJjbvqSSGszumxGaWuGuxVxzL0X19hs/aQDDOe/uFEl\r\nKSNYOUMi+lSZ6NGjiVgkp2Au/ZMLh/REm4yYJoSZnx+ImdvBGV9j9sBzKzfg\r\nN3r/ILAbAVjAz2FbHtwKjLIdcRozCEC3Fsg=\r\n=eWf+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.0.0"}}, "0.6.0": {"name": "@discoveryjs/json-ext", "version": "0.6.0", "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^9.2.2", "eslint": "^8.57.0", "rollup": "^2.67.3", "esbuild": "^0.21.5"}, "dist": {"shasum": "323395f46f8c9a10107be60574c0f8ff8d802220", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.0.tgz", "fileCount": 20, "integrity": "sha512-ggk8A6Y4RxpOPaCER/yl1sYtcZ1JfFBdHR6it/e2IolmV3VXSaFov2hqmWUdh9dXgpMprSg3xUvkGJDfR5sc/w==", "signatures": [{"sig": "MEUCIDwX3ecPaUWJN7EqxGCv6c4JdfYi+/C7aei0yxLL6z4nAiEA8yT7waUVLt7hejPkqbYu8rCcGFw93AXRROkCV+yZrD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142960}, "engines": {"node": ">=14.17.0"}}, "0.6.1": {"name": "@discoveryjs/json-ext", "version": "0.6.1", "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^9.2.2", "eslint": "^8.57.0", "rollup": "^2.67.3", "esbuild": "^0.21.5"}, "dist": {"shasum": "593da7a17a31a72a874e313677183334a49b01c9", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.1.tgz", "fileCount": 20, "integrity": "sha512-boghen8F0Q8D+0/Q1/1r6DUEieUJ8w2a1gIknExMSHBsJFOr2+0KUfHiVYBvucPwl3+RU5PFBK833FjFCh3BhA==", "signatures": [{"sig": "MEUCICnwFSVTXzPpvxPSzOboGEZRyT6G1lwUKkSyyMeDNJT7AiEA6K3adZoPta18cGbltYm3yhx3LduTiH4qez6nGqqCv58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146004}, "engines": {"node": ">=14.17.0"}}, "0.6.2": {"name": "@discoveryjs/json-ext", "version": "0.6.2", "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "mocha": "^9.2.2", "eslint": "^8.57.0", "rollup": "^2.79.2", "esbuild": "^0.24.0"}, "dist": {"shasum": "b3bd3373bca66496ad62f2aff992d070e861d79b", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.2.tgz", "fileCount": 20, "integrity": "sha512-GHZT40sAqBY7qdKaD7XtaohbX00VDfWjX7A6d0c/dc9bR/2h5I51cVh+TbNKCytBkfV+L+n0bR7OZWNt5r4/CQ==", "signatures": [{"sig": "MEUCIB9Lxro1xU/qxqTKeX5iiawdn1/5HYyBthDYs/mKM1z7AiEApHH0O+wxslb9/YR3S801ZSbLeADyEvyuCG6sdK4juc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147958}, "engines": {"node": ">=14.17.0"}}, "0.6.3": {"name": "@discoveryjs/json-ext", "version": "0.6.3", "devDependencies": {"c8": "^7.10.0", "chalk": "^4.1.0", "esbuild": "^0.24.0", "eslint": "^8.57.0", "mocha": "^9.2.2", "rollup": "^2.79.2"}, "dist": {"integrity": "sha512-4B4OijXeVNOPZlYA2oEwWOTkzyltLao+xbotHQeqN++Rv27Y6s818+n2Qkp8q+Fxhn0t/5lA5X1Mxktud8eayQ==", "shasum": "f13c7c205915eb91ae54c557f5e92bddd8be0e83", "tarball": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.3.tgz", "fileCount": 20, "unpackedSize": 147995, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSQOOh3e56j6KLZ+rZpw5j5/WRjH+ysunX/NtK3qELwgIga4cwkDWfmRtS+7v6XqVZs7/kSzMNpXY4AuELyCGUtHY="}]}, "engines": {"node": ">=14.17.0"}}}, "modified": "2024-10-24T21:23:22.958Z"}