{"_id": "lodash.isinteger", "_rev": "10-46bfc785a8d5ee08ad73a7df5de0dde2", "name": "lodash.isinteger", "description": "The lodash method `_.isInteger` exported as a module.", "dist-tags": {"latest": "4.0.4"}, "versions": {"4.0.0": {"name": "lodash.isinteger", "version": "4.0.0", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.0", "_shasum": "ceb8348ced619a7d0fb4883332c7902130955a40", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ceb8348ced619a7d0fb4883332c7902130955a40", "tarball": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.0.tgz", "integrity": "sha512-i4hTXknLPCw5Kw3KgnUJ9zFX6iZbSQzn2PPP7/2iivAfzSExWfOlSRe7XvyV7vympSyh3qfyPSRHy+o6GNVuvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqL2b+JeY9brUInIc74sLdpUAimLcH6wMkN6P0yYf8kQIgIQZ3KKfd17seu+rlALsan2MqRUUaE5NDyGD3FTVONjE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "4.0.1": {"name": "lodash.isinteger", "version": "4.0.1", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.1", "_shasum": "083f627e30538d51d7d27dfb3f1fb0e2a14e661f", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "083f627e30538d51d7d27dfb3f1fb0e2a14e661f", "tarball": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.1.tgz", "integrity": "sha512-yrcZDPS2fmHFNZEpacyZkHGMRWgQYPAGIf/V6VD6Kd61l+zzbppATNVIohX8oCdwXDJe69NDWzJ4jHeHNulcUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBFydyNdj8FSCOqwkq72iKTAkF5K/XqvCMbVEOfM8/d8AiEAmkWb0diUQfKIq107h+RcSYBx8Qp+QMfRMl3NjoP1mzQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.1.tgz_1454484515482_0.2861711150035262"}}, "4.0.2": {"name": "lodash.isinteger", "version": "4.0.2", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.2", "_shasum": "6d5cf9c18f4c811ca50a5377cebaf652a394801f", "_from": ".", "_npmVersion": "2.15.2", "_nodeVersion": "5.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6d5cf9c18f4c811ca50a5377cebaf652a394801f", "tarball": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.2.tgz", "integrity": "sha512-YSww7IGgaM2yltelBVQvxr3FKAL2QD5odTZ3/TAm1b1cZr5J8SWo1r7R0s7X3Uw2JRE2dAq/3jo7tN70ucZw1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjSzJrqGvV24Vy8QEoL/efDnIGC4MJyNwQzAHd+TkGggIgKq0HGpUszV0xhyNM5oHIHRPbGIfNAN+HkhOoEGnFsv4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.2.tgz_1459655399707_0.9143900412600487"}}, "4.0.3": {"name": "lodash.isinteger", "version": "4.0.3", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.3", "_shasum": "dda95f19161331a0ef136d9906fe2308b8fa1475", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dda95f19161331a0ef136d9906fe2308b8fa1475", "tarball": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.3.tgz", "integrity": "sha512-ZjzKxSNhzv0hTAeEwTY4vSuk1fbeH7OrAIwmWZu0ax4l8ba7g4dF3/ywNbBEl4r598tHIjyjm/ktwvnHd7kVJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE1tbb2DwqmBXp74nDsCXlFtQXAKN7aaEirdmdT9oOz3AiBrRKvp9CJmrMWHNFdQB7jne/TneSeicd7VLWhx9kM3lg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.3.tgz_1463062306476_0.03799888282082975"}}, "4.0.4": {"name": "lodash.isinteger", "version": "4.0.4", "description": "The lodash method `_.isInteger` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "isinteger"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.isinteger@4.0.4", "_shasum": "619c0af3d03f8b04c31f5882840b77b11cd68343", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "619c0af3d03f8b04c31f5882840b77b11cd68343", "tarball": "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz", "integrity": "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBUs/lSEqKQB45288dF3BbBIjM1gsk17FZdI7SShwPAvAiEAo/alcQ2S8QEL3TdyNYPN6Zh4UUS/lQauHvLNj0gXCaE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.isinteger-4.0.4.tgz_1471110038989_0.8096776001621038"}}}, "readme": "# lodash.isinteger v4.0.4\n\nThe [lodash](https://lodash.com/) method `_.isInteger` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.isinteger\n```\n\nIn Node.js:\n```js\nvar isInteger = require('lodash.isinteger');\n```\n\nSee the [documentation](https://lodash.com/docs#isInteger) or [package source](https://github.com/lodash/lodash/blob/4.0.4-npm-packages/lodash.isinteger) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:35:58.930Z", "created": "2016-01-13T11:05:12.068Z", "4.0.0": "2016-01-13T11:05:12.068Z", "4.0.1": "2016-02-03T07:28:36.257Z", "4.0.2": "2016-04-03T03:50:00.330Z", "4.0.3": "2016-05-12T14:11:46.975Z", "4.0.4": "2016-08-13T17:40:41.538Z"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "isinteger"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md"}