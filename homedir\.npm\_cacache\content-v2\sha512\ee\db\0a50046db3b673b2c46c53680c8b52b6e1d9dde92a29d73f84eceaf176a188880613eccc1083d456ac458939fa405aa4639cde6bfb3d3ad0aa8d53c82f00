{"name": "resolve-cwd", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "resolve-cwd", "version": "1.0.0", "dependencies": {"resolve-from": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "4eaeea41ed040d1702457df64a42b2b07d246f9f", "tarball": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-1.0.0.tgz", "integrity": "sha512-ac27EnKWWlc2yQ/5GCoCGecqVJ9MSmgiwvUYOS+9A+M0dn1FdP5mnsDZ9gwx+lAvh/d7f4RFn4jLfggRRYxPxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvRXG66N5eGwm1MH+1D055APBqbioFySTKvEtpoc3JpAiANnV8w2X5llo87s/VzKDmfURCs1GDGRXNRsugvsShaDg=="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "resolve-cwd", "version": "2.0.0", "dependencies": {"resolve-from": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "00a9f7387556e27038eae232caa372a6a59b665a", "tarball": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha512-ccu8zQTrzVr954472aUVPLEcB3YpKSYR3cg/3lo1okzobPBM+1INXBbBZlDbnI/hbEocnf8j0QVo43hQKrbchg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDY79Fl7Om4iSlp8CAYfHlSrgenglOFnISfJxOVr6LQHAiEAtYzUoo/wecxMQ81kUXRjc/Vt0piPWUQlu5NixHhMGaM="}]}, "engines": {"node": ">=4"}}, "3.0.0": {"name": "resolve-cwd", "version": "3.0.0", "dependencies": {"resolve-from": "^5.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==", "shasum": "0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d", "tarball": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "fileCount": 5, "unpackedSize": 4984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyEVECRA9TVsSAnZWagAAuD8QAIYlycAr3jQxT+pyQrA3\nYA2rYG6peBfZUdagDFOLMY+8z8BWgTDoCip9PSMqUCcUF79oHgYrso3xbO5C\nDUYnStY3bNqsoP50YPbvbo5dkbwh6lMrEWuOM2/0LpqrmzYaScT3YBR6EcNz\nyO762gsxVpecLNlDVHP55LCrB9m5dFCfuN0SUAizNj6qH6Lgaw7jjtX1BUnH\nXhTi95SAgPm8uKwPx/N1X4ffouaa6vKYBDaRZ/qOQtLEqL2l+yPWl7Tin+4d\n2dlrKH/WIowKGoPA2jZT2XO9JWb8hNg5zgOK9ILkQRmNcjGPAqI91DiGeyNF\nOxJcXtq5/w+z2iAJCRZvahJuyj2Rx1nUGsV1Utl4Vpdv4KBpVgwzpl47epIY\nm587ogdMvPFfCxjBpmY1w6CPb7wqUUY7i/MwjpAQY4QHkRTxOpkxSo8gJZ9y\nj5CL3EM9kr9LrB7zpTNCSsx9q1UBsXo4HuJJZ2wiXC8b5cJYT0gr4+dhXKTQ\nSCWZLg0/oHJ4N6t80bAHs7qC5OYj3szKscKyFkFCQoDQmsbd8+DIEhraWMmt\nLBctFgf/zWPOEiWEbGjs6CNoR7e0wwwHoNcHCkWaOPOb5fMzEn4kJI4Q4oWv\nEmS5A7wYPBrYJ+9mPv534Oyxcoqs+pkUx2KATvoym6MONHuEl7v+KY/j3Vi8\nvDha\r\n=FN61\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEAxLl+ieUhl1z/3Sa3h9fSYhyE4sBRbI9q4p3ekDkqzAiBkGx7tYLK1jNFOMYrrLTr1Gf4UUW12m2ou3uSdrUxIhw=="}]}, "engines": {"node": ">=8"}}}, "modified": "2023-06-09T21:38:04.121Z"}