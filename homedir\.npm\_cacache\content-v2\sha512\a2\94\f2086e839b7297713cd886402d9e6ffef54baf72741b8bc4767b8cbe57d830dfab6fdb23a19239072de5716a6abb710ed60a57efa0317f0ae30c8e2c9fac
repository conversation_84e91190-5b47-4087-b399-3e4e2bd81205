{"name": "shallow-clone", "dist-tags": {"latest": "3.0.1"}, "versions": {"0.1.0": {"name": "shallow-clone", "version": "0.1.0", "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.0", "lazy-cache": "^0.1.0", "mixin-object": "^2.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "4e03c6111a885d8611ddffa635c1ef8052527234", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.0.tgz", "integrity": "sha512-RY6LNQvFdswI2wZ5tc9tu3KI+1N0YwrfdIDXqzbcOjSjUz07ik7+YUHDE4S7EZY1/9C1EED+UTEiW5Z8nJt5vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEGWwvqxdHNt5UEYOnDjpZAxGDbes/2NSKTNGzlstIJrAiA/eVlhBdtyLqASEhb5eCNXvq1bf0Kqqyk4Tt2RYTTwyQ=="}]}, "engines": {"node": ">=0.10.0"}}, "0.1.1": {"name": "shallow-clone", "version": "0.1.1", "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.0", "lazy-cache": "^0.1.0", "mixin-object": "^2.0.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "d114e09ff7c3b198e92f764277c28444a219ea1a", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.1.tgz", "integrity": "sha512-edGqnvFjNmrpRpzvGImm7zX3mDLjPn65SEb94O6mMG7vEdAVMTVZ/0qvLae/YYnHfPxdQ3oobnPRJRVa+wC+HA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGSufbBIVJesXbH60jE39c5RRHkPTlUc0bCeZVbps06KAiBCVyR8TdykXPUmFA80xm3Tksa2R7A6DQqQ7eCBi1D0vA=="}]}, "engines": {"node": ">=0.10.0"}}, "0.1.2": {"name": "shallow-clone", "version": "0.1.2", "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^2.0.1", "lazy-cache": "^0.2.3", "mixin-object": "^2.0.1"}, "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "5909e874ba77106d73ac414cfec1ffca87d97060", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-0.1.2.tgz", "integrity": "sha512-J1zdXCky5GmNnuauESROVu31MQSnLoYvlyEn6j2Ztk6Q5EHFIhxkMhYcv6vuDzl2XEzoRr856QwzMgWM/TmZgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGqzsyyZhDZTZNQjwdodDILy5MbbQKJSnD7I2vnU+qXzAiAlYuj0AqH3dXiO42lU8FkUPMfa3cu/2B8nkLBagBDn5g=="}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "shallow-clone", "version": "1.0.0", "dependencies": {"is-extendable": "^0.1.1", "kind-of": "^5.0.0", "mixin-object": "^2.0.1"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "should": "^11.2.1"}, "dist": {"integrity": "sha512-oeXreoKR/SyNJtRJMAKPDSvd28OqEwG4eR/xc856cRGBII7gX9lvAqDxusPm0846z/w/hWYjI1NpKwJ00NHzRA==", "shasum": "4480cd06e882ef68b2ad88a3ea54832e2c48b571", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNZk3ixT/mza0eGjyuP5ugeGJFzF9X1Zl6YQjgeJGhYwIhAJ22WrLg6VFUj1KE2Mp415mwieg5gkPm12scUJtC74Gw"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "shallow-clone", "version": "2.0.0", "dependencies": {"is-extendable": "^1.0.1", "kind-of": "^6.0.0", "mixin-object": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.2"}, "dist": {"integrity": "sha512-M0ikAzIEgiQqFG8gkkuM+sOiVVNSza7VjcvP+WfB8HvYccIFDKt0YIwlx0bbLsD4eiya8ARd0rznCbES9fXMqA==", "shasum": "5d0f491e313686b2f53660b8859ca33684f18d20", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBuI0TgAXEk9cmLtzFSRQLRNwHEDBHgytTiZ9KXnv0nwAiBBecRGqhlnyt/LL3QNk/G/pfRxrBO87B5ozWGrcym34Q=="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.1": {"name": "shallow-clone", "version": "2.0.1", "dependencies": {"is-extendable": "^1.0.1", "kind-of": "^6.0.2", "mixin-object": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-PII/xIinoENOGpZmTPspQTqSZxfbOMdvGKwGpskewVlFK5YKhe4bHIIGhdsGONZm+N9zMga65YHcn1ZZUyK+/A==", "shasum": "8f33c2b957eb5eada9346b91883184ae1a2395cc", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4tGAv4RVVxs9Og84fOrxxdQVY/Q/m46ctrWcnSg8JNAiEAnZktA60M3DVWqI3NEgRd1ZAhXxuVBrBnoGCRkmyk6jw="}]}, "engines": {"node": ">=8"}}, "2.0.2": {"name": "shallow-clone", "version": "2.0.2", "dependencies": {"is-extendable": "^1.0.1", "kind-of": "^6.0.2", "mixin-object": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-2o81AG/RpLTAG/ZXQekPtH/6yTffzKlJ+i6UhtVTtnP6zWQaNo9vt6LI28bhZLSesB12VQSfJYtXopTogVBveg==", "shasum": "7f6e9cf3b64e37d5f4afb0f648a0204da556b872", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwFW9GiqDCXaek5vo7F4e22umsPQ4WR2Ai1xLiykBsNAIgBKm2/NQ1NSrJdQ+V8h2mh8My4FeWtW710vcjk4gAQAw="}]}, "engines": {"node": ">=8"}}, "3.0.0": {"name": "shallow-clone", "version": "3.0.0", "dependencies": {"kind-of": "^6.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "dist": {"integrity": "sha512-Drg+nOI+ofeuslBf0nulyWLZhK1BZprqNvPJaiB4VvES+9gC6GG+qOVAfuO12zVSgxq9SKevcme7S3uDT6Be8w==", "shasum": "317b701facce5e742d4c04c64e1d52f957e22b28", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.0.tgz", "fileCount": 4, "unpackedSize": 9090, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIClMf11aDHdFdr16USSvmeksTRBy/fdF7pDJYnYFlPpfAiBx18H+BfmFGnWtd+4sMBgTgrvV+8WxAjbh9LWcyfh3MQ=="}]}, "engines": {"node": ">=8"}}, "3.0.1": {"name": "shallow-clone", "version": "3.0.1", "dependencies": {"kind-of": "^6.0.2"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^6.1.3"}, "dist": {"integrity": "sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==", "shasum": "8f2981ad92531f55035b01fb230769a40e02efa3", "tarball": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz", "fileCount": 4, "unpackedSize": 9446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctRyTCRA9TVsSAnZWagAAK0IP/3bAwWqnsdhLgtowdxJm\n6cycI7dxZ8Lsjd5I3e7mQ77uQv92o4yOX6Lqs822o91qAqUW8+xOhDUuCkhr\n+M8QhZs3/Eo3owZTwej7a6mRsqNEaMA1K2oFp5fDE+B3h4vwJRQ7EiSyv8Be\nCKUz5UZ397J43DAW9LDXfP7L5XeTLHbF9PmDAC5zmJPPeyJERWkqqJLc2iOr\nYrJ9npW0DMXEDNxZ16+SqzcBwtPPkcCDwunJN4bxAgNtv5WdlHEhjAfrFXxe\nYE5liz/qQGP/+gmfIlqTb0kjkSkg5nwhJlXX926Pte39/fzVMU5A/nPzbc27\nlfkJP31YzZHn5G1yH2EcByyqyYCbD4L1FbCKbehUA5cyJW90+u0f3qsWmOl2\n6GrKuN0ezPYW/6rkMCgrGBcfiFnIdcFNhS8+TTZFNqq09grdTV6AS6U38ITH\nYlNSIcIaixh6V2GGWDvh9iw/HWyj2mk0K1WZ1vbgFYC12gij5Hm+8YqZtt10\negMQzWxorftG3EOGN1B0Lv89zN3gXPBDVD/Re/kKRDQ4Fe+mQJpuiwT642Lv\nY+Dkti8B9dUVwJt8MV9dl2o6J3lkHGXzo5qDAQ0l5BG5o/6KKCLKQ9kfKnL+\nk1Qtv3ajjpFwi1ZfIQGbw8KfUCDQK7INse2J4UvoD/ver+Vny2tN+eTcqV3r\n/3+t\r\n=f5v4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEqJxRJiC5DKo0M2l1o0PK5vcsVqzxV/UfSwOqXI5vKUAiBHo0Q6wP5/VUp4iiwyBr/pIYMxgdMuQ3u2skcijXqhuQ=="}]}, "engines": {"node": ">=8"}}}, "modified": "2022-06-26T18:34:58.906Z"}