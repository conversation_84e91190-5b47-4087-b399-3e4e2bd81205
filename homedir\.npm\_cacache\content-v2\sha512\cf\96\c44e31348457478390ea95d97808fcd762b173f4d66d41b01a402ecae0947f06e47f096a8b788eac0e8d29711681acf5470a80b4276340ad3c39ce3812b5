{"name": "nodemon", "dist-tags": {"debug-1268": "1.15.2-alpha.2", "debug": "2.0.14-alpha.1", "dev": "0.0.0-development", "latest": "3.1.10"}, "versions": {"0.1.7": {"name": "nodemon", "version": "0.1.7", "bin": {"nodemon": "./nodemon"}, "dist": {"shasum": "db975089c2ff5cb087c02b9f0df4fdb39d754945", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.1.7.tgz", "integrity": "sha512-ADRmZiRLK9YTp4C34D4+GaZRKJtafiyyoqiZX1Nm+Ak+Ox2TeqVilV2jJHRZKbhTk5/f8/+HtXQ/o492kQnulA==", "signatures": [{"sig": "MEUCIQDxtTCjq2dYor0udpBVuMQd133h/hgVLnAqvWqyUfzdtAIgezxsKnI1RM5+Y2yFNbDFjRoW4fLvCd2BZG8pP3TYyEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.2.0": {"name": "nodemon", "version": "0.2.0", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "cd5138da15391fbf60305791d61ecafae3bfaba4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.2.0.tgz", "integrity": "sha512-E9AlsfbcLx1U1XoWcI8iV0A0TQRPzLa/mtdAVvkPadenJuNXxFGabonid26ObyyWKz/kdMqvDsZ33kKWQf6ktw==", "signatures": [{"sig": "MEQCIEH5NhvbhfxyTI7I0baUzJDbmuNHPcVVYKhVvV6D7jptAiBcsHmDuh7RlvA3QNp7TgdbHv1g52Djr21XSAkq+NMWPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.6": {"name": "nodemon", "version": "0.1.6", "bin": {"nodemon": "./nodemon"}, "dist": {"shasum": "94cefed291ce3092416c53aefc743afc3eee070d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.1.6.tgz", "integrity": "sha512-JbGoCiYSgCilpzVCB2h38S4ByuifKwLbFLmQwxOL/pYMPSvkiLh4dfbhEI7xv5utIPJEFb+7MZuZ+wl+ivqXaQ==", "signatures": [{"sig": "MEUCIBtQQ/zBXSlKm4pQxCt4hLsJnx8dFj5uiAcD5bBXyL+UAiEAuBtsBfcsEJdpIVRqKUpt6yf8dBAVUG6zvXOmgk6Hx+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.5": {"name": "nodemon", "version": "0.1.5", "bin": {"nodemon": "./nodemon"}, "dist": {"shasum": "71bc7d252c68b81def89ea4a6b3f278dea3dc6a7", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.1.5.tgz", "integrity": "sha512-D5B8M1eYE8nHc6E4rTqv/PmCn4jn057j4MXKkjcoDXW6qhjlMRm5oujeeiUXkOpTWRKJ0ZVzY77NYXr9QUJ89w==", "signatures": [{"sig": "MEYCIQCuE1OBAlqNKxyhYllWjWKhgdmyA2Xhw9zROc6bEeoAwAIhANhQjE1kNqnjcMBB9+zr8P7taSUxv5lL38akWQ/ikSUL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.8": {"name": "nodemon", "version": "0.1.8", "bin": {"nodemon": "./nodemon"}, "dist": {"shasum": "f2193e9ecae68e7bccd2d598ce479d72c9f0044c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.1.8.tgz", "integrity": "sha512-17loVXiFUghPMBu1KGImpkafbwVoV4TeZCSO8QMcah4QbL7aYFzZSjIYJHe3otncDJpVXW0c+rBo9LeDootu/Q==", "signatures": [{"sig": "MEQCIEn1QxR0qL4BG0XvkP7M7hQQsTNELJEIydpO39sDWzEdAiAGLATQzLBn/RReh+S6pZGFWj2QUd9Qv1PjVb80r+P1oA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.4": {"name": "nodemon", "version": "0.1.4", "bin": {"nodemon": "./nodemon"}, "dist": {"shasum": "7ff4231d45049205b0f0d80359e3205f702ccfeb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.1.4.tgz", "integrity": "sha512-GUZpMPthF1WiBHLFnlmoeZCjABgF6ADZkx0YoznsUWmbC6Z7Eto9D13hgs6sqn59GmXoHAQq5YwIl5+qs/Zgdw==", "signatures": [{"sig": "MEUCIQCF6lbgYl0uGN0IWv5JQaxYDNg0LZxnkpgL0Hp26ZrGxwIgBwH22vPhh1qBigLInw9CPSe8ZttkEyRkYORq6ohvQPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.2.1": {"name": "nodemon", "version": "0.2.1", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "c008feaed5fe47224f50a2414b2ca93584f4c316", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.2.1.tgz", "integrity": "sha512-GWUdEpb2nloB/ZXfAWQJIc9Fakz2urBP3MYuxDq611asQ03jnLlKxGFPkPUrM2pZyfydzQWoHHwD1BiNgOtdCg==", "signatures": [{"sig": "MEQCIGTdkBYTI8BiiR9We24S4nS6a9MtsmYJ4D0uv0o8u5tSAiAly1ff0WL6bfWdiodXMslPJcntwhMxIJNfj689vd7YLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.2.2": {"name": "nodemon", "version": "0.2.2", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "2409de76a68223ddc79e340f65a13f286b86397e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.2.2.tgz", "integrity": "sha512-93rhJQwY6yrYcRtDUOuGonxQIOklTl35AHByE+UmBF97oGLpRn56Zuxe7n0s8Cm/Y2cPgAsdymHXnaPcNaWDlg==", "signatures": [{"sig": "MEQCIBxXyqu0ti6RQspRQJvqJInVEIMyZdoaBrjAiaBzxmdGAiA59DDvMt5dZVRPFx4JlCJxFcU2kn2t/8V4nybXidka2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.3.0": {"name": "nodemon", "version": "0.3.0", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "e9946ffb91b466d6d1276d063a5fbab083124edc", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.3.0.tgz", "integrity": "sha512-xLnq+O5Z+8tiGDRBBPQKv6aqwdEFO/S/uHRR0SvKIkz0CtBo354M5GgIznq3IbT8fmtcqenmBJATu4JV8faaiQ==", "signatures": [{"sig": "MEUCIBxSFnKHOss7ENj//fvO/VQVvcBRFjx7ITvZi1gEuNDPAiEAyFR3X7CmNsBpcVgflS5aWHW+fJdGOWcW4STXFgIHA1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.3.1": {"name": "nodemon", "version": "0.3.1", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "9a5fe241c7d8041577ff162cbd1d3cef207bd4be", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.3.1.tgz", "integrity": "sha512-KnFjpElWf24gzQFjGgRrLR3+s31fkGLCXunj4wT1wgd4/AZoPEJXciy2BXVhiAFi/XmsYAR5gFmFlTYpY67Uog==", "signatures": [{"sig": "MEUCIE/GflsKsU0tIMpyhdNQlZyy+DohROUNmX+DpLyGGOm1AiEA1g4Nw9ERANwJz1YXrdQ4gNF5LpShmB2pt9BJF3rlCVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.3.2": {"name": "nodemon", "version": "0.3.2", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "8ef1430debd6f1316d36b182c309d184c09e711c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.3.2.tgz", "integrity": "sha512-v+0k/SG6iBv/QsZopI4VYblH3+JYCgxLPliqUAHkw9edoj7F3r+15qpZajddwfvGs59CEw8OUr/gFvPsesNPYA==", "signatures": [{"sig": "MEUCIE+z+tq3tUhj3xlP+hPnN0Z+Z079Qp25EmBvd03plQDCAiEAhJZddI/r7X8UklxqJwcf8zfvgw6x32Ot6qBbqmmuemo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.4.0": {"name": "nodemon", "version": "0.4.0", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "8fbeb9430391eb71cb72d077b32c904e2cf6c045", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.4.0.tgz", "integrity": "sha512-65UhcxYrPjk/70sOELjCYmZp3WZHEtYYOrKKHhPExCacOwuiCxeeAsoev8KuHqwNa3Z6DpCmxEomCzG2QcYkyA==", "signatures": [{"sig": "MEQCIFZFBchUzDDdLo0+2AnZtYzoTLnoMyuCx53t+hcCnOQaAiAZTTvPtJ0N8FhWzKJk7iQQYwMSg9QXF2SoK7WbyGgcVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.4.1": {"name": "nodemon", "version": "0.4.1", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "b6d7e6c040649b9e53fd413d2cacbf8cfb1cb183", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.4.1.tgz", "integrity": "sha512-EZsd6+Rj42nKnvl8YrUHqual7mquMp6a1K1EVSRSbBOmyMob70tzXhVRX0ELeF6Z6a9lNTq0GJw0uqXG1g2Qdg==", "signatures": [{"sig": "MEUCIQCGQ0nRcCW0kb/CDE9vKXrxxQQaMLDlBWLsgzputWGXXwIgQecSeYsbBalv6UZ7zkpU/eEiPHgLMi90ZdBqk25hJOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.0": {"name": "nodemon", "version": "0.5.0", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "3bf47eb01518bdc2b756d1d8b01ba45a207e9657", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.0.tgz", "integrity": "sha512-nv1aP8qtEiFO2RzN2shci4j/+DBP3xOp330mAdMbPtIqyFcLIv3B1jyNNQoszFMMYBEKzktQV+jkLdKJoiQ5Mw==", "signatures": [{"sig": "MEUCIFodP+nJH4c0i0ODEyUaILSa3VC7St6cI0wlcS+c5uOeAiEAi3mnQKGVXqv4t1AzU905hPzfTLcqepuiUy1BV92aWwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.1": {"name": "nodemon", "version": "0.5.1", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "18edcfac90af3cd8ed53c06e6dd2081e07715347", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.1.tgz", "integrity": "sha512-xDaFH91Q5D1opZ2aS8+fdmh5Ai4UwiTlU706GsNmg5k2P3ZTbeGTvrriE2nVp9ebYUiFyG/1joWZU6xfKtx0yg==", "signatures": [{"sig": "MEQCIEaViqhP9TpJomF8bry3ZCsf+WLD/eFiodZnBUS3ywTZAiBjq5rhWXqEpyxEXGYRAzVQWHf6kSGiRYItytlcPK/g6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.2": {"name": "nodemon", "version": "0.5.2", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "21c43dba3ccc000501c3fff88288bf0f634ba539", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.2.tgz", "integrity": "sha512-P4vyJYa9w5Z8pkShC2DhzveugNGgirZfcXz+zYre5MUcn9kkDW/QvcuDlGA+zF1L10DOeyYLPHZtemivJKlDZw==", "signatures": [{"sig": "MEUCIQDGemmsXok7flEb2jQiM4CHAb574PwpquALVDQB9NSStwIgViLjks+xYkyZQsbszG/1X4OKKBL7H3mSVt5ajp+duJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.3": {"name": "nodemon", "version": "0.5.3", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "10baddf82ced6e3993d9aad3f0854b4dde57b922", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.3.tgz", "integrity": "sha512-VfEpwH2IulYRseXpQdI4/VGp5B2SwyYvXDEK38uV6Qr4GRqyCi9hyWnh4hu6ofu0ZNNjcC1LFQsGm8eM3Q6LCw==", "signatures": [{"sig": "MEUCIAQoiLYWlGSn1VN0jMXl/mx44DNsycJ5VCdKodbAMtyUAiEAptQKNZRpLPUJ9hRsjRHoeF6IGNCtpvQuYUrHZ8MWvZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.4": {"name": "nodemon", "version": "0.5.4", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "737946f008b9f3d0342902aa279642623bc7fcb6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.4.tgz", "integrity": "sha512-0OMCALizlKZfFsUkYFvq7CM0t2BUWtADS2AdRLgZlHJjg76mYo98CFmcBASh6PzDi5GBLoJkxUag9wuv1UrPxg==", "signatures": [{"sig": "MEYCIQD+2SXqjkY94Mu135bigFnOlO/19MxgA+qa9LxDHShfCwIhAJVVMIiVq7mBOOS52oEb0OvQxEYC8bjzMx1E5/XY8Rwy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.5": {"name": "nodemon", "version": "0.5.5", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "a796125f2e3128d7fbbfcb67efa3593302024129", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.5.tgz", "integrity": "sha512-EkrxgDjj5ttAD53WQnCDWVN6YeDxea8LOxdMTxUEnC8FinHLhYuxkkCVQCcqMtvJ1O/w6n2dQL2grZTUdeGK/w==", "signatures": [{"sig": "MEUCIQCpFCFZSROwaJAyRrlJpcjAe29fJauzG8g/l0MkS+PLaQIge20VyucAhZTamUSvgs9tdq9s0o/UwJKRkNuTwOv1Owk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.6": {"name": "nodemon", "version": "0.5.6", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "f0f59bf98d1da5ca885de44e870c4e419a0db76a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.6.tgz", "integrity": "sha512-/Db5kR2eWD0foWaVYSHALui/spPDBMTB22NUJfDb5YB2XG7qy5HQwFp9vOAtNLb4STSMGWusQD5umAg18Ia2nw==", "signatures": [{"sig": "MEUCIQDm6jqbEnpK874kfLK5yUIjcYAED8OGnmk0COSFjbLDqgIgbS9VRDcgQHUutLnilsG/DGHkYtdCDg7bBk6OPZ7H3pQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.5.7": {"name": "nodemon", "version": "0.5.7", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "19de72490fb0146ff78c67e46b86942128d13b24", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.5.7.tgz", "integrity": "sha512-8d/cwc8CHLX3KWpTTnICFyvJ+g/tqjxv9WdShVBMDqGoQeHlTQ92Yq9pI8E5ADa3vSIBVXVQCP8v3ZDlmQLZSw==", "signatures": [{"sig": "MEQCIHrQ0OnAws35/t3OWy8JDY/OlUMcxfh4YqFrcB5esuAZAiA4qkKmU28ezAaq/p035VO/vZLG/dbjOJyFVDpgUOer/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.0": {"name": "nodemon", "version": "0.6.0", "dependencies": {"commander": "0.5.1"}, "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "d720355f46a40d17a7ad01e5f367b0afdd2b98f9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.0.tgz", "integrity": "sha512-mLooaiaJrT/4r9XQXseubj2j3L3SD+IdvYUXTywT5H1QBit6oLYEu8iTyV4SHnhuSYZq4Phgllfg4V1RXY5oMA==", "signatures": [{"sig": "MEUCIHSdEoogYuPm0CjiOmpUJsGwQakZJ+kxDgZU1nsCiDkeAiEAwKg7h+jxenzSFiSlt/ySM8L/HGcYwc4GXkTYnhISfc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.1": {"name": "nodemon", "version": "0.6.1", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "492d81f1e42aa0aa17c1263d6203c45ef2ccb03b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.1.tgz", "integrity": "sha512-0I3ucS+SCOPC78UEIYwWauQKgPbdY0gvh5Axfw21mIHgmgzyy74yYp/Tepi0b86ZkBYsx1kbfxzAMl9eNm5Ahg==", "signatures": [{"sig": "MEQCIAqJPcbrLtQRlU5oHDwFtO84/cCWJcCYzwH6dXlCp7sMAiBY+XR9Nh2TyKXzfYQIkItmWEY18oLm8DG6c5uLeqPnzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.2": {"name": "nodemon", "version": "0.6.2", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "3e78cc84f1f48e6b21b4ae36be5ce2fcdf3756d6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.2.tgz", "integrity": "sha512-E3IUKGw/Hc68F7mj9odQBgM3ccyrANNa0rwRpkC9QwwvH32raZS+09n3ahVzw5y7FP1LVIrsMidAe9eaioiTjQ==", "signatures": [{"sig": "MEQCIFuq0how9YJP2X/+iaJeWNLKRs2dw20d/IMvAsrhQDZzAiBGOGwKcKKD2+Z5mAOKUQfeY4v4msx1cIUQIw1lOtfmiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.3": {"name": "nodemon", "version": "0.6.3", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "489ca1d69ef5ef1ae5c2d17d71551e10784b1048", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.3.tgz", "integrity": "sha512-0slFgwJTU+u2zwxJil+zgfYEDDWM83KtGCskd8l84Zr3XcQ448LsW0RmQCPgu40V4UeguTY8ZfQhwvXvciFXOg==", "signatures": [{"sig": "MEUCIQD84c6Cd3Slm8F9ZAs7zK7JlL0PRFCOP5+Y8VlwQydFKQIgBo6Af78CZElo9tNX7RqSw2YrFYpgivK/IBND3bGiYYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.4": {"name": "nodemon", "version": "0.6.4", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "38d825b8be624c670ba676181d715ff97581bed9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.4.tgz", "integrity": "sha512-Wm/PVSOiRGF3nYgctPG4ASiczACXCVjuep/T0tJHHkESFK8ON4+xzFaDcliAmi5Di0eG1A8SINFQUAfYsI49Iw==", "signatures": [{"sig": "MEUCIDmfcPU46u9R+YajxRkhzUeeJl8jDYN7D0WPivMtzkTGAiEA3GWyCi8UysrfQvQGqxzv7cj1WsCRL32Vu02qS9dQ/8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.5": {"name": "nodemon", "version": "0.6.5", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "b1da797a527ffba4d4c2f258597f64046f7b0d4c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.5.tgz", "integrity": "sha512-zDT6ZJb+tjbcpUvcKZG8IHZaAVX7Cmj4SIdv+wfN1BGRRtY3iC9/v3ZXvbQ27XrjnbarvtjM1sWC6Bib91/k4A==", "signatures": [{"sig": "MEQCIGFazHsS4bcPDTM0vc2djOsOBMta4/mrZfOUOEMfOV2vAiBX4d+YvMJ/rBhiCv635jLsWjfIXnMck7AUbgESQENIIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.6": {"name": "nodemon", "version": "0.6.6", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "e77d7e893e10dc8471f292257a1e72808ef55fc4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.6.tgz", "integrity": "sha512-Mrl6XR0iC8Oq4o06uPzjEIfxIWnULd6jA/Trz/67/9n2T5VN0BVGva6C9FJWKTFhyUx9vbAW8hboqDc/kg0n+Q==", "signatures": [{"sig": "MEQCIGBxvKSv9qLbHXwks52f2MS6K/kYZty9kYA++b39lu0SAiAoWchMPpZiDMj5z9GNAvJE+mHylq61b6dAN/FJ13Bwmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.7": {"name": "nodemon", "version": "0.6.7", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "ddea0a572c7a0d08c063762530a0476ed0bf45d2", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.7.tgz", "integrity": "sha512-3eIKliWFKkaeapPcg2SlR86S92CFMhg/RbUcfEBHnO9lXhHnS2gV+k6DTJSVIIhxWzIVxTMYNnPAG26Gl03oHg==", "signatures": [{"sig": "MEMCH1lhMS6IgO43AtILBUXHRTwJf1rL0BHmql8w2kzb+50CIFTw9GWZ6gtk0J+s6QxhVc116VfKPtY6FVkMm0SUzerX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.8": {"name": "nodemon", "version": "0.6.8", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "ed05be57e91dd7fc80875be832f42a3e43ccf0c0", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.8.tgz", "integrity": "sha512-wyn3OKqTHiwW6ZsZfJLCCd+HrZYyiaHkjofIs2kf2ESHB4Rfu0xs4nYdPc9d7xBws4BWmVMsa/mT5jBIJ9BUdw==", "signatures": [{"sig": "MEUCIQCE/RTi435ezj64rA3eaTXgFivNh3domO/8EK45WMo8DQIgL1XtND3KYbWknfoqwdXonTH4QVmBSUsUGoaAGhdeC/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.9": {"name": "nodemon", "version": "0.6.9", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "7547ff336d3ff1313344a890f3d13a378066ced2", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.9.tgz", "integrity": "sha512-MpNgqXYun6Ff9fE/BTszhbtqdyM9amCmAKi9RlT2qjdeC2D1MRED34ua43H2mN+Nbf0pFVUSsflG8nClK46Dzw==", "signatures": [{"sig": "MEYCIQDGaXNRJi+l9dMwkLYy6FysWquWEj/V4gGQk2kQI/WkZAIhAKk1S8nXX5SYEJIMQHbQjLlT7ke3ZWZKtbBVF26KYmU9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.10": {"name": "nodemon", "version": "0.6.10", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "0db2973ff19c090d055108f0cf1298f095b23410", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.10.tgz", "integrity": "sha512-Fw62OcSjnLX5k0aNxZAiu8P/WTU/jmEFFE5Y8CY29bhvN4dTIUW9x3Aps+y9lQN1VtfAtOr3Ix0lf5bGcqu3Gw==", "signatures": [{"sig": "MEUCIQCw2Px/zfHU3XT7RjhROwo6M/af10TwP6AbfWzXORysEgIgMr2nczCkzqtuScxCvC09YAyu2GVr5X+zk7a3Y4H2aQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.11": {"name": "nodemon", "version": "0.6.11", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "e4352d1e8c262f5539f64c266c1b8c8322fa1cca", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.11.tgz", "integrity": "sha512-aUSzvnzUb0SOJZ6K9mdVdOVHdvvLST3jxUpaIf2D8gKQVIXXKrXO7e74i3aTENHn6zXKAWR4+3I44UaNbaygWw==", "signatures": [{"sig": "MEYCIQCLybOP1aFvNDFXXs14XQfYq8Qg5cTDE4SS7+n6uwdLTAIhAPC3PY/BckuOJ9tSj2FRYqRp4LrE8yfsj6G8OUDgxeh3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.12": {"name": "nodemon", "version": "0.6.12", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "318dc2b2f4895c203c459aabb7dbc16dfe3c90e5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.12.tgz", "integrity": "sha512-wg8Wy/RtsXtr8Y2QKcXpYPxu1saMQXU1LqHxvRJaKbw7vCNU1UD0/EEuEdgX63KV2ZyMa9XLzh/QhqIMbDUD8g==", "signatures": [{"sig": "MEQCIDYUgATfdIw4d382ifl4qKKxxsmgzxvAC5wX0+WtLNu2AiBvnl9MM9mS3fPHEehMdPIxcpvlJqeYaVHD+ZTcI8D9xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.13": {"name": "nodemon", "version": "0.6.13", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "f861cbf5edac36f4183053034cb08703bfd76779", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.13.tgz", "integrity": "sha512-2mddW1obNX4QyAoqnJ14gh5KS7enzeZZp0bWu8WObr9MtJX+DJAWkpH4AxPmfAgCQ9vWtrehzD6m981IzsU2Pg==", "signatures": [{"sig": "MEUCIQD4tPPV0Yq/5RZ60V9YlUe28P7k4Fr0KnqLBTxjewgd9QIgCySotu+VGCaX/50zgYpnRqtSOpvInSyhlMHS1i5Bbmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.14": {"name": "nodemon", "version": "0.6.14", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "289b0ec3efab780e98ecbe55545d7111dbc695b5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.14.tgz", "integrity": "sha512-zE0CQnrB0c/igO1DTY2b426Y2b78tEAAsJvk6ZevhrdsIdgMfNzkgW3TXvGQW+FX2dZGYMpOeJzybGFXbnyWtQ==", "signatures": [{"sig": "MEYCIQCBNDwi5Y8Hh/m6lJaGGL+pcCvUNfrXW5q0mUgDZmjuyAIhAMcLyZBy4qbNWuxjSwiU5z17BG9jGpwaNkydxMMvyNAU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.15": {"name": "nodemon", "version": "0.6.15", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "f8ee1756f716526146ef2daa84a7819cfe286824", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.15.tgz", "integrity": "sha512-2uIk5ZetYeYTzNeK41eCK0dtDPZt82twQeIBy+lAt9Fo867/mYL99CGQmHgrxjQ9YaKG0sBxSuF6Dx/gcXA47w==", "signatures": [{"sig": "MEUCIQCond+8Si2QQhD6SklpLmQtISnnpJe7fl8747o6CcCJiAIgDSE5J2KcS1yDvU/QqmAokjig155VjRr58e7B1x+9GqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.16": {"name": "nodemon", "version": "0.6.16", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "ce5adb2dddecca7acbd85755f6e5245ac3c3d70a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.16.tgz", "integrity": "sha512-TOm6Z5XQapxqXQE3X/KHBe8LK80BFxCbO4MlNSZXfGl39cAXrrPaxTHRqZusz2lmvVPwukwo1ne6uWAew2ka+A==", "signatures": [{"sig": "MEUCIQCgJv552uIZs6eJY7eZoG8lXfPe9LMIFXMeB2ZG054cpwIgLgj7GakAWB14QakPJ9u/MTVjfXQfNhWhR0Iy0W+nJPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.17": {"name": "nodemon", "version": "0.6.17", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "f0fdc9be6ee7e7cdb35c772b8f2bc835bff4592c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.17.tgz", "integrity": "sha512-d6U5jjOKx2CkQJYFOZ+IGKu7/Jf8BT1BdPKm0QyKHFDjTgakgY2ORGKJTfgO6ChE9xpkADnWY8HCARuWskmS3g==", "signatures": [{"sig": "MEUCIQDj1lDRkffgx4/DfgGpuji5kp3kgjuMUNcOAF1DPg/TigIgSXxcGzTO6VA8JihipgBhRWYcinNbt2SNB0z2NLmp03c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.18": {"name": "nodemon", "version": "0.6.18", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "e3fe6cd4fe29acf135058cafd461dfbcf8a277fd", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.18.tgz", "integrity": "sha512-mlAP9HkGrOh0VuO6+mpW539H4Jfl2AKYZRvgeG3CPWL0C69hm3Q78MLje0czLWk2oKbsfQI5EwI+KXzKNtixUA==", "signatures": [{"sig": "MEYCIQCa3xWm0Ze4vjT56rv5lomtWpGgV+ERcmCdTKNut9jJxQIhANx5jfDQvAPCc9yD6sXxeaq5/rBrHXsv9kpP6J/YcIxq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.19": {"name": "nodemon", "version": "0.6.19", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "47f1ac009bb2c2a83819be11abe826734e978e9d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.19.tgz", "integrity": "sha512-r6R/VCb0/5Gx97gHvxG41EZtCFvO6Onx7MmC/xUW+9oSjcUsFH2JyrbJU8kJkpfGyzklAV260doaF7iOXwZ4lQ==", "signatures": [{"sig": "MEUCIQCEV/f1egBIqrLq4Hg8E76YVaoFwBGXUsOy7EOj6dGd1wIgKcOfC9Itrqg6v/IfHpMg7UsOAk4FMAOoXt6ZmmKF7xM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.20": {"name": "nodemon", "version": "0.6.20", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "83a6a1d9ee999f5ea78f49c64edafa2f432812f4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.20.tgz", "integrity": "sha512-fdQjAYLGb96sT0FOFdlgai1RuUVzq12rNO0InMuu4YceT+wUX9qlIP3F6GbyvEu3LakocVyzCyU10tFJGsiWVw==", "signatures": [{"sig": "MEUCIEsIVhqTmLzOmI5cLJBKLIGGg0MZNWPRxgw6eVOecO5QAiEA6aiTN027CExFFP72ru+ZuQs/0hABHojgQ7XG3Rc+yVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.21": {"name": "nodemon", "version": "0.6.21", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "c9ffb828abdb539e8a66ef594c0472316377f2af", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.21.tgz", "integrity": "sha512-cqitLKlAici4nTDgGzBM1Yf5d967GwvHhpsE+xzKaHa5K0zQX1j40ZjgyoxZcVVpq6rsVVdhCp28BTzSgfGdHg==", "signatures": [{"sig": "MEUCIQDLlwoCSsHI8gHGcwkCFpZzgFXRAsDeHRLCBfqZmSF0dAIgTUwEhfFxVot3vXLIJhARG12Ggb6+FBbug3fRD7rZw7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.22": {"name": "nodemon", "version": "0.6.22", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "5de0f9a026fbe79b6e46186dbf3b59aaea61f5e8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.22.tgz", "integrity": "sha512-f3k+IfVeB1YWpjnL5hGue7lFplPa4L6r3RULrjslVS2Q+pWTiBJ+UeTMCk9GVf4o8bgGoVrifTzKVjZQU9f4wA==", "signatures": [{"sig": "MEUCIE93wYTlfj1tFZat6WE0B+Asx41MrrBwAa1SQt1FoQGoAiEA6Dc9+/s0R/HKYam/jj3dnSvprqHp8bFEaZMGnJhZxuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.6.23": {"name": "nodemon", "version": "0.6.23", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "465847752781d23acad854c200872f61726d9e42", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.23.tgz", "integrity": "sha512-SeAcQrAkAfcYPPi0n5Fsgnx1eK6mMj+1agGS38Xrli9+36YRlPt1cyDxJjdBOrxHEI9gALQf/0BCjO8DpaJxIw==", "signatures": [{"sig": "MEUCIH2Xpq3uwOrz8p0Ot8C1BDpWaY9wgqg609uK5/lRgKIsAiEA88HvWGGIv1u01gD+6qtwTXuDLwIyLE+G7wE31ZCInxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.7.0": {"name": "nodemon", "version": "0.7.0", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "5a599e80b18465323b620520cffe1d74ed5fab84", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.0.tgz", "integrity": "sha512-t3XZtDPBuNPynqL1o/pgdv02YkEoOtU5EUF7qxrXzdVhUeZCwY4d6+sbLv2JvOB5ut9zrKu5uFC8NRmbP4EFSQ==", "signatures": [{"sig": "MEQCICbGKcH79z//RzGGmFITwEKCRB5HvNy5JmynSgwFAA5YAiBp8dLeHqrUJrlmpjWfz5pKcO/dQ51ABIx9VIVazvjX/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.1": {"name": "nodemon", "version": "0.7.1", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "f774a11522cbf86e29987bcc723506b06dae73ff", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.1.tgz", "integrity": "sha512-udFwW2uKw5CBgwNuR1TTmYl0f8zpu9MQwE+pOPXySQG66KDCF+PlVOyYwjw+yKZCqyuamycAlQsrJJ/VX9L96A==", "signatures": [{"sig": "MEUCIF/oMwTdx9NpKVOeuq9YaN3OxfUb+fI65SnClBvzwQ6VAiEA5rNaNTeig3ZVUtcgUPct0xOz81fVsCjK1XGGD6YCDqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.2": {"name": "nodemon", "version": "0.7.2", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "7f15e7a88c669b872a7de46e76a42a88f677f184", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.2.tgz", "integrity": "sha512-zX4ZljKkCs2q1ig871pye0BnTVGtXlMqTMQ4tlvxXg2/Iw77kVn/vOANdwzJSwFc491gLqUwhLaXEy1Okj1t7w==", "signatures": [{"sig": "MEQCIHTMoCe9VMZCludc724qGRVkEmrZYY2VOhuUTP0XCJdgAiARX1G1AwAuC4jK7/fyiQA5TIQJwacOYdzgUkVRFg1j1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.3": {"name": "nodemon", "version": "0.7.3", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "03a655d59bdac52d79dc4de6dfb175f0a4e61e29", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.3.tgz", "integrity": "sha512-voUGG0ax0sNqJ+Efco+7gPLLnpzDRPKYW50ig08ZaUuVVxyFNyS/q2TGofD8fBt1We0lcd+EgwOKQr6YqOYpEg==", "signatures": [{"sig": "MEYCIQCFVTq/rMsnAeXCCV8Si4a7h4w2Tlzj9fOVGPYiZHu6FwIhAM3FCQiAJg3Ahb2JzRDzYvGayF3b20wbjJrymGylVkgy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.4": {"name": "nodemon", "version": "0.7.4", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "ddbd05b905a1b0b471ba64d259034e65b7fb68a4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.4.tgz", "integrity": "sha512-2xSGXWVz9O8hwimWz5GREvI0nqpzRMdSUpJPUcAGDJK0SvNj5cnW/FKPHpF4B43Xi1MeEYBfJoKJOu4rd45K+w==", "signatures": [{"sig": "MEQCIGsPFbKomayECkeDQM5f9Z8zK8Kjr01zLp2I/NQVgB3jAiAW9K29XTQxc/JiSShRAxp9odv0Ru+uqhfrz67LCV7tqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.5": {"name": "nodemon", "version": "0.7.5", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "a93d0a703e83b3803fba12b24caaded623968087", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.5.tgz", "integrity": "sha512-wEofpgSc7Xyd2yUX7sBNSUv1OKr2J/NFNtoQRbOHzKMQCtNz19u0L0upXp93EC/QEr5qVyeDIUT9Xq9zcQhKPA==", "signatures": [{"sig": "MEUCIChvF6szkJ0h6snPHgWCPTn+6xVLeylBfuQ2Mk+n8i81AiEAzPwh946l+bd6t0Ago0fl+P/S1+j0eFuUpuZpvfSPn9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.6": {"name": "nodemon", "version": "0.7.6", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "ae61bf93c129573e3a6870550f46e04fb0783f5a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.6.tgz", "integrity": "sha512-XATwz3kW/en+M1Q485hTnnfoXNx07tkSSeFoVHrV+zWM0RX7pFEztum0MOevrJe61hyXa0Uu/0rrYB6Qc/vMtg==", "signatures": [{"sig": "MEQCIAgJ+PREx0EGCQotsSzfOOkQo2joVkronvvGx1XeSk+JAiBB3RvemyH6yaZS1BK1CuDvUOJ5mu6J2mT0Lqtlmw7FTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.7": {"name": "nodemon", "version": "0.7.7", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "3f7de4a74ae4ffb5fe333aa749070ddfd8b1ccaa", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.7.tgz", "integrity": "sha512-G3EeW+6D6USyT1l4ri2H1PGX7ZudX1DPLmZOyEF8PncRPz+AHxXbpkkby22VBfnGEsMXKpOkDhLba9Xs4qGMGw==", "signatures": [{"sig": "MEUCIQDHiDN5xaHJlICsEk6Cq4LPgrn/VXMz5f9h23HlXFHp5wIgFg4nYO1QBlIZK2H0o1rlamNU9XYrJjfOE9sUafuZLqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.8": {"name": "nodemon", "version": "0.7.8", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "c0667a000f208dbf6a4dad08a5186595a9889590", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.8.tgz", "integrity": "sha512-vCefMJz3kZVJhrrPOnXrE44UdxWe3121ElibKT70hK41ycfvt2VdrTEcaS2DOYFoczgz0yP1nd9IqeM8o43tIg==", "signatures": [{"sig": "MEQCIAVxsGszjjVjGrEQja7a7rWflOP2JqFHI7w+paGItDokAiBpBACovu3N4hD7EhyNkCowi2CP9FWloqwNv0b+Q6tBqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.10": {"name": "nodemon", "version": "0.7.10", "devDependencies": {"mocha": "~1.9.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "695a01b9458b115b03bbe01696d361bd50b4fb9b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.7.10.tgz", "integrity": "sha512-G2h2St7iqmTBy1hdBqoIaXxWv7/8fISyEiGIUvySRtSawBkYP7YuS65fglKsJqpfV8Y8cFEb3tJI5woGNKGVvQ==", "signatures": [{"sig": "MEYCIQDzz2HmAoPqh8XB+KnO5gfoIYUEZnUM/Fcq338puFGOkQIhAPqE3qiVoqxE32GcbnbBm8w9wzhOkbk7lxUosxX3A0nD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.0": {"name": "nodemon", "version": "0.9.0", "dependencies": {"colors": "~0.6.1", "object-mixin": "~0.2.1", "update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "1b08663616d635e437d5b7d54fd58f789ec74b82", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.0.tgz", "integrity": "sha512-QnpEXYlT6tZNXOTU4IwMEcW2A2HOL3qzjMWuFGzLtPocfqfY2xZGGFK7DvI6LUIjAAORYJHV8ZQYFnZGcm60Ow==", "signatures": [{"sig": "MEYCIQD1eURpHlqHaiGWI+VjwBN3rnOoY+pLNrRRLfzcVoYUFwIhAPGIpjVBvLlAnQRSp7ODag3u5D+/N5HAtRcpF4MMI15E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.1": {"name": "nodemon", "version": "0.9.1", "dependencies": {"object-mixin": "~0.2.1", "update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "3d962acdda5d44681f76c4a46e36147d6e8c042f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.1.tgz", "integrity": "sha512-3dbWDLOTHFLSsl/GHtmA4DiwfYiwA5pGVqRKoVa7Haw5+e4kl1nfmlW+yXszNMldo42VAbIB9Mk4iBuxa3h/Jg==", "signatures": [{"sig": "MEQCIG1sUltdkZ2LGEzm0a9HciIluiI6PGTU3IblRzdGca+KAiBvgTubgK1GerDskDq6j8/LFh+7jA0BIgJkpdKc4YRIVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.2": {"name": "nodemon", "version": "0.9.2", "dependencies": {"object-mixin": "~0.2.1", "update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "c19c576078462c6506badda1dec254695f2b2af7", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.2.tgz", "integrity": "sha512-CxJseH4hzYiCxmUK0bJIWai77TwbegZdJwAYRkG6o9sjcI1yE/2Uv/SIJHT/kAXhAhkJDaaoSH6JZp1PyMjulQ==", "signatures": [{"sig": "MEUCIQCNZcw+pp3V8n6EoBlLmvUONwCXHL4fq7f2Rb54D3E/wwIgYKYmZaPtrmbzTCWitFEfxsxY0Cs4Rhd35W3vJKSO0ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.3": {"name": "nodemon", "version": "0.9.3", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "0586ad36ecf0e34fccccff0a874f9629f1a9d84a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.3.tgz", "integrity": "sha512-vc8AG4Swkuml7MsassZclBHzeE1nNJ030+oMmQV5kuCLGcbHy4VMFqGpVrrnhjNv5h9eCxrD2+KboMN0i6OB1w==", "signatures": [{"sig": "MEYCIQDPiUY++oyF0zQMzwivQ+8W0Ers1fDH0skNiBQmFqaY2gIhANMsW8woiYjj825iKLyOFi1OM/sptbUC0OQcCaRLfagR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.5": {"name": "nodemon", "version": "0.9.5", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "a46738c7bf69a1c556858f90aa53844152ef4978", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.5.tgz", "integrity": "sha512-aHxdBJEKWr/8qTvd4MZu+Pe1JNRDYzPgu21kon/hPEkgHAL9uMM6MWIJc735ZjeGks7Gdwk6MCKbenrIPL4KCg==", "signatures": [{"sig": "MEQCIEgUV7DvRE2lcKY1C+LOJEe9+nM27od4XbVocE2NVpd7AiBJM+vRMJu8xYYI78NfONXJ4T1z4FQT3lkUXZjCORdCag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.6": {"name": "nodemon", "version": "0.9.6", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "should": "~1.2.2", "connect": "*"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "7a0b135b9b9c56e7a73025bbac8d6d3dc8dcd5c9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.6.tgz", "integrity": "sha512-6cN68Q8VpdlxuwTyxs8K0fYMt/5t/1i6Hh/l9OhYmLP/uFSscOZcc3JDaAF5hNIo7rR+jMQbj61kxtVuEUY2dA==", "signatures": [{"sig": "MEQCIBB4NQljg6VunHra5YTVpSxwZtIuUmvtepDCLB/B8z/RAiAsEPo9q+AUzYfLyj9DOgmdWOn4dT1asEFybj2y3n9Yvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.7": {"name": "nodemon", "version": "0.9.7", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "bfce004cf399d6785809addaabd6821e20805bd6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.7.tgz", "integrity": "sha512-LRHh5YHNQ0tDi1NnfYjUMbKIODYx4FyKSGz8AoHwowAhJz9EUMWC3XVo1SLsb8aRRqz2h4n6oyIxwhwQTyBxwA==", "signatures": [{"sig": "MEUCIQDUW6Xuc8/QQ2ko1FUBK2+jxJmbHTe+pxR6hBIdc5VKuAIgXA3jdXmvHYhZYw3KiebcvrGt8QmDce/Oh3dW44sZEW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.10": {"name": "nodemon", "version": "0.9.10", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "4faca02a4df262cda9334ef24bfadca14c5c3112", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.10.tgz", "integrity": "sha512-Nb8WgWJVIAAoTQ+TD7a59JvzcVH3XBfUFce+nxOLwDO9KoUReHehtmTrLW7e2R3r9IBIGv7aOfl16Uma8Vv7ZA==", "signatures": [{"sig": "MEYCIQDe37RiURkarKfjgl4MJEZ1s8VTq8IBewe8TTOS6fTRcgIhALhOwHpti4BxtGN9nrU62PY7CV4yuVmKjKBxaWVmPKY6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.11": {"name": "nodemon", "version": "0.9.11", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b68cd8acbf60210209513af38070bf1a31b23018", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.11.tgz", "integrity": "sha512-znH8NL09/jqwt9c1a2IAeUZcEeDvN+VDsIcBGx4ZBsSEHXZAEHrzlAO6uBhkR3deoFv4bgcFcyS35BJ16iLEhg==", "signatures": [{"sig": "MEUCIQDr5wfdQkU7ufgEzH6Vfd8zboXYNH2hYtU0Q99/RsMV8wIgeEHjdYTLvaTWfiICW4ZyciRlYKX/ucd/hFQAShAQOog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.12": {"name": "nodemon", "version": "0.9.12", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "4c95d4751d5a8aa76a6df70a70f5f90d5edf69c3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.12.tgz", "integrity": "sha512-R+Zcyk1XAnE1dbAA+n9EKEct1tQAbfOaRLdsDkN//ph9+LpCD5aJxw+gBV4N0hntKmZ3/di6VnZMGewbQmZt2A==", "signatures": [{"sig": "MEYCIQDslx4V6l9l587mDW9dR7P12zT0KK0HqB9KRJUaUtlH6AIhAKXcAuxV9NELLBiGlUSbXGWXKLEeZL1m3GrhYxEcqMpl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.14": {"name": "nodemon", "version": "0.9.14", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "41c401aaea5b4306aab6ba71f12d3c8a1e693440", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.14.tgz", "integrity": "sha512-1wF5Y0/72WWj8PrajVzbudtsbjYiqJFd/6CU+BVMrLuVZ6OseIZUrBROMuAYhXscKmEMYLadeFMKKF8RXlCzSQ==", "signatures": [{"sig": "MEUCIFDM1mTMHkhX6PSYF52+vrYHnzLN8RJeCVEtKOP+iENgAiEAo9m0vTndfHi2jR5vYAV6uc+Riij8bD1KCGk5TR0igRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.16": {"name": "nodemon", "version": "0.9.16", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "6276004ffc2f7fdcc040126766fedd2f1346edf0", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.16.tgz", "integrity": "sha512-3QBaF0F51KNlqvQ7GE+HWwucj0ugFdEIqIablIBFBpILQb2M1oB/F5QfgJSXIX7Vqdtbd6XRppDua1XZ6xL5VQ==", "signatures": [{"sig": "MEQCIHw2vf4jTb+eCW4YlwAR7QPkQbscPxkkmrWEu6ddx3bSAiAIK2hMe2L0N7gha3F/QxcFe62oen2NTmEoZO3O7HnX7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.17": {"name": "nodemon", "version": "0.9.17", "dependencies": {"update-notifier": "~0.1.5"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "31fbc65cbda162ebff4d9ff5f774fdd3006bb50b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.9.17.tgz", "integrity": "sha512-VbQFqmRHKRBwYzMqigdZ/Ip8wubr76a/10mOaYztgj6edMP2YhrhNTpabadrBYBZXbwuaeMnlQvOXxZAK1esAw==", "signatures": [{"sig": "MEUCIGrzbxo+4uBhs4Dm1xTzYwSgcs1aUefqvfpoR0OSnwuUAiEAtznU9kkMnyLXGd/vPT9k4LBIlyaOFNLtvKYMl84PQ6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "nodemon", "version": "1.0.0", "dependencies": {"update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "148c11b5b3603bb9ed186478b16bdaa54af81337", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.0.tgz", "integrity": "sha512-3JyJwnxwZih4P1WPngWPjkI7ps+QQZExMx2KbY8lxWwhTgtTfuUiAiOW4A75Ql06jbSpp383HqKVTT3sQkVeyw==", "signatures": [{"sig": "MEYCIQDUVNC0eXvoCX79GVy+g5ZfmUztz/PPc4D2Mw4RZEo46wIhANDchTPYLMqXboOsKe4pzULgD5wOf6GldaDMgOYQU4IS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "nodemon", "version": "1.0.1", "dependencies": {"update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "0b69324abfadae00d1976150b3d8722e6516f47f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.1.tgz", "integrity": "sha512-SxqEl+DLOBGR09WkKM4V9vUGFVlzmROepxAekQgu/DE4s7oBxYkHzPskoXJZldZ0xV13/leg3cC+P01/HE6Lww==", "signatures": [{"sig": "MEYCIQDQsEqbEzGlEkbNt+TAG5RoW7N05MLJx4oqaF3YE3aG7gIhAKmD0ol3zgjT1Ro3KbMOExna+WqWTHzR9QhIbuQIFPML", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "nodemon", "version": "1.0.2", "dependencies": {"update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f934df2f87a1b4718c283615d1f4f985b852e28e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.2.tgz", "integrity": "sha512-3qx2Ia6GQYtQS7Qz1kwRnln0TZMT+E+sXUJbJHNq0A0ej25QfYWHvtUOH9R+Ja/00iyZWMsMx4g2H2/nNFEFLg==", "signatures": [{"sig": "MEUCIQCl10IzwtCSNtCJsP7TgDCXPx/ADCCMkvCqfCSNMYZ5RwIgRYSkHc0onV3WnpfE30iY4mUXd0DU4qbcVXeFk243oDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2-rc": {"name": "nodemon", "version": "1.0.2-rc", "dependencies": {"update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "8251ead40a109aa4f9ee0810f8d5b25c82fc8dc8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.2-rc.tgz", "integrity": "sha512-35K9XaiGypQcdMR0e1zIaK0Dd882OXc4BLShWEuz3Rxagi2XSHwuqqnxQEVDJt2HX7s4WBnjYzj0g9tHOq36Zg==", "signatures": [{"sig": "MEQCIBDg62Yez3v5qXku7KclNLcZ3gpPrZ7mVhvzX/PsbuiDAiAYnGpei0GS2DTHDRyWnhJ9dWup/aHXpHT1h0SkNQHAGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2-rc3": {"name": "nodemon", "version": "1.0.2-rc3", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d563347b1f9f0408595fb2cbd2f2e1cad3a0f745", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.2-rc3.tgz", "integrity": "sha512-xDRuy8n7FoZg6DzJ7RR+7k+8BaSdQ/A2hOiunlX8Llx/oBXiQcd3ez0+GKeaZAAMXwFIBHf/IDQWmmU1gUuCYw==", "signatures": [{"sig": "MEYCIQCpgchZIXj/tIzzMWiCZfEHEl8zbiyVyhpFN8Rqpv2oSAIhAK+PXwpBKNFsexhNLZ0ijrslpQG6at0lJTfj7xwbr7Lv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.10-a": {"name": "nodemon", "version": "0.6.10-a", "bin": {"nodemon": "./nodemon.js"}, "dist": {"shasum": "76aa417b4932fce7cba07785653aeb882beb0794", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.6.10a.tgz", "integrity": "sha512-YKn9O/kIV/lddEbsnd+h/9XwhuzPyS2SS7tG6JF1Ii/zxSGULWWw9nbi/uG879PvKqC5zVsPMbEtm0BGmPpt4g==", "signatures": [{"sig": "MEYCIQDxywp4hoPOKpjl5zhHwu0DpUZ/FE9SJti4FS/HcX+amAIhALuHMX/U+5DpWVXU9fcvyb1hIsq+1893rZ1vUve5awBL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "1.0.2-rc4": {"name": "nodemon", "version": "1.0.2-rc4", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "6f034419b067674a16bdd04c060a6c42e19d52dc", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.2-rc4.tgz", "integrity": "sha512-cwGjYufnSskdFnSBnwvJSXD69zAgT/2piL4MdhtYfrTUQXEqrZfSqsWWtTdKK0bD7FKn5dbYf2AxUfsbZvooLQ==", "signatures": [{"sig": "MEYCIQDVKQAvTKnntbbFNyzJGUSdYOPI2uIgE7HeZv5/DwE1dwIhAJl6IJ1yj0uZssgcSZIHhO6j8z6ftsTTKlidWTJ2i9q4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.3": {"name": "nodemon", "version": "1.0.3", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "2584dab812b3e8f214a287b70d5288b33dbaa7c5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.3.tgz", "integrity": "sha512-JwUrBNR+GqlKus4aUAZTU1gSiz1eQX4lWLpeT/sqVOvlANhm9tkdm4xL2hZeSelEAVHsNkHpU6qJNa78S6dq4w==", "signatures": [{"sig": "MEYCIQCKjCI4KDTgB2KJ8Zc/iXQS6RDW0hyPjWUxi2whPBRW3QIhAOXOATmL1YJGDJCJHN4ilN01neF4LxXhiQK9rd7V8sNW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.4": {"name": "nodemon", "version": "1.0.4", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "34733149b700d5139034c3d3ea9f804cb515ae57", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.4.tgz", "integrity": "sha512-JS4rq29Vki7zvbnc2T7ng6EFME19Ee0qMiSOC9EmhaGtHI1vWL87IzZmgP+Yjl7T74MUlCdB/J0Mu67YC9ipRA==", "signatures": [{"sig": "MEUCIQCBopmgqeV4yMIWmrevRAQrJYTu/kCwjW/QIbVt0sb3LAIgFpwV3zOBaA5LkzVKZaspGDHRVjk0eSHKPbQxeXstJ6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.5": {"name": "nodemon", "version": "1.0.5", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "795f57d018791bae6988b9e34b34bb4a11c84d21", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.5.tgz", "integrity": "sha512-zz3s3pzYkv4yD0TGxLjksPOUtqzpT5axg7H+pFtrhbuCB/hwSVBnLNXI2nRpGMtZZKV+Wid63NQYOAMsl+WLeQ==", "signatures": [{"sig": "MEYCIQCXudECm6l6cLf+kXrmq3Nd7sxBld4IJ4ykdtE+OutCTAIhAKA3pLPCk4NuLwMcZ7Lll1gmnSQ5VMYlb+Dsf7YnfoSx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.6": {"name": "nodemon", "version": "1.0.6", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "329802ac92738b4317ea642b5e8b02b55bab4746", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.6.tgz", "integrity": "sha512-B03b2QDq+u2PRMBHwtLQxaORmWN0oOxAvtG/UEfKBM4tQo33TAdLIAkbBT1Pq02GbgNWWFtUjX6QYfUJXSvZOA==", "signatures": [{"sig": "MEUCIQCwAjDwCuGlOtxol85AqZF10XURuKvmkELHSmx6o1II0gIgY39oHiwBWPk1UfvcrhcCtTujmVw0x5g11SrkKcWgDM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.7": {"name": "nodemon", "version": "1.0.7", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d3091ab8df6beab6a3389fd2bce99eb8b1f0f106", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.7.tgz", "integrity": "sha512-2HTcZl0pMmqI1o7IyyoGVOIW+acyTkNSr83nbsH79f1WtRJECmkClmkEu0Z9WLmcxDxrUMZ5M+jBeZ7ZckW0XA==", "signatures": [{"sig": "MEUCIQCWZR131Eh/Oy/AlYyxt3Ro3SRTaPEe+I08Jr6EsW1bpAIgVE2iEB9Ou6aw4C6KiV2hLiqqGXRz6br3VvW8hG6O28o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.8": {"name": "nodemon", "version": "1.0.8", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "fa737c8f0f331ee77c23f993ce3205615f0d27f9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.8.tgz", "integrity": "sha512-R0HSpKR0w4O6YsVnpLF2kuECqP19sOKOhVML7BvA1mOTNjssnteFgon3AbkkNXgaOm4HWj+s/+xg/gCzKqIwWQ==", "signatures": [{"sig": "MEUCIQDMipW1sOZKqE9HEeHDXDkW1BN3PmlftWKzRXCzEgYYyAIgDVe1tfwGIQEOgdAcUUHaUqWhn2C27bW2IG3+oWFNapE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.9": {"name": "nodemon", "version": "1.0.9", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "bd58683f556f2d7af663453554da99229a83c385", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.9.tgz", "integrity": "sha512-Pp5zGTpqoo4RE/V7e2MFH6pOO8m0BlnapCOhtLpcclO7xjtvr5yuhIpg8Eg4TrTM32AJ7QILWyP8wZCzYUI41Q==", "signatures": [{"sig": "MEUCIQC60R6PDx9oPnRCLrjv0hxDRY6+WetcMRF3hqplIVyhRwIgekVSrK+krCIZ1mG/dN3i/rhcVg/ungr3a18Y+VuuA8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.10": {"name": "nodemon", "version": "1.0.10", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "7b7161fb57539acdec931f8ebf22d9dad19d985e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.10.tgz", "integrity": "sha512-qxbGdSYx0LR3cM0RrIbLrjZztSdtWiOWE5qgIDPF0J6TItbATqgCw0QV3NRV5hTuJsidqhtYqvdAE8F9LmWqPw==", "signatures": [{"sig": "MEUCICgUgPPC2FYKEu0HKSPvMBM8HYsf8nQn1xLIfahXl7vDAiEAotJibSPKZlaBaXQv4AOwWXSxtXYxZJzObfmM8/frmlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.11": {"name": "nodemon", "version": "1.0.11", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "121f0480e172bc8da6e40b019a212ecd55b21e30", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.11.tgz", "integrity": "sha512-VUkdOvQ9tVP1JwiHv07+MNcHNItYWh3oMADwbssfyV8emCYeHNb/zbQ15JfgO6eAfsG+z6kcBsdAaW32sTfiww==", "signatures": [{"sig": "MEUCICiYM9Pn28utNfn8gEegYbqjVENgNB5o7ZE4qpznZ8PiAiEAq9lAUcbYWe7vGBaL2CNaPie3U8fb1lD4vSfyQLzv91A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.12": {"name": "nodemon", "version": "1.0.12", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f7b59ba89f9e9fbcac7586cff7aa60d0fa500c4d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.12.tgz", "integrity": "sha512-WBHS9dLsdknmJzGQTfL9rN6UXhST4HE0+HgnX8S3F+h/BP4yvisDZQPR/uLNOkDe9dfnzZht1nELmhn5yiMKvA==", "signatures": [{"sig": "MEUCIQDR16Xd59VNXa+WkU64BNX/L0wN9UbbmtuAqJx1NIcOYwIgZ/WbubokamBfKh1SErhw/KiKRcv1LCHqFiwFPOmL/28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.13": {"name": "nodemon", "version": "1.0.13", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "7ff62ddf0ba03b572fbc22901b33d53531034410", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.13.tgz", "integrity": "sha512-/sUQjatnMIs7NU53EqRXLRx1LYt+Ke0lH3YtdgQz8on5M93SEtQaNpiwcHZ9ODb1hrAyxPMGPPoIwaeA7vzhvQ==", "signatures": [{"sig": "MEQCIHV7LbLulktH71F6WOuNZyYy5ph+hXJZl3vAJ/SugA79AiBtp1/wuqPSLiXgbPHP0RsWbHLCelDKcs+bsmWTZ6rceA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.14": {"name": "nodemon", "version": "1.0.14", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "415499efd9262b6a76cbf37385876c3343f4f0c6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.14.tgz", "integrity": "sha512-3kDduEqNXgDvx0iNk/HJJrFHCJ6zga/TLDOhtRJtGIM7G/rhReQP/xJjh4cNfQEmtiFn8BJNF3aAxjgn30wBqw==", "signatures": [{"sig": "MEQCIBm0ueC6/rpJ2oZAI7rULE81UnL2ZQkbiuAec4Ne14EwAiBQiViy6CLPWkRLtcd7cWNSbHKrVtjLNftDulQMeuG5Zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.15": {"name": "nodemon", "version": "1.0.15", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "32efa6b8134d579dca8894cd9e8a27d381387f11", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.15.tgz", "integrity": "sha512-A6i7tT0nHpBZzVFKyW9MdVv819BluL6x7aw2hkqGNciXhiT+EI0WXAbYhEM4/A9eeO4UhS50UFE7eta/O5SOSw==", "signatures": [{"sig": "MEUCIQD99Q3y63XHJ2ktLic2/yVN1YEA2O26xF8A5aMNIDQKmgIgUZFFfLN2xdnWsOIaPcPsSHJigFEwniZ1m5NJxH+zUNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.16": {"name": "nodemon", "version": "1.0.16", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "ca169f94d4d0b7397493d119499d90cd69415a1d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.16.tgz", "integrity": "sha512-vIrvle5OnTyUs7FZRlXp7NgHTLCF6ZKRMuBJmNkiL76SAoFCQs9TTWOm/QfZQW94t8T/PDeQWJb23QV14aGMlQ==", "signatures": [{"sig": "MEUCICchEBIZ2iSQPADeeYhC3QYi/Pmi1KZqqrc2iAfnLmHlAiEA5QiSDH8aCBEt4W9xk5wfFxxWQNlGL9QbHDbQwzQIe4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.17-rc1": {"name": "nodemon", "version": "1.0.17-rc1", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "1ad567783a17548844ef2913a5ebc7e70f7a2fbb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.17-rc1.tgz", "integrity": "sha512-5yAfQbkT+jUwmw9ARa+Wb4LTrOabdJML9v19JqnbiSb+jEpcfmwcC9V/IzdvfALF694HkKabyfcI+I/WOWnffA==", "signatures": [{"sig": "MEYCIQD3d8aI34sZLc2+YycmRD5b63iWprOTgedR/jEyw+lfVQIhAPwJIe/lTG9b3sCqMS1ckDsJvcnQHRgj7MqK+g653ffZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.17-rc2": {"name": "nodemon", "version": "1.0.17-rc2", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "aa278d7051926a260f66defd34b7e975393eaf65", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.17-rc2.tgz", "integrity": "sha512-/XGzbNR3OR+UIA5P8IIRWNLW8WIx/ERyHVQvomcZQS3FSQn25Ize08N+jRkCjI0ceelv09B4jmL5MWeiyObKhg==", "signatures": [{"sig": "MEUCIQCkz5M4AJL8j8fYbI1Z5XlSk/ScKUWICtMKA1Ra2H5QQgIgLEZJFHUyw0K6UV7vFwSlkOqPdz/t8t9MLI2RHSWw0dQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.17": {"name": "nodemon", "version": "1.0.17", "dependencies": {"minimatch": "~0.2.14", "update-notifier": "~0.1.7"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "910a3f3cb66c06a3499498455cf4e0119f75799c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.17.tgz", "integrity": "sha512-LO3PVrU29obayRcLIlvfe9Z00dW8qnsBZL450fp1aP5Ln1iUFrSFDQijrSd0Qh5u6U49n3k0WSP2D8FLsgvq+w==", "signatures": [{"sig": "MEQCIHxSWGUYLhV+MktIJ6PovTM2Rt9nm+6nzYL7m5CvQI/AAiBbyhkxKSt6q+cI/kgHUQFsLRmHjuKP9pHYhk6WliEFtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.18": {"name": "nodemon", "version": "1.0.18", "dependencies": {"ps-tree": "0.0.3", "minimatch": "~0.2.14", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "6447a8c2f2ee3896daf7ee596c4459212c80dda1", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.18.tgz", "integrity": "sha512-Z6iADPgDsniYqaQogNF4PZQOXqt9AN9NMbZv9Isc608awYTcke04lO6HVDMPuHwnwhoAd0G1iSGjyT3x18ACdg==", "signatures": [{"sig": "MEYCIQCWf7EiNOedy74BO0xwCA8luLYNgMl5kxV24+LK3C0viQIhAN+0kZ/5iha72fYUhdhe5JYQ0PdAxVi3ywvuTrDG6Utp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.19": {"name": "nodemon", "version": "1.0.19", "dependencies": {"ps-tree": "0.0.3", "minimatch": "~0.2.14", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "9babf124cf36bd620594379791f975379a0f18a6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.19.tgz", "integrity": "sha512-sU+5OSxCyMSirPcFfdvxHocUOu+YEaPob1Evw/5rL8T0JilQZKiV//9zKm7Lqb9A/rKRR2WpqpxYk42aMTxwAQ==", "signatures": [{"sig": "MEQCIBHvum6hz/CimwY5XYnHSvPi6sNLl5d5jxjtCLzaBigTAiAjofivuuMi8sfU4rNSlYvL+LpBXnYOhFfL/As8OKoYkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20": {"name": "nodemon", "version": "1.0.20", "dependencies": {"ps-tree": "0.0.3", "minimatch": "~0.2.14", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "bc138a37068cb78dba50885b62497afdfeeedda4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.0.20.tgz", "integrity": "sha512-MTeBdvWV6EUbab3SRUOhOQPg8RkQP5iFlLVLZhQFsPSzRopYqsDyraTcasm4MR3CVD7hloDA+rut7MIC+w2r6g==", "signatures": [{"sig": "MEUCIDMeXuI+SOxveJTs+DPTWMHgPCIgBCXiv5JCYnH36a/bAiEApCJfcPEo6VdBqD7poRKk+1wLrF/QSTV1rWHOLvQgIcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0-alpha": {"name": "nodemon", "version": "1.1.0-alpha", "dependencies": {"ps-tree": "0.0.3", "minimatch": "~0.2.14", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "a00d54e6b57809d6c9b2fb52fbfdaa84009c34d8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.1.0-alpha.tgz", "integrity": "sha512-Ku2Z2KvNQxTQReKFjTwrZeie3L5rk8jKZ2zGqQKcaZJ1G11U8pWWd53AzGVHiSeyHDaXfoCpq3qf0XJwTxC/5A==", "signatures": [{"sig": "MEUCIGDmWquEpHUSbqq777kixO66VVq6bTyZZLdd//+D04HGAiEAiACizhJn2B/lcCDBKOA4IaLagmwNJCLf+/ccXRxMFas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "nodemon", "version": "1.1.0", "dependencies": {"ps-tree": "0.0.3", "minimatch": "~0.2.14", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "98b1c90bbf3f2d775bd12979c2dd45827e47b1c4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.1.0.tgz", "integrity": "sha512-B+juQYj/W/qgk5HuAPi5K275iiF3GOrGzCkMdZ3dtG+4c7Lv69EEouVMWfr6+PC/Fd9BreXEjD7CBO4lDpkqdw==", "signatures": [{"sig": "MEUCIE7E3RL3WlGHTr+2td9zSKoOf9ReEeZLC+LJGbXyBMEzAiEAz0ZQ7Jt6kndc0DEdHHwYe4ROnk+GSt0n2TBLrg2VC/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "nodemon", "version": "1.1.1", "dependencies": {"ps-tree": "0.0.3", "minimatch": "~0.2.14", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.12.0", "touch": "0.0.2", "should": "~1.2.2", "connect": "*", "istanbul": "~0.1.44", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "e82b1ed6d0c31001ebb9d0a9821bb0777af88003", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.1.1.tgz", "integrity": "sha512-0L<PERSON>bWG19cXO1GIPbmF/AoEKq7XcST4tg5JzhlVzgK+mNFz94wSD2r90mGxL/dF9/3eCmwxrvIC2oEi1Sw/sNw==", "signatures": [{"sig": "MEQCIBlnsXFh1nH9Ey12HHQZVnayFYDjKJ1mlP/hxJp3wCDwAiA6NM3CpR0uiXLI4nzUMempx/+/ywZQ/EA4hp08fngRdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "nodemon", "version": "1.2.0", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.20.0", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "fd64fbbd2730c828fe897026ada53ab5ae42727c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.2.0.tgz", "integrity": "sha512-v/CfLaq8sneTJS1SzVUqI9EV2DSWchetapkZWdfhjEkQizOZbSjuV9bXAo+g4cNrXJyXq9auP6NyOG1ANdmqjA==", "signatures": [{"sig": "MEQCIELlyGRwmeHWDqmRXao+vzkGY2derNANbm79POdnHVJPAiAtLjLPQVvtoSUNw7nnWXcSYCsaLiMkqpdAjj51ttq75g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.2.1": {"name": "nodemon", "version": "1.2.1", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "~1.20.0", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "02a288045652e92350e7d752a8054472ed2c4824", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.2.1.tgz", "integrity": "sha512-M3FKiLXCn89jYxrajO7OnvesSmz4oX8zTMQgk8ipiW/LqBmobpoyCCqzEQ9+F9Jw6yf4QoKyA4R6twa2FiUDGg==", "signatures": [{"sig": "MEUCICjbL4QMYkbvgtbqopYvidwQTGDarpF71uON6Xr1bS17AiEAnvnjeZgOurDGOuGb+ILuWTCQkuMnZdKnwyOOJA85cYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-0": {"name": "nodemon", "version": "1.3.0-0", "dependencies": {"mocha": "^2.0.1", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f2885f58eebe5887fafe84297cbd7d0f012188c1", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-0.tgz", "integrity": "sha512-rTMxk3Trcm/R0rON4CnFHBhC1+fpVNcIpo/thJD48rPcQVPTbdTqIMWuDzAn7eRx06hyX+T5XE2MwEOTa+rUbg==", "signatures": [{"sig": "MEQCIC9j/3kG002dV0QX1rMpIdifNqJai7Z4Ifdia0lyYOxpAiAYNCGkQPktwFHnPU4CF25QEkK0IVaoTF+OAecVGtDh2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-1": {"name": "nodemon", "version": "1.3.0-1", "dependencies": {"mocha": "^2.0.1", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "91f0712601bfdf84122c5d2712177d8288a53773", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-1.tgz", "integrity": "sha512-o8SU0W36WT0TeuMM1TMbLJdvaYZRE/STWR4sGLtVq2ehrrESdtGHbWntYBiWiZ3sYfP5yRehG+UbG1MVT1Q1zw==", "signatures": [{"sig": "MEUCIHyQF5foSWXWIt//Mr5dJUY/KXymel1AK/QosWhZ0JT2AiEAwRXHLDeZVsqSod0nzX9rwXkx7MXKM+o60glIV3gEcOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-2": {"name": "nodemon", "version": "1.3.0-2", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "7d58cec9cee3b124cc6dfce76e9ea1e1820c9791", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-2.tgz", "integrity": "sha512-bAUoVGS0RoVuKgb29c5hm/LRxlJ7zKtF5ziuwH4ucrPgHKM4uSO1srM3K6JVZ5zZ2FLodGBAygrsaHijbdwtyw==", "signatures": [{"sig": "MEYCIQDu7g3zaUDhoQqHP+3FHehRjVX54RDxcXKqxYKe+h0plAIhAIcokGQhesOi3ZzPlsQMBsm+IZfiY4XKnCtipuflRZ8D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-3": {"name": "nodemon", "version": "1.3.0-3", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "32f217fc7819a9a42f9c8e2cec6a9b37b4a13ae1", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-3.tgz", "integrity": "sha512-l5VUR4B4FBSHcFYHBL0y4U0sRUlYlSXdVBAtd9wmnGDOC0119W4+JHMhMv8fBLt+DT6O/VT/tGCWMe4ekDy0KQ==", "signatures": [{"sig": "MEYCIQCKgHrCeryqIvKWqo+nAZ1mM2biNZ6tRN4ycf3X6VMjJwIhAN8KEpVKIxuYF7BY+ICDKXiOByMm9IsEZ4DQWiHyYqwo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-4": {"name": "nodemon", "version": "1.3.0-4", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "e75fda16652a0f2e8bc9c941542262091da01453", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-4.tgz", "integrity": "sha512-DmhPqA99cBVTnan4/la9nUXtqY7CDuPBInI7zXnvwDLzC2UA9/XiUBF4McM4AdCMU20OJBw7a3bPGpfBQPVQug==", "signatures": [{"sig": "MEUCIBD2euckZqKBCdnWU0PbQ0Pzwwj0NGz0bIcipTk8RxtVAiEA2M9eBojA9SBihQnvQhfKb7M+vtmcOULv+Uf6gZ6k19c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-5": {"name": "nodemon", "version": "1.3.0-5", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "66a2f46151288e80496236171e605eb80ce244dc", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-5.tgz", "integrity": "sha512-zdaMytT9kR0eb+nuvy6WXXEei/ZAGRShfpmYKECxJyr6fZByaTyxVg+kgOB6MQD2CmqBaZaJVTI6nGPwQy0Q/g==", "signatures": [{"sig": "MEUCIQDSbAdQUos6yvW2g1Dt+15OnRXx5PHZ1UGapAFmEqwDngIgI/LHxwxcn1V8UM7LSUuVKmB//pTsaVasIYzYBHeMzHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-6": {"name": "nodemon", "version": "1.3.0-6", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "8b2c8a4397da6138428cb6a6dcdb4288a89d98c8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-6.tgz", "integrity": "sha512-m/qPAFGsK1oS2aM2GGU2PuDbf6jcmcoU4GVrAbkye2o+jBxgW1lxd/8kIMbIomnOSkhUs9lYfuk6rL4nMExM4w==", "signatures": [{"sig": "MEQCIEXKI4eUc4ZAC/BrtMrPKb60PTGVZQgu+G+jSuo6CErRAiA1pVZ/URLD8fyMGRufohmiGsVPbnlfsDm7yApQANP/jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0-7": {"name": "nodemon", "version": "1.3.0-7", "dependencies": {"ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "touch": "~0.0.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d52e76bfcc628f2282c45b53cdc0aa5a3645d68a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0-7.tgz", "integrity": "sha512-cUa8r14bbO5/5N6e0+EcQmNOSD73yp+JIKeh2CKy5BSBU+cjYeuVteGw0S712QfMGXnkOYBrbkmKZr5L1okjew==", "signatures": [{"sig": "MEQCIH411C2QmLNm8eHlJC7XHff0V9EVOvLRCZciYmZvuW4PAiBB8qnb/4zRyNezbA1bcMuMbNoeJmt2fzCCZJ7WwIidhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.0": {"name": "nodemon", "version": "1.3.0", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "2a814bfc748a72f2b9241b9870070ebaba4c5c2d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.0.tgz", "integrity": "sha512-mXl8+1yV5ossmv0Bw6kEw0uMxDYDmBxkmUXgydfERH1UAeW1UIuUFBRJMzxkVAEy+/RsDaWCGuiznY1Dza8Gqw==", "signatures": [{"sig": "MEYCIQDqPdpZ4gXCH952yOwE5M5rEdGxECaQbr0HHMigUzOJowIhAM6QT7yBwcCmGHBK9Pcaf2wVmFtPMrGtg+eufLcbCv0v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.1": {"name": "nodemon", "version": "1.3.1", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "17ed3de7d1d3cb37f8bebbc4b1b68dcde8d5c9df", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.1.tgz", "integrity": "sha512-+NEK30YRAlxxj6AkghAVBQm89b77UTRdrsRmgf59YpS0vv/nrpRolMr7SXIBMUJxaSxFjuh4aD9GAwMidzaKHg==", "signatures": [{"sig": "MEYCIQCbwzqmM/e9EBnZ5k/W1urMVNlf+0Y89C/8jLAhd9BqfgIhAJAvLiLKGadhuf9U5Z9iZbylMlodf7+CpJ05afIldyGp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.2": {"name": "nodemon", "version": "1.3.2", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "9d54b0731bb2ce213169ebffd5bed9ea988ae9d8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.2.tgz", "integrity": "sha512-fC09swxAytg+SQMRN5nXpmxTPGWccKXJ10L2enfVeBNedvNo1IWvZqSNTT8sNrJMOK8cDywMdOJml174WfUgpg==", "signatures": [{"sig": "MEUCIECKgWONlW79ekQjnclBJmY3yFfmaTayv3aPSOQNORw1AiEAibyokLf8qv/8YmU5lnOhV0mGOW52GSNZ4/NirBCGsG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.3": {"name": "nodemon", "version": "1.3.3", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "~0.1.8"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b8927bdb68f72a70d217cc663cc1cd44f46f00d8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.3.tgz", "integrity": "sha512-eb<PERSON><PERSON><PERSON>bh296hT8itYDZPG28JT2DFh2gSqP2FP2Ahu/mw81d6SGbswQBuUrkNnEMwJOk5qs6quK7y52pLe5U0w==", "signatures": [{"sig": "MEYCIQCty5+n3A7NZ9Yq6+QB9YCTjcjmcAXJn+rS9LOSRcAYJAIhAMWQw8bWtg1pek/wq4WuBLJI0MYN7LDZYguczcHMM+/u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.5": {"name": "nodemon", "version": "1.3.5", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "8ec82d02048474c652d8ad3f4d8a23adb13c4be3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.5.tgz", "integrity": "sha512-A2Y9BkJlSGfbTp7jK17YTUO5uZ1Eq2P1NJRR2YcO1xmbn4kJGq/bNAF260aJtYwb4CD0g7n0YDW6kCPLh7TSjw==", "signatures": [{"sig": "MEYCIQCfHuX+qAH3Tkh5CBhxiT53zrwk4nEJcFqcXGOt33WvXwIhAOpNv9KM39LpEaRio2xKVj+NST56jLd/pt2+3cMqcvkC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.6": {"name": "nodemon", "version": "1.3.6", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "c4d5e43dd797792a8291efd130de02e5520c4998", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.6.tgz", "integrity": "sha512-gEa/FGmuP3WqJBQrdLh7uBkbmP/46aoldJ52q5vTYuzlYUr5eMaG3wXfvkhMLWUt58+yysR5QzI9VHcP43OIEQ==", "signatures": [{"sig": "MEUCIQDInkvAUhWk95IMOVhO07QzafLhJpHT0aTtkEq/Ld89vgIgOtTyqrsxLhJz3X81h0EDEvhLDjsf6p8MubAQM+2C3/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.7": {"name": "nodemon", "version": "1.3.7", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "447bc2e01bf499cbf5853f9937496e0a04835449", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.7.tgz", "integrity": "sha512-Ejc77BZ7CjD7VsXkzATamDeDY5nMWkW1MqpVOMnLRZRMtmZseBCfhjSI8dHAb+6ZlgCeOWbmiOscdYk8BOOaxw==", "signatures": [{"sig": "MEQCIB1p8dS4eRpszgttidkeXYoP/kZ9IA8os2aDQl/X0j6kAiABye0QisyrKj8fxHMjRhbdVgawVX84QD3Req6gFOcHvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.3.8": {"name": "nodemon", "version": "1.3.8", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "40723a96278edeaac48205a1c570cbf4070a6a8e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.3.8.tgz", "integrity": "sha512-IXd6xoERdzvJG0bLrX4mnjHnys2rMAsxIjuHIFew+cPkjTx2isJhFCA2IdGf6VPtVAnwDLhtzHO4+CfzBUFZxg==", "signatures": [{"sig": "MEQCIDMOUqJQUYXIitvEpcZ2A4rDKmFPlBX1TpPzq023IIEQAiAo4rCk8SMK3SDuZHlkljRWS0/ugQtH1gmgyDffUZI3Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.4.0": {"name": "nodemon", "version": "1.4.0", "dependencies": {"touch": "~0.0.3", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f566146245c82910a4e7d051c10afe0535e9394c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.4.0.tgz", "integrity": "sha512-dbi6<PERSON>y24ry9eVUbxPf6rg2Ns2Qc+/r41+xDcyWgbnHTuW6Qc+AdaATO7taVJ+uTB3820CBkH2J6GPOEoKCAhaw==", "signatures": [{"sig": "MEYCIQC7uW672ROqI/cz5fyFSjkS4cXqzscDTDmFqEWCVYSKLQIhALzVumhq47t5TS2frrHWXxkLCcIArjAJ+VXVtTKC1v0Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.4.1": {"name": "nodemon", "version": "1.4.1", "dependencies": {"touch": "~1.0.0", "ps-tree": "~0.0.3", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "ec40ea28e832619b6bf9bc9370edcc062966decb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.4.1.tgz", "integrity": "sha512-nuAIxNTy3GjNnL8gYTKBA4SPaU7AMuucmMWHOnf0M+b8c4oF4AuC5XktkrKtzKQzNyz98awXBgHQC84T+e+XUQ==", "signatures": [{"sig": "MEQCIAd5j8cVkZfqB+ipAIfc+kNY2HuZECPfueKAEWlFec2KAiBqw2zDW2JCbeyLqpuQkJjcjiax5ZqtmNTfkS3dGCbhtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.5.0-alpha1": {"name": "nodemon", "version": "1.5.0-alpha1", "dependencies": {"touch": "~1.0.0", "ps-tree": "~0.0.3", "anymatch": "^1.3.0", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "update-notifier": "^0.3.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b2f008884127d094ca6d3b0d4a0b13425e67cd8e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.5.0-alpha1.tgz", "integrity": "sha512-iHLRSkWOlW95gdxLPk2Wc8qK3b4abZ0p8k1WnqtYsJoC7xcJJJfLvh0CzqeRpyc05ayT6Ym4UbmLWrAKzEWzxw==", "signatures": [{"sig": "MEQCICl7vezLGR8O74yGp/leei8K8qkDLwrwDehaYt8EMoptAiBgrBJhCvzFIrAxrZGL+4hNhoFZEwNjiCbVmcMjQweWWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.5.0-alpha2": {"name": "nodemon", "version": "1.5.0-alpha2", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "update-notifier": "^0.5.0"}, "devDependencies": {"mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "1cf9920769c79403a9b403185091544476c45dab", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.5.0-alpha2.tgz", "integrity": "sha512-xkQsSOqkDMocUWO5M5nKaG9GMJXl8ptJsEmK29UjDJXz8wJj/C9qb4DARpd5MlrFpu1z0viK9Fd3/1XL04ZOnQ==", "signatures": [{"sig": "MEYCIQD/WJI8xt3UZgyhk5HS1nj2sMIYXE/hOsSaf/7n5nCCHwIhAK66YKhgD7hKCD4r6fYWMgXLgJ4X5jit/V14qkogIZg7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.5.0-alpha3": {"name": "nodemon", "version": "1.5.0-alpha3", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "9bd386c66f0f7642eaeb3be46606ca0fc3967c3f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.5.0-alpha3.tgz", "integrity": "sha512-Knnvi5RwhhiTuQj2tw44lYM+TQRFbz5mpnrd/SRPnYQselqzyPBDpdouO2qnLeKO0PcUs6DsRDIJn5CwM6D2kg==", "signatures": [{"sig": "MEUCIQCwKNqCLcWQRPwIWvyC9yDgHxTxB1W7OLGSbDm2+5VYRAIgaIKV3tDEDSbHseIt0AWFSSo7vBLzpF6XlMHn+XNt6l8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.5.0-alpha4": {"name": "nodemon", "version": "1.5.0-alpha4", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "038979ccc37f3ed45d475fdd5c3924ef1b41a77c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.5.0-alpha4.tgz", "integrity": "sha512-YGmoU1EvsXxVlG+QWYw+h2D+I024dGDCWms93t3cwKA/HnVQaZreQA1uog2A67mOmkv9MJqQrzd1IIV1KRj6Og==", "signatures": [{"sig": "MEUCIAKBHB72JnDKIbWjlgRhhwNujd4J5EZ3G/laJAMT+LWoAiEAzYUMCQVpe6ePwMEmlQxoou8jHa/U2kTEQIaAvmglokc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.4.2": {"name": "nodemon", "version": "1.4.2", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "^4.3.4"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "796e4e28f8a995e95a9fda3bb8b1b89931aeec52", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.4.2.tgz", "integrity": "sha512-HzGTzIEBORCPd8264R3Bd6/V/wjLHVepeiVhFjSeMldbRMHOUZyZQe53oXQNAeV5FjcTMNbemiXMOlN9iv5ADQ==", "signatures": [{"sig": "MEUCIDviU8YuUBSpV0Pfm1TL2sSGqM+chAyOOukk0jED81hVAiEAy7XEh+vUWxhwVX/D6qD243d4eLNYBUTuNG4sjZZQDiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.5.0": {"name": "nodemon", "version": "1.5.0", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "^4.3.4"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "75c7737fb13f91da3d85620aca24ba88da5036c3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.5.0.tgz", "integrity": "sha512-cbQwUpxqoGCzN/E+c0fV+ej0xzO/8pBUpnHuRDHoGoPEFFHFx2mgiZrqNdnLocEzHISSD1YF5dnwk8OulyyOGQ==", "signatures": [{"sig": "MEUCIFS3ZdOG5bjQe/VY2hPRCw2QT7wmBP5YnDdYDNdEnSZUAiEA/ua620btD8ID2UvyrlZGk3ncSyBmLsA9SySxC18WQUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.5.1": {"name": "nodemon", "version": "1.5.1", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "^4.3.4"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d327b81114f11c409c7bbaaa5185e116457e3720", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.5.1.tgz", "integrity": "sha512-XWUDo/n9mfMVsbP67wW2zM+DE5i7H1L8a9U2try1TZ5k3N0tOp0aBB1VjXvSShc3rrW/mVxIGNN4ExD7uJWsYA==", "signatures": [{"sig": "MEYCIQDnj9MWYGj42otNIIjdSdjl4CPEnTxwXGXIeGfM1Sj8/QIhALAnkRbRljfGl28EoqMvsMhGvhAkHRIqFQIU9zSugwJl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.6.0": {"name": "nodemon", "version": "1.6.0", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "^4.3.4"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "fa89c965762ebc9de9aa43f4059a6616cda43e7e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.6.0.tgz", "integrity": "sha512-DHdCKsKjwDiZM91Es24C0mWX7rluN2H9hkt/BTDrV9zZ2vfY+fNvCL17459z09xZ2qepY6eRQSQ3ME7wGx2T2g==", "signatures": [{"sig": "MEQCIHJHGXoXbWLkTwvo6frj0yAQy0g7PGPF9WeYbfm2Ow9/AiBS+MBCHr2HL6KAlHu79Yzn30V7INZC1vAjNleRZrAmoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.6.1": {"name": "nodemon", "version": "1.6.1", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "^4.3.4"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "807e065ae2f69836ca0d83a2a9ce911c119c6f77", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.6.1.tgz", "integrity": "sha512-uFH7cUDkiQu/+RC5fENgglG5TDcv4I3jq2mNnc2sM1c1WElKgJ2ZvcMMVwLjPHM4vHnT6WqVJ28EerYWlEItqw==", "signatures": [{"sig": "MEQCID6Hj/ni2WVRZFEW1zWaAC2zCf0p/u0BapHYxB5kkv3hAiBfXg1w+SaqZDpPXZT7XZXW3iH/JUekJ2wMy+UZww/Wew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.7.0": {"name": "nodemon", "version": "1.7.0", "dependencies": {"debug": "^2.2.0", "touch": "~1.0.0", "ps-tree": "~0.0.3", "chokidar": "^1.0.5", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "^0.5.0"}, "devDependencies": {"jscs": "^2.1.1", "async": "^1.4.2", "mocha": "^2.0.1", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "^4.3.4"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "7c691e2b8e62f351c447e05d07b529f304dab044", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.7.0.tgz", "integrity": "sha512-MpUL/dNriArZEK07WicHy4zxxjDHI/49VVvm0ZOU/mgx3JY/5q03Ip97DqanJnaBUAWsgHkulE0OlyHjRIYP5w==", "signatures": [{"sig": "MEUCIHoLe3pMmrsCAN828/jhjQe3E33W6oQrHdI3n21/SX5uAiEAsoOAfl1+FiqWikyCSdh2K3u9YQtpOvuFVR8UMU7WFuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.7.1": {"name": "nodemon", "version": "1.7.1", "dependencies": {"debug": "2.2.0", "touch": "1.0.0", "ps-tree": "~0.0.3", "chokidar": "1.1.0", "minimatch": "~0.3.0", "undefsafe": "0.0.3", "es6-promise": "3.0.2", "lodash.defaults": "3.1.2", "update-notifier": "0.5.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "922633a03b6835c2c29013883be4c7b4fc5f24e4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.7.1.tgz", "integrity": "sha512-ac1aMFg1e+azkNM4MnkA9OVxs0T/1jA8mjr+05D9QraN0J61jQyjk6XpwoYdZHzhRvfOoLSDoFRIb7qj5Xw7HQ==", "signatures": [{"sig": "MEUCIQDOsmC58NGM21y3dhXnO8oAXf+qCjK/stTIQHC84c/mUAIgXt+wlh0mnqGZ50N4k0SJvCQ2zBMLOa+NYOpZxEOJbcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.7.2": {"name": "nodemon", "version": "1.7.2", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.2.0", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "9259190817c531cf33cce7dad2913fb572abf586", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.7.2.tgz", "integrity": "sha512-W0ia4D+NhNsNdn7mIVH1ancdE8lCKau6l6UDssnzY23hDDc/DI+sWjXfhqCG6XFWYPltXP9KSM/Peq+Jml9VFQ==", "signatures": [{"sig": "MEYCIQDbRb44IdBUJIFb1AQ/UYoG+2T+gibbOwHEghLhzHq+OwIhALyLk1Qn21E3vXAwI+Vp7CXp37GQXb30RSRKnahmavMy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.7.3": {"name": "nodemon", "version": "1.7.3", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.2.0", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "65cca6627c0ae9b1a3fb00fa540f5d1347250106", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.7.3.tgz", "integrity": "sha512-2s4/v2OrJpVyWorn3C+guRUZRpT+P5Hy0ypzCUbFwH7fnV9DSARwqM0zdRVT8vfiRBZ/p3ORebBS21+TuYmqrw==", "signatures": [{"sig": "MEYCIQDY6aVS0IiFmV4YIee1PjlzHdRucfRlTY6ysRPcUKU6SAIhANQJ3ZO5YbPVS+xHC+nXAP8ZNPofNr8Jni8b99Hq3vcq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.8.0": {"name": "nodemon", "version": "1.8.0", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.2.0", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "37114e9d1b231975a8be9d36572c99338161fbae", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.8.0.tgz", "integrity": "sha512-+LBnT9zLGZ353NhOE0zYu9S67Fp+m/PG/W4er7RWHZPqQeMIo4/22Y0svPGxXnZq4ND8DPJCS0EmrNJtZiZOrg==", "signatures": [{"sig": "MEUCIQCTHIyty2sOh5OJfld00OGpV3kM2hvOBhtESrTZMMX2sgIgAUjgFxVloFpHidQDZkV34U6lTbRItSUry4GHfA0PP8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.8.1": {"name": "nodemon", "version": "1.8.1", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.2.0", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "75cfd7ac167e938cdab7313c839bc45a1859bb32", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.8.1.tgz", "integrity": "sha512-BLcCGv2AIv4HqVIEuhY/vX00G3I5Ay7bzkLx1CMjWYPKcSGj44i7TRgUDvreUAa0BeBvt6l7QckNOfAswJaUhA==", "signatures": [{"sig": "MEYCIQD7Ae3vTzOyeempvDgef1BhMtAw851AtsQLHpf5mXx3sgIhAKrPMrOafcd/qFdgtTGjlpzjfnuDJljehN7b4ov/V4br", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.9.0": {"name": "nodemon", "version": "1.9.0", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.2.0", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "af62de1765b3c868a29f7504f31eed913a5957b9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.9.0.tgz", "integrity": "sha512-rhT3Dac8x7ZjUPYOJdE80p+1X9WFkd1II77GxLjoLppQss1oY/t7SDl4cAWW5ocuPCf/Ab8yCfeO3hTkK39ZpA==", "signatures": [{"sig": "MEUCIQDm5S4fx+gaKuPHyVQhUmRWddEdheTA3WPQfd1CDLWGywIgM4VNWpzKtEaT34+mDllkwZGlkiuEU1HbfG705ngyitE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.9.1": {"name": "nodemon", "version": "1.9.1", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.2.0", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "442071f88c39d801fb0bdfd209413da5dce6dad3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.9.1.tgz", "integrity": "sha512-OS7tRtrbhtzMYA8U8wS5Ywp5TG6ciWQ09JDVD+QbGkjxqinNSUrWu1eoU4Jf87V9iRRRaohTeYuoBH3Vsc4F8g==", "signatures": [{"sig": "MEYCIQC8AdHOQsvB2uacT5ERefolv6JFv9MuilDY9T2//ZjqFQIhAPZeSIXJihhELXMc6/tq7UCfOKYBnLFfqVwkXDEdn8m/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.9.2": {"name": "nodemon", "version": "1.9.2", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.4.3", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "21b3cd157d5483833b473372c98e1795a4d55970", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.9.2.tgz", "integrity": "sha512-mLB8ugTMBr9Xe73ug9Ncg3tGAAUZ41bx4woi+QmPzOV20xyB6u2igsqO2PoXSj8KX/IGMPA3Xr7FZZJDOqzujQ==", "signatures": [{"sig": "MEQCIHkyQ/lqCPMhpE1qtsMOXbT8bzfdS/lG7v7Tnzv7ApbCAiAxqNWKrkdx9EGh36d1Crm/II/wfq9+g7vG0CgbCcRDmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.10.0": {"name": "nodemon", "version": "1.10.0", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.4.3", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "31f71bbb1081ca88ee86908bbc834e574a5f8454", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.10.0.tgz", "integrity": "sha512-RSJoUo3RimaVIEFi47M9qgoAksoZJvGfgB/xnF7B8YUt5A8q+e5iQQ5skNuWHhROCdl/SxHDcUqY8LFPlkysSA==", "signatures": [{"sig": "MEUCIDaTYshrfKrnJQUbGKrqEbKOmPMJaz1zJI3g+ofF/DMtAiEAjEDTq4BgdkuOzGxkf7VUMP8kqXChd1dBYvjTDoUCVRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.10.1": {"name": "nodemon", "version": "1.10.1", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.4.3", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "4f946b2ab4131e3e0bbcfc0abc4d4eb3fe539357", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.10.1.tgz", "integrity": "sha512-4lVoDQ238q/d8oR0jTg+ChQZETpK8BBOErwynOJYsURzOnEwes04+EG2PuVKvIAERHKYVf8ErM6lRw8WR9Hhow==", "signatures": [{"sig": "MEUCIQDL+wd/Dw8wK/8XVRf+VSwb53qlv8Ro9xUMHr96IvtW/QIgD7mza1CzDldCrfDFeYr74QX6kVKL9XjBzzwM+1i2B8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.10.2": {"name": "nodemon", "version": "1.10.2", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.4.3", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "ec511e14c3ad0858fc121c6006890ed27b7c412e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.10.2.tgz", "integrity": "sha512-o1pGrezGy6+VaerugrpcS4kqY5c7+3/TdmI7FfML0JAt5j2th3vX37jX7l+MoVwYMj/AcSAQpjFd89nN4nisaQ==", "signatures": [{"sig": "MEQCIDncmLbPCvS28cPFtyz71ci+rgsmW3V7lBuaMZFkOBm3AiB+mfJVRc32MUUetlHVmcMvuZ13ZCm8lSrbUzENhWrtWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.11.0": {"name": "nodemon", "version": "1.11.0", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.4.3", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "226c562bd2a7b13d3d7518b49ad4828a3623d06c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.11.0.tgz", "integrity": "sha512-C9Sk4j/UeU/UkQQ5h+LzvsZGuxJ1nBS4hg4w1opCMVv7UuRNGpRvlMr9P6hdMyNGfjMYjATQ5eWBfBqHlCcKhA==", "signatures": [{"sig": "MEUCIAbWqR7w6V3J8xyRmdhEinzy4ZOSLbmpXMfENn/HIdiHAiEAyN6qHhkFbvRKbz+2f4mtCiQGtN9DGwdAwj2rZmWDQ9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.11.1": {"name": "nodemon", "version": "1.11.1", "dependencies": {"debug": "^2.2.0", "touch": "1.0.0", "ps-tree": "^1.0.1", "chokidar": "^1.4.3", "minimatch": "^3.0.0", "undefsafe": "0.0.3", "es6-promise": "^3.0.2", "lodash.defaults": "^3.1.2", "update-notifier": "0.5.0", "ignore-by-default": "^1.0.0"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "semantic-release": "4.3.5"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "ad96ce93dfb1d925aad076697229cc076bba8529", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.11.1.tgz", "integrity": "sha512-C4uGwr1ILYjWn4LY0KvlGIHze2UjbXaJP51kwuzHH+b6j8AMcHfDjTUj0opU9BXepyAnRoQ7L0SKeFg1eyINaA==", "signatures": [{"sig": "MEUCIQCY2gb7m7h/mqQ28TM7ybPu9VeClAiQM1V7LgnV3w0I6AIgIxsLuOmVJYw4JIjblpLMnXd6YmjIdJs3r/a5Tq4wSzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.12.0": {"name": "nodemon", "version": "1.12.0", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.2.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "husky": "^0.14.3", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "4.3.5", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "e538548a777340a19f855c4f087b7e528aa3feda", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.0.tgz", "integrity": "sha512-rV8y9GpAodJCyhjmtE6lgO+dQ5DQPaJpPVm94YvMC2do2cuUanxNRpWVMtUvNDnMYKQIv1zrQETYVn8mgBH1Kw==", "signatures": [{"sig": "MEUCIQCWOsm6Rfqb+Yx0yOAjOh7/r29q+b8SLYeh2YcGr1/wdAIgYUALJ+cMBo16W6EWILiI5W2BQq1eLYMMD9G9bmS3oZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.12.1": {"name": "nodemon", "version": "1.12.1", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.2.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "husky": "^0.14.3", "mocha": "2.3.3", "should": "~4.0.0", "connect": "~2.19.1", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "4.3.5", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "996a56dc49d9f16bbf1b78a4de08f13634b3878d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.1.tgz", "integrity": "sha512-zZuUtiNkYFjGRojUUGR9AKllVrvWPppOj020Ii0twjdzFF7OxMEvgWJjj26VldaYKgdc91C5htYGdMzAAc9Dtg==", "signatures": [{"sig": "MEQCIB5zmx8szblP1xaGl7mcQdEcYQgOszdUOrg0eSUGnWMoAiAEke/W/r6bxgqDrL7LuKP1b8u60LQUngz28TdG995lQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.12.2": {"name": "nodemon", "version": "1.12.2", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.2.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "husky": "^0.14.3", "mocha": "2.3.3", "should": "~4.0.0", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "4.3.5", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "edc51e1589a172429ee30353b6435bfa8e7468e5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.2.tgz", "integrity": "sha512-nGtxYterOArIBFwuOMJ8BUmyreOfE6bmHBKXvjkItB6eTm2ZHTfuxjm43NcrdkmRAz/MFx4URUCfYuObUq+08Q==", "signatures": [{"sig": "MEUCIEAyx1p5/w6ScTzjCgQVRVCIT6hvCy7ykpLWwjrn1Wc8AiEA8G5jqlFnFiRaBOGtY7WlJ7lGXyIcWfwn+yNcgC8WuyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.12.3": {"name": "nodemon", "version": "1.12.3", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "husky": "^0.14.3", "mocha": "2.3.3", "should": "~4.0.0", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "bd89d0b890214104992d02412e893315863617ee", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.3.tgz", "integrity": "sha512-t4f3cTO2fRBBiNtVIG8W19UXYmsClrrQzMKZeWz/b0xe8yQRuRCvAgmVEIvmdgC12uqimAbcrMYv22KUVP6o9A==", "signatures": [{"sig": "MEYCIQDwun+znvvJvtxz87M2BFii0raTpQVMN/iw9pRm/4wwuwIhAJ6j+FVMHbz2cpUm8KA4WaN1B9r9AePepr9BzNH1fHjR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6"}}, "1.12.4": {"name": "nodemon", "version": "1.12.4", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "2.1.1", "async": "1.4.2", "husky": "^0.14.3", "mocha": "2.3.3", "should": "~4.0.0", "istanbul": "~0.2.10", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b3cedad2845ee27298aa889b903b2e324fe57cda", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.4.tgz", "integrity": "sha512-No1zKdY7to4bQD28Owex+30Fmh3OZrF0W6dec1b0jzoGeThTjmKBCfrSo86ACY09oWRw5SCNcCFRF6+14g2X4Q==", "signatures": [{"sig": "MEUCIHP+vH2ODx418TrXsWgNXNzt5Fl1O4lpJ1v3qffzqmseAiEAiCKL07QMMx0QUptC5QqEYxZxeXt2SpRLfW6ummFtnIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6"}}, "1.12.5": {"name": "nodemon", "version": "1.12.5", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "bd34afa1aa855f1996b561147848519f136de620", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.5.tgz", "integrity": "sha512-Kwx492h2buPPOie50cht/PdV+jXLqk28l79Nzs1udrFWIXpYHKCskLict1hTrln4ux61azehZcwm8M5McmiuAw==", "signatures": [{"sig": "MEUCIAp00LPiYijBweTRz1JI1EBucH2v+EhrBaFVhv+9N2EsAiEAnW2rHvXt6A/3p4rgp1oQuglPrqAotPszC1AvXchqOKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=6"}, "hasInstallScript": true}, "1.12.6": {"name": "nodemon", "version": "1.12.6", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "aa0ca0f3b9ea0f09e970e8135b2c9c9aa84eb711", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.6.tgz", "integrity": "sha512-vMMg26uPUTv/NVeeu1oGPPpykVgB9yYeYeu+Dd2y2xsEYmYhof0sH6d7ncdJfhLpHIEwW9ljq6JD81UlQ404fQ==", "signatures": [{"sig": "MEQCIFpnwI7EGm4JFz9G8HAqg80WvfH+yEmynVQ4fMW/vU9+AiB8SapOnOEy2Vetj0b1jhTaSy9DHqQ71MLoVsQSl96NkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.12.7": {"name": "nodemon", "version": "1.12.7", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "4d0fa8386291c4f532f583cc102c05350722f647", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.12.7.tgz", "integrity": "sha512-Dz/9fPJUjtnwA2ceSah4Vr/K4IFsPlYaS0YF3TrppqZxRfQcqN3iN+LnrFGShiUqdWzDBTVZKUnHJRuOjPRn6w==", "signatures": [{"sig": "MEUCIQDVRjpyY8J5MPtPxL3zD9bR9qU1JE6OHNOMmr9B461+hgIgckTgZyTQExkm+xQv6nfV1/IT1ueb3YmXS6wEpBKWQ8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.13.0": {"name": "nodemon", "version": "1.13.0", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "ps-tree": "^1.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "de794b62c3fc149151766681993b565ccc91c064", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.13.0.tgz", "integrity": "sha512-HfLGyLs64g+JlZqS+KvvTrEtZB2fcnYhzzWwfeSVlQSty6XKwdfqe8Psn3U7FibNP9Ra0on7reT1BvCwgtpzfA==", "signatures": [{"sig": "MEUCIG59HMZXOSgT9LpMt4NjL6VzPaPLOHNJ+9Hwbk6WZ0GrAiEA7ZOKZCtk3rw4IyTWP/VkTa8keBh9X7Nrab9Lzysarjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.13.1": {"name": "nodemon", "version": "1.13.1", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "@remy/pstree": "^1.0.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "881b614c60e0a166c1f818002bff5435d95b8109", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.13.1.tgz", "integrity": "sha512-/WMK/zOJE7pjfyTFmKRdIwVomZULKZ4WkYxQk8ygln+SOyedS7J2c66rNAAwKNmdReoeOeR2YJsdd0ZMsGdbFg==", "signatures": [{"sig": "MEYCIQDB7/Qlqiwo71mlGlygXmq2dVDT1NibRKGwQiAbJ7Wd4AIhANx1q1DsN0E9P+bVtxELPMq3Pay0RBI+PLlgcxXPET58", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.13.2": {"name": "nodemon", "version": "1.13.2", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "@remy/pstree": "^1.0.1", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "4f772d8e68ecbd9792d5af39f0dad6f708e46ef1", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.13.2.tgz", "integrity": "sha512-D0+/FlyQZaYW19PK2QN+GHw5W8EZj+oZoYS1vBXv5/HJ5rqVVsg91qtOmWWHKWn81yQ++GpBDjyVlcfUv/3XaQ==", "signatures": [{"sig": "MEUCIHLDCqlWsxdBkGgbhhwQsq9bIet2+8I8llZfWeWJS05SAiEAxMnJ1Et8Hu6qXDRedW3tV0T8yf7EyOXKwcsuyxifBBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.13.3": {"name": "nodemon", "version": "1.13.3", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "es6-promise": "^3.3.1", "@remy/pstree": "^1.1.0", "lodash.defaults": "^3.1.2", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "92d23e6b91dca351215a4b8a50d2bd838cd0f9e3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.13.3.tgz", "integrity": "sha512-JTDI4il69J3rQ5aXQdNcDZDfk8JXC2c5Uh9zjTtFGrlP72UDoOEPB7reDMjo27tbLhtxtPAfGwhs28RVMz5rHQ==", "signatures": [{"sig": "MEYCIQCZsccHCkERAyLEkfC0uVnlEnQOq9f9c2JxRvZydLx7rgIhAJx+anja2G3eAUXLHENJOXDQWb0oQckakAXP5NMvQFCo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.0": {"name": "nodemon", "version": "1.14.0", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "fac9efaf91023fed4ef50e3d4d36ca085a1dd9d5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.0.tgz", "integrity": "sha512-uTFv64Y4/9lFXZZUfSyPJhOAeFC61kVGykL7YKEJTOw1R4EMkAqKpkadDdzgLW+D4BNJIlVnx0fDGx9w0ATvqQ==", "signatures": [{"sig": "MEYCIQDONCLnwZ/JAxqzGR9y0q1qxwawTPUzP8gfiJFJ9UGNdgIhAKD7/2/vU1YFFIQ0kOl0JOYAFzASGC5UdzJ1hrDWW9Q5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.1": {"name": "nodemon", "version": "1.14.1", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f43447d99cfacc586316b38b456a3ffb4537914f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.1.tgz", "integrity": "sha512-PtcTmgr+bF4ikUrUtuxK/ODc7Qrufb2z2zrOd4Qw9pt7dwNUSgiYxN3dlLbQ/wb0b+dx+3GnnqEjmcUya3i7TA==", "signatures": [{"sig": "MEYCIQCbPvANce8XAQ6V+QWdcky/gMT1FeIupUBm6Vxj0oyh0wIhAOlyEfXCn1IUrroc5+o3HPs7oYgFTuWFGq9TTPLqOPMu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.2": {"name": "nodemon", "version": "1.14.2", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "3e17ee110ce917c523a0065d5ac166f4135c5a06", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.2.tgz", "integrity": "sha512-Fzv68HCGvuTMrMSBGQO33Eca6h8JwsDlLOK0vRFAQOPuKKWRsPePLypGQnDnwoBWJ3i8+BwisEHEBdlHz7vzAg==", "signatures": [{"sig": "MEUCIQCC1TeHAvaATj5tbh/ZJPNld0znDV5SX2H8gZuA1Q2MsAIgQRLEt9DTw3BEENWAULFkP6wh0pyj4ddXOVy5X3d/o9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.3": {"name": "nodemon", "version": "1.14.3", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f08d66726fb9876d76956b57cc91624793de4dbb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.3.tgz", "integrity": "sha512-fmcF3Y0IB8fbCBXAhS+D7lBOnk4o334gxt/ndajLzXwiQU+LCd8Qf9h97n1w1uXej+6EdRuRJXYA7tbzTKxYog==", "signatures": [{"sig": "MEUCIDd8greDd7tCxVPhS/AKD3vwI6HIE3sfcR5sVH4V9/CUAiEAmh7UZh0RM6Pv0cPvchVvp/Mt1xg7H1rpFepYeSE9b6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.4": {"name": "nodemon", "version": "1.14.4", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f075c1bd501d2c270674363f984da494fc4f402c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.4.tgz", "integrity": "sha512-G5t6OfHKkeruVQ+IU2XiRvcXKVy9i1ZYlQ67KlScOpWLAu8CHk0CKjQ7HGckbYcD4/ovpCunaOTfOEZZUJICyw==", "signatures": [{"sig": "MEYCIQD6hX/u1e5OHIAYjyDJYnCQcstu4TrzQWIto8YPatjXgwIhAPROYXenKOu/oNt5dYp7RDuvt1RaWymdnq0CTXO3MwRt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.5": {"name": "nodemon", "version": "1.14.5", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "0fc1ad9f325f7923d71c3f0ab0da1c6ba46822e5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.5.tgz", "integrity": "sha512-dGsgKIXkX5t287YjUQ0l+54dgn4LXYb1lO+Oz0senmLGDPIRwKCcg3U/USMRDkr14qIgeMCeEDsw6XsZaerDuw==", "signatures": [{"sig": "MEQCIF3MfrArXQYUkAppTWm4/wx9hlOm6rH6xnLb5LLnQ4TCAiBXO0Y0VZ9ZNj7LABDsQJPdfSCjovLtS2oIkFIcN+PbOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.6": {"name": "nodemon", "version": "1.14.6", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d1b2fa204137b8d2c9242d797cd0b394d05fe77d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.6.tgz", "integrity": "sha512-jwF0CXtG3xO+ZP5Hq+O1Ry1ZxB6boB5SUY1hvsbwOAQ0gG+or2hawqGURYkrxqO/pN4mcp1fUvO69haxG6Mfjw==", "signatures": [{"sig": "MEYCIQChvfbASfCWsN+4MW2ifUewfdc5O05TptPnNwtU3XVbewIhAMpresfexI7n7DC6wj6oxCVgKs9r2DKFV1CVhWVIN65N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.7": {"name": "nodemon", "version": "1.14.7", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "ef544baa64603155c2ee6dcfba9204a2cd46d355", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.7.tgz", "integrity": "sha512-uEguLNr+QIk4TVd8swNvw7kHqOE/sjvNsIwhnc8CM7QdI+ezFvvkMRtCpCJ+DEVyIopLSTu2eayZ/ELKtswcbg==", "signatures": [{"sig": "MEQCIGYA56OJYPE7wtiln2bOczdmDXRQbqZuv2mcg1308oQSAiADZEaK7alMNW36rWk1QJ0XuFQyFWJD6hVx0qF2UCJ10Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.8-alpha.1": {"name": "nodemon", "version": "1.14.8-alpha.1", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "87f03745765816cb16abee11d8da2db98d70cfbf", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.8-alpha.1.tgz", "integrity": "sha512-XBQlZ93pwAQKsbd3YRiq+UIYfvd89Brbs/b7EyYHhmkqtajkJjU+WzmLT0cVoBoMLBkhCK+hOn11BtX8wQdn6w==", "signatures": [{"sig": "MEUCIHYuTSQuXDx6SC8k4P9C+aXzAE8+uYz76cehU58Ttw0rAiEA7t+FhBrEguyQ+3k4s2bsMQWvlr0liCNf9VFNZvEmruI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.8": {"name": "nodemon", "version": "1.14.8", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "0cc906035e2d4bf771d5f40fb5d8e37fb3e673e7", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.8.tgz", "integrity": "sha512-OSqzqzEOKUnSvO9Q7n7S+eM/XWw0xP9Zx2VyEZMVuzaXXIn4W9l+k+/JrDvzY9hXo8qNp1AfLSmxdM6iP1bRPw==", "signatures": [{"sig": "MEUCIQDEvxoJMGdMCo6NC5gAMu6HUIdV/9Ddt9zx7KbMy+SYgwIgFZqLf4VCu9rPpuoc6gWauiuoMxM01rBWgPiqMtOmiV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.9-alpha.1": {"name": "nodemon", "version": "1.14.9-alpha.1", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "67256467920f8f6e208ab729b8ac9925851cb4a4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.9-alpha.1.tgz", "integrity": "sha512-GUxNYoEPV9Ya3fWjQepPPvu+vVMnsuWeadAEraLL08qMZ2Hwx/vawfezhKOQ1S9zD8F9t7rY1dbJOhPeUTnfQw==", "signatures": [{"sig": "MEUCIQDXhaumZ1dgY/kUhiy2WLCkeMBbnCA73Lfu97Jixv9VDQIgOO1lbQz68XQBgUSs1w4UUoqfL3woxniTFt/JsvIH4q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.9": {"name": "nodemon", "version": "1.14.9", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "eec45a916db61503264037b4233c0f8a15d69802", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.9.tgz", "integrity": "sha512-PdavUHQrTPREoeGDMvMxfAkL8Y4ENkxTPZsBKJUPpitQbiQ4eswqKe1M0CbDfx1NVti4NQjtXUfndrL6NQ4R+Q==", "signatures": [{"sig": "MEUCIQDFJJPuPvAY0XNsQpvoadyih6s4J+0Gw2q/hLe3/KoUUwIgZdTahfpjzKdBos9iGfzPwhd+HY0ij61VvcUbA1BmM/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.4.10-alpha.1": {"name": "nodemon", "version": "1.4.10-alpha.1", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "86bdf0954d95598cfb766d015579b446c4befe8b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.4.10-alpha.1.tgz", "integrity": "sha512-+qcKrGU7AaUkOXu+kM5BaOWSdqa9noXcDIewDwM/qq6sxcKZOQ7msrasDxakL64Xrojgftk5rfcLmHIZApzamA==", "signatures": [{"sig": "MEYCIQDvo/bW3T3egILeLDmWDDno90/CvD+ezHTegXAkI7Gj2QIhAKpfogpJVgUnFwhsdm5XkqG3pQjfdJUJsF6ddA/IpD//", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.4.10-alpha.2": {"name": "nodemon", "version": "1.4.10-alpha.2", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b625024ced76042dcd53f1270cee2b54c709bf5f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.4.10-alpha.2.tgz", "integrity": "sha512-EBpR9uzk4wojDYZ56Y5jt0cDCqwpkHjluh3J/tOthOIeGsyajOvqGmN7qnWS1yjsHlvgPZsr2kFeUoIQ+4jhiA==", "signatures": [{"sig": "MEUCIBUSxNAXcFJUpqpGmgR0z3PnYLSiwDCTSSvQy0LysO6kAiEA2pEnX5X+jNcjNQybN4vISGMtdfleofFkGcCDmSMK9mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.4.10-alpha.3": {"name": "nodemon", "version": "1.4.10-alpha.3", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "32e68e955492aa4d7b358f1845984788c756f309", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.4.10-alpha.3.tgz", "integrity": "sha512-JxyFCuKjpH4/anLJb32tx7XS91WyjF4nP/wnHbvSjbJU64sq3Qlxlu2bye7+uZHdP2RqWrFbwW0fAuWBJVASVQ==", "signatures": [{"sig": "MEUCIEv3uGdfT8jbQlWNHR5TRoOBjfLCoPID+CnrfEoMDH8ZAiEA+AGq+omX1wdCfPoSgoNNY/E8BJQZvlJJYZ+4xr/qumc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.10": {"name": "nodemon", "version": "1.14.10", "dependencies": {"debug": "^2.6.8", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^1.7.0", "minimatch": "^3.0.4", "undefsafe": "0.0.3", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "836d33ff0f89ad111b8d49e5e3b3cb872e36304e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.10.tgz", "integrity": "sha512-lXM3pCOfR3MdUVvCQAh/E4pu6VLNQcRsdyxWzH0hxMJZP22ZyvMow1KqkUpNdNfqrYffq5ECtO/GT/tOdaUlBQ==", "signatures": [{"sig": "MEYCIQCOgld7OKZW7wpw97RcKRcEvrIrLFxRZhS7BL1TblEHcgIhAKaZQfVqAGeCPxRS5lv5FcxMUw1TiFJpduRPg8NCHIUN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.11": {"name": "nodemon", "version": "1.14.11", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.0", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "cc0009dd8d82f126f3aba50ace7e753827a8cebc", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.11.tgz", "integrity": "sha512-323uPopdzYcyDR2Ze1UOLF9zocwoQEyGPiKaLm/Y8Mbfjylt/YueAJUVHqox+vgG8TqZqZApcHv5lmUvrn/KQw==", "signatures": [{"sig": "MEQCIGab4SZY9uyRh/Ymf49J+LZbsG/ND40p8qRG/iL1r2RHAiA7epWMFXm32/GgmzVqSnhwGVnM5va8B4rc0d/yeO92kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.14.12": {"name": "nodemon", "version": "1.14.12", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.0", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "fe059424b15ebdb107696287a558d9cf53a63999", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.14.12.tgz", "integrity": "sha512-FssRGtEmt+EwpztWwTiYrLo+jSpgoxuJbmtgbRdTo2436x/3Z4PFODUz8yD52BiWqbHVJtasKv5K2ozFwNaqxA==", "signatures": [{"sig": "MEUCIEeGheAggkNalETE0PvDCCAj5T5AzJuIx3uYU5xscul1AiEAvkAo+/Ttx5wtJznbS7DvcZFRq7KFTlUFpUETqJeaxjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.15.0": {"name": "nodemon", "version": "1.15.0", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "ddec01eeb9c33d53dfbf7eddb2fa32f723474c1e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.15.0.tgz", "fileCount": 60, "integrity": "sha512-0cXNs7jnvQJuxASvFCzrsqwpig1SQDqeDsjddlQLCU6h1KQiF6QBOX99tPs1YdMLXUMZkRuBSioPhe/zCOR75A==", "signatures": [{"sig": "MEYCIQCUzvmCaFYtiZlr4nlgUeySmjTxZ5dF5K/FD1+fUtZ2HQIhALxsy8XKdAwFb5j5a5L/gw4br565nLWv3ylNA9sVY1Cc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177272}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.15.1": {"name": "nodemon", "version": "1.15.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "54daa72443d8d5a548f130866b92e65cded0ed58", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.15.1.tgz", "fileCount": 60, "integrity": "sha512-zWNke/X74j3ljDRCXnhURTLJaCxew31ax4azoPQyRYAoUBqNIlZGaNpMcyi/A/ylkGKWFiUzf7HHFyA/cveTcQ==", "signatures": [{"sig": "MEYCIQCSy1pgA1+5EYf3HMI+juVYNALKNNl8xsq+vjd3ePhQ+AIhAIRnQV3SyJUFodNO3XDO+KGQpZSkWP/BfgNA6K1Hjbf4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177484}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.15.2-alpha.1": {"name": "nodemon", "version": "1.15.2-alpha.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f4a4dad53b4a4944871e9c71b2b69a4e07d8ef49", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.15.2-alpha.1.tgz", "fileCount": 156, "integrity": "sha512-AT9OeNs2meZbY6Qsq3617kJRCoHF3HrSx5Lwo/U4DQa495VO+Llkwcqa3y5Z6B1aVxBFTA2Sfk4B73GKxXSzcg==", "signatures": [{"sig": "MEUCIQDB/HH64s/ya438aTdqG+ddBybs2kA/qwaBcYvrkVqtmwIgMWo5icpG8Aq+Bfwgk1kquu67Fr387Sk5duxoxQ28G28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223687}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.15.2-alpha.2": {"name": "nodemon", "version": "1.15.2-alpha.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "9b31222aa46084c7197a4f3daadb0aa310e3aed6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.15.2-alpha.2.tgz", "fileCount": 163, "integrity": "sha512-v+Xg+YVyDyTUJCUp4cuIKqQWuYcgxYkRj2hwSfpgEQNx24AGexRLD/CFgj81GLpbEGsZCD3oOwyoCgHQrPoYFQ==", "signatures": [{"sig": "MEUCIQCgGMc/MDie17X6fWkIDUV87x86D8BMmsEKbKMDgyIuxwIgTa1OMC8kvJ20F98JiJ0zyJF6/mvazxwoIpC7mNe1xLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225758}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.16.0": {"name": "nodemon", "version": "1.16.0", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "5156262954ad9d868f8fbe9766b060fe18f74cf2", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.16.0.tgz", "fileCount": 60, "integrity": "sha512-2bFVSLL/sJnoyxLi4bDwaCAb9R7xwIv1NGQn8i6HH1h2ol0k6Q/K1dGuMF5VTxnLoEzGTJx3M8aIP04glo3q6w==", "signatures": [{"sig": "MEUCIQCmHRZ6me3+8SU/YQiUqDiTpLklDbLs0cmLMy0VKWqMYgIgF9a1I8y4siQDSHY/fA3m8tzhGzkhBtUzJXtTJ4Cw+xA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177982}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.16.1": {"name": "nodemon", "version": "1.16.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.4.1", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.1", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "371961c04cfd13f76631e689508e0f2a8f108cb2", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.16.1.tgz", "fileCount": 60, "integrity": "sha512-VsPGAXcW9vom1ROZuHlWOZ3ND8bnuUVz8DF1KWSbUUXAANlunl9+a7XOOwi3TgIOkUQ1e7YgzdNX/xlGJZmiTQ==", "signatures": [{"sig": "MEYCIQDKCO4psGqnBqknDdtzzBqYYotPi8eQarCyTVQDbeKfEAIhAKnW4t8fVTs9Kc1jtlVkL1wN/AtIwVIbB/7MG+mWIcrD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178370}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.0": {"name": "nodemon", "version": "1.17.0", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "54351bb480857550114584f3a1dcdae7cd963821", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.0.tgz", "fileCount": 60, "integrity": "sha512-nD+eXVagfQhwcGbc/gxbr4pc8Uvj8vXDeyLEhyOb1X7wWAz4L526tjpFdbCNCEAaWsR4+JMUOlCu8FxVI8QKeA==", "signatures": [{"sig": "MEUCIQDpbgECw0o3KsNTid4qJe0gt52BIwVB/e3TvhSVIp3xFwIgISJe0KrDTc/59lA8bjkDG1noJmI8CxFQZLGPQWk/ck4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178845}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.1": {"name": "nodemon", "version": "1.17.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "cdb4bc53d7a86d6162143a1a44d7adf927d8652f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.1.tgz", "fileCount": 60, "integrity": "sha512-p6+IG/eJyprjRd95na0efIWl3ONITHydCdl7btEIZttvnmXX4aqxIE45Aj+zeT0llJIUfJnZNyAX14XIdjJtmw==", "signatures": [{"sig": "MEQCIBL1VKwaaRF5GDDHMGT2C+pGbsLmkoKbaJ1DVjLQrhQnAiBSP4ShvFyseudXzIiWUyGGIzw0q582j1OAsj+uI+UbHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178841}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.2": {"name": "nodemon", "version": "1.17.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "17c0062629610e03dd01241c576f1c4068da9fdd", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.2.tgz", "fileCount": 60, "integrity": "sha512-v1fVfgaZanBHP/ZOc9V72uKKIF4dcRfZV7GISNVi/w/g5pwB7nIvOK+RGULjrzhs97cwUX41cM4+dlw+bg2igw==", "signatures": [{"sig": "MEUCIDRw+gl2QJT9QoblD1/2meAARU3brahb6Wta4sMdKt/cAiEApMGdNph+ZEXUZgbZXe60vbVUHfm72BLZ8jhzKgUvSt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179222}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.3": {"name": "nodemon", "version": "1.17.3", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "3b0bbc2ee05ccb43b1aef15ba05c63c7bc9b8530", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.3.tgz", "fileCount": 60, "integrity": "sha512-8AtS+wA5u6qoE12LONjqOzUzxAI5ObzSw6U5LgqpaO/0y6wwId4l5dN0ZulYyYdpLZD1MbkBp7GjG1hqaoRqYg==", "signatures": [{"sig": "MEUCIDSgR+IQXsnTHz+4a4BhxLtvgYGc05YsxW6aNUHd538pAiEA5GCx4n8v0vQA5nXb1qA/kdgSj1N+Fl/n5Szk/BlVyJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179209}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.4-alpha.1": {"name": "nodemon", "version": "1.17.4-alpha.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d6c6ef2978aebe2356cda90b5c4b7c27440b5815", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.4-alpha.1.tgz", "fileCount": 10220, "integrity": "sha512-zh0OjcIPL6ndusfilre146vY9bW/4YvFBesqoyui05v4XQoUQFqvhH42h9nVtO/bEDZHcQKVczde056zAzCPWw==", "signatures": [{"sig": "MEUCIQDANuu/OqSOw9ho9FB4x3NeCgyCI8zClCt4LCWHvaXrhQIgDxuDpeVdgbz6rwn/ei3uv3iNN7hUefeVFz34En17VQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1146153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7gC7CRA9TVsSAnZWagAAEM4QAIw7T7d/mU6+lrS7wEDl\nFjdGkQPS5M9d3JnvbBb1J+mOVXCAJ3LyBzU6oPiaUvguvt2eTv+s2lwt4N+q\n2RDjusl1UF27FzY0maPWEqdJESIP0jp3EXMLhnXicghv+27ZDU8y63qvb04E\nT4UQLODYGc/mrdouR9Pct89doodW3WOoZqIxeRi2ePs+JBRoq7RoegaKvw5u\nF28IYB5DU9W+LKAokyGKdupL8MgAKRVNryz8I3k4HABr+XVBZnCKMITqHj5u\nmCU2czLhu7GpyX5pR0b40kjJSBKn0kwgt3etymSOotd32eWTcNN6rqsPpeul\nr+EvoxqxTlRxdXrdm1UTwzm5Sr4nNP9i0rcbtpk0NG3Z2vdZUKG+Vrc06hge\nPiaFd8g/rFGTzOva1jYu2jwNl9Q3/UHRk+GUE+iAqCdbFspQ7VpKvC44KzYH\ntk3f3IYDg0GkmC2BRw38sc0LBQmh9PlcYD7hhEmfqJ9BJuUurtcW/k3Mt0+2\n82EkmvfID38RLzv5Im/d6TGVWc90/ybo8FasCif/V0o38cOKnts4Db23gsKZ\nxW4YykBkwHQguJQ/avcOe4zUz//2B79Cf/Qrws0uTJsBHVWnsxa/VVq+yh0L\nwI9SPcoOOivhDktbU2zj/v5K1+86z6l85IVFvNMmq1Ux9wyvHCIH74PSm0Xn\ns1gv\r\n=025Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.4-alpha.2": {"name": "nodemon", "version": "1.17.4-alpha.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b51b43918eae75a3d9db22a21cdc4177f98e156b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.4-alpha.2.tgz", "fileCount": 43, "integrity": "sha512-q+BCBRSK64xOJrg7o9jXH6M7rPohQTI824QVIoZQQLT1SdXDnu3Yh9qsQwJQiOLOzLvpjv/Q1T5J3VwVhcq5nw==", "signatures": [{"sig": "MEYCIQC6BdQrvotQpQ4YtQB7Wo0QfazcRtLjwpEwtTC7SIFgsAIhAJYZHhTRbBzZD324AnRzF8ohwObqY6zbQE5alKOm6tJE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7gGCCRA9TVsSAnZWagAArbMP/RvPwH1jd3/t+SGtiK4P\ndVoa/XuaKLgyyCZTvfndTQW/Vm8hsgYvGDA9e4gGyYEwqBFArAeWwiPGVyMr\nN66klZ9MBKwF2IoxQwJKxSoYpuucY4CNSfPu5r64OxOWa3mhCj37E59MhRyr\npth8FVAiFBhMKdeHLUs2z38g34RQTgBt1GXIs7HUkwW1PzOxbaWXMz7JUTxt\nEWBb0A+peAavc4Zs5mLGIag1UPwvqszFmaBUxPZqOd7odoxve5QECn9pjozE\nEdz8HgjjWwr4Ivd+bynmCn8s9dyU27Q8uq0Nd/c1b4QeSwC/C4T/RfLMI1gz\n3S++KyCUYbn+DpfZtEBQxP+Cull4wJSIiJUs/2qPfFRzpn9ud4Bgm1oOmBcp\nqk2UHHufrt94KC8Y/94Xhz9oERm2qydEyC41RQ9aDqV8A6zdx0EYS0byE4Bm\n1frY45xea8WRK5bo6IJ5aYrMt/EMji66pC33TEoL60bK2P+YkrK10p4SVyR2\nHGYlh704HlglbHMy71OEx+d7rXXgaSo7IciqEXDRa40/HfuGmHo+kx2GsGnX\nMBQTvqlihtxnan6vmucFJEX5dlQoQaC89Lwd/I92qY0IV7KvwGB54LGi+5eF\ne9AT5E3DkX7pwcDDYxXplgnYpsmd6bR1ateZgFWC7TWIjLNSb24Qx+SJ0Hvp\nmH5Y\r\n=dX6r\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.4": {"name": "nodemon", "version": "1.17.4", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "243ff9c69ffbf1175f2460f9b023f35a072c15e9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.4.tgz", "fileCount": 42, "integrity": "sha512-ZQcvYd8I4sWhbLoqImIiCPBTaHcq3YRRQXYwePchFK3Zk5+LT8HYQR4urf1UkSDiY/gSxjq6ao34RxJp7rRe1w==", "signatures": [{"sig": "MEQCIFZ85XQlpq8HqFetBuQuh2lIrcJRMhi/HlPcJLl4AIVZAiAGP1gFFS3V2fbFoW7gg1lcLGP4AOhPRuGcOgcVkX+8yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7gTECRA9TVsSAnZWagAAhx0P/29M0QFZX350CdLMGYgF\nYv54M95Pc9Wbqw7jP4UR276AbY25PXIqEGpOG2mr/xGTI2KxpxxRJOyEGpM6\nVNaS4TGPrNueggtbVci5G8/etwgVcDWcS0oY15/9Kuur5iMYvvGjpouGKeK+\ndYe6iYWNfj/7IHuvFhVfClZ5osVxgYuMdux5xRPktnis/wusy+3XDYvsAqfT\nGnRcB0xdzlbxNJwAQYMwO08NKty4LT9PfqGAu/fnQOkwfVnMuzNOWk65pBhO\n7OPGdKHnWFgXpSxnwl/UgqsRaRDY7E0oljXMu91uoj4rUqo0Iobcr0vb38+q\npGLK+kRRIZwF2+lXvnd2d751s+44cxAHMpkPn8Sz0Qly/JK7DGbpp1oUschW\nY+/qcFXfBO6/kr9Lcv9HtOJDtgWJX09Reo4ytjN75BUGMDSkRhkXEKPdRVvC\nASMaVRnlHkDLM/8CXiy3ZDNoCI2jdDKOhQZ3DaPKczsBpTX8r3WVToAtTCgr\nuH1cFPLbwrpmVnMEeeLzgOisIPIilvqfCTBklcPCVTnPilYI8LFWFRQ/OJc4\n6I67LTl4IS3RAP5jjuQsX3m6T9PQz8U6QgPY2OtfuEXv0oKp3CH5HvZ7nNrb\nOZeTO//S5W5Ng+/V13If4kgPaGl/3Rj5lkzUJ5xpYnrFomJq2W9YsF1qAohR\nasWT\r\n=wjv2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.5-alpha.1": {"name": "nodemon", "version": "1.17.5-alpha.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "2ee679d63929506067b932bf6131fffe919a18ab", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.5-alpha.1.tgz", "fileCount": 43, "integrity": "sha512-1vDgIW/3mKQk5DrCfYXa1297nqJg/7ixG38mIThv/gXFXDdmt0YbgfkDi2/GEmvvYIT4k/x8ozLxqmTqVnPXWA==", "signatures": [{"sig": "MEQCICVXcug4OipM+QHVBCL75qw9LK8vftRn725YwrlAApO3AiBLJywzpJsdYPMe5aeN3yjufT+K9R0U0biavoW8naQ0Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8AbyCRA9TVsSAnZWagAAcSwP/1qy4Q8aNz1wXORVgJXm\nUWRDC7xGkCxP8rJwJx7CHF+UbSG3Fe7KOFxYd76VxWkRUw5oVFptHD6kX/3k\n5g9I0TbzZnfMGew/Yq440C6rhlFY2BfaPTRyDZiv4gnbhqOCnB2YqPMV+9ng\nXrFABvScOjHqLzHfUL9WnhOhQ/FXpDr1f8m1ObGR+w5ty499rtm6AfM1fItL\nft0n1Qe6rev05qjD56DC4zYxrrIGqS0aMdpr+feuOx7bj+nTSlevvX82liwG\nNh6lbu0HdSQS0M0aFheoEA9J+S6AlQCmjRwmRmypXDZEqMpeRYXotDkP3X3n\nTZ9XzeA0ULJcaDpdItNJ+xXQ3WgHAJ0EUMpO4oYyOfVza/W2QR4M+dgLP5d1\nvsa+67Ap1H0Py3GlnZnMYZU3cX8gggKnqrlY53a8Y9U7LU4I3tZROirxaOI7\nze6HDZAs7o2XqYDOxW3f/FMrjogLKXDEN5+/I9k+mkJ7rz9aLYJ82QIb1zdm\nrIBlM6hVk/KgFyyZ8Prd3EHgbUIMUBVwm6u40Bln8U9US8t+pfhPfeTDoHyD\nIyJ2KNvJ5mqNmhGr/ofGj5fdk8A/C3oV4OJ+a9Ac9wsm0WisLk+fHk9Uz15L\nUdPiu3+0QZdh6QFE4iZLV4VQ5GLMERIDTFy2ZaD7fg8aZKeoKWWqFRA4zPI9\n4V3W\r\n=n1Bq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.5-alpha.2": {"name": "nodemon", "version": "1.17.5-alpha.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f04a2abbd9119694ef5bd65c7973d313e63f28dd", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.5-alpha.2.tgz", "fileCount": 43, "integrity": "sha512-uNixIdOcBTllNICfbp/8y+Vwsloa/i/DwizNTYOgJBxJGaS03RSWD7/oZbxCcwUW3k9Cpijp4Va+LrX3ISCPXg==", "signatures": [{"sig": "MEUCIQDMhRym8AGmyIRttXKhJ09OvZ46avly1idSLvNaneFfTgIgNW4Lfrxzgl70HpsMNC7xhMt4Tbb2PqaFMg0eLOSPSTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8gdOCRA9TVsSAnZWagAALYwP/joKTiA+UJMSYKw9RG0b\nq/efQlEh9P7hsbN44mS3vmiCd0wH3TUqw5OHXYrdICB3CLq2qeCGX6ZrtRn2\nnvlfSJhTdhslaUSp2yvqhTiPWuFDrGLMZ9hPnfT4KxEq5Csk4SZRz8VZ3l+s\nxTHSSu1Hz6tHFSsTqFuxISiE2pWFpfHRc3yFRtQl912v0j/UNnhDRFHEBE19\n2Qh7h5YP1Ste6vNWR98Br8VCWeUkfCCdqS0H8PDJRuzVZUdLOHTzjIfyPtsR\nQ7SjdUFiAfcGdLkv8/jUfkI+Swq88KsYZgYHKXjuU+h8LLJH1443sVfm0H0D\nizjY72TlIPAtQrhqUx2VoXY3b6c+eW7/ZTVt5XkMjzUoCSvc3PufQ7vHxoS+\nGOdzhQ+0h+1H3PaQ0H6MLjhqf49tGV02tCVEhq3bUiVoVdZ0/huGeVpUJhiM\nX+gEdj/Vs8ClvhIQZdKpqo5whPWjy39IiNuN9IhU8OuLGKmJqoZzFyKY/l2m\n9de1mgM0554XKVwd2NVjpv3eGEM99eeswX9eHv+tdizBisqysLUgBwv/XIRY\nddRLP+vVorBJalVMeT1VHNYIXee8V0102Yv4auAP9HfPun35m+nZWrfczLqe\nvARxk8xRJysuAObJYVRtKjEfoWinkQtiBfpGH8QAI35hCKCYJ32YHQrse0P0\nKTsr\r\n=s6hy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.17.5": {"name": "nodemon", "version": "1.17.5", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "e6a665c872fdf09d48bf2a81f3e85f8cfb39322a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.17.5.tgz", "fileCount": 42, "integrity": "sha512-FG2mWJU1Y58a9ktgMJ/RZpsiPz3b7ge77t/okZHEa4NbrlXGKZ8s1A6Q+C7+JPXohAfcPALRwvxcAn8S874pmw==", "signatures": [{"sig": "MEQCIB0XnGxFis0iE9nteDBGndo2OoYuqJIYPILBJfR51D5rAiA3ToYid1XBkNCarF27e8mf2dmaS7dadewF2fEZ94KI3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBRYNCRA9TVsSAnZWagAAG4gP/jxQdcuN50v8oDo830sp\nIh2qe6sYCwURWJBbQ+Or2VFsg16Su8DCz50mDmDKVV9Nb8Png5ZIGJnbStb0\n1tL/VCbiHuV9ioWIb1OrLJPAgdDAJOiXcHh9W6R98LlAUDV4NW/jGP6CrDtk\njMs1KyCaJNDoIQgRQaTq+qeAtl2T/eVG9qcZxnglXs2csP8xs/v6qgYlXM5r\nBb64TFxTycU7E8fAjbxZpzDAV8lyiiB9MM1ul/FbNkRZvTvtjXtHAJmnDAsJ\n6ojWQOby3z4JLBA3F/0XRYbOe4sWEz+FjZ7mOTtk+DYffNUkY75zdTwYI/RX\ngSpaQ9F6wExblwuCg/2FUlVeE0ifKQBMYVTjWFshUbqav1ymrsSUxKP7FUOl\nrkGCVzhkf+9mFBZyD7QTJdkPYrXZapkmLkjQ8ZYfDR083R7EpejoaksQxLw+\nLnQPai0dvrLKraMODvOArcA87xAW88nZVHxXw4PZ477cM2ZLTceABM0KVN3J\nH48zsn2foRSGwyUGBAiArkMiCD4XcVls9IWWg+cnssXKpDPKTEtVT50X7BqP\npdYwKhs3DBLNxEyo7ITViXOXEylr64bgBpqt4CJf5CN0DmjulXW4wzkgvmpn\nlaZIj5dSkRlCSGqAdKzCUuEg4MhGjl2O8p2Q8TucDq9bM4PT0tUE5Bd/qrgz\n8Ea5\r\n=Es5b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.0": {"name": "nodemon", "version": "1.18.0", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "44b75d5f19065c47a262d4ab21990da3b6f8feae", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.0.tgz", "fileCount": 42, "integrity": "sha512-yfZzAhDwR4CUJ9EEqfykKY+KF+OR7n5Kw6oIB5lTjY+aJs1eiVJg5sHku5ID3Wvf3EXEjHY8FrNoXwDnZfkkaw==", "signatures": [{"sig": "MEYCIQDs83ofe39W1+aSAxz6TabpdbKlf9UD035N/MlFNETdvgIhAItPiwdEfhCaUnmM7BCzHKBo+VUTvMUSGXh9zrO8zCIM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRPDcCRA9TVsSAnZWagAAy5IQAJAheoSPwo0+TtT+8Up4\ni+vPL03OU5v+vtKdv9EAUMZA895D8gXvVmxT77O6arnxW5LhX6EGvZ2PvJJH\nxfiyoMAdmzSSuyGjUiUsIzhpYXkN4lU5IlyweN0JFCs2cclUaYkOy7o/SfwR\nO8iZTaqMSZwDz1xvbcaV5ZJjV8Vxe396vdDIV3rwK7Y3unwJx6HMkaOCCajX\nbJhqiMwBhJqZgoH6KcrXV65jTU/faypzhLjPGnJoTpl8ed7J4fguIt3kcCUl\n7ny5aer0GXg8CCxxWcYsKl6lbEb5Fj7M2vC/6kv9W2a/uZbN2qFg+K7OAva/\nN0t+x0h7uPTz20XQePGsGgDLGaCJXSfHHWJV5RmbQaLO2qBTtsBMLNOG7cRX\nZzaEQePcUwyUElKhd0iKQxQDPT2iNRm9+556mD61acboBBmm0ncuvmoaOTw/\nH6a6G7IOsL4/mMx0RZ7rq7Nosb/i7O/JACjm4qtksvO6Iemks9IVp1/lb0Cl\nn+RJG3ZdayZRV19/yk0SzYwSKFaFyhZ8/ebwBdQy+614NoqgM5cjjrn0kkej\n8rYn2hWZD31Is4iWf4bQ9IDu7jr/TOxbO/bExNCFh4tSweTDiKYl+TNXoOVd\nmjvMbLNSpsw+jxQB3DXN8o82+oRZgWNN6dHCjcRhr4sQBgbuw9Kq4l8gNjd2\n73WP\r\n=FKD/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.1-alpha.1": {"name": "nodemon", "version": "1.18.1-alpha.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "43c2ea8364b5084a7f54b317cbf2ae4f38b92ada", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.1-alpha.1.tgz", "fileCount": 43, "integrity": "sha512-nuK9tanPgM1geUwEcL0cRrw+aQedWQqpYsPXnHDqiQ8ewbQUqvK69Y/6yxRfw5K6nClH9vhQDAhetfXl1FFYUw==", "signatures": [{"sig": "MEUCICqJQXw+fJOZFPm9kUBQxWugzatKyB5F+rnucVkCKOJaAiEA9uKSoUCpH56c3XSnSXMZLA6VxD6cqI6hsX0+iCcVBwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRRnsCRA9TVsSAnZWagAAIUYP/AmldTSN3TN9tVgcOu+9\nh1ISBJnzZkL4vsbS5lX6rsrrRvpGZ+V6UTRs0XlI+Cs/46aJ8icrREOs4ALM\n83MFcIlKiO97UJmYk/5MB9vfYAklh6CL0XHOCUOmP9yJAiy2eg1FR7jZ/bAN\nbdjhGGQnR40DXGd5wCuaNqYEVTKelX5kMsbrUpKDqTJENXg+MIIkDs2VX9bm\nGo6KHz/2iCT5hdaaP3zK4oqrjMWT5SezrUoj8Mbx8r5jPArnREfDIixHt8Rb\n8xx0QsBY0cMagSQQhCcg1sK7pcnWX2hf9PEwls+H7krPU3YoeomBLuPnDue7\nDRFbOZn9J9f9j4rqNxf/LREt+XPch+R7t4+aiBXomuxub8Z++qnR+fWiWq7N\nbkhG2JFBhBFnZqdoN5wdcFCTjIiYhZ/F7xRIgOr1iu2rIrxchIDVtT1NqWLU\nfJsrxs1waz+Ex5okaz8wp//+PJwTSQrurkhZ+X65OwutJW1pKZyr/bCPHute\nbUFGiZm1V5zoV3M1w7uf2XgrTJ6jH3BFjGha3yD3Yk2hA1fstERYHcVrip4d\nvLlTJY1RQXW6q06Lv2WZg53FeDhf2w/TRCkfrfS9h7KC5QT42sr7AuKoUkNr\nzzHSyNVu2wQQkMByTTU1evFTMJXzsM8jMFqfYleT3Yo1BJbuw1XFLlejNF8Y\nYKCK\r\n=gvlS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.1-alpha.2": {"name": "nodemon", "version": "1.18.1-alpha.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "51133e9a47103ecbb9bc4a679d4666509c353025", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.1-alpha.2.tgz", "fileCount": 43, "integrity": "sha512-QdfmB0eQG98+tEsiiQ30JjVQkXycWD/NBgKvZwaaxu5WzBjeWOv00/8hbgsVcMLVNlcN2omyNy8ZeLOGmrjIOw==", "signatures": [{"sig": "MEQCIG4Z8QSyhc0ImGKHvcWVT/2rwNHRsnqp+RAXnijGmKZFAiBDGV8r1dwDS6yyIpmz/jnMkY0P7kemRsckpgGC50Bukg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRaGlCRA9TVsSAnZWagAA0UgP/Ruu/YPFLlzIgBhPpIse\nkEz/abjXyQyvjlEmYHQUnqp+eNgsMniWN8rxqXkW5v1k+pnpkdgIV4KqeFoD\nUOsCAjXz/Yb77ETs+Fs/PqfLZV7reskIYWFxKKSjjSCkkfCHk44JS23qkz71\n4idqJgjRXsrpOszfhmGfmrV6FwcLqTH6hJ+nrWBoP23SjxPtnNAMRqhBr8r/\nrA+ZdDQh+TNT1dtQrJnmWoi77Zam3hDk5++bjmZ3VVZUJ4wzKzv7s88MpPta\n1tRIfs0Qmc0anpGj8mv7a4H1HpgrnR+GP76umL0YkyJP7E3g24h8kDHL0zJM\nmT6mBBgyLSwUeuWHHCV9vvde13lw5RxTHml51VCNn0+WhaQELHv/v90Oc8Zo\nF/zuMXb30coC5pCxICPnKSFANJeY/ZlWTyeyJO/kFmePqEHqnTTfWRY5S2BX\nnXjKOkH+/NRlB/lWalnM4bCR1HTZVoW4K6cOlHOgpZmsTR7AjviqRQni0ids\nKa4bQPjZNTBo0v8HkjvmID+IFTDzqZb9547hHYcFIICUmJJtBKZ9YGHy+Gko\nqJLNmHJuHxHiEvhdq2JJEuQ4v9cMaqTdkKI821LEsjAQV8rq64Z/rLm584YG\nQ+FL5W8KxbJQew9RfJnhDONVwh3LgYPlIJ0EZImAIb4Co6iXyV6ohrh9lJBS\neRB8\r\n=B7Jh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.1": {"name": "nodemon", "version": "1.18.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "873d2f7799662c549f7ea557db6934d5cbf665d6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.1.tgz", "fileCount": 42, "integrity": "sha512-zKKrRcn5htBkPz6N5SiFLG8kneJxhW//zO7/RB08TaQgBZrbmjUlORBy6ALIbpwwp+Q8F9mWbnbkJXh6rjlALA==", "signatures": [{"sig": "MEYCIQCSimIsVsWfYy52MGg6TKrZ6ufzsmZl3y3Q33GqL/GcrgIhAJ97kM6Sbj5CQIO2VlSEaHOtAwFOQgByZypfsYwKhxV2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRfA2CRA9TVsSAnZWagAA/G8QAItd2VdtBQtwBQtkGS2W\np4oKAYz3irIz4x7RX5jNY+J0zwWCBq4AXXpbcViK5mVXTOWa7UIJ1EgzaudR\nZZGd7NeYOzG1MayXUhSJPXbkYZXnYvhaUvVrDU4R/P+dieCRBZTjDiEO08Lc\nbixM0JZ04lc0iIWd6loYAMc3IM906kVbRE7Al1y5cje7x3NUW9t11ZK9xKUz\nwAirTN+yIDJQF0k3/JF2Vzl2U3GSULk6UfqI3Wr2bhmMaB4v1S+qdeeDuV9I\ny0aYtowRdlR4LeFoR8C06Gbtod/aGVB6+6QtkRIbflRdB5NtGS7n5CfgVY+D\ngz99bvqHk3/0EdMWiA6q32YUD1mjEEzZZZsiuz4n6nrTewXe8qDjCWFB9tKK\nflKjt8AWrp597zD1VwrMV+QflB6AlTsKQl/kQA55KMV9+oL9ahwRzDO7FuMO\ntfsQnzaOqXchsmq9HvSYrsr2HHTf0Pqfmm4sMioAEfRZrMYtyHa9tcQ37H+P\nPIU3ZMujyg8wH6OC7lbVU0SPFjmfwIEYnDB5QryeqWDSTXkHRBbD46ovOZCD\nBNz5AVrAW9ZiYdM5vBpO6vSvFCvjKRm6Y58EMT46XtHiIQQhju1t7qiFxWWZ\n9fN6fYFbesRTJnsCApLMg3IOIZpfJrU2QeLXg+9pefW90/KykcEbUO6AjX9K\nsAnn\r\n=fjGg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.2-alpha.1": {"name": "nodemon", "version": "1.18.2-alpha.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "f1a0a035812dbab35c611cef798e3caaf2d41757", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.2-alpha.1.tgz", "fileCount": 43, "integrity": "sha512-XyxuhjGheZHpuui4tuyM7JXXwtppdapzzRbz9EynDIU4t4AvMAvpMUXLP5dc0U3yn7FBjXWNbz+N2uZ8yLnSVA==", "signatures": [{"sig": "MEUCIHGFoIBfe8yLH321Q3O2MebBRoPBhiF2MTnhE4TtGePqAiEAlMjYgeg88dwmR+WIhvEIpkwwtdjkyKGfsblOW1jGGoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRvpaCRA9TVsSAnZWagAA1kMP/30wxHT6gX6ffEQXHmjx\nKVn6yA3k2n0W5zQkeARZLmpzdKUW8SEwMFQVnL+F1+S1jrU8L7/cW7gHtcE9\ni4EBaIfBsnWVQGnUFfQkpGWAJgBAHkalTykzV45j2Up07DoOfy8K56OjjAo8\nGAPr9bjRcvHP/jrz34mAPk+bO1C7QZFLrOLz2T8HbUQ+igX7FRgN0pxQkcNh\njFl8hK0CGGdjtfQvASQMNQ+B6pnSOVvmepTMDYYEv2huN14MVmHaZHljb9C9\no2Br41wTtHpL2IKfglOv6MP3oI9WUVI1OJiHyS6SmwetmOY7S4r3Szeib8KQ\n5+fgMVVNihij+T9FtFwNKQKK1QlbkecC6xd7RVt5jCj5q8edJnIQmErg26PD\niua10wxrsLOTt4n4c/DqHuYYrxeQrtMegyj19b7pBzllrF1thb26iB/VNyvO\ny+521ED93rqMp87RhzR9a766J2EII4giEpASxaMYiBFj8ftaCV62qAM1yQ/5\nIsUWkt5J1fRmIOKhPJLvwuYbv7CFZwNbzuqjrxgXIZcWxMe8yFf8DkbE3hmd\nzbPXWzQJlRBZ3itIIlaPnDCJX/UbAGNcuZKF+03/OcWoNZKxB2gA3Q2hWPJE\nOdtA7VIXApz1DhxXLG6yYBNhKSk/oS1kT0dPuAzRyGLeSYBFx633nKsApS4W\nZUEW\r\n=yCHW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.2": {"name": "nodemon", "version": "1.18.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "36b89c790da70c4f270e2cc0718723131bc04abb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.2.tgz", "fileCount": 42, "integrity": "sha512-FKuvzVurERMgX231T9KexWWWopjd93vapFY8rLn2JlPZ58uCW2s7U8utKElpGUEAqU5Y33///KFza5O9ndVRHQ==", "signatures": [{"sig": "MEYCIQCYQQ/cP3wklyQjFHFtk44uvN7U6V9dv43lNgd0fnvUdQIhANxp9JSpF8Qc8Bg6Id6TPNO8afHCA2C5gtLs1kmDrUwV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSP8VCRA9TVsSAnZWagAA2N8QAJ4tSlsFD7HH+SUQeAdP\nqonueOhvELQhDwudMx29WQHUjHXZtsRwKrpEEy0B4kGj7oBxA74/oJwQGfxG\ns3NuQ2aqL8vPKvIqzU6yt7iGqrvCjjWsab30ryGE9fRkkQQzze29yZYcUaF2\ndswjTBJ1S7pikyY1eHBKMMSE/Onf5CiM0qucw9dE64BjDdeGVsR5kUFdny+c\ncclGQY9gK5zxPV346Msz545IAYL49JD6G0BqujZQfFT3yZrQ4jHLwEyhzGLs\nn1FGMaxeKR8rDzCLe7nwU03pyQWJYKd2PH/4nuECLIy4sb+5VRIg01OnxWw3\n+LSwFxanhJGZ4i8rQowLmc8Uf/g+kvcu+DxKYnjvWECx7Gbrpp25T7CFoo4r\nzp2uOgmNLZzK7jdEeKKiSjLhN5WfLSnKIm4bdXLJfOlzzPZOg8D0beoncvFV\nfX3AfHFXKno2keCaVvWQ7R8AJ4T3WPhpvuYRh7uwLfyKElTONuPrXZmZDh+H\nT2pF5X4BGYfW+OtBMsuGSpZ54D1NxOx7y0WbPS0uBF+C9S595VFTdtQyN61e\n6Swmx+jAn0o7nlnejtvCOLVh+eUFCVX+hj+Vytj7P7JkdRJai1vH0GKHC1DD\n5mW1mCExKDbGYCGJU9B+NSc0+3gkYxAs3NDTMWEwdwwmHih+wKoGYk0FxEto\n3nFO\r\n=KiOR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.3": {"name": "nodemon", "version": "1.18.3", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "46e681ee0dd1b590562e03019b4c5df234f906f9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.3.tgz", "fileCount": 42, "integrity": "sha512-XdVfAjGlDKU2nqoGgycxTndkJ5fdwvWJ/tlMGk2vHxMZBrSPVh86OM6z7viAv8BBJWjMgeuYQBofzr6LUoi+7g==", "signatures": [{"sig": "MEYCIQClgmeYlSm+ChjMiTsuUQcEjqikYnTDHGcecB8XnxTxLgIhAJRdOGWFOUSEBLrN8wVwPpCapleCiiuktbcU8ueeTfMQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTjUbCRA9TVsSAnZWagAA1GAP/1pL1P+0rbnam7DdW836\nyzjYwtk4KrbCy08ONQVrIbHQKwiJdYv6ZAAycWul31lcfUZyx2bgfRyh1d96\nwl3GgQd+Y8mDnf1s8E6qb/KYraZOLjBxM64XbS5CgQ7GoZfqK0z/KvOW68XW\n1ZGIVenrhV33bRHkTamGJcAh7CqZYnQdO01Y7hwRMMjOJT+OYH7QkLbM7auG\ngcVERn/ku6qraScPGi5V+kYdxYJy6jSJBJWvpV47LdO+wGAQwWESJXMPzYhC\nl7CESfVGlsdH+f3o7ij30u0t+6+cgXcMA3qmK8LzBh2TUtD2T7exoBJunekG\nJkfduHB0VvDLWFed8UxaZluJTVzQtpKul6WiZ38mnRvkyOVpddXneScD4U77\n23Nth6WPcSZeXJy6ZkfUuzcTjkUS63wVT1riii5nfQsvdrtDBHg+SnlkhlSl\n+7M5pKsA28FPutTgpes9VosOblauZ0+ca+cebZOgIf5BiCHuSdLq3If7rGyu\nLoMERSBkQs1sz72+zW0isCNB2L5Jn1exMZ/IfrudtIl4LXUt/ZoCzvCnMZK5\nAeLpq6nrfBzxkc8WPnhU6dvsXy4xPn5qnPXO8whVVjBZVZ65PBK5gJKpxf5H\nxiHptyaMvKzOsumSCUxL7eic8e9/70iP4eDJRcOjgXdgEGeM5jMCxkvJ3XKO\nupmt\r\n=IrJT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.4": {"name": "nodemon", "version": "1.18.4", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "873f65fdb53220eb166180cf106b1354ac5d714d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.4.tgz", "fileCount": 42, "integrity": "sha512-hyK6vl65IPnky/ee+D3IWvVGgJa/m3No2/Xc/3wanS6Ce1MWjCzH6NnhPJ/vZM+6JFym16jtHx51lmCMB9HDtg==", "signatures": [{"sig": "MEUCIQCEFC/bog/ZcsCdOEwv598JL8JYM2Lueo20GpmG5Y7VJgIgKbiL2dR5/05BAX0X26jsaTAfqA66Fw/CbuxJQZleVvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbht0vCRA9TVsSAnZWagAA1Q4P/0QEEi4rhOdEs18GO1hx\n8nG75rfXoRaV0X2wzRog6pVCXydW0I6u4AFH1R7/dNax7y6UGl1zTn8tmYy/\nJjJbEKxhz5cUKf+5t7F6rjI30kvY1Co4cU4mu+YgS6IZZUtXTthlbZ3jD994\nhH/pB2Krt28c0leK9ea013emRw8vL0ebExt9LfbltPCZ0ZHm2WVeAFQ4Ef6Z\nxJm/2S/IJnI9BxNrSMAyOOZtV9kcRWkjUdrQxRWtXHsCGG3YqDHHMCovJFfV\nybisUt2cYUWRmbBVg0vqkpsTX28wpKIApw5NcL9SUy9Nw8148jb4eklxrnAF\ne2l+Sj7OXfzMvAzfX9deaDSDCDGT4WZdgzY0Zs4mbrP1H7d/YYJ1xRSGUN97\nCM1eUHEnGxuiBSV7tsK0yi3qTSOrhEh76JS7zy12zLBT4Y+jdolcXF6MBH7W\ncs7Af47m26UMSmJSQ89/YLLY4/b6owtB62LW7OD0qVs8ITyzFzvj6WrWsLIX\nYd72IlG8XtY6+dlyRMVgsPN3n5LWC3r4ZBhY+ZphE8TDO40M51LQLdY7IICD\nWVs5z37CddClWNOlMr5r07b4Vzem9HlLb+WtbTDUaek2VPCIPYJsGzQoxMM/\nnEo5mCI4/9VG/5REURrYto8C6HNsFdBel/b1IreaIKg20tDCEarxCCicOAS1\nFNw0\r\n=AezA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.5": {"name": "nodemon", "version": "1.18.5", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.4", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "3d3924df23d06806952e8b6d3de052f2a3351807", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.5.tgz", "fileCount": 42, "integrity": "sha512-8806dC8dfBlbxQmqNOSEeay/qlbddKvFzxIGNxnPtxUlTtH77xsrC66RnA3M47HCSgMgE5bj+U586o50RowXBg==", "signatures": [{"sig": "MEUCIDqxmDf5PUH4Gy8XA69INtPN54Po5aJlAMhyW3X3Stc1AiEAvS9ZaF1AjViHokYqSASQmmv26GmM/FIbqhh3G7j4RjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0vDTCRA9TVsSAnZWagAA5R0P/35GIXz+NA1LNTy9jsLV\n0OtWLqZjLvWF0p/XUcqvV4xgaHxpSbc86GC4G2HPmLZ6zJfmA5Avhz1u1EwL\ng9oKN6kErvbq1wPsbdEp+AQpGpz046XV/gqUej3boxEJ8KjSJHdHqP0eiNLm\nJi3bixnwLoxG65hClmykB6B0CekpkzvbQEGZW9n/3DeTL2Z04xxEH1eTdDu4\nI1lMG52Vx2nZotnpP4pYM1so92JuiCl6QDY3SI24of1Lkj/2AYUI0ozFdldx\ngKRF7Q/XYg+vKW6+zK4ch6K09eJrgutxlx0Tfre51ZFpSfxoG/7FLugRDSu7\n1BoXtzCUuPc0JVPjCxTkrCuAb+jh5aXUnGph8EzpYqy0P1ZwrAPsEtpLJo4T\nSh2SSgOBPI/xo1VtyIm/F7JMxG/z3SI0717gFy8xW9VnZStPOo9glZIJ+ST2\n96VYJ/C4n/8NWMr6tTpLeQTawg+VIUapT/0CTQo9P22mtpCjROMpTXa76xCc\npi6+6yDMoiwLhfDYFwbWYVgz+KAkNAYaDNGatf+BSSWtCIl/59F7wvq+qh6W\nCrwpniyBb0o4PFiN5fM7A20MSr4PUhrXU6ajMeZIFILJ4Xd8QjfBPpTEMldG\ntppPPodWyt4//61lg2vcK+qixysyTOAfNNYTiIumrwSo8+BlfemnmMCk5dav\n8p9u\r\n=Nci7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.6": {"name": "nodemon", "version": "1.18.6", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.4", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.0", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "89b1136634d6c0afc7de24cc932a760e999e2c76", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.6.tgz", "fileCount": 42, "integrity": "sha512-4pHQNYEZun+IkIC2jCaXEhkZnfA7rQe73i8RkdRyDJls/K+WxR7IpI5uNUsAvQ0zWvYcCDNGD+XVtw2ZG86/uQ==", "signatures": [{"sig": "MEUCIGxaDkgj2WVZIp1PXmzEFp9oPv7RFovu4F6uWjIT82qYAiEA3P21zJY5DtVquCXQuluUmH407KIb7itX6VTkLQDUnh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4Ck1CRA9TVsSAnZWagAAuYEP/30YkvRbuXUOSrc7tmdo\nxFNJkGlPfkPNlEgh3xmVV8eaxbKN2wfvN0u7KUG/m7C37IEjT6hzvL2mBFib\nKAGceIs+A9E1sWEe8lFYuByWsBtAtOnpDJ17nnDwlztl0bh/p5Xho/jMMY2Y\nmitEry38Kl2MDa0QjCff1My/EkiHcRC9O2d3fa80XA4GShUvxkfJxBiQNoci\nHqcxwI5q6JIIpw4TOP+H2behVexkg+bU1MolEYJ+71Kp8mn1snVc6mVqet3W\nnH6IcjSHOtIcBpOty0BYVlqabnODGIJjodP0tAz6NOtbSzH5G/LVhQ9YuqpP\nWH/dt0wpKfytQ4F8kfsztosNxuCM+h6ySsee9Z+zByJbXaNZjkQ9+cz0ilqX\nfnJPWnKgSlJ6dycOywXRFD+0gW5PwLaXMCpExdZ8Yci6xTbZUQEJGrQGHeJ+\nlfu9CHHl++NTf7/RlvOvnD2ftNqErLjJD/Iy9m7zFYIsiCSRniaVohSf5uam\nVYyX8s3maFOvSwXduCtSen1ipgt783wxht/ir1TZ/ferXmHUQQnFWqqsQKFi\no2lHJDEAGUrVDC6I3GhKWmWEomPthwwcu9CMt2HK4LYRAB3Zh20T9KXg1HJh\nEnZ/i1kEaeVylcLPegZetLCHk/SAEQFm4f/GvAXJJXykuC4N7kdIkEZcsx40\nlwkq\r\n=vcC4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.7": {"name": "nodemon", "version": "1.18.7", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.4", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.2", "supports-color": "^5.2.0", "update-notifier": "^2.3.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "716b66bf3e89ac4fcfb38a9e61887a03fc82efbb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.7.tgz", "fileCount": 42, "integrity": "sha512-xuC1V0F5EcEyKQ1VhHYD13owznQbUw29JKvZ8bVH7TmuvVNHvvbp9pLgE4PjTMRJVe0pJ8fGRvwR2nMiosIsPQ==", "signatures": [{"sig": "MEQCIDU/IVnOZuE0en3X4tmMCGDHli87Bwy0bLsLUSnYVh9DAiA3B+1smqgVvAnwK2R0h8hfx9FMQPvdZrReVcqSI6gjnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/QUCCRA9TVsSAnZWagAAa8MP/11YVdtqK8cNggTT4Zh2\nzsXZ5j1z6jGtmH5BBfMWt4O6FkM6lt3bObPVg1SgudJklIRFOT/Gew6PO5RW\nbmc9DUDSCgD5lHECN20l9Xs7R2QBEe1hfOf2bOx7ANlJgPIQZfn37xnsRLOE\nxJO6ixdnpw91VvNyC7wq0OEixLgeVmPVA45wq2ooFdJeBKBY1cnCN2iN/BEd\nrQFzB2s1xSzKgTbKdfjlPPQ+LeIlGDxwdo7LOwB1R1rYTru57qc+/8y3W4kv\nsvtRPB+SfDaiUe3PWfVDaf+lvq6tvHrqGQ6xJaXI14oyOqNo/Z+JD9SAZ9+n\nK/Onia02vlmXr1aD7jXLoErfpAx/6gmGs/3qAyvwEL7QubSBSafi/wOW7dSW\ngSKiY9jye84W9HBNRs8paulMSoXs2K+2MFrEFvuGLUB9qPxGbBMSu8GOnetW\nXzSqmpYp3JoX9z7JzcPiCEBMl5erYbedz6T9Yqsh2NX/SH/eoO+LzMV5YNp3\nejKcG5HqoWUP3vSQxCgxUOLsi1P6J7cuckte6uQwxo/d5CxsVK9ffl4sPk9s\nah+zj632AqmJtTTWAdWA/aaa4y956/jaEDRhF+Z7dR3jUtjwdUN58AMY2PXX\no0+x7NzaCM+CLdUi2hPfA/cRlBhPAhzU2/uKCetlPAKJR3D8NGbeGjeadSuL\nIQCc\r\n=QL7F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.8": {"name": "nodemon", "version": "1.18.8", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.4", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.3", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "eb4c0052dc81395bdc503f3c8ae3cba86ca7146a", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.8.tgz", "fileCount": 42, "integrity": "sha512-CgC/JdCf+CT7Z+K6wWaV30t8GU1DPtXpr/6PuXC1/LboXCmUQNKOaz0AEMjoWDTt2AdHOBFxgv41dyC0i79SbA==", "signatures": [{"sig": "MEUCIFTrML33uA6Ra3Ic+9tMiiyktCaU+NzPQ5YGKpGVRZG6AiEAmTtPQko+q6wxQWxaIV5hiYm9DsI1/ROGBHRWu4Ug158=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDtUtCRA9TVsSAnZWagAAosgP/j3pkmDRhvLdiUYlJ6NV\nkyG6LenETJPft9ryPINiZT89F/jbPYIFnVvrdPSHUQhwZYv609YhN6BwXj/q\nvaPMAgALG/g9S8F7lfwDzOlbUT6xEWl6jB9edSV8/r7ypHEMglxSIVJrLIjS\n+SBma0+g0KruW5ctYHkG4lZf+AZdvYHD9RL+4WWgQFzo6pfQNmqGpZf/kvSh\ncPB+0zpjnoeVyBJE1DFDWQGa9uNpYsPaaQ1JQNOh0mwlMjDKrg6e1H1Dbf56\n3lS3u516GdI19PcaxBQm11z/s0OrJQxCHF7SWUOMJkCrz19afgPBwMT0V5aN\nRDXUzm6REtdjwj8rnT995BA2Pf+fYCHkF1PharZdMa/uwGdjOEAJZeOLXi9E\nwRCt3HyOlQWiaCO/k8dKmRjjYK0siCabACxvFexhf6IYYNhp+e1AUAd+uoh8\nePdUoPpRG7w/3rdsime3GQ4AHqljYLofvX9BclPGb70s6rq0gtkKmp4/Smsb\nClug1+MEJh07bWn7M0PRwoQkNzayif/dcN+3uTR/N8XPSTv8nBfLoJtOgRLy\neaIFCivTG4R3nInSmjjKPy6rh18n7eYQkDjhbon2jKWt3iFBvm/FA+aXHWAD\nWaWWT2G/8CQnA5+B0392O4D/9dSzMLnr8ftp2k29tHyEIl8+giCRv2jxvmC+\n4xK3\r\n=FgzT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.9": {"name": "nodemon", "version": "1.18.9", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.0.4", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "90b467efd3b3c81b9453380aeb2a2cba535d0ead", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.9.tgz", "fileCount": 42, "integrity": "sha512-oj/eEVTEI47pzYAjGkpcNw0xYwTl4XSTUQv2NPQI6PpN3b75PhpuYk3Vb3U80xHCyM2Jm+1j68ULHXl4OR3Afw==", "signatures": [{"sig": "MEUCIQCtLxC4No7yKZzz2z6ycJtlX3LHuU0rA2cu5uGdW70AywIgI498n/ONGo3HummG78HOXsdvqH6wghj4bbUR8EnHcJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcE+ywCRA9TVsSAnZWagAAduIP/0CkRHoIjvm30aqOXvaP\nlkTwvTKJkllQD9boF2KuL/Uc6vmiWNGuNCX9UZDkdPZWNo3isWuu62uJ8Mew\ngklUPFjOLCv8757O2YSq0TwhGcLDBOcilobqhTWIE3Xf8jirgbsLl+0uERft\ny67dtkNt4JjDmkpbvzhD/txbJjS7fTBz8Spkh8eI3CTCzfsLxSB0Q8gGkeRE\n0CMm4J6lHxstIY1VaKN04hg7zXtTcMY70JD7+U7kA/Bu0m/vAKru5JpVVRqe\n9Pq0J0jg1xkAX4LkIqggvOEnidVCcL2ziflZpvjXe8JMSEhLcUqcwPWmtHUE\nVVSb65p/YsmfXA6fpePOloMXY88V7sLm7OQHiycz+kITuEYzXwJyfrDCYH9u\nez/InU3TG9HOc5Ps5nvs3qXvkrzus9wdp+9rn2dMCqnTr+stsU3EbuyK//Ns\n/Bn49PjNCP2m2ulKxGcQElw9w67Bt6DVgRxp/ZqfkPsn/Wo+psFMpHU7Hi2a\n2kU40glb34JspgDLsMNhuW0GYM1O7T0hHv+ykpGjW8kAnyeCbWnyG7iuRXEJ\nmvHdjcKj8Xb7wuFkm9fH7zFJ4obWkO2nvQS0p9m25Nhc07BhK0o1V3XVQ4vx\nYSz+pCQu8oJotbRgGvZJZrniy5sJRP9B6VjBX+unGw2D0DqxBc4Ot1nWh64n\nfZaC\r\n=2MO1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.10": {"name": "nodemon", "version": "1.18.10", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.1.0", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "3ba63f64eb4c283cf3e4f75f30817e9d4f393afe", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.10.tgz", "fileCount": 42, "integrity": "sha512-we51yBb1TfEvZamFchRgcfLbVYgg0xlGbyXmOtbBzDwxwgewYS/YbZ5tnlnsH51+AoSTTsT3A2E/FloUbtH8cQ==", "signatures": [{"sig": "MEUCIHvWA0auYighcv/1tyVR7o3cwLpxbhTmyqK4Qon471fEAiEA69NBl626RDWq8mWVRnCAIkiO30vluiFTcmxc9Zr9H7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXgChCRA9TVsSAnZWagAAmwUQAIXiIcV1Ht96MczxAWBS\nj3Qba0iFdMJcq0Mttw70guYh19mmti5xamamVCqnRyc00sAyDGFNFWwCiQ3U\neiHqUDM+ZAU6u/Su+ulJuTWqs0XkgJiI7yK4hM/jObgho6Pbng4LRVTfrEQ+\noOjJPZ1Ls6HPWy3ffl3pUOcLO5PlXKDNI2/vL2K4c2iz9FnPi5wR4AxLJa06\n3wJGaPfQS8K8SIRsGopzx7hKXjMTVq8fNEZDD/H8yzlwybeMhdvVkCmkrobu\nexa46BdxwZk7yvGOqvdnKkBLD/OYOoxCvduD4jcpHlSpkPVqAPrjgu6J8Hh8\n/4FyM2Tb9Jbr12Jruy8RvTyCREq/y5JJW5il2oA+A4cq1TDFm8aJmNijCbaz\nMyJuTSvcgffWrFAZz9hu/zMB/6blOeqL0WT9XpQ05qzYT1sYw194SvfM+MDJ\nrOckvqjdpJymWFtUraFUACwZox5Pt0xWZZTe079JPTPaN4pLFaAOPP6yZaA7\np26NWt/4LsMPoCcGyoeD05fh/JXF4mV5KOtdAm4o1v/p2+TR94b2Gc6bT+wc\nc1H6S6K4Xz2aCbOjzPebJAvSllLkbbgmSRAMK6GI5jKikNkVsDTRWw+NuU/z\nTnUVbkuiMZ7K3UhPQInhpTEgQs8p8vNYH+uoGN0CPwjruv37wX6FFqlOI69J\nTPQY\r\n=Yn43\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.18.11": {"name": "nodemon", "version": "1.18.11", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.1.5", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "d836ab663776e7995570b963da5bfc807e53f6b8", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.18.11.tgz", "fileCount": 42, "integrity": "sha512-KdN3tm1zkarlqNo4+W9raU3ihM4H15MVMSE/f9rYDZmFgDHAfAJsomYrHhApAkuUemYjFyEeXlpCOQ2v5gtBEw==", "signatures": [{"sig": "MEUCIQD31WhtfRE2aiRd94mXb5Scgl9Jy6rUUzePYhUaPuFMFQIgZ8lEVi+cx3dRWxw+c9s5uHdnor7LNBawl6GUJvDh+to=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcq7d+CRA9TVsSAnZWagAATWAQAIki8+9alxc4wrHYRjDl\nHLdDWDXtIe3t0Clpg4vb2+2kS5t9UQS9G1fevgEGXn6oCr/Ls3ZqIgu6i30w\nxpIC5hTd9gLS5TdCAyG5aE0QyLcSH6hZIetQTEAAf2vbm/+UeUytH7piWA30\n8KYif/2H7FFr/lrzGxts6HMCB8PRe4OCfoUJU5HKCFI3oV6uM1Fx1A/V3RWr\n09dcO+ZiSBQNU5tTNHreBXV7KD6PwdHNTRs4uBDXSfZvSy5W5nAyDtdN2RcJ\n6bI3hAqF3FRVqKGpX4exZTDxYANyEEEv6EtCspU05sYV+ryGsNPEUBVbrZyK\nGja+yacq/dqy8E7pR/WQk8tbRgx25ERpOlf++rLh8z4ifARnciMeucD5rm9o\nIJ+i8w75Clf6ADnW35Ke51mE8JTOgUJi8zVny0bPIUJAkDDLXo0mNPc5N7kE\nscc1BLNBdu+OeOLGXnAVXle4QCWmgMW/0mrjYVdnI/2+UWYNZ7KDOcoBrZPA\nMDrvZGcUI7r8QZWl9b/JgNemGexB4QFj26mFAsyfNg1ZMh2L1dVQ93uMIJGS\nKvMuZ8BM0zQo4+lO/KT+AHJVoU+faZDfE8KgrE75xvE8Xzuu0V4d64cigiHF\nshy91v7IUF/nOdnh+2MfiZC72oE/mNhe/Rb7WLksyH9+4MxivK1PGta9iVTZ\n8FxU\r\n=VLPF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.19.0": {"name": "nodemon", "version": "1.19.0", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.1.5", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "358e005549a1e9e1148cb2b9b8b28957dc4e4527", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.19.0.tgz", "fileCount": 42, "integrity": "sha512-NHKpb/Je0Urmwi3QPDHlYuFY9m1vaVfTsRZG5X73rY46xPj0JpNe8WhUGQdkDXQDOxrBNIU3JrcflE9Y44EcuA==", "signatures": [{"sig": "MEUCIFSa5+eJ/gRkdvUoXIs9P+j4hY+0j8JaV3yWgCLOcZmTAiEA3zu3n5wZPiH5w0PcprPEGzeTWDJyr9JDrxFiahjTsx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyUZNCRA9TVsSAnZWagAALncP/2aBPeSzeXTTziz+EiVm\n1URmanBYnPHwUj0WoLU00z/IrYMHXqQSQtKcuTXaKx0G40g3vCkAc+J3goLJ\nieIStyT5MgCl2cQp1fDDD2rc513pBmMRiPKhhZWbWNArBSbZx3a81WYtgdtU\nWayfGsLfb+z81FJzrjWLCaI56Mhio0/clL4E5cqaUj6uE3MyX+3Jyg2IHvFK\n3WsmLUnIypalNcXeWAoJg0xUh/GUGMa+t+4l4pymdojooaatqklIS786AzlP\nUB5B3idrFQTTbq8TqSUaGxnhy6d+F5IuoCC/JOHaXoZebCiTnv6wLK8rOJ6B\nB7zFily3WDfhkvyHdxpigDh0Zk24IY9Ii6foLbVo3MsGTYft1D3/oA+F9/sc\nWisgQ7nro7qfsbmOYmTYATD8brE5i//N84ytbbMQdTO+BwcF3EYkluw+90xX\njJi7C6goBIlxTInrZeOZL8aAEmUy0VOXTNwIFKKXOB9iPfQyWU2vggVkkrTw\ns9KDrVf0hfRDmL10felVtO8Gyso81/+04PPTnK/QHjiF+wDntHDuZM5RNjGc\nZ7anso1ICfM+bnkc1598JQtZYBbLdFth9QmDPrVMwDadWaWw1eDQODvpH+rt\npyqhcYvk92RGnmXos25Ctl3empSdN03OPlYVxsObGxFBKezsk6Q3KOM3cx5d\nyXa0\r\n=mxKU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.19.1": {"name": "nodemon", "version": "1.19.1", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.1.5", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "576f0aad0f863aabf8c48517f6192ff987cd5071", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.19.1.tgz", "fileCount": 42, "integrity": "sha512-/DXLzd/GhiaDXXbGId5BzxP1GlsqtMGM9zTmkWrgXtSqjKmGSbLicM/oAy4FR0YWm14jCHRwnR31AHS2dYFHrg==", "signatures": [{"sig": "MEUCIHcA4lSXULpORCIUYSrdysRg4k4wbL26iX7Rohf8+Nv3AiEA734tpg6I2yMfFxcw2cMgiy1v0ouVUcNiJZ4ssnUC8U0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6YnXCRA9TVsSAnZWagAAU2YP/2JXWbhG115HoBKsUzjH\n6ndm1j3/P5RuKZBgovS/7ppB9Lhx9L/CMpcNwCv8XGH6MyVtgA7wlvis5MDu\nu1E/WDILxIOD66i1QiNvPLtF8KRVmfL/YQZVwWXGEuS1bkc4DqAINDIugITX\nX06M/wDOmXcewTgTWtLQfHzheGVNv00nPjn0oJXHWfWIRbDeLlxCNdUe8XJr\nHUE5SDz4pw1gqvPfRSw+qPfyo9JO/kjSOy27na8YdEqzPsam65SSrxlctZcA\nVbVCYnhWwWpWdbw7Vb6IBWB3PwtcXuam7uXTQ/Ht8KSMZ7XcirUi19EtKBeG\n3UiwFLUfRhTDM8GmY7dEJzljUGDjxUKEddh46Fn52GZsRiZCUPcNI/MmKe3H\n2VmW+pSKaL/YvJ2BnL2vQcV0vd9NkR3QACllG5b53BPjpt2234jKL6AlzpiM\nLeU/dAcTwvcBjFyT1IWJfu/BaY8Na0mVwhjuMblrdVS20pp8QLO5RYu5vuvh\n4LD4P8VOCahaqg++basOaUQeihSCfv/ZCT7wPDMqZusZbiHNj4LeJ0sTo94x\ny96ixRA/73yO+Iu9BtJKiCbrEgF9VSZemOn0W0ZP5aylhnjkKa/3qkA4cKW5\nG923JUZn/uRLIR8WkOVjirBWR694fCJ9aWlXfCPbFuAFs7hNZ/X0pMTO0oAm\ntlcT\r\n=+Cwj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.19.2": {"name": "nodemon", "version": "1.19.2", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.1.5", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b0975147dc99b3761ceb595b3f9277084931dcc0", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.19.2.tgz", "fileCount": 42, "integrity": "sha512-hRLYaw5Ihyw9zK7NF+9EUzVyS6Cvgc14yh8CAYr38tPxJa6UrOxwAQ351GwrgoanHCF0FalQFn6w5eoX/LGdJw==", "signatures": [{"sig": "MEUCIBbBP9sKynsNwpKgvHgdwXqo1FyxtgK5eDuAc+KtgmdPAiEAy0Q1Y1PPbPPwWErQ15BlhKs7xOk7nDzYYL8RwAb6Bf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbkf6CRA9TVsSAnZWagAA5nYP/1LMrjtYfW3vOYftKtXg\nk/6F9GIGylz072FbdGxDcv1y4XrHdB8qMlm/lLikfQvk2GXFaqekqVa4BQ3G\nsfj3LZovJP1cq7G37UxCUx6VLRuHGRNd3lMu4tLfpFL4UNDlsDAB5c6OYA5Z\nNIXq971m7UaJpS+yKixJz79FCtkCB/ThoEeGnnPnvQl8s8hartXkFhJWyc3V\n34ud5eBqoxWAJfFMzLhYet6tV/E+2IfaZ3S9vz9tAnuf0qkIJse8PaF9G2NT\nUeFEbRj9wXpduQCbrf1aeS3ImERNo3m4SomQfiS7chj2uvdDrJkFhjKqza4h\nIykZ9MQCIusGH+MUl9u1Ny0KIlhtMGKLN/iCzOCr0RuOfb4QQm7/yP0zzfVM\nltHkNOfhU5OaFautSbULu7lma8kIbNiiyf1Jjuzp/RAXtZiUvlkt/TojuLnI\nKpGgxoSYmGqTG48Our4R81ikn2K8P4yl64aR8C3wgB4+WZnKtvAn2ZXEOR4V\nWA58vDFW5JimG1QDR+sNrq4JAKiw8RhZM/eqgS4BIAzMmb6qyR2nPa6cKRON\nHTrteUb1xgWv8qXb97k0VMNq5g3dA0t82oJ773EDehut7tcRBeE0trEwAiDX\nbTR0N5RtxVqPqFxz7RTAIHsDdoR1JzKcWO7R5niBTLBjiR+B/tJup0V6Ky2D\nSBSB\r\n=qgkC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.19.3": {"name": "nodemon", "version": "1.19.3", "dependencies": {"debug": "^3.1.0", "touch": "^3.1.0", "semver": "^5.5.0", "chokidar": "^2.1.5", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.6", "supports-color": "^5.2.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.3.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.0", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "db71b3e62aef2a8e1283a9fa00164237356102c0", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.19.3.tgz", "fileCount": 42, "integrity": "sha512-TBNKRmJykEbxpTniZBusqRrUTHIEqa2fpecbTQDQj1Gxjth7kKAPP296ztR0o5gPUWsiYbuEbt73/+XMYab1+w==", "signatures": [{"sig": "MEQCIFVCjSNqr6UZUbGn8SxS2QnYT/Ao686jB7lj4/N6IINhAiB8rHV0UgdsMu0o1Rxo1XVDfCsKSxc64aJyzXv9jO/6dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkIJeCRA9TVsSAnZWagAAeY8P/jRtRtMnaeLaxlRmCMIj\nb+mpAnOlpezsgO5qaC7aZdFVvYh3WsDgJZUVB0tKWL5bP+uH4s1niRUSzOh+\n/XmHZfIHvCXbYAnnR8g9R8Fvs8aXPwzY3yXX5U600njOAbYlmaFV7F3QcDum\nouDZygmBGTfpgtSEsSpR93D3ceSherXK7N+mA7zBiLBsjmvsk49jnHzzWuzX\n8bqmW7SB/bbUf4g7XbvhIuX9MZ2ssq3XEf6HfZgWG+NovWUaOkhrtJCsHby2\n+60UWRQ9XcQtWpmQ231Sb9dPTtTZb968T8zFayM5V8M83OqSL7XSkl7yXm9u\n8hy2xguNzB14ZFKkMM4Kq7+t2qxkDOfKUz7eQ4t77azSUgH6t9PvDcR5J2J0\nhrP4PfRGyLkobHAIBR7W0/KeV3xOMSg3R/wZ5cX5WXShcadj8ATHYxQMjTw1\nWQZKmuDlgG1EAw+ojxy2IKaWWyrwEvdBhJHH0U8i1cHRhfvv12NJCTBLKvR6\ndZgVUm1OGf1HRH84PqSwJiVyp2rx7Mgx/hFXXShDY3HzqxdmKcu7RJ/uYd90\nWj8b7sbZawV93wuZ1ZDScC9LW3QY4VmN23u7+s+xiiSSYYCIh/P/wWNCv1S6\nVDSjirDnOmF07xUT8E6wrYLV3vYv0St8BmF0E6Ua0X4CHauQxRpyuR+WdZIA\nQdVg\r\n=BWEu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "1.19.4": {"name": "nodemon", "version": "1.19.4", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^2.1.8", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "56db5c607408e0fdf8920d2b444819af1aae0971", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-1.19.4.tgz", "fileCount": 42, "integrity": "sha512-VGPaqQBNk193lrJFotBU8nvWZPqEZY2eIzymy2jjY0fJ9qIsxA0sxQ8ATPl0gZC645gijYEc1jtZvpS8QWzJGQ==", "signatures": [{"sig": "MEUCIDWapQ/zwAG3/961whKoWVpFpft1gOjXWGWUyK0jCi17AiEAz7YzD3Vh7AZ7gh5NyKFXmbw8JWYE9NcvIwr83ScOa/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdplbFCRA9TVsSAnZWagAAEnsP/1dA5QAkZomvYE5n6aAJ\nHFygohsYctVz/KuAif7h8vJhzgPzm4pejc0Utrt+J6ijuWTSLCk8KbrOdgLB\n4iYecMVYDBt114kJv1C1c7lx6LenVxiVEAgQuctBfO+l4kSX5pGL8rPCwD9c\nRGp3dNVNW8IK7VOXTkEcGx/aFOinLSzKAFLsmZspW9BgJGRDkK3jsW+mEqTM\nC/uhfGEjVa17nZ80ci1xsn/uDv/F3fcIISLI3KR2S1YjhY0yM4XEqN9oK0WI\nCw4EQsPTJK+IwD6i3MaQhgCB402DbVmrddyv8Zux7ZnbpDfLXdYRGJxrCtB+\nbwrNJ0a2C10UTfDrUtRFEtszQ7pFGBhHxsyzs93VWTXFHOVV9kN+HpatqxPY\npIIvbi436V+3n9J9PqI/wBJ0WkFjz91mEnjGzg5ie6vlLmd9NrBPaQGQ/SoC\nyKEXSAZBUBZaG5RQ5KNxOKEt3Cjycc6gJQIKea+V8znlvUGz5ypuQAEjHw2/\nkyZo5cJHdRCv6bxEg8R3qhi12RTPVOK/uGkPoHc14F2viOg+jCrJylJWnPvv\nlWyBh7vJ94jzKBFgkogMHJbe4Gu3gSsJ8e36BuZlELIgoB4MCUm3YuTjE3Ud\neK1LPNWomMlt+BdxG/12enikenNg+Eq64Z4pJ/hbYDkiChVu1bbiyi0oTuCS\ngCjH\r\n=/ZJq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "hasInstallScript": true}, "2.0.0": {"name": "nodemon", "version": "2.0.0", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "1c19aa3c668fcf5dddd1121555efb90e315d8edd", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.0.tgz", "fileCount": 43, "integrity": "sha512-hs+lNmZc6pIamxCTDrOhMccqSsGjZENGZ/40etM/Zc3aoR4UTvwMH38XOnhD5pmU+Jn2u1OGOC5hZF2tjCHJMA==", "signatures": [{"sig": "MEUCIQDqXMvQr9ljXzrLhX2UDIAEb6SJpXh2ePjaVXU5svoVVwIgfkPOueQ8Lyu4JghLdqBTuTXeIQe8aQlIUwCNRcM/6yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1VrMCRA9TVsSAnZWagAAwckP/RFKvn+twPKkobz4IN5n\n5iXnRi58KRUbA7w10unw/BQAXXn9BHSJ+H/MjunybM15OjNzBA4hrZAIf68Y\nW68gHXkf9kHdkmont3PquyfT7sdXfs6d5OXDjBX6esp4/cibBPV7dj0G5Xuz\nXjAeKSHMyq+Xr5dM5W3cf/4fvTX6/4h8sze2JmMHGi/nppOyLbPrPwrk8J5x\nVWLrsJM7NWRNMDDaWisW+FQIdfTKaiKYFmfpAUqSnOCLyyYJDpZ470ePvLHm\nCz3OPrvI3s40IODpJ1LWVzOFFepCRYrdS90Ot6PzNQwoqE89fy7gKRty2TU2\nZjh4hWFtqkhX0ZWN1Ym5VBSERWI8lVTfrHM+8w3U8+AqMz0HYLtpfLGN1jlk\nNo7ITz+4QzNq4q7njHsO69wdNUZF+rt2nC2w13EwMfYEJM1PwSlSduwsTCVX\n8xkAgAk6sIbC/vRVAJEpkxZ6lAzXBLci2odBiSVZj6jopZd+zW8aMlWSea0t\ndZTjV5jZpvEGp9Oh21iVNasitFLrnejc7g2kr2SnaPfYJ2wiZty0p2TsYWzr\nt2W1kQH/79jIfT3H1CAHos7DNwQ43fpaJwDb2COFRS0D7aAl6W0fLyQ409Mj\nyaqbYTZOsr6thArPYDJax23HQGWxhUlVGwD/Jkmfqwls08dby6SH7hIWnVtf\nSo75\r\n=/NpO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.1-alpha.1": {"name": "nodemon", "version": "2.0.1-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "e35ec2ded54c566c9d9486b1466d9023e27a80a7", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.1-alpha.1.tgz", "fileCount": 44, "integrity": "sha512-TD0lhruHYtDzRNKy1mO6sKi+lev0WBlyF6NUQbKeJcT1Eh1ONekMTAVop9kiohT6iGnZCuSJNgGdBjpS8km06w==", "signatures": [{"sig": "MEYCIQD+wf1wh5uQ5OzMs/K/tIV6z3Ih6UeFbvbl/rOI/nbtvQIhANYP5ArQNSunYLKK2dgA/k39auNxQUUZAttuCvNgbr2U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1nEsCRA9TVsSAnZWagAALNQP/1I8VMKUZepGgMJlZ/tw\nRzClZqoJDvLfJcrkbpz6AYs+Sjct3OUVLxjIsz5zTJrLpH1HVsCYDvBk41K/\ns/plk+5AWs21WqarpRIYfFlQJ1AZFcUCzaFymAOB9loxbWXHTUgxN5XL64Lt\nZDxAMSw2U4v3y0eDaOF7SkxGlWgUNTpD1QCyvG5UxHgTmCXGVYK1mMsWV7L0\nY1FvHd25PubFk3XCCtKiD0hq/zLZXntpc8T4pqN6Xde9wGWUvYp23gYt2hbs\nj8e9dl0U76JY+KU8qqqMM1RXLnsO28hU5KKumHmP0Cu64EjUiVojJYmCcVZ9\n83E6K9A5g+atYMPbryg+zwJEjdFY0ksDgFQheY+TqkQ3DRbPx4DCGMRvQvwF\nuG562ExiQdWw9+UnvQqi4XmQrUQYbt8X/YXMbl8ZV0XAxRNXvR9v8HgOOS6c\nNyADJ/f3cJK8xBuYvbqRV3d3rVkgWRLM1Bq3YrGwN6+VNx2Erd2Nvh8NZite\npOK5bWk/q9Bu7i1HoeUgCbJLm1M4CG3Wpty0R+9vOdNUEML6EwkdHGzdfIqm\nYZ2MqrS7W5Mk98OUJI8o6IlI5Gpii2p7Gr6HFZQQnSpkeO7euMX9awflf3OU\ncUAxOUMuRGzimIzw8V346pNBvhaRyljtdQVDSDvfCINJNlM4Uy7Z6Ea4H1tD\nv+o2\r\n=DJ2S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.1-alpha.2": {"name": "nodemon", "version": "2.0.1-alpha.2", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "e41548390bc09a0428a90c374deb667dbc8e1303", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.1-alpha.2.tgz", "fileCount": 44, "integrity": "sha512-XO723x09GnbtDTwvzWhyZKHaIm/xCg9YojM1BhGDlHuOUwKW1Tdy4DHGwR+gkst44Ff+mo4W0K7jOik/S0JTyQ==", "signatures": [{"sig": "MEUCIAmX2pqvPT9OFPO78ntPzB8PggMmLYq8tkvQyamifeIsAiEAtg2yLjdK/V9kAo+R5kfuLFKCn/CYUpT702DgC9iZ1js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1xKzCRA9TVsSAnZWagAA+9oP/2ak9bStgXe6+g1hpzfJ\nfD52v30Ra5dxg/bgAYY6lmwF9HtkFHmhqRcw0MfKAMx13wEwChWYJVvn9y5v\nmRyeBf8PXChZgKmUo0dfEKcXOqfnz3E9+xcjFxOPrHgq/x5p4Tplq++BHkvg\nMx1j1WqYMFteSIv//tBix87vuMVRgjWgedbbzHPqZjZKoWlor8zvaHV2doAT\ndg/bjCXOWstTAXrEpGq7cNQrU6UG9ZOUNBeFCFhl46EjehvV8GY1zol89wjn\nI/QdA3Yp1cB2oyqIKHMAG59FOYqIE4vO3eAP79+iJ9pjZ9HXFcFpxwDsMjth\nv3P6rnZst3qnGT8jXyHSGfI/WfLXPFfHIibYo9KVtAyxWIGiSkIB3cTTsdg5\now/PDEbmgFDq0oA0/yf8LN2Gs1ln8keYJuKq+Skxd6qH0R7S+QqjR4DdPaAf\ngr3h6sEDIyPUud5kMOhnVd51zLVVokuzAWm2zV4RCJXDAMkJRlIEY2UwMlgM\nM+lcHMng22uY+V95uqvjOFzMnwIJY3FvcYBwhf9baC1t5OwqDOXF4CF5tOB1\njRwSPOYeajUiLnhk3G5wUmu6bYOEpum2fXJWYTR/sRj2zCIl4pWO7o11ihhZ\n3F4gDOyAe1Wat0tPUsTwRYe4nYEybjyxZ6YFPGmOVpewvr3KxzWDD/LPhVgh\n2bF8\r\n=crJk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.1-alpha.3": {"name": "nodemon", "version": "2.0.1-alpha.3", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "b772aa52937cdb83fa84acef1a8dd9980526a5fa", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.1-alpha.3.tgz", "fileCount": 44, "integrity": "sha512-4L1I9Mpk+jkZN6AV5cPGCeclojZnHPO51cyt14uVzPfU3DptyylCPegXiYSky1VU+eQkVEiG8tyHqKeu8g0rSQ==", "signatures": [{"sig": "MEUCIHci84USCB+EHSWa6kqdN97VACsQIsDbheTSMZV8a28VAiEAwbTAxVH8N+k7OojU1NbbLuyg1ESNElrhYFzurooOlqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1xUNCRA9TVsSAnZWagAAYAoP/2RzsdbdHL4/mBYqXG4o\nt509GTjBQlRgpeeMzcLR1sOkf6GocDmUhYG4dB3E/IXEGBM3KHb0S3/COFsy\nhGXlLGpIAoXVLmoUckEojvolOCbkS5DZtuKA49tcaLT8nDsjp97s5WqfK+Pr\nEwxXCLA/OvyqT8jrRhJew8RT3PFTrMfBYPKOMeHf3rafANVCGSW8vAUkIvVe\n522Z0jGOliiST80I7z6EM1QtQ9FF2fOGbDBNwZtD/C5lXVwBJvHxqE1z2NZm\n8QtoJPBqlkfYY0vizWzU6HeWUJOzZa426VQKsUH6yGDtZseh/gHDrRkSKaxW\nt6XswhgeFWn3/iTm2OAwK0HQ4wkG3aI/UDpg7bKx/KeNkDhsPbJ9tx5yAbfW\na3yMbUiib3ngKlOT757Jy8QeYAeUTGpxHX+EnS6kpHbxTVAa9d0NnFmuXj8y\nf6ZvlOtNPrw4nKUDaXxj0qKbCuS87SqcWyYxS3ppZtiVDCKWxYMxvsH5EL8O\na9cmGyUH/0dV54sbIbWRYU/Nzrfh6T1OpIe5kGfsfZCRO7hnxZV37Zar+37m\neQT0RphmtuHl4TvSLEbr/vl03O5jMn6s3+M7wq16j0/ww8efXmJxZE6mVx2e\n24UrW6GmR4yziv0BWvGVvzVEt0sSmYvKHB2QHn3F5ewYNCEsG1+Psp6cSk4C\nO/3e\r\n=7Ixy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.1": {"name": "nodemon", "version": "2.0.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "cec436f8153ad5d3e6c27c304849a06cabea71cc", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.1.tgz", "fileCount": 43, "integrity": "sha512-UC6FVhNLXjbbV4UzaXA3wUdbEkUZzLGgMGzmxvWAex5nzib/jhcSHVFlQODdbuUHq8SnnZ4/EABBAbC3RplvPg==", "signatures": [{"sig": "MEQCICBxpqHWtUKQkzgk9OPcB4Tnwh3gaU8QWMe4H6uH6LqgAiAH7PtgpHoMgMGW8kA4c/8I9/lUlHg3SOo62DN08luQaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd19ztCRA9TVsSAnZWagAAplAP/3NoqNambuc6OQouX4VJ\n0aXNPIE+Vgt/3Z0myeM7O79lDK7jQbls/zpcODJXHXq8Ywja7EEOrO5sCKdw\n6ntaHRyT+XhSXyYL9npiaz4BU1bRx8C191QO1LCfHZhTbm9P8+HBQuO5TxcJ\n4IdJ7Nlj0JFoZkAcsFqze7ub4MjxvZVIw8lg9zIj8V8iWPc48R4rXvK0hUqW\ndhvsV0WLWMyaQGJbWPoHtIHvkqrDjtsKb5SBm62Q6nXSW8q52P1aGHV22iqM\nio9dsdxqTsJ22X7j0EHt1pquV/dhnV9GafsfiL19Xx+lbQC5GwmLCeBGRAjT\nczf7+1MaQtLy5YK/y0wfcbLO/TvV3BTmFOWN93SMMpMX9HiX3dCofPN850zm\n0nOdVEV0C/GQQEf8VodBmffp1lqYp7rTwK9vLEH/iXRspNPpneX1ywAmVMET\nKRxQyVML3ckn2srWi0rRfncy2UxOFu9N/Gd5hWXHJs05q6834M75X/ulNOqV\nPfDEVt+rJA66uwK0hNomCjkw+6p6Og4dtOswR0rx6JLR04tV1yS329R8JqDD\nStV7NJMQxcgLSFIOGs/gXmLo6qMebfbZeOopqkvAJAiMG0SXBGyQHnnnOPtP\nXNDig3E+yM1NJ47/kcTFvc71HBo7SuJ7EaHh4h/OQagT1MU5jkyr3OoLGonn\nYJBv\r\n=Kbz4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.2": {"name": "nodemon", "version": "2.0.2", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^2.5.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "./bin/nodemon.js"}, "dist": {"shasum": "9c7efeaaf9b8259295a97e5d4585ba8f0cbe50b0", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.2.tgz", "fileCount": 43, "integrity": "sha512-GWhYPMfde2+M0FsHnggIHXTqPDHXia32HRhh6H0d75Mt9FKUoCBvumNHr7LdrpPBTKxsWmIEOjoN+P4IU6Hcaw==", "signatures": [{"sig": "MEYCIQCfRpDuxbPUgEf+/auCiXrrUIL+kp7tbQsJ11q+3AmwjgIhAKCsrV9R1/652L5W3nF+HkDPU2dmoeHrKGuFefSytB+P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8PwYCRA9TVsSAnZWagAAETQP/0XMeWEchz3slz7nDrh+\nsOodOuZ2ZPSJao4IXVBW5h73Yn0YHvZdekpGBHeyh1jFdGFMUCEbwke5ASBp\ntp95thwm+DDVe48Uh8kokveqcX3OhMxiRqFsEb7rNQtGdE7khXYLxvyHB69J\nLDjEA+D6qQTMqphLIuLqVpwOJdLzBBJcw/BO9Nqbkx8CP95ykZL06d3hBXOY\nZaHjQ6EtgBDqleRu2PPuPv88zAkOVGCxcQ2EvA0a18gndQly524RuUpIVxhL\nPdl/PyzsCqVee+l/FjskbXvrEylVrNnw6p/ryfabxZbVaED5P6hvEHNmCAUw\ne22oFpIYSPS+qbtlAWRDNnDi32SdFEX7Py5YVB9JJLriKWDhMMZBS0CpoDO7\n+d9oWayjK3pyvK0LtIqX5SibLKKv2b8fqK/MbDwAfYVM05xYhQ5MedYDBEQm\n2YZjH1wg7JS8fgESawuzRk/KnoM4mxbjcsmnWnGEo02oWeoXFl4K9roTtcy6\nA4jiAbX2CxSL09K93r6i++MIwHr/OVKAe65rujrlYUsrU1uIoQGeCKRLZzMu\n03t/SFHDNHyshuZrXClhSzFEMTm3lqL8YFluLJuBJkLfCtOztFbs4XaHqHvX\n7r/ogt8Lu5ogDq1RMMpkqFv2/Fu2oXYi2aayP+eA4NCr9V5PHUEtQScXnqcl\nOcYA\r\n=vglI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.3": {"name": "nodemon", "version": "2.0.3", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.0.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "e9c64df8740ceaef1cb00e1f3da57c0a93ef3714", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.3.tgz", "fileCount": 43, "integrity": "sha512-lLQLPS90Lqwc99IHe0U94rDgvjo+G9I4uEIxRG3evSLROcqQ9hwc0AxlSHKS4T1JW/IMj/7N5mthiN58NL/5kw==", "signatures": [{"sig": "MEUCIGTOuGKbpqwnRPHulHjDk18OuWsX5NiZcnlH9hf1abG7AiEAne2BU6DizNLvQMNkbQ51BjCZFE9rafwxpdDitYNRlt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejh3kCRA9TVsSAnZWagAAJjoP/0fUs5Npbt2lJ7njnbkD\nDEU/lLjP0AoM0bwJ2ru07G/SOtKI0L4m+YmCYMmcTgRCJuEwv+IrlODQY/HC\nohlKnFKmi61SrRU003uN6EF8B8+6avFuEJZ2ZpjX0vhImjid6JZCQxXQZAW+\nsWqzWT313dbg1Zo8x7PLRqotll9twLLdnh1IIQdNi8lOzS/ec64fgsu/XsIk\nsGypJnHuvZwy7HGjFNU7KtZZ1vCgGHpEW3n6UQkyDOZ6SpbPTrWfQ1Zfcrmx\nQ+FRRuk6sBpAUiEjGUPwGdNX86BOCqMpGkulyh86hk53ODtcOpVHBphAH9lj\n06Hazr0MaWPGj3VEpbTC35njExQZlZlFIYL7LpnTqQPmwrqCdU3wuHjysL/z\nEv+dIyVREqwyTr5ukScpGKdUPv6pYlOJNUywuwE0uxT1zBhLGxRP+Teubb0M\nHd4t7vUq0GIyVlvv3N4nCcWbaWCsaseYOIOAab7NK4Ibgb16tnNuZfQk/OJL\noktR9vUlUa6UddD4nxfDg3tnByvE3kT/bYSmHrjEt854k3cssDvhrMXyni/C\nG3tz2u++VAjQaDlKyurSaWSu2MtJqhitLjdCWsfSfxBId3ImeG/Q3YANxjTZ\n7aKuCIAypeaDW3Nx47j6WPYB1c0GcOGOL2qz/DsMKFaoBo5iUArmzQVKZiA4\nAl0N\r\n=AlVc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "hasInstallScript": true}, "2.0.4": {"name": "nodemon", "version": "2.0.4", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.2", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.0.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "55b09319eb488d6394aa9818148c0c2d1c04c416", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.4.tgz", "fileCount": 43, "integrity": "sha512-<PERSON><PERSON>+hIfTmaS28Zjv1BM552oQ3dbwPqI4+zI0SLgq+wpJhSyqgYude/aZa/3i31VCQWMfXJVxvu86abcam3uQ==", "signatures": [{"sig": "MEUCIQD+hL00rQCf6XpBjWJ6rUgTQf9IxfGwSIVj++fhZUBTqgIgbblOmG5Ki8avNKOaNYNqkn95coShqNdR3F924wA8sJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevSxyCRA9TVsSAnZWagAAsrYP/RmKYGfPvrS79Oy4YaHd\nkxCHclSY67Z5Yanh086BAMAZ04CMWt6YiAzXQBxX5y/YzstxVlheliNTLe6E\nXZwqCzI4EO51FgV4q0qIdtEsqErLxicrDwHvHk8X71OHWfhwlOBQJ/yLxwQv\nyvlM/TuOF/b8OTG5S+rdHa8PNcAMKiXfhszbWL2EyZJjQ0mISDsQwDzzOo6l\nDKURH5b7T5QVDaP60rfGjVjkNR6eUtwZHZiGuWio3hMCpMOdjNqc7e5lQBzP\nCu2xqNxpWPO9Mh2pTfvNrq4HxRyDQsfqn3CCQ79RLj/vuaq8moKA2VmKYFuP\nNy+Yb4XP68KqiXFBnCly+UcVS4/Sibzw3Yen8jc0GWMCg09az7zxI6j50kZS\nMtvfV54nyQJ59ZICa78yUXSeNQos/8g5Fma2yqWReV+08Y7NlI33lQN+FKvu\n+/FJtzlFahV8N9hID79gStYf+cnd/9eCJC6i8ecubE1Sj02YQ1deNydN6D+j\nAsgpwPRBrxuE8GmWgkj7x1Z500eXF7cisMd/lEqO3DHwpyoG8pZrZCozgSzJ\nTlkFB3lIi52Gw2MGKfKWpp9ZZxE2wh1ZP1AzVwadJY8sDobCNrL5/sPY3kBw\nUmhrLBYl7EFQTLoyQs1y4I0po8zcb1zkF6vY8OrzeeZH8dyZXqFSn0zoIFEf\nVmrt\r\n=osFK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.5": {"name": "nodemon", "version": "2.0.5", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"jscs": "^3.0.7", "async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "df67fe1fd1312ddb0c1e393ae2cf55aacdcec2f3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.5.tgz", "fileCount": 43, "integrity": "sha512-6/jqtZvJdk092pVnD2AIH19KQ9GQZAKOZVy/yT1ueL6aoV+Ix7a1lVZStXzvEh0fP4zE41DDWlkVoHjR6WlozA==", "signatures": [{"sig": "MEUCIQD4k2MuCpt8F5tW8BTA5mQS7ZO4cf37KjFtI6KfDdXLRAIgW7Srr6O7mZhmvzOyyX3navcUOwP0tPLykAKS+aiK+Zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhdyxCRA9TVsSAnZWagAAJO0QAI+WCNlXS4dL3Mp+zxlf\nXy+wiBGsGLROvISJZK54RSFkfaxUoMmm3WEvR93pXSVULcfy3w51ttDNPbHT\ntfKAgkdwH46y8uP/pOUpYcAoQsxm3OprJGooFYwk9NxL1IDWbmWwIoAhUiHe\n7e6wUbHjmBSKlWnHQL7DTdY5BksRTRQ72dW/tapmtlYF6Y2unFq12gLgQxns\nn2ZVf/S2j2tJQ44y3mfjp/pWv1HIw5iPwbcQQLOuqbeyWhOJkyU70E1FowSP\nlakyi+Pva8hS7m5OPEYKZLJJKRlvC7GwjzLRgz9eCLrAqbULjei72tzjh1YS\nOjcLQZnp0OB77zMDWVf6Yk0NwNBlPFTJvvpB/FMeLcjkVq9E7lEokNnQsKkv\nW703R2tKwBs3q4M5Oprip1eiaHWt0NFJoLQviMzz+LPcW4g0h+QS6GzVhNBd\nqjiyLsMumxDfa5kgSD9b99Kjufgt/AIWZwBkDzxwXwVfdsqpj5JAWmoBTAlv\nyWMFX2xOy0WQuYrzazRS4HMD5p908xpgKBSDC5A1x20xQAlF6EPPetmHWClM\nypFYGex464pej0HtB5wrLQqeicE4c/oycLlxNPUAgKVenp0zaDXAUT9tr5k2\noVJkhThFe+jStnoPOrkpx3Os24+W9/Gce+n5hgw0WKNiQCJVj0VnOo/h129W\nJ4Gu\r\n=jAcU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.6": {"name": "nodemon", "version": "2.0.6", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^3.1.3", "semantic-release": "^8.2.3", "@commitlint/config-angular": "^3.1.1"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "1abe1937b463aaf62f0d52e2b7eaadf28cc2240d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.6.tgz", "fileCount": 43, "integrity": "sha512-4I3YDSKXg6ltYpcnZeHompqac4E6JeAMpGm8tJnB9Y3T0ehasLa4139dJOcCrB93HHrUMsCrKtoAlXTqT5n4AQ==", "signatures": [{"sig": "MEUCIQColQHZmfKrgWNmJ3c/3DspAoCOhHhEwUOY6dvl97qafgIgTvJBvj4GLf82TkTnmns9LQap/mf384UuWRLH/NfSnIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjd2jCRA9TVsSAnZWagAAri8P/2aOfDxrnb4L2v3SJIM5\njT2PErhEXzy5/FICPqj3o2mKlMYfxyTomtaYPplGynvhPax2dfmqPjnfP60B\n1+dNtB3Vm1WuEo1Ju7lgpxxHrI6jCGa93GQztQa+uNdKPNVDdWTLxD31ONso\ni9s7Ykil5U1/IQV6EfaexCFLyxyxlDK1fgbR0lvfk7OLBv3C+B0t3z7Oti0K\n4xCzpoV2k+Njd+801YMi8R2fqhcwMSB/WAIJ4g1o12aOD/tmRKDw63S+wwVk\nFj6iskfzozqXsKyBCEZbumWCm9M/xhhWAm0JEFooqYnITi9WXJNn+/k1re4m\nMC2IYJPVe7T7/dVChXlhyI9uCyMkm06W0/Y7z5E7IhJkaxWhKbfMUwulczLG\nuu1bhNUQVJ9J0mD6rjCPD71C/qMhD3m+0c/0tJdwyNCBRJ0nL/NFTtFGGTPZ\njL0yJMMETuebhjP6CJjav9v5Ja++kRv/7BLxKIKT5Dx0kBUwFH9ATttjyT1G\nQCV5PLpPGoMsRSU3z0NE2KpPkF8TahISerSE7CupRT2kfhZtIlJsmsP6wpA6\noyvTl5luFA51DXfOJ6zoLMigAb1HuBFo5QLX8cLgX+rpXrE9FZk/AoqS2mo/\nC5f50iMhwVdxJk47amRvd1ZY4S4MQrzfjTapHTs6RQCqzPh2ogedcCpuhH2G\n6eNR\r\n=mo7q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.7": {"name": "nodemon", "version": "2.0.7", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^8.2.3", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "6f030a0a0ebe3ea1ba2a38f71bf9bab4841ced32", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.7.tgz", "fileCount": 43, "integrity": "sha512-XHzK69Awgnec9UzHr1kc8EomQh4sjTQ8oRf8TsGrSmHDx9/UmiGG9E/mM3BuTfNeFwdNBvrqQq/RHL0xIeyFOA==", "signatures": [{"sig": "MEQCIAdD6aFQ/wMHscTweIfvpGxf1PeRviNZPHnym1RkvrhZAiAqUTmXSDn+JKC1qONjuF3a9J81ZeabEh5HFHjFGbUY1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9dF0CRA9TVsSAnZWagAAtB4P/jqtLdpHDJxnj+/FiZE3\nmEXKaQf/4Zl6eEl/qoURA/Y70qDwbWzMJDsDadc5St86Pwg0JPSXRyJAQRj5\nbHurTv+0bCsk+r1m3YxI/TKXZ6/n0x7L5dhxZhnqLI8z70ulX7k1NbIWXwMM\nJ/lU/ns6ZMcn5fofLyx1vuNqKfJNMOQk0KFvid8mqFQV0o+GiDhjUncZcHLY\ndaaMdOWIInIijEMYJH04aUZ2ZaAGykbNCiHfuvFNuUZQG/lPhiOjIdQQoVhf\nc7di7DaWV2YGNqLCrL11mUYsk3URVSpQj+bYzisn+2pW1lK7yEe5F/LDbpOq\nk8KiBs8JWBNag974N8n6k75Oz81bqsg2zVCRAKrbWmJ3a/+BO2bi1K+Oz1nL\no8LjYdkSVH8DGuIueeLuO9ZJafim5omHVgukeLiW5shm3Vn1lQ9g1QdEuh+b\nDKaR1H6cJ4I+I9pYSCMOOrYOs3QWvRUUgEtOCXsyQcfC3uW2ocuPhOBDeZ23\n+C3s4twCf1hK0h5EImEBCrIdz15kk/SUWA2JG0y+3C8HmayTP8txvEWE/xqy\nixflG1ZHbBUTIVH4CIX7PHnu2Ei/5EqoXvgZFxjxqEuLo0qX68FFEJppoVVq\n/TD0FNnnNuDViWebR37w+vpSDZK/emsTMO6jKcLRXMB2SZlxbWjhTMzS2Kg+\nXeal\r\n=Lk7K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.8-alpha.a": {"name": "nodemon", "version": "2.0.8-alpha.a", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^8.2.3", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "a5eb845ea373b8054def4678d5a46eec79ddc70f", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.8-alpha.a.tgz", "fileCount": 45, "integrity": "sha512-lErxdLvKwEIUy4HoyeyKgkJN6fMbKXnogDckq6YcQIu/FAW5FFg/yLChTMTxqpHJZSPR5chUqZ09VXrqYhHKJg==", "signatures": [{"sig": "MEQCIH+FirsqI346jhrs9BYp3ZwrpmeEMKOGcAaP5oDSf+GNAiBESPtr8CuUm5KeLQi6TDG4rmdE/r/1087ceGlkSFe8lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 190495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgko5UCRA9TVsSAnZWagAArYwP/iUoNcy3zikkQ9nARYgd\njsbxBkp/kNuMwurDrpDAFYUPXMi+vHDz5zbjijUiZQfe7+kKdNUja+MZDhr+\n3Zsm/xsSRqrvmJIdBl0VHDoXj2GzfZNLJVlKcr435sS7kA/T99+/8cpn5ReO\nZZZYAeZcUZ9WTVuDLAxfjNS7uaZPJcJuLFSTRqm1o/10WtyYPmzk0lTVJYGU\nh1eLSH4DY6eH4EF/0ZfNbtWmobUAkQta2Jf7HtlQoPF/gLsUWhJ6Czl5L9ov\naTkC4S5mhLeMolrbPHYZeSy6DWYSz+OUB9a7U0VXfEzNCxx99/qPL3H+4o58\n9HH3iWyfD21CipQgjCP/EBMpR7CzusSDaZNwhRzbCoQZ//KMMq6RKpaha2IK\n2c9iGG6zANbnht/nORNvvBtZH+PSP2D83lF3zr+41u2neUgrS3c56eFeP33L\ntRvOGBMEwkoM9bcUKHJla9vMJdMbfZR5/rI+pfb91Via6bJHhAw0w32J5ZB/\nwImzKftqJYJ6LK1JYZTXOphB7/uaAw0hUwLgDU03pXLy3OR9UyvcxIew+QLF\nv+bUMhn5ivMTG4m2vFL0rmjlArUocrAHM29K4VomtYzZsRQGVSvsYszGgq7s\nxeA/qVeWBU4/kBdxou3fwrwsWFqsjEzl5adNfP7Sn4Nvl/MEt0neApgqZZN4\nhr8h\r\n=+I3N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.8": {"name": "nodemon", "version": "2.0.8", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "95719984d4ae57c2b5ce2a1e1bf1ca14de36f500", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.8.tgz", "fileCount": 44, "integrity": "sha512-omq5AbNSMZjjZf12qd5vRpVobbuBJsLhTOS/vwy5ApnO6jpHbS/YuN97X4/zzB9YkdAa3q73HG5IuyM6kIQw8Q==", "signatures": [{"sig": "MEYCIQCMWIHnRCtHsY9/ZCryp3zFrkybpxA+o+TDItUQRebKhAIhAL+SIbSu9R8FJE7v3NfvyNL0d1FJnLFxjWSBsnoaH2D1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2xenCRA9TVsSAnZWagAAIqQP/jemz7t04YDaTmncTw9c\nnQBqrQHKk0h/3h0boO+KMyFxCBpp4SV/srtO0E7pX1mRKgyj0M7p+HGTNdux\nIxdILYRUoSc8fyZdCo6H7uo9gz1ErkM//34U8TAMFN1eUv52HIMI/MOApjCc\neAu6Ktz7lXWTyY1dIO1Z/PHjIA8aa6xObe9s0SPq260dThiA+iLl/87nXlTY\nTIDycxyx/QtBVxVALv6d/YzknEWb9ZL0uTTJJxL0LjkIRBVr82+b42Yq8hyM\n9oLauocqLoryn1V9mpwBYQoKW8uIW9p9vWkFruSpfzHoa6Tv6r+6NLS9Kk3Y\ngu+E7Kh0yW5IgVrvindQdtFugqldiXZs4IDsa2vIPoMT0/KXzcwcMS/qHJeM\n08Yg2aTbGIZODTFExQQD1WaLu/n1yTcXoDDDTEdXExdSBKP4eZQ7fBjl7Aex\nxTnCert6Uqg0eKLC3j6eO4BKLnLT8sDIDcjwly8V65IHiQ4bGSL11MrY7/kT\nPme2oT8jUOdxvnWwzo7YduaKSFQVLYuz+6wkRB+Bpeg7q5cYVNnOo/mZNjIy\nXZOLLlBOrdX09dvg7cPJEaqygQeASPDGFT5yU5If+mwhgAtjlSGUnnjQk59K\nB8Touns7zPRMKDmt8quAqc4lQ3U1tsaxL5oUm3keHxkC5KksamP3yAQdmMmB\nzVVv\r\n=ddge\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.8-alpha.1": {"name": "nodemon", "version": "2.0.8-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "e09c884a14b80f879a13328cc2d4162114b1d071", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.8-alpha.1.tgz", "fileCount": 45, "integrity": "sha512-FloyjYw4qcE4GlZN9l4eezxa4PKSBoT+H4xijNXVdzWIDEahxQYWjo8KZZaB1WAzHjr39xBva5ePXEBZh/jG3A==", "signatures": [{"sig": "MEUCIBjVIEkJUWiDP0OoLVqPgFeLt0bfKTJGFAg2hK+gma1RAiEAjNqfeiOQcqNEG8uRBDh5kLdUoWryHEga5upZ8fo9lpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3EeXCRA9TVsSAnZWagAAvt8P/3ZSJ1zRSIQnzg95Ytcd\nCJ6O/HimaBhFhgPCVO2e/UMHX7Mcs9XuzXpFHg9nt76WNm8eLn1vxT6wJIdp\nI3Gyjos7aZ7I5Poz8m9+XmYUfaMwcKef2mJcVi7mmYClSI56CCM7oQyx4TH9\n/zEyx9w4SbW4JUsY9QwlRh3qJO0NxpdcvB75ZFAd2iARsBH32qZz+fjNIyQL\np99IQXnr0cxtgSJDagR00VOhSMAzeGqrL2DVN4JLDcTKeRZeIa4fUunnukBn\n5TXH8kTTjq9zXsnVGf0wNfBeIiPzw+9CsU2RtM7Ye3+iYxk/79tidWluEBx9\n7sygChkZsUndK/g5H0HkcE2ISw3guol14kL0RqvxOX7aght21NUTOnudjL6F\nl/v8QEzADUOm1+QJzBPWw+tO6Ys8xCUfpeeuyZ31RjZ7up+olz6USo6toomw\n4/xxNLPfSl5q3mx2PNRIqljYgjI5FfRL+dCfGx4MJSDNxPgPxzaSwEsWy3Wl\nAWOJFjKs7xi95g5wHRHQWHpI84rL2PYvB5VWZwvm/5gZsWoHjij2CBzmlPgf\nQnN80E5QcIuJWNWAiSTtxeUyJvz2Hl3W43ly5zG7opi3hfd8cs3LV3redCKC\nQ8DNHt2wbi8UeHyCbV3c/J7HW+E1ttdQYu7NKIBKYJ4dB/CU7xAa8hWtjg9W\nJF4z\r\n=Z+DO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.9": {"name": "nodemon", "version": "2.0.9", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "f5cd550ba215da3c1081459ae2c1c8da2c353165", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.9.tgz", "fileCount": 44, "integrity": "sha512-6O4k7C8f2HQArGpaPBOqGGddjzDLQAqCYmq3tKMeNIbz7Is/hOphMHy2dcY10sSq5wl3cqyn9Iz+Ep2j51JOLg==", "signatures": [{"sig": "MEUCIFwLUJm90FA3XuaTs7PqXei1rfJ2fw7k9vwNvhsMHanXAiEA/7YAC+dBMKKchB+U+QKL/3WoVX/TS5WDxtjgpSY2qxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3GpaCRA9TVsSAnZWagAAOM0P/jZqsOj+ozV5eOjsMrNS\nBS9TALZffG7+3WbR4ux9PylkmfKCEoBEoy4LAXE9G2Z22m2gam0p5KzA9NP7\nQBd/7UCzPZhSL2o51mXWe4rtA6HQ7llTMWQoaX/y4WD+TTGWDew6kRCyx1Iq\nKkjardienkv6ijlIXRQKi3WEMi/So26t/xQc5IRUKaanXo9HlYz3JlcjGuJ/\nYUZmx5o0pzgD38SUhTc9n60o+MVj+QIQ5JmnzcG8N+z1m/tufYX0irZp++8u\nR3a6FT7slV0iP5RN3MsLtl5ZPjENnMDPcE6I9ixY4gBwiqszq2p/DAqgpheD\nBlROBwAGxen8mEe/FMhwCWY4exPHPQh1+P8Ro5Pf1TY1qEnlcTJ/PQQBQJ+p\npLPtn8jKh7tzab5j/rN7ksXPF4Ivhle0XmqSWBtkc5BLmIHbAIZlPWMpmRTW\nDXtwbJMwMuDoFgyW1MzXHDoEMZHfUuS9Bkstkn5uBph11Y8COc7dvv5Aj51E\nMCo5x45SgsYgBkAwAFYs+qo7tjfEFf0JPTufNMYYQQrt4Vj92DKKLR4uTK8V\nhUTM+YSE32zH+vT0wDFYWmReyrY1z232IZFD9Z4KLvJpfcS1eey2hIU/9qWe\n8dawUhPXguHdZ34jbWb+vLo8CjrYYMPcDJ4bFfk90EzPnBvZbJbuauAlA60g\niBAY\r\n=YD8v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.10-alpha.1": {"name": "nodemon", "version": "2.0.10-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "409984210d99c4237fb8989211dbc905c457bc47", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.10-alpha.1.tgz", "fileCount": 45, "integrity": "sha512-+TyTKQcYFq9gATtiuljDgqFunRRjMxv0AuwI0cUWFNW13qDXBobH2+VXJOqTRCZlNpClxmovPMv1AAQyqBY15g==", "signatures": [{"sig": "MEYCIQDHM9G5wuDLWU1sSASsc+8aW/3DnIJoUKcAcOSp5RRA+gIhANWZDQTRbU0n2dNnRkBxuqXvIJg7Ksrydk0S94BVbYJP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3wHFCRA9TVsSAnZWagAAN5IP/3k4Uwx3ia7ey2Br/iYN\nFq0Tv9pARy5GN1ILdqDHcoUFWzFfoN47z4co8lLvtTcu7grC7FT+opbvO7C6\nKzH6D7jpb9HSDDBwQHLLGYmwtAV/aB/Ljmh7ralWuiG1wktEn8heWLHPCdxZ\nr/wmaiSaOeHVj4/uITB2QjBqYO8CoZE84h/oX9jVfv7Kb1n9BPctzAJEg7z5\nqcIA/X/oKxPcw45m2x8jND5rDy5pcnWOfCzl1w0l8MozFJGFvA6RYZvnuvMu\nsGm46zqGmU60Xsi+jVYcoER2iG38Hqv3uEAWlOpSkHbKQLM8z3/KvlVcKgqW\nnBpxWbTrl5i5gRe7GA+E69ahixJ+pBgnVxRbt6/LNT4KknC/rXrWt/DWnNn5\ndJzqNka37FgcSzHO1aDyfBb5OqmsVFTX3bf9uQWgVL83hyqnQmRG1ivbC3+i\nfpmhsKtHJ2vikCucauQKy7VF8pBw3D4Mx+BAGlIOWWhphBkJ6Cqwyu2y75lP\n4a2kP2cjKKYKT7FzeOWS130rk/1F1wQwESdG7yN5AJn09wEO7f1g6n2ap8Q1\nKOd18BtyOGfBiqzEekLXq/Lzl285iFynEgVNR2jvoVrnuxiik6VMd0Izb9SP\nf4VH2R4smnaKv/3IEcW3xwH2+8XPNbEV6vjSVuybpSeh8CmeMMnVg2LHsRcI\nf69v\r\n=LzOh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.10-alpha.2": {"name": "nodemon", "version": "2.0.10-alpha.2", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "c96c183483088ff53ed367915df1c343e9593359", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.10-alpha.2.tgz", "fileCount": 45, "integrity": "sha512-c0iVaLXYpPdnxhjo6B22QWQ1L9AG25R9wXblzbrE0vQShERS9LhdB2YaXy2cC0yX3nScSxm2Csc9eOGTW2b1mg==", "signatures": [{"sig": "MEUCIQCi0BIENsuWtnLEhjm0zA7OfIbdbaFFjSkOdnhY/xvY9AIgJ3bvJTJJLbkHpGapdALDfjfta+7aoXrzPIsCmmRqG1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5YSdCRA9TVsSAnZWagAAshIP/25wfAkV/mFScZ0EZ050\n9+klZEXZgXNBa+FJ5/Sjaefgk6HQDhYDPeJ5mL4lcLgvmuyedFIbxailT3zn\nMXnv/eGOm13vqQaQ/s9Lkm+Z5xLK6AjenSFlsUQc3A+sCojymmXu6LQCJeNO\nlaw3oP+4MLEedEfrnFoSBB4r5f0Uj+PRJk+w3BFpJBzwK/vrvrzlzs+1udIL\nbzeCIm7Vk2hS/Og9CDpvyoXxZHir20y1JFyZ1lWWMuJv67rpEhL10Mn+eLXh\nclv3/ykEqsfUkLrxMhqEWetDTfUf1O9MFmyr1V72JCQyN4SFgHlxGPpM6L9P\njMBfYjHE9rjWF73Pzfm+UxuJNsGtlla3IjX0DGzwCz4gAnLtLAfpZRtDJ7oW\nIp7XaCfFroxYf607lqgtsOGBGfWbf/SFwFWORHf46tcQdPywQrbRsJMTUi0j\n81vHBlnSn1ufC5f2w2Cjn9joTaprpKO8N1bywwElLxY5wwRNcNMFdrYuabRa\nBgjYGsZlYqbqqCshBOholiF+BMMX6ZOPoVCE0be/KrejpJhDU0yfQCb2Xhbf\na2ohLxtrVLwsCttbywsq6MVshdjLxKrt4vOT2nRC1bnah4kuK4DDRMtHfOrV\nJujKW2xwmKm1xsvpv5zsoxk1xPdABi+dBTodw29AdESBryhT1z3H32wwizhi\nLwu+\r\n=/TzM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.10": {"name": "nodemon", "version": "2.0.10", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "abda912860e5c1158ce0e1f13e0fe43b36b0ccb3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.10.tgz", "fileCount": 44, "integrity": "sha512-369KB2EC1fLzz7hIuKSRSIVhh9PXqFAwh1stxlNX8DMyat9y/maswuRxRMttyelnduLDa04r4wgVZ4fgRwZWuQ==", "signatures": [{"sig": "MEQCIFI3FTOr0cX/fn6fiL8/mN13i48w80cveel2d8/bBRWaAiB/+kI/rPgNgfKvxJSadyBPItAY7pE5BQt/91QpFJAcbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5xBVCRA9TVsSAnZWagAA2w4P/ReNJxBCze1fHfQIC7+d\nkGx+/5Mw2Z2Fl/TFahGY1ephb0peWJ5L5g/7nUWlOMthAk+XVUW0Ry9Q8Ynr\nGRLc5oqz/J39FGj76H/PCXLdzzvcM+lf1x+8QgmullUpL6RBDj67ez5ChaSE\nl0SLxo5jJIqTPkHhK6vFS1Ey0PMAwrX+j6O7+rHfzoY0LovFUs4Y8+p9Mde6\nXz9cKgo28vRO8V6Mt05ffzLwcdwji1sw1ktoD5x51vPrGaxpOzoOkA/cPsbB\n5373mDyrmuXMJQv4bivg/QpO+r/v0X3js8wfUHbUOtbzCcg9EGfmSpn10Z1Y\nIPkTMKDDA9S6K6SSHiMakE97TwjGFtW5Hod9sASFQVgp9xFRQF5369NyPNoQ\nEI1MVsVDsQZDy4QMqTFODhdqKubi1Dr6NCEtSS5eDiTeM/GxGAP0wensUQcd\n/z2WAi5XEs0iktROpiGrZ0O9t7rrIijXdlyThgA1UOTCZI8TbDxLvIzcEivd\nO+3XaBqWKObCFrzxjz2CQ51GIKz67ihiehm+F+t/vVUyi2LGMSYDsjGGcTYU\nV/NLB/jn/1klntr+nz+gNqygmCjsvjqC2BBEYNCAV+BYF8uaMhlI7avXTS7S\nrthKf0FSuJ/KVs8B/ErtmjukD1EpIFSh5zHFlroqr51BQDsmKBPjdGQxuDRF\nIgGn\r\n=rc/c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.11-alpha.1": {"name": "nodemon", "version": "2.0.11-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "d95629d51776f8a4cc42530145e3affcbe6cb602", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.11-alpha.1.tgz", "fileCount": 45, "integrity": "sha512-ZMRhlJ16utza2df4ZCI0Avp+DggcutUkGUBbg8sFv9sga3SF5y/dKbMfYBIiZxzRuuWwf8aqy1jqEmwtpKRliw==", "signatures": [{"sig": "MEYCIQDAu6ElwqZATZAbh6EWVlVYUKlKZl/ZH5LhuELq01EF6wIhANsYzIXS7GTgH9LMwmYpDlDVc2GUz8xsakTLxZsxleje", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6B4tCRA9TVsSAnZWagAAkT0QAIrya76eVN5q9Pjs6hFB\nZi7V4rrgH3vGaP3qp16c/6nSNJiRL8MnbKfu71yZpDGT30XPxNsVjVx60uKH\nyvRsTode1weeeMQApH3Y/OougSiAUlIUpnMP1id39i405AEq1epyz18XjCbv\nT1HW8/8nyFl3Sku7ncZb1pIad0CPJtwK9FCobLnrn6ofeXAbQ0fD9aPJoFSr\nG8LfrmU6sFbaXlcERZtR30Ky2FhhvGx9lhGbG56ia8lgbvK4sXV/by+CGRax\n3Z2zLvcV+AMVrGPPKZkPyC4LFSWgyhcUAV4vmq4j4p7To/zMkMFxmUiOa/lg\n/J+8blFRLeDNsbVfsirT8P/Zhzxitdx/VtzsuQsEuKjOowSqHBWDIZc6HxuM\nj5/GIBrEyoKOIVTcJETMUXaENdh4nqH8zDlCrXDpFeTYfoZj5WZl6tggjrea\n2TOdt8v31DyDvjXg10tZWniBZ6c+6OTQWtU61naJ4grccjyMb+Bv7lYJELll\nEuuHXHkDt7zNm46J5xvSWbjEQxzxB4fE9kGlxZ6UFuvN+tzOMrmQ5ch7rRvG\njezUQeYYK4p3XfM1jspZojPVRLlTZTJ8IbDEq1h0UBiCnu5HJx9FqRl9qg2C\nhBVaSle4oe9piOwU4AGb42FAb6Pt47OWMd2g8eDhQO6bvPyuhXOAgyxJnSEF\n1KiD\r\n=fe61\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.11": {"name": "nodemon", "version": "2.0.11", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "957f2f28d7d255402eb46e6ec722edceb6e8e8cf", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.11.tgz", "fileCount": 44, "integrity": "sha512-V9UizUMs7hM63YC+e+26FC4iTqEA1GJsrM8C7DiNyPvYBOG/QE169kMIe+sH7FSe8YteMQpaKkUDwfAF83+kEQ==", "signatures": [{"sig": "MEYCIQDTx92vCxLGLXXolPjU9y9GwIBQgPZP3QpL2ZU3cu+u7AIhAI2bjHFkvODJ/PKoyNJqgbQ4vPupwa1JEdLj7uMEkzXx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6HdlCRA9TVsSAnZWagAARYMQAJc1JHvxpWS0EPMYhyyu\niE++jMtRSi/pLfP+FKsPdSr6kmof72WFQrE+A7OdNWYripfvJQ2we4ZKEuPy\nHcbFufw/n5XYQh1wZCqkI2i/6Rk5t9zejIRe6SWf60LJTsMAYssP552KU/7j\nw3dS52qaoYYzgeSVmBQCA6GomGHJ/ObFGQvD+awFAglMyDxLxl0Xor1JOmHL\njlXMpNSW4/7w1ig/5i0hf8U+ugT2ApoFvbiGc1EsWaBVQ0N1rQxUfjEidM2A\nBdkZk1DMgYO6LHeL8S91fZuRM54PMgoNV2WVHYtH9Lzcjhk2JZFs6u3NM+9+\njWmN+AJf55YauAeZ0qUOTiHwqHyYEraQK+LQO5F5Yv5jOkxx+mb1FjefUIL0\nCIxYmGGW5CTYoAtb+RXwnQrMY2Q09tL13Iokj1phuDSvlHHtBbdunyRjWTVC\njgh76qBbYg03rQPzr3/W2+vR61ovVtvQuwQu6YYxVc5iUKmmyoLeH1dy+Kzd\nAdgWvxpZFNbLNaHc2x49iYYvz+HbwwRqsxItKSC/mXYFItqcaj0rOHLCaIVr\n8RdjF6m8LOnm+BfvlNf8Frohn5RXiqySswebrHrxZyC+6YkmYr8v2fu8HWSE\nPzauUHSOLuGx0YTn0Kj7zIs1RE1OCs64uDBJMTUgtfrt3LUJo2rP6mKDnwPf\nHZbE\r\n=IFc7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.12-alpha.1": {"name": "nodemon", "version": "2.0.12-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "0e6f3d6f056b524e1060245b71a1e8b84e9c9505", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.12-alpha.1.tgz", "fileCount": 45, "integrity": "sha512-M2bBgGC5iz6uyH+M6gsEnR7j/kai6h4Bnm5mmROxjtIgC8Fb/xFvM6MaMn1PY5vIxMM5ETiUTEhcHCO1Hj2hAA==", "signatures": [{"sig": "MEQCIHkfau18M3YkvfD9zH8Vn+oQufpwBdcgn9sV1CVeFdYcAiAYkv/dknPY0v8VYHAVhdfMrYTbeEP8VgoaFfIgMgK60Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6bOCCRA9TVsSAnZWagAAsuwP/2HlkPElthLS6NnMmG7i\nhAt/ugGnCe51CzFItUWFUsgoFp8pNsJSBbvs6eD3yqYm+gprd6PTkPT8q97o\nhgFq7A8ynJWVWoaUrl8VA2vU9htfHonCAprcgAGZPMeaTDj+mNqBww8brlnG\nhOI3xK6QWeYnMzU2Bx7F3X1gonzIuSKXaCT7VZ+spKbMa0PrM1AgVWsBDHPH\nQm2qYESDYFTq1V/CHDSJtcBVaLTCipMv5JDNsROngEsododCFaxT6ccOeFiD\nE4cBSB5WmHNIvtNjvjdY3Odn+oIz3zjbxzmYWyMWBtgayMeWQSUWWISodr1f\n9ozvR/BV12NlXH7yOa2YjinjwCrSAJPNu0N4NydzB0jA91yGUHHM5AT0BArG\nqMwPIBwG542SSuO9uN7we44Kw8AGJr0sLpiVpXn3NXMiYw6frPgc4oDnlCIx\nb5MtPKW2/3MFveu4rA9LZZJ/KMxCy35qo/myyo7wY2U7DYI9itE29mfNLui6\nNgZ1YRin2RhKnP7I3gJvhJ5nzsF2cQ5AiOk12G+Eq1+Y3YdTB6QpEJKPEIWu\n8ZnWxsOyF/psRCxCVR5RrocMfoxwWU7swz8o7n8lzd7seH6LrBN3P6UuGZ5+\nUdnpF+RxYOgECo76MKXqG5w1r6uAH8EAf7IZxE8jnRRwHvdotIuobs1hGAfa\nHJns\r\n=mU3y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.12-alpha.2": {"name": "nodemon", "version": "2.0.12-alpha.2", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "40939f6007deccf3da10c174c445dba3b3289647", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.12-alpha.2.tgz", "fileCount": 45, "integrity": "sha512-9a416QiB1OUTkvhFVQsopfxXxz3am0S+mcyARm6/BBa69lvdwTVTiRVJl/peyQ6d2NObeXM/zFGBoweqGdHQ3A==", "signatures": [{"sig": "MEUCIQCXBG9iaVQfe53gSeqpSaeSABHRMU61+vO6wWpgB9PM8wIgPBIul78egApWfgbxMG9bU/AsrP8YcGP2qCbApV+sAY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6bRaCRA9TVsSAnZWagAAEfsQAKGLl9Tbo52tqA9EEVOL\nNDCs/nYK+m+NckQ7IlVUhHAwlsnx1SLHKTShVHthHABUQAyp5YpPDCNaE8nv\n83LSKe8zoSX+1Ue3bPuj+zqRcmVLOAqCeefC1Uvk4GmoXbSNYBJ4tT0yeJj9\nE2ksVZ+DbnJ5KUwDoMpOuUnbnyRlc9RzO9NfHQlVRdgd3wgXgd2OYLCwq0zB\n4TeXC17d9pvDht1/nZMugTNBJcqeRHLR6TajRCrgRiM2F78egmFlNXLcXtgx\nfnIMAzZdEDt9Fbmn7B6zSdxjUIFJ2EZSF93VegeDjDts6G1hZ0hXzO+KjfzT\nNHmBwZulk8RTolvbBKqvh0DLSbXnPZVkyj1XrhHPYXrOF5fSPEMlRgwBFHvE\nlHmh7zi/lu5+M3HNRRKGsJ3UJpaELi/7MPtnb+nA4rR6r5GSVfU+2bvXpCax\nIyzl0iJJWuyQ1eLPxrOdeJuIwteW79NLKknsjSnuxTeYE/qlptN2a2dMpQ1z\nzMM+9hJEIlmzDYjAiOec+pexEcg7COB4oHGdiaejJf2gSqZxiu/p/lQUZXgM\nLTQZCM8dTQBKNUeZiZzvGIh5WtcQdbrxVRymB45+Ql/laD+xJThW/VSqVQVg\n3QA+5XiO5rzTVLzpxgnI1gxAhsEBD45WDtJefEBqlVbq3CRHM8Rxof2k/EfV\nlvBh\r\n=11ke\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.12-alpha.3": {"name": "nodemon", "version": "2.0.12-alpha.3", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "4deabd97a4b61c6d2ad2091bfbaa7b869df4b3b9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.12-alpha.3.tgz", "fileCount": 45, "integrity": "sha512-RLAV6DIrV5U31Dd8Lm9V8wxcnxP3YFw9ja/2qnnFV/uJqEfppdYRCccJB93WfMhU7SQdFbLrLzmrb7RjLf6OnQ==", "signatures": [{"sig": "MEYCIQCrbPy4JrtXhij8qmEYtZcfibEpow4OynWCElIk2XD+HwIhAOQAvJ5MiWxPR3ompJtcPtWwx9BBd0NIsN//iUCMILC3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6bXNCRA9TVsSAnZWagAAEe4P/1hKwu3BRDSTN2IP4BRt\nGJ6RS6hNtjxN2IY2uT/sfwZBrJI4tKRr1Y5WQ4qUy4bWXv81hguIR6ggMLGJ\nbWJlUFWI0nunwpJkG+/kcvWlpOqfMmu88k5Un9R+OwiIqEr7GCM+eo8XGUVz\nOZOPwJBSQqCTG4CNs1VwWXdMAQv/71QkeKkbDpnPWwOD8Yi5SFJ+5QB1GwW1\n5CULonswgVGl6dc0rmbieuGla9UKahqhA1ThzTNYvxM3fPRU/B0gRV8HODen\nLtQMXyvi/xB+dRXx12Tr3SlBgR8wiFYg5nnS2Jk9lerNlgUdJz0CmChAeepz\nsf/3mbipkE9VFmUI9+ywmK5H26SjvIjTcpK1k14+hydx0BHlyErBY9p6CWrA\nwPsGfSXLLYFkA6t4acyzkxhxxah93bpXH4ztTixQCyMGwKbuH9bhBcRE6Cjk\nz6xn00RXnDFZ6FI2IPYjzDImnTniSc29TOyNTqWDpFqGuB8KwDsEmW+kO7NC\nhEpkCSqTDNEHLPdFbnbUszf+u92Q6z2+ZUz2ThgBFFq5v+kf3HuYi49gNaBi\nzdihMGGcEblWdJbT5z4Y97Q30yK9+dLv1I5fMMn5N3nC5SEMd4kRwRpkYQFL\nRG/3+YW2M3Zo/PUg0K8NOJWfh7LBqYPilF4fREsS/m/d4nEKswaLMU+l2O/+\nHbEt\r\n=rHft\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.12": {"name": "nodemon", "version": "2.0.12", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "5dae4e162b617b91f1873b3bfea215dd71e144d5", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.12.tgz", "fileCount": 44, "integrity": "sha512-egCTmNZdObdBxUBw6ZNwvZ/xzk24CKRs5K6d+5zbmrMr7rOpPmfPeF6OxM3DDpaRx331CQRFEktn+wrFFfBSOA==", "signatures": [{"sig": "MEUCIG2WuHFQXZfDEI0kMis81VY5jCg4sG/WAwLOYpavajoAAiEAiLJissqwcVw+qfhzAvlDn3BbEqGWtBjhBERHz3I42d0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6bb0CRA9TVsSAnZWagAAjNcP/3tuO3p2NFEw2YEB5nuZ\nN6R4gG5fayPLvj+sDQ2lnqwCeXHjYZVZooFh362cnkwwhIlsJHlP7SefOwck\nTUujCHJcOaZvoAzEhL0TBkM8vEEr8CsG7FmsKJcQ/5N995lLcoO6iBMTWY2Y\n4Vk4GMYNXFdjecYUdacHTHV26xAU4DIbC9APkd6rMT5rdtiZzhjaSDcOdTsv\nRMduJu5390s5iiO4ZLkM2DopA68xj2qz2BeVVYbrktrNXx3kIgsH7BFJtuJk\ntXaNKa2lVu4fDIc/sYpa9cxYP35mt/5RvITFQiZ4OWM9Qpwy9OVwoCmCiMQ/\nHspQSJyxlKTzd9oxKwCv9V714IyxALkDDJowNGfYtFmzJrPo7FSuHh4nBy86\nYGotKKPdaFirQi/1VIDasGCFwgYjIOD3A34yOoQmf1U0ybZkIpnppfDOCp6Y\nRfbu8tA9u+omtdhrvdQ6pgkHDFGwWB8rJk7zAgN9qfRmBy+03cAmBY3Y0xyG\neYRHnO8UaTKQhundgBKrCVqOEIGQ/QWy8bPnvNcl0PeA89GtehUYzeR01mjq\nkE49kv+1+vfSUjf790n1jt/prajq9bZNSgBdf5+E9aJFqytp9fCovYxnAyq/\n/d4sPr58Wh1JurZggEqGs7/CmJCOlvnKioS4N0GYiUCNts2e5EQPtC8TuN3G\noZt0\r\n=taCP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.13-alpha.1": {"name": "nodemon", "version": "2.0.13-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^4.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"async": "1.4.2", "husky": "^0.14.3", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "istanbul": "^0.4.5", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^17.4.4", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "fe344f07a986c99eb51b23181d554741950b9b8b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.13-alpha.1.tgz", "fileCount": 45, "integrity": "sha512-RrcqMIEKOFDjyX+/6KD21DotjovKuBtmWZDlnnE8SvXGwiaIZ0Cwyd0N2gNnizWLxHQOhhnp6iV0MFcs6Uq7+A==", "signatures": [{"sig": "MEYCIQCJqLpGXPcwU3vYnXmAnQiNF270Iq1cxe53yUCHkkNccAIhALBjPa4tA1SiEKn6HTZYlAojJnhhP0kxjRHfT/WZENIc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/98ECRA9TVsSAnZWagAAPPoP/jfcDXmwFJ/O8SlpOikn\nMqxRrvQ9Ffc9rVrdQcNiikH4YthUnd5luP9zsuSCFqJ5rytsbtMxK+4Vp25p\nRVd+isQIKI5nAg/vwMETyyxq+iRVAgz5wpLs38chUdhFtXU/orEIlDLuUR7O\nPpIe2Zosc+f00lHWNCYYvKbp3wX0SUPTUpAhPYYpQIdSFdxUbckYqtidLi3P\nFF3xlxUi6rYTZpOcQ3w+Qkz1DwsGES8x5hfpxeLD6uvDAEeV+d9vmLMJjRgp\nIfilXsmwz9GeP6dS80KtJt6TP6U3VqmKYz1Ps2g79h1/VZRulSSpbQBDQ2uN\nEGc/ch7PRDEflu0QxeLzZXz812829sfVIdTM8qTejZ10Ru9dEHR8f51prAGO\n8rzB5Ct9gP0nKDwDsZt0yE8+xXGCaNluFGLvyNRzaxS4fAD1kQ+u+zPdsWdK\nQ64YNocBlQBjiseWPHPsuoP0T8J1Vs+CDQpawVb09smWBFEt8uShbc4OLD4Z\nza0h0tCHFNdlTWZU2UCszr0CJiBNamAvpd3LIB02Mete1+/PlsTDV6SkTIQe\nvUBUguBcLsOfAxIrqJvYu+RkcYJVdBe8i7bKRGrTRfNZ0SW6cKWsxI5CVfDJ\nMfXTi2RpPNyXRkKor4VRVk2JkzOzGnG70n3BUPluEwYphhEfYZTAXZJd4KNb\nzWZz\r\n=lj4k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.13": {"name": "nodemon", "version": "2.0.13", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^5.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.2", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "67d40d3a4d5bd840aa785c56587269cfcf5d24aa", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.13.tgz", "fileCount": 45, "integrity": "sha512-UMXMpsZsv1UXUttCn6gv8eQPhn6DR4BW+txnL3IN5IHqrCwcrT/yWHfL35UsClGXknTH79r5xbu+6J1zNHuSyA==", "signatures": [{"sig": "MEMCID6FFwN7jKJnz5j0pbs5QrVLz7Yqj+dJPBfcm48L1uUGAh9x7ZqjZZ7vPryEMdxstoZOcLpZV7H+HrWyTwm0dEtN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196671}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.14-alpha.1": {"name": "nodemon", "version": "2.0.14-alpha.1", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^5.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.2", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "ee2000765f103b2451acfc88d8a2ad6ff8ea1904", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.14-alpha.1.tgz", "fileCount": 46, "integrity": "sha512-WlwQZzZm1iFIJM4KtV3rPNZhIV7w2M0xbUMtcIDNSqTaaS8yWRLx4BkudEnOiptSfHFKoq+MhGpNxZZz7wEk7w==", "signatures": [{"sig": "MEQCIBHWNNNZyh9QhKyTKsHFdryc1uRY2mNTuH84wgh9EqXMAiAERg2ej++IdHCof9r7pB1gWsF2L4GODcBuRs63VNxy/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197358}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.14": {"name": "nodemon", "version": "2.0.14", "dependencies": {"debug": "^3.2.6", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.2.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.3", "pstree.remy": "^1.1.7", "supports-color": "^5.5.0", "update-notifier": "^5.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.2", "mocha": "^2.5.3", "eslint": "^7.11.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "287c7a2f6cd8a18b07e94cd776ecb6a82e4ba439", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.14.tgz", "fileCount": 45, "integrity": "sha512-frcpDx+PviKEQRSYzwhckuO2zoHcBYLHI754RE9z5h1RGtrngerc04mLpQQCPWBkH/2ObrX7We9YiwVSYZpFJQ==", "signatures": [{"sig": "MEUCIQDngwKxqxhCxxx7uxVNRwHw0RgqlX/KoH7WCGxK3YWuNgIgEbFi1TnFV5K089Un1JSp2zhMuXiOgcd2tBoHR2Uijcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197150}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.15": {"name": "nodemon", "version": "2.0.15", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "update-notifier": "^5.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "504516ce3b43d9dc9a955ccd9ec57550a31a8d4e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.15.tgz", "fileCount": 45, "integrity": "sha512-gdHMNx47Gw7b3kWxJV64NI+Q5nfl0y5DgDbiVtShiwa7Z0IZ07Ll4RLFo6AjrhzMtoEZn5PDE3/c2AbVsiCkpA==", "signatures": [{"sig": "MEUCIAQSDgUUFwq4jO3BNmkIjZIM6TlqTdTWGVBs0oQFxTTyAiEA8R/xM1PkOVausiWbjwYfQHSr3BOMzZyxzM7PSlQdoHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196523}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.16": {"name": "nodemon", "version": "2.0.16", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "update-notifier": "^5.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "d71b31bfdb226c25de34afea53486c8ef225fdef", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.16.tgz", "fileCount": 45, "integrity": "sha512-zsrcaOfTWRuUzBn3P44RDliLlp263Z/76FPoHFr3cFFkOz0lTPAcIw8dCzfdVIx/t3AtDYCZRCDkoCojJqaG3w==", "signatures": [{"sig": "MEUCIAmerpreapEsul8Xp0NyMHcWEnUcpCauorDjf8LmCtfdAiEAuOUiRudaUNKKtaJ72ujQXaQBRsC2ZN4ack8OSNbu7Is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia7lTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSiw/9G4X2TW3orlTjDJlpA/Iu1s0CNEmfQtbslgpEUxdepI+XGl9u\r\nma/f9h8tGnrSXrsIzhQyKmD7zJMnnZKfO/4UZWdzj6nRhtSjXqDUdDOhQElX\r\n1+KzeqEAC00/73RV5RnFn6BtsNAMPqQ4h7XZYkkL9bVCREwqbuMvgQoQnPU1\r\nF5VdzSeV5O2sLzGAXbIodvYgFP1tMaQOPOC+EuYlj8ZTdWeGJR0WAua1EnGH\r\nMtul9zAz6TXEPifj+uJRSq4houoEhnvffjcW//u0znlXOB4EhbzB1x7GDB3s\r\ny5afX/sXtMmnEp0btAhRSOGlQ91kGOB5QjuLNTYWicvzh74j2dgNgEtpx+Gr\r\nCr61DQaoQGoImMnq2o3CtBSqnicYS5uGWaQKmzL8CX8x6DT8YrdPqjvad7pb\r\na5WUhMp2WMLM47MLtX9s238CFiXE0alRsFBQEc3PwuJRSUwBkjtIEeerErYT\r\nTwJcZP8vL2Jd+9K/m4J5gu42RyI3iCkZIziuix54sUZlxFiUm/g7Vyw+ROBN\r\ntplU1mPVvmSa6csDDFwnfjVvsP1cR0G1YMJXevnIXybQtgDORIqfmEO3w+8z\r\n7Q1v9vYLH3EHnF7fSbL7Yii5bcMdUBkl9V+uppNyv5o4CtnR3/y9gtQHiGx8\r\n/jlrJ/d/4sgSLshXMSCHBdDlJdRCDNZBE2A=\r\n=sDLi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.17": {"name": "nodemon", "version": "2.0.17", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "update-notifier": "^6.0.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "d7ce8aa970a9f6a7d8b902eada0ee8e28ec38e01", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.17.tgz", "fileCount": 45, "integrity": "sha512-wSLWpHhDLqeV+k0HdSz7D8fxUlshX+SeM3RpcNo5/Y2XBoqVoSBoFNjP9dfT0BVwK/zzqhJ3EF7Z6B9aPt8TzA==", "signatures": [{"sig": "MEUCIGhQOjgUx2F44HiUlHrLeWksbCZUCzoJZSy53Y1T3huKAiEA1BDDBqt4UvcVr/BhKvKJEzJPN2+fHZqmG771T1SMjPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitHdBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqq8A/6As6tV0iV5bim+iWmhstEH/WMQ4oP3qZUXpepo4Kl2tcj52kT\r\nU92hkEt07lzT6MQnoz7ZBztZupHqzcul5WjC7SzBp3BHHWrsCDjohgWr9jzf\r\nyBqyfKyKLoDvdQCJ/t0TdxSBub54dP9T/hrNMQTy9asRbFv09AIygzT6t3YT\r\nkvcKt82rtmGrdSwezn6SoQCaCWK4Objnb6fKqH7G5j64ssjQggDTMiJWkyuD\r\nZujQuKhZHqknjLNysTkq5JZm/mV+AoKETgGJzUS7JJB29/7n/c8OJveulaGk\r\n8vgR1cC801bWEcKYEBZGfKKoW9aQ3DMNt2t0VMM88mgc0ryqdkkCRY5PfYTF\r\nIEwX6UNwHqtdDU8N4ksWxU3G3W3ZMnEbiZilVU8zCyrzIHv7O+gMfj62HEdG\r\nM+6P058NmbHw5znTR2RdTLpKSiP4xTBivvicNGHhl+Bn1v+lIGdLKnk/I6vO\r\n4Vs+HEV3fhQ8gaPgngbTlUQFrk4qwLD8zP4Ci0c2YsNoy8e76qD1Z/dfyM9Z\r\n7ZdCSSxbeFxM21o3guzYAzkteEMzecc5F+QfXXIzlvlmZiRrpoCj9FyTxELO\r\nmd1+J13TCErcEeIbqupuIZUVbQ6DyJ/VZwgQgJP8K2z8SIedpSHMrEplAH1l\r\ntDNos6Bnh4EZZrBAuHF9EW0UaGII0fwkPPE=\r\n=Ccbv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.18": {"name": "nodemon", "version": "2.0.18", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "update-notifier": "^5.1.0", "ignore-by-default": "^1.0.1"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "0f5a3aa7b4587f2626e6f01369deba89cb0462a2", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.18.tgz", "fileCount": 45, "integrity": "sha512-uAvrKipi2zAz8E7nkSz4qW4F4zd5fs2wNGsTx+xXlP8KXqd9ucE0vY9wankOsPboeDyuUGN9vsXGV1pLn80l/A==", "signatures": [{"sig": "MEQCICm4TrgKoJzXbZ9u3AN1EniLgSWUZE9lVov6i198kapoAiAKsxJWQ8X49EymCOuNJ6laLXQ8cjSjFzoOhkPeZwiHIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitIbCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZwQ//XtIiyHvlKtTolvACDmqlT/W0mK9Lq0htXatxO4hcRU3eHZTx\r\nSw/ZubltEgZfXyFWJ0cxLo5Vxd1lGKzll0SnvVJ0ubLlY+sIlSq+lcaC2BEi\r\nshz+kSFOwx98hh++GZQzjDx01MsvGHLRMojv4T5xMXjqqj6+4YB+HagWOiN0\r\nBo6/SlgOVY7MJhjo2jcfotMv34fX0tlJ3mFDS6ZD2mjBpx0142pdzDCm20XZ\r\nHpySQG4S7I6Eb8XQytL4TiCeoGwKjXS1yYIA7z5+qFu5to6sUJi12QtR/EPe\r\nxW8KO4FClW7T1RX5mY79MutsdyBBSmWve5/0TC74V6QqHTVnpqxmkSsEd7Lc\r\nBWzRfBdIhdqr9b3v8NsNKMsZSCkymW/GID4VswrQIYm3+6vbX3DFd+Ruhn/C\r\nHLsDZBdVzVNuAwNCc948qcEI+VxI89GdyTJK3t7WbYmqREqjzr21uy3fuZdl\r\n2+FvQJQm0nNsD/AqdhcyiduKxl8ArEjJ3VV5bR1qaAHqOEdESfkr0qFiVlRi\r\nICkPqaOYqN9JuBf+tJ6cBNq6qYZRY9MEUcw6VlbinVcF1WGr+/Dn72MSa17d\r\nKKH1AhwFshEJ1IeYpbpY97P/mmeAFKcye7pHQRIQM4QfgupHy/Xs9fZzicYh\r\nEqyeUHRw19/oxsEBZ+fbSUHGySJd89YoLcw=\r\n=wNHO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.19": {"name": "nodemon", "version": "2.0.19", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.0.4", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^1.0.7"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "cac175f74b9cb8b57e770d47841995eebe4488bd", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.19.tgz", "fileCount": 45, "integrity": "sha512-4pv1f2bMDj0Eeg/MhGqxrtveeQ5/G/UVe9iO6uTZzjnRluSA4PVWf8CW99LUPwGB3eNIA7zUFoP77YuI7hOc0A==", "signatures": [{"sig": "MEYCIQD3KNh1duXERLe+NFBhOTigDkbsTxHgdfCKnG9mSwYMxgIhAMt9jGPEj5WDIFQvx6LadDQlHao4ffVVEaExLEK28Zl2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixGWTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpi8BAAoz1fmDHb2Z39onFrSyea0LkttpOltQeWxTT/RjsegQgrCszu\r\nOPNhvi3cH91JuA6IPFTY5EPAMg5bFmHPA1OJtjopwV4e9RpRwggjslvy9745\r\n/rqAoKgago6rKtSF4+aucTi9sHwqWeYzEgtGxtrLkc7398Igz7o7uWcqK3vr\r\n4F7jNsIjfMS1TtUbpIvOzdsRkYQ6k5P60HgshSn5pnWsX5wBAKAdZjoJdW7x\r\nMmoykiQ2PJ+6h5bG8kl9HUoWfL3rKDrZjyX2kVNKeMy2rdQ4Nbb/0BKHnzrt\r\nef1Pnzjx8mVTb3zRdsLR9OTPqLKNO5NJrI1cR4I/yJvoiK/WLDYa8DqpJIax\r\nD25TOKkBDQVwzYtMg1x9fP23bXIoYTkdaSMjz/6nWn2xH4m1etR9GAD90nME\r\nEwaNf/F1TmKIozg1UEiNoYPGaa1k00OWFZHjCjbbAEygNihcVebj99rkGUCo\r\ne4j7mdKSdGTLwr19/2jzS9NjZzUNnNFanSVwyf/l+K4C3YPhW/PjvXF1uS2q\r\nm2Oebea4h5/f4SPgGlwAyteK1Esu4khyejUzZAZypiWipTKC+96V18bNv5WQ\r\nSp0/V330V/2ZgjDLPj3V8SDp9j9QBtqJnBWJNARdishA8mh105G0ALvOT5sM\r\npXznkgSwMzyieIAmSsKnl8J++xH9Qi1GFqI=\r\n=/lWx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}, "hasInstallScript": true}, "2.0.20": {"name": "nodemon", "version": "2.0.20", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^1.0.7"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "e3537de768a492e8d74da5c5813cb0c7486fc701", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.20.tgz", "fileCount": 39, "integrity": "sha512-Km2mWHKKY5GzRg6i1j5OxOHQtuvVsgskLfigG25yTtbyfRGn/GNvIbRyOf1PSCKJ2aT/58TiuUsuOU5UToVViw==", "signatures": [{"sig": "MEUCIQCz/xx7m4Ztw7wTIfeQ9Lebzg+yh8Z1l0I10/ZJVQNY8AIgXhkxQ0/+a+8AiAhiWSua3AAqq2fKa5VSkEEYap5MZrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJGgYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol8Q//UYofoeb5RwjU9Az/LS2VvNfSLDpvm8dVctLYndhndie9z7bD\r\nY0WN3S9IinWXiV40qygdKI0yYv03inPRrW3HJyD3cG4gafpioXYG7LTeBUmF\r\nf17Imw6JbjgxoPFJGzojIt+j+3GfSBQcgTk4+fxO7Jx2SuKZ1B4R9mZ7bmek\r\ncwapqxO0DNrG/B0tV8M0YRJKw1e2DI57wMTTAJIyyiHHO2rzJrbFjljXYPPZ\r\nIMGaRAgcq7aRXcJlemuvLxe2iqpIrGJUfF5+KVWkTQ485ZrcUshq3L2XBYlE\r\n/1q0bzDsuinWTpkEWH5QlgyyRDevOAUd9tU4WQrbyAtw+BCuCWoWVG0h571l\r\nd8+10AEm3U3zOOOTtZy/CcUnn6E7KDjzv85uIMqcQ7IOUlxl+hnpWRFELzgD\r\nTDWgmhQ4gFJVTyHKI7rRM6SizkD3F8u/FYPa/0iRM7X9RHBNSlwgJpArwoA3\r\ny8Ejjd3MlufNFaYKNzSfLvG4QsAhv5mehIOTKU6w3Wwd6yRxepAUylFOpugW\r\nq+u794XXwxSbQixQoUTzklUFtTfB+piO58i8CjRWPgX1DhoMXv0VbX0DP0QV\r\n1lrTGyBHuASOAqhR78maZ1bHBD4DisqB74fZ477+pfdoQ8MHV+rwUIq889RB\r\nfiSEZEIfw/qXgacA+WqJpoimUOlXiJoj50g=\r\n=Y2+M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "2.0.21": {"name": "nodemon", "version": "2.0.21", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^1.0.7"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "267edff25578da91075d6aa54346ef77ecb7b302", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.21.tgz", "fileCount": 39, "integrity": "sha512-djN/n2549DUtY33S7o1djRCd7dEm0kBnj9c7S9XVXqRUbuggN1MZH/Nqa+5RFQr63Fbefq37nFXAE9VU86yL1A==", "signatures": [{"sig": "MEUCIQCmU6TrHsPuNjxbyS3mjbqb3FsblrOCAD4IRrF7kcEKDAIgOM17Rx9AJgvnxkahMH6eUZJpIEFJ3NWEuyOL+EYZ1Vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkANRoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmommQ/9GKi/19FWlU399gPeR6Dom7GgK6wakS+q4ZN0hr0JZHJbqYc+\r\nZS7R7xhqFApHKfrWLtXOdeDMnXjVXNXKPkUZAliH9XphQqFCDrFfjxq/MOWx\r\nRe02RWNrbzuwPmRFsliTP30zUXZVvC5qPypKz5Dx/G/smAEXqGmlJ3npxvLw\r\n2+TnHTpb3KdT7P8SFk6+gLTBCqDF1Yk2BUY4aKs0ccYnROfvEIaRY9XEomoU\r\njvnE0CT/FDs83gnQcOvYRhe+lkC9M6KClpFHPMiehX5kSCpoHAjlV90+tEt8\r\nXWmR/qD5OggXeKLEtyYwEI0iBuqVSmQx1EjbUzSXkFc8yzrDjotHJBojXxs1\r\nSsMld8ozxn5qdIQ6SX9lGhXGi3HZr1ROxllgeIObi4wz3Dl3AdAZxj1j4v16\r\nZM/d/0LIXiYM+GbKa9YzjTfRTgrZMYAAFbqrep5C04k/ugw8Ig+kZ8JScOw6\r\ntc4mhEOtzKJE3ulJqn5P+IRMUvQFQHM5l5vfEV0h90U/M/F+pbSCsj9smypG\r\nfeZEtwTw8KwWIVQgZ+gCv12NP9GbUFY8VeAnqrGhKgM3ldHKfsg/WAaeMcCt\r\nQWe0beGyS5TGuELl0D/RX74t3knoDdpRdPWAEZu3pVOl7EXOnKBxT0KikaJe\r\nz09aXqlk365AhrgS6wuQ7oaryUdNLf4S/Ow=\r\n=C8B9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "2.0.22": {"name": "nodemon", "version": "2.0.22", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^5.7.1", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^1.0.7"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "182c45c3a78da486f673d6c1702e00728daf5258", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-2.0.22.tgz", "fileCount": 39, "integrity": "sha512-B8YqaKMmyuCO7BowF1Z1/mkPqLk6cs/l63Ojtd6otKjMx47Dq1utxfRxcavH1I7VSaL8n5BUaoutadnsX3AAVQ==", "signatures": [{"sig": "MEQCIE/FqyKQh7QYa8L2dw1E/kikIyMqwO4rgms1/qT9gzpwAiBvGJ+5OglyoRJzCsr70yWPKnzvokPaEKsvko36BlLnKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG1ylACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmql7Q/8D9DddF345zZinh37/Ri8ePOLlBjQ5NBLdUuwwZy2AAZAfZ+X\r\nfwEncWDEhFbkqcpaPdqoRqMnu1X3JEMXdKmhWN190fOySiadGnpyGFcykpgf\r\nXrHYBlK08V3Gr4TENIGibyRMKBaRP0ZthvRSlHyWiU4INe9MaVGx8+mfWHv5\r\nOrDndtbalWk+yNbtJAnREPB4BE0wAhRIJsR0eiojapkr+i6ovIGGrbyQcB/t\r\n3owquvqwJkD7GOh8I28JZKEXuIa45Ftl3Ur1xAIbGBWvCO6Yk35vg1YzBaU2\r\ngWQO6L+vpVmsw6pvVhmM2Z5E3MnWwAnkBpP352ueDZJ8zv/xMtHVP2yH7BW1\r\nMog6csYyt9rOLpvV0Ek65VnxJWkAVow77TaWMx9oBoOxd6egx22hpK85KW1z\r\nBAlIBZ6RqznuSgDVxnxN+btnJq+IqwdyaLNgCK1iHiv45XPF/D1lVl2vWfTL\r\nsWKTiEr7XvAYEfFd6NSF/WPNykLXSSkcKZ97FlThshGG/KfQSj1cyaAuTVuM\r\nLIOwWef0Fy5R6capxq/Zql8n8ob5xg9gpkjetzNO6go0UX6Q5AT4Jcy41Tk/\r\ndxU4Av8soDkhxJeBCW3P18gqKAqQZdwm8+4Fb39T5reDoVDV9hpxc8kJzn43\r\nWZ1RJXc7RRt9rpc85ugCTHhSmnnTy6YJPEE=\r\n=iMkW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.10.0"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.0.0": {"name": "nodemon", "version": "3.0.0", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "478742adc1e53a72028c7bf21bb889e0e541e9c3", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.0.0.tgz", "fileCount": 40, "integrity": "sha512-yU9NSp3n+DUSt3S2LmtXss+4kOsmC8ZLpXeGe5mKuLdqkoSRwmaplk2lo5cmve7TPw5MgMcd2cazL0KpUscoSQ==", "signatures": [{"sig": "MEYCIQDWZupps1xeP+xSrC5Y74O3C2/nzfF7tTJveLbIL4IspgIhAKgCJu1YbiROPV9r4+kevkaxtf18G5hlXJ1amouAsrDQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208910}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.0.1": {"name": "nodemon", "version": "3.0.1", "dependencies": {"debug": "^3.2.7", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "affe822a2c5f21354466b2fc8ae83277d27dadc7", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.0.1.tgz", "fileCount": 40, "integrity": "sha512-g9AZ7HmkhQkqXkRc20w+ZfQ73cHLbE8hnPbtaFbFtCumZsjyMhKk9LajQ07U5Ux28lvFjZ5X7HvWR1xzU8jHVw==", "signatures": [{"sig": "MEYCIQDDlwu3DV8fd0Mz/fuRwHqI4tP+AIl0if1nlywQ0MibXAIhALwvubxbCIeU0uioPRXp8ZTHJIUX8jozib5QogKUL6cw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208957}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.0.2": {"name": "nodemon", "version": "3.0.2", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "222dd0de79fc7b7b3eedba422d2b9e5fc678621e", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.0.2.tgz", "fileCount": 40, "integrity": "sha512-9qIN2LNTrEzpOPBaWHTm4Asy1LxXLSickZStAQ4IZe7zsoIpD/A7LWxhZV3t4Zu352uBcqVnRsDXSMR2Sc3lTA==", "signatures": [{"sig": "MEYCIQDbli37Jni1IztOHnBxk8H7ppwbxg6ckFD1Qc6tEucvXQIhAIbXF4q689X0a3pvPYM5WqmuQxybuZXHXhuhlnY4jahO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 211058}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.0.3": {"name": "nodemon", "version": "3.0.3", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "244a62d1c690eece3f6165c6cdb0db03ebd80b76", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.0.3.tgz", "fileCount": 40, "integrity": "sha512-7jH/NXbFPxVaMwmBCC2B9F/V6X1VkEdNgx3iu9jji8WxWcvhMWkmhNWhI5077zknOnZnBzba9hZP6bCPJLSReQ==", "signatures": [{"sig": "MEUCIDKth/n+Sr0db1D0UrJ/g4toMcEqZfOzwDhhxBVD8zRFAiEApXEm8xJdrv8E162DTsw+An+ZKfRUVxyi8KNqnDyLmtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215235}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.0": {"name": "nodemon", "version": "3.1.0", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "ff7394f2450eb6a5e96fe4180acd5176b29799c9", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.0.tgz", "fileCount": 40, "integrity": "sha512-xqlktYlDMCepBJd43ZQhjWwMw2obW/JRvkrLxq5RCNcuDDX1DbcPT+qT1IlIIdf+DhnWs90JpTMe+Y5KxOchvA==", "signatures": [{"sig": "MEUCIGVZdA4qgDkSXYuoozWajGVuUwn0bEhqeFkbbYVbSSGGAiEAi8bOSwaBfLlBR5qd/+c8wlcg4M+0wh0KD+authgCm4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220214}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.1": {"name": "nodemon", "version": "3.1.1", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "779631d9da3a7f8a995b16976c87621818c19a8d", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.1.tgz", "fileCount": 42, "integrity": "sha512-k43xGaDtaDIcufn0Fc6fTtsdKSkV/hQzoQFigNH//GaKta28yoKVYXCnV+KXRqfT/YzsFaQU9VdeEG+HEyxr6A==", "signatures": [{"sig": "MEQCIB38ROO7vDZhIcDHb+MD0RmQmxX7wcu/j01/SkaVnqrkAiAZ5VKD+HY43RDtUfY5u1pXEonLXCTfMkgl4JSQGvfZ6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225384}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.2": {"name": "nodemon", "version": "3.1.2", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "e4041ef00b930c5bdc1ab68d897a88eb7b17c8c2", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.2.tgz", "fileCount": 42, "integrity": "sha512-/Ib/kloefDy+N0iRTxIUzyGcdW9lzlnca2Jsa5w73bs3npXjg+WInmiX6VY13mIb6SykkthYX/U5t0ukryGqBw==", "signatures": [{"sig": "MEUCIQC0pxwzjiYmy6wLynxxo/yfJkXYdsHN/17gQFG7cWJILAIgRJd3anaKUbnMzvn407Q31j2CEOW+S+AMw0hHB8Xbino=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223958}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.3": {"name": "nodemon", "version": "3.1.3", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "dcce9ee0aa7d19cd4dcd576ae9a0456d9078b286", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.3.tgz", "fileCount": 42, "integrity": "sha512-m4Vqs+APdKzDFpuaL9F9EVOF85+h070FnkHVEoU4+rmT6Vw0bmNl7s61VEkY/cJkL7RCv1p4urnUDUMrS5rk2w==", "signatures": [{"sig": "MEUCIQCaw9VtunF2slQXnQSWpfswfIMJCaEsh1OxBKI/5GajhgIgSpm+rkbUVoyQubEGs3f3ABnT4C+kjgyixQ24JbHHas0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223982}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.4": {"name": "nodemon", "version": "3.1.4", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "c34dcd8eb46a05723ccde60cbdd25addcc8725e4", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.4.tgz", "fileCount": 42, "integrity": "sha512-wjPBbFhtpJwmIeY2yP7QF+UKzPfltVGtfce1g/bB15/8vCGZj8uxD62b/b9M9/WVgme0NZudpownKN+c0plXlQ==", "signatures": [{"sig": "MEYCIQCwhOm8SpROP+d7yea4+N7msFgzzyUelacfD1ok5BEt1AIhAKbCgWoESstJS/xG95IQSKB8gi/c6/X1RnAUDuK2s+8t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220671}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "0.0.0-development": {"name": "nodemon", "version": "0.0.0-development", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "6e02a06a0cfca0cc0f4b3bc1743dfe92c8521dc6", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-0.0.0-development.tgz", "fileCount": 43, "integrity": "sha512-t5qO0X8GPn9z3dVxe7E3TzJbEyQ6h7BWQTinLcd39GDFHAb3ePtOG4To2smFYUtEKgLXlS0ipO5IeCileF6iqQ==", "signatures": [{"sig": "MEYCIQCMXYLAQ1F5lnPZhY07KOO5kPGXcDWSTjHH1fLuX0ugwQIhAJSikLMrZnZ0K0SryW5nXAWQA8zcfa6dywdskrIQ3+Ed", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222554}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.5": {"name": "nodemon", "version": "3.1.5", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "43d2a07d88a3778a3ba35b0dbdf505dcc5b3b5ad", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.5.tgz", "fileCount": 42, "integrity": "sha512-V5UtfYc7hjFD4SI3EzD5TR8ChAHEZ+Ns7Z5fBk8fAbTVAj+q3G+w7sHJrHxXBkVn6ApLVTljau8wfHwqmGUjMw==", "signatures": [{"sig": "MEUCIFUiTrUxXv4kuIhMYMa2WKB+Sfcgj+A2opT7nvVPdo6nAiEA2D/c8rePMkotaM8HaFj2qMDPSzoTD6Dm1hl3ACN1iHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223680}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.6": {"name": "nodemon", "version": "3.1.6", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "15bd79dca6849aa27b2689d1dbe02dc28bcc1a1c", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.6.tgz", "fileCount": 42, "integrity": "sha512-C8ymJbXpTTinxjWuMfMxw0rZhTn/r7ypSGldQyqPEgDEaVwAthqC0aodsMwontnAInN9TuPwRLeBoyhmfv+iSA==", "signatures": [{"sig": "MEUCIHL3ZnYQd7VsZG0oFR2fNbjw91zlYE+84OiqZMcI2rZAAiEAltGYIxQHyQV9mZTINMxOD+fUKl43wzH+ilpE41OD63U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224317}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.7": {"name": "nodemon", "version": "3.1.7", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "07cb1f455f8bece6a499e0d72b5e029485521a54", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.7.tgz", "fileCount": 42, "integrity": "sha512-hLj7fuMow6f0lbB0cD14Lz2xNjwsyruH251Pk4t/yIitCFJbmY1myuLlHm/q06aST4jg6EgAh74PIBBrRqpVAQ==", "signatures": [{"sig": "MEQCIFeXzdy8PRJNkvTo8YFvt64MoO3KPK+N1YJS74TF2yDjAiBsx4uwogJiIF5oHdoPq1KuTQBMwVx3r3BCjYqVMCcW6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224311}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.8": {"name": "nodemon", "version": "3.1.8", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "328d2358ab4bb075f68ff01b8aa287e939b753cb", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.8.tgz", "fileCount": 42, "integrity": "sha512-uXAaM+KwN9pON/yfuq+l2bmgQO+CpT2gSyjTi9fIJG2LNEXXaiDOXisvtm90wWlSuqB/TNR2CmoYks/DhGHX0w==", "signatures": [{"sig": "MEUCICOHmexsmbl4jF8Zm/4OQC2qUGabI0SHPifOD52ZXSNmAiEA+iZ+zKZlDVl9gyH3xG1sUyzgEgOeinvhH4PIGQnf7ak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220252}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.9": {"name": "nodemon", "version": "3.1.9", "dependencies": {"debug": "^4", "touch": "^3.1.0", "semver": "^7.5.3", "chokidar": "^3.5.2", "minimatch": "^3.1.2", "undefsafe": "^2.0.5", "pstree.remy": "^1.1.8", "supports-color": "^5.5.0", "ignore-by-default": "^1.0.1", "simple-update-notifier": "^2.0.0"}, "devDependencies": {"nyc": "^15.1.0", "async": "1.4.2", "husky": "^7.0.4", "mocha": "^2.5.3", "eslint": "^7.32.0", "should": "~4.0.0", "proxyquire": "^1.8.0", "coffee-script": "~1.7.1", "@commitlint/cli": "^11.0.0", "semantic-release": "^18.0.0", "@commitlint/config-conventional": "^11.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"shasum": "df502cdc3b120e1c3c0c6e4152349019efa7387b", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.9.tgz", "fileCount": 42, "integrity": "sha512-hdr1oIb2p6ZSxu3PB2JWWYS7ZQ0qvaZsc3hK8DR8f02kRzc8rjYmxAIvdz+aYC+8F2IjNaB7HMcSDg8nQpJxyg==", "signatures": [{"sig": "MEQCIF7uX+OMfmOR56LCIxlc7nsO6Vs0+6BQyo72/9sQ3BSBAiBOE38z1L4KMJiohwjoLEn7bbbE1dHXCXq74OfWS9o0Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220421}, "engines": {"node": ">=10"}, "funding": {"url": "https://opencollective.com/nodemon", "type": "opencollective"}}, "3.1.10": {"name": "nodemon", "version": "3.1.10", "dependencies": {"chokidar": "^3.5.2", "debug": "^4", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^7.5.3", "simple-update-notifier": "^2.0.0", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "async": "1.4.2", "coffee-script": "~1.7.1", "eslint": "^7.32.0", "husky": "^7.0.4", "mocha": "^2.5.3", "nyc": "^15.1.0", "proxyquire": "^1.8.0", "semantic-release": "^18.0.0", "should": "~4.0.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "dist": {"integrity": "sha512-WDjw3pJ0/0jMFmyNDp3gvY2YizjLmmOUQo6DEBY+JgdvW/yQ9mEeSw6H5ythl5Ny2ytb7f9C2nIbjSxMNzbJXw==", "shasum": "5015c5eb4fffcb24d98cf9454df14f4fecec9bc1", "tarball": "https://registry.npmjs.org/nodemon/-/nodemon-3.1.10.tgz", "fileCount": 42, "unpackedSize": 218878, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD2viZ8EKR6uM6PnLN4ca0XAHfIfILJ6DFDoRxooqdDKgIgXyBwodAC6aCuu8Jof9pdKubFdJNACD36hPOz0xWZkms="}]}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}}, "modified": "2025-04-23T09:21:44.333Z"}