{"_id": "punycode", "_rev": "130-4d40b59301b83e5191ece86316c9640c", "name": "punycode", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "dist-tags": {"latest": "2.3.1"}, "versions": {"0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "punycode", "description": "Javascript Punycode converter derived from example in RFC3492.", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/reconbot/Node-PunyCode.git"}, "engines": {"npm": "1.0.6", "node": "v0.4.7"}, "dependencies": {}, "devDependencies": {}, "homepage": "https://github.com/reconbot/Node-PunyCode", "_id": "punycode@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.6", "_nodeVersion": "v0.4.7", "_defaultsLoaded": true, "dist": {"shasum": "ae0f52d48d5efcde4a8e02fbdfdc9d679bec0d01", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.0.1.tgz", "integrity": "sha512-4p131gbmlARDXOuRPEa4M6HsMaZy5W8VrNfQ7zI+1A/oZiXvW9hMVfowcLkI952S1lMzbIn+a2OnnsIrnjbO7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICisBtS6j6cVMKBNCdiU/OXbt1zfGuCyPK5y3Hmb9OBDAiEA1OkyQUnseG8M2iucAIcnJfxSbuPQCFR8xHvFTYNaxEw="}]}, "scripts": {}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "punycode", "description": "Javascript Punycode converter derived from example in RFC3492.", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/reconbot/Node-PunyCode.git"}, "engines": {"npm": "1.0.x", "node": "0.4.x"}, "dependencies": {}, "devDependencies": {}, "homepage": "https://github.com/reconbot/Node-PunyCode", "_npmUser": {"name": "wizard", "email": "<EMAIL>"}, "_id": "punycode@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.11", "_defaultsLoaded": true, "dist": {"shasum": "a16bd1e46e5b59276e2e21b6f15086c484f3a6c7", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.0.2.tgz", "integrity": "sha512-v2IWXYnc3Fsorh1AYF+OBrczEK8IrC2AuIJmm+AgQkA1gHtl9r4MD3d0eiHC7cP8Qn5GpyLme3VEx744al2hFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB5zwgPpMy8YBkzR9nKj0GOto4XyIiYkIyl/5JopXz1IAiAOnXJkUAxsRaXSjMy6ySIr5JMgtIVRsCdlPaT2jy/ueA=="}]}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}], "directories": {}}, "0.0.1337": {"name": "punycode", "version": "0.0.1337", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.0.1337", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "73a5fcc5e6c89681a9cd9800bfe75cb245fcc15d", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.0.1337.tgz", "integrity": "sha512-KOc4nF4oGehYglj8XlE6dIk0Z4D3dhAFbHDskHgpuegyghr73dp4YOmmt22AkY1ljshPi742LGZZDMS7BIIU6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICtOV6ZqbuuqwthJWvRLTYiw95a4TnBwMOPG1+9QclzqAiEAzmiAbjO1ScZ6LKGJz1lxTDVEE0KzkP58KUtu55mWikI="}]}}, "0.1.0": {"name": "punycode", "version": "0.1.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.1.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "8f7655c949a9d0adb0e0b61039f8c9d9ac3e12ac", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.1.0.tgz", "integrity": "sha512-roeg8KPiCXpHfvfweMwv1HhP7W6Mv+y+zTZEmDUHCPWO9dtMPTZ0pQ+tNPDW5t07XRmJmEdR793pc4PAnbFvBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5EXj0Rplkhj/SV0u5HYMTAjKYfIr4CFNR0yiEwRMVEAIhALzMYmYL/UDO52fLQrTCO+b1vB0N3dJn/bGgFaDSb/ku"}]}}, "0.1.1": {"name": "punycode", "version": "0.1.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.1.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "35be18fe2e00e034c6815ad763534959595ae5e7", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.1.1.tgz", "integrity": "sha512-Hrm32Ojxzuwrhs9A5X+wiyljnotRxlGjqaEs4HOQYmXn2IeRbPVv6ZaYYKSXhh12BDhvXKgdwqSGEQrT9pXtdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEW0hN7ozDM8soJFTnGhl7gzq550LG+iWX/bozmwAP6jAiAG4CVW+A8jYEqwsza69jWLKaRhCZPjTe4qFsqeu1+rHw=="}]}}, "0.1.2": {"name": "punycode", "version": "0.1.2", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.1.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "bce2172b2307c51d58b95bf02e9b033e8a12982f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.1.2.tgz", "integrity": "sha512-o0ply/kNxdtIwyDbMgi2HeBGAWdzYmX3rz74A1zN2+ua0B37dsZkNMkY+cWJXQgdfQMLfGMor9eyLNZ/Urftdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcAWzBtobqyE2CdEui4TiHVoOI0a9N8r/Hb9oAzaRLmAIhAJIarbMs2mIzbEdjHHVQe18rkBluesko9U/0NSvEWdmP"}]}}, "0.2.0": {"name": "punycode", "version": "0.2.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.2.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "e7cc94740eb902bc9f791a8a598078a481ca677a", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.2.0.tgz", "integrity": "sha512-hhiwybWtWoUSSa0rAto22Q66b8FMibm42i/aJy0dcKePnVz6raqYcfF1o0EK1Pk9qADQiU9UyJ5tUdZhjO4xIQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFRlV//2l/4LU9UiYFWRyuIEAVusUMbjkyIL1dzAGK8AIgBch79p0rR5Tl/9O1q0n7VkmDSMhX1QEmNWr1SIvBUUw="}]}}, "0.2.1": {"name": "punycode", "version": "0.2.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.2.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.1", "_defaultsLoaded": true, "dist": {"shasum": "c52e2d332170d0fb45f853f3b4e718b00245e167", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.2.1.tgz", "integrity": "sha512-BmYVxr5C22UZVBB/1Kjt8EHKY1XUP2HjdIj/SUlDcCTCrKyMls0hQy5K7iQzsJml3JEVfhkgv65z3tgVS8DGxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAFPOl8c9br+/nUwbbM2UorTMHxUaa7TerGYOvYtXiKCAiA0/HF62hLr4lqhHSJ0SMhEqLWrL83/SAY7uhFe829pgA=="}]}}, "0.2.2": {"name": "punycode", "version": "0.2.2", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.2.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "b7bf8403ef5a61a5860a5755dbda0a176d6a3be1", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.2.2.tgz", "integrity": "sha512-Pp/Vkhq+bbdUonJ20LSb2RT++T5zeo2kkVc8rP+S0RNYlP1YqZYOkKuoie6JzUazLD1BRk/WFJCJV2iJ4l4gQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFk1fPaX4ZhHuDJ2zIAlz4lxnVMrx5DC4UZ2zn8iWgUVAiBr45FB6g6lLVSsIx4W6sa5Abd3Ko2e1gVfOLAM+BUdDA=="}]}}, "0.3.0": {"name": "punycode", "version": "0.3.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@0.3.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "3e6d6f2e2ca3dce4364c66ec971823b228302d6f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-0.3.0.tgz", "integrity": "sha512-gdxRpQMsEVATGrfqYplbrHrKLdVBcuW1JRl+6RO3gy0t461ZN9JpHoLMMqfWwwnXry4p42UJDm3H48+agHNn+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRkGt6TUz3H9u7FNGEwgsfaCHBBQhEBVdDk45cGgxK3gIgEtLCZyl5jcCvgVUYmorVjrX474js9BNfameMolxSN6Y="}]}}, "1.0.0": {"name": "punycode", "version": "1.0.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "_id": "punycode@1.0.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.8", "_defaultsLoaded": true, "dist": {"shasum": "ce9e6c6e9c1db5827174fceb12ff4938700a1bd3", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.0.0.tgz", "integrity": "sha512-H/lCVQ35bWuDIVJyWvEsoBokpjJX0OF1eeQzfc/JoNj8seA0tPU599UOGtlCJmP2oukCejCzOBUlvIVXePk6pA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICHgfd8IlZcVrozplC6GyT9MkE9jDcUd6KmZFKrXumd4AiAeJjcBU6BluOOWzF2NiVsqbFSpax49E3v93pzc2C9DdQ=="}]}}, "1.1.0": {"name": "punycode", "version": "1.1.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_id": "punycode@1.1.0", "dist": {"shasum": "6b091fb61ee64128ef9fa18780e74e1e7ba0217f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.1.0.tgz", "integrity": "sha512-AKZuQh5doUm8PLa/g2TG+eCo6S96KJqXR+uAmohMNJnGHvvlzSH+HJRlaUjbfmm58KnkhzH9pKULyPOhJ28tsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHfT9ncjIKI0ImXIM8NWwj8zb0cwyxDnT2bvuHS/ZKOQIhAJBzH8qMZbX0NEiC78wCYrTAL0lNTWYiGwEbUvty6b66"}]}}, "1.1.1": {"name": "punycode", "version": "1.1.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "wizard", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_id": "punycode@1.1.1", "dist": {"shasum": "42b3ceab84d3c9c1da4d5a43f59eddbf54c60678", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.1.1.tgz", "integrity": "sha512-RRkhKrDNJO9rXHJkMGdDfEWCCWQL/228CI4HRDkuvWk+QUr+cXPV53hBUOl9iLXPTWpRUfrfh8W8kmIhal8qoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3vBqAdwnKp/yDhycer22sSMiL4tvsELAMTHR8DM8NCAiEAwGrWW/k/qzWbGGeNgUs+RD6xlZh3K394fSdiBTuCEqQ="}]}}, "1.2.0": {"name": "punycode", "version": "1.2.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "_id": "punycode@1.2.0", "dist": {"shasum": "aee66ec448ebc5c45849af628485ac05c324b9c2", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.0.tgz", "integrity": "sha512-GaFXpycxJqrtLzJNWJaRkqH+NtV5PDB9pb2kjwzI2vbxqugbsgEXFJ7GQcBSkhSigaLR+x04Pwhz0t/o69TGWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCHyLLVToqZ6/20yreyn1KNGxPEN/S6M4pwKofe+4ok68CIG9vgeYmDK/34yu55JEBF+5LQuOCvT8ayzwM6wUum6Bi"}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}}, "1.2.1": {"name": "punycode", "version": "1.2.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"istanbul": "~0.1.33", "grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.0", "grunt-shell": "~0.2.1", "qunitjs": "~1.11.0", "qunit-clib": "~1.3.0", "requirejs": "~2.1.5"}, "_id": "punycode@1.2.1", "dist": {"shasum": "90047bc2e6dbbf757d281b25af69b0a773df9cef", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.1.tgz", "integrity": "sha512-DGJc5D3affPS+Akvn2HvndCJ9qykDE7UbNdW5/JZQDY0YrzR4Hfd0APClGX6q1rHFqi40/QVK5Ppd/2ZBH5bnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHwRkbVYwIWP505CRIrbtQe08YQx70Wau0J906ARnjGwIhAO5bpjbAFZTfY2vF25VeTYc0DiqbqfB61oZKasnHAnpv"}]}, "_from": ".", "_npmVersion": "1.2.14", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}}, "1.2.2": {"name": "punycode", "version": "1.2.2", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.2.2", "istanbul": "~0.1.36", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "punycode@1.2.2", "dist": {"shasum": "a7727afa42fc80a4bc19f7fbecde6ecec7e5a2c4", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.2.tgz", "integrity": "sha512-7fnS1RnGR2BNSDPQBvphDpFLPJcjHMluTm+KJ24Bz0eGH1CUppZ0sfWYaA9nwISNMbVdCtT/HTlarY3RUkTU+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjwCgc1fTCL3DeTkOfpqEuLsaqEP8cW/akR9AiyWGxNwIhAK1akTlfKs9B1i2Bhm+QAVdznglp6TTL3HUG9MFsQdxP"}]}, "_from": ".", "_npmVersion": "1.2.19", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}}, "1.2.3": {"name": "punycode", "version": "1.2.3", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.2.2", "istanbul": "~0.1.37", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "punycode@1.2.3", "dist": {"shasum": "b4e304471082d783c73b3bafabf2fd9b6a486266", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.3.tgz", "integrity": "sha512-fw7/4wZQN3YCxJlsaVgVSjm0EP15jeO+YwlO76qt+qJu9qhtyyqUYQHWIVen6xfG8F9D+yS497xGo6mwB3UtkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRDf/Kh9arxdRySCkAds1F/Rn2yvMcZ7qE37mvcF4peAIhAPc8zL9xCuwDNRj/OpQ+bGxBrcPC0xa/1k+naVAnTnvc"}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}]}, "1.2.4": {"name": "punycode", "version": "1.2.4", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}, {"type": "GPL", "url": "http://mths.be/gpl"}], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}], "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "engines": ["node", "rhino"], "directories": {"doc": "docs", "test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-uglify": "~0.2.2", "grunt-shell": "~0.6.4", "istanbul": "~0.2.4", "qunit-clib": "~1.3.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.6"}, "_id": "punycode@1.2.4", "dist": {"shasum": "54008ac972aec74175def9cba6df7fa9d3918740", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.2.4.tgz", "integrity": "sha512-h/vscxLPvI2l7k/0dFUKZ5I5TgMCJ/Pl+J6rw77PDuQM6UApf/GaRVkjv/YSm2k+fbp7Yw8dxsoe29DolT7h7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXN3txqow0bNcMJ5LTVCxc4NxnBV9cmSlAMZvxQfXYvwIhALq3vAhVwESJ48RZ0UGMbTr/ac+dvDe2ju5QV9NnlJ/i"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}]}, "1.3.0": {"name": "punycode", "version": "1.3.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://allyoucanleet.com/"}], "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.10.1", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.5.0", "grunt-shell": "^0.7.0", "istanbul": "^0.2.13", "qunit-extras": "^1.2.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.14"}, "gitHead": "40e15ef43a44fdcb2b60fb631384168ef8e0181f", "_id": "punycode@1.3.0", "_shasum": "7f5009ef539b9444be5c7a19abd2c3ca49e1731c", "_from": ".", "_npmVersion": "1.4.15", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "7f5009ef539b9444be5c7a19abd2c3ca49e1731c", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.3.0.tgz", "integrity": "sha512-dQN3nR/bArA9w2a4MdkxuRtnIL5nmKh5YDnD2FAN9tc5OSTMZImoS42dI4CneIJmKECLpAriSojg5nZlqbygoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIt9KD65POi/aLnIT5qNLnyUGLpCE6bUqzGX04QIucPgIhAPtiWnqM79GmefTPKLUYykHYI5cNHpxLTMcZ0ckBp9aA"}]}}, "1.3.1": {"name": "punycode", "version": "1.3.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "http://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://allyoucanleet.com/"}], "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.10.1", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.5.0", "grunt-shell": "^0.7.0", "istanbul": "^0.2.13", "qunit-extras": "^1.2.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.14"}, "_id": "punycode@1.3.1", "_shasum": "710afe5123c20a1530b712e3e682b9118fe8058e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "710afe5123c20a1530b712e3e682b9118fe8058e", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.3.1.tgz", "integrity": "sha512-08OSO5WGhNXz0UMydTAJdMkJ57T0gpq9Y8smtppxDKCCc6ozrN+hWmWKfgZtQYE8JtUZa7HWyQ3kk8xUhH7w5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9zOff14gwVK8/0SUwXax3cNDktGmszk2TFBnCxnF9PgIgAe8YEwd8239TdDIFtg+NqnCXDK9cSNrqJc+a5+lNcIA="}]}}, "1.3.2": {"name": "punycode", "version": "1.3.2", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://allyoucanleet.com/"}], "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.10.1", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.5.0", "grunt-shell": "^0.7.0", "istanbul": "^0.2.13", "qunit-extras": "^1.2.0", "qunitjs": "~1.11.0", "requirejs": "^2.1.14"}, "gitHead": "38c8d3131a82567bfef18da09f7f4db68c84f8a3", "_id": "punycode@1.3.2", "_shasum": "9653a036fb7c1ee42342f2325cceefea3926c48d", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "9653a036fb7c1ee42342f2325cceefea3926c48d", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.3.2.tgz", "integrity": "sha512-RofWgt/7fL5wP1Y7fxE7/EmTLzQVnB0ycyibJ0OOHIlJqTNzglYFxVwETOcIoJqJmpDXJ9xImDv+Fq34F/d4Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUtVYz5j518Awum8gq6SMxCoBRy1/pZYSQpgqwT3imLwIgPKWQIgwXSHtJwUOYqUN+zXQ0V4G42H6k9/1UJsQdEyg="}]}, "directories": {}}, "1.4.0": {"name": "punycode", "version": "1.4.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://allyoucanleet.com/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.4", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.11.0", "grunt-shell": "^1.1.2", "istanbul": "^0.4.1", "qunit-extras": "^1.4.4", "qunitjs": "~1.11.0", "requirejs": "^2.1.22"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "27f9d92718c78a9d377c4e5176272ceca44590f4", "_id": "punycode@1.4.0", "_shasum": "3f879ea03f24c718d4d4b7e47de1fb51cf6c3e33", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "3f879ea03f24c718d4d4b7e47de1fb51cf6c3e33", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.4.0.tgz", "integrity": "sha512-2f5mYw3Iqt8BVEvQV1c9RaaSQ9VqEjTPQpens4G9/+VNWjRFu/7ahnB3/qjiXfcbQ8PVXE4DVJXkgyhHqH9aeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5tI439C0W5PU9YqY1yW1hAxcAVlzbQCAS0kys/CLNyAIgZ+RBukk1CcY6xupAx4WHNQwep6ISvLUG+5YEt1jPDfs="}]}, "directories": {}}, "1.4.1": {"name": "punycode", "version": "1.4.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://allyoucanleet.com/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"coveralls": "^2.11.4", "grunt": "^0.4.5", "grunt-contrib-uglify": "^0.11.0", "grunt-shell": "^1.1.2", "istanbul": "^0.4.1", "qunit-extras": "^1.4.4", "qunitjs": "~1.11.0", "requirejs": "^2.1.22"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "0fbadd6e81f3a0ce06c38998040d6db6bdfbc5c9", "_id": "punycode@1.4.1", "_shasum": "c0d5a63b2718800ad8e1eb0fa5269c84dd41845e", "_from": ".", "_npmVersion": "3.8.2", "_nodeVersion": "5.2.0", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "c0d5a63b2718800ad8e1eb0fa5269c84dd41845e", "tarball": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvBwsAIkRTowoxRYNpn856NGuWz6ev3f4CLaUqAjgxkQIhAL2qaZ7H532zvmWlQOGIBgQqEAsim27EBf6tMv0jlFIx"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/punycode-1.4.1.tgz_1458437236261_0.07678767060860991"}, "directories": {}}, "2.0.0": {"name": "punycode", "version": "2.0.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js"], "scripts": {"test": "mocha tests"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "681a9a4446c75c8d5a467f3a0689775d6c88a191", "_id": "punycode@2.0.0", "_shasum": "9145b207b5228410ca17a10fe1cf4ba2c015f6d7", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "9145b207b5228410ca17a10fe1cf4ba2c015f6d7", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.0.0.tgz", "integrity": "sha512-oPNQZ+HNC8wo+rGM6ojbqVaONYfqwphn4QrZJyqZCrPw1lxcKhafaMTYz9dGgY27tfsp/I2+s+zcsuWG8LP/WQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAg9V79f1lvg6HbksVSEiEqfbqKIVUsfZ7DyxLoQ1NtSAiAprBBL6TyQf5p+x6UBNGETrv85KuKJrTFul8v9EQPy5A=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/punycode-2.0.0.tgz_1465576824252_0.9305142343509942"}, "directories": {}}, "2.0.1": {"name": "punycode", "version": "2.0.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js", "punycode.es6.js"], "scripts": {"test": "mocha tests", "prepublish": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "e29d10dbba04dd52cfa2481cc4daea3286bc54ed", "_id": "punycode@2.0.1", "_shasum": "3f142fd8e6ef4e9ce24acbf7ba869ff9b00d2c2b", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "3f142fd8e6ef4e9ce24acbf7ba869ff9b00d2c2b", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.0.1.tgz", "integrity": "sha512-xFK4f2VCQ/FaO3X3Xq5V+d/Cw+kiZAUqoMkp1khOkmrky+sFhgBJn5Pn3Jp57st0qyAmUaWGDxVxiDBnHoj93w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjNQ9PhA4VlDNj9O9Q8D2kK++jLMMzou7I2wt/AxBtEgIgAqib2wcMtEM99dvpwoBxgY86uwHMpJ2OHCziWHEzs84="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/punycode-2.0.1.tgz_1477286274563_0.6842294086236507"}, "directories": {}}, "2.1.0": {"name": "punycode", "version": "2.1.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js", "punycode.es6.js"], "scripts": {"test": "mocha tests", "prepublish": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "9aeca525bba478206c6e1b5501e063f3db7bda7f", "_id": "punycode@2.1.0", "_shasum": "5f863edc89b96db09074bad7947bf09056ca4e7d", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "dist": {"shasum": "5f863edc89b96db09074bad7947bf09056ca4e7d", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.1.0.tgz", "integrity": "sha512-Yxz2kRwT90aPiWEMHVYnEf4+rhwF1tBmmZ4KepCP+Wkium9JxtWnUm1nqGwpiAHr/tnTSeHqr3wb++jgSkXjhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICpb1wgiLZ9yOoMRTMCFCn1L5N49ZXdl5FuF8KAIBGSGAiEAmuwJdXQ8M3nGgjA5zIv8ZzoRF1EEumL8V9aJea0eZIE="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/punycode-2.1.0.tgz_1483725932175_0.5228502317331731"}, "directories": {}}, "2.1.1": {"name": "punycode", "version": "2.1.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "files": ["LICENSE-MIT.txt", "punycode.js", "punycode.es6.js"], "scripts": {"test": "mocha tests", "prepublish": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "68df855dc42d1086ada161331b3074468e8d848d", "_id": "punycode@2.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A==", "shasum": "b58b010ac40c22c5657616c8d2c2c02c7bf479ec", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.1.1.tgz", "fileCount": 5, "unpackedSize": 32434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbA4SZCRA9TVsSAnZWagAA5ZYP/0UOrnGue5Uhy1In5lOe\nh8pglPP9+qBCRx+nfe+YgmPV45IdYadH6InqCibkxAAeZ+j+Sc0g4I1Vv9SF\nPDXiA34tNt12kmLgFSEmdAhbxa87bGtvTnPCzme1iZfmqYmc7GE/q2iazWxd\n9R2im5Xr1oUwZCe0jiY2Le7HOhd+Mnkd76pdknseybWYJxf1RbNSDCtOxndO\nMsZU2CzhG38CFyPVozm+5+XEf5QD3cjKpwpZKZOxVTJ8dwB9FK5SLAvCQSGW\nO15Bili5YCxi2053KDvP/4VGMGdVxPLIp5E+uJyeZSwqVAiqmtk52iLJq4iO\nTs0B3tkKxi3Rxkk5vr0OvvOk6iIW+jkcgaQTFj8jZaGmvvGXztR5F2RDsrDr\nfrXKkahtw+W7u3eoAahCh4FgvJ3sclI23Ik2+ahQQR7B3+AsJ/hVP365RpZo\nSnR/YC+QtfzFJYwbepvg2rhaOqkiGk3dcWS53r2hUm6Ugd0AIR8Cmjuqbsrd\nUaeS+WfpvoceuU45rC9nSCPYms9bBNYBN0leYPb+okOWFbZI5v/gp86CfRs3\n8gwadUA99Z8IFcjzpiIEkmPkk3Z8y5tGXIw66FGuxVDE/tPBORGmEOCPEVpg\nJ/zsiqblJ9pTTGDKTdKxFF4fNq3koen1N4ZAIAoZqJUq7pGEzwjWy9Zw/+JW\nRaNl\r\n=ncBz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBcK2xxRA8qZpahqjEJb2iYYQ3oB5CZL1nabOwakIbRUAiEAzxUMeTgnO/0/ZBwQCOBSmpV8FR0PGra5FS51nJmaeTw="}]}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "reconbot", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/punycode_2.1.1_1526957206887_0.5799851251332668"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "punycode", "version": "2.2.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/bestiejs/punycode.js.git"}, "bugs": {"url": "https://github.com/bestiejs/punycode.js/issues"}, "scripts": {"test": "mocha tests", "prepublish": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "bff3e35710e8e6b21983da388b6ad642462e60f7", "_id": "punycode@2.2.0", "_nodeVersion": "18.7.0", "_npmVersion": "8.15.0", "dist": {"integrity": "sha512-LN6QV1IJ9ZhxWTNdktaPClrNfp8xdSAYS0Zk2ddX7XsXZAxckMHPCBcHRo0cTcEIgYPRiGEkmji3Idkh2yFtYw==", "shasum": "2092cc57cd2582c38e4e7e8bb869dc8d3148bc74", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.2.0.tgz", "fileCount": 5, "unpackedSize": 32776, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIP6Xm9mVsKhWwuUkNOwtc5pc/Av7ddxyExcEtsmu6agIhAK3KDFLU/cDgSMbTHdBIc3UORTZ9ZouNqt8+X+ZYp/aS"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvWqlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocZA/+O/UPiSnw5F69q/vUnwACizMjqUr4Pf3JGsicH6GYzJlozg3Z\r\ned6DOjbc7HGyQBWWqq5dCXRZ18SPdwMV9378ASAA2XBZtfh2UbmdhRTNd82P\r\n/VKcIQjoamlulp8nJ8pV7LSFlpRVaE8auDTXIj0l/8PReZz0q7ysgpFMyTel\r\n/6NGAMnZ2v6e7vDJLgQY1xQRUxzBlPWnbRJue+O1DpPrySm/46tfmr0z98Bf\r\nH+XjXYbkdOb3KuL0IWjz2iugo72+KzHr7z/eTzLlix5h1aK08eJ3VA+mG+zA\r\n47O4/IDIkZSGh6FKlSCVVhpbFdwKv7+C1PTFWHnDo6OpFrfyYvBBLLiuIHyI\r\nlMwjXxfusSeaxXz3zew7kNKOgkkRIWYeypk8R+FtceEsqdDbk8xaSE/TVWCK\r\nf5Earx4ow0bvLlo+kKj3PGTcJfV6jHUJxP+r4rudNq0FBnkWsDibIeAbUcYm\r\nqp1x5f5ImZI8ubL/c9ikVU5uba222r0Kg8/dGU8C29U0bHF5/9StWAMBPrTH\r\ns5gMSG3P+sy+1otCLTQeS+Rks1jthE2FxoGTpNhvGy5Aab0kMQ5sN+VxMqzy\r\nrFShr/BRCX3Wfr48vZVGI7CB+j7lSxyRY/UzaoEWyjxckupFlDlugnPvNHkM\r\n4tGrDTolKKNtcKnbED63FB+g9J5EcgSkSHc=\r\n=O3hv\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "reconbot", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/punycode_2.2.0_1673357989209_0.2288738528869998"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "punycode", "version": "2.2.2", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/punycode.js.git"}, "bugs": {"url": "https://github.com/mathiasbynens/punycode.js/issues"}, "scripts": {"test": "mocha tests", "build": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^10.2.0"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "b77c249baff49fadfcc91a7c0abc5d5160dfa6ca", "_id": "punycode@2.2.2", "_nodeVersion": "14.21.2", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-vKAcKbHEGA3cKbkNWwsA9MjHgIg4/X/xO/PnVQ/Pxt5eNC8/F726fGNlUlwwPej7NkmTg9OBWP4ovYvtdPtmXw==", "shasum": "082313fe5efe0aa2d931d7aed15b99315c24e548", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.2.2.tgz", "fileCount": 5, "unpackedSize": 32931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrpuARtCeUj7cfSn7bcQmWDWQpntWkH+jr/fntODSZvgIgUyzmGnLrY1vO0sdmr+SBJ3SV57L3BMwH3pLKgyBBMfg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyW+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa7g//cjk7hTRa0D9LEMyavWAdRTBg6MIoLQwWbXWSVzkzlS8oWqkz\r\nu0l8/tBoaEL7nkI+AQMJnYAUgjFIGfbfCUrXg4WsNjgbnE7hrv/tBlx247xt\r\nLBQDsdmHnL3dbVBBS/6vq0nzkC0Dk2jri698GAAguNdRUkheMrw1L2dgc5kd\r\nmdIQHnScmW2xulflbf0xkE6GVYTfuUCDBVE3JERvYyGT/LVMMbtpmcFrU1nn\r\ndLnZcCzcwkgN/tcqr/TC4P3hu08lbGteGt+q04+YO4IGB4PaVlDQua4tOiwT\r\nIWmjMMJoNjKtddl7Cxr9wmJSOnJWRszXy0crRjG96WXxArsiszuzfhjwki3I\r\nlYu0LtEPMFhlt8uN8FHERg6TJLvLXv/q1i4VV/aJ8pIJmxWqKByE4iJDBM+Y\r\nt+w7AUDVWlAuQmyxKo70qV5NJi64MX3LUIBJUceIihGNgVfoBnMkLjP9v4ga\r\nIyxwQ2gPMIHtV9Id6+mChWFX/8QipI1sIffPW98U42B/rsEtGtu/pW/HzUPS\r\n0fiQQnQvGlPlV1vUDcu8avz8YbVRVL6S+jsyAUThKXa21a5D9zxF4rQzclNa\r\nYXT2gOinsoUYRNLX/uGxknsQ3/p4n4TosQKp6xr1lhGUdbpTmujrdhaT6Mbi\r\nPg1sArfJaoRFXA6o3t+uu9Ryj6lneKEx7lo=\r\n=kxZR\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/punycode_2.2.2_1674145682637_0.6545097547211449"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "punycode", "version": "2.3.0", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/punycode.js.git"}, "bugs": {"url": "https://github.com/mathiasbynens/punycode.js/issues"}, "scripts": {"test": "mocha tests", "build": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^10.2.0"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "gitHead": "44e6200b96c186ebc5de72dcce2bcdfe8a32bfbc", "_id": "punycode@2.3.0", "_nodeVersion": "14.21.2", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==", "shasum": "f67fa67c94da8f4d0cfff981aee4118064199b8f", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz", "fileCount": 5, "unpackedSize": 32931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7etdrakClReF2eotN7CploeiUvyNk3JG2EwoHyp8INAiAXV2SXP/IKaluu8vQ0Y58UA4zHNTJXqSTAaf1tN3LCvA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyYSnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgOA/+NTfKcCQhRUufecHdwepK2L1WkMZ/I3oTQMTT+1pjBURKI352\r\n0BC2iC7CK5V60JI0KXxDvql+zD/HvAg06gKs9jbUstDshIrbjoESPy3+Fd3c\r\n058ZMX8+YCtA6LiGpsFZWX7gcCArpzj+l1LQidy3Mvep99M3xpOoXDcb/Rjl\r\nnY7hZQjN9Pd2WvMQIWWEeirGkXzAQ+wM5ASuTmqnNr8qxNlJ0uNQhFK1l0+U\r\n4P+1tSCDKUpVv1OTQFLzD39rd7lIOu1zfzth68i/ustADPrWHwU48EgGfQsQ\r\n97gKduBk3jN7ZRfj5AN1v59Y6XXtmLF4K4qsabbdzEz5hsw7z2Hr01udxkjL\r\nlw6wfBn7C0Y89PL3qH0ALhSS/4IL1DXUMfbRJcbaF3/MkcnglcH31EElmrld\r\n1THcl4dTrqe/GuDePY5LSV4oO/zrvdA3eNOUPq46K2RuDloXFNyp6iafsqUK\r\n2VLKbohIhTyu5ooRp7rC22JSi0JKRrC3GI/C7BJGlS40fcfgy/ogFpZ49trq\r\n2WxCL3iXn+gJU97I2yyNw3kFtQ183p5goXcXpgbDtkP/3taAP5pPz35PqnwK\r\n1vueCRcH0lJAypULbQj0+0EsLJlEV9z4nZ7+TIBwyR3cBQAwD37nGrwhBdto\r\nEV7m5YgRguo6aQYCSQLsDS+JQAUyxxaZi2c=\r\n=41yH\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/punycode_2.3.0_1674151079389_0.310718101811883"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "punycode", "version": "2.3.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/punycode.js.git"}, "bugs": {"url": "https://github.com/mathiasbynens/punycode.js/issues"}, "scripts": {"test": "mocha tests", "build": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^3.8.3", "nyc": "^15.1.0", "mocha": "^10.2.0"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "_id": "punycode@2.3.1", "gitHead": "9e1b2cda98d215d3a73fcbfe93c62e021f4ba768", "_nodeVersion": "20.9.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "shasum": "027422e2faec0b25e1549c3e1bd8309b9133b6e5", "tarball": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "fileCount": 5, "unpackedSize": 33514, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZvXa/+EopO0I7a00fXXIlm21laKpF9NAascJaEmE6gAiEAueild+KrVfZMTffBSXj1LfptDjmXF437Aet8pmmV9m8="}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/punycode_2.3.1_1698690512274_0.9133580553307208"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "time": {"modified": "2023-10-30T18:28:32.689Z", "created": "2011-09-07T03:53:54.173Z", "0.0.1": "2011-09-07T03:53:54.402Z", "0.0.2": "2011-10-26T22:21:42.683Z", "0.1.0": "2011-11-13T17:01:18.347Z", "0.1.1": "2011-11-13T17:01:43.406Z", "0.0.1337": "2011-11-13T16:58:36.944Z", "0.1.2": "2011-11-14T13:15:15.136Z", "0.2.0": "2011-11-17T20:46:35.728Z", "0.2.1": "2011-11-28T20:47:36.225Z", "0.2.2": "2011-12-27T14:06:30.675Z", "0.3.0": "2012-01-03T09:05:18.020Z", "1.0.0": "2012-02-24T15:02:54.438Z", "1.1.0": "2012-06-27T15:07:09.317Z", "1.1.1": "2012-06-27T15:43:09.363Z", "1.2.0": "2012-10-10T12:24:07.234Z", "1.2.1": "2013-03-31T12:04:49.515Z", "1.2.2": "2013-06-02T12:11:21.440Z", "1.2.3": "2013-06-20T10:33:05.742Z", "1.2.4": "2014-02-17T06:07:31.240Z", "1.3.0": "2014-07-03T10:59:22.173Z", "1.3.1": "2014-08-08T12:57:39.100Z", "1.3.2": "2014-10-22T12:20:28.551Z", "1.4.0": "2015-12-11T12:41:32.747Z", "1.4.1": "2016-03-20T01:27:18.427Z", "2.0.0": "2016-06-10T16:40:26.846Z", "2.0.1": "2016-10-24T05:17:56.406Z", "2.1.0": "2017-01-06T18:05:34.286Z", "2.1.1": "2018-05-22T02:46:47.304Z", "2.2.0": "2023-01-10T13:39:49.392Z", "2.2.2": "2023-01-19T16:28:02.797Z", "2.3.0": "2023-01-19T17:57:59.553Z", "2.3.1": "2023-10-30T18:28:32.512Z"}, "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/punycode.js.git"}, "users": {"m42am": true, "wenbing": true, "simplyianm": true, "remryahirev": true, "nickeltobias": true, "tobiasnickel": true, "ahmedelgabri": true, "netoperatorwibby": true, "ys_sidson_aidson": true, "klimnikita": true, "kevin-foster": true, "touskar": true, "jdalton": true, "hisorange": true, "flumpus-dev": true}, "readme": "# Punycode.js [![punycode on npm](https://img.shields.io/npm/v/punycode)](https://www.npmjs.com/package/punycode) [![](https://data.jsdelivr.com/v1/package/npm/punycode/badge)](https://www.jsdelivr.com/package/npm/punycode)\n\nPunycode.js is a robust Punycode converter that fully complies to [RFC 3492](https://tools.ietf.org/html/rfc3492) and [RFC 5891](https://tools.ietf.org/html/rfc5891).\n\nThis JavaScript library is the result of comparing, optimizing and documenting different open-source implementations of the Punycode algorithm:\n\n* [The C example code from RFC 3492](https://tools.ietf.org/html/rfc3492#appendix-C)\n* [`punycode.c` by _<PERSON> W. <PERSON>herer_ (IBM)](http://opensource.apple.com/source/ICU/ICU-400.42/icuSources/common/punycode.c)\n* [`punycode.c` by _<PERSON>_](https://github.com/bnoordhuis/punycode/blob/master/punycode.c)\n* [JavaScript implementation by _some_](http://stackoverflow.com/questions/183485/can-anyone-recommend-a-good-free-javascript-for-punycode-to-unicode-conversion/301287#301287)\n* [`punycode.js` by _Ben Noordhuis_](https://github.com/joyent/node/blob/426298c8c1c0d5b5224ac3658c41e7c2a3fe9377/lib/punycode.js) (note: [not fully compliant](https://github.com/joyent/node/issues/2072))\n\nThis project was [bundled](https://github.com/joyent/node/blob/master/lib/punycode.js) with Node.js from [v0.6.2+](https://github.com/joyent/node/compare/975f1930b1...61e796decc) until [v7](https://github.com/nodejs/node/pull/7941) (soft-deprecated).\n\nThis project provides a CommonJS module that uses ES2015+ features and JavaScript module, which work in modern Node.js versions and browsers. For the old Punycode.js version that offers the same functionality in a UMD build with support for older pre-ES2015 runtimes, including Rhino, Ringo, and Narwhal, see [v1.4.1](https://github.com/mathiasbynens/punycode.js/releases/tag/v1.4.1).\n\n## Installation\n\nVia [npm](https://www.npmjs.com/):\n\n```bash\nnpm install punycode --save\n```\n\nIn [Node.js](https://nodejs.org/):\n\n> ⚠️ Note that userland modules don't hide core modules.\n> For example, `require('punycode')` still imports the deprecated core module even if you executed `npm install punycode`.\n> Use `require('punycode/')` to import userland modules rather than core modules.\n\n```js\nconst punycode = require('punycode/');\n```\n\n## API\n\n### `punycode.decode(string)`\n\nConverts a Punycode string of ASCII symbols to a string of Unicode symbols.\n\n```js\n// decode domain name parts\npunycode.decode('maana-pta'); // 'mañana'\npunycode.decode('--dqo34k'); // '☃-⌘'\n```\n\n### `punycode.encode(string)`\n\nConverts a string of Unicode symbols to a Punycode string of ASCII symbols.\n\n```js\n// encode domain name parts\npunycode.encode('mañana'); // 'maana-pta'\npunycode.encode('☃-⌘'); // '--dqo34k'\n```\n\n### `punycode.toUnicode(input)`\n\nConverts a Punycode string representing a domain name or an email address to Unicode. Only the Punycoded parts of the input will be converted, i.e. it doesn’t matter if you call it on a string that has already been converted to Unicode.\n\n```js\n// decode domain names\npunycode.toUnicode('xn--maana-pta.com');\n// → 'mañana.com'\npunycode.toUnicode('xn----dqo34k.com');\n// → '☃-⌘.com'\n\n// decode email addresses\npunycode.toUnicode('джумла@xn--p-8sbkgc5ag7bhce.xn--ba-lmcq');\n// → 'джумла@джpумлатест.bрфa'\n```\n\n### `punycode.toASCII(input)`\n\nConverts a lowercased Unicode string representing a domain name or an email address to Punycode. Only the non-ASCII parts of the input will be converted, i.e. it doesn’t matter if you call it with a domain that’s already in ASCII.\n\n```js\n// encode domain names\npunycode.toASCII('mañana.com');\n// → 'xn--maana-pta.com'\npunycode.toASCII('☃-⌘.com');\n// → 'xn----dqo34k.com'\n\n// encode email addresses\npunycode.toASCII('джумла@джpумлатест.bрфa');\n// → 'джумла@xn--p-8sbkgc5ag7bhce.xn--ba-lmcq'\n```\n\n### `punycode.ucs2`\n\n#### `punycode.ucs2.decode(string)`\n\nCreates an array containing the numeric code point values of each Unicode symbol in the string. While [JavaScript uses UCS-2 internally](https://mathiasbynens.be/notes/javascript-encoding), this function will convert a pair of surrogate halves (each of which UCS-2 exposes as separate characters) into a single code point, matching UTF-16.\n\n```js\npunycode.ucs2.decode('abc');\n// → [0x61, 0x62, 0x63]\n// surrogate pair for U+1D306 TETRAGRAM FOR CENTRE:\npunycode.ucs2.decode('\\uD834\\uDF06');\n// → [0x1D306]\n```\n\n#### `punycode.ucs2.encode(codePoints)`\n\nCreates a string based on an array of numeric code point values.\n\n```js\npunycode.ucs2.encode([0x61, 0x62, 0x63]);\n// → 'abc'\npunycode.ucs2.encode([0x1D306]);\n// → '\\uD834\\uDF06'\n```\n\n### `punycode.version`\n\nA string representing the current Punycode.js version number.\n\n## For maintainers\n\n### How to publish a new release\n\n1. On the `main` branch, bump the version number in `package.json`:\n\n    ```sh\n    npm version patch -m 'Release v%s'\n    ```\n\n    Instead of `patch`, use `minor` or `major` [as needed](https://semver.org/).\n\n    Note that this produces a Git commit + tag.\n\n1. Push the release commit and tag:\n\n    ```sh\n    git push && git push --tags\n    ```\n\n    Our CI then automatically publishes the new release to npm, under both the [`punycode`](https://www.npmjs.com/package/punycode) and [`punycode.js`](https://www.npmjs.com/package/punycode.js) names.\n\n## Author\n\n| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias \"Follow @mathias on Twitter\") |\n|---|\n| [Mathias Bynens](https://mathiasbynens.be/) |\n\n## License\n\nPunycode.js is available under the [MIT](https://mths.be/mit) license.\n", "readmeFilename": "README.md", "homepage": "https://mths.be/punycode", "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "bugs": {"url": "https://github.com/mathiasbynens/punycode.js/issues"}, "license": "MIT"}