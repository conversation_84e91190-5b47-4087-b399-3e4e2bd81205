{"name": "@tybys/wasm-util", "dist-tags": {"latest": "0.9.0"}, "versions": {"0.1.0": {"name": "@tybys/wasm-util", "version": "0.1.0", "dependencies": {"memfs-browser": "^3.4.10801"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-2hQGR2pmvsdjVVhk51tiNAZ4j+4qn51PeOKrAEhm9rQwvr2YcqIZWWECf+hn9Snt0Ja1S1GkW3Lw4J8QbIxzDQ==", "shasum": "933c4574ea1ba497bc1f9b4365642615c96cd94e", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.1.0.tgz", "fileCount": 35, "unpackedSize": 1090810, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6w/7ZnnQwRKTDM+3N2puKSSbsMrktwEl1av63/GiBXAiALW8PRm1oQt/I/T6sLVd4osUQdKE4uW/3yoPH4T7Tt8Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjW+I6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHSA//XgxwpKUxns68FZEuEfyC4XsgA/l4FZ8DqPNMTQIFvDEnyhSJ\r\nfTG3Lv/MjMChmeAd2F+aa4c6nif3p7L13BtOvCD+3PV0vTT2YDcMZwBMoWAB\r\nNvZjQ0b9eIlDt0lqd6grh4HcJ00llBmc+NgBDo9bhaEGhtmOCU4g/iwKR/8n\r\nqnk5ESr7hCK5aXwTQwFrnAWSizrB21wxttTxzbrgLGSf4n378PFIcTLlsEQ1\r\n4wp+YeJ5mwUzhervth5xpjvbzVsnnCuKGCJ5ms8fF68jGeWiA/HmRb+4aDCE\r\naPm4lumbs0cC3LSLIYbfd8Vj5c/eiQTv8tGeQApvcTM7oNpfuIO3Sdmae2C+\r\ncOz44NU+bN9ZdbPLol3rlfpvayh0s5urt4gR8T9uVdcKNO0u4BfLJFFdoWo5\r\nggQznyiDaI4LlztVTKuTn3mDs2j4hq7OaTCWFjgg8yXtxQXaHcx5z1Q6dWS7\r\n/VM2+D0DeHdwxeDnIfyVgbs/qVDrRxI6zpX865Yw+6wJrMn+SeeCQLa4wSwN\r\njjrNOc1XzW2LH5agKqJPJlqC5sBQdhTODGTVgmPMPU3stVL+DDdTCy7UXUy/\r\nI+2i/rN72OMbfRveQoAOWQKzXU/hSayn551aYbALRkRnMUpSKw5kRj5aIp3l\r\nZPO9/V/BB60SG9SU9mZAL4U0elyezeKYtqo=\r\n=ct0Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "@tybys/wasm-util", "version": "0.2.0", "dependencies": {"memfs-browser": "^3.4.10801"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-if0TCxCiFI9qSt0po1oDqSDdQGDSuSU3pfrtJtqb5eQkFkt2a0DAV9xtdvkmJsSwg6T7397hAvanCmTIXL84+Q==", "shasum": "2c7bd6c0ed34a10db0206019795942673e1dc699", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.2.0.tgz", "fileCount": 37, "unpackedSize": 1096088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAPIdiNew5helXlod5+g4raTGD7sHkdO5OpY4pGDTIj0AiBwEoHPrIq9nbF3xMHGm7iTCjBUY7RE9RDQa+3/OiI8Tg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYRoAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoC2A/+Lz0ET5Vjs+apFAOVE1rWe+NyQjD66/h0IYEC0lQJ1oNuEvY1\r\npEXFf7XfhNng5BZVILjpYqyZpuSYiUzQHNOGcMLCHnNvzgjyP1dYqQr1kLpf\r\nXF6gKEkcJk3wTEjgQWwe29/C9HR6NMziuqttbZshP2rhBYPZFF6Ew33SsPNA\r\no0qZy9qkzD7OX6S77FEs7EyAFn95WjY1eUWDFIUwLshy/dFUURRNKZXisBnG\r\n1ZeiaITt8RRf0t0M2B5YglvOout+7TVXUlWcXhO6Cn5kvh55keE4dmocXk2v\r\nFYxUqEQPevN4sD+mitRXNOE5fdpjrNFbp7Udk4OSTE3ul1RnrWGTV9yS2V9Q\r\nAh4ZSz99om+D5vVnT5RCyxhwQOFUueG3izTSjE5aCtM/zmiy7lWTwxLYF2l+\r\n2+J5s2MtXhpwOHX5SfRvy6ZwNv+HrwZ3KLVOPMxCyr4SCSRpOhuXOOsbGe+0\r\n3QRZZznotoVGqYV/MuKP6gdrQwzE1L/yhoQQWEQCKudb14DvcA1SN4Bs1HUN\r\n8AEPmpWuvlsQOFJndZqskpQ/2Zarhgeb9V1UJbIMCNZ+YsI8lIQwOn/XGNMC\r\nsen2SEj8fAI3s1J4zUSd6svQKFY+Jzyp45rkaKNoD+oXEMDx+Bn6REwZs1gJ\r\nkyOWB38DlC1Knii0eLX+0HQbjTOagBfR+eI=\r\n=4yKB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "@tybys/wasm-util", "version": "0.3.0", "dependencies": {"memfs-browser": "^3.4.10801"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-LFHImIkjMh537Bomwd9vJlwMS9TPuhP6PRRmej+U2Fh46s4SW8Z5qYD3xqlnQzlC2BOwDI8y35HrHBwHGk7TfA==", "shasum": "a00da9773af8cd2321449d9c5d2a58f9b14e6875", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.3.0.tgz", "fileCount": 39, "unpackedSize": 1108360, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqUPivuE2aE0SlzHB0Ya4myZ6E3DfwMw/MLXJIMYFmygIhALIvHADfnXBoqZxdbJu9Hk+jwDA7FtuoxGk4EjcWALYo"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcLb3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8ag//cSq+MtaycwjrPFeaQYtt66UHzBeXCaOHMjfMR7OAM1BJzK6j\r\ni86if09Zpe6JLl9xj94/5g/bt0EjJnd4P95Od2T1zIa7DNEaGzXVaLn9/88c\r\nDvF+1EIzYnkz6bN8MFwX8S0PNVf5GO6L/Xz5e/f/0qXnEHcRUd+Vf+vNlk1+\r\njrRSkvPC6qXoopUNM+kcfS65OJNHtAe+SKhFv5YrU0iP0w7ZMUSc1iObn8GZ\r\nUQCv8Qh2+4vg+LR9QtMYhoHiHCb6E7R3E5KAJD7JWW6BMpbzmLLB4g5BFYRs\r\nO/fjJegFs3cYycn61KSCBzhVLgqq+Bt63C0g2QYCJfY3okoZ6lniOHG5pNQk\r\nL+14HGZvPtKVvxblkYcq/sT5usGR9Cz7Se2RTFLU1bk3sl38+RSQ+x61kj8H\r\nwS0iOw7JpL4kn6ACD3MkAcxdW2W6aN1F7dRLVfBuRl0l7hY2Nt5rZoW9iy2n\r\nKt7vS6iKCVS+s8fFSvCg9UUu+TAWR1LrvMIf5hdh3kH/W3qpsWJrP3YbSh4t\r\nEaxoxnU5MMdv+8KJdc5Vvd0YbjBt2Bf81HADf6exFMOjMcKXVryXJHNgFFzz\r\n8jpnPcXT0ToDE4w9fGFobZVZMTGlUOkHbkLrDxu9+GFtYXzlscjiUrzh+7Mk\r\nm0s4mnPJXMm6lrZDlJBbtLSfzzVPb8j6zNw=\r\n=KEse\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.0": {"name": "@tybys/wasm-util", "version": "0.4.0", "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-IpHXomh5v05z2lD03LP3x4y19CwOMcN9xrcdxveBsxcgiN3e5V3VLWB0OYs/yj0/hv/u1dJCMbDeiRWRRHnIWQ==", "shasum": "302686a56b20f4f85dcf16d5c71c9d2fe84df636", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.4.0.tgz", "fileCount": 41, "unpackedSize": 1118502, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA5RMoNvg3fpCGpwozKW/fdOu141SgNn7j3Z7BqNaSWfAiEAu2hSITsGl6ynqdz2bz3cg19dz6kdwSiYFoOT/U/xX9k="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjchrOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpI9A//RxUq5/3wmziQXr6fwzXMTdJdaaxe5ibJ8MSt4R/U+715QXpf\r\nxJZjISWXfzBGZexgvbQHrNvmHhvjBhEv7h5lm1hvGYchMHSG75CKZp5/N1sW\r\n2T307mQXY+pq6jF3t2WAwCUOlhrmcPhbu4ujLo5oPTpYKuVOHQuNGk/MzeG9\r\nHCwmKg7gHcZiTAILKnS1I6PQtCtP2oYn0Dh1hPj7odCNh/IikYVE4PbEL3md\r\naz/tJA/G9pretqI9aUZH1ItNhxTlQdKDgM44I3IS4RRvxb7ZtnNDMOjqTgqh\r\n+S6Jl4ArcUr0yyZTIIQgFivldzG5aZ3DQMT7Z0e+ySe34B3CQP3Op5zxm1it\r\nbTVbxkRCkKw1GE/bLCaUy1Vy5p2CQZWpiEEFoijPyG3ByI7BEN0bphJKkqGz\r\nqi1/vS46xZSZWWvHuZEzwB9MflWPM2HnCFmxNOX+iNKS/U2N+6o+nZ8F6oJG\r\n2fF9+n8btfNj1vQCysEQhXzhH4GpWr10KqMyA8sASQHW80n8ecu1U2WsX6Io\r\n7iaDmNS/MRvVelhvMqXVtLRpGQo8HfxyN6GgCa+pSYIEz1Pag4KPjXpVIz0g\r\nm0g8ihp2dPhWwmJ8gC5W2stor7+1RHYAMtpG0riBFWO7yipKeWu2Pi8Q7ZdU\r\n+wi8w84mkqq5FaUkAFeJPwZp+yeBsLZeWjA=\r\n=T00J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.0": {"name": "@tybys/wasm-util", "version": "0.5.0", "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-GLh0zhlOvBYfY5dednSgvNh7Np2Xw3PVh2XOxeAzlUm+HPpgVf4t9+7kpepoZwroi5ks2TaHdVibV3uVQOa4Rw==", "shasum": "226315b7965c822fe1e6aad5e5ef7672f23c492c", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.5.0.tgz", "fileCount": 41, "unpackedSize": 1283980, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGxCYCkEQ0Gi/LOvcEtTS1L+1sJdDf91auBaD6/oineQIgTUvusRayN45QiGqRMsZo4XfKqdMUkbwTCBypwk9A/aM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlXbEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrM7RAApF+aPGkfOHjCqfikJSKbxtA2ovyOrHwt+Jn+yMBI1XAj1LoO\r\nfm4uHG0qlTO3xc+bJ/cuOo9Dlj2o4GG7b3IUHelKW4rvROS+INjtSU8INGtT\r\negzfFVHKEL2KWXkzV2Ovj/4WGTmTOL6kJK4V3DAU5nr5igTNYs2dpD8ZIOrC\r\nhDmat/xV6Y9PGbV2tOGfjwi4Sb8JwSjqncCQeMSRGA/zgWnD5N72dnvfdfzR\r\n1BiS0TZP4zSbUYl4WhRkcxnD90g5yR7tiwT2Gav13oE1RkI2J0GigxfDBXhC\r\na0RLLmp42iRIumSAOg5WLz3dPh2SedVKMB02Hy1BvPd0kMtT5K5ZhKPuHivJ\r\nk0hTiAtG5Y4qTqiXmO3BqYKAzxhDDboRNrZjiTPdSb5QYSRImngDLKfSGJVU\r\nkb79QhR4NhL6AhJrIUWH59fdQVVhHrLsQbXMlqI/A87JdVK8/np5N0AHYtUb\r\nRRgKF+QO0Rogi2LekckVTyEXqgGDP6t7WAmeTfUEY9b2ahmSagNR69jTbxjl\r\nIHyhzdNrycQgUnsPFFiyh85cbsTzvfdE+4zmdYN4t2KCeQ+G8TbnlXmpuRHh\r\nk3VzUPZwZlYfUQCGI13zh+NMsJk9uTjBfVhcCkeree1d3dQLV8yZUs84a92c\r\nE7saFKYnI5m4s02A6LdLk+Avz4tbcDg3it0=\r\n=Unx7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.1": {"name": "@tybys/wasm-util", "version": "0.5.1", "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-vdlj6jTnhcHPUhbDfrLeJj1XxEKDvkH8ThQJksU08KswK6aK1wJ4TouabBZ+4NPw7CvyDVNiU+HphprM1OyUow==", "shasum": "ff5461119125271375ff0999d208443244082f95", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.5.1.tgz", "fileCount": 41, "unpackedSize": 1287579, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQy2phlZuRbd1ik1Wc4gdot9ymA9vUr5pfzmhWlesfhAIgKpQBZzhetJFZApPb0L4NxLs8EtEqJdBwe/thc6oeEZA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlYdRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFPA//a4cyfgSxdADIYqsPP5p7QMNs6xZUVFDzwxoVEoWYq/1Fcmkj\r\npHgxuWLWYU1UOy9CuUZE57otkTFozuoaEo70TN4xeZZ045mK3YBPuYhBeaul\r\nxmiR4JtkNvE6Sp9mW8BQaT3dUBKx6zWLKUrxFx2Z896TM9JZGtjbKWERuwpi\r\nEF748/4nvSuUgN0mYc/M3uHP2GHsk9CmmWCt1EG8HIvh8g/NhVSLblFMeUBQ\r\nTnI0aEjAyN/nPtuv+WfnKEgjJ1kPPHHmN2wg0OZKlLylWpzUfE8qp4cUBuSZ\r\nl8w1FJAQ/8brLjORQ0CmQYhRxcb4lRQeGNoY3qxjtWBx3qMbVDXnJkO2bxK1\r\n/+WwuenCl+cW+ujobapfqwXb0xMMr45JuYjaiDRXnKMdsq2a0rDeS/Apo9kA\r\n719fmmtnH7PIjeX/hSIdt5s3Kg/qOTc+BOZvaGDXuMmYdncyKbs3z3onHF88\r\nZrtZn1XVQfTeKrkH7YyFmKadBhlL7zE61uDuGAEOKIe0y6uJbWB2LS2vzKJv\r\n5+E91RC8m1AuuewSAiH7Zf4RvpXnUS+XtefJ8rhcMkpdX1lUyj68YW3HLFq5\r\nTiY6ZMS8Xw4DliIf6AfaDBRs/YEF/r+jxif0wywrN5FChTXabFJHfl2oolCC\r\ntyiUTnglQ/urSHD/pDMVkx+8uHMool4SMtc=\r\n=Vjq1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.2": {"name": "@tybys/wasm-util", "version": "0.5.2", "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-WfQJur2aBh6zY3zYd+WMVldWgLdEQbMsiOqtDQPW0aYW4euWAFDea2zWeMX6waqYL2uvSFLUHFzMVsom/Ywwwg==", "shasum": "892901cddc1970318789b71bfe3078bd422434a6", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.5.2.tgz", "fileCount": 41, "unpackedSize": 1290488, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNliZrUsWl5HA5kUfcdeiJC7aCVpfmf1Bdyox9cYHFKAiEApwfSmIw0IxV/NL5dRgpmy7YuTQa6LGIziH2Ot8wAhMo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlYj6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrweRAAi+DdvnOMx2hiTUZyYimglVYe7OqnJ3ky+xwdS73hgNOGk4v7\r\nCYRovpAIB7OZ2UvLdW3bYdEHfuVOXe8H4bpOmlG2PGO4J1zOhI2Oi0hYowc1\r\ndSoLKuIY4/DbNtHsNgpI8hf7yBbiF9oqQNIynquhSDFrQzUzqk4i5cgwm4+K\r\n8yIhSxjZ6l0Y1R/vWWkLVb2TUHCUlU0ShGmboitfwN9kKN4CXvQqWFXoXSTU\r\nsrVMSyPYzpeelh0yd7OXyxCC0txxeQoh9yoCO+YZaFO5y/K+1l+Ni1kB37Ak\r\nr929ePNs64Ep1gJHiXybrvGjKXWMHFwvfaegcq0KRh0uOvSGYyDXQlMO/Zi3\r\nJJaCJF78hpbNIH9i2A3P0oq+f3JFC/hLuzonAFZ1Le+VeqhAyXMJert335pe\r\nWJHBAnJTYL9lsz1iNmGgDgNeVdF647h8hwfRaSHO8ccd489OSQMx3YqI7gKi\r\nL6IPtUQv4pB21JWpUvfYGOBL0wT+Iq99S5jLy94dxE0QyCRnf8I4zjN49+R6\r\nWDkkkxqLGEfn6rQKPcy2yTLMsrauVaHaAJRrylzrBZiCD0RSwzeU6byFI+6s\r\nNijztJBsC4H7Ep3lzwqKQxCzwOam1fja3td/bL5rVC5Os3OflMqYh8Ppt4xL\r\nsnCYuhQu3ad4fc1geU169/wgaQuVIVMBmrM=\r\n=8v/c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.3": {"name": "@tybys/wasm-util", "version": "0.5.3", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-J+oxfyqk9l41s3AKLIlyaWYXGhuqbBhxWqEczwsZukQJqdJu9xkE7OTjBLemw6FoNaxxYzOrhA12Nke1E8bHOQ==", "shasum": "d56550c6cf89f8ceb67aee3a3097963a8355dbf7", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.5.3.tgz", "fileCount": 41, "unpackedSize": 1290886, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMKrXawP0xCfxyg7tm7T5PtKYe1Bn04hRhX7decqRZFAIhAP/xYPyx+rWMOZrbqD1RRCDcfwk2+TS1jtyceOGWvAC3"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlcyYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAnw//fd86AVUoFq53qOHmP8+MV7QPEhcsQqPy+NVZgWXrrE/bq5WT\r\n9XFfOoSwFPoSvn9ogWF1B45leuvf8/Nxx6lUHnprhycJU9lUECgUC+mXhUG/\r\nfnEgi7hfpzoD6TVD+ozNBN+TfaSWVgE/LlYEZGdNgmQ/f3IYKLhhUiS/WSEO\r\nwL7XOWEKNUhXK+64sD2JRw2RlLYWJOyue/QOk3XlkiBWBpxUrLa6m7INrhVv\r\nDilpeHRNQ0AnIyMdYPJZVNyCOoWQjxJNmfxWr6NPuBEoUDHw7+OlhY74blFu\r\nLqYE9IBiQaND5t/VL6hXPFal/7lcpSNUQiFPLxmr3nyDyK/zamrLyNCIHxpq\r\nO7/2aPfpP+nqdCuKHUUtW3+o/CH4O8MsiaPsI3MOCVbPNoSYTa4xC7jdf4QB\r\nuz1hClF0M3wvrG7713Abi1UaE5kFLMnFcJAgcSh8eUNgwjkWUh+itBRLn6X5\r\ndte8QSVifEd6n7HqFY7HqWIHwVykZF2Zb3eD9fBW9nSDZXcqblnHURDm9eur\r\nqnIwwqVtLXOHXHaBUozjoSlA25lTX/1dVRpDhZLgM5SMT5k4wFgySXJ01sH6\r\nsUz7FZfEju7CzG9hEmWHc5qj9ptGG9lEqjQY0TMZnjSUR/7r6TYzr88mQJr2\r\niBBdl5lNsxTIimcK2QXecgr6eDX9Rfm6nzQ=\r\n=czou\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.0": {"name": "@tybys/wasm-util", "version": "0.6.0", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-lLc3ere7iAmJ+Sw4WnCM7KBfsZo5jZo+ssxW0FcBZ6+JqdQy/UPXzideWloDP0S1wNJgNDto5RlNCQ+MjI2SVw==", "shasum": "6ea5a76ae2289dd55cec4189b6bab43630eff71d", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.6.0.tgz", "fileCount": 39, "unpackedSize": 743861, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDidcrds2Vszk0yNNjgMTzd2tjaSn52krG1gNp9vIVzaAiEAi5oKABuXJcQTDZjEQOHjVBRWJHznPqtbUk6d5IRB5gI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8blRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp25w/9F8a7Cow80WQhL/hFCflrI53ZSAEX8Rfyp82++VSJzmEa9Rk0\r\nQ4VSThml6sioMTan7iyoHE6UU8KqhOtLqUSIBbdSyNOfNqLgd/LhEN6w2+M5\r\nLQm+aHeV8eAkrTj7MvZMX6XrbfbHw1MkvKaOQ9Xn4rhhAYhDzSmg1jH/Qcg+\r\nnZdMn7Z61ACj9NEwpwmt0OWB7jYFW/x8mYsx/adX4ZkYNQpLVTsWTvf5fkH1\r\nbQZt1zfK7aBF+Dm1s/Qffwa+zC4Q1wrZ33zNCHQbth1dufq3l9Z7OrRUrnkK\r\nfTUfSxHH+6fYoKH9LJWo0IQQxnPRr0z4Es4eQ81XnNRf/G7p1gUj2u3+l3o3\r\n9KliOkKfgLuyP79NBqqCaYOVWjyPbWHUeEVR1viAhj5HYHDbLdJrrIkF5s7z\r\nK7bMcSggje76DCPWhle8O3eauF7b2UGW6QI0JZ822n8N98vl/hrTm1bJJmOP\r\nRXoTJReT8AaeKnp3iIlDkrp8KBZMD41z2TvLEU3GvrRKmBZNV1NJqsz0Jdgl\r\nmAlmEW6agkE90OkYgBwAxw7Q2rMc5rstt1u3yKDtWp6VPzjl9REOXOMP1s+t\r\nueTUwWHmLnzpxG361R+Znj0gEx8ya72Vi8jwq/jPaoR3AfZpzyUu4t9MxCDM\r\nBAkxfIKr+BEt0/Hcz+lGD1RBsASZmc65AHE=\r\n=4C1l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.0": {"name": "@tybys/wasm-util", "version": "0.7.0", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-sjzytRuEU9rDwzthrDbo4iXTd6+7OiWoNdUFJzbFZQJact7RXFGkuBds8CRHaAEFYZlemaXP9/HRqBbwfMstGw==", "shasum": "c2a8cde1ff5c083a9770fd0ba82aab7dbb8cb615", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.7.0.tgz", "fileCount": 39, "unpackedSize": 744438, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7qj9hJmtAU0dEfs1xWEkOPP5ecA5fxcW1SFRZV3V7iwIgfjGLuFk/kf3T1u2XgNmH4zHONmH0fgQFO1B9AhS6jaY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8dmMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUghAAgMXciRf0RaVcMbX3phKGWLna1+CcQglxLiqe91+lUgSc/N73\r\nPmYf8q57wK9S2Rja+DmGFrLWYfP1ICyAXWM4/xqLdDLpMNSkadx/Dmi7Xcye\r\nlaJekhSB5ljEofeyLasi9CA781Q0E+BC+rnw+FSaxfziJOjT8qtV+ZS+TZUq\r\n5bFl7lrae4utHCFsp4fpx1QlmrBvVOLN/apnO6U26+JNjBdkx4G3IvQ8ITaW\r\nsyV6xsWNV+ZCwuTGZiRM/Ozgn+6OYlKjp9vRVMYotgpJjMHoT6clNtcb385D\r\nDOkYAgc/fN3jnmgq2u1H6OaiFQ1g56uw3i7s2BEZi4cSXfCuiknbib0+ADKY\r\nj1id2kNHm94F156zRFx7YLT1mEXKxK+WTZmieiPTR9nnZPG9q/3AF7wjw3KB\r\nfud5YEg6pWs0PHOJx9jd2314A0I8PtnFukaTjw9aJG8ahUvlFB+LAoUfZact\r\nOM9+3z53BgXu4fzLA7JMAtg70w0yKmpBZykUJEMfK1utDuSEOrX32zl+uw8z\r\n3oPF88DVUhhPM7xc8NY77hPXxmopAZpqZRau44iYdz3/tyzrBLja/kjLn2ns\r\neQXAiq2GQO8xpfmxtEB57+h0K9IAqt/a5DBklPI+d+hNK82K2cJp5Ohc//vK\r\nqIUfl9UEtHvFxiO8+hNajmqV08EHERVgSlo=\r\n=daxm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.0": {"name": "@tybys/wasm-util", "version": "0.8.0", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-00Sa73E9LjWg1d0+zo5WGLUSpHiVGK6OqlDn6fKaVnf0K9FOw+mF+diQAtvJXyBGurQhfJi2oS0JP9EV9ytbCg==", "shasum": "****************************************", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.8.0.tgz", "fileCount": 41, "unpackedSize": 1331819, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA4DLne8U1hA0bNDcastO1NAYL41tg1eXuEDSrpMje4OAiEA+ECZ6HOAamI7d5pHZbWmv6QduwTn+HNN3MOa3512UhI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+2RWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLJA//evNOMmkB1Wl++K9ULbPkNceVO2VxUAbVPtzCPPKa/YORTdBN\r\nBxr4gzEdZnpy2pY/yRmj9B95nhnfo6wIQFafcMCSOVfksABVI1K/ZImSL5+L\r\nr8N6jHcaQ8+gFSF6uwj+vV4LNJHBfkQnM2CaMEXoQLiSLUYjzsMs5GOt9xrt\r\nGw67s0pfATqYq73HC4oebNnwwzq3WX079WdOc0XPsLdxPqTfB7XGkMNrgo/r\r\nmctb5d8KCN0zkyyQFw6v7Ki8Y2m/hIvvWm4BnwuX6beg1KtcUEink3dBTPEW\r\n/phCAzcaO/u1L4eQjtsCh/fAO80NCy5CNULP5/RhrdhYbdWLbRMd01mY9lwe\r\nZca0fFEOlmNSVEyK6WSM8qVTmIOHasW6QkF970xZzOcnEihc6+kWygIJ26Zi\r\n+WHDh9nA+piWdrMjn0OeNnkMQDU00YqhsmucwFLVi+pa9tUfXAu/POzA/zOi\r\nZ3eWQG9rQqcNhOfjoVWJw81YO3BqH6GOKfX9B5ApinJWtHHpnV6HecgUE69R\r\nKYsCQMQXWwdX6BGkoBd+LMiAM/r4rSyU8Q/4oAETWee4DOYqHyMLgYJDEU+o\r\nkXO8SbNTB3SGo82ImEWwjE19TrmSmlwIFxBs5kg7XyMHmLMCRoopRH8e16qf\r\n+v7Izw58eqXk7NdCpkPwhq2exNo13HP80Pc=\r\n=VWbf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.1": {"name": "@tybys/wasm-util", "version": "0.8.1", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-GSsTwyBl4pIzsxAY5wroZdyQKyhXk0d8PCRZtrSZ2WEB1cBdrp2EgGBwHOGCZtIIPun/DL3+AykCv+J6fyRH4Q==", "shasum": "10360976b7f679497ea8526791006417ff304abb", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.8.1.tgz", "fileCount": 39, "unpackedSize": 753554, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9pLOgcyspSE6qwQOrBkXw+UthF50FSrBMOZyCCS3bYgIhAIu0IMJ5taihoaQEH98XV9tVpwOZr8uoIrh98g4wvQT8"}]}}, "0.8.2": {"name": "@tybys/wasm-util", "version": "0.8.2", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-kzI0zdUtC/ymNQCVQiqHCKobSSyppUHet+pHaiOdKUV48B/uKCEDznwf4N9tOoDKdh7Uuhm8V60xPU8VYbBbrQ==", "shasum": "33aa636e019d60b3805df797c54d53e10155a291", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.8.2.tgz", "fileCount": 39, "unpackedSize": 756459, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDbbqheFe39UE3Zmm+RLXrxzOY2yTKtqrp1G4KgPNCCAAiEAstzZTwdMmn6JF/yiuE3hmdu53HMDirv+iCHJN/7RGWw="}]}}, "0.8.3": {"name": "@tybys/wasm-util", "version": "0.8.3", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-Z96T/L6dUFFxgFJ+pQtkPpne9q7i6kIPYCFnQBHSgSPV9idTsKfIhCss0h5iM9irweZCatkrdeP8yi5uM1eX6Q==", "shasum": "34dc6fd51bdc03524a27359137594bb15c59bba1", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.8.3.tgz", "fileCount": 39, "unpackedSize": 759141, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIET2gDOLKXf6R0esuwjgnaIazLQe2I4taBia/JnRZueVAiAdPA6JyUMjc9yKXof/0wWgyvX1yvylXDhwJdGOV9AR/g=="}]}}, "0.9.0": {"name": "@tybys/wasm-util", "version": "0.9.0", "dependencies": {"tslib": "^2.4.0"}, "devDependencies": {"@tybys/ts-transform-module-specifier": "^0.0.2", "@tybys/ts-transform-pure-class": "^0.1.1", "@tybys/tsgo": "^1.1.0", "@types/node": "^14.14.31", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "eslint": "^8.25.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.3.0", "eslint-plugin-promise": "^6.1.0", "memfs-browser": "^3.4.13000", "mocha": "^10.1.0", "ts-node": "^10.9.1", "typescript": "~4.8.3"}, "dist": {"integrity": "sha512-6+7nlbMVX/PVDCwaIQ8nTOPveOcFLSt8GcXdx8hD0bt39uWxYT88uXzqTd4fTvqta7oeUJqudepapKNt2DYJFw==", "shasum": "3e75eb00604c8d6db470bf18c37b7d984a0e3355", "tarball": "https://registry.npmjs.org/@tybys/wasm-util/-/wasm-util-0.9.0.tgz", "fileCount": 39, "unpackedSize": 770258, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDh7dHRgd57RbyOw8jjyOm82XiLSCol2XQtxjgtyws3iwIhAIcevh+qpHZ6PsLgnjXGZ/1X0iOV7BF5XG+OA7A038uV"}]}}}, "modified": "2024-05-07T03:22:11.832Z"}