{"name": "@tailwindcss/oxide-wasm32-wasi", "dist-tags": {"latest": "4.1.10", "insiders": "0.0.0-insiders.c5a997c"}, "versions": {"0.0.0-insiders.d801d8d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d801d8d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "2aeecb5c453f6a96300f0da2864b42a1803dfda6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d801d8d.tgz", "fileCount": 113, "integrity": "sha512-y67m0DRzTwKTC98UWowLd1EXz727/NcKYGISg7h68E3RuCC/cUv2T8SzWXDwWVZ4nUyYpxcVLM5hc+Yp7DP5Fw==", "signatures": [{"sig": "MEQCIAat4iGD2rLHc2/s2p/CuXMV7YsJhb+yRLRI790v0FawAiBCsQ0EpJdVlrMSAi3PwcI6noeR/zdrC0Fok9LKGDv8xQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d801d8d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178501}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.bbd916a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bbd916a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "210b44f493608a6e4ce560bca120e900d05da9df", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bbd916a.tgz", "fileCount": 113, "integrity": "sha512-Ipoo7m2IaNvJSWZy57V6nYBseJtS7vQ+vk5RTakrqBoWgyBrs8alveAIauNs1sQpfZi/zuHEo/MgyGubyEkPww==", "signatures": [{"sig": "MEYCIQCFbAt2DDa0WWsS2sH6I+HlEm8dA1gzhwwihT/+mqkwjgIhAJi0piiIei29qSwzbBa+WPWc9hV7ot24TH+cDzGyOdxt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bbd916a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.cf2591c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.cf2591c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "29445303153eae0906ed13c0e790a9cfd3d7287d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.cf2591c.tgz", "fileCount": 113, "integrity": "sha512-xKfemF07S6hipav91Uc6mlIrx5GiVujF9zIwgEj4ojVCJnoDrpQ+GkOGIjgHw1VMuU509SMsODZO6hpTSDlsfQ==", "signatures": [{"sig": "MEQCIHNYxxH338ObDnSqcVnGD/POTLjvtm+CLOK5GoJopjLhAiBZIgcDaNDDJdAJ30ZxDy7Jj1Klrsw8+WL/zG+VG8VcFg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.cf2591c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.aa836d3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.aa836d3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "4e5bda6f3e50ea45d46d1f312baaba6cc387fd7e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.aa836d3.tgz", "fileCount": 113, "integrity": "sha512-0TwxdBm8gi/5CBtl4TyeauSyaiGuGnLx5vxNqEJWifCHPyCzDf8omQQC5GSCFLaqTGQISduH0e+xoQG81TJcug==", "signatures": [{"sig": "MEUCIQC/zDc2VKOqT+xnRWzc60kwr85am/B/ba8EXTZgN3+S2AIgYxxtz24Tspp+Mj617IQk2l4SpU7IeSaQRKLRFYGeq1k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.aa836d3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.4", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "2c6b1aba1f086c3337625cdb3372c3955832768c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.4.tgz", "fileCount": 113, "integrity": "sha512-2TLe9ir+9esCf6Wm+lLWTMbgklIjiF0pbmDnwmhR9MksVOq+e8aP3TSsXySnBDDvTTVd/vKu1aNttEGj3P6l8Q==", "signatures": [{"sig": "MEYCIQDxtwm5m8nxcBhZ/n9wDNDlwya+HBqUuaTCgsmVH+sqJwIhALY5MB+OhauJdroWkz77y6ew/nbo9XTFzycvPNMzwv/y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178622}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.25539e3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.25539e3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "630c18da465298b49d48afe76ee2376785fa2d70", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.25539e3.tgz", "fileCount": 113, "integrity": "sha512-GID1lai/h2kvx3CzD4nbVI7E/CfnosstjscOtotib3uVVuUXd3+5IIToRKEXZBnKa+PwLDO55B+03xgzMY9Jxg==", "signatures": [{"sig": "MEYCIQDCGdh2f6m6drPCOATO0gWQ3DhmZX+AXLqQhWnwezjz4AIhANzc77ISmd/XzhY4HkXLHixbpd2OjqxLW+IBobFJaNBj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.25539e3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.adcf1de": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.adcf1de", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "0121d48ecde468302f35d48dac48199c86462bd6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.adcf1de.tgz", "fileCount": 113, "integrity": "sha512-+3KyS22HMgktwea4CS/WOTfE4X37UEQaDKXcZw4BReAENwFqbtpufxGBGCZYTQjjkbwLD0rnIELDL49/IJ9srg==", "signatures": [{"sig": "MEUCIQDVqM6XelEjFyvDX3qll83VQx4vcKtd98h2QkT/A7p01AIgZuNgMZ4XD6GkY1/jZ7PW+QU2aIlg8EiGJOAfvB8jVbs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.adcf1de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.8feb6a7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8feb6a7", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e427f594c003d4764eddd0911db3ea350b30ecd7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8feb6a7.tgz", "fileCount": 113, "integrity": "sha512-cLTOqLfttlTCxeFTFyCW8MhaJRs3p9bnzUZBKfJANPvfrCr70ldDfy766ES5l9kTs0aVHLHFx4hyHvcGoIHbQw==", "signatures": [{"sig": "MEQCIC6WjIjdnSWkaZGMbmb/F6Egtl/PwPJ+KcdPmoVIVcWHAiArut6pYrg5fLK0qokWEckLE59Ig9uMl+jI9YkM2fFeuw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8feb6a7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.fc4afc2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.fc4afc2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "1f101c7798866c51986213b981597bfa3fe142c3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.fc4afc2.tgz", "fileCount": 113, "integrity": "sha512-ChhQq9Nuj8aTHU60OLXZcHWOMQbVYYHeU2UEJl0Q9US3t41P2FpSGLpZ/k1uveFJHWKjzrIMHc2culCDwSiO7g==", "signatures": [{"sig": "MEQCIBGffq0iNWPZABZwVKzQAFmXylOM6Tjd4WscdOT50zRuAiA+Lx1Pow6IwIawHgk66Lo/sBPtktb/vb6Dxu9mbr3V+g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.fc4afc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.650558d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.650558d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "b25dfe24bf673a85e30ee80ed3674adb22ac6c16", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.650558d.tgz", "fileCount": 113, "integrity": "sha512-gBJQaGwsEMMBGmWVEvfpXSgvBDRyL5+yi66JxljoaWqIiNYmUMxQk+uD/kqVoPPp7nQztLRI7XKW22xgFrOCQg==", "signatures": [{"sig": "MEUCIQCni8jZo/Ls/lWcHhwf/cpohudJM+qpSjOt1wFbGnC5zgIgUtuKyS3nFg2hko3G3LGi81OvJw57uoch54zoOj0266o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.650558d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ee0d752": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ee0d752", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "5a1de1e1d08bbdd3867555b7c8fe44ec7b8003e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ee0d752.tgz", "fileCount": 113, "integrity": "sha512-FTv3uDMu8XSayiuGga4F4gJGQyiixxHBIwuO7lrFIqValshuyXWlz0hUUKhO3Sa0oCJvJ+GyisozomHCAXkViQ==", "signatures": [{"sig": "MEQCIApEQ+0mrLHOdt/plxOOga34bOMCGuRR1MGkQhsoBWwPAiAzE9mIOn0IJp1YgsSVdyYw2nR/+Iqs8jdUtlEvIbX6HA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ee0d752", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.8bf06ab": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8bf06ab", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e48a9ea1c2354e30e0836a0c795b46293c2d2bf2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8bf06ab.tgz", "fileCount": 113, "integrity": "sha512-r/+9goIOLeroN71ksddwwCGL3B8AKnDv35saWnjoUA1dv/ZX+PtLhxSt1Z7/GVAw26eyKNAqH9J9BH9VmS3Pfw==", "signatures": [{"sig": "MEUCIDe99d4q9pkf4nY5Q69J4wnqckvVjUPPfFAeUZSz2JRVAiEAqh5BiawT3TUjC4+p4dIxEn2L3zRhFKcZki0Bhy0YHyA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8bf06ab", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.25ec6a3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.25ec6a3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "fa6764ea39553ed3d8586ab8a7b86d3181c6e3aa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.25ec6a3.tgz", "fileCount": 113, "integrity": "sha512-Vbu1Pq/eypu9ebcoTxcOzxEPqWpb2DrK9lEo738o22IFjvEdPvmP4FDqRfwKBS+I1xrO3um1XhYTZaDf53bCzw==", "signatures": [{"sig": "MEQCIEhQ4CUEwMNi8PeN4lGz8YxOwFs04VrlKkYH9iz8Dnj4AiAgjUiBQ5gMCHNunNZmEEWZo3mFDBA5AOkiICMZ0zjGZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.25ec6a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.8e826b1": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8e826b1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.0", "@emnapi/runtime": "^1.4.0", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.1", "@napi-rs/wasm-runtime": "^0.2.8"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "b2f4f597e72306fd16b26e42d6d266e2032b1359", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8e826b1.tgz", "fileCount": 113, "integrity": "sha512-pHEKrz2V/dsdxO9JwMhaCA+MuW4d9bwcQujhhjT2HlGWD6K/zkto1kukj5mgi8Y37HQUbrCHbklh+DjhUdqiHw==", "signatures": [{"sig": "MEUCIGAmixBckiSX5qLeiEjQAlu2pO6Sgspm0wWl6D1Q6/ZtAiEAzrVp+G+dpjnFJuCd0T9LtEzPnWu51lYZ1ge5Up1dECc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8e826b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13178639}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.a7f4a4d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a7f4a4d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "9f868856593251f731df227c20deb62ae633217c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a7f4a4d.tgz", "fileCount": 113, "integrity": "sha512-H4eCfXXD/N4TV1J3Ru1xv3xn+uo20jd05wA6YCzoldhhOMK+brAQxsZmFdAg0QJOr0KtbrWXgBYjeYkWGWTNFw==", "signatures": [{"sig": "MEUCIQCh3mhKB2oMS/Ta/X8GWIciwv0L1q2RBEFjgSuSJrj3dgIgB0PbPKbfvtJUV9vtNhfQuLSNavTcLOKGUwO1pjq0gUY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a7f4a4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.46758f7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.46758f7", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "c77a2fd1621b11ae98c63097762ae15aeac746ef", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.46758f7.tgz", "fileCount": 113, "integrity": "sha512-REsq6nkGh0yDJLShpf+gvT3BQoY8moBjogK1n21qLKAptfY0AIHaYikL7wvkquCuuRajK4UwVQkLfyCRx0BxTw==", "signatures": [{"sig": "MEUCIA7cdUgDdAuj/1FSsQ34iEmkwCl9+YbUmx5Y2t3H+tldAiEA1q9HMKqu6ejnPMzUSNisk/ks6RPZrh1RFPxMUBFuB7g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.46758f7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.2bf2b4d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2bf2b4d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "863af99d88f22ef0cef3c0013661ab8ed8ef7c39", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2bf2b4d.tgz", "fileCount": 113, "integrity": "sha512-7IntJwcIOaKWvynh7xPuS56abNJispsWFOAcidszfQs4mf2iY/zsd5zcV8zNM3h0HJm5D3bYmkiPtkaXQ8gjLw==", "signatures": [{"sig": "MEUCICd5iSTBUBfgNnpzBsINQcjUIyAKYCFinWQZFlne4AXaAiEA3SVCvpn93zYiM7eJb16rT22IsJSl/LlUCq/QuLcL+qE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2bf2b4d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d780a55": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d780a55", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "f6990f3872b0203808558500f6e3d6bed40f81c8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d780a55.tgz", "fileCount": 113, "integrity": "sha512-P4PmFt5sOwwFTeTLBadKUJBIr8aXwVd8cVdUQLwwKSVQoFli8umhUcss2GYfJJilTaUensR3SX18ikXNVwKhXw==", "signatures": [{"sig": "MEYCIQDhClPIQ2JRoQVQhS2vE/AyLvNjrBZvanKjQgfYH6ATXAIhAMANUmsiUWhmXD5tLr5BpVpDZxUxuwErm6R6JJApnI5T", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d780a55", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.231cddd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.231cddd", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e6e0bffd63859e139817bb6cb60691a9a4aee2ec", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.231cddd.tgz", "fileCount": 113, "integrity": "sha512-3qn9oKNyc+3USkCK40nqZPlkSt5WW2Do6K6/2jek9uCyTvTv6iz+K0OBjxHolSiZeA7B2Rg5DqNN6RuPo2Oezg==", "signatures": [{"sig": "MEUCIQCvv5D98V9PG76zsB4EJcnX9GyYCKzC5XsRbBCPbwnpYAIgQkpwxAob68sPx6FpJSlNyiUTvrWCHknwZ2F1nOGi88k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.231cddd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.52000a3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.52000a3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "02d14b85ab09a093d4132e4a046b472429101a05", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.52000a3.tgz", "fileCount": 113, "integrity": "sha512-bwCmqnIach25mnOSc7tQsdZU1jCFmAP3RH9ejDp9QI8q6CoZ+LKcJUEsg7ifekPVAhH85kRsMQCYZ2IB+I/mvg==", "signatures": [{"sig": "MEUCIQDmNKtqMb3hCaj0gzV4VEn/oqhtUdO2aq3xHmdUcP0HNAIgXoQYhZeI30CBFBo0B9vZWQDWsg2o5D6g411DJSMpvo8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.52000a3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.62ca1ec": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.62ca1ec", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "640301c8d2d18880a1f7e4f0bc69db1029b44890", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.62ca1ec.tgz", "fileCount": 113, "integrity": "sha512-hL9JNnZoRMHMNP3kZNzfMBHNKfMxiE6jW1Ay9fCmS1hnagYzarTozL1x0dN2VrNIvlO1oGxLKSh7FM/JlvUUmw==", "signatures": [{"sig": "MEUCIQCQydQsnUrTj8dcoSDpwTNW6AB4GFdamtJ9nlvJflIKtAIgS+69YQbOK4RQraz1+Qg1Sili4BHooLQBoGsJCbYNbR0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.62ca1ec", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ba10379": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ba10379", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "32d67a24aba9d83a1054dbc778bc6673c2a26b26", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ba10379.tgz", "fileCount": 113, "integrity": "sha512-hI4HVulZYBYCHCxHUoYk15ZQYYumf23XYmKqkxG3mP1ioKxxlXdof+7BT0Ig7//rzWpcwRx919AgPAWJ8EkHZQ==", "signatures": [{"sig": "MEUCIQDgCwYDNHPyLc328V8Y4DQYyjRCz4zKRkg6MfRvLPuh2QIgTm9bDy5+nqAzyrQi5OfBfSBymBkieZzKBzLhB0v+utg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ba10379", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.af1d4aa": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.af1d4aa", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "38ad7049a3529ab21645d399563c257c28d3e8ff", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.af1d4aa.tgz", "fileCount": 113, "integrity": "sha512-in0hWKK0KWZnGeKO9sf7IyC+yJkduUQlANtbRKaJ1MVwCirPx7P2lVE29PKX3IXNC4bZiXyR3+OlGUN4KSm9sg==", "signatures": [{"sig": "MEYCIQCv8DQ7yTPAngfcX/y0vapCgOjJxzo7XmHUTSOL54R/uAIhAIdlumEMwJwq2PlkFwOs8a5TY3OiILMzhZ2Zlr66RcaR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.af1d4aa", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.3a1b27e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3a1b27e", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e170bb109c55c0db662649b4bf9d5910c6882662", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3a1b27e.tgz", "fileCount": 113, "integrity": "sha512-Em/AIbskR5gUPLo3viDvLWlL6Vl42mQKcpHwr7AuuqsVLLGdtTGaFiOph4sls3LC0m9gDtNAdWSRLwq9TbKA1g==", "signatures": [{"sig": "MEUCID5DouLOTW7smUFgKiYec0kaCxaDWvOS9JPdEShMYKd9AiEA5mgMoSFCsKsQixbjz22ocvJjDUnPfl01rg3QR5zVX0k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3a1b27e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d2daf59": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d2daf59", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "23db094e32d134ccdaa2415eaa3ccdf538b1e7cb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d2daf59.tgz", "fileCount": 113, "integrity": "sha512-2XMix28KYTpOKH4uj9NS3vRqUpSzvjntkSP/XIAzC6nYbPIHyG0DRxynB78eYXfY5m8GpOhxGF9kPedDayEKkQ==", "signatures": [{"sig": "MEUCIQC877edUdahtHhMBWthEMdS9LdXRQmR94x/uwZeEqrBxgIgWl4EqaltdnOcmMlzr7jUBCK7vcThDXKvKF3qGju5OEY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d2daf59", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.9fec4ef": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9fec4ef", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "edc380d248247c055a51622276974b4928902460", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9fec4ef.tgz", "fileCount": 113, "integrity": "sha512-alu8K9/VcKZop4zlgvGc8NE14zkh/DOzyEqEOggXO2PZwDcyIv1qZ4xilkFxvMO8nAON0Wdg91tTE5OA5Ys+rw==", "signatures": [{"sig": "MEQCIEY74hKufRbdFwcLd2rdxdlDIo9iGn96sDdrn1ybHuSfAiA7zbSvsvIW/QwYOnGYUR6p9vgs/242LA0l0RqPLtzilg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9fec4ef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d3846a4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d3846a4", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "2287b4434e4f819bc66753543b87ae480044444c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d3846a4.tgz", "fileCount": 113, "integrity": "sha512-sUjaPx536iViaVaKC9Trd4oJl1FnBS/iNx8T4MbBIRHpOSY+pYX2UT2vsozv320aSGaHB9ph50ICeJq/7fxqPA==", "signatures": [{"sig": "MEUCIQDoobTJFp3gr55yubngGAAQUqrgvAcLuthgaaeVfqF/jAIgROEO9zAE5WWookXLS5Tar7D5yqrxoPZg2Tr1mN7eKZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d3846a4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.dbc8023": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.dbc8023", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "54a8f0b779b55a4a1e3a699911d88c0d4798db2e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.dbc8023.tgz", "fileCount": 113, "integrity": "sha512-20FI3qwgCTLx5HhPi9KrFPqHmQTArnE3rN/p1byPH7CrHjdJtLe0/+0fZ5g0yc4QRDVrwnaZT2BwHgIQeg3KdA==", "signatures": [{"sig": "MEUCIQDae+bXuaGD7tBWQwURv2qedgLrJaLKpbc/s8RemGK0owIgVFDeHjkUZaoSXU6OpAPBwbUz/3IlDT6DOic+7+J9Ybo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.dbc8023", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ab4eb18": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ab4eb18", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "a0821b4b4ae20dc60bf53766feae34445f180668", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ab4eb18.tgz", "fileCount": 113, "integrity": "sha512-BIqWiRabjk3Bxmxn5bF3OI0RIvY8x6sy8+CPHvs4+ynPBYEHETtGqNwAdVbtr1iFU8lVTv9Qe38eQarjBO9y+w==", "signatures": [{"sig": "MEYCIQDPAaVM0Ak/iCzU2r7mrFUz1uWBpbypMoT1QzchlE4V3gIhAO3JmvAMiOQlfOIhmtC/ZzsCRp8y5gIYJMGVP8KBV5xf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ab4eb18", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.a636933": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a636933", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "c03442c204feb314462cd25ac3c84afce8a78396", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a636933.tgz", "fileCount": 113, "integrity": "sha512-ai7F6EL/7okENb87vbQlCCwiIo6/r97vWDlcz/Mq6E3CnJqcnX3px2ggIg4pOsU58XdWLabNLbr44b8UecrDqQ==", "signatures": [{"sig": "MEYCIQD/YSn8nInO2itgampJh69DOjnIMKtD3X8+cMaGlOrxpwIhAJq9QpOtvXmJydHKtKZ6d837QYewI/oI0ULTxxzzZXVF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a636933", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.45cd32e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.45cd32e", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "a08def93fa497ad5772fa6f89dc432a3f7673dc5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.45cd32e.tgz", "fileCount": 113, "integrity": "sha512-Iqq31V1BAaZhJsTrqbwNd4PpCICtmnkGWYcI5pNbdsogfoa1rWz+EJU/jLKPH71pGXGEHE+DWaIqIUtW3a0dQg==", "signatures": [{"sig": "MEYCIQDWYtkj7+EZrLhOD8WFfP17lJUz2xHFiqbhpNcrhyUiWAIhAJNj3loi78+cc8cXiTMfnubTAkRsZArLsemJm1QErVvZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.45cd32e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.5", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "6bceca7bd7b387936b8fe292be3ab3c305da1699", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.5.tgz", "fileCount": 113, "integrity": "sha512-hwALf2K9FHuiXTPqmo1KeOb83fTRNbe9r/Ixv9ZNQ/R24yw8Ge1HOWDDgTdtzntIaIUJG5dfXCf4g9AD4RiyhQ==", "signatures": [{"sig": "MEYCIQCt/Xqy1UYZs06n1Oyivni/qy5+kURTvS0nAwQCKvxhBAIhANRRulaw8GEx3yQR1MyB4yqJar+t+w+vRg7VhpFpLQMv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180340}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.4e42756": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4e42756", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "cf62dcb85b435248a2de77540562df5e14b4710f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4e42756.tgz", "fileCount": 113, "integrity": "sha512-Ki1mY+4s3A/s1jfR1S0S2oSD3/CR0eOnuo0W7bohbpnDEHc8oeXudntnQFdcQDPBA0YyYthJ7bDJIMdL/Sf2OA==", "signatures": [{"sig": "MEUCIQCVSLe3N7RXXKpNxNjNmo77lSQUKQJIPL12Vc7oQ7RgAwIgH4s76866KdHus1IJr2pkUnC2t4HrJXx8MJnJA96JxiA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4e42756", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.c095071": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.c095071", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "89f43b42a02f39c737e36dfde0c6d03bd9be78b5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.c095071.tgz", "fileCount": 113, "integrity": "sha512-wIVbNEIPX0wWo1K+k9gbDKWdShbgvGbsOGnkVO57oP8aR0aQok5YOvQw/vWZsYhgCQR2y4T4ty95nGgIN6D+xg==", "signatures": [{"sig": "MEYCIQD2T7L8HF5vmV8uolPr31qhz0C2SvkNgh3rTWZubU2gwAIhAKMvkIujR/QFctpbRZ0YTrKp5RIrfWRVBe2FGz6RWBjK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.c095071", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.dd5ec49": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.dd5ec49", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e26e9eaf899c01881f5543b0a5becbf1298a98d3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.dd5ec49.tgz", "fileCount": 113, "integrity": "sha512-L76yd/i9RHUBRTM+KmFqmYoa6mQbtqgUZfoaWEJgERCu7SuiPPAbh77x/UoQTsVkQnhZ4f31mF6o7XEoVbuc0w==", "signatures": [{"sig": "MEYCIQCM7a9gcAMK+IfEt+RfcZBltqlsFo982ZufUEueXTEgYAIhAJk0xbr24lVT1Y1U8tyat77xO6b+yzY0Fn1gtWojkeE6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.dd5ec49", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.e00d092": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.e00d092", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "fb0fabe86890f780c19c4cb22c9b01e640d5e20a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.e00d092.tgz", "fileCount": 113, "integrity": "sha512-rKX6NgT49vd1/MJIG1uKPwrBskYniEE/mBFGWMNPzAaPoWGTNaOqS65C+9a4cGdwk5HEJspVXvxlqwOWU44mMg==", "signatures": [{"sig": "MEQCIAoC1+8n4vlee5IZCwkW6+lR6KcYLzX3TN5D3zXYjjfJAiAe7gbE0su9StxaJEcw9AKB98QL/M1ujR5Lq9waZLD8RA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.e00d092", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.473f024": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.473f024", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "55ab31f662cee23f8b805439a527daec4e671c99", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.473f024.tgz", "fileCount": 113, "integrity": "sha512-lUCgGRa8e9ckXpUU9W00pGkyHZVSzw0eITHFwz3nIyL5jEvjOHLoYdXvVWBdQz2grQ9qly38wdVyKAKOty+cOA==", "signatures": [{"sig": "MEYCIQC4LJ9KD9iv9JlcZGn7ebjJedSYj3RDBxvl01ziIX9zsQIhAOyfdruE2Nc6FMk9hxTKbBNCHfY7/BYiz8NsuoGB+z/l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.473f024", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180357}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d38554d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d38554d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "56336625c2a8cc2791201b00da333dd45f2791ad", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d38554d.tgz", "fileCount": 113, "integrity": "sha512-isO1tvEE6/FMFy/7dmrqeYULwonEBoC2OuTInuhNzcBQtf/r8ufsSa/xpTlhcKHHSamlUpRasw4V0GJsIB0gBw==", "signatures": [{"sig": "MEUCIQD/Eil1gOvPu49wG4pwHguBMD5FphmsWSfzpJfXQg4UEwIgbMdhcUSdrZn5mbRH2pax/Jc9ITeE1iqHOBqdM9scSnc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d38554d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.6a1df6a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.6a1df6a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "1b1d26bef1dc2e2911c2e32cceb81353cd0e7a2a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.6a1df6a.tgz", "fileCount": 113, "integrity": "sha512-Nwctrd9WGeONfh+SrxX1MZBhkLKSXhK14kEktnOeZ1UYMNt3eFjf3sERP+2bmfBkdsQI+yU6xpLsXj+i3qI9iQ==", "signatures": [{"sig": "MEUCIQDZ01XE4SOUY9QPRKhjI6//epvx7lFo50kXDMEcf3g84AIgMfcce/f9GohK+gkVpjEfYgd+fG3B/ec0PH8aulw2uN0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.6a1df6a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.5c5ae04": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5c5ae04", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "43f2104aaf95bb274b038350e72289a098440234", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5c5ae04.tgz", "fileCount": 113, "integrity": "sha512-hYyQL6kmVoieyPzkXsShT64Vwi1Buu3zvCZKM0GLllbtkn8V5QGP/M+zR1UW6wWi+xN5EDjWWJUmlJHNSNQcRQ==", "signatures": [{"sig": "MEUCIQCXw8V2ZQpDV1yr+vOJbwJl+2biRm2v2SRnRMi71NUNlQIgSNtxBz+IgamYsAqNowV40ND8zmTDYRKqx8eLwzzYNiA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5c5ae04", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ed45952": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ed45952", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "ad2f6110adbefeafb141e1a13e6a8e2b6332c576", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ed45952.tgz", "fileCount": 113, "integrity": "sha512-RoORXcuJYgrxYCZAglZQLXM17PIzbAai7kLVaz402rhLH3pRvuIBZhZMdyebdeE32zW4NogfMKfy2aYttJipzg==", "signatures": [{"sig": "MEUCIQCcKPAIsN73d9AwmwOs8wBcPyXXdNPab6pJza2TTfuEhQIgfKTULX9qUzSi96MSBgrwVnzqxbDMp92btSrGDdLC4go=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ed45952", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.4f8539c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4f8539c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "4873f2ee851cb50352c37f5fac46abf7fbe3acea", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4f8539c.tgz", "fileCount": 113, "integrity": "sha512-8p37VY5cLeKaJ1eoHeb4cNvWjCcMgRCrjCtKfKxSR9byw0qdWoaYs0i1YljQM72izjDL/PkoxpUWC/ofp9hZ/A==", "signatures": [{"sig": "MEYCIQDtdWij0MaJFw5CPpZsyca2nqoB1YyNPFF1y9OSrDKi6gIhAM41AziOel06oDsJEVIn4y24jzOCSu/+eih4CUsKBW+b", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4f8539c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.449dfcf": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.449dfcf", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e8d03722c534d7b249cecff1aacdb0c981e744f0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.449dfcf.tgz", "fileCount": 113, "integrity": "sha512-r9tK6guqFfGtQL2azrEAclMEXG+oXCYNjNzjhOXabcp2xQ7caXDrRbupUeAjEUuDdTTqPRjyE7cMUOz8LxqJvQ==", "signatures": [{"sig": "MEYCIQCsijolaOgDPP4Uh+oEGah66X1zKQqE7qYYZ2BEdVuoPgIhAOpxTnhKaTpuuKC+iJwAvz0m/K8MTI6PXRFFjdVCs8hU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.449dfcf", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13180126}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d8c4df8": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d8c4df8", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "3fbb65b975b43c2275b925324aae616a589ee08c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d8c4df8.tgz", "fileCount": 113, "integrity": "sha512-PTFGK7tb05kjG+Xv+BWZpYbwQNhLcGFbhYyfa9l2OJhwbxDVbj3szmvsnDzmHliRZTnlc/rz1lfwM2fwg35phA==", "signatures": [{"sig": "MEYCIQD3QlJdW77iJdKwTe1BnD2CYLup8pNx3snvQpoRXAhULgIhAPJ+T0AuqhxwxPev3JmL9uHKpVh+ZfdqkdXcGgcpzxy1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d8c4df8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036359}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.179e5dd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.179e5dd", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "d10cea8728f6373c1c2d950c4fb13a1b3a3b6098", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.179e5dd.tgz", "fileCount": 113, "integrity": "sha512-WaL64zmnIinc0sxA8mHWFRSKYvAqR7rZWCN4dU3762C7GfwiKyeuc5z0Dacc8bfKdTxRvqTj3sv/7c/HdM08fQ==", "signatures": [{"sig": "MEUCIQDttsvjcFCIXzbzofkMVVTVYeE8vEPkqCAdgCV3D65AIgIgdEZieMI3fn5PWnmCKKG3P3WraGLr+6C3t1VIhnCoc8g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.179e5dd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.17ca56d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.17ca56d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "d6609614db4d8f74e3ca4261e0a4cdbbb475aa96", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.17ca56d.tgz", "fileCount": 113, "integrity": "sha512-EoyJueIE/5DVJZIBe2SmaJr2MFUz66AFQoYliQ954L2wGhTLGFhB26j1uNR+a29g0IJrxJL85wFSbh7DbeOGVw==", "signatures": [{"sig": "MEQCIFXbMWpwnbmn1eVBlKlBGGlZmHZvBU7XRfaZGxCqR04IAiB8UkA3n+6tYNVRPGwEJjyyM9lGhZcT6nX2A501vRMgFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.17ca56d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.62706dc": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.62706dc", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "f56d608610e082fe19dee57e261b6e18a4145436", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.62706dc.tgz", "fileCount": 113, "integrity": "sha512-9hAfCXBd0hl+hoRp0V5Ejpq/i2cug45fOCvx04buwcb5ZZ5nJEmRFMFBQCzKXWp76c8fDxGhq5nnp9N3BNdcUA==", "signatures": [{"sig": "MEUCIHywMuXCDD/xC9um7cugYsHYQL36O5YRolfI7MPjC5cpAiEA7JvyZmm1WShzFN8JB3TGOd/qR1ySlfVdM4ujEoVtVyE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.62706dc", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.56b22bb": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.56b22bb", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "4280a050f89a9030a068d53a7ed88d8e707f36d7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.56b22bb.tgz", "fileCount": 113, "integrity": "sha512-J9W4nnb4iSLtPijEzOLv6+GjtUZQSYpPtbzS5ScZexu9zd2I7yZz+i0juB3Mf0WWJIIXR2EvPIRl9YXhabB6sg==", "signatures": [{"sig": "MEQCIHfAq4gltsvLde3h7aPj8eNz7phblHYoi8kQzU6/TrVcAiBcVWGBBDooaL7XRPedNEurk2GqMswxWuIcn/FqlpOB3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.56b22bb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ff9f183": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ff9f183", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "42899c8a01cbeafde1690194bdb6342987c7d464", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ff9f183.tgz", "fileCount": 113, "integrity": "sha512-yp0oSssrKqIV6RXHrtqNYDZ0eklCX4xQJytspG2pU48qSa+vjsIGZ+tMr+sbZkL5+LNCXMzcmPqy5LyKzrKw/Q==", "signatures": [{"sig": "MEYCIQDrqiUz3ENz8enewM4sGzMtPnyQ6Y2yh5cHt7aNsSkPLQIhAPkviLISXR5d2HsU9kYtPUNm9oUGu3KGHxYYoNQyS4HX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ff9f183", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ae57d26": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ae57d26", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "1e0b461251cac03f6787d0b64579006bdee03c79", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ae57d26.tgz", "fileCount": 113, "integrity": "sha512-MbNTiC9dbGq42phlta4SzqkBIM3/rel7XC1P70N7trJMbXRY4+mOpHxmXgGDo7/yjSma8JvvKI5ZEOwbGcpNUw==", "signatures": [{"sig": "MEUCIQD+P4zRULMm1u8VH+z++uQ87mpArczfZZxj+Qq0UmKXYwIgZlrCuWHx6JTY9qrmYYWm3VYqJxHUCvXrz9c+M1XRqDw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ae57d26", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13036440}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.2f6679a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2f6679a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "9e19d89e5692035cce97bd0e0db0acf65516cee6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2f6679a.tgz", "fileCount": 113, "integrity": "sha512-6YQmusTj9GCGnZVa2s6SXBPgp3/3Fxi9jRXjAdvMVBWT0+m6x+gTlzYlnqbwuPr2YBH/2NRjjrQKhb2B3lh2pA==", "signatures": [{"sig": "MEYCIQCGZRfnOCVlP8UpJX5fm3p176DTV+yMt05Bz8HB0d7qHQIhAI/GRSJSuwjeKzAdD/HpcmJCI1b3CBdsk6SN3jVQbIfI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2f6679a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034695}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.47bb007": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.47bb007", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "21eb6844834d7bfa03dd344939b9ad435df2b6e3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.47bb007.tgz", "fileCount": 113, "integrity": "sha512-J<PERSON>+PN+KN2IfbEBonaOniCsK5TTubBvPGN2g/HQg/aQE87urALfj+UTn9eJIYPLNBuvnClCnYBaeoA4WxHk6iwg==", "signatures": [{"sig": "MEQCICg0Z/ZykJtp/xxSLuvAYJoJel5Kw1AIF8/1TdBcEnF+AiBw/1jTY4aO9++0lXkf0jmu4/2q3hxaQpFpU6hnrJvaZg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.47bb007", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034695}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.2d13998": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2d13998", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "8290985142640fa173eaabfb2bb61a31717f9905", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2d13998.tgz", "fileCount": 113, "integrity": "sha512-MgGdYRFb9sHzqrkwgc/Y9ZmsNTXtHB1YTr6Wrm870OXu+DapJVpw5hqAZSzocgrCkDa32ywOSFSZHBCUV+CsCg==", "signatures": [{"sig": "MEUCIBQSUGwaxGhDoDJO5i9w80k+4GqZwl9recj3nsvM4A5kAiEA0qiPhp6AB1iNHjPXF8wARTE1PRsBTt71z19h4ymQ8QQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2d13998", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034695}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.6": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.6", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "7e45eb7aafec0406477a05403689198a9f062b4d", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.6.tgz", "fileCount": 113, "integrity": "sha512-qAp4ooTYrBQ5pk5jgg54/U1rCJ/9FLYOkkQ/nTE+bVMseMfB6O7J8zb19YTpWuu4UdfRf5zzOrNKfl6T64MNrQ==", "signatures": [{"sig": "MEUCIGs4GyiraFFnhe4vpeggtcPo6Nk/40YVOngvhBcm38aNAiEAtshhiD+unauZm/DYLu+qUDhcGxiS/9A7UkiaGC1wxCA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034678}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.737994b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.737994b", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "7a7f7953198145d34ab2a7f5764e77e4d2494af3", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.737994b.tgz", "fileCount": 113, "integrity": "sha512-l+8vHmA0kVndbxmDCVR8ofC82L2yR6nuqPB66dKa3dfjFBbqzt1rpGlJxIr1nuEkYkZrmf+uLBc0iMzH/mnxFA==", "signatures": [{"sig": "MEUCICqgCmjIgJ6vW3WxFUGpVi1BFo7xKYHKc4EQYJ2MAH0wAiEA6OlQnkJP1aJLmVcHwd+rYJYXTLD8O1Emc4DbvM0fvX8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.737994b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.3386049": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3386049", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "8d6c3feee092039053a67d5c1c20a9b684febcc5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3386049.tgz", "fileCount": 113, "integrity": "sha512-dik/E+9qnzEWaxlmbwq/Xbn0munVluIfRydNp84rKtgZWY6QMfCBLoLg/ELMpFVhtrIT8XyXXgVsuazeRu+DQw==", "signatures": [{"sig": "MEYCIQD+4aA9OIsQhCc7PBCyxM5EixoJJ+hvTXaP9AgCFsNwmgIhAJkK8MZrV3pHCHG8DS5toYwWrVMRMPar52+Rkd8+wfcv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3386049", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.f0986ce": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f0986ce", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "530334f679e5ac5576c8e53f686a13d1cb3aab07", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f0986ce.tgz", "fileCount": 113, "integrity": "sha512-uIVGSGi/yod8CDDwOjxGTvIy453tIfhZV28Z+9y1LF1xdRoZWhT7jH5LU6lzQ02dlvqQC2OFbQgV/jPCL6SZzA==", "signatures": [{"sig": "MEYCIQCI5pEeiWnhP2B1pmU9qtvoAgWFLkOOVLXWjukOF8u6XgIhAO+ROMCFWrpd0dnIAV1S3gdyzpTET6V/3Bo+rSfWzWH9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f0986ce", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.0d975f5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.0d975f5", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "9a25f3817916117f5739a5f1796c0c22775adb10", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.0d975f5.tgz", "fileCount": 113, "integrity": "sha512-pfSaNt54ZTMf7NEJuzp1CtQIk9rTsxcAzgSDHmuooiZNG81PjgiJf6yQuE0XZ+Xvc7mflpj8mwCG3SyyaqNZkw==", "signatures": [{"sig": "MEUCIHFhYNWu5a6959vCrnJvxfiRg5jOgrMi7FLbu29c41XhAiEAzy/FbbfqifR+GvKHEuioExHgDYMZos5Rw8g+7I5Nv1Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.0d975f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.19e2b29": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.19e2b29", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "25d4212f080771ae7cde7c06b58111b95c4ea974", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.19e2b29.tgz", "fileCount": 113, "integrity": "sha512-/IFTar/5FWv00/Nr6a9uu4RN9XPP8rsVAPlXhua3wMDTrmJ2PSDRFLgTSIeO9ttcC0RTUu6vGQhHepAAGNpOWA==", "signatures": [{"sig": "MEYCIQDtbgymO1kQgzgBmN6GsoUIdsxQan6ZU/PSBi2P0H4UvwIhAKTIQeXKx2GgsYEtPVXlDM5XU4canObPdHKB1ZjWN78U", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.19e2b29", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.5688f0a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5688f0a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "8913156ee11e7a0938fa793579b5307e54a5273c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5688f0a.tgz", "fileCount": 113, "integrity": "sha512-PyoW60ZO6sxEDJDdmXzMPS7K2oa7noSOy80+S82ui+BcGx3+eARpPQI2nhffYieIfw628PIrOjNnF0NZ671smg==", "signatures": [{"sig": "MEUCIQDAXMZnJq/xm8zjruGYHCO1Qwg0t7tzC1hdGSCtaO89CgIgMRlM8Ie6WlD2KA+X2m5EX0BqyM7xjpesZry3uaYMogA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5688f0a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ba944ca": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ba944ca", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "155a9fbf5fe1444980a55dfc1d4162dd16c248a2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ba944ca.tgz", "fileCount": 113, "integrity": "sha512-LxOoUXfmL/J1dcCGe3WD6s+TzXov3XrQiaRGDlekpBB4HOVCIBamYOulxpD40q/Y0GwTKV0e+kjezlXa5WoqdA==", "signatures": [{"sig": "MEQCIF9uLYK/DREA4g/GJ36rGhh8pf7ZGPsjW8OylOtGQmZsAiBKRyCRjmqC6H67rO7sE53UuL/HhrLXaZBuiSSkaX5jKA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ba944ca", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.4fba87b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4fba87b", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "6c71ff670f32f67ffabdec0c113e8070a4677f56", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4fba87b.tgz", "fileCount": 113, "integrity": "sha512-g/yiO9tOP4TjVqIaCg7roMef4T+gucT1YLLX6hDqHJBXfsRiAstvWXnTIC9E+2kIxW1aJUVB8pTqXyf8aekOXQ==", "signatures": [{"sig": "MEYCIQDIonEFqF2VwIXdrOufPW/BUd7V2lmwtg4VEDuhJOYWMgIhAK4b99GwnZZEAEI7EWX+yxNfqAZJqVFViR+qZ9CqYqdI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4fba87b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ef2e6c7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ef2e6c7", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "8f1cf4ead7fe9e11283cf2f846c5c6cd9802b393", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ef2e6c7.tgz", "fileCount": 113, "integrity": "sha512-8s8aHB4eYnBErtPGKxBl5R8ZAT2k0nYi+QGy7QtG1L9nuKJX3CwMsizwvlhUuP5OUPkErGL+2tHQ2sk3Rkpqbg==", "signatures": [{"sig": "MEYCIQCIn4r48hxRItS0vWketVltibDg5XsqoR/W2MtkjsUQQgIhALGPqyvUEv5BdUevWJ5Q9phXB8pcETkUo+8vji6ldUhw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ef2e6c7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.498f9ff": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.498f9ff", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "b699b46aaad627924cffbb6a5fffdbd7bcc41c57", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.498f9ff.tgz", "fileCount": 113, "integrity": "sha512-scCTbn0Ykc9+rKstDOdrTha5xn9KY3cW+LJU63LS7JvKAjDVhZIJddQ/N8LLjw0OgGCZ3jpnYNWT51ACHoFScQ==", "signatures": [{"sig": "MEQCIATv7x66P2qXhxP2GId5ZXXCoXlIAY60CGZxo1Q5+p+bAiAzeuEpURxFFhes6qhJlD8itkOUzUfIsGeHGtW0xfZ6fw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.498f9ff", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.4db711d": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4db711d", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "a19adb3931df7690dc6f415ce482d2ff6a8e4c07", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4db711d.tgz", "fileCount": 113, "integrity": "sha512-QoXgcAfUY78wMzjRIzTJ+huwuHYZXTJmRTeFXc8kpcd+lA1Rzig/Kvco/xRh+RBERzd2XVVBLD5Bv1Z3ZMikJQ==", "signatures": [{"sig": "MEUCIQDk8mEAwvWQtnf9t7NSWYLhAJCwdrmZv9OQ4EL2xJ7k2wIgedkGh3LY2HLdUBYEriK0eLmFHKBd3A8pXUtTCS4BW/M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4db711d", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.e57a2f5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.e57a2f5", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "44d346bd89756f469606f6089e3d3f5759baf172", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.e57a2f5.tgz", "fileCount": 113, "integrity": "sha512-UzdvpC/EOrpgsUX6QNXIdORzPrGOeOSbef08QMyofbeFkJjy9GslbHkhTqaqdfIwYvO+ItTAaxKwLYJMmJZxcQ==", "signatures": [{"sig": "MEYCIQDW65a7B18G97ATkPS8n4jq0Ok0teC1LAsmhkjoT6S3dAIhAN2iGZzEus5U1UCFTqB0fBd1/QZjtk0AJWyGxoOlyfoC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.e57a2f5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.f3157cd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f3157cd", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "726ba8f25da713c04ef2e7cf9251cfb1b5a654be", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f3157cd.tgz", "fileCount": 113, "integrity": "sha512-AanOvCPVd8jHMNYm3vsBDU289b1TjfSgH2f62+a9vv10/ldAvtAqeTv6g0OKjMKq9QQakhiodVvR7AR/+M6TvA==", "signatures": [{"sig": "MEYCIQDJExQXmyBo8ymhjN45yZJIF2vkqAl5RFNCvlkxFxhVLAIhAKPCRGWaqHQatlaNCi+9TlNQDOuLtgJYjvdlBcEniX0d", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f3157cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.6fb98d2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.6fb98d2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "19afa61e2a6ccf29f19284260462056f946730cc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.6fb98d2.tgz", "fileCount": 113, "integrity": "sha512-WVRqoA0NseiNUHcaZKBsBzFz3Ty9T89MWqr2zYR8FiSd+5Sj4K5W1sHEmmp+xZJ+n8Nho42PA7T7AG/ZOFdqQg==", "signatures": [{"sig": "MEYCIQDHmujMXNXW/FYJxiKLZo0l2BGvVtD32Dwa+TSEA/owxwIhAMPcrBzAA78iGqnQhKeVucwqTsFX7fjelaVYmZJAW4LT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.6fb98d2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.1ada8e0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.1ada8e0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "067ba2d6fbbb0032025ea816953e665bdf0cf51c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.1ada8e0.tgz", "fileCount": 113, "integrity": "sha512-ANJIax3WiwIOEpciK4aHphscEFIWrM/IPnC/C4lAcam5ijS9gxo/cDTI+E9qNTWzNvlia4mRiaCKPVzSQ7uVvQ==", "signatures": [{"sig": "MEUCIEz309qzB+SgjJhYoUse7uoQRTEext4Zp1pQUeW45r52AiEAozLO7aalZVd9E5dPxx6RVEKBv3bpRvAzbfHvxryYFZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.1ada8e0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034696}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.bf591fe": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bf591fe", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "54cccaa3e4d1e7f13013884716e4842870a7323a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bf591fe.tgz", "fileCount": 113, "integrity": "sha512-q8jvr2/6FfxNWzkTNet/F1AYSYCCNvegHg6y4SbREBkZF99+NsX6j/e8a7gj6D33iTsiSzCZBvlLXwfApnpWlQ==", "signatures": [{"sig": "MEUCIQDCsLyHqDTHDeahr0HKtDTgJRP/oWFH2/m3jriebFd6qgIgCk/d5gPZYTZwUB0nesNxNzRy3CpMxX/RAnRqRjk+mQo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bf591fe", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.74e084a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.74e084a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "09ee6096a4ca6555e87db66dbcb9396f6dd54123", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.74e084a.tgz", "fileCount": 113, "integrity": "sha512-CnaLgiNJEZnhlzjJgp7QwcZs6DtWNvxt/TcOYWYyiy1i+OI6PjglIdV/fTHrcU79UlNBnQnLU0arS/uonWVpyw==", "signatures": [{"sig": "MEUCIQDHqXDVln6HmssoX5iSO71JyLCpJD7t5rEQo68TZkQ1ZQIgI6Max8PZkuCtLgdA4wWWMxcbjPwJwk1clQX7fKfOwFk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.74e084a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.7", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "79b41b0c8da6e2f1dc5b722fe43528aa324222d5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.7.tgz", "fileCount": 113, "integrity": "sha512-ANaSKt74ZRzE2TvJmUcbFQ8zS201cIPxUDm5qez5rLEwWkie2SkGtA4P+GPTj+u8N6JbPrC8MtY8RmJA35Oo+A==", "signatures": [{"sig": "MEUCIHuRadZMDQUffwOiyZrsNIt9+WHYfNI1NCiNRVg8ADPoAiEAue4VdVoMa28UFjdx+86boUWmBPOGyeE5bfEm2jhntx0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034763}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d69604e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d69604e", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "4b8ee583911feb35875be4e089de37969f0bea38", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d69604e.tgz", "fileCount": 113, "integrity": "sha512-gGph1uSVhsUCylxsYpuAEZ0h6xsBgbZVMaJMma3LCbWosgz8x7yRvCP3kysNn84+Cjp337KyDkAMyqO3CcNpZg==", "signatures": [{"sig": "MEUCIQCwiZBZeChasGy5mpSlCgNvBWfQaXEULH8rgFlcRFEFBQIgE4SSatC9DquZ2lT4eqMaRapOObKaTddEGTNuh3NgB6c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d69604e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.c7d368b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.c7d368b", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "4cbe60822ae9da6b93d2101232b34ab3d78ee692", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.c7d368b.tgz", "fileCount": 113, "integrity": "sha512-yV9JuFdTCWL6xyeSxFIGUpUo+lfosmQOHoi1/Ah1NsULenVBe+IlXc5iCiuzjPb4dX/34CIhWi8ZeU/Z+9FtCg==", "signatures": [{"sig": "MEYCIQD+PO8Nn3j+FJREpsdJAjytynw8N1bZhWFRYht9hyrAZwIhAJYtXsOeT+8utirxairRm6nADppqV7b8UhsrHxCCPtNI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.c7d368b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.71fb9cd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.71fb9cd", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "db0e86a3c18295540fa4ee7c29d4b5af6a95ffe4", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.71fb9cd.tgz", "fileCount": 113, "integrity": "sha512-zMdH074kOxEqTKLPTQ6pRM9A+Uh2FwnTg8dJ4aPpve6TCKDIFNvHL9+P9g1KOQ5C6TOeAlvSX7YrTxGgGRrHMA==", "signatures": [{"sig": "MEUCIQCoYhQi90q8cm4B3yGgSlV+FIdNDprPVg0M5We6LJ5G8gIgSLjovDgZC8OZ+85bP7Ayd4y+atMf1oj0s27V7TAjiZ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.71fb9cd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.a42251c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a42251c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "14817d4635d45546edbb89aafcc776108e159c80", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a42251c.tgz", "fileCount": 113, "integrity": "sha512-r24+kZOEgQwZrm7uChh0AREe4kaVdp+IOJr1EgZkCBDPAZOX+/29/U/Y8UiksnBPjIX4rN+ZgiADwRD1SdnKzw==", "signatures": [{"sig": "MEUCIQC2dX2ujZpVxvRcRDRF3QaoIWwVO+dverlDJdGIy0pibwIgRb+FOGaRdU4PYXHwPkBlqcAqKBMEIQX10wDoR0aQUQs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a42251c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.9df5ba7": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9df5ba7", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "00e1bab62db66a3d2e516daa10152cb2aed6fa38", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9df5ba7.tgz", "fileCount": 113, "integrity": "sha512-AbEu/uLxs8RHyobDS5Ar5zwCsSCP5Szi1mEwqzFXJCPahLOAPRoAPE4uRvrrc3/IZcuY787KNCNstYWNJpAjrw==", "signatures": [{"sig": "MEUCIQCdZzh5Sv32IrZqomKhHO50R9A7QT7KEllz7gY3WXSFLAIgfLs28CfhSJKsJMHUCZJHesDSQjjQzahPMRByOEs43NA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9df5ba7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.7013ca3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.7013ca3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "bfb71458f2a5ea6da3057db52384500dd8571148", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.7013ca3.tgz", "fileCount": 113, "integrity": "sha512-7nhaNMKvyzA1camV2PQciQ0G8YYq4oNs39o0Vh0RMxE4HCmhLrPHvS7Ur0qqqTpMF0JCDR6t27xg5D61OJFVFQ==", "signatures": [{"sig": "MEUCIQD2dbWofzoj5r5H2DSfhmJfTxGXg+gEcW4rQxHZz/wDpgIgFeZU8sVt3RwTocUekIh+PA3m4j8yV4HV+gJb99lHyv4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.7013ca3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.24ed64e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.24ed64e", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "ecf364cb819674c01b3570b06df4c0e83e512404", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.24ed64e.tgz", "fileCount": 113, "integrity": "sha512-iBjA+FWihQYObp8I8RHpOHqgTLul9ym9nFohEnstq2CO5OqJB4ssYHBxb+KVHK+uI5hGeR6/A/YyFx358NHkHA==", "signatures": [{"sig": "MEUCIAZhD9B/yWtAThkwFWlbJRPcw+23CuFW6ZukQbFbuhQGAiEAo1daCQmSDlq4gVgBiEsrXepPC8IHcr9rmwEw60iOGg0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.24ed64e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034780}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ed3cecd": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ed3cecd", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "aec16464c3e557e0c923acdcd55f5dab081c14d6", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ed3cecd.tgz", "fileCount": 113, "integrity": "sha512-wNYaj97gM3R66iriYEj25UzxognEUTIAHc4nZo05j7tcJTNTZFfq1+d6pNAekneQZMSQmZ7W/wE8CxtTg3k5wQ==", "signatures": [{"sig": "MEUCIGD0TfdW2J3ZEPjwxudyNVQ9FxLr6ZaOpJabUqOxaTiEAiEAyHZphTbY5soVGpV70yNMutDrfFyTQqhXcoBiQ9cRdYE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ed3cecd", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034824}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.9cb3899": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.9cb3899", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "bcb454c58b4c1b555d535a033087f173e8144eeb", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.9cb3899.tgz", "fileCount": 113, "integrity": "sha512-LEYXncYqpKgJ5vHe5GlxVeE95VOOzfqblh22/aPK89kOBVn9z8HybVYqf5LLVEiY0SpEY6/JefZebHbYtDYD3w==", "signatures": [{"sig": "MEQCIHlNK3eWgQqMvBcQ9D7X/hwWQ7dvnS/jbN9KgqRP/IuKAiAsmg4CIU0e0mSMNBy8QOJZONRzpcmNudSZbYYMFy/5nA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.9cb3899", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034824}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.884f02c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.884f02c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.9"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "909f17ba4ac0d0a0ac5f6de07222f1482cbb1fe9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.884f02c.tgz", "fileCount": 113, "integrity": "sha512-wYzk3B1rW4gCDerq9RI1fuDptflewcX1nGebpOP/OsU48EayVMXY7xtxbCFOKrSAu8ERqVmlB4NL6UN7GkOWog==", "signatures": [{"sig": "MEYCIQCLdtuB/i12EL1X+A35hxPUA1DV4T9591xGoeKyIyf7xAIhANTEPgBnh9gyl+O7goz6Ae3rmrj9hzds+DI08yQgwTBt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.884f02c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13034824}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.8fcc633": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8fcc633", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "c599685ffd78ef3b1c1eca094f0978906411f1e7", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8fcc633.tgz", "fileCount": 113, "integrity": "sha512-1jxWt8Qki7CCHYTNc5fPAJNNQd5naTE+tlCbOSPpPfSf8bVQ8gMdtpwpo32yHuR0OaPbZencumWJ2ZA7o0/UKg==", "signatures": [{"sig": "MEYCIQCM8HXVDVSNzckJUXZDMt51BiGdeuU3U0mA+3eQzgZD0QIhAJVbk32HF5gZW98JcsQEl2VVN843sGNsX0PXz0iVa4N1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8fcc633", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035082}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.5d4e8f0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5d4e8f0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "7d39e0575c355011f7f228c52ec7aa6c7a306390", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5d4e8f0.tgz", "fileCount": 113, "integrity": "sha512-D1jcl6kHQeoItUQeMH0vlFghzDp4zgWsVGBljKJB+DQVHVcVx6hrmtatLMUbDWto/1Srlcc+oKDLGp9rTWC6ew==", "signatures": [{"sig": "MEYCIQDNEkib3kFAW82Gf6l8ZXRfKRsIT6DMI1A8K4j7dxtoRQIhAOXBNSUyaTD9uYtnwdkyPOGeZFAuRuVYz2gCnamTpWJK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5d4e8f0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035082}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.5131237": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.5131237", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "3a5b795d6be50145b54a92c55858d9c43ac7ec6f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.5131237.tgz", "fileCount": 113, "integrity": "sha512-84lpWLY18DcNP8uVYNsLCCwNynjj/lO20LmZnebBDCUBbnRuUH43V1e1jmfKGsu1qqfw0h7DkOh/8PURD1HHBw==", "signatures": [{"sig": "MEYCIQD9zJLt0g6dudbpLLjL9Uj1iFYZneU4PHHfizUQs4CDuAIhAOXO7A1sFDbT7cYDyLQ2Mhidy1vicsaYxp8NRBEbh1Xd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.5131237", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035082}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.1d4c263": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.1d4c263", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "8f351ebf08d867624b9012ea155798181599b277", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.1d4c263.tgz", "fileCount": 113, "integrity": "sha512-JxZmx0ls3ga6l5KXRPZdCpqaDoYmVmYto898zM98zus2Kc3NEu2t9YUuWeCw1cP8rFmJXAZkLGO9tyV60ZqSmQ==", "signatures": [{"sig": "MEQCIDuGGsSRlztaYlEMF3GRiOjwobyFJEwBoBiD0AqrrhIrAiAFRcjlgY3hO+BCjlM3tBKzbO0q3c9TSJr48Tvqb/N0sg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.1d4c263", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.a37f6ba": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.a37f6ba", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "d3731fe1285a3046c042b5422ac0be8e00e0dbc9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.a37f6ba.tgz", "fileCount": 113, "integrity": "sha512-9mk/2bjlhbY5b6je+iPMditGSKVxEU/+DnPaSlcX5lJlO616bxWA0IK9HAJrLNCj2VX3PMI+JSfUO1xRTETfdw==", "signatures": [{"sig": "MEQCH1efDlQKyHqi8enKCTnfSEkMAhw5U/GgSQQb0Dof2TMCIQDV+0N5y+/u7jdzfXzDph42blGVS8mj+6ZfeK1H6SUsNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.a37f6ba", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.58a6ad0": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.58a6ad0", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "df48b9907f4dcc59a3ed75a450a40724f04f7f33", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.58a6ad0.tgz", "fileCount": 113, "integrity": "sha512-ZBGo0j1XLBHAeRqHIRKmZNYtzWDBxixmWX61ixqDjVCtDfDcox60og347M5zCWAiZQGGVxJpgJhYMt3yzHZPNw==", "signatures": [{"sig": "MEQCIF2SJ9NSWSKqXlygiSMrXqdcS4oTmsOOX/Ot55vS0crAAiAEgYavvfVHdMTJCx07i/fG/3zs3rGf0yDrE1ZyKm76cQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.58a6ad0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.4bfacb3": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4bfacb3", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "0422647b29896fa408db27268a59cf3493329d0e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4bfacb3.tgz", "fileCount": 113, "integrity": "sha512-BImlpUa3ijrdkEn6fwehx/LaKXxP32faQ3jiq3573K6f8WaScMxYKO2qMEIxsDXDLDKxSzTRFce2TgHqdGR0Sw==", "signatures": [{"sig": "MEQCIHE/XGR6qM7xsPDs9scgRLqx/Q1aMJ3JfbJJub96cVuyAiA+Gb9P35GBZkUEjO8pefG0RP0qw4LPeKTh5HFH0Dn8Nw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4bfacb3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.193eb84": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.193eb84", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e664e456b8441976884e12667f53af19fb0a592c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.193eb84.tgz", "fileCount": 113, "integrity": "sha512-xbVZtYz8bSGPBG8HwQTdsNKjYO2ozGqz/6yQzn9tbdHnGg7mUhZvP54vVxuMi1LcfIxaWVcqQlvsCt1Z30VLnQ==", "signatures": [{"sig": "MEUCIAhx+kmx8x9ShbPUiZXIo/8Jvx91Xvh4trN4XpUQv0ItAiEAiPRzW/k895AJvSdD0dLrohmWd5+1VDOrwiJoon9wb/4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.193eb84", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.8": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.8", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "c5e19fffe67f25cabf12a357bba4e87128151ea0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.8.tgz", "fileCount": 113, "integrity": "sha512-CXBPVFkpDjM67sS1psWohZ6g/2/cd+cq56vPxK4JeawelxwK4YECgl9Y9TjkE2qfF+9/s1tHHJqrC4SS6cVvSg==", "signatures": [{"sig": "MEQCIHamyZoRF3JfoApxl8RoJ42PaANmpLhj7HJuU6WHU1JHAiBm2h4yfjKld23XS7mGLonVFwexXjsNsS4V37pkm6I7+Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035159}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.3c629de": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.3c629de", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "ddda2dd0795cf5d775bdf720fae9192ada287af8", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.3c629de.tgz", "fileCount": 113, "integrity": "sha512-7yGopE+5eVGNv/qOZTHVT3ONp8ZETG7Y4bpU76iJm3iIMDEjZqqwsrNa0CBXkNY9+as5310TBSJ2wRl9ZV4+yQ==", "signatures": [{"sig": "MEUCIQCQn70rX627OVcAzR2Jh/vvkJnUHgPTxiZkiMS9rsuAvAIgF+jOfdi2AU/xapAei9TlBIaqQKHfokDs2i3or6k1W/8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.3c629de", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.31c0a21": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.31c0a21", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "0c84edcb8103910195d65819feb471f62b27ac45", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.31c0a21.tgz", "fileCount": 113, "integrity": "sha512-iRuHHG6Blg/0pBQTIUjUQNPVP8saTUxUkxR7ZMSTWi6582xu6Wgu6RMCuPKYQuGmriI3zkY+/AlEyceUT07O1A==", "signatures": [{"sig": "MEQCIDkjwIZqsBHaJRTb15lJE9Gg1rmCf7woW3wwd7U70wVAAiBFBZatoFNUK8trSI4TDA2070PyW1i6sZvypvq2X6XQ7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.31c0a21", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.54c86d4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.54c86d4", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "956b061c1e6156e6b01438a358541678f2da0e2e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.54c86d4.tgz", "fileCount": 113, "integrity": "sha512-oXfgjbnBX8X80WBpKwyx3gDMUPUo466ec+OvtTVusqwtWgRNsw8Xc3/gs+DC/2aj6S9Zyy7kKqeyGJkhRCrLhA==", "signatures": [{"sig": "MEUCIA84zOfFDYcQ2bySNPpTMTKLS5lEH1Wxf9xrNq5QJcKFAiEAgPXbgeXMIAYLcmqq0aJM5qYcNsfeBmgtELz+95uvPj0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.54c86d4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.191195a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.191195a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "d8ec7d41d58db0f0feff02b145a03501113597a9", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.191195a.tgz", "fileCount": 113, "integrity": "sha512-rz5LxlwBbwh1cw+znjYfc6UvqU9J1mZA2qRFPcH2QImjYbPY90CsWMcpgO/KDxC+ljsAyQqrGmX6dxjm3b6V6w==", "signatures": [{"sig": "MEYCIQDAC6KXbHJvuZhZJhCGLKYp7y8/OEyn5apSmwrRdtYe4QIhAJ4fYcn7JuBNaGmsnTI9ONf1J3aCCKprxeyURMQ5yZ97", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.191195a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.b3fde17": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b3fde17", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "a26eca0b11f494c91571e7cf98257b43fd3c08a0", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b3fde17.tgz", "fileCount": 113, "integrity": "sha512-fqJjDu+Bv2Eg3eVRAEr0zhBM5oBFvmndiKnCRthfZmevJ8pPK/9Jqd9gk7lJyAZOWJRy6yk7ITXcqGbpMdvgDQ==", "signatures": [{"sig": "MEYCIQCvG+Qh9SMcpPqt1IuihtUED5ap210TiUiykbYVQGju1QIhAPASjzqESrIJhhtsBO1GpqKGB4g9Zxqau6Lt++8UvbdH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b3fde17", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.f425720": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f425720", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "8a478a28d8281c522f24e6610418e48be46cf45b", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f425720.tgz", "fileCount": 113, "integrity": "sha512-sebVh9NYbX3XyiazAbIRESUwKx9+YsU8NZ2W9mZoIoDooHmHbPNMZcQiciXNFNV7gYA7ciYKZeNQUNKpkGXi6Q==", "signatures": [{"sig": "MEUCIQCWLF6z4WGRCTTgUkz8G++CIXIpMGmysx+0MaprWsQw9AIgdW/N3pxDT0x9Sc3f4sYBG3XXO+qVY42A77YELc+dt3Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f425720", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.0c1c0c4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.0c1c0c4", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "3472eed92c85e5150f4c437fa7e118d4d9126611", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.0c1c0c4.tgz", "fileCount": 113, "integrity": "sha512-T59xrRUEoKQntMM/Z9sQLcG9cz+koY3iwwHHV0ZebjFG5YRosKQ0YjU/p8eamYFsPmq/TY/AhUqdMUywBcJKYw==", "signatures": [{"sig": "MEQCIEcPuAsDR/VE+JNB8frdsQ+3fYia6YShaUEyGhSEXMFfAiATF7mJKBIIPFNNS902lau5FN6EuOKipBePT+prTfQk1A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.0c1c0c4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.8bfbac5": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.8bfbac5", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "29ff6e241b37688f00b66f655e28c22a07b34d4a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.8bfbac5.tgz", "fileCount": 113, "integrity": "sha512-l7FaSLhlb6k6Tuc7RCHKwhKM6yP/Qspvhja/EgssaQbAYBVrxLyV6bGE2ov/ZTCaRZPgGx4qzN4gPsKUAVRxVA==", "signatures": [{"sig": "MEUCIQD4zkQDuuP5Po+VOBZcb+LBackhrSmig62WINYkLPHnWAIgbXZrA9zrZET37IJIYmie+ee29AsyoKlWKP1+PrSx6Qs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.8bfbac5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.288ab3e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.288ab3e", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "3573c5a0aec0c8269d750edcd475ef5f71e14c1a", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.288ab3e.tgz", "fileCount": 113, "integrity": "sha512-oIffCPEPChYoy9fKTWdDxZ0V3zzpP7aPWtNDqr+5qALTfhi2XXUDHUPGzMUSeSllXZFkvg34wFgiQ1P/YoxgLQ==", "signatures": [{"sig": "MEUCIQCooqJfYdsvA1YsoMRtU+6cxNGj+2Qqd93KBonq2e8vVgIgcn4RGoSjOz0lGdhUiYTs5/DdWTs6yCI8c6cZG6iT73s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.288ab3e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.63f6a6c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.63f6a6c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "544912804208cf90c0c513977a56dad9b0388247", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.63f6a6c.tgz", "fileCount": 113, "integrity": "sha512-XR5gyQlTkeTw7ifcQmXYpsa3wKONILMXEU2HGnoezucqDHQ+PzPI7xnIBQtRJubP6CQwvh7KBuzYHK5ozPGl1A==", "signatures": [{"sig": "MEYCIQDRNinCUDlpMSZXscYFrJ7W4j+24fJERIkqLV0fuE2iPwIhANlL2zJVAeOJedxC1Q9JU5jmwpYQyqXdTeFJTXERJGj8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.63f6a6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.21ece6c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.21ece6c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "743de002b891160450f2a083ad0478916cb70a6f", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.21ece6c.tgz", "fileCount": 113, "integrity": "sha512-vXmYX0axCYp7hDUVcY7M2+wm+hiyiotM3FSH1ZB5L4wCcLOa9llwsFRfGTW7f97rF5H9MUBidDEH1vpnrXX0/Q==", "signatures": [{"sig": "MEUCIAkudoSlSipTUM3PvYcUQV1ABigqxa3hpnfS4qxJU2vFAiEAzQb2TPsijgWBScjbQYEgWvFuSZdahPUFwL6mHarTw/k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.21ece6c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ada85b1": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ada85b1", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "ec9ca8d8950eedc571246643d976571ee9c99d5c", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ada85b1.tgz", "fileCount": 113, "integrity": "sha512-3ubDeZEwRH0a7yfD4WAwJa6h+1Qo0dHckZgkm3llF/nW0hZrO9cJQcLlQOu0JL0CqoV6W4yjJuiNHg3gphbqDw==", "signatures": [{"sig": "MEQCIF09EHA68+99h1bFQdQPGQDgNvWwJ070ov5n5GNnGu5eAiBmasWgWXQfAY7C+U+fmFQRiuAhJfmfjbcen73AC4JGZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ada85b1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.f0f42f6": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f0f42f6", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "e5514284ba72e8c9dccc42fc2d07eb88d8c8db3e", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f0f42f6.tgz", "fileCount": 113, "integrity": "sha512-uCosOPFkmUz3o4mZSzMTg+HLDj+8E2c6rconNpgWRRWan5kcWOlEVoQXPSCcF5H0LSAsNny54lEhQI1u0sZ8MA==", "signatures": [{"sig": "MEQCIH8uxvmdfCCSxnyCQYyLjUw6F19IeONZjm5hhlDZDyBBAiAUFrEGPgZhao9otzFVkiulYa1Zh3kh82n5c2w0tnUjPA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f0f42f6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.fd95af4": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.fd95af4", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "269c2b1e302f26cf57e8336a3b95808bfc513f03", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.fd95af4.tgz", "fileCount": 113, "integrity": "sha512-THoH81CUOrMikP/WVMFDFyYxTTcxFtMOMeAD9KCujp/zvnCw8dZq3hCk5GwVju1Rpl3FQoQky/GIAfH7tsB68Q==", "signatures": [{"sig": "MEUCIQCo1KgdXMORSmuOiB2vT3XFWwYuITzV1WFh88RsldIkYgIgOlsxWTD689eiMy8AZj2wrZAypcKQTM1o45+GAOYL5Sw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.fd95af4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.bea843c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bea843c", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "46c6fb19931ffad02e1a76d4ec6b998f3c6f7e24", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bea843c.tgz", "fileCount": 113, "integrity": "sha512-bfVngAguq1QtdCfygaC3lx0quDuGtPOrmkKDK1lVvSgc2LkCGatVd2DqQcwS/hRakM4FLISWIX03H8Zk0/vzbA==", "signatures": [{"sig": "MEYCIQC1trcR/7SnQNYXsS80R/D6675VaNgGDwrrB6wfgmgMGwIhAMncdEgeWiqsD67R/C1nbuhF1QwNjeDJ2Mv4yOPXu0kf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bea843c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.da08956": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.da08956", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "3a5823d492d532949ce87e217c29efdb2e55d669", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.da08956.tgz", "fileCount": 113, "integrity": "sha512-EB+wh7fVqYvHXdtQi8NJW6eOdsoiWxhPrL3xoLvb1AwV8U6ST6UoocmJBdqGyJcsQ+Wfch3rqDu3oboDQF1h+w==", "signatures": [{"sig": "MEUCIBFKMjI0v18ho3S3RNGxTAHGRhb8jsehqF0PdPFFL+D0AiEAgX5Cp2b1YuXvQ6suNfiWILH3ziDqnKvjkHITuDn0ddE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.da08956", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.aa817fb": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.aa817fb", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "67f15168890b10858b56a2648a616d43441ef164", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.aa817fb.tgz", "fileCount": 113, "integrity": "sha512-QwYBiHoNhP5s+OXEVCu+z16PUAnJn11ztKIQ6xnTOEWMxXT5u7jSyOYk9lN570a+CpKs5z2LPS+5kiR/1pBbMw==", "signatures": [{"sig": "MEYCIQD/FvQ8J4uCK1mBKBj/k8VjGY3+9+jEQAD0cKYV04ZevQIhAK74FtD9w1dFOVOSKn4TMuMvJIYwxYRZfX/4a+4aW5BT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.aa817fb", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.b88371a": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.b88371a", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "57dd0ae952e6d631b47a62ee2ed695c102782d53", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.b88371a.tgz", "fileCount": 113, "integrity": "sha512-OXSe1b2pluHs3O3lINs24RzLmRzKXh3P7Nt7vvC59TMXbHO615PyBESRVwtsKJUO4IPPTryloZ/4I6/3xm8jQQ==", "signatures": [{"sig": "MEUCIBXi/2yHkmyzDvDeyE4fSjyWk3rodfHoMi9jkmz9kiXvAiEAhKyBpvcXW62R/v3bhnoKv25pl83+cuTP763Ej1kayAU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.b88371a", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.9": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.9", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "99033b8f8196884fe947d15ca0541f612fc4deaa", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.9.tgz", "fileCount": 113, "integrity": "sha512-G93Yuf3xrpTxDUCSh685d1dvOkqOB0Gy+Bchv9Zy3k+lNw/9SEgsHit50xdvp1/p9yRH2TeDHJeDLUiV4mlTkA==", "signatures": [{"sig": "MEUCIFVgmbRf1cf1Mpr+7iypX99MeTpN7G9rgNDnxrql6xdRAiEAxzJOHDoB8HQDQPAAwsM/7KcL4NTl1AWGAiYbFeeP7Sw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035159}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.427649e": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.427649e", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "58949fe99d561d2eb89a0ea8ba0eb564d94436ae", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.427649e.tgz", "fileCount": 113, "integrity": "sha512-YNZgSOT/OvrW2//K/8Ved7YrZ3yj7pYwfArxFoAVuZV3yVcnwC+deYhHR92mS6JWeTOlj4ldc3KVTQq5FPhbfw==", "signatures": [{"sig": "MEUCIC/OGjn52zB8XInIt1HC+XWr7ugBNp1mnWRqVERdfWXUAiEAq/5LLptzWyidEEtwt9ZD4X2Zc6z3udZzwOsDO5v75uM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.427649e", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.ddb0bef": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.ddb0bef", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "59299c7d55871ddb92625851a1362a889fff4d40", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.ddb0bef.tgz", "fileCount": 113, "integrity": "sha512-VVFWJTJzN+g42TJ5cDYyiGOF+W1xpvS9AMZnPapKSovNXWq5BbtQv7WK+BWvzMPEE/45gppQ2+thYZnnYMyGPg==", "signatures": [{"sig": "MEUCIQDdo3acX+Adq3nTZ1Z7HSYHNzLKDSNUUEhNQjU+yD5QrAIgHUW95+2T09P1Hk7DE3io/yV5LUa52W65DVticjtU1V0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.ddb0bef", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.d06bbb8": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.d06bbb8", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "a3fd339c945fd7af66126fe7110357db5328c487", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.d06bbb8.tgz", "fileCount": 113, "integrity": "sha512-QL7U8xe0WuomWcQcga3K+eZ12hiFhXx/SYwtgf+jR3lKT5iB9Nj+qzjFdQs8tB5XjXFZAy2z648uqu5OfnvIUg==", "signatures": [{"sig": "MEUCIC9ELJBMP2hFZqG1HHJrT2ZVzxJ4PPLPBaxMUtACNsXpAiEAk/lpfXuWIpmQASDy3avrZc+OVx9WMXDxqb92oPDNAQw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.d06bbb8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "4.1.10": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.10", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "6e749424db4f6e076371a66da7c4daf1fcd4f9df", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.10.tgz", "fileCount": 113, "integrity": "sha512-d6ekQpopFQJAcIK2i7ZzWOYGZ+A6NzzvQ3ozBvWFdeyqfOZdYHU66g5yr+/HC4ipP1ZgWsqa80+ISNILk+ae/Q==", "signatures": [{"sig": "MEQCIBGdUfscytbQT/vNeBCqq5L2HAuNOpOtv+rjcfzA6yQsAiBEu9ITMTHkCyYdju21To2DpfevrRxyfekaUVSomYEG6A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@4.1.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035160}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.2ebaff2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.2ebaff2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "82613478fe79b1c3be69f042ecaca7d6795321cf", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.2ebaff2.tgz", "fileCount": 113, "integrity": "sha512-/uuZS2F8gcMOSVSrNwz8F9o9YbYtwLTEKPF0xLQ0s+nrd2Bbz90yw2sikLJozDXHDCYnxLvo9qov2wXPbMXygQ==", "signatures": [{"sig": "MEUCIAVzuQIgwAFck66rFtvuz5IZDhvwKC2ki6ySeyhvO7eYAiEA9ruS2XPzjthqYf7rJLxCdobVbekcBSyMVGkv27m5M/k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.2ebaff2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.7f97179": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.7f97179", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.10"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "7b5c76f0381ce7413c500fc031bff626852812f2", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.7f97179.tgz", "fileCount": 113, "integrity": "sha512-wvhLQ/UCoD5NlvrmfkNLGs4rhTbhb4rN/01nGikv2/EWAKivFklsuuKB5vSLAI4jAQVvlwP9EYvTKeaW0xpm9Q==", "signatures": [{"sig": "MEQCIDtJ1Eg/WqPkPC8jmv94rmOGS3hw/dFrbrVvuHV+aHLQAiBreOsg8B/vKCy0yPXrmKq4I38FDPLVlwvsanq3oGrM0g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.7f97179", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13035176}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.bab16ae": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.bab16ae", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "9ed003496e30c7d7e9e23ed6a6892ac9cf3845b5", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.bab16ae.tgz", "fileCount": 113, "integrity": "sha512-asA7qxIAMiF1aaVx9vs/rfJVDos17Urmstz2S1SSiUxA8FwO22zJutVvesBXGdHTd1ICbgf8K8YUjX0PYjx1EA==", "signatures": [{"sig": "MEYCIQD5IQ4yClpRKdz5j/PDzznt1Z6F4p1r0ElaT2zSE0TPbAIhAOYDCEbxjyBxoqWyD4ZCvJciETEkafvADhBdqf4aN7SZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.bab16ae", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13031509}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.4453496": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.4453496", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "2e4f58de8cce704d8c5df8f9989487cd9d455dbc", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.4453496.tgz", "fileCount": 113, "integrity": "sha512-o+ZRC5uF3ViCbbQ4vcrAxZCJ3kON3QU8pcvuzMNkezx841Efdz8AjTJfILLf/QpFFwXgKPXiTGtufuzavxiFgw==", "signatures": [{"sig": "MEQCIB8NCL+RpWESHIv6BYhLFMt//4lNMP0xOAInzYL0V20aAiAzgXhbjzJBgiATWwn6UY8hVsq+H77N9g0nGyYmXj5Uyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.4453496", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13031509}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.63b5d7b": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.63b5d7b", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "cc9a9355f591cb5122b35fb3747d6292d4b09526", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.63b5d7b.tgz", "fileCount": 113, "integrity": "sha512-QNBjqWfPujkGZycWvkdENl8XEEfWQ/xDkoS4J+8iRvfwZWxozMCAXtuC5eZ0hCSazDE0Fhwo7L5lf1J5/B8E0g==", "signatures": [{"sig": "MEUCIH59wT+aHaB7bZ4Wbba3SBYY+90bkM19/Gjy8/5G1PlMAiEA+ra6VHHks+Wk70SkQBCtxHENYdCsKs15H6mKT3Th6EY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.63b5d7b", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13031509}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.75cbfc2": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.75cbfc2", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "c8b1523bec1ac2d5b31363a20ea439a876541a09", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.75cbfc2.tgz", "fileCount": 113, "integrity": "sha512-kYDmrjAJdHArRZTVlXyl8DY3wr7e0XyLeEL3MVYFTlJtcuXBkA4AMxZvO4j+SJlpBffA0nyK0Jm3vh6cllm7Ow==", "signatures": [{"sig": "MEYCIQCCzM+AKd6CJU32bQxPoENzpytKj2V689M8AsJeIUEmygIhAJtAVQlOF/D+YuuiAleKy6XXJRrr4th6dmrj7KEu20UW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.75cbfc2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.f4a7eea": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.f4a7eea", "dependencies": {"tslib": "^2.8.0", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "@napi-rs/wasm-runtime": "^0.2.11"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"shasum": "841484c1a8155f128a692e429882771057157741", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.f4a7eea.tgz", "fileCount": 113, "integrity": "sha512-VVXeOEIsu9T/jIDaOpYjB/E96beDAnD4lG4JNWL4cc0SD7lJrW9cwaQYDg/cOOzLuLbmb4p3A1FN+HltxY0YJg==", "signatures": [{"sig": "MEUCIQDq5h/pD8X3EOEjGEndIVm+ztCrVLoYU5sGrWjr6UPcswIgdsZwRJB77itwMP+LQ/3o+j6KzPCFlltNqpDrUgTr/Kg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.f4a7eea", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 13030638}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}, "0.0.0-insiders.c5a997c": {"name": "@tailwindcss/oxide-wasm32-wasi", "version": "0.0.0-insiders.c5a997c", "dependencies": {"@napi-rs/wasm-runtime": "^0.2.11", "@emnapi/core": "^1.4.3", "@emnapi/runtime": "^1.4.3", "@tybys/wasm-util": "^0.9.0", "@emnapi/wasi-threads": "^1.0.2", "tslib": "^2.8.0"}, "bundleDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"], "dist": {"integrity": "sha512-Tg3nHU11riqnUCrm3ThCgOZ6USZgh29hAIgo/aOyR8ZkDw4OfLLWAreVXk0BPLZkbgD3e6pGafYmcAqvoS7lVA==", "shasum": "f6167ce25e9bd8609b6e19e2cd2b819abc1a99ff", "tarball": "https://registry.npmjs.org/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-0.0.0-insiders.c5a997c.tgz", "fileCount": 113, "unpackedSize": 13030638, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@tailwindcss%2foxide-wasm32-wasi@0.0.0-insiders.c5a997c", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDCij1HPoGx4uRw78jKRhvQDoL5WI/n1q3gmYuIDeM3bQIhAKdNuGDkgROUBnyBJtmsnnXzhNB3Y/wql3Peb17505RQ"}]}, "engines": {"node": ">=14.0.0"}, "cpu": ["wasm32"]}}, "modified": "2025-06-19T19:01:17.548Z"}