{"_id": "buffer-equal-constant-time", "_rev": "10-076daeb5d00013aac8a19df6c8575b1d", "name": "buffer-equal-constant-time", "description": "Constant-time comparison of Buffers", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "buffer-equal-constant-time", "version": "1.0.0", "description": "Constant-time comparison of Buffers", "main": "index.js", "scripts": {"test": "mocha test.js"}, "repository": {"type": "git", "url": "**************:goinstant/buffer-equal-constant-time.git"}, "keywords": ["buffer", "equal", "constant-time", "crypto"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"mocha": "~1.15.1"}, "bugs": {"url": "https://github.com/goinstant/buffer-equal-constant-time/issues"}, "_id": "buffer-equal-constant-time@1.0.0", "dist": {"shasum": "cba0775ef5d8bb0482597255b4b297dcac5f44a7", "tarball": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.0.tgz", "integrity": "sha512-sSkkf0CE5ZrsgqSBQYtuoToZ4VhHsVXwyZJCJ8BGp8Hzaw1PfDk+fDS2FUG504xGBdZgCgs+foWsHeEccUdX1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA0dyzUTbV6Jie/eRd3lLoX5s3TarIBVF1NQIU25bVWwAiEA1swLUroToVS6Cf9r5A5Ze7YBZzBRBl/BZVxvLS2vlJU="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "maintainers": [{"name": "goinstant", "email": "<EMAIL>"}]}, "1.0.1": {"name": "buffer-equal-constant-time", "version": "1.0.1", "description": "Constant-time comparison of Buffers", "main": "index.js", "scripts": {"test": "mocha test.js"}, "repository": {"type": "git", "url": "**************:goinstant/buffer-equal-constant-time.git"}, "keywords": ["buffer", "equal", "constant-time", "crypto"], "author": {"name": "GoInstant Inc., a salesforce.com company"}, "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"mocha": "~1.15.1"}, "bugs": {"url": "https://github.com/goinstant/buffer-equal-constant-time/issues"}, "_id": "buffer-equal-constant-time@1.0.1", "dist": {"shasum": "f8e71132f7ffe6e01a5c9697a4c6f3e48d5cc819", "tarball": "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz", "integrity": "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCxn2UBpu2wlAmln1ChWuYUBMnxSn2adhrYCIB90FGjzAIhAIRdBfoB5qhFGwn6GbSAKAqFI/JmBKVwDFMWXDRj8KeY"}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "goinstant", "email": "<EMAIL>"}, "maintainers": [{"name": "goinstant", "email": "<EMAIL>"}]}}, "readme": "# buffer-equal-constant-time\n\nConstant-time `Buffer` comparison for node.js.  Should work with browserify too.\n\n[![Build Status](https://travis-ci.org/goinstant/buffer-equal-constant-time.png?branch=master)](https://travis-ci.org/goinstant/buffer-equal-constant-time)\n\n```sh\n  npm install buffer-equal-constant-time\n```\n\n# Usage\n\n```js\n  var bufferEq = require('buffer-equal-constant-time');\n\n  var a = new Buffer('asdf');\n  var b = new Buffer('asdf');\n  if (bufferEq(a,b)) {\n    // the same!\n  } else {\n    // different in at least one byte!\n  }\n```\n\nIf you'd like to install an `.equal()` method onto the node.js `Buffer` and\n`SlowBuffer` prototypes:\n\n```js\n  require('buffer-equal-constant-time').install();\n\n  var a = new Buffer('asdf');\n  var b = new Buffer('asdf');\n  if (a.equal(b)) {\n    // the same!\n  } else {\n    // different in at least one byte!\n  }\n```\n\nTo get rid of the installed `.equal()` method, call `.restore()`:\n\n```js\n  require('buffer-equal-constant-time').restore();\n```\n\n# Legal\n\n&copy; 2013 GoInstant Inc., a salesforce.com company\n\nLicensed under the BSD 3-clause license.\n", "maintainers": [{"email": "<EMAIL>", "name": "j<PERSON>sh"}, {"email": "<EMAIL>", "name": "goinstant"}], "time": {"modified": "2022-06-13T05:15:17.079Z", "created": "2013-12-16T19:53:23.556Z", "1.0.0": "2013-12-16T19:53:25.360Z", "1.0.1": "2013-12-16T20:12:17.799Z"}, "author": {"name": "GoInstant Inc., a salesforce.com company"}, "repository": {"type": "git", "url": "**************:goinstant/buffer-equal-constant-time.git"}, "users": {"langri-sha": true}, "keywords": ["buffer", "equal", "constant-time", "crypto"], "bugs": {"url": "https://github.com/goinstant/buffer-equal-constant-time/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md"}