{"_id": "serve-static", "_rev": "271-df047d70d2d19ddbcb946db585e178f0", "name": "serve-static", "dist-tags": {"next": "2.1.0", "latest": "2.2.0"}, "versions": {"1.0.0": {"name": "serve-static", "version": "1.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "98efa31e6ae767b233bc44c77bd29140b2d31c6f", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.0.tgz", "integrity": "sha512-IZh31Mm/Y2i03KU0+nJYyjIXwhsmkM3vWH/5eIINdhm/5F55qJUrqtFPF8qTTNxZeHAISWYpWeVk16MIgQvztw==", "signatures": [{"sig": "MEYCIQDtWPJQoqcWygQ0DkKNB0syEWcxWi0cQkiPnRuqsCpQIwIhAKZFtLVzynrvFySGEAjQencftehvF6cOlsfud/mbZTXY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.1.4"}, "devDependencies": {"mocha": "^1.17.0", "should": "^3.0.0", "connect": "^2.13.0", "supertest": "~0.9.0"}}, "1.0.1": {"name": "serve-static", "version": "1.0.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "10dcbfd44b3e0291a131fc9ab4ab25a9f5a78a42", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.1.tgz", "integrity": "sha512-bo0TWkZYykHO97QfRgoaXQQBBmyheAb3MeYFzufTXDHUdaTwJXFa8NejuKbt7UdovoUzB8lJn4gHGQSEC+R4Nw==", "signatures": [{"sig": "MEQCIBSWSDdjtDAEIn94ZWC2IcrFEMKwAdybrYaLUA9Cjz7eAiAygYe1lcyGyDtdd0uvsacfsFyCo1PuuuuIbaOtgRdPWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.1.4"}, "devDependencies": {"mocha": "^1.17.0", "should": "^3.0.0", "connect": "^2.13.0", "supertest": "~0.9.0"}}, "1.0.2": {"name": "serve-static", "version": "1.0.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "4129f6727b09fb031134fa6d185683e30bfbef54", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.2.tgz", "integrity": "sha512-Q6kwHQK6vejIQnc9PCLGMzXpdjmzC77d/Z2zgHDcYH8sX2fgh6wsQQL5IkAOwvqQ1+JSgpB4EX4T0L7oRtMZyQ==", "signatures": [{"sig": "MEYCIQCXrQ2vDX40fYbmP+SX24GF+Lo6DKoBwr5u7BxO66f9yQIhAP74gWyn2od6LYq08kZ3+/XLZj+uLOnGu0F5ztK8qG9b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.2.0"}, "devDependencies": {"mocha": "^1.17.0", "should": "^3.0.0", "connect": "^2.13.0", "supertest": "~0.9.0"}}, "1.0.3": {"name": "serve-static", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "3443a4002fb50d7fa0a777bb53103301e4d0c38a", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.3.tgz", "integrity": "sha512-8zIPP17hI1R1Clti8ILPeknj8W4Uj09Iiamt7zDDCAckogp9hqGezDdJMIzjuw34w23tc7icCFiEGSNCRJBtnQ==", "signatures": [{"sig": "MEYCIQDjZT1EhgH80e/8s+dcvkOPo6JK1Py8BwgMNXcRhnxJnwIhANUL9se3AdTX92vpocEXyF+Nyf3uLDaUjQssROQ0/CyZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.2.0"}, "devDependencies": {"mocha": "~1.17.1", "should": "~3.1.3", "connect": "~2.14.1", "supertest": "~0.9.0"}}, "1.0.4": {"name": "serve-static", "version": "1.0.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "426fedebe77bad21f373f1efcae09746639fba06", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.4.tgz", "integrity": "sha512-HRe4CmpyO+OdhZDNhmCAU8AVTF4Ed13cRicmREPgYcWN3Yy/tr4brktDzW1M0omtv2ga+tgnb5hivkBV4MIEWw==", "signatures": [{"sig": "MEUCIBjLnQre205qBoAkMruAaL4i0+zh53nPpwdlTQBqVl/+AiEA62P1hM4v7U3LXAMMdzo9EdNEff91Km9w3vHgrXxLYYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.2.0", "parseurl": "1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.0", "connect": "~2.14.1", "supertest": "~0.10.0"}}, "1.1.0": {"name": "serve-static", "version": "1.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "454dfa05bb3ddd4e701a8915b83a278aa91c5643", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.1.0.tgz", "integrity": "sha512-vzgWiHz5xrM19pqugiYI6sWP9B0+K6vz4Ep5G1my9lVhuYkRXGYs5xtnXZ06fpLPRumROSZ1CLqiRxdngPkojQ==", "signatures": [{"sig": "MEUCIQC4/eXwKvFn7mikgl4k90AtbDmXzYfbjAh2AntTcfInpQIgfFtn6+LFnHbxtJbThmmbnaP0BJKkYlooDS7RtOPu90g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.3.0", "parseurl": "1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.0", "connect": "~2.14.1", "supertest": "~0.11.0"}}, "1.2.0": {"name": "serve-static", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "b711bde722cad70686c1add385c6020bcdb7d295", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.0.tgz", "integrity": "sha512-C7sge0nUxEukR6en8ADwydHwDYt/yqesodzlE2rz/+9srd/icjT1yeLQZI4A59ygbrWNB1dhLgwfMbUbpB0zEQ==", "signatures": [{"sig": "MEUCIQDlggJSHjaLZTAqT2hnz7WF8mBUCU7wUAJud+MWyyn8MAIgJd8XxiBsQ0XJ8yhM9EnOG0D/BrHk+rgxiwuVvIXIrsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --require should"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.4.0", "parseurl": "1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.0", "connect": "~2.14.1", "supertest": "~0.11.0"}}, "1.2.1": {"name": "serve-static", "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "a800a9de23dbd1ffb1258edb986128ee4a4ea03d", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.1.tgz", "integrity": "sha512-R<PERSON>r<PERSON>emuggK5mxL31rrJLTVDRFWdRREVLdHsR3nPHA/zxH6Mm/4I595LOBO84PE1YjbC/hTKi3ZqfyXPhBdYjpw==", "signatures": [{"sig": "MEUCIQCjizFJYVbv8Lyx+gMlq6lmXffkUmqA1RZFitxZhhO5QwIgeQz2smWxfVi0xNosu3A2iACyn3lwyLGkMr1FrxhbOCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "a800a9de23dbd1ffb1258edb986128ee4a4ea03d", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.4.1", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.2.2": {"name": "serve-static", "version": "1.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "6ffc6c23fad03bcd0710eceda844123bd71bc951", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.2.tgz", "integrity": "sha512-QVxS4GoyrLCl2w5rp4Cs3CqT/QnREwKPAsy+aZCG5P9qwLa3GIXoxxzNvsgGfvngtl/begF+UiRgnpDzyiqWkA==", "signatures": [{"sig": "MEQCIC4Prc2pHkTAhjVw9Uqs3tKVCzbGVt3eVuXXrkL52/XWAiA/abr9V2Pzrp3bpM3RR3qHwMvmU+rK2LsSgC0tYS8rkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.4.2", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.2.3": {"name": "serve-static", "version": "1.2.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "93cecbc340f079ecb8589281d1dc31c26c0cd158", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.3.tgz", "integrity": "sha512-xaOEJYYnhmT2iVnDHcPullns+dFGC18BHseW1ZzkddtPWe4Ot/ZdifPFYk14r+tdWpVNWtXClRRENQ9ODd1Eeg==", "signatures": [{"sig": "MEUCIBm/DS7wOedseXrvBxUwL0yLQkUSXTTkZeawRS2spXEAAiEAz6HEw8crzv8+PjAgz+ftdo5Jr0cst67V6F/lTf/X/4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "93cecbc340f079ecb8589281d1dc31c26c0cd158", "engines": {"node": ">= 0.8.0"}, "gitHead": "9b62eb425f96e421e324cbe23552c214153d6034", "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.14", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.4.3", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}}, "1.3.0": {"name": "serve-static", "version": "1.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "0aba0b27c1b8264eee1a3f9c615886738d9727cb", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.3.0.tgz", "integrity": "sha512-MzfQemPCtA1VU/fj2c1Qja1V6cPaqc4nfYRDhhdgJiZOXCu1pr1zaYiXA7jBXbDk0I2aEA+AzzSiMdat2SMPyw==", "signatures": [{"sig": "MEYCIQCs7QJU56qegkJI41xZPxJKBUw8bjyaQVqgwluvhecH2QIhAPIe9P9IleWb3w+kYYyhWdZ99uQ9QvWdrgDiXFolLniP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.5.0", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.13", "supertest": "~0.13.0"}}, "1.3.1": {"name": "serve-static", "version": "1.3.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "95489d1bcf491d54350d5aeeb2cca53cd3b12d4f", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.3.1.tgz", "integrity": "sha512-E8AezYuv/NZau8nb5PxjMa8Wch19/ikIKJuguze18uSgL28ZUZtmguer3ACrbpwDkD03ZULrBoz2i/3nt/q3Xw==", "signatures": [{"sig": "MEUCIDr+NYNcUdsY5t+U1fEDsvZpVeJd65KpdzNS6x1pHZLaAiEA1VxMJ3GzphBO0ChaLGs5PIIqEmBJNKxSLQI1tr8ETrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.5.0", "parseurl": "~1.1.3", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.3.2": {"name": "serve-static", "version": "1.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "d904a6cbf55f511c78138f6f45ee6e69d9d105ca", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.3.2.tgz", "integrity": "sha512-KwjCeYUx7IM1neg8/P0+O1DZsl76XcOSuV0ZxrI0r60vwGlcjMjKOYCK/OFLJy/a2CFuIyAa/x0PuQ0yuG+IgQ==", "signatures": [{"sig": "MEUCIQDQpGzkAev8vCx7+/v3ioHLoE49CMhQ3R1JGyLM+y+tBgIgGZjgxA1J6YQphuuIz/cNXmCOT0FG5ZhYMPzN5PLpKV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.6.0", "parseurl": "~1.1.3", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.4.0": {"name": "serve-static", "version": "1.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "03c6608035158e3bb999129d9793cddc7e0db772", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.0.tgz", "integrity": "sha512-6hk7O29Y7fPquVCY541E4I1cALtAzc1Rx2/K0tHduYMERnFeIGkpi8BZ3tvQK270cxdq6sXrIk5nno3rEilSdg==", "signatures": [{"sig": "MEQCIHLZ+7RKtam2U+CUhuky+kxbon+c/5oqd2eHTKzrQORMAiB+7SLcNjsLKtBmkTR1y4PlMfdtGHcJUSn3yZeJaSlQZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter dot --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.7.0", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.4.1": {"name": "serve-static", "version": "1.4.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "6814dc11c575db0394883af5ec2202ff989491b6", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.1.tgz", "integrity": "sha512-7/19DczZAYS/IIGo/32S51h5U2+qhiqHrs68Chw56PS2mBtsE2n/AYl0niDdxmrnrBibrf4qJpgtSKiPpB3uug==", "signatures": [{"sig": "MEUCIQD2wtwY7aRXJCCQ6YSwvp6M9e7PaUw3koDipxE/KSDoNwIgOCVyWhpj8Jo7n07yYp9DVPgwYzPo/rIZLCnGBC3KcHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.7.1", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.4.2": {"name": "serve-static", "version": "1.4.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "0153b12368318402827aad902d0f124e79145092", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.2.tgz", "integrity": "sha512-oAN8Kjy/kwdqs7XeRUt7iU0b1eLiBSDMRh9EVbrg+WSZNLLNZfNXunlQ6wJF8uRWCIF4afV8ITcL6hzeu+c6vA==", "signatures": [{"sig": "MEUCIGkjG5fUJ5Tw5tNNaufSEvSDB0E8lJxRSZDJcMDSsmj6AiEA2vuJTT0BmLLZD7B3a/soOHuPlW19i283KHhrYg8zoG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.7.2", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.4.3": {"name": "serve-static", "version": "1.4.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "9f08c7dea1b15e2eb1382ae0e12b8a0de295de52", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.3.tgz", "integrity": "sha512-CPLHZOJB7QVLWUaKVhp8Z59m1pPGpps1ixRnxjcPMVpJbAkiivpdeW/kIxZwWhJ8AZC07iax/Ga/+s9XyOqn+A==", "signatures": [{"sig": "MEUCIQD7pz3Rk0ip6sscnw/SHZUmvoKRNkVgSLnHJFisPFwRtgIgDdaqehDYfpX1MOsIG0+2/S6rPHNrBQcfGTxuElr3xhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.3", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.7.3", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.4.4": {"name": "serve-static", "version": "1.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "9dc99f37a2c5e28cda2fe6045114620a62032f29", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.4.tgz", "integrity": "sha512-pxuoykDJ4REf20MFjVk/RHPvogr1dSiNSd0YJU5GjBM4/tkaQV1sjm5h/tfRvrQwbn6comfRKu81uUeiQahumg==", "signatures": [{"sig": "MEQCIEQk/PKfP0EeDQ2tRcgxRw4K4ET3ZwKemQ158o/tbFumAiBDLyrABL/iO5mtzSp9vD1lt34NxPS/vIPsXpEKv3JDpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9dc99f37a2c5e28cda2fe6045114620a62032f29", "engines": {"node": ">= 0.8.0"}, "gitHead": "90b8f98c641a4c26854754e405365b1d5a388e31", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.7.4", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.0": {"name": "serve-static", "version": "1.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "c0f19e3cb9bef0203258db282a3ddda9cb8e675c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.0.tgz", "integrity": "sha512-2xTDUmQCotD/MSVSYK7D5zgah4skqwGI/Awq63MI1tsOCwVbZ7zrpL9qrGRLPpWkfhN6jxh6OVPCgbwA3I+PBA==", "signatures": [{"sig": "MEYCIQD62MbEOP60UZyPrDqSZC2fGcybsPV4nfJZgNd2SrsLOAIhAOTJoOOKGQJ/duvN+8D8B8djycEUZtET9UrGxeFez7Vm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "c0f19e3cb9bef0203258db282a3ddda9cb8e675c", "engines": {"node": ">= 0.8.0"}, "gitHead": "b292a569848a3a7f60f6c87eabc87780c0954311", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.8.1", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.1": {"name": "serve-static", "version": "1.5.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "86185e202015641a1f962447f5695605cd8aa9c2", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.1.tgz", "integrity": "sha512-FifcURE12EyFDTjyuNhig8P7miiZwGUpr6H7Wn0FEn9jI7CpB0ITMFplDxH/HEIhkeMgOR63xfdhE7wGOzcucQ==", "signatures": [{"sig": "MEQCIDlAdlIpoK7ZyknyzzNCZ2SzVuLNv1N0oWj8FDrI4lRBAiAxJlGpRPTZqqcOq2Dpg9CZ/7X2hSl4onDmKUjGHMckug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "86185e202015641a1f962447f5695605cd8aa9c2", "engines": {"node": ">= 0.8.0"}, "gitHead": "e9df84943e8104ca3cdbf75096964bbfedd3b180", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.8.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.2": {"name": "serve-static", "version": "1.5.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "565d369193a075edac7fa973550d88df154f7b66", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.2.tgz", "integrity": "sha512-tVKWRScaHUvXsCYhIpf5Qe6ieGhogvh6f8wc+D+P11bly6Gqlh0VM1eMzoZvZfjHu6w8ZQyLecDsoZwWRrmZRQ==", "signatures": [{"sig": "MEQCIBozQnR27YZtrRlwxrpSUok3jfAQLXYiEHEk3vwJtvkKAiBWA6X6PkXknzG1dB9yt3Mwno8/B/jBmQCuWuXGQDQ0yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "565d369193a075edac7fa973550d88df154f7b66", "engines": {"node": ">= 0.8.0"}, "gitHead": "72f7362176cf62172617cd795d6c94b295f0c610", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.8.2", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.3": {"name": "serve-static", "version": "1.5.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "2e28efa5899686fd3ccdb97a80aa464002244581", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.3.tgz", "integrity": "sha512-4K9GvOWb3zKUT6W98vplGGI0Ea5xh1JnD4T8Pyd+61OwQ/HLszGxNgj7qoMY0fGmo1WJKbp/OEHkOBP69VlfaQ==", "signatures": [{"sig": "MEYCIQD39h1k3+1GsbArvPKYOtT6fQBte4QTK9seZHKJTKKKUAIhAKBf2VtPlYmRt2kgLEUkpJcPHVo0tQ8pZ2pzy2RUi77W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "2e28efa5899686fd3ccdb97a80aa464002244581", "engines": {"node": ">= 0.8.0"}, "gitHead": "7c237ec83ee199d181b5abfeab2ee986c1394dde", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.8.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}}, "1.5.4": {"name": "serve-static", "version": "1.5.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "819fb37ae46bd02dd520b77fcf7fd8f5112f9782", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.4.tgz", "integrity": "sha512-6dxbEeZO39/rL80vlKRi6OatwVN+f2cAou95AVKOQbMKuZ9ln2rDMjjRQN6tu7u9daaaDsU1+hL4NW7zrSMzZA==", "signatures": [{"sig": "MEQCIBK1j5OIPSYLNSHiQ7Eo2l1QzdP/zVc2M7cJSW7NuuuvAiBVSGKP+54FwBorSnbADgzGvBcdvkG7GMG/eykzDohaZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "819fb37ae46bd02dd520b77fcf7fd8f5112f9782", "engines": {"node": ">= 0.8.0"}, "gitHead": "8f3185f75261cc7b2e87c04cf1377e154bdae1a7", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.8.5", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.6.0": {"name": "serve-static", "version": "1.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "283f43b9051293691ab4979bf2e09b4482517677", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.0.tgz", "integrity": "sha512-gEzzllPQwCxyS4IqlQElF0JLrjaAQuyW7MHgSp7UIWaCznTLyVdLZvq3EYN9sMwXnMDZb+fv+IUfNMqlUAm9iw==", "signatures": [{"sig": "MEUCIQD7MiL7uP4YXjlv6bxKUeeLG4RJ6flCGpdOsHWSvW8GdgIgEY+FeuqVztLR7JmivREi2IuEgvOzwLjcxhn6d6wKdbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "283f43b9051293691ab4979bf2e09b4482517677", "engines": {"node": ">= 0.8.0"}, "gitHead": "229f5486e87518ee88b4fd0c5563e02126032121", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.9.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.6.1": {"name": "serve-static", "version": "1.6.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "2f257563afbe931d28cee4aa3dfeddc975a87193", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.1.tgz", "integrity": "sha512-egcgtzipGNDCWaUpxNlNw2gzSFac4s+5K1q42xfnrMeCP2IVyWR/LECrSwJ77TSvX9TYqLOPN24xSvE6Wt/wqg==", "signatures": [{"sig": "MEUCIQCjwhYPFYohwKyntP4OhBcBGQsLloX7o8G9BY4y0alouQIgQ29uBnwamwOeTpwwp9bpedv7IZlNdfDZUdHwCxFMT6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "2f257563afbe931d28cee4aa3dfeddc975a87193", "engines": {"node": ">= 0.8.0"}, "gitHead": "8c96c3815cd0c96cbe8af68f303c2d36189d3b88", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.9.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.6.2": {"name": "serve-static", "version": "1.6.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "c1390ff43941867250296b091391d25be7c87571", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.2.tgz", "integrity": "sha512-ve7so5UBrn1xrWYsANfaRxj2FSvJqs4nd1zyi9fmYCLHdhR8wKiUZWPRlUZBuxAh4/rXAd3wJMLrTqePie/l2Q==", "signatures": [{"sig": "MEUCIDXXbgLPQtthMjUA+GObbuRuyavlzZzoNb5b2M78yJjYAiEAoMaU1XTJcvux/Z7NFDQ8GnfnAW8z4KLmOtbuOUzwlOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "c1390ff43941867250296b091391d25be7c87571", "engines": {"node": ">= 0.8.0"}, "gitHead": "7053ce75b7091a891d3bcb88a2b19b1b0692396c", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.9.2", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.6.3": {"name": "serve-static", "version": "1.6.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "b214235d4d4516db050ea9f7b429b46212e79132", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.3.tgz", "integrity": "sha512-W1gpJoF2Dq98KXDEmwujmsIBN2v9fwPTeU8Inf364RYHVgvHlHHPpzd7OMMO0LkqVE0OtivjQKKJjx+Q5qmvaw==", "signatures": [{"sig": "MEUCIEIf7IK87Chw+iCL3b/Pi36E2q5+mb7X5fze4ImNfZTSAiEAzjVMdT2/DQsAaSB+/ZWOBM6SOphwJCIGUZzMhJtmLag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "b214235d4d4516db050ea9f7b429b46212e79132", "engines": {"node": ">= 0.8.0"}, "gitHead": "14deeaf397728ee25119bf340eeb37e14ab620b3", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.9.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}}, "1.6.4": {"name": "serve-static", "version": "1.6.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "c512e4188d7a9366672db24e40d294f0c6212367", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.4.tgz", "integrity": "sha512-etjjC5zMPVQSsUHwO8iFPgmpPDJzZOJAgYzGzuYfkgcJe3qTWGVOqAK5OQ2R6oNfLdwIlhlu4AhZs/7AGcZjJQ==", "signatures": [{"sig": "MEQCIAb67fhMkGjqqRtVr5zRohgXpsOdsksTCpJSkWZthe/SAiBfwyuJzCSS06tCVcPMLnyUod4yHUWHv4snsed3tob6mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "c512e4188d7a9366672db24e40d294f0c6212367", "engines": {"node": ">= 0.8.0"}, "gitHead": "f1308134d21aeaf5849a3d41c3a04b1779819f3c", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.9.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.7.0": {"name": "serve-static", "version": "1.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "af2ad4e619fa2d46dcd19dd59e3b034c92510e4d", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.7.0.tgz", "integrity": "sha512-Hm9zbsZX98qckeJDfebqL651kfXr6qU5wOZnBcLR9tvj+EA04hvOX5EiDAq5XGtprK/xNBz1e+RdZczihvnV7A==", "signatures": [{"sig": "MEUCIE9Ra+MwxByYfzjK6ezetWjMG46sukgAVtY+LAm0sH1jAiEA55hijZ+06hZBzCb9rLv547QAJHFSBIZm8l6K2bYMpdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "af2ad4e619fa2d46dcd19dd59e3b034c92510e4d", "engines": {"node": ">= 0.8.0"}, "gitHead": "25a23406b3447d7bc5af283f158da7c4ad05ba03", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.10.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.5", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.7.1": {"name": "serve-static", "version": "1.7.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "6ea54d5ba7ef563f00e5fad25d0e4f5307e9809b", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.7.1.tgz", "integrity": "sha512-LOjpb+p1TgwkUFZ/bChRfxB5ZcmuITZGJQ+D3e5u8hjg4vuiIld78E9tJioDgfRoYQc3YKg1Ue88Y0hyflzpGg==", "signatures": [{"sig": "MEUCICxxvIyDC6tnqZQf7K9H5o3lZ6cuAimJPncTWkTNHHgkAiEAhHRwXu0+TRuoRUpNV4skQYRAA8UKSzjT27MYl+HUlGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "6ea54d5ba7ef563f00e5fad25d0e4f5307e9809b", "engines": {"node": ">= 0.8.0"}, "gitHead": "61f59894e6a3d41532383ca440a395772bcdc8ed", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.21", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.10.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.7.2": {"name": "serve-static", "version": "1.7.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.7.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "3164ce06d4e6c3459bdcc9d6018fb4fb35e84b39", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.7.2.tgz", "integrity": "sha512-/PaWqZFkjFYXqGLOs6nc0ameuFkzzU0MrdKt6oxdv7EZ7Dx2/X2LKMu7VZs8/oHt2//+bBI2z+Stq0EE0F9z1g==", "signatures": [{"sig": "MEUCIQDQj2/slHHIc7lQCR36xOVmpjUIgUHRXC4fqwn/tiWkVwIgFxm4Cpn2FKBC8IVfKyoGbIEqE9KkFmQIjmpXUk+JULI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "3164ce06d4e6c3459bdcc9d6018fb4fb35e84b39", "engines": {"node": ">= 0.8.0"}, "gitHead": "40f88bd0269cd4f4ffcb52bded570ad57e4b56ba", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.10.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.8.0": {"name": "serve-static", "version": "1.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "239e57bbfce030a8933d274e3fe7b55492ea267c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.8.0.tgz", "integrity": "sha512-Omtl+YSv9qXPn6FZbWPBEFSFmEGEZ1ljyoOHypj6WilV9GfXUxcPJbXy5H6ZHQtBKJyFE0fO9SiPECIRbJ7S3w==", "signatures": [{"sig": "MEQCIDFy7nE2TvbDoyMy8mglyGfiGH9bDtuX5Yf343JWoh48AiAxklWgu/m+qgmIOIUsWyxetoE1Jspw8Q9mzefwFOVduw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "239e57bbfce030a8933d274e3fe7b55492ea267c", "engines": {"node": ">= 0.8.0"}, "gitHead": "dadd5479f4316a1201817c6b39be67e2417f3a51", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.11.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.8.1": {"name": "serve-static", "version": "1.8.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "08fabd39999f050fc311443f46d5888a77ecfc7c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.8.1.tgz", "integrity": "sha512-6VxXIbQ64SVDQ+tcPHuibd5NUI23jRY0BQhafagbv1wahH5LDslYIpJtYl/KQrRSu1U7ax54+gWFm9yrxqLBiw==", "signatures": [{"sig": "MEQCIDZp7njAs52F494hMNNeGb/cYHfCe2oYk+aLGdmIX4r4AiBvrPtFtCzw+GgN/M0XkCgSiZh9ok++DOPu436Fywaz2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "08fabd39999f050fc311443f46d5888a77ecfc7c", "engines": {"node": ">= 0.8.0"}, "gitHead": "5a47eff4e550f30a7a1e5fb87c8656a1b8dbb249", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.11.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.6.5": {"name": "serve-static", "version": "1.6.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.6.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "aca17e0deac4a87729f6078781b7d27f63aa3d9c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.5.tgz", "integrity": "sha512-3G68ufF2viLyU6obD2dBivUFwkQdQdYVA1Luj/1UTAZOUcaMZzWsmg5y3jgVErY+/QzodH2h7B2+d6SFGMX4Ig==", "signatures": [{"sig": "MEYCIQDz3Plz0zbbaPVBCDpe7WQRxOz4ETc1/yk78CRe4MWJWQIhAIs9bGV3s7VbF1ykG6ZSEy4izI4LJ1vmkeMAUjaXNWeI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "aca17e0deac4a87729f6078781b7d27f63aa3d9c", "engines": {"node": ">= 0.8.0"}, "gitHead": "07632f27cd7690f516f4f4994279cde4ad6c01d5", "scripts": {"test": "mocha --reporter spec --bail --check-leaks --require should test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks --require should test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks --require should test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.9.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}}, "1.9.0": {"name": "serve-static", "version": "1.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "d304085813ee0a9b3e1c068c9062a56ad8424b44", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.0.tgz", "integrity": "sha512-KweArFzuxsvl0yh9yF9aU1Ob9bMw6/wO1trMyZIN9N9jl+SCjhthdsoT6ani90YncFO8l5m6m4sEzlEKF26LKg==", "signatures": [{"sig": "MEYCIQDw+a00fZGlZDkePKqpCPKGZx9B78OlxBM2L9zh1wF9fAIhAI5M36stKGIVMMX5UasFQ2SoyHEvc/uvg6B1hYZz5RgU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d304085813ee0a9b3e1c068c9062a56ad8424b44", "engines": {"node": ">= 0.8.0"}, "gitHead": "0909dce1eec7dd5cf0cc29ebc9deb3ea1fb56636", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.12.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.9.1": {"name": "serve-static", "version": "1.9.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "a611b2b8a2cfb5f89685f293cb365f3f5eb61451", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.1.tgz", "integrity": "sha512-VHoWApCVs9yhKtgyigJzhOsnUuC6hdKCZcLZMGlf30E0PV/W7V/Ee0l3OmIZ+XBkYS+ZuuFdPpluhz0cEb4K1g==", "signatures": [{"sig": "MEYCIQDJID0wg6Qnjz9j/VmNLx/aARCTqbBhJrNF3SE7ZwJK9AIhAJb8oAqB6sPG0xN59boy1ZwlSUh+C1ef2R/hnogzTIxV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "a611b2b8a2cfb5f89685f293cb365f3f5eb61451", "engines": {"node": ">= 0.8.0"}, "gitHead": "8cce88d079c19cb8ace548f60bd216622de993e1", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.12.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}}, "1.9.2": {"name": "serve-static", "version": "1.9.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "sht<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "069fa32453557b218ec2e39140c82d8905d5672c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.2.tgz", "integrity": "sha512-TZZCQzKNgIt+c3KGhWOVUYewLFFg8fNLKjDTBb9LE7TP/gELWj5RLSakxxNGcw7FrP1FFz+oJlE0WsHRXJHkRQ==", "signatures": [{"sig": "MEUCIQD1PrEi4Xx74BBRy4obwaRmA6FjQf3J7cnEYi0lHuB7VgIgfUDIRbeUw6aEuyopPpImrO6gB5qon9gMLcizHyjZvVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "069fa32453557b218ec2e39140c82d8905d5672c", "engines": {"node": ">= 0.8.0"}, "gitHead": "6446e1c45de75f143b36ce60dd75c4daf52d2376", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.12.2", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.2.1", "istanbul": "0.3.7", "supertest": "~0.15.0"}}, "1.9.3": {"name": "serve-static", "version": "1.9.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.9.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "5f8da07323ad385ff3dc541f1a7917b2e436eb57", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.3.tgz", "integrity": "sha512-RzgLgiNjRMhvdnLWKYJtr/QZ3q8jkDv10loWizQMh53Zlmd2jId5PtarLJO9Z6RtQJ/VcZYkvMOIDTOy86h5qw==", "signatures": [{"sig": "MEUCIHMEDTWCSbFKAUlfSMLYnavVY9dE16koF45ikBINkOA6AiEAsLVVmUD/DygnSGXrM3c78NupFen/U1aUkapQf09IFUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "5f8da07323ad385ff3dc541f1a7917b2e436eb57", "engines": {"node": ">= 0.8.0"}, "gitHead": "c76d20a9f51a15a467eab2b0610e5de60506dfbc", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.12.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.9", "supertest": "1.0.1"}}, "1.10.0": {"name": "serve-static", "version": "1.10.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "be632faa685820e4a43ed3df1379135cc4f370d7", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.0.tgz", "integrity": "sha512-5z+SPxV9MbA/HsSiCmDgiyVdvtFHMJr22cvVVBBrXR9tJpUEuPNc+T1AiUyX/MOsCMZkar4lIvjNKRnzS9dB/w==", "signatures": [{"sig": "MEQCIQC6RiQZspYItWsPG7RGaQR10Vz5o8WX748cIX7chveD6wIfBULi90u0Dp936RzhDgVoDWtys2yk7i4iIio+qu4AXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "be632faa685820e4a43ed3df1379135cc4f370d7", "engines": {"node": ">= 0.8.0"}, "gitHead": "856c5e0f796a8988525c356018594bfb8c51a4fa", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.13.0", "parseurl": "~1.3.0", "escape-html": "1.0.2"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.9", "supertest": "1.0.1"}}, "1.10.1": {"name": "serve-static", "version": "1.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "7f80024368d7fcd7975d0c38844ec5d9b2c43ac4", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.1.tgz", "integrity": "sha512-YBqObpazWsQrMrqIMh4eup8Ht7SqN9eEpk2bJ74pGoGT+3ARM0Pi6HyKCuoPclhtd19vm3Bo6zKprX+jLkXcZg==", "signatures": [{"sig": "MEYCIQDSrGLH23UgbGBsIpMhkSB/vv4Pm48gK84Wr4dVvPdqYgIhAOIw76QYYXxECNhkUiu7MG8QHlEGruyNe8xYmLx41gFg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "7f80024368d7fcd7975d0c38844ec5d9b2c43ac4", "engines": {"node": ">= 0.8.0"}, "gitHead": "8a5da6bf09f515323fd4a669b8f8074762bdf678", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.13.1", "parseurl": "~1.3.0", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.3.4", "istanbul": "0.4.2", "supertest": "1.1.0"}}, "1.10.2": {"name": "serve-static", "version": "1.10.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.10.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jongleberry", "email": "jonathan<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mscdex", "email": "<EMAIL>"}, {"name": "fishrock123", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "feb800d0e722124dd0b00333160c16e9caa8bcb3", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.2.tgz", "integrity": "sha512-s6sZsL6UwRJqhLRxeHQr/YClnm5p+5/AVW21LCnl1oiWI1j/kvX4jfK1Cf3g8XbGE81Qq16DW8ZUr3zCqwVd1Q==", "signatures": [{"sig": "MEYCIQC/g78P2nxecV7VXSoVk6rGrYVAwX/aq4rlVsIqJKLrKgIhAJCrt/Pbh/ie+22hPyXGOUHV/eWDNECNpoMUjwy2tP7+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "feb800d0e722124dd0b00333160c16e9caa8bcb3", "engines": {"node": ">= 0.8.0"}, "gitHead": "aec36c897a33c6c2421fa41cc4947042d67332f6", "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/expressjs/serve-static", "type": "git"}, "_npmVersion": "1.4.28", "description": "Serve static files", "directories": {}, "dependencies": {"send": "0.13.1", "parseurl": "~1.3.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.3.4", "istanbul": "0.4.2", "supertest": "1.1.0"}}, "1.10.3": {"name": "serve-static", "version": "1.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.10.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "ce5a6ecd3101fed5ec09827dac22a9c29bfb0535", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.3.tgz", "integrity": "sha512-ScsFovjz3Db+vGgpofR/U8p8UULEcGV9akqyo8TQ1mMnjcxemE7Y5Muo+dvy3tJLY/doY2v1H61eCBMYGmwfrA==", "signatures": [{"sig": "MEQCID3sMIzzWZXIENVKgLASisr5mtoF2pMVdZ0R1JhCyHL6AiABY5F0iSNykl9eCMhhTH3wNvlKFQB69is1+zu2A9VXvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "ce5a6ecd3101fed5ec09827dac22a9c29bfb0535", "engines": {"node": ">= 0.8.0"}, "gitHead": "8be028d005967471832109d777daa4b45bd1948b", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"send": "0.13.2", "parseurl": "~1.3.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.1", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.10.3.tgz_1464664781274_0.7150349044241011", "host": "packages-16-east.internal.npmjs.com"}}, "1.11.0": {"name": "serve-static", "version": "1.11.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "dbe5fb4e4b63d4d11a824b5be3f368907e675bba", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.0.tgz", "integrity": "sha512-6QNWmFLt0GBpn4f5FQ3a3AsNmOM7D3IrXsiLDoYK/SJVthOP7MSd8VhQJzKROFIwWIl8iRj/JPL90Hy0f3RZzw==", "signatures": [{"sig": "MEUCIGcg2tVXPYzltZGWpxnIdqWy6yLFwPqw589UU68baj1HAiEAqZ/jE5Wc/gObIuk+GjL0EZhwWAaRBBDBz0dmLLcrrMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "dbe5fb4e4b63d4d11a824b5be3f368907e675bba", "engines": {"node": ">= 0.8.0"}, "gitHead": "28022afd11828493521162287b550a508f60769f", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"send": "0.14.0", "parseurl": "~1.3.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.11.0.tgz_1465366666509_0.32959614507853985", "host": "packages-16-east.internal.npmjs.com"}}, "1.11.1": {"name": "serve-static", "version": "1.11.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "d6cce7693505f733c759de57befc1af76c0f0805", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.1.tgz", "integrity": "sha512-SGBgnvUc8p5pZdzo4DMTFZQlzsCGGV7rqb7owrM2FAW7gNJ2Jf2T45Ej/I9sSZNGdx1V2+MXZ0bUm7JMmTYU/w==", "signatures": [{"sig": "MEQCIE1Z6vVcgIxtoSnKNpklKdb/iQv/4pPq25WYXnOuS3lHAiB6Isj2yLmFobpjQaDffV16K+D7vlRJNsUTGtAL5mxq1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "d6cce7693505f733c759de57befc1af76c0f0805", "engines": {"node": ">= 0.8.0"}, "gitHead": "b3a24df138ea2f2c43afcbee0dcce5badf4c78ae", "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.4.3", "dependencies": {"send": "0.14.1", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.11.1.tgz_1465608601758_0.0030737747438251972", "host": "packages-12-west.internal.npmjs.com"}}, "1.11.2": {"name": "serve-static", "version": "1.11.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.11.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "2cf9889bd4435a320cc36895c9aa57bd662e6ac7", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.2.tgz", "integrity": "sha512-nBt9IVflCqc4pEtjttEgnwUJXBdy8xk0yZm16OomALNUKVa0S4X6pupZm/92j7M1AbPrC1WYkjr6HjtLeHnsAg==", "signatures": [{"sig": "MEQCICawbmzeH8RBYnO9P4sAwOpU5dI1H8Ei2aVsSdf97yVcAiAA42ODH1PaR4DnikkI7eHISZpc4QZAxG/bUyy7Tj0IPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "2cf9889bd4435a320cc36895c9aa57bd662e6ac7", "engines": {"node": ">= 0.8.0"}, "gitHead": "01f2a83d7456ef03a89e8c951c757dd79ae92522", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.6.0", "dependencies": {"send": "0.14.2", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.14.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.11.2.tgz_1485190261958_0.8670230756979436", "host": "packages-18-east.internal.npmjs.com"}}, "1.12.0": {"name": "serve-static", "version": "1.12.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "150eb8aa262c2dd1924e960373145446c069dad6", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.0.tgz", "integrity": "sha512-wUl7rlZeKrwCxSnEJ0q18HHg9DpV3VCDPPxrtUPVBbGhc/dyMBq5qVdXiY35VqoSlml/TVrGQ3p4QARnODCQGg==", "signatures": [{"sig": "MEUCIQCeSzrxJSqAY5vEV6OEcNSz7dQjsEdzddNM0pqqblOg8AIgP23W36jZo8NA9TCDKlElboambaT3XfxDbefCAZw1aGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "150eb8aa262c2dd1924e960373145446c069dad6", "engines": {"node": ">= 0.8.0"}, "gitHead": "f75f96908b3b4add99352a59af13560859a1b10a", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"send": "0.15.0", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.16.1", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.0.tgz_1488068897344_0.12889141752384603", "host": "packages-12-west.internal.npmjs.com"}}, "1.12.1": {"name": "serve-static", "version": "1.12.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "7443a965e3ced647aceb5639fa06bf4d1bbe0039", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.1.tgz", "integrity": "sha512-/bjOTafwjEin0RIKpFxB6n82TVPV/z0fH2InaCTkUrxmDQZuPZe/aSqHpOSfzguS1k5oHbf6gWKw0fSUOlKa0A==", "signatures": [{"sig": "MEYCIQDK2A0+rDcbnDY/dxMhzRbiG++eDF8+o3ZgqjMZc3jqpgIhAIaE0wIo/6QqLZr8lYgOWW3rrwTdNyscHRB26a6JwBHp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "7443a965e3ced647aceb5639fa06bf4d1bbe0039", "engines": {"node": ">= 0.8.0"}, "gitHead": "3e6e778fcf6c88dcf659b8f1d5f06be2eebbe2db", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"send": "0.15.1", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.17.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.0.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.1.tgz_1488686352386_0.390035341726616", "host": "packages-18-east.internal.npmjs.com"}}, "1.12.2": {"name": "serve-static", "version": "1.12.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "e546e2726081b81b4bcec8e90808ebcdd323afba", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.2.tgz", "integrity": "sha512-Fy8ZkU7VUqhQSkI8/rdVK2jg6pS91oWcjjw5T5qJhEOZA85P3oNBIIAAPyIoOVIfwkBE2jNGNZhVtslarpv+ug==", "signatures": [{"sig": "MEYCIQDvQs1naHfk7YT+fA3+AGygF2HRS1PPypEizgNKyee59wIhAPTRNLAmc98VWRpBUmN2/C7irI8ARbQhdjriHx0GcHHJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "e546e2726081b81b4bcec8e90808ebcdd323afba", "engines": {"node": ">= 0.8.0"}, "gitHead": "cb296b7ddfa869590d5ce0acb3f4a96b66f1d2b7", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Serve static files", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"send": "0.15.2", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.2.tgz_1493262384444_0.37266619759611785", "host": "packages-18-east.internal.npmjs.com"}}, "1.12.3": {"name": "serve-static", "version": "1.12.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "9f4ba19e2f3030c547f8af99107838ec38d5b1e2", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.3.tgz", "integrity": "sha512-M83Sy74z4RJdcXeZUvNjuv/AtjE/38BNYR8h8mgCC8w32QA6hcQx9Efr/YIlmqIDnxNkS1/fcxNJJtK+g9O+QQ==", "signatures": [{"sig": "MEYCIQCsmaLR/nd0jsw+HtLGzU8gEJsejtyo6erC/SKi+D53EQIhAJWhCpouhJrOsuqMQv8TcbkFck2bXoHAvS0+2ZLBCk43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "9f4ba19e2f3030c547f8af99107838ec38d5b1e2", "engines": {"node": ">= 0.8.0"}, "gitHead": "281475f89cf5b3f4801ed4e5767fce7b0976e411", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"send": "0.15.3", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.3.tgz_1494998781756_0.8577500546816736", "host": "packages-12-west.internal.npmjs.com"}}, "1.12.4": {"name": "serve-static", "version": "1.12.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "9b6aa98eeb7253c4eedc4c1f6fdbca609901a961", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.4.tgz", "integrity": "sha512-S0jS3FQXrBdeOwbysweEhiB5ogGfVZND6H2c2Vh2RbriSPe8Bogtbb4J5ZUeViXS0v0YZYe7llGYrEXjK8fnqw==", "signatures": [{"sig": "MEQCIDa9fQUUjspxHshM7kOT/bcN6GkHAY5EXkoHA1lxnCt4AiBHHazJWWr9c/HipMpciWqoNDMTkmEcqHr3uc9thdocQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "9b6aa98eeb7253c4eedc4c1f6fdbca609901a961", "engines": {"node": ">= 0.8.0"}, "gitHead": "c16b4d1c2c7bc1aaf76194187f087549b63bf2f9", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"send": "0.15.4", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.0", "eslint-plugin-import": "2.6.1", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.4.tgz_1501998894621_0.48076217574998736", "host": "s3://npm-registry-packages"}}, "1.12.5": {"name": "serve-static", "version": "1.12.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "693a54118216f0310105c7180e5fdd6a50f654a5", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.5.tgz", "integrity": "sha512-i7WU1T31eTcKqcmYs1JYr57EpP0MvwS4/QYD4wFK9JBVpN6/WD40lesYatoHGfdKox7Q35QTXtjvan9eO9+4aA==", "signatures": [{"sig": "MEQCIAGupLUD8bMNG9gM0ULO4PduEFty4wHVHF+1DxHEyYjZAiBr2sc32mR4LMWNK9QdOx2tokWcuxeSvWSN8USpdXKBLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "693a54118216f0310105c7180e5fdd6a50f654a5", "engines": {"node": ">= 0.8.0"}, "gitHead": "afd17c3a6ffe81085e606b89d103959a6dc1ef19", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"send": "0.15.5", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.5.tgz_1506036076569_0.7109229087363929", "host": "s3://npm-registry-packages"}}, "1.12.6": {"name": "serve-static", "version": "1.12.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.12.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "b973773f63449934da54e5beba5e31d9f4211577", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.6.tgz", "integrity": "sha512-M2CMkmVnR22x7taVSeYGdfIhnh/mhanPZqqBCSRAvVZNkrhaOpOLlwimKpJR+NhFpD/8Dr9G0YpAtkDkCcAVJQ==", "signatures": [{"sig": "MEQCIDRl26470kT68nKUTw0riPNrDvPsGX2w7UivE9PXLgjfAiB9kVVkzm8B9AoT9wB13U2xr55QBbYDPJOAHH70bU7QlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "b973773f63449934da54e5beba5e31d9f4211577", "engines": {"node": ">= 0.8.0"}, "gitHead": "843d1eabfdef46396c4f6d59d19a955f14574aaa", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"send": "0.15.6", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.12.6.tgz_1506126249562_0.78251140168868", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "serve-static", "version": "1.13.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "810c91db800e94ba287eae6b4e06caab9fdc16f1", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.0.tgz", "integrity": "sha512-SEsucoUvAoXLTLznbyNUCw5Vg/7qaFyENZQITDwJWvGLYuRhw+hIDVXEFGaayXZimatgQxDRgcJve9Kp6rZqqw==", "signatures": [{"sig": "MEYCIQDm4guVbbQf8Sil2+PUgGizC+8kKHMqRpOmfXADC6ZwdQIhAKE8DGJz0/9lZBh3zdON9OhwGGrnun0lpdRRvswvgFjW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["LICENSE", "HISTORY.md", "index.js"], "_shasum": "810c91db800e94ba287eae6b4e06caab9fdc16f1", "engines": {"node": ">= 0.8.0"}, "gitHead": "1c58cfdd2ab8bee9ed5d37bb5b54047f839349ed", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.11.1", "dependencies": {"send": "0.16.0", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.13.0.tgz_1506561074296_0.6352248503826559", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "serve-static", "version": "1.13.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.13.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "4c57d53404a761d8f2e7c1e8a18a47dbf278a719", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.1.tgz", "integrity": "sha512-hSMUZrsPa/I09VYFJwa627JJkNs0NrfL1Uzuup+GqHfToR2KcsXFymXSV90hoyw3M+msjFuQly+YzIH/q0MGlQ==", "signatures": [{"sig": "MEUCIHsTIv4YxnGJwg6qjTeqjs1lmyMaNWdEd7qfeB9x51V1AiEAzRHmgCJ42uAPur+ItT6nGyOmdZRT/wdzSU6g7FiC/+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "gitHead": "f6f76136aa967f917886730c57efd4c9d3bc12f7", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.11.3", "dependencies": {"send": "0.16.1", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.2.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.13.1.tgz_1506715867957_0.268530584173277", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "serve-static", "version": "1.13.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.13.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "095e8472fd5b46237db50ce486a43f4b86c6cec1", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.2.tgz", "fileCount": 5, "integrity": "sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw==", "signatures": [{"sig": "MEUCIDmGhoCwFrrNbMPh+unDDM0Wb+THUbq6nyDRYxynB83hAiEA21s/R6oAhmQh34IU/1oXCOyqMFr+CZ7vNHwDG2TwImM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24364}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "gitHead": "f287bd6c26ad2bfd0422c533b0358f2f4b16f7db", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Serve static files", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"send": "0.16.2", "parseurl": "~1.3.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.2_1518028719917_0.8918243597229449", "host": "s3://npm-registry-packages"}}, "1.14.0": {"name": "serve-static", "version": "1.14.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "fad67e9f36d8c670b93fffd0586afe634f6c88a5", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.0.tgz", "fileCount": 5, "integrity": "sha512-Kg15ayeXWLhAE5T9adD3xGcEHchIZsDUExIIfSTOzg6kgmIa86NP8NpuOAYKLbPEYU1OQ+KCQrtKh8AG3h7KAQ==", "signatures": [{"sig": "MEUCIQCkqhI3tnyMgb5mGc0M0YEdHkSBFwykgW4droL4dzMGjAIgRddiB69uc5DWg3IbLHasgiCT+OgG29IUWKqCrvEu76Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc0k4NCRA9TVsSAnZWagAA7mkQAI7fJzKb8h9eZnXTjYY8\nZTg81cXUpO2bJnIx7ZQkiyL4KQaO62FVuVF/T0l2fAuaqE9MqUWVFGuqyYN7\nu0ZO2vHCf3SZqqNxdBw+xMFD8mIHHyCsAy1se1TyI1yXpo9nbuh5a5aCL4Tm\n0Ly+NazfLOtu/YX7JUj2qwI0BscPJuWB6sUiOZC1pQshiGmOomAbrD/6s7nr\nDkFMFSs5B6/GpiYkh5gHh+JI2NXFxzxciUpHcq2Ef0G5sDbPt3I1p+tuL/EV\noI9lugHSRh6P5hyB9M60a+SeQAS4sRea77VigXNjotHUw7krTUgVA4+pJUdA\no4fEAJg0+rxUK6SEcI4vADXDqRU1CG7FQBgYle+wJdp3IwbEWd47sfZE1ZIW\nVXgK5HWU9+Di8TCTSasM8JSiwqsIJZTL3C3Q2R0Jilsw8JE8EIsgrx/gSlyM\nXnWa/JWRVpj/w64IGf+gH2nl36B9Y938bjaTKMEYxiHpWnu9Yl0yWixsD3kw\nOSyoLv1ZvGLml4u6g+EzCsRup8pZOtT2+TJFI0IHsVYbskdTAE7q5H6R0Ra0\nlwqgdqMdS83Bf80wc/B6XRH5ZJINxWglV6kUfiOMp3Y2dBt83aiePOqsaOKI\nQKHrB1EtbjNjez0YVrqGH0rVsCenXD6pu5YHkO1Hg3YsoTUEeMzyET0AQ6tJ\nHaMq\r\n=44Cy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "a8918403e423da80993ecafdec5709d75e06e6c2", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Serve static files", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"send": "0.17.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.14.0_1557286413079_0.3223911248244453", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "serve-static", "version": "1.14.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.14.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "666e636dc4f010f7ef29970a88a674320898b2f9", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz", "fileCount": 5, "integrity": "sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==", "signatures": [{"sig": "MEYCIQDeqLkJX2oLjurB6jU0L1CV8O+OrAiqjH75JKe550nVlAIhAPgjWqGiUmMCiJsqxPI/ZIoZaBQDiNBkP/d/rCNBqorx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1kQ5CRA9TVsSAnZWagAATzwP/j3OHYbfRHaSzvx+7R9w\nS65ncOxHfuv8DsFQRCJsWmkq1+px8WBIRCZiPePbZC4U/bH9ZnOSycKOWEn9\nc+YAWpOvR+JGFJjMI2KFn3kQgb//WFnD6Hg2d1wY9CeIGp5wfhyDpf7sl1oZ\n9MByAaTfHBxc46eoZ5w2drm7XlOseA5rk8r12NeN7q5JVVRJuPUS2k3Xu+sM\n4vv95+kKz9K4kNLxKfBxK28DNKk1zbtvfade6fMi24YfWVSJO+eiQZ2pCXXf\nx5I31i7gE6RMM2ijr/mwCsZn4zGMzWhnRgejTxEIQeEGm5skMP8MfeobUNon\nRb/XABMEAhWwYBssPwOccjlBPy+iK5KYxSihx28uIj++yreQIWqjdHaqeq7j\nZPdUitvLTfZ3PNCKwtjYqfbKQXZhGlMoT0fOIHYm7KXT2RRwi8XyZVR607xT\nBZVksFpf3K7uuoRWowRohNTpNRJZI90sUm08IBV3iL6XtJg4Rb/iGZCOUHnJ\nEcPKmQZxPKE/Af//RTqBQAOSfYSCoHrWzI7M07JEuGHsQSXB1eeXtZkVirqd\n9i9kSN/u1j7UMj6ml2OJTcH4mchvkPYTS+I+ailnzEPqyaXVZvYCOGTZ4OAl\ng5lKaWEDeYxdg2FwjWKRQCU39kaV7Ia47fTY0sDGfqXREPyJ3ZyhuMrBKHac\nMTiw\r\n=GE2J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "94feedb81682f4503ed9f8dc6d51a5c1b9bfa091", "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Serve static files", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"send": "0.17.1", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.14.1_1557546040326_0.1307430777112919", "host": "s3://npm-registry-packages"}}, "1.14.2": {"name": "serve-static", "version": "1.14.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.14.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "722d6294b1d62626d41b43a013ece4598d292bfa", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.2.tgz", "fileCount": 5, "integrity": "sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ==", "signatures": [{"sig": "MEUCIQDzFHtq8zkQ5kei4iDvfkmW28lzh2j5WQrYN2oUXcJaYgIgedm71RYEFOS5wvmHTMU5itSIRt9NgBsXK6W3QggfceM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhusaPCRA9TVsSAnZWagAA7L0P/2udwkon40470t4kMRKs\n+eY2sbKU++l4ogJg1wI1FuafFcz+LgXfOp3qk38dQVzHIqFKjUibAb5xcsIX\ngbYsth9vFlueuFsGgMIBqzfTEjb9U6ZSloGoLC2acoGXA0jFjQCHKo2yZG3O\nMH0nbFm9LVgm5kozBrLjXdX8xnvS5+mwqnYhmEPa0bb0K048YiLlM1fuwX8P\n5dC0ll0Kc16MlKI2XTPA39shd3l6Tz0wlKrKKcLdyjDxHCKcRtn+2FFf+hNz\ndz+lHX/fYI0Gv5CIhs8YVG6QUmd83AR4hiHZtzpHDXR48llBuCgjxRJIUqgK\njl/GK9Am/sKi/or9LXL9K7N6pE/xb941W8N+MTzlM+T5DNOfr2oU0AnnZObQ\nc1ycNd76FAhH4aV9QuSMew3Br/2TI0wOjQN+eusva4TfPWMQ+ncfwPzVKvj7\nNukNaUUsERbgSDi17L0LILsbNyGkf70SIDJ+eGCPDHe2PJtHagSsEv9e/cyP\nM01Osf/F6nj2HN8ftNYZxeEX5MZfGlvCwaaqd6lYEBpfB7vvYE6iL/QGCKvb\nc/x0G4fFCnx+gOMrrnbDOPAHMjHWW2xCR9tXA6idFA6iw3hCoQzVY3C5/YX+\nElxCRy9jQd7+U6WNvudux72fQNzCfswB1jXdZKkmcn2+iKwSoi/Uz8WngMcd\nLX2y\r\n=6WhO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "8dbf16269362b5bc20134dac9f0ca44c0abfc038", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Serve static files", "directories": {}, "_nodeVersion": "16.7.0", "dependencies": {"send": "0.17.2", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.14.2_1639630479167_0.2780535618951683", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.1": {"name": "serve-static", "version": "2.0.0-beta.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@2.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "003bc35db8b37905e1f6d380a4ccc0a34f30ec1d", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-DEJ9on/tQeFO2Omj7ovT02lCp1YgP4Kb8W2lv2o/4keTFAbgc8HtH3yPd47++2wv9lvQeqiA7FHFDe5+8c4XpA==", "signatures": [{"sig": "MEYCIQCUkxuo9cny6bmaGyNT8KSturrfqMUM3Ur+AuMeaoeG9gIhANOnsKQY2RLD9ui2vQqlzfmR99NdbPaaDTdpFZaez630", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/sbTCRA9TVsSAnZWagAAC5MP/0ibFVwiYpeOrc8HptUW\naKLLGhHnEIHWNaKb83bDHMj8h4cSlcfx52/UoQCcFP7guJnD7zy1k9rByotN\nz083bHq6ediCYVyzfuJ7ltukgu4WYsnjcbAG37rY8Bbx4Gu9KRoG+Bl9Cgrc\n3IiL9+ynVV0PeNd+TcFSIYSfrI0K4p4IFrWoWXcYwsqUv5LtVQ8RulLXMemv\nHBngq0nZnmVUKMfpf54yU4YC+8LUg+CrYxyQp5DEauloFhOXP0C3VLvKLu79\nQHqboOMCipu6efvNz5LwGqO3+rSmd5zo6s2uPtUwNhleSn68PuGBvfnLnz1V\nwsdXyeQetyatQJDZ7g/tEOpnx+wK/zmbOVQkICt+/dffI9Q14uLpS77PKB9r\nqJwUkWzspSWIijdkrzOT3ZpwcLYGtGI6g8BozRC3kZVaU/9aVyk/4My5jFIO\nsFF7dyC5yUl5KcbLwuTf+8MPRBPWhughkuivaS30hjr509pvldhkZViY2I9/\nBj8Sv/QtjdxA5et8eMFZuHFo36vWo/E7TAO20S5tODYGzBZBfrggZNymNf4F\npNvupx+EmM6n/CSkwL4cmNZfAYsLqRV0Qssk5aY7lAu8CNkVr/L9ehF4HS7o\nxnnZ6pF/u7TaZyMZTiyMSvZvUEJf4n56gsZk0jJSzT52aK12ZsHMAlHgBO0f\nYSm3\r\n=SWPh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}, "gitHead": "1362955ca123076a96a4f3693086062e845fb225", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Serve static files", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"send": "1.0.0-beta.1", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_2.0.0-beta.1_1644086995050_0.4878158036832816", "host": "s3://npm-registry-packages"}}, "1.15.0": {"name": "serve-static", "version": "1.15.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "faaef08cffe0a1a62f60cad0c4e513cff0ac9540", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "fileCount": 5, "integrity": "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==", "signatures": [{"sig": "MEUCIEZf9eBW61lcWw8CYuHpws6QKWi8wAKW2JV2majfWNhXAiEA/+h00dxADf6BCFZZjbO6jvuA8HlZyn47cap7jQ/KQYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPSZOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqilA//WFELsx4rTp3leSdsnVrr4KdsrFPc0zhfHBl2E+yOOUXO6nSW\r\nuUZyV+ZFgJnqqwQCEJrn9t59CDzk73+cgkdxF1cqcdrPXz1uAoFROHqQUoFV\r\nV2rgjOv9iHdH7Jq1E69Qwdd8uxwSuVRmcop1SQ0T5FV5Kc4PUIGvnyrwkQW7\r\ntMu9sJ9uznLlvDec54vMr2zWk2p2M0DFd3PNs6rmftO2dclBl0kqCMPfZaFY\r\nSXKBK99AQTqyuSm3JpQ1Ja7xQnoYXjXet2IwhnQFFPrRuRwAuMTTC/03C6QW\r\n5jaExj7fxEHHY4inQ2+yc9YWQs/k1xj565glazhHfmE0vNRp8xMgIoEXCRWV\r\nncKWHUzRnciSdA3D8D5EBJOxFRPDpv2ty3pwtCkUz4IypYv9pjLdrcZ8+V9d\r\ndRZZL/sX6b0t7ek3izXzb2C3cAwPjshJe2fRk58VyAKxA0DT0VSJnNZjpimb\r\n0vXPyDDkZgo00wv3fdAVkx6M+fkJDc+8uNRpJnNab4fiQ0ONF+9tECa0daF1\r\n2VfgdZgyp07w5p0RcnKnQ+Yz5VWJO/fJzr8ER9suhwDF2mNzTMkvUSdLVPjE\r\nqhaj9b1zpwMzx4KNlil/kuBU0/lH/PXfJLBIQpB7Fh/Xb1b3lDy0/JT8ZJK8\r\nOzBWDGmdEA0lav1XGQ+adL4q3wPx5Ee8qoo=\r\n=eUEv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}, "gitHead": "9b5a12a76f4d70530d2d2a8c7742e9158ed3c0a4", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Serve static files", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"send": "0.18.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.0_1648174670057_0.9291971508826276", "host": "s3://npm-registry-packages"}}, "2.0.0-beta.2": {"name": "serve-static", "version": "2.0.0-beta.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@2.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "9c0ca60e21a6332d18ade72db7cf7a96b27db474", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-Ge718g4UJjzYoXFEGLY/VLSuTHp0kQcUV65QA98J8d3XREsVIHu53GBh9NWjDy4u2xwsSwRzu9nu7Q+b4o6Xyw==", "signatures": [{"sig": "MEUCIQCL/i9i7R2h8FsiXC2nRERCGMPbySc6YFuK8c3cBB9x9wIgSZplomAhdviN275+/+1WxHnYokEHTcQoAkAhDClAZdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25477}, "engines": {"node": ">= 0.10"}, "gitHead": "2b97892a24668b6f0a8741f5b0956284cd1f1a36", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Serve static files", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"send": "^1.0.0-beta.2", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_2.0.0-beta.2_1710986052534_0.2726963665192095", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "serve-static", "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@2.0.0", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "849bd6decd1dd4ae9233e4ed95581eb1ae031549", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-HixicRldw1CCqCTgNQOYAUVNiP4B7bwcgNJT0HEY87yrBU4EVcNZ/GkQE0Ufbcuyqaw6DHeoUkhjGq4/vjb+gA==", "signatures": [{"sig": "MEYCIQDtBTebAhRCBZLHrXicyq0LRxfvvdWyclUDFesT9F/imQIhAMPcFGydxYapeM74dbSKIJGH/KPH82sbkBh5wfSfrApV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25676}, "engines": {"node": ">= 18"}, "gitHead": "707f803715ccbd2030aa87a6f756fb040a946262", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Serve static files", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"send": "^1.0.0", "parseurl": "^1.3.3", "encodeurl": "^2.0.0", "escape-html": "^1.0.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^17.0.0", "mocha": "^10.7.0", "eslint": "7.32.0", "supertest": "^6.3.4", "safe-buffer": "^5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_2.0.0_1724446789413_0.6122752111544119", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "serve-static", "version": "1.16.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.16.0", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "2bf4ed49f8af311b519c46f272bf6ac3baf38a92", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.0.tgz", "fileCount": 5, "integrity": "sha512-pDLK8zwl2eKaYrs8mrPZBJua4hMplRWJ1tIFksVC3FtBEBnl8dxgeHtsaMS8DhS9i4fLObaon6ABoc4/hQGdPA==", "signatures": [{"sig": "MEYCIQC9mKYRUvv8fVtpD//OvL22c0x3m9voXPR6Gu1lRvkX4QIhALQBgAjmGKJCiTIOC7Y7CKxEXqKhFj+5e4wxRpHaxZ01", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25297}, "engines": {"node": ">= 0.8.0"}, "gitHead": "48c73970b129b96cba448e792576ad89b1f9fbed", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Serve static files", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"send": "0.18.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.16.0_1725930867128_0.802317033590991", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "serve-static", "version": "2.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@2.1.0", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "1b4eacbe93006b79054faa4d6d0a501d7f0e84e2", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-A3We5UfEjG8Z7VkDv6uItWw6HY2bBSBJT1KtVESn6EOoOr2jAxNhxWCLY3jDE2WcuHXByWju74ck3ZgLwL8xmA==", "signatures": [{"sig": "MEUCIFY3iOM5HQWL0KyQ8aQ1nNs/9z6wWZ9X8SOzF7zMvW8lAiEAhMRwT4I3UdCOZhum3nJbknFrJUIFY7GH9r9v9j5bPvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25786}, "engines": {"node": ">= 18"}, "gitHead": "87c5f092ed7cd23202f699b08c7f407c2b536df0", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "ulisesgascon", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Serve static files", "directories": {}, "_nodeVersion": "22.5.1", "dependencies": {"send": "^1.0.0", "parseurl": "^1.3.3", "encodeurl": "^2.0.0", "escape-html": "^1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^17.0.0", "mocha": "^10.7.0", "eslint": "7.32.0", "supertest": "^6.3.4", "safe-buffer": "^5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_2.1.0_1725931047492_0.7499127906388547", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "serve-static", "version": "1.16.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.16.1", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "ef5b5e8c045446a12e0efe1cb2bc2246dbcf4b66", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.1.tgz", "fileCount": 5, "integrity": "sha512-lQEnvznm+LlIN7a5B/LdKGFt1b/a+L/DNojweniJHyoBxqQlAVnv0QC5UuRbQC4udn3n4Tj2pcvbJa3Wiqwxtw==", "signatures": [{"sig": "MEUCIQCc/ZG/RXfjbl+4NZJwQ56HFKwaFzVA5FlV60HNu0iPpAIgRDASPz01/TN795I4aW2VRGSftj1RnAn0HXO84/nSqQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25359}, "engines": {"node": ">= 0.8.0"}, "gitHead": "77a8255688cc4affc70e6dc9aa02e3ced4957e77", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Serve static files", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"send": "0.19.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.16.1_1726074861618_0.4080807585050421", "host": "s3://npm-registry-packages"}}, "1.16.2": {"name": "serve-static", "version": "1.16.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "serve-static@1.16.2", "maintainers": [{"name": "ulisesgascon", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/expressjs/serve-static#readme", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "dist": {"shasum": "b6a5343da47f6bdd2673848bf45754941e803296", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "fileCount": 5, "integrity": "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==", "signatures": [{"sig": "MEUCIQCsndmgSGAp9vXsjAYSPMOa5Dq2KV76nUiqOP2ECVTN1QIgbcHHRmG5XV21lvK1SHe194hnKkoVWfS8wj+B7U7rcuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25426}, "engines": {"node": ">= 0.8.0"}, "gitHead": "ec9c5ecfb09368519e4698ffbbe1882de00d0ef2", "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/expressjs/serve-static.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Serve static files", "directories": {}, "_nodeVersion": "22.2.0", "dependencies": {"send": "0.19.0", "parseurl": "~1.3.3", "encodeurl": "~2.0.0", "escape-html": "~1.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.16.2_1726079074665_0.9435300624442295", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "serve-static", "description": "Serve static files", "version": "2.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/expressjs/serve-static.git"}, "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "supertest": "^6.3.4"}, "engines": {"node": ">= 18"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}, "_id": "serve-static@2.2.0", "gitHead": "4eb26be6ced8705e491aedcb387fa2e9e45baf12", "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "homepage": "https://github.com/expressjs/serve-static#readme", "_nodeVersion": "23.5.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "shasum": "9c02564ee259bdd2251b82d659a2e7e1938d66f9", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz", "fileCount": 5, "unpackedSize": 25672, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCEbgwSj5X8NfSS9x9DXRK/J/SadyzJwGkvWp76oiGUNwIhAJVpObU+UH53Rpfo0FgMLL83Pmxwk1togbV9jWxgmSRp"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/serve-static_2.2.0_1743122339567_0.00003929683040637677"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-03-06T03:09:28.171Z", "modified": "2025-03-28T00:38:59.979Z", "1.0.0": "2014-03-06T03:09:32.347Z", "1.0.1": "2014-03-06T03:26:54.332Z", "1.0.2": "2014-03-06T15:03:41.948Z", "1.0.3": "2014-03-20T18:09:49.893Z", "1.0.4": "2014-04-07T17:01:58.608Z", "1.1.0": "2014-04-24T18:06:17.300Z", "1.2.0": "2014-05-29T17:11:02.362Z", "1.2.1": "2014-06-03T00:43:02.047Z", "1.2.2": "2014-06-09T20:42:02.880Z", "1.2.3": "2014-06-12T01:46:13.711Z", "1.3.0": "2014-06-29T00:35:07.395Z", "1.3.1": "2014-07-09T19:56:13.955Z", "1.3.2": "2014-07-12T02:45:11.917Z", "1.4.0": "2014-07-22T02:36:40.493Z", "1.4.1": "2014-07-26T20:51:44.572Z", "1.4.2": "2014-07-27T19:17:05.102Z", "1.4.3": "2014-07-30T01:57:08.348Z", "1.4.4": "2014-08-04T21:12:30.992Z", "1.5.0": "2014-08-06T05:23:58.966Z", "1.5.1": "2014-08-10T03:49:14.868Z", "1.5.2": "2014-08-15T01:44:34.666Z", "1.5.3": "2014-08-17T17:58:22.850Z", "1.5.4": "2014-09-05T05:06:27.532Z", "1.6.0": "2014-09-08T01:55:54.182Z", "1.6.1": "2014-09-08T02:59:37.793Z", "1.6.2": "2014-09-16T05:51:48.663Z", "1.6.3": "2014-09-24T20:03:28.791Z", "1.6.4": "2014-10-08T17:05:20.120Z", "1.7.0": "2014-10-16T04:15:43.659Z", "1.7.1": "2014-10-23T04:20:01.443Z", "1.7.2": "2015-01-03T04:35:34.564Z", "1.8.0": "2015-01-06T04:17:27.572Z", "1.8.1": "2015-01-21T05:04:38.586Z", "1.6.5": "2015-02-04T22:19:40.440Z", "1.9.0": "2015-02-17T00:46:14.214Z", "1.9.1": "2015-02-17T19:01:22.984Z", "1.9.2": "2015-03-15T02:52:05.823Z", "1.9.3": "2015-05-15T05:12:55.170Z", "1.10.0": "2015-06-18T04:52:56.197Z", "1.10.1": "2016-01-17T04:51:56.747Z", "1.10.2": "2016-01-20T05:56:47.928Z", "1.10.3": "2016-05-31T03:19:43.455Z", "1.11.0": "2016-06-08T06:17:48.440Z", "1.11.1": "2016-06-11T01:30:04.320Z", "1.11.2": "2017-01-23T16:51:02.629Z", "1.12.0": "2017-02-26T00:28:19.390Z", "1.12.1": "2017-03-05T03:59:13.116Z", "1.12.2": "2017-04-27T03:06:26.381Z", "1.12.3": "2017-05-17T05:26:23.671Z", "1.12.4": "2017-08-06T05:54:55.708Z", "1.12.5": "2017-09-21T23:21:20.128Z", "1.12.6": "2017-09-23T00:24:10.593Z", "1.13.0": "2017-09-28T01:11:15.341Z", "1.13.1": "2017-09-29T20:11:08.976Z", "1.13.2": "2018-02-07T18:38:40.596Z", "1.14.0": "2019-05-08T03:33:33.249Z", "1.14.1": "2019-05-11T03:40:40.466Z", "1.14.2": "2021-12-16T04:54:39.317Z", "2.0.0-beta.1": "2022-02-05T18:49:55.221Z", "1.15.0": "2022-03-25T02:17:50.224Z", "2.0.0-beta.2": "2024-03-21T01:54:12.735Z", "2.0.0": "2024-08-23T20:59:49.618Z", "1.16.0": "2024-09-10T01:14:27.313Z", "2.1.0": "2024-09-10T01:17:27.648Z", "1.16.1": "2024-09-11T17:14:21.805Z", "1.16.2": "2024-09-11T18:24:34.879Z", "2.2.0": "2025-03-28T00:38:59.780Z"}, "bugs": {"url": "https://github.com/expressjs/serve-static/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/expressjs/serve-static#readme", "repository": {"type": "git", "url": "git+https://github.com/expressjs/serve-static.git"}, "description": "Serve static files", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>dd", "email": "<EMAIL>"}, {"name": "ulisesgascon", "email": "<EMAIL>"}], "readme": "# serve-static\n\n[![NPM Version][npm-version-image]][npm-url]\n[![NPM Downloads][npm-downloads-image]][npm-url]\n[![CI][github-actions-ci-image]][github-actions-ci-url]\n[![Test Coverage][coveralls-image]][coveralls-url]\n\n## Install\n\nThis is a [Node.js](https://nodejs.org/en/) module available through the\n[npm registry](https://www.npmjs.com/). Installation is done using the\n[`npm install` command](https://docs.npmjs.com/getting-started/installing-npm-packages-locally):\n\n```sh\n$ npm install serve-static\n```\n\n## API\n\n```js\nvar serveStatic = require('serve-static')\n```\n\n### serveStatic(root, options)\n\nCreate a new middleware function to serve files from within a given root\ndirectory. The file to serve will be determined by combining `req.url`\nwith the provided root directory. When a file is not found, instead of\nsending a 404 response, this module will instead call `next()` to move on\nto the next middleware, allowing for stacking and fall-backs.\n\n#### Options\n\n##### acceptRanges\n\nEnable or disable accepting ranged requests, defaults to true.\nDisabling this will not send `Accept-Ranges` and ignore the contents\nof the `Range` request header.\n\n##### cacheControl\n\nEnable or disable setting `Cache-Control` response header, defaults to\ntrue. Disabling this will ignore the `immutable` and `maxAge` options.\n\n##### dotfiles\n\nSet how \"dotfiles\" are treated when encountered. A dotfile is a file\nor directory that begins with a dot (\".\"). Note this check is done on\nthe path itself without checking if the path actually exists on the\ndisk. If `root` is specified, only the dotfiles above the root are\nchecked (i.e. the root itself can be within a dotfile when set\nto \"deny\").\n\n  - `'allow'` No special treatment for dotfiles.\n  - `'deny'` Deny a request for a dotfile and 403/`next()`.\n  - `'ignore'` Pretend like the dotfile does not exist and 404/`next()`.\n\nThe default value is `'ignore'`.\n\n##### etag\n\nEnable or disable etag generation, defaults to true.\n\n##### extensions\n\nSet file extension fallbacks. When set, if a file is not found, the given\nextensions will be added to the file name and search for. The first that\nexists will be served. Example: `['html', 'htm']`.\n\nThe default value is `false`.\n\n##### fallthrough\n\nSet the middleware to have client errors fall-through as just unhandled\nrequests, otherwise forward a client error. The difference is that client\nerrors like a bad request or a request to a non-existent file will cause\nthis middleware to simply `next()` to your next middleware when this value\nis `true`. When this value is `false`, these errors (even 404s), will invoke\n`next(err)`.\n\nTypically `true` is desired such that multiple physical directories can be\nmapped to the same web address or for routes to fill in non-existent files.\n\nThe value `false` can be used if this middleware is mounted at a path that\nis designed to be strictly a single file system directory, which allows for\nshort-circuiting 404s for less overhead. This middleware will also reply to\nall methods.\n\nThe default value is `true`.\n\n##### immutable\n\nEnable or disable the `immutable` directive in the `Cache-Control` response\nheader, defaults to `false`. If set to `true`, the `maxAge` option should\nalso be specified to enable caching. The `immutable` directive will prevent\nsupported clients from making conditional requests during the life of the\n`maxAge` option to check if the file has changed.\n\n##### index\n\nBy default this module will send \"index.html\" files in response to a request\non a directory. To disable this set `false` or to supply a new index pass a\nstring or an array in preferred order.\n\n##### lastModified\n\nEnable or disable `Last-Modified` header, defaults to true. Uses the file\nsystem's last modified value.\n\n##### maxAge\n\nProvide a max-age in milliseconds for http caching, defaults to 0. This\ncan also be a string accepted by the [ms](https://www.npmjs.org/package/ms#readme)\nmodule.\n\n##### redirect\n\nRedirect to trailing \"/\" when the pathname is a dir. Defaults to `true`.\n\n##### setHeaders\n\nFunction to set custom headers on response. Alterations to the headers need to\noccur synchronously. The function is called as `fn(res, path, stat)`, where\nthe arguments are:\n\n  - `res` the response object\n  - `path` the file path that is being sent\n  - `stat` the stat object of the file that is being sent\n\n## Examples\n\n### Serve files with vanilla node.js http server\n\n```js\nvar finalhandler = require('finalhandler')\nvar http = require('http')\nvar serveStatic = require('serve-static')\n\n// Serve up public/ftp folder\nvar serve = serveStatic('public/ftp', { index: ['index.html', 'index.htm'] })\n\n// Create server\nvar server = http.createServer(function onRequest (req, res) {\n  serve(req, res, finalhandler(req, res))\n})\n\n// Listen\nserver.listen(3000)\n```\n\n### Serve all files as downloads\n\n```js\nvar contentDisposition = require('content-disposition')\nvar finalhandler = require('finalhandler')\nvar http = require('http')\nvar serveStatic = require('serve-static')\n\n// Serve up public/ftp folder\nvar serve = serveStatic('public/ftp', {\n  index: false,\n  setHeaders: setHeaders\n})\n\n// Set header to force download\nfunction setHeaders (res, path) {\n  res.setHeader('Content-Disposition', contentDisposition(path))\n}\n\n// Create server\nvar server = http.createServer(function onRequest (req, res) {\n  serve(req, res, finalhandler(req, res))\n})\n\n// Listen\nserver.listen(3000)\n```\n\n### Serving using express\n\n#### Simple\n\nThis is a simple example of using Express.\n\n```js\nvar express = require('express')\nvar serveStatic = require('serve-static')\n\nvar app = express()\n\napp.use(serveStatic('public/ftp', { index: ['default.html', 'default.htm'] }))\napp.listen(3000)\n```\n\n#### Multiple roots\n\nThis example shows a simple way to search through multiple directories.\nFiles are searched for in `public-optimized/` first, then `public/` second\nas a fallback.\n\n```js\nvar express = require('express')\nvar path = require('path')\nvar serveStatic = require('serve-static')\n\nvar app = express()\n\napp.use(serveStatic(path.join(__dirname, 'public-optimized')))\napp.use(serveStatic(path.join(__dirname, 'public')))\napp.listen(3000)\n```\n\n#### Different settings for paths\n\nThis example shows how to set a different max age depending on the served\nfile. In this example, HTML files are not cached, while everything else\nis for 1 day.\n\n```js\nvar express = require('express')\nvar path = require('path')\nvar serveStatic = require('serve-static')\n\nvar app = express()\n\napp.use(serveStatic(path.join(__dirname, 'public'), {\n  maxAge: '1d',\n  setHeaders: setCustomCacheControl\n}))\n\napp.listen(3000)\n\nfunction setCustomCacheControl (res, file) {\n  if (path.extname(file) === '.html') {\n    // Custom Cache-Control for HTML files\n    res.setHeader('Cache-Control', 'public, max-age=0')\n  }\n}\n```\n\n## License\n\n[MIT](LICENSE)\n\n[coveralls-image]: https://badgen.net/coveralls/c/github/expressjs/serve-static/master\n[coveralls-url]: https://coveralls.io/r/expressjs/serve-static?branch=master\n[github-actions-ci-image]: https://badgen.net/github/checks/expressjs/serve-static/master?label=linux\n[github-actions-ci-url]: https://github.com/expressjs/serve-static/actions/workflows/ci.yml\n[node-image]: https://badgen.net/npm/node/serve-static\n[node-url]: https://nodejs.org/en/download/\n[npm-downloads-image]: https://badgen.net/npm/dm/serve-static\n[npm-url]: https://npmjs.org/package/serve-static\n[npm-version-image]: https://badgen.net/npm/v/serve-static\n", "readmeFilename": "README.md", "users": {"atd": true, "jk6": true, "nex": true, "rsp": true, "viz": true, "j3kz": true, "tute": true, "ubbn": true, "h4des": true, "kthjm": true, "panlw": true, "71emj1": true, "evan2x": true, "isa424": true, "jaqbec": true, "monjer": true, "quafoo": true, "shenyu": true, "wxhthx": true, "3ddario": true, "ahmehri": true, "astesio": true, "chaoliu": true, "drewigg": true, "endsoul": true, "itonyyo": true, "kparkov": true, "mygoare": true, "mykhael": true, "nadimix": true, "sculove": true, "shivayl": true, "vboctor": true, "asfrom30": true, "damianof": true, "dgarlitt": true, "dzhou777": true, "fenivana": true, "illuspas": true, "jon_shen": true, "jtuesday": true, "kistoryg": true, "kujisoft": true, "mayq0422": true, "robermac": true, "softwind": true, "wkaifang": true, "zuojiang": true, "antixrist": true, "eduarte78": true, "flockonus": true, "heartnett": true, "jetthiago": true, "largepuma": true, "ldq-first": true, "levisl176": true, "milfromoz": true, "mojaray2k": true, "rubiadias": true, "xiechao06": true, "axelrindle": true, "ghkddbguse": true, "giussa_dan": true, "juangotama": true, "kankungyip": true, "luffy84217": true, "lwgojustgo": true, "ridermansb": true, "rocket0191": true, "sammok2003": true, "simplyianm": true, "sonhuytran": true, "temoto-kun": true, "blakecscott": true, "jacktan1991": true, "joshukraine": true, "phoenix-xsy": true, "rainstormza": true, "scotchulous": true, "sethbergman": true, "wangnan0610": true, "brentonhouse": true, "kerimdzhanov": true, "mengkzhaoyun": true, "mobeicaoyuan": true, "moosecouture": true, "mrhuangyuhui": true, "nickeltobias": true, "oliverkascha": true, "codeamancoder": true, "jasonwang1888": true, "mdedirudianto": true, "scottfreecode": true, "danielbankhead": true, "spencermathews": true, "usingthesystem": true, "programmer.severson": true, "afewinterestingthings": true}}