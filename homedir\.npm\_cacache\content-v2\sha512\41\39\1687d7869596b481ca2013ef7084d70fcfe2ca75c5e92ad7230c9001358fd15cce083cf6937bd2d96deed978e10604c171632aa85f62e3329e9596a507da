{"_id": "getpass", "_rev": "41-d5b1cbb72d7d6e06185b5bcbb2ffed7a", "name": "getpass", "dist-tags": {"latest": "0.1.7"}, "versions": {"0.1.0": {"name": "getpass", "version": "0.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MPL-2.0", "_id": "getpass@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/getpass#readme", "bugs": {"url": "https://github.com/arekinath/getpass/issues"}, "dist": {"shasum": "2896de9fc14d5b2cc6833c6d3a601326a37eed9b", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.0.tgz", "integrity": "sha512-jAd/+NE/zUMDjEYh+k2r5I3zMc8s8VIzPAEsMTqMiEaD3L4TQrMGNWuQ7LbxnsrQMawehXLX5xKKZs4HrlM5xw==", "signatures": [{"sig": "MEYCIQCgprTg91TBpOitZE1BrHpdwhnTMDzWrZR0IS8tjaOXhgIhAMWHPBHFvFyjr14c+h3ILJs61EQg3NrhrhdFmavkwMkN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "2896de9fc14d5b2cc6833c6d3a601326a37eed9b", "gitHead": "1f13a142025f8f29eab9e06ee9cf7e2b1bf03490", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/getpass.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.13", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.0.tgz_1461273239531_0.9900160033721477", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.1": {"name": "getpass", "version": "0.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MPL-2.0", "_id": "getpass@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/getpass#readme", "bugs": {"url": "https://github.com/arekinath/getpass/issues"}, "dist": {"shasum": "fe4c1462c7eb72a0074d882769bc915f2051bc47", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.1.tgz", "integrity": "sha512-gY9OjCLSjaLu0Yq++uCvtTlyUoaVLlyz7eTpmJTxTRMDaVDupyIqhb1gduaNwEsJv56OtZkm0Ecp+f05jU4T1w==", "signatures": [{"sig": "MEUCIQCO1v2fj9HJmVoP16pvpPbrZBYre+EdIy0WDC5xuMEGiAIgFrRF7icEfo4hI1BwDEfG2xN/KxVebgoxmxjvLCykm7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "fe4c1462c7eb72a0074d882769bc915f2051bc47", "gitHead": "76a1d0da61f85e011455488dd6fbde6172169c1f", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/getpass.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.13", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.1.tgz_1461275183317_0.10568123101256788", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.2": {"name": "getpass", "version": "0.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MPL-2.0", "_id": "getpass@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/getpass#readme", "bugs": {"url": "https://github.com/arekinath/getpass/issues"}, "dist": {"shasum": "dbcc91a1143267bbcd18d061bf889a51182778aa", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.2.tgz", "integrity": "sha512-HkPiKsTNNsboUQNP92qtrYBWBPaaTOxeIkChqNcxjEGY3kD3Htonm6eGWIXzCWWJJPS2IjSTq/VgwB1g8ow81g==", "signatures": [{"sig": "MEYCIQCUUoi4/PCcdFIrPbq5tX3GQfmJDsJExYCKLbpSQzt3HQIhAK3zaqzBaCqQsUn6s/Z94Lbo+YJ2YXPlVKv90ddkTG8+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "dbcc91a1143267bbcd18d061bf889a51182778aa", "gitHead": "cf64430426b2328dfd20af98218073cab4b40600", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/getpass.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.13", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.2.tgz_1461275602229_0.8338033361360431", "host": "packages-12-west.internal.npmjs.com"}}, "0.1.3": {"name": "getpass", "version": "0.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MPL-2.0", "_id": "getpass@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-getpass#readme", "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "dist": {"shasum": "49c085a288f8b1be109d4f4a5ebac839c579fafb", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.3.tgz", "integrity": "sha512-MyTteTHYXLsoHCSo8x8xav73MN/Xs+qCF52J+DSkpA8nFyFrUsl/wrFA3d5/Jsa/3iVDG/467UcBoIoNT7gs+g==", "signatures": [{"sig": "MEUCIF+f085UAKZzb6Ivq5OWXTZ4hUVKLzBaRVYGjyicod+lAiEAwUwIsCL82fN2HfqnbjnsWTbNFkrKD8tpbwT3MS/QPSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "49c085a288f8b1be109d4f4a5ebac839c579fafb", "gitHead": "47051f596ed200e6c097cbc861c4a8be91cc1e9c", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-getpass.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.13", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.3.tgz_1461276132175_0.29017636459320784", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.4": {"name": "getpass", "version": "0.1.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MPL-2.0", "_id": "getpass@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-getpass#readme", "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "dist": {"shasum": "c627e40b359a1db8d0a4ff0a7c218870e011a04d", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.4.tgz", "integrity": "sha512-EFHNzMP/K6eV8HZSMzUGwOAd8l/jUC+5SG/2UTmOfAWldSDSWrdyccddlnv7hydNFLg2NgL1yePcckUDtYspWw==", "signatures": [{"sig": "MEUCIQDXLyPp/XmsrI+6RICMu5l1WryLze8ZWJFAw8P64hyp+gIgRTSWidfmeTleWx4/n809Y+3b8oCiGP6g6Sa/GpkXn0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "c627e40b359a1db8d0a4ff0a7c218870e011a04d", "gitHead": "53eca57276d4f849c83a218acc7c53d89db61a63", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-getpass.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.10.43", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.4.tgz_1461278150352_0.07285057893022895", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.5": {"name": "getpass", "version": "0.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MPL-2.0", "_id": "getpass@0.1.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-getpass#readme", "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "dist": {"shasum": "a20d5f2a8fc83f11db0d591d0487469407e6be14", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.5.tgz", "integrity": "sha512-LyhF74N4wi6zY+/VPSGAXWSOJ8E8P7oXQhNoLMPTbhhP+iR4erBhiar0cdVcL/PyBTOuly+h6lerlFsCPaDYwA==", "signatures": [{"sig": "MEUCIF6jqu1c1kO1rfJzrb22rjLo2NGNrdhackl86xlAffZkAiEAswMoI6/7W/tytNZndt6nsFx3OpPvqGuAw6F9yNcIg2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "a20d5f2a8fc83f11db0d591d0487469407e6be14", "gitHead": "11419e2e861be30c818f422e7c3cb256cc222003", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-getpass.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.13", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.5.tgz_1461279405263_0.8423897379543632", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.6": {"name": "getpass", "version": "0.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "getpass@0.1.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-getpass#readme", "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "dist": {"shasum": "283ffd9fc1256840875311c1b60e8c40187110e6", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.6.tgz", "integrity": "sha512-Uj295v1VGRPhKEty7IiEzGYf2rAIEbcGQ8dxK5QrQuwP7tCW8ftD5o8FUsGW4MLdws4P3eKRBzo+mFySYYcimA==", "signatures": [{"sig": "MEYCIQCtfNYB8RxlW3FzpfzmoitRrM43R7XAybuRPh4cdIlWpgIhALMrT1Fmv+4mLgX8DX+65/u4n7QbjnyVKzr50fPXeCIQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "283ffd9fc1256840875311c1b60e8c40187110e6", "gitHead": "e7fdf43ad60aa520f894d41856852aa320f36646", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-getpass.git", "type": "git"}, "_npmVersion": "2.14.9", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.9", "dependencies": {"assert-plus": "^1.0.0"}, "devDependencies": {"json": "^9.0.3", "tape": "^4.4.0", "pty.js": "^0.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.6.tgz_1461907090215_0.6450737570412457", "host": "packages-16-east.internal.npmjs.com"}}, "0.1.7": {"name": "getpass", "version": "0.1.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "getpass@0.1.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/arekinath/node-getpass#readme", "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "dist": {"shasum": "5eff8e3e684d569ae4cb2b1282604e8ba62149fa", "tarball": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "signatures": [{"sig": "MEYCIQDnhcTUutrTk3UyzzW3QuLFaKQBPE5MVEd6/puNqrHx6wIhAOtCAmw/aE5ENXCoCYN+jM1Mj/apoxT2SuzpQSHDkUnr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "5eff8e3e684d569ae4cb2b1282604e8ba62149fa", "gitHead": "e219fae3a4458a1aa4b84002539265a6a1475267", "scripts": {"test": "tape test/*.test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/arekinath/node-getpass.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "getpass for node.js", "directories": {}, "_nodeVersion": "0.12.18", "dependencies": {"assert-plus": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/getpass-0.1.7.tgz_1493163657029_0.5366648870985955", "host": "packages-18-east.internal.npmjs.com"}}}, "time": {"created": "2016-04-21T21:14:01.250Z", "modified": "2025-02-07T15:28:03.000Z", "0.1.0": "2016-04-21T21:14:01.250Z", "0.1.1": "2016-04-21T21:46:24.961Z", "0.1.2": "2016-04-21T21:53:24.572Z", "0.1.3": "2016-04-21T22:02:13.717Z", "0.1.4": "2016-04-21T22:35:52.015Z", "0.1.5": "2016-04-21T22:56:46.999Z", "0.1.6": "2016-04-29T05:18:12.439Z", "0.1.7": "2017-04-25T23:40:58.770Z"}, "bugs": {"url": "https://github.com/arekinath/node-getpass/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/arekinath/node-getpass#readme", "repository": {"url": "git+https://github.com/arekinath/node-getpass.git", "type": "git"}, "description": "getpass for node.js", "maintainers": [{"email": "<EMAIL>", "name": "todd.whiteman"}, {"email": "<EMAIL>", "name": "kusor"}, {"email": "<EMAIL>", "name": "micha<PERSON>.hicks"}, {"email": "<EMAIL>", "name": "bah<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kebes<PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}], "readme": "## getpass\n\nGet a password from the terminal. Sounds simple? Sounds like the `readline`\nmodule should be able to do it? NOPE.\n\n## Install and use it\n\n```bash\nnpm install --save getpass\n```\n\n```javascript\nconst mod_getpass = require('getpass');\n```\n\n## API\n\n### `mod_getpass.getPass([options, ]callback)`\n\nGets a password from the terminal. If available, this uses `/dev/tty` to avoid\ninterfering with any data being piped in or out of stdio.\n\nThis function prints a prompt (by default `Password:`) and then accepts input\nwithout echoing.\n\nParameters:\n\n * `options`, an Object, with properties:\n   * `prompt`, an optional String\n * `callback`, a `Func(error, password)`, with arguments:\n   * `error`, either `null` (no error) or an `Error` instance\n   * `password`, a String\n", "readmeFilename": "README.md", "users": {"mojaray2k": true}}