{"name": "jsonwebtoken", "dist-tags": {"latest": "9.0.2"}, "versions": {"0.1.0": {"name": "jsonwebtoken", "version": "0.1.0", "dependencies": {"jws": "~0.2.2", "moment": "~2.0.0"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "505628492092fe35d08b600fa6768cd06711aaa2", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-0.1.0.tgz", "integrity": "sha512-F+cS4nX8se0apNdOo4zOjrwPj/nQy2EgG/CPVJB/z0juS+apEuHoow1ZuG0gZN4zFjkUcYuOAcU5FbY5KyjgmA==", "signatures": [{"sig": "MEQCIAPe5oXMliCSPhwqyLu2SUNnN4OiVRk9dlfd0GfJE9e3AiBIZ5BXQlR5jN/U9JQcl9dp2IDXjQpNvDbgb1Tew1FV+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "0.2.0": {"name": "jsonwebtoken", "version": "0.2.0", "dependencies": {"jws": "~0.2.2", "moment": "~2.0.0"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "75ec9d9b17cb958f232cd24bdfa14afa98e28be5", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-0.2.0.tgz", "integrity": "sha512-brUbXYgVRQKAZ11sll3J2IrrP+Q9sfofw5ugDsF42eXH5Yq3k8sEntPE3+FpOc3m/uDLhQuzYWlJHHfv4dWlpA==", "signatures": [{"sig": "MEUCIAuxXzJV5HLHTodXVtuwmLzfErgXvrPDRN+F/lIWc8ygAiEA7FXJLNZXs+LEhuePwJCRU8ntd+vlB9ZPrQMDsUYpvkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "0.3.0": {"name": "jsonwebtoken", "version": "0.3.0", "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "82b4a85b568d4e83c7e1d6735b0be753997c79fb", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-0.3.0.tgz", "integrity": "sha512-UnVpO+ZtDTpo499mkmSokNkJ2SXr+c9kwCifgbY3Zv9yUU7fcGiJ02jYXq3GsRFgPIMGeI4H+AT3bDbnb4/PEQ==", "signatures": [{"sig": "MEQCIC5gagOTV1/TIpP7Dgh08Wu95uax4st8Uhxfr+76v1qtAiAKjTGXDkYVMu1i0Wz4LNJSQcu+tVr70gr37CR7EGPC3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "0.4.0": {"name": "jsonwebtoken", "version": "0.4.0", "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "7dfa44ac8a588e16e0453c81f11ab6addd0742fe", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-0.4.0.tgz", "integrity": "sha512-VQlZ5j/m478UhkmopShghzepWmar8tKEXYWuDX0V64rd8a45ANQop6RqHWAkHypQ7DYh4nuqgS1tD+9uSnvqvw==", "signatures": [{"sig": "MEUCIF9rd7LuTBS0KvepNQ0KLDGNcRORArP4rZP38Zsf6myFAiEA2MupNeCPIylIh/QtVzkypTQrY0qei34RSgqEBfhQ5cA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "0.4.1": {"name": "jsonwebtoken", "version": "0.4.1", "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "741c7a5ca829c4e9e22e942168996c7f9dee11a0", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-0.4.1.tgz", "integrity": "sha512-2/BzdZ79eMmvbBYvM1Jq+JacJw8/irfIn1Uz7hziXUEzjanPfcz4P1hqcAdne2LVz7iMG369OOBQj9uf7NwZHQ==", "signatures": [{"sig": "MEUCIDJtZ2LjUYOF2sm98Y7LLreO7vF5rqMiTaAqp+BQbX8JAiEAzCVca/R3JdAD1BM3LldlJSK7H9DcXzO2ITABzd0ooFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.0.0": {"name": "jsonwebtoken", "version": "1.0.0", "dependencies": {"jws": "~0.2.2"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "a4bd1e7bde39b101189aceb1d6b24a489138e7c3", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.0.0.tgz", "integrity": "sha512-gFWNia4//dNLXMAuWkc+GcUa5XiY3EIKKyHLN0mXogRAWpXvmoaVgFLFnFMQTjD6u+HSJWTeiYiSc4rGIRk9kA==", "signatures": [{"sig": "MEQCIFImPM8B6iRvpHDTSYXLlZh0hKHRY7tQCUK/a7BTL/M7AiBwDZ8fPSnRcteTYCP5CJGfajQ342d8XbZTbYSujg3jaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.0.2": {"name": "jsonwebtoken", "version": "1.0.2", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "069985dfa4b80f0646b6ad94536ec634f92ba7fb", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.0.2.tgz", "integrity": "sha512-eeRGU8unyWEKrInYC+5lja0cRetiqkZvE5QKChsqQj4IQcu4IhprcYX+XMIeG6HH4H4yA/SIHExQu7+JtKWRhA==", "signatures": [{"sig": "MEQCICKZw07ejH2LBOyyfUKt4mw/5PFwjl3hgIFrGH/kpFNSAiARBahiSCLnNjoU130MeXJrU8xWZtle9OUE0XExBmkw6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.1.0": {"name": "jsonwebtoken", "version": "1.1.0", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "d8c466e40f8a26781f523c8eb497d110684bebf1", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.1.0.tgz", "integrity": "sha512-dDfQjYhXREEGtVfDmRKGh4wEFPtuDC1tVTzz8QT6tEPLEdOQ7awyUhKONL6KelyrZHbEiQXr4yx/5cIQxiTzEA==", "signatures": [{"sig": "MEYCIQCtoc+NYkT4JBUo6lFeWZKexfH/U+gL/uoF02epmP51DwIhAIvFD6w07o31Kp/ypg8zxrSDm1DRmRRtUFEPDvpVObdb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.1.1": {"name": "jsonwebtoken", "version": "1.1.1", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "ff4fefe359a2da7dfa445a3a4dfbada44a0e1bce", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.1.1.tgz", "integrity": "sha512-p4O5dIOfLk1poXmdLurI3UUEZRS1YphAl3zs0ZTkQBYZmd4QcwSrOGFpW1kTwTe+77uKM2M0G5Bob+pMeUKu5w==", "signatures": [{"sig": "MEUCIQDZioBCkxhCMVtoQKHUDvV9MG2qOucxHTsC6nt6xFEt2gIgAbOI7kApPMkZOuDDWMORk3qb9Hcz7AtBp3pZ25U3hBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.1.2": {"name": "jsonwebtoken", "version": "1.1.2", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "b231c30b96f01429289eed6f93bf5ce5ae4943c5", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.1.2.tgz", "integrity": "sha512-duzI2jqfR2M2fLRQEb4wiN+LZsBTii93k4+OhFeKqkvkYYPnOyInLojQDVRauetzO4EWzR7j5djvcQzn3Zmenw==", "signatures": [{"sig": "MEUCIFLAn0c+rkkjoY9RCwGRN0NDYmek4kwWYU0NIvf70zb8AiEA4Apkw7y3M9Jbp1q0Jc8ikC+S7rUpqFbO7rNr4b+g3kU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.2.0": {"name": "jsonwebtoken", "version": "1.2.0", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "bfef60d674b036eac59c6178cf778aa7e6ff2212", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.2.0.tgz", "integrity": "sha512-1yaHA62SA/nh4JIZnIwVZfrY3HkBhKv/gxiHAypnB3SI5LvEkTozG0uUSb2u4qdbij7bM2h57mGUGqscXDSf3A==", "signatures": [{"sig": "MEQCIBBHJU0lgbB3Qn5jKYRK2kfkuLfJ8qWAJ/f/43WIo2C8AiBeRKN0w6j0tp/NAOWHqaTlkkdzAynNGzgqlBRz3UEIvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "1.3.0": {"name": "jsonwebtoken", "version": "1.3.0", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "683ceee1bbe09a92d9e026ab25e67f97bcf3c3e2", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-1.3.0.tgz", "integrity": "sha512-W8+0E18LspD4DhMLZVvWc4odyH0zjs5c5kVNfCQDaJrDwlmjEwvc8F/QZD7po83i9Ij1Yvfx7AaCl6rRlMOjtg==", "signatures": [{"sig": "MEYCIQDHuvvdxj6nKB+rQwkx9dd/rxvjcFJFFpeiwWcrsH4cKwIhAP0/XAnNBynMON1QmVmXWbJuXfPqcx0tTeJRl7zdYw+N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "2.0.0": {"name": "jsonwebtoken", "version": "2.0.0", "dependencies": {"jws": "~0.2.6"}, "devDependencies": {"chai": "*", "mocha": "*"}, "dist": {"shasum": "cb1c47a76bba1ffb26311eb6d6338b85a5f045e7", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-2.0.0.tgz", "integrity": "sha512-ioQl//Zum8W2opJL+b/i20MKFWkKl6EcIbDmZYNhjwX4NIpUxmVDCzdiB4sH6t0+Ew4jst81tTqkTXYxzLFYGg==", "signatures": [{"sig": "MEYCIQCqM/Y+zKad/KtZIFUg2dF/1nws1kE7s+8C49zTYFzGWgIhAKhpEoKrnI7gN+mZAwvWZV7jvLZ4toeqGB/lkCM37eau", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "3.0.0": {"name": "jsonwebtoken", "version": "3.0.0", "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "cd8ec263132fc88cb3b25efd303359cebe7b2139", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-3.0.0.tgz", "integrity": "sha512-WN3FIuYXt3ZIgDYjXgMB0ydCfLkNL5SAYmRMaCsqRep9qw93WL8qrT0P1w8M4xm7EkpDigelheY5/chkTT+bVw==", "signatures": [{"sig": "MEQCIHHJLqUVEW7yl7NGt/CR5v24EyOun1XoDKFt8ungyaWsAiBPuKOiX1Xvsqnw+5SzkZ0OzbyJqqL9ygutCbCJp4rrgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "~1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "3.1.0": {"name": "jsonwebtoken", "version": "3.1.0", "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "aee94947138b5d02364c7228775ea2fdb0b5b1e2", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-3.1.0.tgz", "integrity": "sha512-Dq7qi2bp+HqKpNqi05BglBrv71BEUfv1DJazh3HKhF6Gf3J6jpCuZtXpbrDt4Z3/GoY9A8Bpnr6kX4mf17Jokg==", "signatures": [{"sig": "MEQCIG5ZqrXwRtaDAsi40+DIutSySfA4xzI3ZkXf05lHR/SuAiBbjWdv3WfSXCzpyx0GsevReJ1ffZW3KWPlRP9raJ/9JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "~1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "3.1.1": {"name": "jsonwebtoken", "version": "3.1.1", "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "736b77b420d5b73bfac6ec37dfea9b236f2212d9", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-3.1.1.tgz", "integrity": "sha512-OehG8frN7JuPXlC/MjptfaWZ+LaG+krShAtEByiOdZEvhDnPnca6IVPrHQgBiHOAD6Ix1TBUpTolRzk+AQo9Uw==", "signatures": [{"sig": "MEQCIGkzVk7RWWQqIVfJr6F/ROmSpxz2khBZ7GJLj0OCvidYAiA+7MU0OnP/7Sc8uTI5cPVC4OSNJYgMvSm84ja2qLxC0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "~1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "3.2.0": {"name": "jsonwebtoken", "version": "3.2.0", "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "6b9108f6889d05e90ae743ad6873f1affc198ae1", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-3.2.0.tgz", "integrity": "sha512-8CsytR3+jYIgA3Gc/mZ94r0yj1iGbKgh9SXxdcPXZarVIZPspToSKep+z39ULui4N0wVd6OyqBds7SoAwoRvGA==", "signatures": [{"sig": "MEYCIQC9zWsB9I66+BA3paeNoZlTqoiydQdnfNZ1h5cg7O0d4QIhAK99xglswsE7wcVOqkIwRW76q1LVcpgpVqIzd/whyWaN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": "~1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "3.2.1": {"name": "jsonwebtoken", "version": "3.2.1", "dependencies": {"jws": "~1.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "1eab8a142f74aacd5f71baf72e6ef3b986d787e2", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-3.2.1.tgz", "integrity": "sha512-9/xKeQsv2CXXpf/CSBnTmyy+UkFDtAzrCTMr/586KKLjQXgnAoHGeUsOxzO8OonPS1vd/gKdk8L/34lPNsinEQ==", "signatures": [{"sig": "MEUCIEQpJnOhHFTj6MfPgLc8dMGQOU72nhgzX/MfUMQ+VLrOAiEA8kf6mFvBLiRAOJeuLM7OHJjumgkdgRjEwsUkkmDR3ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "3.2.2": {"name": "jsonwebtoken", "version": "3.2.2", "dependencies": {"jws": "~1.0.1"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "b7911b0b48f7851f41024971b70cd0f8b5fe415a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-3.2.2.tgz", "integrity": "sha512-ImBSkVMjUCN+f6oSDPWDBZ5gWAupEPzR0Da4frqFaq1IoCqn8IRozlJR47G0GSu6rNTkbvbiq+TmublUKJmZ5w==", "signatures": [{"sig": "MEQCIE++m2EnVm1noaMU0rwE4F/KDUTcxJbbb079i5k7JKl0AiAd2IUDJ2Cmuviqfcss1SaCuUTLgILZFvAM0spnLTcMvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "4.0.0": {"name": "jsonwebtoken", "version": "4.0.0", "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "5a1182a650f635dd45b18901c05c29c79ea0d2f1", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-4.0.0.tgz", "integrity": "sha512-VtlRNQrlstBBI/10ypvg0l58rFZSbxG26QbXWzMI/PlublvS79adciBJzVvur5TfLCEJY450HuZlVL2wm/pRKQ==", "signatures": [{"sig": "MEYCIQDG2XzcJlgJPyDaN3Qq6BCBxp/tgBvyXfL9fteVFPOvxwIhAK6STdj39bqTMdbBP5HC61CoNcJtrHs3xgIw7f+BSEjx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "4.1.0": {"name": "jsonwebtoken", "version": "4.1.0", "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "b5263435ad19425376584d2823fc8225ddbb68a2", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-4.1.0.tgz", "integrity": "sha512-3zrshyVxl3vOW+c5TVH6znA2eLSCH9Zt2M+2jt29EH1YNLVsjmyd5WaBxQfMu6NG1+r6s/inHfm2z4CMEtyS1g==", "signatures": [{"sig": "MEUCIB/EK8HlN5ecIqVnoU7JZJ7fL0QgO3UwXnktV79LHvtCAiEAxHcOGZaHYMFsiExy7xAjUzp9HWRZDaf9HjAZP3TDBp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "4.2.0": {"name": "jsonwebtoken", "version": "4.2.0", "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "1a5ca5f037f0d57ad00aba5b2ee29d2b4f4b8314", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-4.2.0.tgz", "integrity": "sha512-WGBtsi031X/WO/z/yVfzL4F/ucKRgHtaHAMZRkXTUKHSOHmxLHi0+BOVAgG1nO2pGEFTAUS2DF115zURZna1/A==", "signatures": [{"sig": "MEUCIEK1Al5IIG4afqqfOq5rf8SS8Pc97R1yTQHRuPBCxWOyAiEAq6UWPwDZUykkGWeJ/lOv2TxHxfFUtygvwOuhXj4k4Yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "4.2.1": {"name": "jsonwebtoken", "version": "4.2.1", "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "979b5e2c0a282b81d73a91ed62d9a47970657300", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-4.2.1.tgz", "integrity": "sha512-eq3H2MeU5l5LMBaFnRPh5ng6iTJTEFWMl1jBac0Y/TyicilReu3AxC5vJ8gkl+Qu11S2+TM7058gvpW1NRy6bw==", "signatures": [{"sig": "MEUCIB5j0pRWXsW39FAKi+ajSRBgSWOYJ1mSotFeesj6r0i+AiEAhat/iw015h5Db+VKUmchg8G+4mkJhfuKsWrxulRc27U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "4.2.2": {"name": "jsonwebtoken", "version": "4.2.2", "dependencies": {"jws": "~2.0.0"}, "devDependencies": {"atob": "~1.1.2", "chai": "~1.10.0", "mocha": "~2.1.0"}, "dist": {"shasum": "1f6ee4b14c545d06ec999a37471b5e67cbe401a0", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-4.2.2.tgz", "integrity": "sha512-2N9yTaQM42kA280DVPqy0RP4KKScJQaTrP2N0TBgemdTbz6at3kq29ZCnM9ifaeyf1lQYatABtDUjn18JWWfYQ==", "signatures": [{"sig": "MEUCIQDhucYRjyGfOtDZJU/0gVaMQZFBh9mNJmRbUmirAMOkbQIgSlTRgSZ3Om4FpL21Qcs2FhgaQpTXC1AN3vT0gJiOQr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}, "deprecated": "Critical vulnerability fix in v5.0.0. See https://auth0.com/blog/2015/03/31/critical-vulnerabilities-in-json-web-token-libraries/"}, "5.0.0": {"name": "jsonwebtoken", "version": "5.0.0", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "8244ac5921491a933cfe996f3f142bea1e38adbc", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.0.0.tgz", "integrity": "sha512-ADWSQxWTyk5cjcZgZ1G7mB6jzJxhUFXclsILeiC2jmCIBFDsaTFfL4Wg+VTnZLwEZ4lFINjTEx//fsYRE4A/dw==", "signatures": [{"sig": "MEUCIBUl5cnq0zJ4NNZUJ/VD9GbCJ6H43xI4HiAwRLb4lwe2AiEArX+81slNWLKSHNrd2w5WUNM7c/jOsgsKHq5yjv800vQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.0.1": {"name": "jsonwebtoken", "version": "5.0.1", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "5d85ce580421915be22e4aa7b13d4bb99b280a2a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.0.1.tgz", "integrity": "sha512-Gs/51TvsJmLE4mzgGH0srlIa+/OHSHrwaNgEWHDQ6qKiST4LuPNX9tiwhD/PNowa82bQ1Hemkr+y/evxvM43yA==", "signatures": [{"sig": "MEUCIAXgDxS5GVhKhjuPJTPAMQLtyKz4qThY0pTN3UImVbMDAiEAskPMiKOypow7Sd3WLrQZ6rZLQCo5qMXOaZ/if8z0CKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.0.2": {"name": "jsonwebtoken", "version": "5.0.2", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "c5d9c6130a1270271306c4bab65934ef8ae4f993", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.0.2.tgz", "integrity": "sha512-33th7LcnotKi8MjQzqL6aYTbVR8VCCTVVCAa+7tWBhd5fapfeGQN0MxwwS9PnkiYVCmDujC4UWHmqZWqi9HTWQ==", "signatures": [{"sig": "MEQCICGv4mD5EClT7LfnolkimLjiJkdstQffZN6Alv2zz3lPAiA28rYzkxcn6nMWSjRsoi7Z180zCgbB2GzTybN8DT1xWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.0.3": {"name": "jsonwebtoken", "version": "5.0.3", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "6a55cd162a34fa7e2a94c1b189981e7fb1e218a2", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.0.3.tgz", "integrity": "sha512-9tIeHG+QvRYnxZUPcWr7ZeZqQ3UXgIm0E5qCUc4TA7s9ZlfYk6qQszyTDlieL4+cSFkQItltng1sh+mZSfncBw==", "signatures": [{"sig": "MEQCIAjGHKLeSP0CHMeJKbzs0zpS+VTdJWHRue0r05dmO48nAiB+qvaohidufPaMuxy+IHTHZz98z5s7O3D+ykQHfWvpfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.0.4": {"name": "jsonwebtoken", "version": "5.0.4", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "4099dd73842da454386bf6a4fc18bc638b15cd03", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.0.4.tgz", "integrity": "sha512-ZvdpJ3jJgovIb2478/4TnfMUVthE8gwooJ5gVPAG7w6BDBwxQcT+1uqOkp35X4Wz9VAqOBEZMH0MvoInCoOHJw==", "signatures": [{"sig": "MEUCIBrDNvl4zknFQ0BBRcnnfeNK/HVWEPbbI3pheHZqyTgoAiEAugXEoDhPnPxrvOxWJQ/AH8OcjVB5p0jKEOzsS62+Tws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.0.5": {"name": "jsonwebtoken", "version": "5.0.5", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "6592cc05ee03dd5ad9e03a910911a4da79afe0f8", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.0.5.tgz", "integrity": "sha512-OWh0JVrWWml3X3ykf5FC0xO4VA+NEGHOY1SuFnu0vESdwZrInLPb00AXvoWNno2Vf1gQiTClWH3Xh/e3ZWOK6Q==", "signatures": [{"sig": "MEUCIEBYeYXpdgqRONJILZvp5OjK4gdEY23EMVyp/RL4/wl2AiEAzNPJjVxzI0vOipWBf6lfaoxzU9z5W1Q0xwvqbErVxoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.1.0": {"name": "jsonwebtoken", "version": "5.1.0", "dependencies": {"jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0"}, "dist": {"shasum": "a69f20bc2f27091e6c86a341b0d61d4ce390572a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.1.0.tgz", "integrity": "sha512-aY0swmcpvL289H5Nk5036ZRU+dtUxJXQwJPBlMrA20Ue8jLN4vQBzn6dst9TviknMwoIfkPiXdg4SjVcr4T4wA==", "signatures": [{"sig": "MEUCIQDN8NkpJpOPcETbuzvf71iECCWKLMKISNnWONciErM8GAIgZg7jPJ6mhPSLnVMJwlHiZcmRAJtwHvvVl9tsn4m42vM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.2.0": {"name": "jsonwebtoken", "version": "5.2.0", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "9e9c12c692c35c8355b512d8118dd18440aa99f8", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.2.0.tgz", "integrity": "sha512-IDsE12OmHD0qf5JWAqitGZzk9h5lspl4xA5CBkVAWdiPKKeUWA9Al3Ok2mK6StQZUXamf3NU8LdRsGf/meJnnw==", "signatures": [{"sig": "MEUCIQDaZMAa0k1xl/g1Ea1PmPB+tWcseCnJfLnllHzrP2OirAIgQNjlg3TTHZKYrGuP+RB+8QwVNoYHwwJBPJEm/bSS/F8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.3.1": {"name": "jsonwebtoken", "version": "5.3.1", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "65418ef1bae5eb1f3410f2c133396e8d70c57046", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.3.1.tgz", "integrity": "sha512-kHf2uq4qN0VpPZ81PxoAaKGj/bg86oMtX9snu0dpM7wR6tTJoXUEjw0BUOQ1tv4e5exCNXevpOyYebsTB7uVUQ==", "signatures": [{"sig": "MEQCIA2yyMir5JXgGoDdtn3+OcYlhh7EUqQUICi4qOIlhINDAiB6kWMxLTYcdyiLEmtBc6szmR5vj0U2XKN1iaAA/OnFsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.4.0": {"name": "jsonwebtoken", "version": "5.4.0", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "01afcfcf012a9a3eae1cbfd9e5218726407492db", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.4.0.tgz", "integrity": "sha512-qPn1VCHdQzBWiEqHKcsXIsp0g2ePyD+0SbG+fS71mr4AKwsHdCEHFnR/f/aIS4f69+HukIdozDQiF9N9B6w/IQ==", "signatures": [{"sig": "MEQCIH3ARRZvY3yGJIbtREhZ0INDnVYyxurmC/vsbtpJIqLSAiBcqW/aTPIlJnFnjWKDLpMCikiAtNBYT0uAAPIBAcuqDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.4.1": {"name": "jsonwebtoken", "version": "5.4.1", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "2055c639195ffe56314fa6a51df02468186a9695", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.4.1.tgz", "integrity": "sha512-g0guae6YzYAM5k1cqVmBpUHNIsH54zZgLV8iaxD/7KWnFtThx7bFMIGW5bPMKD2znF5o6tYUmtEZRdpd2w1FYQ==", "signatures": [{"sig": "MEUCIG0W9VUNAFoZox/bx9sF1E0EriDHZqpnJLmguCHkCMp+AiEA4zT5vfa88Oo+NzX2NNVXNF9oaojODoaJDdyXSAnAvIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.5.0": {"name": "jsonwebtoken", "version": "5.5.0", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "8cc6881f0aa3a6e16e29f52051c562fcfec0ce15", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.5.0.tgz", "integrity": "sha512-8DIRsKmQeocNqniVoUhsaJUAcKlCe11SK9BZk9X+NN22pJ7CxUpDuDh54hPUJpz06b6JFQeaODTY5xzhMrQIVg==", "signatures": [{"sig": "MEUCIQDSASJmjTXIfd6ilYh9DR5V+k4dhBdM8egpNfyvIxQpnwIgIF0abvpuKJJWhdf/iEklPDGRjBgPGifAuJAIaBBwYAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.5.1": {"name": "jsonwebtoken", "version": "5.5.1", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "3597b829d51345747798b4ac221d1601a6e0b99f", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.5.1.tgz", "integrity": "sha512-haFXUIpcaYB/mxmjctYC77wn//8D8fPzLJcoMeDBNhckVjbVgwu3CteQGA3vAHOHaOjYbluyNgosp/2+ToDteg==", "signatures": [{"sig": "MEYCIQDBBecPW0BrnTMbfRXBC6clqnEAYwr0RTW7s9WZDrMhFQIhAO7dcSDWefIRd+MHVEsjuZfYZSfG7rSIWDJ5BP6dQksK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.5.2": {"name": "jsonwebtoken", "version": "5.5.2", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "8eed469d091986654b6718d8aef4e52f169deb9a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.5.2.tgz", "integrity": "sha512-QWC7TNEwDHdBXmNU3dkEs2nEJzHWLkfJ9y14x1+4pDQqol46NDnEl6yesk5f8MTim8Kg7jFYEZlAz38bGtedHw==", "signatures": [{"sig": "MEYCIQCmIaSwgzn8b/6KmN6uXgPhnWcj7EhuvXsXDzlYYK7p4QIhAMri2c3GJaXWP1Re2BLvkw7YRD3BjohUS49+QfC6DlQh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.5.3": {"name": "jsonwebtoken", "version": "5.5.3", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "961fe2cdf0ce876f2f1e9eae0c76badac758a528", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.5.3.tgz", "integrity": "sha512-U88q5LIRdy5KiOOiw/mQNupwjO+nzn1gEaiBwTtmEvEXs32LIL193Q5cE4yUp39uyf5TqTW00IMLvSGsraaFww==", "signatures": [{"sig": "MEUCIQD283QYs9olmGsFLzo2h7xbPXOOQKjlr4lR7z6DWGWrsQIgIqStRe/VpXWvACsLghqChzT0PuWhy6upwW2ZbU/9qAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.5.4": {"name": "jsonwebtoken", "version": "5.5.4", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "29b0940522e0226cfca1d834a65b7d87b1cacaee", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.5.4.tgz", "integrity": "sha512-3I2I/sJiyfQBb12+2jnSM1V1B0Qr8AbedvSJ6UN8YUuW1qzPGgpgrQuqE9P+EZaEwqmSllNGjF/6da+l5vKLqQ==", "signatures": [{"sig": "MEUCIB/Dtu3K8XPFc8g2r9/Is+760SyKT8LsXZ3BHfx41bPNAiEAznbYpwjK+PJqebvnXWPOp5y6NR+kjgRSKLVch6WJ3M0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.6.0": {"name": "jsonwebtoken", "version": "5.6.0", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "5219c14c887597a5d188c6b1895994b8d5c2ecd4", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.6.0.tgz", "integrity": "sha512-LdwfytFqeyAY92uhCYbKz9pMOY521LAoS/nYRpdTvmerclqxNzd///0WStQHsmr/6jG7Ct9xNUgMirwDliJzLg==", "signatures": [{"sig": "MEYCIQCWxK6KWS0ppQ0zybB8Qy2zxR0Me5wPVbYQnelBEKbJpgIhAMUmOsRu45qCZuS6gV8+JcnpKToVoqOWXWbZ3iNYH9mX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.6.2": {"name": "jsonwebtoken", "version": "5.6.2", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "a9e829f7a4f0df1b8ba799fa7bef265c7b1cdd2a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.6.2.tgz", "integrity": "sha512-l7WKn8619h8+97BWZoLQzX1ThuTW1JqTFmua/Op3sSXM3kX9hOpwoFWITTyLZv596c/Eu9/lKUxiH2nPW/J6tQ==", "signatures": [{"sig": "MEYCIQCbVuBXAMCi4qmLiWWlM5neuSRrg96bUI0chtvtNLvK6AIhANkK78FnpgRQwq3YJw/bnx+XuRhRhq5si7ZhARb4KPKK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "5.7.0": {"name": "jsonwebtoken", "version": "5.7.0", "dependencies": {"ms": "^0.7.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4"}, "dist": {"shasum": "1c90f9a86ce5b748f5f979c12b70402b4afcddb4", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-5.7.0.tgz", "integrity": "sha512-VP2HXX7tJ73v5ZGGTFdgILhOBwGSDlPP4qArdrq6YbDVJ9YJflcNE/fbuGHPlefPkbM1+7rFzz1eTq1xICsJxA==", "signatures": [{"sig": "MEQCIGBm/6iwW4m3U4zexEhn5bTHwP9QQT1i2a0oV1TVCON+AiAy0+C4H+3jumLpStQ4LHQfXeh/34LpigmBcVKskJe7mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "6.0.0": {"name": "jsonwebtoken", "version": "6.0.0", "dependencies": {"ms": "^0.7.1", "joi": "~8.0.5", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "1488451cd01ab37315ca405fc3647786eee98ae5", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-6.0.0.tgz", "integrity": "sha512-d<PERSON>tzkqeitr90dkTyECPm/3fLkm8DM/HWeCmUXsw+uJGcJdMHLFVozDC3DfCUclwLyCIWdGGII/TZXdN0ThdSSA==", "signatures": [{"sig": "MEUCIESjNBs5euYdefMJSvjquFd5Wcotq2ijOdBPN9gfxNdJAiEA6sLP+XZAmcDRGq0He5NLwMZlUXNipDG4zhIlC5wdfO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "6.0.1": {"name": "jsonwebtoken", "version": "6.0.1", "dependencies": {"ms": "^0.7.1", "joi": "~8.0.5", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "68e33eb31d83dd47a8dce707c4ffee10db72e8df", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-6.0.1.tgz", "integrity": "sha512-4BL7vKrLM7vw27i8RzwQZfllz52SlWTje4sSTr41ZciN5t6J0W4b7nzra+8jc4Wv0uu0r6NOwtKZeM8kPbIncw==", "signatures": [{"sig": "MEUCIH4iW4LHLvs0PmkwrZ/Ni0kpjXJ0uBjkIOwS50YpAr9PAiEAnwgH1YKRPLkiOfG6OCctBBgfdYKzpjo+Fa8uOPO32F4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "6.1.0": {"name": "jsonwebtoken", "version": "6.1.0", "dependencies": {"ms": "^0.7.1", "joi": "~8.0.5", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "cb9a8977d8f50208b8820faf974bc188d762e4af", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-6.1.0.tgz", "integrity": "sha512-Geo5yXRx+hAmVSIrUrUHgwKGi3YhB9nUpweW/+dkTKCAUKsLHKAL0u2OtgLf3kSEgiGn3/VoHa7+76RsCF7zHQ==", "signatures": [{"sig": "MEYCIQDFsjA1M0Ts3VLWqygsJ29s7bZQgx675hTrrpUpzHy0OgIhAMUi4Vm/j8zsAAB3WlsG3CqF1FJeRU3UJ31bntv1H4kJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "6.1.1": {"name": "jsonwebtoken", "version": "6.1.1", "dependencies": {"ms": "^0.7.1", "joi": "~8.0.5", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "c1f302b48a60eddafc288e8f77c717d70e9ae001", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-6.1.1.tgz", "integrity": "sha512-KgfZhiKE1WgYi12CrqwFhxTWuqF1Fdx1RzsEs4qMRAdjeUEjGYRhookhc4qNrguUkRgcwvgnqbTdORFtdYg79A==", "signatures": [{"sig": "MEUCIEFZvl2TeOaJoMafnrINkvWjDLRcE/B3u0/5Ms9PkL3cAiEAyrMPGHKElvH8vgh0RUVSL0jd00iHMB5XhvP4rtOk1uE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28"}}, "6.1.2": {"name": "jsonwebtoken", "version": "6.1.2", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "6105fa82f16c7a4f5483bc52751f19a67fc6ab0a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-6.1.2.tgz", "integrity": "sha512-axtJKAw55gN7wKST/PEjRihZ+Ye32ZlrhqMF9B1PnXuumoKTRmgSHzkoeMiLG2YrU/435GuyBuv2KvPY4smYzw==", "signatures": [{"sig": "MEUCIH4/C79y4xIOxcyB7s8g8e1YMoofapgCaNmnNxjAQebLAiEA24hXjKJj6Z2aC9IfmV1d9/PoME86920U9wc1Clw47AA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "6.2.0": {"name": "jsonwebtoken", "version": "6.2.0", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "8233154c6cd9fd8760ad0f5a563f539dd391f2f9", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-6.2.0.tgz", "integrity": "sha512-jkALyXNdG4ikKxqookaYRpU0ZCuh5Zc/rwqjuEYbt6PQEO8wnikbdZ0Js4z5QD0VudVigy7jgINsOSLJhLCccQ==", "signatures": [{"sig": "MEYCIQDJdUFPKF0+b4RVRYxgWRK5iIR35x4xCW8C0z2Iwx+dbQIhAK9UlvJnTYosSVfafDUmWBG/tqZn+qDgYahMwWzb1pa/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.0.0": {"name": "jsonwebtoken", "version": "7.0.0", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "0e1eb109cffe631db7dc0ec8c3face3b57f8f5c3", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.0.0.tgz", "integrity": "sha512-kg01nNV5qt6ye4slh4gOePBf3D+gkS5HzCRSln9dx8GjFjV5ZPkwHF2u7Op/+aS1TbYZVDh2FQh9WgDtBQ/jJQ==", "signatures": [{"sig": "MEUCIQDXrb0FQxwhiic+NhgpCQd8P8tMDAgToCN8ZnAiOZOtGAIgFOx9yq2R0m8WNV8sKOH/w5L7Fh7UlffyqZ1AG+H1QHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.0.1": {"name": "jsonwebtoken", "version": "7.0.1", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "4aba9fea3552c8f1d415d4117ab80aa09d6af55e", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.0.1.tgz", "integrity": "sha512-3uL6MByh68nVPeDNYZVLNLW79Q2I8aLDXwxg55l4t2Gq1/IXVvGLvEjBa8hong/4Rk9OOELHwiL4DDt0E1qdXQ==", "signatures": [{"sig": "MEYCIQCxxtnIkDvR76of/+RycbNa5ZgIuPIOrzCqt8thsJX3GgIhAP0nXV6YIOEAb0GmrfK5HDglsXFS6nGrw2LcGTPhhFQs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.0": {"name": "jsonwebtoken", "version": "7.1.0", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "ab91ddd7d5a4ee11f1b104ea9ae72e35566f6b13", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.0.tgz", "integrity": "sha512-VR2gaIpgPldoqMi0U/JPzlARBKkDapq617XC06TTmx0KSw4N2oVVj8qOVe10t+zM//ItaR3hhf6UKnODEdiY9A==", "signatures": [{"sig": "MEUCIQCHwO3I/sgxMlaqSxG310RCUhQNekfiPmwLT49WJeJjNQIgVLWy9a+OljFGd1YSxt2G9LIuzQgpftkzhQVSEgwE12o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.1": {"name": "jsonwebtoken", "version": "7.1.1", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "3b6f848ca50f965148a88b8be628d978fa22170e", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.1.tgz", "integrity": "sha512-dJLrT1EyL+BXzwkeIlbB3Y7jNunoRf0tUuchErdrxHzFmihyL+4CfKltQ1Q9diwaDH7mClWtU1fGIgoMGG1WUA==", "signatures": [{"sig": "MEQCIGTM6TiX0k2QvZ7/jQsWWIWtbsGPpp2/asQUs61+bAVhAiBZzBxsztgWKv1H+2VRtkYsFKKpQ2YQVaFcVmiLk8bizA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.3": {"name": "jsonwebtoken", "version": "7.1.3", "dependencies": {"ms": "^0.7.1", "joi": "~6.10.1", "jws": "^3.0.0", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "086bcc40a2ffeba272be76bad30d1a4199123806", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.3.tgz", "integrity": "sha512-lVPF3LT7V/pcR5+/zF0g2SSEJtTDpkvRfymM9jW/GwbvuOinUZ42sElhxA6HPe7SCvfVNpqbhFxh9YzBEomduw==", "signatures": [{"sig": "MEQCIAL3eue39pKMbnBEqogwLs6nWD7iVxBst90fv+lTWTxoAiAsxyEeUr41U9RCLc0tkTpCzQ3AW/SJhzopuNEllxbRvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.5": {"name": "jsonwebtoken", "version": "7.1.5", "dependencies": {"cb": "^0.1.0", "ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.3", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "c83d6ac5b846df1cd906968b8ca3816eaa133d4d", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.5.tgz", "integrity": "sha512-VTRzLqZ3C7xW9aycJSGp4gMV+FAFSFH9CB4TtG0PefFbZscLNPlCtC3Z11DEOMyFGhjHjVy0u6pomVaS4/0ErQ==", "signatures": [{"sig": "MEYCIQDRxdbmu4PATY5n+/6h8u+WU7Px8SkW2Au+MGu2x5FvGQIhAOlANqjTQtwE3RWErsJ7eaRVNHjCEJ4RSsszWWYOTlGo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.6": {"name": "jsonwebtoken", "version": "7.1.6", "dependencies": {"cb": "^0.1.0", "ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.3", "xtend": "^4.0.1"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "2ea9557af144311148f53093cfeec69e1e048abc", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.6.tgz", "integrity": "sha512-Kzld07BEHFcqcwfovTEimuTTaXYQvGalWX7Xd6t5kgssTnAgFTowHVz7vnlvg2FURPEMMLZoWZUlrCaMTWYPZw==", "signatures": [{"sig": "MEYCIQDNvutxOMglhJARBvC/6NHq/RYrnoU40PHQ8zfm3KxVxwIhAIIuDJUBuAMHxgoXoVgx4wRmNRTXolXBSFeZbKj7YFPk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.7": {"name": "jsonwebtoken", "version": "7.1.7", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.3", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "3693ba899be4d15aa06ee0608b912e1100984bf3", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.7.tgz", "integrity": "sha512-2sEIX3NX8TxH67C1IM5uQ5QaWRUyKnxeXl/UEuPkpOY6Gg/4OX0KGK8BPk0lsISKB9qo3nGUipraHby3tiiafQ==", "signatures": [{"sig": "MEYCIQCVarf+vFW4M68X1w+QmbOvOlU1J+2h7ldqDUEEHhzWwQIhAI5FJfLELlsr6pgcMYYdoKue7Vd80xOzrk9Z1igG7Vz9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.8": {"name": "jsonwebtoken", "version": "7.1.8", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.3", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "45bcd43cfeb6aebc95979e5c9ae440f5eeff2025", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.8.tgz", "integrity": "sha512-aHzDXsffPSLzdZkWw/xoYxsiR8pWai3FbdHgFdGRnbflQSd/HbwANA2tACo3tAVtADkq6l55TQVfa9DVQzVZmw==", "signatures": [{"sig": "MEQCIEZ6Q+gAiFa9XysqsDbo8yLnhX7m7aA+40ZYuuTYHC7cAiB0HLVFhKp37dlmg0gXi+AwMdsUzzIYjynFHpTzrEUcoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.9": {"name": "jsonwebtoken", "version": "7.1.9", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.3", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "847804e5258bec5a9499a8dc4a5e7a3bae08d58a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.9.tgz", "integrity": "sha512-e/KWUs23F/CBl0aFrHoqjgNKP8uxcBTFynL5i69U7LNmVHX6R5myva27wc+ZFgFiAme4ma5Pb9y195MlFCCCIA==", "signatures": [{"sig": "MEUCIQC54CM6/4oQkExHDMQCfPKaswbMaqeD19TCoxfYcaB6nQIgWR7S3GXU/gnTVF7zTdFVH4+1Nz9p6w2xps6eraUMtKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.1.10": {"name": "jsonwebtoken", "version": "7.1.10", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "a4601a3dddc28c95ea36515c8082e2bd50d4cc54", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.1.10.tgz", "integrity": "sha512-JaVcxH+TeHQim+KJXDl5Y8d31Yur/rPHOwUzVA9xPwnh9dXmdZZNWrtBosb6F0DHlyCst7K/nfFu0rD3WwNkCw==", "signatures": [{"sig": "MEYCIQC4av3pDKWRtpbGN7yRkvAklWT/lHUAMMd6KWnlszs5sgIhAKvahvt8ivQdNHCThsLfEBQp+9Ptl201ZFXaqtMvZ54H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.2.0": {"name": "jsonwebtoken", "version": "7.2.0", "dependencies": {"ms": "^0.7.1", "joi": "^10.0.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "d21106c7fedda0325007c3f2d4b6918e7249cc42", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.2.0.tgz", "integrity": "sha512-J5nwnvwT0A8xiw18W95RVhH74LfRSazeDJfMNq9s4V/RRnauhcJH96qI6e39P6J1+XHlV2q62FPKtJsLBseg0g==", "signatures": [{"sig": "MEYCIQDYFy9cTaOLwvvlUX5dRCg61IrK50M/nQHhP5waM2nMGwIhAOxegRvcbkBEVuf51C6GkF8byXcLPAGd7yKvUQNk/95r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.2.1": {"name": "jsonwebtoken", "version": "7.2.1", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "0fc7217473fc02b4c9aa1e188aa70b51bba4fccb", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.2.1.tgz", "integrity": "sha512-5scdxBq0PhdqYuSnNEohIddrJurcb5IAGP9Pf7ixzHVsarCtJZ296VySRBc37hp9JFyhJLKb7L2BU9u0xTmycQ==", "signatures": [{"sig": "MEYCIQC26EVZ4tn3jemLFoyBF5WWmpsZfkpreVuRr3S8noiPPAIhAM2aTjSBD4KzMzM5U/ISO30A1fPHFjB0QLSXHb7vcYzb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.3.0": {"name": "jsonwebtoken", "version": "7.3.0", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "85118d6a70e3fccdf14389f4e7a1c3f9c8a9fbba", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.3.0.tgz", "integrity": "sha512-JPSYVU9RM5nObFojvXtjMsSSDhliKQhk61KPwsAyUpD+4plk5CJ2XEYdBy6zIuNYwKcV3CTbCE7Azd0KcymIlw==", "signatures": [{"sig": "MEUCIGKabMR4SUmKRIrex0YpkB29Tpoy9jStp863MfLid5N8AiEA3YUieeWLdljOvsk5psoYdHJzNsBUJVGWmRU4dauens4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.4.0": {"name": "jsonwebtoken", "version": "7.4.0", "dependencies": {"ms": "^0.7.1", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "515bf2bba070ec615bad97fd2e945027eb476946", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.4.0.tgz", "integrity": "sha512-Ufhsu2nyYZ6X+sRZh/UNjeJZIoERj1exXW47cc1FZN4SiauOhFJe4VIZ/4U5f3uIU3yMB9e7lmzy2DfPizBNag==", "signatures": [{"sig": "MEUCICd267dJGvmWtTw+dndVEDZInQtxBKlL6w2Knqeun71cAiEAlrZHrT0MqBLlom0nI25QYS1j9HmEpnJJY5ajnLWJAdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.4.1": {"name": "jsonwebtoken", "version": "7.4.1", "dependencies": {"ms": "^2.0.0", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "7ca324f5215f8be039cd35a6c45bb8cb74a448fb", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.4.1.tgz", "integrity": "sha512-UEiXVGCpewmfbzTow/Aixc7G38JWQxbFb/Gu9359JkVWDdb60TFS4cbZ5bHYAaxWa9AiFOrgqRIAiyZ4I3xtFg==", "signatures": [{"sig": "MEUCIBjz5ybONvCxuSoFcnSPGzvP63nnCdUkUiCzweNvvs4iAiEAibaF93OHkK3FG3/Vy3mkrkdcK/dmHeE8kpBBB/6/uDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.4.2": {"name": "jsonwebtoken", "version": "7.4.2", "dependencies": {"ms": "^2.0.0", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "571b903c07e875c0fc59203d1ac78667d80e09cd", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.4.2.tgz", "integrity": "sha512-h0AEMJwI0IfTzg7DxZVIUJwhjBlOrkJBWcLS4A1PKvG1IcguxN5u8DmvctTQ4MjeBWdJzf+ceVkoPTOu70HvCw==", "signatures": [{"sig": "MEUCIQDCqitZpiK4BC624arh5JGJYKJBqXPZoZfBN7LMzE4jwQIgN5QkZTMDmz++GB+X/ybZNEZ9WvwH+Kda3xLoq4y3poc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "7.4.3": {"name": "jsonwebtoken", "version": "7.4.3", "dependencies": {"ms": "^2.0.0", "joi": "^6.10.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "77f5021de058b605a1783fa1283e99812e645638", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-7.4.3.tgz", "integrity": "sha512-7WWGEZ+/xedHUHLDvSwvN7LbmLIEgOuBNQOBKvfX5zpLok5q6873aCR2zOuJ/DrORp/DlyYImz06nlNoRCWugw==", "signatures": [{"sig": "MEUCIFOtn8nue6bJRaWKtzeLhMGO0XnzTjuwiyR5bbO8Y+F2AiEAlCAupIbjuJD0/m9PDYxJ4M1rEHTyNfwWNZaxVfj+htI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.0.0": {"name": "jsonwebtoken", "version": "8.0.0", "dependencies": {"ms": "^2.0.0", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.isarray": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "7b241aa67c61b3d0b3e641254a9a6f5b66f550e8", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.0.0.tgz", "integrity": "sha512-vaUn9R7rEWjjlg8d7lVS7yV4oJk5ygNKSgPwbk9fkZ2pHQtx4n5SQG47ZhDk2iMN7hq99EBqRpcogFKJMqPdgw==", "signatures": [{"sig": "MEUCIQDB3Kq5GHvZbOgXLsHfEQHYXYvgTWiKCT1JzJqJssfiywIgKRYBWKcNrUN9T0KV2bCYLN/v3Ie7XDepkRuUc/itgXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.0.1": {"name": "jsonwebtoken", "version": "8.0.1", "dependencies": {"ms": "^2.0.0", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "50daef8d0a8c7de2cd06bc1013b75b04ccf3f0cf", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.0.1.tgz", "integrity": "sha512-taW7<PERSON><PERSON>okle/4dCae6Pdt8Px0KL/JZ6r0E0JYiyQ64eii5O8McCc2R4DDiscdw6Pj3dcoMi4s6YVa1l7M0QlVdg==", "signatures": [{"sig": "MEUCIQCvTk8jn3Q+gl+TJGIp6JtQAuxGqREuapi4W0A+nGQ91AIgN8VTVWJw3attFZVczRQS8vhE5J1PaiyEyORp1bKS7T4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.1.0": {"name": "jsonwebtoken", "version": "8.1.0", "dependencies": {"ms": "^2.0.0", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "c6397cd2e5fd583d65c007a83dc7bb78e6982b83", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.1.0.tgz", "integrity": "sha512-KGlASk0MhznKeyI16nTMla+f+Rn/w89tHPnpbRZuQfnTp221suG7FRAR/yY+FeadVSTn72WZsbqF4Yq/B583QA==", "signatures": [{"sig": "MEYCIQCDnr6zOncAN2/BxxvnlBAzqXG3fvbJoI1SvxNhxkMGtQIhAN0LW13cMbYlcXQxkeZtsNWwLTDZ9v90dds7f+DsroHw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.1.1": {"name": "jsonwebtoken", "version": "8.1.1", "dependencies": {"ms": "^2.1.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "b04d8bb2ad847bc93238c3c92170ffdbdd1cb2ea", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.1.1.tgz", "integrity": "sha512-+ijVOtfLMlCII8LJkvabaKX3+8tGrGjiCTfzoed2D1b/ebKTO1hIYBQUJHbd9dJ9Fa4kH+dhYEd1qDwyzDLUUw==", "signatures": [{"sig": "MEUCIQCYl+Llb0/Hsh3Yx0x5QDt2e9bHdk+yOc/mgxP0AC5B8gIgOKLQK+q4y6LKMDh5omKu1IDCq9ZmySP08J0WpSD9G9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.2.0": {"name": "jsonwebtoken", "version": "8.2.0", "dependencies": {"ms": "^2.1.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "690ec3a9e7e95e2884347ce3e9eb9d389aa598b3", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.2.0.tgz", "fileCount": 12, "integrity": "sha512-1Wxh8ADP3cNyPl8tZ95WtraHXCAyXupgc0AhMHjU9er98BV+UcKsO7OJUjfhIu0Uba9A40n1oSx8dbJYrm+EoQ==", "signatures": [{"sig": "MEUCIQDQY6uyZOTLrWYv0BM3g+VAwaQhSz/MtuvqRPPVmD/OowIgFTMIQ32Smx8Q6oSO8zo8pMwBgjD+ALNhjNc/YJp9ycU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57516}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.2.1": {"name": "jsonwebtoken", "version": "8.2.1", "dependencies": {"ms": "^2.1.1", "jws": "^3.1.4", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "333ee39aa8f238f32fa41693e7a2fb7e42f82b31", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.2.1.tgz", "fileCount": 12, "integrity": "sha512-l8rUBr0fqYYwPc8/ZGrue7GiW7vWdZtZqelxo4Sd5lMvuEeCK8/wS54sEo6tJhdZ6hqfutsj6COgC0d1XdbHGw==", "signatures": [{"sig": "MEUCIGJbc9O9haj+QVLfQPt/5EzQbmd/bNO+yC1+Oa8tSz9AAiEAiAK+O+6TTDouKRe1X7hSbh6w+GQXlQeCFZGRBhl2wpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58040}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.2.2": {"name": "jsonwebtoken", "version": "8.2.2", "dependencies": {"ms": "^2.1.1", "jws": "^3.1.5", "xtend": "^4.0.1", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "76d7993fda79660d71bd0f933109e1f133734b20", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.2.2.tgz", "fileCount": 12, "integrity": "sha512-rFFq7ow/JpPzwgaz4IyRL9cp7f4ptjW92eZgsQyqkysLBmDjSSBhnKfQESoq0GU+qJXK/CQ0o4shgwbUPiFCdw==", "signatures": [{"sig": "MEUCIQDnEABGSnM4yFzcAviG7PxfzDvryrK/Me85QIuUzKXHzQIgY5/bhr+UGuHnJnZRC0ZjmP+xDpVW8RHKY3PLD4cWnqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDsCECRA9TVsSAnZWagAAFPkP/29LRhxHubgvOyFFuP0l\nGNMW/45Mx5d9BHY7Q0M9WMY54etrXSvRjSzev71TtPwK7oBzdmwlAmne6GRm\nXjLK94fRmESSK5uz7RIbpFzhOL+6AMHRuxqnDaZ16bIcyxdS1rzhKcH4ifbI\nyWVnvgvSzjMbH6YpYS8PQNp0CzAXoENnnc29V0IhyQ/URM2mQVzlXWQbkJ3P\nRW4DPPzttCaX4s9SHNDuuemlmx4eki5Q50nC6R5CHJXCBQfzw2uIbAO5ZzNY\nZWgoodjfFDKxOVBUVYVtU2phmDUjJjNKU8hkG/5DFrplXe96T9QoqJ7qkSgD\ne6gtrUTNUJK/SHOHj9/oST9G195vB5Vc0RVza7AiXZL5uDnYa8F+fYD7T3nl\nibZmyjeTLZgacyA3sHDHvsVgcxl/CSGXVfJVnj0x3yIgoTe+N7O8V7ID5GXJ\nZgByqlmwdKlNzKfS1RwX6IUzCV9IfEf9v/j4+fIyMeNa5ji/LFQr6MyzOZuW\n8uKhaAX4iZTgpFqnw6RS2/q5f0RhIIjyyb2CJQVuEMMEByuHZpDBxLo5my96\nSSx3OjSegLNcgILvT39215CJb84SZrrJq1g8PeiKcgbk2gWbkk/4SnQNsyN+\noxPmFMcf9jK8ubbpjcECKi8z/u+WnLoe0XrXFhNuAlSU36l5gzDMUWH3u3Wk\n3wBp\r\n=35yC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.3.0": {"name": "jsonwebtoken", "version": "8.3.0", "dependencies": {"ms": "^2.1.1", "jws": "^3.1.5", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "atob": "^1.1.2", "chai": "^1.10.0", "mocha": "^2.1.0", "sinon": "^1.15.4", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "056c90eee9a65ed6e6c72ddb0a1d325109aaf643", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.3.0.tgz", "fileCount": 12, "integrity": "sha512-oge/hvlmeJCH+iIz1DwcO7vKPkNGJHhgkspk8OH3VKlw+mbi42WtD4ig1+VXRln765vxptAv+xT26Fd3cteqag==", "signatures": [{"sig": "MEYCIQDTmWWWgIPdzgE/UKSZ7So5VB7LluadmkzYfJrZpsqirAIhAINRAPDmelkeEjYaS3hPSBv2jp94+brEDmFGjcMtQlKY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHpkXCRA9TVsSAnZWagAAlzQP/jIz0mSy+FsWIu+cz2ZI\n4soWXJuHkVFz7/+WI4XLr0I29lr/+V8oIQKKDqFl7BDFliZdQNBNkGv9fVfe\noaQY6BbhQOanTJishhEE0ZAbugpBDj9SXOPkDDliOAmqvosSmvexT8RPh9+c\ndwryNuvRhLObfM7Jmu3X9zFFZLg+gT5U5bJ8YfykzDUeKQ7cLIe7gr2epi/d\nkGgqyXVVWFECz23Sd27S7wPzdbKo4YzUZjVct6b3ffv8i/C1Zz3MJ9Zlppd6\nc1n8orfBMnq3xePxxvIScOKX8Mq7a6GQJlV8eFhNOO3YhuMi5OgnDIO1ix8a\nVoYqPJ724zz5P6tzrmqp+cteh58ycaAU0gcl+3fD5Xab9mmvJsoGKSUM3b1Q\n+Ql2UsolRuhWy1g/SdOq6EjJKJ7rVKYB+c+5a15SVssYLIYYxMLejcVCGtFt\nPIYHOFpCn2rJnmkSk8jH9f8LvY88v+m3MHsvasBUjhjsYpnz6zcmhrQQrKXn\nkaB8Nyjh6EjXU2qppjAY/VSceTEnSmUl/VxBGfkSH8DGOgdDAQcOG07TTgrs\nZ2xoAWuQr1yQy/BvBF582lnveEkuZhiolcNO/QrXvz+jIuOjFTGGPdezRh2D\nbxPMnEc7AXPgQDVWyyo7GicRgn1QwDd7NKv8DfcxgMJO/pHDwSreKy3qJmT0\nB89K\r\n=XYbR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": ">=1.4.28", "node": ">=0.12"}}, "8.4.0": {"name": "jsonwebtoken", "version": "8.4.0", "dependencies": {"ms": "^2.1.1", "jws": "^3.1.5", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "nyc": "^11.9.0", "atob": "^2.1.2", "chai": "^4.1.2", "mocha": "^5.2.0", "sinon": "^6.0.0", "eslint": "^4.19.1", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "8757f7b4cb7440d86d5e2f3becefa70536c8e46a", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.4.0.tgz", "fileCount": 12, "integrity": "sha512-coyXjRTCy0pw5WYBpMvWOMN+Kjaik2MwTUIq9cna/W7NpO9E+iYbumZONAz3hcr+tXFJECoQVrtmIoC3Oz0gvg==", "signatures": [{"sig": "MEYCIQDtZgzeT0myc20n4ePUTXRJZvf1gs9GRJCLeLE9IXm4IwIhANnxhJelJDgT3rxAl24AgKC6S5mVZYBFWi7LVPgDBnh0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6/0aCRA9TVsSAnZWagAAMQUP/jyb1C/Ih7Sr2IMxgBJD\n9pW2RKSPbC+YkCt3i1hp5GGqjxjLk60Yc0gp+p6pgB3VJFHiffkR8WgZ7ieo\ndN3A9GNy6JGQXScdvETVpjx1Jckk/39eJpUv7/4lIa94ZJqlvYgXd+Qlh6CH\npnn+qeeWnmCO64zGXGo7Rt2UmDSLvvDoAouEbVjw36I9etIiQYJTudQ7WVi6\n/KNj9l6O88iDtIUipTratf8UnFVBh8mOQx4qrIAb225CKRCGt3ed4iJHVfcE\nBDESp4Iuan70vYam0r+ZYAN4Fu1vKqlTP1vMmD0UAaZ3+IxAb1KQznjhDv28\nlcrAHSorkK6z+SLIE1ae8nes61PC8SiAK2zVuFbomlq6ULnzoiGXonvqqSAp\nVkOsrcxpliMCZPaCt0I6GZXINzW+HAfRjjysTPvs9FqU9HBJCoJl1y47uGxn\nFFpst5259T839lpPnXsxl6Q7LkCt4tZSfLBe4H1BkQL86GjkMkpmcKKLttMo\ngLrSxPOVLPxxdTHQvytoR92xOuejg8EbC0yqltL/08IcEF+FmB9yv3oijxnx\n4sXDC3bkyB62yduBZ4r/NXLMQUp+rB7cx6bmRUvai04tcM30JK+0WT8j99Op\nkSbc5e4k9nzrxdNbH5la+e71jfS775u9XGvI+4kn7oJL+f4FSnd/QOnYbvMK\nEt/5\r\n=aweI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": ">=1.4.28", "node": ">=4"}}, "8.5.0": {"name": "jsonwebtoken", "version": "8.5.0", "dependencies": {"ms": "^2.1.1", "jws": "^3.2.1", "semver": "^5.6.0", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "nyc": "^11.9.0", "atob": "^2.1.2", "chai": "^4.1.2", "mocha": "^5.2.0", "sinon": "^6.0.0", "eslint": "^4.19.1", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "ebd0ca2a69797816e1c5af65b6c759787252947e", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.5.0.tgz", "fileCount": 13, "integrity": "sha512-IqEycp0znWHNA11TpYi77bVgyBO/pGESDh7Ajhas+u0ttkGkKYIIAjniL4Bw5+oVejVF+SYkaI7XKfwCCyeTuA==", "signatures": [{"sig": "MEYCIQC2vl87fmhhf0qfySnkpFlzOX8mfI/tLaaS7rJW0F0O0AIhAJf+2Mx6GoumEA3WzxEqgnGApxfCQuCm5GmHFgGK1UPr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbVlbCRA9TVsSAnZWagAAOIIP/1hzG484UVvUnLPgm//1\ncA95X1dqj9I5LGzK2YdY5lvjJnf6vqEs/V9DuxqQEApHdsqa1lgy6X01zSCp\nS8jMDQb7Zt45CdDEyUWtQcZbbjCJbHkKB5DHSNLo6aib2NvjLKALWBIGLqER\nD9yV94lcTcrGb5RH5OQexpLMfhf0yXtgLfcK+tLMIhGiiRdM7D6PFoouR1JT\nx8icZAGjv8vUMO439DBBm5XAazZY5oosc/KocbL7deqRVMhqv4NG51JWF1Zq\n8Ll/TMI7mI8AfKAIPWmUwRaNjZdmgauyvn650htj79MzbJvu8fVOK6tlGgbB\nBTEkvlCOI1MTS2fNAKXrQNs+eYKZmwj1RtjBhBbJS5RMfDdUhrgnDzwNPSpJ\n7TNzU83TXP8MIpe4KlNKi42WyJb3UFBjFHDyXDReCOGLczLlSU99bWTWzSAl\n1mkjFQsQ2O1esE98FycddlijCYIcA2VvaPUMIauVd8FuxFsK2/F4jYZ40JhY\np9EsC96RYZWiW6gggeZamJsHXJHyFuGRBNqiX2CBL4ltPPPkvSgL4EH+6D6A\nCbNHkrMVUIce3HXL/jau/ckDe6Ygza7JQENeZbZrF3iPVLP3ujiGr69puBnQ\nZPiv4SK80Q851BDETKuRMWj5NhW+5SglF1Pb3vTMr5AICHSLHi0C8BNi7Xs8\neFDA\r\n=p2ka\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": ">=1.4.28", "node": ">=4"}}, "8.5.1": {"name": "jsonwebtoken", "version": "8.5.1", "dependencies": {"ms": "^2.1.1", "jws": "^3.2.2", "semver": "^5.6.0", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "nyc": "^11.9.0", "atob": "^2.1.2", "chai": "^4.1.2", "mocha": "^5.2.0", "sinon": "^6.0.0", "eslint": "^4.19.1", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "00e71e0b8df54c2121a1f26137df2280673bcc0d", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-8.5.1.tgz", "fileCount": 13, "integrity": "sha512-XjwVfRS6jTMsqYs0EsuJ4LGxXV14zQybNd4L2r0UvbVnSF9Af8x7p5MzbJ90Ioz/9TI41/hTCvznF/loiSzn8w==", "signatures": [{"sig": "MEUCIQCsfaIytqTHYFDoVGAJN+RqW7W/Z0YgJi/oxyWubMUSRAIgdkvTTn6QhbWu1Enc1j9Lj9ouCb4IOsMKsue3z1ikqT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj4oKCRA9TVsSAnZWagAAOJ0P/RVi6acK6Kp7HQaXvgRD\nen5GgkWOBL6wFEhdM2H2SjZ3NNzeMWkmyoEWPki+Cx/mm2IEHjzyOADwY/+c\n2TLqls9X1URnckpHxQJOh2mXB+QrK88wtfu2anFog0juvqy3xf0oO/NSLtod\nG6yRjN/iswJST1MwjFHzcucMTCKB0yXf2SKxqYEPS4O4JX+Utrye6O29hPfe\nw9SKKjaWAEUp8WKmSrZR0IK7xkZ2/WuKlSE3NVah5lHJIE+wTquMlJwg6PWv\ntX9oLS2QIf/PwbIctofjsGRjGL56KNqVdVFU8QX3AthFIrpgyRumA1pkQiNV\nj5koJFHPI6/pAqOkz0rk8+bdzC8Fyp3ts8s9RJGN09bxZoNwPN1fnwUanJaS\nZsRm4QPpSJiQvSCMDewtBbXGPOL/rZk/kjwLLVXPUFB45EM//NmjYUbciWbL\nkQgNtEEgmPiH8USj0UBhr0jLVFNhP830uxKVuKI96C0XydmO85W8T7G0JCNj\ngSsuNPL27qL4GsFb5UqVXokslm/MS9lDmQacTFPdzZIgpdFt01mk/Y78n/79\n0oPOZ9bZp87jeMVnKZCjz9UAEqXbpIKCUUfidNuf5e79XOSAFDxV4v4klEGH\nj57FvyYPI0A/5E56HBKuR4Hrs/b7tOnlcS19YCPGN3BtfNzPVBf7+PzZcsQs\nrZVj\r\n=+h0w\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": ">=1.4.28", "node": ">=4"}}, "9.0.0": {"name": "jsonwebtoken", "version": "9.0.0", "dependencies": {"ms": "^2.1.1", "jws": "^3.2.2", "lodash": "^4.17.21", "semver": "^7.3.8"}, "devDependencies": {"nsp": "^2.6.2", "nyc": "^11.9.0", "atob": "^2.1.2", "chai": "^4.1.2", "mocha": "^5.2.0", "sinon": "^6.0.0", "eslint": "^4.19.1", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "d0faf9ba1cc3a56255fe49c0961a67e520c1926d", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.0.tgz", "fileCount": 15, "integrity": "sha512-tuGfYXxkQGDPnLJ7SibiQgVgeDgfbPq2k2ICcbgqW8WxWLBAxKQM/ZCu/IT8SOSwmaYl4dpTFCW5xZv7YbbWUw==", "signatures": [{"sig": "MEUCICFbedBlabLjw7NIzX6NxKRtMJpAm5EmyIbY31lMv0ucAiEAtjD5WkMo3pdQruvAFl+kneaKRdGhZBknnEbeDJ86SdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjowRvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8vA/+IjrE5dhwnSVORY+0yO8w1hGVsdJW3a778aDOikwBWBzoDqvF\r\n4FraFF2dQpq/69S18YMWoneyhZnNOFexe6/CaB/kNszKNFiwDFzOcbhAa7PI\r\nAo1p3Udtdn7YeHc1bd9Ao7wfaJKiEsbJeLe17LtgQzCUgxBhnI55/Dpw1aDF\r\nZvh1GV68ZSep9mKDgvpUDdCwWyQ7lI7b9RlOeCrW1k+dDkkDVrkrZ0eT0ejC\r\nvqX20BYIOUkWFGHUxPRNWR0WiXN2PA0MiNdTHZbr/ySt7PNcxth8TJUdukfi\r\nzUXZqL3VS9imoOtxMdN9pu8Dr/ZS1t8GV/Y/Cw165HfiQUHsxaUU+NYHAUcF\r\noEoMyeRB1oOO4KHX0BorXV8NfxlWy7l8OMJgsmf35HB6yb9hFr4U/JBAzyZq\r\n27U+cunYTP1mPBOaQnfeWVAtga/srdBbMGLP94Is14YVuL8C0YZ8ph9veWkT\r\nyQYPdbzVSm0SCbhk2aeJ52jxmpDe7PiSqHZMmKX2TiAWMJU5fgNQOTDOaoyR\r\nQQOCz2mduRQVLRf5jMW8b9yZtAzkImPq4dmHJPs/JXDqfiUi0fVnR8NOdMwf\r\nolOqDsUJRfKVzeSSQmoyJzdfUA2ZFQu6a9O5bL3eAuYW8jcRiFKhOwGk4Gjc\r\nJv6ulQcUo9+LNW+zpoCc9xN0fCJujXJErCM=\r\n=juck\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": ">=6", "node": ">=12"}}, "9.0.1": {"name": "jsonwebtoken", "version": "9.0.1", "dependencies": {"ms": "^2.1.1", "jws": "^3.2.2", "lodash": "^4.17.21", "semver": "^7.3.8"}, "devDependencies": {"nsp": "^2.6.2", "nyc": "^11.9.0", "atob": "^2.1.2", "chai": "^4.1.2", "mocha": "^5.2.0", "sinon": "^6.0.0", "eslint": "^4.19.1", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "81d8c901c112c24e497a55daf6b2be1225b40145", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.1.tgz", "fileCount": 16, "integrity": "sha512-K8wx7eJ5TPvEjuiVSkv167EVboBDv9PZdDoF7BgeQnBLVvZWW9clr2PsQHVJDTKaEIH5JBIwHujGcHp7GgI2eg==", "signatures": [{"sig": "MEUCIHyw1iScPx8Kys/UT3c4e/8rNQG/5Pxu5rDzsYkYDwm9AiEAqOVe8VI23+FnVN3LnrIqsPx/ar8mcGW74fspMJULzjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84221}, "engines": {"npm": ">=6", "node": ">=12"}}, "9.0.2": {"name": "jsonwebtoken", "version": "9.0.2", "dependencies": {"ms": "^2.1.1", "jws": "^3.2.2", "semver": "^7.5.4", "lodash.once": "^4.0.0", "lodash.includes": "^4.3.0", "lodash.isnumber": "^3.0.3", "lodash.isstring": "^4.0.1", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isplainobject": "^4.0.6"}, "devDependencies": {"nsp": "^2.6.2", "nyc": "^11.9.0", "atob": "^2.1.2", "chai": "^4.1.2", "mocha": "^5.2.0", "sinon": "^6.0.0", "eslint": "^4.19.1", "cost-of-modules": "^1.0.1", "conventional-changelog": "~1.1.0"}, "dist": {"shasum": "65ff91f4abef1784697d40952bb1998c504caaf3", "tarball": "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz", "fileCount": 15, "integrity": "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==", "signatures": [{"sig": "MEUCIAtSR64IX6I3rst7ZohCDMNviMvI15lEObZtCDlbwvufAiEAjnPgUOosH8ojmbhwaEOwvPgrCpyURGOVd1tjpkR6FiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43482}, "engines": {"npm": ">=6", "node": ">=12"}}}, "modified": "2025-01-10T00:35:01.616Z"}