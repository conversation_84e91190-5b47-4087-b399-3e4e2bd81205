{"name": "assert-plus", "dist-tags": {"latest": "1.0.0"}, "versions": {"0.1.0": {"name": "assert-plus", "version": "0.1.0", "dist": {"shasum": "386c2c8452ebdfcf8f1f722fdea548c7bf874f9c", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.0.tgz", "integrity": "sha512-dpSjrqWF0+UoVeT8RUg7XgRjPJKp/A3Ki+sh1oRPaZgiB/wDsX48m5rtoy7m6j8a1lZ26uRbVz9Bxo6Jo9uOKw==", "signatures": [{"sig": "MEYCIQCxfJSD8PHbo3iVKHOYQR/Ez/mdh/uNq9m8C4S8rzJj4gIhAO0GMMJw7vS5uY4dPZ2NmNKgp6gO29wNgdZv9V0o9ZBy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.1.1": {"name": "assert-plus", "version": "0.1.1", "dist": {"shasum": "b070bfb80440b5f0812195e1b3395c8744c063a3", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.1.tgz", "integrity": "sha512-Pbe3jXYMjgzV/Jq+WTRGemDgm1fYIffHNdli2k9JSE6XjSgtdNbp+Ab5nM65UHZ7FuyrLZe3ApR1dnIDUo/ppw==", "signatures": [{"sig": "MEQCIBrg8f3BvMEjKeavuNNP1TAKE0BegzGsqdb6K2VuLRTsAiA2Nww8ktD4C9tJ2Z22BYBfvdK90d9kpEA/2aVp+xm5MA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.1.2": {"name": "assert-plus", "version": "0.1.2", "dist": {"shasum": "d93ffdbb67ac5507779be316a7d65146417beef8", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.2.tgz", "integrity": "sha512-BbJV8Hq6grYTokkHi/qKS34kfYIFYpu4wKd/H0dARsa6qOqEFH1wboxMwrccAmFjyRjkemjElaVC/sZSUMxHnA==", "signatures": [{"sig": "MEQCIAb+yNSYdoTV5z5TN+xFNEVEtaCasZmybMxjFFmMGIQ1AiAL4mT0qQQV6syY390SCX12RAX0r4ahGCB+BYLF4GsJmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.6"}}, "0.1.3": {"name": "assert-plus", "version": "0.1.3", "dist": {"shasum": "32eba8ac83e50ae4f4b5babab1ae9aa0edec9fef", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.3.tgz", "integrity": "sha512-1ZRZiZWBBgkI54m2YnNnNIeoynYpPN84XHpHAnPoEzZkrqwNROMQIhOwuEZ1ESsjAc3eXQ/OAmrFDGcVvZbqNA==", "signatures": [{"sig": "MEQCIFlCtjbFMfvVRdA4YaEILwYM7cmuPFkqwu9a+rc3SionAiBLbbQeSmi5uV0mWlqylipBA3SUoAu5XfeNQtYKr33s4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.1.4": {"name": "assert-plus", "version": "0.1.4", "dist": {"shasum": "283eff8b140ecd768529fbf3730a4c09ebec61f7", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.4.tgz", "integrity": "sha512-hS8i9Ke+BHuDcAumCtW4wZrFqaVdjv7kgPqXSbP2B7dntTmsTB+AJ4TLQhSkqDm5CfEeWKq/h3n3GDffu+sipg==", "signatures": [{"sig": "MEUCICoglX4NX3MTzsY3kZ0sNGezMEjwT0FzvkvOK/9xN5q9AiEA8Qrox7BrXj0QX9MjwH4+028MtYVXmpZ2+bYrGyOQBN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.1.5": {"name": "assert-plus", "version": "0.1.5", "dist": {"shasum": "ee74009413002d84cec7219c6ac811812e723160", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.1.5.tgz", "integrity": "sha512-brU24g7ryhRwGCI2y+1dGQmQXiZF7TtIj583S96y0jjdajIe6wn8BuXyELYhvD22dtIxDQVFk04YTJwwdwOYJw==", "signatures": [{"sig": "MEYCIQCLdo/z8k0+S43aAGBmcBs9Ms01QfKSXJ/Bgp4E8XKA2QIhAJKaFwRdJR6xafVCtnEwR23+sKWhKz3oiXtEOAu/csNa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "0.2.0": {"name": "assert-plus", "version": "0.2.0", "devDependencies": {"tape": "4.2.2", "faucet": "0.0.1"}, "dist": {"shasum": "d74e1b87e7affc0db8aadb7021f3fe48101ab234", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "integrity": "sha512-u1L0ZLywRziOVjUhRxI0Qg9G+4RnFB9H/Rq40YWn0dieDgO7vAYeJz6jKAO6t/aruzlDFLAPkQTT87e+f8Imaw==", "signatures": [{"sig": "MEYCIQCwKUBmv9LO9YbpD6fgIGg0s3pZscqI6Ak3Be6o1NMOsQIhAL8oLWRmoCCEp1Yd9MF3Ws+j1JAeWMOrQVdfeffnv9rf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}, "1.0.0": {"name": "assert-plus", "version": "1.0.0", "devDependencies": {"tape": "4.2.2", "faucet": "0.0.1"}, "dist": {"shasum": "f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "signatures": [{"sig": "MEQCID/TqhiA2b4H7rkc6tCDusmu4VSS3xZ0CYxf35pQKJ+sAiA205F5ly8ZJRIyCQmXkt1XDn/2XKo8Wj4uLLJHiBHRfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8"}}}, "modified": "2025-02-07T15:28:21.789Z"}