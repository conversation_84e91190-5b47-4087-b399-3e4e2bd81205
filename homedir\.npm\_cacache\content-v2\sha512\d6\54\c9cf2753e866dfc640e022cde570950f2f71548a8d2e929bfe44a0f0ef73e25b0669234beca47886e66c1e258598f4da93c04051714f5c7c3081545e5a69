{"_id": "@webassemblyjs/wasm-gen", "_rev": "88-d7871b3f4c7b1857fbcfdc66099c7bae", "name": "@webassemblyjs/wasm-gen", "dist-tags": {"latest": "1.14.1"}, "versions": {"1.4.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.4.1", "keywords": ["webassembly", "javascript", "wasm", "binary", "markdown"], "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.4.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "bin": {"wasmgen": "src/cli.js"}, "dist": {"shasum": "774e5ed7d21a52cb1241a862d3fc73ceaa857ff0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.4.1.tgz", "fileCount": 12, "integrity": "sha512-FalpqKEd7U0LfK6fJ3rEF/M2D252US1wHruPehT3LFlq8kw5DjyKnmAEIWmNmvyEyjZ4zc8fDabKVAnjXkU8Ow==", "signatures": [{"sig": "MEUCIEU1OxCVvUrx/dSzcS2VfZgHN4lvcGIeBM9fSJy7T0ygAiEA8Ke0JdupmuN5uqSEnfAqN2bSL0uNIozDG2sq/K7clus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19225}, "main": "src/index.js", "scripts": {"test": "mocha"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Emit documentation/code for your WASM binary Edit", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"commander": "^2.14.1", "@babel/types": "^7.0.0-beta.40", "@babel/template": "^7.0.0-beta.40", "@babel/generator": "^7.0.0-beta.40", "@webassemblyjs/wasm-parser": "1.0.0-y.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.4.1_1519654593562_0.13529261289029493", "host": "s3://npm-registry-packages"}}, "1.0.0-y.7": {"name": "@webassemblyjs/wasm-gen", "version": "1.0.0-y.7", "keywords": ["webassembly", "javascript", "wasm", "binary", "markdown"], "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.0.0-y.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "bin": {"wasmgen": "src/cli.js"}, "dist": {"shasum": "c65ef33b0f41c2e75b2efae43807eeed724ee5cf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.0.0-y.7.tgz", "fileCount": 12, "integrity": "sha512-y6RN1f2jjsfETu2wnkSvw6/Qp6V1C3aVujmD5ShbkUHUllC7pD1tjq8WOkjiBEyDO8l75EFTb9V6qym6ywPnVw==", "signatures": [{"sig": "MEYCIQDaAobSvDQzXcdUIFl/fz3vOz5sMUc80BXasH2ByQM9BAIhANuFfvjP/TxQqC7HiTN8GIms21H6mnpN+aeyzlObJTLS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19229}, "main": "src/index.js", "scripts": {"test": "mocha"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Emit documentation/code for your WASM binary Edit", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"commander": "^2.14.1", "@babel/types": "^7.0.0-beta.40", "@babel/template": "^7.0.0-beta.40", "@babel/generator": "^7.0.0-beta.40", "@webassemblyjs/wasm-parser": "1.0.0-y.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.0.0-y.7_1519654610373_0.5656654847710809", "host": "s3://npm-registry-packages"}}, "1.0.0-y.8": {"name": "@webassemblyjs/wasm-gen", "version": "1.0.0-y.8", "keywords": ["webassembly", "javascript", "wasm", "binary", "markdown"], "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.0.0-y.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "bin": {"wasmgen": "src/cli.js"}, "dist": {"shasum": "1a4572a6f7e2b62a207fd83861e12d03f505920b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.0.0-y.8.tgz", "fileCount": 12, "integrity": "sha512-TdUF8UowM4oMrpN3gf7l3T6NsL1HFeHv0GlrXiMV5UxJS6TFzuSmKcEg0rZQxF/TUjPQKewpUTZWLE0YvyENEA==", "signatures": [{"sig": "MEYCIQDqT/MXLoLmRS/4Hb/7QfV0sVfEg0F5wgkyAn+dwAV0qgIhAIETDzaceZ2OuXHwWM34oMORqUT7dzK3r+XRKJoKjL3A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19229}, "main": "src/index.js", "scripts": {"test": "mocha"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Emit documentation/code for your WASM binary Edit", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"commander": "^2.14.1", "@babel/types": "^7.0.0-beta.40", "@babel/template": "^7.0.0-beta.40", "@babel/generator": "^7.0.0-beta.40", "@webassemblyjs/wasm-parser": "1.0.0-y.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.0.0-y.8_1519655263326_0.5434422749767536", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.0.0", "keywords": ["webassembly", "javascript", "wasm", "binary", "markdown"], "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.0.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "bin": {"wasmgen": "src/cli.js"}, "dist": {"shasum": "9ae9117423c91dcfb7ae3e32d85006297bc491aa", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-pmBmiU8xivYCPj0LCzfgfNLfsNCN/D1WyY3LDpYLVrjGqicA5w4mtY1o2wfsvDbO0uC0pF25IMRSbvNNbGIjsg==", "signatures": [{"sig": "MEUCIQD6EB3rmUjgjdwhcwgLxNEG4IkgazJwSAdooBgfNYsOXQIgK3AuRlFG5FzsiboYAijsPkRC7ySv0JEspN8KqJEzbAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19221}, "main": "src/index.js", "scripts": {"test": "mocha"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Emit documentation/code for your WASM binary Edit", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"commander": "^2.14.1", "@babel/types": "^7.0.0-beta.40", "@babel/template": "^7.0.0-beta.40", "@babel/generator": "^7.0.0-beta.40", "@webassemblyjs/wasm-parser": "1.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.0.0_1519759813786_0.7608460477949579", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "9ae401a83ead6a4ca3d70cd40cb45ae0dee2b407", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-FCFI2B9oWdaRwhKwaPigz4j5eMZR8QK3NmN04PSyn1G3jUAjqTMuUmdm4YikxTerORyUxMItC+pIQkVztyNoZQ==", "signatures": [{"sig": "MEQCIEZE3hBLDEPVYdMOHbaQAXSNgDyJ2b8v65eYs/69wLJ2AiBkUlm5UiuMrhLy+B04CTy1dwTjU1sqqCsyggxjBek5XQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12236}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.0", "@webassemblyjs/helper-leb128": "1.1.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.0_1520242087143_0.8294771561972734", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fe481fb4b6b40a9426f4a0646f51ec5481a8b35f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-dN0dk6/syjbidVqJUXfN5NrllJE+dA8sioOJdE7nQNKu5QZYTSvYFvWydv5Mkr9l+0D+1thyUuaur+0pJTmH9Q==", "signatures": [{"sig": "MEQCIA2nQoHvYIiGjn1YSg0Y2SqiT4jrCqarI1IF1xp21VAAAiAuxAFZvYwhbPEeZUS0iLtslmD4GwjKpaDxNj3rw1IyHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12236}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.1", "@webassemblyjs/helper-leb128": "1.1.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.1_1520245620600_0.8897386670061302", "host": "s3://npm-registry-packages"}}, "1.1.2-y.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "38f103f25eff059b0a2c436e7e345b0bd216fff5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.0.tgz", "fileCount": 5, "integrity": "sha512-37WBIfJwCGicIT2neCojvV7QFC5q+TiY9gHITC581JWsBpKycJoMvHwJRNmE1N876lTzzbt1V2geZ9FnXhfJbw==", "signatures": [{"sig": "MEYCIQDRYcCZNVNIZ/pduGRS8sh0ERmY1Rr9fQJnkaj+zqy3kAIhAMW/UC87562yfnQrtZjJC1sLuhYWBB8VbBAKL2a3ZaJi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13572}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.0", "@webassemblyjs/helper-leb128": "1.1.2-y.0", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.0_1520266575864_0.18001393112182962", "host": "s3://npm-registry-packages"}}, "1.1.2-y.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "84d48fdf3f94d83bd18a09961fbf40c5c3de4617", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.1.tgz", "fileCount": 5, "integrity": "sha512-XrTSuNDMXn9zWVYyB9vVVR4AbVH5zW1jStoHCmrFcgEFa9gLRmlFBdMWYlWnUhLrFooBWTJyyFvChPSOQmjipw==", "signatures": [{"sig": "MEQCIE61MjGJZFIL4w16ep7vqY/5PmycYQH97gJNCABNPOZRAiAtUXHypuCWWeE1hfbooran/d0JlFi73981mL8C4AgMBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14601}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.1", "@webassemblyjs/helper-leb128": "1.1.2-y.1", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.1_1520532613233_0.2218664969509161", "host": "s3://npm-registry-packages"}}, "1.1.2-y.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.2", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "516fa5cc1d589bdd15a463e3fba9192dcc9d412e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.2.tgz", "fileCount": 5, "integrity": "sha512-JZEb3Z6uwHBVVlmynl9gQ+KBRKKpHECZr0AnbuxNMSP+fb5hjsSb68uy7cwqiYjgJ+M0GEjsC2seYehaglqHZg==", "signatures": [{"sig": "MEYCIQDVEyGG+U0NHAedzeRMtUX9Ra80ySiONkhEyPSSUsR4WAIhAMCpUwOSRD96wLgSHw7iKDUa5AcOvvj3d+tCenxVkL6O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14601}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.2", "@webassemblyjs/helper-leb128": "1.1.2-y.2", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.2_1520582278956_0.2041026925591749", "host": "s3://npm-registry-packages"}}, "1.1.2-y.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.3", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3098bc89fc18fcd8acd829f967d8efb7aff63aa5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.3.tgz", "fileCount": 5, "integrity": "sha512-nw6C0MJDvv9xfzkE0wgcm80fxTrzAJ78BznHiCkIMSEKAuJ8xUMvxPGhg19R+A3BFLZ2kXPR5KWe9zw4AO2eVg==", "signatures": [{"sig": "MEQCIGEYncdMejxV+M5/Cm3qtHwRxZt6jX7O0+5xiklziC7EAiAWCjY5bwZTEJsn8vDMPlbdKLgxnonplHHfxsO2FMjNQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14646}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.3", "@webassemblyjs/helper-leb128": "1.1.2-y.3", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.3_1520582949097_0.7776034877176194", "host": "s3://npm-registry-packages"}}, "1.1.2-y.4": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.4", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "cd9508504f217d1272f92bcd00395875338d3978", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.4.tgz", "fileCount": 5, "integrity": "sha512-ULkYqOR16iDwJKzjFyEES2OJ/Jfw7defsRmLBxNrvqo8/zr3uwKwMCDPNcmIo8Vtc5oIT8k0K0qebrTflcf2Mw==", "signatures": [{"sig": "MEQCIF6iopMHRT8Z8tRfVK4Ty2MritGagcMndEJPv8mcG9QAAiBUmIgqfkhSYF9fKkQ9DV0akBLqsVjQ7IJfJwg/ET44/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14732}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.4", "@webassemblyjs/helper-leb128": "1.1.2-y.4", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.4_1520583338828_0.6210791749611182", "host": "s3://npm-registry-packages"}}, "1.1.2-y.5": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.5", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "df5c47b19a4a0d9a4faac0b1ad36d92d20cf9c11", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.5.tgz", "fileCount": 5, "integrity": "sha512-WAXnmuhv503szsUeK9D9cbhkAkHQ+Bw1aGdGkkjmdsHNXbLc4prrHfXkR7tOFBozotoDHLqRCnODr6HdjCgkiw==", "signatures": [{"sig": "MEUCIB6BCqTYmlQp4DmBQf3/2NkTQ2NBPahC4x7vdb9V7V5EAiEAq+1P+JeqLAdilJcT0+Wh0yLjoTQBvxF1yPWOAtsmRZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15658}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.5", "@webassemblyjs/helper-leb128": "1.1.2-y.5", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.5_1520590717260_0.10219301767437661", "host": "s3://npm-registry-packages"}}, "1.1.2-y.6": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.6", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ea960b44712303b22d6a75d176f56109ada90b00", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.6.tgz", "fileCount": 5, "integrity": "sha512-OxyOaKNLrjvQY6ixnkjwgUTOU3oXfsNzTabqquC3j6/aJ8aoXZx/3ArV6cqAVa1nFS5o0IAwmSJ57GOMSTAH+Q==", "signatures": [{"sig": "MEUCIAVysoW5mWZZhYh08I+DeMZyAe2huySm3NI86wURuoaNAiEAyFtNbokevV1Q56yaeP981wS3jqlmPc6Kx0TA7mVpDBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15658}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.6", "@webassemblyjs/helper-leb128": "1.1.2-y.6", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.6_1520591354078_0.11814093238364531", "host": "s3://npm-registry-packages"}}, "1.1.2-y.7": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "06da828790137215399972840f0bcff33354a592", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.7.tgz", "fileCount": 6, "integrity": "sha512-F7dEsiHJARDeysYrLHMT+CG7967+KtxX9JiGrYjeOAl6mN7jZ71W1qLaMTyTuDBOFTCZMD8bwuzkQyaIMjC04Q==", "signatures": [{"sig": "MEYCIQCkEF9O1sDcDtSb69s6CbQOMt7P08IRVdR3QEasNt2dlgIhAKefTdd8dzikAsNuURkwK++DavrbLkRtv74bdX3HyYb9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24036}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.7", "@webassemblyjs/helper-leb128": "1.1.2-y.7", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.7_1520615248298_0.21921202990844457", "host": "s3://npm-registry-packages"}}, "1.1.2-y.8": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a686df3877c7de9e5f602c8767f23f7362cda325", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.8.tgz", "fileCount": 5, "integrity": "sha512-PQhSncbI3+j+mVJUydaO9+5j+vxC8vvbyp8k7KDtmaUaoBrOitLusk/pggbxf5XcfAci0MRyCVToDOlxPw40MA==", "signatures": [{"sig": "MEQCIAlBtWeEWuYQEDsB+YO/miReC+yGJ9wh52RgFk7+sTPqAiBNdP8DIPdostrxDXj9iCgQ2qeqdZoMqlcwXypjKL0aSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19275}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.8", "@webassemblyjs/helper-leb128": "1.1.2-y.8", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.8_1520850157237_0.36105429984993465", "host": "s3://npm-registry-packages"}}, "1.1.2-y.9": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.9", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "04e8ac83a20c6df4b6bea87013cd76cc081bfbbf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.9.tgz", "fileCount": 5, "integrity": "sha512-JcYwyAAGW4TtlDcehBrcndCEVPFzHu5qOEZb2MaWnS9bEVi4D2gqh8texixbAsHWsd4nn+RXMajM4PJOgb47Tg==", "signatures": [{"sig": "MEQCICMTZ9UrRr4h8Ev6kupioYCmWCV108FGnDUuBjwjdEOGAiBnySpM2IiNrlGRHlJsGj8M4yjXHy+5+TRmECzQSZmTiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19275}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.9", "@webassemblyjs/helper-leb128": "1.1.2-y.9", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.9_1520852690223_0.5545944084839072", "host": "s3://npm-registry-packages"}}, "1.1.2-y.10": {"name": "@webassemblyjs/wasm-gen", "version": "1.1.2-y.10", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.1.2-y.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "6ed893cb86a30efc67493cc3424f4cc0e18a6518", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.1.2-y.10.tgz", "fileCount": 5, "integrity": "sha512-XMkBMFocBekudtqFaOIhc3qWxnBL9tYPtP00afwIF0t0HjgpZtX4hp/WOSF0Hn+OKTM76g11TMqccY9Kcps/zA==", "signatures": [{"sig": "MEQCIDSmTPZ8FH+e5bxocZN+jc3P0sXENtLLOSmNjbPa/MUlAiBPgWB9bW9vbUWG0w8Tk4sfgMtnSMLyfj3Qtnb7k7fAaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19312}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.1.2-y.10", "@webassemblyjs/helper-leb128": "1.1.2-y.10", "@webassemblyjs/helper-wasm-bytecode": "1.1.2-y.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.1.2-y.10_1520871136869_0.9336905321013591", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "85cd6645c8e03d511b3c16219b8dff61f2382581", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-z4tKIlJ2n6XmnYd63fqFwRipI1eUvuiJFR+4RxsWaQ28M1lTMmTGaF7Qiin4Y41F6zYriIkYdu/A7TlomjoVRA==", "signatures": [{"sig": "MEUCIQDO0CbWFObqG9FQ8EUQshgSrctQR5KkxCEUmwS2DPUAzAIgQNmXifUtTCJdh7HILW0wXcx7x5zMvSr4+jE1TOfIcjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19308}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.0", "@webassemblyjs/helper-leb128": "1.2.0", "@webassemblyjs/helper-wasm-bytecode": "1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.0_1521099321199_0.2045619361020159", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7bb3c87abfb2b417e16a4bdd2821b8757c879f2c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-m3rOu5u8PwYWbO9XJeCzXp1DMFafUyL5QUNhcgPO8Zk9zAnXRSEeOy1kd83IUaeNsRAGJXJwGIJEiOHZhZF6iQ==", "signatures": [{"sig": "MEYCIQCoJj6glF6EpXKDO24lsNS9Umh7t100SLIECw5HiA/YhgIhALDcSsecDZIC5scgIyl+kjKJJwsh+P9VE5P6EneIK9bA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19308}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.1", "@webassemblyjs/helper-leb128": "1.2.1", "@webassemblyjs/helper-wasm-bytecode": "1.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.1_1521118173257_0.9582699414870643", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.2", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c468901f85a29f15d9f7b60dc72bf55cfe3062d3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.2.tgz", "fileCount": 5, "integrity": "sha512-Jjmfpa3fju8Xuv/LNqK9NUXozUbViCVHBok6SfDm94pDsOPclM2GlYixVCXJ5koc2ceuUwF3ez9eMQ8wLFo2Og==", "signatures": [{"sig": "MEQCIAsnKu99jiaZ9ZuTAoh1LE6gmKR4aRVZf8MTHJUxInWZAiBZR1K59k0qVQCzW2PAqF9vG0j5ckEepXQjM/p4Sva6Ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19308}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.2", "@webassemblyjs/helper-leb128": "1.2.2", "@webassemblyjs/helper-wasm-bytecode": "1.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.2_1522324056215_0.03418414481714671", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.3", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "6c3c940660ac4bc0001414c2a2baa657cc37146c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.3.tgz", "fileCount": 5, "integrity": "sha512-QQMg3dALJM+ugQ+JY8MnYRoXaHQCEzB/ZCDFOnTQwXwEt17+X2oRpgQV6zuvKVhBG9qSaI9VM5rxA2pC4bhstg==", "signatures": [{"sig": "MEQCIFZxxGXBK7bvHH0/CRSTenEqryRbpKqbI5KADOPjgqvyAiBEGAHoP8IDDCF06uvEcDQ4DJNEoks4RBVKdCo9cPgAdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19287}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.3", "@webassemblyjs/leb128": "1.2.3", "@webassemblyjs/helper-wasm-bytecode": "1.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.3_1523096809854_0.4631357358569559", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.4", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a5a29bc3ade23efcc41d6eaf90bef4adc9ac7172", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.4.tgz", "fileCount": 5, "integrity": "sha512-6AuvKWvtTAhwxqPqAq3Ry9oP1fYIF+Vin7xNPVv7CaX78zcu7HenFYvK2n8bxsk9Fnw0CDRcUsavrKXX+2ALrA==", "signatures": [{"sig": "MEUCIEZj3otQF0OIpW4lG9p75/O4MvHja1SUUqS9dEwPdDXaAiEAsDrSzhf/c4Tm4CQXgSg6cAbWN1eLsWoU13PVUGl+Nbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19287}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.4", "@webassemblyjs/leb128": "1.2.4", "@webassemblyjs/helper-wasm-bytecode": "1.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.4_1523615492394_0.43718818093114575", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.5", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "16b45c42ffb2460d54e7dec425ba92761cd9d9dd", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.5.tgz", "fileCount": 5, "integrity": "sha512-XMU2zMBsZUqTu69Ufk+Pq78/XnMYp7nFQuMd4vu2o7TZ0LZcCykBwlq88gKbox/0/p073pBHS9+X3hqtrBtqEQ==", "signatures": [{"sig": "MEQCIAfr9K8c5FYfPGbilfOYie+toQWavqR81aueZDkcJoV0AiBE6PTchgUszL0yJkpMbQs5xMPxNSTUYDlkzb9CItlA+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19287}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.5", "@webassemblyjs/leb128": "1.2.5", "@webassemblyjs/helper-wasm-bytecode": "1.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.5_1523639222858_0.06475770475200382", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.6", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "39608473a2b0c510a35d4ae6938e7920a0b2cfab", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.6.tgz", "fileCount": 5, "integrity": "sha512-xrMSAdjfKz/G+wYAu6UFjThy8d1CWqt4xb3OkxZo0v73JpnZxCzcFWHzIMN7655vr7PctK3jojQ2QfGi8HlXsw==", "signatures": [{"sig": "MEUCIH9xJLa1UT4CMl/bcSLYES7i6vaBpGR4cCvFaOnok3TPAiEAnRoC4T/PS0KL0op95O2pQxPDTRcQOLjdawwnXr+glAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1hCaCRA9TVsSAnZWagAA5JsQAJGsxdoyKdE5sg1Z7+tG\nCFPiDxFNtruwpU7EgmMl4tvnfTDQvlbw3+mT8XyzoccBPZYpX+SaT6gZ/f5m\n7j7YsnQAnyKBLWH97eiClNa9k2+aiTAPn1B+5fgpTfVM95Cbb95/aI0MN+mY\nnXKdsqTs8oy5EYpWAU7FIOmblgKLIad2DIJ9wrNlSKujs8OpCkEZr9QoOtSX\neg6jzbP7Zv+aMyZqeKjACx23Fxwj2t/RDsOAbj2Ujmhw0WI4BOlZZ8E/flLE\nCsjtNShEPSf1IRaF5lkD6z7asXxlMql0HcfBwn3pJzm4vzFcetKRNOqFMoaR\nsAkT/ZgrGAhv0y6BUP1cOymYAcH+BXAmWnATAdhch51B2n5f2sMkluI0Unq+\nmgMWk52/N02aN2m9G8P+KsdfTDDXUQnZ1RSrcsH5l29gBTW+jyC8vL5aKZbL\n0ijuZ16repEJM65Q+cfEjQIb28r4hTjUtTeidazB+uyWkRjrm4cZ0DOfHYZe\nH83vvFjVBTCwNo9C5On0xa3m/2JJWAXsxuyHbOPiGh7teexP6vdbiR+IaURr\nTyyMRB3aVaYNv3k4RmyYqEwDI2FdTF43o7HwC0ghuu0B8wV3HgTvoiOu7Ucd\nCd/06Kfbz7UaGTgALVmdAeANpJnMQIpV9X201dwG7wWezQ5gE1aH8sxBUzIy\n40zb\r\n=MjTX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.6", "@webassemblyjs/leb128": "1.2.6", "@webassemblyjs/helper-wasm-bytecode": "1.2.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.6_1523978393912_0.7697520894758478", "host": "s3://npm-registry-packages"}}, "1.2.7": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.7", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c104608f22b079950e7822703159d43f2c18529f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.7.tgz", "fileCount": 5, "integrity": "sha512-2h8MhNixfDqn++9OXM/bPxlBJEmkTyPe8C86D89CWrTA3JGiihK5Uc9N32zmIikKR13AF9KQ2ZoN/LgJTRwXcQ==", "signatures": [{"sig": "MEUCIQD8s260Mx9qViyWQf4nYwGTek2daaNCuLLWL9snR7Wj/wIgeSWcjzlT04+qCRtHQ8nzQXDUskJhCN+5MgflW5nuIrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5yuACRA9TVsSAnZWagAAchYP/ijS0JfwWHd6oDeeoSnz\nwW6u7r8Jdo4mX0IzNwY5fJQVAevTnsGmgbGQsMpRlnhTATOtl6N6or7Ih8pi\n/uBqtC0cN14LI81lPaoQaSy8Drx0UauheM5aYMyrmhIWfLAbZKhBI5HaJTNf\nSHmTDbZDJ/wfR58frfP0R8ODVgAhWlZ8MycJPT0pRWuElgW0aHO1/L99MOEH\n0v6L80Yc8x4OOyMvejem7THvgvQIYQKL1lkGEJR/LdzqrIkMjaKV/2e1PiK4\n3hgwmb1woCa9F6X6CUfA6rW2A2B3SXh6eeSV9Zn30zf/utOTUgY+DhHH/9rN\n723Oaj4zCMqaor45RvNo306XFPy4GhbOuZTGwETJ9sfdtozHAjpGJhJ+BYk0\nV1SWCsGt63JgwgBqhBxvqqNVU3XOo6/WZQepXmYxBFo71CU8rOu3EfhsL3oI\nfqoITxfvGB4/AQmyQo4X93yZCyvZDbzLGfaRCVlw7OoQN09Qre/mYlMFcBCY\nYNBTyKC68h4fD5D0N61KYvpMG/wVdM0FLGuKOtCOcQN4m8Pz1rdLwgAbf8HU\nfTKCu91VBvoVu6640XUIjs3hCACcW7QqzMjp+lGtI+Xx+ChisxeBNEHZ/iYL\nX5HendGEtqHoOpo6t6FdTkxViXqpmHEGQYERumN1BIQnWO0J0rCQ+L3N4P3j\nEEZb\r\n=fd+D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.7", "@webassemblyjs/leb128": "1.2.7", "@webassemblyjs/helper-wasm-bytecode": "1.2.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.7_1525099392436_0.6389389560675534", "host": "s3://npm-registry-packages"}}, "1.2.8": {"name": "@webassemblyjs/wasm-gen", "version": "1.2.8", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.2.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "d022892502df647d8a4f76313e737ca2c7e81253", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.2.8.tgz", "fileCount": 5, "integrity": "sha512-k+kVGnWkx24Cs4RcoZXOmk/04JIio1oAj24pXnPFSlKqPkdCnTT3K1DW1fW2DVkkOUsF+k0wDGSCGyV7uIigxw==", "signatures": [{"sig": "MEYCIQC/iNevBjLF9+TDhe8Q1y9vzyCdgkJjWsjKYr/fRz9B1gIhAIZMC+N6jlt305SOwYkfFFcDXHOD099PLOWad9cQtPtq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6fRbCRA9TVsSAnZWagAAWD4P/jx1IzSVeEBNvKHV7D8+\nnQyQ72GcxA2NFWNMyQahRJr1POXr+rBhEblyyC1C+zn+MWS+2bpLMGWZGzZF\nYQlgLagyNduQOKDD7o7oGIxtOCkncCCIonOl6GFVAO9PUzbaaTG8ngA7RU+Q\ns/dgYu7m/y9hF1b0YB/4w71Oxo9Hf1dkCgwxFo2iZCbw/fMH0athZAWm5cN+\n58G7yYJnjlF93hLwNQLh3omMGXH7/YO8WBILqTCGNi782rcAuvDgnR8CBWVJ\nfHMbF5ifcR10aKDnFEJbvlgVkKxKyA2iwtzEGRGZnZoLc5rgP8oAnP8Jt961\nYcOnqAWQ4hbRZuwEc7FmR7WAv35WGMpUxdTMLyDxX8wkMGU7Qn+G4jV2hoPP\nejzyLTYWCVTr/mSRIYDXUy5rVvMIAMH35EnzBpna+qnVyW/VE5Hnfjcy+ZjA\nBSaF3bgtE6BT6q+HaJJAK3TP54Dym3doChXIdR3oIl68AtqDq/9ZiapBbNeU\n2E1c6gKAqFmWaaGlIb2aegNP0s/VVQ6lHiSefbrqvKXQnFx5oFeSaGyG/oTM\nlWdupit/r31/AvIWIlRwjc5de7FpsTzvtSkL+HJJT5xYJSfKzS8rUC5yp6xx\nofukmOnQYwPqv0j2jiVgfRciPlW2lTqxQ3pzIv9MjCeX2tDwNw9VLD7/KFT7\nXZTW\r\n=ylag\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.2.8", "@webassemblyjs/leb128": "1.2.8", "@webassemblyjs/helper-wasm-bytecode": "1.2.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.2.8_1525281882873_0.5006486825997041", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.3.0", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.3.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "acf45b38159f351178aa14135e5efa4172931e9a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.3.0.tgz", "fileCount": 6, "integrity": "sha512-vvyJdznx82hqdjGVvQjQ1JMtD8Aokiu9OqaQRt62ywNyeBXNZH+Di3axq8725RFOtEYWuiMun6knfTVitVuTzg==", "signatures": [{"sig": "MEUCIQDcrpXJG6R3yBKVPz2+Ciiufk9cU/XIqTjIJkKckaWsEwIgHaNE+QWkv40qYoggCAh5cDu5dsg4VT9OxL0zfgZStz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7GCPCRA9TVsSAnZWagAAsaUP/0q67vp3OmfMReemVKe+\n+dZVpfVKNpIBS55uxqousiGOuqTO4+5y0iFzeSD5iHJ+AEJm5+xgvOpN7gTr\nlzWynocf15+qeEdAuKzX+cm0RZF4B6F8ysUzVCxuk0iMiRs7LuEBP/Le3MyD\n2cTjUQQlKK/sVp1s/JbTN+Zdb5Zqk8J0judhqGe1SdJ3tav760DRBXQ8baJw\nmglCv26EL+JQj5ul//XzOUQEw6W5+SDXtGb6i+tlJPbVrWnue5g7JcIxx7da\nCRdL/l5xVRIhQoWOLbj2Nfpgh/9pGCRDTBBVHPOmz5OvfxbUJIydnxLUHmQH\n/nkA42eoo3vP7EUnas1molAmYkpC4rdOnOgd0h5fzed5tJCsACagK6kcdmwA\nTO1WFGKMaJikFKsShq0bC50tiMt9CU7YbtyrGB+OXHb9SP7RFHJyJp8HBuia\nA5MiPuU5rOIGEaqiiET7eurN4AYNDweE2r4cKK04sQf1syK86BRENU2FB75j\n+46zhdEioBNpoUVKHkAeplSHeUR9ukfZ/OgjFzz5fYa3A0nznjjmqj3xYVU/\ny88rG6wZkml4T0DDp7WRBCxIAGV/YgFA0DbzoqbhMPTETUPkGfRckLk1nA8f\n5PmjvRNfV6kERuhHsdFNWbyGpgvkAt8IMfTtLnJN6bDDm+FOK9M76BCgrrap\ns9mV\r\n=fveS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.3.0", "@webassemblyjs/leb128": "1.3.0", "@webassemblyjs/helper-wasm-bytecode": "1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.3.0_1525440654747_0.15643861777450674", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.3.1", "author": {"name": "<PERSON>"}, "license": "GPL-2.0", "_id": "@webassemblyjs/wasm-gen@1.3.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "43263fc56a0570e0564e407bbcd4c02e85167398", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.3.1.tgz", "fileCount": 5, "integrity": "sha512-iYgQ3UJpsKiJnXerHulT//JpJAHuLavqMPEtUiRnSh0r/WvZh8pNtYBaN2v64+d9yTtu9aMlJmlKi6uz1j5FFg==", "signatures": [{"sig": "MEQCICWoCkVH1ZeFPAsUds0fbDXvG+53NuiHeUAOaEHh8EYmAiA06hHuTLkUT8GF2OLQEkahcPBK/OPemvYOSP77831Bow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8JY5CRA9TVsSAnZWagAAHq8QAKQJMG2LwW5DQ8h46qiY\nQNrhWjfJ+U6S0Eh8JRqmcx+ayH4LUslgpwkJ0xSf3kZ2PmC52hgU4C3RiygR\nSPeU27fd9ijWgORvjrShE3TrDGMx42GFXMdkcH7oI1Ro/lg6Nr99L8louAlv\nKXndt6WxQqXXfUAMXZgFn40QA8vaLfaRLlgpCSk65u7PnnU31BxN8YH1qo/z\nl5kSUlks+cDofCSz3WyqMrynx1uPJWVVpu/BKJnHMjxeuQekh74tllfn0/XM\nMDg55qYM/Pn3kTAfRZh99ENAST1DgYdXMirVU9zQTuViRu81I9OpJtSQR6qZ\n4ObZ9aP6ADqMfC6xa5SaUANaedC7jjodU7hhdab+q8sR4mpwhmkpGnstEY8b\np8/Ai8Xpwa1lfaV72TyMT0Yk8JivIsCGknlX+UY7jyq1+slAexsFzq3Lxizt\njCSoWkYNmpOrBDWuqyzxxLcLrpzXEBNwJyYpmKmaP6qADY/5NgauCOwcgrFe\nY8gUb1Ol+pFHDXZoPFFxmg0x5/LJY+KMsUC55VqBbZjJieTPWAX+Qdd28/EJ\ngHp/ypdW7Z3DwoybdHgasLHsR+RjyA8FYtSPkS98wSy3XujteBea7Lc6E3iC\nBIe+FDK3doqDyWDHPZyPXqvYeNBSyNS7mzc3/tny6qdIZ1kGIQLWUUTk3Ng0\nIZiK\r\n=Ld18\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.1", "@webassemblyjs/leb128": "1.3.1", "@webassemblyjs/helper-wasm-bytecode": "1.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.3.1_1525716537122_0.8350593957446553", "host": "s3://npm-registry-packages"}}, "1.3.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.3.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1f7baef9dc6c162b290639ea60f4cec9a971701d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.3.2.tgz", "fileCount": 3, "integrity": "sha512-H+pTumKWbx1+u3FagjtQ1ZT2gNMOB4VgBbC32rxPyTeVA51QBzrJ4gOEUgJrExbLVN+mqmVHeZTP1TUIl/dNAQ==", "signatures": [{"sig": "MEUCIBlCQRi1rLVUJWQNPZdOBJbVgoRRpGXLW5G5hI6Fe2yUAiEAz6fh5A/VcDM0vulhNWKFtrREiFTG4+Bqc8HZ9SKcdnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8fCtCRA9TVsSAnZWagAAJMwP/Rk86xpoW3VhsGhwJORi\nA9qqEQXckwtifqtOGATtq8GLuptdq6HRTdKGp2Y+ynIuSsvQPblt5L4xWpHS\nCEoF/6zqNUCBb53kLLSaBvKZ9IzKHfvF9ocQQFC67KJBWfA8+inz9N/qfJZ0\n/5RmeuaIx+9sGCVplaTzJ563cMIr/0jMn5cuV7y8/vpoW3jwZLczQzbjOM1T\nIFND8gYa7kwWWjPDyO0mJCj+ebk3DVx+yKWxPWZsFfY75mlDy2C5GwDfDOQ1\nR7oS2J/Kn/9k8Y3d1phgyoCTnLji3eurPi9RutvOE60dyslupC8yWaeAFAUv\nrm/T9TRpgwMY4Qbc84PveiD+j5w5r2wqPsXDvCCYmHuaVhBmCia/GMZtFDx4\n8GzVnUtn8cDvOyVlpV8RUs30dJ0i84sJYBkAiO/wuk6D6MlpKw9lUZT73QQQ\na/Oj+B9fD8/ELti1IAiJUaoVzaC1JOqNWmlzuI+/tUwGE8o7sqaNNO21Grxh\nqDU574HBBX9YE8wW0cyZKN9eUcKF1DN+kR8OGD0gY0mu78TK9YSn2pAWRssQ\nashXAi9pN9AxC4DXymCl/q1GvmWdGYKcAkHhj+SVUv7GX+9TtCXzSSEE7I8v\na7d8NNph2f/j/jlji57oEogRvsjGH7w/sXcgeSqaknXOuwysy/Z74Yw+hOKq\nX658\r\n=qybU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.2", "@webassemblyjs/leb128": "1.3.2", "@webassemblyjs/helper-wasm-bytecode": "1.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.3.2_1525805228506_0.15939038184582244", "host": "s3://npm-registry-packages"}}, "1.3.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.3.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.3.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "5b3e13f92f801c12a3595bcc350511136bee7f94", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.3.3.tgz", "fileCount": 3, "integrity": "sha512-B87EoXn51XPucnW567H/5j/qDpbYUrD6ev+IPVlPYyJMtHwkWmCqU4KFuT5HJiGWWJJQTNoi7G2VAemLmZFKzw==", "signatures": [{"sig": "MEYCIQDsNfy1kDfGtiK+4kb0NcMQS+OHGX25M3+R0ZjhbhCc2wIhAMeUSMVa2b7k1A2BjWVvMmKtz/7SThFXZJ9iEt7j+Q4G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8qjgCRA9TVsSAnZWagAAJI8P/ieWfQfK3VQcbLXn1aBx\n7dvVAGMqK0U4PgbTxp6cnTNAiIEQzKcjcJMNFL+tZ2IHPVSmmrFZjvWcLoRD\n0sWrKM62EkDb0HZHGGsVe1KR3ba/LyobZ/lqx8MeR3Ns9uyaeVXijWgX8VgJ\nJWW/X+uxhzAHMENdNA4eFqIl24/kueN1xGEr0cs4hEEDThDqD20+eLKQgIKt\nmSbhEuK1tq9EEwggrCHkUX4QfiaIaeDolosVYdPOzMMa70cE+SlePmBzzKUk\ny4xMmPZUHfzHE0YTCMnefbAJmBZQt81a+WALP2kEuEVMCwz9hmwvvip/+MrS\nKLAtdJapOTv5xexfQGo04dZYJOWeNhGcrBneYY6fYJawChrmV/jf/bV4G9js\namrc5yRlOT3IF/eibEIQGksU2Wi37+0CTOUvmt86v8uxwCtgDrrznP+bZA7P\ndeoZBE9bsy/+LjWt2T3wa7Jk1a3BaUgdd9kW4HOLgL4SP6YMWlu+TAK4OsxK\npLthRbT0RFSXR3U1gYKluTFWWP/DuT5tDsRQ97wMme9jucfLtpumBd4MvZsH\nWyDXZYkizxAzGJdk0gpH961wMHSM+U7JCBgJp2jWF7EyvfvWlORQPW9PaDoO\nDb0iiToDAW+Sg6gT7S1oJrp6XkTCvqCiuPfrnZyfJ9EyXZhCml/OLjQ1v8zj\noopS\r\n=0MQe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.3.3", "@webassemblyjs/leb128": "1.3.3", "@webassemblyjs/helper-wasm-bytecode": "1.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.3.3_1525852384318_0.8569046443478705", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.4.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "85824fb95c84ac3ed85313b8c81d5af3053c3bba", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.4.0.tgz", "fileCount": 3, "integrity": "sha512-GVUxTBk6HhIoKh34/VD4Tj5/LtN662IKFU9NS6d3E6OgV2hwoM7ksa0DN5cyEDocq/fLhIlOskVn/hv948otsg==", "signatures": [{"sig": "MEQCIEGiwnGa60wsCeyGX4K86XIVq+7uEbA6b495pNLg9X7ZAiA7XLKlN3ORtIT6Kic4AaiO/brODvHw7iB64uvy0+Ih3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8wyLCRA9TVsSAnZWagAAsdcP/j+L/+wooPP0cjJ+EB0p\nE0NuPMXM3frHE44MPTWapQqeBPE9GrEj0+H+8uGAeB801Eedbdh82HwyDtEf\n9JR/iV0sPDXbANpVmEskgSMccNLP8v4o5Oqn6Ik3mj2BF2wW/+bAtvOYSIeg\nxI3mxotGAiE0Ho2exiI5LVD7ytX5fJ6vdbAY9ozwBs1se1Q8M/6fa43h4Q+1\nWDLnyu2Oe7YdI0NVAnmC/6dskBthKSQ6wYcmuqSGQBP7eN+8nHmzGI71HoCy\nPx2y2evfBIUTNY/QOCqC2DCpMyxcOSNfWObfDpkR+9nKS3SlqjugCUzj2mPq\nDO6qT1q84lo14RdzKc2W/YRXEMzaYnsaIyTGRq/IHj+mmjuneFBVXj9MEDZk\nlmKnXE9C98M8lxN05w2p1THYj6NCLFDWc+fEaBpnEb+I0Lb4qAh0d7AKR8+R\nyBD4Eb4tOkDcLqReQUBtKXYuuA9eCJnGiqcYu1psZaD+MfrJfbm0y1wB5X7g\n1S2TmX3c/aGUex3Fq74TB2ha/FuSiBm0FvoZcZvmpifDH5qXAOwyMotKjjwo\nFl0oLdqOax7hqyjboQUJK60xHSADV5aiJwuk1Lb7U2O1THHX6HK3T/zYUKpD\nFIwZTqZuCuVT9yOOA6tt/pThlAlhEC+4fYiKH7plCeBLV2o+o8S+GJFscBMN\n2TBV\r\n=7O3t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.0", "@webassemblyjs/leb128": "1.4.0", "@webassemblyjs/helper-wasm-bytecode": "1.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.4.0_1525877899040_0.6102271718266956", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.4.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.4.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0899297f9426073736df799287845a73c597cf90", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.4.2.tgz", "fileCount": 3, "integrity": "sha512-CloY0whqC5iMtBAndFJ/1rHdn9m1+NwCXHUb3fx8HTVoEJuBucve6vtAWsm97GHzvi3IG6Ysa/qdy99yeUs6Zw==", "signatures": [{"sig": "MEYCIQDxiVYCVTYv1XV9p/sEmJBUq8vuAV0bpz4bz8YjNOThSQIhALar2bZmwmp+ffDv0xQF4NrfjLfUP/hgmhDwlF1WuF1G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9aTpCRA9TVsSAnZWagAAJqwP+gO1APnmZ7Q2RnBlLhJ4\n1BZOwz7BA+9qTCkPycZwYwPfocafwuSCinAecr4utmj0OzoQXfXzV319KHqt\n3xQpMSiy5LY2vpTGuTJz/T6W25+/WzYQs49dNSm1rBgNrbb8kVxrBs613mXo\nuc6ibbNIHqo11a2wa0hwVEwYblA69YlcNcXg0xiY88gTBYl0aNU1QcGfciCl\nGb9qxnsKRdnvL0yJ49nf7xFy09V7AOO81VoB9eTcPmLT3vtyxZDbsnuXXvDZ\n8l1bD4xLvu7nOZKA/BDOthtocNPJYVRwNZqa6yN2bQdbUOIxBiuXq5bW4/gZ\nUJUx7+O/N/KZyxA4BQ2QQgIqPl5rNoC33S6RQZvXCvP3rD0zQDjbywR/hyhG\nLR7TcccmozkMYWRLrIOWSxLcFo9hKKWnmiW7i4XqMUG3oB9eEyBJNVKz+mAN\nV742au/tSsBE+Udg9CBysIrLaXkquflGMTQPyv2fy35bUZU33fXRmdBt6ayw\n+8Z8f/0rOcjhbcRxFNM2DtsKN5Y91j1y/RI+k3LsoXNqBV3Za/6zXNlsnFe5\n+Nb26ZV5Ecrn6pyAbjIbL2zBZ+GgSjA2/z2w3PlxCYmqq6pQnXTXkdSwK7aE\n7laB/S79jWqYRjaXdzVu3ZqZQRH4fE2Koy/Rtp3g+4ZY7ph2e71wGlyTBtxZ\nziMx\r\n=iLtK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.2", "@webassemblyjs/leb128": "1.4.2", "@webassemblyjs/helper-wasm-bytecode": "1.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.4.2_1526047976501_0.6920309206973407", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.4.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.4.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8553164d0154a6be8f74d653d7ab355f73240aa4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.4.3.tgz", "fileCount": 3, "integrity": "sha512-eR394T8dHZfpLJ7U/Z5pFSvxl1L63JdREebpv9gYc55zLhzzdJPAuxjBYT4XqevUdW67qU2s0nNA3kBuNJHbaQ==", "signatures": [{"sig": "MEYCIQC0RiQG27gl/KFOZK5ZGWeBChsZ/Ead9g48q49GM+uHOQIhAMgpGUuuCJIHs5tDgCHaUM21ZzdBePIR24nIM+m/w+D8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9rH7CRA9TVsSAnZWagAAR60QAJcy09T7JWyEYQglfz0Z\n9n+AZshw5DuvW2YbpgUA9yPYr+jLBp25FO2xHeoMw4Zd01P1yMqSR85aSy4M\nzUIg65Sl/m2ylArT5D+OaeVFMVEyaXjCfsemH1YuLy7mX0nmSIgfzCr02Nl5\n5kkw1uEqkMJp/3xpGLBw2g5L0Ns1fMxFQTm/Y8IA9y6tVYHs2xa3Nq3abL1p\nRrsHDFlgZT2I6el64GWXUCaWlbFKq4TLh9FXm9SRZgyiJA9lVOu9gK3KroF3\nG2FISzzARxj1e5bF8zgqX7mqjfR2hiTn/fIdiJs7HOO8GjYXvY5orFLPggsj\naAC3KSKSdhlKzEXMrdwz/hmfnmye0Em9tZi8z5tjCsHDej0BzLQKcaujbiRC\niIDPmuxU6i/Pwa+NDPdmYcnZoONkqS85JFT8LO+SIOoso0bnoltxJ0m3/Svl\n4HLE9g0Fr2tK2oVi8EeRLcW2DQiIAy5PyBIsy5H4DhHsC70iULjSPiXbz+w6\nYUp5Wk5aL5pTuQ4aeHkvmtPRNqQMrPvEsjQdNvB4xUhmsB+6foGTlNikQvto\nhyXVXQVQ2d2deZgndhyKeHXQtYDbwzXMEwDiSCjFHbMxHrwLnTjcUMJ74k+y\niuEKElV/gYcizj3L5RReOdbWTltLO5S9thIBXbq/cElb65UxWooHRYNOHJFk\nsO0u\r\n=Qa8f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"@webassemblyjs/ast": "1.4.3", "@webassemblyjs/leb128": "1.4.3", "@webassemblyjs/helper-wasm-bytecode": "1.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.4.3_1526116859077_0.054684499149727284", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8d8ce57c8dcf395e1538d9343c1f64b37ceb4e37", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.0.tgz", "fileCount": 3, "integrity": "sha512-EpI1f40rru6MJuqu70riaBudOUJ5flyblG9W0TO3In4kb0DA+wmf28JEs4rXLobGEodN91SaCTxyONSPxb7fqw==", "signatures": [{"sig": "MEUCIQCTZWaoRj0699ZC/D9adZ9Z4u08o4Ucw1XKDFTbmM9t2wIgca2L5grG0uX1iotWErDsPSKeifs3iCd5WvuOFIAGqj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+b2ECRA9TVsSAnZWagAAUh4P/1cH9EthMmvckQ7Ly0so\nEQmaD2XM8Z3vqDG5juO99+CM9F6E/pO+PGJ7fxn9iqDvvbwBpKRfETmTYv0Q\n5uSb5MXk2raOXzMa5ymrjcIBKcegFQ+aZ0Ufx4KwIeBJ4n/YjhLStQFTeV4h\nIycf6mWfJv/hJxJh0/T6RqMtdCcIJqIdKxjwxjkzJXv/pa3oNOwYLvOpgjIi\nBe38rgwAvtmVJU0yFBIWWe5ujHLn1kOR99lbMmxA4Up1BUILRmudkWrxHsDZ\nJg7w7gslcN3MedsowhpOIjiUQuFCmyZVfaAfe/jH0rHvswCA2q78x6yNC88C\noSBsa7BqvuzktN7Ib8+MY2zzfQRsTsQtOJ9CTax/APzQuZ9iU4E94I8EF585\nyPWecl5SnXre+/zo39hwhTwdZgUg1MqDxIj63sR9VqTTBlPnq+P7PQSgFc+/\nwj700TRi5JfvDTRBXmShr/vrFLqE3VElHus65vtRWFV0HeDzZxLhhzD9rRI/\nEBUqMqsMThjHzVyjo/NjKDc2rAbpwGa0WQVVTvVSe+TMi4EFKOmThYwHm6kN\nbilAPAS/NUZIpVrI5KZ+pIDv/9iDE8HcULIO1uldeGeHJlmIjiINRTrOzFBH\nKdQicFQINJTfjSmpx6o4wawCyBap4Abkuq0DLBsJkekYaWixbTjOCVlqu6d9\nX9P2\r\n=tY34\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.0", "@webassemblyjs/leb128": "1.5.0", "@webassemblyjs/helper-wasm-bytecode": "1.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.0_1526316419370_0.5399108867781663", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "805c1b15ef43e3f311e96d4f8a648f03bfc3b5a2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.1.tgz", "fileCount": 3, "integrity": "sha512-oG+ox1blsYpqj/dSjqac96Gu/1nO/jAFeeA/Vwh5CIr0naWsfalivlt6amZX4i0z4NqDMnT5cc5Nr7aCcybivw==", "signatures": [{"sig": "MEUCICXpG37u+wS3MhqvE5SqENN0Fll/Eu5X23H5pMUpzOQ5AiEAwUe+NFJ/60i4zDsU05dZv0b2Z8r6j3t679PLoLVo0Kg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/BT0CRA9TVsSAnZWagAANEkP/j4CVUH7HgckzUc9sQ4N\ngkfBJblKzrYqBJz5Nahs3slmIwgu2KyPhtGMocYL6g8X5ZClvp9u+ALd0f0U\naZ6ghwbpjJ5yB8FjlOP89Lo6oVLqnHgegR+2FQYLV6DXbnsNm10o6tDCt342\nDXxsbWhXc6JN9fi1CFy5/lUghaHZVHHFg32b+brPekk999QWD1k126OmMJK7\nKT8STIr8Nlwu9W7bG8oTLpD6pIogBnj2sZtesJi1xNjkWg99/WPUphV/tv+J\n943TVlzKoOeCddS7EU2q9CdRJS7e9oRZEBmrzAB/6Hy/uMQugSxF3t9ck6GS\nqcTgdJW1aQYkUX9fJn8zFM7nPSHoursmhsZRTecpcv1mobOFl4I1BFbGJjr+\nPUcPvxMhoSVjkiC+9Cxn1gJGMVnzujV10wpu6c2J/aaJjOHClss+eBAK/hYx\nWtXkp/A7fvwn4M+KXLHJp0vo0JZJZIsI4AEvNXhOStPT8j6d8LqsIt8aiFZC\nWKDertVNCHnOH4SElsbfG7Y/CSaK2UZW/aKLkuQeeuqbyr+pmjXCrjvQFt8I\n2s6yUrvrlkohLeR1vdmg+IOIXsS5WDo8yaULFYNqErqaeAWS+9mgWRgxV38s\nfC7R1vFOObbdUYT9fuuHMPRq+mdZG5dkqo1+mIFIaFMxLPYS1WJGV4uJxRGM\nzkh5\r\n=iUV0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.1", "@webassemblyjs/leb128": "1.5.1", "@webassemblyjs/helper-wasm-bytecode": "1.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.1_1526469875549_0.4167869991712079", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8c9c4ce1b0abd75c46da9b95923a83ed84a55085", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.2.tgz", "fileCount": 3, "integrity": "sha512-EkiKmpqUJiNVnY6VY3nDx+frwLgfR6Y16PSKU5T4PVkr1i9FPa6qaOBxE2NDIwF0bjh6Kuz6fTB7xLEIs8m+hg==", "signatures": [{"sig": "MEUCIQC9IyePaJdeX2xaBflShoj+lnTymyjYQz4TxikTr4H3KAIgJAeHSrQyBMAzBbT9qu9djaWQwwbad3+KuXgtG1yk4S4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/XqMCRA9TVsSAnZWagAAqYEP/2+krY4OISA72iysgl8q\nC65zITTf1zVMZKugSEBhUsHngf9Kb+7fJV1QVTDsIzj675jkXnAIpHNqWgrq\ntQ7G+b71AarycJ7golrjUaK9bSoc8/CxpN8GXJCV/nYf7IhXH77rVUPCicpb\n+lyxZ1o+n1FdUkyFXlBbULVRXKSy+E3gVJc9oGnJhIy1YghqL6jB3z13JD64\nJzhDWSN7sNgegVHR2KXF7T7uNJ4EKNReD//saQiXOfSzPRYVh2lWMyJD8WXR\nFtJ5oJG61wTR709GeEKImKbHhHF+shHQjDwiUPEdTnXAsz5ZW1hEX2CBeMa+\nT4OXp6ELwQJS7HfGCw24lLchfMWhVHcM33Kty+qnmBMLPxTh01yM0Qr8QtL4\nKJbW7pi+AvPXVu700sGi0+XKVOTpmr6fq/vtqQnLjEqhFqCEyV3J1sObVQbP\nuY2rWNIWM7cQc9DWxqzdh7H8bRPTd0jYsOO0qFRJPlXRJ2xn7wgvmBfUvycx\nak9yDP0EAjohPa0UftZ9zfXSkq/+L6MwkdAiv5Pr1ChcEiuHZOzeOqJP11Vy\no+Rf3SrloKQqA0Iy+70wNcPldOJssYtpUrMY7YnLH9gpU3QwvjdLHyOYXoLD\nOzT8MXwA+AZUj3pO83qYmRy137bD74LonfTtNDTeaWxX0lgEiWYUUMDLDzMf\npHOJ\r\n=w+RX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.2", "@webassemblyjs/leb128": "1.5.2", "@webassemblyjs/helper-wasm-bytecode": "1.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.2_1526561419724_0.5141003087157807", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c358dc1b1281f140c25aeaa3e1c9e75275bebd90", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.3.tgz", "fileCount": 3, "integrity": "sha512-HDk7ekA3N8eIGAT1XH+LxvWkBTbGB2oI5b+wZ2eu9xsSMys1iYzW4/x6QASkpU92T5iJlZcdG6nhFClpYiARHA==", "signatures": [{"sig": "MEUCIBjidO6yr8a0fdwLOl9e07OJqDss1fe8APK3jc7XzmNgAiEAy1K0SX0xAucy9GQq1+BGUjd0VO8Q49LDexSPvnXEflc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAqidCRA9TVsSAnZWagAALYQP/ArXfaeSuqwwOgEvhchJ\nQa8zxdS5Xr+UwYO6QlS7WFJyIcGJBKWFL6zJQ4mm36psB+48W4QB+hywPuNk\na/g/FyZz8lWzDfr5DrPAUR2ct29irCGjwiZ+Cckp28TulP/14RAzulFhJB4a\nYGxeYdMkeKEhgk1GbqAmtVcsOo0ZPcRSI99s9mc5ToRcOGFUwg7L5at9A9VF\nGPED69Dow+XmKfGbUerclwLOBF3FONyMgYuAXvWfrnG8E7dPj8YcANWig8Gu\nI+lZtIg1rlIytddAeXynWvXzQxAKz7K4eDiOhLt9qK9yILmHXj53pBykf+Iu\nCFJbH+Cl/W14l4dBfJev8uhJTujocc1JUqmueJC4qg77S3t5HjfumwQ2oW3n\no2KM0omQTsqW6apsjzAMi25GTrRFJ2L22QXeyp+NDbRDgn0ILVOtB7yi1g9F\nafmADeP3S6k/RQC1WEACilRzIKCb2Fsz8y8pr88LvxN8iSsCr0EWu4U9Hl6f\ng2IlW7PObkNnmTC+1No/i7+budGY2xK+V6AX9g4gobuRPLPA6Dyb6EfgOHbj\nXAcxy+wEe3Pyyy69Os6A9pu/tTepLWhBjJPqqHuXWUq2omjH0m7jzHc5ky8D\nlwumqcpeawL4WbyuPmnk4mmet6smwJDGZby6TZgJkQ8EnDTMBgSQneP9jD2n\n2mXB\r\n=Ivh+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.3", "@webassemblyjs/leb128": "1.5.3", "@webassemblyjs/helper-wasm-bytecode": "1.5.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.3_1526900893101_0.15641520937405073", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e34ce3b4599aa56e28b21afd7731da7d374f0b28", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.4.tgz", "fileCount": 3, "integrity": "sha512-P75wSG6gF4r2GU71lqRa2vuVUS/HCESW7qGGEys8uBlSXOJAxnN0wGNdtgA8pCCI4dy3kDLVSWXs1a3i1SYEPw==", "signatures": [{"sig": "MEUCIFJn72KS+G74AoexGJ/cNTe0yKEcGtD8iQ7SlA3fhercAiEAsQqp+y27DJdhigZS729NM4q92GenlQCpxFjPzRpH/zM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAsIfCRA9TVsSAnZWagAAATYP/iznayESBAanLaRWiI0l\nDC31nTgsACXoljj1JMm+EET0EPV8vcBEnxWP27bFePiM2+h7Y1dppbJ7/diE\nprnLZckKHqnWbtUWmP2lYq15O+tSpT3g/c6E/+RNTB92yvaSwTDIKe6bMita\nG6D/TUwoRyXGJ/khP1bwt97HVGHCkiaRf/DXokU7oLuO4RG5A0ShcPlZl5r2\ntA9kfSAwCw4ZfZTuhBh8WcY+rwu0DVrJS89nRwbIbgNpf+vh1k5h1aVdZOr0\nR2IgCaPCHqRPR1grFn1J2yOTbJL23A4500tcsNI7FU1KJ8YzvGH9z8nSPzq7\noFM2B8i7Cj2kCrdubVKquE8N0Axabo7wD/SnQL33zZAdilPBKnrUrzpf8/OW\n/rasHZ9x7MmO7yhVdDxn7TaY92wEg7xybyB739rWJ/YpfiMa+pdbQv2ebnaj\nQhGxvCdKw4JVN7rkNZAWwFPED7BT3M56YOv2yZ308rqVFPK0ZbHs1IAQr1Bh\nxNYpE1x/d8WdK/MuXPRWZY/lLuMue+sm4F6PWSz/R2I3gL+AiqymaHfSez9m\nuHflYNqIykUAgfC50AfAIt5vqDa8nV3I0SQkXR0htLWCl+fcMnTYgmbjGVP6\nEf8BKZqnbIae13glFJZ5zDAF++6MybMfwou2WHKYZkd6M5CnOQjt74BE+cf5\nTQcb\r\n=yaMp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.4", "@webassemblyjs/leb128": "1.5.4", "@webassemblyjs/helper-wasm-bytecode": "1.5.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.4_1526907422695_0.3195564922391625", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ed25d7e20fc2ff0027f8ac495edbcc45eb8186bf", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.5.tgz", "fileCount": 3, "integrity": "sha512-SrfRxduMDTCAeFJYx5ROhRlGEZ5PSacC6SSLXcftZA8OTZaT3gZFXh/PKCO2xQXwLrc9fwnSKqAp8Po5y+FRxw==", "signatures": [{"sig": "MEYCIQCCGPlP1GTB7wubfFtUxhG6jsFtvVpl6JJQla2sdMQQgwIhANm7n5FIql/GV599LyN9oTZG4FX+HEWV2GheH7ZTO6nN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBnF1CRA9TVsSAnZWagAAcl4P/3eM8AQfP2HPTszL7xev\nS6Vd0X6vkCl6noqPRGMZZl797D0L9dtHtTQtJ5f66AejbMr06z7Lz7KbeXTK\nToAeBtBZArLCT1Q/NaurhBWOZafPxaP7xVXyNCF0F3bYMgC8yXa6mPHoRnC3\nqN6ZF56R0T+tkM20/ZFO16m3/2IVqK+ce54vIhANJC54fu1lTLFn5bPEHlqk\nyC6xPFtv2Q9yO+RAeiYGPZn30XwAj8R4RKacEeJxFYQkb4636vShvpZEST3S\nQC4y9rmXZQicaH0FzKyHAc5fzSu7dQDpEiHdncjcJQxlNHYXXARHHo50xtIx\nE+aeqk2TXyczg8SjIByVBKyVxa/z88KBaehxLy7qK6aODbG7Rplpbg2Tw8ln\nqXrQEk0LQ/ZAK4ljbwHvoX1ZlnxI7UoTVLMdXeFOrahK2Q++/TJu/dKY/+GB\nc3SoUcCmP1Dn3ad6aaChl291agNf21TY5/b1Eg2OHXAUwxm++P63SW6XCyFc\n9R13YhUUrqs1zHM2pTKbmNDeyS2Ed/VmJXKU2y8e64Dipds2h5BHboK8UBSz\n6NQYtsUx2tcFtbrYU8arptVz5fEZh4mYsJLMV205u05YlQf1MCf9uujzIOP8\neA6zG+DQ2hJf8aBre+qklkJ/HQhNnBOXE9CGmZrVGPie4fUZ1Q+JS3nKv/1S\ndFoB\r\n=pABb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.5", "@webassemblyjs/leb128": "1.5.5", "@webassemblyjs/helper-wasm-bytecode": "1.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.5_1527148917004_0.19844624072600214", "host": "s3://npm-registry-packages"}}, "1.5.6": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8be003320f15d9d01f3cdea5d978356474c91184", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.6.tgz", "fileCount": 3, "integrity": "sha512-bAjUiRpf6/6smmIS7iJQKE1ZwcGmrTW8VKt8Mrg55PpO7TlDxRSAeuEFpkdD+rqoiCYdhrHyOm2gguE5/Vk91A==", "signatures": [{"sig": "MEYCIQCah1a+MUc6f6AIsoNUEEcZ9Y2XoozUjH3/m4VJA0fYTgIhAJAnyZEwl7mCKKz5MRQ7LD8UjgyLeRDVZWZyTtSdwr7L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBsppCRA9TVsSAnZWagAAve0P/AxEtT+lNMKzYlkrM3tR\nQy0PTdcBCVmnARRj5S7jgp1JTczFvxxaPnXqMZ5qyxlGmy2U3r4Uezeh+NH0\nrFHTcVKDqHWhtFGI2lOCpmcHu1kfxqxFuWoqCQiHocDaOpi77tiNBV0XFOG9\no3sIkDAIOR/ebL+rCanNeC5hhkekgCOQIhMPuo7MGYDuRgmb8duiBuhOa1MX\nJLAEHUZ9uJS3yVOKXD9Z6BN3jRWGkGFxmtCxpVeYqByqVm3CeOvB1Tbw+88I\nlgqM0k9xy0KIrwewEsxz+VDaQtxQirQp7m+BagcA/IlOQaEzMSnAA53mph2T\n229FAhBH9pMZ/opy7BVtfC7YYLwLgJwXUxGFPMKe6pLfIQZdEo+AAPoanqVJ\nFGJq8p0Irdpj/iIJm/YCPY7iPFfQR7sUIO/zrVcjFxGNnE6VYqb1Ic0dsPHN\n/sJ4pTgz7cZ/rk7YXqTb0QCCA2HqcR0c05Oh+KGt3E8fzJzgP+vCcxEHavGO\n8jUBqMqBSx8o6xAeHkv+Ebdwsrq4LYXTN4OrAj6NubIr6rBFYPgNuduUub86\nbxrlWxXzTtJepJwy0d/uK2pwfmnIk/9UDfajUYgUZNLfpMmbAOpy7MxrMhQL\nUSVrHnApocs4ErSqyTwK9nxmNU+XvWyUN68mONtuWBHtgpR73gieWEIUJ+GJ\nCIe3\r\n=vuxO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.6", "@webassemblyjs/leb128": "1.5.6", "@webassemblyjs/helper-wasm-bytecode": "1.5.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.6_1527171688626_0.13931783660364983", "host": "s3://npm-registry-packages"}}, "1.5.7": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "00e36330ef9103bd343e37a90c23a6e2fd4e617d", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.7.tgz", "fileCount": 3, "integrity": "sha512-f1f9w+dKdY9pQCcSFU9TABoNXoMJwOp0Vmpfh7w0E8q91HlgBQaO9gGuIgE3aJDjCUnUN1LQGJyJL+J5BBdCEQ==", "signatures": [{"sig": "MEUCIQCNFPfMnqeqDqiyugyEhdAb7eVpimstRGn3+Qi3sqz6rAIgIzQEXbsc2F1p/BM92VdaUHGWe9js0LPL2eWKiGxXEhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCAeFCRA9TVsSAnZWagAA2vsP/iUcjRvlIViqissmUKh+\nbrviYiFV7HhHSD7f1TIF5NnLA5OveAg0ZfomQJNvqPi9RpvlssD2LmlYEhM1\nch1c81Evgs8oMEwwit7He3jXMwPJw4x0UalSY2pNT3cGHFQycaiNZEV31phN\nEGIHir9kK3aJfylje49jzavYW/tkCNpTviJF9Jhsdh0c2rQ2pZ4dlYSedRAx\niGhhRSodFyFp7QcH+8BLlsOUjsc+hxN//dKoptAtrbSUff9PDxfUtJDBb+Oz\n5DuNG9TwrOUXeRtYhJ6nSkGGXrR/XJudDL27+Wv7ivBuZ/vSjhe3CbDfQYiJ\nVATOLQiWkK/20tbV2122f2h3nphai0d/JBzJQaDUBfNe8/XA9vwOcw2wmmeV\nUPlufwIMgr0me8dryVrg88sqyeb3FssQvUYZKJ+bgVWO6+Ndy8K5CUroe7ZB\nusMWXgZ4ju52fR+XfDvPV2DwuV1tdyFB6wz7w0DFc1SVzQz0W2DvZEkhqC4/\n1QNp1M2W0N1jZlVR31iSB096Z+ReYntwpy7fOD2ktkrFe9b2fUaahDW1gZhZ\nErrhZNGwkAD9FYlVyDgw346268eY9Aa2YI4cE8JFFCS3hb6lTt8AugERFEJz\ntTH6zBRDxPOWwb52DwjfyopfXhHIG9D/I45o7mkBalSZ422FfqzE7xsEOyH1\nOYVC\r\n=2lN9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.7", "@webassemblyjs/leb128": "1.5.7", "@webassemblyjs/helper-wasm-bytecode": "1.5.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.7_1527252869338_0.5123094407700863", "host": "s3://npm-registry-packages"}}, "1.5.8": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e94e034a45227aaa7c481b25c1aa9a0a00ab9488", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.8.tgz", "fileCount": 3, "integrity": "sha512-RAg5Ew7h9RzGPNUfPMRgOlA8Qtju1Hf2w6swRgC4suYMreobUig4qImdABwzxI6K5qqcW4AP37cNHuSUo+NaLg==", "signatures": [{"sig": "MEUCIDJsuEoDPGKnWMgHgowm47BkBLgAyH69k5vrYMC+HeRBAiEAsR8r9t+11tvDigDcNh/mU7aVgB6EiqPoaBu/9+m3weM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbC/rBCRA9TVsSAnZWagAAGyQP/2XbraIva3WT/sTrQ1Lj\nSFVgCmDG66aGLAtYH/IUGxdcghL5eSjlFeaFyBAZu/4mWqVUdOR7pUweX/Lu\nwibtCKcSAUnBpKBgVVzt5+TxYbAwuR/Dq+jQH6PjRoC+YTnVisSnjSEK2b0i\nBwERx3Rw/Up9QtOrIe/6dLF3VfFZOgM8z4D3iO5VZL5SJtXvVG3+dLdwTo3E\nfkxJBQKIp6OfU7+4zp9U+0LzRetusnKAfgcaqDNKme/CdC8SOfOucXjaAcKY\noGZOo3oUYCC5+5VMRNCAjuBZZqUQCiKRtYKBGdD2M+JIKWUmBygaC1qIhUtE\nPLOjRe4Hvx4NtQzSOz/CoNvcc6js9xnpWUsnmvP/824fRzjux3nUsh3mqtc4\nWAKfMNEPc0h4ThQ7G4U+M5qhKs6J19tvEHOFFxAKx3d66SzFXykvlWF48pdt\n/MaaNBKxNFbCc4N2Dl7lvy/3aU16XM9TaRIOrn2aUq4FWyAXwjZxIaZAhzll\n3UskXe/DvXgrE00oBkKcM0w8fiGGfFG511waHLl6XGvxQwdDPfXD6Jpdvpci\noaw3lwgapYoZmuRT5ML6PFAkyF0sVIU9zmVlMKtfmB96ii7yJJ1zmj3KRU9s\nNxPrxUG6CDAYUVfz3YEDkXM8L9QR6erfPQooq1UrJT7sSfXJ0kQYPjyUECEj\n8qeQ\r\n=pciW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.8", "@webassemblyjs/leb128": "1.5.8", "@webassemblyjs/ieee754": "1.5.8", "@webassemblyjs/helper-wasm-bytecode": "1.5.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.8_1527511744995_0.5401942511159685", "host": "s3://npm-registry-packages"}}, "1.5.9": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "85e07c047fab917e06b18dee4d16342a2fd3c59c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.9.tgz", "fileCount": 3, "integrity": "sha512-UEhymlxupBUJuwnD2N860MqkpE7LHt0tNKqAgT4YAVjbx+88P6MBBk+q+9wr2FJCXxMgsPTxMWifqC4wd2FzVg==", "signatures": [{"sig": "MEUCIQDXlH+bvCzJE2I212hkYS/14ZNK10nb4YNx+sBPtG0REgIgJRmMQiUUDtByMzwkTOa60/e9egK0yfz7Hasn8YfwRa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDVH5CRA9TVsSAnZWagAAUjwP/i6N1Xvz5eZsTjG0PDmi\n/USpjZ65mr6rIxIbNP1W07clMKA+H5vme8wptLu22SpFHW/ySLg7ESDGeiF+\nI44JvWVXk3FF8OKP2lNHdfHIToJUP5BFP7bylZ9B+rPzMOW9kCzjo3dJAH9A\nTLodr3NZ6w/2KO+Eqz1GvEmqXZOdaY7Q5jMenEDLHs3mrYIjTacZvGILi9Oo\nqZzB/6ctok2jQvhv8DfpZpRWAC+5GRhb7cS2mrQMV92wtyOHHTPEUnw6RPX0\nZCtdxsdhtK7zYjq6c4VyZLcfVLomSDJJAA2SzZPdC4dq6t68O97ceimFtNh0\nHGexky1r4qP/9Dz8xDM55baME8jA8EK9RiDrb2A9CqvKQrzaT5rAD4CRmWk2\nEUtP5eNXdwxx2aqrNWbEpCsK5kSipVltSuQ2qThxo/Rv0EVMc7IDoj/JxMH6\n+sAGqvt6DVV9ASVgimz2IYvtq2PaqYxjZRTbDacz+DVIOGZJkJkoqyGloA81\nlocEJhzEnYwcvSwwqo9yH8E/UDgGjCf7GzmZzUmdv4Ce5Pw72doTT4dyiiW1\n9ZItAOmYXR43hVJnDUA1YUDA+JoJyF4iFrnUqOz4Tf2XxdbxKazJdbgDGFCj\nFS2cr9dZ+nplC8PlmKZBv57aCpFGFQJqNMxSHjVk34Bssu7znuwI2oLXPLtd\nnvpS\r\n=XwYr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.9", "@webassemblyjs/leb128": "1.5.9", "@webassemblyjs/ieee754": "1.5.9", "@webassemblyjs/helper-wasm-bytecode": "1.5.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.9_1527599609457_0.7042817324848736", "host": "s3://npm-registry-packages"}}, "1.5.10": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8b29ddd3651259408ae5d5c816a011fb3f3f3584", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.10.tgz", "fileCount": 3, "integrity": "sha512-MXYoZg7zaRGmU2h2FBa6Oo+y0etuDZycx0h7nrBD4LzVqhufenoWY4Be6K4IMU0L/fRb/GMp17Vfqg4m/J8EuQ==", "signatures": [{"sig": "MEUCIQD7fsnhh9di+R2fLCI7DDZbWoGs0jlBJ2FUYnyq7wJ6mAIgL2BpcSlsPcx4PoCIj4nXSRJ3RPoHQ2Pq0ED7ZKIgx2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEUhDCRA9TVsSAnZWagAAt4YP/RDPfQ5xV4wpOK5TufMZ\n+tnV7KsExLStANZS4/YfOA2WYsmwuWjIdfFyPPVpQnkNSLOwX7HnjhF7eRPI\ng7sARXyOKZhmiFcH9so3Rl6cjSLdRCagZZAv9ZBK5EyX1wdJFqjx6CXcF4JD\nkCsmGn+wy2pNEPm6/4DjbqTOHjA7O4rtvewd7OrhWJ87IOH2ce9xLoHKofJg\nUdeEfPYt5JVOw36CkA+c1PZxVCuhXJsZTVz3gUBC/L5iepr8JGb1arC7sLzt\nsW74cHKm0fGBMS7DSVpHR7MAINwxd4CSzNENL5x6PMzIdVeDOwDLwOsI0fWL\nITx2YuzopHVEgQ/2d2CIEd5G6+O+NujcsINMWzpNvXm5hc3RhtBWd2nlViLj\nosE19DbYp4gd1lMVNEqrJ7Kj1elsvpgQz2479m0gszCbvI1NVQKGj8Ew3fuM\ntKdaO5K6t7QAeFgnHBrA8uQAaRUVyUKDfXF7wdNZOjqo5E/7GkJCp7o0iy2F\nVT1lMdxA4kcAxXqoqAPcQH7q969//G12xYpufKI77I+C0odLSBeHhg85qTdz\nutJNOowgeCvvOjjvot3rMkGJ2t4wKmaJFGEZqW/9BI5ScSADmFn4jf0Jb0Bz\nFbnXpBBVW2fZNpQdDgjK2p1k0Vi35mGcTF1jtlLvmA6LBwpyL5Zb16xm/JGp\nLNqR\r\n=G+Q3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.10", "@webassemblyjs/utf8": "1.5.10", "@webassemblyjs/leb128": "1.5.10", "@webassemblyjs/ieee754": "1.5.10", "@webassemblyjs/helper-wasm-bytecode": "1.5.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.10_1527859266921_0.1860838306900905", "host": "s3://npm-registry-packages"}}, "1.5.11": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "c03aca58ba954f80ab639985e08af06a90dfecb4", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.11.tgz", "fileCount": 3, "integrity": "sha512-0k8xAo9mIMYg3RyLQWktkZ8pgRLJ/u6Fmjye5tkpZA2BEhkxgUvRS/+AivwiBEUbyztnxFehy2kOB9YJuxGa9Q==", "signatures": [{"sig": "MEUCIFNwkSgWb1n1j/eS5scPXPONng01I1526Cckv5bjjM4BAiEAqpWB0i8KRHAXaoCTl6VKDzjsj0yjYw7Jx6gRNEsYbH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbF6I1CRA9TVsSAnZWagAA9V4P+wTnL7Z2N7M1wzP3UiSM\nog/aoestmW1BK0CHag6nrevDcMLyllGeO5TR33kDpRuUfbeuYxvbMUSaZkMT\nByF1tfQfBSUC5/37i2nJGzL1TxKU9dbGlqxUE8c6GHNOZTxvoLLPR2zGYBry\n1nfXnGLl91cLsegobpm8SbA5V6QnLn38PhpI6IU91IqxBVPH8uhioH2I/5iC\nDAX9cixLrVqVXkXknRImNx3xsORxwtb0tAOz+R+TT/CviCBDy/+N2Rawaa1T\nmPjzp5Bc5w0NYtYSkrs0+NlTqIEfCKgLmTCQ/h9kU9Eph3EfEgtmlRHeryCD\n8GaZCcXqFH/yZuw+8FCog8zNw1e5arp1HhOroMt+epzXM51Df9LIeJjiLtJT\nS3m31T5bbFGNG69lnyzA1qshleZNXHmqpQOp4b+OtKGlG014Wh6V/KNmwVmg\nfYZtBICpVofJkdDpahqsorXPmkL912Se6iEQeDMcBcaNq2HZzcG5kcEL8ynO\ndvH+pq+lgpBIOgA2MMXbMiEq4P7P//5jX4geKysrpVkDXwahpK5lZRRGD49J\nITiyV/87FSgyJuB5IBboCWqiIWtJZDY9okmHgfKWp1+c1a5wb3FEMcQuMPhH\njkxX4e6sbuqcU3aIIyeN1r1cTH8RgrDQsolPXYMQiqm3BwNmBV8Vt7XH/ETe\nI/vz\r\n=c1Be\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.11", "@webassemblyjs/utf8": "1.5.11", "@webassemblyjs/leb128": "1.5.11", "@webassemblyjs/ieee754": "1.5.11", "@webassemblyjs/helper-wasm-bytecode": "1.5.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.11_1528275508838_0.2349012673250841", "host": "s3://npm-registry-packages"}}, "1.5.12": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.12", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0b7ccfdb93dab902cc0251014e2e18bae3139bcb", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.12.tgz", "fileCount": 3, "integrity": "sha512-LTu+cr1YRxGGiVIXWhei/35lXXEwTnQU18x4V/gE+qCSJN21QcVTMjJuasTUh8WtmBZtOlqJbOQIeN7fGnHWhg==", "signatures": [{"sig": "MEYCIQDOL2hGUZsAilRj2eAakQ00ISzhHm8vv2HyGr6puUK8AwIhAPMXKql1X3zVp+RP2txR+T4EoH1TYnuNrAaKSzEM3P2I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPoUCRA9TVsSAnZWagAA5rsP/iwNCB8PsAut48uYw8lq\nw57gZocMZnb5tP2ide9RdFHOUGi1p5koPBWRXEK7vJCP2TOxzzj6T94AgElr\nbeutrFRjDyqxhvQAZVpknWcRIHWDNSfRq34o9D38QUF7UioLn1TUTfjDHAuq\nWrHuckULfAQ7/b0SB/38I2bOWhbcZyieNfxAuhicMZNegtsAzQx7wT57O14X\npUO+kyTIfw2QaF8UrbBkayG1MhlbKVOBj0nss7cmVPjIoKJfRs10FWSIEUX3\niGV/n9jLcFcxaz360txCadh4bwv0CLbiFg/2pFZdbsBxRo00h0IWCpBBWFv0\npsQT0W64GLmTPeOs92ZWJzHDmTlf/qbH+CVEOeNV+zh3zf1CK+5XnR9T8hbZ\nS5T4gE/G8xhQjTlvFN+vjg70+FE1w+HSq/IPC20IzaSoTcDssjeg8eRoOZ5X\nctbqWidxESaPRBwiN2rzz/n/+Ar+VmQ60t8gXQjH/afZ8yjeyLepHVxeFnwM\nF2MDK6261VQjp3oEevZ8eLopifIhYS6l6GasM8QP23ySZZiedYcOVowj37BC\np2nPCNkWKg8nr5KiPP79D0S+mdZQKPu5rohAjXjpEy17eT5CQQ7VIA1snCNH\nN9n1jlEN36kY/ovd5Xl5CB5v0UIlUD7NQAbRIOPhjGDBMzzf6Eqo9gDmlQW0\nBjcl\r\n=6Zva\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.12", "@webassemblyjs/utf8": "1.5.12", "@webassemblyjs/leb128": "1.5.12", "@webassemblyjs/ieee754": "1.5.12", "@webassemblyjs/helper-wasm-bytecode": "1.5.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.12_1528363539660_0.6811103169721242", "host": "s3://npm-registry-packages"}}, "1.5.13": {"name": "@webassemblyjs/wasm-gen", "version": "1.5.13", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.5.13", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "8e6ea113c4b432fa66540189e79b16d7a140700e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.5.13.tgz", "fileCount": 3, "integrity": "sha512-yfv94Se8R73zmr8GAYzezFHc3lDwE/lBXQddSiIZEKZFuqy7yWtm3KMwA1uGbv5G1WphimJxboXHR80IgX1hQA==", "signatures": [{"sig": "MEUCIEZNaH2BRmnF97X7BL3/Q3OW0MohbqdC09CqOj7uyl0VAiEA00A/27FrDLXm/zmnLMWaHaW7ktLr2ppgWivx/OT35wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbN4lSCRA9TVsSAnZWagAAs+wP/iXlx40BgBgBkPxQ3JJB\nkdaULleixaaxOrr43i1MlHsRQWa1BOO6q/NhdiFpNYK33beGQq7XTC0l62Ns\nXORDR+eJSBA5zNGilnB5PhDyzu4yuNEn4QFYhP350a/zNH+ksBLNmGbx2/Ni\ncyrusGa5RYj0SwayB8+FhZXfGefJ7uD7bJOHZ7CQUNSZccFnk/Pmqf3/KfEy\nHk+7/IpSQXhZD8Nzu+R2ukz/A3+OI1WO2jTiOLNYNGpnojQC5BLFe3f0a4lF\nUQB1lzWjX5/NsidOVK0lBSX4Bz6vKG1RA9f8hZPhlXvgo549z9v+o/d9p/zW\nnOnOHNDsgWe8SDlJphApGdBJEM4s38hdc4b6nV67Q44Ioyi2NdBivEQAaocG\nvJk2ih5xLMlcOaDVFPZZIPlmnC9QeTPR6bPtdr47I12U4Ps2fIlAzXfrtetL\nKMTeaVD9h6uhVwH8MmkdYYiIslx9pEJ5MIk5fLhdDjFUw38+dt8LKmOyEdOz\nXAqtP9SjlD6252PL9OH6wG1hsZs6sNks+sML5H5a8+bA/LebKQ6bzxcPhqhj\n/y1XxpqGIsCcjF58JruBl/5KUm03n5b61Orp+KntzJzWpw0SUKMgGF8h/i+j\nIqp64SLqjueqSFpJ5cAH38ezwN9tn0/CLWTXQ7SnGQItvSSVyRVWN6T714xa\nffP3\r\n=EK27\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.5.13", "@webassemblyjs/utf8": "1.5.13", "@webassemblyjs/leb128": "1.5.13", "@webassemblyjs/ieee754": "1.5.13", "@webassemblyjs/helper-wasm-bytecode": "1.5.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.5.13_1530366290916_0.4783819393967912", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.6.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "335bc862a5c3cd5b2a41a410719e172f35b7ed0b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.6.0.tgz", "fileCount": 3, "integrity": "sha512-Od0/VEXtZPC9DykLJJ/vsJLW5AQCZlSIJvpzeRBVvO2GiwcG205H0WLuATNpgRMKKSMPMoC48BRehNEDoWW9pA==", "signatures": [{"sig": "MEYCIQDoP1FsXiEbL6E16L2OHZ9CpuOqo1bDfwjkjvD8ooYdVgIhAPGDYy1OfV+qkx6btv3oSCcxJZi2YeyesSMyLOia9soN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTF/dCRA9TVsSAnZWagAAQ50P/jb67CDAVx/CB31HBNH/\ny05eMKYLrI1iBrCv/XLfn3XyVLDWuG4kIK8e+mSPZDGvL0LLhkGcsvO9SWYH\nA79BKJ5DQuGbIgkg0i/yEMhNU3WnncjVM1csJWmAIZbBylVL5EeNLsPNysZ3\nw0h1LbypNAgDyzvxTjgd4XIZeuoiH2asq1wvCWNLjCD6XBUEDLdz+dvJcE/4\n7I+8Mb3M/+a2hYQPHndBeAKGfbjzjKLJge7ky+g2NoD4KC+dGsmOzLp8BTMn\nzpbvCu/6EaJHTs9XuwsInKoS5MTP7jslXwYVTtHxQM4m6pQjkPBwASq27xBi\nQdRUGflRq1i3/dIZmjQgd1mHc1SCX01EcBdOi4FoGN7J3nuO+XJkxVJsHPhX\nF822DIKPhiDuZnkkk+7XFh8pEhWgACDGdvZujZaNI79Pb0Y36ODEo/CzlfYW\nWbx5RrEaGQYJAAnh8edOTx38SdpvZXLFKq7oSDB23a7vYqYkDT9BEYnTB+Xz\n2jrOdwJV7JvSvUwABMkSfiPipDTUHsqKUvUNHFTUGtgXEMu1midObwdqGosr\n/kglJsNWx8X1Ic7uujcXg9f0N/wqNTpJFcwFa3ebo/IqY1Ay/1gwXRZU0sJc\n8FmWt1vUyNAwu3N330RMWyHBLYcTS0WZFTq/5Cff+9KU+wspgwYnyeuR23Vt\n5x1Q\r\n=XwNv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.6.0", "@webassemblyjs/utf8": "1.6.0", "@webassemblyjs/leb128": "1.6.0", "@webassemblyjs/ieee754": "1.6.0", "@webassemblyjs/helper-wasm-bytecode": "1.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.6.0_1531731933802_0.6197447101896707", "host": "s3://npm-registry-packages"}}, "1.7.0-0": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.0-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.0-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "6ed9f7c5516d5537e1243dd3863a0c073ffe0a7f", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.0-0.tgz", "fileCount": 5, "integrity": "sha512-sbfs5c3ze3SihQDI7vT7/f2OE68ujYDzNFfE+ODYWtIPKubQxED02wNhnocYJGG35Pe36u0F3Olm6eZmc6PApw==", "signatures": [{"sig": "MEQCIG8J6iuKKIwascdcinxV3q7MceEZOJFx5QZwc7tYZd1UAiBvwEYnXAvR6yiAvU897lycsahYFsiDIUAJXBhGGrHq7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzlsCRA9TVsSAnZWagAAbpkP/1pJzoA4Fr/hf435NBW2\nhm+OOFMSUgFmMz6cWfNc0OwN0Fp5DNEyT3FKKm86VGCv1+GhLv6XwNS/YC+L\n8AILKXuyyWLgTaDlq/2A93h+5sy5AlMH15Mwl7tsT63U7B/Wh2tqxgZXPDbP\nIasGyvvbPGPltJCFO5+ms0eO/Q8nApX9NZ3wPR8eSFcK4AT5IGOGN/aKwn/7\ncFoFctu0YoZZXvbA9JHres6hN5epknX8R/9/xEURMD+5xAoYIXLdTaqqJYxQ\nuOsUDAnm4ZNgmqzlO2B9u5Eta79/g+yVJ/R71mydwx2cdJHQlMyAL9ZvYHqo\njFrj3iddzZPKOkfFwyxgmixpfy/yktsxiZvw3yaoaVfPGFkh/DDrwOkUK34c\ncR+EyD7+7YlhZLjs5y+BTzzwcF1YogHrl52pOks6Y0WqwRGFQPni3jKOwLSd\n07YY/HYdQLsrpdx4SwKnxIbXwF7i5pjimrx6Nm3Oq+KJi7ANDhj6wPehRlEB\n++h1zInrnkz5igwNkI1BQ95XkWroYCPeUfy0lC9Lfc0xbXaAYnUtgT2R6neK\nKZaY6bQ0SkBDLC2x3cw170iI7W8PshWEuTroyP3G31c4mxwbANKRyecdGFCQ\n4Oah2OgFb/KdFHwRp+EIwqvj0Jt0xRLFmRbBEDkB/WTJwevT8xZVRRbn8qMN\nIWpc\r\n=I/2H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-0", "@webassemblyjs/utf8": "1.7.0-0", "@webassemblyjs/leb128": "1.7.0-0", "@webassemblyjs/ieee754": "1.7.0-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.0-0_1531918700682_0.*****************", "host": "s3://npm-registry-packages"}}, "1.7.1-0": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.1-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.1-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "397f5d635cb5f62c6212f37e4bd9e5198acbdf49", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.1-0.tgz", "fileCount": 5, "integrity": "sha512-E5ZWKjhRvS0mOW8I4XprXl6+eulvnagA2gkQQOU18JAaS97DkES9exeH8M2vfPNciXhjwdwe7SMchL2S1YWF/g==", "signatures": [{"sig": "MEUCIAKwB2zaPASOAbiR+3UjZm2fCb7ylqgfYbNQEQ29gQStAiEApHdqAC20PIQYTjgoK4XqaeC6ds1I7M6BaeyCjrmO/Fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTzwWCRA9TVsSAnZWagAAyMgP/RYRSxRvlJu1MAxnnCzU\nh45Lw+vtRbE7gfaveQfEJ4zdtlcBnGzrxcC6DXzIGSy1YYZVRQR8lxRQutvV\nDOeNfmvsGgX1/dLGAcHYWz6zCTdc4Iy5TFrA42A6O+epgsGdJkvKcViNOa2w\nJverU+BKEGURALZ3ItpIAJw+0tEHfMNt7BD+tWTfgx2amTHNqpqI2e5KoqZf\nF6vzr2IK51fBCAEfAUMwv0ZFBqJ4eWmG1O9zPpRNkRzC+TZSpsScnC8Myfhv\nBZmbafkjXj472Hu02dhkF94OGxbOSHipfejzDjlszTi0rn1+Ah1Uc6QjgDNv\nzGVF4QFHqGluxEHJxt+/yqf0U5enjfQqMSmJuzDLka5SKtw4sFWhQZoECjIN\nfdXqIdDQu9TlFcoEUzajzNg1Fem8xRLZPzVrJOdzKdTRrToS7LpC91OQHkG8\n0hdTGDPR+gjuysYgvrhhljOH/cEKMwy3yPpor1LB6fxTiN0gEjLeINItmnue\n45XbghW0g4gCeFpxqZpu1fbvae+vuq/ZOzrJg7WRo4NsMAeQPYaC8kIqt35/\nCX6uPo48M/fJjBSDulqJD/M4A5efj6F2bEa4T9H5FtPRLRUfUtb0PxK86Qv3\nOeEDO10N20vCeW+iUvSIkhEi4PxbHUAt8cu9/7NnIEGICelOcFLdB6pIIEVZ\naQk7\r\n=R5hd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.1-0", "@webassemblyjs/utf8": "1.7.1-0", "@webassemblyjs/leb128": "1.7.1-0", "@webassemblyjs/ieee754": "1.7.1-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.1-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.1-0_1531919381548_0.08001852160070144", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.6.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "434ef3383597b1232b6990e1766e2605fc24c9be", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.6.1.tgz", "fileCount": 3, "integrity": "sha512-yEUoYHcEA5JSjfgYW9jzAQ4wSkzH0Z4glFI9B5y/WwxY/1fbKArUFTlwQjfn6/VSZFPpO2mzZk/wC2eIFfHI7Q==", "signatures": [{"sig": "MEUCIQDAho5Jpzy8hBj2Du0GoQP3ro6XSicDV6Ei43AwShSODQIgetP9vXpWCfSqvddEOfxlmZJ6OmRlYZyUKPk6BGp+Rj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT0XGCRA9TVsSAnZWagAA7YwP/0zaE7q5b/3Zkxq0tOUI\n4VQlREt4bFFbL3NpJrDSd9zRxkj5KM/ITK20KO4dRFycCYmKt01TjonV5q1f\noNuiL+E2BvvINiXhoHodEWO5cBEFGMCe2G0O6MAkBj4IkMeTGoFeVu/NGEyh\nRVkJAUiNXpKpSAVe/58BtJlMOe+txf1mpEazNWiF8euIN3EVD/Z1Q2UH93vt\nSEEvYUsCno6jnKzk25tY2/00npsivb9Sz45yLeofA3WhtTVan4UFq/HdyNXE\nxUJ8ME3bQeCf5yCcw/6IyyEKGdf/DaJ1E0DFI6gSRleGMOJza+Hu+z4xnYIy\nsu2gjA0V72tfVenqYudyg8r9z0/chY1lXsUqVTshTcQLpGRvn4ehCxK+dZAl\nfAoTC7pwgItpUfv/yvFPRZN0fhGG4ERcwt1c5SFGhOhi0+XFyEg/Ot9sigLO\n6WzfEbZ7710GrinPJvSPdXZWLqOP8xmuWGjWorVmfO5pMNsfgaVQTewPWVf8\nQpADCu+ZgBuaXVgjdkW98Iqzix0Bn4tsr3XikV1z14pemItEBjcUNlzTFHgu\nqnBvvwxlleb7JEjtErizohd6Bekyg5bWdlqXSm3FPH6filNkBXfFoSez9NRA\nfLeiDshlng3zfN1M7Kzs6T556sv4E+pN5VinrP/re6+vwXwx1UAs5B4ZSNhi\n7fEx\r\n=mSvz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.6.1", "@webassemblyjs/utf8": "1.6.1", "@webassemblyjs/leb128": "1.6.1", "@webassemblyjs/ieee754": "1.6.1", "@webassemblyjs/helper-wasm-bytecode": "1.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.6.1_1531921862184_0.33620442584009247", "host": "s3://npm-registry-packages"}}, "1.7.0-1": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.0-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.0-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1a8a4bf7b7bb6be4b5f2589dc132ecb7bca329d0", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.0-1.tgz", "fileCount": 5, "integrity": "sha512-s7+xZhkVHJ4E76/wUjkn1tga0EG0QizXdH4GFF9wAtQrgnc15YlPQrEEaG47b6nLu5kak/ATPPWk5gKhuGMIPg==", "signatures": [{"sig": "MEYCIQD2p5kbeLdeOtxiGvepgxMdOxj88E4twMhkvDL55mbMkAIhAL897EY+J1KncXn09U+7SJQqixpQKtUTRvf7/nB9u2wC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1YOCRA9TVsSAnZWagAAfpsQAI26aIScBtcuUtDOyixX\nQrpxKsfBHlCuGNsPDzLbY8OzRq/ym4h3RJ7477d0H2cCfS9wCSYAWOU0MxnB\npuNhFEw9vmstcAiiap5Gg1CZmJTskMgxDIdN39TDEv1tZJF7mFYL/oZc7p+s\nS3XnMKXCpv/61gtZfHfCpqyz3Iiu+CGXOH53hV+3oJTqxDwqC9PxaTeaob9D\nBcNVqc20+bkPT4KpNtMMZsWZdnlt84dZsBYN4riDnt4hCM+EEtSFQ8h/r8EX\n39KpNs+9jFYxD6sfBIXjJ150IGozEes446zwZO5qF7QX34V7lQ7xmdzyZ5IF\n9OkWWl1fFjial1d/bXXB/EuqdnZaoQrzNuZ1D7sp0VoR5lJdBTTvyq9QXAg1\nL1YpHVpL45ATRhTgt6q0ZNySpyRKctrV0VipVHXmobrxzJozJJSkklgUixdl\nYHk2dvSwX532Bz13cT3DrUe/3o+evbgFrkQsWNSoHID6K/aHUp0Az0j48OsX\nUR/1YwzISz7zDSulG45YOeNWEoJLeh0uo8ba2uRExSuDtDQlaij4Razy0jyW\n4aUR4CLC2mUquI83lqUcXgJlkTO+xwFU8DpIdf2MJeJVxu0P2cR8Qf/pv7kB\n0f3Mba6Lm6jzaoD/Ao0nFbNum5JFDeSoxw/Bn4EdnO1eKXwsa5tqNxz35XCK\nd593\r\n=ntdg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-1", "@webassemblyjs/utf8": "1.7.0-1", "@webassemblyjs/leb128": "1.7.0-1", "@webassemblyjs/ieee754": "1.7.0-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.0-1_1531926030630_0.167055140460034", "host": "s3://npm-registry-packages"}}, "1.7.0-2": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.0-2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.0-2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a74f94746808a166b0c9061aff3e7cf3799a29db", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.0-2.tgz", "fileCount": 5, "integrity": "sha512-gtDMTlhnj9lTAPl3pDmk5/tfjuTOsXhp/MRdqWom+UiYZxdG/9y1XSyvrwl+Fc9QdUTRpW5Tn4HDaDPdJJm+Yw==", "signatures": [{"sig": "MEQCIEi/fYmPkrgsh+fHJMlv4izboWAl8Ur/YMB8mZvPPn21AiACbPXNzA8axAtwDDg05rvtX8/DapngePtoBImIL7UAsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT1mlCRA9TVsSAnZWagAAb64QAIg9v8DbVm/VZdT3Pz33\n4XwWUbnJBXgbMYTTj3OWdfjlZiu9pJNGZKvYBYAGzyQs8T4pkt89RSW6LntO\n2JqV58LNc5jBTnzoyFqhcNFMw2QBiIEHt6L+l/MMwriF1HSPvMwvaBRYoT5u\nwCj0+aKmL2IrsJXvkx5ikQIlxqCZZHVYB9Rajcom4/MicmE41eoOYS1WN6IF\n2s84wRqrubdFqHaohMRl4SPviknMPVY2Z6SDAVe6HkbwnzyBKi6PYmAmJ4SR\nTefQKDR9RHMc0xoskKKpNDmSrhVu3B2cwKUfACtiYdipdSKwpFZzwlGSokA1\nuk+OVLE3V+KeeWbAA6Lk4T1J/hEOLpbG7C67B9NQpRPZFnHy1LBIX31NNtK6\nJ70aNXON6L6GVvr0AP9eVVPxroqPS9OLK3+nazRBz/Vbd2IwHVsCzTd0jFG5\nGXB4PwoA/Z5TAo4ZJSg4A5XQsHnNgrfJxYBJnkucYlLt6gFZ7hqz+c+KnmWq\njh3RgoXK/d8cEo3tjS/Ygm6O5hTtrd+Wjwksr5rIPGOVZL3/6B4EgO4GMxt5\nstHKg13QObkx1fuwvXrUJ3X+LKL4ZfeOlbM4A2hHHsjQbxqP5i5rhE+1imlx\nmWNjtLMC+zvaY7cIypSqkR9Azz0WGmUhDzZS895OBDfO+Xd5146yGGE5kp+J\nbw5g\r\n=bNL7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-2", "@webassemblyjs/utf8": "1.7.0-2", "@webassemblyjs/leb128": "1.7.0-2", "@webassemblyjs/ieee754": "1.7.0-2", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.0-2_1531926949602_0.9150052233258315", "host": "s3://npm-registry-packages"}}, "1.7.0-3": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.0-3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.0-3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "1b362e6fd94bc52f4af5214edc1aef69cc9d819c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.0-3.tgz", "fileCount": 5, "integrity": "sha512-bjMQ<PERSON>+/e9Ry5zKcHPxkGj+mdaLScl2InX3UD2vKEyX9wBGB5IucQL7sJ3n3B6CXswQT+y9S44uIwNzVNc+Fguw==", "signatures": [{"sig": "MEYCIQC07jDft93U0ugSRIaeAPnCiEarA9lNwNmcmACTCxkWhAIhAJIFmLVr5LTCnywW/P7Tvh5mLIz0AkqtXKgmOD4Ohleo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbT5FfCRA9TVsSAnZWagAAhwQQAKLcjcMZD7TvC3WB2k5p\nhdNKlnvYB0MlJ/VL2RiojxscFdWQvR3gugHpXRDgw4OlNpnTIhsOpcJ2JM8F\nbZAYiekOmvqP1XeTEQWjDAo/HhXPN71hFCVlyYVi8WkCbM4JfkFO7b582TMT\nclZl59ruzakeuyIhnOYc5VGW4itxvnYe2iJfUftQAkc4877KV4ek5gjq2YCq\nmxCJ60gjRFgS+0uyj8TU77P5pHkNUn9ByLqLCumH56xPk6Zh/Bbws1QAjB7S\nXvwrUnjsEjOnwJAgiUliol5TF9EH55HP5FP24um4DRAhpefAITthzzBu5imJ\n9j61u3PuJ0MbazZkbz2vH1FIxg+5SIzI/3CDGZEpy7jGcGicozs/G2NnWUvN\njMqSYLH29BqP5UsfJJyWrNMZHFIn04Gw6d4U9uwuOYnnL6Hla0gJnD/ejO73\nVKGKdgBQwkJIq+cBWSDtOXRZjB95hqtjy0eWhpPwnonINywb476b35jEaF5e\nwWDp5+Szan42Rz2H2ELW9JRL+OpCnuY2V/PmU76bPtsTFfrA+5uUJfeFTBPO\nx5sdbeqSJHyQW6LQVd8LuLthWnOPFBaJ+M96dp05DdbmUr4bHrglOkBQIGBk\ny3dPBe6eJ9sQJxN4TuHHFBmCDy6U088nGMLL6QjPNBnd5mnXDzgMvXSRmL5t\n2pHZ\r\n=rX9K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0-3", "@webassemblyjs/utf8": "1.7.0-3", "@webassemblyjs/leb128": "1.7.0-3", "@webassemblyjs/ieee754": "1.7.0-3", "@webassemblyjs/helper-wasm-bytecode": "1.7.0-3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.0-3_1531941215802_0.08034455956353326", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "02a835faa93edf65d6cbddea0054aa62bc86fce2", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.0.tgz", "fileCount": 5, "integrity": "sha512-frf3jFNwt21BCQDaYB1mlENqlpc8SuRUCUXyK64O6xVE8nWS//k8KlCgHyxRmYBXSxPZWgz12iwWbdaLb6P1/w==", "signatures": [{"sig": "MEUCIQD+6Wih6uw4y6KwUHnMUVxmt4+Yp/v+HegGD19Y6TSOUwIgAucUtGDFJeCtY5jnVvnmejAw5TkAC27M0XbV2aMXg9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbULXPCRA9TVsSAnZWagAAM0gP/jl+KZMg2Unu9q2pW1Bw\nYk8SHk8oSsVPkFJClVu0sEuD3pc4L2LfDIL6bsAdaMjwKWWT0QbKseet/aue\n0g3hwxwXEG6MXOgB4QNyB3Gon2JwjI2wdcspwSNNEVZV2Ph+gN2eJi3dUb5i\nBWt80SBY+/LxE5ayVZQYHlkM/2BcWRlGjwYFQKupwGm4uliZBe+TtJsO/Zqw\nPOR5pSmTa6lpQmLL6xquwwuy7zIvAHTuhGqFWfMA8l4rnrARHPIzzyK/I9A6\nD2I7Bqa/dx/PyAJhMpuv43xqJoyf467h3FNsZR3/r4bdUxZHtULMhJsnkKFz\nvGDbO9AShg1zBqCu1Vffx2gpS9do8zCVdcKeY7B8/NvS0Zk2UJNjLJDCgoI+\nMOHDw6ToKujVSMcLF8cEyW9jf/M4aDkJypuC4yg6Sa2PYfBTeP/YH0LWlvUF\nGSX49ovicvAAQX9tjMkmqPnmlMYZQzAIV5aozNLM2bPjuvwau0kjQK8gOGWG\nA4W9eIufAy1rS3YYwJHzV8wOqKEGauNl68RzYu5Z8sz17xRzPJP+4CnJLVbo\nALW8YWadtbiA7Yh/7LogyiJRMVKcqguXEgTFuP/WpwzyS1wSjhPUNKd10JMu\nLmRatfF/P2n6oPppN4OsoMy22npx+3nMtaAfKZTo1RDcq2Vb9ZLd5pNcbAwb\nE5DT\r\n=u2/o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.0", "@webassemblyjs/utf8": "1.7.0", "@webassemblyjs/leb128": "1.7.0", "@webassemblyjs/ieee754": "1.7.0", "@webassemblyjs/helper-wasm-bytecode": "1.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.0_1532016079728_0.8983422547277702", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "e4f3bb58cc9dc6c59bc895423d7cc0110b3511e1", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.1.tgz", "fileCount": 5, "integrity": "sha512-1Oz6aMpHxLB23TyEtvF8IwyaK1wGSmpNgxm2HLoo1yXBoaENGtDPjyx87gxkoXiWj+2HsXbF19Op/39mmz+rsg==", "signatures": [{"sig": "MEUCICtnIinDptuWggqeTBt7iH7EBl63Vu4RmlruzeBCAij5AiEAiO5Ml/CmsnMunlA6FZOZbnB399bFc7oJYYceyEghaC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUL0ACRA9TVsSAnZWagAAS2EP/jimTHyzHSQSX7sT0l5d\n1BGApWSsFgZo6bim041Roo4qSZXXLVnVBwMsCBIXxnWOkAiHQ6EiEIOJu25R\npoxytFZiTnePPC7m1YPYySQz/JhXnwghtD+ANSLInR6pyBOnj86JVjPwdpqA\nWhZi2rnGylX1JBEVQ8i6Wm/jrPBkgGPNaERuHnBss4phyokAj8B8md2gVKH9\n86nLQc+sIPLkvdHEd0AZr30zQlShG6wHhAnv6Q1akwT4oWzb6YJUIr224AYW\nu5VqDrhbqV6PyUwDuyWwFf3iVWGWS0/MeEVxq2TXaQtDHzR1ZtbqK9Cn4lE6\nbWMBAK/wgrX0ty56HSbHQXtWoHqNp3YAQAmP4xoQASlv6+qGeLDvW7lJrWaS\n+gx9i4rs1HWl8qPQ25CevMgKk6TYS1+f+l3pW2ntKryoGl8C64HhOusvHTei\n5x7KZwwYBWyAkzth3T4hDVhG0fhvatYXIcCgaeeqCjQaQzlAu/h9/UJdm6IB\nnMzmvDI6WFKMRTNhLmFMqJpYOxrzUV8HyxokkZBDWh1EnlGCqpoKGVjXgPkI\nnqyzgWVedpfOtPKefi1b4vZb2dlOd/okTZo2cFhprLXrSAlhistKvUQv/DYG\n55rmXr7DJX7vXySyHT+jiJGTbwxrRx6Gz0tYTdywDRZ+wC1CpXXTjLRU2pxQ\nT2Oq\r\n=hzWg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.1", "@webassemblyjs/utf8": "1.7.1", "@webassemblyjs/leb128": "1.7.1", "@webassemblyjs/ieee754": "1.7.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.1_1532017920625_0.6620865464465981", "host": "s3://npm-registry-packages"}}, "1.7.2-0": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.2-0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.2-0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "4587e91ec9a00ced9692eb9baaf641d3fb846423", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.2-0.tgz", "fileCount": 5, "integrity": "sha512-BtaU+pBLrhbWQujuQt0YC2Vvz5LzmrqZS0ok5XcxyFrMswjINtWv5r4k0a/D+fqJZZ0rD3u32PohBrZtLxHZDQ==", "signatures": [{"sig": "MEUCIAqTOp1R0cX5revlo+egK8vtjldzrWLWDsOtKqP9U3+RAiEA7ZIoGUErVXFgdT3fkqIDRDEDTaaoQh09je98o2RMn6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNP9CRA9TVsSAnZWagAAHMIP/1vZAR20sfNXNCUiPwo1\n2FyXQiVN15FS05ha5Vshf/IooQyC15YRN6JNXTX77+nHla6qKdzxxeU5qclK\n7OwtH3XAvfO2uJCGEgGze9cwaziOAOSMOpYxSG0Ao+dPoLmz+3ibp1hVYH3Q\nCLiIqw6yY3pBH3Imc9o6QoTFSC/4esfjoeFIVJl8ZBWG/qjEqZBw3wunf0HC\nNodhypSLhAVPVxttuo2HZeMVrIIJsNkFYwdXxmSXdwcsicB3Vs9zA08RMdyH\nSYBkWQQ2Y2Rg9y1BPnWpXmepsNGuL/9gtvyLmQUNqXIvr6asFjFaT+d2uymR\nJDMb8vDZZ1nfWh6ZI9CiDrDex3Vl1A7KoNUxx4xU5ndASbc2yBYHRgTlaeV/\niYEY7SWFLJe0m/u9Ehn2vKyThd9FBVnnLAq9ZUot4zKxFI+qUL5kzjytmLwv\nQaLEAyPmiIn2imS0GJkHRAE4iUAPqUAHwJgDeF4E/Yff0jYLIBz7V3/5SFPa\nCHyo6grS9jjolRrVLavZ5p/nH8EKgW011O6GvKhsHm1+YPyobfu3Jl1lzw7J\nLfGiMxwZz4GAwPhqKutE7YAProRGzThdrzTx0cW1p0y4KOnhLoEMFmxjFrIK\nanOC90vFTWI4aO2c8aCFt8af1p2qsihL5HrF01ls0Kt7ZzWMQ4T/egOLEnKp\nb/L7\r\n=+oLU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-0", "@webassemblyjs/utf8": "1.7.2-0", "@webassemblyjs/leb128": "1.7.2-0", "@webassemblyjs/ieee754": "1.7.2-0", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.2-0_1532023805485_0.8199997749833265", "host": "s3://npm-registry-packages"}}, "1.7.2-1": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.2-1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.2-1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "df16a576aea919114397d7b94209192c769dbd27", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.2-1.tgz", "fileCount": 5, "integrity": "sha512-aAd/AXNlL0ElL+E0ze3s/SE8QlpLtV9UnzDBOGVpcbqqLYJKyfqDxcTI5spMu0FCLqmsIDJrBEeWuvOATGu40A==", "signatures": [{"sig": "MEUCIQCCK7tUzgOOaakKzfSlNArWJ2JIYqNiBLn7i7zAF5YORQIgJfHGPX06Zie6p32gWWFZZIlKcUwQv3VGUpfAklD1f/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNd0CRA9TVsSAnZWagAA6fsQAKQtIpDChjZMrsvchAS3\nRnFgFibtTWb70Gdlz/d/QWA/UXNFYAglvPz1njDkgJQ9C/Yx6iogtJlXYuJm\nV9BK9qiKtDRuB9KBo4l/u+DqronSWVOQ1SH1m5B0m8XeY01LrRTZm+7c3XxH\nf1u/mstkZoV0wWGPbn+URFZ+H8gOiXgUxE0OEBx2OoA/kOx/vKUlAHc2VJUv\nRxOeHYDxeb1ByG8FbOJFqky0MjKHNRqn59T/00HCj5eG8LOZhPbT+zQc7Ohs\n90tMDERtuumxwkP2acisyRVGn01U5mbVSJJKa6d9ZZvdfFz56XuqWK2gnSfu\nCiHQT7R3yfAG4y0rUVId3+2l/6qzTjDpmTWsV3y3GIlXcM6DNvLN+rVqA9rG\nbc8T0gClKAu4tcl0OzLtaWcWqWxF9qqHp+CkhMUmsxnt7wkb6E4L+rguD5Xi\n/RO6SQlgx+cQ0LDScDLj241qsDDIriBgqRtr21snv8biZD+YDXS6pu5b/hPK\nm3lvSEkfCD3xOxOxLHg7e6YnJBsALDUnade0yQT1hPr1GFttNtxqvYI433XO\nWI02dvoLeQS7Fms5IUSqJ768QHVE4RVe0tVqCPwKB/fWgMRAzLqlHLZnsAq7\n2PwxaBsb4JrUbix1JSwih9ajr/VO4dIZcgxyRqHQR1x+uiAHimVNgYTCtWhe\nPn9s\r\n=lreo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2-1", "@webassemblyjs/utf8": "1.7.2-1", "@webassemblyjs/leb128": "1.7.2-1", "@webassemblyjs/ieee754": "1.7.2-1", "@webassemblyjs/helper-wasm-bytecode": "1.7.2-1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.2-1_1532024692662_0.04866239802658323", "host": "s3://npm-registry-packages"}}, "1.7.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "68bc2910c438e038e45857848c736b528848c99e", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.2.tgz", "fileCount": 5, "integrity": "sha512-ybijPDQadYp5GoHL99f1V9nCJoV+It6PdbA1pr63N/ixv9X+ZCqdorxFH+eXax5aTujjVrJrnpgJ4O5cV1GUbQ==", "signatures": [{"sig": "MEUCICncLBGwqmXS3fzpcr3FPRDAjGH8dNOOp/TIO9+xWHnJAiEAkX+nehUU2JCHoWuLV3kKoBr7jvT9PA1xV/0gn0eZmio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUNmQCRA9TVsSAnZWagAAknoQAITdRLMifEcaw6B+bUSR\nNSmMuO/XZvtxMRJKtGZdjYxY6ww4XRvF7Z4E+zfuIQs41gCaD+XzzH/1B4/y\nrAWz8HEruli2cvHwA+Tq7et5LMNgGwOnO6/FXv859lrI5JyFfsjwNZYm5fXR\nfirBgcVNnSDcDZ8uSJC532OiG+Bv0RkMrF9pfG8c6ZQftDtUalxlYM/Hqg0K\nSO6TZhcuZxq0k37AC0zQZAoCvmpmfRT3fL+zoiIdrxr6NAFR67kUyTXsRLsU\n4cRIGz44fp/UIpLES8N+b0C9wSXetSx9TfJK5/MlwA3ZzZUq6nuhJqLY5E+8\nc0vSFjf4gOQY2Devpk9SshOI4NBpppmsGFve4r4JZmKWX+gKHZBwVpizJAYT\nJDgN+61BYXp1MZigaO8pUFSpROqyWuxz2O1jECKrT/IlvYI7Qh9ZkQI389Vm\nEH/JtIUPwt4+cE1uqm1ANzbeF9T1Qn30kcvaYrapg2GCYCWpP8wKTzTGZvDB\nTwiV3EMJjAvwcA881Kwt5oIqRwREpNjjW/oNiEBxqC3esCHDBxlfCX369g9z\nEXoyl7efZ66jQb0OFosgdzw2nCPGbzTdaOABD44P+Hgu9fSiPQWeaTXO6HES\nbB6lP9su+1Yj+azE1vUAgA70PSSO03Zmbe/QDIWqPbTguNyUMxW8t5wFZ+18\na+TD\r\n=uqzU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.2", "@webassemblyjs/utf8": "1.7.2", "@webassemblyjs/leb128": "1.7.2", "@webassemblyjs/ieee754": "1.7.2", "@webassemblyjs/helper-wasm-bytecode": "1.7.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.2_1532025232775_0.8647874720286297", "host": "s3://npm-registry-packages"}}, "1.7.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "05c45b6b50495b32fa737db01d28dca1286ac901", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.3.tgz", "fileCount": 5, "integrity": "sha512-VfZ6v8y/gEse0kNI0FyxKSgeqlcpyaWV1nO/qoxMQjTpDY5x6YS6po9BEgrqYN1/dcvBY0oEDnoN4g/7w2buYw==", "signatures": [{"sig": "MEQCIHzyQXIgu9KL76NP604y5Y9xNVTJFIcTQLJ6MAZ48pwVAiAQaojycP5hPjFt8W1M5eTLr7R1qSYGRDyxEcRcxUDkCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVX2TCRA9TVsSAnZWagAAoIsP/1jAVh0ugmnSj3dASvaj\nQL6CR9aptsfJN/5WNZd9E1xAFMds/uJtml31CZ3YEaX5A9l2OGKKwC5olcXn\nwoAZa6ODeKsTEZmU+h08p2bdf9A5ar6ht7iEBECBLQ/PmFmlHgDpbpR3Uj/T\n+dpQcFml4yCKmQ/c+bAHfzPDfPaSkK0W0Qc1F9VncZr0Mu3zbrajWTYcNn6R\nj1Whqbi9BEIan4HzPl9khvPkXFZa+rPSv3rfgc8Yxu92EtgJ9vUz7+7+pExS\nR8LiskcWus2Rf/juyUBVRuEYywto9vF0GHOXh2JSTdPoVXlj34QhoNYQYcrv\nj3vnf2l3IGhO9sC/oeGG/owYx83p2LlLna5yTIhbHwr6aKsS57LuN4NWMr6z\ndHeNaMFx2WDNz9NnElR/xB3j6mworPaRofoygqTQgq8gs+Vn5QbHA6I0GHhA\nFRFvRZZpT3mO5PlWg4vC8ak38ENe4elMk7EYvsp0T5Xn+wQYlXBIU8oGHzKQ\nattZau6JHSQ0Fy9bLqEdCi1mbRGmUinQWFerqkfsTgVFUfqHsb3uwKPuwiFg\n8sNow6jXxXBXtsaKOsTR8ceAwMEXQ0oI876Ah2YvZD04fcDYIUNfO4U2eM48\nADR+XYUQfEstsN3bpfLBSh61MZcXxGLS3hFHeXuDxvm4E+jT/NY1B/yRfGNz\n02SS\r\n=Gd9J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.3", "@webassemblyjs/utf8": "1.7.3", "@webassemblyjs/leb128": "1.7.3", "@webassemblyjs/ieee754": "1.7.3", "@webassemblyjs/helper-wasm-bytecode": "1.7.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.3_1532329362973_0.320008384665317", "host": "s3://npm-registry-packages"}}, "1.7.4": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "15cddae961bbde68661b740308cd188e2b93318a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.4.tgz", "fileCount": 5, "integrity": "sha512-h5gl7yDozW3rSEODe4Ubog9WrTcDGbSsNMV0Nt4VzvGkouM8ol+kpTgwZ7kqL29OhjsEldCaJk+tzqS/N5kouQ==", "signatures": [{"sig": "MEUCIQDQ92ku2DbskenQa601sMELH4EUl9CihVbcJ9oKX2MkkgIgLZMhduQuBaawlRS2GE+tUHng/1jH0I2VRtlRhPzVHu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbVs8ICRA9TVsSAnZWagAAqs8P/AnbrQ+dgOknvpspmPgx\nE7JdUlAnmW2xLKXAeA7PBIpvNMr+CRFf8SiOksNH5T0xDixrBZt52YLW7Opn\nSSGp3Dki3P+IfT0Thu0Fah30pzQWmvqdl+XgbBCL0Ld2feqb103jD8bMsSup\nXef4iDwUahyIu6959Wf2lTqEkA60Xa+qEmcfzLW1yPtaQnzaIavYxm6GooNQ\nf/GDJMwrt61D2MSx/MAIPJqynq89X8XqKdsYtT1pnufNcd9+ach7UBNazC8z\nrXc0NCKXw5UUVPFxGI7mgmx83/oplG2oW74BuzEOQXLGTR2JAxVC6HalxaFT\npcSD7cjT/lmmE/sOz9JdBs4OLTqp1fgr2OHdzryRBb1YO054CM7tyGa/FGIt\n9Qq/lw7wje+3/pg9tRzhZwNWpeJ04HJJwun3Bn7ykw+yekPeYUxTsTwrviVl\nkVHg0FlV7VgBh3WYV6wdAvL/S9IMcm7FJt+ppjt+KcwpUYmtj+OjawAwI6Dn\n8kcV5r+nf2Jjv44Cv87Zrx6FeZ+etd9zBoNKw6n+mOOO8bsdNOTmi6orbL3k\nLqqTW1+y++Bog9fv4KxTcdXu5MM3ZAezDlc0RBVE0nK3RiS9E8pxjQRCy/64\n60psdgseeuspdOmdlMNc5vs1QJOC2FeyFib/rT7Aym61YINZ9sk0ETpYnzo+\nD6l/\r\n=uZQ4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.4", "@webassemblyjs/utf8": "1.7.4", "@webassemblyjs/leb128": "1.7.4", "@webassemblyjs/ieee754": "1.7.4", "@webassemblyjs/helper-wasm-bytecode": "1.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.4_1532415752916_0.12620521899555937", "host": "s3://npm-registry-packages"}}, "1.7.5": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "de4c8772346851dd43654e5a5447a61fd63044cb", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.5.tgz", "fileCount": 5, "integrity": "sha512-qKlkz77qS0qIIh1KrFfUaqx4IDbp14lOPRSy6HUIQ+jXxVRqFWLfZ3opdpSvu+jHpirr8/t6oCyditaU8fuGlQ==", "signatures": [{"sig": "MEUCIQCm1wVBlucZX5wI6yO968lZK5k+Q/UdJ3ThGn5kx02PRQIgcu5pznDMJbT107j9VWe3UjD8mR21EwFhQfCKajm56oU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdZAoCRA9TVsSAnZWagAA+okQAIzgDakCNEvk3A/qjLHb\n6rsp7jphLfOtGNfdKY0XUtHp1q+LKBd/Y9Ks9V44YBS8v+zYR7VsPBMRG2Rc\n656/Iq4nJalPovnrIa+bzGJCRXOWGOSgb7lY+5Pt9vN9hsKMNpFJY8INmyyB\nfIWQft3ssI7O3lKx7MIUKxpYWkejduq9Iob7AmftdUvHrS5+p1zANNrxjial\n/f5WTDSF+t5SOtdDtwhNma0/joY8v3o5DjfFOP9/I/Rgmtw5epV2rHn0NKEG\n3+NHchOPwOruIYffQA+Z<PERSON>+cRwynR+QIhzDkHUM4Ox3Fn5J/pSAvq1Gjsy4Ex\nPeNY3dNOdtM+ziV8heF67KYlA1lcJm3C0C2UGRm9s/BwP7eHUL2ipUCaOqhh\nLJLxV1DewB25hW/bdMM7JTLh2g296Jj90iDenDUsWSlrJ/vtUF8Jnq3uZ1KW\nP9zFxWIv01iS1D3mFztjxv/Zewm31dW4XNkv+ZUyN48lrlI7J/XkAHETShnt\nyTEBehfqHjp3Yvlaxr8soqiqBLSfDRPUi+xAu8JZIA6gDE6x9aVWd91ygBYy\nr/qclhQcvTJcz+Vzz1KM1NF2USr9op9FbS1BunrYrSSzkJDLs4R/8kGrrn/k\n7r3SEBEihTAuAe5gLaz6fhLaAOIfnA13ySPh1z0wW4tB+MaLtBFMfglQ6LXk\nuTLy\r\n=L5jw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.5", "@webassemblyjs/utf8": "1.7.5", "@webassemblyjs/leb128": "1.7.5", "@webassemblyjs/ieee754": "1.7.5", "@webassemblyjs/helper-wasm-bytecode": "1.7.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.5_1534431271681_0.7668910655881886", "host": "s3://npm-registry-packages"}}, "1.7.6": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "695ac38861ab3d72bf763c8c75e5f087ffabc322", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.6.tgz", "fileCount": 5, "integrity": "sha512-mQvFJVumtmRKEUXMohwn8nSrtjJJl6oXwF3FotC5t6e2hlKMh8sIaW03Sck2MDzw9xPogZD7tdP5kjPlbH9EcQ==", "signatures": [{"sig": "MEQCIHeI2+3oxGVTM2rkFsq8SG4floieTSRhVUvfbiG5TZyvAiAcMhTkDMfgAsgldEHEqKoahHuWznpD4iIuuKYJ9CbsXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbln0ECRA9TVsSAnZWagAAQSYQAIyMM79cv2cacvyRYp4X\nNJr3CTQ4FI1G1Rn2dsTw3ixiOTZT/bsYyvLLAEqfePz5OSRShRU5hUeWtRxG\npCtI5Yfhg9YBTrjj4XkSuCoSWID3XLuFPmTlFDYAdL+ekLC5tckNAZAs3dWn\n+suYkmEI3BHhlg56dpBP0dOTPtRYqLmyp+qv34fhVQejuZXVMlUkG9Uwjf8A\nMj3n3BF5orL242DxQha4VSKDFV8mweQSXmWkznSjGCTKwHxRgeTiq7HBb/+d\nUdaXjQ1pO8atKbDAevYKV7bAazHcQ305WCZB89yid3ub76GaYyT3Q475o8Fh\n+W0CbC06zTo92uWKlr1CXE1xh1gXQ2NfL3fbbmjSsgIBB2AkwCcp6DwyT4fW\nEU2uoTWhIc0HTnp6UhUJUgzdGQAvXeWQOWDEJejY3nq5LX36BBMOZEIlF56N\ny7RX8Lwk7KLvCYTAlObrgeeWfp843xL8mLduOBM2Yd8/xdYlPHC5Dk2sEaGi\npebz1fGSJVKpEtwh8U4detuGB9UC4t0g6ADDB+b3qJsjM2sHY+YxqcVWXukl\nA9lvjw98rdqr3uQCbp+hDgWizyZBpP9lcmU9bp0H/xdj49XX9HoPl7J4M1uZ\nKrFDl92zU8aGq9lK8WkTUyomYp7uSCPoKtux3GbGZAbhvcya8YAeF3U00BU2\n98yl\r\n=afUj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.6", "@webassemblyjs/utf8": "1.7.6", "@webassemblyjs/leb128": "1.7.6", "@webassemblyjs/ieee754": "1.7.6", "@webassemblyjs/helper-wasm-bytecode": "1.7.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.6_1536589060127_0.43707294461338075", "host": "s3://npm-registry-packages"}}, "1.7.7": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.7", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "7ccbb089c34b60efbf0764b868a461a8d83e5883", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.7.tgz", "fileCount": 5, "integrity": "sha512-lg8e2i3cRFint+VZJGyfaXNBtWkxe2Ug9mQdgwyVLrkX9GvPm2lB240kPqqR/fckCF45mBfIdDpidr0g9n4kjw==", "signatures": [{"sig": "MEUCIDUCTsAckH4QjOVt2GnBRgmXYRE9cpcSLjPnyFe9m2IoAiEAmhPSms9+kjNEo98UtQy3kBKyWx5OqtjKNpts87QWRMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboeETCRA9TVsSAnZWagAANRAP/i42cEh2b0/ZBBF+EK3J\n1eVW2FLHWrEfbC1yCmTXO6rhSHMvtsl7xQtHKfl2gJ8DjwqsvNYCZIPBAXrC\nGBv+3Lef7KurQkdsmZYH16y9sEJFNPYZX2dTA/Aq0hNa6rGcYuy+rpGb/TAu\nzqfSw8BliqGgPDSeacWSbJED0oWGMezjR0PjrqfpTsE+pGiXH9WbkfP/TsOG\noEo2Tv1C6PV+BNhUhuAEoaX31rAamauWjmBOQccVhX7QOjnwg9DtR7RhMJVn\n6dEpqJHetRKntF/b2sNpRf3gZIQL1EA/PetLH8yISTmUVghExxZRlo2qujD5\n87e78/VDIGfjEY9daGyrv+Np0YiLlBK/VvO5JG0dY1F2em13wFWqdsHI7evI\n1r8BntgfgUe468B9asvVI81OR4G3DVT7VppinIWWAEq1aomzsIe91s+TL/qV\nLq1s8Xv2DRcjPQfrk4rueseiFHluo30OkEG7Xmakd19Wo3kwqt1ksCskenGd\n4vkyLYnvtJCDrLH+1tCEvf9sqC9Kf2I4K2lrhqWFisBqz//yBX8ExTEuynYO\nrvaE0r8/o9Kw8h7C8XIrZx3pr54ejVtUst301EClyI+XGCY+o30lseVcLOaH\nvAYhxV0DW6Q3bj87liTYzJf5Lxkk4B0IjtcjN91AJpe7xGTu34GQ47+Uupg2\n9tjD\r\n=spiN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"@webassemblyjs/ast": "1.7.7", "@webassemblyjs/utf8": "1.7.7", "@webassemblyjs/leb128": "1.7.7", "@webassemblyjs/ieee754": "1.7.7", "@webassemblyjs/helper-wasm-bytecode": "1.7.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.7_1537335570569_0.43866289060284247", "host": "s3://npm-registry-packages"}}, "1.7.8": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.8", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "7e8abf1545eae74ac6781d545c034af3cfd0c7d5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.8.tgz", "fileCount": 6, "integrity": "sha512-a7O/wE6eBeVKKUYgpMK7NOHmMADD85rSXLe3CqrWRDwWff5y3cSVbzpN6Qv3z6C4hdkpq9qyij1Ga1kemOZGvQ==", "signatures": [{"sig": "MEYCIQCHc3VyXCVVSXSGkYVLfdbbFDjDu1jroD9vFwP6gIlemAIhAMKfklkdD+SyBNWShuEIDwJzxqBX3Zjm7qMbcLTAnSrR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpJ/hCRA9TVsSAnZWagAAw7QP/3IRiuCJlYoOAiq358Ux\nkFn2+yq5O9wBcwRBheCAFDIdKf7J45ywPBMSBv91cVgdIHUFF7hwzn5RqV/q\n05lDEX3h3zp05A5hIfELbWeJQTQr7Of3LXNZGmt8L2jehf7EBs4Msn29Js+g\nHl/yaNubFyX1qeIUcJd/mdOzWwLVYLou7dA1y2IY/TUjCiRhyh765pZya7m2\n6chSrGeU7yEpuBmTQ4qvaAlHRwx7SkRvD7JO91Zqq9EcnYaRWTACWeX1dw15\ncRLfY/6mzn6TehjICAuE0qoCU8i0NCyw33Du4ng2FfVOMK91umne4IkZh+bp\nfzuu1WN97OkqQ5OYdFyFG3pT6I61+AiEPv/ujLJhz40xtVX3P0gxX7FQCwzJ\nsFw0f7js/Ype5DJnfXQkEIVVyxN3JVpm7tvQNOrGEUGa6mrrK5BOqGj/gcAY\nJUub4nKr5gOb/4yFwa+k+EX27YB/eontUZQlF38vn+F4D/R/n7K/0NDPWIAz\nWbRZBWuTAydSE29yGeft2Rns/FRUVwQCd+1f5jRsHHE3PgSzfRc5l4An3oDG\nn/A7/t6QoKzbtReUdGCHneYtKbngYxa7lkEgJqUbKgaZLAAW2ZcZtZdQWeL8\njISpx8UFlziIU6BeHAvgOVss53aNvy5XkjnNXB4lL5oL5f1qc7ss7RVZQtZX\nhJTA\r\n=t2iA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "43b83b600939b19c48c3c27a1733592c493c4386", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.8", "@webassemblyjs/utf8": "1.7.8", "@webassemblyjs/leb128": "1.7.8", "@webassemblyjs/ieee754": "1.7.8", "@webassemblyjs/helper-wasm-bytecode": "1.7.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.8_1537515488449_0.8427040132351984", "host": "s3://npm-registry-packages"}}, "1.7.9": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.9", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "454f3e9b34e33b3ea381dbd51b2a3ddac1fd8d3b", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.9.tgz", "fileCount": 6, "integrity": "sha512-DBr2RL5Q41zIlImw7rBn7mwexiMsMfbhBWG5oF2e3mOMrwrUlU4ib3WImiHcdFN0O4pfm9l8aiQTWh6O3bk9qA==", "signatures": [{"sig": "MEUCIGFxwIDtc87hcmiKEgGvgrzDGucsCUw8OJ6GlgA8J2kyAiEAhs+YKOtS99iPKHB1m7mYWoWD+MiAQIC9i7SYc6dDPMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLgiCRA9TVsSAnZWagAAKVQP/2m0ww1Ig+EHhOeiIy/W\naOvDLrj0Mp/dWn6jbl2nXopL8DnQP4FMwNsIXU+F4oWau6z+/5MVdId1ArqZ\nzRTq1N2DJkpsWq3xpffYyqZS+dCypuAgRzSCvRAbpgJcdASLS3UkpOGKy2wN\nphMKp86AELwQWH/3nDSdfWhEYUBu7ac86BYZjAU21P/Dj8mCuL9dlEIFCM2k\n9ZvoTbSoO5CfpzL0JOVsqGfKYjt7BX8eODyjkegq8JLE7ZF7pmzzGRrOyi21\nYUSKny6XV9FiawWQhWoUDKcTDwjmSx7jlseoqhQc4Yu50emOv0ryHkIJxJdh\ncGyiRyiqEOodZjxzqIGrwp1FH2RFHL2WheZM32S/Wk81J1gFqJXWdgUcOVFm\nfaDe5ipX1a8h7oMgIMrlC1NSzlQSJwcf7I4RtHpu0MgrYnv0+deSiVGKEv5U\nn7jY5xwX2E5+u97/kiDimJbmDj217ITRYL1ji/if8v6I/d948/3dU4QyhxJ2\nrdOYwNFL4Ik5TfHV+fqLvg2usQ0nDvnY79CNPu5SdYYHVi0XSGSq5vaR7O3j\n2EOPD/fxnvM3p4VQmdPOQ70WfWf253XLLov5n3hXMIe/fNQ5eu/nvw7MJyey\nUtzEztfqVrXPdSk5H2rLI7lnTYMergE/DDMwhMkz4QwhKKQVz366MqEJb/sM\n7HzR\r\n=BSei\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "6c5bd6e21d734967e12bb7b7aaa38c80697b3b68", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.9", "@webassemblyjs/utf8": "1.7.9", "@webassemblyjs/leb128": "1.7.9", "@webassemblyjs/ieee754": "1.7.9", "@webassemblyjs/helper-wasm-bytecode": "1.7.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.9_1539880993894_0.33727413951377794", "host": "s3://npm-registry-packages"}}, "1.7.10": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.10", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "4de003806ae29c97ab3707782469b53299570174", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.10.tgz", "fileCount": 6, "integrity": "sha512-M0lb6cO2Y0PzDye/L39PqwV+jvO+2YxEG5ax+7dgq7EwXdAlpOMx1jxyXJTScQoeTpzOPIb+fLgX/IkLF8h2yw==", "signatures": [{"sig": "MEQCIBiPg1/H0h01i9/KZRlNp2nvpyR8X10JCeP8Fz2GpQAsAiBwlzHorh9HBfLxk8nYyEBQSi/TsY2Bvi4DNMKyCxEYwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzuv2CRA9TVsSAnZWagAAVCMP/0HqtAvNYd2WzUI6tFUQ\nmobAUhtvJBXzRVpx9Hn2CMYsZbIEJoHkFKqqIqPLtW1gMxsRMzMEoot/mJ3T\n7P7faCP46udyd9n2PvTgQaGjzUNnI4WmDw3lssjVUz6s31lVgmr5a5ohsYXO\nCHnAuFgfU91dWDmhzUTTZEa73Afylz31zuPTTWtFprZKs8iMQRKIU4iAvCeK\nuebbjBg8DYC12DxP98Vx+CmNABVyAYxzorseSc1NaomMjcnvmVp+8BEUIDJM\nBti++GfSkbWztt9+th0Styr2Ta9OdVqqqW8T6VymfhkmKksVjrzNXtWvjrAI\nGjo3TYzuv3nlWfAtpESZ4Hhc3dcSOFRrn0YC3JaQbmDiVCWQ4kO7nQ7CuGSx\n8QT2f3UlhXvjsg9HrWiZV635u6bw3ooctRPzM0hiUzOgmcpG+ViRf3a0kBho\nNevq8kj140F4iIy+LHRxrcL34ZUbD9fmleASpdjUUnZYQK6i7G/qzMmxX8Za\nNmJOD97+4021Wi3ktnul5qMAsXLiKFF2BynzX27yN6rpv719l9OIzZa4y0QX\nTtEjDpylY6+PZ4Ze0AmcGgGwf8LqkneDn/H57g1Gnb7GmeYTVL3GqXgPhV6u\nrj2bVR2b6lMI911x9cufCUODS1vzXquLeL+12zKPTY1rulXkviVZV09uZa6g\nMyf/\r\n=JRvU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f558c6c047187f24a2200ab04104f173de226794", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/utf8": "1.7.10", "@webassemblyjs/leb128": "1.7.10", "@webassemblyjs/ieee754": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.10_1540287469549_0.76465158515584", "host": "s3://npm-registry-packages"}}, "1.7.11": {"name": "@webassemblyjs/wasm-gen", "version": "1.7.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.7.11", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "9bbba942f22375686a6fb759afcd7ac9c45da1a8", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.7.11.tgz", "fileCount": 7, "integrity": "sha512-U/KDYp7fgAZX5KPfq4NOupK/BmhDc5Kjy2GIqstMhvvdJRcER/kUsMThpWeRP8BMn4LXaKhSTggIJPOeYHwISA==", "signatures": [{"sig": "MEYCIQCgLJrr8VpwUuxxGwFYC1U4vk6aZeVp3eVhwhy8XDUhcwIhAIb/dNbTuD4GCPmDI+zSAeboy97rcafGuolrtqYj1uU/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2JxxCRA9TVsSAnZWagAAV+QP/07i7LH045kucUzK5OoX\nyGbdqmsxzxmGZR1F3MYs6D836M+HI/9KMfkcrHjNRFqFIR3sUW7gQBoctY+V\nEl80VOuar34/dLMoF85sABjPTG1an5hBY1t5Yb10fVJ0h1ppXRbULnK9IEpI\nR/seD+hCZbarmgNZgTe/pU+A+zxhurgt+bipcxYlPgjvAUWwCoqL40jSjl8d\nib7aKqY4GsPxFq1z9m9Y70pO8TnyBv8jpw+YZ9ZrmZNDcKxWuE4GRnpaJ36l\nyFfJX9xOgCI/5ZbJD213R/8JicLq6CiZyTr5UPlehhXkcRGaWIO6OR4JEePX\n5Mp9Abd9eB2t/NiBLGJYanyfHqaPK78udFP1RyRBSPzE0XUOO2+1/BIksggj\nPTOWXKV9NMe6Nkmlgk+cZN0ePAl2bv+Z6Ywi6YF0fU/9lihnSjUqkOZ73E4K\nTs/ujNxHR2GB3rpcXjbd44OWOC+maL9hTYr77KmihJ06mpaQ8Ju5V5rFog/Q\nHnUthjXc48S/YEyXJj1Bg1wq+RFlssBN2NWHDjWiy0MqDRMh16nx5CCP2Ww2\niUT7e4w2UVjMGN5/3rMcyeqCUReH89YTGy+fdpflnDzJjUEgzZMIjdDwr8NF\nQN1WZbmp2z8biwOdmwVP9uu5SxtbT5gZxdihkQYJsDj7L2Dk0D7gI+Ty2e7p\n6bSp\r\n=YEN3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/utf8": "1.7.11", "@webassemblyjs/leb128": "1.7.11", "@webassemblyjs/ieee754": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.7.11_1540922480952_0.009015531020120982", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.8.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "aa891c41b6e0aa9dfe6e6e8245cc10864cc405c9", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.0.tgz", "fileCount": 7, "integrity": "sha512-a4btGeBVPWL+vrxECm8dCprcVcPtCVJivewpPhjm+EeCo2bDqteViQ+pct9maWVDncZMlT+yNBve5GPu++hMLA==", "signatures": [{"sig": "MEUCIQD7LN1I/bPuUrmhbRlGWJguf3WayMWBuaB10gPUiLN2ngIgBKbWxvW6hD/JFEC48PjCZG3MIrmKjuGw7Yv8Sa6hdGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2Y6CRA9TVsSAnZWagAA940P/jRt8wBwGFTkbKaYADZ0\n9E+O47NQKvhPD45vB5s0F6ADlbmdHGGa0ZmNxnj+gUM4Qf9H98GSpJZm00Kn\nO2Bijdd+XEjPWakDnkrXfHwNO6tklGvAvOXOua9VkCie3qGYMBbHAZGoGQnO\nLeCOhz8yuW1NGK+yOezJGc5GzhZcA+MxFrrOPd4P3lsz58gS1vG4iAsYOGwb\nY2JXshev95OfJBH6NveAT2uoYVhbeYUjTTeiL0277kKbB7y1Ao3j06cIv9ts\nZh+Zrw4A1qQNK+0ZNMmkAU8mh/aN+Asy0txJbReqxQz8J/vfQKA+txEcAgcS\nStJrxr/zKEKntQgDkicQPolyUZpbhCPJWROhF7qjoYmB8w/y68nc2fhyZOdg\nIXA2IKVDmbpd2RKAK/ESDzUVkoHia9MNkPCHK0Eoc6zVyOXzKsuoAgm7UnmY\nkWrq/Eh3kX0ru5L+GIQ2l4OmC+IDytJ4mzkT5Devo4HXloga7uvu4QZDmwa5\nrmyjY75047vNLHmFRCAvz1Ht6bXTBzPCeoypylAzuVHLSaBpQ2iaQvOpeWRJ\nMBgG3lWknLCDZxhXOYuxH9r+lbJkrNr7GDXC1TGT0t84ZJC+75YlwPFuiIN+\nGKhsB3U908FV0dHzFLTPWS+KpzGI1jwsw2Rus8uAhcgudm886pbE3s8J9zUp\nB2TP\r\n=W8eD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "8b2d1afa793ea81f20ec63416134c201e39694eb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.0", "@webassemblyjs/utf8": "1.8.0", "@webassemblyjs/leb128": "1.8.0", "@webassemblyjs/ieee754": "1.8.0", "@webassemblyjs/helper-wasm-bytecode": "1.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.8.0_1544513082124_0.24210121323431189", "host": "s3://npm-registry-packages"}}, "1.8.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.8.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.8.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "4f7b325b370d6405b9f8e6325971bac9c655d9b5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.1.tgz", "fileCount": 7, "integrity": "sha512-xOgoGf6rR6gHlhlNlU0EfMIgDAjbLCO2cNdEIKdGfKj2/fc02pbAyS3gYJ6EWAzSnL/XpAOf3Q/trp/EUeikug==", "signatures": [{"sig": "MEYCIQCeU4gd42PMmqNP0fku2WbsLtuoBO4O8IQsGYOpfooSgQIhANwVpUEfGgmQ5O/FB6G36jRhICY6VPJH3MJndYnT/FfJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPZgUCRA9TVsSAnZWagAA2VwP/1OTFLftSkuxTLmlXTg7\nVEkZpZcTVUxsymPJVy2oE+kB77pHVMGvllWW8IBIH7LreysP71azzoc4Sbfp\nRE2p4AyuSwNLOMWW6TJiG33vByXJl0YdxbNNU1mqPl7/Fc1lLzgx0zeiFpdJ\n8vuEYq0Vv+KmngIy4xuCc6QnKnEBFwQB/4hPYz118rQBeirCL5cUqfdjsnDv\n/esqQm91eMXmXG0wN73KRngjFuXvHD3oNkZR4WOaLPUrGiiZVM3K4b1QvS32\n4h5zK2z6L7aEbqrA8q/u55TaMfkR+9D191ng8KeWVBzkUIVJ/pIWVCPUnQgZ\n+CMpJxjr6EUr4+SJGlspMjOnjgqI6R2yykC7sn1cvLvc80UDKDsrXQ9/GWJf\nLhvAozqRwz9Alm21aKs6/oYBNTPDm9Zlm1zqvuIUO2Q3B+8Rceq9j1WtqY6u\n0D/365WyrKmo7WBMNqYlFFbKdSoID4S7gsNh3IoR+ajCt5HojmUfzG7BGsPO\nJME33NglDmrk8fOQmd9lf9nz6BxlkcPzYl+sSTn0xJiSC8nob9vP1zxIz55s\nf9KZkMd/2h7Fop0690WqgRC0ANX1/WbS4xV6xEYoZxgrSEEX5ZEQlMQEra5b\nFqkGyhQqWsm+yOvu4ypAWJy0VgllDDOe5cDLDemnNFPzWZ+d7dOYCCu4wSpr\nO9AS\r\n=BTev\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "a2f42245e9b597e3541e0f697253449d60fc4d79", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.1", "@webassemblyjs/utf8": "1.8.1", "@webassemblyjs/leb128": "1.8.1", "@webassemblyjs/ieee754": "1.8.1", "@webassemblyjs/helper-wasm-bytecode": "1.8.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.8.1_1547540499728_0.5969237995827357", "host": "s3://npm-registry-packages"}}, "1.8.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.8.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.8.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "389670ade8398275cbf7c7c92e38f6f5eadc4c1a", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.2.tgz", "fileCount": 7, "integrity": "sha512-WTBesrMydDwJbbB48OZGcMq6zDsT6CJd1UalvGuXtHJLargazOron+JBdmt8Nnd+Z2s3TPfCPP54EpQBsDVR7Q==", "signatures": [{"sig": "MEQCICjc4lmk7i3GgT74z9HqJzMdSy5q0hsqOLaxqPBzqou7AiAuLRmWyu2YrZMsf6UCSedmS7Ao4l/cIQKcvDf3X9Q9aQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZZWuCRA9TVsSAnZWagAAz3gP/2I3l3TOqcLUEwLp3oDA\npMRkZtKHSeGNyHtcTcPHUy8d/mVibPGN6ds6+QjBz5Pn2FI98b0RngEyKYRP\nQEUBIYk5y9IorWuk9CzeoRzotelKGTvoJsgqwtpXn6ADsBY7fsyUyRoC2iWY\nItCDSbiECD3SdVmB0ROEl9dKqRFkTGhbw8i/VZeUDuq6DxJqGoafE1RCHf8g\niPq0jlLrbTcXR4gVrZRS32f+IHbKkevTpJSwK0yKf4iT2kCCTWzntEzJyhYC\ndSXLu+uxdHuSlhLrMTO78GzmqmDwYlTQ0Pfxj2IJmh+VbndEjQVdOHLKyqxh\n9kPPrc4jWmV8NuxRwE9JR+D/X9vjN0dKV2R66agR3FMQYDVw8tboB2l3msjB\nGK+VLTDQQ7WoqRkRGkjO2lpWGeuk5DC3beDa3KbPcAzOqOHeJ0niJMSwVV/U\nYH12/fmaGpQPKoMa8MW1fVPzMN6pjOsBP99siWDNkN+z6KzSAILj3n2pEmMX\n1bxn6T9oayURRmxuJJKcORs+hcVYjU9RssLASln48URyfQkHEAANQgJtu9fk\ndtpFCIEKnxJz7bknvCW+zaulCVHoXoKrx9gnbuNX1COJ/jW5SkLS2nSWLV72\n7x++kchxqlsqzY5Rvlqc0svhoOFpVk0BzTd11rfrxcZKg0CRG8HS0UI/tT0/\nfYxp\r\n=3PW+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "02af462b507aa7a24f5d3201178434b181bcdabb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.2", "@webassemblyjs/utf8": "1.8.2", "@webassemblyjs/leb128": "1.8.2", "@webassemblyjs/ieee754": "1.8.2", "@webassemblyjs/helper-wasm-bytecode": "1.8.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.8.2_1550161325809_0.08174450140237433", "host": "s3://npm-registry-packages"}}, "1.8.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.8.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.8.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "1a433b8ab97e074e6ac2e25fcbc8cb6125400813", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.3.tgz", "fileCount": 7, "integrity": "sha512-sDNmu2nLBJZ/huSzlJvd9IK8B1EjCsOl7VeMV9VJPmxKYgTJ47lbkSP+KAXMgZWGcArxmcrznqm7FrAPQ7vVGg==", "signatures": [{"sig": "MEYCIQDMwHorx0afZOzxeWearw5Q4g844RRDoBtu1B2b1HSZVQIhALdHV4v462M88eiUjp9l9rMeuuUYfweQ5Rjnvwqam+By", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcam7ICRA9TVsSAnZWagAAk18P/1ivmz4z3YOub/wIqWON\nqGtvMFoqvtErm2jGJULZpUtAmYPQhmst6IqdOsawfDYIbZGn5yS3AY1hn8u3\nxIvEM7L8JDDVNiBVm6N5WLetHs4Z6z0pKpMt4kpSW6+BgpWTfBh+rYRXZn9B\nJHNNXVS6DT8rPgqIz318V8FlibcUNacfW6ZsMztl6apuHMd5mWS72C0IiROg\nxhb21vUrzaqlfS+KEzQsVZdW73LGXyTvgqyvIXg9UNPMJJpr4abb1yFtJfxp\nuhLFi6LaRPBDYwMm4P7rniH1/du5wCfOpiVD99dE0fwcCjfwV+O/R5yHK5t/\nWggKfOZJMW0dCebyOwNcHjJHfTvmkXmHBDkumz4+4LDeHYt9h0b8Xm+WkI2r\nVNCeqXefYwEaxDhGNyKzdO5mc943zmx1BvxZXecpKfcj+Gte87lnvl3hWBFS\n4I9/T0b1MVsWnqP92lPDkebtk9L5PBaBpdHECoZxmJ4MxIDVmhkTDpKFwJGG\nouOIcHM8haQIolTXcqfWV4xzv9T9KsEbny6YatA4V3SJhxLMOnK4A5VP5OYR\nqDxdnRkcVNLYIki5e/BGegdlQZ2A5N091ioruNkeblo0oLWmHFWrSQbFvxCM\nUMHNAoMu4mc45Ag203Q3KiPkhVtiOIlRpHg/e4cVKQjuo4GVrdGQD7xmF8BT\nCoJd\r\n=Fvj/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "e482c7ec291d61fc46e42c93d3b8ec7517b629e1", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.3", "@webassemblyjs/utf8": "1.8.3", "@webassemblyjs/leb128": "1.8.3", "@webassemblyjs/ieee754": "1.8.3", "@webassemblyjs/helper-wasm-bytecode": "1.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.8.3_1550479047581_0.17475476033561832", "host": "s3://npm-registry-packages"}}, "1.8.4": {"name": "@webassemblyjs/wasm-gen", "version": "1.8.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.8.4", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "bcdeb6efc7b24656af9ecf59c97736c2a2732fd5", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.4.tgz", "fileCount": 7, "integrity": "sha512-9lIIB/fuJLMTrl6ugnyDEm+B+X47Qsj6zHl6tTkx3bV+Fsvml+BP6OhXDwR8caRYPnFL1jl+yn2WXWlPIEBH1Q==", "signatures": [{"sig": "MEUCIGkjRheg2vpeTSxmsQA2Tx5AUTWBZgzvV58Viz+RdphkAiEA9468ySevJN1sZMmHVSX6FBjfNKWlW9A7tTNuQtXL8jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbDyPCRA9TVsSAnZWagAANDsQAJHTv6BWTl3PQF1stR16\nv68gooN0ZmFC5FSUGg/7DChFGrd8TfCwBjZqkLjZMS3JZ/05JGJaEOLuQFrE\nQfvJkACOKzJvDDWXtc4BMkxf+3sEdlq0r+ySNmgGaoq+YTlhpk14kugOTRYU\nNWPkAP5Ae0f118gOuWHocUtS+6I4OvPNWEbYee7FoAWmTwnSctIzGeE8L71C\nzQPdAC0wP7OPkjZjd5FA9uHV+EfBSu26ozTvOzSQueBY1Bh4Z1MhtXIP6LZG\nBHDbZCWCQ208PvyINhIxGVDuFl7XY9wkoJXpjM0m1s5rD24wM5clPVvCzQj6\nHSisUgfz0msPTKCbHedtUS5yHiNZ/gpV6tw2CdEODFtDfA1EYP1WXdEW8OUr\n0LxtQZl0n3X+FboDsv9JI6p2morlDK6hPV64Z7L1V60IVxuqps9QWybtdrlG\nsNvWGihytYjG9v3FFMJGU8O3j7xGeUaem0m3tqt6RZ44jEr/ihq80nb0ITv1\nW3eJTN0XiItTM5zxI8lrnVjcfIjSG1cdbfqscUlBr6iI8O80Hmb1+YttmC1L\nv0VdxDNQfX3CBACEMsiNhe1tkYfcPL141sRLfNFNAwU6qxKKm3PNwIrX1q2r\nekT48QkZBRMWBkq4SBQMGgB6oOVd0lIxbKT/jcUm+Zr+uV82zsUM/2h/jllo\nEMF9\r\n=mrkW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0154b989cc9b41c695724a361b3aa6fa19c5b032", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.4", "@webassemblyjs/utf8": "1.8.4", "@webassemblyjs/leb128": "1.8.4", "@webassemblyjs/ieee754": "1.8.4", "@webassemblyjs/helper-wasm-bytecode": "1.8.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.8.4_1550597263201_0.5151722382394213", "host": "s3://npm-registry-packages"}}, "1.8.5": {"name": "@webassemblyjs/wasm-gen", "version": "1.8.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.8.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "54840766c2c1002eb64ed1abe720aded714f98bc", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.8.5.tgz", "fileCount": 7, "integrity": "sha512-BCZBT0LURC0CXDzj5FXSc2FPTsxwp3nWcqXQdOZE4U7h7i8FqtFK5Egia6f9raQLpEKT1VL7zr4r3+QX6zArWg==", "signatures": [{"sig": "MEQCIAGehxDtfO88gQribNOuyN9QS4n6yWgDlbfPCLTHeDQEAiAVf5X9/J5xC8LYyPNNlEfxuUjHdKerkRiRe1mOveRGbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccnU/CRA9TVsSAnZWagAAWtoP/jTqu0KfkUa9MtijtkPa\nceQ7AqfotAXmhmC7pOc5v27U4keQ69oi4w2XgO0P5FOj+q0fH+SCX18WrpTJ\nSdj/aXAj+gf6Wy0Gf2sQbAXfDaWBFvTE6PtkRRTPEdhoREBYQzWjzUGHbi/3\noskupn8nLu+V8IKjLICZFNsi4bMw7ugHtaTWn8f2baWYuwah5mroOYjo13OO\nVkZyP5TWCoS2FgrJIpYNHb2TihbbWs6am0wpOOGCgTIexWvpxSO2SgPb0J2o\nwnGuEMQJO96N82pPQJHPwWN6UmrLWYOZlemQQ92cDFQkeozAOujqJnonBeKz\nHulFR0mM6p/PgssRsnTOJKrw8TBh7JjWhqwD3Ur1FOiy0akK9+6J3ZtLj9pi\naeqM0oO4t8lWtYDu6DlX5mJdiTHk2Pp3OvLhj/w9qFWcVImwBjtaIYO2ECY8\nOO6J5WR+o77/26DY383lWKv9tGgQSSlZmWwEy2Jya85Fxwsxy1D3no7TN24g\n3PMLP8q7DMpCluG+6cjVHncWOrlqbQDvb/AoWcd2UU5JSnFVIC9nYLKJIrZf\nuXV8TOg+N11OCNWhDGOOH4r9Qtk9dkF96rBmYlklHxB5xTfZpR3HDWZYiqFB\n4gFMnnBO8y27VIzyDhAIIDBe6jiRD8t6DL9ZIsFTbaGsMtVxov+WazamrVJV\nqhI7\r\n=yOa5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "77bc195e9bb463b4fa4cbdb6291eed446d454d49", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "description": "WebAssembly binary format printer", "directories": {}, "dependencies": {"@webassemblyjs/ast": "1.8.5", "@webassemblyjs/utf8": "1.8.5", "@webassemblyjs/leb128": "1.8.5", "@webassemblyjs/ieee754": "1.8.5", "@webassemblyjs/helper-wasm-bytecode": "1.8.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.8.5_1551004991302_0.07892214428447852", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.9.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "50bc70ec68ded8e2763b01a1418bf43491a7a49c", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz", "fileCount": 6, "integrity": "sha512-cPE3o44YzOOHvlsb4+E9qSqjc9Qf9Na1OO/BHFy4OI91XDE14MjFN4lTMezzaIWdPqHnsTodGGNP+iRSYfGkjA==", "signatures": [{"sig": "MEQCIENjcR5/VdkeGoDpd+i4FlKrRuPieVX0kbylLM5SUYhsAiA/B70wy3hnXFz8g3XKLQ70czQM+lv87NCo8SZo68INlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNgeuCRA9TVsSAnZWagAARSYQAJ6nRBpXiR2+Io6CruxF\nwQk+oFMnNVG0Km8vBbmgjw2j/G3b/KznD2VL9MUJQE8UD9J4e0181IGx0usN\nkDAI9pPBFaUKAOO6Mx1j9Af0Pu01IqwYN+bnT3q6/LmZJHbjZ845P7d3VyoA\n0m++7ohCb8RGLtafW7vCaxjt71VaKEDgdGbGIPiG+SgxNy57JwOlb60tbb85\nyHnoiYWfuhEBQS3OWZ0eVAZpBej1RqvqsDIpEIi3M+U5wGlxZYxdQ2YN9xSw\n6JurWaiCWRQtZOpRLZAHcp9JpWHHCjd/7ZnhtZB5+pZwtdrAeJ4f4K8CWEEJ\nDHvFoWA804o6qJnhkDEIU8B0WIxKRCJ+TmR/N3Cd8MwjCci+zn5RXWaQStGt\nQ7c02QX2DqSA/2HisYXHxbOE5AoX3HSoTrQt+7UFyh1jQm8DWdTuf2Kg0UZ4\nNpeONeMUad1eTgFXxyWAxcwUSGUZvCO+cahR2e5/p3aRN0C5i7x4kkDgGXoF\n+DAK7cxiG0XQigiKyLA/zYz8VDNGIOCoX+NMXlBk5d9bZEryvNX/TXi0LJfG\nEQDCFYhwx6MkqDOST1NgszxNnj53vPYouRPolDtyYRVVvq7+82oUy3fLqxQr\nDRq+koyJtX/3+3ecxqGCURqv9or72pwWkZv4qqkhEjTexkLOTFXrP9DmPRr3\nj9mO\r\n=z14H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.3.1+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/utf8": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.9.0_1580599213926_0.28543721065647065", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.9.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "56a0787d1fa7994fdc7bea59004e5bec7189c5fc", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.1.tgz", "fileCount": 6, "integrity": "sha512-bqWI0S4lBQsEN5FTZ35vYzfKUJvtjNnBobB1agCALH30xNk1LToZ7Z8eiaR/Z5iVECTlBndoRQV3F6mbEqE/fg==", "signatures": [{"sig": "MEUCIAoc1smA5kJbSH3B7nHebrp4gPD8D+LTii01aWrisEp1AiEA1C3bcRkdblAhbNopbrtDDU169PaerXrqRChxmOvSmV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeNouCRA9TVsSAnZWagAAePIQAIzIfbFnJK99qg2Xj00j\nX3vLdowbIiK684NJU7n1Cl3VmfQUdoMCxzAy5Wy94e+U5Tv7wuM6aWx8NvGA\nxKF/x10GW9Kx/Wmz4LTXO0k4IPU9YYJ2Z8gqh7UUP6StpHNaveokOOh4mEPV\nTT9kAl1a/sDdMpWnUCXz3e/wRG4njoUXhWXJ6qTUoDANNbhm6hfrqB7mPmXm\nXb3ndzq/7XjpqjGMBJSR0jTvcQTwnLJ5XERFuvenVBYQipQypVA9fanxImTl\na4wIFNbfLIlT36fKA6mJD9U3ZkxDzofC2J41LenSUyGZMafPYqbSKp17h8FB\n0xEBcW7PHrwSrGCgAtiXq/MiDmBlsNQcbka4FWRjWwYr5ou2RsX9Co8+X0E8\ngU+uS3Siseo/uXZMv9nQ6RahHYgmSx2/QIbY4z3zEW43Fujfc6A7K7thAp9A\nVztqH5VmLa0Mw9vLbwWCMaxRpR8VZCYCAqrNxSA2eiqB4VeuovyxMjR/bAnu\nyQXtMIiEyLZIvfNlmcLQO4muA6qTKn+iB6X0LawL4hD6uHgSlTuovdRqHk+4\nKWlsKxZMsYtcpkMuc4JgwE18togTBPoDedCl9VEWdCGE9XYNuY6DFo4Bxxay\nmzf3QRUf5qeuR1HraUsuHFpAwwJH9N+OFka8cZyOwvEjyjyxWHDHWma7Npsc\ngJXM\r\n=n3vp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "697a5f63048049e9ecb3205d789c1e80eaadf478", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.9.1", "@webassemblyjs/utf8": "1.9.1", "@webassemblyjs/leb128": "1.9.1", "@webassemblyjs/ieee754": "1.9.1", "@webassemblyjs/helper-wasm-bytecode": "1.9.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.9.1_1601755693908_0.4616183550678994", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.10.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.10.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "0aaaa2b40d792d45fd6c1600cfc13a84b47fc0de", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.10.0.tgz", "fileCount": 6, "integrity": "sha512-1W6dcyf0AgIAjKO9cBA9uhhE33LWn2jde7hXMfn68x1NREVMeVvN5Q4AIRV8AYIvIHxAQt5zvadrNIG27xtvKw==", "signatures": [{"sig": "MEQCIAqp4OAbo0LMFkK6QehkUaYGLKMFFsrF2JNR6oEcm1u7AiBpif84EDSTx5bcAQ2Vmw5p240I1dRXeuLatvzFVyUKpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zRXCRA9TVsSAnZWagAAh4kP/Rt1sIZNDECmkgjIaE+p\nF2WNUgBN2Uc6posHOjmkwlfbDsfosC1qRRBvaS0X1x+pOfXrKIgkgwEjCCcM\nuDykcnc1lUBnNUTKqfBDnq2NX+7EyyMmScK7sb0pz9y7KWmHQhVv19/uoqRg\nI6a70aYrTbFZ/qFLK5U6GkR2tLuETfOnSHxBEktv5de+tQ9jDCkzx0di4fmT\nMvCHKEbnaPhspLsvdmSYZxCqPwkqz51CCnQRYRJiVSD+5BpVjqO7fXyOei/2\nIPZ7CSFnVJbmqSodQhwx4FVMs9A6pY1w7TdTDokHLtRuB2DLDhU08Q+iGTms\nTVrGuY1wvAVzoGGJ6jdrqPpuQw4JcDALkF5Lpj72P8HtYVFk+Y/BgiYl8nc0\nTxT6SsT+JWTCmw0tcv1JUiihhw58LxPl/6Seneen6rF3TZDGEj/VtE8jpd8I\nGT3Qk9Fr3pnO1dFZi+nkE3E37KQTOnROrJiw4m5W+4xAl2yav+igZJxrBluX\nGgSKoJ47eJ/p1chH7hBHiEMvtyRBFjRVDo/BFIBn4NczIb1FGnxywTGWr9WB\nJOR9JQR+Qzo8EYpmGq6suIu1bX/uGhq6SEw61BUwtawwwDODPSC6v8SALl1j\nLEKQWSkhGI+1IVno+exst/6lt2oYuvNKiZo3fNR0BdkBO2dC1nxrEjd4kUoy\nhafX\r\n=q5u5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "d00b899ece0242275e2475e75232ae18ddd03aeb", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.0", "@webassemblyjs/utf8": "1.10.0", "@webassemblyjs/leb128": "1.10.0", "@webassemblyjs/ieee754": "1.10.0", "@webassemblyjs/helper-wasm-bytecode": "1.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.10.0_1610036310634_0.032659187289814984", "host": "s3://npm-registry-packages"}}, "1.10.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.10.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.10.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "33691b8a4cf21f347db97cc7c0733311aca45907", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.10.1.tgz", "fileCount": 6, "integrity": "sha512-IOXUihIqwVJnlnAdteS45/TUoUPY113njog0r6pFE4x5DfKc4Mu6zI8rrYyFdAu8lq29HglQW/jyo6SMyRXxqw==", "signatures": [{"sig": "MEUCIGcL03rfFlysuYk/zX5r6KvilfO9KcqdhuEmjUwBPknrAiEA5ihjAYDedsvEOIZcYVAtkm7RdVa8ClLr58HMqIW9K9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9zqmCRA9TVsSAnZWagAATF4QAKTyDsKtMh2NGIqlBNI4\n5Wkn00kyO9FrO1f6WxDq2G8vGDUbFueWO3wDsaMjUi+gyEoV8X5pMTDznGDZ\nqMDDYJmid4LRSMfKjeFpk2yKsIKrpw3bYbMU1GfkhMHr93cxqKDlyV6qqZdU\nf8fo4VzdsHy3Kp9SduOVslKzupXzcp78g2PL1K6zR2pVM6QIjFd7ZS+SQwe/\n0lxKfmb5isEp81oKPSfuepstw7UGy65xcOsAv+qog8eXrJyObHdNoEscQ17G\nSp4EjK9utINxQRKb3ZMBJAjSGbLx7RTDKUhKdcioq9qy0ogDfi4dGpw4wAN+\nJFjIQ/8DNmVxcq2yXWuweOrjcSa2TnoLesIf7Bcmhd9iHVxemuN2+7He7tB6\niS7PC2hPX/SVynQpcWdhu4gFra/EnWIc9sf8Gb6yQcrgFHpJDWaLgggXiN3O\naqGBK79GzS8dgQFKNTbHPp6+QKLMKpC3HDKLHsFMHBh1YulvAx9vNXfMSobD\nu/DEHmS2bXJuuO26QlLmwkPXDyQhCePPQiO63zP9Ph3jGTd7onxcUUHt7yrR\nNZk31wigOGpP0HQKHJs/OFzi3njJNiafoOsU+TNgUWyPEQT9PS1pYgdHThg/\ntjzpV2tArGlDoTBUy4Cndg2j2ZKRfD4ha3+iZcMmxEcMRBBs7QhUxJh6OI8T\nIv6Z\r\n=L/26\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "f723f2cdd9bfccb5e199962dd8c5c09bdb0faca4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.10.1", "@webassemblyjs/utf8": "1.10.1", "@webassemblyjs/leb128": "1.10.1", "@webassemblyjs/ieee754": "1.10.1", "@webassemblyjs/helper-wasm-bytecode": "1.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.10.1_1610037926572_0.23252015817524274", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "@webassemblyjs/wasm-gen", "version": "1.11.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.11.0", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "3cdb35e70082d42a35166988dda64f24ceb97abe", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.0.tgz", "fileCount": 6, "integrity": "sha512-BEUv1aj0WptCZ9kIS30th5ILASUnAPEvE3tVMTrItnZRT9tXCLW2LEXT8ezLw59rqPP9klh9LPmpU+WmRQmCPQ==", "signatures": [{"sig": "MEQCICYeL4YFUbOUMr8bEzerrSixQ+aJQ6jDMMUMmoB6aehMAiB+T374TWJUt32bnbs7+8dUAIQiYSB7aarSAdvhnwhnNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf907iCRA9TVsSAnZWagAAyksP/1sBBDPH3F4BLaDswWgH\nh8eZ1OwXqLcaBAbxzz7cZ4G+2j8cbNIVXOfdD8KqK+Vg7jYkFabWCibGFgjh\n9zljP6b5pSIAgZluvrpFOTwn3EWqbAVRol5hYhmqNCV8HKRCS7YTSE6VCByv\neonImDbqPJoEQJaJIedZA9rWykY0Mxt1DqMKkJiFhnSfZau2myrXC4dt6ZMc\neihhTbTIq+ZYvr3bbOY8hx1u0PIZkT1hWvSOkNuIgoLERKKQiSC7uYnUHkk6\nV+okJRu3oImyJUQk0oTYXJlkJ6JQKsSSpo4FUy9YYLr7+9oaMb8ZWZt6ZEK1\nWFQAKu6zVbHE9G81vTucwYHUr8rrWQsZ/zUtX5UG1VnybLyyy9z7HDdVCuLi\n9GD9eJqU64+eoRUrF4aJ2sW5AeQcAyGhu32/iTU3epGAJVlRsixr8qeNJif8\njxNsWGXU1+mrXduE2hWVttSxsvKuqcUgvJNBjp2Qee442mheqtLQRXQ1n7nr\nl3isaHf9R1ICcNw0Fu+212H0AthOcTIsidfg7CdN27C1g4Xx7Y1oZ9LnEOax\nECbHKe0UmRC9b/u68qcPBR2VVf6+vhfCHb6NeZleK5J1TkKKp8AsmkMuOvBL\n/4irT/H/j8LzgULLTOfPWbikrPOBJx4xT0TryDXwJVTVZFqBg7uMR093nZqY\ncwZ2\r\n=C79s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "2646d3b7d79bba66c4a5930c52ae99a30a9767db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.0", "@webassemblyjs/utf8": "1.11.0", "@webassemblyjs/leb128": "1.11.0", "@webassemblyjs/ieee754": "1.11.0", "@webassemblyjs/helper-wasm-bytecode": "1.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.11.0_1610043105888_0.8026347857122678", "host": "s3://npm-registry-packages"}}, "1.11.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.11.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.11.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "86c5ea304849759b7d88c47a32f4f039ae3c8f76", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.1.tgz", "fileCount": 6, "integrity": "sha512-F7QqKXwwNlMmsulj6+O7r4mmtAlCWfO/0HdgOxSklZfQcDu0TpLiD1mRt/zF25Bk59FIjEuGAIyn5ei4yMfLhA==", "signatures": [{"sig": "MEUCIQCTPdX5iTgUtXU9JLktNDFmUtY3hE2RYl7+O3vJW6wC0wIgDJ3SNSR3a1ryHk9UAKrZwKnj5p8MHnLjiOVyDSJP7Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4sE7CRA9TVsSAnZWagAAT4cP/3YYVhIBzKJb6B4ub0sc\nVWRnfIGuQN0jUgpgU4Nn7q6N2bz4CudEMni6TFrx1z7AeHKNVdErYN3H+Syg\nC0K0G8DZxyYJcIGe707spdrgvvky2ndRSXeT2rGkzW0Y1/ODnbsFq3Jh5OGQ\ndugUGQ0nNPDuHacBfdU/YPcMzsiJWpqp+/sqU42ElFfiRnePCm27kjXNvCPA\nd9ltSO2sx3xFH6a+YLCZJmFgOdA3JUGXMeR5u6bz1dsP0SLoG5Q0teQW87/0\nzI6H1sa1YWc7/BCfQw7xJVdnkP+ZeRL58+dsSI3EywIr3a3kCyvZi1BCk3ze\n4MQmtNTXwsPCn6SkFKvJfGjhfuCDzMs9nU0X3azaJMl6E/Gm/nE06IbqI/Vk\ngu5rGo9BwjCV9E853NAgy36YZH3gd0vUUUAxGueAWmjJaqzWsE8jIIhottXd\nYT308umQhquxF6LnVrd+6aLtQ0SAqR+XYSqy4rJZ0L8DXAOP5JoSoFl9re2y\nrHxvJjgE8Xww2UrrR4hvQJjD6VqCHfxzHRt/dwJG1YFUBpFEabn07DRsr8Pv\nsRhSziGyP5WLmoHzDvKaGOxZuPHFe7zsxZZQsRof355V0rQJz2WsIU6gNShs\nw2uemFE/p6wEMd0jdq+yammhSxOsyH5MwC/EQg7CLgeBmpFvK0W6t2eUDexM\n2gR1\r\n=3gdM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/3.13.4/node@v12.2.0+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/utf8": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.11.1_1625473338701_0.1343147156115012", "host": "s3://npm-registry-packages"}}, "1.11.3": {"name": "@webassemblyjs/wasm-gen", "version": "1.11.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.11.3", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "67fd88ec2bd5b0ed34841ea625f09ca1316adffa", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.3.tgz", "fileCount": 6, "integrity": "sha512-f7mxK8GKAP0MIeVeiCrTqcL14UQspIun5P8kqa7Pn7gzfDRwrcEg1bezKeIcwcEuZiP+i1qerRELqiRcJnWZaQ==", "signatures": [{"sig": "MEUCIQCENfL7iXGTMARBBAMUQrgkXDQGJeLnfhE7MaqyODTXHgIgRh7I1ViBVuUQg/ryBzA6yD0rNedZoiJodux93dIlJNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwDGXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK7A/9GxkKRLmbZt9SOxfDd1+HYQJ+3NaNBj5ipcUVnbtbykwZ6Mm9\r\n/yddci7HztOVoynOCX9QlQeAguUvH31NYc4cW8k4ZUzNturbQIpdtifST0iw\r\n9WAmVqZc5eeLU+XlW9BU0stX1aT0iVfhqXWQ/bQKMc2hDgEJ58bPJjTu3VCI\r\nPx9qjA4GlGaXhAyeUXuv9GrvAPSpoWcKEyzGr9Ra35ULCOrEHIzOQRubiqLB\r\nBpSPB+9ee4BSZBcL/MmwIlEGDXf6gE+9a4wDVxEBSjwVhExcg0q+B6Sr/GfA\r\n5GVyLYjawO9UBX/cZVXK2b1zq1k6gBJYv5RHhtYzIVtyb2w0egT23axA9CPZ\r\nbIjTwstdBqo8P3glOBitNHZXvVI60NSPWs+twp+YRVCPIivoS2+rQea7p/FK\r\np43cQwbCw62GpAHUsHJj4YpcO/znav4ddKneQ8rV2byE/3vDwOjsyaA69nMG\r\nxlore0+EGiXPNuwK/oJSCoxkxJp15ML2JDQLa0JGUybUye1wHVMrOfFxaI+e\r\nxfbSUtJ27J0BQvUf3DhtilUEUALgrj8fgvs6E5HX6mY5znUUbA7qaC6pKUuX\r\nZYa8zKnFXwQhrQ3AC43d27ytrEA1m5jQjXmxzbmcEZ135GWS5gTqAns+0RxS\r\nMozBDrzlbLM76LqI1v/kl9eTdXEMSKFleOU=\r\n=GRKD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "5fd2425602b752576bbe8089c343d5d70ebc861c", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v16.13.0+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@webassemblyjs/ast": "1.11.3", "@webassemblyjs/utf8": "1.11.3", "@webassemblyjs/leb128": "1.11.3", "@webassemblyjs/ieee754": "1.11.3", "@webassemblyjs/helper-wasm-bytecode": "1.11.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.11.3_1656762775731_0.5128088384055631", "host": "s3://npm-registry-packages"}}, "1.11.5": {"name": "@webassemblyjs/wasm-gen", "version": "1.11.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.11.5", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "ceb1c82b40bf0cf67a492c53381916756ef7f0b1", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.5.tgz", "fileCount": 3, "integrity": "sha512-14vteRlRjxLK9eSyYFvw1K8Vv+iPdZU0Aebk3j6oB8TQiQYuO6hj9s4d7qf6f2HJr2khzvNldAFG13CgdkAIfA==", "signatures": [{"sig": "MEQCIDvcICKx9TJzqQuGRKdE2bNC8UX3GWa4TUWxdx8Y/MoWAiBi+ZeZUtKbLAQfCMpLOLpNIDOkDR10jI3GKhCmPuGdkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO6BxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV4g/9EmBoVoZbyGH9Rzt1W+zOdlX2Z5D9o3h+vjq2jNSfQMU6u5Nv\r\nw8bdQkWBcSB+tNJUH8ZQFMAY6JnodaAqWXIPN6EwmA1Su8lURSo8ybN0HM+l\r\nOHoh0I191sIPPuBxy/C9N8iPw0Vsrj13RoqiW5UtUP//YnIXc+nwItHU9/nM\r\nRPfyyrbJXBwDurCM+EVanh+S+5KvsIfUvLqAJzsN4iY+v80NmIk72BgPXmFT\r\n0BIrI5yXyU4M2FV/+nZgKkNN1z8UMqFv2gO1VFyU9y2Y/lB5OhReHbgb7D5S\r\nhW+j2eaOJxwzcawCkQprwCo4drgEA4FCDeW5/J16rId4cStvA6UbCvAwo6VW\r\nt5zRlUwbUzrx5xN+ush4UJYpLtafmgDfZm2sdGPv5KJIFVibx9Txs9E7vubq\r\njUpwERljwl/u07K8Ws36KemG67SxI5ifp8DhMH/WBA1J0BitUNcyM61YuWRs\r\nS9TEyskSgzgnMB5kUVIo7ZNO4i7xX1zas7WFOdV6hYlVzrMfBIaY6Juu3szw\r\neT8lmEl5X1SySFavD/ZYueD5WTK/Md3AItJcDvPaCwFUZOb6wxk1BwPFZk+1\r\nfJfxUiCS4tv4S1tZiuzsJHu5RgnLA4a6DZGAxwQxpjX+XtP3FM/CM49JT2RE\r\nzH1hRYP3GYocajY1sDoI6wqt7o9/5se2+qs=\r\n=9pHy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cc856f3cc847a69c31e92ceb2c6527e1d30a9511", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.5", "@webassemblyjs/utf8": "1.11.5", "@webassemblyjs/leb128": "1.11.5", "@webassemblyjs/ieee754": "1.11.5", "@webassemblyjs/helper-wasm-bytecode": "1.11.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.11.5_1681629297093_0.4492744573784526", "host": "s3://npm-registry-packages"}}, "1.11.6": {"name": "@webassemblyjs/wasm-gen", "version": "1.11.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.11.6", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "fb5283e0e8b4551cc4e9c3c0d7184a65faf7c268", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.11.6.tgz", "fileCount": 3, "integrity": "sha512-3XOqkZP/y6B4F0PBAXvI1/bky7GryoogUtfwExeP/v7Nzwo1QLcq5oQmpKlftZLbT+ERUOAZVQjuNVak6UXjPA==", "signatures": [{"sig": "MEUCIQCpUlN5Y/K6rc58jOyLsIX0rgUEgnn3CkMyt+wgzjbVrgIgZgjolRAz9v5JOYD3OFUE5O33/awSoXJsNPq5pEzGWbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16049}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "58d40904ea7de2dd17f6f8d894ebe611b812a4db", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "19.1.0", "dependencies": {"@webassemblyjs/ast": "1.11.6", "@webassemblyjs/utf8": "1.11.6", "@webassemblyjs/leb128": "1.11.6", "@webassemblyjs/ieee754": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.11.6_1683645084220_0.07153283131046106", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.12.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.12.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a6520601da1b5700448273666a71ad0a45d78547", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.12.1.tgz", "fileCount": 6, "integrity": "sha512-TDq4Ojh9fcohAw6OIMXqiIcTq5KUXTGRkVxbSo1hQnSy6lAM5GSdfwWeSxpAo0YzgsgF182E/U0mDNhuA0tW7w==", "signatures": [{"sig": "MEUCIB/N+aqreq4RjzKlW4TCqpxSO1fc/jMNyOj5nDxmQA+FAiEA6OVxAK3HxmG+9Y2SoYX6BGJ9M4FHY0Z+a/7y6KTf99I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28065}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "67c75cf2da65e5588b5e81c10b0d7692d174760b", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v18.18.2+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/utf8": "1.11.6", "@webassemblyjs/leb128": "1.11.6", "@webassemblyjs/ieee754": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.12.1_1710325156920_0.0691894163610387", "host": "s3://npm-registry-packages"}}, "1.13.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.13.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.13.1", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "a821f9a139b72da9614238ecddd3d7ae2a366f64", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.13.1.tgz", "fileCount": 6, "integrity": "sha512-AxWiaqIeLh3c1H+8d1gPgVNXHyKP7jDu2G828Of9/E0/ovVEsh6LjX1QZ6g1tFBHocCwuUHK9O4w35kgojZRqA==", "signatures": [{"sig": "MEUCIQDoEb7mPaVNxq4Vh/ferII2EtbyysHZ2OrJZs8gKc+ebQIgCX/TMIqiWXwxBcaNYBX3bbl8lZ1VPKm37ntKpRVTdpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28065}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "cfe35c57093d414839b9350398369b78d97815b4", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.1", "@webassemblyjs/utf8": "1.12.1", "@webassemblyjs/leb128": "1.12.1", "@webassemblyjs/ieee754": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.13.1_1730912130488_0.3118123361234437", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "@webassemblyjs/wasm-gen", "version": "1.13.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@webassemblyjs/wasm-gen@1.13.2", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://github.com/xtuc/webassemblyjs#readme", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "dist": {"shasum": "76b0e9bf7df10de2915337abb20ea451638148b3", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.13.2.tgz", "fileCount": 6, "integrity": "sha512-69PwsnXaYwUvHeh7V/XKg3zxMECPBZZuymTH3nzFDQGfGUg7TR2n3q1x73FdQiwI/k/jJ6xsVxc67X2WmFiwnA==", "signatures": [{"sig": "MEUCICjuXGuDdX1d83rfVXdk4p8FML8gQ2AYdDNlbKnR+aW1AiEA9IyKuhODBQn8+4m0KH55btyF1FwNyz2NqPBMcSquqzo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28065}, "main": "lib/index.js", "module": "esm/index.js", "gitHead": "897aeb784f042a46a00626f1d1cca96159aa5db3", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/xtuc/webassemblyjs.git", "type": "git"}, "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "description": "WebAssembly binary format printer", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"@webassemblyjs/ast": "1.13.2", "@webassemblyjs/utf8": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/wasm-gen_1.13.2_1730929435887_0.07352892088263752", "host": "s3://npm-registry-packages"}}, "1.14.1": {"name": "@webassemblyjs/wasm-gen", "version": "1.14.1", "description": "WebAssembly binary format printer", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea", "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "homepage": "https://github.com/xtuc/webassemblyjs#readme", "_id": "@webassemblyjs/wasm-gen@1.14.1", "_nodeVersion": "21.7.1", "_npmVersion": "lerna/5.1.6/node@v21.7.1+x64 (linux)", "dist": {"integrity": "sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==", "shasum": "991e7f0c090cb0bb62bbac882076e3d219da9570", "tarball": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz", "fileCount": 6, "unpackedSize": 28065, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEml6kkAYyw27l4Vx8BtluRAej6waVoF/dbGMKOWdLOlAiBeCSxuI3YAoivltsVG0shDaQQAr7quR/owy6PWRndXww=="}]}, "_npmUser": {"name": "xtuc", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wasm-gen_1.14.1_1730930018094_0.5782532479780951"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-02-26T14:16:33.499Z", "modified": "2024-11-06T21:53:38.499Z", "1.4.1": "2018-02-26T14:16:33.629Z", "1.0.0-y.7": "2018-02-26T14:16:50.972Z", "1.0.0-y.8": "2018-02-26T14:27:43.716Z", "1.0.0": "2018-02-27T19:30:13.907Z", "1.1.0": "2018-03-05T09:28:07.189Z", "1.1.1": "2018-03-05T10:27:00.705Z", "1.1.2-y.0": "2018-03-05T16:16:15.934Z", "1.1.2-y.1": "2018-03-08T18:10:13.276Z", "1.1.2-y.2": "2018-03-09T07:57:58.997Z", "1.1.2-y.3": "2018-03-09T08:09:09.151Z", "1.1.2-y.4": "2018-03-09T08:15:39.003Z", "1.1.2-y.5": "2018-03-09T10:18:37.345Z", "1.1.2-y.6": "2018-03-09T10:29:14.132Z", "1.1.2-y.7": "2018-03-09T17:07:28.361Z", "1.1.2-y.8": "2018-03-12T10:22:37.281Z", "1.1.2-y.9": "2018-03-12T11:04:50.291Z", "1.1.2-y.10": "2018-03-12T16:12:16.986Z", "1.2.0": "2018-03-15T07:35:21.271Z", "1.2.1": "2018-03-15T12:49:33.311Z", "1.2.2": "2018-03-29T11:47:36.272Z", "1.2.3": "2018-04-07T10:26:49.933Z", "1.2.4": "2018-04-13T10:31:32.453Z", "1.2.5": "2018-04-13T17:07:02.931Z", "1.2.6": "2018-04-17T15:19:53.985Z", "1.2.7": "2018-04-30T14:43:12.503Z", "1.2.8": "2018-05-02T17:24:43.012Z", "1.3.0": "2018-05-04T13:30:54.884Z", "1.3.1": "2018-05-07T18:08:57.196Z", "1.3.2": "2018-05-08T18:47:08.608Z", "1.3.3": "2018-05-09T07:53:04.373Z", "1.4.0": "2018-05-09T14:58:19.140Z", "1.4.2": "2018-05-11T14:12:56.666Z", "1.4.3": "2018-05-12T09:20:59.175Z", "1.5.0": "2018-05-14T16:46:59.519Z", "1.5.1": "2018-05-16T11:24:35.743Z", "1.5.2": "2018-05-17T12:50:19.811Z", "1.5.3": "2018-05-21T11:08:13.179Z", "1.5.4": "2018-05-21T12:57:02.757Z", "1.5.5": "2018-05-24T08:01:57.100Z", "1.5.6": "2018-05-24T14:21:28.681Z", "1.5.7": "2018-05-25T12:54:29.443Z", "1.5.8": "2018-05-28T12:49:05.066Z", "1.5.9": "2018-05-29T13:13:29.533Z", "1.5.10": "2018-06-01T13:21:06.984Z", "1.5.11": "2018-06-06T08:58:28.891Z", "1.5.12": "2018-06-07T09:25:39.751Z", "1.5.13": "2018-06-30T13:44:50.991Z", "1.6.0": "2018-07-16T09:05:33.881Z", "1.7.0-0": "2018-07-18T12:58:20.752Z", "1.7.1-0": "2018-07-18T13:09:42.098Z", "1.6.1": "2018-07-18T13:51:02.297Z", "1.7.0-1": "2018-07-18T15:00:30.739Z", "1.7.0-2": "2018-07-18T15:15:49.651Z", "1.7.0-3": "2018-07-18T19:13:35.864Z", "1.7.0": "2018-07-19T16:01:19.809Z", "1.7.1": "2018-07-19T16:32:00.690Z", "1.7.2-0": "2018-07-19T18:10:05.550Z", "1.7.2-1": "2018-07-19T18:24:52.736Z", "1.7.2": "2018-07-19T18:33:52.866Z", "1.7.3": "2018-07-23T07:02:43.079Z", "1.7.4": "2018-07-24T07:02:32.990Z", "1.7.5": "2018-08-16T14:54:31.791Z", "1.7.6": "2018-09-10T14:17:40.225Z", "1.7.7": "2018-09-19T05:39:30.707Z", "1.7.8": "2018-09-21T07:38:08.677Z", "1.7.9": "2018-10-18T16:43:14.059Z", "1.7.10": "2018-10-23T09:37:49.706Z", "1.7.11": "2018-10-30T18:01:21.112Z", "1.8.0": "2018-12-11T07:24:42.280Z", "1.8.1": "2019-01-15T08:21:39.857Z", "1.8.2": "2019-02-14T16:22:06.189Z", "1.8.3": "2019-02-18T08:37:27.803Z", "1.8.4": "2019-02-19T17:27:43.407Z", "1.8.5": "2019-02-24T10:43:11.440Z", "1.9.0": "2020-02-01T23:20:14.086Z", "1.9.1": "2020-10-03T20:08:14.087Z", "1.10.0": "2021-01-07T16:18:30.773Z", "1.10.1": "2021-01-07T16:45:26.737Z", "1.11.0": "2021-01-07T18:11:46.001Z", "1.11.1": "2021-07-05T08:22:18.807Z", "1.11.3": "2022-07-02T11:52:55.841Z", "1.11.5": "2023-04-16T07:14:57.251Z", "1.11.6": "2023-05-09T15:11:24.425Z", "1.12.1": "2024-03-13T10:19:17.077Z", "1.13.1": "2024-11-06T16:55:30.650Z", "1.13.2": "2024-11-06T21:43:56.097Z", "1.14.1": "2024-11-06T21:53:38.321Z"}, "bugs": {"url": "https://github.com/xtuc/webassemblyjs/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/xtuc/webassemblyjs#readme", "repository": {"type": "git", "url": "git+https://github.com/xtuc/webassemblyjs.git"}, "description": "WebAssembly binary format printer", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}], "readme": "ERROR: No README data found!", "readmeFilename": ""}