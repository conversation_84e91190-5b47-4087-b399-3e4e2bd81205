{"name": "psl", "dist-tags": {"next": "1.13.0-beta.0", "latest": "1.15.0"}, "versions": {"1.0.0": {"name": "psl", "version": "1.0.0", "devDependencies": {"tape": "^3.0.0", "grunt": "^0.4.5", "JSONStream": "^0.9.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-compress": "^0.12.0"}, "dist": {"shasum": "c8aa198a2150e24424ad4410ec55e68d8409df6d", "tarball": "https://registry.npmjs.org/psl/-/psl-1.0.0.tgz", "integrity": "sha512-oTx1/CAE7FgPPMw79KmhBsYmFf3LOUALvTiIMH5U7Ub64QORHIyoaUgiVPOJi6xybAgueosrMe3dDH05MJ5kgg==", "signatures": [{"sig": "MEQCIGi+c4V+4oYBDYPwLM67s8Ve7yKQM2LRGzC88jznGQUpAiAUJkDRxqxs1dgDrf0KJajxPyYpuZI00MsziCsZeIR3RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "psl", "version": "1.0.1", "devDependencies": {"tape": "^3.0.0", "zuul": "^1.11.2", "grunt": "^0.4.5", "coverify": "^1.0.7", "derequire": "^1.2.0", "phantomjs": "^1.9.10", "JSONStream": "^0.9.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-compress": "^0.12.0"}, "dist": {"shasum": "5daf63da7c284890ac87918e3987fa1e044cff5b", "tarball": "https://registry.npmjs.org/psl/-/psl-1.0.1.tgz", "integrity": "sha512-SwTjJ2GVlPx/5WhzDH7JsRr2wTRTXqK0oKiPT1h2FO0xVdCvq2D2IIowjVDnDZMz9foj4w9NSGwQ+XAyFbSy3w==", "signatures": [{"sig": "MEQCICRvcsFzBmt7XZJ4MXjEwcN9Y1IzxES2Vme/1f722gNuAiBrtra8f2JZlZ/SwAYDWpEA56hPJ0VljH1ks6JVukIUZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "psl", "version": "1.0.2", "devDependencies": {"tape": "^3.0.0", "zuul": "^1.11.2", "grunt": "^0.4.5", "coverify": "^1.0.7", "derequire": "^1.2.0", "phantomjs": "^1.9.10", "JSONStream": "^0.9.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-compress": "^0.12.0"}, "dist": {"shasum": "f6be8a1c6e04aca52198ed8edae3f7ffa4f177f9", "tarball": "https://registry.npmjs.org/psl/-/psl-1.0.2.tgz", "integrity": "sha512-AxcjMXwLomrFCs4SMjr+4VwYI0z1QrhdMTDIIvZ63eVWxHcv6uFNMNsU9eKSHGe/JcDUlw/VgSUey9ihEnpw7Q==", "signatures": [{"sig": "MEUCIFcko1m+VVo4LCaCzVyaviYftPCDDDrtCyJD9RU9ERlMAiEAlisiAY+txwKeuafCL2Fj2AM22di7IZ08nxs+BsIHx4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "psl", "version": "1.1.0", "devDependencies": {"tape": "^3.0.0", "zuul": "^1.11.2", "grunt": "^0.4.5", "coverify": "^1.0.7", "derequire": "^1.2.0", "phantomjs": "1.9.10", "JSONStream": "^0.9.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-compress": "^0.12.0"}, "dist": {"shasum": "3fd3eff8b30360060613ebb57424c3eecff436e6", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.0.tgz", "integrity": "sha512-vf08HFpNMOnw9N1SSI0sfalskNRjyjdNQFfZgZNcL/D61PTBizG3HD3kDVvIp8/QisASsKO38mBLWeSt9pa3wA==", "signatures": [{"sig": "MEYCIQDnl1OeN12DAfMH194bYE0zfiyhz70esK8AagKlTfIp9wIhANhcSShIxccmkI3MHTJXVfpf1qLxW7gCgdnRBdSAaGh9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "psl", "version": "1.1.1", "dependencies": {"request": "^2.51.0"}, "devDependencies": {"tape": "^3.0.0", "zuul": "^1.11.2", "grunt": "^0.4.5", "coverify": "^1.0.7", "derequire": "^1.2.0", "phantomjs": "^1.9.13", "JSONStream": "^0.10.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-compress": "^0.12.0"}, "dist": {"shasum": "7dc112b29da9dff21b46b94f636ab7e3f8ff0226", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.1.tgz", "integrity": "sha512-gM7MXvU3jCjs1PL4NUFFnB2OgFAN115v1t3EuMq1jHk+FDo/gZ5rgCY3/Wb9gdxNsm1CuH2JNw0yLWZ9DEyg4Q==", "signatures": [{"sig": "MEUCIQCboc8osANpmOtAIR21HttDV6Fr1uUB8vVNKn2N4acJnAIgZTx5FNWDAjbfKARBasJFA7TkrPFUKlBV4J0lCvtlTEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "psl", "version": "1.1.2", "devDependencies": {"tape": "^3.0.0", "zuul": "^1.11.2", "grunt": "^0.4.5", "request": "^2.51.0", "coverify": "^1.0.7", "derequire": "^1.2.0", "phantomjs": "^1.9.13", "JSONStream": "^0.10.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.10.0", "grunt-contrib-uglify": "^0.6.0", "grunt-contrib-compress": "^0.12.0"}, "dist": {"shasum": "744199ac2388e38285d0c68889b6d9e791067328", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.2.tgz", "integrity": "sha512-XnF9MBsBcuHYi7XfFSnrbmNiGWVlusPWF3jzyorbJ/g5j27eMssvV5G5tZ7m2QsHapAylEjYn18qIGC3lKruAQ==", "signatures": [{"sig": "MEYCIQDDVI9Y75J/bQ3NtKVT+1VSKw3K3NeblKF35FU4t1j93gIhAJ1udiLAA10He4cQGPKS/oXFUdrDLhHtjcLfshTmP4Ex", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "psl", "version": "1.1.3", "devDependencies": {"tape": "^3.0.0", "zuul": "^1.11.2", "grunt": "^0.4.5", "request": "^2.51.0", "coverify": "^1.0.7", "derequire": "^1.2.0", "phantomjs": "^1.9.13", "JSONStream": "^0.10.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.7.0", "grunt-contrib-compress": "^0.13.0"}, "dist": {"shasum": "2d9a63d15dc451ae32c3ce0dcb115cda3696c01b", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.3.tgz", "integrity": "sha512-mcgl6J5Itn873kR7uUrA0MxGtYRRHIHWYJv9oEyr+Pt0tUjiazoueviOSuENsTiQI9ZZoLM4d0K2k9OpMYBZ8g==", "signatures": [{"sig": "MEUCIQDNvbb5eHw/93oNYoZDtm5cWf2RyfH7zoJ2G8yQT0zOrwIgXKYNP690NcrZrkgJEbjmv5pBDAhUJBTeUeNRbqz3Hv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.4": {"name": "psl", "version": "1.1.4", "devDependencies": {"tape": "^4.0.0", "zuul": "^2.1.1", "grunt": "^0.4.5", "request": "^2.51.0", "coverify": "^1.0.7", "derequire": "^2.0.0", "phantomjs": "^1.9.13", "JSONStream": "^0.10.0", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-compress": "^0.13.0"}, "dist": {"shasum": "f498b1d346be589c14f6c37d71c4c999bfe65266", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.4.tgz", "integrity": "sha512-moxNqM5OtsoSbD86sq3/EAPJjQBdt2jTQpCc1MziwyjXxavhOc7mt8o9+Z5f+yjtJlLit73uVa+HQQIK8Kvp3Q==", "signatures": [{"sig": "MEYCIQD5dN9qN7q5pjF8PlFAH9bglkZEdw0oFC5h0fIjAM/eVAIhALxu9x1b8eefYlEUXwyboPxHJN8N+GfgNBQ8PJv4WSrz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.5": {"name": "psl", "version": "1.1.5", "devDependencies": {"tape": "^4.0.0", "zuul": "^3.0.0", "grunt": "^0.4.5", "request": "^2.51.0", "coverify": "^1.0.7", "derequire": "^2.0.0", "phantomjs": "^1.9.13", "JSONStream": "^1.0.3", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.9.1", "grunt-contrib-compress": "^0.13.0"}, "dist": {"shasum": "9b7c23c0d9977e75874636395106ce54c82db412", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.5.tgz", "integrity": "sha512-g5smCKecqh7P6nvPwnlpUgCWr3FhYFk9ZFYvi944oYbH7q+q8sDMyGBAMnHCgzMvJYFCGpbAg5QcZ+HlDNj6+g==", "signatures": [{"sig": "MEUCIQCtxRvswoNcr5WyRaDxYSA+YRJljr3L5QV31O6An5eI4QIgdA7jrmCT2aQvKPnWLFqoxZ++NXy/2UM7kE7NIsW0DYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.6": {"name": "psl", "version": "1.1.6", "devDependencies": {"tape": "^4.0.1", "zuul": "^3.2.0", "grunt": "^0.4.5", "request": "^2.59.0", "coverify": "^1.4.0", "phantomjs": "^1.9.17", "JSONStream": "^1.0.4", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.9.1"}, "dist": {"shasum": "8980f0ac1ee4447f93e1aaa516c53ee05945d19e", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.6.tgz", "integrity": "sha512-2B0zCOWGZQtIn3wnS1QuSDdu4PWPhozM9DGb1gR9ufdzDlzmPQgr/Uj00dz0GqsxubdbgabeorzaizulOiVQAw==", "signatures": [{"sig": "MEUCICCYbbCumTSPcXl9xljCRP/9OGhrjy6zxDTdGs3Mr3rJAiEArKAjKYKC5+dBiPN1Zr24TaYJRodajiAd/8pGKA+yPeI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.7": {"name": "psl", "version": "1.1.7", "devDependencies": {"tape": "^4.0.1", "zuul": "^3.2.0", "grunt": "^0.4.5", "request": "^2.59.0", "coverify": "^1.4.0", "phantomjs": "^1.9.17", "JSONStream": "^1.0.4", "grunt-shell": "^1.1.1", "event-stream": "^3.1.7", "grunt-browserify": "^3.0.1", "grunt-contrib-clean": "^0.6.0", "grunt-contrib-watch": "^0.6.1", "grunt-contrib-jshint": "^0.11.0", "grunt-contrib-uglify": "^0.9.1"}, "dist": {"shasum": "54da24f2f2ace96d50b62984f2369a3c8c735f38", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.7.tgz", "integrity": "sha512-/MSraOLVQuKfZcrB2O/5GMmTFnAHlm8P2jyE2RuJuypu0mb1kPzUHmdO4zMVwGeVt0veOIgyWtW+V4Zm5PRQ+w==", "signatures": [{"sig": "MEQCIGQw4Pc4kVFUGBckVVHoPotwZ6l+17RYNPqc2lUI7I7sAiALDxBwlAdOKEuHke/3M3bplMV+W4ATWc7O2UyBfJ8ySQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.8": {"name": "psl", "version": "1.1.8", "devDependencies": {"gulp": "^3.9.0", "karma": "^0.13.10", "rimraf": "^2.4.3", "request": "^2.59.0", "coverify": "^1.4.0", "gulp-util": "^3.0.6", "phantomjs": "^1.9.17", "JSONStream": "^1.0.4", "browserify": "^11.2.0", "gulp-mocha": "^2.1.3", "gulp-jshint": "^1.11.2", "gulp-rename": "^1.2.2", "gulp-uglify": "^1.4.1", "karma-mocha": "^0.2.0", "event-stream": "^3.1.7", "vinyl-buffer": "^1.0.0", "jshint-stylish": "^2.0.1", "karma-browserify": "^4.4.0", "vinyl-source-stream": "^1.1.0", "karma-mocha-reporter": "^1.1.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "9591bb3c74265a34a82ea23750f7f3fb7426a646", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.8.tgz", "integrity": "sha512-oUVrw/rnB36KKPmhh/engri+XvScvHd25gSUmERbRqf1B4mumQyC0d2N6UQGlvPxSwks2WeSiOeh29x1W7Qd7A==", "signatures": [{"sig": "MEYCIQDkr9aaxO4dn15NgB6CJ9eTV/4PTsyu6SbjTbdsABgtcgIhAKTzQ4/xSzmTZcEg5LH2CLCSqYA3a+DJ1VIgH6RuUk4R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.9": {"name": "psl", "version": "1.1.9", "devDependencies": {"karma": "^0.13.10", "mocha": "^2.3.4", "eslint": "^1.10.3", "request": "^2.59.0", "uglifyjs": "^2.4.10", "phantomjs": "^1.9.17", "JSONStream": "^1.0.4", "browserify": "^12.0.1", "karma-mocha": "^0.2.0", "event-stream": "^3.1.7", "karma-browserify": "^4.4.0", "eslint-config-hapi": "^8.0.0", "eslint-plugin-hapi": "^4.0.0", "karma-mocha-reporter": "^1.1.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "abe59b52a0c6d1ab30798e0ab0ec6ffb1d34c4cd", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.9.tgz", "integrity": "sha512-s1EWQ12GQO/hUPCAI086YHpAivw/kn6E0RTfQz0wRzCRgKzMKY4twxYWd5ewYbKQGbw66hFDgtjlgBZES3PFoA==", "signatures": [{"sig": "MEUCIQDsWtEjmwBdDYnXI5FMnJQ0G4xkJ+x38q0gF5y+FkddJwIgUy05SiuOvJiPL32jjkN7UorQJFIIsyEYFbe8q1/652A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.10": {"name": "psl", "version": "1.1.10", "devDependencies": {"karma": "^0.13.19", "mocha": "^2.4.5", "eslint": "^1.10.3", "request": "^2.69.0", "uglifyjs": "^2.4.10", "watchify": "^3.7.0", "JSONStream": "^1.0.4", "browserify": "^13.0.0", "karma-mocha": "^0.2.0", "event-stream": "^3.1.7", "karma-browserify": "^5.0.1", "eslint-config-hapi": "^8.0.1", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.4", "karma-mocha-reporter": "^1.1.5", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7a5c0c22d1ae2baecb3055a9287ff3bc580ec15e", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.10.tgz", "integrity": "sha512-Hga12QaPuswyTX4OYf1j2OCxSwUcvFWHsns/NrNonPuv10blidXoCchRY0pMOgYc8PBZvu2oIygGtcbyvr/zRw==", "signatures": [{"sig": "MEQCIGoZP4DlwQbdQrE77TglhyFF7iYf6F9x/dk78KocjhQ1AiBwVyg6G2DlLW9UulYiuhSgxLB9wn6nFqirRmZwkNssFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.11": {"name": "psl", "version": "1.1.11", "devDependencies": {"karma": "^0.13.22", "mocha": "^2.5.3", "eslint": "^2.11.1", "request": "^2.72.0", "uglifyjs": "^2.4.10", "watchify": "^3.7.0", "JSONStream": "^1.1.1", "browserify": "^13.0.1", "karma-mocha": "^1.0.1", "event-stream": "^3.1.7", "karma-browserify": "^5.0.5", "eslint-config-hapi": "^9.1.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.7", "karma-mocha-reporter": "^2.0.3", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "fc527f464c4427a6006f8b164794b60fd2d18281", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.11.tgz", "integrity": "sha512-5zTa/3z7Oh06BeIKRdpaCA2WksXDp02x5k0lO/tAJ4J8cQTRsuf01PWp2c1FQNWbphIypbj+P8XIqeC4BIXe1g==", "signatures": [{"sig": "MEYCIQDbueJa9qvXW8IYg8W1CSdNo8KxlxNlKwJTW58qUi3O4wIhAOTdfuvvBGlM5hpH/6Aq+D4C0xbMwd58MrfcThDLRIHr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.12": {"name": "psl", "version": "1.1.12", "devDependencies": {"karma": "^1.1.1", "mocha": "^2.5.3", "eslint": "^3.1.0", "request": "^2.72.0", "uglifyjs": "^2.4.10", "watchify": "^3.7.0", "JSONStream": "^1.1.1", "browserify": "^13.0.1", "karma-mocha": "^1.0.1", "event-stream": "^3.3.4", "karma-browserify": "^5.0.5", "eslint-config-hapi": "^9.1.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.7", "karma-mocha-reporter": "^2.0.3", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "fa1db4a04fb08e222ecb54a36f05ee9c5c6dfb64", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.12.tgz", "integrity": "sha512-rH<PERSON>uvO0SMWSKqNm62OOUVQxpry2TKvBQGHuHBcKFX/+WmG1uFxr4aa7rm9tp62CBPsOW3LgjH7zbXyws9ywPg==", "signatures": [{"sig": "MEUCIF5nCv74/MdGwpSFxa184FgVEbl88Y1ZoltUKRxeTIS4AiEA/Rk9xyMpJNmk7A2BFMYzi1lJQDOVWi0K1cRAYaDdzPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.13": {"name": "psl", "version": "1.1.13", "devDependencies": {"karma": "^1.2.0", "mocha": "^3.0.2", "eslint": "^3.3.1", "request": "^2.74.0", "uglifyjs": "^2.4.10", "watchify": "^3.7.0", "JSONStream": "^1.1.4", "browserify": "^13.1.0", "karma-mocha": "^1.0.1", "event-stream": "^3.3.4", "karma-browserify": "^5.0.5", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.12", "karma-mocha-reporter": "^2.1.0", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "13b2b75e17965d9e7c5f2761d5d6ecdf2f09a90e", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.13.tgz", "integrity": "sha512-5FFMLrNBSdiNDjeA0n0VMR6z28Zs/Dla3akwwQBfBCkUNMYimaGmmQSS+MYRNnDxVyrKIOxo0PTFs34LSyAq3Q==", "signatures": [{"sig": "MEYCIQDA4C9b6n+/XxLi6EXAYiK2M1d36bKEXx5HnHNz1zwVmwIhAJcjjFv4SOxcVqARLp88sZZZApO0CqqFVCHCIjmU7s0T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.14": {"name": "psl", "version": "1.1.14", "devDependencies": {"karma": "^1.3.0", "mocha": "^3.0.2", "eslint": "^3.5.0", "request": "^2.74.0", "uglifyjs": "^2.4.10", "watchify": "^3.7.0", "JSONStream": "^1.1.4", "browserify": "^13.1.0", "karma-mocha": "^1.0.1", "event-stream": "^3.3.4", "karma-browserify": "^5.0.5", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.12", "karma-mocha-reporter": "^2.1.0", "karma-phantomjs-launcher": "^1.0.2"}, "dist": {"shasum": "d2a23a99ea429c8dede5614afec4cd29e5d6d0e1", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.14.tgz", "integrity": "sha512-wU/EtAPg/HtXBNdzQPfQzn+pL82ibrnGVBmL1d+k4ncHzAnS4AN3QTNi/Ygt1h9JVdYXBkCuIS2rywAjij+h1w==", "signatures": [{"sig": "MEQCIEdG/8MwDdBoA3CA62YXVM9Nomo3L75xI8OIZmA3ZwMpAiB9+aQkekz6IgMss2J1ixm0YZwfxM6mjsfJHHi9md+rlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.15": {"name": "psl", "version": "1.1.15", "devDependencies": {"karma": "^1.3.0", "mocha": "^3.2.0", "eslint": "^3.11.1", "request": "^2.79.0", "uglifyjs": "^2.4.10", "watchify": "^3.7.0", "JSONStream": "^1.2.1", "browserify": "^13.1.1", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.0.5", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.13", "karma-mocha-reporter": "^2.2.1", "karma-phantomjs-launcher": "^1.0.2"}, "dist": {"shasum": "4bcd312929a5a88657c35d23b425712fb0c74f00", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.15.tgz", "integrity": "sha512-5REecTLyHMzpjosNpoe8K1Yc59dd/Z+dJsS/YDKd9cmRBnuy3WDB+gCZYOa0FtAV/jDrtbvetHD5UH8diqLvBw==", "signatures": [{"sig": "MEYCIQCK+G0c/ADfP22RKyeuLFhM7Wdu9EbDQDoGei/CpB3ouQIhAMl8DdL2BOgpIVeSniEwiOqvpvnq7raGpDXXSKcwkZYG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.16": {"name": "psl", "version": "1.1.16", "devDependencies": {"karma": "^1.4.1", "mocha": "^3.2.0", "eslint": "^3.14.1", "request": "^2.79.0", "uglifyjs": "^2.4.10", "watchify": "^3.9.0", "JSONStream": "^1.3.0", "browserify": "^14.0.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.1", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.14", "karma-mocha-reporter": "^2.2.2", "karma-phantomjs-launcher": "^1.0.2"}, "dist": {"shasum": "dc88ca320b87ba79f6feefdf665a8d3113b499c1", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.16.tgz", "integrity": "sha512-dKoEtmduEFAi0nuKUkRXYoVScnoI4Qf3AHsLuqVPLRtz3dRJ2m/tw8XJHsKg1OxiXzwtfgY6ZlMphCHgF2yuHw==", "signatures": [{"sig": "MEUCIBAWg1vQ1pHcs/WaXiMThx7Fu6uRRu1fYRUdjyscw6VjAiEAglWwHY2oKMDaDERRws/Z7Js0rIFWV/6hsHwuz3nDY1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.17": {"name": "psl", "version": "1.1.17", "devDependencies": {"karma": "^1.5.0", "mocha": "^3.2.0", "eslint": "^3.17.1", "request": "^2.81.0", "uglifyjs": "^2.4.10", "watchify": "^3.9.0", "JSONStream": "^1.3.1", "browserify": "^14.1.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.1", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.14", "karma-mocha-reporter": "^2.2.2", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "a849efbdf89c9d3d1356d771c68afe27b11f4d6f", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.17.tgz", "integrity": "sha512-0a+nkrSI2Ms/GD+4GMOCq5VS4MpoK486/PkOq0dsjHN9DbS/UwVfR99sUY0H8mVClxtV6hM1wW/o8tfgt2BxDg==", "signatures": [{"sig": "MEUCIApsFAPfdCrCYoDHwIrYHvlItDvl4CtjWClQzjXbB70qAiEA8Gbid5GRLtjR2iH3wC242VRTLGLXWP72Fen8e0KYVVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.18": {"name": "psl", "version": "1.1.18", "devDependencies": {"karma": "^1.5.0", "mocha": "^3.2.0", "eslint": "^3.19.0", "request": "^2.81.0", "uglifyjs": "^2.4.10", "watchify": "^3.9.0", "JSONStream": "^1.3.1", "browserify": "^14.1.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.1", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.14", "karma-mocha-reporter": "^2.2.3", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "a8f2f5465a01e8acce4ff2d72342b05c7b507d90", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.18.tgz", "integrity": "sha512-DcoK764Cs0KR0pppZPs7rJ6gH6IAbxygODlzZF1UGLIcSs0MmVODy2gzvUBPbZNeOkZlTBpMqv8UDLEU/6ouyw==", "signatures": [{"sig": "MEQCIF7iDJNrCreM2v1wHXeRZ9Mkh+RFMpHKMs2YUjMtMgKXAiAs5+8ocE2m1MMtItYK/z0CbVoIvO6Bp3o1Edv6UvWNwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.19": {"name": "psl", "version": "1.1.19", "devDependencies": {"karma": "^1.7.0", "mocha": "^3.4.2", "eslint": "^3.19.0", "request": "^2.81.0", "watchify": "^3.9.0", "uglify-js": "^3.0.15", "JSONStream": "^1.3.1", "browserify": "^14.4.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.1", "eslint-config-hapi": "^10.0.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.14", "karma-mocha-reporter": "^2.2.3", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "6617b415a34a82bf6580de4d0cfd6003e3d943c2", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.19.tgz", "integrity": "sha512-DHYONy1fq6WX029BOLRtpFQMOne/d3KdIKXaCfXLZHma96lM+825BVlRwyFVQDgg9u27mEgSRZ2EHC7vdSDNsQ==", "signatures": [{"sig": "MEQCIEZY+3i5/EXcAtLmInk3h6Tlg/igiUYh3Mbh0AYEb574AiBmuEgGw3IIgGw2eEGNaKGyulErZFjDuG4Xdo3x7YYCIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20": {"name": "psl", "version": "1.1.20", "devDependencies": {"karma": "^1.7.0", "mocha": "^3.5.0", "eslint": "^4.1.0", "request": "^2.81.0", "watchify": "^3.9.0", "uglify-js": "^3.0.28", "JSONStream": "^1.3.1", "browserify": "^14.4.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.1", "eslint-config-hapi": "^10.1.0", "eslint-plugin-hapi": "^4.0.0", "phantomjs-prebuilt": "^2.1.15", "karma-mocha-reporter": "^2.2.3", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "363382f332388880b155e2506345957084288e9d", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.20.tgz", "integrity": "sha512-JWUi+8DYZnEn9vfV0ppHFLBP0Lk7wxzpobILpBEMDV4nFket4YK+6Rn1Zn6DHmD9PqqsV96AM6l4R/2oirzkgw==", "signatures": [{"sig": "MEQCIHgPbIWXLof2Gb6qFG5pYef/EuOKfj9Ll2hLFK+YEs3dAiAtu5Jj8eknIB1IX5tJOJVIEWpOFf7ZIHafmOzAdE6sEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.21": {"name": "psl", "version": "1.1.21", "devDependencies": {"karma": "^1.7.1", "mocha": "^4.0.1", "eslint": "^4.12.1", "request": "^2.83.0", "watchify": "^3.9.0", "uglify-js": "^3.2.1", "JSONStream": "^1.3.1", "browserify": "^14.5.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.2", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "c8f9340ee7cee997fcd1aff2d47421cf4ab8326f", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.21.tgz", "integrity": "sha512-nZvs33Q3rSTp86GHPTsKbtgRogmbabAt1ft3Gpv6V9024jgLqmhx22MIjWrg4LR8bhFloV71/f9uSh1eBK0DYg==", "signatures": [{"sig": "MEUCIAaYw7qcgtfLnI8SjJzXbYlD28OjJ+lu3S2C0YNkQPHWAiEAtvG+g8TWwBn8KA6Yav0d1/calA4B/S/s9N45frQ64Sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.22": {"name": "psl", "version": "1.1.22", "devDependencies": {"karma": "^2.0.0", "mocha": "^4.1.0", "eslint": "^4.14.0", "request": "^2.83.0", "watchify": "^3.9.0", "uglify-js": "^3.3.4", "JSONStream": "^1.3.2", "browserify": "^14.5.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.2", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "3fc5fe82c8c10146292b17dc88a75cbb11e234c9", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.22.tgz", "integrity": "sha512-<PERSON><PERSON>jqjckzNtRcKM7ux9SKPewxi7Q+hflHYAc0UxedyqGydM3RXd5g95kGWK1nc6g7kn0UwYhBnOpzDIt171pXeA==", "signatures": [{"sig": "MEYCIQD+CXk+HuRUU8AtUzwfc9IabMveocF2xyr7qR8/z6F4+QIhAPy/POprwEJoQhO7/+zlSdw0bcmNlJg+XqxtmX7rje2a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.23": {"name": "psl", "version": "1.1.23", "devDependencies": {"karma": "^2.0.0", "mocha": "^5.0.0", "eslint": "^4.17.0", "request": "^2.83.0", "watchify": "^3.10.0", "uglify-js": "^3.3.9", "JSONStream": "^1.3.2", "browserify": "^15.2.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.1.3", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "8ce59c003a87dc6c44e03a49046368590390286b", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.23.tgz", "integrity": "sha512-3ZBh95p6JYOIHcx13GIHmA0T/6O5pabFgoR+3JnX8Z3swoTFLe5S1V6CDUppkyOJi3c5kwEQMHgenA6cx72H4Q==", "signatures": [{"sig": "MEYCIQDQHddCqhTxKel8NBSD0OtRQW7D1426R3waiOo2zKPhBQIhAKmInZyD3LQAxjkg/R2z80LjZvBzkEO9JLO4irMlQZqk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.24": {"name": "psl", "version": "1.1.24", "devDependencies": {"karma": "^2.0.0", "mocha": "^5.0.1", "eslint": "^4.18.0", "request": "^2.83.0", "watchify": "^3.10.0", "uglify-js": "^3.3.11", "JSONStream": "^1.3.2", "browserify": "^16.1.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.2.0", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "06c70e9c5145b72ed888b318a21f719d1823512b", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.24.tgz", "fileCount": 11, "integrity": "sha512-9/GC+4tA28JSZSgVThFUZlYcYX0zloBP2/DLHanwwM9zOfHzsFwHxOl4EE8CTWNbegF74pNwyhx+/uqMeLbk0Q==", "signatures": [{"sig": "MEUCIQDf79Zpy+AOt431DoREigMiNd3FUeswbcG3C2mwPAxcXQIgOJphPu/fWHJ/hCzh6ImiFxttKO3GkggD7OzdL9eM1AI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 524806}}, "1.1.25": {"name": "psl", "version": "1.1.25", "devDependencies": {"karma": "^2.0.0", "mocha": "^5.0.4", "eslint": "^4.18.2", "request": "^2.85.0", "watchify": "^3.11.0", "uglify-js": "^3.3.14", "JSONStream": "^1.3.2", "browserify": "^16.1.1", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.2.0", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "135edc9c7ba48cd8e4a6903fded8c7f348a5d2ee", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.25.tgz", "fileCount": 11, "integrity": "sha512-Djug/g0La/23cfyh1GujTbrLs/dhUxEquv78at1zHs03oglR1FP54v1nr8J7nCKxLEs1tsNP0u3DGVrugGi/kA==", "signatures": [{"sig": "MEUCICMvg7I5dsmHKgDq0MHspbSu/ZMhu9bGS5bNJpbY4ZCnAiEAxufUcoEoMkCLUNNPlvNOVOU/fZt50c1d9O2ZyTKhDzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 525147}}, "1.1.26": {"name": "psl", "version": "1.1.26", "devDependencies": {"karma": "^2.0.2", "mocha": "^5.1.1", "eslint": "^4.19.1", "request": "^2.85.0", "watchify": "^3.11.0", "uglify-js": "^3.3.23", "JSONStream": "^1.3.2", "browserify": "^16.2.0", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.2.0", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "b2d3248185d7d27ff616ba1536851db0b59d9e75", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.26.tgz", "fileCount": 11, "integrity": "sha512-nEpCZWXUloiAVG7Prmi+3E4xwpQ948TkT/gXYqaahGIRXyawSem+oOuNeqcWczO3Fz2SvEKM1FDWKl1RrGXeBw==", "signatures": [{"sig": "MEUCIBVHNi7LVB7DD+x8vr5Ip7+PI++eWcmOPvDgHRYLlaj3AiEA0f5shztrQeccTtrGqtQ4Sk5Bk/TQFba+HwgxdrBhq80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7Nk2CRA9TVsSAnZWagAAmwoP+gM6WxBPuzBiInJe8mVb\ngfm6PMfP2eHaIaPaHZwzmxM/iT2UeXXMIS9fykj+tjcaLpjDg4DfbF24szNq\nxXisNfV3RcTcmO0CokxoBAhkY0satrCU3vhBsWkFhhsA+cf5gHZdNH0QN783\nAD8M4F4w6NqUw7NcE+/ev2cHYUpfHr7s4raQPZqiqsw+4S/Cb3RfDTSWVFPF\nynjWqanzzgPAyoUINUaleIZ9HG81MCtC8FkfH1qtyzDAqTjaxu5ZMcI7M0ZQ\nu6klWzW6omzShXVAfKjScBCSxEaLWSociNpvK8RAXL4uGZI6LtKWN0eejCr9\n4a4pW7M6h3JgfyTwJoTXAJEfCMJc+q5/2Nz1bltjccUU/KrGqMpo7+Pjmv1v\nbneuS+pp8PXQzXijDUrr+Hvii+Ejs3FYytx750k13lnGXP7MPfe4POM+kfAm\nXhjU8scyVIDkN95DGvhMa7C6E0ynOnU/M3Z41fGuFglpjsFSQThSyU+23MOJ\njTr9uPVLwt3LbFGNSO+5Fn4JVWjjDIRHZYK/SHrZ3yMCCGN18dITYObCXHDZ\njJ60dPaBJ8wgHe96rbgSfhgUbBbZipbHMbt1y94mnZllujsfxvl8yvaQMbqm\nQRKSqKWqH56YFbh1kf2QAkRe4LPrAz07yVBnD6yVrToqo62z1F1eokB3qbCh\nEFcJ\r\n=3fI5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.27": {"name": "psl", "version": "1.1.27", "devDependencies": {"karma": "^2.0.2", "mocha": "^5.2.0", "eslint": "^4.19.1", "request": "^2.87.0", "watchify": "^3.11.0", "uglify-js": "^3.3.26", "JSONStream": "^1.3.2", "browserify": "^16.2.2", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.2.0", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "2b2c77019db86855170d903532400bf71ee085b6", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.27.tgz", "fileCount": 11, "integrity": "sha512-J8tJX5tAeEp9tQTI2w2aMZ6V1INuU4JmNaNPRuHAqjjVq3ZJ+jV3+tcT3ncgTnBxvwJy740IB/WZrxFus2VdMA==", "signatures": [{"sig": "MEYCIQCSP832i4CrZm1MT0jGOP/B2LgZKKBK/mq/x1sD4wEeMQIhANZKXjn/NTUZUfmqOdox+zkD6ROBxy8AjEoZBvFUGH7m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBCFXCRA9TVsSAnZWagAAgEAQAILNbkU3DMd41OaoG97W\nx+5yDpu63+11y619gZ1KoaQCw1SRbOD4FrOJfuDaca0BEEtZ2A69mbRjMstg\nrN75hs+Fdoi+xSeRJf9xnU09Fr/JDz+i46rUe91aL4r/KfRNzcOYxZhkNwyr\nMN9Vd8dkXMEv0dELJbRYhaaHcfTNXhcZ80eLBQM5RFu+tSgCLxHWzSBjc0vG\n/16DXkqvySjFmcD59CaL+hd4zX1JgP0XZ0hEPEnzdE3s27z5766FY0l6kNBr\n2PJ8FdL8E9FcATrNd4WyKyFcHnspPMe31rh9ECBQkDo1n8soJd8Ox1nz0SiY\ndIZ5zRWnHQ6vMUbDvZ+vV2hdYk8H/7w7WRKpEBue9+yB63le4yUjI4LWkXwR\nOxULIOZCiFkMRSSm5ZBYOIDR6zjuLguNdgUcmqt6nhAGuWtnXYsp+OxS9BIF\nerZCvehKUJzTrH/nxPkwbrb4BaX3X7HCxTEj6+u3+z0X9SLWd0oMT6Nv6vaH\nD/r2uJ2C427zN2ugRbp07kbomuXjS1RMmmTpJ9WgwaD3qcwibjZGl87ob8F6\nWx2asyvZd0RpSe/rmB2Jn9j8XFIjA2iWvy6VgHj6TSvm9YL2RFNtuWgp5eYC\nKctJ7k9JLH3F82OvH/OzfuWMNzaMjXFk2qvFPQCkbHxaI/Sj8gZSAVC2AsVk\njLEm\r\n=y9t2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.28": {"name": "psl", "version": "1.1.28", "devDependencies": {"karma": "^2.0.2", "mocha": "^5.2.0", "eslint": "^4.19.1", "request": "^2.87.0", "watchify": "^3.11.0", "uglify-js": "^3.4.0", "JSONStream": "^1.3.3", "browserify": "^16.2.2", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.3.0", "eslint-config-hapi": "^11.1.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "4fb6ceb08a1e2214d4fd4de0ca22dae13740bc7b", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.28.tgz", "fileCount": 11, "integrity": "sha512-+AqO1Ae+N/4r7Rvchrdm432afjT9hqJRyBN3DQv9At0tPz4hIFSGKbq64fN9dVoCow4oggIIax5/iONx0r9hZw==", "signatures": [{"sig": "MEQCID/TYmrUyEJinmuJbQmoKz7a6hploUP17WNYjyJP1HB7AiARRyo8k1q3Z7afVMCdowkbmubVbVl6tCC5Ery0GL3UEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIEl2CRA9TVsSAnZWagAAu4cP/RrdoYLi0++yBeg+6xnO\nZ8N+O4SYEUc5M4UKScbUQmdCjQljd2SUZGyLms2SmjMDtSWMWI7MODjpCrUu\n7+XrU9vyioLp85oWDbGIXVeX5VxbmBE1Jo8knEb9d0lzrOdXO1zug8LjLXNk\n+SmNCRwt9/XXQVBF63HaNW7utVtFwNOq+A/VJYHVSZ2V/Aps3FWg12hfnK80\n7hJGaFRGwXL9GXTmNadEh4/XnCc0wP9HAypX/kDCO7PGMFkYkZezqQZtspCF\nhTLh7slNXuROBnk/AY7d3m/w0brk3NWBvJWqQNECd7NzrUgBRlOjUCqFJw0g\naZS5NuQTsetW8x7DufxQcEWqOHXkrwGYOECN3GCbTUdhSMbh+FjL6hJGEdO4\nAZ3RACO9k+ZMwFj5Gi9LD8FDkjkwcq4DdMKy/yKFG1VRzAGT2bMUvoIBcwWN\nTa1uF4tvE7AqAcRZct3qTeyjMwZaBGQNxARPRnGBDM+/JmyxsuHAz+UumUCk\n6UzCnoXuT7z6z/niR8ILDyoHx2ZKtDbL8uBNY2Uil4+bl/BFeWAwEN37B77y\nFprTSBp3D/eS/XEkelV4jJjj0jatxnXisLonx856MeR5qTDBAzRpgl9pGnQT\nuf9Skr0OkYi5RbPigQlfsReOGvwJ5jFJHSsC9b3Da0dqD36aJ03c0BKzoiNO\nmd6J\r\n=2LLq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.29": {"name": "psl", "version": "1.1.29", "devDependencies": {"karma": "^2.0.5", "mocha": "^5.2.0", "eslint": "^5.2.0", "request": "^2.87.0", "watchify": "^3.11.0", "uglify-js": "^3.4.6", "JSONStream": "^1.3.3", "browserify": "^16.2.2", "karma-mocha": "^1.3.0", "event-stream": "^3.3.4", "karma-browserify": "^5.3.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "60f580d360170bb722a797cc704411e6da850c67", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.29.tgz", "fileCount": 11, "integrity": "sha512-AeUmQ0oLN02flVHXWh9sSJF7mcdFq0ppid/JkErufc3hGIV/AMa8Fo9VgDo/cT2jFdOWoFvHp90qqBH54W+gjQ==", "signatures": [{"sig": "MEQCIEfda2ySr1GtCPgfuQaeKZ2m4ZlVwZ4bov97V40OczQ1AiA3FNsH8kB+1cRgHkC4XhuHlWUYGYSt+JBiupbiqzXlag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 550402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZErUCRA9TVsSAnZWagAAKnUQAJzjAsz6o8LyofA7V4sW\n05AiEbMhn0Y2i9El9TTK8eq7ZX1OdoIc6jnpbIr+VuRPT0waH5xdXKHzulsk\nQrA9GBeeTN57TLRtd/tl7bi5k6CleaiHwA+xwOvr1BaqYEIyAMqlsL0qxKB0\nD+ugemBqaxslghQNpKsFeQej8q+h8D5NuDd2rKVbcLsbltx17aTdctkvDaik\n4D3eUn4ZaS6waHn8uIJcVdoZHdlWLTovT4J7sJ1taNjCD/3nRdGj6xFbDaom\nHiiH2pjU9AHvD0Hqhm/O9ScoART/n1wYCXlfI9Pn7yCr4gLucAdIX/62cXr7\nIJU0p1lpUUbDK6jpfgDyQxW3oINSWhySyHU/X5h0bFcT2ZGhE9Rm6V1bVGBi\nE9CFh61iseAhg4vBbDvS7ifSFDSbQc45S04USRH1l5JMIf0Hn15W/UEiZeXU\nc9R8A910M4bE/kW9EjM+WlBWTDcyxtrhg0Cez19ywB1S/O5HznMriB7TO17H\ntD2f5VCU7LeVAnTLqxv8DWgYgMU8RAD9u2U9izjL+5ZSy4YMjNso2T3rJ7Y8\ndVoRhvfEopVj+Sf0o5XicSqjpbPAZaI6GS1/ZpYSNTea8vydjfKhIvM2RATR\nd/8xpgjBTuEMCptIiQU/KC6t4JfN/SvCrssmdFBxAvJuw7biDwjMpf3gIqwB\nl8et\r\n=ClWM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.30": {"name": "psl", "version": "1.1.30", "devDependencies": {"karma": "^3.1.3", "mocha": "^5.2.0", "eslint": "^5.10.0", "request": "^2.88.0", "watchify": "^3.11.0", "uglify-js": "^3.4.9", "JSONStream": "^1.3.5", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "event-stream": "^4.0.1", "karma-browserify": "^6.0.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "96437eacd59178beaf4ea02a713f718989134e17", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.30.tgz", "fileCount": 12, "integrity": "sha512-VYXyMq0EjBb4GHqBXMTOmHVXaS2Q2AAkiCGjejxTTqLN0viNS9ayubrnalA/Z3pCDYVT6jPY5t0YWOQjXvQbQQ==", "signatures": [{"sig": "MEUCIBVNGj+i2KzYaXyejAaCShxtuC2atFSDZzE66VTLf8uQAiEA9sgfEA2AiUaf1Pm/s1BxCebq7Waikz0XB6rd10887ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 536463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEJfBCRA9TVsSAnZWagAAnqYP/3NGsze51HMGsLSxsusB\nXJVDEcroVbnwP/BrrBOG7C+Todhai6skGokK4LuWqsVtGgOQ8qPR4er2gfW1\ncApAlwGHzwjOCqLGTHEqZglI8/63q5Qy3dBnwWuaRn7220FIujq9QoTvPqo5\nZK04LYAYCCZTbzj3qJLk78dr+dlaTRykKTZ71gF/yCHQ1S4HO0nEqb4WmKuZ\nrFim4oq+lmr1ueOfFm6DepX7uZNeVLTFge1KGIJSW+l6ODTcIcG5623ZT5/m\nmj5D0Ti6lQpkekVO/AcRB9MS7uai6QIqt0xbif62Gqkx0CYbmOTXCseBYSpe\nPT+KWFNge6MnKTDT45w2nZnX3N9R5cN3qyfeAs0TGx8oCBnv+5qTlCgOLMok\n4CRv8VOb/ctTGY5cuytibuFT5FCe7wgULXK5NCjEEpJwICbNAR63r9dfHomw\nen8XG2wWjoewJbzqpvfvQ7Y769wTz48gi3lXct9e56flHLPSWYL72N4uV+ZO\nltSO0F6j8ugJQ+aV2LK5EM8z1iWZ1qV4n97iNwiYvu87nPX3FlXxMlYtvxmG\n/uaEUczibqMlKKyKGhpzUor3gq6QKntDvK289mfVrfU0v+qoBWKk0X5iDxQt\nj92IcpBJ4hdJ+JxsOcAuI4q7ZM7s8nLoWM4yz8wuWwCg7p5lkDLEE2VC28rx\n77Dt\r\n=UtsP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.31": {"name": "psl", "version": "1.1.31", "devDependencies": {"karma": "^3.1.3", "mocha": "^5.2.0", "eslint": "^5.10.0", "request": "^2.88.0", "watchify": "^3.11.0", "uglify-js": "^3.4.9", "JSONStream": "^1.3.5", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "event-stream": "3.3.4", "karma-browserify": "^6.0.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "e9aa86d0101b5b105cbe93ac6b784cd547276184", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.31.tgz", "fileCount": 12, "integrity": "sha512-/6pt4+C+T+wZUieKR620OpzN/LlnNKuWjy1iFLQ/UG35JqHlR/89MP1d96dUfkf6Dne3TuLQzOYEYshJ+Hx8mw==", "signatures": [{"sig": "MEUCIQCtHOSkFFLiuhHYgdc0u0afQK/hCNbOJo+HkyfyedRYdgIgKhIVVfuzUfp3oHt+Y9Xw1XxwH1TRzn0gmtFoV9PbzsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 536269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEJoRCRA9TVsSAnZWagAA3TwP/1gopBAB3sytjh5GO/79\n8Cy5viNTwOmr87LFDdsqUF8hA/T3xmFdhMbhJSTgmS8zIvih2YuQv2Xd1gz5\nB/lbU3KcqUBz2xxQLPO83rezFIGjHAsaeK3XJhLicziR1I+Nhlfxo1l6zELt\nxfuH1hCEy1vlQqSZuxZcBVIMNGfICjM4vQ2yaTfGnDRlHr7xcgd8VOm43JBk\ny205X3QBm6MP5d/Oga4OJj7KWN7ZaSfEk/JOq3UgmcLvPbP/dwuLndVKNpan\n2Er66DvHw3tsFnclfotjNusDGdQzqWhCCt/OkoGVjv4T7YZ7P5Sp51RIjf2r\nHadCJcCcOPh/RZ2PR0UeQ+go+hiIz7ofkFm/CfrFhsSEVyEDG1aD5WtrvdHO\nkEe5dzjlb2Kp2NtWdpOEWCxeDYTnYWFFv6SgJ7wJfCnk66dyKypJRqV2O1I5\nLCKSu5a3fe/esmfB4Y+/ity5V2o6HQOssuDL6ba56Z2SY2glUN+wNPCUGEdz\nYTkbK+QaSPERVulWy/53AlwR2nqKRJOevoQCHpcDbCEuw6PZRXLJ3hIDBa2o\nbGe+1YpuOdKudTOOTncSLnN4Q7XUf5rpe7ygT13nJvEBMGTWjzHm1xesUXg5\nu3+8avpwPlh7E8sAjxPw+5KzV9e8JqKuB1UWuIS+PQHtkeotuXUqGB74lh0d\nAsps\r\n=TQa5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.32": {"name": "psl", "version": "1.1.32", "devDependencies": {"karma": "^3.1.3", "mocha": "^5.2.0", "eslint": "^5.10.0", "request": "^2.88.0", "watchify": "^3.11.0", "uglify-js": "^3.4.9", "JSONStream": "^1.3.5", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "event-stream": "3.3.4", "karma-browserify": "^6.0.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "3f132717cf2f9c169724b2b6caf373cf694198db", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.32.tgz", "fileCount": 11, "integrity": "sha512-MHACAkHpihU/REGGPLj4sEfc/XKW2bheigvHO1dUqjaKigMp1C8+WLQYRGgeKFMsw5PMfegZcaN8IDXK/cD0+g==", "signatures": [{"sig": "MEUCIQCB6E8XzSHxyYYkGGNnhhr7MiEjr1ARskwjup8T87g4EwIgbzVHeudnwVU1v7SxBhn7N67wpZEzI/M1PxgISOoVkWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6jG/CRA9TVsSAnZWagAAK9AP/AyzKDt49mVJej27VIIk\nGVcgMojfwcPp4yoqBPqCNvDRqo+OKTG2d0d3qEEcqHxG6NtO4v/bMPxxckHj\nARlTS6EJ5c3EeBid2mwRkpjr5oLFQt+B0lrrvOa4skWckU71QgxCIJekjRS9\n5lm4nHE4epNHDQc4oeKL4XgKmVNYp5rJyIHZBvRFi2GXMdtVDeZ8XAOI9bTH\nL5fCJ8CK9A8eL9ncLEmVF7YBk5XoR/pGvfj+9WLtRCymz0jUmfonsWKo/DVI\n0ZXEvDP2cS4PEypzHEeckBbJPw8W+dn6paoTDRGlIql40TdMfrXwFwAJ7fv8\n3sZQlXBBrMRkMBdiMxpn0sgvv8REv2iJm+lHq5VxljGotnb+BElg3vQ2+vct\nVzQPdQzxyZt0UvdYJYbpFYaR6VrRMULtDvVjcYq5WnJY5of6ZYAUVOJE9elQ\nfkogNsk87x65MKcdD45znQJn1zfv+7EkjIzBeF+yD0sZG8Gi6kd2EX4KAfnL\nw5AhS5lbGdTwzU1hv/Ea8EI8BAfT7xqIQHtWelgy1Hf9Nt8EiM7cMWNgp4k9\n0dtzVrOTbICRBp8t1tRAAJG8GrDPQggkWOQUyOrl68cebaG8H62U8JWl0gxE\nnqO6In56eWmvhLnrZRq/v8o/thl47VNkCO48sauQ7/lRHSWPcw4HQVltycLt\nrf4H\r\n=zDnR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.33": {"name": "psl", "version": "1.1.33", "devDependencies": {"karma": "^4.1.0", "mocha": "^6.1.4", "eslint": "^5.16.0", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.6.0", "JSONStream": "^1.3.5", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "5533d9384ca7aab86425198e10e8053ebfeab661", "tarball": "https://registry.npmjs.org/psl/-/psl-1.1.33.tgz", "fileCount": 11, "integrity": "sha512-LTDP2uSrsc7XCb5lO7A8BI1qYxRe/8EqlRvMeEl6rsnYAqDOl8xHR+8lSAIVfrNaSAlTPTNOCgNjWcoUL3AZsw==", "signatures": [{"sig": "MEUCIQCXjA3K4Mx6s3SwnWPgXEOtNvq3eWoJmcoiTHB/37hf/gIgb5ibwfW5EtXd1GIPPNuWG0R4Ie4WX35rxIK2EDVivSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCsfgCRA9TVsSAnZWagAAQvwP/A3gRt/pHryiz2JNERSb\n+Vav2u2lwd2vCngkg6Z0lLBAyKAUtS6fq3FHPNcaCLr9ZRdTJEQkbvjlSweh\n6Z9GjybzZIiMw4MZerg+B/JU4Do7C3oP0weV4w6GxsgPRJPrDPpvvXKKP5yH\nyFGZqfvFUv8l0MxOZxie4qgFTnFEOdDSXoWVqa+2lC/1PUmbFRPeUEAwcY78\nud7W+UN1b7MmIB6vTBqmBmnOrGmac3FHflNBOWsyiriaKJVuyw0ocmolzR79\nfj3PorojLUS+Q4hzDp9KIlr0eZPrpqaGwUi6ZY9Edo/5m9nF2qmvkePtTvY4\np7Hx/lsC7PZNqWWR6zoT/tpcpsSW+irpvYQ0M+gKS1ET6tXPS0O4PEeTKJbX\ny6MYQPOrCpdIJ/SEaR02vxLB0hc8L4RvQKmjBZJcYQN0vlBfBro1ki3hBROT\npLrv1kbdsX7J/33nX14TTWteQgkxwEZC1Xgq9fzAjhI0517GoSwAyDXoVr1d\n63/vmx3BYU+nKHkMSg7eQpGntNkFHEYc1i7b1XdGq3lHqI3g1Mx7d9Px1NoQ\nRNkdFnCBvvJkwjbsuLhcVVcn30OlGQfuFzliFo+7XaCOM0NS8Bckc6j+KgZ/\nC/EBa5xzwtpKYcFF3HbLOESVaa75HUOK4W4WivqucOPYr5O202wgMNm5VQpU\nu4f5\r\n=otGe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "psl", "version": "1.2.0", "devDependencies": {"karma": "^4.1.0", "mocha": "^6.1.4", "eslint": "^6.0.1", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.6.0", "JSONStream": "^1.3.5", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.3", "karma-browserify": "^6.0.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "df12b5b1b3a30f51c329eacbdef98f3a6e136dc6", "tarball": "https://registry.npmjs.org/psl/-/psl-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-GEn74ZffufCmkDDLNcl3uuyF/aSD6exEyh1v/ZSdAomB82t6G9hzJVRx0jBmLDW+VfZqks3aScmMw9DszwUalA==", "signatures": [{"sig": "MEYCIQDnZICiix1j3JOHit0kk7kmgytJeQysOkUrPexlSOyPEAIhANav6tFygfv3yanRbvKr+eWUYHWGkQQOTwe6QqB0LUfw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 425981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGoq/CRA9TVsSAnZWagAA2DoP/jr4EZfTz73aXP28CSQ6\nYc16wfOYIi77dLmjrRAizItFCWNkBqC2zU1betvAWeoaj+REk9EUK02jS2aY\nruvAZzmHzM2qFz/Cn6917aSjCrpL3Js4+mEg2awAsNnyhyFTafgy98alOyhr\ntjbeHrExUC5+SV6fEm8HWjsIlHgIreJsUZTPKHRCsKZ0oLrPHyJswF9YsOE6\nE6NUACftRhjozaJ2yytd1mmQoHrUMFUga6cGcsvh1gVNImcb6V5BQdzYSwyV\nS2WFVfQ+QlnrvTaXao2xlLYIucEE2xvdGn7Vkr/nKbVQXT09aouc5tPv09Ak\nfqJrIQ+8+GAYuOU2+RsFaZfnLc316RXLWLlWYCWO/ewrdhy6la57vc0cdFcl\ntqoOB1S1h1ZbZ+AZ4N27XIFgkTSmLa1aI6/Xag5Yjze7NaTqtuGy/h6Ou9x/\ntEurBpQb4Z0+jxGJJS6A9yxisRVq15JIsenpjdFRoe+heB6HbLQMRjk7g5LW\nJlGh/q/uAPXEuzsUWnIvcCZFOWjR+ewfzYpX2vyIhRMeCPOMhiVjzKrgh2Ud\nxCwp+tuR0lH22PLuMhtHs4My+lmNVvvhF5SYmxiG7o7262jCvQi8NSCvJM7F\nISbO8Egbez/pCMH0nux2/7ViWPHB/ZFMDcAvSL26b156kL+XRnBPj4uLe7s2\ntR7+\r\n=UQZh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "psl", "version": "1.3.0", "devDependencies": {"karma": "^4.2.0", "mocha": "^6.2.0", "eslint": "^6.1.0", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.6.0", "JSONStream": "^1.3.5", "browserify": "^16.3.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.3", "karma-browserify": "^6.1.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "e1ebf6a3b5564fa8376f3da2275da76d875ca1bd", "tarball": "https://registry.npmjs.org/psl/-/psl-1.3.0.tgz", "fileCount": 8, "integrity": "sha512-avHdspHO+9rQTLbv1RO+MPYeP/SzsCoxofjVnHanETfQhTJrmB0HlDoW+EiN/R+C0BZ+gERab9NY0lPN2TxNag==", "signatures": [{"sig": "MEUCIAIZU9btygLsbnb5xUjBQsJ8FfpOA3Wk4RvAWR+32p2tAiEAwjI3yHnK3ykzYvcVX1RrgQ16JfK2HgAnoYTWZ0PoCp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431109, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQg/NCRA9TVsSAnZWagAAFq8QAIr+K9xB75zZ1ZKjjIOL\n74PUON+nrONNRSiBJh/NI5qErnVv9Q9xHYkwaWCZ5YTNpW77R8obQAI4vV83\n8MYhmE63VjzT15njeh/Q8uAF132+eCRW1M/2RHrvIa1YmGfC92ybrGRTHxdo\nfNGB0ahI5ZwwR5UIeZnDlpXUa0u/yhjNJAynImFmVym0ReypEzuMQbhrLu4v\nXOpMkhbevWkDHpEmv/2ZGwUYyZ6MG3BU++DjxWAbY143ODZOLOFEP8eNV5LE\nmCQas9psjxtePJItUa107ZqiYv3QJKhMezqfwSVCZGSynT24ERMm/b1qA3PT\nfkAyUIgVoZagHZgMC2FmYxhm9eoeq5g4QdVsG/6AAxazM4QqwXmMumv5ZTgA\nb/uiGoNq/Fws07tn4nVNsi/747NWZ4cv2bA4uty7tKsewbdrnrYExkppXjhZ\nATYDhiAb5oMW/ChsUaDbcREO82kwnhqsr9XWuMWonzkGbSsB10uWQrXpCyj4\nsFOt4QoI2mcHvMUIoOwhf9yzcMnK/g2c8dn3zQfqKWlD+2RCvyjVKjweJWKe\n3zM/IzJX1iFBHdiw2t3XlteUuj23E9t1CTgowsS6rP4ECQZr38ivWsUQsfj7\n4MCVh+h2P+/C3+XovdMoqF5xFb6n92pig70YZH12FauOqOxOwj4FtlW0EUvV\njBKM\r\n=xsnf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "psl", "version": "1.3.1", "devDependencies": {"karma": "^4.3.0", "mocha": "^6.2.0", "eslint": "^6.3.0", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.6.0", "JSONStream": "^1.3.5", "browserify": "^16.5.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.3", "karma-browserify": "^6.1.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "d5aa3873a35ec450bc7db9012ad5a7246f6fc8bd", "tarball": "https://registry.npmjs.org/psl/-/psl-1.3.1.tgz", "fileCount": 8, "integrity": "sha512-2KLd5fKOdAfShtY2d/8XDWVRnmp3zp40Qt6ge2zBPFARLXOGUf2fHD5eg+TV/5oxBtQKVhjUaKFsAaE4HnwfSA==", "signatures": [{"sig": "MEUCIC6OZupuFL6MrIJBAjFKSWL9dOwOmefwvYfxATZh9zt4AiEAyoDyhpWv4P0l61bvykFdl/HLgTd76yeNL+CKt99l0Vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbb84CRA9TVsSAnZWagAADa0P/j833OPDv3CBjSbXkw8F\nfbXlqtckK6EkrOhpca/ajBnGDWKAWoVfK6btqX2lnlTxfVK5OqndPkYL5CGX\nczW6FSU9ozyEMNK5X6PR1c3is4jVHEdxVbZg42Oha5IATdzkpiOo69aAUTfF\nj63ueRbxwUJZ5HcMm0c/xxGFsKfe6WP5YbBUTKYS1BHZurPTxqO/QuLf2zpI\nbM+3oAXzvPdUQ6SlZsr2LHbyn3XT3/LffCCSytCOLrqjD+4Rzcjv+h64J7jH\nOqqrK8MYrmUQJ8oNa93Akp4jzsDUejK4bJv5SFhv7rI7PMthazSsFHRoaU3r\n1oxsP+BW+PJ+DP+08daaTwWs/eILANjnKKsVCEayKjcTXJ0RlBfoNdLA+N2A\nRpwZiWRIXuLGo6LWU/i7LkF5p7aLF3js/1pONwCfSSPKJNXq0kUAl0kzH1fr\n0lTTkyyTyMaA/PwJ0CbNg9NGt54KweJM4QTLZjVwHFKxLgVYEp4C3WfncNUp\neEpReuyL6C3sKWR0yGAbpf87ul7AjlTzIqDZd9EjJudooKxcy97ud1c67xQa\nKuMI376WnK8039WS+FHGUHgrj5b/wpYmnvMJjbzUlaYZ2BCyqF2ATWfknsEy\nANy83NDLMOZg4Yi5NQBxc9TFKoD2GZfSSgPbo2eUstHKpmqDzcI7GgWSQibn\nhp60\r\n=PfUH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "psl", "version": "1.4.0", "devDependencies": {"karma": "^4.3.0", "mocha": "^6.2.0", "eslint": "^6.3.0", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.6.0", "JSONStream": "^1.3.5", "browserify": "^16.5.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.3", "karma-browserify": "^6.1.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "5dd26156cdb69fa1fdb8ab1991667d3f80ced7c2", "tarball": "https://registry.npmjs.org/psl/-/psl-1.4.0.tgz", "fileCount": 8, "integrity": "sha512-HZzqCGPecFLyoRj5HLfuDSKYTJkAfB5thKBIkRHtGjWwY7p1dAyveIbXIq4tO0KYfDF2tHqPUgY9SDnGm00uFw==", "signatures": [{"sig": "MEQCIFBcwy1ReviE+PWqnySjgGPaqQIWWILQvvyBx2YOTl1+AiAaR7/rSUttqwcsCjemLtcY0ysjQn/pY/8wvzN6HKDpIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddmbqCRA9TVsSAnZWagAAco4P/365LAwqqfVvLqEPv45w\nfE8d22hKIqNwkGujdJLQOwJ6IhC12sYMzNsAtAsuCpVtMhx1eciDrNzm6v+G\n4Geix0F63eUuPDiY2cp3bLgMWrrMc+GS8dWVVgsgzNktYuDeCQ08NfwODqo/\ngBSSWEv2QL9NWZILA7gOwzIp9fv6ZgrTfpiatXh08pdAEfGV/4/tjIxRzm2m\nO/xBj1G0jJtcVPh/OyDN/vcQsCOfNRm2NhHO8Qs4lnDfqteX6NXeymzQjwjX\nrNQW1KWCLTaAVwI7pE6L7SFPGTFXn/rlTuFq/G5nN1eTLMFkFQL7rA3YWA9n\naq5vhckc8f8xoIrM5MhUGITn+2S73E5f/P1jMXzs/y1h84dWSVzoDQi+sT8x\nMJSyEGSw2WlTTmv1hIL6P9WEi8uQzJkh0Sws0HPUT1FMqblw8dXUlUd9I61F\nrgVdgRO5TDAOKapnIeqmyytYNomHF58FlZl1g+U+BcjRHUmO/PhzYlJ4OBCg\nh1bNdU2VOlyauc+ylUNqXijWq3r0cxqnWyvVqTuqwfkhA332sh5z3smwgbzs\nzRYnuvA+ElEheolXgMXlxo9TddpKZoghPqSnFXB8liSCAWVIAKwEs23q7qP9\nJMzVl+JPh7/clvxCxDKb2mSPCjLOJPfWDZQqBuOd8WAgEkZqn+RDYC96++n/\nOjaC\r\n=xULg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "psl", "version": "1.5.0", "devDependencies": {"karma": "^4.4.1", "mocha": "^6.2.2", "eslint": "^6.7.1", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.7.0", "JSONStream": "^1.3.5", "browserify": "^16.5.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.4", "karma-browserify": "^6.1.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "47fd1292def7fdb1e138cd78afa8814cebcf7b13", "tarball": "https://registry.npmjs.org/psl/-/psl-1.5.0.tgz", "fileCount": 8, "integrity": "sha512-4vqUjKi2huMu1OJiLhi3jN6jeeKvMZdI1tYgi/njW5zV52jNLgSAZSdN16m9bJFe61/cT8ulmw4qFitV9QRsEA==", "signatures": [{"sig": "MEQCIANd6YWNOI4qWAUZCoWx8kgOnNO+vAYKPAttDIxqG0v8AiAjnGBfWR5CtcZd0jOlWozncvtPOQdJkz8VGq+3+Q87CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4ZhICRA9TVsSAnZWagAAj9cQAJ7I/Du56Tf2tMH5i0Ru\nSG8PSQuwwd/1MqbFkPg+D+jkNQG1lxXOnh3VFTxm4RdQO38jv0FjvvkcKFL9\nUouKPEmYjJ9ebzAXjECKxy1eNoBqY/lVDiEHfvKMoQtYTGQK8NuRcmmr6rTY\nujo+nFj2GKNRlGX7GNzZqADPtBzlm6ei18JfYOJ/OgYVmYxL3m/mCJHzt9og\nKItZ6ZIKfpdSJ05Yx8FQTg2IE41D3pEoHbPNDHSiCuoeQknLO4JYYkDF4Mq+\nPEs2yvO1EDumzs9No6slmmzWCxHZUM7c1H5kaath6299FU8R1ZpKgUkyy6C8\nibqfUghb0l/z3orYLGrIVoxCPKv+UA+OlIv0oKQsgj83YCcYTHwsRl+Tqtgh\ntuWQqlhffC9PrY8jkdDqmjgXQMVvLBwGPMNbvdm6nSHtYn8H7QBKl+TtJF/3\nAKkKJYLo318rb4tFl1Aq1dWerLj2KKZkVSrUBhy0LK8MZkd6sMaMSizIZJ86\ndWlkH43NElNirKvOShxQc9uLsONkqgTwcN7vKKnvp4k5DhI6ABUomWFO6/P7\nOBKZZulHm8lABGvvsCjMOAPGTUH16cN/vOnuj/MgTALgbs9hNHgtQJV5n73b\nloi6aereab6mMQB7P65WZ44N4GPD7FAq9M/l0KxDSMPWtWKXN/zjUKJv4hF+\nU7b+\r\n=D4Hr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "psl", "version": "1.6.0", "devDependencies": {"karma": "^4.4.1", "mocha": "^6.2.2", "eslint": "^6.7.2", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.7.1", "JSONStream": "^1.3.5", "browserify": "^16.5.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.4", "karma-browserify": "^6.1.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "60557582ee23b6c43719d9890fb4170ecd91e110", "tarball": "https://registry.npmjs.org/psl/-/psl-1.6.0.tgz", "fileCount": 8, "integrity": "sha512-SYKKmVel98NCOYXpkwUqZqh0ahZeeKfmisiLIcEZdsb+WbLv02g/dI5BUmZnIyOe7RzZtLax81nnb2HbvC2tzA==", "signatures": [{"sig": "MEUCIECYXKEvuZLV4EuAPzYDqygLJVimbiGJduLOy98O3ggpAiEAwpJPg2tQd99gsDWyWNslv9zRmtNBVY9sI41eUf2LcCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6aA6CRA9TVsSAnZWagAAjrEP/1lp//Y4duI82lYqQ5is\naYNAFVom/ZSphsOQE26eLJ9t5SZHuLBtmlYaK3CIWbVfvyigjguB31PxUvMy\nkuDAUtj4xM6hTOo0VMkKUDnTb9PVbOdEGFLnsEo7MfFJSaTCaWXXoPFzCDY5\nQfMdsZ+4c2xzdtCxELqNnnyo+WBFlmglu5NebtlP1qh09GwfPU8Iz8+WF+BV\nekMX0rkUpu7/xixxV5RW43dTeIYN3OToT9R/3pujzV7OjywZt4qEsJDUuGVJ\nlVq52iyulRLw+e2wtWTibz7jRYPsIflAeboKC83sMzy+/EdKA7d6rBN/2VBQ\n5jvdoCzNChzCR0CeafqL4FQrT3WIVBrcoyHRfjU9FvV0mNR9jJRUXxIIEMQx\niewIP4GMyOmWavQyfz0X0yj+kX+ye7CQ3lIpHFYotXK5tlU+ds3QJzXK1VWW\nptiZjulQHWgHUw2pYpFhLWWt+rS5TCYFditYt8IDnsgHp32pBpRCgkT3NHGT\niTITiTHms8jA0o2FEdIWX+z9O9WS2UqXWEcjmyDT6fYmxWLTsRhCjYiYCSAP\nQXE/J4XPSFW7j4S+4aAFpPvTRqP81FOlZ5su3hDSqJ76GdcfRib7D4YwtkdJ\nf2V9qppeYjRExv/MzVSVN5gtP6MLYDDXYT9MFn3PnPh+xu+T0aG9ctj91fCL\nVq5Y\r\n=bx86\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.7.0": {"name": "psl", "version": "1.7.0", "devDependencies": {"karma": "^4.4.1", "mocha": "^6.2.2", "eslint": "^6.8.0", "request": "^2.88.0", "watchify": "^3.11.1", "uglify-js": "^3.7.3", "JSONStream": "^1.3.5", "browserify": "^16.5.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.4", "karma-browserify": "^6.1.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "f1c4c47a8ef97167dea5d6bbf4816d736e884a3c", "tarball": "https://registry.npmjs.org/psl/-/psl-1.7.0.tgz", "fileCount": 8, "integrity": "sha512-5NsSEDv8zY70ScRnOTn7bK7eanl2MvFrOrS/R6x+dBt5g1ghnj9Zv90kO8GwT8gxcu2ANyFprnFYB85IogIJOQ==", "signatures": [{"sig": "MEUCIEPc5s28kUwRjP1u0IgWkX1kmpJ+9P9m+5WqTkfqUpi9AiEAwAKrtGVFElZP7eyIAuUiRrvsgke3WjPcWGTeBp61cik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeB1N5CRA9TVsSAnZWagAAlFgQAI2LZnmL/HhTm9qDwXMD\n+Kv0N3C8TG75w/c/YTVec9bE7ODTjPHehp+4PKvEu3F5HRwqxpf3X+TL/Jrx\nuva890sppA9ysGXgzqgs24oTjAqmDumJD81oPzyed46azM5vramWge24r6eE\nBRlS0pbHQ2DPgG6/1QaLrM6Kzl1gxEYFL+aFoUlb/3DdFEKlARxri7uatOkd\nRsDWYqK2Tg08mnC4HbKZUclju80otDrN/1ydpedAzh/9OKWPq0qpXLACxFzQ\nZdDlx5J2H669vbx4IKwv/ms1xALALKUuJKxOsWddmhsCk8vMkE7FC73yQb7q\nZ8BZ4DcLmY8z1s6I12MMemEgXHWacyFsm8MSeNnD98EErZKYfxa5GESZcRRL\nqYLys99nT6I967rCgFedmemQGzCpUWPaOrDqqop3RzxApEoE29uKQQbu9XAt\nbW78e0XAsLaketouWiNZQdAHNrdqEm4yslWDNvemPBKFKnexWu7YdWys4gLO\n3h8LUR21fkXXqALnYv8q77rW8ZkO6ZQFRA9+1fLKRHqVSs5H7b+JlzJJOswf\nEwajBj3kDJbzBXakIUd8a1fgqsrhLS2TQi7MdiuyVUxu0GgsxVKSdqeGcS7q\nQ3VqlimA4PXcxjAQyEuV4HVCJvY+DX4gGMkfvDDHMeAKEO5yKdQcwz7+51lM\nOdZU\r\n=E3HF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.8.0": {"name": "psl", "version": "1.8.0", "devDependencies": {"karma": "^4.4.1", "mocha": "^7.1.1", "eslint": "^6.8.0", "request": "^2.88.2", "watchify": "^3.11.1", "uglify-js": "^3.8.0", "JSONStream": "^1.3.5", "browserify": "^16.5.0", "karma-mocha": "^1.3.0", "commit-and-pr": "^1.0.4", "karma-browserify": "^7.0.0", "eslint-config-hapi": "^12.0.0", "eslint-plugin-hapi": "^4.1.0", "phantomjs-prebuilt": "^2.1.16", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "9326f8bcfb013adcc005fdff056acce020e51c24", "tarball": "https://registry.npmjs.org/psl/-/psl-1.8.0.tgz", "fileCount": 8, "integrity": "sha512-RIdOzyoavK+hA18OGGWDqUTsCLhtA7IcZ/6NCs4fFJaHBDab+pDDmDIByWFRQJq2Cd7r1OoQxBGKOaztq+hjIQ==", "signatures": [{"sig": "MEQCIFiOR9bzJ3T1aPcwt9KX3U4iazNRcs2L+iwPfp09VffqAiA51zMehAV/VjNQg78dFbfo7JleGlaGcYLd5jhKfrQjIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 433019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee1ypCRA9TVsSAnZWagAAK4sP/R2ihBum6On0JgOFMk/A\nB3sInYH5eCfSKm54/DoKbST79ugr7XO95hbs784Pd+RLvGlkHY/ltt37wNu/\nZQ9JBVCkOqwYvOpfGCk4g9epCKey6Q5D86VQDLbGwa+vQ6VNyBcQjMeRhS1Y\n8JkzGjvUlBGsr/yVs5GoWRkEzCppOzLlSJjcsk4JIgmabvXDOeq9QPR6TT4A\nF+4eWRMPkW42qH6V6OKdf7Gkj+V1WVejK+uIR0zdd6KizlCVvWo61z3Y/8zR\n5gTRvSEiz0qbL4BXKXcoNGIdp7gJq1EwdqeBawHkqz+FsBElqmgnN2YemqQr\ncPa6y2MyIDVmLWy0fUSBorYr1eg7+Zl9jirxVJMonVqCBR1mMSCD3UISUiRF\nRxoQE9xVGZjvKfyqqQitn+GqSXXY5KmT7T9V8aR8RF25JJAZA7ZWZl3J4Sv3\nQIStlLApSULgk0a92paP37CsXE9QZzrZie/iyl1DRWbzn4rfB4SVQEMxNNZX\nwidKPvD2Mi2MRWE0gC1aLddPITlOfqlCBdo6dsVSZrfJXR7MBFTVHV5RLsvt\n5ug0vRRqEFIaERTKe6ey3sO3Wd0+Svw7ijnMsijNwurYabbW9IaLuzxs4ih5\ngXqEGfLiNRFPcJrRY6salwcGYl9GYPR2/oqaQEKvT4wDm82NLdcuxVJXC2fm\n4N/7\r\n=gPnQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.9.0": {"name": "psl", "version": "1.9.0", "devDependencies": {"chai": "^4.3.6", "mocha": "^7.2.0", "porch": "^2.0.0", "eslint": "^8.19.0", "request": "^2.88.2", "watchify": "^4.0.0", "uglify-js": "^3.16.2", "JSONStream": "^1.3.5", "browserify": "^17.0.0", "commit-and-pr": "^1.0.4", "serve-handler": "^6.1.3", "browserstack-local": "^1.5.1", "selenium-webdriver": "^4.3.0", "browserslist-browserstack": "^3.1.1"}, "dist": {"shasum": "d0df2a137f00794565fcaf3b2c00cd09f8d5a5a7", "tarball": "https://registry.npmjs.org/psl/-/psl-1.9.0.tgz", "fileCount": 9, "integrity": "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==", "signatures": [{"sig": "MEYCIQCU2cXuq9G825zRloZ+cDI/V4Vou1lwLEf7Xd1NSdOKXgIhAPIz1rKolz4ISlRAmRTqzGcXn61yhwqrr2ZhZohJ25+/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 461446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwoS6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmob/g/8CLCHL6lS8ymxvloIUDsJbyQcVdsqqSQGfd2ayUs1hMD0pPDv\r\ntHoSDAjoogFe76+U7L+Vjuj5UWzjVAW7zckv4p+EMPFbYcm630aw+ztLtxzz\r\nkAU4PYVIgMB0WEzQwWHnTD36HMFdl99TQul3jaAEN/aR8pSKcBbnUbOSL0g8\r\nmawVnZ0R0CnYJMPMaum6uFU+RUIwKzk4MOeuxNeIkXto1OFAMPCAa08adOXO\r\nbSPifICVJQ4KVE3v/UuqGOgLhZ93Zk6XfjPUiPxiOuVtOfL20It9ORd81xUr\r\nRJArkOTzLr5UevCdu2rxoP/LbJ3CjZT2zVExFtk4/1NdEjbmioiYErUKZAlQ\r\nx1wY588Te9jE0EiP2x4WinclLvvWa8KyOs2LfEWCzhJyoGqFbPotKB/OxnIy\r\nb2pY8vyakSsOoP3zhzzX0BW+eWh66bU+85bugzf155KYM15gOORw8/IZi5it\r\nM33ng7H1wWOfHpLnOnhnj4Z5MlWV3NVU2/lTdQE/sDivkVOsoI51EKax+NKm\r\ngI7h1NPygnU7zyP1KVE/V06kPJW5GgHmGOEq82XiZmR4L9olEbRNVODwmmWw\r\nO5SeTA2zNE1snT59HFB/wrULdm3THo1HfpAsQ/dJ+YRbsUIRl5dAiWZ1f7Ln\r\nfq369M2Ow5r9cLMJHj8dcSKiQQrFKdISkMo=\r\n=k4Jn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.10.0": {"name": "psl", "version": "1.10.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"chai": "^5.1.2", "mocha": "^10.8.2", "eslint": "^9.14.0", "request": "^2.88.2", "uglify-js": "^3.19.3", "JSONStream": "^1.3.5", "browserify": "^17.0.1", "commit-and-pr": "^1.0.4", "@playwright/test": "^1.48.2", "browserstack-node-sdk": "^1.34.21"}, "dist": {"shasum": "1450f7e16f922c3beeb7bd9db3f312635018fa15", "tarball": "https://registry.npmjs.org/psl/-/psl-1.10.0.tgz", "fileCount": 14, "integrity": "sha512-KSKHEbjAnpUuAUserOq0FxGXCUrzC3WniuSJhvdbs102rL55266ZcHBqLWOsG30spQMlPdpy7icATiAQehg/iA==", "signatures": [{"sig": "MEUCIQDhiJ8AyIoWm/fmFQSmaa/8hT1ywQaSrFXszfqzHlBi9gIgfkDBIL8V04k60YtcVjmU282WsS+SGTDuxJ66Ovd24qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 532113}}, "1.11.0": {"name": "psl", "version": "1.11.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"vite": "^5.4.11", "mocha": "^10.8.2", "eslint": "^9.15.0", "@eslint/js": "^9.15.0", "typescript": "^5.6.3", "@playwright/test": "^1.49.0", "@types/eslint__js": "^8.42.3", "typescript-eslint": "^8.15.0", "browserstack-node-sdk": "^1.34.23"}, "dist": {"shasum": "56fed2560dcb74a9c374f8e6f6596b328b26699c", "tarball": "https://registry.npmjs.org/psl/-/psl-1.11.0.tgz", "fileCount": 13, "integrity": "sha512-pjFdcBXT4g061k/SQkzNCRnav+1RdIOgrcX8hs5eL3CEQcFZP9qT8T1RWYxGKT11rH1DdIW+kJRfCYykBJuerQ==", "signatures": [{"sig": "MEUCIQCLvardpULNfxpKKy4DNXZOj2a7YmDMQpqDzmzN5zU+jQIgI5bhy9XsWCMfQL7I19kY59T76nAVBoFlcpyYplweCzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 711414}}, "1.12.0": {"name": "psl", "version": "1.12.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"chai": "^5.1.2", "mocha": "^10.8.2", "eslint": "^9.14.0", "request": "^2.88.2", "uglify-js": "^3.19.3", "JSONStream": "^1.3.5", "browserify": "^17.0.1", "commit-and-pr": "^1.0.4", "@playwright/test": "^1.48.2", "browserstack-node-sdk": "^1.34.21"}, "dist": {"shasum": "8b09cba186ebee68c0d824c1b22944cf5b2ad42c", "tarball": "https://registry.npmjs.org/psl/-/psl-1.12.0.tgz", "fileCount": 11, "integrity": "sha512-OVcqwt4qWJF9G0fnSEMNz7aSa1PiGX/IvSXDO+PpbDK3r/IJ3QX2iu8ywanYG07e9IaYDigMu+EapE8TdMZOJQ==", "signatures": [{"sig": "MEQCIDM0Wr/y2v09GROuXNDvdgf3xkybe5AWMzqP8wZbn0gyAiAnq68NA4aRzTKp71cqxLa1ogV7wG/ULSyudbW2omdo6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 529162}}, "1.13.0-beta.0": {"name": "psl", "version": "1.13.0-beta.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"vite": "^5.4.11", "mocha": "^10.8.2", "eslint": "^9.15.0", "@eslint/js": "^9.15.0", "typescript": "^5.6.3", "@playwright/test": "^1.49.0", "@types/eslint__js": "^8.42.3", "typescript-eslint": "^8.15.0", "browserstack-node-sdk": "^1.34.23"}, "dist": {"shasum": "c5c4bffaddb6e593002e7ba6dbb904811ed756bb", "tarball": "https://registry.npmjs.org/psl/-/psl-1.13.0-beta.0.tgz", "fileCount": 13, "integrity": "sha512-W4B0CaF48Sl21zRqji5iXIWE+17REc8X4Ig05KpK+igOKYhnODS0EGC7ChKIgCfVxGByYuCxH3q2f2xX2BsIKw==", "signatures": [{"sig": "MEUCIGcSJ3BDX/2xgCD6Doc4hHilvRhImQ6008QqRdvmBVO7AiEApknsubRT/t7An1WHKVjxpvlmxYP/zaomkJpSDounsX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 711448}}, "1.13.0": {"name": "psl", "version": "1.13.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"vite": "^5.4.11", "mocha": "^10.8.2", "eslint": "^9.15.0", "@eslint/js": "^9.15.0", "typescript": "^5.6.3", "@playwright/test": "^1.49.0", "@types/eslint__js": "^8.42.3", "typescript-eslint": "^8.15.0", "browserstack-node-sdk": "^1.34.23"}, "dist": {"shasum": "8b2357f13ef3cf546af3f52de00543a94da86cfa", "tarball": "https://registry.npmjs.org/psl/-/psl-1.13.0.tgz", "fileCount": 13, "integrity": "sha512-BFwmFXiJoFqlUpZ5Qssolv15DMyc84gTBds1BjsV1BfXEo1UyyD7GsmN67n7J77uRhoSNW1AXtXKPLcBFQn9Aw==", "signatures": [{"sig": "MEQCIH2kzgx3/cZg9aJOP+myelByt1Mb1vLMg2CMeqpyYrzZAiBsO52Il/974j3jLc6PNOSWdqi5G94s01HZCvzh8ZT21Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 711442}}, "1.14.0": {"name": "psl", "version": "1.14.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"vite": "^6.0.1", "mocha": "^10.8.2", "eslint": "^9.15.0", "benchmark": "^2.1.4", "@eslint/js": "^9.15.0", "typescript": "^5.7.2", "@playwright/test": "^1.49.0", "@types/eslint__js": "^8.42.3", "typescript-eslint": "^8.16.0", "browserstack-node-sdk": "^1.34.26"}, "dist": {"shasum": "f6ccbbd63e4e663f830ca39eeea08feb3caceaaf", "tarball": "https://registry.npmjs.org/psl/-/psl-1.14.0.tgz", "fileCount": 13, "integrity": "sha512-Syk1bnf6fRZ9wQs03AtKJHcM12cKbOLo9L8JtCCdYj5/DTsHmTyXM4BK5ouWeG2P6kZ4nmFvuNTdtaqfobCOCg==", "signatures": [{"sig": "MEYCIQDksfiW52tMtvWT4dc5Iw4y2GEiFRt3XQXmXyWz/qOvfAIhAMxIAWDb+M5XOcr9+3OaHNBuOcpNbUBEvGXf40j5BS5F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 712019}}, "1.15.0": {"name": "psl", "version": "1.15.0", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"@eslint/js": "^9.16.0", "@playwright/test": "^1.49.0", "@types/eslint__js": "^8.42.3", "benchmark": "^2.1.4", "browserstack-node-sdk": "^1.34.27", "eslint": "^9.16.0", "mocha": "^10.8.2", "typescript": "^5.7.2", "typescript-eslint": "^8.16.0", "vite": "^6.0.2"}, "dist": {"integrity": "sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==", "shasum": "bdace31896f1d97cec6a79e8224898ce93d974c6", "tarball": "https://registry.npmjs.org/psl/-/psl-1.15.0.tgz", "fileCount": 14, "unpackedSize": 711982, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDX+OUXAzhntlpLSp0CJpN9sjmSXolhY7i0MZrp9701QQIhAOIxWy5PqksM6kJyBkPHMrGUKn22cOrzhrtJ5b/E/amK"}]}, "funding": "https://github.com/sponsors/lupomontero"}}, "modified": "2024-12-02T10:16:04.448Z"}