{"name": "terser-webpack-plugin", "dist-tags": {"version-2": "2.3.8", "version-1": "1.4.6", "latest": "5.3.14"}, "versions": {"1.0.0": {"name": "terser-webpack-plugin", "version": "1.0.0", "dependencies": {"terser": "^3.8.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0", "@webpack-contrib/schema-utils": "^1.0.0-beta.0"}, "devDependencies": {"del": "^3.0.0", "nsp": "^3.1.0", "jest": "^22.4.3", "husky": "^0.14.3", "eslint": "^4.14.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "babel-jest": "^22.4.3", "pre-commit": "^1.2.2", "lint-staged": "^6.0.0", "babel-polyfill": "^6.26.0", "@commitlint/cli": "^7.0.0", "babel-preset-env": "^1.6.1", "standard-version": "^4.3.0", "webpack-defaults": "^2.3.0", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^2.6.2", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^2.0.4", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"webpack": "^4.3.0"}, "dist": {"shasum": "757d89d37694e0ba2ea0db0200773e467f2f4231", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-J1i69VN1tyhzpzbWrNvNaS1G4G7iNCvTvoT+qGNFxQPqND2Kk49M6yKw5/yA+dcU0D5pH/PuuH2ouDbhqDf0RQ==", "signatures": [{"sig": "MEUCIQDRmrLexFokUq+lqNQNGrDQvA3NBtQvTgqqFOted45x5wIgBL2k+TiF1WLqIcqjnFU2vuiddiOuXZMNRuaQ9BQSvHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbY0dwCRA9TVsSAnZWagAAzRAQAIwx5h+hn+sruUV3m4mL\n/IM30f3HrCP0hV+Dj2svrnj/AVxulArLi9gKspxxRn9hCZGxy2B5Qnnen/Kr\nUYC6pPBLFqsG3zXAl9iguGU1MS5U1ASYgD415bL3QPpWmWwQVMOBPWfKjMUK\nRjNlfnWEeUHxr2AbNUTj+NjS1OI1pDQBN+5tILNd/mIEpGa2CzzJ1e9SbJBp\nnkuQujAMWOJOzVkDwckEJhHh4EkyO41ZxptjHFCwfN1KhvrM1FgLhgPVsFcl\nC4dkoX6ifgbIp7vDOzUkN577l/bDEcxCFidopmigTrBrlU+8ki+psiwUeEe5\nKkLimXJKtHMP+eGiPutEpwcoq0/Gk5P/1b9jY0bAaxt32TAKSimdgkK17EQQ\nN/cjZzhaqtyfvRxOquARWgt3yU9QNsnOEJ1e5k6qoWdrWlMZ5xLfYVlLugfs\nWvPJOAtvoGm/KP3qLbiklk8r+ytgwyG0n1gdYNznSf6+YG3bYmCTJaAcu+wn\n5wSisGIPCi5Dcblcqp6bOtTsayp4KY12KUh+i5Pz+lssiJZAMeUeTyFsEWik\ntYEzqgz88/PMvtgv1BL4pfC63GiYPt8n6aLqY/Fnvo/svu9jHOl9FoFn2/jC\nMretPKo8rokYb3jEwM3XlbRr+EXzxf8rTbimg03I+kJGCC8++hHzyIsDv4dZ\n6Kjs\r\n=c3gc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}}, "1.0.1": {"name": "terser-webpack-plugin", "version": "1.0.1", "dependencies": {"terser": "^3.8.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0", "@webpack-contrib/schema-utils": "^1.0.0-beta.0"}, "devDependencies": {"del": "^3.0.0", "nsp": "^3.1.0", "jest": "^22.4.3", "husky": "^0.14.3", "eslint": "^4.14.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "babel-jest": "^22.4.3", "pre-commit": "^1.2.2", "lint-staged": "^6.0.0", "babel-polyfill": "^6.26.0", "@commitlint/cli": "^7.0.0", "babel-preset-env": "^1.6.1", "standard-version": "^4.3.0", "webpack-defaults": "^2.3.0", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^2.6.2", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^2.0.4", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"webpack": "^4.3.0"}, "dist": {"shasum": "b184e7b4084fcb5a65beafb15c997e3682e4f90c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-TALB9oZeC5/DheHwVVydwak/xRqiSaqcg9vT01MRP6FICpBsKvRdXob+hIW+MR01fuNwI7kHsJhQgJQ4v02g2g==", "signatures": [{"sig": "MEQCIFdos2mKmDNzG/shhR2QTFk14tdRwTxGOIF9Rw65kJPKAiBpetmyILUG5gmrI7wD2lhd62hojHx3baiFBcPo6mV1aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbc/PbCRA9TVsSAnZWagAAJfAP/ihsiLiD6VELuoessKUp\nGbCCawxTDnDhb6o4V80EWb3p3gH/Ui+sJFqhB4xyC7XLwaLZstZ8ksY+WTWQ\nVuYFKS5yBU14tzhZXXPvNrEP0z/BgvJys2TyuKWReopDyoelRfvIdaYfyKNv\na2pGuOFFpbqaoogEWLZnQFM2ydqAsW5ZIxtIxhxwY+1ju3ocVRY00/SGdiMl\n/yOaa0IMFC3CbV98Bxs1Uf67Rzo/daHSL5oEtXPx0gjWJWh4FE6x5d/iIpPk\nhxsTs1ZQ90KDkb/KFWV5/hpM+lOw4Ep22wjCgNl9dh1FlqFAZSZz965tzTTF\nKO4ZlDe3DOkBHI7ek4rj9ITCNhwrK1otrvdnHP+CvzdyBw/LqU53IYe7Y9R8\ndAP7XVJVQxhpHueMdpswu7SuH6GYm6jpCEzobWSiNp1ywh0JnGOXADKntvl7\nnWkJGqFmi4DDgmujYoCwLM8k5mS7svadtxziLFMCFwr6TsEo6BB6NXv8MlVo\nS+mXBXKlRYb6VXcGTCpS9zLxmE7rwn4wRM+kTRhA0960ZkVLL1fw6cMj4fRT\nrvkgZLlP5j+h4+KCtNcPW0SD7PbjOvjSvCRcuvi34O2kGQ1FlRnSVY5qE74m\nvIWqjW+Fk5QysOB8Z/E/QzIuSIVY0cj5Jbx1ogc05qfb2/56pe9Uxm+S7YeJ\n5u8z\r\n=TxBL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}}, "1.0.2": {"name": "terser-webpack-plugin", "version": "1.0.2", "dependencies": {"terser": "^3.8.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0"}, "devDependencies": {"del": "^3.0.0", "nsp": "^3.1.0", "jest": "^22.4.3", "husky": "^0.14.3", "eslint": "^4.14.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "babel-jest": "^22.4.3", "pre-commit": "^1.2.2", "lint-staged": "^6.0.0", "babel-polyfill": "^6.26.0", "@commitlint/cli": "^7.0.0", "babel-preset-env": "^1.6.1", "standard-version": "^4.3.0", "webpack-defaults": "^2.3.0", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^2.6.2", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^2.0.4", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"webpack": "^4.3.0"}, "dist": {"shasum": "b62dfdc4e59b0b5093665a765b234645b598d1a5", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-gJyt10fRIVj4dwOylFltjrjtcQzvGGlTF4afmiXJ8X5iul5l5lDDym353KOisKjXh2oRBdwQyv+9hkc0Ar+d9g==", "signatures": [{"sig": "MEQCICI/+gg9a3WFJ1BnX1eAY89liO4EO6oMJVArnFmEoDcqAiBT3eZppAUL8H4irEuRL4cyUGBtkxi7QsVCEp5enRduUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdYmtCRA9TVsSAnZWagAAviQP/AmTYQfNnHii2KvSB0FU\n3GOlawlaIy8oW8Ph6JPNQSu/cXc7lPgvCrXM1mkU8A4EKi5wWlrCwB4Ka9m9\nk9Lk+zIg4oxVyf9NBOQjROmp/UP9pXy6Jmxr+zdDf7KnnvVUZtCtzeoIxpM/\neFIHeHbn5MDE9R+yv3bP4dN4KgCssZZXISU8hfn9My1n2ogYQc0MpFXPGhQv\n91PcPqmQX17f7h4fd99b5MzcShELZ5hP6r0+Roc/S5wSB9xaSPC7+A44TjNa\nBNfi4il5RZgg5cGa9oD02+xCc3jKbUoO5zqPvtmxHRWCsbydgEeZAl/PT7Oy\nzJXqqsgcme3M5dwvyXQFYdVpSMxFhCnLZwTe/dGYoCT4win259VV4qQMrlqS\nWrd5QSNLSyvJx456TohT6sko2x/7pJUQkh7ZMrfpe7411THzFqvC76r9ipu7\nL0o4BTxJ8omyPC9+ICpohRis2VfedJ9OSMmEa+7jiX5/UX8MQ/2ThF2ehQG2\nZyQ7/4aR/yQ2K9pE4sQxRam67PAesfUzOmGSBiQMY5GGbx5OVPngVMMxzVfv\nDHXUp9iSG1S2ShcsWm2z+iczl6ZKY2onDICP09KI89uEc/7ua1qDpWruxKKV\nLtOS/LJ4iplfXEwyRq4ieX32RPWnlTM2c+ZXpUZNPdAePVHfweTapNbAX9ir\nLk1o\r\n=9BaE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}}, "1.1.0": {"name": "terser-webpack-plugin", "version": "1.1.0", "dependencies": {"terser": "^3.8.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0"}, "devDependencies": {"del": "^3.0.0", "jest": "^23.5.0", "husky": "^0.14.3", "eslint": "^5.5.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "babel-cli": "^6.26.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "babel-jest": "^23.4.2", "pre-commit": "^1.2.2", "lint-staged": "^7.2.2", "babel-polyfill": "^6.26.0", "@commitlint/cli": "^7.0.0", "babel-preset-env": "^1.6.1", "standard-version": "^4.3.0", "webpack-defaults": "^2.3.0", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^2.6.2", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^2.0.4", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"webpack": "^4.3.0"}, "dist": {"shasum": "cf7c25a1eee25bf121f4a587bb9e004e3f80e528", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-61lV0DSxMAZ8AyZG7/A4a3UPlrbOBo8NIQ4tJzLPAdGOQ+yoNC7l5ijEow27lBAL2humer01KLS6bGIMYQxKoA==", "signatures": [{"sig": "MEQCIGwCdOpNk7b6f12TDtGnXez+XX8YUP2w+9kohatKkQ4mAiBmB1Yr7G7uR5+fn9vFL6oorNCBJRbDmAX3fmRK+GwKWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm8UmCRA9TVsSAnZWagAAJKwP/1k8IS2NK5OW5Ad7rOg5\nDOdkgJxCw+tNy+Re5G520ISbakB5CESFXiC0OWngjQcCDGHNhkceNHFxFuNK\nxNk7M/RrRPeVruQi9j43yRHvFwtF0hwts0d6J5t9jwNxLHZxS1NfiCY5Gpiq\nFCrJUSzUZUoO/UQKEXB0yUCljmAvEsfy9jBLuDNWvNavbrPgNrSy44uHGu6/\nTf7DKAeva4pVj+txlE1TFsRcF1RCLKCVkIO5pFBAj40ua73i8YT7SFBouwX0\nle19tXnG1NqAMJZKn+OVsCMCAHt9+4Rx+qAgvxltbJ/fbHUCHxWbewEXmtkz\nZAJnPdstgBiRGnsFzPaoD/JaBRTviLJaO3NR4wDrB7nI9Nk4zeK2E7Hbg6Un\nPXBXhjbZzqM/d/JtclsMTx/XxjuIXziu0uilq2Yv3ylCHoUsXTx2r/YatO4C\nhD1qrr07n/nXQPOkfRdfGX9xL2G51FkcxkuZOqaMn8Yrh6YYE1/IOxC2ZGDi\ncZ1ma/Q1po6gTwEApX3wAFBP0emVXPWaPvUkJoW0szu4hRyCYZIQ0K2gC+5b\nTLB1wHVlXd+2rQt2K93wh1ZrQ/jOm6lCRQ3aaw0k5pYYmBGXe4sO1hyrnGhq\nPhlH7UUaPTeSt2DgIxPAS3Jcz0csyEbGqk4datkfaU6M/y2nltY73OeEg9q8\n0rMM\r\n=UI5M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}}, "1.2.0": {"name": "terser-webpack-plugin", "version": "1.2.0", "dependencies": {"terser": "^3.8.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0"}, "devDependencies": {"del": "^3.0.0", "jest": "^23.5.0", "husky": "^1.2.1", "eslint": "^5.5.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "@babel/cli": "^7.1.5", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.6", "lint-staged": "^8.1.0", "@babel/polyfill": "^7.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^4.3.0", "@babel/preset-env": "^7.1.6", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^3.0.0", "@webpack-contrib/defaults": "^3.0.0", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "24697213f2128009080f39f081c18cb2221d91ff", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.2.0.tgz", "fileCount": 10, "integrity": "sha512-QW7RACLS89RalHtLDb0s8+Iqcs/IAEw1rnVrV+mS7Gx1kgPG8o1g33JhAGDgc/CQ84hLsTW5WrAMdVysh692yg==", "signatures": [{"sig": "MEQCIFNqIMlzbRN8D3FjkuCJibsX+gZzHZsWPxtVOWEelv1bAiBx2FCKasR1/staS+P1TgeerTSXAqG7effaEMRV6NM7tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHlN2CRA9TVsSAnZWagAApOoP/22qIok4vOlvW3w0otvo\nQBVrBebDATlxfNfFo8qRVBgXg7TTais7El7NM0XRrhDnSrjnjIreIDUnsIqQ\nO469/iNOt1UJb2YSY6D4XjwVU2+MVr6gF4V/uj65Yq2ck/S0dfwQMA+7qmaa\ntb6ZzalrMUvpjjJnYi2n8c4HlMMVuCjBzCEoYO7T8SoMSE3ow24ADzjQDipy\n7bAfbq2anToAJBSQIdbPlLNQzdu17p3obxfwqZyxjKVFWX+hGgmLyGUNHrkx\n6WSmQANKxa0ebE4LxNgLUis8Z9WJHvLk/uL3kwvbR8KDmgccna/UFwX0dkeU\nMyncxDJNq3y+vZXRbnD/E5rOt1okazMukr0d72dVRt0RC+Na97csRM6i2eCu\nJBczz2AQAUz/ikXn5BhEd6kkCwD3mGAMLd20oiZ29AtO9bHKwnCmXDep1Veh\nWQQnro+OvakA4x6q5ukYX9oZE5wCeZQdRv0oaXsbsxld7nP0NIU4pUzAox/n\n5cHWnMrvZj10Lm9BYx55fZJlRw0yiWI0wjtKgrFECq+tRUfJGZ51YF5IaykY\noT141ByNBykePs+zY0A1fw54c0/FHi8UQPUhkPdnHWMFsMp+rnl0mnHVKx5I\nsVooQz8iud9w9LX2yLwaRzz7eOQvKLVmbvZXAqRUzH2n3IUyCmxucL9Du2X8\n3r/K\r\n=lxZA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.2.1": {"name": "terser-webpack-plugin", "version": "1.2.1", "dependencies": {"terser": "^3.8.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0"}, "devDependencies": {"del": "^3.0.0", "jest": "^23.5.0", "husky": "^1.2.1", "eslint": "^5.5.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "@babel/cli": "^7.1.5", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.6", "lint-staged": "^8.1.0", "@babel/polyfill": "^7.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^4.3.0", "@babel/preset-env": "^7.1.6", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^3.0.0", "@webpack-contrib/defaults": "^3.0.0", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "7545da9ae5f4f9ae6a0ac961eb46f5e7c845cc26", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.2.1.tgz", "fileCount": 10, "integrity": "sha512-GGSt+gbT0oKcMDmPx4SRSfJPE1XaN3kQRWG4ghxKQw9cn5G9x6aCKSsgYdvyM0na9NJ4Drv0RG6jbBByZ5CMjw==", "signatures": [{"sig": "MEUCIQCY9clMObJDLCfM1WsPE4iYyIwYpR8WTefzX/MXvu8aewIgd45epqMlDap+Z6evOyNpfaRbfhBT+x6VUWN7vYMxZm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJMaQCRA9TVsSAnZWagAA0doP/2kb2o56eX4ZqC0atMlu\nbexsw/rWyVPW2Rk0YLLBNOVhuAC1D3rzri5LYz82SEDjGP0HRyTNoOUGU4vv\nKdA7PiBIqHMQnBzOEAh9PyMuq053wQrWog1oSKCmTkEQ1jxGk2uShKGa6Ii8\nt5Vl1Fv3XVbsyqPLbHId4xEnEqvINFZY51hy8WwNaJkTVjA/2xlRG+WGJ06s\nBAg6mw1p5yn8ERFLdQ3QQdqUCuVrm0nrUmXSx15/VjEpmfon4sYl1afTBdd4\nyk1naCRA6vgFSl6XP1C6qNF689jpmcdE3bgKHh6CppxASmOYZshi9p9wJ/OP\nohsnEzIKGJIBWRhqpSofoSOFJkjKBv/EoJHKeoa9gEit6B7RwFMdVrY/HvdY\neai5nO+gy0kOBmZ1c6MtSro0g8yZsGT7VivrTn/zegQUvzSF0Z46B2dVNV8g\nxjTOSrbVEd743KPpHwsnslLHi+Fr2oyEo9yp0tJ5+fHbn+6XFN2cElHzW0/U\nrwl4zcxowdkJv00+w3cQd7eEIpvMYVCnEXXn7IbDY+6qK5qRdxjj1ZoOiQ32\nVUFLbFl82/7FRisBlNlhBgzi95kmoJFdbgzmyVihAS99rLoMqRAkp88RIeF3\nwbYHvFDdWNT+yAZekJ2IbYSjRArIN+kq3l8Q+M4CL+XRo4IemlTqL9WDV3/h\niG++\r\n=HQrZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.2.2": {"name": "terser-webpack-plugin", "version": "1.2.2", "dependencies": {"terser": "^3.16.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0"}, "devDependencies": {"del": "^3.0.0", "jest": "^24.0.0", "husky": "^1.2.1", "eslint": "^5.5.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "@babel/cli": "^7.1.5", "babel-jest": "^24.0.0", "@babel/core": "^7.1.6", "lint-staged": "^8.1.0", "@babel/polyfill": "^7.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^4.3.0", "@babel/preset-env": "^7.1.6", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^3.0.0", "@webpack-contrib/defaults": "^3.0.0", "conventional-github-releaser": "^3.1.2", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "9bff3a891ad614855a7dde0d707f7db5a927e3d9", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.2.2.tgz", "fileCount": 10, "integrity": "sha512-1DMkTk286BzmfylAvLXwpJrI7dWa5BnFmscV/2dCr8+c56egFcbaeFAl7+sujAjdmpLam21XRdhA4oifLyiWWg==", "signatures": [{"sig": "MEUCIQCCqN7K6SkBukdkpuuqkA0yx7U7+iN3vZq5pKDD8FDdcAIgCdJ0QXXbnc8Fwwmsu5H/ty27NpoFLHODagwprhsSrSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWDneCRA9TVsSAnZWagAA7MMP/1o+QnTZ0AXHMDXdLwZg\nONsDskRwUdWePZCkf8pnjOjHcUTJILvK21S3RVsw+6+j92lBEYou/m8rojbl\n8++gOrEtEd7oqHGwED1yeU3rH90hp/tsIHsVLCcNfwjod7Yxmr9qF3aB8xwn\nx14kMLP45OsG6pKaXt1+AOa10bmpUPVp4PLcJUgiSPFk2YHQyeVDH3uEy7hw\nJI85SNeR3YBEsELsyaPo1ME44Qgw8sq6F5QVoLcdtsqWI3yHCfIZzqfsIf/X\nPHDzj/dlmEAdKB7OF3KjP+T7I8lr1tYHPaY9ZfuBJDnwkI1qp/R5uLohnC56\nB8Zd0rtUjOY9Kof5ZjajJZQsdQXc5sil/dKnIOyqwZJ14ION76JL4j6AL1wW\nlkMcFmSaNLD/Za7PJAmwDJXPoA8IO6yV3HSdyygQt8/euRXc5pFt0ziJoTSE\noXergl0Uf7/cKbMBQSnIoWKGL+1TRVNh4NTG9MkcPszq5YUKIfu/2qV8dWGZ\nuokPsgu3uQIM/5lTWcKzDVpvVSAmuHvYu1T1W1jgyd4WZ7AGGERuZe0XTHjM\npwWQa9ci4q227E4rH2mf9o29SAim0Vnyzd413rhg8c2Zyge+4Wm3wVt9GO8G\nCLrdt2G/sHkX1ic87OrDLa6YVXoG1p1HuFHTnnFqY7PvI7r/nnMff/aVL0lt\nfXdK\r\n=iJdL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.2.3": {"name": "terser-webpack-plugin", "version": "1.2.3", "dependencies": {"terser": "^3.16.1", "cacache": "^11.0.2", "source-map": "^0.6.1", "worker-farm": "^1.5.2", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.1.0", "serialize-javascript": "^1.4.0"}, "devDependencies": {"del": "^3.0.0", "jest": "^24.0.0", "husky": "^1.2.1", "eslint": "^5.5.0", "del-cli": "^1.1.0", "webpack": "^4.16.3", "prettier": "^1.14.0", "cross-env": "^5.1.3", "memory-fs": "^0.4.1", "uglify-js": "^3.4.3", "@babel/cli": "^7.1.5", "babel-jest": "^24.0.0", "@babel/core": "^7.1.6", "lint-staged": "^8.1.0", "@babel/polyfill": "^7.0.0", "@commitlint/cli": "^7.0.0", "standard-version": "^5.0.0", "@babel/preset-env": "^7.1.6", "eslint-plugin-import": "^2.8.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^3.0.0", "@webpack-contrib/defaults": "^3.0.0", "@commitlint/config-conventional": "^7.0.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "3f98bc902fac3e5d0de730869f50668561262ec8", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.2.3.tgz", "fileCount": 10, "integrity": "sha512-GOK7q85oAb/5kE12fMuLdn2btOS9OBZn4VsecpHDywoUC/jLhSAKOiYo0ezx7ss2EXPMzyEWFoE0s1WLE+4+oA==", "signatures": [{"sig": "MEUCICoAYjPUTlA9vDPJvSX1kp+X4ONmC52BK9zEcvTnngwhAiEAgaOjLNOJ0+rcLa+4i3wBW9dlun2Nbxr63vELNMFp0Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcc83xCRA9TVsSAnZWagAA5u4QAKQI8xKvhMKVehjoDKfo\neT3ooqIDXj+p2xpI1pmSkGOsXgFWyBbKSxKQpQzIwYvUL0+3V890YezkJuyd\n+wTLDFVHIdSi56K6lBW/WtAFNUX9IO4nAp44lbJ7bKxf2TioSBHNYz0PBz94\naX3S/roQ/U1q8zVK5izW6zLWm7f47BiQnIrhWVYgUDlYlhA23vsrmFMtrfTi\n7CN3FvBl/Ttz/uQBsvXnIf3g6H5zNF8+Et10XyImErwenT7kRoo7rndeLNoU\nV9gxL8Vk08ezD88XefCSxUYhngFkNHGylaUh8gOGOxMbv66UXDVEk0/fTPKC\nW741nwtn1g+R45J3buCKNquBpZGLvIkz8+NrOQ5ET0zy6CEBmmy+5S/NeeWM\nFSD/i1uSxGcDFibtI9cRGNRttsyEvU18A4xhQQVDuhWjZDLzFp5IxcI4OhcP\n3CsgJceh/ss/rm5zeGjXMZGuUYrT381SLwE0mMfK20z69nsaVLi/rN9IKLGz\nwVLOJrt1nkh4hBNDG7+V21+4zdhjWAsL0FbIGRstPufJlSWpmg2lbBcgLB/U\nzjPdzZIZjClQKjYIsholHyUD9DotzCZgLNpJx8k5QVCB8fnpluaKc7NdD5GX\nvJdtD4Lny6W2mqsjs0RiB4yX0xvzzWlb92bolcoAVc601PIkeokmGBG4ROIm\nXW/h\r\n=frES\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.2.4": {"name": "terser-webpack-plugin", "version": "1.2.4", "dependencies": {"is-wsl": "^1.1.0", "terser": "^3.17.0", "cacache": "^11.3.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.3.0", "serialize-javascript": "^1.7.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^2.2.0", "eslint": "^5.16.0", "del-cli": "^1.1.0", "webpack": "^4.31.0", "prettier": "^1.17.1", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.5.12", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "jest-junit": "^6.4.0", "@babel/core": "^7.4.4", "lint-staged": "^8.1.6", "@commitlint/cli": "^7.6.1", "standard-version": "^6.0.1", "@babel/preset-env": "^7.4.4", "eslint-plugin-import": "^2.17.2", "eslint-config-webpack": "^1.2.5", "eslint-plugin-prettier": "^3.1.0", "@webpack-contrib/defaults": "^4.0.1", "commitlint-azure-pipelines-cli": "^1.0.1", "@commitlint/config-conventional": "^7.6.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "56f87540c28dd5265753431009388f473b5abba3", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.2.4.tgz", "fileCount": 10, "integrity": "sha512-64IiILNQlACWZLzFlpzNaG0bpQ4ytaB7fwOsbpsdIV70AfLUmIGGeuKL0YV2WmtcrURjE2aOvHD4/lrFV3Rg+Q==", "signatures": [{"sig": "MEUCIEOmcoO3+4BffFUDS7csvviIdxWLRlLfNBsMIRce72vOAiEA++gRzpYWyq6nGb+OLHzrBdmezewlkGVian/9vVZFG08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc28odCRA9TVsSAnZWagAASTAP/09+47PqLEbTigZA698x\ng5MMc4aceXeQOE1KNndSn/VJhEevETnfLKQgqd7iQ0QoFd+q0AgY6JX+m68Z\nM9mCtj30vyg8buLT/GMy1ex+wqQE3n9Ha+g33+FF1Fn0QdJBhAWnPwScsoeF\nZnOxin9QT2/GasZsH/T5M0nCmm53jZifxoCgMVRe5S9FsQKApxU1mmZ7X7bO\nTr2EkF7oSPYso9eB0EThQvgE0xVl87i0fxVUmloDZCSwIv472XHBRwk3Pm1h\nWSiZAPJDXNhpjgLk+Prq9TKcSbELrzlx8dhIo9DbtqylCgmLCTPRD7hUw4b6\n0lMoxjURIY5MLdXOwV8J4ufg3iu+7xjMhNHTAxUgDPbdoO/8dz80J0q9HaCs\nXig20VPlNkB6Hz2UlQ2DNdqVK2sKQGcESXIiqxVIXtGIop+dgxhlw8BNORBB\n21Ao3jzaYidJMgTg4ol1IU/tWL61XNamr68Tr2myEAvh3XDQu7l0xp2csyDz\nafv39TsV+6rSGq8/LzFfq4JjUAApM/1riCWvatD7dJQTj+mdhQyisej2cgqm\n0lHvJbQQyznXqePqMnqDlrWUE4dDhPAN4pQPEe5weSkg3unZWDOU5vf4HAKy\nRJk0SgeCaWLpXs3Pwk3rIcv/nl+zw4Hhw9Bgh6bBV4QE25DwkGm3HycmBGl9\nDVvL\r\n=Cp9y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.3.0": {"name": "terser-webpack-plugin", "version": "1.3.0", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.0.0", "cacache": "^11.3.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "loader-utils": "^1.2.3", "schema-utils": "^1.0.0", "find-cache-dir": "^2.0.0", "webpack-sources": "^1.3.0", "serialize-javascript": "^1.7.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^2.2.0", "eslint": "^5.16.0", "del-cli": "^1.1.0", "webpack": "^4.32.2", "prettier": "^1.17.1", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.5.15", "@babel/cli": "^7.4.4", "babel-jest": "^24.8.0", "jest-junit": "^6.4.0", "@babel/core": "^7.4.5", "lint-staged": "^8.1.7", "npm-run-all": "^4.1.5", "@commitlint/cli": "^7.6.1", "standard-version": "^6.0.1", "@babel/preset-env": "^7.4.5", "eslint-plugin-import": "^2.17.2", "eslint-config-webpack": "^1.2.5", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "@webpack-contrib/defaults": "^4.1.1", "commitlint-azure-pipelines-cli": "^1.0.1", "@commitlint/config-conventional": "^7.6.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "69aa22426299f4b5b3775cbed8cb2c5d419aa1d4", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.3.0.tgz", "fileCount": 10, "integrity": "sha512-W2YWmxPjjkUcOWa4pBEv4OP4er1aeQJlSo2UhtCFQCuRXEHjOFscO8VyWHj9JLlA0RzQb8Y2/Ta78XZvT54uGg==", "signatures": [{"sig": "MEYCIQCVdoGwg5BolWHxTYXoXiPpszDZempVXDKqyvV5MSzJ2AIhAK8FDh77ESpc4xdqe6DtbB0r+6vc5Q1Z1rj6EXh36cdE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49076, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5/EyCRA9TVsSAnZWagAAwqAP/iHS/sjGmsq6IC0id+JJ\ngn4JWl6pDoqcgg9NivvIGtiKoDn4/seDWocHQRAonAJo5I7WDBRCcj+ClG9U\nYYvZLsgZbJEeK4tmH67XizXiaQ1BypwZy4DkhGQRBRkgC/f0xyAKEbcU6tqJ\nOiPVD4DvNO9ndX3U4Wvah+ctvNaYYPkqZrOYylwjxj93OmSFHr64pic8qV4S\nYM1nk8Plzox1OKNC5XGdmSWlsBIhDn/ENxniwGQap2Vuz5riXuCosxVGGRuS\ns48iQUtVU1oVxKczLM9I0TY2aZDJnVqtvY70M628cZ+jDHmPcJHzg5itGUw2\ndagj53mmBumBzDA+dPJBhhTlUa3W0vEOw8depAOx++Maq1GVMAtMKGYGGqgs\n6ueS0uHdOMKDrOmxadJCBttAIF5KB59StBBxLpJSgeKxnjx1eDebbxEc/XMo\n5waGVe2Vp8E5YU46oQbINdMy0RzMVCAi/hrs/i9TShvBqR/SmPEVjJtloUV+\nzdtxXAKF2J0BYWhFXvrBxYu6cWJLLnxc28w5TxmENOsyE7MlAI2Ng9UvXtbY\n6LWRvTo7VaZ7Ymnj32pq58ey6FUJ8I9kJif2Steg49O0iVLBsl6rZE1ymu09\nZrjFndBDOLEt6s3ri2pgluM1tc1RPGwGIwYNhOTvCjohDlxGKe5Kl6lgMzOF\n0s64\r\n=jqm/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.4.0": {"name": "terser-webpack-plugin", "version": "1.4.0", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "loader-utils": "^1.2.3", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^1.7.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "01bc2eb4902441e66dc0cc8d30af5266117df4a4", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.0.tgz", "fileCount": 10, "integrity": "sha512-8ShcKziW7DZDL5jrO7TtBY7DV2tuAoztc73+2ufmiSp72XOjCJlYfnn/VM1SPeO/oqD9wBrLSJVFDamLuETofA==", "signatures": [{"sig": "MEQCIHV/CpImiy32akFM8p+/TgCtklHj/7trPnnHyqr1MzvwAiBqOjWovovUPIMs8U5a6QslU+lknA1bqJiR3RrBtWsSrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQX1NCRA9TVsSAnZWagAAXs0QAJNTs8EkH0lYIm/rLay/\nYM2+zOyiGnOSulHfAZqFri2qXBasJ+nHx+9Rfb4po2qT1H85G+V/p85bZDpm\naKKlkhV82fn82pBqSAxrEA2aEmxJqUZebR9gFt0UB02sXUHF6XTdXv1e7a0I\nBAOhIgR05A5b28+4YKJXLTbrm5nnTtHqFaap5ISzWWLI0ymhh3UNfgrVzlPO\n3nd2EQjt9FgLY3JxlfnBW6i0IYK4/nWfPURBF+clwQ8sl9uH+SKQ5IWbduKq\nM0nBDtDiCT4PUJEK4PZiNg5PzOgLs+E3QUPF5WXDFvxFScKFHu7TX6cVKmzn\nATmwC6r5m2+dDEXr44mX3NnQD0yXYhtU62XAPbPWGNuxcfKtmTU6u9L+vPUk\na49CdoRnhX39l4EMdz3+6OCKq0QayHnDEUJlhyKjDx4BTvhZ4yA/LTQaPrs1\nXkujJt5lziRy20+iQU5YOqcCQ+kzVzPG3UP8WbGgEP5OG7O+tp8pkxBL67td\nJsIlfvaMxJj0IMGk0ni6s4pyfPBCi0hbIcB9Uq1G/BCB088vQVotN79fJsYx\n35T8GiMUYuJdDGIZQHs/6WRQC5tF69r4npAOxfBz9PUYwKFL/zlZgRsVriT6\nB7Eut6tamj0ZBFCsDxdvJa4NkFNN9Bolfh7JHce6kYPHLLw9PyzA4lJBX/Kp\nWpbr\r\n=ISqz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "1.4.1": {"name": "terser-webpack-plugin", "version": "1.4.1", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^1.7.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "61b18e40eaee5be97e771cdbb10ed1280888c2b4", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.1.tgz", "fileCount": 10, "integrity": "sha512-ZXmmfiwtCLfz8WKZyYUuuHf3dMYEjg8NrjHMb0JqHVHVOSkzp3cW2/XG1fP3tRhqEqSzMwzzRQGtAPbs4Cncxg==", "signatures": [{"sig": "MEQCIFOcfidYBmG0HoFcU0cvNZhiyubr7D1/B+fZiRREX+CiAiBBaF7MXzupUaN1dciVvEwsuUImBvD0N7d8uv9ZqPWUvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQYWQCRA9TVsSAnZWagAA1tgP/A0s1F4udYdzwXeIJEDF\nAIMrwAyyKj45nIEGJyOutvOPGi/KRJd8uCJKVHimtX/8CB5T04FVf5CeY4t7\nAmf8vShMMwbUqaJi7XHtpmou/RMu9v2va4u5sRsKykBeVynz5BiIiJgr0gte\ni5qopwSppsQ3s9z9/JzslZeh6HtDm20YLVMH66dDlYhLsbsuvyrtCN6OrORm\nhp9bZ7S1j0P/Te5sMaZikqtoy/Vn5jnv98lCZ7pihhcun7Q1CDHSzV1TK0R2\n7wyvL2CY7kR/CzLkgceboRkn1AB4PZpjurJkIwPReT1aHbEbDnND8Tjdpfwo\nG03WLlvdXB84R0SBoeFu7/ibulrRME80SlonzXcq1q8fhyhTMWsOzYipE5Wp\nvyc6+AqFvCu4D0HXyooQpeXa0q3XB6BQciOUjGlw0yE5DOA1n6+tnqIwcBXL\nYpT4fy/ohN/ufIAWy/r7rI0ktMoTVcY2wN7R4PkZ5vMvfRl5sKR69/vSOV9K\n53v84MnkeyIwiPSRh8fx0KDlMKHHQVzZT5gJUD8+2HQBK/pf7VJ+MZDsZN9B\n/caR6uwQsaEo/3X1o4m1PRBH9NdqNN8/+iTcKPF4aoG2YUCTL06Gt+rjL0pd\nd4UA7NEzCu61JPUFNBiQZsxuMlPng2CCEsXddmzlSPueMcuO7q7IjGrdC3k/\nF2kh\r\n=2LYE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "2.0.0": {"name": "terser-webpack-plugin", "version": "2.0.0", "dependencies": {"terser": "^4.2.1", "cacache": "^12.0.3", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.2.0", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "del-cli": "^2.0.0", "webpack": "^4.39.3", "prettier": "^1.18.2", "cross-env": "^5.2.1", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.5", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.2.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "1b744e0b7b577fb02456355a08fd05b7e4efdd59", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.0.0.tgz", "fileCount": 10, "integrity": "sha512-kbaUz6c4dlfsEEiZK5/tuQqe4VGagCtezxLj+9YuLvI6dRMQo2LWOmAH7+MybT5lzTMx4kVJnA6/jUgWeY/erQ==", "signatures": [{"sig": "MEUCIEq78f3ufDI0VRRYuQeS4mJkNmWQVhp+NYg5DHMCt+l8AiEAwtATZXHpPIomIsKOf2sOWfvyY9EENx76W1b8M2OqqVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcUfxCRA9TVsSAnZWagAAUoQP/RWfNtCOcgPJSo9qiTKQ\nsjxs5mZ1RYIypdmbqLoS5RdkRQ7CvyXdstXSGLKajd1cnPCOkRB+QNzDQR/O\nvapQijMotnWTaO8s9OI179yN4hAy/IRH9V1y1i5H8lgscDct7M7KMEPTwsz4\nH/XF8QZcFImqWHEX8poBSpZ8RLAY0C6zXEXXwcOQc2QcmIQ3TVGeKH4xwT0q\ns743KtIqWLzlfE0aficVjT+6QJrQgToERAc2oo3XSPwam0y86BSLUL45H40o\nB9rHbO/wddGUbaZx+I3MoOfkwcaUCu6GdABNgFnr/3S1TP3zuW79FA1/Q+3F\nhc8GJTtE1W04nNTfqV+X7n9auwveqRZ2yOhwgZghCHMihIZxdkYuNdOFbC9f\nKh/D7BxaHF3Y7xc1SucY45EayimK0BwPJmRSG664723vh8l/mJ/ADipEvNKf\n/LTntOWEQxTKa90UyToRPTEzmypnqG9JkrjFl+jCjVNItVu7VfnMv2pEMIgq\nOkSvmxqqOTHY7N70ecyiO5bx5alR9bgpJQ6V57ekmD1UOa8y9zo/wg8A2Xpi\nwi1ez6zhFGVbkbwoa1srf7r/sDEVd5zCWFMMrakdPJDQQaYmA61Txe3KkmU3\nLLRHYp2wwE6B6o7xSsnMMuF5AdSRD1My0pkA6zxdOgf0HEIaFA8kKQeiwmi3\nBNlN\r\n=o8Mw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.0.1": {"name": "terser-webpack-plugin", "version": "2.0.1", "dependencies": {"terser": "^4.2.1", "cacache": "^12.0.3", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.2.0", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "del-cli": "^2.0.0", "webpack": "^4.39.3", "prettier": "^1.18.2", "cross-env": "^5.2.1", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.5", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.2.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "5e69a187133472174ef2fb51afc76a0f0a7b1d13", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.0.1.tgz", "fileCount": 10, "integrity": "sha512-DXx8f7080P2dgW9Ydd7MUon81AL+GkGr9prfQRf+rH06dRrgCGIvtT7q73JRm2UzmjKeJGtro9O8zwL2y2udag==", "signatures": [{"sig": "MEUCIHtXeeVr8MqAgzMOnC9mX0zRkuoidPgnr4FxhlqQapy1AiEAlc2jqLlJle6a9Mrl6BDI4tE6h4XsvZu0a39X+CZ+a3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcmmoCRA9TVsSAnZWagAAWJ0P/2JZuYC7mFlN4kpzcb7c\nBYYTmYkZbX3qAclQDIuiHeFsNqEXi/rWvJM+FacgyoxiyDMpiKuI8Z3hv4VB\njdF7mx8ab9/b99L3oKuwqvkV0BfW3GdaS9wVT4gRHZ5uqcwoh6LQ4Nt5NZ8X\nJBvZ23RHLRLPhhCbkE2IuKCH7nD0uw6rxWPbwLIf6TMcenhWVDO8nha+hgm0\nEfxWM2IxAF6LXIr7REg9UK6syRZA6GS9XwshvRmbG5xjr1Wun7DXWgisw/Nc\nQgK2jhU8JEMc9KvKM0bL0jptaGCHlnLS4qY9GyKhb0UOG8zMpPNW+H7h/0AZ\nwKoSAQES2SFsimBREJzYds/+95mQzsSngToWfLRSKNt+fJLjKfeBfEE93q6C\nkI4+VEcJjKfiYlE1k15AWii7rFouRieIoIhFn9qoLlM95b6ZEQJpcMs3zxVR\nN587JOTPCSZhAkwJxWBPfrsZyg0ZNMk7Na2CzBnzj+JsZhYSuScToqUAhoSD\na/wUx1LJXMLltJH4dWUzFdFOinIreuLDh9aNeS9cMpJUvBd81jXcI1qis/A1\nNeN2d40ZCkriBQv/CfXlT4ZwrzSTqDqU2JStY8z3wW1KUmsBJqHEmT011OAb\n7mZJn7ZL8QSOyMU4NMbH4oqYF9peTYyq5j8X55VB6W5pXEOJSWdk0N7TM2yD\nqA7M\r\n=9H6u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.1.0": {"name": "terser-webpack-plugin", "version": "2.1.0", "dependencies": {"terser": "^4.3.1", "cacache": "^12.0.3", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.2.0", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "del-cli": "^3.0.0", "webpack": "^4.40.1", "prettier": "^1.18.2", "cross-env": "^5.2.1", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.6.0", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.0", "lint-staged": "^9.2.5", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.0", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "b9cee799a2da2bce788540c3738e081b6db28d8a", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.1.0.tgz", "fileCount": 10, "integrity": "sha512-sZs43FVvNTqHp5hTkTSIC3XKB7rEC2FCbx9Uv0rM7D4iJsbTA1Q84tiaRYSSKSojBe6LCONX44RF73AEHGasvw==", "signatures": [{"sig": "MEUCIC+VK5xalGqhWKstZh55b+OqSe2TiCNV/gyG8ow7L6xiAiEA88mqFLUwv1b8F88KEcmmOIBgzyZ2a/2RpMLol20W134=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdf5POCRA9TVsSAnZWagAAwwAP/ibF7QZEryvwxU9Z/p75\n1wWQ7rMVTpRl8Y+MysC+QYk0hSCYRQ+dIWsJBBJc0y/WdKZssm30VJuq7LUM\nnPmbJhh+m8X2n5ayChYnNYmVZge+oKtJ/CsBX2PU/olVMSfwUvpmUPAB/ipb\nx7cJdBudYx6dGbLxIJrzrCPycQHD+zcPRDd1Fl0d4KlJ2Mx98si71IgJkMA3\nUYcShKXFYbwVh8kQvecV8OFw3HdMfbHHqY318sw2gzHZ27EToiG61axq5i8J\nyGAGtDNgdlFFZqNALlKrMgIK/Vzn4wZBOcAyTr2avmSHK7EmczeVfJPrFBMv\nog5PI2wfqpERrXgiXbJGYI8O2vP0IWptfGkiHBJf6DYo4V1np9rbtBPSx/KW\ne+i/hsrafDfgr3dahudEwZqnSxULzJM1E0zBvWROklPibi+qcOGlQ25Vdk6o\nXL4lzTHyPiN3he15Dr4So41X3DMVaUgxZAPg7e8Mpq9TuWn5UYoJTCadF7aD\nI+NxNFY+lXRe+HnhkTdzmz3/R6YohCqvH/F6hurUlpFm0IuY6KDuk5q8Nbjs\n/s6OHfPcVASdfy3PfAue00rKSegR2r6EAoK3ATzsKGS0hbTC5x4pGXuvFwZz\nXAQ+cHhQdHi/mNftR75L/BlfDH9LzFZuYLVxEqgcyoNosnB+sY0CIBSYq94X\nF9Z4\r\n=xV1h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.1.1": {"name": "terser-webpack-plugin", "version": "2.1.1", "dependencies": {"terser": "^4.3.3", "cacache": "^13.0.0", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.4.1", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.5", "eslint": "^6.3.0", "del-cli": "^3.0.0", "webpack": "^4.40.1", "prettier": "^1.18.2", "cross-env": "^6.0.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.6.0", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.0", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.0", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "ce84a39124bff59b56895642dd803e73bfca007f", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.1.1.tgz", "fileCount": 10, "integrity": "sha512-JhKx1aWtb0woicUwG7VJgunyNigRDKgeriOgh1bAZVSwu3raBuP/jTF9zpFWwoLzuTaoj0TwxcK/nA/PauEe+A==", "signatures": [{"sig": "MEUCIQDDcpjGYrGMyy2saSRx3Um1SAzvafWNiJQO+YOCkYNu6wIgEQjNih4rmFLkg82OqiDRHY1tM+gcCDvRAAg41IEcE5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjh+gCRA9TVsSAnZWagAAmWMP/AspiXQrU/4GlpNRy6Vk\nEYqcbE/DDD8TcJSZBzhKKK53G7AgnIr4vGEWbwhMaQcKv7mSY7CKixQ+E8tY\nElyv8/S86qMFKTUqGUTstzdzgiKR+N9Y9TiOez42mnebGjm+580kn9MtaNvq\nWEPUCyX7DS3rsE/XcDIK/rv/bNecgFdhfw+OmmXFCI27lP5SxwHqbHObGsSa\n3ajb8LwXZB72PAnhW6BOMU4OUmvbWhB+jKo+gB6RrmnjnZg0b0TyKfAzoki1\n6LFW8/vtWuI8Q2F7hC4ToaxwX5aGZ1NlFwgLcuPGRKK5nYKoX1HMsroBKBOR\nzGDdmJJaJ4TyF59XU4e3XwRjiuh1MimS4hn6yqN0kbsQ3lJAeAnFpN7HQ+Up\nmCa+OQv5u3YRyZALo+rYlBRy82LRwJslyP5om3CWP9v8+ZcSVMsskjQUBwAO\nTVI4tM79ao3Awj8cSmZQNGqXIjV204TrFFVA03qRhPnFb9vj/ZCkQEUf+g3d\nbYJeki0W4HSECgOrD5o962liVrB++5lBg/HDTaV5rPyxju+MLoTuH+Hz30ih\nFM/1uiMZ77QBPkkZJDV/m//YD0e+psTTM+6pqdr4lwyqlbNCsV3LTSIB69cl\np6O3GLaWSyGrHgiVDK1yGvL/tVyQIWI5YrZnTCFwhPaGlkBvJyInDSdmszj/\niewp\r\n=wkwR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.1.2": {"name": "terser-webpack-plugin", "version": "2.1.2", "dependencies": {"terser": "^4.3.4", "cacache": "^13.0.0", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.4.1", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.7", "eslint": "^6.3.0", "del-cli": "^3.0.0", "webpack": "^4.40.1", "prettier": "^1.18.2", "cross-env": "^6.0.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.6.0", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.0", "lint-staged": "^9.4.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.0", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.3.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "2b9b8147a6f18918348200800cf9560c50f701bb", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.1.2.tgz", "fileCount": 10, "integrity": "sha512-MF/C4KABwqYOfRDi87f7gG07GP7Wj/kyiX938UxIGIO6l5mkh8XJL7xtS0hX/CRdVQaZI7ThGUPZbznrCjsGpg==", "signatures": [{"sig": "MEYCIQDxAYI1/JJnRJorxyYMH/D5xlehmBJ2ywlCw7zwnVfsowIhANIC3eayEHuYoUBAE0vMLgRNyeNkFhww1Mvem7u3vMR+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdj5BQCRA9TVsSAnZWagAA29MP/3JC9jisIsk7kvLN5Udg\nyCznmTXeMlOXYj/XdMT/pKSMvGqO20O9AA+sRyIUfVepIXjDNPQeWjHhdFht\nEi7mhP5JBgsa7DFh5UvmgV9A9s1+P4cygwBJd63ND3UsFJ/DFqDUMbItzrRH\n0duqatZPGxuj/oUcUXX5IYvjX5fNoRYAsyGD0763+W/uO6R937Cp9F2tb1KL\n60u+5Si+rzb5ZG7u70emWjGCsRrYQaigQcbax6t5uuSCR53OmSnVtNhNKPOu\nmFKRvlycn78SW+/o5Wr19N1/fqiHUrrqaNCsc0aC/TK+4oUFKLHezQGG9eFw\nYMm3+fkHn//vZzKRm1REd08if8ttux43ew2nz6I2/FNQDZ8P8AP1QmXT5xj3\n9q2S3EI50xtEBu4N79TX7o/4KYB9ye/viWcwZa8N+eAGznNQQy06qOnYgjDg\n39S/DUa4OGsxgQXmK8ghxYZFDy/ec1KaECDFURnDh9fPojpmwIyd9C8FeVQr\n9ZroZzFybSenui/KSoEf/sJb2XzwAvRRJ3DQ1hvFw3+8ljuDV+fiGjo42ynG\nk+QW5n7DgmJ5Y3EUZH1LLt3jidHSk71Iscs07pyUZfladVF3qLCBBEAZ63I1\n5NDISAE+pvZll5tmHhd3Yth/xTgShc3o/CckIGOjPrc2tFLk9LK9tsEQOwck\niRmY\r\n=BYRR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.1.3": {"name": "terser-webpack-plugin", "version": "2.1.3", "dependencies": {"terser": "^4.3.8", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.4.1", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.8", "eslint": "^6.5.1", "del-cli": "^3.0.0", "webpack": "^4.41.0", "prettier": "^1.18.2", "cross-env": "^6.0.3", "memory-fs": "^0.5.0", "uglify-js": "^3.6.1", "@babel/cli": "^7.6.3", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.3", "lint-staged": "^9.4.2", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.4.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "85430da71ba88a60072bf659589eafaf6a00dc22", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.1.3.tgz", "fileCount": 10, "integrity": "sha512-z5Utx0TxmirZvRNL1GC795tlDM+bO83ZfcbtkL1y1VLoWtZ7S2a9+HFCLnabSRE/Yjsu4zCEX6U6CIRo4dVmcQ==", "signatures": [{"sig": "MEQCIHhnTInclHuHY+3sBUGKoF0sO7/wg7gYWtuMlgQJ+y6/AiAXGmoFXg9eSOv2vcxGN8V+RxvqrP8uKlXbO1jFEf+HgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn3tfCRA9TVsSAnZWagAAjZIQAJQpKEpIhEcoK+9zsC08\n9s5ix4nzmrVfuf3Ixp9FCyhp5Qe9IPDltYINSk6BV1+3d6awfj1484P6f77v\nDunHSMxfo0SK1T/Akx7VVBuxXpaAQ429x81VMUWpOtouGhwExA7hfpi/EI9r\nT+2gYMOeHtAYm0tBZ9VWGUajgzDE+ANtW4ipU5OkwBdNK/ESCnp+kfjAcXJ3\n/RPkjojSwjSPAaqotjAiqfZyKlBDC0PwTCAhC7eUb2iXd+dyw2dMtEzBmE2v\nriqCLXJZjRAYMb3GI9Q8Ia1ufbIH3G3ugFATN0ojhOUsPYXnVoYPuz37q6er\nTDU54fqvRp/EOZzO+acY3z1KCPQ9hgYRNFCjLgISxSzkKhS5+ERzB9vziqiW\n73/pZwlqi11X3xLKuH2f5p1hoAtL7Z8DSfIVyryTKPpyOeOvZEkBFfbLZSaS\nqQUTm9hlGT4wtRDERjXsfZ4Qq3VCzgsdc/qXDibKRKNZPN/PA1BPZKDWmeLJ\njK4P3/CBIA8UV6dycz7pNF8HxZqA7giA3jYKnSU5hI3yY2r3Hh1UWLRs+tiD\nyEXTNCxnf7aZpCoAbu1XK/2iFYqZzbXqZU5jFhHCLs+xXlMiVWPHbP/+ikUd\n9K8vjsMR12lQEwRCEs8LDQdn8WUQKAoarS9czeEyv0i+eFPVN+Xz4/5SvRMw\nYfKA\r\n=FoVo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.2.0": {"name": "terser-webpack-plugin", "version": "2.2.0", "dependencies": {"terser": "^4.3.9", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.5.0", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.9", "eslint": "^6.5.1", "del-cli": "^3.0.0", "webpack": "^4.41.0", "prettier": "^1.18.2", "cross-env": "^6.0.3", "memory-fs": "^0.5.0", "uglify-js": "^3.6.3", "@babel/cli": "^7.6.4", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.4", "lint-staged": "^9.4.2", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.4.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "c35d66552ccfb9cc6fd39db06f430c3133d73110", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.2.0.tgz", "fileCount": 10, "integrity": "sha512-Sv2tyWCEXSi7aHCBOnigWMIFYHScftKytogG08RCmusPRkYMRCxyq2rVdy7s1ahEE13Yoz2ddgPsNU6axP5LJg==", "signatures": [{"sig": "MEYCIQDFsiiGpC0/8z3ks984FbnLRwo9QBPPpJuXnKuS8cuAhAIhALAZ2AH0GGlo1Wa2Ul53MBlKmi7/gFMooczZRrV0z6Kj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdruPeCRA9TVsSAnZWagAAdqoP/3v+0KPvc1hFmX3k/sd6\n85LPttEGaLyy+fBzITXKYedCl6DgjIvkar4FjiGMp+Owqy6z4aaJNwKjDsjO\n2NVrb4y49j5Q10UWkKk4narbOXRaDMiOLe+4V5rOgJOMe1pfOTgmuqiz9UmZ\n1cSSbspnrBVuiqRV3xYyXf/tD/NunK0SRBmZ5jVaTyo0rEW4LfAISHGhXsIh\nX/l8qQtvH/WlbBTiliU/rN1XEYyNzrpHwlqO1zmcX9NeGybDcCsDT1LD1LRL\n+Ocxfmg+bSLxQ/jHULANWOl0bzz8VTa1X4fvwUOZNUGixP4jYskc1UTBbCn0\nEKrM0fCy+Q7leFQUVEEI9BcJYzQkwQyDvxihb/yWi3wENKxuzHYAjw8IPEbq\nOYtsB2CAQPM9Ni6nRWLVuuSi9OaDDqXdneT4pQ7wZziit2Q+bRl/DUWZvMKz\nIrJ9Ozt1mB43wwUDNXp6qEkDlD2rwcfyPSDnQzSt3BEThmEQ4DSiIZ0oH87c\ngkj/1wzOn4AyVFRlI1knzdcgUd8nh7M8qjMH+8zGCQ0UONYkyC5C61tw5Bju\nnmNggfqd30LpBoNpCRqiOydmiBF6YIuJEgz/ElRzvpxdfkbvaSnNJj7lwadJ\nyKqIg2LETvMKqnNNzhXAV4G0+Qb9JvtMRivYv1D4p/DZ4xEjofgKJiWdyMLU\n9oMv\r\n=CK/t\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.2.1": {"name": "terser-webpack-plugin", "version": "2.2.1", "dependencies": {"terser": "^4.3.9", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.5.0", "find-cache-dir": "^3.0.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.0.9", "eslint": "^6.5.1", "del-cli": "^3.0.0", "webpack": "^4.41.0", "prettier": "^1.18.2", "cross-env": "^6.0.3", "memory-fs": "^0.5.0", "uglify-js": "^3.6.3", "@babel/cli": "^7.6.4", "babel-jest": "^24.9.0", "jest-junit": "^8.0.0", "@babel/core": "^7.6.4", "lint-staged": "^9.4.2", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.6.3", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.4.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "5569e6c7d8be79e5e43d6da23acc3b6ba77d22bd", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.2.1.tgz", "fileCount": 10, "integrity": "sha512-jwdauV5Al7zopR6OAYvIIRcxXCSvLjZjr7uZE8l2tIWb/ryrGN48sJftqGf5k9z09tWhajx53ldp0XPI080YnA==", "signatures": [{"sig": "MEYCIQC6hkfliaKXMVgR5/OSU68rOmz4oMRfAFkG/sJpatngfgIhAKRqlMKYpcav7COcuZCQfX12bT5N8aCcH3ThL+Psyg+5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrwkeCRA9TVsSAnZWagAAc1IP/2xbqahMDaJT1j0UCXkD\nXiusnNvQeMh95pmDVKtALqbVWp3RbKL2IHmkJsyn32OH5txCk+MoE7g5fHLL\n0V0YLiJOYtmn9p7S3Dj9XIg3FkAL5l5Bsgm6/25tzv6T8FFWCbW9O7XPIdWa\n9MpGewinkjRaiNdZqphxPmfywOajv9KHqnjKZNO2aaPUNujkatAdciRN0f6b\nARz2rnJ/i9ygzi5CiE5e3sXxXEyLbU3FwBMo3t8YYEqGsBDNZ1FnWRfXJwyU\nk2szQeGBxIMLzlN6zdEWegwtQy1JjwVwzr7n+D1V239gGzTvQvjuSF7wBm+x\nfzWIInsdcDSVuO+y5j0zIjE+41oy2hVjws3n2AGSM2d7MmERQsY/X0e3Od5p\nyplzeCJoaYp5xtKmmD/9kIx+iZsUL0RNMQXUtzrAnlWixHuu4k7OsgLu8h0y\nyTLpOPH2SUQQNSbA6mWixP85mn3GuS1I+92FnnpT2UIkmqeSR5H3aqy3cp7/\naofK+VqobuFGTQW/s1WIfS9MG9w36+el9NaPsettL5x9Um/W/yMT49S1VFhz\nolrOyEwE39kCfX1DsRFGwjc6DrNAxcRLduSUvIuOjR9RmnyHfCO4lZHq4WCA\nnQFO7LIXU4r+S6PcPvtnbrduKOiE1fm6Wp5g86xFnMncEiwkGKz8sfrHhxbi\nODZ4\r\n=cNh6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}}, "2.2.2": {"name": "terser-webpack-plugin", "version": "2.2.2", "dependencies": {"terser": "^4.4.2", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.6.1", "find-cache-dir": "^3.1.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.1"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "memfs": "^3.0.1", "eslint": "^6.7.2", "del-cli": "^3.0.0", "webpack": "^4.41.2", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.1", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "2a6e00237125564a455ad69b22e08ee59420473a", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.2.2.tgz", "fileCount": 10, "integrity": "sha512-/CHMNswPMAwuD2kd++qys8UmBRmsshPSzHw4BlDwurPtK9YjeK93OV89YWkJulHk972cs07K/7Z92V6PNjWF8A==", "signatures": [{"sig": "MEUCIF+Ic2eFgWSiV6lNuDmOSrLtrBYKlDvHherSVOGQ1pfVAiEA6OsW//xd/9dEjnsSvSfyPaVefCeF5wa2twT6PBhNNjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6lUUCRA9TVsSAnZWagAAF3EP/RRjece7BvodsJ6UVfZ9\nqZnD7BA9fHK3aYhWus7m+jmFG/lqYA8i0Ws1v5lbQGXPOfHXOtTIjKVO7MmF\nQfsTj4tAGhHspt604hLdghlkpBMVc0c6K9khPPLsRo8FJryrLh3atPb3rMaa\ncDGlS0VBwr2GjljyHBYG9X3SalYMyEd9RHq0Y3D3n0+ML9fjT4Uo6cUDrFZy\nux+hoOrNsU5rT4RXDNcEc1HZNgdvceJ9ECpXV4fOtmlWbuCWklmew1a1/d3F\nEHpfkL6IdvJya7H3g9GFKWwh6+Os9B64yF9RnzpQmE3m6JCHLJN4NXEtRUap\nXsrfj1rq9RpqqIN3j+Mk5SqUodenK+WFdiS97w2DXfNZwZ55L4E0IYahIGNf\n2J4ysZCQGI6zq5R2Kanb9XZR9V6Q1ISG5NOpXeHasXrAkgB4Vh9bbeHrqhNs\nUtqONCOX5Gxy+j5PHfT1d2OuYXepiJVV1/S8C1HY9X5ZgFHjjaKMYwlwD8EC\nDZuBA8JxjrF9Mo5qNOCpTlvJamBMrDO3IuVjOOuEVWxz+0ItlUyARIqVPze7\nbPeLUctTKs9QEEM7KxqEEEYYTXFBNcglDSnulWAfaxtmHf4J3aZlROjsZNwD\nJHbvpWE9n4eOdtR3UpXja+vckSiO9dTNVPz5lQ1pdrkTF/ftOy56Bj9atUCD\n0z1B\r\n=GQWj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "1.4.2": {"name": "terser-webpack-plugin", "version": "1.4.2", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^2.1.1"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "e23c0d554587d1f473bd0cf68627720e733890a4", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.2.tgz", "fileCount": 10, "integrity": "sha512-fdEb91kR2l+BVgES77N/NTXWZlpX6vX+pYPjnX5grcDYBF2CMnzJiXX4NNlna4l04lvCW39lZ+O/jSvUhHH/ew==", "signatures": [{"sig": "MEUCICsSelxtJqgKEJE8oftqIE0e0DdVBoFHcfo85T++zuqSAiEAuDGb+K8JuMNE9dyoefUUQPsX3a5pJ15qFPyISxN0lpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6mdqCRA9TVsSAnZWagAAYusP/AwnEClSA8sj0Xo0n0Vu\n8EEcEY0ZEA84OVYKD5JRvxmYVMGVEqnHcSgg/0CKHTMI1V0Mpzj2v90Y7BwV\ne1IXGRexdChLSBuxgyB0quJsBNBAxFKL+nFreO+SQCAuUZB6qwNaxUhFqIvE\n/C6ir62YsWn8PfwYjdNn0O3dMHdPwDbKD5TJs7zErdbhAHMcxsDwROECP7Cr\n3trr2+fhPNV6qIMEd0br2lGX3L3PelJz6i0rfT/4OhYYplW2Tjc/+NPmQJSP\njWJwEI14gOupHChp10FpxK9PTAkW3qa9W03K/jlysiwSILGvAjCpCMI2lddO\njQMBI1/ma3mgQNkf+IG6yL7x9MKbwpE/LZvGiAz1aPCfEDrGUV24yB60OHLa\nN8NtWqV3QdCDoCQeVLgHLU4GVHEvhYY7PBgnYKWeG7Ddb5rbcPJ012LmeUWg\nBoqwoAvQvbwzTRKrYcnG9u4yH4q145OeWvRFcRq00HqYGZ0U+PvA9KWT3FOx\nslqDSejP8UZDKjK9Yrfm+mxGEgJVv6egaXzgqGfYn0lkYdBt5H/ADbTX03sM\n7nONXbE1WzkJs6mfE/aPI6UPOrSGxj6QU4YQipe3+Ujm4JWdeXuatXYO7Azj\nUqhaT8U7/zGJlz6DG0GpM7fj2hQIxvXo4iFt9wdDebk2PCj8KPalK+xYp3Wr\n/AVT\r\n=DfUS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "2.2.3": {"name": "terser-webpack-plugin", "version": "2.2.3", "dependencies": {"terser": "^4.4.2", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.6.1", "find-cache-dir": "^3.1.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "memfs": "^3.0.1", "eslint": "^6.7.2", "del-cli": "^3.0.0", "webpack": "^4.41.2", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.2", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "7b070b40e39ef177ca062a4b6d70c3acdd85defc", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.2.3.tgz", "fileCount": 10, "integrity": "sha512-R8cqQDld4BjVKaIR0D9Q0O/QzfgXzME3wXfSxxW23ZGv5xpMDUt1NEnCuG94y1+bKASLvc5TjIHLlNo0eK8GLA==", "signatures": [{"sig": "MEUCIQCtvBIjHE2GiT7QkuZSooZ4iW8xAcQ/q0Wy0sQqRsF8fAIgNl0+NHaUmPYEaMAAqYcZhk3+dD60Ag6HV8GDL8pTKDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8L9XCRA9TVsSAnZWagAAqeQP/j1BgyiciXdZtXEiCQ3i\nkkJKSVTk1Glv8+GaJ+iw58hZVSou13YHkaEJiSt6DlWpHjDpIU7j6XNceVWE\nPfkssy/LvjK7dihQw+8EzeHDeX7yZjCGJz0+S7UFsvHVik5HRxanMrOLJ51+\nQ2LOyxLGgFfMSIBeNw2Zk02mf14nDZtEadyY1x9dibYXqmQosWaxF9f0qGBc\nPp9PveRW6m3jamacX2TDLhDBqV491nrWZHi3tSbA66U3BbT0g1kDLZaKCBWE\n3+eDko0GbTWguGoISAlM6qTGWKeWWhE8LIHWOY6xJVgn9tX0aY0nGmHkAw1i\nFoBIUbvFd+nzdHBXQgC1kL7wSwmmWFUf2EKoTMen4DhgI0dIKZLi0VbGKWXO\nZkBZzjrdy3mwNsxYD8zL8bx6ElArmXrjyW0b5fcdkn7Ecl99rG1gzs6Rxhyb\np0Is7unDyqb3m2HgDoxgICIu0vbkx/Wt0A/NoYaOfpV/fDxlV+WfUONBabcm\n7k99h6p6Gsr8Y3QGk0bjEG9hphrKKBY9VWRL1cnXGMOp3QBtmMxVmU5BVPy4\nCBLfTW18L0KJkX5VFNJ5KL7vXX8RO8OLeltVIjE91/1fxBZVtrlqPQqHAsUK\nlg7c7c0X8mVU/zxrguxDungvKU62udqukcOeXI9ytnzUfRsDf16QgSToFI0H\nSIcy\r\n=h5mU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "1.4.3": {"name": "terser-webpack-plugin", "version": "1.4.3", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "5ecaf2dbdc5fb99745fd06791f46fc9ddb1c9a7c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.3.tgz", "fileCount": 10, "integrity": "sha512-QMxecFz/gHQwteWwSo5nTc6UaICqN1bMedC5sMtUc7y3Ha3Q8y6ZO0iCR8pq4RJC8Hjf0FEPEHZqcMB/+DFCrA==", "signatures": [{"sig": "MEUCIQCDL6Axg964hbusXImjVajQUcpEXs+KKNYhEJOBLFU8cwIgMSICjOqNXuZn7nxuLGlUTg9b/nhPkBG7Inp8ikkM6uE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8MLHCRA9TVsSAnZWagAAMfMQAIumea6HSJSUDXsQT4Np\nMfs0cMsYzUQlgN81cRkwXCHKspgjvcM2QOAetfkwbDNUc6yR1lrQkK+6hRac\nuvdw4MGaVc9h9xlGDRKHTubXsJdHqV6WgOuKjB+rZEe3/BSsjmp0pUY8kIdG\n1Fn5fWkA0yB2TwE+DwW3cbuf+TvLJ4TCpuOhP+9MXvtUvN0un1ymFB4ZNGdW\neBbnlVdQhjYQBpw459U4EME+HKuKg6RZvA2twSDL+e5HYx84duQOkb6JGktD\nTeXU4m5qf+5CbSTt3/8SZauwxqYMgnFUneinpS2IzrlWAav4kxGRy7BpNgeS\nyuOy69HdBR2SSF2/1O1GcSA3tyKs+x1TOIzDx5vUYHhBWtdsrWmmbsArXZpc\nkQVQGvTY1nHTa8kPOyFCthjEBAo0wNZ8MxWu9v92UXlWKm8BzHv0S1yBMgOF\nnlgpzWCgm12ISidmw+7fS8s9OYl6bmLVawNlXdHv+ZucG/G8cQ7bPQJa8Y8q\nRDHySMrThM+ZzpFV0CvjkORYxoiqS0Rb9x/VZsek3tFbWe2r3bTdVKsE2WHl\nZU46HMJjuI8wRQ9r8o2nLIkQDqZ47GAfHuBx40Ho+B4stwspb95iBz3Qw13g\nmvtaWQoLmUg1qKc6cDNN3YTEaSuuHRlm/8o3yNMUCSDTXnhKsEpds17IOmwY\nsmrx\r\n=u9KZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "2.3.0": {"name": "terser-webpack-plugin", "version": "2.3.0", "dependencies": {"terser": "^4.4.2", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.6.1", "find-cache-dir": "^3.1.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "memfs": "^3.0.1", "eslint": "^6.7.2", "del-cli": "^3.0.0", "webpack": "^4.41.2", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.2", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "00fd8f792a330dc572e2e2b468fd7cb5ffd7ea51", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.0.tgz", "fileCount": 12, "integrity": "sha512-yez0HdpDf/iQVYGf+e/o8ZYWLb1g9d1nRRi5FIOZ4KfXbfSPT259UoqxPiSLhCnr0mlDoh+bucpYQSFbU0cEsQ==", "signatures": [{"sig": "MEUCIQCkzbtmcO//OHRmTeXlIjydsbYym02+Y/ZK3RhFDPF2MQIgSA3cbTBXElJgI/RTbSdkCE8mKX3pAiN6tqqwjgZcqXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8jc5CRA9TVsSAnZWagAAbQkP/i5O9vgYJzWTagjzoZ21\nEBuggHwhxtcSXg/Ys2XK33x3O50+khqYJUsd6wlAPcdIlX8BeNaAYP6T/LXm\noFD/DhOMrUaE3ebc3lrI/KcTdfzgd1pGHHKuyTk6dKMjjUCXSt20DoCE1Gov\ndPh7sBLDvGhPgB0ODBIMu/So+S39pJU/aP5F8K7f0qALcvPvTHYGN1jMtJWt\ndqwTzwmCwJkSojiVsX1Yl5T9yoAuDWj5P8yJxmbSUIy43o2j6p6Bi2QV4SwD\nVNxw3TrNJ1OdideNb23zzR/PyZhoyn3L2Lufnp467cl+bwTn2Op73wiNXPbg\nI6P8iyS3R5GlYMJOl1oKVfYYLXUmT0IyloHe2udxIEKU5dn55zrQ0zcZjgFO\nj2+uFOC6iHNGjRyKZgH1efLbjujJZVdRC87DfR6heQkM+10HMraeQYDX0lYA\nE+V0Sv4gq+8FULGLHi64/Pw9qzwM9tryug6bh/R4nmUsXNqHO4R/yqf6+oXW\ny0O7fF0bGWB949ymPSULASk8GDd1CUWp5cZbR6uqWqpiD1Il2v9pHh7nsivf\nJa2B0g9cbLmxFgSNbf6RZ+IzXvPcNqm/XTEc4CacaLI0jH1VMRdeNFAt6Jra\nEjfNSGeeNNsw0O45X0zTUKfzAFkRtqWZmRa1cneeDOu9h3qbEJvX/2r27Fqs\n39IR\r\n=OlRw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.1": {"name": "terser-webpack-plugin", "version": "2.3.1", "dependencies": {"terser": "^4.4.3", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.6.1", "find-cache-dir": "^3.2.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "memfs": "^3.0.1", "eslint": "^6.7.2", "del-cli": "^3.0.0", "webpack": "^4.41.3", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.2", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "6a63c27debc15b25ffd2588562ee2eeabdcab923", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.1.tgz", "fileCount": 12, "integrity": "sha512-dNxivOXmDgZqrGxOttBH6B4xaxT4zNC+Xd+2K8jwGDMK5q2CZI+KZMA1AAnSRT+BTRvuzKsDx+fpxzPAmAMVcA==", "signatures": [{"sig": "MEUCIQDgjR4SQpazt3Ol7Vj1kRpAEZXLH/Qv43PUoi7SFl7+ZwIgJWPWtcxtapxuNYaw0tVCItPG9bBzB4jDFYQj2ga0gbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+LZFCRA9TVsSAnZWagAA4bcP/jWw86Q/er7CuTxXZQRs\nXnv9/QIoJZ6vnuVDivw1O39HlofHbUdt2EIa8w/DYGlCFQlSFrlbs8CF5F5j\n4VM34S2rhoPPbnfRC8Bi7h8Or0Db3mkvF6PCx9fXQq7/miY7Eo+2/NL8pymz\n6ahCoIu0BSOo0zavr7x5bZJ1Ki0bi3mbkhN6ObPX3/2X9bNoQ2y00T43V+Nq\nDb7Ku0ks9Xg4OM74kGU7pSlk29OEBUucUNQ1/hRHN8/ys9bctaOvYD8Zm3ds\n1Xk5Sd8r0bLrWeM0VgAGilNsEhstzxfP0t0yaGx3drGmC89PZSjoTpX4Wtvc\ncGNrXKTFjNPxrRpz7koYCITlc73T55hHGaUhNUbGx7FrDHuc5xyBMuaFnYCm\nADOmxI/ey3KR9Jnyzlld3Vd0Ax9AZ7qtRbmNdRiAHJzExIyvG4VhDcnj+U6k\n3PXj3BQGY10+OBEtIFDaW3Aqhom2l9uC1ygKbCMn4FFMXso+VMkfK74o5tUQ\n5xBYzWMDcZ4+aZY3h8bWiqCkhafzI4fDKP9St69D8BfbFyqHpfEUB9ypl8qg\n8MSYKTsEQGxxaPvqAORpFUQcNxEcTq6oXeyg3VZoVZRDWxuQBLo6AtQ2yu9o\nCVnj6iUp71/ZBoeL1B6446TeA0k6/fkbQ7/dNNNqvMiddmTVlOeG1or37D99\nhF2c\r\n=p5Dw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.2": {"name": "terser-webpack-plugin", "version": "2.3.2", "dependencies": {"terser": "^4.4.3", "cacache": "^13.0.1", "source-map": "^0.6.1", "jest-worker": "^24.9.0", "schema-utils": "^2.6.1", "find-cache-dir": "^3.2.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^24.9.0", "husky": "^3.1.0", "memfs": "^3.0.1", "eslint": "^6.7.2", "del-cli": "^3.0.0", "webpack": "^4.41.3", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.2", "@babel/cli": "^7.7.4", "babel-jest": "^24.9.0", "jest-junit": "^10.0.0", "@babel/core": "^7.7.4", "lint-staged": "^9.5.0", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.2.0", "standard-version": "^7.0.1", "@babel/preset-env": "^7.7.4", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.7.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.2.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "6d3d1b0590c8f729bfbaeb7fb2528b8b62db4c74", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.2.tgz", "fileCount": 12, "integrity": "sha512-SmvB/6gtEPv+CJ88MH5zDOsZdKXPS/Uzv2//e90+wM1IHFUhsguPKEILgzqrM1nQ4acRXN/SV4Obr55SXC+0oA==", "signatures": [{"sig": "MEUCIAnGqWNjV2Sndjhhqnn2AfIcZ+PoakPxITaSKdvvm0nxAiEA0hNIej+6qmtoA2Nbw+9PHMhISWO1fM/Rj1sCVmTAmg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFwtfCRA9TVsSAnZWagAACmMQAKIK5yMAV+LzNoOlqmeW\n7WZ+On2IihT5XbFDxKJf5Cas9BEPY0SuHABK2O5mFK+xvpYecosdd3QBqxr0\npu8chElqkYA6sLilirr2IZLGgeZrtTC6tMmTGLnYuTATgoxPb0+EKK3IhZtM\nL5ZQIfTLtD4Lh6gX/K4uwARTOCMxxmHzMglewcDPtWa8VommJYFnKqhOX9lX\niodXwznSt1aaxmDwMPwJuLcx67Fs9YbzDabUGSCwqQFV/SYtndZdVAfmwxNH\nZGxo0xJVpJIsvXNX002oXE923UbgUIuvuhbHhF3Heexfpy1iWHpyUIKGcSkb\njRcEVXlwr79uc+028USr9gmGW0dZ0YtxBYQDTLnaYKRjwJpJIjb5c/SmDbDx\nltFsAoVO0w8ge2up08wE6UXcy9ofOpSu/hXI1LD6ITRgEFwMBgaOO8DFHc0Q\nfbwHsNmEsJ+7xOl9+1XmzotNCtBpywDSq2/zU5GonGlSJW+pwjY9OjpnfqLA\nrImV0CbQzurJgBjFDmo6P7wQAuJ3MN6QDHMeunZ9Hv+rG7uGLorwuslWt23J\nqA12Iean9uFkyLoaqqfKxN7Z1loj3NjWUP6VPZql2EcX9PbV5HA0lLJP4tMO\n8AXdYIodDcpBtKjzzBIvlrq26VTQf1SsRUGcX7WGU712KAZMGH10C2mWONee\nns4l\r\n=ROCA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.3": {"name": "terser-webpack-plugin", "version": "2.3.3", "dependencies": {"terser": "^4.4.3", "cacache": "^13.0.1", "p-limit": "^2.2.2", "source-map": "^0.6.1", "jest-worker": "^25.1.0", "schema-utils": "^2.6.4", "find-cache-dir": "^3.2.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.1.0", "husky": "^4.2.1", "memfs": "^3.0.4", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "^4.41.5", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.6", "@babel/cli": "^7.8.3", "babel-jest": "^25.1.0", "jest-junit": "^10.0.0", "@babel/core": "^7.8.3", "lint-staged": "^10.0.3", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.8.3", "eslint-plugin-import": "^2.20.0", "eslint-config-prettier": "^6.10.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "b89043168bd414153bab86f4362ac23d537b78b0", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.3.tgz", "fileCount": 12, "integrity": "sha512-gWHkaGzGYjmDoYxksFZynWTzvXOAjQ5dd7xuTMYlv4zpWlLSb6v0QLSZjELzP5dMs1ox30O1BIPs9dgqlMHuLQ==", "signatures": [{"sig": "MEUCIHENlomDD0ZQaXIXPTCR85x5k6JQx4CCZRH+/YGm9I2BAiEA0wJbEb33ml0VJNrMOCXO1OX5jEjHn7MgHHNJRBpsa4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMEKTCRA9TVsSAnZWagAAnTsP/AtNis3aVbvsXTw78h89\nXaFakmWIYaQUjnuT+lIMHuS/TdIgs+yI6LFUr9EJtWqFWwWaTwvC1REZrc2e\nn7zwSpamvB6MfEjS47QBGeFUSciktFAbhdGW7KTSlaQtlxNleXRCQ9T0ZD1x\nrSlr+2l26Fa9oJA5EU3gXmf1HYPaT0v88xYFFqnfHZf03omY66cAI9QYSZAe\n/mhLCx7WaTJzWx50m4zJlXjvrp5d4FzA4qC7E2y8h1EYhacTFQylXZuSnEAd\nuYlZBaGV1CnunTfyU12MV9v+O6gdwhC+YI0ZRlU/B47odpEX5V680Ik8vM25\nx7WJcrnLfND2EEYe/Jm7JINsuvifijTeTgZJfD7gVu/S6vHOfJ5XTXqvJsqg\nz2Td9LEkXCCvHMtJY9OlXOGUvh26p+MHmu/cGXwbgGXxmod6VZUo188KFmVf\n7lFqYjdqxIjNRLgO2x/7tZyV1A7Ne8Wh9krVklX5EkOz2LEZUDGR77SFwhSj\n1iZakaRmjFFm00CXE2ZXbZSECro9GO9VZ0b/5L8vrmT/suN8fsJyP6WK53qg\nAW0VvH4nIjAHhy4WUbCyjHaLMYDCcza8dVaN4kYj7/YNKXA2lEbs6PZKBltY\nczN40q1PZEzLWpf2vrgAO3jmf4MVmOJe3OCUhILL2eFLCHE3hGD+UX+g/7MA\nFxSw\r\n=LQPz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.4": {"name": "terser-webpack-plugin", "version": "2.3.4", "dependencies": {"terser": "^4.4.3", "cacache": "^13.0.1", "p-limit": "^2.2.2", "source-map": "^0.6.1", "jest-worker": "^25.1.0", "schema-utils": "^2.6.4", "find-cache-dir": "^3.2.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.1.0", "husky": "^4.2.1", "memfs": "^3.0.4", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "^4.41.5", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.6", "@babel/cli": "^7.8.3", "babel-jest": "^25.1.0", "jest-junit": "^10.0.0", "@babel/core": "^7.8.3", "lint-staged": "^10.0.3", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.8.3", "eslint-plugin-import": "^2.20.0", "eslint-config-prettier": "^6.10.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "ac045703bd8da0936ce910d8fb6350d0e1dee5fe", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.4.tgz", "fileCount": 12, "integrity": "sha512-Nv96Nws2R2nrFOpbzF6IxRDpIkkIfmhvOws+IqMvYdFLO7o6wAILWFKONFgaYy8+T4LVz77DQW0f7wOeDEAjrg==", "signatures": [{"sig": "MEUCIQDAR7XijoKxkk+jJNNR1sRpHNWzL1wSAIiBCc2eJoWNqgIgSj4Iw4R+VEgzW/mFyXq2EDjty5NRWSyeclgYa17UPBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMwzfCRA9TVsSAnZWagAAiK4P/25gPrJB0Fjnipk4QJA5\nu4mE2XOnGICImBhXw3MJc9LFQhaZ1Dc6yrEd1fNnIvWW5vrEi0c3dTBJpd6j\nGY2m9lh39RSOARUGAGLXRSuvwuLK/vvc6N5CElvy5sVhpocLkeg5BaRheYxm\n9MQzKLPb0dRfnnGx/+1xhpDRggnc+HgbSHReNF2ltPobuGJvWyAQfoRWJMyL\nZtDxFjgzxSi+Y53wgdfKXi+qSpfMURfgo6EBzuBuv3eopxxxiz9+25jfFpsz\nxCd10NqIbMxxyJaATA80mPJghnnMElP4w5QvofzRyLDhJKjBqlMIPgKraJ9O\ncYyQN/zsxplQnO9jVc6+3Z1H1tfBTVG85Yp8bjPKUWeg9Cdp4APym7+MOr3L\n+Fv4d4AuZnZw4fmThHhDNwE2C3ZueXhIfS+DFZ077w/NXDlYodVFgQTrOo0j\nMwuUrDysgcJgmXSEhn5I3meQvbC7iRCs4rnXYhuvE7I4pHgVRi3j3Eys4lI8\nCCCWearAkwXkc6yMSftFwHX0tXHPAtUwmTx0qj4BoO83vOx4OpiKU/kisSdQ\nlmDi7q7DB037rGw4ZKE2A1tBki2K5xaBbSjO7yp6rU88cmSK7jIfmcUy2bEQ\nP8uzb14UzZBgBNww+fSEhByYkWKKr5ej1pc3BIjzfxBkdh5gUKbmcYzC8DeL\nZH2f\r\n=uomP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.5": {"name": "terser-webpack-plugin", "version": "2.3.5", "dependencies": {"terser": "^4.4.3", "cacache": "^13.0.1", "p-limit": "^2.2.2", "source-map": "^0.6.1", "jest-worker": "^25.1.0", "schema-utils": "^2.6.4", "find-cache-dir": "^3.2.0", "webpack-sources": "^1.4.3", "serialize-javascript": "^2.1.2"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.1.0", "husky": "^4.2.3", "memfs": "^3.0.4", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "next", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.7.7", "@babel/cli": "^7.8.4", "babel-jest": "^25.1.0", "jest-junit": "^10.0.0", "@babel/core": "^7.8.4", "lint-staged": "^10.0.7", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.8.4", "eslint-plugin-import": "^2.20.1", "eslint-config-prettier": "^6.10.0", "@webpack-contrib/defaults": "^6.2.0", "commitlint-azure-pipelines-cli": "^1.0.3", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "5ad971acce5c517440ba873ea4f09687de2f4a81", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.5.tgz", "fileCount": 12, "integrity": "sha512-WlWksUoq+E4+JlJ+h+U+QUzXpcsMSSNXkDy9lBVkSqDn1w23Gg29L/ary9GeJVYCGiNJJX7LnVc4bwL1N3/g1w==", "signatures": [{"sig": "MEUCIFyxm3hHHaLuWB8gv5KygNhNo7ZwyDe0h3ZNTlyzXV09AiEA+s35Nu6Mn+46xRdRcFYpR8K4eM+t9+jT+xgao27Ftn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRsTFCRA9TVsSAnZWagAAL/cQAJWAh/tPCiOh9gP3+5Jo\neO+u0UFYUaIvUEu++yPTROgrZPJiNRd6gnw635cyaRKJql7ET8nYYZWyLrTJ\nq0O6ZS6nPzQQ+T7uErracApCqZytBJWSiJo80Zy9Nrn7PHQ6YJVXGlJw45lc\nRDxrCpkcWti9Kksp38aQ5RSboABez+RNkCs+TVysOzcV+j5hd5KXPY8B0nJE\nyHF6M0fzEu4zYmBElKcDlBEUnnShRK463b7NLRpCBOQhdJZw7a0HHDcSTd4+\nl7S3BObwLXRSpDyTvpua0kGBcVzfY/iWrsUTYODr3fJ4DhfOoQI9bShg+jXa\n8Dca3IGiUZqcrO16lghnNDc8gcToBvO7DtqOGZhPm3HvavYn8Ap/fo5JK/fO\nL19YaK5cxxqi4ICd4q5xZH7+ta/6/LTm0xjZgQ6XU8n4sYV038EitUZFbtbW\nD/ABr1hYTms8DJ2B2O+lOQ/AVVJyvhXnuWOtWhGIl2f9NzaTvEnuMQlCO5vk\n2CU0+RRv3sS4GA9ZYXHdwCzzF4HmooUhSyYB5UY5MOUEhey0xkAYHcq75Wj0\nNncSvTQFaFl2Xe2GuQ2iXPOweGITUqBAtlmVBE5kwtEb47NVTzSHkDUj6qed\n4Jyw5pcMCQeSbycxmdtBGLbcl4/GoLBSZ4cMJ7ZK3YuaCGSbLDCOqjJzuyxQ\nCNca\r\n=p2wf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.6": {"name": "terser-webpack-plugin", "version": "2.3.6", "dependencies": {"terser": "^4.6.12", "cacache": "^13.0.1", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^25.4.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^3.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.4.0", "husky": "^4.2.5", "memfs": "^3.1.2", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.9.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.4.0", "@babel/core": "^7.9.0", "lint-staged": "^10.1.7", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.9.5", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.2.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "a4014b311a61f87c6a1b217ef4f5a75bd0665a69", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.6.tgz", "fileCount": 12, "integrity": "sha512-I8IDsQwZrqjdmOicNeE8L/MhwatAap3mUrtcAKJuilsemUNcX+Hier/eAzwStVqhlCxq0aG3ni9bK/0BESXkTg==", "signatures": [{"sig": "MEUCIF4vZRDuGnWEbG2p2B/BdAjBluU2hdk9HqyLXwUPONh6AiEA7QIgEWCtmZxH7gfcc3906RhRZ7IGg4SlOg63gcz9Pso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepFIyCRA9TVsSAnZWagAA42YP/2EuKoLJsSJ0IcfuuRc0\n1Itg3EYl1ZJ9QCu0QuqYZEyk+gZJutZv75/2Y/Q5YtrLl9LmXMr5uZauboFO\nhACIlggj+5v2mIOgq4WODRDF+UgP42n9+FSRgO07UsQjIlwwNu7NkYftp3KV\nF0gkPz6SZU0b+gekkR2NBQ25uQU2Wwh9ER3axMZEownWodcVO4iwwfBC3H9y\nXO5DYvGqqVXijRhLZYX62Jv8tCFruph4esqWi9uwHK7YlnG2iD6BFCSekryL\n2XnSczF4rLdE/iXaAJ0ZGcJl1+woAn6VBmoBuRjc/1oGrK2IkutJkpyh3leO\nJ21WfrUjbznZvEq5v82KieNa8pKnl4tQtm0HW++qKtscwcUNXbNNy31DqSkI\nKGs7VFT0EiW0iRIYCzT5rqK7h3RNYFwYZWAvmQqJ8UzIqAlHlkQZP37tLueD\nhSmMpUVDfc5t0PwLVyt0d4ZyVCJbioXGBWSDn+f7kH569d+MR9//u+F/VVK0\n2+lr4kEjoTt9xwP2YNPB2BusjuBfLtHshhhpCFy2u+Fx1zdSPs9mBWIQIbDI\n60l6NL5OjEXb84T/mbsbITE5Ls+Bc4PEcBGJEvPfEHzGnKS2LNwZaOGiZ5xX\nAJxFJn4MkCTT8KryheBlzWF3c015JhoF3g2iuZBuc6DmWDzSNlYb7GpKHJzt\nNP+x\r\n=x8xV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.0": {"name": "terser-webpack-plugin", "version": "3.0.0", "dependencies": {"terser": "^4.6.13", "cacache": "^15.0.3", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^25.5.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^3.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.5.3", "husky": "^4.2.5", "memfs": "^3.1.2", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.5.1", "@babel/core": "^7.9.6", "lint-staged": "^10.2.2", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "533d7189efc43d1bc21d89f1cd2b4b1e0c7441aa", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-gHAVFtJz1gQW5cu0btPtb+5Syo7K9hRj3b0lstgfglaBhbtcOCizsaPTnxOBGmF9iIgwsrSIiraBa2xzuWND7Q==", "signatures": [{"sig": "MEUCIDitL2L4++2JSxytPXuBql07ay7q0YtuIQbvdKmD5yyxAiEAv3D0DNq5H+yZeJyVvFuLSOkgI/ghGPhNWhPzYaq6iW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerFxHCRA9TVsSAnZWagAA6qsP/jomWWCrO7W4IiNro7NJ\nj9x2xioTSk1Ht82qjvtConMKjMY/do0Xm53na91u/QV2uzlcn7pB0gIosNU6\nxTQ5+m+cyTNaKq0cgqmoLwGUdtMnyS+siSdSFAEg8OJZfGt0c3zZLSMsvqZS\nGcsR151+t5SXRGsO8BpMTZKOcUC1/2joUR0fn0m5qUabzyQ6jBaTVNlxZTMb\nutc6iaTyHmvJl4aUyEtJl0Le0Tes+miKJ47NsqUZHoM+CHg508RYli01ODyd\n0BSnIYVliTTtRWlu2ezhQ1IZXgEXU5EE/c46PPa0BsKwVNmQC3Zn5U6caZaA\nux91ovnc6GmY4/aLAhcLts07wbg2mXmjgEaZzxBPhZKgqk3ex2ButLoUn3FY\n99MFcpAAacI89DfmJtgHL5utks+ZoibiOPndBtMg8CNdRqRqz/R7wqWp2OUO\nICMsiuQRTe1/YSAZqup5v2ddO4KepjJvEAg1yy4KtXi4nFtbRxuurb2EsQGq\n0mvsdJc8WNu1dFuqUkWWwSq8g6HovRhRd+Hy2tyiqAmFDIsQ10xy6U6Jl3fE\noFqrk14+q3Gos2+e/WaQ8vyJYnDT4VrBRsPF0sTkK/aWf1PEsiZttyMxcZvB\nlAT4QygGLeolm/ZzA4/Nc5rhOOmbI60EUaTOtTrlKJ61notjzuXWYY5Rs/IH\nLP+I\r\n=+293\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.1": {"name": "terser-webpack-plugin", "version": "3.0.1", "dependencies": {"terser": "^4.6.13", "cacache": "^15.0.3", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^26.0.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^3.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.1.2", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "next", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.2", "@babel/cli": "^7.8.4", "babel-jest": "^26.0.1", "@babel/core": "^7.9.6", "lint-staged": "^10.2.2", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "31928c9330a582fb5ec6f90805337289b85cb8fe", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-eFDtq8qPUEa9hXcUzTwKXTnugIVtlqc1Z/ZVhG8LmRT3lgRY13+pQTnFLY2N7ATB6TKCHuW/IGjoAnZz9wOIqw==", "signatures": [{"sig": "MEQCIArRhKrld770M47ipwCf22tNsox2NI3osGfKK9Lf5lrdAiB9ygO/UTJwps4g2TQUQ/Kl0J26x0YXozkt3jUHlkJsog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJessFFCRA9TVsSAnZWagAAu7UQAID6v35X+1Cx0LEzfu/p\nM4XrWOZQaHm2T4AFH7th2Clzhqd0tpXFFHvVTdC4vGSjFZV89DomKpAJuY3A\nWYienrGBJBdorlCKrpRt8sUJa63DlsauA1SFJKvmHhfgfTlv2I05LTR5xot3\nQgPjAfM/FW1zV1Qf10RQRHCIgksTl8xUygMKQPXB7rnndkZG4rzQUci0g7XT\n7RQbfx1/nBd3ZJ9Z5R7SUv+0xQdjoeuua7XW+eAKTurotGt/Jpbccsz2ehGf\n8xY1s1sf4IallYcl8QP6AgDv4ySx1Hl+prZR1WLqecCmsE+RA1HiybLs2ydm\nGJyfo+HC28rQcBROa5PBlGgdSH8lLAx9G9FvDf0ZG5iQBQZUhQqzkM4F2E7d\nuy54MAUB29nHaIqD2oeHiDkEJFTdqTNS17of12AbFTeIa1bYWxyaRE8aAZ5g\ns/N4eoDa/SYxC7IlafAQjZmNcOSY2PH+nUjcQNsfdixTylj2wH2CWCwkZCWx\n9VHSxMzdJvX3IIXh0dJjP0pXlfWEiN0EnwuS4ojIyJiI6Tt/aVbjVFMQkwzc\njaCAyD1Xya9a68ALnFETOl24sI5d7Kx/bv/DEYtpr2aDXQCOfB68n53nmAV+\nmmGZ/yf58IZ4L0zMmhc76GZhBn2/7TYHzkZ/gs+VfbMgTcjM+AuvrrfZ+3LG\nEZ0Y\r\n=cPSk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.2": {"name": "terser-webpack-plugin", "version": "3.0.2", "dependencies": {"terser": "^4.6.13", "cacache": "^15.0.3", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^26.0.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^3.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.1.0", "del-cli": "^3.0.1", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.3", "@babel/cli": "^7.8.4", "babel-jest": "^26.0.1", "@babel/core": "^7.9.6", "lint-staged": "^10.2.6", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^8.0.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "fdc501c73847d8904f6a80c5009b11ee2d11b8eb", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.2.tgz", "fileCount": 10, "integrity": "sha512-QeBHLJzKJHCnrPNlZj5EmOF6wwvzpVGDHvTrySIH8+jZEXfcKKCiriRmF6945rKzuZDnkOEU/LDv7qtPiiyP/Q==", "signatures": [{"sig": "MEUCIBUNCv/XPWftuHCY0J277IGIgQxJAkETVRnBEmUR0zkaAiEA3sQuUAFn5zJciw0KTWJoQV+mgTAv8fvVaQhwfwgRPOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezR5/CRA9TVsSAnZWagAAZbcQAKQxJQtJRBFi22+JwcRb\n+5yzhI6WZmtIs90aUZ6aDhwwMCOSiwBjn1YbXUduqJp5y320VOKpjfesDfbn\nS4Dm9PWSEh7I3HS3L7sgKzozjyxi31CUaO2nk6IobZB9fUnXxdLCI/Li3lXv\nEMPZQalTVFsg/My4tlPsgvZc2QoGJdOCBV8nLsAH+TBpxGXhf16sUI8Yn9O3\nH9o8nwah7C7EvH69wMDUQkjy3xYNZ7cx/ptHYiD2LphzNsjpXK1Cs+dfr2qB\nHoSdpxt90T7tm6DofgUG6SRqjexQh5FQexxF/NUj3Uv6hJggNUb7HIqn0xQ+\nRPPF7G7vRGOYOKHNCc0ede1MnoiCIYDCJ0W5MbSBS38BR39nFyAeEg4dmH/e\n8FXMtXy1vifu5c5TrKNBORlNDIHdMjUPE0qEXQeXpy+Gs6d0McPyHpg5BQQv\nJbE+c03zvcFyB8+fTLxjiLakrZePn9UZBWjYpELubHC+KaIVkcH5wKtHJK+1\nzq4jqVekOcRm3HNYsBcW3MPsFwSWcExAsMpjUqLgfZ4l7yrlGYwtqPIYSA12\ninv8+5itEVmirWb4J4yuNJkbJOtPuB/+r384WlYL+5NGkz8VfJG4RC9GZMX9\nExKhQV1U9ujUIL+CnP9cdT8s1zi+DrteNWejHseSgnyzyPmm+9KQAEJFjwNa\n8Jhs\r\n=a6iZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "1.4.4": {"name": "terser-webpack-plugin", "version": "1.4.4", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^3.1.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "2c63544347324baafa9a56baaddf1634c8abfc2f", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.4.tgz", "fileCount": 10, "integrity": "sha512-U4mACBHIegmfoEe5fdongHESNJWqsGU+W0S/9+BmYGVQDw1+c2Ow05TpMhxjPK1sRb7cuYq1BPl1e5YHJMTCqA==", "signatures": [{"sig": "MEUCIHRQ7qxDODRDchnFa7nrHvBYWtY2XQqbIYmbBQfeo3ijAiEAlq3vFZJoL6mpxcdozGbe3yYOdfIK00txzXSW9RD9jGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe140kCRA9TVsSAnZWagAAdTYP/iNDoKFJ/9ZB11Msqcfa\nyvrtP1gMK9dJajvsGa4tTQjqzWj9VS8wCIpj9R+SLAltzQhU5VpKQtdnCXSe\nwFMqABPOqKVc8q7r3UEMRWApfRf5FKevzlURreDRJxGk5KJe74W0R9XHszLP\nsCWfxGxQqCVcFI6yvGb7Pu7nWUMaU9KA4QZU6ocrdOWYXQzICtQt7CDqYGPR\nnq2eddEXgYfkEfjLpjtUxws5q8fzZ39EnhoiM1zOTlYc9gw01Bq5IShTOlO8\n/vMr5hgtqbuWfhqWZAu9C5C8K3ghuYmslc4nCBupN/0jUWQjyTHiAF1XD0QL\nrz25qKB0Jkp7wf1AKOg6cGGqZIzIFQ9qrduiWzFycyXr0UmM23cua+7nGTN+\n8ShaKOCpliqZn7r6cvDLOYCvVPE/2hCIUQlvLLto8pz9mxCw31Gn+966OFjF\no+IbQ20A4FGVqhqmx+Qewvuh+fnJblsLYUsP4lw9nCphJaVA2hYH/I69vtea\nQeP/V2RLCP5JSNMwmXZx5PsrhxisnOKRybcb2BmEpd4z5dgTPuAroMEzKOQ/\nlAXUS9uZPj41QEZ1w4bYhpTwzqWo6mAe2kxDmivVvTkyNLl4EiQXxnDdXNUD\nBvQnF+SZTRJTMpCPgTlaSmBxKTPwq5dp1aNNfRRnKDrSKUvSuGHMt4PxZIvL\njgsp\r\n=D/mk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "2.3.7": {"name": "terser-webpack-plugin", "version": "2.3.7", "dependencies": {"terser": "^4.6.12", "cacache": "^13.0.1", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^25.4.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^3.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.4.0", "husky": "^4.2.5", "memfs": "^3.1.2", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.9.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.4.0", "@babel/core": "^7.9.0", "lint-staged": "^10.1.7", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.9.5", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.2.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "4910ff5d1a872168cc7fa6cd3749e2b0d60a8a0b", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.7.tgz", "fileCount": 12, "integrity": "sha512-xzYyaHUNhzgaAdBsXxk2Yvo/x1NJdslUaussK3fdpBbvttm1iIwU+c26dj9UxJcwk2c5UWt5F55MUTIA8BE7Dg==", "signatures": [{"sig": "MEUCIQDljeLhgbZzbkOsD2A5qTm1LwcsnvlcDjthH/40s9rfNAIgHMgZ9gDGK8oCCYLM2Msy/D4LlSw+AOU1CjxqPEgl9Kg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe15STCRA9TVsSAnZWagAAg+UP+wRgQuYufvzlQZpncTAZ\nD5TJbwe/cUzI/IU1X4GGZTNT7c+zvhB8z7gTr+y8BrkZ0wzLpzxLPIiO66Vq\nWg7PS8Rwfrw/1uVp4YhEYDewDteHHkHmd80kKatuhOCIXa/wrASu4Dug7/SF\ndcBU6+hVLRc1iCJb8eqRR46KfIMSyr0CJy5jQ2kfghL7EoaiqAjaCAq6HW95\n5GTji4mrF63/fKISiE3RR7uG/BFDkmiqcyCkm+RFShlgHzYWKYhv2hgwDiPy\nwaSEeSPVeoNE0kKyyFwU5pX7Swg9N/JKM/0E+oMgq3J3XgljYjCLXk0qMNLD\npZ35u0xqoaQdB7TWUNQpbpf54P7wAKvr3ur1sT1VNO9ObWWfor+IEGxHFQoe\nri5LekKRN5RTvbbXt0G1Cm7qhYyn+uwzE9YHRe2Iq1MVcOH7j2EkK6TyGn1G\nRQERotl+/2e5oxqLFUUazCxPdHTeU8w0vk6vICxfGCJCVglm74qlBHw/WkNH\nnvSIGJdyRTT1/EvdFTSGdPZVk/U8zPxu/UapyIyxYB1vnlg5dn62JITUCdU0\nWhC5xRAjCliLioR5+LnZ5tPitI2MtjCN9nKKZdIqEG4oRnGs9P9jQWcdhLnh\nCnwxmV0cNZxRyp1AdRQIGyu8j0vZdyrImf5pjw1BUMGrsftTHE47/IUPpgkv\n9WhH\r\n=AFwF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.3": {"name": "terser-webpack-plugin", "version": "3.0.3", "dependencies": {"terser": "^4.6.13", "cacache": "^15.0.4", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^26.0.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^3.1.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.1.0", "del-cli": "^3.0.1", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.3", "@babel/cli": "^7.8.4", "babel-jest": "^26.0.1", "@babel/core": "^7.9.6", "lint-staged": "^10.2.6", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^8.0.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "23bda2687b197f878a743373b9411d917adc2e45", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.3.tgz", "fileCount": 10, "integrity": "sha512-bZFnotuIKq5Rqzrs+qIwFzGdKdffV9epG5vDSEbYzvKAhPeR5RbbrQysfPgbIIMhNAQtZD2hGwBfSKUXjXZZZw==", "signatures": [{"sig": "MEUCIQD+LTwhEdS/sKxmJfYLP0x7GlvC0QQACC97efTXN8GYiwIgZhwPSufxp9hT+TRqfYQXPaLitSzUmEofGpEHJ/RQ9X4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe15i5CRA9TVsSAnZWagAA0rwP/32tFPMbVLST1iAIkGYV\nxIGVqT1VYF9RRUlwqlxAe9LI256iDpbVE829JiJXD3FSX5wCDG1ieQGcqAT+\nEgoW2He5DBmDjBl4kKvrU7sHaPCnPyOy+QXoFT68MhuFrzSjkkEfoWzNBP6x\nGw/gNlWoqu/2Xx4Q4fTXpl4YjBPTWpJ8O3XnIF6aFN1nzpt/jcJ/zA+6Lv18\nzCzIFlV9+qe6rhjaxogFtLwhCzLUukHD8n9KVyfAMLzNjFmWxBOkYVzf9T7L\nK7DOOEaCme9zmeqcLiGIDvzZHWbWEiQRcdYOEtoaExrULCiIQlbBppi+VUYl\nXZEJCM5CLuAn3T+ydXi17Hi276TJp/4aTv7C2h5azhw/YT77kQfDH3rgpT1T\n5h8JCod9ryxDFpma7meVBFkTNRALjveKKC0uIl0VN+YPy4tBLDc0648Xwz5s\nOx7P3r8/Rh81uAZo+xMvCFdHpzVwVU4dWvGxA5yD0YdtyPe3b9tLTOd5QjTV\nWaXNPc/Yjfz9psHhtjbNQBYT5ghcDY45HknLQGY5EeNiLoGEKOkwt+0r1agZ\nDU46ehLuMqZrDn/k0zXZ4/X+RWJsEcimDn7f4nsK1asjLDYfM5SVEWf40eWD\np6B34VgIQwMVlCoo4WybPYEwb6Bd6Fa3Psn3jbmZqq/DqIIRxKPkWF5xJuga\nt3Ib\r\n=e3fg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.4": {"name": "terser-webpack-plugin", "version": "3.0.4", "dependencies": {"terser": "^4.6.13", "cacache": "^15.0.4", "p-limit": "^3.0.1", "source-map": "^0.6.1", "jest-worker": "^26.0.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.2.0", "del-cli": "^3.0.1", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.3", "@babel/cli": "^7.8.4", "babel-jest": "^26.0.1", "@babel/core": "^7.9.6", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^8.0.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "45569bfacff6681ba733abb493e8865d4d87080f", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.4.tgz", "fileCount": 10, "integrity": "sha512-hcDwaacWj4gpWAC5+Up1spE1BulQEiLV63qN8TovDmFTEjC098vDQcO4BsO/HYKevWwisnSCy/qUNK7OUXn22Q==", "signatures": [{"sig": "MEUCIQCJGVwJW/j/jj6aJ7wTKRhN4kG0Pr7GXTRCmTcPKQ2XSwIgDS22MzRxqxA0GAAUpFuEQmVJhquwZMR5u9Pl1thYZM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5PJUCRA9TVsSAnZWagAAQFcP/30Eicj1xxciOsCqIYa4\n5OEj+kmZzg0Is1WFOUwIKmKyH2pXJZ59X7/nLNh++oUxXUrvtTtQu1GXohAl\nuxPBhiE6sZ5PvF6jgxXe/F2BVUS47ujbQCGuSKValm5n3cLGZQokHMY7Nn3e\nadMb686bnhnnZTxHHt+EVEOiRKi51ESvnuR0NXgakXSxSdtmQWKUgdZXivDC\n4kzDmH6am7wtUi/CQOTpyc6hmfByqatpQtAG3dqI9agIFyIZJPJgPs+SY5iv\nVCNDy9OrVu8LZbQ7nrRLdtC6AKb/5xeGL1li+RmFhCfutXYTpg4DbGAMgjxQ\nEPvbR8QTHi+lF76iSpRSiucwRFFNdFuZersLfTuiJBZ/xcxjNo/triZcC1Rg\n8TdMC2sni6qVucMRXV2pHy+sbSm0BWIlX/cqswArriY8GgSUz6y5EQZvIpUb\nsmFxQggOTV+fEiJsgiZOBj9U1OD6JSRnfW3B9cTJHFWWMwE64myemftLr++D\ng/ORqwcMo4fRF/JMpE1B8jvDmF+Dbj+glvgZqD+JrkOWxIfI4n126SMeOrhM\nIzhWky67OTlxE8QBlDQoLKOjKLJhvHvRVLe9ac+1gNLMy1bEiniEakvZsRwX\nVhtRV/acbiDcYo3O5kPTlnM0JbGJ8UW7tRvIwd15VDL/g0z9RXZkx1dg4k7B\nxIg5\r\n=U6Ur\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.5": {"name": "terser-webpack-plugin", "version": "3.0.5", "dependencies": {"terser": "^4.6.13", "cacache": "^15.0.4", "p-limit": "^3.0.1", "source-map": "^0.6.1", "jest-worker": "^26.0.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.2.0", "del-cli": "^3.0.1", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.3", "@babel/cli": "^7.8.4", "babel-jest": "^26.0.1", "@babel/core": "^7.9.6", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^8.0.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "f048cfbddd6098b10a8737f3165344529656daad", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.5.tgz", "fileCount": 10, "integrity": "sha512-pyHUyfQUZB3ciYL81GgXzDh8Qb3fGED77xDjZVSFYSN1cQnWgC51OMPKj7vBWVZx0GGuYhpa9+Vz2KxkzXWhBA==", "signatures": [{"sig": "MEYCIQDNbJL6T9pfreshV1nviOITPGyWTfJef7nbKln5J0LOgAIhAN6o6uDvUqhid9YvPCy2FUoxYlbKnwCuLYEVhBr1ygmw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67569, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe55IYCRA9TVsSAnZWagAA/SQP/irRTpjDJgY7Zf+jQJMV\ndZS7kwe9jSdBCjx4tqjFQvWvRdu31D5RGwLRFeC9OkHKhrwSEzO4zkccnOC6\nv2Zu6jxSYpj/9k1A1+apWe/5xkl+YA+YAcj7VCTMv/udcAwTq8JgfztDgYYN\naZXDqLpG9aCLdaOT1qlwJAc9HNNo7tushbCpNGymyxleHUbGJfmYDlA1oNME\n63k/eHBZrhx4IqIJJGluKgTKd+EqPblE4k+b7TbP83ILPiM62shUomS7KXxl\nYCc3hpVVg/FyS4Vb0+jH9uUf/EZ3oXbAtKEO1tOcUhoU7JUKVI4l5USAQoX+\nolRMpkYyd0gOEEXgtn+pP3onWbzZq/TY4JR5SzP4TZx+yo1z9NsaQ6ioVEeB\n7WoHcoO6JOBwd56Azf0M17gLL2A07nUFkWgJoXwgpY7Gz4Cs8U/aTnQ3ec7v\nVYuHUvuoUgwm4LsvrcEpvidltlAy0TL5aIg6r9kAiWnMsuto2p3oqhvgfbrR\nZruWrWG3OmQ+P5/Be1ArQ3ZI7UftxjAvOWTNkIYmcvIEd+J0gLCj35qrtcAr\n0nYhK5/YpQRs4U8qtjrtVUBNEJpKtPqIbDtYS9yWhjp3wAX+WDmCCxkFgV09\nkuszynbtU/jYn0fj3xNqNWUF8zE43sj3EL5xKbAfR+L9ioRut0zLB38XRvDx\nrvit\r\n=/Yd+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.6": {"name": "terser-webpack-plugin", "version": "3.0.6", "dependencies": {"terser": "^4.8.0", "cacache": "^15.0.4", "p-limit": "^3.0.1", "source-map": "^0.6.1", "jest-worker": "^26.0.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.2.0", "del-cli": "^3.0.1", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.9.3", "@babel/cli": "^7.8.4", "babel-jest": "^26.0.1", "@babel/core": "^7.9.6", "file-loader": "^6.0.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^8.0.0", "@babel/preset-env": "^7.9.6", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "db0a108bbdd3680d72c9b491fbabad09ba207b99", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.6.tgz", "fileCount": 10, "integrity": "sha512-z3HLOOPUHkCNGkeEHqqiMAIy1pjpHwS1o+i6Zn0Ws3EAvHJj46737efNNEvJ0Vx9BdDQM83d56qySDJOSORA0A==", "signatures": [{"sig": "MEUCIDDOjccOE+ijA3srIXpUgOBCHm+/YiYvsIMJFWjqKACDAiEApLHVITyqftcM3fdfZUH9P7XJ9qrTI1t60D5ZLAsXASE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe64chCRA9TVsSAnZWagAAVX8P/1euIEhASjdqga2r+/oy\np61jMSSlWnpTv3Kdjx8sZjgKYeq1Q6mLa4v7WlLUmq9fiI0/Gw9GU5Rfm+9+\ngXbZrY7+/eP59K9hOP7RQBXIWu28ocBFBPtj6YZSopu2uU1ajZjY6co9aVI9\n9id8DRgmOuY5TObDHFk3xRRWGCv+EFCTiYbjhg9L5rVXpyHumoLQtJcWD0Os\n7FUQN29gbtTllmiKpv80jNgnJqCMhjHwNStP3lAqUWLGjz+t1c0JiX6ZUCOK\nciUzVeVg7s8OrenKcm8jDjbkojMSBMpxBIu/haoxPvDKqTKsQWVBEwdQLtyt\nfSunXXRMZ0Sza1ZMK/wnT8NDIJy0ke5J0ynL3VOMVCk7meVVi1g9yFPihoml\nVjwl46HISowRcaUHUuN7Gg5kwMBffNg+YeIaWn8wB9UkXhZ5i0JSX0kVR+ek\nOXYgzo3A65Irsa5f3SyFB4tbeJcl47iaoENuS3n95iE7e95tX1KPcNZyFaNN\nLNvSYxT7jh6vODo7h+mazNsyCB+3+KZc2aGGh0xMD7LfuJssQQAuhrlkHs30\nZ7IfmQo1CUJQZi8VMq3B+Pv0Trls+n+R6c1Q+ivL2yLe+x59Wf78wnM/bkDe\n9SQQX0/2HtLtdOUadjzs+n5jHhOYwWBka9A3y6oOuqYz1wGkeTFcSh4YoliM\n3aM2\r\n=3l2J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.7": {"name": "terser-webpack-plugin", "version": "3.0.7", "dependencies": {"terser": "^4.8.0", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.1.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.4.0", "del-cli": "^3.0.1", "webpack": "^4.43.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.10.0", "@babel/cli": "^7.10.5", "babel-jest": "^26.1.0", "@babel/core": "^7.10.5", "file-loader": "^6.0.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "@commitlint/cli": "^9.1.2", "standard-version": "^8.0.2", "@babel/preset-env": "^7.10.4", "copy-webpack-plugin": "^6.0.3", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^9.1.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "db23b946dcca8954da3ebda3675360bceebdc78e", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.7.tgz", "fileCount": 10, "integrity": "sha512-5JqibUOctE6Ou4T00IVGYTQJBOhu24jz0PpqYeitQJJ3hlZY2ZKSwzzuqjmBH8MzbdWMgIefpmHwTkvwm6Q4CQ==", "signatures": [{"sig": "MEQCIEYIKWc7ACSUw0p2Aj9sEgpfcIUPmju8lejCdZKK8AX0AiBgPjy1531YB+6wZmaPH7G6YcUl/ZA2mXseb0ivdBnTUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEG1OCRA9TVsSAnZWagAAZZIP/2eXtOLtLLit0vPcBdr2\nIo7UnnD20RT8+i76mwcJBtETiSwNEQG5E8ho2vjfENn9dVthgyent99MWM+z\nW/TpLT3JKbfARtMsDfHVn5L4M0wd3aBmIuD4EQ0jRoSj1zAYMaLlO/uWR5ux\nX3GE+YY08TDMaVJ8/U4dLlTXEjeElNuMPt39yH+fTuqEBcOZ4jfIAcRv8nWm\nzlyC5gEFjmY5vX5aL/Rp8b3KMW76rb0AK7aUeF7Qf9AM4o8yZswOYxleCf3d\nyF/uanIizmkTD/TA3TtfcM9TWnBXJ9Z1fD+BVkJ2feRm7J0ZzTlCw8KRLuX5\n/jXMlyKmQ/oJct1hcvEcj1u8ZNC1/QWIYhM/TiltmZtZuSuzyh7WrK3RBlUo\nJU+03X6yTOGhE2kYyr1Yv77BeB5uiBZgjx2k7D4ImsIM7HMPx+gkzQcm+lAS\nWX+5T4X5NKEZIT/DDzJqEnVGtoTcxd18ZXSyxYQ162C0ApqZv51utoDSGPg9\ng6DFWjzmRQO++lGVpspZShz1Xb3yTo79XSNZ0sK31iIrGytug2hM9v96goGn\ntnO3op+KPibQ80yxaLa6T50rqXenLpAXG93nSVWktSkS2hyRIcV5oNf4uWBf\nUPRK2jXPqgViS61Ws7AWkNypRKSKx9STqnXhPdk8zDeLeRYcQvbfR16nJeVc\nZ6YG\r\n=JTsf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.0.8": {"name": "terser-webpack-plugin", "version": "3.0.8", "dependencies": {"terser": "^4.8.0", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.1.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.0.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.5.0", "del-cli": "^3.0.1", "webpack": "^4.44.0", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.10.0", "@babel/cli": "^7.10.5", "babel-jest": "^26.1.0", "@babel/core": "^7.10.5", "file-loader": "^6.0.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "worker-loader": "^2.0.0", "@commitlint/cli": "^9.1.2", "standard-version": "^8.0.2", "@babel/preset-env": "^7.10.4", "copy-webpack-plugin": "^6.0.3", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^9.1.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "d1a53442a143e09e00c880e8d77c1e79cb05318b", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.0.8.tgz", "fileCount": 10, "integrity": "sha512-ygwK8TYMRTYtSyLB2Mhnt90guQh989CIq/mL/2apwi6rA15Xys4ydNUiH4ah6EZCfQxSk26ZFQilZ4IQ6IZw6A==", "signatures": [{"sig": "MEUCIQCzF2Z8+rKOeKF+2mKrXTQcIWzLEacotpJymFTYu5gSsQIgIcnFmm1HZzQILi+Uyb+eZXbqJ+NSYq2VgAehn7vmRS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHvuDCRA9TVsSAnZWagAArksQAIt6+5mtn18jEDJKyTvw\nshk6pcT3mRXqpYNQ4+fgpBlY4RHPhf/4dxSwSZDYTzR15uLMPJeCQEloccq4\nNhH75CGxn0eWfuBWGlrIsT6IH784BHPFDgEIBKyTjIXYAVNw/RSH5DGdkMzV\nNQxGq2Zz1PuYtNlAyumfZK9s1ZVX0e3EIewH1v1BdYp3n6Bfiqs+ZCuWCkss\nm724C7nAPskcJo2GYdCbk9m7sbStNk/nsv881IWgib4/lri+j3Xj3RB5ILJr\nNscZG6SmGaCCg897DK+kRLm75wmJClAzm1d9+H3tQXm3SRyat2/JweUh0FKI\noys/U53L8EOKSZfqPqrB8w7fou7GffyDLOOv0n809RyqxXK0qhAtdEk/e9uu\npB+EhuRLTeY+CJx8S5txz0byHLbWQARdgfjmsUNYTr55bHQUUacnzw1OAmdN\nVOyyxXqpIG4+53v0MiwrSpL01anJKawEMEqEKOj8EX5AuGyDKacHBQ0r3cub\npRRis9KE0BwgJSCfnLtDtE8qVHoYAmwpFORUVfgeUBUE5MtWUK1y8fEq45f0\nnzHB/1C80cX5m6VOrxE0nOHlUbr4vbbseYGZfaYqB72jSV5oTUFJ22WUfnoH\nL6n2+u/nmVGKrIF/oBfwe9NUphIungiZzonwJX4aygEL79gJL4zwLHe7yG/x\nxXrb\r\n=O118\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "3.1.0": {"name": "terser-webpack-plugin", "version": "3.1.0", "dependencies": {"terser": "^4.8.0", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.2.1", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.2.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.5.0", "del-cli": "^3.0.1", "webpack": "^4.44.1", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.10.0", "@babel/cli": "^7.10.5", "babel-jest": "^26.2.1", "@babel/core": "^7.10.5", "file-loader": "^6.0.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.0", "@commitlint/cli": "^9.1.2", "standard-version": "^8.0.2", "@babel/preset-env": "^7.10.4", "copy-webpack-plugin": "^6.0.3", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^9.1.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "91e6d39571460ed240c0cf69d295bcf30ebf98cb", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-3.1.0.tgz", "fileCount": 10, "integrity": "sha512-cjdZte66fYkZ65rQ2oJfrdCAkkhJA7YLYk5eGOcGCSGlq0ieZupRdjedSQXYknMPo2IveQL+tPdrxUkERENCFA==", "signatures": [{"sig": "MEQCIAVHFSbiwrgb8wBMP6LWr4CEHCwU7MMUbdXQM5V4W1Y1AiB6iNxfWmreZppG0MYcohcI+WhMoamQz2aHnnPQNAQ/Ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKEehCRA9TVsSAnZWagAAq3EP/2SaKbRbu5tkIrUMp3dX\nBttfdVE04sUY7ODdy5roGG8SuEw3xcOAbAgB8/IlCRhXQMuBtuNgVA295OAr\ng2UAjbYEfKn99wNlVf6MJSGe5woW4ZaTWJYzQRJjjMxI28u8/sXl60+SJ6bt\nw11Z3QwhHmQyqSwpdQgKkxuG/CtJGgwGDAkFdeKOZCNx3p+VWRdqmLpdOrnv\nT5wwoVOmoiCjAuqmfEELoZEQi8bzwJQ2sn4xmhYPeC3qA04425m8dmpIejD2\nugJWY02AP9/4B8vEZ6LdQyY9AvjgEwK7ueSNfRXyFYGWH3pwWTaCP6AyCz4W\nsnoAySqcSwn3ZQ0oX1VhtftwY6pchftljYFJvQjIb7z2fu+jGeIRPKs+8jQ9\n3twlIkfqcN0Td/FfW8XgG8PO4bDsvH7VJJVY+CEWxf0DsfFBu3Ts7uKm/9G2\ndp1/9n2dP/WLtEdE8F0gB+5OYRukr2FBBhzqa8flF+d2LgK9sWPEzlkkkQlD\nEjrS4uLuNPEZVRD/DXCEUA/HsDnSBdjXeUEn054F3Q7e4vMcuB2BMcmT367u\nYTAW9oyQ1+W2H+IjL+klCJ5OS52+SRPDg5tbqvCUC6VhHWss7TzcMmMVBBis\neWjKoY5y2oH6NqRlKQiynnGuybAv15kiNpphw4z+S8XnPLQgl/woavMyC7zs\noUq/\r\n=WaxK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "4.0.0": {"name": "terser-webpack-plugin", "version": "4.0.0", "dependencies": {"terser": "^5.0.0", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.2.1", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.2.1", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.5.0", "del-cli": "^3.0.1", "webpack": "^4.44.1", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.10.0", "@babel/cli": "^7.10.5", "babel-jest": "^26.2.1", "@babel/core": "^7.10.5", "file-loader": "^6.0.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.0", "@commitlint/cli": "^9.1.2", "standard-version": "^8.0.2", "@babel/preset-env": "^7.10.4", "copy-webpack-plugin": "^6.0.3", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^9.1.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "d371fe78312c47d33452e465a1096db893613e78", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-Gb/bmPMavJsDTYiIocakp9OJhrIBnYrWa5VM0Bb2RngWmszeQUN1xFNh2E8Re+9Cj3/sPrA50Jj/q0nzgLAUuw==", "signatures": [{"sig": "MEUCIQDe+uAA7amwPnKymYyUBicBbDpYLb4LayFAzFBCNLMbdQIgJvDsyigE7xpP9UPu1rcvH+7h1GFgXZb6SC1PsSfueaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKXtcCRA9TVsSAnZWagAA+l8P/iF0YRFIg+cq0XUHQAKX\nCBWYM73IyibhPg6+NGSLjK1YGAZrJi59xKqiEBYXfTNE571phb6eOpiwB4eU\nwV3P3vOKsEO4Kbz/2fsKbZBGtkvW/6kEhIawckCJ9niO3juuwV1Nh7kcT7A7\nrgINMDIrpAtfdBzu5wty6oTxbY2UEcX+dIl7IWER9cEXuR3EdgcyvsQ04Irl\ntwoqD3I7RsU82fbKDdNoqlqVTfnxXP1E2Vefrk0TM/Y7IEyYmQIUoqhaFhtW\n8aUyAQ2cOpD8gnJ1SuC9C4Bp/3euIM+LsrjH2aglhHMIEL5VVBuEG4jnEIRB\nrODvV24s5VU75yHdL8jWf1yGVycmpIVdIsinphfPj+oSrvQlhduFZGOnPQ8U\nTwJCXfmX3WgLz+zcH3x+jzugjgF2glTqvJbktPX9qbSfxtNSSbsltIUbVdJs\nXvaqOsfGkmpZ1V6rNgVM5uHN5N8KjM/D0bnRqVq65q4xUGs1TgjNCcW5Uxze\nEPmUluJ9T1O1FOok2j7TipSLz93YjAlnBAA0TSFIFI84voUxVglEsJXFoJfc\naQCC2k1HpQ1Dt4DxAM1uhxwgBFMI2se8b/nslKOYcVEqQVWpfvAwXQdzjquz\nZ7sM7FTIFt/YrjV+4KfagOQ1OIGl1UrZaJ6jmik9exPlS/zv7mLy0Qeg+FBx\nkfsc\r\n=BTUU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "4.1.0": {"name": "terser-webpack-plugin", "version": "4.1.0", "dependencies": {"terser": "^5.0.0", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.3.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.3.0", "husky": "^4.2.5", "memfs": "^3.2.0", "eslint": "^7.5.0", "del-cli": "^3.0.1", "webpack": "^4.44.1", "prettier": "^2.0.5", "cross-env": "^7.0.2", "uglify-js": "^3.10.0", "@babel/cli": "^7.10.5", "babel-jest": "^26.3.0", "@babel/core": "^7.11.1", "file-loader": "^6.0.0", "lint-staged": "^10.2.11", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.1", "@commitlint/cli": "^9.1.2", "standard-version": "^8.0.2", "@babel/preset-env": "^7.11.0", "copy-webpack-plugin": "^6.0.3", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^9.1.1", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "6e9d6ae4e1a900d88ddce8da6a47507ea61f44bc", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.1.0.tgz", "fileCount": 10, "integrity": "sha512-0<PERSON>WDPIP8BtEDZdChbufcXUigOYk6dOX/P/X0hWxqDDcVAQLb8Yy/0FAaemSfax3PAA67+DJR778oz8qVbmy4hA==", "signatures": [{"sig": "MEQCIGQ3+bqvU74GbkTObxu4oulm01n9yBpKUYxvRLyzFtueAiAFjPBHmx7j2ML5Z333xurVY0elJO1GHb8SxdRR9Gz6dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMVcqCRA9TVsSAnZWagAA2v8P/1Ni69pvoWv7eoe3CI4M\nOb9pxYmPvnL+l58FqW/iwzKWv6oCwBkv7qLNivAeVtzxKdx9h8gnfTvgFVkD\nBivWvMlm1BoJarIaHDR2z66qg0vOVqaclY17BguLPYMiGmHyGc76XsJAQ1ko\nUE9Bn1mgIXbqm6c9r/jOW6rBP6bUhk3IzoHArPsumggREm8U9JvDGTmQjH2p\neshSRb4Mvgj+LSckgcHeMfMR79au4HGMcMP4/118bTwhvF3fq6HxWNL7J4lt\nTbTJl4EzVshhHLy8Tqo18VwEsbalVWaJSQuvdKda+RVLT/h2MyrIGuZJiQU8\nnLB0WOrEvEiE51ZBcMs962q20Toh6uQx01/02lRe8h8uQDrihsAXIrKpK+uN\n3kTOJSAE77W94/frDTUoGflsrR7ljAXd9aB1rRHlPZONQ/NntVoySGHJMshI\ntFAbHOjGWPyiIvZZcejUpU7dGa5Y0byVLcgySHVLCyc0UxPcGrSrldJG0kXB\nJx2gi6JDGfa5jR35QB5T6CiR5PxplvRrVDQQ/mQ7/OGkiFSg2F5axNlEWIC4\nLhh1JlwJJ0fU1fgos8QkyIx0Zr6CxYe7KrKu9Fo0QDft5AfJmz5p4UpUu+nK\nZ2FBbPIBxgWNLvY1lTB4zq5RrYgLww2CxsWLdSiFmJRYLTI1yIRUHTeUz0UX\n1ge0\r\n=6kkt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "2.3.8": {"name": "terser-webpack-plugin", "version": "2.3.8", "dependencies": {"terser": "^4.6.12", "cacache": "^13.0.1", "p-limit": "^2.3.0", "source-map": "^0.6.1", "jest-worker": "^25.4.0", "schema-utils": "^2.6.6", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^25.4.0", "husky": "^4.2.5", "memfs": "^3.1.2", "eslint": "^6.8.0", "del-cli": "^3.0.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "cross-env": "^6.0.3", "uglify-js": "^3.9.1", "@babel/cli": "^7.8.4", "babel-jest": "^25.4.0", "@babel/core": "^7.9.0", "lint-staged": "^10.1.7", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.3.5", "standard-version": "^7.1.0", "@babel/preset-env": "^7.9.5", "eslint-plugin-import": "^2.20.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.2.0", "@commitlint/config-conventional": "^8.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "894764a19b0743f2f704e7c2a848c5283a696724", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-2.3.8.tgz", "fileCount": 12, "integrity": "sha512-/fKw3R+hWyHfYx7Bv6oPqmk4HGQcrWLtV3X6ggvPuwPNHSnzvVV51z6OaaCOus4YLjutYGOz3pEpbhe6Up2s1w==", "signatures": [{"sig": "MEUCIHXHHfz2yCtnIC1s+xHZPrNC6Amfr1bjGzJLXbsjNm9FAiEAvQxdVMQCrYM+NVGBHXuGSbAkgt9AFqsu118PhRGNM3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM88vCRA9TVsSAnZWagAAoLkP/A/vu4wtusCPKh7C6hqr\nuB+Re+eMttUAxugSivO/k3M/5t7GSGYp6TUV53vgHlAu2fhyhPC9WVdzSz7I\nN8vGjN42q/U0AqXL2Ymn4f8rFk8bcbluCl8QxAZe9E5aj38qkeEWc3NjXuGW\nDsUPJnyzkdjRoOWotXKX+OGmcvu0Z8UXLyeg/o2BbrPq6tAiTcSAr5BVjPT0\nI+sIo9QQ7UVuTuy1SGMsEf4XZviJbJ71+24sEOkdi2RffE7ZP6fSMAQKff7N\nA0M05sEOav8c+S/aT4thv9hiR50xzAJbduytLy9Pr4j7J+vtPNkMm6t/sf4/\nj+ydDQvYf1/v30wy+lwba6ONPtULiji/Yg1RjCdL6D6Hk3AAEYb3DrtJYmPX\ndzhU3kjO+L1IK4e4gyE/WyoF6FHr9ycvaHiR5AR1oZFpWOl7IwK+0je024TR\n3yiBcRyfkAiGa5AlFV4MuhHncRLxjxWvlba24hsTweJwHkJKgcrgrmp8Tihr\nHRGMVY8unBCyh3Bwvfvwfp0B/D0NzpzTS1HjTwu/O1d7ZSR99eBtizq4zdcM\np1TgFcZ9b4yvPCyFNcNkBAugGfXsw92ebaVhUA5K/m2B+/xDVyuZBFUPQEEE\noU2zBk8YjT541tt4qOrzYgtfkGx1tl6GXNzsrzRPMZYc5z/fBhmKZgK+MPlx\nY//Z\r\n=Q0Uc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8.9.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "1.4.5": {"name": "terser-webpack-plugin", "version": "1.4.5", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "a217aefaea330e734ffacb6120ec1fa312d6040b", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz", "fileCount": 10, "integrity": "sha512-04Rfe496lN8EYruwi6oPQkG0vo8C+HT49X687FZnpPF0qMAIHONI6HEXYPKDOE8e5HjXTyKfqRd/agHtH0kOtw==", "signatures": [{"sig": "MEYCIQCw53y3Jo0RsOO2gcKaylM3BinMkOyoXCK7E6LxWQ798AIhAJMT0wUQmujn6Fa55IJeYAfUBQl6ntyCZfnJT7PHcx8i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM9CNCRA9TVsSAnZWagAAz7EP/3AAJBnNAYZRhHw+Vhax\n+ycyplHyLtEJF3pvw2T5wIirFHpY/Z0J05cnUxbPXlOttVqZ/0koavlUiYGr\n8uWc5+4QBBszKbpv0cEMCp+7cxdkQq++5wOj+NsSh6p9gfgcad3558Hyjom4\nDYS6+KH8nW1SdtOuJaFz4nBMukYGCCnGP3xpMpa+5e+xdVDzto8GM0tceP0l\n0e/sq7H4AoRQmJmr87lXzy/5A4CzcD3ovQ/RvFGj5QjbG8EbM8sv9tn5ATLi\n9lHlMso2Dt/psm85DRqBZENTlepgaPaPqi11smPwt/pCJQvhqWyfodLtFU0X\n4dIf1QVzKJyC36GLkdJMkLQqo/hyD7VINryJCS027Clpy991m67tgFxPMAjt\nzCJXjxg5VWz7WEnw94JjsfYogHds77NBgMFMlO0XiviLnrWOH4LJ+VjRDqav\nDETr5I7fXx58tJFt2YYR52sVaqMAd8faWAWY2tNTWgsEwdHGyYeDgSFKJQdD\ncM0tF7bylcKsmKOcgT3XNSVFMU+8Hclyd9lONJqlenEcHk6cPoOV13fqJE84\n5zF0oWWV7gpoZ2vTCrOng9CT5jBigpQP+/JNgx5r1kYFn0YpjulS1EvSw6dw\nNI+97Mcr9NOkSlX6QsIedlvsXt7yDC6Fd9NobkGUO/IAoykm1bphWtC53cWl\nlXh7\r\n=9eoh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6.9.0"}}, "4.2.0": {"name": "terser-webpack-plugin", "version": "4.2.0", "dependencies": {"terser": "^5.3.0", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.3.0", "schema-utils": "^2.7.1", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.4.2", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.8.1", "del-cli": "^3.0.1", "webpack": "^4.44.1", "prettier": "^2.1.1", "cross-env": "^7.0.2", "uglify-js": "^3.10.4", "@babel/cli": "^7.11.6", "babel-jest": "^26.3.0", "@babel/core": "^7.11.6", "file-loader": "^6.1.0", "lint-staged": "^10.3.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.2", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.5", "copy-webpack-plugin": "^6.1.0", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "6240a71101a55c6823d84e11c8fff380b9dfd26f", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.2.0.tgz", "fileCount": 10, "integrity": "sha512-Wi0YFbWKG8gBXhbJmrMusRcoXl/C9U5BzIPC2Tn3Si0hejGhhIh0gPf9rEfOCxwigzRPLC8PXv42qDiRTocMXg==", "signatures": [{"sig": "MEQCIEHTUr6mgQwzVIx+oVcCQ1VsTQlPpV1v9NnZSa2NmFAsAiB3XdGz2mFIfA5oeBkcvmy/DQKgMQYyUCf4GJNCoObsRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfW8pFCRA9TVsSAnZWagAA910P/1fny7BYTU/EwsjpZLhQ\nhQ9NHycgBIBZQkqKaHNOVDTO+moJlVGpp1La0VHVGJaYPBP6J/ac8e2L3A5j\n0D/G8+lLsS7pKqPa+uvrrdb1aRqtvAsiZiShMMD9YKAAJi7dkjWfOfSKf9AZ\n6+cLirzo/10IPYwO1DGLZYyfQKh9DVNcAjHDlKLOI08n8uNn3jMYm4KpLOc5\nBudBWtS7tgGyznbBWEalGY5sgkfKFMFdz2J33oLboh63Vr4BruShVkuKdyVC\nI7DZkWLukQL9BuEmwN72Np2VCBjSx7jNg8TRo4F0dEM5B4hfbe3gk8omOlEH\n4SWs9tS1qz2MQOuFqUTMFgb6C0AwoS1NAeYmNg9V91N68MXX8wrPByubrPjz\n0AWFvS7y2uRwUY/Oh8RZSx/1MOHWM5xF7OvwwxzEiq3k+3XhVv31cc+Gl8Um\n6cjw8qb4p/9ev4u4F22MwM1ZyX9GgweU5mY8PJ/8cASEsA3flg1OSOkk6+sV\n5XDL/WNV3tiHDFzLyr1JXKMcuarWqE+uLV8tWDLM6H8/oh83ZVKSt2jY5N/5\nKoljdJYIrHI/ciSmQ+v49uUN8cjCx3batBEIuUs3KzXJpvquoizpbBCtALM5\nIhibfPcfo7gPIUMnDuCcxPWZhwQqk3KA4dEI6PDCAAZ66XC1eynDtxqnR9nV\nwz+Z\r\n=d8oO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "4.2.1": {"name": "terser-webpack-plugin", "version": "4.2.1", "dependencies": {"terser": "^5.3.1", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.3.0", "schema-utils": "^2.7.1", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.4.2", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.9.0", "del-cli": "^3.0.1", "webpack": "^4.44.1", "prettier": "^2.1.1", "cross-env": "^7.0.2", "uglify-js": "^3.10.4", "@babel/cli": "^7.11.6", "babel-jest": "^26.3.0", "@babel/core": "^7.11.6", "file-loader": "^6.1.0", "lint-staged": "^10.3.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.2", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.5", "copy-webpack-plugin": "^6.1.0", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "44b88ef4d7443129fb136a68b5ec3e80d63ec471", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.2.1.tgz", "fileCount": 10, "integrity": "sha512-D0IZQNl1ZN/JivFNDFzOeU2Bk2LdQQESHJhKTHsodpUmISkaeRwVFk7gzHzX4OuQwanDGelOxIEsBt1SZ+s6nA==", "signatures": [{"sig": "MEQCIDkeUwqwws5qR9zj7/Jixlfuhsv97SAO1EJ8SYqnZgJ+AiBFJoqRqR7w8eidFbuj/2NAWkMF5BEdEyHxHzabcifWhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYNiHCRA9TVsSAnZWagAAwmIP/RnIaJlKzSceITzoOTY1\nvTxpzFdU9Y+iweAzRZxx2utvvPKKIEztZN/YDphXP/rjjSvF7G2mz4WJCpoF\n0kEDPDqpk9M/1npyuiPj1Uhx2+UbadLcoE9O5Zq4Vc4BHSNLe64GQ+ymge9T\nc/FftuiZMYy7E3IZY09kbv0ZCml7eCWd7M+1tpLUSUyhbErJL93egqT6jUPX\n/xl9zuVXE6CRrVs+VI6d9VnKclRtL9qRsIPf2rr5JZzYYs10hrj02vB0RD9k\nfhhk3cElPCVYmZDdSVuNmecDEuMa+PEQswAfuU5mBkprSoWh3KJRoW2vFsJq\nXfPndyg0tv032LN0hDjj61NW8etnoxgxRdOsOM67tZpvevWoRQ2mCElcLGTT\n0lt2pvYgVbKIW5JLtiWS9H0H/54OZpM5iG5FahIT5kGuGTcVF/DOEtjUULew\nPFpvCxfywia2GnM2rWnk1KRnjQeaIemYlnfS+mkgpuR/Xu6b56i6t+dbxH2d\n8fwNg09f/cspZEzw+aFNT/1+TNRvtGdXhEE3bjbLb9ZbUai7IHPQPDiURl1Z\n9vG7yLb/29sCPpQ+HA+mRsHXQBSQozQ2MGgrLB/lhtH3OSKkZtIY05Rp/USG\nWX0Gr/d+LbuzjZec7DwSt7ac+XETFsY2o8FcQzf21lWYkDEO6BhrNsh+2dWw\ncnfj\r\n=kAaJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "4.2.2": {"name": "terser-webpack-plugin", "version": "4.2.2", "dependencies": {"terser": "^5.3.2", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.3.0", "schema-utils": "^2.7.1", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^5.1.0", "jest": "^26.4.2", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.9.0", "del-cli": "^3.0.1", "webpack": "^4.44.2", "prettier": "^2.1.2", "cross-env": "^7.0.2", "uglify-js": "^3.10.4", "@babel/cli": "^7.11.6", "babel-jest": "^26.3.0", "@babel/core": "^7.11.6", "file-loader": "^6.1.0", "lint-staged": "^10.4.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.2", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.5", "copy-webpack-plugin": "^6.1.1", "eslint-plugin-import": "^2.21.2", "eslint-config-prettier": "^6.11.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "d86200c700053bba637913fe4310ba1bdeb5568e", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.2.2.tgz", "fileCount": 10, "integrity": "sha512-3qAQpykRTD5DReLu5/cwpsg7EZFzP3Q0Hp2XUWJUw2mpq2jfgOKTZr8IZKKnNieRVVo1UauROTdhbQJZveGKtQ==", "signatures": [{"sig": "MEYCIQCC29tljuKQOMm3xV1e7IPTGBxD0vOJc2GzoATiUuVBdAIhAMEyuipp7qg0HDhf3L/l9gL+qfeB7nxOuUO1dIYFcOuv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZjSsCRA9TVsSAnZWagAAin8P/24x0s6goSvZN+4/pkyZ\n5wIoaa6yDRpcf7Ait/KWHay+KD56JBNKDkkXAIFH9zdaWPj0oZI807mQ5QAz\n0Ge6lWxW1sJCW1eYnz277Xu5XTdr5MekATNjWruaAGY6A5dK+VA0y+TqPrK2\nG3jtoA5tJBlSXh7T3sFCR08W/eGs3wNA5N+PEKJqrLhDkwPmoX9xxQ2RmDi1\n+8061VZCwbl+hwz1W76Irapl1cHzqo2XbSmDj89VjtlNsqo6uvJSN1msFhHq\nsuTnbhmCa7h0wypWqR1Ye8ZtGWcIC/RSgHpJQcmn2euz/EsP2PRfaWZ/Vl7B\n92IDJFPoED+b1PRwEKGYNWacjUuMXM55CEuacaacxOCHFsY1nc97qeAV26cD\nFbj1j1OBhEKD2sOLQ1fU9sFQ54uCAa+W01Y+nYYonBINAfyeUWgVVelSwABq\nb/Mk2MUfmC8Y7VuRe8SBqKewG4LVp9WBz7DkrvUSFEqdhAELiV4keC5LR19F\nZdLjPDosM7cZ5/JLcGEqaQEnUig3MTm53gVRVHRmvQRq8utJhmTEoHff/dBz\nEHHksD6U+ie401ryhBikXVqh35oLfTdAjPCc8MQ7zyiqxEL64tNrDTDEwPdj\ncDNTcTRBBD+QIveT+KYl74pWR63dPYosIXSqnwtM8q4NUaIpGtoTQmINsQin\nPemk\r\n=U+qR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "4.2.3": {"name": "terser-webpack-plugin", "version": "4.2.3", "dependencies": {"terser": "^5.3.4", "cacache": "^15.0.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.5.0", "schema-utils": "^3.0.0", "find-cache-dir": "^3.3.1", "webpack-sources": "^1.4.3", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.5.2", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.10.0", "del-cli": "^3.0.1", "webpack": "^4.44.2", "prettier": "^2.1.2", "cross-env": "^7.0.2", "uglify-js": "^3.11.1", "@babel/cli": "^7.11.6", "babel-jest": "^26.5.2", "@babel/core": "^7.11.6", "file-loader": "^6.1.0", "lint-staged": "^10.4.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.3", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.5", "copy-webpack-plugin": "^6.2.0", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^6.12.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "dist": {"shasum": "28daef4a83bd17c1db0297070adc07fc8cfc6a9a", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-4.2.3.tgz", "fileCount": 10, "integrity": "sha512-jTgXh40RnvOrLQNgIkwEKnQ8rmHjHK4u+6UBEi+W+FPmvb+uo+chJXntKe7/3lW5mNysgSWD60KyesnhW8D6MQ==", "signatures": [{"sig": "MEQCIE+gySXkVvNGYQbLywCaQ5Yuzl++25kB0cIF8TH+NB2YAiA8qxxb+j9+AY0eKr03z7fiS9xntpF6lJ2FtOtqnb2xHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffdhoCRA9TVsSAnZWagAAKPYP/iSC3sZ7NIXZR2Xvu4M4\nuVgFzyfzjxMrJrluXy1SgAmP3EQzPWhRRO9dg3jwSLmEKTkiKsUTOlkPK/LV\nLxkHwXEzp9rAOkXu3MNvOt6gJP+vNFnS3KdaKhuC2ZM6/lM62/pHMCg61HSA\nfCA1FcEKpMygfq2bZR33LfGep67YsuiK42DMmuKV244FY01hiWGS1CmbDT7y\nOT26MJVKEcbEwVccIFoOOV5hfcxqUWMSdEGET0eqMtf5VYMYx59fljN6dttx\n7usULev+l/9RvXVcz/Iq0WttHh0sJJt/6qDRalnGcPYspCRsikwt0F1nMAhp\nzsiYpNjE9KLJh/91LCywEzgNWRtzGm1sL4I+hQSbEziNXbBor2EtYlR/buzm\nsOyybbopUYSJfpl1EJlJ8cacntJDAucg1rE6bPVCzdODikSQzxQOtn1VyH6q\nfVYjjge155Fw9m42v00vhxsZHmumqBBEWM4nHPpMFtrc4peNWfOaaRtiFDz1\nJhpK+mnYwlMWq2wjmudyOJeCTAs3lO/hHSmTiW3Z0w5oE2Wgmnyl6IefYzQj\nT224macQ5uHBqEbQc3fSV19avzeWafxacSDnEgS3BoYCzJGaHVaYZOUb54K+\naO8YSnK5hq4ClQk8q5ApwpifmkHxZA1Alk5AWJ94XKZPmbf6guyZPMOp/g5r\ns0sB\r\n=OXE4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.0": {"name": "terser-webpack-plugin", "version": "5.0.0", "dependencies": {"terser": "^5.3.5", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.5.0", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.5.3", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.11.0", "del-cli": "^3.0.1", "webpack": "^5.1.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "uglify-js": "^3.11.2", "@babel/cli": "^7.11.6", "babel-jest": "^26.5.2", "@babel/core": "^7.11.6", "file-loader": "^6.1.1", "lint-staged": "^10.4.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.4", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.11.5", "copy-webpack-plugin": "^6.2.1", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^6.12.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "88f58d27d1c8244965c59540d3ccda1598fc958c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-rf7l5a9xamIVX3enQeTl0MY2MNeZClo5yPX/tVPy22oY0nzu0b45h7JqyFi/bygqKWtzXMnml0u12mArhQPsBQ==", "signatures": [{"sig": "MEYCIQD34zm7XS9zvx+GJ6kIY/HfI7z0BclDViKcTpII4+90UQIhAMI2hIL+o1KI6GCmiW5WGVLMAfHddXMuAtRtt9xT7KU3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1BiCRA9TVsSAnZWagAA+fcP/RT5DFtKhuIgQ5FSf2Yd\nZrBYcSdEDAhV+FcBz67Cf9npDAO7mhyrlcBntzeHki7p3mOvd1kqlY6k8Rmp\ny+TzmMm86aU8uSMtxEQOzjMRNxYRJ/4b3Pm4BKxBp36b/YkiXjX2Zl1dlfPb\niRwvmZ03PgLHGgCFj99sClYqLEQKfez+5uyrRoYKLzvlLXuQ4x/sa8znU8Wz\nqr0TvntPd+3gHXrtksaYiKxQTTGnOQSN+QG40w+cXxVpYHpeZX3xoeiyodl0\ngaXg4tq421SiEaJyuvCCHVoWB0426jQD1Ye9gE12k/ZQvpbj8+ZztD1xAnm5\nnwHYSKlZ9bzEpuBfMJTS9ygGV/lw/E3KoBhgqj1cIJLBNmxlbrsP/tcePT0e\nywgAXxcTJJNE8gOqko1fMfKd2wh+cB3slJleWGAxBAPzWi8Gm2mZbvd4WvPL\nKlW/OGQDZ4iHtFR8ARjlGf9fIzZHd7xGzi6McxI1xPAEBi+Q8Y93xdHKz0tK\nKaajoV0WFBjo5pjSLKUO7Joe204ApnVr99smcjNArvxE+Rco4bA8ALsMPAfb\nZwGXa6ZMevun4riEUV2phnEW94c33w7ID9MHtIsH3Q85CPhpmyhudslYGF78\nsK6qX8fN70QL7dqFJgOG+XuRJN1VJBMI6ufhAy8Yt/BHdzDTeL9y36ptifkm\nXDQs\r\n=2Yxw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.1": {"name": "terser-webpack-plugin", "version": "5.0.1", "dependencies": {"terser": "^5.3.8", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.6.1", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.6.1", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.11.0", "del-cli": "^3.0.1", "webpack": "^5.2.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "uglify-js": "^3.11.3", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "@babel/core": "^7.12.3", "file-loader": "^6.1.1", "lint-staged": "^10.4.2", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.5", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.12.1", "copy-webpack-plugin": "^6.2.1", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^6.14.0", "@webpack-contrib/defaults": "^6.3.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "b1f02e43d93ca61a0bdd9e870f4e3489e12a6c9b", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-EwUe+XDTFf/2cAlmAlZZ7vRpNKMZUAypX2kIRm0Fmjlz4l7SqbI/VabmgiesNZW2nq/LR0N7ku/wlTQ6ygen0w==", "signatures": [{"sig": "MEQCIFVKv9mh8thVbwxvCM6GEEI/auLzsA0OQupTGRJGJWO/AiAqxIZypXqO2dqigr8sItHPHLQ4yOt6+ZBBjYji7wqH5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkyt6CRA9TVsSAnZWagAAD+IP/2eahHUx1kgYT+pAyLmr\nvYlIJpVSg0XijQx9CzRcrlrsUkdvZo2s6v7Fuqu7pNyoqfC4VwxTmYDK8ICJ\nKaRnHwr4NAoCyGMdvb+EGc02CaH1gxvrJ9NU0NQ76mXMnWUlH2qJLVk/M36G\ngIRffyxNGZOE/R9mVZC7ovRCs3uaNCXBZcCLT0/QCRNGxr5hWVeAhfrVRMmH\noy3nljPIChMnLJsQ4+Rm20n7DK5k5GMnQdyg/3hyOybQqCfXqPKfZiOcXfWy\ncnVfxcOSuBZY9vq9ZfR3h34u0s7x3EXvQonJkX+3m+WHdq6KSs58nFlh18e8\nqx6cHwEFEnkFyvKRMOwTv+lJzun1FqesL7rtG9pMoFFGMKf1iK40s4MDwZNZ\nkOYPdtE8+eoVl5qyEj2O+jk2pHdamBO+j1pu0UA7zBDYuVkBNMyd6ZoBC4oD\nL2QRCq95UIZ4kumeg9jRc2R559z3JLypMilfWmIT5HzFN/KVFc1+lPJ5Gh27\nuiCYFR7LxYcWHpVR4M9Qjenmo9T+VHI79vnf6ScqvXV7g6YwfwXCAebZnUfr\nm/X80wq0jjhc6NPqQu+aGgwN6FXr+/Up97fmnnV1bmAcatPiwqBGYpxmFOz/\nxIN8CGpbkzawQoiX1UWw/RY/EnYrMSKBnfFONM+SyWBf5wsI0V3vbG/LlCyX\nAglD\r\n=060L\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.2": {"name": "terser-webpack-plugin", "version": "5.0.2", "dependencies": {"terser": "^5.3.8", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.6.1", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.6.1", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.12.1", "del-cli": "^3.0.1", "webpack": "^5.3.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "uglify-js": "^3.11.4", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.0.5", "@babel/core": "^7.12.3", "file-loader": "^6.1.1", "lint-staged": "^10.5.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.5", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.12.1", "copy-webpack-plugin": "^6.2.1", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^6.14.0", "@webpack-contrib/defaults": "^6.3.0", "@types/serialize-javascript": "^4.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "dc771be6e5f161eda2b553d996db55e44859f21c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.0.2.tgz", "fileCount": 8, "integrity": "sha512-adg+JLykDi9HkouHqmVg+qbSGyKSJXioNQl7v79xqyaFbv6a/pCd5nVj4HcTDaFNxt6CzDuUrMEnAEbKA/w/QQ==", "signatures": [{"sig": "MEYCIQDGx+evmyEzNujY7BJJaR5N726dNw7njKdm/ZQ09hGgkwIhAKeJJWh1v90LfeR03+dQ5nta6VaNgFlmW0rpnvgKH3lM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmCXqCRA9TVsSAnZWagAAkqIP/36NMcAunnS7bzDYY+wC\nZeGYs7oXPQKWf+8wO9JssAKLUO2pVX/2xp1oi8eExrSE9+0od/uS28ZeiJ2J\n+R729DFs6Dw2HP+Xvz+cP3UHfNljjSyJqdmVejMfwtsqAopjqOGJ6rrhaOqn\ndAKn34T6swvamR9HmeaEhvK8DTGCEqE8aXunggB4dsP1+fSbExQHtRqqRVgp\nP4pz0+dARed+SkPsgULkhXWIDXW+CguNSVe0Tn/p1NkuStQJzCB2H4wBGl+k\nSD1zb1SDgp0S4zb8S+RziichHBs2cuUqHodh36DnyCjLqAFh2Q6VOcdhYU/y\nwVI51fcu0I25HvkT44Vprm8qM6njIyfHjgrijS0RJ7UK/CJ6pHcbD5CREkQx\nMUGQ2e+PZntKY8HDnYbvVWu1zHEZ2bd6XbxPlgDnzOPBKuQwtFZv8GoUjhkY\n68A1Oqts3NxGmJFtNN43ELr0+u6RGRK2eFNnHvg6yn0wJDVKJFUoLf6cN2+U\nchmjJi2xuj2gbvjlpfaUhO5GoU/RFoqHyBrrc5epCdd58CA+eOavw3P2Bl/n\nS0txGzdC7Q1mWcCGNQC+CnAL23Mfy1ESBjTRgO6rwi9ifmeHb4fZhPJOxtdg\nZts88f2S4gUXKcO5lxE1GW2d51TwWAFMBNJPZoBoqDaKb3ty4UdHhKTFy1ne\nMiHs\r\n=2LT3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.0.3": {"name": "terser-webpack-plugin", "version": "5.0.3", "dependencies": {"terser": "^5.3.8", "p-limit": "^3.0.2", "source-map": "^0.6.1", "jest-worker": "^26.6.1", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.6.1", "husky": "^4.3.0", "memfs": "^3.2.0", "eslint": "^7.12.1", "del-cli": "^3.0.1", "webpack": "^5.3.0", "prettier": "^2.1.2", "cross-env": "^7.0.2", "uglify-js": "^3.11.4", "@babel/cli": "^7.12.1", "babel-jest": "^26.6.1", "typescript": "^4.0.5", "@babel/core": "^7.12.3", "file-loader": "^6.1.1", "lint-staged": "^10.5.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.5", "@commitlint/cli": "^11.0.0", "standard-version": "^9.0.0", "@babel/preset-env": "^7.12.1", "copy-webpack-plugin": "^6.2.1", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^6.14.0", "@webpack-contrib/defaults": "^6.3.0", "@types/serialize-javascript": "^4.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "ec60542db2421f45735c719d2e17dabfbb2e3e42", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.0.3.tgz", "fileCount": 8, "integrity": "sha512-zFdGk8Lh9ZJGPxxPE6jwysOlATWB8GMW8HcfGULWA/nPal+3VdATflQvSBSLQJRCmYZnfFJl6vkRTiwJGNgPiQ==", "signatures": [{"sig": "MEUCIALfxG6if+uG02J6mJtcAbZ1Z4YlMUcsZd+NPBx1D47hAiEA7+GpwD2if0Ll/CA0cp4peDJD6CCi+/dCjyV6iysaD50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmU4FCRA9TVsSAnZWagAAG1kP/idfEEWtWB5TpOkurh58\nAaCNlZQb55kIxvkK24MrRef7CkxvkY+QPFNZn2b9ATaiYaf6D6Y0FqoD5bxe\nOdVgoTBnPo1wXw3NaDmUvaFOZ8wBkav9eW3dRlMvWnKNGgcoxCzLhlyGe3K6\nUObsXJGC/1xQq83jvTZzg2Ul74sNaClQdCM/Q6lVCXpEyhVLdDR3jX54VqdS\ngC6QsxIrHttIA9/O8ncdORsimzzjhI4r83gPAZPEEQYgCOhm9Yj1kbGetzJd\nNlxwsFNb+gjjnD4SRgD+ejet44pPl8RBjb6ZgeC+XQ03sM8fgXtC8Fm/XUBg\ngRSfz9Su/b6YB3PE6Hg7dYnNnvtiOnUQ5UGkma7YynJtTTA44v/5t0huIIsy\nWU3Bewt2u2PmcOsICfLoRMWY6UGSJH6YfqKQpd8iWBE0SmgLTv6mDuDw7ZyX\nFcJbNH87ASQe7eIMOx2xLwoQRcGd2jQePajC5+6pqC8+3uF0K/aRCMduRYBf\nYNMa+g4efi5kxmK9dZC8hEM8Ju5wcpkux4nOIIWcJHlTvs7+2RGVIUUo7hBf\npUfYzzoNmsUlk4M60FcEwopcIxyjzSvSm2o2HX6CngLNY6fQyoxKaOoyh+8J\nGV3n5QpmFW0MgGK40xLDm+Y+2Uy6SkeLvkLKrfKZhmzuf+BrfUcS7pyRn2qK\nBm1k\r\n=dI+7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.0": {"name": "terser-webpack-plugin", "version": "5.1.0", "dependencies": {"terser": "^5.5.1", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^26.6.2", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.6.3", "husky": "^4.3.7", "memfs": "^3.2.0", "eslint": "^7.17.0", "del-cli": "^3.0.1", "webpack": "^5.12.1", "prettier": "^2.2.1", "cross-env": "^7.0.3", "uglify-js": "^3.12.4", "@babel/cli": "^7.12.10", "babel-jest": "^26.6.3", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "file-loader": "^6.2.0", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.7", "@commitlint/cli": "^11.0.0", "standard-version": "^9.1.0", "@babel/preset-env": "^7.12.11", "copy-webpack-plugin": "^7.0.0", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^7.1.0", "@webpack-contrib/defaults": "^6.3.0", "@types/serialize-javascript": "^5.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "e52e838ae7a0c8b6783afd80313db52f5e07154b", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.1.0.tgz", "fileCount": 8, "integrity": "sha512-7Hw5b45IslUGsR3rh1WhKlt2EHHIemwrus2Y++8f+36SGBVXruvwuDU1/bgkM44i/x6F24yJk1d+3r+JGtHaOg==", "signatures": [{"sig": "MEUCIQD5PR1Gjof4ru8p8miKQc6KN5MB3Y3/5KdFo/ewxJth1gIgVudqsuOKN64KAxSUqXe3XX7GQUvet5l2mBbW95HeHSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+KihCRA9TVsSAnZWagAAn0wQAIggBH/+zae8PMH2w8l9\nZCke4Uc2tejgDnuraS3gJzkU5xipXOyytLOVrze5V5AxK4in2KsjeOow+2sP\n596qsxrrFYASxpMS3oR5XHUrX1nidko4lZwwuCsS+2PpGIQhfYfRg+hm1NYb\nvWRs44JsY4vXrrwQweV+JuSvKTk8WVbknW2NJ+OZdttOiwFY5lSUo3D3oMFq\n3jijnCYL3OMjzE3NKKSHK1nH0dEH1Jhv/4z23ujNCWLuXOqVe+LtRMiblyig\nB0l9RTRCA5Wc9jmZ2lFAw3ZqgUWh7ZvKR9FaGCrnaKPz489Y7pw2mkamV2eD\naXP8Gr5+x0AvgbS+6pfHPf3LeIqMd04axh9iuRBY9Kld5zLxO/+z5QTbR+9W\noK+TxqCLWWTs8vKS3rFhkcma6khE/l8kzhpTiO+bDMxJoVo+kGlZdtMgfiXQ\nA0CW1rBN3jKBKAjcITgp/oXmV+xqLxTaBApFFXU/fs2LP1XVGaf18uMTQh0V\nBrTEHD16GFYw98MLRJNMjBXKALheZL6/6MfMZp8TXwM8nYOog/ktETDJLpdw\nknHkWk7oz1P/atfjGe7DMKj9hEPFwDIp9E6TBXjOJ2K3311lGI1nJOVeLju3\niaI2m5W942pSQjLIQMbx9WLRVAn4dapFn6iYkUeeCKPnml97ClyOuSVpMxNK\nz30E\r\n=hpqH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.1": {"name": "terser-webpack-plugin", "version": "5.1.1", "dependencies": {"terser": "^5.5.1", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^26.6.2", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.6.3", "husky": "^4.3.7", "memfs": "^3.2.0", "eslint": "^7.17.0", "del-cli": "^3.0.1", "webpack": "^5.12.1", "prettier": "^2.2.1", "cross-env": "^7.0.3", "uglify-js": "^3.12.4", "@babel/cli": "^7.12.10", "babel-jest": "^26.6.3", "typescript": "^4.1.3", "@babel/core": "^7.12.10", "file-loader": "^6.2.0", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.7", "@commitlint/cli": "^11.0.0", "standard-version": "^9.1.0", "@babel/preset-env": "^7.12.11", "copy-webpack-plugin": "^7.0.0", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^7.1.0", "@webpack-contrib/defaults": "^6.3.0", "@types/serialize-javascript": "^5.0.0", "@commitlint/config-conventional": "^11.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "7effadee06f7ecfa093dbbd3e9ab23f5f3ed8673", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.1.1.tgz", "fileCount": 8, "integrity": "sha512-5XNNXZiR8YO6X6KhSGXfY0QrGrCRlSwAEjIIrlRQR4W8nP69TaJUlh3bkuac6zzgspiGPfKEHcY295MMVExl5Q==", "signatures": [{"sig": "MEUCIFCLYZyauS3m731ET7quqltjz3w5SI8TLxcNRns+HJcbAiEAo1Lmegofu5WiMbNID+EBkiGAhhDBc3KvAjuadqQs204=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+aQlCRA9TVsSAnZWagAAvaIP/0VeAJzqFIDCFfmwe7qR\nMSkOuDIV5KxjJfq+A15zorBFc6Bjd0ZEQ0HhLri/b7W1KJfkdEx3PPKGbf5g\nY8jgeVooYKccxL6yp7m/VS96QYlamWJCN06Has0FACGRh4xgfT60YWC8u+o6\nTTtZHafbHdted9fkd+VDHqSSjviEsCoHuTRbP0cgadOwxL+r1wk5U56tpTrt\n4vQtKy4UTo5lacx6irWi9ig8Cl1gQQDKSyv0En4R0hi0CuV/Ik8v0Uh0FD+k\nAePX3J9+zRr37NRgD8o9kjQ/jumYZpoiR0I53LXD14UOw7+1RXm8SksLBK72\nPT2j955sB0FWBA9zVWackEc5x0sdfttHPu7W/s84/FDtRvoPr85V/zBmaOeS\nZIaMKtFUufTYHWOnrgTvAifBvEWvWPeGjLrl4QhqWySHZWivd+jKV+1EhaNG\nJpN1PtFmTuQ2jqvwbw9O+MXc6eXtxXrjnZwWBxN1CuXIwPALNhlJrHCihM8q\nHUAKhgUC2Mbtf8eiVBCxnzdzx6xzeEf03mq0pORUKNQu1xYYAQ3ecJmsuC6d\n2A+q2oW/3PmiOzy73HkQGI7DUvUObuoASDN3QXJD3cNhOI0BLj3MfsUoLT2z\nmHJlWeUWVR61KISuZWmRSiNOTFXyWmaVbGe6w/3iRZcYHl67RBVc0BJpSFQW\n4KSM\r\n=cRvv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.2": {"name": "terser-webpack-plugin", "version": "5.1.2", "dependencies": {"terser": "^5.7.0", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^26.6.2", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^26.6.3", "husky": "^6.0.0", "memfs": "^3.2.2", "eslint": "^7.26.0", "del-cli": "^3.0.1", "webpack": "^5.37.0", "prettier": "^2.3.0", "cross-env": "^7.0.3", "uglify-js": "^3.13.6", "@babel/cli": "^7.13.16", "babel-jest": "^26.6.3", "typescript": "^4.2.4", "@babel/core": "^7.14.0", "file-loader": "^6.2.0", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^12.1.3", "standard-version": "^9.3.0", "@babel/preset-env": "^7.14.1", "copy-webpack-plugin": "^8.1.1", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.0", "@commitlint/config-conventional": "^12.1.3", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "51d295eb7cc56785a67a372575fdc46e42d5c20c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.1.2.tgz", "fileCount": 8, "integrity": "sha512-6QhDaAiVHIQr5Ab3XUWZyDmrIPCHMiqJVljMF91YKyqwKkL5QHnYMkrMBy96v9Z7ev1hGhSEw1HQZc2p/s5Z8Q==", "signatures": [{"sig": "MEYCIQCgPk6eKjCvjX0JrZSkCp8nou8/52C/QAUEnPLANX+/fgIhAN+eoSQytxWeUF9uv59iuYaD9tG2cP2PZKgnXe5cNnPI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgm+l8CRA9TVsSAnZWagAANOoQAJoF+9fQZS/UlggYkcpS\n0Ffc/4E/ep9pDH+L8GzYr/JJeIHCF46fGc2I2deIOvLO6ujIzfvp5aPi6ulc\nIVDcMjKzHMj8OBG4LO87owgT7/Ec8h0/o6kOKVlpnkRxLwrEwzvihhCgR6mG\nRBrJC8DdCXHgtdnAychYm2ImrbC3Df0Za+qClKP5N5aWwg0pIDq3q4sKhRhZ\nvxttvJLiVp6NXp7dkg+jODVEZl1VtUz/9vOIC0DpciJUs0JBGTlKKhSicH1e\n/fXnXP9WAhzL3ESSySQEgsZ+rYX0MuQbEioRxtdzpc7hpT8VQEcfwyIPPRnf\n+NHSU+405OLyUnpUfhyQDDIvdrU+Pjd5LdnXOdb+1i6F8MOK7ruqE34juK3/\nVKRrDzMQ5xDUrD2jL/sa3GnKj7Woh1uB7fi4J3lXTCpmeBvRT7MkN8D50OVc\nwPHg6e+4hfJd2yXi9LRy4/NMuPcE6S5/bbmjlKBKF8icO2ubNSUCGgMSuf0/\nC0nljWwZ4ahx5+MXwAYSl++ahajFboef03hUwb/dwzGdOmoK9tpPVHTRelN3\ntk4djJcM6Rp7iZy9yf4pmuYA8X8ZnhyWIcgwg6SC9MwBLr9C70XbUMeq9GAc\niY5O8sdgihZqLNPHKx47cmxmr5u4Vq3qZ8+PIXYJyJPKwWT/n5Hb9+KeHRzy\nyCCh\r\n=KeT1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.3": {"name": "terser-webpack-plugin", "version": "5.1.3", "dependencies": {"terser": "^5.7.0", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.2", "schema-utils": "^3.0.0", "serialize-javascript": "^5.0.1"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.3", "husky": "^6.0.0", "memfs": "^3.2.2", "eslint": "^7.26.0", "del-cli": "^3.0.1", "webpack": "^5.37.0", "prettier": "^2.3.0", "cross-env": "^7.0.3", "uglify-js": "^3.13.6", "@babel/cli": "^7.13.16", "babel-jest": "^27.0.2", "typescript": "^4.2.4", "@babel/core": "^7.14.0", "file-loader": "^6.2.0", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^12.1.3", "standard-version": "^9.3.0", "@babel/preset-env": "^7.14.1", "copy-webpack-plugin": "^8.1.1", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.0", "@commitlint/config-conventional": "^12.1.3", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "30033e955ca28b55664f1e4b30a1347e61aa23af", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.1.3.tgz", "fileCount": 10, "integrity": "sha512-cxGbMqr6+A2hrIB5ehFIF+F/iST5ZOxvOmy9zih9ySbP1C2oEWQSOUS+2SNBTjzx5xLKO4xnod9eywdfq1Nb9A==", "signatures": [{"sig": "MEUCIA5fFVOsdlAC/TMO7j4Sl+EmEdZ9b796wZCPD/vSAoBNAiEApbV4DrfxC/ZVhycQ8czIi9BAlW2iP0UwVUTCotCIPYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtQFZCRA9TVsSAnZWagAAN/wP/RblBJnfhhjhf3rxF3E7\nJ9+nLpS3Ea2v5seccAYIgCClsXNIO38OYPjvbm2Jntd4a7t1Inenji4JQCxt\nO/592Vs3jqspR/C+ei0GJyFvBFbaFvQhl1+5vdMOi+ZyzKUBvU8Q3kaIszAQ\nbgkgy/U1NrdmGZCBpycowxHVNF+dqn9w/nNj0gJbacfw9QOfYsSiGFcVi0xs\nZsC073pW2/6axLVbjQP64M3sYBNn6HfcJ2OYoley1+O4PJehONIaIOz/Mesk\nL9zVfprqvYoXRbGx6w2vIeAwUYSH8Yc5veG9Rn9B7NKh9jy8yCDTAYgO2RNt\ns4H8gt0b0ypqE1HNRQXjHyxWGqxkI/N4M+MCDuvqRfcctY8iVF/rys7urfcF\nF7DfMhR8bFATvA32S4kpYr8OM+hG/aSyLfAhfDEBJqhRa4OzC3KCLgxAgMHd\nKVa3zRdFa45/KTr9jUu4xNL5aY0FOlOvujnUlp8vME+kuLM92mv03SRjakAv\nVRz4OWllUxCGvJVADH3k1Hpt/zHbnekN8z0XQLohSyfktKR3Ml7I89TujlO5\nyRzzsTBOfaPpdrCI5XLDJWB3SXGVPP06f4ZCGMLszuytJEyRUtocgMQgZRZw\nEmwCUAh159mV3Oher3xGFR7my0bP1yqhhX0thnNia/MZ4L5CT18L5TKrxzZW\nBMAE\r\n=6pNB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.1.4": {"name": "terser-webpack-plugin", "version": "5.1.4", "dependencies": {"terser": "^5.7.0", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.2", "schema-utils": "^3.0.0", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.5", "husky": "^6.0.0", "memfs": "^3.2.2", "eslint": "^7.29.0", "del-cli": "^3.0.1", "webpack": "^5.40.0", "prettier": "^2.3.1", "cross-env": "^7.0.3", "uglify-js": "^3.13.9", "@babel/cli": "^7.14.5", "babel-jest": "^27.0.2", "typescript": "^4.3.4", "@babel/core": "^7.14.6", "file-loader": "^6.2.0", "lint-staged": "^11.0.0", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^12.1.4", "standard-version": "^9.3.0", "@babel/preset-env": "^7.14.7", "copy-webpack-plugin": "^9.0.0", "eslint-plugin-import": "^2.22.1", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.0", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "c369cf8a47aa9922bd0d8a94fe3d3da11a7678a1", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.1.4.tgz", "fileCount": 10, "integrity": "sha512-C2WkFwstHDhVEmsmlCxrXUtVklS+Ir1A7twrYzrDrQQOIMOaVAYykaoo/Aq1K0QRkMoY2hhvDQY1cm4jnIMFwA==", "signatures": [{"sig": "MEUCIDsfSXTEaS1kgaaxQIPI8XycKBC6UIk5ruKGXBtqBZh2AiEA3A9p+ZfpgSNbnyorDUf97zFNS3TUm4ROE3O1t/LblXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1c0rCRA9TVsSAnZWagAAEdAP/1zT4xgmR6aAFxjlABdE\nPsiaw6ozBGYeUhYccspmpQWZNgy1sp8v8exkeLrb/wx+5IzFBxFvw75ebpV/\nP4RbT5WKyT/arAyXI/XVIJlBVPS8PTyxwrnv82o9ov/cCkPaYHuKK+SX79/Z\nVr2LiH8N+S8CMi6gzfRJny37gpYb0GXxYd/bQLy45uqZBWZog1gdHyX0CN8b\nL7+1EwQnWJFlhQ2f1mVi/uGjm0m+crgg/WXjBH80XZ3k13MmvT9g7OqdaC80\nS2unk4OOmBJ3ZQMP62oEusZWVY7oclD93CeNcUxCOKFbVtG5NAUnI7XdrDXI\nFjFhA4qu6QTvRrYYYHQw5dNdkcEfMX2Z/yxcxZcT3KoAsWPVKoDICfE8sygb\nC3/MyLlhM24AkLCJovAQIw93OCSVVUiyjaqV/8ioSOkj4v13SN+rJazF7l+c\nsR3TdfIvN+sXEuXQZxamzrTVeJtLCu1vZmxRe38Ob4wKp+o8DodC+JIEKqRH\nZ2SvDMDp0phmEd0LXJ9gpCjU2BF1+EKyVmofj54ACQswF2XJlAxYGgtkje0L\n7IOylYhjbO/2mL3tsVgQFXrGNlDRkX5mK4G3Gi19f95a2AsvoCWwnfNGVUx2\nzaPa0OC6PNDUzRaJh19BJwChZW2xf6ZmAmxx5ty6ZWwkRVvwslILDR+Sx35f\nxuZD\r\n=ccV/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.2.0": {"name": "terser-webpack-plugin", "version": "5.2.0", "dependencies": {"terser": "^5.7.2", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.6", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.12.23", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^13.1.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "694c54fcdfa5f5cb2ceaf31929e7535b32a8a50c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.0.tgz", "fileCount": 12, "integrity": "sha512-FpR4Qe0Yt4knSQ5u2bA1wkM0R8VlVsvhyfSHvomXRivS4vPLk0dJV2IhRBIHRABh7AFutdMeElIA5y1dETwMBg==", "signatures": [{"sig": "MEQCIF1LmQArQpt60hZJ2P+WqopAxiuEVAiara9lB2YaDCc5AiBAUFqLJfJhJM936T3OUPIlYgLW4Fjmal2aQakUfuynFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLmU8CRA9TVsSAnZWagAAXLUP/1rNMspL+KQTlXocD5Ii\nPHQXuDzoy0hsqTl8BInDW+u7nIs3viZPdCId8t/bpHc9ZHO9UBW7wP3kfMtX\nuIehhCltQNR1HRN4Sfl5T/gaTYT0ZYYmXudnx3SRhkd89WgUwlUMN734K8Md\nHRlnKln4kACdqJs9ArAE4tXA3RaiKEzQxTNh88OtDD9fWXTwd/e9kSDPFYBu\n2tRedFgrpT2QfOedW647P9CoqtVKomMAtxCuh7btMvPhdXeuqNuk22o1WBI6\nNSw+imicRZNeG4aopII390Xkrt41qrgjfeKcFxqaWWRDBa+rR7Kx0tvZ4GPe\nsXHTzvLF2tr2Yi1c4ZGf3OI6oJaVsKqSiauh6WeznDcAAvOiqzKLDMIVulPu\npznE7QY0TowzlT1Zn8mlR8k9TvDwG69gxxv9jz2hCoGgLXZqA6etEshZKATt\nECHS6fh3LS1Z8EHkoE+tgXWmAoREjNaq5T37W2BSCe3eYN/wPbtrXrPeBXDR\n2BYu/Onl7y35PWrLq2rLkKSPnrnl5Suinj34vpsSSaA7XhLxd94R+CNutAUA\nnXurPQRXFF3L9WH1m3RgVJNMI4qDv5XdW+WlT52bE2y83GoH8P6BSdA4B0Zy\nyrczOagg5Dl9S1tijJ5F0ZFWOgT2eV5a4/cTzUIv0DCIC03KYAwjGnvWjOHm\nERCv\r\n=qXPr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.2.1": {"name": "terser-webpack-plugin", "version": "5.2.1", "dependencies": {"terser": "^5.7.2", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.6", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.12.23", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^13.1.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "7baa96287ca8f29d22814d57c809cbae6be4de3c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.1.tgz", "fileCount": 12, "integrity": "sha512-mUAWsS2RDNL3rEr0ZTr7hm/R1DDxNwrED7Kf59F2rgFTfy+LrnciwA51MNWhGGQcqHnqvbPHgkW9LYr5HGBhfw==", "signatures": [{"sig": "MEUCIQDRFMjrlqXs1Mox+zTgaFbBekAFUHubpfj+kM6jBjNZeQIgRbsoKcXMeunNhGMjyz5s46/9i7R4R0ST/IOlg1Qx8DI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMLyOCRA9TVsSAnZWagAA2ksP/16Sh58ay6VQTEB5bCau\ndMdtjRDSQJ3box7HdvHh4VR2UIc4AyOfbh2c8B7Bv9LfwjJ9bQzFOnQLiinW\nzvDkhl45u6TsiulvT2j3/TD8l71Q6hIchLY2zKi61eceVfEJWluAHE5VRgq3\nuL0IH6FH8MdZ1hS6/NcDJa7Nyb6oG3fF1ti8BVmzkbD36HEa+suPk439kMpK\nyblq4O0nVrbtC7SBLXs8ZLWhSeo2P2DG/0HScByQpPzAAKUlMHgjhrWaPDQX\n7MBjfN1rlbmSa6Wp3TEgemilk2iZhP8Vrh4P6UH0zsxa3WtOGZpL+C+qpoxG\nLvbovJWN9ogaUw7BpirTkMUpZPb73zxyBX4TQsPWifu0zg+kyr4ZsFYuY6DT\nGRC5R4VBFMrfjyPr/g/brDZRqsQImmL1tiKN1t2+xhyiWtg0rLDw8J0bJOWK\nAvwJJue8DPu1yHI6femD8ldVaM8hrAx5mzDFyLbMwoaiGK7Ub9I7enrkNwRw\nJHAfqhWZfP3xit9VLYrumWLApPSePUfITDy/vk4A4I8AK8sDjinX9IchbIv0\nLi1XJm/LKE+UVS75w0mnX0E7/QbFGXqJwnpaOrRnoGw8iTPtctaNR3JJjlfN\npRE9dVZ4iFSXJEOyZwDGU+k1oGnyraTsCc0t5zOZX3144EdGwpePcu9g6CIe\nE3rK\r\n=1m47\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.2.2": {"name": "terser-webpack-plugin", "version": "5.2.2", "dependencies": {"terser": "^5.7.2", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.6", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.12.23", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^13.1.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "d7fd08109d47b8d4ec7dbfeee6b18fe1a9142d8c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.2.tgz", "fileCount": 12, "integrity": "sha512-/SNcbgI4Igd0E02R6HY7BnKkQBfAdV6BHcYYXk++QrGXpQoayMt79eaOtTrghtdRoDL5w3hgyMh+AippkuOEFg==", "signatures": [{"sig": "MEQCID0k8IYr3vq5LUP14ywpulbcX7q/7aDW8xb+XuF+1hOlAiBVsC935wMRLqNLRJE52M3H3/FbGHbnQdtYvLae/RF+KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMXOVCRA9TVsSAnZWagAAHcUP+QBZ0pTM4vM54fF6dRWc\n4js4uZJpR46Nu0UN82vpcDNKYJvi0yJyTrbMn/Di2DZIsyVRgTXIfMzTnbIN\n9klyMSXLRWNAAkdbxHMddGYhtnuDF7W/IgIUox/UvlmkwI8Jc4DIzzwAbqRW\nSUChVM0/nBVFROH1//zFo3YkBJo6Nh3+XG+QwlhXO082iIGWYz11F6g6atWL\nRzt5uaYu49hhS6TrtTIuPJKi94ydG4naghBQ4t1ErmvGYbitY1edGCqR8zsh\nX02owf00UpJEZtkaXtrhRTCX8hpvqk6IojF03+yM27ec1O9p+rMSJZStqGGY\n0F8lbZ6CvTXuc++AWXHOeJhUsyZAtTXebjRyaVSvhQz+RPHbFLWZ0KiGGNU2\nQcl0gkdY4pBsY0IkDvE9+TnH0H7iEjN4YTJqUk5j5Tmsr0KribDlTpAAglHO\nb3tr0P3WrnHcDlys8otnQw80Xbkqa1uojmZo/xjV2CO5YL/AfiYwEzpWRc0Q\nFSWv1dQ9giSjLheB5dLwK8ScxWfPLAJMuYVnDXuvWen2wpspxzi2IjrFL9GS\nDtZEpT0oq9IC6rBUR4/R5FKa0FOADeeDeTpr4S3EXgAagk4Qv4whci6bbrpS\nsBC+VIngROdyU0ql2rLWWyKhZtpFG75pLpj+eNRNiWIk46eMpKLGnHAU0eZf\nNODg\r\n=RSUZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.2.3": {"name": "terser-webpack-plugin", "version": "5.2.3", "dependencies": {"terser": "^5.7.2", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.6", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.12.23", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^13.1.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "4852c91f709a4ea2bcf324cf48e7e88124cda0cc", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.3.tgz", "fileCount": 12, "integrity": "sha512-eDbuaDlXhVaaoKuLD3DTNTozKqln6xOG6Us0SzlKG5tNlazG+/cdl8pm9qiF1Di89iWScTI0HcO+CDcf2dkXiw==", "signatures": [{"sig": "MEYCIQCCCSi77ZbfbsYY7n0ZV9bHvItZ2/sEVnS/j/ETRZfAJwIhAMdAnuHIi07gHAU6x8Wgib+2wq/mTEOXOYIrUytXEi3N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMhOOCRA9TVsSAnZWagAA7S8P/2BfNw9R1sHnWMxceNTR\nxcgq/aEU7jc7JYxLEJcI7wW0Lqa+BofYBW2Apz2zO/rR+v+Q4zKuXltmPhML\nEjWVKvkx2HElk9AP93qJ+0zrMou4WEIPifeyGGLRUzNdy3SqKWKuCWnm7+rr\nc5LqJzoouw+RIIYQk0YLfOQrA+HJ0KJlqclubsxuq7SGVgsF8sZWgEg2Ljlk\n/etKYuuuCWaPVpRWTAj6gZE56pEC00SVt3Mh7ew+RUNjxpD+ZAIXbbixgAup\nkfxQVZ+Kv/oDCRLzUXADNFoozD9E03w9wj5EsFWW+n95yID7PtGoYw34fDg/\nHF9RcrKbRJcLwcooTQh8TszaLIRoXO09IthOeMUTpuhvFSkY2F2XIaWYboMk\nP3zbHmq/gLwCvlf8OMfptl2e5zIvXdxZsdZ4xJYJLor5+xIqL8h31F2bHvRN\nFfPh81SNyyUQzLhBiKhosx3lYBGCmVLZnjguVxNo+ZhXY9Oex5sTB26eN0iz\n8Gb+97HE/XyvK5FEncY25CaKBRxwxI8+YX5JlLNxdjQwDYq5JIwHpbEo0DrT\nSvsklsvLz0n3eeCOCcxpmy+p9nIRnQK01FOSbcvSJkdK7AW15UTuclN2iviu\ncwQANI5vr7VfDY6rJrFAZfGZST19YFq49X20Ig+swA/PlG6vVPUnM61aBvly\nGClD\r\n=9jg7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.2.4": {"name": "terser-webpack-plugin", "version": "5.2.4", "dependencies": {"terser": "^5.7.2", "p-limit": "^3.1.0", "source-map": "^0.6.1", "jest-worker": "^27.0.6", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.12.23", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^13.1.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^13.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "ad1be7639b1cbe3ea49fab995cbe7224b31747a1", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.4.tgz", "fileCount": 12, "integrity": "sha512-E2CkNMN+1cho04YpdANyRrn8CyN4yMy+WdFKZIySFZrGXZxJwJP6PMNGGc/Mcr6qygQHUUqRxnAPmi0M9f00XA==", "signatures": [{"sig": "MEYCIQDRiogNWh/0+3p5zzwY9z916SstXikbj55+JCWy2uz+PgIhAMdcJ94i7febk+dqho2WZ0WhL4qfnB9SYIsv3zzCpeo8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOivFCRA9TVsSAnZWagAAfC4P/iQ1RWlfbfotZZtpHDnQ\nRC1Qwt+QY+oVfPPPFCcHNwDMpaWl6T2jkVBZvl1SEVUqBhHRijGvx8H7fRYx\nKUhe3xCaW0246wyi/lv8yIFakuBZq8tYEtuTBeKbtRw6vTLEn3GgA6o+58MY\nsZpbg/PIICAtSr00JeYozrwF7lLc6IKm5vq8H8co+hJfxvpvpF6XIzKs9XRh\njpPcT1NqatkNKCdf6sinCm+QfDLlPjQS6H+yoqO92yYTRRcNEjHH4vqkXv4P\nMdA0cG2RAuu4Kvv4YtTUDWr7S5DKpKfOVL6UhVzsDIKcOWo1WRf8Ulkrj+XD\nBsmOMH5QulF5YEiiv871spPT+Ap3CwoXoFlDgG+Qvq8UTaktR09VrkTC+68D\nk54ef3Syghz+edbXo0mnlCOdSf6RSOPUdeQfWx6x0KM6jsk2TL7Q3jckSdcz\nq61QmtepTGEc0ci1nXwnYI74FSjdNV3/LkyTHPirwUctDYGwcS3AVWz70Nca\nh3rqAvkvHjfX0JZL6O/VuRsnWlh2yxbtvepmJdbi99a8NWLpNcQ1zyutY28e\n1rDmqYYJE+YoAI0WlxwhE4LX7ji4PNIPN6H2X6/FPd6tuaJfvlD7JJX+cTN0\npPwI0qFf0QRf0jr2ZKtJCCB+2rWnxckCQnBriwHOjbWr7Ycn1LlCaK3PveSd\nnfwX\r\n=/s6c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.2.5": {"name": "terser-webpack-plugin", "version": "5.2.5", "dependencies": {"terser": "^5.7.2", "source-map": "^0.6.1", "jest-worker": "^27.0.6", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.13.3", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "worker-loader": "^3.0.8", "@commitlint/cli": "^14.1.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^14.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "ce65b9880a0c36872555c4874f45bbdb02ee32c9", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.2.5.tgz", "fileCount": 12, "integrity": "sha512-3luOVHku5l0QBeYS8r4CdHYWEGMmIj3H1U64jgkdZzECcSOJAyJ9TjuqcQZvw1Y+4AOBN9SeYJPJmFn2cM4/2g==", "signatures": [{"sig": "MEYCIQDj+P77/8U5YYP7zyrTXhmjIosXQ7GeVry5gdXfYX7kmwIhAPWwxVDYKejcu+dubtHmQPyt+YhKkWr1q1jr0A5Blo/V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90369}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.0": {"name": "terser-webpack-plugin", "version": "5.3.0", "dependencies": {"terser": "^5.7.2", "source-map": "^0.6.1", "jest-worker": "^27.4.1", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.0.6", "husky": "^7.0.2", "memfs": "^3.2.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.0", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.82", "cross-env": "^7.0.3", "uglify-js": "^3.14.1", "@babel/cli": "^7.14.8", "babel-jest": "^27.0.6", "typescript": "^4.3.5", "@babel/core": "^7.14.8", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "webpack-cli": "^4.9.1", "worker-loader": "^3.0.8", "@commitlint/cli": "^15.0.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.14.9", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.24.2", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.1", "@commitlint/config-conventional": "^15.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "21641326486ecf91d8054161c816e464435bae9f", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.0.tgz", "fileCount": 10, "integrity": "sha512-LPIisi3Ol4chwAaPP8toUJ3L4qCM1G0wao7L3qNv57Drezxj6+VEyySpPw4B1HSO2Eg/hDY/MNF5XihCAoqnsQ==", "signatures": [{"sig": "MEQCIHPvg/nE3W51/5sl3e3MIqtsWpuiyEHJ10/yciyiIpQnAiB3wTPNvihFZUYUILT3gfBIzS2MBOrfyzUJJ91hdPL4uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhu1KVCRA9TVsSAnZWagAAtFgP/iD3PI+1GOgK41VeYTEp\nCWuhZxniLIC6FBnyfMkBnvpnu0eycK0SlGQ34tvXyyjFi2FyRu510TnYbDSo\nn+PBZHli9E2B/hIM1cADfRWfdMtwP59KltjM0oMXoVo9jWkDJlC/2k9R5cBZ\nlYHBy63fPubCj3K+od1TiSw2nGH4t/8a2GpXi/r8wyx1/n0qUUzoidxh5g/7\ncKA4vXfhTxQNoQxbS4ydxlV0wQHLNowc8seQgAP5+ILlGsn1fV994rlcu2+C\nlOw6sZgR2SiSEKLF8AOIc+86Xrw4AYsNv6w2Kf1eaFnKiN/E3ht7M+P3l8zF\nPOLM6uNkMG6UavKSmpeZBTF3TY5611FCeHAeGuojaB49eMB32btESyG+8xuX\n4y2whdxo1YVcN8OrDcc1Mc6CDt0GGXlEgX4ZwwQDcRwo4k3Au8jeAUrFIdxs\na6gNnrU79YMzCvn9ngZqU6WduEGXZKzv8xH5xI8BYvA3LoxUEzHVRloQhmn9\n2qjxj85QINsYzbvoHenvf1lTHbWxwJFSPJ3j3+eaIMSEv+N5NjQVy3iDvd12\nLhRMdlvw+x+4AOreHe9C85VGakMt26KqW7QAfGe8l3tuAo6/29GpRC8luv2I\n/FsH65dwJBzx+E1roXwU4U90ifPDoLpPG7xagfVPAwnI/C0Y54HkD0hdSmM2\nBH8s\r\n=LVjq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.1": {"name": "terser-webpack-plugin", "version": "5.3.1", "dependencies": {"terser": "^5.7.2", "source-map": "^0.6.1", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.4.5", "husky": "^7.0.2", "memfs": "^3.4.1", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.10", "webpack": "^5.48.0", "prettier": "^2.3.2", "@swc/core": "^1.2.126", "cross-env": "^7.0.3", "uglify-js": "^3.14.5", "@babel/cli": "^7.16.7", "babel-jest": "^27.4.5", "typescript": "^4.5.4", "@babel/core": "^7.16.7", "file-loader": "^6.2.0", "lint-staged": "^11.0.1", "npm-run-all": "^4.1.5", "webpack-cli": "^4.9.1", "worker-loader": "^3.0.8", "@commitlint/cli": "^15.0.0", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.16.7", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^15.0.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "0320dcc270ad5372c1e8993fabbd927929773e54", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.1.tgz", "fileCount": 10, "integrity": "sha512-GvlZdT6wPQKbDNW/GDQzZFg/j4vKU96yl2q6mcUkzKOgW4gwf1Z8cZToUCrz31XHlPWH8MVb1r2tFtdDtTGJ7g==", "signatures": [{"sig": "MEQCIARMVRkXjMw1wgHpjcZh52kr1lc0q5xatuf0nV9L+f75AiBRxLisP/OKH+AqsbRL3qsWdaOxaj9LtgY0lMEX6JPVGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+S+UCRA9TVsSAnZWagAAtvkP/1nI5TXmOLp36u+lk3T1\nUo8O5jQ5A6DP83f78o9UW9qUQqQ50HHLy+IZ58Nw3pNg2rdpmE0yeZ4Q48eA\njLKgeOUeeme5YNGkimZlDTqgXsGMiWS/u3725VV6LBzuTz+6W3OjAYGiJ8lR\ntigd4OGduoSq23FefHRqCBh8zwySBbSs1g8DcykrEuukqV1jC4mtOfMUz3bu\nFTRZ+uGut1sol8M4CO/nBBfNBJcc9Q27HJlY2ECe1e0TK9TGPAxAqvLBWnQN\nIHHr4rMjQdJlvzLiFLNM6EZFANnTvX2zVs3iAou/C44hwH1+QatsS8VwJ5os\nEu04F1WvB6CZ+ppUByo5tN3VB2z2b14T2vrevYwxv2kXMGx3uwwkrIuh5DTo\nyceTYwhuW4KOSTFKbXyh/1+vu/GKpzI51CcT1yTfFvR8ml7vcaffoKpr/O0r\nY0ck45q0YDJQjqJOSyJM+eqx4W59G3alFjpWrIwjcLIhAMJFE0xgRNnrW8AI\nKxSDTUap1D5DxNjd4ZVZa8NkfL3ddh/S7pj/KqvGRrKmBt+e2DX46D1BD8bB\naR+GD3tZzwE+vj4sIY+RcnjYcFyIuerHbWdfjLkg0u2OFaXalAt0YnstifH9\nQeiQ7U2f3GZloywXczE6YVtfE7tMpfj/UJVs69VYQGMGcxuZppG7DbqQUwkB\noy+0\r\n=Te8V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.2": {"name": "terser-webpack-plugin", "version": "5.3.2", "dependencies": {"terser": "^5.7.2", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "@jridgewell/trace-mapping": "^0.3.7"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.1", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.10", "webpack": "^5.70.0", "prettier": "^2.3.2", "@swc/core": "^1.2.126", "cross-env": "^7.0.3", "uglify-js": "^3.14.5", "@babel/cli": "^7.16.7", "babel-jest": "^28.1.0", "typescript": "^4.5.4", "@babel/core": "^7.16.7", "file-loader": "^6.2.0", "lint-staged": "^13.0.0", "npm-run-all": "^4.1.5", "webpack-cli": "^4.9.1", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.0.2", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.16.7", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "b0cad14c922b2e740c400c69e678d6bc5824b653", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.2.tgz", "fileCount": 3, "integrity": "sha512-ftljYw7jiNTLhbQPhmz2JXQSRD/vlpa234mdsDDPOiCdlu1LHXgu8LalvDbemfgGVHB/CbyKpzZSxAywgwE9dg==", "signatures": [{"sig": "MEYCIQDOG4IbcRfJeWfgDTgMmMPgntXHoLF0XLyGJgJ+dJ10LQIhAJYqIbX9Ot1u0LmnlJQWUb8erNqn1bxvxlXg8EOlNi/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimAYPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQ2xAAmgpWPulpnoUJjQL6HptyaqX+i5qNVn/e9kVTVITAuGC/xihz\r\nioMyYRJRXTeX4aTh4wmWLovqzK+9JM/UynXgj3i8oKD3vkMn9jWRszRmw/wk\r\nE1FHDmtdwPmUPuIEAAMYv0zDTLw14Y/TCtw4lsT7qKNlrwFfVV4dfq2QLpHH\r\n2zWSpGIf61ASY0ALiTMjn9egznvZJJcp4V6VoO6+zraFP1Bd9pSEkwS7yaHU\r\n0QANqRPsJcYmwNiAIDg5e7TlMc+KNdmvEjv18NAkFLN0oNh/sbSt942yBwn6\r\nVglTjudGYy5wqXuOmZpcYx4y3meGO4IrK8M2tGQy+fOI+ImxMP5vhe5EKuFk\r\nWW5nsLp/jUDhrRMRXiAQnaYivhDfMeTZRXI9b6J2i6UQX4kpRqWCVEr6Rxrb\r\nzWE9T8e2/PlkVztWTd1CN0EGv6fFp2gwke1OKOLhk2hFXS64IgPfsGtl8ef0\r\nO/ZnHjkor3SHHXTtjOgX70pk/bKmdof1hRxULKwkD3746ek4loSQti30FgtC\r\n9opP/djqr4sR5KIEhftdGjViCV9MPDPJwrnqwKAf9oKbEFjWf7RuhHjGwWbN\r\nUaHPgDA0b94QB6nU5BYqeUBhLqhvF3Ygb6Jvvz0YMPi4HJg2wyW6zPcFUfpQ\r\njxyu5d0mpsn0e2qLNcet8c+0gIxcn+UT/LE=\r\n=vM0z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.3": {"name": "terser-webpack-plugin", "version": "5.3.3", "dependencies": {"terser": "^5.7.2", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "@jridgewell/trace-mapping": "^0.3.7"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.1", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.10", "webpack": "^5.70.0", "prettier": "^2.3.2", "@swc/core": "^1.2.126", "cross-env": "^7.0.3", "uglify-js": "^3.14.5", "@babel/cli": "^7.16.7", "babel-jest": "^28.1.0", "typescript": "^4.5.4", "@babel/core": "^7.16.7", "file-loader": "^6.2.0", "lint-staged": "^13.0.0", "npm-run-all": "^4.1.5", "webpack-cli": "^4.9.1", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.0.2", "@types/uglify-js": "^3.13.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.16.7", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.0.2", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "8033db876dd5875487213e87c627bca323e5ed90", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.3.tgz", "fileCount": 10, "integrity": "sha512-Fx60G5HNYknNTNQnzQ1VePRuu89ZVYWfjRAeT5rITuCY/1b08s49e5kSQwHDirKZWuoKOBRFS98EUUoZ9kLEwQ==", "signatures": [{"sig": "MEQCIFUpAtWIWXM9cDTCortq4wO/DX/S1besHoludBiqI6H9AiArjXFFtprY2xX7noC8Yn+oAaSIc+zVcThrSbWCyXnJPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimBT9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQKg/9HVYYTSRl4qWj7DPeIXPe5Xfmh7wpFY/mZW+oblhcaCTKg/N/\r\n2OxHyEDjgZa8D/6hO31VhGeQ1r8FQ52bETJYzZhylYCeWTIYRkXsF4qsGcr4\r\nuYuVvWyuydZlAdRdRNKkTtlv3Vyi49yw+xDISmPqQ6m/9MlpDdy16DGBjgce\r\nxNgtriLTSFhrvc6ORPHkdlppRgLbDZB4yWQzzfq2HFdme/95zI1BHPD7yHYx\r\nW5enPCQN4h0rJ+N/DI2S62ZClsyEvhT42at3ZZsXSCcKbFREVdhU8FX7wAcd\r\nzCecPkpafwseKR3odR+0CcvS88MSl5K5imlGGoTsBg6Q3I2pA63A7xQ3+ZRt\r\nveyFqtGkcNfgrD/+XC7iJIq9HdJCssDZSk6Db17q2LgX7vKXh3iSpoFqCuGX\r\nQfkWtjyMz8kDFVLLCUUj2waIDARlHKFqNd2xvWWRkRu261gdWcQpMmuZlunj\r\nZwDamSaBvOso1jnt4T5JpiYAgW6BHLi4qgzX6CzqJU69mbRR/OIyfyfgvkSo\r\n5OSbPrRb+207T5brRYmWGRTP3XszbKT5Bg9JQ0XNdr4GkNDdFKlWNts0//U2\r\n4pc4uE403VCgMVi9QPJff+FluliD0Sr2vzCIjsUZ0ZJ+LeDEnpsJsTuJK9PH\r\nHfSpc59cFXNWKPcmD8tQYYSYebkhRF+R1lo=\r\n=Jq3Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.4": {"name": "terser-webpack-plugin", "version": "5.3.4", "dependencies": {"terser": "^5.14.1", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "@jridgewell/trace-mapping": "^0.3.14"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.7", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.51", "webpack": "^5.73.0", "prettier": "^2.7.1", "@swc/core": "^1.2.220", "cross-env": "^7.0.3", "uglify-js": "^3.16.1", "@babel/cli": "^7.18.6", "babel-jest": "^28.1.2", "typescript": "^4.7.4", "@babel/core": "^7.18.6", "file-loader": "^6.2.0", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.0.3", "@types/uglify-js": "^3.16.0", "standard-version": "^9.3.1", "@babel/preset-env": "^7.18.6", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.0.3", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "f4d31e265883d20fda3ca9c0fc6a53f173ae62e3", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.4.tgz", "fileCount": 10, "integrity": "sha512-SmnkUhBxLDcBfTIeaq+ZqJXLVEyXxSaNcCeSezECdKjfkMrTTnPvapBILylYwyEvHFZAn2cJ8dtiXel5XnfOfQ==", "signatures": [{"sig": "MEYCIQD3WwJf6hw2MT1NJO8x54R2i9gdIOQNF2avfUhHX5mqmAIhAMONpiypmKbhWXtV+WMXdrPWIkOzlCjQ53Ee1jEhya5q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9mC+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi5w/+MsMeJqCGrV0wOce9e4AcB+o9Wfq4pVB85iMD2bD4bj2wccBB\r\n//S795aU08GkMqatOky3eeLAMP/hw7ygcGOPQO+RLyHewiL8qmz/S0lENbAN\r\nWLMmTfY3+b69sI9SlVYJTBNqA3c+LlOhnQzJDMCIazbJ7l6hWgXpcSoiP/IX\r\nb7aR46xlpDdJ4hfJ9aK/N32pdENCtqupl3gHkCQmtN/hFBzzOTx09/k8nlKh\r\nX/0/jQoXIsG2tWznX4ALRjIwVknEp0QKxGqLX1DAn9RB/altpXaTCEhXmHFb\r\nh2+2HHKbrXlQTGNzWS41Tea7FX8jZc2zdfaHm7uhzk5rbrLuGoc6V1XVmAnW\r\n2QAZQ2qpQl9PbDabBjpH7bZvNwfEQXu94Wv1Xpot+VrYtqQBOY09aBHTnk2b\r\nd9usOLnhHJjBZ7EfkyGrr3ArWnKsI9XeZHm07KiruC72UBmlqd3PrsBpPwfW\r\nBbMHLL6fYMiKhOAVyuiJ2Ni6kVVjjVmA3aAWMohjR+83aVv0GJKJZ4sxa7fJ\r\nHmmev8nkuHev2dHCniTQ/Qly/EUE1y2QjR2c0Htgo+i5MC2XHFeT23HA8pt0\r\nKnzJJuqwDDWUeTwe8HTj4bVq1sQetTOB7DeDDzY3OosVJzsiKD5jNzK4homv\r\n+a9+lryChUivl+AV/RbQtHCevZ/Z4sMdXLk=\r\n=dUXy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.5": {"name": "terser-webpack-plugin", "version": "5.3.5", "dependencies": {"terser": "^5.14.1", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "@jridgewell/trace-mapping": "^0.3.14"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.7", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.51", "webpack": "^5.73.0", "prettier": "^2.7.1", "@swc/core": "^1.2.220", "cross-env": "^7.0.3", "uglify-js": "^3.16.1", "@babel/cli": "^7.18.6", "babel-jest": "^28.1.2", "typescript": "^4.7.4", "@babel/core": "^7.18.6", "file-loader": "^6.2.0", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.0.3", "@types/uglify-js": "^3.16.0", "standard-version": "^9.3.1", "@babel/preset-env": "^7.18.6", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.0.3", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "f7d82286031f915a4f8fb81af4bd35d2e3c011bc", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.5.tgz", "fileCount": 10, "integrity": "sha512-AOEDLDxD2zylUGf/wxHxklEkOe2/r+seuyOWujejFrIxHf11brA1/dWQNIgXa1c6/Wkxgu7zvv0JhOWfc2ELEA==", "signatures": [{"sig": "MEUCIQCtLiCSlV3/ZFes8kcwCH2rwqFAx50j8nZk1tghUmz3CQIgMz94239242hJpkOOuQ7qiJuzNB81HYj2vC2Rx6MND80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+5SFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC3g/+O60ILLXtWoSdMIJ+FQkyV37HIo8yEWsn00L5O2tUvyjvbXpE\r\n2ZAJ7yV2/7LIeiqncEcenZ5oUAb4y0fhNwlWHPOkmcnvlcFWJxeeKuPPhXpr\r\ndALaMkuIEhpgs0HkPbOEhHsilzmHCwQjRhtT+mKrl31fT5lojI10gfDBwXtr\r\nnQ0LEXkASeNuzX1R2Jw5ObuknDgYpViJrpE4RMGI20x2RyL+fA9GoZkgV7uU\r\nBCcz1+SJWK1cb+WPZOUSNjs2qjaKOXvNBYuitDdj6/B8YtpVc9NNXtQSpq19\r\ncCSJo9WHY6n+WJogiTFWL+1C4el9x7oMItu3RotL+BhFQaX0LgQsPcKQdSOy\r\n2AwMrJXagQ1sliHlSuU+Ow6auGhg04KeSjlnDI5yjFVX+4knTTvIxzWN2Nog\r\nDbF6c7hfwSoQbjv/1zHsycFkYMVxYUbLHwowpBDnL5cVXq6cdncPdHV8dYib\r\n8gb2iX7MUFmFGzWYBZv68AixSeNNFmqVF6GfkcoN2Qp9qoyaBm0D+ak1W6yp\r\n4KY0Abh+UR2T9Qungi5TtQLwMp2uoWQFFfKvChkmXFIxdKMlKnZdwaSRGrUR\r\n8T1lA3Q8ayYLbAkdg5ig+NspIxFJYWP4mtZRZRTs3iI8Yr3mmWg7sOr0SSdO\r\n9s7Mb75EorpfHz89jMfG5yuMbEErUXwedeE=\r\n=Klan\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.6": {"name": "terser-webpack-plugin", "version": "5.3.6", "dependencies": {"terser": "^5.14.1", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.0", "@jridgewell/trace-mapping": "^0.3.14"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.7", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.51", "webpack": "^5.73.0", "prettier": "^2.7.1", "@swc/core": "^1.2.220", "cross-env": "^7.0.3", "uglify-js": "^3.16.1", "@babel/cli": "^7.18.6", "babel-jest": "^28.1.2", "typescript": "^4.7.4", "@babel/core": "^7.18.6", "file-loader": "^6.2.0", "lint-staged": "^13.0.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.0.3", "@types/uglify-js": "^3.16.0", "standard-version": "^9.3.1", "@babel/preset-env": "^7.18.6", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.25.4", "eslint-config-prettier": "^8.3.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.0.3", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "5590aec31aa3c6f771ce1b1acca60639eab3195c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.6.tgz", "fileCount": 10, "integrity": "sha512-kfLFk+PoLUQIbLmB1+PZDMRSZS99Mp+/MHqDNmMA6tOItzRt+Npe3E+fsMs5mfcM0wCtrrdU387UnV+vnSffXQ==", "signatures": [{"sig": "MEUCIAWiHDdbFvMDgNHeGIoPJsaBIoU4xZBj6hfwqmdJYwUyAiEA6yvWFFQeyq44paHLt80o8UmNNtsramu1mHNC1snmz9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDQI+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyshAAi2Aigwx/AihFcLfFHOX3+0Y/sYUx1B8M+7j+bWxo1CeHggao\r\nIFed8fksPUbnCgfVAaUTMXU1OCXKEgvn1X/brfcWWXvHQeGMIURasVoT/8mb\r\n/wVlgg4wZWXQd1sVDZaimKBEuFabeJ1gqDtfPIJRVMM/e5+vKi5m2/L+Ddwa\r\nYPM+DHFXFwjZyuxS6aONepNvl0MY/x41E6WUOk6r4m/bx210WutErbkgZlt7\r\noFSc2q0QmQKmFrYRQ6JtozP7F3wuzrT3y0kdQUaN6sTacWZ5nGUhqg5BFKVQ\r\nVwagh+4QW75wJLPlrhO1JqUmnPkwEdndAhz31zHGJ+2erZN2RRQc+8XALEwQ\r\nGBw34MiSxyHzdReBDIm+I1Ld/x8jmH9tQuuT0+VkfoCihHXdfMqbNvhHklT7\r\nvWuAFDHF8j8ToilTG68g+daQAW0q+7W28T+xQb6R8NG0S6pXT1y4QgzIlmLo\r\n57464R64c2aeIByWM7AgOp9dBWw4O25/nNrmYBetGRQixgxw18FTh8ZTlZbM\r\n/5zZbBU3I0Y+/pWAuqkJSNEg9I2cQjqjj0E1YIGdbXTs/WrO1u+3K/4wKbzZ\r\nX37D0bAx9WuCWFDgBrG0EN8kzfvfhKdFzR0QsJWnaa4WCG6Z11DuG8xcGYXm\r\neEu/PImk0Vqjw6565MxdHqvV/2BSiReD4E0=\r\n=aHHq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.7": {"name": "terser-webpack-plugin", "version": "5.3.7", "dependencies": {"terser": "^5.16.5", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "@jridgewell/trace-mapping": "^0.3.17"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.27.0", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.51", "webpack": "^5.75.0", "prettier": "^2.8.4", "@swc/core": "^1.3.37", "cross-env": "^7.0.3", "uglify-js": "^3.17.4", "@babel/cli": "^7.21.0", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.21.0", "@types/node": "^18.14.4", "file-loader": "^6.2.0", "lint-staged": "^13.1.2", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.4.4", "@types/uglify-js": "^3.17.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.20.2", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.27.5", "eslint-config-prettier": "^8.6.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "ef760632d24991760f339fe9290deb936ad1ffc7", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.7.tgz", "fileCount": 10, "integrity": "sha512-AfKwIktyP7Cu50xNjXF/6Qb5lBNzYaWpU6YfoX3uZicTx0zTy0stDDCsvjDapKsSDvOeWo5MEq4TmdBy2cNoHw==", "signatures": [{"sig": "MEUCIAWCpg6vusfB/V0+npi2pb260BNhfXQ6ldXw6mjrRbcQAiEAkp2bB9jsQ5Km0aIKMxhYG6kaAK4v1xIFPK3k65lFESM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCRD6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHbg//RBBd3B14JkrGJDb8xzrM6ArWzCAhmUdxHhYkuQmDkv7pLBWz\r\nzTv0C8xxuGs93RjzLwqZA+9IoJYi9hbMayorNcBSihgoPFmRR2c7JWoB5QHM\r\nXTiuVy8ifOj+RAiqQlJvR74yXy4NTF/6x4KoWf7odAJZ5y8oBxCNqK4mLR+M\r\nLjYdcf+WlqyBje6RYgHzg3TmWFVCro9UptGEzStePEq923FwKMFVZHRdW4F7\r\ny9uxBoY5wZlilUs6CgHNKgJApV+TOV3xcjogmd0EBYES1amC/8YiFffwB3tA\r\n9cCaZhNrlyWgVnmT2SWkzuvF+aNUYursOY7x3ysWmRs+ykSkFr9VVcPiGUug\r\norFF3rHBu2C6qL25JgdSRdCXcrxP5MCnAPQsQ37HPN8qtejtkGqNZrC7IrOm\r\nFNVp0f3H3z9rNYZt6AdnVz8qpKZSTrpEaiNvgztou1G4SzvtiSXQgnFeqrTL\r\nAu8TfeXMpDNg+hg1I6Ned2PCz4Ud6YXEP8274hkiqDaoly7hKR5TelZtuLPs\r\n9si93UEPejQt5T5dc1v6bY81LEeBihFV9R27hjImP4bNYrpPlRRDTG88iNTx\r\nJTQZxebZjkuie2tmd+h5LfaETlJPAQMmnC1DLlnVD0L0bmQMsVaHT+mnnvzz\r\nvOwfHi19+tO0pHYUIMcpr8UjODac0YLXswE=\r\n=rpeM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.8": {"name": "terser-webpack-plugin", "version": "5.3.8", "dependencies": {"terser": "^5.16.8", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "@jridgewell/trace-mapping": "^0.3.17"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.31.1", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.51", "webpack": "^5.77.0", "prettier": "^2.8.7", "@swc/core": "^1.3.44", "cross-env": "^7.0.3", "uglify-js": "^3.17.4", "@babel/cli": "^7.21.0", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.21.4", "@types/node": "^18.15.11", "file-loader": "^6.2.0", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.5.1", "@types/uglify-js": "^3.17.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.21.4", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.27.5", "eslint-config-prettier": "^8.8.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "415e03d2508f7de63d59eca85c5d102838f06610", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.8.tgz", "fileCount": 10, "integrity": "sha512-W<PERSON><PERSON>3ElchZMsK27P8uIUh4604IgJyAW47LVXGbEoB21DbQcZ+OuMpGjVYnEUaqcWM6dO8uS2qUbA7LSCWqvsbg==", "signatures": [{"sig": "MEYCIQDZa8mY11G61zZ39zqE4HB6gwKFAikDL5lcBf4pwmASGwIhALyl10sx6ONUDl5JV75eOZ948gixunFZ2rnSqnDRvED0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87799}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.9": {"name": "terser-webpack-plugin", "version": "5.3.9", "dependencies": {"terser": "^5.16.8", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "@jridgewell/trace-mapping": "^0.3.17"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.31.1", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.14.51", "webpack": "^5.83.1", "prettier": "^2.8.7", "@swc/core": "^1.3.44", "cross-env": "^7.0.3", "uglify-js": "^3.17.4", "@babel/cli": "^7.21.0", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.21.4", "@types/node": "^18.15.11", "file-loader": "^6.2.0", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.5.1", "@types/uglify-js": "^3.17.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.21.4", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.27.5", "eslint-config-prettier": "^8.8.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "832536999c51b46d468067f9e37662a3b96adfe1", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.9.tgz", "fileCount": 10, "integrity": "sha512-ZuXsqE07EcggTWQjXUj+Aot/OMcD0bMKGgF63f7UxYcu5/AJF53aIpK1YoP5xR9l6s/Hy2b+t1AM0bLNPRuhwA==", "signatures": [{"sig": "MEQCIDOY9x8Fhvw5h7CtemifrlMKHFCrc/YDyFEKwYQ4fTekAiASUvDO85uSpPb6fNXAuJ0G5YnNTa46MUtSFwFZLYruRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87860}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.10": {"name": "terser-webpack-plugin", "version": "5.3.10", "dependencies": {"terser": "^5.26.0", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "@jridgewell/trace-mapping": "^0.3.20"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.31.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.19.10", "webpack": "^5.88.2", "prettier": "^2.8.7", "@swc/core": "^1.3.84", "cross-env": "^7.0.3", "uglify-js": "^3.17.4", "@babel/cli": "^7.22.15", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.22.17", "@types/node": "^18.15.11", "file-loader": "^6.2.0", "lint-staged": "^13.2.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.7.1", "@types/uglify-js": "^3.17.1", "standard-version": "^9.3.1", "@babel/preset-env": "^7.22.15", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.28.1", "eslint-config-prettier": "^8.9.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.7.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "904f4c9193c6fd2a03f693a2150c62a92f40d199", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz", "fileCount": 10, "integrity": "sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==", "signatures": [{"sig": "MEQCIDZmr+T9LWJ06nZacC4RcNDV4ikHM/wDdM4uEeWfEWtLAiBBkRBbbKYby5h69PYh9lXznkM6vxAtfJY6HPLeNVuIHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88044}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "1.4.6": {"name": "terser-webpack-plugin", "version": "1.4.6", "dependencies": {"is-wsl": "^1.1.0", "terser": "^4.1.2", "cacache": "^12.0.2", "source-map": "^0.6.1", "worker-farm": "^1.7.0", "schema-utils": "^1.0.0", "find-cache-dir": "^2.1.0", "webpack-sources": "^1.4.0", "serialize-javascript": "^4.0.0"}, "devDependencies": {"del": "^4.1.1", "jest": "^24.8.0", "husky": "^3.0.2", "eslint": "^6.1.0", "del-cli": "^1.1.0", "webpack": "^4.38.0", "prettier": "^1.18.2", "cross-env": "^5.2.0", "memory-fs": "^0.4.1", "uglify-js": "^3.6.0", "@babel/cli": "^7.5.5", "babel-jest": "^24.8.0", "jest-junit": "^7.0.0", "@babel/core": "^7.5.5", "lint-staged": "^9.2.1", "npm-run-all": "^4.1.5", "@commitlint/cli": "^8.1.0", "standard-version": "^7.0.0", "@babel/preset-env": "^7.5.5", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.0.0", "@webpack-contrib/defaults": "^5.0.2", "commitlint-azure-pipelines-cli": "^1.0.2", "@commitlint/config-conventional": "^8.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}, "dist": {"shasum": "87fcb6593fd1c977cd09e56143ecd31404600755", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-1.4.6.tgz", "fileCount": 9, "integrity": "sha512-2lBVf/VMVIddjSn3GqbT90GvIJ/eYXJkt8cTzU7NbjKqK8fwv18Ftr4PlbF46b/e88743iZFL5Dtr/rC4hjIeA==", "signatures": [{"sig": "MEUCIQDMTE7lTKxTGskhJDssqsmsaaRfJgCp+r4wXODeDl8X1AIgKvq6h/AcbIL02FA91I3TNJYt+uUZsoqTE8ZOVQwRwO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45485}, "engines": {"node": ">= 6.9.0"}}, "5.3.11": {"name": "terser-webpack-plugin", "version": "5.3.11", "dependencies": {"terser": "^5.31.1", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "@jridgewell/trace-mapping": "^0.3.25"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.31.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.19.11", "webpack": "^5.92.1", "prettier": "^2.8.7", "@swc/core": "^1.3.102", "cross-env": "^7.0.3", "uglify-js": "^3.18.0", "@babel/cli": "^7.24.7", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.24.7", "@types/node": "^18.15.11", "file-loader": "^6.2.0", "lint-staged": "^13.2.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.7.1", "@types/uglify-js": "^3.17.5", "standard-version": "^9.3.1", "@babel/preset-env": "^7.24.7", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.28.1", "eslint-config-prettier": "^8.9.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.7.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "93c21f44ca86634257cac176f884f942b7ba3832", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.11.tgz", "fileCount": 10, "integrity": "sha512-RVCsMfuD0+cTt3EwX8hSl2Ks56EbFHWmhluwcqoPKtBnfjiT6olaq7PRIRfhyU8nnC2MrnDrBLfrD/RGE+cVXQ==", "signatures": [{"sig": "MEUCIEYfcbRuhDjJLORmzcWZHjnwmFzpSgYFBYDwTPllK+FzAiEA9gw/LosfwuOgNj9PWsqxnSOE8hBl3f+qhrufviwXdMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88042}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.12": {"name": "terser-webpack-plugin", "version": "5.3.12", "dependencies": {"terser": "^5.31.1", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "@jridgewell/trace-mapping": "^0.3.25"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.31.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.19.11", "webpack": "^5.92.1", "prettier": "^2.8.7", "@swc/core": "^1.3.102", "cross-env": "^7.0.3", "uglify-js": "^3.18.0", "@babel/cli": "^7.24.7", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.24.7", "@types/node": "^18.15.11", "file-loader": "^6.2.0", "lint-staged": "^13.2.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.7.1", "@types/uglify-js": "^3.17.5", "standard-version": "^9.3.1", "@babel/preset-env": "^7.24.7", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.28.1", "eslint-config-prettier": "^8.9.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.7.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "d9518c80493081bace668aa8613b22e4a838810c", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.12.tgz", "fileCount": 10, "integrity": "sha512-jDLYqo7oF8tJIttjXO6jBY5Hk8p3A8W4ttih7cCEq64fQFWmgJ4VqAQjKr7WwIDlmXKEc6QeoRb5ecjZ+2afcg==", "signatures": [{"sig": "MEUCIC4lhGpE/i0ph7KG0UzvJBvvFjxzW+YVqSYfIazpKn3fAiEA06+1BZYW0qj6p9Y3WJV6X/JPgsi5N+2swnX2tc1dtCw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89123}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.13": {"name": "terser-webpack-plugin", "version": "5.3.13", "dependencies": {"terser": "^5.31.1", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "@jridgewell/trace-mapping": "^0.3.25"}, "devDependencies": {"del": "^6.0.0", "jest": "^27.5.1", "husky": "^7.0.2", "memfs": "^3.4.13", "cspell": "^6.31.2", "eslint": "^7.32.0", "del-cli": "^3.0.1", "esbuild": "^0.19.11", "webpack": "^5.92.1", "prettier": "^2.8.7", "@swc/core": "^1.3.102", "cross-env": "^7.0.3", "uglify-js": "^3.18.0", "@babel/cli": "^7.24.7", "babel-jest": "^28.1.2", "typescript": "^4.9.5", "@babel/core": "^7.24.7", "@types/node": "^18.15.11", "file-loader": "^6.2.0", "lint-staged": "^13.2.3", "npm-run-all": "^4.1.5", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8", "@commitlint/cli": "^17.7.1", "@types/uglify-js": "^3.17.5", "standard-version": "^9.3.1", "@babel/preset-env": "^7.24.7", "copy-webpack-plugin": "^9.0.1", "eslint-plugin-import": "^2.28.1", "eslint-config-prettier": "^8.9.0", "@types/serialize-javascript": "^5.0.2", "@commitlint/config-conventional": "^17.7.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"shasum": "ff84f9dcdfe82a3de99a6bf448f8aa1501a24a05", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.13.tgz", "fileCount": 10, "integrity": "sha512-JG3pBixF6kx2o0Yfz2K6pqh72DpwTI08nooHd06tcj5WyIt5SsSiUYqRT+kemrGUNSuSzVhwfZ28aO8gogajNQ==", "signatures": [{"sig": "MEUCIEvNemwHa5VLk5cnXEuq1o/q76WHEDC6hIZQDideQAYRAiEA4a8zK8Swzd1sk7R1mLPLSJH4VWtg/KCtoDD1UhszMIc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89647}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"esbuild": {"optional": true}, "@swc/core": {"optional": true}, "uglify-js": {"optional": true}}, "funding": {"url": "https://opencollective.com/webpack", "type": "opencollective"}}, "5.3.14": {"name": "terser-webpack-plugin", "version": "5.3.14", "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "devDependencies": {"@babel/cli": "^7.24.7", "@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@swc/core": "^1.3.102", "@types/node": "^18.15.11", "@types/serialize-javascript": "^5.0.2", "@types/uglify-js": "^3.17.5", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^28.1.2", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.3", "cspell": "^6.31.2", "del": "^6.0.0", "del-cli": "^3.0.1", "esbuild": "^0.19.11", "eslint": "^7.32.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.28.1", "file-loader": "^6.2.0", "husky": "^7.0.2", "jest": "^27.5.1", "lint-staged": "^13.2.3", "memfs": "^3.4.13", "npm-run-all": "^4.1.5", "prettier": "^2.8.7", "standard-version": "^9.3.1", "typescript": "^4.9.5", "uglify-js": "^3.18.0", "webpack": "^5.92.1", "webpack-cli": "^4.10.0", "worker-loader": "^3.0.8"}, "peerDependencies": {"webpack": "^5.1.0"}, "dist": {"integrity": "sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==", "shasum": "9031d48e57ab27567f02ace85c7d690db66c3e06", "tarball": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz", "fileCount": 10, "unpackedSize": 89163, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCXfjQd2u0z7iiMONhcumVF4CsI+EZfgCQX0/xNE6R2MwIgXvz8vQUfQcgHqeSX2tTITO4wXrM5kExjbwXZ0yxkrZg="}]}, "engines": {"node": ">= 10.13.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "uglify-js": {"optional": true}, "esbuild": {"optional": true}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}}, "modified": "2025-03-06T13:21:21.525Z"}