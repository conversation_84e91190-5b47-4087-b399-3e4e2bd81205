{"_id": "request", "_rev": "2046-14407b2a003f70ad40e733b3c5f2a9fd", "name": "request", "dist-tags": {"latest": "2.88.2"}, "versions": {"0.9.0": {"name": "request", "version": "0.9.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@0.9.0", "bugs": {"web": "http://github.com/mikeal/node-utils/issues"}, "dist": {"shasum": "1049f59a6f46588e6d030921fbb84ca2f0c2714e", "tarball": "https://registry.npmjs.org/request/-/request-0.9.0.tgz", "integrity": "sha512-Dd5KRv1//FksoXzCLLaUqwTQ4VJt6CqG0pm+N/MuAj3tuxpr5m90GzbyA467Z7F6wiyPbtQAj6bTuVlpVyeY+g==", "signatures": [{"sig": "MEQCIFhriiezeTRk+oum/YFlhvhjQ+5Zjy7OFvPIEe6Hab8AAiAQC3eI3HZaU5jVE6iSjdYCGb599Mn4KsYRKyLc4HmQOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/node-utils.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Simplified HTTP request method.", "directories": {"lib": "lib"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.8.3": {"name": "request", "version": "0.8.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@0.8.3", "bugs": {"web": "http://github.com/mikeal/node-utils/issues"}, "dist": {"shasum": "03e3a382fb677a42eac0e7b0fa8a46b504944c08", "tarball": "https://registry.npmjs.org/request/-/request-0.8.3.tgz", "integrity": "sha512-O3BR9zo5Fp28jxCwSSjSitG+/I7hCHyNS0g7kDARk6Tlqpgk2iWdb0GU/Hhzs7fXiaMQxXIbdhWV1EOB8Cvxig==", "signatures": [{"sig": "MEUCIADfc17Rxif+pHQe4daY0ovxwrag7VcH1ZQdgrL+mKf6AiEA5mN+2czlWtc4z9QdYQ4wqnTjd00wMR+k8Latfh5KfxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/node-utils.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Simplified HTTP request method.", "directories": {"lib": "lib"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.10.0": {"name": "request", "version": "0.10.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@0.10.0", "bugs": {"web": "http://github.com/mikeal/node-utils/issues"}, "dist": {"shasum": "0910540d9b86c4f90b4c4b44c84025220a1f9d2e", "tarball": "https://registry.npmjs.org/request/-/request-0.10.0.tgz", "integrity": "sha512-Qn6cLFKm3532h7EOmaUHuONxPtefzpB5StKzR3HE+fkIu+SLHlVWHU8peg4wI9cV9BcX8AZZ7mdss4pcWtFMyA==", "signatures": [{"sig": "MEUCIQDtN8dPaaA+tJ+vcIm15O3UCLluoU6Eyx7yaKx2Xl9mwwIgY+J0qIHMdFRNn0GI+YNqfRuQaXgEsMrAzcbJYH1M27M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/node-utils.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Simplified HTTP request method.", "directories": {"lib": "lib"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.9.1": {"name": "request", "version": "0.9.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@0.9.1", "bugs": {"web": "http://github.com/mikeal/node-utils/issues"}, "dist": {"shasum": "a08a9a253f5b45447112e1ceb4d3087a1578682d", "tarball": "https://registry.npmjs.org/request/-/request-0.9.1.tgz", "integrity": "sha512-gwHvNdFs01N2kMz6GAlRlVGnMUIJpxYZI562nzjcAZwHwKugZc9hvY37N477zIvPTCHebwp7LlH4/H+bMfGwhQ==", "signatures": [{"sig": "MEYCIQC41wEAICmVMjLer2zgsMZJdPwdaLespbTsHsXgrvdBAAIhAMjFBIC/cxM+OH2UZ12MIRK1P5DKSjZlqrVkxqEuBN3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/node-utils.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Simplified HTTP request method.", "directories": {"lib": "lib"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "0.9.5": {"name": "request", "version": "0.9.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@0.9.5", "bugs": {"web": "http://github.com/mikeal/node-utils/issues"}, "dist": {"shasum": "66269143f6f5023f967159d5760369a606abfc0a", "tarball": "https://registry.npmjs.org/request/-/request-0.9.5.tgz", "integrity": "sha512-auJLUcOjMeeNN/Oaz8uaOFPD9kFNJV6sDuN8fZRZhxEYc6BIdhvtVU7s/zLs088jLkw+sX5Ea51v1J1vU7b1Ig==", "signatures": [{"sig": "MEUCIFqJz7xZtqUUh55EKVPIbR5v1mFN0fv6UtH6DsbS1sUXAiEA40zwFvx8S0xHQT17cUPf+pHHoNOYisHxJjApyUcPlIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lib/main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/node-utils.git", "type": "git"}, "_npmVersion": "0.2.7-2", "description": "Simplified HTTP request method.", "directories": {"lib": "lib"}, "_nodeVersion": "v0.3.1-pre", "_nodeSupported": true}, "1.0.0": {"name": "request", "version": "1.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.0.0", "bugs": {"url": "http://github.com/mikeal/node-utils/issues"}, "dist": {"shasum": "6e482329b10657461b51db9157f64a2046f635c0", "tarball": "https://registry.npmjs.org/request/-/request-1.0.0.tgz", "integrity": "sha512-9wvnlUf9ymOQdWtAyjezi3NpktX18kyHwxquDg9wgo+wsInxtvg/Y8tPXJaCNOYK9mp9Psxb8rrymwbaWkq6cw==", "signatures": [{"sig": "MEUCIQCYTDjB0z1mG8eYISgPDOoYl8i8T5Pj53Pkk8sW5XqHtAIgDAv6YgMsL/Ty7lA+m8P/kH6xREKjS4tig9JR3dbz/6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >=0.1.90"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/node-utils.git", "type": "git"}, "_npmVersion": "0.2.13-3", "description": "Simplified HTTP request method.", "directories": {"lib": "lib"}, "_nodeVersion": "v0.3.5-pre", "_engineSupported": true}, "1.1.0": {"name": "request", "version": "1.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.1.0", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "327162dd590a9b4bcb71d70cff1267ef10ff5101", "tarball": "https://registry.npmjs.org/request/-/request-1.1.0.tgz", "integrity": "sha512-vUugKnlvrgNiPeaJGXggWWXUPeU8z5v9uNFEAhpFbJTOZ73wM6ArUUNIYG2AfKk421LIfj6vM8ejJvKZwoVrnw==", "signatures": [{"sig": "MEUCIAagtmuuAoonE781AvmkoRCuh7yGnFb6QfOYc0beTD94AiEApmGYfDestSBF18XjgDoftjBBBn7qNeCrd5WPrSmV9Hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node < 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.2.15", "description": "Simplified HTTP request method.", "directories": {}, "_nodeVersion": "v0.3.7-pre", "_defaultsLoaded": true, "_engineSupported": false}, "1.1.1": {"name": "request", "version": "1.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.1.1", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "6d6581049397c51e1b0e98eca7bfd29c0c8a4f6f", "tarball": "https://registry.npmjs.org/request/-/request-1.1.1.tgz", "integrity": "sha512-rvsF+AZnEId1/3D0b8lGniBAgCYPBzxAeh8owo6dxA2Sw7FCJL08nPMUMWkpFg0V1rEQ2pOcNWBVnsFwRMEO2A==", "signatures": [{"sig": "MEYCIQDdsJK7v5a2pZV17bBrfo+OBFuVFIlUBhmcw+nX2laaiQIhAMVtSQGtLcl4PLtGAq3SF6mlIbicpSnHiODeS79QrULg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node < 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.2.15", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.3.7-pre", "_defaultsLoaded": true, "_engineSupported": false}, "1.2.0": {"name": "request", "version": "1.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.2.0", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "f4b74ad2edd68c777bd640ffc74737aa0ffe5bba", "tarball": "https://registry.npmjs.org/request/-/request-1.2.0.tgz", "integrity": "sha512-/NpGjjOoV1dj0dDlaDglP3LI+TudCMP4P54gXM7Qf/EV0rwykFTIWy415SJQlvCPmpszOebUXsYb2y1GUYpvaw==", "signatures": [{"sig": "MEYCIQCFqBw+vmHSImVVutsEM0MT6Lq3IB29McMJt/p/86GqFAIhALoAcTbW0ncc8c9WfucgpWhgSq5lclRlLGqPK/xgVqhr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node < 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.2.15", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.3.7-pre", "_defaultsLoaded": true, "_engineSupported": false}, "1.9.0": {"name": "request", "version": "1.9.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.0", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "67758b48da41f4240c6ed50d2bd8ced8157b4bb1", "tarball": "https://registry.npmjs.org/request/-/request-1.9.0.tgz", "integrity": "sha512-ILXT4Yedbdomi4NTtP02jixuybeo6t9xDeqidtqd3iklcLCyDaDWFAj/5rBA6PFUGSbp3bdQw1qdsqFzN/zsmg==", "signatures": [{"sig": "MEUCICMpVPC+MOa2YZefFNpixe9pKuhfYkJtBS8d9OhsL6aDAiEAxbTzxvtQjEnCNiEXUCQfaBoG8qbmGA4wAjnwZDiZCh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.2.15", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.0-pre", "_defaultsLoaded": true, "_engineSupported": true}, "1.9.1": {"name": "request", "version": "1.9.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.1", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "428e8283e87db620cd3303673e62ead2bd7451b8", "tarball": "https://registry.npmjs.org/request/-/request-1.9.1.tgz", "integrity": "sha512-HvL5e4Z4dzL/DhyBc+gGvsC13x/TblF0uB/N/dbB5k40DMSUII/NCCFICjyaxCyjjiNAlEmXfDwgIK6makviAA==", "signatures": [{"sig": "MEQCIBFfXBHUjiJJwg+ooTf7pkwB0ve+7CYE+L0PyuFaaxfsAiBLB7IP3OU7GfMvFsa2IW9y/VX9paRso+aZNEPkLs68sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.3.0-9", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "1.9.2": {"name": "request", "version": "1.9.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.2", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "b1b66f63c5250fc1fc7caeeec53fdfa0b2528c29", "tarball": "https://registry.npmjs.org/request/-/request-1.9.2.tgz", "integrity": "sha512-8wnHe30YNWM0IQ6NxAi1A1xCwZYb/Q3By1kSQl2NyIz7bAtnvNY5sOcCJ60UPlfHhIv0YjFwVt0ca5f6rPNZfA==", "signatures": [{"sig": "MEUCIQDJWXQ1/ABuvp6s7ia8nha5AKSav2HhjrCnz4aSDQ0bQQIgBJFzFQlxh1V1+uPlHswtB9+EXB7eGOVn5nViuaWxUA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.3.0-9", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "1.9.3": {"name": "request", "version": "1.9.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.3", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "7c4b88c1e36ef8f265f1487958c5a2042f13a624", "tarball": "https://registry.npmjs.org/request/-/request-1.9.3.tgz", "integrity": "sha512-cfTjGyvNOfOrrmXmXfmBSu/i3VsBtB536KnqlaleAylkc9/yOJLh5lc0l5ccGcX7PflwLsJfvzJyxqeiG0bfwA==", "signatures": [{"sig": "MEUCIQCf8v8g3NkkIXNpKP1aJiOHut+RmeD+XsCUqkEz9tbTYgIgYjPQyT2NDx5qq80OJhhR9QVLrIEjufOR9lKaOxtN134=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.3.0-9", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "1.9.5": {"name": "request", "version": "1.9.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.5", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "24084f6c377fc2f3368b53be0922bebe5d40afd6", "tarball": "https://registry.npmjs.org/request/-/request-1.9.5.tgz", "integrity": "sha512-4BAHj7n3+ddE1PQiIQKVCljNRzz6ayuoRBuzUBduURnlroz59yMtaAzbxdvB0ybTHznLPQxL7QqjgOqQaUl9UA==", "signatures": [{"sig": "MEUCICp3Kk+/tb2WsWI0tBFk2rQjldNca4aBxaTYeuedNjlxAiEArm1+/CvVF3kmB1rl0Wx0ArlOgUKZkE1GauCc7imzCPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "files": [""], "engines": ["node >= 0.3.6"], "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "0.3.0-9", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.2", "_defaultsLoaded": true, "_engineSupported": true}, "1.9.7": {"name": "request", "version": "1.9.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.7", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "efc79859e5386aeb3f211f63b0d0e549026c7133", "tarball": "https://registry.npmjs.org/request/-/request-1.9.7.tgz", "integrity": "sha512-75fUnRqxfHT0h81t5hsvquHv+FU/6LaFkXZN/w0BaXp9gDx0QhKPo6+/6tuqhJU5eoP5yNWqJVFEoZEnzlI4mw==", "signatures": [{"sig": "MEYCIQCzaPtrSTz2aKLsyMAbNkwmgRL9q0yoj9gpKAQjMBrd9QIhAMOrqzoUMyvsK1a1v4jlkwU0x4QDwcpPHevUc7DMLzf8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.9-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.9.8": {"name": "request", "version": "1.9.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.8", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "2da5138da2851fd5bdb50500aa7eb9e2df2a9c2b", "tarball": "https://registry.npmjs.org/request/-/request-1.9.8.tgz", "integrity": "sha512-55OXT+zfcXPr2IgPINYbBDnBLwUzxYu3K4SIE+BCWDXUB67lmTfc9iXSC6tuGnCCTfAKmdJQoCEQ80u7IunV6Q==", "signatures": [{"sig": "MEQCIFXitrXI3paW27MqvHas35V5rP0tbsQGyOiirlgc+BzJAiBzwTvIqXqnBq0FigcGaHLkuk0lba1yzmc8sih/WN66Mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.9-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.9.9": {"name": "request", "version": "1.9.9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@1.9.9", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "af231fe87e3725a5db46ff74833f3aa0ebfd9151", "tarball": "https://registry.npmjs.org/request/-/request-1.9.9.tgz", "integrity": "sha512-YVWSKYdBolxLllo8GVK24jnf0ck+P4XeBEkFPz5jdVZzZ2L8jXzDJPaZ+DrIrc89S1c4fDBgu3hsluxe4X9nqQ==", "signatures": [{"sig": "MEYCIQDAiWLvMAY+vRsLTRBS69QN5QK3YooLg18YabMhfLLlcwIhAM2/Tr+X6fxOvoFnt5Eq8lsUB6iN+Xgyh5eWHI+Yj3O3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.2-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.0": {"name": "request", "version": "2.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.0.0", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "e8ecdcb12ded03b795aa1de0f44afddbab98f3db", "tarball": "https://registry.npmjs.org/request/-/request-2.0.0.tgz", "integrity": "sha512-/YiTJ2FZtvOWFhRE+HHiq3GxpX4e1RMEnqYYQM/0xhQRzQ/dgdHrfK+WB4uRW3OWrcTjWA6uE8GSMhwbm7dLLw==", "signatures": [{"sig": "MEYCIQDb+YQ49NJ/z/lGlhI6s9ha3fZweZR2pCAcGDOsFGbqPgIhAI51QKQNS7DCAb0dSkI1jNelUhlLAulgStdNDZGzPxou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.2-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.1": {"name": "request", "version": "2.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.0.1", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "2188893b17b9b0c761d5e53ee0cb76a50e720d89", "tarball": "https://registry.npmjs.org/request/-/request-2.0.1.tgz", "integrity": "sha512-V0rD2kZTDmntx2LoimfFj9ba1o/jyCpk3JDiPPxM0aO06V59Tg3Dl9ivohXUTYN+wAiGxWVBsrJd5Ft3qIYiPA==", "signatures": [{"sig": "MEQCIAMvMkomcdbYEpJ6RLzcmbCE4l4DtbAplV1Bmk7Jsw3ZAiBAYoxl4C1MtJsXbPkz8JIO6HuMdf03EDE3QXMNUOKVkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.2-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.2": {"name": "request", "version": "2.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.0.2", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "8fb7985ed54581bb152e8c1c5649ec0f5a10d9b1", "tarball": "https://registry.npmjs.org/request/-/request-2.0.2.tgz", "integrity": "sha512-AhMSUZ6HVXYjlzOVzZ39ZGq4ZDrWRccVZ5EefKVgLV1FAnxujoac3Y6lunsjJ+n7OhyF7RQSVFWTa/MQsywtVA==", "signatures": [{"sig": "MEYCIQDbMKrrk1dakFBlU8cNBQmP4AXAj6sdM+Y26TwL9DUe7gIhAIXXLUp5G1NinH+AAqnFMk8JHlT96h3NqciBXuP9sJC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.2-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.3": {"name": "request", "version": "2.0.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.0.3", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "ec8d338be3de528ca16a5307d94bbd06298a80cc", "tarball": "https://registry.npmjs.org/request/-/request-2.0.3.tgz", "integrity": "sha512-J/qwIsTyygco2DKCy0oLVr4MbdC6qr8J8T1hmg9U6dM5sbHZEC0s9zd7HPp4FXG1d+4q2Z1jMaxI3FJd9DfgjQ==", "signatures": [{"sig": "MEQCICbVM5RdAHiCQxtITMM7wL6jh3XqQ8DBbBsFPIudWDEBAiAM+Aw2LB0dSCe6lKQcxUpATM2fcWjq1iulgvC3qVRYPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.4-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.4": {"name": "request", "version": "2.0.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.0.4", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "7baa221cef8923c65ba74479f7963f1214b02161", "tarball": "https://registry.npmjs.org/request/-/request-2.0.4.tgz", "integrity": "sha512-X3fiob5cD/E6gwqkuz8vPaIA7RDF6eMFIYjhWhi2qw+9bxrKzoRpY4BCohfCmlR8JTq1UH8Wv1Y46DbQt6tqeg==", "signatures": [{"sig": "MEQCIBi62QKY7QfgH0c2CH68+qQSoPwzDBf7ifJ4iKKdF91kAiBRau6n/LXL9Nyu9GcTTZkBm1kEhRA3KMLX+lohUDGi/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.4-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.0.5": {"name": "request", "version": "2.0.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.0.5", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "a3173897f02aa2f64a7cb5f06b6b931162edaa38", "tarball": "https://registry.npmjs.org/request/-/request-2.0.5.tgz", "integrity": "sha512-Gev9ZlNZTB0sDZsDKNyomovQ39u8Uq7jRN5XtGT+fFv/K1TD72hnauTLMELV+PYOvv418SmAa2Rk8JXiWfb2aQ==", "signatures": [{"sig": "MEQCIH8z1uRdTSm91hBuelavqrd1oMpRK7mP668A9tMJBeY0AiAWMXNFRtYQk3yUZPeH7brZDuhd+2Rm3g7xs9aiBlHzOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.4-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.1.0": {"name": "request", "version": "2.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.1.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "8280cbeaadeee1190e4587b0ad9dea48d8a9e1a3", "tarball": "https://registry.npmjs.org/request/-/request-2.1.0.tgz", "integrity": "sha512-0H0CNaLPz95ncl5+Q5Q6ngflcqnC8J4Gmvnirl/V3/cC1CqBL4BgK/PjsFCU7ul3CvoCQAHtw7j1vB3Y95QX4Q==", "signatures": [{"sig": "MEYCIQD7zQ+somsKE/3Zgeu9Com4OY1Ez65uSeUb2lp+DHRC8AIhAOh1l4iKJEo4+AYlsrGRIcWkghBFpQrhZRnxibKA17Xo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.5.4-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.1.1": {"name": "request", "version": "2.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.1.1", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "5570e0c737d656ebd6d1d3c80aa510d3cf86c6fa", "tarball": "https://registry.npmjs.org/request/-/request-2.1.1.tgz", "integrity": "sha512-FMF+25XbxxgyPn+/twIEJeupQuu5LqifPjeNigL3hf0UQUqZHKMbgbRuUKNudxxEHNqJhSXIekZSEiikc/ApVw==", "signatures": [{"sig": "MEUCIQD3bqpIG0shXdrByhxod+tN5AjciLyLwjqPYx3tttFFeAIgU4z+vK+PQOmOTLzw5FBbZ4vkr3wc38RcAyePxiYfuIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/request/2.1.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.2.0": {"name": "request", "version": "2.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.2.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "cfa3210cd64b76a4d26b8f0cd1ec2d958d2cddc0", "tarball": "https://registry.npmjs.org/request/-/request-2.2.0.tgz", "integrity": "sha512-TgMm9MKxiseS2busbpx4aGN4WTS3y9b3rmg48v4ER5IxcjcsiDE6vUvIKMbWkdJVosczKQk+IBTx5hyByJ8Z6g==", "signatures": [{"sig": "MEUCIDuCEZQXir68YXo4MDZSBI4+wP5IhnuU2N7wNKJ9MkbUAiEA28CZexmW53jGS59kG4jHyAdwHSffGxbF1aAiVYfrtbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/request/2.2.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.2.5": {"name": "request", "version": "2.2.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.2.5", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "da3edd6a5b903563aea1531a6cb61cfe381820b4", "tarball": "https://registry.npmjs.org/request/-/request-2.2.5.tgz", "integrity": "sha512-lXR+YV2XUATijcnoR9OgJ5fkP+aQNyhmjxKRONMbof7VuGeY9ngUXYNuI9+1Y6GirrgpVYrajeYz6PJml6CBBA==", "signatures": [{"sig": "MEYCIQCwreT0jeD0VdYmSvEHp4ZpClA7Owu+627W+PEHp6eLZgIhAJsKk11wB6mThsalkKJthpNs7R6o7O4qqn/kj0Gra8Iq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/request/2.2.5/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.2.6": {"name": "request", "version": "2.2.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.2.6", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "87dbd87959d1b85ce07253f9255878f20473d99e", "tarball": "https://registry.npmjs.org/request/-/request-2.2.6.tgz", "integrity": "sha512-kL3UIfgvH1t3GKkOcepYtcgztDE44GF1VH9cQ5d4CfoBI2WDtHf4OznkRYyFeucyRunrapP7XwXqwdv115w4DA==", "signatures": [{"sig": "MEYCIQCI33LO019wiTyojrl+6J8aknBQQBUAXPvDI5k6wfO19wIhAJHYTvhwfFA7MMTwRNdAX7x621eOh59fDflQNV1Gk6VE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/request/2.2.6/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.2.9": {"name": "request", "version": "2.2.9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.2.9", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "efbf8afbfe7f1e200d483b99752b6c42b404a0f1", "tarball": "https://registry.npmjs.org/request/-/request-2.2.9.tgz", "integrity": "sha512-ghe0epdhsogU/laqPTcWw+bS/fJ1cW6IDFXhDCr3VV42GDyMJdW8c2l+taYxLu+vuAHGh043IpypZIq/3jqEyQ==", "signatures": [{"sig": "MEUCIQDlk3SKXXcH3HiqyqO6BEnyyBQv6rGPsdzh6KBVTwB1sgIgbvF37+m416Q7MZac/soG9HgJEt3U4eBZqsvWV0+3NhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/request/2.2.9/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.0": {"name": "request", "version": "2.9.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "f0a672cd33f4534f9ce811ede17e3fbc18ccaafd", "tarball": "https://registry.npmjs.org/request/-/request-2.9.0.tgz", "integrity": "sha512-RLf0TD5ipXfarVOwCDgNGDbYmidMU1F8gtO69TbS0gMb4xigB/lA5Yl73xt/Cx7UTNOF7RZtaml1KqIIcX3wOg==", "signatures": [{"sig": "MEQCIFp4ygq96H5eGrmLuC69+9RNRZRZmtU/Gp1Nm8sizY+EAiBuVeZMs8fbHqaCCvZSmZpfmDrt0L6xW6JTDhtZr8nPuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.1": {"name": "request", "version": "2.9.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.1", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "54c62ebbe608fd7202c22345c45c044ba4115b1c", "tarball": "https://registry.npmjs.org/request/-/request-2.9.1.tgz", "integrity": "sha512-lwgpRo/oVvj9iykBqgL8LDbndb/87THxn6RdB61ZEiNjPshoIw5ns7TCrgotGvgemLkU4cdgkEGutUPaiIIxRg==", "signatures": [{"sig": "MEUCIFFedaIuoKW8RJdarh0bkGIrM81usSl9XSOAox/DxW9MAiEAsLQCa5M1RsMm5A/rNjqOTDjMTyauLOdAcTP862emNSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.2": {"name": "request", "version": "2.9.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "5f1c4e6c5ad085a713f83110c3d43b29a1603c23", "tarball": "https://registry.npmjs.org/request/-/request-2.9.2.tgz", "integrity": "sha512-T+fGfW3qsWj7Os6KKiTjUkxL3x1UafK+ClsPfb8NoYnKUlvNKY5nux0bVnu7gttXYI9ak4FZTIznkM2NDjRj9g==", "signatures": [{"sig": "MEYCIQDQOHaN490u4ybkX0yxgMxbR2yMblBhMHuJpzxk8ghhDgIhAJiNiCjJcvChgGXIECD3pK7tWMN70O6UNTyUQfXpNFyX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.3": {"name": "request", "version": "2.9.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.3", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "ac0ac26169568837d2886a92b22c0bd1e15e7ad7", "tarball": "https://registry.npmjs.org/request/-/request-2.9.3.tgz", "integrity": "sha512-SyyCqkxodKeOkoQqbLfLnwSGmVP/HXI3/CedADYEzH0Txml8P1p2XNyhfCJR9ejk8yqPMftdeYXH9+Oi0liX6Q==", "signatures": [{"sig": "MEYCIQDXJVnBIWVzaAytikFi25EQAnIH2RTknHX1dXo9MAQ0mgIhAOKxovtFRwXomwQP211Ql1vKfEHHAWW7TJKHtBaTfPRx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.4.13-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.100": {"name": "request", "version": "2.9.100", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.100", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "c9900a6cc8576b8400a82161a6268962f3d09b78", "tarball": "https://registry.npmjs.org/request/-/request-2.9.100.tgz", "integrity": "sha512-HnJn5hieQn1hoeVErtCS/Uh+CIv5KHKUEe1AYtNemWlgGk4LQKKwpZaB8drSE/edvkI9Zig89GNf7ftqL9x/NQ==", "signatures": [{"sig": "MEUCICaAGOc5qNsnZG1ZnTPxsBrZO2Ka+OoMCNpXqtMPUqxGAiEAogmarr+/uIUHjsPDzzS3POeDw/du/N80w9A8pBQCwA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "bash tests/run.sh"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.150": {"name": "request", "version": "2.9.150", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.150", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "a19e4ab54ee27274fe6c55b10fdc86ff413123b7", "tarball": "https://registry.npmjs.org/request/-/request-2.9.150.tgz", "integrity": "sha512-sRYFEgv3SUjqEI4altehjwv5OkS9T+BTgX2T9gshnKur6FVBZJtXPwS5mPHBHv5rxcwNpBsTRSBtRnIf/8x8rg==", "signatures": [{"sig": "MEYCIQCsINbMY8QLffG9jOFcP0TU3ElwG2VjCyDQindHaH0EmQIhAO2ETkYX4E7z9if9/n5EzHhSoLJuU8zl7LcyhKTOfEQP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.151": {"name": "request", "version": "2.9.151", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.151", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "caa84501f05e8b9a32c22b8caead5bce3b41ea87", "tarball": "https://registry.npmjs.org/request/-/request-2.9.151.tgz", "integrity": "sha512-zq4v14V9mzQpkQT/Q7yXytgmvOovYTxT55UUgSuQJVYxNJJb4rqS771r3hXIdIWd28Ic/BTZpeb4F6Uv50Vniw==", "signatures": [{"sig": "MEQCIDFdeupFqZHxu/mqk3WwFfejVgl+m4h3xmbIuCcB3ljjAiBUOC8m+08VXySSUa3RuQPMKg+1Mxo4+EpGCVs12a7Dcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.152": {"name": "request", "version": "2.9.152", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.152", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "100cffcd9a012556fedcc916e647a9cd1e2bbf43", "tarball": "https://registry.npmjs.org/request/-/request-2.9.152.tgz", "integrity": "sha512-2ot/OsmzpgPoEV99RemyM0Nd4Ghuwy975ws7gwrSDHlhaQRpz6FM+W3xBDywx0174Yc7l44bP7H1i7jRfHWd4g==", "signatures": [{"sig": "MEUCIFwnGsD2SulZ/q53DE7tRv1coFspBxij0oaTh6CyBc7XAiEAoxb0mBiq4lErLtOy6ey2IPpsulA47xkpJhUxF67GzAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.153": {"name": "request", "version": "2.9.153", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.153", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "a93c4f7a02d733b56882a166c1e68a9688e75742", "tarball": "https://registry.npmjs.org/request/-/request-2.9.153.tgz", "integrity": "sha512-VJilcCb99AhBauo6fVH3yNfimSqEw80JcZvRkzCKtsPbBzuPtbqMsPsKqJbD+wDUvGsZPYvgkFbOBbPv3ZclFA==", "signatures": [{"sig": "MEUCIQDN9JHO9rk3lVFRDNLP78Yen7ByTEkwYnHKJ/nkL5FnagIgRBeg5HHb2I2YCFOAMFfr9qi0WJ338S3dG23iBfldwHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.0-beta-10", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "2.9.200": {"name": "request", "version": "2.9.200", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.200", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "ccb8b3407c34fe545522aca40b77f3194978b0c6", "tarball": "https://registry.npmjs.org/request/-/request-2.9.200.tgz", "integrity": "sha512-M90+ctNWZSNr1V5x8FqGFYmjTI0+NICVxOVS1y7pqi4OuXZzryD5agPlMG8fob5tDn+k3lUQW/Aoe/leNxSeeQ==", "signatures": [{"sig": "MEQCIFad5RBE3/IPKUxg/qhEdKfhIHRiYpDPGmhXbI1wV0IxAiAldqlgQrJbCwJKPO/BPVwwAAPEKi3XZHmnrTsrmktndw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.4", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "2.9.201": {"name": "request", "version": "2.9.201", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.201", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "65277189f7b2348aa90a1f8fe639436b6ad339fc", "tarball": "https://registry.npmjs.org/request/-/request-2.9.201.tgz", "integrity": "sha512-EH62WoOo6NHAtMDz5ntUYszD4+C+s4HmPhGJ23gQW3JBa6D7ZY5zgi+qPCqe4Zh+IeajcELA70bpTSKwPaLWbA==", "signatures": [{"sig": "MEUCIQCianLf+ifHh2xu9CAt+dgmHKD55e6ha3OFLZNXaPNA0gIgCpe8J0CeLigSJwxFaOpT+ElcuugedCKkUL2Gjp1OV+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "2.9.202": {"name": "request", "version": "2.9.202", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.202", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "c61399cdfbbefda5dc48653b0e9bea517d9f8497", "tarball": "https://registry.npmjs.org/request/-/request-2.9.202.tgz", "integrity": "sha512-3tc+z/Mcu5Ux4WCZ2dh3F/h7EghvlTQNr0OeplFt09gOCbSZ8MZpHwWIjX/s6I8CsineNvUBGG4/QUNLSC9nKg==", "signatures": [{"sig": "MEQCIBSj9NqOxkxVTFpMBKK0K6G39lqMRCtd4q4rJ6Z/jzu0AiB6Amt4Y62dM5Ypdl79dLUqjkZmmpR/hbljKg7ZUgMNyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "2.9.203": {"name": "request", "version": "2.9.203", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.9.203", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "6c1711a5407fb94a114219563e44145bcbf4723a", "tarball": "https://registry.npmjs.org/request/-/request-2.9.203.tgz", "integrity": "sha512-OWtna9w7yRI/gcfu3VaURgIwE1FHgbz5+fHGQ9GJTHcJ4+uvHnDjXd+N7mVDOv5+1fp1CRPzUSY2wcM345Z2Fw==", "signatures": [{"sig": "MEQCID0jsr4XHE+cRN40u1T3x/71iFSFiB65FhmcwVOelW15AiBjVDSuWElS4oRael7Se3z+Y8qTwRK5lQKt7j2Us8w9bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.6.20-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "2.10.0": {"name": "request", "version": "2.10.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.10.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "9911b5ef669b6500da2ae0b133fa1cfc92b1c48a", "tarball": "https://registry.npmjs.org/request/-/request-2.10.0.tgz", "integrity": "sha512-m/AqwBGi9jC0WLSobeVZmh6kxs31zRUVSvmOCUeLUJ129Lx+yfh7jnidiaJzuvtilMsHz5YeJ7W4+v2kq4Ud3g==", "signatures": [{"sig": "MEUCIB9pgTqm5Ou5TF4bLKtdTLfSJHkAmRWpMwNa9PBATtUjAiEAtoTNV6bkwVY932T9KCCiH9wtkAhMpXaBYFi50vA3yK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "2.11.0": {"name": "request", "version": "2.11.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.11.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "7d0fd1b5a9e19781c63b2eb0c3ac119dc6a97130", "tarball": "https://registry.npmjs.org/request/-/request-2.11.0.tgz", "integrity": "sha512-OQbcnNfUlm2O4Zvi0s/DEMOqBPYTbwCzrBSC8rRExQjX4YdBetqCIrUE95QSAVKw+QpJzjZ8ecnvQ1WVrqyzfw==", "signatures": [{"sig": "MEUCIQC06cA2XrCGHRWAnQmMFRSedOGXQ1FElzZ/7qluWBJlxwIgVb3bozOaO/j4WCH0U+KDvUq9PBELxb5wIp9ZEXaX9aI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.11.1": {"name": "request", "version": "2.11.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.11.1", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "257f46bcaf4d54e62c0b61ba28fac0268f658e2c", "tarball": "https://registry.npmjs.org/request/-/request-2.11.1.tgz", "integrity": "sha512-usK6DfshUDNc0DQZms016mQ3UFwxMQKr2hMrlfwLnHmscv96gz2DIRXj7eCgNUAMXQmAzmm7g84o3YM0+xEgFw==", "signatures": [{"sig": "MEUCIF8cqr/YxFJPrO3BycEfmffNP+l09jd6LsZULO6lgDTqAiEA7k9sTN/LeQGmAV5ESkfZjDvV7Fhxv3t/+GsqgzccWhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.11.2": {"name": "request", "version": "2.11.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.11.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "070340fc7628b2a8c23b8c11395854f2f546ab34", "tarball": "https://registry.npmjs.org/request/-/request-2.11.2.tgz", "integrity": "sha512-HQgcZY21Z9dMGwjVvOE1WwVzLa8WTNZOij2iM8SQQ/F/cpj3IjTWlxP9e+oemzK5dXTePDj0Mg40VEiDsFU+sQ==", "signatures": [{"sig": "MEYCIQD2F/MlVuT+4id54+MWRcMcZabpK4h6FiSoIA4ygkRr/QIhAIZ9PX8i7zcutofNsmJA7v+PeXzQ9xSg2Nv6gvzR+Fym", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.11.3": {"name": "request", "version": "2.11.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.11.3", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "b44425b540d65a0b2563014a28cea0dcb1e8d659", "tarball": "https://registry.npmjs.org/request/-/request-2.11.3.tgz", "integrity": "sha512-pfWT+DRPzJzhe6C/NLODNAadJp3NuIyz/MkUorafJ/OasRhKglpfg5EXV4mSRn+GIblMHyNqhWQi8ZhlcwLV4A==", "signatures": [{"sig": "MEUCIQCum8TxUYiFbbfP5GEMYFEoT0SYW0uzweGchQvBZ4NKPAIgPwm8QVTYHgxkBVG1d4cdKCkWy5wmgvR88ZqZjvj/81I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.11.4": {"name": "request", "version": "2.11.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.11.4", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "6347d7d44e52dc588108cc1ce5cee975fc8926de", "tarball": "https://registry.npmjs.org/request/-/request-2.11.4.tgz", "integrity": "sha512-lL+DgCpVn4b8scEhyGGel3kAipjMyHHfIEcP1SCbdJq9jCuaJLSBELJ7pel5RhK3oXl/xTI9HecR4NnHSV+Hrg==", "signatures": [{"sig": "MEYCIQCTGn2vZjfIiKaysu7DdLFh0WPqpvnpJQumzg1lNsS9pwIhAKYuPYSc27o5fuEi50IfFZlikNZn8HoXn45vxCodF2CE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.12.0": {"name": "request", "version": "2.12.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.12.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "11f46f20b3d0f4848c6383991c80790af16c8e48", "tarball": "https://registry.npmjs.org/request/-/request-2.12.0.tgz", "integrity": "sha512-GcOI9NyUw2lRke4kdbrjQFxu+cLugShENHvcb9QCSLLc/yH3lP7I/7RESxVbYS+AxC/r9rwzks52MRHVoZdb5w==", "signatures": [{"sig": "MEYCIQDrY1QGl7k6ekTHX05QSVn3IV1MCBxBkRqAHIJ9Kb0x2wIhAPLn8rqJcilFmBoXaYCv8LhfB9kpZDP9TX43binB2JjA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.14.0": {"name": "request", "version": "2.14.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.14.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "0d8acbb0b14c1ab82e000b7d381fa8c80d1a7d88", "tarball": "https://registry.npmjs.org/request/-/request-2.14.0.tgz", "integrity": "sha512-L8hGGkSLQf2e0V5BJDX0tpKBQU1dchI2cXZj8IgahCnUwZeLoUB/z5/CcBD0jPB84rV3EC4DHTYfPsEU94JZ1g==", "signatures": [{"sig": "MEUCIE3k3bExGlJ6VDfW69T8F1/v2V3jkso2esYtOh6IsiNcAiEAlXEZJ28BWuQQd9/f2KBHM+KvLuP++U2mamNk7b60KlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./main", "tags": ["http", "simple", "util", "utility"], "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "v0.8.1", "dependencies": {"mime": "~1.2.7", "form-data": "~0.0.3"}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "bundleDependencies": ["form-data", "mime"], "optionalDependencies": {}}, "2.16.0": {"name": "request", "version": "2.16.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.16.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "4e428a90da323c2a23deaa10aa729efb71da60eb", "tarball": "https://registry.npmjs.org/request/-/request-2.16.0.tgz", "integrity": "sha512-GewfOBIN4N0UJ0+tgX7ODjl5LHKlLHSkZMozzUkQrrwy0wd175R7fLWMhBlXA62L41gg0osf1/Wb9Rbde5pIsQ==", "signatures": [{"sig": "MEUCIQCnr4Bs9f1CUNtQmi2GQXIw5TgNlbCpZFBv9tfwq99EygIgSi/hfLEkniOxap0bJZW1r5eSIo337hJKEw2JWVgKLNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.0", "hawk": "~0.10.0", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}}, "2.16.2": {"name": "request", "version": "2.16.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.16.2", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "83a028be61be4a05163e7e2e7a4b40e35df1bcb9", "tarball": "https://registry.npmjs.org/request/-/request-2.16.2.tgz", "integrity": "sha512-vL1C/7tZxSk7uWdUkjGgkPs37Z/0BrKx2kXfvHATlNrUZdAwz29gvhblnnTH7MUzsyduLENWNfmwJ6H/TlbQig==", "signatures": [{"sig": "MEUCIG29w6IYxGu/jWO23fq1kiHWgtyq58BfHF+8AkWheNJ/AiEAqXq2eNq54QUhQLliphcXYJe0uvR4znjB73kCEu7gy8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.3.6"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.0", "hawk": "~0.10.0", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}}, "2.16.4": {"name": "request", "version": "2.16.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.16.4", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "9bbb264fad62cf12bd3e03ef83d7c728c4f78ff3", "tarball": "https://registry.npmjs.org/request/-/request-2.16.4.tgz", "integrity": "sha512-9SQLWcwSr/ttI+pNmUrWGATkB26HToB2+H5LGsbBKnm6BeBpRXrirfh0GaG1yWW8sQzOkjQMxuyyVu3Aa9ZCIg==", "signatures": [{"sig": "MEUCIQDe0TMfCgI40ET4YNsZNVTA14oC0aZDIrxXIuO5js40hQIgN29eb8sUFZGJrC/TBWqWoyV66cnQlb4C0mQITyhIuks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}}, "2.16.6": {"name": "request", "version": "2.16.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.16.6", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "872fe445ae72de266b37879d6ad7dc948fa01cad", "tarball": "https://registry.npmjs.org/request/-/request-2.16.6.tgz", "integrity": "sha512-TfD4kMo40kwuOpO7GYfAZpb2wYdw7yvTIglPNgPPSmp2Fz6MKNvPLla40FQ/ypdhy6B2jRNz3VlCjPD6mnzsmA==", "signatures": [{"sig": "MEQCIBraxEQt4tsT3/Pp20Y/tRhVsWp79G5sudGqxJCvPtgPAiBtvpsi33UVDQRYC0lNe83awIwADhv0f0M2SCKppRXl7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.2.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.2.0", "oauth-sign": "~0.2.0", "tunnel-agent": "~0.2.0", "forever-agent": "~0.2.0", "json-stringify-safe": "~3.0.0"}}, "2.18.0": {"name": "request", "version": "2.18.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.18.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "8abf1ae86ba12553832984dab7d2a46880025fc5", "tarball": "https://registry.npmjs.org/request/-/request-2.18.0.tgz", "integrity": "sha512-oCWztPHBEBx1oTA8N5sFSjF6ABOPDtL/eUpoKu7J2fNGO4OlvTyoJK7LRQe5JJztt8pOz7W1KMKK5ntDZgH9Ig==", "signatures": [{"sig": "MEYCIQD/Jk//16E5VDvqaQazO3CmLs9apz8bOnHvvnLOVRzL+AIhAIOsyxRmkspg2LoRn2WlUaGSIU7AwVM9u9ufMi+79WGR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.3.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.5.0", "forever-agent": "~0.3.0", "http-signature": "~0.9.1", "json-stringify-safe": "~3.0.0"}}, "2.19.0": {"name": "request", "version": "2.19.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.19.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "d7b614fd6248b4a0365a7fb7b8e1cdd081d08025", "tarball": "https://registry.npmjs.org/request/-/request-2.19.0.tgz", "integrity": "sha512-AXvYdkxQsyicPEAO0U3pZR654aSmCCNaCI0z4F4NEBwPQXSs4A8w74eJdORusik9rzlzgceMPs3QHCIrsgk+wQ==", "signatures": [{"sig": "MEQCIATwNNOZXX7dBfFfATZTuLPkXCGw10vEdH+thY3Ri76mAiBPTc5pYjS+IxAbiMq/Z7rcflqFToPQsn4CwLEefxJo/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.3.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.3.0", "http-signature": "~0.9.1", "json-stringify-safe": "~3.0.0"}}, "2.20.0": {"name": "request", "version": "2.20.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.20.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "497c9bc869f03c34f6cabaac962224d2d835d47d", "tarball": "https://registry.npmjs.org/request/-/request-2.20.0.tgz", "integrity": "sha512-xw4akmDNVV4TiYiz3zPKhWyos+7WtCNi/v+8dHsjJ2flMx6aM0rjWPgA0m2UuqXNS3aa4wvFtyubMLX5JRVKag==", "signatures": [{"sig": "MEYCIQCbXZ8UmsKWucM5hfxamwMmDVCBMRl/5J8xNK2H8ABQXQIhAK6YN9D/8Oo7+NKDv0/Ct+cLudoiSQjJ8vaN2VB8xb0K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.5.4", "hawk": "~0.10.2", "mime": "~1.2.7", "aws-sign": "~0.3.0", "form-data": "~0.0.3", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.3.0", "http-signature": "~0.9.1", "json-stringify-safe": "~3.0.0"}}, "2.21.0": {"name": "request", "version": "2.21.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.21.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "5728ab9c45e5a87c99daccd530298b6673a868d7", "tarball": "https://registry.npmjs.org/request/-/request-2.21.0.tgz", "integrity": "sha512-jvDa6FC46ystc0cn+EqtJ4B32SSz/cMX7fEIv0UHX4wsYAKJYXjA5EyAMWpRQ+hWCnX9jPD1b4o7xZ/r1Tyx/Q==", "signatures": [{"sig": "MEQCIFxQJqyMfHBSgH7PBZzLFy5CUNJJ6MCFdIJhRMn3TPWmAiBXW2qd4RpYl7pnsn17UwxWqVavlSCvxvAh3VyP4RideQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~0.13.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "0.0.8", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.9.11", "json-stringify-safe": "~4.0.0"}}, "2.22.0": {"name": "request", "version": "2.22.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.22.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "b883a769cc4a909571eb5004b344c43cf7e51592", "tarball": "https://registry.npmjs.org/request/-/request-2.22.0.tgz", "integrity": "sha512-s05oCBjWuzNi/UbZtvwOnSJ85/lHUdYPriJyFUwdxHKr8VcZHB0wx0eTX8y5hCH3p7OTDi9iQUqMFyDkW6K7EQ==", "signatures": [{"sig": "MEYCIQDRMjGtTiO84KvhsMz0t8BURDlwH7dbUJ0FWFOUjNixJwIhAIQZh/UfVCXrjM52II4kN27bd2gQ/DOUWdg35Jg2xSdL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.2.18", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~0.13.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "0.0.8", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~4.0.0"}}, "2.23.0": {"name": "request", "version": "2.23.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.23.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "121742874bf40b3c149fe113b7a35847597fc885", "tarball": "https://registry.npmjs.org/request/-/request-2.23.0.tgz", "integrity": "sha512-OzY/QLUcGhIkdSyuoKBoVZKkijjJsUp0nCXavCxAG6A5nkDEvnhOi1sFcn78somoGwpV9a3RT1RAnhLtUHdGCQ==", "signatures": [{"sig": "MEUCIDPmS36DhHINtxXYFwtMYd79IC/ihdW2ItERpkqTti/ZAiEAokwR0Op6XA02iCwOamcReOHNFdS4JP9Ej618wwFEhvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~0.13.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "0.0.8", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~4.0.0"}}, "2.24.0": {"name": "request", "version": "2.24.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.24.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "1d9f104118a0389aac5dc873c713869efceed8ef", "tarball": "https://registry.npmjs.org/request/-/request-2.24.0.tgz", "integrity": "sha512-lRJnw74Nlkq6KhfY8BtIGEmIIszdSJGj4R/SqRuNdB7IQtVofY01XllLD6wbbWFwHidvEa8DqC1R276GO8zGgQ==", "signatures": [{"sig": "MEQCIQD0lItplTxRfzs5UAxB0u9yps8UbuNf+1Q7sK1BUmrRAAIfaiJR5mczFeyv4Q36mydTzgY8oW54PyWV25x2ebMKMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}}, "2.25.0": {"name": "request", "version": "2.25.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.25.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "dac1673181887fe0b2ce6bd7e12f46d554a02ce9", "tarball": "https://registry.npmjs.org/request/-/request-2.25.0.tgz", "integrity": "sha512-HgRVnAwpnaDMWux0KoVJot83Ixz82QllrnkOgw7EzpOAdIrkp7NlkIL3kGUWNG0s3SP1E1vqYJd/dGgTrifgVw==", "signatures": [{"sig": "MEUCIB9nu/ZgjuIBMChACbI5bPF2pY9qsKkfu7FfbbT3Q5kNAiEAryDXTNKKLCrn1IcbNqcKbE2+kzTiknL3uwRX+PF6lEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}}, "2.26.0": {"name": "request", "version": "2.26.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.26.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "79b03075cbac2e22ebe41aa7fca884e869c1c212", "tarball": "https://registry.npmjs.org/request/-/request-2.26.0.tgz", "integrity": "sha512-4+OESo492n2ZbGv7z265lj+U1/fXK5nYaJhKVNHqXNpxwZtrovDk8j459IKU48SlGeAEy0o9zjlPura5+xY4ow==", "signatures": [{"sig": "MEUCICdwO8bx0uE1y/2J5bS85wMmllanr1BQdJ841cFkM4sgAiEA49rnAdY5Khharicnqcp3flHtrR/ezgetcuCIOL+IdJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}}, "2.27.0": {"name": "request", "version": "2.27.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.27.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "dfb1a224dd3a5a9bade4337012503d710e538668", "tarball": "https://registry.npmjs.org/request/-/request-2.27.0.tgz", "integrity": "sha512-V4AYOEmdUrf0X+CVF2hndyMzIeQ8G7LB45HER/rXZYEwNVI3QFGgLPLafQLHjqtG/ggzHEMb66Ng5IemksixsQ==", "signatures": [{"sig": "MEQCIA7YLpmZDZeMpK6kue6vI6cNTRG1OEJs1LChD77V9+rXAiBwRfMxpfHGyRG7NXM4Z+9XmEP32fsqw/Ms1pKppDltOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign": "~0.3.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "cookie-jar": "~0.3.0", "oauth-sign": "~0.3.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}}, "2.28.0": {"name": "request", "version": "2.28.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.28.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "f20c4045de01eaf6976e127cbebff113827ab33a", "tarball": "https://registry.npmjs.org/request/-/request-2.28.0.tgz", "integrity": "sha512-6Z/lLMHDdBk4Gq7bVGfpkfa3vprifgKqwvwVYjf5tt7UZJjIyz1AC4fmnTJikLkW74bgdE3FR0XF4r1mf7cmlg==", "signatures": [{"sig": "MEQCIEkWYlET73VLLW9kuY03yBsEmF2QWyGCBNEmc/Nr+x/RAiBnWeJwk/8ooSomr41wQUg21tjxHNZnYGcPQaKG4+jsqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.29.0": {"name": "request", "version": "2.29.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.29.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "0d4b8de70d26a9911a8344af9a0e8edab81ff1c3", "tarball": "https://registry.npmjs.org/request/-/request-2.29.0.tgz", "integrity": "sha512-iAFUuqfaFTUTixvLiP0AgydECINAMJA287va2xCvmwobxPD50jNL8ScT4D5HiyiZMG+wCbvXcr49OzQdJBn19Q==", "signatures": [{"sig": "MEUCIHb4pnR0RVl74KaROTLIg9ruoNKAQh997f7TFTcsRWo6AiEArtuXUQHdJ+cl77Ipp3ErNkTPA4ZdkjIKUhpLCChwt7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.30.0": {"name": "request", "version": "2.30.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.30.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "8e0d36f0806e8911524b072b64c5ee535a09d861", "tarball": "https://registry.npmjs.org/request/-/request-2.30.0.tgz", "integrity": "sha512-YjobnprRMBsJzyZOkIFf+GYKmuF+PsQWesN2s0yXedTCDBGpTKE2pm4viygPtGTRAtg9uF3+wmmb/+2a9Elc/A==", "signatures": [{"sig": "MEUCIQC9xR+ECNplObghM1zxAWN5+IiFoIvC4HZW05drSbyXFgIgbyj4mjc7glNOjPseHdAyoZjpbTP93T8RP4zih/g3rtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.31.0": {"name": "request", "version": "2.31.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.31.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "4c8ac967c9a4b9410cb4ba1a61fdb644267eeeff", "tarball": "https://registry.npmjs.org/request/-/request-2.31.0.tgz", "integrity": "sha512-mR68gQIhSwipuNPRcQx1Z5SsiGEd/PiZBARCFduBnzsaoBhsTvR47tAABDdqzlh+S301QnecgkWjUkib9+mC8A==", "signatures": [{"sig": "MEUCIDT3SS14l9kQ69QZuwcVjUhhetag2ahjGcvPH/S5GxlrAiEA5eMLKr/KTeQMZ36LTJ2uuvZyShexXBhVWd0ZpDfbWkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.32.0": {"name": "request", "version": "2.32.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.32.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "c546e8ab8ae6d431047158c74cb85bbda57586c5", "tarball": "https://registry.npmjs.org/request/-/request-2.32.0.tgz", "integrity": "sha512-I8Qkf/P6Xwc29AHN1+vri7Y1blPtf6IANSc6HsqMsm7UD98NXrqcm6eBDuHlbkWtX0pJOMPYSPxl8pX2Vlibgg==", "signatures": [{"sig": "MEUCICFJZAkP+5HLOftqF9/E3+SkHKm0BJ+pNqNFZE0oe5X0AiEA/25KHeWbjygfoc2q7ygsZHYg4rpLdpxj+3Iicw56JWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": "~0.9.15", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.33.0": {"name": "request", "version": "2.33.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "request@2.33.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "5167878131726070ec633752ea230a2379dc65ff", "tarball": "https://registry.npmjs.org/request/-/request-2.33.0.tgz", "integrity": "sha512-FqM/Jy/kECM/UjanL+3fyQbeEBMEutBXRgltnneYIpb7R+u/3kWgzrAoj+55DjCyMYQkzXVeW4/JkwDJ1H8HxA==", "signatures": [{"sig": "MEUCICQDnhueG8aFyDCjwff177lIrlmBPkSbXCBcBEUntr67AiEAtAFkb89COiZ7v32A9sfAVXtC34rBwoYydKGARWPyzYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.34.0": {"name": "request", "version": "2.34.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache, Version 2.0", "_id": "request@2.34.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "b5d8b9526add4a2d4629f4d417124573996445ae", "tarball": "https://registry.npmjs.org/request/-/request-2.34.0.tgz", "integrity": "sha512-mD5mNhfkeaKMg5ZY/hZFbW4lyC/NTn34/ILGQr/XLSuxYOE6vJfL0MTPPXZcZrdt+Nh1Kce+f4B4KbGThIETxQ==", "signatures": [{"sig": "MEUCIQCGFNpBKrG6Odz4EoSJ4kjSe5FwnQW/pwqShfgzbzxKuQIgOltVM2ppXOQ/2RN8rmDleCdP3svkGjy8gvDUBUy7V9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.3.0", "http-signature": "~0.10.0"}}, "2.35.0": {"name": "request", "version": "2.35.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache, Version 2.0", "_id": "request@2.35.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "0d5c0f293479a080cba508f94342cd2415a0d297", "tarball": "https://registry.npmjs.org/request/-/request-2.35.0.tgz", "integrity": "sha512-l6TZWFCEwywj3eLWikFkIcrTWJBkX5db6Yz/NGC1RzwAujF86jzhvxv5EQKREf+jSduKNIUhh4+2QH3o3pUULQ==", "signatures": [{"sig": "MEQCIBP9/U53hnKeZY9g1ag1huCsOFCy7yXfSt/WuxBpESwbAiBVyT8LGu+btApJurKWtKzgPJM/qSNCoOQpUWp3BBQreg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "lodash.merge": "~2.4.1", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}}, "2.36.0": {"name": "request", "version": "2.36.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache, Version 2.0", "_id": "request@2.36.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "28c6c04262c7b9ffdd21b9255374517ee6d943f5", "tarball": "https://registry.npmjs.org/request/-/request-2.36.0.tgz", "integrity": "sha512-iVii/ruMH9i8k++HYYPqi+nb1Pbgz7UOTGbFEiyhl7uDN8PhyFV2lGJa8XLIUS5tyt5scERcLkwqvCNF84Vv2Q==", "signatures": [{"sig": "MEYCIQC8BDlmIvSMEVZXWrUA2VinLuMoPRr1gcuW6biCyKuIUwIhAJrusWcWL0h2GueppEQwSKwox+8EnOLb932HXGbZinlz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.4", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "~1.0.0", "mime": "~1.2.9", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "~1.0.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}}, "2.37.0": {"name": "request", "version": "2.37.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.37.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "6c04c1f0f34af0c8b7408f1c1e30d4d6bd852d46", "tarball": "https://registry.npmjs.org/request/-/request-2.37.0.tgz", "integrity": "sha512-WFkZiwB84gb8jtO7DJKkYflmFikmqIVyraK69u5hFjxHGzEFhgCwVs8+4rwqQA+DRjmVCEJqyJKCEqMTa18+bg==", "signatures": [{"sig": "MEYCIQDEx1LZmRVxItMWh2DSijsPgxkTUSf2t7q4/ven7oocCAIhALUSsrYlpWObl0TW5zcBPURLzm5aGU77LWK7glUMw9fY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "6c04c1f0f34af0c8b7408f1c1e30d4d6bd852d46", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "http://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}}, "2.38.0": {"name": "request", "version": "2.38.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.38.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "b7e36dcbf74072feaa7156e05ef1ba2829816886", "tarball": "https://registry.npmjs.org/request/-/request-2.38.0.tgz", "integrity": "sha512-Kw1usPQZat9XsZoGs0o5MHgLCa7MhTxhJTBEEVPFjDLl99XHui6cdoGTvsaEe87xdv+/adFK+6276icWWyd/RQ==", "signatures": [{"sig": "MEUCIQDt43EcTssp1+5vuoHAnNhJsJsA7BEhcbjzQlxlYLWebwIgR0XDAt6r831fKNgFfCK5iD+AoVYu0+0iYkv4aWKlQi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "b7e36dcbf74072feaa7156e05ef1ba2829816886", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}}, "2.39.0": {"name": "request", "version": "2.39.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.39.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "e01bdbec68c37a3b154681867daa2833bb38cbc4", "tarball": "https://registry.npmjs.org/request/-/request-2.39.0.tgz", "integrity": "sha512-cCWh1oPe7aVkBe46QaR4DV9nKf/TLP8ypiVtBxFkpdiJm6vDI9DFEsE9jCKcExoDbJWqncpPEh4j+YdbmNcONA==", "signatures": [{"sig": "MEUCIDw9W2YWrH4IpjHSolh5hw36CErRNe5i5MD5S9Hsm4S0AiEA/BSubC3YRSVLH0yP2/8X13SXXDM/VPe8dsnXBzVGAXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "e01bdbec68c37a3b154681867daa2833bb38cbc4", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~0.6.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}}, "2.40.0": {"name": "request", "version": "2.40.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.40.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "4dd670f696f1e6e842e66b4b5e839301ab9beb67", "tarball": "https://registry.npmjs.org/request/-/request-2.40.0.tgz", "integrity": "sha512-waNoGB4Z7bPn+lgqPk7l7hhze4Vd68jKccnwLeS7vr9GMxz0iWQbYTbBNWzfIk87Urx7V44pu29qjF/omej+Fw==", "signatures": [{"sig": "MEUCICT8DpL7kQ+7g54k1vJEcIEIs8xxGZBvMKGNj8KgXZY4AiEAjk+KQGXwXgh5BMwp3UbXIcJMDFfD+zYGCGNPwkMLMfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "4dd670f696f1e6e842e66b4b5e839301ab9beb67", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"qs": "~1.0.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.3.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "http-signature": "~0.10.0"}}, "2.41.0": {"name": "request", "version": "2.41.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.41.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "752520b1abf5cb78c6321e1d3ed692d11fbd1058", "tarball": "https://registry.npmjs.org/request/-/request-2.41.0.tgz", "integrity": "sha512-ZeNsqFKXkyJ0cqPd+a00o6DaQPfmt5Bn7VFqucm2yPm+H6VSNIg6JWhKKeFxcpvrI04vUmzCUPN5fjsvu8Zvxw==", "signatures": [{"sig": "MEQCIDMbLXU8pYXhitdtzkP6uGoPYME4oxC4AWZ5J5zEkcyKAiBsk+CXhkxXtFfSED9bk63cGpoDMJLOHvN0feosm8Fw/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "752520b1abf5cb78c6321e1d3ed692d11fbd1058", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}}, "2.42.0": {"name": "request", "version": "2.42.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.42.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "572bd0148938564040ac7ab148b96423a063304a", "tarball": "https://registry.npmjs.org/request/-/request-2.42.0.tgz", "integrity": "sha512-ZpqQyQWQ7AdVurjxpmP/fgpN3wAZBruO2GeD3zDijWmnqg3SYz9YY6uZC8tJF++IhZ/P2VZkZug/fFEshAkD6g==", "signatures": [{"sig": "MEUCIEWiFto4V0FH+P9ly9usWQei85uSSSPy4O0cf1nH9Xm2AiEAhJrXLFw2DsbuerZwQWojuN3ZlElYb3k2ed4qdwNfXvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "572bd0148938564040ac7ab148b96423a063304a", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}}, "2.43.0": {"name": "request", "version": "2.43.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.43.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "6d1549482c494e13bfd4e206a3311f951be9ea63", "tarball": "https://registry.npmjs.org/request/-/request-2.43.0.tgz", "integrity": "sha512-1gdHqAy7FzGD4gReZRak7ggkOKcaEqOZ44VDm46MWWQuGqM1Gzcyc67d5xG3yxvBDo7Q7/hpVr+ypTxKYQU9Yw==", "signatures": [{"sig": "MEUCIQCDNysVq+wQcoPOHBIOjfhlC1LilTpgO3IuQk09MBf+AwIgR2FjxWftepo5yZtrecYjOziegTgKpj03pvAPxn80mIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "6d1549482c494e13bfd4e206a3311f951be9ea63", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}}, "2.44.0": {"name": "request", "version": "2.44.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.44.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "78d62454d68853cadfb07ad31f58b9ec98072ea8", "tarball": "https://registry.npmjs.org/request/-/request-2.44.0.tgz", "integrity": "sha512-gF6ZBvOhdOAANnP9yy9rPJ34PoUA1oqkrABVN2WZWoEBOQHvQuM/h9lYEAg/DQZEMmATx7E1x9Yiq3P3EFKz3Q==", "signatures": [{"sig": "MEYCIQCEKOjm0pOk3oPZrxYXByOX1A+7OMNubpuoB1XO/edh0QIhAJmaPEWMEm1f8Xz8ZyPaWyc4S3mhCKfxTP1k004Hxom2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "78d62454d68853cadfb07ad31f58b9ec98072ea8", "engines": ["node >= 0.8.0"], "scripts": {"test": "node tests/run.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"rimraf": "~2.2.8"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}}, "2.45.0": {"name": "request", "version": "2.45.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.45.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "29d713a0a07f17fb2e7b61815d2010681718e93c", "tarball": "https://registry.npmjs.org/request/-/request-2.45.0.tgz", "integrity": "sha512-gEPucp8vYKQBoPhO45nAdWFvpiYklhA3TjDzseGRgDK4lYIOoWEwFff22a0Cy7uXMDw/wNpGHd2eDRvIpKDE0g==", "signatures": [{"sig": "MEUCIQCvJ8ciAUSincB3PBVNxHoSRJW90lS9Uh0DVPnpQvpjvgIgIJaVi1lAlsGyU7yGXEcvwZZdGWhp9EjJM3wPnfuTTOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "29d713a0a07f17fb2e7b61815d2010681718e93c", "engines": {"node": ">=0.8.0"}, "gitHead": "fff5c951778859dc1f3d17f38f7d4426cbb75918", "scripts": {"lint": "./node_modules/eslint/bin/eslint.js lib/ *.js", "test": "npm run lint && node tests/run.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"eslint": "0.5.1", "rimraf": "~2.2.8"}, "optionalDependencies": {"hawk": "1.1.1", "aws-sign2": "~0.5.0", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "http-signature": "~0.10.0"}}, "2.46.0": {"name": "request", "version": "2.46.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.46.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "homepage": "https://github.com/mikeal/request", "bugs": {"url": "http://github.com/mikeal/request/issues"}, "dist": {"shasum": "359195d52eaf720bc69742579d04ad6d265a8274", "tarball": "https://registry.npmjs.org/request/-/request-2.46.0.tgz", "integrity": "sha512-cI6BttkBbiIPKBmDrzo0Ovuo9dlVGd+hrou7O/xEpY4a6EhvPYPyiR7qQb0a+Q34q5uaxfq6qJdOhmY0fupblA==", "signatures": [{"sig": "MEYCIQDfa51fqWokmcr29T5J1YtQZMDtmaCMQ3b+j4i80kioRAIhAO18W0wYRgrmXjTGK1ES1fIwXXQ089SqF5y5TEHlLJmW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "359195d52eaf720bc69742579d04ad6d265a8274", "engines": {"node": ">=0.8.0"}, "gitHead": "7cdd75ec184868bba3be88a780bfb6e10fe33be4", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/mikeal/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~1.2.0", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}}, "2.47.0": {"name": "request", "version": "2.47.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.47.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "09e9fd1a4fed6593a805ef8202b20f0c5ecb485f", "tarball": "https://registry.npmjs.org/request/-/request-2.47.0.tgz", "integrity": "sha512-7HDodfmCGAgxZWJddewFP3t3dKGFyMfb/tz9uWkyA3VbR79Wb/ydZ+OihNgOIj1IliYYbqohqox5evZgBCv5aw==", "signatures": [{"sig": "MEUCIQC7kwfKaBWxS3+P6uCCFbOoUOMk1JNd3f7IiKZSI6DcOAIgQiLpOeTgBpBFUjG/9YVCML9ZEcsT+K+IwrekZlQQkT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "09e9fd1a4fed6593a805ef8202b20f0c5ecb485f", "engines": {"node": ">=0.8.0"}, "gitHead": "0ad38bf9a51b34cb4bde1e65807dbee7e83bca82", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.6.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.4.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}}, "2.48.0": {"name": "request", "version": "2.48.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.48.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "3ae2e091c9698282d58a0e6989ece2638f0f1f28", "tarball": "https://registry.npmjs.org/request/-/request-2.48.0.tgz", "integrity": "sha512-FJdqbvTjo4HBlnTUAkjnvjvJS4cCSORYLI+eAIibhU2ripMirSpxMqJ4xu2uER85L/xb0XAKnrCmpao4M1/SPw==", "signatures": [{"sig": "MEYCIQCQtyd3SAmEGTl0pSOyF2P4ce9T6yyDzH8rJEAX/M2XCQIhAKdY+7hTkAvzrdEmmr42crNtJQu9ESZUWknaK7x+A+ac", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "3ae2e091c9698282d58a0e6989ece2638f0f1f28", "engines": {"node": ">=0.8.0"}, "gitHead": "0d8644ec6e0699a4dba8aad11bc0c10a101c0aec", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.7.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}}, "2.49.0": {"name": "request", "version": "2.49.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.49.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "0d4f6348dc3348059b553e4db60fd2478de662a7", "tarball": "https://registry.npmjs.org/request/-/request-2.49.0.tgz", "integrity": "sha512-AV8Rypi7OHPxmkTu1W30zw8UEZW55Hx11JAgXBhucNylP34qNBjJDHoMNRdkivJYbtenSFsxGnkZeAqGXt4Q8A==", "signatures": [{"sig": "MEYCIQCLNxwRJVufovTUR+QVkEnIgHEKaM5G9WnGTc4eo4sIcwIhALzKKsgPEvKme7oNXAiPxyqmBdjcNbBTWg8F0O6eNszV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "0d4f6348dc3348059b553e4db60fd2478de662a7", "engines": {"node": ">=0.8.0"}, "gitHead": "6e6e589159a30bb441df4eb5e30428ce4d50c2ec", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.8.0", "aws-sign2": "~0.5.0", "form-data": "~0.1.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8"}}, "2.50.0": {"name": "request", "version": "2.50.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.50.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "d23881a57c76b5cc8b9ca969e3acbfc3c8fea367", "tarball": "https://registry.npmjs.org/request/-/request-2.50.0.tgz", "integrity": "sha512-K3eqRa9HxSoWwDkQLQqM3Xwfmibkok/47hnZrNSIQ+aFLoST9UtkXREh6rAsJseLyf273VmhME1Xx4Cd9wNtPg==", "signatures": [{"sig": "MEYCIQC8CN6MCCgzyEhzFHhj4KHxiaOan0FKqq6FKYVfmRbX8AIhAPySz8UJ3elMWJQ2QI9gCvox5f3vDG0K4Ha3RMNTvCHS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "d23881a57c76b5cc8b9ca969e3acbfc3c8fea367", "engines": {"node": ">=0.8.0"}, "gitHead": "e07f6acdeed9c27c77445829880f1cd2922b4414", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser", "test-browser": "browserify tests/browser/test.js -o tests/browser/test-browser.js && karma start tests/browser/karma.conf.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.8.0", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-phantomjs-launcher": "~0.1.4"}}, "2.51.0": {"name": "request", "version": "2.51.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.51.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "35d00bbecc012e55f907b1bd9e0dbd577bfef26e", "tarball": "https://registry.npmjs.org/request/-/request-2.51.0.tgz", "integrity": "sha512-6pfShjLfn6ThOlPHyQo7nBxEwTa2PzvqHruxQS51TrADjWj3qetRZ2Ae5gRzMF7N2fKG5Ww7su+Z6jA3sFv0Gw==", "signatures": [{"sig": "MEUCIFBWDUdysSyliFHK97gWnGV8tDlh8dPW4fhJBFL4UMX+AiEA9KHJ1cpBfnHFk3Z2ZXDM07haaV2Ge5M7FfTvG5Wavgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "35d00bbecc012e55f907b1bd9e0dbd577bfef26e", "engines": {"node": ">=0.8.0"}, "gitHead": "1c8aca6a9205df58660c676005fb8ec4603d5265", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser", "test-browser": "browserify tests/browser/test.js -o tests/browser/test-browser.js && karma start tests/browser/karma.conf.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "1.1.1", "caseless": "~0.8.0", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~1.0.1", "oauth-sign": "~0.5.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.3.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-phantomjs-launcher": "~0.1.4"}}, "2.52.0": {"name": "request", "version": "2.52.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.52.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "02d82a8adc04dc94a3a79f09fc850ade9aa21e74", "tarball": "https://registry.npmjs.org/request/-/request-2.52.0.tgz", "integrity": "sha512-U5lwtiMgtCOQB2TrfW/UVFZibdrDpheO2VF7rKrV8yqokNG0lqGqovRHBB+Z4aYdYk9TzZOxYlCU/Iwing0Fyw==", "signatures": [{"sig": "MEYCIQD7ZK2jvH81nAnUxJmJJCUX3OaK3obLMp0VjSaitdhKOgIhAJ6K6MPqyTca5YpdtDgt4TcTvuf9b3Rv64wAItcuoBM/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "02d82a8adc04dc94a3a79f09fc850ade9aa21e74", "engines": {"node": ">=0.8.0"}, "gitHead": "06875f5935de4b2c66d8bf62e9fe77939fdcce96", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser && npm run clean", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.53.0": {"name": "request", "version": "2.53.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.53.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "180a3ae92b7b639802e4f9545dd8fcdeb71d760c", "tarball": "https://registry.npmjs.org/request/-/request-2.53.0.tgz", "integrity": "sha512-E/kWR29ujsKySEMwTRod7i8fzxIV0v58itPRcvG3FyE0Uv/l8wujgPeXlXstBybNF0EdSVounY+vcnkBn03woQ==", "signatures": [{"sig": "MEYCIQCEYidengovhADH2N9hluiSdTuUNJRBNtoiEMM6YduS2wIhANPZtz/PK/wDEripIZVvcVy2NDrYjlBb+YNdvOOej/VB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "180a3ae92b7b639802e4f9545dd8fcdeb71d760c", "engines": {"node": ">=0.8.0"}, "gitHead": "541ce25648bc2ecab924d7d7197a2685fa16d348", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser && npm run clean", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "nylen", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.3.1", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.5.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.5.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.54.0": {"name": "request", "version": "2.54.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.54.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "a13917cd8e8fa73332da0bf2f84a30181def1953", "tarball": "https://registry.npmjs.org/request/-/request-2.54.0.tgz", "integrity": "sha512-1ND8gGyQEkrwGr0NVNrdBt22PZ2vjDb8Etq458qRbn96Q958C2jsPNQUwEsOSKGNmsBuIsObK3qfJL/ZENNpmw==", "signatures": [{"sig": "MEUCIQCx0dd46p0UIZNKUd9fpvGGPNByoJc+JEUh1P8IqtawWAIgEp9nod7xp2uWQN1q7mafP9t0Yk98CwZvYcE1zcIeVFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "a13917cd8e8fa73332da0bf2f84a30181def1953", "engines": {"node": ">=0.8.0"}, "gitHead": "12080382de2b0e57f3dbaafadc8109af7bbc85ed", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.4.0", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.4.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.17.1", "rimraf": "~2.2.8", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.55.0": {"name": "request", "version": "2.55.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.55.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "d75c1cdf679d76bb100f9bffe1fe551b5c24e93d", "tarball": "https://registry.npmjs.org/request/-/request-2.55.0.tgz", "integrity": "sha512-tmHyusPYdblyvhGzDxPtDGOHWnP2h3dR9M5yO0UC5ndGGx0nRpOU+4c8bcv7utMxB7AakrE9p4B0CsqUBxYyyA==", "signatures": [{"sig": "MEYCIQDhKeoqAlAzsoAoi3ELto4SOf4Z0A6jzln6pZYC9Z/0bgIhALlJN4mf/hjmvK918EcFMAUPt0TAb8qP4yEaogOH6U9W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "d75c1cdf679d76bb100f9bffe1fe551b5c24e93d", "engines": {"node": ">=0.8.0"}, "gitHead": "b6000376387db12d0c2d7ed9ee87b0ba123e36dc", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "https://github.com/request/request.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Simplified HTTP request client.", "directories": {}, "dependencies": {"bl": "~0.9.0", "qs": "~2.4.0", "hawk": "~2.3.0", "caseless": "~0.9.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.6.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.4.0", "http-signature": "~0.10.0", "combined-stream": "~0.0.5", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.17.1", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.56.0": {"name": "request", "version": "2.56.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.56.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "21a6bd9cfd6aff33a7749971ffac29e833fa28fe", "tarball": "https://registry.npmjs.org/request/-/request-2.56.0.tgz", "integrity": "sha512-s7vRMnqbXdbh1JE5N28Z4b1SHz37as1kNV0TAPbnCGqab9NHC2VFgBi5Ll/ftfTl8HyvT7CZJezGFVmqT0at0A==", "signatures": [{"sig": "MEUCIQDnejmLvmaBtq1eikh3Irzbc7ESLqDxt1gTp35kgAYlCwIgDGDYZbdA/NlEajldclDsCzijjea1JHleIb8yJQ33MtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "21a6bd9cfd6aff33a7749971ffac29e833fa28fe", "engines": {"node": ">=0.8.0"}, "gitHead": "dec45b11eb264a8100e8c43fa44817d62368f780", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"bl": "~0.9.0", "qs": "~3.1.0", "hawk": "~2.3.0", "caseless": "~0.10.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.57.0": {"name": "request", "version": "2.57.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.57.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "d445105a42d009b9d724289633b449a6d723d989", "tarball": "https://registry.npmjs.org/request/-/request-2.57.0.tgz", "integrity": "sha512-MWUXeVetZBn2usb2PWmqVsAAzG7Bg7HOFQoarVxsIjO3CNYFfXCC7rwWUAtrRBxa9b3WhsLdDXx1VL8RHY/rjA==", "signatures": [{"sig": "MEUCIQCDRy6lClhyo0JmR2kca8tpVYd3Y/HZ2yl8yDRCum8zyQIgGBg0IzhRpopGfkBihDDDEgsQ+BeJgoXrsbkzGdzPhGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "d445105a42d009b9d724289633b449a6d723d989", "engines": {"node": ">=0.8.0"}, "gitHead": "1fafe0dc387e5efa9ae95b40aa80c43e83e1b98f", "scripts": {"lint": "node node_modules/.bin/eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && node node_modules/.bin/taper tests/test-*.js && npm run test-browser", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"bl": "~0.9.0", "qs": "~3.1.0", "hawk": "~2.3.0", "caseless": "~0.10.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~0.2.0", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.58.0": {"name": "request", "version": "2.58.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.58.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "b5f49c0b94aab7fad388612a1fb6ad03b6cc1580", "tarball": "https://registry.npmjs.org/request/-/request-2.58.0.tgz", "integrity": "sha512-okf2uk5kTQKSr+xClLCKA6jGH+ekhV1TfzS7eGiR0u2+ozO2uOSwFUlBZ8xx7C8Oula36UfTaYGxqs4VRpaPRw==", "signatures": [{"sig": "MEUCIQCIdXMTOuIah4rI7rnZtZISn6pkylL7Bf2N3+2awRTE2QIgOLuUb+NB2mP+DPMwHLIe43MMdEm7LyhJDf4uwmH40hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "b5f49c0b94aab7fad388612a1fb6ad03b6cc1580", "engines": {"node": ">=0.8.0"}, "gitHead": "ab40f9e61f813f9cc68257c17621b7879561486c", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"bl": "~0.9.0", "qs": "~3.1.0", "hawk": "~2.3.0", "extend": "~2.0.1", "caseless": "~0.10.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.0.1", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.59.0": {"name": "request", "version": "2.59.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.59.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "984c7d66a8779af9300161ac83203d4b49ed9c65", "tarball": "https://registry.npmjs.org/request/-/request-2.59.0.tgz", "integrity": "sha512-KR3P4KRw8WKb4Du6lYz3gQx3wWe22p1KbTIRVG8zOXI5lsOGdCZLmMdMioZiT+cWinKkLYvHta0hYailDlQKxA==", "signatures": [{"sig": "MEQCIGSo5rDFcY4FqrFHdww/d5HDt0QBajutEIOZ8CjCWN6RAiBi5/67s38+Laffpi5/+55Y+1QnorFyERAzYYvm0a0MlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "984c7d66a8779af9300161ac83203d4b49ed9c65", "engines": {"node": ">=0.8.0"}, "gitHead": "24015ff407f30c7fe72716c43bb96411fb79bdcf", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"bl": "~1.0.0", "qs": "~4.0.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.60.0": {"name": "request", "version": "2.60.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.60.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "498820957fcdded1d37749069610c85f61a29f2d", "tarball": "https://registry.npmjs.org/request/-/request-2.60.0.tgz", "integrity": "sha512-X6joBWf52/4vPVEYBuEWk142MY/QCMQLBQzXcSLxEKgU36RT0D0JicZ8nsuoiohDUhn4RmBWOGenmKoiFnTCmg==", "signatures": [{"sig": "MEQCIEv6V+iIX/on/vhV5SeLKXPGcp+1Iy+3o6VJdFuRQirFAiAu1IGwcGsa94NaDhbydU5jv4n9HE+jE7t/I4BJ8r0YUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "498820957fcdded1d37749069610c85f61a29f2d", "engines": {"node": ">=0.8.0"}, "gitHead": "af19cef3bc60e9151ffce5015d8ce3c0728d3aca", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"bl": "~1.0.0", "qs": "~4.0.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.61.0": {"name": "request", "version": "2.61.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.61.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "6973cb2ac94885f02693f554eec64481d6013f9f", "tarball": "https://registry.npmjs.org/request/-/request-2.61.0.tgz", "integrity": "sha512-VzuqfRVF3Fy53F+dgzN3yf1S2P+Jf5CygTa5CSCn5UYfZgeRK7GgshyKEKjCs1fOAFLFVaBkVJSSb+yh9vTsKw==", "signatures": [{"sig": "MEYCIQCHcbBlbMhJwU7xR6Wigwt2kRdC1rX2TubsLpD5LuYxZgIhAKKRS0iKqlseQ1/72+bhrEo/hqvGLqBQ2xgAE/E1kZ/f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "6973cb2ac94885f02693f554eec64481d6013f9f", "engines": {"node": ">=0.8.0"}, "gitHead": "8492d18add93af1214943ee12e25371f9f9adad3", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"bl": "~1.0.0", "qs": "~4.0.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.62.0": {"name": "request", "version": "2.62.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.62.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "55c165f702a146f1e21e0725c0b75e1136487b0f", "tarball": "https://registry.npmjs.org/request/-/request-2.62.0.tgz", "integrity": "sha512-WhEj/HXtKaFlzD/dE08poXcEZa01pf0w79sr2265xIfhHAUvt00Hx3QvVZgZm6Z3f1aF2Nycg7hJNXbPA5ZSfg==", "signatures": [{"sig": "MEUCIFOwIt4/6MdRSVEEt/gy1Rc1PBFwsly9RBZzXhhTHwNCAiEA+itpiiKJxQNLKUckud/HeF5CQJSJ6oB0UWgtXwPV/N8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "55c165f702a146f1e21e0725c0b75e1136487b0f", "engines": {"node": ">=0.8.0"}, "gitHead": "9cda443b62b33d077dc002aefd19e0b267df9cfa", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.14.2", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.1.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.63.0": {"name": "request", "version": "2.63.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.63.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "c83e7c3485e5d9bf9b146318429bc48f1253d8be", "tarball": "https://registry.npmjs.org/request/-/request-2.63.0.tgz", "integrity": "sha512-aKJ1taGX6UDm3JjQMEmEaZH3EDL7It6slBBo/YoqgowiBYewoLHqaoiqDfqmQkJ6s6RN33ZmEslUGocEtZL9CA==", "signatures": [{"sig": "MEUCIQDOPbXR/Ax6DANNpUaL7AgUUHDtGRJLQGMmIPiewL6W6gIgQFDzCtlAH9wg4F0fy6REDXLkP3mX8fkJNsEJ780MfjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "c83e7c3485e5d9bf9b146318429bc48f1253d8be", "engines": {"node": ">=0.8.0"}, "gitHead": "e1c36a0d10cab1466262efddd6cc54f80d88be9c", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.1.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.64.0": {"name": "request", "version": "2.64.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.64.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "96a582423ce9b4b5c34e9b232e480173f14ba608", "tarball": "https://registry.npmjs.org/request/-/request-2.64.0.tgz", "integrity": "sha512-LsVk5VhLw6tPl7qSPHxhJBktlnJWDNLjoHiCqJxVP9CU0xDwninOi7jWHKo2Wjzb1xTg/itdHYOPXOteVBo03g==", "signatures": [{"sig": "MEUCIEtZM/8xYaAvVM+/RxYQVx7Fbtl58BReyXBopXhl+b7iAiEA5sz3XKBDiD7EqGDhnVFIzgVknu8JhOxUAhWCys/icjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "96a582423ce9b4b5c34e9b232e480173f14ba608", "engines": {"node": ">=0.8.0"}, "gitHead": "ca364485249f13c4810bb9b3952fb0fb886a93ee", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.1.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.1", "aws-sign2": "~0.5.0", "form-data": "~1.0.0-rc1", "node-uuid": "~1.4.0", "mime-types": "~2.1.2", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": ">=0.12.0", "tunnel-agent": "~0.4.0", "forever-agent": "~0.6.0", "har-validator": "^1.6.1", "http-signature": "~0.11.0", "combined-stream": "~1.0.1", "json-stringify-safe": "~5.0.0"}, "devDependencies": {"tape": "~3.0.0", "karma": "~0.12.21", "taper": "~0.4.0", "eslint": "0.18.0", "rimraf": "~2.2.8", "bluebird": "~2.9.21", "istanbul": "~0.3.2", "coveralls": "~2.11.2", "karma-cli": "0.0.4", "karma-tap": "~1.0.1", "browserify": "~5.9.1", "codecov.io": "~0.1.2", "buffer-equal": "0.0.1", "function-bind": "~1.0.0", "karma-coverage": "0.2.6", "server-destroy": "~1.0.0", "karma-browserify": "~3.0.1", "browserify-istanbul": "~0.1.3", "karma-phantomjs-launcher": "~0.1.4"}}, "2.65.0": {"name": "request", "version": "2.65.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.65.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "cc1a3bc72b96254734fc34296da322f9486ddeba", "tarball": "https://registry.npmjs.org/request/-/request-2.65.0.tgz", "integrity": "sha512-576hyNF2kC13I++6Y1KwVE9GWirk1TBYwF2lLvqV2JI2m4Q1ULG0u+eCs4oPGvvXogsvahoixveokP0X/IBiTA==", "signatures": [{"sig": "MEUCIQDKKIwYc04GF11BXizO14nW2UaK3r6TPH+z0zuxzP1yRwIgDwUFeQUC1tsUusG5ckrx5byseBC1NsUOIn3V7umvrL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "cc1a3bc72b96254734fc34296da322f9486ddeba", "engines": {"node": ">=0.8.0"}, "gitHead": "8a7a37835c600f5006a6679aa23a0db504003ecd", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.14.3", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "4.1.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.2.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.3", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.2", "http-signature": "~0.11.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "0.18.0", "rimraf": "^2.2.8", "bluebird": "^2.10.1", "istanbul": "^0.3.21", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^11.2.0", "codecov.io": "^0.1.6", "buffer-equal": "^0.0.1", "function-bind": "^1.0.2", "karma-coverage": "^0.2.6", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}}, "2.66.0": {"name": "request", "version": "2.66.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.66.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "d4c7c9b2ecea2b5fca45ad2d7cfb3b6afeeaa1d8", "tarball": "https://registry.npmjs.org/request/-/request-2.66.0.tgz", "integrity": "sha512-7NSJW4jh3iU7rcd8gU0dPJ90WmsququSqJphUd0Xw37P5Dn61J5HMX0LBb23DbaC3dRKBdFS9R5RiBJeZ58rPg==", "signatures": [{"sig": "MEUCIFBOrBBZ85MPk5x9DWnAI5kRgRUXDzbhuVySfK2btrb/AiEA1dgOfdC+peSm0zbQwBF/n//hdSx90h4GsFt09n6IGcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "d4c7c9b2ecea2b5fca45ad2d7cfb3b6afeeaa1d8", "engines": {"node": ">=0.8.0"}, "gitHead": "48e505d845d4fa1a9d87b3c94c205e563d91e8b9", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.2.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.2", "is-typedarray": "~1.0.0", "http-signature": "~1.0.2", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.9.0", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.1", "codecov.io": "^0.1.6", "buffer-equal": "^0.0.1", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}}, "2.67.0": {"name": "request", "version": "2.67.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.67.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "8af74780e2bf11ea0ae9aa965c11f11afd272742", "tarball": "https://registry.npmjs.org/request/-/request-2.67.0.tgz", "integrity": "sha512-fzMRDWVEdMktE3foqvL4CBmC+AR8WvcP8pIPx6JSqqhWuPr+BxX9tKx4XiijfyeKtqqRMNpHDWqFMw4JlRPIJg==", "signatures": [{"sig": "MEYCIQCtmPBPObiMgQZucc2DRRgLwT0n3ji1UM7dgLGH1WRSHAIhAOo3otAXwaNL+NDids6iLVeArG/HBAgyCuEwPZRZ7uKU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "8af74780e2bf11ea0ae9aa965c11f11afd272742", "engines": {"node": ">=0.8.0"}, "gitHead": "76f0655befbe8b37fa246bdca1107cbf57798d9a", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"bl": "~1.0.0", "qs": "~5.2.0", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.2", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.9.0", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.1", "codecov.io": "^0.1.6", "buffer-equal": "^0.0.1", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}}, "2.68.0": {"name": "request", "version": "2.68.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.68.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "35fe6a5cd184393a477c95b0725604d822fb9105", "tarball": "https://registry.npmjs.org/request/-/request-2.68.0.tgz", "integrity": "sha512-TTVSLA1b45Y3kP86SDLRuCe/22KJ6KFS4aDBZ7MvRwTl3BuDJP7viX6zaYdiA2C8NHpxA0lxDbtiZfFl1KugGw==", "signatures": [{"sig": "MEUCIE/xaGzMZJnERqGrdBE6/Y7M8m6LzWwohTUomgPhAEiJAiEA5t1K/oMxt2bsreASzdGtGwwjpTWewPrdWa9eqA1bXJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "35fe6a5cd184393a477c95b0725604d822fb9105", "engines": {"node": ">=0.8.0"}, "gitHead": "ce6c69faee2dd2a3d2d23f3d20532ff025da3a42", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "5.2.0", "dependencies": {"bl": "~1.0.0", "qs": "~6.0.2", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"aws4": "^1.2.1", "tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.10.3", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^13.0.0", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}}, "2.69.0": {"name": "request", "version": "2.69.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.69.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "cf91d2e000752b1217155c005241911991a2346a", "tarball": "https://registry.npmjs.org/request/-/request-2.69.0.tgz", "integrity": "sha512-WORLlE5HDoewOPTWQwArryfR0KPT0ac5Ck3VFbJ5g+mt2NoSgTLfEWQ+7Net/3QP7mMmKoJzsR28kOXO2Fx5Yw==", "signatures": [{"sig": "MEQCIBl+65hYG71htLL1ut+CbfgSZyHh5rUttKmIqFRnvouDAiBKfKgnOiGvnlUJhhVVRRIvTGSPZ4B+PervzWIStKHo2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "cf91d2e000752b1217155c005241911991a2346a", "engines": {"node": ">=0.8.0"}, "gitHead": "1c2fb40c74efb4f706f350a78dbd5e58fe913af3", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "4.1.2", "dependencies": {"bl": "~1.0.0", "qs": "~6.0.2", "aws4": "^1.2.1", "hawk": "~3.1.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.0", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "1.10.3", "rimraf": "^2.2.8", "bluebird": "^3.0.2", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^13.0.0", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^4.4.0", "browserify-istanbul": "^0.1.5", "karma-phantomjs-launcher": "^0.1.4"}}, "2.70.0": {"name": "request", "version": "2.70.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.70.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "7ecf8437d6fb553e92e2981a411b9ee2aadd7cce", "tarball": "https://registry.npmjs.org/request/-/request-2.70.0.tgz", "integrity": "sha512-66LXJcAM4BWOtCRVqLqlL4D4xGLSSIvnY43tES/vR5+0hj1QewsOtSVrvcoDv7OERIpWfzYVEo0g/5aZ1IwMCA==", "signatures": [{"sig": "MEUCIQC8vJuMYq2dlLbX2zxjsmPaYOtgg0LZfQUhMsLrLcZ2/AIgdkK6Sj7Gkw6nohv+72eGcLT6TRk3k1giijC3Zn6/ljU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "7ecf8437d6fb553e92e2981a411b9ee2aadd7cce", "engines": {"node": ">=0.8.0"}, "gitHead": "bc781cb49aafedb057719e8b1eb7850979688a85", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "5.9.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.1.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.2", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.70.0.tgz_1459850846109_0.8679330893792212", "host": "packages-12-west.internal.npmjs.com"}}, "2.71.0": {"name": "request", "version": "2.71.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.71.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "6f14643c9c5a67caee6a95cf8ef0477d5603bd91", "tarball": "https://registry.npmjs.org/request/-/request-2.71.0.tgz", "integrity": "sha512-6RFoZBS0is61M6kIsE311mGfEmh60P54zHaxpHo62repHs7dsaCppyX4fPcz2QssfLOgFxZ4t4arDz2PFQd7Fg==", "signatures": [{"sig": "MEQCIDBl5jVWug9CuxX1P6C0bUUDweJmK8bsIoyGk3hU0V2kAiA43te5iPQMhRDnOYUxiKy+/RSGqXr8I8/758WVXMudbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "6f14643c9c5a67caee6a95cf8ef0477d5603bd91", "engines": {"node": ">=0.8.0"}, "gitHead": "25b3ea86c330eb68a68ffe332c4002107a3d6272", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "5.9.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.1.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.2", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.71.0.tgz_1460466575069_0.2864116954151541", "host": "packages-16-east.internal.npmjs.com"}}, "2.72.0": {"name": "request", "version": "2.72.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.72.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "0ce3a179512620b10441f14c82e21c12c0ddb4e1", "tarball": "https://registry.npmjs.org/request/-/request-2.72.0.tgz", "integrity": "sha512-rQiQ3Eza3HNC+gBlzKxXaPwG1rQIcO0/7TKGIgA9D/obvFK//H+pzkCS4CctQ7aFk6LboTvyFXHMEdf8P4pSxg==", "signatures": [{"sig": "MEQCIFJBtrotdbGK73/aVLt/qqEBX0lzpBu8I6adYuqdvfiRAiBxg62LrLcDn2Onkk5UHor1ES7PnSyvT54Mxrkfp+Pn0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "0ce3a179512620b10441f14c82e21c12c0ddb4e1", "engines": {"node": ">=0.8.0"}, "gitHead": "6dcac13642955577592fdafb5ff3cdc8a6ff1b1b", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.8.5", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "5.9.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.1.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc3", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.2.0", "karma": "^0.13.10", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^0.1.1", "karma-tap": "^1.0.3", "browserify": "^12.0.2", "codecov.io": "^0.1.6", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^0.5.3", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.72.0.tgz_1460901215210_0.9173020373564214", "host": "packages-12-west.internal.npmjs.com"}}, "2.73.0": {"name": "request", "version": "2.73.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.73.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "5f78a9fde4370abc8ff6479d7a84a71a14b878a2", "tarball": "https://registry.npmjs.org/request/-/request-2.73.0.tgz", "integrity": "sha512-FMowpvTkMn1jqYkIB+0fGbtlWgGGuV6WFOUBjWCSuhLKhRY29BbvY0ls6w1Ql0vqsbtjbs1XWsgw+pI40YfQhg==", "signatures": [{"sig": "MEUCICAJPu2n8AHfC0BArJgEDnCWwQaWYphPq60LJmo4VpxIAiEA4/bPSaDqx0Yx/pRuGgabnekMNcprz+YPS0EEZH5Ml8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "5f78a9fde4370abc8ff6479d7a84a71a14b878a2", "engines": {"node": ">=0.8.0"}, "gitHead": "fa46be421b165c737c93672a8f920245cfb090a2", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.6", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"bl": "~1.1.2", "qs": "~6.2.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc4", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.2.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^1.0.3", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.73.0.tgz_1468050202545_0.40326908184215426", "host": "packages-12-west.internal.npmjs.com"}}, "2.74.0": {"name": "request", "version": "2.74.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.74.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "7693ca768bbb0ea5c8ce08c084a45efa05b892ab", "tarball": "https://registry.npmjs.org/request/-/request-2.74.0.tgz", "integrity": "sha512-m3uMovC42y63jXe/Sr49/qJdqpSYwQAgYIc487l0zSXI6Z6f5cV/V4a86h2Z+AAwKpt5bfB66KrZxOfOSdh6FQ==", "signatures": [{"sig": "MEUCIAD5F2TueVV9mdv+d9mIzs7ABfFrO361U+OhAvyV66TaAiEAnkjYqaMrIS/OSMHtvb2K0J5JH79TqHDwukVBhzcshHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "7693ca768bbb0ea5c8ce08c084a45efa05b892ab", "engines": {"node": ">=0.8.0"}, "gitHead": "76e82351cbc21049441b1763c6f2bbd504fa8f5a", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.6", "description": "Simplified HTTP request client.", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"bl": "~1.1.2", "qs": "~6.2.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~1.0.0-rc4", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^2.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.74.0.tgz_1469231082306_0.13140005595050752", "host": "packages-16-east.internal.npmjs.com"}}, "2.75.0": {"name": "request", "version": "2.75.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.75.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "d2b8268a286da13eaa5d01adf5d18cc90f657d93", "tarball": "https://registry.npmjs.org/request/-/request-2.75.0.tgz", "integrity": "sha512-uNXre8CefDRFBhfB1bL0CkKBD+5E1xmx69KMjl7p+bBc0vesXLQMS+iwsI2pKRlYZOOtLzkeBfz7jItKA3XlKQ==", "signatures": [{"sig": "MEUCIAOVp1foZ9NbQyF1nHbUh8QstpfK5/ePxqQID2Zg/7fFAiEA6qlt5HOsxOJijfTvpXZgUJF5/6h/t1udulbPF++f06k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "d2b8268a286da13eaa5d01adf5d18cc90f657d93", "engines": {"node": ">=0.8.0"}, "gitHead": "e9f09c2832073858d6d988ba82a2895f36efa92d", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "6.5.0", "dependencies": {"bl": "~1.1.2", "qs": "~6.2.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.0.0", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.75.0.tgz_1474151606844_0.8052814984694123", "host": "packages-16-east.internal.npmjs.com"}}, "2.76.0": {"name": "request", "version": "2.76.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.76.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "be44505afef70360a0436955106be3945d95560e", "tarball": "https://registry.npmjs.org/request/-/request-2.76.0.tgz", "integrity": "sha512-oAqKWlLKtLtHlLa/dxjQ0Q03rXvGeNd4Kdj63cIbxmi2VulqWTuccD5V8a7GtI3QjtJ9294dltpp4PMBokIsww==", "signatures": [{"sig": "MEYCIQCKaJRpo4O7ZHB0AHkfXYRE2gt9GJYR/zVuwGn08VZiEgIhAOtO3otFfDBKx3gmK9gjVh2Jz1+BuRRNaYhZGU28QTdA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "be44505afef70360a0436955106be3945d95560e", "engines": {"node": ">=0.8.0"}, "gitHead": "7e873863803817d321dbc994d3eff943cde42ac7", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "6.5.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.76.0.tgz_1477385874691_0.41635931469500065", "host": "packages-12-west.internal.npmjs.com"}}, "2.77.0": {"name": "request", "version": "2.77.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.77.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "2b00d82030ededcc97089ffa5d8810a9c2aa314b", "tarball": "https://registry.npmjs.org/request/-/request-2.77.0.tgz", "integrity": "sha512-7mwEakHB+//oEb7f8cvfI4/5w10rp5DTcHYrGMzyqGMlIqwCsjFIKw3kTZTpH44y4SbeWJEqjhlawy31bpuuBg==", "signatures": [{"sig": "MEUCIQCW3p++p+3MylLd5pvTGZpS21pSU8mLcfDYfn+1OfKITwIgfg733NHQ9F3JUj3/AD8XodPxyVq5Df7bO3cSiLMaWfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "2b00d82030ededcc97089ffa5d8810a9c2aa314b", "engines": {"node": ">= 4"}, "gitHead": "8d534217a9411053e40885120696515c1bee0673", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "6.5.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.77.0.tgz_1478171877374_0.7896335965488106", "host": "packages-18-east.internal.npmjs.com"}}, "2.78.0": {"name": "request", "version": "2.78.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.78.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "e1c8dec346e1c81923b24acdb337f11decabe9cc", "tarball": "https://registry.npmjs.org/request/-/request-2.78.0.tgz", "integrity": "sha512-ui0hctTyP0uRKj6TYbl9t5+JJ8heIIuzsEJtKpfzSlWWEVKG9zisEs3djAbw6UXXR3vKNA5k9MnN0IpsEWrksA==", "signatures": [{"sig": "MEYCIQCwk6ToVJcRGUF+yvW2SfGENyEQzRJduAJSoah+jYef8QIhAMpWw576jBMOl6KpAJd7RtMglADP9KJaY2xGhy/JpME6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "_shasum": "e1c8dec346e1c81923b24acdb337f11decabe9cc", "engines": {"node": ">= 4"}, "gitHead": "d4a68e9b64979f388c204f5d957e366878262340", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "6.5.0", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "node-uuid": "~1.4.7", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.4.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.78.0.tgz_1478180284881_0.7387848466169089", "host": "packages-18-east.internal.npmjs.com"}}, "2.79.0": {"name": "request", "version": "2.79.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.79.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "4dfe5bf6be8b8cdc37fcf93e04b65577722710de", "tarball": "https://registry.npmjs.org/request/-/request-2.79.0.tgz", "integrity": "sha512-e7MIJshe1eZAmRqg4ryaO0N9G0fs+/gpDe5FlbnIFy6zZznRSwdRFrLp63if0Yt43vrI5wowOqHv1qJdVocdOQ==", "signatures": [{"sig": "MEUCIFP15AFZnuzE88zO9aWw4GfHwYBHXHPzQ2nSRT+ION0jAiEA+uTbuNuwGhThhWnPdk6tTsMjMjHC5QwYqpsmoYG+gHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "tags": ["http", "simple", "util", "utility"], "_from": ".", "files": ["lib/", "index.js", "request.js"], "_shasum": "4dfe5bf6be8b8cdc37fcf93e04b65577722710de", "engines": {"node": ">= 4"}, "gitHead": "ff729c6f1a87237060d075908563ce13386395ac", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "6.3.1", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "uuid": "^3.0.0", "extend": "~3.0.0", "caseless": "~0.11.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~2.0.6", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.79.0.tgz_1479489666177_0.7806831058114767", "host": "packages-12-west.internal.npmjs.com"}}, "2.80.0": {"name": "request", "version": "2.80.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.80.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "8cc162d76d79381cdefdd3505d76b80b60589bd0", "tarball": "https://registry.npmjs.org/request/-/request-2.80.0.tgz", "integrity": "sha512-js3h8XcQ/6zNNyhnnbpF6RVZixLxIkJUcMHr3k/t7NkBW4NOo2/k5olseL8gCtIcqaugPGsiGpJvsc5OQN3Xrw==", "signatures": [{"sig": "MEQCIAqLrhk26Yoajf22Sqc0GF7zPu6fdjTXQhAEk+cGYp83AiAvGU/EPC+8tNVWzLYbQ9sybQT3HVjDkITtraRQ96n8nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib/", "index.js", "request.js"], "_shasum": "8cc162d76d79381cdefdd3505d76b80b60589bd0", "engines": {"node": ">= 4"}, "gitHead": "f422111e0bc44e065a7b15e243748b59d5e99e33", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "6.9.2", "dependencies": {"qs": "~6.3.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "uuid": "^3.0.0", "extend": "~3.0.0", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "~0.4.1", "forever-agent": "~0.6.1", "har-validator": "~4.2.0", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "performance-now": "^0.2.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.80.0.tgz_1488602560726_0.9356375557836145", "host": "packages-18-east.internal.npmjs.com"}}, "2.81.0": {"name": "request", "version": "2.81.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.81.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "c6928946a0e06c5f8d6f8a9333469ffda46298a0", "tarball": "https://registry.npmjs.org/request/-/request-2.81.0.tgz", "integrity": "sha512-IZnsR7voF0miGSu29EXPRgPTuEsI/+aibNSBbN1pplrfartF5wDYGADz3iD9vmBVf2r00rckWZf8BtS5kk7Niw==", "signatures": [{"sig": "MEYCIQCI7lFyGWcxdAw6GBweX8bP1MIRTtv0+M3nASmonLUxaQIhAKSOn6XZN3DI/eAjMnSyJ5ciSgxav/1poUkoqs4nJBne", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib/", "index.js", "request.js"], "_shasum": "c6928946a0e06c5f8d6f8a9333469ffda46298a0", "engines": {"node": ">= 4"}, "gitHead": "a0cdc704c19e63e6f1740e173bb003c51eef524c", "scripts": {"lint": "eslint lib/ *.js tests/ && echo <PERSON> passed.", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["eslint", "hawk", "har-validator"]}, "_nodeVersion": "7.2.1", "dependencies": {"qs": "~6.4.0", "aws4": "^1.2.1", "hawk": "~3.1.3", "uuid": "^3.0.0", "extend": "~3.0.0", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.6.0", "form-data": "~2.1.1", "mime-types": "~2.1.7", "oauth-sign": "~0.8.1", "safe-buffer": "^5.0.1", "stringstream": "~0.0.4", "tough-cookie": "~2.3.0", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~4.2.1", "is-typedarray": "~1.0.0", "http-signature": "~1.1.0", "combined-stream": "~1.0.5", "performance-now": "^0.2.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "eslint": "^2.5.3", "rimraf": "^2.2.8", "codecov": "^1.0.1", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.81.0.tgz_1489075005134_0.036041518207639456", "host": "packages-12-west.internal.npmjs.com"}}, "2.82.0": {"name": "request", "version": "2.82.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.82.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "2ba8a92cd7ac45660ea2b10a53ae67cd247516ea", "tarball": "https://registry.npmjs.org/request/-/request-2.82.0.tgz", "integrity": "sha512-/QWqfmyTfQ4OYs6EhB1h2wQsX9ZxbuNePCvCm0Mdz/mxw73mjdg0D4QdIl0TQBFs35CZmMXLjk0iCGK395CUDg==", "signatures": [{"sig": "MEUCIBCBGWaR5ZJthc6zq3d8eX7Ree/ESJxmlvH7angRD/2MAiEAywLfTIGIPjEeFfGt75rm2G+Hkj1GfYRB2+FDz8Ge6fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "0ab5c36b6715ae054b8398c80791fdd65be69f72", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "8.5.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.2", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.82.0.tgz_1505849970127_0.44438329339027405", "host": "s3://npm-registry-packages"}}, "2.83.0": {"name": "request", "version": "2.83.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.83.0", "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "ca0b65da02ed62935887808e6f510381034e3356", "tarball": "https://registry.npmjs.org/request/-/request-2.83.0.tgz", "integrity": "sha512-lR3gD69osqm6EYLk9wB/G1W/laGWjzH90t1vEa2xuxHD5KUrSzp9pUSfTm+YC5Nxt2T8nMPEvKlhbQayU7bgFw==", "signatures": [{"sig": "MEUCIQDCyesl0W5YuP4xMmWDYDzPEtqUtoMQFGg2kd7/GUYdDwIgUjA1YL2edjFiCC1y6srmYOrS00TgxL4eG4N98yyhzQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "dd427d7aa0177a876da69f82801bf0c63a855310", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "8.5.0", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request-2.83.0.tgz_1506481236346_0.6338994088582695", "host": "s3://npm-registry-packages"}}, "2.84.0": {"name": "request", "version": "2.84.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.84.0", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "59ca54a18663d7986aa37dbe4632aa8ada3a467f", "tarball": "https://registry.npmjs.org/request/-/request-2.84.0.tgz", "fileCount": 16, "integrity": "sha512-+arBW9+9rg/X6TeMxseqWVdPF1AT3KQ7cEEC4mmkdrbC6pQ6m7+iKzfU6vZ21cBD0MbygN2sn15PzwlLkZ2xpw==", "signatures": [{"sig": "MEYCIQD9mXUVwxqnxyf0zrQSorsAOFkTBNrCO59JxGacwkHEzwIhANqc4hWxvqB+5EMkcR/5zrlJXYgwEWG1N7yZje8H+ZXR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203664}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "d77c8397e387e28745ee8b66723367e0bfc70fc0", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "8.1.2", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~7.0.7", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request_2.84.0_1520844034135_0.14831336774671255", "host": "s3://npm-registry-packages"}}, "2.85.0": {"name": "request", "version": "2.85.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.85.0", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "5a03615a47c61420b3eb99b7dba204f83603e1fa", "tarball": "https://registry.npmjs.org/request/-/request-2.85.0.tgz", "fileCount": 16, "integrity": "sha512-8H7Ehijd4js+s6wuVPLjwORxD4zeuyjYugprdOXlPSqaApmL/QOy+EB/beICHVCHkGMKNh5rvihb5ov+IDw4mg==", "signatures": [{"sig": "MEUCIQCZhZNpjAiXiiWCUQ9uwM0TsXsKoEVMccPkdq4mVhGyygIgJXPdnb8zz1oyi39aGDJ/K/9eTXbzw6QwYXuwQoCp1Lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203792}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "21ef363b91c17763d6c79a639a197bf72135b97a", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "8.1.2", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request_2.85.0_1520851069876_0.369555657762632", "host": "s3://npm-registry-packages"}}, "2.86.0": {"name": "request", "version": "2.86.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.86.0", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "2b9497f449b0a32654c081a5cf426bbfb5bf5b69", "tarball": "https://registry.npmjs.org/request/-/request-2.86.0.tgz", "fileCount": 16, "integrity": "sha512-BQZih67o9r+Ys94tcIW4S7Uu8pthjrQVxhsZ/weOwHbDfACxvIyvnAbzFQxjy1jMtvFSzv5zf4my6cZsJBbVzw==", "signatures": [{"sig": "MEQCIHUguJal/cBLZgg4aMw0e3Xw5qLgvrLWDmEQhw0asL99AiBuNFDzzd67m2C81oPJ/ZWnbMalKWLJEKkpnX1CZHc/Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+s08CRA9TVsSAnZWagAAbbkP/3c/1i/9l1U9DZKqfTL1\nv0mY8Dlvq/wuz8+4+IJiSTbenEK2SKuqbs1WxfLd8waekoaOWfHchodvFNQk\n2kpRvx9xq8GxNKZHty+VerGMGz9BDRl8LdeaenXBaChTCtFOc38xTf4ALwGJ\nZchyVJwij1aP9gHkY1oBsJdwh4VDrPNh3YT1IS90ExhaJ0GZI8Tx5t5i7xj6\npHwHcXh427BKyzOOe79YvyhVsORrQ+5RXYhzBiI+sLtKAsEhyi9Uu2D98YZw\nGJo4BFRltxooP1Zn3CDj+7sQZnQHK3Cr1gYS301RScVB54BMWvB+z1DHt/89\n8mOxrBMUa5/XmyD1sHwsgn7uiDMONx1ZtqOWy9q9Q9El+9pOlRKVZGbagnC/\nKKpFciZgcWbbKIC+MLjXlkREFTLr14NNd1a4C8tbBNucR3RMpPkPfUY0K86N\n0/QgEkYt1+Y+X0zdHtrah9SaOXTnDY3OQDqE8lY35zflf3iyeRjY1JvFZBH0\nZu/elUyGc3Uq7Kx1+mE3LJwbyIqiUbheH0yb8qPrwUjqbXORP09QOJiX5Z7b\naJTZV/L0qi7nt19W79wazRX9fbbJPmaToBCmWv9ghhcFXIKbmfmkRX+ntudb\n8EbEY0yF1Zl/Y3XAaHKG+tE3NkHeIIRjk8SGs2Vz+sU5JifMLoNNta43GkQN\neNGu\r\n=2QBw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "8f2fd4d4d576833eb8379cd4f7b66a8c7cdf79f3", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "8.11.1", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "hawk": "~6.0.2", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request_2.86.0_1526385978113_0.22563835624780104", "host": "s3://npm-registry-packages"}}, "2.87.0": {"name": "request", "version": "2.87.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.87.0", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "32f00235cd08d482b4d0d68db93a829c0ed5756e", "tarball": "https://registry.npmjs.org/request/-/request-2.87.0.tgz", "fileCount": 17, "integrity": "sha512-fcogkm7Az5bsS6Sl0sibkbhcKsnyon/jV1kF3ajGmF0c8HrttdKTPRT9hieOaQHA5HEq6r8OyWOo/o781C1tNw==", "signatures": [{"sig": "MEQCIByUW0qUVouvpNZ5ek8rVDna2dO1N4Xy1GvU7BM69g59AiBuEs6tld7FIQJo8spzlrqcp7q067w7+zqa8wE6RhRn7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAnapCRA9TVsSAnZWagAAn44P/3RyMkTB2JOuitF4Ar5t\nkc2Ma4HuCxaiFpI/2sXHN/kzYm1Vsv0W4PGeyIxPqnKu99zbUGdVPi27lfDU\nmWADUxX0ujUH5L/BoivUi/sMGvroKGMrwhO2DZlVHdfl6y6dMLyqQ7XLT7EH\nu5VJCGbmBnWfrL3ZjvHoIXOW7byAXHXoNBD85cX3s4J+wOQ7ZiWoCjfM9/9G\nSbQPYO+Ji5UNT6706DTh+doBuI9fXsdugh/9gPMGvpm9leYQ2r8KRAisuENj\nZvYwBcTq8PPtdHvwT6dKIe/HYm6IwI+f3U7rVcHjHvwb4a9RmU5jPmIqwM5v\nEj5GqFq18aoRsQkv0ktOnGBMBoklx867F2rdQZjXd+GrMzbWBAQjYpFui/pK\nlWLRz5G4FL8O7wYbGHgXYOwfkP1am5guF6FOvfq2i5ajK6gIz5JG9Ln0RUYk\nKVqfMGYWbKvt1UQCaAHqjfyq1TvMS1rrouQeJtmhbf3RqAXecw1LbZsA6SxT\n+EmP28zEcmJuuo77v6y06g4XZZ1snmGnlEx4NfweGuMtRJmTGo/mUO7aGLmx\nK2oNJGLf2TJpTPSnmysglUirr5D0RZ/mmvfmrWqxWsD+Jyngks0gD47BjCcZ\nrmUcaFslqAcNV2iY2mASr8Dts/vZfchcUSxCkHhAN7As4EsBv07ziDVlS2AR\n/gk2\r\n=6VhQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "02fc5b1f0123173c308a79c43e804f6fcbefbbaf", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "8.11.1", "dependencies": {"qs": "~6.5.1", "aws4": "^1.6.0", "uuid": "^3.1.0", "extend": "~3.0.1", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "safe-buffer": "^5.1.1", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.0.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.5", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "karma": "^1.1.1", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^2.0.2", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^2.11.4", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request_2.87.0_1526888102675_0.008639832585368934", "host": "s3://npm-registry-packages"}}, "2.88.0": {"name": "request", "version": "2.88.0", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.88.0", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "9c2fca4f7d35b592efe57c7f0a55e81052124fef", "tarball": "https://registry.npmjs.org/request/-/request-2.88.0.tgz", "fileCount": 17, "integrity": "sha512-NAqBSrijGLZdM0WZNsInLJpkJokL72XYjUpnB0iwsRgxh7dB6COrHnTBNwN0E+lHDAJzu7kLAkDeY08z2/A0hg==", "signatures": [{"sig": "MEYCIQCHVFoYUkIMyk75VggkcxvjasHT4hGrHsQ6/nPzknNjjwIhAMZ5luEIDrX4IKaZrB3mJoAy8rdu/3ofu7tjHQAY/qE4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbbzLCRA9TVsSAnZWagAAhRcP/0ha56tDuCS2+dV<PERSON><PERSON><PERSON>\ngQLIBd2yWuZImcDNxU6k30tNhKWfzxannP+vsAFV+qRvP2pUnE7hG6Zx8OLL\nm1kck/YWtBuSCrhYb402bTSKRUCbhWdW0+ytYfDsgDkskKtY85SFtr3hauOm\nmeBkpSLiwu2EosMzjxY8jntyYPDOIK9tXTYe/Lm6ZMx40CGZo5mlS4mktfXF\n/HVfP+zjp4Gr/wBqfArJmuBKWozuKxorsHbjuRvcaQ6hDPW47mhbDFaPhWo0\n4/3u1w9enypd4LvHpVCcbKM3OdYGIcWCiy7YvJSjZHkNAR1G7yhUnLh3jQBh\nS5j4Iuif3ph6IO6DwCHFvkXzHYM5lasdJT0nXtJiF6+XUJIQtpsIDs6B3jwR\naxTE8u1BqWFq4AyHQ6xzGro/Wrn7toDuqWlsKbBeX7qFFrCYz3rYTWrM4ghH\nB57PgOQsPPn5y2Y8gRPFdQJzEs38xdFaZNWwrQUr+ZDA/yM3My9SEWr3ZcOc\ndBsYb6AH4GMHinFM6ktHpmQB7AwY+lKzY3BU3NzvN0ZwuHtg8YtdEF+NMXhG\n3NnykA8h88Oy5P4m7emNm0DJj3J1lmMd26SQybPrXta1gBFPtuvyXpM7K6HM\nQzfagTktQNzxCT8pE8voz9z2eDnZMbXkSx2RT7ejZbHdkphTfw4riRQ97hpF\niSWD\r\n=3tmo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib/", "index.js", "request.js"], "engines": {"node": ">= 4"}, "gitHead": "642024036379239a7fa29c27ef7bb4dd3fa3b3a4", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "istanbul cover tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "10.5.0", "dependencies": {"qs": "~6.5.2", "aws4": "^1.8.0", "uuid": "^3.3.2", "extend": "~3.0.2", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.2", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.1.0", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.6", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.0", "karma": "^3.0.0", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^3.0.4", "bluebird": "^3.2.1", "istanbul": "^0.4.0", "standard": "^9.0.0", "coveralls": "^3.0.2", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request_2.88.0_1533918410800_0.13178047716290342", "host": "s3://npm-registry-packages"}}, "2.88.2": {"name": "request", "version": "2.88.2", "keywords": ["http", "simple", "util", "utility"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "request@2.88.2", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "homepage": "https://github.com/request/request#readme", "bugs": {"url": "http://github.com/request/request/issues"}, "dist": {"shasum": "d73c918731cb5a87da047e207234146f664d12b3", "tarball": "https://registry.npmjs.org/request/-/request-2.88.2.tgz", "fileCount": 17, "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "signatures": [{"sig": "MEQCIFj6xTRl6fMJKw6ARRHIknwsRjtcirXlYWkzpg0kVEPYAiBsTXEj1GF7e9LdNxMAnxRMGbWnCOmuv+VsNPXr9ynwnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQtfYCRA9TVsSAnZWagAA59oQAJLH1NJl5fM1Km3ck/W2\ncmx/3u1fgojd4MKqWSnmykUMoEAQ6qEDQ0/WVDbCkRRtynWTA8ZPdD4pIKUk\nBcfmlibHMAy4HGDIXS+fIM7jVO8vqfVefTkOPUtsDjEvGZxE4IwXN6PEhVaZ\nAQ6UaJbLUt3M3j5VbZCREXjhdq90tCqjmb/2NuuijTHScxexzzBbuBjfVqsO\nR2v/So/iaMQvJOOObiImdxZNzwwPZ5zDEFpfv3jGnG1m/Os93yNFrJ59cy0D\nMaphon1TVDrZRt7g0vn88TCIbl/bikiaa0OpTAgVtxXSkA15dcllmSsTbc5p\nfkHADrX8AdTqij9Qyir4p363UaTMfASJDLPsEDOO37zKNzt8Yzak5H+XE5oQ\ns+VF2DxTfUf0ODZBwswp+eXImmZsskkPU+Hgpw5pu9wt/vtJ/ZNupy3pIeAM\nr0gkqSNQctSUevhAcFM7dhzG2D5Htgh0905vkxE8Rio4VoD85ic/U3klAcD5\n6uWMKSDgBTgt7hb6A7npM5lqK5IK9kS4Ni9jKit/Fl7kDHpStj6w2y6tq0dj\nsoq0CB6NjGTte9/fB6Eo19DtUBlIt07LdGjqpEj0c1gYErl2Ho7JCM/hi9ha\nkhbLSs1EfH4QM2n71bUm2UGKRnXTtFexRJBzk2rScAll5N8P13eTEB879Lat\n+cyh\r\n=cj06\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 6"}, "gitHead": "16a7d46f251b2fc1930446800c8d2fb379cd4311", "scripts": {"lint": "standard", "test": "npm run lint && npm run test-ci && npm run test-browser", "test-ci": "taper tests/test-*.js", "test-cov": "nyc --reporter=lcov tape tests/test-*.js", "test-browser": "node tests/browser/start.js"}, "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Simplified HTTP request client.", "directories": {}, "greenkeeper": {"ignore": ["hawk", "har-validator"]}, "_nodeVersion": "12.13.1", "dependencies": {"qs": "~6.5.2", "aws4": "^1.8.0", "uuid": "^3.3.2", "extend": "~3.0.2", "caseless": "~0.12.0", "isstream": "~0.1.2", "aws-sign2": "~0.7.0", "form-data": "~2.3.2", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "forever-agent": "~0.6.1", "har-validator": "~5.1.3", "is-typedarray": "~1.0.0", "http-signature": "~1.2.0", "combined-stream": "~1.0.6", "performance-now": "^2.1.0", "json-stringify-safe": "~5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.1", "tape": "^4.6.0", "karma": "^3.0.0", "taper": "^0.5.0", "rimraf": "^2.2.8", "codecov": "^3.0.4", "bluebird": "^3.2.1", "standard": "^9.0.0", "coveralls": "^3.0.2", "karma-cli": "^1.0.0", "karma-tap": "^3.0.1", "browserify": "^13.0.1", "buffer-equal": "^1.0.0", "function-bind": "^1.0.2", "karma-coverage": "^1.0.0", "server-destroy": "^1.0.1", "karma-browserify": "^5.0.1", "phantomjs-prebuilt": "^2.1.3", "browserify-istanbul": "^2.0.0", "karma-phantomjs-launcher": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/request_2.88.2_1581438935876_0.19193206646405736", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-01-22T00:36:12.640Z", "modified": "2024-10-30T14:41:05.915Z", "0.9.0": "2011-01-22T00:36:12.640Z", "0.8.3": "2011-01-22T00:36:12.640Z", "0.10.0": "2011-01-22T00:36:12.640Z", "0.9.1": "2011-01-22T00:36:12.640Z", "0.9.5": "2011-01-22T00:36:12.640Z", "1.0.0": "2011-01-22T00:36:12.640Z", "1.1.0": "2011-01-23T01:14:46.626Z", "1.1.1": "2011-01-23T01:38:57.823Z", "1.2.0": "2011-01-30T22:05:41.553Z", "1.9.0": "2011-02-11T00:10:06.903Z", "1.9.1": "2011-03-22T18:07:16.344Z", "1.9.2": "2011-03-22T18:29:21.464Z", "1.9.3": "2011-03-22T18:32:57.223Z", "1.9.5": "2011-03-27T22:30:25.139Z", "1.9.7": "2011-06-23T17:36:13.839Z", "1.9.8": "2011-06-23T21:15:20.971Z", "1.9.9": "2011-07-21T02:03:21.081Z", "2.0.0": "2011-07-21T21:10:38.897Z", "2.0.1": "2011-07-21T22:22:13.282Z", "2.0.2": "2011-07-29T20:48:36.410Z", "2.0.3": "2011-08-12T23:16:25.100Z", "2.0.4": "2011-08-13T21:28:21.109Z", "2.0.5": "2011-08-13T21:46:39.966Z", "2.1.0": "2011-08-15T04:03:17.126Z", "2.1.1": "2011-08-23T03:59:30.206Z", "2.2.0": "2011-11-06T01:40:00.212Z", "2.2.5": "2011-11-17T06:35:04.405Z", "2.2.6": "2011-12-01T07:38:36.311Z", "2.2.9": "2011-12-01T08:39:41.637Z", "2.9.0": "2011-12-28T00:47:33.584Z", "2.9.1": "2011-12-28T01:02:20.539Z", "2.9.2": "2011-12-28T01:04:02.634Z", "2.9.3": "2011-12-28T01:49:19.797Z", "2.9.100": "2012-01-20T21:25:15.722Z", "2.9.150": "2012-02-24T17:53:29.835Z", "2.9.151": "2012-02-24T23:08:55.848Z", "2.9.152": "2012-02-25T20:55:24.387Z", "2.9.153": "2012-03-01T23:43:34.140Z", "2.9.200": "2012-04-08T00:41:38.386Z", "2.9.201": "2012-04-12T17:44:37.172Z", "2.9.202": "2012-04-14T01:48:20.232Z", "2.9.203": "2012-06-28T19:58:58.857Z", "2.10.0": "2012-08-01T20:56:37.322Z", "2.11.0": "2012-08-29T19:18:28.340Z", "2.11.1": "2012-09-04T15:20:46.781Z", "2.11.2": "2012-09-17T19:19:33.839Z", "2.11.3": "2012-09-17T19:20:14.479Z", "2.11.4": "2012-09-17T19:34:20.945Z", "2.12.0": "2012-11-09T21:49:57.215Z", "2.14.0": "2013-02-19T23:53:42.323Z", "2.16.0": "2013-03-13T17:48:37.937Z", "2.16.2": "2013-03-13T20:46:28.455Z", "2.16.4": "2013-03-18T19:16:10.266Z", "2.16.6": "2013-03-18T22:48:42.025Z", "2.18.0": "2013-04-22T15:53:37.983Z", "2.19.0": "2013-04-22T16:48:30.477Z", "2.20.0": "2013-04-22T21:49:15.616Z", "2.21.0": "2013-04-30T21:28:44.759Z", "2.22.0": "2013-07-05T17:12:48.170Z", "2.23.0": "2013-07-23T02:45:03.153Z", "2.24.0": "2013-07-23T20:51:33.068Z", "2.25.0": "2013-07-23T21:51:30.696Z", "2.26.0": "2013-08-07T16:31:07.773Z", "2.27.0": "2013-08-15T21:30:34.410Z", "2.28.0": "2013-12-04T19:42:46.371Z", "2.29.0": "2013-12-06T20:05:35.433Z", "2.30.0": "2013-12-13T19:17:56.851Z", "2.31.0": "2014-01-08T02:57:12.510Z", "2.32.0": "2014-01-16T19:33:18.069Z", "2.33.0": "2014-01-16T19:48:02.613Z", "2.34.0": "2014-02-18T19:35:27.908Z", "2.35.0": "2014-05-17T20:56:43.641Z", "2.36.0": "2014-05-19T20:58:14.683Z", "2.37.0": "2014-07-07T17:24:45.290Z", "2.38.0": "2014-07-22T13:44:48.332Z", "2.39.0": "2014-07-24T02:20:50.881Z", "2.40.0": "2014-08-06T18:29:34.302Z", "2.41.0": "2014-09-04T20:36:17.279Z", "2.42.0": "2014-09-04T22:24:16.945Z", "2.43.0": "2014-09-18T10:51:34.944Z", "2.44.0": "2014-09-18T10:53:09.149Z", "2.45.0": "2014-10-06T00:06:02.756Z", "2.46.0": "2014-10-23T16:34:08.339Z", "2.47.0": "2014-10-26T23:52:13.024Z", "2.48.0": "2014-11-12T17:08:56.247Z", "2.49.0": "2014-11-28T18:12:39.597Z", "2.50.0": "2014-12-09T15:36:36.588Z", "2.51.0": "2014-12-10T15:08:10.339Z", "2.52.0": "2015-02-02T00:58:58.406Z", "2.53.0": "2015-02-02T16:09:19.191Z", "2.54.0": "2015-03-24T22:01:04.401Z", "2.55.0": "2015-04-05T04:24:35.395Z", "2.56.0": "2015-05-28T18:03:06.887Z", "2.57.0": "2015-05-31T19:04:35.520Z", "2.58.0": "2015-06-16T11:28:02.894Z", "2.59.0": "2015-07-20T08:49:43.418Z", "2.60.0": "2015-07-21T12:29:31.604Z", "2.61.0": "2015-08-19T15:39:45.178Z", "2.62.0": "2015-09-15T08:23:47.863Z", "2.63.0": "2015-09-21T14:00:39.341Z", "2.64.0": "2015-09-25T12:21:27.306Z", "2.65.0": "2015-10-11T18:04:53.725Z", "2.66.0": "2015-11-18T10:07:33.695Z", "2.67.0": "2015-11-19T07:45:48.220Z", "2.68.0": "2016-01-27T16:20:27.766Z", "2.69.0": "2016-01-27T19:00:03.126Z", "2.70.0": "2016-04-05T10:07:28.642Z", "2.71.0": "2016-04-12T13:09:36.572Z", "2.72.0": "2016-04-17T13:53:37.912Z", "2.73.0": "2016-07-09T07:43:25.258Z", "2.74.0": "2016-07-22T23:44:44.438Z", "2.75.0": "2016-09-17T22:33:28.885Z", "2.76.0": "2016-10-25T08:57:56.992Z", "2.77.0": "2016-11-03T11:17:58.147Z", "2.78.0": "2016-11-03T13:38:05.614Z", "2.79.0": "2016-11-18T17:21:08.710Z", "2.80.0": "2017-03-04T04:42:42.782Z", "2.81.0": "2017-03-09T15:56:47.595Z", "2.82.0": "2017-09-19T19:39:30.271Z", "2.83.0": "2017-09-27T03:00:36.500Z", "2.84.0": "2018-03-12T08:40:34.279Z", "2.85.0": "2018-03-12T10:37:49.925Z", "2.86.0": "2018-05-15T12:06:18.253Z", "2.87.0": "2018-05-21T07:35:02.834Z", "2.88.0": "2018-08-10T16:26:50.936Z", "2.88.2": "2020-02-11T16:35:36.122Z"}, "bugs": {"url": "http://github.com/request/request/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://github.com/request/request#readme", "keywords": ["http", "simple", "util", "utility"], "repository": {"url": "git+https://github.com/request/request.git", "type": "git"}, "description": "Simplified HTTP request client.", "maintainers": [{"name": "f<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "readme": "# Deprecated!\n\nAs of Feb 11th 2020, request is fully deprecated. No new changes are expected land. In fact, none have landed for some time.\n\nFor more information about why request is deprecated and possible alternatives refer to\n[this issue](https://github.com/request/request/issues/3142).\n\n# Request - Simplified HTTP client\n\n[![npm package](https://nodei.co/npm/request.png?downloads=true&downloadRank=true&stars=true)](https://nodei.co/npm/request/)\n\n[![Build status](https://img.shields.io/travis/request/request/master.svg?style=flat-square)](https://travis-ci.org/request/request)\n[![Coverage](https://img.shields.io/codecov/c/github/request/request.svg?style=flat-square)](https://codecov.io/github/request/request?branch=master)\n[![Coverage](https://img.shields.io/coveralls/request/request.svg?style=flat-square)](https://coveralls.io/r/request/request)\n[![Dependency Status](https://img.shields.io/david/request/request.svg?style=flat-square)](https://david-dm.org/request/request)\n[![Known Vulnerabilities](https://snyk.io/test/npm/request/badge.svg?style=flat-square)](https://snyk.io/test/npm/request)\n[![Gitter](https://img.shields.io/badge/gitter-join_chat-blue.svg?style=flat-square)](https://gitter.im/request/request?utm_source=badge)\n\n\n## Super simple to use\n\nRequest is designed to be the simplest way possible to make http calls. It supports HTTPS and follows redirects by default.\n\n```js\nconst request = require('request');\nrequest('http://www.google.com', function (error, response, body) {\n  console.error('error:', error); // Print the error if one occurred\n  console.log('statusCode:', response && response.statusCode); // Print the response status code if a response was received\n  console.log('body:', body); // Print the HTML for the Google homepage.\n});\n```\n\n\n## Table of contents\n\n- [Streaming](#streaming)\n- [Promises & Async/Await](#promises--asyncawait)\n- [Forms](#forms)\n- [HTTP Authentication](#http-authentication)\n- [Custom HTTP Headers](#custom-http-headers)\n- [OAuth Signing](#oauth-signing)\n- [Proxies](#proxies)\n- [Unix Domain Sockets](#unix-domain-sockets)\n- [TLS/SSL Protocol](#tlsssl-protocol)\n- [Support for HAR 1.2](#support-for-har-12)\n- [**All Available Options**](#requestoptions-callback)\n\nRequest also offers [convenience methods](#convenience-methods) like\n`request.defaults` and `request.post`, and there are\nlots of [usage examples](#examples) and several\n[debugging techniques](#debugging).\n\n\n---\n\n\n## Streaming\n\nYou can stream any response to a file stream.\n\n```js\nrequest('http://google.com/doodle.png').pipe(fs.createWriteStream('doodle.png'))\n```\n\nYou can also stream a file to a PUT or POST request. This method will also check the file extension against a mapping of file extensions to content-types (in this case `application/json`) and use the proper `content-type` in the PUT request (if the headers don’t already provide one).\n\n```js\nfs.createReadStream('file.json').pipe(request.put('http://mysite.com/obj.json'))\n```\n\nRequest can also `pipe` to itself. When doing so, `content-type` and `content-length` are preserved in the PUT headers.\n\n```js\nrequest.get('http://google.com/img.png').pipe(request.put('http://mysite.com/img.png'))\n```\n\nRequest emits a \"response\" event when a response is received. The `response` argument will be an instance of [http.IncomingMessage](https://nodejs.org/api/http.html#http_class_http_incomingmessage).\n\n```js\nrequest\n  .get('http://google.com/img.png')\n  .on('response', function(response) {\n    console.log(response.statusCode) // 200\n    console.log(response.headers['content-type']) // 'image/png'\n  })\n  .pipe(request.put('http://mysite.com/img.png'))\n```\n\nTo easily handle errors when streaming requests, listen to the `error` event before piping:\n\n```js\nrequest\n  .get('http://mysite.com/doodle.png')\n  .on('error', function(err) {\n    console.error(err)\n  })\n  .pipe(fs.createWriteStream('doodle.png'))\n```\n\nNow let’s get fancy.\n\n```js\nhttp.createServer(function (req, resp) {\n  if (req.url === '/doodle.png') {\n    if (req.method === 'PUT') {\n      req.pipe(request.put('http://mysite.com/doodle.png'))\n    } else if (req.method === 'GET' || req.method === 'HEAD') {\n      request.get('http://mysite.com/doodle.png').pipe(resp)\n    }\n  }\n})\n```\n\nYou can also `pipe()` from `http.ServerRequest` instances, as well as to `http.ServerResponse` instances. The HTTP method, headers, and entity-body data will be sent. Which means that, if you don't really care about security, you can do:\n\n```js\nhttp.createServer(function (req, resp) {\n  if (req.url === '/doodle.png') {\n    const x = request('http://mysite.com/doodle.png')\n    req.pipe(x)\n    x.pipe(resp)\n  }\n})\n```\n\nAnd since `pipe()` returns the destination stream in ≥ Node 0.5.x you can do one line proxying. :)\n\n```js\nreq.pipe(request('http://mysite.com/doodle.png')).pipe(resp)\n```\n\nAlso, none of this new functionality conflicts with requests previous features, it just expands them.\n\n```js\nconst r = request.defaults({'proxy':'http://localproxy.com'})\n\nhttp.createServer(function (req, resp) {\n  if (req.url === '/doodle.png') {\n    r.get('http://google.com/doodle.png').pipe(resp)\n  }\n})\n```\n\nYou can still use intermediate proxies, the requests will still follow HTTP forwards, etc.\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## Promises & Async/Await\n\n`request` supports both streaming and callback interfaces natively. If you'd like `request` to return a Promise instead, you can use an alternative interface wrapper for `request`. These wrappers can be useful if you prefer to work with Promises, or if you'd like to use `async`/`await` in ES2017.\n\nSeveral alternative interfaces are provided by the request team, including:\n- [`request-promise`](https://github.com/request/request-promise) (uses [Bluebird](https://github.com/petkaantonov/bluebird) Promises)\n- [`request-promise-native`](https://github.com/request/request-promise-native) (uses native Promises)\n- [`request-promise-any`](https://github.com/request/request-promise-any) (uses [any-promise](https://www.npmjs.com/package/any-promise) Promises)\n\nAlso, [`util.promisify`](https://nodejs.org/api/util.html#util_util_promisify_original), which is available from Node.js v8.0 can be used to convert a regular function that takes a callback to return a promise instead.\n\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## Forms\n\n`request` supports `application/x-www-form-urlencoded` and `multipart/form-data` form uploads. For `multipart/related` refer to the `multipart` API.\n\n\n#### application/x-www-form-urlencoded (URL-Encoded Forms)\n\nURL-encoded forms are simple.\n\n```js\nrequest.post('http://service.com/upload', {form:{key:'value'}})\n// or\nrequest.post('http://service.com/upload').form({key:'value'})\n// or\nrequest.post({url:'http://service.com/upload', form: {key:'value'}}, function(err,httpResponse,body){ /* ... */ })\n```\n\n\n#### multipart/form-data (Multipart Form Uploads)\n\nFor `multipart/form-data` we use the [form-data](https://github.com/form-data/form-data) library by [@felixge](https://github.com/felixge). For the most cases, you can pass your upload form data via the `formData` option.\n\n\n```js\nconst formData = {\n  // Pass a simple key-value pair\n  my_field: 'my_value',\n  // Pass data via Buffers\n  my_buffer: Buffer.from([1, 2, 3]),\n  // Pass data via Streams\n  my_file: fs.createReadStream(__dirname + '/unicycle.jpg'),\n  // Pass multiple values /w an Array\n  attachments: [\n    fs.createReadStream(__dirname + '/attachment1.jpg'),\n    fs.createReadStream(__dirname + '/attachment2.jpg')\n  ],\n  // Pass optional meta-data with an 'options' object with style: {value: DATA, options: OPTIONS}\n  // Use case: for some types of streams, you'll need to provide \"file\"-related information manually.\n  // See the `form-data` README for more information about options: https://github.com/form-data/form-data\n  custom_file: {\n    value:  fs.createReadStream('/dev/urandom'),\n    options: {\n      filename: 'topsecret.jpg',\n      contentType: 'image/jpeg'\n    }\n  }\n};\nrequest.post({url:'http://service.com/upload', formData: formData}, function optionalCallback(err, httpResponse, body) {\n  if (err) {\n    return console.error('upload failed:', err);\n  }\n  console.log('Upload successful!  Server responded with:', body);\n});\n```\n\nFor advanced cases, you can access the form-data object itself via `r.form()`. This can be modified until the request is fired on the next cycle of the event-loop. (Note that this calling `form()` will clear the currently set form data for that request.)\n\n```js\n// NOTE: Advanced use-case, for normal use see 'formData' usage above\nconst r = request.post('http://service.com/upload', function optionalCallback(err, httpResponse, body) {...})\nconst form = r.form();\nform.append('my_field', 'my_value');\nform.append('my_buffer', Buffer.from([1, 2, 3]));\nform.append('custom_file', fs.createReadStream(__dirname + '/unicycle.jpg'), {filename: 'unicycle.jpg'});\n```\nSee the [form-data README](https://github.com/form-data/form-data) for more information & examples.\n\n\n#### multipart/related\n\nSome variations in different HTTP implementations require a newline/CRLF before, after, or both before and after the boundary of a `multipart/related` request (using the multipart option). This has been observed in the .NET WebAPI version 4.0. You can turn on a boundary preambleCRLF or postamble by passing them as `true` to your request options.\n\n```js\n  request({\n    method: 'PUT',\n    preambleCRLF: true,\n    postambleCRLF: true,\n    uri: 'http://service.com/upload',\n    multipart: [\n      {\n        'content-type': 'application/json',\n        body: JSON.stringify({foo: 'bar', _attachments: {'message.txt': {follows: true, length: 18, 'content_type': 'text/plain' }}})\n      },\n      { body: 'I am an attachment' },\n      { body: fs.createReadStream('image.png') }\n    ],\n    // alternatively pass an object containing additional options\n    multipart: {\n      chunked: false,\n      data: [\n        {\n          'content-type': 'application/json',\n          body: JSON.stringify({foo: 'bar', _attachments: {'message.txt': {follows: true, length: 18, 'content_type': 'text/plain' }}})\n        },\n        { body: 'I am an attachment' }\n      ]\n    }\n  },\n  function (error, response, body) {\n    if (error) {\n      return console.error('upload failed:', error);\n    }\n    console.log('Upload successful!  Server responded with:', body);\n  })\n```\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## HTTP Authentication\n\n```js\nrequest.get('http://some.server.com/').auth('username', 'password', false);\n// or\nrequest.get('http://some.server.com/', {\n  'auth': {\n    'user': 'username',\n    'pass': 'password',\n    'sendImmediately': false\n  }\n});\n// or\nrequest.get('http://some.server.com/').auth(null, null, true, 'bearerToken');\n// or\nrequest.get('http://some.server.com/', {\n  'auth': {\n    'bearer': 'bearerToken'\n  }\n});\n```\n\nIf passed as an option, `auth` should be a hash containing values:\n\n- `user` || `username`\n- `pass` || `password`\n- `sendImmediately` (optional)\n- `bearer` (optional)\n\nThe method form takes parameters\n`auth(username, password, sendImmediately, bearer)`.\n\n`sendImmediately` defaults to `true`, which causes a basic or bearer\nauthentication header to be sent. If `sendImmediately` is `false`, then\n`request` will retry with a proper authentication header after receiving a\n`401` response from the server (which must contain a `WWW-Authenticate` header\nindicating the required authentication method).\n\nNote that you can also specify basic authentication using the URL itself, as\ndetailed in [RFC 1738](http://www.ietf.org/rfc/rfc1738.txt). Simply pass the\n`user:password` before the host with an `@` sign:\n\n```js\nconst username = 'username',\n    password = 'password',\n    url = 'http://' + username + ':' + password + '@some.server.com';\n\nrequest({url}, function (error, response, body) {\n   // Do more stuff with 'body' here\n});\n```\n\nDigest authentication is supported, but it only works with `sendImmediately`\nset to `false`; otherwise `request` will send basic authentication on the\ninitial request, which will probably cause the request to fail.\n\nBearer authentication is supported, and is activated when the `bearer` value is\navailable. The value may be either a `String` or a `Function` returning a\n`String`. Using a function to supply the bearer token is particularly useful if\nused in conjunction with `defaults` to allow a single function to supply the\nlast known token at the time of sending a request, or to compute one on the fly.\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## Custom HTTP Headers\n\nHTTP Headers, such as `User-Agent`, can be set in the `options` object.\nIn the example below, we call the github API to find out the number\nof stars and forks for the request repository. This requires a\ncustom `User-Agent` header as well as https.\n\n```js\nconst request = require('request');\n\nconst options = {\n  url: 'https://api.github.com/repos/request/request',\n  headers: {\n    'User-Agent': 'request'\n  }\n};\n\nfunction callback(error, response, body) {\n  if (!error && response.statusCode == 200) {\n    const info = JSON.parse(body);\n    console.log(info.stargazers_count + \" Stars\");\n    console.log(info.forks_count + \" Forks\");\n  }\n}\n\nrequest(options, callback);\n```\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## OAuth Signing\n\n[OAuth version 1.0](https://tools.ietf.org/html/rfc5849) is supported. The\ndefault signing algorithm is\n[HMAC-SHA1](https://tools.ietf.org/html/rfc5849#section-3.4.2):\n\n```js\n// OAuth1.0 - 3-legged server side flow (Twitter example)\n// step 1\nconst qs = require('querystring')\n  , oauth =\n    { callback: 'http://mysite.com/callback/'\n    , consumer_key: CONSUMER_KEY\n    , consumer_secret: CONSUMER_SECRET\n    }\n  , url = 'https://api.twitter.com/oauth/request_token'\n  ;\nrequest.post({url:url, oauth:oauth}, function (e, r, body) {\n  // Ideally, you would take the body in the response\n  // and construct a URL that a user clicks on (like a sign in button).\n  // The verifier is only available in the response after a user has\n  // verified with twitter that they are authorizing your app.\n\n  // step 2\n  const req_data = qs.parse(body)\n  const uri = 'https://api.twitter.com/oauth/authenticate'\n    + '?' + qs.stringify({oauth_token: req_data.oauth_token})\n  // redirect the user to the authorize uri\n\n  // step 3\n  // after the user is redirected back to your server\n  const auth_data = qs.parse(body)\n    , oauth =\n      { consumer_key: CONSUMER_KEY\n      , consumer_secret: CONSUMER_SECRET\n      , token: auth_data.oauth_token\n      , token_secret: req_data.oauth_token_secret\n      , verifier: auth_data.oauth_verifier\n      }\n    , url = 'https://api.twitter.com/oauth/access_token'\n    ;\n  request.post({url:url, oauth:oauth}, function (e, r, body) {\n    // ready to make signed requests on behalf of the user\n    const perm_data = qs.parse(body)\n      , oauth =\n        { consumer_key: CONSUMER_KEY\n        , consumer_secret: CONSUMER_SECRET\n        , token: perm_data.oauth_token\n        , token_secret: perm_data.oauth_token_secret\n        }\n      , url = 'https://api.twitter.com/1.1/users/show.json'\n      , qs =\n        { screen_name: perm_data.screen_name\n        , user_id: perm_data.user_id\n        }\n      ;\n    request.get({url:url, oauth:oauth, qs:qs, json:true}, function (e, r, user) {\n      console.log(user)\n    })\n  })\n})\n```\n\nFor [RSA-SHA1 signing](https://tools.ietf.org/html/rfc5849#section-3.4.3), make\nthe following changes to the OAuth options object:\n* Pass `signature_method : 'RSA-SHA1'`\n* Instead of `consumer_secret`, specify a `private_key` string in\n  [PEM format](http://how2ssl.com/articles/working_with_pem_files/)\n\nFor [PLAINTEXT signing](http://oauth.net/core/1.0/#anchor22), make\nthe following changes to the OAuth options object:\n* Pass `signature_method : 'PLAINTEXT'`\n\nTo send OAuth parameters via query params or in a post body as described in The\n[Consumer Request Parameters](http://oauth.net/core/1.0/#consumer_req_param)\nsection of the oauth1 spec:\n* Pass `transport_method : 'query'` or `transport_method : 'body'` in the OAuth\n  options object.\n* `transport_method` defaults to `'header'`\n\nTo use [Request Body Hash](https://oauth.googlecode.com/svn/spec/ext/body_hash/1.0/oauth-bodyhash.html) you can either\n* Manually generate the body hash and pass it as a string `body_hash: '...'`\n* Automatically generate the body hash by passing `body_hash: true`\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## Proxies\n\nIf you specify a `proxy` option, then the request (and any subsequent\nredirects) will be sent via a connection to the proxy server.\n\nIf your endpoint is an `https` url, and you are using a proxy, then\nrequest will send a `CONNECT` request to the proxy server *first*, and\nthen use the supplied connection to connect to the endpoint.\n\nThat is, first it will make a request like:\n\n```\nHTTP/1.1 CONNECT endpoint-server.com:80\nHost: proxy-server.com\nUser-Agent: whatever user agent you specify\n```\n\nand then the proxy server make a TCP connection to `endpoint-server`\non port `80`, and return a response that looks like:\n\n```\nHTTP/1.1 200 OK\n```\n\nAt this point, the connection is left open, and the client is\ncommunicating directly with the `endpoint-server.com` machine.\n\nSee [the wikipedia page on HTTP Tunneling](https://en.wikipedia.org/wiki/HTTP_tunnel)\nfor more information.\n\nBy default, when proxying `http` traffic, request will simply make a\nstandard proxied `http` request. This is done by making the `url`\nsection of the initial line of the request a fully qualified url to\nthe endpoint.\n\nFor example, it will make a single request that looks like:\n\n```\nHTTP/1.1 GET http://endpoint-server.com/some-url\nHost: proxy-server.com\nOther-Headers: all go here\n\nrequest body or whatever\n```\n\nBecause a pure \"http over http\" tunnel offers no additional security\nor other features, it is generally simpler to go with a\nstraightforward HTTP proxy in this case. However, if you would like\nto force a tunneling proxy, you may set the `tunnel` option to `true`.\n\nYou can also make a standard proxied `http` request by explicitly setting\n`tunnel : false`, but **note that this will allow the proxy to see the traffic\nto/from the destination server**.\n\nIf you are using a tunneling proxy, you may set the\n`proxyHeaderWhiteList` to share certain headers with the proxy.\n\nYou can also set the `proxyHeaderExclusiveList` to share certain\nheaders only with the proxy and not with destination host.\n\nBy default, this set is:\n\n```\naccept\naccept-charset\naccept-encoding\naccept-language\naccept-ranges\ncache-control\ncontent-encoding\ncontent-language\ncontent-length\ncontent-location\ncontent-md5\ncontent-range\ncontent-type\nconnection\ndate\nexpect\nmax-forwards\npragma\nproxy-authorization\nreferer\nte\ntransfer-encoding\nuser-agent\nvia\n```\n\nNote that, when using a tunneling proxy, the `proxy-authorization`\nheader and any headers from custom `proxyHeaderExclusiveList` are\n*never* sent to the endpoint server, but only to the proxy server.\n\n\n### Controlling proxy behaviour using environment variables\n\nThe following environment variables are respected by `request`:\n\n * `HTTP_PROXY` / `http_proxy`\n * `HTTPS_PROXY` / `https_proxy`\n * `NO_PROXY` / `no_proxy`\n\nWhen `HTTP_PROXY` / `http_proxy` are set, they will be used to proxy non-SSL requests that do not have an explicit `proxy` configuration option present. Similarly, `HTTPS_PROXY` / `https_proxy` will be respected for SSL requests that do not have an explicit `proxy` configuration option. It is valid to define a proxy in one of the environment variables, but then override it for a specific request, using the `proxy` configuration option. Furthermore, the `proxy` configuration option can be explicitly set to false / null to opt out of proxying altogether for that request.\n\n`request` is also aware of the `NO_PROXY`/`no_proxy` environment variables. These variables provide a granular way to opt out of proxying, on a per-host basis. It should contain a comma separated list of hosts to opt out of proxying. It is also possible to opt of proxying when a particular destination port is used. Finally, the variable may be set to `*` to opt out of the implicit proxy configuration of the other environment variables.\n\nHere's some examples of valid `no_proxy` values:\n\n * `google.com` - don't proxy HTTP/HTTPS requests to Google.\n * `google.com:443` - don't proxy HTTPS requests to Google, but *do* proxy HTTP requests to Google.\n * `google.com:443, yahoo.com:80` - don't proxy HTTPS requests to Google, and don't proxy HTTP requests to Yahoo!\n * `*` - ignore `https_proxy`/`http_proxy` environment variables altogether.\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## UNIX Domain Sockets\n\n`request` supports making requests to [UNIX Domain Sockets](https://en.wikipedia.org/wiki/Unix_domain_socket). To make one, use the following URL scheme:\n\n```js\n/* Pattern */ 'http://unix:SOCKET:PATH'\n/* Example */ request.get('http://unix:/absolute/path/to/unix.socket:/request/path')\n```\n\nNote: The `SOCKET` path is assumed to be absolute to the root of the host file system.\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## TLS/SSL Protocol\n\nTLS/SSL Protocol options, such as `cert`, `key` and `passphrase`, can be\nset directly in `options` object, in the `agentOptions` property of the `options` object, or even in `https.globalAgent.options`. Keep in mind that, although `agentOptions` allows for a slightly wider range of configurations, the recommended way is via `options` object directly, as using `agentOptions` or `https.globalAgent.options` would not be applied in the same way in proxied environments (as data travels through a TLS connection instead of an http/https agent).\n\n```js\nconst fs = require('fs')\n    , path = require('path')\n    , certFile = path.resolve(__dirname, 'ssl/client.crt')\n    , keyFile = path.resolve(__dirname, 'ssl/client.key')\n    , caFile = path.resolve(__dirname, 'ssl/ca.cert.pem')\n    , request = require('request');\n\nconst options = {\n    url: 'https://api.some-server.com/',\n    cert: fs.readFileSync(certFile),\n    key: fs.readFileSync(keyFile),\n    passphrase: 'password',\n    ca: fs.readFileSync(caFile)\n};\n\nrequest.get(options);\n```\n\n### Using `options.agentOptions`\n\nIn the example below, we call an API that requires client side SSL certificate\n(in PEM format) with passphrase protected private key (in PEM format) and disable the SSLv3 protocol:\n\n```js\nconst fs = require('fs')\n    , path = require('path')\n    , certFile = path.resolve(__dirname, 'ssl/client.crt')\n    , keyFile = path.resolve(__dirname, 'ssl/client.key')\n    , request = require('request');\n\nconst options = {\n    url: 'https://api.some-server.com/',\n    agentOptions: {\n        cert: fs.readFileSync(certFile),\n        key: fs.readFileSync(keyFile),\n        // Or use `pfx` property replacing `cert` and `key` when using private key, certificate and CA certs in PFX or PKCS12 format:\n        // pfx: fs.readFileSync(pfxFilePath),\n        passphrase: 'password',\n        securityOptions: 'SSL_OP_NO_SSLv3'\n    }\n};\n\nrequest.get(options);\n```\n\nIt is able to force using SSLv3 only by specifying `secureProtocol`:\n\n```js\nrequest.get({\n    url: 'https://api.some-server.com/',\n    agentOptions: {\n        secureProtocol: 'SSLv3_method'\n    }\n});\n```\n\nIt is possible to accept other certificates than those signed by generally allowed Certificate Authorities (CAs).\nThis can be useful, for example,  when using self-signed certificates.\nTo require a different root certificate, you can specify the signing CA by adding the contents of the CA's certificate file to the `agentOptions`.\nThe certificate the domain presents must be signed by the root certificate specified:\n\n```js\nrequest.get({\n    url: 'https://api.some-server.com/',\n    agentOptions: {\n        ca: fs.readFileSync('ca.cert.pem')\n    }\n});\n```\n\nThe `ca` value can be an array of certificates, in the event you have a private or internal corporate public-key infrastructure hierarchy. For example, if you want to connect to https://api.some-server.com which presents a key chain consisting of:\n1. its own public key, which is signed by:\n2. an intermediate \"Corp Issuing Server\", that is in turn signed by: \n3. a root CA \"Corp Root CA\";\n\nyou can configure your request as follows:\n\n```js\nrequest.get({\n    url: 'https://api.some-server.com/',\n    agentOptions: {\n        ca: [\n          fs.readFileSync('Corp Issuing Server.pem'),\n          fs.readFileSync('Corp Root CA.pem')\n        ]\n    }\n});\n```\n\n[back to top](#table-of-contents)\n\n\n---\n\n## Support for HAR 1.2\n\nThe `options.har` property will override the values: `url`, `method`, `qs`, `headers`, `form`, `formData`, `body`, `json`, as well as construct multipart data and read files from disk when `request.postData.params[].fileName` is present without a matching `value`.\n\nA validation step will check if the HAR Request format matches the latest spec (v1.2) and will skip parsing if not matching.\n\n```js\n  const request = require('request')\n  request({\n    // will be ignored\n    method: 'GET',\n    uri: 'http://www.google.com',\n\n    // HTTP Archive Request Object\n    har: {\n      url: 'http://www.mockbin.com/har',\n      method: 'POST',\n      headers: [\n        {\n          name: 'content-type',\n          value: 'application/x-www-form-urlencoded'\n        }\n      ],\n      postData: {\n        mimeType: 'application/x-www-form-urlencoded',\n        params: [\n          {\n            name: 'foo',\n            value: 'bar'\n          },\n          {\n            name: 'hello',\n            value: 'world'\n          }\n        ]\n      }\n    }\n  })\n\n  // a POST request will be sent to http://www.mockbin.com\n  // with body an application/x-www-form-urlencoded body:\n  // foo=bar&hello=world\n```\n\n[back to top](#table-of-contents)\n\n\n---\n\n## request(options, callback)\n\nThe first argument can be either a `url` or an `options` object. The only required option is `uri`; all others are optional.\n\n- `uri` || `url` - fully qualified uri or a parsed url object from `url.parse()`\n- `baseUrl` - fully qualified uri string used as the base url. Most useful with `request.defaults`, for example when you want to do many requests to the same domain. If `baseUrl` is `https://example.com/api/`, then requesting `/end/point?test=true` will fetch `https://example.com/api/end/point?test=true`. When `baseUrl` is given, `uri` must also be a string.\n- `method` - http method (default: `\"GET\"`)\n- `headers` - http headers (default: `{}`)\n\n---\n\n- `qs` - object containing querystring values to be appended to the `uri`\n- `qsParseOptions` - object containing options to pass to the [qs.parse](https://github.com/hapijs/qs#parsing-objects) method. Alternatively pass options to the [querystring.parse](https://nodejs.org/docs/v0.12.0/api/querystring.html#querystring_querystring_parse_str_sep_eq_options) method using this format `{sep:';', eq:':', options:{}}`\n- `qsStringifyOptions` - object containing options to pass to the [qs.stringify](https://github.com/hapijs/qs#stringifying) method. Alternatively pass options to the  [querystring.stringify](https://nodejs.org/docs/v0.12.0/api/querystring.html#querystring_querystring_stringify_obj_sep_eq_options) method using this format `{sep:';', eq:':', options:{}}`. For example, to change the way arrays are converted to query strings using the `qs` module pass the `arrayFormat` option with one of `indices|brackets|repeat`\n- `useQuerystring` - if true, use `querystring` to stringify and parse\n  querystrings, otherwise use `qs` (default: `false`). Set this option to\n  `true` if you need arrays to be serialized as `foo=bar&foo=baz` instead of the\n  default `foo[0]=bar&foo[1]=baz`.\n\n---\n\n- `body` - entity body for PATCH, POST and PUT requests. Must be a `Buffer`, `String` or `ReadStream`. If `json` is `true`, then `body` must be a JSON-serializable object.\n- `form` - when passed an object or a querystring, this sets `body` to a querystring representation of value, and adds `Content-type: application/x-www-form-urlencoded` header. When passed no options, a `FormData` instance is returned (and is piped to request). See \"Forms\" section above.\n- `formData` - data to pass for a `multipart/form-data` request. See\n  [Forms](#forms) section above.\n- `multipart` - array of objects which contain their own headers and `body`\n  attributes. Sends a `multipart/related` request. See [Forms](#forms) section\n  above.\n  - Alternatively you can pass in an object `{chunked: false, data: []}` where\n    `chunked` is used to specify whether the request is sent in\n    [chunked transfer encoding](https://en.wikipedia.org/wiki/Chunked_transfer_encoding)\n    In non-chunked requests, data items with body streams are not allowed.\n- `preambleCRLF` - append a newline/CRLF before the boundary of your `multipart/form-data` request.\n- `postambleCRLF` - append a newline/CRLF at the end of the boundary of your `multipart/form-data` request.\n- `json` - sets `body` to JSON representation of value and adds `Content-type: application/json` header. Additionally, parses the response body as JSON.\n- `jsonReviver` - a [reviver function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse) that will be passed to `JSON.parse()` when parsing a JSON response body.\n- `jsonReplacer` - a [replacer function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify) that will be passed to `JSON.stringify()` when stringifying a JSON request body.\n\n---\n\n- `auth` - a hash containing values `user` || `username`, `pass` || `password`, and `sendImmediately` (optional). See documentation above.\n- `oauth` - options for OAuth HMAC-SHA1 signing. See documentation above.\n- `hawk` - options for [Hawk signing](https://github.com/hueniverse/hawk). The `credentials` key must contain the necessary signing info, [see hawk docs for details](https://github.com/hueniverse/hawk#usage-example).\n- `aws` - `object` containing AWS signing information. Should have the properties `key`, `secret`, and optionally `session` (note that this only works for services that require session as part of the canonical string). Also requires the property `bucket`, unless you’re specifying your `bucket` as part of the path, or the request doesn’t use a bucket (i.e. GET Services). If you want to use AWS sign version 4 use the parameter `sign_version` with value `4` otherwise the default is version 2. If you are using SigV4, you can also include a `service` property that specifies the service name. **Note:** you need to `npm install aws4` first.\n- `httpSignature` - options for the [HTTP Signature Scheme](https://github.com/joyent/node-http-signature/blob/master/http_signing.md) using [Joyent's library](https://github.com/joyent/node-http-signature). The `keyId` and `key` properties must be specified. See the docs for other options.\n\n---\n\n- `followRedirect` - follow HTTP 3xx responses as redirects (default: `true`). This property can also be implemented as function which gets `response` object as a single argument and should return `true` if redirects should continue or `false` otherwise.\n- `followAllRedirects` - follow non-GET HTTP 3xx responses as redirects (default: `false`)\n- `followOriginalHttpMethod` - by default we redirect to HTTP method GET. you can enable this property to redirect to the original HTTP method (default: `false`)\n- `maxRedirects` - the maximum number of redirects to follow (default: `10`)\n- `removeRefererHeader` - removes the referer header when a redirect happens (default: `false`). **Note:** if true, referer header set in the initial request is preserved during redirect chain.\n\n---\n\n- `encoding` - encoding to be used on `setEncoding` of response data. If `null`, the `body` is returned as a `Buffer`. Anything else **(including the default value of `undefined`)** will be passed as the [encoding](http://nodejs.org/api/buffer.html#buffer_buffer) parameter to `toString()` (meaning this is effectively `utf8` by default). (**Note:** if you expect binary data, you should set `encoding: null`.)\n- `gzip` - if `true`, add an `Accept-Encoding` header to request compressed content encodings from the server (if not already present) and decode supported content encodings in the response. **Note:** Automatic decoding of the response content is performed on the body data returned through `request` (both through the `request` stream and passed to the callback function) but is not performed on the `response` stream (available from the `response` event) which is the unmodified `http.IncomingMessage` object which may contain compressed data. See example below.\n- `jar` - if `true`, remember cookies for future use (or define your custom cookie jar; see examples section)\n\n---\n\n- `agent` - `http(s).Agent` instance to use\n- `agentClass` - alternatively specify your agent's class name\n- `agentOptions` - and pass its options. **Note:** for HTTPS see [tls API doc for TLS/SSL options](http://nodejs.org/api/tls.html#tls_tls_connect_options_callback) and the [documentation above](#using-optionsagentoptions).\n- `forever` - set to `true` to use the [forever-agent](https://github.com/request/forever-agent) **Note:** Defaults to `http(s).Agent({keepAlive:true})` in node 0.12+\n- `pool` - an object describing which agents to use for the request. If this option is omitted the request will use the global agent (as long as your options allow for it). Otherwise, request will search the pool for your custom agent. If no custom agent is found, a new agent will be created and added to the pool. **Note:** `pool` is used only when the `agent` option is not specified.\n  - A `maxSockets` property can also be provided on the `pool` object to set the max number of sockets for all agents created (ex: `pool: {maxSockets: Infinity}`).\n  - Note that if you are sending multiple requests in a loop and creating\n    multiple new `pool` objects, `maxSockets` will not work as intended. To\n    work around this, either use [`request.defaults`](#requestdefaultsoptions)\n    with your pool options or create the pool object with the `maxSockets`\n    property outside of the loop.\n- `timeout` - integer containing number of milliseconds, controls two timeouts.\n  - **Read timeout**: Time to wait for a server to send response headers (and start the response body) before aborting the request.\n  - **Connection timeout**: Sets the socket to timeout after `timeout` milliseconds of inactivity. Note that increasing the timeout beyond the OS-wide TCP connection timeout will not have any effect ([the default in Linux can be anywhere from 20-120 seconds][linux-timeout])\n\n[linux-timeout]: http://www.sekuda.com/overriding_the_default_linux_kernel_20_second_tcp_socket_connect_timeout\n\n---\n\n- `localAddress` - local interface to bind for network connections.\n- `proxy` - an HTTP proxy to be used. Supports proxy Auth with Basic Auth, identical to support for the `url` parameter (by embedding the auth info in the `uri`)\n- `strictSSL` - if `true`, requires SSL certificates be valid. **Note:** to use your own certificate authority, you need to specify an agent that was created with that CA as an option.\n- `tunnel` - controls the behavior of\n  [HTTP `CONNECT` tunneling](https://en.wikipedia.org/wiki/HTTP_tunnel#HTTP_CONNECT_tunneling)\n  as follows:\n   - `undefined` (default) - `true` if the destination is `https`, `false` otherwise\n   - `true` - always tunnel to the destination by making a `CONNECT` request to\n     the proxy\n   - `false` - request the destination as a `GET` request.\n- `proxyHeaderWhiteList` - a whitelist of headers to send to a\n  tunneling proxy.\n- `proxyHeaderExclusiveList` - a whitelist of headers to send\n  exclusively to a tunneling proxy and not to destination.\n\n---\n\n- `time` - if `true`, the request-response cycle (including all redirects) is timed at millisecond resolution. When set, the following properties are added to the response object:\n  - `elapsedTime` Duration of the entire request/response in milliseconds (*deprecated*).\n  - `responseStartTime` Timestamp when the response began (in Unix Epoch milliseconds) (*deprecated*).\n  - `timingStart` Timestamp of the start of the request (in Unix Epoch milliseconds).\n  - `timings` Contains event timestamps in millisecond resolution relative to `timingStart`. If there were redirects, the properties reflect the timings of the final request in the redirect chain:\n    - `socket` Relative timestamp when the [`http`](https://nodejs.org/api/http.html#http_event_socket) module's `socket` event fires. This happens when the socket is assigned to the request.\n    - `lookup` Relative timestamp when the [`net`](https://nodejs.org/api/net.html#net_event_lookup) module's `lookup` event fires. This happens when the DNS has been resolved.\n    - `connect`: Relative timestamp when the [`net`](https://nodejs.org/api/net.html#net_event_connect) module's `connect` event fires. This happens when the server acknowledges the TCP connection.\n    - `response`: Relative timestamp when the [`http`](https://nodejs.org/api/http.html#http_event_response) module's `response` event fires. This happens when the first bytes are received from the server.\n    - `end`: Relative timestamp when the last bytes of the response are received.\n  - `timingPhases` Contains the durations of each request phase. If there were redirects, the properties reflect the timings of the final request in the redirect chain:\n    - `wait`: Duration of socket initialization (`timings.socket`)\n    - `dns`: Duration of DNS lookup (`timings.lookup` - `timings.socket`)\n    - `tcp`: Duration of TCP connection (`timings.connect` - `timings.socket`)\n    - `firstByte`: Duration of HTTP server response (`timings.response` - `timings.connect`)\n    - `download`: Duration of HTTP download (`timings.end` - `timings.response`)\n    - `total`: Duration entire HTTP round-trip (`timings.end`)\n\n- `har` - a [HAR 1.2 Request Object](http://www.softwareishard.com/blog/har-12-spec/#request), will be processed from HAR format into options overwriting matching values *(see the [HAR 1.2 section](#support-for-har-12) for details)*\n- `callback` - alternatively pass the request's callback in the options object\n\nThe callback argument gets 3 arguments:\n\n1. An `error` when applicable (usually from [`http.ClientRequest`](http://nodejs.org/api/http.html#http_class_http_clientrequest) object)\n2. An [`http.IncomingMessage`](https://nodejs.org/api/http.html#http_class_http_incomingmessage) object (Response object)\n3. The third is the `response` body (`String` or `Buffer`, or JSON object if the `json` option is supplied)\n\n[back to top](#table-of-contents)\n\n\n---\n\n## Convenience methods\n\nThere are also shorthand methods for different HTTP METHODs and some other conveniences.\n\n\n### request.defaults(options)\n\nThis method **returns a wrapper** around the normal request API that defaults\nto whatever options you pass to it.\n\n**Note:** `request.defaults()` **does not** modify the global request API;\ninstead, it **returns a wrapper** that has your default settings applied to it.\n\n**Note:** You can call `.defaults()` on the wrapper that is returned from\n`request.defaults` to add/override defaults that were previously defaulted.\n\nFor example:\n```js\n//requests using baseRequest() will set the 'x-token' header\nconst baseRequest = request.defaults({\n  headers: {'x-token': 'my-token'}\n})\n\n//requests using specialRequest() will include the 'x-token' header set in\n//baseRequest and will also include the 'special' header\nconst specialRequest = baseRequest.defaults({\n  headers: {special: 'special value'}\n})\n```\n\n### request.METHOD()\n\nThese HTTP method convenience functions act just like `request()` but with a default method already set for you:\n\n- *request.get()*: Defaults to `method: \"GET\"`.\n- *request.post()*: Defaults to `method: \"POST\"`.\n- *request.put()*: Defaults to `method: \"PUT\"`.\n- *request.patch()*: Defaults to `method: \"PATCH\"`.\n- *request.del() / request.delete()*: Defaults to `method: \"DELETE\"`.\n- *request.head()*: Defaults to `method: \"HEAD\"`.\n- *request.options()*: Defaults to `method: \"OPTIONS\"`.\n\n### request.cookie()\n\nFunction that creates a new cookie.\n\n```js\nrequest.cookie('key1=value1')\n```\n### request.jar()\n\nFunction that creates a new cookie jar.\n\n```js\nrequest.jar()\n```\n\n### response.caseless.get('header-name')\n\nFunction that returns the specified response header field using a [case-insensitive match](https://tools.ietf.org/html/rfc7230#section-3.2)\n\n```js\nrequest('http://www.google.com', function (error, response, body) {\n  // print the Content-Type header even if the server returned it as 'content-type' (lowercase)\n  console.log('Content-Type is:', response.caseless.get('Content-Type')); \n});\n```\n\n[back to top](#table-of-contents)\n\n\n---\n\n\n## Debugging\n\nThere are at least three ways to debug the operation of `request`:\n\n1. Launch the node process like `NODE_DEBUG=request node script.js`\n   (`lib,request,otherlib` works too).\n\n2. Set `require('request').debug = true` at any time (this does the same thing\n   as #1).\n\n3. Use the [request-debug module](https://github.com/request/request-debug) to\n   view request and response headers and bodies.\n\n[back to top](#table-of-contents)\n\n\n---\n\n## Timeouts\n\nMost requests to external servers should have a timeout attached, in case the\nserver is not responding in a timely manner. Without a timeout, your code may\nhave a socket open/consume resources for minutes or more.\n\nThere are two main types of timeouts: **connection timeouts** and **read\ntimeouts**. A connect timeout occurs if the timeout is hit while your client is\nattempting to establish a connection to a remote machine (corresponding to the\n[connect() call][connect] on the socket). A read timeout occurs any time the\nserver is too slow to send back a part of the response.\n\nThese two situations have widely different implications for what went wrong\nwith the request, so it's useful to be able to distinguish them. You can detect\ntimeout errors by checking `err.code` for an 'ETIMEDOUT' value. Further, you\ncan detect whether the timeout was a connection timeout by checking if the\n`err.connect` property is set to `true`.\n\n```js\nrequest.get('http://10.255.255.1', {timeout: 1500}, function(err) {\n    console.log(err.code === 'ETIMEDOUT');\n    // Set to `true` if the timeout was a connection timeout, `false` or\n    // `undefined` otherwise.\n    console.log(err.connect === true);\n    process.exit(0);\n});\n```\n\n[connect]: http://linux.die.net/man/2/connect\n\n## Examples:\n\n```js\n  const request = require('request')\n    , rand = Math.floor(Math.random()*100000000).toString()\n    ;\n  request(\n    { method: 'PUT'\n    , uri: 'http://mikeal.iriscouch.com/testjs/' + rand\n    , multipart:\n      [ { 'content-type': 'application/json'\n        ,  body: JSON.stringify({foo: 'bar', _attachments: {'message.txt': {follows: true, length: 18, 'content_type': 'text/plain' }}})\n        }\n      , { body: 'I am an attachment' }\n      ]\n    }\n  , function (error, response, body) {\n      if(response.statusCode == 201){\n        console.log('document saved as: http://mikeal.iriscouch.com/testjs/'+ rand)\n      } else {\n        console.log('error: '+ response.statusCode)\n        console.log(body)\n      }\n    }\n  )\n```\n\nFor backwards-compatibility, response compression is not supported by default.\nTo accept gzip-compressed responses, set the `gzip` option to `true`. Note\nthat the body data passed through `request` is automatically decompressed\nwhile the response object is unmodified and will contain compressed data if\nthe server sent a compressed response.\n\n```js\n  const request = require('request')\n  request(\n    { method: 'GET'\n    , uri: 'http://www.google.com'\n    , gzip: true\n    }\n  , function (error, response, body) {\n      // body is the decompressed response body\n      console.log('server encoded the data as: ' + (response.headers['content-encoding'] || 'identity'))\n      console.log('the decoded data is: ' + body)\n    }\n  )\n  .on('data', function(data) {\n    // decompressed data as it is received\n    console.log('decoded chunk: ' + data)\n  })\n  .on('response', function(response) {\n    // unmodified http.IncomingMessage object\n    response.on('data', function(data) {\n      // compressed data as it is received\n      console.log('received ' + data.length + ' bytes of compressed data')\n    })\n  })\n```\n\nCookies are disabled by default (else, they would be used in subsequent requests). To enable cookies, set `jar` to `true` (either in `defaults` or `options`).\n\n```js\nconst request = request.defaults({jar: true})\nrequest('http://www.google.com', function () {\n  request('http://images.google.com')\n})\n```\n\nTo use a custom cookie jar (instead of `request`’s global cookie jar), set `jar` to an instance of `request.jar()` (either in `defaults` or `options`)\n\n```js\nconst j = request.jar()\nconst request = request.defaults({jar:j})\nrequest('http://www.google.com', function () {\n  request('http://images.google.com')\n})\n```\n\nOR\n\n```js\nconst j = request.jar();\nconst cookie = request.cookie('key1=value1');\nconst url = 'http://www.google.com';\nj.setCookie(cookie, url);\nrequest({url: url, jar: j}, function () {\n  request('http://images.google.com')\n})\n```\n\nTo use a custom cookie store (such as a\n[`FileCookieStore`](https://github.com/mitsuru/tough-cookie-filestore)\nwhich supports saving to and restoring from JSON files), pass it as a parameter\nto `request.jar()`:\n\n```js\nconst FileCookieStore = require('tough-cookie-filestore');\n// NOTE - currently the 'cookies.json' file must already exist!\nconst j = request.jar(new FileCookieStore('cookies.json'));\nrequest = request.defaults({ jar : j })\nrequest('http://www.google.com', function() {\n  request('http://images.google.com')\n})\n```\n\nThe cookie store must be a\n[`tough-cookie`](https://github.com/SalesforceEng/tough-cookie)\nstore and it must support synchronous operations; see the\n[`CookieStore` API docs](https://github.com/SalesforceEng/tough-cookie#api)\nfor details.\n\nTo inspect your cookie jar after a request:\n\n```js\nconst j = request.jar()\nrequest({url: 'http://www.google.com', jar: j}, function () {\n  const cookie_string = j.getCookieString(url); // \"key1=value1; key2=value2; ...\"\n  const cookies = j.getCookies(url);\n  // [{key: 'key1', value: 'value1', domain: \"www.google.com\", ...}, ...]\n})\n```\n\n[back to top](#table-of-contents)\n", "readmeFilename": "README.md", "users": {"9137": true, "184455": true, "285858315": true, "291296283": true, "yi": true, "atd": true, "bat": true, "bnu": true, "djk": true, "dmy": true, "drj": true, "dyg": true, "gvn": true, "hyq": true, "kyx": true, "luk": true, "nrn": true, "p6r": true, "soc": true, "srl": true, "sua": true, "tmn": true, "vbv": true, "vot": true, "a1ip": true, "aahz": true, "alca": true, "andr": true, "as0n": true, "bam5": true, "bcoe": true, "bigp": true, "boto": true, "cath": true, "cdll": true, "dodo": true, "dofy": true, "dyaa": true, "egsy": true, "ferx": true, "fill": true, "foto": true, "gamr": true, "glab": true, "hain": true, "hema": true, "huyz": true, "ibio": true, "ions": true, "irae": true, "izzy": true, "j3kz": true, "jits": true, "jso0": true, "jtrh": true, "kai_": true, "katy": true, "kobs": true, "kola": true, "leor": true, "lobo": true, "lova": true, "maff": true, "meme": true, "mhat": true, "mktj": true, "mr.d": true, "n370": true, "naij": true, "nsue": true, "old9": true, "orit": true, "pana": true, "pcac": true, "sgpr": true, "siam": true, "swak": true, "tg-z": true, "tpkn": true, "uojo": true, "vasc": true, "vasz": true, "vwal": true, "wayn": true, "wzbg": true, "xsdc": true, "xufz": true, "zhen": true, "zick": true, "zwap": true, "akiva": true, "arefm": true, "ashco": true, "bloep": true, "boogy": true, "brend": true, "brpaz": true, "capaj": true, "ccyll": true, "cl1ck": true, "cliff": true, "coppy": true, "csbun": true, "dec_f": true, "deide": true, "demod": true, "djeck": true, "dlion": true, "dshaw": true, "ealen": true, "ehrig": true, "eklem": true, "eyson": true, "ezhik": true, "fedor": true, "flozz": true, "fm-96": true, "givan": true, "ha006": true, "haeck": true, "hexon": true, "heyun": true, "imd92": true, "incar": true, "jgubo": true, "jlord": true, "jmm23": true, "jnath": true, "johno": true, "jream": true, "jtsky": true, "junos": true, "kremr": true, "kthjm": true, "kwcjr": true, "laomu": true, "lbeff": true, "lgh06": true, "linus": true, "liugc": true, "lqweb": true, "luics": true, "m42am": true, "m4r35": true, "madeo": true, "magoz": true, "meryn": true, "nak2k": true, "ndxbn": true, "ntl88": true, "panlw": true, "panzy": true, "ph3nx": true, "pilsy": true, "plord": true, "pmasa": true, "podow": true, "qizai": true, "rioli": true, "robur": true, "rotom": true, "roxnz": true, "ryanj": true, "saoud": true, "scalz": true, "segen": true, "shama": true, "sjnnr": true, "sky3r": true, "sopov": true, "suncn": true, "t1st3": true, "tchey": true, "tdevm": true, "temsa": true, "thejh": true, "tsyue": true, "ugarz": true, "ukuli": true, "yarob": true, "yswon": true, "yuxin": true, "zckrs": true, "zetay": true, "zfkun": true, "1barry": true, "456wyc": true, "71emj1": true, "a_dent": true, "acmeid": true, "adamlu": true, "adammc": true, "adeelp": true, "afc163": true, "agplan": true, "akarem": true, "aksalj": true, "alek-s": true, "andysw": true, "antong": true, "aolu11": true, "apopek": true, "arttse": true, "axelav": true, "ayyash": true, "bhenav": true, "blalor": true, "booyaa": true, "bourne": true, "buzuli": true, "cgfeel": true, "chaowi": true, "chiefy": true, "chrisx": true, "cr8tiv": true, "craiem": true, "crodas": true, "dainov": true, "damkat": true, "dbarth": true, "decoda": true, "devnka": true, "dknell": true, "dmdnkv": true, "dna2go": true, "drudge": true, "dsiddy": true, "dubban": true, "dubbya": true, "dviate": true, "eaglex": true, "ecelis": true, "edin-m": true, "egantz": true, "ekmpls": true, "feedm3": true, "felegz": true, "fenrir": true, "fizzvr": true, "fotooo": true, "frankg": true, "funzzz": true, "fyockm": true, "gaafar": true, "gabeio": true, "gabeno": true, "gdbtek": true, "genovo": true, "glebec": true, "gloddy": true, "godion": true, "gtopia": true, "guogai": true, "halyph": true, "helrod": true, "hij1nx": true, "horaci": true, "hualei": true, "iamwiz": true, "igasho": true, "ikoala": true, "irfan3": true, "isaacs": true, "itcorp": true, "itsakt": true, "itskdk": true, "jackhq": true, "jmkim9": true, "joshmu": true, "js3692": true, "jsolis": true, "kakrot": true, "katsos": true, "kaycee": true, "kazem1": true, "kbakba": true, "kcando": true, "keycdn": true, "kivava": true, "knoja4": true, "koooge": true, "kriget": true, "leakon": true, "legacy": true, "legiao": true, "lenine": true, "lionet": true, "lixt07": true, "ljharb": true, "ljmf00": true, "lonjoy": true, "mahume": true, "mandyc": true, "mestar": true, "miezoy": true, "migkjy": true, "mihaiv": true, "mihaur": true, "mikend": true, "mjasso": true, "mmatto": true, "monjer": true, "mortiy": true, "moueza": true, "mrbgit": true, "mrzmmr": true, "niccai": true, "notdom": true, "novalu": true, "nusmql": true, "nuwaio": true, "oheard": true, "olonam": true, "omar84": true, "owillo": true, "pandao": true, "patmcc": true, "pigram": true, "pirmax": true, "prbsas": true, "qlqllu": true, "quafoo": true, "raidou": true, "rangzf": true, "ray_ye": true, "robnov": true, "ronchi": true, "ronder": true, "royliu": true, "rugare": true, "ryandu": true, "ryaned": true, "s_grig": true, "samobo": true, "sariss": true, "scull7": true, "sdove1": true, "seanjh": true, "selich": true, "sermir": true, "shaner": true, "shoyer": true, "shriek": true, "sm0ck1": true, "starak": true, "tarcio": true, "tedyhy": true, "temasm": true, "tenshi": true, "thouky": true, "tjwebb": true, "tmpvar": true, "tonijz": true, "trinup": true, "trotyl": true, "tujiaw": true, "tunjos": true, "ulongx": true, "veegee": true, "vegera": true, "vjenks": true, "vladan": true, "xlaoyu": true, "xmwx38": true, "xtiawy": true, "xu_q90": true, "yichan": true, "yinfxs": true, "yjsosa": true, "yoking": true, "yong_a": true, "yorusi": true, "ysmile": true, "yuch4n": true, "zambon": true, "zanner": true, "zhoutk": true, "ziflex": true, "zolern": true, "zoluzo": true, "ajarora": true, "alanson": true, "alectic": true, "alekzzz": true, "alexu84": true, "algonzo": true, "aliemre": true, "anoubis": true, "asaupup": true, "astesio": true, "ayoungh": true, "azevedo": true, "batosai": true, "bezoerb": true, "bhurlow": true, "biao166": true, "biggora": true, "bjarnij": true, "brainss": true, "bredele": true, "cambera": true, "canibal": true, "chamix2": true, "chzhewl": true, "coalman": true, "coiscir": true, "cooboor": true, "coockoo": true, "dabielf": true, "dac2205": true, "david-l": true, "dcriori": true, "deivbid": true, "demoive": true, "denmerc": true, "deubaka": true, "dino_su": true, "diosney": true, "djjaron": true, "dkannan": true, "drafael": true, "drewigg": true, "drosado": true, "dtamade": true, "duooduo": true, "elidiaz": true, "engleek": true, "eterna2": true, "evkline": true, "fatelei": true, "ferrari": true, "fflores": true, "fkamani": true, "flaxbin": true, "floppee": true, "fnouama": true, "ftornik": true, "funroll": true, "fwhenin": true, "fxkraus": true, "gemini5": true, "gregl83": true, "gyaresu": true, "gztomas": true, "hartzis": true, "harutlc": true, "hckhanh": true, "hexcola": true, "huytard": true, "io2work": true, "itonyyo": true, "itsmyth": true, "jacques": true, "jaguarj": true, "jasonxu": true, "jbocook": true, "jinmatt": true, "jmorris": true, "jyounce": true, "kaashin": true, "kahboom": true, "kekdude": true, "kkho595": true, "kparkov": true, "ksidibe": true, "kxbrand": true, "laoshaw": true, "lezyeoh": true, "lgatica": true, "loasnir": true, "lucthev": true, "luizmds": true, "lysunht": true, "mafikes": true, "mamalat": true, "mamsori": true, "mapaiva": true, "maur1th": true, "maxwell": true, "min85da": true, "mnishig": true, "mosario": true, "mr_eaze": true, "mugifly": true, "mythern": true, "natsuko": true, "ngpixel": true, "nikolay": true, "nof1000": true, "obihann": true, "pdedkov": true, "perrywu": true, "philard": true, "plusman": true, "pureppl": true, "rapomon": true, "ray0214": true, "rebolon": true, "roryrjb": true, "rparris": true, "rugoals": true, "sahilsk": true, "sahlzen": true, "sakatam": true, "samersm": true, "san4osq": true, "sehrgut": true, "sfgarza": true, "shivayl": true, "siirial": true, "sinahwz": true, "skarlso": true, "skilbjo": true, "smrr723": true, "sopepos": true, "springy": true, "subchen": true, "subying": true, "svoss24": true, "syaning": true, "takonyc": true, "tellnes": true, "tewarid": true, "tianmin": true, "timzero": true, "tin-lek": true, "tmypawa": true, "tonstwo": true, "toszter": true, "touskar": true, "trtrojo": true, "trvrfrd": true, "vboctor": true, "vicapow": true, "vonthar": true, "wesleyr": true, "wjp5826": true, "x0000ff": true, "xicombd": true, "xngiser": true, "yakumat": true, "yanghcc": true, "zendayu": true, "zonetti": true, "abraheom": true, "ahaswell": true, "ahvonenj": true, "alanshaw": true, "alexkval": true, "alicebox": true, "almgwary": true, "amkaradi": true, "antouank": true, "aodreman": true, "baishuiz": true, "bouchezb": true, "brucehem": true, "buddh!ka": true, "buritica": true, "buru1020": true, "cain.chu": true, "cdokolas": true, "cetincem": true, "claveren": true, "clydekuo": true, "coalesce": true, "colageno": true, "corefive": true, "coroneos": true, "crewmoss": true, "crissdev": true, "crowelch": true, "cslasher": true, "csscottc": true, "cuprobot": true, "dada1134": true, "danday74": true, "danielye": true, "davepoon": true, "dburdese": true, "demian85": true, "demopark": true, "dercoder": true, "dexteryy": true, "dgarlitt": true, "dharapvj": true, "diebombe": true, "dizlexik": true, "djamseed": true, "djbrandl": true, "djviolin": true, "dolymood": true, "drscript": true, "duyongbo": true, "dzhou777": true, "edalorzo": true, "edloidas": true, "ehershey": true, "elussich": true, "enriched": true, "erikvold": true, "esundahl": true, "eugen814": true, "evegreen": true, "fabioper": true, "fangleen": true, "fanjieqi": true, "faraoman": true, "fgarrido": true, "foobarjs": true, "frankl83": true, "gabroot1": true, "garustar": true, "gavatron": true, "geooogle": true, "gigerlin": true, "gimenete": true, "gitaarik": true, "greenbie": true, "gregjopa": true, "gujarabh": true, "gurunate": true, "guybrush": true, "guywicks": true, "ham2yagi": true, "hckrmoon": true, "hex20dec": true, "hkb06542": true, "hlpetway": true, "hourdays": true, "housecor": true, "huacnlee": true, "hyanghai": true, "iamninad": true, "ifeature": true, "impereal": true, "jackvial": true, "jdcauley": true, "jdorfman": true, "jlmorgan": true, "jmshahen": true, "jmsherry": true, "johnsonb": true, "jon_shen": true, "joshberg": true, "joshmili": true, "jphilung": true, "jpillora": true, "jprempeh": true, "jrnail23": true, "jsumners": true, "junyeong": true, "kainos90": true, "kevbaker": true, "klombomb": true, "koreyhan": true, "koulmomo": true, "krabello": true, "kulyk404": true, "l0n9h02n": true, "leejefon": true, "leonning": true, "losymear": true, "louisyeh": true, "mabotech": true, "macrotea": true, "makediff": true, "makenova": true, "mariod3w": true, "marsking": true, "maxogden": true, "maxxiris": true, "maxzhang": true, "maykonlf": true, "mhaidarh": true, "milan322": true, "mkomigor": true, "mluberry": true, "mmierswa": true, "mobergmk": true, "moxiaohe": true, "moyanhao": true, "mtscout6": true, "musikele": true, "nalindak": true, "nataliaw": true, "nchmouli": true, "nketchum": true, "noddycha": true, "northsun": true, "nukisman": true, "oboochin": true, "oleynikd": true, "pablaber": true, "panos277": true, "paullang": true, "pddivine": true, "pdilyard": true, "phette23": true, "pnolasco": true, "porkbits": true, "qddegtya": true, "rayjshin": true, "rbartoli": true, "rizowski": true, "robermac": true, "rochejul": true, "rrobayna": true, "ruyan768": true, "s-konrad": true, "sadd_yar": true, "sakai135": true, "satoyami": true, "scaffrey": true, "schacker": true, "scottkay": true, "sean9999": true, "semencov": true, "shieldax": true, "shiva127": true, "sibawite": true, "sobralia": true, "softwind": true, "ssljivic": true, "staraple": true, "stjohn3d": true, "stoneren": true, "stuligan": true, "sumit270": true, "szymex73": true, "tblobaum": true, "tcauduro": true, "tdmalone": true, "tenpenny": true, "thekuzia": true, "thewolff": true, "thitinun": true, "thorsson": true, "tkhemani": true, "tmcguire": true, "tomasmax": true, "tommyjs7": true, "tosbodes": true, "trendoid": true, "u.turkoz": true, "uqualifi": true, "vchouhan": true, "vishwasc": true, "vtomilin": true, "waitfish": true, "waiwaiku": true, "waldrupm": true, "walkerbe": true, "wkaifang": true, "woverton": true, "wozhizui": true, "xgheaven": true, "xiaobing": true, "xjamundx": true, "xmalinov": true, "yokiming": true, "zalithka": true, "zeusdeux": true, "abpeinado": true, "abuelwafa": true, "adamkdean": true, "adeamos83": true, "aipae.org": true, "alcovegan": true, "alejcerro": true, "allendale": true, "andrelion": true, "andrewlam": true, "animabear": true, "anmol1771": true, "antixrist": true, "appastair": true, "aristo_sh": true, "arthanzel": true, "arty.name": true, "asawq2006": true, "asfaltboy": true, "avenida14": true, "azaritech": true, "bad-coder": true, "bbcaptain": true, "bcipriano": true, "benlinhuo": true, "binarymax": true, "biozdilek": true, "bmpvieira": true, "bobxuyang": true, "boneskull": true, "buhaiqing": true, "caboosesw": true, "ccastelli": true, "chiefford": true, "chriscalo": true, "cilindrox": true, "cisco_lai": true, "cmilhench": true, "code_monk": true, "cparker15": true, "cpsubrian": true, "daniheras": true, "darkowlzz": true, "davequick": true, "daviddias": true, "davidrlee": true, "dbrockman": true, "decaylala": true, "dennisgnl": true, "devmoreno": true, "dillonace": true, "diwushi33": true, "dondyabla": true, "dylanh724": true, "edosrecki": true, "eduarte78": true, "edwardxyt": true, "edwingeng": true, "elidiazgt": true, "emersonmx": true, "emircanok": true, "ephigenia": true, "epuigvros": true, "erincinci": true, "errhunter": true, "evanyeung": true, "ezcabrera": true, "fanyegong": true, "feferonka": true, "fgribreau": true, "filmplane": true, "firerishi": true, "fleischer": true, "flockonus": true, "freesuraj": true, "g33klaura": true, "gameknife": true, "gavinning": true, "gilt-tech": true, "gonzalofj": true, "grabantot": true, "guumaster": true, "guzgarcia": true, "heartnett": true, "hehaiyang": true, "huigezong": true, "i-erokhin": true, "iceriver2": true, "icirellik": true, "igorissen": true, "interlock": true, "jakedalus": true, "james3299": true, "jasoncmcg": true, "jcasey214": true, "jerkovicl": true, "jerrywu12": true, "jesusgoku": true, "jhillacre": true, "joaogalli": true, "joemdavis": true, "joshwyatt": true, "karmadude": true, "kizzlebot": true, "kulakowka": true, "l8niteowl": true, "lakipatel": true, "landy2014": true, "largepuma": true, "larrychen": true, "larryteal": true, "leahcimic": true, "lenrok258": true, "leonel-ai": true, "leonweecs": true, "liulei224": true, "liweifeng": true, "logiclabs": true, "lonespear": true, "luckyulin": true, "maalthous": true, "madalozzo": true, "magemagic": true, "makhmurov": true, "maks11060": true, "mark12433": true, "martinlev": true, "maxwelldu": true, "mbrevoort": true, "megadrive": true, "michael-f": true, "mikemimik": true, "mistkafka": true, "mjurincic": true, "mojaray2k": true, "momepukku": true, "mondalaci": true, "mr-smiley": true, "mvolkmann": true, "nice_body": true, "nickeljew": true, "nicksnell": true, "ninozhang": true, "noisypixy": true, "obouchari": true, "omgbbqhax": true, "onelaview": true, "pavelusov": true, "philligan": true, "phpjsnerd": true, "qingleili": true, "qinshulei": true, "qqcome110": true, "raabbajam": true, "rafarocha": true, "rainnwind": true, "raitucarp": true, "ramzesucr": true, "rbecheras": true, "rgraves90": true, "rise0chen": true, "rlafferty": true, "rmlewisuk": true, "roccomuso": true, "rubiadias": true, "ruyadorno": true, "rylan_yan": true, "sasquatch": true, "sayansaha": true, "shakakira": true, "sironfoot": true, "slmcassio": true, "snowdream": true, "sparkrico": true, "spekkionu": true, "spences10": true, "stanzheng": true, "steel1990": true, "sternelee": true, "steven166": true, "stone-jin": true, "stretchgz": true, "sujeet555": true, "sunkeyhub": true, "telco2011": true, "thepanuto": true, "thomas-so": true, "thomask33": true, "thunsaker": true, "tinyproxy": true, "tomgao365": true, "trgmedina": true, "trocafone": true, "troygizzi": true, "ttsuchiya": true, "txmcy1993": true, "ukrbublik": true, "uptonking": true, "valenwave": true, "vmichalak": true, "waitstone": true, "wuxiaword": true, "xxsnake28": true, "yayayahei": true, "yvanscher": true, "zuizuihao": true, "21xhipster": true, "76449128mm": true, "aaronsaray": true, "adardesign": true, "ahmetertem": true, "aitorllj93": true, "ajohnstone": true, "akashgupta": true, "alexdreptu": true, "alexindigo": true, "alexmercer": true, "alshamiri2": true, "amdsouza92": true, "arystan-sw": true, "avivharuzi": true, "axelrindle": true, "bangbang93": true, "basharmadi": true, "cestrensem": true, "cfleschhut": true, "chiaychang": true, "chinaqstar": true, "chirag8642": true, "chris.ch86": true, "clarenceho": true, "creativ073": true, "cuckoonest": true, "dandewitte": true, "davidrapin": true, "desmondddd": true, "dglanzmann": true, "dh19911021": true, "dolphin278": true, "donvercety": true, "drmrbrewer": true, "dutchmansa": true, "dwayneford": true, "echaouchna": true, "edgardoalz": true, "ericlondon": true, "evanlovely": true, "f124275809": true, "farskipper": true, "fmoliveira": true, "franksansc": true, "fsepulveda": true, "garrickajo": true, "gearcraft1": true, "ghkddbguse": true, "goodseller": true, "gustavorps": true, "henrytseng": true, "hoibatpham": true, "huangkerui": true, "huoshaolin": true, "incendiary": true, "isaacvitor": true, "jackishere": true, "jacobswain": true, "jaggedsoft": true, "jakemiller": true, "jameskrill": true, "jashsayani": true, "jbpionnier": true, "johndorian": true, "jokeyrhyme": true, "jswartwood": true, "justincone": true, "kaerimichi": true, "kappuccino": true, "karlbright": true, "kenjisan4u": true, "kevteljeur": true, "kubakubula": true, "land-melon": true, "langri-sha": true, "leizongmin": true, "leon740727": true, "leonardorb": true, "lijinghust": true, "liushoukai": true, "louielouie": true, "lucachaves": true, "luffy84217": true, "luhalvesbr": true, "manikantag": true, "marco.jahn": true, "mattbodman": true, "maxime1992": true, "maxmaximov": true, "meganekick": true, "menoncello": true, "mgocobachi": true, "michelmelo": true, "mickeyzhou": true, "miroklarin": true, "mjwilliams": true, "morpheus72": true, "mpmckenna8": true, "myorkgitis": true, "nate-river": true, "nickleefly": true, "nicomf1982": true, "njnesterov": true, "nleblanc88": true, "nosthertus": true, "nwservices": true, "ocd_lionel": true, "oldthunder": true, "omidnikrah": true, "panoptican": true, "pantyuhind": true, "pedroparra": true, "pillar0514": true, "pmbenjamin": true, "princetoad": true, "ptrevethan": true, "puffydaddy": true, "qqqppp9998": true, "rajikaimal": true, "raycharles": true, "reportbase": true, "ricardweii": true, "roberkules": true, "rocket0191": true, "ruiquelhas": true, "ryuchihoon": true, "samhou1988": true, "sammok2003": true, "sanketss84": true, "saravananr": true, "selenasong": true, "shreyawhiz": true, "shuoshubao": true, "sigkill(9)": true, "smedegaard": true, "stefan.age": true, "stephenhuh": true, "stephenykk": true, "stormcrows": true, "supersephy": true, "svmatthews": true, "takahitooh": true, "tangweikun": true, "thesseyren": true, "thetimmaeh": true, "thomashzhu": true, "tiagodanin": true, "tomasgvivo": true, "tomjamescn": true, "tonyljl526": true, "vapeadores": true, "vcordero07": true, "wangshijun": true, "werninator": true, "windupdurb": true, "wuyangwang": true, "xenohunter": true, "xieranmaya": true, "xsmallbird": true, "xwy5201314": true, "zaphod1984": true, "zhouguotao": true, "acollins-ts": true, "adamdreszer": true, "aereobarato": true, "ahmed-dinar": true, "ahsanshafiq": true, "ajwarreniii": true, "alaeddine17": true, "aleaxandors": true, "alex-skakun": true, "alexey-mish": true, "alxe.master": true, "ambition101": true, "antonmyrzak": true, "antonnguyen": true, "arkanciscan": true, "arleytriana": true, "arnoldstoba": true, "axelstudios": true, "azhsetiawan": true, "balazserdos": true, "blackmiaool": true, "bracketdash": true, "brandonb927": true, "caffellatte": true, "carlrondoni": true, "cedrickchee": true, "chinmay2893": true, "chrisakakay": true, "chunhei2008": true, "cloudychris": true, "coolhanddev": true, "craigpatten": true, "cycomachead": true, "danieldwyer": true, "danmartinez": true, "davidchubbs": true, "davidnyhuis": true, "dilterporto": true, "diogocapela": true, "elessarkrin": true, "elliotchong": true, "ergunozyurt": true, "ericteng177": true, "evert-arias": true, "fengbeijing": true, "fiveisprime": true, "flumpus-dev": true, "garenyondem": true, "gauravmehla": true, "geerlingguy": true, "hal9zillion": true, "he313572052": true, "heyimeugene": true, "highgravity": true, "hortinstein": true, "hugesuccess": true, "iancrowther": true, "icerainnuaa": true, "icognivator": true, "italoacasas": true, "itsnotvalid": true, "jamesbedont": true, "janggomgeun": true, "jasonbourne": true, "jasonlotito": true, "jbdoumenjou": true, "jeffreylowy": true, "jensnilsson": true, "joannerpena": true, "jonatasnona": true, "josealmeida": true, "josemarjobs": true, "jovenbarola": true, "justcode247": true, "karlbateman": true, "kasperstuck": true, "kauegimenes": true, "kobleistvan": true, "lorenazohar": true, "louxiaojian": true, "lupomontero": true, "m80126colin": true, "marcelotrad": true, "marcovossen": true, "marinear212": true, "mattattaque": true, "memoramirez": true, "mikewesthad": true, "mrmartineau": true, "mryongzhang": true, "mseminatore": true, "neaker15668": true, "octetstream": true, "ouroboros99": true, "payaamemami": true, "phoenix-xsy": true, "rockedpanda": true, "rsmccloskey": true, "rubenjose75": true, "sadmansamee": true, "sammyteahan": true, "scytalezero": true, "shawndsouza": true, "shay-altman": true, "skytreasure": true, "sojinljenny": true, "songhuitang": true, "strathausen": true, "sunny.zhouy": true, "swedendrift": true, "technolojay": true, "themadjoker": true, "threeputted": true, "tunght13488": true, "wangnan0610": true, "windmillboy": true, "xinwangwang": true, "yeahoffline": true, "abhijitkalta": true, "alexandermac": true, "annesrinivas": true, "arvindrsingh": true, "austinkeeley": true, "bianlongting": true, "calvinmuthig": true, "carlosgrossi": true, "ccxsungodzzx": true, "chriskinsman": true, "conantonakos": true, "deepakkapoor": true, "designbymind": true, "doc.gunthrop": true, "doctoruseful": true, "dryliketoast": true, "dustinphipps": true, "einfallstoll": true, "evanshortiss": true, "fanchangyong": true, "feibenrenqwe": true, "floriannagel": true, "ghalvatzakis": true, "goblindegook": true, "hareeshnpmer": true, "helpfulhuman": true, "henriesteves": true, "ianizaguirre": true, "ihatehandles": true, "iori20091101": true, "ismailismail": true, "ivan.marquez": true, "ivangaravito": true, "jackchendong": true, "jakedemonaco": true, "jamesmgreene": true, "jfromaniello": true, "johnny.young": true, "jonathandion": true, "josuehenry14": true, "jricardoprog": true, "justintormey": true, "kevinohara80": true, "khaihoangdev": true, "kwabenaberko": true, "lukaswilkeer": true, "markusgattol": true, "matiasmarani": true, "matthewbauer": true, "maurogestoso": true, "michaelsosin": true, "mohsinnadeem": true, "mucahitnezir": true, "nickhardwood": true, "npm-packages": true, "oussoulessou": true, "paulomcnally": true, "paulpdaniels": true, "rahsaanbasek": true, "rethinkflash": true, "richard.webb": true, "rodfernandez": true, "roman.kirian": true, "ruchirgodura": true, "runningtalus": true, "sanchezjjose": true, "sfpharmaplus": true, "shanemileham": true, "shekharreddy": true, "sinisterstuf": true, "stevepsharpe": true, "sundaycrafts": true, "superchenney": true, "themiddleman": true, "tobiasnickel": true, "toby_reynold": true, "trycatch9264": true, "tuomastolppi": true, "tylerstalder": true, "vasiltehanov": true, "walexstevens": true, "wallenberg12": true, "wesleylhandy": true, "yassinesania": true, "yourhoneysky": true, "zhanghao-web": true, "zhenguo.zhao": true, "alex.hortopan": true, "andyprocesser": true, "anypossible.w": true, "axelniklasson": true, "benjaminaaron": true, "billhinderman": true, "brad-christie": true, "cairacshields": true, "chinawolf_wyp": true, "chrisdevwords": true, "codeamancoder": true, "crazyjingling": true, "curioussavage": true, "danielgray.me": true, "dewang-mistry": true, "diegorbaquero": true, "dongguangming": true, "dreamineering": true, "duskalbatross": true, "ericholiveira": true, "everywhere.js": true, "favasconcelos": true, "ferchoriverar": true, "gamersdelight": true, "gillesruppert": true, "govindaraja91": true, "hibrahimsafak": true, "intuitivcloud": true, "jasonwang1888": true, "josephdavisco": true, "juanma_romero": true, "komarovsergey": true, "landry_soules": true, "lassevolkmann": true, "leelandmiller": true, "liangtongzhuo": true, "manojkhannakm": true, "markthethomas": true, "matthiasgrune": true, "mattmcfarland": true, "miadzadfallah": true, "montyanderson": true, "nonemoticoner": true, "pedroteosousa": true, "perevezentsev": true, "piotrposzytek": true, "piyushmakhija": true, "polarisjunior": true, "program247365": true, "rabahtahraoui": true, "rafaelsampaio": true, "ral.amgstromg": true, "raphael_vogel": true, "roboterhund87": true, "rollingtester": true, "sakthiifnotec": true, "serge-nikitin": true, "shen-weizhong": true, "spacerockzero": true, "themanspeaker": true, "tranceyos2419": true, "unitetheclans": true, "vitali.doudko": true, "willhumphreys": true, "xavierharrell": true, "arnold-almeida": true, "avinashkoyyana": true, "bannerbschafer": true, "beatwinthewave": true, "bigglesatlarge": true, "gabrielbasilio": true, "galochkinkirov": true, "geduardcatalin": true, "greenbud-seeds": true, "imaginegenesis": true, "jeremy-j-ackso": true, "joshuadavidson": true, "kamikazechaser": true, "karzanosman984": true, "liqueurdetoile": true, "luismoramedina": true, "mananvaghasiya": true, "matteo.collina": true, "matteospampani": true, "maycon_ribeiro": true, "mirkogelsomini": true, "natarajanmca11": true, "nikunjchapadia": true, "patrickpietens": true, "projectweekend": true, "shanewholloway": true, "suryasaripalli": true, "thatwasawkward": true, "thebearingedge": true, "thecodeparadox": true, "thiagowittmann": true, "tomas-sereikis": true, "willwolffmyren": true, "andrew.medvedev": true, "antoine-richard": true, "antongorodezkiy": true, "cashew-webmaker": true, "chrisscastaneda": true, "hyokosdeveloper": true, "icodeforcookies": true, "jeffb_incontact": true, "joaquin.briceno": true, "jonnymaceachern": true, "jslilly.charter": true, "juancarloscruzd": true, "krishna_kandula": true, "lbrentcarpenter": true, "pensierinmusica": true, "valeriu-zdrobau": true, "yoshimaa-tricot": true, "youzaiyouzai666": true, "animustechnology": true, "bluejeansandrain": true, "carlosvillademor": true, "digitalextremist": true, "ernst-eiswuerfel": true, "florianbellazouz": true, "knight-of-design": true, "lherediawoodward": true, "maciej.litwiniec": true, "michaeljwilliams": true, "rodrigo-medeiros": true, "shashankpallerla": true, "svgkrishnamurthy": true, "travelingtechguy": true, "volodymyr.sichka": true, "ys_sidson_aidson": true, "alquilerargentina": true, "christopherritter": true, "joris-van-der-wel": true, "nguyenmanhdat2903": true, "ognjen.jevremovic": true, "omkar.sheral.1989": true, "programmingpearls": true, "scott.m.sarsfield": true, "tuannguyen1508.tn": true, "adrian.arroyocalle": true, "fabian.moron.zirfas": true, "felipeferreirasilva": true, "programmer.severson": true, "robertwarrengilmore": true, "daniel-lewis-bsc-hons": true, "michaelz-voice2sports": true, "unsunstoppablegreatness": true}}