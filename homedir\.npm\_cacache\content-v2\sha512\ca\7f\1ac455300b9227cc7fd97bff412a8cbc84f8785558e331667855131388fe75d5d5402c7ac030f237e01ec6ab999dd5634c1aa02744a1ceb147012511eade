{"name": "@types/eslint", "dist-tags": {"ts2.3": "6.1.3", "ts2.4": "6.1.3", "ts2.5": "6.1.3", "ts2.7": "6.1.3", "ts2.6": "6.1.3", "ts2.2": "6.1.3", "ts2.8": "6.8.0", "ts2.9": "6.8.0", "ts3.0": "7.2.0", "ts3.1": "7.2.2", "ts3.2": "7.2.5", "ts3.4": "7.2.6", "ts3.3": "7.2.6", "ts3.5": "7.2.12", "ts3.6": "7.28.0", "ts3.7": "8.2.0", "ts3.8": "8.4.1", "ts3.9": "8.4.3", "ts4.0": "8.4.6", "ts4.1": "8.4.10", "ts4.2": "8.21.2", "ts4.4": "8.44.2", "ts4.3": "8.44.2", "ts4.5": "8.44.7", "ts4.6": "8.56.5", "ts4.7": "8.56.10", "ts4.9": "9.6.1", "ts4.8": "9.6.1", "ts5.7": "9.6.1", "ts5.5": "9.6.1", "ts5.6": "9.6.1", "ts5.4": "9.6.1", "ts5.3": "9.6.1", "ts5.2": "9.6.1", "ts5.1": "9.6.1", "ts5.0": "9.6.1", "latest": "9.6.1"}, "versions": {"4.16.0": {"name": "@types/eslint", "version": "4.16.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e7ed874c0d664a18cc2165481875daba5aaa9e73", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.0.tgz", "fileCount": 4, "integrity": "sha512-DtdMTe0EyDuPLIv3e28djIqDUgst7PQdDyiHGgGbTtRtVK67bazN8KYf7qsbzIZr1F4RS+vgpF8Yhl1qBoqeew==", "signatures": [{"sig": "MEUCIQD6WYVC/xmh0TNyA1Wj3qnknVznZNsNmkkjKSUiY11HzQIgCa7X8KE+g8jyl9V91oExJq/0stfp+e/XPPrf6yOtBmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19562}}, "4.16.1": {"name": "@types/eslint", "version": "4.16.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "19730c9fcb66b6e44742d12b27a603fabfeb2f49", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.1.tgz", "fileCount": 4, "integrity": "sha512-lRUXQAULl5geixTiP2K0iYvMUbCkEnuOwvLGjwff12I4ECxoW5QaWML5UUOZ1CvpQLILkddBdMPMZz4ByQizsg==", "signatures": [{"sig": "MEQCIDEZiUpyjyh2H8s6q0C30mwFEnsXhZlHfq3WUlOQllbTAiAwCIIQFe1YED7DSQPKkF1hoKoyibdPymfNx9dToFF12w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa376zCRA9TVsSAnZWagAAh4kQAJnJsnF6yseuf+ZTECoB\nuAOdWkg52J2ck7/dl7rWYxU55w9DKcVzFXXn4ij2GGlj0C5gbX8yQdB2hzck\nLnI69oMbSSKsfnbxcNSw/zMZN81XnVDvD4yp9r3Dn4F4QCtPa8752h3IvzZr\nfJ09RB9Z2zu3OIqQT09xVKvc8jJ4NzKQIoxiV7pEpZ3O0+rJKOhBhWRVZTIi\nGCKpm800Lh3hJZg8mhCqtdG0dgSQoUQt/9JCB1Kcykeu+aRdutLIjisoGWmC\nIbokT6FHGnPzi72crmHgJWcbKg9IdJVEqNEkG38XQc5A+IZitdvU+By6OmLP\n+uHDog0mXblj5DUBxYjNoMzcgYuS4O7b4pQybuqGsNpI6JCImzRuyO9+BR82\nRzkVwgcISPOvmC3EvxT+GiqppMO++WxZfP+r8KMB2IjXDSgOklxgellD7oi/\naYxHjBfIxcqBlQseFcu6Nd2hlK0RZEeptAFl/UfR5z1HOBJoa6mQvJEltvc7\nj9tCyTrBcJZhxjaS2sbFbx/Om6tDbBoDOOdmOTENUWz0eukj8tiaT22Dt0Af\nb0rJfmNUfR/O1ahZi9Pf7czIRU/FLkV4hLnA2fjJyxGUGO9BPXRwgsuQ2DTX\nPMMOu+y5g9KBnMFW81oQ65HpWWrSJED/6z/wMdnz1ASsZZU0aUP/n8N2d4e4\n5xh1\r\n=47IJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.2": {"name": "@types/eslint", "version": "4.16.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "30f4f026019eb78a6ef12f276b75cd16ea2afb27", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.2.tgz", "fileCount": 4, "integrity": "sha512-gCqhoFlyLic8Ux1OQt9cjlPbXk/dS7zPpofazBkie6SWCl+e1IEZBgLqyakm27nh0/uSZYW2TqkBusV9fLmztw==", "signatures": [{"sig": "MEQCIHiHY9+mJN+vd09rueI5mg9pkYYnsQpduWRRaqHXwS2mAiACA71JdESUWMoEYs0PkZTnqvlJYBqH7Uk5eLPxFIZkBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8dNYCRA9TVsSAnZWagAAALUP/RkvX/MNXd/OEAeqZLmE\nzmZtQ0vsoXUZKHxINfQ4CFrsa4x0+sqZtgtL7oON5EcWT/JxtV/Pk4+I5r4P\nr5lPPWeYoac16afASQL91BHmg2RfTob8QC1ubt8+P5rKlztEHjf0MeCli/ay\n/g7Mi1L9SB1lPsEQ+A0cPuWPZz0SZ2NXneH2e7Vy8gMOjsgXyBT5h9787Rap\nyms5b71nasmz1PxSnieAWlxiG5EXcG6jfp8eTWNDHEIDiYOy/sMY29cM1TKS\nVmKIKHyHRPEzaXi1U7h/syhYccb7yqq2gjwOKjzyO7U/QwVSH7cIVteIkotE\nVwJt+mxSLZqTfC76xhieo5E8o78L3ASFGciuwZY6eIUkKAx5npSW8xOr0nlm\nZWfBNbw2+MFeRQBjlDLKn3jgB4OoTOyKE7dR5VUFaKc/n4L/bn6te/SZZjQF\nazlHhslbgPVoZL7HPsF6Tz6IiDlZ36HdJuhvVU3OMeCkAAwFgWjvEnRCLMLQ\nOI54BhFPZzqx3eruuQojo906IfQXLxMc6nlrn+3RFoIyduu2BLIMeMid4XGc\nyc6LFQ0BcgvZcKyycimwA0PstOy0qA0FunMnZKPKo3G7UO8eBT0suwLVk8/Y\n5xsqKdBZTo3BLaNR1A9GzcUALMhfKUfoSSnTtag5kOl2UORo4fl/StlsmH2j\nWvB7\r\n=WwrZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.3": {"name": "@types/eslint", "version": "4.16.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "6ed2a9c68f73162fa57ba84582d63dcd8ee2d473", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.3.tgz", "fileCount": 4, "integrity": "sha512-3/RbuVmefwKXGWxq6/+F9UKx8z6Wm8Pa7e4428r7a8L5plqdPzMAuYkvQBLf+wqDPqLQ93TsYi3j3cnql1P5Qw==", "signatures": [{"sig": "MEUCIDYb3JOuG/FtW0EgvHQ3KFUikXqS/AwGYzQBc3Z4OGtmAiEAmZbJ0jKvMLyKVeFV/NnpHy0KQ36V22mlB32V2/MlLqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNvABCRA9TVsSAnZWagAAJ7kP/AhYLrGMwFIz30+RmQYU\nCMlfDPHWu2Muv6EXmmO7FnHJmzfnvEQG4eVEzI+8jm2YyeQmNSImRmG2xA3D\nrU0BnvcY2oa8DOAQv2Ht9n0o6IPVv+mhyQnvh5iB+ZIxPSlgUlyISLVMa3RV\n5IwhzcjO3KFzYSuQTCiz4wv/ylizhHcu6mW/qpScO0oNtdpn/mt5d/DKA9bM\nripBRXUN/m/gxoFTi/HmDDQWMqMQVDK9vR38cl0h11H97ef7mhIH5m0aHysk\n5i/mNpHQoHdTXcrhV1ApWEzqkPeOe7C1/xiuk1FWaB7RA8yYvjh+3sco6AAD\nSSWrDrbJx9ezklloybcbFaSDA65aFtJPISvXDsdcGF1qLqYlLFDYaZx2PJ4E\nCiI4zRK3UsYhfW8AAJjZEG0Z5FkatGmNqDbKNt1ys4/o3HwyyRsg892xRBAv\nU3PfZ3FZWPkfNoIdhOjLVuYdTn0UpVM4urX0UmQT8Zg1fBJYAI3znKbwQbtZ\nUWQWw+MIjos0MCwgSm85p2cD5p4F/DDA31/fns7luZj+NtJsSkkfGuswmBCA\nDorsP7K4IYRYWb9EIg9CH17fjCZKtik4DmxDo/OwbzwMeUf0EUfoQ3ipOR01\nZaMp2tJrCI97TbrdFP+KPBXkhvfzKk3hAwgg1I9uFB6L36CXv3RGjRc93VeY\n9APD\r\n=TBWn\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.4": {"name": "@types/eslint", "version": "4.16.4", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "9a0d80208b22f0fd1a9d174e76dc675f3802c011", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.4.tgz", "fileCount": 4, "integrity": "sha512-g+PV8wktZLJrSPrdCZj6aWfa0xqTrGdx3FywlGE8YH13am8PhkhjtA+Wu1fLdV45gtpM1FM8JWnyFwOsLZI4VA==", "signatures": [{"sig": "MEUCIFpU9PHyQ9ZsdzOXQjLC/94Yn85XRje2w1JKVaK5n5wgAiEAmGovPlrD+2TSE8fSw6JWurHDqhFHPKcHss6q9wffIhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0JH3CRA9TVsSAnZWagAAl+QP/12y9pijDELhFDsi6Gb7\nXt0bhs+JMvSKokqA7mcoNn3meeLtGajNxvZiXo0bPJieiIBXcQwgbced0hXu\ny5Vow0v41RfD/2RHOOBzudaI4grZno/XnAfuzlrDBSmvw3Kd6fUNx+wxlMJX\nJAaY91PnYmmcgQ5MNb4NSxPMH8jYPazwKdg1j3mA718BDDXI4e9s32+GHkWw\nW2rUKAPYR0mN9zSmh47ALxMASRxSzuwKbd3xm1i1zlQSwgtA8IXcpXl4q+2b\nXkEb0rIkUUzq0oVXbUIXQDKeHRxRFO0qggD9KTy/jX9fnKtcmu4ZfMxw8Eed\nva3scx26SjyG1lVz8HrxIuRE6Lu/wJ5iBLUKvLrkh51a9fXs8mRSPA9VhZzO\nCWUDrEbf9ZPLjakw4EZ04leURkEjQ4RzTL16CLxOQyQdPNlpo5nyR2fyvt28\nSineuVUBjFJ5aqzM/4pRlyWFiIx+fF0++hdl3YW2wD7CdkjDwFg/5O6YujJy\n7N/a3w2oZpyA9yiGARxNEfIoRS+eEj0cgmj+0riYv1DhjAQ6AZ7mNDCHB5M+\nKOC+26+X9ABaPXZe/J1nHiLz442LOqqYgN1DOOypv8RJKCNHX4SI3sojTtLH\n2gnxTSi5UVBeq9tNFI9FebtiVgu8tvUwM/4NsbZ+nCwwsFWVOSMU1NTKU/Fx\ncqjo\r\n=rpQm\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.5": {"name": "@types/eslint", "version": "4.16.5", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "f0b56e011a3f7d01a380a568776f93ec56d7c911", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.5.tgz", "fileCount": 4, "integrity": "sha512-103C3Adr8UaucsjYBDdKzL9AZk6c6F2gKIlyZOxv48KtmnvNcXoDsEddF4N/DZJNoVFg6NGSbzYodvxix3Md2g==", "signatures": [{"sig": "MEYCIQDN9S9dSFW5F32syWKvG8enbF1HUlBvLx33AGBC49JWiQIhANz9rRJ7OsBiTGrRpEwtR1YVl7y0iLzd9JphFXRRQcdL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBaIMCRA9TVsSAnZWagAA25oP/1nahozFxJeiVIDuMi7+\nFYKvHaKh82lPAl/okz3aLS3+OYq4/4OzeznSGR5GOxZgm72JxP6VmW6Ygtfn\nVCKqJwuiD6zXvh43RnZ1qXgrN6+38kZOxd7AGP0K12+NFdRCdYuzI0rzXpc7\nDQx9ifLorGokBBmDHl30270J1h/zO8L5LkentZclf0t1e95xZeXzZJ5Qx7Xq\n8MLvG50wI+9uhCvjL8otlElzMBFC3Ce1Kkfk9f2tuTC26IgvUMOuWpPLeiLA\nxtwfSxswPrf5HRiPMsfP2lyryrVeL9oGGOtKqGL3HoZdFB96beC5Fe38ly+Q\ndPXA4Tf83YsnN64z1osBaFPwsV6issnTD6qkhZJ8gZB1awB8/R1MhHLEB7pu\nxwCeviBu4OB4rDIj3Trxr0Z4B+JFaFQvl+W9vWkTF60W4TGxIl+fIvCnMLL9\nKaNf5Sjff11NIY0E+vWSPNm+BRYxbTw7mSBelTxM7EXw2WlopQD2uhSfkiWG\nDBE9VA3NOYFE9z5BOTcq3cSV/ti01WJip1GRc6+nOVCp59FZUol2gs5+d2cg\nJZOcuJsQtCkLX1nugLbxrelIKntCMR+BAo7mX2hdozpvWTGQCAO0yl2sIUy+\nn4jDCmmvT6uQVb3L1UOidvRXCi9S7w+yzdZiDAbc7IUhhB6xDsQAucDwMF5H\n1Dqf\r\n=T2w8\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.6": {"name": "@types/eslint", "version": "4.16.6", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "96d4ecddbea618ab0b55eaf0dffedf387129b06c", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.6.tgz", "fileCount": 4, "integrity": "sha512-<PERSON><PERSON><PERSON>tGJig55FeclpOytU7nCCqtR143jBoC7AUdH0DO9xBSIFiNNUFCY/S3KNWsHeQJuU3hjw/OC1+kRTFNXqUZQ==", "signatures": [{"sig": "MEQCIDLywtQDrbhXQJhi3fEBA6o6UhPWoGw3kFmB+cKkSjFRAiBR19+k4WRoLd7Kahhym93YTNa58PYOLGnDBkKuCYIuZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcT2dICRA9TVsSAnZWagAAQrsP/2/WHgH8hb76u6ChMabX\nFixpIjkJXzhgZutX8L/1UOWEeTwFLr/41uVCMiGApnt0fresC9TGEG/TJNcU\nRbbd3B8CYKSx7Z+8UMd6qEvoLGI64yyUR6T+UyUFzq4ymW5kpsZSFe5P9YCt\nUOPn+sh7N1U+QFQabIpomG0PApSDlcPPv6I97xzNMbb9FjPd6a9UY03IftGn\nltX3BPt2+ceE9fj28lRR7+1epUkFp+CxEbEt5jP5tNXV/zeL2ufxi3EhetwZ\n1Gde3UQYo2Y2hUyPHLeYbNolzgWKexfuZYmYqg06WgZeBS3BwVqqZRpZNj5b\nolK1k0C5xPatZ5viPTT6JWVvgcF9RhM4/RmMo5HEwqirVsLyc2XyKuZ6fjox\nYNu+Fb9i//wE7N2OKOUEA0YLYFUWbd2yoUL17Yhwikgzx0fByFOsEeGBvIGn\n3lv9IwdGc30Xe2wXw4PWGfTBnL9kw1MrwkXpT2SJfK4H6zYbnYvFNY+mpkKG\nWSV3PgHGMJIM5jdIsmPHvKXybrQJ2i7pR1AOjppAYF7AwGS6xV0SYXMymWLF\nIKH3JrAwX4Nd+U5GNewBX96ovWDfjrRwG3IQ5+rGDcdkXunmMVBxWAOK+TGk\nsiWm7MEGX7GPlhohY0ZFS1Un8TdJ2kGF+RukofVgRB0Lsr14Mp3CLHWmDynq\nOARN\r\n=YJvE\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.7": {"name": "@types/eslint", "version": "4.16.7", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "723a2e53c6884204323ee44e80ea036857e34ef8", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.7.tgz", "fileCount": 4, "integrity": "sha512-Xr50GH0b4cc/65IVQOJtbH8Ilob8HSmr9HW/OmxpNrqJwEp+FX8TL5V5lv6GJEXIlLd4eiWFt439hlImZ9PgdA==", "signatures": [{"sig": "MEUCIQCYXOkLWSFTCJCvOsaJ+0yCKGFJMg3RufviFImaYwZa5AIgJUtFnbVuQ5ZL1CFuBVA62PGEe6JJJSQDsXh6Qxrpslw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTKdBCRA9TVsSAnZWagAAzLAQAIeHsqC3iQgXTl3F+Puk\nFamx069Unreh4QtKWMhAmuvCBs3xqNXAYisQdoupmSC+pc7ojWt2Np8pIVvC\nXHIMVqpurY4buA49vzXPGrVcyV0uVVoe+xXXeHQWFhvlAClQaroonVfjAeOF\nX8Z7jaXc65I921B6vUwe2TpJONcJzLWvvnqsIJjjqvCOwnrGy36PGd1aqaHb\nK6dT1M5+Ekam7uMFnLXt2QKCVvBfvAd+LS4caGD9hOptkgO+w/UVZvjwwELQ\nCizI8phRw4WY0PxSsZ5Zrs2NmVhcMW8usZ1qNpb6b9Zz33YKUjmw/seVDNJ0\nG3Mupc9vg6M0ukG5V1chzlwseqy9DbhbaTe90t0RXTg/dm2+q8+xZwAkQ1ny\nmB1McHw4nJSC+axJt+vafpGn+izPmjB7nn5CpARyoiceAvgiyTUNaxhSbtfi\ncDMPSarfQxE4vNG3SsNmkvswUy8oPvlhN43XnsT39N6KbS47I/8Wf566zyVx\nnlalPrTbaXjsNkfohjtJ29puA8EfZVjt+wfHA00dvRDapgH4W2CfXzU1HDNS\n71u3gDuSzLZT3lwUaJhiLDBzHacHTUaPqCh75Z6yy0S+Ta19CE9pyomGlZR0\nQz8w2M7qI52fBwFOrXHd9vmBaKJrdLh9IXfc82wnr/LujA0zk12vBa7gNzsU\nNgQL\r\n=fddl\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.16.8": {"name": "@types/eslint", "version": "4.16.8", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "856f0eb8a312d25a7989b6d0ab708e8d5f8cc7ee", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-4.16.8.tgz", "fileCount": 4, "integrity": "sha512-n0ZvaIpPeBxproRvV+tZoCHRxIoNAk+k+XMvQefKgx3qM3IundoogQBAwiNEnqW0GDP1j1ATe5lFy9xxutFAHg==", "signatures": [{"sig": "MEQCICuYNmNQOn96xZYy2Ko3vQG9Vd59hQslfr50GT+o9R4VAiBqq/VHFAUMi3XGiPnANDWosWaqfe7hfeKVAqniewgQAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTZFtCRA9TVsSAnZWagAArr8P/AxgIM9JtVBJ2EcpvbxQ\n9RfzdaII2Cg/2MnTrB726Z/Lbh/HH/G5ZybN7psY8iP8ZQhVqxhaj+6ZgPoq\njfT2ey/ycy+PxwvJFtkZbklaMTYmkst1gXFkBl+6mD0VidpTPSwMPh9OmJVf\nafdxiotT4YtODZE8zwAxvHellLGFxtngVmvla7fahMsL2TqFVLuDAc3mDbYq\nqpKE69qCjfq74QW5dAlL5RSXt1ws6w82kOsusnkrt95LceXd2nvG6pddCn+p\nQMZOMQBQyf0V348bUg47uh14GGf5J3SfJ5/J4OFaysR4fS8nvzUbQdzVf8xg\nVA+P3dos7WFy1RVWlO1h2Mh4OsukwytCPw2oRxdJCbKMkmLTog3XI4rqclO5\n58yhCZsCC+RrPzaRMG13EqcnYlZkWE8Sfn33Z2O6idvFQCKFpgffcg6Gak1t\n+HSxBMK+emhM4CXhliKX+syi2y2NTpn76clcTXQdApNKNnTc7DezQ+KaxR0J\nMNihmd82x5DFahQ1qolef6MZlmE1dreAVAfEV1Zx0Tp3X3D3LOkQAfp6jHtY\nqPgHCQ55OUTbXE8FG2YgAAoqIlCUJn3JtwXBDUuglKvJl9QYc8Q3kyK66fMy\no5FFMBDvdNKxJ/eu+Lr2pgwIfN7wa+znVNXsj7ts0Xutq7aK3MnmR+qd+Z5R\nlR/c\r\n=tcD1\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.0": {"name": "@types/eslint", "version": "6.1.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "8877f938dc843e641c11fd56790944905b16156f", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.0.tgz", "fileCount": 17, "integrity": "sha512-AQT8DimJSk6euBWT4w3xJd8R77Yev4uANuOSNdSYRKZ4azJxMKC8Nm0k8e43edZ3CZkr2C4bqtkcps4oN0yNKg==", "signatures": [{"sig": "MEYCIQCAaDIa0Ry/jFr8xtAb2pP8y9c8tuP040OS7dtnJ+PxrwIhAJOBhtdFstuQKf75QF516zyqLZzUOvVhzVPC9SAq0ue4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXEdrCRA9TVsSAnZWagAA+OAP/1GAAIl18ZGCOl5uJvze\ncQerd+v7zfvzdubXyYoJXZmu+1YJEHQOuqCGniARZmpKt2gtLMRNDLz0Ro2X\n8RYkgNtehypfj1hED6Fen+m45j+ZgOSN8JDwtUsZbDgIEl5zlOft2GxX1iT7\nK3BeiwuZA1AbsCG1SHgsQJ2Ijc5tUKmZkrH7U15p/RmwnHEn2o875ez41qXE\n8WERtqJYpV01Dt/NH+yJ5/q+hhjjMf99LP9UPr7YHoj1Q/y+OCH1J6Npr3Ee\n+tJBkCi79o0JMoeFJCUWVIVGODeq/ZbBuObEBG125ExhUyE4oPp7euMf/Zz4\ndowO1TzNk3pfa1Eg2GsQrdEivmFs5WQVJDJH+vBwXjBid630IZ2hN+FtrCFk\nqn9yCQIII8EiNvUy9PLUgAmaGAHj46ou6oIJgJ05SqeUCdH+jTXVHonrgh/3\n02AuO5LW3gMY29Tq/dV0RPw+67CLgFF9t0rtsdclS2y4p4DRu7K4gb91o1YM\nlbAA2KfW2I53y/UqUNw20FQ/PVLfAnpfi20QxlpFQhDpBQuCAq2OMMLhBVKx\n3riduuWSuO54fwqZvCAIgoWUtXKJrTdTClLi15+6J0P1vJY1BA8GV7hlIJCl\nDlBPWvDFTjyPpClmJCZHJfQYuY9ofURZuOd7GlWet1//iM7jLkQ7GXE0yAFx\nMZj3\r\n=HBSg\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.1": {"name": "@types/eslint", "version": "6.1.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "62a59e00800e954b1c57b2d7148db68adb94363a", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.1.tgz", "fileCount": 17, "integrity": "sha512-ImT/b1lN/nOvhYpm4knz25xV8mDm5Xnc1Y1cobdDfTnK1oC/kBfuZxnfJCoLHKs8WRj6SU7CE5ExK+a2gwwwvQ==", "signatures": [{"sig": "MEUCIErbWP/duVZk4vFkP62GsSfFXjwP5FdYdgUtWsRDRKLOAiEAsdfy+vswRZB4BOWScpSXq4NITDYmfVO3qkHeoeX/eSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYAj+CRA9TVsSAnZWagAAnZwP/1u3trIqsVHghfgKEeTZ\ntWpJcO9qXeVQjYqGaj9McC1fA8qYtaW414U38Khpc56XNJROyaJYolX03jyQ\ntO89tIUmcJTCHMfCQ9vjK2biYlasihHkZit35aRslRH2fYprFWoh/6MV36an\npQyT2Bs4Hi3Vxgj2OT+TyqwJASMgNPUjHIqHz9nJUkmreSVtwT9SPk0kR7fS\n8mivBC+3xyIfQLqDYmBu5ajabHzKFavooa1tvStpKx/FqF1jlvvf0A4l4Rez\n8hte4gXGSezpUTmsZAhVJzSdkcNpRU5njVWEOFcnnngFEyfeZpKIt297+Ql8\nAOejpe65N1YzU2mbZ6Cg9quwo3PaVwBJqxOe3UQKR1+C5UnXul8BLUeYQzmO\n7niKAoFlON83bqYsNy/b5pbuHH9jYdRO72zhbmkxUDx0goyfZS1onpkiQadH\niSvBQGxOBltku/cQOkmEd7QTx0vOilUclP1gI+K7bP33dQkx1YQiris2aUN2\nrwJtFAPXb06O04KLfK33tdLN7QsKx+5/btZnEZvJveH9+V8Te6D3NjQX/40+\nG8L6eJfmlY7sWwnt4eKY2nZnqmxq8Mdq6i4+GjSlqvbbW2BrRFstOrlJ/P0A\nioBSVkh8akX07wW7gikyFFBoB1yDnrlymFl2GNJAz9PKsXLYeu5ArPBC3+Yc\nIMWL\r\n=MU51\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.2": {"name": "@types/eslint", "version": "6.1.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "297ece0f3815f93d699b18bdade5e6bee747284f", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.2.tgz", "fileCount": 17, "integrity": "sha512-t+smTKg1e9SshiIOI94Zi+Lvo3bfHF20MuKP8w3VGWdrS1dYm33A7xrSoyy9FQv6oE2TwYqEXVJ50I0or8+FWQ==", "signatures": [{"sig": "MEUCIF+DbSBWo2JKH9JZuC7lMm6y8sIRJWye343wO3P9XjJfAiEA0lTTBZESAZ1pueQU6DYiIj5TJXhflpg/YWxgtgVW8hA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlR7PCRA9TVsSAnZWagAA/4sQAJg/+ac+PASRGgF8UtmQ\nkYr+5NhRf7rlyJNS3KS70KwQ/yPtR4+nwq8YxljNNYUANMHGDcAiXhhxV34X\n3kpeM49tigNyT4kLdmhlje4IwY6V4Nd+8yhoUjzqY3zOefPJLHRU2wBCVzI4\nXPfAh12eB94/6FaXIXjH/5lcxNhhf7HcRBMjFpIOFV+/lqVVs1GPDh0u6t3g\nvIkzVE+Zlwx+aTtgua++L2GfHluLaCVWuHi2QTU7PhPI1Y4tAHrVjfubVIzG\nH3EB5B1pYvNL2m8I0j9DDfY2b2DX3iYCYEtaA1OQSGQX+14ofEpRItEniCHz\nbf5EOiyBi0KTUHTmmgjrl8BVKMyHUuu7W5L00o2LBr1Brnn6JO7LScX7ab+V\nnDs/30jHmvRPo/xKJTtTyA9z/94X3dNDz5bTK3N4cs/GiVcJl1N1d8ToyGxs\noo9YF+KFFJb+zPgQ+4Lq0oH7vNBkHfQAo5n3FRsTRhsmAzr9NMJLIcdC4roy\nEg2cMo72VIr+VeEKgHDY537UOjwMgYlDYcnA8d9jP/P6BWGk5nMrv0RZu2QD\nfrv+zAY/WaQolZTHh/QKy8N1g11WgWvDWjUx4Cy1Ppf/WvSv39Je3GoZsZ/W\nSDa+f59r5mg4iCqkHESS0wHzqMIvo2M5ERqgok4cEWP+GKhqKVfjGhu+EeGS\nEzNQ\r\n=dKyO\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.3": {"name": "@types/eslint", "version": "6.1.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "ec2a66e445a48efaa234020eb3b6e8f06afc9c61", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.3.tgz", "fileCount": 17, "integrity": "sha512-llYf1QNZaDweXtA7uY6JczcwHmFwJL9TpK3E6sY0B18l6ulDT6VWNMAdEjYccFHiDfxLPxffd8QmSDV4QUUspA==", "signatures": [{"sig": "MEQCIBCVphHEvnzUFp0vzJm401h4N5DDeMMjSfhhVtGpaq9QAiBiUMTDUnq6qZ0hwHwpuB2cyB7uzPtYorI/VtweO4EVDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsM/4CRA9TVsSAnZWagAAvYkQAIJp/an3fw0MDhRQHPpK\nmc/MTZv4dSiRWk2H7jj5faKw35NWwRSaZm3WDY6XwvILR3ogapv1MEzxHA0y\nga3xev0/SIq4G5pqGlFb76e7YYBZMUkWt7EAC4pg/n5AVfa3vFRSBeJDqWIK\nzK4AX/KkDNtbL8C1GGs7U6fY1aA9BX4YTSEe36vmWAV+/Hy6+2CCB/TDXNqp\nMehe1N4NrOz3u2nAbGhO2yuVMeHzuoNdQZTbWAYtk821AumVgvESCgYjaEyC\nw2Ez58S/5PvHdQlbjN4PEy72YtfXsaw7/yQNpXrcs+PlphVR1aVyTOlbkgDD\nrI7kXgKFAxnJZzaKxasPLJla8gp2VNIpui7Rv2kqlDwgfb/DNXTYc+BBMVna\n6J2vvStRs/8bqHKqR77LxQa9GYY59jo8D0O+UX6pzjczDIRPnaBKYGf8LM+4\nAvCpWqaljdp6txgYFe57VNV7/ldBbESqnsHUGUUatI0jm56NHeNVaDFLxGbS\n/AmVEwZixdxQ/d3lvKAAzWfG98Q1TY49ufSJSesU0x2gHkYYET7oov9qnjeF\nTfeX/1xPma1EDFF+HvICiCc2z1SaW4jcJtFJI5XUCNV9VBaCqU1VgArCHAoa\nZ/m4sc8G5CZmfekC43MyJdWH5FhXR44rOazKODtovueAFz3GUxVXB+sL9qhU\nZXjN\r\n=Hg7s\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.4": {"name": "@types/eslint", "version": "6.1.4", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "2b03daed7fc310d02a9099b67b7c248c3bb482c6", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.4.tgz", "fileCount": 17, "integrity": "sha512-Y6<PERSON><PERSON>LSSFdpaXvyOncYfT7Ir8qp9oZZaEQnQIDENztqYJfIdLcXLQ6S52f7ZPEtrdzWQhes4JZXKJs5mpOFCUA==", "signatures": [{"sig": "MEUCIQCeN3zcFVH9tN6TXJ3ozJijzIXdZ0o8+2OTOtiey0H1IgIgIWvA5cVH4q+cUoSgwfhV2CnnWfwe1P6O6vp1mJ2jv3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ5zLCRA9TVsSAnZWagAApvcQAJQ5xMQLrt18jhp+gSUx\nXI8jsu7Hw4jnyYa5Vwq5J9SY1NqXgvyuXD6KwtfXkxNSQtzCSVRCT1E90pBe\nAtKPxP1PSZgylU9FqW/nLkZn6FETyNrQe0IxIelwcPUNi74rKo/g7FAFX0Ax\nnaqRy2zpHuoZBGDkrL8GuU0QTZcogFMEhUAXr6Nt0bBjbkpd+2uHNqiXNjeH\nIb5RVUY/KW3OUc49sqpr543ZL0ZedqzVr2dDHQ0EsLwBXahPbncFieM1AIba\nhtGtxcB1CYgRuLmhoAZU5QCXLWl2HlHAnAA6+4hurXzWCKgghWQLSmbJKINK\nd1gXXWs38rKB/liSxxMeNDBXUilxw9vWU2mM6zqSHnShntAm8ZNlWERtIp5Z\nK+uig+AX71t9ZU+d3KlWqVeU9IukGx3FSLbcBaZuEuZPJU287GBLz17nmvrQ\nGwI2PmKJRqCYWKD345/tZhzVKalX7AcFXlK+5hKO/KuzJHOOmwyM/Fzis+jH\nHDGPkjLDzjQFZCROTO4+DH57eMw9i5Bus2TLU1oq8evjI+gpSq1v5d4d5TT0\n7S7q6Q1JyvQUMObuLqK4v6BA6bcN90cSVULaaS3nEnZoF0VctBAmhaOUY0E0\nw+b4AvDMhE/GaQ+uospCKq7hyClR2OedC+moJQH8guw6RlaCkx2qWb5Yqt9O\nSHZh\r\n=fhoF\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.5": {"name": "@types/eslint", "version": "6.1.5", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "1e1c0f6349504091a8bb71a8a38eb663d401f9ab", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.5.tgz", "fileCount": 17, "integrity": "sha512-DDNhspp3xrOUnPQtHeuwqnAGEv6uLNjUPjKI6MUHh34iMHa8xu808YdVsCdyrB6q8ZSudZ5Hbhndt62/bwwUVA==", "signatures": [{"sig": "MEUCIQDr2Pk4R7fkh2NY84ZYHnl0AOShdGCUZFoOikYUql2WQwIgfGvxm314s0dxr+edDstywKikOA7KSWiY+VE+ubIJr7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKKglCRA9TVsSAnZWagAAnF8P/AidBuy36iyI3a/D9pB9\nLklyQ9DDs+wLKgcRv+zmmh6hp9PDJdwgSvo/k1JP26L2Pk46cSdzFlsBo/LN\nMNvXWOraEYdewZ5znfLTBLGd8GlPe8TRw4uVErmn4mNtvAGP43xa7LudAkH0\nyAcWqDqGVelkn2hWyoAYOVZNcYofjLR0t8FnoW/bZE5gG0fgglrdeTvzxGJ+\n/sb4w1HmyFUjv7OCaQkTKZAa9YTuf+R6u/13qDmv9I8IAKSZdxIp/gdlaUiD\nP1dQZvSgi34Snp/UzKzNJFoVvWBY/uAKSVHxM8C+KfKYv+RGYWjtU3+s7BRC\nELMexiSPFsWXJjWHAIqKCyc47ChOdCClVGf6ZS8tY4bdatI9uSuYV1LhRyce\nXPAetzPMFxBTKwOLhORJv69igKZHFJ8d+9jDZhNfEmrYezVKET91yHfaDxMK\n8sQA514s53Y+gYQATnBeUgDQAHR3Y2yCiEs1wH/515DD9+8KCszRS3+zCWfk\nvqm48Uk1M/ncHZokvOglkCkoAUbELYSCjyz54zizlsn3/JK7KJXo00lAuVK5\nn6+zLQ+L/RND+DS9KWDLP9+sZ1wyUjW2R7lOlZG+Ld7JhDzhW24PSiXCAtPO\nxiFnMLC4SK9sd7VG1hg+f75TMeW/7UF7Zq5E3yU2WmBkO+9Hg/pHVm8oyRaI\n1yjl\r\n=rpW2\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.6": {"name": "@types/eslint", "version": "6.1.6", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "1b54c949c270dd0e12809e4d5aa265953aa5cc22", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.6.tgz", "fileCount": 17, "integrity": "sha512-bTZQc4vx5zjlVWI8f+Ce1Ioc7o4RRi+qLhDXlHWIadmieyb6dZU4lxNFbWXd2l16+Izxw1Y6G0bG3ksi8W79dg==", "signatures": [{"sig": "MEQCIFiFdRA6BQC137IR7TMGF3qwQQc5N9Jn/WH1bwXiqRl8AiBlzA3iEFA+BoyWd7DjuMxyg/3avxHeh0G5wt44cM2fQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKeUfCRA9TVsSAnZWagAALG4QAKJQvorPOc25iR6zE63X\n1Ybvay4OrzD51gpiWxYKUmkFWKGOgRKHOrLgzV/k969XmzHjgjyMkfKOKDoV\niIvMRkZWFJpdiKbKg8VkRebcHhYPox2wT/gpa8rdHULvABZLfw+QVc6gRx0W\nxsDz50UqgVcKi0MGGaYX0udh26dCJpoV5Sd9dXVHsQ78g56mQneScfEMW2bl\n+owYPbOUt70HJuLq9A6sZrHrwqc87mkcjx+7uWyIPX6OfFXtQvPkveau6LaH\n+UsXwp26zaNt1T/4zwmwpFIsfw+qAiClopdPyk+P00UeASY9q4Olhp6xwtNZ\nYItLTMsWyVda3CFshBNslHrK/TQRfF6MqSMBKhZO4urrg5mOxL3wfEaBGesb\nYJwOSTe362li0G2v2idmjWYM37bwIm56F/HdmlmXEO3wbcX5VtQ5b6LMNVhp\nIdMVfmo+c5EUNbPCzsH8lMelBiRX8ah05+m7JJME+I9ee4mXqpEL6wMTg0F9\n5IBc6IJCacaIcGzGw51xv1qI5cJVi9ZAVIkJ3xP/t62awPqcaVIR2NByXsmD\n+9yNg+wDsIGAuNDAF0EImGmDGVXT+/IdVrPzO9ytxbf/5eU5vCtJB4E98ExS\nEL11NXMkrjIMz0DI23+s4mNfKfv4cbH/Y336wr1RuH+0kK+wvG6NmYz1LZ2E\n13qI\r\n=iitf\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.7": {"name": "@types/eslint", "version": "6.1.7", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "81ce3a730bbe84bd34e60cf2accd9dc9e60fbcc2", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.7.tgz", "fileCount": 17, "integrity": "sha512-SP9WDPZeOfX0VFXylehXoKGtuHx7Cm4JaXSiJj6dHbgLW503i95MAs22zIQgciof3LXWIAejZXp5bOgbWC/Faw==", "signatures": [{"sig": "MEUCIGpvHVN9aGmjMpV87t12KariEeU9yFQ0903M/0qqqnH+AiEA/WE8WnScHbOZmkJclFjA0x7h4wzJbCcpgspsWCjvEnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeK5OwCRA9TVsSAnZWagAA2bwP/307DzmRjl7eA3VGKq3C\nV7HWdzLjJNxv7Ac+KsqSLjf+i9ybRC86P1UHGfhQNhb0eCQ4D//V9U9eE8fF\nrbNRRjYp93AJ7d4X4rLra9jiwGF7UQNgmaR+Thm96kfZJjaWOS5RE86oCTrS\nGRapxiyTBAplb6r/pGEyOBZQRJh5OlxU2GjNJLm+bABKvWYLzqNHwu/GRZHT\nEEDrsX5tpmVDrZWkLDyYU0N47O8g3MPLoG64yXW1yif8M/8tWMWnou2UFDwu\ntHeqm3Gb1GQC5VTgONPl/eIUDl1e3HqCAyJOrxt1LvaFhEvJPmcuAg8GFoMZ\npkTMMNhmko70/QfrOigcuzL/5VlGPkbhmsjV6t1jyANi+FQaml4/ayeYC8A+\nuKuWKze1sfY9/tK67w0/xJaD7OXpF0Qw7tg5zhQ5wJKJGYVDRePm2HUW+d2K\n/lQKWjL0f5mi48Ie0DakCSphdmykR/pBTUaKM50Fdq+r7ryEdppRavNPTCVp\nTSv1hbTS72M53mquU9wAZ4/LYI9zygfNC/h4KRMxQwax5IUC+SbL803bD47F\nJd6GELKTluX4Hzcj1VhVKm4IKYPdKRVP7NGM4kniwZ1N0nTFkz4EnksCvUEo\n+DWDFXXaEj66PhfM7iX54gB+oLPD6ylmP7JaR16XXiu8vCq0N9Jr0D7w3dFK\nwD4W\r\n=cEAW\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.8": {"name": "@types/eslint", "version": "6.1.8", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "7e868f89bc1e520323d405940e49cb912ede5bba", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.8.tgz", "fileCount": 17, "integrity": "sha512-CJBhm9pYdUS8cFVbXACWlLxZWFBTQMiM0eI6RYxng3u9oQ9gHdQ5PN89DHPrK4RISRzX62nRsteUlbBgEIdSug==", "signatures": [{"sig": "MEQCID45GavZNcwuVJXitTWP5F8WbU629ttAG6dqmRPnvWGEAiB7mXxg9tZs0eKebpzpGbuJWuIx+InkuPu6ylySGb0GAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOa5WCRA9TVsSAnZWagAAztkP/0ysUpVkkdgZBMUrRFme\n0u4BM8IRXPI/5hUswb2K0zz/g6bSOctb2V9aWnyvpKQwRqMvmPrIvWTndEn8\nJmg4PQyyEmXRxb6mxsvJhORzcGSxi3SGmg+Wrb6sHb7AFHdbSHSZyreL8Asb\nF7v/eROD9Rg+5SMwFL+zxYt2BpK3ZLAit5QsqlLqylP2MoW7pJYLsLuMWSA9\nF2phv2twCiUDITUKNcJuPKX279tQInpE1gS5iUShJNj26ZOwecQ2E6PZJ18C\nRmeNBqhYz8IzEao9z4fAxpDnOZVvB1prrFG8Y0b/qCSh9IDEICmJyScMhIk6\nMz8GKC5SMUHTrHez/yP3KRwmpMJydOu+0xRcAtxV6srH3e434KwvPFSPS/qX\n9YK+3z+1DThA/SC3IioZRs4RzS8vwqqZ+jou9b1CAl3aQyp1BlaKaDkDcHIi\nozjoyrKaoZpr3lGett7Fyu913tOdKXFl6TVIdB6DtfsMeJjpiePY58JrCXSh\nOJjqG9AB8U+xmdfkXXLD3vMXGyiW6hrwhb4Oj24U4knuGBD1EmYM3WtR1V6/\nabs6tYf95wwucvo6GXewUD4TisNXpUBVyhbprZ6zII3VPClP8hEJsAOUG25X\nC8XfTX7LjeJLa4/3SmDXVt2N4zak/OrV39JcxNojbh2Q5TK8Ya06dasAZjV2\nr3rk\r\n=C9r/\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.9": {"name": "@types/eslint", "version": "6.1.9", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "f43351d8830b3273d4eb4c8c24d0d6e38fcb0fa8", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.1.9.tgz", "fileCount": 17, "integrity": "sha512-iGnyI+jgHqGr+Hgcvnc9KIGT2A0h6bx2wslVlVFxTBkfIMMAEWWoVa8SV0havS8ITNo8ayzzgj9TFclwhw//8Q==", "signatures": [{"sig": "MEUCIB4KpPwarBWboqPjXxlW7khmmL62cawOF/B5X4FGspJPAiEA0iHC8ibtYhwNuhZPbIsFrf0pWVgJ+9rKxQNb1J0Heo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegpABCRA9TVsSAnZWagAAWx8P/jIlQYE909BeyOuocz3R\nYQlXivaMNHTD5NqFXhEOgWA9yz+4iAx/Szlco1zMMW1j2VMiSRBZRGPjW5Jc\nZnPpV3lb8N9JMIKI9nk6gJgDToviTiwT5acJqri92wKnxUEX/qUthoUshxyX\nrRgpk0/lVtWNCUUhMA6aZ6oSwMGCNoKo7C4lVO56xTzEFTYKnNeDbmLEgx8r\nvbg0lCtltWDlY/wj0LrGU+qzkKmQ0dYoZkkTOTKq9gCfQyTgKEmtjDFyC/mB\nsxguTUxZ/srDOvjur2Pfg0sJu4L+FvHfJhoyiXOIpjcOKMppsj12rLtf7zHS\nPZ9AMAapsChkUK0Tsl+xQa4FXD5kaia1Z4+3udq0SgJRltPgkWLP9X1GA0A1\nqruP1nViD7xXL8RpSizju5EJvN4AQQvseLZWmHkkBDWFTnGbu1HfsR/WWmDv\nZArSDf5aD40whxoEZK6Il4DVLM7k85iKjfAraHFjncSvruSc+bEMKE+RDVqV\n1uF0Z5SByNwImjLaC9b0jiJWyBEVxumDSw4y91loPe+Odn2mVYQKK3gL9osU\nCmSZcvS+gjjCj4dz55GT6wJO/23Y+55iigUXaKvPKOY9v830aorNNk3Xx20n\nt0jfeFmyHuDEUL1zwDSDoiLdRR/G1krCIRRyiLBI4Kiuf/CpPVAvYUknaODf\nz+Gy\r\n=mFAk\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.8.0": {"name": "@types/eslint", "version": "6.8.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "5f2289b9f01316da7cf31c9e63109a10602a23cb", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.8.0.tgz", "fileCount": 17, "integrity": "sha512-hqzmggoxkOubpgTdcOltkfc5N8IftRJqU70d1jbOISjjZVPvjcr+CLi2CI70hx1SUIRkLgpglTy9w28nGe2Hsw==", "signatures": [{"sig": "MEYCIQDicLROy1JNZLnEQ5Jp2bOvl1AZ0EhQ6RIROieRMp2VYAIhAK798pPcfwgcsig+YbU0nPWf9BQXM0isCLlFw0cY2AYF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei4/9CRA9TVsSAnZWagAA8L0QAIFUIhRB+AKopJT0mWvx\nlmiJw3wdQtwanbRZYAewpExX3YLJ4TXLNJnyHQsDhNvzvy/EbraJBCwzgXN8\n1rgXOjD6IaSndhDhZ17aJZX8dpbm8l2Sut0xej7FLfaB7S/wXeUBA+uEEjGR\nqy/wytwpOVABmtqJjfP54nABmFlcnMfcEGpA7vKPHNwIZ5tm33a1ZD7jm5N/\nF6LyXAQ3yXsj7yIzoire3qqwoh/fdfDh9CFcbAIEfc284KmrIQoEDeg9WFn3\n/zdonD9AS2dDiTe1noDLipbFtNltaK80lEdwAQGT5I14fPO++LqSHuYvORZR\nlHwA6D1oV+xGazMtUwYg1Qsq+stWqgJO6HOQvDzdNmZQLRnV7JEAQeVtu4HM\ng0a3Wy62/TNIj1KPUxED855RRTrW1uV9lFfQOZPF5rqmVHD/3Z5pvd7ycUht\nbCV0Y1I9yc8LNDXV6hNWIE0FyQqVoBwaZG/aTKNusIvK/y+Wloavzv3dlE62\nwO8BV2nve2ueyJc8TGWXSE9KNAXcdu1/YAcNAUzPFJykAUM2NsA4YKE01zar\nTDetqszcCfRu9Iu6lT8DwSj4Ufc+yo8CTSBhRypbv/eIUwPq9kCh9RgbP0SS\nV1fK70/TQS5kC9Zktsr04bah6es6zMQkdXfwAi+j4RF2kzlcO79uZBGK817w\ngqFZ\r\n=/zbH\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.8.1": {"name": "@types/eslint", "version": "6.8.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e26f365a5dda12445d1d5a17eb70efd7c844a3d8", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-6.8.1.tgz", "fileCount": 17, "integrity": "sha512-eutiEpQ4SN7kdF8QVDPyiSSy7ZFM+werJVw6/mRxLGbG4oet6/p81WFjSIcuY9PzM+dsu25Yh5EAUmQ9aJC1gg==", "signatures": [{"sig": "MEYCIQCMS4ydc+chEVPl2WnhOxdU63TBNyIyARfJ4Fbdutl6UQIhALSwFEbSXg7Z2bw/W0CLEQ2irndZ1YMZcxsSCnPpyjSe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeviO0CRA9TVsSAnZWagAA7LUP/ik8AbuTpq6TRnsnXx5s\nWoSS4m81lMfWx159WTfaslw5BEu0vBiCTEfJc9u8K3tKzFQeXmRtfeEz/Yg/\nU/tjSmBocV1CXEi1BKllJ7XvxrOMITCipULEiduSfHVWs3jX6g1Rtgxw7Wnn\n+JyxVlIJUCJ16bNJR9h0v2NhsjrkSZxiG9PAVwLCuthiLIJ2vgYUUttRZf3Q\ngmgrWtE4VQTYwwpNsUNBtItG3aonO4hu5HKJuknlXEDB31ejBbHL+2VEB0Bs\nYaIzdYUZWW10tQUIOPbb3T/BDcgMIeb9YmnllpenGIF5/hhZQurRa9SNPbBq\nZxneiWR0fb2ku02RuFfKwY6wBDU5jHvhc+RziwYSNBBC1HFTcLR1WPZBF8zX\ndbyRR+ece8PxJ23G8FVTn3rrUKu/guGbXCpN21k0ewEdml1LEYU/gqZmHhHc\n5ZPD5Y7paUrYdBEe4F+KoNA4nlnMfTtNJ2cupJF6rDqs1BBE0VvlvrTvklta\nnw5/jLrX4K6wIXLTQsrfNqnyYSmzJBw50RKPa/+mItijNgetlTQcFd/ybH1/\nrxGOKFuKk7DTFRlhWAA78L2S+rwElhmx8kIxjz2NV2ONv33rBK/Aam2IfJWj\nngvj0TWsZsVYVLpVdWsiSzB6YmeBP1BTrx7wbDOHKnQ5eHIed1M7qs0DCtLa\nzo5T\r\n=nQIL\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.0": {"name": "@types/eslint", "version": "7.2.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "eb5c5b575237334df24c53195e37b53d66478d7b", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.0.tgz", "fileCount": 17, "integrity": "sha512-LpUXkr7fnmPXWGxB0ZuLEzNeTURuHPavkC5zuU4sg62/TgL5ZEjamr5Y8b6AftwHtx2bPJasI+CL0TT2JwQ7aA==", "signatures": [{"sig": "MEUCIQCW8CIYCobDm7coGSbZbYA0kAgnUId4texfvXKtUGkLmwIgH//nnU1SUM1/f6TxQmDbJZHGTiZwnsgCkO6qMzv9bDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4r8NCRA9TVsSAnZWagAAj14P/iDgckCNgZjH07u7LiPl\nHpGswGLSlWYVz8dMuzmfJNhiwbAGBuNSA4tROZ5GHRpCPkZMeZdrqjUbkdw2\nzxlNNO42AUb+eJnowoCFOPJIXNXXrw2zrMUC7p5rtaAZFs/KUXoi7968CI2G\ns2eAcjiOY27AUVt+wRtqqcuDRMdKPysFVYbSxeD/cyibPXBeinYdYw7Pe8sB\n5Bft0fNLYSHsXQGMhO5uCoUuaYLHR3xXMFPzVBXY0gScOYO1gEgIldUhLjH4\nNMHrR34IR5FjTtnaMosECCmEjeMdm7EPTXLxmCuX/JZHKLbb4Ztwvml8r8Tm\n1yTZmHxOlFOS+4MxD0o8gmRTEcc9v98Qn4hLJCDAS1lZYlpERz5APX6UzA/6\nMfrw3XSZg9GQCKzST1Szpe+rU1fJYZQQ/sP4pJi/KWZZF1RewsSPpGhGMnvR\nbFlorEw7+mzDWwFvk03vjNvjJG3+okX+e9zUfBJWdOY/AGnRfDDKgO8DnuP4\nGCwbTbLn5cfzHtEPZghGal2M9wEq+B2bMJjJVs8v8IR5fU3hhZNVg1R5l8rQ\nbwNdWXeQRgncHjt8kweGSid6x2fhKOHVFzY3c0EZifpmFSOLFts8CT6SopiX\nDvCHfihgtUM/iHQtkFsQgWUofTGjIW6BFAeKBcfRnSR+M8iL5UR482YY3M40\n0GrZ\r\n=0Qrn\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.1": {"name": "@types/eslint", "version": "7.2.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "fcfbcccaecfb487b0fc5d44686807cc625f358b6", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.1.tgz", "fileCount": 15, "integrity": "sha512-<PERSON>jKivjZyeL65Qt8HLLGwyhC3NkhoPVgCvxPcCMb02k8fCSJH1vvDRefXmSHA4U9TcjOj024yCOxFgCPKC0gMXQ==", "signatures": [{"sig": "MEQCIDkoPUjtJwi60srWmfze5uRFAim11b1ZQYDdsyl6NTiAAiA9k4a90huNgN+a4D4GOmtzxfIkfBI4OX9pt1qhuGIiww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfP/9TCRA9TVsSAnZWagAApbEP/3gItydlUsgltGNTFTe2\nrsELpw3SW8Af9ThuetWR0/+rKiM5j+lbkPnmEk+mh6IoLVcPJuT2tdPTwwas\nBJ1eCLxh9PFw/T78WI91UIEpEp+DeMerAjOGXregaLbQDAaSHY9RLZ1r31WG\nDIu5HBZnGjQprYxNib8SsnPBmyJL1MWzlc0Auxalij+yQ9SzVWKwP7+JdTZj\n1LJAsltH3SCjb+fXz4NKztkdtJq6fNMWy7QhpAz+ETdGg5mNJrqknWxEklNe\nfg81jHXiZCg+Zrqs5kyeQlBw4UJgm5vS/2eeiU87vDaPO1Xiv0J7hQPP5uky\ncWJBsFmK2d7hWFLr6tfpPthbeR09PyU1afvVoMvcStIK3U0KsHdH4FA97K10\nautj0cGhGGC/Huuxv6IbGPuwtN+I5ON6xDprBdHcRaQs4p6s+lkgKmRfFdc0\ncCC1IlmwKqRNXY8eLFHhQ114nmjiI6WSg9QMomiLll2UQGxVfsI0FA1kWnO8\nCjiYs0xuvArqrtvKxJWm+v5hQaIcUdOd3cD19h6umWHTRAIpwitjl0DK248h\nK8EKERFSdMbiHuuIIH9PHNSA5c7jiKYC708YDDeuCaVLXgNUTWN1Oqu7/iPc\nR51J5rVM1kM1PZaFosH/ld/7CWKkUbDWNq2wKmnM/5aSfrjB6B7Wvf0xsMwy\npnAp\r\n=SRTN\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.2": {"name": "@types/eslint", "version": "7.2.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "c88426b896efeb0b2732a92431ce8aa7ec0dee61", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.2.tgz", "fileCount": 15, "integrity": "sha512-psWuwNXuKR2e6vMU5d2qH0Kqzrb2Zxwk+uBCF2LsyEph+Nex3lFIPMJXwxfGesdtJM2qtjKoCYsyh76K3x9wLg==", "signatures": [{"sig": "MEQCIBMMXCpEHE8xf5diKTLuoIxxOBJYIbGPF4TDo7OA1ZV1AiBfraLfApulzngTlavHC2R5Nh8tlWpk0CH+7gQdzGYwGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR/ggCRA9TVsSAnZWagAA6aUP/iTRlbTGYj14x8yHbvNk\nJwRTPUSq2t+91f9kNvRne5ySVY3G8w0axk2+ixuYAWaEocYECN71CWXu3B0x\nJQ4tm98JwUlCMhetoVmgrtqmPtNcZrN6Z+x3HvUa8+RpLcDOcn+DcfB1Tn60\n59fLsc5lVbXn+K206eRHD8fyZEwP/hf9USdja6+5xmiM5rtRbtBWwkZij7Ne\n+7JjCbuGB+QsgVj4f37KdINgZ6P1O4WRB7XIsHvaCINFjagmU5iEdtG9dKK0\nmMkFcjWdbohKKQ3VsxmwoErK8NcbpTWxvgKaam1kfUIXoxoQwkjUfQXEElhp\nYHosh00HMoOWXervFqY84ORHqY6InnaLS7iB4V1470rqhyQ+XFgOtxyi3zZm\nX9Ln6voQClsFH2XklcANacJFlU+C1vcAZt/1NrKCq9U7w8GeI5eZLgBUMst9\np9DOwyXN0O+cGX7ecbTMSxGeS0ehDDFIRkByGvxraDQmcdODEeL+HUtzGohg\nM4PP5Pz74EEBQEaMv4kCWjKsYgmbJE8Sig82OmpAkLWvPdaphjZ0sHM6OGX3\nCbITTmBPUMYCfITCfvuaJDAKbj8a5uojqLOy+r7tCGbjT65HOBTK5mIQIyoW\nNN4YUUTY+02pEnH3a5OYwiCUwn47vmFn9buM0O5QVs4xQqVrNflJPGtJQvh5\nahfI\r\n=J+vZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.3": {"name": "@types/eslint", "version": "7.2.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "a3731b7584fe1a847a34e67ac57a556afd9b0c0e", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.3.tgz", "fileCount": 15, "integrity": "sha512-SPBkpC+awgFfyAn4sjt0JBZ3vzACoSp2zhGBJkkrs09EzPqLbxkzaE8kJs3EsRRgkZwWk9zyXT/swvhnJYX8pQ==", "signatures": [{"sig": "MEUCIFb/h9lO5ztWNhiUIpsROfqd0eOclkCjCm5BfgmQb/uSAiEA+VP9e9lkE7rwr9viNvFIshaUFcz0xXXQE6bcT88+Gz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa59zCRA9TVsSAnZWagAA9ZwP/0RK8fl+q+VUTsMaldht\nUEOo5P5VHFU0elDc5XL8CIK7zxr3W6X1yyhqG52k2FqOQReBRHh9wvkH2bxU\nJwd8+C/KqGc79QGYeg8ID04r8+Qas6fjTpw6HOovobV8dHu0yOBt03OQHpkW\nLbwa8oT67iR12ZXIH+lIbFeocUlo8yfyOz9RtQqHi8DZBQui4hIahXqnSrA6\nh7RBckvsR9avzNtd9SsFgVzodJ1mnS9x6/kWvgdEqvCY3z7ndtymAzj/f3bx\njAlknHXVU7xmlXjrquIttFhHWNtANMLqp6gLWsefYLsicyI+sfLMmechxmaw\nBL1pnsDxgbdhfc+6I0Jd3WSe/7pjOUbXhwAvqj3QfHjuThgLNrUQJEgDwkWl\nXxI/k3pzA1W31HGdcZuTLPnD6eVxOhJR48fB2RosffrFoddsFrCn2Gg9EpTM\n4hsJzj2mdXatZweM3uiRYwu3k/F6/kEjixGt1yxySCE0VCT0PUDhuQaC8cGE\nNB6pT409Gn5ML9uxbYZut1pbLYIWlrbHi3qB6vuzBr64ZZZdPhgkme0ORlU4\nLq2vNWo/QaLAk166qo6tmbi2rXhtqHzE9TPpV3DBuwazevun+umLhFYwr3WY\nbqGfMwEepPnHi4p1nMVlyJC0Y1wtoK2ylZiyrperyvCYmY7/Z3nHGHV69j3C\nyXUh\r\n=YhkN\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.4": {"name": "@types/eslint", "version": "7.2.4", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "d12eeed7741d2491b69808576ac2d20c14f74c41", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.4.tgz", "fileCount": 15, "integrity": "sha512-YCY4kzHMsHoyKspQH+nwSe+70Kep7Vjt2X+dZe5Vs2vkRudqtoFoUIv1RlJmZB8Hbp7McneupoZij4PadxsK5Q==", "signatures": [{"sig": "MEUCIQDR1agjk0Zdpgg0dV/eHxMjJ1dJddA58leeDtPDSszAUwIgF09yx6i2DxCdDsYp4gMC4Kmi2mCO/Zgxhw1lFZPgl9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffzrYCRA9TVsSAnZWagAA6S8P/1OBy7jZksjGomyUXx8x\n3XlzmoLNANMzcrj4wECm4JgvEZBMDYUgG3dQv68Rt3bun2OFYVPrwzI+dQ0c\n42CuDSOLM0c6C45cM3CFTmMNDcMrbxxsN3ukDS/AlQSqKSHnZy/2wYhyJV2x\n16P1UI0as5gFvQcukY1CzRp+lIl1i7MQnJeWXECnnoyYwlCr7vRugSGeNp2I\nCZfTTr9UFc6xnUUz0sNECx00U6K6W+T77mTki62rb0ZbiBWWDbTQk36SdVbm\nONIaz6kEZ4gaYGI400/cUFJJXHLxqWTw1FmQmpbXE2Xzsl7Z3WmHIobsDwmM\nGMk0pklQpVpNmMchBdkcEMumE5MnPkTK0zElYdGeP55LJYvgTeADBP+/ozhN\nTg49Y/2Xm2G9GCAavlfBcpfh6XUjrSBt8yta1YAFishYqVM1kwXypjdKBjLf\n8ftbgZ8L9YHTHz3jpTmpiZS3LGqa998YrtoqOF7xHrEMYnE61yVfAE9rTvof\nPY34xf19XfntE4PL8V91lmiPgV7rPJZmT6WY2nnU5V6pZkzrvkpjCRNtwL/s\nx/iqId84y4GP2OzylMH5/fv3x+OKZ+8NeQ2El4g964PRxt2c0n1rIeFZLZNw\nKfj733ORxyjzu1GEj9WoFYEnMWwTVbHvXAvcFMBwVvae/0m6C3Rs8P88YsIX\nX1dx\r\n=5+lM\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.5": {"name": "@types/eslint", "version": "7.2.5", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "92172ecf490c2fce4b076739693d75f30376d610", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.5.tgz", "fileCount": 15, "integrity": "sha512-Dc6ar9x16BdaR3NSxSF7T4IjL9gxxViJq8RmFd+2UAyA+K6ck2W+gUwfgpG/y9TPyUuBL35109bbULpEynvltA==", "signatures": [{"sig": "MEUCIQDq+IU61kHqAqtHQz7yhFpUXVLWaStRjWJSPzkxHthU6wIgEkGp/Ue9iN0dAPEhhi+JOaox0r8oCwGXjWQzK5XaAyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsk5cCRA9TVsSAnZWagAAhX4QAIlGBMVIRgSmijhrdr1l\nCsWvsHwWYFaqoOvlREYXpZ7TvTf19/lUXFZJ78x2wb5pGIJqm1MP87g/0OUG\n0C6m5GdpTYD0+ev9c92ljUICwCCqhzDbXNV2+I8BZto/uFbQITS3x6ChuTBd\nOiZyhW5K+hX+k9/g6PKozjEW1s02ub5Ahvjv/IydPrJ+KVvN/oVMdVxsKKAX\nXDuYe7HqJ1RwvptcxUKnd0AdReITsl5pRqTAx3UV0BnKAVXiRDKzq+egrJ1K\nBikwD5L8IrVWdUcQIuX7oG4VsG8FJxDllfXmozm+IULBESEuXK0DDjFQIYaE\nVjL0Wr0RAxA138Di+1Q+r3oPTMdSYRYh8BraV0pSWtRraFAxNgl7puDX3nI2\n/ZaNHmXTEYvyQj4tWxMq9nEWrgmxfZ5ZZuG/n5b8x8KtvonMBxNCnVElSNip\n3U5gdbh/RHCCmiwaSzZfp/MtcyqYA6E64gAX881sMvzLhiv1hsdSXaSjALX9\nIMzgD21J5H/Zj4uYHI61auVztsHyfiwlHREwORJsgiLsfCSRXOjcupzYc9TW\nV7NH95qPxhveke+WKvmpQrktA6THqak7JnwC22DWlOJ0f3Q8EK725DEwmVeB\nH92CW+LuJYNrYXKq6q0eoKL7LxkmL7i0VlEzVwG12MwvhThY7X3KW3idrRzL\n9SGC\r\n=fpl4\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.6": {"name": "@types/eslint", "version": "7.2.6", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "5e9aff555a975596c03a98b59ecd103decc70c3c", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.6.tgz", "fileCount": 15, "integrity": "sha512-I+1sYH+NPQ3/tVqCeUSBwTE/0heyvtXqpIopUUArlBm0Kpocb8FbMa3AZ/ASKIFpN3rnEx932TTXDbt9OXsNDw==", "signatures": [{"sig": "MEUCIQD03Ac6QfZMvxqyNQVuHksoqjTs+ZCONGm2R/bdXGLS8AIgcO1ExHzp4DNHRh7lURjo4c1EpzBkBYTjsLoAX/7/R/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfx1h3CRA9TVsSAnZWagAA/DUP/AxkFP0ZkzG20gK8sv5H\nq8dvPFWBUhYJfsJNsKIixXrPXqBzf722XnQxp+Up21q7ocvWH54rX0IgTDyk\nSg9laUMzAkAQpgBQ4U9mq8b1xxfpHTqSiqOcJH36aBbgWUAxXx/25ClIDOGu\nDLUKemm4e/ZQZRY6GEH2B8sP9l/BeTU4LbKTFV8DC0a7+v24PoARnc061v6z\n7dSwUy86YZWWz/kRKoTb9Fkn24pv8InNZr17PlayyztzNuddL2LKC+BdEmq4\nXl88xhRKkWTmxes2f8Pimi03+RZs7MS40RAWzFumh/j8iSVZCaq8aqQ/Hvnu\nCo594JiTR8q+8pdNSoLtCzWYPJo8VaVt/EtwZFQ2T+ioCGrjinGGaoLv/s7+\nyQbOBZ99/Qak73IYYzYN0A7nh1VkqXwbyS7DeXRwndobkTAbozIcHakAn/qz\nzwx75/dvy/acithc2yEoC9/bKdEP/ugp/Rh+/Vg6f1/cUovC6rtXVFHA1U3L\nTlKVLRmf/5mVJ4YujLm7M9nTBV2nmIW5XpK6oPh/BM26xeTRMpcrshtsi+H/\nXRJxmj6/QAapGP5VRP2uiS+bm+52qpIbJyXy0VDNxZ0N64cznxx84VOO/qSA\nr3rqdH/5QCgN6IDcDxq25+Pam1D8XTCrIkX3zM8qZaCD8KDSERZFVNozhPdf\nnIvh\r\n=8FOF\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.7": {"name": "@types/eslint", "version": "7.2.7", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "f7ef1cf0dceab0ae6f9a976a0a9af14ab1baca26", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.7.tgz", "fileCount": 15, "integrity": "sha512-EHXbc1z2GoQRqHaAT7+grxlTJ3WE2YNeD6jlpPoRc83cCoThRY+NUWjCUZaYmk51OICkPXn2hhphcWcWXgNW0Q==", "signatures": [{"sig": "MEQCIGct9THsth/QwD5rG4R8KrP39AEKuV18La+ZBM5evMazAiBbtU1mwEWvwyz7gu5AzLMH762mzs+NieZJJgbHNESBXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRKZhCRA9TVsSAnZWagAAw9AP+gPWHmPgDRCMA4wWK8RJ\nxVym/M5nE6Ear0BizPbA8os10ZKDFZeGyTRv0SfjmoCRV8nVnFJ7HUaxTqrl\nSu1+lzsCBnQp+82bHqes7AufRfiqoCYbtTR1G4FbMLWHYV9OYlZqo1RZSOp6\n0/PbIN/ZrY40q6g77joSMyaj09zRzDb4XBJkXJSkxfGI+NRwXPiCJzW0YyTg\nYd66Z97uEMVtP472e162k4VmhJ+6z7MuLxOGshebBkz7zLqqhuq4gVakk38a\nSQBQDAt3yAcprhR+6DkyYWY/sCP5zkdVK7ZcSk7c9E7bT6CUyhTRuOHUYLqi\nvLKXinGMi4BKZO63Y/lqHJNOrwHtDqtFW4ydla5SSACv7oEo9RrrrfYeDV4A\nwbzO9H9tF/1bql0FlbXXDv1MVHAlNoTlOnvrlfu1zri1q5hPCTgDBPzCMcw6\nMFOJ78knvQ/M7frbjU1XZwAPCbF7RDFpBaO4BOwd/drI+yKz7e9kLecPimLF\nw2v3MVeTXPb3fHkMWyO+6ed4sSfGUHCJAxM7VONlnv2aQN8qQW9bK4MLtTQy\nPb31u4BpNQaRQ3GXqjUtd+lU6daJDJ/W8yximXdYrEYUH8nkTVc9xUqcWBtw\nbpX8MT0wXLG6lUsbb0LFx4aWhEx8ScDLdBPpgHJTCG/vAqyzi6KfvptZSDM8\nKJhj\r\n=6KSI\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.8": {"name": "@types/eslint", "version": "7.2.8", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "45cd802380fcc352e5680e1781d43c50916f12ee", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.8.tgz", "fileCount": 18, "integrity": "sha512-RTKvBsfz0T8CKOGZMfuluDNyMFHnu5lvNr4hWEsQeHXH6FcmIDIozOyWMh36nLGMwVd5UFNXC2xztA8lln22MQ==", "signatures": [{"sig": "MEQCICp95AONMs58zbqikWVmEybluXiscJIP5fqmdFSf0bXRAiA5yKW+cIfjCyV6kGvbOFIuMefe1je5ssCuayEWk1dp+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140760, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY8LoCRA9TVsSAnZWagAAllgP/0nqbnuS/C1KXCmvABvh\nCAzyQw7FVRDsjsB3IQ7YFkCYowf0m/7CMngp/x+2A3DO//Q9w+zOcu/n0GUN\nhAffHo3G3s6ny5l6dev63IlIHEZeLFprTTecpIxf1qKCWhnYwUldmC0Zx9Rf\nXqGWbW7K58FfFMrZAiQUaagVHb14R67AvFxPdEFBv8KhF7rvdYPt+VvyHny6\nMfldcIXrfTqiHGT6zB3xvLJb3U077jslmQyYHcW8af8FHDS832iv7Dn7IVYg\nWG+nADSW8H8pJ9FTWQAUASPe+x4suHg1Dg3+75F+dZzt/6zP5swjOW/hm6dj\nTiPj6X1EvjB1/ivksmLoT3PBlWwVmkOE6FIMXs4NEfqyMIv+40cDz69C7ToG\nRQSCJoOt6fBIt4HOCjbN6yBh7n9TtAHI+kjo927ZiUz1PcuWtXxC89iXj1eJ\nIWWgPTTJAXIoPNdqDazgHH3ZMrVnCBWevWKKosqgM0/2+lyqoMoVF0nF9vbo\nFDdmRvoLbYUDWyKe9S2sbUTZdQRu3+NJbyCNoFQKH9LXXYzwNLBdylOFJCa8\nOVlzOnxl7YpgnYhxW2OKxNGLGHPHEwBzDOTXJhmyg0PpP0rZREHLrA3FgF7w\ntRhZp5ZPMXnUR9FedT/hKWKMGwsPa+k4aVujKbH+jOCQDl69ULV0LXIWCAZm\ny9iE\r\n=/c40\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.9": {"name": "@types/eslint", "version": "7.2.9", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "5d26eadbb6d04a225967176399a18eff622da982", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.9.tgz", "fileCount": 18, "integrity": "sha512-SdAAXZNvWfhtf3X3y1cbbCZhP3xyPh7mfTvzV6CgfWc/ZhiHpyr9bVroe2/RCHIf7gczaNcprhaBLsx0CCJHQA==", "signatures": [{"sig": "MEQCIFNnsLEzlKnLlSHMkYwC8DbcnWJKJCbo9DvCdvco4K/aAiAb+DhmrWIwOXMBTNT6v5M2F+EKJGLxKw8ynmmflytCDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcM7FCRA9TVsSAnZWagAA7V4P/RCIfMKBlkHTZKduUEU8\nDf2P6lC1zqUM1Pa9VOTQMkJCNgBDXFsC1CyiUN3muxpVsUAHFL8PZJ6oxQzN\nZnTY5WeUSQHH5OASsyufm9SP73iysuo68Qfb9AVoPErLW7o/6x5Xz8FQA+Yj\ny2mEUYbXBBxx/iVnWIRt/R4+0vxYv9JxE6FW8K+E6YUSIGLSrRkYo4FPRT/3\nzVQKXmwQVqqtyRaJt4g/BmK0rsJxm1UQ3xaAd/g4dVqx49H0qXPQ+ELPKx/e\nYvpQyY/sCDFmKZGAsXWTTbGo3ZuWhdJHn+bLt6PKZFnh3/36JRYvjlOHgRVG\nfZ3L0iTGyAfurr6Rn75nuCRRvOWuolivB2zPnucZcDvj7ML8zWhYcnkruV+y\nDqH5RJkCwP/2SnaH/zoIfk9+1lyxOmIdGFJYyAN39HeIkbILBgOYsYflYsFe\nSFABl2ABDybwksE8RByhmcED1sDiI9g+SzRMQhdGwcMR07sbgDH+pwTcQj0B\nEyu84vVYtWaDGvHw+kfWV9Y8NJuKxtoTs96Dij0gLDsPwmeUrR8VHMaoZzvE\nsUNIoBl+ftwWmNwlxgYMgSOVILF8IMVmxq75vBpXCM/eYOQ7OBH0WidqvzTD\ni4hcUissUfu/Oxrg4nn8O9DDRIdNqo+3A1BD8blgP6l2orwy+SDzKQwUNjiO\nsSr8\r\n=kXxY\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.10": {"name": "@types/eslint", "version": "7.2.10", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "4b7a9368d46c0f8cd5408c23288a59aa2394d917", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.10.tgz", "fileCount": 18, "integrity": "sha512-kUEPnMKrqbtpCq/KTaGFFKAcz6Ethm2EjCoKIDaCmfRBWLbFuTcOJfTlorwbnboXBzahqWLgUp1BQeKHiJzPUQ==", "signatures": [{"sig": "MEYCIQDD/DiRwiJGwmrdnnNaAz8YqbOBLyDC5N7yaP7ZcRpkRQIhAJpTtKM+UPf9/tp7fJYxj5VTXEELiQ6I0cZmumFQSwbY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdzUDCRA9TVsSAnZWagAAmfQP/jjW9z81RSrGJLfDHAA2\nprkgIcc+ShGprbfw5aQx5wdghRsE0tfiyLf9r5N7ECjfsi9PfCSbp7N+o8qM\nGo156m2X3zzfYN/inx9xkMdEw3Tg6jTuFpzh2rv2Ky74gMjUsgJaRBApwXWu\n9bAMdL8gI0K7vkcTOIHfIZSazapb3jKWsn3wJxqlPVXT5vgAQl9XbwZNj4Vf\nxWUV1EMyGFB0MXcWdqZ/cJSD0wAizZbVKmmxLI6klSWTporOXVYUpy9KZuZq\nRAln6vR4sDSfLbh1wYMDk4PQ6bXL6+fCa49HZFQph//OXyHBWM7G398lwcba\n3ka7yYrS5JsDZpXv9b4tPhbYONWLqTAFKKAj8xUSWXv2w2wbVOySXoBwUfkU\nB8x9fx/1kvFoCmohm0FuIo3U5oZ1UhIb6tmvs6fMrIpPVWWCGz2LFSnq8zWe\nRipoxSaqVDk9ViRevF6hE1iR/C4YZjL8GExwqSsWwJCQqy4ShceoLGPeqBtB\nDYSLWNuQ/iClQRWNEbPTzyDtUsg3e2QHJox/+V7l0xuTc5fwP7yqVZ1escmb\nhAO95CK52r69RtphF+7dIEnNYZmo3imx/dUHYlruYSwse/Xn5jmkd4uHW/6/\nzV9PgHU8YFTC74kH/mhCeCFOjI//QzIzoJH+GkjDrNVQowXPjAqUU9cTbJMk\n2wOa\r\n=CSBP\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.11": {"name": "@types/eslint", "version": "7.2.11", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "180b58f5bb7d7376e39d22496e2b08901aa52fd2", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.11.tgz", "fileCount": 18, "integrity": "sha512-WYhv//5K8kQtsSc9F1Kn2vHzhYor6KpwPbARH7hwYe3C3ETD0EVx/3P5qQybUoaBEuUa9f/02JjBiXFWalYUmw==", "signatures": [{"sig": "MEUCIQDqCAAYTyfP1SsUOpkv6h24/PTcn1M/uQYdVLPyfpMQDwIgeeDQxXz/ZthcBlfyRZIweiNFv9FvaFZ7qAaz/9QKJt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpxuXCRA9TVsSAnZWagAAc3kP+wezL2Lye2h1wr0nCX+o\nni8OhMBDxYKJqHujEioP7Fn6f1kGGn0UmswlZDtBssfifOhkdhpCU2HezOeS\nJiCe9nNdAvcoiRncioAiSp6eRA5XydHUUb4LCYBlnwP0TUXAPeWi0lkESDk/\nd+kt+RqqFOWEvF7Km81+VH7BS+HXNx5GKdRtRPSTcCNf4tZ2YAT1I0Emfhrt\ntms84XZMh5Cng0MW4OPSsgHacWw2xl6FAMTbQpu0/mvniD6AMjrLzwXRq3+v\nXMbqkdo2fJDMwegt13Qy3IrFRVmGsEnQq/dqgxPmQ9wp2gDNc246gFHCZwY/\numKft+wzWGV6aYeCWdJDvV8vPAf9ocenn28R5TKhEXeUWAUx57sqxe8C66IK\nGwpclBthw9xVok6WJLUYjsAaLzv5IPHz7VXmxUm4sJxcETxJUsB8qsKfC/sr\nScn0pxyNflZ6DV8pDbINe+HGRBC6Gz/57HM3W+goVhQVJBDaZNmqHZ8PLBqa\nYZrWuj+KquI2yb+QOzdLYHFVlphUSzWiYrCMvCT4RWUhodXs6OUJSikx5Fyb\nHwitKuNsuTkBIAz0W4n+AIHdtECuLfeuhiwZNCWUsm3ViQ96p13iAiBCQWSK\nnD5dlRkze5et+2Sl7R0E7hBZXRpvp9934mZk7m9W60feaawy4lalmDA4aZ8i\n3BN5\r\n=HQS2\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.12": {"name": "@types/eslint", "version": "7.2.12", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "fefaa48a4db2415b621fe315e4baeedde525927e", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.12.tgz", "fileCount": 18, "integrity": "sha512-HjikV/jX6e0Pg4DcB+rtOBKSrG6w5IaxWpmi3efL/eLxMz5lZTK+W1DKERrX5a+mNzL78axfsDNXu7JHFP4uLg==", "signatures": [{"sig": "MEYCIQDPuN/5BKBpz9eanIm/z/WJR/3/dxtLpG2nZsHboZY80QIhAM+nD+2BUjEHqaQAKmcguiz1pm6q7kuwxnOJeOJyIbw9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgru90CRA9TVsSAnZWagAACj8P/2EnYc5d3SfJygXWq5zM\ndpMiCyiqsfe0ykjxNE5GCVixCvc73IzaE1VdlTGwFehbqp1vHQdLIvSaA3kV\nSyoi8IwyDuBJ5kJA5wL2OBW67gRqHBn/YNNFEnUdguhudJs71oMLpAjGOFUN\nHbJ8uhN6V1L6uXCbVwn8U6GdFOx45rnDU9UV5rnYemeGFjD+nE+rguV2xnls\nH4ysPCyEfJbDFb53DZ4S2UNCfIiL4tTlPCHgro4c/kK/R+C8m76bvGgHMJAK\nTNdsWPSlj7V+luFHWulZjO2NziTyb7VIvnel8gqi7njtibwJVM7I/i9Gc5tL\n8g7GYtWYCFqJ8Q/4tPa7/qAYjMAW6y/FthM84cZxhZzeeOfOAkcAloFfR1Mw\nrzQ/TkzJOTJYlmZ2Qz7nOlr656otWSlkmg95RG38jQBcY9j3Xt0DT+PVq/ul\n9hzJYS7BjZ8GH115K9H1QIGqvb7uG2GEKCpk4QTu/cN9sMUKhjpyH6C6Nyfd\n0ofEYS9V/FCBacBasJ2zh+E54mET7DPtuIgTPhdltA/tWcmaMS8IA+nx82l+\nMUgZqSwasQaHEO5EKYypl+xM1PLQC7REKZNWqIxUFYAY8nkiUtVT2giFIrFB\nKfVZd1XlcoB5zcWxUCUXeVKzi2ExzJIbN4LwcyiQveXcpbvQpmXLA7SNlKp0\nHY1T\r\n=4vSG\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.13": {"name": "@types/eslint", "version": "7.2.13", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e0ca7219ba5ded402062ad6f926d491ebb29dd53", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.13.tgz", "fileCount": 18, "integrity": "sha512-LKmQCWAlnVHvvXq4oasNUMTJJb2GwSyTY8+1C7OH5ILR8mPLaljv1jxL1bXW3xB3jFbQxTKxJAvI8PyjB09aBg==", "signatures": [{"sig": "MEQCIBe4hrZmqYiX8gAvAl3IKOC8pqW/Wc7tbWyeKowzbDStAiAtzdO9yWMIRVg3gMI3bX495DbeNXSMa8VjoHY0YjsISg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtqdhCRA9TVsSAnZWagAAUBwQAIZ/LKaSmHNLlISzcRsH\nUspbijpcEnjNcy0OVWQCeu69wuCw4wewsCqG7v0KT2toC7LbLmnIfK3fUvOq\naQ7CPSxp2bUlJaSwlJKYiO1dQ9k8WOrSo2xE55XN+qvdPg3KfRsvG9YGXB+B\n56NMD4JojLaci8i6qZnnDBSsSVwvwZF8rOYln96i0ES9OJX+RAIE5r17rOJd\nq6LOka6PAewGwtPVN5YIg6LeF6lNSCJDJSMQ6WT5qOl+XD0ACSzPf7vgcgTa\nU80AcBSMFhd/jcTALudcGXLqD+xm4I5r+jTn9EqXkTV+aDD7nlThaYI0+ntJ\nZl2hpIDWQ5B++s+MTBrdYUb8vlhnWgbxaOlFTIjgTd3GSdaxPxh4UcW9DSB5\n8vSGoq308a1RRtvqZelNhGwqM9IOkBA+6wUD6gptL2PtIZyZjPoaOFnWJiDR\nvrsIQEvXIPPrz1ec2pi89Vj2DYexRjwMEJ5PJe/8yFt+Vr+R2IygvYGbaH0I\nOev6hd8S2o1TIlW40RhX4NohCYDdSAdta1oRyXtM2CzpJip30lrPMW08qJ5/\nfAyYr+7uJqpvFoMr4gnwef0Axq+vNYTg6NM8f+uS2lrBmpTCWC3bEzhTtGKs\n/aDNGTLkxyk/2sEQuJ3PiM1x1ZzBfUfYsSDNWAKaaPTHgb8aUOOcrD0LAiLx\nDN2g\r\n=vScW\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.14": {"name": "@types/eslint", "version": "7.2.14", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "088661518db0c3c23089ab45900b99dd9214b92a", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.2.14.tgz", "fileCount": 18, "integrity": "sha512-pESyhSbUOskqrGcaN+bCXIQDyT5zTaRWfj5ZjjSlMatgGjIn3QQPfocAu4WSabUR7CGyLZ2CQaZyISOEX7/saw==", "signatures": [{"sig": "MEUCIE6ygc33chAalFC0ZQe31/26sqDRoFR966+weFUbbm3WAiEA4u5HRNe5L/CdGnxEbx7rqAtsgwnhjF5JgcSr/GMPX2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5KsECRA9TVsSAnZWagAAbDQP/j64yww+P2qmBkBDLfOP\nyAYjjFyylSMNdrpCcr5UJJtpyZxMhVvq/ScVrU8VJA2qNEIK4XisLuyXDRtq\nEQq8jg6DuywTZpx35GQUP4QtIVeIDkdGodLyg3G049C/JLv1LEuKa5ZaGtBp\n3FFd4yl/rO4/f8HI9iSQrznNDGtZqxNlLRThucmHY2BCZVKg+GXzq+sDiK1D\nAE65tgbrRFYAhHnR5AJ8gAeHpKdAw4kALAsGWIyQiGlYlisvuALdNW/fHm0T\nGxZmrJDU0GktDE7EyyQKqZAkaxhfQkWwa1c/SKIJtLj3Hw3B1M6G3dUDVE3L\nZBAptJHqROgTdQuFi7YQdqWP08XTi9L6fVo8JCeF4uFfQ+7PvGB6yfuI00z2\no/seeE61gosqyyQbp2i78Py/RrJOQhx/YKz9zRtcSZ7v6W/gXgOVXZ2lXmLJ\nJqHpjsLSTYTbyM4Ept07icaVkvA867NamFvLurN1Maz37FvS0Y/jDhzTHAr3\nTs93oTsM0wm8xbh2osV9+XjQInfTXqF17O2fmUtY2hu4f+6YxYkIVs80oIbE\nF49+P2We/lhC7FoVP3ygHF8AfqpaM4XRpvqNRq9tbVwQpmovxBAaK1XKJO0x\nrVRRreDsmfZrsDe+Jvw7Rh8LNSQCO0SNJxxRvstKQRuE5S77/NB8ADqialgj\nHMQM\r\n=uA2p\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.28.0": {"name": "@types/eslint", "version": "7.28.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "7e41f2481d301c68e14f483fe10b017753ce8d5a", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.28.0.tgz", "fileCount": 18, "integrity": "sha512-07XlgzX0YJUn4iG1ocY4IX9DzKSmMGUs6ESKlxWhZRaa0fatIWaHWUVapcuGa8r5HFnTqzj+4OCjd5f7EZ/i/A==", "signatures": [{"sig": "MEUCIQCtjz3quSxjhkGy3OgY0W0XBRGTplmOP5zwl7ecBtZlaAIgM0lI1t8ze+qOi/8cOBKLQ74hVc6Qj7SUcEVPN7dKYCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7NfuCRA9TVsSAnZWagAAghMP/R5/aEyqVMydcyGLbxGE\n1aZrs/AA1wf65RIzFnBRVtZCCbAVDHQdDdt0NH+a4aS/ZfaALFBAn39hsnLe\nMXxF8JCRCWWOCXuJnDm4KN3LOaroPPMI+UIpqu6CMwL0gdIXah8TSNgIvpTx\nHUoO+QGyOwky1RSMFv1ECR295rW0fND34zH61Gp77zIND80mRyXFjqQCb6Il\nMtsQaW+J/Y8Os8idqQTDoH72u8OUNaVZrzwJgG9nGaCZ4rYiZI//rCtkajJr\nuOx1GLz+ATTSPQ4bvzrMEawCIOZukv+Z/fLQtSB9zDSCnTFIHPtfJIIKRISR\niIPnfHcsSW2AVWY5CXE4I2jUPGop6/lGVWh2pAEiMM4BA0Gt9UXbwXp0d/LH\ndvIADvW6x+hXpYn3SRpxB5O6BrIQNKEme3g7Z2ayujrm14GfPcokv5+QjxC2\n+6iNr8BTt8miGGiEYFgtGed/KWUzCXu0+iwehitrHLLErqWXrjsbxefSpxhu\nd/cnHIDTFYC5CoNUo7J6ZRhEg/S1q6FYCvJKX92ng8NgGvpApfIc03SAECEs\nr0Sno2gl8VHrrw4Palvvb2It8DjOq9OhdvrXXcwXnOE4+rFOSmcUcCYzKU03\nQPqEvy+6gHjQcrvUBeuLCA2e++ie4hH/4JrkilETyZ9yVr5600AHEj+4UL90\n0SYB\r\n=sER9\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.28.1": {"name": "@types/eslint", "version": "7.28.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "50b07747f1f84c2ba8cd394cf0fe0ba07afce320", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.28.1.tgz", "fileCount": 18, "integrity": "sha512-XhZKznR3i/W5dXqUhgU9fFdJekufbeBd5DALmkuXoeFcjbQcPk+2cL+WLHf6Q81HWAnM2vrslIHpGVyCAviRwg==", "signatures": [{"sig": "MEYCIQCMYBct9d5yEcAxnIhF7Nsqw4G3BJq91/cE/TU2+1Lh4wIhAKs8n3YtwxrjkutSz9hx50SvXtBfuatFy9Ul9CW4YRWr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164208}}, "7.28.2": {"name": "@types/eslint", "version": "7.28.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "0ff2947cdd305897c52d5372294e8c76f351db68", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.28.2.tgz", "fileCount": 18, "integrity": "sha512-KubbADPkfoU75KgKeKLsFHXnU4ipH7wYg0TRT33NK3N3yiu7jlFAAoygIWBV+KbuHx/G+AvuGX6DllnK35gfJA==", "signatures": [{"sig": "MEYCIQCpL4dJsP5kVeXf4GGxYBmglupW1gyi59loMbEuk4MtxgIhAMf7JqXl8UT2S+FBGm7m6lCwN6jLg+UqvAzl9GK04Kjk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164241}}, "7.29.0": {"name": "@types/eslint", "version": "7.29.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e56ddc8e542815272720bb0b4ccc2aff9c3e1c78", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-7.29.0.tgz", "fileCount": 18, "integrity": "sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng==", "signatures": [{"sig": "MEUCIEQFzWw1iby/OlH59onYKvUVBtHrFNzp0H3mt4nte+quAiEAi150OeiM0SLAsW+bA7oo0AMn+vSxSbaTF0WMuexuJH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164528}}, "8.2.0": {"name": "@types/eslint", "version": "8.2.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "afd0519223c29c347087542cbaee2fedc0873b16", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.2.0.tgz", "fileCount": 16, "integrity": "sha512-74hbvsnc+7TEDa1z5YLSe4/q8hGYB3USNvCuzHUJrjPV6hXaq8IXcngCrHkuvFt0+8rFz7xYXrHgNayIX0UZvQ==", "signatures": [{"sig": "MEUCIQCF7LDws4lgN49Yh8OX1U1KKlMdEFBi0fMvqTWuGpKOPQIgai2R8+4ocHM5HsujKdGvgceISlYLMyp7YAtPvdHj9sI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlW3GCRA9TVsSAnZWagAAv3oP/2Q9Ujm1Y3OBGAF3tNeb\nHgk5SuicplN4WL+b1GJyrP4a9eRlrhbmiEQOurgsX5Ll0d+S7JimAPVLVLHo\n8xPw+YGnk9aVzs1Zdu1cEHtQsF3tBqofRejbZuo/sT1JDxyDY6ClhwNb+TZJ\nYHF6xVla8qj9NA7NidG+71CFuaw7XzD+8+MiCwxt55TQp9eRpfDQ59k+Mtmf\nMFTbp6jcsAeBjPaPhkP52+qdaylNRxeKzQidV+Xv2cqLNRH44x4QiwgvJXwu\ntRzvnh950lThei+kwcp/f/a5fzvK/n8dJDsz7We4L5pjgpacQn9AjS1y2KQ/\nzacGsXuuYDORVp5wB8Ywmyihrm7a0begXK8DqonHitgGJ535J3ZRee7Af7JS\nYn8IOWuuzvlrH3ODBIIn7/y0mxJ3XMWvOK+7tCo0I0H/K6jPouIkapsvSTcb\nropOsJmHmCcDRYd9FShXDY3tvU9kKugW7eLPNkl7ticHOmKvnChj6ZbBvSE3\nM6fHPN8EnYSC+0N7g75sOVoWZnbsfxLMSc9krdNTEAg7vvSwa+C9H/F4uMta\nRFUwjVN50IERxY4LHx+uaUILLfRW9OIKQ9NvXuCHOnrK38AXLMUaSlOJ4jHX\nybxJTaouUPqloYO5N8ETkQ3mUaxSzvR2F/TF9dyiUsBGtqCdJW76zjkLiR7N\nxGNj\r\n=hL2P\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.2.1": {"name": "@types/eslint", "version": "8.2.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "13f3d69bac93c2ae008019c28783868d0a1d6605", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.2.1.tgz", "fileCount": 16, "integrity": "sha512-UP9rzNn/XyGwb5RQ2fok+DzcIRIYwc16qTXse5+Smsy8MOIccCChT15KAwnsgQx4PzJkaMq4myFyZ4CL5TjhIQ==", "signatures": [{"sig": "MEYCIQDAkI4iAjeWw9+nr5E6bNMq3jYQKdx14C26Y3DPfXw9IAIhAMEkoybXf8qmV/OfvXb9T9ZKdAnEDgCw6uRT43PdTCqA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqmKDCRA9TVsSAnZWagAAcQ0P/3AD26IHWs9NTCCqnhd9\nzVAjEtRG+Im6+OIhtLDbB8gui/bWkYNL1TRefED5MGGz6drKppA6URH/0vMx\nptKSISGYJQIihKqQxWp3nDZ2RQn1N8GyA86GnzWHRx03xYMtNeO+WhSDNvMM\niF8OgtHnpfU7k3Ax3l4YWTha5Arot94rUoQtmfkHfVzt/Z0dEkXd+timCXl9\nnHSL6CCYy1teWG6+gqYEY3zDjZR1x3r+OWoNKV8qKHui8r8+s53tlaYmv6es\nUaG/nOjDQIUC8nNawP8KEBXSv4B4nA+qmwRoKQZ1VWibos2cuJ1ZoFSfWLKR\nlHwuS2AtaX6GiwxyNfr2QyCo+3y0/SfR5GSXwUVOX/U3u/Ngtc1T3gBuxsn1\nLT+aQsPobnosGzpJxUDGKgD0m6mfDXtPKAbnENUGnQZX20fY2L6TqGcZLKfr\niqOwtxJkT88SnjSnOVo2xHd5I1woEOCLm75ntqpMYe/BNcnsii8fVsq0ji+I\nZhx1QK03hKx11H5a1+8hiB4GGPEgndScGMpmN+C98/6DcH6jE/o2J87Qpg8V\nDT1xhrwfywuXOaTlU0G3RA0BjHkTap6DWZvX/vDzvf57qbTR6aRaWxfqwlYo\nAGN00aRZal/0Irkv9Qzw/8WCMQRpyaKc3vjRnPbYKyY/lhhdeGa/5pEojhrm\nKo7t\r\n=LQSh\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.2.2": {"name": "@types/eslint", "version": "8.2.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "b64dbdb64b1957cfc8a698c68297fcf8983e94c7", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.2.2.tgz", "fileCount": 16, "integrity": "sha512-nQxgB8/Sg+QKhnV8e0WzPpxjIGT3tuJDDzybkDi8ItE/IgTlHo07U0shaIjzhcvQxlq9SDRE42lsJ23uvEgJ2A==", "signatures": [{"sig": "MEUCIQCm9gIs03+/NPPihJC8ZJo6vdoa5MWjXlMudjtPER+wEQIgJhGumqQJ/Insyq1FwgA9UdodOdLSN7dh27dw3VHS6zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh14LnCRA9TVsSAnZWagAAo90P/2iaSO+iektouqo4cN7z\nvuXF99zpMqI5AynVH5BZux46JGT5gugB8wH9pQIP40h3utLziSu0hfsJBNH3\nP0mjEPZtSMkuXqC32ao3pYweKDmRGIvala02pcT8iLcZznS5I3m3eUMm0Kz2\n96yvvjbRm4jjyPxXJ0wbbK0Ir61ZKADcjr6K9SfFEc66f6brSo9OVVdvAsfA\nekGPzJhbC1S/gfBbc1XkJRMylF5qY1z17k9/a68FLnf7GgGKq8kgsEPQlyIF\nuRht7MViWsHu9ARTJs9LirDJFsJmqvU5X6mNe2r/3PjgLgyiXpVeq7rXk9Ph\nt01Y3PXzfQQbrIlu30DAKban3I/7pU4HccrKyoKl5z9CbQQJyftW3n+P9bmB\nSAdwAF+D5XfV4BPuLqj0IpAJ8f9qSqcEa4obL4H7EjvYtJ/L9u5A1t742C3d\nJ1fvGx6djeq8JjNGDeI8qPXBJmjCrEEdPHt7LH8RBzDZk6Bt+uNwwL/ro1Fu\nthw6Pgs14T0t1dQ9q/i5bVuzwVLdaIREpJZ38Q3KY0lfiZAgynqqfQ7UKl5r\nQHvdkumY1ZAQE7Wj2YjMaoGhZMywr5PCUbNlAqk1UouBuEOK4IZXHqtzqOnt\nG4sgKm4TyzTmM1b725Oxc9ojAYAx42kG+hiDOrS8nNluWkQQgXp8v6GI6cM1\niRFb\r\n=cfxF\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.0": {"name": "@types/eslint", "version": "8.4.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "95712d5b32fd99a0d9493c31c6197ea7471c3ba6", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.0.tgz", "fileCount": 16, "integrity": "sha512-JUYa/5JwoqikCy7O7jKtuNe9Z4ZZt615G+1EKfaDGSNEpzaA2OwbV/G1v08Oa7fd1XzlFoSCvt9ePl9/6FyAug==", "signatures": [{"sig": "MEQCIGpeXUw4OLlG0AHMjTcICFf0MuL3CoswxEk+6aijEPQaAiAOKiWS2QiLJa77fEOmWiXtVctU5ZVGq9J+isDYHs0AOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162444, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6J9uCRA9TVsSAnZWagAAmR4P/3j4BKpBzov7WA/eXwSi\nsLdZMaREHXp+qfiFX+QN3clKKqEtEtBAwhnt0768rV+xnG+m9GCulU2Wasuu\nRspsMMkIMA7IZZd0zK2QCVeWkxd7l86OpSysDu5p2fwKiZHfL8qu4UT9hx7j\nzriO60d0e7Dyx1PLcCZ31V0ETA8o1b6zdKoD95DDMLL0Znz7ZB9WioaL4+Sc\nVuIb/nett5qU+9+xKW6P+qx06bhCDx4u05nA1lsy0LJs44yi8dnHhpUxbMLX\nelScQ2UB46z+6BStP2A+edM3tY9yRti/YjeG1jiCiGgn/ZAfdk4Dqjsbl/6F\nhHFmWfhOwdXKcJJOqeSgnGtSVINe3bt+kw0epiaFlmlEgpSvePv+1f+FjDMw\n+054Q26thBf0veyf2eccC6yepu1ZHZF1n+LUx24aAJ5RAfVC29hWR+YIbww3\nRXbxNOdhhrUhx0TmgNdb9DJHTvdE50Xc3+cIJDCKrEJ/xDL2MzNmC1q8qQPK\nsGfBRgugXiSGiTeX1LkyixOIeOFrgyTyM9XFnmmi5IGQsggQ8xvgL+AxTVSc\n25x7uw7J1cnMr/QEhe2q0oZUyIuhXc/+g1YjVyKcGryrsQQwdRHrYj8uRS29\nMKttNpSqnyuISBBMvBt71/HZUlqEo9WwqkGPH8QKfQs3UKqEIMtFFa8+tZ9P\nRM7D\r\n=gZfE\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.1": {"name": "@types/eslint", "version": "8.4.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "c48251553e8759db9e656de3efc846954ac32304", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.1.tgz", "fileCount": 16, "integrity": "sha512-GE44+DNEyxxh2Kc6ro/VkIj+9ma0pO0bwv9+uHSyBrikYOHr8zYcdPvnBOp1aw8s+CjRvuSx7CyWqRrNFQ59mA==", "signatures": [{"sig": "MEUCIQCvuSJXKsn59ODR/EOao87wtel0yY21bH0x1Rot+DEANgIgbro/QJ2tSwpwWkGVPGCzXXgPKM7RWo1Wpr0O55gPzao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7FSXCRA9TVsSAnZWagAA8K8P/jHuN/oQEb3QLmYgYeW3\nt0L+zArArrAGzf0VIlhjS2D8g7JaCviUhvPyeT0zUvOBdXC/HL65dE/5S7v4\n7M+ted2+NgsPU/xBMIPNLOT4XXnZwqOtrxGgB5wxSK3Qv/GRLmW4dmlJlMx+\nWH6aiSc1aV+m3DkoF1Y3Ms0WTWqieZjKKTK7SNhbaCT+kNeP4t/Zl+TkWUqn\nGqLVNHkRsEKCtVLZTiCTrJFTjNT6qncXVRDJuRzUptvlKzXOlPyW2/Eq9RaB\namO7OHw+IDf5W5YYnEh3F066rxKSNmBc0pgyc1OSG95gjDEN0UADdvEHifsi\nm33FiWlHMol9QU8/ot6waEjZyeV4n8fg10M5sTFDQcYtYbI3qYZJv7uG8Vx9\nkCJPvAu9yV2/sX/5KP6XSDlbodVAJHzJs/kNb8hksDj1VlwQPrz5+cgRZG+0\n8PLrE+7Of/Y+89BTAjHl/tzRyth7romynWPVQ3v8lIaaTew1lLPzofzvpBwV\nV3YyUHo3QPtUEmrJgl66Qb4es5p7Eq9bxoC4OwgtVfldUgcH6+WLEqiLDQsc\nWwOl8CtguHuLPJmi1XBaoSeRERAJkkjKS8q6/BDNiCtGGdEsv3O5vV0DQvsy\npPefX87Po5FvVL3cdD1fTsoUnrxc3t0/usGKW0oAsFrPDu0ho4A41xfAKNeq\nGdm4\r\n=avZK\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.2": {"name": "@types/eslint", "version": "8.4.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "48f2ac58ab9c631cb68845c3d956b28f79fad575", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.2.tgz", "fileCount": 16, "integrity": "sha512-Z1nseZON+GEnFjJc04sv4NSALGjhFwy6K0HXt7qsn5ArfAKtb63dXNJHf+1YW6IpOIYRBGUbu3GwJdj8DGnCjA==", "signatures": [{"sig": "MEUCIFS4j2JTm22HwNYaraFleFfB1XfNxcdDOIueEPFMj0rOAiEA6HnwH4UYeTH81XSdubpkLody0afUfVnDh+93cJOsQpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVkCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWtQ/7BW+/NN/CuyQye/gnuKFK/O4kj3o7Y9wrrs0Mh3Wz7/HGUUM+\r\ncrXw7Gze2FXiF6OXz3dzty80bCnTb38e8kEvwyqpYXuZGJYkAzcAPsPRXlSb\r\nq3AnwzOuBy5UpkM6PUxY2/tACoZGZ+C81UVBCy2II5P23oz6GXE7DrcJuXtt\r\n56ATQ6NFqhjlQrh/ge7CXmNdKPSk6cA1u4pRPmeWP5FBsc/znj5KU4Au66Rm\r\n0ve7/Z0f57Szq0542CEQA9TOyTPTZAB+DriYZrPU7hh2P7+sNEZ8beUkoEZl\r\nsmxr96vboinbb4nEeqA5TvOZdM0p+emi3FCukcOBGqL5UFqCSiPnlj5Is11n\r\nULbFwPFhvg2ke/dPV/OWJe06g2JmNmEbX93nkZ0LKC/KeYsk+jaPSuXoKitN\r\neeTC3+qHyrq43EVVgarefXzM8BeUO0v7ywLyMQad9LtEYPcIypuAAVxhFhAl\r\n5gi0MZlnT/O9SCU80cO9WU8X/A1CtvHEFKtT2qicU/3Py1ewb2amX/gt9vFW\r\ns/Rph40kEIzEpK34g6JUxfuMjW3rL+U/sRa5+X0LUblmvX2OiMUD9QvnGrQb\r\ncUj3ooHP/UGEkDAtez4mdZzszGba4Ri9e9XZungkhVTKoKcVfrWx195Urhy+\r\nRYE8St7FIlRyT3xGU9YfAYGMQPuqy/aqvSo=\r\n=ZRSg\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.3": {"name": "@types/eslint", "version": "8.4.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "5c92815a3838b1985c90034cd85f26f59d9d0ece", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.3.tgz", "fileCount": 16, "integrity": "sha512-YP1S7YJRMPs+7KZKDb9G63n8YejIwW9BALq7a5j2+H4yl6iOv9CB29edho+cuFRrvmJbbaH2yiVChKLJVysDGw==", "signatures": [{"sig": "MEUCIAifK09tf7lRL3fELu+kCeT0Bl31VC/tdiJqkSyOslBuAiEAk8yvAbJpodsIxN28LJg7LydVSg3QyZDr2//oSdY8IsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioPkvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqecA/+LlzOqLfehfDHL7ZOWi2jwXX69zNuOnrm/BF+/KczR9Zlnz+1\r\nJXhFdeBgnOWrya8kQPSrSYVyvvaYMm2yKqu6xJVF8JjZMaqO/g3V9HskQryB\r\nzWDjRTuC2xmwROBouEyLetkNHH6uToQaA8asALBVPSH9gxiyanB/GKrG/YtW\r\nWyGmzkcuvxzf5WYsqD0OX4G9jMWKMW9TAwTd7k6aUdr8Lc3w2cNI1haDYZ4x\r\nBhPxu/cOAz2DL6i7+5aliKH0N+vPTyXPLRj1YUWpMRiq75bbff3pIWR7ihK1\r\n9QxmmVU+flKDOpt1PeamZJu5nol0Opbt98EYbij/EqF4wRzXwIDCxoMim0l5\r\n2xkxYW5eW8oKrjc3SPC4SGwYUHQF3sJ0fxrJg83CKLpIP92pYUFVaPwFyiP1\r\n16b5tf5v+xUyrp5DrpkVyDKJRpoSWURiX0mKk9i7EJ54jXNLMU805KJM96is\r\ng8KDGROg3IC/PhACx7zugjYK027CyS5makBvn8J8bdLARhtWMBo7if7r+E16\r\nrH6ERgR3Ck0RntMfELHFGdsc3vRxQRxpKvOgQJWnn7hw4Oiaz/jMV1QzGTdu\r\nf7KQGaTXyOgu5CtNtOC2I7m+EsuXZb+N6FV81NX3MPZoPJjQt9qMaevN3C7v\r\nAvBwI9YwC136c1kCg8pbeHXX+l4OKWSiuZc=\r\n=FrZh\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.4": {"name": "@types/eslint", "version": "8.4.4", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "6a242a86448fede7742a0ed1f444677017db56dd", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.4.tgz", "fileCount": 17, "integrity": "sha512-agaBbw9wzMWtQaS0l0R6I0pewCZNVE23QA6jJUkg7F0HFdMvPrT75fFOtG4+cxstalGqHBkYVXFHP0hTpDnhfw==", "signatures": [{"sig": "MEQCICPahj5ITWQbh9GIu3WLcsB8dmz7CzFutvd0gxvYrTrPAiBLa6LxtDfRdlJShMQpSPArD21Ge8/DbLN8PNEftIuefg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivfNVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpllBAAi7iniv2HldYRm/E8VVoDxvw7KPN9neWP3F6bR5O5P79uIfUe\r\nyDuvkPUzlgX140jQ4ZrUk8us/VoeY7TcmRN6qmZhXTaoiZnShY8SThiWe0dM\r\nDFmPtvgG+wjLhIQdLaGub68qGO8un3RxN7hqxngua5b8WsJex3Wp4n923J+A\r\nvJFgqv/Sz8k5boBVcVNt4Ew9IFx5FvwVhDSFyGPEGcI2sd35xWbHT7UQY6Ts\r\n041I2kCnZm9iob3Vs1tVc8S0xKk3FUHrfwmXTiz08TMeXsGrXBakgadauX29\r\noJlDFJtrdeeKJMgsChCj2+h/gMJoB2CPzU513RIDlvElMRT7EanMPGgglzqv\r\nWWFO3nZP8o7HCqNuIXMLonZC+2EMr3HR0B+8moi90QKW6WxxOMyqQOj6MKb0\r\niZfPYmgM0v01X2xgGRcxzNLYK8tGtDuX6s8RZwyJSe1bta6/HHSssTAFLrBV\r\nggT9f8eR9Q1Pwm97c3N3VGh5OnXzW54Rnoc4TbkScsuKinP3yeeHp7hl6jc9\r\nbTqYPu/N6LXvyDfQMKXs5hZY16PadCKlJqez5nSzwLAj6AgLpw1mjCAEKbWX\r\n2MfJT9QMFqEH3KTo17KjhvlZCUzkRVujV0hkG9RBnx+TaLyHNnH78N6QaQwG\r\n/9dwKyvFe+8gs8yBu+QDg4UXETVxCQ+sl4s=\r\n=m7p3\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.5": {"name": "@types/eslint", "version": "8.4.5", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "acdfb7dd36b91cc5d812d7c093811a8f3d9b31e4", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.5.tgz", "fileCount": 17, "integrity": "sha512-dhsC09y1gpJWnK+Ff4SGvCuSnk9DaU0BJZSzOwa6GVSg65XtTugLBITDAAzRU5duGBoXBHpdR/9jHGxJjNflJQ==", "signatures": [{"sig": "MEUCIFVNezubNWY6MTmC+5UfT0l4oZOLBE9t13EUeZUqLnl/AiEA3OOt8RBdS6Ayy2MNYP0llJoH73Uh8ZBlD7KrepoPAGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivhZnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZig/5AAVpOCVL5gBp5RAgSSM9EM67onqfGSZ8CbiRMua5ZrIj3zzP\r\nyU1BcujSyaxxu+TrHdhWthgXVxQbaL0HWq7Od142JAFgutqyXFCKqZzzJqla\r\n+teOd/Bxptzcr1ijNsTTukhqQagseU/hF5LEIc7s/GVTwXZUYdaUeEk+lDck\r\n29TV1FX6GoDHgflhG0UROgc28BPZeqML/myAYBMat03+2qSPK/lFXDHr8y9t\r\njzl4QTSwn5bPuWiadn7ZENSf7uncSv0lFpRbKEC2V0H4TzYv4T5Z38LS02Nn\r\nAicFi7iY02rBVChyCr517oQf58mO3jbxpBcxx/pFkF+gsyeP8isz16Xov36R\r\niIO7FMzXOSvLFvZ1LTbLUlROc4AnuZbwigYJNrLrIPuu3KqihPSFmv3OlPNZ\r\ndg34KyW+w/8z9eJhffH3xj+rpH9Z+PGm5zC7/O9bvsTlDGH68qZ6L8RHk6fZ\r\nWiUT4ymUBGHA2xlvnvd9E5EbfSoSssGQfwGoDD7GyluP/gmulY8VoUv/gCtV\r\nydiQQLQxUMkLuOQSHQpfTr8mVFMmx8PXis5tmuECYLJlwJGINl/CDyWoGPXy\r\nKbucvJ1FJmAZ0RKJ/Zkh6c94cNb8Pip2XyKcnQnYZsEmfIMaWa6iB8a0OxH5\r\n9rnwSwNWTtlWl1oV+96FvtBtnkumBcPNdKk=\r\n=Lyff\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.6": {"name": "@types/eslint", "version": "8.4.6", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "7976f054c1bccfcf514bff0564c0c41df5c08207", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.6.tgz", "fileCount": 17, "integrity": "sha512-/fqTbjxyFUaYNO7VcW5g+4npmqVACz1bB7RTHYuLj+PRjw9hrCwrUXVQFpChUS0JsyEFvMZ7U/PfmvWgxJhI9g==", "signatures": [{"sig": "MEYCIQD+8gTQHmrvN6xBljAjR8snSkJm1M9EydFc+10vWQgJ8gIhALYzQel5m73e8+ImECV+u+LdufwplO8NnbcK3IKVnnKs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/vZBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmot6xAAj9h1o+43xJtsStpeQ9jguHpMF6exE0u6ZLFXtjfcJRm0wU0t\r\nBw++wTK1Zqqfqi4PkmC0XiKAB+/37SQCOQSKfO4hh4mHLXq/2BdIZ1Xw0thW\r\ns2yoZySjB/bPoAQAfWjjoJBs5QkA7pwTb2jRj/+3lJlGfIUdpRQV8sZCl1tZ\r\nkf1gTtdxXkOGRnB5ww7NiT8Pkeosu623z2nN6D6XNv7SmzhpKNaSKb9FXhmS\r\nComkNvW5lXPFl4S4/4tPOgDgBb75KduRCFeLj0yNLU+KHlp3n2b8wvqZwKvp\r\n7uHbOM0B4Gqn6VYeFcbPMwboAIAX8dzJnMsTlAzCylJn96Fn9UV5nWKrZnXT\r\nCEXKF1NGP26Jiw4kTZmdDxROAlSZeI9i/3en2+HFGgcjRYGOM5ZdcFJjB7XF\r\nlbVLQAhhP2LsQJsj+1mmjReOFNuQKXr+9tWXF7w/F+XrRcSNqDkVmDs3ukgr\r\nDor6yEvdz3iGuKpYnBbYRJpSQLa/7n+g4K7jdKXpaYxzbBE5KcGHKpHwdQw8\r\nkdqxqA2HRYQjMGDQckRpqVly185RN2KRSKhXjrTgfGArqbuFx8Ncn1h5AW+Q\r\nC7t99aDUAvkNaJOGtTnekXKMuAdVoUDxOCWCMMhtui///xLwn3YuT/8K0bZM\r\nghQ0jCfaNU9pDZKmVDvRmObr0dKcZ8+fBqY=\r\n=21v/\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.7": {"name": "@types/eslint", "version": "8.4.7", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "0f05a2677d1a394ff70c21a964a32d3efa05f966", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.7.tgz", "fileCount": 17, "integrity": "sha512-ehM7cCt2RSFs42mb+lcmhFT9ouIlV92PuaeRGn8N8c98oMjG4Z5pJHA9b1QiCcuqnbPSHcyfiD3mlhqMaHsQIw==", "signatures": [{"sig": "MEQCIGpk0q8ZUvtXqZI+z7NhLiRkMGmX1TTYzE0AlBgWjBt9AiBKz/C5xzQb/iSWpAVXhvGC/uxx3tUq/AnstA8JzDKf7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUSruACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxVA/+Mk5oVxisShdQ6LtYSPtr/WG7+/SjaGll7Gh+7PWtbD1vfnpn\r\nyt9Rj7a1kTUoFPvK1dNT/Kf+HwCH0xwTM/R98KtP5/JxOywmUn6hzQUQTzQN\r\nkgn7qrQd7JMLRzJD5I/XLXEMNAJFA5up0Su1t6Z4pTI85neVWaGE+JEUQVqA\r\n6y+bMfu00njdQTEDKryjbfkRZkT7dx16lJnkmINiQG+CQVJzgosNir4noGVx\r\nmdaP1RlYbAo+SzDDC2rAixcfZiPRri33SOeotWQRJrP/ELbAFSI43NtQDqjN\r\nX0AR4YZ2W+OrfufamBxiiUYXK7i1LLPsG9RObM074mdDQx3ERTj3dqd5W9v5\r\nEo4InHgesbqXp5w0FdtfIT20FlscKyPKf3sQ4DOGunYHTVhB+MBMwlhVf+Ye\r\n7hkZRJJpI2jb8xyuU1oAFMPtm88m+3dmSPVVdCQzSpwik2KFWNwGMIQl4DDB\r\nA4poPCh3FTEwutxiUgk5xvkFt3tUl7RBh3bas8HFU8qBvV39HpiCgg3/XyaR\r\np/9uhbwzTiuIhwycbJc1Y9ojTKkJeFyu5c18k2YnqqVrLBLgC2BOXapiP9lC\r\nYI1zHkZDImYDpNSF12xsCQoB1yZodNppEh8kzli8c/qYYuuT8bA01f48w1AM\r\n5x2xDtVJEjQdp5nIQF3FWH0oARqohv6hhUE=\r\n=CE1c\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.8": {"name": "@types/eslint", "version": "8.4.8", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "720dd6a32b8219b9aba1a07b13e9d03b622695fd", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.8.tgz", "fileCount": 17, "integrity": "sha512-zUCKQI1bUCTi+0kQs5ZQzQ/XILWRLIlh15FXWNykJ+NG3TMKMVvwwC6GP3DR1Ylga15fB7iAExSzc4PNlR5i3w==", "signatures": [{"sig": "MEUCIQD3HhTJ4Q/eLdgPdj0fYfv62z1iHfNdf4OI/EZdbKPAZQIgDax2XmmaG+W/k/Kx1X2z19N0LpMz/wD2pv7/6bNOW+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjV607ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOvA/+LkUyvF2iye9S8/+SmRW5zXuJAURnCYTPJX81Fr2mi13Cs7J+\r\nYTeANy3GH1sTEZlVpiITMtUsdv+tYB6m0PAUssyIzbZ0if1qIQ9WYRPCiex0\r\nNJo9vbITGVbKD45o68dzHeyloHCi5TPIjyu95Z/VXOaDP2shkg/O8tpcyfRh\r\nIp7YtIRM189OULZHLW8BLQuGqqqOR7gVkktWz7zuNMq/iluxVhdJ4TRE4x7n\r\ngJOhFwN/FGPSYuDPe82eQDlrsUH+H4VCY3IeV16FaGjoh0N3PPtkecYdQK0i\r\nFxsm3tja4rG4yDOplH1UdToPyIHXpI0mkZMkbTe7JcyI9RPAmdshl6xo4gr6\r\ndVCWWxBsrRDFomV0Hsez83CvN/DeKo21VFZTtwTdz36VRW/oLdyZQv+lR8ad\r\n8KxBB9242OdUcwC4EayWxVeK24GXnanpJ+NViVMaNdnfeMe/sKj7xPH6obzF\r\nGuXElASOs8nUHWX8P1A13W6/uP3hoSfn9yf3ONLskIHFRjxKz6f1qYfeEBkI\r\n3G1H9dNd0bUR/2bO2vmp4V+MtMi9jaWHwaI7rsdwCTSORL7sYuHs+IPoQAoX\r\nLMToIZcoHInX9N72kN7y15dRsT0Xh5Z3MW2lHfRf81625ISnqLaRivvT65BJ\r\ncZeot3Psbr7xI6ROnA+8Ir3rZkvu7ajZmz0=\r\n=tGFV\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.9": {"name": "@types/eslint", "version": "8.4.9", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "f7371980148697f4b582b086630319b55324b5aa", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.9.tgz", "fileCount": 17, "integrity": "sha512-jFCSo4wJzlHQLCpceUhUnXdrPuCNOjGFMQ8Eg6JXxlz3QaCKOb7eGi2cephQdM4XTYsNej69P9JDJ1zqNIbncQ==", "signatures": [{"sig": "MEUCIQDrguZbgmtKf+yCsnduIcct4NH2tg8VuvPX4A87BNHUrwIgJYtLIm2v0M6iYdxHCD1pA308S/B7I1Bw95vc4NkSVf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjXEN2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTgw/9Ee8LZT/uUnynnXaDzPfGLH4DThXjIGUYKFNw07wQhVq/xg0M\r\nDKRrHNxGbB+mKk4TBBeAOiWZ+C29LG+/Ra/d+s2NNSGU6I98Q82DhDDvY+uD\r\nHvS7Z9iVh+tw7oeKshSOADT2Es4iEtvqvM+eHdJBgXEQUrsUCy8JMPcENc0d\r\n6iRUPfx9UMXfyezyioEOfeuaJNZf+cNNhdg2eiUHRRfcKaEgxdx3FEFRqIdm\r\nfYBuBlA3D1EywFeDcQRGACBS4/O4sfpLd3FZkRghS/huYaMKoB0v8J9SifSE\r\nBDORh2FXvjRtB8YjfPKHaFA6cjrvLlY3QO3loCoYuelIXHLnGF4Cx4eOn4SJ\r\nFHvrYS2JFgx+ymX0QAV3Y7BXGKYWuUpXe/FqirtBHllW2SoRwUc4TsS6QPAh\r\nKnoZ9PIAaNsSCgr27xR8VrnTATINROCT4ktQdrHp6VAQgXbU0WxQL4mRoz6F\r\ndAAkyGFNaBzcBKbWWahllxbU6gevypnWGod7tfdOfCamyqhjWDnMcWXvEnc4\r\nl+Gi+AFKH5U+UTkEgyfQjjEzzYf83a+WoGZICi4Orzz8tuYeNm1zScdsIlsV\r\nPk3ykOGjYNESWvR9vQGO7sr6Mt4myWI5UcUJ9UTJfZpL5yxD4SNPfX4FGCqH\r\niPPFZSdjQCE34/g/lLMZGfc4ulvedRngRII=\r\n=4Gf3\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.10": {"name": "@types/eslint", "version": "8.4.10", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "19731b9685c19ed1552da7052b6f668ed7eb64bb", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.4.10.tgz", "fileCount": 17, "integrity": "sha512-Sl/HOqN8NKPmhWo2VBEPm0nvHnu2LL3v9vKo8MEq0EtbJ4eVzGPl41VNPvn5E1i5poMk4/XD8UriLHpJvEP/Nw==", "signatures": [{"sig": "MEUCIQD8/alhZb2siksQ+vxIWiGzwsRFZ0CHJ1lU9gDsYufJiQIgVvVENR1KB2mkKj1L8ZBn6bHbga2KTfPXE4hmz3FkOZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZSmxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAdg/+OLzD7tOxV/rAqMZ0oOFTagJndHQeFuMfEGSdxCHn9oerqaFd\r\nnGBBWu/UezR9vsg+c8EuCNhDCLu+fofKMPVYnflhkhIaMZwuZ4FE3DyjQaYQ\r\nf8+tyQhp38ZDTFYud4TElV1PA6RtgeiZ/3Zqcnsa4fYwd4vn1nDnpf5RnNAn\r\nH3/qbUbhFBuUo4DOtM6zl4UfpkJki4r/stDR7bqyJ7O8sZ1xndk/+HD06AJU\r\n7FWgbm3LIHYaSK8HDT1rhWWSrinRs6Dlpe8Eg4FmLL/5dD2WLftGCFpy7hfu\r\nBtzV68qPRfaNAgPypT7lAhsOe7KGmqBAPC23RZV4H9tUmZfsgCbrqUoZCYEg\r\nAjT8b6Mrdy6J6+EnCJUX1/HuNzC9kjM06yj3Y/fR29ihYbzPs1N18wXaVSu6\r\nkfF8VHvuCE1wvTfW1Ku6TzhFghNRt7guUtxJZsToL/uFVXiD2raBXVkNFccZ\r\nWxyr/NuURXq1JXXyJighHIGF8GOISZP0sV7Iqb26lUMUm+/8tvEzOFsc7BFj\r\nysuA0Hqro/Q/EJu3kAvDb+o8I5x6DGCTevaVszenTMi7toLaRaqHMDrW23h4\r\nn5BRn0Oi0nbu3AMFYgofg7Qe1iYyoIo4lNbCy1T/G3YL2U0BX2amqrMd5hFW\r\nvENcxHJAwgu281Hj+iM6t4UPCoqVMrHy4NM=\r\n=tWq/\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.21.0": {"name": "@types/eslint", "version": "8.21.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "21724cfe12b96696feafab05829695d4d7bd7c48", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.21.0.tgz", "fileCount": 17, "integrity": "sha512-35EhHNOXgxnUgh4XCJsGhE7zdlDhYDN/aMG6UbkByCFFNgQ7b3U+uVoqBpicFydR8JEfgdjCF7SJ7MiJfzuiTA==", "signatures": [{"sig": "MEQCIQD359oxFFkem1En3MxHLVzTJNxuYqiTrE3236GQXg7IeQIfcIz/6gnf4F/eREo7q43NQbWmx4LVTfTg/q7imeW3eQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj27R0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVRw/+N9ab68yXyVaEqKz+tZIqh2ebr09R8//pQ1a29wpOqK/AjvrH\r\ny3purn7jucc21FoJLCReDALxz/Y7D7+DQAZsyimprf+oM1YvGwR2/Ez2El3o\r\ns1lhWnN4k/nT+pMAXQ3m8C2vB4z0oUsKEilYXCSCsf1eq3fvNetCnpuc/BCk\r\nz0KuALxoM1zKqO6HvqS/IJPbcSC+KtFmXvR6h8eLf0nNP3P6dVzjc/v4kWaE\r\neOMUFBP7OVBca/J2li3i1sDnngaMBTantS4eQ+qRRo9gfW7kF1wJegJCPqHn\r\nT8eDT4DstPlogJUhzCye009NBZD2nbK1lKT2eRyXJpCq+D2itkvxkzcVd88l\r\nNr7kjRhKvA9V1X0fpnIga8iBwXQHF+5OBzMb3BsYcvhjqjYYLmXdgEn3R7si\r\nNKTwnbRXYDlM/O1pn22l73UFJCRLnnTH3DMu9Ke6jCBJXqpfvFBRGWezbYMb\r\nm69pkFqw+pvrYnpKCAjHXIhhRhoic77u6NJR9IVw/fyY4L+9CngxupQK9CZH\r\noF5u9iKUZ0mkMUbkOjfM5KY0c2H1HG4dLDQjdwOkP5FQjaAiqypSCWWAVkDh\r\n0T6dBZ4SY2OCUaE2cpCOenDDG2+qZ/npG98OtvQUfkXdPVG1Uc0L2Dny9u+Z\r\ncUg/p+iF66m0ynIn+js5ITV2INT6dZTwto0=\r\n=7Bzs\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.21.1": {"name": "@types/eslint", "version": "8.21.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "110b441a210d53ab47795124dbc3e9bb993d1e7c", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.21.1.tgz", "fileCount": 17, "integrity": "sha512-rc9K8ZpVjNcLs8Fp0dkozd5Pt2Apk1glO4Vgz8ix1u6yFByxfqo5Yavpy65o+93TAe24jr7v+eSBtFLvOQtCRQ==", "signatures": [{"sig": "MEQCIEZFCdwsrlYxfatu7GeQE07AEeLJQrv1ev06rC88S839AiBJcMWSkf53HbJouDlxQbSHXw9Qcy6S+5LDOD+qTn3v5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6zkVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Gg/9Gf87chVtz4pPpP6TKNhMG2H4jbXDOnNCrtLxuNJi5IUx5aaF\r\nHVmLEhv3z2SLOQI+Sv8KBwQ7kLg3gZDnxm6tIp97DDnZ+Gv6dMoJAi+JukFw\r\n86ibqdfjloYLQOgTW8YIhw6+bUmgdIJkCIRahkfL+JdgzlRb61wZfazNgxzc\r\nTzVJh3IYt8KtbQUTvReKbrRwO/KK3+j8q3Vt6KCSTRcn3wt5a54j6NH7mJZD\r\nzn53kde6H5P4L7lAqykylYh92TlVqXPE8NSzHCBFZYmJsOT6Y3evE0whIirU\r\nWyfQsYlNjxncD1d4RW3v8qUasIeby7VZ3TFrpTvSbmEIe2AYLmPxMTtC5KKD\r\nnFzIiiNQQ9w5LO/mfCjL7P9G9AJ9rZlRZia2mnRKrWKdx6x8+RSFlnUtqmp5\r\n7mdnkplS0gu7TPDB1STloCy7e5ZfZjct9fS4FoWDPw6ETCpIthqDZN763wUM\r\nx8VnPwnBGpbRIOH7/QpOc/51LfsbBo23Diq9DVxOpu0caFsGFAcOkTRuMiwg\r\nfWOWM/pbKDBL8NTD+cZiEXsS0qJDvCMGVkB8FCsyYWCbLGXjc+Fm44kiz1im\r\n+1187x//0DXSxHHBJoi9gcD7HBVkU9D7/z9g9ro1S4Gj2D9bcg+UvIUdWxsR\r\nkEU6Q2EGqs6EO6W+Qkaq8cscwYoP3m0TLT8=\r\n=gl3w\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.21.2": {"name": "@types/eslint", "version": "8.21.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "2b61b43a8b0e66006856a2a4c8e51f6f773ead27", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.21.2.tgz", "fileCount": 17, "integrity": "sha512-EMpxUyystd3uZVByZap1DACsMXvb82ypQnGn89e1Y0a+LYu3JJscUd/gqhRsVFDkaD2MIiWo0MT8EfXr3DGRKw==", "signatures": [{"sig": "MEYCIQC+zsB75ZJb4C6cPaPWRpklhqZYZgsy/TFHa1pM2OwuZAIhAK4HrPquAPoB5WpuQsS7WJa3mZU5eUyDhefzF2ydJWf3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkD3s7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxsA//emfy15rJwdv4CWklWK4nyAwdpkxUV7erp1wqe/lUPiPcKbjb\r\ny8wJGq++JNmZ84lxlTxX7SibwC8YahTIkAxTrTByM/nVK++zwhZrMhFUDaZA\r\nVCpAgqfPs+l5WVklfoPwjLy0XD/g44X7YLl+T1D13XxhNP7qVrZItTQ+tEvm\r\nxp6sBf7OA/IgTrjc4obgzG1zD4X8bDAmwrpPQEQds9G/q9QYed1m277p0M4R\r\n/lShJcF0BRZCWm/8ZG6AI19UkNhZj7yyoHPoVXUCPDLPasPZMAxsVeFriXg7\r\neLvG3xO+IcAQUn/SK+Pad+cMAhKe0ZPuaYTYeYUNZ1acA6B3bFcag+Q0xcjX\r\nbqsSXfCJRS8pSsJpOwj/khUPpxNEqbcnFbcUpGjQy1H+Az6bHVn33p9SUhBa\r\nYpoVDC7QQzafH1YFBXFEjO9ry5OYsJCG/nbA+yelcdz1AsdgrLS2cToAxqt5\r\nRt7+bZeFyJZnx8kBhC/c9KfdyY7Qq9dXz/F3Qi4/CNR6tB7RyFiHBHNjPLT2\r\n04pfkzaxqjum4lUBR6cZVKoIyrvWNsg5n9f1brJS7xfo78oqVCHAzvcxTxUU\r\nwqv/K29+MzuspPis+a59rLw1J0IBUC6vvvRH4CapUFjVz5dW8Sop/kLUpY1A\r\nVjWIce4vHnKFkSi19B13RevDX4m4/bH2qcs=\r\n=8sdy\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.21.3": {"name": "@types/eslint", "version": "8.21.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "5794b3911f0f19e34e3a272c49cbdf48d6f543f2", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.21.3.tgz", "fileCount": 17, "integrity": "sha512-fa7GkppZVEByMWGbTtE5MbmXWJTVbrjjaS8K6uQj+XtuuUv1fsuPAxhygfqLmsb/Ufb3CV8deFCpiMfAgi00Sw==", "signatures": [{"sig": "MEUCIEiKQsJHB1Yve4Qej0rN7lNXmdVKwJ/Opsb1y+GMS0pmAiEA6DX/rhdZBFZDx+U822/yFFhsf2KqbJRkrkAU3namohc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkF2PRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMgw/+I4Chz3I9VH18Ew8p44Ulzd2q1CYbVTZSSX8n6Gepu3/fPHTa\r\nm/VWMWRXsAPZC95yC6FsPkv+84Tb3k/eJbjTdOB1X+M1MIalnY7nb+aVVZ6w\r\nY7o+VPNaW7DyHgAYV0lSokBZx94TXyEkUSGHxMin9mJMjK5YHSTMPH1wZuG2\r\nMKZuxZa+XPeXdBeWwh+D0UypuzNdDVKb2HuAxUBkcTLsbDrERMT5D2FkbqBd\r\nTAn6+sJJiFaE3iCz4TpHViv+XUEBIgS+djO6N0uN66bYAnf//pJd4NhVVPZy\r\niItuQ/Ig8GuZ20G/Kl2udSrv37IZEGcIU1gaj3AuGVylPpUyRjIQCDbzF/M8\r\n9QF9MGf+yAnfHV3qku3RZq6Ynpn/i1oyAS2WdEZgRkj+Evpz3M1b1rV6LNmT\r\nRVGIiIuWVqwdFP+ivrKtApLEJjhSICMq27m0IC/fVKhITO/hxiukIEXiZRQv\r\njIDoIFIwy4NOYgN+6tUt9/QqGvUQFyO3KPXD7seE5xJhW16kIofCKC8y3Luo\r\nadrqGrTWCK0nSag1pY5Yags4wn2fFHVPKRETws7VQqVKYE6XXNrzFeAW4aRj\r\n0WC3INESOa+qCffQCdilo/9ghn2SfCmvAcJdl5ye7EcYwQF/nM5b6/jZt16P\r\nG2m9N2HgPsAVqCyvP6QipnMsGK/yoNcPsIs=\r\n=DZeb\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.37.0": {"name": "@types/eslint", "version": "8.37.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "29cebc6c2a3ac7fea7113207bf5a828fdf4d7ef1", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.37.0.tgz", "fileCount": 17, "integrity": "sha512-Piet7dG2JBuDIfohBngQ3rCt7MgO9xCO4xIMKxBThCq5PNRB91IjlJ10eJVwfoNtvTErmxLzwBZ7rHZtbOMmFQ==", "signatures": [{"sig": "MEQCIGo+IXzTPcZyIX9cqjT54GhCaOtdqgXKMCL8LZ86+UyzAiBpa5LAyz6XuWNx18t6vwWNOgRRod4R9e62RBusb9lynw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJKeNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogHw//U/Rx+o00GTxQE30TNpVL7d0/V8BcQK86xFUi9KoiYTMmchY8\r\nEZNcMgTbJANitpZvu35/wTq1S3fcd6UOh4zSIvJg0jQvtMc8kdrWsO9drAIj\r\nGISJZmRx6zy8esfdniPTlGEoj24OlYzk7MXV77hSOfIjOFdxlSVQGi/lknDn\r\n18xuJC2ilkzx7tvpvKMWHrVyzilyp8Lga3udwYMuPqgotsjpfyLM06fp2Giu\r\nRcz0z2bORhIlfkr7R4HqPfBOGSao0i3fQRlEV5E4aOX+oqkynBxci54g3SQD\r\nhA0hele3AD/n2eqsaBuKx+utjUq6DMubYU37Vp3c0yDX0WAwndWbHF8hzI72\r\n6vsY+0/U2ykBQb+/Iiowvx8qXx3vkL0zzOUsN0BA/4GQ0esFoE9YUJqeSf7O\r\neiUB+BGeVrfdbcMi9LPGIUgxwhg7woDSWp4YMK2GYMnd1V+rJ3D2K/bV73Qz\r\n+vu+JWw4NAqvWA559dLgRFLjXQMM6jpYx+Raen3QS3HL9ZSxqdRyNB0/2lc6\r\no5ANX854Ltm4ywKImVno+Iyqp3oXQK4yQ0Itwnf0TjoOEUiBFrfv6Btjvk7L\r\n0DK+bVXOAk8Y84W2Mky8mTxWjSMKtEHyWoveClHtF04/gwH3VZIKXzj8prRT\r\nCNuwDsRxZ/0cr2uv9DCbqkl71Z7ZYtkmfrM=\r\n=YKLh\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.40.0": {"name": "@types/eslint", "version": "8.40.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "ae73dc9ec5237f2794c4f79efd6a4c73b13daf23", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.40.0.tgz", "fileCount": 17, "integrity": "sha512-nbq2mvc/tBrK9zQQuItvjJl++GTN5j06DaPtp3hZCpngmG6Q3xoyEmd0TwZI0gAy/G1X0zhGBbr2imsGFdFV0g==", "signatures": [{"sig": "MEUCIEhfpYGp/K9vEUbXKcDWlEIbqErtgUDYliCLID7RC3wBAiEApA3uGT7VU+d/wyphx88FbHLo5nWbva2JwMgdPVAtVus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176965}}, "8.40.1": {"name": "@types/eslint", "version": "8.40.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "92edc592c3575b52a8e790cd5ec04efe28f3d24c", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.40.1.tgz", "fileCount": 17, "integrity": "sha512-vRb792M4mF1FBT+eoLecmkpLXwxsBHvWWRGJjzbYANBM6DtiJc6yETyv4rqDA6QNjF1pkj1U7LMA6dGb3VYlHw==", "signatures": [{"sig": "MEUCIDaomGbdU0i/TnjEPvQqFdytzhImO/ALZ2qvqp6Dm2ZCAiEAkE8Hb3DDFShktp6/6nDgIa6UjViPMfmto6zNkPx9MDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177660}}, "8.40.2": {"name": "@types/eslint", "version": "8.40.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "2833bc112d809677864a4b0e7d1de4f04d7dac2d", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.40.2.tgz", "fileCount": 17, "integrity": "sha512-PRVjQ4Eh9z9pmmtaq8nTjZjQwKFk7YIHIud3lRoKRBgUQjgjRmoGxxGEPXQkF+lH7QkHJRNr5F4aBgYCW0lqpQ==", "signatures": [{"sig": "MEUCIQCqipwkF15RFS3mvGw94czh1GQkXDpvzGmsMQyySs2/xQIgROPu+vBgbfnS1VtP0gjOT3gNLM3AGeM+VPQKvPQiRr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177702}}, "8.44.0": {"name": "@types/eslint", "version": "8.44.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "55818eabb376e2272f77fbf5c96c43137c3c1e53", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.0.tgz", "fileCount": 17, "integrity": "sha512-gsF+c/0XOguWgaOgvFs+xnnRqt9GwgTvIks36WpE6ueeI4KCEHHd8K/CKHqhOqrJKsYH8m27kRzQEvWXAwXUTw==", "signatures": [{"sig": "MEUCIQC6GdgQ2sYsIxX50eWXkc2WWAPutZVDb/UupdD1//Z5PQIgFPXVPMgc6hF2uX3G4Kgxh+3uPcetEO1b73gQaDwm4Nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177731}}, "8.44.1": {"name": "@types/eslint", "version": "8.44.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "d1811559bb6bcd1a76009e3f7883034b78a0415e", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.1.tgz", "fileCount": 17, "integrity": "sha512-XpNDc4Z5Tb4x+SW1MriMVeIsMoONHCkWFMkR/aPJbzEsxqHy+4Glu/BqTdPrApfDeMaXbtNh6bseNgl5KaWrSg==", "signatures": [{"sig": "MEUCIQDvB/El1BsNsPqwrke54nSEmvZC7bXdpP9x+ph12QkfAAIgNMWDppPMJomozByzlWycvJxRrly1iAwzcNqV6ks2JH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178032}}, "8.44.2": {"name": "@types/eslint", "version": "8.44.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "0d21c505f98a89b8dd4d37fa162b09da6089199a", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.2.tgz", "fileCount": 17, "integrity": "sha512-sdPRb9K6iL5XZOmBubg8yiFp5yS/JdUDQsq5e6h95km91MCYMuvp7mh1fjPEYUhvHepKpZOjnEaMBR4PxjWDzg==", "signatures": [{"sig": "MEYCIQCX1tOGMoMx7PNoxTqf30IdDFyPSoWjeJ9Vqt3FjE7pUAIhAKedOafhlcyUigOjoK6eDlhaXgmCaGeHkXIEaSluZX3a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178426}}, "8.44.3": {"name": "@types/eslint", "version": "8.44.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "96614fae4875ea6328f56de38666f582d911d962", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.3.tgz", "fileCount": 17, "integrity": "sha512-iM/WfkwAhwmPff3wZuPLYiHX18HI24jU8k1ZSH7P8FHwxTjZ2P6CoX2wnF43oprR+YXJM6UUxATkNvyv/JHd+g==", "signatures": [{"sig": "MEUCIQCPnCs9Z5C3YaL1QCiGUP9/8fnY+89sZxa2kcpobMufggIgBWkjqTfvFvAvTCme7mFAPN5KBpN/kpL3yJCvZZbmiUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179272}}, "8.44.4": {"name": "@types/eslint", "version": "8.44.4", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "28eaff82e1ca0a96554ec5bb0188f10ae1a74c2f", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.4.tgz", "fileCount": 17, "integrity": "sha512-lOzjyfY/D9QR4hY9oblZ76B90MYTB3RrQ4z2vBIJKj9ROCRqdkYl2gSUx1x1a4IWPjKJZLL4Aw1Zfay7eMnmnA==", "signatures": [{"sig": "MEUCIQC4LyQdZ468Up/9wyN+yEeej134DDJcWB0jjjKdP+25owIgAKrb11GISCAbpSDWtauqWWzRr1Qe4bzaS82THECmegc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179272}}, "8.44.5": {"name": "@types/eslint", "version": "8.44.5", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "24d7f3b07aff47a13b570efd5c52d96f38cd352e", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.5.tgz", "fileCount": 7, "integrity": "sha512-Ol2eio8LtD/tGM4Ga7Jb83NuFwEv3NqvssSlifXL9xuFpSyQZw0ecmm2Kux6iU0KxQmp95hlPmGCzGJ0TCFeRA==", "signatures": [{"sig": "MEUCIFkSCCe+8B5c2SwfVRgefUHRFDL1F+ZeukMnAuoYZaKjAiEAr8g7wVq5iAlcFNtASJ6IimTdG/1uXqBmGCHvktTzN1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58717}}, "8.44.6": {"name": "@types/eslint", "version": "8.44.6", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "60e564551966dd255f4c01c459f0b4fb87068603", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.6.tgz", "fileCount": 17, "integrity": "sha512-P6bY56TVmX8y9J87jHNgQh43h6VVU+6H7oN7hgvivV81K2XY8qJZ5vqPy/HdUoVIelii2kChYVzQanlswPWVFw==", "signatures": [{"sig": "MEQCIBs2ut19y+05i46YBLPeAanGeEyXcSUfjXWemaxDr1qcAiBB6LVGR23zPzQ/A9OBSPLpVgibvc4kZygzr1ff8wVJAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178755}}, "8.44.7": {"name": "@types/eslint", "version": "8.44.7", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "430b3cc96db70c81f405e6a08aebdb13869198f5", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.7.tgz", "fileCount": 17, "integrity": "sha512-f5ORu2hcBbKei97U73mf+l9t4zTGl74IqZ0GQk4oVea/VS8tQZYkUveSYojk+frraAVYId0V2WC9O4PTNru2FQ==", "signatures": [{"sig": "MEYCIQDWbKXX7l1n3Kn/qjiU+IhXHvrvOfWCfC6aJ5aISAkXcAIhAJ1b+gXNKvVCfkM8CCE1B9EWKI0K6mDrV9OS8BGs9hrj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178755}}, "8.44.8": {"name": "@types/eslint", "version": "8.44.8", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "f4fe1dab9b3d3dd98082d4b9f80e59ab40f1261c", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.8.tgz", "fileCount": 17, "integrity": "sha512-4K8GavROwhrYl2QXDXm0Rv9epkA8GBFu0EI+XrrnnuCl7u8CWBRusX7fXJfanhZTDWSAL24gDI/UqXyUM0Injw==", "signatures": [{"sig": "MEUCIQC2Y7+54T7gcII+sWZ6jc27pW19WMdkn1E4PIai7Yhl1AIgUDhRjyNPk0EX/5XZ6EsZxE2jQKZb4DhPiHUWFWKn+7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185006}}, "8.44.9": {"name": "@types/eslint", "version": "8.44.9", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "5799663009645637bd1c45b2e1a7c8f4caf89534", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.9.tgz", "fileCount": 17, "integrity": "sha512-6yBxcvwnnYoYT1Uk2d+jvIfsuP4mb2EdIxFnrPABj5a/838qe5bGkNLFOiipX4ULQ7XVQvTxOh7jO+BTAiqsEw==", "signatures": [{"sig": "MEYCIQCkjZrtMrOGXdPxHb5/0ky03/ERTVctt7F+UAiQdsWL6AIhAJVgrk0p6SE4F0tqBXDk6lTJMrhH4zJLlr6rJKsMeSua", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185084}}, "8.56.0": {"name": "@types/eslint", "version": "8.56.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e28d045b8e530a33c9cbcfbf02332df0d1380a2c", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.0.tgz", "fileCount": 17, "integrity": "sha512-FlsN0p4FhuYRjIxpbdXovvHQhtlG05O1GG/RNWvdAxTboR438IOTwmrY/vLA+Xfgg06BTkP045M3vpFwTMv1dg==", "signatures": [{"sig": "MEQCIFQlEUO9fux10xwr6dD+VNBbuKxC9mJVXXtnDD31b1sQAiAuABzbKUoBx+mi2UV7lGscLr1ymLZlPsFsYdOxe/Cg6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185121}}, "8.56.1": {"name": "@types/eslint", "version": "8.56.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "988cabb39c973e9200f35fdbb29d17992965bb08", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.1.tgz", "fileCount": 17, "integrity": "sha512-18PLWRzhy9glDQp3+wOgfLYRWlhgX0azxgJ63rdpoUHyrC9z0f5CkFburjQx4uD7ZCruw85ZtMt6K+L+R8fLJQ==", "signatures": [{"sig": "MEUCIEmWMa2ifHsO/k3T//m32OsrX/HjxPHKjhf1fmUBDBllAiEAyRPQuhx8Ua+q8Yw+Df/mYgbbOWWNP53qq+C4waTVHoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185485}}, "8.56.2": {"name": "@types/eslint", "version": "8.56.2", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "1c72a9b794aa26a8b94ad26d5b9aa51c8a6384bb", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.2.tgz", "fileCount": 17, "integrity": "sha512-uQDwm1wFHmbBbCZCqAlq6Do9LYwByNZHWzXppSnay9SuwJ+VRbjkbLABer54kcPnMSlG6Fdiy2yaFXm/z9Z5gw==", "signatures": [{"sig": "MEUCIQCzdKOe8tiBuF7S7nFS69fDFA7jKvKQGaxfbMI9t+K/iQIgW0t/YNCK2ANTnkgmhzDMque1Huj8OMt+BsCjjsCQSns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185560}}, "8.56.3": {"name": "@types/eslint", "version": "8.56.3", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "d1f6b2303ac5ed53cb2cf59e0ab680cde1698f5f", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.3.tgz", "fileCount": 17, "integrity": "sha512-PvSf1wfv2wJpVIFUMSb+i4PvqNYkB9Rkp9ZDO3oaWzq4SKhsQk4mrMBr3ZH06I0hKrVGLBacmgl8JM4WVjb9dg==", "signatures": [{"sig": "MEUCIQDOz5CA1vjlRMxPvMPJJjiQ8ZlxLW3wAbXvvEUvvsxDvAIgLLbFAPYeJk/R5lKkE1uG1VdcodxxNRDrgbHTG5cAfnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185738}}, "8.56.4": {"name": "@types/eslint", "version": "8.56.4", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "1ce772b385cf23982d048c3ddadba6ff5787c761", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.4.tgz", "fileCount": 17, "integrity": "sha512-lG1GLUnL5vuRBGb3MgWUWLdGMH2Hps+pERuyQXCfWozuGKdnhf9Pbg4pkcrVUHjKrU7Rl+GCZ/299ObBXZFAxg==", "signatures": [{"sig": "MEUCICoU7Lg8xYq2/LgwvSzMcbqft6JaqVC9GcFljYbdrRzLAiEAgLb53p7UxcpvP8YahOqMX1PnHEjxy+lgWPqrmbh9ucs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186191}}, "8.56.5": {"name": "@types/eslint", "version": "8.56.5", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "94b88cab77588fcecdd0771a6d576fa1c0af9d02", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.5.tgz", "fileCount": 17, "integrity": "sha512-u5/YPJHo1tvkSF2CE0USEkxon82Z5DBy2xR+qfyYNszpX9qcs4sT6uq2kBbj4BXY1+DBGDPnrhMZV3pKWGNukw==", "signatures": [{"sig": "MEYCIQCIGItN0lqZjRtf2K9f25HORAQwwlG8Ibkh6PPZNcNLIwIhAJh+Ynt3Ku5zcmbDLOhErRCD4Nw99SqE8AmV3iw20bEu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187465}}, "8.56.6": {"name": "@types/eslint", "version": "8.56.6", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "d5dc16cac025d313ee101108ba5714ea10eb3ed0", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.6.tgz", "fileCount": 17, "integrity": "sha512-ymwc+qb1XkjT/gfoQwxIeHZ6ixH23A+tCT2ADSA/DPVKzAjwYkTXBMCQ/f6fe4wEa85Lhp26VPeUxI7wMhAi7A==", "signatures": [{"sig": "MEYCIQDHoAYA2YLCBpQvcuY7by7aWsJYvU5/dNjonofIt34lwQIhAJOYxB/90FsNi+w4bpbr5/ZOrhatGDdCMDIOTDl+3jSz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192337}}, "8.56.7": {"name": "@types/eslint", "version": "8.56.7", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "c33b5b5a9cfb66881beb7b5be6c34aa3e81d3366", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.7.tgz", "fileCount": 17, "integrity": "sha512-SjDvI/x3zsZnOkYZ3lCt9lOZWZLB2jIlNKz+LBgCtDurK0JZcwucxYHn1w2BJkD34dgX9Tjnak0txtq4WTggEA==", "signatures": [{"sig": "MEUCIQDQL54GTykKJJQqdQYQ7ksxcKaK4b8wOROe140C/uDcVgIgWocypEFVRmP0ioIx0tWO2BW9w8oCgvdcybs1btc8AfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192500}}, "8.56.8": {"name": "@types/eslint", "version": "8.56.8", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e927fdc742a98fc3195a9d047631e6ab95029b50", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.8.tgz", "fileCount": 17, "integrity": "sha512-LdDdQVDzDXf3ijhhMnE27C5vc0QEknD8GiMR/Hi+fVbdZNfAfCy2j69m0LjUd2MAy0+kIgnOtd5ndTmDk/VWCA==", "signatures": [{"sig": "MEYCIQCyPAfoBCiPWX/c7+WEofpVAXbUaWi8dhYkppUP15mpKgIhAM9tcI5HFf4BMaNxjIZdM3bUbryNP4DBsWUK5ibCqu8m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192544}}, "8.56.9": {"name": "@types/eslint", "version": "8.56.9", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "403e9ced04a34e63f1c383c5b8ee1a94442c8cc4", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.9.tgz", "fileCount": 17, "integrity": "sha512-W4W3KcqzjJ0sHg2vAq9vfml6OhsJ53TcUjUqfzzZf/EChUtwspszj/S0pzMxnfRcO55/iGq47dscXw71Fxc4Zg==", "signatures": [{"sig": "MEQCIBzT38zHU0UkiomD1EIA+JIrtCDsyJEad42COcprDDmNAiAQ2KPLA0sn672PJP8Tyz1oBmFzQpXfS6gFjX7nQqe72Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192435}}, "8.56.10": {"name": "@types/eslint", "version": "8.56.10", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "eb2370a73bf04a901eeba8f22595c7ee0f7eb58d", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.10.tgz", "fileCount": 17, "integrity": "sha512-Shavhk87gCtY2fhXDctcfS3e6FdxWkCx1iUZ9eEUbh7rTqlZT0/IzOkCOVt0fCjcFuZ9FPYfuezTBImfHCDBGQ==", "signatures": [{"sig": "MEQCIE4WgFNDe9vHmOJ4jSOBmOyi1O4XRnQP6hYBoe8CPBOWAiB0iNf0XMn+mO0t32Vo2B+h8xWVAX6WUU9EGjG0Bx8r1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192449}}, "9.6.0": {"name": "@types/eslint", "version": "9.6.0", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "51d4fe4d0316da9e9f2c80884f2c20ed5fb022ff", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.0.tgz", "fileCount": 16, "integrity": "sha512-gi6WQJ7cHRgZxtkQEoyHMppPjq9Kxo5Tjn2prSKDSmZrCz8TZ3jSRCeTJm+WoM+oB0WG37bRqLzaaU3q7JypGg==", "signatures": [{"sig": "MEUCIGB9Crw9eaGqxRE0eS3fA1VHvBMGGCnc1H5nY8ioLQkQAiEAvJVt9hLxbW9Krs6ad2/21UU7GeYGudzMzRjKknaBV3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195449}}, "8.56.11": {"name": "@types/eslint", "version": "8.56.11", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "e2ff61510a3b9454b3329fe7731e3b4c6f780041", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.11.tgz", "fileCount": 17, "integrity": "sha512-sVBpJMf7UPo/wGecYOpk2aQya2VUGeHhe38WG7/mN5FufNSubf5VT9Uh9Uyp8/eLJpu1/tuhJ/qTo4mhSB4V4Q==", "signatures": [{"sig": "MEQCIEFHDAiy6YQ0/a2BcnnpojOmVvKP7e031vGtDJl7u7tyAiA4kR2sBxLi1YiXCVbmP5AX0BFuuZJcksfB8HbTQk0KJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192575}}, "9.6.1": {"name": "@types/eslint", "version": "9.6.1", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "d5795ad732ce81715f27f75da913004a56751584", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz", "fileCount": 16, "integrity": "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==", "signatures": [{"sig": "MEYCIQD3xsjGD7kPv9RbaC4bETopskOcH8d74df7pkn9K4GfzwIhAM57Ka1fWFmsIQ3Dez9JrE4H7JR5Huy7KzFKrcER/Wg4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195627}}, "8.56.12": {"name": "@types/eslint", "version": "8.56.12", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}, "dist": {"shasum": "1657c814ffeba4d2f84c0d4ba0f44ca7ea1ca53a", "tarball": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.12.tgz", "fileCount": 17, "integrity": "sha512-03<PERSON>ubjWyOHlmljCVoxSuNDdmfZDzsrrz0P2LeJsOXr+ZwFQ+0yQIwNCwt/GYhV7Z31fgtXJTAEs+FYlEL851g==", "signatures": [{"sig": "MEQCIF2XtG89EaMHa78lBcWZKQbKpFenUu+RFlaqGTua6ggUAiAWFTYgfIaGKy0zy72yG6rY27daPdbm4B0SxIln34q/LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192753}}}, "modified": "2024-08-26T07:08:21.721Z"}