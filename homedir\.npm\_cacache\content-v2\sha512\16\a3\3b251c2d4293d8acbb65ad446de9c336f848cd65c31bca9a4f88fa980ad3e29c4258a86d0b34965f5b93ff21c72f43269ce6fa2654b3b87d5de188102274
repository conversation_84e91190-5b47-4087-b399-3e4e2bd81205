{"_id": "boolbase", "_rev": "4-1326bf7f9caccc67f78738ed0a9edb8c", "name": "boolbase", "description": "two functions: One that returns true, one that returns false", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "boolbase", "version": "1.0.0", "description": "two functions: One that returns true, one that returns false", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/fb55/boolbase"}, "keywords": ["boolean", "function"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/fb55/boolbase/issues"}, "homepage": "https://github.com/fb55/boolbase", "_id": "boolbase@1.0.0", "dist": {"shasum": "68dff5fbe60c51eb37725ea9e3ed310dcc1e776e", "tarball": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDnj9Fb6MV0ijpKOGLBTlGLuLLGf93sOI1Tp6YqrMI/cAiEAiem+6dYcR3Z21eyzZuPqOR5ddxPqF+qzMrXW1LWDtEw="}]}, "_from": ".", "_npmVersion": "1.4.2", "_npmUser": {"name": "feedic", "email": "<EMAIL>"}, "maintainers": [{"name": "feedic", "email": "<EMAIL>"}]}}, "readme": "#boolbase\nThis very simple module provides two basic functions, one that always returns true (`trueFunc`) and one that always returns false (`falseFunc`).\n\n###WTF?\n\nBy having only a single instance of these functions around, it's possible to do some nice optimizations. Eg. [`CSSselect`](https://github.com/fb55/CSSselect) uses these functions to determine whether a selector won't match any elements. If that's the case, the DOM doesn't even have to be touched.\n\n###And why is this a separate module?\n\nI'm trying to modularize `CSSselect` and most modules depend on these functions. IMHO, having a separate module is the easiest solution to this problem.", "maintainers": [{"name": "feedic", "email": "<EMAIL>"}], "time": {"modified": "2022-06-13T04:55:59.169Z", "created": "2014-02-15T14:44:50.619Z", "1.0.0": "2014-02-15T14:44:50.620Z"}, "readmeFilename": "README.md"}