
32ab3a178928afaa96435ecbed1a8380be806bc0	{"key":"pacote:range-manifest:https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz:sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==","integrity":"sha512-C2EkHXwXvLsbrucJTRS3xFHv7Mf/y9klmKDxPTE8yevCoH5h8Ae69Y+/lP+ahpW91crnzgO78elOk2E6APJfIQ==","time":1750369199557,"size":1,"metadata":{"id":"boolbase@1.0.0","manifest":{"name":"boolbase","version":"1.0.0","dependencies":{},"optionalDependencies":{},"peerDependenciesMeta":{},"devDependencies":{},"bundleDependencies":false,"peerDependencies":{},"deprecated":false,"_resolved":"https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz","_integrity":"sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==","_shasum":"68dff5fbe60c51eb37725ea9e3ed310dcc1e776e","_shrinkwrap":null,"_id":"boolbase@1.0.0"},"type":"finalized-manifest"}}